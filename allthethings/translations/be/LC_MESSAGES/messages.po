#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Няправільны запыт. Наведайце %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Бібліятэка Internet Archive"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " і "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "і больш"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Мы люструем %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Мы збіраем і адкрываем зыходны код %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Увесь наш код і дадзеныя цалкам адкрытыя."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Найбуйнейшая і па-сапраўднаму адкрытая бібліятэка ў гісторыі чалавецтва."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;кніг, %(paper_count)s&nbsp;дакументаў захавана назаўжды."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Найбуйнейшая ў свеце бібліятэка адкрытых дадзеных з адкрытым кодам. ⭐️&nbsp;адлюстроўвае Sci-Hub, бібліятэку Genesis, z-Library і многае іншае. 📈&nbsp;%(book_any)s кніг, %(journal_article)s артыкулаў, %(book_comic)s коміксаў, %(magazine)s часопісаў — захоўваюцца назаўжды.."

msgid "layout.index.header.tagline_short"
msgstr "📚 Найбуйнейшая ў свеце бібліятэка адкрытых дадзеных з адкрытым зыходным кодам.<br>⭐️ адлюстроўвае Scihub, Libgen, Zlib і многае іншае."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Няправільна метаданыя (Metadata) (напрыклад, назва, апісанне, выява вокладкі)"

msgid "common.md5_report_type_mapping.download"
msgstr "Праблемы з загрузкай (напрыклад, не ўдаецца далучыцца, паведамленне пра памылку, вельмі павольна)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Файл не можа быць адкрыты (напрыклад, пашкоджаны файл, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Нізкая якасць (напрыклад, праблемы з фарматаваннем, дрэнная якасць сканавання, адсутнічаюць старонкі)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Спам / файл павінен быць выдалены (напрыклад, рэклама, абразлівы змест)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Прэтэнзія на аўтарскія правы"

msgid "common.md5_report_type_mapping.other"
msgstr "Іншае"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Бонусныя загрузкі"

msgid "common.membership.tier_name.2"
msgstr "Цудоўны Кнігалюб"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Шчаслівы Бібліятэкар"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Захапляльны Збіральнік Дадзеных"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Дзіўны Архівіст"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s усяго"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) усяго"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s бонус)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "неаплочана"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "аплочана"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "адменена"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "скончыўся"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "чакае пацверджання ад Анны"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "няправільны"

msgid "page.donate.title"
msgstr "Падтрымаць праект"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "У вас ёсць <a %(a_donation)s>існуючае ахвяраванне</a> у працэсе. Калі ласка, завяршыце або адмяніце гэтае ахвяраванне, перш чым рабіць новае."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Прагледзець усе мае ахвяраванні</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Anna’s Archive — гэта некамерцыйны, з адкрытым зыходным кодам і адкрытымі дадзенымі праект. Дзякуючы ахвяраванням і ўдзелу ў членстве, вы падтрымліваеце нашу дзейнасць і развіццё. Усім нашым членам: дзякуй, што падтрымліваеце нас! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Для атрымання дадатковай інфармацыі азнаёмцеся з <a %(a_donate)s>Частымі пытаннямі аб ахвяраваннях</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Каб атрымаць яшчэ больш загрузак, <a %(a_refer)s>запрасіце сваіх сяброў</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Вы атрымліваеце %(percentage)s%% бонусных хуткіх загрузак, таму што вас накіраваў карыстальнік %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Гэта распаўсюджваецца на ўвесь перыяд членства."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s хуткіх спамповак у дзень"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "калі вы ахвяруеце ў гэтым месяцы!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / месяц"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Далучыцца"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Выбрана"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "зніжкі да %(percentage)s%%"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Артыкулы SciDB <strong>неабмежавана</strong> без верыфікацыі"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Доступ да JSON API</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Атрымайце <strong>%(percentage)s%% бонусных загрузак</strong>, <a %(a_refer)s>запрасіўшы сяброў</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Ваш лагін або ананімная згадка ў крэдытах"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Папярэднія перавагі, плюс:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Ранні доступ да новых функцый"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Эксклюзіўны Telegram з абнаўленнямі за кулісамі"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "«Адапртаваць торэнт»: ваша імя карыстальніка або паведамленне ў назве торэнт-файла <div %(div_months)s>раз на 12 месяцаў сяброўства</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Легендарны статус у захаванні ведаў і культуры чалавецтва"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Экспертны доступ"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "звяжыцеся з намі"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Мы невялікая каманда валанцёраў. Адказ можа заняць 1-2 тыдні."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Неабмежаваны</strong> высокахуткасны доступ"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Прамыя <strong>SFTP</strong> серверы"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Ахвяраванне на ўзроўні прадпрыемства або абмен на новыя калекцыі (напрыклад, новыя сканы, OCR’аваныя datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Мы вітаем буйныя ахвяраванні ад заможных асоб або ўстаноў. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Для ахвяраванняў звыш $5000, калі ласка, звяжыцеся з намі непасрэдна па %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Звярніце ўвагу, што хоць сяброўства на гэтай старонцы пазначана як «штомесячнае», гэта аднаразовыя ахвяраванні (не паўтараюцца). Глядзіце <a %(faq)s>Частыя пытанні пра ахвяраванні</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Калі вы хочаце зрабіць ахвяраванне (любы памер) без сяброўства, не саромейцеся выкарыстоўваць гэты адрас Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Калі ласка, выберыце спосаб аплаты."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(часова недаступна)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s падарункавая карта"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Банкаўская карта (з выкарыстаннем прыкладання)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Крыпта %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Крэдытная/дэбетная карта"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (ЗША) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (звычайны)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Карта / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Крэдытная/дэбетавая/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Банкаўская карта"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Крэдытная/дэбетная карта (рэзервовая)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Крэдытная/дэбетавая карта 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Бінанс"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "З крыпта вы можаце ахвяраваць з дапамогай BTC, ETH, XMR і SOL. Выкарыстоўвайце гэты варыянт, калі вы ўжо знаёмыя з крыптавалютай."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "З дапамогай крыпта вы можаце ахвяраваць, выкарыстоўваючы BTC, ETH, XMR і іншыя."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Калі вы ўпершыню карыстаецеся криптавалютай, мы раім выкарыстоўваць %(options)s для пакупкі і ахвяравання Bitcoin (першай і найбольш выкарыстоўванай криптавалюты)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Бінанс"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Для ахвяравання з дапамогай PayPal US мы будзем выкарыстоўваць PayPal Crypto, што дазваляе нам заставацца ананімнымі. Мы цэнім ваш час, выдаткаваны на вывучэнне гэтага метаду ахвяравання, бо гэта вельмі дапамагае нам."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Ахвяраваць з дапамогай PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Ахвяруйце з дапамогай Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Калі ў вас ёсць Cash App, гэта самы просты спосаб ахвяраваць!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Звярніце ўвагу, што за транзакцыі менш за %(amount)s, Cash App можа спаганяць плату ў памеры %(fee)s. За %(amount)s і больш — бясплатна!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Ахвяраваць з дапамогай Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Калі ў вас ёсць Revolut, гэта самы просты спосаб ахвяраваць!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Ахвяраваць з дапамогай крэдытнай або дэбетавай карты."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay і Apple Pay таксама могуць працаваць."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Звярніце ўвагу, што для невялікіх ахвяраванняў камісія за крэдытную карту можа ліквідаваць нашу %(discount)s%% зніжку, таму мы рэкамендуем больш працяглыя падпіскі."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Звярніце ўвагу, што для невялікіх ахвяраванняў камісія высокая, таму мы рэкамендуем больш працяглыя падпіскі."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "З Binance вы купляеце Bitcoin з дапамогай крэдытнай/дэбетавай карты або банкаўскага рахунку, а затым ахвяруеце гэты Bitcoin нам. Такім чынам, мы можам заставацца бяспечнымі і ананімнымі пры прыняцці вашага ахвяравання."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance даступны амаль у кожнай краіне і падтрымлівае большасць банкаў і крэдытных/дэбетавых карт. Гэта наша асноўная рэкамендацыя на дадзены момант. Мы ўдзячныя вам за тое, што вы знайшлі час, каб даведацца, як ахвяраваць з дапамогай гэтага метаду, бо гэта вельмі дапамагае нам."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Ахвяраваць з дапамогай вашага звычайнага акаўнта PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Ахвяруйце з дапамогай крэдытнай/дэбетавай карты, PayPal або Venmo. Вы можаце выбраць паміж імі на наступнай старонцы."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Ахвяруйце з дапамогай падарункавай карты Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Звярніце ўвагу, што нам трэба акругліць да сум, якія прымаюцца нашымі рэсэлерамі (мінімум %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>ВАЖНА:</strong> Мы падтрымліваем толькі Amazon.com, іншыя сайты Amazon, напрыклад, .de, .co.uk, .ca, НЕ падтрымліваюцца."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>ВАЖНА:</strong> Гэты варыянт для %(amazon)s. Калі вы хочаце выкарыстоўваць іншы сайт Amazon, выберыце яго вышэй."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Гэты метад выкарыстоўвае пастаўшчыка криптовалюты ў якасці прамежкавага канвертара. Гэта можа быць крыху заблытана, таму выкарыстоўвайце гэты метад толькі калі іншыя спосабы аплаты не працуюць. Таксама ён не працуе ва ўсіх краінах."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Ахвяруйце з дапамогай крэдытнай/дэбетавай карты праз прыкладанне Alipay (вельмі лёгка наладзіць)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Усталюйце прыкладанне Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Усталюйце прыкладанне Alipay з <a %(a_app_store)s>Apple App Store</a> або <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Зарэгіструйцеся, выкарыстоўваючы свой нумар тэлефона."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Дадатковыя асабістыя дадзеныя не патрабуюцца."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Дадайце банкаўскую карту"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Падтрымліваюцца: Visa, MasterCard, JCB, Diners Club і Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Глядзіце <a %(a_alipay)s>гэты гід</a> для больш падрабязнай інфармацыі."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Мы не можам падтрымліваць крэдытныя/дэбетавыя карты наўпрост, таму што банкі не хочуць з намі працаваць. ☹ Аднак ёсць некалькі спосабаў выкарыстання крэдытных/дэбетавых карт праз іншыя метады аплаты:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Падарункавая карта Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Дасылайце нам падарункавыя карты Amazon.com з дапамогай вашай крэдытнай/дэбетавай карты."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay падтрымлівае міжнародныя крэдытныя/дэбетавыя карты. Глядзіце <a %(a_alipay)s>гэты гід</a> для атрымання дадатковай інфармацыі."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) падтрымлівае міжнародныя крэдытныя/дэбетныя карты. У дадатку WeChat перайдзіце ў “Me => Services => Wallet => Add a Card”. Калі вы гэтага не бачыце, уключыце гэта праз “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Вы можаце купіць крыптавалюту з дапамогай крэдытных/дэбетавых карт."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Крыпта экспрэс паслугі"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Экспрэс паслугі зручныя, але спаганяюць большыя зборы."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Вы можаце выкарыстоўваць гэта замест крыпта біржы, калі хочаце хутка зрабіць больш буйное ахвяраванне і не супраць платы ў памеры $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Абавязкова адпраўце дакладную суму крыпта, паказаную на старонцы ахвяраванняў, а не суму ў $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Інакш плата будзе вылічана, і мы не зможам аўтаматычна апрацаваць ваша сяброўства."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(мінімум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(мінімум: %(minimum)s у залежнасці ад краіны, без праверкі для першай транзакцыі)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(мінімум: %(minimum)s, без праверкі для першай транзакцыі)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(мінімум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(мінімум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(мінімум: %(minimum)s, без праверкі для першай транзакцыі)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Калі якая-небудзь з гэтай інфармацыі састарэла, калі ласка, напішыце нам па электроннай пошце, каб паведаміць нам."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Для крэдытных карт, дэбетавых карт, Apple Pay і Google Pay мы выкарыстоўваем “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). У іх сістэме адна “кава” роўная $5, таму ваша ахвяраванне будзе акруглена да бліжэйшага множніка 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Выберыце, на які тэрмін вы хочаце падпісацца."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 месяц"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 месяцы"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 месяцаў"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 месяцаў"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 месяцы"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 месяцаў"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 месяцаў"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>пасля <span %(span_discount)s></span> зніжак</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Гэты спосаб аплаты патрабуе мінімум %(amount)s. Калі ласка, выберыце іншую працягласць або спосаб аплаты."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Ахвяраваць"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Гэты спосаб аплаты дазваляе максімум %(amount)s. Калі ласка, выберыце іншую працягласць або спосаб аплаты."

#, fuzzy
msgid "page.donate.login2"
msgstr "Каб стаць удзельнікам, калі ласка, <a %(a_login)s>Увайдзіце або Зарэгіструйцеся</a>. Дзякуй за вашу падтрымку!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Выберыце сваю любімую крыптаманету:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(мінімальная сума)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(выкарыстоўвайце пры адпраўцы Ethereum з Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(папярэджанне: высокая мінімальная сума)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Націсніце кнопку ахвяравання, каб пацвердзіць гэтае ахвяраванне."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Ахвяраваць <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Вы ўсё яшчэ можаце адмяніць ахвяраванне падчас афармлення."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Перанакіраванне на старонку ахвяраванняў…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / месяц"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "на 1 месяц"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "на 3 месяцы"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "на працягу 6 месяцаў"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "на 12 месяцаў"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "на працягу 24 месяцаў"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "на 48 месяцаў"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "на працягу 96 месяцаў"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "на 1 месяц “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "на 3 месяцы “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "на 6 месяцаў “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "на 12 месяцаў “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "на 24 месяцы “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "на працягу 48 месяцаў “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "на 96 месяцаў “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Ахвяраванне"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Дата: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Усяго: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месяц на працягу %(duration)s месяцаў, уключаючы %(discounts)s%% зніжку)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Усяго: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месяц на працягу %(duration)s месяцаў)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Статус: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Ідэнтыфікатар: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Адмяніць"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Вы ўпэўненыя, што жадаеце адмяніць? Не адмяняйце, калі вы ўжо аплацілі."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Так, калі ласка, адмяніце"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Ваша ахвяраванне было адменена."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Зрабіць новае ахвяраванне"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Перасартаваць"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Вы ўжо аплацілі. Калі вы ўсё ж хочаце перагледзець інструкцыі па аплаце, націсніце тут:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Паказаць старыя інструкцыі па аплаце"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Дзякуй за ваш ахвяраванне!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Калі вы яшчэ не зрабілі гэтага, запішыце ваш сакрэтны ключ для ўваходу:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Інакш вы можаце быць заблакіраваны ў гэтым акаўнце!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Інструкцыі па аплаце цяпер састарэлі. Калі вы хочаце зрабіць яшчэ адну ахвяру, выкарыстоўвайце кнопку “Reorder” вышэй."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Важная заўвага:</strong> Крыптацэны могуць моцна вагацца, часам нават на 20%% за некалькі хвілін. Гэта ўсё яшчэ менш, чым зборы, якія мы нясем з многімі пастаўшчыкамі плацяжоў, якія часта бяруць 50-60%% за працу з такой \"ценявой дабрачыннасцю\", як наша. <u>Калі вы дашлеце нам квітанцыю з першапачатковай цаной, якую вы заплацілі, мы ўсё роўна залічым ваш рахунак на абраны ўзровень сяброўства</u> (пакуль квітанцыя не старэйшая за некалькі гадзін). Мы вельмі ўдзячныя, што вы гатовыя мірыцца з такімі рэчамі, каб падтрымаць нас! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Гэта ахвяраванне скончылася. Калі ласка, адмяніце і стварыце новае."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Інструкцыі па криптовалютам"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Пералічыце на адзін з нашых крыптарахункаў"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Ахвяруйце агульную суму %(total)s на адзін з гэтых адрасоў:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Купіць Bitcoin на Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Знайдзіце старонку “Crypto” у вашым дадатку або на сайце PayPal. Звычайна гэта знаходзіцца ў раздзеле “Фінансы”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Выконвайце інструкцыі для пакупкі Bitcoin (BTC). Вам трэба купіць толькі тую суму, якую вы хочаце ахвяраваць, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Пералічыце Bitcoin на наш адрас"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Перайдзіце на старонку “Bitcoin” у вашым дадатку або на сайце PayPal. Націсніце кнопку “Transfer” %(transfer_icon)s, а затым “Send”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Увядзіце наш адрас Bitcoin (BTC) у якасці атрымальніка і выконвайце інструкцыі для адпраўкі вашага ахвяравання ў памеры %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Інструкцыі па крэдытнай / дэбетавай карце"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Ахвяраваць праз нашу старонку крэдытных / дэбетавых картак"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Ахвяруйце %(amount)s на <a %(a_page)s>гэтым старонцы</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Глядзіце пакрокавую інструкцыю ніжэй."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Статус:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Чакаем пацверджання (абнавіце старонку, каб праверыць)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Чаканне перадачы (абнавіце старонку, каб праверыць)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Час, які застаўся:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(вы можаце адмяніць і стварыць новае ахвяраванне)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Каб скінуць таймер, проста стварыце новае ахвяраванне."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Абнавіць статус"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Калі ў вас узнікнуць праблемы, калі ласка, звяжыцеся з намі па адрасе %(email)s і ўключыце як мага больш інфармацыі (напрыклад, скрыншоты)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Калі вы ўжо аплацілі:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Часам пацверджанне можа заняць да 24 гадзін, таму абавязкова абнавіце гэтую старонку (нават калі яна пратэрмінавана)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Купіць манету PYUSD на PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Выконвайце інструкцыі, каб набыць манету PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума, якую вы ахвяруеце (%(amount)s), каб пакрыць выдаткі на транзакцыі. Вы захаваеце ўсё, што засталося."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Перайдзіце на старонку \"PYUSD\" у вашым дадатку або на сайце PayPal. Націсніце кнопку \"Пераклад\" %(icon)s, а затым \"Адправіць\"."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Перадаць %(amount)s да %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Купіць Bitcoin (BTC) у Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Перайдзіце на старонку “Bitcoin” (BTC) у Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума вашага ахвяравання (%(amount)s), каб пакрыць транзакцыйныя зборы. Вы захаваеце ўсё, што застанецца."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Перадайце Bitcoin на наш адрас"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Націсніце кнопку “Send bitcoin”, каб зрабіць “вывад”. Пераключыцеся з долараў на BTC, націснуўшы на значок %(icon)s. Увядзіце суму BTC ніжэй і націсніце “Send”. Калі ўзнікнуць цяжкасці, глядзіце <a %(help_video)s>гэта відэа</a>."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Для невялікіх ахвяраванняў (менш за $25) вам можа спатрэбіцца выкарыстоўваць Rush або Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Купіць Bitcoin (BTC) у Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Перайдзіце на старонку “Crypto” у Revolut, каб купіць Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума вашага ахвяравання (%(amount)s), каб пакрыць транзакцыйныя зборы. Вы захаваеце ўсё, што застанецца."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Перадайце Bitcoin на наш адрас"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Націсніце кнопку “Send bitcoin”, каб зрабіць “вывад”. Пераключыцеся з еўра на BTC, націснуўшы на значок %(icon)s. Увядзіце суму BTC ніжэй і націсніце “Send”. Калі ўзнікнуць цяжкасці, глядзіце <a %(help_video)s>гэта відэа</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Абавязкова выкарыстоўвайце колькасць BTC ніжэй, <em>НЕ</em> еўра або долары, інакш мы не атрымаем правільную суму і не зможам аўтаматычна пацвердзіць ваша сяброўства."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Для невялікіх ахвяраванняў (менш за $25) вам можа спатрэбіцца выкарыстоўваць Rush або Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Выкарыстоўвайце любы з наступных экспрэс-сэрвісаў “крэдытная карта ў Bitcoin”, якія займаюць усяго некалькі хвілін:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Запоўніце наступныя дэталі ў форме:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Сума BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Калі ласка, выкарыстоўвайце <span %(underline)s>дакладную суму</span>. Вашы агульныя выдаткі могуць быць вышэйшымі з-за камісій за крэдытныя карты. Для невялікіх сум гэта можа быць больш, чым наша зніжка, на жаль."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Адрас BTC / Bitcoin (знешні кашалёк):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s інструкцыі"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Мы падтрымліваем толькі стандартную версію крыптаманет, без экзатычных сетак або версій манет. Пацверджанне транзакцыі можа заняць да гадзіны, у залежнасці ад манеты."

msgid "page.donation.crypto_qr_code_title"
msgstr "Скануйце QR -код для аплаты"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Скануйце гэты QR -код з дапамогай прыкладання Crypto Wallet, каб хутка запоўніць плацежныя дадзеныя"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Падарункавая карта Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Калі ласка, выкарыстоўвайце <a %(a_form)s>афіцыйную форму Amazon.com</a>, каб адправіць нам падарункавую карту на %(amount)s на электронную пошту ніжэй."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Мы не можам прыняць іншыя метады падарункавых картак, <strong>толькі адпраўленыя непасрэдна з афіцыйнай формы на Amazon.com</strong>. Мы не зможам вярнуць вашу падарункавую картку, калі вы не выкарыстоўваеце гэтую форму."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Увядзіце дакладную суму: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Калі ласка, НЕ пішыце сваё паведамленне."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Электронная пошта атрымальніка \"Каму\" у форме:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Унікальна для вашага акаўнта, не дзяліцеся."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Выкарыстоўваць толькі адзін раз."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Чакаем падарункавую карту… (абнавіце старонку, каб праверыць)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Пасля адпраўкі вашай падарункавай карты наша аўтаматычная сістэма пацвердзіць яе на працягу некалькіх хвілін. Калі гэта не спрацуе, паспрабуйце адправіць падарункавую карту яшчэ раз (<a %(a_instr)s>інструкцыі</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Калі гэта ўсё яшчэ не працуе, калі ласка, напішыце нам па электроннай пошце, і Ганна праверыць гэта ўручную (гэта можа заняць некалькі дзён), і абавязкова ўкажыце, ці спрабавалі вы паўторна адправіць ужо."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Прыклад:"

msgid "page.donate.strange_account"
msgstr "Звярніце ўвагу, што імя ўліковага запісу або малюнак можа выглядаць дзіўна. Няма неабходнасці турбавацца! Гэтымі ўліковымі запісамі кіруюць нашы партнёры па ахвяраваннях. Нашы акаўнты не былі ўзламаныя."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Інструкцыі Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Ахвяруйце праз Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Ахвяруйце агульную суму %(total)s з дапамогай <a %(a_account)s>гэтага рахунку Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Калі старонка ахвяраванняў будзе заблакаваная, паспрабуйце іншае інтэрнэт-злучэнне (напрыклад, VPN або мабільны інтэрнэт)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "На жаль, старонка Alipay часта даступная толькі з <strong>мацерыковага Кітая</strong>. Магчыма, вам трэба будзе часова адключыць ваш VPN або выкарыстоўваць VPN для мацерыковага Кітая (часам таксама працуе Ганконг)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Зрабіце ахвяраванне (скануйце QR-код або націсніце кнопку)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Адкрыйце <a %(a_href)s>старонку ахвяраванняў з QR-кодам</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Скануйце QR-код з дапамогай прыкладання Alipay або націсніце кнопку, каб адкрыць прыкладанне Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Калі ласка, будзьце цярплівыя; старонка можа загружацца некаторы час, бо яна знаходзіцца ў Кітаі."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Інструкцыі WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Ахвяраваць праз WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Ахвяруйце агульную суму %(total)s з дапамогай <a %(a_account)s>гэтага акаўнта WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Інструкцыі Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Ахвяраваць на Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Ахвяруйце агульную суму %(total)s, выкарыстоўваючы <a %(a_account)s>гэты Pix рахунак"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Адпраўце нам квітанцыю па электроннай пошце"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Адпраўце квітанцыю або скрыншот на ваш асабісты адрас для праверкі. НЕ выкарыстоўвайце гэты адрас электроннай пошты для вашага ахвяравання праз PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Адпраўце квітанцыю або скрыншот на ваш асабісты адрас для праверкі:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Калі курс криптавалюты змяніўся падчас транзакцыі, абавязкова ўключыце квітанцыю, якая паказвае першапачатковы курс абмену. Мы вельмі ўдзячныя вам за тое, што вы выкарыстоўваеце криптавалюту, гэта нам вельмі дапамагае!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Калі вы адправілі квітанцыю па электроннай пошце, націсніце гэтую кнопку, каб Ганна магла ўручную праверыць яе (гэта можа заняць некалькі дзён):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Так, я адправіў па электроннай пошце квітанцыю"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Дзякуй за вашу ахвяру! Анна ўручную актывуе ваша сяброўства на працягу некалькіх дзён."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Пакрокавая інструкцыя"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Некаторыя крокі згадваюць крыпта-кашалькі, але не хвалюйцеся, вам не трэба нічога ведаць пра крыпта для гэтага."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Увядзіце ваш email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Выберыце спосаб аплаты."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Выберыце спосаб аплаты зноў."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Выберыце «Самастойны» кашалёк."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Націсніце “Я пацвярджаю ўласнасць”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Вы павінны атрымаць квітанцыю па электроннай пошце. Калі ласка, дашліце яе нам, і мы пацвердзім ваша ахвяраванне як мага хутчэй."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Калі ласка, пачакайце прынамсі <span %(span_hours)s>24 гадзіны</span> (і абнавіце гэтую старонку), перш чым звязацца з намі."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Калі вы зрабілі памылку падчас аплаты, мы не можам зрабіць вяртанне, але паспрабуем выправіць сітуацыю."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Мае ахвяраванні"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Дэталі ахвяраванняў не паказваюцца публічна."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Ахвяраванняў пакуль няма. <a %(a_donate)s>Зрабіць маё першае ахвяраванне.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Зрабіце яшчэ адно ахвяраванне."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Загружаныя файлы"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Загрузкі з хуткіх партнёрскіх сервераў пазначаны %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Калі вы запампавалі файл з хуткімі і павольнымі запампоўкамі, ён з'явіцца двойчы."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Хуткія загрузкі за апошнія 24 гадзіны ўлічваюцца ў штодзённым ліміце."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Усе часы ў UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Загружаныя файлы не паказваюцца публічна."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Файлы яшчэ не спампаваны."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Апошнія 18 гадзін"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Раней"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Уліковы запіс"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Увайсці / Зарэгістравацца"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Ідэнтыфікатар уліковага запісу: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Публічны профіль: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Сакрэтны ключ (не дзяліцеся!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "паказаць"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Членства: <strong>%(tier_name)s</strong> да %(until_date)s <a %(a_extend)s>(падаўжэнне)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Членства: <strong>Няма</strong> <a %(a_become)s>(стаць членам)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Хуткія загрузкі выкарыстаны (апошнія 24 гадзіны): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "якія загрузкі?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Эксклюзіўная група ў Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Далучайцеся да нас тут!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Абнавіце да <a %(a_tier)s>вышэйшага ўзроўню</a>, каб далучыцца да нашай групы."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Звяжыцеся з Аннай па %(email)s, калі вы зацікаўлены ў абнаўленні вашага членства да вышэйшага ўзроўню."

#, fuzzy
msgid "page.contact.title"
msgstr "Кантактны email"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Вы можаце аб'яднаць некалькі членстваў (хуткасць загрузак за 24 гадзіны будзе сумавана)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Публічны профіль"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Загружаныя файлы"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Мае ахвяраванні"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Выйсці"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Вы выйшлі з сістэмы. Перазагрузіце старонку, каб зноў увайсці."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Рэгістрацыя паспяховая! Ваш сакрэтны ключ: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Захоўвайце гэты ключ асцярожна. Калі вы яго страціце, вы страціце доступ да свайго ўліковага запісу."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Закладка.</strong> Вы можаце захаваць гэтую старонку ў закладках, каб атрымаць ваш ключ.</li><li %(li_item)s><strong>Спампаваць.</strong> Націсніце <a %(a_download)s>гэтую спасылку</a>, каб спампаваць ваш ключ.</li><li %(li_item)s><strong>Менеджар пароляў.</strong> Выкарыстоўвайце менеджар пароляў, каб захаваць ключ, калі ўводзіце яго ніжэй.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Увядзіце ваш сакрэтны ключ для ўваходу:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Сакрэтны ключ"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Увайсці"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Няправільны сакрэтны ключ. Праверце ваш ключ і паспрабуйце зноў, альбо зарэгіструйце новы акаўнт ніжэй."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Не губляйце свой ключ!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Яшчэ не маеце ўліковага запісу?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Зарэгістраваць новы ўліковы запіс"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Калі вы страцілі ключ, калі ласка, <a %(a_contact)s>звяжыцеся з намі</a> і падайце як мага больш інфармацыі."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Магчыма, вам давядзецца часова стварыць новы ўліковы запіс, каб звязацца з намі."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Стары ўліковы запіс на аснове электроннай пошты? Увядзіце ваш <a %(a_open)s>электронны адрас тут</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Спіс"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "рэдагаваць"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Захаваць"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Захавана. Калі ласка, перазагрузіце старонку."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, паспрабуйце яшчэ раз."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Спіс па %(by)s, створаны <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Спіс пусты."

#, fuzzy
msgid "page.list.new_item"
msgstr "Дадайце або выдаліце з гэтага спісу, знайшоўшы файл і адкрыўшы ўкладку “Спісы”."

#, fuzzy
msgid "page.profile.title"
msgstr "Профіль"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Профіль не знойдзены."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "рэдагаваць"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Змяніце сваё імя карыстальніка. Ваш ідэнтыфікатар (частка пасля “#”) нельга змяніць."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Захаваць"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Захавана. Калі ласка, перазагрузіце старонку."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, паспрабуйце яшчэ раз."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Профіль створаны <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Спісы"

msgid "page.profile.lists.no_lists"
msgstr "У цяперашні час няма спісаў"

msgid "page.profile.lists.new_list"
msgstr "Стварыце новы спіс, знайшоўшы файл і адкрыўшы ўкладку \"спісы\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Рэформа аўтарскага права неабходная для нацыянальнай бяспекі"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Кароткі змест: Кітайскія LLM (уключаючы DeepSeek) навучаюцца на маім незаконным архіве кніг і артыкулаў — самым вялікім у свеце. Захад павінен перагледзець заканадаўства аб аўтарскім праве ў інтарэсах нацыянальнай бяспекі."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "суправаджальныя артыкулы ад TorrentFreak: <a %(torrentfreak)s>першы</a>, <a %(torrentfreak_2)s>другі</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Не так даўно \"ценявыя бібліятэкі\" былі на мяжы знікнення. Sci-Hub, велізарны незаконны архіў навуковых артыкулаў, спыніў прыём новых прац з-за судовых пазоваў. \"Z-Library\", самая вялікая незаконная бібліятэка кніг, убачыла, як яе меркаваныя стваральнікі былі арыштаваны па крымінальных абвінавачваннях у парушэнні аўтарскага права. Яны неверагодным чынам змаглі ўцячы ад арышту, але іх бібліятэка ўсё яшчэ пад пагрозай."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Калі Z-Library сутыкнулася з закрыццём, я ўжо зрабіў рэзервовую копію ўсёй яе бібліятэкі і шукаў платформу для яе размяшчэння. Гэта была мая матывацыя для стварэння Архіва Анны: працяг місіі, якая стаяла за гэтымі ранейшымі ініцыятывамі. З тых часоў мы выраслі да самай вялікай ценявой бібліятэкі ў свеце, якая змяшчае больш за 140 мільёнаў тэкстаў, абароненых аўтарскім правам, у розных фарматах — кнігі, навуковыя артыкулы, часопісы, газеты і іншае."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Мая каманда і я — ідэолагі. Мы лічым, што захаванне і размяшчэнне гэтых файлаў маральна правільнае. Бібліятэкі па ўсім свеце сутыкаюцца са скарачэннем фінансавання, і мы таксама не можам даверыць спадчыну чалавецтва карпарацыям."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Потым з'явіўся штучны інтэлект. Амаль усе буйныя кампаніі, якія ствараюць LLM, звязаліся з намі, каб навучацца на нашых дадзеных. Большасць (але не ўсе!) кампаній з ЗША перагледзелі сваё рашэнне, калі зразумелі незаконную прыроду нашай працы. У адрозненне ад гэтага, кітайскія кампаніі з энтузіязмам прынялі нашу калекцыю, відавочна, не турбуючыся пра яе законнасць. Гэта варта адзначыць, улічваючы ролю Кітая як падпісанта амаль усіх асноўных міжнародных дагавораў аб аўтарскім праве."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Мы далі высокахуткасны доступ каля 30 кампаніям. Большасць з іх — кампаніі LLM, а некаторыя — брокеры дадзеных, якія будуць перапрадаваць нашу калекцыю. Большасць з іх — кітайскія, хоць мы таксама працавалі з кампаніямі з ЗША, Еўропы, Расіі, Паўднёвай Карэі і Японіі. DeepSeek <a %(arxiv)s>прызнала</a>, што ранейшая версія была навучана на частцы нашай калекцыі, хоць яны маўчаць пра сваю апошнюю мадэль (верагодна, таксама навучаную на нашых дадзеных)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Калі Захад хоча заставацца наперадзе ў гонцы LLM і, у канчатковым выніку, AGI, яму трэба перагледзець сваю пазіцыю адносна аўтарскага права, і хутка. Незалежна ад таго, ці згодныя вы з намі ў нашым маральным выпадку, гэта цяпер становіцца пытаннем эканомікі і нават нацыянальнай бяспекі. Усе магутныя блокі ствараюць штучных супер-навукоўцаў, супер-хакераў і супер-вайскоўцаў. Свабода інфармацыі становіцца пытаннем выжывання для гэтых краін — нават пытаннем нацыянальнай бяспекі."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Наша каманда з усяго свету, і ў нас няма пэўнай арыентацыі. Але мы б заклікалі краіны з моцнымі законамі аб аўтарскім праве выкарыстоўваць гэтую экзістэнцыяльную пагрозу для іх рэфармавання. Дык што рабіць?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Наша першая рэкамендацыя простая: скараціць тэрмін дзеяння аўтарскага права. У ЗША аўтарскае права прадастаўляецца на 70 гадоў пасля смерці аўтара. Гэта абсурдна. Мы можам прывесці гэта ў адпаведнасць з патэнтамі, якія прадастаўляюцца на 20 гадоў пасля падачы заяўкі. Гэта павінна быць больш чым дастаткова часу для аўтараў кніг, артыкулаў, музыкі, мастацтва і іншых творчых прац, каб атрымаць поўную кампенсацыю за свае намаганні (уключаючы доўгатэрміновыя праекты, такія як экранізацыі)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Затым, як мінімум, палітыкі павінны ўключыць выключэнні для масавага захавання і распаўсюджвання тэкстаў. Калі асноўнай праблемай з'яўляецца страта даходу ад індывідуальных кліентаў, распаўсюджванне на асабістым узроўні можа заставацца забароненым. У сваю чаргу, тыя, хто здольны кіраваць вялікімі сховішчамі — кампаніі, якія навучаюць LLM, разам з бібліятэкамі і іншымі архівамі — будуць ахоплены гэтымі выключэннямі."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Некаторыя краіны ўжо робяць версію гэтага. TorrentFreak <a %(torrentfreak)s>паведаміў</a>, што Кітай і Японія ўвялі выключэнні для штучнага інтэлекту ў свае законы аб аўтарскім праве. Нам не зразумела, як гэта ўзаемадзейнічае з міжнароднымі дагаворамі, але гэта, безумоўна, дае абарону іх унутраным кампаніям, што тлумачыць тое, што мы назіраем."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Што тычыцца Архіва Анны — мы працягнем нашу падпольную працу, заснаваную на маральных перакананнях. Але наша самая вялікая жаданне — выйсці на святло і легальна ўзмацніць наш уплыў. Калі ласка, рэфармуйце аўтарскае права."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Прачытайце суправаджальныя артыкулы ад TorrentFreak: <a %(torrentfreak)s>першы</a>, <a %(torrentfreak_2)s>другі</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Пераможцы прэміі ў $10,000 за візуалізацыю ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Кароткі змест: Мы атрымалі неверагодныя заяўкі на прэмію ў $10,000 за візуалізацыю ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Некалькі месяцаў таму мы абвясцілі <a %(all_isbns)s>прэмію ў $10,000</a> за лепшую магчымую візуалізацыю нашых дадзеных, якія паказваюць прастору ISBN. Мы падкрэслілі паказ, якія файлы мы ўжо архівавалі/не архівавалі, і пазней дадалі набор дадзеных, які апісвае, колькі бібліятэк утрымліваюць ISBN (меркаванне рэдкасці)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Мы былі перагружаны адказам. Было столькі творчасці. Вялікі дзякуй усім, хто ўдзельнічаў: ваша энергія і энтузіязм заразлівыя!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "У рэшце рэшт, мы хацелі адказаць на наступныя пытанні: <strong>якія кнігі існуюць у свеце, колькі з іх мы ўжо архівавалі, і на якіх кнігах нам варта засяродзіцца далей?</strong> Прыемна бачыць, што так шмат людзей клапоцяцца пра гэтыя пытанні."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Мы пачалі з базавай візуалізацыі самі. Менш чым у 300 кб гэтая карціна лаканічна прадстаўляе найбуйнейшы цалкам адкрыты \"спіс кніг\", калі-небудзь сабраны ў гісторыі чалавецтва:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Усе ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Файлы ў Архіве Анны"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Уцечка дадзеных CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Індэкс eBook ад EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Інтэрнэт-архіў"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Глабальны рэестр выдаўцоў ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Расійская дзяржаўная бібліятэка"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Імперская бібліятэка Трантора"

#, fuzzy
msgid "common.back"
msgstr "Назад"

#, fuzzy
msgid "common.forward"
msgstr "Наперад"

#, fuzzy
msgid "common.last"
msgstr "Апошні"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Калі ласка, глядзіце <a %(all_isbns)s>арыгінальны блог</a> для больш падрабязнай інфармацыі."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Мы абвясцілі выклік для паляпшэння гэтага. Мы прапанавалі ўзнагароду за першае месца ў памеры $6,000, за другое месца — $3,000, і за трэцяе месца — $1,000. З-за велізарнага водгуку і неверагодных падач мы вырашылі крыху павялічыць прызавы фонд і ўзнагародзіць чатыры трэція месцы па $500 кожнае. Пераможцы ніжэй, але абавязкова паглядзіце ўсе падачы <a %(annas_archive)s>тут</a>, або спампуйце наш <a %(a_2025_01_isbn_visualization_files)s>аб'яднаны торэнт</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Першае месца $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Гэтая <a %(phiresky_github)s>падача</a> (<a %(annas_archive_note_2951)s>каментар Gitlab</a>) з'яўляецца проста ўсім, што мы хацелі, і нават больш! Асабліва нам спадабаліся неверагодна гнуткія варыянты візуалізацыі (нават з падтрымкай карыстальніцкіх шэйдараў), але з поўным спісам прэсэтаў. Нам таксама спадабалася, наколькі хутка і плаўна ўсё працуе, простая рэалізацыя (якая нават не мае бэкэнда), разумная мінікарта і шырокае тлумачэнне ў іх <a %(phiresky_github)s>блог-паведамленні</a>. Неверагодная праца і заслужаны пераможца!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Другое месца $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Яшчэ адна неверагодная <a %(annas_archive_note_2913)s>падача</a>. Не такая гнуткая, як першае месца, але нам на самай справе больш спадабалася яе макра-ўзроўневая візуалізацыя ў параўнанні з першым месцам (крывізна, межы, маркіроўка, вылучэнне, панарамаванне і маштабаванне). <a %(annas_archive_note_2971)s>Каментар</a> Джо Дэвіса рэзаніраваў з намі:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "«Хоць ідэальныя квадраты і прастакутнікі матэматычна прывабныя, яны не забяспечваюць лепшую лакальнасць у кантэксце картаграфавання. Я лічу, што асіметрыя, уласцівая гэтым крывым Хільберта або класічным Мортанам, не з'яўляецца недахопам, а асаблівасцю. Як і знакаміты абрыс Італіі ў форме бота, які робіць яе адразу пазнавальнай на карце, унікальныя \"асаблівасці\" гэтых крывых могуць служыць пазнавальнымі арыенцірамі. Гэтая адметнасць можа палепшыць прасторавую памяць і дапамагчы карыстальнікам арыентавацца, патэнцыйна палягчаючы пошук канкрэтных рэгіёнаў або выяўленне шаблонаў»."

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "І ўсё яшчэ шмат варыянтаў для візуалізацыі і рэндэрынгу, а таксама неверагодна плаўны і інтуітыўна зразумелы інтэрфейс. Цвёрдае другое месца!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Трэцяе месца $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "У гэтай <a %(annas_archive_note_2940)s>падачы</a> нам вельмі спадабаліся розныя віды праглядаў, у прыватнасці параўнальны і выдавецкі прагляды."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Трэцяе месца $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Хоць інтэрфейс не самы адшліфаваны, гэтая <a %(annas_archive_note_2917)s>падача</a> адпавядае многім крытэрыям. Асабліва нам спадабалася яе функцыя параўнання."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Трэцяе месца $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Як і першае месца, гэтая <a %(annas_archive_note_2975)s>падача</a> уразіла нас сваёй гнуткасцю. У канчатковым выніку гэта тое, што робіць інструмент візуалізацыі выдатным: максімальная гнуткасць для магутных карыстальнікаў, захоўваючы пры гэтым прастату для звычайных карыстальнікаў."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Трэцяе месца $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Апошняя <a %(annas_archive_note_2947)s>падача</a>, якая атрымала ўзнагароду, даволі простая, але мае некаторыя унікальныя асаблівасці, якія нам вельмі спадабаліся. Нам спадабалася, як яны паказваюць, колькі набораў дадзеных ахопліваюць пэўны ISBN як меру папулярнасці/надзейнасці. Нам таксама вельмі спадабалася прастата, але эфектыўнасць выкарыстання паўзунка непразрыстасці для параўнанняў."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Значныя ідэі"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Некаторыя іншыя ідэі і рэалізацыі, якія нам асабліва спадабаліся:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Нябёсы для рэдкасці"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Жывая статыстыка"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Анатацыі, а таксама жывая статыстыка"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Унікальны выгляд карты і фільтры"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Крутая стандартная каляровая схема і цеплавая карта."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Лёгкае пераключэнне Datasets для хуткіх параўнанняў."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Прыгожыя этыкеткі."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Шкала з колькасцю кніг."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Шмат паўзункоў для параўнання Datasets, як быццам вы DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Мы маглі б працягваць яшчэ доўга, але давайце спынімся тут. Абавязкова паглядзіце ўсе падачы <a %(annas_archive)s>тут</a>, або спампуйце наш <a %(a_2025_01_isbn_visualization_files)s>аб'яднаны торэнт</a>. Так шмат падач, і кожная прыносіць унікальную перспектыву, ці то ў інтэрфейсе, ці ў рэалізацыі."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Мы, па меншай меры, уключым падачу, якая заняла першае месца, у наш асноўны сайт, і, магчыма, некаторыя іншыя. Мы таксама пачалі думаць пра тое, як арганізаваць працэс выяўлення, пацверджання і затым архівавання самых рэдкіх кніг. Больш падрабязнасцяў будзе пазней."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Дзякуй усім, хто ўдзельнічаў. Гэта дзіўна, што так шмат людзей клапоцяцца."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Нашы сэрцы поўныя ўдзячнасці."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Візуалізацыя ўсіх ISBN — ўзнагарода $10,000 да 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Гэтая карціна ўяўляе сабой найбуйнейшы цалкам адкрыты \"спіс кніг\", калі-небудзь сабраны ў гісторыі чалавецтва."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Гэтая карціна мае памер 1000×800 пікселяў. Кожны піксель уяўляе сабой 2,500 ISBN. Калі ў нас ёсць файл для ISBN, мы робім гэты піксель больш зялёным. Калі мы ведаем, што ISBN быў выдадзены, але ў нас няма адпаведнага файла, мы робім яго больш чырвоным."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Менш чым у 300 кб, гэтая карціна лаканічна ўяўляе сабой найбуйнейшы цалкам адкрыты \"спіс кніг\", калі-небудзь сабраны ў гісторыі чалавецтва (некалькі сотняў ГБ у сціснутым выглядзе)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Гэта таксама паказвае: засталося шмат працы па рэзервовым капіраванні кніг (у нас толькі 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Фон"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Як можа «Архіў Анны» дасягнуць сваёй місіі па рэзервовым капіраванні ўсіх ведаў чалавецтва, не ведаючы, якія кнігі яшчэ існуюць? Нам патрэбны спіс задач. Адзін з спосабаў гэта зрабіць — праз нумары ISBN, якія з 1970-х гадоў прысвойваюцца кожнай апублікаванай кнізе (у большасці краін)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Няма цэнтральнага органа, які ведае ўсе прысваенні ISBN. Замест гэтага гэта размеркаваная сістэма, дзе краіны атрымліваюць дыяпазоны нумароў, якія затым прысвойваюцца буйным выдаўцам, якія могуць далей падзяляць дыяпазоны на меншых выдаўцоў. Нарэшце, індывідуальныя нумары прысвойваюцца кнігам."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Мы пачалі картаграфаваць ISBN <a %(blog)s>два гады таму</a> з нашым скрэйпінгам ISBNdb. З тых часоў мы скрэйпілі шмат іншых крыніц metadata, такіх як <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby і іншыя. Поўны спіс можна знайсці на старонках «Datasets» і «Torrents» на «Архіве Анны». Цяпер у нас самая вялікая цалкам адкрытая, лёгка загружальная калекцыя metadata кніг (і, такім чынам, ISBN) у свеце."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Мы <a %(blog)s>шмат пісалі</a> пра тое, чаму нам важна захаванне, і чаму мы зараз знаходзімся ў крытычным акне. Мы павінны цяпер вызначыць рэдкія, недастаткова асветленыя і ўнікальна рызыкоўныя кнігі і захаваць іх. Добрае metadata на ўсе кнігі ў свеце дапамагае ў гэтым."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Візуалізацыя"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Акрамя агляднай выявы, мы таксама можам паглядзець на асобныя datasets, якія мы набылі. Выкарыстоўвайце выпадальнае меню і кнопкі, каб пераключацца паміж імі."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "У гэтых малюнках можна ўбачыць шмат цікавых узораў. Чаму існуе нейкая рэгулярнасць ліній і блокаў, якая, здаецца, адбываецца на розных маштабах? Што такое пустыя вобласці? Чаму пэўныя datasets так згрупаваныя? Мы пакінем гэтыя пытанні як практыкаванне для чытача."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Узнагарода $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Тут шмат чаго можна даследаваць, таму мы аб'яўляем узнагароду за паляпшэнне візуалізацыі вышэй. У адрозненне ад большасці нашых узнагарод, гэтая мае абмежаваны час. Вы павінны <a %(annas_archive)s>падаць</a> свой адкрыты код да 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Лепшая падача атрымае $6,000, другое месца — $3,000, а трэцяе месца — $1,000. Усе ўзнагароды будуць выплачаны з выкарыстаннем Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Ніжэй прыведзены мінімальныя крытэрыі. Калі ніводная падача не адпавядае крытэрыям, мы ўсё роўна можам прысудзіць некаторыя ўзнагароды, але гэта будзе на нашым меркаванні."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Форкніце гэты рэпазітар і адрэдагуйце гэты HTML блог-пост (ніякія іншыя бэкэнды, акрамя нашага Flask бэкэнда, не дазволены)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Зрабіце малюнак вышэй плаўна маштабаваным, каб вы маглі маштабаваць да індывідуальных ISBN. Націск на ISBN павінен перанакіроўваць вас на старонку metadata або пошук у «Архіве Анны»."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Вы ўсё яшчэ павінны мець магчымасць пераключацца паміж усімі рознымі datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Дыяпазоны краін і выдаўцоў павінны вылучацца пры навядзенні курсора. Вы можаце выкарыстоўваць, напрыклад, <a %(github_xlcnd_isbnlib)s>data4info.py у isbnlib</a> для інфармацыі пра краіны і наш скрэйпінг «isbngrp» для выдаўцоў (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Гэта павінна добра працаваць на настольных і мабільных прыладах."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Для дадатковых балаў (гэта проста ідэі — дайце вашай творчасці свабоду):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Вялікая ўвага будзе нададзена зручнасці выкарыстання і візуальнай прывабнасці."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Паказвайце фактычнае metadata для індывідуальных ISBN пры маштабаванні, такіх як назва і аўтар."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Лепшая кривая запаўнення прасторы. Напрыклад, зигзаг, які ідзе ад 0 да 4 у першым радку, а затым назад (у зваротным парадку) ад 5 да 9 у другім радку — рэкурсіўна прымяняецца."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Розныя або наладжвальныя каляровыя схемы."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Спецыяльныя прагляды для параўнання Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Спосабы адладкі праблем, такіх як іншыя metadata, якія не вельмі добра супадаюць (напрыклад, значна розныя назвы)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Анатацыя малюнкаў з каментарамі на ISBN або дыяпазонах."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Любая эврыстыка для вызначэння рэдкіх або пад пагрозай кніг."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Любыя творчыя ідэі, якія вы можаце прыдумаць!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Вы МОЖАЦЕ цалкам адысці ад мінімальных крытэрыяў і зрабіць зусім іншую візуалізацыю. Калі гэта сапраўды ўражвае, то гэта кваліфікуецца для ўзнагароды, але па нашым меркаванні."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Зрабіце падачы, пакідаючы каментар да <a %(annas_archive)s>гэтага пытання</a> з спасылкай на ваш форкнуты рэпазітар, запыт на зліццё або розніцу."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Код"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Код для генерацыі гэтых малюнкаў, а таксама іншыя прыклады, можна знайсці ў <a %(annas_archive)s>гэтым каталогу</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Мы прыдумалі кампактны фармат даных, з якім уся неабходная інфармацыя ISBN складае каля 75 МБ (у сціснутым выглядзе). Апісанне фармату даных і код для яго генерацыі можна знайсці <a %(annas_archive_l1244_1319)s>тут</a>. Для ўзнагароды вам не абавязкова выкарыстоўваць гэта, але гэта, верагодна, самы зручны фармат для пачатку. Вы можаце трансфармаваць нашы metadata як заўгодна (хаця ўвесь ваш код павінен быць з адкрытым зыходным кодам)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Мы не можам дачакацца, каб убачыць, што вы прыдумаеце. Поспехаў!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Архіў Ганны: стандартызацыя выпускоў з найбуйнейшай у свеце ценявой бібліятэкі"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Архіў Ганны стаў найбуйнейшай ценявой бібліятэкай у свеце, што патрабуе ад нас стандартызацыі нашых выпускоў."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Архіў Ганны</a> стаў, безумоўна, найбуйнейшай ценявой бібліятэкай у свеце і адзінай ценявой бібліятэкай такога маштабу, якая цалкам з адкрытым зыходным кодам і адкрытымі данымі. Ніжэй прыведзена табліца з нашай старонкі Datasets (трохі змененая):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Мы дасягнулі гэтага трыма спосабамі:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Адлюстраванне існуючых ценявых бібліятэк з адкрытымі данымі (такіх як Sci-Hub і Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Дапамога ценявым бібліятэкам, якія хочуць быць больш адкрытымі, але не мелі часу або рэсурсаў для гэтага (напрыклад, калекцыя коміксаў Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Скрапінг бібліятэк, якія не жадаюць дзяліцца ў вялікіх аб'ёмах (такіх як Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Для (2) і (3) мы цяпер кіруем значнай калекцыяй торэнтаў самастойна (сотні ТБ). Да гэтага часу мы разглядалі гэтыя калекцыі як адзіночныя выпадкі, што азначае індывідуальную інфраструктуру і арганізацыю даных для кожнай калекцыі. Гэта дадае значныя накладныя выдаткі да кожнага выпуску і робіць асабліва цяжкім выкананне больш паступовых выпускаў."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Таму мы вырашылі стандартызаваць нашы выпускі. Гэта тэхнічны блог, у якім мы прадстаўляем наш стандарт: <strong>Кантэйнеры Архіва Анны</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Мэты дызайну"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Наш асноўны выпадак выкарыстання — распаўсюджванне файлаў і звязаных з імі metadata з розных існуючых калекцый. Нашы найбольш важныя меркаванні:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Гетэрагенныя файлы і metadata, як мага бліжэй да арыгінальнага фармату."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Гетэрагенныя ідэнтыфікатары ў крынічных бібліятэках або нават адсутнасць ідэнтыфікатараў."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Асобныя выпускі metadata супраць даных файлаў або выпускі толькі metadata (напрыклад, наш выпуск ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Распаўсюджванне праз торэнты, хоць з магчымасцю іншых метадаў распаўсюджвання (напрыклад, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Незменныя запісы, паколькі мы павінны меркаваць, што нашы торэнты будуць жыць вечна."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Паступовыя выпускі / дадаваемыя выпускі."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Машыначытальныя і запісвальныя, зручна і хутка, асабліва для нашай стэкі (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Даволі лёгкая інспекцыя чалавекам, хоць гэта другасна ў параўнанні з машыначытальнасцю."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Лёгка засяваць нашы калекцыі з дапамогай стандартнага арандаванага seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Бінарныя даныя могуць быць абслугоўваны непасрэдна вэб-серверамі, як Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Некаторыя не-мэты:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Нам не важна, каб файлы было лёгка навігаваць уручную на дыску або шукаць без папярэдняй апрацоўкі."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Нам не важна быць непасрэдна сумяшчальнымі з існуючым бібліятэчным праграмным забеспячэннем."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Хоць павінна быць лёгка для любога засяваць нашу калекцыю з дапамогай торэнтаў, мы не чакаем, што файлы будуць выкарыстоўвацца без значных тэхнічных ведаў і абавязацельстваў."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Паколькі Архіў Анны з'яўляецца адкрытым зыходным кодам, мы хочам выкарыстоўваць наш фармат непасрэдна. Калі мы абнаўляем наш індэкс пошуку, мы атрымліваем доступ толькі да публічна даступных шляхоў, каб любы, хто форкне нашу бібліятэку, мог хутка пачаць працу."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Стандарт"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "У рэшце рэшт, мы спыніліся на адносна простым стандарце. Ён даволі гнуткі, не нарматыўны і знаходзіцца ў стадыі распрацоўкі."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Кантэйнер Архіва Анны) — гэта адзін элемент, які складаецца з <strong>metadata</strong>, і, па жаданні, <strong>бінарных даных</strong>, абодва з якіх нязменныя. Ён мае глабальна унікальны ідэнтыфікатар, які называецца <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Калекцыя.</strong> Кожны AAC належыць да калекцыі, якая па вызначэнні з'яўляецца спісам AAC, якія семантычна паслядоўныя. Гэта азначае, што калі вы ўносіце значныя змены ў фармат метаданных, то вам трэба стварыць новую калекцыю."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Калекцыі “запісаў” і “файлаў”.</strong> Па звычаі, часта зручна выпускаць “запісы” і “файлы” як розныя калекцыі, каб іх можна было выпускаць па розных графіках, напрыклад, на аснове хуткасці сканавання. “Запіс” — гэта калекцыя толькі метаданных, якая змяшчае інфармацыю, такую як назвы кніг, аўтары, ISBN і г.д., у той час як “файлы” — гэта калекцыі, якія змяшчаюць самі файлы (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Фармат AACID такі: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Напрыклад, фактычны AACID, які мы выпусцілі, гэта <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: назва калекцыі, якая можа ўтрымліваць літары ASCII, лічбы і падкрэсліванні (але не двайныя падкрэсліванні)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: кароткая версія ISO 8601, заўсёды ў UTC, напрыклад, <code>20220723T194746Z</code>. Гэтае лікавае значэнне павінна манатонна павялічвацца для кожнага выпуску, хоць яго дакладная семантыка можа адрознівацца ў залежнасці ад калекцыі. Мы прапануем выкарыстоўваць час сканавання або генерацыі ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: ідэнтыфікатар, спецыфічны для калекцыі, калі гэта дастасавальна, напрыклад, ID Z-Library. Можа быць апушчаны або скарачаны. Павінен быць апушчаны або скарачаны, калі AACID у адваротным выпадку перавысіць 150 сімвалаў."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, але сціснуты да ASCII, напрыклад, з выкарыстаннем base57. У цяперашні час мы выкарыстоўваем бібліятэку Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Дыяпазон AACID.</strong> Паколькі AACID ўтрымліваюць манатонна павялічваючыяся меткі часу, мы можам выкарыстоўваць гэта для абазначэння дыяпазонаў у межах пэўнай калекцыі. Мы выкарыстоўваем гэты фармат: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, дзе меткі часу ўключныя. Гэта адпавядае натацыі ISO 8601. Дыяпазоны бесперапынныя і могуць перакрывацца, але ў выпадку перакрыцця павінны ўтрымліваць ідэнтычныя запісы, як і раней выпушчаныя ў гэтай калекцыі (паколькі AAC нязменныя). Адсутныя запісы не дапускаюцца."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Файл метаданных.</strong> Файл метаданных змяшчае метаданыя дыяпазону AAC для адной пэўнай калекцыі. Яны маюць наступныя ўласцівасці:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Імя файла павінна быць дыяпазонам AACID, з прэфіксам <code style=\"color: red\">annas_archive_meta__</code> і суфіксам <code>.jsonl.zstd</code>. Напрыклад, адзін з нашых выпускаў называецца<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Як паказана пашырэннем файла, тып файла — гэта <a %(jsonlines)s>JSON Lines</a>, сціснуты з дапамогай <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Кожны аб'ект JSON павінен утрымліваць наступныя палі на верхнім узроўні: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (апцыянальна). Іншыя палі не дапускаюцца."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> — гэта адвольныя метаданыя, у адпаведнасці з семантыкай калекцыі. Яны павінны быць семантычна паслядоўнымі ў межах калекцыі."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> з'яўляецца апцыянальным і з'яўляецца назвай тэчкі бінарных даных, якая змяшчае адпаведныя бінарныя даныя. Імя файла адпаведных бінарных даных у гэтай тэчцы — гэта AACID запісу."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Префікс <code style=\"color: red\">annas_archive_meta__</code> можа быць адаптаваны да назвы вашай установы, напрыклад, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Тэчка бінарных даных.</strong> Тэчка з бінарнымі данымі дыяпазону AAC для адной пэўнай калекцыі. Яны маюць наступныя ўласцівасці:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Назва каталога павінна быць дыяпазонам AACID, з прэфіксам <code style=\"color: green\">annas_archive_data__</code>, і без суфікса. Напрыклад, адзін з нашых фактычных выпускаў мае каталог з назвай<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Каталог павінен утрымліваць файлы даных для ўсіх AAC у межах указаннага дыяпазону. Кожны файл даных павінен мець сваё AACID у якасці імя файла (без пашырэнняў)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Рэкамендуецца рабіць гэтыя тэчкі кіраванымі па памеры, напрыклад, не больш за 100 ГБ-1 ТБ кожная, хоць гэтая рэкамендацыя можа змяняцца з цягам часу."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Торэнты.</strong> Файлы метададзеных і тэчкі з бінарнымі дадзенымі могуць быць аб'яднаны ў торэнты, з адным торэнтам на файл метададзеных або адным торэнтам на тэчку з бінарнымі дадзенымі. Торэнты павінны мець арыгінальную назву файла/каталога плюс суфікс <code>.torrent</code> у якасці назвы файла."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Прыклад"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Давайце разгледзім наш нядаўні выпуск Z-Library як прыклад. Ён складаецца з дзвюх калекцый: “<span style=\"background: #fffaa3\">zlib3_records</span>” і “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Гэта дазваляе нам асобна збіраць і выпускаць метададзеныя з рэальных файлаў кніг. Такім чынам, мы выпусцілі два торэнты з файламі метададзеных:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Мы таксама выпусцілі некалькі торэнтаў з тэчкамі бінарных дадзеных, але толькі для калекцыі “<span style=\"background: #ffd6fe\">zlib3_files</span>”, усяго 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Запусціўшы <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, мы можам убачыць, што ўнутры:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "У гэтым выпадку гэта метададзеныя кнігі, як паведамляе Z-Library. На верхнім узроўні ў нас ёсць толькі “aacid” і “metadata”, але няма “data_folder”, паколькі няма адпаведных бінарных дадзеных. AACID змяшчае “22430000” у якасці асноўнага ID, які мы бачым, узяты з “zlibrary_id”. Мы можам чакаць, што іншыя AAC у гэтай калекцыі будуць мець такую ж структуру."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Цяпер давайце запусцім <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Гэта значна меншыя метададзеныя AAC, хоць асноўная частка гэтага AAC знаходзіцца ў іншым месцы ў бінарным файле! У рэшце рэшт, у нас ёсць “data_folder” на гэты раз, таму мы можам чакаць, што адпаведныя бінарныя дадзеныя будуць размешчаны ў <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” змяшчае “zlibrary_id”, таму мы можам лёгка звязаць яго з адпаведным AAC у калекцыі “zlib_records”. Мы маглі б звязаць рознымі спосабамі, напрыклад, праз AACID — стандарт гэтага не прадпісвае."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Звярніце ўвагу, што таксама не абавязкова, каб поле “metadata” само па сабе было JSON. Гэта можа быць радок, які змяшчае XML або любы іншы фармат дадзеных. Вы нават можаце захоўваць інфармацыю аб метададзеных у звязаным бінарным блобе, напрыклад, калі гэта шмат дадзеных."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Заключэнне"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "З гэтым стандартам мы можам рабіць выпускі больш паступова і лягчэй дадаваць новыя крыніцы дадзеных. У нас ужо ёсць некалькі захапляльных выпускаў у распрацоўцы!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Мы таксама спадзяемся, што іншыя ценявыя бібліятэкі змогуць лягчэй люстраваць нашы калекцыі. У рэшце рэшт, наша мэта — захаваць чалавечыя веды і культуру назаўсёды, таму чым больш рэзервовых копій, тым лепш."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Абнаўленне Анны: цалкам адкрыты архіў, ElasticSearch, больш за 300 ГБ вокладак кніг"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Мы працавалі кругласутачна, каб забяспечыць добрую альтэрнатыву з Архівам Анны. Вось некаторыя з дасягненняў, якія мы зрабілі нядаўна."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "З-за закрыцця Z-Library і арышту (меркаваных) заснавальнікаў, мы працавалі кругласутачна, каб забяспечыць добрую альтэрнатыву з Архівам Анны (мы не будзем спасылацца на яго тут, але вы можаце знайсці яго ў Google). Вось некаторыя з дасягненняў, якія мы зрабілі нядаўна."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Архіў Анны цалкам адкрыты"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Мы лічым, што інфармацыя павінна быць свабоднай, і наш уласны код не з'яўляецца выключэннем. Мы выпусцілі ўвесь наш код на нашым прыватна размешчаным Gitlab: <a %(annas_archive)s>Праграмнае забеспячэнне Анны</a>. Мы таксама выкарыстоўваем трэкер праблем для арганізацыі нашай працы. Калі вы хочаце ўдзельнічаць у нашай распрацоўцы, гэта выдатнае месца для пачатку."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Каб даць вам уяўленне пра тое, над чым мы працуем, вазьміце нашу нядаўнюю працу па паляпшэнні прадукцыйнасці на баку кліента. Паколькі мы яшчэ не рэалізавалі пагінацыю, мы часта вярталі вельмі доўгія старонкі пошуку з 100-200 вынікамі. Мы не хацелі занадта рана абрэзаць вынікі пошуку, але гэта азначала, што гэта запавольвала некаторыя прылады. Для гэтага мы рэалізавалі невялікі трук: мы абгарнулі большасць вынікаў пошуку ў HTML каментарыі (<code><!-- --></code>), а затым напісалі невялікі Javascript, які вызначаў, калі вынік павінен стаць бачным, у гэты момант мы разгарнулі б каментар:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "Рэалізацыя \"віртуалізацыі\" DOM у 23 радках, без патрэбы ў складаных бібліятэках! Гэта той тып хуткага прагматычнага кода, які вы атрымліваеце, калі ў вас абмежаваны час і рэальныя праблемы, якія трэба вырашаць. Было паведамлена, што наш пошук цяпер добра працуе на павольных прыладах!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Яшчэ адным вялікім намаганнем было аўтаматызаваць стварэнне базы дадзеных. Калі мы запусціліся, мы проста выпадкова сабралі розныя крыніцы разам. Цяпер мы хочам падтрымліваць іх у актуальным стане, таму мы напісалі шэраг скрыптоў для загрузкі новых metadata з двух форкаў Library Genesis і інтэграцыі іх. Мэта не толькі зрабіць гэта карысным для нашага архіва, але і зрабіць усё лёгкім для тых, хто хоча паэксперыментаваць з metadata ценявой бібліятэкі. Мэта — стварыць Jupyter notebook, які будзе мець усе віды цікавых metadata, каб мы маглі праводзіць больш даследаванняў, напрыклад, вызначаць, які <a %(blog)s>працэнт ISBN захоўваецца назаўсёды</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Нарэшце, мы абнавілі нашу сістэму ахвяраванняў. Цяпер вы можаце выкарыстоўваць крэдытную карту, каб непасрэдна ўнесці грошы ў нашы крыптакашалькі, не маючы патрэбы ведаць што-небудзь пра криптовалюты. Мы будзем працягваць сачыць за тым, наколькі добра гэта працуе на практыцы, але гэта вялікая справа."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Пераход на ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Адзін з нашых <a %(annas_archive)s>квіткоў</a> быў зборнай сумкай праблем з нашай пошукавай сістэмай. Мы выкарыстоўвалі поўнатэкставы пошук MySQL, паколькі ў нас усе дадзеныя былі ў MySQL. Але ў яго былі свае абмежаванні:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Некаторыя запыты займалі вельмі шмат часу, да такой ступені, што яны захоплівалі ўсе адкрытыя злучэнні."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Па змаўчанні MySQL мае мінімальную даўжыню слова, або ваш індэкс можа стаць вельмі вялікім. Людзі паведамлялі, што не могуць шукаць \"Бэн Гур\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Пошук быў толькі адносна хуткім, калі цалкам загружаны ў памяць, што патрабавала ад нас набыць больш дарагую машыну для запуску гэтага, плюс некаторыя каманды для папярэдняй загрузкі індэкса пры запуску."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Мы не змаглі б лёгка пашырыць яго для стварэння новых функцый, такіх як лепшая <a %(wikipedia_cjk_characters)s>токенізацыя для моў без прабелаў</a>, фільтраванне/фасетаванне, сартаванне, прапановы \"вы мелі на ўвазе\", аўтазапаўненне і гэтак далей."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Пасля размовы з шэрагам экспертаў мы спыніліся на ElasticSearch. Гэта не было ідэальным (іх стандартныя прапановы \"вы мелі на ўвазе\" і функцыі аўтазапаўнення не вельмі добрыя), але ў цэлым гэта было значна лепш, чым MySQL для пошуку. Мы ўсё яшчэ не <a %(youtube)s>занадта ўпэўненыя</a> ў выкарыстанні яго для любых крытычна важных дадзеных (хоць яны зрабілі шмат <a %(elastic_co)s>прагрэсу</a>), але ў цэлым мы вельмі задаволеныя пераходам."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "На дадзены момант мы рэалізавалі значна хутчэйшы пошук, лепшую падтрымку моў, лепшае сартаванне па актуальнасці, розныя варыянты сартавання і фільтраванне па мове/тыпу кнігі/тыпу файла. Калі вам цікава, як гэта працуе, <a %(annas_archive_l140)s>паглядзіце</a> <a %(annas_archive_l1115)s>на</a> <a %(annas_archive_l1635)s>гэта</a>. Гэта даволі даступна, хоць магло б быць больш каментароў…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Выпушчана больш за 300 ГБ вокладак кніг"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Нарэшце, мы рады аб'явіць невялікі выпуск. У супрацоўніцтве з людзьмі, якія кіруюць форкам Libgen.rs, мы дзелімся ўсімі іх вокладкамі кніг праз торэнты і IPFS. Гэта размеркаваць нагрузку прагляду вокладак сярод большай колькасці машын і лепш захавае іх. У многіх (але не ва ўсіх) выпадках вокладкі кніг уключаны ў самі файлы, таму гэта свайго роду \"вытворныя дадзеныя\". Але наяўнасць іх у IPFS усё яшчэ вельмі карысна для штодзённай працы як Архіва Анны, так і розных форкаў Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Як звычайна, вы можаце знайсці гэты выпуск у Пірацкай бібліятэчнай люстэрцы (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>). Мы не будзем спасылацца на яго тут, але вы можаце лёгка знайсці яго."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Спадзяемся, мы зможам крыху расслабіць наш тэмп, цяпер, калі ў нас ёсць годная альтэрнатыва Z-Library. Гэтая нагрузка не з'яўляецца асабліва ўстойлівай. Калі вы зацікаўлены ў дапамозе з праграмаваннем, аперацыямі сервера або працай па захаванні, абавязкова звяжыцеся з намі. Яшчэ шмат <a %(annas_archive)s>работы трэба зрабіць</a>. Дзякуй за ваш інтарэс і падтрымку."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Архіў Анны захаваў найбуйнейшую ў свеце ценявую бібліятэку коміксаў (95 ТБ) — вы можаце дапамагчы яе распаўсюджваць"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Найбуйнейшая ў свеце ценявая бібліятэка коміксаў мела адзіны пункт адмовы.. да сённяшняга дня."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Абмеркаваць на Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Найбуйнейшая ценявая бібліятэка коміксаў, верагодна, належыць пэўнаму форку Library Genesis: Libgen.li. Адміністратар, які кіруе гэтым сайтам, здолеў сабраць неверагодную калекцыю коміксаў з больш чым 2 мільёнаў файлаў, агульным аб'ёмам больш за 95 ТБ. Аднак, у адрозненне ад іншых калекцый Library Genesis, гэтая не была даступная ў масавым парадку праз торэнты. Вы маглі атрымаць доступ да гэтых коміксаў толькі індывідуальна праз яго павольны асабісты сервер — адзіны пункт адмовы. Да сённяшняга дня!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "У гэтым допісе мы раскажам вам больш пра гэтую калекцыю і пра наш збор сродкаў для падтрымкі большай колькасці гэтай працы."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Доктар Барбара Гордан спрабуе згубіцца ў звычайным свеце бібліятэкі…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Форкі Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Спачатку трохі фону. Вы можаце ведаць Library Genesis за іх эпічную калекцыю кніг. Менш людзей ведае, што валанцёры Library Genesis стварылі іншыя праекты, такія як значная калекцыя часопісаў і стандартных дакументаў, поўная рэзервовая копія Sci-Hub (у супрацоўніцтве з заснавальніцай Sci-Hub, Аляксандрай Элбакян) і, сапраўды, велізарная калекцыя коміксаў."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "У нейкі момант розныя аператары люстэркаў Library Genesis пайшлі сваімі шляхамі, што прывяло да цяперашняй сітуацыі, калі існуе некалькі розных \"форкаў\", якія ўсё яшчэ носяць назву Library Genesis. Форк Libgen.li унікальна мае гэтую калекцыю коміксаў, а таксама значную калекцыю часопісаў (над якой мы таксама працуем)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Супрацоўніцтва"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Улічваючы яе памер, гэтая калекцыя даўно была ў нашым спісе жаданняў, таму пасля нашага поспеху з рэзервовым капіраваннем Z-Library, мы нацэліліся на гэтую калекцыю. Спачатку мы непасрэдна скрабілі яе, што было даволі складана, паколькі іх сервер быў не ў лепшым стане. Такім чынам мы атрымалі каля 15 ТБ, але гэта ішло павольна."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "На шчасце, нам удалося звязацца з аператарам бібліятэкі, які пагадзіўся адправіць нам усе дадзеныя непасрэдна, што было значна хутчэй. Тым не менш, перадача і апрацоўка ўсіх дадзеных заняла больш за паўгода, і мы амаль страцілі ўсё з-за пашкоджання дыска, што азначала б пачатак зноў."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Гэты вопыт прымусіў нас паверыць, што важна як мага хутчэй распаўсюдзіць гэтыя дадзеныя, каб іх можна было люстраваць шырока і далёка. Мы ўсяго ў адным-двух няўдачных выпадках ад страты гэтай калекцыі назаўсёды!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Калекцыя"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Хуткасць азначае, што калекцыя трохі неарганізаваная… Давайце паглядзім. Уявіце, што ў нас ёсць файл-сістэма (якую на самай справе мы раздзяляем на торэнты):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Першы каталог, <code>/repository</code>, з'яўляецца больш структураванай часткай гэтага. Гэты каталог утрымлівае так званыя \"тысячныя дырэкторыі\": дырэкторыі, кожная з якіх мае тысячу файлаў, якія паступова нумаруюцца ў базе дадзеных. Дырэкторыя <code>0</code> утрымлівае файлы з comic_id 0–999 і гэтак далей."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Гэта тая ж схема, якую Library Genesis выкарыстоўвае для сваіх калекцый мастацкай і навуковай літаратуры. Ідэя заключаецца ў тым, што кожная \"тысячная дырэкторыя\" аўтаматычна ператвараецца ў торэнт, як толькі яна запаўняецца."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Аднак аператар Libgen.li ніколі не ствараў торэнты для гэтай калекцыі, і таму тысячныя дырэкторыі, верагодна, сталі нязручнымі і саступілі месца \"несартыраваным дырэкторыям\". Гэта <code>/comics0</code> праз <code>/comics4</code>. Усе яны ўтрымліваюць унікальныя структуры дырэкторый, якія, верагодна, мелі сэнс для збору файлаў, але цяпер нам не вельмі зразумелыя. На шчасце, metadata ўсё яшчэ непасрэдна спасылаецца на ўсе гэтыя файлы, таму іх арганізацыя на дыску на самай справе не мае значэння!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata даступна ў выглядзе базы дадзеных MySQL. Яе можна загрузіць непасрэдна з сайта Libgen.li, але мы таксама зробім яе даступнай у торэнце разам з нашай уласнай табліцай з усімі MD5 хэшамі."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Аналіз"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Калі вы атрымліваеце 95 ТБ у ваш кластар захоўвання, вы спрабуеце зразумець, што там наогул ёсць… Мы правялі некаторы аналіз, каб паглядзець, ці можам мы крыху зменшыць памер, напрыклад, выдаляючы дублікаты. Вось некаторыя з нашых высноў:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Семантычныя дублікаты (розныя сканы адной і той жа кнігі) тэарэтычна можна адфільтраваць, але гэта складана. Калі мы ўручную праглядалі коміксы, мы знайшлі занадта шмат ілжывых пазітываў."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Ёсць некаторыя дублікаты толькі па MD5, што адносна марнатраўна, але фільтраванне іх дало б нам толькі каля 1% in эканоміі. У такім маштабе гэта ўсё яшчэ каля 1 ТБ, але таксама, у такім маштабе 1 ТБ не мае вялікага значэння. Мы б лепш не рызыкавалі выпадкова знішчыць дадзеныя ў гэтым працэсе."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Мы знайшлі шмат дадзеных, якія не з'яўляюцца кнігамі, такіх як фільмы, заснаваныя на коміксах. Гэта таксама здаецца марнатраўствам, паколькі яны ўжо шырока даступныя іншымі спосабамі. Аднак мы зразумелі, што не можам проста адфільтраваць файлы фільмаў, паколькі ёсць таксама <em>інтэрактыўныя коміксы</em>, якія былі выпушчаны на камп'ютары, якія нехта запісаў і захаваў як фільмы."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "У рэшце рэшт, усё, што мы маглі б выдаліць з калекцыі, зэканоміла б толькі некалькі працэнтаў. Потым мы ўспомнілі, што мы захавальнікі дадзеных, і людзі, якія будуць люстраваць гэта, таксама захавальнікі дадзеных, і таму: «ШТО ВЫ МАЕЦЕ НА ЎВАЗЕ, ВЫДАЛІЦЬ?!» :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Таму мы прадстаўляем вам поўную, нязмененую калекцыю. Гэта шмат дадзеных, але мы спадзяемся, што дастаткова людзей захоча яе распаўсюджваць."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Збор сродкаў"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Мы выпускаем гэтыя дадзеныя ў вялікіх частках. Першы торэнт — гэта <code>/comics0</code>, які мы змясцілі ў адзін вялікі 12TB .tar файл. Гэта лепш для вашага жорсткага дыска і торэнт-праграмнага забеспячэння, чым мноства маленькіх файлаў."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "У рамках гэтага выпуску мы праводзім збор сродкаў. Мы імкнемся сабраць $20,000, каб пакрыць аперацыйныя і кантрактныя выдаткі на гэтую калекцыю, а таксама забяспечыць працяг і будучыя праекты. У нас ёсць некалькі <em>вялікіх</em> праектаў у распрацоўцы."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Каго я падтрымліваю сваім ахвяраваннем?</em> Карацей: мы захоўваем усе веды і культуру чалавецтва і робім іх лёгка даступнымі. Увесь наш код і дадзеныя з'яўляюцца адкрытым зыходным кодам, мы цалкам валанцёрскі праект, і мы захавалі 125TB кніг (у дадатак да існуючых торэнтаў Libgen і Scihub). У рэшце рэшт, мы ствараем махавік, які дазваляе і стымулюе людзей знаходзіць, сканаваць і захоўваць усе кнігі ў свеце. Мы напішам пра наш галоўны план у будучым паведамленні. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Калі вы ахвяруеце на 12-месячнае сяброўства “Amazing Archivist” ($780), вы можаце <strong>“усынавіць торэнт”</strong>, што азначае, што мы размесцім ваша імя карыстальніка або паведамленне ў назве аднаго з торэнтаў!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Вы можаце ахвяраваць, перайшоўшы на <a %(wikipedia_annas_archive)s>Архіў Анны</a> і націснуўшы кнопку «Ахвяраваць». Мы таксама шукаем больш валанцёраў: праграмістаў, даследчыкаў бяспекі, экспертаў па ананімных плацяжах і перакладчыкаў. Вы таксама можаце падтрымаць нас, прадастаўляючы паслугі хостынгу. І, вядома, калі ласка, распаўсюджвайце нашы торэнты!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Дзякуй усім, хто ўжо так шчодра нас падтрымаў! Вы сапраўды робіце розніцу."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Вось торэнты, якія былі выпушчаны да гэтага часу (мы ўсё яшчэ апрацоўваем астатнія):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Усе торэнты можна знайсці на <a %(wikipedia_annas_archive)s>Архіў Анны</a> у раздзеле «Datasets» (мы не спасылаемся на іх наўпрост, каб спасылкі на гэты блог не выдаляліся з Reddit, Twitter і г.д.). Адтуль перайдзіце па спасылцы на сайт Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Што далей?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Шматлікія торэнты выдатныя для доўгатэрміновага захавання, але не так добрыя для штодзённага доступу. Мы будзем працаваць з партнёрамі па хостынгу, каб загрузіць усе гэтыя дадзеныя ў Інтэрнэт (паколькі Архіў Анны нічога не хостыць наўпрост). Вядома, вы зможаце знайсці гэтыя спасылкі для загрузкі ў Архіве Анны."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Мы таксама запрашаем усіх працаваць з гэтымі дадзенымі! Дапамажыце нам лепш іх аналізаваць, выдаляць дублікаты, размяшчаць на IPFS, змешваць, трэніраваць вашыя AI мадэлі з імі і гэтак далей. Гэта ўсё ваша, і мы не можам дачакацца, каб убачыць, што вы з гэтым зробіце."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Нарэшце, як ужо казалі раней, у нас яшчэ ёсць некалькі вялікіх выпускаў (калі <em>хтосьці</em> мог бы <em>выпадкова</em> даслаць нам дамп <em>пэўнай</em> базы дадзеных ACS4, вы ведаеце, дзе нас знайсці...), а таксама стварэнне махавіка для захавання ўсіх кніг у свеце."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Так што заставайцеся з намі, мы толькі пачынаем."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x новыя кнігі дададзены ў Люстра Пірацкай Бібліятэкі (+24TB, 3,8 мільёна кніг)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "У арыгінальным выпуску Люстра Пірацкай Бібліятэкі (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>), мы зрабілі люстра Z-Library, вялікай незаконнай калекцыі кніг. Як напамін, вось што мы напісалі ў тым арыгінальным блогу:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library — гэта папулярная (і незаконная) бібліятэка. Яны ўзялі калекцыю Library Genesis і зрабілі яе лёгка даступнай для пошуку. Акрамя таго, яны сталі вельмі эфектыўнымі ў прыцягненні новых кніг, стымулюючы карыстальнікаў рознымі перавагамі. У цяперашні час яны не вяртаюць гэтыя новыя кнігі ў Library Genesis. І ў адрозненне ад Library Genesis, яны не робяць сваю калекцыю лёгка люстранай, што перашкаджае шырокаму захаванню. Гэта важна для іх бізнес-мадэлі, паколькі яны бяруць грошы за доступ да іх калекцыі ў вялікіх аб'ёмах (больш за 10 кніг у дзень)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Мы не выносім маральных ацэнак наконт спагнання грошай за масавы доступ да незаконнай калекцыі кніг. Несумненна, што Z-Library паспяхова пашырыла доступ да ведаў і атрымала больш кніг. Мы проста тут, каб зрабіць сваю частку: забяспечыць доўгатэрміновае захаванне гэтай прыватнай калекцыі."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Гэтая калекцыя датуецца сярэдзінай 2021 года. Тым часам Z-Library расце з уражлівай хуткасцю: яны дадалі каля 3,8 мільёна новых кніг. Там ёсць некаторыя дублікаты, але большасць з іх, здаецца, сапраўды новыя кнігі або больш якасныя сканы раней прадстаўленых кніг. Гэта ў значнай ступені дзякуючы павелічэнню колькасці валанцёраў-мадэратараў у Z-Library і іх сістэме масавай загрузкі з дэдуплікацыяй. Мы хацелі б павіншаваць іх з гэтымі дасягненнямі."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Мы рады паведаміць, што атрымалі ўсе кнігі, якія былі дададзены ў Z-Library паміж нашым апошнім люстэркам і жніўнем 2022 года. Мы таксама вярнуліся і сабралі некаторыя кнігі, якія прапусцілі ў першы раз. У цэлым, гэтая новая калекцыя складае каля 24 ТБ, што значна больш, чым папярэдняя (7 ТБ). Наше люстэрка цяпер складае 31 ТБ у агульнай складанасці. Зноў жа, мы правялі дэдуплікацыю з Library Genesis, паколькі ўжо ёсць торэнты для гэтай калекцыі."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Калі ласка, наведайце Pirate Library Mirror, каб азнаёміцца з новай калекцыяй (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>). Там ёсць больш інфармацыі пра тое, як структураваны файлы і што змянілася з мінулага разу. Мы не будзем спасылацца на гэта адсюль, паколькі гэта проста блог-сайт, які не размяшчае ніякіх незаконных матэрыялаў."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Вядома, сідынг таксама з'яўляецца выдатным спосабам дапамагчы нам. Дзякуй усім, хто сідзіруе наш папярэдні набор торэнтаў. Мы ўдзячныя за пазітыўны водгук і рады, што так шмат людзей клапоцяцца пра захаванне ведаў і культуры такім незвычайным спосабам."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Як стаць піратам-архівістам"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Першы выклік можа быць нечаканым. Гэта не тэхнічная праблема або юрыдычная праблема. Гэта псіхалагічная праблема."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Перш чым мы паглыбімся, два абнаўленні пра Pirate Library Mirror (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Мы атрымалі вельмі шчодрыя ахвяраванні. Першае было $10 тыс. ад ананімнай асобы, якая таксама падтрымлівала \"bookwarrior\", арыгінальнага заснавальніка Library Genesis. Асаблівая падзяка bookwarrior за садзейнічанне гэтаму ахвяраванню. Другое было яшчэ $10 тыс. ад ананімнага донара, які звязаўся з намі пасля нашага апошняга выпуску і быў натхнёны дапамагчы. Мы таксама атрымалі шэраг меншых ахвяраванняў. Вялікі дзякуй за ўсю вашу шчодрую падтрымку. У нас ёсць некалькі захапляльных новых праектаў у распрацоўцы, якія гэта падтрымае, таму заставайцеся з намі."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "У нас былі некаторыя тэхнічныя цяжкасці з памерам нашага другога выпуску, але нашы торэнты цяпер у сетцы і сідзіруюцца. Мы таксама атрымалі шчодрую прапанову ад ананімнай асобы сідзіраваць нашу калекцыю на іх вельмі хуткасных серверах, таму мы робім спецыяльную загрузку на іх машыны, пасля чаго ўсе астатнія, хто загружае калекцыю, павінны ўбачыць значнае паляпшэнне хуткасці."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Цэлыя кнігі можна напісаць пра <em>чаму</em> лічбавага захавання ў цэлым і піратскага архівізму ў прыватнасці, але дазвольце нам даць кароткі ўвод для тых, хто не вельмі знаёмы. Свет стварае больш ведаў і культуры, чым калі-небудзь раней, але таксама больш з гэтага губляецца, чым калі-небудзь раней. Чалавецтва ў асноўным давярае карпарацыям, такім як акадэмічныя выдавецтвы, струменевыя сэрвісы і сацыяльныя медыя, гэта спадчына, і яны часта не аказваюцца выдатнымі захавальнікамі. Паглядзіце дакументальны фільм Digital Amnesia або любы выступ Джэйсана Скота."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ёсць некаторыя ўстановы, якія добра архівуюць столькі, колькі могуць, але яны звязаны законам. Як піраты, мы знаходзімся ў унікальнай пазіцыі архіваваць калекцыі, якія яны не могуць крануць з-за выканання аўтарскіх правоў або іншых абмежаванняў. Мы таксама можам люстраваць калекцыі шмат разоў па ўсім свеце, тым самым павялічваючы шанцы на належнае захаванне."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Пакуль што мы не будзем унікаць у дыскусіі пра плюсы і мінусы інтэлектуальнай уласнасці, маральнасць парушэння закона, разважанні пра цэнзуру або пытанне доступу да ведаў і культуры. З усім гэтым на баку, давайце паглыбімся ў <em>як</em>. Мы падзелімся, як наша каманда стала піратамі-архівістамі, і ўрокамі, якія мы вывучылі на гэтым шляху. Ёсць шмат выклікаў, калі вы пачынаеце гэтае падарожжа, і спадзяемся, што мы зможам дапамагчы вам праз некаторыя з іх."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Супольнасць"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Першы выклік можа быць нечаканым. Гэта не тэхнічная праблема або юрыдычная праблема. Гэта псіхалагічная праблема: праца ў цені можа быць неверагодна адзінотай. У залежнасці ад таго, што вы плануеце рабіць, і вашай мадэлі пагрозы, вам, магчыма, прыйдзецца быць вельмі асцярожнымі. На адным канцы спектра ў нас ёсць людзі, як Аляксандра Элбакян*, заснавальніца Sci-Hub, якая вельмі адкрыта пра сваю дзейнасць. Але яна знаходзіцца пад высокай рызыкай быць арыштаванай, калі наведае заходнюю краіну ў гэты момант, і можа сутыкнуцца з дзесяцігоддзямі турэмнага зняволення. Ці гатовыя вы рызыкнуць гэтым? Мы знаходзімся на іншым канцы спектра; вельмі асцярожныя, каб не пакінуць ніякіх слядоў, і маем моцную аперацыйную бяспеку."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Як згадана на HN \"ynno\", Аляксандра спачатку не хацела быць вядомай: \"Яе серверы былі настроены на выдачу падрабязных паведамленняў пра памылкі з PHP, уключаючы поўны шлях да файла з памылкай, які знаходзіўся ў каталогу /home/<USER>" Таму выкарыстоўвайце выпадковыя імёны карыстальнікаў на камп'ютарах, якія вы выкарыстоўваеце для гэтага, на выпадак, калі вы нешта няправільна наладзіце."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Аднак гэтая сакрэтнасць мае псіхалагічную цану. Большасць людзей любіць, калі іх прызнаюць за працу, якую яны робяць, і ўсё ж вы не можаце атрымаць ніякага прызнання за гэта ў рэальным жыцці. Нават простыя рэчы могуць быць складанымі, як, напрыклад, сябры, якія пытаюцца, чым вы займаліся (у нейкі момант \"возня з маім NAS / homelab\" надакучае)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Вось чаму так важна знайсці нейкую супольнасць. Вы можаце адмовіцца ад часткі аперацыйнай бяспекі, даверыўшыся некаторым вельмі блізкім сябрам, у якіх вы ведаеце, што можаце глыбока давяраць. Нават тады будзьце асцярожныя, каб не пакідаць нічога ў пісьмовай форме, на выпадак, калі ім прыйдзецца перадаць свае электронныя лісты ўладам, або калі іх прылады будуць скампраметаваны іншым чынам."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Яшчэ лепш знайсці некаторых аднадумцаў-піратов. Калі вашы блізкія сябры зацікаўлены далучыцца да вас, выдатна! У адваротным выпадку, вы можаце знайсці іншых у Інтэрнэце. На жаль, гэта ўсё яшчэ нішавое супольнасць. Пакуль што мы знайшлі толькі некалькі іншых, хто актыўны ў гэтай галіне. Добрыя пачатковыя месцы, здаецца, гэта форумы Library Genesis і r/DataHoarder. Каманда архіва таксама мае аднадумцаў, хоць яны дзейнічаюць у рамках закона (нават калі ў некаторых шэрых зонах закона). Традыцыйныя сцэны \"warez\" і пірацтва таксама маюць людзей, якія думаюць падобным чынам."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Мы адкрыты для ідэй, як развіваць супольнасць і даследаваць ідэі. Не саромейцеся пісаць нам у Twitter або Reddit. Магчыма, мы маглі б арганізаваць нейкі форум або групу чата. Адна з праблем заключаецца ў тым, што гэта можа лёгка падвергнуцца цэнзуры пры выкарыстанні звычайных платформаў, таму нам давядзецца размясціць гэта самастойна. Ёсць таксама кампраміс паміж тым, каб гэтыя абмеркаванні былі цалкам публічнымі (больш патэнцыйнага ўдзелу) і тым, каб зрабіць іх прыватнымі (не дазваляючы патэнцыйным \"мэтам\" ведаць, што мы збіраемся іх скрабаваць). Нам трэба будзе падумаць пра гэта. Дайце нам ведаць, калі вас гэта цікавіць!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Праекты"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Калі мы робім праект, ён мае некалькі этапаў:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Выбар дамена / філасофія: На чым вы прыблізна хочаце засяродзіцца і чаму? Якія вашы унікальныя захапленні, навыкі і абставіны, якія вы можаце выкарыстоўваць на сваю карысць?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Выбар мэты: Якую канкрэтную калекцыю вы будзеце люстраваць?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Скрабінг metadata: Каталагізацыя інфармацыі пра файлы без фактычнага спампоўвання саміх (часта значна большых) файлаў."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Выбар даных: На аснове metadata звужэнне таго, якія дадзеныя найбольш актуальныя для архівацыі зараз. Гэта можа быць усё, але часта ёсць разумны спосаб зэканоміць месца і прапускную здольнасць."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Скрабінг даных: Фактычнае атрыманне даных."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Распаўсюджванне: Упакоўка ў торэнты, аб'ява пра гэта дзесьці, прыцягненне людзей да распаўсюджвання."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Гэта не зусім незалежныя этапы, і часта разуменне з пазнейшага этапу вяртае вас да ранейшага этапу. Напрыклад, падчас скрабінгу metadata вы можаце зразумець, што мэта, якую вы выбралі, мае абарончыя механізмы, якія перавышаюць ваш узровень навыкаў (напрыклад, IP-блокі), таму вы вяртаецеся і знаходзіце іншую мэту."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Выбар дамена / філасофія"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Няма недахопу ў ведах і культурнай спадчыне, якую трэба захаваць, што можа быць пераважным. Вось чаму часта карысна зрабіць паўзу і падумаць, які ўклад вы можаце зрабіць."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Кожны мае розны спосаб думаць пра гэта, але вось некаторыя пытанні, якія вы маглі б сабе задаць:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Чаму вас гэта цікавіць? Чым вы захапляецеся? Калі мы зможам сабраць групу людзей, якія ўсе архівуюць тое, што ім асабліва цікава, гэта пакрые шмат! Вы будзеце ведаць значна больш, чым сярэдні чалавек, пра сваё захапленне, напрыклад, якія важныя дадзеныя трэба захаваць, якія лепшыя калекцыі і інтэрнэт-супольнасці і гэтак далей."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Якія навыкі ў вас ёсць, якія вы можаце выкарыстоўваць на сваю карысць? Напрыклад, калі вы эксперт па бяспецы ў Інтэрнэце, вы можаце знайсці спосабы пераадолення IP-блокаў для бяспечных мэтаў. Калі вы выдатна арганізуеце супольнасці, то, магчыма, вы зможаце сабраць некаторых людзей вакол мэты. Аднак карысна ведаць некаторыя праграмаванні, хаця б для падтрымання добрай аперацыйнай бяспекі на працягу ўсяго гэтага працэсу."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Колькі часу ў вас ёсць на гэта? Наша парада - пачынаць з малога і рабіць большыя праекты, калі вы асвоіцеся, але гэта можа стаць усёпаглынальным."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "На якой вобласці з высокім уздзеяннем варта засяродзіцца? Калі вы збіраецеся выдаткаваць X гадзін на пірацкую архівацыю, то як вы можаце атрымаць максімальную \"аддачу ад укладзеных сродкаў\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Якія ўнікальныя спосабы, якія вы думаеце пра гэта? У вас могуць быць цікавыя ідэі або падыходы, якія іншыя маглі прапусціць."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "У нашым выпадку нас асабліва цікавіла доўгатэрміновае захаванне навукі. Мы ведалі пра Library Genesis і тое, як ён быў цалкам люстраваны шмат разоў з дапамогай торэнтаў. Нам спадабалася гэтая ідэя. Потым аднойчы адзін з нас паспрабаваў знайсці навуковыя падручнікі на Library Genesis, але не змог іх знайсці, што паставіла пад сумнеў, наколькі ён сапраўды поўны. Мы потым шукалі гэтыя падручнікі ў Інтэрнэце і знайшлі іх у іншых месцах, што пасадзіла зерне для нашага праекта. Яшчэ да таго, як мы даведаліся пра Z-Library, у нас была ідэя не спрабаваць збіраць усе гэтыя кнігі ўручную, а засяродзіцца на люстраванні існуючых калекцый і ўкладзе іх назад у Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Выбар мэты"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Такім чынам, у нас ёсць вобласць, якую мы разглядаем, цяпер якую канкрэтную калекцыю мы будзем люстраваць? Ёсць некалькі рэчаў, якія робяць мэту добрай:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Вялікая"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Унікальная: не ўжо добра ахопленая іншымі праектамі."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Даступная: не выкарыстоўвае шмат слаёў абароны, каб перашкодзіць вам скрабаваць іх metadata і дадзеныя."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Асаблівы ўспрыманне: у вас ёсць нейкая асаблівая інфармацыя пра гэтую мэту, напрыклад, вы неяк маеце асаблівы доступ да гэтай калекцыі, або вы зразумелі, як перамагчы іх абарону. Гэта не абавязкова (наш будучы праект не робіць нічога асаблівага), але гэта, безумоўна, дапамагае!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Калі мы знайшлі нашы навуковыя падручнікі на сайтах, акрамя Library Genesis, мы спрабавалі зразумець, як яны трапілі ў інтэрнэт. Мы тады знайшлі Z-Library і зразумелі, што хоць большасць кніг не з'яўляюцца там упершыню, яны ў рэшце рэшт там апыняюцца. Мы даведаліся пра яго сувязь з Library Genesis і (фінансавую) стымульную структуру і перавагу карыстальніцкага інтэрфейсу, якія зрабілі яго значна больш поўнай калекцыяй. Мы тады зрабілі некаторыя папярэднія скрабаванні metadata і дадзеных і зразумелі, што мы можам абыйсці іх абмежаванні на загрузку IP, выкарыстоўваючы асаблівы доступ аднаго з нашых удзельнікаў да шматлікіх праксі-сервераў."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Калі вы даследуеце розныя мэты, ужо важна схаваць свае сляды, выкарыстоўваючы VPN і адкідныя адрасы электроннай пошты, пра якія мы пагаворым пазней."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Скрабаванне metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Давайце станем крыху больш тэхнічнымі. Для фактычнага скрабавання metadata з сайтаў мы трымалі рэчы даволі простымі. Мы выкарыстоўваем скрыпты Python, часам curl, і базу дадзеных MySQL для захоўвання вынікаў. Мы не выкарыстоўвалі ніякага складанага праграмнага забеспячэння для скрабавання, якое можа картаграфаваць складаныя сайты, паколькі пакуль нам трэба было скрабаваць толькі адзін або два тыпы старонак, проста пералічваючы ідэнтыфікатары і парсінг HTML. Калі няма лёгка пералічаных старонак, тады вам можа спатрэбіцца належны краулер, які спрабуе знайсці ўсе старонкі."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Перш чым пачаць скрабаваць увесь сайт, паспрабуйце зрабіць гэта ўручную на працягу некаторага часу. Прайдзіце некалькі дзесяткаў старонак самастойна, каб зразумець, як гэта працуе. Часам вы ўжо сутыкнецеся з IP-блокамі або іншымі цікавымі паводзінамі такім чынам. Тое ж самае адносіцца і да скрабавання дадзеных: перш чым занадта глыбока пагрузіцца ў гэтую мэту, пераканайцеся, што вы сапраўды можаце эфектыўна загружаць яе дадзеныя."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Каб абыйсці абмежаванні, ёсць некалькі рэчаў, якія вы можаце паспрабаваць. Ці ёсць іншыя IP-адрасы або серверы, якія размяшчаюць тыя ж дадзеныя, але не маюць тых жа абмежаванняў? Ці ёсць якія-небудзь API-канчатковыя кропкі, якія не маюць абмежаванняў, у той час як іншыя маюць? З якой хуткасцю загрузкі ваш IP блакуецца і на як доўга? Ці вас не блакуюць, але зніжаюць хуткасць? Што, калі вы ствараеце ўліковы запіс карыстальніка, як тады змяняюцца рэчы? Ці можаце вы выкарыстоўваць HTTP/2, каб трымаць злучэнні адкрытымі, і ці павялічвае гэта хуткасць, з якой вы можаце запытваць старонкі? Ці ёсць старонкі, якія пералічваюць некалькі файлаў адначасова, і ці дастаткова інфармацыі, пералічанай там?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Рэчы, якія вы, верагодна, захочаце захаваць, уключаюць:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Назва"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Імя файла / размяшчэнне"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: можа быць нейкі ўнутраны ID, але ID, такія як ISBN або DOI, таксама карысныя."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Памер: для разліку, колькі месца на дыску вам трэба."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Хэш (md5, sha1): каб пацвердзіць, што вы правільна загрузілі файл."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Дата дадавання/змены: каб вы маглі вярнуцца пазней і загрузіць файлы, якія вы не загружалі раней (хоць вы часта таксама можаце выкарыстоўваць ID або хэш для гэтага)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Апісанне, катэгорыя, тэгі, аўтары, мова і г.д."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Звычайна мы робім гэта ў два этапы. Спачатку мы загружаем сырыя HTML-файлы, звычайна непасрэдна ў MySQL (каб пазбегнуць шматлікіх маленькіх файлаў, пра якія мы пагаворым ніжэй). Затым, на асобным этапе, мы праходзім праз гэтыя HTML-файлы і парсім іх у фактычныя табліцы MySQL. Такім чынам, вам не трэба перазагружаць усё з нуля, калі вы выявіце памылку ў вашым кодзе парсінгу, паколькі вы можаце проста перапрацаваць HTML-файлы з новым кодам. Гэта таксама часта прасцей паралелізаваць этап апрацоўкі, такім чынам эканомячы час (і вы можаце пісаць код апрацоўкі, пакуль скрабаванне працуе, замест таго, каб пісаць абодва этапы адначасова)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Нарэшце, звярніце ўвагу, што для некаторых мэтаў збор metadata — гэта ўсё, што ёсць. Ёсць некаторыя вялікія калекцыі metadata, якія не захоўваюцца належным чынам."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Выбар дадзеных"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Часта вы можаце выкарыстоўваць metadata, каб вызначыць разумны паднабор дадзеных для загрузкі. Нават калі вы ў рэшце рэшт хочаце загрузіць усе дадзеныя, можа быць карысна спачатку прыярытэзаваць найбольш важныя элементы, на выпадак, калі вас выявяць і абарона будзе ўзмоцнена, або таму, што вам трэба будзе набыць больш дыскаў, або проста таму, што нешта іншае з'явіцца ў вашым жыцці, перш чым вы зможаце загрузіць усё."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Напрыклад, калекцыя можа мець некалькі выданняў аднаго і таго ж рэсурсу (напрыклад, кнігі або фільма), дзе адно пазначана як найлепшая якасць. Захаванне гэтых выданняў у першую чаргу было б вельмі разумным. Вы можаце ў рэшце рэшт захацець захаваць усе выданні, паколькі ў некаторых выпадках metadata можа быць пазначана няправільна, або могуць быць невядомыя кампрамісы паміж выданнямі (напрыклад, \"лепшае выданне\" можа быць лепшым у большасці аспектаў, але горшым у іншых, напрыклад, фільм з больш высокім дазволам, але без субтытраў)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Вы таксама можаце шукаць у сваёй базе дадзеных metadata, каб знайсці цікавыя рэчы. Які самы вялікі файл, які размешчаны, і чаму ён такі вялікі? Які самы маленькі файл? Ці ёсць цікавыя або нечаканыя ўзоры, калі гаворка ідзе пра пэўныя катэгорыі, мовы і гэтак далей? Ці ёсць дублікатныя або вельмі падобныя назвы? Ці ёсць узоры, калі дадзеныя былі дададзены, напрыклад, адзін дзень, калі шмат файлаў было дададзена адначасова? Вы часта можаце шмат даведацца, гледзячы на набор дадзеных з розных бакоў."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "У нашым выпадку мы выдалілі дублікаты кніг Z-Library з дапамогай хэшаў md5 у Library Genesis, тым самым зэканоміўшы шмат часу на загрузку і месца на дыску. Аднак гэта даволі унікальная сітуацыя. У большасці выпадкаў няма комплексных баз дадзеных, якія файлы ўжо належным чынам захаваны іншымі піратамі. Гэта само па сабе вялікая магчымасць для кагосьці. Было б выдатна мець рэгулярна абнаўляны агляд такіх рэчаў, як музыка і фільмы, якія ўжо шырока распаўсюджаны на торэнт-сайтах, і таму маюць ніжэйшы прыярытэт для ўключэння ў пірацкія люстэркі."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Збор дадзеных"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Цяпер вы гатовыя фактычна загрузіць дадзеныя ў вялікім аб'ёме. Як ужо згадвалася раней, на гэтым этапе вы ўжо павінны былі ўручную загрузіць некалькі файлаў, каб лепш зразумець паводзіны і абмежаванні мэты. Аднак вас усё яшчэ чакаюць сюрпрызы, калі вы фактычна пачнеце загружаць шмат файлаў адначасова."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Наша парада тут — трымаць усё проста. Пачніце з таго, што проста загрузіце некалькі файлаў. Вы можаце выкарыстоўваць Python, а затым пашырыць да некалькіх патокаў. Але часам нават прасцей — гэта генераваць файлы Bash непасрэдна з базы дадзеных, а затым запускаць некалькі з іх у некалькіх аконцах тэрмінала для маштабавання. Хуткі тэхнічны трук, варты згадкі тут, — гэта выкарыстанне OUTFILE у MySQL, які вы можаце пісаць дзе заўгодна, калі адключыце \"secure_file_priv\" у mysqld.cnf (і абавязкова адключыце/перазапішыце AppArmor, калі вы на Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Мы захоўваем дадзеныя на простых жорсткіх дысках. Пачніце з таго, што ў вас ёсць, і павольна пашырайцеся. Можа быць складана думаць пра захоўванне сотняў ТБ дадзеных. Калі гэта сітуацыя, з якой вы сутыкаецеся, проста выстаўце добры паднабор спачатку, і ў сваім аб'яве папрасіце дапамогі ў захоўванні астатняга. Калі вы хочаце набыць больш жорсткіх дыскаў самастойна, то r/DataHoarder мае добрыя рэсурсы па атрыманні добрых прапаноў."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Старайцеся не турбавацца занадта шмат пра складаныя файлавыя сістэмы. Лёгка трапіць у трусіную нару наладжвання такіх рэчаў, як ZFS. Адна тэхнічная дэталь, пра якую варта ведаць, — гэта тое, што многія файлавыя сістэмы не спраўляюцца з вялікай колькасцю файлаў. Мы выявілі, што просты абыходны шлях — гэта стварэнне некалькіх каталогаў, напрыклад, для розных дыяпазонаў ID або прэфіксаў хэшаў."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Пасля загрузкі дадзеных абавязкова праверце цэласнасць файлаў з дапамогай хэшаў у metadata, калі яны даступныя."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Распаўсюджванне"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "У вас ёсць дадзеныя, такім чынам, вы валодаеце першым у свеце пірацкім люстэркам вашай мэты (хутчэй за ўсё). У многіх адносінах самая цяжкая частка ўжо ззаду, але самая рызыкоўная частка ўсё яшчэ наперадзе. У рэшце рэшт, да гэтага часу вы былі незаўважнымі; ляталі пад радарам. Усё, што вам трэба было зрабіць, гэта выкарыстоўваць добры VPN на працягу ўсяго часу, не запаўняць свае асабістыя дадзеныя ў любых формах (вядома), і, магчыма, выкарыстоўваць спецыяльную сесію браўзера (ці нават іншы камп'ютар)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Цяпер вам трэба распаўсюджваць дадзеныя. У нашым выпадку мы спачатку хацелі вярнуць кнігі ў Library Genesis, але хутка выявілі цяжкасці ў гэтым (сартаванне мастацкай і навуковай літаратуры). Таму мы вырашылі распаўсюджваць з дапамогай торэнтаў у стылі Library Genesis. Калі ў вас ёсць магчымасць унесці свой уклад у існуючы праект, гэта можа зэканоміць вам шмат часу. Аднак у цяперашні час існуе не так шмат добра арганізаваных пірацкіх люстэркаў."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Такім чынам, скажам, вы вырашылі распаўсюджваць торэнты самастойна. Старайцеся трымаць гэтыя файлы невялікімі, каб іх было лёгка люстраваць на іншых сайтах. Вам давядзецца самастойна раздаваць торэнты, пры гэтым застаючыся ананімным. Вы можаце выкарыстоўваць VPN (з пераадрасацыяй порта або без яе), або аплаціць Seedbox з дапамогай перамешаных Bitcoins. Калі вы не ведаеце, што азначаюць некаторыя з гэтых тэрмінаў, вам давядзецца шмат чытаць, паколькі важна, каб вы разумелі рызыкі."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Вы можаце размясціць самі торэнт-файлы на існуючых торэнт-сайтах. У нашым выпадку мы вырашылі фактычна размясціць сайт, паколькі мы таксама хацелі распаўсюджваць нашу філасофію ясным чынам. Вы можаце зрабіць гэта самастойна аналагічным чынам (мы выкарыстоўваем Njalla для нашых даменаў і хостынгу, аплачаных перамешанымі Bitcoins), але таксама не саромейцеся звязацца з намі, каб мы размясцілі вашы торэнты. Мы імкнемся стварыць комплексны індэкс пірацкіх люстэркаў з цягам часу, калі гэтая ідэя атрымае распаўсюджванне."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Што тычыцца выбару VPN, пра гэта ўжо шмат напісана, таму мы проста паўторым агульную параду выбіраць па рэпутацыі. Фактычныя судовыя праверкі палітыкі без лагавання з доўгімі запісамі абароны прыватнасці — гэта самы нізкі рызыкавы варыянт, на нашу думку. Звярніце ўвагу, што нават калі вы робіце ўсё правільна, вы ніколі не зможаце дасягнуць нулявога рызыкі. Напрыклад, пры раздачы вашых торэнтаў высокаматываваны дзяржаўны актор можа, верагодна, паглядзець на ўваходныя і выходныя патокі дадзеных для сервераў VPN і вызначыць, хто вы. Або вы можаце проста неяк памыліцца. Мы, верагодна, ужо зрабілі гэта, і зробім зноў. На шчасце, дзяржавы не так моцна клапоцяцца пра пірацтва."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Адно рашэнне, якое трэба прыняць для кожнага праекта, — гэта ці публікаваць яго пад тым жа імем, што і раней, ці не. Калі вы працягваеце выкарыстоўваць тое ж імя, то памылкі ў аператыўнай бяспецы з ранейшых праектаў могуць вярнуцца, каб вас укусіць. Але публікацыя пад рознымі імёнамі азначае, што вы не будуеце доўгатэрміновую рэпутацыю. Мы вырашылі мець моцную аператыўную бяспеку з самага пачатку, каб мы маглі працягваць выкарыстоўваць тое ж імя, але мы не будзем вагацца публікаваць пад іншым імем, калі мы памылімся або калі абставіны гэтага патрабуюць."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Паведаміць пра гэта можа быць складана. Як мы сказалі, гэта ўсё яшчэ нішавы супольнасць. Мы першапачаткова размясцілі на Reddit, але сапраўды атрымалі ўвагу на Hacker News. Пакуль наша рэкамендацыя — размясціць гэта ў некалькіх месцах і паглядзець, што адбудзецца. І зноў жа, звяжыцеся з намі. Мы хацелі б распаўсюджваць інфармацыю пра больш пірацкія архіўныя намаганні."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Заключэнне"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Спадзяемся, гэта будзе карысна для пачынаючых пірацкіх архівістаў. Мы рады вітаць вас у гэтым свеце, таму не саромейцеся звяртацца. Давайце захаваем як мага больш ведаў і культуры свету і распаўсюдзім іх паўсюль."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Прадстаўляем Пірацкае Люстэрка Бібліятэкі: Захаванне 7 ТБ кніг (якія не знаходзяцца ў Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Гэты праект (РЭДАКЦЫЯ: перанесены ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>) накіраваны на ўклад у захаванне і вызваленне чалавечых ведаў. Мы робім наш невялікі і сціплы ўклад, ідучы па слядах вялікіх, якія былі да нас."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Фокус гэтага праекта ілюструецца яго назвай:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Пірат</strong> - Мы наўмысна парушаем закон аб аўтарскім праве ў большасці краін. Гэта дазваляе нам рабіць тое, што юрыдычныя асобы не могуць: забяспечваць, каб кнігі былі распаўсюджаны паўсюль."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Бібліятэка</strong> - Як і большасць бібліятэк, мы ў асноўным засяроджваемся на пісьмовых матэрыялах, такіх як кнігі. Магчыма, у будучыні мы пашырымся на іншыя тыпы медыя."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Люстэрка</strong> - Мы строга з'яўляемся люстэркам існуючых бібліятэк. Мы засяроджваемся на захаванні, а не на тым, каб зрабіць кнігі лёгка даступнымі для пошуку і загрузкі (доступ) або ствараць вялікую супольнасць людзей, якія ўносяць новыя кнігі (крыніцы)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Першая бібліятэка, якую мы адлюстравалі, - гэта Z-Library. Гэта папулярная (і незаконная) бібліятэка. Яны ўзялі калекцыю Library Genesis і зрабілі яе лёгка даступнай для пошуку. Акрамя таго, яны сталі вельмі эфектыўнымі ў прыцягненні новых кніг, стымулюючы карыстальнікаў, якія ўносяць уклад, рознымі перавагамі. У цяперашні час яны не ўносяць гэтыя новыя кнігі назад у Library Genesis. І ў адрозненне ад Library Genesis, яны не робяць сваю калекцыю лёгка адлюстравальнай, што перашкаджае шырокаму захаванню. Гэта важна для іх бізнес-мадэлі, паколькі яны бяруць грошы за доступ да сваёй калекцыі ў вялікіх аб'ёмах (больш за 10 кніг у дзень)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Мы не выносім маральных ацэнак наконт спагнання грошай за масавы доступ да незаконнай калекцыі кніг. Несумненна, што Z-Library паспяхова пашырыла доступ да ведаў і атрымала больш кніг. Мы проста тут, каб зрабіць сваю частку: забяспечыць доўгатэрміновае захаванне гэтай прыватнай калекцыі."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Мы хацелі б запрасіць вас дапамагчы захаваць і вызваліць чалавечыя веды, загружаючы і распаўсюджваючы нашы торэнты. Глядзіце старонку праекта для атрымання дадатковай інфармацыі аб тым, як арганізаваны дадзеныя."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Мы таксама вельмі хацелі б запрасіць вас унесці свае ідэі аб тым, якія калекцыі адлюстраваць наступнымі і як гэта зрабіць. Разам мы можам дасягнуць шмат. Гэта толькі невялікі ўклад сярод незлічоных іншых. Дзякуй вам за ўсё, што вы робіце."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Мы не спасылаемся на файлы з гэтага блога. Калі ласка, знайдзіце іх самастойна.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Выгрузка ISBNdb, або Колькі Кніг Захавана Навечна?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Калі б мы правільна выдалілі дублікаты файлаў з ценявых бібліятэк, які працэнт усіх кніг у свеце мы захавалі?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "З Пірацкім Люстэркам Бібліятэкі (РЭДАКЦЫЯ: перанесены ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>), наша мэта - узяць усе кнігі ў свеце і захаваць іх навечна.<sup>1</sup> Паміж нашымі торэнтамі Z-Library і арыгінальнымі торэнтамі Library Genesis у нас ёсць 11,783,153 файлаў. Але колькі гэта на самай справе? Калі б мы правільна выдалілі дублікаты гэтых файлаў, які працэнт усіх кніг у свеце мы захавалі? Мы сапраўды хацелі б мець нешта накшталт гэтага:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% oф пісьмовай спадчыны чалавецтва захавана навечна"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Для працэнта нам патрэбен назоўнік: агульная колькасць кніг, калі-небудзь апублікаваных.<sup>2</sup> Да заняпаду Google Books, інжынер праекта, Леанід Тайчэр, <a %(booksearch_blogspot)s>спрабаваў ацаніць</a> гэтую колькасць. Ён прыйшоў — жартам — да 129,864,880 («прынамсі да нядзелі»). Ён ацаніў гэтую колькасць, стварыўшы аб'яднаную базу дадзеных усіх кніг у свеце. Для гэтага ён сабраў розныя наборы дадзеных і затым аб'яднаў іх рознымі спосабамі."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Як невялікае адступленне, існуе яшчэ адна асоба, якая спрабавала каталогізаваць усе кнігі ў свеце: Аарон Шварц, нябожчык лічбавы актывіст і сузаснавальнік Reddit.<sup>3</sup> Ён <a %(youtube)s>заснаваў Open Library</a> з мэтай «адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі», аб'ядноўваючы дадзеныя з розных крыніц. Ён заплаціў найвышэйшую цану за сваю працу па лічбавым захаванні, калі яго абвінавацілі ў масавым спампоўванні навуковых артыкулаў, што прывяло да яго самагубства. Не трэба казаць, што гэта адна з прычын, чаму наша група з'яўляецца псеўданімнай і чаму мы вельмі асцярожныя. Open Library па-геройску кіруецца супрацоўнікамі Internet Archive, працягваючы спадчыну Аарона. Мы вернемся да гэтага пазней у гэтым паведамленні."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "У блогу Google, Тэйчэр апісвае некаторыя праблемы з ацэнкай гэтай колькасці. Па-першае, што такое кніга? Ёсць некалькі магчымых вызначэнняў:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Фізічныя копіі.</strong> Відавочна, што гэта не вельмі карысна, бо яны проста дублікатары аднаго і таго ж матэрыялу. Было б крута, калі б мы маглі захаваць усе анатацыі, якія людзі робяць у кнігах, як знакамітыя «запісы на палях» Ферма. Але, на жаль, гэта застанецца марай архівіста."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>«Творы».</strong> Напрыклад, «Гары Потэр і Пакой Сакрэтаў» як лагічная канцэпцыя, якая ахоплівае ўсе версіі яго, як розныя пераклады і перавыданні. Гэта даволі карыснае вызначэнне, але можа быць цяжка вызначыць, што лічыць. Напрыклад, мы, верагодна, хочам захаваць розныя пераклады, хоць перавыданні з нязначнымі адрозненнямі могуць быць не такімі важнымі."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>«Выданні».</strong> Тут вы лічыце кожную унікальную версію кнігі. Калі нешта ў ёй адрозніваецца, напрыклад, іншая вокладка або іншая прадмова, гэта лічыцца іншым выданнем."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Файлы.</strong> Пры працы з ценявымі бібліятэкамі, такімі як Library Genesis, Sci-Hub або Z-Library, ёсць дадатковы аспект. Можа быць некалькі сканаванняў аднаго і таго ж выдання. І людзі могуць ствараць лепшыя версіі існуючых файлаў, скануючы тэкст з дапамогай OCR або выпраўляючы старонкі, якія былі сканаваны пад вуглом. Мы хочам лічыць гэтыя файлы як адно выданне, што патрабуе добрай metadata або дэдуплікацыі з выкарыстаннем мер падобнасці дакументаў."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "«Выданні» здаюцца найбольш практычным вызначэннем таго, што такое «кнігі». Зручна, што гэтае вызначэнне таксама выкарыстоўваецца для прысваення унікальных нумароў ISBN. ISBN, або Міжнародны стандартны кніжны нумар, звычайна выкарыстоўваецца ў міжнародным гандлі, паколькі ён інтэграваны з міжнароднай сістэмай штрых-кодаў («Міжнародны нумар артыкула»). Калі вы хочаце прадаваць кнігу ў крамах, ёй патрэбен штрых-код, таму вы атрымліваеце ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "У блогу Тэйчера згадваецца, што хоць ISBN карысныя, яны не з'яўляюцца універсальнымі, паколькі яны былі сапраўды прыняты толькі ў сярэдзіне сямідзесятых гадоў і не ўсюды па свеце. Тым не менш, ISBN, верагодна, з'яўляецца найбольш шырока выкарыстоўваным ідэнтыфікатарам выданняў кніг, таму гэта наш лепшы пачатковы пункт. Калі мы зможам знайсці ўсе ISBN у свеце, мы атрымаем карысны спіс кніг, якія яшчэ трэба захаваць."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Дык адкуль мы атрымліваем дадзеныя? Ёсць шэраг існуючых намаганняў, якія спрабуюць скласці спіс усіх кніг у свеце:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> У рэшце рэшт, яны правялі гэтае даследаванне для Google Books. Аднак іх metadata не даступна ў масавым парадку і даволі цяжка для скрэпінгу."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Як ужо згадвалася, гэта іх уся місія. Яны атрымалі велізарныя аб'ёмы бібліятэчных дадзеных ад супрацоўнічаючых бібліятэк і нацыянальных архіваў і працягваюць гэта рабіць. У іх таксама ёсць валанцёры-бібліятэкары і тэхнічная каманда, якія спрабуюць дэдуплікаваць запісы і пазначыць іх усімі відамі metadata. Лепш за ўсё, іх набор дадзеных цалкам адкрыты. Вы можаце проста <a %(openlibrary)s>спампаваць яго</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Гэта вэб-сайт, які кіруецца некамерцыйнай арганізацыяй OCLC, якая прадае сістэмы кіравання бібліятэкамі. Яны агрэгуюць metadata кніг з мноства бібліятэк і робяць яго даступным праз вэб-сайт WorldCat. Аднак яны таксама зарабляюць грошы, прадаючы гэтыя дадзеныя, таму яны не даступныя для масавага спампоўвання. Яны маюць некаторыя больш абмежаваныя масавыя наборы дадзеных, даступныя для спампоўвання, у супрацоўніцтве з пэўнымі бібліятэкамі."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Гэта тэма гэтага блога. ISBNdb скрэпіць розныя вэб-сайты для metadata кніг, у прыватнасці дадзеныя аб цэнах, якія яны затым прадаюць кнігагандлярам, каб яны маглі ўсталёўваць цэны на свае кнігі ў адпаведнасці з астатнім рынкам. Паколькі ISBN у наш час даволі ўніверсальныя, яны фактычна стварылі «вэб-старонку для кожнай кнігі»."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Розныя індывідуальныя бібліятэчныя сістэмы і архівы.</strong> Ёсць бібліятэкі і архівы, якія не былі індэксаваны і агрэгаваны ні адным з вышэйзгаданых, часта таму, што яны недастаткова фінансуюцца або па іншых прычынах не хочуць дзяліцца сваімі дадзенымі з Open Library, OCLC, Google і гэтак далей. Многія з іх маюць лічбавыя запісы, даступныя праз інтэрнэт, і яны часта не вельмі добра абаронены, таму, калі вы хочаце дапамагчы і атрымаць задавальненне ад вывучэння дзіўных бібліятэчных сістэм, гэта выдатныя адпраўныя пункты."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "У гэтым паведамленні мы рады аб'явіць невялікі выпуск (у параўнанні з нашымі папярэднімі выпускамі Z-Library). Мы скрэпілі большасць ISBNdb і зрабілі дадзеныя даступнымі для торэнта на вэб-сайце Pirate Library Mirror (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>; мы не будзем спасылацца на яго тут наўпрост, проста знайдзіце яго). Гэта каля 30,9 мільёна запісаў (20 ГБ як <a %(jsonlines)s>JSON Lines</a>; 4,4 ГБ у сціснутым выглядзе). На іх вэб-сайце яны сцвярджаюць, што ў іх на самай справе ёсць 32,6 мільёна запісаў, таму мы маглі б неяк прапусціць некаторыя, або <em>яны</em> могуць рабіць нешта няправільна. У любым выпадку, пакуль мы не будзем дзяліцца дакладна, як мы гэта зрабілі — мы пакінем гэта як практыкаванне для чытача. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Тое, што мы падзелімся, — гэта некаторы папярэдні аналіз, каб паспрабаваць наблізіцца да ацэнкі колькасці кніг у свеце. Мы разгледзелі тры наборы дадзеных: гэты новы набор дадзеных ISBNdb, наша арыгінальнае выданне метададзеных, якія мы скрабавалі з ценявой бібліятэкі Z-Library (якая ўключае Library Genesis), і выгрузку дадзеных Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Пачнем з некаторых прыблізных лічбаў:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "У абедзвюх Z-Library/Libgen і Open Library ёсць значна больш кніг, чым унікальных ISBN. Ці азначае гэта, што шмат з гэтых кніг не маюць ISBN, або проста адсутнічае metadata ISBN? Мы, верагодна, можам адказаць на гэтае пытанне з дапамогай аўтаматызаванага супастаўлення на аснове іншых атрыбутаў (назва, аўтар, выдавецтва і г.д.), прыцягнення большай колькасці крыніц дадзеных і выцягвання ISBN з саміх сканаванняў кніг (у выпадку Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Колькі з гэтых ISBN з'яўляюцца унікальнымі? Гэта лепш за ўсё ілюструецца дыяграмай Вена:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Каб быць больш дакладным:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Мы былі здзіўлены, наколькі мала перакрыццяў! ISBNdb мае велізарную колькасць ISBN, якія не з'яўляюцца ні ў Z-Library, ні ў Open Library, і тое ж самае адносіцца (у меншай, але ўсё ж значнай ступені) да астатніх двух. Гэта выклікае шмат новых пытанняў. Наколькі аўтаматызаванае супастаўленне дапаможа ў пазначэнні кніг, якія не былі пазначаны ISBN? Ці будзе шмат супадзенняў і, такім чынам, павялічанае перакрыцце? Таксама, што адбудзецца, калі мы дадамо 4-ы або 5-ы набор дадзеных? Наколькі вялікім будзе перакрыцце тады?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Гэта дае нам пачатковы пункт. Цяпер мы можам паглядзець на ўсе ISBN, якія не былі ў наборы дадзеных Z-Library, і якія таксама не супадаюць з палямі назвы/аўтара. Гэта можа даць нам магчымасць захаваць усе кнігі ў свеце: спачатку шляхам скрабавання інтэрнэту для сканаванняў, затым шляхам выхаду ў рэальны свет для сканавання кніг. Апошняе можа нават быць фінансавана грамадскасцю або кіравацца «наградамі» ад людзей, якія хацелі б убачыць пэўныя кнігі ў лічбавым фармаце. Усё гэта гісторыя для іншага часу."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Калі вы хочаце дапамагчы з гэтым — далейшы аналіз; скрабаванне большай колькасці метададзеных; пошук большай колькасці кніг; OCR кніг; выкананне гэтага для іншых абласцей (напрыклад, дакументы, аўдыякнігі, фільмы, тэлешоў, часопісы) або нават прадастаўленне часткі гэтых дадзеных для такіх рэчаў, як ML / навучанне вялікіх моўных мадэляў — калі ласка, звяжыцеся са мной (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Калі вас асабліва цікавіць аналіз дадзеных, мы працуем над тым, каб зрабіць нашы наборы дадзеных і скрыпты даступнымі ў больш зручным для выкарыстання фармаце. Было б выдатна, калі б вы маглі проста зрабіць форк нататніка і пачаць з гэтым гуляць."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Нарэшце, калі вы хочаце падтрымаць гэтую працу, калі ласка, разгледзьце магчымасць зрабіць ахвяраванне. Гэта цалкам валанцёрская аперацыя, і ваш уклад мае вялікае значэнне. Кожная дробязь дапамагае. Пакуль мы прымаем ахвяраванні ў криптовалюте; глядзіце старонку Ахвяраванні на Архіве Анны."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Для некаторага разумнага вызначэння \"назаўсёды\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Вядома, пісьмовая спадчына чалавецтва значна больш, чым кнігі, асабліва ў наш час. Для мэты гэтага паста і нашых нядаўніх выданняў мы засяроджваемся на кнігах, але нашы інтарэсы распаўсюджваюцца далей."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Ёсць шмат чаго, што можна сказаць пра Аарона Шварца, але мы проста хацелі коратка згадаць яго, паколькі ён адыгрывае ключавую ролю ў гэтай гісторыі. З цягам часу больш людзей могуць упершыню сутыкнуцца з яго імем і затым самастойна паглыбіцца ў гэтую тэму."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Крытычнае акно ценявых бібліятэк"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды, калі яны ўжо набліжаюцца да 1 ПБ?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Кітайская версія 中文版</a>, абмеркаванне на <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "У Архіве Анны нас часта пытаюцца, як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды, калі агульны аб'ём ужо набліжаецца да 1 Петабайта (1000 ТБ) і працягвае расці. У гэтым артыкуле мы разгледзім нашу філасофію і паглядзім, чаму наступнае дзесяцігоддзе з'яўляецца крытычным для нашай місіі па захаванні ведаў і культуры чалавецтва."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Агульны аб'ём</a> нашых калекцый за апошнія некалькі месяцаў, разбіты па колькасці сідэраў торэнта."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Прыярытэты"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Чаму мы так клапоцімся пра дакументы і кнігі? Адкладзем у бок нашае фундаментальнае перакананне ў захаванні ўвогуле — магчыма, мы напішам пра гэта асобны пост. Дык чаму дакументы і кнігі ў прыватнасці? Адказ просты: <strong>шчыльнасць інфармацыі</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "На мегабайт захоўвання напісаны тэкст захоўвае найбольшую колькасць інфармацыі з усіх медыя. Хоць мы клапоцімся як пра веды, так і пра культуру, мы больш клапоцімся пра першае. У цэлым, мы знаходзім іерархію шчыльнасці інфармацыі і важнасці захавання, якая выглядае прыблізна так:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Навуковыя артыкулы, часопісы, справаздачы"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Арганічныя даныя, такія як паслядоўнасці ДНК, насенне раслін або мікрабныя ўзоры"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Нефікцыйныя кнігі"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Навуковае і інжынернае праграмнае забеспячэнне"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Дадзеныя вымярэнняў, такія як навуковыя вымярэнні, эканамічныя дадзеныя, карпаратыўныя справаздачы"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Навуковыя і інжынерныя сайты, онлайн-дыскусіі"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Нефікцыйныя часопісы, газеты, кіраўніцтвы"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Нефікцыйныя транскрыпцыі выступленняў, дакументальных фільмаў, падкастаў"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Унутраныя даныя ад карпарацый або ўрадаў (уцечкі)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Запісы metadata у цэлым (фікцыйныя і нефікцыйныя; іншыя медыя, мастацтва, людзі і г.д.; уключаючы агляды)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Геаграфічныя даныя (напрыклад, карты, геалагічныя даследаванні)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Транскрыпцыі юрыдычных або судовых працэсаў"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Фікцыйныя або забаўляльныя версіі ўсяго вышэйпералічанага"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Рэйтынг у гэтым спісе з'яўляецца некалькі адвольным — некалькі пунктаў маюць аднолькавы рэйтынг або ёсць рознагалоссі ў нашай камандзе — і мы, магчыма, забываем некаторыя важныя катэгорыі. Але гэта прыблізна тое, як мы прыярытэтуем."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Некаторыя з гэтых пунктаў занадта адрозніваюцца ад іншых, каб мы маглі турбавацца (або ўжо займаюцца іншымі ўстановамі), такія як арганічныя даныя або геаграфічныя даныя. Але большасць пунктаў у гэтым спісе на самай справе важныя для нас."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Яшчэ адзін вялікі фактар у нашай прыярытэзацыі — гэта наколькі рызыкоўная пэўная праца. Мы аддаем перавагу засяродзіцца на працах, якія:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Рэдкія"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Унікальна недастаткова асветленыя"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Унікальна падвяргаюцца рызыцы знішчэння (напрыклад, вайной, скарачэннем фінансавання, судовымі пазовамі або палітычнымі пераследамі)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Нарэшце, нас цікавіць маштаб. У нас абмежаваны час і грошы, таму мы аддаем перавагу выдаткаваць месяц на захаванне 10 000 кніг, чым 1 000 кніг — калі яны прыкладна аднолькава каштоўныя і падвяргаюцца рызыцы."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Ценявыя бібліятэкі"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Існуе шмат арганізацый, якія маюць падобныя місіі і прыярытэты. Сапраўды, ёсць бібліятэкі, архівы, лабараторыі, музеі і іншыя ўстановы, якія займаюцца захаваннем такога роду. Многія з іх добра фінансуюцца ўрадамі, прыватнымі асобамі або карпарацыямі. Але ў іх ёсць адна вялікая сляпая зона: прававая сістэма."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Тут заключаецца унікальная роля ценявых бібліятэк і прычына існавання Архіва Анны. Мы можам рабіць тое, што іншыя ўстановы не могуць. Цяпер, гэта не (часта) тое, што мы можам архіваваць матэрыялы, якія незаконна захоўваць у іншых месцах. Не, у многіх месцах законна ствараць архіў з любымі кнігамі, артыкуламі, часопісамі і гэтак далей."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Але тое, чаго часта не хапае ў легальных архівах, гэта <strong>рэзервовае капіраванне і даўгавечнасць</strong>. Існуюць кнігі, з якіх існуе толькі адзін экзэмпляр у нейкай фізічнай бібліятэцы. Існуюць запісы metadata, якія ахоўваюцца адной карпарацыяй. Існуюць газеты, захаваныя толькі на мікрафільме ў адным архіве. Бібліятэкі могуць сутыкнуцца з скарачэннем фінансавання, карпарацыі могуць абанкруціцца, архівы могуць быць разбомблены і спалены да зямлі. Гэта не гіпатэтычна — гэта адбываецца ўвесь час."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Тое, што мы можам унікальна рабіць у Архіве Анны, — гэта захоўваць шмат копій твораў у вялікіх маштабах. Мы можам збіраць артыкулы, кнігі, часопісы і іншае, і распаўсюджваць іх масава. У цяперашні час мы робім гэта праз торэнты, але дакладныя тэхналогіі не маюць значэння і будуць змяняцца з цягам часу. Важна тое, каб шмат копій было распаўсюджана па ўсім свеце. Гэты цытат з больш чым 200 гадоў таму ўсё яшчэ актуальны:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Страчанае не можа быць адноўлена; але давайце захаваем тое, што засталося: не ў сховішчах і замках, якія хаваюць іх ад грамадскага вока і выкарыстання, аддаючы іх на марнаванне часу, а шляхам такой множнасці копій, якая паставіць іх па-за дасягальнасцю выпадковасці.</q></em><br>— Томас Джэферсан, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Кароткая заўвага пра грамадскую ўласнасць. Паколькі Архіў Анны ўнікальна засяроджваецца на дзейнасці, якая з'яўляецца незаконнай у многіх месцах па ўсім свеце, мы не турбуемся пра шырока даступныя калекцыі, такія як кнігі грамадскай уласнасці. Юрыдычныя асобы часта ўжо добра клапоцяцца пра гэта. Аднак ёсць меркаванні, якія часам прымушаюць нас працаваць над публічна даступнымі калекцыямі:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Запісы metadata можна свабодна праглядаць на сайце Worldcat, але не загружаць масава (пакуль мы іх не <a %(worldcat_scrape)s>скрэпілі</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Код можа быць з адкрытым зыходным кодам на Github, але Github у цэлым нельга лёгка адлюстраваць і, такім чынам, захаваць (хаця ў гэтым канкрэтным выпадку існуюць дастаткова распаўсюджаныя копіі большасці рэпазітароў кода)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit можна выкарыстоўваць бясплатна, але нядаўна ён увёў строгія меры супраць скрэпінгу ў сувязі з патрэбай у дадзеных для навучання LLM (падрабязней пра гэта пазней)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Множнасць копій"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Вяртаючыся да нашага першапачатковага пытання: як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды? Асноўная праблема тут у тым, што наша калекцыя <a %(torrents_stats)s>расце</a> вельмі хутка, дзякуючы скрэпінгу і адкрытаму зыходнаму коду некаторых вялікіх калекцый (на дадатак да выдатнай працы, ужо зробленай іншымі бібліятэкамі з адкрытымі дадзенымі, такімі як Sci-Hub і Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Гэты рост дадзеных ускладняе адлюстраванне калекцый па ўсім свеце. Захоўванне дадзеных каштуе дорага! Але мы аптымістычныя, асабліва назіраючы за наступнымі трыма тэндэнцыямі."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Мы сабралі лёгкадаступныя рэсурсы"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Гэта вынікае непасрэдна з нашых прыярытэтаў, абмеркаваных вышэй. Мы аддаем перавагу працаваць над вызваленнем вялікіх калекцый у першую чаргу. Цяпер, калі мы забяспечылі некаторыя з найбуйнейшых калекцый у свеце, мы чакаем, што наш рост будзе значна павольнейшым."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Існуе яшчэ доўгі хвост меншых калекцый, і новыя кнігі скануюцца або публікуюцца кожны дзень, але хуткасць, верагодна, будзе значна павольнейшай. Мы можам яшчэ падвоіцца ці нават патроіцца ў памеры, але на працягу больш доўгага перыяду часу."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Кошт захоўвання працягвае экспанентна зніжацца"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "На момант напісання, <a %(diskprices)s>цэны на дыскі</a> за ТБ складаюць каля $12 за новыя дыскі, $8 за выкарыстаныя дыскі і $4 за стужку. Калі мы будзем асцярожнымі і паглядзім толькі на новыя дыскі, гэта азначае, што захоўванне петабайта каштуе каля $12,000. Калі мы мяркуем, што наша бібліятэка патроіцца з 900 ТБ да 2.7 ПБ, гэта будзе азначаць $32,400 для адлюстравання ўсёй нашай бібліятэкі. Дадаючы электрычнасць, кошт іншага абсталявання і гэтак далей, давайце акруглім да $40,000. Або са стужкай больш як $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "З аднаго боку, <strong>$15,000–$40,000 за суму ўсіх чалавечых ведаў — гэта выдатная здзелка</strong>. З іншага боку, гэта крыху крута, каб чакаць тоны поўных копій, асабліва калі мы таксама хацелі б, каб гэтыя людзі працягвалі раздаваць свае торэнты на карысць іншых."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Гэта сёння. Але прагрэс рухаецца наперад:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Кошт жорсткіх дыскаў за ТБ быў прыкладна зніжаны ў траціну за апошнія 10 гадоў і, верагодна, будзе працягваць зніжацца з падобнай хуткасцю. Стужка, здаецца, ідзе па падобнай траекторыі. Цэны на SSD зніжаюцца яшчэ хутчэй і могуць перасягнуць цэны на HDD да канца дзесяцігоддзя."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Тэндэнцыі цэн на HDD з розных крыніц (націсніце, каб праглядзець даследаванне)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Калі гэта спраўдзіцца, то праз 10 гадоў мы можам разлічваць на выдаткі ўсяго $5,000–$13,000, каб адлюстраваць усю нашу калекцыю (1/3), або нават менш, калі мы будзем расці павольней. Хоць гэта ўсё яшчэ шмат грошай, гэта будзе дасягальна для многіх людзей. І гэта можа быць яшчэ лепш з-за наступнага пункта…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Паляпшэнні ў шчыльнасці інфармацыі"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "У цяперашні час мы захоўваем кнігі ў сырых фарматах, у якіх яны нам падаюцца. Вядома, яны сціснутыя, але часта гэта ўсё яшчэ вялікія сканы або фатаграфіі старонак."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Да гэтага часу адзінымі варыянтамі для скарачэння агульнага памеру нашай калекцыі былі больш агрэсіўнае сцісканне або дэдуплікацыя. Аднак, каб атрымаць дастаткова значную эканомію, абодва варыянты занадта страчальныя для нас. Сільнае сцісканне фатаграфій можа зрабіць тэкст ледзь чытэльным. А дэдуплікацыя патрабуе высокай упэўненасці ў тым, што кнігі дакладна аднолькавыя, што часта занадта недакладна, асабліва калі змест аднолькавы, але сканы зроблены ў розны час."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Заўсёды быў трэці варыянт, але яго якасць была настолькі жахлівай, што мы ніколі не разглядалі яго: <strong>OCR, або аптычнае распазнаванне сімвалаў</strong>. Гэта працэс пераўтварэння фатаграфій у просты тэкст з дапамогай штучнага інтэлекту для распазнавання сімвалаў на фатаграфіях. Інструменты для гэтага існуюць даўно і былі даволі прыстойнымі, але \"даволі прыстойна\" недастаткова для мэтаў захавання."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Аднак нядаўнія шматмодальныя мадэлі глыбокага навучання зрабілі надзвычай хуткі прагрэс, хоць і з высокімі выдаткамі. Мы чакаем, што дакладнасць і выдаткі значна палепшацца ў бліжэйшыя гады, да такой ступені, што гэта стане рэалістычным для прымянення да ўсёй нашай бібліятэкі."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Паляпшэнні OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Калі гэта адбудзецца, мы, верагодна, усё яшчэ захаваем арыгінальныя файлы, але ў дадатак мы можам мець значна меншую версію нашай бібліятэкі, якую большасць людзей захоча адлюстраваць. Сакрэт у тым, што сыры тэкст сам па сабе сціскаецца яшчэ лепш і значна лягчэй дэдуплікуецца, што дае нам яшчэ больш эканоміі."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "У цэлым, не нереалістычна чакаць, што агульны памер файлаў зменшыцца як мінімум у 5-10 разоў, магчыма, нават больш. Нават пры кансерватыўным скарачэнні ў 5 разоў, мы б разлічвалі на <strong>$1,000–$3,000 праз 10 гадоў, нават калі наша бібліятэка павялічыцца ў тры разы</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Крытычнае акно"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Калі гэтыя прагнозы дакладныя, нам <strong>трэба толькі пачакаць некалькі гадоў</strong>, пакуль уся наша калекцыя не будзе шырока адлюстравана. Такім чынам, па словах Томаса Джэферсана, “размешчана па-за дасягальнасцю выпадковасці”."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "На жаль, з'яўленне LLM і іх дадзенагалоднага навучання паставіла многіх уладальнікаў аўтарскіх правоў у абарончую пазіцыю. Яшчэ больш, чым яны ўжо былі. Многія вэб-сайты ўскладняюць скрэйпінг і архіваванне, судовыя справы лятаюць вакол, і ў той жа час фізічныя бібліятэкі і архівы працягваюць быць занядбанымі."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Мы можам толькі чакаць, што гэтыя тэндэнцыі будуць працягваць пагаршацца, і многія працы будуць страчаны задоўга да таго, як яны ўвойдуць у грамадскі здабытак."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Мы знаходзімся на парозе рэвалюцыі ў захаванні, але <q>страчанае не можа быць адноўлена.</q></strong> У нас ёсць крытычнае акно прыкладна 5-10 гадоў, на працягу якога ўсё яшчэ даволі дорага кіраваць ценявой бібліятэкай і ствараць шматлікія люстэркі па ўсім свеце, і на працягу якога доступ яшчэ не быў цалкам закрыты."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Калі мы зможам пераадолець гэта акно, то сапраўды захаваем веды і культуру чалавецтва назаўсёды. Мы не павінны дазволіць гэтаму часу быць змарнаваным. Мы не павінны дазволіць гэтаму крытычнаму акну зачыніцца для нас."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Пачнем."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Эксклюзіўны доступ для кампаній LLM да найбуйнейшай у свеце калекцыі кітайскіх нон-фікшн кніг"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Кітайская версія 中文版</a>, <a %(news_ycombinator)s>Абмеркаваць на Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Каротка:</strong> Архіў Анны набыў унікальную калекцыю з 7,5 мільёна / 350 ТБ кітайскіх нон-фікшн кніг — больш, чым Library Genesis. Мы гатовыя даць кампаніі LLM эксклюзіўны доступ у абмен на якаснае OCR і выманне тэксту.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Гэта кароткі блог-пост. Мы шукаем кампанію або ўстанову, якая дапаможа нам з OCR і выманнем тэксту для велізарнай калекцыі, якую мы набылі, у абмен на эксклюзіўны ранні доступ. Пасля перыяду эмбарга мы, вядома, выпусцім усю калекцыю."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Высокая якасць акадэмічнага тэксту надзвычай карысная для навучання LLM. Хоць наша калекцыя кітайская, гэта павінна быць карысным нават для навучання англійскіх LLM: мадэлі, здаецца, кадуюць канцэпцыі і веды незалежна ад мовы крыніцы."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Для гэтага тэкст трэба выцягнуць са сканаў. Што атрымлівае Архіў Анны з гэтага? Поўнатэкставы пошук па кнігах для сваіх карыстальнікаў."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Паколькі нашы мэты супадаюць з мэтамі распрацоўшчыкаў LLM, мы шукаем супрацоўніка. Мы гатовыя даць вам <strong>эксклюзіўны ранні доступ да гэтай калекцыі ў аб'ёме на 1 год</strong>, калі вы зможаце правільна выканаць OCR і выманне тэксту. Калі вы гатовыя падзяліцца з намі ўсім кодам вашай сістэмы, мы гатовыя падоўжыць перыяд эмбарга."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Прыкладныя старонкі"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Каб даказаць нам, што ў вас ёсць добрая сістэма, вось некалькі прыкладных старонак для пачатку, з кнігі пра звышправоднікі. Ваша сістэма павінна правільна апрацоўваць матэматыку, табліцы, дыяграмы, зноскі і гэтак далей."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Дашліце апрацаваныя старонкі на наш электронны адрас. Калі яны будуць выглядаць добра, мы дашлем вам больш у прыватным парадку, і мы чакаем, што вы зможаце хутка запусціць вашу сістэму на іх таксама. Калі мы будзем задаволеныя, мы зможам заключыць здзелку."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Калекцыя"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Некаторая дадатковая інфармацыя пра калекцыю. <a %(duxiu)s>Duxiu</a> — гэта велізарная база дадзеных адсканаваных кніг, створаная <a %(chaoxing)s>SuperStar Digital Library Group</a>. Большасць з іх — акадэмічныя кнігі, адсканаваныя для таго, каб зрабіць іх даступнымі ў лічбавым выглядзе для ўніверсітэтаў і бібліятэк. Для нашай англамоўнай аўдыторыі <a %(library_princeton)s>Прынстан</a> і <a %(guides_lib_uw)s>Універсітэт Вашынгтона</a> маюць добрыя агляды. Таксама ёсць выдатны артыкул, які дае больш інфармацыі: <a %(doi)s>“Алічбоўка кітайскіх кніг: Выпадак даследавання пошукавай сістэмы SuperStar DuXiu Scholar”</a> (знайдзіце ў Архіве Анны)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Кнігі з Duxiu даўно піратуюцца ў кітайскім інтэрнэце. Звычайна яны прадаюцца па цане менш за долар перапрадаўцамі. Яны звычайна распаўсюджваюцца з дапамогай кітайскага аналага Google Drive, які часта ўзломваецца для павелічэння аб'ёму сховішча. Некаторыя тэхнічныя дэталі можна знайсці <a %(github_duty_machine)s>тут</a> і <a %(github_821_github_io)s>тут</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Хоць кнігі былі паўпублічна распаўсюджаны, іх даволі складана атрымаць у вялікім аб'ёме. Мы мелі гэта высока ў нашым спісе задач і выдзелілі некалькі месяцаў поўнай занятасці для гэтага. Аднак нядаўна да нас звярнуўся неверагодны, цудоўны і таленавіты валанцёр, які паведаміў, што ўжо выканаў усю гэтую працу — за вялікія выдаткі. Яны падзяліліся з намі поўнай калекцыяй, не чакаючы нічога ўзамен, акрамя гарантыі доўгатэрміновага захавання. Сапраўды выдатна. Яны пагадзіліся папрасіць дапамогі такім чынам, каб калекцыя была апрацавана з дапамогай OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Калекцыя складаецца з 7,543,702 файлаў. Гэта больш, чым Library Genesis non-fiction (каля 5.3 мільёна). Агульны памер файлаў складае каля 359TB (326TiB) у цяперашнім выглядзе."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Мы адкрыты для іншых прапаноў і ідэй. Проста звяжыцеся з намі. Праверце Архіў Анны для атрымання большай інфармацыі пра нашы калекцыі, намаганні па захаванні і як вы можаце дапамагчы. Дзякуй!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Папярэджанне: гэты блогавы пост быў састарэлы. Мы вырашылі, што IPFS яшчэ не гатовы для шырокага выкарыстання. Мы ўсё яшчэ будзем спасылацца на файлы на IPFS з архіва Анны, калі гэта магчыма, але больш не будзем размяшчаць яго самастойна і не рэкамендуем іншым люстэркаваць з выкарыстаннем IPFS. Калі ласка, глядзіце нашу старонку з торэнтамі, калі вы хочаце дапамагчы захаваць нашу калекцыю."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Дапамажыце распаўсюджваць Z-Library на IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Як кіраваць ценявой бібліятэкай: аперацыі ў Архіве Анны"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Няма <q>AWS для ценявых дабрачынных арганізацый,</q> дык як мы кіруем Архівам Анны?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Я кірую <a %(wikipedia_annas_archive)s>Архівам Анны</a>, найбуйнейшай у свеце адкрытай некамерцыйнай пошукавай сістэмай для <a %(wikipedia_shadow_library)s>ценявых бібліятэк</a>, такіх як Sci-Hub, Library Genesis і Z-Library. Наша мэта — зрабіць веды і культуру лёгка даступнымі і ў канчатковым выніку стварыць супольнасць людзей, якія разам архівуюць і захоўваюць <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>усе кнігі ў свеце</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "У гэтым артыкуле я пакажу, як мы кіруем гэтым сайтам, і унікальныя выклікі, якія ўзнікаюць пры эксплуатацыі сайта з сумніўным юрыдычным статусам, паколькі няма \"AWS для ценявых дабрачынных арганізацый\"."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Таксама азнаёмцеся з сястрынскім артыкулам <a %(blog_how_to_become_a_pirate_archivist)s>Як стаць піратам-архівістам</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Токены інавацый"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Пачнем з нашай тэхналагічнай стэкі. Яна наўмысна сумная. Мы выкарыстоўваем Flask, MariaDB і ElasticSearch. І гэта літаральна ўсё. Пошук у асноўным вырашаная праблема, і мы не збіраемся яе пераасэнсоўваць. Акрамя таго, мы павінны выдаткаваць нашы <a %(mcfunley)s>токены інавацый</a> на нешта іншае: не быць закрытымі ўладамі."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Наколькі законны ці незаконны архіў Анны? Гэта ў асноўным залежыць ад юрысдыкцыі. Большасць краін прытрымліваецца нейкай формы аўтарскага права, што азначае, што людзям або кампаніям прысвойваецца эксклюзіўная манаполія на пэўныя віды твораў на пэўны перыяд часу. Дарэчы, у архіве Анны мы лічым, што, хоць ёсць некаторыя перавагі, у цэлым аўтарскае права з'яўляецца негатыўным для грамадства — але гэта гісторыя для іншага часу."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Гэта эксклюзіўная манаполія на пэўныя творы азначае, што незаконна для любога, хто знаходзіцца па-за гэтай манаполіяй, непасрэдна распаўсюджваць гэтыя творы — уключаючы нас. Але архіў Анны — гэта пошукавая сістэма, якая не распаўсюджвае гэтыя творы непасрэдна (прынамсі, не на нашым сайце ў адкрытым доступе), таму мы павінны быць у парадку, так? Не зусім. У многіх юрысдыкцыях незаконна не толькі распаўсюджваць абароненыя аўтарскім правам творы, але і спасылацца на месцы, якія гэта робяць. Класічным прыкладам гэтага з'яўляецца закон DMCA Злучаных Штатаў."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Гэта самы строгі канец спектра. На іншым канцы спектра тэарэтычна могуць быць краіны, у якіх наогул няма законаў аб аўтарскім праве, але такіх на самай справе не існуе. Амаль у кожнай краіне ёсць нейкая форма закона аб аўтарскім праве. Выкананне — гэта іншая гісторыя. Ёсць шмат краін з урадамі, якія не жадаюць выконваць закон аб аўтарскім праве. Ёсць таксама краіны паміж гэтымі двума крайнасцямі, якія забараняюць распаўсюджваць абароненыя аўтарскім правам творы, але не забараняюць спасылацца на такія творы."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Іншы аспект — гэта ўзровень кампаніі. Калі кампанія працуе ў юрысдыкцыі, якая не клапоціцца пра аўтарскае права, але сама кампанія не жадае рызыкаваць, то яны могуць закрыць ваш сайт, як толькі хтосьці на яго паскардзіцца."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Нарэшце, вялікае значэнне маюць плацяжы. Паколькі нам трэба заставацца ананімнымі, мы не можам выкарыстоўваць традыцыйныя спосабы аплаты. Гэта пакідае нам криптовалюты, і толькі невялікая частка кампаній падтрымлівае іх (ёсць віртуальныя дэбетавыя карты, аплачаныя криптовалютай, але іх часта не прымаюць)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Архітэктура сістэмы"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Такім чынам, скажам, вы знайшлі некалькі кампаній, якія гатовыя размясціць ваш сайт, не закрываючы яго — назавем іх «правайдэры, якія любяць свабоду» 😄. Вы хутка выявіце, што размяшчэнне ўсяго з імі даволі дарагое, таму вы можаце знайсці некаторых «танных правайдэраў» і размясціць там фактычнае размяшчэнне, праксіруючы праз правайдэраў, якія любяць свабоду. Калі вы зробіце гэта правільна, танныя правайдэры ніколі не даведаюцца, што вы размяшчаеце, і ніколі не атрымаюць ніякіх скаргаў."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "З усімі гэтымі правайдэрамі існуе рызыка, што яны ўсё роўна вас закрыюць, таму вам таксама патрэбна рэзерваванне. Нам гэта трэба на ўсіх узроўнях нашай стэкі."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Адна з кампаній, якая крыху любіць свабоду і паставіла сябе ў цікавае становішча, — гэта Cloudflare. Яны <a %(blog_cloudflare)s>сцвярджалі</a>, што яны не з'яўляюцца хостынгавым правайдэрам, а ўтылітай, як ISP. Таму яны не падлягаюць DMCA або іншым запытам на выдаленне і перасылаюць любыя запыты вашаму фактычнаму хостынгаваму правайдэру. Яны зайшлі так далёка, што пайшлі ў суд, каб абараніць гэтую структуру. Таму мы можам выкарыстоўваць іх як яшчэ адзін пласт кэшавання і абароны."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare не прымае ананімныя плацяжы, таму мы можам выкарыстоўваць толькі іх бясплатны план. Гэта азначае, што мы не можам выкарыстоўваць іх функцыі балансавання нагрузкі або рэзервавання. Таму мы <a %(annas_archive_l255)s>рэалізавалі гэта самастойна</a> на ўзроўні дамена. Пры загрузцы старонкі браўзер праверыць, ці даступны бягучы дамен, і калі не, перапісвае ўсе URL на іншы дамен. Паколькі Cloudflare кэшуе шмат старонак, гэта азначае, што карыстальнік можа трапіць на наш асноўны дамен, нават калі праксі-сервер не працуе, а затым пры наступным кліку перайсці на іншы дамен."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "У нас таксама ёсць звычайныя аперацыйныя праблемы, з якімі трэба мець справу, такія як маніторынг здароўя сервера, рэгістрацыя памылак на баку сервера і кліента і гэтак далей. Наша архітэктура рэзервавання дазваляе большай надзейнасці ў гэтым плане, напрыклад, шляхам запуску зусім іншага набору сервераў на адным з даменаў. Мы нават можам запускаць старыя версіі кода і набораў дадзеных на гэтым асобным дамене, у выпадку, калі крытычная памылка ў асноўнай версіі застанецца незаўважанай."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Мы таксама можам засцерагчыся ад таго, што Cloudflare павернецца супраць нас, выдаліўшы яго з аднаго з даменаў, напрыклад, з гэтага асобнага дамена. Магчымыя розныя камбінацыі гэтых ідэй."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Інструменты"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Давайце паглядзім, якія інструменты мы выкарыстоўваем для дасягнення ўсяго гэтага. Гэта вельмі развіваецца, паколькі мы сутыкаемся з новымі праблемамі і знаходзім новыя рашэнні."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Сервер прыкладанняў: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Проксі-сервер: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Кіраванне серверам: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Распрацоўка: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Статычны хостынг Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ёсць некаторыя рашэнні, якія мы пераглядалі неаднаразова. Адно з іх — гэта камунікацыя паміж серверамі: раней мы выкарыстоўвалі Wireguard для гэтага, але выявілі, што ён часам перастае перадаваць дадзеныя або перадае іх толькі ў адным кірунку. Гэта адбывалася з некалькімі рознымі наладкамі Wireguard, якія мы спрабавалі, такімі як <a %(github_costela_wesher)s>wesher</a> і <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Мы таксама спрабавалі тунэляваць порты праз SSH, выкарыстоўваючы autossh і sshuttle, але сутыкнуліся з <a %(github_sshuttle)s>праблемамі там</a> (хоць мне ўсё яшчэ не зразумела, ці пакутуе autossh ад праблем TCP-over-TCP ці не — мне гэта здаецца няўстойлівым рашэннем, але, магчыма, яно на самай справе нармальнае?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Замест гэтага мы вярнуліся да прамых злучэнняў паміж серверамі, хаваючы, што сервер працуе на танных правайдэрах, выкарыстоўваючы IP-фільтраванне з UFW. Гэта мае недахоп у тым, што Docker не працуе добра з UFW, калі вы не выкарыстоўваеце <code>network_mode: \"host\"</code>. Усё гэта крыху больш схільна да памылак, таму што вы адкрыеце свой сервер для інтэрнэту з невялікай няправільнай канфігурацыяй. Магчыма, нам варта вярнуцца да autossh — зваротная сувязь тут будзе вельмі дарэчы."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Мы таксама пераглядалі Varnish супраць Nginx. Зараз нам падабаецца Varnish, але ў яго ёсць свае асаблівасці і недахопы. Тое ж самае адносіцца да Checkmk: мы яго не любім, але ён працуе пакуль што. Weblate быў нармальны, але не неверагодны — я часам баюся, што ён страціць мае дадзеныя, калі я спрабую сінхранізаваць яго з нашым git-рэпазіторыем. Flask быў добры ў цэлым, але ў яго ёсць некаторыя дзіўныя асаблівасці, якія каштавалі шмат часу на адладку, такія як наладка карыстальніцкіх даменаў або праблемы з інтэграцыяй SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Пакуль што іншыя інструменты былі выдатныя: у нас няма сур'ёзных скаргаў на MariaDB, ElasticSearch, Gitlab, Zulip, Docker і Tor. Усе яны мелі некаторыя праблемы, але нічога занадта сур'ёзнага або патрабуючага шмат часу."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Заключэнне"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Гэта быў цікавы вопыт, каб даведацца, як наладзіць надзейную і ўстойлівую пошукавую сістэму для ценявой бібліятэкі. Ёсць шмат іншых дэталяў, якія можна падзяліць у наступных пастах, таму дайце мне ведаць, пра што вы хацелі б даведацца больш!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Як заўсёды, мы шукаем ахвяраванні для падтрымкі гэтай працы, таму абавязкова наведайце старонку \"Ахвяраваць\" на Архіве Анны. Мы таксама шукаем іншыя віды падтрымкі, такія як гранты, доўгатэрміновыя спонсары, пастаўшчыкі плацяжоў з высокім рызыкай, магчыма, нават (са смакам!) рэкламу. І калі вы хочаце ўнесці свой час і навыкі, мы заўсёды шукаем распрацоўшчыкаў, перакладчыкаў і гэтак далей. Дзякуй за ваш інтарэс і падтрымку."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Прывітанне, я Анна. Я стварыла <a %(wikipedia_annas_archive)s>Архіў Анны</a>, найбуйнейшую ў свеце ценявую бібліятэку. Гэта мой асабісты блог, у якім я і мае калегі пішам пра пірацтва, лічбавую захаванасць і многае іншае."

#, fuzzy
msgid "blog.index.text2"
msgstr "Звяжыцеся са мной на <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Звярніце ўвагу, што гэты сайт — проста блог. Мы размяшчаем тут толькі свае ўласныя словы. Ніякія торэнты або іншыя абароненыя аўтарскім правам файлы тут не размяшчаюцца і не звязваюцца."

#, fuzzy
msgid "blog.index.heading"
msgstr "Паведамленні ў блогу"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat скрэйп"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Размяшчэнне 5,998,794 кніг на IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Папярэджанне: гэты блогавы пост быў састарэлы. Мы вырашылі, што IPFS яшчэ не гатовы для шырокага выкарыстання. Мы ўсё яшчэ будзем спасылацца на файлы на IPFS з архіва Анны, калі гэта магчыма, але больш не будзем размяшчаць яго самастойна і не рэкамендуем іншым люстэркаваць з выкарыстаннем IPFS. Калі ласка, глядзіце нашу старонку з торэнтамі, калі вы хочаце дапамагчы захаваць нашу калекцыю."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Кароткі змест:</strong> Архіў Анны скрэйпіў увесь WorldCat (найбуйнейшую ў свеце калекцыю бібліятэчных метададзеных), каб стварыць спіс кніг, якія трэба захаваць.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Год таму мы <a %(blog)s>пачалі</a> адказваць на гэтае пытанне: <strong>Які працэнт кніг быў пастаянна захаваны ценявымі бібліятэкамі?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Калі кніга трапляе ў бібліятэку з адкрытымі дадзенымі, як <a %(wikipedia_library_genesis)s>Library Genesis</a>, а цяпер і <a %(wikipedia_annas_archive)s>Архіў Анны</a>, яна адлюстроўваецца па ўсім свеце (праз торэнты), тым самым практычна захоўваючы яе назаўсёды."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Каб адказаць на пытанне, які працэнт кніг быў захаваны, нам трэба ведаць назоўнік: колькі кніг існуе ўсяго? І ў ідэале ў нас ёсць не проста лічба, а фактычныя метаданыя. Тады мы можам не толькі супаставіць іх з ценявымі бібліятэкамі, але і <strong>стварыць спіс задач для захавання астатніх кніг!</strong> Мы нават можам пачаць марыць пра сумесныя намаганні па выкананні гэтага спісу задач."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Мы скрэпілі <a %(wikipedia_isbndb_com)s>ISBNdb</a> і загрузілі <a %(openlibrary)s>набор дадзеных Open Library</a>, але вынікі былі незадавальняючыя. Асноўная праблема была ў тым, што не было шмат перакрыццяў ISBN. Глядзіце гэтую дыяграму Вена з <a %(blog)s>нашага блога</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Мы былі вельмі здзіўлены, наколькі мала было перакрыццяў паміж ISBNdb і Open Library, якія абодва ўключаюць дадзеныя з розных крыніц, такіх як вэб-скрэпы і бібліятэчныя запісы. Калі б яны абодва добра выконвалі сваю працу па пошуку большасці ISBN, іх кругі, безумоўна, мелі б значнае перакрыцце, або адзін быў бы падмноствам іншага. Гэта прымусіла нас задумацца, колькі кніг знаходзіцца <em>цалкам па-за гэтымі кругамі</em>? Нам патрэбна большая база дадзеных."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Тады мы нацэліліся на найбуйнейшую базу дадзеных кніг у свеце: <a %(wikipedia_worldcat)s>WorldCat</a>. Гэта прыватная база дадзеных некамерцыйнай арганізацыі <a %(wikipedia_oclc)s>OCLC</a>, якая агрэгуе метаданыя з бібліятэк па ўсім свеце ў абмен на прадастаўленне гэтых бібліятэк доступу да поўнага набору дадзеных і паказ іх у выніках пошуку канчатковых карыстальнікаў."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Нягледзячы на тое, што OCLC з'яўляецца некамерцыйнай арганізацыяй, іх бізнес-мадэль патрабуе абароны іх базы дадзеных. Ну, мы шкадуем, сябры з OCLC, мы ўсё гэта аддаем. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "За мінулы год мы старанна скрэпілі ўсе запісы WorldCat. Спачатку нам пашанцавала. WorldCat толькі што запускаў поўны рэдызайн свайго сайта (у жніўні 2022 года). Гэта ўключала значную перапрацоўку іх сістэм, што прывяло да шматлікіх уразлівасцяў бяспекі. Мы адразу скарысталіся магчымасцю і змаглі скрэпіць сотні мільёнаў (!) запісаў за лічаныя дні."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Рэдызайн WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Пасля гэтага ўразлівасці бяспекі паступова выпраўляліся адна за адной, пакуль апошняя, якую мы знайшлі, не была выпраўлена каля месяца таму. Да таго часу ў нас былі амаль усе запісы, і мы толькі імкнуліся да крыху больш якасных запісаў. Таму мы адчулі, што настаў час выпускаць!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Давайце паглядзім на некаторую асноўную інфармацыю пра дадзеныя:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Фармат?</strong> <a %(blog)s>Кантэйнеры Архіва Анны (AAC)</a>, якія па сутнасці з'яўляюцца <a %(jsonlines)s>JSON Lines</a>, сціснутымі з дапамогай <a %(zstd)s>Zstandard</a>, плюс некаторыя стандартызаваныя семантыкі. Гэтыя кантэйнеры ахопліваюць розныя тыпы запісаў, заснаваныя на розных скрэпах, якія мы разгортвалі."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Дадзеныя"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Адбылася невядомая памылка. Калі ласка, звяжыцеся з намі па %(email)s з скрыншотам."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Гэтая манета мае вышэйшы за звычайны мінімум. Калі ласка, выберыце іншую працягласць або іншую манету."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Запыт не можа быць выкананы. Калі ласка, паспрабуйце яшчэ раз праз некалькі хвілін, і калі гэта працягваецца, звяжыцеся з намі па %(email)s з скрыншотам."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Памылка апрацоўкі плацяжу. Калі ласка, пачакайце момант і паспрабуйце зноў. Калі праблема застаецца больш за 24 гадзіны, звяжыцеся з намі па %(email)s з скрыншотам."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "схаваны каментар"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Праблема з файлам: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Лепшая версія"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Вы хочаце паведаміць пра гэтага карыстальніка за абразлівыя або недарэчныя паводзіны?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Паведаміць пра злоўжыванне"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Злоўжыванне паведамлена:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Вы паведамілі пра гэтага карыстальніка за злоўжыванне."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Адказаць"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Калі ласка, <a %(a_login)s>увайдзіце ў сістэму</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Вы пакінулі каментар. Гэта можа заняць хвіліну, каб ён з'явіўся."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s закранутыя старонкі"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Не адлюстроўваецца ў \".rs-fork\" акадэмічнага падзела Library Genesis"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Не адлюстроўваецца ў \".rs-fork\" мастацкага падзелу Library Genesis"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Не адлюстроўваецца в \".li-версіі\" Library Genesis"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Пазначана як \"зламаны файл\" у \".li-версіі\" акадэмічнага раздзела Library Genesis"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Адсутнічае ў Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Пазначана як “спам” у Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Пазначана як “дрэнны файл” у Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Не ўсе старонкі можна было пераўтварыць у PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Запуск exiftool на гэтым файле не ўдалося"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Кніга (невядомая)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Кніга (акадэмічная літаратура)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Кніга (мастацкая літаратура)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Артыкул у часопісе"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Дакумент са стандартамі"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Часопіс"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Комікс"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Музычны твор"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Аўдыякніга"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Іншае"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Загрузка з сервера партнёра"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Знешняя загрузка"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Знешняе пазычанне"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Знешняе пазычанне (друк адключаны)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Даследаваць метаданыя"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Змяшчаецца ў торэнтах"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Кітайская"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Загрузкі ў AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Індэкс eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Чэшскія метаданыя"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Расійская дзяржаўная бібліятэка"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Назва"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Аўтар"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Выдавец"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Выданне"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Год выдання"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Арыгінальная назва файла"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Апісанне і каментары да метададзеных"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Спампоўванне з Партнёрскага Сервера часова недаступнае для гэтага файла."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Хуткі партнёрскі сервер №%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(рэкамендуецца)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(без праверкі браўзера або спісаў чакання)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Павольны партнёрскі сервер #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(крыху хутчэй, але з чаргой)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(без спісу чакання, але можа быць вельмі павольным)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Library Genesis \".rs-версія\" - акадэмічны раздзел"

msgid "page.md5.box.download.lgrsfic"
msgstr "Library Genesis \".rs-версія\" - мастацкая літаратура"

msgid "page.md5.box.download.lgli"
msgstr "Library Genesis \".li-версія\""

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(таксама націсніце \"GET\" уверсе)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(націсніце \"GET\" уверсе)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "іх рэклама вядомая тым, што ўтрымлівае шкоднаснае праграмнае забеспячэнне, таму выкарыстоўвайце блакіроўшчык рэкламы або не націскайце на рэкламу"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Файлы Nexus/STC могуць быць ненадзейнымі для загрузкі)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library на Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(патрабуецца ўваход праз браўзэр ТОР)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Пазычыць з Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(толькі для карыстальнікаў з абмежаванымі магчымасцямі друку)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(адпаведны DOI можа быць недаступны ў Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "калекцыя"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "торэнт"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Масавыя загрузкі торэнтаў"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(толькі для экспертаў)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Шукайце ў архіве Анны па ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Шукайце ў розных іншых базах дадзеных па ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Знайсці арыгінальны запіс у ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Шукаць у архіве Анны па ідэнтыфікатару Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Знайсці арыгінальны запіс у Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Шукайце ў Анныным Архіве па нумары OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Знайсці арыгінальны запіс у WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Шукайце ў Архіве Анны нумар SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Шукайце ўручную на DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Шукайце ў архіве Анны нумар CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Знайсці арыгінальны запіс у CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Шукайце ў архіве Анны па нумары DuXiu DXID"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Індэкс eBook EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Архіў Анны 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(не патрабуецца праверка браўзэра)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Чэшская метадата %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Трантор %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Метаданыя"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "апісанне"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Альтэрнатыўная назва файла"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Альтэрнатыўны загаловак"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Альтэрнатыўны аўтар"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Альтэрнатыўны выдаўца"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Альтэрнатыўнае выданне"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Альтэрнатыўнае пашырэнне"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "каментары да метададзеных"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Альтэрнатыўнае апісанне"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "дата адкрыцця кода"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Файл Sci-Hub «%(id)s»"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Файл Internet Archive Controlled Digital Lending «%(id)s»"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Гэта запіс файла з Internet Archive, а не файл для прамога спампоўвання. Вы можаце паспрабаваць узяць кнігу ў арэнду (спасылка ніжэй) або выкарыстоўваць гэты URL пры <a %(a_request)s>запыце файла</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Калі ў вас ёсць гэты файл і ён яшчэ не даступны ў Anna’s Archive, разгледзьце магчымасць <a %(a_request)s>загрузкі яго</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Запіс метададзеных ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Запіс метададзеных Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Нумар OCLC (WorldCat) %(id)s метададзеныя запісу"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Запіс метададзеных DuXiu SSID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s метададзеныя запісу"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Запіс метададзеных MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Запіс метададзеных Nexus/STC ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Гэта метададзеныя, а не файл для запампоўкі. Вы можаце выкарыстоўваць гэты URL, калі <a %(a_request)s>запытваеце файл</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Метаданыя з звязанага запісу"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Паляпшэнне метаданых у Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Папярэджанне: некалькі звязаных запісаў:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Паляпшэнне метададзеных"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Паведаміць пра якасць файла"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Час загрузкі"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Вэб-сайт:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Шукайце ў архіве Анны “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Даследчык кодаў:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Прагляд у Codes Explorer “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Чытаць далей…"

msgid "page.md5.tabs.downloads"
msgstr "Загрузкі (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Пазычыць (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Даследаваць метададзеныя (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Каментары (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Спісы (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Статыстыка (%(count)s)"

msgid "common.tech_details"
msgstr "Тэхнічныя дэталі (на англійскай мове)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ у гэтага файла могуць быць праблемы і ён быў скрыты з бібліятэкі зыходнага кода.</span> Часам гэта адбываецца па запыце ўладальніка аўтарскіх правоў, часам таму што даступная лепшая альтэрнатыва, але часам гэта адбываецца з-за праблемы з самім файлам. Мы рэкамендуем пашукаць альтэрнатыўны файл, але вы можаце спампаваць гэты пры жаданні. Больш інфармацыі:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Магчыма, лепшая версія гэтага файла даступная па %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Калі вы ўсё яшчэ хочаце спампаваць гэты файл, абавязкова выкарыстоўвайце для яго адкрыцця толькі праверанае, абноўленае праграмнае забеспячэнне."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Хуткія загрузкі"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong> 🚀 Хуткая загрузка </strong> станьце <a %(a_membership)s>ўдзельнікам</a>, каб падтрымаць доўгатэрміновую захаванасць кніг, дакументаў і шмат чаго іншага. У знак нашай падзякі за вашу падтрымку вы атрымліваеце хуткую загрузку. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Калі вы ахвяруеце ў гэтым месяцы, вы атрымаеце <strong>двайны</strong> лік хуткіх загрузак."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Хуткія загрузкі</strong> У вас засталося %(remaining)s на сёння. Дзякуй за тое, што вы з намі! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Хуткія спампоўкі</strong> Вы выкарысталі ўсе хуткія спампоўкі на сёння."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Хуткія загрузкі</strong> Вы нядаўна загружалі гэты файл. Спасылкі застаюцца сапраўднымі некаторы час."

msgid "page.md5.box.download.option"
msgstr "Варыянт #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(без перанакіравання)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(адкрыць у праглядальніку)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Запрасіце сябра, і вы абодва атрымаеце %(percentage)s%% бонусных хуткіх загрузак!"

msgid "layout.index.header.learn_more"
msgstr "Даведацца больш…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Павольныя загрузкі"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Ад надзейных партнёраў."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Больш інфармацыі ў <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(можа спатрэбіцца <a %(a_browser)s>праверка браўзэра</a> — неабмежаваная колькасць загрузак!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Пасля загрузкі:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Адкрыць у нашым праглядальніку"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "паказаць знешнія загрузкі"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Знешнія спампоўванні"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Загрузак не знойдзена."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Усе крыніцы ўтрымліваюць той жа файл і павінны быць бяспечнымі. Аднак будзьце асцярожныя пры загрузцы файлаў з інтэрнэту. Пераканайцеся што ваша прылада і дадатак абноўленыя да апошняй версіі для бяспекі вашых дадзеных."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Для вялікіх файлаў мы рэкамендуем выкарыстоўваць мэнэджар загрузак, каб пазбегнуць перапынкаў."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Рэкамендаваныя мэнэджары загрузак: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Вам спатрэбіцца чытач электронных кніг або PDF, каб адкрыць файл, у залежнасці ад фармату файла."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Рэкамендаваныя чытачы электронных кніг: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Анін Архіў онлайн праглядальнік"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Выкарыстоўвайце анлайн-інструменты для канвертацыі паміж фарматамі."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Рэкамендаваныя інструменты канвертацыі: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Вы можаце адправіць як PDF, так і EPUB файлы на ваш Kindle або Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Рэкамендаваныя інструменты: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon‘s “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz‘s “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Падтрымлівайце аўтараў і бібліятэкі"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Калі вам гэта падабаецца і вы можаце сабе гэта дазволіць, разгледзьце магчымасць набыцця арыгінала або падтрымкі аўтараў наўпрост."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Калі гэта даступна ў вашай мясцовай бібліятэцы, разгледзьце магчымасць узяць яе там бясплатна."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Якасць файла"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Дапамажыце супольнасці, паведамляючы пра якасць гэтага файла! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Паведаміць пра праблему з файлам (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Выдатная якасць файла (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Дадаць каментар (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Што не так з гэтым файлам?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Калі ласка, выкарыстоўвайце <a %(a_copyright)s>форму прэтэнзіі DMCA / аўтарскіх правоў</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Апішыце праблему (абавязкова)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Апісанне праблемы"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 лепшай версіі гэтага файла (калі дастасавальна)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Запоўніце гэта, калі ёсць іншы файл, які дакладна адпавядае гэтаму файлу (тае ж выданне, тое ж пашырэнне файла, калі знойдзеце), які людзі павінны выкарыстоўваць замест гэтага файла. Калі вы ведаеце пра лепшую версію гэтага файла па-за архівам Анны, калі ласка, <a %(a_upload)s>загрузіце яе</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Вы можаце атрымаць md5 з URL, напрыклад,"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Адправіць справаздачу"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Даведайцеся, як <a %(a_metadata)s>паляпшаць метаданыя</a> для гэтага файла самастойна."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Дзякуй за падачу вашага справаздачы. Яна будзе паказана на гэтай старонцы, а таксама прааналізавана ўручную Ганнай (пакуль у нас не будзе належнай сістэмы мадэрацыі)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Калі гэты файл мае выдатную якасць, вы можаце абмеркаваць яго тут! Калі не, калі ласка, выкарыстоўвайце кнопку «Паведаміць пра праблему з файлам»."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Мне вельмі спадабалася гэтая кніга!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Пакінуць каментар"

msgid "common.english_only"
msgstr "Тэкст ніжэй даступны толькі на англійскай мове."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Агульная колькасць загрузак: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "«MD5 файла» — гэта хэш, які вылічваецца з утрымання файла і з'яўляецца дастаткова унікальным на аснове гэтага ўтрымання. Усе ценявыя бібліятэкі, якія мы індэксавалі тут, у першую чаргу выкарыстоўваюць MD5 для ідэнтыфікацыі файлаў."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Файл можа з'яўляцца ў некалькіх ценявых бібліятэках. Для атрымання інфармацыі пра розныя наборы дадзеных, якія мы сабралі, глядзіце <a %(a_datasets)s>старонку Наборы дадзеных</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Гэта файл, кіраваны бібліятэкай <a %(a_ia)s>Кантраляванага лічбавага пазычання IA</a> і індэксаваны Ганнавай Архівай для пошуку. Для атрымання інфармацыі пра розныя наборы дадзеных, якія мы сабралі, глядзіце <a %(a_datasets)s>старонку Наборы дадзеных</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Для атрымання інфармацыі пра гэты канкрэтны файл, азнаёмцеся з яго <a %(a_href)s>JSON-файлам</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Праблема з загрузкай гэтай старонкі"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Абнавіце старонку, каб паспрабаваць яшчэ раз. <a %(a_contact)s>Звяжыцеся з намі</a>, калі праблема застаецца на працягу некалькіх гадзін."

msgid "page.md5.invalid.header"
msgstr "Не знойдзена"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” не знойдзена у нашай базе дадзеных."

#, fuzzy
msgid "page.login.title"
msgstr "Увайсці / Зарэгістравацца"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Праверка браўзера"

#, fuzzy
msgid "page.login.text1"
msgstr "Каб прадухіліць стварэнне шматлікіх уліковых запісаў спам-ботамі, нам трэба спачатку праверыць ваш браўзер."

#, fuzzy
msgid "page.login.text2"
msgstr "Калі вы трапілі ў бясконцую завеску, мы рэкамендуем усталяваць <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Можа таксама дапамагчы адключэнне рэкламных блакіроўшчыкаў і іншых пашырэнняў браўзэра."

#, fuzzy
msgid "page.codes.title"
msgstr "Коды"

#, fuzzy
msgid "page.codes.heading"
msgstr "Даследчык кодаў"

#, fuzzy
msgid "page.codes.intro"
msgstr "Даследуйце коды, з якімі пазначаны запісы, па прэфіксе. Слупок “запісы” паказвае колькасць запісаў, пазначаных кодамі з дадзеным прэфіксам, як гэта бачна ў пошукавай сістэме (уключаючы запісы толькі з метаданымі). Слупок “коды” паказвае, колькі фактычных кодаў маюць дадзены прэфікс."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Гэтая старонка можа заняць некаторы час для генерацыі, таму патрабуецца капча Cloudflare. <a %(a_donate)s>Члены</a> могуць прапусціць капчу."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Калі ласка, не скрабіце гэтыя старонкі. Замест гэтага мы рэкамендуем <a %(a_import)s>генераваць</a> або <a %(a_download)s>загружаць</a> нашы базы дадзеных ElasticSearch і MariaDB і запускаць наш <a %(a_software)s>адкрыты зыходны код</a>. Сырыя дадзеныя можна ўручную даследаваць праз файлы JSON, такія як <a %(a_json_file)s>гэты</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Префікс"

#, fuzzy
msgid "common.form.go"
msgstr "Ідзі"

#, fuzzy
msgid "common.form.reset"
msgstr "Скінуць"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Шукаць у Архіве Анны"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Папярэджанне: код мае няправільныя сімвалы Unicode і можа паводзіць сябе некарэктна ў розных сітуацыях. Сыры бінарны код можна дэкадаваць з base64 прадстаўлення ў URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Вядомы прэфікс кода “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Префікс"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Метка"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Апісанне"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL для канкрэтнага кода"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%” будзе заменены на значэнне кода"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Агульны URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Вэбсайт"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] ""
msgstr[1] ""
msgstr[2] "%(count)s запісаў, якія адпавядаюць “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL для канкрэтнага кода: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Больш…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Коды, якія пачынаюцца з “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Індэкс"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "запісы"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "коды"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Менш за %(count)s запісаў"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Для DMCA / аўтарскіх прэтэнзій выкарыстоўвайце <a %(a_copyright)s>гэту форму</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Любая іншая форма кантакту з намі па пытаннях аўтарскіх правоў будзе аўтаматычна выдалена."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Мы вельмі вітаем вашыя водгукі і пытанні!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Аднак, з-за вялікай колькасці спаму і непатрэбных лістоў, калі ласка, пастаўце галачкі, каб пацвердзіць, што вы разумееце гэтыя ўмовы для кантакту з намі."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Прэтэнзіі на аўтарскія правы на гэты адрас электроннай пошты будуць ігнаравацца; выкарыстоўвайце форму замест гэтага."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Серверы партнёраў недаступныя з-за закрыццяў хостынгу. Яны павінны хутка зноў запрацаваць."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Членства будзе адпаведна падоўжана."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Не пішыце нам па электроннай пошце, каб <a %(a_request)s>запытаць кнігі</a><br>або невялікія (<10k) <a %(a_upload)s>загрузкі</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Калі задаеце пытанні аб уліковым запісе або ахвяраваннях, дадайце ваш ID уліковага запісу, скрыншоты, квітанцыі, як мага больш інфармацыі. Мы правяраем нашу электронную пошту кожныя 1-2 тыдні, таму не ўключэнне гэтай інфармацыі затрымае вырашэнне."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Паказаць электронную пошту"

#, fuzzy
msgid "page.copyright.title"
msgstr "Форма прэтэнзіі DMCA / аўтарскіх правоў"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Калі ў вас ёсць прэтэнзія DMCA або іншая прэтэнзія на аўтарскія правы, калі ласка, запоўніце гэтую форму як мага дакладней. Калі ў вас узнікнуць праблемы, звяжыцеся з намі па нашым спецыяльным адрасе DMCA: %(email)s. Звярніце ўвагу, што прэтэнзіі, адпраўленыя на гэты адрас па электроннай пошце, не будуць апрацаваны, ён прызначаны толькі для пытанняў. Калі ласка, выкарыстоўвайце форму ніжэй для падачы вашых прэтэнзій."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL-адрасы на архіве Анны (абавязкова). Адзін на радок. Калі ласка, уключайце толькі URL-адрасы, якія апісваюць дакладна тое ж выданне кнігі. Калі вы хочаце падаць прэтэнзію на некалькі кніг або некалькі выданняў, калі ласка, падайце гэтую форму некалькі разоў."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Прэтэнзіі, якія аб'ядноўваюць некалькі кніг або выданняў разам, будуць адхілены."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Ваша імя (абавязкова)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Адрас (абавязкова)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Нумар тэлефона (абавязкова)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Электронная пошта (абавязкова)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Дакладнае апісанне зыходнага матэрыялу (абавязкова)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN-ы зыходнага матэрыялу (калі дастасавальна). Адзін на радок. Калі ласка, уключайце толькі тыя, якія дакладна адпавядаюць выданню, для якога вы паведамляеце прэтэнзію на аўтарскія правы."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>URL-адрасы Open Library</a> зыходнага матэрыялу, адзін на радок. Калі ласка, знайдзіце хвілінку, каб пашукаць ваш зыходны матэрыял у Open Library. Гэта дапаможа нам праверыць вашу прэтэнзію."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL-адрасы зыходнага матэрыялу, адзін на радок (абавязкова). Калі ласка, уключайце як мага больш, каб дапамагчы нам праверыць вашу прэтэнзію (напрыклад, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Заява і подпіс (абавязкова)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Адправіць прэтэнзію"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Дзякуй за падачу вашай прэтэнзіі на аўтарскія правы. Мы разгледзім яе як мага хутчэй. Калі ласка, перазагрузіце старонку, каб падаць яшчэ адну."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце яшчэ раз."

#, fuzzy
msgid "page.datasets.title"
msgstr "Наборы дадзеных"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Калі вы зацікаўлены ў люстраванні гэтага набору дадзеных для <a %(a_archival)s>архівавання</a> або <a %(a_llm)s>навучання LLM</a>, калі ласка, звяжыцеся з намі."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Наша місія — архіваваць усе кнігі ў свеце (а таксама артыкулы, часопісы і г.д.) і зрабіць іх шырока даступнымі. Мы лічым, што ўсе кнігі павінны быць люстраваны шырока, каб забяспечыць рэзервовае капіраванне і ўстойлівасць. Вось чаму мы збіраем файлы з розных крыніц. Некаторыя крыніцы цалкам адкрытыя і могуць быць люстраваны ў масавым парадку (напрыклад, Sci-Hub). Іншыя закрытыя і абароненыя, таму мы спрабуем іх скрабаваць, каб «вызваліць» іх кнігі. Яшчэ іншыя знаходзяцца дзесьці пасярэдзіне."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Усе нашы дадзеныя можна <a %(a_torrents)s>загрузіць праз торэнт</a>, а ўсе нашы метаданыя можна <a %(a_anna_software)s>згенераваць</a> або <a %(a_elasticsearch)s>загрузіць</a> у выглядзе баз дадзеных ElasticSearch і MariaDB. Сырыя дадзеныя можна ўручную даследаваць праз JSON-файлы, такія як <a %(a_dbrecord)s>гэты</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Агляд"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Ніжэй прыведзены кароткі агляд крыніц файлаў на Ганнавай Архіве."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Крыніца"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Памер"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% адлюстравана AA / даступныя торэнты"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Працэнты колькасці файлаў"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Апошняе абнаўленне"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Нефікшн і Фікшн"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] ""
msgstr[1] ""
msgstr[2] "%(count)s файлы"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Праз Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: замарожаны з 2021 года; большасць даступная праз торэнты"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: невялікія дапаўненні з таго часу</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Выключаючы “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Торэнты з мастацкай літаратурай адстаюць (хоць ID ~4-6M не торэнтаваны, бо яны перакрываюцца з нашымі Zlib торэнтамі)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Калекцыя “Кітайская” ў Z-Library, здаецца, такая ж, як і наша калекцыя DuXiu, але з рознымі MD5. Мы выключаем гэтыя файлы з торэнтаў, каб пазбегнуць дублявання, але ўсё роўна паказваем іх у нашым пошукавым індэксе."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Кантраляванае лічбавае пазычанне IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ файлаў даступныя для пошуку."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Агульная колькасць"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Выключаючы дублікаты"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Паколькі ценявыя бібліятэкі часта сінхранізуюць дадзеныя адна з адной, існуе значнае перакрыцце паміж бібліятэкамі. Вось чаму лічбы не складаюцца ў агульную колькасць."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Працэнт “люстранаваных і пасеяных архівам Анны” паказвае, колькі файлаў мы люстраем самі. Мы масава пасеем гэтыя файлы праз торэнты і робім іх даступнымі для прамога спампоўвання праз партнёрскія вэб-сайты."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Бібліятэкі-крыніцы"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Некаторыя бібліятэкі-крыніцы прасоўваюць масавае распаўсюджванне сваіх дадзеных праз торэнты, у той час як іншыя не ахвотна дзеляцца сваёй калекцыяй. У апошнім выпадку, Архіў Анны спрабуе скрабаць іх калекцыі і рабіць іх даступнымі (глядзіце нашу старонку <a %(a_torrents)s>Торэнты</a>). Ёсць таксама прамежкавыя сітуацыі, напрыклад, калі бібліятэкі-крыніцы гатовыя дзяліцца, але не маюць рэсурсаў для гэтага. У такіх выпадках мы таксама спрабуем дапамагчы."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Ніжэй прыведзены агляд таго, як мы ўзаемадзейнічаем з рознымі бібліятэкамі-крыніцамі."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Крыніца"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Файлы"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Штодзённыя <a %(dbdumps)s>дампы базы дадзеных HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Аўтаматызаваныя торэнты для <a %(nonfiction)s>Нон-фікшн</a> і <a %(fiction)s>Фікшн</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(covers)s>торэнтаў вокладак кніг</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub замарозіў новыя файлы з 2021 года."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Дампы метадатаў даступныя <a %(scihub1)s>тут</a> і <a %(scihub2)s>тут</a>, а таксама як частка <a %(libgenli)s>базы дадзеных Libgen.li</a> (якую мы выкарыстоўваем)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Дадзеныя торэнтаў даступныя <a %(scihub1)s>тут</a>, <a %(scihub2)s>тут</a> і <a %(libgenli)s>тут</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Некаторыя новыя файлы <a %(libgenrs)s>дадаюцца</a> у “scimag” Libgen, але не дастаткова, каб апраўдаць новыя торэнты."

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Штоквартальныя <a %(dbdumps)s>дампы базы дадзеных HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Торэнты Нон-фікшн падзяляюцца з Libgen.rs (і люструюцца <a %(libgenli)s>тут</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Архіў Анны і Libgen.li сумесна кіруюць калекцыямі <a %(comics)s>коміксаў</a>, <a %(magazines)s>часопісаў</a>, <a %(standarts)s>стандартных дакументаў</a> і <a %(fiction)s>мастацкай літаратуры (аддзялілася ад Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Іх калекцыя “fiction_rus” (руская мастацкая літаратура) не мае асобных торэнтаў, але пакрываецца торэнтамі ад іншых, і мы захоўваем <a %(fiction_rus)s>люстэрка</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Архіў Анны і Z-Library сумесна кіруюць калекцыяй <a %(metadata)s>метадатаў Z-Library</a> і <a %(files)s>файлаў Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Некаторая метадата даступная праз <a %(openlib)s>дампы базы дадзеных Open Library</a>, але яны не ахопліваюць усю калекцыю IA."

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Няма лёгка даступных дампаў метадатаў для ўсёй іх калекцыі."

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(ia)s>метадатаў IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Файлы даступныя толькі для пазыкі на абмежаванай аснове, з рознымі абмежаваннямі доступу."

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(ia)s>файлаў IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Розныя базы метададзеных, раскіданыя па кітайскім інтэрнэце; хоць часта гэта платныя базы даных"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Няма лёгкадаступных дампаў метададзеных для ўсёй іх калекцыі."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(duxiu)s>метададзеных DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Розныя базы дадзеных файлаў, раскіданыя па кітайскім інтэрнэце; часта платныя базы дадзеных"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Большасць файлаў даступныя толькі з прэміум-акаўнтамі BaiduYun; нізкая хуткасць загрузкі."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(duxiu)s>файлаў DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Розныя меншыя або адзіночныя крыніцы. Мы заклікаем людзей спачатку загружаць у іншыя ценявыя бібліятэкі, але часам у людзей ёсць калекцыі, якія занадта вялікія, каб іншыя маглі іх сартаваць, хоць і не дастаткова вялікія, каб заслугоўваць уласную катэгорыю."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Крыніцы толькі з метаданымі"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Мы таксама ўзбагачаем нашу калекцыю крыніцамі толькі з метаданымі, якія мы можам супаставіць з файламі, напрыклад, выкарыстоўваючы нумары ISBN або іншыя палі. Ніжэй прыведзены агляд гэтых крыніц. Зноў жа, некаторыя з гэтых крыніц цалкам адкрытыя, у той час як для іншых нам даводзіцца іх скрабаць."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Нашым натхненнем для збору метададзеных з'яўляецца мэта Аарона Шварца \"адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі\", для якой ён стварыў <a %(a_openlib)s>Open Library</a>. Гэты праект добра прасунуўся, але наша унікальная пазіцыя дазваляе нам атрымліваць метададзеныя, якія яны не могуць. Яшчэ адным натхненнем было наша жаданне ведаць <a %(a_blog)s>колькі кніг існуе ў свеце</a>, каб мы маглі падлічыць, колькі кніг нам яшчэ трэба захаваць."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Звярніце ўвагу, што ў пошуку метаданых мы паказваем арыгінальныя запісы. Мы не аб'ядноўваем запісы."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Апошняе абнаўленне"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Штомесячныя <a %(dbdumps)s>дампы базы дадзеных</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Няма магчымасці атрымаць непасрэдна ў вялікім аб'ёме, абаронена ад скрапінгу"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(worldcat)s>метададзеных OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Аб'яднаная база дадзеных"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Мы аб'ядноўваем усе вышэйзгаданыя крыніцы ў адну аб'яднаную базу дадзеных, якую мы выкарыстоўваем для абслугоўвання гэтага сайта. Гэтая аб'яднаная база дадзеных не даступная непасрэдна, але паколькі Архіў Анны цалкам з адкрытым зыходным кодам, яе можна даволі лёгка <a %(a_generated)s>стварыць</a> або <a %(a_downloaded)s>спампаваць</a> у выглядзе баз дадзеных ElasticSearch і MariaDB. Скрыпты на гэтай старонцы аўтаматычна спампуюць усе неабходныя метаданыя з вышэйзгаданых крыніц."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Калі вы хочаце даследаваць нашы дадзеныя перад тым, як запускаць гэтыя скрыпты лакальна, вы можаце паглядзець нашы файлы JSON, якія спасылаюцца на іншыя файлы JSON. <a %(a_json)s>Гэты файл</a> з'яўляецца добрым пачаткам."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Адаптавана з нашага <a %(a_href)s>блога</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> — гэта велізарная база дадзеных адсканаваных кніг, створаная <a %(superstar_link)s>SuperStar Digital Library Group</a>. Большасць з іх — гэта акадэмічныя кнігі, адсканаваныя для таго, каб зрабіць іх даступнымі ў лічбавым фармаце для ўніверсітэтаў і бібліятэк. Для нашай англамоўнай аўдыторыі <a %(princeton_link)s>Прынстан</a> і <a %(uw_link)s>Вашынгтонскі ўніверсітэт</a> маюць добрыя агляды. Таксама ёсць выдатны артыкул, які дае больш падрабязную інфармацыю: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Кнігі з Duxiu даўно піратуюцца ў кітайскім інтэрнэце. Звычайна яны прадаюцца перапрадаўцамі менш чым за долар. Яны звычайна распаўсюджваюцца з дапамогай кітайскага аналага Google Drive, які часта ўзломваюць для павелічэння аб'ёму сховішча. Некаторыя тэхнічныя падрабязнасці можна знайсці <a %(link1)s>тут</a> і <a %(link2)s>тут</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Хоць кнігі былі паўпублічна распаўсюджаны, іх даволі цяжка атрымаць у вялікай колькасці. Мы мелі гэта высока ў нашым спісе задач і выдзелілі некалькі месяцаў поўнай занятасці для гэтага. Аднак у канцы 2023 года да нас звярнуўся неверагодны, дзіўны і таленавіты валанцёр, які паведаміў, што ўжо выканаў усю гэтую працу — за вялікія выдаткі. Яны падзяліліся з намі поўнай калекцыяй, не чакаючы нічога ўзамен, акрамя гарантыі доўгатэрміновага захавання. Сапраўды выдатна."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Рэсурсы"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Агульная колькасць файлаў: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Агульны памер файлаў: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Файлы, адлюстраваныя архівам Анны: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Апошняе абнаўленне: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Торэнты ад архіва Анны"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Прыклад запісу ў архіве Анны"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Наш блог пра гэтыя дадзеныя"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Скрыпты для імпарту метаданых"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Фармат кантэйнераў архіва Анны"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Больш інфармацыі ад нашых валанцёраў (сырыя нататкі):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "Кантраляванае лічбавае пазычанне IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Гэты набор дадзеных цесна звязаны з <a %(a_datasets_openlib)s>наборам дадзеных Open Library</a>. Ён утрымлівае скрэйп усіх метададзеных і вялікую частку файлаў з Кантраляванай лічбавай бібліятэкі IA. Абнаўленні выпускаюцца ў <a %(a_aac)s>фармаце кантэйнераў архіва Анны</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Гэтыя запісы спасылаюцца непасрэдна на набор дадзеных Open Library, але таксама ўтрымліваюць запісы, якіх няма ў Open Library. У нас таксама ёсць шэраг файлаў дадзеных, сабраных членамі супольнасці на працягу многіх гадоў."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Калекцыя складаецца з двух частак. Вам патрэбны абедзве часткі, каб атрымаць усе дадзеныя (акрамя састарэлых торэнтаў, якія перакрэслены на старонцы торэнтаў)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "наш першы выпуск, да таго як мы стандартызавалі фармат <a %(a_aac)s>кантэйнераў архіва Анны (AAC)</a>. Утрымлівае метаданыя (у фарматах json і xml), pdf-файлы (з лічбавых сістэм пазыкі acsm і lcpdf) і мініяцюры вокладак."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "інкрэментальныя новыя выпускі, выкарыстоўваючы AAC. Утрымліваюць толькі метаданыя з часовымі меткамі пасля 2023-01-01, бо астатняе ўжо пакрыта “ia”. Таксама ўсе pdf-файлы, на гэты раз з сістэм пазыкі acsm і “bookreader” (вэб-чытач IA). Нягледзячы на тое, што назва не зусім дакладная, мы ўсё роўна ўключаем файлы bookreader у калекцыю ia2_acsmpdf_files, бо яны ўзаемавыключальныя."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Асноўны %(source)s сайт"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Лічбавая бібліятэка пазыкі"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Дакументацыя метаданых (большасць палёў)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Інфармацыя пра краіну ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Міжнароднае агенцтва ISBN рэгулярна выпускае дыяпазоны, якія яно выдзеліла нацыянальным агенцтвам ISBN. З гэтага мы можам вызначыць, да якой краіны, рэгіёна або моўнай групы належыць гэты ISBN. У цяперашні час мы выкарыстоўваем гэтыя дадзеныя ўскосна, праз бібліятэку Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Рэсурсы"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Апошняе абнаўленне: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Вэб-сайт ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Метаданыя"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Для гісторыі розных форкаў Library Genesis глядзіце старонку <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li змяшчае большасць таго ж кантэнту і метаданых, што і Libgen.rs, але мае некаторыя дадатковыя калекцыі, а менавіта коміксы, часопісы і стандартныя дакументы. Ён таксама інтэграваў <a %(a_scihub)s>Sci-Hub</a> у свае метаданыя і пошукавую сістэму, якую мы выкарыстоўваем для нашай базы дадзеных."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Метаданыя для гэтай бібліятэкі даступныя бясплатна <a %(a_libgen_li)s>на libgen.li</a>. Аднак гэты сервер павольны і не падтрымлівае аднаўленне перапыненых злучэнняў. Тыя ж файлы таксама даступныя на <a %(a_ftp)s>FTP-серверы</a>, які працуе лепш."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Торэнты даступныя для большасці дадатковага кантэнту, асабліва торэнты для коміксаў, часопісаў і стандартных дакументаў былі выпушчаны ў супрацоўніцтве з Архівам Анны."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Калекцыя мастацкай літаратуры мае ўласныя торэнты (аддзялілася ад <a %(a_href)s>Libgen.rs</a>), пачынаючы з %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Паводле адміністратара Libgen.li, калекцыя “fiction_rus” (руская мастацкая літаратура) павінна быць пакрыта рэгулярна выпушчанымі торэнтамі з <a %(a_booktracker)s>booktracker.org</a>, асабліва торэнтамі <a %(a_flibusta)s>flibusta</a> і <a %(a_librusec)s>lib.rus.ec</a> (якія мы люструем <a %(a_torrents)s>тут</a>, хоць мы яшчэ не ўсталявалі, якія торэнты адпавядаюць якім файлам)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Статыстыка для ўсіх калекцый даступная <a %(a_href)s>на сайце libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Нехудожняя літаратура таксама, здаецца, адхілілася, але без новых торэнтаў. Падобна, што гэта адбылося з пачатку 2022 года, хоць мы гэтага не правяралі."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Пэўныя дыяпазоны без торэнтаў (такія як дыяпазоны мастацкай літаратуры f_3463000 да f_4260000) верагодна з'яўляюцца файламі Z-Library (ці іншымі дублямі), хоць мы можам захацець зрабіць дэдуплікацыю і стварыць торэнты для унікальных файлаў lgli у гэтых дыяпазонах."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Звярніце ўвагу, што торэнт-файлы, якія адносяцца да “libgen.is”, з'яўляюцца люстэркамі <a %(a_libgen)s>Libgen.rs</a> (“.is” — гэта іншы дамен, які выкарыстоўваецца Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Карысная рэсурс для выкарыстання метададзеных — <a %(a_href)s>гэтая старонка</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Торэнты з мастацкай літаратурай на Архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Торэнты з коміксамі на Архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Торэнты з часопісамі на Архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Торэнты стандартных дакументаў у Архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Торэнты рускай мастацкай літаратуры ў Архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Метададзеныя"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Метададзеныя праз FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Інфармацыя пра палі метададзеных"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Люстэрка іншых торэнтаў (і унікальныя торэнты з мастацкай літаратурай і коміксамі)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Форум для абмеркавання"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Наш блог-пост пра выпуск коміксаў"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Кароткая гісторыя розных адгалінаванняў Library Genesis (або “Libgen”) заключаецца ў тым, што з цягам часу розныя людзі, якія ўдзельнічалі ў Library Genesis, пасварыліся і пайшлі сваімі шляхамі."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Версія “.fun” была створана першапачатковым заснавальнікам. Яна перапрацоўваецца на карысць новай, больш размеркаванай версіі."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Версія “.rs” мае вельмі падобныя дадзеныя і найбольш паслядоўна выпускае сваю калекцыю ў выглядзе масавых торэнтаў. Яна прыкладна падзелена на раздзелы “мастацкая літаратура” і “не-мастацкая літаратура”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Першапачаткова на “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Версія “.li”</a> мае велізарную калекцыю коміксаў, а таксама іншага кантэнту, які пакуль не даступны для масавага спампоўвання праз торэнты. Яна мае асобную калекцыю торэнтаў з мастацкімі кнігамі і ўтрымлівае метададзеныя <a %(a_scihub)s>Sci-Hub</a> у сваёй базе дадзеных."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Паводле гэтага <a %(a_mhut)s>паведамлення на форуме</a>, Libgen.li першапачаткова размяшчаўся на “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> у пэўным сэнсе таксама з'яўляецца адгалінаваннем Library Genesis, хоць яны выкарыстоўвалі іншую назву для свайго праекта."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Гэтая старонка пра версію “.rs”. Яна вядомая тым, што паслядоўна публікуе як свае метададзеныя, так і поўны змест свайго каталога кніг. Яе калекцыя кніг падзелена на раздзелы мастацкай і не-мастацкай літаратуры."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Карысная рэсурс для выкарыстання метададзеных — <a %(a_metadata)s>гэтая старонка</a> (блокуе дыяпазоны IP, можа спатрэбіцца VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Па стане на 2024-03, новыя торэнты размяшчаюцца ў <a %(a_href)s>гэтым форуме</a> (блокіруе дыяпазоны IP, можа спатрэбіцца VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Нефікшн торэнты на архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Фікшн торэнты на архіве Анны"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Метаданыя"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Інфармацыя пра метаданыя Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Нефікшн торэнты"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Фікшн торэнты"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Форум абмеркаванняў"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Торэнты ад архіва Анны (вокладкі кніг)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Наш блог пра выпуск вокладак кніг"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis вядомы тым, што ўжо шчодра робіць свае дадзеныя даступнымі ў вялікіх аб'ёмах праз торэнты. Наша калекцыя Libgen складаецца з дадатковых дадзеных, якія яны не выпускаюць непасрэдна, у партнёрстве з імі. Вялікі дзякуй усім, хто ўдзельнічае ў Library Genesis за супрацоўніцтва з намі!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Выпуск 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Гэты <a %(blog_post)s>першы выпуск</a> даволі невялікі: каля 300 ГБ вокладак кніг з форка Libgen.rs, як фікшн, так і нефікшн. Яны арганізаваны такім жа чынам, як яны з'яўляюцца на libgen.rs, напрыклад:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s для нефікшн кнігі."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s для фікшн кнігі."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Як і ў выпадку з калекцыяй Z-Library, мы паклалі іх усе ў вялікі .tar файл, які можна змантраваць з дапамогай <a %(a_ratarmount)s>ratarmount</a>, калі вы хочаце непасрэдна абслугоўваць файлы."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> — гэта ўласная база дадзеных некамерцыйнай арганізацыі <a %(a_oclc)s>OCLC</a>, якая збірае метаданыя з бібліятэк па ўсім свеце. Гэта, верагодна, самая вялікая калекцыя бібліятэчных метаданых у свеце."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Кастрычнік 2023, пачатковы выпуск:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "У кастрычніку 2023 года мы <a %(a_scrape)s>выпусцілі</a> комплексны скрэйп базы дадзеных OCLC (WorldCat) у фармаце <a %(a_aac)s>кантэйнераў Аннавай Архівы</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Торэнты ад Аннавай Архівы"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Наш блог пра гэтыя дадзеныя"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library — гэта праект з адкрытым зыходным кодам ад Internet Archive для каталогізацыі кожнай кнігі ў свеце. Ён мае адну з найбуйнейшых у свеце аперацый па сканаванні кніг і мае шмат кніг, даступных для лічбавага пазычання. Яго каталог метаданых кніг даступны для бясплатнага спампоўвання і ўключаны ў архіў Анны (хоць у цяперашні час не ў пошуку, за выключэннем выпадкаў, калі вы яўна шукаеце ідэнтыфікатар Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Метаданыя"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Выпуск 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Гэта дамп вялікай колькасці запытаў да isbndb.com у верасні 2022 года. Мы спрабавалі ахапіць усе дыяпазоны ISBN. Гэта каля 30,9 мільёна запісаў. На іх сайце яны сцвярджаюць, што ў іх на самай справе ёсць 32,6 мільёна запісаў, таму мы маглі неяк прапусціць некаторыя, або <em>яны</em> маглі зрабіць нешта няправільна."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Адказы JSON амаль сырыя з іх сервера. Адна з праблем якасці дадзеных, якую мы заўважылі, заключаецца ў тым, што для нумароў ISBN-13, якія пачынаюцца з іншага прэфікса, чым «978-», яны ўсё роўна ўключаюць поле «isbn», якое проста з'яўляецца нумарам ISBN-13 з першымі 3 лічбамі, адрэзанымі (і пералічанай кантрольнай лічбай). Гэта відавочна няправільна, але яны, здаецца, робяць гэта так, таму мы не змянялі гэта."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Яшчэ адна патэнцыйная праблема, з якой вы можаце сутыкнуцца, заключаецца ў тым, што поле «isbn13» мае дублікаты, таму вы не можаце выкарыстоўваць яго ў якасці асноўнага ключа ў базе дадзеных. Поля «isbn13»+«isbn» у спалучэнні, здаецца, унікальныя."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Для атрымання інфармацыі пра Sci-Hub, калі ласка, звярніцеся да яго <a %(a_scihub)s>афіцыйнага сайта</a>, <a %(a_wikipedia)s>старонкі ў Вікіпедыі</a> і гэтага <a %(a_radiolab)s>падкаста</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Звярніце ўвагу, што Sci-Hub быў <a %(a_reddit)s>замарожаны з 2021 года</a>. Ён быў замарожаны раней, але ў 2021 годзе было дададзена некалькі мільёнаў артыкулаў. Тым не менш, некаторая абмежаваная колькасць артыкулаў дадаецца ў калекцыі “scimag” Libgen, але гэтага недастаткова для стварэння новых масавых торэнтаў."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Мы выкарыстоўваем метаданыя Sci-Hub, прадастаўленыя <a %(a_libgen_li)s>Libgen.li</a> у яго калекцыі “scimag”. Мы таксама выкарыстоўваем набор дадзеных <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Звярніце ўвагу, што торэнты “smarch” <a %(a_smarch)s>састарэлі</a> і таму не ўключаны ў наш спіс торэнтаў."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Торэнты на Аннавай Архіве"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Метаданыя і торэнты"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Торэнты на Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Торэнты на Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Абнаўленні на Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Старонка ў Вікіпедыі"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Падкаст"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Загрузкі ў Архіў Анны"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Агляд з <a %(a1)s>старонкі з наборамі дадзеных</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Розныя меншыя або адзіночныя крыніцы. Мы заклікаем людзей спачатку загружаць у іншыя ценявыя бібліятэкі, але часам у людзей ёсць калекцыі, якія занадта вялікія, каб іншыя маглі іх сартаваць, хоць і не дастаткова вялікія, каб заслугоўваць уласную катэгорыю."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "\"Загружаная\" калекцыя падзелена на меншыя падкалекцыі, якія пазначаны ў AACIDs і назвах торэнтаў. Усе падкалекцыі спачатку былі дэдупікаваны ў параўнанні з асноўнай калекцыяй, хоць метададзеныя \"upload_records\" JSON файлы ўсё яшчэ ўтрымліваюць шмат спасылак на арыгінальныя файлы. Накніжныя файлы таксама былі выдалены з большасці падкалекцый і звычайна <em>не</em> пазначаны ў \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Многія падкалекцыі самі складаюцца з пад-падкалекцый (напрыклад, з розных арыгінальных крыніц), якія прадстаўлены як каталогі ў палях \"filepath\"."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Падкалекцыі:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Падкалекцыя"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Заўвагі"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "прагляд"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "пошук"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "З <a %(a_href)s>aaaaarg.fail</a>. Выглядае даволі поўным. Ад нашага валанцёра “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "З <a %(a_href)s><q>ACM Digital Library 2020</q></a> торэнта. Мае даволі вялікае перакрыцце з існуючымі калекцыямі артыкулаў, але вельмі мала супадзенняў MD5, таму мы вырашылі захаваць яго цалкам."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Скрапінг <q>iRead eBooks</q> (= фанетычна <q>ai rit i-books</q>; airitibooks.com), выкананы валанцёрам <q>j</q>. Адпавядае metadata <q>airitibooks</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "З калекцыі <a %(a1)s><q>Бібліятэка Александрыя</q></a>. Часткова з арыгінальнай крыніцы, часткова з the-eye.eu, часткова з іншых люстэркаў."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "З прыватнага сайта торэнтаў кніг, <a %(a_href)s>Bibliotik</a> (часта называецца “Bib”), кнігі якога былі аб'яднаны ў торэнты па назвах (A.torrent, B.torrent) і распаўсюджваліся праз the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Ад нашага валанцёра “bpb9v”. Для атрымання дадатковай інфармацыі пра <a %(a_href)s>CADAL</a>, глядзіце нататкі на нашай <a %(a_duxiu)s>старонцы набора дадзеных DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Больш ад нашага валанцёра “bpb9v”, у асноўным файлы DuXiu, а таксама тэчка “WenQu” і “SuperStar_Journals” (SuperStar — гэта кампанія, якая стаіць за DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Ад нашага валанцёра “cgiym”, кітайскія тэксты з розных крыніц (прадстаўлены як падкаталогі), уключаючы <a %(a_href)s>China Machine Press</a> (буйное кітайскае выдавецтва)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Некітайскія калекцыі (прадстаўлены як падкаталогі) ад нашага валанцёра “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Скрапінг кніг пра кітайскую архітэктуру, выкананы валанцёрам <q>cm</q>: <q>Я атрымаў гэта, выкарыстоўваючы ўразлівасць сеткі ў выдавецтве, але гэтая дзірка ўжо закрыта</q>. Адпавядае metadata <q>chinese_architecture</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Кнігі з акадэмічнага выдавецтва <a %(a_href)s>De Gruyter</a>, сабраныя з некалькіх буйных торэнтаў."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Скрапінг <a %(a_href)s>docer.pl</a>, польскага сайта абмену файламі, які засяроджваецца на кнігах і іншых пісьмовых працах. Скрапінг быў праведзены ў канцы 2023 года валанцёрам “p”. У нас няма добрых метададзеных з арыгінальнага сайта (нават пашырэнняў файлаў), але мы адфільтравалі файлы, падобныя на кнігі, і часта маглі здабыць метададзеныя з саміх файлаў."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, непасрэдна з DuXiu, сабраныя валанцёрам “w”. Толькі нядаўнія кнігі DuXiu даступныя непасрэдна праз электронныя кнігі, таму большасць з іх павінны быць нядаўнімі."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Астатнія файлы DuXiu ад валанцёра “m”, якія не былі ў фармаце DuXiu PDG (асноўны <a %(a_href)s>набор дадзеных DuXiu</a>). Сабраныя з многіх арыгінальных крыніц, на жаль, без захавання гэтых крыніц у шляху файла."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Скрапінг эратычных кніг, выкананы валанцёрам <q>do no harm</q>. Адпавядае metadata <q>hentai</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Калекцыя, скрапінг з японскага выдавецтва мангі валанцёрам “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Выбраныя судовыя архівы Лунцюань</a>, прадастаўленыя валанцёрам “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Скрапінг <a %(a_href)s>magzdb.org</a>, саюзніка Library Genesis (ён звязаны на галоўнай старонцы libgen.rs), але які не хацеў прадастаўляць свае файлы непасрэдна. Атрыманая валанцёрам “p” у канцы 2023 года."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Розныя невялікія загрузкі, занадта малыя для ўласнай падкалекцыі, але прадстаўлены як каталогі."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Электронныя кнігі з AvaxHome, расійскага сайта для абмену файламі."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Архіў газет і часопісаў. Адпавядае metadata <q>newsarch_magz</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Скрапінг <a %(a1)s>Цэнтра дакументацыі па філасофіі</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Калекцыя валанцёра “o”, які збіраў польскія кнігі непасрэдна з арыгінальных рэлізных (“scene”) сайтаў."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Аб'яднаныя калекцыі <a %(a_href)s>shuge.org</a> валанцёраў “cgiym” і “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Імператарская бібліятэка Трантора”</a> (названая ў гонар выдуманай бібліятэкі), скрапінг у 2022 годзе валанцёрам “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Пад-пад-калекцыі (прадстаўлены як каталогі) ад валанцёра “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (ад <a %(a_sikuquanshu)s>Dizhi(迪志)</a> на Тайвані), mebook (mebook.cc, 我的小书屋, мая маленькая кніжная пакой — woz9ts: “Гэты сайт у асноўным засяроджваецца на абмене файламі электронных кніг высокай якасці, некаторыя з якіх былі набраны самім уладальнікам. Уладальнік быў <a %(a_arrested)s>арыштаваны</a> у 2019 годзе, і нехта зрабіў калекцыю файлаў, якімі ён падзяліўся.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Астатнія файлы DuXiu ад валанцёра «woz9ts», якія не былі ў фармаце DuXiu уласнага PDG (яшчэ трэба пераўтварыць у PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Торэнты ад Архіва Анны"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Скрапінг Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library мае свае карані ў супольнасці <a %(a_href)s>Library Genesis</a> і першапачаткова выкарыстоўвала іх дадзеныя. З таго часу яна значна прафесіяналізавалася і мае значна больш сучасны інтэрфейс. Таму яны могуць атрымліваць значна больш ахвяраванняў, як грашовых для паляпшэння свайго сайта, так і ахвяраванняў новых кніг. Яны назапасілі вялікую калекцыю ў дадатак да Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Абнаўленне на люты 2023 года."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "У канцы 2022 года меркаваныя заснавальнікі Z-Library былі арыштаваныя, а дамены канфіскаваныя ўладамі Злучаных Штатаў. З таго часу сайт павольна вяртаецца ў інтэрнэт. Невядома, хто ў цяперашні час ім кіруе."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Калекцыя складаецца з трох частак. Арыгінальныя апісанні старонак для першых двух частак захаваны ніжэй. Вам патрэбны ўсе тры часткі, каб атрымаць усе дадзеныя (за выключэннем замяшчаных торэнтаў, якія перакрэслены на старонцы торэнтаў)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: наш першы выпуск. Гэта быў самы першы выпуск таго, што тады называлася «Пірацкае Люстэрка Бібліятэкі» («pilimi»)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: другі выпуск, на гэты раз з усімі файламі, загорнутымі ў .tar файлы."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: інкрэментальныя новыя выпускі, выкарыстоўваючы <a %(a_href)s>фармат кантэйнераў Архіва Анны (AAC)</a>, цяпер выпушчаныя ў супрацоўніцтве з камандай Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Торэнты ад Архіва Анны (метаданыя + змест)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Прыклад запісу ў Архіве Анны (арыгінальная калекцыя)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Прыклад запісу ў Архіве Анны (калекцыя «zlib3»)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Асноўны сайт"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor дамен"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Паведамленне ў блогу пра Выпуск 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Паведамленне ў блогу пра Выпуск 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Выпускі Zlib (арыгінальныя апісанні старонак)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Выпуск 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Першапачатковае люстэрка было старанна атрымана на працягу 2021 і 2022 гадоў. На дадзены момант яно крыху састарэла: яно адлюстроўвае стан калекцыі ў чэрвені 2021 года. Мы абновім гэта ў будучыні. Зараз мы сканцэнтраваны на выпуску гэтага першага выпуску."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Паколькі Library Genesis ужо захаваны з дапамогай публічных торэнтаў і ўключаны ў Z-Library, мы правялі базавую дэдуплікацыю супраць Library Genesis у чэрвені 2022 года. Для гэтага мы выкарыстоўвалі MD5-хэшы. Верагодна, у бібліятэцы ёсць значна больш дублікатаў, такіх як некалькі фарматаў файлаў з адной і той жа кнігай. Гэта цяжка дакладна выявіць, таму мы гэтага не робім. Пасля дэдуплікацыі ў нас засталося больш за 2 мільёны файлаў, агульным аб'ёмам крыху менш за 7 ТБ."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Калекцыя складаецца з двух частак: дампа метададзеных MySQL «.sql.gz» і 72 торэнт-файлаў аб'ёмам каля 50-100 ГБ кожны. Метададзеныя ўтрымліваюць дадзеныя, як паведамляецца на сайце Z-Library (назва, аўтар, апісанне, тып файла), а таксама фактычны памер файла і md5sum, якія мы назіралі, бо часам яны не супадаюць. Здаецца, ёсць дыяпазоны файлаў, для якіх Z-Library мае няправільныя метададзеныя. У некаторых асобных выпадках мы таксама маглі няправільна загрузіць файлы, што мы паспрабуем выявіць і выправіць у будучыні."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Вялікія торэнт-файлы ўтрымліваюць фактычныя дадзеныя кніг, з ідэнтыфікатарам Z-Library у якасці імя файла. Пашырэнні файлаў можна аднавіць з дапамогай дампа метададзеных."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Калекцыя ўключае змешаны кантэнт, як нон-фікшн, так і фікшн (не падзелены, як у Library Genesis). Якасць таксама вельмі розная."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Гэта першае выданне цяпер цалкам даступна. Звярніце ўвагу, што торэнт-файлы даступныя толькі праз наш Tor-люстэрка."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Выпуск 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Мы атрымалі ўсе кнігі, якія былі дададзены ў Z-Library паміж нашым апошнім люстэркам і жніўнем 2022 года. Мы таксама вярнуліся і сабралі некаторыя кнігі, якія прапусцілі ў першы раз. У цэлым, гэтая новая калекцыя складае каля 24 ТБ. Зноў жа, гэтая калекцыя дэдуплікаваная супраць Library Genesis, паколькі для гэтай калекцыі ўжо даступныя торэнты."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Дадзеныя арганізаваны аналагічна першаму выпуску. Існуе дамп метададзеных MySQL «.sql.gz», які таксама ўключае ўсе метададзеныя з першага выпуску, тым самым замяняючы яго. Мы таксама дадалі некалькі новых слупкоў:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ці ёсць гэты файл ужо ў Library Genesis, у калекцыі нон-фікшн або фікшн (супадзенне па md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: у якім торэнце знаходзіцца гэты файл."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: усталёўваецца, калі мы не змаглі загрузіць кнігу."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Мы згадвалі пра гэта ў мінулы раз, але проста для ўдакладнення: «filename» і «md5» - гэта фактычныя ўласцівасці файла, у той час як «filename_reported» і «md5_reported» - гэта тое, што мы сабралі з Z-Library. Часам гэтыя два не супадаюць, таму мы ўключылі абодва."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Для гэтага выпуску мы змянілі кадыроўку на «utf8mb4_unicode_ci», якая павінна быць сумяшчальная са старымі версіямі MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Файлы дадзеных падобныя на мінулы раз, хоць яны значна большыя. Мы проста не маглі сабе дазволіць ствараць мноства меншых торэнт-файлаў. «pilimi-zlib2-0-14679999-extra.torrent» утрымлівае ўсе файлы, якія мы прапусцілі ў мінулым выпуску, у той час як іншыя торэнты ўключаюць новыя дыяпазоны ідэнтыфікатараў. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Абнаўленне %(date)s:</strong> Мы зрабілі большасць нашых торэнтаў занадта вялікімі, што выклікала праблемы ў торэнт-кліентаў. Мы выдалілі іх і выпусцілі новыя торэнты."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Абнаўленне %(date)s:</strong> Файлаў усё яшчэ было занадта шмат, таму мы загарнулі іх у tar-файлы і зноў выпусцілі новыя торэнты."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Дадатак да выпуску 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Гэта адзіны дадатковы торэнт-файл. Ён не ўтрымлівае ніякай новай інфармацыі, але ў ім ёсць некаторыя дадзеныя, якія могуць заняць некаторы час для вылічэння. Гэта робіць яго зручным, бо загрузка гэтага торэнта часта хутчэйшая, чым вылічэнне з нуля. У прыватнасці, ён утрымлівае індэксы SQLite для tar-файлаў, для выкарыстання з <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Часта задаваныя пытанні (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Што такое Архіў Анны?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Архіў Анны</span> з'яўляецца некамерцыйным праектам з двума мэтамі:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Захаванне:</strong> Рэзервовае капіраванне ўсіх ведаў і культуры чалавецтва.</li><li><strong>Доступ:</strong> Забеспячэнне доступу да гэтых ведаў і культуры для ўсіх у свеце.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Увесь наш <a %(a_code)s>код</a> і <a %(a_datasets)s>дадзеныя</a> цалкам з адкрытым зыходным кодам."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Захаванне"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Мы захоўваем кнігі, артыкулы, коміксы, часопісы і многае іншае, збіраючы гэтыя матэрыялы з розных <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ценявых бібліятэк</a>, афіцыйных бібліятэк і іншых калекцый у адным месцы. Усе гэтыя дадзеныя захоўваюцца назаўсёды, бо іх лёгка дубляваць у вялікіх аб'ёмах — з дапамогай торэнтаў — што прыводзіць да шматлікіх копій па ўсім свеце. Некаторыя ценявыя бібліятэкі ўжо робяць гэта самі (напрыклад, Sci-Hub, Library Genesis), у той час як Архіў Ганны «вызваляе» іншыя бібліятэкі, якія не прапануюць масавае распаўсюджванне (напрыклад, Z-Library) або зусім не з'яўляюцца ценявымі бібліятэкамі (напрыклад, Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Гэта шырокае распаўсюджванне ў спалучэнні з адкрытым зыходным кодам робіць наш сайт устойлівым да выдаленняў і забяспечвае доўгатэрміновае захаванне ведаў і культуры чалавецтва. Даведайцеся больш пра <a href=\"/datasets\">нашы наборы дадзеных</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Мы ацэньваем, што захавалі каля <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% сусветных кніг</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Доступ"

#, fuzzy
msgid "page.home.access.text"
msgstr "Мы працуем з партнёрамі, каб зрабіць нашы калекцыі лёгка і бясплатна даступнымі для ўсіх. Мы верым, што кожны мае права на калектыўную мудрасць чалавецтва. І <a %(a_search)s>не за кошт аўтараў</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Гадзінныя загрузкі за апошнія 30 дзён. Сярэдняе за гадзіну: %(hourly)s. Сярэдняе за дзень: %(daily)s."

msgid "page.about.text2"
msgstr "Мы верым, што вопыт пакаленняў павінен быць надзейна захаваным і даступным кожнаму. Ствараючы гэтую пошукавую сістэму, мы сабралі разам гіганцкія інфармацыйныя творы, створаныя ў выніку карпатлівай працы распрацоўшчыкаў ценявых бібліятэк. І мы спадзяемся, што наша праца будзе працягам іх намаганняў па захаванню інфармацыі і яе данясення да людзей."

msgid "page.about.text3"
msgstr "Мы публікуем навіны пра нашу працу ў <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> і на <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Калі ў вас ёсць пытанні ці вы хочаце падзяліцца вопытам карыстання сайтам, адпраўце нам ліст на %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Як я магу дапамагчы?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Сачыце за намі на <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> або <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Распавядайце пра Anna’s Archive у Twitter, Reddit, Tiktok, Instagram, у мясцовай кавярні або бібліятэцы, ці дзе заўгодна! Мы не верым у абмежаванні доступу — калі нас выдаляць, мы проста з'явімся ў іншым месцы, бо ўвесь наш код і дадзеныя цалкам адкрытыя.</li><li>3. Калі ў вас ёсць магчымасць, разгледзьце магчымасць <a href=\"/donate\">ахвяравання</a>.</li><li>4. Дапамажыце <a href=\"https://translate.annas-software.org/\">перакласці</a> наш сайт на розныя мовы.</li><li>5. Калі вы інжынер-праграміст, разгледзьце магчымасць унесці свой уклад у наш <a href=\"https://annas-software.org/\">адкрыты код</a> або раздаваць нашы <a href=\"/datasets\">торэнты</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Цяпер у нас таксама ёсць сінхранізаваны канал Matrix на %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Калі вы даследчык бяспекі, мы можам выкарыстоўваць вашы навыкі як для атакі, так і для абароны. Праверце нашу старонку <a %(a_security)s>Бяспека</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Мы шукаем экспертаў па плацяжах для ананімных гандляроў. Ці можаце вы дапамагчы нам дадаць больш зручныя спосабы ахвяравання? PayPal, WeChat, падарункавыя карты. Калі ведаеце каго-небудзь, калі ласка, звяжыцеся з намі."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Мы заўсёды шукаем больш магутнасці сервера."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Вы можаце дапамагчы, паведамляючы пра праблемы з файламі, пакідаючы каментары і ствараючы спісы прама на гэтым сайце. Вы таксама можаце дапамагчы, <a %(a_upload)s>загружаючы больш кніг</a>, або выпраўляючы праблемы з файламі ці фарматаваннем існуючых кніг."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Стварыце або дапамажыце падтрымліваць старонку Вікіпедыі для архіва Анны на вашай мове."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Мы шукаем магчымасці размясціць невялікія, стыльныя рэкламы. Калі вы хочаце рэкламаваць на архіве Анны, калі ласка, дайце нам ведаць."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Мы былі б рады, калі б людзі наладзілі <a %(a_mirrors)s>люстэркі</a>, і мы фінансава падтрымаем гэта."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Для больш падрабязнай інфармацыі аб тым, як стаць валанцёрам, глядзіце нашу старонку <a %(a_volunteering)s>Валанцёрства і ўзнагароды</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Чаму хуткасць загрузкі такая нізкая?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Мы літаральна не маем дастаткова рэсурсаў, каб даць усім у свеце хуткасныя загрузкі, як бы нам гэтага не хацелася. Калі багаты дабрадзей захоча дапамагчы і забяспечыць гэта для нас, гэта было б неверагодна, але да таго часу мы стараемся з усіх сіл. Мы некамерцыйны праект, які ледзь можа існаваць за кошт ахвяраванняў."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Вось чаму мы рэалізавалі дзве сістэмы для бясплатных загрузак з нашымі партнёрамі: агульныя серверы з павольнымі загрузкамі і крыху хутчэйшыя серверы з чаргой (каб зменшыць колькасць людзей, якія загружаюць адначасова)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "У нас таксама ёсць <a %(a_verification)s>верыфікацыя браўзера</a> для нашых павольных загрузак, таму што ў адваротным выпадку боты і скраперы будуць злоўжываць імі, робячы рэчы яшчэ больш павольнымі для законных карыстальнікаў."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Звярніце ўвагу, што пры выкарыстанні Tor Browser вам можа спатрэбіцца наладзіць параметры бяспекі. На самым нізкім з варыянтаў, які называецца «Стандартны», выклік Cloudflare turnstile праходзіць паспяхова. На больш высокіх варыянтах, якія называюцца «Бяспечней» і «Самы бяспечны», выклік не праходзіць."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Для вялікіх файлаў часам павольныя загрузкі могуць перарывацца ў сярэдзіне. Мы рэкамендуем выкарыстоўваць менеджар загрузак (напрыклад, JDownloader), каб аўтаматычна аднаўляць вялікія загрузкі."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Частыя пытанні па ахвяраваннях"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Ці аўтаматычна абнаўляюцца сяброўствы?</div> Сяброўствы <strong>не</strong> абнаўляюцца аўтаматычна. Вы можаце далучыцца на столькі часу, колькі хочаце."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Ці магу я абнавіць сваё сяброўства або атрымаць некалькі сяброўстваў?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Ці ёсць у вас іншыя спосабы аплаты?</div> у цяперашні час няма. Многія людзі не хочуць, каб падобныя архівы існавалі, таму мы павінны быць асцярожныя. Калі вы можаце дапамагчы нам бяспечна наладзіць іншыя (больш зручныя) спосабы аплаты, калі ласка, звяжыцеся з намі па адрасе %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Што азначаюць дыяпазоны за месяц?</div> Вы можаце дасягнуць ніжняй мяжы дыяпазону, ужыўшы ўсе зніжкі, напрыклад, выбраўшы перыяд больш за месяц."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>На што вы траціце ахвяраванні?</div> 100%% ідзе на захаванне і забеспячэнне доступу да сусветных ведаў і культуры. У цяперашні час мы ў асноўным трацім іх на серверы, захоўванне і прапускную здольнасць. Ніводная капейка не ідзе асабіста камусьці з каманды."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Ці магу я адправіць вялікую суму?</div> Так, вядома! Калі вы збіраецеся адправіць некалькі тысяч даляраў альбо больш, напішыце нам на %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Ці магу я зрабіць ахвяраванне, не становячыся ўдзельнікам?</div> Вядома. Мы прымаем ахвяраванні любой сумы на гэты адрас Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Як мне загрузіць новыя кнігі?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Акрамя таго, вы можаце загрузіць іх у Z-Library <a %(a_upload)s>тут</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Для невялікіх загрузак (да 10 000 файлаў) калі ласка, загрузіце іх як на %(first)s, так і на %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Пакуль што мы прапануем загружаць новыя кнігі ў форкі Library Genesis. Вось <a %(a_guide)s>карысны гід</a>. Звярніце ўвагу, што абодва форкі, якія мы індэксуем на гэтым сайце, выкарыстоўваюць гэтую ж сістэму загрузкі."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Для Libgen.li, пераканайцеся, што спачатку ўвайшлі на <a %(a_forum)s >іх форум</a> з імем карыстальніка %(username)s і паролем %(password)s, а затым вярніцеся на іх <a %(a_upload_page)s >старонку загрузкі</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Калі ваш адрас электроннай пошты не працуе на форумах Libgen, мы рэкамендуем выкарыстоўваць <a %(a_mail)s>Proton Mail</a> (бясплатна). Вы таксама можаце <a %(a_manual)s>запытаць актывацыю</a> вашага акаўнта ўручную."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Звярніце ўвагу, што mhut.org блакуе пэўныя дыяпазоны IP, таму можа спатрэбіцца VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Для вялікіх загрузак (больш за 10 000 файлаў), якія не прымаюцца Libgen або Z-Library, калі ласка, звяжыцеся з намі па %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Каб загрузіць навуковыя артыкулы, калі ласка, таксама (акрамя Library Genesis) загружайце на <a %(a_stc_nexus)s>STC Nexus</a>. Гэта лепшая ценявая бібліятэка для новых артыкулаў. Мы яшчэ не інтэгравалі іх, але зробім гэта ў нейкі момант. Вы можаце выкарыстоўваць іх <a %(a_telegram)s>бот для загрузкі ў Telegram</a> або звязацца з адрасам, указаным у іх замацаваным паведамленні, калі ў вас занадта шмат файлаў для загрузкі такім чынам."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Як запытаць кнігі?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "На дадзены момант мы не можам задаволіць запыты на кнігі."

#, fuzzy
msgid "page.request.forums"
msgstr "Калі ласка, рабіце свае запыты на форумах Z-Library або Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Не дасылайце нам запыты на кнігі па электроннай пошце."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Ці збіраеце вы метаданыя?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Так, сапраўды."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Я спампаваў «1984» Джорджа Оруэла, ці прыйдзе паліцыя да мяне дадому?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Не хвалюйцеся занадта, шмат людзей спампоўваюць з сайтаў, на якія мы спасылаемся, і вельмі рэдка ўзнікаюць праблемы. Аднак, каб заставацца ў бяспецы, мы рэкамендуем выкарыстоўваць VPN (платны) або <a %(a_tor)s>Tor</a> (бясплатны)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Як захаваць мае налады пошуку?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Выберыце налады, якія вам падабаюцца, пакіньце поле пошуку пустым, націсніце “Пошук”, а затым дадайце старонку ў закладкі з дапамогай функцыі закладак вашага браўзера."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Ці ёсць у вас мабільнае прыкладанне?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "У нас няма афіцыйнага мабільнага дадатку, але вы можаце ўсталяваць гэты сайт як дадатак."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Націсніце на меню з трыма кропкамі ў правым верхнім куце і выберыце «Дадаць на галоўны экран»."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Націсніце кнопку “Share” унізе і выберыце “Add to Home Screen”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Ці ёсць у вас API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "У нас ёсць стабільны JSON API для ўдзельнікаў, каб атрымаць хуткую спасылку для загрузкі: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (дакументацыя ўнутры JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Для іншых выпадкаў выкарыстання, такіх як ітэрацыя праз усе нашы файлы, стварэнне карыстацкага пошуку і гэтак далей, мы рэкамендуем <a %(a_generate)s>генераваць</a> або <a %(a_download)s>загружаць</a> нашы базы дадзеных ElasticSearch і MariaDB. Сырыя дадзеныя можна ўручную даследаваць <a %(a_explore)s>праз файлы JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Наш спіс сырых торэнтаў таксама можна загрузіць у фармаце <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Я хацеў бы дапамагчы раздаваць, але ў мяне мала месца на дыску."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Выкарыстоўвайце <a %(a_list)s>генератар спісу торэнтаў</a>, каб стварыць спіс торэнтаў, якія найбольш маюць патрэбу ў торэнтынгу, у межах вашых абмежаванняў па захоўванні."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Торэнты занадта павольныя; ці магу я загрузіць дадзеныя непасрэдна ад вас?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Так, глядзіце старонку <a %(a_llm)s>LLM data</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Ці магу я загрузіць толькі частку файлаў, напрыклад, толькі пэўную мову або тэму?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Кароткі адказ: не так проста."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Доўгі адказ:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Большасць торэнтаў утрымліваюць файлы непасрэдна, што азначае, што вы можаце ўказаць кліентам торэнтаў загружаць толькі патрэбныя файлы. Каб вызначыць, якія файлы загружаць, вы можаце <a %(a_generate)s>стварыць</a> нашы метаданыя або <a %(a_download)s>загрузіць</a> нашы базы дадзеных ElasticSearch і MariaDB. На жаль, некаторыя калекцыі торэнтаў утрымліваюць файлы .zip або .tar у кораню, у такім выпадку вам трэба загрузіць увесь торэнт, перш чым вы зможаце выбраць асобныя файлы."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(У нас ёсць <a %(a_ideas)s>некаторыя ідэі</a> для апошняга выпадку.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Пакуль што няма простых у выкарыстанні інструментаў для фільтрацыі торэнтаў, але мы вітаем уклады."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Як вы апрацоўваеце дублікаты ў торэнтах?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Мы стараемся мінімізаваць дубляванне або перакрыцце паміж торэнтамі ў гэтым спісе, але гэта не заўсёды магчыма і моцна залежыць ад палітык крынічных бібліятэк. Для бібліятэк, якія выпускаюць свае ўласныя торэнты, гэта не ў нашых руках. Для торэнтаў, выпушчаных Anna’s Archive, мы выдаляем дублікаты толькі на аснове MD5-хэша, што азначае, што розныя версіі адной і той жа кнігі не выдаляюцца."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Ці магу я атрымаць спіс торэнтаў у фармаце JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Так."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Я не бачу PDF або EPUB у торэнтах, толькі бінарныя файлы? Што рабіць?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Гэта на самай справе PDF і EPUB, яны проста не маюць пашырэння ў многіх нашых торэнтах. Ёсць два месцы, дзе вы можаце знайсці метададзеныя для торэнт-файлаў, уключаючы тыпы/пашырэнні файлаў:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Кожная калекцыя або выпуск мае свае метаданыя. Напрыклад, <a %(a_libgen_nonfic)s>торэнты Libgen.rs</a> маюць адпаведную базу метаданых, размешчаную на сайце Libgen.rs. Мы звычайна спасылаемся на адпаведныя рэсурсы метаданых з кожнай старонкі <a %(a_datasets)s>набору дадзеных</a> калекцыі."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Мы рэкамендуем <a %(a_generate)s>стварыць</a> або <a %(a_download)s>загрузіць</a> нашы базы дадзеных ElasticSearch і MariaDB. Яны ўтрымліваюць адпаведнасць кожнага запісу ў Anna’s Archive з яго адпаведнымі торэнт-файламі (калі даступна), пад \"torrent_paths\" у JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Чаму мой торэнт-кліент не можа адкрыць некаторыя з вашых торэнт-файлаў / магнітных спасылак?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Некаторыя торэнт-кліенты не падтрымліваюць вялікія памеры частак, якія маюць шмат нашых торэнтаў (для новых мы гэтага больш не робім — хоць гэта і адпавядае спецыфікацыям!). Таму паспрабуйце іншы кліент, калі сутыкнецеся з гэтай праблемай, або скардзіцеся вытворцам вашага торэнт-кліента."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Ці ёсць у вас праграма адказнага раскрыцця інфармацыі?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Мы вітаем даследчыкаў бяспекі для пошуку ўразлівасцяў у нашых сістэмах. Мы вялікія прыхільнікі адказнага раскрыцця інфармацыі. Звяжыцеся з намі <a %(a_contact)s>тут</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "У цяперашні час мы не можам узнагароджваць за выяўленне памылак, за выключэннем уразлівасцяў, якія маюць <a %(a_link)s >патэнцыял скампраметаваць нашу ананімнасць</a>, за якія мы прапануем ўзнагароды ў дыяпазоне $10k-50k. Мы хацелі б у будучыні пашырыць сферу ўзнагароджання за выяўленне памылак! Звярніце ўвагу, што атакі сацыяльнай інжынерыі не ўваходзяць у сферу."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Калі вас цікавіць наступальная бяспека і вы хочаце дапамагчы архіваваць веды і культуру свету, абавязкова звяжыцеся з намі. Ёсць шмат спосабаў, як вы можаце дапамагчы."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ці ёсць больш рэсурсаў пра Архіў Анны?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Блог Анны</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — рэгулярныя абнаўленні"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Праграмнае забеспячэнне Анны</a> — наш адкрыты зыходны код"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Пераклад на праграмным забеспячэнні Анны</a> — наша сістэма перакладу"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Наборы дадзеных</a> — пра дадзеныя"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтэрнатыўныя дамены"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Вікіпедыя</a> — больш пра нас (калі ласка, дапамажыце падтрымліваць гэтую старонку ў актуальным стане або стварыце адну для вашай мовы!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Як паведаміць пра парушэнне аўтарскіх правоў?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Мы не размяшчаем тут ніякіх матэрыялаў, абароненых аўтарскім правам. Мы з'яўляемся пошукавай сістэмай і, такім чынам, індэксуем толькі метаданыя, якія ўжо даступныя публічна. Пры загрузцы з гэтых знешніх крыніц мы раім праверыць законы ў вашай юрысдыкцыі адносна таго, што дазволена. Мы не нясем адказнасці за змест, размешчаны іншымі."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Калі ў вас ёсць скаргі на тое, што вы бачыце тут, лепш за ўсё звязацца з арыгінальным сайтам. Мы рэгулярна ўключаем іх змены ў нашу базу дадзеных. Калі вы сапраўды лічыце, што ў вас ёсць сапраўдная скарга DMCA, на якую мы павінны адказаць, калі ласка, запоўніце <a %(a_copyright)s>форму скаргі DMCA / аўтарскага права</a>. Мы сур'ёзна ставімся да вашых скаргаў і адкажам вам як мага хутчэй."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Я ненавіджу, як вы кіруеце гэтым праектам!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Мы таксама хочам нагадаць усім, што ўвесь наш код і дадзеныя цалкам з'яўляюцца адкрытым зыходным кодам. Гэта ўнікальна для такіх праектаў, як наш, — мы не ведаем ніводнага іншага праекта з такім жа вялікім каталогам, які таксама цалкам з'яўляецца адкрытым зыходным кодам. Мы вельмі вітаем усіх, хто лічыць, што мы дрэнна кіруем нашым праектам, узяць наш код і дадзеныя і стварыць сваю ўласную цень-бібліятэку! Мы не кажам гэта з крыўдай ці чымсьці падобным — мы сапраўды лічым, што гэта было б выдатна, бо гэта падняло б планку для ўсіх і лепш захавала спадчыну чалавецтва."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Ці ёсць у вас манітор часу працы?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Калі ласка, паглядзіце <a %(a_href)s>гэты выдатны праект</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Як я магу ахвяраваць кнігі ці іншыя фізічныя матэрыялы?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Калі ласка, адпраўце іх у <a %(a_archive)s>Internet Archive</a>. Яны належным чынам захаваюць іх."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Хто такая Анна?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Вы — Анна!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Якія вашы любімыя кнігі?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Вось некаторыя кнігі, якія маюць асаблівае значэнне для свету ценявых бібліятэк і лічбавага захавання:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Сёння вы вычарпалі колькасць хуткіх загрузак."

msgid "page.fast_downloads.no_member"
msgstr "Падпішыцеся, каб атрымаць доступ да хуткай загрузкі."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Цяпер мы падтрымліваем падарункавыя карты Amazon, крэдытныя і дэбетавыя карты, крыптавалюту, Alipay і WeChat."

msgid "page.home.full_database.header"
msgstr "Поўная база дадзеных"

msgid "page.home.full_database.subtitle"
msgstr "Кнігі, артыкулы, часопісы, коміксы, метададзеныя, …"

msgid "page.home.full_database.search"
msgstr "Пошук"

msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "бэта"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>прыпыніў</a> загрузку новых артыкулаў."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB з'яўляецца працягам Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Прамы доступ да %(count)s навуковых артыкулаў"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Адкрыць"

msgid "page.home.scidb.browser_verification"
msgstr "Калі вы <a %(a_member)s>удзельнік</a>, праверка браўзэра не патрабуецца."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Доўгатэрміновы архіў"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Наборы дадзеных, якія выкарыстоўваюцца ў архіве Анны, цалкам адкрытыя і могуць быць люстраваны ў вялікіх аб'ёмах з дапамогай торэнтаў. <a %(a_datasets)s>Даведайцеся больш…</a>"

msgid "page.home.torrents.body"
msgstr "Вы можаце вельмі дапамагчы, калі зможаце раздаваць торэнты. <a %(a_torrents)s>Даведацца больш…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s сідэраў"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s сідэраў"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s сідэры"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

msgid "page.home.llm.body"
msgstr "У нас самая вялікая ў свеце калекцыя высакаякасных тэкставых дадзеных. <a %(a_llm)s>Даведацца больш…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Люстэркі: заклік да валанцёраў"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Шукаем валанцёраў"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Як некамерцыйны праект з адкрытым зыходным кодам, мы заўсёды шукаем людзей, якія могуць дапамагчы."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Калі вы кіруеце ананімным плацежным працэсарам высокай рызыкі, калі ласка, звяжыцеся з намі. Мы таксама шукаем людзей, якія жадаюць размясціць стрыманыя невялікія аб'явы. Усе сродкі ідуць на нашы намаганні па захаванні."

msgid "layout.index.header.nav.annasblog"
msgstr "Блог Ганны ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS загрузкі"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Усе спасылкі для спампоўвання гэтага файла: <a %(a_main)s>Галоўная старонка файла</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS-партал #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(магчыма, вам прыйдзецца паспрабаваць некалькі разоў з IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Каб павялічыць хуткасць загрузак, <a %(a_membership)s>падпішыцеся на сайт</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Для масавага люстравання нашай калекцыі, азнаёмцеся з <a %(a_datasets)s>Datasets</a> і <a %(a_torrents)s>Torrents</a> старонкамі."

#, fuzzy
msgid "page.llm.title"
msgstr "Дадзеныя LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Добра вядома, што LLM-ы квітнеюць на якасных дадзеных. У нас самая вялікая калекцыя кніг, артыкулаў, часопісаў і г.д. у свеце, якія з'яўляюцца аднымі з самых якасных тэкставых крыніц."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Унікальны маштаб і разнастайнасць"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Наша калекцыя ўключае больш за сто мільёнаў файлаў, уключаючы акадэмічныя часопісы, падручнікі і часопісы. Мы дасягаем гэтага маштабу, аб'ядноўваючы вялікія існуючыя рэпазітарыі."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Некаторыя з нашых крынічных калекцый ужо даступныя ў вялікіх аб'ёмах (Sci-Hub і часткі Libgen). Іншыя крыніцы мы вызвалілі самі. <a %(a_datasets)s>Datasets</a> паказвае поўны агляд."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Наша калекцыя ўключае мільёны кніг, артыкулаў і часопісаў, якія былі створаны да эпохі электронных кніг. Вялікія часткі гэтай калекцыі ўжо прайшлі OCR і маюць мала ўнутраных паўтораў."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Як мы можам дапамагчы"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Мы можам забяспечыць высокахуткасны доступ да нашых поўных калекцый, а таксама да неапублікаваных калекцый."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Гэта доступ на ўзроўні прадпрыемства, які мы можам забяспечыць за ахвяраванні ў памеры дзесяткаў тысяч долараў ЗША. Мы таксама гатовыя абмяняць гэта на якасныя калекцыі, якіх у нас яшчэ няма."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Мы можам вярнуць вам грошы, калі вы зможаце забяспечыць нас узбагачэннем нашых дадзеных, такім як:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Выдаленне паўтораў (дэдуплікацыя)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Экстракцыя тэксту і метададзеных"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Падтрымлівайце доўгатэрміновае архіваванне чалавечых ведаў, атрымліваючы лепшыя дадзеныя для вашай мадэлі!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Звяжыцеся з намі</a>, каб абмеркаваць, як мы можам супрацоўнічаць."

#, fuzzy
msgid "page.login.continue"
msgstr "Працягнуць"

#, fuzzy
msgid "page.login.please"
msgstr "Калі ласка, <a %(a_account)s>увайдзіце</a>, каб праглядзець гэтую старонку.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Архіў Анны часова недаступны з-за тэхнічнага абслугоўвання. Калі ласка, вярніцеся праз гадзіну."

#, fuzzy
msgid "page.metadata.header"
msgstr "Паляпшэнне метададзеных"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Вы можаце дапамагчы ў захаванні кніг, паляпшаючы метададзеныя! Спачатку прачытайце інфармацыю пра метададзеныя на архіве Анны, а затым даведайцеся, як палепшыць метададзеныя праз спасылкі з Open Library, і атрымайце бясплатнае сяброўства ў архіве Анны."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Фон"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Калі вы глядзіце на кнігу ў архіве Анны, вы можаце ўбачыць розныя палі: назва, аўтар, выдавецтва, выданне, год, апісанне, імя файла і іншае. Уся гэтая інфармацыя называецца <em>метададзеныя</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Паколькі мы аб'ядноўваем кнігі з розных <em>бібліятэк-крыніц</em>, мы паказваем метададзеныя, якія даступныя ў гэтай бібліятэцы-крыніцы. Напрыклад, для кнігі, якую мы атрымалі з Library Genesis, мы пакажам назву з базы дадзеных Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Часам кніга прысутнічае ў <em>некалькіх</em> бібліятэках-крыніцах, якія могуць мець розныя палі метададзеных. У такім выпадку мы проста паказваем самую доўгую версію кожнага поля, бо яна, спадзяемся, утрымлівае найбольш карысную інфармацыю! Мы ўсё роўна пакажам іншыя палі ніжэй апісання, напрыклад, як «альтэрнатыўная назва» (але толькі калі яны адрозніваюцца)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Мы таксама здабываем <em>коды</em>, такія як ідэнтыфікатары і класіфікатары, з бібліятэкі-крыніцы. <em>Ідэнтыфікатары</em> унікальна прадстаўляюць пэўнае выданне кнігі; прыклады ўключаюць ISBN, DOI, Open Library ID, Google Books ID або Amazon ID. <em>Класіфікатары</em> аб'ядноўваюць некалькі падобных кніг; прыклады ўключаюць Дэві Дзесятковую (DCC), УДК, ЛКК, РВК або ГОСТ. Часам гэтыя коды відавочна звязаны ў бібліятэках-крыніцах, а часам мы можам здабыць іх з імя файла або апісання (у асноўным ISBN і DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Мы можам выкарыстоўваць ідэнтыфікатары для пошуку запісаў у <em>калекцыях толькі метададзеных</em>, такіх як OpenLibrary, ISBNdb або WorldCat/OCLC. У нашай пошукавай сістэме ёсць спецыяльная <em>ўкладка метададзеных</em>, калі вы хочаце праглядаць гэтыя калекцыі. Мы выкарыстоўваем адпаведныя запісы для запаўнення адсутных палёў метададзеных (напрыклад, калі адсутнічае назва) або, напрыклад, як «альтэрнатыўная назва» (калі існуе існуючая назва)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Каб убачыць, адкуль менавіта паходзяць метададзеныя кнігі, глядзіце <em>ўкладку «Тэхнічныя дэталі»</em> на старонцы кнігі. Там ёсць спасылка на сыры JSON для гэтай кнігі з указальнікамі на сыры JSON арыгінальных запісаў."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Для атрымання дадатковай інфармацыі глядзіце наступныя старонкі: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Пошук (ўкладка метададзеных)</a>, <a %(a_codes)s>Даследчык кодаў</a> і <a %(a_example)s>Прыклад метададзеных JSON</a>. Нарэшце, усе нашы метададзеныя могуць быць <a %(a_generated)s>згенераваны</a> або <a %(a_downloaded)s>загружаны</a> як базы дадзеных ElasticSearch і MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Спасылка з Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Дык што рабіць, калі вы сутыкнуліся з файлам з дрэннымі метададзенымі? Вы можаце пайсці ў бібліятэку-крыніцу і прытрымлівацца яе працэдур для выпраўлення метададзеных, але што рабіць, калі файл прысутнічае ў некалькіх бібліятэках-крыніцах?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Існуе адзін ідэнтыфікатар, які мае асаблівае значэнне ў архіве Анны. <strong>Поле annas_archive md5 у Open Library заўсёды пераўзыходзіць усе іншыя метададзеныя!</strong> Давайце спачатку вернемся назад і даведаемся пра Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library была заснавана ў 2006 годзе Ааронам Шварцам з мэтай «адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі». Гэта свайго роду Вікіпедыя для метададзеных кніг: кожны можа яе рэдагаваць, яна свабодна ліцэнзаваная і можа быць загружана ў масавым парадку. Гэта база дадзеных кніг, якая найбольш адпавядае нашай місіі — на самай справе, архіў Анны быў натхнёны бачаннямі і жыццём Аарона Шварца."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Замест таго, каб вынаходзіць ровар нанова, мы вырашылі накіраваць нашых валанцёраў у Open Library. Калі вы бачыце кнігу з няправільнымі метададзенымі, вы можаце дапамагчы наступным чынам:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Перайдзіце на <a %(a_openlib)s>вэб-сайт Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Знайдзіце правільны запіс кнігі. <strong>УВАГА:</strong> абавязкова выбірайце правільнае <strong>выданне</strong>. У Open Library ёсць «творы» і «выданні»."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "«Твор» можа быць «Гары Потэр і філасофскі камень»."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "«Выданне» можа быць:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Першае выданне 1997 года, апублікаванае Bloomsbery, з 256 старонкамі."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Выданне ў мяккай вокладцы 2003 года, апублікаванае Raincoast Books, з 223 старонкамі."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Польскі пераклад 2000 года «Harry Potter I Kamie Filozoficzn» ад Media Rodzina з 328 старонкамі."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Усе гэтыя выданні маюць розныя ISBN і розны змест, таму абавязкова выбірайце правільнае!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Рэдагуйце запіс (або стварыце яго, калі ён не існуе), і дадайце як мага больш карыснай інфармацыі! Вы ўжо тут, таму зрабіце запіс сапраўды выдатным."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "У раздзеле «ID Numbers» выберыце «Архіў Анны» і дадайце MD5 кнігі з Архіва Анны. Гэта доўгі радок літар і лічбаў пасля «/md5/» у URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Паспрабуйце знайсці іншыя файлы ў Архіве Анны, якія таксама адпавядаюць гэтаму запісу, і дадайце іх таксама. У будучыні мы зможам групаваць іх як дублікаты на старонцы пошуку Архіва Анны."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Калі скончыце, запішыце URL, які вы толькі што абнавілі. Пасля таго, як вы абновіце прынамсі 30 запісаў з MD5 Архіва Анны, дашліце нам <a %(a_contact)s>ліст</a> і спіс. Мы дамо вам бясплатнае сяброўства ў Архіве Анны, каб вы маглі лягчэй выконваць гэтую працу (і як падзяку за вашу дапамогу). Гэтыя рэдагаванні павінны быць высокай якасці і дадаваць значную колькасць інфармацыі, інакш ваша запыт будзе адхілены. Ваш запыт таксама будзе адхілены, калі любое з рэдагаванняў будзе адменена або выпраўлена мадэратарамі Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Звярніце ўвагу, што гэта працуе толькі для кніг, а не для навуковых артыкулаў або іншых тыпаў файлаў. Для іншых тыпаў файлаў мы ўсё яшчэ рэкамендуем знайсці крынічную бібліятэку. Можа спатрэбіцца некалькі тыдняў, каб змены былі ўключаны ў Архіў Анны, бо нам трэба загрузіць апошні дамп дадзеных Open Library і перагенераваць наш індэкс пошуку."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Люстэркі: заклік да валанцёраў"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Для павышэння ўстойлівасці архіва Анны мы шукаем валанцёраў для запуску люстэркаў."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Мы шукаем гэта:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Вы кіруеце адкрытым зыходным кодам архіва Анны і рэгулярна абнаўляеце як код, так і дадзеныя."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Ваша версія выразна адрозніваецца як люстраная, напрыклад, “Архіў Боба, люстраны архіў Анны”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Вы гатовыя прыняць рызыкі, звязаныя з гэтай працай, якія значныя. Вы глыбока разумееце неабходную аперацыйную бяспеку. Змест <a %(a_shadow)s>гэтых</a> <a %(a_pirate)s>паведамленняў</a> для вас відавочны."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Вы гатовыя ўнесці свой уклад у наш <a %(a_codebase)s>зыходны код</a> — у супрацоўніцтве з нашай камандай — каб гэта адбылося."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Першапачаткова мы не дамо вам доступу да загрузак з сервера партнёра, але калі ўсё пойдзе добра, мы можам падзяліцца гэтым з вамі."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Выдаткі на хостынг"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Мы гатовыя пакрыць выдаткі на хостынг і VPN, першапачаткова да $200 у месяц. Гэта дастаткова для базавага пошукавага сервера і праксі з абаронай DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Мы будзем аплачваць хостынг толькі пасля таго, як усё будзе наладжана і вы пакажаце, што здольныя падтрымліваць архіў у актуальным стане з абнаўленнямі. Гэта азначае, што вам давядзецца аплаціць першыя 1-2 месяцы самастойна."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Ваш час не будзе кампенсаваны (і наш таксама), бо гэта чыста валанцёрская праца."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Калі вы значна ўцягнецеся ў распрацоўку і аперацыі нашай працы, мы можам абмеркаваць падзел большай часткі даходаў ад ахвяраванняў з вамі, каб вы маглі выкарыстоўваць іх па неабходнасці."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Пачатак працы"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Калі ласка, <strong>не звяртайцеся да нас</strong> за дазволам або з базавымі пытаннямі. Дзеянні гавораць гучней за словы! Уся інфармацыя ёсць у адкрытым доступе, таму проста пачніце наладжваць сваё люстэрка."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Калі вы сутыкнецеся з праблемамі, не саромейцеся размяшчаць білеты або запыты на зліццё ў нашым Gitlab. Магчыма, нам спатрэбіцца стварыць некаторыя функцыі, спецыфічныя для люстэркаў, разам з вамі, напрыклад, рэбрэндынг з “Архіва Анны” на назву вашага сайта, (першапачаткова) адключэнне ўліковых запісаў карыстальнікаў або спасылка на наш асноўны сайт са старонак кніг."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Калі ў вас запрацуе люстэрка, калі ласка, звяжыцеся з намі. Мы хацелі б праверыць вашу бяспеку, і калі ўсё будзе ў парадку, мы спасылкуемся на ваша люстэрка і пачнем цесна супрацоўнічаць з вамі."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Дзякуй загадзя ўсім, хто гатовы ўнесці свой уклад такім чынам! Гэта не для слабых сэрцам, але гэта ўмацуе даўгавечнасць найбуйнейшай сапраўды адкрытай бібліятэкі ў гісторыі чалавецтва."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Спампаваць з партнёрскага сайта"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Павольныя загрузкі даступныя толькі праз афіцыйны сайт. Наведайце %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Павольныя загрузкі недаступныя праз Cloudflare VPN або з IP-адрасоў Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Калі ласка, пачакайце <span %(span_countdown)s>%(wait_seconds)s</span> секунд, каб загрузіць гэты файл."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Выкарыстоўвайце наступны URL для загрузкі: <a %(a_download)s>Загрузіць зараз</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Дзякуй за чаканне, гэта дазваляе зрабіць сайт даступным бясплатна для ўсіх! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Папярэджанне: за апошнія 24 гадзіны з вашага IP-адраса было шмат загрузак. Загрузкі могуць быць павольнейшымі, чым звычайна."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Запампоўкі з вашага IP-адраса за апошнія 24 гадзіны: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Калі вы карыстаецеся VPN, агульным падключэннем да Інтэрнэту або ваш інтэрнэт-правайдэр дзеліць IP-адрасы, гэта папярэджанне можа быць звязана з гэтым."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Каб даць усім магчымасць бясплатна загружаць файлы, вам трэба пачакаць, перш чым вы зможаце загрузіць гэты файл."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Не саромейцеся працягваць праглядаць архіў Анны ў іншай укладцы, пакуль чакаеце (калі ваш браўзер падтрымлівае абнаўленне фонавых укладак)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Не саромейцеся чакаць, пакуль загружаюцца некалькі старонак загрузкі адначасова (але, калі ласка, загружайце толькі адзін файл адначасова з аднаго сервера)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Пасля атрымання спасылкі для загрузкі яна будзе дзейнічаць на працягу некалькіх гадзін."

msgid "layout.index.header.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Запіс у архіве Анны"

#, fuzzy
msgid "page.scidb.download"
msgstr "Загрузіць"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Каб падтрымаць даступнасць і доўгатэрміновае захаванне чалавечых ведаў, станьце <a %(a_donate)s>членам</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "У якасці бонуса, 🧬&nbsp;SciDB загружаецца хутчэй для ўдзельнікаў, без абмежаванняў."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Не працуе? Паспрабуйце <a %(a_refresh)s>абнавіць</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Папярэдні прагляд пакуль недаступны. Спампуйце файл з <a %(a_path)s>Архіва Анны</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB з'яўляецца працягам Sci-Hub, з яго знаёмым інтэрфейсам і прамым праглядам PDF. Увядзіце ваш DOI для прагляду."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "У нас ёсць поўная калекцыя Sci-Hub, а таксама новыя артыкулы. Большасць можна праглядаць непасрэдна з знаёмым інтэрфейсам, падобным да Sci-Hub. Некаторыя можна спампаваць праз знешнія крыніцы, у такім выпадку мы паказваем спасылкі на іх."

msgid "page.search.title.results"
msgstr "%(search_input)s - Пошук"

msgid "page.search.title.new"
msgstr "Новы пошук"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Уключыць толькі"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Выключыць"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Неправерана"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Спампаваць"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Артыкулы з часопісаў"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Лічбавае пазычанне"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Метаданыя"

msgid "common.search.placeholder"
msgstr "назве, аўтару, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Пошук"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Налады пошуку"

#, fuzzy
msgid "page.search.submit"
msgstr "Пошук"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Пошук заняў занадта шмат часу, што звычайна для шырокіх запытаў. Колькасць фільтраў можа быць недакладнай."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Пошук заняў занадта шмат часу, што азначае, што вы можаце ўбачыць недакладныя вынікі. Часам <a %(a_reload)s>перазагрузка</a> старонкі дапамагае."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Адлюстраваць"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Спіс"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Табліца"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Пашыраны"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Пошук апісанняў і каментарыяў да метададзеных"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Дадаць канкрэтнае поле пошуку"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(пошук па канкрэтным полі)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Год выдання"

msgid "page.search.filters.content.header"
msgstr "Змест"

msgid "page.search.filters.filetype.header"
msgstr "Тып файла"

#, fuzzy
msgid "page.search.more"
msgstr "яшчэ…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Доступ"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Крыніца"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "скрэбавана і адкрыта AA"

msgid "page.search.filters.language.header"
msgstr "Мова"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Сартаваць па"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Найбольш дарэчны"

msgid "page.search.filters.sorting.newest"
msgstr "Найноўшы"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(год публікацыі)"

msgid "page.search.filters.sorting.oldest"
msgstr "Найстарэйшы"

msgid "page.search.filters.sorting.largest"
msgstr "Найбуйнейшы"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(памер файла)"

msgid "page.search.filters.sorting.smallest"
msgstr "Найменшы"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(з адкрытым зыходным кодам)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Выпадкова"

msgid "page.search.header.update_info"
msgstr "Пошукавы індэкс абнаўляецца штомесяц. У цяперашні час ён уключае запісы да %(last_data_refresh_date)s. Для атрымання дадатковай тэхнічнай інфармацыі глядзіце старонку %(link_open_tag)sнаборы дадзеных</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Каб даследаваць індэкс пошуку па кодах, выкарыстоўвайце <a %(a_href)s>Даследчык кодаў</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Увядзіце ў поле для пошуку ў нашым каталогу %(count)s файлаў, якія можна спампаваць непасрэдна, якія мы <a %(a_preserve)s>захоўваем назаўсёды</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "На самай справе, кожны можа дапамагчы захаваць гэтыя файлы, раздаючы наш <a %(a_torrents)s>аб'яднаны спіс торэнтаў</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "У нас зараз самы поўны адкрыты каталог кніг, артыкулаў і іншых пісьмовых прац у свеце. Мы люструем Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>і іншыя</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Калі вы знойдзеце іншыя «ценявыя бібліятэкі», якія мы павінны адлюстраваць, або калі ў вас ёсць якія-небудзь пытанні, калі ласка, звяжыцеся з намі па %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Для заяў DMCA / аўтарскіх правоў <a %(a_copyright)s>націсніце тут</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Падказка: выкарыстоўвайце спалучэнні клавіш “/” (фокус пошуку), “enter” (пошук), “j” (уверх), “k” (уніз), “<” (папярэдняя старонка), “>” (наступная старонка) для хутчэйшай навігацыі."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Шукаеце навуковыя артыкулы?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Увядзіце ў поле для пошуку наш каталог %(count)s акадэмічных артыкулаў і часопісаў, якія мы <a %(a_preserve)s>захоўваем назаўсёды</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Увядзіце ў поле для пошуку файлаў у лічбавых бібліятэках."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Гэты індэкс пошуку ў цяперашні час уключае метададзеныя з бібліятэкі кантраляванага лічбавага пазычання Internet Archive. <a %(a_datasets)s>Больш пра нашы Datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Для большай колькасці лічбавых бібліятэк глядзіце <a %(a_wikipedia)s>Вікіпедыя</a> і <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Увядзіце ў поле для пошуку метаданых з бібліятэк. Гэта можа быць карысна пры <a %(a_request)s>запыце файла</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Гэты індэкс пошуку ў цяперашні час уключае метаданыя з розных крыніц метаданых. <a %(a_datasets)s>Больш пра нашы наборы дадзеных</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Для метададзеных мы паказваем арыгінальныя запісы. Мы не аб'ядноўваем запісы."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Існуе шмат крыніц метададзеных для пісьмовых твораў па ўсім свеце. <a %(a_wikipedia)s>Гэтая старонка Вікіпедыі</a> — добры пачатак, але калі вы ведаеце іншыя добрыя спісы, паведаміце нам."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Увядзіце ў поле для пошуку."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Гэта метададзеныя, <span %(classname)s>а не</span> файлы для загрузкі."

msgid "page.search.results.error.header"
msgstr "Памылка падчас пошуку."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Паспрабуйце <a %(a_reload)s>перазагрузіць старонку</a>. Калі праблема застаецца, калі ласка, напішыце нам на %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\"> Нічога не знойдзена.</span> Паспрабуйце скараціць запыт або выкарыстоўваць іншыя ключавыя словы або фільтры."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Часам гэта адбываецца некарэктна, калі сервер пошуку працуе павольна. У такіх выпадках <a %(a_attrs)s>перазагрузка</a> можа дапамагчы."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Мы знайшлі супадзенні ў: %(in)s. Вы можаце спасылацца на URL, знойдзены там, калі <a %(a_request)s>запытваеце файл</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Артыкулы з часопісаў (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Лічбавае пазычанне (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Метаданыя (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Вынікі %(from)s-%(to)s (%(total)s усяго)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ частковыя супадзенні"

msgid "page.search.results.partial"
msgstr "%(num)d частковыя супадзенні"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Валанцёрства і ўзнагароды"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Архіў Анны абапіраецца на такіх валанцёраў, як вы. Мы вітаем усе ўзроўні ўдзелу і маем дзве асноўныя катэгорыі дапамогі, якую мы шукаем:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Лёгкая валанцёрская праца:</span> калі вы можаце вылучыць толькі некалькі гадзін час ад часу, усё роўна ёсць шмат спосабаў, як вы можаце дапамагчы. Мы ўзнагароджваем пастаянных валанцёраў <span %(bold)s>🤝 сяброўствамі ў Архіве Анны</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Інтэнсіўная валанцёрская праца (узнагароды ад 50 да 5000 долараў ЗША):</span> калі вы можаце прысвяціць шмат часу і/або рэсурсаў нашай місіі, мы будзем рады працаваць з вамі больш цесна. У рэшце рэшт, вы можаце далучыцца да ўнутранай каманды. Хоць у нас абмежаваны бюджэт, мы можам узнагародзіць <span %(bold)s>💰 грашовымі ўзнагародамі</span> за самую інтэнсіўную працу."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Калі вы не можаце валанцёрыць свой час, вы ўсё роўна можаце вельмі дапамагчы нам, <a %(a_donate)s>ахвяруючы грошы</a>, <a %(a_torrents)s>раздаючы нашы торэнты</a>, <a %(a_uploading)s>загружаючы кнігі</a> або <a %(a_help)s>распавядаючы сваім сябрам пра Архіў Анны</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Кампаніі:</span> мы прапануем высокахуткасны прамы доступ да нашых калекцый у абмен на карпаратыўныя ахвяраванні або ў абмен на новыя калекцыі (напрыклад, новыя сканы, OCR-дадзеныя, узбагачэнне нашых дадзеных). <a %(a_contact)s>Звяжыцеся з намі</a>, калі гэта пра вас. Глядзіце таксама нашу <a %(a_llm)s>старонку LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Лёгкае валанцёрства"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Калі ў вас ёсць некалькі гадзін вольнага часу, вы можаце дапамагчы рознымі спосабамі. Абавязкова далучайцеся да <a %(a_telegram)s>чата валанцёраў у Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "У знак удзячнасці мы звычайна даем 6 месяцаў “Шчаслівага Бібліятэкара” за базавыя дасягненні і больш за працяглую валанцёрскую працу. Усе дасягненні патрабуюць высокай якасці працы — неахайная праца шкодзіць нам больш, чым дапамагае, і мы яе адхіляем. Калі ласка, <a %(a_contact)s>напішыце нам</a>, калі дасягнеце мэты."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Задача"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Мэта"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Распаўсюджванне інфармацыі пра Архіў Анны. Напрыклад, рэкамендуючы кнігі на AA, спасылаючыся на нашы блогі або накіроўваючы людзей на наш сайт."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s спасылкі або скрыншоты."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Гэта павінна паказваць, як вы паведамляеце камусьці пра Архіў Анны, і яны дзякуюць вам."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Паляпшэнне метададзеных шляхам <a %(a_metadata)s>звязвання</a> з Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Вы можаце выкарыстоўваць <a %(a_list)s >спіс выпадковых праблем з metadata</a> у якасці адпраўной кропкі."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Абавязкова пакідайце каментар да праблем, якія вы выправілі, каб іншыя не дублявалі вашу працу."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s спасылкі на запісы, якія вы палепшылі."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Пераклад</a> сайта."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Поўны пераклад мовы (калі ён не быў амаль завершаны)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Паляпшэнне старонкі Вікіпедыі пра Архіў Анны на вашай мове. Уключыце інфармацыю са старонкі Вікіпедыі AA на іншых мовах, а таксама з нашага сайта і блога. Дадайце спасылкі на AA на іншых адпаведных старонках."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Спасылка на гісторыю рэдагавання, якая паказвае, што вы зрабілі значныя ўнёскі."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Выконванне запытаў на кнігі (або артыкулы і г.д.) на форумах Z-Library або Library Genesis. У нас няма ўласнай сістэмы запытаў на кнігі, але мы люструем гэтыя бібліятэкі, таму паляпшэнне іх робіць Архіў Анны лепшым таксама."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s спасылкі або скрыншоты запытаў, якія вы выканалі."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Невялікія задачы, размешчаныя ў нашым <a %(a_telegram)s>чаце валанцёраў у Telegram</a>. Звычайна для членства, часам для невялікіх узнагарод."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Невялікія задачы размешчаны ў нашай групе чата для валанцёраў."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Залежыць ад задачы."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Узнагароды"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Мы заўсёды шукаем людзей з моцнымі навыкамі праграмавання або наступальнай бяспекі, каб далучыцца да нас. Вы можаце зрабіць сур'ёзны ўклад у захаванне спадчыны чалавецтва."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "У якасці падзякі мы даем членства за значныя ўнёскі. У якасці вялікай падзякі мы даем грашовыя ўзнагароды за асабліва важныя і складаныя задачы. Гэта не павінна разглядацца як замена працы, але гэта дадатковы стымул і можа дапамагчы з пакрыццём выдаткаў."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Большасць нашага кода з'яўляецца адкрытым зыходным кодам, і мы таксама папросім, каб ваш код быў адкрытым пры ўзнагароджанні. Ёсць некаторыя выключэнні, якія мы можам абмеркаваць індывідуальна."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Узнагароды прысуджаюцца першаму чалавеку, які выканае задачу. Не саромейцеся каментаваць білет на ўзнагароду, каб паведаміць іншым, што вы працуеце над чымсьці, каб іншыя маглі пачакаць або звязацца з вамі для сумеснай працы. Але будзьце ўважлівыя, што іншыя таксама могуць працаваць над гэтым і паспрабаваць апярэдзіць вас. Аднак мы не прысуджаем узнагароды за неахайную працу. Калі дзве якасныя заяўкі зроблены блізка адна да адной (на працягу дня або двух), мы можам выбраць узнагароджанне абедзвюх па нашым меркаванні, напрыклад, 100%% за першую заяўку і 50%% за другую заяўку (такім чынам, усяго 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Для больш буйных узнагарод (асабліва за скрэйпінг), калі ласка, звяжыцеся з намі, калі вы завяршылі ~5%% ад гэтага, і вы ўпэўнены, што ваш метад будзе маштабавацца да поўнай мэты. Вам трэба будзе падзяліцца сваім метадам з намі, каб мы маглі даць зваротную сувязь. Таксама такім чынам мы можам вырашыць, што рабіць, калі некалькі чалавек набліжаюцца да ўзнагароды, напрыклад, патэнцыйна ўзнагароджваць некалькіх людзей, заахвочваць людзей да сумеснай працы і г.д."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ПАПЯРЭДЖАННЕ: задачы з высокімі ўзнагародамі <span %(bold)s>складаныя</span> — магчыма, варта пачаць з больш простых."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Перайдзіце ў наш <a %(a_gitlab)s>спіс задач на Gitlab</a> і адсартыруйце па \"Прыярытэт пазнакі\". Гэта прыблізна паказвае парадак задач, якія нас цікавяць. Задачы без відавочных узнагарод усё яшчэ могуць быць прэтэндэнтамі на членства, асабліва тыя, якія пазначаны як \"Прынята\" і \"Любімае Анны\". Магчыма, вам варта пачаць з \"Пачатковага праекта\"."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Абнаўленні пра <a %(wikipedia_annas_archive)s>Архіў Анны</a>, найбуйнейшую сапраўды адкрытую бібліятэку ў гісторыі чалавецтва."

msgid "layout.index.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Найбуйнейшая ў свеце бібліятэка з адкрытым зыходным кодам і адкрытымі дадзенымі. Люстэркі Sci-Hub, Library Genesis, Z-Library і іншыя."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Шукаць у Архіве Анны"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Архіў Анны патрэбна ваша дапамога!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Многія спрабуюць нас знішчыць, але мы змагаемся."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Калі вы ахвяруеце зараз, вы атрымаеце <strong>двайны</strong> лік хуткіх загрузак."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Дзейнічае да канца гэтага месяца."

msgid "layout.index.header.nav.donate"
msgstr "Ахвяраваць"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Выратаванне чалавечых ведаў: выдатны падарунак на свята!"

msgid "layout.index.header.banner.surprise"
msgstr "Здзівіце каханага чалавека, падарыце яму рахунак з членствам."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Для павышэння ўстойлівасці Архіва Анны мы шукаем валанцёраў для запуску люстэркаў."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Ідэальны падарунак на Дзень святога Валянціна!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "У нас даступны новы метад ахвяраванні: %(method_name)s. Калі ласка, падумайце аб %(donate_link_open_tag)sахвяраванні </a> — запуск гэтага вэб-сайта абыходзіцца нятанна і ваша ахвяраванне сапраўды мае значэнне. Вялікі дзякуй."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Мы праводзім збор сродкаў для <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">рэзервовага капіявання</a> найбуйнейшай бібліятэкі коміксаў у свеце. Дзякуй за вашу падтрымку! <a href=\"/donate\">Ахвяруйце.</a> Калі вы не можаце ахвяраваць, разгледзьце магчымасць падтрымаць нас, расказаўшы сваім сябрам і падпісаўшыся на нас у <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> або <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Апошнія загрузкі:"

msgid "layout.index.header.nav.search"
msgstr "Пошук"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Частыя пытанні"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Паляпшэнне метададзеных"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Валанцёрства і ўзнагароды"

msgid "layout.index.header.nav.datasets"
msgstr "Наборы дадзеных"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Торэнты"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Актыўнасць"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Даследчык кодаў"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Дадзеныя LLM"

msgid "layout.index.header.nav.home"
msgstr "Галоўная старонка"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Пераклад ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Увайсці / Рэгістрацыя"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Акаўнт"

msgid "layout.index.footer.list1.header"
msgstr "Архіў Ганны"

msgid "layout.index.footer.list2.header"
msgstr "Заставацца на сувязі"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / прэтэнзіі на аўтарскія правы"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Прасунуты"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Бяспека"

msgid "layout.index.footer.list3.header"
msgstr "Альтэрнатыўныя варыянты"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "неафіляваны"

msgid "page.search.results.issues"
msgstr "❌ Гэты файл можа ўтрымліваць памылкі."

msgid "page.search.results.fast_download"
msgstr "Хуткае спампоўванне"

#, fuzzy
msgid "page.donate.copy"
msgstr "капіраваць"

#, fuzzy
msgid "page.donate.copied"
msgstr "скапіявана!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Папярэдні"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Далей"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Люстэрка #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Сабрэддіт"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% пісьмовай спадчыны чалавецтва, захавана назаўжды %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Наборы дадзеных ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Спампаваць бясплатна электронную кнігу/файл %(extension)s з:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "У нас ёсць некалькі варыянтаў загрузкі на выпадак, калі адзін з іх не спрацуе. Усе яны маюць адзін і той жа файл."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "У Anna’s Archive няма нічога з таго, што тут прадстаўлена. Мы проста спасылаемся на сайты іншых людзей. Калі вы сапраўды лічыце, што ў вас ёсць абгрунтаваная скарга DMCA, Калі ласка, азнаёмцеся са старонкай %(about_link)sПра нас</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Ананімнае люстэрка #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Падтрымаць праект"

#~ msgid "page.donate.header"
#~ msgstr "Падтрымаць праект"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive - гэта некамерцыйны праект з адкрытым зыходным кодам, цалкам кіраваны валанцёрамі. Мы прымаем ахвяраванні для пакрыцця нашых выдаткаў, якія ўключаюць хостынг, даменныя імёны, распрацоўку і іншыя выдаткі."

#~ msgid "page.donate.text2"
#~ msgstr "Дзякуючы вашаму ўнёску мы можам падтрымліваць працу гэтага сайта, паляпшаць яго функцыі і захоўваць больш калекцый кніг."

#~ msgid "page.donate.text3"
#~ msgstr "Нядаўнія ахвяраванні: %(donations)s. Мы вельмі ўдзячныя Вам за вашу шчодрасць і давер, у якой бы суме яна ні выяўлялася."

#~ msgid "page.donate.text4"
#~ msgstr "Каб зрабіць ахвяраванне, абярыце адзін з метадаў ніжэй. Калі ў вас узніклі якія-небудзь праблемы, напішыце нам на %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Банкаўская карта"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Крыптавалюты"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Пытанні"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Прайдзіце па %(link_open_tag)sспасылцы</a> і вынікайце інструкцыі, якія будуць даступныя па QR-коду або спасылцы. Калі нешта не спрацавала, паспрабуйце абнавіць старонку – гэта можа пераключыць вас на іншы рахунак."

#~ msgid "page.donate.cc.header"
#~ msgstr "Банкаўская картка"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Мы супрацоўнічаем з Sanswire для канвертавання вашага плацяжу ў Bitcoin (BTC). Аперацыя залічэння можа заняць да пяці хвілін."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Мінімальная сума транзакцыі гэтым метадам складае $30 і будзе мець камісію ў памеры прыкладна $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Наступныя крокі:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Скапіруйце нашу Bitcoin-адрас: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Перайдзіце на %(link_open_tag)sгэтай старонкі</a> і націсніце на \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Ўстаўце адрас свайго кашалька і выконвайце далейшыя інструкцыі"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Crypto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(таксама працуе для BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Калі ласка, выкарыстоўвайце гэты ўліковы запіс %(link_open_tag)sAlipay</a> для адпраўкі вашага ахвяраванні. Калі гэта не спрацуе, паспрабуйце абнавіць старонку, так як гэта можа прывесці да стварэння іншы ўліковага запісу."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Гэты варыянт ахвяраванні ў цяперашні час не працуе. Калі ласка, зайдзіце пазней. Дзякуй, што вырашалі ахвяраваць, мы сапраўды цэнім гэта!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Калі ласка, выкарыстоўвайце %(link_open_tag)sна гэтай старонцы Pix</a>, каб адправіць сваё ахвяраванне. Калі гэта не спрацуе, паспрабуйце абнавіць старонку, так як гэта можа прывесці да стварэння іншы ўліковага запісу."

#~ msgid "page.donate.faq.header"
#~ msgstr "Частыя пытанні"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> - гэта праект, мэтай якога з'яўляецца каталагізацыя ўсіх існуючых кніг шляхам аб'яднання дадзеных з розных крыніц. Мы таксама адсочваем прагрэс чалавецтва ў напрамку забеспячэння лёгкага доступу да ўсіх гэтых кніг у лічбавай форме праз “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ценявыя бібліятэкі</a>\". Даведайцеся больш <a href=\"/about\">пра нас.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Кніга (любая)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Галоўная"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Наборы дадзеных ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Не знойдзена"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” - недапушчальны ISBN нумар. ISBN маюць даўжыню 10 або 13 знакаў, не лічачы неабавязковых працяжнік. Усе сімвалы павінны быць лікамі, за выключэннем апошняга сімвала, які таксама можа быць \"X\". Апошні сімвал-гэта \"кантрольная лічба\", якая павінна адпавядаць значэнню кантрольнай сумы, вылічаецца з іншых лікаў. Ён таксама павінен знаходзіцца ў дапушчальным дыяпазоне, вылучаным Міжнародным агенцтвам ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Адпаведныя файлы ў нашай базе дадзеных:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Файлаў з дадзенай назвай не знойдзена."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Пошук ▶ %(num)d+ вынікі для <span class=\"italic\">%(search_input)s</span> знойдзена ў метададзеных ценявых бібліятэк"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Пошук ▶ %(num)d вынікі для <span class=\"italic\">%(search_input)s</span> знойдзена ў метададзеных ценявых бібліятэк"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Пошук ▶ Памылка пошуку для <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Пошук ▶ Новы пошук"

#~ msgid "page.donate.header.text3"
#~ msgstr ""

#~ msgid "page.donate.buttons.one_time"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Калі ў вас ужо ёсць крыпта-грошы, вось нашы адрасы."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Вялікі вам дзякуй за дапамогу! Гэты праект быў бы немагчымы без вас."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Калі зручны для Вас метад аплаты не падтрымліваецца, мы рэкамендуем загрузіць <a href=\"https://paypal.com/\">PayPal</a> or <a href=\"https://www.coinbase.com/\">Coinbase</a> на ваш смартфон, купіць Bitcoin ў ім і адправіць на наш адрас %(address)s. У большасці краін поўны працэс налады павінен заняць не больш чым некалькі хвілін."

#~ msgid "page.search.results.error.text"
#~ msgstr "Паспрабуйце <a href=\"javascript:location.reload()\">абнавіць старонку</a>. Калі праблема не знікла, калі ласка, паведаміце нам пра гэта ў <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ці ў <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Галоўная"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Пра нас"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Ахвяраваць"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Наборы дадзеных"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Мабільнае прыкладанне"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Блог Ганны"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Пераклад"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Твітэр"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Люстэркі %(libraries)s і іншае."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Наборы дадзеных ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Не знойдзена"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" не з'яўляецца правільным doi-кодам. Ён павінен пачынацца на \"10\" і мець хоць бы адну касую рысу."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Кананічны URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Патэнцыйная лакацыя файла: %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Адпаведныя файлы ў нашай базе дадзеных:"

#~ msgid "page.doi.results.none"
#~ msgstr "У нашай базе дадзеных не знойдзена адпаведных файлаў."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Ці магу я вам яшчэ як-небудзь дапамагчы?</div> Так! Паглядзіце нашу <a href=\"/about\">інфармацыйную старонку </a>, раздзел \"Дапамога праекту\"."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr "Запытаць кнігі"

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr "Загрузіць"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Пра праект"

#~ msgid "page.about.header"
#~ msgstr "Пра праект"

#~ msgid "page.home.search.header"
#~ msgstr "Пошук"

#~ msgid "page.home.search.intro"
#~ msgstr "Пошук у нашым каталогу ценявых бібліятэк."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr "Anna's Archive - гэта бясплатная пошукавая сістэма з адкрытым зыходным кодам для “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ценявых бібліятэк</a>”. Яна была створана <a href=\"http://annas-blog.org\">Ганнай</a>. Ганне не хапала цэнтралізаванага партала для пошуку кніг, даследаванняў, коміксаў, часопісаў і іншай літаратуры, і яна рэалізавала свой намер, стварыўшы сайт на якім вы знаходзіцеся."

#~ msgid "page.about.text4"
#~ msgstr "Калі ў вас ёсць абгрунтаваная скарга DMCA, глядзіце кантакты ўнізе гэтай старонкі або звяжыцеся з намі па адрасе %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Папулярныя кнігі"

#~ msgid "page.home.explore.intro"
#~ msgstr "Гэты спіс змяшчае папулярныя кнігі і кнігі якія ўяўляюць вялікую каштоўнасць для ценявых бібліятэк і лічбавага захавання."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Пра нас"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Мабільнае прыкладанне"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Дапамога праекту"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr ""

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "толькі ў гэтым месяцы!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>прыпыніў</a> загрузку новых артыкулаў."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Выберыце варыянт аплаты. Мы даем зніжкі на плацяжы ў криптовалюте %(bitcoin_icon)s, таму што мы нясем (значна) менш выдаткаў."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Выберыце варыянт аплаты. У цяперашні час у нас ёсць толькі крыптавалютныя плацяжы %(bitcoin_icon)s, паколькі традыцыйныя аплатныя сістэмы адмаўляюцца працаваць з намі."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Мы не можам падтрымліваць крэдытныя/дэбетавыя карты непасрэдна, таму што банкі не хочуць з намі працаваць. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Аднак ёсць некалькі спосабаў выкарыстання крэдытных/дэбетавых картак, выкарыстоўваючы нашы іншыя метады аплаты:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "Спампаваць павольна з знешніх рэсурсаў"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Спампоўкі"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Калі вы ўпершыню карыстаецеся крыптавалютай, мы раім выкарыстоўваць %(option1)s, %(option2)s або %(option3)s для пакупкі і ахвяравання Bitcoin (першая і найбольш выкарыстоўваная крыптавалюта)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 спасылак на запісы, якія вы палепшылі."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 спасылак або скрыншотаў."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 спасылак або скрыншотаў запытаў, якія вы выканалі."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Калі вы зацікаўлены ў люстраванні гэтых набораў дадзеных для <a %(a_faq)s>архівавання</a> або <a %(a_llm)s>навучання LLM</a>, калі ласка, звяжыцеся з намі."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Калі вы зацікаўлены ў люстраванні гэтага набору дадзеных для <a %(a_archival)s>архівавання</a> або для мэт <a %(a_llm)s>навучання LLM</a>, калі ласка, звяжыцеся з намі."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Галоўны сайт"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Інфармацыя пра краіну ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Калі вы зацікаўлены ў люстраванні гэтага набору дадзеных для мэт <a %(a_archival)s>архівавання</a> або <a %(a_llm)s>навучання LLM</a>, калі ласка, звяжыцеся з намі."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Міжнароднае агенцтва ISBN рэгулярна выпускае дыяпазоны, якія яно выдзеліла нацыянальным агенцтвам ISBN. З гэтага мы можам вызначыць, да якой краіны, рэгіёна або моўнай групы належыць гэты ISBN. У цяперашні час мы выкарыстоўваем гэтыя дадзеныя ўскосна, праз бібліятэку Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Рэсурсы"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Апошняе абнаўленне: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Сайт ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Метаданыя"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Выключаючы “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Нашым натхненнем для збору метададзеных з'яўляецца мэта Аарона Шварца \"адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі\", для якой ён стварыў <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Гэты праект спраўляецца добра, але наша ўнікальная пазіцыя дазваляе нам атрымліваць метададзеныя, якія яны не могуць."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Яшчэ адной натхненнем было наша жаданне ведаць <a %(a_blog)s>колькі кніг існуе ў свеце</a>, каб мы маглі падлічыць, колькі кніг нам яшчэ трэба захаваць."

#~ msgid "page.partner_download.text1"
#~ msgstr "Каб даць усім магчымасць бясплатна спампаваць файлы, вам трэба пачакаць <strong>%(wait_seconds)s секунд</strong>, перш чым вы зможаце спампаваць гэты файл."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Аўтаматычна абнаўляць старонку. Калі вы прапусціце акно загрузкі, таймер перазапусціцца, таму рэкамендуецца аўтаматычнае абнаўленне."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Спампаваць зараз"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Канвертаваць: выкарыстоўвайце анлайн-інструменты для канвертавання паміж фарматамі. Напрыклад, для канвертавання паміж epub і pdf, выкарыстоўвайце <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: спампуйце файл (падтрымліваюцца pdf або epub), затым <a %(a_kindle)s>адпраўце яго на Kindle</a> з дапамогай вэб-сайта, прыкладання або электроннай пошты. Карысныя інструменты: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Падтрымайце аўтараў: калі вам гэта падабаецца і вы можаце сабе гэта дазволіць, падумайце аб куплі арыгінала або аб прамой падтрымцы аўтараў."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Падтрымлівайце бібліятэкі: калі такая кніга ёсць у вашай мясцовай бібліятэцы, вазьміце яшчэ іх бясплатна."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Няма магчымасці атрымаць непасрэдна ў вялікім аб'ёме, толькі ў паўаб'ёме за платным доступам"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Архіў Анны кіруе калекцыяй <a %(isbndb)s>метададзеных ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb — гэта кампанія, якая збірае метаданыя ISBN з розных інтэрнэт-кнігарняў. Архіў Анны робіць рэзервовыя копіі метаданых кніг ISBNdb. Гэтыя метаданыя даступныя праз Архіў Анны (хоць у цяперашні час не ў пошуку, за выключэннем выпадкаў, калі вы яўна шукаеце нумар ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Для тэхнічных дэталяў глядзіце ніжэй. У нейкі момант мы можам выкарыстоўваць гэта для вызначэння, якіх кніг яшчэ не хапае ў ценявых бібліятэках, каб прыярытэзаваць, якія кнігі знайсці і/або адсканаваць."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Наш блог пра гэтыя дадзеныя"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Збор ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "У цяперашні час у нас ёсць адзін торэнт, які змяшчае 4,4 ГБ сціснуты файл <a %(a_jsonl)s>JSON Lines</a> (20 ГБ у распакаваным выглядзе): «isbndb_2022_09.jsonl.gz». Каб імпартаваць файл «.jsonl» у PostgreSQL, вы можаце выкарыстоўваць нешта накшталт <a %(a_script)s>гэтага скрыпту</a>. Вы нават можаце накіраваць яго непасрэдна, выкарыстоўваючы нешта накшталт %(example_code)s, каб ён распакоўваўся на ляту."

#~ msgid "page.donate.wait"
#~ msgstr "Калі ласка, пачакайце як мінімум <span %(span_hours)s>дзве гадзіны</span> (і абнавіце гэтую старонку), перш чым звязацца з намі."

#~ msgid "page.codes.search_archive"
#~ msgstr "Шукаць у Архіве Анны “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Ахвяраваць з дапамогай Alipay або WeChat. Вы можаце выбраць паміж імі на наступнай старонцы."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Распаўсюджванне інфармацыі пра Архіў Анны ў сацыяльных сетках і на інтэрнэт-форумах, рэкамендуючы кнігі або спісы на AA, або адказваючы на пытанні."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Калекцыя фікшн разышлася, але ўсё яшчэ мае <a %(libgenli)s>торэнты</a>, хоць не абнаўлялася з 2022 года (у нас ёсць прамыя загрузкі)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Архіў Анны і Libgen.li сумесна кіруюць калекцыямі <a %(comics)s>коміксаў</a> і <a %(magazines)s>часопісаў</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Няма торэнтаў для калекцый рускай літаратуры і стандартных дакументаў."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Дадатковы кантэнт не даступны праз торэнты. Торэнты, якія знаходзяцца на сайце Libgen.li, з'яўляюцца люстэркамі іншых торэнтаў, пералічаных тут. Адзінае выключэнне — торэнты з мастацкай літаратурай, пачынаючы з %(fiction_starting_point)s. Торэнты з коміксамі і часопісамі выпускаюцца ў супрацоўніцтве паміж Архівам Анны і Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "З калекцыі <a %(a_href)s><q>Бібліятэка Аляксандрына,</q></a> дакладнае паходжанне не ясна. Часткова з the-eye.eu, часткова з іншых крыніц."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

