��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  �d 
  �h 7   �j �  �j    ]l   ~o ]  �r �  �t �   �v �   Aw Q   �w �   4x �  �x W  �{ �  	~ ,  � �  ΂ s  s� !  � l  	� y  v� }  ��   n� r   r� �  � �   v� �  � (   � }  5� Q   �� 6   �    <� $   T� \   y� *   ֠ "   � a   $� <   �� C   á    � x   '� �  �� �  �� �   h� Z   �    J�    V� %   b�    �� !   ��    �� 	   ç    ͧ    � 5   �    &� #   ,� 	   P� 
   Z�    h� <   t� 8   �� %   � 8  � E  I�   �� 7   �� +  ̰ �   �� �  � ?  �� (   ʶ �   � (   ߷ 	  � -   � �  @� (   /� �  X� M   � �   \�    ��    ��   �� �  � �  ��    v� �   �� �   '� 6  �� M   � M   Q� �   �� b   g� o   �� R   :�    �� �   �� �  T� s   G� �   �� @  �� �   �� �  `� w   � �  �� �   1� <  � �   R� B  �� �   � Q   � �  b� F  � �   X� W   � �   Y�    $�   =� �  X� Q   2�    �� )  �� {  ��    ?� #  N� �   r�    g� U  �� �   �� R  �� �  1� {   � �   �� �   � �   �� �   k� �   � J   �� �   �� �   �� �   d�    �� �   � �   �� A  g�   �� !   �� �  �� �  �  m  J �  � �   ] �  8   �	 
  � �   
   �
 �  
   � �  �     �   � X  �   � �  � �   � �   o �   l �  M �      � �  ! C   �" �   "# �   �# �   �$ z  Q% �   �' �   �( �   Z) E   %* \  k* j  �-   3/ !   �1 �   �1 �   �2 u  �3 �  5 u  �6 �  8 z  ; /   �= �  �=   8@    JD g  KF (  �J �  �L �   �N �   @O    8P W  EP   �Q �  �S �  XV �  *X   �Y    �Z K  �Z   F] �  K_    �`   �` 	  �a �  	d X  �e J  �h �   =j    �j g  �j #  @m    do �  |o �  q B  �r i  v p  �w �   �y �   ~z }  { C   �|    �| :  �| �  ' �  � W   ͂ Q   %� s  w� �   � �   ͇ �   �� �  O� 3   �    C� �  a� E  � �   ^� �   -� �   �� B   i� �  �� �  ��    G�    a� X   v� m   ϖ u   =� Z   �� �   � �   ǘ !   |� M   �� �   � �   v� M   � T   Y� R   �� �   �    �� @   � �  G�   
� �  � �  �� �   o� p  k�   ܦ #   �� �   � �   �� ,  �� 2  �� a  � �  D� �  д �  Ƿ Q   �� [   � D   G� '  �� _  �� �  � �  �� 4  �� �  !� �  �� �  }� K   I� �  ��   �� k  ��    � �  (� 5  �� #   � :  $� �  _� 
   �� =   �� �   9�    �� !  �� Q  � �  l�   a� \  q� %   �� �  �� �  �� Q   X� �   �� �  <� �  �� �   �� N  �� �   �� �  }� %   �� �  %�    �� K  �� �   �  
 L  �    �	 �  �	   �    � &  �   � �   �   �  � 4  B Q   w �  � n  r" �   �# f   �$ �   %    �% M   �% /   �% ;   -& 2   i& #   �& H  �& �  	( A  �, �  �/ e  �3    �4   5 �  1: b  > �  ~@ �  LC n  CG    �K �  �K    LM    [M �  zM �  nP   7T �  �W !   �[ �  �[   6_ d  Nb �  �e �  di �  4n �  /r /   �t 5  �t �   +v �  �v   n{ \  r~ �   �   �� S  �� "   � )  .� n   X� �   ǅ *   J� P   u� ]   Ɔ 
  $�   2� �  L� c   2� q  ��   � 
   !� ;  ,� m   h� �   ֚ Z  �� N   � �   T�    
� )  *� �  T�   � �  
� �   ֩    �� �   �� �  k�    d�   s� p  ��   �� `   � Q   g� 1  �� 7   � �  #� �  � :  �� 3   �� �   � �   �� �  �� �  �� &   E� s  l� ?   �� S   � 7  t� �  �� b  �� �   �� 7   ~� z  �� R   1� X  �� 
  �� �  �� �  p� �   !� `   ��   '� �  -� _  �� �  >� 5  2� _   h� s  �� �  <� �  � 9  �   K� �  i� 7   �� �  -� E   %� �  k� �   Y� .   �� �  &� �  � �  D �  �   �
 �  �   � �   � %  � �   �    �  � O  �! V   $ �   t$ �   <% "   
&    -&   >& �   D( x   /) �  �) �  >+ �  �- *  �/ r  �2 +  95 h  e6 �  �8    �: +  �:    < �  < E   �> 3   ?    Q? !   W? ?   y?    �? )   �? (   @ 
   ,@    7@    N@    c@    t@    �@ 1   �@    �@ W   �@    9A    BA    QA    `A \  mA �   �B :   gC    �C 1   �C ?   �C M   1D \   D >   �D    E    0E 4   =E 8   rE    �E "   �E    �E    �E 
   F ,    F P   MF �   �F I    G e   jG i   �G D   :H @   H !   �H 3   �H g   I 5   ~I �   �I �   gJ 
   �J �   �J �   �K    4L    PL &   fL    �L '   �L <   �L    M !   0M    RM !   ZM    |M    �M    �M 	   �M 
   �M    �M A   �M    
N    N 	   N !   'N 	   IN    SN    pN    vN 	   }N    �N    �N <   �N    �N    �N    O    &O    .O 	   EO    OO -   lO 
   �O 
   �O D   �O    �O .   P    3P /   BP 
   rP    }P D   �P �   �P 
  �Q �   �R !  2S �  TT �   W    �W <   �W &   X    CX -   JX    xX    �X ?   �X �   �X X   pY �   �Y =   bZ h   �Z B   	[ �   L[ �   �[ v  �\ �   ^ q   �^ V   �^     V_    w_    �_    �_    �_    �_    �_    �_    `    `    4`    T` !   p` -   �`    �` '   �`    �`    a 
   ;a    Fa    Ua    da 0   ya     �a �  �a    }c 
   �c    �c %   �c    �c �   �c t   Ud 0   �d L   �d K   He    �e    �e    �e �   �e    �f    �f C   �f �   �f %   �g    �g �   �g G   �h �  .i �   �m �   xn    po �  qp S   �q �  Kr �   t �  �t v  �w J   �x    Dy �   Qy u   �y ~   Uz o   �z �   D{ Y   �{ �   >| 1   �| D   $}    i} *   z} �   �} 9   &~    `~    z~    �~ (   �~ �   �~    � H   � �   � ?   �� 9   ۀ p   � �   �� 9  >� /   x� !   �� $  ʄ    � 
    �    � *   �    E� A   T�   �� &   ��    ��    Ԉ    �   �� 2   � 
   >�    L� �   [�    �    �� C   � %   G�    m� 9   v� �   �� .   ��    ȍ �   � 0   ��    ��    ӎ 2   � e   � �   }� 2   X� K   ��   א �   � ~   ��    � �  /� �   � !   �� `   �� 8   � 7  S� U  �� &   � �   �   �� P  �� 2   �� 1   *� %   \� �  �� D   p� 4   �� )   � 9   � 7   N�   �� -   �� "   �� K   ڡ Y   &�    �� ;   �� 0   ˢ 2   �� �  /� 9  �� V  �� a   M� B   ��    � ;   �� �  ;� �  � $  �� 2   �� h  ޱ ~  G� A   Ƶ :   � �  C� �  Ĺ    ��    �� E   ͻ    �   "� -   (�   V� �  u� �  A�    �� �   � =   �� ?   � �   \�   �� �  ��   }� ?  �� �   �� �  �� r   Q� 7  ��   �� �   �   �� \   �� V   � *   l�    �� %   �� A   �� =   � �   V� S   �� 	   C� E   M� �  �� =   �� �  �� �   Q� �   )� 4   �� #   +� /   O�    � 8   �� '   �� 8   �� )   6� D  `� -   �� 1  ��    � D  � �   d� �   (� �  � }  �� >   x� �   �� 	   ��   �� �   �� 3   �� �  ��    �� '   �� #   �� 0   �� B   &� 
   i�    t� w   �� 6  �� ~  2� 
   �� #   �� /   ��   �   0�   J� �   b�    �    .� $   L�     q�    �� &   ��    �� m   �� M   >� 6  �� �   �� #   b� �   �� �   9� ^   �� �   @� {   �� j   v  
   �  �   �  X   � �   � v   � ^   @ #   � Q  � �    b   � �   � w   � Z   �    V W   i �   � �   R K   	 �   d	    _
 �  n
 �   M w   � �   \
     >  1 {  p -   � )       D 
   S �  ^ R   4 �   � 9     B �   E +  +   W �   f   [ �   s �   �   � w  L  -  �" ^  �# 
   Q% 
   _% 
   m% �   {% 
   L& 
   Z& v   h& �   �& G  k' 
   �( �   �( r   r) �   �) ]   �* �   �* r   �+ 
   , �   , 
   - 
   - 
   - 9  ,- �   f0 �  '1    �3    �3    �3   4 '   5 2   @5 {  s5 �   �6 ;   �7    "8 /   28 `   b8 Q   �8 K   9 <   a9 <   �9 m  �9 1   I; �  {; �  > �   �? �   �@   !A �  7B 2   D �  3G 3  �J �   �K �   �L    �M i  �M /   P \  MP ~  �R �  )T �   �U   �V ,  �X �   �Y �   �Z M   _[ c   �[    \ P   +\    |\    �\    �\ t  �\    3^ �   F^ X   �^    S_    h_    {_ "   �_ �   �_ �   �` }   ,a e  �a M   c    ^c    tc !   �c J   �c    �c    d    d    'd    7d    Fd    Xd    id R   {d �   �d    te    �e    �e %   �e    �e    �e $   �e %   $f    Jf    hf 1   �f �   �f    |g S   �g �   �g   �h   �i   �j �  �k   �m   �n 9   �p �   q V   �q �   Lr l   �r G  Bs �  �t �   .v �   �v    �w �   �w �   ox �   7y    z     z *   0z .   [z    �z V   �z    { 8   {    G{ .   P{ 2   { E   �{    �{    |     2|    S|    Z|    t|    �| #   �|    �| B   �| T   �| �   L} �   
~ I   �~ 8    �   L �   � �   �� d   :� U   �� H   �� O   >� s   �� F   � }  I� �  Ǆ �  �� 2   R� g   �� �   � c   ĉ �  (� p  ԋ �   E� �   #� *   ��   Վ #  � �   � G   � �   -� {  �� 2   t� c   �� 3   � f   ?� �   �� �   a�    �    '�    0� @   7� �   x� L   _� 6   �� Q   � 8   5� <   n� +   �� k   י 0   C� �   t� ^   � �  a� |   #� m   �� u  � M   �� *   ҟ %   �� (   #� '   L� 9   t� )   �� *   ؠ l   � x   p� �  � �   �� >   .� Q   m� T   �� !   � �   6� �   �� |  ڨ q  W�    ɫ �   ٫ T   Ѭ *   &� }  Q� <   Ϯ O   � r   \� ^   ϯ �   .� �   ڰ �   �� +   � &   :�   a� K   s� 1   �� �   � x  �� d   � �   p� �   f� �   � �   �� L   �� l   ι �   ;� "   � �   *� /   Ի 3  � Q   8� {   �� &   � U   -� �  �� �   �    �� �   %� �   � |   ��   9�   A�    T� -   e� �   �� �   � 0   �� >   ��    � "   %�    H� 3   c� �   �� �   R� !   �� .   �� 7   -� <   e� G   �� �   �� Z   �� �   2� �   � D   �� �   �� n  �� @   ;� �   |�    � m   � d   �� 4   �� �   %� �   �� ;   �� �   �    �� B   �� �   ��    l� �   �� 7   Z�    �� A   �� �   �� m  �� _   +�    ��    �� _   ��    '� �   E� _   ��   /� �   @�    � d   .� (  �� %   ��   �� f   �� 1   [� �   �� r  .� 
   ��    ��    �� )   �� F   �� ^   $� �   �� 0   �     6�    W� ,   n� g   �� a   � 
   e� ~   p� e   ��    U� .   t� M   ��    ��    	� �   )� �  �� z   L�    ��    ��   ��   �� �   ��    �� d  �� �  � T   �� �   �� ,   ��   �� L   �� �   G� $   ��    �   ,� .   I� �   x� �   � z   �� �   F� B   �� �   +� e   �� !   "� �   D� |   �� O   R  m   �  E    �   V 	  � 5  � 7   ) '  a �  � 2   d   H	 A  �	 �  � �  �
    \  & C   � 2   � -  � D   ( 5  m �   � k   Y    � 4   � �      � R  � �  * �  
   �  q   �" �   2# �   �# J   O$ S   �$ t   �$ �   c%    & 5   & a   O& ;   �& &   �& `   ' �   u' T   -(    �( �   �( U  3) 1  �* '   �+ &   �+ 
   
, ]   , �   s, �   - 4  �- �   �.    {/ =   �/ �  �/    ]1 �   p1 p  +2 �  �6 t   Q8 =   �8    9    
9    9 \   9 L   z9 �   �9 �  �: �   8<    �<    �< ,   = &   <= �   c=    > J   .>    y> e   �> W   �>    H?    ]? �   r?    @ '   @ >   7@ =   v@    �@ �   �@ ]  �A �   �B �   �C �   [D F  �D    )F ?   >F I  ~F #  �G M  �H    :J �   OJ �   K |   �K �   L �   �L �   uM /   N �   JN %   �N -    O +   NO /   zO 2   �O )   �O -   P "   5P    XP    sP =   �P =   �P D    Q 1   EQ O   wQ T   �Q ;   R /   XR    �R b   �R 0   S    9S    JS U   �S �    T K   �T    U (   :U 6   cU '   �U '   �U �   �U �   �V �   W �  �W "   tY (   �Y $   �Y    �Y j    Z "   kZ L   �Z H   �Z   $[    4\ #   T\    x\ 
   \ 	   �\ ]   �\ )   �\ �  ] '   �^ 9   �^ 0   _ +   D_ 9   p_ (   �_ :   �_ <   ` @   K` O   �` r   �`    Oa �   Va T   b    Xb    wb J   �b p   �b >   Dc 9   �c C   �c �   d �   �d �   �e    *f +   7f 	   cf    mf D   �f 0   �f �  �f   �i 0   �j    �j 6   �j    k /   2k    bk    gk �   xk ?   <l I  |l -   �m 7   �m �   ,n 5   �n 1   -o 4   _o 7   �o G   �o 7   p    Lp    hp T   �p )   �p B   �p \   Bq   �q �   �s s   /t �   �t 6   4u 
  ku 4   vv    �v �   �v !   Qw M   sw A   �w w   x �   {x H   y 0  Ny !   z )   �z .   �z    �z    {    6{ ;   S{    �{     �{ �   �{ �  g| 5   ~ 7   T~ �  �~ 7  .� 3   f� ?   ��    ځ    � '   ��    %�    8�    Q�    f�    x�    ��    �� 
   ��    ��    Ȃ    ڂ    �    � K   
� X  V� �  �� �  J� W  ܈   4� �  D� K  ː    � �  � -   � �  � z  ̖ �  G� M  )� h  w� M   �� �   .� \   %� %   �� n   �� �   � �   �� �   "� 
  �� �   ̣ V  �� �  �     �� V  �� �  � �   ��   B� "   J� n  m� �  ܯ ^  ~� S  ݳ    1� �   K� �   � T  �� �   � �   ո �   ��    T� 8   p� 8   �� ~   � S   a�    �� �   ӻ f   �� �   �� ;   |�   ��   �� �   ڿ �   }� �   
� ~   �� �   "� �   �� �   6� �   � �   ��   x�    �� e   �� W   � �   Y� C   ��    9� 
   N� p   Y� 2   �� '   ��    %� h   4� c   �� d   � #   f�    ��    ��    �� 	   �� �   �� �   G� �   �� P   ��    �� 7   �� U   "�    x�    ��    �� 
   ��    ��    ��    ��    ��    ��    �    �    2�    R�    c� 1   {�    ��    ��    ��    ��    � )   � /   ?� 2   o� �   ��    `� �   � R  �    k�    ~�    ��    �� 
   ��    ��    ��   �� �   �� h   ��    � )   &� �   P� %   � �   9� �   � E   �� 3   7� �   k� D  N� �   �� �   "� �   � 3   ��   0� /   K� 0   {� i   �� �   � 1   � �   :� �   � F  �� i   �    �� 
   �� #   ��    �� &   ��    �    �    .� �   L� �   E� �   	� 9  �� n  � �   �� i   #� �  �� �  e� �  �� �   �� �  �� -  I�    w� ;  �� �  �� c  �� G  �� E  F� �   � Z  S %   � l   �   A 9  P d   �	 }  �	    m    v #   � �   � s   � �   
 O   �
 �    t   � �   % 5   �   � _     3   ` �   � x  & 0   �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: be
Language-Team: be <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library — гэта папулярная (і незаконная) бібліятэка. Яны ўзялі калекцыю Library Genesis і зрабілі яе лёгка даступнай для пошуку. Акрамя таго, яны сталі вельмі эфектыўнымі ў прыцягненні новых кніг, стымулюючы карыстальнікаў рознымі перавагамі. У цяперашні час яны не вяртаюць гэтыя новыя кнігі ў Library Genesis. І ў адрозненне ад Library Genesis, яны не робяць сваю калекцыю лёгка люстранай, што перашкаджае шырокаму захаванню. Гэта важна для іх бізнес-мадэлі, паколькі яны бяруць грошы за доступ да іх калекцыі ў вялікіх аб'ёмах (больш за 10 кніг у дзень). Мы не выносім маральных ацэнак наконт спагнання грошай за масавы доступ да незаконнай калекцыі кніг. Несумненна, што Z-Library паспяхова пашырыла доступ да ведаў і атрымала больш кніг. Мы проста тут, каб зрабіць сваю частку: забяспечыць доўгатэрміновае захаванне гэтай прыватнай калекцыі. - Анна і каманда (<a %(reddit)s>Reddit</a>) У арыгінальным выпуску Люстра Пірацкай Бібліятэкі (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>), мы зрабілі люстра Z-Library, вялікай незаконнай калекцыі кніг. Як напамін, вось што мы напісалі ў тым арыгінальным блогу: Гэтая калекцыя датуецца сярэдзінай 2021 года. Тым часам Z-Library расце з уражлівай хуткасцю: яны дадалі каля 3,8 мільёна новых кніг. Там ёсць некаторыя дублікаты, але большасць з іх, здаецца, сапраўды новыя кнігі або больш якасныя сканы раней прадстаўленых кніг. Гэта ў значнай ступені дзякуючы павелічэнню колькасці валанцёраў-мадэратараў у Z-Library і іх сістэме масавай загрузкі з дэдуплікацыяй. Мы хацелі б павіншаваць іх з гэтымі дасягненнямі. Мы рады паведаміць, што атрымалі ўсе кнігі, якія былі дададзены ў Z-Library паміж нашым апошнім люстэркам і жніўнем 2022 года. Мы таксама вярнуліся і сабралі некаторыя кнігі, якія прапусцілі ў першы раз. У цэлым, гэтая новая калекцыя складае каля 24 ТБ, што значна больш, чым папярэдняя (7 ТБ). Наше люстэрка цяпер складае 31 ТБ у агульнай складанасці. Зноў жа, мы правялі дэдуплікацыю з Library Genesis, паколькі ўжо ёсць торэнты для гэтай калекцыі. Калі ласка, наведайце Pirate Library Mirror, каб азнаёміцца з новай калекцыяй (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>). Там ёсць больш інфармацыі пра тое, як структураваны файлы і што змянілася з мінулага разу. Мы не будзем спасылацца на гэта адсюль, паколькі гэта проста блог-сайт, які не размяшчае ніякіх незаконных матэрыялаў. Вядома, сідынг таксама з'яўляецца выдатным спосабам дапамагчы нам. Дзякуй усім, хто сідзіруе наш папярэдні набор торэнтаў. Мы ўдзячныя за пазітыўны водгук і рады, што так шмат людзей клапоцяцца пра захаванне ведаў і культуры такім незвычайным спосабам. 3x новыя кнігі дададзены ў Люстра Пірацкай Бібліятэкі (+24TB, 3,8 мільёна кніг) Прачытайце суправаджальныя артыкулы ад TorrentFreak: <a %(torrentfreak)s>першы</a>, <a %(torrentfreak_2)s>другі</a> - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) суправаджальныя артыкулы ад TorrentFreak: <a %(torrentfreak)s>першы</a>, <a %(torrentfreak_2)s>другі</a> Не так даўно "ценявыя бібліятэкі" былі на мяжы знікнення. Sci-Hub, велізарны незаконны архіў навуковых артыкулаў, спыніў прыём новых прац з-за судовых пазоваў. "Z-Library", самая вялікая незаконная бібліятэка кніг, убачыла, як яе меркаваныя стваральнікі былі арыштаваны па крымінальных абвінавачваннях у парушэнні аўтарскага права. Яны неверагодным чынам змаглі ўцячы ад арышту, але іх бібліятэка ўсё яшчэ пад пагрозай. Некаторыя краіны ўжо робяць версію гэтага. TorrentFreak <a %(torrentfreak)s>паведаміў</a>, што Кітай і Японія ўвялі выключэнні для штучнага інтэлекту ў свае законы аб аўтарскім праве. Нам не зразумела, як гэта ўзаемадзейнічае з міжнароднымі дагаворамі, але гэта, безумоўна, дае абарону іх унутраным кампаніям, што тлумачыць тое, што мы назіраем. Што тычыцца Архіва Анны — мы працягнем нашу падпольную працу, заснаваную на маральных перакананнях. Але наша самая вялікая жаданне — выйсці на святло і легальна ўзмацніць наш уплыў. Калі ласка, рэфармуйце аўтарскае права. Калі Z-Library сутыкнулася з закрыццём, я ўжо зрабіў рэзервовую копію ўсёй яе бібліятэкі і шукаў платформу для яе размяшчэння. Гэта была мая матывацыя для стварэння Архіва Анны: працяг місіі, якая стаяла за гэтымі ранейшымі ініцыятывамі. З тых часоў мы выраслі да самай вялікай ценявой бібліятэкі ў свеце, якая змяшчае больш за 140 мільёнаў тэкстаў, абароненых аўтарскім правам, у розных фарматах — кнігі, навуковыя артыкулы, часопісы, газеты і іншае. Мая каманда і я — ідэолагі. Мы лічым, што захаванне і размяшчэнне гэтых файлаў маральна правільнае. Бібліятэкі па ўсім свеце сутыкаюцца са скарачэннем фінансавання, і мы таксама не можам даверыць спадчыну чалавецтва карпарацыям. Потым з'явіўся штучны інтэлект. Амаль усе буйныя кампаніі, якія ствараюць LLM, звязаліся з намі, каб навучацца на нашых дадзеных. Большасць (але не ўсе!) кампаній з ЗША перагледзелі сваё рашэнне, калі зразумелі незаконную прыроду нашай працы. У адрозненне ад гэтага, кітайскія кампаніі з энтузіязмам прынялі нашу калекцыю, відавочна, не турбуючыся пра яе законнасць. Гэта варта адзначыць, улічваючы ролю Кітая як падпісанта амаль усіх асноўных міжнародных дагавораў аб аўтарскім праве. Мы далі высокахуткасны доступ каля 30 кампаніям. Большасць з іх — кампаніі LLM, а некаторыя — брокеры дадзеных, якія будуць перапрадаваць нашу калекцыю. Большасць з іх — кітайскія, хоць мы таксама працавалі з кампаніямі з ЗША, Еўропы, Расіі, Паўднёвай Карэі і Японіі. DeepSeek <a %(arxiv)s>прызнала</a>, што ранейшая версія была навучана на частцы нашай калекцыі, хоць яны маўчаць пра сваю апошнюю мадэль (верагодна, таксама навучаную на нашых дадзеных). Калі Захад хоча заставацца наперадзе ў гонцы LLM і, у канчатковым выніку, AGI, яму трэба перагледзець сваю пазіцыю адносна аўтарскага права, і хутка. Незалежна ад таго, ці згодныя вы з намі ў нашым маральным выпадку, гэта цяпер становіцца пытаннем эканомікі і нават нацыянальнай бяспекі. Усе магутныя блокі ствараюць штучных супер-навукоўцаў, супер-хакераў і супер-вайскоўцаў. Свабода інфармацыі становіцца пытаннем выжывання для гэтых краін — нават пытаннем нацыянальнай бяспекі. Наша каманда з усяго свету, і ў нас няма пэўнай арыентацыі. Але мы б заклікалі краіны з моцнымі законамі аб аўтарскім праве выкарыстоўваць гэтую экзістэнцыяльную пагрозу для іх рэфармавання. Дык што рабіць? Наша першая рэкамендацыя простая: скараціць тэрмін дзеяння аўтарскага права. У ЗША аўтарскае права прадастаўляецца на 70 гадоў пасля смерці аўтара. Гэта абсурдна. Мы можам прывесці гэта ў адпаведнасць з патэнтамі, якія прадастаўляюцца на 20 гадоў пасля падачы заяўкі. Гэта павінна быць больш чым дастаткова часу для аўтараў кніг, артыкулаў, музыкі, мастацтва і іншых творчых прац, каб атрымаць поўную кампенсацыю за свае намаганні (уключаючы доўгатэрміновыя праекты, такія як экранізацыі). Затым, як мінімум, палітыкі павінны ўключыць выключэнні для масавага захавання і распаўсюджвання тэкстаў. Калі асноўнай праблемай з'яўляецца страта даходу ад індывідуальных кліентаў, распаўсюджванне на асабістым узроўні можа заставацца забароненым. У сваю чаргу, тыя, хто здольны кіраваць вялікімі сховішчамі — кампаніі, якія навучаюць LLM, разам з бібліятэкамі і іншымі архівамі — будуць ахоплены гэтымі выключэннямі. Рэформа аўтарскага права неабходная для нацыянальнай бяспекі Кароткі змест: Кітайскія LLM (уключаючы DeepSeek) навучаюцца на маім незаконным архіве кніг і артыкулаў — самым вялікім у свеце. Захад павінен перагледзець заканадаўства аб аўтарскім праве ў інтарэсах нацыянальнай бяспекі. Калі ласка, глядзіце <a %(all_isbns)s>арыгінальны блог</a> для больш падрабязнай інфармацыі. Мы абвясцілі выклік для паляпшэння гэтага. Мы прапанавалі ўзнагароду за першае месца ў памеры $6,000, за другое месца — $3,000, і за трэцяе месца — $1,000. З-за велізарнага водгуку і неверагодных падач мы вырашылі крыху павялічыць прызавы фонд і ўзнагародзіць чатыры трэція месцы па $500 кожнае. Пераможцы ніжэй, але абавязкова паглядзіце ўсе падачы <a %(annas_archive)s>тут</a>, або спампуйце наш <a %(a_2025_01_isbn_visualization_files)s>аб'яднаны торэнт</a>. Першае месца $6,000: phiresky Гэтая <a %(phiresky_github)s>падача</a> (<a %(annas_archive_note_2951)s>каментар Gitlab</a>) з'яўляецца проста ўсім, што мы хацелі, і нават больш! Асабліва нам спадабаліся неверагодна гнуткія варыянты візуалізацыі (нават з падтрымкай карыстальніцкіх шэйдараў), але з поўным спісам прэсэтаў. Нам таксама спадабалася, наколькі хутка і плаўна ўсё працуе, простая рэалізацыя (якая нават не мае бэкэнда), разумная мінікарта і шырокае тлумачэнне ў іх <a %(phiresky_github)s>блог-паведамленні</a>. Неверагодная праца і заслужаны пераможца! - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Нашы сэрцы поўныя ўдзячнасці. Значныя ідэі Нябёсы для рэдкасці Шмат паўзункоў для параўнання Datasets, як быццам вы DJ. Шкала з колькасцю кніг. Прыгожыя этыкеткі. Крутая стандартная каляровая схема і цеплавая карта. Унікальны выгляд карты і фільтры Анатацыі, а таксама жывая статыстыка Жывая статыстыка Некаторыя іншыя ідэі і рэалізацыі, якія нам асабліва спадабаліся: Мы маглі б працягваць яшчэ доўга, але давайце спынімся тут. Абавязкова паглядзіце ўсе падачы <a %(annas_archive)s>тут</a>, або спампуйце наш <a %(a_2025_01_isbn_visualization_files)s>аб'яднаны торэнт</a>. Так шмат падач, і кожная прыносіць унікальную перспектыву, ці то ў інтэрфейсе, ці ў рэалізацыі. Мы, па меншай меры, уключым падачу, якая заняла першае месца, у наш асноўны сайт, і, магчыма, некаторыя іншыя. Мы таксама пачалі думаць пра тое, як арганізаваць працэс выяўлення, пацверджання і затым архівавання самых рэдкіх кніг. Больш падрабязнасцяў будзе пазней. Дзякуй усім, хто ўдзельнічаў. Гэта дзіўна, што так шмат людзей клапоцяцца. Лёгкае пераключэнне Datasets для хуткіх параўнанняў. Усе ISBN CADAL SSNOs Уцечка дадзеных CERLALC DuXiu SSIDs Індэкс eBook ад EBSCOhost Google Books Goodreads Інтэрнэт-архіў ISBNdb Глабальны рэестр выдаўцоў ISBN Libby Файлы ў Архіве Анны Nexus/STC OCLC/Worldcat OpenLibrary Расійская дзяржаўная бібліятэка Імперская бібліятэка Трантора Другое месца $3,000: hypha «Хоць ідэальныя квадраты і прастакутнікі матэматычна прывабныя, яны не забяспечваюць лепшую лакальнасць у кантэксце картаграфавання. Я лічу, што асіметрыя, уласцівая гэтым крывым Хільберта або класічным Мортанам, не з'яўляецца недахопам, а асаблівасцю. Як і знакаміты абрыс Італіі ў форме бота, які робіць яе адразу пазнавальнай на карце, унікальныя "асаблівасці" гэтых крывых могуць служыць пазнавальнымі арыенцірамі. Гэтая адметнасць можа палепшыць прасторавую памяць і дапамагчы карыстальнікам арыентавацца, патэнцыйна палягчаючы пошук канкрэтных рэгіёнаў або выяўленне шаблонаў». Яшчэ адна неверагодная <a %(annas_archive_note_2913)s>падача</a>. Не такая гнуткая, як першае месца, але нам на самай справе больш спадабалася яе макра-ўзроўневая візуалізацыя ў параўнанні з першым месцам (крывізна, межы, маркіроўка, вылучэнне, панарамаванне і маштабаванне). <a %(annas_archive_note_2971)s>Каментар</a> Джо Дэвіса рэзаніраваў з намі: І ўсё яшчэ шмат варыянтаў для візуалізацыі і рэндэрынгу, а таксама неверагодна плаўны і інтуітыўна зразумелы інтэрфейс. Цвёрдае другое месца! - Анна і каманда (<a %(reddit)s>Reddit</a>) Некалькі месяцаў таму мы абвясцілі <a %(all_isbns)s>прэмію ў $10,000</a> за лепшую магчымую візуалізацыю нашых дадзеных, якія паказваюць прастору ISBN. Мы падкрэслілі паказ, якія файлы мы ўжо архівавалі/не архівавалі, і пазней дадалі набор дадзеных, які апісвае, колькі бібліятэк утрымліваюць ISBN (меркаванне рэдкасці). Мы былі перагружаны адказам. Было столькі творчасці. Вялікі дзякуй усім, хто ўдзельнічаў: ваша энергія і энтузіязм заразлівыя! У рэшце рэшт, мы хацелі адказаць на наступныя пытанні: <strong>якія кнігі існуюць у свеце, колькі з іх мы ўжо архівавалі, і на якіх кнігах нам варта засяродзіцца далей?</strong> Прыемна бачыць, што так шмат людзей клапоцяцца пра гэтыя пытанні. Мы пачалі з базавай візуалізацыі самі. Менш чым у 300 кб гэтая карціна лаканічна прадстаўляе найбуйнейшы цалкам адкрыты "спіс кніг", калі-небудзь сабраны ў гісторыі чалавецтва: Трэцяе месца $500 #1: maxlion У гэтай <a %(annas_archive_note_2940)s>падачы</a> нам вельмі спадабаліся розныя віды праглядаў, у прыватнасці параўнальны і выдавецкі прагляды. Трэцяе месца $500 #2: abetusk Хоць інтэрфейс не самы адшліфаваны, гэтая <a %(annas_archive_note_2917)s>падача</a> адпавядае многім крытэрыям. Асабліва нам спадабалася яе функцыя параўнання. Трэцяе месца $500 #3: conundrumer0 Як і першае месца, гэтая <a %(annas_archive_note_2975)s>падача</a> уразіла нас сваёй гнуткасцю. У канчатковым выніку гэта тое, што робіць інструмент візуалізацыі выдатным: максімальная гнуткасць для магутных карыстальнікаў, захоўваючы пры гэтым прастату для звычайных карыстальнікаў. Трэцяе месца $500 #4: charelf Апошняя <a %(annas_archive_note_2947)s>падача</a>, якая атрымала ўзнагароду, даволі простая, але мае некаторыя унікальныя асаблівасці, якія нам вельмі спадабаліся. Нам спадабалася, як яны паказваюць, колькі набораў дадзеных ахопліваюць пэўны ISBN як меру папулярнасці/надзейнасці. Нам таксама вельмі спадабалася прастата, але эфектыўнасць выкарыстання паўзунка непразрыстасці для параўнанняў. Пераможцы прэміі ў $10,000 за візуалізацыю ISBN Кароткі змест: Мы атрымалі неверагодныя заяўкі на прэмію ў $10,000 за візуалізацыю ISBN. Фон Як можа «Архіў Анны» дасягнуць сваёй місіі па рэзервовым капіраванні ўсіх ведаў чалавецтва, не ведаючы, якія кнігі яшчэ існуюць? Нам патрэбны спіс задач. Адзін з спосабаў гэта зрабіць — праз нумары ISBN, якія з 1970-х гадоў прысвойваюцца кожнай апублікаванай кнізе (у большасці краін). Няма цэнтральнага органа, які ведае ўсе прысваенні ISBN. Замест гэтага гэта размеркаваная сістэма, дзе краіны атрымліваюць дыяпазоны нумароў, якія затым прысвойваюцца буйным выдаўцам, якія могуць далей падзяляць дыяпазоны на меншых выдаўцоў. Нарэшце, індывідуальныя нумары прысвойваюцца кнігам. Мы пачалі картаграфаваць ISBN <a %(blog)s>два гады таму</a> з нашым скрэйпінгам ISBNdb. З тых часоў мы скрэйпілі шмат іншых крыніц metadata, такіх як <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby і іншыя. Поўны спіс можна знайсці на старонках «Datasets» і «Torrents» на «Архіве Анны». Цяпер у нас самая вялікая цалкам адкрытая, лёгка загружальная калекцыя metadata кніг (і, такім чынам, ISBN) у свеце. Мы <a %(blog)s>шмат пісалі</a> пра тое, чаму нам важна захаванне, і чаму мы зараз знаходзімся ў крытычным акне. Мы павінны цяпер вызначыць рэдкія, недастаткова асветленыя і ўнікальна рызыкоўныя кнігі і захаваць іх. Добрае metadata на ўсе кнігі ў свеце дапамагае ў гэтым. Узнагарода $10,000 Вялікая ўвага будзе нададзена зручнасці выкарыстання і візуальнай прывабнасці. Паказвайце фактычнае metadata для індывідуальных ISBN пры маштабаванні, такіх як назва і аўтар. Лепшая кривая запаўнення прасторы. Напрыклад, зигзаг, які ідзе ад 0 да 4 у першым радку, а затым назад (у зваротным парадку) ад 5 да 9 у другім радку — рэкурсіўна прымяняецца. Розныя або наладжвальныя каляровыя схемы. Спецыяльныя прагляды для параўнання Datasets. Спосабы адладкі праблем, такіх як іншыя metadata, якія не вельмі добра супадаюць (напрыклад, значна розныя назвы). Анатацыя малюнкаў з каментарамі на ISBN або дыяпазонах. Любая эврыстыка для вызначэння рэдкіх або пад пагрозай кніг. Любыя творчыя ідэі, якія вы можаце прыдумаць! Код Код для генерацыі гэтых малюнкаў, а таксама іншыя прыклады, можна знайсці ў <a %(annas_archive)s>гэтым каталогу</a>. Мы прыдумалі кампактны фармат даных, з якім уся неабходная інфармацыя ISBN складае каля 75 МБ (у сціснутым выглядзе). Апісанне фармату даных і код для яго генерацыі можна знайсці <a %(annas_archive_l1244_1319)s>тут</a>. Для ўзнагароды вам не абавязкова выкарыстоўваць гэта, але гэта, верагодна, самы зручны фармат для пачатку. Вы можаце трансфармаваць нашы metadata як заўгодна (хаця ўвесь ваш код павінен быць з адкрытым зыходным кодам). Мы не можам дачакацца, каб убачыць, што вы прыдумаеце. Поспехаў! Форкніце гэты рэпазітар і адрэдагуйце гэты HTML блог-пост (ніякія іншыя бэкэнды, акрамя нашага Flask бэкэнда, не дазволены). Зрабіце малюнак вышэй плаўна маштабаваным, каб вы маглі маштабаваць да індывідуальных ISBN. Націск на ISBN павінен перанакіроўваць вас на старонку metadata або пошук у «Архіве Анны». Вы ўсё яшчэ павінны мець магчымасць пераключацца паміж усімі рознымі datasets. Дыяпазоны краін і выдаўцоў павінны вылучацца пры навядзенні курсора. Вы можаце выкарыстоўваць, напрыклад, <a %(github_xlcnd_isbnlib)s>data4info.py у isbnlib</a> для інфармацыі пра краіны і наш скрэйпінг «isbngrp» для выдаўцоў (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Гэта павінна добра працаваць на настольных і мабільных прыладах. Тут шмат чаго можна даследаваць, таму мы аб'яўляем узнагароду за паляпшэнне візуалізацыі вышэй. У адрозненне ад большасці нашых узнагарод, гэтая мае абмежаваны час. Вы павінны <a %(annas_archive)s>падаць</a> свой адкрыты код да 2025-01-31 (23:59 UTC). Лепшая падача атрымае $6,000, другое месца — $3,000, а трэцяе месца — $1,000. Усе ўзнагароды будуць выплачаны з выкарыстаннем Monero (XMR). Ніжэй прыведзены мінімальныя крытэрыі. Калі ніводная падача не адпавядае крытэрыям, мы ўсё роўна можам прысудзіць некаторыя ўзнагароды, але гэта будзе на нашым меркаванні. Для дадатковых балаў (гэта проста ідэі — дайце вашай творчасці свабоду): Вы МОЖАЦЕ цалкам адысці ад мінімальных крытэрыяў і зрабіць зусім іншую візуалізацыю. Калі гэта сапраўды ўражвае, то гэта кваліфікуецца для ўзнагароды, але па нашым меркаванні. Зрабіце падачы, пакідаючы каментар да <a %(annas_archive)s>гэтага пытання</a> з спасылкай на ваш форкнуты рэпазітар, запыт на зліццё або розніцу. - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Гэтая карціна мае памер 1000×800 пікселяў. Кожны піксель уяўляе сабой 2,500 ISBN. Калі ў нас ёсць файл для ISBN, мы робім гэты піксель больш зялёным. Калі мы ведаем, што ISBN быў выдадзены, але ў нас няма адпаведнага файла, мы робім яго больш чырвоным. Менш чым у 300 кб, гэтая карціна лаканічна ўяўляе сабой найбуйнейшы цалкам адкрыты "спіс кніг", калі-небудзь сабраны ў гісторыі чалавецтва (некалькі сотняў ГБ у сціснутым выглядзе). Гэта таксама паказвае: засталося шмат працы па рэзервовым капіраванні кніг (у нас толькі 16%). Візуалізацыя ўсіх ISBN — ўзнагарода $10,000 да 2025-01-31 Гэтая карціна ўяўляе сабой найбуйнейшы цалкам адкрыты "спіс кніг", калі-небудзь сабраны ў гісторыі чалавецтва. Візуалізацыя Акрамя агляднай выявы, мы таксама можам паглядзець на асобныя datasets, якія мы набылі. Выкарыстоўвайце выпадальнае меню і кнопкі, каб пераключацца паміж імі. У гэтых малюнках можна ўбачыць шмат цікавых узораў. Чаму існуе нейкая рэгулярнасць ліній і блокаў, якая, здаецца, адбываецца на розных маштабах? Што такое пустыя вобласці? Чаму пэўныя datasets так згрупаваныя? Мы пакінем гэтыя пытанні як практыкаванне для чытача. - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Заключэнне З гэтым стандартам мы можам рабіць выпускі больш паступова і лягчэй дадаваць новыя крыніцы дадзеных. У нас ужо ёсць некалькі захапляльных выпускаў у распрацоўцы! Мы таксама спадзяемся, што іншыя ценявыя бібліятэкі змогуць лягчэй люстраваць нашы калекцыі. У рэшце рэшт, наша мэта — захаваць чалавечыя веды і культуру назаўсёды, таму чым больш рэзервовых копій, тым лепш. Прыклад Давайце разгледзім наш нядаўні выпуск Z-Library як прыклад. Ён складаецца з дзвюх калекцый: “<span style="background: #fffaa3">zlib3_records</span>” і “<span style="background: #ffd6fe">zlib3_files</span>”. Гэта дазваляе нам асобна збіраць і выпускаць метададзеныя з рэальных файлаў кніг. Такім чынам, мы выпусцілі два торэнты з файламі метададзеных: Мы таксама выпусцілі некалькі торэнтаў з тэчкамі бінарных дадзеных, але толькі для калекцыі “<span style="background: #ffd6fe">zlib3_files</span>”, усяго 62: Запусціўшы <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, мы можам убачыць, што ўнутры: У гэтым выпадку гэта метададзеныя кнігі, як паведамляе Z-Library. На верхнім узроўні ў нас ёсць толькі “aacid” і “metadata”, але няма “data_folder”, паколькі няма адпаведных бінарных дадзеных. AACID змяшчае “22430000” у якасці асноўнага ID, які мы бачым, узяты з “zlibrary_id”. Мы можам чакаць, што іншыя AAC у гэтай калекцыі будуць мець такую ж структуру. Цяпер давайце запусцім <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Гэта значна меншыя метададзеныя AAC, хоць асноўная частка гэтага AAC знаходзіцца ў іншым месцы ў бінарным файле! У рэшце рэшт, у нас ёсць “data_folder” на гэты раз, таму мы можам чакаць, што адпаведныя бінарныя дадзеныя будуць размешчаны ў <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” змяшчае “zlibrary_id”, таму мы можам лёгка звязаць яго з адпаведным AAC у калекцыі “zlib_records”. Мы маглі б звязаць рознымі спосабамі, напрыклад, праз AACID — стандарт гэтага не прадпісвае. Звярніце ўвагу, што таксама не абавязкова, каб поле “metadata” само па сабе было JSON. Гэта можа быць радок, які змяшчае XML або любы іншы фармат дадзеных. Вы нават можаце захоўваць інфармацыю аб метададзеных у звязаным бінарным блобе, напрыклад, калі гэта шмат дадзеных. Гетэрагенныя файлы і metadata, як мага бліжэй да арыгінальнага фармату. Бінарныя даныя могуць быць абслугоўваны непасрэдна вэб-серверамі, як Nginx. Гетэрагенныя ідэнтыфікатары ў крынічных бібліятэках або нават адсутнасць ідэнтыфікатараў. Асобныя выпускі metadata супраць даных файлаў або выпускі толькі metadata (напрыклад, наш выпуск ISBNdb). Распаўсюджванне праз торэнты, хоць з магчымасцю іншых метадаў распаўсюджвання (напрыклад, IPFS). Незменныя запісы, паколькі мы павінны меркаваць, што нашы торэнты будуць жыць вечна. Паступовыя выпускі / дадаваемыя выпускі. Машыначытальныя і запісвальныя, зручна і хутка, асабліва для нашай стэкі (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Даволі лёгкая інспекцыя чалавекам, хоць гэта другасна ў параўнанні з машыначытальнасцю. Лёгка засяваць нашы калекцыі з дапамогай стандартнага арандаванага seedbox. Мэты дызайну Нам не важна, каб файлы было лёгка навігаваць уручную на дыску або шукаць без папярэдняй апрацоўкі. Нам не важна быць непасрэдна сумяшчальнымі з існуючым бібліятэчным праграмным забеспячэннем. Хоць павінна быць лёгка для любога засяваць нашу калекцыю з дапамогай торэнтаў, мы не чакаем, што файлы будуць выкарыстоўвацца без значных тэхнічных ведаў і абавязацельстваў. Наш асноўны выпадак выкарыстання — распаўсюджванне файлаў і звязаных з імі metadata з розных існуючых калекцый. Нашы найбольш важныя меркаванні: Некаторыя не-мэты: Паколькі Архіў Анны з'яўляецца адкрытым зыходным кодам, мы хочам выкарыстоўваць наш фармат непасрэдна. Калі мы абнаўляем наш індэкс пошуку, мы атрымліваем доступ толькі да публічна даступных шляхоў, каб любы, хто форкне нашу бібліятэку, мог хутка пачаць працу. <strong>AAC.</strong> AAC (Кантэйнер Архіва Анны) — гэта адзін элемент, які складаецца з <strong>metadata</strong>, і, па жаданні, <strong>бінарных даных</strong>, абодва з якіх нязменныя. Ён мае глабальна унікальны ідэнтыфікатар, які называецца <strong>AACID</strong>. <strong>AACID.</strong> Фармат AACID такі: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Напрыклад, фактычны AACID, які мы выпусцілі, гэта <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Дыяпазон AACID.</strong> Паколькі AACID ўтрымліваюць манатонна павялічваючыяся меткі часу, мы можам выкарыстоўваць гэта для абазначэння дыяпазонаў у межах пэўнай калекцыі. Мы выкарыстоўваем гэты фармат: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, дзе меткі часу ўключныя. Гэта адпавядае натацыі ISO 8601. Дыяпазоны бесперапынныя і могуць перакрывацца, але ў выпадку перакрыцця павінны ўтрымліваць ідэнтычныя запісы, як і раней выпушчаныя ў гэтай калекцыі (паколькі AAC нязменныя). Адсутныя запісы не дапускаюцца. <code>{collection}</code>: назва калекцыі, якая можа ўтрымліваць літары ASCII, лічбы і падкрэсліванні (але не двайныя падкрэсліванні). <code>{collection-specific ID}</code>: ідэнтыфікатар, спецыфічны для калекцыі, калі гэта дастасавальна, напрыклад, ID Z-Library. Можа быць апушчаны або скарачаны. Павінен быць апушчаны або скарачаны, калі AACID у адваротным выпадку перавысіць 150 сімвалаў. <code>{ISO 8601 timestamp}</code>: кароткая версія ISO 8601, заўсёды ў UTC, напрыклад, <code>20220723T194746Z</code>. Гэтае лікавае значэнне павінна манатонна павялічвацца для кожнага выпуску, хоць яго дакладная семантыка можа адрознівацца ў залежнасці ад калекцыі. Мы прапануем выкарыстоўваць час сканавання або генерацыі ID. <code>{shortuuid}</code>: UUID, але сціснуты да ASCII, напрыклад, з выкарыстаннем base57. У цяперашні час мы выкарыстоўваем бібліятэку Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Тэчка бінарных даных.</strong> Тэчка з бінарнымі данымі дыяпазону AAC для адной пэўнай калекцыі. Яны маюць наступныя ўласцівасці: Каталог павінен утрымліваць файлы даных для ўсіх AAC у межах указаннага дыяпазону. Кожны файл даных павінен мець сваё AACID у якасці імя файла (без пашырэнняў). Назва каталога павінна быць дыяпазонам AACID, з прэфіксам <code style="color: green">annas_archive_data__</code>, і без суфікса. Напрыклад, адзін з нашых фактычных выпускаў мае каталог з назвай<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Рэкамендуецца рабіць гэтыя тэчкі кіраванымі па памеры, напрыклад, не больш за 100 ГБ-1 ТБ кожная, хоць гэтая рэкамендацыя можа змяняцца з цягам часу. <strong>Калекцыя.</strong> Кожны AAC належыць да калекцыі, якая па вызначэнні з'яўляецца спісам AAC, якія семантычна паслядоўныя. Гэта азначае, што калі вы ўносіце значныя змены ў фармат метаданных, то вам трэба стварыць новую калекцыю. Стандарт <strong>Файл метаданных.</strong> Файл метаданных змяшчае метаданыя дыяпазону AAC для адной пэўнай калекцыі. Яны маюць наступныя ўласцівасці: <code>data_folder</code> з'яўляецца апцыянальным і з'яўляецца назвай тэчкі бінарных даных, якая змяшчае адпаведныя бінарныя даныя. Імя файла адпаведных бінарных даных у гэтай тэчцы — гэта AACID запісу. Кожны аб'ект JSON павінен утрымліваць наступныя палі на верхнім узроўні: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (апцыянальна). Іншыя палі не дапускаюцца. Імя файла павінна быць дыяпазонам AACID, з прэфіксам <code style="color: red">annas_archive_meta__</code> і суфіксам <code>.jsonl.zstd</code>. Напрыклад, адзін з нашых выпускаў называецца<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Як паказана пашырэннем файла, тып файла — гэта <a %(jsonlines)s>JSON Lines</a>, сціснуты з дапамогай <a %(zstd)s>Zstandard</a>. <code>metadata</code> — гэта адвольныя метаданыя, у адпаведнасці з семантыкай калекцыі. Яны павінны быць семантычна паслядоўнымі ў межах калекцыі. Префікс <code style="color: red">annas_archive_meta__</code> можа быць адаптаваны да назвы вашай установы, напрыклад, <code style="color: red">my_institute_meta__</code>. <strong>Калекцыі “запісаў” і “файлаў”.</strong> Па звычаі, часта зручна выпускаць “запісы” і “файлы” як розныя калекцыі, каб іх можна было выпускаць па розных графіках, напрыклад, на аснове хуткасці сканавання. “Запіс” — гэта калекцыя толькі метаданных, якая змяшчае інфармацыю, такую як назвы кніг, аўтары, ISBN і г.д., у той час як “файлы” — гэта калекцыі, якія змяшчаюць самі файлы (pdf, epub). У рэшце рэшт, мы спыніліся на адносна простым стандарце. Ён даволі гнуткі, не нарматыўны і знаходзіцца ў стадыі распрацоўкі. <strong>Торэнты.</strong> Файлы метададзеных і тэчкі з бінарнымі дадзенымі могуць быць аб'яднаны ў торэнты, з адным торэнтам на файл метададзеных або адным торэнтам на тэчку з бінарнымі дадзенымі. Торэнты павінны мець арыгінальную назву файла/каталога плюс суфікс <code>.torrent</code> у якасці назвы файла. <a %(wikipedia_annas_archive)s>Архіў Ганны</a> стаў, безумоўна, найбуйнейшай ценявой бібліятэкай у свеце і адзінай ценявой бібліятэкай такога маштабу, якая цалкам з адкрытым зыходным кодам і адкрытымі данымі. Ніжэй прыведзена табліца з нашай старонкі Datasets (трохі змененая): Мы дасягнулі гэтага трыма спосабамі: Адлюстраванне існуючых ценявых бібліятэк з адкрытымі данымі (такіх як Sci-Hub і Library Genesis). Дапамога ценявым бібліятэкам, якія хочуць быць больш адкрытымі, але не мелі часу або рэсурсаў для гэтага (напрыклад, калекцыя коміксаў Libgen). Скрапінг бібліятэк, якія не жадаюць дзяліцца ў вялікіх аб'ёмах (такіх як Z-Library). Для (2) і (3) мы цяпер кіруем значнай калекцыяй торэнтаў самастойна (сотні ТБ). Да гэтага часу мы разглядалі гэтыя калекцыі як адзіночныя выпадкі, што азначае індывідуальную інфраструктуру і арганізацыю даных для кожнай калекцыі. Гэта дадае значныя накладныя выдаткі да кожнага выпуску і робіць асабліва цяжкім выкананне больш паступовых выпускаў. Таму мы вырашылі стандартызаваць нашы выпускі. Гэта тэхнічны блог, у якім мы прадстаўляем наш стандарт: <strong>Кантэйнеры Архіва Анны</strong>. Архіў Ганны: стандартызацыя выпускоў з найбуйнейшай у свеце ценявой бібліятэкі Архіў Ганны стаў найбуйнейшай ценявой бібліятэкай у свеце, што патрабуе ад нас стандартызацыі нашых выпускоў. Выпушчана больш за 300 ГБ вокладак кніг Нарэшце, мы рады аб'явіць невялікі выпуск. У супрацоўніцтве з людзьмі, якія кіруюць форкам Libgen.rs, мы дзелімся ўсімі іх вокладкамі кніг праз торэнты і IPFS. Гэта размеркаваць нагрузку прагляду вокладак сярод большай колькасці машын і лепш захавае іх. У многіх (але не ва ўсіх) выпадках вокладкі кніг уключаны ў самі файлы, таму гэта свайго роду "вытворныя дадзеныя". Але наяўнасць іх у IPFS усё яшчэ вельмі карысна для штодзённай працы як Архіва Анны, так і розных форкаў Library Genesis. Як звычайна, вы можаце знайсці гэты выпуск у Пірацкай бібліятэчнай люстэрцы (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>). Мы не будзем спасылацца на яго тут, але вы можаце лёгка знайсці яго. Спадзяемся, мы зможам крыху расслабіць наш тэмп, цяпер, калі ў нас ёсць годная альтэрнатыва Z-Library. Гэтая нагрузка не з'яўляецца асабліва ўстойлівай. Калі вы зацікаўлены ў дапамозе з праграмаваннем, аперацыямі сервера або працай па захаванні, абавязкова звяжыцеся з намі. Яшчэ шмат <a %(annas_archive)s>работы трэба зрабіць</a>. Дзякуй за ваш інтарэс і падтрымку. Пераход на ElasticSearch Некаторыя запыты займалі вельмі шмат часу, да такой ступені, што яны захоплівалі ўсе адкрытыя злучэнні. Па змаўчанні MySQL мае мінімальную даўжыню слова, або ваш індэкс можа стаць вельмі вялікім. Людзі паведамлялі, што не могуць шукаць "Бэн Гур". Пошук быў толькі адносна хуткім, калі цалкам загружаны ў памяць, што патрабавала ад нас набыць больш дарагую машыну для запуску гэтага, плюс некаторыя каманды для папярэдняй загрузкі індэкса пры запуску. Мы не змаглі б лёгка пашырыць яго для стварэння новых функцый, такіх як лепшая <a %(wikipedia_cjk_characters)s>токенізацыя для моў без прабелаў</a>, фільтраванне/фасетаванне, сартаванне, прапановы "вы мелі на ўвазе", аўтазапаўненне і гэтак далей. Адзін з нашых <a %(annas_archive)s>квіткоў</a> быў зборнай сумкай праблем з нашай пошукавай сістэмай. Мы выкарыстоўвалі поўнатэкставы пошук MySQL, паколькі ў нас усе дадзеныя былі ў MySQL. Але ў яго былі свае абмежаванні: Пасля размовы з шэрагам экспертаў мы спыніліся на ElasticSearch. Гэта не было ідэальным (іх стандартныя прапановы "вы мелі на ўвазе" і функцыі аўтазапаўнення не вельмі добрыя), але ў цэлым гэта было значна лепш, чым MySQL для пошуку. Мы ўсё яшчэ не <a %(youtube)s>занадта ўпэўненыя</a> ў выкарыстанні яго для любых крытычна важных дадзеных (хоць яны зрабілі шмат <a %(elastic_co)s>прагрэсу</a>), але ў цэлым мы вельмі задаволеныя пераходам. На дадзены момант мы рэалізавалі значна хутчэйшы пошук, лепшую падтрымку моў, лепшае сартаванне па актуальнасці, розныя варыянты сартавання і фільтраванне па мове/тыпу кнігі/тыпу файла. Калі вам цікава, як гэта працуе, <a %(annas_archive_l140)s>паглядзіце</a> <a %(annas_archive_l1115)s>на</a> <a %(annas_archive_l1635)s>гэта</a>. Гэта даволі даступна, хоць магло б быць больш каментароў… Архіў Анны цалкам адкрыты Мы лічым, што інфармацыя павінна быць свабоднай, і наш уласны код не з'яўляецца выключэннем. Мы выпусцілі ўвесь наш код на нашым прыватна размешчаным Gitlab: <a %(annas_archive)s>Праграмнае забеспячэнне Анны</a>. Мы таксама выкарыстоўваем трэкер праблем для арганізацыі нашай працы. Калі вы хочаце ўдзельнічаць у нашай распрацоўцы, гэта выдатнае месца для пачатку. Каб даць вам уяўленне пра тое, над чым мы працуем, вазьміце нашу нядаўнюю працу па паляпшэнні прадукцыйнасці на баку кліента. Паколькі мы яшчэ не рэалізавалі пагінацыю, мы часта вярталі вельмі доўгія старонкі пошуку з 100-200 вынікамі. Мы не хацелі занадта рана абрэзаць вынікі пошуку, але гэта азначала, што гэта запавольвала некаторыя прылады. Для гэтага мы рэалізавалі невялікі трук: мы абгарнулі большасць вынікаў пошуку ў HTML каментарыі (<code><!-- --></code>), а затым напісалі невялікі Javascript, які вызначаў, калі вынік павінен стаць бачным, у гэты момант мы разгарнулі б каментар: Рэалізацыя "віртуалізацыі" DOM у 23 радках, без патрэбы ў складаных бібліятэках! Гэта той тып хуткага прагматычнага кода, які вы атрымліваеце, калі ў вас абмежаваны час і рэальныя праблемы, якія трэба вырашаць. Было паведамлена, што наш пошук цяпер добра працуе на павольных прыладах! Яшчэ адным вялікім намаганнем было аўтаматызаваць стварэнне базы дадзеных. Калі мы запусціліся, мы проста выпадкова сабралі розныя крыніцы разам. Цяпер мы хочам падтрымліваць іх у актуальным стане, таму мы напісалі шэраг скрыптоў для загрузкі новых metadata з двух форкаў Library Genesis і інтэграцыі іх. Мэта не толькі зрабіць гэта карысным для нашага архіва, але і зрабіць усё лёгкім для тых, хто хоча паэксперыментаваць з metadata ценявой бібліятэкі. Мэта — стварыць Jupyter notebook, які будзе мець усе віды цікавых metadata, каб мы маглі праводзіць больш даследаванняў, напрыклад, вызначаць, які <a %(blog)s>працэнт ISBN захоўваецца назаўсёды</a>. Нарэшце, мы абнавілі нашу сістэму ахвяраванняў. Цяпер вы можаце выкарыстоўваць крэдытную карту, каб непасрэдна ўнесці грошы ў нашы крыптакашалькі, не маючы патрэбы ведаць што-небудзь пра криптовалюты. Мы будзем працягваць сачыць за тым, наколькі добра гэта працуе на практыцы, але гэта вялікая справа. З-за закрыцця Z-Library і арышту (меркаваных) заснавальнікаў, мы працавалі кругласутачна, каб забяспечыць добрую альтэрнатыву з Архівам Анны (мы не будзем спасылацца на яго тут, але вы можаце знайсці яго ў Google). Вось некаторыя з дасягненняў, якія мы зрабілі нядаўна. Абнаўленне Анны: цалкам адкрыты архіў, ElasticSearch, больш за 300 ГБ вокладак кніг Мы працавалі кругласутачна, каб забяспечыць добрую альтэрнатыву з Архівам Анны. Вось некаторыя з дасягненняў, якія мы зрабілі нядаўна. Аналіз Семантычныя дублікаты (розныя сканы адной і той жа кнігі) тэарэтычна можна адфільтраваць, але гэта складана. Калі мы ўручную праглядалі коміксы, мы знайшлі занадта шмат ілжывых пазітываў. Ёсць некаторыя дублікаты толькі па MD5, што адносна марнатраўна, але фільтраванне іх дало б нам толькі каля 1% in эканоміі. У такім маштабе гэта ўсё яшчэ каля 1 ТБ, але таксама, у такім маштабе 1 ТБ не мае вялікага значэння. Мы б лепш не рызыкавалі выпадкова знішчыць дадзеныя ў гэтым працэсе. Мы знайшлі шмат дадзеных, якія не з'яўляюцца кнігамі, такіх як фільмы, заснаваныя на коміксах. Гэта таксама здаецца марнатраўствам, паколькі яны ўжо шырока даступныя іншымі спосабамі. Аднак мы зразумелі, што не можам проста адфільтраваць файлы фільмаў, паколькі ёсць таксама <em>інтэрактыўныя коміксы</em>, якія былі выпушчаны на камп'ютары, якія нехта запісаў і захаваў як фільмы. У рэшце рэшт, усё, што мы маглі б выдаліць з калекцыі, зэканоміла б толькі некалькі працэнтаў. Потым мы ўспомнілі, што мы захавальнікі дадзеных, і людзі, якія будуць люстраваць гэта, таксама захавальнікі дадзеных, і таму: «ШТО ВЫ МАЕЦЕ НА ЎВАЗЕ, ВЫДАЛІЦЬ?!» :) Калі вы атрымліваеце 95 ТБ у ваш кластар захоўвання, вы спрабуеце зразумець, што там наогул ёсць… Мы правялі некаторы аналіз, каб паглядзець, ці можам мы крыху зменшыць памер, напрыклад, выдаляючы дублікаты. Вось некаторыя з нашых высноў: Таму мы прадстаўляем вам поўную, нязмененую калекцыю. Гэта шмат дадзеных, але мы спадзяемся, што дастаткова людзей захоча яе распаўсюджваць. Супрацоўніцтва Улічваючы яе памер, гэтая калекцыя даўно была ў нашым спісе жаданняў, таму пасля нашага поспеху з рэзервовым капіраваннем Z-Library, мы нацэліліся на гэтую калекцыю. Спачатку мы непасрэдна скрабілі яе, што было даволі складана, паколькі іх сервер быў не ў лепшым стане. Такім чынам мы атрымалі каля 15 ТБ, але гэта ішло павольна. На шчасце, нам удалося звязацца з аператарам бібліятэкі, які пагадзіўся адправіць нам усе дадзеныя непасрэдна, што было значна хутчэй. Тым не менш, перадача і апрацоўка ўсіх дадзеных заняла больш за паўгода, і мы амаль страцілі ўсё з-за пашкоджання дыска, што азначала б пачатак зноў. Гэты вопыт прымусіў нас паверыць, што важна як мага хутчэй распаўсюдзіць гэтыя дадзеныя, каб іх можна было люстраваць шырока і далёка. Мы ўсяго ў адным-двух няўдачных выпадках ад страты гэтай калекцыі назаўсёды! Калекцыя Хуткасць азначае, што калекцыя трохі неарганізаваная… Давайце паглядзім. Уявіце, што ў нас ёсць файл-сістэма (якую на самай справе мы раздзяляем на торэнты): Першы каталог, <code>/repository</code>, з'яўляецца больш структураванай часткай гэтага. Гэты каталог утрымлівае так званыя "тысячныя дырэкторыі": дырэкторыі, кожная з якіх мае тысячу файлаў, якія паступова нумаруюцца ў базе дадзеных. Дырэкторыя <code>0</code> утрымлівае файлы з comic_id 0–999 і гэтак далей. Гэта тая ж схема, якую Library Genesis выкарыстоўвае для сваіх калекцый мастацкай і навуковай літаратуры. Ідэя заключаецца ў тым, што кожная "тысячная дырэкторыя" аўтаматычна ператвараецца ў торэнт, як толькі яна запаўняецца. Аднак аператар Libgen.li ніколі не ствараў торэнты для гэтай калекцыі, і таму тысячныя дырэкторыі, верагодна, сталі нязручнымі і саступілі месца "несартыраваным дырэкторыям". Гэта <code>/comics0</code> праз <code>/comics4</code>. Усе яны ўтрымліваюць унікальныя структуры дырэкторый, якія, верагодна, мелі сэнс для збору файлаў, але цяпер нам не вельмі зразумелыя. На шчасце, metadata ўсё яшчэ непасрэдна спасылаецца на ўсе гэтыя файлы, таму іх арганізацыя на дыску на самай справе не мае значэння! Metadata даступна ў выглядзе базы дадзеных MySQL. Яе можна загрузіць непасрэдна з сайта Libgen.li, але мы таксама зробім яе даступнай у торэнце разам з нашай уласнай табліцай з усімі MD5 хэшамі. <q>Доктар Барбара Гордан спрабуе згубіцца ў звычайным свеце бібліятэкі…</q> Форкі Libgen Спачатку трохі фону. Вы можаце ведаць Library Genesis за іх эпічную калекцыю кніг. Менш людзей ведае, што валанцёры Library Genesis стварылі іншыя праекты, такія як значная калекцыя часопісаў і стандартных дакументаў, поўная рэзервовая копія Sci-Hub (у супрацоўніцтве з заснавальніцай Sci-Hub, Аляксандрай Элбакян) і, сапраўды, велізарная калекцыя коміксаў. У нейкі момант розныя аператары люстэркаў Library Genesis пайшлі сваімі шляхамі, што прывяло да цяперашняй сітуацыі, калі існуе некалькі розных "форкаў", якія ўсё яшчэ носяць назву Library Genesis. Форк Libgen.li унікальна мае гэтую калекцыю коміксаў, а таксама значную калекцыю часопісаў (над якой мы таксама працуем). Збор сродкаў Мы выпускаем гэтыя дадзеныя ў вялікіх частках. Першы торэнт — гэта <code>/comics0</code>, які мы змясцілі ў адзін вялікі 12TB .tar файл. Гэта лепш для вашага жорсткага дыска і торэнт-праграмнага забеспячэння, чым мноства маленькіх файлаў. У рамках гэтага выпуску мы праводзім збор сродкаў. Мы імкнемся сабраць $20,000, каб пакрыць аперацыйныя і кантрактныя выдаткі на гэтую калекцыю, а таксама забяспечыць працяг і будучыя праекты. У нас ёсць некалькі <em>вялікіх</em> праектаў у распрацоўцы. <em>Каго я падтрымліваю сваім ахвяраваннем?</em> Карацей: мы захоўваем усе веды і культуру чалавецтва і робім іх лёгка даступнымі. Увесь наш код і дадзеныя з'яўляюцца адкрытым зыходным кодам, мы цалкам валанцёрскі праект, і мы захавалі 125TB кніг (у дадатак да існуючых торэнтаў Libgen і Scihub). У рэшце рэшт, мы ствараем махавік, які дазваляе і стымулюе людзей знаходзіць, сканаваць і захоўваць усе кнігі ў свеце. Мы напішам пра наш галоўны план у будучым паведамленні. :) Калі вы ахвяруеце на 12-месячнае сяброўства “Amazing Archivist” ($780), вы можаце <strong>“усынавіць торэнт”</strong>, што азначае, што мы размесцім ваша імя карыстальніка або паведамленне ў назве аднаго з торэнтаў! Вы можаце ахвяраваць, перайшоўшы на <a %(wikipedia_annas_archive)s>Архіў Анны</a> і націснуўшы кнопку «Ахвяраваць». Мы таксама шукаем больш валанцёраў: праграмістаў, даследчыкаў бяспекі, экспертаў па ананімных плацяжах і перакладчыкаў. Вы таксама можаце падтрымаць нас, прадастаўляючы паслугі хостынгу. І, вядома, калі ласка, распаўсюджвайце нашы торэнты! Дзякуй усім, хто ўжо так шчодра нас падтрымаў! Вы сапраўды робіце розніцу. Вось торэнты, якія былі выпушчаны да гэтага часу (мы ўсё яшчэ апрацоўваем астатнія): Усе торэнты можна знайсці на <a %(wikipedia_annas_archive)s>Архіў Анны</a> у раздзеле «Datasets» (мы не спасылаемся на іх наўпрост, каб спасылкі на гэты блог не выдаляліся з Reddit, Twitter і г.д.). Адтуль перайдзіце па спасылцы на сайт Tor. <a %(news_ycombinator)s>Абмеркаваць на Hacker News</a> Што далей? Шматлікія торэнты выдатныя для доўгатэрміновага захавання, але не так добрыя для штодзённага доступу. Мы будзем працаваць з партнёрамі па хостынгу, каб загрузіць усе гэтыя дадзеныя ў Інтэрнэт (паколькі Архіў Анны нічога не хостыць наўпрост). Вядома, вы зможаце знайсці гэтыя спасылкі для загрузкі ў Архіве Анны. Мы таксама запрашаем усіх працаваць з гэтымі дадзенымі! Дапамажыце нам лепш іх аналізаваць, выдаляць дублікаты, размяшчаць на IPFS, змешваць, трэніраваць вашыя AI мадэлі з імі і гэтак далей. Гэта ўсё ваша, і мы не можам дачакацца, каб убачыць, што вы з гэтым зробіце. Нарэшце, як ужо казалі раней, у нас яшчэ ёсць некалькі вялікіх выпускаў (калі <em>хтосьці</em> мог бы <em>выпадкова</em> даслаць нам дамп <em>пэўнай</em> базы дадзеных ACS4, вы ведаеце, дзе нас знайсці...), а таксама стварэнне махавіка для захавання ўсіх кніг у свеце. Так што заставайцеся з намі, мы толькі пачынаем. - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Найбуйнейшая ценявая бібліятэка коміксаў, верагодна, належыць пэўнаму форку Library Genesis: Libgen.li. Адміністратар, які кіруе гэтым сайтам, здолеў сабраць неверагодную калекцыю коміксаў з больш чым 2 мільёнаў файлаў, агульным аб'ёмам больш за 95 ТБ. Аднак, у адрозненне ад іншых калекцый Library Genesis, гэтая не была даступная ў масавым парадку праз торэнты. Вы маглі атрымаць доступ да гэтых коміксаў толькі індывідуальна праз яго павольны асабісты сервер — адзіны пункт адмовы. Да сённяшняга дня! У гэтым допісе мы раскажам вам больш пра гэтую калекцыю і пра наш збор сродкаў для падтрымкі большай колькасці гэтай працы. Архіў Анны захаваў найбуйнейшую ў свеце ценявую бібліятэку коміксаў (95 ТБ) — вы можаце дапамагчы яе распаўсюджваць Найбуйнейшая ў свеце ценявая бібліятэка коміксаў мела адзіны пункт адмовы.. да сённяшняга дня. Папярэджанне: гэты блогавы пост быў састарэлы. Мы вырашылі, што IPFS яшчэ не гатовы для шырокага выкарыстання. Мы ўсё яшчэ будзем спасылацца на файлы на IPFS з архіва Анны, калі гэта магчыма, але больш не будзем размяшчаць яго самастойна і не рэкамендуем іншым люстэркаваць з выкарыстаннем IPFS. Калі ласка, глядзіце нашу старонку з торэнтамі, калі вы хочаце дапамагчы захаваць нашу калекцыю. Размяшчэнне 5,998,794 кніг на IPFS Множнасць копій Вяртаючыся да нашага першапачатковага пытання: як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды? Асноўная праблема тут у тым, што наша калекцыя <a %(torrents_stats)s>расце</a> вельмі хутка, дзякуючы скрэпінгу і адкрытаму зыходнаму коду некаторых вялікіх калекцый (на дадатак да выдатнай працы, ужо зробленай іншымі бібліятэкамі з адкрытымі дадзенымі, такімі як Sci-Hub і Library Genesis). Гэты рост дадзеных ускладняе адлюстраванне калекцый па ўсім свеце. Захоўванне дадзеных каштуе дорага! Але мы аптымістычныя, асабліва назіраючы за наступнымі трыма тэндэнцыямі. <a %(annas_archive_stats)s>Агульны аб'ём</a> нашых калекцый за апошнія некалькі месяцаў, разбіты па колькасці сідэраў торэнта. Тэндэнцыі цэн на HDD з розных крыніц (націсніце, каб праглядзець даследаванне). <a %(critical_window_chinese)s>Кітайская версія 中文版</a>, абмеркаванне на <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Мы сабралі лёгкадаступныя рэсурсы Гэта вынікае непасрэдна з нашых прыярытэтаў, абмеркаваных вышэй. Мы аддаем перавагу працаваць над вызваленнем вялікіх калекцый у першую чаргу. Цяпер, калі мы забяспечылі некаторыя з найбуйнейшых калекцый у свеце, мы чакаем, што наш рост будзе значна павольнейшым. Існуе яшчэ доўгі хвост меншых калекцый, і новыя кнігі скануюцца або публікуюцца кожны дзень, але хуткасць, верагодна, будзе значна павольнейшай. Мы можам яшчэ падвоіцца ці нават патроіцца ў памеры, але на працягу больш доўгага перыяду часу. Паляпшэнні OCR. Прыярытэты Навуковае і інжынернае праграмнае забеспячэнне Фікцыйныя або забаўляльныя версіі ўсяго вышэйпералічанага Геаграфічныя даныя (напрыклад, карты, геалагічныя даследаванні) Унутраныя даныя ад карпарацый або ўрадаў (уцечкі) Дадзеныя вымярэнняў, такія як навуковыя вымярэнні, эканамічныя дадзеныя, карпаратыўныя справаздачы Запісы metadata у цэлым (фікцыйныя і нефікцыйныя; іншыя медыя, мастацтва, людзі і г.д.; уключаючы агляды) Нефікцыйныя кнігі Нефікцыйныя часопісы, газеты, кіраўніцтвы Нефікцыйныя транскрыпцыі выступленняў, дакументальных фільмаў, падкастаў Арганічныя даныя, такія як паслядоўнасці ДНК, насенне раслін або мікрабныя ўзоры Навуковыя артыкулы, часопісы, справаздачы Навуковыя і інжынерныя сайты, онлайн-дыскусіі Транскрыпцыі юрыдычных або судовых працэсаў Унікальна падвяргаюцца рызыцы знішчэння (напрыклад, вайной, скарачэннем фінансавання, судовымі пазовамі або палітычнымі пераследамі) Рэдкія Унікальна недастаткова асветленыя Чаму мы так клапоцімся пра дакументы і кнігі? Адкладзем у бок нашае фундаментальнае перакананне ў захаванні ўвогуле — магчыма, мы напішам пра гэта асобны пост. Дык чаму дакументы і кнігі ў прыватнасці? Адказ просты: <strong>шчыльнасць інфармацыі</strong>. На мегабайт захоўвання напісаны тэкст захоўвае найбольшую колькасць інфармацыі з усіх медыя. Хоць мы клапоцімся як пра веды, так і пра культуру, мы больш клапоцімся пра першае. У цэлым, мы знаходзім іерархію шчыльнасці інфармацыі і важнасці захавання, якая выглядае прыблізна так: Рэйтынг у гэтым спісе з'яўляецца некалькі адвольным — некалькі пунктаў маюць аднолькавы рэйтынг або ёсць рознагалоссі ў нашай камандзе — і мы, магчыма, забываем некаторыя важныя катэгорыі. Але гэта прыблізна тое, як мы прыярытэтуем. Некаторыя з гэтых пунктаў занадта адрозніваюцца ад іншых, каб мы маглі турбавацца (або ўжо займаюцца іншымі ўстановамі), такія як арганічныя даныя або геаграфічныя даныя. Але большасць пунктаў у гэтым спісе на самай справе важныя для нас. Яшчэ адзін вялікі фактар у нашай прыярытэзацыі — гэта наколькі рызыкоўная пэўная праца. Мы аддаем перавагу засяродзіцца на працах, якія: Нарэшце, нас цікавіць маштаб. У нас абмежаваны час і грошы, таму мы аддаем перавагу выдаткаваць месяц на захаванне 10 000 кніг, чым 1 000 кніг — калі яны прыкладна аднолькава каштоўныя і падвяргаюцца рызыцы. <em><q>Страчанае не можа быць адноўлена; але давайце захаваем тое, што засталося: не ў сховішчах і замках, якія хаваюць іх ад грамадскага вока і выкарыстання, аддаючы іх на марнаванне часу, а шляхам такой множнасці копій, якая паставіць іх па-за дасягальнасцю выпадковасці.</q></em><br>— Томас Джэферсан, 1791 Ценявыя бібліятэкі Код можа быць з адкрытым зыходным кодам на Github, але Github у цэлым нельга лёгка адлюстраваць і, такім чынам, захаваць (хаця ў гэтым канкрэтным выпадку існуюць дастаткова распаўсюджаныя копіі большасці рэпазітароў кода) Запісы metadata можна свабодна праглядаць на сайце Worldcat, але не загружаць масава (пакуль мы іх не <a %(worldcat_scrape)s>скрэпілі</a>) Reddit можна выкарыстоўваць бясплатна, але нядаўна ён увёў строгія меры супраць скрэпінгу ў сувязі з патрэбай у дадзеных для навучання LLM (падрабязней пра гэта пазней) Існуе шмат арганізацый, якія маюць падобныя місіі і прыярытэты. Сапраўды, ёсць бібліятэкі, архівы, лабараторыі, музеі і іншыя ўстановы, якія займаюцца захаваннем такога роду. Многія з іх добра фінансуюцца ўрадамі, прыватнымі асобамі або карпарацыямі. Але ў іх ёсць адна вялікая сляпая зона: прававая сістэма. Тут заключаецца унікальная роля ценявых бібліятэк і прычына існавання Архіва Анны. Мы можам рабіць тое, што іншыя ўстановы не могуць. Цяпер, гэта не (часта) тое, што мы можам архіваваць матэрыялы, якія незаконна захоўваць у іншых месцах. Не, у многіх месцах законна ствараць архіў з любымі кнігамі, артыкуламі, часопісамі і гэтак далей. Але тое, чаго часта не хапае ў легальных архівах, гэта <strong>рэзервовае капіраванне і даўгавечнасць</strong>. Існуюць кнігі, з якіх існуе толькі адзін экзэмпляр у нейкай фізічнай бібліятэцы. Існуюць запісы metadata, якія ахоўваюцца адной карпарацыяй. Існуюць газеты, захаваныя толькі на мікрафільме ў адным архіве. Бібліятэкі могуць сутыкнуцца з скарачэннем фінансавання, карпарацыі могуць абанкруціцца, архівы могуць быць разбомблены і спалены да зямлі. Гэта не гіпатэтычна — гэта адбываецца ўвесь час. Тое, што мы можам унікальна рабіць у Архіве Анны, — гэта захоўваць шмат копій твораў у вялікіх маштабах. Мы можам збіраць артыкулы, кнігі, часопісы і іншае, і распаўсюджваць іх масава. У цяперашні час мы робім гэта праз торэнты, але дакладныя тэхналогіі не маюць значэння і будуць змяняцца з цягам часу. Важна тое, каб шмат копій было распаўсюджана па ўсім свеце. Гэты цытат з больш чым 200 гадоў таму ўсё яшчэ актуальны: Кароткая заўвага пра грамадскую ўласнасць. Паколькі Архіў Анны ўнікальна засяроджваецца на дзейнасці, якая з'яўляецца незаконнай у многіх месцах па ўсім свеце, мы не турбуемся пра шырока даступныя калекцыі, такія як кнігі грамадскай уласнасці. Юрыдычныя асобы часта ўжо добра клапоцяцца пра гэта. Аднак ёсць меркаванні, якія часам прымушаюць нас працаваць над публічна даступнымі калекцыямі: - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Кошт захоўвання працягвае экспанентна зніжацца 3. Паляпшэнні ў шчыльнасці інфармацыі У цяперашні час мы захоўваем кнігі ў сырых фарматах, у якіх яны нам падаюцца. Вядома, яны сціснутыя, але часта гэта ўсё яшчэ вялікія сканы або фатаграфіі старонак. Да гэтага часу адзінымі варыянтамі для скарачэння агульнага памеру нашай калекцыі былі больш агрэсіўнае сцісканне або дэдуплікацыя. Аднак, каб атрымаць дастаткова значную эканомію, абодва варыянты занадта страчальныя для нас. Сільнае сцісканне фатаграфій можа зрабіць тэкст ледзь чытэльным. А дэдуплікацыя патрабуе высокай упэўненасці ў тым, што кнігі дакладна аднолькавыя, што часта занадта недакладна, асабліва калі змест аднолькавы, але сканы зроблены ў розны час. Заўсёды быў трэці варыянт, але яго якасць была настолькі жахлівай, што мы ніколі не разглядалі яго: <strong>OCR, або аптычнае распазнаванне сімвалаў</strong>. Гэта працэс пераўтварэння фатаграфій у просты тэкст з дапамогай штучнага інтэлекту для распазнавання сімвалаў на фатаграфіях. Інструменты для гэтага існуюць даўно і былі даволі прыстойнымі, але "даволі прыстойна" недастаткова для мэтаў захавання. Аднак нядаўнія шматмодальныя мадэлі глыбокага навучання зрабілі надзвычай хуткі прагрэс, хоць і з высокімі выдаткамі. Мы чакаем, што дакладнасць і выдаткі значна палепшацца ў бліжэйшыя гады, да такой ступені, што гэта стане рэалістычным для прымянення да ўсёй нашай бібліятэкі. Калі гэта адбудзецца, мы, верагодна, усё яшчэ захаваем арыгінальныя файлы, але ў дадатак мы можам мець значна меншую версію нашай бібліятэкі, якую большасць людзей захоча адлюстраваць. Сакрэт у тым, што сыры тэкст сам па сабе сціскаецца яшчэ лепш і значна лягчэй дэдуплікуецца, што дае нам яшчэ больш эканоміі. У цэлым, не нереалістычна чакаць, што агульны памер файлаў зменшыцца як мінімум у 5-10 разоў, магчыма, нават больш. Нават пры кансерватыўным скарачэнні ў 5 разоў, мы б разлічвалі на <strong>$1,000–$3,000 праз 10 гадоў, нават калі наша бібліятэка павялічыцца ў тры разы</strong>. На момант напісання, <a %(diskprices)s>цэны на дыскі</a> за ТБ складаюць каля $12 за новыя дыскі, $8 за выкарыстаныя дыскі і $4 за стужку. Калі мы будзем асцярожнымі і паглядзім толькі на новыя дыскі, гэта азначае, што захоўванне петабайта каштуе каля $12,000. Калі мы мяркуем, што наша бібліятэка патроіцца з 900 ТБ да 2.7 ПБ, гэта будзе азначаць $32,400 для адлюстравання ўсёй нашай бібліятэкі. Дадаючы электрычнасць, кошт іншага абсталявання і гэтак далей, давайце акруглім да $40,000. Або са стужкай больш як $15,000–$20,000. З аднаго боку, <strong>$15,000–$40,000 за суму ўсіх чалавечых ведаў — гэта выдатная здзелка</strong>. З іншага боку, гэта крыху крута, каб чакаць тоны поўных копій, асабліва калі мы таксама хацелі б, каб гэтыя людзі працягвалі раздаваць свае торэнты на карысць іншых. Гэта сёння. Але прагрэс рухаецца наперад: Кошт жорсткіх дыскаў за ТБ быў прыкладна зніжаны ў траціну за апошнія 10 гадоў і, верагодна, будзе працягваць зніжацца з падобнай хуткасцю. Стужка, здаецца, ідзе па падобнай траекторыі. Цэны на SSD зніжаюцца яшчэ хутчэй і могуць перасягнуць цэны на HDD да канца дзесяцігоддзя. Калі гэта спраўдзіцца, то праз 10 гадоў мы можам разлічваць на выдаткі ўсяго $5,000–$13,000, каб адлюстраваць усю нашу калекцыю (1/3), або нават менш, калі мы будзем расці павольней. Хоць гэта ўсё яшчэ шмат грошай, гэта будзе дасягальна для многіх людзей. І гэта можа быць яшчэ лепш з-за наступнага пункта… У Архіве Анны нас часта пытаюцца, як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды, калі агульны аб'ём ужо набліжаецца да 1 Петабайта (1000 ТБ) і працягвае расці. У гэтым артыкуле мы разгледзім нашу філасофію і паглядзім, чаму наступнае дзесяцігоддзе з'яўляецца крытычным для нашай місіі па захаванні ведаў і культуры чалавецтва. Крытычнае акно Калі гэтыя прагнозы дакладныя, нам <strong>трэба толькі пачакаць некалькі гадоў</strong>, пакуль уся наша калекцыя не будзе шырока адлюстравана. Такім чынам, па словах Томаса Джэферсана, “размешчана па-за дасягальнасцю выпадковасці”. На жаль, з'яўленне LLM і іх дадзенагалоднага навучання паставіла многіх уладальнікаў аўтарскіх правоў у абарончую пазіцыю. Яшчэ больш, чым яны ўжо былі. Многія вэб-сайты ўскладняюць скрэйпінг і архіваванне, судовыя справы лятаюць вакол, і ў той жа час фізічныя бібліятэкі і архівы працягваюць быць занядбанымі. Мы можам толькі чакаць, што гэтыя тэндэнцыі будуць працягваць пагаршацца, і многія працы будуць страчаны задоўга да таго, як яны ўвойдуць у грамадскі здабытак. <strong>Мы знаходзімся на парозе рэвалюцыі ў захаванні, але <q>страчанае не можа быць адноўлена.</q></strong> У нас ёсць крытычнае акно прыкладна 5-10 гадоў, на працягу якога ўсё яшчэ даволі дорага кіраваць ценявой бібліятэкай і ствараць шматлікія люстэркі па ўсім свеце, і на працягу якога доступ яшчэ не быў цалкам закрыты. Калі мы зможам пераадолець гэта акно, то сапраўды захаваем веды і культуру чалавецтва назаўсёды. Мы не павінны дазволіць гэтаму часу быць змарнаваным. Мы не павінны дазволіць гэтаму крытычнаму акну зачыніцца для нас. Пачнем. Крытычнае акно ценявых бібліятэк Як мы можам сцвярджаць, што захоўваем нашы калекцыі назаўсёды, калі яны ўжо набліжаюцца да 1 ПБ? Калекцыя Некаторая дадатковая інфармацыя пра калекцыю. <a %(duxiu)s>Duxiu</a> — гэта велізарная база дадзеных адсканаваных кніг, створаная <a %(chaoxing)s>SuperStar Digital Library Group</a>. Большасць з іх — акадэмічныя кнігі, адсканаваныя для таго, каб зрабіць іх даступнымі ў лічбавым выглядзе для ўніверсітэтаў і бібліятэк. Для нашай англамоўнай аўдыторыі <a %(library_princeton)s>Прынстан</a> і <a %(guides_lib_uw)s>Універсітэт Вашынгтона</a> маюць добрыя агляды. Таксама ёсць выдатны артыкул, які дае больш інфармацыі: <a %(doi)s>“Алічбоўка кітайскіх кніг: Выпадак даследавання пошукавай сістэмы SuperStar DuXiu Scholar”</a> (знайдзіце ў Архіве Анны). Кнігі з Duxiu даўно піратуюцца ў кітайскім інтэрнэце. Звычайна яны прадаюцца па цане менш за долар перапрадаўцамі. Яны звычайна распаўсюджваюцца з дапамогай кітайскага аналага Google Drive, які часта ўзломваецца для павелічэння аб'ёму сховішча. Некаторыя тэхнічныя дэталі можна знайсці <a %(github_duty_machine)s>тут</a> і <a %(github_821_github_io)s>тут</a>. Хоць кнігі былі паўпублічна распаўсюджаны, іх даволі складана атрымаць у вялікім аб'ёме. Мы мелі гэта высока ў нашым спісе задач і выдзелілі некалькі месяцаў поўнай занятасці для гэтага. Аднак нядаўна да нас звярнуўся неверагодны, цудоўны і таленавіты валанцёр, які паведаміў, што ўжо выканаў усю гэтую працу — за вялікія выдаткі. Яны падзяліліся з намі поўнай калекцыяй, не чакаючы нічога ўзамен, акрамя гарантыі доўгатэрміновага захавання. Сапраўды выдатна. Яны пагадзіліся папрасіць дапамогі такім чынам, каб калекцыя была апрацавана з дапамогай OCR. Калекцыя складаецца з 7,543,702 файлаў. Гэта больш, чым Library Genesis non-fiction (каля 5.3 мільёна). Агульны памер файлаў складае каля 359TB (326TiB) у цяперашнім выглядзе. Мы адкрыты для іншых прапаноў і ідэй. Проста звяжыцеся з намі. Праверце Архіў Анны для атрымання большай інфармацыі пра нашы калекцыі, намаганні па захаванні і як вы можаце дапамагчы. Дзякуй! Прыкладныя старонкі Каб даказаць нам, што ў вас ёсць добрая сістэма, вось некалькі прыкладных старонак для пачатку, з кнігі пра звышправоднікі. Ваша сістэма павінна правільна апрацоўваць матэматыку, табліцы, дыяграмы, зноскі і гэтак далей. Дашліце апрацаваныя старонкі на наш электронны адрас. Калі яны будуць выглядаць добра, мы дашлем вам больш у прыватным парадку, і мы чакаем, што вы зможаце хутка запусціць вашу сістэму на іх таксама. Калі мы будзем задаволеныя, мы зможам заключыць здзелку. - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Кітайская версія 中文版</a>, <a %(news_ycombinator)s>Абмеркаваць на Hacker News</a> Гэта кароткі блог-пост. Мы шукаем кампанію або ўстанову, якая дапаможа нам з OCR і выманнем тэксту для велізарнай калекцыі, якую мы набылі, у абмен на эксклюзіўны ранні доступ. Пасля перыяду эмбарга мы, вядома, выпусцім усю калекцыю. Высокая якасць акадэмічнага тэксту надзвычай карысная для навучання LLM. Хоць наша калекцыя кітайская, гэта павінна быць карысным нават для навучання англійскіх LLM: мадэлі, здаецца, кадуюць канцэпцыі і веды незалежна ад мовы крыніцы. Для гэтага тэкст трэба выцягнуць са сканаў. Што атрымлівае Архіў Анны з гэтага? Поўнатэкставы пошук па кнігах для сваіх карыстальнікаў. Паколькі нашы мэты супадаюць з мэтамі распрацоўшчыкаў LLM, мы шукаем супрацоўніка. Мы гатовыя даць вам <strong>эксклюзіўны ранні доступ да гэтай калекцыі ў аб'ёме на 1 год</strong>, калі вы зможаце правільна выканаць OCR і выманне тэксту. Калі вы гатовыя падзяліцца з намі ўсім кодам вашай сістэмы, мы гатовыя падоўжыць перыяд эмбарга. Эксклюзіўны доступ для кампаній LLM да найбуйнейшай у свеце калекцыі кітайскіх нон-фікшн кніг <em><strong>Каротка:</strong> Архіў Анны набыў унікальную калекцыю з 7,5 мільёна / 350 ТБ кітайскіх нон-фікшн кніг — больш, чым Library Genesis. Мы гатовыя даць кампаніі LLM эксклюзіўны доступ у абмен на якаснае OCR і выманне тэксту.</em> Архітэктура сістэмы Такім чынам, скажам, вы знайшлі некалькі кампаній, якія гатовыя размясціць ваш сайт, не закрываючы яго — назавем іх «правайдэры, якія любяць свабоду» 😄. Вы хутка выявіце, што размяшчэнне ўсяго з імі даволі дарагое, таму вы можаце знайсці некаторых «танных правайдэраў» і размясціць там фактычнае размяшчэнне, праксіруючы праз правайдэраў, якія любяць свабоду. Калі вы зробіце гэта правільна, танныя правайдэры ніколі не даведаюцца, што вы размяшчаеце, і ніколі не атрымаюць ніякіх скаргаў. З усімі гэтымі правайдэрамі існуе рызыка, што яны ўсё роўна вас закрыюць, таму вам таксама патрэбна рэзерваванне. Нам гэта трэба на ўсіх узроўнях нашай стэкі. Адна з кампаній, якая крыху любіць свабоду і паставіла сябе ў цікавае становішча, — гэта Cloudflare. Яны <a %(blog_cloudflare)s>сцвярджалі</a>, што яны не з'яўляюцца хостынгавым правайдэрам, а ўтылітай, як ISP. Таму яны не падлягаюць DMCA або іншым запытам на выдаленне і перасылаюць любыя запыты вашаму фактычнаму хостынгаваму правайдэру. Яны зайшлі так далёка, што пайшлі ў суд, каб абараніць гэтую структуру. Таму мы можам выкарыстоўваць іх як яшчэ адзін пласт кэшавання і абароны. Cloudflare не прымае ананімныя плацяжы, таму мы можам выкарыстоўваць толькі іх бясплатны план. Гэта азначае, што мы не можам выкарыстоўваць іх функцыі балансавання нагрузкі або рэзервавання. Таму мы <a %(annas_archive_l255)s>рэалізавалі гэта самастойна</a> на ўзроўні дамена. Пры загрузцы старонкі браўзер праверыць, ці даступны бягучы дамен, і калі не, перапісвае ўсе URL на іншы дамен. Паколькі Cloudflare кэшуе шмат старонак, гэта азначае, што карыстальнік можа трапіць на наш асноўны дамен, нават калі праксі-сервер не працуе, а затым пры наступным кліку перайсці на іншы дамен. У нас таксама ёсць звычайныя аперацыйныя праблемы, з якімі трэба мець справу, такія як маніторынг здароўя сервера, рэгістрацыя памылак на баку сервера і кліента і гэтак далей. Наша архітэктура рэзервавання дазваляе большай надзейнасці ў гэтым плане, напрыклад, шляхам запуску зусім іншага набору сервераў на адным з даменаў. Мы нават можам запускаць старыя версіі кода і набораў дадзеных на гэтым асобным дамене, у выпадку, калі крытычная памылка ў асноўнай версіі застанецца незаўважанай. Мы таксама можам засцерагчыся ад таго, што Cloudflare павернецца супраць нас, выдаліўшы яго з аднаго з даменаў, напрыклад, з гэтага асобнага дамена. Магчымыя розныя камбінацыі гэтых ідэй. Заключэнне Гэта быў цікавы вопыт, каб даведацца, як наладзіць надзейную і ўстойлівую пошукавую сістэму для ценявой бібліятэкі. Ёсць шмат іншых дэталяў, якія можна падзяліць у наступных пастах, таму дайце мне ведаць, пра што вы хацелі б даведацца больш! Як заўсёды, мы шукаем ахвяраванні для падтрымкі гэтай працы, таму абавязкова наведайце старонку "Ахвяраваць" на Архіве Анны. Мы таксама шукаем іншыя віды падтрымкі, такія як гранты, доўгатэрміновыя спонсары, пастаўшчыкі плацяжоў з высокім рызыкай, магчыма, нават (са смакам!) рэкламу. І калі вы хочаце ўнесці свой час і навыкі, мы заўсёды шукаем распрацоўшчыкаў, перакладчыкаў і гэтак далей. Дзякуй за ваш інтарэс і падтрымку. Токены інавацый Пачнем з нашай тэхналагічнай стэкі. Яна наўмысна сумная. Мы выкарыстоўваем Flask, MariaDB і ElasticSearch. І гэта літаральна ўсё. Пошук у асноўным вырашаная праблема, і мы не збіраемся яе пераасэнсоўваць. Акрамя таго, мы павінны выдаткаваць нашы <a %(mcfunley)s>токены інавацый</a> на нешта іншае: не быць закрытымі ўладамі. Наколькі законны ці незаконны архіў Анны? Гэта ў асноўным залежыць ад юрысдыкцыі. Большасць краін прытрымліваецца нейкай формы аўтарскага права, што азначае, што людзям або кампаніям прысвойваецца эксклюзіўная манаполія на пэўныя віды твораў на пэўны перыяд часу. Дарэчы, у архіве Анны мы лічым, што, хоць ёсць некаторыя перавагі, у цэлым аўтарскае права з'яўляецца негатыўным для грамадства — але гэта гісторыя для іншага часу. Гэта эксклюзіўная манаполія на пэўныя творы азначае, што незаконна для любога, хто знаходзіцца па-за гэтай манаполіяй, непасрэдна распаўсюджваць гэтыя творы — уключаючы нас. Але архіў Анны — гэта пошукавая сістэма, якая не распаўсюджвае гэтыя творы непасрэдна (прынамсі, не на нашым сайце ў адкрытым доступе), таму мы павінны быць у парадку, так? Не зусім. У многіх юрысдыкцыях незаконна не толькі распаўсюджваць абароненыя аўтарскім правам творы, але і спасылацца на месцы, якія гэта робяць. Класічным прыкладам гэтага з'яўляецца закон DMCA Злучаных Штатаў. Гэта самы строгі канец спектра. На іншым канцы спектра тэарэтычна могуць быць краіны, у якіх наогул няма законаў аб аўтарскім праве, але такіх на самай справе не існуе. Амаль у кожнай краіне ёсць нейкая форма закона аб аўтарскім праве. Выкананне — гэта іншая гісторыя. Ёсць шмат краін з урадамі, якія не жадаюць выконваць закон аб аўтарскім праве. Ёсць таксама краіны паміж гэтымі двума крайнасцямі, якія забараняюць распаўсюджваць абароненыя аўтарскім правам творы, але не забараняюць спасылацца на такія творы. Іншы аспект — гэта ўзровень кампаніі. Калі кампанія працуе ў юрысдыкцыі, якая не клапоціцца пра аўтарскае права, але сама кампанія не жадае рызыкаваць, то яны могуць закрыць ваш сайт, як толькі хтосьці на яго паскардзіцца. Нарэшце, вялікае значэнне маюць плацяжы. Паколькі нам трэба заставацца ананімнымі, мы не можам выкарыстоўваць традыцыйныя спосабы аплаты. Гэта пакідае нам криптовалюты, і толькі невялікая частка кампаній падтрымлівае іх (ёсць віртуальныя дэбетавыя карты, аплачаныя криптовалютай, але іх часта не прымаюць). - Анна і каманда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Я кірую <a %(wikipedia_annas_archive)s>Архівам Анны</a>, найбуйнейшай у свеце адкрытай некамерцыйнай пошукавай сістэмай для <a %(wikipedia_shadow_library)s>ценявых бібліятэк</a>, такіх як Sci-Hub, Library Genesis і Z-Library. Наша мэта — зрабіць веды і культуру лёгка даступнымі і ў канчатковым выніку стварыць супольнасць людзей, якія разам архівуюць і захоўваюць <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>усе кнігі ў свеце</a>. У гэтым артыкуле я пакажу, як мы кіруем гэтым сайтам, і унікальныя выклікі, якія ўзнікаюць пры эксплуатацыі сайта з сумніўным юрыдычным статусам, паколькі няма "AWS для ценявых дабрачынных арганізацый". <em>Таксама азнаёмцеся з сястрынскім артыкулам <a %(blog_how_to_become_a_pirate_archivist)s>Як стаць піратам-архівістам</a>.</em> Як кіраваць ценявой бібліятэкай: аперацыі ў Архіве Анны Няма <q>AWS для ценявых дабрачынных арганізацый,</q> дык як мы кіруем Архівам Анны? Інструменты Сервер прыкладанняў: Flask, MariaDB, ElasticSearch, Docker. Распрацоўка: Gitlab, Weblate, Zulip. Кіраванне серверам: Ansible, Checkmk, UFW. Статычны хостынг Onion: Tor, Nginx. Проксі-сервер: Varnish. Давайце паглядзім, якія інструменты мы выкарыстоўваем для дасягнення ўсяго гэтага. Гэта вельмі развіваецца, паколькі мы сутыкаемся з новымі праблемамі і знаходзім новыя рашэнні. Ёсць некаторыя рашэнні, якія мы пераглядалі неаднаразова. Адно з іх — гэта камунікацыя паміж серверамі: раней мы выкарыстоўвалі Wireguard для гэтага, але выявілі, што ён часам перастае перадаваць дадзеныя або перадае іх толькі ў адным кірунку. Гэта адбывалася з некалькімі рознымі наладкамі Wireguard, якія мы спрабавалі, такімі як <a %(github_costela_wesher)s>wesher</a> і <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Мы таксама спрабавалі тунэляваць порты праз SSH, выкарыстоўваючы autossh і sshuttle, але сутыкнуліся з <a %(github_sshuttle)s>праблемамі там</a> (хоць мне ўсё яшчэ не зразумела, ці пакутуе autossh ад праблем TCP-over-TCP ці не — мне гэта здаецца няўстойлівым рашэннем, але, магчыма, яно на самай справе нармальнае?). Замест гэтага мы вярнуліся да прамых злучэнняў паміж серверамі, хаваючы, што сервер працуе на танных правайдэрах, выкарыстоўваючы IP-фільтраванне з UFW. Гэта мае недахоп у тым, што Docker не працуе добра з UFW, калі вы не выкарыстоўваеце <code>network_mode: "host"</code>. Усё гэта крыху больш схільна да памылак, таму што вы адкрыеце свой сервер для інтэрнэту з невялікай няправільнай канфігурацыяй. Магчыма, нам варта вярнуцца да autossh — зваротная сувязь тут будзе вельмі дарэчы. Мы таксама пераглядалі Varnish супраць Nginx. Зараз нам падабаецца Varnish, але ў яго ёсць свае асаблівасці і недахопы. Тое ж самае адносіцца да Checkmk: мы яго не любім, але ён працуе пакуль што. Weblate быў нармальны, але не неверагодны — я часам баюся, што ён страціць мае дадзеныя, калі я спрабую сінхранізаваць яго з нашым git-рэпазіторыем. Flask быў добры ў цэлым, але ў яго ёсць некаторыя дзіўныя асаблівасці, якія каштавалі шмат часу на адладку, такія як наладка карыстальніцкіх даменаў або праблемы з інтэграцыяй SqlAlchemy. Пакуль што іншыя інструменты былі выдатныя: у нас няма сур'ёзных скаргаў на MariaDB, ElasticSearch, Gitlab, Zulip, Docker і Tor. Усе яны мелі некаторыя праблемы, але нічога занадта сур'ёзнага або патрабуючага шмат часу. Супольнасць Першы выклік можа быць нечаканым. Гэта не тэхнічная праблема або юрыдычная праблема. Гэта псіхалагічная праблема: праца ў цені можа быць неверагодна адзінотай. У залежнасці ад таго, што вы плануеце рабіць, і вашай мадэлі пагрозы, вам, магчыма, прыйдзецца быць вельмі асцярожнымі. На адным канцы спектра ў нас ёсць людзі, як Аляксандра Элбакян*, заснавальніца Sci-Hub, якая вельмі адкрыта пра сваю дзейнасць. Але яна знаходзіцца пад высокай рызыкай быць арыштаванай, калі наведае заходнюю краіну ў гэты момант, і можа сутыкнуцца з дзесяцігоддзямі турэмнага зняволення. Ці гатовыя вы рызыкнуць гэтым? Мы знаходзімся на іншым канцы спектра; вельмі асцярожныя, каб не пакінуць ніякіх слядоў, і маем моцную аперацыйную бяспеку. * Як згадана на HN "ynno", Аляксандра спачатку не хацела быць вядомай: "Яе серверы былі настроены на выдачу падрабязных паведамленняў пра памылкі з PHP, уключаючы поўны шлях да файла з памылкай, які знаходзіўся ў каталогу /home/<USER>" Таму выкарыстоўвайце выпадковыя імёны карыстальнікаў на камп'ютарах, якія вы выкарыстоўваеце для гэтага, на выпадак, калі вы нешта няправільна наладзіце. Аднак гэтая сакрэтнасць мае псіхалагічную цану. Большасць людзей любіць, калі іх прызнаюць за працу, якую яны робяць, і ўсё ж вы не можаце атрымаць ніякага прызнання за гэта ў рэальным жыцці. Нават простыя рэчы могуць быць складанымі, як, напрыклад, сябры, якія пытаюцца, чым вы займаліся (у нейкі момант "возня з маім NAS / homelab" надакучае). Вось чаму так важна знайсці нейкую супольнасць. Вы можаце адмовіцца ад часткі аперацыйнай бяспекі, даверыўшыся некаторым вельмі блізкім сябрам, у якіх вы ведаеце, што можаце глыбока давяраць. Нават тады будзьце асцярожныя, каб не пакідаць нічога ў пісьмовай форме, на выпадак, калі ім прыйдзецца перадаць свае электронныя лісты ўладам, або калі іх прылады будуць скампраметаваны іншым чынам. Яшчэ лепш знайсці некаторых аднадумцаў-піратов. Калі вашы блізкія сябры зацікаўлены далучыцца да вас, выдатна! У адваротным выпадку, вы можаце знайсці іншых у Інтэрнэце. На жаль, гэта ўсё яшчэ нішавое супольнасць. Пакуль што мы знайшлі толькі некалькі іншых, хто актыўны ў гэтай галіне. Добрыя пачатковыя месцы, здаецца, гэта форумы Library Genesis і r/DataHoarder. Каманда архіва таксама мае аднадумцаў, хоць яны дзейнічаюць у рамках закона (нават калі ў некаторых шэрых зонах закона). Традыцыйныя сцэны "warez" і пірацтва таксама маюць людзей, якія думаюць падобным чынам. Мы адкрыты для ідэй, як развіваць супольнасць і даследаваць ідэі. Не саромейцеся пісаць нам у Twitter або Reddit. Магчыма, мы маглі б арганізаваць нейкі форум або групу чата. Адна з праблем заключаецца ў тым, што гэта можа лёгка падвергнуцца цэнзуры пры выкарыстанні звычайных платформаў, таму нам давядзецца размясціць гэта самастойна. Ёсць таксама кампраміс паміж тым, каб гэтыя абмеркаванні былі цалкам публічнымі (больш патэнцыйнага ўдзелу) і тым, каб зрабіць іх прыватнымі (не дазваляючы патэнцыйным "мэтам" ведаць, што мы збіраемся іх скрабаваць). Нам трэба будзе падумаць пра гэта. Дайце нам ведаць, калі вас гэта цікавіць! Заключэнне Спадзяемся, гэта будзе карысна для пачынаючых пірацкіх архівістаў. Мы рады вітаць вас у гэтым свеце, таму не саромейцеся звяртацца. Давайце захаваем як мага больш ведаў і культуры свету і распаўсюдзім іх паўсюль. Праекты 4. Выбар дадзеных Часта вы можаце выкарыстоўваць metadata, каб вызначыць разумны паднабор дадзеных для загрузкі. Нават калі вы ў рэшце рэшт хочаце загрузіць усе дадзеныя, можа быць карысна спачатку прыярытэзаваць найбольш важныя элементы, на выпадак, калі вас выявяць і абарона будзе ўзмоцнена, або таму, што вам трэба будзе набыць больш дыскаў, або проста таму, што нешта іншае з'явіцца ў вашым жыцці, перш чым вы зможаце загрузіць усё. Напрыклад, калекцыя можа мець некалькі выданняў аднаго і таго ж рэсурсу (напрыклад, кнігі або фільма), дзе адно пазначана як найлепшая якасць. Захаванне гэтых выданняў у першую чаргу было б вельмі разумным. Вы можаце ў рэшце рэшт захацець захаваць усе выданні, паколькі ў некаторых выпадках metadata можа быць пазначана няправільна, або могуць быць невядомыя кампрамісы паміж выданнямі (напрыклад, "лепшае выданне" можа быць лепшым у большасці аспектаў, але горшым у іншых, напрыклад, фільм з больш высокім дазволам, але без субтытраў). Вы таксама можаце шукаць у сваёй базе дадзеных metadata, каб знайсці цікавыя рэчы. Які самы вялікі файл, які размешчаны, і чаму ён такі вялікі? Які самы маленькі файл? Ці ёсць цікавыя або нечаканыя ўзоры, калі гаворка ідзе пра пэўныя катэгорыі, мовы і гэтак далей? Ці ёсць дублікатныя або вельмі падобныя назвы? Ці ёсць узоры, калі дадзеныя былі дададзены, напрыклад, адзін дзень, калі шмат файлаў было дададзена адначасова? Вы часта можаце шмат даведацца, гледзячы на набор дадзеных з розных бакоў. У нашым выпадку мы выдалілі дублікаты кніг Z-Library з дапамогай хэшаў md5 у Library Genesis, тым самым зэканоміўшы шмат часу на загрузку і месца на дыску. Аднак гэта даволі унікальная сітуацыя. У большасці выпадкаў няма комплексных баз дадзеных, якія файлы ўжо належным чынам захаваны іншымі піратамі. Гэта само па сабе вялікая магчымасць для кагосьці. Было б выдатна мець рэгулярна абнаўляны агляд такіх рэчаў, як музыка і фільмы, якія ўжо шырока распаўсюджаны на торэнт-сайтах, і таму маюць ніжэйшы прыярытэт для ўключэння ў пірацкія люстэркі. 6. Распаўсюджванне У вас ёсць дадзеныя, такім чынам, вы валодаеце першым у свеце пірацкім люстэркам вашай мэты (хутчэй за ўсё). У многіх адносінах самая цяжкая частка ўжо ззаду, але самая рызыкоўная частка ўсё яшчэ наперадзе. У рэшце рэшт, да гэтага часу вы былі незаўважнымі; ляталі пад радарам. Усё, што вам трэба было зрабіць, гэта выкарыстоўваць добры VPN на працягу ўсяго часу, не запаўняць свае асабістыя дадзеныя ў любых формах (вядома), і, магчыма, выкарыстоўваць спецыяльную сесію браўзера (ці нават іншы камп'ютар). Цяпер вам трэба распаўсюджваць дадзеныя. У нашым выпадку мы спачатку хацелі вярнуць кнігі ў Library Genesis, але хутка выявілі цяжкасці ў гэтым (сартаванне мастацкай і навуковай літаратуры). Таму мы вырашылі распаўсюджваць з дапамогай торэнтаў у стылі Library Genesis. Калі ў вас ёсць магчымасць унесці свой уклад у існуючы праект, гэта можа зэканоміць вам шмат часу. Аднак у цяперашні час існуе не так шмат добра арганізаваных пірацкіх люстэркаў. Такім чынам, скажам, вы вырашылі распаўсюджваць торэнты самастойна. Старайцеся трымаць гэтыя файлы невялікімі, каб іх было лёгка люстраваць на іншых сайтах. Вам давядзецца самастойна раздаваць торэнты, пры гэтым застаючыся ананімным. Вы можаце выкарыстоўваць VPN (з пераадрасацыяй порта або без яе), або аплаціць Seedbox з дапамогай перамешаных Bitcoins. Калі вы не ведаеце, што азначаюць некаторыя з гэтых тэрмінаў, вам давядзецца шмат чытаць, паколькі важна, каб вы разумелі рызыкі. Вы можаце размясціць самі торэнт-файлы на існуючых торэнт-сайтах. У нашым выпадку мы вырашылі фактычна размясціць сайт, паколькі мы таксама хацелі распаўсюджваць нашу філасофію ясным чынам. Вы можаце зрабіць гэта самастойна аналагічным чынам (мы выкарыстоўваем Njalla для нашых даменаў і хостынгу, аплачаных перамешанымі Bitcoins), але таксама не саромейцеся звязацца з намі, каб мы размясцілі вашы торэнты. Мы імкнемся стварыць комплексны індэкс пірацкіх люстэркаў з цягам часу, калі гэтая ідэя атрымае распаўсюджванне. Што тычыцца выбару VPN, пра гэта ўжо шмат напісана, таму мы проста паўторым агульную параду выбіраць па рэпутацыі. Фактычныя судовыя праверкі палітыкі без лагавання з доўгімі запісамі абароны прыватнасці — гэта самы нізкі рызыкавы варыянт, на нашу думку. Звярніце ўвагу, што нават калі вы робіце ўсё правільна, вы ніколі не зможаце дасягнуць нулявога рызыкі. Напрыклад, пры раздачы вашых торэнтаў высокаматываваны дзяржаўны актор можа, верагодна, паглядзець на ўваходныя і выходныя патокі дадзеных для сервераў VPN і вызначыць, хто вы. Або вы можаце проста неяк памыліцца. Мы, верагодна, ужо зрабілі гэта, і зробім зноў. На шчасце, дзяржавы не так моцна клапоцяцца пра пірацтва. Адно рашэнне, якое трэба прыняць для кожнага праекта, — гэта ці публікаваць яго пад тым жа імем, што і раней, ці не. Калі вы працягваеце выкарыстоўваць тое ж імя, то памылкі ў аператыўнай бяспецы з ранейшых праектаў могуць вярнуцца, каб вас укусіць. Але публікацыя пад рознымі імёнамі азначае, што вы не будуеце доўгатэрміновую рэпутацыю. Мы вырашылі мець моцную аператыўную бяспеку з самага пачатку, каб мы маглі працягваць выкарыстоўваць тое ж імя, але мы не будзем вагацца публікаваць пад іншым імем, калі мы памылімся або калі абставіны гэтага патрабуюць. Паведаміць пра гэта можа быць складана. Як мы сказалі, гэта ўсё яшчэ нішавы супольнасць. Мы першапачаткова размясцілі на Reddit, але сапраўды атрымалі ўвагу на Hacker News. Пакуль наша рэкамендацыя — размясціць гэта ў некалькіх месцах і паглядзець, што адбудзецца. І зноў жа, звяжыцеся з намі. Мы хацелі б распаўсюджваць інфармацыю пра больш пірацкія архіўныя намаганні. 1. Выбар дамена / філасофія Няма недахопу ў ведах і культурнай спадчыне, якую трэба захаваць, што можа быць пераважным. Вось чаму часта карысна зрабіць паўзу і падумаць, які ўклад вы можаце зрабіць. Кожны мае розны спосаб думаць пра гэта, але вось некаторыя пытанні, якія вы маглі б сабе задаць: У нашым выпадку нас асабліва цікавіла доўгатэрміновае захаванне навукі. Мы ведалі пра Library Genesis і тое, як ён быў цалкам люстраваны шмат разоў з дапамогай торэнтаў. Нам спадабалася гэтая ідэя. Потым аднойчы адзін з нас паспрабаваў знайсці навуковыя падручнікі на Library Genesis, але не змог іх знайсці, што паставіла пад сумнеў, наколькі ён сапраўды поўны. Мы потым шукалі гэтыя падручнікі ў Інтэрнэце і знайшлі іх у іншых месцах, што пасадзіла зерне для нашага праекта. Яшчэ да таго, як мы даведаліся пра Z-Library, у нас была ідэя не спрабаваць збіраць усе гэтыя кнігі ўручную, а засяродзіцца на люстраванні існуючых калекцый і ўкладзе іх назад у Library Genesis. Якія навыкі ў вас ёсць, якія вы можаце выкарыстоўваць на сваю карысць? Напрыклад, калі вы эксперт па бяспецы ў Інтэрнэце, вы можаце знайсці спосабы пераадолення IP-блокаў для бяспечных мэтаў. Калі вы выдатна арганізуеце супольнасці, то, магчыма, вы зможаце сабраць некаторых людзей вакол мэты. Аднак карысна ведаць некаторыя праграмаванні, хаця б для падтрымання добрай аперацыйнай бяспекі на працягу ўсяго гэтага працэсу. На якой вобласці з высокім уздзеяннем варта засяродзіцца? Калі вы збіраецеся выдаткаваць X гадзін на пірацкую архівацыю, то як вы можаце атрымаць максімальную "аддачу ад укладзеных сродкаў"? Якія ўнікальныя спосабы, якія вы думаеце пра гэта? У вас могуць быць цікавыя ідэі або падыходы, якія іншыя маглі прапусціць. Колькі часу ў вас ёсць на гэта? Наша парада - пачынаць з малога і рабіць большыя праекты, калі вы асвоіцеся, але гэта можа стаць усёпаглынальным. Чаму вас гэта цікавіць? Чым вы захапляецеся? Калі мы зможам сабраць групу людзей, якія ўсе архівуюць тое, што ім асабліва цікава, гэта пакрые шмат! Вы будзеце ведаць значна больш, чым сярэдні чалавек, пра сваё захапленне, напрыклад, якія важныя дадзеныя трэба захаваць, якія лепшыя калекцыі і інтэрнэт-супольнасці і гэтак далей. 3. Скрабаванне metadata Дата дадавання/змены: каб вы маглі вярнуцца пазней і загрузіць файлы, якія вы не загружалі раней (хоць вы часта таксама можаце выкарыстоўваць ID або хэш для гэтага). Хэш (md5, sha1): каб пацвердзіць, што вы правільна загрузілі файл. ID: можа быць нейкі ўнутраны ID, але ID, такія як ISBN або DOI, таксама карысныя. Імя файла / размяшчэнне Апісанне, катэгорыя, тэгі, аўтары, мова і г.д. Памер: для разліку, колькі месца на дыску вам трэба. Давайце станем крыху больш тэхнічнымі. Для фактычнага скрабавання metadata з сайтаў мы трымалі рэчы даволі простымі. Мы выкарыстоўваем скрыпты Python, часам curl, і базу дадзеных MySQL для захоўвання вынікаў. Мы не выкарыстоўвалі ніякага складанага праграмнага забеспячэння для скрабавання, якое можа картаграфаваць складаныя сайты, паколькі пакуль нам трэба было скрабаваць толькі адзін або два тыпы старонак, проста пералічваючы ідэнтыфікатары і парсінг HTML. Калі няма лёгка пералічаных старонак, тады вам можа спатрэбіцца належны краулер, які спрабуе знайсці ўсе старонкі. Перш чым пачаць скрабаваць увесь сайт, паспрабуйце зрабіць гэта ўручную на працягу некаторага часу. Прайдзіце некалькі дзесяткаў старонак самастойна, каб зразумець, як гэта працуе. Часам вы ўжо сутыкнецеся з IP-блокамі або іншымі цікавымі паводзінамі такім чынам. Тое ж самае адносіцца і да скрабавання дадзеных: перш чым занадта глыбока пагрузіцца ў гэтую мэту, пераканайцеся, што вы сапраўды можаце эфектыўна загружаць яе дадзеныя. Каб абыйсці абмежаванні, ёсць некалькі рэчаў, якія вы можаце паспрабаваць. Ці ёсць іншыя IP-адрасы або серверы, якія размяшчаюць тыя ж дадзеныя, але не маюць тых жа абмежаванняў? Ці ёсць якія-небудзь API-канчатковыя кропкі, якія не маюць абмежаванняў, у той час як іншыя маюць? З якой хуткасцю загрузкі ваш IP блакуецца і на як доўга? Ці вас не блакуюць, але зніжаюць хуткасць? Што, калі вы ствараеце ўліковы запіс карыстальніка, як тады змяняюцца рэчы? Ці можаце вы выкарыстоўваць HTTP/2, каб трымаць злучэнні адкрытымі, і ці павялічвае гэта хуткасць, з якой вы можаце запытваць старонкі? Ці ёсць старонкі, якія пералічваюць некалькі файлаў адначасова, і ці дастаткова інфармацыі, пералічанай там? Рэчы, якія вы, верагодна, захочаце захаваць, уключаюць: Звычайна мы робім гэта ў два этапы. Спачатку мы загружаем сырыя HTML-файлы, звычайна непасрэдна ў MySQL (каб пазбегнуць шматлікіх маленькіх файлаў, пра якія мы пагаворым ніжэй). Затым, на асобным этапе, мы праходзім праз гэтыя HTML-файлы і парсім іх у фактычныя табліцы MySQL. Такім чынам, вам не трэба перазагружаць усё з нуля, калі вы выявіце памылку ў вашым кодзе парсінгу, паколькі вы можаце проста перапрацаваць HTML-файлы з новым кодам. Гэта таксама часта прасцей паралелізаваць этап апрацоўкі, такім чынам эканомячы час (і вы можаце пісаць код апрацоўкі, пакуль скрабаванне працуе, замест таго, каб пісаць абодва этапы адначасова). Нарэшце, звярніце ўвагу, што для некаторых мэтаў збор metadata — гэта ўсё, што ёсць. Ёсць некаторыя вялікія калекцыі metadata, якія не захоўваюцца належным чынам. Назва Выбар дамена / філасофія: На чым вы прыблізна хочаце засяродзіцца і чаму? Якія вашы унікальныя захапленні, навыкі і абставіны, якія вы можаце выкарыстоўваць на сваю карысць? Выбар мэты: Якую канкрэтную калекцыю вы будзеце люстраваць? Скрабінг metadata: Каталагізацыя інфармацыі пра файлы без фактычнага спампоўвання саміх (часта значна большых) файлаў. Выбар даных: На аснове metadata звужэнне таго, якія дадзеныя найбольш актуальныя для архівацыі зараз. Гэта можа быць усё, але часта ёсць разумны спосаб зэканоміць месца і прапускную здольнасць. Скрабінг даных: Фактычнае атрыманне даных. Распаўсюджванне: Упакоўка ў торэнты, аб'ява пра гэта дзесьці, прыцягненне людзей да распаўсюджвання. 5. Збор дадзеных Цяпер вы гатовыя фактычна загрузіць дадзеныя ў вялікім аб'ёме. Як ужо згадвалася раней, на гэтым этапе вы ўжо павінны былі ўручную загрузіць некалькі файлаў, каб лепш зразумець паводзіны і абмежаванні мэты. Аднак вас усё яшчэ чакаюць сюрпрызы, калі вы фактычна пачнеце загружаць шмат файлаў адначасова. Наша парада тут — трымаць усё проста. Пачніце з таго, што проста загрузіце некалькі файлаў. Вы можаце выкарыстоўваць Python, а затым пашырыць да некалькіх патокаў. Але часам нават прасцей — гэта генераваць файлы Bash непасрэдна з базы дадзеных, а затым запускаць некалькі з іх у некалькіх аконцах тэрмінала для маштабавання. Хуткі тэхнічны трук, варты згадкі тут, — гэта выкарыстанне OUTFILE у MySQL, які вы можаце пісаць дзе заўгодна, калі адключыце "secure_file_priv" у mysqld.cnf (і абавязкова адключыце/перазапішыце AppArmor, калі вы на Linux). Мы захоўваем дадзеныя на простых жорсткіх дысках. Пачніце з таго, што ў вас ёсць, і павольна пашырайцеся. Можа быць складана думаць пра захоўванне сотняў ТБ дадзеных. Калі гэта сітуацыя, з якой вы сутыкаецеся, проста выстаўце добры паднабор спачатку, і ў сваім аб'яве папрасіце дапамогі ў захоўванні астатняга. Калі вы хочаце набыць больш жорсткіх дыскаў самастойна, то r/DataHoarder мае добрыя рэсурсы па атрыманні добрых прапаноў. Старайцеся не турбавацца занадта шмат пра складаныя файлавыя сістэмы. Лёгка трапіць у трусіную нару наладжвання такіх рэчаў, як ZFS. Адна тэхнічная дэталь, пра якую варта ведаць, — гэта тое, што многія файлавыя сістэмы не спраўляюцца з вялікай колькасцю файлаў. Мы выявілі, што просты абыходны шлях — гэта стварэнне некалькіх каталогаў, напрыклад, для розных дыяпазонаў ID або прэфіксаў хэшаў. Пасля загрузкі дадзеных абавязкова праверце цэласнасць файлаў з дапамогай хэшаў у metadata, калі яны даступныя. 2. Выбар мэты Даступная: не выкарыстоўвае шмат слаёў абароны, каб перашкодзіць вам скрабаваць іх metadata і дадзеныя. Асаблівы ўспрыманне: у вас ёсць нейкая асаблівая інфармацыя пра гэтую мэту, напрыклад, вы неяк маеце асаблівы доступ да гэтай калекцыі, або вы зразумелі, як перамагчы іх абарону. Гэта не абавязкова (наш будучы праект не робіць нічога асаблівага), але гэта, безумоўна, дапамагае! Вялікая Такім чынам, у нас ёсць вобласць, якую мы разглядаем, цяпер якую канкрэтную калекцыю мы будзем люстраваць? Ёсць некалькі рэчаў, якія робяць мэту добрай: Калі мы знайшлі нашы навуковыя падручнікі на сайтах, акрамя Library Genesis, мы спрабавалі зразумець, як яны трапілі ў інтэрнэт. Мы тады знайшлі Z-Library і зразумелі, што хоць большасць кніг не з'яўляюцца там упершыню, яны ў рэшце рэшт там апыняюцца. Мы даведаліся пра яго сувязь з Library Genesis і (фінансавую) стымульную структуру і перавагу карыстальніцкага інтэрфейсу, якія зрабілі яго значна больш поўнай калекцыяй. Мы тады зрабілі некаторыя папярэднія скрабаванні metadata і дадзеных і зразумелі, што мы можам абыйсці іх абмежаванні на загрузку IP, выкарыстоўваючы асаблівы доступ аднаго з нашых удзельнікаў да шматлікіх праксі-сервераў. Калі вы даследуеце розныя мэты, ужо важна схаваць свае сляды, выкарыстоўваючы VPN і адкідныя адрасы электроннай пошты, пра якія мы пагаворым пазней. Унікальная: не ўжо добра ахопленая іншымі праектамі. Калі мы робім праект, ён мае некалькі этапаў: Гэта не зусім незалежныя этапы, і часта разуменне з пазнейшага этапу вяртае вас да ранейшага этапу. Напрыклад, падчас скрабінгу metadata вы можаце зразумець, што мэта, якую вы выбралі, мае абарончыя механізмы, якія перавышаюць ваш узровень навыкаў (напрыклад, IP-блокі), таму вы вяртаецеся і знаходзіце іншую мэту. - Анна і каманда (<a %(reddit)s>Reddit</a>) Цэлыя кнігі можна напісаць пра <em>чаму</em> лічбавага захавання ў цэлым і піратскага архівізму ў прыватнасці, але дазвольце нам даць кароткі ўвод для тых, хто не вельмі знаёмы. Свет стварае больш ведаў і культуры, чым калі-небудзь раней, але таксама больш з гэтага губляецца, чым калі-небудзь раней. Чалавецтва ў асноўным давярае карпарацыям, такім як акадэмічныя выдавецтвы, струменевыя сэрвісы і сацыяльныя медыя, гэта спадчына, і яны часта не аказваюцца выдатнымі захавальнікамі. Паглядзіце дакументальны фільм Digital Amnesia або любы выступ Джэйсана Скота. Ёсць некаторыя ўстановы, якія добра архівуюць столькі, колькі могуць, але яны звязаны законам. Як піраты, мы знаходзімся ў унікальнай пазіцыі архіваваць калекцыі, якія яны не могуць крануць з-за выканання аўтарскіх правоў або іншых абмежаванняў. Мы таксама можам люстраваць калекцыі шмат разоў па ўсім свеце, тым самым павялічваючы шанцы на належнае захаванне. Пакуль што мы не будзем унікаць у дыскусіі пра плюсы і мінусы інтэлектуальнай уласнасці, маральнасць парушэння закона, разважанні пра цэнзуру або пытанне доступу да ведаў і культуры. З усім гэтым на баку, давайце паглыбімся ў <em>як</em>. Мы падзелімся, як наша каманда стала піратамі-архівістамі, і ўрокамі, якія мы вывучылі на гэтым шляху. Ёсць шмат выклікаў, калі вы пачынаеце гэтае падарожжа, і спадзяемся, што мы зможам дапамагчы вам праз некаторыя з іх. Як стаць піратам-архівістам Першы выклік можа быць нечаканым. Гэта не тэхнічная праблема або юрыдычная праблема. Гэта псіхалагічная праблема. Перш чым мы паглыбімся, два абнаўленні пра Pirate Library Mirror (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>): Мы атрымалі вельмі шчодрыя ахвяраванні. Першае было $10 тыс. ад ананімнай асобы, якая таксама падтрымлівала "bookwarrior", арыгінальнага заснавальніка Library Genesis. Асаблівая падзяка bookwarrior за садзейнічанне гэтаму ахвяраванню. Другое было яшчэ $10 тыс. ад ананімнага донара, які звязаўся з намі пасля нашага апошняга выпуску і быў натхнёны дапамагчы. Мы таксама атрымалі шэраг меншых ахвяраванняў. Вялікі дзякуй за ўсю вашу шчодрую падтрымку. У нас ёсць некалькі захапляльных новых праектаў у распрацоўцы, якія гэта падтрымае, таму заставайцеся з намі. У нас былі некаторыя тэхнічныя цяжкасці з памерам нашага другога выпуску, але нашы торэнты цяпер у сетцы і сідзіруюцца. Мы таксама атрымалі шчодрую прапанову ад ананімнай асобы сідзіраваць нашу калекцыю на іх вельмі хуткасных серверах, таму мы робім спецыяльную загрузку на іх машыны, пасля чаго ўсе астатнія, хто загружае калекцыю, павінны ўбачыць значнае паляпшэнне хуткасці. Паведамленні ў блогу Прывітанне, я Анна. Я стварыла <a %(wikipedia_annas_archive)s>Архіў Анны</a>, найбуйнейшую ў свеце ценявую бібліятэку. Гэта мой асабісты блог, у якім я і мае калегі пішам пра пірацтва, лічбавую захаванасць і многае іншае. Звяжыцеся са мной на <a %(reddit)s>Reddit</a>. Звярніце ўвагу, што гэты сайт — проста блог. Мы размяшчаем тут толькі свае ўласныя словы. Ніякія торэнты або іншыя абароненыя аўтарскім правам файлы тут не размяшчаюцца і не звязваюцца. <strong>Бібліятэка</strong> - Як і большасць бібліятэк, мы ў асноўным засяроджваемся на пісьмовых матэрыялах, такіх як кнігі. Магчыма, у будучыні мы пашырымся на іншыя тыпы медыя. <strong>Люстэрка</strong> - Мы строга з'яўляемся люстэркам існуючых бібліятэк. Мы засяроджваемся на захаванні, а не на тым, каб зрабіць кнігі лёгка даступнымі для пошуку і загрузкі (доступ) або ствараць вялікую супольнасць людзей, якія ўносяць новыя кнігі (крыніцы). <strong>Пірат</strong> - Мы наўмысна парушаем закон аб аўтарскім праве ў большасці краін. Гэта дазваляе нам рабіць тое, што юрыдычныя асобы не могуць: забяспечваць, каб кнігі былі распаўсюджаны паўсюль. <em>Мы не спасылаемся на файлы з гэтага блога. Калі ласка, знайдзіце іх самастойна.</em> - Анна і каманда (<a %(reddit)s>Reddit</a>) Гэты праект (РЭДАКЦЫЯ: перанесены ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>) накіраваны на ўклад у захаванне і вызваленне чалавечых ведаў. Мы робім наш невялікі і сціплы ўклад, ідучы па слядах вялікіх, якія былі да нас. Фокус гэтага праекта ілюструецца яго назвай: Першая бібліятэка, якую мы адлюстравалі, - гэта Z-Library. Гэта папулярная (і незаконная) бібліятэка. Яны ўзялі калекцыю Library Genesis і зрабілі яе лёгка даступнай для пошуку. Акрамя таго, яны сталі вельмі эфектыўнымі ў прыцягненні новых кніг, стымулюючы карыстальнікаў, якія ўносяць уклад, рознымі перавагамі. У цяперашні час яны не ўносяць гэтыя новыя кнігі назад у Library Genesis. І ў адрозненне ад Library Genesis, яны не робяць сваю калекцыю лёгка адлюстравальнай, што перашкаджае шырокаму захаванню. Гэта важна для іх бізнес-мадэлі, паколькі яны бяруць грошы за доступ да сваёй калекцыі ў вялікіх аб'ёмах (больш за 10 кніг у дзень). Мы не выносім маральных ацэнак наконт спагнання грошай за масавы доступ да незаконнай калекцыі кніг. Несумненна, што Z-Library паспяхова пашырыла доступ да ведаў і атрымала больш кніг. Мы проста тут, каб зрабіць сваю частку: забяспечыць доўгатэрміновае захаванне гэтай прыватнай калекцыі. Мы хацелі б запрасіць вас дапамагчы захаваць і вызваліць чалавечыя веды, загружаючы і распаўсюджваючы нашы торэнты. Глядзіце старонку праекта для атрымання дадатковай інфармацыі аб тым, як арганізаваны дадзеныя. Мы таксама вельмі хацелі б запрасіць вас унесці свае ідэі аб тым, якія калекцыі адлюстраваць наступнымі і як гэта зрабіць. Разам мы можам дасягнуць шмат. Гэта толькі невялікі ўклад сярод незлічоных іншых. Дзякуй вам за ўсё, што вы робіце. Прадстаўляем Пірацкае Люстэрка Бібліятэкі: Захаванне 7 ТБ кніг (якія не знаходзяцца ў Libgen) 10% oф пісьмовай спадчыны чалавецтва захавана навечна <strong>Google.</strong> У рэшце рэшт, яны правялі гэтае даследаванне для Google Books. Аднак іх metadata не даступна ў масавым парадку і даволі цяжка для скрэпінгу. <strong>Розныя індывідуальныя бібліятэчныя сістэмы і архівы.</strong> Ёсць бібліятэкі і архівы, якія не былі індэксаваны і агрэгаваны ні адным з вышэйзгаданых, часта таму, што яны недастаткова фінансуюцца або па іншых прычынах не хочуць дзяліцца сваімі дадзенымі з Open Library, OCLC, Google і гэтак далей. Многія з іх маюць лічбавыя запісы, даступныя праз інтэрнэт, і яны часта не вельмі добра абаронены, таму, калі вы хочаце дапамагчы і атрымаць задавальненне ад вывучэння дзіўных бібліятэчных сістэм, гэта выдатныя адпраўныя пункты. <strong>ISBNdb.</strong> Гэта тэма гэтага блога. ISBNdb скрэпіць розныя вэб-сайты для metadata кніг, у прыватнасці дадзеныя аб цэнах, якія яны затым прадаюць кнігагандлярам, каб яны маглі ўсталёўваць цэны на свае кнігі ў адпаведнасці з астатнім рынкам. Паколькі ISBN у наш час даволі ўніверсальныя, яны фактычна стварылі «вэб-старонку для кожнай кнігі». <strong>Open Library.</strong> Як ужо згадвалася, гэта іх уся місія. Яны атрымалі велізарныя аб'ёмы бібліятэчных дадзеных ад супрацоўнічаючых бібліятэк і нацыянальных архіваў і працягваюць гэта рабіць. У іх таксама ёсць валанцёры-бібліятэкары і тэхнічная каманда, якія спрабуюць дэдуплікаваць запісы і пазначыць іх усімі відамі metadata. Лепш за ўсё, іх набор дадзеных цалкам адкрыты. Вы можаце проста <a %(openlibrary)s>спампаваць яго</a>. <strong>WorldCat.</strong> Гэта вэб-сайт, які кіруецца некамерцыйнай арганізацыяй OCLC, якая прадае сістэмы кіравання бібліятэкамі. Яны агрэгуюць metadata кніг з мноства бібліятэк і робяць яго даступным праз вэб-сайт WorldCat. Аднак яны таксама зарабляюць грошы, прадаючы гэтыя дадзеныя, таму яны не даступныя для масавага спампоўвання. Яны маюць некаторыя больш абмежаваныя масавыя наборы дадзеных, даступныя для спампоўвання, у супрацоўніцтве з пэўнымі бібліятэкамі. 1. Для некаторага разумнага вызначэння "назаўсёды". ;) 2. Вядома, пісьмовая спадчына чалавецтва значна больш, чым кнігі, асабліва ў наш час. Для мэты гэтага паста і нашых нядаўніх выданняў мы засяроджваемся на кнігах, але нашы інтарэсы распаўсюджваюцца далей. 3. Ёсць шмат чаго, што можна сказаць пра Аарона Шварца, але мы проста хацелі коратка згадаць яго, паколькі ён адыгрывае ключавую ролю ў гэтай гісторыі. З цягам часу больш людзей могуць упершыню сутыкнуцца з яго імем і затым самастойна паглыбіцца ў гэтую тэму. <strong>Фізічныя копіі.</strong> Відавочна, што гэта не вельмі карысна, бо яны проста дублікатары аднаго і таго ж матэрыялу. Было б крута, калі б мы маглі захаваць усе анатацыі, якія людзі робяць у кнігах, як знакамітыя «запісы на палях» Ферма. Але, на жаль, гэта застанецца марай архівіста. <strong>«Выданні».</strong> Тут вы лічыце кожную унікальную версію кнігі. Калі нешта ў ёй адрозніваецца, напрыклад, іншая вокладка або іншая прадмова, гэта лічыцца іншым выданнем. <strong>Файлы.</strong> Пры працы з ценявымі бібліятэкамі, такімі як Library Genesis, Sci-Hub або Z-Library, ёсць дадатковы аспект. Можа быць некалькі сканаванняў аднаго і таго ж выдання. І людзі могуць ствараць лепшыя версіі існуючых файлаў, скануючы тэкст з дапамогай OCR або выпраўляючы старонкі, якія былі сканаваны пад вуглом. Мы хочам лічыць гэтыя файлы як адно выданне, што патрабуе добрай metadata або дэдуплікацыі з выкарыстаннем мер падобнасці дакументаў. <strong>«Творы».</strong> Напрыклад, «Гары Потэр і Пакой Сакрэтаў» як лагічная канцэпцыя, якая ахоплівае ўсе версіі яго, як розныя пераклады і перавыданні. Гэта даволі карыснае вызначэнне, але можа быць цяжка вызначыць, што лічыць. Напрыклад, мы, верагодна, хочам захаваць розныя пераклады, хоць перавыданні з нязначнымі адрозненнямі могуць быць не такімі важнымі. - Анна і каманда (<a %(reddit)s>Reddit</a>) З Пірацкім Люстэркам Бібліятэкі (РЭДАКЦЫЯ: перанесены ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>), наша мэта - узяць усе кнігі ў свеце і захаваць іх навечна.<sup>1</sup> Паміж нашымі торэнтамі Z-Library і арыгінальнымі торэнтамі Library Genesis у нас ёсць 11,783,153 файлаў. Але колькі гэта на самай справе? Калі б мы правільна выдалілі дублікаты гэтых файлаў, які працэнт усіх кніг у свеце мы захавалі? Мы сапраўды хацелі б мець нешта накшталт гэтага: Пачнем з некаторых прыблізных лічбаў: У абедзвюх Z-Library/Libgen і Open Library ёсць значна больш кніг, чым унікальных ISBN. Ці азначае гэта, што шмат з гэтых кніг не маюць ISBN, або проста адсутнічае metadata ISBN? Мы, верагодна, можам адказаць на гэтае пытанне з дапамогай аўтаматызаванага супастаўлення на аснове іншых атрыбутаў (назва, аўтар, выдавецтва і г.д.), прыцягнення большай колькасці крыніц дадзеных і выцягвання ISBN з саміх сканаванняў кніг (у выпадку Z-Library/Libgen). Колькі з гэтых ISBN з'яўляюцца унікальнымі? Гэта лепш за ўсё ілюструецца дыяграмай Вена: Каб быць больш дакладным: Мы былі здзіўлены, наколькі мала перакрыццяў! ISBNdb мае велізарную колькасць ISBN, якія не з'яўляюцца ні ў Z-Library, ні ў Open Library, і тое ж самае адносіцца (у меншай, але ўсё ж значнай ступені) да астатніх двух. Гэта выклікае шмат новых пытанняў. Наколькі аўтаматызаванае супастаўленне дапаможа ў пазначэнні кніг, якія не былі пазначаны ISBN? Ці будзе шмат супадзенняў і, такім чынам, павялічанае перакрыцце? Таксама, што адбудзецца, калі мы дадамо 4-ы або 5-ы набор дадзеных? Наколькі вялікім будзе перакрыцце тады? Гэта дае нам пачатковы пункт. Цяпер мы можам паглядзець на ўсе ISBN, якія не былі ў наборы дадзеных Z-Library, і якія таксама не супадаюць з палямі назвы/аўтара. Гэта можа даць нам магчымасць захаваць усе кнігі ў свеце: спачатку шляхам скрабавання інтэрнэту для сканаванняў, затым шляхам выхаду ў рэальны свет для сканавання кніг. Апошняе можа нават быць фінансавана грамадскасцю або кіравацца «наградамі» ад людзей, якія хацелі б убачыць пэўныя кнігі ў лічбавым фармаце. Усё гэта гісторыя для іншага часу. Калі вы хочаце дапамагчы з гэтым — далейшы аналіз; скрабаванне большай колькасці метададзеных; пошук большай колькасці кніг; OCR кніг; выкананне гэтага для іншых абласцей (напрыклад, дакументы, аўдыякнігі, фільмы, тэлешоў, часопісы) або нават прадастаўленне часткі гэтых дадзеных для такіх рэчаў, як ML / навучанне вялікіх моўных мадэляў — калі ласка, звяжыцеся са мной (<a %(reddit)s>Reddit</a>). Калі вас асабліва цікавіць аналіз дадзеных, мы працуем над тым, каб зрабіць нашы наборы дадзеных і скрыпты даступнымі ў больш зручным для выкарыстання фармаце. Было б выдатна, калі б вы маглі проста зрабіць форк нататніка і пачаць з гэтым гуляць. Нарэшце, калі вы хочаце падтрымаць гэтую працу, калі ласка, разгледзьце магчымасць зрабіць ахвяраванне. Гэта цалкам валанцёрская аперацыя, і ваш уклад мае вялікае значэнне. Кожная дробязь дапамагае. Пакуль мы прымаем ахвяраванні ў криптовалюте; глядзіце старонку Ахвяраванні на Архіве Анны. Для працэнта нам патрэбен назоўнік: агульная колькасць кніг, калі-небудзь апублікаваных.<sup>2</sup> Да заняпаду Google Books, інжынер праекта, Леанід Тайчэр, <a %(booksearch_blogspot)s>спрабаваў ацаніць</a> гэтую колькасць. Ён прыйшоў — жартам — да 129,864,880 («прынамсі да нядзелі»). Ён ацаніў гэтую колькасць, стварыўшы аб'яднаную базу дадзеных усіх кніг у свеце. Для гэтага ён сабраў розныя наборы дадзеных і затым аб'яднаў іх рознымі спосабамі. Як невялікае адступленне, існуе яшчэ адна асоба, якая спрабавала каталогізаваць усе кнігі ў свеце: Аарон Шварц, нябожчык лічбавы актывіст і сузаснавальнік Reddit.<sup>3</sup> Ён <a %(youtube)s>заснаваў Open Library</a> з мэтай «адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі», аб'ядноўваючы дадзеныя з розных крыніц. Ён заплаціў найвышэйшую цану за сваю працу па лічбавым захаванні, калі яго абвінавацілі ў масавым спампоўванні навуковых артыкулаў, што прывяло да яго самагубства. Не трэба казаць, што гэта адна з прычын, чаму наша група з'яўляецца псеўданімнай і чаму мы вельмі асцярожныя. Open Library па-геройску кіруецца супрацоўнікамі Internet Archive, працягваючы спадчыну Аарона. Мы вернемся да гэтага пазней у гэтым паведамленні. У блогу Google, Тэйчэр апісвае некаторыя праблемы з ацэнкай гэтай колькасці. Па-першае, што такое кніга? Ёсць некалькі магчымых вызначэнняў: «Выданні» здаюцца найбольш практычным вызначэннем таго, што такое «кнігі». Зручна, што гэтае вызначэнне таксама выкарыстоўваецца для прысваення унікальных нумароў ISBN. ISBN, або Міжнародны стандартны кніжны нумар, звычайна выкарыстоўваецца ў міжнародным гандлі, паколькі ён інтэграваны з міжнароднай сістэмай штрых-кодаў («Міжнародны нумар артыкула»). Калі вы хочаце прадаваць кнігу ў крамах, ёй патрэбен штрых-код, таму вы атрымліваеце ISBN. У блогу Тэйчера згадваецца, што хоць ISBN карысныя, яны не з'яўляюцца універсальнымі, паколькі яны былі сапраўды прыняты толькі ў сярэдзіне сямідзесятых гадоў і не ўсюды па свеце. Тым не менш, ISBN, верагодна, з'яўляецца найбольш шырока выкарыстоўваным ідэнтыфікатарам выданняў кніг, таму гэта наш лепшы пачатковы пункт. Калі мы зможам знайсці ўсе ISBN у свеце, мы атрымаем карысны спіс кніг, якія яшчэ трэба захаваць. Дык адкуль мы атрымліваем дадзеныя? Ёсць шэраг існуючых намаганняў, якія спрабуюць скласці спіс усіх кніг у свеце: У гэтым паведамленні мы рады аб'явіць невялікі выпуск (у параўнанні з нашымі папярэднімі выпускамі Z-Library). Мы скрэпілі большасць ISBNdb і зрабілі дадзеныя даступнымі для торэнта на вэб-сайце Pirate Library Mirror (РЭДАКЦЫЯ: перанесена ў <a %(wikipedia_annas_archive)s>Архіў Анны</a>; мы не будзем спасылацца на яго тут наўпрост, проста знайдзіце яго). Гэта каля 30,9 мільёна запісаў (20 ГБ як <a %(jsonlines)s>JSON Lines</a>; 4,4 ГБ у сціснутым выглядзе). На іх вэб-сайце яны сцвярджаюць, што ў іх на самай справе ёсць 32,6 мільёна запісаў, таму мы маглі б неяк прапусціць некаторыя, або <em>яны</em> могуць рабіць нешта няправільна. У любым выпадку, пакуль мы не будзем дзяліцца дакладна, як мы гэта зрабілі — мы пакінем гэта як практыкаванне для чытача. ;-) Тое, што мы падзелімся, — гэта некаторы папярэдні аналіз, каб паспрабаваць наблізіцца да ацэнкі колькасці кніг у свеце. Мы разгледзелі тры наборы дадзеных: гэты новы набор дадзеных ISBNdb, наша арыгінальнае выданне метададзеных, якія мы скрабавалі з ценявой бібліятэкі Z-Library (якая ўключае Library Genesis), і выгрузку дадзеных Open Library. Выгрузка ISBNdb, або Колькі Кніг Захавана Навечна? Калі б мы правільна выдалілі дублікаты файлаў з ценявых бібліятэк, які працэнт усіх кніг у свеце мы захавалі? Абнаўленні пра <a %(wikipedia_annas_archive)s>Архіў Анны</a>, найбуйнейшую сапраўды адкрытую бібліятэку ў гісторыі чалавецтва. <em>Рэдызайн WorldCat</em> Дадзеныя <strong>Фармат?</strong> <a %(blog)s>Кантэйнеры Архіва Анны (AAC)</a>, якія па сутнасці з'яўляюцца <a %(jsonlines)s>JSON Lines</a>, сціснутымі з дапамогай <a %(zstd)s>Zstandard</a>, плюс некаторыя стандартызаваныя семантыкі. Гэтыя кантэйнеры ахопліваюць розныя тыпы запісаў, заснаваныя на розных скрэпах, якія мы разгортвалі. Год таму мы <a %(blog)s>пачалі</a> адказваць на гэтае пытанне: <strong>Які працэнт кніг быў пастаянна захаваны ценявымі бібліятэкамі?</strong> Давайце паглядзім на некаторую асноўную інфармацыю пра дадзеныя: Калі кніга трапляе ў бібліятэку з адкрытымі дадзенымі, як <a %(wikipedia_library_genesis)s>Library Genesis</a>, а цяпер і <a %(wikipedia_annas_archive)s>Архіў Анны</a>, яна адлюстроўваецца па ўсім свеце (праз торэнты), тым самым практычна захоўваючы яе назаўсёды. Каб адказаць на пытанне, які працэнт кніг быў захаваны, нам трэба ведаць назоўнік: колькі кніг існуе ўсяго? І ў ідэале ў нас ёсць не проста лічба, а фактычныя метаданыя. Тады мы можам не толькі супаставіць іх з ценявымі бібліятэкамі, але і <strong>стварыць спіс задач для захавання астатніх кніг!</strong> Мы нават можам пачаць марыць пра сумесныя намаганні па выкананні гэтага спісу задач. Мы скрэпілі <a %(wikipedia_isbndb_com)s>ISBNdb</a> і загрузілі <a %(openlibrary)s>набор дадзеных Open Library</a>, але вынікі былі незадавальняючыя. Асноўная праблема была ў тым, што не было шмат перакрыццяў ISBN. Глядзіце гэтую дыяграму Вена з <a %(blog)s>нашага блога</a>: Мы былі вельмі здзіўлены, наколькі мала было перакрыццяў паміж ISBNdb і Open Library, якія абодва ўключаюць дадзеныя з розных крыніц, такіх як вэб-скрэпы і бібліятэчныя запісы. Калі б яны абодва добра выконвалі сваю працу па пошуку большасці ISBN, іх кругі, безумоўна, мелі б значнае перакрыцце, або адзін быў бы падмноствам іншага. Гэта прымусіла нас задумацца, колькі кніг знаходзіцца <em>цалкам па-за гэтымі кругамі</em>? Нам патрэбна большая база дадзеных. Тады мы нацэліліся на найбуйнейшую базу дадзеных кніг у свеце: <a %(wikipedia_worldcat)s>WorldCat</a>. Гэта прыватная база дадзеных некамерцыйнай арганізацыі <a %(wikipedia_oclc)s>OCLC</a>, якая агрэгуе метаданыя з бібліятэк па ўсім свеце ў абмен на прадастаўленне гэтых бібліятэк доступу да поўнага набору дадзеных і паказ іх у выніках пошуку канчатковых карыстальнікаў. Нягледзячы на тое, што OCLC з'яўляецца некамерцыйнай арганізацыяй, іх бізнес-мадэль патрабуе абароны іх базы дадзеных. Ну, мы шкадуем, сябры з OCLC, мы ўсё гэта аддаем. :-) За мінулы год мы старанна скрэпілі ўсе запісы WorldCat. Спачатку нам пашанцавала. WorldCat толькі што запускаў поўны рэдызайн свайго сайта (у жніўні 2022 года). Гэта ўключала значную перапрацоўку іх сістэм, што прывяло да шматлікіх уразлівасцяў бяспекі. Мы адразу скарысталіся магчымасцю і змаглі скрэпіць сотні мільёнаў (!) запісаў за лічаныя дні. Пасля гэтага ўразлівасці бяспекі паступова выпраўляліся адна за адной, пакуль апошняя, якую мы знайшлі, не была выпраўлена каля месяца таму. Да таго часу ў нас былі амаль усе запісы, і мы толькі імкнуліся да крыху больш якасных запісаў. Таму мы адчулі, што настаў час выпускаць! 1.3B WorldCat скрэйп <em><strong>Кароткі змест:</strong> Архіў Анны скрэйпіў увесь WorldCat (найбуйнейшую ў свеце калекцыю бібліятэчных метададзеных), каб стварыць спіс кніг, якія трэба захаваць.</em> WorldCat Папярэджанне: гэты блогавы пост быў састарэлы. Мы вырашылі, што IPFS яшчэ не гатовы для шырокага выкарыстання. Мы ўсё яшчэ будзем спасылацца на файлы на IPFS з архіва Анны, калі гэта магчыма, але больш не будзем размяшчаць яго самастойна і не рэкамендуем іншым люстэркаваць з выкарыстаннем IPFS. Калі ласка, глядзіце нашу старонку з торэнтамі, калі вы хочаце дапамагчы захаваць нашу калекцыю. Дапамажыце распаўсюджваць Z-Library на IPFS Загрузка з сервера партнёра SciDB Знешняе пазычанне Знешняе пазычанне (друк адключаны) Знешняя загрузка Даследаваць метаданыя Змяшчаецца ў торэнтах Назад  (+%(num)s бонус) неаплочана аплочана адменена скончыўся чакае пацверджання ад Анны няправільны Тэкст ніжэй даступны толькі на англійскай мове. Ідзі Скінуць Наперад Апошні Калі ваш адрас электроннай пошты не працуе на форумах Libgen, мы рэкамендуем выкарыстоўваць <a %(a_mail)s>Proton Mail</a> (бясплатна). Вы таксама можаце <a %(a_manual)s>запытаць актывацыю</a> вашага акаўнта ўручную. (можа спатрэбіцца <a %(a_browser)s>праверка браўзэра</a> — неабмежаваная колькасць загрузак!) Хуткі партнёрскі сервер №%(number)s (рэкамендуецца) (крыху хутчэй, але з чаргой) (не патрабуецца праверка браўзэра) (без праверкі браўзера або спісаў чакання) (без спісу чакання, але можа быць вельмі павольным) Павольны партнёрскі сервер #%(number)s Аўдыякніга Комікс Кніга (мастацкая літаратура) Кніга (акадэмічная літаратура) Кніга (невядомая) Артыкул у часопісе Часопіс Музычны твор Іншае Дакумент са стандартамі Не ўсе старонкі можна было пераўтварыць у PDF Пазначана як "зламаны файл" у ".li-версіі" акадэмічнага раздзела Library Genesis Не адлюстроўваецца в ".li-версіі" Library Genesis Не адлюстроўваецца ў ".rs-fork" мастацкага падзелу Library Genesis Не адлюстроўваецца ў ".rs-fork" акадэмічнага падзела Library Genesis Запуск exiftool на гэтым файле не ўдалося Пазначана як “дрэнны файл” у Z-Library Адсутнічае ў Z-Library Пазначана як “спам” у Z-Library Файл не можа быць адкрыты (напрыклад, пашкоджаны файл, DRM) Прэтэнзія на аўтарскія правы Праблемы з загрузкай (напрыклад, не ўдаецца далучыцца, паведамленне пра памылку, вельмі павольна) Няправільна метаданыя (Metadata) (напрыклад, назва, апісанне, выява вокладкі) Іншае Нізкая якасць (напрыклад, праблемы з фарматаваннем, дрэнная якасць сканавання, адсутнічаюць старонкі) Спам / файл павінен быць выдалены (напрыклад, рэклама, абразлівы змест) %(amount)s (%(amount_usd)s) %(amount)s усяго %(amount)s (%(amount_usd)s) усяго Цудоўны Кнігалюб Шчаслівы Бібліятэкар Захапляльны Збіральнік Дадзеных Дзіўны Архівіст Бонусныя загрузкі Cerlalc Чэшскія метаданыя DuXiu 读秀 Індэкс eBook EBSCOhost Google Books Goodreads HathiTrust IA Кантраляванае лічбавае пазычанне IA ISBNdb ISBN GRP Libgen.li Выключаючы “scimag” Libgen.rs Нефікшн і Фікшн Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Расійская дзяржаўная бібліятэка Sci-Hub Праз Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Загрузкі ў AA Z-Library Z-Library Кітайская назве, аўтару, DOI, ISBN, MD5, … Пошук Аўтар Апісанне і каментары да метададзеных Выданне Арыгінальная назва файла Выдавец (пошук па канкрэтным полі) Назва Год выдання Тэхнічныя дэталі (на англійскай мове) Гэтая манета мае вышэйшы за звычайны мінімум. Калі ласка, выберыце іншую працягласць або іншую манету. Запыт не можа быць выкананы. Калі ласка, паспрабуйце яшчэ раз праз некалькі хвілін, і калі гэта працягваецца, звяжыцеся з намі па %(email)s з скрыншотам. Адбылася невядомая памылка. Калі ласка, звяжыцеся з намі па %(email)s з скрыншотам. Памылка апрацоўкі плацяжу. Калі ласка, пачакайце момант і паспрабуйце зноў. Калі праблема застаецца больш за 24 гадзіны, звяжыцеся з намі па %(email)s з скрыншотам. Мы праводзім збор сродкаў для <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">рэзервовага капіявання</a> найбуйнейшай бібліятэкі коміксаў у свеце. Дзякуй за вашу падтрымку! <a href="/donate">Ахвяруйце.</a> Калі вы не можаце ахвяраваць, разгледзьце магчымасць падтрымаць нас, расказаўшы сваім сябрам і падпісаўшыся на нас у <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> або <a href="https://t.me/annasarchiveorg">Telegram</a>. Не пішыце нам па электроннай пошце, каб <a %(a_request)s>запытаць кнігі</a><br>або невялікія (<10k) <a %(a_upload)s>загрузкі</a>. Архіў Ганны DMCA / прэтэнзіі на аўтарскія правы Заставацца на сувязі Reddit Альтэрнатыўныя варыянты SLUM (%(unaffiliated)s) неафіляваны Архіў Анны патрэбна ваша дапамога! Калі вы ахвяруеце зараз, вы атрымаеце <strong>двайны</strong> лік хуткіх загрузак. Многія спрабуюць нас знішчыць, але мы змагаемся. Калі вы ахвяруеце ў гэтым месяцы, вы атрымаеце <strong>двайны</strong> лік хуткіх загрузак. Дзейнічае да канца гэтага месяца. Выратаванне чалавечых ведаў: выдатны падарунак на свята! Членства будзе адпаведна падоўжана. Серверы партнёраў недаступныя з-за закрыццяў хостынгу. Яны павінны хутка зноў запрацаваць. Для павышэння ўстойлівасці Архіва Анны мы шукаем валанцёраў для запуску люстэркаў. У нас даступны новы метад ахвяраванні: %(method_name)s. Калі ласка, падумайце аб %(donate_link_open_tag)sахвяраванні </a> — запуск гэтага вэб-сайта абыходзіцца нятанна і ваша ахвяраванне сапраўды мае значэнне. Вялікі дзякуй. Запрасіце сябра, і вы абодва атрымаеце %(percentage)s%% бонусных хуткіх загрузак! Здзівіце каханага чалавека, падарыце яму рахунак з членствам. Ідэальны падарунак на Дзень святога Валянціна! Даведацца больш… Акаўнт Актыўнасць Прасунуты Блог Ганны ↗ Anna’s Software ↗ бэта Даследчык кодаў Наборы дадзеных Ахвяраваць Загружаныя файлы Частыя пытанні Галоўная старонка Паляпшэнне метададзеных Дадзеныя LLM Увайсці / Рэгістрацыя Мае ахвяраванні Публічны профіль Пошук Бяспека Торэнты Пераклад ↗ Валанцёрства і ўзнагароды Апошнія загрузкі: 📚&nbsp;Найбуйнейшая ў свеце бібліятэка адкрытых дадзеных з адкрытым кодам. ⭐️&nbsp;адлюстроўвае Sci-Hub, бібліятэку Genesis, z-Library і многае іншае. 📈&nbsp;%(book_any)s кніг, %(journal_article)s артыкулаў, %(book_comic)s коміксаў, %(magazine)s часопісаў — захоўваюцца назаўжды..  і  і больш DuXiu Бібліятэка Internet Archive LibGen 📚&nbsp;Найбуйнейшая і па-сапраўднаму адкрытая бібліятэка ў гісторыі чалавецтва. 📈&nbsp;%(book_count)s&nbsp;кніг, %(paper_count)s&nbsp;дакументаў захавана назаўжды. ⭐️&nbsp;Мы люструем %(libraries)s. Мы збіраем і адкрываем зыходны код %(scraped)s. Увесь наш код і дадзеныя цалкам адкрытыя. OpenLib Sci-Hub ,  📚 Найбуйнейшая ў свеце бібліятэка адкрытых дадзеных з адкрытым зыходным кодам.<br>⭐️ адлюстроўвае Scihub, Libgen, Zlib і многае іншае. Z-Lib Anna’s Archive Няправільны запыт. Наведайце %(websites)s. Найбуйнейшая ў свеце бібліятэка з адкрытым зыходным кодам і адкрытымі дадзенымі. Люстэркі Sci-Hub, Library Genesis, Z-Library і іншыя. Шукаць у Архіве Анны Anna’s Archive Абнавіце старонку, каб паспрабаваць яшчэ раз. <a %(a_contact)s>Звяжыцеся з намі</a>, калі праблема застаецца на працягу некалькіх гадзін. 🔥 Праблема з загрузкай гэтай старонкі <li>1. Сачыце за намі на <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> або <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Распавядайце пра Anna’s Archive у Twitter, Reddit, Tiktok, Instagram, у мясцовай кавярні або бібліятэцы, ці дзе заўгодна! Мы не верым у абмежаванні доступу — калі нас выдаляць, мы проста з'явімся ў іншым месцы, бо ўвесь наш код і дадзеныя цалкам адкрытыя.</li><li>3. Калі ў вас ёсць магчымасць, разгледзьце магчымасць <a href="/donate">ахвяравання</a>.</li><li>4. Дапамажыце <a href="https://translate.annas-software.org/">перакласці</a> наш сайт на розныя мовы.</li><li>5. Калі вы інжынер-праграміст, разгледзьце магчымасць унесці свой уклад у наш <a href="https://annas-software.org/">адкрыты код</a> або раздаваць нашы <a href="/datasets">торэнты</a>.</li> 10. Стварыце або дапамажыце падтрымліваць старонку Вікіпедыі для архіва Анны на вашай мове. 11. Мы шукаем магчымасці размясціць невялікія, стыльныя рэкламы. Калі вы хочаце рэкламаваць на архіве Анны, калі ласка, дайце нам ведаць. 6. Калі вы даследчык бяспекі, мы можам выкарыстоўваць вашы навыкі як для атакі, так і для абароны. Праверце нашу старонку <a %(a_security)s>Бяспека</a>. 7. Мы шукаем экспертаў па плацяжах для ананімных гандляроў. Ці можаце вы дапамагчы нам дадаць больш зручныя спосабы ахвяравання? PayPal, WeChat, падарункавыя карты. Калі ведаеце каго-небудзь, калі ласка, звяжыцеся з намі. 8. Мы заўсёды шукаем больш магутнасці сервера. 9. Вы можаце дапамагчы, паведамляючы пра праблемы з файламі, пакідаючы каментары і ствараючы спісы прама на гэтым сайце. Вы таксама можаце дапамагчы, <a %(a_upload)s>загружаючы больш кніг</a>, або выпраўляючы праблемы з файламі ці фарматаваннем існуючых кніг. Для больш падрабязнай інфармацыі аб тым, як стаць валанцёрам, глядзіце нашу старонку <a %(a_volunteering)s>Валанцёрства і ўзнагароды</a>. Мы верым, што вопыт пакаленняў павінен быць надзейна захаваным і даступным кожнаму. Ствараючы гэтую пошукавую сістэму, мы сабралі разам гіганцкія інфармацыйныя творы, створаныя ў выніку карпатлівай працы распрацоўшчыкаў ценявых бібліятэк. І мы спадзяемся, што наша праца будзе працягам іх намаганняў па захаванню інфармацыі і яе данясення да людзей. Мы публікуем навіны пра нашу працу ў <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> і на <a href="https://t.me/annasarchiveorg">Telegram</a>. Калі ў вас ёсць пытанні ці вы хочаце падзяліцца вопытам карыстання сайтам, адпраўце нам ліст на %(email)s. Ідэнтыфікатар уліковага запісу: %(account_id)s Выйсці ❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. ✅ Вы выйшлі з сістэмы. Перазагрузіце старонку, каб зноў увайсці. Хуткія загрузкі выкарыстаны (апошнія 24 гадзіны): <strong>%(used)s / %(total)s</strong> Членства: <strong>%(tier_name)s</strong> да %(until_date)s <a %(a_extend)s>(падаўжэнне)</a> Вы можаце аб'яднаць некалькі членстваў (хуткасць загрузак за 24 гадзіны будзе сумавана). Членства: <strong>Няма</strong> <a %(a_become)s>(стаць членам)</a> Звяжыцеся з Аннай па %(email)s, калі вы зацікаўлены ў абнаўленні вашага членства да вышэйшага ўзроўню. Публічны профіль: %(profile_link)s Сакрэтны ключ (не дзяліцеся!): %(secret_key)s паказаць Далучайцеся да нас тут! Абнавіце да <a %(a_tier)s>вышэйшага ўзроўню</a>, каб далучыцца да нашай групы. Эксклюзіўная група ў Telegram: %(link)s Уліковы запіс якія загрузкі? Увайсці Не губляйце свой ключ! Няправільны сакрэтны ключ. Праверце ваш ключ і паспрабуйце зноў, альбо зарэгіструйце новы акаўнт ніжэй. Сакрэтны ключ Увядзіце ваш сакрэтны ключ для ўваходу: Стары ўліковы запіс на аснове электроннай пошты? Увядзіце ваш <a %(a_open)s>электронны адрас тут</a>. Зарэгістраваць новы ўліковы запіс Яшчэ не маеце ўліковага запісу? Рэгістрацыя паспяховая! Ваш сакрэтны ключ: <span %(span_key)s>%(key)s</span> Захоўвайце гэты ключ асцярожна. Калі вы яго страціце, вы страціце доступ да свайго ўліковага запісу. <li %(li_item)s><strong>Закладка.</strong> Вы можаце захаваць гэтую старонку ў закладках, каб атрымаць ваш ключ.</li><li %(li_item)s><strong>Спампаваць.</strong> Націсніце <a %(a_download)s>гэтую спасылку</a>, каб спампаваць ваш ключ.</li><li %(li_item)s><strong>Менеджар пароляў.</strong> Выкарыстоўвайце менеджар пароляў, каб захаваць ключ, калі ўводзіце яго ніжэй.</li> Увайсці / Зарэгістравацца Праверка браўзера Папярэджанне: код мае няправільныя сімвалы Unicode і можа паводзіць сябе некарэктна ў розных сітуацыях. Сыры бінарны код можна дэкадаваць з base64 прадстаўлення ў URL. Апісанне Метка Префікс URL для канкрэтнага кода Вэбсайт Коды, якія пачынаюцца з “%(prefix_label)s” Калі ласка, не скрабіце гэтыя старонкі. Замест гэтага мы рэкамендуем <a %(a_import)s>генераваць</a> або <a %(a_download)s>загружаць</a> нашы базы дадзеных ElasticSearch і MariaDB і запускаць наш <a %(a_software)s>адкрыты зыходны код</a>. Сырыя дадзеныя можна ўручную даследаваць праз файлы JSON, такія як <a %(a_json_file)s>гэты</a>. Менш за %(count)s запісаў Агульны URL Даследчык кодаў Індэкс Даследуйце коды, з якімі пазначаны запісы, па прэфіксе. Слупок “запісы” паказвае колькасць запісаў, пазначаных кодамі з дадзеным прэфіксам, як гэта бачна ў пошукавай сістэме (уключаючы запісы толькі з метаданымі). Слупок “коды” паказвае, колькі фактычных кодаў маюць дадзены прэфікс. Вядомы прэфікс кода “%(key)s” Больш… Префікс page.codes.record_starting_with page.codes.records_starting_with %(count)s запісаў, якія адпавядаюць “%(prefix_label)s” коды запісы “%%” будзе заменены на значэнне кода Шукаць у Архіве Анны Коды URL для канкрэтнага кода: “%(url)s” Гэтая старонка можа заняць некаторы час для генерацыі, таму патрабуецца капча Cloudflare. <a %(a_donate)s>Члены</a> могуць прапусціць капчу. Злоўжыванне паведамлена: Лепшая версія Вы хочаце паведаміць пра гэтага карыстальніка за абразлівыя або недарэчныя паводзіны? Праблема з файлам: %(file_issue)s схаваны каментар Адказаць Паведаміць пра злоўжыванне Вы паведамілі пра гэтага карыстальніка за злоўжыванне. Прэтэнзіі на аўтарскія правы на гэты адрас электроннай пошты будуць ігнаравацца; выкарыстоўвайце форму замест гэтага. Паказаць электронную пошту Мы вельмі вітаем вашыя водгукі і пытанні! Аднак, з-за вялікай колькасці спаму і непатрэбных лістоў, калі ласка, пастаўце галачкі, каб пацвердзіць, што вы разумееце гэтыя ўмовы для кантакту з намі. Любая іншая форма кантакту з намі па пытаннях аўтарскіх правоў будзе аўтаматычна выдалена. Для DMCA / аўтарскіх прэтэнзій выкарыстоўвайце <a %(a_copyright)s>гэту форму</a>. Кантактны email URL-адрасы на архіве Анны (абавязкова). Адзін на радок. Калі ласка, уключайце толькі URL-адрасы, якія апісваюць дакладна тое ж выданне кнігі. Калі вы хочаце падаць прэтэнзію на некалькі кніг або некалькі выданняў, калі ласка, падайце гэтую форму некалькі разоў. Прэтэнзіі, якія аб'ядноўваюць некалькі кніг або выданняў разам, будуць адхілены. Адрас (абавязкова) Дакладнае апісанне зыходнага матэрыялу (абавязкова) Электронная пошта (абавязкова) URL-адрасы зыходнага матэрыялу, адзін на радок (абавязкова). Калі ласка, уключайце як мага больш, каб дапамагчы нам праверыць вашу прэтэнзію (напрыклад, Amazon, WorldCat, Google Books, DOI). ISBN-ы зыходнага матэрыялу (калі дастасавальна). Адзін на радок. Калі ласка, уключайце толькі тыя, якія дакладна адпавядаюць выданню, для якога вы паведамляеце прэтэнзію на аўтарскія правы. Ваша імя (абавязкова) ❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце яшчэ раз. ✅ Дзякуй за падачу вашай прэтэнзіі на аўтарскія правы. Мы разгледзім яе як мага хутчэй. Калі ласка, перазагрузіце старонку, каб падаць яшчэ адну. <a %(a_openlib)s>URL-адрасы Open Library</a> зыходнага матэрыялу, адзін на радок. Калі ласка, знайдзіце хвілінку, каб пашукаць ваш зыходны матэрыял у Open Library. Гэта дапаможа нам праверыць вашу прэтэнзію. Нумар тэлефона (абавязкова) Заява і подпіс (абавязкова) Адправіць прэтэнзію Калі ў вас ёсць прэтэнзія DMCA або іншая прэтэнзія на аўтарскія правы, калі ласка, запоўніце гэтую форму як мага дакладней. Калі ў вас узнікнуць праблемы, звяжыцеся з намі па нашым спецыяльным адрасе DMCA: %(email)s. Звярніце ўвагу, што прэтэнзіі, адпраўленыя на гэты адрас па электроннай пошце, не будуць апрацаваны, ён прызначаны толькі для пытанняў. Калі ласка, выкарыстоўвайце форму ніжэй для падачы вашых прэтэнзій. Форма прэтэнзіі DMCA / аўтарскіх правоў Прыклад запісу ў архіве Анны Торэнты ад архіва Анны Фармат кантэйнераў архіва Анны Скрыпты для імпарту метаданых Калі вы зацікаўлены ў люстраванні гэтага набору дадзеных для <a %(a_archival)s>архівавання</a> або <a %(a_llm)s>навучання LLM</a>, калі ласка, звяжыцеся з намі. Апошняе абнаўленне: %(date)s Асноўны %(source)s сайт Дакументацыя метаданых (большасць палёў) Файлы, адлюстраваныя архівам Анны: %(count)s (%(percent)s%%) Рэсурсы Агульная колькасць файлаў: %(count)s Агульны памер файлаў: %(size)s Наш блог пра гэтыя дадзеныя <a %(duxiu_link)s>Duxiu</a> — гэта велізарная база дадзеных адсканаваных кніг, створаная <a %(superstar_link)s>SuperStar Digital Library Group</a>. Большасць з іх — гэта акадэмічныя кнігі, адсканаваныя для таго, каб зрабіць іх даступнымі ў лічбавым фармаце для ўніверсітэтаў і бібліятэк. Для нашай англамоўнай аўдыторыі <a %(princeton_link)s>Прынстан</a> і <a %(uw_link)s>Вашынгтонскі ўніверсітэт</a> маюць добрыя агляды. Таксама ёсць выдатны артыкул, які дае больш падрабязную інфармацыю: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Кнігі з Duxiu даўно піратуюцца ў кітайскім інтэрнэце. Звычайна яны прадаюцца перапрадаўцамі менш чым за долар. Яны звычайна распаўсюджваюцца з дапамогай кітайскага аналага Google Drive, які часта ўзломваюць для павелічэння аб'ёму сховішча. Некаторыя тэхнічныя падрабязнасці можна знайсці <a %(link1)s>тут</a> і <a %(link2)s>тут</a>. Хоць кнігі былі паўпублічна распаўсюджаны, іх даволі цяжка атрымаць у вялікай колькасці. Мы мелі гэта высока ў нашым спісе задач і выдзелілі некалькі месяцаў поўнай занятасці для гэтага. Аднак у канцы 2023 года да нас звярнуўся неверагодны, дзіўны і таленавіты валанцёр, які паведаміў, што ўжо выканаў усю гэтую працу — за вялікія выдаткі. Яны падзяліліся з намі поўнай калекцыяй, не чакаючы нічога ўзамен, акрамя гарантыі доўгатэрміновага захавання. Сапраўды выдатна. Больш інфармацыі ад нашых валанцёраў (сырыя нататкі): Адаптавана з нашага <a %(a_href)s>блога</a>. DuXiu 读秀 page.datasets.file page.datasets.files %(count)s файлы Гэты набор дадзеных цесна звязаны з <a %(a_datasets_openlib)s>наборам дадзеных Open Library</a>. Ён утрымлівае скрэйп усіх метададзеных і вялікую частку файлаў з Кантраляванай лічбавай бібліятэкі IA. Абнаўленні выпускаюцца ў <a %(a_aac)s>фармаце кантэйнераў архіва Анны</a>. Гэтыя запісы спасылаюцца непасрэдна на набор дадзеных Open Library, але таксама ўтрымліваюць запісы, якіх няма ў Open Library. У нас таксама ёсць шэраг файлаў дадзеных, сабраных членамі супольнасці на працягу многіх гадоў. Калекцыя складаецца з двух частак. Вам патрэбны абедзве часткі, каб атрымаць усе дадзеныя (акрамя састарэлых торэнтаў, якія перакрэслены на старонцы торэнтаў). Лічбавая бібліятэка пазыкі наш першы выпуск, да таго як мы стандартызавалі фармат <a %(a_aac)s>кантэйнераў архіва Анны (AAC)</a>. Утрымлівае метаданыя (у фарматах json і xml), pdf-файлы (з лічбавых сістэм пазыкі acsm і lcpdf) і мініяцюры вокладак. інкрэментальныя новыя выпускі, выкарыстоўваючы AAC. Утрымліваюць толькі метаданыя з часовымі меткамі пасля 2023-01-01, бо астатняе ўжо пакрыта “ia”. Таксама ўсе pdf-файлы, на гэты раз з сістэм пазыкі acsm і “bookreader” (вэб-чытач IA). Нягледзячы на тое, што назва не зусім дакладная, мы ўсё роўна ўключаем файлы bookreader у калекцыю ia2_acsmpdf_files, бо яны ўзаемавыключальныя. Кантраляванае лічбавае пазычанне IA 98%%+ файлаў даступныя для пошуку. Наша місія — архіваваць усе кнігі ў свеце (а таксама артыкулы, часопісы і г.д.) і зрабіць іх шырока даступнымі. Мы лічым, што ўсе кнігі павінны быць люстраваны шырока, каб забяспечыць рэзервовае капіраванне і ўстойлівасць. Вось чаму мы збіраем файлы з розных крыніц. Некаторыя крыніцы цалкам адкрытыя і могуць быць люстраваны ў масавым парадку (напрыклад, Sci-Hub). Іншыя закрытыя і абароненыя, таму мы спрабуем іх скрабаваць, каб «вызваліць» іх кнігі. Яшчэ іншыя знаходзяцца дзесьці пасярэдзіне. Усе нашы дадзеныя можна <a %(a_torrents)s>загрузіць праз торэнт</a>, а ўсе нашы метаданыя можна <a %(a_anna_software)s>згенераваць</a> або <a %(a_elasticsearch)s>загрузіць</a> у выглядзе баз дадзеных ElasticSearch і MariaDB. Сырыя дадзеныя можна ўручную даследаваць праз JSON-файлы, такія як <a %(a_dbrecord)s>гэты</a>. Метаданыя Вэб-сайт ISBN Апошняе абнаўленне: %(isbn_country_date)s (%(link)s) Рэсурсы Міжнароднае агенцтва ISBN рэгулярна выпускае дыяпазоны, якія яно выдзеліла нацыянальным агенцтвам ISBN. З гэтага мы можам вызначыць, да якой краіны, рэгіёна або моўнай групы належыць гэты ISBN. У цяперашні час мы выкарыстоўваем гэтыя дадзеныя ўскосна, праз бібліятэку Python <a %(a_isbnlib)s>isbnlib</a>. Інфармацыя пра краіну ISBN Гэта дамп вялікай колькасці запытаў да isbndb.com у верасні 2022 года. Мы спрабавалі ахапіць усе дыяпазоны ISBN. Гэта каля 30,9 мільёна запісаў. На іх сайце яны сцвярджаюць, што ў іх на самай справе ёсць 32,6 мільёна запісаў, таму мы маглі неяк прапусціць некаторыя, або <em>яны</em> маглі зрабіць нешта няправільна. Адказы JSON амаль сырыя з іх сервера. Адна з праблем якасці дадзеных, якую мы заўважылі, заключаецца ў тым, што для нумароў ISBN-13, якія пачынаюцца з іншага прэфікса, чым «978-», яны ўсё роўна ўключаюць поле «isbn», якое проста з'яўляецца нумарам ISBN-13 з першымі 3 лічбамі, адрэзанымі (і пералічанай кантрольнай лічбай). Гэта відавочна няправільна, але яны, здаецца, робяць гэта так, таму мы не змянялі гэта. Яшчэ адна патэнцыйная праблема, з якой вы можаце сутыкнуцца, заключаецца ў тым, што поле «isbn13» мае дублікаты, таму вы не можаце выкарыстоўваць яго ў якасці асноўнага ключа ў базе дадзеных. Поля «isbn13»+«isbn» у спалучэнні, здаецца, унікальныя. Выпуск 1 (2022-10-31) Торэнты з мастацкай літаратурай адстаюць (хоць ID ~4-6M не торэнтаваны, бо яны перакрываюцца з нашымі Zlib торэнтамі). Наш блог-пост пра выпуск коміксаў Торэнты з коміксамі на Архіве Анны Для гісторыі розных форкаў Library Genesis глядзіце старонку <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li змяшчае большасць таго ж кантэнту і метаданых, што і Libgen.rs, але мае некаторыя дадатковыя калекцыі, а менавіта коміксы, часопісы і стандартныя дакументы. Ён таксама інтэграваў <a %(a_scihub)s>Sci-Hub</a> у свае метаданыя і пошукавую сістэму, якую мы выкарыстоўваем для нашай базы дадзеных. Метаданыя для гэтай бібліятэкі даступныя бясплатна <a %(a_libgen_li)s>на libgen.li</a>. Аднак гэты сервер павольны і не падтрымлівае аднаўленне перапыненых злучэнняў. Тыя ж файлы таксама даступныя на <a %(a_ftp)s>FTP-серверы</a>, які працуе лепш. Нехудожняя літаратура таксама, здаецца, адхілілася, але без новых торэнтаў. Падобна, што гэта адбылося з пачатку 2022 года, хоць мы гэтага не правяралі. Паводле адміністратара Libgen.li, калекцыя “fiction_rus” (руская мастацкая літаратура) павінна быць пакрыта рэгулярна выпушчанымі торэнтамі з <a %(a_booktracker)s>booktracker.org</a>, асабліва торэнтамі <a %(a_flibusta)s>flibusta</a> і <a %(a_librusec)s>lib.rus.ec</a> (якія мы люструем <a %(a_torrents)s>тут</a>, хоць мы яшчэ не ўсталявалі, якія торэнты адпавядаюць якім файлам). Калекцыя мастацкай літаратуры мае ўласныя торэнты (аддзялілася ад <a %(a_href)s>Libgen.rs</a>), пачынаючы з %(start)s. Пэўныя дыяпазоны без торэнтаў (такія як дыяпазоны мастацкай літаратуры f_3463000 да f_4260000) верагодна з'яўляюцца файламі Z-Library (ці іншымі дублямі), хоць мы можам захацець зрабіць дэдуплікацыю і стварыць торэнты для унікальных файлаў lgli у гэтых дыяпазонах. Статыстыка для ўсіх калекцый даступная <a %(a_href)s>на сайце libgen</a>. Торэнты даступныя для большасці дадатковага кантэнту, асабліва торэнты для коміксаў, часопісаў і стандартных дакументаў былі выпушчаны ў супрацоўніцтве з Архівам Анны. Звярніце ўвагу, што торэнт-файлы, якія адносяцца да “libgen.is”, з'яўляюцца люстэркамі <a %(a_libgen)s>Libgen.rs</a> (“.is” — гэта іншы дамен, які выкарыстоўваецца Libgen.rs). Карысная рэсурс для выкарыстання метададзеных — <a %(a_href)s>гэтая старонка</a>. %(icon)s Іх калекцыя “fiction_rus” (руская мастацкая літаратура) не мае асобных торэнтаў, але пакрываецца торэнтамі ад іншых, і мы захоўваем <a %(fiction_rus)s>люстэрка</a>. Торэнты рускай мастацкай літаратуры ў Архіве Анны Торэнты з мастацкай літаратурай на Архіве Анны Форум для абмеркавання Метададзеныя Метададзеныя праз FTP Торэнты з часопісамі на Архіве Анны Інфармацыя пра палі метададзеных Люстэрка іншых торэнтаў (і унікальныя торэнты з мастацкай літаратурай і коміксамі) Торэнты стандартных дакументаў у Архіве Анны Libgen.li Торэнты ад архіва Анны (вокладкі кніг) Library Genesis вядомы тым, што ўжо шчодра робіць свае дадзеныя даступнымі ў вялікіх аб'ёмах праз торэнты. Наша калекцыя Libgen складаецца з дадатковых дадзеных, якія яны не выпускаюць непасрэдна, у партнёрстве з імі. Вялікі дзякуй усім, хто ўдзельнічае ў Library Genesis за супрацоўніцтва з намі! Наш блог пра выпуск вокладак кніг Гэтая старонка пра версію “.rs”. Яна вядомая тым, што паслядоўна публікуе як свае метададзеныя, так і поўны змест свайго каталога кніг. Яе калекцыя кніг падзелена на раздзелы мастацкай і не-мастацкай літаратуры. Карысная рэсурс для выкарыстання метададзеных — <a %(a_metadata)s>гэтая старонка</a> (блокуе дыяпазоны IP, можа спатрэбіцца VPN). Па стане на 2024-03, новыя торэнты размяшчаюцца ў <a %(a_href)s>гэтым форуме</a> (блокіруе дыяпазоны IP, можа спатрэбіцца VPN). Фікшн торэнты на архіве Анны Libgen.rs Фікшн торэнты Libgen.rs Форум абмеркаванняў Libgen.rs Метаданыя Інфармацыя пра метаданыя Libgen.rs Libgen.rs Нефікшн торэнты Нефікшн торэнты на архіве Анны %(example)s для фікшн кнігі. Гэты <a %(blog_post)s>першы выпуск</a> даволі невялікі: каля 300 ГБ вокладак кніг з форка Libgen.rs, як фікшн, так і нефікшн. Яны арганізаваны такім жа чынам, як яны з'яўляюцца на libgen.rs, напрыклад: %(example)s для нефікшн кнігі. Як і ў выпадку з калекцыяй Z-Library, мы паклалі іх усе ў вялікі .tar файл, які можна змантраваць з дапамогай <a %(a_ratarmount)s>ratarmount</a>, калі вы хочаце непасрэдна абслугоўваць файлы. Выпуск 1 (%(date)s) Кароткая гісторыя розных адгалінаванняў Library Genesis (або “Libgen”) заключаецца ў тым, што з цягам часу розныя людзі, якія ўдзельнічалі ў Library Genesis, пасварыліся і пайшлі сваімі шляхамі. Паводле гэтага <a %(a_mhut)s>паведамлення на форуме</a>, Libgen.li першапачаткова размяшчаўся на “http://free-books.dontexist.com”. Версія “.fun” была створана першапачатковым заснавальнікам. Яна перапрацоўваецца на карысць новай, больш размеркаванай версіі. <a %(a_li)s>Версія “.li”</a> мае велізарную калекцыю коміксаў, а таксама іншага кантэнту, які пакуль не даступны для масавага спампоўвання праз торэнты. Яна мае асобную калекцыю торэнтаў з мастацкімі кнігамі і ўтрымлівае метададзеныя <a %(a_scihub)s>Sci-Hub</a> у сваёй базе дадзеных. Версія “.rs” мае вельмі падобныя дадзеныя і найбольш паслядоўна выпускае сваю калекцыю ў выглядзе масавых торэнтаў. Яна прыкладна падзелена на раздзелы “мастацкая літаратура” і “не-мастацкая літаратура”. Першапачаткова на “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> у пэўным сэнсе таксама з'яўляецца адгалінаваннем Library Genesis, хоць яны выкарыстоўвалі іншую назву для свайго праекта. Libgen.rs Мы таксама ўзбагачаем нашу калекцыю крыніцамі толькі з метаданымі, якія мы можам супаставіць з файламі, напрыклад, выкарыстоўваючы нумары ISBN або іншыя палі. Ніжэй прыведзены агляд гэтых крыніц. Зноў жа, некаторыя з гэтых крыніц цалкам адкрытыя, у той час як для іншых нам даводзіцца іх скрабаць. Звярніце ўвагу, што ў пошуку метаданых мы паказваем арыгінальныя запісы. Мы не аб'ядноўваем запісы. Крыніцы толькі з метаданымі Open Library — гэта праект з адкрытым зыходным кодам ад Internet Archive для каталогізацыі кожнай кнігі ў свеце. Ён мае адну з найбуйнейшых у свеце аперацый па сканаванні кніг і мае шмат кніг, даступных для лічбавага пазычання. Яго каталог метаданых кніг даступны для бясплатнага спампоўвання і ўключаны ў архіў Анны (хоць у цяперашні час не ў пошуку, за выключэннем выпадкаў, калі вы яўна шукаеце ідэнтыфікатар Open Library). Open Library Выключаючы дублікаты Апошняе абнаўленне Працэнты колькасці файлаў %% адлюстравана AA / даступныя торэнты Памер Крыніца Ніжэй прыведзены кароткі агляд крыніц файлаў на Ганнавай Архіве. Паколькі ценявыя бібліятэкі часта сінхранізуюць дадзеныя адна з адной, існуе значнае перакрыцце паміж бібліятэкамі. Вось чаму лічбы не складаюцца ў агульную колькасць. Працэнт “люстранаваных і пасеяных архівам Анны” паказвае, колькі файлаў мы люстраем самі. Мы масава пасеем гэтыя файлы праз торэнты і робім іх даступнымі для прамога спампоўвання праз партнёрскія вэб-сайты. Агляд Агульная колькасць Торэнты на Аннавай Архіве Для атрымання інфармацыі пра Sci-Hub, калі ласка, звярніцеся да яго <a %(a_scihub)s>афіцыйнага сайта</a>, <a %(a_wikipedia)s>старонкі ў Вікіпедыі</a> і гэтага <a %(a_radiolab)s>падкаста</a>. Звярніце ўвагу, што Sci-Hub быў <a %(a_reddit)s>замарожаны з 2021 года</a>. Ён быў замарожаны раней, але ў 2021 годзе было дададзена некалькі мільёнаў артыкулаў. Тым не менш, некаторая абмежаваная колькасць артыкулаў дадаецца ў калекцыі “scimag” Libgen, але гэтага недастаткова для стварэння новых масавых торэнтаў. Мы выкарыстоўваем метаданыя Sci-Hub, прадастаўленыя <a %(a_libgen_li)s>Libgen.li</a> у яго калекцыі “scimag”. Мы таксама выкарыстоўваем набор дадзеных <a %(a_dois)s>dois-2022-02-12.7z</a>. Звярніце ўвагу, што торэнты “smarch” <a %(a_smarch)s>састарэлі</a> і таму не ўключаны ў наш спіс торэнтаў. Торэнты на Libgen.li Торэнты на Libgen.rs Метаданыя і торэнты Абнаўленні на Reddit Падкаст Старонка ў Вікіпедыі Sci-Hub Sci-Hub: замарожаны з 2021 года; большасць даступная праз торэнты Libgen.li: невялікія дапаўненні з таго часу</div> Некаторыя бібліятэкі-крыніцы прасоўваюць масавае распаўсюджванне сваіх дадзеных праз торэнты, у той час як іншыя не ахвотна дзеляцца сваёй калекцыяй. У апошнім выпадку, Архіў Анны спрабуе скрабаць іх калекцыі і рабіць іх даступнымі (глядзіце нашу старонку <a %(a_torrents)s>Торэнты</a>). Ёсць таксама прамежкавыя сітуацыі, напрыклад, калі бібліятэкі-крыніцы гатовыя дзяліцца, але не маюць рэсурсаў для гэтага. У такіх выпадках мы таксама спрабуем дапамагчы. Ніжэй прыведзены агляд таго, як мы ўзаемадзейнічаем з рознымі бібліятэкамі-крыніцамі. Бібліятэкі-крыніцы %(icon)s Розныя базы дадзеных файлаў, раскіданыя па кітайскім інтэрнэце; часта платныя базы дадзеных %(icon)s Большасць файлаў даступныя толькі з прэміум-акаўнтамі BaiduYun; нізкая хуткасць загрузкі. %(icon)s Архіў Анны кіруе калекцыяй <a %(duxiu)s>файлаў DuXiu</a> %(icon)s Розныя базы метададзеных, раскіданыя па кітайскім інтэрнэце; хоць часта гэта платныя базы даных %(icon)s Няма лёгкадаступных дампаў метададзеных для ўсёй іх калекцыі. %(icon)s Архіў Анны кіруе калекцыяй <a %(duxiu)s>метададзеных DuXiu</a> Файлы %(icon)s Файлы даступныя толькі для пазыкі на абмежаванай аснове, з рознымі абмежаваннямі доступу. %(icon)s Архіў Анны кіруе калекцыяй <a %(ia)s>файлаў IA</a> %(icon)s Некаторая метадата даступная праз <a %(openlib)s>дампы базы дадзеных Open Library</a>, але яны не ахопліваюць усю калекцыю IA. %(icon)s Няма лёгка даступных дампаў метадатаў для ўсёй іх калекцыі. %(icon)s Архіў Анны кіруе калекцыяй <a %(ia)s>метадатаў IA</a> Апошняе абнаўленне %(icon)s Архіў Анны і Libgen.li сумесна кіруюць калекцыямі <a %(comics)s>коміксаў</a>, <a %(magazines)s>часопісаў</a>, <a %(standarts)s>стандартных дакументаў</a> і <a %(fiction)s>мастацкай літаратуры (аддзялілася ад Libgen.rs)</a>. %(icon)s Торэнты Нон-фікшн падзяляюцца з Libgen.rs (і люструюцца <a %(libgenli)s>тут</a>). %(icon)s Штоквартальныя <a %(dbdumps)s>дампы базы дадзеных HTTP</a> %(icon)s Аўтаматызаваныя торэнты для <a %(nonfiction)s>Нон-фікшн</a> і <a %(fiction)s>Фікшн</a> %(icon)s Архіў Анны кіруе калекцыяй <a %(covers)s>торэнтаў вокладак кніг</a> %(icon)s Штодзённыя <a %(dbdumps)s>дампы базы дадзеных HTTP</a> Метаданыя %(icon)s Штомесячныя <a %(dbdumps)s>дампы базы дадзеных</a> %(icon)s Дадзеныя торэнтаў даступныя <a %(scihub1)s>тут</a>, <a %(scihub2)s>тут</a> і <a %(libgenli)s>тут</a> %(icon)s Некаторыя новыя файлы <a %(libgenrs)s>дадаюцца</a> у “scimag” Libgen, але не дастаткова, каб апраўдаць новыя торэнты. %(icon)s Sci-Hub замарозіў новыя файлы з 2021 года. %(icon)s Дампы метадатаў даступныя <a %(scihub1)s>тут</a> і <a %(scihub2)s>тут</a>, а таксама як частка <a %(libgenli)s>базы дадзеных Libgen.li</a> (якую мы выкарыстоўваем) Крыніца %(icon)s Розныя меншыя або адзіночныя крыніцы. Мы заклікаем людзей спачатку загружаць у іншыя ценявыя бібліятэкі, але часам у людзей ёсць калекцыі, якія занадта вялікія, каб іншыя маглі іх сартаваць, хоць і не дастаткова вялікія, каб заслугоўваць уласную катэгорыю. %(icon)s Няма магчымасці атрымаць непасрэдна ў вялікім аб'ёме, абаронена ад скрапінгу %(icon)s Архіў Анны кіруе калекцыяй <a %(worldcat)s>метададзеных OCLC (WorldCat)</a> %(icon)s Архіў Анны і Z-Library сумесна кіруюць калекцыяй <a %(metadata)s>метадатаў Z-Library</a> і <a %(files)s>файлаў Z-Library</a> Наборы дадзеных Мы аб'ядноўваем усе вышэйзгаданыя крыніцы ў адну аб'яднаную базу дадзеных, якую мы выкарыстоўваем для абслугоўвання гэтага сайта. Гэтая аб'яднаная база дадзеных не даступная непасрэдна, але паколькі Архіў Анны цалкам з адкрытым зыходным кодам, яе можна даволі лёгка <a %(a_generated)s>стварыць</a> або <a %(a_downloaded)s>спампаваць</a> у выглядзе баз дадзеных ElasticSearch і MariaDB. Скрыпты на гэтай старонцы аўтаматычна спампуюць усе неабходныя метаданыя з вышэйзгаданых крыніц. Калі вы хочаце даследаваць нашы дадзеныя перад тым, як запускаць гэтыя скрыпты лакальна, вы можаце паглядзець нашы файлы JSON, якія спасылаюцца на іншыя файлы JSON. <a %(a_json)s>Гэты файл</a> з'яўляецца добрым пачаткам. Аб'яднаная база дадзеных Торэнты ад Архіва Анны прагляд пошук Розныя меншыя або адзіночныя крыніцы. Мы заклікаем людзей спачатку загружаць у іншыя ценявыя бібліятэкі, але часам у людзей ёсць калекцыі, якія занадта вялікія, каб іншыя маглі іх сартаваць, хоць і не дастаткова вялікія, каб заслугоўваць уласную катэгорыю. Агляд з <a %(a1)s>старонкі з наборамі дадзеных</a>. З <a %(a_href)s>aaaaarg.fail</a>. Выглядае даволі поўным. Ад нашага валанцёра “cgiym”. З <a %(a_href)s><q>ACM Digital Library 2020</q></a> торэнта. Мае даволі вялікае перакрыцце з існуючымі калекцыямі артыкулаў, але вельмі мала супадзенняў MD5, таму мы вырашылі захаваць яго цалкам. Скрапінг <q>iRead eBooks</q> (= фанетычна <q>ai rit i-books</q>; airitibooks.com), выкананы валанцёрам <q>j</q>. Адпавядае metadata <q>airitibooks</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>. З калекцыі <a %(a1)s><q>Бібліятэка Александрыя</q></a>. Часткова з арыгінальнай крыніцы, часткова з the-eye.eu, часткова з іншых люстэркаў. З прыватнага сайта торэнтаў кніг, <a %(a_href)s>Bibliotik</a> (часта называецца “Bib”), кнігі якога былі аб'яднаны ў торэнты па назвах (A.torrent, B.torrent) і распаўсюджваліся праз the-eye.eu. Ад нашага валанцёра “bpb9v”. Для атрымання дадатковай інфармацыі пра <a %(a_href)s>CADAL</a>, глядзіце нататкі на нашай <a %(a_duxiu)s>старонцы набора дадзеных DuXiu</a>. Больш ад нашага валанцёра “bpb9v”, у асноўным файлы DuXiu, а таксама тэчка “WenQu” і “SuperStar_Journals” (SuperStar — гэта кампанія, якая стаіць за DuXiu). Ад нашага валанцёра “cgiym”, кітайскія тэксты з розных крыніц (прадстаўлены як падкаталогі), уключаючы <a %(a_href)s>China Machine Press</a> (буйное кітайскае выдавецтва). Некітайскія калекцыі (прадстаўлены як падкаталогі) ад нашага валанцёра “cgiym”. Скрапінг кніг пра кітайскую архітэктуру, выкананы валанцёрам <q>cm</q>: <q>Я атрымаў гэта, выкарыстоўваючы ўразлівасць сеткі ў выдавецтве, але гэтая дзірка ўжо закрыта</q>. Адпавядае metadata <q>chinese_architecture</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>. Кнігі з акадэмічнага выдавецтва <a %(a_href)s>De Gruyter</a>, сабраныя з некалькіх буйных торэнтаў. Скрапінг <a %(a_href)s>docer.pl</a>, польскага сайта абмену файламі, які засяроджваецца на кнігах і іншых пісьмовых працах. Скрапінг быў праведзены ў канцы 2023 года валанцёрам “p”. У нас няма добрых метададзеных з арыгінальнага сайта (нават пашырэнняў файлаў), але мы адфільтравалі файлы, падобныя на кнігі, і часта маглі здабыць метададзеныя з саміх файлаў. DuXiu epubs, непасрэдна з DuXiu, сабраныя валанцёрам “w”. Толькі нядаўнія кнігі DuXiu даступныя непасрэдна праз электронныя кнігі, таму большасць з іх павінны быць нядаўнімі. Астатнія файлы DuXiu ад валанцёра “m”, якія не былі ў фармаце DuXiu PDG (асноўны <a %(a_href)s>набор дадзеных DuXiu</a>). Сабраныя з многіх арыгінальных крыніц, на жаль, без захавання гэтых крыніц у шляху файла. <span></span> <span></span> <span></span> Скрапінг эратычных кніг, выкананы валанцёрам <q>do no harm</q>. Адпавядае metadata <q>hentai</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>. <span></span> <span></span> Калекцыя, скрапінг з японскага выдавецтва мангі валанцёрам “t”. <a %(a_href)s>Выбраныя судовыя архівы Лунцюань</a>, прадастаўленыя валанцёрам “c”. Скрапінг <a %(a_href)s>magzdb.org</a>, саюзніка Library Genesis (ён звязаны на галоўнай старонцы libgen.rs), але які не хацеў прадастаўляць свае файлы непасрэдна. Атрыманая валанцёрам “p” у канцы 2023 года. <span></span> Розныя невялікія загрузкі, занадта малыя для ўласнай падкалекцыі, але прадстаўлены як каталогі. Электронныя кнігі з AvaxHome, расійскага сайта для абмену файламі. Архіў газет і часопісаў. Адпавядае metadata <q>newsarch_magz</q> у <a %(a1)s><q>Іншыя скрапінгі metadata</q></a>. Скрапінг <a %(a1)s>Цэнтра дакументацыі па філасофіі</a>. Калекцыя валанцёра “o”, які збіраў польскія кнігі непасрэдна з арыгінальных рэлізных (“scene”) сайтаў. Аб'яднаныя калекцыі <a %(a_href)s>shuge.org</a> валанцёраў “cgiym” і “woz9ts”. <span></span> <a %(a_href)s>“Імператарская бібліятэка Трантора”</a> (названая ў гонар выдуманай бібліятэкі), скрапінг у 2022 годзе валанцёрам “t”. <span></span> <span></span> <span></span> Пад-пад-калекцыі (прадстаўлены як каталогі) ад валанцёра “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (ад <a %(a_sikuquanshu)s>Dizhi(迪志)</a> на Тайвані), mebook (mebook.cc, 我的小书屋, мая маленькая кніжная пакой — woz9ts: “Гэты сайт у асноўным засяроджваецца на абмене файламі электронных кніг высокай якасці, некаторыя з якіх былі набраны самім уладальнікам. Уладальнік быў <a %(a_arrested)s>арыштаваны</a> у 2019 годзе, і нехта зрабіў калекцыю файлаў, якімі ён падзяліўся.”). Астатнія файлы DuXiu ад валанцёра «woz9ts», якія не былі ў фармаце DuXiu уласнага PDG (яшчэ трэба пераўтварыць у PDF). "Загружаная" калекцыя падзелена на меншыя падкалекцыі, якія пазначаны ў AACIDs і назвах торэнтаў. Усе падкалекцыі спачатку былі дэдупікаваны ў параўнанні з асноўнай калекцыяй, хоць метададзеныя "upload_records" JSON файлы ўсё яшчэ ўтрымліваюць шмат спасылак на арыгінальныя файлы. Накніжныя файлы таксама былі выдалены з большасці падкалекцый і звычайна <em>не</em> пазначаны ў "upload_records" JSON. Падкалекцыі: Заўвагі Падкалекцыя Многія падкалекцыі самі складаюцца з пад-падкалекцый (напрыклад, з розных арыгінальных крыніц), якія прадстаўлены як каталогі ў палях "filepath". Загрузкі ў Архіў Анны Наш блог пра гэтыя дадзеныя <a %(a_worldcat)s>WorldCat</a> — гэта ўласная база дадзеных некамерцыйнай арганізацыі <a %(a_oclc)s>OCLC</a>, якая збірае метаданыя з бібліятэк па ўсім свеце. Гэта, верагодна, самая вялікая калекцыя бібліятэчных метаданых у свеце. У кастрычніку 2023 года мы <a %(a_scrape)s>выпусцілі</a> комплексны скрэйп базы дадзеных OCLC (WorldCat) у фармаце <a %(a_aac)s>кантэйнераў Аннавай Архівы</a>. Кастрычнік 2023, пачатковы выпуск: OCLC (WorldCat) Торэнты ад Аннавай Архівы Прыклад запісу ў Архіве Анны (арыгінальная калекцыя) Прыклад запісу ў Архіве Анны (калекцыя «zlib3») Торэнты ад Архіва Анны (метаданыя + змест) Паведамленне ў блогу пра Выпуск 1 Паведамленне ў блогу пра Выпуск 2 У канцы 2022 года меркаваныя заснавальнікі Z-Library былі арыштаваныя, а дамены канфіскаваныя ўладамі Злучаных Штатаў. З таго часу сайт павольна вяртаецца ў інтэрнэт. Невядома, хто ў цяперашні час ім кіруе. Абнаўленне на люты 2023 года. Z-Library мае свае карані ў супольнасці <a %(a_href)s>Library Genesis</a> і першапачаткова выкарыстоўвала іх дадзеныя. З таго часу яна значна прафесіяналізавалася і мае значна больш сучасны інтэрфейс. Таму яны могуць атрымліваць значна больш ахвяраванняў, як грашовых для паляпшэння свайго сайта, так і ахвяраванняў новых кніг. Яны назапасілі вялікую калекцыю ў дадатак да Library Genesis. Калекцыя складаецца з трох частак. Арыгінальныя апісанні старонак для першых двух частак захаваны ніжэй. Вам патрэбны ўсе тры часткі, каб атрымаць усе дадзеныя (за выключэннем замяшчаных торэнтаў, якія перакрэслены на старонцы торэнтаў). %(title)s: наш першы выпуск. Гэта быў самы першы выпуск таго, што тады называлася «Пірацкае Люстэрка Бібліятэкі» («pilimi»). %(title)s: другі выпуск, на гэты раз з усімі файламі, загорнутымі ў .tar файлы. %(title)s: інкрэментальныя новыя выпускі, выкарыстоўваючы <a %(a_href)s>фармат кантэйнераў Архіва Анны (AAC)</a>, цяпер выпушчаныя ў супрацоўніцтве з камандай Z-Library. Першапачатковае люстэрка было старанна атрымана на працягу 2021 і 2022 гадоў. На дадзены момант яно крыху састарэла: яно адлюстроўвае стан калекцыі ў чэрвені 2021 года. Мы абновім гэта ў будучыні. Зараз мы сканцэнтраваны на выпуску гэтага першага выпуску. Паколькі Library Genesis ужо захаваны з дапамогай публічных торэнтаў і ўключаны ў Z-Library, мы правялі базавую дэдуплікацыю супраць Library Genesis у чэрвені 2022 года. Для гэтага мы выкарыстоўвалі MD5-хэшы. Верагодна, у бібліятэцы ёсць значна больш дублікатаў, такіх як некалькі фарматаў файлаў з адной і той жа кнігай. Гэта цяжка дакладна выявіць, таму мы гэтага не робім. Пасля дэдуплікацыі ў нас засталося больш за 2 мільёны файлаў, агульным аб'ёмам крыху менш за 7 ТБ. Калекцыя складаецца з двух частак: дампа метададзеных MySQL «.sql.gz» і 72 торэнт-файлаў аб'ёмам каля 50-100 ГБ кожны. Метададзеныя ўтрымліваюць дадзеныя, як паведамляецца на сайце Z-Library (назва, аўтар, апісанне, тып файла), а таксама фактычны памер файла і md5sum, якія мы назіралі, бо часам яны не супадаюць. Здаецца, ёсць дыяпазоны файлаў, для якіх Z-Library мае няправільныя метададзеныя. У некаторых асобных выпадках мы таксама маглі няправільна загрузіць файлы, што мы паспрабуем выявіць і выправіць у будучыні. Вялікія торэнт-файлы ўтрымліваюць фактычныя дадзеныя кніг, з ідэнтыфікатарам Z-Library у якасці імя файла. Пашырэнні файлаў можна аднавіць з дапамогай дампа метададзеных. Калекцыя ўключае змешаны кантэнт, як нон-фікшн, так і фікшн (не падзелены, як у Library Genesis). Якасць таксама вельмі розная. Гэта першае выданне цяпер цалкам даступна. Звярніце ўвагу, што торэнт-файлы даступныя толькі праз наш Tor-люстэрка. Выпуск 1 (%(date)s) Гэта адзіны дадатковы торэнт-файл. Ён не ўтрымлівае ніякай новай інфармацыі, але ў ім ёсць некаторыя дадзеныя, якія могуць заняць некаторы час для вылічэння. Гэта робіць яго зручным, бо загрузка гэтага торэнта часта хутчэйшая, чым вылічэнне з нуля. У прыватнасці, ён утрымлівае індэксы SQLite для tar-файлаў, для выкарыстання з <a %(a_href)s>ratarmount</a>. Дадатак да выпуску 2 (%(date)s) Мы атрымалі ўсе кнігі, якія былі дададзены ў Z-Library паміж нашым апошнім люстэркам і жніўнем 2022 года. Мы таксама вярнуліся і сабралі некаторыя кнігі, якія прапусцілі ў першы раз. У цэлым, гэтая новая калекцыя складае каля 24 ТБ. Зноў жа, гэтая калекцыя дэдуплікаваная супраць Library Genesis, паколькі для гэтай калекцыі ўжо даступныя торэнты. Дадзеныя арганізаваны аналагічна першаму выпуску. Існуе дамп метададзеных MySQL «.sql.gz», які таксама ўключае ўсе метададзеныя з першага выпуску, тым самым замяняючы яго. Мы таксама дадалі некалькі новых слупкоў: Мы згадвалі пра гэта ў мінулы раз, але проста для ўдакладнення: «filename» і «md5» - гэта фактычныя ўласцівасці файла, у той час як «filename_reported» і «md5_reported» - гэта тое, што мы сабралі з Z-Library. Часам гэтыя два не супадаюць, таму мы ўключылі абодва. Для гэтага выпуску мы змянілі кадыроўку на «utf8mb4_unicode_ci», якая павінна быць сумяшчальная са старымі версіямі MySQL. Файлы дадзеных падобныя на мінулы раз, хоць яны значна большыя. Мы проста не маглі сабе дазволіць ствараць мноства меншых торэнт-файлаў. «pilimi-zlib2-0-14679999-extra.torrent» утрымлівае ўсе файлы, якія мы прапусцілі ў мінулым выпуску, у той час як іншыя торэнты ўключаюць новыя дыяпазоны ідэнтыфікатараў.  <strong>Абнаўленне %(date)s:</strong> Мы зрабілі большасць нашых торэнтаў занадта вялікімі, што выклікала праблемы ў торэнт-кліентаў. Мы выдалілі іх і выпусцілі новыя торэнты. <strong>Абнаўленне %(date)s:</strong> Файлаў усё яшчэ было занадта шмат, таму мы загарнулі іх у tar-файлы і зноў выпусцілі новыя торэнты. %(key)s: ці ёсць гэты файл ужо ў Library Genesis, у калекцыі нон-фікшн або фікшн (супадзенне па md5). %(key)s: у якім торэнце знаходзіцца гэты файл. %(key)s: усталёўваецца, калі мы не змаглі загрузіць кнігу. Выпуск 2 (%(date)s) Выпускі Zlib (арыгінальныя апісанні старонак) Tor дамен Асноўны сайт Скрапінг Z-Library Калекцыя “Кітайская” ў Z-Library, здаецца, такая ж, як і наша калекцыя DuXiu, але з рознымі MD5. Мы выключаем гэтыя файлы з торэнтаў, каб пазбегнуць дублявання, але ўсё роўна паказваем іх у нашым пошукавым індэксе. Метаданыя Вы атрымліваеце %(percentage)s%% бонусных хуткіх загрузак, таму што вас накіраваў карыстальнік %(profile_link)s. Гэта распаўсюджваецца на ўвесь перыяд членства. Ахвяраваць Далучыцца Выбрана зніжкі да %(percentage)s%% Alipay падтрымлівае міжнародныя крэдытныя/дэбетавыя карты. Глядзіце <a %(a_alipay)s>гэты гід</a> для атрымання дадатковай інфармацыі. Дасылайце нам падарункавыя карты Amazon.com з дапамогай вашай крэдытнай/дэбетавай карты. Вы можаце купіць крыптавалюту з дапамогай крэдытных/дэбетавых карт. WeChat (Weixin Pay) падтрымлівае міжнародныя крэдытныя/дэбетныя карты. У дадатку WeChat перайдзіце ў “Me => Services => Wallet => Add a Card”. Калі вы гэтага не бачыце, уключыце гэта праз “Me => Settings => General => Tools => Weixin Pay => Enable”. (выкарыстоўвайце пры адпраўцы Ethereum з Coinbase) скапіявана! капіраваць (мінімальная сума) (папярэджанне: высокая мінімальная сума) -%(percentage)s%% 12 месяцаў 1 месяц 24 месяцы 3 месяцы 48 месяцаў 6 месяцаў 96 месяцаў Выберыце, на які тэрмін вы хочаце падпісацца. <div %(div_monthly_cost)s></div><div %(div_after)s>пасля <span %(span_discount)s></span> зніжак</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% на 12 месяцаў на 1 месяц на працягу 24 месяцаў на 3 месяцы на 48 месяцаў на працягу 6 месяцаў на працягу 96 месяцаў %(monthly_cost)s / месяц звяжыцеся з намі Прамыя <strong>SFTP</strong> серверы Ахвяраванне на ўзроўні прадпрыемства або абмен на новыя калекцыі (напрыклад, новыя сканы, OCR’аваныя datasets). Экспертны доступ <strong>Неабмежаваны</strong> высокахуткасны доступ <div %(div_question)s>Ці магу я абнавіць сваё сяброўства або атрымаць некалькі сяброўстваў?</div> <div %(div_question)s>Ці магу я зрабіць ахвяраванне, не становячыся ўдзельнікам?</div> Вядома. Мы прымаем ахвяраванні любой сумы на гэты адрас Monero (XMR): %(address)s. <div %(div_question)s>Што азначаюць дыяпазоны за месяц?</div> Вы можаце дасягнуць ніжняй мяжы дыяпазону, ужыўшы ўсе зніжкі, напрыклад, выбраўшы перыяд больш за месяц. <div %(div_question)s>Ці аўтаматычна абнаўляюцца сяброўствы?</div> Сяброўствы <strong>не</strong> абнаўляюцца аўтаматычна. Вы можаце далучыцца на столькі часу, колькі хочаце. <div %(div_question)s>На што вы траціце ахвяраванні?</div> 100%% ідзе на захаванне і забеспячэнне доступу да сусветных ведаў і культуры. У цяперашні час мы ў асноўным трацім іх на серверы, захоўванне і прапускную здольнасць. Ніводная капейка не ідзе асабіста камусьці з каманды. <div %(div_question)s>Ці магу я адправіць вялікую суму?</div> Так, вядома! Калі вы збіраецеся адправіць некалькі тысяч даляраў альбо больш, напішыце нам на %(email)s. <div %(div_question)s>Ці ёсць у вас іншыя спосабы аплаты?</div> у цяперашні час няма. Многія людзі не хочуць, каб падобныя архівы існавалі, таму мы павінны быць асцярожныя. Калі вы можаце дапамагчы нам бяспечна наладзіць іншыя (больш зручныя) спосабы аплаты, калі ласка, звяжыцеся з намі па адрасе %(email)s. Частыя пытанні па ахвяраваннях У вас ёсць <a %(a_donation)s>існуючае ахвяраванне</a> у працэсе. Калі ласка, завяршыце або адмяніце гэтае ахвяраванне, перш чым рабіць новае. <a %(a_all_donations)s>Прагледзець усе мае ахвяраванні</a> Для ахвяраванняў звыш $5000, калі ласка, звяжыцеся з намі непасрэдна па %(email)s. Мы вітаем буйныя ахвяраванні ад заможных асоб або ўстаноў.  Звярніце ўвагу, што хоць сяброўства на гэтай старонцы пазначана як «штомесячнае», гэта аднаразовыя ахвяраванні (не паўтараюцца). Глядзіце <a %(faq)s>Частыя пытанні пра ахвяраванні</a>. Anna’s Archive — гэта некамерцыйны, з адкрытым зыходным кодам і адкрытымі дадзенымі праект. Дзякуючы ахвяраванням і ўдзелу ў членстве, вы падтрымліваеце нашу дзейнасць і развіццё. Усім нашым членам: дзякуй, што падтрымліваеце нас! ❤️ Для атрымання дадатковай інфармацыі азнаёмцеся з <a %(a_donate)s>Частымі пытаннямі аб ахвяраваннях</a>. Каб стаць удзельнікам, калі ласка, <a %(a_login)s>Увайдзіце або Зарэгіструйцеся</a>. Дзякуй за вашу падтрымку! $%(cost)s / месяц Калі вы зрабілі памылку падчас аплаты, мы не можам зрабіць вяртанне, але паспрабуем выправіць сітуацыю. Знайдзіце старонку “Crypto” у вашым дадатку або на сайце PayPal. Звычайна гэта знаходзіцца ў раздзеле “Фінансы”. Перайдзіце на старонку “Bitcoin” у вашым дадатку або на сайце PayPal. Націсніце кнопку “Transfer” %(transfer_icon)s, а затым “Send”. Alipay Alipay 支付宝 / WeChat 微信 Падарункавая карта Amazon %(amazon)s падарункавая карта Банкаўская карта Банкаўская карта (з выкарыстаннем прыкладання) Бінанс Крэдытная/дэбетавая/Apple/Google (BMC) Cash App Крэдытная/дэбетная карта Крэдытная/дэбетавая карта 2 Крэдытная/дэбетная карта (рэзервовая) Крыпта %(bitcoin_icon)s Карта / PayPal / Venmo PayPal (ЗША) %(bitcoin_icon)s PayPal PayPal (звычайны) Pix (Brazil) Revolut (часова недаступна) WeChat Выберыце сваю любімую крыптаманету: Ахвяруйце з дапамогай падарункавай карты Amazon. <strong>ВАЖНА:</strong> Гэты варыянт для %(amazon)s. Калі вы хочаце выкарыстоўваць іншы сайт Amazon, выберыце яго вышэй. <strong>ВАЖНА:</strong> Мы падтрымліваем толькі Amazon.com, іншыя сайты Amazon, напрыклад, .de, .co.uk, .ca, НЕ падтрымліваюцца. Калі ласка, НЕ пішыце сваё паведамленне. Увядзіце дакладную суму: %(amount)s Звярніце ўвагу, што нам трэба акругліць да сум, якія прымаюцца нашымі рэсэлерамі (мінімум %(minimum)s). Ахвяруйце з дапамогай крэдытнай/дэбетавай карты праз прыкладанне Alipay (вельмі лёгка наладзіць). Усталюйце прыкладанне Alipay з <a %(a_app_store)s>Apple App Store</a> або <a %(a_play_store)s>Google Play Store</a>. Зарэгіструйцеся, выкарыстоўваючы свой нумар тэлефона. Дадатковыя асабістыя дадзеныя не патрабуюцца. <span %(style)s>1</span>Усталюйце прыкладанне Alipay Падтрымліваюцца: Visa, MasterCard, JCB, Diners Club і Discover. Глядзіце <a %(a_alipay)s>гэты гід</a> для больш падрабязнай інфармацыі. <span %(style)s>2</span>Дадайце банкаўскую карту З Binance вы купляеце Bitcoin з дапамогай крэдытнай/дэбетавай карты або банкаўскага рахунку, а затым ахвяруеце гэты Bitcoin нам. Такім чынам, мы можам заставацца бяспечнымі і ананімнымі пры прыняцці вашага ахвяравання. Binance даступны амаль у кожнай краіне і падтрымлівае большасць банкаў і крэдытных/дэбетавых карт. Гэта наша асноўная рэкамендацыя на дадзены момант. Мы ўдзячныя вам за тое, што вы знайшлі час, каб даведацца, як ахвяраваць з дапамогай гэтага метаду, бо гэта вельмі дапамагае нам. Для крэдытных карт, дэбетавых карт, Apple Pay і Google Pay мы выкарыстоўваем “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). У іх сістэме адна “кава” роўная $5, таму ваша ахвяраванне будзе акруглена да бліжэйшага множніка 5. Ахвяруйце з дапамогай Cash App. Калі ў вас ёсць Cash App, гэта самы просты спосаб ахвяраваць! Звярніце ўвагу, што за транзакцыі менш за %(amount)s, Cash App можа спаганяць плату ў памеры %(fee)s. За %(amount)s і больш — бясплатна! Ахвяраваць з дапамогай крэдытнай або дэбетавай карты. Гэты метад выкарыстоўвае пастаўшчыка криптовалюты ў якасці прамежкавага канвертара. Гэта можа быць крыху заблытана, таму выкарыстоўвайце гэты метад толькі калі іншыя спосабы аплаты не працуюць. Таксама ён не працуе ва ўсіх краінах. Мы не можам падтрымліваць крэдытныя/дэбетавыя карты наўпрост, таму што банкі не хочуць з намі працаваць. ☹ Аднак ёсць некалькі спосабаў выкарыстання крэдытных/дэбетавых карт праз іншыя метады аплаты: З крыпта вы можаце ахвяраваць з дапамогай BTC, ETH, XMR і SOL. Выкарыстоўвайце гэты варыянт, калі вы ўжо знаёмыя з крыптавалютай. З дапамогай крыпта вы можаце ахвяраваць, выкарыстоўваючы BTC, ETH, XMR і іншыя. Крыпта экспрэс паслугі Калі вы ўпершыню карыстаецеся криптавалютай, мы раім выкарыстоўваць %(options)s для пакупкі і ахвяравання Bitcoin (першай і найбольш выкарыстоўванай криптавалюты). Звярніце ўвагу, што для невялікіх ахвяраванняў камісія за крэдытную карту можа ліквідаваць нашу %(discount)s%% зніжку, таму мы рэкамендуем больш працяглыя падпіскі. Ахвяруйце з дапамогай крэдытнай/дэбетавай карты, PayPal або Venmo. Вы можаце выбраць паміж імі на наступнай старонцы. Google Pay і Apple Pay таксама могуць працаваць. Звярніце ўвагу, што для невялікіх ахвяраванняў камісія высокая, таму мы рэкамендуем больш працяглыя падпіскі. Для ахвяравання з дапамогай PayPal US мы будзем выкарыстоўваць PayPal Crypto, што дазваляе нам заставацца ананімнымі. Мы цэнім ваш час, выдаткаваны на вывучэнне гэтага метаду ахвяравання, бо гэта вельмі дапамагае нам. Ахвяраваць з дапамогай PayPal. Ахвяраваць з дапамогай вашага звычайнага акаўнта PayPal. Ахвяраваць з дапамогай Revolut. Калі ў вас ёсць Revolut, гэта самы просты спосаб ахвяраваць! Гэты спосаб аплаты дазваляе максімум %(amount)s. Калі ласка, выберыце іншую працягласць або спосаб аплаты. Гэты спосаб аплаты патрабуе мінімум %(amount)s. Калі ласка, выберыце іншую працягласць або спосаб аплаты. Бінанс Coinbase Kraken Калі ласка, выберыце спосаб аплаты. «Адапртаваць торэнт»: ваша імя карыстальніка або паведамленне ў назве торэнт-файла <div %(div_months)s>раз на 12 месяцаў сяброўства</div> Ваш лагін або ананімная згадка ў крэдытах Ранні доступ да новых функцый Эксклюзіўны Telegram з абнаўленнямі за кулісамі %(number)s хуткіх спамповак у дзень калі вы ахвяруеце ў гэтым месяцы! <a %(a_api)s>Доступ да JSON API</a> Легендарны статус у захаванні ведаў і культуры чалавецтва Папярэднія перавагі, плюс: Атрымайце <strong>%(percentage)s%% бонусных загрузак</strong>, <a %(a_refer)s>запрасіўшы сяброў</a>. Артыкулы SciDB <strong>неабмежавана</strong> без верыфікацыі Калі задаеце пытанні аб уліковым запісе або ахвяраваннях, дадайце ваш ID уліковага запісу, скрыншоты, квітанцыі, як мага больш інфармацыі. Мы правяраем нашу электронную пошту кожныя 1-2 тыдні, таму не ўключэнне гэтай інфармацыі затрымае вырашэнне. Каб атрымаць яшчэ больш загрузак, <a %(a_refer)s>запрасіце сваіх сяброў</a>! Мы невялікая каманда валанцёраў. Адказ можа заняць 1-2 тыдні. Звярніце ўвагу, што імя ўліковага запісу або малюнак можа выглядаць дзіўна. Няма неабходнасці турбавацца! Гэтымі ўліковымі запісамі кіруюць нашы партнёры па ахвяраваннях. Нашы акаўнты не былі ўзламаныя. Ахвяраваць <span %(span_cost)s></span> <span %(span_label)s></span> на 12 месяцаў “%(tier_name)s” на 1 месяц “%(tier_name)s” на 24 месяцы “%(tier_name)s” на 3 месяцы “%(tier_name)s” на працягу 48 месяцаў “%(tier_name)s” на 6 месяцаў “%(tier_name)s” на 96 месяцаў “%(tier_name)s” Вы ўсё яшчэ можаце адмяніць ахвяраванне падчас афармлення. Націсніце кнопку ахвяравання, каб пацвердзіць гэтае ахвяраванне. <strong>Важная заўвага:</strong> Крыптацэны могуць моцна вагацца, часам нават на 20%% за некалькі хвілін. Гэта ўсё яшчэ менш, чым зборы, якія мы нясем з многімі пастаўшчыкамі плацяжоў, якія часта бяруць 50-60%% за працу з такой "ценявой дабрачыннасцю", як наша. <u>Калі вы дашлеце нам квітанцыю з першапачатковай цаной, якую вы заплацілі, мы ўсё роўна залічым ваш рахунак на абраны ўзровень сяброўства</u> (пакуль квітанцыя не старэйшая за некалькі гадзін). Мы вельмі ўдзячныя, што вы гатовыя мірыцца з такімі рэчамі, каб падтрымаць нас! ❤️ ❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. <span %(span_circle)s>1</span>Купіць Bitcoin на Paypal <span %(span_circle)s>2</span>Пералічыце Bitcoin на наш адрас ✅ Перанакіраванне на старонку ахвяраванняў… Падтрымаць праект Калі ласка, пачакайце прынамсі <span %(span_hours)s>24 гадзіны</span> (і абнавіце гэтую старонку), перш чым звязацца з намі. Калі вы хочаце зрабіць ахвяраванне (любы памер) без сяброўства, не саромейцеся выкарыстоўваць гэты адрас Monero (XMR): %(address)s. Пасля адпраўкі вашай падарункавай карты наша аўтаматычная сістэма пацвердзіць яе на працягу некалькіх хвілін. Калі гэта не спрацуе, паспрабуйце адправіць падарункавую карту яшчэ раз (<a %(a_instr)s>інструкцыі</a>). Калі гэта ўсё яшчэ не працуе, калі ласка, напішыце нам па электроннай пошце, і Ганна праверыць гэта ўручную (гэта можа заняць некалькі дзён), і абавязкова ўкажыце, ці спрабавалі вы паўторна адправіць ужо. Прыклад: Калі ласка, выкарыстоўвайце <a %(a_form)s>афіцыйную форму Amazon.com</a>, каб адправіць нам падарункавую карту на %(amount)s на электронную пошту ніжэй. Электронная пошта атрымальніка "Каму" у форме: Падарункавая карта Amazon Мы не можам прыняць іншыя метады падарункавых картак, <strong>толькі адпраўленыя непасрэдна з афіцыйнай формы на Amazon.com</strong>. Мы не зможам вярнуць вашу падарункавую картку, калі вы не выкарыстоўваеце гэтую форму. Выкарыстоўваць толькі адзін раз. Унікальна для вашага акаўнта, не дзяліцеся. Чакаем падарункавую карту… (абнавіце старонку, каб праверыць) Адкрыйце <a %(a_href)s>старонку ахвяраванняў з QR-кодам</a>. Скануйце QR-код з дапамогай прыкладання Alipay або націсніце кнопку, каб адкрыць прыкладанне Alipay. Калі ласка, будзьце цярплівыя; старонка можа загружацца некаторы час, бо яна знаходзіцца ў Кітаі. <span %(style)s>3</span>Зрабіце ахвяраванне (скануйце QR-код або націсніце кнопку) Купіць манету PYUSD на PayPal Купіць Bitcoin (BTC) у Cash App Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума вашага ахвяравання (%(amount)s), каб пакрыць транзакцыйныя зборы. Вы захаваеце ўсё, што застанецца. Перайдзіце на старонку “Bitcoin” (BTC) у Cash App. Перадайце Bitcoin на наш адрас Для невялікіх ахвяраванняў (менш за $25) вам можа спатрэбіцца выкарыстоўваць Rush або Priority. Націсніце кнопку “Send bitcoin”, каб зрабіць “вывад”. Пераключыцеся з долараў на BTC, націснуўшы на значок %(icon)s. Увядзіце суму BTC ніжэй і націсніце “Send”. Калі ўзнікнуць цяжкасці, глядзіце <a %(help_video)s>гэта відэа</a>. Экспрэс паслугі зручныя, але спаганяюць большыя зборы. Вы можаце выкарыстоўваць гэта замест крыпта біржы, калі хочаце хутка зрабіць больш буйное ахвяраванне і не супраць платы ў памеры $5-10. Абавязкова адпраўце дакладную суму крыпта, паказаную на старонцы ахвяраванняў, а не суму ў $USD. Інакш плата будзе вылічана, і мы не зможам аўтаматычна апрацаваць ваша сяброўства. Часам пацверджанне можа заняць да 24 гадзін, таму абавязкова абнавіце гэтую старонку (нават калі яна пратэрмінавана). Інструкцыі па крэдытнай / дэбетавай карце Ахвяраваць праз нашу старонку крэдытных / дэбетавых картак Некаторыя крокі згадваюць крыпта-кашалькі, але не хвалюйцеся, вам не трэба нічога ведаць пра крыпта для гэтага. %(coin_name)s інструкцыі Скануйце гэты QR -код з дапамогай прыкладання Crypto Wallet, каб хутка запоўніць плацежныя дадзеныя Скануйце QR -код для аплаты Мы падтрымліваем толькі стандартную версію крыптаманет, без экзатычных сетак або версій манет. Пацверджанне транзакцыі можа заняць да гадзіны, у залежнасці ад манеты. Ахвяруйце %(amount)s на <a %(a_page)s>гэтым старонцы</a>. Гэта ахвяраванне скончылася. Калі ласка, адмяніце і стварыце новае. Калі вы ўжо аплацілі: Так, я адправіў па электроннай пошце квітанцыю Калі курс криптавалюты змяніўся падчас транзакцыі, абавязкова ўключыце квітанцыю, якая паказвае першапачатковы курс абмену. Мы вельмі ўдзячныя вам за тое, што вы выкарыстоўваеце криптавалюту, гэта нам вельмі дапамагае! ❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. <span %(span_circle)s>%(circle_number)s</span>Адпраўце нам квітанцыю па электроннай пошце Калі ў вас узнікнуць праблемы, калі ласка, звяжыцеся з намі па адрасе %(email)s і ўключыце як мага больш інфармацыі (напрыклад, скрыншоты). ✅ Дзякуй за вашу ахвяру! Анна ўручную актывуе ваша сяброўства на працягу некалькіх дзён. Адпраўце квітанцыю або скрыншот на ваш асабісты адрас для праверкі: Калі вы адправілі квітанцыю па электроннай пошце, націсніце гэтую кнопку, каб Ганна магла ўручную праверыць яе (гэта можа заняць некалькі дзён): Адпраўце квітанцыю або скрыншот на ваш асабісты адрас для праверкі. НЕ выкарыстоўвайце гэты адрас электроннай пошты для вашага ахвяравання праз PayPal. Адмяніць Так, калі ласка, адмяніце Вы ўпэўненыя, што жадаеце адмяніць? Не адмяняйце, калі вы ўжо аплацілі. ❌ Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. Зрабіць новае ахвяраванне ✅ Ваша ахвяраванне было адменена. Дата: %(date)s Ідэнтыфікатар: %(id)s Перасартаваць Статус: <span %(span_label)s>%(label)s</span> Усяго: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месяц на працягу %(duration)s месяцаў, уключаючы %(discounts)s%% зніжку)</span> Усяго: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месяц на працягу %(duration)s месяцаў)</span> 1. Увядзіце ваш email. 2. Выберыце спосаб аплаты. 3. Выберыце спосаб аплаты зноў. 4. Выберыце «Самастойны» кашалёк. 5. Націсніце “Я пацвярджаю ўласнасць”. 6. Вы павінны атрымаць квітанцыю па электроннай пошце. Калі ласка, дашліце яе нам, і мы пацвердзім ваша ахвяраванне як мага хутчэй. (вы можаце адмяніць і стварыць новае ахвяраванне) Інструкцыі па аплаце цяпер састарэлі. Калі вы хочаце зрабіць яшчэ адну ахвяру, выкарыстоўвайце кнопку “Reorder” вышэй. Вы ўжо аплацілі. Калі вы ўсё ж хочаце перагледзець інструкцыі па аплаце, націсніце тут: Паказаць старыя інструкцыі па аплаце Калі старонка ахвяраванняў будзе заблакаваная, паспрабуйце іншае інтэрнэт-злучэнне (напрыклад, VPN або мабільны інтэрнэт). На жаль, старонка Alipay часта даступная толькі з <strong>мацерыковага Кітая</strong>. Магчыма, вам трэба будзе часова адключыць ваш VPN або выкарыстоўваць VPN для мацерыковага Кітая (часам таксама працуе Ганконг). <span %(span_circle)s>1</span>Ахвяруйце праз Alipay Ахвяруйце агульную суму %(total)s з дапамогай <a %(a_account)s>гэтага рахунку Alipay</a> Інструкцыі Alipay <span %(span_circle)s>1</span>Пералічыце на адзін з нашых крыптарахункаў Ахвяруйце агульную суму %(total)s на адзін з гэтых адрасоў: Інструкцыі па криптовалютам Выконвайце інструкцыі для пакупкі Bitcoin (BTC). Вам трэба купіць толькі тую суму, якую вы хочаце ахвяраваць, %(total)s. Увядзіце наш адрас Bitcoin (BTC) у якасці атрымальніка і выконвайце інструкцыі для адпраўкі вашага ахвяравання ў памеры %(total)s: <span %(span_circle)s>1</span>Ахвяраваць на Pix Ахвяруйце агульную суму %(total)s, выкарыстоўваючы <a %(a_account)s>гэты Pix рахунак Інструкцыі Pix <span %(span_circle)s>1</span>Ахвяраваць праз WeChat Ахвяруйце агульную суму %(total)s з дапамогай <a %(a_account)s>гэтага акаўнта WeChat</a> Інструкцыі WeChat Выкарыстоўвайце любы з наступных экспрэс-сэрвісаў “крэдытная карта ў Bitcoin”, якія займаюць усяго некалькі хвілін: Адрас BTC / Bitcoin (знешні кашалёк): Сума BTC / Bitcoin: Запоўніце наступныя дэталі ў форме: Калі якая-небудзь з гэтай інфармацыі састарэла, калі ласка, напішыце нам па электроннай пошце, каб паведаміць нам. Калі ласка, выкарыстоўвайце <span %(underline)s>дакладную суму</span>. Вашы агульныя выдаткі могуць быць вышэйшымі з-за камісій за крэдытныя карты. Для невялікіх сум гэта можа быць больш, чым наша зніжка, на жаль. (мінімум: %(minimum)s, без праверкі для першай транзакцыі) (мінімум: %(minimum)s) (мінімум: %(minimum)s) (мінімум: %(minimum)s, без праверкі для першай транзакцыі) (мінімум: %(minimum)s) (мінімум: %(minimum)s у залежнасці ад краіны, без праверкі для першай транзакцыі) Выконвайце інструкцыі, каб набыць манету PYUSD (PayPal USD). Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума, якую вы ахвяруеце (%(amount)s), каб пакрыць выдаткі на транзакцыі. Вы захаваеце ўсё, што засталося. Перайдзіце на старонку "PYUSD" у вашым дадатку або на сайце PayPal. Націсніце кнопку "Пераклад" %(icon)s, а затым "Адправіць". Абнавіць статус Каб скінуць таймер, проста стварыце новае ахвяраванне. Абавязкова выкарыстоўвайце колькасць BTC ніжэй, <em>НЕ</em> еўра або долары, інакш мы не атрымаем правільную суму і не зможам аўтаматычна пацвердзіць ваша сяброўства. Купіць Bitcoin (BTC) у Revolut Купіце крыху больш (мы рэкамендуем %(more)s больш), чым сума вашага ахвяравання (%(amount)s), каб пакрыць транзакцыйныя зборы. Вы захаваеце ўсё, што застанецца. Перайдзіце на старонку “Crypto” у Revolut, каб купіць Bitcoin (BTC). Перадайце Bitcoin на наш адрас Для невялікіх ахвяраванняў (менш за $25) вам можа спатрэбіцца выкарыстоўваць Rush або Priority. Націсніце кнопку “Send bitcoin”, каб зрабіць “вывад”. Пераключыцеся з еўра на BTC, націснуўшы на значок %(icon)s. Увядзіце суму BTC ніжэй і націсніце “Send”. Калі ўзнікнуць цяжкасці, глядзіце <a %(help_video)s>гэта відэа</a>. Статус: 1 2 Пакрокавая інструкцыя Глядзіце пакрокавую інструкцыю ніжэй. Інакш вы можаце быць заблакіраваны ў гэтым акаўнце! Калі вы яшчэ не зрабілі гэтага, запішыце ваш сакрэтны ключ для ўваходу: Дзякуй за ваш ахвяраванне! Час, які застаўся: Ахвяраванне Перадаць %(amount)s да %(account)s Чакаем пацверджання (абнавіце старонку, каб праверыць)… Чаканне перадачы (абнавіце старонку, каб праверыць)… Раней Хуткія загрузкі за апошнія 24 гадзіны ўлічваюцца ў штодзённым ліміце. Загрузкі з хуткіх партнёрскіх сервераў пазначаны %(icon)s. Апошнія 18 гадзін Файлы яшчэ не спампаваны. Загружаныя файлы не паказваюцца публічна. Усе часы ў UTC. Загружаныя файлы Калі вы запампавалі файл з хуткімі і павольнымі запампоўкамі, ён з'явіцца двойчы. Не хвалюйцеся занадта, шмат людзей спампоўваюць з сайтаў, на якія мы спасылаемся, і вельмі рэдка ўзнікаюць праблемы. Аднак, каб заставацца ў бяспецы, мы рэкамендуем выкарыстоўваць VPN (платны) або <a %(a_tor)s>Tor</a> (бясплатны). Я спампаваў «1984» Джорджа Оруэла, ці прыйдзе паліцыя да мяне дадому? Вы — Анна! Хто такая Анна? У нас ёсць стабільны JSON API для ўдзельнікаў, каб атрымаць хуткую спасылку для загрузкі: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (дакументацыя ўнутры JSON). Для іншых выпадкаў выкарыстання, такіх як ітэрацыя праз усе нашы файлы, стварэнне карыстацкага пошуку і гэтак далей, мы рэкамендуем <a %(a_generate)s>генераваць</a> або <a %(a_download)s>загружаць</a> нашы базы дадзеных ElasticSearch і MariaDB. Сырыя дадзеныя можна ўручную даследаваць <a %(a_explore)s>праз файлы JSON</a>. Наш спіс сырых торэнтаў таксама можна загрузіць у фармаце <a %(a_torrents)s>JSON</a>. Ці ёсць у вас API? Мы не размяшчаем тут ніякіх матэрыялаў, абароненых аўтарскім правам. Мы з'яўляемся пошукавай сістэмай і, такім чынам, індэксуем толькі метаданыя, якія ўжо даступныя публічна. Пры загрузцы з гэтых знешніх крыніц мы раім праверыць законы ў вашай юрысдыкцыі адносна таго, што дазволена. Мы не нясем адказнасці за змест, размешчаны іншымі. Калі ў вас ёсць скаргі на тое, што вы бачыце тут, лепш за ўсё звязацца з арыгінальным сайтам. Мы рэгулярна ўключаем іх змены ў нашу базу дадзеных. Калі вы сапраўды лічыце, што ў вас ёсць сапраўдная скарга DMCA, на якую мы павінны адказаць, калі ласка, запоўніце <a %(a_copyright)s>форму скаргі DMCA / аўтарскага права</a>. Мы сур'ёзна ставімся да вашых скаргаў і адкажам вам як мага хутчэй. Як паведаміць пра парушэнне аўтарскіх правоў? Вось некаторыя кнігі, якія маюць асаблівае значэнне для свету ценявых бібліятэк і лічбавага захавання: Якія вашы любімыя кнігі? Мы таксама хочам нагадаць усім, што ўвесь наш код і дадзеныя цалкам з'яўляюцца адкрытым зыходным кодам. Гэта ўнікальна для такіх праектаў, як наш, — мы не ведаем ніводнага іншага праекта з такім жа вялікім каталогам, які таксама цалкам з'яўляецца адкрытым зыходным кодам. Мы вельмі вітаем усіх, хто лічыць, што мы дрэнна кіруем нашым праектам, узяць наш код і дадзеныя і стварыць сваю ўласную цень-бібліятэку! Мы не кажам гэта з крыўдай ці чымсьці падобным — мы сапраўды лічым, што гэта было б выдатна, бо гэта падняло б планку для ўсіх і лепш захавала спадчыну чалавецтва. Я ненавіджу, як вы кіруеце гэтым праектам! Мы былі б рады, калі б людзі наладзілі <a %(a_mirrors)s>люстэркі</a>, і мы фінансава падтрымаем гэта. Як я магу дапамагчы? Так, сапраўды. Нашым натхненнем для збору метададзеных з'яўляецца мэта Аарона Шварца "адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі", для якой ён стварыў <a %(a_openlib)s>Open Library</a>. Гэты праект добра прасунуўся, але наша унікальная пазіцыя дазваляе нам атрымліваць метададзеныя, якія яны не могуць. Яшчэ адным натхненнем было наша жаданне ведаць <a %(a_blog)s>колькі кніг існуе ў свеце</a>, каб мы маглі падлічыць, колькі кніг нам яшчэ трэба захаваць. Ці збіраеце вы метаданыя? Звярніце ўвагу, што mhut.org блакуе пэўныя дыяпазоны IP, таму можа спатрэбіцца VPN. <strong>Android:</strong> Націсніце на меню з трыма кропкамі ў правым верхнім куце і выберыце «Дадаць на галоўны экран». <strong>iOS:</strong> Націсніце кнопку “Share” унізе і выберыце “Add to Home Screen”. У нас няма афіцыйнага мабільнага дадатку, але вы можаце ўсталяваць гэты сайт як дадатак. Ці ёсць у вас мабільнае прыкладанне? Калі ласка, адпраўце іх у <a %(a_archive)s>Internet Archive</a>. Яны належным чынам захаваюць іх. Як я магу ахвяраваць кнігі ці іншыя фізічныя матэрыялы? Як запытаць кнігі? <a %(a_blog)s>Блог Анны</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — рэгулярныя абнаўленні <a %(a_software)s>Праграмнае забеспячэнне Анны</a> — наш адкрыты зыходны код <a %(a_datasets)s>Наборы дадзеных</a> — пра дадзеныя <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтэрнатыўныя дамены Ці ёсць больш рэсурсаў пра Архіў Анны? <a %(a_translate)s>Пераклад на праграмным забеспячэнні Анны</a> — наша сістэма перакладу <a %(a_wikipedia)s>Вікіпедыя</a> — больш пра нас (калі ласка, дапамажыце падтрымліваць гэтую старонку ў актуальным стане або стварыце адну для вашай мовы!) Выберыце налады, якія вам падабаюцца, пакіньце поле пошуку пустым, націсніце “Пошук”, а затым дадайце старонку ў закладкі з дапамогай функцыі закладак вашага браўзера. Як захаваць мае налады пошуку? Мы вітаем даследчыкаў бяспекі для пошуку ўразлівасцяў у нашых сістэмах. Мы вялікія прыхільнікі адказнага раскрыцця інфармацыі. Звяжыцеся з намі <a %(a_contact)s>тут</a>. У цяперашні час мы не можам узнагароджваць за выяўленне памылак, за выключэннем уразлівасцяў, якія маюць <a %(a_link)s >патэнцыял скампраметаваць нашу ананімнасць</a>, за якія мы прапануем ўзнагароды ў дыяпазоне $10k-50k. Мы хацелі б у будучыні пашырыць сферу ўзнагароджання за выяўленне памылак! Звярніце ўвагу, што атакі сацыяльнай інжынерыі не ўваходзяць у сферу. Калі вас цікавіць наступальная бяспека і вы хочаце дапамагчы архіваваць веды і культуру свету, абавязкова звяжыцеся з намі. Ёсць шмат спосабаў, як вы можаце дапамагчы. Ці ёсць у вас праграма адказнага раскрыцця інфармацыі? Мы літаральна не маем дастаткова рэсурсаў, каб даць усім у свеце хуткасныя загрузкі, як бы нам гэтага не хацелася. Калі багаты дабрадзей захоча дапамагчы і забяспечыць гэта для нас, гэта было б неверагодна, але да таго часу мы стараемся з усіх сіл. Мы некамерцыйны праект, які ледзь можа існаваць за кошт ахвяраванняў. Вось чаму мы рэалізавалі дзве сістэмы для бясплатных загрузак з нашымі партнёрамі: агульныя серверы з павольнымі загрузкамі і крыху хутчэйшыя серверы з чаргой (каб зменшыць колькасць людзей, якія загружаюць адначасова). У нас таксама ёсць <a %(a_verification)s>верыфікацыя браўзера</a> для нашых павольных загрузак, таму што ў адваротным выпадку боты і скраперы будуць злоўжываць імі, робячы рэчы яшчэ больш павольнымі для законных карыстальнікаў. Звярніце ўвагу, што пры выкарыстанні Tor Browser вам можа спатрэбіцца наладзіць параметры бяспекі. На самым нізкім з варыянтаў, які называецца «Стандартны», выклік Cloudflare turnstile праходзіць паспяхова. На больш высокіх варыянтах, якія называюцца «Бяспечней» і «Самы бяспечны», выклік не праходзіць. Для вялікіх файлаў часам павольныя загрузкі могуць перарывацца ў сярэдзіне. Мы рэкамендуем выкарыстоўваць менеджар загрузак (напрыклад, JDownloader), каб аўтаматычна аднаўляць вялікія загрузкі. Чаму хуткасць загрузкі такая нізкая? Часта задаваныя пытанні (FAQ) Выкарыстоўвайце <a %(a_list)s>генератар спісу торэнтаў</a>, каб стварыць спіс торэнтаў, якія найбольш маюць патрэбу ў торэнтынгу, у межах вашых абмежаванняў па захоўванні. Так, глядзіце старонку <a %(a_llm)s>LLM data</a>. Большасць торэнтаў утрымліваюць файлы непасрэдна, што азначае, што вы можаце ўказаць кліентам торэнтаў загружаць толькі патрэбныя файлы. Каб вызначыць, якія файлы загружаць, вы можаце <a %(a_generate)s>стварыць</a> нашы метаданыя або <a %(a_download)s>загрузіць</a> нашы базы дадзеных ElasticSearch і MariaDB. На жаль, некаторыя калекцыі торэнтаў утрымліваюць файлы .zip або .tar у кораню, у такім выпадку вам трэба загрузіць увесь торэнт, перш чым вы зможаце выбраць асобныя файлы. Пакуль што няма простых у выкарыстанні інструментаў для фільтрацыі торэнтаў, але мы вітаем уклады. (У нас ёсць <a %(a_ideas)s>некаторыя ідэі</a> для апошняга выпадку.) Доўгі адказ: Кароткі адказ: не так проста. Мы стараемся мінімізаваць дубляванне або перакрыцце паміж торэнтамі ў гэтым спісе, але гэта не заўсёды магчыма і моцна залежыць ад палітык крынічных бібліятэк. Для бібліятэк, якія выпускаюць свае ўласныя торэнты, гэта не ў нашых руках. Для торэнтаў, выпушчаных Anna’s Archive, мы выдаляем дублікаты толькі на аснове MD5-хэша, што азначае, што розныя версіі адной і той жа кнігі не выдаляюцца. Так. Гэта на самай справе PDF і EPUB, яны проста не маюць пашырэння ў многіх нашых торэнтах. Ёсць два месцы, дзе вы можаце знайсці метададзеныя для торэнт-файлаў, уключаючы тыпы/пашырэнні файлаў: 1. Кожная калекцыя або выпуск мае свае метаданыя. Напрыклад, <a %(a_libgen_nonfic)s>торэнты Libgen.rs</a> маюць адпаведную базу метаданых, размешчаную на сайце Libgen.rs. Мы звычайна спасылаемся на адпаведныя рэсурсы метаданых з кожнай старонкі <a %(a_datasets)s>набору дадзеных</a> калекцыі. 2. Мы рэкамендуем <a %(a_generate)s>стварыць</a> або <a %(a_download)s>загрузіць</a> нашы базы дадзеных ElasticSearch і MariaDB. Яны ўтрымліваюць адпаведнасць кожнага запісу ў Anna’s Archive з яго адпаведнымі торэнт-файламі (калі даступна), пад "torrent_paths" у JSON ElasticSearch. Некаторыя торэнт-кліенты не падтрымліваюць вялікія памеры частак, якія маюць шмат нашых торэнтаў (для новых мы гэтага больш не робім — хоць гэта і адпавядае спецыфікацыям!). Таму паспрабуйце іншы кліент, калі сутыкнецеся з гэтай праблемай, або скардзіцеся вытворцам вашага торэнт-кліента. Я хацеў бы дапамагчы раздаваць, але ў мяне мала месца на дыску. Торэнты занадта павольныя; ці магу я загрузіць дадзеныя непасрэдна ад вас? Ці магу я загрузіць толькі частку файлаў, напрыклад, толькі пэўную мову або тэму? Як вы апрацоўваеце дублікаты ў торэнтах? Ці магу я атрымаць спіс торэнтаў у фармаце JSON? Я не бачу PDF або EPUB у торэнтах, толькі бінарныя файлы? Што рабіць? Чаму мой торэнт-кліент не можа адкрыць некаторыя з вашых торэнт-файлаў / магнітных спасылак? Torrents FAQ Як мне загрузіць новыя кнігі? Калі ласка, паглядзіце <a %(a_href)s>гэты выдатны праект</a>. Ці ёсць у вас манітор часу працы? Што такое Архіў Анны? Падпішыцеся, каб атрымаць доступ да хуткай загрузкі. Цяпер мы падтрымліваем падарункавыя карты Amazon, крэдытныя і дэбетавыя карты, крыптавалюту, Alipay і WeChat. Сёння вы вычарпалі колькасць хуткіх загрузак. Доступ Гадзінныя загрузкі за апошнія 30 дзён. Сярэдняе за гадзіну: %(hourly)s. Сярэдняе за дзень: %(daily)s. Мы працуем з партнёрамі, каб зрабіць нашы калекцыі лёгка і бясплатна даступнымі для ўсіх. Мы верым, што кожны мае права на калектыўную мудрасць чалавецтва. І <a %(a_search)s>не за кошт аўтараў</a>. Наборы дадзеных, якія выкарыстоўваюцца ў архіве Анны, цалкам адкрытыя і могуць быць люстраваны ў вялікіх аб'ёмах з дапамогай торэнтаў. <a %(a_datasets)s>Даведайцеся больш…</a> Доўгатэрміновы архіў Поўная база дадзеных Пошук Кнігі, артыкулы, часопісы, коміксы, метададзеныя, … Увесь наш <a %(a_code)s>код</a> і <a %(a_datasets)s>дадзеныя</a> цалкам з адкрытым зыходным кодам. <span %(span_anna)s>Архіў Анны</span> з'яўляецца некамерцыйным праектам з двума мэтамі: <li><strong>Захаванне:</strong> Рэзервовае капіраванне ўсіх ведаў і культуры чалавецтва.</li><li><strong>Доступ:</strong> Забеспячэнне доступу да гэтых ведаў і культуры для ўсіх у свеце.</li> У нас самая вялікая ў свеце калекцыя высакаякасных тэкставых дадзеных. <a %(a_llm)s>Даведацца больш…</a> LLM training data 🪩 Люстэркі: заклік да валанцёраў Калі вы кіруеце ананімным плацежным працэсарам высокай рызыкі, калі ласка, звяжыцеся з намі. Мы таксама шукаем людзей, якія жадаюць размясціць стрыманыя невялікія аб'явы. Усе сродкі ідуць на нашы намаганні па захаванні. Захаванне Мы ацэньваем, што захавалі каля <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% сусветных кніг</a>. Мы захоўваем кнігі, артыкулы, коміксы, часопісы і многае іншае, збіраючы гэтыя матэрыялы з розных <a href="https://en.wikipedia.org/wiki/Shadow_library">ценявых бібліятэк</a>, афіцыйных бібліятэк і іншых калекцый у адным месцы. Усе гэтыя дадзеныя захоўваюцца назаўсёды, бо іх лёгка дубляваць у вялікіх аб'ёмах — з дапамогай торэнтаў — што прыводзіць да шматлікіх копій па ўсім свеце. Некаторыя ценявыя бібліятэкі ўжо робяць гэта самі (напрыклад, Sci-Hub, Library Genesis), у той час як Архіў Ганны «вызваляе» іншыя бібліятэкі, якія не прапануюць масавае распаўсюджванне (напрыклад, Z-Library) або зусім не з'яўляюцца ценявымі бібліятэкамі (напрыклад, Internet Archive, DuXiu). Гэта шырокае распаўсюджванне ў спалучэнні з адкрытым зыходным кодам робіць наш сайт устойлівым да выдаленняў і забяспечвае доўгатэрміновае захаванне ведаў і культуры чалавецтва. Даведайцеся больш пра <a href="/datasets">нашы наборы дадзеных</a>. Калі вы <a %(a_member)s>удзельнік</a>, праверка браўзэра не патрабуецца. 🧬&nbsp;SciDB з'яўляецца працягам Sci-Hub. SciDB Адкрыць DOI Sci-Hub <a %(a_paused)s>прыпыніў</a> загрузку новых артыкулаў. Прамы доступ да %(count)s навуковых артыкулаў 🧬&nbsp;SciDB з'яўляецца працягам Sci-Hub, з яго знаёмым інтэрфейсам і прамым праглядам PDF. Увядзіце ваш DOI для прагляду. У нас ёсць поўная калекцыя Sci-Hub, а таксама новыя артыкулы. Большасць можна праглядаць непасрэдна з знаёмым інтэрфейсам, падобным да Sci-Hub. Некаторыя можна спампаваць праз знешнія крыніцы, у такім выпадку мы паказваем спасылкі на іх. Вы можаце вельмі дапамагчы, калі зможаце раздаваць торэнты. <a %(a_torrents)s>Даведацца больш…</a> >%(count)s сідэры <%(count)s сідэраў %(count_min)s–%(count_max)s сідэраў 🤝 Шукаем валанцёраў Як некамерцыйны праект з адкрытым зыходным кодам, мы заўсёды шукаем людзей, якія могуць дапамагчы. IPFS загрузкі Спіс па %(by)s, створаны <span %(span_time)s>%(time)s</span> Захаваць ❌ Нешта пайшло не так. Калі ласка, паспрабуйце яшчэ раз. ✅ Захавана. Калі ласка, перазагрузіце старонку. Спіс пусты. рэдагаваць Дадайце або выдаліце з гэтага спісу, знайшоўшы файл і адкрыўшы ўкладку “Спісы”. Спіс Як мы можам дапамагчы Выдаленне паўтораў (дэдуплікацыя) Экстракцыя тэксту і метададзеных OCR Мы можам забяспечыць высокахуткасны доступ да нашых поўных калекцый, а таксама да неапублікаваных калекцый. Гэта доступ на ўзроўні прадпрыемства, які мы можам забяспечыць за ахвяраванні ў памеры дзесяткаў тысяч долараў ЗША. Мы таксама гатовыя абмяняць гэта на якасныя калекцыі, якіх у нас яшчэ няма. Мы можам вярнуць вам грошы, калі вы зможаце забяспечыць нас узбагачэннем нашых дадзеных, такім як: Падтрымлівайце доўгатэрміновае архіваванне чалавечых ведаў, атрымліваючы лепшыя дадзеныя для вашай мадэлі! <a %(a_contact)s>Звяжыцеся з намі</a>, каб абмеркаваць, як мы можам супрацоўнічаць. Добра вядома, што LLM-ы квітнеюць на якасных дадзеных. У нас самая вялікая калекцыя кніг, артыкулаў, часопісаў і г.д. у свеце, якія з'яўляюцца аднымі з самых якасных тэкставых крыніц. Дадзеныя LLM Унікальны маштаб і разнастайнасць Наша калекцыя ўключае больш за сто мільёнаў файлаў, уключаючы акадэмічныя часопісы, падручнікі і часопісы. Мы дасягаем гэтага маштабу, аб'ядноўваючы вялікія існуючыя рэпазітарыі. Некаторыя з нашых крынічных калекцый ужо даступныя ў вялікіх аб'ёмах (Sci-Hub і часткі Libgen). Іншыя крыніцы мы вызвалілі самі. <a %(a_datasets)s>Datasets</a> паказвае поўны агляд. Наша калекцыя ўключае мільёны кніг, артыкулаў і часопісаў, якія былі створаны да эпохі электронных кніг. Вялікія часткі гэтай калекцыі ўжо прайшлі OCR і маюць мала ўнутраных паўтораў. Працягнуць Калі вы страцілі ключ, калі ласка, <a %(a_contact)s>звяжыцеся з намі</a> і падайце як мага больш інфармацыі. Магчыма, вам давядзецца часова стварыць новы ўліковы запіс, каб звязацца з намі. Калі ласка, <a %(a_account)s>увайдзіце</a>, каб праглядзець гэтую старонку.</a> Каб прадухіліць стварэнне шматлікіх уліковых запісаў спам-ботамі, нам трэба спачатку праверыць ваш браўзер. Калі вы трапілі ў бясконцую завеску, мы рэкамендуем усталяваць <a %(a_privacypass)s>Privacy Pass</a>. Можа таксама дапамагчы адключэнне рэкламных блакіроўшчыкаў і іншых пашырэнняў браўзэра. Увайсці / Зарэгістравацца Архіў Анны часова недаступны з-за тэхнічнага абслугоўвання. Калі ласка, вярніцеся праз гадзіну. Альтэрнатыўны аўтар Альтэрнатыўнае апісанне Альтэрнатыўнае выданне Альтэрнатыўнае пашырэнне Альтэрнатыўная назва файла Альтэрнатыўны выдаўца Альтэрнатыўны загаловак дата адкрыцця кода Чытаць далей… апісанне Шукайце ў архіве Анны нумар CADAL SSNO Шукайце ў Архіве Анны нумар SSID DuXiu Шукайце ў архіве Анны па нумары DuXiu DXID Шукайце ў архіве Анны па ISBN Шукайце ў Анныным Архіве па нумары OCLC (WorldCat) Шукаць у архіве Анны па ідэнтыфікатару Open Library Анін Архіў онлайн праглядальнік %(count)s закранутыя старонкі Пасля загрузкі: Магчыма, лепшая версія гэтага файла даступная па %(link)s Масавыя загрузкі торэнтаў калекцыя Выкарыстоўвайце анлайн-інструменты для канвертацыі паміж фарматамі. Рэкамендаваныя інструменты канвертацыі: %(links)s Для вялікіх файлаў мы рэкамендуем выкарыстоўваць мэнэджар загрузак, каб пазбегнуць перапынкаў. Рэкамендаваныя мэнэджары загрузак: %(links)s Індэкс eBook EBSCOhost (толькі для экспертаў) (таксама націсніце "GET" уверсе) (націсніце "GET" уверсе) Знешнія спампоўванні <strong>🚀 Хуткія загрузкі</strong> У вас засталося %(remaining)s на сёння. Дзякуй за тое, што вы з намі! ❤️ <strong>🚀 Хуткія спампоўкі</strong> Вы выкарысталі ўсе хуткія спампоўкі на сёння. <strong>🚀 Хуткія загрузкі</strong> Вы нядаўна загружалі гэты файл. Спасылкі застаюцца сапраўднымі некаторы час. <strong> 🚀 Хуткая загрузка </strong> станьце <a %(a_membership)s>ўдзельнікам</a>, каб падтрымаць доўгатэрміновую захаванасць кніг, дакументаў і шмат чаго іншага. У знак нашай падзякі за вашу падтрымку вы атрымліваеце хуткую загрузку. ❤️ 🚀 Хуткія загрузкі 🐢 Павольныя загрузкі Пазычыць з Internet Archive IPFS-партал #%(num)d (магчыма, вам прыйдзецца паспрабаваць некалькі разоў з IPFS) Library Genesis ".li-версія" Library Genesis ".rs-версія" - мастацкая літаратура Library Genesis ".rs-версія" - акадэмічны раздзел іх рэклама вядомая тым, што ўтрымлівае шкоднаснае праграмнае забеспячэнне, таму выкарыстоўвайце блакіроўшчык рэкламы або не націскайце на рэкламу Amazon‘s “Send to Kindle” djazz‘s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Файлы Nexus/STC могуць быць ненадзейнымі для загрузкі) Загрузак не знойдзена. Усе крыніцы ўтрымліваюць той жа файл і павінны быць бяспечнымі. Аднак будзьце асцярожныя пры загрузцы файлаў з інтэрнэту. Пераканайцеся што ваша прылада і дадатак абноўленыя да апошняй версіі для бяспекі вашых дадзеных. (без перанакіравання) Адкрыць у нашым праглядальніку (адкрыць у праглядальніку) Варыянт #%(num)d: %(link)s %(extra)s Знайсці арыгінальны запіс у CADAL Шукайце ўручную на DuXiu Знайсці арыгінальны запіс у ISBNdb Знайсці арыгінальны запіс у WorldCat Знайсці арыгінальны запіс у Open Library Шукайце ў розных іншых базах дадзеных па ISBN (толькі для карыстальнікаў з абмежаванымі магчымасцямі друку) PubMed Вам спатрэбіцца чытач электронных кніг або PDF, каб адкрыць файл, у залежнасці ад фармату файла. Рэкамендаваныя чытачы электронных кніг: %(links)s Архіў Анны 🧬 SciDB Sci-Hub: %(doi)s (адпаведны DOI можа быць недаступны ў Sci-Hub) Вы можаце адправіць як PDF, так і EPUB файлы на ваш Kindle або Kobo eReader. Рэкамендаваныя інструменты: %(links)s Больш інфармацыі ў <a %(a_slow)s>FAQ</a>. Падтрымлівайце аўтараў і бібліятэкі Калі вам гэта падабаецца і вы можаце сабе гэта дазволіць, разгледзьце магчымасць набыцця арыгінала або падтрымкі аўтараў наўпрост. Калі гэта даступна ў вашай мясцовай бібліятэцы, разгледзьце магчымасць узяць яе там бясплатна. Спампоўванне з Партнёрскага Сервера часова недаступнае для гэтага файла. торэнт Ад надзейных партнёраў. Z-Library Z-Library на Tor (патрабуецца ўваход праз браўзэр ТОР) паказаць знешнія загрузкі <span class="font-bold">❌ у гэтага файла могуць быць праблемы і ён быў скрыты з бібліятэкі зыходнага кода.</span> Часам гэта адбываецца па запыце ўладальніка аўтарскіх правоў, часам таму што даступная лепшая альтэрнатыва, але часам гэта адбываецца з-за праблемы з самім файлам. Мы рэкамендуем пашукаць альтэрнатыўны файл, але вы можаце спампаваць гэты пры жаданні. Больш інфармацыі: Калі вы ўсё яшчэ хочаце спампаваць гэты файл, абавязкова выкарыстоўвайце для яго адкрыцця толькі праверанае, абноўленае праграмнае забеспячэнне. каментары да метададзеных AA: Шукайце ў архіве Анны “%(name)s” Даследчык кодаў: Прагляд у Codes Explorer “%(name)s” URL: Вэб-сайт: Калі ў вас ёсць гэты файл і ён яшчэ не даступны ў Anna’s Archive, разгледзьце магчымасць <a %(a_request)s>загрузкі яго</a>. Файл Internet Archive Controlled Digital Lending «%(id)s» Гэта запіс файла з Internet Archive, а не файл для прамога спампоўвання. Вы можаце паспрабаваць узяць кнігу ў арэнду (спасылка ніжэй) або выкарыстоўваць гэты URL пры <a %(a_request)s>запыце файла</a>. Паляпшэнне метададзеных CADAL SSNO %(id)s метададзеныя запісу Гэта метададзеныя, а не файл для запампоўкі. Вы можаце выкарыстоўваць гэты URL, калі <a %(a_request)s>запытваеце файл</a>. Запіс метададзеных DuXiu SSID %(id)s Запіс метададзеных ISBNdb %(id)s Запіс метададзеных MagzDB ID %(id)s Запіс метададзеных Nexus/STC ID %(id)s Нумар OCLC (WorldCat) %(id)s метададзеныя запісу Запіс метададзеных Open Library %(id)s Файл Sci-Hub «%(id)s» Не знойдзена “%(md5_input)s” не знойдзена у нашай базе дадзеных. Дадаць каментар (%(count)s) Вы можаце атрымаць md5 з URL, напрыклад, MD5 лепшай версіі гэтага файла (калі дастасавальна). Запоўніце гэта, калі ёсць іншы файл, які дакладна адпавядае гэтаму файлу (тае ж выданне, тое ж пашырэнне файла, калі знойдзеце), які людзі павінны выкарыстоўваць замест гэтага файла. Калі вы ведаеце пра лепшую версію гэтага файла па-за архівам Анны, калі ласка, <a %(a_upload)s>загрузіце яе</a>. Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. Вы пакінулі каментар. Гэта можа заняць хвіліну, каб ён з'явіўся. Калі ласка, выкарыстоўвайце <a %(a_copyright)s>форму прэтэнзіі DMCA / аўтарскіх правоў</a>. Апішыце праблему (абавязкова) Калі гэты файл мае выдатную якасць, вы можаце абмеркаваць яго тут! Калі не, калі ласка, выкарыстоўвайце кнопку «Паведаміць пра праблему з файлам». Выдатная якасць файла (%(count)s) Якасць файла Даведайцеся, як <a %(a_metadata)s>паляпшаць метаданыя</a> для гэтага файла самастойна. Апісанне праблемы Калі ласка, <a %(a_login)s>увайдзіце ў сістэму</a>. Мне вельмі спадабалася гэтая кніга! Дапамажыце супольнасці, паведамляючы пра якасць гэтага файла! 🙌 Нешта пайшло не так. Калі ласка, перазагрузіце старонку і паспрабуйце зноў. Паведаміць пра праблему з файлам (%(count)s) Дзякуй за падачу вашага справаздачы. Яна будзе паказана на гэтай старонцы, а таксама прааналізавана ўручную Ганнай (пакуль у нас не будзе належнай сістэмы мадэрацыі). Пакінуць каментар Адправіць справаздачу Што не так з гэтым файлам? Пазычыць (%(count)s) Каментары (%(count)s) Загрузкі (%(count)s) Даследаваць метададзеныя (%(count)s) Спісы (%(count)s) Статыстыка (%(count)s) Для атрымання інфармацыі пра гэты канкрэтны файл, азнаёмцеся з яго <a %(a_href)s>JSON-файлам</a>. Гэта файл, кіраваны бібліятэкай <a %(a_ia)s>Кантраляванага лічбавага пазычання IA</a> і індэксаваны Ганнавай Архівай для пошуку. Для атрымання інфармацыі пра розныя наборы дадзеных, якія мы сабралі, глядзіце <a %(a_datasets)s>старонку Наборы дадзеных</a>. Метаданыя з звязанага запісу Паляпшэнне метаданых у Open Library «MD5 файла» — гэта хэш, які вылічваецца з утрымання файла і з'яўляецца дастаткова унікальным на аснове гэтага ўтрымання. Усе ценявыя бібліятэкі, якія мы індэксавалі тут, у першую чаргу выкарыстоўваюць MD5 для ідэнтыфікацыі файлаў. Файл можа з'яўляцца ў некалькіх ценявых бібліятэках. Для атрымання інфармацыі пра розныя наборы дадзеных, якія мы сабралі, глядзіце <a %(a_datasets)s>старонку Наборы дадзеных</a>. Паведаміць пра якасць файла Агульная колькасць загрузак: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Чэшская метадата %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Трантор %(id)s} Папярэджанне: некалькі звязаных запісаў: Калі вы глядзіце на кнігу ў архіве Анны, вы можаце ўбачыць розныя палі: назва, аўтар, выдавецтва, выданне, год, апісанне, імя файла і іншае. Уся гэтая інфармацыя называецца <em>метададзеныя</em>. Паколькі мы аб'ядноўваем кнігі з розных <em>бібліятэк-крыніц</em>, мы паказваем метададзеныя, якія даступныя ў гэтай бібліятэцы-крыніцы. Напрыклад, для кнігі, якую мы атрымалі з Library Genesis, мы пакажам назву з базы дадзеных Library Genesis. Часам кніга прысутнічае ў <em>некалькіх</em> бібліятэках-крыніцах, якія могуць мець розныя палі метададзеных. У такім выпадку мы проста паказваем самую доўгую версію кожнага поля, бо яна, спадзяемся, утрымлівае найбольш карысную інфармацыю! Мы ўсё роўна пакажам іншыя палі ніжэй апісання, напрыклад, як «альтэрнатыўная назва» (але толькі калі яны адрозніваюцца). Мы таксама здабываем <em>коды</em>, такія як ідэнтыфікатары і класіфікатары, з бібліятэкі-крыніцы. <em>Ідэнтыфікатары</em> унікальна прадстаўляюць пэўнае выданне кнігі; прыклады ўключаюць ISBN, DOI, Open Library ID, Google Books ID або Amazon ID. <em>Класіфікатары</em> аб'ядноўваюць некалькі падобных кніг; прыклады ўключаюць Дэві Дзесятковую (DCC), УДК, ЛКК, РВК або ГОСТ. Часам гэтыя коды відавочна звязаны ў бібліятэках-крыніцах, а часам мы можам здабыць іх з імя файла або апісання (у асноўным ISBN і DOI). Мы можам выкарыстоўваць ідэнтыфікатары для пошуку запісаў у <em>калекцыях толькі метададзеных</em>, такіх як OpenLibrary, ISBNdb або WorldCat/OCLC. У нашай пошукавай сістэме ёсць спецыяльная <em>ўкладка метададзеных</em>, калі вы хочаце праглядаць гэтыя калекцыі. Мы выкарыстоўваем адпаведныя запісы для запаўнення адсутных палёў метададзеных (напрыклад, калі адсутнічае назва) або, напрыклад, як «альтэрнатыўная назва» (калі існуе існуючая назва). Каб убачыць, адкуль менавіта паходзяць метададзеныя кнігі, глядзіце <em>ўкладку «Тэхнічныя дэталі»</em> на старонцы кнігі. Там ёсць спасылка на сыры JSON для гэтай кнігі з указальнікамі на сыры JSON арыгінальных запісаў. Для атрымання дадатковай інфармацыі глядзіце наступныя старонкі: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Пошук (ўкладка метададзеных)</a>, <a %(a_codes)s>Даследчык кодаў</a> і <a %(a_example)s>Прыклад метададзеных JSON</a>. Нарэшце, усе нашы метададзеныя могуць быць <a %(a_generated)s>згенераваны</a> або <a %(a_downloaded)s>загружаны</a> як базы дадзеных ElasticSearch і MariaDB. Фон Вы можаце дапамагчы ў захаванні кніг, паляпшаючы метададзеныя! Спачатку прачытайце інфармацыю пра метададзеныя на архіве Анны, а затым даведайцеся, як палепшыць метададзеныя праз спасылкі з Open Library, і атрымайце бясплатнае сяброўства ў архіве Анны. Паляпшэнне метададзеных Дык што рабіць, калі вы сутыкнуліся з файлам з дрэннымі метададзенымі? Вы можаце пайсці ў бібліятэку-крыніцу і прытрымлівацца яе працэдур для выпраўлення метададзеных, але што рабіць, калі файл прысутнічае ў некалькіх бібліятэках-крыніцах? Існуе адзін ідэнтыфікатар, які мае асаблівае значэнне ў архіве Анны. <strong>Поле annas_archive md5 у Open Library заўсёды пераўзыходзіць усе іншыя метададзеныя!</strong> Давайце спачатку вернемся назад і даведаемся пра Open Library. Open Library была заснавана ў 2006 годзе Ааронам Шварцам з мэтай «адна вэб-старонка для кожнай калі-небудзь апублікаванай кнігі». Гэта свайго роду Вікіпедыя для метададзеных кніг: кожны можа яе рэдагаваць, яна свабодна ліцэнзаваная і можа быць загружана ў масавым парадку. Гэта база дадзеных кніг, якая найбольш адпавядае нашай місіі — на самай справе, архіў Анны быў натхнёны бачаннямі і жыццём Аарона Шварца. Замест таго, каб вынаходзіць ровар нанова, мы вырашылі накіраваць нашых валанцёраў у Open Library. Калі вы бачыце кнігу з няправільнымі метададзенымі, вы можаце дапамагчы наступным чынам: Звярніце ўвагу, што гэта працуе толькі для кніг, а не для навуковых артыкулаў або іншых тыпаў файлаў. Для іншых тыпаў файлаў мы ўсё яшчэ рэкамендуем знайсці крынічную бібліятэку. Можа спатрэбіцца некалькі тыдняў, каб змены былі ўключаны ў Архіў Анны, бо нам трэба загрузіць апошні дамп дадзеных Open Library і перагенераваць наш індэкс пошуку.  Перайдзіце на <a %(a_openlib)s>вэб-сайт Open Library</a>. Знайдзіце правільны запіс кнігі. <strong>УВАГА:</strong> абавязкова выбірайце правільнае <strong>выданне</strong>. У Open Library ёсць «творы» і «выданні». «Твор» можа быць «Гары Потэр і філасофскі камень». «Выданне» можа быць: Першае выданне 1997 года, апублікаванае Bloomsbery, з 256 старонкамі. Выданне ў мяккай вокладцы 2003 года, апублікаванае Raincoast Books, з 223 старонкамі. Польскі пераклад 2000 года «Harry Potter I Kamie Filozoficzn» ад Media Rodzina з 328 старонкамі. Усе гэтыя выданні маюць розныя ISBN і розны змест, таму абавязкова выбірайце правільнае! Рэдагуйце запіс (або стварыце яго, калі ён не існуе), і дадайце як мага больш карыснай інфармацыі! Вы ўжо тут, таму зрабіце запіс сапраўды выдатным. У раздзеле «ID Numbers» выберыце «Архіў Анны» і дадайце MD5 кнігі з Архіва Анны. Гэта доўгі радок літар і лічбаў пасля «/md5/» у URL. Паспрабуйце знайсці іншыя файлы ў Архіве Анны, якія таксама адпавядаюць гэтаму запісу, і дадайце іх таксама. У будучыні мы зможам групаваць іх як дублікаты на старонцы пошуку Архіва Анны. Калі скончыце, запішыце URL, які вы толькі што абнавілі. Пасля таго, як вы абновіце прынамсі 30 запісаў з MD5 Архіва Анны, дашліце нам <a %(a_contact)s>ліст</a> і спіс. Мы дамо вам бясплатнае сяброўства ў Архіве Анны, каб вы маглі лягчэй выконваць гэтую працу (і як падзяку за вашу дапамогу). Гэтыя рэдагаванні павінны быць высокай якасці і дадаваць значную колькасць інфармацыі, інакш ваша запыт будзе адхілены. Ваш запыт таксама будзе адхілены, калі любое з рэдагаванняў будзе адменена або выпраўлена мадэратарамі Open Library. Спасылка з Open Library Калі вы значна ўцягнецеся ў распрацоўку і аперацыі нашай працы, мы можам абмеркаваць падзел большай часткі даходаў ад ахвяраванняў з вамі, каб вы маглі выкарыстоўваць іх па неабходнасці. Мы будзем аплачваць хостынг толькі пасля таго, як усё будзе наладжана і вы пакажаце, што здольныя падтрымліваць архіў у актуальным стане з абнаўленнямі. Гэта азначае, што вам давядзецца аплаціць першыя 1-2 месяцы самастойна. Ваш час не будзе кампенсаваны (і наш таксама), бо гэта чыста валанцёрская праца. Мы гатовыя пакрыць выдаткі на хостынг і VPN, першапачаткова да $200 у месяц. Гэта дастаткова для базавага пошукавага сервера і праксі з абаронай DMCA. Выдаткі на хостынг Калі ласка, <strong>не звяртайцеся да нас</strong> за дазволам або з базавымі пытаннямі. Дзеянні гавораць гучней за словы! Уся інфармацыя ёсць у адкрытым доступе, таму проста пачніце наладжваць сваё люстэрка. Калі вы сутыкнецеся з праблемамі, не саромейцеся размяшчаць білеты або запыты на зліццё ў нашым Gitlab. Магчыма, нам спатрэбіцца стварыць некаторыя функцыі, спецыфічныя для люстэркаў, разам з вамі, напрыклад, рэбрэндынг з “Архіва Анны” на назву вашага сайта, (першапачаткова) адключэнне ўліковых запісаў карыстальнікаў або спасылка на наш асноўны сайт са старонак кніг. Калі ў вас запрацуе люстэрка, калі ласка, звяжыцеся з намі. Мы хацелі б праверыць вашу бяспеку, і калі ўсё будзе ў парадку, мы спасылкуемся на ваша люстэрка і пачнем цесна супрацоўнічаць з вамі. Дзякуй загадзя ўсім, хто гатовы ўнесці свой уклад такім чынам! Гэта не для слабых сэрцам, але гэта ўмацуе даўгавечнасць найбуйнейшай сапраўды адкрытай бібліятэкі ў гісторыі чалавецтва. Пачатак працы Для павышэння ўстойлівасці архіва Анны мы шукаем валанцёраў для запуску люстэркаў. Ваша версія выразна адрозніваецца як люстраная, напрыклад, “Архіў Боба, люстраны архіў Анны”. Вы гатовыя прыняць рызыкі, звязаныя з гэтай працай, якія значныя. Вы глыбока разумееце неабходную аперацыйную бяспеку. Змест <a %(a_shadow)s>гэтых</a> <a %(a_pirate)s>паведамленняў</a> для вас відавочны. Першапачаткова мы не дамо вам доступу да загрузак з сервера партнёра, але калі ўсё пойдзе добра, мы можам падзяліцца гэтым з вамі. Вы кіруеце адкрытым зыходным кодам архіва Анны і рэгулярна абнаўляеце як код, так і дадзеныя. Вы гатовыя ўнесці свой уклад у наш <a %(a_codebase)s>зыходны код</a> — у супрацоўніцтве з нашай камандай — каб гэта адбылося. Мы шукаем гэта: Люстэркі: заклік да валанцёраў Зрабіце яшчэ адно ахвяраванне. Ахвяраванняў пакуль няма. <a %(a_donate)s>Зрабіць маё першае ахвяраванне.</a> Дэталі ахвяраванняў не паказваюцца публічна. Мае ахвяраванні 📡 Для масавага люстравання нашай калекцыі, азнаёмцеся з <a %(a_datasets)s>Datasets</a> і <a %(a_torrents)s>Torrents</a> старонкамі. Запампоўкі з вашага IP-адраса за апошнія 24 гадзіны: %(count)s. 🚀 Каб павялічыць хуткасць загрузак, <a %(a_membership)s>падпішыцеся на сайт</a>. Спампаваць з партнёрскага сайта Не саромейцеся працягваць праглядаць архіў Анны ў іншай укладцы, пакуль чакаеце (калі ваш браўзер падтрымлівае абнаўленне фонавых укладак). Не саромейцеся чакаць, пакуль загружаюцца некалькі старонак загрузкі адначасова (але, калі ласка, загружайце толькі адзін файл адначасова з аднаго сервера). Пасля атрымання спасылкі для загрузкі яна будзе дзейнічаць на працягу некалькіх гадзін. Дзякуй за чаканне, гэта дазваляе зрабіць сайт даступным бясплатна для ўсіх! 😊 🔗 Усе спасылкі для спампоўвання гэтага файла: <a %(a_main)s>Галоўная старонка файла</a>. ❌ Павольныя загрузкі недаступныя праз Cloudflare VPN або з IP-адрасоў Cloudflare. ❌ Павольныя загрузкі даступныя толькі праз афіцыйны сайт. Наведайце %(websites)s. 📚 Выкарыстоўвайце наступны URL для загрузкі: <a %(a_download)s>Загрузіць зараз</a>. Каб даць усім магчымасць бясплатна загружаць файлы, вам трэба пачакаць, перш чым вы зможаце загрузіць гэты файл. Калі ласка, пачакайце <span %(span_countdown)s>%(wait_seconds)s</span> секунд, каб загрузіць гэты файл. Папярэджанне: за апошнія 24 гадзіны з вашага IP-адраса было шмат загрузак. Загрузкі могуць быць павольнейшымі, чым звычайна. Калі вы карыстаецеся VPN, агульным падключэннем да Інтэрнэту або ваш інтэрнэт-правайдэр дзеліць IP-адрасы, гэта папярэджанне можа быць звязана з гэтым. Захаваць ❌ Нешта пайшло не так. Калі ласка, паспрабуйце яшчэ раз. ✅ Захавана. Калі ласка, перазагрузіце старонку. Змяніце сваё імя карыстальніка. Ваш ідэнтыфікатар (частка пасля “#”) нельга змяніць. Профіль створаны <span %(span_time)s>%(time)s</span> рэдагаваць Спісы Стварыце новы спіс, знайшоўшы файл і адкрыўшы ўкладку "спісы". У цяперашні час няма спісаў Профіль не знойдзены. Профіль На дадзены момант мы не можам задаволіць запыты на кнігі. Не дасылайце нам запыты на кнігі па электроннай пошце. Калі ласка, рабіце свае запыты на форумах Z-Library або Libgen. Запіс у архіве Анны DOI: %(doi)s Загрузіць SciDB Nexus/STC Папярэдні прагляд пакуль недаступны. Спампуйце файл з <a %(a_path)s>Архіва Анны</a>. Каб падтрымаць даступнасць і доўгатэрміновае захаванне чалавечых ведаў, станьце <a %(a_donate)s>членам</a>. У якасці бонуса, 🧬&nbsp;SciDB загружаецца хутчэй для ўдзельнікаў, без абмежаванняў. Не працуе? Паспрабуйце <a %(a_refresh)s>абнавіць</a>. Sci-Hub Дадаць канкрэтнае поле пошуку Пошук апісанняў і каментарыяў да метададзеных Год выдання Пашыраны Доступ Змест Адлюстраваць Спіс Табліца Тып файла Мова Сартаваць па Найбуйнейшы Найбольш дарэчны Найноўшы (памер файла) (з адкрытым зыходным кодам) (год публікацыі) Найстарэйшы Выпадкова Найменшы Крыніца скрэбавана і адкрыта AA Лічбавае пазычанне (%(count)s) Артыкулы з часопісаў (%(count)s) Мы знайшлі супадзенні ў: %(in)s. Вы можаце спасылацца на URL, знойдзены там, калі <a %(a_request)s>запытваеце файл</a>. Метаданыя (%(count)s) Каб даследаваць індэкс пошуку па кодах, выкарыстоўвайце <a %(a_href)s>Даследчык кодаў</a>. Пошукавы індэкс абнаўляецца штомесяц. У цяперашні час ён уключае запісы да %(last_data_refresh_date)s. Для атрымання дадатковай тэхнічнай інфармацыі глядзіце старонку %(link_open_tag)sнаборы дадзеных</a>. Выключыць Уключыць толькі Неправерана яшчэ… Далей … Папярэдні Гэты індэкс пошуку ў цяперашні час уключае метададзеныя з бібліятэкі кантраляванага лічбавага пазычання Internet Archive. <a %(a_datasets)s>Больш пра нашы Datasets</a>. Для большай колькасці лічбавых бібліятэк глядзіце <a %(a_wikipedia)s>Вікіпедыя</a> і <a %(a_mobileread)s>MobileRead Wiki</a>. Для заяў DMCA / аўтарскіх правоў <a %(a_copyright)s>націсніце тут</a>. Час загрузкі Памылка падчас пошуку. Паспрабуйце <a %(a_reload)s>перазагрузіць старонку</a>. Калі праблема застаецца, калі ласка, напішыце нам на %(email)s. Хуткае спампоўванне На самай справе, кожны можа дапамагчы захаваць гэтыя файлы, раздаючы наш <a %(a_torrents)s>аб'яднаны спіс торэнтаў</a>. ➡️ Часам гэта адбываецца некарэктна, калі сервер пошуку працуе павольна. У такіх выпадках <a %(a_attrs)s>перазагрузка</a> можа дапамагчы. ❌ Гэты файл можа ўтрымліваць памылкі. Шукаеце навуковыя артыкулы? Гэты індэкс пошуку ў цяперашні час уключае метаданыя з розных крыніц метаданых. <a %(a_datasets)s>Больш пра нашы наборы дадзеных</a>. Існуе шмат крыніц метададзеных для пісьмовых твораў па ўсім свеце. <a %(a_wikipedia)s>Гэтая старонка Вікіпедыі</a> — добры пачатак, але калі вы ведаеце іншыя добрыя спісы, паведаміце нам. Для метададзеных мы паказваем арыгінальныя запісы. Мы не аб'ядноўваем запісы. У нас зараз самы поўны адкрыты каталог кніг, артыкулаў і іншых пісьмовых прац у свеце. Мы люструем Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>і іншыя</a>. <span class="font-bold"> Нічога не знойдзена.</span> Паспрабуйце скараціць запыт або выкарыстоўваць іншыя ключавыя словы або фільтры. Вынікі %(from)s-%(to)s (%(total)s усяго) Калі вы знойдзеце іншыя «ценявыя бібліятэкі», якія мы павінны адлюстраваць, або калі ў вас ёсць якія-небудзь пытанні, калі ласка, звяжыцеся з намі па %(email)s. %(num)d частковыя супадзенні %(num)d+ частковыя супадзенні Увядзіце ў поле для пошуку файлаў у лічбавых бібліятэках. Увядзіце ў поле для пошуку ў нашым каталогу %(count)s файлаў, якія можна спампаваць непасрэдна, якія мы <a %(a_preserve)s>захоўваем назаўсёды</a>. Увядзіце ў поле для пошуку. Увядзіце ў поле для пошуку наш каталог %(count)s акадэмічных артыкулаў і часопісаў, якія мы <a %(a_preserve)s>захоўваем назаўсёды</a>. Увядзіце ў поле для пошуку метаданых з бібліятэк. Гэта можа быць карысна пры <a %(a_request)s>запыце файла</a>. Падказка: выкарыстоўвайце спалучэнні клавіш “/” (фокус пошуку), “enter” (пошук), “j” (уверх), “k” (уніз), “<” (папярэдняя старонка), “>” (наступная старонка) для хутчэйшай навігацыі. Гэта метададзеныя, <span %(classname)s>а не</span> файлы для загрузкі. Налады пошуку Пошук Лічбавае пазычанне Спампаваць Артыкулы з часопісаў Метаданыя Новы пошук %(search_input)s - Пошук Пошук заняў занадта шмат часу, што азначае, што вы можаце ўбачыць недакладныя вынікі. Часам <a %(a_reload)s>перазагрузка</a> старонкі дапамагае. Пошук заняў занадта шмат часу, што звычайна для шырокіх запытаў. Колькасць фільтраў можа быць недакладнай. Для вялікіх загрузак (больш за 10 000 файлаў), якія не прымаюцца Libgen або Z-Library, калі ласка, звяжыцеся з намі па %(a_email)s. Для Libgen.li, пераканайцеся, што спачатку ўвайшлі на <a %(a_forum)s >іх форум</a> з імем карыстальніка %(username)s і паролем %(password)s, а затым вярніцеся на іх <a %(a_upload_page)s >старонку загрузкі</a>. Пакуль што мы прапануем загружаць новыя кнігі ў форкі Library Genesis. Вось <a %(a_guide)s>карысны гід</a>. Звярніце ўвагу, што абодва форкі, якія мы індэксуем на гэтым сайце, выкарыстоўваюць гэтую ж сістэму загрузкі. Для невялікіх загрузак (да 10 000 файлаў) калі ласка, загрузіце іх як на %(first)s, так і на %(second)s. Акрамя таго, вы можаце загрузіць іх у Z-Library <a %(a_upload)s>тут</a>. Каб загрузіць навуковыя артыкулы, калі ласка, таксама (акрамя Library Genesis) загружайце на <a %(a_stc_nexus)s>STC Nexus</a>. Гэта лепшая ценявая бібліятэка для новых артыкулаў. Мы яшчэ не інтэгравалі іх, але зробім гэта ў нейкі момант. Вы можаце выкарыстоўваць іх <a %(a_telegram)s>бот для загрузкі ў Telegram</a> або звязацца з адрасам, указаным у іх замацаваным паведамленні, калі ў вас занадта шмат файлаў для загрузкі такім чынам. <span %(label)s>Інтэнсіўная валанцёрская праца (узнагароды ад 50 да 5000 долараў ЗША):</span> калі вы можаце прысвяціць шмат часу і/або рэсурсаў нашай місіі, мы будзем рады працаваць з вамі больш цесна. У рэшце рэшт, вы можаце далучыцца да ўнутранай каманды. Хоць у нас абмежаваны бюджэт, мы можам узнагародзіць <span %(bold)s>💰 грашовымі ўзнагародамі</span> за самую інтэнсіўную працу. <span %(label)s>Лёгкая валанцёрская праца:</span> калі вы можаце вылучыць толькі некалькі гадзін час ад часу, усё роўна ёсць шмат спосабаў, як вы можаце дапамагчы. Мы ўзнагароджваем пастаянных валанцёраў <span %(bold)s>🤝 сяброўствамі ў Архіве Анны</span>. Архіў Анны абапіраецца на такіх валанцёраў, як вы. Мы вітаем усе ўзроўні ўдзелу і маем дзве асноўныя катэгорыі дапамогі, якую мы шукаем: Калі вы не можаце валанцёрыць свой час, вы ўсё роўна можаце вельмі дапамагчы нам, <a %(a_donate)s>ахвяруючы грошы</a>, <a %(a_torrents)s>раздаючы нашы торэнты</a>, <a %(a_uploading)s>загружаючы кнігі</a> або <a %(a_help)s>распавядаючы сваім сябрам пра Архіў Анны</a>. <span %(bold)s>Кампаніі:</span> мы прапануем высокахуткасны прамы доступ да нашых калекцый у абмен на карпаратыўныя ахвяраванні або ў абмен на новыя калекцыі (напрыклад, новыя сканы, OCR-дадзеныя, узбагачэнне нашых дадзеных). <a %(a_contact)s>Звяжыцеся з намі</a>, калі гэта пра вас. Глядзіце таксама нашу <a %(a_llm)s>старонку LLM</a>. Узнагароды Мы заўсёды шукаем людзей з моцнымі навыкамі праграмавання або наступальнай бяспекі, каб далучыцца да нас. Вы можаце зрабіць сур'ёзны ўклад у захаванне спадчыны чалавецтва. У якасці падзякі мы даем членства за значныя ўнёскі. У якасці вялікай падзякі мы даем грашовыя ўзнагароды за асабліва важныя і складаныя задачы. Гэта не павінна разглядацца як замена працы, але гэта дадатковы стымул і можа дапамагчы з пакрыццём выдаткаў. Большасць нашага кода з'яўляецца адкрытым зыходным кодам, і мы таксама папросім, каб ваш код быў адкрытым пры ўзнагароджанні. Ёсць некаторыя выключэнні, якія мы можам абмеркаваць індывідуальна. Узнагароды прысуджаюцца першаму чалавеку, які выканае задачу. Не саромейцеся каментаваць білет на ўзнагароду, каб паведаміць іншым, што вы працуеце над чымсьці, каб іншыя маглі пачакаць або звязацца з вамі для сумеснай працы. Але будзьце ўважлівыя, што іншыя таксама могуць працаваць над гэтым і паспрабаваць апярэдзіць вас. Аднак мы не прысуджаем узнагароды за неахайную працу. Калі дзве якасныя заяўкі зроблены блізка адна да адной (на працягу дня або двух), мы можам выбраць узнагароджанне абедзвюх па нашым меркаванні, напрыклад, 100%% за першую заяўку і 50%% за другую заяўку (такім чынам, усяго 150%%). Для больш буйных узнагарод (асабліва за скрэйпінг), калі ласка, звяжыцеся з намі, калі вы завяршылі ~5%% ад гэтага, і вы ўпэўнены, што ваш метад будзе маштабавацца да поўнай мэты. Вам трэба будзе падзяліцца сваім метадам з намі, каб мы маглі даць зваротную сувязь. Таксама такім чынам мы можам вырашыць, што рабіць, калі некалькі чалавек набліжаюцца да ўзнагароды, напрыклад, патэнцыйна ўзнагароджваць некалькіх людзей, заахвочваць людзей да сумеснай працы і г.д. ПАПЯРЭДЖАННЕ: задачы з высокімі ўзнагародамі <span %(bold)s>складаныя</span> — магчыма, варта пачаць з больш простых. Перайдзіце ў наш <a %(a_gitlab)s>спіс задач на Gitlab</a> і адсартыруйце па "Прыярытэт пазнакі". Гэта прыблізна паказвае парадак задач, якія нас цікавяць. Задачы без відавочных узнагарод усё яшчэ могуць быць прэтэндэнтамі на членства, асабліва тыя, якія пазначаны як "Прынята" і "Любімае Анны". Магчыма, вам варта пачаць з "Пачатковага праекта". Лёгкае валанцёрства Цяпер у нас таксама ёсць сінхранізаваны канал Matrix на %(matrix)s. Калі ў вас ёсць некалькі гадзін вольнага часу, вы можаце дапамагчы рознымі спосабамі. Абавязкова далучайцеся да <a %(a_telegram)s>чата валанцёраў у Telegram</a>. У знак удзячнасці мы звычайна даем 6 месяцаў “Шчаслівага Бібліятэкара” за базавыя дасягненні і больш за працяглую валанцёрскую працу. Усе дасягненні патрабуюць высокай якасці працы — неахайная праца шкодзіць нам больш, чым дапамагае, і мы яе адхіляем. Калі ласка, <a %(a_contact)s>напішыце нам</a>, калі дасягнеце мэты. %(links)s спасылкі або скрыншоты запытаў, якія вы выканалі. Выконванне запытаў на кнігі (або артыкулы і г.д.) на форумах Z-Library або Library Genesis. У нас няма ўласнай сістэмы запытаў на кнігі, але мы люструем гэтыя бібліятэкі, таму паляпшэнне іх робіць Архіў Анны лепшым таксама. Мэта Задача Залежыць ад задачы. Невялікія задачы, размешчаныя ў нашым <a %(a_telegram)s>чаце валанцёраў у Telegram</a>. Звычайна для членства, часам для невялікіх узнагарод. Невялікія задачы размешчаны ў нашай групе чата для валанцёраў. Абавязкова пакідайце каментар да праблем, якія вы выправілі, каб іншыя не дублявалі вашу працу. %(links)s спасылкі на запісы, якія вы палепшылі. Вы можаце выкарыстоўваць <a %(a_list)s >спіс выпадковых праблем з metadata</a> у якасці адпраўной кропкі. Паляпшэнне метададзеных шляхам <a %(a_metadata)s>звязвання</a> з Open Library. Гэта павінна паказваць, як вы паведамляеце камусьці пра Архіў Анны, і яны дзякуюць вам. %(links)s спасылкі або скрыншоты. Распаўсюджванне інфармацыі пра Архіў Анны. Напрыклад, рэкамендуючы кнігі на AA, спасылаючыся на нашы блогі або накіроўваючы людзей на наш сайт. Поўны пераклад мовы (калі ён не быў амаль завершаны). <a %(a_translate)s>Пераклад</a> сайта. Спасылка на гісторыю рэдагавання, якая паказвае, што вы зрабілі значныя ўнёскі. Паляпшэнне старонкі Вікіпедыі пра Архіў Анны на вашай мове. Уключыце інфармацыю са старонкі Вікіпедыі AA на іншых мовах, а таксама з нашага сайта і блога. Дадайце спасылкі на AA на іншых адпаведных старонках. Валанцёрства і ўзнагароды 