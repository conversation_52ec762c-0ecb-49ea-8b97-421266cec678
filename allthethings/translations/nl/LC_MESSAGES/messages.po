msgid "layout.index.invalid_request"
msgstr "Ongeldig ve<PERSON>. Bezoek %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " en "

msgid "layout.index.header.tagline_and_more"
msgstr "en meer"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;We spiegelen %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "We scrapen %(scraped)s en maken deze open source."

msgid "layout.index.header.tagline_open_source"
msgstr "Al onze code en data zijn volledig open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;De grootste écht open bibliotheek in de geschiedenis van de mensheid."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;boeken, %(paper_count)s&nbsp;papers – voor altijd behouden."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;'s Werelds grootste opensourcebibliotheek met open data. ⭐️&nbsp;Een spiegeling van Sci-Hub, Library Genesis, Z-Library en meer. 📈&nbsp;%(book_any)s boeken, %(journal_article)s papers, %(book_comic)s stripboeken, %(magazine)s tijdschriften – voor altijd behouden."

msgid "layout.index.header.tagline_short"
msgstr "📚 's Werelds grootste opensourcebibliotheek met open data.<br>⭐️ Een spiegeling van Sci-Hub, Libgen, Zlib en meer."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Onjuiste metadata (zoals titel, beschrijving, omslagafbeelding)"

msgid "common.md5_report_type_mapping.download"
msgstr "Downloadproblemen (zoals verbindingsproblemen, foutmeldingen, traagheid)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Bestand kan niet worden geopend (zoals beschadigd bestand, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Slechte kwaliteit (zoals formatteringsproblemen, slechte scans, ontbrekende pagina's)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam/bestand moet worden verwijderd (zoals advertenties, schadelijke inhoud)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Auteursrechtclaim"

msgid "common.md5_report_type_mapping.other"
msgstr "Overig"

msgid "common.membership.tier_name.bonus"
msgstr "Bonusdownloads"

msgid "common.membership.tier_name.2"
msgstr "Briljante boekenwurm"

msgid "common.membership.tier_name.3"
msgstr "Blije bibliothecaris"

msgid "common.membership.tier_name.4"
msgstr "Grandioze gegevensverzamelaar"

msgid "common.membership.tier_name.5"
msgstr "Aanzienlijke archivaris"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s totaal"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totaal"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "Onbetaald"

msgid "common.donation.order_processing_status_labels.1"
msgstr "Betaald"

msgid "common.donation.order_processing_status_labels.2"
msgstr "Geannuleerd"

msgid "common.donation.order_processing_status_labels.3"
msgstr "Verlopen"

msgid "common.donation.order_processing_status_labels.4"
msgstr "Wachten op Anna ter bevestiging"

msgid "common.donation.order_processing_status_labels.5"
msgstr "ongeldig"

msgid "page.donate.title"
msgstr "Doneren"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Er loopt nog een <a %(a_donation)s>bestaande donatie</a>. Rond die donatie af of annuleer hem voordat je een nieuwe donatie doet."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Al mijn donaties weergeven</a>"

msgid "page.donate.header.text1"
msgstr "Anna’s Archief is een non-profit, open-source, open-data project. Door te doneren en lid te worden steun je onze activiteiten en ontwikkeling. Aan al onze leden: bedankt dat jullie ons draaiende houden! ❤️"

msgid "page.donate.header.text2"
msgstr "Voor meer informatie, bekijk de <a %(a_donate)s>Veelgestelde vragen over donaties</a>."

msgid "page.donate.refer.text1"
msgstr "<a %(a_refer)s>Verwijs je vrienden</a> voor nog meer downloads!"

msgid "page.donate.bonus_downloads.main"
msgstr "Je krijgt %(percentage)s%% snelle bonusdownloads, omdat je bent doorverwezen door gebruiker %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Dit geldt voor de gehele lidmaatschapstermijn."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s snelle downloads per dag"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "als je deze maand doneert!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / maand"

msgid "page.donate.buttons.join"
msgstr "Lid worden"

msgid "page.donate.buttons.selected"
msgstr "Geselecteerd"

msgid "page.donate.buttons.up_to_discounts"
msgstr "kortingen tot wel %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "SciDB-papers <strong>(onbeperkt)</strong> zonder verificatie"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> toegang"

msgid "page.donate.perks.refer"
msgstr "Verdien <strong>%(percentage)s%% bonus downloads</strong> door <a %(a_refer)s>vrienden door te verwijzen</a>."

msgid "page.donate.perks.credits"
msgstr "Je gebruikersnaam of anonieme vermelding in de vermeldingen"

msgid "page.donate.perks.previous_plus"
msgstr "Voorgaande voordelen, plus:"

msgid "page.donate.perks.early_access"
msgstr "Vroege toegang tot nieuwe functies"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Exclusieve Telegram met updates van achter de schermen"

msgid "page.donate.perks.adopt"
msgstr "'Een torrent adopteren': je gebruikersnaam of bericht in de bestandsnaam van een torrent <div %(div_months)s>een keer per 12 maanden lidmaatschap</div>"

msgid "page.donate.perks.legendary"
msgstr "Een legendarische status bij het behoud van de kennis en cultuur van de mensheid"

msgid "page.donate.expert.title"
msgstr "Toegang voor experts"

msgid "page.donate.expert.contact_us"
msgstr "Contact opnemen"

msgid "page.donate.small_team"
msgstr "We zijn een klein team dat bestaat uit vrijwilligers. Het kan 1-2 weken duren voordat je antwoord van ons krijgt."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Onbeperkte</strong> snelle toegang"

msgid "page.donate.expert.direct_sftp"
msgstr "Directe <strong>SFTP</strong>-servers"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donatie op bedrijfsniveau of uitwisseling van nieuwe collecties (bijv. nieuwe scans of datasets via OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "We verwelkomen grote donaties van vermogende individuen en instellingen. "

msgid "page.donate.header.large_donations"
msgstr "Neem rechtstreeks contact met ons op via %(email)s voor donaties van $ 5000 en meer."

msgid "page.donate.header.recurring"
msgstr "Houd er rekening mee dat hoewel de lidmaatschappen op deze pagina “per maand” zijn, het eenmalige donaties zijn (niet terugkerend). Zie de <a %(faq)s>Veelgestelde vragen over donaties</a>."

msgid "page.donate.without_membership"
msgstr "Als je wil doneren zonder lidmaatschap (ongeacht het bedrag), dan kun je dit Monero (XMR)-adres gebruiken: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Kies een betaalmethode."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(tijdelijk niet beschikbaar)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s cadeaubon"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkaart (via app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Creditcard/betaalpas"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (VS) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (normaal)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Kaart / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Credit/debit/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazilië)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkaart"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Creditcard/debetkaart (back-up)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Creditcard/betaalpas 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Met crypto kun je doneren met BTC, ETH, XMR en SOL. Gebruik deze optie als je al bekend bent met cryptocurrency."

msgid "page.donate.payment.desc.crypto2"
msgstr "Met crypto kun je BTC, ETH, XMR en meer doneren."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Als je voor de eerste keer crypto gebruikt, raden we aan om %(options)s te gebruiken om Bitcoin (de originele en meest gebruikte cryptocurrency) te kopen en te doneren."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "We gebruiken PayPal Crypto om je te laten doneren met PayPal (VS). Zo blijven we anoniem. We waarderen het dat je de tijd neemt om te leren hoe je op deze manier kunt doneren; dit helpt ons enorm."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Doneren met PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Doneren met Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Als je Cash App hebt, is dat de eenvoudigste manier om te doneren!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Voor transacties onder %(amount)s kan Cash App kosten van %(fee)s in rekening brengen. Bedragen boven %(amount)s zijn gratis!"

msgid "page.donate.payment.desc.revolut"
msgstr "Doneer met Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Als je Revolut hebt, is dit de makkelijkste manier om te doneren!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Doneren met een creditcard of betaalpas."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay en Apple Pay kunnen ook werken."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Bij kleine donaties kunnen creditcardkosten onze korting van %(discount)s%% tenietdoen. Daarom raden we langere abonnementen aan."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Bij kleine donaties zijn de kosten hoog. Daarom raden we langere abonnementen aan."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Met Binance koop je Bitcoin met een creditcard/debetkaart of bankrekening, en doneer je vervolgens die Bitcoin aan ons. Op die manier kunnen we je donatie veilig en anoniem accepteren."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance is beschikbaar in bijna elk land en ondersteunt de meeste banken en creditcards/debetkaarten. Dit is op dit moment onze voornaamste aanbeveling. We waarderen het dat je de tijd neemt om te leren hoe je met deze methode kunt doneren, omdat dit ons enorm helpt."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Doneer met je reguliere PayPal account."

msgid "page.donate.payment.desc.givebutter"
msgstr "Doneer met creditcard/debetkaart, PayPal of Venmo. Op de volgende pagina kun je een keuze maken."

msgid "page.donate.payment.desc.amazon"
msgstr "Doneren met een Amazon-cadeaubon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Houd er rekening mee dat we moeten afronden naar bedragen die door onze wederverkopers worden geaccepteerd (minimaal %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>BELANGRIJK:</strong> We ondersteunen alleen Amazon.com, geen andere websites van Amazon. Lokale winkels zoals .nl, .de en .co.uk worden níét ondersteund."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>BELANGRIJK:</strong> Deze optie is voor %(amazon)s. Als u een andere Amazon-website wilt gebruiken, selecteer deze dan hierboven."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Deze methode gebruikt een cryptocurrency aanbieder als tussenliggende conversie. Dit kan verwarrend zijn, dus gebruik deze methode alleen als andere betaalmethoden niet werken. De methode werkt ook niet in alle landen."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Doneer met een creditcard/debetkaart via de Alipay-app (supereenvoudig in te stellen)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installeer de Alipay-app"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installeer de Alipay-app vanuit de <a %(a_app_store)s>Apple App Store</a> of <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registreer met je telefoonnummer."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Geen verdere persoonlijke gegevens zijn vereist."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Voeg bankkaart toe"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Ondersteund: Visa, MasterCard, JCB, Diners Club en Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Zie <a %(a_alipay)s>deze handleiding</a> voor meer informatie."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "We kunnen credit/debit kaarten niet rechtstreeks ondersteunen, omdat banken niet met ons willen werken. ☹ Er zijn echter verschillende manieren om toch credit/debit kaarten te gebruiken via andere betaalmethoden:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon-cadeaubon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Stuur ons cadeaubonnen van Amazon.com met je creditcard of betaalpas."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay ondersteunt internationale credit/debit kaarten. Zie <a %(a_alipay)s>deze gids</a> voor meer informatie."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) biedt ondersteuning voor internationale credit/debit kaarten. Ga in de WeChat-app naar Me → Services → Wallet → Add a Card. Als je die optie niet ziet, kun je het inschakelen via Me → Settings → General → Tools → Weixin Pay → Enable."

msgid "page.donate.ccexp.crypto"
msgstr "Je kunt crypto kopen met een creditcard of betaalpas."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Crypto expressdiensten"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Expressdiensten zijn handig, maar rekenen hogere kosten."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Je kunt dit gebruiken in plaats van een crypto-uitwisseling als je snel een grotere donatie wil doen en geen bezwaar hebt tegen kosten van $5-10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Zorg ervoor dat je het exacte crypto bedrag verzendt dat op de donatiepagina wordt weergegeven en niet het bedrag in $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Anders worden de kosten afgetrokken en kunnen we je lidmaatschap niet automatisch verwerken."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s afhankelijk van het land, geen verificatie voor de eerste transactie)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, geen verificatie voor de eerste transactie)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Als deze informatie niet meer up-to-date is, stuur ons dan een e-mail om ons op de hoogte te stellen."

msgid "page.donate.payment.desc.bmc"
msgstr "Voor creditcards, debitcards, Apple Pay en Google Pay gebruiken we “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In hun systeem is één “koffie” gelijk aan $5, dus je donatie wordt afgerond naar het dichtstbijzijnde veelvoud van 5."

msgid "page.donate.duration.intro"
msgstr "Kies hoelang je je wil abonneren."

msgid "page.donate.duration.1_mo"
msgstr "1 maand"

msgid "page.donate.duration.3_mo"
msgstr "3 maanden"

msgid "page.donate.duration.6_mo"
msgstr "6 maanden"

msgid "page.donate.duration.12_mo"
msgstr "12 maanden"

msgid "page.donate.duration.24_mo"
msgstr "24 maanden"

msgid "page.donate.duration.48_mo"
msgstr "48 maanden"

msgid "page.donate.duration.96_mo"
msgstr "96 maanden"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>na <span %(span_discount)s></span> kortingen</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Voor deze betaalmethode is een minimum van %(amount)s vereist. Selecteer een andere duur of betaalmethode."

msgid "page.donate.buttons.donate"
msgstr "Doneer"

msgid "page.donate.payment.maximum_method"
msgstr "Deze betaalmethode staat slechts een maximum van %(amount)s toe. Selecteer een andere duur of betaalmethode."

msgid "page.donate.login2"
msgstr "<a %(a_login)s>Log in of registreer</a> om lid te worden. Bedankt voor je steun!"

msgid "page.donate.payment.crypto_select"
msgstr "Selecteer je gewenste cryptomunt:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(laagste minimumbedrag)"

msgid "page.donate.coinbase_eth"
msgstr "(gebruik bij het verzenden van Ethereum vanuit Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(waarschuwing: hoogste minimumbedrag)"

msgid "page.donate.submit.confirm"
msgstr "Klik op de doneerknop om deze donatie te bevestigen."

msgid "page.donate.submit.button"
msgstr "<span %(span_cost)s></span> doneren <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Je kunt de donatie nog annuleren tijdens het afrekenen."

msgid "page.donate.submit.success"
msgstr "✅ Je wordt doorverwezen naar de donatiepagina…"

msgid "page.donate.submit.failure"
msgstr "❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / maand"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "voor 1 maand"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "voor 3 maanden"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "voor 6 maanden"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "voor 12 maanden"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "voor 24 maanden"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "voor 48 maanden"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "voor 96 maanden"

msgid "page.donate.submit.button.label.1_mo"
msgstr "voor 1 maand %(tier_name)s"

msgid "page.donate.submit.button.label.3_mo"
msgstr "voor 3 maanden %(tier_name)s"

msgid "page.donate.submit.button.label.6_mo"
msgstr "voor 6 maanden %(tier_name)s"

msgid "page.donate.submit.button.label.12_mo"
msgstr "voor 12 maanden %(tier_name)s"

msgid "page.donate.submit.button.label.24_mo"
msgstr "voor 24 maanden %(tier_name)s"

msgid "page.donate.submit.button.label.48_mo"
msgstr "voor 48 maanden “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "voor 96 maanden “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donatie"

msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand voor %(duration)s maanden, inclusief %(discounts)s%% korting)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand voor %(duration)s maanden)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificatiecode: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Annuleren"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Weet je zeker dat je wilt annuleren? Annuleer niet als je al hebt betaald."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, annuleren"

msgid "page.donation.header.cancel.success"
msgstr "✅ Je donatie is geannuleerd."

msgid "page.donation.header.cancel.new_donation"
msgstr "Nieuwe donatie doen"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw."

msgid "page.donation.header.reorder"
msgstr "Opnieuw bestellen"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Je hebt al betaald. Klik hier als je de betaalinstructies toch wilt bekijken:"

msgid "page.donation.old_instructions.show_button"
msgstr "Oude betaalinstructies tonen"

msgid "page.donation.thank_you_donation"
msgstr "Bedankt voor je donatie!"

msgid "page.donation.thank_you.secret_key"
msgstr "Schrijf je geheime sleutel op om in te loggen als je dat nog niet hebt gedaan:"

msgid "page.donation.thank_you.locked_out"
msgstr "Anders word je mogelijk buitengesloten van dit account!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "De betaalinstructies zijn nu verouderd. Gebruik de knop 'Opnieuw bestellen' hierboven als je een andere donatie wil doen."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Belangrijke opmerking:</strong> Cryptoprijzen kunnen enorm fluctueren; soms wel 20%% in een paar minuten. Dit is nog steeds minder dan de kosten die we bij veel betalingsaanbieders oplopen. Deze vragen vaak 50-60%% om met een 'grijze' liefdadigheidsinstelling als wij te werken. <u>Als je ons een afschrift stuurt met de oorspronkelijke prijs die je hebt betaald, dan schrijven we je account alsnog het gekozen lidmaatschap bij</u> (zolang het afschrift niet ouder dan een paar uur is). We waarderen het enorm dat je dit soort dingen verdraagt om ons te steunen! ❤️"

msgid "page.donation.expired"
msgstr "Deze donatie is verlopen. Annuleer 'm en start een nieuwe."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instructies voor crypto"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Maak over naar een van onze cryptorekeningen"

msgid "page.donation.payment.crypto.text1"
msgstr "Doneer het totaalbedrag van %(total)s naar een van de volgende adressen:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Bitcoin op PayPal kopen"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Zoek de pagina Crypto in de PayPal-app of -website. Deze vind je vaak onder Financiën."

msgid "page.donation.payment.paypal.text3"
msgstr "Volg de instructies om Bitcoin (BTC) te kopen. Je hoeft alleen het bedrag te kopen dat je wil doneren, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Bitcoin naar ons adres versturen"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Ga naar de Bitcoin-pagina in de PayPal-app of -website. Druk op de knop Versturen %(transfer_icon)s en daarna op Verzenden."

msgid "page.donation.payment.paypal.text5"
msgstr "Voer ons Bitcoin (BTC)-adres in als ontvanger en volg de instructies om je donatie van %(total)s te doen:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instructies voor creditcard/betaalpas"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Doneren via onze pagina voor creditcards/betaalpassen"

msgid "page.donation.donate_on_this_page"
msgstr "Doneer %(amount)s op <a %(a_page)s>deze pagina</a>."

msgid "page.donation.stepbystep_below"
msgstr "Raadpleeg de stapsgewijze handleiding hierboven."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Wachten op bevestiging (vernieuw de pagina om te controleren)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Wachten op transactie (vernieuw de pagina om te controleren)…"

msgid "page.donation.time_left_header"
msgstr "Tijd resterend:"

msgid "page.donation.might_want_to_cancel"
msgstr "(het kan handig zijn om te annuleren en een nieuwe donatie te starten)"

msgid "page.donation.reset_timer"
msgstr "Start een nieuwe donatie om de teller terug te zetten."

msgid "page.donation.refresh_status"
msgstr "Status bijwerken"

msgid "page.donation.footer.issues_contact"
msgstr "Neem contact met ons op via %(email)s als je problemen ondervindt. Voeg zo veel mogelijk informatie toe (zoals screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Als u al betaald heeft:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Soms kan de bevestiging tot 24 uur duren, dus zorg ervoor dat u deze pagina ververst (ook als deze is verlopen)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Koop PYUSD op PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Volg de instructies om PYUSD (PayPal USD) te kopen."

msgid "page.donation.pyusd.more"
msgstr "Koop iets meer (we raden %(more)s extra aan) dan het bedrag dat je wilt doneren (%(amount)s) om de transactiekosten te dekken. Wat overblijft, kun je houden."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Ga naar de pagina 'PYUSD' in de app of op de website van PayPal. Druk op de knop 'Transfer' %(icon)s en vervolgens op 'Send'."

msgid "page.donation.transfer_amount_to"
msgstr "Maak %(amount)s over naar %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Koop Bitcoin (BTC) met Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Ga naar de “Bitcoin” (BTC) pagina in Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Koop iets meer (we raden %(more)s meer aan) dan het bedrag dat je doneert (%(amount)s) om transactiekosten te dekken. Je houdt alles wat overblijft."

msgid "page.donation.cash_app_btc.step2"
msgstr "Stuur de Bitcoin naar ons adres"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik op de knop “Stuur bitcoin” om een “opname” te doen. Schakel over van dollars naar BTC door op het %(icon)s icoon te drukken. Voer het BTC bedrag er onder in en klik op “Stuur”. Bekijk <a %(help_video)s>deze video</a> als je vastloopt."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Voor kleine donaties (onder $25) moet je mogelijk Rush of Priority gebruiken."

msgid "page.donation.revolut.step1"
msgstr "Koop Bitcoin (BTC) bij Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Ga naar de “Crypto” pagina in Revolut om Bitcoin (BTC) te kopen."

msgid "page.donation.revolut.step1.more"
msgstr "Koop iets meer (we raden %(more)s meer aan) dan het bedrag dat je doneert (%(amount)s), om transactiekosten te dekken. Je houdt alles wat overblijft."

msgid "page.donation.revolut.step2"
msgstr "Stuur de Bitcoin naar ons adres"

msgid "page.donation.revolut.step2.transfer"
msgstr "Klik op de knop “Stuur bitcoin” om een “opname” te doen. Schakel over van euro's naar BTC door op het %(icon)s icoon te drukken. Voer het BTC-bedrag er onder in en klik op “Stuur”. Bekijk <a %(help_video)s>deze video</a> als je vastloopt."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Zorg ervoor dat je het BTC-bedrag hieronder gebruikt, en <em>NIET</em> euro's of dollars, anders ontvangen we niet het juiste bedrag en kunnen we je lidmaatschap niet automatisch bevestigen."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Voor kleine donaties (onder de $25) moet je mogelijk Rush of Priority gebruiken."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Gebruik een van de volgende “creditcard naar Bitcoin” express diensten, die slechts enkele minuten duren:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Vul de volgende gegevens in op het formulier:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin bedrag:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Gebruik dit <span %(underline)s>exacte bedrag</span>. Je totale kosten kunnen hoger zijn vanwege creditcardkosten. Voor kleine bedragen kan dit helaas meer zijn dan onze korting."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adres (externe wallet):"

msgid "page.donation.crypto_instructions"
msgstr "Instructies voor %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "We ondersteunen alleen de standaardversie van cryptomunten, dus geen exotische netwerken of versies van munten. Afhankelijk van de munt kan het een uur duren om transacties te bevestigen."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR -code om te betalen"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan deze QR -code met uw crypto -portemonnee -app om de betalingsgegevens snel in te vullen"

msgid "page.donation.amazon.header"
msgstr "Amazon-cadeaubon"

msgid "page.donation.amazon.form_instructions"
msgstr "Gebruik het <a %(a_form)s>officiële formulier van Amazon.com</a> om ons een cadeaubon van %(amount)s te sturen naar onderstaand e-mailadres."

msgid "page.donation.amazon.only_official"
msgstr "We accepteren geen andere methodes voor cadeaubonnen, <strong>alleen die rechtstreeks via het officiële formulier op Amazon.com worden verzonden</strong>. We kunnen je cadeaubon niet retourneren als je dit formulier niet gebruikt."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Voer het exacte bedrag in: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Schrijf NIET je eigen bericht."

msgid "page.donation.amazon.form_to"
msgstr "Geadresseerde 'Aan' in het formulier:"

msgid "page.donation.amazon.unique"
msgstr "Uniek voor jouw account; niet delen."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Slechts één keer gebruiken."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Wachten op cadeaubon (vernieuw de pagina om te controleren)…"

msgid "page.donation.amazon.confirm_automated"
msgstr "Nadat je je cadeaubon hebt verzonden, bevestigt ons geautomatiseerde systeem dit binnen een paar minuten. Probeer je cadeaubon opnieuw te verzenden als dit niet werkt (<a %(a_instr)s>instructies</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Als dat nog steeds niet werkt, stuur ons dan een e-mail. Anna controleert het vervolgens handmatig (dit kan een paar dagen duren). Vermeld daarbij of je al opnieuw hebt geprobeerd te verzenden."

msgid "page.donation.amazon.example"
msgstr "Voorbeeld:"

msgid "page.donate.strange_account"
msgstr "Let op dat je accountnaam of profielfoto er vreemd uit kan zien. Geen zorgen! Deze accounts worden beheerd door onze donatiepartners. Onze accounts zijn niet gehackt."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instructies voor Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Doneren met Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Doneer het totale bedrag van %(total)s via <a %(a_account)s>dit Alipay account</a>"

msgid "page.donation.page_blocked"
msgstr "Als de donatiepagina wordt geblokkeerd, probeer dan een andere internetverbinding (bijv. VPN of mobiel internet)."

msgid "page.donation.payment.alipay.error"
msgstr "Helaas is de Alipay pagina vaak alleen toegankelijk vanuit <strong>het vasteland van China</strong>. Je moet mogelijk tijdelijk je VPN uitschakelen, of een VPN naar het vasteland van China gebruiken (Hong Kong werkt soms ook)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Doe een donatie (scan QR-code of druk op de knop)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Open de <a %(a_href)s>QR-code donatiepagina</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scan de QR-code met de Alipay-app, of druk op de knop om de Alipay-app te openen."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Wees geduldig; het kan even duren voordat de pagina geladen is, omdat deze in China is."

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat instructies"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Doneer via WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Doneer het totale bedrag van %(total)s via <a %(a_account)s>dit WeChat account</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instructies voor Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Doneren met Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Doneer het totaalbedrag van %(total)s aan <a %(a_account)s>dit Pix-account"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Mail ons het ontvangstbewijs"

msgid "page.donation.footer.verification"
msgstr "Stuur een ontvangstbewijs of screenshot naar je persoonlijke verificatie adres. Gebruik dit e-mailadres NIET voor je PayPal donatie."

msgid "page.donation.footer.text1"
msgstr "Stuur een ontvangstbewijs of schermafbeelding naar je persoonlijke verificatieadres:"

msgid "page.donation.footer.crypto_note"
msgstr "Als de koers op de cryptobeurs tijdens de transactie fluctueerde, zorg er dan voor dat je het afschrift met de oorspronkelijke wisselkoers bijvoegt. We waarderen het dat je de moeite neemt om crypto te gebruiken. Dat helpt ons enorm!"

msgid "page.donation.footer.text2"
msgstr "Klik op deze knop als je je ontvangstbewijs hebt gemaild, zodat Anna dat handmatig kan controleren (dit kan een paar dagen duren):"

msgid "page.donation.footer.button"
msgstr "Ja, ik heb mijn ontvangstbewijs gemaild"

msgid "page.donation.footer.success"
msgstr "✅ Bedankt voor je donatie! Anna activeert je lidmaatschap handmatig binnen een paar dagen."

msgid "page.donation.footer.failure"
msgstr "❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw."

msgid "page.donation.stepbystep"
msgstr "Stapsgewijze handleiding"

msgid "page.donation.crypto_dont_worry"
msgstr "Sommige stappen benoemen 'cryptowallets'. Geen zorgen: je hoeft niets over crypto te leren."

msgid "page.donation.hoodpay.step1"
msgstr "1. Voer je e-mailadres in."

msgid "page.donation.hoodpay.step2"
msgstr "2. Selecteer de betaalmethode."

msgid "page.donation.hoodpay.step3"
msgstr "3. Selecteer de betaalmethode opnieuw."

msgid "page.donation.hoodpay.step4"
msgstr "4. Selecteer de wallet 'Self-hosted'."

msgid "page.donation.hoodpay.step5"
msgstr "5. Klik op 'I confirm ownership'."

msgid "page.donation.hoodpay.step6"
msgstr "6. Je ontvangt een afschrift. Stuur dat naar ons. Wij bevestigen je donatie zo snel mogelijk."

msgid "page.donate.wait_new"
msgstr "Wacht alsjeblieft minstens <span %(span_hours)s>24 uur</span> (en ververs deze pagina) voordat je contact met ons opneemt."

msgid "page.donate.mistake"
msgstr "Als je tijdens het betalen een fout hebt gemaakt, kunnen we je niet terugbetalen. We doen dan ons best om het te corrigeren."

msgid "page.my_donations.title"
msgstr "Mijn donaties"

msgid "page.my_donations.not_shown"
msgstr "Donatiegegevens zijn niet openbaar zichtbaar."

msgid "page.my_donations.no_donations"
msgstr "Nog geen donaties. <a %(a_donate)s>Ik wil mijn eerste donatie doen.</a>"

msgid "page.my_donations.make_another"
msgstr "Nog een donatie doen."

msgid "page.downloaded.title"
msgstr "Gedownloade bestanden"

msgid "page.downloaded.fast_partner_star"
msgstr "Downloads van snelle partnerservers worden gemarkeerd met %(icon)s."

msgid "page.downloaded.twice"
msgstr "Als je een bestand met zowel een snelle als langzame download hebt binnengehaald, worden beide weergegeven."

msgid "page.downloaded.fast_download_time"
msgstr "Snelle downloads van de afgelopen 24 uur tellen mee voor de daglimiet."

msgid "page.downloaded.times_utc"
msgstr "Alle tijden worden weergegeven in UTC."

msgid "page.downloaded.not_public"
msgstr "Gedownloade bestanden zijn niet openbaar zichtbaar."

msgid "page.downloaded.no_files"
msgstr "Nog geen bestanden gedownload."

msgid "page.downloaded.last_18_hours"
msgstr "Laatste 18 uur"

msgid "page.downloaded.earlier"
msgstr "Eerder"

msgid "page.account.logged_in.title"
msgstr "Account"

msgid "page.account.logged_out.title"
msgstr "Inloggen/registreren"

msgid "page.account.logged_in.account_id"
msgstr "Account-ID: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Openbaar profiel: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Geheime sleutel (niet delen!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "tonen"

msgid "page.account.logged_in.membership_has_some"
msgstr "Lidmaatschap: <strong>%(tier_name)s</strong> tot %(until_date)s <a %(a_extend)s>(verlengen)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Lidmaatschap: <strong>geen</strong> <a %(a_become)s>(lid worden)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Gebruikte snelle downloads (afgelopen 24 uur): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "welke downloads?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Exclusieve Telegram-groep: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Sluit je aan!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Upgrade naar een <a %(a_tier)s>hoger niveau</a> om aan onze groep deel te nemen."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Neem contact op met Anna via %(email)s als je je lidmaatschap naar een hoger niveau wilt upgraden."

msgid "page.contact.title"
msgstr "Contactmail"

msgid "page.account.logged_in.membership_multiple"
msgstr "Je kunt meerdere lidmaatschappen combineren (het aantal snelle downloads per 24 uur wordt dan samengevoegd)."

msgid "layout.index.header.nav.public_profile"
msgstr "Openbaar profiel"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Gedownloade bestanden"

msgid "layout.index.header.nav.my_donations"
msgstr "Mijn donaties"

msgid "page.account.logged_in.logout.button"
msgstr "Uitloggen"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Je bent nu uitgelogd. Herlaad de pagina om opnieuw in te loggen."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw."

msgid "page.account.logged_out.registered.text1"
msgstr "Registratie gelukt! Je geheime sleutel is: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Bewaar deze sleutel goed. Als je 'm kwijtraakt, heb je geen toegang meer tot je account."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Favorieten.</strong> Je kunt deze pagina aan je favorieten toevoegen om je sleutel op te halen.</li><li %(li_item)s><strong>Downloaden.</strong> Klik op <a %(a_download)s>deze link</a> om je sleutel te downloaden.</li><li %(li_item)s><strong>Wachtwoordmanager.</strong> Gebruik een wachtwoordmanager om de sleutel op te slaan wanneer je deze hieronder invoert.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Voer je geheime sleutel in om in te loggen:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Geheime sleutel"

msgid "page.account.logged_out.key_form.button"
msgstr "Inloggen"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ongeldige geheime sleutel. Controleer je sleutel en probeer het opnieuw of registreer hieronder een nieuw account."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Raak je sleutel niet kwijt!"

msgid "page.account.logged_out.register.header"
msgstr "Heb je nog geen account?"

msgid "page.account.logged_out.register.button"
msgstr "Nieuw account registreren"

msgid "page.login.lost_key"
msgstr "<a %(a_contact)s>Neem contact met ons op</a> als je je sleutel bent verloren en geef daarbij zo veel mogelijk informatie door."

msgid "page.login.lost_key_contact"
msgstr "Om contact op te nemen, moet je mogelijk tijdelijk een nieuw account maken."

msgid "page.account.logged_out.old_email.button"
msgstr "Oud account met e-mailadres? Voer <a %(a_open)s>hier</a> je e-mail in."

msgid "page.list.title"
msgstr "Lijst"

msgid "page.list.header.edit.link"
msgstr "bewerken"

msgid "page.list.edit.button"
msgstr "Opslaan"

msgid "page.list.edit.success"
msgstr "✅ Opgeslagen. Vernieuw de pagina."

msgid "page.list.edit.failure"
msgstr "❌ Er ging iets mis. Probeer het opnieuw."

msgid "page.list.by_and_date"
msgstr "Lijst van %(by)s, gemaakt op <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "De lijst is leeg."

msgid "page.list.new_item"
msgstr "Voeg dingen aan deze lijst toe of verwijder ze door een bestand te zoeken en het tabblad Lijsten te openen."

msgid "page.profile.title"
msgstr "Profiel"

msgid "page.profile.not_found"
msgstr "Profiel niet gevonden."

msgid "page.profile.header.edit"
msgstr "bewerken"

msgid "page.profile.change_display_name.text"
msgstr "Wijzig je weergavenaam. Je identificatiecode (het deel na #) kan niet worden gewijzigd."

msgid "page.profile.change_display_name.button"
msgstr "Opslaan"

msgid "page.profile.change_display_name.success"
msgstr "✅ Opgeslagen. Vernieuw de pagina."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Er ging iets mis. Probeer het opnieuw."

msgid "page.profile.created_time"
msgstr "Profiel gemaakt: <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Lijsten"

msgid "page.profile.lists.no_lists"
msgstr "Nog geen lijsten"

msgid "page.profile.lists.new_list"
msgstr "Maak een nieuwe lijst door een titel te zoeken en het tabblad Lijsten te openen."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Hervorming van het auteursrecht is noodzakelijk voor nationale veiligheid"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Chinese LLM's (inclusief DeepSeek) zijn getraind op mijn illegale archief van boeken en papers — het grootste ter wereld. Het Westen moet het auteursrecht herzien als een kwestie van nationale veiligheid."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "begeleidende artikelen door TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Niet zo lang geleden waren “schaduw-bibliotheken” aan het uitsterven. Sci-Hub, het enorme illegale archief van academische papers, was gestopt met het opnemen van nieuwe werken, vanwege rechtszaken. “Z-Library”, de grootste illegale bibliotheek van boeken, zag zijn vermeende makers gearresteerd op strafrechtelijke aanklachten wegens auteursrecht. Ze slaagden er ongelooflijk in om aan hun arrestatie te ontsnappen, maar hun bibliotheek is niet minder bedreigd."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Toen Z-Library met sluiting werd geconfronteerd, had ik al hun hele bibliotheek geback-upt en was ik op zoek naar een platform om het te huisvesten. Dat was mijn motivatie om Anna’s Archief te starten: een voortzetting van de missie achter die eerdere initiatieven. We zijn sindsdien uitgegroeid tot de grootste schaduw bibliotheek ter wereld, met meer dan 140 miljoen auteursrechtelijk beschermde teksten in tal van formaten — boeken, academische papers, tijdschriften, kranten en meer."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mijn team en ik zijn ideologen. Wij geloven dat het bewaren en hosten van deze bestanden moreel juist is. Bibliotheken over de hele wereld zien hun financiering gekort worden, en we kunnen het erfgoed van de mensheid ook niet aan bedrijven toevertrouwen."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Toen kwam AI. Vrijwel alle grote bedrijven die LLM's bouwen, namen contact met ons op om op onze data te trainen. De meeste (maar niet alle!) Amerikaanse bedrijven heroverwogen dit toen ze zich realiseerden dat ons werk illegaal was. Daarentegen hebben Chinese bedrijven onze collectie enthousiast omarmd, blijkbaar niet gehinderd door de legaliteit ervan. Dit is opmerkelijk gezien de rol van China als ondertekenaar van bijna alle grote internationale auteursrechtverdragen."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "We hebben ongeveer 30 bedrijven toegang met hoge snelheid gegeven. De meeste zijn LLM-bedrijven, en sommige zijn databrokers, die onze collectie zullen doorverkopen. De meeste zijn Chinees, hoewel we ook hebben samengewerkt met bedrijven uit de VS, Europa, Rusland, Zuid-Korea en Japan. DeepSeek <a %(arxiv)s>gaf toe</a> dat een eerdere versie was getraind op een deel van onze collectie, hoewel ze zwijgzaam zijn over hun nieuwste model (waarschijnlijk ook getraind op onze data)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Als het Westen voorop wil blijven in de race van LLM's, en uiteindelijk AGI, moet het zijn standpunt over auteursrecht heroverwegen, en snel. Of u het nu met ons eens bent of niet over onze morele zaak, dit wordt nu een kwestie van economie, en zelfs van nationale veiligheid. Alle machtsblokken bouwen kunstmatige superwetenschappers, superhackers en superlegers. Vrijheid van informatie wordt een kwestie van overleven voor deze landen — zelfs een kwestie van nationale veiligheid."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Ons team komt van over de hele wereld en we hebben geen specifieke voorkeur. Maar we zouden landen met strenge auteursrechtwetten willen aanmoedigen om deze existentiële dreiging te gebruiken om ze te hervormen. Dus wat te doen?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Onze eerste aanbeveling is eenvoudig: verkort de duur van het auteursrecht. In de VS wordt auteursrecht verleend voor 70 jaar na de dood van de auteur. Dit is absurd. We kunnen dit in lijn brengen met patenten, die worden verleend voor 20 jaar na indiening. Dit zou meer dan genoeg tijd moeten zijn voor auteurs van boeken, artikelen, muziek, kunst en andere creatieve werken, om volledig gecompenseerd te worden voor hun inspanningen (inclusief langetermijnprojecten zoals filmadaptaties)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Vervolgens zouden beleidsmakers op zijn minst uitzonderingen moeten opnemen voor de massale bewaring en verspreiding van teksten. Als verloren inkomsten van individuele klanten de grootste zorg zijn, zou distributie op persoonlijk niveau verboden kunnen blijven. Op hun beurt zouden degenen die in staat zijn om enorme verzamelingen te beheren — bedrijven die LLM's trainen, samen met bibliotheken en andere archieven — onder deze uitzonderingen vallen."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Sommige landen doen al een versie hiervan. TorrentFreak <a %(torrentfreak)s>rapporteerde</a> dat China en Japan AI-uitzonderingen hebben geïntroduceerd in hun auteursrechtwetten. Het is voor ons onduidelijk hoe dit samenwerkt met internationale verdragen, maar het biedt zeker dekking aan hun binnenlandse bedrijven, wat verklaart wat we hebben gezien."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Wat betreft Anna’s Archief — we zullen ons ondergrondse werk voortzetten, geworteld in morele overtuiging. Toch is onze grootste wens om in het licht te treden en onze impact legaal te versterken. Hervorm alstublieft het auteursrecht."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lees de begeleidende artikelen van TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Winnaars van de $10.000 ISBN-visualisatiebeloning"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Kort samengevat: We hebben enkele ongelooflijke inzendingen ontvangen voor de $10.000 ISBN-visualisatiebeloning."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Een paar maanden geleden kondigden we een <a %(all_isbns)s>beloning van $10.000</a> aan om de best mogelijke visualisatie van onze gegevens te maken die de ISBN-ruimte toont. We benadrukten het tonen van welke bestanden we al wel/niet hebben gearchiveerd, en we voegden later een dataset toe die beschrijft hoeveel bibliotheken ISBN's bezitten (een maatstaf voor zeldzaamheid)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "We zijn overweldigd door de respons. Er is zoveel creativiteit geweest. Een grote dank aan iedereen die heeft deelgenomen: uw energie en enthousiasme zijn aanstekelijk!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Uiteindelijk wilden we de volgende vragen beantwoorden: <strong>welke boeken bestaan er in de wereld, hoeveel hebben we al gearchiveerd, en op welke boeken moeten we ons vervolgens richten?</strong> Het is geweldig om te zien dat zoveel mensen om deze vragen geven."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "We begonnen zelf met een basisvisualisatie. In minder dan 300kb vertegenwoordigt deze afbeelding beknopt de grootste volledig open \"boekenlijst\" ooit samengesteld in de geschiedenis van de mensheid:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBN's"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Bestanden in Anna’s Archief"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO's"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC datalek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID's"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Boeken"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russische Staatsbibliotheek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Keizerlijke Bibliotheek van Trantor"

#, fuzzy
msgid "common.back"
msgstr "Terug"

#, fuzzy
msgid "common.forward"
msgstr "Vooruit"

#, fuzzy
msgid "common.last"
msgstr "Laatste"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Zie de <a %(all_isbns)s>originele blogpost</a> voor meer informatie."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "We hebben een uitdaging uitgegeven om dit te verbeteren. We zouden een eerste plaats beloning van $6.000, een tweede plaats van $3.000 en een derde plaats van $1.000 toekennen. Vanwege de overweldigende respons en ongelooflijke inzendingen hebben we besloten de prijzenpot iets te verhogen en een gedeelde derde plaats van $500 elk toe te kennen. De winnaars staan hieronder, maar zorg ervoor dat je alle inzendingen <a %(annas_archive)s>hier</a> bekijkt, of download onze <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Eerste plaats $6.000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Deze <a %(phiresky_github)s>inzending</a> (<a %(annas_archive_note_2951)s>Gitlab-opmerking</a>) is precies wat we wilden, en meer! We waren vooral gecharmeerd van de ongelooflijk flexibele visualisatiemogelijkheden (zelfs met ondersteuning voor aangepaste shaders), maar met een uitgebreide lijst van presets. We vonden ook de snelheid en soepelheid geweldig, de eenvoudige implementatie (die zelfs geen backend heeft), de slimme minimap en de uitgebreide uitleg in hun <a %(phiresky_github)s>blogbericht</a>. Ongelooflijk werk, en de welverdiende winnaar!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Tweede plaats $3.000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Nog een geweldige <a %(annas_archive_note_2913)s>inzending</a>. Niet zo flexibel als de eerste plaats, maar we gaven eigenlijk de voorkeur aan de macro-niveau visualisatie boven de eerste plaats (ruimte-vullende curve, grenzen, labeling, highlighting, panning en zoomen). Een <a %(annas_archive_note_2971)s>opmerking</a> van Joe Davis sprak ons aan:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Hoewel perfecte vierkanten en rechthoeken wiskundig bevredigend zijn, bieden ze geen superieure localiteit in een mappingcontext. Ik geloof dat de asymmetrie inherent aan deze Hilbert of klassieke Morton geen fout is, maar een kenmerk. Net zoals de beroemde laarsvormige omtrek van Italië het onmiddellijk herkenbaar maakt op een kaart, kunnen de unieke \"eigenaardigheden\" van deze curves dienen als cognitieve bakens. Deze onderscheidendheid kan het ruimtelijk geheugen verbeteren en gebruikers helpen zich te oriënteren, waardoor het mogelijk gemakkelijker wordt om specifieke regio's te lokaliseren of patronen op te merken.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "En nog steeds veel opties voor visualisatie en rendering, evenals een ongelooflijk soepele en intuïtieve UI. Een solide tweede plaats!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Derde plaats $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In deze <a %(annas_archive_note_2940)s>inzending</a> vonden we de verschillende soorten weergaven erg leuk, met name de vergelijkings- en uitgeversweergaven."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Derde plaats $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Hoewel niet de meest gepolijste UI, voldoet deze <a %(annas_archive_note_2917)s>inzending</a> aan veel van de eisen. We vonden vooral de vergelijkingsfunctie erg goed."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Derde plaats $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Net als de eerste plaats, heeft deze <a %(annas_archive_note_2975)s>inzending</a> ons geïmponeerd met zijn flexibiliteit. Uiteindelijk is dit wat een geweldige visualisatietool maakt: maximale flexibiliteit voor power users, terwijl het eenvoudig blijft voor gemiddelde gebruikers."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Derde plaats $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "De laatste <a %(annas_archive_note_2947)s>inzending</a> om een beloning te krijgen is vrij eenvoudig, maar heeft enkele unieke kenmerken die we erg leuk vonden. We vonden het leuk hoe ze laten zien hoeveel datasets een bepaalde ISBN dekken als een maatstaf voor populariteit/betrouwbaarheid. We vonden ook de eenvoud maar effectiviteit van het gebruik van een opaciteitsschuifregelaar voor vergelijkingen erg goed."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Opmerkelijke ideeën"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Nog enkele ideeën en implementaties die we bijzonder leuk vonden:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Wolkenkrabbers voor zeldzaamheid"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live statistieken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotaties, en ook live statistieken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unieke kaartweergave en filters"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Coole standaardkleurenpalet en heatmap."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Eenvoudig schakelen van datasets voor snelle vergelijkingen."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Mooie labels."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Schaalstreep met aantal boeken."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Veel schuifregelaars om datasets te vergelijken, alsof je een DJ bent."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "We zouden nog wel even door kunnen gaan, maar laten we hier stoppen. Zorg ervoor dat je alle inzendingen <a %(annas_archive)s>hier</a> bekijkt, of download onze <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a>. Zoveel inzendingen, en elk biedt een uniek perspectief, of het nu in UI of implementatie is."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "We zullen in ieder geval de inzending die de eerste plaats behaalde integreren in onze hoofdwebsite, en misschien nog enkele anderen. We zijn ook begonnen na te denken over hoe we het proces van het identificeren, bevestigen en vervolgens archiveren van de zeldzaamste boeken kunnen organiseren. Meer hierover volgt."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Bedankt aan iedereen die heeft deelgenomen. Het is geweldig dat zoveel mensen geven om dit project."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Onze harten zijn vol dankbaarheid."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualiseren van Alle ISBN's — $10.000 beloning tegen 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Deze afbeelding vertegenwoordigt de grootste volledig open \"boekenlijst\" ooit samengesteld in de geschiedenis van de mensheid."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Deze afbeelding is 1000×800 pixels. Elke pixel vertegenwoordigt 2.500 ISBN's. Als we een bestand hebben voor een ISBN, maken we die pixel groener. Als we weten dat een ISBN is uitgegeven, maar we hebben geen bijbehorend bestand, maken we het roder."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In minder dan 300kb vertegenwoordigt deze afbeelding beknopt de grootste volledig open \"boekenlijst\" ooit samengesteld in de geschiedenis van de mensheid (een paar honderd GB volledig gecomprimeerd)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Het laat ook zien: er is nog veel werk te doen bij het back-uppen van boeken (we hebben er slechts 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Achtergrond"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hoe kan Anna’s Archief haar missie volbrengen om alle kennis van de mensheid te back-uppen, zonder te weten welke boeken er nog zijn? We hebben een TODO-lijst nodig. Een manier om dit in kaart te brengen is via ISBN-nummers, die sinds de jaren 70 aan elk gepubliceerd boek zijn toegewezen (in de meeste landen)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Er is geen centrale autoriteit die alle ISBN-toewijzingen kent. In plaats daarvan is het een gedistribueerd systeem, waarbij landen reeksen nummers krijgen, die vervolgens kleinere reeksen toewijzen aan grote uitgevers, die mogelijk verder onderverdelen naar kleinere uitgevers. Uiteindelijk worden individuele nummers aan boeken toegewezen."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "We zijn <a %(blog)s>twee jaar geleden</a> begonnen met het in kaart brengen van ISBN's met onze scrape van ISBNdb. Sindsdien hebben we veel meer metadata bronnen gescraped, zoals <a %(blog_2)s>Worldcat</a>, Google Boeken, Goodreads, Libby en meer. Een volledige lijst is te vinden op de pagina's “Datasets” en “Torrents” op Anna’s Archief. We hebben nu veruit de grootste volledig open, gemakkelijk downloadbare collectie van boekmetadata (en dus ISBN's) ter wereld."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "We hebben <a %(blog)s>uitgebreid geschreven</a> over waarom we geven om behoud, en waarom we ons momenteel in een kritieke periode bevinden. We moeten nu zeldzame, onderbelichte en uniek bedreigde boeken identificeren en behouden. Goede metadata over alle boeken in de wereld helpt daarbij."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualiseren"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Naast de overzichtsafbeelding kunnen we ook kijken naar individuele datasets die we hebben verkregen. Gebruik de dropdown en knoppen om daartussen te schakelen."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Er zijn veel interessante patronen te zien in deze afbeeldingen. Waarom is er enige regelmaat van lijnen en blokken, die op verschillende schalen lijkt te gebeuren? Wat zijn de lege gebieden? Waarom zijn bepaalde datasets zo geclusterd? We laten deze vragen als een oefening voor de lezer."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10.000 beloning"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Er is hier veel te ontdekken, dus we kondigen een beloning aan voor het verbeteren van de visualisatie hierboven. In tegenstelling tot de meeste van onze beloningen, is deze tijdgebonden. Je moet je open source code <a %(annas_archive)s>indienen</a> vóór 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "De beste inzending krijgt $6.000, de tweede plaats $3.000, en de derde plaats $1.000. Alle beloningen worden uitgereikt in Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Hieronder staan de minimale criteria. Als geen enkele inzending aan de criteria voldoet, kunnen we nog steeds enkele beloningen toekennen, maar dat is naar eigen goeddunken."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork deze repo en bewerk deze blogpost HTML (geen andere backends behalve onze Flask-backend zijn toegestaan)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Maak de bovenstaande afbeelding soepel in te zoomen, zodat je helemaal kunt inzoomen op individuele ISBN's. Klikken op ISBN's moet je naar een metadata-pagina of zoekopdracht op Anna’s Archief brengen."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Je moet nog steeds kunnen schakelen tussen alle verschillende datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landbereiken en uitgeverbereiken moeten worden gemarkeerd bij hoveren. Je kunt bijvoorbeeld <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> gebruiken voor landinformatie, en onze “isbngrp” scrape voor uitgevers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Het moet goed werken op desktop en mobiel."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Voor bonuspunten (dit zijn slechts ideeën — laat je creativiteit de vrije loop):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sterke overweging zal worden gegeven aan bruikbaarheid en hoe goed het eruitziet."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Toon daadwerkelijke metadata voor individuele ISBN's bij inzoomen, zoals titel en auteur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Betere ruimte-vullende curve. Bijvoorbeeld een zigzag, gaande van 0 naar 4 op de eerste rij en dan terug (in omgekeerde richting) van 5 naar 9 op de tweede rij — recursief toegepast."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Verschillende of aanpasbare kleurenschema's."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Speciale weergaven voor het vergelijken van datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Manieren om problemen op te sporen, zoals andere metadata die niet goed overeenkomen (bijv. sterk verschillende titels)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Afbeeldingen annoteren met opmerkingen over ISBN's of bereiken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Eventuele heuristieken voor het identificeren van zeldzame of bedreigde boeken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Welke creatieve ideeën je ook kunt bedenken!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Je MAG volledig afwijken van de minimale criteria en een totaal andere visualisatie maken. Als het echt spectaculair is, dan komt dat in aanmerking voor de beloning, maar naar ons eigen inzicht."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Dien inzendingen in door een opmerking te plaatsen bij <a %(annas_archive)s>dit issue</a> met een link naar je geforkte repo, merge request of diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "De code om deze afbeeldingen te genereren, evenals andere voorbeelden, is te vinden in <a %(annas_archive)s>deze directory</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "We hebben een compact dataformaat ontwikkeld, waarmee alle vereiste ISBN-informatie ongeveer 75MB (gecomprimeerd) is. De beschrijving van het dataformaat en de code om het te genereren is <a %(annas_archive_l1244_1319)s>hier</a> te vinden. Voor de beloning ben je niet verplicht dit te gebruiken, maar het is waarschijnlijk het meest handige formaat om mee te beginnen. Je kunt onze metadata transformeren zoals je wilt (hoewel al je code open source moet zijn)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "We kunnen niet wachten om te zien wat je bedenkt. Veel succes!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna’s Archief Containers (AAC): standaardiseren van releases van 's werelds grootste schaduw bibliotheek"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’s Archief is de grootste schaduw bibliotheek ter wereld geworden, waardoor we onze releases moeten standaardiseren."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’s Archief</a> is veruit de grootste schaduw bibliotheek ter wereld geworden, en de enige schaduw bibliotheek van deze schaal die volledig open-source en open-data is. Hieronder staat een tabel van onze Datasets-pagina (licht aangepast):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "We hebben dit op drie manieren bereikt:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Het spiegelen van bestaande open-data schaduw bibliotheken (zoals Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Het helpen van schaduw bibliotheken die meer open willen zijn, maar niet de tijd of middelen hadden om dit te doen (zoals de Libgen stripverzameling)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Het scrapen van bibliotheken die niet in bulk willen delen (zoals Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Voor (2) en (3) beheren we nu zelf een aanzienlijke collectie torrents (honderden TB's). Tot nu toe hebben we deze collecties als eenmalige projecten benaderd, wat betekent dat er voor elke collectie op maat gemaakte infrastructuur en dataorganisatie is. Dit voegt aanzienlijke overhead toe aan elke release en maakt het bijzonder moeilijk om meer incrementele releases te doen."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Daarom hebben we besloten om onze releases te standaardiseren. Dit is een technische blogpost waarin we onze standaard introduceren: <strong>Anna’s Archief Containers</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Ontwerpdoelen"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Ons primaire gebruiksdoel is de distributie van bestanden en bijbehorende metadata uit verschillende bestaande collecties. Onze belangrijkste overwegingen zijn:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene bestanden en metadata, zo dicht mogelijk bij het originele formaat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene identificatoren in de bronbibliotheken, of zelfs het ontbreken van identificatoren."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Gescheiden releases van metadata versus bestandsgegevens, of alleen metadata-releases (bijv. onze ISBNdb-release)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distributie via torrents, hoewel met de mogelijkheid van andere distributiemethoden (bijv. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Onveranderlijke records, aangezien we moeten aannemen dat onze torrents voor altijd zullen blijven bestaan."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Incrementele releases / aanvulbare releases."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Machineleesbaar en -schrijfbaar, handig en snel, vooral voor onze stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Enigszins eenvoudige menselijke inspectie, hoewel dit ondergeschikt is aan machineleesbaarheid."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Gemakkelijk om onze collecties te zaaien met een standaard gehuurde seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binaire data kan direct worden bediend door webservers zoals Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Enkele niet-doelen:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "We geven er niet om dat bestanden gemakkelijk handmatig op schijf te navigeren zijn, of doorzoekbaar zonder voorverwerking."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "We geven er niet om direct compatibel te zijn met bestaande bibliotheeksoftware."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Hoewel het gemakkelijk moet zijn voor iedereen om onze collectie te zaaien met torrents, verwachten we niet dat de bestanden bruikbaar zijn zonder aanzienlijke technische kennis en toewijding."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Aangezien Anna’s Archief open source is, willen we ons formaat direct gebruiken. Wanneer we onze zoekindex vernieuwen, hebben we alleen toegang tot openbaar beschikbare paden, zodat iedereen die onze bibliotheek forked snel aan de slag kan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "De standaard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Uiteindelijk hebben we gekozen voor een relatief eenvoudige standaard. Het is vrij los, niet-normatief en een werk in uitvoering."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archief Container) is een enkel item bestaande uit <strong>metadata</strong>, en optioneel <strong>binaire data</strong>, die beide onveranderlijk zijn. Het heeft een wereldwijd unieke identificator, genaamd <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collectie.</strong> Elke AAC behoort tot een collectie, die per definitie een lijst is van AAC's die semantisch consistent zijn. Dat betekent dat als u een significante verandering aanbrengt in het formaat van de metadata, u een nieuwe collectie moet creëren."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” en “files” collecties.</strong> Volgens conventie is het vaak handig om “records” en “files” als verschillende collecties uit te brengen, zodat ze op verschillende tijdschema's kunnen worden uitgebracht, bijvoorbeeld op basis van scraping-snelheden. Een “record” is een collectie die alleen uit metadata bestaat, met informatie zoals boektitels, auteurs, ISBN's, enz., terwijl “files” de collecties zijn die de daadwerkelijke bestanden zelf bevatten (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Het formaat van AACID is als volgt: <code style=\"color: #0093ff\">aacid__{collectie}__{ISO 8601 tijdstempel}__{collectie-specifieke ID}__{shortuuid}</code>. Bijvoorbeeld, een daadwerkelijke AACID die we hebben uitgebracht is <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collectie}</code>: de naam van de collectie, die ASCII-letters, cijfers en underscores kan bevatten (maar geen dubbele underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 tijdstempel}</code>: een korte versie van de ISO 8601, altijd in UTC, bijv. <code>20220723T194746Z</code>. Dit nummer moet monotoon toenemen bij elke release, hoewel de exacte betekenis per collectie kan verschillen. We raden aan om de tijd van scraping of het genereren van de ID te gebruiken."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collectie-specifieke ID}</code>: een collectie-specifieke identificator, indien van toepassing, bijv. de Z-Library ID. Mag worden weggelaten of ingekort. Moet worden weggelaten of ingekort als de AACID anders meer dan 150 tekens zou bevatten."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: een UUID maar gecomprimeerd naar ASCII, bijv. met behulp van base57. We gebruiken momenteel de <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-bibliotheek."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID-bereik.</strong> Aangezien AACID's monotoon toenemende tijdstempels bevatten, kunnen we deze gebruiken om bereiken binnen een bepaalde collectie aan te duiden. We gebruiken dit formaat: <code style=\"color: blue\">aacid__{collectie}__{van_tijdstempel}--{tot_tijdstempel}</code>, waarbij de tijdstempels inclusief zijn. Dit is consistent met ISO 8601-notatie. Bereiken zijn continu en kunnen overlappen, maar in geval van overlap moeten ze identieke records bevatten als degene die eerder in die collectie zijn uitgebracht (aangezien AAC's onveranderlijk zijn). Ontbrekende records zijn niet toegestaan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata-bestand.</strong> Een metadata-bestand bevat de metadata van een reeks AAC's, voor een specifieke collectie. Deze hebben de volgende eigenschappen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "De bestandsnaam moet een AACID-bereik zijn, voorafgegaan door <code style=\"color: red\">annas_archive_meta__</code> en gevolgd door <code>.jsonl.zstd</code>. Bijvoorbeeld, een van onze releases heet<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Zoals aangegeven door de bestandsextensie, is het bestandstype <a %(jsonlines)s>JSON Lines</a> gecomprimeerd met <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Elk JSON-object moet de volgende velden op het hoogste niveau bevatten: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optioneel). Geen andere velden zijn toegestaan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> is willekeurige metadata, volgens de semantiek van de collectie. Het moet semantisch consistent zijn binnen de collectie."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> is optioneel en is de naam van de binaire gegevensmap die de bijbehorende binaire gegevens bevat. De bestandsnaam van de bijbehorende binaire gegevens binnen die map is de AACID van het record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Het voorvoegsel <code style=\"color: red\">annas_archive_meta__</code> kan worden aangepast aan de naam van uw instelling, bijvoorbeeld <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binaire gegevensmap.</strong> Een map met de binaire gegevens van een reeks AAC's, voor een specifieke collectie. Deze hebben de volgende eigenschappen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "De mapnaam moet een AACID-bereik zijn, voorafgegaan door <code style=\"color: green\">annas_archive_data__</code>, en geen achtervoegsel. Bijvoorbeeld, een van onze daadwerkelijke releases heeft een map genaamd<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "De map moet gegevensbestanden bevatten voor alle AAC's binnen het gespecificeerde bereik. Elk gegevensbestand moet zijn AACID als bestandsnaam hebben (geen extensies)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Het wordt aanbevolen om deze mappen enigszins beheersbaar in grootte te maken, bijvoorbeeld niet groter dan 100GB-1TB elk, hoewel deze aanbeveling in de loop van de tijd kan veranderen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> De metadata-bestanden en binaire gegevensmappen kunnen worden gebundeld in torrents, met één torrent per metadata-bestand of één torrent per binaire gegevensmap. De torrents moeten de originele bestands-/mapnaam plus een <code>.torrent</code> achtervoegsel als hun bestandsnaam hebben."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Voorbeeld"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Laten we onze recente Z-Library-release als voorbeeld bekijken. Deze bestaat uit twee collecties: “<span style=\"background: #fffaa3\">zlib3_records</span>” en “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dit stelt ons in staat om metadatarecords apart van de daadwerkelijke boekbestanden te verzamelen en vrij te geven. Zo hebben we twee torrents met metadata-bestanden vrijgegeven:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "We hebben ook een aantal torrents met binaire gegevensmappen vrijgegeven, maar alleen voor de “<span style=\"background: #ffd6fe\">zlib3_files</span>” collectie, in totaal 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Door <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> uit te voeren, kunnen we zien wat erin zit:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In dit geval is het metadata van een boek zoals gerapporteerd door Z-Library. Op het hoogste niveau hebben we alleen “aacid” en “metadata”, maar geen “data_folder”, aangezien er geen bijbehorende binaire gegevens zijn. De AACID bevat “22430000” als de primaire ID, waarvan we kunnen zien dat deze is overgenomen van “zlibrary_id”. We kunnen verwachten dat andere AAC's in deze collectie dezelfde structuur hebben."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Laten we nu <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> uitvoeren:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dit is een veel kleinere AAC-metadata, hoewel het grootste deel van deze AAC elders in een binair bestand is opgeslagen! We hebben immers deze keer een “data_folder”, dus we kunnen verwachten dat de bijbehorende binaire gegevens zich bevinden op <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. De “metadata” bevat de “zlibrary_id”, zodat we deze gemakkelijk kunnen associëren met de bijbehorende AAC in de “zlib_records” collectie. We hadden het op verschillende manieren kunnen associëren, bijvoorbeeld via AACID — de standaard schrijft dat niet voor."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Merk op dat het ook niet nodig is dat het “metadata” veld zelf JSON is. Het kan een string zijn die XML of een ander gegevensformaat bevat. Je zou zelfs metadata-informatie kunnen opslaan in de bijbehorende binaire blob, bijvoorbeeld als het veel gegevens zijn."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusie"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Met deze standaard kunnen we releases meer incrementeel maken en gemakkelijker nieuwe gegevensbronnen toevoegen. We hebben al een paar spannende releases in de pijplijn!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "We hopen ook dat het voor andere schaduw bibliotheken gemakkelijker wordt om onze collecties te mirroren. Immers, ons doel is om menselijke kennis en cultuur voor altijd te behouden, dus hoe meer redundantie, hoe beter."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna’s Update: volledig open source archief, ElasticSearch, 300GB+ aan boekomslagen"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "We hebben dag en nacht gewerkt om een goed alternatief te bieden met Anna’s Archief. Hier zijn enkele van de dingen die we recentelijk hebben bereikt."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Met Z-Library die offline gaat en de (vermeende) oprichters die gearresteerd worden, hebben we dag en nacht gewerkt om een goed alternatief te bieden met Anna’s Archief (we zullen het hier niet linken, maar je kunt het googelen). Hier zijn enkele van de dingen die we recentelijk hebben bereikt."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna’s Archief is volledig open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Wij geloven dat informatie vrij moet zijn, en onze eigen code is daarop geen uitzondering. We hebben al onze code vrijgegeven op onze privé gehoste Gitlab-instantie: <a %(annas_archive)s>Anna’s Software</a>. We gebruiken ook de issue tracker om ons werk te organiseren. Als je wilt deelnemen aan onze ontwikkeling, is dit een geweldige plek om te beginnen."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Om je een idee te geven van de dingen waar we aan werken, neem ons recente werk aan prestatieverbeteringen aan de clientzijde. Aangezien we nog geen paginering hebben geïmplementeerd, zouden we vaak zeer lange zoekpagina's retourneren, met 100-200 resultaten. We wilden de zoekresultaten niet te snel afkappen, maar dit betekende wel dat het sommige apparaten zou vertragen. Hiervoor hebben we een kleine truc geïmplementeerd: we hebben de meeste zoekresultaten in HTML-opmerkingen gewikkeld (<code><!-- --></code>), en vervolgens een kleine Javascript geschreven die zou detecteren wanneer een resultaat zichtbaar zou moeten worden, op welk moment we de opmerking zouden uitpakken:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisatie\" geïmplementeerd in 23 regels, geen behoefte aan fancy libraries! Dit is het soort snelle pragmatische code dat je krijgt wanneer je beperkte tijd hebt en echte problemen moet oplossen. Er is gerapporteerd dat onze zoekfunctie nu goed werkt op langzame apparaten!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Een andere grote inspanning was het automatiseren van het bouwen van de database. Toen we lanceerden, hebben we gewoon lukraak verschillende bronnen samengevoegd. Nu willen we ze up-to-date houden, dus hebben we een aantal scripts geschreven om nieuwe metadata van de twee Library Genesis forks te downloaden en te integreren. Het doel is niet alleen om dit nuttig te maken voor ons archief, maar om het gemakkelijk te maken voor iedereen die wil experimenteren met schaduw bibliotheek metadata. Het doel zou een Jupyter-notebook zijn dat allerlei interessante metadata beschikbaar heeft, zodat we meer onderzoek kunnen doen, zoals uitzoeken welk <a %(blog)s>percentage van ISBN's voor altijd bewaard blijft</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Ten slotte hebben we ons donatiesysteem vernieuwd. Je kunt nu een creditcard gebruiken om direct geld op onze crypto wallets te storten, zonder echt iets te hoeven weten over cryptocurrencies. We zullen blijven monitoren hoe goed dit in de praktijk werkt, maar dit is een grote stap."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Overstap naar ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Een van onze <a %(annas_archive)s>tickets</a> was een grabbelton van problemen met ons zoeksysteem. We gebruikten MySQL full-text search, aangezien we al onze data toch al in MySQL hadden. Maar het had zijn beperkingen:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Sommige zoekopdrachten duurden super lang, tot het punt waarop ze alle open verbindingen in beslag namen."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Standaard heeft MySQL een minimale woordlengte, of je index kan echt groot worden. Mensen meldden dat ze niet konden zoeken naar “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Zoeken was alleen enigszins snel wanneer volledig in het geheugen geladen, wat vereiste dat we een duurdere machine moesten aanschaffen om dit op te draaien, plus enkele commando's om de index bij het opstarten vooraf te laden."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "We zouden het niet gemakkelijk hebben kunnen uitbreiden om nieuwe functies te bouwen, zoals betere <a %(wikipedia_cjk_characters)s>tokenisatie voor niet-spatiegebruikende talen</a>, filtering/facettering, sorteren, \"bedoelde u\" suggesties, autocompleteren, enzovoort."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Na overleg met een aantal experts hebben we gekozen voor ElasticSearch. Het is niet perfect geweest (hun standaard “bedoelde u” suggesties en autocompleet functies zijn slecht), maar over het algemeen is het veel beter geweest dan MySQL voor zoeken. We zijn nog steeds niet <a %(youtube)s>te enthousiast</a> om het te gebruiken voor enige missie-kritieke gegevens (hoewel ze veel <a %(elastic_co)s>vooruitgang</a> hebben geboekt), maar over het algemeen zijn we best tevreden met de overstap."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Voor nu hebben we een veel snellere zoekfunctie geïmplementeerd, betere taalondersteuning, betere relevantiesortering, verschillende sorteeropties en filtering op taal/boekt type/bestandstype. Als je nieuwsgierig bent hoe het werkt, <a %(annas_archive_l140)s>kijk</a> <a %(annas_archive_l1115)s>eens</a> <a %(annas_archive_l1635)s>hier</a>. Het is redelijk toegankelijk, hoewel het wat meer opmerkingen zou kunnen gebruiken…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ aan boekomslagen vrijgegeven"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Ten slotte zijn we blij een kleine release aan te kondigen. In samenwerking met de mensen die de Libgen.rs fork beheren, delen we al hun boekomslagen via torrents en IPFS. Dit zal de belasting van het bekijken van de omslagen over meer machines verdelen en ze beter behouden. In veel (maar niet alle) gevallen zijn de boekomslagen opgenomen in de bestanden zelf, dus dit is een soort van “afgeleide gegevens”. Maar het hebben in IPFS is nog steeds erg nuttig voor de dagelijkse werking van zowel Anna’s Archief als de verschillende Library Genesis forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Zoals gebruikelijk kun je deze release vinden bij de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>). We zullen hier niet naar linken, maar je kunt het gemakkelijk vinden."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hopelijk kunnen we ons tempo een beetje ontspannen, nu we een fatsoenlijk alternatief voor Z-Library hebben. Deze werklast is niet bijzonder duurzaam. Als je geïnteresseerd bent in het helpen met programmeren, serverbeheer of conserveringswerk, neem dan zeker contact met ons op. Er is nog veel <a %(annas_archive)s>werk te doen</a>. Bedankt voor je interesse en steun."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna’s Archief heeft de grootste schaduw bibliotheek van stripboeken ter wereld geback-upt (95TB) — je kunt helpen deze te seeden"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "De grootste schaduw bibliotheek van stripboeken ter wereld had een enkel storingspunt.. tot vandaag."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Bespreek op Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "De grootste schaduw bibliotheek van stripboeken is waarschijnlijk die van een bepaalde Library Genesis fork: Libgen.li. De enige beheerder die die site runt, slaagde erin een waanzinnige stripboekencollectie van meer dan 2 miljoen bestanden te verzamelen, met een totaal van meer dan 95TB. Echter, in tegenstelling tot andere Library Genesis collecties, was deze niet in bulk beschikbaar via torrents. Je kon deze strips alleen individueel benaderen via zijn langzame persoonlijke server — een enkel storingspunt. Tot vandaag!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In deze post vertellen we je meer over deze collectie en over onze inzamelingsactie om meer van dit werk te ondersteunen."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon probeert zichzelf te verliezen in de alledaagse wereld van de bibliotheek…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen forks"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Eerst wat achtergrondinformatie. Je kent Library Genesis misschien vanwege hun epische boekencollectie. Minder mensen weten dat vrijwilligers van Library Genesis andere projecten hebben gecreëerd, zoals een aanzienlijke collectie tijdschriften en standaarddocumenten, een volledige back-up van Sci-Hub (in samenwerking met de oprichter van Sci-Hub, Alexandra Elbakyan), en inderdaad, een enorme collectie strips."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Op een gegeven moment gingen verschillende beheerders van Library Genesis-mirrors hun eigen weg, wat leidde tot de huidige situatie met een aantal verschillende \"forks\", die allemaal nog steeds de naam Library Genesis dragen. De Libgen.li-fork heeft uniek deze stripverzameling, evenals een aanzienlijke tijdschriftencollectie (waar we ook aan werken)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Samenwerking"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Gezien de omvang stond deze collectie al lang op onze verlanglijst, dus na ons succes met het back-uppen van Z-Library, richtten we onze pijlen op deze collectie. In eerste instantie schraapten we het direct, wat een behoorlijke uitdaging was, aangezien hun server niet in de beste staat was. Op deze manier kregen we ongeveer 15TB, maar het ging langzaam."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Gelukkig slaagden we erin om in contact te komen met de beheerder van de bibliotheek, die ermee instemde om ons alle gegevens direct te sturen, wat veel sneller ging. Het duurde nog steeds meer dan een half jaar om alle gegevens over te dragen en te verwerken, en we verloren bijna alles door schijfcorruptie, wat zou hebben betekend dat we helemaal opnieuw moesten beginnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Deze ervaring heeft ons doen geloven dat het belangrijk is om deze gegevens zo snel mogelijk te verspreiden, zodat ze wijd en zijd gespiegeld kunnen worden. We zijn slechts één of twee ongelukkig getimede incidenten verwijderd van het voor altijd verliezen van deze collectie!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "De collectie"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Snel handelen betekent wel dat de collectie een beetje ongeorganiseerd is… Laten we eens kijken. Stel je voor dat we een bestandssysteem hebben (dat we in werkelijkheid over torrents verdelen):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "De eerste directory, <code>/repository</code>, is het meer gestructureerde deel hiervan. Deze directory bevat zogenaamde \"duizend mappen\": mappen elk met duizend bestanden, die incrementeel genummerd zijn in de database. Directory <code>0</code> bevat bestanden met comic_id 0–999, enzovoort."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dit is hetzelfde schema dat Library Genesis gebruikt voor zijn fictie- en non-fictiecollecties. Het idee is dat elke \"duizend map\" automatisch wordt omgezet in een torrent zodra deze vol is."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Echter, de Libgen.li operator heeft nooit torrents gemaakt voor deze collectie, en dus werden de duizend mappen waarschijnlijk onhandig, en maakten plaats voor “ongesorteerde mappen”. Dit zijn <code>/comics0</code> tot <code>/comics4</code>. Ze bevatten allemaal unieke directorystructuren, die waarschijnlijk logisch waren voor het verzamelen van de bestanden, maar nu niet veel zin meer hebben voor ons. Gelukkig verwijst de metadata nog steeds direct naar al deze bestanden, dus hun opslagorganisatie op schijf doet er eigenlijk niet toe!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "De metadata is beschikbaar in de vorm van een MySQL-database. Deze kan direct worden gedownload van de Libgen.li-website, maar we zullen het ook beschikbaar maken in een torrent, naast onze eigen tabel met alle MD5-hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Wanneer je 95TB in je opslagcluster gedumpt krijgt, probeer je te begrijpen wat er überhaupt in zit… We hebben wat analyses gedaan om te zien of we de grootte een beetje konden verminderen, bijvoorbeeld door duplicaten te verwijderen. Hier zijn enkele van onze bevindingen:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantische duplicaten (verschillende scans van hetzelfde boek) kunnen theoretisch worden uitgefilterd, maar het is lastig. Bij het handmatig doorzoeken van de strips vonden we te veel valse positieven."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Er zijn enkele duplicaten puur op basis van MD5, wat relatief verspilling is, maar het filteren daarvan zou ons slechts ongeveer 1% in besparing opleveren. Op deze schaal is dat nog steeds ongeveer 1TB, maar ook, op deze schaal maakt 1TB niet echt uit. We willen liever niet het risico lopen om per ongeluk gegevens te vernietigen in dit proces."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "We vonden een hoop niet-boekgegevens, zoals films gebaseerd op stripboeken. Dat lijkt ook verspilling, aangezien deze al op andere manieren breed beschikbaar zijn. We realiseerden ons echter dat we filmbestanden niet zomaar konden filteren, omdat er ook <em>interactieve stripboeken</em> zijn die op de computer zijn uitgebracht, die iemand heeft opgenomen en als films heeft opgeslagen."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Uiteindelijk zou alles wat we uit de collectie zouden kunnen verwijderen slechts een paar procent besparen. Toen herinnerden we ons dat we datahoarders zijn, en de mensen die dit zullen spiegelen zijn ook datahoarders, en dus, “WAT BEDOEL JE, VERWIJDEREN?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "We presenteren u daarom de volledige, ongewijzigde collectie. Het is veel data, maar we hopen dat genoeg mensen het toch zullen willen seeden."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Fondsenwerving"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "We brengen deze data in enkele grote stukken uit. De eerste torrent is van <code>/comics0</code>, die we in één enorme 12TB .tar-bestand hebben gestopt. Dat is beter voor je harde schijf en torrentsoftware dan een ontelbaar aantal kleinere bestanden."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Als onderdeel van deze release houden we een fondsenwerving. We willen $20.000 inzamelen om de operationele en contractkosten voor deze collectie te dekken, evenals om lopende en toekomstige projecten mogelijk te maken. We hebben enkele <em>enorme</em> projecten in de maak."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Wie steun ik met mijn donatie?</em> In het kort: we maken een back-up van alle kennis en cultuur van de mensheid en maken het gemakkelijk toegankelijk. Al onze code en data zijn open source, we zijn een volledig door vrijwilligers gerund project, en we hebben tot nu toe 125TB aan boeken gered (naast de bestaande torrents van Libgen en Scihub). Uiteindelijk bouwen we een vliegwiel dat mensen in staat stelt en stimuleert om alle boeken ter wereld te vinden, te scannen en te back-uppen. We zullen in een toekomstige post over ons meesterplan schrijven. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Als je doneert voor een 12 maanden “Amazing Archivist” lidmaatschap ($780), kun je <strong>“een torrent adopteren”</strong>, wat betekent dat we je gebruikersnaam of bericht in de bestandsnaam van een van de torrents zullen plaatsen!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Je kunt doneren door naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a> te gaan en op de knop “Doneer” te klikken. We zijn ook op zoek naar meer vrijwilligers: software-ingenieurs, beveiligingsonderzoekers, experts in anonieme handel en vertalers. Je kunt ons ook steunen door hostingdiensten te bieden. En natuurlijk, seed onze torrents alstublieft!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dank aan iedereen die ons al zo genereus heeft gesteund! Jullie maken echt een verschil."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hier zijn de torrents die tot nu toe zijn uitgebracht (we verwerken de rest nog):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alle torrents zijn te vinden op <a %(wikipedia_annas_archive)s>Anna’s Archief</a> onder “Datasets” (we linken daar niet direct naartoe, zodat links naar deze blog niet van Reddit, Twitter, etc. worden verwijderd). Van daaruit volg je de link naar de Tor-website."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wat is de volgende stap?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Een hoop torrents zijn geweldig voor langdurige bewaring, maar niet zozeer voor dagelijks gebruik. We zullen samenwerken met hostingpartners om al deze data op het web te krijgen (aangezien Anna’s Archief zelf niets host). Natuurlijk kun je deze downloadlinks vinden op Anna’s Archief."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "We nodigen iedereen ook uit om iets met deze data te doen! Help ons het beter te analyseren, dedupliceren, op IPFS te zetten, het te remixen, je AI-modellen ermee te trainen, enzovoort. Het is allemaal van jou, en we kunnen niet wachten om te zien wat je ermee doet."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Tenslotte, zoals eerder gezegd, hebben we nog enkele enorme releases in de planning (als <em>iemand</em> ons per <em>ongeluk</em> een dump van een <em>bepaalde</em> ACS4-database zou kunnen sturen, weet je waar je ons kunt vinden...), evenals het bouwen van het vliegwiel voor het back-uppen van alle boeken ter wereld."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Dus blijf op de hoogte, we zijn nog maar net begonnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nieuwe boeken toegevoegd aan de Pirate Library Mirror (+24TB, 3,8 miljoen boeken)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In de oorspronkelijke release van de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>), maakten we een mirror van Z-Library, een grote illegale boekencollectie. Ter herinnering, dit is wat we schreven in die oorspronkelijke blogpost:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library is een populaire (en illegale) bibliotheek. Ze hebben de Library Genesis-collectie genomen en gemakkelijk doorzoekbaar gemaakt. Bovendien zijn ze zeer effectief geworden in het werven van nieuwe boekbijdragen, door bijdragende gebruikers te belonen met verschillende voordelen. Ze dragen deze nieuwe boeken momenteel niet terug bij aan Library Genesis. En in tegenstelling tot Library Genesis, maken ze hun collectie niet gemakkelijk mirrorbaar, wat brede bewaring verhindert. Dit is belangrijk voor hun bedrijfsmodel, aangezien ze geld vragen voor toegang tot hun collectie in bulk (meer dan 10 boeken per dag)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "We vellen geen moreel oordeel over het vragen van geld voor bulktoegang tot een illegale boekencollectie. Het staat buiten kijf dat de Z-Library succesvol is geweest in het vergroten van de toegang tot kennis en het verkrijgen van meer boeken. Wij zijn hier simpelweg om ons deel te doen: het waarborgen van de langetermijnbewaring van deze privécollectie."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Die collectie dateerde van midden 2021. In de tussentijd is de Z-Library in een verbluffend tempo gegroeid: ze hebben ongeveer 3,8 miljoen nieuwe boeken toegevoegd. Er zitten zeker wat duplicaten tussen, maar het merendeel lijkt legitiem nieuwe boeken te zijn, of scans van hogere kwaliteit van eerder ingediende boeken. Dit is grotendeels te danken aan het toegenomen aantal vrijwillige moderatoren bij de Z-Library en hun bulk-upload systeem met deduplicatie. We willen hen feliciteren met deze prestaties."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "We zijn blij aan te kondigen dat we alle boeken hebben verkregen die aan de Z-Library zijn toegevoegd tussen onze laatste mirror en augustus 2022. We zijn ook teruggegaan en hebben enkele boeken verzameld die we de eerste keer hebben gemist. Al met al is deze nieuwe collectie ongeveer 24TB, wat veel groter is dan de vorige (7TB). Onze mirror is nu in totaal 31TB. We hebben opnieuw gededupliceerd tegen Library Genesis, aangezien er al torrents beschikbaar zijn voor die collectie."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Ga alstublieft naar de Pirate Library Mirror om de nieuwe collectie te bekijken (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>). Daar is meer informatie over hoe de bestanden zijn gestructureerd en wat er sinds de laatste keer is veranderd. We zullen er hier niet naar linken, aangezien dit slechts een blogwebsite is die geen illegale materialen host."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Natuurlijk is seeden ook een geweldige manier om ons te helpen. Bedankt aan iedereen die onze vorige set torrents seedt. We zijn dankbaar voor de positieve respons en blij dat er zoveel mensen zijn die op deze ongewone manier geven om het behoud van kennis en cultuur."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Hoe word je een piratenarchivaris"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "De eerste uitdaging kan een verrassende zijn. Het is geen technisch probleem, of een juridisch probleem. Het is een psychologisch probleem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Voordat we beginnen, twee updates over de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "We hebben enkele uiterst genereuze donaties ontvangen. De eerste was $10k van de anonieme persoon die ook \"bookwarrior\", de oorspronkelijke oprichter van Library Genesis, heeft gesteund. Speciale dank aan bookwarrior voor het faciliteren van deze donatie. De tweede was nog eens $10k van een anonieme donor, die contact met ons opnam na onze laatste release en geïnspireerd was om te helpen. We hebben ook een aantal kleinere donaties ontvangen. Heel erg bedankt voor al jullie genereuze steun. We hebben enkele spannende nieuwe projecten in de pijplijn die hierdoor ondersteund zullen worden, dus blijf op de hoogte."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "We hadden enkele technische problemen met de grootte van onze tweede release, maar onze torrents zijn nu online en worden geseed. We kregen ook een genereus aanbod van een anonieme persoon om onze collectie te seeden op hun zeer snelle servers, dus we doen een speciale upload naar hun machines, waarna iedereen die de collectie downloadt een grote verbetering in snelheid zou moeten zien."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Er kunnen hele boeken worden geschreven over het <em>waarom</em> van digitale bewaring in het algemeen, en piratenarchivisme in het bijzonder, maar laten we een korte inleiding geven voor degenen die er niet zo bekend mee zijn. De wereld produceert meer kennis en cultuur dan ooit tevoren, maar er gaat ook meer verloren dan ooit tevoren. De mensheid vertrouwt grotendeels op bedrijven zoals academische uitgevers, streamingdiensten en sociale mediabedrijven voor dit erfgoed, en zij hebben zich vaak niet bewezen als geweldige beheerders. Bekijk de documentaire Digital Amnesia, of echt elke lezing van Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Er zijn enkele instellingen die goed werk leveren door zoveel mogelijk te archiveren, maar zij zijn gebonden aan de wet. Als piraten bevinden we ons in een unieke positie om collecties te archiveren die zij niet kunnen aanraken, vanwege handhaving van auteursrechten of andere beperkingen. We kunnen ook collecties vele malen over de hele wereld spiegelen, waardoor de kans op juiste bewaring toeneemt."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Voor nu gaan we niet in op de voor- en nadelen van intellectueel eigendom, de moraliteit van het overtreden van de wet, overpeinzingen over censuur, of de kwestie van toegang tot kennis en cultuur. Met dat allemaal uit de weg, laten we duiken in de <em>hoe</em>. We zullen delen hoe ons team piratenarchivarissen werd, en de lessen die we onderweg hebben geleerd. Er zijn veel uitdagingen wanneer je aan deze reis begint, en hopelijk kunnen we je door enkele daarvan helpen."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Gemeenschap"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "De eerste uitdaging kan een verrassende zijn. Het is geen technisch probleem, of een juridisch probleem. Het is een psychologisch probleem: dit werk in de schaduw doen kan ongelooflijk eenzaam zijn. Afhankelijk van wat je van plan bent te doen, en je dreigingsmodel, moet je misschien heel voorzichtig zijn. Aan de ene kant van het spectrum hebben we mensen zoals Alexandra Elbakyan*, de oprichter van Sci-Hub, die heel open is over haar activiteiten. Maar ze loopt een groot risico gearresteerd te worden als ze op dit moment een westers land zou bezoeken, en zou tientallen jaren gevangenisstraf kunnen krijgen. Is dat een risico dat je bereid bent te nemen? Wij bevinden ons aan de andere kant van het spectrum; we zijn heel voorzichtig om geen spoor achter te laten en hebben een sterke operationele beveiliging."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Zoals vermeld op HN door \"ynno\", wilde Alexandra aanvankelijk niet bekend zijn: \"Haar servers waren ingesteld om gedetailleerde foutmeldingen van PHP uit te zenden, inclusief het volledige pad van het foutieve bronbestand, dat zich onder de directory /home/<USER>" Dus, gebruik willekeurige gebruikersnamen op de computers die je voor dit soort dingen gebruikt, voor het geval je iets verkeerd configureert."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Die geheimhouding komt echter met een psychologische prijs. De meeste mensen houden ervan erkend te worden voor het werk dat ze doen, en toch kun je hier in het echte leven geen eer voor krijgen. Zelfs eenvoudige dingen kunnen uitdagend zijn, zoals vrienden die je vragen wat je hebt gedaan (op een gegeven moment wordt \"rommelen met mijn NAS / homelab\" oud)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Daarom is het zo belangrijk om een gemeenschap te vinden. Je kunt wat operationele beveiliging opgeven door in vertrouwen te nemen bij enkele zeer goede vrienden, van wie je weet dat je ze diep kunt vertrouwen. Zelfs dan moet je oppassen om niets op schrift te stellen, voor het geval ze hun e-mails aan de autoriteiten moeten overhandigen, of als hun apparaten op een andere manier zijn gecompromitteerd."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Beter nog is het om enkele mede-piraten te vinden. Als je goede vrienden geïnteresseerd zijn om mee te doen, geweldig! Anders kun je misschien anderen online vinden. Helaas is dit nog steeds een nichegemeenschap. Tot nu toe hebben we slechts een handvol anderen gevonden die actief zijn in deze ruimte. Goede startpunten lijken de Library Genesis-forums en r/DataHoarder te zijn. Het Archive Team heeft ook gelijkgestemde individuen, hoewel ze binnen de wet opereren (zelfs als dat in enkele grijze gebieden van de wet is). De traditionele \"warez\" en piraterijscènes hebben ook mensen die op vergelijkbare manieren denken."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "We staan open voor ideeën over hoe we gemeenschap kunnen bevorderen en ideeën kunnen verkennen. Voel je vrij om ons een bericht te sturen op Twitter of Reddit. Misschien kunnen we een soort forum of chatgroep hosten. Een uitdaging is dat dit gemakkelijk gecensureerd kan worden bij het gebruik van gangbare platforms, dus we zouden het zelf moeten hosten. Er is ook een afweging tussen het volledig openbaar maken van deze discussies (meer potentiële betrokkenheid) versus het privé maken (niet laten weten aan potentiële \"doelen\" dat we op het punt staan ze te scrapen). We zullen daarover moeten nadenken. Laat ons weten of je hierin geïnteresseerd bent!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projecten"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Wanneer we een project doen, heeft het een paar fasen:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domeinselectie / filosofie: Waar wil je je ongeveer op richten, en waarom? Wat zijn je unieke passies, vaardigheden en omstandigheden die je in je voordeel kunt gebruiken?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Doelselectie: Welke specifieke collectie ga je mirroren?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata scraping: Informatie over de bestanden catalogiseren, zonder de (vaak veel grotere) bestanden zelf te downloaden."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Gegevensselectie: Op basis van de metadata bepalen welke gegevens op dit moment het meest relevant zijn om te archiveren. Het kan alles zijn, maar vaak is er een redelijke manier om ruimte en bandbreedte te besparen."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Gegevens scraping: De gegevens daadwerkelijk verkrijgen."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distributie: Het verpakken in torrents, het ergens aankondigen, mensen zover krijgen het te verspreiden."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Dit zijn niet volledig onafhankelijke fasen, en vaak sturen inzichten uit een latere fase je terug naar een eerdere fase. Bijvoorbeeld, tijdens metadata scraping kun je je realiseren dat het doel dat je hebt geselecteerd verdedigingsmechanismen heeft die je vaardigheidsniveau te boven gaan (zoals IP-blokkades), dus ga je terug en zoek je een ander doel."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domeinselectie / filosofie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Er is geen tekort aan kennis en cultureel erfgoed om te redden, wat overweldigend kan zijn. Daarom is het vaak nuttig om even de tijd te nemen en na te denken over wat jouw bijdrage kan zijn."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Iedereen heeft een andere manier van denken hierover, maar hier zijn enkele vragen die je jezelf zou kunnen stellen:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Waarom bent u hierin geïnteresseerd? Waar bent u gepassioneerd over? Als we een groep mensen kunnen verzamelen die allemaal de soorten dingen archiveren waar ze specifiek om geven, zou dat veel dekken! U zult veel meer weten dan de gemiddelde persoon over uw passie, zoals welke belangrijke data bewaard moeten worden, wat de beste collecties en online gemeenschappen zijn, enzovoort."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Welke vaardigheden heeft u die u in uw voordeel kunt gebruiken? Bijvoorbeeld, als u een online beveiligingsexpert bent, kunt u manieren vinden om IP-blokkades voor beveiligde doelen te omzeilen. Als u goed bent in het organiseren van gemeenschappen, dan kunt u misschien mensen samenbrengen rond een doel. Het is nuttig om enige programmeerkennis te hebben, al is het maar om goede operationele beveiliging te behouden gedurende dit proces."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hoeveel tijd heeft u hiervoor? Ons advies zou zijn om klein te beginnen en grotere projecten te doen naarmate u er meer ervaring mee krijgt, maar het kan allesomvattend worden."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wat zou een gebied met hoge impact zijn om op te focussen? Als u X uren gaat besteden aan piratenarchivering, hoe kunt u dan de grootste \"waarde voor uw tijd\" krijgen?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wat zijn unieke manieren waarop u hierover denkt? U heeft misschien interessante ideeën of benaderingen die anderen hebben gemist."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In ons geval gaven we vooral om de langdurige bewaring van wetenschap. We wisten van Library Genesis, en hoe het vele malen volledig werd gespiegeld met torrents. We hielden van dat idee. Toen probeerde een van ons op een dag enkele wetenschappelijke leerboeken te vinden op Library Genesis, maar kon ze niet vinden, wat twijfel zaaide over hoe compleet het echt was. We zochten die leerboeken toen online en vonden ze op andere plaatsen, wat de kiem legde voor ons project. Zelfs voordat we van de Z-Library wisten, hadden we het idee om niet te proberen al die boeken handmatig te verzamelen, maar ons te richten op het spiegelen van bestaande collecties en ze terug te geven aan Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Doelselectie"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Dus, we hebben ons gebied dat we bekijken, welke specifieke collectie spiegelen we nu? Er zijn een paar dingen die een goed doelwit maken:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Groot"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Uniek: niet al goed gedekt door andere projecten."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Toegankelijk: gebruikt niet veel lagen van bescherming om te voorkomen dat je hun metadata en data kunt scrapen."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Speciale inzichten: je hebt speciale informatie over dit doelwit, zoals speciale toegang tot deze collectie, of je hebt ontdekt hoe je hun verdedigingen kunt omzeilen. Dit is niet vereist (ons aankomende project doet niets bijzonders), maar het helpt zeker!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Toen we onze wetenschappelijke leerboeken op andere websites dan Anna’s Archief vonden, probeerden we te achterhalen hoe ze op het internet terechtkwamen. We ontdekten toen de Z-Library en realiseerden ons dat, hoewel de meeste boeken daar niet als eerste verschijnen, ze er uiteindelijk wel terechtkomen. We leerden over de relatie met Anna’s Archief en de (financiële) prikkelstructuur en superieure gebruikersinterface, die het tot een veel completere collectie maakten. We deden vervolgens wat voorlopige metadata- en datascraping en realiseerden ons dat we hun IP-downloadlimieten konden omzeilen door gebruik te maken van de speciale toegang van een van onze leden tot veel proxyservers."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Terwijl je verschillende doelen verkent, is het al belangrijk om je sporen te verbergen door VPN's en wegwerp-e-mailadressen te gebruiken, waar we later meer over zullen praten."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata-scraping"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Laten we hier wat technischer worden. Voor het daadwerkelijk scrapen van de metadata van websites hebben we het vrij eenvoudig gehouden. We gebruiken Python-scripts, soms curl, en een MySQL-database om de resultaten in op te slaan. We hebben geen geavanceerde scraping-software gebruikt die complexe websites kan in kaart brengen, aangezien we tot nu toe alleen één of twee soorten pagina's hoefden te scrapen door simpelweg door id's te enumereren en de HTML te parseren. Als er geen gemakkelijk te enumereren pagina's zijn, heb je misschien een goede crawler nodig die probeert alle pagina's te vinden."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Voordat je een hele website gaat scrapen, probeer het eerst handmatig een beetje. Ga zelf door een paar dozijn pagina's om een gevoel te krijgen voor hoe dat werkt. Soms kom je op deze manier al IP-blokkades of ander interessant gedrag tegen. Hetzelfde geldt voor datascraping: voordat je te diep in dit doelwit duikt, zorg ervoor dat je de data daadwerkelijk effectief kunt downloaden."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Om beperkingen te omzeilen, zijn er een paar dingen die je kunt proberen. Zijn er andere IP-adressen of servers die dezelfde data hosten maar niet dezelfde beperkingen hebben? Zijn er API-eindpunten die geen beperkingen hebben, terwijl anderen dat wel hebben? Bij welke downloadsnelheid wordt je IP geblokkeerd, en hoe lang? Of word je niet geblokkeerd maar vertraagd? Wat als je een gebruikersaccount aanmaakt, hoe veranderen de zaken dan? Kun je HTTP/2 gebruiken om verbindingen open te houden, en verhoogt dat de snelheid waarmee je pagina's kunt aanvragen? Zijn er pagina's die meerdere bestanden tegelijk vermelden, en is de daar vermelde informatie voldoende?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dingen die je waarschijnlijk wilt opslaan zijn:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Bestandsnaam / locatie"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kan een interne ID zijn, maar ID's zoals ISBN of DOI zijn ook nuttig."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Grootte: om te berekenen hoeveel schijfruimte je nodig hebt."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): om te bevestigen dat je het bestand correct hebt gedownload."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum toegevoegd/aangepast: zodat je later kunt terugkomen en bestanden kunt downloaden die je eerder niet hebt gedownload (hoewel je hiervoor vaak ook de ID of hash kunt gebruiken)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beschrijving, categorie, tags, auteurs, taal, enz."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "We doen dit meestal in twee fasen. Eerst downloaden we de ruwe HTML-bestanden, meestal direct in MySQL (om veel kleine bestanden te vermijden, waar we hieronder meer over praten). Vervolgens, in een aparte stap, gaan we door die HTML-bestanden en parseren ze in daadwerkelijke MySQL-tabellen. Op deze manier hoef je niet alles opnieuw te downloaden als je een fout in je parseringscode ontdekt, omdat je de HTML-bestanden gewoon opnieuw kunt verwerken met de nieuwe code. Het is ook vaak gemakkelijker om de verwerkingsstap te paralleliseren, waardoor je wat tijd bespaart (en je kunt de verwerkingscode schrijven terwijl de scraping loopt, in plaats van beide stappen tegelijk te moeten schrijven)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Ten slotte, merk op dat voor sommige doelen metadata scraping alles is wat er is. Er zijn enkele enorme metadata collecties die niet goed bewaard zijn gebleven."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Gegevensselectie"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Vaak kun je de metadata gebruiken om een redelijk deel van de gegevens te bepalen om te downloaden. Zelfs als je uiteindelijk alle gegevens wilt downloaden, kan het nuttig zijn om de belangrijkste items eerst te prioriteren, voor het geval je wordt gedetecteerd en de verdedigingen worden verbeterd, of omdat je meer schijven zou moeten kopen, of simpelweg omdat er iets anders in je leven opduikt voordat je alles kunt downloaden."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Een collectie kan bijvoorbeeld meerdere edities van dezelfde onderliggende bron hebben (zoals een boek of een film), waarbij één is gemarkeerd als de beste kwaliteit. Het zou heel logisch zijn om die edities eerst op te slaan. Uiteindelijk wil je misschien alle edities opslaan, aangezien in sommige gevallen de metadata mogelijk onjuist is getagd, of er onbekende afwegingen zijn tussen edities (bijvoorbeeld, de \"beste editie\" kan in de meeste opzichten het beste zijn, maar in andere opzichten slechter, zoals een film met een hogere resolutie maar zonder ondertitels)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Je kunt ook je metadata database doorzoeken om interessante dingen te vinden. Wat is het grootste bestand dat wordt gehost, en waarom is het zo groot? Wat is het kleinste bestand? Zijn er interessante of onverwachte patronen als het gaat om bepaalde categorieën, talen, enzovoort? Zijn er dubbele of zeer vergelijkbare titels? Zijn er patronen in wanneer gegevens zijn toegevoegd, zoals een dag waarop veel bestanden tegelijk zijn toegevoegd? Je kunt vaak veel leren door op verschillende manieren naar de dataset te kijken."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In ons geval hebben we Z-Library boeken gededupliceerd tegen de md5 hashes in Library Genesis, waardoor we veel downloadtijd en schijfruimte hebben bespaard. Dit is echter een vrij unieke situatie. In de meeste gevallen zijn er geen uitgebreide databases van welke bestanden al goed bewaard zijn door mede-piraten. Dit is op zichzelf een enorme kans voor iemand daarbuiten. Het zou geweldig zijn om een regelmatig bijgewerkt overzicht te hebben van dingen zoals muziek en films die al veelvuldig worden gedeeld op torrent websites, en daarom een lagere prioriteit hebben om op te nemen in piraten mirrors."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Gegevens scraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nu ben je klaar om de gegevens daadwerkelijk in bulk te downloaden. Zoals eerder vermeld, zou je op dit punt al handmatig een aantal bestanden hebben gedownload, om het gedrag en de beperkingen van het doel beter te begrijpen. Er zullen echter nog steeds verrassingen voor je in petto zijn zodra je daadwerkelijk veel bestanden tegelijk gaat downloaden."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Ons advies hier is vooral om het simpel te houden. Begin gewoon met het downloaden van een aantal bestanden. Je kunt Python gebruiken, en dan uitbreiden naar meerdere threads. Maar soms is het zelfs eenvoudiger om Bash-bestanden direct vanuit de database te genereren, en vervolgens meerdere daarvan in meerdere terminalvensters uit te voeren om op te schalen. Een snelle technische truc die het vermelden waard is, is het gebruik van OUTFILE in MySQL, die je overal kunt schrijven als je \"secure_file_priv\" uitschakelt in mysqld.cnf (en zorg ervoor dat je ook AppArmor uitschakelt/overruled als je op Linux zit)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "We slaan de gegevens op eenvoudige harde schijven op. Begin met wat je hebt, en breid langzaam uit. Het kan overweldigend zijn om na te denken over het opslaan van honderden TB's aan gegevens. Als dat de situatie is waarin je je bevindt, zet dan eerst een goed deel uit, en vraag in je aankondiging om hulp bij het opslaan van de rest. Als je zelf meer harde schijven wilt aanschaffen, dan heeft r/DataHoarder enkele goede bronnen om goede deals te krijgen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Probeer je niet te veel zorgen te maken over geavanceerde bestandssystemen. Het is gemakkelijk om in het konijnenhol te vallen van het opzetten van dingen zoals ZFS. Een technisch detail om je bewust van te zijn, is dat veel bestandssystemen niet goed omgaan met veel bestanden. We hebben ontdekt dat een eenvoudige oplossing is om meerdere directories te maken, bijvoorbeeld voor verschillende ID-bereiken of hash-prefixen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Na het downloaden van de gegevens, zorg ervoor dat je de integriteit van de bestanden controleert met behulp van hashes in de metadata, indien beschikbaar."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distributie"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Je hebt de gegevens, waardoor je waarschijnlijk de eerste piraten mirror van je doel in handen hebt. In veel opzichten is het moeilijkste deel voorbij, maar het risicovolste deel ligt nog voor je. Tot nu toe ben je immers onopgemerkt gebleven; onder de radar gevlogen. Alles wat je hoefde te doen was een goede VPN gebruiken, je persoonlijke gegevens niet invullen in formulieren (duh), en misschien een speciale browsersessie gebruiken (of zelfs een andere computer)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nu moet je de gegevens distribueren. In ons geval wilden we eerst de boeken terug bijdragen aan Library Genesis, maar ontdekten toen snel de moeilijkheden daarin (fictie versus non-fictie sortering). Dus besloten we tot distributie via Library Genesis-stijl torrents. Als je de kans hebt om bij te dragen aan een bestaand project, dan kan dat je veel tijd besparen. Er zijn echter momenteel niet veel goed georganiseerde piraten mirrors."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Stel dat je besluit om zelf torrents te distribueren. Probeer die bestanden klein te houden, zodat ze gemakkelijk op andere websites kunnen worden gemirrord. Je zult dan zelf de torrents moeten seeden, terwijl je anoniem blijft. Je kunt een VPN gebruiken (met of zonder port forwarding), of betalen met gewassen Bitcoins voor een Seedbox. Als je niet weet wat sommige van die termen betekenen, heb je nog veel te lezen, want het is belangrijk dat je de risico-afwegingen hier begrijpt."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Je kunt de torrentbestanden zelf hosten op bestaande torrent websites. In ons geval kozen we ervoor om daadwerkelijk een website te hosten, omdat we ook onze filosofie op een duidelijke manier wilden verspreiden. Je kunt dit zelf op een vergelijkbare manier doen (wij gebruiken Njalla voor onze domeinen en hosting, betaald met gewassen Bitcoins), maar voel je ook vrij om contact met ons op te nemen om ons je torrents te laten hosten. We willen in de loop van de tijd een uitgebreide index van piraten mirrors opbouwen, als dit idee aanslaat."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Wat betreft de keuze van een VPN, er is al veel over geschreven, dus we herhalen gewoon het algemene advies om te kiezen op basis van reputatie. Werkelijk door de rechtbank geteste no-log beleid met lange staat van dienst in het beschermen van privacy is volgens ons de optie met het laagste risico. Merk op dat zelfs als je alles goed doet, je nooit tot nul risico kunt komen. Bijvoorbeeld, bij het seeden van je torrents kan een zeer gemotiveerde actor van een natiestaat waarschijnlijk kijken naar inkomende en uitgaande datastromen voor VPN-servers, en afleiden wie je bent. Of je kunt gewoon ergens een fout maken. Wij hebben dat waarschijnlijk al gedaan, en zullen het opnieuw doen. Gelukkig geven natiestaten niet <em>zoveel</em> om piraterij."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Een beslissing die je voor elk project moet nemen, is of je het onder dezelfde identiteit als voorheen publiceert, of niet. Als je dezelfde naam blijft gebruiken, kunnen fouten in operationele beveiliging van eerdere projecten je achtervolgen. Maar publiceren onder verschillende namen betekent dat je geen langdurige reputatie opbouwt. Wij kozen ervoor om vanaf het begin sterke operationele beveiliging te hebben, zodat we dezelfde identiteit kunnen blijven gebruiken, maar we zullen niet aarzelen om onder een andere naam te publiceren als we een fout maken of als de omstandigheden daarom vragen."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Het woord verspreiden kan lastig zijn. Zoals we al zeiden, is dit nog steeds een nichegemeenschap. We hebben oorspronkelijk op Reddit gepost, maar kregen echt tractie op Hacker News. Voor nu is onze aanbeveling om het op een paar plaatsen te posten en te zien wat er gebeurt. En nogmaals, neem contact met ons op. We zouden graag het woord verspreiden over meer piratenarchivisme-inspanningen."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusie"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hopelijk is dit nuttig voor nieuw beginnende piratenarchivarissen. We zijn verheugd u in deze wereld te verwelkomen, dus aarzel niet om contact op te nemen. Laten we zoveel mogelijk van de kennis en cultuur van de wereld behouden en het wijd en zijd spiegelen."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introductie van de Piratenbibliotheekspiegel: Behoud van 7TB aan boeken (die niet in Libgen staan)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dit project (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>) heeft als doel bij te dragen aan het behoud en de bevrijding van menselijke kennis. We leveren onze kleine en bescheiden bijdrage, in de voetsporen van de groten die ons voorgingen."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "De focus van dit project wordt geïllustreerd door zijn naam:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piraat</strong> - We overtreden opzettelijk de auteurswet in de meeste landen. Dit stelt ons in staat iets te doen wat legale entiteiten niet kunnen: ervoor zorgen dat boeken wijd en zijd worden gespiegeld."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotheek</strong> - Net als de meeste bibliotheken richten we ons voornamelijk op geschreven materialen zoals boeken. We kunnen in de toekomst uitbreiden naar andere soorten media."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spiegel</strong> - We zijn strikt een spiegel van bestaande bibliotheken. We richten ons op behoud, niet op het gemakkelijk doorzoekbaar en downloadbaar maken van boeken (toegang) of het bevorderen van een grote gemeenschap van mensen die nieuwe boeken bijdragen (bron)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "De eerste bibliotheek die we hebben gespiegeld is Z-Library. Dit is een populaire (en illegale) bibliotheek. Ze hebben de Library Genesis-collectie genomen en deze gemakkelijk doorzoekbaar gemaakt. Bovendien zijn ze zeer effectief geworden in het werven van nieuwe boekbijdragen, door bijdragende gebruikers te belonen met verschillende voordelen. Ze dragen deze nieuwe boeken momenteel niet terug bij aan Library Genesis. En in tegenstelling tot Library Genesis maken ze hun collectie niet gemakkelijk spiegelbaar, wat wijdverspreid behoud verhindert. Dit is belangrijk voor hun bedrijfsmodel, aangezien ze geld vragen voor toegang tot hun collectie in bulk (meer dan 10 boeken per dag)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "We vellen geen moreel oordeel over het vragen van geld voor bulktoegang tot een illegale boekencollectie. Het staat buiten kijf dat de Z-Library succesvol is geweest in het vergroten van de toegang tot kennis en het verkrijgen van meer boeken. Wij zijn hier simpelweg om ons deel te doen: het waarborgen van de langetermijnbewaring van deze privécollectie."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "We willen u uitnodigen om te helpen bij het behouden en bevrijden van menselijke kennis door onze torrents te downloaden en te seeden. Zie de projectpagina voor meer informatie over hoe de data is georganiseerd."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "We nodigen u ook van harte uit om uw ideeën bij te dragen over welke collecties we als volgende moeten spiegelen en hoe we dat moeten aanpakken. Samen kunnen we veel bereiken. Dit is slechts een kleine bijdrage te midden van talloze anderen. Dank u, voor alles wat u doet."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>We linken niet naar de bestanden vanaf deze blog. Zoek het zelf.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, of Hoeveel Boeken Worden Voor Altijd Behouden?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Als we de bestanden van schaduwbibliotheken goed zouden dedupliceren, welk percentage van alle boeken in de wereld hebben we dan behouden?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Met de Piratenbibliotheekspiegel (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>), is ons doel om alle boeken in de wereld te nemen en ze voor altijd te behouden.<sup>1</sup> Tussen onze Z-Library torrents en de originele Library Genesis torrents hebben we 11.783.153 bestanden. Maar hoeveel is dat echt? Als we die bestanden goed zouden dedupliceren, welk percentage van alle boeken in de wereld hebben we dan behouden? We zouden echt graag zoiets willen hebben:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of het geschreven erfgoed van de mensheid voor altijd behouden"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Voor een percentage hebben we een noemer nodig: het totale aantal boeken dat ooit is gepubliceerd.<sup>2</sup> Voor de ondergang van Google Books probeerde een ingenieur van het project, Leonid Taycher, <a %(booksearch_blogspot)s>dit aantal te schatten</a>. Hij kwam — met een knipoog — uit op 129.864.880 (“tenminste tot zondag”). Hij schatte dit aantal door een verenigde database van alle boeken in de wereld te bouwen. Hiervoor verzamelde hij verschillende datasets en voegde deze op verschillende manieren samen."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Als een korte terzijde is er nog een persoon die probeerde alle boeken in de wereld te catalogiseren: Aaron Swartz, de overleden digitale activist en mede-oprichter van Reddit.<sup>3</sup> Hij <a %(youtube)s>startte Open Library</a> met als doel “één webpagina voor elk boek dat ooit is gepubliceerd”, waarbij hij gegevens uit veel verschillende bronnen combineerde. Hij betaalde uiteindelijk de hoogste prijs voor zijn digitale behoudswerk toen hij werd vervolgd voor het bulk-downloaden van academische papers, wat leidde tot zijn zelfmoord. Het is overbodig te zeggen dat dit een van de redenen is waarom onze groep pseudoniem is, en waarom we zeer voorzichtig zijn. Open Library wordt nog steeds heroïsch gerund door mensen bij het Internet Archive, die Aaron’s nalatenschap voortzetten. We komen hier later in deze post op terug."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "In de Google-blogpost beschrijft Taycher enkele van de uitdagingen bij het schatten van dit aantal. Ten eerste, wat is een boek? Er zijn een paar mogelijke definities:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fysieke exemplaren.</strong> Uiteraard is dit niet erg nuttig, aangezien het slechts duplicaten van hetzelfde materiaal zijn. Het zou geweldig zijn als we alle aantekeningen die mensen in boeken maken, zoals Fermats beroemde “krabbels in de marges”, konden bewaren. Maar helaas, dat blijft een droom van een archivaris."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Werken”.</strong> Bijvoorbeeld “Harry Potter en de Geheime Kamer” als een logisch concept, dat alle versies ervan omvat, zoals verschillende vertalingen en herdrukken. Dit is een soort nuttige definitie, maar het kan moeilijk zijn om de grens te trekken van wat telt. We willen bijvoorbeeld waarschijnlijk verschillende vertalingen bewaren, hoewel herdrukken met slechts kleine verschillen misschien minder belangrijk zijn."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edities”.</strong> Hier tel je elke unieke versie van een boek. Als er iets anders aan is, zoals een andere omslag of een ander voorwoord, telt het als een andere editie."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Bestanden.</strong> Bij het werken met schaduw bibliotheken zoals Library Genesis, Sci-Hub of Z-Library, is er een extra overweging. Er kunnen meerdere scans van dezelfde editie zijn. En mensen kunnen betere versies van bestaande bestanden maken door de tekst te scannen met OCR, of pagina's die onder een hoek zijn gescand te corrigeren. We willen deze bestanden slechts als één editie tellen, wat goede metadata vereist, of deduplicatie met behulp van documentvergelijkingsmaatregelen."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edities” lijken de meest praktische definitie van wat “boeken” zijn. Handig genoeg wordt deze definitie ook gebruikt voor het toekennen van unieke ISBN-nummers. Een ISBN, of Internationaal Standaard Boeknummer, wordt vaak gebruikt voor internationale handel, omdat het is geïntegreerd met het internationale barcodesysteem (”Internationaal Artikelnummer”). Als je een boek in winkels wilt verkopen, heeft het een barcode nodig, dus krijg je een ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taychers blogpost vermeldt dat hoewel ISBN's nuttig zijn, ze niet universeel zijn, aangezien ze pas echt werden aangenomen in het midden van de jaren zeventig, en niet overal ter wereld. Toch is ISBN waarschijnlijk de meest gebruikte identificator van boekedities, dus het is ons beste startpunt. Als we alle ISBN's in de wereld kunnen vinden, krijgen we een nuttige lijst van welke boeken nog bewaard moeten worden."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Dus, waar halen we de gegevens vandaan? Er zijn een aantal bestaande inspanningen die proberen een lijst van alle boeken ter wereld samen te stellen:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Ze hebben tenslotte dit onderzoek gedaan voor Google Books. Hun metadata is echter niet in bulk toegankelijk en vrij moeilijk te scrapen."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Zoals eerder vermeld, is dit hun hele missie. Ze hebben enorme hoeveelheden bibliotheekgegevens verzameld van samenwerkende bibliotheken en nationale archieven, en blijven dit doen. Ze hebben ook vrijwillige bibliothecarissen en een technisch team dat probeert records te dedupliceren en ze te taggen met allerlei metadata. Het beste van alles is dat hun dataset volledig open is. Je kunt het eenvoudig <a %(openlibrary)s>downloaden</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dit is een website beheerd door de non-profit OCLC, die bibliotheekbeheersystemen verkoopt. Ze verzamelen boekmetadata van veel bibliotheken en maken het beschikbaar via de WorldCat-website. Ze verdienen echter ook geld met de verkoop van deze gegevens, dus het is niet beschikbaar voor bulkdownload. Ze hebben wel enkele meer beperkte bulkdatasets beschikbaar voor download, in samenwerking met specifieke bibliotheken."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dit is het onderwerp van deze blogpost. ISBNdb scrapt verschillende websites voor boekmetadata, met name prijsgegevens, die ze vervolgens verkopen aan boekverkopers, zodat ze hun boeken kunnen prijzen in overeenstemming met de rest van de markt. Aangezien ISBN's tegenwoordig vrij universeel zijn, hebben ze effectief een “webpagina voor elk boek” gebouwd."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Verschillende individuele bibliotheeksystemen en archieven.</strong> Er zijn bibliotheken en archieven die niet zijn geïndexeerd en geaggregeerd door een van de bovenstaande, vaak omdat ze ondergefinancierd zijn, of om andere redenen hun gegevens niet willen delen met Open Library, OCLC, Google, enzovoort. Veel van deze hebben digitale records die toegankelijk zijn via het internet, en ze zijn vaak niet erg goed beschermd, dus als je wilt helpen en wat plezier wilt hebben met het leren over vreemde bibliotheeksystemen, zijn dit geweldige startpunten."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In deze post kondigen we met plezier een kleine release aan (vergeleken met onze eerdere Z-Library releases). We hebben het grootste deel van ISBNdb gescraped en de gegevens beschikbaar gemaakt voor torrenting op de website van de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>; we zullen het hier niet direct linken, zoek er gewoon naar). Dit zijn ongeveer 30,9 miljoen records (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gecomprimeerd). Op hun website beweren ze dat ze eigenlijk 32,6 miljoen records hebben, dus we hebben misschien op de een of andere manier iets gemist, of <em>zij</em> kunnen iets verkeerd doen. In ieder geval zullen we voorlopig niet precies delen hoe we het hebben gedaan — we laten dat als een oefening voor de lezer. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wat we wel zullen delen is een voorlopige analyse, om te proberen dichter bij het schatten van het aantal boeken in de wereld te komen. We hebben naar drie datasets gekeken: deze nieuwe ISBNdb-dataset, onze oorspronkelijke release van metadata die we hebben gescraped van de Z-Library schaduw bibliotheek (die Library Genesis omvat), en de Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Laten we beginnen met enkele ruwe cijfers:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In zowel Z-Library/Libgen als Open Library zijn er veel meer boeken dan unieke ISBN's. Betekent dit dat veel van die boeken geen ISBN's hebben, of ontbreekt de ISBN-metadata gewoon? We kunnen deze vraag waarschijnlijk beantwoorden met een combinatie van geautomatiseerde matching op basis van andere attributen (titel, auteur, uitgever, enz.), het binnenhalen van meer gegevensbronnen, en het extraheren van ISBN's uit de daadwerkelijke boekscans zelf (in het geval van Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Hoeveel van die ISBN's zijn uniek? Dit wordt het beste geïllustreerd met een Venn-diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Om preciezer te zijn:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "We waren verrast door hoe weinig overlap er is! ISBNdb heeft een enorme hoeveelheid ISBN's die niet voorkomen in Z-Library of Open Library, en hetzelfde geldt (in mindere maar nog steeds substantiële mate) voor de andere twee. Dit roept veel nieuwe vragen op. Hoeveel zou geautomatiseerde matching helpen bij het taggen van de boeken die niet met ISBN's waren getagd? Zou er veel overeenkomsten zijn en daardoor meer overlap? Ook, wat zou er gebeuren als we een 4e of 5e dataset toevoegen? Hoeveel overlap zouden we dan zien?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dit geeft ons een startpunt. We kunnen nu kijken naar alle ISBN's die niet in de Z-Library dataset stonden en die ook niet overeenkomen met titel/auteur velden. Dat kan ons helpen om alle boeken in de wereld te behouden: eerst door het internet af te speuren naar scans, en vervolgens door in het echte leven boeken te scannen. Het laatste kan zelfs door crowdfunding worden gefinancierd, of gedreven door \"beloningen\" van mensen die bepaalde boeken gedigitaliseerd willen zien. Dat is allemaal een verhaal voor een andere keer."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Als u wilt helpen met een van deze taken — verdere analyse; meer metadata verzamelen; meer boeken vinden; boeken OCR'en; dit doen voor andere domeinen (bijv. papers, audioboeken, films, tv-shows, tijdschriften) of zelfs een deel van deze data beschikbaar maken voor zaken zoals ML / grote taalmodeltraining — neem dan contact met mij op (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Als u specifiek geïnteresseerd bent in de data-analyse, werken we eraan om onze datasets en scripts beschikbaar te maken in een gebruiksvriendelijker formaat. Het zou geweldig zijn als u gewoon een notebook kunt forken en hiermee kunt beginnen te spelen."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Ten slotte, als u dit werk wilt ondersteunen, overweeg dan een donatie te doen. Dit is een volledig door vrijwilligers gerunde operatie, en uw bijdrage maakt een groot verschil. Elke bijdrage helpt. Voor nu accepteren we donaties in crypto; zie de Doneer-pagina op Anna’s Archief."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Voor een redelijke definitie van \"voor altijd\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Natuurlijk is het geschreven erfgoed van de mensheid veel meer dan boeken, vooral tegenwoordig. Voor de doeleinden van deze post en onze recente releases richten we ons op boeken, maar onze interesses reiken verder."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Er kan veel meer gezegd worden over Aaron Swartz, maar we wilden hem slechts kort noemen, aangezien hij een cruciale rol speelt in dit verhaal. Naarmate de tijd verstrijkt, kunnen meer mensen zijn naam voor het eerst tegenkomen en vervolgens zelf in het konijnenhol duiken."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Het kritieke venster van schaduw bibliotheken"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Hoe kunnen we beweren onze collecties voor altijd te behouden, terwijl ze al bijna 1 PB naderen?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Chinese versie 中文版</a>, bespreek op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Bij Anna’s Archief worden we vaak gevraagd hoe we kunnen beweren onze collecties voor altijd te behouden, terwijl de totale omvang al bijna 1 Petabyte (1000 TB) nadert en nog steeds groeit. In dit artikel bekijken we onze filosofie en zien we waarom het volgende decennium cruciaal is voor onze missie om de kennis en cultuur van de mensheid te behouden."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "De <a %(annas_archive_stats)s>totale grootte</a> van onze collecties, over de afgelopen maanden, uitgesplitst naar aantal torrent seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioriteiten"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Waarom geven we zoveel om papers en boeken? Laten we ons fundamentele geloof in behoud in het algemeen even terzijde schuiven — we kunnen daar een ander bericht over schrijven. Dus waarom specifiek papers en boeken? Het antwoord is simpel: <strong>informatiedichtheid</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte opslag slaat geschreven tekst de meeste informatie op van alle media. Hoewel we zowel om kennis als cultuur geven, geven we meer om het eerste. Over het algemeen vinden we een hiërarchie van informatiedichtheid en belang van behoud die er ongeveer zo uitziet:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Academische papers, tijdschriften, rapporten"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organische data zoals DNA-sequenties, plantenzaden of microbiële monsters"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Non-fictie boeken"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Wetenschap & techniek softwarecode"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Meetgegevens zoals wetenschappelijke metingen, economische gegevens, bedrijfsrapporten"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Wetenschaps- en techniekwebsites, online discussies"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Non-fictie tijdschriften, kranten, handleidingen"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Non-fictie transcripties van lezingen, documentaires, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Interne gegevens van bedrijven of overheden (lekken)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata records in het algemeen (van non-fictie en fictie; van andere media, kunst, mensen, enz.; inclusief recensies)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografische gegevens (bijv. kaarten, geologische onderzoeken)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripties van juridische of gerechtelijke procedures"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fictieve of entertainmentversies van al het bovenstaande"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "De rangschikking in deze lijst is enigszins willekeurig — verschillende items zijn gelijk of er zijn meningsverschillen binnen ons team — en we vergeten waarschijnlijk enkele belangrijke categorieën. Maar dit is ongeveer hoe we prioriteren."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Sommige van deze items zijn te verschillend van de anderen om ons zorgen over te maken (of worden al verzorgd door andere instellingen), zoals organische gegevens of geografische gegevens. Maar de meeste items in deze lijst zijn eigenlijk belangrijk voor ons."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Een andere grote factor in onze prioritering is hoe groot het risico is dat een bepaald werk loopt. We geven de voorkeur aan werken die:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Zeldzaam"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniek onderbelicht"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniek in gevaar van vernietiging (bijv. door oorlog, bezuinigingen, rechtszaken of politieke vervolging)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Tenslotte geven we om schaal. We hebben beperkte tijd en geld, dus besteden we liever een maand aan het redden van 10.000 boeken dan 1.000 boeken — als ze ongeveer even waardevol en risicovol zijn."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Schaduw bibliotheken"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Er zijn veel organisaties met vergelijkbare missies en prioriteiten. Inderdaad, er zijn bibliotheken, archieven, laboratoria, musea en andere instellingen die belast zijn met het behoud van dit soort zaken. Veel van deze worden goed gefinancierd door overheden, individuen of bedrijven. Maar ze hebben één enorme blinde vlek: het juridische systeem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hierin ligt de unieke rol van schaduw bibliotheken, en de reden waarom Anna’s Archief bestaat. Wij kunnen dingen doen die andere instellingen niet mogen doen. Nu is het niet (vaak) zo dat we materialen kunnen archiveren die elders illegaal zijn om te bewaren. Nee, het is in veel plaatsen legaal om een archief te bouwen met boeken, papers, tijdschriften, enzovoort."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Maar wat legale archieven vaak missen, is <strong>redundantie en duurzaamheid</strong>. Er bestaan boeken waarvan slechts één exemplaar in een fysieke bibliotheek ergens aanwezig is. Er bestaan metadatarecords die door één enkel bedrijf worden bewaakt. Er bestaan kranten die alleen op microfilm in een enkel archief bewaard worden. Bibliotheken kunnen te maken krijgen met bezuinigingen, bedrijven kunnen failliet gaan, archieven kunnen gebombardeerd en tot de grond toe afgebrand worden. Dit is niet hypothetisch — dit gebeurt voortdurend."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Wat we uniek kunnen doen bij Anna’s Archief is het opslaan van vele kopieën van werken, op grote schaal. We kunnen papers, boeken, tijdschriften en meer verzamelen en in bulk verspreiden. We doen dit momenteel via torrents, maar de exacte technologieën doen er niet toe en zullen in de loop van de tijd veranderen. Het belangrijkste is dat er veel kopieën over de hele wereld worden verspreid. Deze quote van meer dan 200 jaar geleden is nog steeds waar:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Het verloren kan niet worden hersteld; maar laten we redden wat er overblijft: niet door kluizen en sloten die hen van het publieke oog en gebruik afschermen, door ze aan de vergetelheid over te laten, maar door een zodanige vermenigvuldiging van kopieën, dat ze buiten het bereik van het toeval worden geplaatst.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Een korte opmerking over het publieke domein. Omdat Anna’s Archief zich uniek richt op activiteiten die in veel delen van de wereld illegaal zijn, houden we ons niet bezig met algemeen beschikbare collecties, zoals boeken in het publieke domein. Wettelijke entiteiten zorgen daar vaak al goed voor. Er zijn echter overwegingen die ons soms doen werken aan publiek beschikbare collecties:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadatarecords kunnen vrij worden bekeken op de Worldcat-website, maar niet in bulk worden gedownload (totdat we ze <a %(worldcat_scrape)s>gescrapet</a> hebben)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Code kan open source zijn op Github, maar Github als geheel kan niet gemakkelijk worden gemirrord en dus bewaard blijven (hoewel er in dit specifieke geval voldoende verspreide kopieën van de meeste coderepositories zijn)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit is gratis te gebruiken, maar heeft onlangs strenge anti-scraping maatregelen ingevoerd, in de nasleep van data-hongerige LLM-training (daarover later meer)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Een vermenigvuldiging van kopieën"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Terug naar onze oorspronkelijke vraag: hoe kunnen we claimen onze collecties voor altijd te bewaren? Het belangrijkste probleem hier is dat onze collectie in een snel tempo <a %(torrents_stats)s>groeit</a>, door het scrapen en open-sourcen van enkele enorme collecties (bovenop het geweldige werk dat al is gedaan door andere open-data schaduw bibliotheken zoals Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Deze groei in data maakt het moeilijker om de collecties wereldwijd te spiegelen. Dataopslag is duur! Maar we zijn optimistisch, vooral bij het observeren van de volgende drie trends."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. We hebben het laaghangend fruit geplukt"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dit volgt direct uit onze hierboven besproken prioriteiten. We geven de voorkeur aan het eerst bevrijden van grote collecties. Nu we enkele van de grootste collecties ter wereld hebben veiliggesteld, verwachten we dat onze groei veel langzamer zal zijn."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Er is nog steeds een lange staart van kleinere collecties, en er worden elke dag nieuwe boeken gescand of gepubliceerd, maar het tempo zal waarschijnlijk veel langzamer zijn. We kunnen nog steeds verdubbelen of zelfs verdrievoudigen in omvang, maar over een langere periode."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Opslagkosten blijven exponentieel dalen"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Op het moment van schrijven zijn <a %(diskprices)s>schijfprijzen</a> per TB ongeveer $12 voor nieuwe schijven, $8 voor gebruikte schijven en $4 voor tape. Als we conservatief zijn en alleen naar nieuwe schijven kijken, betekent dat dat het opslaan van een petabyte ongeveer $12.000 kost. Als we aannemen dat onze bibliotheek zal verdrievoudigen van 900TB naar 2,7PB, zou dat $32.400 betekenen om onze hele bibliotheek te mirroren. Met elektriciteit, kosten van andere hardware, enzovoort, ronden we het af op $40.000. Of met tape meer als $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Aan de ene kant is <strong>$15.000–$40.000 voor de som van alle menselijke kennis een koopje</strong>. Aan de andere kant is het een beetje veel om te verwachten dat er tonnen volledige kopieën zijn, vooral als we ook willen dat die mensen hun torrents blijven seeden ten behoeve van anderen."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dat is vandaag. Maar de vooruitgang gaat door:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "De kosten van harde schijven per TB zijn de afgelopen 10 jaar ruwweg met een derde gedaald en zullen waarschijnlijk in een vergelijkbaar tempo blijven dalen. Tape lijkt een vergelijkbaar traject te volgen. SSD-prijzen dalen nog sneller en zouden tegen het einde van het decennium de prijzen van HDD's kunnen overnemen."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-prijstrends van verschillende bronnen (klik om de studie te bekijken)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Als dit standhoudt, dan kijken we over 10 jaar misschien naar slechts $5.000–$13.000 om onze hele collectie te mirroren (1/3e), of zelfs minder als we minder in omvang groeien. Hoewel het nog steeds veel geld is, zal dit voor veel mensen haalbaar zijn. En het kan zelfs beter zijn vanwege het volgende punt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Verbeteringen in informatiedichtheid"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "We slaan momenteel boeken op in de ruwe formaten waarin ze aan ons worden gegeven. Natuurlijk zijn ze gecomprimeerd, maar vaak zijn het nog steeds grote scans of foto’s van pagina’s."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Tot nu toe waren de enige opties om de totale omvang van onze collectie te verkleinen door middel van agressievere compressie of deduplicatie. Echter, om voldoende besparingen te realiseren, zijn beide te verliesgevend naar onze smaak. Zware compressie van foto’s kan tekst nauwelijks leesbaar maken. En deduplicatie vereist een hoge mate van vertrouwen dat boeken precies hetzelfde zijn, wat vaak te onnauwkeurig is, vooral als de inhoud hetzelfde is maar de scans op verschillende momenten zijn gemaakt."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Er is altijd een derde optie geweest, maar de kwaliteit ervan was zo abominabel dat we het nooit overwogen: <strong>OCR, of Optische Karakterherkenning</strong>. Dit is het proces van het omzetten van foto’s in platte tekst, door AI te gebruiken om de karakters in de foto’s te detecteren. Hulpmiddelen hiervoor bestaan al lang en zijn behoorlijk goed, maar “behoorlijk goed” is niet genoeg voor bewaringsdoeleinden."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Echter, recente multi-modale deep-learning modellen hebben extreem snelle vooruitgang geboekt, hoewel nog steeds tegen hoge kosten. We verwachten dat zowel de nauwkeurigheid als de kosten de komende jaren dramatisch zullen verbeteren, tot het punt waarop het realistisch zal worden om op onze gehele bibliotheek toe te passen."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR-verbeteringen."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Wanneer dat gebeurt, zullen we waarschijnlijk nog steeds de originele bestanden bewaren, maar daarnaast zouden we een veel kleinere versie van onze bibliotheek kunnen hebben die de meeste mensen willen spiegelen. Het punt is dat ruwe tekst zelf nog beter comprimeert en veel gemakkelijker te dedupliceren is, wat ons nog meer besparingen oplevert."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Over het algemeen is het niet onrealistisch om ten minste een 5-10x reductie in totale bestandsgrootte te verwachten, misschien zelfs meer. Zelfs met een conservatieve 5x reductie, zouden we kijken naar <strong>$1.000–$3.000 in 10 jaar, zelfs als onze bibliotheek verdrievoudigt in grootte</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritiek venster"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Als deze voorspellingen accuraat zijn, hoeven we <strong>maar een paar jaar te wachten</strong> voordat onze hele collectie op grote schaal wordt gemirrord. Dus, in de woorden van Thomas Jefferson, “buiten het bereik van een ongeluk geplaatst.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Helaas heeft de opkomst van LLM's, en hun data-hongerige training, veel auteursrechthouders in de verdediging gedwongen. Nog meer dan ze al waren. Veel websites maken het moeilijker om te scrapen en archiveren, rechtszaken vliegen in het rond, en ondertussen blijven fysieke bibliotheken en archieven verwaarloosd."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "We kunnen alleen verwachten dat deze trends zullen blijven verslechteren, en dat veel werken verloren zullen gaan ruim voordat ze het publieke domein betreden."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>We staan aan de vooravond van een revolutie in bewaring, maar <q>het verloren kan niet worden hersteld.</q></strong> We hebben een kritieke periode van ongeveer 5-10 jaar waarin het nog steeds vrij duur is om een schaduw bibliotheek te exploiteren en veel mirrors over de hele wereld te creëren, en waarin de toegang nog niet volledig is afgesloten."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Als we dit venster kunnen overbruggen, dan hebben we inderdaad de kennis en cultuur van de mensheid voor altijd bewaard. We mogen deze tijd niet verspillen. We mogen dit kritieke venster niet voor ons laten sluiten."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Laten we gaan."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Exclusieve toegang voor LLM-bedrijven tot de grootste Chinese non-fictieboekencollectie ter wereld"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Chinese versie 中文版</a>, <a %(news_ycombinator)s>Discussieer op Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Kort samengevat:</strong> Anna’s Archief heeft een unieke collectie van 7,5 miljoen / 350TB Chinese non-fictieboeken verworven — groter dan Library Genesis. We zijn bereid een LLM-bedrijf exclusieve toegang te geven, in ruil voor hoogwaardige OCR en tekstuittreksels.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dit is een kort blogbericht. We zijn op zoek naar een bedrijf of instelling die ons kan helpen met OCR en tekstuittreksels voor een enorme collectie die we hebben verworven, in ruil voor exclusieve vroege toegang. Na de embargo-periode zullen we natuurlijk de hele collectie vrijgeven."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Hoogwaardige academische teksten zijn uiterst nuttig voor de training van LLM's. Hoewel onze collectie Chinees is, zou dit zelfs nuttig moeten zijn voor de training van Engelse LLM's: modellen lijken concepten en kennis te coderen, ongeacht de brontaal."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Hiervoor moet tekst uit de scans worden gehaald. Wat krijgt Anna’s Archief hieruit? Volledige tekstzoekfunctie van de boeken voor haar gebruikers."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Omdat onze doelen overeenkomen met die van LLM-ontwikkelaars, zijn we op zoek naar een samenwerkingspartner. We zijn bereid om je <strong>exclusieve vroege toegang tot deze collectie in bulk voor 1 jaar</strong> te geven, als je goede OCR en tekstuittreksels kunt doen. Als je bereid bent om de volledige code van je pijplijn met ons te delen, zouden we bereid zijn om de collectie langer te embargeren."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Voorbeeldpagina's"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Om ons te bewijzen dat je een goede pijplijn hebt, zijn hier enkele voorbeeldpagina's om mee te beginnen, uit een boek over supergeleiders. Je pijplijn moet goed omgaan met wiskunde, tabellen, grafieken, voetnoten, enzovoort."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Stuur je verwerkte pagina's naar ons e-mailadres. Als ze er goed uitzien, sturen we je meer in privé, en we verwachten dat je je pijplijn daar ook snel op kunt draaien. Zodra we tevreden zijn, kunnen we een deal sluiten."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collectie"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Wat meer informatie over de collectie. <a %(duxiu)s>Duxiu</a> is een enorme database van gescande boeken, gecreëerd door de <a %(chaoxing)s>SuperStar Digital Library Group</a>. De meeste zijn academische boeken, gescand om ze digitaal beschikbaar te maken voor universiteiten en bibliotheken. Voor ons Engelssprekende publiek hebben <a %(library_princeton)s>Princeton</a> en de <a %(guides_lib_uw)s>University of Washington</a> goede overzichten. Er is ook een uitstekend artikel dat meer achtergrond geeft: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (zoek het op in Anna’s Archief)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "De boeken van Duxiu zijn al lang gepirateerd op het Chinese internet. Meestal worden ze voor minder dan een dollar verkocht door wederverkopers. Ze worden doorgaans verspreid via het Chinese equivalent van Google Drive, dat vaak is gehackt om meer opslagruimte mogelijk te maken. Enkele technische details zijn te vinden <a %(github_duty_machine)s>hier</a> en <a %(github_821_github_io)s>hier</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Hoewel de boeken semi-openbaar zijn verspreid, is het vrij moeilijk om ze in bulk te verkrijgen. We hadden dit hoog op onze TODO-lijst staan en hebben er meerdere maanden fulltime werk aan toegewezen. Echter, onlangs nam een ongelooflijke, geweldige en getalenteerde vrijwilliger contact met ons op, die ons vertelde dat ze al dit werk al hadden gedaan — tegen grote kosten. Ze deelden de volledige collectie met ons, zonder iets terug te verwachten, behalve de garantie van langdurige bewaring. Echt opmerkelijk. Ze stemden ermee in om op deze manier om hulp te vragen om de collectie te OCR'en."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "De collectie bestaat uit 7.543.702 bestanden. Dit is meer dan Library Genesis non-fictie (ongeveer 5,3 miljoen). De totale bestandsgrootte is ongeveer 359TB (326TiB) in zijn huidige vorm."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "We staan open voor andere voorstellen en ideeën. Neem gewoon contact met ons op. Bekijk Anna’s Archief voor meer informatie over onze collecties, bewaringsinspanningen en hoe u kunt helpen. Bedankt!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Waarschuwing: deze blogpost is verouderd. We hebben besloten dat IPFS nog niet klaar is voor prime time. We zullen nog steeds linken naar bestanden op IPFS vanuit Anna’s Archief wanneer mogelijk, maar we zullen het niet langer zelf hosten, noch raden we anderen aan om te spiegelen met IPFS. Bekijk onze Torrents-pagina als u wilt helpen onze collectie te behouden."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Help Z-Library op IPFS te seeden"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Hoe een schaduw bibliotheek te runnen: operaties bij Anna’s Archief"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Er is geen <q>AWS voor schaduw liefdadigheidsinstellingen,</q> dus hoe runnen we Anna’s Archief?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ik run <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, 's werelds grootste open-source non-profit zoekmachine voor <a %(wikipedia_shadow_library)s>schaduw bibliotheken</a>, zoals Sci-Hub, Library Genesis en Z-Library. Ons doel is om kennis en cultuur gemakkelijk toegankelijk te maken, en uiteindelijk een gemeenschap op te bouwen van mensen die samen <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle boeken ter wereld</a> archiveren en bewaren."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In dit artikel laat ik zien hoe we deze website runnen, en de unieke uitdagingen die komen kijken bij het beheren van een website met een twijfelachtige juridische status, aangezien er geen “AWS voor schaduw liefdadigheidsinstellingen” is."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Bekijk ook het zusterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Hoe word je een piratenarchivaris</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovatietokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Laten we beginnen met onze tech stack. Die is opzettelijk saai. We gebruiken Flask, MariaDB en ElasticSearch. Dat is letterlijk alles. Zoeken is grotendeels een opgelost probleem, en we zijn niet van plan het opnieuw uit te vinden. Bovendien moeten we onze <a %(mcfunley)s>innovatietokens</a> aan iets anders besteden: niet uit de lucht gehaald worden door de autoriteiten."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Dus hoe legaal of illegaal is Anna’s Archief precies? Dit hangt grotendeels af van de juridische jurisdictie. De meeste landen geloven in een vorm van auteursrecht, wat betekent dat mensen of bedrijven een exclusief monopolie krijgen op bepaalde soorten werken voor een bepaalde periode. Terzijde, bij Anna’s Archief geloven we dat hoewel er enkele voordelen zijn, auteursrecht over het algemeen een netto-negatief is voor de samenleving — maar dat is een verhaal voor een andere keer."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dit exclusieve monopolie op bepaalde werken betekent dat het illegaal is voor iedereen buiten dit monopolie om die werken direct te verspreiden — inclusief ons. Maar Anna’s Archief is een zoekmachine die die werken niet direct verspreidt (althans niet op onze clearnet-website), dus we zouden in orde moeten zijn, toch? Niet precies. In veel jurisdicties is het niet alleen illegaal om auteursrechtelijk beschermde werken te verspreiden, maar ook om te linken naar plaatsen die dat doen. Een klassiek voorbeeld hiervan is de Amerikaanse DMCA-wet."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dat is het strengste uiteinde van het spectrum. Aan de andere kant van het spectrum zouden er theoretisch landen kunnen zijn zonder enige auteursrechtwetten, maar die bestaan eigenlijk niet. Vrijwel elk land heeft een vorm van auteursrechtwetgeving. Handhaving is een ander verhaal. Er zijn genoeg landen met regeringen die zich niet druk maken om de handhaving van auteursrechtwetten. Er zijn ook landen tussen de twee uitersten in, die het verspreiden van auteursrechtelijk beschermde werken verbieden, maar niet het linken naar dergelijke werken."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Een andere overweging is op bedrijfsniveau. Als een bedrijf opereert in een rechtsgebied dat zich niet bekommert om auteursrechten, maar het bedrijf zelf geen enkel risico wil nemen, dan kunnen ze je website sluiten zodra iemand erover klaagt."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Tenslotte is een grote overweging betalingen. Aangezien we anoniem moeten blijven, kunnen we geen traditionele betaalmethoden gebruiken. Dit laat ons met cryptocurrencies, en slechts een klein deel van de bedrijven ondersteunt die (er zijn virtuele debetkaarten betaald met crypto, maar die worden vaak niet geaccepteerd)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systeemarchitectuur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Stel dat je enkele bedrijven hebt gevonden die bereid zijn je website te hosten zonder je af te sluiten — laten we deze “vrijheidslievende providers” noemen 😄. Je zult snel merken dat het hosten van alles bij hen vrij duur is, dus je wilt misschien enkele “goedkope providers” vinden en daar de daadwerkelijke hosting doen, via de vrijheidslievende providers. Als je het goed doet, zullen de goedkope providers nooit weten wat je host en nooit klachten ontvangen."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Met al deze providers is er een risico dat ze je toch afsluiten, dus je hebt ook redundantie nodig. We hebben dit op alle niveaus van onze stack nodig."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Een enigszins vrijheidslievende onderneming die zich in een interessante positie heeft geplaatst, is Cloudflare. Ze hebben <a %(blog_cloudflare)s>aangevoerd</a> dat ze geen hostingprovider zijn, maar een nutsvoorziening, zoals een ISP. Ze zijn daarom niet onderworpen aan DMCA of andere verwijderingsverzoeken en sturen eventuele verzoeken door naar je daadwerkelijke hostingprovider. Ze zijn zelfs zover gegaan dat ze naar de rechter zijn gestapt om deze structuur te beschermen. We kunnen hen daarom gebruiken als een extra laag van caching en bescherming."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare accepteert geen anonieme betalingen, dus we kunnen alleen hun gratis plan gebruiken. Dit betekent dat we hun load balancing- of failover-functies niet kunnen gebruiken. Daarom hebben we <a %(annas_archive_l255)s>dit zelf geïmplementeerd</a> op domeinniveau. Bij het laden van de pagina controleert de browser of het huidige domein nog beschikbaar is, en zo niet, dan herschrijft het alle URL's naar een ander domein. Aangezien Cloudflare veel pagina's in de cache opslaat, betekent dit dat een gebruiker op ons hoofddomein kan landen, zelfs als de proxyserver niet werkt, en dan bij de volgende klik naar een ander domein wordt verplaatst."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "We hebben ook nog steeds te maken met normale operationele zorgen, zoals het monitoren van de servergezondheid, het loggen van backend- en frontend-fouten, enzovoort. Onze failover-architectuur zorgt ook voor meer robuustheid op dit gebied, bijvoorbeeld door een compleet andere set servers op een van de domeinen te draaien. We kunnen zelfs oudere versies van de code en datasets op dit aparte domein draaien, voor het geval een kritieke bug in de hoofdversie onopgemerkt blijft."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "We kunnen ons ook indekken tegen Cloudflare die zich tegen ons keert, door het van een van de domeinen te verwijderen, zoals dit aparte domein. Verschillende permutaties van deze ideeën zijn mogelijk."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Tools"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Laten we eens kijken naar welke tools we gebruiken om dit allemaal te bereiken. Dit evolueert sterk naarmate we nieuwe problemen tegenkomen en nieuwe oplossingen vinden."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Applicatieserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxyserver: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serverbeheer: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Ontwikkeling: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion statische hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Er zijn enkele beslissingen waar we over en weer over hebben gediscussieerd. Een daarvan is de communicatie tussen servers: we gebruikten hiervoor vroeger Wireguard, maar ontdekten dat het af en toe stopt met het verzenden van gegevens, of alleen gegevens in één richting verzendt. Dit gebeurde bij verschillende Wireguard-configuraties die we probeerden, zoals <a %(github_costela_wesher)s>wesher</a> en <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. We probeerden ook poorten te tunnelen via SSH, met behulp van autossh en sshuttle, maar liepen tegen <a %(github_sshuttle)s>problemen aan</a> (hoewel het nog steeds niet duidelijk is of autossh last heeft van TCP-over-TCP-problemen of niet — het voelt gewoon als een krakkemikkige oplossing voor mij, maar misschien is het eigenlijk prima?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "In plaats daarvan zijn we teruggekeerd naar directe verbindingen tussen servers, waarbij we verbergen dat een server draait op de goedkope providers door IP-filtering met UFW te gebruiken. Dit heeft het nadeel dat Docker niet goed werkt met UFW, tenzij je <code>network_mode: \"host\"</code> gebruikt. Dit alles is wat foutgevoeliger, omdat je je server aan het internet blootstelt met slechts een kleine misconfiguratie. Misschien moeten we teruggaan naar autossh — feedback zou hier zeer welkom zijn."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "We hebben ook heen en weer geschakeld tussen Varnish en Nginx. We geven momenteel de voorkeur aan Varnish, maar het heeft wel zijn eigenaardigheden en ruwe kantjes. Hetzelfde geldt voor Checkmk: we zijn er niet dol op, maar het werkt voor nu. Weblate is oké geweest, maar niet geweldig — ik ben soms bang dat het mijn gegevens verliest wanneer ik probeer het te synchroniseren met onze git-repo. Flask is over het algemeen goed geweest, maar het heeft enkele vreemde eigenaardigheden die veel tijd hebben gekost om te debuggen, zoals het configureren van aangepaste domeinen, of problemen met de integratie van SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Tot nu toe zijn de andere tools geweldig geweest: we hebben geen serieuze klachten over MariaDB, ElasticSearch, Gitlab, Zulip, Docker en Tor. Al deze hebben enkele problemen gehad, maar niets al te serieus of tijdrovend."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusie"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Het is een interessante ervaring geweest om te leren hoe je een robuuste en veerkrachtige zoekmachine voor schaduw bibliotheken opzet. Er zijn nog veel meer details te delen in latere berichten, dus laat me weten waar je meer over wilt leren!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Zoals altijd zijn we op zoek naar donaties om dit werk te ondersteunen, dus zorg ervoor dat je de Doneer-pagina op Anna’s Archief bekijkt. We zijn ook op zoek naar andere vormen van ondersteuning, zoals subsidies, langetermijnsponsoren, hoogrisico-betaalproviders, misschien zelfs (stijlvolle!) advertenties. En als je je tijd en vaardigheden wilt bijdragen, zijn we altijd op zoek naar ontwikkelaars, vertalers, enzovoort. Bedankt voor je interesse en steun."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hallo, ik ben Anna. Ik heb <a %(wikipedia_annas_archive)s>Anna’s Archief</a> gecreëerd, 's werelds grootste schaduw bibliotheek. Dit is mijn persoonlijke blog, waarin ik en mijn teamgenoten schrijven over piraterij, digitale bewaring en meer."

#, fuzzy
msgid "blog.index.text2"
msgstr "Verbind met mij op <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Let op dat deze website slechts een blog is. We hosten hier alleen onze eigen woorden. Er worden hier geen torrents of andere auteursrechtelijk beschermde bestanden gehost of gelinkt."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogberichten"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5.998.794 boeken op IPFS plaatsen"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Waarschuwing: deze blogpost is verouderd. We hebben besloten dat IPFS nog niet klaar is voor prime time. We zullen nog steeds linken naar bestanden op IPFS vanuit Anna’s Archief wanneer mogelijk, maar we zullen het niet langer zelf hosten, noch raden we anderen aan om te spiegelen met IPFS. Bekijk onze Torrents-pagina als u wilt helpen onze collectie te behouden."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archief heeft heel WorldCat (de grootste bibliotheek metadatacollectie ter wereld) gescraped om een TODO-lijst te maken van boeken die bewaard moeten worden.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Een jaar geleden <a %(blog)s>begonnen</a> we met het beantwoorden van deze vraag: <strong>Welk percentage van de boeken is permanent bewaard door schaduw bibliotheken?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Zodra een boek in een open-data schaduw bibliotheek zoals <a %(wikipedia_library_genesis)s>Library Genesis</a> terechtkomt, en nu ook in <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, wordt het wereldwijd gespiegeld (via torrents), waardoor het praktisch voor altijd bewaard blijft."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Om de vraag te beantwoorden welk percentage van de boeken is bewaard, moeten we de noemer weten: hoeveel boeken bestaan er in totaal? En idealiter hebben we niet alleen een getal, maar ook daadwerkelijke metadata. Dan kunnen we ze niet alleen vergelijken met schaduw bibliotheken, maar ook <strong>een TODO-lijst maken van de resterende boeken die bewaard moeten worden!</strong> We zouden zelfs kunnen dromen van een crowdsourced inspanning om deze TODO-lijst af te werken."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "We hebben <a %(wikipedia_isbndb_com)s>ISBNdb</a> gescraped en de <a %(openlibrary)s>Open Library dataset</a> gedownload, maar de resultaten waren onbevredigend. Het grootste probleem was dat er niet veel overlap was van ISBN's. Zie dit Venn-diagram uit <a %(blog)s>onze blogpost</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "We waren erg verrast door hoe weinig overlap er was tussen ISBNdb en Open Library, die beide vrijelijk gegevens uit verschillende bronnen opnemen, zoals webscrapes en bibliotheekrecords. Als ze allebei goed werk zouden leveren bij het vinden van de meeste ISBN's die er zijn, zouden hun cirkels zeker aanzienlijke overlap hebben, of zou de een een subset van de ander zijn. Het deed ons afvragen, hoeveel boeken vallen <em>volledig buiten deze cirkels</em>? We hebben een grotere database nodig."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Dat is wanneer we onze zinnen zetten op de grootste boeken database ter wereld: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is een eigendomsdatabase van de non-profit <a %(wikipedia_oclc)s>OCLC</a>, die metadatagegevens van bibliotheken over de hele wereld verzamelt, in ruil voor het geven van toegang aan die bibliotheken tot de volledige dataset, en ervoor zorgt dat ze verschijnen in de zoekresultaten van eindgebruikers."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Hoewel OCLC een non-profit is, vereist hun bedrijfsmodel dat ze hun database beschermen. Nou, het spijt ons te zeggen, vrienden bij OCLC, we geven het allemaal weg. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Het afgelopen jaar hebben we alle WorldCat-records nauwgezet gescraped. In het begin hadden we een gelukstreffer. WorldCat was net bezig met de uitrol van hun complete website-herontwerp (in augustus 2022). Dit omvatte een substantiële revisie van hun backend-systemen, waarbij veel beveiligingsfouten werden geïntroduceerd. We grepen onmiddellijk de kans en konden honderden miljoenen (!) records in slechts enkele dagen scrapen."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat-herontwerp</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Daarna werden de beveiligingsfouten langzaam één voor één opgelost, totdat de laatste die we vonden ongeveer een maand geleden werd gepatcht. Tegen die tijd hadden we vrijwel alle records en gingen we alleen nog voor iets hogere kwaliteit records. Dus we vonden dat het tijd was om te publiceren!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Laten we eens kijken naar enkele basisgegevens over de data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formaat?</strong> <a %(blog)s>Anna’s Archief Containers (AAC)</a>, wat in wezen <a %(jsonlines)s>JSON Lines</a> is gecomprimeerd met <a %(zstd)s>Zstandard</a>, plus enkele gestandaardiseerde semantieken. Deze containers omvatten verschillende soorten records, gebaseerd op de verschillende scrapes die we hebben uitgevoerd."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

msgid "dyn.buy_membership.error.unknown"
msgstr "Er is een onbekende fout opgetreden. Neem contact met ons op via %(email)s met een screenshot."

msgid "dyn.buy_membership.error.minimum"
msgstr "Deze munt heeft een minimum dat hoger is dan gebruikelijk. Selecteer een andere duur of munt."

msgid "dyn.buy_membership.error.try_again"
msgstr "Aanvraag kon niet worden voltooid. Probeer het over een paar minuten opnieuw en als het blijft gebeuren, neem dan contact met ons op via %(email)s met een schermafbeelding."

msgid "dyn.buy_membership.error.wait"
msgstr "Fout bij het verwerken van de betaling. Wacht even en probeer het daarna opnieuw. Als het probleem meer dan 24 uur aanhoudt, neem dan contact met ons op via %(email)s met een screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "verborgen opmerking"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Bestandsprobleem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Betere versie"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Wilt u deze gebruiker melden voor beledigend of ongepast gedrag?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Meld misbruik"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Misbruik gemeld:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "U heeft deze gebruiker gemeld voor misbruik."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Antwoord"

msgid "page.md5.quality.logged_out_login"
msgstr "Gelieve <a %(a_login)s>in te loggen</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "U heeft een reactie achtergelaten. Het kan een minuut duren voordat deze zichtbaar is."

msgid "page.md5.quality.comment_error"
msgstr "Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s getroffen pagina's"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Niet zichtbaar in Libgen.rs Non-Fictie"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Niet zichtbaar in Libgen.rs Fictie"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Niet zichtbaar in Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Gemarkeerd als beschadigd bestand in Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Niet aanwezig in Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Gemarkeerd als “spam” in Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Gemarkeerd als “slecht bestand” in Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Niet alle pagina's konden worden omgezet naar PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Het uitvoeren van exiftool op dit bestand is mislukt"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Boek (onbekend)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Boek (non-fictie)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Boek (fictie)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Wetenschappelijk artikel"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentatievormen"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Tijdschrift"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Stripboek"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partituur"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audioboek"

msgid "common.md5_content_type_mapping.other"
msgstr "Overig"

msgid "common.access_types_mapping.aa_download"
msgstr "Download van partnerserver"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Externe download"

msgid "common.access_types_mapping.external_borrow"
msgstr "Extern lenen"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Extern lenen (leesbeperkingen)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Metadata verkennen"

msgid "common.access_types_mapping.torrents_available"
msgstr "In torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinees"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Uploads naar AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tsjechische metadata"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Boeken"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Russische Staatsbibliotheek"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Titel"

msgid "common.specific_search_fields.author"
msgstr "Auteur"

msgid "common.specific_search_fields.publisher"
msgstr "Uitgever"

msgid "common.specific_search_fields.edition_varia"
msgstr "Editie"

msgid "common.specific_search_fields.year"
msgstr "Publicatiejaar"

msgid "common.specific_search_fields.original_filename"
msgstr "Originele bestandsnaam"

msgid "common.specific_search_fields.description_comments"
msgstr "Beschrijving en metadata-opmerkingen"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads van partnerservers zijn momenteel niet beschikbaar voor dit bestand."

msgid "common.md5.servers.fast_partner"
msgstr "Snelle partnerserver #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(aanbevolen)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(geen browser verificatie of wachtlijsten)"

msgid "common.md5.servers.slow_partner"
msgstr "Langzame partnerserver #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(iets sneller maar met wachtlijst)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(geen wachtlijst, maar kan erg traag zijn)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fictie"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fictie"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klik ook op \"GET\" bovenaan de pagina)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(klik op \"GET\" bovenaan de pagina)"

msgid "page.md5.box.download.libgen_ads"
msgstr "hun advertenties staan erom bekend kwaadaardige software te bevatten, dus gebruik een adblocker of klik niet op advertenties"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC-bestanden kunnen onbetrouwbaar zijn om te downloaden)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library op Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(vereist de Tor Browser)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Lenen uit het internetarchief"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(alleen voor gebruikers met leesbeperkingen)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(bijbehorende DOI is mogelijk niet beschikbaar in Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "collectie"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Torrentdownloads in bulk"

msgid "page.md5.box.download.experts_only"
msgstr "(alleen voor experts)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Anna’s Archief doorzoeken op ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Verschillende andere databases doorzoeken op ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Originele vermelding in ISBNdb zoeken"

msgid "page.md5.box.download.aa_openlib"
msgstr "Anna’s Archief doorzoeken op Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Originele vermelding in Open Library zoeken"

msgid "page.md5.box.download.aa_oclc"
msgstr "Anna’s Archief doorzoeken op OCLC-nummer (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Originele vermelding in WorldCat zoeken"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Anna's Archief doorzoeken op DuXiu SSID-nummer"

msgid "page.md5.box.download.original_duxiu"
msgstr "Handmatig zoeken op DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Anna's Archief doorzoeken op CADAL SSNO-nummer"

msgid "page.md5.box.download.original_cadal"
msgstr "Originele vermelding in CADAL zoeken"

msgid "page.md5.box.download.aa_dxid"
msgstr "Anna's Archief doorzoeken op DuXiu DXID-nummer"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archief 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(geen browserverificatie vereist)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tsjechische metadata %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Boeken %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

msgid "page.md5.box.descr_title"
msgstr "beschrijving"

msgid "page.md5.box.alternative_filename"
msgstr "Alternatieve bestandsnaam"

msgid "page.md5.box.alternative_title"
msgstr "Alternatieve titel"

msgid "page.md5.box.alternative_author"
msgstr "Alternatieve auteur"

msgid "page.md5.box.alternative_publisher"
msgstr "Alternatieve uitgever"

msgid "page.md5.box.alternative_edition"
msgstr "Alternatieve editie"

msgid "page.md5.box.alternative_extension"
msgstr "Alternatieve extensie"

msgid "page.md5.box.metadata_comments_title"
msgstr "metadata-opmerkingen"

msgid "page.md5.box.alternative_description"
msgstr "Alternatieve beschrijving"

msgid "page.md5.box.date_open_sourced_title"
msgstr "opensourcedatum"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub-bestand “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Controlled Digital Lending-bestand van Internet Archive “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Dit is een vermelding van een bestand van Internet Archive, geen direct downloadbaar bestand. Je kunt het boek proberen te lenen (link hieronder) of deze URL gebruiken wanneer je <a %(a_request)s>een bestand aanvraagt</a>."

msgid "page.md5.header.consider_upload"
msgstr "Als je dit bestand hebt, maar het nog niet in Anna's Archief beschikbaar is, overweeg dan om het te <a %(a_request)s>uploaden</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Metadatavermelding ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Metadatavermelding Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Metadatavermelding OCLC (WorldCat) %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Metadatavermelding DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Metadatavermelding CADAL SSNO %(id)s"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

msgid "page.md5.header.meta_desc"
msgstr "Dit is een metadatavermelding, geen downloadbaar bestand. Je kunt deze URL gebruiken wanneer je <a %(a_request)s>een bestand aanvraagt</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Metadata van gekoppeld record"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Verbeter metadata op Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Waarschuwing: meerdere gekoppelde records:"

msgid "page.md5.header.improve_metadata"
msgstr "Metadata verbeteren"

msgid "page.md5.text.report_quality"
msgstr "Rapporteer bestandskwaliteit"

msgid "page.search.results.download_time"
msgstr "Downloadtijd"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Website:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Anna's Archief doorzoeken op “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Code Verkenner:"

msgid "page.md5.codes.code_search"
msgstr "Bekijk in Codes Explorer “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Meer informatie…"

msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Lenen (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Metadata verkennen (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Reacties (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Lijsten (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistieken (%(count)s)"

msgid "common.tech_details"
msgstr "Technische details"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌Dit bestand heeft mogelijk problemen en is verborgen in de bron bibliotheek.</span> Soms is dit vanwege een verzoek op basis van auteursrecht, soms is dit omdat er een beter alternatief beschikbaar is, maar het kan ook liggen aan een probleem met het bestand zelf. Het bestand kan mogelijk wel worden gedownload, maar we raden aan eerst een alternatief bestand te zoeken. Meer details:"

msgid "page.md5.box.download.better_file"
msgstr "Er is mogelijk een betere versie van dit bestand beschikbaar op %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Als je dit bestand nog steeds wilt downloaden, zorg dan dat je enkel betrouwbare en geüpdatete software gebruikt om het te openen."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Snelle downloads"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Word <a %(a_membership)s>lid</a> om het langdurige behoud van boeken, papers en meer te ondersteunen. Als dank daarvoor krijg je snelle downloads. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Als je deze maand doneert, krijg je <strong>dubbel</strong> zoveel snelle downloads."

msgid "page.md5.box.download.header_fast_member"
msgstr "Je hebt vandaag nog %(remaining)s over. Bedankt dat je lid bent!❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Je hebt alle snelle downloads van vandaag gebruikt."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Je hebt dit bestand onlangs gedownload. Links blijven een tijdje geldig."

msgid "page.md5.box.download.option"
msgstr "Optie #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(geen omleiding)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(open in viewer)"

msgid "layout.index.header.banner.refer"
msgstr "Als je een vriend doorverwijst, krijgen jullie beiden %(percentage)s%% snelle bonusdownloads!"

msgid "layout.index.header.learn_more"
msgstr "Meer informatie…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Langzame downloads"

msgid "page.md5.box.download.trusted_partners"
msgstr "Van vertrouwde partners."

msgid "page.md5.box.download.slow_faq"
msgstr "Meer informatie in de <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(<a %(a_browser)s>browserverificatie</a> mogelijk vereist — onbeperkte downloads!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Na het downloaden:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Open in onze viewer"

msgid "page.md5.box.external_downloads"
msgstr "toon externe downloads"

msgid "page.md5.box.download.header_external"
msgstr "Externe downloads"

msgid "page.md5.box.download.no_found"
msgstr "Geen downloads gevonden."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Alle download opties zouden veilig moeten zijn. Dat gezegd hebbende: wees altijd voorzichtig met het downloaden van bestanden van het internet. Zorg bijvoorbeeld altijd dat je apparaat geüpdatet is."

msgid "page.md5.box.download.dl_managers"
msgstr "Voor grote bestanden raden we aan een downloadmanager te gebruiken om onderbrekingen te voorkomen."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Aanbevolen downloadmanagers: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Je hebt een e-boek- of PDF-lezer nodig om het bestand te openen, afhankelijk van het bestandsformaat."

msgid "page.md5.box.download.readers.links"
msgstr "Aanbevolen e-boeklezers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Anna’s Archief online viewer"

msgid "page.md5.box.download.conversion"
msgstr "Gebruik online tools om tussen formaten te converteren."

msgid "page.md5.box.download.conversion.links"
msgstr "Aanbevolen conversietools: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Je kunt zowel PDF- als EPUB-bestanden naar je Kindle of Kobo e-reader sturen."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Aanbevolen tools: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon’s “Send to Kindle”"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz’s “Send to Kobo/Kindle”"

msgid "page.md5.box.download.support"
msgstr "Steun auteurs en bibliotheken"

msgid "page.md5.box.download.support.authors"
msgstr "Als je dit leuk vindt en het je kunt veroorloven, overweeg dan om het origineel te kopen of de auteurs direct te steunen."

msgid "page.md5.box.download.support.libraries"
msgstr "Als dit beschikbaar is in jouw lokale bibliotheek, overweeg dan om het daar gratis te lenen."

msgid "page.md5.quality.header"
msgstr "Bestandskwaliteit"

msgid "page.md5.quality.report"
msgstr "Help de community door de kwaliteit van dit bestand te rapporteren! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Rapporteer bestandsprobleem (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Uitstekende bestandskwaliteit (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Voeg een opmerking toe (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Wat is er mis met dit bestand?"

msgid "page.md5.quality.copyright"
msgstr "Gebruik alstublieft het <a %(a_copyright)s>DMCA / Auteursrechtenclaim formulier</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Beschrijf het probleem (verplicht)"

msgid "page.md5.quality.issue_description"
msgstr "Probleembeschrijving"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 van een betere versie van dit bestand (indien van toepassing)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Vul dit in als er een ander bestand is dat nauw overeenkomt met dit bestand (dezelfde editie, dezelfde bestandsextensie als je er een kunt vinden), die mensen in plaats van dit bestand zouden moeten gebruiken. Als je een betere versie van dit bestand buiten Anna’s Archive kent, upload deze dan <a %(a_upload)s>hier</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Je kunt de md5 uit de URL halen, bijvoorbeeld"

msgid "page.md5.quality.submit_report"
msgstr "Rapport indienen"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Leer hoe je zelf de <a %(a_metadata)s>metadata voor dit bestand kunt verbeteren</a>."

msgid "page.md5.quality.report_thanks"
msgstr "Bedankt voor het indienen van je rapport. Het zal op deze pagina worden weergegeven en handmatig worden beoordeeld door Anna (totdat we een goed moderatiesysteem hebben)."

msgid "page.md5.quality.report_error"
msgstr "Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw."

msgid "page.md5.quality.great.summary"
msgstr "Als dit bestand van hoge kwaliteit is, kun je hier alles er over bespreken! Zo niet, gebruik dan de knop “Bestandsprobleem melden”."

msgid "page.md5.quality.loved_the_book"
msgstr "Ik vond dit boek geweldig!"

msgid "page.md5.quality.submit_comment"
msgstr "Laat een reactie achter"

msgid "common.english_only"
msgstr "Onderstaande tekst is alleen in het Engels beschikbaar."

msgid "page.md5.text.stats.total_downloads"
msgstr "Totaal aantal downloads: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Een “bestand MD5” is een hash die wordt berekend op basis van de inhoud van het bestand en redelijk uniek is op basis van die inhoud. Alle schaduwbibliotheken die we hier hebben geïndexeerd, gebruiken voornamelijk MD5's om bestanden te identificeren."

msgid "page.md5.text.md5_info.text2"
msgstr "Een bestand kan in meerdere schaduwbibliotheken voorkomen. Voor informatie over de verschillende datasets die we hebben samengesteld, zie de <a %(a_datasets)s>Datasets pagina</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Dit is een bestand dat beheerd wordt door de <a %(a_ia)s>IA’s Controlled Digital Lending</a> bibliotheek, en wordt geïndexeerd door Anna’s Archive voor zoekopdrachten. Voor informatie over de verschillende datasets die we hebben samengesteld, zie de <a %(a_datasets)s>Datasets pagina</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Voor informatie over dit specifieke bestand, zie het <a %(a_href)s>JSON-bestand</a> dat erbij hoort."

msgid "page.aarecord_issue.title"
msgstr "🔥 Probleem bij het laden van deze pagina"

msgid "page.aarecord_issue.text"
msgstr "Ververs de pagina om het opnieuw te proberen. <a %(a_contact)s>Neem contact met ons op</a> als het probleem meerdere uren aanhoudt."

msgid "page.md5.invalid.header"
msgstr "Niet gevonden"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” is niet gevonden in onze database."

msgid "page.login.title"
msgstr "Inloggen/registreren"

msgid "page.browserverification.header"
msgstr "Browser verificatie"

msgid "page.login.text1"
msgstr "Om te voorkomen dat spambots veel accounts maken, moeten we je browser eerst verifiëren."

msgid "page.login.text2"
msgstr "Als je in een oneindige lus vastloopt, raden we aan om <a %(a_privacypass)s>Privacy Pass</a> te installeren."

msgid "page.login.text3"
msgstr "Het kan ook helpen om advertentieblokkers en andere browserextensies uit te schakelen."

msgid "page.codes.title"
msgstr "Codes"

msgid "page.codes.heading"
msgstr "Code Verkenner"

#, fuzzy
msgid "page.codes.intro"
msgstr "Verken de codes waarmee records op prefix zijn getagd. De kolom “records” toont het aantal records dat is getagd met codes met de gegeven prefix, zoals te zien is in de zoekmachine (inclusief metadata-only records). De kolom “codes” toont hoeveel daadwerkelijke codes een gegeven prefix hebben."

msgid "page.codes.why_cloudflare"
msgstr "Deze pagina kan even nodig hebben om te genereren, daarom is een Cloudflare captcha vereist. <a %(a_donate)s>Leden</a> kunnen de captcha overslaan."

msgid "page.codes.dont_scrape"
msgstr "Gelieve deze pagina's niet te scrapen. In plaats daarvan raden we aan om onze ElasticSearch en MariaDB databases <a %(a_import)s>te genereren</a> of <a %(a_download)s>te downloaden</a>, en onze <a %(a_software)s>open source code</a> te draaien. De ruwe data kan handmatig worden verkend via JSON-bestanden zoals <a %(a_json_file)s>deze</a>."

msgid "page.codes.prefix"
msgstr "Prefix"

msgid "common.form.go"
msgstr "Ga"

msgid "common.form.reset"
msgstr "Reset"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Zoek in Anna’s Archief"

msgid "page.codes.bad_unicode"
msgstr "Waarschuwing: code bevat onjuiste Unicode tekens en kan zich in verschillende situaties onjuist gedragen. De ruwe binaire gegevens kunnen worden gedecodeerd vanuit de base64 representatie in de URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bekende code prefix “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Prefix"

msgid "page.codes.code_label"
msgstr "Label"

msgid "page.codes.code_description"
msgstr "Beschrijving"

msgid "page.codes.code_url"
msgstr "URL voor een specifieke code"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” zal worden vervangen door de waarde van de code"

msgid "page.codes.generic_url"
msgstr "Algemene URL"

msgid "page.codes.code_website"
msgstr "Website"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s record die overeenkomt met “%(prefix_label)s”"
msgstr[1] "%(count)s records die overeenkomen met “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL voor specifieke code: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Meer…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes die beginnen met “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index van"

msgid "page.codes.records_prefix"
msgstr "records"

msgid "page.codes.records_codes"
msgstr "codes"

msgid "page.codes.fewer_than"
msgstr "Minder dan %(count)s records"

msgid "page.contact.dmca.form"
msgstr "Gebruik <a %(a_copyright)s>dit formulier</a> voor DMCA-/auteursrechtclaims."

msgid "page.contact.dmca.delete"
msgstr "Berichten over auteursrechtclaims die op andere manieren worden ingediend, worden automatisch verwijderd."

msgid "page.contact.checkboxes.text1"
msgstr "We kijken uit naar jullie feedback en vragen!"

msgid "page.contact.checkboxes.text2"
msgstr "Door de hoeveelheid spam en onzin die we per e-mail ontvangen, vragen we je echter wel om de vakjes aan te vinken om te bevestigen dat je deze voorwaarden voor contact met ons begrijpt."

msgid "page.contact.checkboxes.copyright"
msgstr "Auteursrechtclaims via deze e-mail worden genegeerd; gebruik in plaats daarvan het formulier."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partnerservers zijn niet beschikbaar vanwege sluitingen van hosting. Ze zouden snel weer online moeten zijn."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Lidmaatschappen worden dienovereenkomstig verlengd."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Mail ons niet voor <a %(a_request)s>boekaanvragen</a><br>of kleine (<10k) <a %(a_upload)s>uploads</a>."

msgid "page.donate.please_include"
msgstr "Wanneer je vragen hebt over je account of donaties, voeg dan je account-ID, screenshots, bonnen en zoveel mogelijk informatie toe. We controleren onze e-mail slechts elke 1-2 weken, dus het niet toevoegen van deze informatie zal ervoor zorgen dat een oplossing langer op zich laat wachten."

msgid "page.contact.checkboxes.show_email_button"
msgstr "E-mail weergeven"

msgid "page.copyright.title"
msgstr "DMCA / Auteursrecht claimformulier"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Als je een DCMA of andere auteursrechtclaim hebt, vul dit formulier dan zo nauwkeurig mogelijk in. Als je problemen ondervindt, neem dan contact met ons op via ons speciale DMCA-adres: %(email)s. Let op dat claims die naar dit adres worden gemaild niet worden verwerkt, het is alleen bedoeld voor vragen. Gebruik het onderstaande formulier om uw claims in te dienen."

msgid "page.copyright.form.aa_urls"
msgstr "URL's op Anna’s Archive (vereist). Eén per regel. Voeg a.u.b. alleen URL's toe die exact dezelfde editie van een boek beschrijven. Als je een claim wil indienen voor meerdere boeken of meerdere edities, dien dit formulier dan meerdere keren in."

msgid "page.copyright.form.aa_urls.note"
msgstr "Claims die meerdere boeken of edities bundelen, worden afgewezen."

msgid "page.copyright.form.name"
msgstr "Je naam (verplicht)"

msgid "page.copyright.form.address"
msgstr "Adres (verplicht)"

msgid "page.copyright.form.phone"
msgstr "Telefoonnummer (verplicht)"

msgid "page.copyright.form.email"
msgstr "E-mail (verplicht)"

msgid "page.copyright.form.description"
msgstr "Duidelijke beschrijving van het bronmateriaal (verplicht)"

msgid "page.copyright.form.isbns"
msgstr "ISBN's van het bronmateriaal (indien van toepassing). Eén per regel. Gelieve alleen diegene bij te voegen die exact overeenkomen met de editie waarvoor je een auteursrechtclaim indient."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL's van het bronmateriaal, één per regel. Neem even de tijd om in de Open Library naar je bronmateriaal te zoeken. Dit zal ons helpen jouw claim te verifiëren."

msgid "page.copyright.form.external_urls"
msgstr "URL's naar bronmateriaal, één per regel (verplicht). Gelieve er zoveel mogelijk bij te voegen om ons te helpen je claim te verifiëren (bijv. Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Verklaring en handtekening (verplicht)"

msgid "page.copyright.form.submit_claim"
msgstr "Claim indienen"

msgid "page.copyright.form.on_success"
msgstr "✅ Bedankt voor het indienen van je auteursrechtenclaim. We zullen deze zo snel mogelijk beoordelen. Gelieve de pagina te herladen om een nieuwe claim in te dienen."

msgid "page.copyright.form.on_failure"
msgstr "❌ Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "Als u geïnteresseerd bent in het mirroren van deze dataset voor <a %(a_archival)s>archivering</a> of <a %(a_llm)s>LLM-training</a>, neem dan contact met ons op."

msgid "page.datasets.intro.text2"
msgstr "Onze missie is om alle boeken ter wereld (evenals papers, tijdschriften, enz.) te archiveren en breed toegankelijk te maken. Wij geloven dat alle boeken wijd en zijd gemirrored moeten worden om redundantie en veerkracht te waarborgen. Daarom verzamelen we bestanden uit verschillende bronnen. Sommige bronnen zijn volledig open en kunnen in bulk worden gespiegeld (zoals Sci-Hub). Andere zijn gesloten en beschermend, dus proberen we ze te scrapen om hun boeken te “bevrijden”. Weer anderen vallen er ergens tussenin."

msgid "page.datasets.intro.text3"
msgstr "Al onze data kan worden <a %(a_torrents)s>getorrent</a>, en al onze metadata kan <a %(a_anna_software)s>gegenereerd</a> of <a %(a_elasticsearch)s>gedownload</a> worden als ElasticSearch- en MariaDB-databases. De ruwe data kan handmatig worden verkend via JSON-bestanden zoals <a %(a_dbrecord)s>deze</a>."

msgid "page.datasets.overview.title"
msgstr "Overzicht"

msgid "page.datasets.overview.text1"
msgstr "Hieronder vind je een kort overzicht van de bronnen van de bestanden op Anna’s Archive."

msgid "page.datasets.overview.source.header"
msgstr "Bron"

msgid "page.datasets.overview.size.header"
msgstr "Grootte"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% gemirrored door AA / torrents beschikbaar"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentages van aantallen bestanden"

msgid "page.datasets.overview.last_updated.header"
msgstr "Laatst bijgewerkt"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-fictie en Fictie"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s bestand"
msgstr[1] "%(count)s bestanden"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: bevroren sinds 2021; meeste beschikbaar via torrents"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: sindsdien kleine toevoegingen</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Exclusief “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fictie torrents lopen achter (hoewel ID's ~4-6M niet getorrent zijn omdat ze overlappen met onze Zlib torrents)."

msgid "page.datasets.zlibzh.searchable"
msgstr "De “Chinese” collectie in Z-Library lijkt dezelfde te zijn als onze DuXiu collectie, maar met verschillende MD5's. We sluiten deze bestanden uit van torrents om duplicatie te voorkomen, maar tonen ze nog steeds in onze zoekindex."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ van de bestanden zijn doorzoekbaar."

msgid "page.datasets.overview.total"
msgstr "Totaal"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Uitgezonderd duplicaten"

msgid "page.datasets.overview.text4"
msgstr "Aangezien de schaduw bibliotheken vaak gegevens van elkaar synchroniseren, is er aanzienlijke overlap tussen de bibliotheken. Daarom komen de aantallen niet overeen met het totaal."

msgid "page.datasets.overview.text5"
msgstr "Het percentage “gemirrored en geseed door Anna’s Archive” toont hoeveel bestanden we zelf mirroren. We seeden die bestanden in bulk via torrents en maken ze beschikbaar voor directe download via partnerwebsites."

msgid "page.datasets.source_libraries.title"
msgstr "Bron bibliotheken"

msgid "page.datasets.source_libraries.text1"
msgstr "Sommige bron bibliotheken promoten het massaal delen van hun data via torrents, terwijl anderen hun collectie niet gemakkelijk delen. In het laatste geval probeert Anna’s Archive hun collecties te scrapen en beschikbaar te maken (zie onze <a %(a_torrents)s>Torrents</a> pagina). Er zijn ook tussenliggende situaties, bijvoorbeeld w aar bronbibliotheken bereid zijn te delen, maar niet de middelen hebben om dit te doen. In die gevallen proberen we ook te helpen."

msgid "page.datasets.source_libraries.text2"
msgstr "Hieronder vind je een overzicht van hoe we omgaan met de verschillende bron bibliotheken."

msgid "page.datasets.sources.source.header"
msgstr "Bron"

msgid "page.datasets.sources.files.header"
msgstr "Bestanden"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dagelijkse <a %(dbdumps)s>HTTP-database dumps</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Geautomatiseerde torrents voor <a %(nonfiction)s>Non-Fictie</a> en <a %(fiction)s>Fictie</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(covers)s>boekomslag torrents</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub heeft sinds 2021 geen nieuwe bestanden meer toegevoegd."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata dumps <a %(scihub1)s>hier</a> en <a %(scihub2)s>hier</a> beschikbaar. Ook als onderdeel beschikbaar van de <a %(libgenli)s>Libgen.li database</a> (die wij gebruiken)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Data torrents beschikbaar <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a>, en <a %(libgenli)s>hier</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Sommige nieuwe bestanden <a %(libgenrs)s>worden</a> <a %(libgenli)s>toegevoegd</a> aan Libgen’s “scimag”, maar niet genoeg om nieuwe torrents te rechtvaardigen"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kwartaallijkse <a %(dbdumps)s>HTTP-database dumps</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-fictie torrents worden gedeeld met Libgen.rs (en <a %(libgenli)s>hier</a> gemirrored)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna’s Archief en Libgen.li beheren gezamenlijk collecties van <a %(comics)s>stripboeken</a>, <a %(magazines)s>tijdschriften</a>, <a %(standarts)s>standaarddocumenten</a> en <a %(fiction)s>fictie (afgeleid van Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Hun “fiction_rus” collectie (Russische fictie) heeft geen speciale torrents, maar wordt gedekt door torrents van anderen, en we houden een <a %(fiction_rus)s>mirror</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna’s Archive en Z-Library beheren gezamenlijk een collectie van <a %(metadata)s>Z-Library metadata</a> en <a %(files)s>Z-Library bestanden</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Enige metadata beschikbaar via <a %(openlib)s>Open Library database dumps</a>, maar deze dekken niet de volledige IA-collectie"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Geen gemakkelijk toegankelijke metadata dumps beschikbaar voor hun volledige collectie"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(ia)s>IA metadata</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Bestanden alleen beperkt beschikbaar voor lenen, met verschillende toegangsbeperkingen"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna’s Archief beheert een collectie van <a %(ia)s>IA bestanden</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Verschillende metadata databases verspreid over het Chinese internet; echter vaak betaalde databases"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Geen gemakkelijk toegankelijke metadata dumps beschikbaar voor hun volledige collectie."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(duxiu)s>DuXiu metadata</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Diverse databanken verspreid over het Chinese internet; echter vaak betaalde databanken"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s De meeste bestanden zijn alleen toegankelijk met premium BaiduYun-accounts; trage downloadsnelheden."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(duxiu)s>DuXiu bestanden</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Diverse kleinere of eenmalige bronnen. We moedigen mensen aan om eerst naar andere schaduw bibliotheken te uploaden, maar soms hebben mensen collecties die te groot zijn voor anderen om te verwerken, maar niet groot genoeg om hun eigen categorie te rechtvaardigen."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Metadata-only bronnen"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "We verrijken onze collectie ook met metadata-only bronnen, die we kunnen koppelen aan bestanden, bijvoorbeeld met behulp van ISBN-nummers of andere velden. Hieronder vind je een overzicht van deze bronnen. Nogmaals, sommige van deze bronnen zijn volledig open, terwijl we andere moeten scrapen."

msgid "page.faq.metadata.inspiration"
msgstr "Onze inspiratie voor het verzamelen van metadata is Aaron Swartz’ doel van “één webpagina voor elk boek dat ooit is gepubliceerd”, waarvoor hij <a %(a_openlib)s>Open Library</a> heeft gecreëerd. Dat project heeft het goed gedaan, maar onze unieke positie stelt ons in staat om metadata te verkrijgen die zij niet kunnen verkrijgen. Een andere inspiratie was ons verlangen om te weten <a %(a_blog)s>hoeveel boeken er in de wereld zijn</a>, zodat we kunnen berekenen hoeveel boeken we nog moeten redden."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Let op dat we bij metadata zoekopdrachten de originele records tonen. We voegen geen records samen."

msgid "page.datasets.sources.last_updated.header"
msgstr "Laatst bijgewerkt"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Maandelijkse <a %(dbdumps)s>database dumps</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Niet direct beschikbaar in bulk, beschermd tegen scraping"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

msgid "page.datasets.unified_database.title"
msgstr "Gecombineerde database"

msgid "page.datasets.unified_database.text1"
msgstr "We combineren al de bovenstaande bronnen in één gecombineerde database die we gebruiken om deze website te laten draaien. Deze gecombineerde database is niet direct beschikbaar, maar aangezien Anna’s Archive volledig open source is, kan deze vrij eenvoudig <a %(a_generated)s>gegenereerd</a> of <a %(a_downloaded)s>gedownload</a> worden als ElasticSearch en MariaDB databases. De scripts op die pagina zullen automatisch alle vereiste metadata downloaden van de hierboven genoemde bronnen."

msgid "page.datasets.unified_database.text2"
msgstr "Als je onze data wil verkennen voordat je die scripts lokaal uitvoert, kun je onze JSON-bestanden bekijken, die verder linken naar andere JSON-bestanden. <a %(a_json)s>Dit bestand</a> is een goed startpunt."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Gebaseerd op onze <a %(a_href)s>blogpost</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> is een enorme database van gescande boeken, gecreëerd door de <a %(superstar_link)s>SuperStar Digital Library Group</a>. De meeste boeken zijn academische boeken, gescand om ze digitaal beschikbaar te maken voor universiteiten en bibliotheken. Voor ons Engelssprekende publiek hebben <a %(princeton_link)s>Princeton</a> en de <a %(uw_link)s>University of Washington</a> goede overzichten. Er is ook een uitstekend artikel dat meer achtergrondinformatie geeft: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "De boeken van Duxiu zijn al lang gepirate op het Chinese internet. Meestal worden ze voor minder dan een dollar verkocht door resllers. Ze worden doorgaans verspreid via het Chinese equivalent van Google Drive, dat vaak is gehackt om meer opslagruimte mogelijk te maken. Enkele technische details zijn te vinden <a %(link1)s>hier</a> en <a %(link2)s>hier</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Hoewel de boeken semi-openbaar zijn verspreid, is het vrij moeilijk om ze in bulk te verkrijgen. We hadden dit hoog op onze TODO-lijst staan en hebben er meerdere maanden fulltime werk aan besteed. Echter, eind 2023 nam een ongelooflijke, geweldige en getalenteerde vrijwilliger contact met ons op en vertelde ons dat ze al dit werk al had gedaan — tegen grote kosten. Ze deelde de volledige collectie met ons, zonder iets terug te verwachten, behalve de garantie van langdurige bewaring. Echt opmerkelijk."

msgid "page.datasets.common.resources"
msgstr "Middelen"

msgid "page.datasets.common.total_files"
msgstr "Totaal aantal bestanden: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Totale bestandsgrootte: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Bestanden gemirrored door Anna’s Archive: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Laatst bijgewerkt: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrents door Anna’s Archive"

msgid "page.datasets.common.aa_example_record"
msgstr "Voorbeeldrecord op Anna’s Archive"

msgid "page.datasets.duxiu.blog_post"
msgstr "Onze blogpost over deze data"

msgid "page.datasets.common.import_scripts"
msgstr "Scripts voor het importeren van metadata"

msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers formaat"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Meer informatie van onze vrijwilligers (ruwe notities):"

msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.ia.description"
msgstr "Deze dataset is nauw verwant aan de <a %(a_datasets_openlib)s>Open Library dataset</a>. Het bevat een scrape van alle metadata en een groot deel van de bestanden uit de IA’s Controlled Digital Lending Library. Updates worden uitgebracht in het <a %(a_aac)s>Anna’s Archive Containers-formaat</a>."

msgid "page.datasets.ia.description2"
msgstr "Naar deze records wordt rechtstreeks verwezen vanuit de Open Library dataset, maar ze bevatten ook records die niet in de Open Library staan. We hebben ook een aantal databestanden die door communityleden door de jaren heen zijn gescraped."

msgid "page.datasets.ia.description3"
msgstr "De collectie bestaat uit twee delen. Je hebt beide delen nodig om alle data te verkrijgen (behalve verouderde torrents, die zijn doorgestreept op de torrents-pagina)."

msgid "page.datasets.ia.part1"
msgstr "onze eerste release, voordat we het <a %(a_aac)s>Anna’s Archive Containers (AAC) formaat</a> standaardiseerden. Bevat metadata (als json en xml), pdf's (van acsm en lcpdf digitale uitleensystemen), en cover thumbnails."

msgid "page.datasets.ia.part2"
msgstr "incrementele nieuwe releases, met gebruik van AAC. Bevat alleen metadata met tijdstempels na 2023-01-01, aangezien de rest al is gedekt door “ia”. Ook alle pdf-bestanden, dit keer van de acsm en “bookreader” (IA’s webreader) uitleensystemen. Ondanks dat de naam niet helemaal klopt, voegen we nog steeds bookreader-bestanden toe aan de ia2_acsmpdf_files collectie, aangezien ze elkaar uitsluiten."

msgid "page.datasets.common.main_website"
msgstr "Hoofd %(source)s website"

msgid "page.datasets.ia.ia_lending"
msgstr "Digitale Uitleenbibliotheek"

msgid "page.datasets.common.metadata_docs"
msgstr "Metadata documentatie (meeste velden)"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN-landinformatie"

msgid "page.datasets.isbn_ranges.text1"
msgstr "Het Internationale ISBN Agentschap geeft regelmatig de reeksen vrij die het heeft toegewezen aan nationale ISBN-agentschappen. Hieruit kunnen we afleiden tot welk land, regio of taalgroep dit ISBN behoort. We gebruiken deze gegevens momenteel indirect, via de <a %(a_isbnlib)s>isbnlib</a> Python-bibliotheek."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Middelen"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Laatst bijgewerkt: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN website"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Voor de achtergrond van de verschillende Library Genesis forks, zie de pagina voor de <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "De Libgen.li bevat de meeste van dezelfde inhoud en metadata als de Libgen.rs, maar heeft enkele collecties bovenop dit, namelijk strips, tijdschriften en standaarddocumenten. Het heeft ook <a %(a_scihub)s>Sci-Hub</a> geïntegreerd in zijn metadata en zoekmachine, wat wij gebruiken voor onze database."

msgid "page.datasets.libgen_li.description3"
msgstr "De metadata voor deze bibliotheek is vrij beschikbaar <a %(a_libgen_li)s>op libgen.li</a>. Deze server is echter traag en ondersteunt het hervatten van verbroken verbindingen niet. Dezelfde bestanden zijn ook beschikbaar op <a %(a_ftp)s>een FTP-server</a>, die beter werkt."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents zijn beschikbaar voor de meeste extra inhoud, met name torrents voor strips, tijdschriften en standaarddocumenten zijn uitgebracht in samenwerking met Anna’s Archief."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "De fictiecollectie heeft zijn eigen torrents (afwijkend van <a %(a_href)s>Libgen.rs</a>) beginnend bij %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Volgens de beheerder van Libgen.li zou de “fiction_rus” (Russische fictie) collectie gedekt moeten worden door regelmatig uitgebrachte torrents van <a %(a_booktracker)s>booktracker.org</a>, met name de <a %(a_flibusta)s>flibusta</a> en <a %(a_librusec)s>lib.rus.ec</a> torrents (die we <a %(a_torrents)s>hier</a> mirroren, hoewel we nog niet hebben vastgesteld welke torrents overeenkomen met welke bestanden)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistieken voor alle collecties zijn te vinden <a %(a_href)s>op de website van libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fictie lijkt ook te zijn afgedwaald, maar zonder nieuwe torrents. Het lijkt erop dat dit sinds begin 2022 is gebeurd, hoewel we dit niet hebben geverifieerd."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Bepaalde reeksen zonder torrents (zoals fictiereeksen f_3463000 tot f_4260000) zijn waarschijnlijk Z-Library (of andere dubbele) bestanden, hoewel we mogelijk wat deduplicatie willen doen en torrents willen maken voor lgli-unieke bestanden in deze reeksen."

msgid "page.datasets.libgen_li.description5"
msgstr "Let op dat de torrentbestanden die verwijzen naar “libgen.is” expliciet mirrors zijn van <a %(a_libgen)s>Libgen.rs</a> (“.is” is een ander domein dat door Libgen.rs wordt gebruikt)."

msgid "page.datasets.libgen_li.description6"
msgstr "Een nuttige bron voor het gebruik van de metadata is <a %(a_href)s>deze pagina</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fictie-torrents op Anna’s Archive"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Strip-torrents op Anna’s Archive"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Tijdschrift-torrents op Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standaard document torrents op Anna’s Archief"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russische fictie torrents op Anna’s Archief"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata veld informatie"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror van andere torrents (en unieke fictie- en strip-torrents)"

msgid "page.datasets.libgen_li.forum"
msgstr "Discussieforum"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Onze blogpost over de stripboekenuitgave"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "Het korte verhaal van de verschillende Library Genesis (of “Libgen”) forks, is dat na verloop van tijd de verschillende mensen die betrokken waren bij Library Genesis ruzie kregen en hun eigen weg gingen."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "De “.fun” versie is gemaakt door de oorspronkelijke oprichter. Het wordt vernieuwd ten gunste van een nieuwe, meer gedistribueerde versie."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "De “.rs” versie heeft zeer vergelijkbare data en publiceert het meest consequent hun collectie in bulk torrents. Het is ruwweg opgesplitst in een “fictie” en een “non-fictie” sectie."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oorspronkelijk op “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "De <a %(a_li)s>“.li” versie</a> heeft een enorme collectie strips, evenals andere inhoud, die (nog) niet beschikbaar is voor bulk download via torrents. Het heeft wel een aparte torrentcollectie van fictieboeken en bevat de metadata van <a %(a_scihub)s>Sci-Hub</a> in zijn database."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Volgens deze <a %(a_mhut)s>forumpost</a> werd Libgen.li oorspronkelijk gehost op “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> is in zekere zin ook een fork van Library Genesis, hoewel ze een andere naam voor hun project hebben gebruikt."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Deze pagina gaat over de “.rs” versie. Het staat bekend om het consequent publiceren van zowel zijn metadata als de volledige inhoud van zijn boekencatalogus. De boekencollectie is opgesplitst in een fictie en non-fictie gedeelte."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Een nuttige bron voor het gebruik van de metadata is <a %(a_metadata)s>deze pagina</a> (blokkeert IP-reeksen, VPN kan nodig zijn)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Vanaf 2024-03 worden nieuwe torrents gepost in <a %(a_href)s>deze forumthread</a> (blokkeert IP-reeksen, VPN kan nodig zijn)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-fictie torrents op Anna’s Archive"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fictie torrents op Anna’s Archive"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata veld informatie"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-fictie torrents"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fictie torrents"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Discussieforum"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents door Anna’s Archive (boekomslagen)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Onze blog over de release van de boekomslagen"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis staat bekend om het genereus beschikbaar stellen van hun data in bulk via torrents. Onze Libgen-collectie bestaat uit aanvullende data die zij niet direct vrijgeven, in samenwerking met hen. Veel dank aan iedereen die betrokken is bij Library Genesis voor de samenwerking met ons!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Deze <a %(blog_post)s>eerste release</a> is vrij klein: ongeveer 300GB aan boekomslagen van de Libgen.rs fork, zowel fictie als non-fictie. Ze zijn georganiseerd op dezelfde manier als hoe ze op libgen.rs staan, bijvoorbeeld:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s voor een non-fictie boek."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s voor een fictie boek."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Net als bij de Z-Library collectie hebben we ze allemaal in een groot .tar-bestand geplaatst, dat gemount kan worden met <a %(a_ratarmount)s>ratarmount</a> als je de bestanden direct wilt serveren."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> is een propriëtaire database van de non-profitorganisatie <a %(a_oclc)s>OCLC</a>, die metadatarecords van bibliotheken over de hele wereld verzamelt. Het is waarschijnlijk de grootste collectie van bibliotheek-metadata ter wereld."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, eerste uitgave:"

msgid "page.datasets.worldcat.description2"
msgstr "In oktober 2023 hebben we een uitgebreide scrape van de OCLC (WorldCat) database <a %(a_scrape)s>uitgebracht</a>, in het <a %(a_aac)s>Anna’s Archive Containers-formaat</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrents door Anna’s Archive"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Onze blogpost over deze data"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library is een open source project van het Internet Archive om elk boek ter wereld te catalogiseren. Het heeft een van 's werelds grootste operaties om boeken te scannen en heeft veel boeken beschikbaar voor digitale uitleen. De metadata catalogus van boeken is vrij beschikbaar om te downloaden en is opgenomen in Anna’s Archive (hoewel momenteel niet in de zoekfunctie, behalve als je expliciet zoekt op een Open Library ID)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

msgid "page.datasets.isbndb.release1.title"
msgstr "Release 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Dit is een dump van veel aanroepen naar isbndb.com in september 2022. We hebben geprobeerd alle ISBN-reeksen te dekken. Dit zijn ongeveer 30,9 miljoen records. Op hun website claimen ze dat ze eigenlijk 32,6 miljoen records hebben, dus we hebben misschien op de een of andere manier iets gemist, of <em>zij</em> kunnen iets verkeerd doen."

msgid "page.datasets.isbndb.release1.text2"
msgstr "De JSON-responses zijn vrijwel ruw van hun server. Een probleem met de data kwaliteit dat we hebben opgemerkt, is dat voor ISBN-13-nummers die beginnen met een andere prefix dan “978-”, ze nog steeds een “isbn”-veld opnemen dat simpelweg het ISBN-13-nummer is met de eerste 3 cijfers eraf gehakt (en het controlecijfer opnieuw berekend). Dit is duidelijk fout, maar dit is how ze het lijken te doen, dus hebben we het niet aangepast."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Een ander potentieel probleem dat je kunt tegenkomen, is het feit dat het “isbn13”-veld duplicaten heeft, dus je kunt het niet gebruiken als primaire sleutel in een database. De velden “isbn13”+“isbn” lijken gecombineerd wel uniek te zijn."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Voor achtergrond over Sci-Hub, kun je terecht op de <a %(a_scihub)s>officiële website</a>, de <a %(a_wikipedia)s>Wikipedia-pagina</a> en dit <a %(a_radiolab)s>podcastinterview</a>."

msgid "page.datasets.scihub.description2"
msgstr "Let op dat Sci-Hub sinds <a %(a_reddit)s>2021 bevroren</a> is. Het was al eerder bevroren, maar in 2021 werden er een paar miljoen papers toegevoegd. Nog steeds worden er een beperkt aantal papers toegevoegd aan de Libgen “scimag” collecties, maar niet genoeg om nieuwe bulk torrents te rechtvaardigen."

msgid "page.datasets.scihub.description3"
msgstr "We gebruiken de Sci-Hub metadata zoals verstrekt door <a %(a_libgen_li)s>Libgen.li</a> in de “scimag” collectie. We gebruiken ook de dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Let op dat de “smarch” torrents <a %(a_smarch)s>verouderd</a> zijn en daarom niet zijn opgenomen in onze torrent lijst."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents op Anna’s Archive"

msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata en torrents"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents op Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents op Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Updates op Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-pagina"

msgid "page.datasets.scihub.link_podcast"
msgstr "Podcastinterview"

msgid "page.datasets.upload.title"
msgstr "Uploads naar Anna’s Archive"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Overzicht van <a %(a1)s>datasets pagina</a>."

msgid "page.datasets.upload.description"
msgstr "Verschillende kleinere of eenmalige bronnen. We moedigen mensen aan om eerst naar andere schaduw-bibliotheken te uploaden, maar soms hebben mensen collecties die te groot zijn voor anderen om te verwerken, maar niet groot genoeg om hun eigen categorie te rechtvaardigen."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "De “upload” collectie is opgesplitst in kleinere subcollecties, die worden aangegeven in de AACIDs en torrentnamen. Alle subcollecties zijn eerst gededupliceerd aan de hand van de hoofcollectie, hoewel de metadata “upload_records” JSON-bestanden nog steeds veel verwijzingen naar de originele bestanden bevatten. Niet-boek bestanden zijn ook verwijderd uit de meeste subcollecties, en worden meestal <em>niet</em> vermeld in de “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Veel subcollecties zelf bestaan uit sub-sub-collecties (bijv. van verschillende oorspronkelijke bronnen), die worden weergegeven als mappen in de “filepath” velden."

msgid "page.datasets.upload.subs.heading"
msgstr "De subcollecties zijn:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcollectie"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notities"

msgid "page.datasets.upload.action.browse"
msgstr "bladeren"

msgid "page.datasets.upload.action.search"
msgstr "zoeken"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Van <a %(a_href)s>aaaaarg.fail</a>. Lijkt redelijk compleet te zijn. Van onze vrijwilliger “cgiym”."

msgid "page.datasets.upload.source.acm"
msgstr "Van een <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Heeft een vrij grote overlap met bestaande paper collecties, maar zeer weinig MD5-overeenkomsten, dus we besloten het volledig te behouden."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape van <q>iRead eBooks</q> (= fonetisch <q>ai rit i-books</q>; airitibooks.com), door vrijwilliger <q>j</q>. Komt overeen met <q>airitibooks</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Uit een collectie <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Gedeeltelijk van de oorspronkelijke bron, gedeeltelijk van the-eye.eu, gedeeltelijk van andere mirrors."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Van een privé boeken-torrentwebsite, <a %(a_href)s>Bibliotik</a> (vaak aangeduid als “Bib”), waarvan boeken in torrents werden gebundeld op naam (A.torrent, B.torrent) en verspreid via the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Van onze vrijwilliger “bpb9v”. Voor meer informatie over <a %(a_href)s>CADAL</a>, zie de notities op onze <a %(a_duxiu)s>DuXiu dataset pagina</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Meer van onze vrijwilliger “bpb9v”, voornamelijk DuXiu-bestanden, evenals een map “WenQu” en “SuperStar_Journals” (SuperStar is het bedrijf achter DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Van onze vrijwilliger “cgiym”, Chinese teksten uit verschillende bronnen (weergegeven als submappen), waaronder van <a %(a_href)s>China Machine Press</a> (een grote Chinese uitgever)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Niet-Chinese collecties (weergegeven als submappen) van onze vrijwilliger “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape van boeken over Chinese architectuur, door vrijwilliger <q>cm</q>: <q>Ik heb het verkregen door een netwerk kwetsbaarheid bij de uitgeverij te exploiteren, maar die maas in de wet is inmiddels gesloten</q>. Komt overeen met <q>chinese_architecture</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Boeken van academische uitgeverij <a %(a_href)s>De Gruyter</a>, verzameld uit een paar grote torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape van <a %(a_href)s>docer.pl</a>, een Poolse website voor het delen van bestanden, gericht op boeken en andere geschreven werken. Gescrapet in eind 2023 door vrijwilliger “p”. We hebben geen goede metadata van de originele website (zelfs geen bestandsextensies), maar we hebben gefilterd op boekachtige bestanden en konden vaak metadata uit de bestanden zelf halen."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direct van DuXiu, verzameld door vrijwilliger “w”. Alleen recente DuXiu-boeken zijn direct beschikbaar via ebooks, dus de meeste hiervan moeten recent zijn."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Overgebleven DuXiu-bestanden van vrijwilliger “m”, die niet in het DuXiu-eigen PDG-formaat waren (de hoofd <a %(a_href)s>DuXiu dataset</a>). Verzameld uit vele originele bronnen, helaas zonder die bronnen in het bestandspad te behouden."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape van erotische boeken, door vrijwilliger <q>do no harm</q>. Komt overeen met <q>hentai</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collectie gescrapet van een Japanse Manga-uitgever door vrijwilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Geselecteerde gerechtelijke archieven van Longquan</a>, verstrekt door vrijwilliger “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape van <a %(a_href)s>magzdb.org</a>, een bondgenoot van Library Genesis (wordt gelinkt op de libgen.rs homepage) maar die hun bestanden niet direct wilden verstrekken. Verkregen door vrijwilliger “p” eind 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Diverse kleine uploads, te klein als hun eigen subcollectie, maar weergegeven als mappen."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks van AvaxHome, een Russische bestandsdeelwebsite."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archief van kranten en tijdschriften. Komt overeen met <q>newsarch_magz</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape van het <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collectie van vrijwilliger “o” die Poolse boeken direct van originele release (“scene”) websites verzamelde."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Gecombineerde collecties van <a %(a_href)s>shuge.org</a> door vrijwilligers “cgiym” en “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (vernoemd naar de fictieve bibliotheek), gescrapet in 2022 door vrijwilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-collecties (weergegeven als mappen) van vrijwilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (door <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, mijn kleine boekenhuis — woz9ts: “Deze site richt zich voornamelijk op het delen van hoogwaardige e-boek bestanden, waarvan sommige door de eigenaar zelf zijn gezet. De eigenaar werd <a %(a_arrested)s>gearresteerd</a> in 2019 en iemand heeft een collectie van de bestanden die hij deelde gemaakt.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Overgebleven DuXiu-bestanden van vrijwilliger “woz9ts”, die niet in het DuXiu-eigen PDG-formaat waren (moet nog omgezet worden naar PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents door Anna’s Archive"

msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library heeft zijn wortels in de <a %(a_href)s>Library Genesis</a> gemeenschap, en is oorspronkelijk opgestart met hun data. Sindsdien is het aanzienlijk geprofessionaliseerd en heeft het een veel modernere interface. Ze zijn daarom in staat om veel meer donaties te krijgen, zowel financieel om hun website te blijven verbeteren, als donaties van nieuwe boeken. Ze hebben een grote collectie verzameld naast Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Update vanaf februari 2023."

msgid "page.datasets.zlib.description.allegations"
msgstr "Eind 2022 werden de vermeende oprichters van Z-Library gearresteerd en werden domeinen in beslag genomen door de Amerikaanse autoriteiten. Sindsdien is de website langzaam weer online gekomen. Het is onbekend wie het momenteel beheert."

msgid "page.datasets.zlib.description.three_parts"
msgstr "De collectie bestaat uit drie delen. De oorspronkelijke beschrijvingspagina's voor de eerste twee delen zijn hieronder bewaard gebleven. Je hebt alle drie de delen nodig om alle data te verkrijgen (behalve verouderde torrents, die zijn doorgestreept op de torrentpagina)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: onze eerste release. Dit was de allereerste release van wat toen de “Pirate Library Mirror” (“pilimi”) werd genoemd."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: tweede release, dit keer met alle bestanden verpakt in .tar-bestanden."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: incrementele nieuwe releases, in het <a %(a_href)s>Anna’s Archive Containers (AAC) formaat</a>, nu uitgebracht in samenwerking met het Z-Library team."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents door Anna’s Archive (metadata + inhoud)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Voorbeeldrecord op Anna’s Archive (oorspronkelijke collectie)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Voorbeeldrecord op Anna’s Archive (“zlib3” collectie)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Hoofdwebsite"

msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domein"

msgid "page.datasets.zlib.blog.release1"
msgstr "Blogpost over Release 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Blogpost over Release 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-releases (oorspronkelijke beschrijvingspagina's)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Release 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "De oorspronkelijke mirror is in de loop van 2021 en 2022 met veel moeite verkregen. Op dit moment is deze enigszins verouderd: het reflecteert de staat van de collectie in juni 2021. We zullen deze in de toekomst updaten. Op dit moment zijn we gefocust op het uitbrengen van deze eerste release."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Aangezien Library Genesis al gepreserveerd is met openbare torrents en is opgenomen in de Z-Library, hebben we in juni 2022 een basale deduplicatie uitgevoerd tegen Library Genesis. Hiervoor hebben we MD5-hashes gebruikt. Waarschijnlijk is er veel meer dubbele inhoud in de bibliotheek, zoals meerdere bestandsformaten van hetzelfde boek. Dit is moeilijk om accuraat te detecteren, dus doen we dat niet. Na de deduplicatie blijven we over met meer dan 2 miljoen bestanden, in totaal net onder de 7TB."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "De collectie bestaat uit twee delen: een MySQL “.sql.gz” dump van de metadata, en de 72 torrentbestanden van elk ongeveer 50-100GB. De metadata bevat de gegevens zoals gerapporteerd door de Z-Library website (titel, auteur, beschrijving, bestandstype), evenals de werkelijke bestandsgrootte en md5sum die we hebben waargenomen, aangezien deze soms niet overeenkomen. Er lijken reeksen bestanden te zijn waarvoor de Z-Library zelf onjuiste metadata heeft. In sommige geïsoleerde gevallen hebben we mogelijk ook bestanden incorrect gedownload. Dit zullen we in de toekomst proberen te detecteren en corrigeren."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "De grote torrentbestanden bevatten de daadwerkelijke boekgegevens, met de Z-Library ID als bestandsnaam. De bestandsextensies kunnen worden gereconstrueerd met behulp van de metadata dump."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "De collectie is een mix van non-fictie en fictie inhoud (niet gescheiden zoals in Library Genesis). De kwaliteit varieert ook sterk."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Deze eerste release is nu volledig beschikbaar. Let op dat de torrentbestanden alleen beschikbaar zijn via onze Tor-mirror."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Release 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "We hebben alle boeken verzameld die tussen onze laatste mirror en augustus 2022 aan de Z-Library zijn toegevoegd. We hebben ook enkele boeken gescrapet die we de eerste keer hebben gemist. Alles bij elkaar is deze nieuwe collectie ongeveer 24TB. Nogmaals, deze collectie is gededupliceerd tegen Library Genesis, aangezien er al torrents beschikbaar zijn voor die collectie."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "De data is vergelijkbaar georganiseerd als bij de eerste release. Er is een MySQL “.sql.gz” dump van de metadata, die ook alle metadata van de eerste release bevat en deze daarmee vervangt. We hebben ook enkele nieuwe kolommen toegevoegd:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: of dit bestand al in Library Genesis staat, in de non-fictie of fictie collectie (gematcht op md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in welke torrent dit bestand zit."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: ingesteld wanneer we het boek niet konden downloaden."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "We hebben dit de vorige keer al vermeld, maar om het te verduidelijken: “filename” en “md5” zijn de daadwerkelijke eigenschappen van het bestand, terwijl “filename_reported” en “md5_reported” zijn wat we van Z-Library hebben gescrapet. Soms komen deze twee niet overeen, dus hebben we beide opgenomen."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Voor deze release hebben we de verzameling veranderd naar “utf8mb4_unicode_ci”, wat compatibel zou moeten zijn met oudere versies van MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "De databestanden zijn vergelijkbaar met de vorige keer, hoewel ze veel groter zijn. We konden ons simpelweg niet druk maken om tonnen kleinere torrentbestanden te maken. “pilimi-zlib2-0-14679999-extra.torrent” bevat alle bestanden die we in de laatste release hebben gemist, terwijl de andere torrents allemaal nieuwe ID-reeksen zijn. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> We hebben de meeste van onze torrents te groot gemaakt, waardoor torrentclients moeite hadden. We hebben ze verwijderd en nieuwe torrents uitgebracht."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> Er waren nog steeds te veel bestanden, dus hebben we ze in tar-bestanden verpakt en opnieuw nieuwe torrents uitgebracht."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Release 2 addendum (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dit is een enkel extra torrentbestand. Het bevat geen nieuwe informatie, maar het bevat wel wat data die enige tijd kan kosten om te berekenen. Dat maakt het handig om te hebben, aangezien het downloaden van deze torrent vaak sneller is dan het vanaf nul te berekenen. In het bijzonder bevat het SQLite-indexen voor de tar-bestanden, voor gebruik met <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Veelgestelde Vragen (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Wat is Anna's Archief?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archief</span> is een non-profitproject met twee doelen:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Behoud:</strong> we slaan de kennis en cultuur van de mensheid op.</li><li><strong>Toegang:</strong> we maken deze kennis en cultuur beschikbaar voor iedereen ter wereld.</li>"

msgid "page.home.intro.open_source"
msgstr "Al onze <a %(a_code)s>code</a> en <a %(a_datasets)s>data</a> zijn volledig open source."

msgid "page.home.preservation.header"
msgstr "Behoud"

msgid "page.home.preservation.text1"
msgstr "We behouden boeken, papers, strips, tijdschriften en meer door deze materialen uit verschillende <a href=\"https://nl.wikipedia.org/wiki/Schaduwbibliotheek\">schaduwbibliotheken</a>, officiële bibliotheken en andere collecties samen te brengen op één plek. Al deze gegevens worden voor altijd bewaard door het makkelijk te maken om ze in bulk te dupliceren – met behulp van torrents – waardoor er overal ter wereld vele kopieën zijn. Sommige schaduwbibliotheken doen dit al zelf (zoals Sci-Hub en Library Genesis), terwijl Anna's Archief andere bibliotheken 'bevrijdt' die geen bulkverspreiding aanbieden (zoals Z-Library) of helemaal geen schaduwbibliotheken zijn (zoals Internet Archive en DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Deze brede distributie, gecombineerd met de opensourcecode, maakt onze website weerbaar tegen verwijderingen en verzekert het behoud van de kennis en cultuur van de mensheid op de lange termijn. Lees meer over <a href=\"/datasets\">onze datasets</a>."

msgid "page.home.preservation.label"
msgstr "We schatten dat we ongeveer <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% van alle boeken ter wereld</a> hebben bewaard."

msgid "page.home.access.header"
msgstr "Toegang"

msgid "page.home.access.text"
msgstr "We werken met partners samen om onze verzamelingen gemakkelijk en vrij toegankelijk te maken. We geloven dat iedereen het recht tot de collectieve wijsheid van de mensheid heeft – en <a %(a_search)s>niet ten koste van auteurs</a>."

msgid "page.home.access.label"
msgstr "Downloads per uur in de afgelopen 30 dagen. Gemiddelde per uur: %(hourly)s. Gemiddelde per dag: %(daily)s."

msgid "page.about.text2"
msgstr "We geloven sterk in de vrije toegankelijkheid van informatie en behoud van kennis en cultuur. Met deze zoekmachine bouwen we voort op wat ons is nagelaten. We hebben heel veel respect voor het harde werk van mensen die verschillende schaduwbibliotheken hebben gemaakt en we hopen dat deze zoekmachine het bereik van deze bibliotheken vergroot."

msgid "page.about.text3"
msgstr "Volg Anna om op de hoogte te blijven van onze voortgang op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Voor vragen of feedback kun je contact opnemen met Anna via %(email)s."

msgid "page.faq.help.title"
msgstr "Hoe kan ik helpen?"

msgid "page.about.help.text"
msgstr "<li>1. Volg ons op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Laat anderen weten over Anna’s Archief op Twitter, Reddit, TikTok, Instagram, bij je lokale café of bibliotheek of waar je ook gaat! We geloven niet in gatekeeping – als we offline worden gehaald, steken we ergens anders de kop op, omdat al onze broncode open source is.</li><li>3. Als het mogelijk is, overweeg dan om te <a href=\"/donate\">doneren</a>.</li><li>4. Help onze website te <a href=\"https://translate.annas-software.org/\">vertalen</a> in verschillende talen. </li><li>5. Als je een softwareontwikkelaar bent, overweeg dan bij te dragen aan onze <a href=\"https://annas-software.org/\">open source</a> of onze <a href=\"/datasets\">torrents</a> te seeden.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "We hebben nu ook een gesynchroniseerd Matrix-kanaal op %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Als je een beveiligingsonderzoeker bent, kunnen we je vaardigheden op zowel offensief als defensief vlak goed gebruiken. Bekijk daarvoor de pagina <a %(a_security)s>Beveiliging</a>."

msgid "page.about.help.text7"
msgstr "7. We zijn op zoek naar betalingsexperts voor anonieme verkopers. Kan jij, of iemand die je kent, ons helpen bij het toevoegen van makkelijkere manieren om te doneren? Denk daarbij aan PayPal, WeChat en cadeaukaarten. Neem dan contact met ons op."

msgid "page.about.help.text8"
msgstr "8. We zijn altijd op zoek naar meer servercapaciteit."

msgid "page.about.help.text9"
msgstr "9. Je kunt ons helpen door problemen met bestanden te melden, opmerkingen achter te laten en lijsten op deze website samen te stellen. Je kunt ons ook helpen door <a %(a_upload)s>meer boeken te uploaden</a> of door problemen met of indelingen van bestaande boeken te verhelpen."

msgid "page.about.help.text10"
msgstr "10. Maak of help bij het onderhouden van de Wikipedia-pagina voor Anna’s Archief in jouw taal."

msgid "page.about.help.text11"
msgstr "11. We zijn op zoek naar kleine, stijlvolle advertenties. Als je wilt adverteren op Anna's Archief, laat het ons dan weten."

msgid "page.faq.help.mirrors"
msgstr "Het zou geweldig zijn als mensen '<a %(a_mirrors)s>mirrors</a>' opzetten, die we financieel ondersteunen."

msgid "page.about.help.volunteer"
msgstr "Voor uitgebreidere informatie over hoe je vrijwilligerswerkt kunt doen, zie onze <a %(a_volunteering)s>Vrijwilligers & Premies</a> pagina."

msgid "page.faq.slow.title"
msgstr "Waarom zijn de langzame downloads zo traag?"

msgid "page.faq.slow.text1"
msgstr "We hebben letterlijk niet genoeg middelen om iedereen ter wereld snelle downloads te bieden, hoe graag we dat ook zouden willen. Als een rijke weldoener ons hiermee zou willen helpen, zou dat geweldig zijn, maar tot die tijd doen we ons best. We zijn een non-profit project die nauwelijks rondkomt van donaties."

msgid "page.faq.slow.text2"
msgstr "Daarom hebben we twee systemen voor gratis downloads geïmplementeerd met onze partners: gedeelde servers met trage downloads, en iets snellere servers met een wachtlijst (om het aantal mensen dat tegelijkertijd downloadt te verminderen)."

msgid "page.faq.slow.text3"
msgstr "We hebben ook <a %(a_verification)s>browser verificatie</a> voor onze trage downloads, omdat anders bots en scrapers er misbruik van zouden maken, waardoor het nog trager wordt voor legitieme gebruikers."

msgid "page.faq.slow.text4"
msgstr "Let op dat je bij het gebruik van de Tor Browser mogelijk je beveiligingsinstellingen moet aanpassen. Bij de laagste optie, genaamd “Standaard”, slaagt de Cloudflare turnstile beveiliging. Bij de hogere opties, genaamd “Veiliger” en “Veiligst”, faalt de beveiliging."

msgid "page.faq.slow.text5"
msgstr "Voor grote bestanden kunnen trage downloads soms halverwege onderbroken worden. We raden aan om een downloadmanager (zoals JDownloader) te gebruiken om grote downloads automatisch te hervatten."

msgid "page.donate.faq.title"
msgstr "Veelgestelde vragen over donaties"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Worden lidmaatschappen automatisch verlengd?</div> Lidmaatschappen worden <strong>niet</strong> automatisch verlengd. Blijf zolang lid als je zelf wil."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kan ik mijn lidmaatschap upgraden of meerdere lidmaatschappen krijgen?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Zijn er andere betaalmethodes beschikbaar?</div> Momenteel niet. Veel mensen willen dat archieven zoals deze niet bestaan, dus we moeten voorzichtig zijn. Als je ons kunt helpen om andere (gemakkelijkere) betaalmethodes veilig op te zetten, neem dan contact op via %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Wat betekenen de periodes per maand?</div> Je kunt aan de lagere kant van een periode komen door alle kortingen toe te passen, zoals het kiezen van een periode langer dan een maand."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Waar geven jullie de donaties aan uit?</div> 100%% gaat naar het behoud en het toegankelijk maken van de kennis en cultuur van de wereld. Momenteel geven we het vooral uit aan servers, opslag en bandbreedte. Er gaat geen geld naar teamleden."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kan ik een grote donatie doen?</div> Dat zou geweldig zijn! Als je meer dan een paar duizend dollar wilt doneren, neem dan contact op via %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kan ik een donatie doen zonder lid te worden?</div> Natuurlijk. We accepteren donaties van elk bedrag op dit Monero (XMR) adres: %(address)s."

msgid "page.faq.upload.title"
msgstr "Hoe upload ik nieuwe boeken?"

msgid "page.upload.zlib.text1"
msgstr "Als alternatief kun je ze <a %(a_upload)s>hier</a> naar Z-Library uploaden ."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Voor kleine uploads (tot 10.000 bestanden) upload ze alstublieft naar zowel %(first)s als %(second)s."

msgid "page.upload.text1"
msgstr "Voorlopig raden we je aan om nieuwe boeken op de forks van Library Genesis te uploaden. Hier vind je een <a %(a_guide)s>handleiding</a>. Beide forks die we op deze website indexeren, halen hun gegevens van ditzelfde uploadsysteem."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Voor Libgen.li, zorg ervoor dat u eerst inlogt op <a %(a_forum)s >hun forum</a> met gebruikersnaam %(username)s en wachtwoord %(password)s, en keer dan terug naar hun <a %(a_upload_page)s >uploadpagina</a>."

msgid "common.libgen.email"
msgstr "Als je e-mailadres niet op de Libgen-fora werkt, raden we je aan om <a %(a_mail)s>Proton Mail</a> (gratis) te gebruiken. Je kunt ook <a %(a_manual)s>handmatig vragen</a> of je account kan worden geactiveerd."

msgid "page.faq.mhut_upload"
msgstr "Let op dat mhut.org bepaalde IP-reeksen blokkeert, dus een VPN kan nodig zijn."

msgid "page.upload.large.text"
msgstr "Neem contact met ons op via %(a_email)s bij grote uploads (van meer dan 10.000 bestanden) die niet door Libgen of Z-Library worden geaccepteerd."

msgid "page.upload.zlib.text2"
msgstr "Upload academische papers naast Library Genesis ook naar <a %(a_stc_nexus)s>STC Nexus</a> . Zij zijn de beste schaduw-bibliotheek voor nieuwe papers. We hebben ze nog niet geïntegreerd, maar dat zal uiteindelijk wel gebeuren. Je kunt hun<a %(a_telegram)s>upload bot op Telegram</a> gebruiken, of contact opnemen met het adres dat in hun gepinde bericht staat als je te veel bestanden hebt om op deze manier te uploaden."

msgid "page.faq.request.title"
msgstr "Hoe vraag ik boeken aan?"

msgid "page.request.cannot_accomodate"
msgstr "Op dit moment kunnen we boekaanvragen niet inwilligen."

msgid "page.request.forums"
msgstr "Doe je aanvragen op de fora van Z-Library of Libgen."

msgid "page.request.dont_email"
msgstr "Mail ons je boekaanvragen niet."

msgid "page.faq.metadata.title"
msgstr "Verzamelen jullie metadata?"

msgid "page.faq.metadata.indeed"
msgstr "Ja."

msgid "page.faq.1984.title"
msgstr "Ik heb 1984 van George Orwell gedownload, komt de politie nu aan mijn deur?"

msgid "page.faq.1984.text"
msgstr "Maak je niet te veel zorgen, er zijn veel mensen die downloaden van websites waar wij naar linken, en het is uiterst zeldzaam om in de problemen te komen. Om veilig te blijven raden we echter aan een VPN (betaald) of <a %(a_tor)s>Tor</a> (gratis) te gebruiken."

msgid "page.faq.save_search.title"
msgstr "Hoe sla ik mijn zoekinstellingen op?"

msgid "page.faq.save_search.text1"
msgstr "Selecteer de instellingen die u wilt, laat het zoekvak leeg, klik op “Zoeken” en voeg de pagina dan toe aan uw bladwijzers met de bladwijzerfunctie van uw browser."

msgid "page.faq.mobile.title"
msgstr "Hebben jullie een mobiele app?"

msgid "page.faq.mobile.text1"
msgstr "We hebben geen officiële mobiele app, maar je kunt wel deze website als app installeren."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik op het menu met de drie stippen rechtsboven en selecteer “Toevoegen aan startscherm”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik op de “Deel” knop onderaan en selecteer “Toevoegen aan startscherm”."

msgid "page.faq.api.title"
msgstr "Hebben jullie een API?"

msgid "page.faq.api.text1"
msgstr "We hebben één stabiele JSON API voor leden om een snelle download-URL te krijgen: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentatie in de JSON zelf)."

msgid "page.faq.api.text2"
msgstr "Voor andere gebruiksscenario's, zoals het itereren door al onze bestanden, het bouwen van aangepaste zoekfuncties, enzovoort, raden we aan om onze ElasticSearch- en MariaDB-databases <a %(a_generate)s>te genereren</a> of <a %(a_download)s>te downloaden</a>. De ruwe data kan handmatig worden verkend <a %(a_explore)s>via JSON-bestanden</a>."

msgid "page.faq.api.text3"
msgstr "Onze ruwe torrentlijst kan ook als <a %(a_torrents)s>JSON</a> worden gedownload."

msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

msgid "page.faq.torrents.q1"
msgstr "Ik wil graag helpen met seeden, maar ik heb niet veel schijfruimte."

msgid "page.faq.torrents.a1"
msgstr "Gebruik de <a %(a_list)s>torrentlijstgenerator</a> om een lijst te genereren van torrents die het meest behoefte hebben aan seeding en passen in de limiet van je opslagruimte."

msgid "page.faq.torrents.q2"
msgstr "De torrents zijn te langzaam; kan ik de data direct van jullie downloaden?"

msgid "page.faq.torrents.a2"
msgstr "Ja, zie de <a %(a_llm)s>LLM data</a> pagina."

msgid "page.faq.torrents.q3"
msgstr "Kan ik slechts een subset van de bestanden downloaden, zoals alleen een bepaalde taal of onderwerp?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kort antwoord: niet gemakkelijk."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Lang antwoord:"

msgid "page.faq.torrents.a3"
msgstr "De meeste torrents bevatten de bestanden direct, wat betekent dat je torrent-clients kunt instrueren om alleen de benodigde bestanden te downloaden. Om te bepalen welke bestanden je moet downloaden kun je onze <a %(a_generate)s>metadata genereren</a> of onze ElasticSearch- en MariaDB-databases <a %(a_download)s>downloaden</a>. Helaas bevatten een aantal torrent-collecties .zip- of .tar-bestanden in de root, in welk geval je de hele torrent moet downloaden voordat je individuele bestanden kunt selecteren."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(We hebben echter <a %(a_ideas)s>enkele ideeën</a> voor het laatste geval.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Er zijn nog geen gebruiksvriendelijke tools beschikbaar voor het filteren van torrents, maar we verwelkomen bijdragen."

msgid "page.faq.torrents.q4"
msgstr "Hoe gaan jullie om met duplicaten in de torrents?"

msgid "page.faq.torrents.a4"
msgstr "We proberen minimale duplicatie of overlap tussen de torrents in deze lijst te houden, maar dit lukt niet altijd en hangt sterk af van het beleid van de bron bibliotheken. Wij hebben dit niet in de hand voor bibliotheken die hun eigen torrents uitbrengen. Voor torrents die door Anna’s Archief worden uitgebracht, dedupliceren we alleen op basis van MD5-hash, wat betekent dat verschillende versies van hetzelfde boek niet worden gededupliceerd."

msgid "page.faq.torrents.q5"
msgstr "Kan ik de torrentlijst als JSON krijgen?"

msgid "page.faq.torrents.a5"
msgstr "Ja."

msgid "page.faq.torrents.q6"
msgstr "Ik zie geen PDF's of EPUB's in de torrents, alleen binaire bestanden? Wat moet ik doen?"

msgid "page.faq.torrents.a6"
msgstr "Dit zijn wel degelijk PDF's en EPUB's, ze hebben alleen geen extensie in veel van onze torrents. Er zijn twee plaatsen waar je de metadata voor torrent-bestanden kunt vinden, inclusief de bestandstypen/extensies:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Elke collectie of release heeft zijn eigen metadata. <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> hebben bijvoorbeeld een bijbehorende metadata-database die wordt gehost op de Libgen.rs-website. We linken meestal naar relevante metadata bronnen vanaf de <a %(a_datasets)s>dataset pagina</a> van elke collectie."

msgid "page.faq.torrents.a6.li2"
msgstr "2. We raden aan om onze ElasticSearch- en MariaDB-databases te <a %(a_generate)s>genereren</a> of <a %(a_download)s>downloaden</a>. Deze bevatten een mapping naar de bijbehorende torrent-bestanden voor elk record in Anna’s Archive (indien beschikbaar), onder \"torrent_paths\" in de ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Waarom kan mijn torrentclient sommige van jullie torrentbestanden / magnetlinks niet openen?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Sommige torrentclients ondersteunen geen grote stukgroottes, wat veel van onze torrents wel hebben (voor nieuwere doen we dit niet meer — ook al is het volgens de specificaties geldig!). Probeer een andere client als je hier tegenaan loopt, of klaag bij de makers van je torrentclient."

msgid "page.faq.security.title"
msgstr "Hebben jullie een responsible disclosure beleid?"

msgid "page.faq.security.text1"
msgstr "We verwelkomen beveiligingsonderzoekers om naar kwetsbaarheden in onze systemen te zoeken. We zijn grote voorstanders van responsible disclosure. Neem <a %(a_contact)s>hier</a> contact met ons op ."

msgid "page.faq.security.text2"
msgstr "We zijn momenteel niet in staat om bug bounties toe te kennen, behalve voor kwetsbaarheden die het <a %(a_link)s>potentieel hebben om onze anonimiteit in gevaar te brengen</a>. Hiervoor bieden we beloningen aan van tussen de $10k en $50k. We zouden in de toekomst graag een breder scala aan bug bounties willen aanbieden! Houd er rekening mee dat social engineering-aanvallen buiten de scope vallen."

msgid "page.faq.security.text3"
msgstr "Als je geïnteresseerd bent in offensieve beveiliging en wil helpen de kennis en cultuur van de wereld te archiveren, neem dan zeker contact met ons op. Er zijn veel manieren waarop je kunt helpen."

msgid "page.faq.resources.title"
msgstr "Zijn er meer bronnen over Anna’s Archive?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmatige updates"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — onze open source code"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Vertalen op Anna’s Software</a> — ons vertalingssysteem"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — over de data"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatieve domeinen"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — meer over ons (help deze pagina up-to-date te houden, of maak er een aan in je eigen taal!)"

msgid "page.faq.copyright.title"
msgstr "Hoe meld ik inbreuk op het auteursrecht?"

msgid "page.faq.copyright.text1"
msgstr "Wij hosten hier geen auteursrechtelijk beschermd materiaal. Wij zijn een zoekmachine en indexeren alleen metadata die al openbaar beschikbaar is. Bij het downloaden van deze externe bronnen raden wij aan om de wetten in jouw jurisdictie te controleren met betrekking tot wat is toegestaan. Wij zijn niet verantwoordelijk voor inhoud die door anderen wordt gehost."

msgid "page.faq.copyright.text2"
msgstr "Als je klachten hebt over wat je hier ziet, kun je het beste contact opnemen met de oorspronkelijke website. Wij halen regelmatig hun wijzigingen op in onze database. Als je echt denkt dat je een geldige DMCA-klacht hebt waar wij op moeten reageren, vul dan het <a %(a_copyright)s>DMCA / Auteursrecht klachtformulier</a> in. Wij nemen je klachten serieus en zullen zo snel mogelijk contact met je opnemen."

msgid "page.faq.hate.title"
msgstr "Ik haat hoe jullie dit project runnen!"

msgid "page.faq.hate.text1"
msgstr "Wij willen iedereen er ook aan herinneren dat al onze code en data volledig open source zijn. Dit is uniek voor projecten zoals het onze — we zijn ons niet bewust van een ander project met een even omvangrijke catalogus die ook volledig open source is. We verwelkomen iedereen die denkt dat we ons project slecht runnen om onze code en data te nemen en hun eigen schaduw bibliotheek op te zetten! We zeggen dit niet uit wrok of iets dergelijks — we denken echt dat dit geweldig zou zijn, omdat het de lat voor iedereen zou verhogen en de erfenis van de mensheid beter zou behouden."

msgid "page.faq.uptime.title"
msgstr "Heb je een uptime-monitor?"

msgid "page.faq.uptime.text1"
msgstr "Zie <a %(a_href)s>dit uitstekende project</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Hoe doneer ik boeken of ander fysiek materiaal?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Stuur ze alstublieft naar het <a %(a_archive)s>Internet Archive</a>. Zij zullen ze op de juiste manier bewaren."

msgid "page.faq.anna.title"
msgstr "Wie is Anna?"

msgid "page.faq.anna.text1"
msgstr "Jij bent Anna!"

msgid "page.faq.favorite.title"
msgstr "Wat zijn jullie favoriete boeken?"

msgid "page.faq.favorite.text1"
msgstr "Hier zijn enkele boeken die een speciale betekenis hebben voor de wereld van schaduw bibliotheken en digitale archivering:"

msgid "page.fast_downloads.no_more_new"
msgstr "Je hebt alle snelle downloads van vandaag gebruikt."

msgid "page.fast_downloads.no_member"
msgstr "Word lid voor snelle downloads."

msgid "page.fast_downloads.no_member_2"
msgstr "We ondersteunen nu Amazon cadeaubonnen, credit- en debetkaarten, crypto, Alipay en WeChat."

msgid "page.home.full_database.header"
msgstr "Volledige database"

msgid "page.home.full_database.subtitle"
msgstr "Boeken, papers, tijdschriften, strips, bibliotheekgegevens, metadata, …"

msgid "page.home.full_database.search"
msgstr "Zoeken"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "bèta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub heeft de upload van nieuwe papers <a %(a_paused)s>gepauzeerd</a>."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB is een voortzetting van Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Directe toegang tot %(count)s academische papers"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Open"

msgid "page.home.scidb.browser_verification"
msgstr "Als je <a %(a_member)s>lid</a> bent, is browserverificatie niet nodig."

msgid "page.home.archive.header"
msgstr "Langetermijnarchief"

msgid "page.home.archive.body"
msgstr "De datasets die worden gebruikt in Anna’s Archive zijn volledig open en kunnen in bulk worden gemirrored met torrents. <a %(a_datasets)s>Meer informatie…</a>"

msgid "page.home.torrents.body"
msgstr "Je helpt ons enorm door torrents te seeden. <a %(a_torrents)s>Meer informatie…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "Trainingsdata voor LLM's"

msgid "page.home.llm.body"
msgstr "We hebben 's werelds grootste verzameling tekstgegevens van hoge kwaliteit. <a %(a_llm)s>Meer informatie…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: oproep voor vrijwilligers"

msgid "page.home.volunteering.header"
msgstr "🤝 Op zoek naar vrijwilligers"

msgid "page.home.volunteering.help_out"
msgstr "Als een non-profit, open-source project zijn we altijd op zoek naar mensen die willen helpen."

msgid "page.home.payment_processor.body"
msgstr "Neem contact met ons op als je een risicovolle, anonieme betalingsverwerker draait. We zijn daarnaast op zoek naar mensen die aantrekkelijke kleine advertenties willen plaatsen. Alle opbrengsten gaan naar onze inspanningen voor het behoud."

msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

msgid "page.ipfs_downloads.title"
msgstr "IPFS downloads"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Alle downloadlinks voor dit bestand</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS-gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(mogelijk moet je het een paar keer proberen met IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 <a %(a_membership)s>Word lid</a> om snellere downloads te krijgen en browsercontroles over te slaan."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Bekijk de pagina's <a %(a_datasets)s>Datasets</a> en <a %(a_torrents)s>Torrents</a> voor het spiegelen van onze collectie in bulk."

msgid "page.llm.title"
msgstr "LLM data"

msgid "page.llm.intro"
msgstr "Het is algemeen bekend dat LLM's gedijen op hoogwaardige gegevens. We hebben de grootste collectie boeken, artikelen, tijdschriften, enz. ter wereld. Deze zijn sommige van de hoogste kwaliteit tekstbronnen."

msgid "page.llm.unique_scale"
msgstr "Unieke schaal en bereik"

msgid "page.llm.unique_scale.text1"
msgstr "Onze collectie bevat meer dan honderd miljoen bestanden, waaronder wetenschappelijke tijdschriften, boeken en tijdschriften. We bereiken deze hoeveelheid door grote bestaande repositories te combineren."

msgid "page.llm.unique_scale.text2"
msgstr "Sommige van onze broncollecties zijn al in bulk beschikbaar (Sci-Hub en delen van Libgen). Andere bronnen hebben we zelf bevrijd. <a %(a_datasets)s>Datasets</a> toont een volledig overzicht."

msgid "page.llm.unique_scale.text3"
msgstr "Onze collectie omvat miljoenen boeken, artikelen en tijdschriften van vóór het e-boek tijdperk. Grote delen van deze collectie zijn al omgezet doormiddel van OCR en hebben al van zichzelf weinig interne overlap."

msgid "page.llm.how_we_can_help"
msgstr "Hoe we kunnen helpen"

msgid "page.llm.how_we_can_help.text1"
msgstr "We kunnen snelle toegang bieden tot onze volledige collecties, evenals tot niet-uitgebrachte collecties."

msgid "page.llm.how_we_can_help.text2"
msgstr "Dit is toegang op ondernemingsniveau die we kunnen bieden voor donaties in de orde van tienduizenden USD. We zijn ook bereid dit te ruilen voor hoogwaardige collecties die we nog niet hebben."

msgid "page.llm.how_we_can_help.text3"
msgstr "We kunnen je terugbetalen als je ons kunt voorzien van verrijking van onze gegevens, zoals:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Overlap verwijderen (deduplicatie)"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Tekst- en metadata-extractie"

msgid "page.llm.how_we_can_help.text4"
msgstr "Ondersteun langdurige archivering van menselijke kennis, terwijl je betere gegevens krijgt voor jouw model!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Neem contact met ons op</a> om te bespreken hoe we kunnen samenwerken."

msgid "page.login.continue"
msgstr "Doorgaan"

msgid "page.login.please"
msgstr "<a %(a_account)s>Log in</a> om deze pagina weer te geven.</a>"

msgid "page.maintenance.header"
msgstr "Anna’s Archive is tijdelijk offline voor onderhoud. Kom over een uur terug."

msgid "page.metadata.header"
msgstr "Verbeter metadata"

msgid "page.metadata.body1"
msgstr "Je kunt helpen bij het behoud van boeken door de metadata te verbeteren! Lees eerst de achtergrondinformatie over metadata op Anna’s Archive en leer vervolgens hoe je metadata kunt verbeteren door te linken met Open Library, en verdien gratis lidmaatschap op Anna’s Archive."

msgid "page.metadata.background.title"
msgstr "Achtergrond"

msgid "page.metadata.background.body1"
msgstr "Wanneer je een boek bekijkt op Anna’s Archive, kun je verschillende velden zien: titel, auteur, uitgever, editie, jaar, beschrijving, bestandsnaam, en meer. Al deze stukjes informatie worden <em>metadata</em> genoemd."

msgid "page.metadata.background.body2"
msgstr "Aangezien we boeken combineren uit verschillende <em>bronbibliotheken</em>, tonen we alle metadata die beschikbaar is in die bronbibliotheek. Voor een boek dat we hebben verkregen uit Library Genesis, tonen we bijvoorbeeld de titel uit de database van Library Genesis."

msgid "page.metadata.background.body3"
msgstr "Soms is een boek aanwezig in <em>meerdere</em> bronbibliotheken, die mogelijk verschillende metadata velden hebben. In dat geval tonen we simpelweg de langste versie van elk veld, omdat die hopelijk de meest bruikbare informatie bevat! We tonen de andere velden nog steeds onder de beschrijving, bijvoorbeeld als ”alternatieve titel” (maar alleen als ze verschillend zijn)."

msgid "page.metadata.background.body4"
msgstr "We halen ook <em>codes</em> zoals ID's en classificaties uit de bronbibliotheek. <em>Identificatoren</em> vertegenwoordigen een specifieke editie van een boek; voorbeelden zijn ISBN, DOI, Open Library ID, Google Books ID of Amazon ID. <em>Classificaties</em> groeperen meerdere vergelijkbare boeken; voorbeelden zijn Dewey Decimal (DCC), UDC, LCC, RVK of GOST. Soms zijn deze codes expliciet gekoppeld in bronbibliotheken, en soms kunnen we ze uit de bestandsnaam of beschrijving halen (voornamelijk ISBN en DOI)."

msgid "page.metadata.background.body5"
msgstr "We kunnen ID's gebruiken om records te vinden in <em>metadata-only collecties</em>, zoals OpenLibrary, ISBNdb of WorldCat/OCLC. Er is een specifieke <em>metadata-tab</em> in onze zoekmachine als u die collecties wilt doorzoeken. We gebruiken overeenkomende records om ontbrekende metadata-velden in te vullen (bijvoorbeeld als een titel ontbreekt), of bijvoorbeeld als “alternatieve titel” (als er een bestaande titel is)."

msgid "page.metadata.background.body6"
msgstr "Kijk om precies te zien waar de metadata van een boek vandaan komt naar het <em>“Technische details” tabblad</em> op een boekpagina. Het bevat een link naar de ruwe JSON voor dat boek, met verwijzingen naar de ruwe JSON van de originele records."

msgid "page.metadata.background.body7"
msgstr "Voor meer informatie, zie de volgende pagina’s: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Zoeken (metadata-tab)</a>, <a %(a_codes)s>Codes Explorer</a>, en <a %(a_example)s>Voorbeeld metadata JSON</a>. Ten slotte kan al onze metadata <a %(a_generated)s>gegenereerd</a> of <a %(a_downloaded)s>gedownload</a> worden als ElasticSearch- en MariaDB-databases."

msgid "page.metadata.openlib.title"
msgstr "Open Library koppeling"

msgid "page.metadata.openlib.body1"
msgstr "Dus als je een bestand met slechte metadata tegenkomt, hoe moet je dat dan repareren? Je kunt naar de bronbibliotheek gaan en de procedures volgen voor het repareren van metadata, maar wat moet je doen als een bestand aanwezig is in meerdere bronnenbibliotheken?"

msgid "page.metadata.openlib.body2"
msgstr "Er is één ID die speciaal wordt behandeld op Anna’s Archive. <strong>Het annas_archive md5-veld op Open Library overschrijft altijd alle andere metadata!</strong> Laten we eerst even terugspoelen en meer leren over Open Library."

msgid "page.metadata.openlib.body3"
msgstr "Open Library werd in 2006 opgericht door Aaron Swartz met als doel “één webpagina voor elk boek ooit gepubliceerd”. Het is een soort Wikipedia voor metadata van boeken: iedereen kan het bewerken, het is vrij gelicenseerd en kan in bulk worden gedownload. Het is een boekendatabase die het meest in lijn is met onze missie — in feite is Anna’s Archive geïnspireerd door Aaron Swartz’ visie en leven."

msgid "page.metadata.openlib.body4"
msgstr "In plaats van het wiel opnieuw uit te vinden, hebben we besloten onze vrijwilligers door te verwijzen naar Open Library. Als je een boek ziet met onjuiste metadata, dan kun je op de volgende manier helpen:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Ga naar de <a %(a_openlib)s>Open Library website</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Vind het juiste boekrecord. <strong>WAARSCHUWING:</strong> zorg ervoor dat je de juiste <strong>editie</strong> selecteert. In Open Library heb je “works” en “editions."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Een “work” kan “Harry Potter en de Steen der Wijzen” zijn."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Een “edition” kan zijn:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "De eerste editie uit 1997, uitgegeven door Bloomsbery, met 256 pagina’s."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "De paperback editie uit 2003, uitgegeven door Raincoast Books, met 223 pagina’s."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "De Poolse vertaling uit 2000, “Harry Potter I Kamie Filozoficzn” door Media Rodzina, met 328 pagina’s."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Al deze edities hebben verschillende ISBN’s en verschillende inhoud, dus zorg ervoor dat je de juiste kiest!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Bewerk het record (of maak deze aan als hij niet bestaat) en voeg zoveel nuttige informatie toe als je kunt! Je bent er nu toch, dus kun je er net zo goed voor zorgen dat het record fantastisch is."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Selecteer onder “ID Numbers” “Anna’s Archive” en voeg de MD5 van het boek toe van Anna’s Archive. Dit is de lange reeks letters en cijfers na “/md5/” in de URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Probeer andere bestanden te vinden in Anna’s Archive die ook bij dit record passen, en voeg die ook toe. In de toekomst kunnen we deze groeperen als duplicaten op de zoekpagina van Anna’s Archive."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Als je klaar bent, schrijf dan de URL op die je zojuist hebt bijgewerkt. Zodra je minstens 30 records hebt bijgewerkt met Anna’s Archive MD5’s, stuur ons dan een <a %(a_contact)s>e-mail</a> en stuur de lijst. We geven je een gratis lidmaatschap voor Anna’s Archive, zodat je dit werk gemakkelijker kunt doen (en als dank voor je hulp). Dit moeten hoogwaardige bewerkingen zijn die aanzienlijke hoeveelheden informatie toevoegen, anders wordt je verzoek afgewezen. Je verzoek wordt ook afgewezen als een van de bewerkingen wordt teruggedraaid of gecorrigeerd door Open Library-moderators."

msgid "page.metadata.openlib.body5"
msgstr "Let op dat dit alleen werkt voor boeken, niet voor academische papers of andere soorten bestanden. Voor andere soorten bestanden raden we nog steeds aan om de bronbibliotheek te zoeken. Het kan enkele weken duren voordat wijzigingen zijn opgenomen in Anna’s Archive, omdat we de nieuwste data dump van Open Library moeten downloaden en onze zoekindex opnieuw moeten genereren."

msgid "page.mirrors.title"
msgstr "Mirrors: oproep voor vrijwilligers"

msgid "page.mirrors.intro"
msgstr "Om de veerkracht van Anna’s Archive te vergroten, zijn we op zoek naar vrijwilligers om mirrors te runnen."

msgid "page.mirrors.text1"
msgstr "We zijn op zoek naar het volgende:"

msgid "page.mirrors.list.run_anna"
msgstr "Je beheert de open source codebase van Anna’s Archive en je werkt regelmatig zowel de code als de data bij."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Jouw versie is duidelijk te onderscheiden als een mirror, bijvoorbeeld “Bob’s Archief, een Anna’s Archive mirror”."

msgid "page.mirrors.list.know_the_risks"
msgstr "Je bent bereid de aanzienlijke risico’s te nemen die met dit werk gepaard gaan. Je hebt een diep begrip van de vereiste operationele beveiliging. De inhoud van <a %(a_shadow)s>deze</a> <a %(a_pirate)s>berichten</a> is voor jou vanzelfsprekend."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "Je bent bereid bij te dragen aan onze <a %(a_codebase)s>codebase</a> — in samenwerking met ons team — om dit te realiseren."

msgid "page.mirrors.list.maybe_partner"
msgstr "In eerste instantie zullen we je geen toegang geven tot de downloads van onze partner server, maar als alles goed gaat, kunnen we dat met je delen."

msgid "page.mirrors.expenses.title"
msgstr "Hostingkosten"

msgid "page.mirrors.expenses.text1"
msgstr "We zijn bereid om de kosten voor hosting en VPN te dekken, aanvankelijk tot $200 per maand. Dit is voldoende voor een basis zoekserver en een DMCA-beschermde proxy."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "We betalen alleen voor hosting zodra je alles hebt opgezet en hebt aangetoond dat je in staat bent het archief up-to-date te houden met updates. Dit betekent dat je de eerste 1-2 maanden zelf moet betalen."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Je tijd wordt niet vergoed (en die van ons ook niet), aangezien dit puur vrijwilligerswerk is."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Als je jezelf significant bezighoudt met de ontwikkeling en operaties van ons werk, kunnen we bespreken om meer van de donatie-inkomsten met je te delen, zodat je deze naar behoefte kunt inzetten."

msgid "page.mirrors.getting_started.title"
msgstr "Aan de slag"

msgid "page.mirrors.getting_started.text1"
msgstr "Neem alsjeblieft <strong>>geen contact met ons op</strong> om toestemming te vragen of voor basisvragen. Daden spreken luider dan woorden! Alle informatie is beschikbaar, dus ga gewoon aan de slag met het opzetten van jouw mirror."

msgid "page.mirrors.getting_started.text2"
msgstr "Voel je wel vrij om tickets of merge-requests te plaatsen op onze Gitlab wanneer je tegen problemen aanloopt. We moeten mogelijk enkele mirror-specifieke functies met je bouwen, zoals het rebranden van “Anna’s Archive” naar jouw website naam, (aanvankelijk) het uitschakelen van gebruikersaccounts, of het linken naar onze hoofdpagina vanaf boekpagina’s."

msgid "page.mirrors.getting_started.text3"
msgstr "Zodra je jouw mirror draaiende hebt, neem dan alsjeblieft contact met ons op. We zouden graag je OpSec beoordelen, en zodra dat solide is, zullen we naar jouw mirror linken en nauwer met je samenwerken."

msgid "page.mirrors.getting_started.text4"
msgstr "Bij voorbaat dank aan iedereen die op deze manier wil bijdragen! Het is niet voor de mensen met een zwak hart, maar het zou de levensduur van de grootste écht open bibliotheek in de menselijke geschiedenis versterken."

msgid "page.partner_download.header"
msgstr "Download van partnerwebsite"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Trage downloads zijn alleen beschikbaar via de officiële website. Bezoek %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Trage downloads zijn niet beschikbaar via VPN's van Cloudflare of vanaf IP-adressen van Cloudflare."

msgid "page.partner_download.wait_banner"
msgstr "Wacht alsjeblieft <span %(span_countdown)s>%(wait_seconds)s</span> seconden om dit bestand te downloaden."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Download nu</a>"

msgid "page.partner_download.li4"
msgstr "Bedankt voor het wachten, dit houdt de website gratis toegankelijk voor iedereen! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Waarschuwing: In de afgelopen 24 uur zijn er veel downloads vanaf jouw IP-adres gedaan. Downloads kunnen trager zijn dan gebruikelijk."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads vanaf jouw IP-adres in de afgelopen 24 uur: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Deze waarschuwing kan komen doordat je een VPN of gedeelde internetverbinding gebruikt, of als je internetprovider IP's deelt."

msgid "page.partner_download.wait"
msgstr "Om iedereen de kans te geven bestanden gratis te downloaden, moet je wachten voordat je dit bestand kunt downloaden."

msgid "page.partner_download.li1"
msgstr "Doorzoek Anna’s Archive in een ander tabblad terwijl je wacht (als uw browser het verversen van achtergrondtabbladen ondersteunt)."

msgid "page.partner_download.li2"
msgstr "Voel je vrij om op het laden van meerdere downloadpagina's tegelijk te wachten (maar download alsjeblieft maar één bestand tegelijk per server)."

msgid "page.partner_download.li3"
msgstr "Zodra je een downloadlink ontvangt, is deze enkele uren geldig."

msgid "layout.index.header.title"
msgstr "Anna’s Archief"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Vermelding in Anna’s Archief"

msgid "page.scidb.download"
msgstr "Download"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Word <a %(a_donate)s>lid</a> om de toegankelijkheid en het langdurige behoud van menselijke kennis te ondersteunen."

msgid "page.scidb.please_donate_bonus"
msgstr "Als bonus laadt 🧬&nbsp;SciDB sneller voor leden, zonder enige limieten."

msgid "page.scidb.refresh"
msgstr "Werkt het niet? Probeer te <a %(a_refresh)s>vernieuwen</a>."

msgid "page.scidb.no_preview_new"
msgstr "Nog geen voorbeeld beschikbaar. Download het bestand van <a %(a_path)s>Anna’s Archive</a>."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB is een voortzetting van Sci-Hub, met de vertrouwde interface en directe weergave van PDF's. Voer je DOI in om te bekijken."

msgid "page.home.scidb.text3"
msgstr "Wij hebben de volledige Sci-Hub collectie, maar ook nieuwe papers. De meeste kunnen direct worden bekeken met een vertrouwde interface, die vergelijkbaar is met die van Sci-Hub. Sommige kunnen worden gedownload via externe bronnen. In dat geval tonen wij links naar die bronnen."

msgid "page.search.title.results"
msgstr "%(search_input)s - Zoeken"

msgid "page.search.title.new"
msgstr "Nieuwe zoekopdracht"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Alleen opnemen"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Uitsluiten"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Niet gecontroleerd"

msgid "page.search.tabs.download"
msgstr "Download"

msgid "page.search.tabs.journals"
msgstr "Wetenschappelijke artikelen"

msgid "page.search.tabs.digital_lending"
msgstr "Digitaal lenen"

msgid "page.search.tabs.metadata"
msgstr "Metadata"

msgid "common.search.placeholder"
msgstr "Zoek op titel, auteur, taal, bestandstype, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Zoeken"

msgid "page.search.search_settings"
msgstr "Zoekinstellingen"

msgid "page.search.submit"
msgstr "Zoeken"

msgid "page.search.too_long_broad_query"
msgstr "De zoekopdracht duurde te lang. Dit is gebruikelijk bij brede opdrachten. De filteraantallen kunnen onnauwkeurig zijn."

msgid "page.search.too_inaccurate"
msgstr "De zoekopdracht duurde te lang. Dit kan betekenen dat je onnauwkeurige resultaten te zien krijgt. Soms helpt het om de pagina te <a %(a_reload)s>herladen</a>."

msgid "page.search.filters.display.header"
msgstr "Weergeven"

msgid "page.search.filters.display.list"
msgstr "Lijst"

msgid "page.search.filters.display.table"
msgstr "Tabel"

msgid "page.search.advanced.header"
msgstr "Geavanceerd"

msgid "page.search.advanced.description_comments"
msgstr "Beschrijvingen en metadata-opmerkingen doorzoeken"

msgid "page.search.advanced.add_specific"
msgstr "Specifiek zoekveld toevoegen"

msgid "common.specific_search_fields.select"
msgstr "(specifiek veld doorzoeken)"

msgid "page.search.advanced.field.year_published"
msgstr "Publicatiejaar"

msgid "page.search.filters.content.header"
msgstr "Inhoud"

msgid "page.search.filters.filetype.header"
msgstr "Bestandstype"

msgid "page.search.more"
msgstr "meer…"

msgid "page.search.filters.access.header"
msgstr "Toegang"

msgid "page.search.filters.source.header"
msgstr "Bron"

msgid "page.search.filters.source.scraped"
msgstr "gescrapet en open source door AA"

msgid "page.search.filters.language.header"
msgstr "Taal"

msgid "page.search.filters.order_by.header"
msgstr "Sorteren op"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Meest relevant"

msgid "page.search.filters.sorting.newest"
msgstr "Nieuwste"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publicatiejaar)"

msgid "page.search.filters.sorting.oldest"
msgstr "Oudste"

msgid "page.search.filters.sorting.largest"
msgstr "Grootste"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(bestandsgrootte)"

msgid "page.search.filters.sorting.smallest"
msgstr "Kleinste"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open source)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Willekeurig"

msgid "page.search.header.update_info"
msgstr "De zoekindex wordt iedere maand geüpdatet. Momenteel bevat de index invoergegevens tot %(last_data_refresh_date)s. Voor meer technische informatie, ga naar de %(link_open_tag)sdatasets pagina</a>."

msgid "page.search.header.codes_explorer"
msgstr "Gebruik de <a %(a_href)s Codes Explorer</a> om de zoekindex te doorzoeken op codes."

msgid "page.search.results.search_downloads"
msgstr "Typ in het veld om onze catalogus van %(count)s direct downloadbare bestanden te doorzoeken, die we <a %(a_preserve)s>voor altijd behouden</a>."

msgid "page.search.results.help_preserve"
msgstr "In feite kan iedereen helpen bij het behoud van deze bestanden door onze <a %(a_torrents)s>gebundelde torrentlijst</a> te seeden."

msgid "page.search.results.most_comprehensive"
msgstr "Momenteel hebben we 's werelds meest uitgebreide open catalogus van boeken, papers en andere geschreven werken. We spiegelen Sci-Hub, Library Genesis, Z-Library <a %(a_datasets)s>en meer</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Als je andere 'schaduwbibliotheken' vindt die we moeten spiegelen of vragen hebt, kun je contact met ons opnemen via %(email)s."

msgid "page.search.results.dmca"
msgstr "<a %(a_copyright)s>Klik hier</a> voor DMCA-/auteursrechtclaims."

msgid "page.search.results.shortcuts"
msgstr "Tip: Gebruik de sneltoetsen “/” (focus op zoeken), “Enter” (zoeken), “j” (omhoog) en “k” (omlaag) om sneller te navigeren."

msgid "page.search.results.looking_for_papers"
msgstr "Op zoek naar papers?"

msgid "page.search.results.search_journals"
msgstr "Typ in het veld om onze catalogus van %(count)s wetenschappelijke artikelen te doorzoeken, die we voor altijd <a %(a_preserve)s>bewaren</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Typ in het veld om bestanden in bibliotheken die digitaal uitlenen te zoeken."

msgid "page.search.results.digital_lending_info"
msgstr "Deze zoekindex bevat momenteel metadata van de Controlled Digital Lending-bibliotheek van Internet Archive. <a %(a_datasets)s>Meer informatie over onze datasets</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Bekijk <a %(a_wikipedia)s>Wikipedia</a> en <a %(a_mobileread)s>MobileRead Wiki</a> voor meer bibliotheken die digitaal uitlenen."

msgid "page.search.results.search_metadata"
msgstr "Typ in het veld om naar metadata van bibliotheken te zoeken. Dit kan handig zijn bij het <a %(a_request)s>aanvragen van een bestand</a>."

msgid "page.search.results.metadata_info"
msgstr "Deze zoekindex bevat momenteel metadata van verschillende bronnen. <a %(a_datasets)s>Meer informatie over onze datasets</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "We tonen de originele velden met metadata. We combineren deze niet."

msgid "page.search.results.metadata_info_more"
msgstr "Er zijn vele bronnen met metadata voor geschreven werken van over de hele wereld. <a %(a_wikipedia)s>Deze Wikipedia-pagina</a> is een goed begin, maar als je andere goede lijsten kent, laat het ons dan vooral weten."

msgid "page.search.results.search_generic"
msgstr "Typ in het veld om te zoeken."

msgid "page.search.results.these_are_records"
msgstr "Dit zijn metadata records, <span %(classname)s>geen</span> downloadbare bestanden."

msgid "page.search.results.error.header"
msgstr "Fout bij de zoekopdracht."

msgid "page.search.results.error.unknown"
msgstr "Probeer de pagina te <a %(a_reload)s>herladen</a>. Stuur een e-mail naar %(email)s als het probleem zich blijft voordoen."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Geen bestanden gevonden.</span> Probeer het met minder zoektermen en filters."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Soms gebeurt dit onjuist wanneer de zoekserver traag is. In dergelijke gevallen kan <a %(a_attrs)s>herladen</a> helpen."

msgid "page.search.found_matches.main"
msgstr "We hebben overeenkomsten gevonden in %(in)s. Je kunt verwijzen naar de URL daar als je <a %(a_request)s>een bestand aanvraagt</a>."

msgid "page.search.found_matches.journals"
msgstr "Wetenschappelijke artikelen (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Digitaal lenen (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Resultaten %(from)s-%(to)s (%(total)s totaal)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ gedeeltelijke overeenkomsten"

msgid "page.search.results.partial"
msgstr "%(num)d gedeeltelijke overeenkomsten"

msgid "page.volunteering.title"
msgstr "Vrijwilligerswerk & Premies"

msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive is afhankelijk van vrijwilligers zoals jij. We verwelkomen alle betrokkenheidsniveaus en hebben twee hoofdcategorieën van hulp die we zoeken:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Licht vrijwilligerswerk:</span> als je slechts een paar uur hier en daar kunt besteden, zijn er nog steeds genoeg manieren waarop je kunt helpen. We belonen consistente vrijwilligers met <span %(bold)s>🤝 lidmaatschappen van Anna’s Archive</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Zwaar vrijwilligerswerk (USD$50-USD$5,000 beloningen):</span> als je veel tijd en/of middelen aan onze missie kunt besteden, werken we graag nauwer met je samen. Uiteindelijk kun je lid worden van het kernteam. Hoewel we een krap budget hebben, kunnen we <span %(bold)s>💰 monetaire beloningen</span> toekennen voor het meest intensieve werk."

msgid "page.volunteering.intro.text2"
msgstr "Als je geen tijd kunt vrijmaken om vrijwilligerswerk te doen, kunt je ons nog steeds enorm helpen door <a %(a_donate)s>geld te doneren</a>, <a %(a_torrents)s>onze torrents te seeden</a>, <a %(a_uploading)s>boeken te uploaden</a>, of <a %(a_help)s>je vrienden te vertellen over Anna’s Archive</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Bedrijven:</span> we bieden directe toegang met hoge snelheid tot onze collecties aan in ruil voor een donatie op ondernemingsniveau of in ruil voor nieuwe collecties (bijv. nieuwe scans, datasets die met OCR zijn omgezet, verrijking van onze data). <a %(a_contact)s>Neem contact met ons op</a> als dit je aanspreekt. Zie ook onze <a %(a_llm)s>LLM-pagina</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Licht vrijwilligerswerk"

msgid "page.volunteering.section.light.text1"
msgstr "Als je een paar uur over hebt, kun je op verschillende manieren helpen. Zorg ervoor dat je lid wordt van de <a %(a_telegram)s>vrijwilligerschat op Telegram</a>."

msgid "page.volunteering.section.light.text2"
msgstr "Als blijk van waardering geven we meestal 6 maanden “Gelukkige Bibliothecaris” voor basis mijlpalen, en meer voor voortgezet vrijwilligerswerk. Alle mijlpalen vereisen werk van hoge kwaliteit — slordig werk schaadt ons meer dan het helpt en we zullen het afwijzen. Stuur ons een <a %(a_contact)s>e-mail</a> wanneer je een mijlpaal bereikt."

msgid "page.volunteering.table.header.task"
msgstr "Taak"

msgid "page.volunteering.table.header.milestone"
msgstr "Mijlpaal"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Het woord van Anna’s Archief verspreiden. Bijvoorbeeld door boeken aan te bevelen op AA, te linken naar onze blogposts, of mensen in het algemeen naar onze website te leiden."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links of screenshots."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Deze zouden moeten laten zien dat je iemand vertelt over Anna’s Archief, en dat zij je bedanken."

msgid "page.volunteering.table.open_library.task"
msgstr "Verbeter metadata door <a %(a_metadata)s>te linken</a> met Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Je kunt de <a %(a_list)s >lijst van willekeurige metadata-problemen</a> gebruiken als startpunt."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Zorg ervoor dat je een opmerking achterlaat bij problemen die je oplost, zodat anderen je werk niet dupliceren."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links van records die je verbetert hebt."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Vertalen</a> van de website."

msgid "page.volunteering.table.translate.milestone"
msgstr "Volledig vertalen van een taal (als deze nog niet bijna voltooid was.)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "Verbeter de Wikipedia-pagina voor Anna’s Archief in jouw taal. Voeg informatie toe van de Wikipedia-pagina van AA in andere talen, en van onze website en blog. Voeg verwijzingen naar AA toe op andere relevante pagina’s."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link naar de bewerkingsgeschiedenis die laat zien dat je significante bijdragen hebt geleverd."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Boek (of paper, etc.) verzoeken vervullen op de Z-Library of de Library Genesis forums. We hebben geen eigen verzoeksysteem voor boeken, maar we mirroren die bibliotheken, dus het verbeteren die bibliotheken maakt Anna’s Archive ook beter."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links of screenshots van verzoeken die je hebt vervuld."

msgid "page.volunteering.table.misc.task"
msgstr "Kleine taken gepost in onze <a %(a_telegram)s>vrijwilligerschat op Telegram</a>. Meestal voor lidmaatschap, soms voor kleine beloningen."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Kleine taken gepost in onze vrijwilligerschatgroep."

msgid "page.volunteering.table.misc.milestone"
msgstr "Afhankelijk van de taak."

msgid "page.volunteering.section.bounties.heading"
msgstr "Beloningen"

msgid "page.volunteering.section.bounties.text1"
msgstr "We zijn altijd op zoek naar mensen met solide programmeer- of offensieve beveiligingsvaardigheden om betrokken te raken. Je kunt een serieuze bijdrage leveren aan het behouden van het erfgoed van de mensheid."

msgid "page.volunteering.section.bounties.text2"
msgstr "Als dank geven we lidmaatschappen weg voor solide bijdragen. Als grote dank geven we monetaire beloningen voor bijzonder belangrijke en moeilijke taken. Dit moet niet worden gezien als een vervanging voor een baan, maar het is een extra stimulans en kan helpen met gemaakte kosten."

msgid "page.volunteering.section.bounties.text3"
msgstr "Het merendeel van onze code is open source, en we zullen dat ook van jouw code vragen bij het toekennen van de beloning. Er zijn enkele uitzonderingen die we op individuele basis kunnen bespreken."

msgid "page.volunteering.section.bounties.text4"
msgstr "Beloningen worden toegekend aan de eerste persoon die een taak voltooit. Voel je vrij om een opmerking te plaatsen bij een beloningsticket om anderen te laten weten dat je ergens aan werkt, zodat anderen kunnen wachten of contact met je kunnen opnemen om samen te werken. Maar wees je ervan bewust dat anderen nog steeds vrij zijn om eraan te werken en proberen je voor te zijn. We kennen echter geen beloningen toe voor slordig werk. Als twee hoogwaardige inzendingen dicht bij elkaar worden gedaan (binnen een dag of twee), kunnen we ervoor kiezen om beloningen aan beide toe te kennen, naar eigen goeddunken, bijvoorbeeld 100%% voor de eerste inzending en 50%% voor de tweede inzending (dus 150%% in totaal)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Voor de grotere beloningen (vooral beloningen voor scraping), neem contact met ons op wanneer je ongeveer ~5%% ervan hebt voltooid en je ervan overtuigd bent dat je methode schaalbaar is naar de volledige mijlpaal. Je zult je methode met ons moeten delen zodat we feedback kunnen geven. Op deze manier kunnen we ook beslissen wat te doen als er meerdere mensen dicht bij een beloning komen, zoals mogelijk toekennen aan meerdere mensen, mensen aanmoedigen om samen te werken, etc."

msgid "page.volunteering.section.bounties.text6"
msgstr "WAARSCHUWING: de taken met hoge beloningen zijn <span %(bold)s>moeilijk</span> — het kan verstandig zijn om met gemakkelijkere te beginnen."

msgid "page.volunteering.section.bounties.text7"
msgstr "Ga naar onze <a %(a_gitlab)s>Gitlab issues lijst</a> en sorteer op “Label priority”. Dit toont ongeveer de volgorde van taken die voor ons belangrijk zijn. Taken zonder expliciete beloningen komen nog steeds in aanmerking voor lidmaatschap, vooral die gemarkeerd als “Accepted” en “Anna’s favorite”. Je zou misschien het beste kunnen beginnen met een “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates over <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, de grootste echt open bibliotheek in de menselijke geschiedenis."

msgid "layout.index.title"
msgstr "Anna’s Archief"

msgid "layout.index.meta.description"
msgstr "'s Werelds grootste opensourcebibliotheek met open data. Een spiegeling van Sci-Hub, Library Genesis, Z-Library en meer."

msgid "layout.index.meta.opensearch"
msgstr "Anna’s Archief doorzoeken"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’s Archive heeft jouw hulp nodig!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Velen proberen ons neer te halen, maar wij vechten terug."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Als je nu doneert, krijg je <strong>dubbel</strong> zoveel snelle downloads."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Geldig tot het einde van deze maand."

msgid "layout.index.header.nav.donate"
msgstr "Doneren"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Het redden van menselijke kennis: een geweldig cadeau voor de feestdagen!"

msgid "layout.index.header.banner.surprise"
msgstr "Verras een dierbare, geef hem of haar een account met lidmaatschap."

msgid "layout.index.header.banner.mirrors"
msgstr "Om de veerkracht van Anna's Archief te vergroten, zijn we op zoek naar vrijwilligers die 'mirrors' kunnen draaien."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Het perfecte valentijnscadeau!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "We hebben een nieuwe donatiemogelijkheid: %(method_name)s. Overweeg alsjeblieft te %(donate_link_open_tag)sdoneren</a> — het is niet goedkoop om deze website te draaien en je donatie maakt hierbij echt het verschil. Heel erg bedankt."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "We houden een fondsenwerving om <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">een kopie</a> van 's werelds grootste schaduwbibliotheek voor stripboeken te maken. Bedankt voor je steun! <a href=\"/donate\">Doneren.</a> En als je niet kunt doneren, probeer ons dan te steunen door je vrienden over ons te vertellen en ons te volgen op <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Recente downloads:"

msgid "layout.index.header.nav.search"
msgstr "Zoeken"

msgid "layout.index.header.nav.faq"
msgstr "FAQ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Metadata verbeteren"

msgid "layout.index.header.nav.volunteering"
msgstr "Vrijwilligerswerk & Premies"

msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activiteit"

msgid "layout.index.header.nav.codes"
msgstr "Code Verkenner"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM-data"

msgid "layout.index.header.nav.home"
msgstr "Startpagina"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Vertalen ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Inloggen/registreren"

msgid "layout.index.header.nav.account"
msgstr "Account"

msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archief"

msgid "layout.index.footer.list2.header"
msgstr "Blijf in contact"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA-/auteursrechtclaims"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Geavanceerd"

msgid "layout.index.header.nav.security"
msgstr "Beveiliging"

msgid "layout.index.footer.list3.header"
msgstr "Alternatieven"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "niet aangesloten"

msgid "page.search.results.issues"
msgstr "❌ Dit bestand heeft mogelijk problemen."

msgid "page.search.results.fast_download"
msgstr "Snelle download"

msgid "page.donate.copy"
msgstr "kopiëren"

msgid "page.donate.copied"
msgstr "gekopieerd!"

msgid "page.search.pagination.prev"
msgstr "Vorige"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Volgende"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% van het geschreven erfgoed van de mensheid voor eeuwig bewaard %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datasets ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Download via:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "We hebben meerdere download opties voor het geval een van de opties het niet doet. Alle opties leiden naar hetzelfde bestand."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Let op: Anna’s Archive host de inhoud van deze site niet. We linken slechts naar andere websites. Als je denkt dat je een geldige DMCA klacht hebt, bekijk dan de %(about_link)sover ons pagina</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Anonieme Mirror #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Doneer"

#~ msgid "page.donate.header"
#~ msgstr "Doneer"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive is een non-profit, open-source project dat volledig door vrijwilligers wordt beheerd door vrijwilligers. We nemen donaties aan om onze kosten te dekken, zoals bijvoorbeeld hosting, domeinnamen, ontwikkeling en andere kosten."

#~ msgid "page.donate.text2"
#~ msgstr "Met jouw bijdrage kunnen we deze site draaiende houden, de functionaliteit vergroten en meer collecties helpen behouden."

#~ msgid "page.donate.text3"
#~ msgstr "Recente donaties: %(donations)s. Bedankt iedereen voor jullie gulle giften. We waarderen het erg dan jullie ons jullie donatie toevertrouwen, hoeveel je ook kunt missen."

#~ msgid "page.donate.text4"
#~ msgstr "Om te doneren, selecteer je voorkeursmethode. Als je problemen ondervindt, neem dan contact op via %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Creditcard"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Cryptocurrency"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Vragen"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Ga naar %(link_open_tag)sdeze pagina</a> en volg de instructies door de QR code te scannen of op de \"paypal.me\" link te klikken. Als dit niet werkt, probeer dan de pagina te verversen, omdat dit je een ander account zou kunnen geven."

#~ msgid "page.donate.cc.header"
#~ msgstr "Creditcard"

#~ msgid "page.donate.cc.text1"
#~ msgstr "We gebruiken Sendwyre om geld in onze Bitcoin (BTC) wallet te storten. Dit kan ongeveer 5 minuten duren om te voltooien."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Deze methode heeft een minimumdonatie van $30 en kosten van ongeveer $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Stappen:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Kopieer ons Bitcoin (BTC) wallet adres: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Ga naar %(link_open_tag)sdeze pagina</a> en klik op \"buy crypto instantly\" (koop direct crypto)"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Plak ons wallet adres en volg de instructies"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Cryptocurrency"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(werkt ook met BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Gebuik %(link_open_tag)sdit Alipay account</a> om je donatie over te boeken. Als dit niet werkt, probeer dan de pagina te verversen. Dit geeft je mogelijk een nieuw account."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Deze donatiemethode is momenteel buiten werking. Probeer het later opnieuw. Bedankt dat je wilt doneren, we waarderen het erg!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Gebruik %(link_open_tag)sdeze Pix pagina</a> om je donatie over te boeken. Als dit niet werkt, probeer dan de pagina te verversen. Dit geeft je mogelijk een nieuw account."

#~ msgid "page.donate.faq.header"
#~ msgstr "Veel gestelde vragen"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> is een project dat beoogd om alle bestaande boeken te rangschikken door date van verschillende bronnen samen te voegen. We houden ook bij hoe ver de mensheid is met het digitaal beschikbaar maken van al deze boeken door “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">schaduw bibliotheken</a>”. Lees meer <a href=\"/about\">over ons.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Boek (elke)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Start"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datasets ▶ ISBN's ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Niet gevonden"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” is geen geldig ISBN nummer ISBN nummers zijn 10 tot 13 karakters lang (zonder de optionele streepjes). Alle karakters moeten cijfers zijn, behalve het laatste karakter, dat ook \"X\" mag zijn. Het laatste karakter is het \"controlenummer\", dat overeen moet komen met een controlegetal dat met de andere getallen is berekend. Het moet ook een geldige cijferreeks zijn die door het ISBN Agenschap is toegewezen."

#~ msgid "page.isbn.results.text"
#~ msgstr "Overeenkomstige bestanden in onze database:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Geen overeenkomsten gevonden in onze database."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Zoeken ▶ %(num)d+ resultaten voor <span class=\"italic\">%(search_input)s</span> (in schaduw bibliotheek metadata)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Zoeken ▶ %(num)d resultaten voor <span class=\"italic\">%(search_input)s</span> (in schaduwbibliotheek metadata)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Zoeken ▶ zoekfout voor <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Zoeken ▶ Nieuwe zoekopdracht"

#~ msgid "page.donate.header.text3"
#~ msgstr "Je kunt ook een donatie doen zonder een account aan te maken:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Eenmalige donatie (geen voordelen)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Als je al cryptocurrency hebt, zijn dit onze adressen."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Ontzettend bedankt voor je hulp! Dit project zou niet mogelijk zijn zonder jou."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "We gebruiken PayPal Crypto om je te laten doneren met PayPal (VS). Zo blijven we anoniem. We waarderen het dat je de tijd neemt om te leren hoe je op deze manier kunt doneren; dit helpt ons enorm."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Volg de instructies om Bitcoin (BTC) te kopen. Je hoeft slechts het bedrag te kopen dat je wil doneren."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "<em>Maak je geen zorgen</em> als je wat Bitcoin kwijtraakt door fluctuaties of kosten. Dit hoort bij cryptovaluta, maar zorgt er wel voor dat we anoniem kunnen handelen."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Voer ons Bitcoin (BTC)-adres in als ontvanger en volg de instructies om je donatie te versturen:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Gebruik <a %(a_account)s>dit Alipay-account</a> om je donatie naar te sturen."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Gebruik <a %(a_account)s>dit Pix-account</a> om je donatie naar te sturen."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Als je betaalmethode niet in de lijst staat, kun je het beste <a href=\"https://paypal.com/\">PayPal</a> of <a href=\"https://coinbase.com/\">Coinbase</a> downloaden op je telefoon en daar Bitcoin (BTC) kopen. Je kunt dit dan naar ons adres overmaken: %(address)s. In de meeste landen duurt dit slechts een paar minuten."

#~ msgid "page.search.results.error.text"
#~ msgstr "Probeer <a href=\"javascript:location.reload()\">de pagina opnieuw te laden</a>. Als het probleem zich blijft voordoen, laat het ons dan weten op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "<a href=\"/login\">Log in of registreer je</a> om lid te worden. Als je geen account wil maken, kies je hierboven voor 'Eenmalige donatie'. Bedankt voor je steun!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Start"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Over"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Doneren"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datasets"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Mobiele app"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Vertalen"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Spiegelt %(libraries)s en meer."

#~ msgid "page.home.preservation.text"
#~ msgstr "We behouden boeken, papers, stripboeken, tijdschriften en meer door deze materialen van verschillende <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">schaduwbibliotheken</a> samen te brengen op één plek. Doordat de gegevens grootschalig kunnen worden gekopieerd, zijn overal ter wereld vele exemplaren te vinden. Deze brede distributie zorgt er samen met de opensourcecode tevens voor dat onze website weerbaar is tegen aanvallen. Meer informatie over <a href=\"/datasets\">onze datasets</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datasets ▶ DOI's ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Niet gevonden"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" lijkt niet op een DIO. DOI's beginnen met \"10\" en bevatten een schuine streep."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Canonieke URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Dit bestand kan in %(link_open_tag)sSci-Hub</a> staan."

#~ msgid "page.doi.results.text"
#~ msgstr "Bijbehorende bestanden in onze database:"

#~ msgid "page.doi.results.none"
#~ msgstr "Geen bijpassende bestanden gevonden in onze database."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Snelle downloads</strong> Je hebt vandaag geen snelle downloads meer over. Neem contact op met Anna via %(email)s als je interesse hebt om je lidmaatschap te upgraden."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Vandaag heb je geen snelle downloads meer over. Neem contact op met Anna via %(email)s als je interesse hebt om je lidmaatschap te upgraden."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Kan ik op andere manieren bijdragen?</div> Ja! Kijk eens op onze <a href=\"/about\">over ons pagina</a> onder “Hoe kan ik helpen”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Ik vind het maar niks dat jullie Anna’s Archief monetariseren!</div> Als je het niet eens bent met de manier waarop we ons project draaien, kun je je eigen schaduwbibliotheek opzetten! Al onze code en data zijn opensource, dus niets houdt je tegen. ;)"

#~ msgid "page.request.title"
#~ msgstr "Boeken aanvragen"

#~ msgid "page.request.text1"
#~ msgstr "Kun je e-boeken voorlopig aanvragen op het <a %(a_forum)s>forum van Libgen.rs</a>? Je kunt daar een account maken en berichten plaatsen in een van deze discussies:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Gebruik <a %(a_ebook)s>deze discussie</a> voor e-boeken.</li><li %(li_item)s>Gebruik <a %(a_regular)s>deze discussie</a> voor boeken die niet beschikbaar zijn als e-boeken.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Zorg er in beide gevallen voor dat je de regels in de discussies volgt."

#~ msgid "page.upload.title"
#~ msgstr "Uploaden"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Grote uploads"

#~ msgid "page.about.title"
#~ msgstr "Over"

#~ msgid "page.about.header"
#~ msgstr "Over"

#~ msgid "page.home.search.header"
#~ msgstr "Zoeken"

#~ msgid "page.home.search.intro"
#~ msgstr "Doorzoek onze catalogus."

#~ msgid "page.home.random_book.header"
#~ msgstr "Willekeurig boek"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Ga naar een willekeurig boek uit de catalogus."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Willekeurig boek"

#~ msgid "page.about.text1"
#~ msgstr "Anna’s Archief is een non-profit-, opensource-zoekmachine voor '<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">schaduwbibliotheken</a>'. Het is gemaakt door <a href=\"http://annas-blog.org\">Anna</a>, die vond dat er een centrale plek nodig was om boeken, papers, stripboeken, tijdschriften en andere documenten te zoeken."

#~ msgid "page.about.text4"
#~ msgstr "Als je een gegronde DMCA-klacht hebt, kijk dan onderaan deze pagina of neem contact op via %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Verken boeken"

#~ msgid "page.home.explore.intro"
#~ msgstr "Dit is een combinatie van populaire boeken en boeken die een speciale betekenis hebben voor de wereld van schaduwbibliotheken en digitaal behoud."

#~ msgid "page.wechat.header"
#~ msgstr "Onofficiële WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "We hebben een onofficiële WeChat-pagina die door een lid van de community wordt bijgehouden. Gebruik onderstaande code voor toegang."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Over"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Mobiele app"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Onofficiële WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Boeken aanvragen"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Uploaden"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Vrienden verwijzen"

#~ msgid "page.about.help.header"
#~ msgstr "Hoe je kunt helpen"

#~ msgid "page.refer.title"
#~ msgstr "Verwijs vrienden voor snelle bonusdownloads"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Leden kunnen vrienden doorverwijzen en bonusdownloads verdienen."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Voor elke vriend die lid wordt:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "krijgt <strong>de vriend</strong> %(percentage)s%% bonusdownloads bovenop de reguliere dagelijkse downloads gedurende het lidmaatschap."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "krijg <strong>jij</strong> hetzelfde aantal bonusdownloads bovenop je reguliere dagelijkse downloads gedurende het lidmaatschap van je vriend (voor een totaal van max. %(max)s bonusdownloads op elk willekeurig moment). Voor deze bonusdownloads moet je een actief lidmaatschap hebben."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Voorbeeld:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Je vriend gebruikt jouw verwijzingslink om zich drie maanden aan te melden voor het lidmaatschap 'Blije bibliothecaris' met %(num)s snelle downloads."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Gedurende die drie maanden ontvangt deze dagelijks %(num)s bonusdownloads."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Gedurende diezelfde drie maanden ontvang jij ook dagelijks %(num)s bonusdownloads."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Verwijzingslink:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Log in</a> en word lid om vrienden te verwijzen."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Word lid</a> om vrienden te verwijzen."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Of voeg %(referral_suffix)s toe aan het einde van een link, waarmee de verwijzing wordt bijgehouden als je vriend lid wordt."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Doneer het totaalbedrag van %(total)s aan <a %(a_account)s>dit Alipay-account"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Je kunt ze ook <a %(a_upload)s>hier</a> uploaden naar Z-Library."

#~ msgid "page.home.mirrors.body"
#~ msgstr "We zijn op zoek naar vrijwilligers die kunnen helpen bij het spiegelen om Anna's Archief weerbaarder te maken. <a href=\"/mirrors\">Meer informatie...</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Mirrors: oproep voor vrijwilligers"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "alleen deze maand!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub heeft <a %(a_closed)s>het uploaden</a> van nieuwe papers gepauzeerd."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selecteer een betaalwijze. We geven korting op cryptobetalingen %(bitcoin_icon)s omdat die ons (veel) minder kosten."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selecteer een betaalwijze. Er zijn momenteel alleen cryptobetaalwijzen %(bitcoin_icon)s beschikbaar, omdat traditionele betalingsverwerkers weigeren met ons samen te werken."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "We kunnen creditcards/debetkaarten niet direct ondersteunen, omdat banken niet met ons willen werken. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Er zijn echter verschillende manieren om toch met credit-/debitcards te betalen, namelijk via onze andere betaalmethoden:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Langzame en externe downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Als je voor het eerst crypto gebruikt, raden we je aan Bitcoin (de oorspronkelijke en meestgebruikte cryptovaluta) te kopen en doneren met %(option1)s, %(option2)s of %(option3)s."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links van records die je hebt verbeterd."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links of screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links of screenshots van verzoeken die je hebt vervuld."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Als je geïnteresseerd bent in het spiegelen van deze datasets voor <a %(a_faq)s>archivering</a> of <a %(a_llm)s>LLM-trainingsdoeleinden</a>, neem dan contact met ons op."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Als je geïnteresseerd bent in het mirroren van deze dataset voor <a %(a_archival)s>archivering</a> of <a %(a_llm)s>LLM-training</a> doeleinden, neem dan contact met ons op."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Hoofdwebsite"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN-landinformatie"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Als je geïnteresseerd bent in het mirroren van deze dataset voor <a %(a_archival)s>archiverings</a> of <a %(a_llm)s>LLM-trainings</a> doeleinden, neem dan contact met ons op."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Het Internationale ISBN Agentschap geeft regelmatig de reeksen vrij die het heeft toegewezen aan nationale ISBN agentschappen. Hieruit kunnen we afleiden tot welk land, regio of taalgroep dit ISBN behoort. We gebruiken deze data momenteel indirect, via de <a %(a_isbnlib)s>isbnlib</a> Python-bibliotheek."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Middelen"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Laatst bijgewerkt: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN website"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Exclusief “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Onze inspiratie voor het verzamelen van metadata komt voort uit Aaron Swartz' doel voor \"één webpagina voor elk boek dat ooit is gepubliceerd\", waarvoor hij <a %(a_openlib)s>Open Library</a> creëerde."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dat project heeft het goed gedaan. Onze unieke positie stelt ons echter in staat om metadata te verkrijgen die zij niet kunnen krijgen."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Een andere inspiratiebron was onze wens om te weten <a %(a_blog)s>hoeveel boeken er in de wereld zijn</a>, zodat we kunnen berekenen hoeveel boeken we nog moeten redden."

#~ msgid "page.partner_download.text1"
#~ msgstr "Om iedereen de kans te geven bestanden gratis te downloaden, moet je <strong>%(wait_seconds)s seconden</strong> wachten voordat je dit bestand kunt downloaden."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Pagina automatisch vernieuwen. Als je het downloadvenster mist, wordt de timer opnieuw gestart, dus automatisch vernieuwen wordt aanbevolen."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Nu downloaden"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converteren: gebruik online tools om tussen formaten te converteren. Gebruik om tussen EPUB en PDF te converteren bijvoorbeeld <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: download het bestand (PDF of EPUB worden ondersteund) en <a %(a_kindle)s>stuur het naar Kindle</a> via web, app of e-mail. Handige tools: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Steun auteurs: Als je dit leuk vindt en je kunt het je veroorloven, overweeg dan om het origineel te kopen of de auteurs rechtstreeks te steunen."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Steun bibliotheken: Als dit beschikbaar is in je plaatselijke bibliotheek, overweeg dan om het daar gratis te lenen."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Niet direct beschikbaar in bulk, alleen semi-bulk achter een betaalmuur"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive beheert een collectie van <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb is een bedrijf dat verschillende online boekhandels scrapet om ISBN-metadata te vinden. Anna’s Archive maakt back-ups van de ISBNdb boek-metadata. Deze metadata is beschikbaar via Anna’s Archive (hoewel momenteel niet in de zoekfunctie, behalve als je expliciet zoekt op een ISBN-nummer)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Voor technische details, zie hieronder. Op een gegeven moment kunnen we het gebruiken om te bepalen welke boeken nog ontbreken in schaduw bibliotheken, zodat we kunnen prioriteren welke boeken we moeten vinden en/of scannen."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Onze blogpost over deze gegevens"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb scrape"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Momenteel hebben we een enkele torrent, die een 4,4GB gzip gecomprimeerd<a %(a_jsonl)s>JSON Lines</a> bestand bevat (20GB uitgepakt): “isbndb_2022_09.jsonl.gz”. Om een “.jsonl” bestand in PostgreSQL te importeren, kun je iets gebruiken zoals <a %(a_script)s>dit script</a>. Je kunt het zelfs direct pipen met iets als %(example_code)s zodat het on-the-fly decomprimeert."

#~ msgid "page.donate.wait"
#~ msgstr "Wacht minstens <span %(span_hours)s>twee uur</span> (en vernieuw daarna deze pagina) voordat je contact zoekt."

#~ msgid "page.codes.search_archive"
#~ msgstr "Zoek in Anna’s Archive naar “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Doneren met Alipay of WeChat. Op de volgende pagina kun je kiezen."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Het woord over Anna’s Archive verspreiden op sociale media en online forums, door boeken of lijsten op AA aan te bevelen, of vragen te beantwoorden."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Het verzamelen van fictie is gedivergeerd, maar heeft nog steeds <a %(libgenli)s>torrents</a> beschikbaar, ook al zijn deze niet bijgewerkt sinds 2022 (we hebben wel directe downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna’s Archive en Libgen.li beheren gezamenlijk collecties van <a %(comics)s>stripboeken</a> en <a %(magazines)s>tijdschriften</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Geen torrents voor Russische fictie en standaard documentencollecties."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Er zijn geen torrents beschikbaar voor de extra inhoud. De torrents die op de Libgen.li website staan, zijn mirrors van andere torrents die hier vermeld staan. De enige uitzondering zijn fictie-torrents die beginnen bij %(fiction_starting_point)s. De strips en tijdschriften torrents worden uitgebracht als een samenwerking tussen Anna’s Archive en Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Van een collectie <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> exacte oorsprong onduidelijk. Gedeeltelijk van the-eye.eu, gedeeltelijk van andere bronnen."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

