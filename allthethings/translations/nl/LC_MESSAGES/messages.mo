��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b n  sd e  �f -   Hh   vh �  �i �  �k |  qm   �n T   �o q   Pp G   �p j   
q �  uq a  Ls �   �t �  �u �   �w �  �x �  ez �  G| �   -~ �   �  �� I   Ȃ �   � D   �   -�    K� ,  j� G   �� "   ߈    �     � F   8�    � 
   �� '   ��    Չ $   ��    � B   ,� B  o� <  �� c   � <   S�    ��    ��    ��    ��    ƍ 
   �� 	   �    ��    	� "   �    3�    9� 	   W� 
   a�    o�    {� #   ��    �� {  ׎ ]  S� �   �� -   9� y  g� �   � 	  �� �   ��    [� �   y�    � �   5� "   ݘ    �    � �  9� 1   ؛ p   
�    {� 9  �� U  �� �  � "  ��    � Q   (� Y   z� �   Ԣ ,   �� 5   �� x   � ?   i� O   �� -   ��    '�    ,� �  �� >   {� n   �� �   )� G   �� &  =� *   d�   �� �   �� �   1� S   ߬ �   3� �   �� G   �� �   Ӯ �   ͯ h   �� B   �� ~   A�    �� �   ͱ !  n� G   �� 	   س �   � �   �� 	   h� �  r� �   �   �� �  �� �   n� e  Y� 	  �� N   ɿ C   � ^   \� r   �� a   .� k   �� ,   �� �   )� _   �� L   
� 
   Z� {   h� P   �� �   5� �   ��    �� �   ��   �� e  �� f  � �   w� �   � 6  �� �   4� �   �� �   �� o  >� �   ��   h�    u� �   �� �   '� �   � n  �� �   :� �   �� �   V� �  � �   
� <  ��   �� '   �� ^   � �   c� M   �� z  H� �   �� k   v� z   �� #   ]� 0  �� �   �� r  ��    � i   � �   �� �   �   �� �   	� �  �� �  �� (   �� g  �� �  �   �� �  ��   �� )  �� U   �� �   A�    �� �   �� Y  �� �  �   ��   �� �   ��    8  d  E  w  �   "    9 �   F &  
 �   1 !  � �   	 f   �	    X
 �  e
 `      d
 �   s
   p 1  � �   � h  � X    Q   i   � 3   �    � !   
  7 ?  B 6   � G   �    y    �   � d    o  w !   � "   	 �  , �   �  �   l! J   �! �   B" *   �" �   �"   �#    	%    % "   )% 8   L% >   �% 4   �% V   �% w   P&    �& 0   �& >   ' J   J' ,   �' 3   �' 8   �' h   /(    �(    �(   �(   �) �   �*   �+ �   �, �   a- h  ).    �/ �   �/ �   �0 �   (1 _  �1 p  +3 #  �4 �  �6 �  �8 G   : *   Z: '   �: �   �: �  h; �  d= F  
? [  T@ -  �A -  �B '  E .   4F >  cF 7  �G d  �H    ?J �   OJ :  HK �   �L f  #M �   �N    bO -   qO `   �O 	    P �  
P �  �R V  !T �   xV �   4W    �W �   X �   �X G   �Y s   Z   �Z �   �[ �   �\ �  =] b   �^    4_    U` �  i` �   Fb .  �b �  
e �  �g �   zi 	   Dj �   Nj �  Ak    m u  m �  �n &  �p %  �r �   �t B  �u G   w �  Mw �   !y y   z E   �z b   �z    8{ 8   >{ %   w{ $   �{ $   �{    �{ �   �{    �| �  � q  �� �   1�    � 0  � I  K� g  �� �  �� p  �� �  � 	   ��   �� 	   ��    �� �  ȓ >  x� 
  �� ]  ř    #� �  2� �  � �  ��    �� �  ģ X  �� �  �    �� �   �� t   t� �  � �  �� �   ]� �   � �   �� �  :�    �� �   ѳ N   �� I   ״    !� 2   8� <   k� ^  �� �  � �  �� /   $� �  T� �   �    �� �   �� 8   c� z   �� �   � 8   �� h   )�    �� a  �� e  	� �  o� �  9� �   ��    ~� p   ��   ��    � �   � �  �� �   M� 1   �� 6   1� c  h� -   �� h  �� �  c� �  �� !   �� �   �� �   � j  � �  w� 
   �� �   � ,   � �   .� �   ��   �� �   �� I   �� -   �� 
  � =   � �  X� e  	� �   o�   C� b   U� B   �� �   �� 5  �� �  �� �  V� �  +� 5   �� �   !�   �� K  � �   ]� �  � �  � -   �� �  �� *   �� �  � [   ��    I�   _�   n  p   �   �   � 
   K   �   e �  
 �  �
 �    $   k  : ;   � �   � �   m    �    
 M   �   ` <       N �  o   J �  f �  V �      �  �  ,  Z"    �# �   �#    d$ o  m$     �%    �%    &    &    ,&    K&    \&    o&    {&    �& 	   �&    �&    �&    �&    �&    �& 7   �&    '    '    $'    ,' �   4' T   (     Y(    z( "   �( !   �( *   �( *   �( "   ") 	   E) 	   O) 
   Y)    g)    y)    �)    �) 	   �)    �)    �) 1   �) .   *    3* "   O* &   r* 4   �* 0   �*    �* &   + ?   A+    �+ H   �+ ?   �+    , U   #, L   y,    �,    �, "   �,    -    ,-    A-    _-    w-    �-    �-    �-    �- 
   �- 	   �- 
   �-    �-    �-    
.    . 	   .    $. 	   ;.    E.    Z.    `. 	   g.    q.    �.    �.    �.    �.    �.    �.    �. 	   /    / 9   /    X/    _/ $   f/    �/    �/    �/    �/    �/    �/    �/ ]   �/ �   T0 ^   1 �   `1 �  2 f   4    h4    y4    �4    �4 
   �4    �4    �4 '   �4 L   	5 9   V5 T   �5 $   �5 I   
6 3   T6 l   �6 r   �6 �   h7 ]   T8 C   �8    �8    9    (9 
   09    ;9    G9    Y9    o9    u9    �9    �9    �9    �9    �9    �9    �9    �9 
   �9    �9    :    :    :    (:    5:    Q:   d:    ;    �;    �;    �;    �; P   �; [   �; '   W< 1   < /   �<    �<    �<    �< y   �<    n=    t= &   �= x   �=    %>    A> �   R> +   �> 2  ? `   5B {   �B �   C �   �C 5   �D   �D �   F W  �F �   �G    �H 	   I @   
I D   NI T   �I _   �I l   HJ D   �J b   �J "   ]K -   �K    �K 
   �K P   �K #   L    7L    ?L    PL    YL r   uL    �L +   �L F   $M    kM    �M L   �M X   �M �  DN    �O    �O �   �O    �P    �P    �P    �P    �P -   �P T  'Q    |R    �R    �R 	   �R .  �R !   �S    T    T y   T    �T    �T 9   �T    �T    �T '    U �   (U    �U 
   �U @   �U     V    =V    QV 
   ZV ,   hV ]   �V    �V -   W �   2W i   �W K   VX    �X �   �X A   �Y    �Y 9   �Y    4Z �   GZ �   �Z    �[ C   �[ �   \ �   �\    }] &   �]    �] n  �] "   =_ #   `_    �_ #   �_ (   �_ �   �_    �`    �` %   �` E   �`    3a "   <a     _a    �a [  �a g  �c �  ae 7   ^g -   �g    �g %   �g +  �g �   #i �   j    �j �   �j �  �k    Jm )   hm 	  �m /  �o    �p    �p 3   �p    q 4  q    Tr R  hr �  �s �   uu    qv p   �v (   �v "   "w w   Ew .  �w   �x �   �y �  �z q   ?|    �| \   �} �   ~ �   �~ S    �   � -   �� #   ��    ܀    �    � (   �    .� @   G� /   �� 	   �� -    (  �� -   � �   G� �   2� }   �� #   3�    W�    q�    �� "   ��    �� '   ޅ !   � �   (� %   
� �   0�    �� �   � w   ܈ �   T�   � �   � .   ŋ �   � 	   � &  �� c   ��    � �  *�    ݏ    �    � #   � ,   8�    e�    m� Y   r� �   ̐ �   �� 	   [�    e�    l� �   �� 2  ?� �   r� {   )�    ��    ��    ѕ    �    ��    	�    � =   "� .   `� �  �� Y   `�    �� `   ̘ m   -� T   �� m   � `   ^� S   �� 	   � _   � N   }� �   ̛ _   T� M   ��    � �   � c   � >   e� e   �� Y   
� :   d�    �� 7   �� p   �� �   Q� H    � �   I�    �   � B   � `   [� �   ��    X� �  a� �   O�    �    5�    T�    ]�   d� ,   s� g   �� �   � �   ש �   �� �   N� �   � �   �� �   V� V   � <  i� f   �� v  
� �   �� �   2� 
   #� 
   1� 
   ?� �   M� 
   � 
   �� M   � j   R� �   �� 
   �� Y   �� 7    � �   8� =   �� t   �� h   q� 
   ڷ �   � 
   r� 
   �� 
   �� 6  �� �   Ӻ �  a�    0�    G�    P� �   ]�    �    $�   A� �   H�    ��    �    %� ?   D� ;   �� 2   ��    ��    � �   #�    � �  +�   �� �   �� Q   o� �   �� '  e� �  �� e  �� �   �� �   �� {   *�    �� |  ��    8� u  V� �   �� =  �� �   �� S  �� �   �� �   �� l   6� *   �� >   ��    
� 5   "� 
   X�    c�    p� �   ��    k� m   t� .   ��    � 
   �    #� "   0� o   S� E   �� 5   	� 
  ?� 8   M�    �� 	   ��    �� %   ��    �� 
   ��    �� 
   �� 	   
� 
   � 	   � 
   )� !   4� �   V�    ��    �    �    �    /�    >�    N�    ]�    m�    �� %   �� i   ��    &� *   ;� b   f� �   �� �   m� �   9�   �� �   �� )  �� !   �� �   �� 5   h� T   �� I   �� �   =� �   �� V   �� P   )�    z� |   �� W   	� {   a�    ��     ��    �    � 	   +�    5�    I�    Q�    q�    z�    ��    ��    ��    ��    ��    �    �    *�    :�    B�    _� !   f� !   �� �   �� �   4�    �� %   �� �   � V   �� w   �� !   o� 0   �� 0   �� <   �� >   0� *   o� �   ��   S� "  _�    �� B   �� }   �� (   Y� �   �� �   ]� p   4� 0   ��    �� �   �� �   �� `   � *   y� R   �� �   ��    �� '   ��    �� A   � l   N� j   ��    &�    .�    7�    >� �   V� ;   �� "   *� 6   M� #   ��    �� !   �� P   ��    6� m   R� <   �� !  �� ?   � q   _� �   �� @   x�    ��    ��    ��    � #   -�    Q� #   n� 7   �� 4   �� A  �� @   A 5   � >   � 2   �    * z   2 w   � �   % �   � 
   � �   � %   I    o �   �    h $   � >   � 0   � Q    W   m I   �    	    $	 �   D	 2   �	    
 M   ,
 �   z
 8   v �   � z   A \   � p   
 %   �
 5   �
 [   �
    B \   a    � �   � 3   � :   �     '    �   E @   / J   p |   � \   8 T   � �   � �   m 	   � 
   � J   
 @   U    �    �    �    �    � -    �   3 j   �    ,    G &   f %   � !   � ]   � F   3 y   z M   �    B q   _ �   � 0   � R   �    8 J   P H   �    � q   � i   n -   � J       Q /   f R   �    � m   � %   j    � -   � e   � �   :    �         B   2    u [   � 3   � �     }   �     8! 6   I! �   �!    ?" �   ^" D   �"    9# P   Y# �   �#    �$    �$    �$    �$ 0   �$ 7   �$ N   3%    �%    �%    �% %   �% @   �% ?   &    Z& F   a& C   �&    �&    �& 3   ' &   N'    u' k   �'   �' K   �(    H)    W) �   d) T  * P   d+    �+ k  �+ �  8- (   �. z   �. !   r/ I  �/ &   �1 i   2    o2    �2 �  �2    �4 N   �4 x   �4 g   i5 Y   �5    +6 o   J6 /   �6    �6 v   7 A   z7 /   �7 Y   �7 +   F8 N   r8 �   �8 �   B9 $   �9 �   : �  �: �   e< 0   += 7  \= �   �> �   �?   O@ �   fA +   (B    TB �   nB ,   C �  KC v   IE L   �E    
F     F �  =F    �G �   H :  �H .  J   @K C   `L J   �L c   �L 1   SM (   �M W   �M \   N    cN    pN .   �N    �N    �N    �N Z   O 3   iO    �O j   �O �   P �   �P    �Q    �Q    �Q I   �Q W   R U   kR �   �R o   }S    �S '   T �   .T    U �   %U �  �U �   �X F   �Y 0   �Y    Z    	Z    Z I   Z 0   \Z �   �Z   [ T   /\    �\    �\ %   �\    �\ ]   �\    N] @   ]]    �] *   �] #   �]    �]    ^ k   ^    |^    �^ "   �^    �^    �^ h   �^ �   D_ [   ` k   `` W   �` �   $a    �a    �a �   b �   �b �   �c    td ~   }d K   �d =   He Y   �e l   �e V   Mf    �f M   �f    g    g    5g    Ig    _g    yg    �g    �g    �g    �g .   �g .   h .   0h #   _h 5   �h .   �h    �h    i    $i H   7i    �i 	   �i 7   �i $   �i b    j &   cj    �j    �j &   �j "   �j     k F   k 3   Yk H   �k �   �k    pl    �l    �l    �l 6   �l 	   	m    m    $m |   9m    �m #   �m    �m 
   n 	   n @   n    Wn �   pn    8o    Io    ]o "   no $   �o    �o %   �o '   �o +   p 1   Jp ,   |p    �p e   �p "   q    9q    Uq :   fq M   �q    �q ,   r    8r y   Vr \   �r N   -s    |s    �s 	   �s    �s    �s    �s �  �s �   �u    	v    v +   "v    Nv '   ^v    �v    �v �   �v D   w �   \w    ;x $   Ox �   tx $    y     %y     Fy #   gy )   �y &   �y    �y 
   �y 6   z "   >z -   az B   �z B  �z ?   | V   U| T   �| "   } �   $} )   �}    �} T   �}    =~ (   R~    {~ H   �~ ?   �~ '    �   G    �    
�    �    :�    L�    a�    w�    ��    �� d   À %  (�    N� !   l� �   �� �   ��    B� "   _�    ��    ��    ��        Մ    �    �    �    )�    8� 
   I�    W�    f�    x�    ��    �� *   �� �   ̅   �� y  ��   /� �  1� �   ܌ r  ֍    I�   U�    l�   ~� �   �� �  n� �   
� z  ؔ 6   S� �   �� B   9�    |� J   �� R   � l   6� n   �� �   � �   ؙ �   �� Q  R�    �� �   �� �   �� ^   N� �   �� 
   R� �   `� j  G� �   �� �   }�    X� l   d� z   Ѥ �   L� �   B� m   ֦    D� "   ħ "   �    
� G    � -   h� 
   �� �   �� @   ,� i   m�    ש �   � �   x� ?   � V   K� :   �� g   ݫ [   E� &   �� t   Ȭ i   =� �   �� ~   .�    �� *   �� #   � W   � 4   \�    ��    �� P   ��    �    �    � 6   #�    Z� 4   z�    ��    ΰ    ۰    � 	   � \   �� s   Q� J   ű ;   �    L�    T� 1   q�    ��    ��    ��    Ʋ 	   Ͳ    ײ    ݲ    �    �    ��    �    
�    �    "� 
   4�    B�    S�    Z�    f�    o�     t�    �� '   �� �   س    [� S   p� �   Ĵ 
   ��    ��    ��    ��    ��    ȵ    ̵ �   ӵ �   y� ?   ��    :�    G� y   a�    ۷ �   � ~   m� )   �    � |   +� �   �� C   �� �   ĺ a   �� -   �    � $   �� %   �� M   ߼ �   -�    �� �   ۽ �   h� �   � R   |�    Ͽ    �    �    ��    ��    �    $�    8� �   R� v   �� �   h� �   �� �   �� e   �� L   � �  b� h  �   p� �   |� *  � v  G� 
   �� �   ��   �� �   �� �  y� �  A� �   "� �  ��    4� B   L� �   �� Y  0� A   �� �   ��    ��    ��    �� �   �� 3   n� o   �� 2   � `   E� H   �� b   ��    R� �   r� F   #� /   j� ^   �� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: nl
Language-Team: nl <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library is een populaire (en illegale) bibliotheek. Ze hebben de Library Genesis-collectie genomen en gemakkelijk doorzoekbaar gemaakt. Bovendien zijn ze zeer effectief geworden in het werven van nieuwe boekbijdragen, door bijdragende gebruikers te belonen met verschillende voordelen. Ze dragen deze nieuwe boeken momenteel niet terug bij aan Library Genesis. En in tegenstelling tot Library Genesis, maken ze hun collectie niet gemakkelijk mirrorbaar, wat brede bewaring verhindert. Dit is belangrijk voor hun bedrijfsmodel, aangezien ze geld vragen voor toegang tot hun collectie in bulk (meer dan 10 boeken per dag). We vellen geen moreel oordeel over het vragen van geld voor bulktoegang tot een illegale boekencollectie. Het staat buiten kijf dat de Z-Library succesvol is geweest in het vergroten van de toegang tot kennis en het verkrijgen van meer boeken. Wij zijn hier simpelweg om ons deel te doen: het waarborgen van de langetermijnbewaring van deze privécollectie. - Anna en het team (<a %(reddit)s>Reddit</a>) In de oorspronkelijke release van de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>), maakten we een mirror van Z-Library, een grote illegale boekencollectie. Ter herinnering, dit is wat we schreven in die oorspronkelijke blogpost: Die collectie dateerde van midden 2021. In de tussentijd is de Z-Library in een verbluffend tempo gegroeid: ze hebben ongeveer 3,8 miljoen nieuwe boeken toegevoegd. Er zitten zeker wat duplicaten tussen, maar het merendeel lijkt legitiem nieuwe boeken te zijn, of scans van hogere kwaliteit van eerder ingediende boeken. Dit is grotendeels te danken aan het toegenomen aantal vrijwillige moderatoren bij de Z-Library en hun bulk-upload systeem met deduplicatie. We willen hen feliciteren met deze prestaties. We zijn blij aan te kondigen dat we alle boeken hebben verkregen die aan de Z-Library zijn toegevoegd tussen onze laatste mirror en augustus 2022. We zijn ook teruggegaan en hebben enkele boeken verzameld die we de eerste keer hebben gemist. Al met al is deze nieuwe collectie ongeveer 24TB, wat veel groter is dan de vorige (7TB). Onze mirror is nu in totaal 31TB. We hebben opnieuw gededupliceerd tegen Library Genesis, aangezien er al torrents beschikbaar zijn voor die collectie. Ga alstublieft naar de Pirate Library Mirror om de nieuwe collectie te bekijken (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>). Daar is meer informatie over hoe de bestanden zijn gestructureerd en wat er sinds de laatste keer is veranderd. We zullen er hier niet naar linken, aangezien dit slechts een blogwebsite is die geen illegale materialen host. Natuurlijk is seeden ook een geweldige manier om ons te helpen. Bedankt aan iedereen die onze vorige set torrents seedt. We zijn dankbaar voor de positieve respons en blij dat er zoveel mensen zijn die op deze ongewone manier geven om het behoud van kennis en cultuur. 3x nieuwe boeken toegevoegd aan de Pirate Library Mirror (+24TB, 3,8 miljoen boeken) Lees de begeleidende artikelen van TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a> - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) begeleidende artikelen door TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a> Niet zo lang geleden waren “schaduw-bibliotheken” aan het uitsterven. Sci-Hub, het enorme illegale archief van academische papers, was gestopt met het opnemen van nieuwe werken, vanwege rechtszaken. “Z-Library”, de grootste illegale bibliotheek van boeken, zag zijn vermeende makers gearresteerd op strafrechtelijke aanklachten wegens auteursrecht. Ze slaagden er ongelooflijk in om aan hun arrestatie te ontsnappen, maar hun bibliotheek is niet minder bedreigd. Sommige landen doen al een versie hiervan. TorrentFreak <a %(torrentfreak)s>rapporteerde</a> dat China en Japan AI-uitzonderingen hebben geïntroduceerd in hun auteursrechtwetten. Het is voor ons onduidelijk hoe dit samenwerkt met internationale verdragen, maar het biedt zeker dekking aan hun binnenlandse bedrijven, wat verklaart wat we hebben gezien. Wat betreft Anna’s Archief — we zullen ons ondergrondse werk voortzetten, geworteld in morele overtuiging. Toch is onze grootste wens om in het licht te treden en onze impact legaal te versterken. Hervorm alstublieft het auteursrecht. Toen Z-Library met sluiting werd geconfronteerd, had ik al hun hele bibliotheek geback-upt en was ik op zoek naar een platform om het te huisvesten. Dat was mijn motivatie om Anna’s Archief te starten: een voortzetting van de missie achter die eerdere initiatieven. We zijn sindsdien uitgegroeid tot de grootste schaduw bibliotheek ter wereld, met meer dan 140 miljoen auteursrechtelijk beschermde teksten in tal van formaten — boeken, academische papers, tijdschriften, kranten en meer. Mijn team en ik zijn ideologen. Wij geloven dat het bewaren en hosten van deze bestanden moreel juist is. Bibliotheken over de hele wereld zien hun financiering gekort worden, en we kunnen het erfgoed van de mensheid ook niet aan bedrijven toevertrouwen. Toen kwam AI. Vrijwel alle grote bedrijven die LLM's bouwen, namen contact met ons op om op onze data te trainen. De meeste (maar niet alle!) Amerikaanse bedrijven heroverwogen dit toen ze zich realiseerden dat ons werk illegaal was. Daarentegen hebben Chinese bedrijven onze collectie enthousiast omarmd, blijkbaar niet gehinderd door de legaliteit ervan. Dit is opmerkelijk gezien de rol van China als ondertekenaar van bijna alle grote internationale auteursrechtverdragen. We hebben ongeveer 30 bedrijven toegang met hoge snelheid gegeven. De meeste zijn LLM-bedrijven, en sommige zijn databrokers, die onze collectie zullen doorverkopen. De meeste zijn Chinees, hoewel we ook hebben samengewerkt met bedrijven uit de VS, Europa, Rusland, Zuid-Korea en Japan. DeepSeek <a %(arxiv)s>gaf toe</a> dat een eerdere versie was getraind op een deel van onze collectie, hoewel ze zwijgzaam zijn over hun nieuwste model (waarschijnlijk ook getraind op onze data). Als het Westen voorop wil blijven in de race van LLM's, en uiteindelijk AGI, moet het zijn standpunt over auteursrecht heroverwegen, en snel. Of u het nu met ons eens bent of niet over onze morele zaak, dit wordt nu een kwestie van economie, en zelfs van nationale veiligheid. Alle machtsblokken bouwen kunstmatige superwetenschappers, superhackers en superlegers. Vrijheid van informatie wordt een kwestie van overleven voor deze landen — zelfs een kwestie van nationale veiligheid. Ons team komt van over de hele wereld en we hebben geen specifieke voorkeur. Maar we zouden landen met strenge auteursrechtwetten willen aanmoedigen om deze existentiële dreiging te gebruiken om ze te hervormen. Dus wat te doen? Onze eerste aanbeveling is eenvoudig: verkort de duur van het auteursrecht. In de VS wordt auteursrecht verleend voor 70 jaar na de dood van de auteur. Dit is absurd. We kunnen dit in lijn brengen met patenten, die worden verleend voor 20 jaar na indiening. Dit zou meer dan genoeg tijd moeten zijn voor auteurs van boeken, artikelen, muziek, kunst en andere creatieve werken, om volledig gecompenseerd te worden voor hun inspanningen (inclusief langetermijnprojecten zoals filmadaptaties). Vervolgens zouden beleidsmakers op zijn minst uitzonderingen moeten opnemen voor de massale bewaring en verspreiding van teksten. Als verloren inkomsten van individuele klanten de grootste zorg zijn, zou distributie op persoonlijk niveau verboden kunnen blijven. Op hun beurt zouden degenen die in staat zijn om enorme verzamelingen te beheren — bedrijven die LLM's trainen, samen met bibliotheken en andere archieven — onder deze uitzonderingen vallen. Hervorming van het auteursrecht is noodzakelijk voor nationale veiligheid TL;DR: Chinese LLM's (inclusief DeepSeek) zijn getraind op mijn illegale archief van boeken en papers — het grootste ter wereld. Het Westen moet het auteursrecht herzien als een kwestie van nationale veiligheid. Zie de <a %(all_isbns)s>originele blogpost</a> voor meer informatie. We hebben een uitdaging uitgegeven om dit te verbeteren. We zouden een eerste plaats beloning van $6.000, een tweede plaats van $3.000 en een derde plaats van $1.000 toekennen. Vanwege de overweldigende respons en ongelooflijke inzendingen hebben we besloten de prijzenpot iets te verhogen en een gedeelde derde plaats van $500 elk toe te kennen. De winnaars staan hieronder, maar zorg ervoor dat je alle inzendingen <a %(annas_archive)s>hier</a> bekijkt, of download onze <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a>. Eerste plaats $6.000: phiresky Deze <a %(phiresky_github)s>inzending</a> (<a %(annas_archive_note_2951)s>Gitlab-opmerking</a>) is precies wat we wilden, en meer! We waren vooral gecharmeerd van de ongelooflijk flexibele visualisatiemogelijkheden (zelfs met ondersteuning voor aangepaste shaders), maar met een uitgebreide lijst van presets. We vonden ook de snelheid en soepelheid geweldig, de eenvoudige implementatie (die zelfs geen backend heeft), de slimme minimap en de uitgebreide uitleg in hun <a %(phiresky_github)s>blogbericht</a>. Ongelooflijk werk, en de welverdiende winnaar! - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Onze harten zijn vol dankbaarheid. Opmerkelijke ideeën Wolkenkrabbers voor zeldzaamheid Veel schuifregelaars om datasets te vergelijken, alsof je een DJ bent. Schaalstreep met aantal boeken. Mooie labels. Coole standaardkleurenpalet en heatmap. Unieke kaartweergave en filters Annotaties, en ook live statistieken Live statistieken Nog enkele ideeën en implementaties die we bijzonder leuk vonden: We zouden nog wel even door kunnen gaan, maar laten we hier stoppen. Zorg ervoor dat je alle inzendingen <a %(annas_archive)s>hier</a> bekijkt, of download onze <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a>. Zoveel inzendingen, en elk biedt een uniek perspectief, of het nu in UI of implementatie is. We zullen in ieder geval de inzending die de eerste plaats behaalde integreren in onze hoofdwebsite, en misschien nog enkele anderen. We zijn ook begonnen na te denken over hoe we het proces van het identificeren, bevestigen en vervolgens archiveren van de zeldzaamste boeken kunnen organiseren. Meer hierover volgt. Bedankt aan iedereen die heeft deelgenomen. Het is geweldig dat zoveel mensen geven om dit project. Eenvoudig schakelen van datasets voor snelle vergelijkingen. Alle ISBN's CADAL SSNO's CERLALC datalek DuXiu SSID's EBSCOhost’s eBook Index Google Boeken Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Bestanden in Anna’s Archief Nexus/STC OCLC/Worldcat OpenLibrary Russische Staatsbibliotheek Keizerlijke Bibliotheek van Trantor Tweede plaats $3.000: hypha “Hoewel perfecte vierkanten en rechthoeken wiskundig bevredigend zijn, bieden ze geen superieure localiteit in een mappingcontext. Ik geloof dat de asymmetrie inherent aan deze Hilbert of klassieke Morton geen fout is, maar een kenmerk. Net zoals de beroemde laarsvormige omtrek van Italië het onmiddellijk herkenbaar maakt op een kaart, kunnen de unieke "eigenaardigheden" van deze curves dienen als cognitieve bakens. Deze onderscheidendheid kan het ruimtelijk geheugen verbeteren en gebruikers helpen zich te oriënteren, waardoor het mogelijk gemakkelijker wordt om specifieke regio's te lokaliseren of patronen op te merken.” Nog een geweldige <a %(annas_archive_note_2913)s>inzending</a>. Niet zo flexibel als de eerste plaats, maar we gaven eigenlijk de voorkeur aan de macro-niveau visualisatie boven de eerste plaats (ruimte-vullende curve, grenzen, labeling, highlighting, panning en zoomen). Een <a %(annas_archive_note_2971)s>opmerking</a> van Joe Davis sprak ons aan: En nog steeds veel opties voor visualisatie en rendering, evenals een ongelooflijk soepele en intuïtieve UI. Een solide tweede plaats! - Anna en het team (<a %(reddit)s>Reddit</a>) Een paar maanden geleden kondigden we een <a %(all_isbns)s>beloning van $10.000</a> aan om de best mogelijke visualisatie van onze gegevens te maken die de ISBN-ruimte toont. We benadrukten het tonen van welke bestanden we al wel/niet hebben gearchiveerd, en we voegden later een dataset toe die beschrijft hoeveel bibliotheken ISBN's bezitten (een maatstaf voor zeldzaamheid). We zijn overweldigd door de respons. Er is zoveel creativiteit geweest. Een grote dank aan iedereen die heeft deelgenomen: uw energie en enthousiasme zijn aanstekelijk! Uiteindelijk wilden we de volgende vragen beantwoorden: <strong>welke boeken bestaan er in de wereld, hoeveel hebben we al gearchiveerd, en op welke boeken moeten we ons vervolgens richten?</strong> Het is geweldig om te zien dat zoveel mensen om deze vragen geven. We begonnen zelf met een basisvisualisatie. In minder dan 300kb vertegenwoordigt deze afbeelding beknopt de grootste volledig open "boekenlijst" ooit samengesteld in de geschiedenis van de mensheid: Derde plaats $500 #1: maxlion In deze <a %(annas_archive_note_2940)s>inzending</a> vonden we de verschillende soorten weergaven erg leuk, met name de vergelijkings- en uitgeversweergaven. Derde plaats $500 #2: abetusk Hoewel niet de meest gepolijste UI, voldoet deze <a %(annas_archive_note_2917)s>inzending</a> aan veel van de eisen. We vonden vooral de vergelijkingsfunctie erg goed. Derde plaats $500 #3: conundrumer0 Net als de eerste plaats, heeft deze <a %(annas_archive_note_2975)s>inzending</a> ons geïmponeerd met zijn flexibiliteit. Uiteindelijk is dit wat een geweldige visualisatietool maakt: maximale flexibiliteit voor power users, terwijl het eenvoudig blijft voor gemiddelde gebruikers. Derde plaats $500 #4: charelf De laatste <a %(annas_archive_note_2947)s>inzending</a> om een beloning te krijgen is vrij eenvoudig, maar heeft enkele unieke kenmerken die we erg leuk vonden. We vonden het leuk hoe ze laten zien hoeveel datasets een bepaalde ISBN dekken als een maatstaf voor populariteit/betrouwbaarheid. We vonden ook de eenvoud maar effectiviteit van het gebruik van een opaciteitsschuifregelaar voor vergelijkingen erg goed. Winnaars van de $10.000 ISBN-visualisatiebeloning Kort samengevat: We hebben enkele ongelooflijke inzendingen ontvangen voor de $10.000 ISBN-visualisatiebeloning. Achtergrond Hoe kan Anna’s Archief haar missie volbrengen om alle kennis van de mensheid te back-uppen, zonder te weten welke boeken er nog zijn? We hebben een TODO-lijst nodig. Een manier om dit in kaart te brengen is via ISBN-nummers, die sinds de jaren 70 aan elk gepubliceerd boek zijn toegewezen (in de meeste landen). Er is geen centrale autoriteit die alle ISBN-toewijzingen kent. In plaats daarvan is het een gedistribueerd systeem, waarbij landen reeksen nummers krijgen, die vervolgens kleinere reeksen toewijzen aan grote uitgevers, die mogelijk verder onderverdelen naar kleinere uitgevers. Uiteindelijk worden individuele nummers aan boeken toegewezen. We zijn <a %(blog)s>twee jaar geleden</a> begonnen met het in kaart brengen van ISBN's met onze scrape van ISBNdb. Sindsdien hebben we veel meer metadata bronnen gescraped, zoals <a %(blog_2)s>Worldcat</a>, Google Boeken, Goodreads, Libby en meer. Een volledige lijst is te vinden op de pagina's “Datasets” en “Torrents” op Anna’s Archief. We hebben nu veruit de grootste volledig open, gemakkelijk downloadbare collectie van boekmetadata (en dus ISBN's) ter wereld. We hebben <a %(blog)s>uitgebreid geschreven</a> over waarom we geven om behoud, en waarom we ons momenteel in een kritieke periode bevinden. We moeten nu zeldzame, onderbelichte en uniek bedreigde boeken identificeren en behouden. Goede metadata over alle boeken in de wereld helpt daarbij. $10.000 beloning Sterke overweging zal worden gegeven aan bruikbaarheid en hoe goed het eruitziet. Toon daadwerkelijke metadata voor individuele ISBN's bij inzoomen, zoals titel en auteur. Betere ruimte-vullende curve. Bijvoorbeeld een zigzag, gaande van 0 naar 4 op de eerste rij en dan terug (in omgekeerde richting) van 5 naar 9 op de tweede rij — recursief toegepast. Verschillende of aanpasbare kleurenschema's. Speciale weergaven voor het vergelijken van datasets. Manieren om problemen op te sporen, zoals andere metadata die niet goed overeenkomen (bijv. sterk verschillende titels). Afbeeldingen annoteren met opmerkingen over ISBN's of bereiken. Eventuele heuristieken voor het identificeren van zeldzame of bedreigde boeken. Welke creatieve ideeën je ook kunt bedenken! Code De code om deze afbeeldingen te genereren, evenals andere voorbeelden, is te vinden in <a %(annas_archive)s>deze directory</a>. We hebben een compact dataformaat ontwikkeld, waarmee alle vereiste ISBN-informatie ongeveer 75MB (gecomprimeerd) is. De beschrijving van het dataformaat en de code om het te genereren is <a %(annas_archive_l1244_1319)s>hier</a> te vinden. Voor de beloning ben je niet verplicht dit te gebruiken, maar het is waarschijnlijk het meest handige formaat om mee te beginnen. Je kunt onze metadata transformeren zoals je wilt (hoewel al je code open source moet zijn). We kunnen niet wachten om te zien wat je bedenkt. Veel succes! Fork deze repo en bewerk deze blogpost HTML (geen andere backends behalve onze Flask-backend zijn toegestaan). Maak de bovenstaande afbeelding soepel in te zoomen, zodat je helemaal kunt inzoomen op individuele ISBN's. Klikken op ISBN's moet je naar een metadata-pagina of zoekopdracht op Anna’s Archief brengen. Je moet nog steeds kunnen schakelen tussen alle verschillende datasets. Landbereiken en uitgeverbereiken moeten worden gemarkeerd bij hoveren. Je kunt bijvoorbeeld <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> gebruiken voor landinformatie, en onze “isbngrp” scrape voor uitgevers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Het moet goed werken op desktop en mobiel. Er is hier veel te ontdekken, dus we kondigen een beloning aan voor het verbeteren van de visualisatie hierboven. In tegenstelling tot de meeste van onze beloningen, is deze tijdgebonden. Je moet je open source code <a %(annas_archive)s>indienen</a> vóór 2025-01-31 (23:59 UTC). De beste inzending krijgt $6.000, de tweede plaats $3.000, en de derde plaats $1.000. Alle beloningen worden uitgereikt in Monero (XMR). Hieronder staan de minimale criteria. Als geen enkele inzending aan de criteria voldoet, kunnen we nog steeds enkele beloningen toekennen, maar dat is naar eigen goeddunken. Voor bonuspunten (dit zijn slechts ideeën — laat je creativiteit de vrije loop): Je MAG volledig afwijken van de minimale criteria en een totaal andere visualisatie maken. Als het echt spectaculair is, dan komt dat in aanmerking voor de beloning, maar naar ons eigen inzicht. Dien inzendingen in door een opmerking te plaatsen bij <a %(annas_archive)s>dit issue</a> met een link naar je geforkte repo, merge request of diff. - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Deze afbeelding is 1000×800 pixels. Elke pixel vertegenwoordigt 2.500 ISBN's. Als we een bestand hebben voor een ISBN, maken we die pixel groener. Als we weten dat een ISBN is uitgegeven, maar we hebben geen bijbehorend bestand, maken we het roder. In minder dan 300kb vertegenwoordigt deze afbeelding beknopt de grootste volledig open "boekenlijst" ooit samengesteld in de geschiedenis van de mensheid (een paar honderd GB volledig gecomprimeerd). Het laat ook zien: er is nog veel werk te doen bij het back-uppen van boeken (we hebben er slechts 16%). Visualiseren van Alle ISBN's — $10.000 beloning tegen 2025-01-31 Deze afbeelding vertegenwoordigt de grootste volledig open "boekenlijst" ooit samengesteld in de geschiedenis van de mensheid. Visualiseren Naast de overzichtsafbeelding kunnen we ook kijken naar individuele datasets die we hebben verkregen. Gebruik de dropdown en knoppen om daartussen te schakelen. Er zijn veel interessante patronen te zien in deze afbeeldingen. Waarom is er enige regelmaat van lijnen en blokken, die op verschillende schalen lijkt te gebeuren? Wat zijn de lege gebieden? Waarom zijn bepaalde datasets zo geclusterd? We laten deze vragen als een oefening voor de lezer. - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusie Met deze standaard kunnen we releases meer incrementeel maken en gemakkelijker nieuwe gegevensbronnen toevoegen. We hebben al een paar spannende releases in de pijplijn! We hopen ook dat het voor andere schaduw bibliotheken gemakkelijker wordt om onze collecties te mirroren. Immers, ons doel is om menselijke kennis en cultuur voor altijd te behouden, dus hoe meer redundantie, hoe beter. Voorbeeld Laten we onze recente Z-Library-release als voorbeeld bekijken. Deze bestaat uit twee collecties: “<span style="background: #fffaa3">zlib3_records</span>” en “<span style="background: #ffd6fe">zlib3_files</span>”. Dit stelt ons in staat om metadatarecords apart van de daadwerkelijke boekbestanden te verzamelen en vrij te geven. Zo hebben we twee torrents met metadata-bestanden vrijgegeven: We hebben ook een aantal torrents met binaire gegevensmappen vrijgegeven, maar alleen voor de “<span style="background: #ffd6fe">zlib3_files</span>” collectie, in totaal 62: Door <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> uit te voeren, kunnen we zien wat erin zit: In dit geval is het metadata van een boek zoals gerapporteerd door Z-Library. Op het hoogste niveau hebben we alleen “aacid” en “metadata”, maar geen “data_folder”, aangezien er geen bijbehorende binaire gegevens zijn. De AACID bevat “22430000” als de primaire ID, waarvan we kunnen zien dat deze is overgenomen van “zlibrary_id”. We kunnen verwachten dat andere AAC's in deze collectie dezelfde structuur hebben. Laten we nu <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> uitvoeren: Dit is een veel kleinere AAC-metadata, hoewel het grootste deel van deze AAC elders in een binair bestand is opgeslagen! We hebben immers deze keer een “data_folder”, dus we kunnen verwachten dat de bijbehorende binaire gegevens zich bevinden op <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. De “metadata” bevat de “zlibrary_id”, zodat we deze gemakkelijk kunnen associëren met de bijbehorende AAC in de “zlib_records” collectie. We hadden het op verschillende manieren kunnen associëren, bijvoorbeeld via AACID — de standaard schrijft dat niet voor. Merk op dat het ook niet nodig is dat het “metadata” veld zelf JSON is. Het kan een string zijn die XML of een ander gegevensformaat bevat. Je zou zelfs metadata-informatie kunnen opslaan in de bijbehorende binaire blob, bijvoorbeeld als het veel gegevens zijn. Heterogene bestanden en metadata, zo dicht mogelijk bij het originele formaat. Binaire data kan direct worden bediend door webservers zoals Nginx. Heterogene identificatoren in de bronbibliotheken, of zelfs het ontbreken van identificatoren. Gescheiden releases van metadata versus bestandsgegevens, of alleen metadata-releases (bijv. onze ISBNdb-release). Distributie via torrents, hoewel met de mogelijkheid van andere distributiemethoden (bijv. IPFS). Onveranderlijke records, aangezien we moeten aannemen dat onze torrents voor altijd zullen blijven bestaan. Incrementele releases / aanvulbare releases. Machineleesbaar en -schrijfbaar, handig en snel, vooral voor onze stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Enigszins eenvoudige menselijke inspectie, hoewel dit ondergeschikt is aan machineleesbaarheid. Gemakkelijk om onze collecties te zaaien met een standaard gehuurde seedbox. Ontwerpdoelen We geven er niet om dat bestanden gemakkelijk handmatig op schijf te navigeren zijn, of doorzoekbaar zonder voorverwerking. We geven er niet om direct compatibel te zijn met bestaande bibliotheeksoftware. Hoewel het gemakkelijk moet zijn voor iedereen om onze collectie te zaaien met torrents, verwachten we niet dat de bestanden bruikbaar zijn zonder aanzienlijke technische kennis en toewijding. Ons primaire gebruiksdoel is de distributie van bestanden en bijbehorende metadata uit verschillende bestaande collecties. Onze belangrijkste overwegingen zijn: Enkele niet-doelen: Aangezien Anna’s Archief open source is, willen we ons formaat direct gebruiken. Wanneer we onze zoekindex vernieuwen, hebben we alleen toegang tot openbaar beschikbare paden, zodat iedereen die onze bibliotheek forked snel aan de slag kan. <strong>AAC.</strong> AAC (Anna’s Archief Container) is een enkel item bestaande uit <strong>metadata</strong>, en optioneel <strong>binaire data</strong>, die beide onveranderlijk zijn. Het heeft een wereldwijd unieke identificator, genaamd <strong>AACID</strong>. <strong>AACID.</strong> Het formaat van AACID is als volgt: <code style="color: #0093ff">aacid__{collectie}__{ISO 8601 tijdstempel}__{collectie-specifieke ID}__{shortuuid}</code>. Bijvoorbeeld, een daadwerkelijke AACID die we hebben uitgebracht is <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-bereik.</strong> Aangezien AACID's monotoon toenemende tijdstempels bevatten, kunnen we deze gebruiken om bereiken binnen een bepaalde collectie aan te duiden. We gebruiken dit formaat: <code style="color: blue">aacid__{collectie}__{van_tijdstempel}--{tot_tijdstempel}</code>, waarbij de tijdstempels inclusief zijn. Dit is consistent met ISO 8601-notatie. Bereiken zijn continu en kunnen overlappen, maar in geval van overlap moeten ze identieke records bevatten als degene die eerder in die collectie zijn uitgebracht (aangezien AAC's onveranderlijk zijn). Ontbrekende records zijn niet toegestaan. <code>{collectie}</code>: de naam van de collectie, die ASCII-letters, cijfers en underscores kan bevatten (maar geen dubbele underscores). <code>{collectie-specifieke ID}</code>: een collectie-specifieke identificator, indien van toepassing, bijv. de Z-Library ID. Mag worden weggelaten of ingekort. Moet worden weggelaten of ingekort als de AACID anders meer dan 150 tekens zou bevatten. <code>{ISO 8601 tijdstempel}</code>: een korte versie van de ISO 8601, altijd in UTC, bijv. <code>20220723T194746Z</code>. Dit nummer moet monotoon toenemen bij elke release, hoewel de exacte betekenis per collectie kan verschillen. We raden aan om de tijd van scraping of het genereren van de ID te gebruiken. <code>{shortuuid}</code>: een UUID maar gecomprimeerd naar ASCII, bijv. met behulp van base57. We gebruiken momenteel de <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-bibliotheek. <strong>Binaire gegevensmap.</strong> Een map met de binaire gegevens van een reeks AAC's, voor een specifieke collectie. Deze hebben de volgende eigenschappen: De map moet gegevensbestanden bevatten voor alle AAC's binnen het gespecificeerde bereik. Elk gegevensbestand moet zijn AACID als bestandsnaam hebben (geen extensies). De mapnaam moet een AACID-bereik zijn, voorafgegaan door <code style="color: green">annas_archive_data__</code>, en geen achtervoegsel. Bijvoorbeeld, een van onze daadwerkelijke releases heeft een map genaamd<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Het wordt aanbevolen om deze mappen enigszins beheersbaar in grootte te maken, bijvoorbeeld niet groter dan 100GB-1TB elk, hoewel deze aanbeveling in de loop van de tijd kan veranderen. <strong>Collectie.</strong> Elke AAC behoort tot een collectie, die per definitie een lijst is van AAC's die semantisch consistent zijn. Dat betekent dat als u een significante verandering aanbrengt in het formaat van de metadata, u een nieuwe collectie moet creëren. De standaard <strong>Metadata-bestand.</strong> Een metadata-bestand bevat de metadata van een reeks AAC's, voor een specifieke collectie. Deze hebben de volgende eigenschappen: <code>data_folder</code> is optioneel en is de naam van de binaire gegevensmap die de bijbehorende binaire gegevens bevat. De bestandsnaam van de bijbehorende binaire gegevens binnen die map is de AACID van het record. Elk JSON-object moet de volgende velden op het hoogste niveau bevatten: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optioneel). Geen andere velden zijn toegestaan. De bestandsnaam moet een AACID-bereik zijn, voorafgegaan door <code style="color: red">annas_archive_meta__</code> en gevolgd door <code>.jsonl.zstd</code>. Bijvoorbeeld, een van onze releases heet<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Zoals aangegeven door de bestandsextensie, is het bestandstype <a %(jsonlines)s>JSON Lines</a> gecomprimeerd met <a %(zstd)s>Zstandard</a>. <code>metadata</code> is willekeurige metadata, volgens de semantiek van de collectie. Het moet semantisch consistent zijn binnen de collectie. Het voorvoegsel <code style="color: red">annas_archive_meta__</code> kan worden aangepast aan de naam van uw instelling, bijvoorbeeld <code style="color: red">my_institute_meta__</code>. <strong>“records” en “files” collecties.</strong> Volgens conventie is het vaak handig om “records” en “files” als verschillende collecties uit te brengen, zodat ze op verschillende tijdschema's kunnen worden uitgebracht, bijvoorbeeld op basis van scraping-snelheden. Een “record” is een collectie die alleen uit metadata bestaat, met informatie zoals boektitels, auteurs, ISBN's, enz., terwijl “files” de collecties zijn die de daadwerkelijke bestanden zelf bevatten (pdf, epub). Uiteindelijk hebben we gekozen voor een relatief eenvoudige standaard. Het is vrij los, niet-normatief en een werk in uitvoering. <strong>Torrents.</strong> De metadata-bestanden en binaire gegevensmappen kunnen worden gebundeld in torrents, met één torrent per metadata-bestand of één torrent per binaire gegevensmap. De torrents moeten de originele bestands-/mapnaam plus een <code>.torrent</code> achtervoegsel als hun bestandsnaam hebben. <a %(wikipedia_annas_archive)s>Anna’s Archief</a> is veruit de grootste schaduw bibliotheek ter wereld geworden, en de enige schaduw bibliotheek van deze schaal die volledig open-source en open-data is. Hieronder staat een tabel van onze Datasets-pagina (licht aangepast): We hebben dit op drie manieren bereikt: Het spiegelen van bestaande open-data schaduw bibliotheken (zoals Sci-Hub en Library Genesis). Het helpen van schaduw bibliotheken die meer open willen zijn, maar niet de tijd of middelen hadden om dit te doen (zoals de Libgen stripverzameling). Het scrapen van bibliotheken die niet in bulk willen delen (zoals Z-Library). Voor (2) en (3) beheren we nu zelf een aanzienlijke collectie torrents (honderden TB's). Tot nu toe hebben we deze collecties als eenmalige projecten benaderd, wat betekent dat er voor elke collectie op maat gemaakte infrastructuur en dataorganisatie is. Dit voegt aanzienlijke overhead toe aan elke release en maakt het bijzonder moeilijk om meer incrementele releases te doen. Daarom hebben we besloten om onze releases te standaardiseren. Dit is een technische blogpost waarin we onze standaard introduceren: <strong>Anna’s Archief Containers</strong>. Anna’s Archief Containers (AAC): standaardiseren van releases van 's werelds grootste schaduw bibliotheek Anna’s Archief is de grootste schaduw bibliotheek ter wereld geworden, waardoor we onze releases moeten standaardiseren. 300GB+ aan boekomslagen vrijgegeven Ten slotte zijn we blij een kleine release aan te kondigen. In samenwerking met de mensen die de Libgen.rs fork beheren, delen we al hun boekomslagen via torrents en IPFS. Dit zal de belasting van het bekijken van de omslagen over meer machines verdelen en ze beter behouden. In veel (maar niet alle) gevallen zijn de boekomslagen opgenomen in de bestanden zelf, dus dit is een soort van “afgeleide gegevens”. Maar het hebben in IPFS is nog steeds erg nuttig voor de dagelijkse werking van zowel Anna’s Archief als de verschillende Library Genesis forks. Zoals gebruikelijk kun je deze release vinden bij de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>). We zullen hier niet naar linken, maar je kunt het gemakkelijk vinden. Hopelijk kunnen we ons tempo een beetje ontspannen, nu we een fatsoenlijk alternatief voor Z-Library hebben. Deze werklast is niet bijzonder duurzaam. Als je geïnteresseerd bent in het helpen met programmeren, serverbeheer of conserveringswerk, neem dan zeker contact met ons op. Er is nog veel <a %(annas_archive)s>werk te doen</a>. Bedankt voor je interesse en steun. Overstap naar ElasticSearch Sommige zoekopdrachten duurden super lang, tot het punt waarop ze alle open verbindingen in beslag namen. Standaard heeft MySQL een minimale woordlengte, of je index kan echt groot worden. Mensen meldden dat ze niet konden zoeken naar “Ben Hur”. Zoeken was alleen enigszins snel wanneer volledig in het geheugen geladen, wat vereiste dat we een duurdere machine moesten aanschaffen om dit op te draaien, plus enkele commando's om de index bij het opstarten vooraf te laden. We zouden het niet gemakkelijk hebben kunnen uitbreiden om nieuwe functies te bouwen, zoals betere <a %(wikipedia_cjk_characters)s>tokenisatie voor niet-spatiegebruikende talen</a>, filtering/facettering, sorteren, "bedoelde u" suggesties, autocompleteren, enzovoort. Een van onze <a %(annas_archive)s>tickets</a> was een grabbelton van problemen met ons zoeksysteem. We gebruikten MySQL full-text search, aangezien we al onze data toch al in MySQL hadden. Maar het had zijn beperkingen: Na overleg met een aantal experts hebben we gekozen voor ElasticSearch. Het is niet perfect geweest (hun standaard “bedoelde u” suggesties en autocompleet functies zijn slecht), maar over het algemeen is het veel beter geweest dan MySQL voor zoeken. We zijn nog steeds niet <a %(youtube)s>te enthousiast</a> om het te gebruiken voor enige missie-kritieke gegevens (hoewel ze veel <a %(elastic_co)s>vooruitgang</a> hebben geboekt), maar over het algemeen zijn we best tevreden met de overstap. Voor nu hebben we een veel snellere zoekfunctie geïmplementeerd, betere taalondersteuning, betere relevantiesortering, verschillende sorteeropties en filtering op taal/boekt type/bestandstype. Als je nieuwsgierig bent hoe het werkt, <a %(annas_archive_l140)s>kijk</a> <a %(annas_archive_l1115)s>eens</a> <a %(annas_archive_l1635)s>hier</a>. Het is redelijk toegankelijk, hoewel het wat meer opmerkingen zou kunnen gebruiken… Anna’s Archief is volledig open source Wij geloven dat informatie vrij moet zijn, en onze eigen code is daarop geen uitzondering. We hebben al onze code vrijgegeven op onze privé gehoste Gitlab-instantie: <a %(annas_archive)s>Anna’s Software</a>. We gebruiken ook de issue tracker om ons werk te organiseren. Als je wilt deelnemen aan onze ontwikkeling, is dit een geweldige plek om te beginnen. Om je een idee te geven van de dingen waar we aan werken, neem ons recente werk aan prestatieverbeteringen aan de clientzijde. Aangezien we nog geen paginering hebben geïmplementeerd, zouden we vaak zeer lange zoekpagina's retourneren, met 100-200 resultaten. We wilden de zoekresultaten niet te snel afkappen, maar dit betekende wel dat het sommige apparaten zou vertragen. Hiervoor hebben we een kleine truc geïmplementeerd: we hebben de meeste zoekresultaten in HTML-opmerkingen gewikkeld (<code><!-- --></code>), en vervolgens een kleine Javascript geschreven die zou detecteren wanneer een resultaat zichtbaar zou moeten worden, op welk moment we de opmerking zouden uitpakken: DOM "virtualisatie" geïmplementeerd in 23 regels, geen behoefte aan fancy libraries! Dit is het soort snelle pragmatische code dat je krijgt wanneer je beperkte tijd hebt en echte problemen moet oplossen. Er is gerapporteerd dat onze zoekfunctie nu goed werkt op langzame apparaten! Een andere grote inspanning was het automatiseren van het bouwen van de database. Toen we lanceerden, hebben we gewoon lukraak verschillende bronnen samengevoegd. Nu willen we ze up-to-date houden, dus hebben we een aantal scripts geschreven om nieuwe metadata van de twee Library Genesis forks te downloaden en te integreren. Het doel is niet alleen om dit nuttig te maken voor ons archief, maar om het gemakkelijk te maken voor iedereen die wil experimenteren met schaduw bibliotheek metadata. Het doel zou een Jupyter-notebook zijn dat allerlei interessante metadata beschikbaar heeft, zodat we meer onderzoek kunnen doen, zoals uitzoeken welk <a %(blog)s>percentage van ISBN's voor altijd bewaard blijft</a>. Ten slotte hebben we ons donatiesysteem vernieuwd. Je kunt nu een creditcard gebruiken om direct geld op onze crypto wallets te storten, zonder echt iets te hoeven weten over cryptocurrencies. We zullen blijven monitoren hoe goed dit in de praktijk werkt, maar dit is een grote stap. Met Z-Library die offline gaat en de (vermeende) oprichters die gearresteerd worden, hebben we dag en nacht gewerkt om een goed alternatief te bieden met Anna’s Archief (we zullen het hier niet linken, maar je kunt het googelen). Hier zijn enkele van de dingen die we recentelijk hebben bereikt. Anna’s Update: volledig open source archief, ElasticSearch, 300GB+ aan boekomslagen We hebben dag en nacht gewerkt om een goed alternatief te bieden met Anna’s Archief. Hier zijn enkele van de dingen die we recentelijk hebben bereikt. Analyse Semantische duplicaten (verschillende scans van hetzelfde boek) kunnen theoretisch worden uitgefilterd, maar het is lastig. Bij het handmatig doorzoeken van de strips vonden we te veel valse positieven. Er zijn enkele duplicaten puur op basis van MD5, wat relatief verspilling is, maar het filteren daarvan zou ons slechts ongeveer 1% in besparing opleveren. Op deze schaal is dat nog steeds ongeveer 1TB, maar ook, op deze schaal maakt 1TB niet echt uit. We willen liever niet het risico lopen om per ongeluk gegevens te vernietigen in dit proces. We vonden een hoop niet-boekgegevens, zoals films gebaseerd op stripboeken. Dat lijkt ook verspilling, aangezien deze al op andere manieren breed beschikbaar zijn. We realiseerden ons echter dat we filmbestanden niet zomaar konden filteren, omdat er ook <em>interactieve stripboeken</em> zijn die op de computer zijn uitgebracht, die iemand heeft opgenomen en als films heeft opgeslagen. Uiteindelijk zou alles wat we uit de collectie zouden kunnen verwijderen slechts een paar procent besparen. Toen herinnerden we ons dat we datahoarders zijn, en de mensen die dit zullen spiegelen zijn ook datahoarders, en dus, “WAT BEDOEL JE, VERWIJDEREN?!” :) Wanneer je 95TB in je opslagcluster gedumpt krijgt, probeer je te begrijpen wat er überhaupt in zit… We hebben wat analyses gedaan om te zien of we de grootte een beetje konden verminderen, bijvoorbeeld door duplicaten te verwijderen. Hier zijn enkele van onze bevindingen: We presenteren u daarom de volledige, ongewijzigde collectie. Het is veel data, maar we hopen dat genoeg mensen het toch zullen willen seeden. Samenwerking Gezien de omvang stond deze collectie al lang op onze verlanglijst, dus na ons succes met het back-uppen van Z-Library, richtten we onze pijlen op deze collectie. In eerste instantie schraapten we het direct, wat een behoorlijke uitdaging was, aangezien hun server niet in de beste staat was. Op deze manier kregen we ongeveer 15TB, maar het ging langzaam. Gelukkig slaagden we erin om in contact te komen met de beheerder van de bibliotheek, die ermee instemde om ons alle gegevens direct te sturen, wat veel sneller ging. Het duurde nog steeds meer dan een half jaar om alle gegevens over te dragen en te verwerken, en we verloren bijna alles door schijfcorruptie, wat zou hebben betekend dat we helemaal opnieuw moesten beginnen. Deze ervaring heeft ons doen geloven dat het belangrijk is om deze gegevens zo snel mogelijk te verspreiden, zodat ze wijd en zijd gespiegeld kunnen worden. We zijn slechts één of twee ongelukkig getimede incidenten verwijderd van het voor altijd verliezen van deze collectie! De collectie Snel handelen betekent wel dat de collectie een beetje ongeorganiseerd is… Laten we eens kijken. Stel je voor dat we een bestandssysteem hebben (dat we in werkelijkheid over torrents verdelen): De eerste directory, <code>/repository</code>, is het meer gestructureerde deel hiervan. Deze directory bevat zogenaamde "duizend mappen": mappen elk met duizend bestanden, die incrementeel genummerd zijn in de database. Directory <code>0</code> bevat bestanden met comic_id 0–999, enzovoort. Dit is hetzelfde schema dat Library Genesis gebruikt voor zijn fictie- en non-fictiecollecties. Het idee is dat elke "duizend map" automatisch wordt omgezet in een torrent zodra deze vol is. Echter, de Libgen.li operator heeft nooit torrents gemaakt voor deze collectie, en dus werden de duizend mappen waarschijnlijk onhandig, en maakten plaats voor “ongesorteerde mappen”. Dit zijn <code>/comics0</code> tot <code>/comics4</code>. Ze bevatten allemaal unieke directorystructuren, die waarschijnlijk logisch waren voor het verzamelen van de bestanden, maar nu niet veel zin meer hebben voor ons. Gelukkig verwijst de metadata nog steeds direct naar al deze bestanden, dus hun opslagorganisatie op schijf doet er eigenlijk niet toe! De metadata is beschikbaar in de vorm van een MySQL-database. Deze kan direct worden gedownload van de Libgen.li-website, maar we zullen het ook beschikbaar maken in een torrent, naast onze eigen tabel met alle MD5-hashes. <q>Dr. Barbara Gordon probeert zichzelf te verliezen in de alledaagse wereld van de bibliotheek…</q> Libgen forks Eerst wat achtergrondinformatie. Je kent Library Genesis misschien vanwege hun epische boekencollectie. Minder mensen weten dat vrijwilligers van Library Genesis andere projecten hebben gecreëerd, zoals een aanzienlijke collectie tijdschriften en standaarddocumenten, een volledige back-up van Sci-Hub (in samenwerking met de oprichter van Sci-Hub, Alexandra Elbakyan), en inderdaad, een enorme collectie strips. Op een gegeven moment gingen verschillende beheerders van Library Genesis-mirrors hun eigen weg, wat leidde tot de huidige situatie met een aantal verschillende "forks", die allemaal nog steeds de naam Library Genesis dragen. De Libgen.li-fork heeft uniek deze stripverzameling, evenals een aanzienlijke tijdschriftencollectie (waar we ook aan werken). Fondsenwerving We brengen deze data in enkele grote stukken uit. De eerste torrent is van <code>/comics0</code>, die we in één enorme 12TB .tar-bestand hebben gestopt. Dat is beter voor je harde schijf en torrentsoftware dan een ontelbaar aantal kleinere bestanden. Als onderdeel van deze release houden we een fondsenwerving. We willen $20.000 inzamelen om de operationele en contractkosten voor deze collectie te dekken, evenals om lopende en toekomstige projecten mogelijk te maken. We hebben enkele <em>enorme</em> projecten in de maak. <em>Wie steun ik met mijn donatie?</em> In het kort: we maken een back-up van alle kennis en cultuur van de mensheid en maken het gemakkelijk toegankelijk. Al onze code en data zijn open source, we zijn een volledig door vrijwilligers gerund project, en we hebben tot nu toe 125TB aan boeken gered (naast de bestaande torrents van Libgen en Scihub). Uiteindelijk bouwen we een vliegwiel dat mensen in staat stelt en stimuleert om alle boeken ter wereld te vinden, te scannen en te back-uppen. We zullen in een toekomstige post over ons meesterplan schrijven. :) Als je doneert voor een 12 maanden “Amazing Archivist” lidmaatschap ($780), kun je <strong>“een torrent adopteren”</strong>, wat betekent dat we je gebruikersnaam of bericht in de bestandsnaam van een van de torrents zullen plaatsen! Je kunt doneren door naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a> te gaan en op de knop “Doneer” te klikken. We zijn ook op zoek naar meer vrijwilligers: software-ingenieurs, beveiligingsonderzoekers, experts in anonieme handel en vertalers. Je kunt ons ook steunen door hostingdiensten te bieden. En natuurlijk, seed onze torrents alstublieft! Dank aan iedereen die ons al zo genereus heeft gesteund! Jullie maken echt een verschil. Hier zijn de torrents die tot nu toe zijn uitgebracht (we verwerken de rest nog): Alle torrents zijn te vinden op <a %(wikipedia_annas_archive)s>Anna’s Archief</a> onder “Datasets” (we linken daar niet direct naartoe, zodat links naar deze blog niet van Reddit, Twitter, etc. worden verwijderd). Van daaruit volg je de link naar de Tor-website. <a %(news_ycombinator)s>Bespreek op Hacker News</a> Wat is de volgende stap? Een hoop torrents zijn geweldig voor langdurige bewaring, maar niet zozeer voor dagelijks gebruik. We zullen samenwerken met hostingpartners om al deze data op het web te krijgen (aangezien Anna’s Archief zelf niets host). Natuurlijk kun je deze downloadlinks vinden op Anna’s Archief. We nodigen iedereen ook uit om iets met deze data te doen! Help ons het beter te analyseren, dedupliceren, op IPFS te zetten, het te remixen, je AI-modellen ermee te trainen, enzovoort. Het is allemaal van jou, en we kunnen niet wachten om te zien wat je ermee doet. Tenslotte, zoals eerder gezegd, hebben we nog enkele enorme releases in de planning (als <em>iemand</em> ons per <em>ongeluk</em> een dump van een <em>bepaalde</em> ACS4-database zou kunnen sturen, weet je waar je ons kunt vinden...), evenals het bouwen van het vliegwiel voor het back-uppen van alle boeken ter wereld. Dus blijf op de hoogte, we zijn nog maar net begonnen. - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) De grootste schaduw bibliotheek van stripboeken is waarschijnlijk die van een bepaalde Library Genesis fork: Libgen.li. De enige beheerder die die site runt, slaagde erin een waanzinnige stripboekencollectie van meer dan 2 miljoen bestanden te verzamelen, met een totaal van meer dan 95TB. Echter, in tegenstelling tot andere Library Genesis collecties, was deze niet in bulk beschikbaar via torrents. Je kon deze strips alleen individueel benaderen via zijn langzame persoonlijke server — een enkel storingspunt. Tot vandaag! In deze post vertellen we je meer over deze collectie en over onze inzamelingsactie om meer van dit werk te ondersteunen. Anna’s Archief heeft de grootste schaduw bibliotheek van stripboeken ter wereld geback-upt (95TB) — je kunt helpen deze te seeden De grootste schaduw bibliotheek van stripboeken ter wereld had een enkel storingspunt.. tot vandaag. Waarschuwing: deze blogpost is verouderd. We hebben besloten dat IPFS nog niet klaar is voor prime time. We zullen nog steeds linken naar bestanden op IPFS vanuit Anna’s Archief wanneer mogelijk, maar we zullen het niet langer zelf hosten, noch raden we anderen aan om te spiegelen met IPFS. Bekijk onze Torrents-pagina als u wilt helpen onze collectie te behouden. 5.998.794 boeken op IPFS plaatsen Een vermenigvuldiging van kopieën Terug naar onze oorspronkelijke vraag: hoe kunnen we claimen onze collecties voor altijd te bewaren? Het belangrijkste probleem hier is dat onze collectie in een snel tempo <a %(torrents_stats)s>groeit</a>, door het scrapen en open-sourcen van enkele enorme collecties (bovenop het geweldige werk dat al is gedaan door andere open-data schaduw bibliotheken zoals Sci-Hub en Library Genesis). Deze groei in data maakt het moeilijker om de collecties wereldwijd te spiegelen. Dataopslag is duur! Maar we zijn optimistisch, vooral bij het observeren van de volgende drie trends. De <a %(annas_archive_stats)s>totale grootte</a> van onze collecties, over de afgelopen maanden, uitgesplitst naar aantal torrent seeders. HDD-prijstrends van verschillende bronnen (klik om de studie te bekijken). <a %(critical_window_chinese)s>Chinese versie 中文版</a>, bespreek op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. We hebben het laaghangend fruit geplukt Dit volgt direct uit onze hierboven besproken prioriteiten. We geven de voorkeur aan het eerst bevrijden van grote collecties. Nu we enkele van de grootste collecties ter wereld hebben veiliggesteld, verwachten we dat onze groei veel langzamer zal zijn. Er is nog steeds een lange staart van kleinere collecties, en er worden elke dag nieuwe boeken gescand of gepubliceerd, maar het tempo zal waarschijnlijk veel langzamer zijn. We kunnen nog steeds verdubbelen of zelfs verdrievoudigen in omvang, maar over een langere periode. OCR-verbeteringen. Prioriteiten Wetenschap & techniek softwarecode Fictieve of entertainmentversies van al het bovenstaande Geografische gegevens (bijv. kaarten, geologische onderzoeken) Interne gegevens van bedrijven of overheden (lekken) Meetgegevens zoals wetenschappelijke metingen, economische gegevens, bedrijfsrapporten Metadata records in het algemeen (van non-fictie en fictie; van andere media, kunst, mensen, enz.; inclusief recensies) Non-fictie boeken Non-fictie tijdschriften, kranten, handleidingen Non-fictie transcripties van lezingen, documentaires, podcasts Organische data zoals DNA-sequenties, plantenzaden of microbiële monsters Academische papers, tijdschriften, rapporten Wetenschaps- en techniekwebsites, online discussies Transcripties van juridische of gerechtelijke procedures Uniek in gevaar van vernietiging (bijv. door oorlog, bezuinigingen, rechtszaken of politieke vervolging) Zeldzaam Uniek onderbelicht Waarom geven we zoveel om papers en boeken? Laten we ons fundamentele geloof in behoud in het algemeen even terzijde schuiven — we kunnen daar een ander bericht over schrijven. Dus waarom specifiek papers en boeken? Het antwoord is simpel: <strong>informatiedichtheid</strong>. Per megabyte opslag slaat geschreven tekst de meeste informatie op van alle media. Hoewel we zowel om kennis als cultuur geven, geven we meer om het eerste. Over het algemeen vinden we een hiërarchie van informatiedichtheid en belang van behoud die er ongeveer zo uitziet: De rangschikking in deze lijst is enigszins willekeurig — verschillende items zijn gelijk of er zijn meningsverschillen binnen ons team — en we vergeten waarschijnlijk enkele belangrijke categorieën. Maar dit is ongeveer hoe we prioriteren. Sommige van deze items zijn te verschillend van de anderen om ons zorgen over te maken (of worden al verzorgd door andere instellingen), zoals organische gegevens of geografische gegevens. Maar de meeste items in deze lijst zijn eigenlijk belangrijk voor ons. Een andere grote factor in onze prioritering is hoe groot het risico is dat een bepaald werk loopt. We geven de voorkeur aan werken die: Tenslotte geven we om schaal. We hebben beperkte tijd en geld, dus besteden we liever een maand aan het redden van 10.000 boeken dan 1.000 boeken — als ze ongeveer even waardevol en risicovol zijn. <em><q>Het verloren kan niet worden hersteld; maar laten we redden wat er overblijft: niet door kluizen en sloten die hen van het publieke oog en gebruik afschermen, door ze aan de vergetelheid over te laten, maar door een zodanige vermenigvuldiging van kopieën, dat ze buiten het bereik van het toeval worden geplaatst.</q></em><br>— Thomas Jefferson, 1791 Schaduw bibliotheken Code kan open source zijn op Github, maar Github als geheel kan niet gemakkelijk worden gemirrord en dus bewaard blijven (hoewel er in dit specifieke geval voldoende verspreide kopieën van de meeste coderepositories zijn) Metadatarecords kunnen vrij worden bekeken op de Worldcat-website, maar niet in bulk worden gedownload (totdat we ze <a %(worldcat_scrape)s>gescrapet</a> hebben) Reddit is gratis te gebruiken, maar heeft onlangs strenge anti-scraping maatregelen ingevoerd, in de nasleep van data-hongerige LLM-training (daarover later meer) Er zijn veel organisaties met vergelijkbare missies en prioriteiten. Inderdaad, er zijn bibliotheken, archieven, laboratoria, musea en andere instellingen die belast zijn met het behoud van dit soort zaken. Veel van deze worden goed gefinancierd door overheden, individuen of bedrijven. Maar ze hebben één enorme blinde vlek: het juridische systeem. Hierin ligt de unieke rol van schaduw bibliotheken, en de reden waarom Anna’s Archief bestaat. Wij kunnen dingen doen die andere instellingen niet mogen doen. Nu is het niet (vaak) zo dat we materialen kunnen archiveren die elders illegaal zijn om te bewaren. Nee, het is in veel plaatsen legaal om een archief te bouwen met boeken, papers, tijdschriften, enzovoort. Maar wat legale archieven vaak missen, is <strong>redundantie en duurzaamheid</strong>. Er bestaan boeken waarvan slechts één exemplaar in een fysieke bibliotheek ergens aanwezig is. Er bestaan metadatarecords die door één enkel bedrijf worden bewaakt. Er bestaan kranten die alleen op microfilm in een enkel archief bewaard worden. Bibliotheken kunnen te maken krijgen met bezuinigingen, bedrijven kunnen failliet gaan, archieven kunnen gebombardeerd en tot de grond toe afgebrand worden. Dit is niet hypothetisch — dit gebeurt voortdurend. Wat we uniek kunnen doen bij Anna’s Archief is het opslaan van vele kopieën van werken, op grote schaal. We kunnen papers, boeken, tijdschriften en meer verzamelen en in bulk verspreiden. We doen dit momenteel via torrents, maar de exacte technologieën doen er niet toe en zullen in de loop van de tijd veranderen. Het belangrijkste is dat er veel kopieën over de hele wereld worden verspreid. Deze quote van meer dan 200 jaar geleden is nog steeds waar: Een korte opmerking over het publieke domein. Omdat Anna’s Archief zich uniek richt op activiteiten die in veel delen van de wereld illegaal zijn, houden we ons niet bezig met algemeen beschikbare collecties, zoals boeken in het publieke domein. Wettelijke entiteiten zorgen daar vaak al goed voor. Er zijn echter overwegingen die ons soms doen werken aan publiek beschikbare collecties: - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Opslagkosten blijven exponentieel dalen 3. Verbeteringen in informatiedichtheid We slaan momenteel boeken op in de ruwe formaten waarin ze aan ons worden gegeven. Natuurlijk zijn ze gecomprimeerd, maar vaak zijn het nog steeds grote scans of foto’s van pagina’s. Tot nu toe waren de enige opties om de totale omvang van onze collectie te verkleinen door middel van agressievere compressie of deduplicatie. Echter, om voldoende besparingen te realiseren, zijn beide te verliesgevend naar onze smaak. Zware compressie van foto’s kan tekst nauwelijks leesbaar maken. En deduplicatie vereist een hoge mate van vertrouwen dat boeken precies hetzelfde zijn, wat vaak te onnauwkeurig is, vooral als de inhoud hetzelfde is maar de scans op verschillende momenten zijn gemaakt. Er is altijd een derde optie geweest, maar de kwaliteit ervan was zo abominabel dat we het nooit overwogen: <strong>OCR, of Optische Karakterherkenning</strong>. Dit is het proces van het omzetten van foto’s in platte tekst, door AI te gebruiken om de karakters in de foto’s te detecteren. Hulpmiddelen hiervoor bestaan al lang en zijn behoorlijk goed, maar “behoorlijk goed” is niet genoeg voor bewaringsdoeleinden. Echter, recente multi-modale deep-learning modellen hebben extreem snelle vooruitgang geboekt, hoewel nog steeds tegen hoge kosten. We verwachten dat zowel de nauwkeurigheid als de kosten de komende jaren dramatisch zullen verbeteren, tot het punt waarop het realistisch zal worden om op onze gehele bibliotheek toe te passen. Wanneer dat gebeurt, zullen we waarschijnlijk nog steeds de originele bestanden bewaren, maar daarnaast zouden we een veel kleinere versie van onze bibliotheek kunnen hebben die de meeste mensen willen spiegelen. Het punt is dat ruwe tekst zelf nog beter comprimeert en veel gemakkelijker te dedupliceren is, wat ons nog meer besparingen oplevert. Over het algemeen is het niet onrealistisch om ten minste een 5-10x reductie in totale bestandsgrootte te verwachten, misschien zelfs meer. Zelfs met een conservatieve 5x reductie, zouden we kijken naar <strong>$1.000–$3.000 in 10 jaar, zelfs als onze bibliotheek verdrievoudigt in grootte</strong>. Op het moment van schrijven zijn <a %(diskprices)s>schijfprijzen</a> per TB ongeveer $12 voor nieuwe schijven, $8 voor gebruikte schijven en $4 voor tape. Als we conservatief zijn en alleen naar nieuwe schijven kijken, betekent dat dat het opslaan van een petabyte ongeveer $12.000 kost. Als we aannemen dat onze bibliotheek zal verdrievoudigen van 900TB naar 2,7PB, zou dat $32.400 betekenen om onze hele bibliotheek te mirroren. Met elektriciteit, kosten van andere hardware, enzovoort, ronden we het af op $40.000. Of met tape meer als $15.000–$20.000. Aan de ene kant is <strong>$15.000–$40.000 voor de som van alle menselijke kennis een koopje</strong>. Aan de andere kant is het een beetje veel om te verwachten dat er tonnen volledige kopieën zijn, vooral als we ook willen dat die mensen hun torrents blijven seeden ten behoeve van anderen. Dat is vandaag. Maar de vooruitgang gaat door: De kosten van harde schijven per TB zijn de afgelopen 10 jaar ruwweg met een derde gedaald en zullen waarschijnlijk in een vergelijkbaar tempo blijven dalen. Tape lijkt een vergelijkbaar traject te volgen. SSD-prijzen dalen nog sneller en zouden tegen het einde van het decennium de prijzen van HDD's kunnen overnemen. Als dit standhoudt, dan kijken we over 10 jaar misschien naar slechts $5.000–$13.000 om onze hele collectie te mirroren (1/3e), of zelfs minder als we minder in omvang groeien. Hoewel het nog steeds veel geld is, zal dit voor veel mensen haalbaar zijn. En het kan zelfs beter zijn vanwege het volgende punt… Bij Anna’s Archief worden we vaak gevraagd hoe we kunnen beweren onze collecties voor altijd te behouden, terwijl de totale omvang al bijna 1 Petabyte (1000 TB) nadert en nog steeds groeit. In dit artikel bekijken we onze filosofie en zien we waarom het volgende decennium cruciaal is voor onze missie om de kennis en cultuur van de mensheid te behouden. Kritiek venster Als deze voorspellingen accuraat zijn, hoeven we <strong>maar een paar jaar te wachten</strong> voordat onze hele collectie op grote schaal wordt gemirrord. Dus, in de woorden van Thomas Jefferson, “buiten het bereik van een ongeluk geplaatst.” Helaas heeft de opkomst van LLM's, en hun data-hongerige training, veel auteursrechthouders in de verdediging gedwongen. Nog meer dan ze al waren. Veel websites maken het moeilijker om te scrapen en archiveren, rechtszaken vliegen in het rond, en ondertussen blijven fysieke bibliotheken en archieven verwaarloosd. We kunnen alleen verwachten dat deze trends zullen blijven verslechteren, en dat veel werken verloren zullen gaan ruim voordat ze het publieke domein betreden. <strong>We staan aan de vooravond van een revolutie in bewaring, maar <q>het verloren kan niet worden hersteld.</q></strong> We hebben een kritieke periode van ongeveer 5-10 jaar waarin het nog steeds vrij duur is om een schaduw bibliotheek te exploiteren en veel mirrors over de hele wereld te creëren, en waarin de toegang nog niet volledig is afgesloten. Als we dit venster kunnen overbruggen, dan hebben we inderdaad de kennis en cultuur van de mensheid voor altijd bewaard. We mogen deze tijd niet verspillen. We mogen dit kritieke venster niet voor ons laten sluiten. Laten we gaan. Het kritieke venster van schaduw bibliotheken Hoe kunnen we beweren onze collecties voor altijd te behouden, terwijl ze al bijna 1 PB naderen? Collectie Wat meer informatie over de collectie. <a %(duxiu)s>Duxiu</a> is een enorme database van gescande boeken, gecreëerd door de <a %(chaoxing)s>SuperStar Digital Library Group</a>. De meeste zijn academische boeken, gescand om ze digitaal beschikbaar te maken voor universiteiten en bibliotheken. Voor ons Engelssprekende publiek hebben <a %(library_princeton)s>Princeton</a> en de <a %(guides_lib_uw)s>University of Washington</a> goede overzichten. Er is ook een uitstekend artikel dat meer achtergrond geeft: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (zoek het op in Anna’s Archief). De boeken van Duxiu zijn al lang gepirateerd op het Chinese internet. Meestal worden ze voor minder dan een dollar verkocht door wederverkopers. Ze worden doorgaans verspreid via het Chinese equivalent van Google Drive, dat vaak is gehackt om meer opslagruimte mogelijk te maken. Enkele technische details zijn te vinden <a %(github_duty_machine)s>hier</a> en <a %(github_821_github_io)s>hier</a>. Hoewel de boeken semi-openbaar zijn verspreid, is het vrij moeilijk om ze in bulk te verkrijgen. We hadden dit hoog op onze TODO-lijst staan en hebben er meerdere maanden fulltime werk aan toegewezen. Echter, onlangs nam een ongelooflijke, geweldige en getalenteerde vrijwilliger contact met ons op, die ons vertelde dat ze al dit werk al hadden gedaan — tegen grote kosten. Ze deelden de volledige collectie met ons, zonder iets terug te verwachten, behalve de garantie van langdurige bewaring. Echt opmerkelijk. Ze stemden ermee in om op deze manier om hulp te vragen om de collectie te OCR'en. De collectie bestaat uit 7.543.702 bestanden. Dit is meer dan Library Genesis non-fictie (ongeveer 5,3 miljoen). De totale bestandsgrootte is ongeveer 359TB (326TiB) in zijn huidige vorm. We staan open voor andere voorstellen en ideeën. Neem gewoon contact met ons op. Bekijk Anna’s Archief voor meer informatie over onze collecties, bewaringsinspanningen en hoe u kunt helpen. Bedankt! Voorbeeldpagina's Om ons te bewijzen dat je een goede pijplijn hebt, zijn hier enkele voorbeeldpagina's om mee te beginnen, uit een boek over supergeleiders. Je pijplijn moet goed omgaan met wiskunde, tabellen, grafieken, voetnoten, enzovoort. Stuur je verwerkte pagina's naar ons e-mailadres. Als ze er goed uitzien, sturen we je meer in privé, en we verwachten dat je je pijplijn daar ook snel op kunt draaien. Zodra we tevreden zijn, kunnen we een deal sluiten. - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Chinese versie 中文版</a>, <a %(news_ycombinator)s>Discussieer op Hacker News</a> Dit is een kort blogbericht. We zijn op zoek naar een bedrijf of instelling die ons kan helpen met OCR en tekstuittreksels voor een enorme collectie die we hebben verworven, in ruil voor exclusieve vroege toegang. Na de embargo-periode zullen we natuurlijk de hele collectie vrijgeven. Hoogwaardige academische teksten zijn uiterst nuttig voor de training van LLM's. Hoewel onze collectie Chinees is, zou dit zelfs nuttig moeten zijn voor de training van Engelse LLM's: modellen lijken concepten en kennis te coderen, ongeacht de brontaal. Hiervoor moet tekst uit de scans worden gehaald. Wat krijgt Anna’s Archief hieruit? Volledige tekstzoekfunctie van de boeken voor haar gebruikers. Omdat onze doelen overeenkomen met die van LLM-ontwikkelaars, zijn we op zoek naar een samenwerkingspartner. We zijn bereid om je <strong>exclusieve vroege toegang tot deze collectie in bulk voor 1 jaar</strong> te geven, als je goede OCR en tekstuittreksels kunt doen. Als je bereid bent om de volledige code van je pijplijn met ons te delen, zouden we bereid zijn om de collectie langer te embargeren. Exclusieve toegang voor LLM-bedrijven tot de grootste Chinese non-fictieboekencollectie ter wereld <em><strong>Kort samengevat:</strong> Anna’s Archief heeft een unieke collectie van 7,5 miljoen / 350TB Chinese non-fictieboeken verworven — groter dan Library Genesis. We zijn bereid een LLM-bedrijf exclusieve toegang te geven, in ruil voor hoogwaardige OCR en tekstuittreksels.</em> Systeemarchitectuur Stel dat je enkele bedrijven hebt gevonden die bereid zijn je website te hosten zonder je af te sluiten — laten we deze “vrijheidslievende providers” noemen 😄. Je zult snel merken dat het hosten van alles bij hen vrij duur is, dus je wilt misschien enkele “goedkope providers” vinden en daar de daadwerkelijke hosting doen, via de vrijheidslievende providers. Als je het goed doet, zullen de goedkope providers nooit weten wat je host en nooit klachten ontvangen. Met al deze providers is er een risico dat ze je toch afsluiten, dus je hebt ook redundantie nodig. We hebben dit op alle niveaus van onze stack nodig. Een enigszins vrijheidslievende onderneming die zich in een interessante positie heeft geplaatst, is Cloudflare. Ze hebben <a %(blog_cloudflare)s>aangevoerd</a> dat ze geen hostingprovider zijn, maar een nutsvoorziening, zoals een ISP. Ze zijn daarom niet onderworpen aan DMCA of andere verwijderingsverzoeken en sturen eventuele verzoeken door naar je daadwerkelijke hostingprovider. Ze zijn zelfs zover gegaan dat ze naar de rechter zijn gestapt om deze structuur te beschermen. We kunnen hen daarom gebruiken als een extra laag van caching en bescherming. Cloudflare accepteert geen anonieme betalingen, dus we kunnen alleen hun gratis plan gebruiken. Dit betekent dat we hun load balancing- of failover-functies niet kunnen gebruiken. Daarom hebben we <a %(annas_archive_l255)s>dit zelf geïmplementeerd</a> op domeinniveau. Bij het laden van de pagina controleert de browser of het huidige domein nog beschikbaar is, en zo niet, dan herschrijft het alle URL's naar een ander domein. Aangezien Cloudflare veel pagina's in de cache opslaat, betekent dit dat een gebruiker op ons hoofddomein kan landen, zelfs als de proxyserver niet werkt, en dan bij de volgende klik naar een ander domein wordt verplaatst. We hebben ook nog steeds te maken met normale operationele zorgen, zoals het monitoren van de servergezondheid, het loggen van backend- en frontend-fouten, enzovoort. Onze failover-architectuur zorgt ook voor meer robuustheid op dit gebied, bijvoorbeeld door een compleet andere set servers op een van de domeinen te draaien. We kunnen zelfs oudere versies van de code en datasets op dit aparte domein draaien, voor het geval een kritieke bug in de hoofdversie onopgemerkt blijft. We kunnen ons ook indekken tegen Cloudflare die zich tegen ons keert, door het van een van de domeinen te verwijderen, zoals dit aparte domein. Verschillende permutaties van deze ideeën zijn mogelijk. Conclusie Het is een interessante ervaring geweest om te leren hoe je een robuuste en veerkrachtige zoekmachine voor schaduw bibliotheken opzet. Er zijn nog veel meer details te delen in latere berichten, dus laat me weten waar je meer over wilt leren! Zoals altijd zijn we op zoek naar donaties om dit werk te ondersteunen, dus zorg ervoor dat je de Doneer-pagina op Anna’s Archief bekijkt. We zijn ook op zoek naar andere vormen van ondersteuning, zoals subsidies, langetermijnsponsoren, hoogrisico-betaalproviders, misschien zelfs (stijlvolle!) advertenties. En als je je tijd en vaardigheden wilt bijdragen, zijn we altijd op zoek naar ontwikkelaars, vertalers, enzovoort. Bedankt voor je interesse en steun. Innovatietokens Laten we beginnen met onze tech stack. Die is opzettelijk saai. We gebruiken Flask, MariaDB en ElasticSearch. Dat is letterlijk alles. Zoeken is grotendeels een opgelost probleem, en we zijn niet van plan het opnieuw uit te vinden. Bovendien moeten we onze <a %(mcfunley)s>innovatietokens</a> aan iets anders besteden: niet uit de lucht gehaald worden door de autoriteiten. Dus hoe legaal of illegaal is Anna’s Archief precies? Dit hangt grotendeels af van de juridische jurisdictie. De meeste landen geloven in een vorm van auteursrecht, wat betekent dat mensen of bedrijven een exclusief monopolie krijgen op bepaalde soorten werken voor een bepaalde periode. Terzijde, bij Anna’s Archief geloven we dat hoewel er enkele voordelen zijn, auteursrecht over het algemeen een netto-negatief is voor de samenleving — maar dat is een verhaal voor een andere keer. Dit exclusieve monopolie op bepaalde werken betekent dat het illegaal is voor iedereen buiten dit monopolie om die werken direct te verspreiden — inclusief ons. Maar Anna’s Archief is een zoekmachine die die werken niet direct verspreidt (althans niet op onze clearnet-website), dus we zouden in orde moeten zijn, toch? Niet precies. In veel jurisdicties is het niet alleen illegaal om auteursrechtelijk beschermde werken te verspreiden, maar ook om te linken naar plaatsen die dat doen. Een klassiek voorbeeld hiervan is de Amerikaanse DMCA-wet. Dat is het strengste uiteinde van het spectrum. Aan de andere kant van het spectrum zouden er theoretisch landen kunnen zijn zonder enige auteursrechtwetten, maar die bestaan eigenlijk niet. Vrijwel elk land heeft een vorm van auteursrechtwetgeving. Handhaving is een ander verhaal. Er zijn genoeg landen met regeringen die zich niet druk maken om de handhaving van auteursrechtwetten. Er zijn ook landen tussen de twee uitersten in, die het verspreiden van auteursrechtelijk beschermde werken verbieden, maar niet het linken naar dergelijke werken. Een andere overweging is op bedrijfsniveau. Als een bedrijf opereert in een rechtsgebied dat zich niet bekommert om auteursrechten, maar het bedrijf zelf geen enkel risico wil nemen, dan kunnen ze je website sluiten zodra iemand erover klaagt. Tenslotte is een grote overweging betalingen. Aangezien we anoniem moeten blijven, kunnen we geen traditionele betaalmethoden gebruiken. Dit laat ons met cryptocurrencies, en slechts een klein deel van de bedrijven ondersteunt die (er zijn virtuele debetkaarten betaald met crypto, maar die worden vaak niet geaccepteerd). - Anna en het team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ik run <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, 's werelds grootste open-source non-profit zoekmachine voor <a %(wikipedia_shadow_library)s>schaduw bibliotheken</a>, zoals Sci-Hub, Library Genesis en Z-Library. Ons doel is om kennis en cultuur gemakkelijk toegankelijk te maken, en uiteindelijk een gemeenschap op te bouwen van mensen die samen <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle boeken ter wereld</a> archiveren en bewaren. In dit artikel laat ik zien hoe we deze website runnen, en de unieke uitdagingen die komen kijken bij het beheren van een website met een twijfelachtige juridische status, aangezien er geen “AWS voor schaduw liefdadigheidsinstellingen” is. <em>Bekijk ook het zusterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Hoe word je een piratenarchivaris</a>.</em> Hoe een schaduw bibliotheek te runnen: operaties bij Anna’s Archief Er is geen <q>AWS voor schaduw liefdadigheidsinstellingen,</q> dus hoe runnen we Anna’s Archief? Tools Applicatieserver: Flask, MariaDB, ElasticSearch, Docker. Ontwikkeling: Gitlab, Weblate, Zulip. Serverbeheer: Ansible, Checkmk, UFW. Onion statische hosting: Tor, Nginx. Proxyserver: Varnish. Laten we eens kijken naar welke tools we gebruiken om dit allemaal te bereiken. Dit evolueert sterk naarmate we nieuwe problemen tegenkomen en nieuwe oplossingen vinden. Er zijn enkele beslissingen waar we over en weer over hebben gediscussieerd. Een daarvan is de communicatie tussen servers: we gebruikten hiervoor vroeger Wireguard, maar ontdekten dat het af en toe stopt met het verzenden van gegevens, of alleen gegevens in één richting verzendt. Dit gebeurde bij verschillende Wireguard-configuraties die we probeerden, zoals <a %(github_costela_wesher)s>wesher</a> en <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. We probeerden ook poorten te tunnelen via SSH, met behulp van autossh en sshuttle, maar liepen tegen <a %(github_sshuttle)s>problemen aan</a> (hoewel het nog steeds niet duidelijk is of autossh last heeft van TCP-over-TCP-problemen of niet — het voelt gewoon als een krakkemikkige oplossing voor mij, maar misschien is het eigenlijk prima?). In plaats daarvan zijn we teruggekeerd naar directe verbindingen tussen servers, waarbij we verbergen dat een server draait op de goedkope providers door IP-filtering met UFW te gebruiken. Dit heeft het nadeel dat Docker niet goed werkt met UFW, tenzij je <code>network_mode: "host"</code> gebruikt. Dit alles is wat foutgevoeliger, omdat je je server aan het internet blootstelt met slechts een kleine misconfiguratie. Misschien moeten we teruggaan naar autossh — feedback zou hier zeer welkom zijn. We hebben ook heen en weer geschakeld tussen Varnish en Nginx. We geven momenteel de voorkeur aan Varnish, maar het heeft wel zijn eigenaardigheden en ruwe kantjes. Hetzelfde geldt voor Checkmk: we zijn er niet dol op, maar het werkt voor nu. Weblate is oké geweest, maar niet geweldig — ik ben soms bang dat het mijn gegevens verliest wanneer ik probeer het te synchroniseren met onze git-repo. Flask is over het algemeen goed geweest, maar het heeft enkele vreemde eigenaardigheden die veel tijd hebben gekost om te debuggen, zoals het configureren van aangepaste domeinen, of problemen met de integratie van SqlAlchemy. Tot nu toe zijn de andere tools geweldig geweest: we hebben geen serieuze klachten over MariaDB, ElasticSearch, Gitlab, Zulip, Docker en Tor. Al deze hebben enkele problemen gehad, maar niets al te serieus of tijdrovend. Gemeenschap De eerste uitdaging kan een verrassende zijn. Het is geen technisch probleem, of een juridisch probleem. Het is een psychologisch probleem: dit werk in de schaduw doen kan ongelooflijk eenzaam zijn. Afhankelijk van wat je van plan bent te doen, en je dreigingsmodel, moet je misschien heel voorzichtig zijn. Aan de ene kant van het spectrum hebben we mensen zoals Alexandra Elbakyan*, de oprichter van Sci-Hub, die heel open is over haar activiteiten. Maar ze loopt een groot risico gearresteerd te worden als ze op dit moment een westers land zou bezoeken, en zou tientallen jaren gevangenisstraf kunnen krijgen. Is dat een risico dat je bereid bent te nemen? Wij bevinden ons aan de andere kant van het spectrum; we zijn heel voorzichtig om geen spoor achter te laten en hebben een sterke operationele beveiliging. * Zoals vermeld op HN door "ynno", wilde Alexandra aanvankelijk niet bekend zijn: "Haar servers waren ingesteld om gedetailleerde foutmeldingen van PHP uit te zenden, inclusief het volledige pad van het foutieve bronbestand, dat zich onder de directory /home/<USER>" Dus, gebruik willekeurige gebruikersnamen op de computers die je voor dit soort dingen gebruikt, voor het geval je iets verkeerd configureert. Die geheimhouding komt echter met een psychologische prijs. De meeste mensen houden ervan erkend te worden voor het werk dat ze doen, en toch kun je hier in het echte leven geen eer voor krijgen. Zelfs eenvoudige dingen kunnen uitdagend zijn, zoals vrienden die je vragen wat je hebt gedaan (op een gegeven moment wordt "rommelen met mijn NAS / homelab" oud). Daarom is het zo belangrijk om een gemeenschap te vinden. Je kunt wat operationele beveiliging opgeven door in vertrouwen te nemen bij enkele zeer goede vrienden, van wie je weet dat je ze diep kunt vertrouwen. Zelfs dan moet je oppassen om niets op schrift te stellen, voor het geval ze hun e-mails aan de autoriteiten moeten overhandigen, of als hun apparaten op een andere manier zijn gecompromitteerd. Beter nog is het om enkele mede-piraten te vinden. Als je goede vrienden geïnteresseerd zijn om mee te doen, geweldig! Anders kun je misschien anderen online vinden. Helaas is dit nog steeds een nichegemeenschap. Tot nu toe hebben we slechts een handvol anderen gevonden die actief zijn in deze ruimte. Goede startpunten lijken de Library Genesis-forums en r/DataHoarder te zijn. Het Archive Team heeft ook gelijkgestemde individuen, hoewel ze binnen de wet opereren (zelfs als dat in enkele grijze gebieden van de wet is). De traditionele "warez" en piraterijscènes hebben ook mensen die op vergelijkbare manieren denken. We staan open voor ideeën over hoe we gemeenschap kunnen bevorderen en ideeën kunnen verkennen. Voel je vrij om ons een bericht te sturen op Twitter of Reddit. Misschien kunnen we een soort forum of chatgroep hosten. Een uitdaging is dat dit gemakkelijk gecensureerd kan worden bij het gebruik van gangbare platforms, dus we zouden het zelf moeten hosten. Er is ook een afweging tussen het volledig openbaar maken van deze discussies (meer potentiële betrokkenheid) versus het privé maken (niet laten weten aan potentiële "doelen" dat we op het punt staan ze te scrapen). We zullen daarover moeten nadenken. Laat ons weten of je hierin geïnteresseerd bent! Conclusie Hopelijk is dit nuttig voor nieuw beginnende piratenarchivarissen. We zijn verheugd u in deze wereld te verwelkomen, dus aarzel niet om contact op te nemen. Laten we zoveel mogelijk van de kennis en cultuur van de wereld behouden en het wijd en zijd spiegelen. Projecten 4. Gegevensselectie Vaak kun je de metadata gebruiken om een redelijk deel van de gegevens te bepalen om te downloaden. Zelfs als je uiteindelijk alle gegevens wilt downloaden, kan het nuttig zijn om de belangrijkste items eerst te prioriteren, voor het geval je wordt gedetecteerd en de verdedigingen worden verbeterd, of omdat je meer schijven zou moeten kopen, of simpelweg omdat er iets anders in je leven opduikt voordat je alles kunt downloaden. Een collectie kan bijvoorbeeld meerdere edities van dezelfde onderliggende bron hebben (zoals een boek of een film), waarbij één is gemarkeerd als de beste kwaliteit. Het zou heel logisch zijn om die edities eerst op te slaan. Uiteindelijk wil je misschien alle edities opslaan, aangezien in sommige gevallen de metadata mogelijk onjuist is getagd, of er onbekende afwegingen zijn tussen edities (bijvoorbeeld, de "beste editie" kan in de meeste opzichten het beste zijn, maar in andere opzichten slechter, zoals een film met een hogere resolutie maar zonder ondertitels). Je kunt ook je metadata database doorzoeken om interessante dingen te vinden. Wat is het grootste bestand dat wordt gehost, en waarom is het zo groot? Wat is het kleinste bestand? Zijn er interessante of onverwachte patronen als het gaat om bepaalde categorieën, talen, enzovoort? Zijn er dubbele of zeer vergelijkbare titels? Zijn er patronen in wanneer gegevens zijn toegevoegd, zoals een dag waarop veel bestanden tegelijk zijn toegevoegd? Je kunt vaak veel leren door op verschillende manieren naar de dataset te kijken. In ons geval hebben we Z-Library boeken gededupliceerd tegen de md5 hashes in Library Genesis, waardoor we veel downloadtijd en schijfruimte hebben bespaard. Dit is echter een vrij unieke situatie. In de meeste gevallen zijn er geen uitgebreide databases van welke bestanden al goed bewaard zijn door mede-piraten. Dit is op zichzelf een enorme kans voor iemand daarbuiten. Het zou geweldig zijn om een regelmatig bijgewerkt overzicht te hebben van dingen zoals muziek en films die al veelvuldig worden gedeeld op torrent websites, en daarom een lagere prioriteit hebben om op te nemen in piraten mirrors. 6. Distributie Je hebt de gegevens, waardoor je waarschijnlijk de eerste piraten mirror van je doel in handen hebt. In veel opzichten is het moeilijkste deel voorbij, maar het risicovolste deel ligt nog voor je. Tot nu toe ben je immers onopgemerkt gebleven; onder de radar gevlogen. Alles wat je hoefde te doen was een goede VPN gebruiken, je persoonlijke gegevens niet invullen in formulieren (duh), en misschien een speciale browsersessie gebruiken (of zelfs een andere computer). Nu moet je de gegevens distribueren. In ons geval wilden we eerst de boeken terug bijdragen aan Library Genesis, maar ontdekten toen snel de moeilijkheden daarin (fictie versus non-fictie sortering). Dus besloten we tot distributie via Library Genesis-stijl torrents. Als je de kans hebt om bij te dragen aan een bestaand project, dan kan dat je veel tijd besparen. Er zijn echter momenteel niet veel goed georganiseerde piraten mirrors. Stel dat je besluit om zelf torrents te distribueren. Probeer die bestanden klein te houden, zodat ze gemakkelijk op andere websites kunnen worden gemirrord. Je zult dan zelf de torrents moeten seeden, terwijl je anoniem blijft. Je kunt een VPN gebruiken (met of zonder port forwarding), of betalen met gewassen Bitcoins voor een Seedbox. Als je niet weet wat sommige van die termen betekenen, heb je nog veel te lezen, want het is belangrijk dat je de risico-afwegingen hier begrijpt. Je kunt de torrentbestanden zelf hosten op bestaande torrent websites. In ons geval kozen we ervoor om daadwerkelijk een website te hosten, omdat we ook onze filosofie op een duidelijke manier wilden verspreiden. Je kunt dit zelf op een vergelijkbare manier doen (wij gebruiken Njalla voor onze domeinen en hosting, betaald met gewassen Bitcoins), maar voel je ook vrij om contact met ons op te nemen om ons je torrents te laten hosten. We willen in de loop van de tijd een uitgebreide index van piraten mirrors opbouwen, als dit idee aanslaat. Wat betreft de keuze van een VPN, er is al veel over geschreven, dus we herhalen gewoon het algemene advies om te kiezen op basis van reputatie. Werkelijk door de rechtbank geteste no-log beleid met lange staat van dienst in het beschermen van privacy is volgens ons de optie met het laagste risico. Merk op dat zelfs als je alles goed doet, je nooit tot nul risico kunt komen. Bijvoorbeeld, bij het seeden van je torrents kan een zeer gemotiveerde actor van een natiestaat waarschijnlijk kijken naar inkomende en uitgaande datastromen voor VPN-servers, en afleiden wie je bent. Of je kunt gewoon ergens een fout maken. Wij hebben dat waarschijnlijk al gedaan, en zullen het opnieuw doen. Gelukkig geven natiestaten niet <em>zoveel</em> om piraterij. Een beslissing die je voor elk project moet nemen, is of je het onder dezelfde identiteit als voorheen publiceert, of niet. Als je dezelfde naam blijft gebruiken, kunnen fouten in operationele beveiliging van eerdere projecten je achtervolgen. Maar publiceren onder verschillende namen betekent dat je geen langdurige reputatie opbouwt. Wij kozen ervoor om vanaf het begin sterke operationele beveiliging te hebben, zodat we dezelfde identiteit kunnen blijven gebruiken, maar we zullen niet aarzelen om onder een andere naam te publiceren als we een fout maken of als de omstandigheden daarom vragen. Het woord verspreiden kan lastig zijn. Zoals we al zeiden, is dit nog steeds een nichegemeenschap. We hebben oorspronkelijk op Reddit gepost, maar kregen echt tractie op Hacker News. Voor nu is onze aanbeveling om het op een paar plaatsen te posten en te zien wat er gebeurt. En nogmaals, neem contact met ons op. We zouden graag het woord verspreiden over meer piratenarchivisme-inspanningen. 1. Domeinselectie / filosofie Er is geen tekort aan kennis en cultureel erfgoed om te redden, wat overweldigend kan zijn. Daarom is het vaak nuttig om even de tijd te nemen en na te denken over wat jouw bijdrage kan zijn. Iedereen heeft een andere manier van denken hierover, maar hier zijn enkele vragen die je jezelf zou kunnen stellen: In ons geval gaven we vooral om de langdurige bewaring van wetenschap. We wisten van Library Genesis, en hoe het vele malen volledig werd gespiegeld met torrents. We hielden van dat idee. Toen probeerde een van ons op een dag enkele wetenschappelijke leerboeken te vinden op Library Genesis, maar kon ze niet vinden, wat twijfel zaaide over hoe compleet het echt was. We zochten die leerboeken toen online en vonden ze op andere plaatsen, wat de kiem legde voor ons project. Zelfs voordat we van de Z-Library wisten, hadden we het idee om niet te proberen al die boeken handmatig te verzamelen, maar ons te richten op het spiegelen van bestaande collecties en ze terug te geven aan Library Genesis. Welke vaardigheden heeft u die u in uw voordeel kunt gebruiken? Bijvoorbeeld, als u een online beveiligingsexpert bent, kunt u manieren vinden om IP-blokkades voor beveiligde doelen te omzeilen. Als u goed bent in het organiseren van gemeenschappen, dan kunt u misschien mensen samenbrengen rond een doel. Het is nuttig om enige programmeerkennis te hebben, al is het maar om goede operationele beveiliging te behouden gedurende dit proces. Wat zou een gebied met hoge impact zijn om op te focussen? Als u X uren gaat besteden aan piratenarchivering, hoe kunt u dan de grootste "waarde voor uw tijd" krijgen? Wat zijn unieke manieren waarop u hierover denkt? U heeft misschien interessante ideeën of benaderingen die anderen hebben gemist. Hoeveel tijd heeft u hiervoor? Ons advies zou zijn om klein te beginnen en grotere projecten te doen naarmate u er meer ervaring mee krijgt, maar het kan allesomvattend worden. Waarom bent u hierin geïnteresseerd? Waar bent u gepassioneerd over? Als we een groep mensen kunnen verzamelen die allemaal de soorten dingen archiveren waar ze specifiek om geven, zou dat veel dekken! U zult veel meer weten dan de gemiddelde persoon over uw passie, zoals welke belangrijke data bewaard moeten worden, wat de beste collecties en online gemeenschappen zijn, enzovoort. 3. Metadata-scraping Datum toegevoegd/aangepast: zodat je later kunt terugkomen en bestanden kunt downloaden die je eerder niet hebt gedownload (hoewel je hiervoor vaak ook de ID of hash kunt gebruiken). Hash (md5, sha1): om te bevestigen dat je het bestand correct hebt gedownload. ID: kan een interne ID zijn, maar ID's zoals ISBN of DOI zijn ook nuttig. Bestandsnaam / locatie Beschrijving, categorie, tags, auteurs, taal, enz. Grootte: om te berekenen hoeveel schijfruimte je nodig hebt. Laten we hier wat technischer worden. Voor het daadwerkelijk scrapen van de metadata van websites hebben we het vrij eenvoudig gehouden. We gebruiken Python-scripts, soms curl, en een MySQL-database om de resultaten in op te slaan. We hebben geen geavanceerde scraping-software gebruikt die complexe websites kan in kaart brengen, aangezien we tot nu toe alleen één of twee soorten pagina's hoefden te scrapen door simpelweg door id's te enumereren en de HTML te parseren. Als er geen gemakkelijk te enumereren pagina's zijn, heb je misschien een goede crawler nodig die probeert alle pagina's te vinden. Voordat je een hele website gaat scrapen, probeer het eerst handmatig een beetje. Ga zelf door een paar dozijn pagina's om een gevoel te krijgen voor hoe dat werkt. Soms kom je op deze manier al IP-blokkades of ander interessant gedrag tegen. Hetzelfde geldt voor datascraping: voordat je te diep in dit doelwit duikt, zorg ervoor dat je de data daadwerkelijk effectief kunt downloaden. Om beperkingen te omzeilen, zijn er een paar dingen die je kunt proberen. Zijn er andere IP-adressen of servers die dezelfde data hosten maar niet dezelfde beperkingen hebben? Zijn er API-eindpunten die geen beperkingen hebben, terwijl anderen dat wel hebben? Bij welke downloadsnelheid wordt je IP geblokkeerd, en hoe lang? Of word je niet geblokkeerd maar vertraagd? Wat als je een gebruikersaccount aanmaakt, hoe veranderen de zaken dan? Kun je HTTP/2 gebruiken om verbindingen open te houden, en verhoogt dat de snelheid waarmee je pagina's kunt aanvragen? Zijn er pagina's die meerdere bestanden tegelijk vermelden, en is de daar vermelde informatie voldoende? Dingen die je waarschijnlijk wilt opslaan zijn: We doen dit meestal in twee fasen. Eerst downloaden we de ruwe HTML-bestanden, meestal direct in MySQL (om veel kleine bestanden te vermijden, waar we hieronder meer over praten). Vervolgens, in een aparte stap, gaan we door die HTML-bestanden en parseren ze in daadwerkelijke MySQL-tabellen. Op deze manier hoef je niet alles opnieuw te downloaden als je een fout in je parseringscode ontdekt, omdat je de HTML-bestanden gewoon opnieuw kunt verwerken met de nieuwe code. Het is ook vaak gemakkelijker om de verwerkingsstap te paralleliseren, waardoor je wat tijd bespaart (en je kunt de verwerkingscode schrijven terwijl de scraping loopt, in plaats van beide stappen tegelijk te moeten schrijven). Ten slotte, merk op dat voor sommige doelen metadata scraping alles is wat er is. Er zijn enkele enorme metadata collecties die niet goed bewaard zijn gebleven. Titel Domeinselectie / filosofie: Waar wil je je ongeveer op richten, en waarom? Wat zijn je unieke passies, vaardigheden en omstandigheden die je in je voordeel kunt gebruiken? Doelselectie: Welke specifieke collectie ga je mirroren? Metadata scraping: Informatie over de bestanden catalogiseren, zonder de (vaak veel grotere) bestanden zelf te downloaden. Gegevensselectie: Op basis van de metadata bepalen welke gegevens op dit moment het meest relevant zijn om te archiveren. Het kan alles zijn, maar vaak is er een redelijke manier om ruimte en bandbreedte te besparen. Gegevens scraping: De gegevens daadwerkelijk verkrijgen. Distributie: Het verpakken in torrents, het ergens aankondigen, mensen zover krijgen het te verspreiden. 5. Gegevens scraping Nu ben je klaar om de gegevens daadwerkelijk in bulk te downloaden. Zoals eerder vermeld, zou je op dit punt al handmatig een aantal bestanden hebben gedownload, om het gedrag en de beperkingen van het doel beter te begrijpen. Er zullen echter nog steeds verrassingen voor je in petto zijn zodra je daadwerkelijk veel bestanden tegelijk gaat downloaden. Ons advies hier is vooral om het simpel te houden. Begin gewoon met het downloaden van een aantal bestanden. Je kunt Python gebruiken, en dan uitbreiden naar meerdere threads. Maar soms is het zelfs eenvoudiger om Bash-bestanden direct vanuit de database te genereren, en vervolgens meerdere daarvan in meerdere terminalvensters uit te voeren om op te schalen. Een snelle technische truc die het vermelden waard is, is het gebruik van OUTFILE in MySQL, die je overal kunt schrijven als je "secure_file_priv" uitschakelt in mysqld.cnf (en zorg ervoor dat je ook AppArmor uitschakelt/overruled als je op Linux zit). We slaan de gegevens op eenvoudige harde schijven op. Begin met wat je hebt, en breid langzaam uit. Het kan overweldigend zijn om na te denken over het opslaan van honderden TB's aan gegevens. Als dat de situatie is waarin je je bevindt, zet dan eerst een goed deel uit, en vraag in je aankondiging om hulp bij het opslaan van de rest. Als je zelf meer harde schijven wilt aanschaffen, dan heeft r/DataHoarder enkele goede bronnen om goede deals te krijgen. Probeer je niet te veel zorgen te maken over geavanceerde bestandssystemen. Het is gemakkelijk om in het konijnenhol te vallen van het opzetten van dingen zoals ZFS. Een technisch detail om je bewust van te zijn, is dat veel bestandssystemen niet goed omgaan met veel bestanden. We hebben ontdekt dat een eenvoudige oplossing is om meerdere directories te maken, bijvoorbeeld voor verschillende ID-bereiken of hash-prefixen. Na het downloaden van de gegevens, zorg ervoor dat je de integriteit van de bestanden controleert met behulp van hashes in de metadata, indien beschikbaar. 2. Doelselectie Toegankelijk: gebruikt niet veel lagen van bescherming om te voorkomen dat je hun metadata en data kunt scrapen. Speciale inzichten: je hebt speciale informatie over dit doelwit, zoals speciale toegang tot deze collectie, of je hebt ontdekt hoe je hun verdedigingen kunt omzeilen. Dit is niet vereist (ons aankomende project doet niets bijzonders), maar het helpt zeker! Groot Dus, we hebben ons gebied dat we bekijken, welke specifieke collectie spiegelen we nu? Er zijn een paar dingen die een goed doelwit maken: Toen we onze wetenschappelijke leerboeken op andere websites dan Anna’s Archief vonden, probeerden we te achterhalen hoe ze op het internet terechtkwamen. We ontdekten toen de Z-Library en realiseerden ons dat, hoewel de meeste boeken daar niet als eerste verschijnen, ze er uiteindelijk wel terechtkomen. We leerden over de relatie met Anna’s Archief en de (financiële) prikkelstructuur en superieure gebruikersinterface, die het tot een veel completere collectie maakten. We deden vervolgens wat voorlopige metadata- en datascraping en realiseerden ons dat we hun IP-downloadlimieten konden omzeilen door gebruik te maken van de speciale toegang van een van onze leden tot veel proxyservers. Terwijl je verschillende doelen verkent, is het al belangrijk om je sporen te verbergen door VPN's en wegwerp-e-mailadressen te gebruiken, waar we later meer over zullen praten. Uniek: niet al goed gedekt door andere projecten. Wanneer we een project doen, heeft het een paar fasen: Dit zijn niet volledig onafhankelijke fasen, en vaak sturen inzichten uit een latere fase je terug naar een eerdere fase. Bijvoorbeeld, tijdens metadata scraping kun je je realiseren dat het doel dat je hebt geselecteerd verdedigingsmechanismen heeft die je vaardigheidsniveau te boven gaan (zoals IP-blokkades), dus ga je terug en zoek je een ander doel. - Anna en het team (<a %(reddit)s>Reddit</a>) Er kunnen hele boeken worden geschreven over het <em>waarom</em> van digitale bewaring in het algemeen, en piratenarchivisme in het bijzonder, maar laten we een korte inleiding geven voor degenen die er niet zo bekend mee zijn. De wereld produceert meer kennis en cultuur dan ooit tevoren, maar er gaat ook meer verloren dan ooit tevoren. De mensheid vertrouwt grotendeels op bedrijven zoals academische uitgevers, streamingdiensten en sociale mediabedrijven voor dit erfgoed, en zij hebben zich vaak niet bewezen als geweldige beheerders. Bekijk de documentaire Digital Amnesia, of echt elke lezing van Jason Scott. Er zijn enkele instellingen die goed werk leveren door zoveel mogelijk te archiveren, maar zij zijn gebonden aan de wet. Als piraten bevinden we ons in een unieke positie om collecties te archiveren die zij niet kunnen aanraken, vanwege handhaving van auteursrechten of andere beperkingen. We kunnen ook collecties vele malen over de hele wereld spiegelen, waardoor de kans op juiste bewaring toeneemt. Voor nu gaan we niet in op de voor- en nadelen van intellectueel eigendom, de moraliteit van het overtreden van de wet, overpeinzingen over censuur, of de kwestie van toegang tot kennis en cultuur. Met dat allemaal uit de weg, laten we duiken in de <em>hoe</em>. We zullen delen hoe ons team piratenarchivarissen werd, en de lessen die we onderweg hebben geleerd. Er zijn veel uitdagingen wanneer je aan deze reis begint, en hopelijk kunnen we je door enkele daarvan helpen. Hoe word je een piratenarchivaris De eerste uitdaging kan een verrassende zijn. Het is geen technisch probleem, of een juridisch probleem. Het is een psychologisch probleem. Voordat we beginnen, twee updates over de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>): We hebben enkele uiterst genereuze donaties ontvangen. De eerste was $10k van de anonieme persoon die ook "bookwarrior", de oorspronkelijke oprichter van Library Genesis, heeft gesteund. Speciale dank aan bookwarrior voor het faciliteren van deze donatie. De tweede was nog eens $10k van een anonieme donor, die contact met ons opnam na onze laatste release en geïnspireerd was om te helpen. We hebben ook een aantal kleinere donaties ontvangen. Heel erg bedankt voor al jullie genereuze steun. We hebben enkele spannende nieuwe projecten in de pijplijn die hierdoor ondersteund zullen worden, dus blijf op de hoogte. We hadden enkele technische problemen met de grootte van onze tweede release, maar onze torrents zijn nu online en worden geseed. We kregen ook een genereus aanbod van een anonieme persoon om onze collectie te seeden op hun zeer snelle servers, dus we doen een speciale upload naar hun machines, waarna iedereen die de collectie downloadt een grote verbetering in snelheid zou moeten zien. Blogberichten Hallo, ik ben Anna. Ik heb <a %(wikipedia_annas_archive)s>Anna’s Archief</a> gecreëerd, 's werelds grootste schaduw bibliotheek. Dit is mijn persoonlijke blog, waarin ik en mijn teamgenoten schrijven over piraterij, digitale bewaring en meer. Verbind met mij op <a %(reddit)s>Reddit</a>. Let op dat deze website slechts een blog is. We hosten hier alleen onze eigen woorden. Er worden hier geen torrents of andere auteursrechtelijk beschermde bestanden gehost of gelinkt. <strong>Bibliotheek</strong> - Net als de meeste bibliotheken richten we ons voornamelijk op geschreven materialen zoals boeken. We kunnen in de toekomst uitbreiden naar andere soorten media. <strong>Spiegel</strong> - We zijn strikt een spiegel van bestaande bibliotheken. We richten ons op behoud, niet op het gemakkelijk doorzoekbaar en downloadbaar maken van boeken (toegang) of het bevorderen van een grote gemeenschap van mensen die nieuwe boeken bijdragen (bron). <strong>Piraat</strong> - We overtreden opzettelijk de auteurswet in de meeste landen. Dit stelt ons in staat iets te doen wat legale entiteiten niet kunnen: ervoor zorgen dat boeken wijd en zijd worden gespiegeld. <em>We linken niet naar de bestanden vanaf deze blog. Zoek het zelf.</em> - Anna en het team (<a %(reddit)s>Reddit</a>) Dit project (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>) heeft als doel bij te dragen aan het behoud en de bevrijding van menselijke kennis. We leveren onze kleine en bescheiden bijdrage, in de voetsporen van de groten die ons voorgingen. De focus van dit project wordt geïllustreerd door zijn naam: De eerste bibliotheek die we hebben gespiegeld is Z-Library. Dit is een populaire (en illegale) bibliotheek. Ze hebben de Library Genesis-collectie genomen en deze gemakkelijk doorzoekbaar gemaakt. Bovendien zijn ze zeer effectief geworden in het werven van nieuwe boekbijdragen, door bijdragende gebruikers te belonen met verschillende voordelen. Ze dragen deze nieuwe boeken momenteel niet terug bij aan Library Genesis. En in tegenstelling tot Library Genesis maken ze hun collectie niet gemakkelijk spiegelbaar, wat wijdverspreid behoud verhindert. Dit is belangrijk voor hun bedrijfsmodel, aangezien ze geld vragen voor toegang tot hun collectie in bulk (meer dan 10 boeken per dag). We vellen geen moreel oordeel over het vragen van geld voor bulktoegang tot een illegale boekencollectie. Het staat buiten kijf dat de Z-Library succesvol is geweest in het vergroten van de toegang tot kennis en het verkrijgen van meer boeken. Wij zijn hier simpelweg om ons deel te doen: het waarborgen van de langetermijnbewaring van deze privécollectie. We willen u uitnodigen om te helpen bij het behouden en bevrijden van menselijke kennis door onze torrents te downloaden en te seeden. Zie de projectpagina voor meer informatie over hoe de data is georganiseerd. We nodigen u ook van harte uit om uw ideeën bij te dragen over welke collecties we als volgende moeten spiegelen en hoe we dat moeten aanpakken. Samen kunnen we veel bereiken. Dit is slechts een kleine bijdrage te midden van talloze anderen. Dank u, voor alles wat u doet. Introductie van de Piratenbibliotheekspiegel: Behoud van 7TB aan boeken (die niet in Libgen staan) 10% of het geschreven erfgoed van de mensheid voor altijd behouden <strong>Google.</strong> Ze hebben tenslotte dit onderzoek gedaan voor Google Books. Hun metadata is echter niet in bulk toegankelijk en vrij moeilijk te scrapen. <strong>Verschillende individuele bibliotheeksystemen en archieven.</strong> Er zijn bibliotheken en archieven die niet zijn geïndexeerd en geaggregeerd door een van de bovenstaande, vaak omdat ze ondergefinancierd zijn, of om andere redenen hun gegevens niet willen delen met Open Library, OCLC, Google, enzovoort. Veel van deze hebben digitale records die toegankelijk zijn via het internet, en ze zijn vaak niet erg goed beschermd, dus als je wilt helpen en wat plezier wilt hebben met het leren over vreemde bibliotheeksystemen, zijn dit geweldige startpunten. <strong>ISBNdb.</strong> Dit is het onderwerp van deze blogpost. ISBNdb scrapt verschillende websites voor boekmetadata, met name prijsgegevens, die ze vervolgens verkopen aan boekverkopers, zodat ze hun boeken kunnen prijzen in overeenstemming met de rest van de markt. Aangezien ISBN's tegenwoordig vrij universeel zijn, hebben ze effectief een “webpagina voor elk boek” gebouwd. <strong>Open Library.</strong> Zoals eerder vermeld, is dit hun hele missie. Ze hebben enorme hoeveelheden bibliotheekgegevens verzameld van samenwerkende bibliotheken en nationale archieven, en blijven dit doen. Ze hebben ook vrijwillige bibliothecarissen en een technisch team dat probeert records te dedupliceren en ze te taggen met allerlei metadata. Het beste van alles is dat hun dataset volledig open is. Je kunt het eenvoudig <a %(openlibrary)s>downloaden</a>. <strong>WorldCat.</strong> Dit is een website beheerd door de non-profit OCLC, die bibliotheekbeheersystemen verkoopt. Ze verzamelen boekmetadata van veel bibliotheken en maken het beschikbaar via de WorldCat-website. Ze verdienen echter ook geld met de verkoop van deze gegevens, dus het is niet beschikbaar voor bulkdownload. Ze hebben wel enkele meer beperkte bulkdatasets beschikbaar voor download, in samenwerking met specifieke bibliotheken. 1. Voor een redelijke definitie van "voor altijd". ;) 2. Natuurlijk is het geschreven erfgoed van de mensheid veel meer dan boeken, vooral tegenwoordig. Voor de doeleinden van deze post en onze recente releases richten we ons op boeken, maar onze interesses reiken verder. 3. Er kan veel meer gezegd worden over Aaron Swartz, maar we wilden hem slechts kort noemen, aangezien hij een cruciale rol speelt in dit verhaal. Naarmate de tijd verstrijkt, kunnen meer mensen zijn naam voor het eerst tegenkomen en vervolgens zelf in het konijnenhol duiken. <strong>Fysieke exemplaren.</strong> Uiteraard is dit niet erg nuttig, aangezien het slechts duplicaten van hetzelfde materiaal zijn. Het zou geweldig zijn als we alle aantekeningen die mensen in boeken maken, zoals Fermats beroemde “krabbels in de marges”, konden bewaren. Maar helaas, dat blijft een droom van een archivaris. <strong>“Edities”.</strong> Hier tel je elke unieke versie van een boek. Als er iets anders aan is, zoals een andere omslag of een ander voorwoord, telt het als een andere editie. <strong>Bestanden.</strong> Bij het werken met schaduw bibliotheken zoals Library Genesis, Sci-Hub of Z-Library, is er een extra overweging. Er kunnen meerdere scans van dezelfde editie zijn. En mensen kunnen betere versies van bestaande bestanden maken door de tekst te scannen met OCR, of pagina's die onder een hoek zijn gescand te corrigeren. We willen deze bestanden slechts als één editie tellen, wat goede metadata vereist, of deduplicatie met behulp van documentvergelijkingsmaatregelen. <strong>“Werken”.</strong> Bijvoorbeeld “Harry Potter en de Geheime Kamer” als een logisch concept, dat alle versies ervan omvat, zoals verschillende vertalingen en herdrukken. Dit is een soort nuttige definitie, maar het kan moeilijk zijn om de grens te trekken van wat telt. We willen bijvoorbeeld waarschijnlijk verschillende vertalingen bewaren, hoewel herdrukken met slechts kleine verschillen misschien minder belangrijk zijn. - Anna en het team (<a %(reddit)s>Reddit</a>) Met de Piratenbibliotheekspiegel (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>), is ons doel om alle boeken in de wereld te nemen en ze voor altijd te behouden.<sup>1</sup> Tussen onze Z-Library torrents en de originele Library Genesis torrents hebben we 11.783.153 bestanden. Maar hoeveel is dat echt? Als we die bestanden goed zouden dedupliceren, welk percentage van alle boeken in de wereld hebben we dan behouden? We zouden echt graag zoiets willen hebben: Laten we beginnen met enkele ruwe cijfers: In zowel Z-Library/Libgen als Open Library zijn er veel meer boeken dan unieke ISBN's. Betekent dit dat veel van die boeken geen ISBN's hebben, of ontbreekt de ISBN-metadata gewoon? We kunnen deze vraag waarschijnlijk beantwoorden met een combinatie van geautomatiseerde matching op basis van andere attributen (titel, auteur, uitgever, enz.), het binnenhalen van meer gegevensbronnen, en het extraheren van ISBN's uit de daadwerkelijke boekscans zelf (in het geval van Z-Library/Libgen). Hoeveel van die ISBN's zijn uniek? Dit wordt het beste geïllustreerd met een Venn-diagram: Om preciezer te zijn: We waren verrast door hoe weinig overlap er is! ISBNdb heeft een enorme hoeveelheid ISBN's die niet voorkomen in Z-Library of Open Library, en hetzelfde geldt (in mindere maar nog steeds substantiële mate) voor de andere twee. Dit roept veel nieuwe vragen op. Hoeveel zou geautomatiseerde matching helpen bij het taggen van de boeken die niet met ISBN's waren getagd? Zou er veel overeenkomsten zijn en daardoor meer overlap? Ook, wat zou er gebeuren als we een 4e of 5e dataset toevoegen? Hoeveel overlap zouden we dan zien? Dit geeft ons een startpunt. We kunnen nu kijken naar alle ISBN's die niet in de Z-Library dataset stonden en die ook niet overeenkomen met titel/auteur velden. Dat kan ons helpen om alle boeken in de wereld te behouden: eerst door het internet af te speuren naar scans, en vervolgens door in het echte leven boeken te scannen. Het laatste kan zelfs door crowdfunding worden gefinancierd, of gedreven door "beloningen" van mensen die bepaalde boeken gedigitaliseerd willen zien. Dat is allemaal een verhaal voor een andere keer. Als u wilt helpen met een van deze taken — verdere analyse; meer metadata verzamelen; meer boeken vinden; boeken OCR'en; dit doen voor andere domeinen (bijv. papers, audioboeken, films, tv-shows, tijdschriften) of zelfs een deel van deze data beschikbaar maken voor zaken zoals ML / grote taalmodeltraining — neem dan contact met mij op (<a %(reddit)s>Reddit</a>). Als u specifiek geïnteresseerd bent in de data-analyse, werken we eraan om onze datasets en scripts beschikbaar te maken in een gebruiksvriendelijker formaat. Het zou geweldig zijn als u gewoon een notebook kunt forken en hiermee kunt beginnen te spelen. Ten slotte, als u dit werk wilt ondersteunen, overweeg dan een donatie te doen. Dit is een volledig door vrijwilligers gerunde operatie, en uw bijdrage maakt een groot verschil. Elke bijdrage helpt. Voor nu accepteren we donaties in crypto; zie de Doneer-pagina op Anna’s Archief. Voor een percentage hebben we een noemer nodig: het totale aantal boeken dat ooit is gepubliceerd.<sup>2</sup> Voor de ondergang van Google Books probeerde een ingenieur van het project, Leonid Taycher, <a %(booksearch_blogspot)s>dit aantal te schatten</a>. Hij kwam — met een knipoog — uit op 129.864.880 (“tenminste tot zondag”). Hij schatte dit aantal door een verenigde database van alle boeken in de wereld te bouwen. Hiervoor verzamelde hij verschillende datasets en voegde deze op verschillende manieren samen. Als een korte terzijde is er nog een persoon die probeerde alle boeken in de wereld te catalogiseren: Aaron Swartz, de overleden digitale activist en mede-oprichter van Reddit.<sup>3</sup> Hij <a %(youtube)s>startte Open Library</a> met als doel “één webpagina voor elk boek dat ooit is gepubliceerd”, waarbij hij gegevens uit veel verschillende bronnen combineerde. Hij betaalde uiteindelijk de hoogste prijs voor zijn digitale behoudswerk toen hij werd vervolgd voor het bulk-downloaden van academische papers, wat leidde tot zijn zelfmoord. Het is overbodig te zeggen dat dit een van de redenen is waarom onze groep pseudoniem is, en waarom we zeer voorzichtig zijn. Open Library wordt nog steeds heroïsch gerund door mensen bij het Internet Archive, die Aaron’s nalatenschap voortzetten. We komen hier later in deze post op terug. In de Google-blogpost beschrijft Taycher enkele van de uitdagingen bij het schatten van dit aantal. Ten eerste, wat is een boek? Er zijn een paar mogelijke definities: “Edities” lijken de meest praktische definitie van wat “boeken” zijn. Handig genoeg wordt deze definitie ook gebruikt voor het toekennen van unieke ISBN-nummers. Een ISBN, of Internationaal Standaard Boeknummer, wordt vaak gebruikt voor internationale handel, omdat het is geïntegreerd met het internationale barcodesysteem (”Internationaal Artikelnummer”). Als je een boek in winkels wilt verkopen, heeft het een barcode nodig, dus krijg je een ISBN. Taychers blogpost vermeldt dat hoewel ISBN's nuttig zijn, ze niet universeel zijn, aangezien ze pas echt werden aangenomen in het midden van de jaren zeventig, en niet overal ter wereld. Toch is ISBN waarschijnlijk de meest gebruikte identificator van boekedities, dus het is ons beste startpunt. Als we alle ISBN's in de wereld kunnen vinden, krijgen we een nuttige lijst van welke boeken nog bewaard moeten worden. Dus, waar halen we de gegevens vandaan? Er zijn een aantal bestaande inspanningen die proberen een lijst van alle boeken ter wereld samen te stellen: In deze post kondigen we met plezier een kleine release aan (vergeleken met onze eerdere Z-Library releases). We hebben het grootste deel van ISBNdb gescraped en de gegevens beschikbaar gemaakt voor torrenting op de website van de Pirate Library Mirror (EDIT: verplaatst naar <a %(wikipedia_annas_archive)s>Anna’s Archief</a>; we zullen het hier niet direct linken, zoek er gewoon naar). Dit zijn ongeveer 30,9 miljoen records (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gecomprimeerd). Op hun website beweren ze dat ze eigenlijk 32,6 miljoen records hebben, dus we hebben misschien op de een of andere manier iets gemist, of <em>zij</em> kunnen iets verkeerd doen. In ieder geval zullen we voorlopig niet precies delen hoe we het hebben gedaan — we laten dat als een oefening voor de lezer. ;-) Wat we wel zullen delen is een voorlopige analyse, om te proberen dichter bij het schatten van het aantal boeken in de wereld te komen. We hebben naar drie datasets gekeken: deze nieuwe ISBNdb-dataset, onze oorspronkelijke release van metadata die we hebben gescraped van de Z-Library schaduw bibliotheek (die Library Genesis omvat), en de Open Library data dump. ISBNdb dump, of Hoeveel Boeken Worden Voor Altijd Behouden? Als we de bestanden van schaduwbibliotheken goed zouden dedupliceren, welk percentage van alle boeken in de wereld hebben we dan behouden? Updates over <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, de grootste echt open bibliotheek in de menselijke geschiedenis. <em>WorldCat-herontwerp</em> Data <strong>Formaat?</strong> <a %(blog)s>Anna’s Archief Containers (AAC)</a>, wat in wezen <a %(jsonlines)s>JSON Lines</a> is gecomprimeerd met <a %(zstd)s>Zstandard</a>, plus enkele gestandaardiseerde semantieken. Deze containers omvatten verschillende soorten records, gebaseerd op de verschillende scrapes die we hebben uitgevoerd. Een jaar geleden <a %(blog)s>begonnen</a> we met het beantwoorden van deze vraag: <strong>Welk percentage van de boeken is permanent bewaard door schaduw bibliotheken?</strong> Laten we eens kijken naar enkele basisgegevens over de data: Zodra een boek in een open-data schaduw bibliotheek zoals <a %(wikipedia_library_genesis)s>Library Genesis</a> terechtkomt, en nu ook in <a %(wikipedia_annas_archive)s>Anna’s Archief</a>, wordt het wereldwijd gespiegeld (via torrents), waardoor het praktisch voor altijd bewaard blijft. Om de vraag te beantwoorden welk percentage van de boeken is bewaard, moeten we de noemer weten: hoeveel boeken bestaan er in totaal? En idealiter hebben we niet alleen een getal, maar ook daadwerkelijke metadata. Dan kunnen we ze niet alleen vergelijken met schaduw bibliotheken, maar ook <strong>een TODO-lijst maken van de resterende boeken die bewaard moeten worden!</strong> We zouden zelfs kunnen dromen van een crowdsourced inspanning om deze TODO-lijst af te werken. We hebben <a %(wikipedia_isbndb_com)s>ISBNdb</a> gescraped en de <a %(openlibrary)s>Open Library dataset</a> gedownload, maar de resultaten waren onbevredigend. Het grootste probleem was dat er niet veel overlap was van ISBN's. Zie dit Venn-diagram uit <a %(blog)s>onze blogpost</a>: We waren erg verrast door hoe weinig overlap er was tussen ISBNdb en Open Library, die beide vrijelijk gegevens uit verschillende bronnen opnemen, zoals webscrapes en bibliotheekrecords. Als ze allebei goed werk zouden leveren bij het vinden van de meeste ISBN's die er zijn, zouden hun cirkels zeker aanzienlijke overlap hebben, of zou de een een subset van de ander zijn. Het deed ons afvragen, hoeveel boeken vallen <em>volledig buiten deze cirkels</em>? We hebben een grotere database nodig. Dat is wanneer we onze zinnen zetten op de grootste boeken database ter wereld: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is een eigendomsdatabase van de non-profit <a %(wikipedia_oclc)s>OCLC</a>, die metadatagegevens van bibliotheken over de hele wereld verzamelt, in ruil voor het geven van toegang aan die bibliotheken tot de volledige dataset, en ervoor zorgt dat ze verschijnen in de zoekresultaten van eindgebruikers. Hoewel OCLC een non-profit is, vereist hun bedrijfsmodel dat ze hun database beschermen. Nou, het spijt ons te zeggen, vrienden bij OCLC, we geven het allemaal weg. :-) Het afgelopen jaar hebben we alle WorldCat-records nauwgezet gescraped. In het begin hadden we een gelukstreffer. WorldCat was net bezig met de uitrol van hun complete website-herontwerp (in augustus 2022). Dit omvatte een substantiële revisie van hun backend-systemen, waarbij veel beveiligingsfouten werden geïntroduceerd. We grepen onmiddellijk de kans en konden honderden miljoenen (!) records in slechts enkele dagen scrapen. Daarna werden de beveiligingsfouten langzaam één voor één opgelost, totdat de laatste die we vonden ongeveer een maand geleden werd gepatcht. Tegen die tijd hadden we vrijwel alle records en gingen we alleen nog voor iets hogere kwaliteit records. Dus we vonden dat het tijd was om te publiceren! 1,3B WorldCat scrape <em><strong>TL;DR:</strong> Anna’s Archief heeft heel WorldCat (de grootste bibliotheek metadatacollectie ter wereld) gescraped om een TODO-lijst te maken van boeken die bewaard moeten worden.</em> WorldCat Waarschuwing: deze blogpost is verouderd. We hebben besloten dat IPFS nog niet klaar is voor prime time. We zullen nog steeds linken naar bestanden op IPFS vanuit Anna’s Archief wanneer mogelijk, maar we zullen het niet langer zelf hosten, noch raden we anderen aan om te spiegelen met IPFS. Bekijk onze Torrents-pagina als u wilt helpen onze collectie te behouden. Help Z-Library op IPFS te seeden Download van partnerserver SciDB Extern lenen Extern lenen (leesbeperkingen) Externe download Metadata verkennen In torrents Terug  (+%(num)s bonus) Onbetaald Betaald Geannuleerd Verlopen Wachten op Anna ter bevestiging ongeldig Onderstaande tekst is alleen in het Engels beschikbaar. Ga Reset Vooruit Laatste Als je e-mailadres niet op de Libgen-fora werkt, raden we je aan om <a %(a_mail)s>Proton Mail</a> (gratis) te gebruiken. Je kunt ook <a %(a_manual)s>handmatig vragen</a> of je account kan worden geactiveerd. (<a %(a_browser)s>browserverificatie</a> mogelijk vereist — onbeperkte downloads!) Snelle partnerserver #%(number)s (aanbevolen) (iets sneller maar met wachtlijst) (geen browserverificatie vereist) (geen browser verificatie of wachtlijsten) (geen wachtlijst, maar kan erg traag zijn) Langzame partnerserver #%(number)s Audioboek Stripboek Boek (fictie) Boek (non-fictie) Boek (onbekend) Wetenschappelijk artikel Tijdschrift Partituur Overig Documentatievormen Niet alle pagina's konden worden omgezet naar PDF Gemarkeerd als beschadigd bestand in Libgen.li Niet zichtbaar in Libgen.li Niet zichtbaar in Libgen.rs Fictie Niet zichtbaar in Libgen.rs Non-Fictie Het uitvoeren van exiftool op dit bestand is mislukt Gemarkeerd als “slecht bestand” in Z-Library Niet aanwezig in Z-Library Gemarkeerd als “spam” in Z-Library Bestand kan niet worden geopend (zoals beschadigd bestand, DRM) Auteursrechtclaim Downloadproblemen (zoals verbindingsproblemen, foutmeldingen, traagheid) Onjuiste metadata (zoals titel, beschrijving, omslagafbeelding) Overig Slechte kwaliteit (zoals formatteringsproblemen, slechte scans, ontbrekende pagina's) Spam/bestand moet worden verwijderd (zoals advertenties, schadelijke inhoud) %(amount)s (%(amount_usd)s) %(amount)s totaal %(amount)s (%(amount_usd)s) totaal Briljante boekenwurm Blije bibliothecaris Grandioze gegevensverzamelaar Aanzienlijke archivaris Bonusdownloads Cerlalc Tsjechische metadata DuXiu 读秀 EBSCOhost eBook Index Google Boeken Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Exclusief “scimag” Libgen.rs Non-fictie en Fictie Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russische Staatsbibliotheek Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads naar AA Z-Library Z-Library Chinees Zoek op titel, auteur, taal, bestandstype, ISBN, MD5, … Zoeken Auteur Beschrijving en metadata-opmerkingen Editie Originele bestandsnaam Uitgever (specifiek veld doorzoeken) Titel Publicatiejaar Technische details Deze munt heeft een minimum dat hoger is dan gebruikelijk. Selecteer een andere duur of munt. Aanvraag kon niet worden voltooid. Probeer het over een paar minuten opnieuw en als het blijft gebeuren, neem dan contact met ons op via %(email)s met een schermafbeelding. Er is een onbekende fout opgetreden. Neem contact met ons op via %(email)s met een screenshot. Fout bij het verwerken van de betaling. Wacht even en probeer het daarna opnieuw. Als het probleem meer dan 24 uur aanhoudt, neem dan contact met ons op via %(email)s met een screenshot. We houden een fondsenwerving om <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">een kopie</a> van 's werelds grootste schaduwbibliotheek voor stripboeken te maken. Bedankt voor je steun! <a href="/donate">Doneren.</a> En als je niet kunt doneren, probeer ons dan te steunen door je vrienden over ons te vertellen en ons te volgen op <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> of <a href="https://t.me/annasarchiveorg">Telegram</a>. Mail ons niet voor <a %(a_request)s>boekaanvragen</a><br>of kleine (<10k) <a %(a_upload)s>uploads</a>. Anna’s Archief DMCA-/auteursrechtclaims Blijf in contact Reddit Alternatieven SLUM (%(unaffiliated)s) niet aangesloten Anna’s Archive heeft jouw hulp nodig! Als je nu doneert, krijg je <strong>dubbel</strong> zoveel snelle downloads. Velen proberen ons neer te halen, maar wij vechten terug. Als je deze maand doneert, krijg je <strong>dubbel</strong> zoveel snelle downloads. Geldig tot het einde van deze maand. Het redden van menselijke kennis: een geweldig cadeau voor de feestdagen! Lidmaatschappen worden dienovereenkomstig verlengd. Partnerservers zijn niet beschikbaar vanwege sluitingen van hosting. Ze zouden snel weer online moeten zijn. Om de veerkracht van Anna's Archief te vergroten, zijn we op zoek naar vrijwilligers die 'mirrors' kunnen draaien. We hebben een nieuwe donatiemogelijkheid: %(method_name)s. Overweeg alsjeblieft te %(donate_link_open_tag)sdoneren</a> — het is niet goedkoop om deze website te draaien en je donatie maakt hierbij echt het verschil. Heel erg bedankt. Als je een vriend doorverwijst, krijgen jullie beiden %(percentage)s%% snelle bonusdownloads! Verras een dierbare, geef hem of haar een account met lidmaatschap. Het perfecte valentijnscadeau! Meer informatie… Account Activiteit Geavanceerd Anna’s Blog ↗ Anna’s Software ↗ bèta Code Verkenner Datasets Doneren Gedownloade bestanden FAQ Startpagina Metadata verbeteren LLM-data Inloggen/registreren Mijn donaties Openbaar profiel Zoeken Beveiliging Torrents Vertalen ↗ Vrijwilligerswerk & Premies Recente downloads: 📚&nbsp;'s Werelds grootste opensourcebibliotheek met open data. ⭐️&nbsp;Een spiegeling van Sci-Hub, Library Genesis, Z-Library en meer. 📈&nbsp;%(book_any)s boeken, %(journal_article)s papers, %(book_comic)s stripboeken, %(magazine)s tijdschriften – voor altijd behouden.  en  en meer DuXiu Internet Archive LibGen 📚&nbsp;De grootste écht open bibliotheek in de geschiedenis van de mensheid. 📈&nbsp;%(book_count)s&nbsp;boeken, %(paper_count)s&nbsp;papers – voor altijd behouden. ⭐️&nbsp;We spiegelen %(libraries)s. We scrapen %(scraped)s en maken deze open source. Al onze code en data zijn volledig open source. OpenLib Sci-Hub ,  📚 's Werelds grootste opensourcebibliotheek met open data.<br>⭐️ Een spiegeling van Sci-Hub, Libgen, Zlib en meer. Z-Lib Anna’s Archief Ongeldig verzoek. Bezoek %(websites)s. 's Werelds grootste opensourcebibliotheek met open data. Een spiegeling van Sci-Hub, Library Genesis, Z-Library en meer. Anna’s Archief doorzoeken Anna’s Archief Ververs de pagina om het opnieuw te proberen. <a %(a_contact)s>Neem contact met ons op</a> als het probleem meerdere uren aanhoudt. 🔥 Probleem bij het laden van deze pagina <li>1. Volg ons op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> of <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Laat anderen weten over Anna’s Archief op Twitter, Reddit, TikTok, Instagram, bij je lokale café of bibliotheek of waar je ook gaat! We geloven niet in gatekeeping – als we offline worden gehaald, steken we ergens anders de kop op, omdat al onze broncode open source is.</li><li>3. Als het mogelijk is, overweeg dan om te <a href="/donate">doneren</a>.</li><li>4. Help onze website te <a href="https://translate.annas-software.org/">vertalen</a> in verschillende talen. </li><li>5. Als je een softwareontwikkelaar bent, overweeg dan bij te dragen aan onze <a href="https://annas-software.org/">open source</a> of onze <a href="/datasets">torrents</a> te seeden.</li> 10. Maak of help bij het onderhouden van de Wikipedia-pagina voor Anna’s Archief in jouw taal. 11. We zijn op zoek naar kleine, stijlvolle advertenties. Als je wilt adverteren op Anna's Archief, laat het ons dan weten. 6. Als je een beveiligingsonderzoeker bent, kunnen we je vaardigheden op zowel offensief als defensief vlak goed gebruiken. Bekijk daarvoor de pagina <a %(a_security)s>Beveiliging</a>. 7. We zijn op zoek naar betalingsexperts voor anonieme verkopers. Kan jij, of iemand die je kent, ons helpen bij het toevoegen van makkelijkere manieren om te doneren? Denk daarbij aan PayPal, WeChat en cadeaukaarten. Neem dan contact met ons op. 8. We zijn altijd op zoek naar meer servercapaciteit. 9. Je kunt ons helpen door problemen met bestanden te melden, opmerkingen achter te laten en lijsten op deze website samen te stellen. Je kunt ons ook helpen door <a %(a_upload)s>meer boeken te uploaden</a> of door problemen met of indelingen van bestaande boeken te verhelpen. Voor uitgebreidere informatie over hoe je vrijwilligerswerkt kunt doen, zie onze <a %(a_volunteering)s>Vrijwilligers & Premies</a> pagina. We geloven sterk in de vrije toegankelijkheid van informatie en behoud van kennis en cultuur. Met deze zoekmachine bouwen we voort op wat ons is nagelaten. We hebben heel veel respect voor het harde werk van mensen die verschillende schaduwbibliotheken hebben gemaakt en we hopen dat deze zoekmachine het bereik van deze bibliotheken vergroot. Volg Anna om op de hoogte te blijven van onze voortgang op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> of <a href="https://t.me/annasarchiveorg">Telegram</a>. Voor vragen of feedback kun je contact opnemen met Anna via %(email)s. Account-ID: %(account_id)s Uitloggen ❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw. ✅ Je bent nu uitgelogd. Herlaad de pagina om opnieuw in te loggen. Gebruikte snelle downloads (afgelopen 24 uur): <strong>%(used)s / %(total)s</strong> Lidmaatschap: <strong>%(tier_name)s</strong> tot %(until_date)s <a %(a_extend)s>(verlengen)</a> Je kunt meerdere lidmaatschappen combineren (het aantal snelle downloads per 24 uur wordt dan samengevoegd). Lidmaatschap: <strong>geen</strong> <a %(a_become)s>(lid worden)</a> Neem contact op met Anna via %(email)s als je je lidmaatschap naar een hoger niveau wilt upgraden. Openbaar profiel: %(profile_link)s Geheime sleutel (niet delen!): %(secret_key)s tonen Sluit je aan! Upgrade naar een <a %(a_tier)s>hoger niveau</a> om aan onze groep deel te nemen. Exclusieve Telegram-groep: %(link)s Account welke downloads? Inloggen Raak je sleutel niet kwijt! Ongeldige geheime sleutel. Controleer je sleutel en probeer het opnieuw of registreer hieronder een nieuw account. Geheime sleutel Voer je geheime sleutel in om in te loggen: Oud account met e-mailadres? Voer <a %(a_open)s>hier</a> je e-mail in. Nieuw account registreren Heb je nog geen account? Registratie gelukt! Je geheime sleutel is: <span %(span_key)s>%(key)s</span> Bewaar deze sleutel goed. Als je 'm kwijtraakt, heb je geen toegang meer tot je account. <li %(li_item)s><strong>Favorieten.</strong> Je kunt deze pagina aan je favorieten toevoegen om je sleutel op te halen.</li><li %(li_item)s><strong>Downloaden.</strong> Klik op <a %(a_download)s>deze link</a> om je sleutel te downloaden.</li><li %(li_item)s><strong>Wachtwoordmanager.</strong> Gebruik een wachtwoordmanager om de sleutel op te slaan wanneer je deze hieronder invoert.</li> Inloggen/registreren Browser verificatie Waarschuwing: code bevat onjuiste Unicode tekens en kan zich in verschillende situaties onjuist gedragen. De ruwe binaire gegevens kunnen worden gedecodeerd vanuit de base64 representatie in de URL. Beschrijving Label Prefix URL voor een specifieke code Website Codes die beginnen met “%(prefix_label)s” Gelieve deze pagina's niet te scrapen. In plaats daarvan raden we aan om onze ElasticSearch en MariaDB databases <a %(a_import)s>te genereren</a> of <a %(a_download)s>te downloaden</a>, en onze <a %(a_software)s>open source code</a> te draaien. De ruwe data kan handmatig worden verkend via JSON-bestanden zoals <a %(a_json_file)s>deze</a>. Minder dan %(count)s records Algemene URL Code Verkenner Index van Verken de codes waarmee records op prefix zijn getagd. De kolom “records” toont het aantal records dat is getagd met codes met de gegeven prefix, zoals te zien is in de zoekmachine (inclusief metadata-only records). De kolom “codes” toont hoeveel daadwerkelijke codes een gegeven prefix hebben. Bekende code prefix “%(key)s” Meer… Prefix %(count)s record die overeenkomt met “%(prefix_label)s” %(count)s records die overeenkomen met “%(prefix_label)s” codes records “%%s” zal worden vervangen door de waarde van de code Zoek in Anna’s Archief Codes URL voor specifieke code: “%(url)s” Deze pagina kan even nodig hebben om te genereren, daarom is een Cloudflare captcha vereist. <a %(a_donate)s>Leden</a> kunnen de captcha overslaan. Misbruik gemeld: Betere versie Wilt u deze gebruiker melden voor beledigend of ongepast gedrag? Bestandsprobleem: %(file_issue)s verborgen opmerking Antwoord Meld misbruik U heeft deze gebruiker gemeld voor misbruik. Auteursrechtclaims via deze e-mail worden genegeerd; gebruik in plaats daarvan het formulier. E-mail weergeven We kijken uit naar jullie feedback en vragen! Door de hoeveelheid spam en onzin die we per e-mail ontvangen, vragen we je echter wel om de vakjes aan te vinken om te bevestigen dat je deze voorwaarden voor contact met ons begrijpt. Berichten over auteursrechtclaims die op andere manieren worden ingediend, worden automatisch verwijderd. Gebruik <a %(a_copyright)s>dit formulier</a> voor DMCA-/auteursrechtclaims. Contactmail URL's op Anna’s Archive (vereist). Eén per regel. Voeg a.u.b. alleen URL's toe die exact dezelfde editie van een boek beschrijven. Als je een claim wil indienen voor meerdere boeken of meerdere edities, dien dit formulier dan meerdere keren in. Claims die meerdere boeken of edities bundelen, worden afgewezen. Adres (verplicht) Duidelijke beschrijving van het bronmateriaal (verplicht) E-mail (verplicht) URL's naar bronmateriaal, één per regel (verplicht). Gelieve er zoveel mogelijk bij te voegen om ons te helpen je claim te verifiëren (bijv. Amazon, WorldCat, Google Books, DOI). ISBN's van het bronmateriaal (indien van toepassing). Eén per regel. Gelieve alleen diegene bij te voegen die exact overeenkomen met de editie waarvoor je een auteursrechtclaim indient. Je naam (verplicht) ❌ Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw. ✅ Bedankt voor het indienen van je auteursrechtenclaim. We zullen deze zo snel mogelijk beoordelen. Gelieve de pagina te herladen om een nieuwe claim in te dienen. <a %(a_openlib)s>Open Library</a> URL's van het bronmateriaal, één per regel. Neem even de tijd om in de Open Library naar je bronmateriaal te zoeken. Dit zal ons helpen jouw claim te verifiëren. Telefoonnummer (verplicht) Verklaring en handtekening (verplicht) Claim indienen Als je een DCMA of andere auteursrechtclaim hebt, vul dit formulier dan zo nauwkeurig mogelijk in. Als je problemen ondervindt, neem dan contact met ons op via ons speciale DMCA-adres: %(email)s. Let op dat claims die naar dit adres worden gemaild niet worden verwerkt, het is alleen bedoeld voor vragen. Gebruik het onderstaande formulier om uw claims in te dienen. DMCA / Auteursrecht claimformulier Voorbeeldrecord op Anna’s Archive Torrents door Anna’s Archive Anna’s Archive Containers formaat Scripts voor het importeren van metadata Als u geïnteresseerd bent in het mirroren van deze dataset voor <a %(a_archival)s>archivering</a> of <a %(a_llm)s>LLM-training</a>, neem dan contact met ons op. Laatst bijgewerkt: %(date)s Hoofd %(source)s website Metadata documentatie (meeste velden) Bestanden gemirrored door Anna’s Archive: %(count)s (%(percent)s%%) Middelen Totaal aantal bestanden: %(count)s Totale bestandsgrootte: %(size)s Onze blogpost over deze data <a %(duxiu_link)s>Duxiu</a> is een enorme database van gescande boeken, gecreëerd door de <a %(superstar_link)s>SuperStar Digital Library Group</a>. De meeste boeken zijn academische boeken, gescand om ze digitaal beschikbaar te maken voor universiteiten en bibliotheken. Voor ons Engelssprekende publiek hebben <a %(princeton_link)s>Princeton</a> en de <a %(uw_link)s>University of Washington</a> goede overzichten. Er is ook een uitstekend artikel dat meer achtergrondinformatie geeft: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. De boeken van Duxiu zijn al lang gepirate op het Chinese internet. Meestal worden ze voor minder dan een dollar verkocht door resllers. Ze worden doorgaans verspreid via het Chinese equivalent van Google Drive, dat vaak is gehackt om meer opslagruimte mogelijk te maken. Enkele technische details zijn te vinden <a %(link1)s>hier</a> en <a %(link2)s>hier</a>. Hoewel de boeken semi-openbaar zijn verspreid, is het vrij moeilijk om ze in bulk te verkrijgen. We hadden dit hoog op onze TODO-lijst staan en hebben er meerdere maanden fulltime werk aan besteed. Echter, eind 2023 nam een ongelooflijke, geweldige en getalenteerde vrijwilliger contact met ons op en vertelde ons dat ze al dit werk al had gedaan — tegen grote kosten. Ze deelde de volledige collectie met ons, zonder iets terug te verwachten, behalve de garantie van langdurige bewaring. Echt opmerkelijk. Meer informatie van onze vrijwilligers (ruwe notities): Gebaseerd op onze <a %(a_href)s>blogpost</a>. DuXiu 读秀 %(count)s bestand %(count)s bestanden Deze dataset is nauw verwant aan de <a %(a_datasets_openlib)s>Open Library dataset</a>. Het bevat een scrape van alle metadata en een groot deel van de bestanden uit de IA’s Controlled Digital Lending Library. Updates worden uitgebracht in het <a %(a_aac)s>Anna’s Archive Containers-formaat</a>. Naar deze records wordt rechtstreeks verwezen vanuit de Open Library dataset, maar ze bevatten ook records die niet in de Open Library staan. We hebben ook een aantal databestanden die door communityleden door de jaren heen zijn gescraped. De collectie bestaat uit twee delen. Je hebt beide delen nodig om alle data te verkrijgen (behalve verouderde torrents, die zijn doorgestreept op de torrents-pagina). Digitale Uitleenbibliotheek onze eerste release, voordat we het <a %(a_aac)s>Anna’s Archive Containers (AAC) formaat</a> standaardiseerden. Bevat metadata (als json en xml), pdf's (van acsm en lcpdf digitale uitleensystemen), en cover thumbnails. incrementele nieuwe releases, met gebruik van AAC. Bevat alleen metadata met tijdstempels na 2023-01-01, aangezien de rest al is gedekt door “ia”. Ook alle pdf-bestanden, dit keer van de acsm en “bookreader” (IA’s webreader) uitleensystemen. Ondanks dat de naam niet helemaal klopt, voegen we nog steeds bookreader-bestanden toe aan de ia2_acsmpdf_files collectie, aangezien ze elkaar uitsluiten. IA Controlled Digital Lending 98%%+ van de bestanden zijn doorzoekbaar. Onze missie is om alle boeken ter wereld (evenals papers, tijdschriften, enz.) te archiveren en breed toegankelijk te maken. Wij geloven dat alle boeken wijd en zijd gemirrored moeten worden om redundantie en veerkracht te waarborgen. Daarom verzamelen we bestanden uit verschillende bronnen. Sommige bronnen zijn volledig open en kunnen in bulk worden gespiegeld (zoals Sci-Hub). Andere zijn gesloten en beschermend, dus proberen we ze te scrapen om hun boeken te “bevrijden”. Weer anderen vallen er ergens tussenin. Al onze data kan worden <a %(a_torrents)s>getorrent</a>, en al onze metadata kan <a %(a_anna_software)s>gegenereerd</a> of <a %(a_elasticsearch)s>gedownload</a> worden als ElasticSearch- en MariaDB-databases. De ruwe data kan handmatig worden verkend via JSON-bestanden zoals <a %(a_dbrecord)s>deze</a>. Metadata ISBN website Laatst bijgewerkt: %(isbn_country_date)s (%(link)s) Middelen Het Internationale ISBN Agentschap geeft regelmatig de reeksen vrij die het heeft toegewezen aan nationale ISBN-agentschappen. Hieruit kunnen we afleiden tot welk land, regio of taalgroep dit ISBN behoort. We gebruiken deze gegevens momenteel indirect, via de <a %(a_isbnlib)s>isbnlib</a> Python-bibliotheek. ISBN-landinformatie Dit is een dump van veel aanroepen naar isbndb.com in september 2022. We hebben geprobeerd alle ISBN-reeksen te dekken. Dit zijn ongeveer 30,9 miljoen records. Op hun website claimen ze dat ze eigenlijk 32,6 miljoen records hebben, dus we hebben misschien op de een of andere manier iets gemist, of <em>zij</em> kunnen iets verkeerd doen. De JSON-responses zijn vrijwel ruw van hun server. Een probleem met de data kwaliteit dat we hebben opgemerkt, is dat voor ISBN-13-nummers die beginnen met een andere prefix dan “978-”, ze nog steeds een “isbn”-veld opnemen dat simpelweg het ISBN-13-nummer is met de eerste 3 cijfers eraf gehakt (en het controlecijfer opnieuw berekend). Dit is duidelijk fout, maar dit is how ze het lijken te doen, dus hebben we het niet aangepast. Een ander potentieel probleem dat je kunt tegenkomen, is het feit dat het “isbn13”-veld duplicaten heeft, dus je kunt het niet gebruiken als primaire sleutel in een database. De velden “isbn13”+“isbn” lijken gecombineerd wel uniek te zijn. Release 1 (2022-10-31) Fictie torrents lopen achter (hoewel ID's ~4-6M niet getorrent zijn omdat ze overlappen met onze Zlib torrents). Onze blogpost over de stripboekenuitgave Strip-torrents op Anna’s Archive Voor de achtergrond van de verschillende Library Genesis forks, zie de pagina voor de <a %(a_libgen_rs)s>Libgen.rs</a>. De Libgen.li bevat de meeste van dezelfde inhoud en metadata als de Libgen.rs, maar heeft enkele collecties bovenop dit, namelijk strips, tijdschriften en standaarddocumenten. Het heeft ook <a %(a_scihub)s>Sci-Hub</a> geïntegreerd in zijn metadata en zoekmachine, wat wij gebruiken voor onze database. De metadata voor deze bibliotheek is vrij beschikbaar <a %(a_libgen_li)s>op libgen.li</a>. Deze server is echter traag en ondersteunt het hervatten van verbroken verbindingen niet. Dezelfde bestanden zijn ook beschikbaar op <a %(a_ftp)s>een FTP-server</a>, die beter werkt. Non-fictie lijkt ook te zijn afgedwaald, maar zonder nieuwe torrents. Het lijkt erop dat dit sinds begin 2022 is gebeurd, hoewel we dit niet hebben geverifieerd. Volgens de beheerder van Libgen.li zou de “fiction_rus” (Russische fictie) collectie gedekt moeten worden door regelmatig uitgebrachte torrents van <a %(a_booktracker)s>booktracker.org</a>, met name de <a %(a_flibusta)s>flibusta</a> en <a %(a_librusec)s>lib.rus.ec</a> torrents (die we <a %(a_torrents)s>hier</a> mirroren, hoewel we nog niet hebben vastgesteld welke torrents overeenkomen met welke bestanden). De fictiecollectie heeft zijn eigen torrents (afwijkend van <a %(a_href)s>Libgen.rs</a>) beginnend bij %(start)s. Bepaalde reeksen zonder torrents (zoals fictiereeksen f_3463000 tot f_4260000) zijn waarschijnlijk Z-Library (of andere dubbele) bestanden, hoewel we mogelijk wat deduplicatie willen doen en torrents willen maken voor lgli-unieke bestanden in deze reeksen. Statistieken voor alle collecties zijn te vinden <a %(a_href)s>op de website van libgen</a>. Torrents zijn beschikbaar voor de meeste extra inhoud, met name torrents voor strips, tijdschriften en standaarddocumenten zijn uitgebracht in samenwerking met Anna’s Archief. Let op dat de torrentbestanden die verwijzen naar “libgen.is” expliciet mirrors zijn van <a %(a_libgen)s>Libgen.rs</a> (“.is” is een ander domein dat door Libgen.rs wordt gebruikt). Een nuttige bron voor het gebruik van de metadata is <a %(a_href)s>deze pagina</a>. %(icon)s Hun “fiction_rus” collectie (Russische fictie) heeft geen speciale torrents, maar wordt gedekt door torrents van anderen, en we houden een <a %(fiction_rus)s>mirror</a>. Russische fictie torrents op Anna’s Archief Fictie-torrents op Anna’s Archive Discussieforum Metadata Metadata via FTP Tijdschrift-torrents op Anna’s Archive Metadata veld informatie Mirror van andere torrents (en unieke fictie- en strip-torrents) Standaard document torrents op Anna’s Archief Libgen.li Torrents door Anna’s Archive (boekomslagen) Library Genesis staat bekend om het genereus beschikbaar stellen van hun data in bulk via torrents. Onze Libgen-collectie bestaat uit aanvullende data die zij niet direct vrijgeven, in samenwerking met hen. Veel dank aan iedereen die betrokken is bij Library Genesis voor de samenwerking met ons! Onze blog over de release van de boekomslagen Deze pagina gaat over de “.rs” versie. Het staat bekend om het consequent publiceren van zowel zijn metadata als de volledige inhoud van zijn boekencatalogus. De boekencollectie is opgesplitst in een fictie en non-fictie gedeelte. Een nuttige bron voor het gebruik van de metadata is <a %(a_metadata)s>deze pagina</a> (blokkeert IP-reeksen, VPN kan nodig zijn). Vanaf 2024-03 worden nieuwe torrents gepost in <a %(a_href)s>deze forumthread</a> (blokkeert IP-reeksen, VPN kan nodig zijn). Fictie torrents op Anna’s Archive Libgen.rs Fictie torrents Libgen.rs Discussieforum Libgen.rs Metadata Libgen.rs metadata veld informatie Libgen.rs Non-fictie torrents Non-fictie torrents op Anna’s Archive %(example)s voor een fictie boek. Deze <a %(blog_post)s>eerste release</a> is vrij klein: ongeveer 300GB aan boekomslagen van de Libgen.rs fork, zowel fictie als non-fictie. Ze zijn georganiseerd op dezelfde manier als hoe ze op libgen.rs staan, bijvoorbeeld: %(example)s voor een non-fictie boek. Net als bij de Z-Library collectie hebben we ze allemaal in een groot .tar-bestand geplaatst, dat gemount kan worden met <a %(a_ratarmount)s>ratarmount</a> als je de bestanden direct wilt serveren. Release 1 (%(date)s) Het korte verhaal van de verschillende Library Genesis (of “Libgen”) forks, is dat na verloop van tijd de verschillende mensen die betrokken waren bij Library Genesis ruzie kregen en hun eigen weg gingen. Volgens deze <a %(a_mhut)s>forumpost</a> werd Libgen.li oorspronkelijk gehost op “http://free-books.dontexist.com”. De “.fun” versie is gemaakt door de oorspronkelijke oprichter. Het wordt vernieuwd ten gunste van een nieuwe, meer gedistribueerde versie. De <a %(a_li)s>“.li” versie</a> heeft een enorme collectie strips, evenals andere inhoud, die (nog) niet beschikbaar is voor bulk download via torrents. Het heeft wel een aparte torrentcollectie van fictieboeken en bevat de metadata van <a %(a_scihub)s>Sci-Hub</a> in zijn database. De “.rs” versie heeft zeer vergelijkbare data en publiceert het meest consequent hun collectie in bulk torrents. Het is ruwweg opgesplitst in een “fictie” en een “non-fictie” sectie. Oorspronkelijk op “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> is in zekere zin ook een fork van Library Genesis, hoewel ze een andere naam voor hun project hebben gebruikt. Libgen.rs We verrijken onze collectie ook met metadata-only bronnen, die we kunnen koppelen aan bestanden, bijvoorbeeld met behulp van ISBN-nummers of andere velden. Hieronder vind je een overzicht van deze bronnen. Nogmaals, sommige van deze bronnen zijn volledig open, terwijl we andere moeten scrapen. Let op dat we bij metadata zoekopdrachten de originele records tonen. We voegen geen records samen. Metadata-only bronnen Open Library is een open source project van het Internet Archive om elk boek ter wereld te catalogiseren. Het heeft een van 's werelds grootste operaties om boeken te scannen en heeft veel boeken beschikbaar voor digitale uitleen. De metadata catalogus van boeken is vrij beschikbaar om te downloaden en is opgenomen in Anna’s Archive (hoewel momenteel niet in de zoekfunctie, behalve als je expliciet zoekt op een Open Library ID). Open Library Uitgezonderd duplicaten Laatst bijgewerkt Percentages van aantallen bestanden %% gemirrored door AA / torrents beschikbaar Grootte Bron Hieronder vind je een kort overzicht van de bronnen van de bestanden op Anna’s Archive. Aangezien de schaduw bibliotheken vaak gegevens van elkaar synchroniseren, is er aanzienlijke overlap tussen de bibliotheken. Daarom komen de aantallen niet overeen met het totaal. Het percentage “gemirrored en geseed door Anna’s Archive” toont hoeveel bestanden we zelf mirroren. We seeden die bestanden in bulk via torrents en maken ze beschikbaar voor directe download via partnerwebsites. Overzicht Totaal Torrents op Anna’s Archive Voor achtergrond over Sci-Hub, kun je terecht op de <a %(a_scihub)s>officiële website</a>, de <a %(a_wikipedia)s>Wikipedia-pagina</a> en dit <a %(a_radiolab)s>podcastinterview</a>. Let op dat Sci-Hub sinds <a %(a_reddit)s>2021 bevroren</a> is. Het was al eerder bevroren, maar in 2021 werden er een paar miljoen papers toegevoegd. Nog steeds worden er een beperkt aantal papers toegevoegd aan de Libgen “scimag” collecties, maar niet genoeg om nieuwe bulk torrents te rechtvaardigen. We gebruiken de Sci-Hub metadata zoals verstrekt door <a %(a_libgen_li)s>Libgen.li</a> in de “scimag” collectie. We gebruiken ook de dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Let op dat de “smarch” torrents <a %(a_smarch)s>verouderd</a> zijn en daarom niet zijn opgenomen in onze torrent lijst. Torrents op Libgen.li Torrents op Libgen.rs Metadata en torrents Updates op Reddit Podcastinterview Wikipedia-pagina Sci-Hub Sci-Hub: bevroren sinds 2021; meeste beschikbaar via torrents Libgen.li: sindsdien kleine toevoegingen</div> Sommige bron bibliotheken promoten het massaal delen van hun data via torrents, terwijl anderen hun collectie niet gemakkelijk delen. In het laatste geval probeert Anna’s Archive hun collecties te scrapen en beschikbaar te maken (zie onze <a %(a_torrents)s>Torrents</a> pagina). Er zijn ook tussenliggende situaties, bijvoorbeeld w aar bronbibliotheken bereid zijn te delen, maar niet de middelen hebben om dit te doen. In die gevallen proberen we ook te helpen. Hieronder vind je een overzicht van hoe we omgaan met de verschillende bron bibliotheken. Bron bibliotheken %(icon)s Diverse databanken verspreid over het Chinese internet; echter vaak betaalde databanken %(icon)s De meeste bestanden zijn alleen toegankelijk met premium BaiduYun-accounts; trage downloadsnelheden. %(icon)s Anna’s Archive beheert een collectie van <a %(duxiu)s>DuXiu bestanden</a> %(icon)s Verschillende metadata databases verspreid over het Chinese internet; echter vaak betaalde databases %(icon)s Geen gemakkelijk toegankelijke metadata dumps beschikbaar voor hun volledige collectie. %(icon)s Anna’s Archive beheert een collectie van <a %(duxiu)s>DuXiu metadata</a> Bestanden %(icon)s Bestanden alleen beperkt beschikbaar voor lenen, met verschillende toegangsbeperkingen %(icon)s Anna’s Archief beheert een collectie van <a %(ia)s>IA bestanden</a> %(icon)s Enige metadata beschikbaar via <a %(openlib)s>Open Library database dumps</a>, maar deze dekken niet de volledige IA-collectie %(icon)s Geen gemakkelijk toegankelijke metadata dumps beschikbaar voor hun volledige collectie %(icon)s Anna’s Archive beheert een collectie van <a %(ia)s>IA metadata</a> Laatst bijgewerkt %(icon)s Anna’s Archief en Libgen.li beheren gezamenlijk collecties van <a %(comics)s>stripboeken</a>, <a %(magazines)s>tijdschriften</a>, <a %(standarts)s>standaarddocumenten</a> en <a %(fiction)s>fictie (afgeleid van Libgen.rs)</a>. %(icon)s Non-fictie torrents worden gedeeld met Libgen.rs (en <a %(libgenli)s>hier</a> gemirrored). %(icon)s Kwartaallijkse <a %(dbdumps)s>HTTP-database dumps</a> %(icon)s Geautomatiseerde torrents voor <a %(nonfiction)s>Non-Fictie</a> en <a %(fiction)s>Fictie</a> %(icon)s Anna’s Archive beheert een collectie van <a %(covers)s>boekomslag torrents</a> %(icon)s Dagelijkse <a %(dbdumps)s>HTTP-database dumps</a> Metadata %(icon)s Maandelijkse <a %(dbdumps)s>database dumps</a> %(icon)s Data torrents beschikbaar <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a>, en <a %(libgenli)s>hier</a> %(icon)s Sommige nieuwe bestanden <a %(libgenrs)s>worden</a> <a %(libgenli)s>toegevoegd</a> aan Libgen’s “scimag”, maar niet genoeg om nieuwe torrents te rechtvaardigen %(icon)s Sci-Hub heeft sinds 2021 geen nieuwe bestanden meer toegevoegd. %(icon)s Metadata dumps <a %(scihub1)s>hier</a> en <a %(scihub2)s>hier</a> beschikbaar. Ook als onderdeel beschikbaar van de <a %(libgenli)s>Libgen.li database</a> (die wij gebruiken) Bron %(icon)s Diverse kleinere of eenmalige bronnen. We moedigen mensen aan om eerst naar andere schaduw bibliotheken te uploaden, maar soms hebben mensen collecties die te groot zijn voor anderen om te verwerken, maar niet groot genoeg om hun eigen categorie te rechtvaardigen. %(icon)s Niet direct beschikbaar in bulk, beschermd tegen scraping %(icon)s Anna’s Archive beheert een collectie van <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Anna’s Archive en Z-Library beheren gezamenlijk een collectie van <a %(metadata)s>Z-Library metadata</a> en <a %(files)s>Z-Library bestanden</a> Datasets We combineren al de bovenstaande bronnen in één gecombineerde database die we gebruiken om deze website te laten draaien. Deze gecombineerde database is niet direct beschikbaar, maar aangezien Anna’s Archive volledig open source is, kan deze vrij eenvoudig <a %(a_generated)s>gegenereerd</a> of <a %(a_downloaded)s>gedownload</a> worden als ElasticSearch en MariaDB databases. De scripts op die pagina zullen automatisch alle vereiste metadata downloaden van de hierboven genoemde bronnen. Als je onze data wil verkennen voordat je die scripts lokaal uitvoert, kun je onze JSON-bestanden bekijken, die verder linken naar andere JSON-bestanden. <a %(a_json)s>Dit bestand</a> is een goed startpunt. Gecombineerde database Torrents door Anna’s Archive bladeren zoeken Verschillende kleinere of eenmalige bronnen. We moedigen mensen aan om eerst naar andere schaduw-bibliotheken te uploaden, maar soms hebben mensen collecties die te groot zijn voor anderen om te verwerken, maar niet groot genoeg om hun eigen categorie te rechtvaardigen. Overzicht van <a %(a1)s>datasets pagina</a>. Van <a %(a_href)s>aaaaarg.fail</a>. Lijkt redelijk compleet te zijn. Van onze vrijwilliger “cgiym”. Van een <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Heeft een vrij grote overlap met bestaande paper collecties, maar zeer weinig MD5-overeenkomsten, dus we besloten het volledig te behouden. Scrape van <q>iRead eBooks</q> (= fonetisch <q>ai rit i-books</q>; airitibooks.com), door vrijwilliger <q>j</q>. Komt overeen met <q>airitibooks</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>. Uit een collectie <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Gedeeltelijk van de oorspronkelijke bron, gedeeltelijk van the-eye.eu, gedeeltelijk van andere mirrors. Van een privé boeken-torrentwebsite, <a %(a_href)s>Bibliotik</a> (vaak aangeduid als “Bib”), waarvan boeken in torrents werden gebundeld op naam (A.torrent, B.torrent) en verspreid via the-eye.eu. Van onze vrijwilliger “bpb9v”. Voor meer informatie over <a %(a_href)s>CADAL</a>, zie de notities op onze <a %(a_duxiu)s>DuXiu dataset pagina</a>. Meer van onze vrijwilliger “bpb9v”, voornamelijk DuXiu-bestanden, evenals een map “WenQu” en “SuperStar_Journals” (SuperStar is het bedrijf achter DuXiu). Van onze vrijwilliger “cgiym”, Chinese teksten uit verschillende bronnen (weergegeven als submappen), waaronder van <a %(a_href)s>China Machine Press</a> (een grote Chinese uitgever). Niet-Chinese collecties (weergegeven als submappen) van onze vrijwilliger “cgiym”. Scrape van boeken over Chinese architectuur, door vrijwilliger <q>cm</q>: <q>Ik heb het verkregen door een netwerk kwetsbaarheid bij de uitgeverij te exploiteren, maar die maas in de wet is inmiddels gesloten</q>. Komt overeen met <q>chinese_architecture</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>. Boeken van academische uitgeverij <a %(a_href)s>De Gruyter</a>, verzameld uit een paar grote torrents. Scrape van <a %(a_href)s>docer.pl</a>, een Poolse website voor het delen van bestanden, gericht op boeken en andere geschreven werken. Gescrapet in eind 2023 door vrijwilliger “p”. We hebben geen goede metadata van de originele website (zelfs geen bestandsextensies), maar we hebben gefilterd op boekachtige bestanden en konden vaak metadata uit de bestanden zelf halen. DuXiu epubs, direct van DuXiu, verzameld door vrijwilliger “w”. Alleen recente DuXiu-boeken zijn direct beschikbaar via ebooks, dus de meeste hiervan moeten recent zijn. Overgebleven DuXiu-bestanden van vrijwilliger “m”, die niet in het DuXiu-eigen PDG-formaat waren (de hoofd <a %(a_href)s>DuXiu dataset</a>). Verzameld uit vele originele bronnen, helaas zonder die bronnen in het bestandspad te behouden. <span></span> <span></span> <span></span> Scrape van erotische boeken, door vrijwilliger <q>do no harm</q>. Komt overeen met <q>hentai</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>. <span></span> <span></span> Collectie gescrapet van een Japanse Manga-uitgever door vrijwilliger “t”. <a %(a_href)s>Geselecteerde gerechtelijke archieven van Longquan</a>, verstrekt door vrijwilliger “c”. Scrape van <a %(a_href)s>magzdb.org</a>, een bondgenoot van Library Genesis (wordt gelinkt op de libgen.rs homepage) maar die hun bestanden niet direct wilden verstrekken. Verkregen door vrijwilliger “p” eind 2023. <span></span> Diverse kleine uploads, te klein als hun eigen subcollectie, maar weergegeven als mappen. Ebooks van AvaxHome, een Russische bestandsdeelwebsite. Archief van kranten en tijdschriften. Komt overeen met <q>newsarch_magz</q> metadata in <a %(a1)s><q>Andere metadata scrapes</q></a>. Scrape van het <a %(a1)s>Philosophy Documentation Center</a>. Collectie van vrijwilliger “o” die Poolse boeken direct van originele release (“scene”) websites verzamelde. Gecombineerde collecties van <a %(a_href)s>shuge.org</a> door vrijwilligers “cgiym” en “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (vernoemd naar de fictieve bibliotheek), gescrapet in 2022 door vrijwilliger “t”. <span></span> <span></span> <span></span> Sub-sub-collecties (weergegeven als mappen) van vrijwilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (door <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, mijn kleine boekenhuis — woz9ts: “Deze site richt zich voornamelijk op het delen van hoogwaardige e-boek bestanden, waarvan sommige door de eigenaar zelf zijn gezet. De eigenaar werd <a %(a_arrested)s>gearresteerd</a> in 2019 en iemand heeft een collectie van de bestanden die hij deelde gemaakt.”). Overgebleven DuXiu-bestanden van vrijwilliger “woz9ts”, die niet in het DuXiu-eigen PDG-formaat waren (moet nog omgezet worden naar PDF). De “upload” collectie is opgesplitst in kleinere subcollecties, die worden aangegeven in de AACIDs en torrentnamen. Alle subcollecties zijn eerst gededupliceerd aan de hand van de hoofcollectie, hoewel de metadata “upload_records” JSON-bestanden nog steeds veel verwijzingen naar de originele bestanden bevatten. Niet-boek bestanden zijn ook verwijderd uit de meeste subcollecties, en worden meestal <em>niet</em> vermeld in de “upload_records” JSON. De subcollecties zijn: Notities Subcollectie Veel subcollecties zelf bestaan uit sub-sub-collecties (bijv. van verschillende oorspronkelijke bronnen), die worden weergegeven als mappen in de “filepath” velden. Uploads naar Anna’s Archive Onze blogpost over deze data <a %(a_worldcat)s>WorldCat</a> is een propriëtaire database van de non-profitorganisatie <a %(a_oclc)s>OCLC</a>, die metadatarecords van bibliotheken over de hele wereld verzamelt. Het is waarschijnlijk de grootste collectie van bibliotheek-metadata ter wereld. In oktober 2023 hebben we een uitgebreide scrape van de OCLC (WorldCat) database <a %(a_scrape)s>uitgebracht</a>, in het <a %(a_aac)s>Anna’s Archive Containers-formaat</a>. Oktober 2023, eerste uitgave: OCLC (WorldCat) Torrents door Anna’s Archive Voorbeeldrecord op Anna’s Archive (oorspronkelijke collectie) Voorbeeldrecord op Anna’s Archive (“zlib3” collectie) Torrents door Anna’s Archive (metadata + inhoud) Blogpost over Release 1 Blogpost over Release 2 Eind 2022 werden de vermeende oprichters van Z-Library gearresteerd en werden domeinen in beslag genomen door de Amerikaanse autoriteiten. Sindsdien is de website langzaam weer online gekomen. Het is onbekend wie het momenteel beheert. Update vanaf februari 2023. Z-Library heeft zijn wortels in de <a %(a_href)s>Library Genesis</a> gemeenschap, en is oorspronkelijk opgestart met hun data. Sindsdien is het aanzienlijk geprofessionaliseerd en heeft het een veel modernere interface. Ze zijn daarom in staat om veel meer donaties te krijgen, zowel financieel om hun website te blijven verbeteren, als donaties van nieuwe boeken. Ze hebben een grote collectie verzameld naast Library Genesis. De collectie bestaat uit drie delen. De oorspronkelijke beschrijvingspagina's voor de eerste twee delen zijn hieronder bewaard gebleven. Je hebt alle drie de delen nodig om alle data te verkrijgen (behalve verouderde torrents, die zijn doorgestreept op de torrentpagina). %(title)s: onze eerste release. Dit was de allereerste release van wat toen de “Pirate Library Mirror” (“pilimi”) werd genoemd. %(title)s: tweede release, dit keer met alle bestanden verpakt in .tar-bestanden. %(title)s: incrementele nieuwe releases, in het <a %(a_href)s>Anna’s Archive Containers (AAC) formaat</a>, nu uitgebracht in samenwerking met het Z-Library team. De oorspronkelijke mirror is in de loop van 2021 en 2022 met veel moeite verkregen. Op dit moment is deze enigszins verouderd: het reflecteert de staat van de collectie in juni 2021. We zullen deze in de toekomst updaten. Op dit moment zijn we gefocust op het uitbrengen van deze eerste release. Aangezien Library Genesis al gepreserveerd is met openbare torrents en is opgenomen in de Z-Library, hebben we in juni 2022 een basale deduplicatie uitgevoerd tegen Library Genesis. Hiervoor hebben we MD5-hashes gebruikt. Waarschijnlijk is er veel meer dubbele inhoud in de bibliotheek, zoals meerdere bestandsformaten van hetzelfde boek. Dit is moeilijk om accuraat te detecteren, dus doen we dat niet. Na de deduplicatie blijven we over met meer dan 2 miljoen bestanden, in totaal net onder de 7TB. De collectie bestaat uit twee delen: een MySQL “.sql.gz” dump van de metadata, en de 72 torrentbestanden van elk ongeveer 50-100GB. De metadata bevat de gegevens zoals gerapporteerd door de Z-Library website (titel, auteur, beschrijving, bestandstype), evenals de werkelijke bestandsgrootte en md5sum die we hebben waargenomen, aangezien deze soms niet overeenkomen. Er lijken reeksen bestanden te zijn waarvoor de Z-Library zelf onjuiste metadata heeft. In sommige geïsoleerde gevallen hebben we mogelijk ook bestanden incorrect gedownload. Dit zullen we in de toekomst proberen te detecteren en corrigeren. De grote torrentbestanden bevatten de daadwerkelijke boekgegevens, met de Z-Library ID als bestandsnaam. De bestandsextensies kunnen worden gereconstrueerd met behulp van de metadata dump. De collectie is een mix van non-fictie en fictie inhoud (niet gescheiden zoals in Library Genesis). De kwaliteit varieert ook sterk. Deze eerste release is nu volledig beschikbaar. Let op dat de torrentbestanden alleen beschikbaar zijn via onze Tor-mirror. Release 1 (%(date)s) Dit is een enkel extra torrentbestand. Het bevat geen nieuwe informatie, maar het bevat wel wat data die enige tijd kan kosten om te berekenen. Dat maakt het handig om te hebben, aangezien het downloaden van deze torrent vaak sneller is dan het vanaf nul te berekenen. In het bijzonder bevat het SQLite-indexen voor de tar-bestanden, voor gebruik met <a %(a_href)s>ratarmount</a>. Release 2 addendum (%(date)s) We hebben alle boeken verzameld die tussen onze laatste mirror en augustus 2022 aan de Z-Library zijn toegevoegd. We hebben ook enkele boeken gescrapet die we de eerste keer hebben gemist. Alles bij elkaar is deze nieuwe collectie ongeveer 24TB. Nogmaals, deze collectie is gededupliceerd tegen Library Genesis, aangezien er al torrents beschikbaar zijn voor die collectie. De data is vergelijkbaar georganiseerd als bij de eerste release. Er is een MySQL “.sql.gz” dump van de metadata, die ook alle metadata van de eerste release bevat en deze daarmee vervangt. We hebben ook enkele nieuwe kolommen toegevoegd: We hebben dit de vorige keer al vermeld, maar om het te verduidelijken: “filename” en “md5” zijn de daadwerkelijke eigenschappen van het bestand, terwijl “filename_reported” en “md5_reported” zijn wat we van Z-Library hebben gescrapet. Soms komen deze twee niet overeen, dus hebben we beide opgenomen. Voor deze release hebben we de verzameling veranderd naar “utf8mb4_unicode_ci”, wat compatibel zou moeten zijn met oudere versies van MySQL. De databestanden zijn vergelijkbaar met de vorige keer, hoewel ze veel groter zijn. We konden ons simpelweg niet druk maken om tonnen kleinere torrentbestanden te maken. “pilimi-zlib2-0-14679999-extra.torrent” bevat alle bestanden die we in de laatste release hebben gemist, terwijl de andere torrents allemaal nieuwe ID-reeksen zijn.  <strong>Update %(date)s:</strong> We hebben de meeste van onze torrents te groot gemaakt, waardoor torrentclients moeite hadden. We hebben ze verwijderd en nieuwe torrents uitgebracht. <strong>Update %(date)s:</strong> Er waren nog steeds te veel bestanden, dus hebben we ze in tar-bestanden verpakt en opnieuw nieuwe torrents uitgebracht. %(key)s: of dit bestand al in Library Genesis staat, in de non-fictie of fictie collectie (gematcht op md5). %(key)s: in welke torrent dit bestand zit. %(key)s: ingesteld wanneer we het boek niet konden downloaden. Release 2 (%(date)s) Zlib-releases (oorspronkelijke beschrijvingspagina's) Tor-domein Hoofdwebsite Z-Library scrape De “Chinese” collectie in Z-Library lijkt dezelfde te zijn als onze DuXiu collectie, maar met verschillende MD5's. We sluiten deze bestanden uit van torrents om duplicatie te voorkomen, maar tonen ze nog steeds in onze zoekindex. Metadata Je krijgt %(percentage)s%% snelle bonusdownloads, omdat je bent doorverwezen door gebruiker %(profile_link)s. Dit geldt voor de gehele lidmaatschapstermijn. Doneer Lid worden Geselecteerd kortingen tot wel %(percentage)s%% Alipay ondersteunt internationale credit/debit kaarten. Zie <a %(a_alipay)s>deze gids</a> voor meer informatie. Stuur ons cadeaubonnen van Amazon.com met je creditcard of betaalpas. Je kunt crypto kopen met een creditcard of betaalpas. WeChat (Weixin Pay) biedt ondersteuning voor internationale credit/debit kaarten. Ga in de WeChat-app naar Me → Services → Wallet → Add a Card. Als je die optie niet ziet, kun je het inschakelen via Me → Settings → General → Tools → Weixin Pay → Enable. (gebruik bij het verzenden van Ethereum vanuit Coinbase) gekopieerd! kopiëren (laagste minimumbedrag) (waarschuwing: hoogste minimumbedrag) -%(percentage)s%% 12 maanden 1 maand 24 maanden 3 maanden 48 maanden 6 maanden 96 maanden Kies hoelang je je wil abonneren. <div %(div_monthly_cost)s></div><div %(div_after)s>na <span %(span_discount)s></span> kortingen</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% voor 12 maanden voor 1 maand voor 24 maanden voor 3 maanden voor 48 maanden voor 6 maanden voor 96 maanden %(monthly_cost)s / maand Contact opnemen Directe <strong>SFTP</strong>-servers Donatie op bedrijfsniveau of uitwisseling van nieuwe collecties (bijv. nieuwe scans of datasets via OCR). Toegang voor experts <strong>Onbeperkte</strong> snelle toegang <div %(div_question)s>Kan ik mijn lidmaatschap upgraden of meerdere lidmaatschappen krijgen?</div> <div %(div_question)s>Kan ik een donatie doen zonder lid te worden?</div> Natuurlijk. We accepteren donaties van elk bedrag op dit Monero (XMR) adres: %(address)s. <div %(div_question)s>Wat betekenen de periodes per maand?</div> Je kunt aan de lagere kant van een periode komen door alle kortingen toe te passen, zoals het kiezen van een periode langer dan een maand. <div %(div_question)s>Worden lidmaatschappen automatisch verlengd?</div> Lidmaatschappen worden <strong>niet</strong> automatisch verlengd. Blijf zolang lid als je zelf wil. <div %(div_question)s>Waar geven jullie de donaties aan uit?</div> 100%% gaat naar het behoud en het toegankelijk maken van de kennis en cultuur van de wereld. Momenteel geven we het vooral uit aan servers, opslag en bandbreedte. Er gaat geen geld naar teamleden. <div %(div_question)s>Kan ik een grote donatie doen?</div> Dat zou geweldig zijn! Als je meer dan een paar duizend dollar wilt doneren, neem dan contact op via %(email)s. <div %(div_question)s>Zijn er andere betaalmethodes beschikbaar?</div> Momenteel niet. Veel mensen willen dat archieven zoals deze niet bestaan, dus we moeten voorzichtig zijn. Als je ons kunt helpen om andere (gemakkelijkere) betaalmethodes veilig op te zetten, neem dan contact op via %(email)s. Veelgestelde vragen over donaties Er loopt nog een <a %(a_donation)s>bestaande donatie</a>. Rond die donatie af of annuleer hem voordat je een nieuwe donatie doet. <a %(a_all_donations)s>Al mijn donaties weergeven</a> Neem rechtstreeks contact met ons op via %(email)s voor donaties van $ 5000 en meer. We verwelkomen grote donaties van vermogende individuen en instellingen.  Houd er rekening mee dat hoewel de lidmaatschappen op deze pagina “per maand” zijn, het eenmalige donaties zijn (niet terugkerend). Zie de <a %(faq)s>Veelgestelde vragen over donaties</a>. Anna’s Archief is een non-profit, open-source, open-data project. Door te doneren en lid te worden steun je onze activiteiten en ontwikkeling. Aan al onze leden: bedankt dat jullie ons draaiende houden! ❤️ Voor meer informatie, bekijk de <a %(a_donate)s>Veelgestelde vragen over donaties</a>. <a %(a_login)s>Log in of registreer</a> om lid te worden. Bedankt voor je steun! $%(cost)s / maand Als je tijdens het betalen een fout hebt gemaakt, kunnen we je niet terugbetalen. We doen dan ons best om het te corrigeren. Zoek de pagina Crypto in de PayPal-app of -website. Deze vind je vaak onder Financiën. Ga naar de Bitcoin-pagina in de PayPal-app of -website. Druk op de knop Versturen %(transfer_icon)s en daarna op Verzenden. Alipay Alipay 支付宝 / WeChat 微信 Amazon-cadeaubon %(amazon)s cadeaubon Bankkaart Bankkaart (via app) Binance Credit/debit/Apple/Google (BMC) Cash App Creditcard/betaalpas Creditcard/betaalpas 2 Creditcard/debetkaart (back-up) Crypto %(bitcoin_icon)s Kaart / PayPal / Venmo PayPal (VS) %(bitcoin_icon)s PayPal PayPal (normaal) Pix (Brazilië) Revolut (tijdelijk niet beschikbaar) WeChat Selecteer je gewenste cryptomunt: Doneren met een Amazon-cadeaubon. <strong>BELANGRIJK:</strong> Deze optie is voor %(amazon)s. Als u een andere Amazon-website wilt gebruiken, selecteer deze dan hierboven. <strong>BELANGRIJK:</strong> We ondersteunen alleen Amazon.com, geen andere websites van Amazon. Lokale winkels zoals .nl, .de en .co.uk worden níét ondersteund. Schrijf NIET je eigen bericht. Voer het exacte bedrag in: %(amount)s Houd er rekening mee dat we moeten afronden naar bedragen die door onze wederverkopers worden geaccepteerd (minimaal %(minimum)s). Doneer met een creditcard/debetkaart via de Alipay-app (supereenvoudig in te stellen). Installeer de Alipay-app vanuit de <a %(a_app_store)s>Apple App Store</a> of <a %(a_play_store)s>Google Play Store</a>. Registreer met je telefoonnummer. Geen verdere persoonlijke gegevens zijn vereist. <span %(style)s>1</span>Installeer de Alipay-app Ondersteund: Visa, MasterCard, JCB, Diners Club en Discover. Zie <a %(a_alipay)s>deze handleiding</a> voor meer informatie. <span %(style)s>2</span>Voeg bankkaart toe Met Binance koop je Bitcoin met een creditcard/debetkaart of bankrekening, en doneer je vervolgens die Bitcoin aan ons. Op die manier kunnen we je donatie veilig en anoniem accepteren. Binance is beschikbaar in bijna elk land en ondersteunt de meeste banken en creditcards/debetkaarten. Dit is op dit moment onze voornaamste aanbeveling. We waarderen het dat je de tijd neemt om te leren hoe je met deze methode kunt doneren, omdat dit ons enorm helpt. Voor creditcards, debitcards, Apple Pay en Google Pay gebruiken we “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). In hun systeem is één “koffie” gelijk aan $5, dus je donatie wordt afgerond naar het dichtstbijzijnde veelvoud van 5. Doneren met Cash App. Als je Cash App hebt, is dat de eenvoudigste manier om te doneren! Voor transacties onder %(amount)s kan Cash App kosten van %(fee)s in rekening brengen. Bedragen boven %(amount)s zijn gratis! Doneren met een creditcard of betaalpas. Deze methode gebruikt een cryptocurrency aanbieder als tussenliggende conversie. Dit kan verwarrend zijn, dus gebruik deze methode alleen als andere betaalmethoden niet werken. De methode werkt ook niet in alle landen. We kunnen credit/debit kaarten niet rechtstreeks ondersteunen, omdat banken niet met ons willen werken. ☹ Er zijn echter verschillende manieren om toch credit/debit kaarten te gebruiken via andere betaalmethoden: Met crypto kun je doneren met BTC, ETH, XMR en SOL. Gebruik deze optie als je al bekend bent met cryptocurrency. Met crypto kun je BTC, ETH, XMR en meer doneren. Crypto expressdiensten Als je voor de eerste keer crypto gebruikt, raden we aan om %(options)s te gebruiken om Bitcoin (de originele en meest gebruikte cryptocurrency) te kopen en te doneren. Bij kleine donaties kunnen creditcardkosten onze korting van %(discount)s%% tenietdoen. Daarom raden we langere abonnementen aan. Doneer met creditcard/debetkaart, PayPal of Venmo. Op de volgende pagina kun je een keuze maken. Google Pay en Apple Pay kunnen ook werken. Bij kleine donaties zijn de kosten hoog. Daarom raden we langere abonnementen aan. We gebruiken PayPal Crypto om je te laten doneren met PayPal (VS). Zo blijven we anoniem. We waarderen het dat je de tijd neemt om te leren hoe je op deze manier kunt doneren; dit helpt ons enorm. Doneren met PayPal. Doneer met je reguliere PayPal account. Doneer met Revolut. Als je Revolut hebt, is dit de makkelijkste manier om te doneren! Deze betaalmethode staat slechts een maximum van %(amount)s toe. Selecteer een andere duur of betaalmethode. Voor deze betaalmethode is een minimum van %(amount)s vereist. Selecteer een andere duur of betaalmethode. Binance Coinbase Kraken Kies een betaalmethode. 'Een torrent adopteren': je gebruikersnaam of bericht in de bestandsnaam van een torrent <div %(div_months)s>een keer per 12 maanden lidmaatschap</div> Je gebruikersnaam of anonieme vermelding in de vermeldingen Vroege toegang tot nieuwe functies Exclusieve Telegram met updates van achter de schermen %(number)s snelle downloads per dag als je deze maand doneert! <a %(a_api)s>JSON API</a> toegang Een legendarische status bij het behoud van de kennis en cultuur van de mensheid Voorgaande voordelen, plus: Verdien <strong>%(percentage)s%% bonus downloads</strong> door <a %(a_refer)s>vrienden door te verwijzen</a>. SciDB-papers <strong>(onbeperkt)</strong> zonder verificatie Wanneer je vragen hebt over je account of donaties, voeg dan je account-ID, screenshots, bonnen en zoveel mogelijk informatie toe. We controleren onze e-mail slechts elke 1-2 weken, dus het niet toevoegen van deze informatie zal ervoor zorgen dat een oplossing langer op zich laat wachten. <a %(a_refer)s>Verwijs je vrienden</a> voor nog meer downloads! We zijn een klein team dat bestaat uit vrijwilligers. Het kan 1-2 weken duren voordat je antwoord van ons krijgt. Let op dat je accountnaam of profielfoto er vreemd uit kan zien. Geen zorgen! Deze accounts worden beheerd door onze donatiepartners. Onze accounts zijn niet gehackt. <span %(span_cost)s></span> doneren <span %(span_label)s></span> voor 12 maanden %(tier_name)s voor 1 maand %(tier_name)s voor 24 maanden %(tier_name)s voor 3 maanden %(tier_name)s voor 48 maanden “%(tier_name)s” voor 6 maanden %(tier_name)s voor 96 maanden “%(tier_name)s” Je kunt de donatie nog annuleren tijdens het afrekenen. Klik op de doneerknop om deze donatie te bevestigen. <strong>Belangrijke opmerking:</strong> Cryptoprijzen kunnen enorm fluctueren; soms wel 20%% in een paar minuten. Dit is nog steeds minder dan de kosten die we bij veel betalingsaanbieders oplopen. Deze vragen vaak 50-60%% om met een 'grijze' liefdadigheidsinstelling als wij te werken. <u>Als je ons een afschrift stuurt met de oorspronkelijke prijs die je hebt betaald, dan schrijven we je account alsnog het gekozen lidmaatschap bij</u> (zolang het afschrift niet ouder dan een paar uur is). We waarderen het enorm dat je dit soort dingen verdraagt om ons te steunen! ❤️ ❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw. <span %(span_circle)s>1</span>Bitcoin op PayPal kopen <span %(span_circle)s>2</span>Bitcoin naar ons adres versturen ✅ Je wordt doorverwezen naar de donatiepagina… Doneren Wacht alsjeblieft minstens <span %(span_hours)s>24 uur</span> (en ververs deze pagina) voordat je contact met ons opneemt. Als je wil doneren zonder lidmaatschap (ongeacht het bedrag), dan kun je dit Monero (XMR)-adres gebruiken: %(address)s. Nadat je je cadeaubon hebt verzonden, bevestigt ons geautomatiseerde systeem dit binnen een paar minuten. Probeer je cadeaubon opnieuw te verzenden als dit niet werkt (<a %(a_instr)s>instructies</a>). Als dat nog steeds niet werkt, stuur ons dan een e-mail. Anna controleert het vervolgens handmatig (dit kan een paar dagen duren). Vermeld daarbij of je al opnieuw hebt geprobeerd te verzenden. Voorbeeld: Gebruik het <a %(a_form)s>officiële formulier van Amazon.com</a> om ons een cadeaubon van %(amount)s te sturen naar onderstaand e-mailadres. Geadresseerde 'Aan' in het formulier: Amazon-cadeaubon We accepteren geen andere methodes voor cadeaubonnen, <strong>alleen die rechtstreeks via het officiële formulier op Amazon.com worden verzonden</strong>. We kunnen je cadeaubon niet retourneren als je dit formulier niet gebruikt. Slechts één keer gebruiken. Uniek voor jouw account; niet delen. Wachten op cadeaubon (vernieuw de pagina om te controleren)… Open de <a %(a_href)s>QR-code donatiepagina</a>. Scan de QR-code met de Alipay-app, of druk op de knop om de Alipay-app te openen. Wees geduldig; het kan even duren voordat de pagina geladen is, omdat deze in China is. <span %(style)s>3</span>Doe een donatie (scan QR-code of druk op de knop) Koop PYUSD op PayPal Koop Bitcoin (BTC) met Cash App Koop iets meer (we raden %(more)s meer aan) dan het bedrag dat je doneert (%(amount)s) om transactiekosten te dekken. Je houdt alles wat overblijft. Ga naar de “Bitcoin” (BTC) pagina in Cash App. Stuur de Bitcoin naar ons adres Voor kleine donaties (onder $25) moet je mogelijk Rush of Priority gebruiken. Klik op de knop “Stuur bitcoin” om een “opname” te doen. Schakel over van dollars naar BTC door op het %(icon)s icoon te drukken. Voer het BTC bedrag er onder in en klik op “Stuur”. Bekijk <a %(help_video)s>deze video</a> als je vastloopt. Expressdiensten zijn handig, maar rekenen hogere kosten. Je kunt dit gebruiken in plaats van een crypto-uitwisseling als je snel een grotere donatie wil doen en geen bezwaar hebt tegen kosten van $5-10. Zorg ervoor dat je het exacte crypto bedrag verzendt dat op de donatiepagina wordt weergegeven en niet het bedrag in $USD. Anders worden de kosten afgetrokken en kunnen we je lidmaatschap niet automatisch verwerken. Soms kan de bevestiging tot 24 uur duren, dus zorg ervoor dat u deze pagina ververst (ook als deze is verlopen). Instructies voor creditcard/betaalpas Doneren via onze pagina voor creditcards/betaalpassen Sommige stappen benoemen 'cryptowallets'. Geen zorgen: je hoeft niets over crypto te leren. Instructies voor %(coin_name)s Scan deze QR -code met uw crypto -portemonnee -app om de betalingsgegevens snel in te vullen Scan QR -code om te betalen We ondersteunen alleen de standaardversie van cryptomunten, dus geen exotische netwerken of versies van munten. Afhankelijk van de munt kan het een uur duren om transacties te bevestigen. Doneer %(amount)s op <a %(a_page)s>deze pagina</a>. Deze donatie is verlopen. Annuleer 'm en start een nieuwe. Als u al betaald heeft: Ja, ik heb mijn ontvangstbewijs gemaild Als de koers op de cryptobeurs tijdens de transactie fluctueerde, zorg er dan voor dat je het afschrift met de oorspronkelijke wisselkoers bijvoegt. We waarderen het dat je de moeite neemt om crypto te gebruiken. Dat helpt ons enorm! ❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw. <span %(span_circle)s>%(circle_number)s</span>Mail ons het ontvangstbewijs Neem contact met ons op via %(email)s als je problemen ondervindt. Voeg zo veel mogelijk informatie toe (zoals screenshots). ✅ Bedankt voor je donatie! Anna activeert je lidmaatschap handmatig binnen een paar dagen. Stuur een ontvangstbewijs of schermafbeelding naar je persoonlijke verificatieadres: Klik op deze knop als je je ontvangstbewijs hebt gemaild, zodat Anna dat handmatig kan controleren (dit kan een paar dagen duren): Stuur een ontvangstbewijs of screenshot naar je persoonlijke verificatie adres. Gebruik dit e-mailadres NIET voor je PayPal donatie. Annuleren Ja, annuleren Weet je zeker dat je wilt annuleren? Annuleer niet als je al hebt betaald. ❌ Er ging iets mis. Vernieuw de pagina en probeer het opnieuw. Nieuwe donatie doen ✅ Je donatie is geannuleerd. Datum: %(date)s Identificatiecode: %(id)s Opnieuw bestellen Status: <span %(span_label)s>%(label)s</span> Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand voor %(duration)s maanden, inclusief %(discounts)s%% korting)</span> Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand voor %(duration)s maanden)</span> 1. Voer je e-mailadres in. 2. Selecteer de betaalmethode. 3. Selecteer de betaalmethode opnieuw. 4. Selecteer de wallet 'Self-hosted'. 5. Klik op 'I confirm ownership'. 6. Je ontvangt een afschrift. Stuur dat naar ons. Wij bevestigen je donatie zo snel mogelijk. (het kan handig zijn om te annuleren en een nieuwe donatie te starten) De betaalinstructies zijn nu verouderd. Gebruik de knop 'Opnieuw bestellen' hierboven als je een andere donatie wil doen. Je hebt al betaald. Klik hier als je de betaalinstructies toch wilt bekijken: Oude betaalinstructies tonen Als de donatiepagina wordt geblokkeerd, probeer dan een andere internetverbinding (bijv. VPN of mobiel internet). Helaas is de Alipay pagina vaak alleen toegankelijk vanuit <strong>het vasteland van China</strong>. Je moet mogelijk tijdelijk je VPN uitschakelen, of een VPN naar het vasteland van China gebruiken (Hong Kong werkt soms ook). <span %(span_circle)s>1</span>Doneren met Alipay Doneer het totale bedrag van %(total)s via <a %(a_account)s>dit Alipay account</a> Instructies voor Alipay <span %(span_circle)s>1</span>Maak over naar een van onze cryptorekeningen Doneer het totaalbedrag van %(total)s naar een van de volgende adressen: Instructies voor crypto Volg de instructies om Bitcoin (BTC) te kopen. Je hoeft alleen het bedrag te kopen dat je wil doneren, %(total)s. Voer ons Bitcoin (BTC)-adres in als ontvanger en volg de instructies om je donatie van %(total)s te doen: <span %(span_circle)s>1</span>Doneren met Pix Doneer het totaalbedrag van %(total)s aan <a %(a_account)s>dit Pix-account Instructies voor Pix <span %(span_circle)s>1</span>Doneer via WeChat Doneer het totale bedrag van %(total)s via <a %(a_account)s>dit WeChat account</a> WeChat instructies Gebruik een van de volgende “creditcard naar Bitcoin” express diensten, die slechts enkele minuten duren: BTC / Bitcoin adres (externe wallet): BTC / Bitcoin bedrag: Vul de volgende gegevens in op het formulier: Als deze informatie niet meer up-to-date is, stuur ons dan een e-mail om ons op de hoogte te stellen. Gebruik dit <span %(underline)s>exacte bedrag</span>. Je totale kosten kunnen hoger zijn vanwege creditcardkosten. Voor kleine bedragen kan dit helaas meer zijn dan onze korting. (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, geen verificatie voor de eerste transactie) (minimum: %(minimum)s) (minimum: %(minimum)s afhankelijk van het land, geen verificatie voor de eerste transactie) Volg de instructies om PYUSD (PayPal USD) te kopen. Koop iets meer (we raden %(more)s extra aan) dan het bedrag dat je wilt doneren (%(amount)s) om de transactiekosten te dekken. Wat overblijft, kun je houden. Ga naar de pagina 'PYUSD' in de app of op de website van PayPal. Druk op de knop 'Transfer' %(icon)s en vervolgens op 'Send'. Status bijwerken Start een nieuwe donatie om de teller terug te zetten. Zorg ervoor dat je het BTC-bedrag hieronder gebruikt, en <em>NIET</em> euro's of dollars, anders ontvangen we niet het juiste bedrag en kunnen we je lidmaatschap niet automatisch bevestigen. Koop Bitcoin (BTC) bij Revolut Koop iets meer (we raden %(more)s meer aan) dan het bedrag dat je doneert (%(amount)s), om transactiekosten te dekken. Je houdt alles wat overblijft. Ga naar de “Crypto” pagina in Revolut om Bitcoin (BTC) te kopen. Stuur de Bitcoin naar ons adres Voor kleine donaties (onder de $25) moet je mogelijk Rush of Priority gebruiken. Klik op de knop “Stuur bitcoin” om een “opname” te doen. Schakel over van euro's naar BTC door op het %(icon)s icoon te drukken. Voer het BTC-bedrag er onder in en klik op “Stuur”. Bekijk <a %(help_video)s>deze video</a> als je vastloopt. Status: 1 2 Stapsgewijze handleiding Raadpleeg de stapsgewijze handleiding hierboven. Anders word je mogelijk buitengesloten van dit account! Schrijf je geheime sleutel op om in te loggen als je dat nog niet hebt gedaan: Bedankt voor je donatie! Tijd resterend: Donatie Maak %(amount)s over naar %(account)s Wachten op bevestiging (vernieuw de pagina om te controleren)… Wachten op transactie (vernieuw de pagina om te controleren)… Eerder Snelle downloads van de afgelopen 24 uur tellen mee voor de daglimiet. Downloads van snelle partnerservers worden gemarkeerd met %(icon)s. Laatste 18 uur Nog geen bestanden gedownload. Gedownloade bestanden zijn niet openbaar zichtbaar. Alle tijden worden weergegeven in UTC. Gedownloade bestanden Als je een bestand met zowel een snelle als langzame download hebt binnengehaald, worden beide weergegeven. Maak je niet te veel zorgen, er zijn veel mensen die downloaden van websites waar wij naar linken, en het is uiterst zeldzaam om in de problemen te komen. Om veilig te blijven raden we echter aan een VPN (betaald) of <a %(a_tor)s>Tor</a> (gratis) te gebruiken. Ik heb 1984 van George Orwell gedownload, komt de politie nu aan mijn deur? Jij bent Anna! Wie is Anna? We hebben één stabiele JSON API voor leden om een snelle download-URL te krijgen: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentatie in de JSON zelf). Voor andere gebruiksscenario's, zoals het itereren door al onze bestanden, het bouwen van aangepaste zoekfuncties, enzovoort, raden we aan om onze ElasticSearch- en MariaDB-databases <a %(a_generate)s>te genereren</a> of <a %(a_download)s>te downloaden</a>. De ruwe data kan handmatig worden verkend <a %(a_explore)s>via JSON-bestanden</a>. Onze ruwe torrentlijst kan ook als <a %(a_torrents)s>JSON</a> worden gedownload. Hebben jullie een API? Wij hosten hier geen auteursrechtelijk beschermd materiaal. Wij zijn een zoekmachine en indexeren alleen metadata die al openbaar beschikbaar is. Bij het downloaden van deze externe bronnen raden wij aan om de wetten in jouw jurisdictie te controleren met betrekking tot wat is toegestaan. Wij zijn niet verantwoordelijk voor inhoud die door anderen wordt gehost. Als je klachten hebt over wat je hier ziet, kun je het beste contact opnemen met de oorspronkelijke website. Wij halen regelmatig hun wijzigingen op in onze database. Als je echt denkt dat je een geldige DMCA-klacht hebt waar wij op moeten reageren, vul dan het <a %(a_copyright)s>DMCA / Auteursrecht klachtformulier</a> in. Wij nemen je klachten serieus en zullen zo snel mogelijk contact met je opnemen. Hoe meld ik inbreuk op het auteursrecht? Hier zijn enkele boeken die een speciale betekenis hebben voor de wereld van schaduw bibliotheken en digitale archivering: Wat zijn jullie favoriete boeken? Wij willen iedereen er ook aan herinneren dat al onze code en data volledig open source zijn. Dit is uniek voor projecten zoals het onze — we zijn ons niet bewust van een ander project met een even omvangrijke catalogus die ook volledig open source is. We verwelkomen iedereen die denkt dat we ons project slecht runnen om onze code en data te nemen en hun eigen schaduw bibliotheek op te zetten! We zeggen dit niet uit wrok of iets dergelijks — we denken echt dat dit geweldig zou zijn, omdat het de lat voor iedereen zou verhogen en de erfenis van de mensheid beter zou behouden. Ik haat hoe jullie dit project runnen! Het zou geweldig zijn als mensen '<a %(a_mirrors)s>mirrors</a>' opzetten, die we financieel ondersteunen. Hoe kan ik helpen? Ja. Onze inspiratie voor het verzamelen van metadata is Aaron Swartz’ doel van “één webpagina voor elk boek dat ooit is gepubliceerd”, waarvoor hij <a %(a_openlib)s>Open Library</a> heeft gecreëerd. Dat project heeft het goed gedaan, maar onze unieke positie stelt ons in staat om metadata te verkrijgen die zij niet kunnen verkrijgen. Een andere inspiratie was ons verlangen om te weten <a %(a_blog)s>hoeveel boeken er in de wereld zijn</a>, zodat we kunnen berekenen hoeveel boeken we nog moeten redden. Verzamelen jullie metadata? Let op dat mhut.org bepaalde IP-reeksen blokkeert, dus een VPN kan nodig zijn. <strong>Android:</strong> Klik op het menu met de drie stippen rechtsboven en selecteer “Toevoegen aan startscherm”. <strong>iOS:</strong> Klik op de “Deel” knop onderaan en selecteer “Toevoegen aan startscherm”. We hebben geen officiële mobiele app, maar je kunt wel deze website als app installeren. Hebben jullie een mobiele app? Stuur ze alstublieft naar het <a %(a_archive)s>Internet Archive</a>. Zij zullen ze op de juiste manier bewaren. Hoe doneer ik boeken of ander fysiek materiaal? Hoe vraag ik boeken aan? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmatige updates <a %(a_software)s>Anna’s Software</a> — onze open source code <a %(a_datasets)s>Datasets</a> — over de data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatieve domeinen Zijn er meer bronnen over Anna’s Archive? <a %(a_translate)s>Vertalen op Anna’s Software</a> — ons vertalingssysteem <a %(a_wikipedia)s>Wikipedia</a> — meer over ons (help deze pagina up-to-date te houden, of maak er een aan in je eigen taal!) Selecteer de instellingen die u wilt, laat het zoekvak leeg, klik op “Zoeken” en voeg de pagina dan toe aan uw bladwijzers met de bladwijzerfunctie van uw browser. Hoe sla ik mijn zoekinstellingen op? We verwelkomen beveiligingsonderzoekers om naar kwetsbaarheden in onze systemen te zoeken. We zijn grote voorstanders van responsible disclosure. Neem <a %(a_contact)s>hier</a> contact met ons op . We zijn momenteel niet in staat om bug bounties toe te kennen, behalve voor kwetsbaarheden die het <a %(a_link)s>potentieel hebben om onze anonimiteit in gevaar te brengen</a>. Hiervoor bieden we beloningen aan van tussen de $10k en $50k. We zouden in de toekomst graag een breder scala aan bug bounties willen aanbieden! Houd er rekening mee dat social engineering-aanvallen buiten de scope vallen. Als je geïnteresseerd bent in offensieve beveiliging en wil helpen de kennis en cultuur van de wereld te archiveren, neem dan zeker contact met ons op. Er zijn veel manieren waarop je kunt helpen. Hebben jullie een responsible disclosure beleid? We hebben letterlijk niet genoeg middelen om iedereen ter wereld snelle downloads te bieden, hoe graag we dat ook zouden willen. Als een rijke weldoener ons hiermee zou willen helpen, zou dat geweldig zijn, maar tot die tijd doen we ons best. We zijn een non-profit project die nauwelijks rondkomt van donaties. Daarom hebben we twee systemen voor gratis downloads geïmplementeerd met onze partners: gedeelde servers met trage downloads, en iets snellere servers met een wachtlijst (om het aantal mensen dat tegelijkertijd downloadt te verminderen). We hebben ook <a %(a_verification)s>browser verificatie</a> voor onze trage downloads, omdat anders bots en scrapers er misbruik van zouden maken, waardoor het nog trager wordt voor legitieme gebruikers. Let op dat je bij het gebruik van de Tor Browser mogelijk je beveiligingsinstellingen moet aanpassen. Bij de laagste optie, genaamd “Standaard”, slaagt de Cloudflare turnstile beveiliging. Bij de hogere opties, genaamd “Veiliger” en “Veiligst”, faalt de beveiliging. Voor grote bestanden kunnen trage downloads soms halverwege onderbroken worden. We raden aan om een downloadmanager (zoals JDownloader) te gebruiken om grote downloads automatisch te hervatten. Waarom zijn de langzame downloads zo traag? Veelgestelde Vragen (FAQ) Gebruik de <a %(a_list)s>torrentlijstgenerator</a> om een lijst te genereren van torrents die het meest behoefte hebben aan seeding en passen in de limiet van je opslagruimte. Ja, zie de <a %(a_llm)s>LLM data</a> pagina. De meeste torrents bevatten de bestanden direct, wat betekent dat je torrent-clients kunt instrueren om alleen de benodigde bestanden te downloaden. Om te bepalen welke bestanden je moet downloaden kun je onze <a %(a_generate)s>metadata genereren</a> of onze ElasticSearch- en MariaDB-databases <a %(a_download)s>downloaden</a>. Helaas bevatten een aantal torrent-collecties .zip- of .tar-bestanden in de root, in welk geval je de hele torrent moet downloaden voordat je individuele bestanden kunt selecteren. Er zijn nog geen gebruiksvriendelijke tools beschikbaar voor het filteren van torrents, maar we verwelkomen bijdragen. (We hebben echter <a %(a_ideas)s>enkele ideeën</a> voor het laatste geval.) Lang antwoord: Kort antwoord: niet gemakkelijk. We proberen minimale duplicatie of overlap tussen de torrents in deze lijst te houden, maar dit lukt niet altijd en hangt sterk af van het beleid van de bron bibliotheken. Wij hebben dit niet in de hand voor bibliotheken die hun eigen torrents uitbrengen. Voor torrents die door Anna’s Archief worden uitgebracht, dedupliceren we alleen op basis van MD5-hash, wat betekent dat verschillende versies van hetzelfde boek niet worden gededupliceerd. Ja. Dit zijn wel degelijk PDF's en EPUB's, ze hebben alleen geen extensie in veel van onze torrents. Er zijn twee plaatsen waar je de metadata voor torrent-bestanden kunt vinden, inclusief de bestandstypen/extensies: 1. Elke collectie of release heeft zijn eigen metadata. <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> hebben bijvoorbeeld een bijbehorende metadata-database die wordt gehost op de Libgen.rs-website. We linken meestal naar relevante metadata bronnen vanaf de <a %(a_datasets)s>dataset pagina</a> van elke collectie. 2. We raden aan om onze ElasticSearch- en MariaDB-databases te <a %(a_generate)s>genereren</a> of <a %(a_download)s>downloaden</a>. Deze bevatten een mapping naar de bijbehorende torrent-bestanden voor elk record in Anna’s Archive (indien beschikbaar), onder "torrent_paths" in de ElasticSearch JSON. Sommige torrentclients ondersteunen geen grote stukgroottes, wat veel van onze torrents wel hebben (voor nieuwere doen we dit niet meer — ook al is het volgens de specificaties geldig!). Probeer een andere client als je hier tegenaan loopt, of klaag bij de makers van je torrentclient. Ik wil graag helpen met seeden, maar ik heb niet veel schijfruimte. De torrents zijn te langzaam; kan ik de data direct van jullie downloaden? Kan ik slechts een subset van de bestanden downloaden, zoals alleen een bepaalde taal of onderwerp? Hoe gaan jullie om met duplicaten in de torrents? Kan ik de torrentlijst als JSON krijgen? Ik zie geen PDF's of EPUB's in de torrents, alleen binaire bestanden? Wat moet ik doen? Waarom kan mijn torrentclient sommige van jullie torrentbestanden / magnetlinks niet openen? Torrents FAQ Hoe upload ik nieuwe boeken? Zie <a %(a_href)s>dit uitstekende project</a>. Heb je een uptime-monitor? Wat is Anna's Archief? Word lid voor snelle downloads. We ondersteunen nu Amazon cadeaubonnen, credit- en debetkaarten, crypto, Alipay en WeChat. Je hebt alle snelle downloads van vandaag gebruikt. Toegang Downloads per uur in de afgelopen 30 dagen. Gemiddelde per uur: %(hourly)s. Gemiddelde per dag: %(daily)s. We werken met partners samen om onze verzamelingen gemakkelijk en vrij toegankelijk te maken. We geloven dat iedereen het recht tot de collectieve wijsheid van de mensheid heeft – en <a %(a_search)s>niet ten koste van auteurs</a>. De datasets die worden gebruikt in Anna’s Archive zijn volledig open en kunnen in bulk worden gemirrored met torrents. <a %(a_datasets)s>Meer informatie…</a> Langetermijnarchief Volledige database Zoeken Boeken, papers, tijdschriften, strips, bibliotheekgegevens, metadata, … Al onze <a %(a_code)s>code</a> en <a %(a_datasets)s>data</a> zijn volledig open source. <span %(span_anna)s>Anna’s Archief</span> is een non-profitproject met twee doelen: <li><strong>Behoud:</strong> we slaan de kennis en cultuur van de mensheid op.</li><li><strong>Toegang:</strong> we maken deze kennis en cultuur beschikbaar voor iedereen ter wereld.</li> We hebben 's werelds grootste verzameling tekstgegevens van hoge kwaliteit. <a %(a_llm)s>Meer informatie…</a> Trainingsdata voor LLM's 🪩 Mirrors: oproep voor vrijwilligers Neem contact met ons op als je een risicovolle, anonieme betalingsverwerker draait. We zijn daarnaast op zoek naar mensen die aantrekkelijke kleine advertenties willen plaatsen. Alle opbrengsten gaan naar onze inspanningen voor het behoud. Behoud We schatten dat we ongeveer <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% van alle boeken ter wereld</a> hebben bewaard. We behouden boeken, papers, strips, tijdschriften en meer door deze materialen uit verschillende <a href="https://nl.wikipedia.org/wiki/Schaduwbibliotheek">schaduwbibliotheken</a>, officiële bibliotheken en andere collecties samen te brengen op één plek. Al deze gegevens worden voor altijd bewaard door het makkelijk te maken om ze in bulk te dupliceren – met behulp van torrents – waardoor er overal ter wereld vele kopieën zijn. Sommige schaduwbibliotheken doen dit al zelf (zoals Sci-Hub en Library Genesis), terwijl Anna's Archief andere bibliotheken 'bevrijdt' die geen bulkverspreiding aanbieden (zoals Z-Library) of helemaal geen schaduwbibliotheken zijn (zoals Internet Archive en DuXiu). Deze brede distributie, gecombineerd met de opensourcecode, maakt onze website weerbaar tegen verwijderingen en verzekert het behoud van de kennis en cultuur van de mensheid op de lange termijn. Lees meer over <a href="/datasets">onze datasets</a>. Als je <a %(a_member)s>lid</a> bent, is browserverificatie niet nodig. 🧬&nbsp;SciDB is een voortzetting van Sci-Hub. SciDB Open DOI Sci-Hub heeft de upload van nieuwe papers <a %(a_paused)s>gepauzeerd</a>. Directe toegang tot %(count)s academische papers 🧬&nbsp;SciDB is een voortzetting van Sci-Hub, met de vertrouwde interface en directe weergave van PDF's. Voer je DOI in om te bekijken. Wij hebben de volledige Sci-Hub collectie, maar ook nieuwe papers. De meeste kunnen direct worden bekeken met een vertrouwde interface, die vergelijkbaar is met die van Sci-Hub. Sommige kunnen worden gedownload via externe bronnen. In dat geval tonen wij links naar die bronnen. Je helpt ons enorm door torrents te seeden. <a %(a_torrents)s>Meer informatie…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Op zoek naar vrijwilligers Als een non-profit, open-source project zijn we altijd op zoek naar mensen die willen helpen. IPFS downloads Lijst van %(by)s, gemaakt op <span %(span_time)s>%(time)s</span> Opslaan ❌ Er ging iets mis. Probeer het opnieuw. ✅ Opgeslagen. Vernieuw de pagina. De lijst is leeg. bewerken Voeg dingen aan deze lijst toe of verwijder ze door een bestand te zoeken en het tabblad Lijsten te openen. Lijst Hoe we kunnen helpen Overlap verwijderen (deduplicatie) Tekst- en metadata-extractie OCR We kunnen snelle toegang bieden tot onze volledige collecties, evenals tot niet-uitgebrachte collecties. Dit is toegang op ondernemingsniveau die we kunnen bieden voor donaties in de orde van tienduizenden USD. We zijn ook bereid dit te ruilen voor hoogwaardige collecties die we nog niet hebben. We kunnen je terugbetalen als je ons kunt voorzien van verrijking van onze gegevens, zoals: Ondersteun langdurige archivering van menselijke kennis, terwijl je betere gegevens krijgt voor jouw model! <a %(a_contact)s>Neem contact met ons op</a> om te bespreken hoe we kunnen samenwerken. Het is algemeen bekend dat LLM's gedijen op hoogwaardige gegevens. We hebben de grootste collectie boeken, artikelen, tijdschriften, enz. ter wereld. Deze zijn sommige van de hoogste kwaliteit tekstbronnen. LLM data Unieke schaal en bereik Onze collectie bevat meer dan honderd miljoen bestanden, waaronder wetenschappelijke tijdschriften, boeken en tijdschriften. We bereiken deze hoeveelheid door grote bestaande repositories te combineren. Sommige van onze broncollecties zijn al in bulk beschikbaar (Sci-Hub en delen van Libgen). Andere bronnen hebben we zelf bevrijd. <a %(a_datasets)s>Datasets</a> toont een volledig overzicht. Onze collectie omvat miljoenen boeken, artikelen en tijdschriften van vóór het e-boek tijdperk. Grote delen van deze collectie zijn al omgezet doormiddel van OCR en hebben al van zichzelf weinig interne overlap. Doorgaan <a %(a_contact)s>Neem contact met ons op</a> als je je sleutel bent verloren en geef daarbij zo veel mogelijk informatie door. Om contact op te nemen, moet je mogelijk tijdelijk een nieuw account maken. <a %(a_account)s>Log in</a> om deze pagina weer te geven.</a> Om te voorkomen dat spambots veel accounts maken, moeten we je browser eerst verifiëren. Als je in een oneindige lus vastloopt, raden we aan om <a %(a_privacypass)s>Privacy Pass</a> te installeren. Het kan ook helpen om advertentieblokkers en andere browserextensies uit te schakelen. Inloggen/registreren Anna’s Archive is tijdelijk offline voor onderhoud. Kom over een uur terug. Alternatieve auteur Alternatieve beschrijving Alternatieve editie Alternatieve extensie Alternatieve bestandsnaam Alternatieve uitgever Alternatieve titel opensourcedatum Meer informatie… beschrijving Anna's Archief doorzoeken op CADAL SSNO-nummer Anna's Archief doorzoeken op DuXiu SSID-nummer Anna's Archief doorzoeken op DuXiu DXID-nummer Anna’s Archief doorzoeken op ISBN Anna’s Archief doorzoeken op OCLC-nummer (WorldCat) Anna’s Archief doorzoeken op Open Library ID Anna’s Archief online viewer %(count)s getroffen pagina's Na het downloaden: Er is mogelijk een betere versie van dit bestand beschikbaar op %(link)s Torrentdownloads in bulk collectie Gebruik online tools om tussen formaten te converteren. Aanbevolen conversietools: %(links)s Voor grote bestanden raden we aan een downloadmanager te gebruiken om onderbrekingen te voorkomen. Aanbevolen downloadmanagers: %(links)s EBSCOhost eBook Index (alleen voor experts) (klik ook op "GET" bovenaan de pagina) (klik op "GET" bovenaan de pagina) Externe downloads Je hebt vandaag nog %(remaining)s over. Bedankt dat je lid bent!❤️ Je hebt alle snelle downloads van vandaag gebruikt. Je hebt dit bestand onlangs gedownload. Links blijven een tijdje geldig. Word <a %(a_membership)s>lid</a> om het langdurige behoud van boeken, papers en meer te ondersteunen. Als dank daarvoor krijg je snelle downloads. ❤️ 🚀 Snelle downloads 🐢 Langzame downloads Lenen uit het internetarchief IPFS-gateway #%(num)d (mogelijk moet je het een paar keer proberen met IPFS) Libgen.li Libgen.rs Fictie Libgen.rs Non-Fictie hun advertenties staan erom bekend kwaadaardige software te bevatten, dus gebruik een adblocker of klik niet op advertenties Amazon’s “Send to Kindle” djazz’s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC-bestanden kunnen onbetrouwbaar zijn om te downloaden) Geen downloads gevonden. Alle download opties zouden veilig moeten zijn. Dat gezegd hebbende: wees altijd voorzichtig met het downloaden van bestanden van het internet. Zorg bijvoorbeeld altijd dat je apparaat geüpdatet is. (geen omleiding) Open in onze viewer (open in viewer) Optie #%(num)d: %(link)s %(extra)s Originele vermelding in CADAL zoeken Handmatig zoeken op DuXiu Originele vermelding in ISBNdb zoeken Originele vermelding in WorldCat zoeken Originele vermelding in Open Library zoeken Verschillende andere databases doorzoeken op ISBN (alleen voor gebruikers met leesbeperkingen) PubMed Je hebt een e-boek- of PDF-lezer nodig om het bestand te openen, afhankelijk van het bestandsformaat. Aanbevolen e-boeklezers: %(links)s Anna’s Archief 🧬 SciDB Sci-Hub: %(doi)s (bijbehorende DOI is mogelijk niet beschikbaar in Sci-Hub) Je kunt zowel PDF- als EPUB-bestanden naar je Kindle of Kobo e-reader sturen. Aanbevolen tools: %(links)s Meer informatie in de <a %(a_slow)s>FAQ</a>. Steun auteurs en bibliotheken Als je dit leuk vindt en het je kunt veroorloven, overweeg dan om het origineel te kopen of de auteurs direct te steunen. Als dit beschikbaar is in jouw lokale bibliotheek, overweeg dan om het daar gratis te lenen. Downloads van partnerservers zijn momenteel niet beschikbaar voor dit bestand. torrent Van vertrouwde partners. Z-Library Z-Library op Tor (vereist de Tor Browser) toon externe downloads <span class="font-bold">❌Dit bestand heeft mogelijk problemen en is verborgen in de bron bibliotheek.</span> Soms is dit vanwege een verzoek op basis van auteursrecht, soms is dit omdat er een beter alternatief beschikbaar is, maar het kan ook liggen aan een probleem met het bestand zelf. Het bestand kan mogelijk wel worden gedownload, maar we raden aan eerst een alternatief bestand te zoeken. Meer details: Als je dit bestand nog steeds wilt downloaden, zorg dan dat je enkel betrouwbare en geüpdatete software gebruikt om het te openen. metadata-opmerkingen AA: Anna's Archief doorzoeken op “%(name)s” Code Verkenner: Bekijk in Codes Explorer “%(name)s” URL: Website: Als je dit bestand hebt, maar het nog niet in Anna's Archief beschikbaar is, overweeg dan om het te <a %(a_request)s>uploaden</a>. Controlled Digital Lending-bestand van Internet Archive “%(id)s” Dit is een vermelding van een bestand van Internet Archive, geen direct downloadbaar bestand. Je kunt het boek proberen te lenen (link hieronder) of deze URL gebruiken wanneer je <a %(a_request)s>een bestand aanvraagt</a>. Metadata verbeteren Metadatavermelding CADAL SSNO %(id)s Dit is een metadatavermelding, geen downloadbaar bestand. Je kunt deze URL gebruiken wanneer je <a %(a_request)s>een bestand aanvraagt</a>. Metadatavermelding DuXiu SSID %(id)s Metadatavermelding ISBNdb %(id)s MagzDB ID %(id)s metadata record Nexus/STC ID %(id)s metadata record Metadatavermelding OCLC (WorldCat) %(id)s Metadatavermelding Open Library %(id)s Sci-Hub-bestand “%(id)s” Niet gevonden “%(md5_input)s” is niet gevonden in onze database. Voeg een opmerking toe (%(count)s) Je kunt de md5 uit de URL halen, bijvoorbeeld MD5 van een betere versie van dit bestand (indien van toepassing). Vul dit in als er een ander bestand is dat nauw overeenkomt met dit bestand (dezelfde editie, dezelfde bestandsextensie als je er een kunt vinden), die mensen in plaats van dit bestand zouden moeten gebruiken. Als je een betere versie van dit bestand buiten Anna’s Archive kent, upload deze dan <a %(a_upload)s>hier</a>. Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw. U heeft een reactie achtergelaten. Het kan een minuut duren voordat deze zichtbaar is. Gebruik alstublieft het <a %(a_copyright)s>DMCA / Auteursrechtenclaim formulier</a>. Beschrijf het probleem (verplicht) Als dit bestand van hoge kwaliteit is, kun je hier alles er over bespreken! Zo niet, gebruik dan de knop “Bestandsprobleem melden”. Uitstekende bestandskwaliteit (%(count)s) Bestandskwaliteit Leer hoe je zelf de <a %(a_metadata)s>metadata voor dit bestand kunt verbeteren</a>. Probleembeschrijving Gelieve <a %(a_login)s>in te loggen</a>. Ik vond dit boek geweldig! Help de community door de kwaliteit van dit bestand te rapporteren! 🙌 Er is iets misgegaan. Herlaad de pagina en probeer het opnieuw. Rapporteer bestandsprobleem (%(count)s) Bedankt voor het indienen van je rapport. Het zal op deze pagina worden weergegeven en handmatig worden beoordeeld door Anna (totdat we een goed moderatiesysteem hebben). Laat een reactie achter Rapport indienen Wat is er mis met dit bestand? Lenen (%(count)s) Reacties (%(count)s) Downloads (%(count)s) Metadata verkennen (%(count)s) Lijsten (%(count)s) Statistieken (%(count)s) Voor informatie over dit specifieke bestand, zie het <a %(a_href)s>JSON-bestand</a> dat erbij hoort. Dit is een bestand dat beheerd wordt door de <a %(a_ia)s>IA’s Controlled Digital Lending</a> bibliotheek, en wordt geïndexeerd door Anna’s Archive voor zoekopdrachten. Voor informatie over de verschillende datasets die we hebben samengesteld, zie de <a %(a_datasets)s>Datasets pagina</a>. Metadata van gekoppeld record Verbeter metadata op Open Library Een “bestand MD5” is een hash die wordt berekend op basis van de inhoud van het bestand en redelijk uniek is op basis van die inhoud. Alle schaduwbibliotheken die we hier hebben geïndexeerd, gebruiken voornamelijk MD5's om bestanden te identificeren. Een bestand kan in meerdere schaduwbibliotheken voorkomen. Voor informatie over de verschillende datasets die we hebben samengesteld, zie de <a %(a_datasets)s>Datasets pagina</a>. Rapporteer bestandskwaliteit Totaal aantal downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tsjechische metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Boeken %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Waarschuwing: meerdere gekoppelde records: Wanneer je een boek bekijkt op Anna’s Archive, kun je verschillende velden zien: titel, auteur, uitgever, editie, jaar, beschrijving, bestandsnaam, en meer. Al deze stukjes informatie worden <em>metadata</em> genoemd. Aangezien we boeken combineren uit verschillende <em>bronbibliotheken</em>, tonen we alle metadata die beschikbaar is in die bronbibliotheek. Voor een boek dat we hebben verkregen uit Library Genesis, tonen we bijvoorbeeld de titel uit de database van Library Genesis. Soms is een boek aanwezig in <em>meerdere</em> bronbibliotheken, die mogelijk verschillende metadata velden hebben. In dat geval tonen we simpelweg de langste versie van elk veld, omdat die hopelijk de meest bruikbare informatie bevat! We tonen de andere velden nog steeds onder de beschrijving, bijvoorbeeld als ”alternatieve titel” (maar alleen als ze verschillend zijn). We halen ook <em>codes</em> zoals ID's en classificaties uit de bronbibliotheek. <em>Identificatoren</em> vertegenwoordigen een specifieke editie van een boek; voorbeelden zijn ISBN, DOI, Open Library ID, Google Books ID of Amazon ID. <em>Classificaties</em> groeperen meerdere vergelijkbare boeken; voorbeelden zijn Dewey Decimal (DCC), UDC, LCC, RVK of GOST. Soms zijn deze codes expliciet gekoppeld in bronbibliotheken, en soms kunnen we ze uit de bestandsnaam of beschrijving halen (voornamelijk ISBN en DOI). We kunnen ID's gebruiken om records te vinden in <em>metadata-only collecties</em>, zoals OpenLibrary, ISBNdb of WorldCat/OCLC. Er is een specifieke <em>metadata-tab</em> in onze zoekmachine als u die collecties wilt doorzoeken. We gebruiken overeenkomende records om ontbrekende metadata-velden in te vullen (bijvoorbeeld als een titel ontbreekt), of bijvoorbeeld als “alternatieve titel” (als er een bestaande titel is). Kijk om precies te zien waar de metadata van een boek vandaan komt naar het <em>“Technische details” tabblad</em> op een boekpagina. Het bevat een link naar de ruwe JSON voor dat boek, met verwijzingen naar de ruwe JSON van de originele records. Voor meer informatie, zie de volgende pagina’s: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Zoeken (metadata-tab)</a>, <a %(a_codes)s>Codes Explorer</a>, en <a %(a_example)s>Voorbeeld metadata JSON</a>. Ten slotte kan al onze metadata <a %(a_generated)s>gegenereerd</a> of <a %(a_downloaded)s>gedownload</a> worden als ElasticSearch- en MariaDB-databases. Achtergrond Je kunt helpen bij het behoud van boeken door de metadata te verbeteren! Lees eerst de achtergrondinformatie over metadata op Anna’s Archive en leer vervolgens hoe je metadata kunt verbeteren door te linken met Open Library, en verdien gratis lidmaatschap op Anna’s Archive. Verbeter metadata Dus als je een bestand met slechte metadata tegenkomt, hoe moet je dat dan repareren? Je kunt naar de bronbibliotheek gaan en de procedures volgen voor het repareren van metadata, maar wat moet je doen als een bestand aanwezig is in meerdere bronnenbibliotheken? Er is één ID die speciaal wordt behandeld op Anna’s Archive. <strong>Het annas_archive md5-veld op Open Library overschrijft altijd alle andere metadata!</strong> Laten we eerst even terugspoelen en meer leren over Open Library. Open Library werd in 2006 opgericht door Aaron Swartz met als doel “één webpagina voor elk boek ooit gepubliceerd”. Het is een soort Wikipedia voor metadata van boeken: iedereen kan het bewerken, het is vrij gelicenseerd en kan in bulk worden gedownload. Het is een boekendatabase die het meest in lijn is met onze missie — in feite is Anna’s Archive geïnspireerd door Aaron Swartz’ visie en leven. In plaats van het wiel opnieuw uit te vinden, hebben we besloten onze vrijwilligers door te verwijzen naar Open Library. Als je een boek ziet met onjuiste metadata, dan kun je op de volgende manier helpen: Let op dat dit alleen werkt voor boeken, niet voor academische papers of andere soorten bestanden. Voor andere soorten bestanden raden we nog steeds aan om de bronbibliotheek te zoeken. Het kan enkele weken duren voordat wijzigingen zijn opgenomen in Anna’s Archive, omdat we de nieuwste data dump van Open Library moeten downloaden en onze zoekindex opnieuw moeten genereren.  Ga naar de <a %(a_openlib)s>Open Library website</a>. Vind het juiste boekrecord. <strong>WAARSCHUWING:</strong> zorg ervoor dat je de juiste <strong>editie</strong> selecteert. In Open Library heb je “works” en “editions. Een “work” kan “Harry Potter en de Steen der Wijzen” zijn. Een “edition” kan zijn: De eerste editie uit 1997, uitgegeven door Bloomsbery, met 256 pagina’s. De paperback editie uit 2003, uitgegeven door Raincoast Books, met 223 pagina’s. De Poolse vertaling uit 2000, “Harry Potter I Kamie Filozoficzn” door Media Rodzina, met 328 pagina’s. Al deze edities hebben verschillende ISBN’s en verschillende inhoud, dus zorg ervoor dat je de juiste kiest! Bewerk het record (of maak deze aan als hij niet bestaat) en voeg zoveel nuttige informatie toe als je kunt! Je bent er nu toch, dus kun je er net zo goed voor zorgen dat het record fantastisch is. Selecteer onder “ID Numbers” “Anna’s Archive” en voeg de MD5 van het boek toe van Anna’s Archive. Dit is de lange reeks letters en cijfers na “/md5/” in de URL. Probeer andere bestanden te vinden in Anna’s Archive die ook bij dit record passen, en voeg die ook toe. In de toekomst kunnen we deze groeperen als duplicaten op de zoekpagina van Anna’s Archive. Als je klaar bent, schrijf dan de URL op die je zojuist hebt bijgewerkt. Zodra je minstens 30 records hebt bijgewerkt met Anna’s Archive MD5’s, stuur ons dan een <a %(a_contact)s>e-mail</a> en stuur de lijst. We geven je een gratis lidmaatschap voor Anna’s Archive, zodat je dit werk gemakkelijker kunt doen (en als dank voor je hulp). Dit moeten hoogwaardige bewerkingen zijn die aanzienlijke hoeveelheden informatie toevoegen, anders wordt je verzoek afgewezen. Je verzoek wordt ook afgewezen als een van de bewerkingen wordt teruggedraaid of gecorrigeerd door Open Library-moderators. Open Library koppeling Als je jezelf significant bezighoudt met de ontwikkeling en operaties van ons werk, kunnen we bespreken om meer van de donatie-inkomsten met je te delen, zodat je deze naar behoefte kunt inzetten. We betalen alleen voor hosting zodra je alles hebt opgezet en hebt aangetoond dat je in staat bent het archief up-to-date te houden met updates. Dit betekent dat je de eerste 1-2 maanden zelf moet betalen. Je tijd wordt niet vergoed (en die van ons ook niet), aangezien dit puur vrijwilligerswerk is. We zijn bereid om de kosten voor hosting en VPN te dekken, aanvankelijk tot $200 per maand. Dit is voldoende voor een basis zoekserver en een DMCA-beschermde proxy. Hostingkosten Neem alsjeblieft <strong>>geen contact met ons op</strong> om toestemming te vragen of voor basisvragen. Daden spreken luider dan woorden! Alle informatie is beschikbaar, dus ga gewoon aan de slag met het opzetten van jouw mirror. Voel je wel vrij om tickets of merge-requests te plaatsen op onze Gitlab wanneer je tegen problemen aanloopt. We moeten mogelijk enkele mirror-specifieke functies met je bouwen, zoals het rebranden van “Anna’s Archive” naar jouw website naam, (aanvankelijk) het uitschakelen van gebruikersaccounts, of het linken naar onze hoofdpagina vanaf boekpagina’s. Zodra je jouw mirror draaiende hebt, neem dan alsjeblieft contact met ons op. We zouden graag je OpSec beoordelen, en zodra dat solide is, zullen we naar jouw mirror linken en nauwer met je samenwerken. Bij voorbaat dank aan iedereen die op deze manier wil bijdragen! Het is niet voor de mensen met een zwak hart, maar het zou de levensduur van de grootste écht open bibliotheek in de menselijke geschiedenis versterken. Aan de slag Om de veerkracht van Anna’s Archive te vergroten, zijn we op zoek naar vrijwilligers om mirrors te runnen. Jouw versie is duidelijk te onderscheiden als een mirror, bijvoorbeeld “Bob’s Archief, een Anna’s Archive mirror”. Je bent bereid de aanzienlijke risico’s te nemen die met dit werk gepaard gaan. Je hebt een diep begrip van de vereiste operationele beveiliging. De inhoud van <a %(a_shadow)s>deze</a> <a %(a_pirate)s>berichten</a> is voor jou vanzelfsprekend. In eerste instantie zullen we je geen toegang geven tot de downloads van onze partner server, maar als alles goed gaat, kunnen we dat met je delen. Je beheert de open source codebase van Anna’s Archive en je werkt regelmatig zowel de code als de data bij. Je bent bereid bij te dragen aan onze <a %(a_codebase)s>codebase</a> — in samenwerking met ons team — om dit te realiseren. We zijn op zoek naar het volgende: Mirrors: oproep voor vrijwilligers Nog een donatie doen. Nog geen donaties. <a %(a_donate)s>Ik wil mijn eerste donatie doen.</a> Donatiegegevens zijn niet openbaar zichtbaar. Mijn donaties 📡 Bekijk de pagina's <a %(a_datasets)s>Datasets</a> en <a %(a_torrents)s>Torrents</a> voor het spiegelen van onze collectie in bulk. Downloads vanaf jouw IP-adres in de afgelopen 24 uur: %(count)s. 🚀 <a %(a_membership)s>Word lid</a> om snellere downloads te krijgen en browsercontroles over te slaan. Download van partnerwebsite Doorzoek Anna’s Archive in een ander tabblad terwijl je wacht (als uw browser het verversen van achtergrondtabbladen ondersteunt). Voel je vrij om op het laden van meerdere downloadpagina's tegelijk te wachten (maar download alsjeblieft maar één bestand tegelijk per server). Zodra je een downloadlink ontvangt, is deze enkele uren geldig. Bedankt voor het wachten, dit houdt de website gratis toegankelijk voor iedereen! 😊 <a %(a_main)s>&lt; Alle downloadlinks voor dit bestand</a> ❌ Trage downloads zijn niet beschikbaar via VPN's van Cloudflare of vanaf IP-adressen van Cloudflare. ❌ Trage downloads zijn alleen beschikbaar via de officiële website. Bezoek %(websites)s. <a %(a_download)s>📚 Download nu</a> Om iedereen de kans te geven bestanden gratis te downloaden, moet je wachten voordat je dit bestand kunt downloaden. Wacht alsjeblieft <span %(span_countdown)s>%(wait_seconds)s</span> seconden om dit bestand te downloaden. Waarschuwing: In de afgelopen 24 uur zijn er veel downloads vanaf jouw IP-adres gedaan. Downloads kunnen trager zijn dan gebruikelijk. Deze waarschuwing kan komen doordat je een VPN of gedeelde internetverbinding gebruikt, of als je internetprovider IP's deelt. Opslaan ❌ Er ging iets mis. Probeer het opnieuw. ✅ Opgeslagen. Vernieuw de pagina. Wijzig je weergavenaam. Je identificatiecode (het deel na #) kan niet worden gewijzigd. Profiel gemaakt: <span %(span_time)s>%(time)s</span> bewerken Lijsten Maak een nieuwe lijst door een titel te zoeken en het tabblad Lijsten te openen. Nog geen lijsten Profiel niet gevonden. Profiel Op dit moment kunnen we boekaanvragen niet inwilligen. Mail ons je boekaanvragen niet. Doe je aanvragen op de fora van Z-Library of Libgen. Vermelding in Anna’s Archief DOI: %(doi)s Download SciDB Nexus/STC Nog geen voorbeeld beschikbaar. Download het bestand van <a %(a_path)s>Anna’s Archive</a>. Word <a %(a_donate)s>lid</a> om de toegankelijkheid en het langdurige behoud van menselijke kennis te ondersteunen. Als bonus laadt 🧬&nbsp;SciDB sneller voor leden, zonder enige limieten. Werkt het niet? Probeer te <a %(a_refresh)s>vernieuwen</a>. Sci-Hub Specifiek zoekveld toevoegen Beschrijvingen en metadata-opmerkingen doorzoeken Publicatiejaar Geavanceerd Toegang Inhoud Weergeven Lijst Tabel Bestandstype Taal Sorteren op Grootste Meest relevant Nieuwste (bestandsgrootte) (open source) (publicatiejaar) Oudste Willekeurig Kleinste Bron gescrapet en open source door AA Digitaal lenen (%(count)s) Wetenschappelijke artikelen (%(count)s) We hebben overeenkomsten gevonden in %(in)s. Je kunt verwijzen naar de URL daar als je <a %(a_request)s>een bestand aanvraagt</a>. Metadata (%(count)s) Gebruik de <a %(a_href)s Codes Explorer</a> om de zoekindex te doorzoeken op codes. De zoekindex wordt iedere maand geüpdatet. Momenteel bevat de index invoergegevens tot %(last_data_refresh_date)s. Voor meer technische informatie, ga naar de %(link_open_tag)sdatasets pagina</a>. Uitsluiten Alleen opnemen Niet gecontroleerd meer… Volgende … Vorige Deze zoekindex bevat momenteel metadata van de Controlled Digital Lending-bibliotheek van Internet Archive. <a %(a_datasets)s>Meer informatie over onze datasets</a>. Bekijk <a %(a_wikipedia)s>Wikipedia</a> en <a %(a_mobileread)s>MobileRead Wiki</a> voor meer bibliotheken die digitaal uitlenen. <a %(a_copyright)s>Klik hier</a> voor DMCA-/auteursrechtclaims. Downloadtijd Fout bij de zoekopdracht. Probeer de pagina te <a %(a_reload)s>herladen</a>. Stuur een e-mail naar %(email)s als het probleem zich blijft voordoen. Snelle download In feite kan iedereen helpen bij het behoud van deze bestanden door onze <a %(a_torrents)s>gebundelde torrentlijst</a> te seeden. ➡️ Soms gebeurt dit onjuist wanneer de zoekserver traag is. In dergelijke gevallen kan <a %(a_attrs)s>herladen</a> helpen. ❌ Dit bestand heeft mogelijk problemen. Op zoek naar papers? Deze zoekindex bevat momenteel metadata van verschillende bronnen. <a %(a_datasets)s>Meer informatie over onze datasets</a>. Er zijn vele bronnen met metadata voor geschreven werken van over de hele wereld. <a %(a_wikipedia)s>Deze Wikipedia-pagina</a> is een goed begin, maar als je andere goede lijsten kent, laat het ons dan vooral weten. We tonen de originele velden met metadata. We combineren deze niet. Momenteel hebben we 's werelds meest uitgebreide open catalogus van boeken, papers en andere geschreven werken. We spiegelen Sci-Hub, Library Genesis, Z-Library <a %(a_datasets)s>en meer</a>. <span %(classname)s>Geen bestanden gevonden.</span> Probeer het met minder zoektermen en filters. Resultaten %(from)s-%(to)s (%(total)s totaal) Als je andere 'schaduwbibliotheken' vindt die we moeten spiegelen of vragen hebt, kun je contact met ons opnemen via %(email)s. %(num)d gedeeltelijke overeenkomsten %(num)d+ gedeeltelijke overeenkomsten Typ in het veld om bestanden in bibliotheken die digitaal uitlenen te zoeken. Typ in het veld om onze catalogus van %(count)s direct downloadbare bestanden te doorzoeken, die we <a %(a_preserve)s>voor altijd behouden</a>. Typ in het veld om te zoeken. Typ in het veld om onze catalogus van %(count)s wetenschappelijke artikelen te doorzoeken, die we voor altijd <a %(a_preserve)s>bewaren</a>. Typ in het veld om naar metadata van bibliotheken te zoeken. Dit kan handig zijn bij het <a %(a_request)s>aanvragen van een bestand</a>. Tip: Gebruik de sneltoetsen “/” (focus op zoeken), “Enter” (zoeken), “j” (omhoog) en “k” (omlaag) om sneller te navigeren. Dit zijn metadata records, <span %(classname)s>geen</span> downloadbare bestanden. Zoekinstellingen Zoeken Digitaal lenen Download Wetenschappelijke artikelen Metadata Nieuwe zoekopdracht %(search_input)s - Zoeken De zoekopdracht duurde te lang. Dit kan betekenen dat je onnauwkeurige resultaten te zien krijgt. Soms helpt het om de pagina te <a %(a_reload)s>herladen</a>. De zoekopdracht duurde te lang. Dit is gebruikelijk bij brede opdrachten. De filteraantallen kunnen onnauwkeurig zijn. Neem contact met ons op via %(a_email)s bij grote uploads (van meer dan 10.000 bestanden) die niet door Libgen of Z-Library worden geaccepteerd. Voor Libgen.li, zorg ervoor dat u eerst inlogt op <a %(a_forum)s >hun forum</a> met gebruikersnaam %(username)s en wachtwoord %(password)s, en keer dan terug naar hun <a %(a_upload_page)s >uploadpagina</a>. Voorlopig raden we je aan om nieuwe boeken op de forks van Library Genesis te uploaden. Hier vind je een <a %(a_guide)s>handleiding</a>. Beide forks die we op deze website indexeren, halen hun gegevens van ditzelfde uploadsysteem. Voor kleine uploads (tot 10.000 bestanden) upload ze alstublieft naar zowel %(first)s als %(second)s. Als alternatief kun je ze <a %(a_upload)s>hier</a> naar Z-Library uploaden . Upload academische papers naast Library Genesis ook naar <a %(a_stc_nexus)s>STC Nexus</a> . Zij zijn de beste schaduw-bibliotheek voor nieuwe papers. We hebben ze nog niet geïntegreerd, maar dat zal uiteindelijk wel gebeuren. Je kunt hun<a %(a_telegram)s>upload bot op Telegram</a> gebruiken, of contact opnemen met het adres dat in hun gepinde bericht staat als je te veel bestanden hebt om op deze manier te uploaden. <span %(label)s>Zwaar vrijwilligerswerk (USD$50-USD$5,000 beloningen):</span> als je veel tijd en/of middelen aan onze missie kunt besteden, werken we graag nauwer met je samen. Uiteindelijk kun je lid worden van het kernteam. Hoewel we een krap budget hebben, kunnen we <span %(bold)s>💰 monetaire beloningen</span> toekennen voor het meest intensieve werk. <span %(label)s>Licht vrijwilligerswerk:</span> als je slechts een paar uur hier en daar kunt besteden, zijn er nog steeds genoeg manieren waarop je kunt helpen. We belonen consistente vrijwilligers met <span %(bold)s>🤝 lidmaatschappen van Anna’s Archive</span>. Anna’s Archive is afhankelijk van vrijwilligers zoals jij. We verwelkomen alle betrokkenheidsniveaus en hebben twee hoofdcategorieën van hulp die we zoeken: Als je geen tijd kunt vrijmaken om vrijwilligerswerk te doen, kunt je ons nog steeds enorm helpen door <a %(a_donate)s>geld te doneren</a>, <a %(a_torrents)s>onze torrents te seeden</a>, <a %(a_uploading)s>boeken te uploaden</a>, of <a %(a_help)s>je vrienden te vertellen over Anna’s Archive</a>. <span %(bold)s>Bedrijven:</span> we bieden directe toegang met hoge snelheid tot onze collecties aan in ruil voor een donatie op ondernemingsniveau of in ruil voor nieuwe collecties (bijv. nieuwe scans, datasets die met OCR zijn omgezet, verrijking van onze data). <a %(a_contact)s>Neem contact met ons op</a> als dit je aanspreekt. Zie ook onze <a %(a_llm)s>LLM-pagina</a>. Beloningen We zijn altijd op zoek naar mensen met solide programmeer- of offensieve beveiligingsvaardigheden om betrokken te raken. Je kunt een serieuze bijdrage leveren aan het behouden van het erfgoed van de mensheid. Als dank geven we lidmaatschappen weg voor solide bijdragen. Als grote dank geven we monetaire beloningen voor bijzonder belangrijke en moeilijke taken. Dit moet niet worden gezien als een vervanging voor een baan, maar het is een extra stimulans en kan helpen met gemaakte kosten. Het merendeel van onze code is open source, en we zullen dat ook van jouw code vragen bij het toekennen van de beloning. Er zijn enkele uitzonderingen die we op individuele basis kunnen bespreken. Beloningen worden toegekend aan de eerste persoon die een taak voltooit. Voel je vrij om een opmerking te plaatsen bij een beloningsticket om anderen te laten weten dat je ergens aan werkt, zodat anderen kunnen wachten of contact met je kunnen opnemen om samen te werken. Maar wees je ervan bewust dat anderen nog steeds vrij zijn om eraan te werken en proberen je voor te zijn. We kennen echter geen beloningen toe voor slordig werk. Als twee hoogwaardige inzendingen dicht bij elkaar worden gedaan (binnen een dag of twee), kunnen we ervoor kiezen om beloningen aan beide toe te kennen, naar eigen goeddunken, bijvoorbeeld 100%% voor de eerste inzending en 50%% voor de tweede inzending (dus 150%% in totaal). Voor de grotere beloningen (vooral beloningen voor scraping), neem contact met ons op wanneer je ongeveer ~5%% ervan hebt voltooid en je ervan overtuigd bent dat je methode schaalbaar is naar de volledige mijlpaal. Je zult je methode met ons moeten delen zodat we feedback kunnen geven. Op deze manier kunnen we ook beslissen wat te doen als er meerdere mensen dicht bij een beloning komen, zoals mogelijk toekennen aan meerdere mensen, mensen aanmoedigen om samen te werken, etc. WAARSCHUWING: de taken met hoge beloningen zijn <span %(bold)s>moeilijk</span> — het kan verstandig zijn om met gemakkelijkere te beginnen. Ga naar onze <a %(a_gitlab)s>Gitlab issues lijst</a> en sorteer op “Label priority”. Dit toont ongeveer de volgorde van taken die voor ons belangrijk zijn. Taken zonder expliciete beloningen komen nog steeds in aanmerking voor lidmaatschap, vooral die gemarkeerd als “Accepted” en “Anna’s favorite”. Je zou misschien het beste kunnen beginnen met een “Starter project”. Licht vrijwilligerswerk We hebben nu ook een gesynchroniseerd Matrix-kanaal op %(matrix)s. Als je een paar uur over hebt, kun je op verschillende manieren helpen. Zorg ervoor dat je lid wordt van de <a %(a_telegram)s>vrijwilligerschat op Telegram</a>. Als blijk van waardering geven we meestal 6 maanden “Gelukkige Bibliothecaris” voor basis mijlpalen, en meer voor voortgezet vrijwilligerswerk. Alle mijlpalen vereisen werk van hoge kwaliteit — slordig werk schaadt ons meer dan het helpt en we zullen het afwijzen. Stuur ons een <a %(a_contact)s>e-mail</a> wanneer je een mijlpaal bereikt. %(links)s links of screenshots van verzoeken die je hebt vervuld. Boek (of paper, etc.) verzoeken vervullen op de Z-Library of de Library Genesis forums. We hebben geen eigen verzoeksysteem voor boeken, maar we mirroren die bibliotheken, dus het verbeteren die bibliotheken maakt Anna’s Archive ook beter. Mijlpaal Taak Afhankelijk van de taak. Kleine taken gepost in onze <a %(a_telegram)s>vrijwilligerschat op Telegram</a>. Meestal voor lidmaatschap, soms voor kleine beloningen. Kleine taken gepost in onze vrijwilligerschatgroep. Zorg ervoor dat je een opmerking achterlaat bij problemen die je oplost, zodat anderen je werk niet dupliceren. %(links)s links van records die je verbetert hebt. Je kunt de <a %(a_list)s >lijst van willekeurige metadata-problemen</a> gebruiken als startpunt. Verbeter metadata door <a %(a_metadata)s>te linken</a> met Open Library. Deze zouden moeten laten zien dat je iemand vertelt over Anna’s Archief, en dat zij je bedanken. %(links)s links of screenshots. Het woord van Anna’s Archief verspreiden. Bijvoorbeeld door boeken aan te bevelen op AA, te linken naar onze blogposts, of mensen in het algemeen naar onze website te leiden. Volledig vertalen van een taal (als deze nog niet bijna voltooid was.) <a %(a_translate)s>Vertalen</a> van de website. Link naar de bewerkingsgeschiedenis die laat zien dat je significante bijdragen hebt geleverd. Verbeter de Wikipedia-pagina voor Anna’s Archief in jouw taal. Voeg informatie toe van de Wikipedia-pagina van AA in andere talen, en van onze website en blog. Voeg verwijzingen naar AA toe op andere relevante pagina’s. Vrijwilligerswerk & Premies 