#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Ungültige Anfrage. Besöök %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "Libgen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " un "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "un mehr"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Wi spegeln %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Wi schrapen un maken open-source %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "All uns Kood un Daten sünd heel open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;De gröttste echt open Bibliothek in de Minschheit."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;Böker, %(paper_count)s&nbsp;Papers — för immer bewohren."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;De gröttste open-source open-data Bibliothek vun de Welt. ⭐️&nbsp;Spegelt Sci-Hub, Library Genesis, Z-Library, un mehr. 📈&nbsp;%(book_any)s Böker, %(journal_article)s Papers, %(book_comic)s Comics, %(magazine)s Tiedschriften — för immer bewohren."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 De gröttste open-source open-data Bibliothek vun de Welt.<br>⭐️ Spegelt Scihub, Libgen, Zlib, un mehr."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Falsche Metadaten (z.B. Titel, Beschrievung, Omslagbild)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Download-Problemen (z.B. keen Verbinnen, Fehlermeldung, heel langsam)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Datei kann nich öppent warrn (z.B. korrupte Datei, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Schlechte Qualität (z.B. Formatierungsprobleme, schlechte Scanqualität, fehlende Seiten)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / Datei sollte entfernt werden (z.B. Werbung, missbräuchlicher Inhalt)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Urheberrechtsanspruch"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Anderes"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonus-Downloads"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brillanter Bücherwurm"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Glücklicher Bibliothekar"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Funkelnder Datensammler"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Erstaunlicher Archivar"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s insgesamt"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) insgesamt"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s Bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "unbezahlt"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "bezahlt"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "storniert"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "abgelaufen"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "wartet auf Bestätigung von Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "ungültig"

#, fuzzy
msgid "page.donate.title"
msgstr "Spenden"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Du hest en <a %(a_donation)s>lööpende Spende</a>. Bitte schließ oder annullier disse Spende, bevör du en nee Spende maakt."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>All mine Spenden ankieken</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Annas Archiv is en gemeennützigen, open-source, open-data Projekt. Mit dien Spende un Mitgliedschaft ünnerstüttst du uns Operations un Utwikkeln. An all uns Mitgliedern: Dank för dat du uns ünnerstüttst! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "För mehr Informatschoonen, kiek in de <a %(a_donate)s>Spenden-FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "För noch mehr Downloads, <a %(a_refer)s>verwijr dien Frünnen</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Du kriegst %(percentage)s%% bonus schnelle Downloads, weil du von Bruker %(profile_link)s verwiesen wurrst."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Dit gellt för de ganze Mitgliedschaftsperiod."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s schnelle Downloads pro Dag"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "wenn Se disse Maand spenden!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / Maand"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Mitglied warrn"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Utwählt"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "bis to %(percentage)s%% Rabatt"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB Papers <strong>unbegrenzt</strong> ohne Verifikatschoon"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a>-Toegang"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Verdiin <strong>%(percentage)s%% bonus Downloads</strong> dör <a %(a_refer)s>Frünnen verwiesen</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Dien Brukernaam oder anonymen Nennung in de Credits"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Vörige Vorteele, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Frühen Toegang to nee Features"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Exklusiven Telegram mit Updates achter de Kulissen"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "„Adopt een Torrent“: dien Brukernaam oder Botschaft in een Torrent-Dateinaam <div %(div_months)s>eenmaal elke 12 Maand Mitgliedschaft</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legendären Status in’t Bewahren vun de Minschheit ehr Wissen un Kultur"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Expertentilgang"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontaktiert uns"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Wi sünd en lütt Team vun Friewilligen. Dat kann 1-2 Weeken dauen, bit wi antwöörten."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Unbegrenzten</strong> Höögschwindigkeitstoogang"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direkte <strong>SFTP</strong> Servern"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Spitzenlevel-Spenden oder Utwessel för ne’e Sammlungen (t.ex. ne’e Scans, OCR’te Datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Wi freet uns över grote Spenden vun rieke Lüüd oder Instituts. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "För Spenden över $5000 kontaktiert uns direkt bi %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Wees dorbi, dat de Mitgliedschaften op disse Sied „pro Maand“ sünd, aver dat sünd eenmolige Spenden (nich widderkehrend). Seih de <a %(faq)s>Spenden-FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Wenn ji en Spende maken wüllt (jeden Bedrag) ahn Medlemschaft, bruukt ji disse Monero (XMR) Adress: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Bitte wählt en Betahlmetood ut."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(vörloopig nich verfügbar)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s Geschenkkaart"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkaart (mit App)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Krypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit-/Debitkaart"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regulär)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Karte / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit-/Debitkaart/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkaart"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit-/Debitkaart (Reserve)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit-/Debitkaart 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Mit Krypto könnt ji mit BTC, ETH, XMR un SOL spenden. Bruk dissen Weg, wenn ji al mit Kryptowährung vertraut sünd."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Mit Krypto könnt ji mit BTC, ETH, XMR un mehr spenden."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Wenn Se för de eerste Tied Crypto bruken, föörslahn wi, %(options)s to bruken, üm Bitcoin (de orijinaal un meest bruukte Kryptowährung) to köpen un to schenken."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "För Spenden mit PayPal US bruken wi PayPal Crypto, wat uns anonym blieven lett. Wi schätzen dat ji de Tied nehmt, um to leeren, wie man mit dissen Weg spenden kann, dat helpt uns veel."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Mit PayPal spenden."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Mit Cash App spenden."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Wenn ji Cash App hebt, is dit de eenfachste Weg to spenden!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Togt dat bi Transaktionen ünner %(amount)s Cash App een %(fee)s Gebühr erhebt. För %(amount)s oder mehr is dat gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Spenden mit Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Wenn Se Revolut hebt, is dat de einfachste Weg to spenden!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Mit Kredit- oder Debitkaart spenden."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay un Apple Pay köönt ok funktioneren."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Togt dat bi lütten Spenden de Kreditkaartgebühren uns %(discount)s%% Rabatt opfräten köönt, darum föhrslahn wi längere Abonnements."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Togt dat bi lütten Spenden de Gebühren höög sünd, darum föhrslahn wi längere Abonnements."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Mit Binance köönt ji Bitcoin mit Kredit-/Debitkaart oder Bankkonto köpen un denn de Bitcoin an uns spenden. So köönt wi sicher un anonym blieven, wenn wi jümme Spenden annahmen."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance is in fast all Länner verfügbar un ünnerstütt de meisten Banken un Kredit-/Debitkaarten. Dit is nu uns hööchst Empfehlung. Wi schätzen dat ji di Tied nehmt, üm to leeren, wie man mit dissen Methode spenden kann, denn dat helpt uns veel."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Spenden mit jüm regulären PayPal-Konto."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Spenden mit Kredit-/Debitkaart, PayPal oder Venmo. Ji köönt op de nächsten Siet dor twischen wählen."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Spenden mit en Amazon-Geschenkkort."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Tüüg dorup, dat wi de Beträgen op de Summen runden mööt, de vun unsen Wiederverkäupers annahmen warrt (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>WICHTIG:</strong> Wi ünnerstütt bloß Amazon.com, nich annere Amazon-Websieten. Zum Bispeel, .de, .co.uk, .ca, sünd NICH ünnerstütt."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>WICHTIG:</strong> Deze Optie is voor %(amazon)s. Als je een andere Amazon-website wilt gebruiken, selecteer deze dan hierboven."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Disse Methode brukt en Kryptowährungsanbieter as en tussensteg Conversion. Dat kann wat verwirrend wesen, also brukt disse Methode bloß, wenn annere Betahlmethoden nich funktschoon. Dat funktschoont ok nich in all Länner."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Spende mit en Kredit-/Debitkaart, över de Alipay-App (super einfach to installeern)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installier de Alipay-App"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installier de Alipay-App ut den <a %(a_app_store)s>Apple App Store</a> oder <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrier di mit dien Telefonnummer."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Keen weitere persöönliche Details sünd nödig."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Föög Bankkaart to"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Ünnerstütt: Visa, MasterCard, JCB, Diners Club un Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Seih <a %(a_alipay)s>disse Anwißung</a> för mehr Information."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Wi köönt Kredit-/Debitkaarten nich direkt ünnerstütten, weil Banken nich mit uns warken wüllt. ☹ Man, dor sünd verschiddene Mööglichkeiten, Kredit-/Debitkaarten över annere Betahlmethoden to bruken:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Giftkaart"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Senden ji uns Amazon.com-Geschenkkorten mit jümme Kredit-/Debitkaart."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay ünnerstütt internationale Kredit-/Debitkaarten. Seht <a %(a_alipay)s>disse Anwißung</a> för mehr Information."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) ünnerstütt internationale Kredit-/Debitkaarten. In de WeChat-App, gah to „Me => Services => Wallet => Add a Card“. Wenn ji dat nich seht, schalt dat an mit „Me => Settings => General => Tools => Weixin Pay => Enable“."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Ji köönt Krypto mit Kredit-/Debitkaarten köpen."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Krypto-Express-Servicen"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Express-Servicen sünd praktisch, aver hebbt höger Gebühren."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Du kannst dit as Alternativ to en Krypto-Börs bruken, wenn du schnell en gröttere Spende maken wullt un di de Gebühr vun $5-10 nich stör."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Sorg dorför, dat du de exakte Krypto-Summe, de op de Spendensied steiht, un nich de Summe in $USD versennen deist."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Anners warrt de Gebühr afdragen un wi köönt dien Mitgliedschaft nich automaatsch verarbeiden."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s je na Land, keen Verifikation för de eerste Transaktion)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, keen Verifikation för de eerste Transaktion)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, keen Verifikation för de eerste Transaktion)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Wenn een vun disse Informatschonen nich mehr aktuell is, mailt uns dat bitte."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "För Kreditkaarten, Debitkaarten, Apple Pay un Google Pay brukt wi „Buy Me a Coffee“ (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In ehr System is een „coffee“ gliek $5, also warrt jümme Spende op de nächsten Vielfachen vun 5 gerundet."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Wählt, wie lang ji abonnieren wüllt."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 Maand"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 Maand"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 Maand"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 Maand"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 Maand"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 Maanden"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 Maanden"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>na <span %(span_discount)s></span> Rabatten</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Disse Betahlmetood kräigt en Minimum vun %(amount)s. Bitte en annern Tiedraum oder Betahlmetood utwählen."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Spenden"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Disse Betahlmetood lett blot en Maximum vun %(amount)s to. Bitte en annern Tiedraum oder Betahlmetood utwählen."

#, fuzzy
msgid "page.donate.login2"
msgstr "To’n Mitglied warrn, bitte <a %(a_login)s>Anmellen oder Registeren</a>. Dank för dien Ünnerstüttung!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Dien föögten Crypto-Mün utwählen:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(lüttste Minimum-Betrag)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(bruuk bi’t Senden vun Ethereum vun Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(Warnung: höög Minimum-Betrag)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Klick op den Spenden-Knopp, üm disse Spende to bestäätigen."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Spenden <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Du kannst de Spende noch bi’t Checkout afbreken."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Wiedenleiden na de Spenden-Sied…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Wat is schiefgahn. Bitte de Sied neuladen un nochmaal versöken."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / Maand"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "för 1 Maand"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "för 3 Maanden"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "för 6 Maanden"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "för 12 Maanden"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "för 24 Maanden"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "för 48 Maanden"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "för 96 Maanden"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "för 1 Maand “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "för 3 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "för 6 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "för 12 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "för 24 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "för 48 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "för 96 Maanden “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Spende"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Summe: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Maand för %(duration)s Maanden, inklusiv %(discounts)s%% Rabatt)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Summe: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Maand för %(duration)s Maanden)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Kennung: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Afbreken"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Bist du seker, dat du afbreken wullt? Nich afbreken, wenn du al betahlt hest."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Jo, bitte afbreken"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Dien Spende is afsagt worrn."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Maak een nee Spende"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Wat is schiefgahn. Laden de Sied nochmaal un versöök dat noch eenmol."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Neebestellen"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Du hest al betahlt. Wenn du de Betahlungsinstruktions nochmol ankieken wullt, klick hier:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Olle Betahlungsinstruktions wiesen"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Dank för dien Spende!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Wenn du dat noch nich doon hest, schrieven dien geheime Schlüssel op för dat Inloggen:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Anners kööntst du ut dissen Account utlockt warrn!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "De Betahlungsinstruktions sünd nu veraltet. Wenn du een nee Spende maken wullt, bruuk de „Neebestellen“-Knopp hier boven."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Wichtige Notiz:</strong> De Priesen för Krypto köönt wild swanken, mankmal so veel as 20%% in een poor Minuten. Dat is noch immer minder as de Gebühren, de wi bi veel Betahlproviders hebbt, de oft 50-60%% för dat Warken mit een „Schattenspendenorganisation“ as uns tohren. <u>Wenn du uns de Kassenbon mit den orignalen Pries, den du betahlt hest, schickst, krediteren wi dien Account noch för dat utwählte Medlemschaft</u> (so lang as de Kassenbon nich öller as een poor Stunden is). Wi weet dat höög to schätzen, dat du bereit büst, so wat mit to maken, üm uns to ünnerstütten! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Disse Spende is utlopen. Bitte afsagen un een nee maken."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Krypto-Instruktions"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Överföör na een vun uns Krypto-Accounts"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Spende de Gesamtbedrag vun %(total)s na een vun disse Adressen:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Bitcoin bi Paypal köpen"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Finden de „Krypto“-Sied in dien PayPal-App oder op de Websied. Dat is normaalwies ünner „Finanzen“."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Fölgen de Instruktions, üm Bitcoin (BTC) to köpen. Du bruukst bloots de Betrag to köpen, den du spenden wullt, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Överföör de Bitcoin na uns Adress"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gahn na de „Bitcoin“-Sied in dien PayPal-App oder op de Websied. Drücken de „Överföör“-Knopp %(transfer_icon)s, un denn „Sennen“."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Geven Se uns Bitcoin (BTC)-Adress as den Empfänger an, un follt de Instrukschoonen, üm jümme Spende vun %(total)s to senden:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrukschoonen för Kredit- / Debitkaart"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Spende över uns Kredit- / Debitkaart-Sied"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Spende %(amount)s op <a %(a_page)s>disse Sied</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Seht de Schritt-för-Schritt-Anwißungen ünnen."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Warten op Bestätigen (Sied aktualisieren, üm to checken)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Warten op Överföhren (Sied aktualisieren, üm to checken)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tied över:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(jümme mööt canceln un en nee Spende maken)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Üm de Timer to resetten, maakt einfach en nee Spende."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Status aktualisieren"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Wenn jümme Probleme hebt, kontaktiert uns bitte ünner %(email)s un fügt so veel Information as mööglich to (so as Screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Wenn Se al betahlt hebt:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Manchmal kann de Bestätigen bis to 24 Stünnen dauen, also vergesst nich, disse Siet to aktualisieren (ok, wenn se utlopen is)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Kööp PYUSD-Münzen op PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Follt de Instrukschoonen, üm PYUSD-Münzen (PayPal USD) to köpen."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Kööp en beten mehr (wi rekommendeert %(more)s mehr) as de Bedrag, den jümme spenden (%(amount)s), üm Transaktionsgebaren to decken. Wat överblifft, beholt jümme."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Gah na de “PYUSD”-Sied in jümme PayPal-App oder op de Websied. Drückt de “Överföhren”-Knopp %(icon)s, un denn “Senden”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Överföhren %(amount)s na %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Bitcoin (BTC) op Cash App köpen"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gah na de Sied „Bitcoin“ (BTC) in Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Kööp een beten mehr (wi rekommendeert %(more)s mehr) as de Bedrag, de du spenden deist (%(amount)s), üm de Transaktionsgeern to decken. Wat överblifft, behahlst du."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfereer de Bitcoin na uns Adress"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Drück op de Knopp „Send bitcoin“, üm een „Afhoalung“ to maken. Wessel vun Dollar na BTC dör dat %(icon)s Icon to drücken. Gah de BTC-Betrag ünnen in un drück op „Send“. Seih <a %(help_video)s>dis Video</a>, wenn du stecken blieven deist."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "För lütte Spenden (ünner $25) kannst du Rush oder Priority bruken."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Bitcoin (BTC) op Revolut köpen"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Gah na de Sied „Crypto“ in Revolut, üm Bitcoin (BTC) to köpen."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Kööp een beten mehr (wi rekommendeert %(more)s mehr) as de Bedrag, de du spenden deist (%(amount)s), üm de Transaktionsgeern to decken. Wat överblifft, behahlst du."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfereer de Bitcoin na uns Adress"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Drück op de Knopp „Send bitcoin“, üm een „Afhoalung“ to maken. Wessel vun Euro na BTC dör dat %(icon)s Icon to drücken. Gah de BTC-Betrag ünnen in un drück op „Send“. Seih <a %(help_video)s>dis Video</a>, wenn du stecken blieven deist."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Sorgt dafür, dat de BTC-Betrag dor ünnen bruukt warrt, <em>NICHT</em> Euro oder Dollar, anners kriegen wi nich den richtigen Betrag un köönt dien Medlemschaft nich automaatsch bestäätigen."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "För lütte Spenden (ünner $25) kannst du Rush oder Priority bruken."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Bruk een vun de nakamen „Kreditkaart to Bitcoin“ Express-Servicen, de blots een poor Minuten doort:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Füllt de volgende Details in dat Formular in:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin Bedrag:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Brukt bitte dissen <span %(underline)s>genauen Betrag</span>. Ju gesamt Koste köönt höger wesen wegen Kreditkaart-Gebühren. Bi lütten Beträgen kann dat leider mehr as uns Rabatt wesen."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin Adress (extern Wallet):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s-Instrukschoonen"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Wi ünnerstütt bloß de Standardversion vun Kryptomünzen, keen exotische Netwerken oder Versionen vun Münzen. Dat kann bis to en Stünn dauen, üm de Transaktion to bestätigen, je na Münz."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scannen Sie den QR -Code, um sie zu bezahlen"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scannen Sie diesen QR -Code mit Ihrer Crypto -Wallet -App, um die Zahlungsdetails schnell einzugeben"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon-Geschenkkarte"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Bitte nutzen Sie das <a %(a_form)s>offizielle Amazon.com-Formular</a>, um uns eine Geschenkkarte von %(amount)s an die unten stehende E-Mail-Adresse zu senden."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Wir können keine anderen Methoden von Geschenkkarten akzeptieren, <strong>nur direkt über das offizielle Formular auf Amazon.com gesendet</strong>. Wir können Ihre Geschenkkarte nicht zurückgeben, wenn Sie dieses Formular nicht verwenden."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Gib den genauen Betrag ein: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Bitte schreiben Sie KEINE eigene Nachricht."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "„An“ Empfänger-E-Mail im Formular:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Einzigartig für Ihr Konto, nicht teilen."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Nur einmal verwenden."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Warten auf Geschenkkarte… (Seite aktualisieren, um zu überprüfen)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Nachdem Sie Ihre Geschenkkarte gesendet haben, wird unser automatisiertes System dies innerhalb weniger Minuten bestätigen. Wenn dies nicht funktioniert, versuchen Sie, Ihre Geschenkkarte erneut zu senden (<a %(a_instr)s>Anweisungen</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Wenn das immer noch nicht funktioniert, senden Sie uns bitte eine E-Mail und Anna wird es manuell überprüfen (dies kann einige Tage dauern). Geben Sie unbedingt an, ob Sie bereits versucht haben, die Karte erneut zu senden."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Beispiel:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Beachten Sie, dass der Kontoname oder das Bild seltsam aussehen könnte. Kein Grund zur Sorge! Diese Konten werden von unseren Spendenpartnern verwaltet. Unsere Konten wurden nicht gehackt."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay-Anweisungen"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Spenden Sie über Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Spenden Sie den Gesamtbetrag von %(total)s über <a %(a_account)s>dieses Alipay-Konto</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Wenn de Spenden-Siet blockt warrt, versöök en anner Internetverbindung (z.B. VPN oder Handy-Internet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Leider ist die Alipay-Seite oft nur von <strong>Festlandchina</strong> aus zugänglich. Möglicherweise müssen Sie Ihr VPN vorübergehend deaktivieren oder ein VPN nach Festlandchina verwenden (manchmal funktioniert auch Hongkong)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Maak Spende (QR-Code scannen oder Knopp drücken)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Maak de <a %(a_href)s>QR-code donatiesied</a> op."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scann de QR-code mit de Alipay-App, oder drück de Knopp, üm de Alipay-App op to maken."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Bitte hebbt Geduld; de Sied kann een beten Tiet brauchen, üm to laden, weil se in China is."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat-Anweisungen"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Spenden Sie über WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Spenden Sie den Gesamtbetrag von %(total)s über <a %(a_account)s>dieses WeChat-Konto</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix-Anweisungen"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Spenden Sie über Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Spenn de Gesamtbedrag vun %(total)s över <a %(a_account)s>disse Pix-Konto"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Mailt uns de Quittung"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Senn een Kassenbon oder Screenshot an ju persöönliche Verifikationsadress. Brukt DISSE E-Mail-Adress nich för ju PayPal-Spende."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Schickt en Quittung oder Screenshot an jüm personalen Verifikationsadress:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Wenn de Krypto-Ümschlagskurs bi de Transaktion swankt hett, schickt de Quittung mit den originalen Ümschlagskurs mit. Wi sünd jüm sehr dankbor, dat ji Krypto bruken, dat helpt uns veel!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Wenn ji jüm Quittung mailt hett, klickt op dissen Knopp, so dat Anna dat manuell överpröven kann (dat kann een poor Daag dauen):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Jo, ik heff mi Quittung mailt"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Dank för jüm Spende! Anna wurr jüm Mitgliedschaft binnen een poor Daag manuell aktivieren."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Wat is schiefgahn. Ladd de Sied nochmaal un versöök dat nochmaal."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Schritt-för-Schritt Anwiessung"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Een poor vun de Schritten vermellt Krypto-Wallets, aver maakt jüm keen Sorgen, ji mööt nich wat över Krypto leern för dis."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Gifft jüm E-Mail an."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Wählt jüm Betahlmetood."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Wählt jüm Betahlmetood nochmaal."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Wählt „Self-hosted“ Wallet."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Klickt op „Ik bestäätig de Besitt“."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Ji schullt en E-Mail-Quittung kriegen. Schickt de an uns, un wi wurr jüm Spende so gau as mööglich bestäätigen."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Wart bitte minnstens <span %(span_hours)s>24 Stünnen</span> (un lad disse Siet nieg) vördat du uns kontaktierst."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Wenn ji een Fehler bi de Betahlung maakt hett, köönt wi keen Geld torüchgeven, aver wi wurrt versöken dat to richten."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Mi Spenden"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Spenden-Details sünd nich öffentlich to sehn."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Noch keen Spenden. <a %(a_donate)s>Maak mine eerste Spende.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Noch een Spende maken."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Herunterlaadene Dateien"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Downloads vun Fast-Partner-Server sünd markeert mit %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Wenn du een Datei mit baade, fast un langsaame Downloads herunterlaaden hest, wiest se tweemal op."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Fast-Downloads in de letzten 24 Stünnen tellt to'n Daglimit."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "All Tieden sünd in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Herunterlaadene Dateien sünd nich öffentlich to sehn."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Noch keen Dateien herunterlaaden."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Letzte 18 Stunden"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Früher"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Account"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Anmellen / Registeren"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Account-ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Public Profil: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Geheim Schlüssel (nich delen!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "wiesen"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Mitgliedschaft: <strong>%(tier_name)s</strong> bit %(until_date)s <a %(a_extend)s>(verläden)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Mitgliedschaft: <strong>Keene</strong> <a %(a_become)s>(Mitglied warrn)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Fast-Downloads bruukt (letzten 24 Stünnen): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "welke Downloads?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Exklusiv Telegram-Gruppe: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Kummt bi uns mit!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Steiht op na en <a %(a_tier)s>högeren Rang</a> to, üm bi uns Grupp mit to maken."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontakteert Anna bi %(email)s, wenn ji Interesse hebt, üm jümme Mitgliedschaft op en högeren Rang to steihen."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontakt-E-Mail"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Ji könnt meerdere Mitgliedschaften kombineren (schnelle Downloads per 24 Stünnen warrt tohoppenreken)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Offentlik Profil"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Herunterlaadene Dateien"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Miene Spenden"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Afmelden"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Ji sünd nu afmellt. Laden de Sied ne’e, üm wedder an to mellen."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Wat is schiefgahn. Laden de Sied ne’e un versöök dat nochmaal."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Anmellen erfolgriek! Ju geheime Sleutel is: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Bewahr dissen Sleutel good up. Wenn ji den verlierst, verlierst ji den Toogang to jümme Konto."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Bookmark.</strong> Ji könnt disse Sied markeren, üm jümme Sleutel wedder to finnen.</li><li %(li_item)s><strong>Herunterlaaden.</strong> Klick <a %(a_download)s>disse Link</a>, üm jümme Sleutel to herunterlaaden.</li><li %(li_item)s><strong>Passwort-Manager.</strong> Bruk en Passwort-Manager, üm den Sleutel to speicheren, wenn ji den hier ingeevst.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Geev jümme geheime Sleutel in, üm an to mellen:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Geheime Sleutel"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Anmellen"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ongültige geheime Sleutel. Prüv jümme Sleutel un versöök dat nochmaal, oder mellt ji alternativ en nee Konto ünnen an."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Verlier jümme Sleutel nich!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Noch keen Konto?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Nee Konto anmellen"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Wenn Se sien Schlüssel verlorn hebt, bitte <a %(a_contact)s>kontaktieren Se uns</a> un geven Se so veel Information as mööglich."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Se mööt vielleicht temporär en nee Konto maken, üm uns to kontaktieren."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Olle E-Mail-basierte Konto? Gaven Se hier <a %(a_open)s>E-Mail in</a>."

#, fuzzy
msgid "page.list.title"
msgstr "List"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "bearbeiden"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Spiekern"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Spiekert. Bitte laden Se de Sied neek."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Wat is schiefgahn. Bitte versöken Se dat nochmaal."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "List vun %(by)s, maakt <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "List is leddig."

#, fuzzy
msgid "page.list.new_item"
msgstr "Toe- oder afnehmen vun dissen List dör en Datei to finnen un den “Lists”-Tab to openen."

#, fuzzy
msgid "page.profile.title"
msgstr "Profil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profil nich funnen."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "bearbeiden"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Ännern Se Ihren Anzeigenamen. Ihren Identifikator (den Deel na “#”) kann nich ännert warrn."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Spiekern"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Spiekert. Bitte laden Se de Sied neek."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Wat is schiefgahn. Bitte versöken Se dat nochmaal."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profil maakt <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Listen"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Noch keen Listens"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Maak en neege List, indem Du en Datei finnst un den „Listens“-Tab opmaakst."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Urheberrechtsreform is för de nationale Sekerheit nödig"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Chinesische LLMs (inklusive DeepSeek) wöörn op min illegalen Archiv vun Böker un Papieren trainiert — dat gröttste in de Welt. De Westen mööt dat Urheberrecht as en Froge vun nationale Sekerheit överholen."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "Begleitende Artikel vun TorrentFreak: <a %(torrentfreak)s>eersten</a>, <a %(torrentfreak_2)s>tweten</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Nich to lang her, wöörn „Schattbibliotheken“ am utsterven. Sci-Hub, dat massive illegale Archiv vun akademischen Papieren, harr op, ne’e Warken to nehmen, wegen Klagen. „Z-Library“, de gröttste illegale Bibliothek vun Böker, sehn ehr angebliche Schöpper op kriminelle Urheberrechtsanklagen verhaft. Se hebbt unglaublich managed, ehr Verhaftung to entkamen, man ehr Bibliothek is nich minder in Gefahr."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "As Z-Library mit Schließung konfrontiert weer, harr ik all ehr ganze Bibliothek gesichert un weer op de Sööke na en Plattform, üm se to hüschen. Dit weer min Motivation för de Gründung vun Anna’s Archiv: en Fortsetzung vun de Mission achter disse fröheren Initiativen. Wi hebbt sietdem to de gröttste Schattbibliothek vun de Welt wussen, mit mehr as 140 Millionen urheberrechtlich geschützte Texte över vele Formaten — Böker, akademische Papieren, Magazine, Zeitungen, un mehr."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mien Team un ik sünd Ideologen. Wi glöövt, dat dat moralisch richtig is, disse Dateien to bewaren un to hosten. Bibliotheken op de ganze Welt kriegt weniger Geld, un wi köönt de Menschheit ehr Erven ook nich an Konzerne överlaten."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Denn keem AI. Fast all grote Företaken, de LLMs bouwen, hebbt uns kontaktiert, um op unsen Daten to trainieren. De meisten (aver nich all!) US-basierte Företaken hebbt ehr Meenung ännert, as se de illegalen Charakter vun unsen Arbeit erkannt hebbt. Im Gegensatz darto hebbt chinesische Företaken unsen Sammlungen enthusiastisch annahmen, anscheinend unbeeindruckt vun ehr Legalität. Dat is bemerkenswert, giff dat China en Unnerschriever vun fast all grote internationale Urheberrechtsverträge is."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Wi hebbt Höchstgeschwindigkeits-Zugang to rund 30 Företaken geven. De meisten vun ehr sünd LLM-Företaken, un eenige sünd Datenhändler, de unsen Sammlungen wedderverkopen wüllt. De meisten sünd Chinesen, aver wi hebbt ok mit Företaken ut de USA, Europa, Russland, Südkorea un Japan arbeidt. DeepSeek <a %(arxiv)s>hett togeven</a>, dat en fröhere Version op en Deel vun unsen Sammlungen trainiert worrn is, obschon se verschwiegen sünd över ehr ne'est Modell (wahrscheinlich ok op unsen Daten trainiert)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Wenn de Westen in de Rennen vun LLMs un letztlich AGI vörn blieven will, mutt he sien Standpunkt to Urheberrecht överdenken, un dat bald. Egal, ob ji mit uns över unsen moralischen Standpunkt eenig sünd oder nich, dat wörrd nu en Fall vun Ökonomie, un sogar vun nationaler Sicherheit. All Machtblöcke bouwen künstliche Super-Wissenschaftler, Super-Hacker un Super-Militärs. Informationsfreiheit wörrd för disse Länner en Frage vun't Överleven — sogar en Frage vun nationaler Sicherheit."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Us Team kümmt ut de ganze Welt, un wi hebbt keen besünneren Standpunkt. Aver wi würd Länner mit starke Urheberrechtsgesetze anregen, disse existenzielle Bedrohung to nutzen, um se to reformieren. Wat to doon?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Uns erste Empfehlung is straightforward: de Urheberrechtstied verkorten. In de USA warrt Urheberrecht för 70 Johr na den Dood vun den Autor grantiert. Dit is absurd. Wi köönt dit in Linie mit Patenten brengen, de för 20 Johr na den Antrag grantiert warrt. Dit schullt mehr as genug Tied för Autoren vun Böker, Papieren, Musik, Kunst un annere kreative Warken wesen, üm för ehr Insats voll kompensiert to warrn (inklusive längerfristige Projekte as Filmadaptionen)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Denn, as een Minimum, schüllt Politikmakers Utnahmen för de Massenbewaring un Disseminatschoon vun Texten inbegriepen. Wenn verlorene Einnahmen vun individuelle Kunden de grött Sorge sünd, köönt de persöhnliche Verbreitung verboden blieven. Im Gegenzug wörrt de, de in de Lage sünd, groote Sammlungen to managen — Firmen, de LLMs trainieren, mit Bibliotheken un anneren Archiven — vun disse Utnahmen deckt."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Een paar Länner doot al een Version vun dit. TorrentFreak <a %(torrentfreak)s>berichtete</a>, dat China un Japan AI-Utnahmen in ehr Urheberrechtsgesetze inbröcht hebbt. Dat is uns unklar, wie dit mit internationale Verträge interagiert, aver dat gifft sicher Deckung för ehr inländische Firmen, wat erklärt, wat wi sehn hebbt."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Wat Anna’s Archiv angahn deit — wi wüllt uns unnerirdische Arbeit, de op moralische Überzeugung basert, wieterführen. Doch uns gröttste Wünsch is, in’t Licht to treeden, un uns Effekt legal to verstärken. Bitte reformiert dat Urheberrecht."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lees de begeleidende artikelen van TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Winnaars van de $10.000 ISBN visualisatie beloning"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: We hebben enkele ongelooflijke inzendingen ontvangen voor de $10.000 ISBN visualisatie beloning."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Een paar maanden geleden kondigden we een <a %(all_isbns)s>$10.000 beloning</a> aan om de best mogelijke visualisatie van onze gegevens te maken die de ISBN-ruimte toont. We benadrukten het tonen van welke bestanden we al wel/niet hebben gearchiveerd, en we voegden later een dataset toe die beschrijft hoeveel bibliotheken ISBNs bezitten (een maat voor zeldzaamheid)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "We zijn overweldigd door de respons. Er is zoveel creativiteit geweest. Een grote dank aan iedereen die heeft deelgenomen: jullie energie en enthousiasme zijn aanstekelijk!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Uiteindelijk wilden we de volgende vragen beantwoorden: <strong>welke boeken bestaan er in de wereld, hoeveel hebben we al gearchiveerd, en op welke boeken moeten we ons vervolgens richten?</strong> Het is geweldig om te zien dat zoveel mensen om deze vragen geven."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "We begonnen zelf met een basisvisualisatie. In minder dan 300kb vertegenwoordigt deze afbeelding beknopt de grootste volledig open \"lijst van boeken\" ooit samengesteld in de geschiedenis van de mensheid:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Bestanden in Anna’s Archief"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC datalek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Boeken"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archief"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register vun Verläger"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russische Staatsbibliothek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Keiserliche Bibliothek vun Trantor"

#, fuzzy
msgid "common.back"
msgstr "Terug"

#, fuzzy
msgid "common.forward"
msgstr "Vooruit"

#, fuzzy
msgid "common.last"
msgstr "Laatste"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Seht de <a %(all_isbns)s>originale Blogpost</a> för mehr Informationen."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Wi hebbt en Utfordrung utgahn, dat to verbedern. Wi würd'n en Pries för den eersten Platz vun $6,000, den tweeten Platz vun $3,000 un den dritten Platz vun $1,000 utgeven. Wegens de överwältigende Reaktion un de fantastischen Insendungen hebbt wi beslaten, de Priespool een lütt beten to erhöhen un viermol den dritten Platz mit $500 to vergeven. De Gewinners sünd dorunner, aver seht sik all Insendungen <a %(annas_archive)s>hier</a> an, oder ladet unsen <a %(a_2025_01_isbn_visualization_files)s>kombineerten Torrent</a> rut."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Eersten Platz $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Disse <a %(phiresky_github)s>Insendung</a> (<a %(annas_archive_note_2951)s>Gitlab-Kommentar</a>) is einfach allens, wat wi wüllt hebbt, un noch mehr! Wi hebbt besünners de unglaublich flexiblen Visualisierungsopties (sogar mit Unterstützung för eigene Shader) mögt, aver mit en umfassende List vun Voreinstellungen. Wi hebbt ok mögt, wo fix un glat allens löppt, de einfache Implementierung (de nich eens en Backend hett), de kloke Minikarte un de umfassende Erklärung in ehr <a %(phiresky_github)s>Blogpost</a>. Unglaublich Arbeit, un de verdiente Gewinners!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Tweete Platz $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Noch en unglaubliche <a %(annas_archive_note_2913)s>Insendung</a>. Nich so flexibel as den eersten Platz, aver wi hebbt ehr Makro-Level-Visualisierung över den eersten Platz föredragen (Raumfüllende Kurve, Grenzen, Beschriftung, Hervorhebung, Schwenken un Zoom). En <a %(annas_archive_note_2971)s>Kommentar</a> vun Joe Davis hett uns bewegt:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Wohlen perfekte Quadrate un Rechtecke mathematisch gefallig sünd, bieden se nich de beste Lokalität in en Kartenzusammenhang. Ik glööv, de Asymmetrie, de in disse Hilbert oder klassisch Morton innewohnend is, is keen Fehler, man en Merkmal. So as Italiens berühmt Stiefelform maakt dat Land op en Kaarte gliek wiederkännbaar, köönt de uniek „Eigenarten“ vun disse Kurven as kognitive Landmarken dienen. Disse Unverwechselbarkeit kann de räumliche Gedächtnis verbeetern un de Brukers helpen, sik to orientieren, wat dat Finden vun spesifischen Regionen oder dat Opföllen vun Mustern eventuell lechter maakt.“"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Un noch veel Optschonen för Visualisieren un Rendern, as ok en unglaublich glatten un intuitiven UI. En solide twete Platz!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Dritte Platz $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In dissen <a %(annas_archive_note_2940)s>Bidrag</a> hett uns de verschiddenen Arten vun Ansichten, besünners de Vergliek- un Verlägeransichten, echt gefallen."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Dritte Platz $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Wohlen nich dat meest polierte UI, checkt dissen <a %(annas_archive_note_2917)s>Bidrag</a> veel vun de Boxen. Wi hett besünners de Vergliekfunkschoon leuk funnen."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Dritte Platz $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "So as de eerste Platz, hett dissen <a %(annas_archive_note_2975)s>Bidrag</a> uns mit sien Flexibilität imponeert. Letztlich is dat, wat en groot Visualisierungs-Tool maakt: maximale Flexibilität för Power-Brukers, währnd dat för de normale Brukers simpel blifft."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Dritte Platz $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "De letzte <a %(annas_archive_note_2947)s>Bidrag</a>, de en Pries kriegt, is recht basic, aver hett paar unieke Funkschoonen, de uns echt gefallen hett. Wi hett leuk funnen, wie se wiesen, wieveel Datasets en spesifischen ISBN decken as en Maß för Popularität/Verlässlichkeit. Wi hett ok de Einfachheit, aver Effektivität vun en Opazitätsslider för Verglieken, echt leuk funnen."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Nennenswerte Ideen"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Noch paar Ideen un Implementierungen, de uns besünners gefallen hett:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Hochhüser för Seltensheit"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live-Statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anmerkungen, un ok Live-Statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unieke Kaarteansicht un Filter"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Kuhle Standard-Farbenschema un Hitzkaart."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Einfache Umschakeln vun Datasets för schnelle Verglieken."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Schöne Etiketten."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skalebalken mit Anzohl vun Böker."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Veel Schuivers för Datasets to verglieken, as wenn ji een DJ weern."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Wi köönt noch een Tied so wietermaken, man laten wi hier stoppen. Kiekt gern all Insendungen <a %(annas_archive)s>hier</a> an, oder daunloadt unsen <a %(a_2025_01_isbn_visualization_files)s>kombineerten Torrent</a>. So veel Insendungen, un jede bringt een uniek Perspektiv mit, egal ob in UI oder Implementering."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Wi wüllt mindestens de eerste Platz Insending in unsen Hauptwebsteed inarbeiden, un villicht noch annere. Wi hebbt ok anfungen, doröver to denken, wo wi den Prozess vun Identifizeren, Bestätigen un Archiveren vun de seltenschten Böker organiseren köönt. Mehr doröver kümmt noch."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Dank an all, de mitmaakt hebbt. Dat is fantastsch, dat so veel Lüüd sik dorüm kümmeret."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Uns Hart is full mit Dankbarkeet."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualiseren vun all ISBNs — $10,000 Belohnung bit 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dit Bild representert de gröttste fullstendig open „List vun Böker“, de jemals in de Geschicht vun de Minschheit tosamengestellt wurrn is."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dit Bild is 1000×800 Pixel. Jeden Pixel representert 2.500 ISBNs. Wenn wi een Datei för een ISBN hebbt, maken wi den Pixel griener. Wenn wi weet, dat een ISBN utgeven wurrn is, man wi hebbt keen passenen Datei, maken wi den röder."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In minder as 300kb representert dit Bild knapp de gröttste fullstendig open „List vun Böker“, de jemals in de Geschicht vun de Minschheit tosamengestellt wurrn is (een poor hundert GB fullstendig komprimert)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Dat wiest ok: dor is noch veel Arbeid to doon för dat Sicherstellen vun Böker (wi hebbt blots 16%%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Achtergrund"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Wo kann Anna’s Archiv sien Missioon vun dat Sicherstellen vun all Minschheit sien Wissen erreichen, ohne to weten, welke Böker noch dorbuiten sünd? Wi bruukt een TODO-List. Een Weg, dat to kartieren, is dör ISBN-Nummern, de siet de 1970er an jeden utgevenen Book toewiesen wurrn sünd (in de meisten Länner)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Dor is keen zentrale Autoriteit, de all ISBN-Toewiesungen weet. Stattdessen is dat een verteiltes System, wo Länner Nummernbereiken kriegen, de se denn an grote Verlägen toewiesen, de villicht wedder kleinere Bereiken an lütte Verlägen verdeelen. Endlich wurrn individuelle Nummern an Böker toewiesen."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Wi hebbt anfungen, ISBNs <a %(blog)s>twee Johren her</a> mit unsen Scrape vun ISBNdb to kartieren. Sietdem hebbt wi veel mehr Metadata-Quellen gescrapet, so as <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby un mehr. Een fullstendige List is op de „Datasets“ un „Torrents“ Sieden vun Anna’s Archiv to finnen. Wi hebbt nu bi wiet dat gröttste fullstendig open, leicht daunloadbare Sammling vun Book-Metadata (un dorüm ISBNs) in de Welt."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Wi hebbt <a %(blog)s>umfangreich schreven</a> doröver, woarum wi uns üm dat Bewahren kümmeret, un woarum wi nu in een kritschen Fenster sünd. Wi mööt nu seltne, unnerfokuste un uniek gefährdete Böker identifizeren un bewahren. Gode Metadata vun all Böker in de Welt helpt dorbi."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualiseren"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Neben dat Übersichtsbild köönt wi ok individuelle Datasets ankieken, de wi kriegen hebbt. Bruk de Dropdown un Knööp, üm twischen disse to wechseln."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Dor sünd veel interessante Musters to sehn in disse Biller. Woarum is dor een Regelmäßigkeit vun Linnen un Blöcken, de op verschiddene Skalen to passeren schient? Wat sünd de leere Gebieten? Woarum sünd gewisse Datasets so geklustert? Wi laten disse Froogen as een Übung för den Leser."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10.000 Bounty"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Hier is veel te ontdekken, dus we kondigen een bounty aan voor het verbeteren van de visualisatie hierboven. In tegenstelling tot de meeste van onze bounties, is deze tijdgebonden. U moet uw open source-code indienen voor 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "De beste inzending krijgt $6.000, de tweede plaats $3.000 en de derde plaats $1.000. Alle bounties worden uitgereikt met Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Hieronder staan de minimale criteria. Als geen enkele inzending aan de criteria voldoet, kunnen we nog steeds enkele bounties toekennen, maar dat is naar ons eigen inzicht."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork deze repo en bewerk deze blogpost HTML (geen andere backends behalve onze Flask-backend zijn toegestaan)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Maak de afbeelding hierboven soepel in- en uitzoomend, zodat u helemaal kunt inzoomen op individuele ISBN's. Klikken op ISBN's moet u naar een metadata-pagina of zoekopdracht op Anna’s Archiv brengen."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "U moet nog steeds kunnen schakelen tussen alle verschillende datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landbereiken en uitgeverbereiken moeten worden gemarkeerd bij hoveren. U kunt bijvoorbeeld <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> gebruiken voor landinformatie, en onze “isbngrp” scrape voor uitgevers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Het moet goed werken op desktop en mobiel."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Voor bonuspunten (dit zijn slechts ideeën — laat uw creativiteit de vrije loop):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sterke overweging zal worden gegeven aan bruikbaarheid en hoe goed het eruitziet."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Toon daadwerkelijke metadata voor individuele ISBN's bij inzoomen, zoals titel en auteur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Betere ruimte-vullende curve. Bijvoorbeeld een zigzag, gaande van 0 naar 4 op de eerste rij en dan terug (in omgekeerde richting) van 5 naar 9 op de tweede rij — recursief toegepast."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Verschillende of aanpasbare kleurenschema's."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Speciale weergaven voor het vergelijken van datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Manieren om problemen op te sporen, zoals andere metadata die niet goed overeenkomen (bijv. sterk verschillende titels)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Afbeeldingen annoteren met opmerkingen over ISBN's of bereiken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Eventuele heuristieken voor het identificeren van zeldzame of bedreigde boeken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Welke creatieve ideeën u ook kunt bedenken!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "U MAG volledig afwijken van de minimale criteria en een volledig andere visualisatie maken. Als het echt spectaculair is, komt dat in aanmerking voor de bounty, maar naar ons eigen inzicht."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Maak inzendingen door een opmerking te plaatsen bij <a %(annas_archive)s>dit issue</a> met een link naar uw geforkte repo, merge-verzoek of diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "De code om deze afbeeldingen te genereren, evenals andere voorbeelden, is te vinden in <a %(annas_archive)s>deze directory</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "We hebben een compact dataformaat ontwikkeld, waarmee alle vereiste ISBN-informatie ongeveer 75MB (gecomprimeerd) is. De beschrijving van het dataformaat en de code om het te genereren is <a %(annas_archive_l1244_1319)s>hier</a> te vinden. Voor de beloning bent u niet verplicht dit te gebruiken, maar het is waarschijnlijk het meest handige formaat om mee te beginnen. U kunt onze metadata transformeren zoals u wilt (hoewel al uw code open source moet zijn)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "We kunnen niet wachten om te zien wat u bedenkt. Veel succes!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna’s Archief Containers (AAC): standaardiseren van releases van de grootste schaduw bibliotheek ter wereld"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’s Archief is de grootste schaduw bibliotheek ter wereld geworden, waardoor we onze releases moeten standaardiseren."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’s Archief</a> is veruit de grootste schaduw bibliotheek ter wereld geworden, en de enige schaduw bibliotheek van deze schaal die volledig open-source en open-data is. Hieronder staat een tabel van onze Datasets-pagina (licht aangepast):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "We hebben dit op drie manieren bereikt:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Het spiegelen van bestaande open-data schaduw bibliotheken (zoals Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Het helpen van schaduw bibliotheken die meer open willen zijn, maar niet de tijd of middelen hadden om dit te doen (zoals de Libgen stripverzameling)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Het scrapen van bibliotheken die niet in bulk willen delen (zoals Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Voor (2) en (3) beheren we nu zelf een aanzienlijke collectie torrents (honderden TB's). Tot nu toe hebben we deze collecties als eenmalige projecten benaderd, wat betekent dat er voor elke collectie op maat gemaakte infrastructuur en dataorganisatie is. Dit voegt aanzienlijke overhead toe aan elke release en maakt het bijzonder moeilijk om meer incrementele releases te doen."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Daarom hebben we besloten om onze releases te standaardiseren. Dit is een technische blogpost waarin we onze standaard introduceren: <strong>Anna’s Archief Containers</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Ontwerpdoelen"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Ons primaire gebruiksscenario is de distributie van bestanden en bijbehorende metadata van verschillende bestaande collecties. Onze belangrijkste overwegingen zijn:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene bestanden en metadata, zo dicht mogelijk bij het originele formaat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene identificatoren in de bronbibliotheken, of zelfs het ontbreken van identificatoren."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Gescheiden releases van metadata versus bestandsgegevens, of alleen metadata-releases (bijv. onze ISBNdb-release)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribution över Torrents, obwool mit de Mööglichkeit för annere Distributionmetoden (z.B. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Unveränderliche Records, denn wi schüllt annehmen, dat uns Torrents för immer leven wüllt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementelle Releases / anhängbare Releases."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Maschinenlesbar un schrieven, bequäm un fix, besünners för uns Stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Tüchlich einfach för Minschen to inspizieren, obwool dat sekundär to Maschinenlesbarkeit is."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Einfach to seed uns Sammlungen mit en standard gemieteten Seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binärdaten köönt direkt vun Webservern as Nginx serviert warrn."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Een paar Nich-Ziele:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Wi kümmt uns nich dorüm, dat Dateien manuell op de Disk einfach to navigieren sünd, oder söökbar ohne Vörverarbeitung."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Wi kümmt uns nich dorüm, direkt kompatibel mit bestahn Bibliotheksoftware to wesen."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Obwool dat einfach schüllen wesen för jedermann uns Sammlung över Torrents to seed, verwacht wi nich, dat de Dateien bruukbar sünd ohne bedüüdende technische Kenntnis un Engagement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Da Anna’s Archiv Open Source is, will wi uns Format direkt dogfooden. Wenn wi uns Söökindex aktualisieren, greift wi nur op öffentlich verfügbare Paden to, so dat jedermann, de uns Bibliothek forkt, fix starten kann."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "De Standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Letztlich hebbt wi uns för en relativ einfachen Standard entschieden. He is recht locker, nich-normativ, un en Arbeit in Fortschritt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archiv Container) is en enkel Item, dat ut <strong>Metadata</strong> bestahn deit, un optional <strong>Binärdaten</strong>, beide sünd unveränderlich. He hett en global eindeutige Identifikator, de <strong>AACID</strong> heet."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Sammlung.</strong> Jede AAC höört to en Sammlung, de per Definition en List vun AACs is, de semantisch konsistent sünd. Dat bedüüt, wenn du en bedüüdende Ännerung an dat Format vun de Metadata maakt, denn musst du en niege Sammlung erstellen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>„Records“ un „Files“ Sammlungen.</strong> Vun de Konvention her is dat oft bequäm, „Records“ un „Files“ as verschiedene Sammlungen to veröffentlichen, so dat se to verschiedene Tiden veröffentlicht warrn köönt, z.B. basierend op Scraping-Raten. En „Record“ is en Sammlung, de nur Metadata enthüllt, mit Informatschen as Booktitels, Autoren, ISBNs, etc., währnd „Files“ de Sammlungen sünd, de de faktischen Dateien enthüllt (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Dat Format vun AACID is dis: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. För exempel, en faktische AACID, de wi veröffentlicht hebbt, is <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: de Sammlungsnaam, de ASCII-Bokstäven, Ziffern un Underscores enthüllen kann (aber keen dubbel Underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: en kort Version vun de ISO 8601, allens in UTC, z.B. <code>20220723T194746Z</code>. Disse Nummer mutt för jede Release monoton tonehmen, obwool de genau Semantik per Sammlung verschieden kann. Wi föörslahn de Tied vun Scraping oder vun Generieren vun de ID to bruken."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{kollekschoon-spezifisch ID}</code>: en kollekschoon-spezifisch Identifikator, wenn dat to passt, z.B. de Z-Library ID. Kann weggelaten oder afkorten warrn. Mut weggelaten oder afkorten warrn, wenn de AACID anners över 150 Teken wöör."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: en UUID, aver to ASCII verkorten, z.B. mit base57. Wi bruukt nu de <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-Bibliothek."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID-Range.</strong> As AACIDs Tiedenstempels hebbt, de sik immer vergröttert, köönt wi dat bruken, üm Ranges binnen en besünnere Kollekschoon to markeren. Wi bruken dit Format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wo de Tiedenstempels mit inbegrepen sünd. Dit is in Übereenstimmung mit de ISO 8601-Notation. Ranges sünd kontinuierlich un köönt sik överlappen, aver in'n Fall vun Överlappung mööt se identische Records as de vörher friegeloote in de Kollekschoon hebben (as AACs nich ännerbar sünd). Fehlende Records sünd nich toestahn."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata-Datei.</strong> En Metadata-Datei enthält de Metadata vun en Range vun AACs för en besünnere Kollekschoon. Disse hebbt de volgende Eegenschoppen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "De Dateinaam mööt en AACID-Range wesen, vörsett mit <code style=\"color: red\">annas_archive_meta__</code> un follt vun <code>.jsonl.zstd</code>. För'n Bispeel, een vun uns Releases heet<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "As de Dateiendung angifft, is de Dateityp <a %(jsonlines)s>JSON Lines</a> kompressiert mit <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Jede JSON-Objekt mööt de volgende Felder op de top Level hebben: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). Keen anner Felder sünd toestahn."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> is willekürliche Metadata, na de Semantik vun de Kollekschoon. Se mööt semantisch konsistent binnen de Kollekschoon wesen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> is optional un is de Naam vun den binären Datenfolder, de de tohörige binäre Daten enthält. De Dateinaam vun de tohörige binäre Daten binnen den Folder is de Record ehr AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "De <code style=\"color: red\">annas_archive_meta__</code> Vörsatz kann an de Naam vun jow Institution anpasst warrn, z.B. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binäre Datenfolder.</strong> En Folder mit de binären Daten vun en Range vun AACs för en besünnere Kollekschoon. Disse hebbt de volgende Eegenschoppen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "De Directory-Naam mööt en AACID-Range wesen, vörsett mit <code style=\"color: green\">annas_archive_data__</code>, un keen Suffix. För'n Bispeel, een vun uns echte Releases heet en Directory mit den Naam<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "De Directory mööt Datein för all AACs binnen den angevenen Range hebben. Jede Datei mööt ehr AACID as Dateinaam hebben (keen Extensions)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Dat is anraten, disse Folders in en managebaren Grött to holden, z.B. nich grötter as 100GB-1TB je, obschon disse Anraten över Tied ännern kann."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> De Metadata-Dateien un binäre Datenfolders köönt in Torrents bündelt warrn, mit een Torrent per Metadata-Datei oder een Torrent per binäre Datenfolder. De Torrents mööt de originale Datei/Directory-Naam plus en <code>.torrent</code> Suffix as ehr Dateinaam hebben."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Bispeel"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Laten wi uns unsen jüngsten Z-Library-Release as en Bispeel ankieken. Dat besteiht ut twee Kollekschoonen: “<span style=\"background: #fffaa3\">zlib3_records</span>” un “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dit maakt dat wi Metadata-Records vun de echte Bookdateien apart scrapen un friegeloote köönt. As so hebbt wi twee Torrents mit Metadata-Dateien friegeloote:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Wi hebbt ook en Bunt vun Torrents mit binäre Datenfolders friegeloote, aver bloß för de “<span style=\"background: #ffd6fe\">zlib3_files</span>” Kollekschoon, 62 in't Ganse:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Dorch dat lopen vun <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> köönt wi ankieken, wat dorbinnen is:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In dit Fall is dat Metadata vun en Book, as vun Z-Library angifft. Op de top Level hebbt wi bloß „aacid“ un „metadata“, aver keen „data_folder“, denn dor is keen tohörige binäre Daten. De AACID enthält „22430000“ as de primäre ID, wat wi sehn köönt, dat ut „zlibrary_id“ nahmen is. Wi köönt verwachten, dat annere AACs in disse Kollekschoon de sülve Struktuur hebben."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nu laten wi <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> lopen:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dit is en veel kläiner AAC-Metadata, obwool de gröttste Deel vun dissen AAC annerswo in en Binärdatei liggt! Wi hebbt ja dismal en „data_folder“, also köönt wi verwachten, dat de tohörige Binärdaten bi <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> liggt. De „metadata“ enthält de „zlibrary_id“, also köönt wi dat mit den tohörigen AAC in de „zlib_records“-Sammlung einfach toordnen. Wi köönt dat op verschiddene Weegen toordnen, z.B. över AACID — de Standard schrievt dat nich vör."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Merkt op, dat dat ok nich nödig is, dat dat „metadata“-Feld sülvst JSON is. Dat köönt en String wesen, de XML oder een anner Datenformat enthält. Du köönt ok Metadata-Informationen in den tohörigen Binärblob spiekern, z.B. wenn dat veel Daten sünd."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Slussfolgerung"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Mit dissen Standard köönt wi Releases mehr inkrementell maken un ne’e Datenquellen leichter tofögen. Wi hebbt al een paar spannende Releases in de Pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Wi hopen ok, dat dat för annere Schattbibliotheken leichter warrt, uns Sammlungen to spigeln. Na allens is uns Mål, dat Minschenwissen un -kultur för immer to bewaren, also je mehr Redundanz, desto beter."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna sien Update: full open source Archiv, ElasticSearch, 300GB+ vun Bookcovers"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Wi hebbt rund um de Uhr arbeidt, um en goede Alternativ mit Anna sien Archiv to bieden. Hier sünd een paar vun de Dingen, de wi jüngst erreicht hebbt."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Mit Z-Library, dat dorunner geiht un de (vermeent) Gründers arrestiert warrt sünd, hebbt wi rund um de Uhr arbeidt, um en goede Alternativ mit Anna sien Archiv to bieden (wi wüllt dat hier nich linken, man du köönt dat googeln). Hier sünd een paar vun de Dingen, de wi jüngst erreicht hebbt."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna sien Archiv is full open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Wi glöövt, dat Information frie wesen schall, un uns egen Code is keen Utnahm. Wi hebbt all uns Code op uns privaten Gitlab-Instanz friegäven: <a %(annas_archive)s>Anna sien Software</a>. Wi bruken ok den Issue-Tracker, um uns Arbeid to organiseren. Wenn du mit uns Utwikkelung in Kontakt treeden wullt, is dit en goede Startpunkt."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Um di en Vörgeschmack vun de Dingen to geven, an de wi arbeidt, neem uns jüngst Arbeid an de Performance-Verbesserungen op de Klient-Sied. Da wi noch keen Paginering implementeert hebbt, würd wi oft heel lange Suchsieden teruggeven, mit 100-200 Resultaten. Wi wüllt de Suchresultaten nich to fröh afsnieden, man dat hett bedüden, dat dat op eenige Geräten langsaam warrt. För dit hebbt wi en lütt Trick implementeert: wi hebbt de meisten Suchresultaten in HTML-Kommentaren (<code><!-- --></code>) inpackt, un denn en lütt Javascript schreven, dat detecteert, wenn en Resultat sichtbor warrn schall, an welk Moment wi den Kommentar utpackt:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM-\"Virtualisierung\" in 23 Reegen implementeert, keen Bedüden för schnieke Bibliotheken! Dit is de Art vun schnellen pragmatischen Code, de du kriegst, wenn du begrenzte Tied hest un echte Probleme, de löst warrn mööt. Dat is berichtet, dat uns Such nu ok op langsaame Geräten goed funktioneert!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Een anner grote Insats weer, de Datenbankautomatisering to bouwen. As wi start hebbt, hebbt wi einfach verschiddene Quellen tosamendragen. Nu wüllt wi se aktuell holden, also hebbt wi en Bünn Skripten schreven, um ne’e Metadata vun de twee Library Genesis-Forks to downloaden un se to integreren. Dat Mål is, dat dat nich bloots för uns Archiv nützlich is, man dat dat ok för jeden, de mit Schattbibliothek-Metadata spelen wull, einfach is. Dat Mål weer en Jupyter-Notebook, dat allerhand interessante Metadata verfügbar hett, so dat wi mehr Forschen maken köönt, as to utfinnen, wat <a %(blog)s>Prozent vun ISBNs för immer bewaren sünd</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Tosletzt hebbt wi uns Spendensystem överholt. Du köönt nu en Kreditkaart bruken, um direkt Geld in uns Krypto-Wallets to överwiesen, ohne dat du wirklich wat över Kryptowährungen weten mööt. Wi wüllt beobachten, wie goed dat in de Praxis funktioneert, man dit is en grote Deal."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Wessel na ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Een vun uns <a %(annas_archive)s>Tickets</a> weer en Sammelsurium vun Problemen mit uns Suchsystem. Wi hebbt MySQL-Volltextsuche bruken, da wi all uns Daten sowieso in MySQL hadden. Man dat hett sien Grenzen:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Eenige Anfragen hebbt super lang daurt, so dat se all de opene Verbindungen blockeert hebbt."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Standardmäßig hett MySQL en minimum Woordlengte, oder dien Index kann heel groot warrn. Lüüd hebbt berichtet, dat se nich na „Ben Hur“ söken köönt."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "De Suche weer bloots somewhat schnell, wenn se full in den Speicher ladet weer, wat uns nödig maakt, en düürere Maschien to kriegen, um dat to laten lopen, plus eenige Kommandos, um den Index bi’n Start vör to laden."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Wi würrn nich in de Lage wesen, dat einfach to utweiten, um ne'e Funktionen to bouwen, as bessere <a %(wikipedia_cjk_characters)s>Tokenisierung för Sprachen ohne Leerzeichen</a>, Filterung/Facettierung, Sortierung, \"Meent ji dat?\"-Vörslagen, Autovervollständigung un so wieter."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Na’t wi mit en Reeg Experten snackt hebbt, hebbt wi us för ElasticSearch beslaten. Dat is nich perfekt wesen (de Vörslagen för „meent ji dat“ un Autocomplete sünd nich so good), aver överall is dat veel beter as MySQL för’t Söken. Wi sünd noch nich <a %(youtube)s>to överzeugt</a>, dat för wichdige Daten to bruken (obwohl se veel <a %(elastic_co)s>Vörschritt</a> maakt hebbt), aver överall sünd wi heel tofreden mit de Wessel."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "För nu hebbt wi en veel schnellere Söken, betere Spraakstütten, betere Relevanzsortierung, verschedene Sortieroptionen un Filter för Spraak/Buchtyp/Dateityp inricht. Wenn ji neugierig sünd, wo dat funktioneeren deit, <a %(annas_archive_l140)s>kiek</a> <a %(annas_archive_l1115)s>mol</a> <a %(annas_archive_l1635)s>rin</a>. Dat is recht toganglich, obschon dat noch wat mehr Kommentaren bruken könnt…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ an Bookcovers friggaven"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Endlich freit wi uns, een lüttje Veröffentlikung ankündigen to könen. In Tüsamenarbeit mit de Lüüd, de den Libgen.rs-Fork bedrieven, delen wi all ehr Bookcovers över Torrents un IPFS. Dit wööd de Belastung vun dat Ankieken vun de Covers op mehr Reekners verteilen un se beter bewaren. In vele (aber nich all) Fallen sünd de Bookcovers in de Dateien sülvst inbunnen, also is dit een soort vun „afleiten Daten“. Aber dat in IPFS to hebben, is noch immer heel nützlich för den dagtäglichen Betrieb vun Annas Archiv un de verschedenen Library Genesis-Forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "As üüblich könnt ji disse Veröffentlikung bi den Pirate Library Mirror finnen (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Wi wööd hier nich dorhen linken, aber ji könnt dat leicht finnen."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hoffentlich köönt wi uns Tempo een beten rutnehmen, nu dat wi een anständige Alternativ to Z-Library hebben. Dit Arbeitspensum is nich besünners haltbar. Wenn ji Interesse hebben, bi de Programmierung, Serverbetrieb oder Bewahrungsarbeit mit to helpen, neem gern Kontakt mit uns op. Dor is noch veel <a %(annas_archive)s>Arbeit to doon</a>. Dank för jüm Interesse un Ünnerstüttung."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annas Archiv hett de grötste Comics-Schattenbibliothek vun de Welt (95TB) sichert — ji könnt helpen, se to seeden"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "De grötste Comics-Schattenbibliothek vun de Welt hett een eenzigen Schwachpunkt... bis hüüt."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskutieren op Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "De grötste Comics-Schattenbibliothek is wahrscheinlik de vun een besünneren Library Genesis-Fork: Libgen.li. De eenzigen Administrator vun dissen Siet hett een wahnsinnige Comics-Sammlung vun över 2 Millionen Dateien, mit een Gesamtvolumen vun över 95TB, tosammlen. Aber, anners as annere Library Genesis-Sammlungen, weer disse nich in Masse över Torrents verfügbar. Ji köönt disse Comics alleen individuell över sien langsaamen persöönlichen Server togriepen — een eenzigen Schwachpunkt. Bis hüüt!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In dissen Beitrag vertellen wi mehr över disse Sammlung un över uns Spendenaktion, üm mehr vun dissen Arbeit to ünnerstütten."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon versöcht, sik in de alltagslüüdige Welt vun de Bibliothek to verluren…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-Forks"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Eerst een beten Hintergrund. Ji könnt Library Genesis för ehr epische Book-Sammlung kennen. Weniger Lüüd weten, dat Library Genesis-Volontairs annere Projekten erschaffen hebben, as een ansehnliche Sammlung vun Tiedschriften un Standarddokumenten, een fullstännigen Backup vun Sci-Hub (in Tüsamenarbeit mit de Gründeren vun Sci-Hub, Alexandra Elbakyan), un in düden Fall, een massive Sammlung vun Comics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "To eenen Tiedpunkt sünd verschedene Betriivers vun Library Genesis-Spiegels ehr eegenen Weg gahn, wat to de hüdigen Sittuatjon vun een Reeg vun verschedenen „Forks“ föhrt hett, de all noch den Naam Library Genesis dragen. De Libgen.li-Fork hett besünners disse Comics-Sammlung, as ok een ansehnliche Tiedschriften-Sammlung (an de wi ok arbeiden)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Tüsamenarbeit"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Angesicht vun ehr Grötte stün disse Sammlung lang op uns Wünschlist, also na uns Erfolg mit den Backup vun Z-Library, hebbt wi uns Blick op disse Sammlung richt. Eerst hebbt wi se direkt afschrapt, wat een rechte Utfordring weer, denn ehr Server weer nich in besten Tostand. Wi hebbt so üm die 15TB kriegen, aber dat weer langsaam."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Glücklik, hebbt wi Kontakt mit den Betriiver vun de Bibliothek kriegen, de togestimmt hett, uns all de Daten direkt to senden, wat veel schneller weer. Dat hett noch immer mehr as een halvt Johr dauen, all de Daten to överdragen un to verarbeiden, un wi hebbt fast allens an Diskkorruption verloren, wat bedeutet hett, vun vörn to beginnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Disse Erfahrung hett uns glöven laten, dat dat wichtig is, disse Daten so snel as mööglich rut to kriegen, so dat se wiet un breet speegelt warrt köönt. Wi sünd alleen een oder twee unglücklik getimede Vorfälle vun dat Verluren vun disse Sammlung för immer entfernt!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "De Sammlung"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Snel to bewegen bedeutet, dat de Sammlung een beten unorganisiert is… Laat uns een Blick dorop werfen. Stell di vör, wi hebben een Dateisystem (wat wi in Wirklikkeit över Torrents opdeelen):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "De eerste Ordner, <code>/repository</code>, is de meer struktureerte Deel dorvun. Disse Ordner enthält de so-köönt „Tausend-Dirs“: Ordner, je mit een Tausend Dateien, de in de Datenbank inkrementell nummeriert sünd. Ordner <code>0</code> enthält Dateien mit comic_id 0–999, un so wieter."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dit is datseelvde Schema, dat Library Genesis för sien Sammlungen vun Fikschon un Non-Fikschon bruukt. De Idee is, dat elke „Tausend Dir“ automaatsch in en Torrent verwandelt warrt, so gau as dat füllt is."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Awer de Libgen.li-Operator hett nie Torrents för disse Sammlung maakt, un so sünd de düsenden Dirs wahrscheinlik unpraktisch worrn, un hebbt Platz maakt för „unsorted dirs“. Disse sünd <code>/comics0</code> dörch <code>/comics4</code>. Se all hebbt unieke Verzeichnisstrukturen, de wahrscheinlik för dat Sammeln vun de Dateien Sinn maakt hebbt, awer nu nich mehr för uns. Glücklicherweise verweist de Metadata noch direkt op all disse Dateien, also maakt de Speicherorganisation op de Disk eigentlik keen Unterschied!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "De Metadata is in Form vun en MySQL-Datenbank verfügbar. Disse kann direkt vun de Libgen.li-Websteed daunloadt warrn, aver wi maakt se ok in en Torrent verfügbar, tosamen mit uns egen Tabel mit all de MD5-Hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Wenn du 95TB in dien Speicherklooster dumpst, versöchst du to verstahn, wat dor all in is… Wi hebbt en beten Analyse maakt, um to sehn, of wi de Grött wat verkleinern köönt, so as dör dat Entfernen vun Dubletten. Hier sünd en poor vun uns Resultaten:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantische Dubletten (verschiedene Scans vun dat sülvige Book) köönt theoretisch rutfiltert warrn, aver dat is tricky. Bi dat manuelle Dörchsehn vun de Comics hebbt wi toveel falsche Positiven funnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Dor sünd en poor Dubletten, die blots dör MD5 sünd, wat relativ verschwenderisch is, aver dat Rutfilteren vun disse würd uns blots so ümtrent 1%% an Sparen geven. Op disse Grött is dat noch ümtrent 1TB, aver ok, op disse Grött maakt 1TB nich wirklich wat ut. Wi würd leever keen Risiko ingahn, dör disse Prozess Daten versehentlich to vernichten."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Wi hebbt en Bünn non-Book-Daten funnen, so as Filme, de op Comics baseren. Dat schient ok verschwenderisch, denn disse sünd al över annere Weegen wiet verfügbar. Aver wi hebbt verstahn, dat wi nich einfach Filmdateien rutfilteren köönt, denn dor sünd ok <em>interaktive Comics</em>, de op den Computer rutkamen, de een opnahmen un as Filme spiekert hebbt."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Letztlich würd allens, wat wi ut de Kollekschoon löschen köönt, blots en poor Prozent sparn. Denn hebbt wi uns dran erinnert, dat wi Datenhorters sünd, un de Lüüd, de dat spegeln, sünd ok Datenhorters, un so, „WAT MEENST DU, LÖSCHEN?!“ :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Wi präsenteern di dorüm de ganze, unmodifizierte Kollekschoon. Dat is en Haufen Daten, aver wi hoopt, dat genug Lüüd sik dorüm kümmer, dat to seeden."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Spendenaktion"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Wi freet disse Daten in en poor grote Stücken rut. De eerste Torrent is vun <code>/comics0</code>, de wi in een grote 12TB .tar-Datei packt hebbt. Dat is beter för dien Festplaten un Torrent-Software as en Gazillion lütte Dateien."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "As Deel vun disse Freigave maakt wi en Spendenaktion. Wi versöken $20,000 to sammlen, um de Betriebskosten un Vertragskosten för disse Kollekschoon to decken, as ok för de Fortführung un de Zukünftige Projekten. Wi hebbt en poor <em>massive</em> in de Maak."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Wen ünnerstütte ik mit mien Spende?</em> In kort: wi sichert all dat Wissen un de Kultur vun de Minschheit un maakt se lichten togänglig. All uns Code un Daten sünd Open Source, wi sünd en komplett vun Friewilligen drievn Projekt, un wi hebbt bis nu 125TB an Böker spiekert (naast de bestehenden Torrents vun Libgen un Scihub). Letztlich bauw wi en Schwungrad, dat Lüüd motiviert un ünnerstützt, all Böker in de Welt to funnen, to scannen un to sichern. Wi schrieven över uns Masterplan in en tokomstigen Beitrag. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Wenn du för en 12-Monate „Amazing Archivist“-Mitgliedschaft ($780) spendst, kannst du <strong>„en Torrent adopteern“</strong>, wat bedüüt, dat wi dien Username oder Botschaft in den Dateinamen vun een vun de Torrents sett!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Du kannst spenden, indem du na <a %(wikipedia_annas_archive)s>Annas Archiv</a> geihst un op den „Spenden“-Knopp klickst. Wi söken ok noch mehr Friewillige: Software-Ingenieure, Sicherheitsforscher, anonyme Händler-Experten un Översetters. Du kannst uns ok ünnerstütte, indem du Hosting-Dienste bereitstellst. Un natüürlich, seed uns Torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dank an all, de uns al so großzügig ünnerstützt hebbt! Du maakt wirklich en Unterschied."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hier sünd de Torrents, de bis nu freet worrn sünd (wi sünd noch bi dat Verarbeiden vun de Rest):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "All Torrents sünd op <a %(wikipedia_annas_archive)s>Annas Archiv</a> ünner „Datasets“ to finnen (wi linken dor nich direkt, so dat Links to disse Blog nich vun Reddit, Twitter, etc. rutnahmen warrn). Vun dor, follg den Link to de Tor-Websteed."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wat kümmt as nächstes?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "En Bünn Torrents sünd großartig för de langfriste Sichern, aver nich so veel för den dagtäglichen Toggäng. Wi wüllt mit Hosting-Partners arbeiden, um all disse Daten up de Web to kriegen (denn Annas Archiv hostet nix direkt). Natüürlich kannst du disse Daunload-Links op Annas Archiv finnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Wi lad ok all Lüüd in, wat mit disse Daten to maken! Help uns, se beter to analysieren, to deduplizieren, up IPFS to sett, to remixen, dien KI-Modelle mit to trainieren, un so wieter. Dat is all dien, un wi köönt nich afwarten, wat du dor mit maakt."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Togst, as seggt vörher, wi hebbt noch een poor groote Releases in’t Vörhaben (wenn <em>een</em> uns <em>tofoällig</em> en Dump vun en <em>besünners</em> ACS4-Datenbank schicken deit, ji weet, wo wi to finnen sünd…), as ok dat Schwungrad to bouwen för dat Sichern vun all Böker op de Welt."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Blifft dorbi, wi sünd erst an’n Anfang."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nee Böker to de Piratenbibliothek-Spegel tofögt (+24TB, 3,8 Millionen Böker)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In de orschinalen Release vun de Piratenbibliothek-Spegel (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>), hebbt wi en Spegel vun Z-Library maakt, en grote illegal Bökerkollektion. As en Erinnerung, dit hebbt wi in dat orschinalen Blogpost schreven:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library is en populäre (un illegal) Bibliothek. Se hebbt de Sammlungen vun Library Genesis nahnomen un makt se lecht dörchsöken. Doröver henut, sünd se heel effektiv worrn in’t anlocken vun nee Bookbidrääg, dördat se de Bidrääger mit verscheden Vorteele belohnt. Se dragen disse nee Booken nu nich wedder to Library Genesis bietragen. Un anners as Library Genesis, maken se ehr Sammlungen nich lecht to spegeln, wat de wietverbreide Erhollen verhindert. Dit is wichtig för ehr Geschäftsmodell, denn se rekent Geld för den Toegang to ehr Sammlungen in grote Mengen (mehr as 10 Booken pro Dag)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Wi maken keen moralische Oordelen över dat Rekenen vun Geld för den Toegang to en illegal Booksammlung in grote Mengen. Dat is över allen Twivel, dat de Z-Library erfolgriek worrn is in’t Utbreiden vun den Toegang to Wissen un dat Sourcen vun mehr Booken. Wi sünd hier einfach, um uns Deel to doon: de langfriste Erhollen vun disse private Sammlung to sichern."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Disse Sammlung geiht trüch to Mitte 2021. In de Tied hett de Z-Library sik mit en enormen Tempo utweitet: se hebbt ümtrent 3,8 Millionen nee Booken tofögt. Dor sünd wat Dubletten bi, dat is klar, man de Mehrheit schient legitim nee Booken to wesen, oder Booken mit höger Kwalität vun de Vörlagen. Dit is in grooten Deel dör de vermehrte Anzohl vun ehrenamtlichen Moderatoren bi de Z-Library, un ehr System för de Massen-Uploads mit Dublettenerkennung. Wi wüllt se to disse Errungenschaften gratulieren."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Wi sünd froh to verkunnen, dat wi all Booken kregen hebbt, de twischen unsen letzten Spiegel un August 2022 to de Z-Library tofögt worrn sünd. Wi hebbt ok wedder wat Booken afsökt, de wi bi de eerste Mal översehn hebbt. Allens in all, is disse nee Sammlung ümtrent 24TB groot, wat veel grötter is as de letzte (7TB). Uns Spiegel is nu in’t Ganse 31TB groot. Wedder hebbt wi gegen Library Genesis dedupliziert, denn dor sünd al Torrents för disse Sammlung verfügbar."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Gahn Se to de Pirate Library Mirror, um de nee Sammlung to ankieken (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Dor is mehr Information doröver, wo de Dateien strukturiert sünd, un wat sik siet de letzte Mal ännert hett. Wi wüllt hier keen Link to geven, denn dit is blots en Blog-Websteed, de keen illegal Materialen host."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Natüürlich is dat Seedn ok en grootartige Weg, uns to helpen. Dankt an all, de uns vörrige Set vun Torrents seedn. Wi sünd dankbar för de positive Reaktion, un froh, dat dor so veel Lüüd sünd, de sik för de Erhollen vun Wissen un Kultur op disse ongewöhnliche Weg interessieren."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Wo een Piratenarchivar warrn"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "De eerste Utfordrung könnt en överraschende wesen. Dat is keen technisches Problem, oder en legal Problem. Dat is en psychologisches Problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Vördat wi in’t Thema diven, twee Updates över de Pirate Library Mirror (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Wi hebbt en poor heel großzügige Spenden kregen. De eerste weer $10k vun en anonymen Individuum, de ok \"bookwarrior\", den oorsprünglichen Grunner vun Library Genesis, ünnerstütt hett. Besünnere Dank an bookwarrior för dat Vermitteln vun disse Spende. De twete weer nochmaals $10k vun en anonymen Spender, de sik na unsen letzten Release melde un inspirert worrn is, to helpen. Wi hebbt ok en Anzohl vun lütter Spenden kregen. Dankt veelmals för all Johr großzügige Ünnerstüttung. Wi hebbt en poor spannende nee Projekte in de Pipeline, de dorvun ünnerstütt warrt, also bliev dorbi."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Wi hebbt en poor technische Swierigkeiten mit de Grött vun unsen tweten Release harrt, man uns Torrents sünd nu up un seedn. Wi hebbt ok en großzügige Offer vun en anonymen Individuum kregen, uns Sammlung op ehr heel-schnelle Servern to seedn, also maken wi en besünneren Upload to ehr Maschinen, na wat all de annern, de de Sammlung runterladen, en grooten Verbeterung in de Geschwindigkeit sehn schüllt."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Ganze Booken köönt schreven warrn över dat <em>warum</em> vun digitaler Erhollen in’t Ganse, un Piratenarchivismus in’t Besünnere, man laten wi en korten Vörtrag geven för de, de nich to veel doröver weten. De Welt produziert mehr Wissen un Kultur as jümmer vörher, man ok mehr dorvun geiht verloren as jümmer vörher. De Minschheit vertraut grootendeels op Firmen as akademische Verläger, Streamingdienste, un soziale Medien mit dit Erbe, un se hebbt sik oft nich as grote Bewahrers bewiesen. Kiekt de Dokumentation Digital Amnesia an, oder eigentlik een Talk vun Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Dor sünd en poor Institutionen, de en goede Job doon, so veel as mööglich to archivieren, man se sünd an’t Recht bunden. As Piraten sünd wi in en unieke Position, Sammlungen to archivieren, de se nich antasten köönt, wegen Urheberrecht oder anner Beschränkungen. Wi köönt ok Sammlungen veelfach spegeln, över de Welt verteilt, un dorbi de Chancen för en ordentliche Erhollen erhöhen."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "För nu wüllt wi keen Diskussioon över de Vor- un Nachteile vun intellektuelle Eigentum, de Moralität vun Gesetzesbrüch, Überlegungen över Zensur, oder de Froog vun Toegang to Wissen un Kultur führen. Mit all dat ut de Weg, laten wi in’t <em>wie</em> diven. Wi deelt, wo uns Team Piratenarchivare worrn is, un de Lessen, de wi op de Weg leert hebbt. Dor sünd veel Utfordrungen, wenn een disse Reis antreten deit, un hoffentlich köönt wi Johr dorbi helpen."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Gemeenschap"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "De eerste Utfordrung könnt en överraschende wesen. Dat is keen technisches Problem, oder en legal Problem. Dat is en psychologisches Problem: disse Arbeit in’t Verborgene to doon, kann heel einsam wesen. Afhängig vun wat Se van plan sünd to doon, un ehr Bedrohungsmodell, mööt Se heel vörsichtig wesen. An een Enn vun’t Spektrum hebbt wi Lüüd as Alexandra Elbakyan*, de Grunnerin vun Sci-Hub, de heel open över ehr Aktiviteten is. Man se is in groot Risiko, verhaftet to warrn, wenn se nu en westliches Land besöken deit, un köönt Jahrzehnte in’t Gefängnis stecken. Is dat en Risiko, dat Se bereit sünd to nehmen? Wi sünd an’t anner Enn vun’t Spektrum; heel vörsichtig, keen Spuren to laten, un mit starke operationelle Sicherheit."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* As op HN vun \"ynno\" erwähnt, Alexandra wull eerst nich bekannt wesen: \"Ihren Servern weer so instellt, dat se detaillierte Fehlermeldungen vun PHP utgeven, inklusiv de ganze Pfad vun de fehlerhafte Quelldatei, de ünner de Verzeichnis /home/<USER>" Also, bruuk random Benutzernamen op de Computer, de Se för disse Sachen bruukt, för den Fall, dat Se wat falsch konfiguriert hebbt."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Disse Geheimhaltung, allerding, kümmt mit en psychologischen Pries. De meisten Lüüd leevt dat, för ehr Arbeit annerkannt to warrn, un doch köönt Se keen Kredit för dit in’t echte Leven nehmen. Sogar einfache Dingen köönt en Utfordrung wesen, as Frünnen, de Se frogen, wat Se so treckt hebbt (op een Tiedpunkt wurrt \"mit mien NAS / Homelab rumfummeln\" alt)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dit is, worüm dat so wichtig is, een Gemeenschop to finnen. Ji könnt een beten vun de operationalen Sekerheit opgeven, dördat ji een paar ganz nahen Frünnen vertrauen, de ji wiest, dat ji se dörch un dörch vertrauen könnt. Ok denn, passt op, nix to schrieven, falls se ehr E-Mails an de Autoritäten övergeven mööt, oder wenn ehr Geräten op een anner Art un Wies kompromittiert sünd."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Noch beter is, een paar anner Piraten to finnen. Wenn ehr nahen Frünnen Interesse hebben, mit to maken, super! Anners könnt ji ok online anner finnen. Leider is dat noch een Nischen-Gemeenschop. Bislang hebben wi blots een Handvull annere funnen, de in dissen Bereich aktiv sünd. Gode Startpunkten scheinen de Library Genesis-Foren un r/DataHoarder to wesen. De Archive Team hett ok glieksinnige Lüüd, obschon se binnen de Gesett (ok wenn in griese Gebieten vun de Gesett) arbeiden. De traditionelle \"warez\"- un Piraterie-Szenen hebben ok Lüüd, de ähnlich denken."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Wi sünd open för Ideen, woans wi Gemeenschop fördern un Ideen utfoorschen köönt. Schreibt uns giern op Twitter oder Reddit. Veellicht köönt wi een Art Forum oder Chat-Gruppe hosten. Een Utforderring is, dat dat leicht censurert warrn kann, wenn wi gängige Plattformen bruken, also mööt wi dat sülvst hosten. Dat gifft ok een Afwägen twischen de Diskusschoonen ganz public maken (mehr potentiale Deelnehm) un dat privat maken (nich de potentiale \"Targets\" weten laten, dat wi se bald scrapen). Dat mööt wi noch överleggen. Laat uns weten, wenn ji dor Interesse an hebben!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekten"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Wenn wi een Projekt maken, hett dat een paar Phasen:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domeinwahl / Philosophie: Woans wullt ji grob fokussieren, un worüm? Wat sünd ehr unieke Passioonen, Fertigkeiten un Umstänn, de ji to ehr Vorteil bruken könnt?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Targetwahl: Welke spesifische Sammlung wullt ji spegeln?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata-Scraping: Katalogisieren vun Informatschonen över de Dateien, ahn de (oft veel gröttere) Dateien sülvst to downloaden."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Datenauswahl: Basierend op de Metadata, ingrenzen, welke Daten nu am relevantesten sünd to archiveren. Könnt allens wesen, aver oft gifft dat een vernünftige Weg, Raum un Bandbreite to sparen."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Daten-Scraping: De Daten tatsächlich kriegen."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution: Dat in Torrents verpacken, dat irgendo antoonen, Lüüd kriegen, dat to verbreiden."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Disse sünd nich ganz unabhängige Phasen, un oft bringen Erkenntnisse ut een lateren Phase ji torüch to een frühren Phase. Zum Beispiel, bi Metadata-Scraping könnt ji merken, dat dat Target, dat ji utwählt hebben, Verteidigungsmechanismen hett, de över ehr Fertigkeitsniveau gahn (as IP-Sperren), also gahn ji torüch un finnen een anner Target."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domeinwahl / Philosophie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Dat gifft keen Mangeln an Wissen un kulturellen Erven, de to retten sünd, wat överwältigend wesen kann. Darüm is dat oft nützlich, een Moment to nehmen un to denken, wat ehr Beitrag wesen kann."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Jeder hett een anneren Weg, doröver to denken, aver hier sünd een paar Fragen, de ji sülvst stellen köönt:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Worüm hebt ji Interesse doran? Wat is ehr Leidenschaft? Wenn wi een Haufen Lüüd kriegen köönt, de all de Art vun Dingen archiveren, de se spesifisch wichtig finnen, dat würd veel decken! Ji weet veel mehr as de durschnittliche Persoon över ehr Leidenschaft, as wat wichtige Daten to retten sünd, wat de besten Sammlungen un online Gemeenschoppen sünd, un so wieter."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Wat för Fertigkeiten hebbt ji, de ji to jüm Vorteil nutzen könnt? För'n Bispill, wenn ji en Online-Sicherheits-Expert sünd, könnt ji Weegen finnen, IP-Sperren för sichere Ziele to överwinden. Wenn ji grootartig in't Organisieren vun Gemeenschappen sünd, denn könnt ji vielleicht eenige Lüüd üm en Ziel versammeln. Dat is nützlich, wenn ji een beten Programmieren könnt, wenn ok blots för gute operationelle Sicherheit dorch disse Prozess to behollen."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Wieviel Tied hebbt ji för dit? Us Raat wörrd wesen, kleen to beginnen un gröttere Projekten to doon, as ji de Handgriffe kriegt, aver dat kann allumfassend wesen."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wor sünd dat Höögheffekts-Arealen, op de man sik fokussieren schall? Wenn ji X Stünnen an Piratenarchivering spenden, wo kriegt ji denn de gröttste \"Bang för jüm Buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wat sünd de unieke Weegen, de ji dorbi bedenkt? Ji köönt interessante Ideen oder Ansäten hebben, de annere mööglicherwies översehn hebben."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In unsen Fall, wi hebben uns spesifisch för de langfriste Erhaltung vun de Wissenschaft interessiert. Wi wisten över Library Genesis, un woans dat veelmol mit Torrents spegelt worrn is. Wi hebben de Idee gemocht. Denn een Dag, een vun uns versöcht, een paar wissenschaftliche Lehrböker op Library Genesis to finnen, aver kunn se nich finnen, wat de Vollständigkeit in Frage stellt. Wi hebben denn disse Lehrböker online söcht un se an annern Steden funnen, wat de Idee för uns Projekt gepflanzt hett. Noch bevor wi över de Z-Library wisten, hadden wi de Idee, nich all disse Böker manuell to sammeln, aver to fokussieren op dat Spegeln vun bestehenden Sammlungen, un se torüch to Library Genesis to bringen."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Targetwahl"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Also, wi hebben uns Bereich, den wi ankieken, nu welke spesifische Sammlung wullt wi spegeln? Dat gifft een paar Dingen, de een good Target utmaken:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Groot"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Uniek: nich all to'n annern Projekten good bedekt."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Toegänglich: bruukt nich tonnen vun Schichten vun Schutz, üm di dervun to hinnern, ehr Metadata un Daten to scrapen."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Besünner Insicht: du hest wat besünners över dit Ziel, as dat du op een oder anner Weg besünneren Tohgang to disse Sammlung hest, oder du hest utfunnen, wie du ehr Verteidigung överwinnst. Dit is nich nödig (unser kommande Projekt deit nix besünners), aver dat helpt seker!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "As wi unsere Wissenschaftsböker op annern Websieden as Library Genesis funnen hebbt, hebbt wi versöcht, rut to finnen, wie se ehr Weg in't Internet funnen hebbt. Wi hebbt denn de Z-Library funnen un begrepen, dat während de meisten Böker nich eerst dor updocht, se dor doch letztendlich landen. Wi hebbt över ehr Verknüppung mit Library Genesis un de (finanzielle) Anreizstruktur un överläggen Brukeroberfläche lehrt, de se to en veel vollständiger Sammlung maakt. Wi hebbt denn wat vörläufige Metadata un Daten scrapen maakt un begrepen, dat wi ehr IP-Download-Limits ümgehn köönt, indem wi de besünnere Tohgang vun een vun unsen Medlemmers to vele Proxy-Servern utspelen."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "As du verschedenen Ziele explorierst, is dat al wichtig, dien Sporen to verbergen, indem du VPNs un Wegwerf-E-Mail-Adressen bruukst, wat wi later noch mehr bespreken wüllt."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata-Scraping"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Laten wi wat technischer warrn. För dat eigentliche Scrapen vun Metadata vun Websieden hebbt wi dat ganz simpel holden. Wi bruuken Python-Skripten, manchmol curl, un en MySQL-Datenbank, üm de Resultaten to speicheren. Wi hebbt keen fancy Scraping-Software bruukt, de komplexe Websieden mappen kann, denn bislang hebbt wi blots een oder twee Siedenarten scrapen müsst, indem wi einfach dörch IDs enumeriert un den HTML parst hebbt. Wenn dor keen einfach to enumerierende Sieden sünd, denn bruukst du villicht en ordentlichen Crawler, de versöcht, all Sieden to finnen."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Vör dat du en ganze Websied scrapst, versöök dat manuell för en beten. Gah dörch en paar Dutzend Sieden sülvst, üm en Gefühl dorför to kriegen, wie dat geiht. Manchmol lööfst du op dissen Weg al in IP-Sperren oder anner interessant Verhollen rin. Dat gellt ok för dat Daten-Scraping: vör dat du to deep in dit Ziel rin geihst, stell seker, dat du ehr Daten effektiv runterladen kannst."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Üm Restriktionen to ümgehn, gifft dat en paar Dingen, de du versöken kannst. Gifft dat anner IP-Adressen oder Servern, de de sülven Daten hosten, aver nich de sülven Restriktionen hebbt? Gifft dat API-Endpunkte, de keen Restriktionen hebbt, während anner dat hebbt? Bi welk Download-Tempo warrt dien IP blockt, un för wie lang? Oder warrst du nich blockt, man drosselt? Wat, wenn du en Brukerkonto anlegst, wie ännert sik dat denn? Kannst du HTTP/2 bruuken, üm Verbindungen op to holden, un erhöht dat dat Tempo, mit dat du Sieden anfragen kannst? Gifft dat Sieden, de mehrfache Dateien op eenmol oplisten, un is de dor oplistete Information togenüch?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dingen, de du villicht speicheren wullt, sünd:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Dateinaam / Standort"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kann en interne ID wesen, man IDs as ISBN oder DOI sünd ok nuttig."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Grött: üm to berechnen, wieveel Diskplatz du bruukst."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): üm to bestätigen, dat du de Datei korrekt runterladen hest."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum tofögt/änderd: so kannst du later wedderkamen un Dateien runterladen, de du vörher nich runterladen hest (obwohl du ok oft de ID oder Hash dorför bruuken kannst)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beschrievung, Kategorie, Tags, Autoren, Spraak, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Wi maakt dat typisch in twee Stufen. Eerst laden wi de rohe HTML-Dateien runter, meist direkt in MySQL (üm vele lütte Dateien to vermeiden, wat wi later noch mehr bespreken wüllt). Denn, in en separaten Schritt, gah wi dörch disse HTML-Dateien un parsen se in echte MySQL-Tabellen. So musst du nich allens vun vörn anfangen, wenn du en Fehler in dien Parsing-Code upptäckst, denn du kannst einfach de HTML-Dateien mit den niegen Code wedderverarbeiten. Dat is ok oft leichter, den Verarbeitungs-Schritt to parallelisieren, so dat du Tied sparst (un du kannst den Verarbeitungs-Code schrieven, während dat Scraping löppt, statt beide Schritte op eenmol to schrieven)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Togoodt, merk op, dat för eenige Ziele Metadata-Scraping allens is, wat dor is. Dor sünd eenige grote Metadata-Sammlungen dorbuiten, de nich richtig bewohrt sünd."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Datenselektioon"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Oftens künnt ji de Metadata bruken, üm en redeliken Deel vun de Daten ut to söken, de ji herunnerladen wüllt. Ok, wenn ji all de Daten herunnerladen wüllt, kann dat nützlich wesen, de wichtigsten Saken eerst to priooritiseren, falls ji entdeckt warrt un de Verteidigung verbeetert warrt, oder weil ji mehr Disken köpen müsst, oder einfach, weil wat anners in jüm Leben upkamen deit, vördat ji allens herunnerladen künnt."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "As Bispill kann en Samlung mehrfache Utgaven vun de sülven Ressource hebben (as en Book oder en Film), wo een as de beste Kwaliteit markert is. Disse Utgaven eerst to spiekern maakt veel Sinn. Ji künnt eventuell all Utgaven spiekern wüllen, denn in eenige Fallen kann de Metadata verkehrt markert wesen, oder dor köönt onbekannte Kompromissen twischen de Utgaven wesen (as Bispill, de \"beste Utgave\" kann in de meisten Aspekten de beste wesen, aver schlechter in annere, as en Film mit höger Upplösing, aver ohne Ünnertitels)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Ji künnt ok in jüm Metadata-Datenbank söken, üm interessante Saken to finnen. Wat is de gröttste Datei, de hostet warrt, un woans is se so groot? Wat is de lüttste Datei? Gift dat interessante oder onverwacht Muster, wenn dat üm bestimmte Kategorien, Spraken, un so wieter geiht? Gift dat dubble oder heel ähnliche Titels? Gift dat Muster, wenn Daten tofögt warrt, as een Dag, an den veel Dateien op eenmal tofögt warrt? Ji künnt veel leeren, wenn ji de Datenset op verschiddene Weegen ankiekt."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In uns Fall hebbt wi de Z-Library-Böker deduplizeert gegen de md5-Hashes in Library Genesis, un dorbi veel Herunnerladentied un Diskspieker spaaren. Dit is een heel unieke Situatschoon. In de meisten Fallen gift dat keen ümfattende Datenbanken, welke Dateien al korrekt von annere Piraten spiekert warrt. Dit is sülvst een grote Gelegenheid för een, de dorbuiten is. Dat weer grootartig, een regelmääßig aktualiseert Överblick vun Saken as Musik un Filmen to hebben, de al wiet verbreitet op Torrent-Websieden seedet warrt, un dorüm en lüttere Priorität för de Inklusion in Piraten-Speegels hebben."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Datenscraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nu sünd ji bereit, de Daten in grooten Mengen herunner to laden. As vörher seggt, an dissen Punkt schullt ji al manuell een Bunt vun Dateien herunnerladen hebben, üm dat Verhoolden un de Restriktschoonen vun dat Ziel beter to verstoahn. Aver dor wüllt noch Överraschungen op ji warten, wenn ji veel Dateien op eenmal herunnerladen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Uns Raat hier is, dat simpel to holden. Fangt an, indem ji einfach een Bunt vun Dateien herunnerladen. Ji künnt Python bruken, un denn op mehrfache Threads utweiden. Aver manchmol is dat noch simpler, Bash-Dateien direkt ut de Datenbank to genereren, un denn mehrfache vun den in mehrfache Terminalfensters to lopen, üm dat up to skaalen. Een schnellt technisches Trick, dat hier erwähnenswäert is, is dat Bruken vun OUTFILE in MySQL, dat ji överall schrieven künnt, wenn ji \"secure_file_priv\" in mysqld.cnf deaktiviert (un sörgt dat ji ok AppArmor deaktiviert/överridet, wenn ji Linux bruken)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Wi spiekern de Daten op einfache Hartdisken. Fangt an mit wat ji hebben, un weitet dat langsam ut. Dat kann överwältigend wesen, an dat Spiekern vun hunderte TBs vun Daten to denken. Wenn dat de Situatschoon is, de ji anblickt, sett einfach een guten Deel eerst ut, un in jüm Ankündigung frogt för Hülp bi dat Spiekern vun de Rest. Wenn ji sülvst mehr Hartdisken köpen wüllt, denn hett r/DataHoarder eenige gute Ressourcen för dat Kriegen vun guten Deals."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Versökt, nich to veel över fancy Dateisysteme to grübbeln. Dat is einfach, in dat Kaninchenloch vun dat Opstellen vun Saken as ZFS to fallen. Een technisches Detail, dat ji bewust wesen schullt, is dat veel Dateisysteme nich gut mit veel Dateien klarkomen. Wi hebbt funnen, dat een einfache Umweg is, mehrfache Dossiers to maken, z.B. för verschiddene ID-Reeken oder Hash-Präfixen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Na dat Herunnerladen vun de Daten, sörgt dat ji de Integrität vun de Dateien mit Hashes in de Metadata prövt, wenn verfügbar."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Ji hebbt de Daten, un dorüm hebbt ji (höchstwahrscheinlich) de erste Piraten-Speegel vun jüm Ziel in Besitz. In veel Weegen is de swierste Deel vörbi, aver de riskanteste Deel liggt noch vör ji. Bisher weer ji unopfallend; unner de Radar fliegend. All wat ji doon musst, weer een guten VPN to bruken, keen persöönliche Details in Formulare to infüllen (natürlich), un vleicht een spezielle Browser-Session (oder sülvst een annern Computer) to bruken."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nu müsst ji de Daten verteilen. In uns Fall wüllt wi eerst de Böker torüch to Library Genesis beitragen, aver hebbt denn schnell de Swierigkeiten dorin funnen (Fiktschoon vs. Non-Fiktschoon Sorteren). So hebbt wi besluten, de Distribution mit Library Genesis-Stil Torrents to doon. Wenn ji de Gelegenheid hebbt, an een bestaand Projekt to bijdragen, denn kann dat ji veel Tied sparen. Aver dor sünd nich veel goed organisierte Piraten-Speegels dorbuiten."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Laten wi seggen, du beslüts, Torrents sülvst to verteilen. Versöök, disse Dateien lütt to holden, so dat se einfach op annern Websieden to spiegeln sünd. Du musst denn de Torrents sülvst seeden, während du anonym bliebst. Du kannst en VPN bruuken (mit oder ahn Port-Forwarding), oder mit getumblten Bitcoins för en Seedbox betalen. Wenn du nich weet, wat een paar vun disse Begrippen bedüden, hest du en ganze Reeg Lektüre vör di, denn dat is wichtig, dat du de Risiko-Abwägungen hier versteihst."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Ji könnt de Torrentdateien sülvst op bestahn Torrentwebsites hosten. In uns Fall hebbt wi besluten, en Website to hosten, denn wi wüllt ok uns Philosophie op en kloren Weg verbreiden. Ji könnt dit sülvst op en ähnlichen Weg doon (wi bruukt Njalla för uns Domänen un Hosting, betahlt mit getumblten Bitcoins), aver ji könnt ok gern Kontakt mit uns opnehmen, dat wi jihr Torrents hosten. Wi wüllt över Tied en ümfangriken Index vun Piratenspegeln opbouwen, wenn dit Idee Anklang finnt."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Wat VPN-Utwahl angahn deit, is dor al veel över schreven worrn, dorüm weerholt wi blots de algemeene Ratschlag, na Reputation to kiezen. Faktisch Gericht-getestete No-Log-Politiken mit lange Historie vun Datenschutz sünd de Risikoärmste Optioon, in uns Meenung. Merkt, dat ok wenn ji allens richtig maakt, ji nie to nul Risiko kömmt. As Bispill, wenn jihr Torrents seedt, kann en hoch motiverte Nation-State-Actor wahrschienlich de inkomende un utgaande Datenströme för VPN-Servern ankieken un herleiden, wo ji sünd. Oder ji könnt einfach en Fehler maken. Wi hebbt dat wahrschienlich al, un wüllt dat wedder doon. Glücklicherweise kümmt Nationen nich so veel <em>darüm</em> üm Piraterie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Een Besluten för elke Projekt is, ob dat mit de sülve Identität as vörher to publizieren, oder nich. Wenn ji de sülve Naam bruken, denn köönt Fehler in de operatschoonelle Sekerheit vun vörherige Projekte torüchkommen, üm ji to beißen. Aver dat Publizieren unner verschiddene Namen betekent, dat ji keen länger bestahn Ruf opbauen. Wi hebbt besluten, starke operatschoonelle Sekerheit vun Anfang an to hebben, so dat wi de sülve Identität bruken künnt, aver wi wüllt nich twiefeln, unner een annern Naam to publizieren, wenn wi een Fehler maken oder wenn de Umständ dat nödig maakt."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Dat Wort to verbreiten kann tricky wesen. As wi seggt hebbt, is dit noch een Nischen-Community. Wi hebbt ursprünglich op Reddit postet, aver hebbt wirklich Traktion op Hacker News kregen. För nu is uns Empfehlung, dat in eenige Plaatsen to posten un to kieken, wat passiert. Un nochmaals, kontaktiert uns. Wi wüllt giern dat Wort vun mehr Piraten-Archivismus-Bemühungen verbreiten."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Slussfolgerung"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hoffentlich is dit nützlich för neuanfangende Piraten-Archivisten. Wi sünd opregt, ji in disse Welt to begrüßen, so twiefelt nich, uns to erreichen. Laat uns so veel vun de Welt's Wissen un Kultur as mööglich bewaren, un dat wiet un breet speegeln."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Vörstellen de Piraten-Bibliothek-Speegel: Bewahren 7TB vun Böker (de nich in Libgen sünd)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dit Projekt (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>) hett dat Ziel, to de Bewahring un Befreiung vun de Minschenkenntnis bitrüch to dragen. Wi maken uns lütt un bescheidenen Baitrag, in de Footstappen vun de Groten vör uns."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Dat Fokus vun dit Projekt warrt dör den Naam verdeutlicht:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirat</strong> - Wi bruken mit Absicht dat Urheberrecht in de meisten Länner. Dit maakt uns dat mööglich, wat legale Institutionen nich köönt: Bücher wiet un breet to spigeln."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliothek</strong> - So as de meisten Bibliotheken, liggt uns Fokus vör allen op schrievne Materialien as Böker. Veellicht warden wi in de Zukunft ok annere Medientypen mit in Betracht trecken."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spiegel</strong> - Wi sünd strikt en Spiegel vun bestahn Bibliotheken. Wi liggt uns Fokus op de Bewahring, nich dorop, Böker einfach to dörsöken un to downloaden (Toegang) oder en grote Gemeenschop vun Lüüd to fördern, de ne’e Böker bitragen (Quellen)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "De erste Bibliothek, de wi spiegelt hebbt, is Z-Library. Dit is en populäre (un illegale) Bibliothek. Se hebbt de Library Genesis-Sammlung nahbar maakt. Doröver hen, hebbt se sik dorin effektiv maakt, ne’e Baiträgen vun Böker to kriegen, dördat se de bitragende Brukers mit verschedene Vorteele belohnt. Se dragen disse ne’e Böker aktuell nich wedder to Library Genesis bit. Un anners as Library Genesis, maken se ehr Sammlung nich einfach to spigeln, wat de wiet Bewahring verhinnern deit. Dit is wichtig för ehr Geschäftsmodell, denn se rekent Geld för den Toegang to ehr Sammlung in grote Mengen (mehr as 10 Böker pro Dag)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Wi maken keen moralische Oordelen över dat Rekenen vun Geld för den Toegang to en illegal Booksammlung in grote Mengen. Dat is över allen Twivel, dat de Z-Library erfolgriek worrn is in’t Utbreiden vun den Toegang to Wissen un dat Sourcen vun mehr Booken. Wi sünd hier einfach, um uns Deel to doon: de langfriste Erhollen vun disse private Sammlung to sichern."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Wi wüllt jüm to laden, to helpen, de Minschenkenntnis to bewahren un to befreien, dördat ji uns Torrents download un seed. Seht de Projekt-Sied för mehr Informatschoon, wo de Daten organiseert sünd."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Wi wüllt jüm ok ganz giern to laden, jüm Ideen to geven, welke Sammlungen as nächst to spigeln sünd, un wo dat to maken is. Tosamen köönt wi veel erreichen. Dit is blot en lütten Baitrag ünner unzählige annere. Dank, för all dat, wat ji doot."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Wi linken nich to de Dateien vun dit Blog. Bitte finnt se sülvst.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb Dump, oder Wieviele Böker sünd för immer bewahrt?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Wenn wi de Dateien vun Schattbibliotheken richtig deduplizieren würren, welk Prozentsatz vun all de Böker op de Welt hebbt wi bewahrt?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Mit den Piratenbibliothek-Spiegel (EDIT: verlegt na <a %(wikipedia_annas_archive)s>Annas Archiv</a>), is uns Ziel, all de Böker op de Welt to nehmen un för immer to bewahren.<sup>1</sup> Tüschen uns Z-Library Torrents un de originale Library Genesis Torrents hebbt wi 11.783.153 Dateien. Aber wieveel is dat, in echt? Wenn wi disse Dateien richtig deduplizieren würren, welk Prozentsatz vun all de Böker op de Welt hebbt wi bewahrt? Wi würren giern so wat hebben:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10%% vun de Minschheit ehr schrevene Heritage för immer bewaart"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "För en Prozentsatz bruukt wi en Nenner: de totale Anzohl vun Böker, de jemals utgeven wurrn sünd.<sup>2</sup> Vör de Enn vun Google Books, hett en Ingenieur vun dat Projekt, Leonid Taycher, <a %(booksearch_blogspot)s>versöcht, dissen Anzohl to schätzen</a>. He keem up — mit en Augenzwinkern — mit 129.864.880 („minstens bit Sünndag“). He hett dissen Anzohl schätzt, dördat he en eenheitliche Datenbank vun all de Böker op de Welt opbaut hett. För dit hett he verschedene Datasets tosammentragen un denn op verschedene Weegen tosammerführt."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "As en korte Sietensprung, gifft dat noch en annern Minsch, de versöcht hett, all de Böker op de Welt to katalogisieren: Aaron Swartz, de verstorvene digitale Aktivist un Reddit-Mitgründer.<sup>3</sup> He hett <a %(youtube)s>Open Library start</a> mit dat Ziel vun „een Websied för jeden Book, dat jemals utgeven wurrn is“, un hett Daten vun vele verschedene Quellen tosammentragen. He hett den höchsten Pris för sien digitale Bewahrungsarbeit betahlt, as he för dat massenhafte Downloaden vun akademischen Artikeln verfolgt wurrn is, wat to sien Suizid föhrt hett. Dat is een vun de Gründe, warrüm uns Grupp pseudonym is, un warrüm wi heel vörsichtig sünd. Open Library warrt noch immer heldenhaft vun de Lüüd bi Internet Archive leedt, de Aarons Vermächtnis fortsetten. Wi köönt later in dissen Post wedder dorop tospraken."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "In den Google Blogpost beschrievt Taycher eenige vun de Utforderrungen bi de Schätzung vun dissen Anzohl. Eerstens, wat maakt en Book ut? Dat gifft eenige möögliche Definitschoonen:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fysische Kopien.</strong> Dat is natüürlich nich heel hilfreich, denn dat sünd blot Duplikaten vun dat sülvst Material. Dat würr cool ween, wenn wi all de Anmerkungen, de Lüüd in Böker maken, bewahren köönt, as Fermats berühmt „Kritzeleien in de Rand“. Aber leider blifft dat en Traum för Archivaren."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Werke“.</strong> För Bispill „Harry Potter un de Kammer des Schreckens“ as en logisches Konzept, dat all Versionen dorvun umfast, as verschedene Översettungen un Neudrucke. Dit is en soort nützliche Definitschoon, aber dat kann swor ween, de Grenz to trekken, wat dor to tellt. För Bispill, wi wüllt veellicht verschedene Översettungen bewahren, obwool Neudrucke mit blot lütten Unnerscheden veellicht nich so wichtig sünd."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Ausgaben“.</strong> Hier tellt ji jede eenzigartige Version vun en Book. Wenn wat doran anners is, as en annere Umslag oder en anner Vörwort, tellt dat as en annere Ausgabe."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Dateien.</strong> Wenn man mit Schattenbibliotheken wie Library Genesis, Sci-Hub oder Z-Library arbeitet, gibt es eine zusätzliche Überlegung. Es kann mehrere Scans derselben Ausgabe geben. Und Menschen können bessere Versionen bestehender Dateien erstellen, indem sie den Text mit OCR scannen oder Seiten korrigieren, die schräg gescannt wurden. Wir möchten diese Dateien nur als eine Ausgabe zählen, was gute Metadata oder eine Deduplizierung mit Dokumentähnlichkeitsmaßen erfordern würde."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Ausgaben“ scheinen die praktischste Definition dessen zu sein, was „Bücher“ sind. Bequemerweise wird diese Definition auch zur Vergabe einzigartiger ISBN-Nummern verwendet. Eine ISBN, oder Internationale Standardbuchnummer, wird häufig für den internationalen Handel verwendet, da sie in das internationale Barcode-System („International Article Number“) integriert ist. Wenn Sie ein Buch in Geschäften verkaufen möchten, benötigt es einen Barcode, also erhalten Sie eine ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taychers Blogbeitrag erwähnt, dass, obwohl ISBNs nützlich sind, sie nicht universell sind, da sie erst Mitte der siebziger Jahre wirklich übernommen wurden und nicht überall auf der Welt. Dennoch ist die ISBN wahrscheinlich der am weitesten verbreitete Identifikator für Buchausgaben, daher ist sie unser bester Ausgangspunkt. Wenn wir alle ISBNs der Welt finden können, erhalten wir eine nützliche Liste, welche Bücher noch erhalten werden müssen."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Also, woher bekommen wir die Daten? Es gibt eine Reihe bestehender Bemühungen, die versuchen, eine Liste aller Bücher der Welt zu erstellen:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Schließlich haben sie diese Forschung für Google Books durchgeführt. Allerdings sind ihre Metadata nicht in großen Mengen zugänglich und ziemlich schwer zu scrapen."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Wie bereits erwähnt, ist dies ihre gesamte Mission. Sie haben riesige Mengen an Bibliotheksdaten von kooperierenden Bibliotheken und nationalen Archiven bezogen und tun dies weiterhin. Sie haben auch freiwillige Bibliothekare und ein technisches Team, das versucht, Datensätze zu deduplizieren und sie mit allen möglichen Metadata zu versehen. Am besten ist, dass ihr Dataset vollständig offen ist. Sie können es einfach <a %(openlibrary)s>herunterladen</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dies ist eine Website, die von der gemeinnützigen OCLC betrieben wird, die Bibliotheksverwaltungssysteme verkauft. Sie aggregieren Buchmetadata aus vielen Bibliotheken und stellen sie über die WorldCat-Website zur Verfügung. Allerdings verdienen sie auch Geld mit dem Verkauf dieser Daten, sodass sie nicht für den Massen-Download verfügbar sind. Sie haben einige begrenztere Massendatensätze zum Download verfügbar, in Zusammenarbeit mit bestimmten Bibliotheken."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dies ist das Thema dieses Blogbeitrags. ISBNdb scrapt verschiedene Websites nach Buchmetadata, insbesondere Preisdaten, die sie dann an Buchhändler verkaufen, damit diese ihre Bücher im Einklang mit dem Rest des Marktes bepreisen können. Da ISBNs heutzutage ziemlich universell sind, haben sie effektiv eine „Webseite für jedes Buch“ erstellt."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Verschiedene individuelle Bibliothekssysteme und Archive.</strong> Es gibt Bibliotheken und Archive, die von keiner der oben genannten indiziert und aggregiert wurden, oft weil sie unterfinanziert sind oder aus anderen Gründen ihre Daten nicht mit Open Library, OCLC, Google usw. teilen möchten. Viele davon haben digitale Aufzeichnungen, die über das Internet zugänglich sind, und sie sind oft nicht sehr gut geschützt, sodass, wenn Sie helfen möchten und Spaß daran haben, seltsame Bibliothekssysteme kennenzulernen, diese großartige Ausgangspunkte sind."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In diesem Beitrag freuen wir uns, eine kleine Veröffentlichung anzukündigen (im Vergleich zu unseren vorherigen Z-Library-Veröffentlichungen). Wir haben den Großteil von ISBNdb gescrapt und die Daten zum Torrenting auf der Website des Pirate Library Mirror verfügbar gemacht (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>; wir werden es hier nicht direkt verlinken, suchen Sie einfach danach). Dies sind etwa 30,9 Millionen Datensätze (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gezippt). Auf ihrer Website behaupten sie, dass sie tatsächlich 32,6 Millionen Datensätze haben, also könnten wir irgendwie einige verpasst haben, oder <em>sie</em> könnten etwas falsch machen. In jedem Fall werden wir vorerst nicht genau mitteilen, wie wir es gemacht haben — wir überlassen das als Übung dem Leser. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wat wi delen is een vörloopige Analyse, üm to versöken, de Tall von Böker in de Welt to schätzen. Wi hebbt trü Datasets ankeken: disse niege ISBNdb Dataset, unsen orsprünglichen Utgaav von Metadata, de wi ut de Z-Library Schadowbibliothek (wat Library Genesis inbegriept) utkratz hebbt, un den Open Library Datendump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Laten wi mit een paar grobe Tall anfungen:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In beiden Z-Library/Libgen un Open Library gifft dat veel mehr Böker as unique ISBNs. Bedüüt dat, dat veel von disse Böker keen ISBN hebbt, oder fehlt dat ISBN Metadata einfach? Wi köönt disse Froge wahrschienlich mit en Kombinatschoon von automaatschen Vergleiken op anneren Egenschappen (Titel, Autor, Verlag, etc.), dat Inholen von mehr Datenquellen, un dat Utkratzen von ISBNs ut de faktischen Böker-Scans sülven (im Fall von Z-Library/Libgen) beantworen."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Wieviel von disse ISBNs sünd unique? Dit is best mit en Venn-Diagramm to illustreren:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Um dat präziser to seggen:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Wi wöörn överrascht, dat dor so lütt Överlappt is! ISBNdb hett en groten Höög vun ISBNs, de nich in Z-Library oder Open Library vörkamen, un dat gellt (in en lüttje, aver noch immer bedüüdende Grad) ok för de annern beiden. Dit bröcht veel nee Froogen up. Wieviel würd en automaatsche Verknüppeln helpen bi dat Markeren vun Böker, de nich mit ISBNs markert sünd? Würr dor veel Verknüppeln sünd un dorüm en gröter Överlappt? Ok, wat schall passeren, wenn wi en 4. oder 5. Dataset inbröcht? Wieviel Överlappt würd wi denn sehn?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dat gifft uns en Startpunkt. Wi köönt nu all de ISBNs ankieken, de nich in’t Z-Library Dataset wesen sünd, un de ok nich Titel/Autor-Felder passen. Dat kann uns en Gripp geven, all de Böker op de Welt to bewaren: eerstens dör dat Internet för Scans to dörsöken, denn dör dat in’t echte Leven Böker to scannen. Dat letzte könnt ok dör Crowdfunding finanzeert warrn, oder dör „Bounties“ vun Lüüd, de spesifische Böker digitalisiert sehn wüllt. All dat is en Geschicht för en anner Tied."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Wenn ji bi wat vun dit helpen wüllt — wietere Analyse; mehr Metadata scrapen; mehr Böker finnen; OCR vun Böker; dat för annere Domänen doon (z.B. Papers, Audiobooks, Filme, TV-Shows, Tiedschriften) oder ok en Deel vun disse Daten för Dingen as ML / groote Spraakmodellen Training verfügbar maken — bitte kontaktiert mi (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Wenn ji spesifisch an de Datenanalyse interessiert sünd, wi arbeidt doran, uns Datasets un Skripten in en mehr togangliche Format verfügbar to maken. Dat wöör grootartig, wenn ji einfach en Notebook forken köönt un mit dat Spelen anfongen."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Endlich, wenn ji dit Arbeid ünnerstütten wüllt, bitte överlegt, en Spende to maken. Dit is en ganz freiwillig löpen Operation, un jihr Beitrag maakt en grooten Unnerscheed. Allens helpt. För nu nehmt wi Spenden in Krypto an; seht de Spenden-Sied op Anna’s Archive."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. För en redelijke Definitschoon vun \"för immer\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Natürlich is de schriftliche Heritüüd vun de Minschheit veel mehr as Böker, besünners hüüt. För de Zweck vun dit Post un uns neuste Friggaven fokussiere wi uns op Böker, aver uns Interessen geiht wieter."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Dat gifft veel mehr, wat över Aaron Swartz seggt warrn kann, aver wi wüllt em kurz erwähnen, denn he speelt en zentrale Rull in disse Geschicht. As de Tied vörbi geiht, köönt mehr Lüüd sien Naam för dat erste Mal hören, un sülvst in dat Kaninchenloch diven."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "De kritische Fenster vun Schattbibliotheken"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Wo köönt wi behaupen, uns Kollekschoonen för immer to bewaren, wenn se al bald 1 PB erreichen?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Chinesische Version 中文版</a>, diskuteer op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Bi Anna’s Archive warrt wi oft froogt, wo wi behaupen köönt, uns Kollekschoonen för immer to bewaren, wenn de totale Grött al bald 1 Petabyte (1000 TB) erreicht, un noch wööst. In dit Artikel kiek wi uns uns Philosophie an, un seht, wo de nächste Dekade kritisch för uns Misschoon is, de Kunn un Kultuur vun de Minschheit to bewaren."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "De <a %(annas_archive_stats)s>totale Grött</a> vun uns Kollekschoonen, över de letzten Maanen, opdeelt na de Anzohl vun Torrent-Seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritäten"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Wo kümmt dat, dat wi so veel Wert op Papers un Böker leggt? Laat uns uns grundlegende Gloven an Bewahren in’t Allgemene to Sieden stellen — wi köönt en anner Post doröver schrieven. Also, wo Papers un Böker spesifisch? De Antwoort is simpel: <strong>Informationsdichte</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Pro Megabyte an Speichern, schrieven Text speichert de meisten Informatschoon vun all Medien. Wi kümmer uns üm beiden Wissen un Kultur, aver wi kümmer uns mehr üm dat eerstere. Överall finnen wi en Hierarchie vun Informatschoondichtheit un Wichtichkeit vun Bewahren, de ungefäär so utseht:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademische Papers, Journals, Berichte"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organische Daten as DNA-Sequenzen, Planten-Sieden oder mikrobielle Proben"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Sachbücher"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Wissenschafts- un Ingenieurs-Softwarecode"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Messdaten as wissenschaftliche Messungen, Wirtschaftsdaten, Unternehmensberichte"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Wissenschafts- un Ingenieurs-Websites, Online-Diskussionen"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Sachzeitschriften, Zeitungen, Handbücher"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Sachtranskriptionen vun Reden, Dokumentationen, Podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Interne Daten vun Unternehmen oder Regierungen (Lecks)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata-Records generell (vun Sach- un Fiktschoon; vun annere Medien, Kunst, Lüüd, etc.; inklusiv Rezensionen)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografische Daten (z.B. Karten, geologische Untersuchungen)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkriptionen vun rechtlichen oder gerichtlichen Verfahren"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiktschoonale oder Unterhaltungs-Versionen vun all dat Vörherige"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "De Rangfolegen in dissen List is wat willkürlich — eenige Punkte sünd glieks oder hebbt Unstimmigkeiten binnen uns Team — un wi vergaaten wahrscheinlech eenige wichtige Kategorien. Aver dat is ungefäär, wo wi priorisieren."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Eenige vun dissen Punkten sünd to anners as de annern, dat wi uns dorüm kümmer mööt (oder sünd al vun annere Institutionen versorgt), as organische Daten oder geografische Daten. Aver de meisten Punkten in dissen List sünd för uns tatsächlich wichtig."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Een annern grooten Faktor in uns Priorisierung is, wieveel Risiko een bestimmtes Werk hett. Wi fögen uns lieker op Werke, de:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Seldent"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Einzigartig unnerfokussiert"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniek in't Risiko vun Vernichtung (z.B. dörch Krieg, Geldkürzungen, Klagen oder politische Verfolgung)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Endlich kümmer wi uns üm de Grootheit. Wi hebbt begrenzt Tied un Geld, also würd wi leever en Maand dorup verwenden, 10.000 Böker to retten as 1.000 Böker — wenn se ungefäähr gliek wertvoll un in't Risiko sünd."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Schattbibliotheken"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Dat gifft vele Organisationen, de ähnliche Missonen un ähnliche Prioritäten hebbt. In't Düütsche gifft dat Bibliotheken, Archive, Labore, Museen un annere Institutionen, de mit so'n Erhalt beauftragt sünd. Veel vun disse sünd good finanziert, vun Regierungen, Individuen oder Firmen. Awer se hebbt een groten blinden Fleck: dat Rechtssystem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hier liggt de unieke Rolle vun Schattbibliotheken, un de Grund, warrüm Anna sien Archiv existiert. Wi köönt Dingen doon, de annere Institutionen nich doorn dröfft. Nu, dat is nich (oft) so, dat wi Materialien archiveren köönt, de annerswo illegal to bewaren sünd. Nee, dat is in veel Steeden legal, en Archiv mit all Böker, Papiere, Tiedschriften, un so wieter to bouwen."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Awer wat legale Archive oft fehlt, is <strong>Redundanz un Langlebigkeit</strong>. Dat gifft Böker, vun de nur een Kopie in een physische Bibliothek irgendo existiert. Dat gifft Metadata-Records, de vun eenzig en Firma bewahrt warrt. Dat gifft Zeitungen, de nur op Mikrofilm in eenzig en Archiv bewahrt warrt. Bibliotheken köönt Geldkürzungen kriegen, Firmen köönt pleite gohn, Archive köönt bombt un to Grund brannt warrn. Dat is nich hypothetisch — dat passiert all de Tied."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Dat, wat wi uniek bi Anna sien Archiv doon köönt, is vele Kopien vun Warken to speicher, in grootem Umfang. Wi köönt Papiere, Böker, Tiedschriften un mehr sammeln un in Masse verteilen. Wi doon dat aktuell dörch Torrents, awer de exakte Technologien maakt keen Unterschied un warrt sik mit de Tied ännern. Dat wichtichste is, vele Kopien över de Welt to verteilen. Dit Zitat vun över 200 Johren her is noch immer wahr:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Dat Verlorene kann nich wedderfunnen warrn; awer laat uns bewaren, wat överbleven is: nich dörch Tresore un Schlößer, de se vun de Publick-Augen un -Bruk fernhollen, in't Vergeuden vun Tied, awer dörch so'n Vervielfältigung vun Kopien, as se över de Reek vun Zufall sett.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "En schnacken Notiz över dat Publick-Domain. Da Anna sien Archiv uniek op Aktivitäten fokussiert, de in veel Steeden op de Welt illegal sünd, kümmer wi uns nich üm weit verbreitete Sammlungen, as Publick-Domain-Böker. Legale Entitäten kümmer sik oft al good dorüm. Awer dat gifft Überlegungen, de uns manchmol an öffentlich verfügbare Sammlungen werken laat:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata-Records köönt op de Worldcat-Website frie ankeken warrn, awer nich in Masse rutladen (bis wi se <a %(worldcat_scrape)s>scraped</a> hebbt)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Code kann op Github open source warrn, awer Github as Ganzes kann nich einfach spiegelt un so bewahrt warrn (obwohl in dissen speziellen Fall gifft dat genöögend verteilte Kopien vun de meisten Code-Repositories)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit is frie to bruken, awer hett kürzlich strenge Anti-Scraping-Maßnahmen upstellt, in de Folge vun datadurstige LLM-Training (mehr doröver later)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "En Vervielfältigung vun Kopien"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tüch to unsen ursprünlichen Froog: warrüm köönt wi behaupen, uns Sammlungen för immer to bewaren? Dat Hauptproblem hier is, dat uns Sammlung <a %(torrents_stats)s>schnell wüxt</a>, dörch Scraping un Open-Sourcing vun en masse Sammlungen (op de fantastischen Arbeit, de annere Open-Data-Schattbibliotheken as Sci-Hub un Library Genesis al doon hebbt)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Disse Wüxt vun Daten maakt dat swieriger för de Sammlungen, överall op de Welt spiegelt to warrn. Datenspeicher is teuer! Awer wi sünd optimistisch, besünners wenn wi de folgende drei Trends beobachten."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Wi hebbt dat \"low-hanging fruit\" plückt"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Disse een folgt direkt vun unsen obendiskutierten Prioritäten. Wi föredragen, eerst op de Befreiung vun grote Sammlungen to werken. Nu, dat wi eenige vun de größten Sammlungen in de Welt gesichert hebbt, verwacht wi, dat uns Wüxt veel langsamer warrt."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Dat gifft noch en langen Schwanz vun klienere Sammlungen, un ne'e Böker warrt jeden Dag scannt oder veröffentlicht, awer de Rate warrt wahrscheinlik veel langsamer. Wi köönt noch verdoppeln oder sogar verdreifachen in Grootheit, awer över en längere Tiedraum."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Speicherkosten fallet exponentiell"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "As vun de Tied vun’t Schrieven, sünd <a %(diskprices)s>Diskpriesen</a> per TB üm $12 för nee Disken, $8 för bruukte Disken, un $4 för Tape. Wenn wi konservativ sünd un blots na nee Disken kieket, bedüüt dat, dat en Petabyte to spegeln üm $12,000 kost. Wenn wi annehmen, dat uns Bibliothek vun 900TB op 2.7PB verdreifacht, bedüüt dat $32,400 för uns ganze Bibliothek to spegeln. Mit Elektrizität, Kosten för annere Hardware, un so wieter, runden wi dat op $40,000 op. Oder mit Tape ehr üm $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Op de een Siet <strong>$15,000–$40,000 för de Summe vun all menschklich Wissen is en Schnapper</strong>. Op de annere Siet is dat en beten steil, tonnenwies volle Kopien to verwachten, besünners wenn wi ok wüllt, dat de Lüüd ehr Torrents för de Vorteil vun annere seedt."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dat is hüüt. Aver de Fortschritt geiht vöran:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Harddiskpriesen per TB sünd üm een Drüttel in de letzten 10 Johren rutslagen worrn, un wüllt wahrschienlich op en ähnlichen Tempo fahlen. Tape schient op en ähnlichen Kurs to wesen. SSD-Priesen fahlen noch schneller, un köönt HDD-Priesen bi’t Enn vun’t Jahrzehnt övernehmen."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-Priestrends vun verscheden Quellen (klick för Studie to ankieken)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Wenn dit so blifft, köönt wi in 10 Johren blots üm $5,000–$13,000 för uns ganze Kollektion to spegeln ankieken (1/3), oder noch minder, wenn wi in Grött nich so veel wussen. Ok wenn dat noch veel Geld is, wüllt dat för veel Lüüd togänglich wesen. Un dat köönt noch beter warden wegen den nächsten Punkt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Verbeterungen in Informationsdicht"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Wi speichert Böker in de rohen Formaten, as se uns geven worrn sünd. Sicher, se sünd komprimiert, aver oft sünd se noch grote Scans oder Foto’s vun Sieden."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Bislang wöör de eenzig Optioon, de totale Grött vun uns Kollektion to verkleinen, dörch mehr aggressiv Kompression oder Deduplication. Aver för significant Sparen sünd beid to verlustig för uns Geschmack. Swore Kompression vun Foto’s kann Text kaum lesbar maken. Un Deduplication vereist hohe Vertrauheit, dat Böker genau de sülvst sünd, wat oft to ungenau is, besünners wenn de Inholt de sülvst sünd, aver de Scans op verscheden Gelegenheiten maakt worrn sünd."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Dor weer allens en drüdd Optioon, aver ehr Qualität weer so schrecklich, dat wi se nie in Betracht treckt hebbt: <strong>OCR, oder Optische Zeichenerkennung</strong>. Dit is de Prozess, Foto’s in plain Text to konvertieren, dörch AI to bruuken, de de Zeichen in de Foto’s erkennt. Tools för dit hebbt lang existiert, un wöörn recht anständig, aver „recht anständig“ is nich genug för Erhaltungszwecken."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Aver, neulich multi-modale Deep-Learning-Modelle hebbt extrem schnellen Fortschritt maakt, wenn ok noch op hohe Kosten. Wi verwacht, dat beid Genauigkeit un Kosten dramatisch in de kommenden Johren verbetert wüllt, to’n Punkt, wo dat realistich wüllt warden, dat op uns ganze Bibliothek to anwenden."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR-Verbeterungen."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Wenn dat passiert, wüllt wi wahrschienlich noch de orschinalen Dateien bewaren, aver dorbi köönt wi en veel kleinere Version vun uns Bibliothek hebben, de de meisten Lüüd wüllt spegeln wüllt. De Clou is, dat roher Text sülvst noch beter komprimiert, un veel leichter to deduplizieren is, wat uns noch mehr Sparen geeft."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Överall is dat nich ünrealistisch to verwachten, dat de totale Dateigrött üm minnstens 5-10 mol minnt, vleicht noch mehr. Sogar mit en konservativen 5 mol Reduktion, würd wi op <strong>$1,000–$3,000 in 10 Johren kiken, sülvst wenn uns Bibliothek drüffach so grot wurrd</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritische Tied"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Wenn disse Vörhersagen stimmen, <strong>müssen wi bloß en paar Johren wachten</strong>, bevor uns ganze Sammling wiet verbreitet spiegelt wurrd. So, in de Wöör von Thomas Jefferson, „beyond de Reek vun Unfall“."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Leider hett de Opkamen vun LLMs, un ehr datehungrige Training, vele Urheberrechtsinhaber in de Defensive drängt. Noch mehr as se al wurrn. Veel Websieden maakt dat sworer to scrapen un archiveren, Klagen fleegen ümher, un all de Tied wurrd fysiske Bibliotheken un Archive vernachlässigt."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Wi köönt bloß verwachten, dat disse Trenden sik noch verschlechtern, un vele Warken verloren gahn, lang bevor se in de Public Domain kummen."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Wi sünd op de Schwelle vun en Revolution in de Bewahring, aver <q>dat Verlorene kann nich wedderfunn wurrn.</q></strong> Wi hebbt en kritische Tied vun ümtrent 5-10 Johren, in de dat noch recht teuer is, en Schattbibliothek to bedrieven un vele Spiegel üm de Welt to schapen, un in de de Toegang noch nich ganz dichtmaakt wurrn is."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Wenn wi disse Tied överbrücken köönt, denn hebbt wi in de Taat de Wissen un Kultur vun de Minschheit för immer bewahrt. Wi schüllt disse Tied nich verluren laten. Wi schüllt nich tolaten, dat disse kritische Tied för uns sluten deit."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Laten wi gohn."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Exklusiven Toegang för LLM-Firmen to de grotste chinesische Sachbuchsammling vun de Welt"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Chinesische Version 中文版</a>, <a %(news_ycombinator)s>Diskutieren op Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archiv hett en unieke Sammling vun 7,5 Millionen / 350TB chinesische Sachböker övernommen — grötter as Library Genesis. Wi sünd bereit, en LLM-Firma exklusiven Toegang to geven, in Utwesselung för hochkvalitativ OCR un Text-Extraktion.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dit is en kort Blogpost. Wi söcht en Firma oder Institution, de uns mit OCR un Text-Extraktion för en massive Sammling, de wi övernommen hebbt, helpen kann, in Utwesselung för exklusiven fröhen Toegang. Na de Embargo-Period, wurrd wi natüürlich de ganze Sammling freigeben."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Hochkvalitativ akademischen Text is extrem nützlich för de Training vun LLMs. Ok wenn uns Sammling chinesisch is, schüllt dit ok för de Training vun engelsche LLMs nützlich wesen: Modellen scheinen Konzepte un Wissen to enkodieren, egal vun welke Ursprungsprache."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Daför mutt Text ut de Scans extrahiert wurrn. Wat kriegt Anna’s Archiv dorvun? Volltextsuche vun de Böker för ehr Brukers."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Weil uns Ziele mit de vun LLM-Entwicklers übereinstimmen, söcht wi en Kollaborateur. Wi sünd bereit, <strong>exklusiven fröhen Toegang to disse Sammling in Masse för 1 Johr to geven</strong>, wenn ji proper OCR un Text-Extraktion maken köönt. Wenn ji bereit sünd, de ganze Code vun jüm Pipeline mit uns to delen, würd wi bereit wesen, de Sammling länger to embargieren."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Beispielseiten"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "To beweisen, dat ji en gute Pipeline hebbt, hier sünd en paar Beispielseiten, mit de ji starten köönt, vun en Book över Supraleiter. Jüm Pipeline schüllt korrekt mit Mathematik, Tabellen, Diagrammen, Fußnoten, un so weiter umgehen."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Senden ji jüm verarbeiteten Sieden to uns E-Mail. Wenn se good utseht, senden wi ji mehr in Privat, un wi verwachten, dat ji jüm Pipeline dor ok schnell op lopen laten köönt. Wenn wi zufrieden sünd, köönt wi en Deal maken."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Sammling"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Einige weitere Informationen über die Sammlung. <a %(duxiu)s>Duxiu</a> ist eine riesige Datenbank gescannter Bücher, erstellt von der <a %(chaoxing)s>SuperStar Digital Library Group</a>. Die meisten sind akademische Bücher, die gescannt wurden, um sie Universitäten und Bibliotheken digital zugänglich zu machen. Für unser englischsprachiges Publikum haben <a %(library_princeton)s>Princeton</a> und die <a %(guides_lib_uw)s>University of Washington</a> gute Übersichten. Es gibt auch einen ausgezeichneten Artikel, der mehr Hintergrundinformationen bietet: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine“</a> (suchen Sie danach in Annas Archiv)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Die Bücher von Duxiu wurden lange Zeit im chinesischen Internet piratiert. Normalerweise werden sie von Wiederverkäufern für weniger als einen Dollar verkauft. Sie werden typischerweise mit dem chinesischen Äquivalent von Google Drive verteilt, das oft gehackt wurde, um mehr Speicherplatz zu ermöglichen. Einige technische Details finden Sie <a %(github_duty_machine)s>hier</a> und <a %(github_821_github_io)s>hier</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Obwohl die Bücher halböffentlich verteilt wurden, ist es ziemlich schwierig, sie in großen Mengen zu erhalten. Wir hatten dies hoch auf unserer TODO-Liste und haben mehrere Monate Vollzeitarbeit dafür eingeplant. Kürzlich hat sich jedoch ein unglaublicher, erstaunlicher und talentierter Freiwilliger an uns gewandt und uns mitgeteilt, dass er all diese Arbeit bereits erledigt hat — mit großem Aufwand. Sie haben die vollständige Sammlung mit uns geteilt, ohne etwas im Gegenzug zu erwarten, außer der Garantie für eine langfristige Erhaltung. Wirklich bemerkenswert. Sie stimmten zu, auf diese Weise um Hilfe zu bitten, um die Sammlung OCR'en zu lassen."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Die Sammlung umfasst 7.543.702 Dateien. Das ist mehr als Library Genesis Sachbücher (etwa 5,3 Millionen). Die Gesamtdateigröße beträgt etwa 359TB (326TiB) in ihrer aktuellen Form."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Wir sind offen für andere Vorschläge und Ideen. Kontaktieren Sie uns einfach. Schauen Sie sich Annas Archiv an, um mehr über unsere Sammlungen, Erhaltungsbemühungen und wie Sie helfen können, zu erfahren. Danke!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Warnung: dissen Blogpost is veraltet. Wi hebbt besluten, dat IPFS noch nich bereit för de Primetime is. Wi wüllt noch Links to Dateien op IPFS vun Anna’s Archiv maken, wenn mööglich, aver wi wüllt dat nich mehr sülvst hosten, noch empfahlen wi annere, dat mit IPFS to spiegeln. Kiek op uns Torrents-Sied, wenn du uns Sammling helpen wullt to bewaren."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Helfen Sie, Z-Library auf IPFS zu seeden"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Wie man eine Schattenbibliothek betreibt: Betrieb bei Annas Archiv"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Es gibt kein <q>AWS für Schattenwohltätigkeiten,</q> also wie betreiben wir Annas Archiv?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ik dröff <a %(wikipedia_annas_archive)s>Annas Archiv</a>, de gröttste open-source non-profit Suchmaschien för <a %(wikipedia_shadow_library)s>Schattbibliotheken</a> op de Welt, so as Sci-Hub, Library Genesis un Z-Library. Uns Tiel is, Wissen un Kultur för jedermann togänglich to maken un letztlich en Gemeenschop op to bouwen vun Lüüd, de tosamen all de Böker op de Welt archiveren un bewaren."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In dit Artikel wies ik, wo wi disse Websteed dröff, un de besünnere Ütforderrungen, de mit dat Bedrief vun en Websteed mit fragwürdigen legalen Status dorherkamen, denn dat gifft keen „AWS för Schattwohltätigkeiten“."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Kiek ok mol in dat Süsternartikel <a %(blog_how_to_become_a_pirate_archivist)s>Wie man en Piratenarchivar warrt</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovatschoonstokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Laten wi mit uns Technologiestack anfungen. Dat is mit Absicht langweilig. Wi bruken Flask, MariaDB un ElasticSearch. Dat is wortwörtlich allens. Suchen is größtendeels en löst Problem, un wi hebt keen Intress daran, dat nieg to erfinnen. Doröver mööt wi uns <a %(mcfunley)s>Innovatschoonstokens</a> för wat anners utgeven: nich vun de Autoritäten ut de Verkehr trecken laten."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Also, wo legal oder illegal is Annas Archiv genau? Dat hängt größtendeels vun de legalen Jurisdiktschoon af. De meisten Länner glöven an en Form vun Urheberrecht, wat bedüden de Lüüd oder Firmen en exklusiven Monopol för bestimmte Warken för en bestimmte Tiet toewiesen warrt. As en Nebensache, bi Annas Archiv glöven wi, dat, obwool dat en paar Vorteele gifft, dat Urheberrecht insgesamt en negativen Nettowert för de Gemeenschop hett — aver dat is en Geschicht för en anner Tiet."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dit exklusiven Monopol op bestimmte Warken bedüden, dat dat illegal is för jedermann buten dissen Monopol, disse Warken direkt to verteren — inklusiv uns. Aver Annas Archiv is en Suchmaschien, de disse Warken nich direkt vertert (minstens nich op uns clearnet Websteed), also schullt dat ok sien, nich? Nich ganz. In vele Jurisdiktschoonen is dat nich alleen illegal, urheberrechtlich schützt Warken to verteren, aver ok, to verlinken to Steden, de dat doon. En klassisch Beispiel dorför is de DMCA-Gesetz vun de USA."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dat is de strengste Enn vun de Spektrum. Op de anner Enn vun de Spektrum köönt theoretisch Länner wesen, de keen Urheberrechtsgesetze hebben, aver disse gifft dat praktisch nich. Fast elk Land hett en Form vun Urheberrechtsgesetz op de Böker. De Durchsetten is en anner Geschicht. Dor gifft vele Länner mit Regeringen, de keen Intress doran hebben, dat Urheberrecht to dorchsetten. Dor gifft ok Länner twischen de beiden Extremen, de dat Verteren vun urheberrechtlich schützt Warken verbieten, aver nich dat Verlinken to solche Warken."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Een anner Überlegung is op de Firma-Niveau. Wenn een Firma in en Jurisdiktschoon operiert, de sik nich üm Urheberrecht schert, aver de Firma sülvst keen Risiko innehmen will, denn köönt se dien Websiet so gau as een Froge dorüm opkamen, dichtmaken."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Endlich is een grote Überlegung de Betahlungen. Da wi anonym blieven mööt, köönt wi keen traditionell Betahlmethoden bruken. Dit läwt uns mit Kryptowährungen, un blots een lütt Deel von Firmen ünnerstütt disse (dat gifft virtuell Debitkarten, de mit Krypto betahlt wörrt, aver de wörrt oft nich annehmen)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systemarchitektur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Laten wi seggen, dat du een paar Firmen funnen hest, de bereit sünd, dien Websiet to hosten, ohne di dicht to maken — laten wi disse „Freiheitsliebende Providers“ nennen 😄. Du wirst gau funnen, dat dat Hosten von allens mit disse recht teuer is, so dat du veellicht een paar „billige Providers“ funnen wullt un dat faktische Hosten dor doon, mit Proxying dörch de Freiheitsliebende Providers. Wenn du dat richtig doost, weet de billige Providers nie, wat du hostest, un kriegt nie Frogen."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Mit all disse Providers gifft dat een Risiko, dat se di doch dichtmaken, so dat du ok Redundanz bruken mööt. Wi bruken dit op all Niveauen von uns Stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Een lüttje freiheitsliebende Firma, de sik in een interessante Positschoon sett hett, is Cloudflare. Se hebbt <a %(blog_cloudflare)s>argumentiert</a>, dat se keen Hosting-Provider sünd, aver een Utility, as een ISP. Se sünd dorüm nich vun DMCA oder anneren Takedown-Froge betroffen, un leiten Froge an dien faktischen Hosting-Provider wieter. Se sünd so wiet gahn, dat se to Gericht gahn sünd, üm disse Struktur to schützen. Wi köönt se dorüm as een anner Layer von Caching un Schutz bruken."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare accepteert keen anonyme Betahlungen, also köönt wi alleen hun gratis Plan bruken. Dat bedüden, dat wi keen Lastenausgleich oder Failover-Funktschoonen bruken köönt. Wi hebbt dat dorüm <a %(annas_archive_l255)s>sülvst implementiert</a> op de Domain-Niveau. Bi de Siedenladen checkt de Browser, of de aktuelle Domain noch togänglich is, un wenn nich, schrievt dat all URLs op en anner Domain üm. Da Cloudflare vele Sieden cachet, bedüden dat, dat en Bruker op uns Hauptdomain landen kann, ok wenn de Proxy-Server nich togänglich is, un denn bi de nächsten Klick op en anner Domain överführt warrt."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Wi hebben ok noch normale betriebliche Sorgen, so as de Servergesundheit to överwachen, Backend- un Frontend-Fehler to protokolleren, un so wieter. Uns Failover-Architektur maakt dat ok op dissen Front robuster, för exempel dör dat Bedrief vun en ganz anner Set vun Servern op een vun de Domains. Wi köönt ok öllere Versionen vun de Code un Datasets op dissen separaten Domain lopen laten, för den Fall, dat en kritischen Fehler in de Hauptversion unbemerkt blifft."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Wi köönt ok Cloudflare entgegentreden, indem wi dat vun een vun de Domains wegnahmen, so as dissen separaten Domain. Verschiedene Permutatschoonen vun disse Ideen sünd mööglich."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Warktügen"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Laten wi mol kiek, wat för Warktügen wi bruken, üm all dit to erreichen. Dit is heel wat in Utwickeln, as wi op niege Probleme stoten un niege Lösen finnen."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Applikatschoonserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy-Server: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Servermanagement: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Utwickeln: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion-Statichosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Dor sünd en paar Besluten, de wi hin un her överlegt hebben. Een is de Kommunikatschoon twischen Servern: wi hebben fröher Wireguard dorför bruken, aver funnen, dat dat gelegentlich opheert, Daten to överdragen, oder alleen Daten in een Richtung överdragen deit. Dit is bi mehrere verschiedene Wireguard-Setups passiert, de wi utprobiert hebben, so as <a %(github_costela_wesher)s>wesher</a> un <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Wi hebben ok versöcht, Ports över SSH to tunneln, mit autossh un sshuttle, aver stoten op <a %(github_sshuttle)s>Probleme dor</a> (obwohl dat noch nich klar is för mi, ob autossh vun TCP-over-TCP-Problemen leidet oder nich — dat föhlt sik einfach as en wackelige Lösen an, aver vleicht is dat ok in Ordnung?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Stattdessen sünd wi torüch to direkten Verbindungen twischen Servern, indem wi verbergen, dat en Server op billige Providers löppt, mit IP-Filterung mit UFW. Dit hett den Nachdeel, dat Docker nich good mit UFW funktioneert, ünner de Vörussetzung, dat man <code>network_mode: \"host\"</code> bruken deit. All dit is en beten fehleranfällig, denn man sett sin Server mit en lütten Fehlkonfiguratschoon de Internet bloot. Vleicht schullt wi torüch to autossh gahn — Feedback weer hier heel welkamen."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Wi hebbt ok hin un her över Varnish un Nginx snackt. Wi mögen Varnish nu, man dat hett sien Macken un kantig Kanten. Dat gellt ok för Checkmk: wi sünd nich begeistert, man dat geiht för nu. Weblate is okay, man nich överwältigend — ik förchte manchmol, dat dat mien Daten verlöre, wenn ik versöke, dat mit unsen git repo to synchronisieren. Flask hett överall good funktioneert, man dat hett wat komische Macken, de veel Tied för dat Debuggen kosten hebbt, so as dat Konfigurieren vun egen Domenen oder Problemen mit sien SqlAlchemy-Integratschoon."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Bisher hebbt de annern Tools good funktioneert: wi hebbt keen gröttere Klagen över MariaDB, ElasticSearch, Gitlab, Zulip, Docker un Tor. All de hebbt wat Problemen hatt, man nix to ernsthaft oder Tied opwändig."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Slussfolgerung"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Dat weer en interessante Erfahrung, to leeren, wo een robuste un widerstandsfähige Schadow-Bibliothek-Sökmotor op to setten. Dat gifft noch veel mehr Details to delen in lateren Posts, also laat mi weten, wat ji mehr döröver weten wüllt!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "As alltohoop, söken wi Spenden, üm disse Arbeit to ünnerstütten, also kiek mol op de Spendenseite vun Annas Archiv. Wi söken ok anner Arten vun Ünnerstüttung, so as Stipendien, langfriste Sponsoren, Högrisk-Betahlproviders, vleicht ok (geschmackvolle!) Reklame. Un wenn ji Tied un Fäigkeiten bi dragen wüllt, wi söken alltohoop Utwickler, Översetters, un so wieter. Dank för jow Interesse un Ünnerstüttung."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna un dat Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Moin, ik bün Anna. Ik hebb <a %(wikipedia_annas_archive)s>Annas Archiv</a> schapen, de gröttste Schadow-Bibliothek vun de Welt. Dit is mien persöönlichen Blog, in den ik un mien Teamkameraden över Piraterie, digitale Bewahrung un mehr schrieven."

#, fuzzy
msgid "blog.index.text2"
msgstr "Verbinnen ji sik mit mi op <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Bedenkt, dat disse Websteed blots en Blog is. Wi hosten hier blots uns egen Wöör. Keen Torrents oder annere urheberrechtlich schützt Dateien sünd hier hostet oder verlinkt."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogposts"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5.998.794 Böker op IPFS setten"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Warnung: dissen Blogpost is veraltet. Wi hebbt besluten, dat IPFS noch nich bereit för de Primetime is. Wi wüllt noch Links to Dateien op IPFS vun Anna’s Archiv maken, wenn mööglich, aver wi wüllt dat nich mehr sülvst hosten, noch empfahlen wi annere, dat mit IPFS to spiegeln. Kiek op uns Torrents-Sied, wenn du uns Sammling helpen wullt to bewaren."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archiv hett heel WorldCat (de gröttste Bibliothek-Metadata-Sammlung vun de Welt) utkratz, üm een TODO-List vun Böker to maken, de bewaren wörrt mööt.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Een Johr her hebbt wi <a %(blog)s>begunnen</a>, disse Froge to beantworen: <strong>Watt för een Prozentsatz vun Böker is permanent vun Schadowbibliotheken bewaren wörrt?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Wenn een Book in een Open-Data Schadowbibliothek as <a %(wikipedia_library_genesis)s>Library Genesis</a> kummt, un nu <a %(wikipedia_annas_archive)s>Anna’s Archiv</a>, denn wörrt dat överall op de Welt (dörch Torrents) gespiegelt, un so praktisch för immer bewaren."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "To de Froge, welk Prozentsatz vun Böker bewohrt worrn is, mööt wi den Nenner weten: Wieviel Böker gifft dat in’t Ganse? Un idealerwies hebbt wi nich blot en Tahl, man ok faktische Metadata. Denn köönt wi se nich blot mit Schattbibliotheken verglieken, man ok <strong>en TODO-List vun de restlichen Böker maken, de bewohrt warrn mööt!</strong> Wi köönt ok dreegen, vun en crowdsourcede Insats to dreegen, üm disse TODO-List af to arbeiden."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Wi hebbt <a %(wikipedia_isbndb_com)s>ISBNdb</a> scrapt un den <a %(openlibrary)s>Open Library dataset</a> daunloadt, man de Resultaten wöörn nich tofrädenstellend. Dat gröttste Problem weer, dat dor nich veel Överlap vun ISBNs weer. Seht disse Venn-Diagramm ut <a %(blog)s>uns Blogpost</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Wi wöörn sehr överrascht, wie wenig Överlap dor twischen ISBNdb un Open Library weer, beide vun de liberale Daten ut verschedenen Quellen, as Webscrapes un Bibliotheksrecords, inbegriepen. Wenn se beide en gooden Job maken, de meisten ISBNs to finnen, schullt ehr Kring sicher en substantialen Överlap hebben, oder een weer en Deel vun den annern. Dat hett uns dreegen laten, wieveel Böker <em>ganz buten disse Kringen fallt</em>? Wi bruken en gröttere Datenbank."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Dat weer, as wi uns op de gröttste Bökerdatenbank vun de Welt fokussiert hebbt: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is en proprietäre Datenbank vun de Non-Profit <a %(wikipedia_oclc)s>OCLC</a>, de Metadata-Records vun Bibliotheken ut de ganze Welt aggregiert, in Utwesselung för de Bibliotheken den vollen Dataset to geven un se in de Endnutzer-Söökresultaten to laten wiesen."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Obwohl OCLC en Non-Profit is, vereist ehr Geschäftsmodell den Schutz vun ehr Datenbank. Na, wi sünd leid, Frünnen bi OCLC, wi geven dat all weg. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Över dat verganne Johr hebbt wi all WorldCat-Records metikulös scrapt. Eerst hebbt wi en glücklichen Treffer. WorldCat weer grad ehr komplette Websiteredesign (in Aug 2022) utrollen. Dit inbegriep en substantialen Overhaul vun ehr Backend-Systemen, de vele Sicherheitslücken inbröcht. Wi hebbt de Gelegenheid sofort ergrepen un wöörn in Stann, Hunnerten vun Millionen (!) Records in bloß een paar Daag to scrapen."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat Redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dorno wöörn de Sicherheitslücken een na den annern langsam fixiert, bit de letzte, de wi funnen hebbt, vör etwa een Maand patcht weer. Bi dat Tiedpunkt hebbt wi pretty much all Records, un wöörn bloß noch op en lüttje höger Qualität vun Records ut. So hebbt wi dat Gefühl, dat dat Tied is, to veröffentlichen!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Laten wi en beten Basisinformationen över de Daten ankieken:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Anna’s Archiv-Behälters (AAC)</a>, wat im Wesentlichen <a %(jsonlines)s>JSON Lines</a> is, komprimiert mit <a %(zstd)s>Zstandard</a>, plus en paar standardisierte Semantiken. Disse Behälters umsluten verschedene Typen vun Records, basierend op de verschedene Scrapes, de wi utführt hebbt."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Daten"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "En onbekannten Fehler is optraden. Kontaktiert uns bitte ünner %(email)s mit en Screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Disse Münz hett en höger Minimum as normaal. Wähl en anner Tiedduur oder en anner Münz."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "De Anfroog kunn nich afslooten warrn. Versöök dat bitte nochmaal in en poor Minuten, un wenn dat wedder passeert, kontaktiert uns bitte ünner %(email)s mit en Screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Fehler bi de Betahlverwerking. Wart een Moment un versöök dat nochmaal. Wenn dat Problem länger as 24 Stünnen anhölt, kontaktiert uns bitte ünner %(email)s mit en Screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "verborger Kommentaar"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Dateiproblem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Bessere Version"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Wullt ji disse Bruker för missbrükkliches oder unpassendes Verhollen melden?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Missbrükk melden"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Missbrükk gemeldet:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Ji hebt disse Bruker för Missbrükk gemeldet."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Antwurd"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Bitte <a %(a_login)s>anmelden</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Du hest en Kommentaar achterlaten. Dat kann en Minuut dauen, bit dat he wiest warrt."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Wat is schiefgahn. Ladd de Sied nee un versöök dat nochmaal."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s betroffene Sieden"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nich sichtbor in Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nich sichtbor in Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nich sichtbor in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "As defekt markeert in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Fehlt in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Als „Spam“ in Z-Library markiert"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Als „schlechte Datei“ in Z-Library markiert"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nich all Sieden köönt to PDF konverteert warrn"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Exiftool is bi disse Datei fehlslaan"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Book (unbekannt)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Book (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Book (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Journalartikel"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Normendokument"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Tiedschrift"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Tegnbok"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Musikpartitur"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Hööbook"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Annert"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Partner-Server-Download"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Externen Download"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Externen Leih"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Externen Leih (druckbehindert)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Metadaten dörchsöken"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "In Torrents innehollen"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinesisch"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads to AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tschechsche Metadaten"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russische Staatsbibliothek"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titel"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Schriever"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Verlag"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Utgaav"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Jahr utgeven"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Original Dateinaam"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Beschrieven un Metadaten Kommentaren"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partner-Server-Downloads för disse Datei sünd temporär nich verfügbar."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Schnell Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(empfohlen)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(keen Browser-Verifikation oder Wartelisten)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Langsam Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(bi't lüttje schneller, aver mit Warteliste)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(keen Warteliste, aver kann sehr langsam ween)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(ok ook op „GET“ boven klicken)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klicken op „GET“ boven)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "deren Reklame sünd bekannt, Schadsoftware to hebben, also bruken Se en Adblocker oder klicken Se nich op Reklame"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC Dateien köönt unbetruenbar ween för’t Herunterladen)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Bibliothek op Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(bruukt den Tor-Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Leihen von de Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(blot för print-disabled Patrons)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(assozierten DOI is mööglicherweise nich in Sci-Hub verfügbar)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "Sammlung"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "Torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Bulk-Torrent-Downloads"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(blot för Experten)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Söken Anna’s Archive för ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Söken in verschieden anner Datenbanken för ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Original-Record in ISBNdb finnen"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Söök Anna’s Archive na Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Finde den originalen Eintrag in Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Söök Anna’s Archive na OCLC (WorldCat) Nummer"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Finde den originalen Eintrag in WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Söök Anna’s Archive na DuXiu SSID Nummer"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Manuell op DuXiu söken"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Söök Anna’s Archive na CADAL SSNO Nummer"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Finde den originalen Eintrag in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Söök Anna’s Archive na DuXiu DXID Nummer"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(keen Browser-Verifikation nödig)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tschechsche Metadaten %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadaten"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "Beschrieven"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternativen Dateinaam"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternativen Titel"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternativen Schriever"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternativen Verlag"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternativen Utgaav"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternativ Extenschoon"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "Metadaten Kommentaren"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternativen Beschrieven"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "Datum open sourced"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub Datei „%(id)s“"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending Datei „%(id)s“"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dit is en Eintrag vun en Datei ut den Internet Archive, keen direkt to laden Datei. Du kannst versöken dat Book to leihen (Link ünnen), oder bruken dissen URL wenn du <a %(a_request)s>en Datei anforderst</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Wenn du dissen Datei hest un he is noch nich in Anna’s Archive, överleg <a %(a_request)s>ihn to laden</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s Metadaten-Eintrag"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s Metadaten-Eintrag"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) Nummer %(id)s Metadaten-Eintrag"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s Metadaten-Eintrag"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s Metadaten-Eintrag"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s Metadaten-Record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s Metadaten-Record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dit is en Metadaten-Record, keen Datei för't Dounloaden. Du kannst disse URL bruken, wenn du <a %(a_request)s>en Datei anforderst</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadaten vun den verlinkten Datensatz"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Metadaten op'n Open Library verbetern"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Wohrschau: meerdere verlinkte Datensätze:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Metadaten verbeedern"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Dateikwaliteit melden"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Herunterlaadtiet"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Websteed:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Anna’s Archive na “%(name)s” dörsöken"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Codes Explorer:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "In Codes Explorer ankieken “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Mehr lesen…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Leihen (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Metadaten dörsöken (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Kommentaren (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listens (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistiken (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Technische Details"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Disse Datei könnt Problem hebben un is ut en Ursprungsbibliothek versteken worrn.</span> Manchmal is dat op Anfroge vun en Urheberrechtsinhaber, manchmal is dat, weil en betere Alternativ vörhannen is, aver manchmal is dat, weil en Problem mit de Datei sülvst is. Dat könnt noch ok sien, dat du dat dounloaden kannst, aver wi raten an, eerst na en Alternativdatei to söken. Mehr Details:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "En betere Version vun disse Datei könnt bi %(link)s vörhannen sien"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Wenn du disse Datei noch dounloaden wullt, brük seker un aktualisiert Software, üm dat to openen."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Snelle Downloads"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Snelle Downloads</strong> Worr <a %(a_membership)s>Mitglied</a>, üm de langfristeg Bewahrung vun Böker, Papers un mehr to ünnerstütten. As Dank för dien Ünnerstüttung kriegst du snelle Downloads. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Wenn du dissen Maand spendst, kriegst du <strong>doppelt so veel</strong> schnelle Downloads."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Snelle Downloads</strong> Du hest hüüt noch %(remaining)s över. Dank för dat du Mitglied büst! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Snelle Downloads</strong> Du hest för hüt keen snelle Downloads mehr."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Snelle Downloads</strong> Du hest dit Dokument kortleden daunloodt. Links blifft för en Tied gültig."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Optschon #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(keen Redirect)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(in'n Viewer opmaken)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Vertell en Fründ, un beid du un dien Fründ kriegt %(percentage)s%% Bonus-Snelle-Downloads!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Mehr weten…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Langsame Downloads"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Von vertrauenswörden Partnern."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mehr Informatschonen in de <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kann <a %(a_browser)s>Browser-Verifikatschon</a> nödig hebben — unbegrenzte Downloads!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Na't Herunterladen:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "In unsen Viewer opmaken"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "externen Downloads wiesen"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Externe Downloads"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Keen Downloads funnen."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "All Download-Optschonen hebbt de sülvige Datei un sünd sicher to bruken. Dat seggt, wees allens vörsichtig, wenn du Dateien vun’t Internet daunloodst, besünners vun Sieden, de nich to Anna’s Archiv höört. För Bispill, hest dien Geräte updatet to holden."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "För grote Dateien empfahlen wi, en Download-Manager to bruken, üm Ünnerbrekungen to vörhindern."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Empfohlene Download-Manager: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Ji bruken en eBook- oder PDF-Reader, üm de Datei to openen, dat hangt vun dat Dateiformat af."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Empfohlene eBook-Reader: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Anna sien Archiv online Viewer"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Bruk online Warktügen, üm twüschen Formaten to konvertieren."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Empfohlene Konvertierungswarktügen: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Ji köönt baade PDF- un EPUB-Dateien an jüm Kindle oder Kobo eReader senden."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Empfohlene Warktügen: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon sien „Send to Kindle“"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz sien „Send to Kobo/Kindle“"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Ünnerstütt Autoren un Bibliotheken"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Wenn ji dit mögt un dat sik leisten köönt, överlegt dat Original to köpen oder de Autoren direkt to ünnerstütten."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Wenn dat bi ju lokale Bibliothek to kriegen is, överweeg dat dor för umsonst to leen."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Dateikwaliteit"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Hülp de Gemeenschop, indem du de Kwaliteit vun disse Datei meldst! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Dateiproblem melden (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Grote Dateikwaliteit (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Kommentar tofögen (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Wat is mit disse Datei verkehrt?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Bitte bruuk dat <a %(a_copyright)s>DMCA / Urheberrechtsformular</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Beschriev dat Problem (verplicht)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Problem-Beschrieven"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 vun en betere Version vun disse Datei (wenn tohanden)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Füll dit ut, wenn du en annere Datei hewwst, de disse Datei fast gleicht (sülve Edition, sülve Dateiendung, wenn du een finnen kannst), de Lüd bruken schullen statt disse Datei. Wenn du en betere Version vun disse Datei buten Anna’s Archive kennst, dann bitte <a %(a_upload)s>lad se hooch</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Du kannst den md5 ut de URL kriegen, z.B."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Meldung afgeven"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lärn, woans du de <a %(a_metadata)s>Metadaten för dit File sülvst verbeedern</a> kannst."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Dank för dien Bericht. He warrt op disse Sied wiest un manuell vun Anna överkeken (bis wi en richtig Moderationssystem hebben)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Wat is schiefgahn. Ladd de Sied nee un versöök dat nochmaal."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Wenn dit File gute Qualität hett, kannst du hier allens doröver besnacken! Wenn nich, brük de Knopp „File-Problem melden“."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Ik heff dit Book leevt!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Kommentaar achterlaten"

#, fuzzy
msgid "common.english_only"
msgstr "Text dorunner geiht op Englisch wieter."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totaal Downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "En „File MD5“ is en Hash, de ut de File-Inholt berekent warrt un is basierend op den Inholt recht uniek. All Schattbibliotheken, de wi hier indexeert hebben, bruken vör allem MD5s, üm Files to identifizeren."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "En File kann in meerdere Schattbibliotheken vörkamen. För Informatschoonen över de verschedenen Datasets, de wi tosammentragen hebben, kiek op de <a %(a_datasets)s>Datasets-Sied</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dit is en File, de vun de <a %(a_ia)s>IA’s Controlled Digital Lending</a> Bibliothek verwaltet warrt un vun Anna’s Archive för de Sook indexeert is. För Informatschoonen över de verschedenen Datasets, de wi tosammentragen hebben, kiek op de <a %(a_datasets)s>Datasets-Sied</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "För Informatschoonen över dit besünners File, kiek op sien <a %(a_href)s>JSON-File</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problem bi’t Laden vun disse Sied"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Versöök dat nochmaal. <a %(a_contact)s>Kontakteer uns</a>, wenn dat Problem för mehrstünnig anholt."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Nich funnen"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” weer nich in uns Datenbank funnen."

#, fuzzy
msgid "page.login.title"
msgstr "Anmellen / Registeren"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Browser-Verifikation"

#, fuzzy
msgid "page.login.text1"
msgstr "To verhinnern, dat Spam-Bots vele Konten maken, mööt wi eerst dien Browser verifizeeren."

#, fuzzy
msgid "page.login.text2"
msgstr "Wenn du in en unendlichen Loop festsittst, empfelen wi, <a %(a_privacypass)s>Privacy Pass</a> to installen."

#, fuzzy
msgid "page.login.text3"
msgstr "Dat kann ok helpen, Adblockers un annere Browser-Extensions ut to schalten."

#, fuzzy
msgid "page.codes.title"
msgstr "Codes"

#, fuzzy
msgid "page.codes.heading"
msgstr "Codes-Explorer"

#, fuzzy
msgid "page.codes.intro"
msgstr "Erkunden Sie die Codes, mit denen Datensätze nach Präfixen markiert sind. Die Spalte „Datensätze“ zeigt die Anzahl der Datensätze, die mit Codes mit dem angegebenen Präfix markiert sind, wie in der Suchmaschine zu sehen (einschließlich nur Metadaten-Datensätze). Die Spalte „Codes“ zeigt, wie viele tatsächliche Codes ein gegebenes Präfix haben."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Diese Seite kann eine Weile dauern, um generiert zu werden, weshalb sie ein Cloudflare-Captcha erfordert. <a %(a_donate)s>Mitglieder</a> können das Captcha überspringen."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Bitte scrapen Sie diese Seiten nicht. Stattdessen empfehlen wir, unsere ElasticSearch- und MariaDB-Datenbanken <a %(a_import)s>zu generieren</a> oder <a %(a_download)s>herunterzuladen</a> und unseren <a %(a_software)s>Open-Source-Code</a> auszuführen. Die Rohdaten können manuell durch JSON-Dateien wie <a %(a_json_file)s>diese hier</a> erkundet werden."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Präfix"

#, fuzzy
msgid "common.form.go"
msgstr "Los"

#, fuzzy
msgid "common.form.reset"
msgstr "Zurücksetzen"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Anna’s Archiv dörchsöken"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Warnung: Der Code enthält falsche Unicode-Zeichen und könnte in verschiedenen Situationen falsch funktionieren. Die Rohdaten können aus der Base64-Darstellung in der URL dekodiert werden."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bekanntes Code-Präfix „%(key)s“"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Präfix"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Beschreibung"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL für einen spezifischen Code"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "„%%s“ wird durch den Wert des Codes ersetzt"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generische URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Websteed"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s Opnahmen, de to “%(prefix_label)s” passen"
msgstr[1] "%(count)s Opnahmen, de to “%(prefix_label)s” passen"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL för spesifischen Kood: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mehr…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kooden, de mit “%(prefix_label)s” anfungen"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Inhalt vun"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "Opnahmen"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "Kooden"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Weniger as %(count)s Opnahmen"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "För DMCA / Copyright-Ansprüch, brük <a %(a_copyright)s>dit Formular</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Annere Weegen, uns över Copyright-Ansprüch to kontakteeren, warrt automaatsch löscht."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Wi freet uns över dien Feedback un Froogen!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Aver, wegen de vele Spam un Unsinn-E-Mails, die wi kriegen, check de Boxen, üm to bestäätigen, dat du disse Bedingungen för dat Kontakteeren verstahn hest."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Copyright-Ansprüch an disse E-Mail warrt ignoriert; brük dat Formular."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner-Server sünd nich verfügbar wegen Hosting-Schließungen. Se schüllt bald wedder up sünd."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Mitgliedschaften warrt dementsprechend verlängert."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Mail uns nich, üm <a %(a_request)s>Böker to anfragen</a><br>oder lütte (<10k) <a %(a_upload)s>Uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Wenn du Froogen to dien Konto oder Spenden hest, füüg dien Konto-ID, Screenshots, Quittungen, so veel Information as mööglich to. Wi checkt uns E-Mail all 1-2 Weeken, also dat nich tofögen warrt jedwede Lösung verlangsamen."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "E-Mail wiesen"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Copyright-Formulier"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Wenn Sie eine DCMA- oder andere Urheberrechtsbeschwerde haben, füllen Sie bitte dieses Formular so genau wie möglich aus. Wenn Sie auf Probleme stoßen, kontaktieren Sie uns bitte unter unserer speziellen DMCA-Adresse: %(email)s. Beachten Sie, dass an diese Adresse gesendete Ansprüche nicht bearbeitet werden, sie ist nur für Fragen gedacht. Bitte verwenden Sie das untenstehende Formular, um Ihre Ansprüche einzureichen."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs op Anna’s Archiv (erforderlich). Eine pro Zeile. Bitte nur URLs einfügen, die genau dieselbe Ausgabe eines Buches beschreiben. Wenn Sie Ansprüche für mehrere Bücher oder Ausgaben geltend machen möchten, reichen Sie dieses Formular bitte mehrfach ein."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Ansprüche, die mehrere Bücher oder Ausgaben zusammenfassen, werden abgelehnt."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Ihr Name (erforderlich)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adresse (erforderlich)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefonnummer (erforderlich)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-Mail (erforderlich)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Klare Beschreibung des Quellmaterials (erforderlich)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs des Quellmaterials (falls zutreffend). Eine pro Zeile. Bitte nur solche einfügen, die genau mit der Ausgabe übereinstimmen, für die Sie eine Urheberrechtsbeschwerde einreichen."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs des Quellmaterials, eine pro Zeile. Bitte nehmen Sie sich einen Moment Zeit, um in der Open Library nach Ihrem Quellmaterial zu suchen. Dies wird uns helfen, Ihren Anspruch zu überprüfen."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs zum Quellmaterial, eine pro Zeile (erforderlich). Bitte so viele wie möglich einfügen, um uns bei der Überprüfung Ihres Anspruchs zu helfen (z.B. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Erklärung und Unterschrift (erforderlich)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Anspruch einreichen"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Vielen Dank für das Einreichen Ihrer Urheberrechtsbeschwerde. Wir werden sie so schnell wie möglich überprüfen. Bitte laden Sie die Seite neu, um eine weitere einzureichen."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Etwas ist schief gelaufen. Bitte laden Sie die Seite neu und versuchen Sie es erneut."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Wenn Sie daran interessiert sind, dieses Datenset für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainings</a>-Zwecke zu spiegeln, kontaktieren Sie uns bitte."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Uns Misschoon is, all Böker op de Welt (as ok Papers, Tiedschriften, etc.) to archivieren un wietgängich togänglich to maken. Wi glöven, dat all Böker wiet un breet spegelt warrn schüllt, üm Redundanz un Resilienz to sichern. Dorüm sammlen wi Files ut verschedenen Quellen. Somm Quellen sünd ganz open un köönt in Masse spegelt warrn (as Sci-Hub). Annere sünd sluten un schützend, dorüm versöken wi, se to scrapen, üm ehr Böker to „befreien“. Wedder annere liggt dor twischen."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "All uns Daten köönt <a %(a_torrents)s>torrented</a> warrn, un all uns Metadaten köönt <a %(a_anna_software)s>generiert</a> oder <a %(a_elasticsearch)s>downlaodet</a> warrn as ElasticSearch- un MariaDB-Datenbanken. De rohdaten köönt manuell dör JSON-Files as <a %(a_dbrecord)s>dit</a> exploriert warrn."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Överblick"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Hier is en schnellen Överblick över de Quellen vun de Files op Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Born"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Grött"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% spegelt vun AA / Torrents verfügbar"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Prozentsätz vun de Anzohl vun Dateien"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Toletzt aktualisiert"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Sachböker un Romanen"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s File"
msgstr[1] "%(count)s Files"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Över Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: infahrn siet 2021; de meisten över Torrents verfügbar"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: lütte Tofögen sietden</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Utsluten “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Romanen-Torrents sünd achter (obwohl IDs ~4-6M nich torrented sünd, weil se mit unsen Zlib-Torrents överlappt)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "De „Chinese“-Sammlung in Z-Library schient de sülvige as uns DuXiu-Sammlung to wesen, man mit anners MD5s. Wi schullen disse Dateien ut Torrents ut, üm Dubletten to vermieden, man wiesen se noch in uns Söökenindex."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ vun de Dateien sünd dörchsöökbar."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Dubletten utnehmen"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Da de Schattbibliotheken sik vaak ünnernanner synchronisieren, gifft dat betüchlich Överlapptwischen de Bibliotheken. Dat is, worüm de Tallen nich tosammpassen."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "De „mirrored and seeded by Anna’s Archive“-Prozent wiest, wieveel Dateien wi sülvst spegeln. Wi seed disse Dateien in grote Mengen dörch Torrents, un maken se direkt dörch Partnerwebsites togänglich."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Ursprungsbibliotheken"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Somm Bibliotheken fördert dat massenhafte Deelen vun ehr Daten över Torrents, annere deelt ehr Sammlungen nich so giern. In dat leste Fall versöcht Anna’s Archive, ehr Sammlungen to scrapen un verfügbar to maken (seih unsen <a %(a_torrents)s>Torrents</a>-Sied). Dor gifft dat ok Mittelwägen, as wenn Bibliotheken bereit sünd to deelen, aver nich de Ressourcen hebben. In disse Fälle versöken wi ok to helpen."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Hier ünner is en Överblick, wo wi mit de verschiddene Quellen-Bibliotheken interagieren."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Quelle"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Dateien"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Daglich <a %(dbdumps)s>HTTP-Datenbank-Dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatische Torrents för <a %(nonfiction)s>Sachbücher</a> un <a %(fiction)s>Belletristik</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna’s Archiv verwaltet en Samlung vun <a %(covers)s>Bookcover-Torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen „scimag“"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub hett siet 2021 keen ne’e Dateien mehr tofögt."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadaten-Dumps sünd verfügbar <a %(scihub1)s>hier</a> un <a %(scihub2)s>hier</a>, as ok as Deel vun de <a %(libgenli)s>Libgen.li Datenbank</a> (de wi bruuken)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Datentorrents sünd verfügbar <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a>, un <a %(libgenli)s>hier</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s En poor nee Dateien worrt <a %(libgenrs)s>tofögt</a> to Libgen’s “scimag”, aver nich genug för nee Torrents"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Vierteljählich <a %(dbdumps)s>HTTP-Datenbank-Dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiction Torrents worrt mit Libgen.rs deelt (un spegelt <a %(libgenli)s>hier</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna’s Archiv un Libgen.li fört jemainsam Sammlungen vun <a %(comics)s>Tegnböker</a>, <a %(magazines)s>Tiedschriften</a>, <a %(standarts)s>Standarddokumente</a> un <a %(fiction)s>Fiktschoon (afwichen vun Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s De “fiction_rus” Sammlungen (russische Fiktschoon) hett keen eegene Torrents, aver de sünd dörch annere Torrents dekt, un wi holt en <a %(fiction_rus)s>Spegel</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna sien Archiv un Z-Library förwaltet tosam en Samlung vun <a %(metadata)s>Z-Library Metadaten</a> un <a %(files)s>Z-Library Dateien</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Eenige Metadaten sünd över <a %(openlib)s>Open Library Datenbank Dumps</a> togänglich, aver de dekken nich de ganze IA-Sammlung"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Keen lichten togängliche Metadaten-Dumps för de ganze Sammlung"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(ia)s>IA Metadaten</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Dateien sünd bloots för en begrenzt Tied to leihen, mit verschedenen Toegangsbeschränkungen"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(ia)s>IA Dateien</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Verschieden Metadaten-Datenbanken sünd över dat chinesche Internet verstreut; aver oft sünd dat betahlte Datenbanken"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Keen lichten togängliche Metadaten-Dumps för de ganze Sammlung."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(duxiu)s>DuXiu Metadaten</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Verschieden Dateidatenbanken sünd över dat chinesche Internet verstreut; aver oft sünd dat betahlte Datenbanken"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s De meisten Dateien sünd bloots mit Premium BaiduYun Konten togänglich; langsame Download-Geschwindigkeiten."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(duxiu)s>DuXiu Dateien</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Verschieden lütte oder eenmalige Quellen. Wi animieren Lüüd, eerst to annere Schattenbibliotheken to uploaden, aver manchmol hebbt Lüüd Sammlungen, de to groot sünd för annere to sorteren, aver nich groot genug för en egen Kategorie."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Metadaten-only Quellen"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Wi rieken uns Sammlungen ok mit Metadaten-only Quellen an, de wi mit Dateien verknüppen köönt, z.B. över ISBN-Nummern oder annere Felder. Hier ünner is en Överblick vun disse Quellen. Wedder gifft dat ganz offene Quellen, annere mütt wi scrapen."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Uns Inspiration för dat Sammlen vun Metadaten is Aaron Swartz sien Ziel vun „een Websied för jümmer Book, dat je publiziert worrn is“, för dat he <a %(a_openlib)s>Open Library</a> maakt hett. Dat Projekt hett sik good maakt, aver uns unieke Position maakt dat uns mööglich, Metadaten to kriegen, de se nich kriegen köönt. Een annere Inspiration weer uns Wünsch to weten <a %(a_blog)s>wieviel Böker in de Welt gifft</a>, so dat wi utrechnen köönt, wieveel Böker wi noch retten mööt."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Tüüg dorup, dat wi bi de Metadaten-Söök de originalen Records wiesen. Wi maken keen Zesammenfügen vun Records."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Laatst aktualisiert"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Maandlich <a %(dbdumps)s>Datenbank Dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nich direkt in Masse togänglich, schützt tegen Scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(worldcat)s>OCLC (WorldCat) Metadaten</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Vereenigte Datenbank"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Wi kombineeren all de bovenste Quellen in een vereenigte Datenbank, de wi för disse Websied bruken. Disse vereenigte Datenbank is nich direkt verfügbar, aver as Anna’s Archive full open source is, kann se recht einfach <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>herunterladen</a> warrn as ElasticSearch un MariaDB Datenbanken. De Skripten op disse Sied laden automatsch all de nödig Metadaten vun de oben genannten Quellen."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Wenn Se uns Daten vöör dat lokale Laufen vun disse Skripten utproberen wüllt, köönt Se uns JSON-Dateien ankieken, de wieter to annere JSON-Dateien linken. <a %(a_json)s>Disse Datei</a> is en gooden Startpunkt."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Angepasst von unserem <a %(a_href)s>Blogbeitrag</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> is en groten Datenset vun inscannte Böker, maakt vun de <a %(superstar_link)s>SuperStar Digital Library Group</a>. De meisten sünd akademische Böker, de inscannt worrn sünd, üm se digital för Universitäten un Bibliotheken to vergeven. För uns engelschspraken Publikum hebbt <a %(princeton_link)s>Princeton</a> un de <a %(uw_link)s>University of Washington</a> goede Överblicken. Dor is ok en utmärkten Artikel, de mehr Background gifft: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "De Böker vun Duxiu sünd lang op den chinesischen Internet piratert worrn. Normalerwies warrn se för minder as en Dollar vun Wiederverköpern verkaft. Se warrn typisch över den chinesischen Google Drive-Äquivalent verteilt, de oft hackt worrn is, üm mehr Speicherruum to hebben. En paar technische Details finnst du <a %(link1)s>hier</a> un <a %(link2)s>hier</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Obwohl de Böker halvoffentlich verteilt worrn sünd, is dat recht swor, se in grooten Mengen to kriegen. Wi harrn dat höög op uns TODO-Liste, un hebbt meerdere Maanden fulltiet doran arbeit. Aver, in laten 2023 hett en inkrediblen, fantastischen un talentierten Friewilligen uns kontaktiert un seggt, dat se all dat Arbeid al maakt hebbt — mit groten Utkosten. Se hebbt de ganze Sammlung mit uns deelt, ohne wat dorför to verwachten, utser de Garantie vun langfrüstigen Bewahren. Wirklich bemerkenswert."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Ressourcen"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Gesamtdateien: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totale Dateigröße: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Dateien gespiegelt von Anna’s Archiv: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Zuletzt aktualisiert: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents von Anna’s Archiv"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Beispieldatensatz auf Anna’s Archiv"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Uns Blogbeitrag över dis Daten"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripte zum Importieren von Metadaten"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archiv Container-Format"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mehr Informationen vun uns Friewilligen (raw Notizen):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dieses Dataset steht in enger Beziehung zum <a %(a_datasets_openlib)s>Open Library Dataset</a>. Es enthält einen Scrape aller Metadaten und einen großen Teil der Dateien aus der kontrollierten digitalen Leihbibliothek der IA. Updates werden im <a %(a_aac)s>Anna’s Archiv Container-Format</a> veröffentlicht."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Diese Datensätze werden direkt aus dem Open Library Dataset referenziert, enthalten aber auch Datensätze, die nicht in der Open Library sind. Wir haben auch eine Reihe von Datendateien, die im Laufe der Jahre von Community-Mitgliedern gescraped wurden."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Die Sammlung besteht aus zwei Teilen. Sie benötigen beide Teile, um alle Daten zu erhalten (außer veraltete Torrents, die auf der Torrents-Seite durchgestrichen sind)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "unsere erste Veröffentlichung, bevor wir auf das <a %(a_aac)s>Anna’s Archiv Container (AAC) Format</a> standardisiert haben. Enthält Metadaten (als json und xml), PDFs (aus den digitalen Leihsystemen acsm und lcpdf) und Cover-Thumbnails."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementelle Neuveröffentlichungen, unter Verwendung von AAC. Enthält nur Metadaten mit Zeitstempeln nach dem 01.01.2023, da der Rest bereits durch „ia“ abgedeckt ist. Auch alle PDF-Dateien, diesmal aus den Leihsystemen acsm und „bookreader“ (IA’s Web-Reader). Trotz des nicht ganz passenden Namens fügen wir weiterhin Bookreader-Dateien in die Sammlung ia2_acsmpdf_files ein, da sie sich gegenseitig ausschließen."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Haupt %(source)s Websteed"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitale Leihbibliothek"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadaten-Dokumentation (die meisten Felder)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN-Land-Informationen"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "De Internationale ISBN-Agenschaft geiht regelmääßig de Rangen rut, de se an nationale ISBN-Agenschaften verteelt hett. Dorut köönt wi afleiden, to welk Land, Region oder Spraakgrupp disse ISBN höört. Wi bruken disse Daten op’n Oomweg, över de <a %(a_isbnlib)s>isbnlib</a> Python-Bibliothek."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Ressourcen"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Laatst aktualiseert: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN-Websteed"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadaten"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "För de Achtergrund vun de verscheiden Library Genesis-Forks, kiek op de Sied för de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "De Libgen.li enthält de meisten vun de sülven Inhollen und Metadaten as de Libgen.rs, aber hett noch wat Sammlungen doröver, nämlich Comics, Tiedschriften und Standarddokumente. Se hett ok <a %(a_scihub)s>Sci-Hub</a> in ehr Metadaten und Söökmotor integrert, wat wi för ünner Datenbank bruken."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "De Metadaten för disse Bibliothek sünd frie togänglich <a %(a_libgen_li)s>op libgen.li</a>. Allerding is disse Server langsam und ünnerstütt nich dat Fortsetten vun afbraken Verbindungen. De sülven Dateien sünd ok op <a %(a_ftp)s>en FTP-Server</a> togänglich, wat beter funktschooniert."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents sünd för de meisten vun de annere Inholt to kriegen, besünners för Tegnböker, Tiedschriften un Standarddokumente, de in Sammerarbeit mit Anna’s Archiv rutkamen sünd."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "De Fiktschoon-Sammlung hett eegene Torrents (afwichen vun <a %(a_href)s>Libgen.rs</a>) beginnend bi %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Laut den Libgen.li-Administrator schall de “fiction_rus” (russische Fiktschoon) Sammlung dörch regelmääßig rutkommene Torrents vun <a %(a_booktracker)s>booktracker.org</a> dekt warrn, besünners de <a %(a_flibusta)s>flibusta</a> un <a %(a_librusec)s>lib.rus.ec</a> Torrents (de wi <a %(a_torrents)s>hier</a> spegeln, obwool wi noch nich faststellt hebbt, welke Torrents to welke Dateien höört)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistiken för all Sammlungen sünd <a %(a_href)s>op de Libgen-Website</a> to finnen."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Sachböker scheinen ok anners worrn to wesen, aver dat gifft keen niege Torrents. Dat schient siet fröh 2022 so to wesen, aver wi hebbt dat nich pröövt."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Bestimmte Reeken ohne Torrents (so as Fiktschoon-Reeken f_3463000 bis f_4260000) sünd wahrscheinlik Z-Library (oder annere Duplikat) Dateien, obwool wi mööglicherweise en Dedublikatschoon maken un Torrents för lgli-eegene Dateien in disse Reeken maken mööchten."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Wies dorup hen, dat de Torrent-Dateien, de op „libgen.is“ wiest, explizit Speegels vun <a %(a_libgen)s>Libgen.rs</a> sünd („.is“ is en anner Domain, de vun Libgen.rs bruukt warrt)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "En nützliche Ressource för’t Bruken vun de Metadaten is <a %(a_href)s>disse Sied</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiction-Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Comics-Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Magazinen-Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standarddokumente Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russische Fiktschoon Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadaten"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadaten över FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informationen över Metadaten-Felder"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Speegel vun annere Torrents (un unieke Fiction- un Comics-Torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Ünnern Blogpost över de Comics-Veröffentlichung"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "De kort Historie vun de verscheden Library Genesis (oder „Libgen“) Forks is, dat över Tied de verscheden Lüüd, de bi Library Genesis inwullt worrn, sick överworren hebbt un ehr eegen Weg gahn sünd."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "De „.fun“-Version wurr vun den oorsprünglichen Grunner maakt. Se warrt nu överholt för en nee, mehr verteilte Version."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "De „.rs“-Version hett ganz ähnliche Daten un veröffentlicht ehr Sammlungen meestens in groote Torrent-Paketen. Se is ungefäähr in en „Fiction“- un en „Non-Fiction“-Sektion opdeelt."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oorsprünglich bi „http://gen.lib.rus.ec“."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "De <a %(a_li)s>„.li“-Version</a> hett en groote Sammlung vun Comics, as ok annere Inholt, de (noch) nich för en groote Torrent-Download to kriegen sünd. Se hett aver en separate Torrent-Sammlung vun Fiction-Böker un hett de Metadaten vun <a %(a_scihub)s>Sci-Hub</a> in ehr Datenbank."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Noh disse <a %(a_mhut)s>Forum-Post</a> weer Libgen.li oorsprünglich bi „http://free-books.dontexist.com“ to finnen."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> is in een Sinn ok en Fork vun Library Genesis, obschon se en anner Naam för ehr Projekt bruukt hebbt."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Disse Sied geiht över de „.rs“-Version. Se is bekannt dorför, dat se regelmääßig ehr Metadaten un de ganze Inholt vun ehr Bökerkatalog veröffentlicht. Ehr Böker-Sammlung is in en Fiction- un en Non-Fiction-Teil opdeelt."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "En nützliche Ressource för’t Bruken vun de Metadaten is <a %(a_metadata)s>disse Sied</a> (IP-Ranges warrt blockt, VPN könnt nödig wesen)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "As vun 2024-03, nee Torrents warrt in <a %(a_href)s>disse Forumthread</a> postet (IP-Ranges warrt blockt, VPN könnt nödig wesen)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-Fiction Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiction Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs Metadata-Feldinformationen"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-Fiction Torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiction Torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents vun Anna’s Archiv (Bookcovers)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Üüs Blog över de Bookcovers-Release"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis is bekannt för dat se ehr Daten al generös in Bulk över Torrents verfügbar maakt. Üüs Libgen-Sammlung bestäht ut Hilfsdaten, de se nich direkt freigewt, in Partnerschaft mit ehr. Veel Dank an all, de bi Library Genesis mitmakt hebbt för de Zusammenarbeit mit uns!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Disse <a %(blog_post)s>eerste Release</a> is recht lütt: ümtrent 300GB Bookcovers vun de Libgen.rs-Fork, baid Fiction un Non-Fiction. Se sünd so organiseert, as se op libgen.rs to finnen sünd, z.B.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s för en Non-Fiction Book."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s för en Fiction Book."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Jüst as bi de Z-Library-Sammlung, hebbt wi se all in en groot .tar-Datei packt, de mit <a %(a_ratarmount)s>ratarmount</a> mount warrn kann, wenn ji de Dateien direkt serven wüllt."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> is en propritäre Datenbank vun de non-profit <a %(a_oclc)s>OCLC</a>, de Metadaten-Records vun Bibliotheken ut de ganze Welt aggregiert. Dat is warschau de grötste Bibliotheks-Metadatensammlung in de Welt."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, eerste Utgaav:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "In Oktober 2023 hebbt wi en umfassende Scrape vun de OCLC (WorldCat) Datenbank rutbröcht, in de <a %(a_aac)s>Anna’s Archiv Containers Format</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents vun Anna’s Archiv"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Ons blogbericht over deze gegevens"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library is en Open-Source-Projekt vun de Internet Archive, üm jümmer Book in de Welt to katalogisieren. Se hett een vun de gröttsten Book-Scan-Operationen in de Welt un hett veel Book för digital Leih verfügbar. Ehr Book-Metadata-Katalog is fre verfügbar för Download un is op Anna’s Archiv inbunnen (allerdings nich direkt in de Sööken, utse wenn ji explizit na en Open Library ID söökt)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Release 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dit is en Dump vun vele Anfragen an isbndb.com in September 2022. Wi hebbt versöcht, all ISBN-Rangen to decken. Dit sünd ümtrent 30,9 Millionen Datensätze. Op ehr Websteed seggt se, dat se in’t Goren 32,6 Millionen Datensätze hebbt, also hebbt wi vleicht wat verpasst, oder <em>se</em> maakt wat verkehrt."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "De JSON-Antworten sünd fast roh vun ehr Server. Een Datenqualitätsproblem, dat wi bemerkt hebbt, is dat för ISBN-13-Nummern, de mit en annern Präfix as „978-“ anfängt, se noch immer en „isbn“-Feld hebben, dat einfach de ISBN-13-Nummer mit de ersten 3 Nummern afsnieden hett (und de Prüfziffer nöög berechnet). Dit is natüürlich verkehrt, aber so maakt se dat, also hebbt wi dat nich ännert."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Een anner mööglichen Problem, dat Se hebben köönt, is dat dat „isbn13“-Feld Duplikaten hett, also köönt Se dat nich as Primärschlüssel in en Datenbank bruken. „isbn13“+„isbn“-Felder in Kombinatschoon scheinen eenzigartig to wesen."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "För en Background över Sci-Hub, kiek op de <a %(a_scihub)s>offizielle Websiet</a>, de <a %(a_wikipedia)s>Wikipedia-Siet</a>, un dissen <a %(a_radiolab)s>Podcast-Interview</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Wichtige Information: Sci-Hub is siet <a %(a_reddit)s>2021 tofroren</a>. Dat weer vörher ok al mol so, man 2021 sünd noch een poor Millionen Papers tofögt worrn. Doch gifft dat noch een beperkt An’n Papers, de to de Libgen “scimag” Sammlungen tofögt warrt, man dat is nich genug, üm ne’e große Torrents to rechtfertigen."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Wi bruukt de Sci-Hub Metadaten, as se vun <a %(a_libgen_li)s>Libgen.li</a> in de “scimag” Sammlung anbioten warrt. Wi bruukt ok dat <a %(a_dois)s>dois-2022-02-12.7z</a> Dataset."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Wichtige Information: De “smarch” Torrents sünd <a %(a_smarch)s>veraltet</a> un warrt dorüm nich in uns Torrents-Liste opnommen."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents op Anna’s Archiv"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadaten un Torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents op Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents op Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Updates op Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-Sied"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast-Interview"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uploads to Anna’s Archiv"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Överblick vun de <a %(a1)s>Datasets-Siet</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Verschieden lüttje oder eenmalige Quellen. Wi animiert Lüüd, eerst to annere Schattenbibliotheken to uploaden, man mankmal hebbt Lüüd Sammlungen, de to groot sünd för annere, üm se dörch to seihn, man nich groot genug, üm en egen Kategorie to warrt."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "De “upload” Sammlung is in lüttje Untersammlungen opdeelt, de in de AACIDs un Torrent-Namen angeven warrt. All Untersammlungen sünd eerst mit de Hööftsammlung dedupliziert worrn, man de Metadaten “upload_records” JSON Dateien hebbt noch veel Verwiisungen to de oorsprünglichen Dateien. Nich-Buch Dateien sünd ok ut de meisten Untersammlungen rutnahmen worrn un sünd typisch <em>nich</em> in de “upload_records” JSON angeven."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Veel Untersammlungen bestahn sülvst ut Untersubsammlungen (z.B. vun verschidden oorsprünglichen Quellen), de as Dossiers in de “filepath” Felder angeven sünd."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "De Untersammlungen sünd:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Unnersammlen"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notizen"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "bläddere"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "söken"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Vun <a %(a_href)s>aaaaarg.fail</a>. Schient recht komplett to wesen. Vun uns Friewilligen “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Vun en <a %(a_href)s><q>ACM Digital Library 2020</q></a> Torrent. Hett recht veel Överlap mit bestahn Papers-Sammlungen, man ganz wenig MD5-Treffers, darum hebbt wi beslaten, dat ganz to behollen."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Afkratzen vun <q>iRead eBooks</q> (= fonetisch <q>ai rit i-books</q>; airitibooks.com), vun Friewilligen <q>j</q>. Entspricht <q>airitibooks</q> metadata in <a %(a1)s><q>Annere metadata-afkratzen</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Ut en Sammlen <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Deelwies ut de orijinale Quelle, deelwies vun the-eye.eu, deelwies vun annere Spiegels."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Ut en privaten Books-Torrent-Websteed, <a %(a_href)s>Bibliotik</a> (oft as “Bib” bekennt), vun de Books in Torrents na Naam (A.torrent, B.torrent) bundelt weern un över the-eye.eu verdeelt weern."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Ut unsen Friewilligen “bpb9v”. För mehr Informatschonen över <a %(a_href)s>CADAL</a>, kiek in de Notizen op unsen <a %(a_duxiu)s>DuXiu-Dataset-Sied</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mehr vun unsen Friewilligen “bpb9v”, meist DuXiu-Dateien, as ok en Ordner “WenQu” un “SuperStar_Journals” (SuperStar is de Firma achter DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Ut unsen Friewilligen “cgiym”, chinesische Texte ut verschedenen Quellen (as Unnerdiekter representiert), inklusiv vun <a %(a_href)s>China Machine Press</a> (en grote chinesische Verlag)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Nich-chinesische Samlungen (as Unnerdiekter representiert) vun unsen Friewilligen “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Afkratzen vun Böker över chinesische Architektur, vun Friewilligen <q>cm</q>: <q>Ik heff dat kriegen dör dat Exploitieren vun en Netzwark-Swackpunkt bi de Verlaag, aver de Loop is nu to</q>. Entspricht <q>chinese_architecture</q> metadata in <a %(a1)s><q>Annere metadata-afkratzen</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Books vun akademischen Verlag <a %(a_href)s>De Gruyter</a>, ut en paar grote Torrents samelt."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape vun <a %(a_href)s>docer.pl</a>, en polnischen Dateidelen-Websteed, de sik op Books un annere Schrievwerken fokussiert. In laten 2023 vun Friewilligen “p” scrapet. Wi hebbt nich good Metadaten vun den originalen Websteed (nich eens Dateiendungen), aver wi hebbt för book-ähnliche Dateien filtert un köönt oft Metadaten ut de Dateien sülvst utkriegen."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu-epubs, direkt vun DuXiu, samelt vun Friewilligen “w”. Blots ne’e DuXiu-Books sünd direkt över eBooks togänglich, also mööt de meisten vun disse ne’e wesen."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Restliche DuXiu-Dateien vun Friewilligen “m”, de nich in dat DuXiu-eigenen PDG-Format weern (de Haupt-<a %(a_href)s>DuXiu-Dataset</a>). Ut vele originalen Quellen samelt, leider ohne disse Quellen in den Dateipath to bewaren."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Afkratzen vun erotische Böker, vun Friewilligen <q>do no harm</q>. Entspricht <q>hentai</q> metadata in <a %(a1)s><q>Annere metadata-afkratzen</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Samlung scrapet vun en japanischen Manga-Verlag vun Friewilligen “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Utwählte justiziale Archiven vun Longquan</a>, vun Friewilligen “c” provided."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape vun <a %(a_href)s>magzdb.org</a>, en Verbündeten vun Library Genesis (dat is op de libgen.rs-Startseet linked) aver de nich direkt ehr Dateien provide wullt. In laten 2023 vun Friewilligen “p” kriegen."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Verschiedene lütte Uploads, to lütt för en egen Unnersammlung, aver as Diekter representiert."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks vun AvaxHome, en russische Dateideel-Websiet."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archiv vun Tiedungen un Tiedschriften. Entspricht <q>newsarch_magz</q> metadata in <a %(a1)s><q>Annere metadata-afkratzen</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Afkratzen vun de <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Samlung vun Friewilligen “o”, de polnische Books direkt vun originalen Release- (“Scene”-) Websteeds samelt hett."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Kombineerte Samlungen vun <a %(a_href)s>shuge.org</a> vun Friewilligen “cgiym” un “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (na de fiktive Bibliothek nöömt), 2022 vun den Friewilligen “t” scrapet."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-collecties (weergegeven als mappen) van vrijwilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (door <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, mijn kleine boekenkamer — woz9ts: “Deze site richt zich voornamelijk op het delen van hoogwaardige e-boekbestanden, waarvan sommige door de eigenaar zelf zijn opgemaakt. De eigenaar werd <a %(a_arrested)s>gearresteerd</a> in 2019 en iemand maakte een verzameling van de bestanden die hij deelde.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Overgebleven DuXiu-bestanden van vrijwilliger “woz9ts”, die niet in het DuXiu-eigen PDG-formaat waren (nog om te zetten naar PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents van Anna’s Archief"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library heeft zijn wortels in de <a %(a_href)s>Library Genesis</a> gemeenschap, en oorspronkelijk opgestart met hun gegevens. Sindsdien is het aanzienlijk geprofessionaliseerd en heeft het een veel modernere interface. Ze zijn daarom in staat om veel meer donaties te krijgen, zowel financieel om hun website te blijven verbeteren, als donaties van nieuwe boeken. Ze hebben een grote collectie verzameld naast Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Update vanaf februari 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Eind 2022 werden de vermeende oprichters van Z-Library gearresteerd en werden domeinen in beslag genomen door de Amerikaanse autoriteiten. Sindsdien is de website langzaam weer online gekomen. Het is onbekend wie het momenteel beheert."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "De collectie bestaat uit drie delen. De oorspronkelijke beschrijvingspagina's voor de eerste twee delen zijn hieronder bewaard gebleven. Je hebt alle drie de delen nodig om alle gegevens te krijgen (behalve verouderde torrents, die zijn doorgestreept op de torrentpagina)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: onze eerste release. Dit was de allereerste release van wat toen de “Pirate Library Mirror” (“pilimi”) werd genoemd."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: tweede release, dit keer met alle bestanden verpakt in .tar-bestanden."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: incrementele nieuwe releases, met behulp van het <a %(a_href)s>Anna’s Archief Containers (AAC) formaat</a>, nu uitgebracht in samenwerking met het Z-Library team."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents van Anna’s Archief (metadata + inhoud)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Voorbeeldrecord op Anna’s Archief (originele collectie)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Voorbeeldrecord op Anna’s Archief (“zlib3” collectie)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Hoofdwebsite"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domein"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blogbericht over Release 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blogpost över Release 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-Releases (originale Beschrieven-Sieden)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "De eerste Spegel is möösam över de Tied vun 2021 un 2022 kriegen worrn. To dissen Tiedpunkt is he een beten veraltet: he wiest den Stand vun de Sammlung in Juni 2021. Wi wüllt dat in de Zukunft updaten. Just nu focussiert wi uns dorup, dissen ersten Release rut to brengen."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Sünner Library Genesis al mit public Torrents bewohren is un in de Z-Library inbunnen is, hebbt wi en basic Deduplication gegen Library Genesis in Juni 2022 maakt. Dorför hebbt wi MD5-Hashes bruukt. Dat is warschau, dat dor veel mehr duplizierten Inholt in de Bibliothek is, so as multiple Dateiformaten mit dat sülvst Book. Dat is swor genau to erkennen, dorüm doot wi dat nich. Na de Deduplication hebbt wi över 2 Millionen Dateien över, mit en Gesamtgrött vun knapp 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "De Samlung bestiht ut twee Deele: en MySQL “.sql.gz” Dump vun de Metadaten un de 72 Torrentdateien vun ümto 50-100GB jeedeen. De Metadaten enthoolt de Daten, as se vun de Z-Library Websteed angeven warrt (Titel, Autor, Beschrieven, Dateityp), as ok de faktische Dateigrött un md5sum, de wi observeert hebbt, denn mankmal stemmt dat nich övereen. Dat gifft Ansehn, wo de Z-Library sülvst verkehrte Metadaten hett. Wi köönt ok in eenzelne Fäll verkehrt Dateien rodden hebbt, de wi in de Zukunft versöken to finnen un to fixen."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "De groote Torrentdateien enthoolt de faktische Bookdaten, mit de Z-Library ID as Dateinaam. De Dateiendungen köönt mit de Metadaten-Dump rekonstrueert warrn."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "De Samlung is en Mix vun Sachbücher un Belletristik (nich trenn as bi Library Genesis). De Qualität is ok heel verscheden."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Disse eerste Release is nu fullstännig verfügbar. Achtet dorop, dat de Torrentdateien bloots över uns Tor-Mirror verfügbar sünd."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Release 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Wi hebbt all Böker kriegen, de twischen uns letzte Mirror un August 2022 to Z-Library toföögt worrn sünd. Wi hebbt ok noch mol Böker rutkramt, de wi bi de eerste Mal verpasst hebbt. Allens in all, disse niege Samlung is ümto 24TB groot. Noch mol, disse Samlung is deduplizeert tegen Library Genesis, denn dor gifft al Torrents för disse Samlung."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "De Daten sünd ähnlich struktureert as bi de eerste Release. Dor is en MySQL “.sql.gz” Dump vun de Metadaten, de ok all Metadaten vun de eerste Release enthoolt, un domit överholt. Wi hebbt ok nee Spalten toföögt:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: of disse Datei al in Library Genesis is, in de Sachbuch- oder Belletristik-Sammlung (vergliek mit md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in welk Torrent disse Datei is."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: sett, wenn wi dat Book nich runterladen köönt."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Wi hebbt dat letzte Mal dat anmerkt, man bloots to klarstellen: “filename” un “md5” sünd de faktische Eegenschaften vun de Datei, wohingegen “filename_reported” un “md5_reported” dat sünd, wat wi vun Z-Library rutkramt hebbt. Mankmal stemmt disse twee nich övereen, dorüm hebbt wi beidens inbunnen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "För disse Release hebbt wi de Kollation to “utf8mb4_unicode_ci” ännert, wat kompatibel mit öllere Versionen vun MySQL wesen schall."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "De Datendateien sünd ähnlich as dat letzte Mal, man se sünd veel grötter. Wi köönt uns nich dörchringen, tonnenweise lüttje Torrentdateien to maken. “pilimi-zlib2-0-14679999-extra.torrent” enthoolt all Dateien, de wi bi de letzte Release verpasst hebbt, wohingegen de annern Torrents all nee ID-Bereeken sünd. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> Wi hebbt de meisten vun uns Torrents to groot maakt, wat de Torrent-Clients överfordert hett. Wi hebbt se rutnahmen un nee Torrents veröfentlicht."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> Dor wöörn noch toveel Dateien, dorüm hebbt wi se in tar-Dateien inbunnen un nee Torrents veröfentlicht."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Release 2 Addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dit is een enkel extra torrent-bestand. Het bevat geen nieuwe informatie, maar het heeft wat gegevens die enige tijd kunnen kosten om te berekenen. Dat maakt het handig om te hebben, aangezien het downloaden van deze torrent vaak sneller is dan het vanaf nul te berekenen. In het bijzonder bevat het SQLite-indexen voor de tar-bestanden, voor gebruik met <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Frequently Asked Questions (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Wat is Anna’s Archive?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> is en gemeennüützigen Projekt mit twee Ziele:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Bewahren:</strong> All Wissen un Kultur vun de Minschheit sichern.</li><li><strong>Toegang:</strong> Dit Wissen un Kultur för jümmer op de Welt togänglich maken.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "All uns <a %(a_code)s>Code</a> un <a %(a_datasets)s>Daten</a> sünd ganz open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Bewahren"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Wi bewahrt Böker, Papers, Comics, Tiedschriften un mehr, indem wi disse Materialien ut verscheden <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">Schattbibliotheken</a>, offizielle Bibliotheken un annere Sammlungen an een Steed tosamendoon. All disse Daten warrt för jümmer bewahrt, indem wi dat einfach maken, se in grooten Mengen to verdubbeln — mit Torrents — wat in vele Kopien överall op de Welt resultiert. Somm Schattbibliotheken doon dat al sülvst (z.B. Sci-Hub, Library Genesis), währnd Anna’s Archive annere Bibliotheken „befreit“, de keen Massendistributie bieden (z.B. Z-Library) oder gor keen Schattbibliotheken sünd (z.B. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Disse wide Verbreiden, tosamen mit open-source Code, maakt uns Websteed resistent gegen Takedowns un sicher de langfristege Bewahren vun de Minschheit ehr Wissen un Kultur. Lär mehr över <a href=\"/datasets\">unsere Datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Wi schätzt, dat wi ümtrent <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% vun de Welt ehr Böker bewahrt hebt.</a>"

#, fuzzy
msgid "page.home.access.header"
msgstr "Toegang"

#, fuzzy
msgid "page.home.access.text"
msgstr "Wi arbeit mit Partners tosamen, üm uns Sammlungen för jümmer un free togänglich för all to maken. Wi glöövt, dat jümmer een Recht op dat kollektiv Wissen vun de Minschheit hett. Un <a %(a_search)s>nich op de Kosten vun de Autoren</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Stündliche Downloads in de letzten 30 Dagen. Stündliche Durchschnitt: %(hourly)s. Dagliche Durchschnitt: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Wi glöövt stark an de free Fluss vun Information un dat Bewahren vun Wissen un Kultur. Mit disse Suchmaschine bouwen wi op de Schullern vun Giganten. Wi hebbt grooten Respekt för dat hart Arbeid vun de Lüüd, de de verscheden Schattbibliotheken opbaut hebt, un wi hofft, dat disse Suchmaschine ehr Reekweite vergröttert."

#, fuzzy
msgid "page.about.text3"
msgstr "Üm updatet to blieven över uns Fortschritt, fölgt Anna op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. För Fragen un Feedback, kontaktiert Anna bi %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Wo kann ik helpen?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Fölgt uns op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Vertellt över Anna’s Archive op Twitter, Reddit, Tiktok, Instagram, in jümmer lokal Café oder Bibliothek, oder wo ook immer! Wi glöövt nich an Gatekeeping — wenn wi dorhnahmen warrt, poppen wi einfach annerswöör up, denn all uns Code un Daten sünd ganz open source.</li><li>3. Wenn jümmer dat mööglich is, överlegt <a href=\"/donate\">to spenden</a>.</li><li>4. Helft <a href=\"https://translate.annas-software.org/\">uns Websteed in verscheden Spraken to översetten.</li><li>5. Wenn jümmer een Software-Ingenieur büst, överlegt to uns <a href=\"https://annas-software.org/\">open source</a> beidragen, oder uns <a href=\"/datasets\">Torrents</a> to seeden.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Wi hebbt nu ok en synchroniseerten Matrix-Kanal bi %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Wenn jümmer een Security-Forscher büst, köönt wi jümmer Fäigkeiten för Angrief un Verteidigung bruken. Kiekt op uns <a %(a_security)s>Security</a> Sied."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Wi söcht Experten för Betalungen för anonyme Händler. Kannst du uns helpen, mehr bequeme Möglichkeiten to adden, üm to spenden? PayPal, WeChat, Geschenkkaart. Wenn jümmer een kennt, kontaktiert uns."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Wi sünd alltid op de Sook na mehr Serverkapazität."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Jümmer könnt helpen, indem jümmer Dateiproblemen meldt, Kommentaren achterlaat, un Listens direkt op disse Websteed erstellt. Jümmer könnt ook helpen, indem jümmer <a %(a_upload)s>mehr Böker uploadet</a>, oder Dateiproblemen oder Formatierungen vun bestahn Böker fixen."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Erstellt oder helft bi de Pflege vun de Wikipedia-Sied för Anna’s Archive in jümmer Spraak."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Wi söcht lütte, smakige Reklameplatzen. Wenn ji op Anna’s Archive Reklame maken wüllt, laat us dat weten."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Wi würd dat giern hebben, wenn Lüüd <a %(a_mirrors)s>Spiegelsiten</a> opstellen, un wi würd dat finansiell ünnerstütten."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "För mehr utführliche Informationen, wie man as Friewilligen helpen kann, seih uns <a %(a_volunteering)s>Volunteering & Bounties</a>-Sied."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Wo sünd de lütten Downloads so lütt?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Wi hebben wortlich nich genug Ressourcen, üm all Lüüd op de Welt Höögschwindigkeits-Downloads to geven, so giern wi dat ok wüllt. Wenn en rieke Wohltäter dat för uns övernehmen wüllt, dat weer fantastsch, aver bis dorhen doot wi uns bestes. Wi sünd en Non-Profit-Projekt, dat sik knapp dör Spenden över Water hollen kann."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dit is, worüm wi twee Systemen för free Downloads mit uns Partners implementeert hebben: gedeelde Servern mit lütten Downloads un wat schnellere Servern mit en Warteliste (üm de Anzohl vun Lüüd, de to glieke Tied downloaden, to reduceren)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Wi hebben ok <a %(a_verification)s>Browser-Verifikation</a> för uns lütten Downloads, weil anners Bots un Scrapers dat misbruken würd, wat de Saken för legitime Brukers noch langsamer maakt."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Tüüg dorup, dat Se bi’t Bruken vun den Tor Browser mööglicherwies ehr Sekerheitseinstellungen anpassen mööt. Bi de lüttste Optioon, de „Standard“ heet, klappt de Cloudflare turnstile challenge. Bi de högeren Optiounen, de „Säkerer“ un „Am säkersten“ heet, geiht de Challenge nich."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "För grote Dateien kann dat manchmol passeren, dat de Downloads mitten in’t Afbraken. Wi rekommendere, en Download-Manager (so as JDownloader) to bruken, de automaatsch grote Downloads wedder opnimmt."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Spenden-FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Verlengen de Mitgliedschaften sik automatsch?</div> Mitgliedschaften verlengen sik <strong>nich</strong> automatsch. Ji könnt so lang oder kort Mitglied wesen, as ji wüllt."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kann ick mien Mitgliedschaft upstuwen oder meerdere Mitgliedschaften kriegen?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Hebbt ji annere Betahlmethoden?</div> Aktuell nich. Veel Lüüd wüllt nich, dat so’n Archiv existiert, dorüm mööt wi vorsichtig wesen. Wenn ji uns helpen könnt, annere (bequemere) Betahlmethoden sicher opstellen, laat uns dat weten bi %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Wat bedüden de Rangen pro Maand?</div> Du kannst de neddere Siet vun en Range erreichen, indem du all Rabatt anwendst, so as en Period länger as en Maand utwählen."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Worüm geiht de Spenden?</div> 100%% geiht dorüm, de Weltwissen un Kultur to bewaren un to vergeven. Aktuell geiht dat meist för Server, Speicher un Bandbreite. Keen Geld geiht an een Teammitglied personlich."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kann ik en grote Spende maken?</div> Dat weer fantastisch! För Spenden över een poor Duusend Dollar, neem direkt Kontakt op mit uns bi %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kann ik en Spende maken, ahn Mitglied to wesen?</div> Sicher. Wi nehmen Spenden vun elke Höögde op disse Monero (XMR) Adress: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Wo kann ik nee Böker hoochladen?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativ könnt ji se op Z-Library <a %(a_upload)s>hier</a> hoochladen."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "För lüttje Uploads (bis to 10.000 Dateien) ladt se bitte op beiden %(first)s un %(second)s hooch."

#, fuzzy
msgid "page.upload.text1"
msgstr "För nu, föhlt wi vör, nee Böker op de Library Genesis Forks hooch to laden. Hier is en <a %(a_guide)s>praktische Guide</a>. Merkt, dat beide Forks, de wi op disse Website indexeren, vun dit sülvige Upload-System trekken."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "För Libgen.li, maakt seker, dat ji eerst op <a %(a_forum)s >deren Forum</a> mit Brukernaam %(username)s un Passwort %(password)s inloggt, un denn torüch to deren <a %(a_upload_page)s >Upload-Siet</a> gahn."

#, fuzzy
msgid "common.libgen.email"
msgstr "Wenn jihr E-Mail-Adress nich op de Libgen-Forums werkt, föhlt wi Proton Mail (free) to bruken. Ji könnt ok <a %(a_manual)s>manuell anfragen</a>, dat jihr Account aktiviert warrt."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Merkt, dat mhut.org bestimmte IP-Bereiken blockt, so dat en VPN nödig wesen könnt."

#, fuzzy
msgid "page.upload.large.text"
msgstr "För grote Uploads (över 10.000 Dateien), de nich vun Libgen oder Z-Library annehmen warrt, neem Kontakt op mit uns bi %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "För akademische Papers, lad se ok (naast Library Genesis) op <a %(a_stc_nexus)s>STC Nexus</a> hooch. Se sünd de beste Schattenbibliothek för nee Papers. Wi hebben se noch nich integriert, aver dat kümmt noch. Ji könnt ehr <a %(a_telegram)s>Upload-Bot op Telegram</a> bruken, oder de Adress in ehr pinnte Nachricht kontaktieren, wenn jihr toveel Dateien hebt, üm se so to hoochladen."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Wo kann ik Böker anfragen?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Aktuell köönt wi keen Bök-Anfragen annehmen."

#, fuzzy
msgid "page.request.forums"
msgstr "Maak Dien Anfragen op de Foren vun Z-Library oder Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Schick uns keen E-Mail mit Dien Bookanfragen."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Sammelt ji Metadaten?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Dat doot wi."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Ik heff \"1984\" vun George Orwell daunloodt, kümmt de Polizei nu bi mi to Huus?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Maak Di keen groot Sorgen, vele Lüüd daunloade vun de Websieden, de wi verlinkt hebben, un dat is heel sülten, dat dor Probleme sünd. Man, üm sicher to blieven, raden wi an, en VPN (betalen) to bruken, oder <a %(a_tor)s>Tor</a> (free)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Wo kann ik mien Söökinstellingen spiekern?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Wähl de Instellingen, de Di gefallt, laat dat Söökfeld leeg, klick op „Söken“, un denn spieker de Sied as Bookmärk mit Dien Browser sien Bookmärk-Funktion."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Hebbt ji en Mobil-App?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Wi hebben keen offizielle Mobil-App, man du kannst disse Websied as App installern."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klick op dat Dreipunkt-Menü in de rechte Bovenhörn, un wähl „To Huussied tofögen“."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klick op den „Delen“-Knopp ünnen, un wähl „To Huussied tofögen“."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Hebbt ji en API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Wi hebben een stabile JSON API för Medleems, för en snelle Daunlood-URL to kriegen: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (Dokumentation binnen JSON sülvst)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "För annere Anwendengen, as dörch all uns Datein to iterieren, en egen Söök to bouwen, un so wieter, raden wi an, uns ElasticSearch- un MariaDB-Datenbanken to <a %(a_generate)s>genereren</a> oder to <a %(a_download)s>daunlooden</a>. De raw Daten köönt manuell <a %(a_explore)s>dörch JSON-Datein</a> explorert warrn."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Uns raw Torrents-Liste kann as <a %(a_torrents)s>JSON</a> daunloodt warrn."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Ik wull helpen bi’t Seeden, man ik heff nich veel Diskspieker."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Bruk den <a %(a_list)s>Torrent-Liste-Generator</a> to en Liste vun Torrents to genereren, de am meisten Seed-Hülp bruken, binnen Dien Spiekergrenzen."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "De Torrents sünd to langsam; kann ik de Daten direkt vun ji daunlooden?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ja, seht de <a %(a_llm)s>LLM-Daten</a>-Sied."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Kann ik ok bloß en Deel vun de Dateien dalladen, as bloß en bestimmd Sprook of Thema?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Korte Antwurd: nich so einfach."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Lange Antwurd:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "De meisten Torrents hebbt de Dateien direkt, dat bedüüt, dat ji Torrent-Clients anwiessen könnt, bloß de nödig Dateien to dalladen. To bestimmen, welke Dateien ji dalladen wüllt, könnt ji unsen Metadaten <a %(a_generate)s>genereren</a> oder unsen ElasticSearch- und MariaDB-Datenbanken <a %(a_download)s>dalladen</a>. Leider hebbt en poor Torrent-Kollekschonen .zip- oder .tar-Dateien an de Root, in so'n Fall mööt ji den ganzen Torrent dalladen, vördat ji enkelne Dateien utsöken könnt."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Wi hebbt <a %(a_ideas)s>een paar Ideen</a> för den lesten Fall.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Noch gifft dat keen einfach to bruuken Warktüüch för dat Filteren vun Torrents, man wi freet uns över Bidrääg."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Wo geiht ji mit Dubletten in de Torrents üm?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Wi versöken, so wenig Dubletten oder Överlappten as mööglich in de Torrents op disse List to hebben, aver dat is nich alltohoop to erreichen un hängt stark vun de Richtlinien vun de Ursprungsbibliotheken af. För Bibliotheken, de ehr egen Torrents rutgeven, hebbt wi keen Influss. För Torrents, de vun Anna’s Archiv rutgeven warrt, deduplizieren wi bloß op Basis vun MD5-Hash, dat bedüüt, dat verscheden Versionen vun dat sülvst Book nich dedupliziert warrt."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Kann ik de Torrent-List as JSON kriegen?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ja."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Ik seh keen PDFs oder EPUBs in de Torrents, bloß binäre Dateien? Wat schall ik doon?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Disse sünd in Wirklichkeit PDFs un EPUBs, se hebbt bloß in vele vun unsen Torrents keen Dateiendung. Dor sünd twee Steden, wo ji de Metadaten för Torrent-Dateien finnen könnt, inklusiv de Dateitypen/-endungen:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Jede Kollekschonen oder Veröffentlikung hett ehr egen Metadaten. As Bispill hebbt de <a %(a_libgen_nonfic)s>Libgen.rs-Torrents</a> en tohörige Metadaten-Datenbank, de op de Libgen.rs-Websied hostet warrt. Wi linken normaal to relevante Metadaten-Ressourcen vun jede Kollekschonen ehr <a %(a_datasets)s>Datensied</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Wi rekommenderen, unsen ElasticSearch- und MariaDB-Datenbanken to <a %(a_generate)s>genereren</a> oder to <a %(a_download)s>dalladen</a>. Disse hebbt en Zuordnung för jede Record in Anna’s Archiv to ehr tohörige Torrent-Dateien (wenn verfügbar), ünner „torrent_paths“ in de ElasticSearch-JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Woarum kann mien Torrent-Client nich eenige vun jüm Torrent-Dateien / Magnet-Lenken opmaken?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Eenige Torrent-Clients ünnerstütten keen grote Stücken-Grööt, wat veel vun uns Torrents hebben (bi ne'e Torrents doot wi dat nich mehr — ok wenn dat na Specs gellt!). Versöök een annern Client, wenn du dit Problem hest, oder beschwer di bi de Makers vun dien Torrent-Client."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Hebbt ji en Programm för verantwortungsvolle Offenlegung?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Wi wüllt Sekerheitsforscher willkommen heten, för Schwachstellen in unsen Systemen to söken. Wi sünd grote Anhänger vun verantwortungsvolle Offenlegung. Kontaktiert uns <a %(a_contact)s>hier</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Wi sünd de Tiet nich in de Lage, Bug-Bounties to vergeven, utsehn för Schwachstellen, de dat <a %(a_link)s>Potential hebbt, uns Anonymität to kompromittieren</a>, för de wi Bounties in de Range vun $10k-50k bieden. Wi wüllt in de Zukunft en breedere Umfang för Bug-Bounties bieden! Bitte beachten, dat Social-Engineering-Angriepen ut de Umfang sünd."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Wenn ji Interesse an offensiver Sekerheit hebbt un de Welt ehr Wissen un Kultur archiveren wüllt, kontaktiert uns. Dor sünd vele Mööglichkeiten, wo ji helpen könnt."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Hebbt ji mehr Ressourcen över Anna’s Archiv?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmääßige Updates"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — uns Open-Source-Code"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Übersetten op Anna’s Software</a> — uns Übersettensystem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — över de Daten"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative Domänen"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mehr över uns (bitte helft, disse Siet up to date to holden, oder maakt een för jümme Spraak!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Wo kann ik Urheberrechtsverletzungen melden?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Wi hosten hier keen urheberrechtlich schützt Material. Wi sünd en Suchmaschien, un indexeren blots Metadaten, de al öffentlich togänglig sünd. Bi't Herunterladen vun disse externen Quellen, föd wi an, de Gesetze in jümme Jurisdiktion to checken, wat erlaubt is. Wi sünd nich verantwörtlich för'n Inhalt, de vun annere hostet warrt."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Wenn ji Beschwerden över dat hebbt, wat ji hier seht, is de beste Weg, de Original-Websiet to kontaktieren. Wi övernehmen regelmääßig deren Ännerungen in uns Datenbank. Wenn ji wirklich meen, dat ji en gültige DMCA-Beschwerde hebbt, de wi beantwören schüllt, füllt bitte dat <a %(a_copyright)s>DMCA / Urheberrechts-Beschwerdeformular</a> ut. Wi nehmen jümme Beschwerden ernst un melden uns so gau as müöglich."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Ik kann nich lieden, wie ji dit Projekt leedt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Wi willen ok allens dran erinnern, dat all uns Code un Daten komplett Open Source sünd. Dit is uniek för Projekte as uns — wi weet keen anner Projekt mit en ähnlich massiven Katalog, de ok komplett Open Source is. Wi wörren allens begrüßen, de meent, wi leedt uns Projekt schlecht, uns Code un Daten to nehmen un en egen Schattenbibliothek up to setten! Wi seggt dit nich ut Trotz oder so — wi meen dat wirklich, dat dit großartig wörr, weil dat de Standard för allens höger maken wörr un de Erbe vun de Menschheit beter bewahren wörr."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Hest du een Uptime-Monitor?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Seih <a %(a_href)s>dit utmärkten Projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Wo kann ik Böker oder annere physische Materialien spenden?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Bitte send se an de <a %(a_archive)s>Internet Archive</a>. Se warrn korrekt bewahrt."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Wo is Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Du büst Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Wat sünd jümme Lieblingsböker?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Hier sünd een paar Böker, de en besünnere Bedüden för de Welt vun Schattenbibliotheken un digitale Bewahrung hebbt:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Ji hebbt hüt keen schnellen Downloads mehr."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Worrt Mitglied, üm schnellen Downloads to bruken."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Wi ünnerstütt nu Amazon-Geschenkkarten, Kredit- un Debitkarten, Krypto, Alipay un WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Ganzen Datenbank"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Böker, Papiere, Magazine, Comics, Bibliotheksdatensätze, Metadaten, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Söken"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub hett <a %(a_paused)s>pausiert</a> mit dat Uploaden vun nee Papiere."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB is en Fortsetzung vun Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direkten Toegang to %(count)s akademische Papiere"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Open"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Wenn Se en <a %(a_member)s>Mitglied</a> sünd, is keen Browser-Verifikation nödig."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Langtied-Archiv"

#, fuzzy
msgid "page.home.archive.body"
msgstr "De Datasets, de bi Anna’s Archiv bruukt warrt sünd, sünd ganz open un köönt in grooten Mengen över Torrents spegelt warrn. <a %(a_datasets)s>Mehr leren…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Se köönt enorm helpen, wenn Se Torrents seedt. <a %(a_torrents)s>Mehr leren…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s Seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s Seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s Seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM-Trainingsdaten"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Wi hebbt de gröttste Sammlung vun hochkvalitativ Textdaten op de Welt. <a %(a_llm)s>Mehr leren…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Spegels: Oproop an Friewillige"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Friewillige söcht"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "As en non-profit, open-source Projekt sünd wi alltid op Sook na Lüüd, de helpen wüllt."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Wenn Se en hoochriskanten anonymen Betahlverwerker bedrieven, neem Kontakt mit uns op. Wi söcht ok Lüüd, de kleen, smakvolle Annoncen setten wüllt. All Innahmen gahn an uns Vörsorgsarbeid."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Annas Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS-Downloads"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 All Download-Lenken för dit Datei: <a %(a_main)s>Datei-Hauptsiet</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(Se mööt evt. mehrmals mit IPFS versöken)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 För schnellere Downloads un üm de Browser-Checks to överspringen, <a %(a_membership)s>warrt en Mitglied</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 För datenspegeln in grooten Mengen, kiek mol op de <a %(a_datasets)s>Datasets</a> un <a %(a_torrents)s>Torrents</a>-Sieden."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-Daten"

#, fuzzy
msgid "page.llm.intro"
msgstr "Dat is good begrepen, dat LLMs op hoochwiesige Daten blöhen. Wi hebben de gröttste Samlung vun Böker, Papers, Tiedschriften, etc. op de Welt, de to de hoochwiesigsten Textquellen höört."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unieke Oomfang un Reeg"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Üs Samlung hett över hundert Millionen Dateien, inklusiv akademische Journals, Lehrböker un Tiedschriften. Wi erreich diesen Oomfang dör de Kombinatschoon vun grote bestahn Repositories."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Een deel vun uns Quelle-Samlungen sünd al in grooten Mengen togäng (Sci-Hub un Deele vun Libgen). Annere Quellen hebben wi sülvst befreit. <a %(a_datasets)s>Datasets</a> wiest en fullen Överblick."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Üs Samlung innihölt Millionen vun Böker, Papers un Tiedschriften vun vör de E-Book-Tied. Grote Deele vun dissen Samlung sünd al OCR’t, un hebben al wenig intern Oorloop."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Wo wi helpen köönt"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Wi köönt hoochschnelle Toegang to uns fullen Samlungen bieden, as ok to nich friegaven Samlungen."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Dit is Toegang op Enterprise-Niveau, de wi för Spenden in’t Oomfang vun Tüüsenden USD bieden köönt. Wi sünd ok bereit, dit to utwesseln för hoochwiesige Samlungen, de wi noch nich hebben."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Wi köönt di dat weddergeven, wenn du uns mit Verbetern vun uns Daten helpen kannst, so as:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Oorloop wegnahmen (Deduplikatschoon)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Text- un Metadaten-Extraction"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Ünnerstütt langfrüstige Archiverung vun Minschenwissen, un krieg betere Daten för dien Modell!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontakteer uns</a> to bespreken, wo wi tosamen arbeiden köönt."

#, fuzzy
msgid "page.login.continue"
msgstr "Wietermaken"

#, fuzzy
msgid "page.login.please"
msgstr "Bitte <a %(a_account)s>anmellen</a>, üm disse Sied to ankieken.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Archiv is temporär wedder in Reep. Kiek mol in een Stünn wedder rin."

#, fuzzy
msgid "page.metadata.header"
msgstr "Metadaten verbeetern"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Du kannst bi de Bewahrn von Böker helpen, indem du de Metadaten verbeeterst! Lest eerst de Achtergrund över Metadaten op Anna’s Archiv, un denn lehr, wie du Metadaten dörch Verlinken mit Open Library verbeetern kannst, un kriegst en freee Medlemschaft op Anna’s Archiv."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Achtergrund"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Wenn du en Book op Anna’s Archiv ankiekst, kannst du verscheden Felder sehn: Titel, Autor, Verlag, Upplage, Johr, Beschrieven, Dateinaam, un mehr. All disse Informatschonen nennt man <em>Metadaten</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Da wi Böker ut verscheden <em>Quellenbibliotheken</em> tosamendoon, wiest wi wat för Metadaten in de Quellebibliothek tofoon is. För’n Beipiel, för en Book, dat wi ut Library Genesis hebben, wiest wi den Titel ut de Library Genesis-Datenbank."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Manchmal is en Book in <em>mehrere</em> Quellenbibliotheken tofoon, de verscheden Metadatenfelder hebben. In so’n Fall wiest wi einfach de längste Version vun jeden Feld, denn de enthält hoffentlich de meest nützliche Informatschonen! Wi wiest de annern Felder noch ünner de Beschrieven, z.B. as „alternativen Titel“ (aber nur, wenn se verscheden sünd)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Wi trekken ok <em>Kodes</em> as Identifikatoren un Klassifikatoren ut de Quellebibliothek. <em>Identifikatoren</em> representeren en bestimmte Upplage vun en Book; Beipiele sünd ISBN, DOI, Open Library ID, Google Books ID, oder Amazon ID. <em>Klassifikatoren</em> gruppieren mehrere ähnliche Böker tosam; Beipiele sünd Dewey Decimal (DCC), UDC, LCC, RVK, oder GOST. Manchmal sünd disse Kodes explizit in de Quellebibliotheken verlinkt, un manchmal kunnen wi se ut den Dateinaam oder de Beschrieven trekken (primär ISBN un DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Wi kunnen Identifikatoren bruken, um Records in <em>Metadaten-only Sammlungen</em> to finnen, as OpenLibrary, ISBNdb, oder WorldCat/OCLC. In uns Suchmaschien gifft dat en speziellen <em>Metadaten-Tab</em>, wenn du disse Sammlungen dörchbladern wullt. Wi bruken passende Records, um fehlende Metadatenfelder to füllen (z.B. wenn en Titel fehlt), oder z.B. as „alternativen Titel“ (wenn dat en bestahn Titel gifft)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Um genau to sehn, woher de Metadaten vun en Book kamen, kiek in den <em>„Technische Details“-Tab</em> op en Book-Sied. Dor is en Link to’n raw JSON för dat Book, mit Hinwiesen to’n raw JSON vun de originalen Records."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "För mehr Informatschonen, kiek op disse Sieden: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Suche (Metadaten-Tab)</a>, <a %(a_codes)s>Kodes Explorer</a>, un <a %(a_example)s>Beispiel Metadaten JSON</a>. Tohopen, all uns Metadaten kunnen <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>herunterladen</a> as ElasticSearch un MariaDB Datenbanken warrn."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library Verlinken"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Wenn du en Datei mit schlechten Metadaten finnst, wat schallst du doon? Du kannst to de Quellebibliothek gahn un ehr Prozeduren för Metadatenverbeeterung volgen, aber wat to doon, wenn en Datei in mehrere Quellebibliotheken tofoon is?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Dor is een Identifikator, de op Anna’s Archiv special behandelt warrt. <strong>Dat annas_archive md5 Feld op Open Library överschreibt all annere Metadaten!</strong> Lest eerst en beten Achtergrund över Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library wurr 2006 vun Aaron Swartz grünnt mit dat Ziel vun „een Websied för jedes Book, dat jemals utgeven wurr“. Dat is so’n beten as Wikipedia för Book-Metadaten: jedermann kann dat redigeren, dat is freee lizensiert, un kann in grooten Mengen herunterladen warrn. Dat is en Book-Datenbank, de am meesten mit uns Misschoon übereenstimmt — in Fakt, Anna’s Archiv is vun Aaron Swartz’ Vision un Leven inspirert."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Statt dat Rad neig to erfinden, hebben wi besluten, uns Friewillige to Open Library to leiten. Wenn du en Book sehn, dat verkehrte Metadaten hett, kannst du in de folgende Wiese helpen:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Gah to de <a %(a_openlib)s>Open Library Websied</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Finn den richtigen Book-Record. <strong>WARNUNG:</strong> sei seker, dat du de richtige <strong>Upplage</strong> utwählst. In Open Library gifft dat „Werke“ un „Upplagen“."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Een „Werk“ könnt „Harry Potter und der Stein der Weisen“ wesen."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Een „Upplage“ könnt wesen:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "De 1997 eerste Utgaav, utgeven vun Bloomsbery mit 256 Sieden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "De 2003 Paperback-Utgaav, utgeven vun Raincoast Books mit 223 Sieden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "De 2000 polske Översettung „Harry Potter I Kamie Filozoficzn“ vun Media Rodzina mit 328 Sieden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "All disse Utgaaven hebbt verscheden ISBNs un verscheden Inholt, also wees seker, dat du de richtige utwählst!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Bearbeid de Opnahme (oder leg een nee an, wenn keen dor is), un füüg so veel nützliche Informatschonen as mööglich to! Du büst nu hier, also maak de Opnahme richtig tof."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Unner „ID-Nummern“ wähl „Anna’s Archive“ un füüg de MD5 vun’t Book ut Anna’s Archive to. Dit is de lange Reeg vun Bookstaven un Nummern na „/md5/“ in de URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Versöök annere Dateien in Anna’s Archive to finnen, de ok to disse Opnahme passen, un füüg disse ok to. In’t Futuur köönt wi disse as Dubletten op de Anna’s Archive-Söökpage gruppieren."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Wenn du fardig büst, schriebs de URL op, de du gerade aktualisiert hest. Wenn du minnstens 30 Opnahmen mit Anna’s Archive MD5s aktualisiert hest, schick uns en <a %(a_contact)s>E-Mail</a> un schick uns de List. Wi geven di een gratis Medlemschaft för Anna’s Archive, so dat du dit Arbeid lechter maken kannst (un as Dank för dien Hülp). Disse mööt hoochwertige Bearbeidungen wesen, de substanzielle Informatschonen tofüügen, anners ward dien Anfraag aflehnt. Dien Anfraag ward ok aflehnt, wenn een vun de Bearbeidungen vun Open Library Moderatoren trüchsetzt oder korrigeert ward."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Merk, dat dit alleen för Böker gellt, nich för akademische Papers oder annere Typen vun Dateien. För annere Typen vun Dateien empfahlen wi noch immer, de Ursprungsbibliothek to finnen. Dat kann een poor Weeken dauen, bit de Ännerungen in Anna’s Archive inbunnen sünd, denn wi mööt de neeste Open Library Datendump runterladen un uns Söökindex neegenereeren."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spiegels: Oproop för Volunteers"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "To de Verlässlichkeit vun Anna’s Archiv to erhöhen, söken wi Volunteers, de Spiegels bedrieven."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Wi söcht dit:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Du löppt de Anna’s Archiv Open-Source-Codebasis un aktualiseerst regelmääßig bäd de Code un de Daten."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Dien Version is klor as Spiegel kennmakt, z.B. „Bob’s Archiv, en Anna’s Archiv Spiegel“."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Du büst bereit, de Risiken, de mit dissen Arbeid verknüppt sünd, up di to nehmen, de sünd betüchlich. Du hest en diepe Verstahn vun de operationalen Sekerheit, de nödig is. De Inholt vun <a %(a_shadow)s>disse</a> <a %(a_pirate)s>Posts</a> sünd di sülvstverstäändlich."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Du büst bereit, to uns <a %(a_codebase)s>Codebase</a> bi to dragen — in Sammenarbeit mit uns Team — üm dat to realisieren."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Toerst wüllt wi di keen Toegang to uns Partner-Server-Downloads geven, man wenn dat goot geiht, köönt wi dat mit di delen."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hosting-Kosten"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Wi sünd bereit, de Hosting- un VPN-Kosten to decken, toerst bis to $200 pro Maand. Dit is gnoog för en basis Söökenserver un en DMCA-beschützten Proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Wi wüllt blots för Hosting betahlen, wenn du allens upsett hest un wiest hest, dat du in Stann büst, de Archiv mit Updates up to date to holden. Dit bedüüt, dat du de eersten 1-2 Maanden ut egenen Sack betahlen musst."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Dien Tied wüllt nich betahlt (un uns ook nich), da dit reine Friewilligenarbeid is."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Wenn du di betüchlich in de Utwikkeln un de Operatschoon vun uns Arbeid engaschierst, köönt wi över dat delen vun mehr Spendeninkamen mit di snacken, för di to deployen as nödig."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Anfangen"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Bitte <strong>kontaktiere uns nich</strong> üm Erlaubnis to frägen oder för basis Froogen. Taten snacken lütter as Woort! All de Informationen sünd dorbuiten, also fang einfach an mit dat Upstellen vun dien Spiegel."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Föhl di free, Tickets oder Merge Requests to uns Gitlab to posten, wenn du op Problemen stöst. Wi köönt mit di spegel-spezifische Features utarbeiden, as Rebranding vun „Anna’s Archive“ to dien Website-Naam, (toerst) deaktiveren vun Brukerkonten, oder Verlinken to uns Hauptsied vun Book-Sieden."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Wenn du dien Spiegel in’t Loop hest, neem gern Kontakt mit uns op. Wi würd gern dien OpSec överkiek’n, un wenn dat in Ornung is, linkt wi to dien Spiegel un arbeit’n näger mit di tosamen."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Vörut Dank an all, de op disse Wies bidragen wüllt! Dat is nix för Swakkopps, aver dat würd de Bestännig vun de grötste echt free Bibliothek in de Minschheitshistorie fastmaken."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Herunterladen vun Partner-Websiet"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Langsame Downloads sünd alleen över de offizielle Websiet to kriegen. Besöök %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Langsame Downloads sünd nich över Cloudflare VPNs oder anners von Cloudflare IP-Adressen to kriegen."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Wachten Sie bitte <span %(span_countdown)s>%(wait_seconds)s</span> Sekonden, üm dit Dokument to downloaden."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Bruk de folgende URL för’t Herunterladen: <a %(a_download)s>Nu downloaden</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Dank för’t Warten, dat maakt de Websiet för all gratis togänglig! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Wohrschau: vun dien IP-Adress sünd in de letzten 24 Stünnen veel Downloads maakt worrn. Downloads köönt langsaamer as normaal wesen."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads vun dien IP-Adress in de letzten 24 Stünnen: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Wenn du en VPN, en gedeelten Internetanslusst oder dien ISP IPs deelt, kann disse Wohrschau dorch dat kommen."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Üm all Lüü de Mööglichkeit to geven, Dateien gratis to downloaden, mööt ji wachten, bevör ji dit Dokument downloaden köönt."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Du kannst Anna’s Archiv in en annern Tab ankieken, während du wartest (wenn dien Browser dat Refreshen vun Hintergrund-Tabs ünnerstütt)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Du kannst ok mehrer Download-Sieden gliek laden laten (aber bitte nur een Datei gliek per Server downloaden)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Wenn du een Download-Link kriegst, is he för mehrer Stünnen gültig."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Anna’s Archiv"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Opnahm in Annas Archiv"

#, fuzzy
msgid "page.scidb.download"
msgstr "Herunterladen"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "För de Tohgänglichkeit un langfrüstige Bewahren vun Minschenwissen to ünnerstütten, warr <a %(a_donate)s>Mitglied</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "As Bonus, 🧬&nbsp;SciDB laadt för Mitglieder sneller, ahn een Grenzen."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Gaat dat nich? Versöök <a %(a_refresh)s>to verfrischen</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Noch keen Vörschau togänglich. Datei vun <a %(a_path)s>Annas Archiv</a> herunterladen."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB is en Fortsätt vun Sci-Hub, mit de bekannte Oberfläche un direkter PDF-Ansicht. Giff dien DOI in för to ankieken."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Wi hebbt de ganze Sci-Hub-Sammlung, as ok nee Papers. De meisten köönt direkt mit en bekannte Oberfläche ankeken warrn, ähnlich as bi Sci-Hub. Eenige köönt över externe Quellen herunterlaadt warrn, in so'n Fall wiest wi Links dorhen."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Söken"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nee Söken"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Nur innehmen"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Utsluten"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nich nich kontrolliert"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Herunterladen"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Tiedschriftenartikels"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digitale Leihgabe"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadaten"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titel, Autor, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Söken"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Söökinstellen"

#, fuzzy
msgid "page.search.submit"
msgstr "Söken"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "De Söök hett to lang daurt, wat bi wiet söken normaal is. De Filteranzahl kunn nich genau wesen."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "De Söök hett to lang daurt, wat bedüden kann, dat de Resultaten nich genau sünd. Manchmal hülpt dat <a %(a_reload)s>Siet neuladen</a>."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Anwiesen"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "List"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Advanciert"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Söken in Beschrieven un Metadaten-Kommentaren"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Spezifisch Söökfeld tofögen"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(spezifisch Söökfeld)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Veröffentlichungsjohr"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Inhalt"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Dateityp"

#, fuzzy
msgid "page.search.more"
msgstr "mehr…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Toegang"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Quelle"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scraped un open-sourced vun AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Spraak"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Sortieren na"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Am relevantsten"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Neuest"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(Veröffentlichungsjohr)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Ooldest"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Gröttst"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(Dateigrött)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Lüttst"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open source)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Tüüg"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "De Sööksindex warrt maandelang aktualiseert. He hett nu Ennern bis %(last_data_refresh_date)s. För mehr technische Informatschonen, kiek op de %(link_open_tag)sDatasets-Siet</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "To de Söökindex mit Koden to dorchsöken, brük de <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Schriev in dat Feld, üm in uns Katalog vun %(count)s direkt to downloade Dateien to söken, de wi <a %(a_preserve)s>för immer bewaren</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "In Fakt, jedereen kann helpen, disse Dateien to bewaren, indem he uns <a %(a_torrents)s>eenheitliche List vun Torrents</a> seedet."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Wi hebben de weltwied grötste offene Katalog vun Böker, Papers un annere schrievne Warken. Wi spegeln Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>un mehr</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Wenn du annere „Schattbibliotheken“ finnst, de wi spegeln sullen, oder wenn du Froogen hest, neem Kontakt op mit uns bi %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "För DMCA / Copyright-Ansprüch <a %(a_copyright)s>hier klicken</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: Bruk de Tasten-Kombinatschonen „/“ (Söökfokus), „Enter“ (Söök), „j“ (op), „k“ (af), „<“ (vörige Siet), „>“ (nächste Siet) för schnellere Navigation."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Söökst du Papers?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Schriev in dat Feld, üm in uns Katalog vun %(count)s akademische Papers un Journal-Artikels to söken, de wi <a %(a_preserve)s>för immer bewaren</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Schriev in dat Feld, üm Dateien in digitale Leihbibliotheken to söken."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Disse Sööksindex hett nu Metadaten vun de Internet Archive’s Controlled Digital Lending Bibliothek. <a %(a_datasets)s>Mehr över uns Datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "För mehr digitale Leihbibliotheken, kiek op <a %(a_wikipedia)s>Wikipedia</a> un de <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Schriev in dat Feld, üm Metadaten vun Bibliotheken to söken. Dit kann nützlich wesen, wenn du <a %(a_request)s>een Datei anfrochst</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Disse Sööksindex hett nu Metadaten vun verschedenen Metadaten-Quellen. <a %(a_datasets)s>Mehr över uns Datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "För Metadaten wiesen wi de originale Records. Wi doon keen Records tosamendoon."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Dat gifft vele, vele Quellen för Metadaten vun schrievne Warken op de Welt. <a %(a_wikipedia)s>Disse Wikipedia-Siet</a> is en gooden Anfang, aver wenn du annere goede Listen kennst, laat uns dat weten."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Schriev in dat Feld, üm to söken."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Dit sünd Metadaten-Records, <span %(classname)s>keen</span> Dateien för’n Download."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Fehler bi’t Söken."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Versöök <a %(a_reload)s>de Siet ne’e to laden</a>. Wenn dat Problem blifft, mail uns bitte ünner %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Keen Dateien funnen.</span> Versöök minder oder anners Söökbegriepen un Filter."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Manchmal passiert dat verkehrt, wenn de Suchserver langsam is. In so’n Fall kann <a %(a_attrs)s>Neuladen</a> helpen."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Wi hebbt Treffer funnen in: %(in)s. Du kannst de URL dor funnen, wenn du <a %(a_request)s>en Datei anfrochst</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Tiedschriftenartikels (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digitale Leihgaben (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadaten (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultaten %(from)s-%(to)s (%(total)s totaal)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ deelwies Treffer"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d deelwies Treffer"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Ehrenamt & Prämien"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archiv is op Ehrenamtliche wie di angewiesen. Wi heet all Commitment-Niveaus willkommen un hebbt twee Hööftkategorien vun Hülp, de wi söcht:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Licht ehrenamtliche Arbeit:</span> wenn du blots een poor Stünnen hier un dor hest, gifft dat noch veel Mööglichkeiten, wo du hülpen kannst. Wi belönen regelmääßige Ehrenamtliche mit <span %(bold)s>🤝 Mitgliedschaften bi Anna’s Archiv</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Swor Volunteerarbeit (USD$50-USD$5,000 Bounties):</span> wenn ji veel Tied un/oder Ressourcen för uns Misschoon opbrengen könnt, wüllt wi giern nahr bi ji warken. Mit de Tied könnt ji in't Innerteam kommen. Ok wenn wi en knapp Budget hebben, köönt wi för de sworste Arbeit <span %(bold)s>💰 Geldbounties</span> utgeven."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Wenn ji keen Tied för Volunteerarbeit hebben, köönt ji uns ok veel helpen dör <a %(a_donate)s>Geld to spenden</a>, <a %(a_torrents)s>uns Torrents to seeden</a>, <a %(a_uploading)s>Böker hoochladen</a>, oder <a %(a_help)s>jiere Frünnen vun Anna’s Archive to vertellen</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Fimen:</span> wi bieden hoochschnellen direkten Toogang to uns Kollekschonen in'n Utwessel för Spenden op Fimenlevel oder för nee Kollekschonen (z.B. nee Scans, OCR’ed Datasets, uns Daten to verbiessern). <a %(a_contact)s>Kontakteert uns</a> wenn dat op ji zutrifft. Seht ok uns <a %(a_llm)s>LLM-Sied</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Lichte Volunteerarbeit"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Wenn ji een poor Stünnen över hebben, köönt ji op vele Weegen helpen. Treedt bi in den <a %(a_telegram)s>Volunteers-Chat op Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "As Dankeschön geven wi normaal 6 Maanden „Lücklichen Bibliotheker“ för basiske Milestones, un mehr för wedderholende Volunteerarbeit. All Milestones bruken hoochwertige Arbeit — schlampige Arbeit schadet uns mehr as dat helpt un wi wiest dat af. Bitte <a %(a_contact)s>mailt uns</a> wenn ji een Milestone erreicht hebben."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Opgave"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Meilensteen"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Dat Wurt vun Annas Archiv verbreiden. För Beispill, dördat Se Böker op AA vörslahn, op uns Blogposts linken, oder allgemen Lüüd op uns Websteed wiesen."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s Links oder Screenshots."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Disse schüllt wiesen, dat Se eenen vun Annas Archiv vertellen, un dat de anner Se bedanket."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Verbiessert Metadata dör <a %(a_metadata)s>Verlinken</a> mit Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Du kannst de <a %(a_list)s >List vun toföögte Metadata-Problemen</a> as Startpunkt bruken."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Pass op, dat du en Kommentaar bi de Probleme, de du lööst, achterlaat, so dat annere dien Wark nich dubbel maken."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s Links vun Records, de du verbeetert hest."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Übersetten</a> vun de Websied."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Een Spraak komplett översetten (wenn se nich al fast fertig weer)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "De Wikipedia-Sied för Anna’s Archive in jiere Spraak verbiessern. Informationen vun AA’s Wikipedia-Sied in annere Spraaken, un vun uns Websied un Blog inbinden. Referenzen to AA op annere relevante Sieden tofögen."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link to de Bearbeitungs-Historie, de wiest dat ji significant bidragen hebben."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Böker (oder Papers, etc.) Anfragen op de Z-Library oder de Library Genesis Foren erfüllen. Wi hebben keen eegen Böker-Anfragensystem, aver wi spegeln disse Bibliotheken, also maakt dat se beter maakt Anna’s Archive ok beter."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s Links oder Screenshots vun Anfragen, de du erfüllt hest."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Lütte Opgaven, de op uns <a %(a_telegram)s>Volunteers-Chat op Telegram</a> postet sünd. Normal för Membership, manchmal för lütte Bounties."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Lütte Opgaven in uns Friewilligen-Chat-Gruppe postet."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dat hängt vun de Opgave af."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Kopgeld"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Wi söcht alltid Lüüd mit gode Programmier- oder offensiven Sekerheitsskills, de mitmaken wüllt. Du kannst en groten Deel doran hebben, de Menschheit ehr Erven to bewaren."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "As Dank geven wi Medlemsschaften för gode Bidrääg. As en groten Dank geven wi ok Geldpriesen för besünners wichtige un swiere Opgaven. Dat schull nich as en Job-Ersatz sehn warrn, man as en extra Anreiz un kann bi de opkommenden Kosten helpen."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "De meiste vun unsen Code is Open Source, un wi wüllt dat ok vun dien Code, wenn wi den Kopgeld utbetalen. Dor gifft dat een paar Utnahmen, de wi individuell bespreken köönt."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Kopgeld warrt den eersten Minschen geven, de en Opgave afmaakt. Du kannst gern en Kommentaar bi en Kopgeld-Ticket schrieven, dat annere weet, dat du doran arbeitst, so dat se afwarten oder di kontaktieren köönt, üm tosam to arbeiden. Man wees dorbi, dat annere ok frie sünd, doran to arbeiden un versöken, di to övertreffen. Wi geven aver keen Kopgeld för schludrige Arbeit. Wenn twee hoogkvaliteetige Inschicken dicht bianner liggt (binnen en Dag oder twee), köönt wi besluten, Kopgeld an beid to geven, na unsen Ermessen, as Bispill 100%% för de eerste Inschick un 50%% för de twete Inschick (so 150%% in totaal)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "För de gröteren Kopgeld (besünners för Scraping-Kopgeld), kontakt uns, wenn du ~5%% davon afmaakt hest un du seker büst, dat dien Methode för de ganze Opgave taugt. Du musst uns dien Methode vertellen, so dat wi Feedback geven köönt. Ok so köönt wi besluten, wat wi maken, wenn meerdere Lüüd dicht bi en Kopgeld sünd, as Bispill dat wi dat an meerdere Lüüd geven, Lüüd tosam to arbeiden anregen, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "WARNUNG: de hoogen Kopgeld-Opgaven sünd <span %(bold)s>swier</span> — dat könnt klauk ween, mit de lichteren to beginnen."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Gah to unsen <a %(a_gitlab)s>Gitlab-Issue-Liste</a> un sorteer na „Label priority“. Dat wiest ungefäähr de Reegfolge vun Opgaven, de uns wichtig sünd. Opgaven ohne explizit Kopgeld sünd ok för Medlemsschaften tolässig, besünners de markeert as „Accepted“ un „Anna’s favorite“. Du könntst mit en „Starter project“ beginnen."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates över <a %(wikipedia_annas_archive)s>Annas Archiv</a>, de gröttste wirklich offene Bibliothek in de Minschheitshistorie."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna’s Archiv"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "De gröttste open-source open-data Bibliothek vun de Welt. Spiegelt Sci-Hub, Library Genesis, Z-Library un mehr."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Söök Anna’s Archiv"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’s Archiv bruukt dien Hülp!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Veel versöken uns runner to kriegen, aver wi wehren uns."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Wenn du nu spendst, kriegst du <strong>doppelt</strong> so veel schnelle Downloads."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Gültig bit to’n Enn vun dissen Maand."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Spenden"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Menschlich Wissen retten: en groot Gave för de Feiertieden!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Överrasch eenen, den du leev hest, un geef em en Konto mit en Mitgliedschaft."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "To de Resilienz vun Anna’s Archiv to erhöhen, söken wi Friewillige, de Spiegels runnen."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Dat perfekte Geschenk för Valentinsdag!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Wi hebbt en nee Spendenmethode: %(method_name)s. Överweeg %(donate_link_open_tag)sto spenden</a> — dat is nich billigen, disse Websteed to bedrieven, un jümme Spende maakt en grooten Ünnerscheed. Hartlichen Dank."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Wi maken en Spendenaktion för <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">dat grötste Comics-Archiv</a> op de Welt to sichern. Dank för jümme Ünnerstüttung! <a href=\"/donate\">Spenden.</a> Wenn ji nich spenden könnt, överweeg uns to ünnerstütten, indem ji jümme Frünnen vertellt, un uns op <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a> volgt."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Laatst herunnerladen:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Söken"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Metadaten verbedern"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Ehrenamt & Belohnungen"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktivität"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Codes Explorer"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM-Daten"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Start"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Übersetten ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Anmellen / Registeren"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Account"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archiv"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Bleev in Tuch"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / Urheberrechtsansprüche"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Vörten"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sekerheit"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativen"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nich tohörig"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Dit Datei könnt Probleemen hebben."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Snaak Herunterlaaden"

#, fuzzy
msgid "page.donate.copy"
msgstr "koperen"

#, fuzzy
msgid "page.donate.copied"
msgstr "kopert!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Vörige"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Nächste"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "bloot dissen Maand!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub hett dat <a %(a_closed)s>Hochladen</a> vun nee Papers pauziert."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Wählt en Betahlmööglichkeit ut. Wi geven Rabatt för Krypto-Betahlungen %(bitcoin_icon)s, weil wi (veel) minder Gebühren hebbt."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Wählt en Betahlmööglichkeit ut. Wi hebbt nu blots Krypto-Betahlungen %(bitcoin_icon)s, weil traditionell Betahlverarbeiders nich mit uns warken wüllt."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Wi köönt Kredit-/Debitkaarten nich direkt ünnerstütten, weil Banken nich mit uns warken wüllt. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Doch gifft dat verschiddene Mööglichkeiten, Kredit-/Debitkaarten to bruken, mit unsen annere Betahlmethoden:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Langsame & externe Downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Wenn ji för de eerste Tied Krypto bruken, föhrslahn wi %(option1)s, %(option2)s oder %(option3)s to bruken, um Bitcoin (de oorsprüngliche un meest bruukte Kryptowährung) to köpen un to spenden."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 Links vun Records, de ji verbiessert hebben."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 Links oder Screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 Links oder Screenshots vun erfüllte Anfragen."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Wenn du Interesse hest, disse Datasets för <a %(a_faq)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Training</a>-Zwecken to spiegeln, neem Kontakt mit uns op."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Wenn Sie daran interessiert sind, dieses Dataset für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainings</a>-zwecke zu spiegeln, kontaktieren Sie uns bitte."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Hauptwebsite"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN-Landinformationen"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Wenn Se interessiert sünd an dat Spiegeln vun disse Datenset för <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Training</a>-Zwecken, neem Kontakt mit uns op."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "De International ISBN Agency gifft regelmääßig de Bereiche rut, de se an nationale ISBN-Agenturen verteilt hett. Dorut köönt wi afleiden, to welk Land, Region oder Spraakgruppe disse ISBN höört. Wi bruken disse Daten aktuell indirekt, över de <a %(a_isbnlib)s>isbnlib</a> Python-Bibliothek."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ressourcen"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Lest aktualisiert: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN-Websied"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadaten"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "“scimag” utnehmen"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Uns Inspiration för dat Sammlen vun Metadaten is Aaron Swartz sien Ziel vun “een Web-Sied för jümmer Book, dat je publizeert weer”, för dat he <a %(a_openlib)s>Open Library</a> maakt hett."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dat Projekt hett sik good maakt, aver uns eenzigarteg Position maakt uns dat mööglich, Metadaten to kriegen, de se nich köönt."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Een annere Inspiration weer uns Wüünschen to weten <a %(a_blog)s>wieviel Böker in de Welt gifft</a>, so dat wi utrechnen köönt, wieveel Böker wi noch retten mööt."

#~ msgid "page.partner_download.text1"
#~ msgstr "Üm allens de Mööglichkeit to geven, Dateien gratis to downloaden, mutt du <strong>%(wait_seconds)s Sekunnen </strong> wachten, vördat du disse Datei downloaden kannst."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Siet automaatsch neuladen. Wenn du de Download-Tiedfenster verpasst, start de Timer vun vörne, also is automaatsch Neuladen to föddern."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Nu downloaden"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvertieren: Bruk Online-Tools för Formate to konvertieren. För Bispill, för epub to pdf to konvertieren, bruk <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: Daunlood de Datei (pdf oder epub sünd ünnerstütt), denn <a %(a_kindle)s>send se to Kindle</a> över Web, App oder E-Mail. Nützliche Tools: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Stüüt Autoren: Wenn du dit magst un dat dir leisten kannst, överleg de Original to köpen oder de Autoren direkt to ünnerstütten."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Stüüt Bibliotheken: Wenn dit in dien lokale Bibliothek to kriegen is, överleg dat dort för umme to leihen."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nich direkt in Masse togänglich, bloots in halbe Masse achter en Paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna sien Archiv förwaltet en Samlung vun <a %(isbndb)s>ISBNdb Metadaten</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb is en Firma, de verscheiden Online-Bokladen dursöcht, üm ISBN-Metadaten to finnen. Anna’s Archiv maakt Backups vun de ISBNdb-Bok-Metadaten. Disse Metadaten sünd över Anna’s Archiv togänglich (allerdings nich direkt in de Sööken, utse wenn Se explizit na en ISBN-Nummer söökt)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "För technische Details, kiek ünnen. To en Tiedpunkt köönt wi dat bruken, üm to bestimmen, welke Böker noch in Schattbibliotheken fehlt, üm to prioritiseeren, welke Böker wi finnen und/oder inscannen mööt."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Ünnen Blogpost över disse Daten"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb-Dursöken"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Aktuell hebbt wi en eenzigen Torrent, dat en 4,4GB gzipped <a %(a_jsonl)s>JSON Lines</a>-Datei (20GB unzipped) enthält: „isbndb_2022_09.jsonl.gz“. Üm en „.jsonl“-Datei in PostgreSQL to importeren, köönt Se wat as <a %(a_script)s>dit Skript</a> bruken. Se köönt dat ok direkt pipen, wat %(example_code)s so dat dat in’t Fliegen dekomprimiert."

#~ msgid "page.donate.wait"
#~ msgstr "Wartet mindestens <span %(span_hours)s>twee Stünnen</span> (un lad dis Sied nochmaal) vör dat ji uns kontaktiert."

#~ msgid "page.codes.search_archive"
#~ msgstr "Anna’s Archive na “%(term)s” dörsöken"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Spenden mit Alipay oder WeChat. Ji köönt op de nächsten Siet dor twischen wählen."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Anna’s Archive op Social Media un Online-Foren bekannter maken, dör Böker oder Listens op AA to empfehlen, oder Froogen to beantworen."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiction-Sammlung hett sik veränderd, aver hett noch <a %(libgenli)s>Torrents</a>, obschon nich updatet siet 2022 (wi hebbt direktem Downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna’s Archiv un Libgen.li managen tosam Sammlungen vun <a %(comics)s>Comic-Böker</a> un <a %(magazines)s>Tiedschriften</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Kene Torrents för russische Fiction un Standard-Dokumentensammlungen."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Der sünd keen Torrents för dat to’nanner Inholt to kriegen. De Torrents op de Libgen.li-Websteed sünd Speegels vun annere Torrents, de hier oplist sünd. De eenzige Utnahm sünd Fiction-Torrents, de bi %(fiction_starting_point)s anfungen. De Comics- un Magazinen-Torrents wurrn as en Tosamenarbeit twischen Anna’s Archiv un Libgen.li rutgeven."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Ut en Samlung <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> genau Herkunft nich bekannt. Deelwies ut the-eye.eu, deelwies ut annere Quellen."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

