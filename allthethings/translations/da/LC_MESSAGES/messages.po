msgid "layout.index.invalid_request"
msgstr "Ugyldig anmodning. Besøg %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " og "

msgid "layout.index.header.tagline_and_more"
msgstr "og mere"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;<PERSON>i spejler %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Vi samler og benytter  %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Al vores kode og data er fuldstændig open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Det største åbne bibliotek nogensinde i menneskets historie"

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;bøger, %(paper_count)s&nbsp;artikler — bevaret for evigt."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Verdens største open-source open-data bibliotek. ⭐️&nbsp;Spejler Sci-Hub, Library Genesis, Z-Library og mere. 📈&nbsp;%(book_any)s bøger, %(journal_article)s artikler, %(book_comic)s tegneserier, %(magazine)s magasiner — bevaret for evigt."

msgid "layout.index.header.tagline_short"
msgstr "📚 Verdens største open-source og open-data bibliotek.<br>⭐️ Mirrors Scihub, Libgen, Zlib og mere."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Forkert metadata (f.eks. titel, beskrivelse, forsidebillede)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemer med download(f.eks. kan ikke forbinde, fejlmeddelelse, meget langsom)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Filen kan ikke åbnes (f.eks. ødelagt fil, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Dårlig kvalitet (f.eks. formateringsproblemer, dårlig scanningskvalitet eller manglende sider)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fil skal fjernes (f.eks. reklame eller stødende indhold)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Ophavsretskrav"

msgid "common.md5_report_type_mapping.other"
msgstr "Andet"

msgid "common.membership.tier_name.bonus"
msgstr "Ekstra downloads"

msgid "common.membership.tier_name.2"
msgstr "Smart Bogorm"

msgid "common.membership.tier_name.3"
msgstr "Heldige bibliotekar"

msgid "common.membership.tier_name.4"
msgstr "Strålende Samler"

msgid "common.membership.tier_name.5"
msgstr "Fantastisk Arkivar"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s i alt"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) i alt"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "ubetalt"

msgid "common.donation.order_processing_status_labels.1"
msgstr "betalt"

msgid "common.donation.order_processing_status_labels.2"
msgstr "annulleret"

msgid "common.donation.order_processing_status_labels.3"
msgstr "udløbet"

msgid "common.donation.order_processing_status_labels.4"
msgstr "venter på, at Anna bekræfter"

msgid "common.donation.order_processing_status_labels.5"
msgstr "ugyldig"

msgid "page.donate.title"
msgstr "Donér"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Du har en <a %(a_donation)s>eksisterende donation</a> i gang. Afslut eller annuller venligst denne donation, før du laver en ny donation."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Se alle mine donationer</a>"

msgid "page.donate.header.text1"
msgstr "Annas Arkiv er et non-profit, open-source og open-data projekt. Ved at donere og blive medlem, støtter du vores drift og udvikling. Til alle vores medlemmer: tak for at holde os i gang! ❤️"

msgid "page.donate.header.text2"
msgstr "For mere information, se <a %(a_donate)s>Donations FAQ</a>."

msgid "page.donate.refer.text1"
msgstr "For at få endnu flere downloads, <a %(a_refer)s>henvis dine venner</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Du får %(percentage)s%% procent hurtigere downloads, fordi du blev henvist af bruger %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Dette gælder for hele medlemskabsperioden."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s hurtige downloads per dag"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "hvis du donerer denne måned!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / måned"

msgid "page.donate.buttons.join"
msgstr "Deltag"

msgid "page.donate.buttons.selected"
msgstr "Valgt"

msgid "page.donate.buttons.up_to_discounts"
msgstr "op til %(percentage)s%% rabat"

msgid "page.donate.perks.scidb"
msgstr "SciDB artikler <strong>ubegrænset</strong> uden verifikation"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> adgang"

msgid "page.donate.perks.refer"
msgstr "Tjen <strong>%(percentage)s%% hurtigere downloads</strong> ved at <a %(a_refer)s>henvise venner</a>."

msgid "page.donate.perks.credits"
msgstr "Dit brugernavn eller anonym omtale i krediteringerne"

msgid "page.donate.perks.previous_plus"
msgstr "Tidligere fordele, plus:"

msgid "page.donate.perks.early_access"
msgstr "Tidlig adgang til nye funktioner"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Eksklusiv Telegram med opdateringer bag kulisserne"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adoptér en torrent”: dit brugernavn eller besked i en torrent-filnavn <div %(div_months)s>én gang hver 12. måned af medlemskab</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legendarisk status i bevarelsen af menneskehedens viden og kultur"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Ekspertadgang"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontakt os"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Vi er et lille hold af frivillige. Det kan tage os 1-2 uger at svare."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Ubegrænset</strong> højhastighedsadgang"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direkte <strong>SFTP</strong> servere"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donation på virksomhedsniveau eller udveksling for nye samlinger (f.eks. nye scanninger, OCR’ede datasæt)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Vi byder store donationer fra velhavende individer eller institutioner velkommen. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "For donationer over $5000 bedes du kontakte os direkte på %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Vær opmærksom på, at selvom medlemskaberne på denne side er \"pr. måned\", er de engangsdonationer (ikke tilbagevendende). Se <a %(faq)s>Donations-FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Hvis du ønsker at donere (ethvert beløb) uden medlemskab, er du velkommen til at bruge denne Monero (XMR) adresse: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Vælg venligst en betalingsmetode."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(midlertidigt utilgængelig)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s gavekort"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkort (ved brug af app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Krypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit-/debitkort"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (almindelig)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kort / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit/debit/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkort"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit-/debitkort (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit-/debitkort 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Med krypto kan du donere ved hjælp af BTC, ETH, XMR og SOL. Brug denne mulighed, hvis du allerede er bekendt med kryptovaluta."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Med krypto kan du donere ved hjælp af BTC, ETH, XMR og mere."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Hvis du bruger krypto for første gang, foreslår vi at bruge %(options)s til at købe og donere Bitcoin (den oprindelige og mest brugte kryptovaluta)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Blæksprutte"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "For at donere ved hjælp af PayPal US, vil vi bruge PayPal Crypto, som giver os mulighed for at forblive anonyme. Vi sætter pris på, at du tager dig tid til at lære, hvordan du donerer ved hjælp af denne metode, da det hjælper os meget."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donér ved hjælp af PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donér ved hjælp af Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Hvis du har Cash App, er dette den nemmeste måde at donere på!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Bemærk, at for transaktioner under %(amount)s kan Cash App opkræve et %(fee)s gebyr. For %(amount)s eller derover er det gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Doner ved hjælp af Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Hvis du har Revolut, er dette den nemmeste måde at donere på!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donér med et kredit- eller debetkort."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay og Apple Pay kan også virke."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Bemærk, at for små donationer kan kreditkortgebyrerne eliminere vores %(discount)s%% rabat, så vi anbefaler længere abonnementer."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Bemærk, at for små donationer er gebyrerne høje, så vi anbefaler længere abonnementer."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Med Binance kan du købe Bitcoin med et kredit-/debitkort eller bankkonto og derefter donere den Bitcoin til os. På den måde kan vi forblive sikre og anonyme, når vi modtager din donation."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance er tilgængelig i næsten alle lande og understøtter de fleste banker og kredit-/debitkort. Dette er i øjeblikket vores hovedanbefaling. Vi værdsætter, at du tager dig tid til at lære, hvordan du donerer ved hjælp af denne metode, da det hjælper os meget."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Doner ved hjælp af din almindelige PayPal-konto."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donér ved hjælp af kredit-/debitkort, PayPal eller Venmo. Du kan vælge mellem disse på den næste side."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donér ved hjælp af et Amazon-gavekort."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Bemærk, at vi skal runde op til beløb, der accepteres af vores forhandlere (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>VIGTIGT:</strong> Vi understøtter kun Amazon.com, ikke andre Amazon-websteder. For eksempel understøttes .de, .co.uk, .ca IKKE."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>VIGTIGT:</strong> Denne mulighed er for %(amazon)s. Hvis du vil bruge en anden Amazon-hjemmeside, skal du vælge den ovenfor."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Denne metode bruger en kryptovalutaudbyder som en mellemmand til konvertering. Dette kan være lidt forvirrende, så brug kun denne metode, hvis andre betalingsmetoder ikke virker. Det virker heller ikke i alle lande."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donér ved hjælp af et kredit-/debetkort gennem Alipay-appen (super nemt at sætte op)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installer Alipay-appen"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installer Alipay-appen fra <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrer dig med dit telefonnummer."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Ingen yderligere personlige oplysninger er nødvendige."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Tilføj bankkort"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Understøttet: Visa, MasterCard, JCB, Diners Club og Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Se <a %(a_alipay)s>denne vejledning</a> for mere information."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Vi kan ikke understøtte kredit-/debetkort direkte, fordi bankerne ikke vil samarbejde med os. ☹ Der er dog flere måder at bruge kredit-/debetkort alligevel, ved hjælp af andre betalingsmetoder:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon-gavekort"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Send os Amazon.com-gavekort ved hjælp af dit kredit-/debitkort."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay understøtter internationale kredit-/debetkort. Se <a %(a_alipay)s>denne vejledning</a> for mere information."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) understøtter internationale kredit-/debitkort. I WeChat-appen skal du gå til “Me => Services => Wallet => Add a Card”. Hvis du ikke ser det, skal du aktivere det ved hjælp af “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Du kan købe krypto ved hjælp af kredit-/debitkort."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Krypto ekspres tjenester"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Ekspres tjenester er bekvemme, men opkræver højere gebyrer."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Du kan bruge dette i stedet for en krypto børs, hvis du ønsker hurtigt at lave en større donation og ikke har noget imod et gebyr på $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Sørg for at sende det nøjagtige krypto beløb, der vises på donationssiden, ikke beløbet i $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Ellers vil gebyret blive trukket fra, og vi kan ikke automatisk behandle dit medlemskab."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s afhængig af land, ingen verifikation for første transaktion)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, ingen verifikation for første transaktion)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Hvis nogen af disse oplysninger er forældede, bedes du sende os en e-mail for at informere os."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "For kreditkort, debitkort, Apple Pay og Google Pay bruger vi “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). I deres system er en “kaffe” lig med $5, så din donation vil blive afrundet til nærmeste multiplum af 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Vælg, hvor længe du vil abonnere."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 måned"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 måneder"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 måneder"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 måneder"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 måneder"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 måneder"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 måneder"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>efter <span %(span_discount)s></span> rabatter</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Denne betalingsmetode kræver et minimum på %(amount)s. Vælg venligst en anden varighed eller betalingsmetode."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donér"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Denne betalingsmetode tillader kun et maksimum på %(amount)s. Vælg venligst en anden varighed eller betalingsmetode."

#, fuzzy
msgid "page.donate.login2"
msgstr "For at blive medlem, venligst <a %(a_login)s>Log ind eller Registrer</a>. Tak for din støtte!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Vælg din foretrukne kryptomønt:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(laveste minimumsbeløb)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(bruges ved afsendelse af Ethereum fra Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(advarsel: højt minimumsbeløb)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Klik på doner-knappen for at bekræfte denne donation."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Doner <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Du kan stadig annullere donationen under betalingen."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Omdirigerer til donationssiden…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Noget gik galt. Genindlæs siden og prøv igen."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / måned"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "for 1 måned"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "for 3 måneder"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "for 6 måneder"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "for 12 måneder"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "for 24 måneder"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "for 48 måneder"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "i 96 måneder"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "i 1 måned “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "i 3 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "i 6 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "i 12 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "i 24 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "i 48 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "i 96 måneder “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donation"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Dato: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned i %(duration)s måneder, inklusive %(discounts)s%% rabat)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned i %(duration)s måneder)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifikator: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Annuller"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Er du sikker på, at du ønsker at annullere? Annuller ikke, hvis du allerede har betalt."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, venligst annuller"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Din donation er blevet annulleret."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Foretag en ny donation"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Noget gik galt. Genindlæs venligst siden og prøv igen."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Genbestil"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Du har allerede betalt. Hvis du alligevel vil gennemgå betalingsinstruktionerne, klik her:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Vis gamle betalingsinstruktioner"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Tak for din donation!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Hvis du ikke allerede har gjort det, skriv din hemmelige nøgle ned til at logge ind:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Ellers risikerer du at blive låst ude af denne konto!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Betalingsinstruktionerne er nu forældede. Hvis du ønsker at foretage en ny donation, brug knappen \"Genbestil\" ovenfor."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Vigtig bemærkning:</strong> Kryptopriser kan svinge voldsomt, nogle gange endda så meget som 20%% på få minutter. Dette er stadig mindre end de gebyrer, vi pådrager os med mange betalingsudbydere, som ofte opkræver 50-60%% for at arbejde med en “skyggevelgørenhed” som os. <u>Hvis du sender os kvitteringen med den oprindelige pris, du betalte, vil vi stadig kreditere din konto for det valgte medlemskab</u> (så længe kvitteringen ikke er ældre end et par timer). Vi sætter virkelig pris på, at du er villig til at håndtere sådanne ting for at støtte os! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Denne donation er udløbet. Annuller venligst og opret en ny."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Kryptoinstruktioner"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Overfør til en af vores kryptokonti"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donér det samlede beløb af %(total)s til en af disse adresser:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Køb Bitcoin på Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Find siden “Krypto” i din PayPal-app eller på hjemmesiden. Dette er typisk under “Finanser”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Følg instruktionerne for at købe Bitcoin (BTC). Du behøver kun at købe det beløb, du ønsker at donere, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Overfør Bitcoin til vores adresse"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gå til siden “Bitcoin” i din PayPal-app eller på hjemmesiden. Tryk på knappen “Overfør” %(transfer_icon)s, og derefter “Send”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Indtast vores Bitcoin (BTC) adresse som modtager, og følg instruktionerne for at sende din donation på %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredit- / debetkortinstruktioner"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donér via vores kredit- / debetkorts side"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donér %(amount)s på <a %(a_page)s>denne side</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Se trin-for-trin guiden nedenfor."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Venter på bekræftelse (opdater siden for at tjekke)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Venter på overførsel (opdater siden for at tjekke)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tid tilbage:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(du vil måske annullere og oprette en ny donation)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "For at nulstille timeren skal du blot oprette en ny donation."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Opdater status"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Hvis du støder på problemer, bedes du kontakte os på %(email)s og inkludere så mange oplysninger som muligt (såsom skærmbilleder)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Hvis du allerede har betalt:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Nogle gange kan bekræftelse tage op til 24 timer, så sørg for at opdatere denne side (selv hvis den er udløbet)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Køb PYUSD-mønt på PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Følg instruktionerne for at købe PYUSD-mønt (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er til overs."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Gå til “PYUSD”-siden i din PayPal-app eller på hjemmesiden. Tryk på “Overfør”-knappen %(icon)s, og derefter “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Overfør %(amount)s til %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Køb Bitcoin (BTC) på Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gå til “Bitcoin” (BTC) siden i Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er tilbage."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Overfør Bitcoin til vores adresse"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik på “Send bitcoin” knappen for at foretage en “udbetaling”. Skift fra dollars til BTC ved at trykke på %(icon)s ikonet. Indtast BTC-beløbet nedenfor og klik på “Send”. Se <a %(help_video)s>denne video</a> hvis du sidder fast."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "For små donationer (under $25), kan du muligvis bruge Rush eller Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Køb Bitcoin (BTC) på Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Gå til “Crypto”-siden i Revolut for at købe Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er til overs."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Overfør Bitcoin til vores adresse"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klik på “Send bitcoin”-knappen for at foretage en “udbetaling”. Skift fra euro til BTC ved at trykke på %(icon)s-ikonet. Indtast BTC-beløbet nedenfor og klik på “Send”. Se <a %(help_video)s>denne video</a>, hvis du sidder fast."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Sørg for at bruge BTC-beløbet nedenfor, <em>IKKE</em> euro eller dollars, ellers modtager vi ikke det korrekte beløb og kan ikke automatisk bekræfte dit medlemskab."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "For små donationer (under $25) kan du være nødt til at bruge Rush eller Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Brug en af følgende “kreditkort til Bitcoin” ekspres-tjenester, som kun tager et par minutter:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Udfyld følgende oplysninger i formularen:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin beløb:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Brug venligst dette <span %(underline)s>præcise beløb</span>. Din samlede omkostning kan være højere på grund af kreditkortgebyrer. For små beløb kan dette desværre være mere end vores rabat."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adresse (ekstern wallet):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instruktioner"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Vi understøtter kun standardversionen af kryptomønter, ingen eksotiske netværk eller versioner af mønter. Det kan tage op til en time at bekræfte transaktionen, afhængigt af mønten."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR -kode for at betale"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan denne QR -kode med din Crypto Wallet -app til hurtigt at udfylde betalingsoplysningerne"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon gavekort"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Brug venligst <a %(a_form)s>den officielle Amazon.com formular</a> til at sende os et gavekort på %(amount)s til e-mailadressen nedenfor."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Vi kan ikke acceptere andre metoder til gavekort, <strong>kun sendt direkte fra den officielle formular på Amazon.com</strong>. Vi kan ikke returnere dit gavekort, hvis du ikke bruger denne formular."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Indtast det præcise beløb: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Skriv venligst IKKE din egen besked."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“Til” modtagerens e-mail i formularen:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unik for din konto, del ikke."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Brug kun én gang."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Venter på gavekort… (opdater siden for at tjekke)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Efter at have sendt dit gavekort, vil vores automatiserede system bekræfte det inden for få minutter. Hvis dette ikke virker, prøv at sende dit gavekort igen (<a %(a_instr)s>instruktioner</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Hvis det stadig ikke virker, bedes du sende os en e-mail, og Anna vil manuelt gennemgå det (dette kan tage et par dage), og sørg for at nævne, om du har prøvet at sende igen."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Eksempel:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Bemærk, at kontonavnet eller billedet kan se mærkeligt ud. Ingen grund til bekymring! Disse konti administreres af vores donationspartnere. Vores konti er ikke blevet hacket."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay-instruktioner"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Doner på Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne Alipay-konto</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Hvis donationssiden bliver blokeret, prøv en anden internetforbindelse (f.eks. VPN eller telefoninternet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Desværre er Alipay-siden ofte kun tilgængelig fra <strong>fastlands-Kina</strong>. Du kan være nødt til midlertidigt at deaktivere din VPN eller bruge en VPN til fastlands-Kina (eller Hongkong fungerer også nogle gange)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Foretag donation (scan QR-kode eller tryk på knappen)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Åbn <a %(a_href)s>QR-kode donationssiden</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scan QR-koden med Alipay-appen, eller tryk på knappen for at åbne Alipay-appen."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Vær venligst tålmodig; siden kan tage lidt tid at indlæse, da den er i Kina."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat-instruktioner"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donér på WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne WeChat-konto</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix-instruktioner"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donér på Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne Pix-konto"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Email os kvitteringen"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Send en kvittering eller skærmbillede til din personlige verifikationsadresse. Brug IKKE denne e-mailadresse til din PayPal-donation."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Send en kvittering eller skærmbillede til din personlige verificeringsadresse:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Hvis kryptovalutakursen svingede under transaktionen, skal du sørge for at inkludere kvitteringen, der viser den oprindelige valutakurs. Vi sætter virkelig pris på, at du tager besværet med at bruge krypto, det hjælper os meget!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Når du har sendt din kvittering via e-mail, klik på denne knap, så Anna kan gennemgå den manuelt (dette kan tage et par dage):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Ja, jeg har sendt min kvittering via e-mail"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Tak for din donation! Anna vil manuelt aktivere dit medlemskab inden for et par dage."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Noget gik galt. Genindlæs siden og prøv igen."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Trin-for-trin guide"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Nogle af trinene nævner kryptotegnebøger, men bare rolig, du behøver ikke at lære noget om krypto for dette."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Indtast din e-mail."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Vælg din betalingsmetode."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Vælg din betalingsmetode igen."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Vælg \"Selvhostet\" tegnebog."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Klik på \"Jeg bekræfter ejerskab\"."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Du bør modtage en e-mailkvittering. Send den venligst til os, og vi vil bekræfte din donation så hurtigt som muligt."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Vent venligst mindst <span %(span_hours)s>24 timer</span> (og opdater denne side) før du kontakter os."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Hvis du lavede en fejl under betalingen, kan vi ikke refundere, men vi vil forsøge at rette det."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Mine donationer"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donationsdetaljer vises ikke offentligt."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Ingen donationer endnu. <a %(a_donate)s>Lav min første donation.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Lav en ny donation."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Downloadede filer"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Downloads fra Hurtige Partner Servere er markeret med %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Hvis du har downloadet en fil med både hurtige og langsomme downloads, vil den vises to gange."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Hurtige downloads inden for de sidste 24 timer tæller med i den daglige grænse."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Alle tider er i UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Downloadede filer vises ikke offentligt."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Ingen filer downloadet endnu."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Sidste 18 timer"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Tidligere"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Konto"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Log ind / Registrer"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Konto ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Offentlig profil: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Hemmelig nøgle (del ikke!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "vis"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Medlemskab: <strong>%(tier_name)s</strong> indtil %(until_date)s <a %(a_extend)s>(forlæng)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Medlemskab: <strong>Ingen</strong> <a %(a_become)s>(bliv medlem)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Hurtige downloads brugt (sidste 24 timer): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "hvilke downloads?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Eksklusiv Telegram-gruppe: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Bliv medlem her!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Opgrader til et <a %(a_tier)s>højere niveau</a> for at blive medlem af vores gruppe."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontakt Anna på %(email)s, hvis du er interesseret i at opgradere dit medlemskab til et højere niveau."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontakt email"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Du kan kombinere flere medlemskaber (hurtige downloads pr. 24 timer vil blive lagt sammen)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Offentlig profil"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Downloadede filer"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Mine donationer"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Log ud"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Du er nu logget ud. Genindlæs siden for at logge ind igen."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Noget gik galt. Genindlæs siden og prøv igen."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registrering vellykket! Din hemmelige nøgle er: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Gem denne nøgle omhyggeligt. Hvis du mister den, mister du adgangen til din konto."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Bogmærke.</strong> Du kan bogmærke denne side for at hente din nøgle.</li><li %(li_item)s><strong>Download.</strong> Klik <a %(a_download)s>på dette link</a> for at downloade din nøgle.</li><li %(li_item)s><strong>Adgangskodeadministrator.</strong> Brug en adgangskodeadministrator til at gemme nøglen, når du indtaster den nedenfor.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Indtast din hemmelige nøgle for at logge ind:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Hemmelig nøgle"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Log ind"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ugyldig hemmelig nøgle. Bekræft din nøgle og prøv igen, eller registrer alternativt en ny konto nedenfor."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Mister ikke din nøgle!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Har du ikke en konto endnu?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registrer ny konto"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Hvis du har mistet din nøgle, bedes du <a %(a_contact)s>kontakte os</a> og give så mange oplysninger som muligt."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Du skal muligvis midlertidigt oprette en ny konto for at kontakte os."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Gammel email-baseret konto? Indtast din <a %(a_open)s>email her</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Liste"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "rediger"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Gem"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Gemt. Genindlæs venligst siden."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Noget gik galt. Prøv venligst igen."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Liste af %(by)s, oprettet <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Listen er tom."

#, fuzzy
msgid "page.list.new_item"
msgstr "Tilføj eller fjern fra denne liste ved at finde en fil og åbne fanen “Lister”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profil ikke fundet."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "rediger"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Skift dit visningsnavn. Din identifikator (delen efter “#”) kan ikke ændres."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Gem"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Gemt. Genindlæs venligst siden."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Noget gik galt. Prøv venligst igen."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profil oprettet <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Lister"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Ingen lister endnu"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Opret en ny liste ved at finde en fil og åbne fanen “Lister”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Ophavsretsreform er nødvendig for national sikkerhed"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Kort fortalt: Kinesiske LLM'er (inklusive DeepSeek) er trænet på mit ulovlige arkiv af bøger og artikler — det største i verden. Vesten skal revidere ophavsretsloven som et spørgsmål om national sikkerhed."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "ledsagende artikler af TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>anden</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "For ikke så længe siden var \"skyggebiblioteker\" ved at dø ud. Sci-Hub, det massive ulovlige arkiv af akademiske artikler, var stoppet med at tage imod nye værker på grund af retssager. \"Z-Library\", det største ulovlige bibliotek af bøger, så sine påståede skabere arresteret på grund af kriminelle ophavsretsanklager. De formåede utroligt nok at undslippe deres arrestation, men deres bibliotek er ikke mindre truet."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Da Z-Library stod over for lukning, havde jeg allerede sikkerhedskopieret hele biblioteket og søgte efter en platform til at huse det. Det var min motivation for at starte Annas Arkiv: en fortsættelse af missionen bag de tidligere initiativer. Vi er siden vokset til at være det største skyggebibliotek i verden, der huser mere end 140 millioner ophavsretligt beskyttede tekster i forskellige formater — bøger, akademiske artikler, magasiner, aviser og mere."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mit team og jeg er ideologer. Vi mener, at det er moralsk rigtigt at bevare og huse disse filer. Biblioteker rundt om i verden oplever nedskæringer i finansieringen, og vi kan heller ikke stole på, at menneskehedens arv overlades til virksomheder."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Så kom AI. Næsten alle større virksomheder, der bygger LLM'er, kontaktede os for at træne på vores data. De fleste (men ikke alle!) amerikanske virksomheder genovervejede, da de indså den ulovlige karakter af vores arbejde. Til gengæld har kinesiske firmaer entusiastisk omfavnet vores samling, tilsyneladende uden at bekymre sig om dens lovlighed. Dette er bemærkelsesværdigt i betragtning af Kinas rolle som underskriver af næsten alle større internationale ophavsretstraktater."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Vi har givet højhastighedsadgang til omkring 30 virksomheder. De fleste af dem er LLM-virksomheder, og nogle er databrokere, der vil videresælge vores samling. De fleste er kinesiske, men vi har også arbejdet med virksomheder fra USA, Europa, Rusland, Sydkorea og Japan. DeepSeek <a %(arxiv)s>indrømmede</a>, at en tidligere version blev trænet på en del af vores samling, selvom de er tilbageholdende med at tale om deres nyeste model (sandsynligvis også trænet på vores data)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Hvis Vesten vil forblive foran i kapløbet om LLM'er, og i sidste ende AGI, skal den genoverveje sin holdning til ophavsret, og det snart. Uanset om du er enig med os eller ej i vores moralske sag, bliver dette nu en sag om økonomi og endda national sikkerhed. Alle magtblokke bygger kunstige super-videnskabsmænd, super-hackere og super-militærer. Informationsfrihed bliver et spørgsmål om overlevelse for disse lande — endda et spørgsmål om national sikkerhed."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Vores team er fra hele verden, og vi har ikke en bestemt tilknytning. Men vi vil opfordre lande med stærke ophavsretslove til at bruge denne eksistentielle trussel til at reformere dem. Så hvad skal man gøre?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Vores første anbefaling er ligetil: forkort ophavsretsperioden. I USA gives ophavsret i 70 år efter forfatterens død. Dette er absurd. Vi kan bringe dette i overensstemmelse med patenter, som gives i 20 år efter indgivelse. Dette burde være mere end nok tid for forfattere af bøger, artikler, musik, kunst og andre kreative værker til at blive fuldt kompenseret for deres indsats (inklusive længerevarende projekter som filmatiseringer)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Derefter bør politikerne som minimum inkludere undtagelser for massebevaring og formidling af tekster. Hvis tabt indtægt fra individuelle kunder er den største bekymring, kunne distribution på personligt niveau forblive forbudt. Til gengæld ville de, der er i stand til at administrere store arkiver — virksomheder, der træner LLM'er, sammen med biblioteker og andre arkiver — være dækket af disse undtagelser."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Nogle lande gør allerede en version af dette. TorrentFreak <a %(torrentfreak)s>rapporterede</a>, at Kina og Japan har indført AI-undtagelser i deres ophavsretslove. Det er uklart for os, hvordan dette interagerer med internationale traktater, men det giver bestemt dækning til deres indenlandske virksomheder, hvilket forklarer, hvad vi har set."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Hvad angår Annas Arkiv — vi vil fortsætte vores undergrundsarbejde rodfæstet i moralsk overbevisning. Alligevel er vores største ønske at træde frem i lyset og forstærke vores indflydelse lovligt. Venligst reformér ophavsretten."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Læs de ledsagende artikler af TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>anden</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vinderne af $10.000 ISBN-visualiseringspræmien"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Kort fortalt: Vi fik nogle utrolige indsendelser til $10.000 ISBN-visualiseringspræmien."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "For et par måneder siden annoncerede vi en <a %(all_isbns)s>$10.000 præmie</a> for at lave den bedst mulige visualisering af vores data, der viser ISBN-rummet. Vi lagde vægt på at vise, hvilke filer vi allerede har/ikke har arkiveret, og vi tilføjede senere et datasæt, der beskriver, hvor mange biblioteker der har ISBN'er (et mål for sjældenhed)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Vi har været overvældet af responsen. Der har været så meget kreativitet. En stor tak til alle, der har deltaget: jeres energi og entusiasme er smittende!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "I sidste ende ønskede vi at besvare følgende spørgsmål: <strong>hvilke bøger findes i verden, hvor mange har vi allerede arkiveret, og hvilke bøger skal vi fokusere på næste gang?</strong> Det er fantastisk at se, at så mange mennesker interesserer sig for disse spørgsmål."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Vi startede med en grundlæggende visualisering selv. På mindre end 300kb repræsenterer dette billede kortfattet den største fuldt åbne \"liste over bøger\", der nogensinde er samlet i menneskehedens historie:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBN'er"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Filer i Annas Arkiv"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO'er"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC datalæk"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID'er"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost's eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Bøger"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internetarkivet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Det Russiske Statsbibliotek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Det Kejserlige Bibliotek i Trantor"

#, fuzzy
msgid "common.back"
msgstr "Tilbage"

#, fuzzy
msgid "common.forward"
msgstr "Frem"

#, fuzzy
msgid "common.last"
msgstr "Sidste"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Se venligst <a %(all_isbns)s>det originale blogindlæg</a> for mere information."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Vi udstedte en udfordring for at forbedre dette. Vi ville tildele en førstepræmie på $6.000, andenpladsen $3.000, og tredjepladsen $1.000. På grund af den overvældende respons og utrolige indsendelser har vi besluttet at øge præmiepuljen en smule og tildele en delt tredjeplads til fire deltagere med $500 hver. Vinderne er nedenfor, men sørg for at se alle indsendelser <a %(annas_archive)s>her</a>, eller download vores <a %(a_2025_01_isbn_visualization_files)s>kombinerede torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Førsteplads $6.000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Denne <a %(phiresky_github)s>indsendelse</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) er simpelthen alt, hvad vi ønskede, og mere til! Vi kunne især godt lide de utroligt fleksible visualiseringsmuligheder (selv med understøttelse af brugerdefinerede shaders), men med en omfattende liste af forudindstillinger. Vi kunne også godt lide, hvor hurtigt og glat alting er, den enkle implementering (som ikke engang har en backend), det smarte minimap og den omfattende forklaring i deres <a %(phiresky_github)s>blogindlæg</a>. Utroligt arbejde og en velfortjent vinder!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Andenplads $3.000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Endnu en utrolig <a %(annas_archive_note_2913)s>indsendelse</a>. Ikke så fleksibel som førstepladsen, men vi foretrak faktisk dens makroniveau-visualisering frem for førstepladsen (space-filling curve, grænser, mærkning, fremhævning, panorering og zoom). En <a %(annas_archive_note_2971)s>kommentar</a> af Joe Davis resonnerede med os:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "\"Mens perfekte firkanter og rektangler er matematisk behagelige, giver de ikke overlegen lokalitet i en kortlægningskontekst. Jeg mener, at asymmetrien, der er iboende i disse Hilbert eller klassiske Morton, ikke er en fejl, men en funktion. Ligesom Italiens berømte støvleformede omrids gør det øjeblikkeligt genkendeligt på et kort, kan de unikke 'quirks' ved disse kurver tjene som kognitive landemærker. Denne særprægethed kan forbedre rumlig hukommelse og hjælpe brugere med at orientere sig, hvilket potentielt gør det lettere at finde specifikke regioner eller bemærke mønstre.\""

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Og stadig masser af muligheder for visualisering og rendering samt en utrolig glat og intuitiv brugergrænseflade. En solid andenplads!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tredjeplads $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "I denne <a %(annas_archive_note_2940)s>indsendelse</a> kunne vi virkelig godt lide de forskellige slags visninger, især sammenlignings- og udgivervisningerne."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tredjeplads $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Selvom brugergrænsefladen ikke er den mest polerede, opfylder denne <a %(annas_archive_note_2917)s>indsendelse</a> mange af kriterierne. Vi kunne især godt lide dens sammenligningsfunktion."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tredjeplads $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Ligesom førstepladsen imponerede denne <a %(annas_archive_note_2975)s>indsendelse</a> os med sin fleksibilitet. I sidste ende er det, hvad der gør et godt visualiseringsværktøj: maksimal fleksibilitet for avancerede brugere, mens det holdes enkelt for gennemsnitsbrugere."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tredjeplads $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Den sidste <a %(annas_archive_note_2947)s>indsendelse</a> til at modtage en præmie er ret grundlæggende, men har nogle unikke funktioner, som vi virkelig godt kunne lide. Vi kunne godt lide, hvordan de viser, hvor mange datasets der dækker et bestemt ISBN som et mål for popularitet/pålidelighed. Vi kunne også virkelig godt lide enkelheden, men effektiviteten af at bruge en opacitetsregulator til sammenligninger."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Bemærkelsesværdige idéer"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Nogle flere idéer og implementeringer, vi især kunne lide:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Skyskrabere for sjældenhed"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live-statistik"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotationer og også live-statistik"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unik kortvisning og filtre"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Sejt standardfarveskema og varmekort."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Nem skift mellem datasets for hurtige sammenligninger."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Pæne etiketter."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skalabar med antal bøger."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Mange skydeknapper til at sammenligne datasets, som om du er en DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Vi kunne fortsætte et stykke tid, men lad os stoppe her. Sørg for at se alle indsendelser <a %(annas_archive)s>her</a>, eller download vores <a %(a_2025_01_isbn_visualization_files)s>kombinerede torrent</a>. Så mange indsendelser, og hver bringer et unikt perspektiv, hvad enten det er i UI eller implementering."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Vi vil i det mindste inkorporere førstepladsens indsendelse på vores hovedwebsite, og måske nogle andre. Vi er også begyndt at tænke over, hvordan vi kan organisere processen med at identificere, bekræfte og derefter arkivere de sjældneste bøger. Mere kommer på denne front."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Tak til alle, der deltog. Det er fantastisk, at så mange mennesker bekymrer sig."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Vores hjerter er fulde af taknemmelighed."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisering af alle ISBN'er — $10.000 dusør inden 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dette billede repræsenterer den største fuldt åbne \"bogliste\", der nogensinde er samlet i menneskehedens historie."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dette billede er 1000×800 pixels. Hver pixel repræsenterer 2.500 ISBN'er. Hvis vi har en fil for et ISBN, gør vi den pixel mere grøn. Hvis vi ved, at et ISBN er udstedt, men vi ikke har en matchende fil, gør vi den mere rød."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "På mindre end 300kb repræsenterer dette billede kortfattet den største fuldt åbne \"bogliste\", der nogensinde er samlet i menneskehedens historie (et par hundrede GB komprimeret i fuld størrelse)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Det viser også: der er meget arbejde tilbage med at sikkerhedskopiere bøger (vi har kun 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Baggrund"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hvordan kan Annas Arkiv opnå sin mission om at sikkerhedskopiere al menneskehedens viden uden at vide, hvilke bøger der stadig er derude? Vi har brug for en TODO-liste. En måde at kortlægge dette på er gennem ISBN-numre, som siden 1970'erne er blevet tildelt til hver bog, der udgives (i de fleste lande)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Der er ingen central myndighed, der kender alle ISBN-tildelinger. I stedet er det et distribueret system, hvor lande får tildelt nummerområder, som derefter tildeler mindre områder til større forlag, der måske yderligere underopdeler områder til mindre forlag. Endelig tildeles individuelle numre til bøger."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Vi begyndte at kortlægge ISBN'er <a %(blog)s>for to år siden</a> med vores scraping af ISBNdb. Siden da har vi skrabet mange flere metadata-kilder, såsom <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby og flere. En fuld liste kan findes på siderne \"Datasets\" og \"Torrents\" på Annas Arkiv. Vi har nu den suverænt største fuldt åbne, let downloadbare samling af bogmetadata (og dermed ISBN'er) i verden."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Vi har <a %(blog)s>skrevet udførligt</a> om, hvorfor vi bekymrer os om bevaring, og hvorfor vi i øjeblikket er i et kritisk vindue. Vi skal nu identificere sjældne, underfokuserede og unikt truede bøger og bevare dem. At have gode metadata på alle bøger i verden hjælper med det."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisering"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Udover oversigtsbilledet kan vi også se på individuelle datasets, vi har erhvervet. Brug dropdown-menuen og knapperne til at skifte mellem dem."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Der er mange interessante mønstre at se i disse billeder. Hvorfor er der en vis regelmæssighed af linjer og blokke, der synes at ske i forskellige skalaer? Hvad er de tomme områder? Hvorfor er visse datasets så klyngede? Vi vil lade disse spørgsmål være en øvelse for læseren."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10.000 dusør"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Der er meget at udforske her, så vi annoncerer en dusør for at forbedre visualiseringen ovenfor. I modsætning til de fleste af vores dusører er denne tidsbegrænset. Du skal <a %(annas_archive)s>indsende</a> din open source-kode inden 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Den bedste indsendelse vil få $6.000, andenpladsen er $3.000, og tredjepladsen er $1.000. Alle dusører vil blive udbetalt i Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Nedenfor er de minimale kriterier. Hvis ingen indsendelse opfylder kriterierne, kan vi stadig tildele nogle dusører, men det vil være efter vores skøn."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork dette repo, og rediger dette blogindlæg HTML (ingen andre backends end vores Flask-backend er tilladt)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Gør billedet ovenfor glat zoom-bart, så du kan zoome helt ind på individuelle ISBN'er. Klik på ISBN'er skal føre dig til en metadata-side eller søgning på Annas Arkiv."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Du skal stadig kunne skifte mellem alle forskellige datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landeområder og forlagsområder skal fremhæves ved hover. Du kan f.eks. bruge <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> til landeinfo og vores “isbngrp” scraping til forlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Det skal fungere godt på både desktop og mobil."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "For bonuspoint (dette er blot ideer — lad din kreativitet løbe løbsk):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Der vil blive lagt stor vægt på brugervenlighed og hvor godt det ser ud."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Vis faktiske metadata for individuelle ISBN'er, når du zoomer ind, såsom titel og forfatter."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Bedre pladsfyldningskurve. F.eks. en zig-zag, der går fra 0 til 4 på første række og derefter tilbage (i omvendt rækkefølge) fra 5 til 9 på anden række — anvendt rekursivt."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Forskellige eller tilpassede farveskemaer."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Specielle visninger til sammenligning af datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Måder at fejlfinde problemer på, såsom anden metadata der ikke stemmer godt overens (f.eks. meget forskellige titler)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotering af billeder med kommentarer om ISBN'er eller intervaller."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Eventuelle heuristikker til at identificere sjældne eller truede bøger."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Hvilke kreative ideer du end kan komme på!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Du MÅ helt afvige fra de minimale kriterier og lave en helt anden visualisering. Hvis den er virkelig spektakulær, kvalificerer den sig til belønningen, men efter vores skøn."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Indsend ved at skrive en kommentar til <a %(annas_archive)s>dette issue</a> med et link til din forkede repo, merge request eller diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kode"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Koden til at generere disse billeder, samt andre eksempler, kan findes i <a %(annas_archive)s>dette bibliotek</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Vi har udviklet et kompakt dataformat, hvor al den nødvendige ISBN-information fylder omkring 75MB (komprimeret). Beskrivelsen af dataformatet og koden til at generere det kan findes <a %(annas_archive_l1244_1319)s>her</a>. For belønningen er du ikke forpligtet til at bruge dette, men det er sandsynligvis det mest bekvemme format at starte med. Du kan transformere vores metadata, som du vil (dog skal al din kode være open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Vi kan ikke vente med at se, hvad du finder på. Held og lykke!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annas Arkiv Beholdere (AAC): standardisering af udgivelser fra verdens største skyggebibliotek"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annas Arkiv er blevet verdens største skyggebibliotek, hvilket kræver, at vi standardiserer vores udgivelser."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annas Arkiv</a> er blevet det suverænt største skyggebibliotek i verden og det eneste skyggebibliotek af sin størrelse, der er fuldt open-source og open-data. Nedenfor er en tabel fra vores Datasets-side (let modificeret):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Vi opnåede dette på tre måder:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Spejling af eksisterende open-data skyggebiblioteker (som Sci-Hub og Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Hjælpe skyggebiblioteker, der ønsker at være mere åbne, men ikke havde tid eller ressourcer til det (som Libgen tegneseriesamlingen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scraping af biblioteker, der ikke ønsker at dele i bulk (som Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "For (2) og (3) administrerer vi nu en betydelig samling af torrents selv (100'er af TB'er). Indtil nu har vi behandlet disse samlinger som enkeltstående, hvilket betyder skræddersyet infrastruktur og dataorganisation for hver samling. Dette tilføjer betydelig overhead til hver udgivelse og gør det særligt svært at lave mere inkrementelle udgivelser."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Derfor har vi besluttet at standardisere vores udgivelser. Dette er et teknisk blogindlæg, hvor vi introducerer vores standard: <strong>Annas Arkiv Beholdere</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Designmål"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Vores primære anvendelsestilfælde er distribution af filer og tilhørende metadata fra forskellige eksisterende samlinger. Vores vigtigste overvejelser er:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene filer og metadata, så tæt på det originale format som muligt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene identifikatorer i kildesamlingerne, eller endda mangel på identifikatorer."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separate udgivelser af metadata vs fildata, eller kun metadata-udgivelser (f.eks. vores ISBNdb-udgivelse)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribution gennem torrents, men med mulighed for andre distributionsmetoder (f.eks. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Uforanderlige optegnelser, da vi bør antage, at vores torrents vil eksistere for evigt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementelle udgivelser / tilføjelsesudgivelser."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Maskinlæsbare og skrivbare, bekvemt og hurtigt, især for vores stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Nogenlunde let menneskelig inspektion, selvom dette er sekundært i forhold til maskinlæsbarhed."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Let at så vores samlinger med en standard lejet seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binære data kan serveres direkte af webservere som Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Nogle ikke-mål:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Vi er ligeglade med, om filer er lette at navigere manuelt på disk, eller søgbare uden forbehandling."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Vi er ligeglade med at være direkte kompatible med eksisterende bibliotekssoftware."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Selvom det skal være let for alle at så vores samling ved hjælp af torrents, forventer vi ikke, at filerne er brugbare uden betydelig teknisk viden og engagement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Da Annas Arkiv er open source, ønsker vi at bruge vores format direkte. Når vi opdaterer vores søgeindeks, har vi kun adgang til offentligt tilgængelige stier, så alle, der forgrener vores bibliotek, hurtigt kan komme i gang."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standarden"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "I sidste ende besluttede vi os for en relativt enkel standard. Den er ret løs, ikke-normativ og et igangværende arbejde."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Arkiv Container) er en enkelt enhed bestående af <strong>metadata</strong> og eventuelt <strong>binære data</strong>, som begge er uforanderlige. Den har en globalt unik identifikator, kaldet <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Samling.</strong> Hver AAC tilhører en samling, som per definition er en liste over AAC'er, der er semantisk konsistente. Det betyder, at hvis du foretager en væsentlig ændring i formatet af metadataene, skal du oprette en ny samling."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” og “files” samlinger.</strong> Af konvention er det ofte praktisk at udgive “records” og “files” som forskellige samlinger, så de kan udgives på forskellige tidspunkter, f.eks. baseret på scraping-hastigheder. En “record” er en samling kun med metadata, der indeholder information som bogtitler, forfattere, ISBN'er osv., mens “files” er samlingerne, der indeholder selve filerne (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Formatet for AACID er dette: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. For eksempel er en faktisk AACID, vi har udgivet, <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: samlingsnavnet, som kan indeholde ASCII-bogstaver, tal og understregninger (men ingen dobbelte understregninger)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: en kort version af ISO 8601, altid i UTC, f.eks. <code>20220723T194746Z</code>. Dette tal skal stige monotont for hver udgivelse, selvom dets præcise semantik kan variere per samling. Vi foreslår at bruge tidspunktet for scraping eller generering af ID'et."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: en samlingsspecifik identifikator, hvis relevant, f.eks. Z-Library ID. Kan udelades eller afkortes. Skal udelades eller afkortes, hvis AACID ellers ville overstige 150 tegn."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: en UUID men komprimeret til ASCII, f.eks. ved brug af base57. Vi bruger i øjeblikket <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID-interval.</strong> Da AACID'er indeholder monotont stigende tidsstempler, kan vi bruge det til at angive intervaller inden for en bestemt samling. Vi bruger dette format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, hvor tidsstemplerne er inklusive. Dette er i overensstemmelse med ISO 8601-notation. Intervaller er kontinuerlige og kan overlappe, men i tilfælde af overlapning skal de indeholde identiske poster som den, der tidligere blev udgivet i den samling (da AAC'er er uforanderlige). Manglende poster er ikke tilladt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata-fil.</strong> En metadata-fil indeholder metadataene for et interval af AAC'er, for en bestemt samling. Disse har følgende egenskaber:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Filnavnet skal være et AACID-interval, foranstillet med <code style=\"color: red\">annas_archive_meta__</code> og efterfulgt af <code>.jsonl.zstd</code>. For eksempel er en af vores udgivelser kaldet<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Som angivet af filtypenavnet er filtypen <a %(jsonlines)s>JSON Lines</a> komprimeret med <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Hvert JSON-objekt skal indeholde følgende felter på øverste niveau: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valgfrit). Ingen andre felter er tilladt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> er vilkårlige metadata, i henhold til samlingens semantik. Det skal være semantisk konsistent inden for samlingen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> er valgfrit og er navnet på den binære data-mappe, der indeholder de tilsvarende binære data. Filnavnet på de tilsvarende binære data i den mappe er postens AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Præfikset <code style=\"color: red\">annas_archive_meta__</code> kan tilpasses til navnet på din institution, f.eks. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binær data-mappe.</strong> En mappe med de binære data for et interval af AAC'er, for en bestemt samling. Disse har følgende egenskaber:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Mappenavnet skal være et AACID-interval, foranstillet med <code style=\"color: green\">annas_archive_data__</code>, og ingen suffix. For eksempel har en af vores faktiske udgivelser en mappe kaldet<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Mappen skal indeholde datafiler for alle AAC'er inden for det angivne interval. Hver datafil skal have sin AACID som filnavn (ingen filtypenavne)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Det anbefales at gøre disse mapper nogenlunde håndterbare i størrelse, f.eks. ikke større end 100GB-1TB hver, selvom denne anbefaling kan ændre sig over tid."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Metadatafilerne og binære datamapper kan pakkes i torrents, med én torrent pr. metadatafil eller én torrent pr. binær datamappe. Torrenterne skal have det originale fil-/mappenavn plus et <code>.torrent</code> suffiks som deres filnavn."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Eksempel"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Lad os se på vores seneste Z-Library-udgivelse som et eksempel. Den består af to samlinger: “<span style=\"background: #fffaa3\">zlib3_records</span>” og “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dette giver os mulighed for separat at skrabe og udgive metadataoptegnelser fra de faktiske bogfiler. Som sådan udgav vi to torrents med metadatafiler:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Vi udgav også en række torrents med binære datamapper, men kun for “<span style=\"background: #ffd6fe\">zlib3_files</span>” samlingen, i alt 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Ved at køre <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se, hvad der er indeni:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "I dette tilfælde er det metadata af en bog som rapporteret af Z-Library. På topniveauet har vi kun “aacid” og “metadata”, men ingen “data_folder”, da der ikke er nogen tilsvarende binære data. AACID indeholder “22430000” som det primære ID, hvilket vi kan se er taget fra “zlibrary_id”. Vi kan forvente, at andre AAC'er i denne samling har den samme struktur."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Lad os nu køre <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dette er en meget mindre AAC-metadata, selvom størstedelen af denne AAC er placeret et andet sted i en binær fil! Vi har trods alt en “data_folder” denne gang, så vi kan forvente, at de tilsvarende binære data er placeret på <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” indeholder “zlibrary_id”, så vi kan nemt forbinde det med den tilsvarende AAC i “zlib_records” samlingen. Vi kunne have forbundet på en række forskellige måder, f.eks. gennem AACID — standarden foreskriver ikke det."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Bemærk, at det heller ikke er nødvendigt for “metadata”-feltet selv at være JSON. Det kunne være en streng, der indeholder XML eller et hvilket som helst andet dataformat. Du kunne endda gemme metadataoplysninger i den tilknyttede binære blob, f.eks. hvis det er en masse data."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Konklusion"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Med denne standard kan vi lave udgivelser mere gradvist og lettere tilføje nye datakilder. Vi har allerede nogle spændende udgivelser på vej!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Vi håber også, at det bliver lettere for andre skyggebiblioteker at spejle vores samlinger. Trods alt er vores mål at bevare menneskelig viden og kultur for evigt, så jo mere redundans, desto bedre."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annas Opdatering: fuldt open source arkiv, ElasticSearch, 300GB+ af bogomslag"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Vi har arbejdet døgnet rundt for at tilbyde et godt alternativ med Annas Arkiv. Her er nogle af de ting, vi har opnået for nylig."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Med Z-Library nede og dets (påståede) grundlæggere arresteret, har vi arbejdet døgnet rundt for at tilbyde et godt alternativ med Annas Arkiv (vi vil ikke linke det her, men du kan Google det). Her er nogle af de ting, vi har opnået for nylig."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annas Arkiv er fuldt open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Vi mener, at information skal være fri, og vores egen kode er ingen undtagelse. Vi har udgivet al vores kode på vores privat hostede Gitlab-instans: <a %(annas_archive)s>Annas Software</a>. Vi bruger også problemsporeren til at organisere vores arbejde. Hvis du vil engagere dig i vores udvikling, er dette et godt sted at starte."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "For at give dig en forsmag på de ting, vi arbejder på, kan du se vores nylige arbejde med forbedringer af klient-side ydeevne. Da vi endnu ikke har implementeret pagination, ville vi ofte returnere meget lange søgeresultatsider med 100-200 resultater. Vi ønskede ikke at afkorte søgeresultaterne for tidligt, men det betød, at det ville sænke nogle enheder. For dette implementerede vi et lille trick: vi pakkede de fleste søgeresultater ind i HTML-kommentarer (<code><!-- --></code>), og skrev derefter en lille Javascript, der ville opdage, hvornår et resultat skulle blive synligt, på hvilket tidspunkt vi ville pakke kommentaren ud:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisering\" implementeret i 23 linjer, ingen grund til fancy biblioteker! Dette er den slags hurtig pragmatisk kode, du ender med, når du har begrænset tid og reelle problemer, der skal løses. Det er blevet rapporteret, at vores søgning nu fungerer godt på langsomme enheder!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "En anden stor indsats var at automatisere opbygningen af databasen. Da vi lancerede, trak vi bare tilfældigt forskellige kilder sammen. Nu vil vi holde dem opdaterede, så vi skrev en række scripts til at downloade ny metadata fra de to Library Genesis forks og integrere dem. Målet er ikke kun at gøre dette nyttigt for vores arkiv, men også at gøre det nemt for alle, der vil lege med shadow library metadata. Målet ville være en Jupyter-notesbog, der har alle mulige interessante metadata tilgængelige, så vi kan lave mere forskning som at finde ud af, hvilken <a %(blog)s>procentdel af ISBN'er der bevares for evigt</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Endelig har vi fornyet vores donationssystem. Du kan nu bruge et kreditkort til direkte at indsætte penge i vores kryptovaluta-tegnebøger uden virkelig at skulle vide noget om kryptovalutaer. Vi vil fortsætte med at overvåge, hvor godt dette fungerer i praksis, men det er en stor ting."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Skift til ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "En af vores <a %(annas_archive)s>billetter</a> var en samling af problemer med vores søgesystem. Vi brugte MySQL fuldtekst-søgning, da vi alligevel havde alle vores data i MySQL. Men det havde sine begrænsninger:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Nogle forespørgsler tog super lang tid, til det punkt hvor de ville optage alle de åbne forbindelser."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Som standard har MySQL en minimumsordlængde, ellers kan din indeks blive virkelig stor. Folk rapporterede, at de ikke kunne søge efter \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Søgning var kun nogenlunde hurtig, når den var fuldt indlæst i hukommelsen, hvilket krævede, at vi fik en dyrere maskine til at køre dette på, plus nogle kommandoer til at forindlæse indekset ved opstart."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Vi ville ikke have været i stand til nemt at udvide det til at bygge nye funktioner, som bedre <a %(wikipedia_cjk_characters)s>tokenisering for ikke-mellemrumssprog</a>, filtrering/facettering, sortering, \"mente du\" forslag, autoudfyldning osv."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Efter at have talt med en masse eksperter, besluttede vi os for ElasticSearch. Det har ikke været perfekt (deres standard \"mente du\" forslag og autoudfyldningsfunktioner er dårlige), men overordnet set har det været meget bedre end MySQL til søgning. Vi er stadig ikke <a %(youtube)s>for begejstrede</a> for at bruge det til nogen missionkritiske data (selvom de har gjort en masse <a %(elastic_co)s>fremskridt</a>), men overordnet set er vi ret tilfredse med skiftet."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "For nu har vi implementeret meget hurtigere søgning, bedre sprogunderstøttelse, bedre relevanssortering, forskellige sorteringsmuligheder og filtrering på sprog/bogtype/filtype. Hvis du er nysgerrig på, hvordan det fungerer, <a %(annas_archive_l140)s>tag</a> <a %(annas_archive_l1115)s>et</a> <a %(annas_archive_l1635)s>kig</a>. Det er ret tilgængeligt, selvom det kunne bruge nogle flere kommentarer…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ af bogomslag frigivet"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Endelig er vi glade for at annoncere en lille udgivelse. I samarbejde med folkene, der driver Libgen.rs fork, deler vi alle deres bogomslag gennem torrents og IPFS. Dette vil fordele belastningen af at se omslagene blandt flere maskiner og bevare dem bedre. I mange (men ikke alle) tilfælde er bogomslagene inkluderet i filerne selv, så dette er en slags \"afledte data\". Men at have det i IPFS er stadig meget nyttigt for den daglige drift af både Annas Arkiv og de forskellige Library Genesis forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Som sædvanlig kan du finde denne udgivelse på Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi vil ikke linke til det her, men du kan nemt finde det."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Forhåbentlig kan vi slappe lidt af nu, hvor vi har et anstændigt alternativ til Z-Library. Denne arbejdsbyrde er ikke særlig bæredygtig. Hvis du er interesseret i at hjælpe med programmering, serverdrift eller bevaringsarbejde, så kontakt os endelig. Der er stadig meget <a %(annas_archive)s>arbejde at gøre</a>. Tak for din interesse og støtte."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annas Arkiv har sikkerhedskopieret verdens største tegneserie-shadow-bibliotek (95TB) — du kan hjælpe med at seed det"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Verdens største shadow-bibliotek for tegneserier havde et enkelt fejlpunkt... indtil i dag."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskuter på Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Det største shadow-bibliotek af tegneserier er sandsynligvis det fra en bestemt Library Genesis fork: Libgen.li. Den ene administrator, der driver det site, formåede at samle en vanvittig tegneseriesamling på over 2 millioner filer, i alt over 95TB. Men i modsætning til andre Library Genesis-samlinger var denne ikke tilgængelig i bulk gennem torrents. Du kunne kun få adgang til disse tegneserier individuelt gennem hans langsomme personlige server — et enkelt fejlpunkt. Indtil i dag!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "I dette indlæg vil vi fortælle dig mere om denne samling og om vores indsamlingskampagne for at støtte mere af dette arbejde."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon forsøger at miste sig selv i bibliotekets trivielle verden…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-forke"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Først lidt baggrund. Du kender måske Library Genesis for deres episke bogsamling. Færre mennesker ved, at Library Genesis-frivillige har skabt andre projekter, såsom en betydelig samling af magasiner og standarddokumenter, en fuld backup af Sci-Hub (i samarbejde med grundlæggeren af Sci-Hub, Alexandra Elbakyan), og faktisk en massiv samling af tegneserier."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "På et tidspunkt gik forskellige operatører af Library Genesis-spejle hver deres vej, hvilket gav anledning til den nuværende situation med at have en række forskellige \"forke\", som alle stadig bærer navnet Library Genesis. Libgen.li-forken har unikt denne tegneseriesamling samt en betydelig magasinsamling (som vi også arbejder på)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Samarbejde"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "På grund af dens størrelse har denne samling længe været på vores ønskeliste, så efter vores succes med at tage backup af Z-Library, satte vi vores mål på denne samling. Først skrabede vi den direkte, hvilket var en stor udfordring, da deres server ikke var i den bedste stand. Vi fik omkring 15TB på denne måde, men det gik langsomt."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Heldigvis lykkedes det os at komme i kontakt med operatøren af biblioteket, som indvilligede i at sende os alle data direkte, hvilket gik meget hurtigere. Det tog stadig mere end et halvt år at overføre og behandle alle dataene, og vi var tæt på at miste det hele til disk-korruption, hvilket ville have betydet at starte forfra."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Denne oplevelse har fået os til at tro, at det er vigtigt at få disse data ud så hurtigt som muligt, så de kan spejles vidt og bredt. Vi er kun en eller to uheldige hændelser væk fra at miste denne samling for evigt!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Samlingen"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "At bevæge sig hurtigt betyder, at samlingen er lidt uorganiseret… Lad os tage et kig. Forestil dig, at vi har et filsystem (som i virkeligheden bliver delt op på tværs af torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Den første mappe, <code>/repository</code>, er den mere strukturerede del af dette. Denne mappe indeholder såkaldte \"tusind mapper\": mapper hver med tusind filer, som er inkrementelt nummereret i databasen. Mappen <code>0</code> indeholder filer med comic_id 0–999, og så videre."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dette er det samme skema, som Library Genesis har brugt til sine fiktion- og non-fiktion-samlinger. Ideen er, at hver \"tusind mappe\" automatisk bliver til en torrent, så snart den er fyldt op."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Dog lavede Libgen.li-operatøren aldrig torrents for denne samling, og derfor blev tusind mapperne sandsynligvis upraktiske og gav plads til \"usorterede mapper\". Disse er <code>/comics0</code> til <code>/comics4</code>. De indeholder alle unikke mappestrukturer, der sandsynligvis gav mening for at samle filerne, men som ikke giver så meget mening for os nu. Heldigvis henviser metadataene stadig direkte til alle disse filer, så deres lagringsorganisation på disken betyder faktisk ikke noget!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadataene er tilgængelige i form af en MySQL-database. Denne kan downloades direkte fra Libgen.li-websitet, men vi vil også gøre den tilgængelig i en torrent, sammen med vores egen tabel med alle MD5-hashene."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Når du får 95TB dumpet ind i din lagringsklynge, forsøger du at finde ud af, hvad der overhovedet er derinde… Vi lavede nogle analyser for at se, om vi kunne reducere størrelsen lidt, for eksempel ved at fjerne dubletter. Her er nogle af vores fund:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantiske dubletter (forskellige scanninger af den samme bog) kan teoretisk set filtreres ud, men det er vanskeligt. Når vi manuelt kiggede igennem tegneserierne, fandt vi for mange falske positiver."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Der er nogle dubletter rent ved MD5, hvilket er relativt spild, men at filtrere dem ud ville kun give os omkring 1% in besparelse. I denne skala er det stadig omkring 1TB, men også, i denne skala betyder 1TB ikke rigtig noget. Vi vil hellere ikke risikere at ødelægge data ved en fejl i denne proces."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Vi fandt en bunke ikke-bog data, såsom film baseret på tegneserier. Det virker også spild, da disse allerede er bredt tilgængelige gennem andre midler. Men vi indså, at vi ikke bare kunne filtrere filmfiler ud, da der også er <em>interaktive tegneseriebøger</em>, der blev udgivet på computeren, som nogen optog og gemte som film."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "I sidste ende ville alt, hvad vi kunne slette fra samlingen, kun spare et par procent. Så huskede vi, at vi er datahoardere, og de mennesker, der vil spejle dette, er også datahoardere, og derfor, \"HVAD MENER DU, SLET?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Vi præsenterer derfor for dig den fulde, uændrede samling. Det er en masse data, men vi håber, at nok mennesker vil være interesserede i at dele det alligevel."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Indsamling"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Vi frigiver disse data i nogle store bidder. Den første torrent er af <code>/comics0</code>, som vi har samlet i en kæmpe 12TB .tar-fil. Det er bedre for din harddisk og torrent-software end en million mindre filer."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Som en del af denne udgivelse laver vi en indsamling. Vi søger at rejse $20.000 til at dække drifts- og kontraktomkostninger for denne samling samt muliggøre igangværende og fremtidige projekter. Vi har nogle <em>enorme</em> i gang."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Hvem støtter jeg med min donation?</em> Kort sagt: vi sikkerhedskopierer al menneskehedens viden og kultur og gør det let tilgængeligt. Al vores kode og data er open source, vi er et fuldstændigt frivilligt drevet projekt, og vi har gemt 125TB bøger indtil videre (ud over Libgen og Scihub's eksisterende torrents). I sidste ende bygger vi et svinghjul, der muliggør og motiverer folk til at finde, scanne og sikkerhedskopiere alle bøger i verden. Vi vil skrive om vores masterplan i et fremtidigt indlæg. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Hvis du donerer for et 12-måneders \"Amazing Archivist\"-medlemskab ($780), får du lov til at <strong>“adoptere en torrent”</strong>, hvilket betyder, at vi vil sætte dit brugernavn eller besked i filnavnet på en af torrentene!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Du kan donere ved at gå til <a %(wikipedia_annas_archive)s>Annas Arkiv</a> og klikke på \"Doner\"-knappen. Vi søger også flere frivillige: softwareingeniører, sikkerhedsforskere, anonyme handels-eksperter og oversættere. Du kan også støtte os ved at tilbyde hostingtjenester. Og selvfølgelig, del venligst vores torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Tak til alle, der allerede har støttet os så generøst! I gør virkelig en forskel."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Her er de torrents, der er udgivet indtil videre (vi behandler stadig resten):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alle torrents kan findes på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under \"Datasets\" (vi linker ikke direkte dertil, så links til denne blog ikke bliver fjernet fra Reddit, Twitter osv.). Derfra kan du følge linket til Tor-websitet."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Hvad er det næste?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "En masse torrents er gode til langtidsbevaring, men ikke så meget til daglig adgang. Vi vil arbejde med hostingpartnere for at få alle disse data op på nettet (da Annas Arkiv ikke hoster noget direkte). Selvfølgelig vil du kunne finde disse download-links på Annas Arkiv."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Vi inviterer også alle til at gøre noget med disse data! Hjælp os med at analysere dem bedre, deduplikere dem, lægge dem på IPFS, remixe dem, træne dine AI-modeller med dem osv. Det er alt dit, og vi kan ikke vente med at se, hvad du gør med det."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Endelig, som sagt før, har vi stadig nogle enorme udgivelser på vej (hvis <em>nogen</em> kunne <em>tilfældigvis</em> sende os en dump af en <em>vis</em> ACS4-database, ved du, hvor du kan finde os...), samt bygge svinghjulet til at sikkerhedskopiere alle bøger i verden."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Så hold dig opdateret, vi er kun lige begyndt."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nye bøger tilføjet til Pirate Library Mirror (+24TB, 3,8 millioner bøger)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "I den oprindelige udgivelse af Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), lavede vi et spejl af Z-Library, en stor ulovlig bogsamling. Som en påmindelse, dette er, hvad vi skrev i det oprindelige blogindlæg:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library er et populært (og ulovligt) bibliotek. De har taget Library Genesis-samlingen og gjort den let søgbar. Oven i det er de blevet meget effektive til at anmode om nye bogbidrag ved at motivere bidragende brugere med forskellige fordele. De bidrager i øjeblikket ikke med disse nye bøger tilbage til Library Genesis. Og i modsætning til Library Genesis gør de ikke deres samling let spejlbar, hvilket forhindrer bred bevaring. Dette er vigtigt for deres forretningsmodel, da de opkræver penge for at få adgang til deres samling i bulk (mere end 10 bøger om dagen)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Vi foretager ikke moralske vurderinger om at opkræve penge for masseadgang til en ulovlig bogsamling. Det er uden tvivl, at Z-Library har haft succes med at udvide adgangen til viden og skaffe flere bøger. Vi er her blot for at gøre vores del: at sikre den langsigtede bevarelse af denne private samling."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Den samling daterer sig tilbage til midten af 2021. I mellemtiden er Z-Library vokset i et forbløffende tempo: de har tilføjet omkring 3,8 millioner nye bøger. Der er nogle dubletter derinde, selvfølgelig, men størstedelen af det ser ud til at være legitime nye bøger eller højere kvalitetsscanninger af tidligere indsendte bøger. Dette skyldes i høj grad det øgede antal frivillige moderatorer hos Z-Library og deres masse-upload system med deduplikering. Vi vil gerne lykønske dem med disse præstationer."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Vi er glade for at kunne meddele, at vi har fået alle bøger, der blev tilføjet til Z-Library mellem vores sidste spejl og august 2022. Vi har også gået tilbage og skrabet nogle bøger, som vi missede første gang. Alt i alt er denne nye samling omkring 24TB, hvilket er meget større end den sidste (7TB). Vores spejl er nu i alt 31TB. Igen deduplikerede vi mod Library Genesis, da der allerede er torrents tilgængelige for den samling."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Gå venligst til Pirate Library Mirror for at tjekke den nye samling (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Der er mere information der om, hvordan filerne er struktureret, og hvad der er ændret siden sidst. Vi vil ikke linke til det herfra, da dette blot er en blogside, der ikke hoster ulovligt materiale."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Selvfølgelig er seeding også en fantastisk måde at hjælpe os på. Tak til alle, der seeder vores tidligere sæt af torrents. Vi er taknemmelige for den positive respons og glade for, at der er så mange mennesker, der bekymrer sig om bevarelse af viden og kultur på denne usædvanlige måde."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Sådan bliver du en piratarkivar"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Den første udfordring kan være en overraskende en. Det er ikke et teknisk problem eller et juridisk problem. Det er et psykologisk problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Før vi dykker ned, to opdateringer om Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Vi fik nogle ekstremt generøse donationer. Den første var $10k fra den anonyme person, der også har støttet \"bookwarrior\", den oprindelige grundlægger af Library Genesis. Særlig tak til bookwarrior for at facilitere denne donation. Den anden var endnu $10k fra en anonym donor, der tog kontakt efter vores sidste udgivelse og blev inspireret til at hjælpe. Vi havde også en række mindre donationer. Mange tak for al jeres generøse støtte. Vi har nogle spændende nye projekter i pipelinen, som dette vil støtte, så hold øje med det."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Vi havde nogle tekniske vanskeligheder med størrelsen af vores anden udgivelse, men vores torrents er nu oppe og seeder. Vi fik også et generøst tilbud fra en anonym person om at seede vores samling på deres meget højhastighedsservere, så vi laver en særlig upload til deres maskiner, hvorefter alle andre, der downloader samlingen, bør se en stor forbedring i hastighed."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Hele bøger kan skrives om <em>hvorfor</em> digital bevaring generelt, og piratarkivisme i særdeleshed, men lad os give en hurtig introduktion til dem, der ikke er så bekendte. Verden producerer mere viden og kultur end nogensinde før, men også mere af det går tabt end nogensinde før. Menneskeheden stoler i vid udstrækning på virksomheder som akademiske forlag, streamingtjenester og sociale medievirksomheder med denne arv, og de har ofte ikke vist sig at være gode forvaltere. Se dokumentaren Digital Amnesia, eller virkelig enhver tale af Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Der er nogle institutioner, der gør et godt stykke arbejde med at arkivere så meget som muligt, men de er bundet af loven. Som pirater er vi i en unik position til at arkivere samlinger, som de ikke kan røre ved, på grund af ophavsrets håndhævelse eller andre restriktioner. Vi kan også spejle samlinger mange gange over hele verden, hvilket øger chancerne for korrekt bevaring."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "For nu vil vi ikke gå ind i diskussioner om fordele og ulemper ved intellektuel ejendom, moralen ved at bryde loven, spekulationer om censur eller spørgsmålet om adgang til viden og kultur. Med alt det af vejen, lad os dykke ned i <em>hvordan</em>. Vi vil dele, hvordan vores team blev piratarkivarer, og de erfaringer, vi lærte undervejs. Der er mange udfordringer, når du begiver dig ud på denne rejse, og forhåbentlig kan vi hjælpe dig igennem nogle af dem."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Fællesskab"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Den første udfordring kan være en overraskende en. Det er ikke et teknisk problem eller et juridisk problem. Det er et psykologisk problem: at udføre dette arbejde i skyggerne kan være utroligt ensomt. Afhængigt af hvad du planlægger at gøre, og din trusselsmodel, kan du være nødt til at være meget forsigtig. I den ene ende af spektret har vi folk som Alexandra Elbakyan*, grundlæggeren af Sci-Hub, der er meget åben om sine aktiviteter. Men hun er i høj risiko for at blive arresteret, hvis hun skulle besøge et vestligt land på nuværende tidspunkt, og kunne stå over for årtier i fængsel. Er det en risiko, du er villig til at tage? Vi er i den anden ende af spektret; meget forsigtige med ikke at efterlade nogen spor og have stærk operationel sikkerhed."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Som nævnt på HN af \"ynno\", ønskede Alexandra oprindeligt ikke at være kendt: \"Hendes servere var sat op til at udsende detaljerede fejlmeddelelser fra PHP, inklusive fuld sti til den fejlende kildefil, som var under biblioteket /home/<USER>" Så brug tilfældige brugernavne på de computere, du bruger til dette, i tilfælde af at du konfigurerer noget forkert."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Den hemmelighed kommer dog med en psykologisk omkostning. De fleste mennesker elsker at blive anerkendt for det arbejde, de udfører, og alligevel kan du ikke tage nogen kredit for dette i det virkelige liv. Selv simple ting kan være udfordrende, som når venner spørger dig, hvad du har lavet (på et tidspunkt bliver \"rode med min NAS / homelab\" gammelt)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Derfor er det så vigtigt at finde et fællesskab. Du kan give afkald på noget operationel sikkerhed ved at betro dig til nogle meget nære venner, som du ved, du kan stole dybt på. Selv da skal du være forsigtig med ikke at skrive noget ned, i tilfælde af at de skal overdrage deres e-mails til myndighederne, eller hvis deres enheder er kompromitteret på en anden måde."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Endnu bedre er det at finde nogle medpirater. Hvis dine nære venner er interesserede i at slutte sig til dig, fantastisk! Ellers kan du muligvis finde andre online. Desværre er dette stadig et nichefællesskab. Indtil videre har vi kun fundet en håndfuld andre, der er aktive på dette område. Gode startsteder synes at være Library Genesis-foraene og r/DataHoarder. Archive Team har også ligesindede individer, selvom de opererer inden for loven (selv hvis i nogle grå områder af loven). De traditionelle \"warez\" og piratscener har også folk, der tænker på lignende måder."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Vi er åbne for idéer om, hvordan vi kan fremme fællesskabet og udforske idéer. Du er velkommen til at sende os en besked på Twitter eller Reddit. Måske kunne vi være vært for en form for forum eller chatgruppe. En udfordring er, at dette let kan blive censureret, når man bruger almindelige platforme, så vi ville være nødt til at hoste det selv. Der er også en afvejning mellem at have disse diskussioner helt offentlige (mere potentiel engagement) versus at gøre det privat (ikke lade potentielle \"mål\" vide, at vi er ved at skrabe dem). Vi må tænke over det. Lad os vide, hvis du er interesseret i dette!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekter"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Når vi laver et projekt, har det et par faser:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domænevalg / filosofi: Hvor vil du nogenlunde fokusere, og hvorfor? Hvad er dine unikke passioner, færdigheder og omstændigheder, som du kan bruge til din fordel?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Målvalg: Hvilken specifik samling vil du spejle?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata-skrabning: Katalogisering af information om filerne uden faktisk at downloade de (ofte meget større) filer selv."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Datavalg: Baseret på metadataene, indsnævring af hvilken data der er mest relevant at arkivere lige nu. Det kunne være alt, men ofte er der en rimelig måde at spare plads og båndbredde på."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Data-skrabning: Faktisk at få fat i dataene."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution: Pakke det sammen i torrents, annoncere det et sted, få folk til at sprede det."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Disse er ikke helt uafhængige faser, og ofte sender indsigter fra en senere fase dig tilbage til en tidligere fase. For eksempel, under metadata-skrabning kan du indse, at det mål, du har valgt, har forsvarsmekanismer ud over dit færdighedsniveau (som IP-blokeringer), så du går tilbage og finder et andet mål."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domænevalg / filosofi"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Der er ingen mangel på viden og kulturarv, der skal bevares, hvilket kan være overvældende. Derfor er det ofte nyttigt at tage et øjeblik og tænke over, hvad dit bidrag kan være."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Alle har en forskellig måde at tænke over dette på, men her er nogle spørgsmål, du kunne stille dig selv:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Hvorfor er du interesseret i dette? Hvad brænder du for? Hvis vi kan få en flok mennesker, der alle arkiverer de ting, de specifikt interesserer sig for, ville det dække meget! Du vil vide meget mere end den gennemsnitlige person om din passion, som hvad der er vigtig data at gemme, hvad der er de bedste samlinger og online fællesskaber, og så videre."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Hvilke færdigheder har du, som du kan bruge til din fordel? For eksempel, hvis du er ekspert i online sikkerhed, kan du finde måder at overvinde IP-blokeringer for sikre mål. Hvis du er god til at organisere fællesskaber, kan du måske samle nogle mennesker omkring et mål. Det er dog nyttigt at kende noget programmering, om ikke andet for at opretholde god operationel sikkerhed gennem denne proces."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hvor meget tid har du til dette? Vores råd ville være at starte småt og lave større projekter, efterhånden som du får styr på det, men det kan blive altopslugende."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Hvad ville være et område med høj indflydelse at fokusere på? Hvis du vil bruge X timer på piratarkivering, hvordan kan du så få mest muligt ud af det?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Hvad er unikke måder, du tænker over dette på? Du kan have nogle interessante idéer eller tilgange, som andre måske har overset."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "I vores tilfælde var vi særligt interesserede i den langsigtede bevaring af videnskab. Vi vidste om Library Genesis, og hvordan det blev fuldt spejlet mange gange ved hjælp af torrents. Vi elskede den idé. Så en dag forsøgte en af os at finde nogle videnskabelige lærebøger på Library Genesis, men kunne ikke finde dem, hvilket satte spørgsmålstegn ved, hvor komplet det egentlig var. Vi søgte derefter disse lærebøger online og fandt dem andre steder, hvilket plantede frøet til vores projekt. Selv før vi vidste om Z-Library, havde vi idéen om ikke at forsøge at samle alle disse bøger manuelt, men at fokusere på at spejle eksisterende samlinger og bidrage dem tilbage til Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Målvalg"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Så, vi har vores område, som vi kigger på, men hvilken specifik samling skal vi spejle? Der er et par ting, der gør et godt mål:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Stor"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unik: ikke allerede godt dækket af andre projekter."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Tilgængelig: bruger ikke mange lag af beskyttelse for at forhindre dig i at skrabe deres metadata og data."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Speciel indsigt: du har nogle særlige oplysninger om dette mål, som at du på en eller anden måde har særlig adgang til denne samling, eller du har fundet ud af, hvordan du overvinder deres forsvar. Dette er ikke nødvendigt (vores kommende projekt gør ikke noget specielt), men det hjælper bestemt!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Da vi fandt vores naturvidenskabelige lærebøger på andre hjemmesider end Library Genesis, forsøgte vi at finde ud af, hvordan de fandt vej til internettet. Vi fandt derefter Z-Library og indså, at mens de fleste bøger ikke først dukker op der, ender de til sidst der. Vi lærte om dets forhold til Library Genesis og den (økonomiske) incitamentsstruktur og overlegne brugergrænseflade, som begge gjorde det til en meget mere komplet samling. Vi foretog derefter nogle indledende metadata- og datascraping og indså, at vi kunne omgå deres IP-downloadbegrænsninger ved at udnytte en af vores medlemmers særlige adgang til mange proxyservere."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Når du udforsker forskellige mål, er det allerede vigtigt at skjule dine spor ved at bruge VPN'er og engangse-mailadresser, som vi vil tale mere om senere."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata-scraping"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Lad os blive lidt mere tekniske her. For faktisk at skrabe metadata fra hjemmesider har vi holdt tingene ret enkle. Vi bruger Python-scripts, nogle gange curl, og en MySQL-database til at gemme resultaterne i. Vi har ikke brugt nogen fancy scraping-software, der kan kortlægge komplekse hjemmesider, da vi indtil videre kun har haft brug for at skrabe en eller to slags sider ved blot at enumerere gennem id'er og analysere HTML'en. Hvis der ikke er let enumererede sider, kan du have brug for en ordentlig crawler, der forsøger at finde alle sider."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Før du begynder at skrabe en hel hjemmeside, så prøv at gøre det manuelt i et stykke tid. Gå igennem et par dusin sider selv for at få en fornemmelse af, hvordan det fungerer. Nogle gange vil du allerede støde på IP-blokeringer eller anden interessant adfærd på denne måde. Det samme gælder for datascraping: før du går for dybt ind i dette mål, skal du sikre dig, at du faktisk kan downloade dets data effektivt."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "For at omgå restriktioner er der et par ting, du kan prøve. Er der andre IP-adresser eller servere, der hoster de samme data, men ikke har de samme restriktioner? Er der nogen API-endepunkter, der ikke har restriktioner, mens andre har? Ved hvilken downloadhastighed bliver din IP blokeret, og hvor længe? Eller bliver du ikke blokeret, men neddroslet? Hvad hvis du opretter en brugerkonto, hvordan ændrer tingene sig så? Kan du bruge HTTP/2 til at holde forbindelser åbne, og øger det den hastighed, hvormed du kan anmode om sider? Er der sider, der lister flere filer på én gang, og er de oplysninger, der er angivet der, tilstrækkelige?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Ting, du sandsynligvis vil gemme, inkluderer:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Filnavn / placering"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kan være et internt ID, men ID'er som ISBN eller DOI er også nyttige."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Størrelse: for at beregne, hvor meget diskplads du har brug for."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): for at bekræfte, at du har downloadet filen korrekt."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Dato tilføjet/modificeret: så du kan komme tilbage senere og downloade filer, som du ikke downloadede før (selvom du ofte også kan bruge ID eller hash til dette)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beskrivelse, kategori, tags, forfattere, sprog osv."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Vi gør typisk dette i to faser. Først downloader vi de rå HTML-filer, normalt direkte ind i MySQL (for at undgå mange små filer, som vi taler mere om nedenfor). Derefter, i et separat trin, gennemgår vi disse HTML-filer og parser dem til faktiske MySQL-tabeller. På denne måde behøver du ikke at gen-downloade alt fra bunden, hvis du opdager en fejl i din parseringskode, da du blot kan genbehandle HTML-filerne med den nye kode. Det er også ofte lettere at parallelisere behandlingsfasen, hvilket sparer tid (og du kan skrive behandlingskoden, mens scraping kører, i stedet for at skulle skrive begge trin på én gang)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Endelig skal det bemærkes, at for nogle mål er metadata scraping alt, hvad der er. Der findes nogle enorme metadata-samlinger derude, som ikke er ordentligt bevaret."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Dataudvælgelse"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Ofte kan du bruge metadataene til at finde ud af et rimeligt delmængde af data at downloade. Selv hvis du til sidst ønsker at downloade alle dataene, kan det være nyttigt at prioritere de vigtigste elementer først, i tilfælde af at du bliver opdaget og forsvarene forbedres, eller fordi du skal købe flere diske, eller simpelthen fordi noget andet dukker op i dit liv, før du kan downloade alt."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "For eksempel kan en samling have flere udgaver af den samme underliggende ressource (som en bog eller en film), hvor en er markeret som værende af den bedste kvalitet. At gemme disse udgaver først ville give meget mening. Du vil måske til sidst gemme alle udgaver, da metadataene i nogle tilfælde kan være forkert mærket, eller der kan være ukendte kompromiser mellem udgaverne (for eksempel kan \"den bedste udgave\" være bedst på de fleste måder, men værre på andre måder, som en film med højere opløsning men uden undertekster)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Du kan også søge i din metadata-database for at finde interessante ting. Hvad er den største fil, der er hostet, og hvorfor er den så stor? Hvad er den mindste fil? Er der interessante eller uventede mønstre, når det kommer til bestemte kategorier, sprog osv.? Er der dubletter eller meget lignende titler? Er der mønstre for, hvornår data blev tilføjet, som en dag, hvor mange filer blev tilføjet på én gang? Du kan ofte lære meget ved at se på datasættet på forskellige måder."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "I vores tilfælde deduplikerede vi Z-Library bøger mod md5-hashene i Library Genesis, hvilket sparer meget downloadtid og diskplads. Dette er dog en ret unik situation. I de fleste tilfælde er der ingen omfattende databaser over, hvilke filer der allerede er ordentligt bevaret af andre pirater. Dette er i sig selv en stor mulighed for nogen derude. Det ville være fantastisk at have en regelmæssigt opdateret oversigt over ting som musik og film, der allerede er bredt seedet på torrent-websteder, og derfor er lavere prioritet at inkludere i piratspejle."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Datascraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nu er du klar til faktisk at downloade dataene i bulk. Som nævnt tidligere, på dette tidspunkt bør du allerede manuelt have downloadet en bunke filer for bedre at forstå målgruppens adfærd og begrænsninger. Der vil dog stadig være overraskelser i vente for dig, når du faktisk begynder at downloade mange filer på én gang."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Vores råd her er hovedsageligt at holde det simpelt. Start med blot at downloade en bunke filer. Du kan bruge Python og derefter udvide til flere tråde. Men nogle gange er det endnu enklere at generere Bash-filer direkte fra databasen og derefter køre flere af dem i flere terminalvinduer for at skalere op. Et hurtigt teknisk trick, der er værd at nævne her, er at bruge OUTFILE i MySQL, som du kan skrive hvor som helst, hvis du deaktiverer \"secure_file_priv\" i mysqld.cnf (og sørg også for at deaktivere/overstyre AppArmor, hvis du er på Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Vi gemmer dataene på simple harddiske. Start med det, du har, og udvid langsomt. Det kan være overvældende at tænke på at gemme hundreder af TB data. Hvis det er den situation, du står overfor, så læg først en god delmængde ud, og i din meddelelse bed om hjælp til at gemme resten. Hvis du selv vil have flere harddiske, så har r/DataHoarder nogle gode ressourcer til at få gode tilbud."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Prøv ikke at bekymre dig for meget om fancy filsystemer. Det er nemt at falde ned i kaninhullet med at opsætte ting som ZFS. En teknisk detalje at være opmærksom på er dog, at mange filsystemer ikke håndterer mange filer godt. Vi har fundet ud af, at en simpel løsning er at oprette flere mapper, f.eks. for forskellige ID-intervaller eller hash-præfikser."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Efter at have downloadet dataene, skal du sørge for at kontrollere filernes integritet ved hjælp af hashene i metadataene, hvis tilgængelige."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Du har dataene, hvilket giver dig besiddelse af verdens første piratspejl af dit mål (højst sandsynligt). På mange måder er den sværeste del overstået, men den mest risikable del ligger stadig foran dig. Trods alt har du indtil nu været i det skjulte; fløjet under radaren. Alt, hvad du skulle gøre, var at bruge en god VPN hele vejen igennem, ikke udfylde dine personlige oplysninger i nogen formularer (duh), og måske bruge en speciel browsersession (eller endda en anden computer)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nu skal du distribuere dataene. I vores tilfælde ønskede vi først at bidrage med bøgerne tilbage til Library Genesis, men opdagede hurtigt vanskelighederne ved det (fiktion vs. ikke-fiktion sortering). Så vi besluttede os for distribution ved hjælp af Library Genesis-stil torrents. Hvis du har mulighed for at bidrage til et eksisterende projekt, kan det spare dig for meget tid. Der er dog ikke mange velorganiserede piratspejle derude i øjeblikket."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Så lad os sige, at du beslutter dig for at distribuere torrents selv. Prøv at holde disse filer små, så de er nemme at spejle på andre websteder. Du skal derefter selv seede torrents, mens du stadig forbliver anonym. Du kan bruge en VPN (med eller uden port forwarding), eller betale med tumbled Bitcoins for en Seedbox. Hvis du ikke ved, hvad nogle af disse termer betyder, har du en masse læsning at gøre, da det er vigtigt, at du forstår risikohandlingerne her."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Du kan hoste torrentfilerne selv på eksisterende torrent-websteder. I vores tilfælde valgte vi faktisk at hoste en hjemmeside, da vi også ønskede at sprede vores filosofi på en klar måde. Du kan gøre dette selv på en lignende måde (vi bruger Njalla til vores domæner og hosting, betalt med tumbled Bitcoins), men du er også velkommen til at kontakte os for at få os til at hoste dine torrents. Vi ønsker at opbygge en omfattende indeks over piratspejle over tid, hvis denne idé fanger an."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Hvad angår VPN-valg, er der allerede skrevet meget om dette, så vi gentager blot det generelle råd om at vælge efter omdømme. Faktiske retssagstestede no-log-politikker med lange track records for at beskytte privatlivets fred er den laveste risikomulighed, efter vores mening. Bemærk, at selv når du gør alt rigtigt, kan du aldrig komme til nul risiko. For eksempel, når du seeder dine torrents, kan en højt motiveret statslig aktør sandsynligvis se på indgående og udgående dataflow for VPN-servere og udlede, hvem du er. Eller du kan simpelthen lave en fejl. Vi har sandsynligvis allerede gjort det, og vil gøre det igen. Heldigvis er nationer ikke så interesserede i pirateri."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "En beslutning, der skal træffes for hvert projekt, er, om det skal offentliggøres med den samme identitet som før eller ej. Hvis du fortsætter med at bruge det samme navn, kan fejl i operationel sikkerhed fra tidligere projekter komme tilbage og bide dig. Men at offentliggøre under forskellige navne betyder, at du ikke opbygger et længerevarende omdømme. Vi valgte at have stærk operationel sikkerhed fra starten, så vi kan fortsætte med at bruge den samme identitet, men vi vil ikke tøve med at offentliggøre under et andet navn, hvis vi laver en fejl, eller hvis omstændighederne kræver det."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "At få ordet ud kan være vanskeligt. Som vi sagde, er dette stadig et nichefællesskab. Vi postede oprindeligt på Reddit, men fik virkelig opmærksomhed på Hacker News. For nu er vores anbefaling at poste det nogle få steder og se, hvad der sker. Og igen, kontakt os. Vi vil elske at sprede ordet om flere piratarkivisme-indsatser."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Konklusion"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Forhåbentlig er dette nyttigt for nybegyndere inden for piratarkivering. Vi er begejstrede for at byde dig velkommen til denne verden, så tøv ikke med at række ud. Lad os bevare så meget af verdens viden og kultur som muligt og spejle det vidt og bredt."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introduktion til Piratbibliotekets Spejl: Bevaring af 7TB bøger (som ikke er i Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dette projekt (REDIGERET: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) har til formål at bidrage til bevarelse og frigørelse af menneskelig viden. Vi yder vores lille og ydmyge bidrag i fodsporene på de store før os."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Fokus for dette projekt er illustreret ved dets navn:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirat</strong> - Vi overtræder bevidst ophavsretsloven i de fleste lande. Dette giver os mulighed for at gøre noget, som juridiske enheder ikke kan: sikre, at bøger spejles vidt og bredt."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotek</strong> - Ligesom de fleste biblioteker fokuserer vi primært på skriftlige materialer som bøger. Vi kan udvide til andre typer medier i fremtiden."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spejl</strong> - Vi er udelukkende et spejl af eksisterende biblioteker. Vi fokuserer på bevaring, ikke på at gøre bøger let søgbare og downloadbare (adgang) eller på at fremme et stort fællesskab af mennesker, der bidrager med nye bøger (kilder)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Det første bibliotek, vi har spejlet, er Z-Library. Dette er et populært (og ulovligt) bibliotek. De har taget Library Genesis-samlingen og gjort den let søgbar. Derudover er de blevet meget effektive til at anmode om nye bogbidrag ved at belønne bidragende brugere med forskellige fordele. De bidrager i øjeblikket ikke med disse nye bøger tilbage til Library Genesis. Og i modsætning til Library Genesis gør de ikke deres samling let spejlbar, hvilket forhindrer bred bevaring. Dette er vigtigt for deres forretningsmodel, da de opkræver penge for at få adgang til deres samling i bulk (mere end 10 bøger om dagen)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Vi foretager ikke moralske vurderinger om at opkræve penge for masseadgang til en ulovlig bogsamling. Det er uden tvivl, at Z-Library har haft succes med at udvide adgangen til viden og skaffe flere bøger. Vi er her blot for at gøre vores del: at sikre den langsigtede bevarelse af denne private samling."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Vi vil gerne invitere dig til at hjælpe med at bevare og frigøre menneskelig viden ved at downloade og seede vores torrents. Se projektets side for mere information om, hvordan dataene er organiseret."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Vi vil også meget gerne invitere dig til at bidrage med dine idéer til, hvilke samlinger der skal spejles næste gang, og hvordan man gør det. Sammen kan vi opnå meget. Dette er blot et lille bidrag blandt utallige andre. Tak for alt, hvad du gør."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Vi linker ikke til filerne fra denne blog. Find dem selv.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, eller Hvor Mange Bøger Er Bevarede For Evigt?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Hvis vi skulle deduplikere filerne fra skyggebiblioteker korrekt, hvilken procentdel af alle bøger i verden har vi bevaret?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Med Piratbibliotekets Spejl (REDIGERET: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) er vores mål at tage alle bøger i verden og bevare dem for evigt.<sup>1</sup> Mellem vores Z-Library torrents og de originale Library Genesis torrents har vi 11.783.153 filer. Men hvor mange er det egentlig? Hvis vi korrekt deduplikerede disse filer, hvilken procentdel af alle bøger i verden har vi bevaret? Vi vil virkelig gerne have noget som dette:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of menneskehedens skriftlige arv bevaret for evigt"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "For en procentdel har vi brug for en nævner: det samlede antal bøger, der nogensinde er udgivet.<sup>2</sup> Før Google Books' nedlæggelse forsøgte en ingeniør på projektet, Leonid Taycher, <a %(booksearch_blogspot)s>at estimere</a> dette antal. Han kom — med et glimt i øjet — frem til 129.864.880 (“i det mindste indtil søndag”). Han estimerede dette antal ved at bygge en samlet database over alle bøger i verden. Til dette samlede han forskellige datasæt og fusionerede dem derefter på forskellige måder."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Som en hurtig sidebemærkning er der en anden person, der forsøgte at katalogisere alle bøger i verden: Aaron Swartz, den afdøde digitale aktivist og medstifter af Reddit.<sup>3</sup> Han <a %(youtube)s>startede Open Library</a> med målet om “en webside for hver bog, der nogensinde er udgivet”, ved at kombinere data fra mange forskellige kilder. Han endte med at betale den ultimative pris for sit arbejde med digital bevaring, da han blev retsforfulgt for masse-download af akademiske artikler, hvilket førte til hans selvmord. Det siger sig selv, at dette er en af grundene til, at vores gruppe er pseudonym, og hvorfor vi er meget forsigtige. Open Library drives stadig heroisk af folkene hos Internet Archive, der fortsætter Aarons arv. Vi vender tilbage til dette senere i dette indlæg."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "I Google-blogindlægget beskriver Taycher nogle af udfordringerne ved at estimere dette tal. Først, hvad udgør en bog? Der er et par mulige definitioner:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fysiske kopier.</strong> Det er selvfølgelig ikke særlig nyttigt, da de blot er kopier af det samme materiale. Det ville være fedt, hvis vi kunne bevare alle de noter, folk laver i bøger, som Fermats berømte “kruseduller i marginerne”. Men desværre vil det forblive en arkivars drøm."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Værker”.</strong> For eksempel “Harry Potter og Hemmelighedernes Kammer” som et logisk koncept, der omfatter alle versioner af den, som forskellige oversættelser og genoptryk. Dette er en slags nyttig definition, men det kan være svært at trække grænsen for, hvad der tæller. For eksempel vil vi sandsynligvis gerne bevare forskellige oversættelser, selvom genoptryk med kun mindre forskelle måske ikke er så vigtige."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Udgaver”.</strong> Her tæller du hver unik version af en bog. Hvis noget ved den er anderledes, som et andet omslag eller et andet forord, tæller det som en anden udgave."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Filer.</strong> Når man arbejder med skyggebiblioteker som Library Genesis, Sci-Hub eller Z-Library, er der en yderligere overvejelse. Der kan være flere scanninger af den samme udgave. Og folk kan lave bedre versioner af eksisterende filer ved at scanne teksten ved hjælp af OCR eller rette sider, der blev scannet i en vinkel. Vi ønsker kun at tælle disse filer som én udgave, hvilket ville kræve god metadata eller deduplikering ved hjælp af dokumentlignende målinger."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Udgaver” synes at være den mest praktiske definition af, hvad “bøger” er. Bekvemt bruges denne definition også til at tildele unikke ISBN-numre. Et ISBN, eller International Standard Book Number, bruges almindeligvis til international handel, da det er integreret med det internationale stregkodesystem (”International Article Number”). Hvis du vil sælge en bog i butikker, skal den have en stregkode, så du får et ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taychers blogindlæg nævner, at mens ISBN'er er nyttige, er de ikke universelle, da de først blev rigtig adopteret i midten af halvfjerdserne, og ikke overalt i verden. Alligevel er ISBN sandsynligvis den mest udbredte identifikator for bogudgaver, så det er vores bedste udgangspunkt. Hvis vi kan finde alle ISBN'er i verden, får vi en nyttig liste over, hvilke bøger der stadig skal bevares."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Så, hvor får vi dataene fra? Der er en række eksisterende bestræbelser, der forsøger at samle en liste over alle bøger i verden:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> De lavede trods alt denne forskning for Google Books. Dog er deres metadata ikke tilgængelige i bulk og ret svære at skrabe."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Som nævnt før, er dette deres hele mission. De har hentet enorme mængder af biblioteksdata fra samarbejdende biblioteker og nationale arkiver og fortsætter med at gøre det. De har også frivillige bibliotekarer og et teknisk team, der forsøger at deduplikere poster og mærke dem med alle slags metadata. Bedst af alt er deres datasæt helt åbent. Du kan simpelthen <a %(openlibrary)s>downloade det</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dette er en hjemmeside drevet af den non-profit OCLC, som sælger biblioteksstyringssystemer. De samler bogmetadata fra mange biblioteker og gør det tilgængeligt gennem WorldCat-hjemmesiden. Dog tjener de også penge på at sælge disse data, så de er ikke tilgængelige for bulk-download. De har nogle mere begrænsede bulk-datasæt tilgængelige for download i samarbejde med specifikke biblioteker."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dette er emnet for dette blogindlæg. ISBNdb skraber forskellige hjemmesider for bogmetadata, især prisdata, som de derefter sælger til boghandlere, så de kan prissætte deres bøger i overensstemmelse med resten af markedet. Da ISBN'er er ret universelle i dag, har de effektivt bygget en “webside for hver bog”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Forskellige individuelle biblioteksystemer og arkiver.</strong> Der er biblioteker og arkiver, der ikke er blevet indekseret og aggregeret af nogen af de ovenstående, ofte fordi de er underfinansierede, eller af andre grunde ikke ønsker at dele deres data med Open Library, OCLC, Google osv. Mange af disse har digitale optegnelser tilgængelige via internettet, og de er ofte ikke særlig godt beskyttede, så hvis du vil hjælpe og have det sjovt med at lære om mærkelige biblioteksystemer, er disse gode udgangspunkter."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "I dette indlæg er vi glade for at kunne annoncere en lille udgivelse (sammenlignet med vores tidligere Z-Library-udgivelser). Vi skrabede det meste af ISBNdb og gjorde dataene tilgængelige for torrenting på Pirate Library Mirror's hjemmeside (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>; vi vil ikke linke det direkte her, bare søg efter det). Disse er omkring 30,9 millioner poster (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippet). På deres hjemmeside hævder de, at de faktisk har 32,6 millioner poster, så vi kan på en eller anden måde have misset nogle, eller <em>de</em> kunne gøre noget forkert. Under alle omstændigheder vil vi for nu ikke dele præcis, hvordan vi gjorde det — vi vil lade det være en øvelse for læseren. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Hvad vi vil dele, er nogle foreløbige analyser for at forsøge at komme tættere på at estimere antallet af bøger i verden. Vi kiggede på tre datasæt: dette nye ISBNdb-datasæt, vores oprindelige udgivelse af metadata, som vi skrabede fra Z-Library skyggebiblioteket (som inkluderer Library Genesis), og Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Lad os starte med nogle grove tal:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "I både Z-Library/Libgen og Open Library er der mange flere bøger end unikke ISBN'er. Betyder det, at mange af disse bøger ikke har ISBN'er, eller mangler ISBN-metadataen simpelthen? Vi kan sandsynligvis besvare dette spørgsmål med en kombination af automatiseret matching baseret på andre attributter (titel, forfatter, udgiver osv.), inddragelse af flere datakilder og udtrækning af ISBN'er fra selve bogscanningerne (i tilfælde af Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Hvor mange af disse ISBN'er er unikke? Dette illustreres bedst med et Venn-diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "For at være mere præcis:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Vi blev overraskede over, hvor lidt overlap der er! ISBNdb har en enorm mængde ISBN'er, der ikke dukker op i hverken Z-Library eller Open Library, og det samme gælder (i mindre, men stadig betydelig grad) for de to andre. Dette rejser mange nye spørgsmål. Hvor meget ville automatisk matching hjælpe med at tagge de bøger, der ikke blev tagget med ISBN'er? Ville der være mange matches og dermed øget overlap? Og hvad ville der ske, hvis vi tilføjer et 4. eller 5. datasæt? Hvor meget overlap ville vi se da?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dette giver os et udgangspunkt. Vi kan nu se på alle de ISBN'er, der ikke var i Z-Library-datasættet, og som heller ikke matcher titel/forfatter-felterne. Det kan give os en håndsrækning til at bevare alle bøger i verden: først ved at skrabe internettet for scanninger, derefter ved at gå ud i det virkelige liv for at scanne bøger. Sidstnævnte kunne endda være crowd-finansieret eller drevet af \"dusører\" fra folk, der gerne vil se bestemte bøger digitaliseret. Alt det er en historie til en anden gang."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Hvis du vil hjælpe med noget af dette — yderligere analyse; skrabe mere metadata; finde flere bøger; OCR'ing af bøger; gøre dette for andre domæner (f.eks. artikler, lydbøger, film, tv-shows, magasiner) eller endda gøre nogle af disse data tilgængelige til ting som ML / store sprogmodeltræning — kontakt mig venligst (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Hvis du er specielt interesseret i dataanalysen, arbejder vi på at gøre vores datasæt og scripts tilgængelige i et mere brugervenligt format. Det ville være fantastisk, hvis du bare kunne forke en notebook og begynde at lege med dette."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Endelig, hvis du vil støtte dette arbejde, overvej venligst at give en donation. Dette er en helt frivilligt drevet operation, og dit bidrag gør en stor forskel. Hver lille smule hjælper. For nu tager vi donationer i krypto; se Doner-siden på Annas Arkiv."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. For en rimelig definition af \"for evigt\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Selvfølgelig er menneskehedens skriftlige arv meget mere end bøger, især i dag. For denne posts skyld og vores seneste udgivelser fokuserer vi på bøger, men vores interesser strækker sig længere."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Der er meget mere, der kan siges om Aaron Swartz, men vi ville blot nævne ham kort, da han spiller en central rolle i denne historie. Som tiden går, kan flere mennesker støde på hans navn for første gang og derefter selv dykke ned i kaninhullet."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Det kritiske vindue for skyggebiblioteker"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Hvordan kan vi påstå at bevare vores samlinger i al evighed, når de allerede nærmer sig 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Kinesisk version 中文版</a>, diskuter på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "På Annas Arkiv bliver vi ofte spurgt, hvordan vi kan påstå at bevare vores samlinger i al evighed, når den samlede størrelse allerede nærmer sig 1 Petabyte (1000 TB) og stadig vokser. I denne artikel vil vi se på vores filosofi og se, hvorfor det næste årti er kritisk for vores mission om at bevare menneskehedens viden og kultur."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Den <a %(annas_archive_stats)s>samlede størrelse</a> af vores samlinger, over de sidste par måneder, opdelt efter antal torrent-seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioriteter"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Hvorfor bekymrer vi os så meget om artikler og bøger? Lad os sætte vores grundlæggende tro på bevaring generelt til side — vi kunne skrive et andet indlæg om det. Så hvorfor artikler og bøger specifikt? Svaret er enkelt: <strong>informationsdensitet</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte lagerplads gemmer skreven tekst mest information af alle medier. Mens vi bekymrer os om både viden og kultur, bekymrer vi os mere om førstnævnte. Overordnet set finder vi en hierarki af informationsdensitet og vigtigheden af bevaring, der ser nogenlunde sådan ud:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademiske artikler, tidsskrifter, rapporter"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organiske data som DNA-sekvenser, plantefrø eller mikrobielle prøver"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Faglitteratur bøger"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Videnskabs- og ingeniørsoftwarekode"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Måledata som videnskabelige målinger, økonomiske data, virksomhedsrapporter"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Videnskabs- og ingeniørwebsites, online diskussioner"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Faglitterære magasiner, aviser, manualer"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Faglitterære transskriptioner af foredrag, dokumentarer, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Intern data fra virksomheder eller regeringer (lækager)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata-poster generelt (af faglitteratur og skønlitteratur; af andre medier, kunst, personer osv.; inklusive anmeldelser)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografiske data (f.eks. kort, geologiske undersøgelser)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transskriptioner af juridiske eller retlige procedurer"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiktive eller underholdningsversioner af alle ovenstående"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Rangeringen på denne liste er noget vilkårlig — flere punkter er uafgjorte eller har uenigheder inden for vores team — og vi glemmer sandsynligvis nogle vigtige kategorier. Men dette er omtrent, hvordan vi prioriterer."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Nogle af disse punkter er for forskellige fra de andre til, at vi bekymrer os om dem (eller er allerede taget hånd om af andre institutioner), såsom organiske data eller geografiske data. Men de fleste af punkterne på denne liste er faktisk vigtige for os."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "En anden stor faktor i vores prioritering er, hvor meget en bestemt værk er i fare. Vi foretrækker at fokusere på værker, der er:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Sjældne"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unikt underfokuserede"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unikt i fare for ødelæggelse (f.eks. ved krig, nedskæringer i finansiering, retssager eller politisk forfølgelse)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Endelig bekymrer vi os om skala. Vi har begrænset tid og penge, så vi vil hellere bruge en måned på at redde 10.000 bøger end 1.000 bøger — hvis de er omtrent lige værdifulde og i fare."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Skyggebiblioteker"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Der er mange organisationer, der har lignende missioner og lignende prioriteter. Faktisk er der biblioteker, arkiver, laboratorier, museer og andre institutioner, der har til opgave at bevare denne slags. Mange af dem er velstøttede af regeringer, enkeltpersoner eller virksomheder. Men de har én massiv blind vinkel: det juridiske system."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Her ligger den unikke rolle for skyggebiblioteker, og grunden til at Annas Arkiv eksisterer. Vi kan gøre ting, som andre institutioner ikke har lov til at gøre. Nu er det ikke (ofte), at vi kan arkivere materialer, der er ulovlige at bevare andre steder. Nej, det er lovligt mange steder at opbygge et arkiv med alle bøger, papirer, magasiner osv."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Men hvad juridiske arkiver ofte mangler, er <strong>redundans og lang levetid</strong>. Der findes bøger, hvoraf der kun eksisterer én kopi i et fysisk bibliotek et sted. Der findes metadataoptegnelser, der er bevogtet af en enkelt virksomhed. Der findes aviser, der kun er bevaret på mikrofilm i et enkelt arkiv. Biblioteker kan få nedskæringer i finansieringen, virksomheder kan gå konkurs, arkiver kan blive bombet og brændt ned til grunden. Dette er ikke hypotetisk — det sker hele tiden."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Det, vi unikt kan gøre på Annas Arkiv, er at opbevare mange kopier af værker i stor skala. Vi kan samle artikler, bøger, magasiner og mere og distribuere dem i store mængder. Vi gør dette i øjeblikket gennem torrents, men de præcise teknologier er ikke vigtige og vil ændre sig over tid. Det vigtige er at få mange kopier distribueret over hele verden. Dette citat fra for over 200 år siden er stadig aktuelt:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Det tabte kan ikke genvindes; men lad os redde det, der er tilbage: ikke ved hvælvinger og låse, der beskytter dem fra offentlighedens øjne og brug, ved at overgive dem til tidens spild, men ved en sådan mangfoldiggørelse af kopier, som placerer dem uden for rækkevidde af uheld.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "En hurtig bemærkning om public domain. Da Annas Arkiv unikt fokuserer på aktiviteter, der er ulovlige mange steder i verden, bekymrer vi os ikke om bredt tilgængelige samlinger, såsom public domain-bøger. Juridiske enheder tager ofte allerede godt vare på det. Dog er der overvejelser, der gør, at vi nogle gange arbejder på offentligt tilgængelige samlinger:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadataoptegnelser kan frit ses på Worldcat-webstedet, men ikke downloades i bulk (indtil vi <a %(worldcat_scrape)s>scrapede</a> dem)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kode kan være open source på Github, men Github som helhed kan ikke let spejles og dermed bevares (selvom der i dette særlige tilfælde er tilstrækkeligt distribuerede kopier af de fleste kodearkiver)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit er gratis at bruge, men har for nylig indført strenge anti-scraping foranstaltninger i kølvandet på datahungrende LLM-træning (mere om det senere)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "En mangfoldiggørelse af kopier"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tilbage til vores oprindelige spørgsmål: hvordan kan vi hævde at bevare vores samlinger i al evighed? Hovedproblemet her er, at vores samling er <a %(torrents_stats)s>vokset</a> hurtigt ved at scrape og open-source nogle massive samlinger (oven på det fantastiske arbejde, der allerede er udført af andre open-data skyggebiblioteker som Sci-Hub og Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Denne vækst i data gør det sværere for samlingerne at blive spejlet rundt om i verden. Datastorage er dyrt! Men vi er optimistiske, især når vi observerer følgende tre tendenser."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Vi har plukket de lavthængende frugter"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dette følger direkte af vores prioriteter diskuteret ovenfor. Vi foretrækker at arbejde på at frigøre store samlinger først. Nu hvor vi har sikret nogle af de største samlinger i verden, forventer vi, at vores vækst vil være meget langsommere."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Der er stadig en lang hale af mindre samlinger, og nye bøger bliver scannet eller udgivet hver dag, men hastigheden vil sandsynligvis være meget langsommere. Vi kan stadig fordoble eller endda tredoble i størrelse, men over en længere periode."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Lageromkostninger fortsætter med at falde eksponentielt"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "På tidspunktet for skrivningen er <a %(diskprices)s>diskpriser</a> pr. TB omkring $12 for nye diske, $8 for brugte diske og $4 for bånd. Hvis vi er konservative og kun ser på nye diske, betyder det, at det koster omkring $12.000 at opbevare en petabyte. Hvis vi antager, at vores bibliotek vil tredoble fra 900TB til 2,7PB, ville det betyde $32.400 for at spejle hele vores bibliotek. Tilføjer vi elektricitet, omkostninger til andet hardware osv., lad os runde det op til $40.000. Eller med bånd mere som $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "På den ene side er <strong>$15.000–$40.000 for summen af al menneskelig viden et kup</strong>. På den anden side er det lidt stejlt at forvente tonsvis af fulde kopier, især hvis vi også gerne vil have, at de mennesker fortsætter med at seede deres torrents til gavn for andre."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Det er i dag. Men fremskridt marcherer fremad:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Harddiskomkostninger pr. TB er blevet reduceret til en tredjedel over de sidste 10 år og vil sandsynligvis fortsætte med at falde i et lignende tempo. Bånd ser ud til at være på en lignende bane. SSD-priser falder endnu hurtigere og kan overtage HDD-priserne ved slutningen af årtiet."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-pristrends fra forskellige kilder (klik for at se undersøgelse)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Hvis dette holder, kan vi om 10 år se på kun $5.000–$13.000 for at spejle hele vores samling (1/3), eller endda mindre, hvis vi vokser mindre i størrelse. Selvom det stadig er mange penge, vil dette være opnåeligt for mange mennesker. Og det kan endda blive bedre på grund af det næste punkt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Forbedringer i informationsdensitet"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Vi opbevarer i øjeblikket bøger i de rå formater, som de bliver givet til os. De er godt nok komprimerede, men ofte er de stadig store scanninger eller fotografier af sider."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Indtil nu har de eneste muligheder for at reducere den samlede størrelse af vores samling været gennem mere aggressiv komprimering eller deduplikering. Men for at opnå betydelige besparelser er begge dele for tabsgivende for vores smag. Kraftig komprimering af fotos kan gøre teksten næsten ulæselig. Og deduplikering kræver høj sikkerhed for, at bøgerne er nøjagtigt de samme, hvilket ofte er for unøjagtigt, især hvis indholdet er det samme, men scanningerne er lavet på forskellige tidspunkter."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Der har altid været en tredje mulighed, men dens kvalitet har været så elendig, at vi aldrig overvejede den: <strong>OCR, eller optisk tegngenkendelse</strong>. Dette er processen med at konvertere fotos til almindelig tekst ved hjælp af AI til at genkende tegnene i fotos. Værktøjer til dette har længe eksisteret og har været ret gode, men \"ret gode\" er ikke nok til bevaringsformål."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Dog har nylige multimodale dybdelæringsmodeller gjort ekstremt hurtige fremskridt, selvom de stadig er dyre. Vi forventer, at både nøjagtighed og omkostninger vil forbedres dramatisk i de kommende år, til det punkt hvor det bliver realistisk at anvende på hele vores bibliotek."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Forbedringer i OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Når det sker, vil vi sandsynligvis stadig bevare de originale filer, men derudover kunne vi have en meget mindre version af vores bibliotek, som de fleste vil ønske at spejle. Det smarte er, at rå tekst i sig selv komprimerer endnu bedre og er meget lettere at deduplikere, hvilket giver os endnu flere besparelser."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Samlet set er det ikke urealistisk at forvente mindst en 5-10x reduktion i den samlede filstørrelse, måske endda mere. Selv med en konservativ 5x reduktion, ville vi se på <strong>1.000–3.000 $ om 10 år, selv hvis vores bibliotek tredobles i størrelse</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritisk vindue"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Hvis disse prognoser er nøjagtige, skal vi <strong>bare vente et par år</strong>, før hele vores samling vil blive bredt spejlet. Således, med Thomas Jeffersons ord, \"placeret uden for rækkevidde af uheld.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Desværre har fremkomsten af LLM'er og deres datahungrige træning sat mange ophavsretshavere på defensiven. Endnu mere end de allerede var. Mange hjemmesider gør det sværere at skrabe og arkivere, retssager flyver rundt, og imens fortsætter fysiske biblioteker og arkiver med at blive forsømt."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Vi kan kun forvente, at disse tendenser fortsætter med at forværres, og mange værker vil gå tabt længe før de kommer i det offentlige domæne."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Vi står på tærsklen til en revolution inden for bevaring, men <q>det tabte kan ikke genvindes.</q></strong> Vi har et kritisk vindue på omkring 5-10 år, hvor det stadig er ret dyrt at drive et skyggebibliotek og skabe mange spejle rundt om i verden, og hvor adgangen endnu ikke er blevet fuldstændig lukket."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Hvis vi kan bygge bro over dette vindue, vil vi faktisk have bevaret menneskehedens viden og kultur for evigt. Vi bør ikke lade denne tid gå til spilde. Vi bør ikke lade dette kritiske vindue lukke for os."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Lad os komme i gang."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Eksklusiv adgang for LLM-virksomheder til verdens største kinesiske faglitterære bogsamling"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Kinesisk version 中文版</a>, <a %(news_ycombinator)s>Diskuter på Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Kort fortalt:</strong> Annas Arkiv har erhvervet en unik samling af 7,5 millioner / 350TB kinesiske faglitterære bøger — større end Library Genesis. Vi er villige til at give en LLM-virksomhed eksklusiv adgang i bytte for høj kvalitet OCR og tekstekstraktion.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dette er et kort blogindlæg. Vi leder efter en virksomhed eller institution, der kan hjælpe os med OCR og tekstekstraktion for en massiv samling, vi har erhvervet, i bytte for eksklusiv tidlig adgang. Efter embargo-perioden vil vi selvfølgelig frigive hele samlingen."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Akademisk tekst af høj kvalitet er yderst nyttig til træning af LLM'er. Selvom vores samling er kinesisk, bør den også være nyttig til træning af engelske LLM'er: modeller synes at kunne kode koncepter og viden uanset kildesproget."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "For dette skal teksten udtrækkes fra scanningerne. Hvad får Annas Arkiv ud af det? Fuldtekstsøgning i bøgerne for sine brugere."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Fordi vores mål stemmer overens med LLM-udvikleres, leder vi efter en samarbejdspartner. Vi er villige til at give dig <strong>eksklusiv tidlig adgang til denne samling i bulk i 1 år</strong>, hvis du kan udføre korrekt OCR og tekstudtrækning. Hvis du er villig til at dele hele koden til din pipeline med os, vil vi være villige til at forlænge embargoperioden for samlingen."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Eksempelsider"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "For at bevise for os, at du har en god pipeline, er her nogle eksempelsider at starte med, fra en bog om superledere. Din pipeline skal korrekt håndtere matematik, tabeller, diagrammer, fodnoter osv."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Send dine behandlede sider til vores e-mail. Hvis de ser godt ud, sender vi dig flere privat, og vi forventer, at du hurtigt kan køre din pipeline på dem også. Når vi er tilfredse, kan vi indgå en aftale."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Samling"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Lidt mere information om samlingen. <a %(duxiu)s>Duxiu</a> er en massiv database af scannede bøger, skabt af <a %(chaoxing)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøger, scannet for at gøre dem digitalt tilgængelige for universiteter og biblioteker. For vores engelsktalende publikum har <a %(library_princeton)s>Princeton</a> og <a %(guides_lib_uw)s>University of Washington</a> gode oversigter. Der er også en fremragende artikel, der giver mere baggrund: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (find den i Annas Arkiv)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Bøgerne fra Duxiu er længe blevet piratkopieret på det kinesiske internet. Normalt bliver de solgt for mindre end en dollar af forhandlere. De distribueres typisk ved hjælp af den kinesiske ækvivalent til Google Drive, som ofte er blevet hacket for at tillade mere lagerplads. Nogle tekniske detaljer kan findes <a %(github_duty_machine)s>her</a> og <a %(github_821_github_io)s>her</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Selvom bøgerne er blevet semi-offentligt distribueret, er det ret svært at få dem i bulk. Vi havde dette højt på vores TODO-liste og afsatte flere måneders fuldtidsarbejde til det. Men for nylig kontaktede en utrolig, fantastisk og talentfuld frivillig os og fortalte, at de allerede havde udført alt dette arbejde — til stor udgift. De delte hele samlingen med os uden at forvente noget til gengæld, bortset fra garantien om langsigtet bevaring. Virkelig bemærkelsesværdigt. De gik med til at bede om hjælp på denne måde for at få samlingen OCR'et."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Samlingen består af 7.543.702 filer. Dette er mere end Library Genesis non-fiction (omkring 5,3 millioner). Den samlede filstørrelse er omkring 359TB (326TiB) i sin nuværende form."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Vi er åbne for andre forslag og idéer. Kontakt os bare. Tjek Annas Arkiv for mere information om vores samlinger, bevaringsindsats og hvordan du kan hjælpe. Tak!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Advarsel: dette blogindlæg er blevet forældet. Vi har besluttet, at IPFS endnu ikke er klar til primetime. Vi vil stadig linke til filer på IPFS fra Annas Arkiv, når det er muligt, men vi vil ikke længere hoste det selv, og vi anbefaler heller ikke andre at spejle ved hjælp af IPFS. Se venligst vores Torrents-side, hvis du vil hjælpe med at bevare vores samling."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Hjælp med at seed Z-Library på IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Sådan driver du et skyggebibliotek: operationer på Annas Arkiv"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Der er ingen <q>AWS for skyggevelgørenheder,</q> så hvordan driver vi Annas Arkiv?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Jeg driver <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største open-source non-profit søgemaskine for <a %(wikipedia_shadow_library)s>skyggebiblioteker</a>, som Sci-Hub, Library Genesis og Z-Library. Vores mål er at gøre viden og kultur let tilgængelig og i sidste ende at opbygge et fællesskab af mennesker, der sammen arkiverer og bevarer <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle bøger i verden</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "I denne artikel vil jeg vise, hvordan vi driver denne hjemmeside, og de unikke udfordringer, der følger med at drive en hjemmeside med tvivlsom juridisk status, da der ikke er nogen “AWS for skyggevelgørenheder”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Tjek også søsterartiklen <a %(blog_how_to_become_a_pirate_archivist)s>Hvordan man bliver en piratarkivar</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovationstokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Lad os starte med vores teknologistak. Den er bevidst kedelig. Vi bruger Flask, MariaDB og ElasticSearch. Det er bogstaveligt talt det. Søgefunktioner er stort set et løst problem, og vi har ikke til hensigt at genopfinde det. Desuden skal vi bruge vores <a %(mcfunley)s>innovationstokens</a> på noget andet: ikke at blive lukket ned af myndighederne."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Så hvor lovlig eller ulovlig er Annas Arkiv egentlig? Det afhænger mest af den juridiske jurisdiktion. De fleste lande tror på en form for ophavsret, hvilket betyder, at personer eller virksomheder tildeles et eksklusivt monopol på visse typer værker i en bestemt periode. Som en sidebemærkning mener vi hos Annas Arkiv, at selvom der er nogle fordele, er ophavsret samlet set en negativ for samfundet — men det er en historie til en anden gang."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dette eksklusive monopol på visse værker betyder, at det er ulovligt for nogen uden for dette monopol at distribuere disse værker direkte — inklusive os. Men Annas Arkiv er en søgemaskine, der ikke direkte distribuerer disse værker (i det mindste ikke på vores clearnet-websted), så vi burde være okay, ikke? Ikke helt. I mange jurisdiktioner er det ikke kun ulovligt at distribuere ophavsretligt beskyttede værker, men også at linke til steder, der gør det. Et klassisk eksempel på dette er USA's DMCA-lov."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Det er den strengeste ende af spektret. I den anden ende af spektret kunne der teoretisk set være lande uden nogen ophavsretslove overhovedet, men disse eksisterer ikke rigtig. Næsten alle lande har en form for ophavsretslovgivning. Håndhævelse er en anden historie. Der er masser af lande med regeringer, der ikke bryder sig om at håndhæve ophavsretsloven. Der er også lande mellem de to ekstremer, som forbyder distribution af ophavsretligt beskyttede værker, men ikke forbyder at linke til sådanne værker."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "En anden overvejelse er på virksomhedsplan. Hvis en virksomhed opererer i en jurisdiktion, der ikke bryder sig om ophavsret, men virksomheden selv ikke er villig til at tage nogen risiko, kan de lukke dit websted, så snart nogen klager over det."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Endelig er en stor overvejelse betalinger. Da vi skal forblive anonyme, kan vi ikke bruge traditionelle betalingsmetoder. Dette efterlader os med kryptovalutaer, og kun en lille delmængde af virksomheder understøtter dem (der er virtuelle debetkort betalt med krypto, men de accepteres ofte ikke)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systemarkitektur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Så lad os sige, at du har fundet nogle virksomheder, der er villige til at hoste dit websted uden at lukke dig ned — lad os kalde dem “frihedselskende udbydere” 😄. Du vil hurtigt opdage, at det er ret dyrt at hoste alt hos dem, så du vil måske finde nogle “billige udbydere” og gøre den faktiske hosting der, ved at proxy gennem de frihedselskende udbydere. Hvis du gør det rigtigt, vil de billige udbydere aldrig vide, hvad du hoster, og aldrig modtage nogen klager."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Med alle disse udbydere er der en risiko for, at de alligevel lukker dig ned, så du har også brug for redundans. Vi har brug for dette på alle niveauer af vores stak."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "En nogenlunde frihedselskende virksomhed, der har sat sig selv i en interessant position, er Cloudflare. De har <a %(blog_cloudflare)s>argumenteret</a> for, at de ikke er en hostingudbyder, men en forsyning, ligesom en internetudbyder. De er derfor ikke underlagt DMCA eller andre anmodninger om nedlukning og videresender eventuelle anmodninger til din faktiske hostingudbyder. De er gået så langt som at gå i retten for at beskytte denne struktur. Vi kan derfor bruge dem som et andet lag af caching og beskyttelse."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare accepterer ikke anonyme betalinger, så vi kan kun bruge deres gratis plan. Dette betyder, at vi ikke kan bruge deres load balancing eller failover-funktioner. Vi har derfor <a %(annas_archive_l255)s>implementeret dette selv</a> på domæneniveau. Ved sideindlæsning vil browseren kontrollere, om det aktuelle domæne stadig er tilgængeligt, og hvis ikke, omskriver den alle URL'er til et andet domæne. Da Cloudflare cacher mange sider, betyder det, at en bruger kan lande på vores hoveddomæne, selvom proxyserveren er nede, og derefter ved næste klik blive flyttet over til et andet domæne."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Vi har stadig også normale operationelle bekymringer at håndtere, såsom overvågning af serverens sundhed, logning af backend- og frontend-fejl og så videre. Vores failover-arkitektur tillader mere robusthed på denne front også, for eksempel ved at køre et helt andet sæt servere på et af domænerne. Vi kan endda køre ældre versioner af koden og datasæt på dette separate domæne, i tilfælde af at en kritisk fejl i hovedversionen går ubemærket hen."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Vi kan også gardere os mod, at Cloudflare vender sig mod os, ved at fjerne det fra et af domænerne, såsom dette separate domæne. Forskellige permutationer af disse ideer er mulige."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Værktøjer"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Lad os se på, hvilke værktøjer vi bruger til at opnå alt dette. Dette udvikler sig meget, efterhånden som vi støder på nye problemer og finder nye løsninger."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Applikationsserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxyserver: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serveradministration: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Udvikling: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion statisk hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Der er nogle beslutninger, som vi har gået frem og tilbage med. En af dem er kommunikationen mellem servere: vi plejede at bruge Wireguard til dette, men fandt ud af, at det lejlighedsvis stopper med at transmittere data, eller kun transmitterer data i én retning. Dette skete med flere forskellige Wireguard-opsætninger, som vi prøvede, såsom <a %(github_costela_wesher)s>wesher</a> og <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi prøvede også at tunnelere porte over SSH ved hjælp af autossh og sshuttle, men stødte på <a %(github_sshuttle)s>problemer der</a> (selvom det stadig ikke er klart for mig, om autossh lider af TCP-over-TCP problemer eller ej — det føles bare som en klodset løsning for mig, men måske er det faktisk fint?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "I stedet gik vi tilbage til direkte forbindelser mellem servere, og skjulte at en server kører på de billige udbydere ved hjælp af IP-filtrering med UFW. Dette har den ulempe, at Docker ikke fungerer godt med UFW, medmindre du bruger <code>network_mode: \"host\"</code>. Alt dette er lidt mere fejlbehæftet, fordi du vil udsætte din server for internettet med bare en lille fejlkonfiguration. Måske skulle vi gå tilbage til autossh — feedback ville være meget velkommen her."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Vi har også gået frem og tilbage mellem Varnish og Nginx. Vi kan i øjeblikket godt lide Varnish, men det har sine særheder og ru kanter. Det samme gælder for Checkmk: vi elsker det ikke, men det fungerer for nu. Weblate har været okay, men ikke fantastisk — jeg frygter nogle gange, at det vil miste mine data, når jeg prøver at synkronisere det med vores git-repo. Flask har generelt været god, men det har nogle mærkelige særheder, der har kostet meget tid at fejlfinde, såsom at konfigurere brugerdefinerede domæner eller problemer med dets SqlAlchemy-integration."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Indtil videre har de andre værktøjer været fantastiske: vi har ingen alvorlige klager over MariaDB, ElasticSearch, Gitlab, Zulip, Docker og Tor. Alle disse har haft nogle problemer, men intet alt for alvorligt eller tidskrævende."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Konklusion"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Det har været en interessant oplevelse at lære, hvordan man opsætter en robust og modstandsdygtig skyggebibliotekssøgemaskine. Der er mange flere detaljer at dele i senere indlæg, så lad mig vide, hvad du gerne vil lære mere om!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Som altid søger vi donationer for at støtte dette arbejde, så sørg for at tjekke donationssiden på Annas Arkiv. Vi leder også efter andre former for støtte, såsom tilskud, langsigtede sponsorer, højrisiko betalingsudbydere, måske endda (smagfulde!) annoncer. Og hvis du vil bidrage med din tid og dine færdigheder, leder vi altid efter udviklere, oversættere osv. Tak for din interesse og støtte."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hej, jeg er Anna. Jeg skabte <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største skyggebibliotek. Dette er min personlige blog, hvor jeg og mine holdkammerater skriver om piratkopiering, digital bevaring og mere."

#, fuzzy
msgid "blog.index.text2"
msgstr "Forbind med mig på <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Bemærk, at denne hjemmeside kun er en blog. Vi hoster kun vores egne ord her. Ingen torrents eller andre ophavsretligt beskyttede filer hostes eller linkes her."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogindlæg"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat skrabning"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Lægger 5.998.794 bøger på IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Advarsel: dette blogindlæg er blevet forældet. Vi har besluttet, at IPFS endnu ikke er klar til primetime. Vi vil stadig linke til filer på IPFS fra Annas Arkiv, når det er muligt, men vi vil ikke længere hoste det selv, og vi anbefaler heller ikke andre at spejle ved hjælp af IPFS. Se venligst vores Torrents-side, hvis du vil hjælpe med at bevare vores samling."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Kort fortalt:</strong> Annas Arkiv skrabede hele WorldCat (verdens største bibliotek metadata samling) for at lave en TODO-liste over bøger, der skal bevares.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "For et år siden <a %(blog)s>begyndte vi</a> at besvare dette spørgsmål: <strong>Hvilken procentdel af bøger er blevet permanent bevaret af skyggebiblioteker?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Når en bog kommer ind i et åbent-data skyggebibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, og nu <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, bliver den spejlet over hele verden (gennem torrents), og dermed praktisk talt bevaret for evigt."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "For at besvare spørgsmålet om, hvilken procentdel af bøger der er blevet bevaret, skal vi kende nævneren: hvor mange bøger findes der i alt? Og ideelt set har vi ikke bare et tal, men faktisk metadata. Så kan vi ikke kun matche dem mod skyggebiblioteker, men også <strong>oprette en TODO-liste over resterende bøger, der skal bevares!</strong> Vi kunne endda begynde at drømme om en crowdsourcet indsats for at gå ned ad denne TODO-liste."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Vi skrabede <a %(wikipedia_isbndb_com)s>ISBNdb</a> og downloadede <a %(openlibrary)s>Open Library dataset</a>, men resultaterne var utilfredsstillende. Hovedproblemet var, at der ikke var meget overlap af ISBN'er. Se dette Venn-diagram fra <a %(blog)s>vores blogindlæg</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Vi blev meget overraskede over, hvor lidt overlap der var mellem ISBNdb og Open Library, som begge liberalt inkluderer data fra forskellige kilder, såsom webscrapes og biblioteksregistre. Hvis de begge gør et godt stykke arbejde med at finde de fleste ISBN'er derude, ville deres cirkler helt sikkert have betydeligt overlap, eller den ene ville være en delmængde af den anden. Det fik os til at undre os over, hvor mange bøger der falder <em>helt uden for disse cirkler</em>? Vi har brug for en større database."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Det var da, vi satte vores mål på verdens største bogdatabase: <a %(wikipedia_worldcat)s>WorldCat</a>. Dette er en proprietær database af den non-profit <a %(wikipedia_oclc)s>OCLC</a>, som samler metadataregistre fra biblioteker over hele verden, i bytte for at give disse biblioteker adgang til det fulde datasæt og få dem til at dukke op i slutbrugernes søgeresultater."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Selvom OCLC er en non-profit, kræver deres forretningsmodel, at de beskytter deres database. Nå, vi er kede af at sige det, venner hos OCLC, vi giver det hele væk. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "I løbet af det sidste år har vi omhyggeligt skrabet alle WorldCat-poster. I starten fik vi et heldigt gennembrud. WorldCat var netop ved at udrulle deres komplette webstedsdesign (i august 2022). Dette omfattede en betydelig revision af deres backend-systemer, hvilket introducerede mange sikkerhedsfejl. Vi greb straks muligheden og var i stand til at skrabe hundreder af millioner (!) af poster på få dage."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Derefter blev sikkerhedsfejl langsomt rettet én efter én, indtil den sidste, vi fandt, blev lappet for omkring en måned siden. På det tidspunkt havde vi stort set alle poster og gik kun efter lidt højere kvalitetsregistre. Så vi følte, det var tid til at frigive!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Lad os se på nogle grundlæggende oplysninger om dataene:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Annas Arkiv Beholdere (AAC)</a>, som i det væsentlige er <a %(jsonlines)s>JSON Lines</a> komprimeret med <a %(zstd)s>Zstandard</a>, plus nogle standardiserede semantikker. Disse beholdere omslutter forskellige typer poster, baseret på de forskellige skrabninger, vi har udført."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "En ukendt fejl opstod. Kontakt os venligst på %(email)s med et skærmbillede."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Denne mønt har en højere minimum end normalt. Vælg en anden varighed eller en anden mønt."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Anmodningen kunne ikke gennemføres. Prøv igen om et par minutter, og hvis det fortsætter, kontakt os på %(email)s med et skærmbillede."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Fejl i betalingsbehandlingen. Vent et øjeblik og prøv igen. Hvis problemet fortsætter i mere end 24 timer, kontakt os på %(email)s med et skærmbillede."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "skjult kommentar"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Filproblem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Bedre version"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ønsker du at rapportere denne bruger for misbrug eller upassende adfærd?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Rapportér misbrug"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Misbrug rapporteret:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Du har rapporteret denne bruger for misbrug."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Svar"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Venligst <a %(a_login)s>log ind</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Du har efterladt en kommentar. Det kan tage et minut, før den vises."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Noget gik galt. Genindlæs siden og prøv igen."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s berørte sider"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Ikke synlig i Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Ikke synlig i Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Ikke synlig i Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Markeret som ødelagt i Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Mangler fra Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Markeret som “spam” i Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Markeret som “dårlig fil” i Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Ikke alle sider kunne konverteres til PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Kørsel af exiftool mislykkedes på denne fil"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Bog (ukendt)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Bog (non-fiktion)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Bog (fiktion)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Tidsskriftsartikel"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standarddokument"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Magasin"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Tegneserie"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Noder"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Lydbog"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Andet"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Partner Server download"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Ekstern download"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Ekstern lån"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Ekstern lån (print handicappet)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Udforsk metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Indeholdt i torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Kinesisk"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads til AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tjekkiske metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Bøger"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russisk Statsbibliotek"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titel"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Forfatter"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Forlag"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Udgave"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Udgivelsesår"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Original filnavn"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Beskrivelse og metadata kommentarer"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partner Server-downloads midlertidigt ikke tilgængelige for denne fil."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Hurtig Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(anbefalet)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(ingen browserverifikation eller ventelister)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Langsom Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(en smule hurtigere, men med venteliste)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(ingen venteliste, men kan være meget langsom)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Skønlitteratur"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klik også på “GET” øverst)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klik på “GET” øverst)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "deres annoncer er kendt for at indeholde skadelig software, så brug en annonceblokker eller klik ikke på annoncer"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC-filer kan være upålidelige at downloade)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library på Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(kræver Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Lån fra Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(kun for print-handicappede)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(tilknyttet DOI er muligvis ikke tilgængelig i Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "samling"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Bulk torrent downloads"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(kun for eksperter)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Søg i Anna’s Arkiv efter ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Søg i forskellige andre databaser efter ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Find originalposten i ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Søg i Anna’s Arkiv efter Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Find originalposten i Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Søg i Anna’s Arkiv efter OCLC (WorldCat) nummer"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Find originalposten i WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Søg i Anna’s Arkiv efter DuXiu SSID nummer"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Søg manuelt på DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Søg i Anna’s Arkiv efter CADAL SSNO nummer"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Find originalposten i CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Søg i Anna’s Arkiv efter DuXiu DXID nummer"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Arkiv 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(ingen browserverifikation påkrævet)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tjekkisk metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Bøger %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "beskrivelse"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternativ filnavn"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternativ titel"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternativ forfatter"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternativ udgiver"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternativ udgave"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternativ filtype"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata kommentarer"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternativ beskrivelse"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "dato open sourced"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub fil “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending fil “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dette er en registrering af en fil fra Internet Archive, ikke en direkte downloadbar fil. Du kan prøve at låne bogen (link nedenfor), eller bruge denne URL, når du <a %(a_request)s>anmoder om en fil</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Hvis du har denne fil og den endnu ikke er tilgængelig i Anna’s Arkiv, overvej at <a %(a_request)s>uploade den</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadataregistrering"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadataregistrering"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nummer %(id)s metadataregistrering"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadataregistrering"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadataregistrering"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dette er en metadata-post, ikke en fil, der kan downloades. Du kan bruge denne URL, når du <a %(a_request)s>anmoder om en fil</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata fra tilknyttet post"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Forbedr metadata på Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Advarsel: flere tilknyttede poster:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Forbedr metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Rapportér filkvalitet"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Downloadtid"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Hjemmeside:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Søg i Annas Arkiv efter “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Koder Udforsker:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vis i Koder Udforsker “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Læs mere…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Lån (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Udforsk metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Kommentarer (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Lister (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistikker (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Tekniske detaljer"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Denne fil kan have problemer og er blevet skjult fra et kildebibliotek.</span> Nogle gange er det på anmodning af en ophavsretshaver, andre gange fordi der er et bedre alternativ tilgængeligt, men nogle gange er det på grund af et problem med selve filen. Det kan stadig være fint at downloade, men vi anbefaler først at søge efter en alternativ fil. Flere detaljer:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "En bedre version af denne fil kan være tilgængelig på %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Hvis du stadig vil downloade denne fil, skal du sørge for kun at bruge betroet, opdateret software til at åbne den."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Hurtige downloads"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Hurtige downloads</strong> Bliv <a %(a_membership)s>medlem</a> for at støtte den langsigtede bevaring af bøger, artikler og mere. For at vise vores taknemmelighed for din støtte, får du hurtige downloads. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Hvis du donerer denne måned, får du <strong>dobbelt</strong> så mange hurtige downloads."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Hurtige downloads</strong> Du har %(remaining)s tilbage i dag. Tak fordi du er medlem! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Hurtige downloads</strong> Du har opbrugt dine hurtige downloads for i dag."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Hurtige downloads</strong> Du har downloadet denne fil for nylig. Links forbliver gyldige i et stykke tid."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Option #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(ingen omdirigering)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(åbn i fremviser)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Henvis en ven, og både du og din ven får %(percentage)s%% bonus hurtige downloads!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Lær mere…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Langsomme downloads"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Fra betroede partnere."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mere information i <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kan kræve <a %(a_browser)s>browserverifikation</a> — ubegrænsede downloads!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Efter download:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Åbn i vores fremviser"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "vis eksterne downloads"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Eksterne downloads"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Ingen downloads fundet."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Alle downloadmuligheder har den samme fil og bør være sikre at bruge. Når det er sagt, vær altid forsigtig, når du downloader filer fra internettet, især fra eksterne sider til Anna’s Arkiv. For eksempel, sørg for at holde dine enheder opdaterede."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "For store filer anbefaler vi at bruge en download manager for at undgå afbrydelser."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Anbefalede download managers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Du skal bruge en e-bog eller PDF-læser for at åbne filen, afhængigt af filformatet."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Anbefalede e-bogslæsere: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Annas Arkiv online fremviser"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Brug onlineværktøjer til at konvertere mellem formater."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Anbefalede konverteringsværktøjer: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Du kan sende både PDF- og EPUB-filer til din Kindle eller Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Anbefalede værktøjer: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazons “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz’ “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Støt forfattere og biblioteker"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Hvis du kan lide dette og har råd til det, overvej at købe originalen eller støtte forfatterne direkte."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Hvis dette er tilgængeligt på dit lokale bibliotek, overvej at låne det gratis der."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Filkvalitet"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Hjælp fællesskabet ved at rapportere kvaliteten af denne fil! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Rapportér filproblem (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Fremragende filkvalitet (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Tilføj kommentar (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Hvad er der galt med denne fil?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Venligst brug <a %(a_copyright)s>DMCA / Copyright-krav formularen</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Beskriv problemet (påkrævet)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Problembeskrivelse"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 af en bedre version af denne fil (hvis relevant)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Udfyld dette, hvis der er en anden fil, der tæt matcher denne fil (samme udgave, samme filtype, hvis du kan finde en), som folk bør bruge i stedet for denne fil. Hvis du kender til en bedre version af denne fil uden for Anna’s Arkiv, så <a %(a_upload)s>upload den</a> venligst."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Du kan få md5 fra URL'en, f.eks."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Indsend rapport"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lær hvordan du selv kan <a %(a_metadata)s>forbedre metadataen</a> for denne fil."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Tak for din indsendelse af rapporten. Den vil blive vist på denne side og manuelt gennemgået af Anna (indtil vi har et ordentligt moderationssystem)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Noget gik galt. Genindlæs siden og prøv igen."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Hvis denne fil har høj kvalitet, kan du diskutere alt om den her! Hvis ikke, brug venligst knappen \"Rapportér filproblem\"."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Jeg elskede denne bog!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Efterlad kommentar"

#, fuzzy
msgid "common.english_only"
msgstr "Teksten nedenfor fortsætter på engelsk."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Samlede downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "En \"fil MD5\" er en hash, der beregnes ud fra filens indhold og er rimelig unik baseret på det indhold. Alle skyggebiblioteker, som vi har indekseret her, bruger primært MD5'er til at identificere filer."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "En fil kan optræde i flere skyggebiblioteker. For information om de forskellige datasets, vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dette er en fil administreret af <a %(a_ia)s>IA's Controlled Digital Lending</a>-bibliotek og indekseret af Annas Arkiv til søgning. For information om de forskellige datasets, vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "For information om denne specifikke fil, tjek dens <a %(a_href)s>JSON-fil</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problem med at indlæse denne side"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Opdater for at prøve igen. <a %(a_contact)s>Kontakt os</a> hvis problemet fortsætter i flere timer."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Ikke fundet"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” blev ikke fundet i vores database."

#, fuzzy
msgid "page.login.title"
msgstr "Log ind / Registrer"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Browserverifikation"

#, fuzzy
msgid "page.login.text1"
msgstr "For at forhindre spam-bots i at oprette mange konti, skal vi først verificere din browser."

#, fuzzy
msgid "page.login.text2"
msgstr "Hvis du sidder fast i en uendelig løkke, anbefaler vi at installere <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Det kan også hjælpe at slå annonceblokkere og andre browserudvidelser fra."

#, fuzzy
msgid "page.codes.title"
msgstr "Koder"

#, fuzzy
msgid "page.codes.heading"
msgstr "Kodeudforsker"

#, fuzzy
msgid "page.codes.intro"
msgstr "Udforsk de koder, som poster er tagget med, efter præfiks. Kolonnen “poster” viser antallet af poster tagget med koder med det givne præfiks, som set i søgemaskinen (inklusive metadata-only poster). Kolonnen “koder” viser, hvor mange faktiske koder der har et givet præfiks."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Denne side kan tage et stykke tid at generere, hvilket er grunden til, at den kræver en Cloudflare captcha. <a %(a_donate)s>Medlemmer</a> kan springe captchaen over."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Venligst skrab ikke disse sider. I stedet anbefaler vi <a %(a_import)s>generering</a> eller <a %(a_download)s>download</a> af vores ElasticSearch og MariaDB databaser og kørsel af vores <a %(a_software)s>open source kode</a>. De rå data kan manuelt udforskes gennem JSON-filer som <a %(a_json_file)s>denne</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Præfiks"

#, fuzzy
msgid "common.form.go"
msgstr "Gå"

#, fuzzy
msgid "common.form.reset"
msgstr "Nulstil"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Søg i Annas Arkiv"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Advarsel: koden har forkerte Unicode-tegn og kan opføre sig forkert i forskellige situationer. Den rå binærkode kan dekodes fra base64-repræsentationen i URL'en."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Kendt kodepræfiks “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Præfiks"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiket"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Beskrivelse"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL til en specifik kode"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” vil blive erstattet med kodens værdi"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generisk URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Hjemmeside"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s post matcher “%(prefix_label)s”"
msgstr[1] "%(count)s poster matcher “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL til specifik kode: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mere…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Koder der starter med “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks over"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "poster"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "koder"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Færre end %(count)s poster"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "For DMCA / ophavsretskrav, brug <a %(a_copyright)s>denne formular</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Enhver anden måde at kontakte os om ophavsretskrav vil automatisk blive slettet."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Vi byder meget velkommen til din feedback og dine spørgsmål!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Men på grund af mængden af spam og nonsens-e-mails, vi modtager, bedes du markere felterne for at bekræfte, at du forstår disse betingelser for at kontakte os."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Ophavsretskrav til denne e-mail vil blive ignoreret; brug i stedet formularen."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner-servere er utilgængelige på grund af hosting-lukninger. De burde være oppe igen snart."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Medlemskaber vil blive forlænget tilsvarende."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Send ikke e-mails til os for at <a %(a_request)s>anmode om bøger</a><br>eller små (<10k) <a %(a_upload)s>uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Når du stiller spørgsmål om konto eller donation, tilføj dit kontonummer, skærmbilleder, kvitteringer, så meget information som muligt. Vi tjekker kun vores e-mail hver 1-2 uge, så hvis du ikke inkluderer disse oplysninger, vil det forsinke enhver løsning."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Vis e-mail"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Formular til ophavsretskrav"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Hvis du har et DCMA- eller andet ophavsretskrav, bedes du udfylde denne formular så præcist som muligt. Hvis du støder på problemer, bedes du kontakte os på vores dedikerede DMCA-adresse: %(email)s. Bemærk, at krav sendt til denne adresse ikke vil blive behandlet, den er kun til spørgsmål. Brug venligst formularen nedenfor til at indsende dine krav."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL'er på Annas Arkiv (påkrævet). En per linje. Inkluder venligst kun URL'er, der beskriver præcis samme udgave af en bog. Hvis du vil fremsætte et krav for flere bøger eller flere udgaver, bedes du indsende denne formular flere gange."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Krav, der samler flere bøger eller udgaver sammen, vil blive afvist."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Dit navn (påkrævet)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adresse (påkrævet)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefonnummer (påkrævet)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (påkrævet)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Klar beskrivelse af kildematerialet (påkrævet)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN'er på kildematerialet (hvis relevant). En per linje. Inkluder venligst kun dem, der præcist matcher den udgave, som du rapporterer et ophavsretskrav for."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL'er på kildematerialet, en per linje. Tag venligst et øjeblik til at søge i Open Library efter dit kildemateriale. Dette vil hjælpe os med at verificere dit krav."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL'er til kildematerialet, en per linje (påkrævet). Inkluder venligst så mange som muligt for at hjælpe os med at verificere dit krav (f.eks. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Erklæring og underskrift (påkrævet)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Indsend krav"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Tak for din indsendelse af ophavsretskravet. Vi vil gennemgå det så hurtigt som muligt. Genindlæs venligst siden for at indsende et nyt krav."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Noget gik galt. Genindlæs venligst siden og prøv igen."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Hvis du er interesseret i at spejle dette datasæt til <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-træning</a>, bedes du kontakte os."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Vores mission er at arkivere alle bøger i verden (samt artikler, magasiner osv.) og gøre dem bredt tilgængelige. Vi mener, at alle bøger bør spejles vidt og bredt for at sikre redundans og modstandsdygtighed. Derfor samler vi filer fra en række forskellige kilder. Nogle kilder er helt åbne og kan spejles i bulk (såsom Sci-Hub). Andre er lukkede og beskyttende, så vi forsøger at skrabe dem for at \"befri\" deres bøger. Andre falder et sted midt imellem."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Alle vores data kan <a %(a_torrents)s>torrentes</a>, og al vores metadata kan <a %(a_anna_software)s>genereres</a> eller <a %(a_elasticsearch)s>downloades</a> som ElasticSearch- og MariaDB-databaser. De rå data kan manuelt udforskes gennem JSON-filer som <a %(a_dbrecord)s>denne</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Oversigt"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Nedenfor er en hurtig oversigt over kilderne til filerne på Annas Arkiv."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Kilde"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Størrelse"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% spejlet af AA / torrents tilgængelige"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Procentdel af antal filer"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Sidst opdateret"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction og Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s fil"
msgstr[1] "%(count)s filer"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: frosset siden 2021; det meste tilgængeligt via torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: mindre tilføjelser siden da</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Eksklusive “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fiktion torrents er bagud (selvom ID'er ~4-6M ikke er torrentet, da de overlapper med vores Zlib torrents)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Den “kinesiske” samling i Z-Library ser ud til at være den samme som vores DuXiu-samling, men med forskellige MD5'er. Vi ekskluderer disse filer fra torrents for at undgå duplikering, men viser dem stadig i vores søgeindeks."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrolleret Digital Udlån"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ af filer er søgbare."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Ekskluderer duplikater"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Da skyggebibliotekerne ofte synkroniserer data fra hinanden, er der betydelig overlapning mellem bibliotekerne. Derfor stemmer tallene ikke overens med totalen."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Procentdelen “spejlet og seedet af Anna’s Arkiv” viser, hvor mange filer vi selv spejler. Vi seeder disse filer i bulk gennem torrents og gør dem tilgængelige for direkte download gennem partnerwebsteder."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Kildebiblioteker"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Nogle kildelibraries fremmer deling af deres data i bulk gennem torrents, mens andre ikke deler deres samling så let. I sidstnævnte tilfælde forsøger Anna’s Arkiv at skrabe deres samlinger og gøre dem tilgængelige (se vores <a %(a_torrents)s>Torrents</a> side). Der er også mellemliggende situationer, for eksempel hvor kildelibraries er villige til at dele, men ikke har ressourcerne til det. I disse tilfælde forsøger vi også at hjælpe."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Nedenfor er en oversigt over, hvordan vi interagerer med de forskellige kildelibraries."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Kilde"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Filer"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Daglige <a %(dbdumps)s>HTTP-database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatiserede torrents for <a %(nonfiction)s>Non-Fiction</a> og <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(covers)s>bogomslag torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub har frosset nye filer siden 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata dumps tilgængelige <a %(scihub1)s>her</a> og <a %(scihub2)s>her</a>, samt som en del af <a %(libgenli)s>Libgen.li-databasen</a> (som vi bruger)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Data torrents tilgængelige <a %(scihub1)s>her</a>, <a %(scihub2)s>her</a>, og <a %(libgenli)s>her</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Nogle nye filer <a %(libgenrs)s>bliver</a> <a %(libgenli)s>tilføjet</a> til Libgens \"scimag\", men ikke nok til at retfærdiggøre nye torrenter"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kvartalsvise <a %(dbdumps)s>HTTP-database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiction torrenter deles med Libgen.rs (og spejles <a %(libgenli)s>her</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annas Arkiv og Libgen.li administrerer i fællesskab samlinger af <a %(comics)s>tegneserier</a>, <a %(magazines)s>magasiner</a>, <a %(standarts)s>standarddokumenter</a> og <a %(fiction)s>fiktion (afviget fra Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Deres “fiction_rus” samling (russisk fiktion) har ingen dedikerede torrents, men er dækket af torrents fra andre, og vi holder et <a %(fiction_rus)s>spejl</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annas Arkiv og Z-Library administrerer i fællesskab en samling af <a %(metadata)s>Z-Library metadata</a> og <a %(files)s>Z-Library filer</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Nogle metadata tilgængelige gennem <a %(openlib)s>Open Library database dumps</a>, men de dækker ikke hele IA-samlingen"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Ingen let tilgængelige metadata dumps tilgængelige for deres samlede samling"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Filer kun tilgængelige for udlån på begrænset basis, med forskellige adgangsbegrænsninger"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(ia)s>IA-filer</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Forskellige metadata-databaser spredt rundt på det kinesiske internet; dog ofte betalte databaser"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Ingen let tilgængelige metadata-dumps tilgængelige for deres samlede samling."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(duxiu)s>DuXiu-metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Forskellige fil-databaser spredt rundt på det kinesiske internet; dog ofte betalte databaser"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s De fleste filer er kun tilgængelige med premium BaiduYun-konti; langsomme downloadhastigheder."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(duxiu)s>DuXiu-filer</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Forskellige mindre eller enkeltstående kilder. Vi opfordrer folk til at uploade til andre skyggebiblioteker først, men nogle gange har folk samlinger, der er for store til, at andre kan sortere dem, men ikke store nok til at berettige deres egen kategori."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Kun metadata-kilder"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Vi beriger også vores samling med kun metadata-kilder, som vi kan matche til filer, f.eks. ved hjælp af ISBN-numre eller andre felter. Nedenfor er en oversigt over disse. Igen, nogle af disse kilder er helt åbne, mens vi for andre er nødt til at skrabe dem."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Vores inspiration til at samle metadata er Aaron Swartz' mål om “en webside for hver bog, der nogensinde er udgivet”, for hvilket han skabte <a %(a_openlib)s>Open Library</a>. Det projekt har klaret sig godt, men vores unikke position giver os mulighed for at få metadata, som de ikke kan. En anden inspiration var vores ønske om at vide <a %(a_blog)s>hvor mange bøger der er i verden</a>, så vi kan beregne, hvor mange bøger vi stadig har tilbage at redde."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Bemærk, at i metadata-søgning viser vi de originale poster. Vi foretager ingen sammensmeltning af poster."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Sidst opdateret"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Månedlige <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Ikke tilgængelig direkte i bulk, beskyttet mod scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Forenet database"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Vi kombinerer alle ovenstående kilder til én forenet database, som vi bruger til at betjene denne hjemmeside. Denne forenede database er ikke direkte tilgængelig, men da Anna’s Arkiv er fuldt open source, kan den ret nemt <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>downloades</a> som ElasticSearch og MariaDB databaser. Scripts på den side vil automatisk downloade alle nødvendige metadata fra de ovennævnte kilder."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Hvis du gerne vil udforske vores data, før du kører disse scripts lokalt, kan du se på vores JSON-filer, som linker videre til andre JSON-filer. <a %(a_json)s>Denne fil</a> er et godt udgangspunkt."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Tilpasset fra vores <a %(a_href)s>blogindlæg</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> er en enorm database med scannede bøger, skabt af <a %(superstar_link)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøger, scannet for at gøre dem digitalt tilgængelige for universiteter og biblioteker. For vores engelsktalende publikum har <a %(princeton_link)s>Princeton</a> og <a %(uw_link)s>University of Washington</a> gode oversigter. Der er også en fremragende artikel, der giver mere baggrund: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Bøgerne fra Duxiu er længe blevet piratkopieret på det kinesiske internet. Normalt bliver de solgt for mindre end en dollar af forhandlere. De distribueres typisk ved hjælp af den kinesiske ækvivalent til Google Drive, som ofte er blevet hacket for at tillade mere lagerplads. Nogle tekniske detaljer kan findes <a %(link1)s>her</a> og <a %(link2)s>her</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Selvom bøgerne er blevet semi-offentligt distribueret, er det ret svært at få dem i bulk. Vi havde dette højt på vores TODO-liste og afsatte flere måneder af fuldtidsarbejde til det. Men i slutningen af 2023 kontaktede en utrolig, fantastisk og talentfuld frivillig os og fortalte, at de allerede havde udført alt dette arbejde — til stor udgift. De delte hele samlingen med os uden at forvente noget til gengæld, bortset fra garantien om langsigtet bevaring. Virkelig bemærkelsesværdigt."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Ressourcer"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Samlede filer: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Samlet filstørrelse: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Filer spejlet af Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Sidst opdateret: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrenter af Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Eksempelpost på Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Vores blogindlæg om disse data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts til import af metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers-format"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mere information fra vores frivillige (rå noter):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Kontrolleret Digital Udlån"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dette datasæt er tæt forbundet med <a %(a_datasets_openlib)s>Open Library-datasættet</a>. Det indeholder en scraping af alle metadata og en stor del af filer fra IA's Controlled Digital Lending Library. Opdateringer udgives i <a %(a_aac)s>Annas Arkiv Containers-format</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Disse poster henvises direkte fra Open Library-datasættet, men indeholder også poster, der ikke er i Open Library. Vi har også en række datafiler, der er skrabet af fællesskabsmedlemmer gennem årene."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Samlingen består af to dele. Du skal bruge begge dele for at få alle data (undtagen forældede torrents, som er krydset ud på torrentsiden)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "vores første udgivelse, før vi standardiserede på <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Indeholder metadata (som json og xml), pdf'er (fra acsm og lcpdf digitale udlånsystemer) og omslagsminiaturer."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementelle nye udgivelser, der bruger AAC. Indeholder kun metadata med tidsstempler efter 2023-01-01, da resten allerede er dækket af “ia”. Også alle pdf-filer, denne gang fra acsm og “bookreader” (IA's web-læser) udlånsystemer. På trods af at navnet ikke er helt korrekt, placerer vi stadig bookreader-filer i ia2_acsmpdf_files-samlingen, da de er gensidigt udelukkende."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Hoved %(source)s hjemmeside"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitalt Udlånsbibliotek"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata-dokumentation (de fleste felter)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN landinformation"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Den Internationale ISBN Agentur udgiver regelmæssigt de intervaller, som den har tildelt nationale ISBN agenturer. Ud fra dette kan vi udlede, hvilket land, region eller sproggruppe dette ISBN tilhører. Vi bruger i øjeblikket disse data indirekte gennem <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Ressourcer"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Sidst opdateret: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN hjemmeside"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "For baggrundshistorien om de forskellige Library Genesis forks, se siden for <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li indeholder det meste af det samme indhold og metadata som Libgen.rs, men har nogle samlinger oveni, nemlig tegneserier, magasiner og standarddokumenter. Det har også integreret <a %(a_scihub)s>Sci-Hub</a> i sin metadata og søgemaskine, hvilket er det, vi bruger til vores database."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadataene for dette bibliotek er frit tilgængelige <a %(a_libgen_li)s>på libgen.li</a>. Dog er denne server langsom og understøtter ikke genoptagelse af afbrudte forbindelser. De samme filer er også tilgængelige på <a %(a_ftp)s>en FTP-server</a>, som fungerer bedre."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents er tilgængelige for det meste af det ekstra indhold, især torrents for tegneserier, magasiner og standarddokumenter er blevet udgivet i samarbejde med Annas Arkiv."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Fiktionssamlingen har sine egne torrents (afvigende fra <a %(a_href)s>Libgen.rs</a>) startende ved %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Ifølge Libgen.li-administratoren bør “fiction_rus” (russisk fiktion) samlingen være dækket af regelmæssigt udgivne torrents fra <a %(a_booktracker)s>booktracker.org</a>, især <a %(a_flibusta)s>flibusta</a> og <a %(a_librusec)s>lib.rus.ec</a> torrents (som vi spejler <a %(a_torrents)s>her</a>, selvom vi endnu ikke har fastslået, hvilke torrents der svarer til hvilke filer)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistikker for alle samlinger kan findes <a %(a_href)s>på libgens hjemmeside</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Ikke-fiktion ser også ud til at have divergeret, men uden nye torrents. Det ser ud til, at dette er sket siden begyndelsen af 2022, selvom vi ikke har verificeret det."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Visse områder uden torrents (såsom fiktionsområder f_3463000 til f_4260000) er sandsynligvis Z-Library (eller andre duplikat) filer, selvom vi måske ønsker at foretage en deduplikation og lave torrents for lgli-unikke filer i disse områder."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Bemærk, at torrentfilerne, der henviser til “libgen.is”, eksplicit er spejle af <a %(a_libgen)s>Libgen.rs</a> (“.is” er et andet domæne, der bruges af Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "En nyttig ressource til brug af metadata er <a %(a_href)s>denne side</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiktionstorrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Tegneserietorrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Magasintorrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standarddokument torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russiske fiktions torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata feltinformation"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Spejl af andre torrents (og unikke fiktion- og tegneserietorrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Vores blogindlæg om udgivelsen af tegneserier"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Den korte historie om de forskellige Library Genesis (eller “Libgen”) forgreninger er, at over tid havde de forskellige personer involveret i Library Genesis en uoverensstemmelse og gik hver til sit."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "“.fun”-versionen blev skabt af den oprindelige grundlægger. Den bliver fornyet til fordel for en ny, mere distribueret version."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "“.rs”-versionen har meget lignende data og udgiver konsekvent deres samling i bulk torrents. Den er groft opdelt i en “fiktion” og en “non-fiktion” sektion."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oprindeligt på “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>“.li”-versionen</a> har en enorm samling af tegneserier samt andet indhold, der (endnu) ikke er tilgængeligt for bulk download gennem torrents. Den har en separat torrent-samling af fiktionsbøger, og den indeholder metadata fra <a %(a_scihub)s>Sci-Hub</a> i sin database."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Ifølge dette <a %(a_mhut)s>forumpost</a> blev Libgen.li oprindeligt hostet på “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> er på en måde også en forgrening af Library Genesis, selvom de brugte et andet navn til deres projekt."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Denne side handler om “.rs”-versionen. Den er kendt for konsekvent at offentliggøre både sine metadata og det fulde indhold af sin bogkatalog. Dens bogsamling er opdelt mellem en fiktion- og non-fiktion-del."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "En nyttig ressource til brug af metadata er <a %(a_metadata)s>denne side</a> (blokerer IP-intervaller, VPN kan være påkrævet)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Fra marts 2024 bliver nye torrents postet i <a %(a_href)s>denne forumtråd</a> (blokerer IP-intervaller, VPN kan være påkrævet)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-fiktion torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiktion torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata feltinformation"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-fiktion torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiktion torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents af Annas Arkiv (bogomslag)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Vores blog om udgivelsen af bogomslag"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis er kendt for allerede generøst at gøre deres data tilgængelige i bulk gennem torrents. Vores Libgen-samling består af hjælpeoplysninger, som de ikke frigiver direkte, i partnerskab med dem. Mange tak til alle involverede i Library Genesis for at arbejde sammen med os!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Udgivelse 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Denne <a %(blog_post)s>første udgivelse</a> er ret lille: omkring 300GB af bogomslag fra Libgen.rs forken, både fiktion og non-fiktion. De er organiseret på samme måde som de vises på libgen.rs, f.eks.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s for en non-fiktion bog."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s for en fiktion bog."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Ligesom med Z-Library samlingen, har vi lagt dem alle i en stor .tar-fil, som kan monteres ved hjælp af <a %(a_ratarmount)s>ratarmount</a>, hvis du vil servere filerne direkte."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> er en proprietær database af den non-profit organisation <a %(a_oclc)s>OCLC</a>, som samler metadataoptegnelser fra biblioteker over hele verden. Det er sandsynligvis den største biblioteksmetadata-samling i verden."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, første udgivelse:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "I oktober 2023 <a %(a_scrape)s>udgav</a> vi en omfattende scraping af OCLC (WorldCat) databasen, i <a %(a_aac)s>Annas Arkiv Containers format</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents af Annas Arkiv"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Vores blogindlæg om disse data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library er et open source-projekt af Internet Archive til at katalogisere hver bog i verden. Det har en af verdens største bogscanningsoperationer og har mange bøger tilgængelige for digital udlån. Dets bogmetadata-katalog er frit tilgængeligt for download og er inkluderet på Annas Arkiv (dog ikke i øjeblikket i søgning, medmindre du eksplicit søger efter et Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Udgivelse 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dette er en dump af mange opkald til isbndb.com i løbet af september 2022. Vi forsøgte at dække alle ISBN-intervaller. Disse er omkring 30,9 millioner poster. På deres hjemmeside hævder de, at de faktisk har 32,6 millioner poster, så vi kan på en eller anden måde have misset nogle, eller <em>de</em> kan gøre noget forkert."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON-svarene er stort set rå fra deres server. Et datakvalitetsproblem, som vi bemærkede, er, at for ISBN-13-numre, der starter med et andet præfiks end \"978-\", inkluderer de stadig et \"isbn\"-felt, der simpelthen er ISBN-13-nummeret med de første 3 tal fjernet (og kontrolcifret genberegnet). Dette er åbenlyst forkert, men det er sådan, de ser ud til at gøre det, så vi ændrede det ikke."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Et andet potentielt problem, du kan støde på, er, at \"isbn13\"-feltet har dubletter, så du kan ikke bruge det som en primær nøgle i en database. \"isbn13\"+\"isbn\"-felter kombineret ser dog ud til at være unikke."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "For en baggrund om Sci-Hub, henvises til dens <a %(a_scihub)s>officielle hjemmeside</a>, <a %(a_wikipedia)s>Wikipedia-side</a> og dette <a %(a_radiolab)s>podcast-interview</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Bemærk, at Sci-Hub har været <a %(a_reddit)s>frosset siden 2021</a>. Det var frosset før, men i 2021 blev der tilføjet et par millioner artikler. Stadig bliver et begrænset antal artikler tilføjet til Libgen “scimag” samlinger, dog ikke nok til at berettige nye bulk torrents."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Vi bruger Sci-Hub metadata som leveret af <a %(a_libgen_li)s>Libgen.li</a> i dens “scimag” samling. Vi bruger også <a %(a_dois)s>dois-2022-02-12.7z</a> datasættet."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Bemærk, at “smarch” torrents er <a %(a_smarch)s>udgået</a> og derfor ikke inkluderet i vores torrents liste."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata og torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents på Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents på Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Opdateringer på Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-side"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast-interview"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uploads til Annas Arkiv"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Oversigt fra <a %(a1)s>Datasets-side</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Forskellige mindre eller enkeltstående kilder. Vi opfordrer folk til at uploade til andre skyggebiblioteker først, men nogle gange har folk samlinger, der er for store til, at andre kan sortere dem, men ikke store nok til at berettige deres egen kategori."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "\"Upload\"-samlingen er opdelt i mindre underkollektioner, som er angivet i AACIDs og torrent-navne. Alle underkollektioner blev først deduplikeret mod hovedsamlingen, selvom metadata \"upload_records\" JSON-filer stadig indeholder mange referencer til de originale filer. Ikke-bogfiler blev også fjernet fra de fleste underkollektioner og er typisk <em>ikke</em> noteret i \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Mange underkollektioner består selv af under-underkollektioner (f.eks. fra forskellige originale kilder), som er repræsenteret som mapper i “filepath”-felterne."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Underkollektionerne er:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Underkollektion"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Noter"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "gennemse"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "søg"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Fra <a %(a_href)s>aaaaarg.fail</a>. Ser ud til at være ret komplet. Fra vores frivillige “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Fra en <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ret høj overlap med eksisterende papers-samlinger, men meget få MD5-matches, så vi besluttede at beholde den fuldstændigt."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape af <q>iRead eBooks</q> (= fonetisk <q>ai rit i-books</q>; airitibooks.com), af frivillig <q>j</q>. Svarer til <q>airitibooks</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Fra en samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvist fra den oprindelige kilde, delvist fra the-eye.eu, delvist fra andre spejle."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Fra en privat bøger torrent hjemmeside, <a %(a_href)s>Bibliotik</a> (ofte omtalt som “Bib”), hvor bøger blev samlet i torrents efter navn (A.torrent, B.torrent) og distribueret gennem the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Fra vores frivillige “bpb9v”. For mere information om <a %(a_href)s>CADAL</a>, se noterne på vores <a %(a_duxiu)s>DuXiu dataset side</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mere fra vores frivillige “bpb9v”, mest DuXiu-filer, samt en mappe “WenQu” og “SuperStar_Journals” (SuperStar er firmaet bag DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Fra vores frivillige “cgiym”, kinesiske tekster fra forskellige kilder (repræsenteret som undermapper), herunder fra <a %(a_href)s>China Machine Press</a> (en stor kinesisk udgiver)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Ikke-kinesiske samlinger (repræsenteret som undermapper) fra vores frivillige “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape af bøger om kinesisk arkitektur, af frivillig <q>cm</q>: <q>Jeg fik det ved at udnytte en netværkssårbarhed hos forlaget, men den smuthul er siden blevet lukket</q>. Svarer til <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Bøger fra det akademiske forlag <a %(a_href)s>De Gruyter</a>, samlet fra nogle få store torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape af <a %(a_href)s>docer.pl</a>, en polsk fil-deling hjemmeside fokuseret på bøger og andre skriftlige værker. Scraped i slutningen af 2023 af frivillig “p”. Vi har ikke god metadata fra den oprindelige hjemmeside (ikke engang filendelser), men vi filtrerede for bog-lignende filer og var ofte i stand til at udtrække metadata fra filerne selv."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direkte fra DuXiu, samlet af frivillig “w”. Kun nyere DuXiu-bøger er tilgængelige direkte gennem e-bøger, så de fleste af disse må være nyere."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Resterende DuXiu-filer fra frivillig “m”, som ikke var i DuXius proprietære PDG-format (det primære <a %(a_href)s>DuXiu dataset</a>). Samlet fra mange originale kilder, desværre uden at bevare disse kilder i filstien."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape af erotiske bøger, af frivillig <q>do no harm</q>. Svarer til <q>hentai</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Samling skrabet fra en japansk Manga-udgiver af frivillig “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Udvalgte retsarkiver fra Longquan</a>, leveret af frivillig “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape af <a %(a_href)s>magzdb.org</a>, en allieret af Library Genesis (det er linket på libgen.rs hjemmesiden), men som ikke ønskede at levere deres filer direkte. Opnået af frivillig “p” i slutningen af 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Forskellige små uploads, for små til at være deres egen undersamling, men repræsenteret som mapper."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-bøger fra AvaxHome, en russisk fildelingsside."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arkiv af aviser og magasiner. Svarer til <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape af <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Samling af frivillig “o” som samlede polske bøger direkte fra originale udgivelses (“scene”) hjemmesider."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Kombinerede samlinger af <a %(a_href)s>shuge.org</a> af frivillige “cgiym” og “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (opkaldt efter det fiktive bibliotek), scraped i 2022 af frivillig “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Under-under-samlinger (repræsenteret som mapper) fra frivillig “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (af <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mit lille bogrum — woz9ts: “Denne side fokuserer hovedsageligt på at dele høj kvalitet e-bogsfiler, hvoraf nogle er sat op af ejeren selv. Ejeren blev <a %(a_arrested)s>arresteret</a> i 2019, og nogen lavede en samling af de filer, han delte.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Resterende DuXiu-filer fra frivillig “woz9ts”, som ikke var i DuXiu's proprietære PDG-format (skal stadig konverteres til PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents af Annas Arkiv"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library har sine rødder i <a %(a_href)s>Library Genesis</a>-fællesskabet og blev oprindeligt startet med deres data. Siden da har det professionaliseret sig betydeligt og har en meget mere moderne grænseflade. De er derfor i stand til at få mange flere donationer, både økonomisk for at fortsætte med at forbedre deres hjemmeside, samt donationer af nye bøger. De har samlet en stor samling ud over Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Opdatering pr. februar 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "I slutningen af 2022 blev de påståede grundlæggere af Z-Library arresteret, og domæner blev beslaglagt af amerikanske myndigheder. Siden da har hjemmesiden langsomt fundet vej online igen. Det er ukendt, hvem der i øjeblikket driver den."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Samlingen består af tre dele. De originale beskrivelsessider for de første to dele er bevaret nedenfor. Du har brug for alle tre dele for at få alle data (undtagen forældede torrents, som er streget ud på torrentsiden)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: vores første udgivelse. Dette var den allerførste udgivelse af det, der dengang blev kaldt “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: anden udgivelse, denne gang med alle filer pakket ind i .tar-filer."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementelle nye udgivelser, ved hjælp af <a %(a_href)s>Annas Arkiv Beholdere (AAC) format</a>, nu udgivet i samarbejde med Z-Library teamet."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenter af Annas Arkiv (metadata + indhold)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Eksempelpost i Annas Arkiv (original samling)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Eksempelpost i Annas Arkiv (“zlib3” samling)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Hovedwebsted"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domæne"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blogindlæg om Udgivelse 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blogindlæg om Udgivelse 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-udgivelser (originale beskrivelsessider)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Udgivelse 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Det første spejl blev møjsommeligt opnået i løbet af 2021 og 2022. På nuværende tidspunkt er det en smule forældet: det afspejler samlingens tilstand i juni 2021. Vi vil opdatere dette i fremtiden. Lige nu fokuserer vi på at få denne første udgivelse ud."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Da Library Genesis allerede er bevaret med offentlige torrents og er inkluderet i Z-Library, foretog vi en grundlæggende deduplikation mod Library Genesis i juni 2022. Til dette brugte vi MD5-hashes. Der er sandsynligvis meget mere duplikeret indhold i biblioteket, såsom flere filformater med den samme bog. Dette er svært at opdage præcist, så det gør vi ikke. Efter deduplikationen har vi over 2 millioner filer tilbage, i alt lige under 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Samlingen består af to dele: en MySQL “.sql.gz” dump af metadataene og de 72 torrentfiler på omkring 50-100GB hver. Metadataene indeholder dataene som rapporteret af Z-Library-webstedet (titel, forfatter, beskrivelse, filtype), samt den faktiske filstørrelse og md5sum, som vi observerede, da disse nogle gange ikke stemmer overens. Der ser ud til at være områder af filer, hvor Z-Library selv har forkerte metadata. Vi kan også have downloadet filer forkert i nogle isolerede tilfælde, som vi vil forsøge at opdage og rette i fremtiden."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "De store torrentfiler indeholder de faktiske bogdata, med Z-Library ID som filnavn. Filendelserne kan rekonstrueres ved hjælp af metadata-dumpen."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Samlingen er en blanding af faglitteratur og skønlitteratur (ikke adskilt som i Library Genesis). Kvaliteten varierer også meget."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Denne første udgivelse er nu fuldt tilgængelig. Bemærk, at torrentfilerne kun er tilgængelige gennem vores Tor-spejl."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Udgivelse 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Vi har fået alle bøger, der blev tilføjet til Z-Library mellem vores sidste spejl og august 2022. Vi har også gået tilbage og skrabet nogle bøger, som vi missede første gang. Alt i alt er denne nye samling omkring 24TB. Igen er denne samling deduplikeret mod Library Genesis, da der allerede er torrents tilgængelige for den samling."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Dataene er organiseret på samme måde som den første udgivelse. Der er en MySQL “.sql.gz” dump af metadataene, som også inkluderer alle metadata fra den første udgivelse, og dermed erstatter den. Vi har også tilføjet nogle nye kolonner:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: om denne fil allerede er i Library Genesis, enten i faglitteratur- eller skønlitteratursamlingen (matchet ved md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: hvilken torrent denne fil er i."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: angivet når vi ikke kunne downloade bogen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Vi nævnte dette sidste gang, men for at præcisere: \"filename\" og \"md5\" er de faktiske egenskaber ved filen, mens \"filename_reported\" og \"md5_reported\" er, hvad vi har skrabet fra Z-Library. Nogle gange stemmer disse to ikke overens, så vi inkluderede begge."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Til denne udgivelse ændrede vi sorteringen til \"utf8mb4_unicode_ci\", hvilket burde være kompatibelt med ældre versioner af MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Datafilerne ligner dem fra sidste gang, selvom de er meget større. Vi kunne simpelthen ikke være generet med at skabe tonsvis af mindre torrentfiler. \"pilimi-zlib2-0-14679999-extra.torrent\" indeholder alle de filer, vi missede i den sidste udgivelse, mens de andre torrenter alle er nye ID-rækker. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Opdatering %(date)s:</strong> Vi gjorde de fleste af vores torrents for store, hvilket fik torrentklienter til at kæmpe. Vi har fjernet dem og udgivet nye torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Opdatering %(date)s:</strong> Der var stadig for mange filer, så vi pakkede dem ind i tar-filer og udgav nye torrents igen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Udgivelse 2 tillæg (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dette er en enkelt ekstra torrentfil. Den indeholder ikke nogen ny information, men den har nogle data i sig, der kan tage et stykke tid at beregne. Det gør det praktisk at have, da det ofte er hurtigere at downloade denne torrent end at beregne den fra bunden. Specifikt indeholder den SQLite-indekser for tar-filerne, til brug med <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Ofte stillede spørgsmål (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Hvad er Anna’s Arkiv?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Arkiv</span> er et non-profit projekt med to mål:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Bevaring:</strong> Sikkerhedskopiering af al menneskelig viden og kultur.</li><li><strong>Adgang:</strong> At gøre denne viden og kultur tilgængelig for alle i verden.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Alt vores <a %(a_code)s>kode</a> og <a %(a_datasets)s>data</a> er fuldstændig open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Bevaring"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Vi bevarer bøger, artikler, tegneserier, magasiner og mere ved at samle disse materialer fra forskellige <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">skyggebiblioteker</a>, officielle biblioteker og andre samlinger ét sted. Alle disse data bevares for evigt ved at gøre det nemt at kopiere dem i bulk — ved hjælp af torrents — hvilket resulterer i mange kopier rundt om i verden. Nogle skyggebiblioteker gør allerede dette selv (f.eks. Sci-Hub, Library Genesis), mens Anna’s Arkiv “frigør” andre biblioteker, der ikke tilbyder bulkdistribution (f.eks. Z-Library) eller slet ikke er skyggebiblioteker (f.eks. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Denne brede distribution, kombineret med open-source kode, gør vores hjemmeside modstandsdygtig over for nedlukninger og sikrer den langsigtede bevaring af menneskehedens viden og kultur. Læs mere om <a href=\"/datasets\">vores datasæt</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Vi anslår, at vi har bevaret omkring <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% af verdens bøger</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Adgang"

#, fuzzy
msgid "page.home.access.text"
msgstr "Vi arbejder med partnere for at gøre vores samlinger let og frit tilgængelige for alle. Vi mener, at alle har ret til menneskehedens kollektive visdom. Og <a %(a_search)s>ikke på bekostning af forfatterne</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Timelige downloads i de sidste 30 dage. Timeligt gennemsnit: %(hourly)s. Dagligt gennemsnit: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Vi tror stærkt på den frie strøm af information og bevaring af viden og kultur. Med denne søgemaskine bygger vi på skuldrene af giganter. Vi respekterer dybt det hårde arbejde fra de mennesker, der har skabt de forskellige skyggebiblioteker, og vi håber, at denne søgemaskine vil udvide deres rækkevidde."

#, fuzzy
msgid "page.about.text3"
msgstr "For at holde dig opdateret om vores fremskridt, følg Anna på <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> eller <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. For spørgsmål og feedback kontakt venligst Anna på %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Hvordan kan jeg hjælpe?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Følg os på <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, eller <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Spred budskabet om Anna’s Arkiv på Twitter, Reddit, Tiktok, Instagram, på din lokale café eller bibliotek, eller hvor du end går! Vi tror ikke på at holde noget hemmeligt — hvis vi bliver taget ned, dukker vi bare op et andet sted, da al vores kode og data er fuldstændig open source.</li><li>3. Hvis du har mulighed for det, overvej at <a href=\"/donate\">donere</a>.</li><li>4. Hjælp med at <a href=\"https://translate.annas-software.org/\">oversætte</a> vores hjemmeside til forskellige sprog.</li><li>5. Hvis du er softwareingeniør, overvej at bidrage til vores <a href=\"https://annas-software.org/\">open source</a>, eller seed vores <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Vi har nu også en synkroniseret Matrix-kanal på %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Hvis du er sikkerhedsforsker, kan vi bruge dine færdigheder både til angreb og forsvar. Tjek vores <a %(a_security)s>Sikkerhed</a> side."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Vi søger eksperter i betalinger for anonyme handlende. Kan du hjælpe os med at tilføje mere bekvemme måder at donere på? PayPal, WeChat, gavekort. Hvis du kender nogen, kontakt os venligst."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Vi søger altid mere serverkapacitet."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Du kan hjælpe ved at rapportere filproblemer, efterlade kommentarer og oprette lister direkte på denne hjemmeside. Du kan også hjælpe ved at <a %(a_upload)s>uploade flere bøger</a>, eller rette filproblemer eller formatering af eksisterende bøger."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Opret eller hjælp med at vedligeholde Wikipedia-siden for Annas Arkiv på dit sprog."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Vi søger at placere små, smagfulde annoncer. Hvis du ønsker at annoncere på Annas Arkiv, så lad os det vide."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Vi ville elske, hvis folk opsætter <a %(a_mirrors)s>spejle</a>, og vi vil økonomisk støtte dette."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "For mere omfattende information om, hvordan man kan være frivillig, se vores <a %(a_volunteering)s>Frivillighed & Belønninger</a> side."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Hvorfor er de langsomme downloads så langsomme?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Vi har bogstaveligt talt ikke nok ressourcer til at give alle i verden højhastighedsdownloads, så meget som vi gerne ville. Hvis en rig velgører ville træde til og give os dette, ville det være fantastisk, men indtil da gør vi vores bedste. Vi er et non-profit projekt, der knap nok kan opretholde sig selv gennem donationer."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Derfor har vi implementeret to systemer for gratis downloads med vores partnere: delte servere med langsomme downloads og lidt hurtigere servere med en venteliste (for at reducere antallet af personer, der downloader samtidig)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Vi har også <a %(a_verification)s>browserverifikation</a> for vores langsomme downloads, fordi bots og skrabere ellers vil misbruge dem, hvilket gør det endnu langsommere for legitime brugere."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Bemærk, at når du bruger Tor Browser, kan du være nødt til at justere dine sikkerhedsindstillinger. På den laveste af mulighederne, kaldet “Standard”, lykkes Cloudflare turnstile-udfordringen. På de højere muligheder, kaldet “Sikrere” og “Sikrest”, mislykkes udfordringen."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "For store filer kan langsomme downloads nogle gange bryde midtvejs. Vi anbefaler at bruge en download manager (såsom JDownloader) til automatisk at genoptage store downloads."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donations-FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Fornyes medlemskaber automatisk?</div> Medlemskaber <strong>fornyes ikke</strong> automatisk. Du kan være medlem så længe eller kort, som du ønsker."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kan jeg opgradere mit medlemskab eller få flere medlemskaber?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Har I andre betalingsmetoder?</div> Ikke i øjeblikket. Mange mennesker ønsker ikke, at arkiver som dette skal eksistere, så vi skal være forsigtige. Hvis du kan hjælpe os med at oprette andre (mere bekvemme) betalingsmetoder sikkert, så kontakt os på %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Hvad betyder intervallerne pr. måned?</div> Du kan nå den lave ende af et interval ved at anvende alle rabatterne, såsom at vælge en periode længere end en måned."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Hvad bruger I donationer på?</div> 100%% går til at bevare og gøre verdens viden og kultur tilgængelig. I øjeblikket bruger vi det mest på servere, lagerplads og båndbredde. Ingen penge går til nogen teammedlemmer personligt."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kan jeg give en stor donation?</div> Det ville være fantastisk! For donationer over et par tusinde dollars, kontakt os venligst direkte på %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kan jeg give en donation uden at blive medlem?</div> Selvfølgelig. Vi accepterer donationer af enhver størrelse på denne Monero (XMR) adresse: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Hvordan uploader jeg nye bøger?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativt kan du uploade dem til Z-Library <a %(a_upload)s>her</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "For små uploads (op til 10.000 filer) bedes du uploade dem til både %(first)s og %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Indtil videre foreslår vi at uploade nye bøger til Library Genesis forks. Her er en <a %(a_guide)s>praktisk guide</a>. Bemærk, at begge forks, som vi indekserer på denne hjemmeside, trækker fra dette samme upload-system."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "For Libgen.li, sørg for først at logge ind på <a %(a_forum)s>deres forum</a> med brugernavn %(username)s og adgangskode %(password)s, og vend derefter tilbage til deres <a %(a_upload_page)s>uploadside</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Hvis din e-mailadresse ikke virker på Libgen-foraene, anbefaler vi at bruge <a %(a_mail)s>Proton Mail</a> (gratis). Du kan også <a %(a_manual)s>anmode manuelt</a> om at få din konto aktiveret."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Bemærk, at mhut.org blokerer visse IP-områder, så en VPN kan være nødvendig."

#, fuzzy
msgid "page.upload.large.text"
msgstr "For store uploads (over 10.000 filer), der ikke accepteres af Libgen eller Z-Library, kontakt os venligst på %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "For at uploade akademiske artikler, bedes du også (ud over Library Genesis) uploade til <a %(a_stc_nexus)s>STC Nexus</a>. De er det bedste skyggebibliotek for nye artikler. Vi har ikke integreret dem endnu, men det vil vi på et tidspunkt. Du kan bruge deres <a %(a_telegram)s>upload-bot på Telegram</a> eller kontakte adressen, der er angivet i deres fastgjorte besked, hvis du har for mange filer til at uploade på denne måde."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Hvordan anmoder jeg om bøger?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "På nuværende tidspunkt kan vi ikke imødekomme boganmodninger."

#, fuzzy
msgid "page.request.forums"
msgstr "Lav venligst dine anmodninger på Z-Library eller Libgen-foraene."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Send os ikke dine boganmodninger."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Indsamler I metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Det gør vi faktisk."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Jeg downloadede 1984 af George Orwell, vil politiet komme til min dør?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Bekymr dig ikke for meget, der er mange mennesker, der downloader fra de hjemmesider, vi linker til, og det er ekstremt sjældent at komme i problemer. For at være på den sikre side anbefaler vi at bruge en VPN (betalt) eller <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Hvordan gemmer jeg mine søgeindstillinger?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Vælg de indstillinger, du kan lide, lad søgefeltet være tomt, klik på “Søg”, og bogmærk derefter siden ved hjælp af din browsers bogmærkefunktion."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Har I en mobilapp?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Vi har ikke en officiel mobilapp, men du kan installere denne hjemmeside som en app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik på tre-punkts menuen øverst til højre, og vælg “Tilføj til startskærm”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik på “Del” knappen nederst, og vælg “Tilføj til startskærm”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Har I en API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Vi har en stabil JSON API for medlemmer, til at få en hurtig download-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentation inden for JSON selv)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Til andre brugsscenarier, såsom at iterere gennem alle vores filer, bygge tilpassede søgninger og så videre, anbefaler vi <a %(a_generate)s>at generere</a> eller <a %(a_download)s>downloade</a> vores ElasticSearch og MariaDB databaser. De rå data kan manuelt udforskes <a %(a_explore)s>gennem JSON-filer</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Vores rå torrentliste kan også downloades som <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Jeg vil gerne hjælpe med at seede, men jeg har ikke meget diskplads."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Brug <a %(a_list)s>torrentlistegeneratoren</a> til at generere en liste over torrents, der har mest brug for seeding, inden for dine lagerpladsgrænser."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrenterne er for langsomme; kan jeg downloade dataene direkte fra jer?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ja, se <a %(a_llm)s>LLM data</a> siden."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Kan jeg kun downloade et underudvalg af filerne, som kun et bestemt sprog eller emne?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kort svar: ikke nemt."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Langt svar:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "De fleste torrents indeholder filerne direkte, hvilket betyder, at du kan instruere torrentklienter til kun at downloade de nødvendige filer. For at bestemme hvilke filer der skal downloades, kan du <a %(a_generate)s>generere</a> vores metadata, eller <a %(a_download)s>downloade</a> vores ElasticSearch- og MariaDB-databaser. Desværre indeholder en række torrent-samlinger .zip- eller .tar-filer i roden, i hvilket tilfælde du skal downloade hele torrenten, før du kan vælge individuelle filer."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Vi har dog <a %(a_ideas)s>nogle idéer</a> til sidstnævnte tilfælde.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Der er endnu ikke tilgængelige nemme værktøjer til at filtrere torrents, men vi byder bidrag velkommen."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Hvordan håndterer I dubletter i torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Vi forsøger at holde minimal duplikering eller overlap mellem torrents i denne liste, men det kan ikke altid opnås og afhænger meget af politikkerne i kildens biblioteker. For biblioteker, der udgiver deres egne torrents, er det uden for vores kontrol. For torrents udgivet af Annas Arkiv, deduplikerer vi kun baseret på MD5-hash, hvilket betyder, at forskellige versioner af den samme bog ikke bliver deduplikeret."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Kan jeg få torrent-listen som JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ja."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Jeg ser ikke PDF'er eller EPUB'er i torrents, kun binære filer? Hvad gør jeg?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Disse er faktisk PDF'er og EPUB'er, de har bare ikke en udvidelse i mange af vores torrents. Der er to steder, hvor du kan finde metadata for torrentfiler, inklusive filtyper/udvidelser:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Hver samling eller udgivelse har sine egne metadata. For eksempel har <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> en tilsvarende metadatadatabase, der er hostet på Libgen.rs-websitet. Vi linker typisk til relevante metadataressourcer fra hver samlings <a %(a_datasets)s>dataside</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Vi anbefaler at <a %(a_generate)s>generere</a> eller <a %(a_download)s>downloade</a> vores ElasticSearch- og MariaDB-databaser. Disse indeholder en mapping for hver post i Annas Arkiv til de tilsvarende torrentfiler (hvis tilgængelige), under “torrent_paths” i ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Hvorfor kan min torrent-klient ikke åbne nogle af jeres torrent-filer / magnet-links?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Nogle torrent-klienter understøtter ikke store stykstørrelser, som mange af vores torrents har (for nyere torrents gør vi ikke dette længere — selvom det er gyldigt ifølge specifikationerne!). Så prøv en anden klient, hvis du støder på dette problem, eller klag til producenten af din torrent-klient."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Har I et program for ansvarlig offentliggørelse?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Vi byder sikkerhedsforskere velkommen til at søge efter sårbarheder i vores systemer. Vi er store tilhængere af ansvarlig offentliggørelse. Kontakt os <a %(a_contact)s>her</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Vi er i øjeblikket ikke i stand til at tildele bug bounties, undtagen for sårbarheder, der har <a %(a_link)s>potentiale til at kompromittere vores anonymitet</a>, for hvilke vi tilbyder bounties i området $10k-50k. Vi vil gerne tilbyde bredere scope for bug bounties i fremtiden! Bemærk venligst, at social engineering-angreb er uden for scope."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Hvis du er interesseret i offensiv sikkerhed og vil hjælpe med at arkivere verdens viden og kultur, så sørg for at kontakte os. Der er mange måder, hvorpå du kan hjælpe."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Er der flere ressourcer om Annas Arkiv?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Annas Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmæssige opdateringer"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Annas Software</a> — vores open source-kode"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Oversæt på Annas Software</a> — vores oversættelsessystem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — om dataene"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domæner"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mere om os (hjælp venligst med at holde denne side opdateret, eller opret en for dit eget sprog!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Hvordan rapporterer jeg ophavsretskrænkelser?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Vi hoster ikke noget ophavsretligt beskyttet materiale her. Vi er en søgemaskine og indekserer derfor kun metadata, der allerede er offentligt tilgængelige. Når du downloader fra disse eksterne kilder, foreslår vi, at du tjekker lovene i din jurisdiktion med hensyn til, hvad der er tilladt. Vi er ikke ansvarlige for indhold hostet af andre."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Hvis du har klager over, hvad du ser her, er det bedste at kontakte den oprindelige hjemmeside. Vi trækker regelmæssigt deres ændringer ind i vores database. Hvis du virkelig mener, at du har en gyldig DMCA-klage, som vi bør reagere på, bedes du udfylde <a %(a_copyright)s>DMCA / Copyright-klageformularen</a>. Vi tager dine klager alvorligt og vender tilbage til dig så hurtigt som muligt."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Jeg hader, hvordan I kører dette projekt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Vi vil også gerne minde alle om, at al vores kode og data er fuldstændig open source. Dette er unikt for projekter som vores — vi kender ikke til noget andet projekt med en tilsvarende massiv katalog, der også er fuldstændig open source. Vi byder meget velkommen til alle, der mener, at vi kører vores projekt dårligt, til at tage vores kode og data og oprette deres eget skyggebibliotek! Vi siger ikke dette af trods eller noget — vi synes oprigtigt, at dette ville være fantastisk, da det ville hæve standarden for alle og bedre bevare menneskehedens arv."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Har du en oppetidsovervågning?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Se venligst <a %(a_href)s>dette fremragende projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Hvordan donerer jeg bøger eller andre fysiske materialer?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Send dem venligst til <a %(a_archive)s>Internet Archive</a>. De vil bevare dem korrekt."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Hvem er Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Du er Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Hvad er jeres yndlingsbøger?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Her er nogle bøger, der har særlig betydning for skyggebiblioteker og digital bevaring:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Du har opbrugt dine hurtige downloads for i dag."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Bliv medlem for at bruge hurtige downloads."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Vi understøtter nu Amazon-gavekort, kredit- og debetkort, krypto, Alipay og WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Fuld database"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Bøger, artikler, magasiner, tegneserier, biblioteksposter, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Søg"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub har <a %(a_paused)s>pauseret</a> upload af nye artikler."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB er en fortsættelse af Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direkte adgang til %(count)s akademiske artikler"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Åben"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Hvis du er <a %(a_member)s>medlem</a>, er browserverifikation ikke påkrævet."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Langtidsarkiv"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Datasættene brugt i Anna’s Arkiv er helt åbne og kan spejles i bulk ved hjælp af torrents. <a %(a_datasets)s>Lær mere…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Du kan hjælpe enormt ved at seede torrents. <a %(a_torrents)s>Lær mere…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seedere"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seedere"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seedere"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Vi har verdens største samling af tekstdata af høj kvalitet. <a %(a_llm)s>Lær mere…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Spejle: opfordring til frivillige"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Søger frivillige"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Som et non-profit, open-source projekt søger vi altid folk til at hjælpe."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Hvis du driver en højrisiko anonym betalingsprocessor, bedes du kontakte os. Vi søger også folk, der ønsker at placere smagfulde små annoncer. Alle indtægter går til vores bevaringsindsats."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Annas Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS-downloads"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Alle downloadlinks for denne fil: <a %(a_main)s>Filens hovedside</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(du skal muligvis prøve flere gange med IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 For hurtigere downloads og for at springe browserkontrollerne over, <a %(a_membership)s>bliv medlem</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 For bulk-spejling af vores samling, tjek <a %(a_datasets)s>Datasets</a> og <a %(a_torrents)s>Torrents</a> siderne."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-data"

#, fuzzy
msgid "page.llm.intro"
msgstr "Det er velkendt, at LLM'er trives med data af høj kvalitet. Vi har verdens største samling af bøger, artikler, magasiner osv., som er nogle af de højeste kvalitetstekstkilder."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unik skala og rækkevidde"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Vores samling indeholder over hundrede millioner filer, herunder akademiske tidsskrifter, lærebøger og magasiner. Vi opnår denne skala ved at kombinere store eksisterende arkiver."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Nogle af vores kildekollektioner er allerede tilgængelige i bulk (Sci-Hub og dele af Libgen). Andre kilder har vi selv frigjort. <a %(a_datasets)s>Datasets</a> viser en fuld oversigt."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Vores samling inkluderer millioner af bøger, artikler og magasiner fra før e-bogens æra. Store dele af denne samling er allerede blevet OCR'et og har allerede lidt intern overlapning."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Hvordan vi kan hjælpe"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Vi er i stand til at give højhastighedsadgang til vores fulde samlinger samt til uudgivne samlinger."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Dette er adgang på virksomhedsniveau, som vi kan tilbyde for donationer i størrelsesordenen titusinder af USD. Vi er også villige til at bytte dette for høj-kvalitets samlinger, som vi endnu ikke har."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Vi kan refundere dig, hvis du er i stand til at give os berigelse af vores data, såsom:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Fjernelse af overlapning (deduplikation)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Tekst- og metadataudtræk"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Støt langsigtet arkivering af menneskelig viden, mens du får bedre data til din model!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontakt os</a> for at diskutere, hvordan vi kan arbejde sammen."

#, fuzzy
msgid "page.login.continue"
msgstr "Fortsæt"

#, fuzzy
msgid "page.login.please"
msgstr "Venligst <a %(a_account)s>log ind</a> for at se denne side.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Arkiv er midlertidigt nede for vedligeholdelse. Kom venligst tilbage om en time."

#, fuzzy
msgid "page.metadata.header"
msgstr "Forbedr metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Du kan hjælpe med at bevare bøger ved at forbedre metadata! Først, læs baggrunden om metadata på Anna’s Arkiv, og lær derefter hvordan du forbedrer metadata gennem linking med Open Library, og tjen gratis medlemskab på Anna’s Arkiv."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Baggrund"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Når du ser på en bog på Anna’s Arkiv, kan du se forskellige felter: titel, forfatter, udgiver, udgave, år, beskrivelse, filnavn og mere. Alle disse informationer kaldes <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Da vi kombinerer bøger fra forskellige <em>kildelibraries</em>, viser vi de metadata, der er tilgængelige i den pågældende kildelibrary. For eksempel, for en bog vi har fra Library Genesis, viser vi titlen fra Library Genesis’ database."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Nogle gange er en bog til stede i <em>flere</em> kildelibraries, som måske har forskellige metadatafelter. I så fald viser vi simpelthen den længste version af hvert felt, da den forhåbentlig indeholder de mest nyttige informationer! Vi viser stadig de andre felter under beskrivelsen, f.eks. som ”alternativ titel” (men kun hvis de er forskellige)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Vi udtrækker også <em>koder</em> såsom identifikatorer og klassifikatorer fra kildelibrary. <em>Identifikatorer</em> repræsenterer unikt en bestemt udgave af en bog; eksempler er ISBN, DOI, Open Library ID, Google Books ID eller Amazon ID. <em>Klassifikatorer</em> grupperer flere lignende bøger; eksempler er Dewey Decimal (DCC), UDC, LCC, RVK eller GOST. Nogle gange er disse koder eksplicit linket i kildelibraries, og nogle gange kan vi udtrække dem fra filnavnet eller beskrivelsen (primært ISBN og DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Vi kan bruge identifikatorer til at finde poster i <em>metadata-only samlinger</em>, såsom OpenLibrary, ISBNdb eller WorldCat/OCLC. Der er en specifik <em>metadata-fane</em> i vores søgemaskine, hvis du vil gennemse disse samlinger. Vi bruger matchende poster til at udfylde manglende metadatafelter (f.eks. hvis en titel mangler), eller f.eks. som “alternativ titel” (hvis der er en eksisterende titel)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "For at se præcis hvor metadata for en bog kom fra, se <em>“Tekniske detaljer” fanen</em> på en bogside. Den har et link til den rå JSON for den bog, med henvisninger til den rå JSON af de originale poster."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "For mere information, se følgende sider: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Søgning (metadata-fane)</a>, <a %(a_codes)s>Koder Explorer</a>, og <a %(a_example)s>Eksempel metadata JSON</a>. Endelig kan alle vores metadata <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>downloades</a> som ElasticSearch og MariaDB databaser."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library linking"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Så hvis du støder på en fil med dårlige metadata, hvordan skal du så rette det? Du kan gå til kildelibrary og følge dets procedurer for at rette metadata, men hvad skal du gøre, hvis en fil er til stede i flere kildelibraries?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Der er en identifikator, der behandles specielt på Anna’s Arkiv. <strong>Feltet annas_archive md5 på Open Library tilsidesætter altid alle andre metadata!</strong> Lad os først tage et skridt tilbage og lære om Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library blev grundlagt i 2006 af Aaron Swartz med målet om “en webside for hver bog, der nogensinde er udgivet”. Det er en slags Wikipedia for bogmetadata: alle kan redigere det, det er frit licenseret og kan downloades i bulk. Det er en bogdatabase, der er mest på linje med vores mission — faktisk er Anna’s Arkiv inspireret af Aaron Swartz’ vision og liv."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "I stedet for at genopfinde hjulet besluttede vi at omdirigere vores frivillige mod Open Library. Hvis du ser en bog, der har forkerte metadata, kan du hjælpe på følgende måde:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Gå til <a %(a_openlib)s>Open Library website</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Find den korrekte bogpost. <strong>ADVARSEL:</strong> sørg for at vælge den korrekte <strong>udgave</strong>. I Open Library har du “værker” og “udgaver”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Et “værk” kunne være “Harry Potter og De Vises Sten”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "En “udgave” kunne være:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Første udgave fra 1997 udgivet af Bloomsbery med 256 sider."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Paperback-udgaven fra 2003 udgivet af Raincoast Books med 223 sider."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Den polske oversættelse fra 2000 “Harry Potter I Kamie Filozoficzn” af Media Rodzina med 328 sider."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Alle disse udgaver har forskellige ISBN-numre og forskelligt indhold, så sørg for at vælge den rigtige!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Rediger posten (eller opret den, hvis den ikke findes), og tilføj så meget nyttig information som muligt! Du er her nu alligevel, så du kan lige så godt gøre posten virkelig fantastisk."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Under “ID-numre” vælg “Anna’s Archive” og tilføj bogens MD5 fra Anna’s Archive. Dette er den lange streng af bogstaver og tal efter “/md5/” i URL'en."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Prøv at finde andre filer i Anna’s Archive, der også matcher denne post, og tilføj dem også. I fremtiden kan vi gruppere dem som dubletter på Anna’s Archive søgeside."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Når du er færdig, skriv URL'en ned, som du lige har opdateret. Når du har opdateret mindst 30 poster med Anna’s Archive MD5'er, send os en <a %(a_contact)s>email</a> og send os listen. Vi giver dig et gratis medlemskab til Anna’s Archive, så du nemmere kan udføre dette arbejde (og som en tak for din hjælp). Disse skal være redigeringer af høj kvalitet, der tilføjer betydelige mængder information, ellers vil din anmodning blive afvist. Din anmodning vil også blive afvist, hvis nogen af redigeringerne bliver tilbageført eller rettet af Open Library-moderatorer."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Bemærk, at dette kun virker for bøger, ikke akademiske artikler eller andre typer filer. For andre typer filer anbefaler vi stadig at finde kildebiblioteket. Det kan tage et par uger, før ændringerne bliver inkluderet i Anna’s Archive, da vi skal downloade den nyeste Open Library data dump og genskabe vores søgeindeks."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spejle: opfordring til frivillige"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "For at øge robustheden af Annas Arkiv søger vi frivillige til at køre spejle."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Vi leder efter dette:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Du kører Anna’s Archive open source-kodebase, og du opdaterer regelmæssigt både koden og dataene."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Din version er tydeligt adskilt som et spejl, f.eks. “Bobs Arkiv, et Anna’s Archive spejl”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Du er villig til at tage de risici, der er forbundet med dette arbejde, som er betydelige. Du har en dyb forståelse af den nødvendige operationelle sikkerhed. Indholdet af <a %(a_shadow)s>disse</a> <a %(a_pirate)s>indlæg</a> er selvindlysende for dig."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Du er villig til at bidrage til vores <a %(a_codebase)s>kodebase</a> — i samarbejde med vores team — for at få dette til at ske."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "I starten vil vi ikke give dig adgang til vores partner server downloads, men hvis det går godt, kan vi dele det med dig."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hostingudgifter"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Vi er villige til at dække hosting- og VPN-udgifter, i starten op til $200 pr. måned. Dette er tilstrækkeligt til en grundlæggende søgeserver og en DMCA-beskyttet proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Vi betaler kun for hosting, når du har alt sat op, og har demonstreret, at du er i stand til at holde arkivet opdateret med opdateringer. Dette betyder, at du skal betale for de første 1-2 måneder af egen lomme."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Din tid vil ikke blive kompenseret (og det vil vores heller ikke), da dette er rent frivilligt arbejde."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Hvis du bliver væsentligt involveret i udviklingen og driften af vores arbejde, kan vi diskutere at dele mere af donationsindtægterne med dig, så du kan bruge dem efter behov."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Kom godt i gang"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Venligst <strong>kontakt os ikke</strong> for at bede om tilladelse eller for grundlæggende spørgsmål. Handlinger taler højere end ord! Alle oplysninger er derude, så gå bare i gang med at opsætte dit spejl."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Du er velkommen til at oprette billetter eller merge requests på vores Gitlab, når du støder på problemer. Vi kan have brug for at bygge nogle spejl-specifikke funktioner med dig, såsom rebranding fra “Anna’s Archive” til dit websteds navn, (i starten) deaktivering af brugerkonti eller linke tilbage til vores hovedside fra bogsider."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Når du har dit spejl kørende, bedes du kontakte os. Vi vil gerne gennemgå din OpSec, og når den er solid, vil vi linke til dit spejl og begynde at arbejde tættere sammen med dig."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Tak på forhånd til alle, der er villige til at bidrage på denne måde! Det er ikke for de sarte sjæle, men det vil styrke levetiden for det største virkelig åbne bibliotek i menneskets historie."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Download fra partnerwebsite"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Langsomme downloads er kun tilgængelige via den officielle hjemmeside. Besøg %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Langsomme downloads er ikke tilgængelige via Cloudflare VPN'er eller fra Cloudflare IP-adresser."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Vent venligst <span %(span_countdown)s>%(wait_seconds)s</span> sekunder for at downloade denne fil."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Brug følgende URL til at downloade: <a %(a_download)s>Download nu</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Tak fordi du venter, dette holder hjemmesiden tilgængelig gratis for alle! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Advarsel: der har været mange downloads fra din IP-adresse inden for de sidste 24 timer. Downloads kan være langsommere end normalt."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads fra din IP-adresse i de sidste 24 timer: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Hvis du bruger en VPN, delt internetforbindelse eller din internetudbyder deler IP-adresser, kan denne advarsel skyldes det."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "For at give alle mulighed for at downloade filer gratis, skal du vente, før du kan downloade denne fil."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Du er velkommen til at fortsætte med at browse Anna’s Arkiv i en anden fane, mens du venter (hvis din browser understøtter opdatering af baggrundsfaner)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Du er velkommen til at vente på, at flere download-sider indlæses samtidig (men download venligst kun én fil ad gangen pr. server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Når du får et downloadlink, er det gyldigt i flere timer."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Annas Arkiv"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Post i Annas Arkiv"

#, fuzzy
msgid "page.scidb.download"
msgstr "Download"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "For at støtte tilgængeligheden og den langsigtede bevaring af menneskelig viden, bliv <a %(a_donate)s>medlem</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Som en bonus, 🧬&nbsp;SciDB indlæses hurtigere for medlemmer, uden nogen begrænsninger."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Virker det ikke? Prøv at <a %(a_refresh)s>opdatere</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Ingen forhåndsvisning tilgængelig endnu. Download filen fra <a %(a_path)s>Annas Arkiv</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB er en fortsættelse af Sci-Hub, med dens velkendte grænseflade og direkte visning af PDF'er. Indtast din DOI for at se."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Vi har hele Sci-Hub-samlingen samt nye artikler. De fleste kan ses direkte med en velkendt grænseflade, der ligner Sci-Hub. Nogle kan downloades gennem eksterne kilder, i hvilket tilfælde vi viser links til dem."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Søg"

#, fuzzy
msgid "page.search.title.new"
msgstr "Ny søgning"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Inkluder kun"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Ekskluder"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Ikke markeret"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Download"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Tidsskriftsartikler"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digital Udlån"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titel, forfatter, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Søg"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Søgeindstillinger"

#, fuzzy
msgid "page.search.submit"
msgstr "Søg"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Søgningen tog for lang tid, hvilket er almindeligt for brede forespørgsler. Filtertællingerne kan være unøjagtige."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Søgningen tog for lang tid, hvilket betyder, at du måske ser unøjagtige resultater. Nogle gange hjælper det at <a %(a_reload)s>genindlæse</a> siden."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Visning"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Liste"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avanceret"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Søg beskrivelser og metadata kommentarer"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Tilføj specifikt søgefelt"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(søg specifikt felt)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Udgivelsesår"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Indhold"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Filtype"

#, fuzzy
msgid "page.search.more"
msgstr "mere…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Adgang"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Kilde"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scrapet og open-sourcet af AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Sprog"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Sorter efter"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Mest relevant"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Nyeste"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(udgivelsesår)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Ældste"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Største"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(filstørrelse)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Mindste"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open sourced)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Tilfældig"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Søgeindekset opdateres månedligt. Det inkluderer i øjeblikket poster op til %(last_data_refresh_date)s. For mere teknisk information, se <a %(link_open_tag)s>datasets-siden</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "For at udforske søgeindekset efter koder, brug <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Skriv i boksen for at søge i vores katalog af %(count)s direkte downloadbare filer, som vi <a %(a_preserve)s>bevarer for evigt</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Faktisk kan alle hjælpe med at bevare disse filer ved at seede vores <a %(a_torrents)s>samlede liste over torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Vi har i øjeblikket verdens mest omfattende åbne katalog af bøger, artikler og andre skriftlige værker. Vi spejler Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>og mere</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Hvis du finder andre “skyggebiblioteker”, som vi bør spejle, eller hvis du har spørgsmål, bedes du kontakte os på %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "For DMCA / copyright-krav <a %(a_copyright)s>klik her</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: brug tastaturgenveje “/” (søgefokus), “enter” (søg), “j” (op), “k” (ned), “<” (forrige side), “>” (næste side) for hurtigere navigation."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Leder du efter artikler?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Skriv i boksen for at søge i vores katalog af %(count)s akademiske artikler og tidsskriftsartikler, som vi <a %(a_preserve)s>bevarer for evigt</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Skriv i boksen for at søge efter filer i digitale udlånsbiblioteker."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Dette søgeindeks inkluderer i øjeblikket metadata fra Internet Archive’s Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Mere om vores datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "For flere digitale udlånsbiblioteker, se <a %(a_wikipedia)s>Wikipedia</a> og <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Skriv i boksen for at søge efter metadata fra biblioteker. Dette kan være nyttigt, når du <a %(a_request)s>anmoder om en fil</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Denne søgeindeks inkluderer i øjeblikket metadata fra forskellige metadatakilder. <a %(a_datasets)s>Mere om vores datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "For metadata viser vi de originale poster. Vi foretager ingen sammensmeltning af poster."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Der er mange, mange kilder til metadata for skriftlige værker rundt om i verden. <a %(a_wikipedia)s>Denne Wikipedia-side</a> er et godt udgangspunkt, men hvis du kender andre gode lister, så lad os det vide."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Skriv i boksen for at søge."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Dette er metadata-poster, <span %(classname)s>ikke</span> downloadbare filer."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Fejl under søgning."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Prøv <a %(a_reload)s>at genindlæse siden</a>. Hvis problemet fortsætter, bedes du sende en e-mail til os på %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Ingen filer fundet.</span> Prøv færre eller andre søgeord og filtre."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Nogle gange sker dette forkert, når søgeserveren er langsom. I sådanne tilfælde kan <a %(a_attrs)s>genindlæsning</a> hjælpe."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Vi har fundet match i: %(in)s. Du kan henvise til URL'en fundet der, når du <a %(a_request)s>anmoder om en fil</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Tidsskriftsartikler (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digital udlån (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultater %(from)s-%(to)s (%(total)s i alt)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ delvise match"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d delvise match"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Frivilligt arbejde & Præmier"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive er afhængig af frivillige som dig. Vi byder alle engagementsniveauer velkommen og har to hovedkategorier af hjælp, vi leder efter:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Let frivilligt arbejde:</span> hvis du kun kan afsætte et par timer her og der, er der stadig masser af måder, du kan hjælpe på. Vi belønner konsekvente frivillige med <span %(bold)s>🤝 medlemskaber til Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Tungt frivilligt arbejde (USD$50-USD$5,000 belønninger):</span> hvis du er i stand til at dedikere meget tid og/eller ressourcer til vores mission, vil vi meget gerne arbejde tættere sammen med dig. Til sidst kan du blive en del af det indre team. Selvom vi har et stramt budget, er vi i stand til at tildele <span %(bold)s>💰 monetære belønninger</span> for det mest intense arbejde."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Hvis du ikke kan frivilligt give din tid, kan du stadig hjælpe os meget ved at <a %(a_donate)s>donere penge</a>, <a %(a_torrents)s>seede vores torrents</a>, <a %(a_uploading)s>uploade bøger</a>, eller <a %(a_help)s>fortælle dine venner om Anna’s Arkiv</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Virksomheder:</span> vi tilbyder højhastigheds direkte adgang til vores samlinger i bytte for donationer på virksomhedsniveau eller udveksling af nye samlinger (f.eks. nye scanninger, OCR’ede datasets, berigelse af vores data). <a %(a_contact)s>Kontakt os</a> hvis dette er dig. Se også vores <a %(a_llm)s>LLM-side</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Let frivilligt arbejde"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Hvis du har et par timer til overs, kan du hjælpe på flere måder. Sørg for at deltage i <a %(a_telegram)s>frivilligchatten på Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Som et tegn på vores påskønnelse giver vi typisk 6 måneder af “Heldige Bibliotekar” for grundlæggende milepæle, og mere for fortsat frivilligt arbejde. Alle milepæle kræver arbejde af høj kvalitet — sjusket arbejde skader os mere, end det hjælper, og vi vil afvise det. Venligst <a %(a_contact)s>send os en e-mail</a>, når du når en milepæl."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Opgave"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Milepæl"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Sprede budskabet om Annas Arkiv. For eksempel ved at anbefale bøger på AA, linke til vores blogindlæg eller generelt henvise folk til vores hjemmeside."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links eller skærmbilleder."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Disse skal vise, at du fortæller nogen om Annas Arkiv, og at de takker dig."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Forbedre metadata ved <a %(a_metadata)s>at linke</a> med Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Du kan bruge <a %(a_list)s>listen over tilfældige metadata-problemer</a> som udgangspunkt."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Sørg for at efterlade en kommentar på de problemer, du løser, så andre ikke duplikerer dit arbejde."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links til optegnelser, du har forbedret."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Oversætte</a> hjemmesiden."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Fuldstændig oversættelse af et sprog (hvis det ikke allerede var tæt på færdiggørelse)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Forbedre Wikipedia-siden for Anna’s Arkiv på dit sprog. Inkluder information fra AA’s Wikipedia-side på andre sprog og fra vores hjemmeside og blog. Tilføj referencer til AA på andre relevante sider."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link til redigeringshistorik, der viser, at du har lavet betydelige bidrag."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Opfylde bog- (eller papir osv.) anmodninger på Z-Library eller Library Genesis fora. Vi har ikke vores eget boganmodningssystem, men vi spejler disse biblioteker, så at gøre dem bedre gør også Anna’s Arkiv bedre."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links eller skærmbilleder af anmodninger, du har opfyldt."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Små opgaver, der er postet i vores <a %(a_telegram)s>frivilligchat på Telegram</a>. Normalt for medlemskab, nogle gange for små belønninger."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Små opgaver lagt op i vores frivillige chatgruppe."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Afhænger af opgaven."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Dusører"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Vi leder altid efter folk med solide programmerings- eller offensive sikkerhedsfærdigheder til at involvere sig. Du kan gøre en seriøs forskel i bevarelsen af menneskehedens arv."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Som tak giver vi medlemskab for solide bidrag. Som en stor tak giver vi monetære dusører for særligt vigtige og vanskelige opgaver. Dette bør ikke ses som en erstatning for et job, men det er et ekstra incitament og kan hjælpe med påløbne omkostninger."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Det meste af vores kode er open source, og vi vil bede om det samme af din kode, når vi tildeler dusøren. Der er nogle undtagelser, som vi kan diskutere individuelt."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Dusører tildeles den første person, der fuldfører en opgave. Du er velkommen til at kommentere på en dusørbillet for at lade andre vide, at du arbejder på noget, så andre kan holde sig tilbage eller kontakte dig for at samarbejde. Men vær opmærksom på, at andre stadig er frie til at arbejde på det og forsøge at slå dig. Vi tildeler dog ikke dusører for sjusket arbejde. Hvis to højtkvalitetsindsendelser laves tæt på hinanden (inden for en dag eller to), kan vi vælge at tildele dusører til begge, efter vores skøn, for eksempel 100%% for den første indsendelse og 50%% for den anden indsendelse (så 150%% i alt)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "For de større dusører (især scraping-dusører), bedes du kontakte os, når du har fuldført ~5%% af det, og du er sikker på, at din metode vil skalere til den fulde milepæl. Du skal dele din metode med os, så vi kan give feedback. På denne måde kan vi også beslutte, hvad vi skal gøre, hvis der er flere personer, der nærmer sig en dusør, såsom potentielt at tildele den til flere personer, opmuntre folk til at samarbejde osv."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ADVARSEL: de højtbelønnede opgaver er <span %(bold)s>svære</span> — det kan være klogt at starte med de lettere."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Gå til vores <a %(a_gitlab)s>Gitlab-issues-liste</a> og sorter efter \"Label priority\". Dette viser nogenlunde rækkefølgen af opgaver, vi går op i. Opgaver uden eksplicitte dusører er stadig berettigede til medlemskab, især dem markeret \"Accepted\" og \"Anna’s favorite\". Du vil måske starte med et \"Starter project\"."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Opdateringer om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det største virkelig åbne bibliotek i menneskets historie."

#, fuzzy
msgid "layout.index.title"
msgstr "Annas Arkiv"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Verdens største open-source open-data bibliotek. Spejler Sci-Hub, Library Genesis, Z-Library og mere."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Søg i Annas Arkiv"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annas Arkiv har brug for din hjælp!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Mange forsøger at tage os ned, men vi kæmper tilbage."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Hvis du donerer nu, får du <strong>dobbelt</strong> så mange hurtige downloads."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Gyldig indtil udgangen af denne måned."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donér"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "At redde menneskelig viden: en fantastisk julegave!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Overrask en elsket, giv dem en konto med medlemskab."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "For at øge modstandsdygtigheden af Annas Arkiv søger vi frivillige til at køre spejle."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Den perfekte Valentinsgave!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Vi har en ny donationsmetode tilgængelig: %(method_name)s. Overvej venligst %(donate_link_open_tag)sat donere</a> — det er ikke billigt at drive denne hjemmeside, og din donation gør virkelig en forskel. Mange tak."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Vi kører en indsamlingskampagne for <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">backup</a> af verdens største skyggebibliotek for tegneserier. Tak for din støtte! <a href=\"/donate\">Donér.</a> Hvis du ikke kan donere, overvej at støtte os ved at fortælle dine venner og følge os på <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> eller <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Seneste downloads:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Søg"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Forbedr metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Frivilligt arbejde & dusører"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasæt"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktivitet"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Koder Explorer"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Hjem"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Annas Software ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Oversæt ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Log ind / Registrer"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Konto"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Annas Arkiv"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Hold kontakten"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / ophavsretskrav"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avanceret"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sikkerhed"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativer"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "ikke tilknyttet"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Denne fil kan have problemer."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Hurtig download"

#, fuzzy
msgid "page.donate.copy"
msgstr "kopiér"

#, fuzzy
msgid "page.donate.copied"
msgstr "kopieret!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Forrige"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Næste"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "kun denne måned!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub har <a %(a_closed)s>pauset</a> upload af nye artikler."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Vælg en betalingsmulighed. Vi giver rabatter for kryptobaserede betalinger %(bitcoin_icon)s, fordi vi pådrager os (meget) færre gebyrer."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Vælg en betalingsmulighed. Vi har i øjeblikket kun kryptobaserede betalinger %(bitcoin_icon)s, da traditionelle betalingsprocessorer nægter at arbejde med os."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Vi kan ikke understøtte kredit-/debitkort direkte, fordi bankerne ikke vil arbejde med os. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Der er dog flere måder at bruge kredit-/debitkort alligevel ved hjælp af vores andre betalingsmetoder:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Langsomme & eksterne downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Hvis du bruger krypto for første gang, foreslår vi at bruge %(option1)s, %(option2)s eller %(option3)s til at købe og donere Bitcoin (den oprindelige og mest brugte kryptovaluta)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links af poster, du har forbedret."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links eller skærmbilleder."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links eller skærmbilleder af anmodninger, du har opfyldt."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Hvis du er interesseret i at spejle disse datasets til <a %(a_faq)s>arkivering</a> eller <a %(a_llm)s>LLM-træning</a>, kontakt os venligst."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Hvis du er interesseret i at spejle dette datasæt til <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-træning</a>, bedes du kontakte os."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Hovedwebsted"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN landinformation"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Hvis du er interesseret i at spejle dette datasæt til <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-træning</a> formål, bedes du kontakte os."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Den Internationale ISBN Agentur frigiver regelmæssigt de intervaller, som det har tildelt nationale ISBN-agenturer. Ud fra dette kan vi udlede, hvilket land, region eller sproggruppe denne ISBN tilhører. Vi bruger i øjeblikket disse data indirekte gennem <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ressourcer"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Sidst opdateret: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN hjemmeside"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Ekskluderer “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Vores inspiration til at samle metadata er Aaron Swartz' mål om “en webside for hver bog, der nogensinde er udgivet”, som han skabte <a %(a_openlib)s>Open Library</a> for."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Det projekt har klaret sig godt, men vores unikke position giver os mulighed for at få metadata, som de ikke kan."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "En anden inspiration var vores ønske om at vide <a %(a_blog)s>hvor mange bøger der er i verden</a>, så vi kan beregne, hvor mange bøger vi stadig mangler at redde."

#~ msgid "page.partner_download.text1"
#~ msgstr "For at give alle mulighed for at downloade filer gratis, skal du vente <strong>%(wait_seconds)s sekunder</strong>, før du kan downloade denne fil."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Opdater siden automatisk. Hvis du går glip af download-vinduet, vil timeren genstarte, så automatisk opdatering anbefales."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Download nu"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konverter: brug online værktøjer til at konvertere mellem formater. For eksempel, for at konvertere mellem epub og pdf, brug <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: download filen (pdf eller epub understøttes), og <a %(a_kindle)s>send den til Kindle</a> via web, app eller email. Nyttige værktøjer: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Støt forfattere: Hvis du kan lide dette og har råd til det, overvej at købe originalen eller støtte forfatterne direkte."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Støt biblioteker: Hvis dette er tilgængeligt på dit lokale bibliotek, overvej at låne det gratis der."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Ikke tilgængelig direkte i bulk, kun i semi-bulk bag en betalingsmur"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annas Arkiv administrerer en samling af <a %(isbndb)s>ISBNdb-metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb er en virksomhed, der skraber forskellige online boghandlere for at finde ISBN metadata. Annas Arkiv har lavet sikkerhedskopier af ISBNdb bogmetadata. Disse metadata er tilgængelige gennem Annas Arkiv (dog ikke i øjeblikket i søgning, medmindre du eksplicit søger efter et ISBN-nummer)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "For tekniske detaljer, se nedenfor. På et tidspunkt kan vi bruge det til at bestemme, hvilke bøger der stadig mangler fra skyggebiblioteker, for at prioritere hvilke bøger der skal findes og/eller scannes."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Vores blogindlæg om disse data"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb skrabning"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "I øjeblikket har vi en enkelt torrent, der indeholder en 4,4GB gzippet <a %(a_jsonl)s>JSON Lines</a> fil (20GB uzippet): \"isbndb_2022_09.jsonl.gz\". For at importere en \".jsonl\"-fil til PostgreSQL kan du bruge noget som <a %(a_script)s>dette script</a>. Du kan endda pipe det direkte ved hjælp af noget som %(example_code)s, så det dekomprimerer undervejs."

#~ msgid "page.donate.wait"
#~ msgstr "Vent venligst mindst <span %(span_hours)s>to timer</span> (og opdater denne side) før du kontakter os."

#~ msgid "page.codes.search_archive"
#~ msgstr "Søg i Annas Arkiv efter “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donér ved hjælp af Alipay eller WeChat. Du kan vælge mellem disse på den næste side."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Udbrede kendskabet til Anna’s Arkiv på sociale medier og online fora, ved at anbefale bøger eller lister på AA, eller besvare spørgsmål."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiction-samlingen er divergeret, men har stadig <a %(libgenli)s>torrents</a>, dog ikke opdateret siden 2022 (vi har direkte downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annas Arkiv og Libgen.li administrerer i fællesskab samlinger af <a %(comics)s>tegneserier</a> og <a %(magazines)s>magasiner</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Ingen torrenter for russisk fiktion og standarddokument samlinger."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Der er ingen torrents tilgængelige for det ekstra indhold. De torrents, der er på Libgen.li-websitet, er spejle af andre torrents, der er opført her. Den ene undtagelse er fiktionstorrents, der starter ved %(fiction_starting_point)s. Tegneserie- og magasin-torrents udgives som et samarbejde mellem Annas Arkiv og Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Fra en samling <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> præcis oprindelse uklar. Delvist fra the-eye.eu, delvist fra andre kilder."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

