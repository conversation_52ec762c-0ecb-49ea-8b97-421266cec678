��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b D  sd 3  �f +   �g    h   i �  !k Q  �l (  .n O   Wo m   �o E   p e   [p �  �p \  nr �   �s �  �t �   �v �  �w �  ry �  Z{ �   2} �  ~ �  � 5   k� �   �� P   w� �  Ȃ    �� G  ׄ E   � )   e�    ��    �� C   Ǉ    �    &� %   7�    ]� $   x�    �� <   �� ;  �   %� Q   A� 6   ��    ʋ 
   ׋    � 
   ��    � 
   � 	   *�    4�    D� "   K�    n�    t� 	   �� 
   ��    ��    �� "   Ȍ    � V  � U  [� �   �� +   9� d  e� �   ʒ   i� �   ��    [� �   x�    � �   5� !   ��   �    +� �  H� /   � Y   �    x� 6  �� :  �� �  �   ��    �� J   Ɵ ^   � �   p� *   '� 2   R� y   �� D   �� I   D� +   ��    �� r   �� �  2� ?   � m   (� �   �� =   E�   �� 1   ��   �� �   Ĩ �   P� J   � �   6� �   � E   q� �   �� �   �� _   g� A   ǭ u   	� 
   � �   ��   � E   =� 
   �� �   �� �   �    � r  � �   f�   ��   �� �   ~� 7  c�   �� K   �� :   � V   A� j   �� \   � X   `� 2   �� �   � a   o� 9   Ѿ 
   � g   � T   ~� �   ӿ �   y�    � �   (� �   � J  
� H  X� �   �� �   .� %  � �   )� �   �� �   u� c  � �   l� �   � 
   � �   � �   �� �   k� o  -� s   �� �   � �   �� �  F� z   ��   w�    �� !   �� V   �� �   �� I   �� e  �� �   7� _   �� o   @�    �� �  �� �   �� a  ��    �� g   � �   k� �   �� �   �� �   �� �  �� �  z�     � M  4� �  �� !  	� z  +� "  �� �   �� M   �� �   �    �� �   �� /  f� R  �� �   �� �   �� �   �� 
   o� Z  z� N  �� �   $� 	   � �   
�   �� �   �� �  �� �   �� X   q     �  k  �  T  C 
   � �   � �   }   j �   s H  ] U   �	 N   �	 �   K
 4   >    s   � �   �   �
 /   � E   � �  # �    y   � \    s  k !   �     r  ! �   � �   M E   � �    *   � �   � �   �    �    � $   � :    9   K 8   � N   � |   
    � )   � B   � F    ,   S 5   � 6   � u   �    c    l 
  �   � �   �   �  �   �! �   " K  �"    #$ �   5$ �   % �   �% U  (& ^  ~' �  �( �  �* p  x, E   �- ;   /. &   k. �   �. �  C/ �  B1   �2 >  �3   '5   46   F8 .   c9 "  �9 /  �: T  �;    := �   I= +  > �   I? A  �? �    A    �A )   B a   0B    �B h  �B �  E 5  �F �   �H �   yI 
   J �   ,J �   �J E   �K s   
L   �L �   �M �   ~N ~  O ]   �P   �P    �Q �  R �   �S   �T a  �V �  Y �   �Z 
   �[ �   �[ �  �\    #^ b  5^ �  �_ 	  ^a   hc �   oe +  gf E   �g �  �g �   �i x   rj @   �j T   ,k    �k :   �k "   �k ,   �k "   l    ;l �   Ql �  �l �  �o F  �q �   t    u   u   x g  .z y  �{ I  } p  Z 
   ˁ   ց 	   ق    � �  ��    �� �  �� 2  ��    ̊ �  ܊ �  ̌ �  �� �  p� �  g� a   � O  ��    Ҙ �   � n   �� �  � �  ݜ �   t� �   � �   �� f  D�    �� �   �� G   g� K   ��    �� 3   � A   C� '  �� �  �� �  Z� -   � x  � �   ��    3� �   9� 1   ߭ z   � �   �� -   O� ]   }�    ۯ M  � ,  9� �  f� l  �� �   c�    �� k    � 1  l�    �� �   �� �  )� �   �� 4   T� /   �� <  �� +   �� 5  "� �  X� �  ��     �� �   �� �   _� "  �� {  �    � �   �� -   m� �   �� �   =�   �� �   �� B   �� +   �� �   $� 5   � t  F� 3  �� �   �� �   �� W   �� 6   � �   F�   �� Y  �� �  O� �  	� /   �� �   �� �   �� .  �� �   �� �  �� �  �� +   D� �  p� "   8� �  [� S   '�    {�   ��   �� f  �� �   	�   ��   �� $  � �   5� �  �� �  �� �   � 	  �� L  �� ;   �� |   2� |   ��    ,�    G� =  L� �   �� :   5  	  p  �  z   ;   N z  U �   � �  {   
    ' �   ?    � s  � %   n
    �
    �
    �
     �
    �
    �
                1    9 
   @    K    T    s )   {    �    �    �    � �   � Q   � !   �    � (    &   * -   Q /    "   �    � 
   � 
   �    �            $    ,    2    8 )   I !   s    �    � #   � -   � *       J #   ` 0   �    � O   � <       Q `   W @   �    �     !   &    H    U    i    {    �    �    �    �    � 
   � 	   � 
   �             "    ) 	   2    < 	   T    ^    u    { 	   �    �    �    �    �    �    �          	       ! %   4    Z 	   _ #   i    �    �    �    �    � 
   �    � ]   � �   F N   � �   ! �  � w       �            (    /    <    T $   d Q   � 7   � [    '   o 3   � .   � a   � Y   \ �   � T   � 4   �        7    D 	   J 	   T    ^    m    �    �    �    �    �    �    �    �    �    �    �    �      	             &     3     Q    d     h!    m!    u!     {!    �! G   �! Z   �! %   F" #   l" 2   �"    �"    �"    �" i   �"    @#    F# '   R# f   z#    �#    �# e    $ '   f$ E  �$ Y   �' u   .( �   �( �   2) (   �)    !* �   "+ 9  �+ �   �,    �-    �- 3   �- ?   2. P   r. _   �. [   #/ D   / h   �/ "   -0 +   P0    |0    �0 U   �0 #   �0    1    1    #1    +1 m   C1    �1 .   �1 D   �1    52    H2 R   d2 S   �2 q  3    }4    �4 �   �4    K5    W5    ^5    g5 
   �5 ,   �5 8  �5    �6    
7 
   7    (7   47     S8    t8    |8 ]   �8    �8    �8 /   �8     9    39 $   99 �   ^9    : 
   : J   (:    s:    �:    �:    �: ,   �: N   �: 
   3; >   >; �   }; Q   !< F   s< 
   �< �   �< E   �=     > 0   >    F> �   Z> �   ?    �? <   �? �   @ �   �@    hA &   �A    �A g  �A "   C !   BC    dC "   �C    �C �   �C    WD    qD )   �D <   �D 
   �D    �D    E    7E 6  WE j  �G �  �H 2   �J 2   !K    TK    aK   K �   �L �   bM    �M �   N �  �N    pP    �P �  �P   }R    �S    �S 1   �S 
   �S 1  �S    "U M  7U �  �V �   X    �X k   Y .   oY "   �Y n   �Y %  0Z   V[ �   i\ �  ] m   �^ �   _ T   �_ �   O` �   �` I   �a �   �a *   �b     �b    �b     c    	c    c    :c B   Sc )   �c 	   �c #   �c !  �c %   e �   6e �   f �   �f     g    3g    Ng    ig "   |g    �g $   �g    �g �   h #   �h �   �h    �i �   �i v   �j �   k !  �k �   �l ,   Sm �   �m 	   n   n k   o    �o �  �o    q    ,q    Cq    Sq )   mq 
   �q    �q I   �q �   �q �   �r    hs    qs    ws �   �s   At �   `u r   
v    }v    �v    �v    �v    �v    �v    �v A   w -   Cw �  qw W   5y    �y f   �y h   z M   nz k   �z X   ({ P   �{    �{ g   �{ G   @| �   �| W   } J   c}    �} �   �} X   �~ <     d   = U   � 7   �    0� 5   9� o   o� �   ߀ 2   y� �   ��    O� 
  U� A   `� ]   �� �    �    �� �  �� �   X�    !�    2�    J�    S�   X� )   Z� f   �� �   � �   �� �   p� �   � �   ̋ �   [� �   � [   ��   � c   � e  w� �   ݐ �   �� 
   d� 
   r� 
   �� �   �� 
   � 
   (� B   6� R   y� �   ̓ 
   �� g   �� 1   � u   N� 8   ĕ r   �� _   p� 
   Ж    ޖ 
   ^� 
   l� 
   z�   �� �   �� �  �    ��    ��    �� �   ɛ    p�    �� �   �� �   ��     4�    U�    e� -   }� 0   �� -   ܞ    
�    %� �   @�    3� �  P� �   �� �   ۢ N   o� �   ��   Y� �  b� %  &� �   L� �   ߩ y   c�    ݪ k  ��    `� U  � �   խ   ̮ �   ѯ -  V� �   �� �   2� }   �� (   5� 4   ^�    �� -   ��    س    �    � �   �    � g   � +   [�    ��    ��    ��    �� t   �� @   .� 4   o�   �� 0   �� 	   �    �    �     �    -�    ?�    K�    T� 
   `�    k� 
   w�    �� #   �� �   ��    O�    `�    p�    }�    ��    ��    �� 
   ��    ɹ 
   � %   � n   � 
   �� 1   �� Z   ú �   � �   ӻ �   �� �   A� �   A� "  � 
   � �    � 2   �� E   �� R   $� �   w� �   � ;   �� ^   �    s� a   �� e   �� �   N�    ��     ��    �    �    )�    2�    M�    U�    u�    ~�    ��    ��    ��    ��    ��    
�    �    %�    2�    :�    W� !   ^� (   �� �   �� �   /� $   �� '   �� c   � X   j� r   �� $   6� 7   [� .   �� >   �� =   � (   ?� �   h�   (�   7�    G� @   f� �   �� &   *� �   Q� �   +�    �� =   r�    �� �   �� �   a� k   �� (   S� [   |� �   ��    �� 1   ��    � ?   6� v   v� p   ��    ^�    f�    o� "   |� �   �� 4   ,�     a� 2   �� $   ��    ��     �� A   �    [� d   t� =   ��   � H    � E   i� �   �� >   `� !   ��    �� !   ��     � !   #�     E� !   f� 4   �� 7   �� M  �� 3   C� 5   w� @   �� %   ��    � g   � �   �� �   � �   �� 	   }� �   �� *   �    =� �   M�    �    )� 4   G� .   |� Q   �� O   �� N   M�    ��    �� �   �� -   q� "   �� K   �� �   � =   � �   B� d   �� X   7� t   ��     � *   &� p   Q�    �� \   ��    ;� �   W� 3   � =   H�    �� +   �� �   �� 3   �� C   �� �   1� Y   �� O   � �   d� �   ��    n�    w� Y   �� <   ��    $� &   ;�    b�    q� 	   �� -   �� �   �� h   I�    ��    �� "   ��    
� &   *� z   Q� 3   �� x    � [   y�     �� k   �� �   b� .   D� \   s�    �� B   �� @   (�    i� w   }� s   �� ,   i� U   ��    �� /   �� \   .�    �� c   �� '   �    ,� *   B� _   m� �   ��    ��    ��    �� B   ��    � T   5� <   �� �   �� �   b�    �� =   �� �   3�    �� �   �� @   �� "   �� T   �� �   O�    C     K     M     O  !   c  6   �  U   �         (    5 #   > 8   b 7   � 	   � Q   � ?   /    o     (   �    �    � _   �   M G   P    � 
   � �   � 8  Z K   � 
   � Z  � �  H .   �	 Y   
    ^
 9  |
 *   � d   �    F
    _
 �  t
    H Q   ^ p   � a   ! T   �    � W   � :   C    ~ z   � ?    -   X X   � '   � Q    �   Y �   � +   � �   � \  a �   � 1   n K  � �   � �   � #  � �   � 0   g    � �   � '   Q �  y j   o H   �    #     /  �  E     �! �   �! !  �"    �# 7  �$ E   #& H   i& U   �& *   ' $   3' O   X' V   �'    �'     ( 8   -(    f(    �( +   �( T   �( 0   )    P) g   W) �   �) �   �* 
   + 
   %+    3+ I   8+ [   �+ O   �+ �   ., \   �,    F- &   X- �   -    D. �   M. �  �. �   ~1 N   p2 /   �2    �2    �2    �2 @   �2 0   @3 �   q3 �   �3 O   �4     5    35 %   F5    l5 K   �5    �5 =   �5    6 (    6 $   I6    n6    }6 T   �6    �6    �6 (   �6     7    :7 e   >7 �   �7 X   q8 X   �8 P   #9 �   t9    (:    1: �   K: �   ; �   �;    v< r   < E   �< ?   8= [   x= k   �= M   @>    �> Y   �>    �>    ?    (?    :?    M?    `?    s?    �?    �?    �? -   �? -   �? -   @     9@ 2   Z@ +   �@    �@    �@    �@ A   �@    AA    XA 9   `A .   �A T   �A '   B    FB    \B "   pB    �B    �B j   �B X   .C w   �C �   �C    �D    �D    E    .E .   DE 	   sE    }E    �E s   �E    !F "   >F    aF 
   hF 	   sF 5   }F    �F    �F    �G    �G    �G #   H    /H    KH    bH    H "   �H -   �H    �H    I V   I #   jI    �I    �I 8   �I H   �I !   ;J )   ]J    �J j   �J V   K G   iK    �K    �K 	   �K    �K    �K    L �  L u   �M     N    5N '   9N    aN $   rN    �N    �N v   �N <   O �   \O    +P &   <P �   cP &   �P "   Q    1Q !   PQ 2   rQ (   �Q    �Q    �Q 6   �Q    *R !   HR 5   jR   �R /   �S E   �S F   1T    xT |   �T #   U    8U Q   DU    �U $   �U    �U D   �U /   *V !   ZV �   |V    W    'W    7W    WW    hW    �W    �W    �W    �W N   �W �   .X    Y !   5Y �   WY �   $Z    �Z    �Z    �Z    �Z    [    ([    ;[    T[    j[    |[    �[    �[ 
   �[    �[    �[    �[    �[    �[ #   \ �   +\ �   �\ e  �]   F_ �  Ka �   �b e  �c    "e �   +e    f �   0f �   g w  h �   {i G  /j 3   wk �   �k ?   Ql    �l <   �l D   �l h   0m j   �m �   n �   �n �   ko E  p    br �   wr �   *s g   t �   it    u �   (u Y  �u �   Yw �   x    �x P   �x a   ;y �   �y z   �z f   { �   ~{    | !   |    <| E   P| (   �|    �| w   �| =   G} m   �}    �} �   ~ �   �~ ;   4 P   p J   � e   � `   r� L   Ӏ h    � c   �� �   � |   t�    � (   �� $   � Q   C� 3   ��    Ƀ    у B   ؃    �    .�    B� @   I� !   �� A   ��    �    �    �    � 	   � \   '� s   �� [   �� 8   T�    ��    �� )   �� 
   ۆ 	   �    �    ��    �    
�    �    �    �    $�    1� 
   :�    H�    O�    _�    n�    ~� 
   ��    ��    ��    ��    ��    ؇ t   ��    m� Q   �� �   Ԉ 	   ��    �� 
   ��    ��    ��    ��    �� �   ɉ v   j� :   �    �    (� z   =�    �� x   ȋ �   A� !   ˌ    � �   � �   �� X   Z� �   �� _   n� ,   Ώ �   ��    ��    �� F   �� �   ��    y� �   �� �   +� �   �� M   Y�    ��    ��    ��    Γ    ד    �    ��     � �   � w   �� z   *� �   �� �   v� ^   X� E   �� �  �� �  �� �   E� �   @�   ֜ S  ܝ    0� �   9�   � �   � }  �� �  � v   ҥ C  I�    �� =   �� �   � h  s� D   ܩ �   !�    ��    �    
� �   #� 3   �� g   � 2   P� [   �� F   ߬ L   &� %   s� �   �� ]   4� .   �� K   �� �   
�    ݯ  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: da
Language-Team: da <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library er et populært (og ulovligt) bibliotek. De har taget Library Genesis-samlingen og gjort den let søgbar. Oven i det er de blevet meget effektive til at anmode om nye bogbidrag ved at motivere bidragende brugere med forskellige fordele. De bidrager i øjeblikket ikke med disse nye bøger tilbage til Library Genesis. Og i modsætning til Library Genesis gør de ikke deres samling let spejlbar, hvilket forhindrer bred bevaring. Dette er vigtigt for deres forretningsmodel, da de opkræver penge for at få adgang til deres samling i bulk (mere end 10 bøger om dagen). Vi foretager ikke moralske vurderinger om at opkræve penge for masseadgang til en ulovlig bogsamling. Det er uden tvivl, at Z-Library har haft succes med at udvide adgangen til viden og skaffe flere bøger. Vi er her blot for at gøre vores del: at sikre den langsigtede bevarelse af denne private samling. - Anna og teamet (<a %(reddit)s>Reddit</a>) I den oprindelige udgivelse af Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), lavede vi et spejl af Z-Library, en stor ulovlig bogsamling. Som en påmindelse, dette er, hvad vi skrev i det oprindelige blogindlæg: Den samling daterer sig tilbage til midten af 2021. I mellemtiden er Z-Library vokset i et forbløffende tempo: de har tilføjet omkring 3,8 millioner nye bøger. Der er nogle dubletter derinde, selvfølgelig, men størstedelen af det ser ud til at være legitime nye bøger eller højere kvalitetsscanninger af tidligere indsendte bøger. Dette skyldes i høj grad det øgede antal frivillige moderatorer hos Z-Library og deres masse-upload system med deduplikering. Vi vil gerne lykønske dem med disse præstationer. Vi er glade for at kunne meddele, at vi har fået alle bøger, der blev tilføjet til Z-Library mellem vores sidste spejl og august 2022. Vi har også gået tilbage og skrabet nogle bøger, som vi missede første gang. Alt i alt er denne nye samling omkring 24TB, hvilket er meget større end den sidste (7TB). Vores spejl er nu i alt 31TB. Igen deduplikerede vi mod Library Genesis, da der allerede er torrents tilgængelige for den samling. Gå venligst til Pirate Library Mirror for at tjekke den nye samling (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Der er mere information der om, hvordan filerne er struktureret, og hvad der er ændret siden sidst. Vi vil ikke linke til det herfra, da dette blot er en blogside, der ikke hoster ulovligt materiale. Selvfølgelig er seeding også en fantastisk måde at hjælpe os på. Tak til alle, der seeder vores tidligere sæt af torrents. Vi er taknemmelige for den positive respons og glade for, at der er så mange mennesker, der bekymrer sig om bevarelse af viden og kultur på denne usædvanlige måde. 3x nye bøger tilføjet til Pirate Library Mirror (+24TB, 3,8 millioner bøger) Læs de ledsagende artikler af TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>anden</a> - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ledsagende artikler af TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>anden</a> For ikke så længe siden var "skyggebiblioteker" ved at dø ud. Sci-Hub, det massive ulovlige arkiv af akademiske artikler, var stoppet med at tage imod nye værker på grund af retssager. "Z-Library", det største ulovlige bibliotek af bøger, så sine påståede skabere arresteret på grund af kriminelle ophavsretsanklager. De formåede utroligt nok at undslippe deres arrestation, men deres bibliotek er ikke mindre truet. Nogle lande gør allerede en version af dette. TorrentFreak <a %(torrentfreak)s>rapporterede</a>, at Kina og Japan har indført AI-undtagelser i deres ophavsretslove. Det er uklart for os, hvordan dette interagerer med internationale traktater, men det giver bestemt dækning til deres indenlandske virksomheder, hvilket forklarer, hvad vi har set. Hvad angår Annas Arkiv — vi vil fortsætte vores undergrundsarbejde rodfæstet i moralsk overbevisning. Alligevel er vores største ønske at træde frem i lyset og forstærke vores indflydelse lovligt. Venligst reformér ophavsretten. Da Z-Library stod over for lukning, havde jeg allerede sikkerhedskopieret hele biblioteket og søgte efter en platform til at huse det. Det var min motivation for at starte Annas Arkiv: en fortsættelse af missionen bag de tidligere initiativer. Vi er siden vokset til at være det største skyggebibliotek i verden, der huser mere end 140 millioner ophavsretligt beskyttede tekster i forskellige formater — bøger, akademiske artikler, magasiner, aviser og mere. Mit team og jeg er ideologer. Vi mener, at det er moralsk rigtigt at bevare og huse disse filer. Biblioteker rundt om i verden oplever nedskæringer i finansieringen, og vi kan heller ikke stole på, at menneskehedens arv overlades til virksomheder. Så kom AI. Næsten alle større virksomheder, der bygger LLM'er, kontaktede os for at træne på vores data. De fleste (men ikke alle!) amerikanske virksomheder genovervejede, da de indså den ulovlige karakter af vores arbejde. Til gengæld har kinesiske firmaer entusiastisk omfavnet vores samling, tilsyneladende uden at bekymre sig om dens lovlighed. Dette er bemærkelsesværdigt i betragtning af Kinas rolle som underskriver af næsten alle større internationale ophavsretstraktater. Vi har givet højhastighedsadgang til omkring 30 virksomheder. De fleste af dem er LLM-virksomheder, og nogle er databrokere, der vil videresælge vores samling. De fleste er kinesiske, men vi har også arbejdet med virksomheder fra USA, Europa, Rusland, Sydkorea og Japan. DeepSeek <a %(arxiv)s>indrømmede</a>, at en tidligere version blev trænet på en del af vores samling, selvom de er tilbageholdende med at tale om deres nyeste model (sandsynligvis også trænet på vores data). Hvis Vesten vil forblive foran i kapløbet om LLM'er, og i sidste ende AGI, skal den genoverveje sin holdning til ophavsret, og det snart. Uanset om du er enig med os eller ej i vores moralske sag, bliver dette nu en sag om økonomi og endda national sikkerhed. Alle magtblokke bygger kunstige super-videnskabsmænd, super-hackere og super-militærer. Informationsfrihed bliver et spørgsmål om overlevelse for disse lande — endda et spørgsmål om national sikkerhed. Vores team er fra hele verden, og vi har ikke en bestemt tilknytning. Men vi vil opfordre lande med stærke ophavsretslove til at bruge denne eksistentielle trussel til at reformere dem. Så hvad skal man gøre? Vores første anbefaling er ligetil: forkort ophavsretsperioden. I USA gives ophavsret i 70 år efter forfatterens død. Dette er absurd. Vi kan bringe dette i overensstemmelse med patenter, som gives i 20 år efter indgivelse. Dette burde være mere end nok tid for forfattere af bøger, artikler, musik, kunst og andre kreative værker til at blive fuldt kompenseret for deres indsats (inklusive længerevarende projekter som filmatiseringer). Derefter bør politikerne som minimum inkludere undtagelser for massebevaring og formidling af tekster. Hvis tabt indtægt fra individuelle kunder er den største bekymring, kunne distribution på personligt niveau forblive forbudt. Til gengæld ville de, der er i stand til at administrere store arkiver — virksomheder, der træner LLM'er, sammen med biblioteker og andre arkiver — være dækket af disse undtagelser. Ophavsretsreform er nødvendig for national sikkerhed Kort fortalt: Kinesiske LLM'er (inklusive DeepSeek) er trænet på mit ulovlige arkiv af bøger og artikler — det største i verden. Vesten skal revidere ophavsretsloven som et spørgsmål om national sikkerhed. Se venligst <a %(all_isbns)s>det originale blogindlæg</a> for mere information. Vi udstedte en udfordring for at forbedre dette. Vi ville tildele en førstepræmie på $6.000, andenpladsen $3.000, og tredjepladsen $1.000. På grund af den overvældende respons og utrolige indsendelser har vi besluttet at øge præmiepuljen en smule og tildele en delt tredjeplads til fire deltagere med $500 hver. Vinderne er nedenfor, men sørg for at se alle indsendelser <a %(annas_archive)s>her</a>, eller download vores <a %(a_2025_01_isbn_visualization_files)s>kombinerede torrent</a>. Førsteplads $6.000: phiresky Denne <a %(phiresky_github)s>indsendelse</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) er simpelthen alt, hvad vi ønskede, og mere til! Vi kunne især godt lide de utroligt fleksible visualiseringsmuligheder (selv med understøttelse af brugerdefinerede shaders), men med en omfattende liste af forudindstillinger. Vi kunne også godt lide, hvor hurtigt og glat alting er, den enkle implementering (som ikke engang har en backend), det smarte minimap og den omfattende forklaring i deres <a %(phiresky_github)s>blogindlæg</a>. Utroligt arbejde og en velfortjent vinder! - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Vores hjerter er fulde af taknemmelighed. Bemærkelsesværdige idéer Skyskrabere for sjældenhed Mange skydeknapper til at sammenligne datasets, som om du er en DJ. Skalabar med antal bøger. Pæne etiketter. Sejt standardfarveskema og varmekort. Unik kortvisning og filtre Annotationer og også live-statistik Live-statistik Nogle flere idéer og implementeringer, vi især kunne lide: Vi kunne fortsætte et stykke tid, men lad os stoppe her. Sørg for at se alle indsendelser <a %(annas_archive)s>her</a>, eller download vores <a %(a_2025_01_isbn_visualization_files)s>kombinerede torrent</a>. Så mange indsendelser, og hver bringer et unikt perspektiv, hvad enten det er i UI eller implementering. Vi vil i det mindste inkorporere førstepladsens indsendelse på vores hovedwebsite, og måske nogle andre. Vi er også begyndt at tænke over, hvordan vi kan organisere processen med at identificere, bekræfte og derefter arkivere de sjældneste bøger. Mere kommer på denne front. Tak til alle, der deltog. Det er fantastisk, at så mange mennesker bekymrer sig. Nem skift mellem datasets for hurtige sammenligninger. Alle ISBN'er CADAL SSNO'er CERLALC datalæk DuXiu SSID'er EBSCOhost's eBook Index Google Bøger Goodreads Internetarkivet ISBNdb ISBN Global Register of Publishers Libby Filer i Annas Arkiv Nexus/STC OCLC/Worldcat OpenLibrary Det Russiske Statsbibliotek Det Kejserlige Bibliotek i Trantor Andenplads $3.000: hypha "Mens perfekte firkanter og rektangler er matematisk behagelige, giver de ikke overlegen lokalitet i en kortlægningskontekst. Jeg mener, at asymmetrien, der er iboende i disse Hilbert eller klassiske Morton, ikke er en fejl, men en funktion. Ligesom Italiens berømte støvleformede omrids gør det øjeblikkeligt genkendeligt på et kort, kan de unikke 'quirks' ved disse kurver tjene som kognitive landemærker. Denne særprægethed kan forbedre rumlig hukommelse og hjælpe brugere med at orientere sig, hvilket potentielt gør det lettere at finde specifikke regioner eller bemærke mønstre." Endnu en utrolig <a %(annas_archive_note_2913)s>indsendelse</a>. Ikke så fleksibel som førstepladsen, men vi foretrak faktisk dens makroniveau-visualisering frem for førstepladsen (space-filling curve, grænser, mærkning, fremhævning, panorering og zoom). En <a %(annas_archive_note_2971)s>kommentar</a> af Joe Davis resonnerede med os: Og stadig masser af muligheder for visualisering og rendering samt en utrolig glat og intuitiv brugergrænseflade. En solid andenplads! - Anna og teamet (<a %(reddit)s>Reddit</a>) For et par måneder siden annoncerede vi en <a %(all_isbns)s>$10.000 præmie</a> for at lave den bedst mulige visualisering af vores data, der viser ISBN-rummet. Vi lagde vægt på at vise, hvilke filer vi allerede har/ikke har arkiveret, og vi tilføjede senere et datasæt, der beskriver, hvor mange biblioteker der har ISBN'er (et mål for sjældenhed). Vi har været overvældet af responsen. Der har været så meget kreativitet. En stor tak til alle, der har deltaget: jeres energi og entusiasme er smittende! I sidste ende ønskede vi at besvare følgende spørgsmål: <strong>hvilke bøger findes i verden, hvor mange har vi allerede arkiveret, og hvilke bøger skal vi fokusere på næste gang?</strong> Det er fantastisk at se, at så mange mennesker interesserer sig for disse spørgsmål. Vi startede med en grundlæggende visualisering selv. På mindre end 300kb repræsenterer dette billede kortfattet den største fuldt åbne "liste over bøger", der nogensinde er samlet i menneskehedens historie: Tredjeplads $500 #1: maxlion I denne <a %(annas_archive_note_2940)s>indsendelse</a> kunne vi virkelig godt lide de forskellige slags visninger, især sammenlignings- og udgivervisningerne. Tredjeplads $500 #2: abetusk Selvom brugergrænsefladen ikke er den mest polerede, opfylder denne <a %(annas_archive_note_2917)s>indsendelse</a> mange af kriterierne. Vi kunne især godt lide dens sammenligningsfunktion. Tredjeplads $500 #3: conundrumer0 Ligesom førstepladsen imponerede denne <a %(annas_archive_note_2975)s>indsendelse</a> os med sin fleksibilitet. I sidste ende er det, hvad der gør et godt visualiseringsværktøj: maksimal fleksibilitet for avancerede brugere, mens det holdes enkelt for gennemsnitsbrugere. Tredjeplads $500 #4: charelf Den sidste <a %(annas_archive_note_2947)s>indsendelse</a> til at modtage en præmie er ret grundlæggende, men har nogle unikke funktioner, som vi virkelig godt kunne lide. Vi kunne godt lide, hvordan de viser, hvor mange datasets der dækker et bestemt ISBN som et mål for popularitet/pålidelighed. Vi kunne også virkelig godt lide enkelheden, men effektiviteten af at bruge en opacitetsregulator til sammenligninger. Vinderne af $10.000 ISBN-visualiseringspræmien Kort fortalt: Vi fik nogle utrolige indsendelser til $10.000 ISBN-visualiseringspræmien. Baggrund Hvordan kan Annas Arkiv opnå sin mission om at sikkerhedskopiere al menneskehedens viden uden at vide, hvilke bøger der stadig er derude? Vi har brug for en TODO-liste. En måde at kortlægge dette på er gennem ISBN-numre, som siden 1970'erne er blevet tildelt til hver bog, der udgives (i de fleste lande). Der er ingen central myndighed, der kender alle ISBN-tildelinger. I stedet er det et distribueret system, hvor lande får tildelt nummerområder, som derefter tildeler mindre områder til større forlag, der måske yderligere underopdeler områder til mindre forlag. Endelig tildeles individuelle numre til bøger. Vi begyndte at kortlægge ISBN'er <a %(blog)s>for to år siden</a> med vores scraping af ISBNdb. Siden da har vi skrabet mange flere metadata-kilder, såsom <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby og flere. En fuld liste kan findes på siderne "Datasets" og "Torrents" på Annas Arkiv. Vi har nu den suverænt største fuldt åbne, let downloadbare samling af bogmetadata (og dermed ISBN'er) i verden. Vi har <a %(blog)s>skrevet udførligt</a> om, hvorfor vi bekymrer os om bevaring, og hvorfor vi i øjeblikket er i et kritisk vindue. Vi skal nu identificere sjældne, underfokuserede og unikt truede bøger og bevare dem. At have gode metadata på alle bøger i verden hjælper med det. $10.000 dusør Der vil blive lagt stor vægt på brugervenlighed og hvor godt det ser ud. Vis faktiske metadata for individuelle ISBN'er, når du zoomer ind, såsom titel og forfatter. Bedre pladsfyldningskurve. F.eks. en zig-zag, der går fra 0 til 4 på første række og derefter tilbage (i omvendt rækkefølge) fra 5 til 9 på anden række — anvendt rekursivt. Forskellige eller tilpassede farveskemaer. Specielle visninger til sammenligning af datasets. Måder at fejlfinde problemer på, såsom anden metadata der ikke stemmer godt overens (f.eks. meget forskellige titler). Annotering af billeder med kommentarer om ISBN'er eller intervaller. Eventuelle heuristikker til at identificere sjældne eller truede bøger. Hvilke kreative ideer du end kan komme på! Kode Koden til at generere disse billeder, samt andre eksempler, kan findes i <a %(annas_archive)s>dette bibliotek</a>. Vi har udviklet et kompakt dataformat, hvor al den nødvendige ISBN-information fylder omkring 75MB (komprimeret). Beskrivelsen af dataformatet og koden til at generere det kan findes <a %(annas_archive_l1244_1319)s>her</a>. For belønningen er du ikke forpligtet til at bruge dette, men det er sandsynligvis det mest bekvemme format at starte med. Du kan transformere vores metadata, som du vil (dog skal al din kode være open source). Vi kan ikke vente med at se, hvad du finder på. Held og lykke! Fork dette repo, og rediger dette blogindlæg HTML (ingen andre backends end vores Flask-backend er tilladt). Gør billedet ovenfor glat zoom-bart, så du kan zoome helt ind på individuelle ISBN'er. Klik på ISBN'er skal føre dig til en metadata-side eller søgning på Annas Arkiv. Du skal stadig kunne skifte mellem alle forskellige datasets. Landeområder og forlagsområder skal fremhæves ved hover. Du kan f.eks. bruge <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> til landeinfo og vores “isbngrp” scraping til forlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Det skal fungere godt på både desktop og mobil. Der er meget at udforske her, så vi annoncerer en dusør for at forbedre visualiseringen ovenfor. I modsætning til de fleste af vores dusører er denne tidsbegrænset. Du skal <a %(annas_archive)s>indsende</a> din open source-kode inden 2025-01-31 (23:59 UTC). Den bedste indsendelse vil få $6.000, andenpladsen er $3.000, og tredjepladsen er $1.000. Alle dusører vil blive udbetalt i Monero (XMR). Nedenfor er de minimale kriterier. Hvis ingen indsendelse opfylder kriterierne, kan vi stadig tildele nogle dusører, men det vil være efter vores skøn. For bonuspoint (dette er blot ideer — lad din kreativitet løbe løbsk): Du MÅ helt afvige fra de minimale kriterier og lave en helt anden visualisering. Hvis den er virkelig spektakulær, kvalificerer den sig til belønningen, men efter vores skøn. Indsend ved at skrive en kommentar til <a %(annas_archive)s>dette issue</a> med et link til din forkede repo, merge request eller diff. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dette billede er 1000×800 pixels. Hver pixel repræsenterer 2.500 ISBN'er. Hvis vi har en fil for et ISBN, gør vi den pixel mere grøn. Hvis vi ved, at et ISBN er udstedt, men vi ikke har en matchende fil, gør vi den mere rød. På mindre end 300kb repræsenterer dette billede kortfattet den største fuldt åbne "bogliste", der nogensinde er samlet i menneskehedens historie (et par hundrede GB komprimeret i fuld størrelse). Det viser også: der er meget arbejde tilbage med at sikkerhedskopiere bøger (vi har kun 16%). Visualisering af alle ISBN'er — $10.000 dusør inden 2025-01-31 Dette billede repræsenterer den største fuldt åbne "bogliste", der nogensinde er samlet i menneskehedens historie. Visualisering Udover oversigtsbilledet kan vi også se på individuelle datasets, vi har erhvervet. Brug dropdown-menuen og knapperne til at skifte mellem dem. Der er mange interessante mønstre at se i disse billeder. Hvorfor er der en vis regelmæssighed af linjer og blokke, der synes at ske i forskellige skalaer? Hvad er de tomme områder? Hvorfor er visse datasets så klyngede? Vi vil lade disse spørgsmål være en øvelse for læseren. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklusion Med denne standard kan vi lave udgivelser mere gradvist og lettere tilføje nye datakilder. Vi har allerede nogle spændende udgivelser på vej! Vi håber også, at det bliver lettere for andre skyggebiblioteker at spejle vores samlinger. Trods alt er vores mål at bevare menneskelig viden og kultur for evigt, så jo mere redundans, desto bedre. Eksempel Lad os se på vores seneste Z-Library-udgivelse som et eksempel. Den består af to samlinger: “<span style="background: #fffaa3">zlib3_records</span>” og “<span style="background: #ffd6fe">zlib3_files</span>”. Dette giver os mulighed for separat at skrabe og udgive metadataoptegnelser fra de faktiske bogfiler. Som sådan udgav vi to torrents med metadatafiler: Vi udgav også en række torrents med binære datamapper, men kun for “<span style="background: #ffd6fe">zlib3_files</span>” samlingen, i alt 62: Ved at køre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se, hvad der er indeni: I dette tilfælde er det metadata af en bog som rapporteret af Z-Library. På topniveauet har vi kun “aacid” og “metadata”, men ingen “data_folder”, da der ikke er nogen tilsvarende binære data. AACID indeholder “22430000” som det primære ID, hvilket vi kan se er taget fra “zlibrary_id”. Vi kan forvente, at andre AAC'er i denne samling har den samme struktur. Lad os nu køre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Dette er en meget mindre AAC-metadata, selvom størstedelen af denne AAC er placeret et andet sted i en binær fil! Vi har trods alt en “data_folder” denne gang, så vi kan forvente, at de tilsvarende binære data er placeret på <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” indeholder “zlibrary_id”, så vi kan nemt forbinde det med den tilsvarende AAC i “zlib_records” samlingen. Vi kunne have forbundet på en række forskellige måder, f.eks. gennem AACID — standarden foreskriver ikke det. Bemærk, at det heller ikke er nødvendigt for “metadata”-feltet selv at være JSON. Det kunne være en streng, der indeholder XML eller et hvilket som helst andet dataformat. Du kunne endda gemme metadataoplysninger i den tilknyttede binære blob, f.eks. hvis det er en masse data. Heterogene filer og metadata, så tæt på det originale format som muligt. Binære data kan serveres direkte af webservere som Nginx. Heterogene identifikatorer i kildesamlingerne, eller endda mangel på identifikatorer. Separate udgivelser af metadata vs fildata, eller kun metadata-udgivelser (f.eks. vores ISBNdb-udgivelse). Distribution gennem torrents, men med mulighed for andre distributionsmetoder (f.eks. IPFS). Uforanderlige optegnelser, da vi bør antage, at vores torrents vil eksistere for evigt. Inkrementelle udgivelser / tilføjelsesudgivelser. Maskinlæsbare og skrivbare, bekvemt og hurtigt, især for vores stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Nogenlunde let menneskelig inspektion, selvom dette er sekundært i forhold til maskinlæsbarhed. Let at så vores samlinger med en standard lejet seedbox. Designmål Vi er ligeglade med, om filer er lette at navigere manuelt på disk, eller søgbare uden forbehandling. Vi er ligeglade med at være direkte kompatible med eksisterende bibliotekssoftware. Selvom det skal være let for alle at så vores samling ved hjælp af torrents, forventer vi ikke, at filerne er brugbare uden betydelig teknisk viden og engagement. Vores primære anvendelsestilfælde er distribution af filer og tilhørende metadata fra forskellige eksisterende samlinger. Vores vigtigste overvejelser er: Nogle ikke-mål: Da Annas Arkiv er open source, ønsker vi at bruge vores format direkte. Når vi opdaterer vores søgeindeks, har vi kun adgang til offentligt tilgængelige stier, så alle, der forgrener vores bibliotek, hurtigt kan komme i gang. <strong>AAC.</strong> AAC (Anna’s Arkiv Container) er en enkelt enhed bestående af <strong>metadata</strong> og eventuelt <strong>binære data</strong>, som begge er uforanderlige. Den har en globalt unik identifikator, kaldet <strong>AACID</strong>. <strong>AACID.</strong> Formatet for AACID er dette: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. For eksempel er en faktisk AACID, vi har udgivet, <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-interval.</strong> Da AACID'er indeholder monotont stigende tidsstempler, kan vi bruge det til at angive intervaller inden for en bestemt samling. Vi bruger dette format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, hvor tidsstemplerne er inklusive. Dette er i overensstemmelse med ISO 8601-notation. Intervaller er kontinuerlige og kan overlappe, men i tilfælde af overlapning skal de indeholde identiske poster som den, der tidligere blev udgivet i den samling (da AAC'er er uforanderlige). Manglende poster er ikke tilladt. <code>{collection}</code>: samlingsnavnet, som kan indeholde ASCII-bogstaver, tal og understregninger (men ingen dobbelte understregninger). <code>{collection-specific ID}</code>: en samlingsspecifik identifikator, hvis relevant, f.eks. Z-Library ID. Kan udelades eller afkortes. Skal udelades eller afkortes, hvis AACID ellers ville overstige 150 tegn. <code>{ISO 8601 timestamp}</code>: en kort version af ISO 8601, altid i UTC, f.eks. <code>20220723T194746Z</code>. Dette tal skal stige monotont for hver udgivelse, selvom dets præcise semantik kan variere per samling. Vi foreslår at bruge tidspunktet for scraping eller generering af ID'et. <code>{shortuuid}</code>: en UUID men komprimeret til ASCII, f.eks. ved brug af base57. Vi bruger i øjeblikket <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket. <strong>Binær data-mappe.</strong> En mappe med de binære data for et interval af AAC'er, for en bestemt samling. Disse har følgende egenskaber: Mappen skal indeholde datafiler for alle AAC'er inden for det angivne interval. Hver datafil skal have sin AACID som filnavn (ingen filtypenavne). Mappenavnet skal være et AACID-interval, foranstillet med <code style="color: green">annas_archive_data__</code>, og ingen suffix. For eksempel har en af vores faktiske udgivelser en mappe kaldet<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Det anbefales at gøre disse mapper nogenlunde håndterbare i størrelse, f.eks. ikke større end 100GB-1TB hver, selvom denne anbefaling kan ændre sig over tid. <strong>Samling.</strong> Hver AAC tilhører en samling, som per definition er en liste over AAC'er, der er semantisk konsistente. Det betyder, at hvis du foretager en væsentlig ændring i formatet af metadataene, skal du oprette en ny samling. Standarden <strong>Metadata-fil.</strong> En metadata-fil indeholder metadataene for et interval af AAC'er, for en bestemt samling. Disse har følgende egenskaber: <code>data_folder</code> er valgfrit og er navnet på den binære data-mappe, der indeholder de tilsvarende binære data. Filnavnet på de tilsvarende binære data i den mappe er postens AACID. Hvert JSON-objekt skal indeholde følgende felter på øverste niveau: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valgfrit). Ingen andre felter er tilladt. Filnavnet skal være et AACID-interval, foranstillet med <code style="color: red">annas_archive_meta__</code> og efterfulgt af <code>.jsonl.zstd</code>. For eksempel er en af vores udgivelser kaldet<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Som angivet af filtypenavnet er filtypen <a %(jsonlines)s>JSON Lines</a> komprimeret med <a %(zstd)s>Zstandard</a>. <code>metadata</code> er vilkårlige metadata, i henhold til samlingens semantik. Det skal være semantisk konsistent inden for samlingen. Præfikset <code style="color: red">annas_archive_meta__</code> kan tilpasses til navnet på din institution, f.eks. <code style="color: red">my_institute_meta__</code>. <strong>“records” og “files” samlinger.</strong> Af konvention er det ofte praktisk at udgive “records” og “files” som forskellige samlinger, så de kan udgives på forskellige tidspunkter, f.eks. baseret på scraping-hastigheder. En “record” er en samling kun med metadata, der indeholder information som bogtitler, forfattere, ISBN'er osv., mens “files” er samlingerne, der indeholder selve filerne (pdf, epub). I sidste ende besluttede vi os for en relativt enkel standard. Den er ret løs, ikke-normativ og et igangværende arbejde. <strong>Torrents.</strong> Metadatafilerne og binære datamapper kan pakkes i torrents, med én torrent pr. metadatafil eller én torrent pr. binær datamappe. Torrenterne skal have det originale fil-/mappenavn plus et <code>.torrent</code> suffiks som deres filnavn. <a %(wikipedia_annas_archive)s>Annas Arkiv</a> er blevet det suverænt største skyggebibliotek i verden og det eneste skyggebibliotek af sin størrelse, der er fuldt open-source og open-data. Nedenfor er en tabel fra vores Datasets-side (let modificeret): Vi opnåede dette på tre måder: Spejling af eksisterende open-data skyggebiblioteker (som Sci-Hub og Library Genesis). Hjælpe skyggebiblioteker, der ønsker at være mere åbne, men ikke havde tid eller ressourcer til det (som Libgen tegneseriesamlingen). Scraping af biblioteker, der ikke ønsker at dele i bulk (som Z-Library). For (2) og (3) administrerer vi nu en betydelig samling af torrents selv (100'er af TB'er). Indtil nu har vi behandlet disse samlinger som enkeltstående, hvilket betyder skræddersyet infrastruktur og dataorganisation for hver samling. Dette tilføjer betydelig overhead til hver udgivelse og gør det særligt svært at lave mere inkrementelle udgivelser. Derfor har vi besluttet at standardisere vores udgivelser. Dette er et teknisk blogindlæg, hvor vi introducerer vores standard: <strong>Annas Arkiv Beholdere</strong>. Annas Arkiv Beholdere (AAC): standardisering af udgivelser fra verdens største skyggebibliotek Annas Arkiv er blevet verdens største skyggebibliotek, hvilket kræver, at vi standardiserer vores udgivelser. 300GB+ af bogomslag frigivet Endelig er vi glade for at annoncere en lille udgivelse. I samarbejde med folkene, der driver Libgen.rs fork, deler vi alle deres bogomslag gennem torrents og IPFS. Dette vil fordele belastningen af at se omslagene blandt flere maskiner og bevare dem bedre. I mange (men ikke alle) tilfælde er bogomslagene inkluderet i filerne selv, så dette er en slags "afledte data". Men at have det i IPFS er stadig meget nyttigt for den daglige drift af både Annas Arkiv og de forskellige Library Genesis forks. Som sædvanlig kan du finde denne udgivelse på Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi vil ikke linke til det her, men du kan nemt finde det. Forhåbentlig kan vi slappe lidt af nu, hvor vi har et anstændigt alternativ til Z-Library. Denne arbejdsbyrde er ikke særlig bæredygtig. Hvis du er interesseret i at hjælpe med programmering, serverdrift eller bevaringsarbejde, så kontakt os endelig. Der er stadig meget <a %(annas_archive)s>arbejde at gøre</a>. Tak for din interesse og støtte. Skift til ElasticSearch Nogle forespørgsler tog super lang tid, til det punkt hvor de ville optage alle de åbne forbindelser. Som standard har MySQL en minimumsordlængde, ellers kan din indeks blive virkelig stor. Folk rapporterede, at de ikke kunne søge efter "Ben Hur". Søgning var kun nogenlunde hurtig, når den var fuldt indlæst i hukommelsen, hvilket krævede, at vi fik en dyrere maskine til at køre dette på, plus nogle kommandoer til at forindlæse indekset ved opstart. Vi ville ikke have været i stand til nemt at udvide det til at bygge nye funktioner, som bedre <a %(wikipedia_cjk_characters)s>tokenisering for ikke-mellemrumssprog</a>, filtrering/facettering, sortering, "mente du" forslag, autoudfyldning osv. En af vores <a %(annas_archive)s>billetter</a> var en samling af problemer med vores søgesystem. Vi brugte MySQL fuldtekst-søgning, da vi alligevel havde alle vores data i MySQL. Men det havde sine begrænsninger: Efter at have talt med en masse eksperter, besluttede vi os for ElasticSearch. Det har ikke været perfekt (deres standard "mente du" forslag og autoudfyldningsfunktioner er dårlige), men overordnet set har det været meget bedre end MySQL til søgning. Vi er stadig ikke <a %(youtube)s>for begejstrede</a> for at bruge det til nogen missionkritiske data (selvom de har gjort en masse <a %(elastic_co)s>fremskridt</a>), men overordnet set er vi ret tilfredse med skiftet. For nu har vi implementeret meget hurtigere søgning, bedre sprogunderstøttelse, bedre relevanssortering, forskellige sorteringsmuligheder og filtrering på sprog/bogtype/filtype. Hvis du er nysgerrig på, hvordan det fungerer, <a %(annas_archive_l140)s>tag</a> <a %(annas_archive_l1115)s>et</a> <a %(annas_archive_l1635)s>kig</a>. Det er ret tilgængeligt, selvom det kunne bruge nogle flere kommentarer… Annas Arkiv er fuldt open source Vi mener, at information skal være fri, og vores egen kode er ingen undtagelse. Vi har udgivet al vores kode på vores privat hostede Gitlab-instans: <a %(annas_archive)s>Annas Software</a>. Vi bruger også problemsporeren til at organisere vores arbejde. Hvis du vil engagere dig i vores udvikling, er dette et godt sted at starte. For at give dig en forsmag på de ting, vi arbejder på, kan du se vores nylige arbejde med forbedringer af klient-side ydeevne. Da vi endnu ikke har implementeret pagination, ville vi ofte returnere meget lange søgeresultatsider med 100-200 resultater. Vi ønskede ikke at afkorte søgeresultaterne for tidligt, men det betød, at det ville sænke nogle enheder. For dette implementerede vi et lille trick: vi pakkede de fleste søgeresultater ind i HTML-kommentarer (<code><!-- --></code>), og skrev derefter en lille Javascript, der ville opdage, hvornår et resultat skulle blive synligt, på hvilket tidspunkt vi ville pakke kommentaren ud: DOM "virtualisering" implementeret i 23 linjer, ingen grund til fancy biblioteker! Dette er den slags hurtig pragmatisk kode, du ender med, når du har begrænset tid og reelle problemer, der skal løses. Det er blevet rapporteret, at vores søgning nu fungerer godt på langsomme enheder! En anden stor indsats var at automatisere opbygningen af databasen. Da vi lancerede, trak vi bare tilfældigt forskellige kilder sammen. Nu vil vi holde dem opdaterede, så vi skrev en række scripts til at downloade ny metadata fra de to Library Genesis forks og integrere dem. Målet er ikke kun at gøre dette nyttigt for vores arkiv, men også at gøre det nemt for alle, der vil lege med shadow library metadata. Målet ville være en Jupyter-notesbog, der har alle mulige interessante metadata tilgængelige, så vi kan lave mere forskning som at finde ud af, hvilken <a %(blog)s>procentdel af ISBN'er der bevares for evigt</a>. Endelig har vi fornyet vores donationssystem. Du kan nu bruge et kreditkort til direkte at indsætte penge i vores kryptovaluta-tegnebøger uden virkelig at skulle vide noget om kryptovalutaer. Vi vil fortsætte med at overvåge, hvor godt dette fungerer i praksis, men det er en stor ting. Med Z-Library nede og dets (påståede) grundlæggere arresteret, har vi arbejdet døgnet rundt for at tilbyde et godt alternativ med Annas Arkiv (vi vil ikke linke det her, men du kan Google det). Her er nogle af de ting, vi har opnået for nylig. Annas Opdatering: fuldt open source arkiv, ElasticSearch, 300GB+ af bogomslag Vi har arbejdet døgnet rundt for at tilbyde et godt alternativ med Annas Arkiv. Her er nogle af de ting, vi har opnået for nylig. Analyse Semantiske dubletter (forskellige scanninger af den samme bog) kan teoretisk set filtreres ud, men det er vanskeligt. Når vi manuelt kiggede igennem tegneserierne, fandt vi for mange falske positiver. Der er nogle dubletter rent ved MD5, hvilket er relativt spild, men at filtrere dem ud ville kun give os omkring 1% in besparelse. I denne skala er det stadig omkring 1TB, men også, i denne skala betyder 1TB ikke rigtig noget. Vi vil hellere ikke risikere at ødelægge data ved en fejl i denne proces. Vi fandt en bunke ikke-bog data, såsom film baseret på tegneserier. Det virker også spild, da disse allerede er bredt tilgængelige gennem andre midler. Men vi indså, at vi ikke bare kunne filtrere filmfiler ud, da der også er <em>interaktive tegneseriebøger</em>, der blev udgivet på computeren, som nogen optog og gemte som film. I sidste ende ville alt, hvad vi kunne slette fra samlingen, kun spare et par procent. Så huskede vi, at vi er datahoardere, og de mennesker, der vil spejle dette, er også datahoardere, og derfor, "HVAD MENER DU, SLET?!" :) Når du får 95TB dumpet ind i din lagringsklynge, forsøger du at finde ud af, hvad der overhovedet er derinde… Vi lavede nogle analyser for at se, om vi kunne reducere størrelsen lidt, for eksempel ved at fjerne dubletter. Her er nogle af vores fund: Vi præsenterer derfor for dig den fulde, uændrede samling. Det er en masse data, men vi håber, at nok mennesker vil være interesserede i at dele det alligevel. Samarbejde På grund af dens størrelse har denne samling længe været på vores ønskeliste, så efter vores succes med at tage backup af Z-Library, satte vi vores mål på denne samling. Først skrabede vi den direkte, hvilket var en stor udfordring, da deres server ikke var i den bedste stand. Vi fik omkring 15TB på denne måde, men det gik langsomt. Heldigvis lykkedes det os at komme i kontakt med operatøren af biblioteket, som indvilligede i at sende os alle data direkte, hvilket gik meget hurtigere. Det tog stadig mere end et halvt år at overføre og behandle alle dataene, og vi var tæt på at miste det hele til disk-korruption, hvilket ville have betydet at starte forfra. Denne oplevelse har fået os til at tro, at det er vigtigt at få disse data ud så hurtigt som muligt, så de kan spejles vidt og bredt. Vi er kun en eller to uheldige hændelser væk fra at miste denne samling for evigt! Samlingen At bevæge sig hurtigt betyder, at samlingen er lidt uorganiseret… Lad os tage et kig. Forestil dig, at vi har et filsystem (som i virkeligheden bliver delt op på tværs af torrents): Den første mappe, <code>/repository</code>, er den mere strukturerede del af dette. Denne mappe indeholder såkaldte "tusind mapper": mapper hver med tusind filer, som er inkrementelt nummereret i databasen. Mappen <code>0</code> indeholder filer med comic_id 0–999, og så videre. Dette er det samme skema, som Library Genesis har brugt til sine fiktion- og non-fiktion-samlinger. Ideen er, at hver "tusind mappe" automatisk bliver til en torrent, så snart den er fyldt op. Dog lavede Libgen.li-operatøren aldrig torrents for denne samling, og derfor blev tusind mapperne sandsynligvis upraktiske og gav plads til "usorterede mapper". Disse er <code>/comics0</code> til <code>/comics4</code>. De indeholder alle unikke mappestrukturer, der sandsynligvis gav mening for at samle filerne, men som ikke giver så meget mening for os nu. Heldigvis henviser metadataene stadig direkte til alle disse filer, så deres lagringsorganisation på disken betyder faktisk ikke noget! Metadataene er tilgængelige i form af en MySQL-database. Denne kan downloades direkte fra Libgen.li-websitet, men vi vil også gøre den tilgængelig i en torrent, sammen med vores egen tabel med alle MD5-hashene. <q>Dr. Barbara Gordon forsøger at miste sig selv i bibliotekets trivielle verden…</q> Libgen-forke Først lidt baggrund. Du kender måske Library Genesis for deres episke bogsamling. Færre mennesker ved, at Library Genesis-frivillige har skabt andre projekter, såsom en betydelig samling af magasiner og standarddokumenter, en fuld backup af Sci-Hub (i samarbejde med grundlæggeren af Sci-Hub, Alexandra Elbakyan), og faktisk en massiv samling af tegneserier. På et tidspunkt gik forskellige operatører af Library Genesis-spejle hver deres vej, hvilket gav anledning til den nuværende situation med at have en række forskellige "forke", som alle stadig bærer navnet Library Genesis. Libgen.li-forken har unikt denne tegneseriesamling samt en betydelig magasinsamling (som vi også arbejder på). Indsamling Vi frigiver disse data i nogle store bidder. Den første torrent er af <code>/comics0</code>, som vi har samlet i en kæmpe 12TB .tar-fil. Det er bedre for din harddisk og torrent-software end en million mindre filer. Som en del af denne udgivelse laver vi en indsamling. Vi søger at rejse $20.000 til at dække drifts- og kontraktomkostninger for denne samling samt muliggøre igangværende og fremtidige projekter. Vi har nogle <em>enorme</em> i gang. <em>Hvem støtter jeg med min donation?</em> Kort sagt: vi sikkerhedskopierer al menneskehedens viden og kultur og gør det let tilgængeligt. Al vores kode og data er open source, vi er et fuldstændigt frivilligt drevet projekt, og vi har gemt 125TB bøger indtil videre (ud over Libgen og Scihub's eksisterende torrents). I sidste ende bygger vi et svinghjul, der muliggør og motiverer folk til at finde, scanne og sikkerhedskopiere alle bøger i verden. Vi vil skrive om vores masterplan i et fremtidigt indlæg. :) Hvis du donerer for et 12-måneders "Amazing Archivist"-medlemskab ($780), får du lov til at <strong>“adoptere en torrent”</strong>, hvilket betyder, at vi vil sætte dit brugernavn eller besked i filnavnet på en af torrentene! Du kan donere ved at gå til <a %(wikipedia_annas_archive)s>Annas Arkiv</a> og klikke på "Doner"-knappen. Vi søger også flere frivillige: softwareingeniører, sikkerhedsforskere, anonyme handels-eksperter og oversættere. Du kan også støtte os ved at tilbyde hostingtjenester. Og selvfølgelig, del venligst vores torrents! Tak til alle, der allerede har støttet os så generøst! I gør virkelig en forskel. Her er de torrents, der er udgivet indtil videre (vi behandler stadig resten): Alle torrents kan findes på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under "Datasets" (vi linker ikke direkte dertil, så links til denne blog ikke bliver fjernet fra Reddit, Twitter osv.). Derfra kan du følge linket til Tor-websitet. <a %(news_ycombinator)s>Diskuter på Hacker News</a> Hvad er det næste? En masse torrents er gode til langtidsbevaring, men ikke så meget til daglig adgang. Vi vil arbejde med hostingpartnere for at få alle disse data op på nettet (da Annas Arkiv ikke hoster noget direkte). Selvfølgelig vil du kunne finde disse download-links på Annas Arkiv. Vi inviterer også alle til at gøre noget med disse data! Hjælp os med at analysere dem bedre, deduplikere dem, lægge dem på IPFS, remixe dem, træne dine AI-modeller med dem osv. Det er alt dit, og vi kan ikke vente med at se, hvad du gør med det. Endelig, som sagt før, har vi stadig nogle enorme udgivelser på vej (hvis <em>nogen</em> kunne <em>tilfældigvis</em> sende os en dump af en <em>vis</em> ACS4-database, ved du, hvor du kan finde os...), samt bygge svinghjulet til at sikkerhedskopiere alle bøger i verden. Så hold dig opdateret, vi er kun lige begyndt. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Det største shadow-bibliotek af tegneserier er sandsynligvis det fra en bestemt Library Genesis fork: Libgen.li. Den ene administrator, der driver det site, formåede at samle en vanvittig tegneseriesamling på over 2 millioner filer, i alt over 95TB. Men i modsætning til andre Library Genesis-samlinger var denne ikke tilgængelig i bulk gennem torrents. Du kunne kun få adgang til disse tegneserier individuelt gennem hans langsomme personlige server — et enkelt fejlpunkt. Indtil i dag! I dette indlæg vil vi fortælle dig mere om denne samling og om vores indsamlingskampagne for at støtte mere af dette arbejde. Annas Arkiv har sikkerhedskopieret verdens største tegneserie-shadow-bibliotek (95TB) — du kan hjælpe med at seed det Verdens største shadow-bibliotek for tegneserier havde et enkelt fejlpunkt... indtil i dag. Advarsel: dette blogindlæg er blevet forældet. Vi har besluttet, at IPFS endnu ikke er klar til primetime. Vi vil stadig linke til filer på IPFS fra Annas Arkiv, når det er muligt, men vi vil ikke længere hoste det selv, og vi anbefaler heller ikke andre at spejle ved hjælp af IPFS. Se venligst vores Torrents-side, hvis du vil hjælpe med at bevare vores samling. Lægger 5.998.794 bøger på IPFS En mangfoldiggørelse af kopier Tilbage til vores oprindelige spørgsmål: hvordan kan vi hævde at bevare vores samlinger i al evighed? Hovedproblemet her er, at vores samling er <a %(torrents_stats)s>vokset</a> hurtigt ved at scrape og open-source nogle massive samlinger (oven på det fantastiske arbejde, der allerede er udført af andre open-data skyggebiblioteker som Sci-Hub og Library Genesis). Denne vækst i data gør det sværere for samlingerne at blive spejlet rundt om i verden. Datastorage er dyrt! Men vi er optimistiske, især når vi observerer følgende tre tendenser. Den <a %(annas_archive_stats)s>samlede størrelse</a> af vores samlinger, over de sidste par måneder, opdelt efter antal torrent-seeders. HDD-pristrends fra forskellige kilder (klik for at se undersøgelse). <a %(critical_window_chinese)s>Kinesisk version 中文版</a>, diskuter på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Vi har plukket de lavthængende frugter Dette følger direkte af vores prioriteter diskuteret ovenfor. Vi foretrækker at arbejde på at frigøre store samlinger først. Nu hvor vi har sikret nogle af de største samlinger i verden, forventer vi, at vores vækst vil være meget langsommere. Der er stadig en lang hale af mindre samlinger, og nye bøger bliver scannet eller udgivet hver dag, men hastigheden vil sandsynligvis være meget langsommere. Vi kan stadig fordoble eller endda tredoble i størrelse, men over en længere periode. Forbedringer i OCR. Prioriteter Videnskabs- og ingeniørsoftwarekode Fiktive eller underholdningsversioner af alle ovenstående Geografiske data (f.eks. kort, geologiske undersøgelser) Intern data fra virksomheder eller regeringer (lækager) Måledata som videnskabelige målinger, økonomiske data, virksomhedsrapporter Metadata-poster generelt (af faglitteratur og skønlitteratur; af andre medier, kunst, personer osv.; inklusive anmeldelser) Faglitteratur bøger Faglitterære magasiner, aviser, manualer Faglitterære transskriptioner af foredrag, dokumentarer, podcasts Organiske data som DNA-sekvenser, plantefrø eller mikrobielle prøver Akademiske artikler, tidsskrifter, rapporter Videnskabs- og ingeniørwebsites, online diskussioner Transskriptioner af juridiske eller retlige procedurer Unikt i fare for ødelæggelse (f.eks. ved krig, nedskæringer i finansiering, retssager eller politisk forfølgelse) Sjældne Unikt underfokuserede Hvorfor bekymrer vi os så meget om artikler og bøger? Lad os sætte vores grundlæggende tro på bevaring generelt til side — vi kunne skrive et andet indlæg om det. Så hvorfor artikler og bøger specifikt? Svaret er enkelt: <strong>informationsdensitet</strong>. Per megabyte lagerplads gemmer skreven tekst mest information af alle medier. Mens vi bekymrer os om både viden og kultur, bekymrer vi os mere om førstnævnte. Overordnet set finder vi en hierarki af informationsdensitet og vigtigheden af bevaring, der ser nogenlunde sådan ud: Rangeringen på denne liste er noget vilkårlig — flere punkter er uafgjorte eller har uenigheder inden for vores team — og vi glemmer sandsynligvis nogle vigtige kategorier. Men dette er omtrent, hvordan vi prioriterer. Nogle af disse punkter er for forskellige fra de andre til, at vi bekymrer os om dem (eller er allerede taget hånd om af andre institutioner), såsom organiske data eller geografiske data. Men de fleste af punkterne på denne liste er faktisk vigtige for os. En anden stor faktor i vores prioritering er, hvor meget en bestemt værk er i fare. Vi foretrækker at fokusere på værker, der er: Endelig bekymrer vi os om skala. Vi har begrænset tid og penge, så vi vil hellere bruge en måned på at redde 10.000 bøger end 1.000 bøger — hvis de er omtrent lige værdifulde og i fare. <em><q>Det tabte kan ikke genvindes; men lad os redde det, der er tilbage: ikke ved hvælvinger og låse, der beskytter dem fra offentlighedens øjne og brug, ved at overgive dem til tidens spild, men ved en sådan mangfoldiggørelse af kopier, som placerer dem uden for rækkevidde af uheld.</q></em><br>— Thomas Jefferson, 1791 Skyggebiblioteker Kode kan være open source på Github, men Github som helhed kan ikke let spejles og dermed bevares (selvom der i dette særlige tilfælde er tilstrækkeligt distribuerede kopier af de fleste kodearkiver) Metadataoptegnelser kan frit ses på Worldcat-webstedet, men ikke downloades i bulk (indtil vi <a %(worldcat_scrape)s>scrapede</a> dem) Reddit er gratis at bruge, men har for nylig indført strenge anti-scraping foranstaltninger i kølvandet på datahungrende LLM-træning (mere om det senere) Der er mange organisationer, der har lignende missioner og lignende prioriteter. Faktisk er der biblioteker, arkiver, laboratorier, museer og andre institutioner, der har til opgave at bevare denne slags. Mange af dem er velstøttede af regeringer, enkeltpersoner eller virksomheder. Men de har én massiv blind vinkel: det juridiske system. Her ligger den unikke rolle for skyggebiblioteker, og grunden til at Annas Arkiv eksisterer. Vi kan gøre ting, som andre institutioner ikke har lov til at gøre. Nu er det ikke (ofte), at vi kan arkivere materialer, der er ulovlige at bevare andre steder. Nej, det er lovligt mange steder at opbygge et arkiv med alle bøger, papirer, magasiner osv. Men hvad juridiske arkiver ofte mangler, er <strong>redundans og lang levetid</strong>. Der findes bøger, hvoraf der kun eksisterer én kopi i et fysisk bibliotek et sted. Der findes metadataoptegnelser, der er bevogtet af en enkelt virksomhed. Der findes aviser, der kun er bevaret på mikrofilm i et enkelt arkiv. Biblioteker kan få nedskæringer i finansieringen, virksomheder kan gå konkurs, arkiver kan blive bombet og brændt ned til grunden. Dette er ikke hypotetisk — det sker hele tiden. Det, vi unikt kan gøre på Annas Arkiv, er at opbevare mange kopier af værker i stor skala. Vi kan samle artikler, bøger, magasiner og mere og distribuere dem i store mængder. Vi gør dette i øjeblikket gennem torrents, men de præcise teknologier er ikke vigtige og vil ændre sig over tid. Det vigtige er at få mange kopier distribueret over hele verden. Dette citat fra for over 200 år siden er stadig aktuelt: En hurtig bemærkning om public domain. Da Annas Arkiv unikt fokuserer på aktiviteter, der er ulovlige mange steder i verden, bekymrer vi os ikke om bredt tilgængelige samlinger, såsom public domain-bøger. Juridiske enheder tager ofte allerede godt vare på det. Dog er der overvejelser, der gør, at vi nogle gange arbejder på offentligt tilgængelige samlinger: - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Lageromkostninger fortsætter med at falde eksponentielt 3. Forbedringer i informationsdensitet Vi opbevarer i øjeblikket bøger i de rå formater, som de bliver givet til os. De er godt nok komprimerede, men ofte er de stadig store scanninger eller fotografier af sider. Indtil nu har de eneste muligheder for at reducere den samlede størrelse af vores samling været gennem mere aggressiv komprimering eller deduplikering. Men for at opnå betydelige besparelser er begge dele for tabsgivende for vores smag. Kraftig komprimering af fotos kan gøre teksten næsten ulæselig. Og deduplikering kræver høj sikkerhed for, at bøgerne er nøjagtigt de samme, hvilket ofte er for unøjagtigt, især hvis indholdet er det samme, men scanningerne er lavet på forskellige tidspunkter. Der har altid været en tredje mulighed, men dens kvalitet har været så elendig, at vi aldrig overvejede den: <strong>OCR, eller optisk tegngenkendelse</strong>. Dette er processen med at konvertere fotos til almindelig tekst ved hjælp af AI til at genkende tegnene i fotos. Værktøjer til dette har længe eksisteret og har været ret gode, men "ret gode" er ikke nok til bevaringsformål. Dog har nylige multimodale dybdelæringsmodeller gjort ekstremt hurtige fremskridt, selvom de stadig er dyre. Vi forventer, at både nøjagtighed og omkostninger vil forbedres dramatisk i de kommende år, til det punkt hvor det bliver realistisk at anvende på hele vores bibliotek. Når det sker, vil vi sandsynligvis stadig bevare de originale filer, men derudover kunne vi have en meget mindre version af vores bibliotek, som de fleste vil ønske at spejle. Det smarte er, at rå tekst i sig selv komprimerer endnu bedre og er meget lettere at deduplikere, hvilket giver os endnu flere besparelser. Samlet set er det ikke urealistisk at forvente mindst en 5-10x reduktion i den samlede filstørrelse, måske endda mere. Selv med en konservativ 5x reduktion, ville vi se på <strong>1.000–3.000 $ om 10 år, selv hvis vores bibliotek tredobles i størrelse</strong>. På tidspunktet for skrivningen er <a %(diskprices)s>diskpriser</a> pr. TB omkring $12 for nye diske, $8 for brugte diske og $4 for bånd. Hvis vi er konservative og kun ser på nye diske, betyder det, at det koster omkring $12.000 at opbevare en petabyte. Hvis vi antager, at vores bibliotek vil tredoble fra 900TB til 2,7PB, ville det betyde $32.400 for at spejle hele vores bibliotek. Tilføjer vi elektricitet, omkostninger til andet hardware osv., lad os runde det op til $40.000. Eller med bånd mere som $15.000–$20.000. På den ene side er <strong>$15.000–$40.000 for summen af al menneskelig viden et kup</strong>. På den anden side er det lidt stejlt at forvente tonsvis af fulde kopier, især hvis vi også gerne vil have, at de mennesker fortsætter med at seede deres torrents til gavn for andre. Det er i dag. Men fremskridt marcherer fremad: Harddiskomkostninger pr. TB er blevet reduceret til en tredjedel over de sidste 10 år og vil sandsynligvis fortsætte med at falde i et lignende tempo. Bånd ser ud til at være på en lignende bane. SSD-priser falder endnu hurtigere og kan overtage HDD-priserne ved slutningen af årtiet. Hvis dette holder, kan vi om 10 år se på kun $5.000–$13.000 for at spejle hele vores samling (1/3), eller endda mindre, hvis vi vokser mindre i størrelse. Selvom det stadig er mange penge, vil dette være opnåeligt for mange mennesker. Og det kan endda blive bedre på grund af det næste punkt… På Annas Arkiv bliver vi ofte spurgt, hvordan vi kan påstå at bevare vores samlinger i al evighed, når den samlede størrelse allerede nærmer sig 1 Petabyte (1000 TB) og stadig vokser. I denne artikel vil vi se på vores filosofi og se, hvorfor det næste årti er kritisk for vores mission om at bevare menneskehedens viden og kultur. Kritisk vindue Hvis disse prognoser er nøjagtige, skal vi <strong>bare vente et par år</strong>, før hele vores samling vil blive bredt spejlet. Således, med Thomas Jeffersons ord, "placeret uden for rækkevidde af uheld." Desværre har fremkomsten af LLM'er og deres datahungrige træning sat mange ophavsretshavere på defensiven. Endnu mere end de allerede var. Mange hjemmesider gør det sværere at skrabe og arkivere, retssager flyver rundt, og imens fortsætter fysiske biblioteker og arkiver med at blive forsømt. Vi kan kun forvente, at disse tendenser fortsætter med at forværres, og mange værker vil gå tabt længe før de kommer i det offentlige domæne. <strong>Vi står på tærsklen til en revolution inden for bevaring, men <q>det tabte kan ikke genvindes.</q></strong> Vi har et kritisk vindue på omkring 5-10 år, hvor det stadig er ret dyrt at drive et skyggebibliotek og skabe mange spejle rundt om i verden, og hvor adgangen endnu ikke er blevet fuldstændig lukket. Hvis vi kan bygge bro over dette vindue, vil vi faktisk have bevaret menneskehedens viden og kultur for evigt. Vi bør ikke lade denne tid gå til spilde. Vi bør ikke lade dette kritiske vindue lukke for os. Lad os komme i gang. Det kritiske vindue for skyggebiblioteker Hvordan kan vi påstå at bevare vores samlinger i al evighed, når de allerede nærmer sig 1 PB? Samling Lidt mere information om samlingen. <a %(duxiu)s>Duxiu</a> er en massiv database af scannede bøger, skabt af <a %(chaoxing)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøger, scannet for at gøre dem digitalt tilgængelige for universiteter og biblioteker. For vores engelsktalende publikum har <a %(library_princeton)s>Princeton</a> og <a %(guides_lib_uw)s>University of Washington</a> gode oversigter. Der er også en fremragende artikel, der giver mere baggrund: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (find den i Annas Arkiv). Bøgerne fra Duxiu er længe blevet piratkopieret på det kinesiske internet. Normalt bliver de solgt for mindre end en dollar af forhandlere. De distribueres typisk ved hjælp af den kinesiske ækvivalent til Google Drive, som ofte er blevet hacket for at tillade mere lagerplads. Nogle tekniske detaljer kan findes <a %(github_duty_machine)s>her</a> og <a %(github_821_github_io)s>her</a>. Selvom bøgerne er blevet semi-offentligt distribueret, er det ret svært at få dem i bulk. Vi havde dette højt på vores TODO-liste og afsatte flere måneders fuldtidsarbejde til det. Men for nylig kontaktede en utrolig, fantastisk og talentfuld frivillig os og fortalte, at de allerede havde udført alt dette arbejde — til stor udgift. De delte hele samlingen med os uden at forvente noget til gengæld, bortset fra garantien om langsigtet bevaring. Virkelig bemærkelsesværdigt. De gik med til at bede om hjælp på denne måde for at få samlingen OCR'et. Samlingen består af 7.543.702 filer. Dette er mere end Library Genesis non-fiction (omkring 5,3 millioner). Den samlede filstørrelse er omkring 359TB (326TiB) i sin nuværende form. Vi er åbne for andre forslag og idéer. Kontakt os bare. Tjek Annas Arkiv for mere information om vores samlinger, bevaringsindsats og hvordan du kan hjælpe. Tak! Eksempelsider For at bevise for os, at du har en god pipeline, er her nogle eksempelsider at starte med, fra en bog om superledere. Din pipeline skal korrekt håndtere matematik, tabeller, diagrammer, fodnoter osv. Send dine behandlede sider til vores e-mail. Hvis de ser godt ud, sender vi dig flere privat, og vi forventer, at du hurtigt kan køre din pipeline på dem også. Når vi er tilfredse, kan vi indgå en aftale. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kinesisk version 中文版</a>, <a %(news_ycombinator)s>Diskuter på Hacker News</a> Dette er et kort blogindlæg. Vi leder efter en virksomhed eller institution, der kan hjælpe os med OCR og tekstekstraktion for en massiv samling, vi har erhvervet, i bytte for eksklusiv tidlig adgang. Efter embargo-perioden vil vi selvfølgelig frigive hele samlingen. Akademisk tekst af høj kvalitet er yderst nyttig til træning af LLM'er. Selvom vores samling er kinesisk, bør den også være nyttig til træning af engelske LLM'er: modeller synes at kunne kode koncepter og viden uanset kildesproget. For dette skal teksten udtrækkes fra scanningerne. Hvad får Annas Arkiv ud af det? Fuldtekstsøgning i bøgerne for sine brugere. Fordi vores mål stemmer overens med LLM-udvikleres, leder vi efter en samarbejdspartner. Vi er villige til at give dig <strong>eksklusiv tidlig adgang til denne samling i bulk i 1 år</strong>, hvis du kan udføre korrekt OCR og tekstudtrækning. Hvis du er villig til at dele hele koden til din pipeline med os, vil vi være villige til at forlænge embargoperioden for samlingen. Eksklusiv adgang for LLM-virksomheder til verdens største kinesiske faglitterære bogsamling <em><strong>Kort fortalt:</strong> Annas Arkiv har erhvervet en unik samling af 7,5 millioner / 350TB kinesiske faglitterære bøger — større end Library Genesis. Vi er villige til at give en LLM-virksomhed eksklusiv adgang i bytte for høj kvalitet OCR og tekstekstraktion.</em> Systemarkitektur Så lad os sige, at du har fundet nogle virksomheder, der er villige til at hoste dit websted uden at lukke dig ned — lad os kalde dem “frihedselskende udbydere” 😄. Du vil hurtigt opdage, at det er ret dyrt at hoste alt hos dem, så du vil måske finde nogle “billige udbydere” og gøre den faktiske hosting der, ved at proxy gennem de frihedselskende udbydere. Hvis du gør det rigtigt, vil de billige udbydere aldrig vide, hvad du hoster, og aldrig modtage nogen klager. Med alle disse udbydere er der en risiko for, at de alligevel lukker dig ned, så du har også brug for redundans. Vi har brug for dette på alle niveauer af vores stak. En nogenlunde frihedselskende virksomhed, der har sat sig selv i en interessant position, er Cloudflare. De har <a %(blog_cloudflare)s>argumenteret</a> for, at de ikke er en hostingudbyder, men en forsyning, ligesom en internetudbyder. De er derfor ikke underlagt DMCA eller andre anmodninger om nedlukning og videresender eventuelle anmodninger til din faktiske hostingudbyder. De er gået så langt som at gå i retten for at beskytte denne struktur. Vi kan derfor bruge dem som et andet lag af caching og beskyttelse. Cloudflare accepterer ikke anonyme betalinger, så vi kan kun bruge deres gratis plan. Dette betyder, at vi ikke kan bruge deres load balancing eller failover-funktioner. Vi har derfor <a %(annas_archive_l255)s>implementeret dette selv</a> på domæneniveau. Ved sideindlæsning vil browseren kontrollere, om det aktuelle domæne stadig er tilgængeligt, og hvis ikke, omskriver den alle URL'er til et andet domæne. Da Cloudflare cacher mange sider, betyder det, at en bruger kan lande på vores hoveddomæne, selvom proxyserveren er nede, og derefter ved næste klik blive flyttet over til et andet domæne. Vi har stadig også normale operationelle bekymringer at håndtere, såsom overvågning af serverens sundhed, logning af backend- og frontend-fejl og så videre. Vores failover-arkitektur tillader mere robusthed på denne front også, for eksempel ved at køre et helt andet sæt servere på et af domænerne. Vi kan endda køre ældre versioner af koden og datasæt på dette separate domæne, i tilfælde af at en kritisk fejl i hovedversionen går ubemærket hen. Vi kan også gardere os mod, at Cloudflare vender sig mod os, ved at fjerne det fra et af domænerne, såsom dette separate domæne. Forskellige permutationer af disse ideer er mulige. Konklusion Det har været en interessant oplevelse at lære, hvordan man opsætter en robust og modstandsdygtig skyggebibliotekssøgemaskine. Der er mange flere detaljer at dele i senere indlæg, så lad mig vide, hvad du gerne vil lære mere om! Som altid søger vi donationer for at støtte dette arbejde, så sørg for at tjekke donationssiden på Annas Arkiv. Vi leder også efter andre former for støtte, såsom tilskud, langsigtede sponsorer, højrisiko betalingsudbydere, måske endda (smagfulde!) annoncer. Og hvis du vil bidrage med din tid og dine færdigheder, leder vi altid efter udviklere, oversættere osv. Tak for din interesse og støtte. Innovationstokens Lad os starte med vores teknologistak. Den er bevidst kedelig. Vi bruger Flask, MariaDB og ElasticSearch. Det er bogstaveligt talt det. Søgefunktioner er stort set et løst problem, og vi har ikke til hensigt at genopfinde det. Desuden skal vi bruge vores <a %(mcfunley)s>innovationstokens</a> på noget andet: ikke at blive lukket ned af myndighederne. Så hvor lovlig eller ulovlig er Annas Arkiv egentlig? Det afhænger mest af den juridiske jurisdiktion. De fleste lande tror på en form for ophavsret, hvilket betyder, at personer eller virksomheder tildeles et eksklusivt monopol på visse typer værker i en bestemt periode. Som en sidebemærkning mener vi hos Annas Arkiv, at selvom der er nogle fordele, er ophavsret samlet set en negativ for samfundet — men det er en historie til en anden gang. Dette eksklusive monopol på visse værker betyder, at det er ulovligt for nogen uden for dette monopol at distribuere disse værker direkte — inklusive os. Men Annas Arkiv er en søgemaskine, der ikke direkte distribuerer disse værker (i det mindste ikke på vores clearnet-websted), så vi burde være okay, ikke? Ikke helt. I mange jurisdiktioner er det ikke kun ulovligt at distribuere ophavsretligt beskyttede værker, men også at linke til steder, der gør det. Et klassisk eksempel på dette er USA's DMCA-lov. Det er den strengeste ende af spektret. I den anden ende af spektret kunne der teoretisk set være lande uden nogen ophavsretslove overhovedet, men disse eksisterer ikke rigtig. Næsten alle lande har en form for ophavsretslovgivning. Håndhævelse er en anden historie. Der er masser af lande med regeringer, der ikke bryder sig om at håndhæve ophavsretsloven. Der er også lande mellem de to ekstremer, som forbyder distribution af ophavsretligt beskyttede værker, men ikke forbyder at linke til sådanne værker. En anden overvejelse er på virksomhedsplan. Hvis en virksomhed opererer i en jurisdiktion, der ikke bryder sig om ophavsret, men virksomheden selv ikke er villig til at tage nogen risiko, kan de lukke dit websted, så snart nogen klager over det. Endelig er en stor overvejelse betalinger. Da vi skal forblive anonyme, kan vi ikke bruge traditionelle betalingsmetoder. Dette efterlader os med kryptovalutaer, og kun en lille delmængde af virksomheder understøtter dem (der er virtuelle debetkort betalt med krypto, men de accepteres ofte ikke). - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Jeg driver <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største open-source non-profit søgemaskine for <a %(wikipedia_shadow_library)s>skyggebiblioteker</a>, som Sci-Hub, Library Genesis og Z-Library. Vores mål er at gøre viden og kultur let tilgængelig og i sidste ende at opbygge et fællesskab af mennesker, der sammen arkiverer og bevarer <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle bøger i verden</a>. I denne artikel vil jeg vise, hvordan vi driver denne hjemmeside, og de unikke udfordringer, der følger med at drive en hjemmeside med tvivlsom juridisk status, da der ikke er nogen “AWS for skyggevelgørenheder”. <em>Tjek også søsterartiklen <a %(blog_how_to_become_a_pirate_archivist)s>Hvordan man bliver en piratarkivar</a>.</em> Sådan driver du et skyggebibliotek: operationer på Annas Arkiv Der er ingen <q>AWS for skyggevelgørenheder,</q> så hvordan driver vi Annas Arkiv? Værktøjer Applikationsserver: Flask, MariaDB, ElasticSearch, Docker. Udvikling: Gitlab, Weblate, Zulip. Serveradministration: Ansible, Checkmk, UFW. Onion statisk hosting: Tor, Nginx. Proxyserver: Varnish. Lad os se på, hvilke værktøjer vi bruger til at opnå alt dette. Dette udvikler sig meget, efterhånden som vi støder på nye problemer og finder nye løsninger. Der er nogle beslutninger, som vi har gået frem og tilbage med. En af dem er kommunikationen mellem servere: vi plejede at bruge Wireguard til dette, men fandt ud af, at det lejlighedsvis stopper med at transmittere data, eller kun transmitterer data i én retning. Dette skete med flere forskellige Wireguard-opsætninger, som vi prøvede, såsom <a %(github_costela_wesher)s>wesher</a> og <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi prøvede også at tunnelere porte over SSH ved hjælp af autossh og sshuttle, men stødte på <a %(github_sshuttle)s>problemer der</a> (selvom det stadig ikke er klart for mig, om autossh lider af TCP-over-TCP problemer eller ej — det føles bare som en klodset løsning for mig, men måske er det faktisk fint?). I stedet gik vi tilbage til direkte forbindelser mellem servere, og skjulte at en server kører på de billige udbydere ved hjælp af IP-filtrering med UFW. Dette har den ulempe, at Docker ikke fungerer godt med UFW, medmindre du bruger <code>network_mode: "host"</code>. Alt dette er lidt mere fejlbehæftet, fordi du vil udsætte din server for internettet med bare en lille fejlkonfiguration. Måske skulle vi gå tilbage til autossh — feedback ville være meget velkommen her. Vi har også gået frem og tilbage mellem Varnish og Nginx. Vi kan i øjeblikket godt lide Varnish, men det har sine særheder og ru kanter. Det samme gælder for Checkmk: vi elsker det ikke, men det fungerer for nu. Weblate har været okay, men ikke fantastisk — jeg frygter nogle gange, at det vil miste mine data, når jeg prøver at synkronisere det med vores git-repo. Flask har generelt været god, men det har nogle mærkelige særheder, der har kostet meget tid at fejlfinde, såsom at konfigurere brugerdefinerede domæner eller problemer med dets SqlAlchemy-integration. Indtil videre har de andre værktøjer været fantastiske: vi har ingen alvorlige klager over MariaDB, ElasticSearch, Gitlab, Zulip, Docker og Tor. Alle disse har haft nogle problemer, men intet alt for alvorligt eller tidskrævende. Fællesskab Den første udfordring kan være en overraskende en. Det er ikke et teknisk problem eller et juridisk problem. Det er et psykologisk problem: at udføre dette arbejde i skyggerne kan være utroligt ensomt. Afhængigt af hvad du planlægger at gøre, og din trusselsmodel, kan du være nødt til at være meget forsigtig. I den ene ende af spektret har vi folk som Alexandra Elbakyan*, grundlæggeren af Sci-Hub, der er meget åben om sine aktiviteter. Men hun er i høj risiko for at blive arresteret, hvis hun skulle besøge et vestligt land på nuværende tidspunkt, og kunne stå over for årtier i fængsel. Er det en risiko, du er villig til at tage? Vi er i den anden ende af spektret; meget forsigtige med ikke at efterlade nogen spor og have stærk operationel sikkerhed. * Som nævnt på HN af "ynno", ønskede Alexandra oprindeligt ikke at være kendt: "Hendes servere var sat op til at udsende detaljerede fejlmeddelelser fra PHP, inklusive fuld sti til den fejlende kildefil, som var under biblioteket /home/<USER>" Så brug tilfældige brugernavne på de computere, du bruger til dette, i tilfælde af at du konfigurerer noget forkert. Den hemmelighed kommer dog med en psykologisk omkostning. De fleste mennesker elsker at blive anerkendt for det arbejde, de udfører, og alligevel kan du ikke tage nogen kredit for dette i det virkelige liv. Selv simple ting kan være udfordrende, som når venner spørger dig, hvad du har lavet (på et tidspunkt bliver "rode med min NAS / homelab" gammelt). Derfor er det så vigtigt at finde et fællesskab. Du kan give afkald på noget operationel sikkerhed ved at betro dig til nogle meget nære venner, som du ved, du kan stole dybt på. Selv da skal du være forsigtig med ikke at skrive noget ned, i tilfælde af at de skal overdrage deres e-mails til myndighederne, eller hvis deres enheder er kompromitteret på en anden måde. Endnu bedre er det at finde nogle medpirater. Hvis dine nære venner er interesserede i at slutte sig til dig, fantastisk! Ellers kan du muligvis finde andre online. Desværre er dette stadig et nichefællesskab. Indtil videre har vi kun fundet en håndfuld andre, der er aktive på dette område. Gode startsteder synes at være Library Genesis-foraene og r/DataHoarder. Archive Team har også ligesindede individer, selvom de opererer inden for loven (selv hvis i nogle grå områder af loven). De traditionelle "warez" og piratscener har også folk, der tænker på lignende måder. Vi er åbne for idéer om, hvordan vi kan fremme fællesskabet og udforske idéer. Du er velkommen til at sende os en besked på Twitter eller Reddit. Måske kunne vi være vært for en form for forum eller chatgruppe. En udfordring er, at dette let kan blive censureret, når man bruger almindelige platforme, så vi ville være nødt til at hoste det selv. Der er også en afvejning mellem at have disse diskussioner helt offentlige (mere potentiel engagement) versus at gøre det privat (ikke lade potentielle "mål" vide, at vi er ved at skrabe dem). Vi må tænke over det. Lad os vide, hvis du er interesseret i dette! Konklusion Forhåbentlig er dette nyttigt for nybegyndere inden for piratarkivering. Vi er begejstrede for at byde dig velkommen til denne verden, så tøv ikke med at række ud. Lad os bevare så meget af verdens viden og kultur som muligt og spejle det vidt og bredt. Projekter 4. Dataudvælgelse Ofte kan du bruge metadataene til at finde ud af et rimeligt delmængde af data at downloade. Selv hvis du til sidst ønsker at downloade alle dataene, kan det være nyttigt at prioritere de vigtigste elementer først, i tilfælde af at du bliver opdaget og forsvarene forbedres, eller fordi du skal købe flere diske, eller simpelthen fordi noget andet dukker op i dit liv, før du kan downloade alt. For eksempel kan en samling have flere udgaver af den samme underliggende ressource (som en bog eller en film), hvor en er markeret som værende af den bedste kvalitet. At gemme disse udgaver først ville give meget mening. Du vil måske til sidst gemme alle udgaver, da metadataene i nogle tilfælde kan være forkert mærket, eller der kan være ukendte kompromiser mellem udgaverne (for eksempel kan "den bedste udgave" være bedst på de fleste måder, men værre på andre måder, som en film med højere opløsning men uden undertekster). Du kan også søge i din metadata-database for at finde interessante ting. Hvad er den største fil, der er hostet, og hvorfor er den så stor? Hvad er den mindste fil? Er der interessante eller uventede mønstre, når det kommer til bestemte kategorier, sprog osv.? Er der dubletter eller meget lignende titler? Er der mønstre for, hvornår data blev tilføjet, som en dag, hvor mange filer blev tilføjet på én gang? Du kan ofte lære meget ved at se på datasættet på forskellige måder. I vores tilfælde deduplikerede vi Z-Library bøger mod md5-hashene i Library Genesis, hvilket sparer meget downloadtid og diskplads. Dette er dog en ret unik situation. I de fleste tilfælde er der ingen omfattende databaser over, hvilke filer der allerede er ordentligt bevaret af andre pirater. Dette er i sig selv en stor mulighed for nogen derude. Det ville være fantastisk at have en regelmæssigt opdateret oversigt over ting som musik og film, der allerede er bredt seedet på torrent-websteder, og derfor er lavere prioritet at inkludere i piratspejle. 6. Distribution Du har dataene, hvilket giver dig besiddelse af verdens første piratspejl af dit mål (højst sandsynligt). På mange måder er den sværeste del overstået, men den mest risikable del ligger stadig foran dig. Trods alt har du indtil nu været i det skjulte; fløjet under radaren. Alt, hvad du skulle gøre, var at bruge en god VPN hele vejen igennem, ikke udfylde dine personlige oplysninger i nogen formularer (duh), og måske bruge en speciel browsersession (eller endda en anden computer). Nu skal du distribuere dataene. I vores tilfælde ønskede vi først at bidrage med bøgerne tilbage til Library Genesis, men opdagede hurtigt vanskelighederne ved det (fiktion vs. ikke-fiktion sortering). Så vi besluttede os for distribution ved hjælp af Library Genesis-stil torrents. Hvis du har mulighed for at bidrage til et eksisterende projekt, kan det spare dig for meget tid. Der er dog ikke mange velorganiserede piratspejle derude i øjeblikket. Så lad os sige, at du beslutter dig for at distribuere torrents selv. Prøv at holde disse filer små, så de er nemme at spejle på andre websteder. Du skal derefter selv seede torrents, mens du stadig forbliver anonym. Du kan bruge en VPN (med eller uden port forwarding), eller betale med tumbled Bitcoins for en Seedbox. Hvis du ikke ved, hvad nogle af disse termer betyder, har du en masse læsning at gøre, da det er vigtigt, at du forstår risikohandlingerne her. Du kan hoste torrentfilerne selv på eksisterende torrent-websteder. I vores tilfælde valgte vi faktisk at hoste en hjemmeside, da vi også ønskede at sprede vores filosofi på en klar måde. Du kan gøre dette selv på en lignende måde (vi bruger Njalla til vores domæner og hosting, betalt med tumbled Bitcoins), men du er også velkommen til at kontakte os for at få os til at hoste dine torrents. Vi ønsker at opbygge en omfattende indeks over piratspejle over tid, hvis denne idé fanger an. Hvad angår VPN-valg, er der allerede skrevet meget om dette, så vi gentager blot det generelle råd om at vælge efter omdømme. Faktiske retssagstestede no-log-politikker med lange track records for at beskytte privatlivets fred er den laveste risikomulighed, efter vores mening. Bemærk, at selv når du gør alt rigtigt, kan du aldrig komme til nul risiko. For eksempel, når du seeder dine torrents, kan en højt motiveret statslig aktør sandsynligvis se på indgående og udgående dataflow for VPN-servere og udlede, hvem du er. Eller du kan simpelthen lave en fejl. Vi har sandsynligvis allerede gjort det, og vil gøre det igen. Heldigvis er nationer ikke så interesserede i pirateri. En beslutning, der skal træffes for hvert projekt, er, om det skal offentliggøres med den samme identitet som før eller ej. Hvis du fortsætter med at bruge det samme navn, kan fejl i operationel sikkerhed fra tidligere projekter komme tilbage og bide dig. Men at offentliggøre under forskellige navne betyder, at du ikke opbygger et længerevarende omdømme. Vi valgte at have stærk operationel sikkerhed fra starten, så vi kan fortsætte med at bruge den samme identitet, men vi vil ikke tøve med at offentliggøre under et andet navn, hvis vi laver en fejl, eller hvis omstændighederne kræver det. At få ordet ud kan være vanskeligt. Som vi sagde, er dette stadig et nichefællesskab. Vi postede oprindeligt på Reddit, men fik virkelig opmærksomhed på Hacker News. For nu er vores anbefaling at poste det nogle få steder og se, hvad der sker. Og igen, kontakt os. Vi vil elske at sprede ordet om flere piratarkivisme-indsatser. 1. Domænevalg / filosofi Der er ingen mangel på viden og kulturarv, der skal bevares, hvilket kan være overvældende. Derfor er det ofte nyttigt at tage et øjeblik og tænke over, hvad dit bidrag kan være. Alle har en forskellig måde at tænke over dette på, men her er nogle spørgsmål, du kunne stille dig selv: I vores tilfælde var vi særligt interesserede i den langsigtede bevaring af videnskab. Vi vidste om Library Genesis, og hvordan det blev fuldt spejlet mange gange ved hjælp af torrents. Vi elskede den idé. Så en dag forsøgte en af os at finde nogle videnskabelige lærebøger på Library Genesis, men kunne ikke finde dem, hvilket satte spørgsmålstegn ved, hvor komplet det egentlig var. Vi søgte derefter disse lærebøger online og fandt dem andre steder, hvilket plantede frøet til vores projekt. Selv før vi vidste om Z-Library, havde vi idéen om ikke at forsøge at samle alle disse bøger manuelt, men at fokusere på at spejle eksisterende samlinger og bidrage dem tilbage til Library Genesis. Hvilke færdigheder har du, som du kan bruge til din fordel? For eksempel, hvis du er ekspert i online sikkerhed, kan du finde måder at overvinde IP-blokeringer for sikre mål. Hvis du er god til at organisere fællesskaber, kan du måske samle nogle mennesker omkring et mål. Det er dog nyttigt at kende noget programmering, om ikke andet for at opretholde god operationel sikkerhed gennem denne proces. Hvad ville være et område med høj indflydelse at fokusere på? Hvis du vil bruge X timer på piratarkivering, hvordan kan du så få mest muligt ud af det? Hvad er unikke måder, du tænker over dette på? Du kan have nogle interessante idéer eller tilgange, som andre måske har overset. Hvor meget tid har du til dette? Vores råd ville være at starte småt og lave større projekter, efterhånden som du får styr på det, men det kan blive altopslugende. Hvorfor er du interesseret i dette? Hvad brænder du for? Hvis vi kan få en flok mennesker, der alle arkiverer de ting, de specifikt interesserer sig for, ville det dække meget! Du vil vide meget mere end den gennemsnitlige person om din passion, som hvad der er vigtig data at gemme, hvad der er de bedste samlinger og online fællesskaber, og så videre. 3. Metadata-scraping Dato tilføjet/modificeret: så du kan komme tilbage senere og downloade filer, som du ikke downloadede før (selvom du ofte også kan bruge ID eller hash til dette). Hash (md5, sha1): for at bekræfte, at du har downloadet filen korrekt. ID: kan være et internt ID, men ID'er som ISBN eller DOI er også nyttige. Filnavn / placering Beskrivelse, kategori, tags, forfattere, sprog osv. Størrelse: for at beregne, hvor meget diskplads du har brug for. Lad os blive lidt mere tekniske her. For faktisk at skrabe metadata fra hjemmesider har vi holdt tingene ret enkle. Vi bruger Python-scripts, nogle gange curl, og en MySQL-database til at gemme resultaterne i. Vi har ikke brugt nogen fancy scraping-software, der kan kortlægge komplekse hjemmesider, da vi indtil videre kun har haft brug for at skrabe en eller to slags sider ved blot at enumerere gennem id'er og analysere HTML'en. Hvis der ikke er let enumererede sider, kan du have brug for en ordentlig crawler, der forsøger at finde alle sider. Før du begynder at skrabe en hel hjemmeside, så prøv at gøre det manuelt i et stykke tid. Gå igennem et par dusin sider selv for at få en fornemmelse af, hvordan det fungerer. Nogle gange vil du allerede støde på IP-blokeringer eller anden interessant adfærd på denne måde. Det samme gælder for datascraping: før du går for dybt ind i dette mål, skal du sikre dig, at du faktisk kan downloade dets data effektivt. For at omgå restriktioner er der et par ting, du kan prøve. Er der andre IP-adresser eller servere, der hoster de samme data, men ikke har de samme restriktioner? Er der nogen API-endepunkter, der ikke har restriktioner, mens andre har? Ved hvilken downloadhastighed bliver din IP blokeret, og hvor længe? Eller bliver du ikke blokeret, men neddroslet? Hvad hvis du opretter en brugerkonto, hvordan ændrer tingene sig så? Kan du bruge HTTP/2 til at holde forbindelser åbne, og øger det den hastighed, hvormed du kan anmode om sider? Er der sider, der lister flere filer på én gang, og er de oplysninger, der er angivet der, tilstrækkelige? Ting, du sandsynligvis vil gemme, inkluderer: Vi gør typisk dette i to faser. Først downloader vi de rå HTML-filer, normalt direkte ind i MySQL (for at undgå mange små filer, som vi taler mere om nedenfor). Derefter, i et separat trin, gennemgår vi disse HTML-filer og parser dem til faktiske MySQL-tabeller. På denne måde behøver du ikke at gen-downloade alt fra bunden, hvis du opdager en fejl i din parseringskode, da du blot kan genbehandle HTML-filerne med den nye kode. Det er også ofte lettere at parallelisere behandlingsfasen, hvilket sparer tid (og du kan skrive behandlingskoden, mens scraping kører, i stedet for at skulle skrive begge trin på én gang). Endelig skal det bemærkes, at for nogle mål er metadata scraping alt, hvad der er. Der findes nogle enorme metadata-samlinger derude, som ikke er ordentligt bevaret. Titel Domænevalg / filosofi: Hvor vil du nogenlunde fokusere, og hvorfor? Hvad er dine unikke passioner, færdigheder og omstændigheder, som du kan bruge til din fordel? Målvalg: Hvilken specifik samling vil du spejle? Metadata-skrabning: Katalogisering af information om filerne uden faktisk at downloade de (ofte meget større) filer selv. Datavalg: Baseret på metadataene, indsnævring af hvilken data der er mest relevant at arkivere lige nu. Det kunne være alt, men ofte er der en rimelig måde at spare plads og båndbredde på. Data-skrabning: Faktisk at få fat i dataene. Distribution: Pakke det sammen i torrents, annoncere det et sted, få folk til at sprede det. 5. Datascraping Nu er du klar til faktisk at downloade dataene i bulk. Som nævnt tidligere, på dette tidspunkt bør du allerede manuelt have downloadet en bunke filer for bedre at forstå målgruppens adfærd og begrænsninger. Der vil dog stadig være overraskelser i vente for dig, når du faktisk begynder at downloade mange filer på én gang. Vores råd her er hovedsageligt at holde det simpelt. Start med blot at downloade en bunke filer. Du kan bruge Python og derefter udvide til flere tråde. Men nogle gange er det endnu enklere at generere Bash-filer direkte fra databasen og derefter køre flere af dem i flere terminalvinduer for at skalere op. Et hurtigt teknisk trick, der er værd at nævne her, er at bruge OUTFILE i MySQL, som du kan skrive hvor som helst, hvis du deaktiverer "secure_file_priv" i mysqld.cnf (og sørg også for at deaktivere/overstyre AppArmor, hvis du er på Linux). Vi gemmer dataene på simple harddiske. Start med det, du har, og udvid langsomt. Det kan være overvældende at tænke på at gemme hundreder af TB data. Hvis det er den situation, du står overfor, så læg først en god delmængde ud, og i din meddelelse bed om hjælp til at gemme resten. Hvis du selv vil have flere harddiske, så har r/DataHoarder nogle gode ressourcer til at få gode tilbud. Prøv ikke at bekymre dig for meget om fancy filsystemer. Det er nemt at falde ned i kaninhullet med at opsætte ting som ZFS. En teknisk detalje at være opmærksom på er dog, at mange filsystemer ikke håndterer mange filer godt. Vi har fundet ud af, at en simpel løsning er at oprette flere mapper, f.eks. for forskellige ID-intervaller eller hash-præfikser. Efter at have downloadet dataene, skal du sørge for at kontrollere filernes integritet ved hjælp af hashene i metadataene, hvis tilgængelige. 2. Målvalg Tilgængelig: bruger ikke mange lag af beskyttelse for at forhindre dig i at skrabe deres metadata og data. Speciel indsigt: du har nogle særlige oplysninger om dette mål, som at du på en eller anden måde har særlig adgang til denne samling, eller du har fundet ud af, hvordan du overvinder deres forsvar. Dette er ikke nødvendigt (vores kommende projekt gør ikke noget specielt), men det hjælper bestemt! Stor Så, vi har vores område, som vi kigger på, men hvilken specifik samling skal vi spejle? Der er et par ting, der gør et godt mål: Da vi fandt vores naturvidenskabelige lærebøger på andre hjemmesider end Library Genesis, forsøgte vi at finde ud af, hvordan de fandt vej til internettet. Vi fandt derefter Z-Library og indså, at mens de fleste bøger ikke først dukker op der, ender de til sidst der. Vi lærte om dets forhold til Library Genesis og den (økonomiske) incitamentsstruktur og overlegne brugergrænseflade, som begge gjorde det til en meget mere komplet samling. Vi foretog derefter nogle indledende metadata- og datascraping og indså, at vi kunne omgå deres IP-downloadbegrænsninger ved at udnytte en af vores medlemmers særlige adgang til mange proxyservere. Når du udforsker forskellige mål, er det allerede vigtigt at skjule dine spor ved at bruge VPN'er og engangse-mailadresser, som vi vil tale mere om senere. Unik: ikke allerede godt dækket af andre projekter. Når vi laver et projekt, har det et par faser: Disse er ikke helt uafhængige faser, og ofte sender indsigter fra en senere fase dig tilbage til en tidligere fase. For eksempel, under metadata-skrabning kan du indse, at det mål, du har valgt, har forsvarsmekanismer ud over dit færdighedsniveau (som IP-blokeringer), så du går tilbage og finder et andet mål. - Anna og teamet (<a %(reddit)s>Reddit</a>) Hele bøger kan skrives om <em>hvorfor</em> digital bevaring generelt, og piratarkivisme i særdeleshed, men lad os give en hurtig introduktion til dem, der ikke er så bekendte. Verden producerer mere viden og kultur end nogensinde før, men også mere af det går tabt end nogensinde før. Menneskeheden stoler i vid udstrækning på virksomheder som akademiske forlag, streamingtjenester og sociale medievirksomheder med denne arv, og de har ofte ikke vist sig at være gode forvaltere. Se dokumentaren Digital Amnesia, eller virkelig enhver tale af Jason Scott. Der er nogle institutioner, der gør et godt stykke arbejde med at arkivere så meget som muligt, men de er bundet af loven. Som pirater er vi i en unik position til at arkivere samlinger, som de ikke kan røre ved, på grund af ophavsrets håndhævelse eller andre restriktioner. Vi kan også spejle samlinger mange gange over hele verden, hvilket øger chancerne for korrekt bevaring. For nu vil vi ikke gå ind i diskussioner om fordele og ulemper ved intellektuel ejendom, moralen ved at bryde loven, spekulationer om censur eller spørgsmålet om adgang til viden og kultur. Med alt det af vejen, lad os dykke ned i <em>hvordan</em>. Vi vil dele, hvordan vores team blev piratarkivarer, og de erfaringer, vi lærte undervejs. Der er mange udfordringer, når du begiver dig ud på denne rejse, og forhåbentlig kan vi hjælpe dig igennem nogle af dem. Sådan bliver du en piratarkivar Den første udfordring kan være en overraskende en. Det er ikke et teknisk problem eller et juridisk problem. Det er et psykologisk problem. Før vi dykker ned, to opdateringer om Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>): Vi fik nogle ekstremt generøse donationer. Den første var $10k fra den anonyme person, der også har støttet "bookwarrior", den oprindelige grundlægger af Library Genesis. Særlig tak til bookwarrior for at facilitere denne donation. Den anden var endnu $10k fra en anonym donor, der tog kontakt efter vores sidste udgivelse og blev inspireret til at hjælpe. Vi havde også en række mindre donationer. Mange tak for al jeres generøse støtte. Vi har nogle spændende nye projekter i pipelinen, som dette vil støtte, så hold øje med det. Vi havde nogle tekniske vanskeligheder med størrelsen af vores anden udgivelse, men vores torrents er nu oppe og seeder. Vi fik også et generøst tilbud fra en anonym person om at seede vores samling på deres meget højhastighedsservere, så vi laver en særlig upload til deres maskiner, hvorefter alle andre, der downloader samlingen, bør se en stor forbedring i hastighed. Blogindlæg Hej, jeg er Anna. Jeg skabte <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største skyggebibliotek. Dette er min personlige blog, hvor jeg og mine holdkammerater skriver om piratkopiering, digital bevaring og mere. Forbind med mig på <a %(reddit)s>Reddit</a>. Bemærk, at denne hjemmeside kun er en blog. Vi hoster kun vores egne ord her. Ingen torrents eller andre ophavsretligt beskyttede filer hostes eller linkes her. <strong>Bibliotek</strong> - Ligesom de fleste biblioteker fokuserer vi primært på skriftlige materialer som bøger. Vi kan udvide til andre typer medier i fremtiden. <strong>Spejl</strong> - Vi er udelukkende et spejl af eksisterende biblioteker. Vi fokuserer på bevaring, ikke på at gøre bøger let søgbare og downloadbare (adgang) eller på at fremme et stort fællesskab af mennesker, der bidrager med nye bøger (kilder). <strong>Pirat</strong> - Vi overtræder bevidst ophavsretsloven i de fleste lande. Dette giver os mulighed for at gøre noget, som juridiske enheder ikke kan: sikre, at bøger spejles vidt og bredt. <em>Vi linker ikke til filerne fra denne blog. Find dem selv.</em> - Anna og teamet (<a %(reddit)s>Reddit</a>) Dette projekt (REDIGERET: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) har til formål at bidrage til bevarelse og frigørelse af menneskelig viden. Vi yder vores lille og ydmyge bidrag i fodsporene på de store før os. Fokus for dette projekt er illustreret ved dets navn: Det første bibliotek, vi har spejlet, er Z-Library. Dette er et populært (og ulovligt) bibliotek. De har taget Library Genesis-samlingen og gjort den let søgbar. Derudover er de blevet meget effektive til at anmode om nye bogbidrag ved at belønne bidragende brugere med forskellige fordele. De bidrager i øjeblikket ikke med disse nye bøger tilbage til Library Genesis. Og i modsætning til Library Genesis gør de ikke deres samling let spejlbar, hvilket forhindrer bred bevaring. Dette er vigtigt for deres forretningsmodel, da de opkræver penge for at få adgang til deres samling i bulk (mere end 10 bøger om dagen). Vi foretager ikke moralske vurderinger om at opkræve penge for masseadgang til en ulovlig bogsamling. Det er uden tvivl, at Z-Library har haft succes med at udvide adgangen til viden og skaffe flere bøger. Vi er her blot for at gøre vores del: at sikre den langsigtede bevarelse af denne private samling. Vi vil gerne invitere dig til at hjælpe med at bevare og frigøre menneskelig viden ved at downloade og seede vores torrents. Se projektets side for mere information om, hvordan dataene er organiseret. Vi vil også meget gerne invitere dig til at bidrage med dine idéer til, hvilke samlinger der skal spejles næste gang, og hvordan man gør det. Sammen kan vi opnå meget. Dette er blot et lille bidrag blandt utallige andre. Tak for alt, hvad du gør. Introduktion til Piratbibliotekets Spejl: Bevaring af 7TB bøger (som ikke er i Libgen) 10% of menneskehedens skriftlige arv bevaret for evigt <strong>Google.</strong> De lavede trods alt denne forskning for Google Books. Dog er deres metadata ikke tilgængelige i bulk og ret svære at skrabe. <strong>Forskellige individuelle biblioteksystemer og arkiver.</strong> Der er biblioteker og arkiver, der ikke er blevet indekseret og aggregeret af nogen af de ovenstående, ofte fordi de er underfinansierede, eller af andre grunde ikke ønsker at dele deres data med Open Library, OCLC, Google osv. Mange af disse har digitale optegnelser tilgængelige via internettet, og de er ofte ikke særlig godt beskyttede, så hvis du vil hjælpe og have det sjovt med at lære om mærkelige biblioteksystemer, er disse gode udgangspunkter. <strong>ISBNdb.</strong> Dette er emnet for dette blogindlæg. ISBNdb skraber forskellige hjemmesider for bogmetadata, især prisdata, som de derefter sælger til boghandlere, så de kan prissætte deres bøger i overensstemmelse med resten af markedet. Da ISBN'er er ret universelle i dag, har de effektivt bygget en “webside for hver bog”. <strong>Open Library.</strong> Som nævnt før, er dette deres hele mission. De har hentet enorme mængder af biblioteksdata fra samarbejdende biblioteker og nationale arkiver og fortsætter med at gøre det. De har også frivillige bibliotekarer og et teknisk team, der forsøger at deduplikere poster og mærke dem med alle slags metadata. Bedst af alt er deres datasæt helt åbent. Du kan simpelthen <a %(openlibrary)s>downloade det</a>. <strong>WorldCat.</strong> Dette er en hjemmeside drevet af den non-profit OCLC, som sælger biblioteksstyringssystemer. De samler bogmetadata fra mange biblioteker og gør det tilgængeligt gennem WorldCat-hjemmesiden. Dog tjener de også penge på at sælge disse data, så de er ikke tilgængelige for bulk-download. De har nogle mere begrænsede bulk-datasæt tilgængelige for download i samarbejde med specifikke biblioteker. 1. For en rimelig definition af "for evigt". ;) 2. Selvfølgelig er menneskehedens skriftlige arv meget mere end bøger, især i dag. For denne posts skyld og vores seneste udgivelser fokuserer vi på bøger, men vores interesser strækker sig længere. 3. Der er meget mere, der kan siges om Aaron Swartz, men vi ville blot nævne ham kort, da han spiller en central rolle i denne historie. Som tiden går, kan flere mennesker støde på hans navn for første gang og derefter selv dykke ned i kaninhullet. <strong>Fysiske kopier.</strong> Det er selvfølgelig ikke særlig nyttigt, da de blot er kopier af det samme materiale. Det ville være fedt, hvis vi kunne bevare alle de noter, folk laver i bøger, som Fermats berømte “kruseduller i marginerne”. Men desværre vil det forblive en arkivars drøm. <strong>“Udgaver”.</strong> Her tæller du hver unik version af en bog. Hvis noget ved den er anderledes, som et andet omslag eller et andet forord, tæller det som en anden udgave. <strong>Filer.</strong> Når man arbejder med skyggebiblioteker som Library Genesis, Sci-Hub eller Z-Library, er der en yderligere overvejelse. Der kan være flere scanninger af den samme udgave. Og folk kan lave bedre versioner af eksisterende filer ved at scanne teksten ved hjælp af OCR eller rette sider, der blev scannet i en vinkel. Vi ønsker kun at tælle disse filer som én udgave, hvilket ville kræve god metadata eller deduplikering ved hjælp af dokumentlignende målinger. <strong>“Værker”.</strong> For eksempel “Harry Potter og Hemmelighedernes Kammer” som et logisk koncept, der omfatter alle versioner af den, som forskellige oversættelser og genoptryk. Dette er en slags nyttig definition, men det kan være svært at trække grænsen for, hvad der tæller. For eksempel vil vi sandsynligvis gerne bevare forskellige oversættelser, selvom genoptryk med kun mindre forskelle måske ikke er så vigtige. - Anna og teamet (<a %(reddit)s>Reddit</a>) Med Piratbibliotekets Spejl (REDIGERET: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) er vores mål at tage alle bøger i verden og bevare dem for evigt.<sup>1</sup> Mellem vores Z-Library torrents og de originale Library Genesis torrents har vi 11.783.153 filer. Men hvor mange er det egentlig? Hvis vi korrekt deduplikerede disse filer, hvilken procentdel af alle bøger i verden har vi bevaret? Vi vil virkelig gerne have noget som dette: Lad os starte med nogle grove tal: I både Z-Library/Libgen og Open Library er der mange flere bøger end unikke ISBN'er. Betyder det, at mange af disse bøger ikke har ISBN'er, eller mangler ISBN-metadataen simpelthen? Vi kan sandsynligvis besvare dette spørgsmål med en kombination af automatiseret matching baseret på andre attributter (titel, forfatter, udgiver osv.), inddragelse af flere datakilder og udtrækning af ISBN'er fra selve bogscanningerne (i tilfælde af Z-Library/Libgen). Hvor mange af disse ISBN'er er unikke? Dette illustreres bedst med et Venn-diagram: For at være mere præcis: Vi blev overraskede over, hvor lidt overlap der er! ISBNdb har en enorm mængde ISBN'er, der ikke dukker op i hverken Z-Library eller Open Library, og det samme gælder (i mindre, men stadig betydelig grad) for de to andre. Dette rejser mange nye spørgsmål. Hvor meget ville automatisk matching hjælpe med at tagge de bøger, der ikke blev tagget med ISBN'er? Ville der være mange matches og dermed øget overlap? Og hvad ville der ske, hvis vi tilføjer et 4. eller 5. datasæt? Hvor meget overlap ville vi se da? Dette giver os et udgangspunkt. Vi kan nu se på alle de ISBN'er, der ikke var i Z-Library-datasættet, og som heller ikke matcher titel/forfatter-felterne. Det kan give os en håndsrækning til at bevare alle bøger i verden: først ved at skrabe internettet for scanninger, derefter ved at gå ud i det virkelige liv for at scanne bøger. Sidstnævnte kunne endda være crowd-finansieret eller drevet af "dusører" fra folk, der gerne vil se bestemte bøger digitaliseret. Alt det er en historie til en anden gang. Hvis du vil hjælpe med noget af dette — yderligere analyse; skrabe mere metadata; finde flere bøger; OCR'ing af bøger; gøre dette for andre domæner (f.eks. artikler, lydbøger, film, tv-shows, magasiner) eller endda gøre nogle af disse data tilgængelige til ting som ML / store sprogmodeltræning — kontakt mig venligst (<a %(reddit)s>Reddit</a>). Hvis du er specielt interesseret i dataanalysen, arbejder vi på at gøre vores datasæt og scripts tilgængelige i et mere brugervenligt format. Det ville være fantastisk, hvis du bare kunne forke en notebook og begynde at lege med dette. Endelig, hvis du vil støtte dette arbejde, overvej venligst at give en donation. Dette er en helt frivilligt drevet operation, og dit bidrag gør en stor forskel. Hver lille smule hjælper. For nu tager vi donationer i krypto; se Doner-siden på Annas Arkiv. For en procentdel har vi brug for en nævner: det samlede antal bøger, der nogensinde er udgivet.<sup>2</sup> Før Google Books' nedlæggelse forsøgte en ingeniør på projektet, Leonid Taycher, <a %(booksearch_blogspot)s>at estimere</a> dette antal. Han kom — med et glimt i øjet — frem til 129.864.880 (“i det mindste indtil søndag”). Han estimerede dette antal ved at bygge en samlet database over alle bøger i verden. Til dette samlede han forskellige datasæt og fusionerede dem derefter på forskellige måder. Som en hurtig sidebemærkning er der en anden person, der forsøgte at katalogisere alle bøger i verden: Aaron Swartz, den afdøde digitale aktivist og medstifter af Reddit.<sup>3</sup> Han <a %(youtube)s>startede Open Library</a> med målet om “en webside for hver bog, der nogensinde er udgivet”, ved at kombinere data fra mange forskellige kilder. Han endte med at betale den ultimative pris for sit arbejde med digital bevaring, da han blev retsforfulgt for masse-download af akademiske artikler, hvilket førte til hans selvmord. Det siger sig selv, at dette er en af grundene til, at vores gruppe er pseudonym, og hvorfor vi er meget forsigtige. Open Library drives stadig heroisk af folkene hos Internet Archive, der fortsætter Aarons arv. Vi vender tilbage til dette senere i dette indlæg. I Google-blogindlægget beskriver Taycher nogle af udfordringerne ved at estimere dette tal. Først, hvad udgør en bog? Der er et par mulige definitioner: “Udgaver” synes at være den mest praktiske definition af, hvad “bøger” er. Bekvemt bruges denne definition også til at tildele unikke ISBN-numre. Et ISBN, eller International Standard Book Number, bruges almindeligvis til international handel, da det er integreret med det internationale stregkodesystem (”International Article Number”). Hvis du vil sælge en bog i butikker, skal den have en stregkode, så du får et ISBN. Taychers blogindlæg nævner, at mens ISBN'er er nyttige, er de ikke universelle, da de først blev rigtig adopteret i midten af halvfjerdserne, og ikke overalt i verden. Alligevel er ISBN sandsynligvis den mest udbredte identifikator for bogudgaver, så det er vores bedste udgangspunkt. Hvis vi kan finde alle ISBN'er i verden, får vi en nyttig liste over, hvilke bøger der stadig skal bevares. Så, hvor får vi dataene fra? Der er en række eksisterende bestræbelser, der forsøger at samle en liste over alle bøger i verden: I dette indlæg er vi glade for at kunne annoncere en lille udgivelse (sammenlignet med vores tidligere Z-Library-udgivelser). Vi skrabede det meste af ISBNdb og gjorde dataene tilgængelige for torrenting på Pirate Library Mirror's hjemmeside (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>; vi vil ikke linke det direkte her, bare søg efter det). Disse er omkring 30,9 millioner poster (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippet). På deres hjemmeside hævder de, at de faktisk har 32,6 millioner poster, så vi kan på en eller anden måde have misset nogle, eller <em>de</em> kunne gøre noget forkert. Under alle omstændigheder vil vi for nu ikke dele præcis, hvordan vi gjorde det — vi vil lade det være en øvelse for læseren. ;-) Hvad vi vil dele, er nogle foreløbige analyser for at forsøge at komme tættere på at estimere antallet af bøger i verden. Vi kiggede på tre datasæt: dette nye ISBNdb-datasæt, vores oprindelige udgivelse af metadata, som vi skrabede fra Z-Library skyggebiblioteket (som inkluderer Library Genesis), og Open Library data dump. ISBNdb dump, eller Hvor Mange Bøger Er Bevarede For Evigt? Hvis vi skulle deduplikere filerne fra skyggebiblioteker korrekt, hvilken procentdel af alle bøger i verden har vi bevaret? Opdateringer om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det største virkelig åbne bibliotek i menneskets historie. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Annas Arkiv Beholdere (AAC)</a>, som i det væsentlige er <a %(jsonlines)s>JSON Lines</a> komprimeret med <a %(zstd)s>Zstandard</a>, plus nogle standardiserede semantikker. Disse beholdere omslutter forskellige typer poster, baseret på de forskellige skrabninger, vi har udført. For et år siden <a %(blog)s>begyndte vi</a> at besvare dette spørgsmål: <strong>Hvilken procentdel af bøger er blevet permanent bevaret af skyggebiblioteker?</strong> Lad os se på nogle grundlæggende oplysninger om dataene: Når en bog kommer ind i et åbent-data skyggebibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, og nu <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, bliver den spejlet over hele verden (gennem torrents), og dermed praktisk talt bevaret for evigt. For at besvare spørgsmålet om, hvilken procentdel af bøger der er blevet bevaret, skal vi kende nævneren: hvor mange bøger findes der i alt? Og ideelt set har vi ikke bare et tal, men faktisk metadata. Så kan vi ikke kun matche dem mod skyggebiblioteker, men også <strong>oprette en TODO-liste over resterende bøger, der skal bevares!</strong> Vi kunne endda begynde at drømme om en crowdsourcet indsats for at gå ned ad denne TODO-liste. Vi skrabede <a %(wikipedia_isbndb_com)s>ISBNdb</a> og downloadede <a %(openlibrary)s>Open Library dataset</a>, men resultaterne var utilfredsstillende. Hovedproblemet var, at der ikke var meget overlap af ISBN'er. Se dette Venn-diagram fra <a %(blog)s>vores blogindlæg</a>: Vi blev meget overraskede over, hvor lidt overlap der var mellem ISBNdb og Open Library, som begge liberalt inkluderer data fra forskellige kilder, såsom webscrapes og biblioteksregistre. Hvis de begge gør et godt stykke arbejde med at finde de fleste ISBN'er derude, ville deres cirkler helt sikkert have betydeligt overlap, eller den ene ville være en delmængde af den anden. Det fik os til at undre os over, hvor mange bøger der falder <em>helt uden for disse cirkler</em>? Vi har brug for en større database. Det var da, vi satte vores mål på verdens største bogdatabase: <a %(wikipedia_worldcat)s>WorldCat</a>. Dette er en proprietær database af den non-profit <a %(wikipedia_oclc)s>OCLC</a>, som samler metadataregistre fra biblioteker over hele verden, i bytte for at give disse biblioteker adgang til det fulde datasæt og få dem til at dukke op i slutbrugernes søgeresultater. Selvom OCLC er en non-profit, kræver deres forretningsmodel, at de beskytter deres database. Nå, vi er kede af at sige det, venner hos OCLC, vi giver det hele væk. :-) I løbet af det sidste år har vi omhyggeligt skrabet alle WorldCat-poster. I starten fik vi et heldigt gennembrud. WorldCat var netop ved at udrulle deres komplette webstedsdesign (i august 2022). Dette omfattede en betydelig revision af deres backend-systemer, hvilket introducerede mange sikkerhedsfejl. Vi greb straks muligheden og var i stand til at skrabe hundreder af millioner (!) af poster på få dage. Derefter blev sikkerhedsfejl langsomt rettet én efter én, indtil den sidste, vi fandt, blev lappet for omkring en måned siden. På det tidspunkt havde vi stort set alle poster og gik kun efter lidt højere kvalitetsregistre. Så vi følte, det var tid til at frigive! 1,3B WorldCat skrabning <em><strong>Kort fortalt:</strong> Annas Arkiv skrabede hele WorldCat (verdens største bibliotek metadata samling) for at lave en TODO-liste over bøger, der skal bevares.</em> WorldCat Advarsel: dette blogindlæg er blevet forældet. Vi har besluttet, at IPFS endnu ikke er klar til primetime. Vi vil stadig linke til filer på IPFS fra Annas Arkiv, når det er muligt, men vi vil ikke længere hoste det selv, og vi anbefaler heller ikke andre at spejle ved hjælp af IPFS. Se venligst vores Torrents-side, hvis du vil hjælpe med at bevare vores samling. Hjælp med at seed Z-Library på IPFS Partner Server download SciDB Ekstern lån Ekstern lån (print handicappet) Ekstern download Udforsk metadata Indeholdt i torrents Tilbage  (+%(num)s bonus) ubetalt betalt annulleret udløbet venter på, at Anna bekræfter ugyldig Teksten nedenfor fortsætter på engelsk. Gå Nulstil Frem Sidste Hvis din e-mailadresse ikke virker på Libgen-foraene, anbefaler vi at bruge <a %(a_mail)s>Proton Mail</a> (gratis). Du kan også <a %(a_manual)s>anmode manuelt</a> om at få din konto aktiveret. (kan kræve <a %(a_browser)s>browserverifikation</a> — ubegrænsede downloads!) Hurtig Partner Server #%(number)s (anbefalet) (en smule hurtigere, men med venteliste) (ingen browserverifikation påkrævet) (ingen browserverifikation eller ventelister) (ingen venteliste, men kan være meget langsom) Langsom Partner Server #%(number)s Lydbog Tegneserie Bog (fiktion) Bog (non-fiktion) Bog (ukendt) Tidsskriftsartikel Magasin Noder Andet Standarddokument Ikke alle sider kunne konverteres til PDF Markeret som ødelagt i Libgen.li Ikke synlig i Libgen.li Ikke synlig i Libgen.rs Fiction Ikke synlig i Libgen.rs Non-Fiction Kørsel af exiftool mislykkedes på denne fil Markeret som “dårlig fil” i Z-Library Mangler fra Z-Library Markeret som “spam” i Z-Library Filen kan ikke åbnes (f.eks. ødelagt fil, DRM) Ophavsretskrav Problemer med download(f.eks. kan ikke forbinde, fejlmeddelelse, meget langsom) Forkert metadata (f.eks. titel, beskrivelse, forsidebillede) Andet Dårlig kvalitet (f.eks. formateringsproblemer, dårlig scanningskvalitet eller manglende sider) Spam / fil skal fjernes (f.eks. reklame eller stødende indhold) %(amount)s (%(amount_usd)s) %(amount)s i alt %(amount)s (%(amount_usd)s) i alt Smart Bogorm Heldige bibliotekar Strålende Samler Fantastisk Arkivar Ekstra downloads Cerlalc Tjekkiske metadata DuXiu 读秀 EBSCOhost eBook Index Google Bøger Goodreads HathiTrust IA IA Kontrolleret Digital Udlån ISBNdb ISBN GRP Libgen.li Eksklusive “scimag” Libgen.rs Non-Fiction og Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russisk Statsbibliotek Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads til AA Z-Library Z-Library Kinesisk Titel, forfatter, DOI, ISBN, MD5, … Søg Forfatter Beskrivelse og metadata kommentarer Udgave Original filnavn Forlag (søg specifikt felt) Titel Udgivelsesår Tekniske detaljer Denne mønt har en højere minimum end normalt. Vælg en anden varighed eller en anden mønt. Anmodningen kunne ikke gennemføres. Prøv igen om et par minutter, og hvis det fortsætter, kontakt os på %(email)s med et skærmbillede. En ukendt fejl opstod. Kontakt os venligst på %(email)s med et skærmbillede. Fejl i betalingsbehandlingen. Vent et øjeblik og prøv igen. Hvis problemet fortsætter i mere end 24 timer, kontakt os på %(email)s med et skærmbillede. Vi kører en indsamlingskampagne for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">backup</a> af verdens største skyggebibliotek for tegneserier. Tak for din støtte! <a href="/donate">Donér.</a> Hvis du ikke kan donere, overvej at støtte os ved at fortælle dine venner og følge os på <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> eller <a href="https://t.me/annasarchiveorg">Telegram</a>. Send ikke e-mails til os for at <a %(a_request)s>anmode om bøger</a><br>eller små (<10k) <a %(a_upload)s>uploads</a>. Annas Arkiv DMCA / ophavsretskrav Hold kontakten Reddit Alternativer SLUM (%(unaffiliated)s) ikke tilknyttet Annas Arkiv har brug for din hjælp! Hvis du donerer nu, får du <strong>dobbelt</strong> så mange hurtige downloads. Mange forsøger at tage os ned, men vi kæmper tilbage. Hvis du donerer denne måned, får du <strong>dobbelt</strong> så mange hurtige downloads. Gyldig indtil udgangen af denne måned. At redde menneskelig viden: en fantastisk julegave! Medlemskaber vil blive forlænget tilsvarende. Partner-servere er utilgængelige på grund af hosting-lukninger. De burde være oppe igen snart. For at øge modstandsdygtigheden af Annas Arkiv søger vi frivillige til at køre spejle. Vi har en ny donationsmetode tilgængelig: %(method_name)s. Overvej venligst %(donate_link_open_tag)sat donere</a> — det er ikke billigt at drive denne hjemmeside, og din donation gør virkelig en forskel. Mange tak. Henvis en ven, og både du og din ven får %(percentage)s%% bonus hurtige downloads! Overrask en elsket, giv dem en konto med medlemskab. Den perfekte Valentinsgave! Lær mere… Konto Aktivitet Avanceret Annas Blog ↗ Annas Software ↗ beta Koder Explorer Datasæt Donér Downloadede filer FAQ Hjem Forbedr metadata LLM data Log ind / Registrer Mine donationer Offentlig profil Søg Sikkerhed Torrents Oversæt ↗ Frivilligt arbejde & dusører Seneste downloads: 📚&nbsp;Verdens største open-source open-data bibliotek. ⭐️&nbsp;Spejler Sci-Hub, Library Genesis, Z-Library og mere. 📈&nbsp;%(book_any)s bøger, %(journal_article)s artikler, %(book_comic)s tegneserier, %(magazine)s magasiner — bevaret for evigt.  og  og mere DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Det største åbne bibliotek nogensinde i menneskets historie 📈&nbsp;%(book_count)s&nbsp;bøger, %(paper_count)s&nbsp;artikler — bevaret for evigt. ⭐️&nbsp;Vi spejler %(libraries)s. Vi samler og benytter  %(scraped)s. Al vores kode og data er fuldstændig open source. OpenLib Sci-Hub ,  📚 Verdens største open-source og open-data bibliotek.<br>⭐️ Mirrors Scihub, Libgen, Zlib og mere. Z-Lib Annas Arkiv Ugyldig anmodning. Besøg %(websites)s. Verdens største open-source open-data bibliotek. Spejler Sci-Hub, Library Genesis, Z-Library og mere. Søg i Annas Arkiv Annas Arkiv Opdater for at prøve igen. <a %(a_contact)s>Kontakt os</a> hvis problemet fortsætter i flere timer. 🔥 Problem med at indlæse denne side <li>1. Følg os på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spred budskabet om Anna’s Arkiv på Twitter, Reddit, Tiktok, Instagram, på din lokale café eller bibliotek, eller hvor du end går! Vi tror ikke på at holde noget hemmeligt — hvis vi bliver taget ned, dukker vi bare op et andet sted, da al vores kode og data er fuldstændig open source.</li><li>3. Hvis du har mulighed for det, overvej at <a href="/donate">donere</a>.</li><li>4. Hjælp med at <a href="https://translate.annas-software.org/">oversætte</a> vores hjemmeside til forskellige sprog.</li><li>5. Hvis du er softwareingeniør, overvej at bidrage til vores <a href="https://annas-software.org/">open source</a>, eller seed vores <a href="/datasets">torrents</a>.</li> 10. Opret eller hjælp med at vedligeholde Wikipedia-siden for Annas Arkiv på dit sprog. 11. Vi søger at placere små, smagfulde annoncer. Hvis du ønsker at annoncere på Annas Arkiv, så lad os det vide. 6. Hvis du er sikkerhedsforsker, kan vi bruge dine færdigheder både til angreb og forsvar. Tjek vores <a %(a_security)s>Sikkerhed</a> side. 7. Vi søger eksperter i betalinger for anonyme handlende. Kan du hjælpe os med at tilføje mere bekvemme måder at donere på? PayPal, WeChat, gavekort. Hvis du kender nogen, kontakt os venligst. 8. Vi søger altid mere serverkapacitet. 9. Du kan hjælpe ved at rapportere filproblemer, efterlade kommentarer og oprette lister direkte på denne hjemmeside. Du kan også hjælpe ved at <a %(a_upload)s>uploade flere bøger</a>, eller rette filproblemer eller formatering af eksisterende bøger. For mere omfattende information om, hvordan man kan være frivillig, se vores <a %(a_volunteering)s>Frivillighed & Belønninger</a> side. Vi tror stærkt på den frie strøm af information og bevaring af viden og kultur. Med denne søgemaskine bygger vi på skuldrene af giganter. Vi respekterer dybt det hårde arbejde fra de mennesker, der har skabt de forskellige skyggebiblioteker, og vi håber, at denne søgemaskine vil udvide deres rækkevidde. For at holde dig opdateret om vores fremskridt, følg Anna på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> eller <a href="https://t.me/annasarchiveorg">Telegram</a>. For spørgsmål og feedback kontakt venligst Anna på %(email)s. Konto ID: %(account_id)s Log ud ❌ Noget gik galt. Genindlæs siden og prøv igen. ✅ Du er nu logget ud. Genindlæs siden for at logge ind igen. Hurtige downloads brugt (sidste 24 timer): <strong>%(used)s / %(total)s</strong> Medlemskab: <strong>%(tier_name)s</strong> indtil %(until_date)s <a %(a_extend)s>(forlæng)</a> Du kan kombinere flere medlemskaber (hurtige downloads pr. 24 timer vil blive lagt sammen). Medlemskab: <strong>Ingen</strong> <a %(a_become)s>(bliv medlem)</a> Kontakt Anna på %(email)s, hvis du er interesseret i at opgradere dit medlemskab til et højere niveau. Offentlig profil: %(profile_link)s Hemmelig nøgle (del ikke!): %(secret_key)s vis Bliv medlem her! Opgrader til et <a %(a_tier)s>højere niveau</a> for at blive medlem af vores gruppe. Eksklusiv Telegram-gruppe: %(link)s Konto hvilke downloads? Log ind Mister ikke din nøgle! Ugyldig hemmelig nøgle. Bekræft din nøgle og prøv igen, eller registrer alternativt en ny konto nedenfor. Hemmelig nøgle Indtast din hemmelige nøgle for at logge ind: Gammel email-baseret konto? Indtast din <a %(a_open)s>email her</a>. Registrer ny konto Har du ikke en konto endnu? Registrering vellykket! Din hemmelige nøgle er: <span %(span_key)s>%(key)s</span> Gem denne nøgle omhyggeligt. Hvis du mister den, mister du adgangen til din konto. <li %(li_item)s><strong>Bogmærke.</strong> Du kan bogmærke denne side for at hente din nøgle.</li><li %(li_item)s><strong>Download.</strong> Klik <a %(a_download)s>på dette link</a> for at downloade din nøgle.</li><li %(li_item)s><strong>Adgangskodeadministrator.</strong> Brug en adgangskodeadministrator til at gemme nøglen, når du indtaster den nedenfor.</li> Log ind / Registrer Browserverifikation Advarsel: koden har forkerte Unicode-tegn og kan opføre sig forkert i forskellige situationer. Den rå binærkode kan dekodes fra base64-repræsentationen i URL'en. Beskrivelse Etiket Præfiks URL til en specifik kode Hjemmeside Koder der starter med “%(prefix_label)s” Venligst skrab ikke disse sider. I stedet anbefaler vi <a %(a_import)s>generering</a> eller <a %(a_download)s>download</a> af vores ElasticSearch og MariaDB databaser og kørsel af vores <a %(a_software)s>open source kode</a>. De rå data kan manuelt udforskes gennem JSON-filer som <a %(a_json_file)s>denne</a>. Færre end %(count)s poster Generisk URL Kodeudforsker Indeks over Udforsk de koder, som poster er tagget med, efter præfiks. Kolonnen “poster” viser antallet af poster tagget med koder med det givne præfiks, som set i søgemaskinen (inklusive metadata-only poster). Kolonnen “koder” viser, hvor mange faktiske koder der har et givet præfiks. Kendt kodepræfiks “%(key)s” Mere… Præfiks %(count)s post matcher “%(prefix_label)s” %(count)s poster matcher “%(prefix_label)s” koder poster “%%s” vil blive erstattet med kodens værdi Søg i Annas Arkiv Koder URL til specifik kode: “%(url)s” Denne side kan tage et stykke tid at generere, hvilket er grunden til, at den kræver en Cloudflare captcha. <a %(a_donate)s>Medlemmer</a> kan springe captchaen over. Misbrug rapporteret: Bedre version Ønsker du at rapportere denne bruger for misbrug eller upassende adfærd? Filproblem: %(file_issue)s skjult kommentar Svar Rapportér misbrug Du har rapporteret denne bruger for misbrug. Ophavsretskrav til denne e-mail vil blive ignoreret; brug i stedet formularen. Vis e-mail Vi byder meget velkommen til din feedback og dine spørgsmål! Men på grund af mængden af spam og nonsens-e-mails, vi modtager, bedes du markere felterne for at bekræfte, at du forstår disse betingelser for at kontakte os. Enhver anden måde at kontakte os om ophavsretskrav vil automatisk blive slettet. For DMCA / ophavsretskrav, brug <a %(a_copyright)s>denne formular</a>. Kontakt email URL'er på Annas Arkiv (påkrævet). En per linje. Inkluder venligst kun URL'er, der beskriver præcis samme udgave af en bog. Hvis du vil fremsætte et krav for flere bøger eller flere udgaver, bedes du indsende denne formular flere gange. Krav, der samler flere bøger eller udgaver sammen, vil blive afvist. Adresse (påkrævet) Klar beskrivelse af kildematerialet (påkrævet) E-mail (påkrævet) URL'er til kildematerialet, en per linje (påkrævet). Inkluder venligst så mange som muligt for at hjælpe os med at verificere dit krav (f.eks. Amazon, WorldCat, Google Books, DOI). ISBN'er på kildematerialet (hvis relevant). En per linje. Inkluder venligst kun dem, der præcist matcher den udgave, som du rapporterer et ophavsretskrav for. Dit navn (påkrævet) ❌ Noget gik galt. Genindlæs venligst siden og prøv igen. ✅ Tak for din indsendelse af ophavsretskravet. Vi vil gennemgå det så hurtigt som muligt. Genindlæs venligst siden for at indsende et nyt krav. <a %(a_openlib)s>Open Library</a> URL'er på kildematerialet, en per linje. Tag venligst et øjeblik til at søge i Open Library efter dit kildemateriale. Dette vil hjælpe os med at verificere dit krav. Telefonnummer (påkrævet) Erklæring og underskrift (påkrævet) Indsend krav Hvis du har et DCMA- eller andet ophavsretskrav, bedes du udfylde denne formular så præcist som muligt. Hvis du støder på problemer, bedes du kontakte os på vores dedikerede DMCA-adresse: %(email)s. Bemærk, at krav sendt til denne adresse ikke vil blive behandlet, den er kun til spørgsmål. Brug venligst formularen nedenfor til at indsende dine krav. DMCA / Formular til ophavsretskrav Eksempelpost på Anna’s Archive Torrenter af Anna’s Archive Anna’s Archive Containers-format Scripts til import af metadata Hvis du er interesseret i at spejle dette datasæt til <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-træning</a>, bedes du kontakte os. Sidst opdateret: %(date)s Hoved %(source)s hjemmeside Metadata-dokumentation (de fleste felter) Filer spejlet af Anna’s Archive: %(count)s (%(percent)s%%) Ressourcer Samlede filer: %(count)s Samlet filstørrelse: %(size)s Vores blogindlæg om disse data <a %(duxiu_link)s>Duxiu</a> er en enorm database med scannede bøger, skabt af <a %(superstar_link)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøger, scannet for at gøre dem digitalt tilgængelige for universiteter og biblioteker. For vores engelsktalende publikum har <a %(princeton_link)s>Princeton</a> og <a %(uw_link)s>University of Washington</a> gode oversigter. Der er også en fremragende artikel, der giver mere baggrund: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Bøgerne fra Duxiu er længe blevet piratkopieret på det kinesiske internet. Normalt bliver de solgt for mindre end en dollar af forhandlere. De distribueres typisk ved hjælp af den kinesiske ækvivalent til Google Drive, som ofte er blevet hacket for at tillade mere lagerplads. Nogle tekniske detaljer kan findes <a %(link1)s>her</a> og <a %(link2)s>her</a>. Selvom bøgerne er blevet semi-offentligt distribueret, er det ret svært at få dem i bulk. Vi havde dette højt på vores TODO-liste og afsatte flere måneder af fuldtidsarbejde til det. Men i slutningen af 2023 kontaktede en utrolig, fantastisk og talentfuld frivillig os og fortalte, at de allerede havde udført alt dette arbejde — til stor udgift. De delte hele samlingen med os uden at forvente noget til gengæld, bortset fra garantien om langsigtet bevaring. Virkelig bemærkelsesværdigt. Mere information fra vores frivillige (rå noter): Tilpasset fra vores <a %(a_href)s>blogindlæg</a>. DuXiu 读秀 %(count)s fil %(count)s filer Dette datasæt er tæt forbundet med <a %(a_datasets_openlib)s>Open Library-datasættet</a>. Det indeholder en scraping af alle metadata og en stor del af filer fra IA's Controlled Digital Lending Library. Opdateringer udgives i <a %(a_aac)s>Annas Arkiv Containers-format</a>. Disse poster henvises direkte fra Open Library-datasættet, men indeholder også poster, der ikke er i Open Library. Vi har også en række datafiler, der er skrabet af fællesskabsmedlemmer gennem årene. Samlingen består af to dele. Du skal bruge begge dele for at få alle data (undtagen forældede torrents, som er krydset ud på torrentsiden). Digitalt Udlånsbibliotek vores første udgivelse, før vi standardiserede på <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Indeholder metadata (som json og xml), pdf'er (fra acsm og lcpdf digitale udlånsystemer) og omslagsminiaturer. inkrementelle nye udgivelser, der bruger AAC. Indeholder kun metadata med tidsstempler efter 2023-01-01, da resten allerede er dækket af “ia”. Også alle pdf-filer, denne gang fra acsm og “bookreader” (IA's web-læser) udlånsystemer. På trods af at navnet ikke er helt korrekt, placerer vi stadig bookreader-filer i ia2_acsmpdf_files-samlingen, da de er gensidigt udelukkende. IA Kontrolleret Digital Udlån 98%%+ af filer er søgbare. Vores mission er at arkivere alle bøger i verden (samt artikler, magasiner osv.) og gøre dem bredt tilgængelige. Vi mener, at alle bøger bør spejles vidt og bredt for at sikre redundans og modstandsdygtighed. Derfor samler vi filer fra en række forskellige kilder. Nogle kilder er helt åbne og kan spejles i bulk (såsom Sci-Hub). Andre er lukkede og beskyttende, så vi forsøger at skrabe dem for at "befri" deres bøger. Andre falder et sted midt imellem. Alle vores data kan <a %(a_torrents)s>torrentes</a>, og al vores metadata kan <a %(a_anna_software)s>genereres</a> eller <a %(a_elasticsearch)s>downloades</a> som ElasticSearch- og MariaDB-databaser. De rå data kan manuelt udforskes gennem JSON-filer som <a %(a_dbrecord)s>denne</a>. Metadata ISBN hjemmeside Sidst opdateret: %(isbn_country_date)s (%(link)s) Ressourcer Den Internationale ISBN Agentur udgiver regelmæssigt de intervaller, som den har tildelt nationale ISBN agenturer. Ud fra dette kan vi udlede, hvilket land, region eller sproggruppe dette ISBN tilhører. Vi bruger i øjeblikket disse data indirekte gennem <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket. ISBN landinformation Dette er en dump af mange opkald til isbndb.com i løbet af september 2022. Vi forsøgte at dække alle ISBN-intervaller. Disse er omkring 30,9 millioner poster. På deres hjemmeside hævder de, at de faktisk har 32,6 millioner poster, så vi kan på en eller anden måde have misset nogle, eller <em>de</em> kan gøre noget forkert. JSON-svarene er stort set rå fra deres server. Et datakvalitetsproblem, som vi bemærkede, er, at for ISBN-13-numre, der starter med et andet præfiks end "978-", inkluderer de stadig et "isbn"-felt, der simpelthen er ISBN-13-nummeret med de første 3 tal fjernet (og kontrolcifret genberegnet). Dette er åbenlyst forkert, men det er sådan, de ser ud til at gøre det, så vi ændrede det ikke. Et andet potentielt problem, du kan støde på, er, at "isbn13"-feltet har dubletter, så du kan ikke bruge det som en primær nøgle i en database. "isbn13"+"isbn"-felter kombineret ser dog ud til at være unikke. Udgivelse 1 (2022-10-31) Fiktion torrents er bagud (selvom ID'er ~4-6M ikke er torrentet, da de overlapper med vores Zlib torrents). Vores blogindlæg om udgivelsen af tegneserier Tegneserietorrents på Annas Arkiv For baggrundshistorien om de forskellige Library Genesis forks, se siden for <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li indeholder det meste af det samme indhold og metadata som Libgen.rs, men har nogle samlinger oveni, nemlig tegneserier, magasiner og standarddokumenter. Det har også integreret <a %(a_scihub)s>Sci-Hub</a> i sin metadata og søgemaskine, hvilket er det, vi bruger til vores database. Metadataene for dette bibliotek er frit tilgængelige <a %(a_libgen_li)s>på libgen.li</a>. Dog er denne server langsom og understøtter ikke genoptagelse af afbrudte forbindelser. De samme filer er også tilgængelige på <a %(a_ftp)s>en FTP-server</a>, som fungerer bedre. Ikke-fiktion ser også ud til at have divergeret, men uden nye torrents. Det ser ud til, at dette er sket siden begyndelsen af 2022, selvom vi ikke har verificeret det. Ifølge Libgen.li-administratoren bør “fiction_rus” (russisk fiktion) samlingen være dækket af regelmæssigt udgivne torrents fra <a %(a_booktracker)s>booktracker.org</a>, især <a %(a_flibusta)s>flibusta</a> og <a %(a_librusec)s>lib.rus.ec</a> torrents (som vi spejler <a %(a_torrents)s>her</a>, selvom vi endnu ikke har fastslået, hvilke torrents der svarer til hvilke filer). Fiktionssamlingen har sine egne torrents (afvigende fra <a %(a_href)s>Libgen.rs</a>) startende ved %(start)s. Visse områder uden torrents (såsom fiktionsområder f_3463000 til f_4260000) er sandsynligvis Z-Library (eller andre duplikat) filer, selvom vi måske ønsker at foretage en deduplikation og lave torrents for lgli-unikke filer i disse områder. Statistikker for alle samlinger kan findes <a %(a_href)s>på libgens hjemmeside</a>. Torrents er tilgængelige for det meste af det ekstra indhold, især torrents for tegneserier, magasiner og standarddokumenter er blevet udgivet i samarbejde med Annas Arkiv. Bemærk, at torrentfilerne, der henviser til “libgen.is”, eksplicit er spejle af <a %(a_libgen)s>Libgen.rs</a> (“.is” er et andet domæne, der bruges af Libgen.rs). En nyttig ressource til brug af metadata er <a %(a_href)s>denne side</a>. %(icon)s Deres “fiction_rus” samling (russisk fiktion) har ingen dedikerede torrents, men er dækket af torrents fra andre, og vi holder et <a %(fiction_rus)s>spejl</a>. Russiske fiktions torrents på Annas Arkiv Fiktionstorrents på Annas Arkiv Diskussionsforum Metadata Metadata via FTP Magasintorrents på Annas Arkiv Metadata feltinformation Spejl af andre torrents (og unikke fiktion- og tegneserietorrents) Standarddokument torrents på Annas Arkiv Libgen.li Torrents af Annas Arkiv (bogomslag) Library Genesis er kendt for allerede generøst at gøre deres data tilgængelige i bulk gennem torrents. Vores Libgen-samling består af hjælpeoplysninger, som de ikke frigiver direkte, i partnerskab med dem. Mange tak til alle involverede i Library Genesis for at arbejde sammen med os! Vores blog om udgivelsen af bogomslag Denne side handler om “.rs”-versionen. Den er kendt for konsekvent at offentliggøre både sine metadata og det fulde indhold af sin bogkatalog. Dens bogsamling er opdelt mellem en fiktion- og non-fiktion-del. En nyttig ressource til brug af metadata er <a %(a_metadata)s>denne side</a> (blokerer IP-intervaller, VPN kan være påkrævet). Fra marts 2024 bliver nye torrents postet i <a %(a_href)s>denne forumtråd</a> (blokerer IP-intervaller, VPN kan være påkrævet). Fiktion torrents på Annas Arkiv Libgen.rs Fiktion torrents Libgen.rs Diskussionsforum Libgen.rs Metadata Libgen.rs metadata feltinformation Libgen.rs Non-fiktion torrents Non-fiktion torrents på Annas Arkiv %(example)s for en fiktion bog. Denne <a %(blog_post)s>første udgivelse</a> er ret lille: omkring 300GB af bogomslag fra Libgen.rs forken, både fiktion og non-fiktion. De er organiseret på samme måde som de vises på libgen.rs, f.eks.: %(example)s for en non-fiktion bog. Ligesom med Z-Library samlingen, har vi lagt dem alle i en stor .tar-fil, som kan monteres ved hjælp af <a %(a_ratarmount)s>ratarmount</a>, hvis du vil servere filerne direkte. Udgivelse 1 (%(date)s) Den korte historie om de forskellige Library Genesis (eller “Libgen”) forgreninger er, at over tid havde de forskellige personer involveret i Library Genesis en uoverensstemmelse og gik hver til sit. Ifølge dette <a %(a_mhut)s>forumpost</a> blev Libgen.li oprindeligt hostet på “http://free-books.dontexist.com”. “.fun”-versionen blev skabt af den oprindelige grundlægger. Den bliver fornyet til fordel for en ny, mere distribueret version. <a %(a_li)s>“.li”-versionen</a> har en enorm samling af tegneserier samt andet indhold, der (endnu) ikke er tilgængeligt for bulk download gennem torrents. Den har en separat torrent-samling af fiktionsbøger, og den indeholder metadata fra <a %(a_scihub)s>Sci-Hub</a> i sin database. “.rs”-versionen har meget lignende data og udgiver konsekvent deres samling i bulk torrents. Den er groft opdelt i en “fiktion” og en “non-fiktion” sektion. Oprindeligt på “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> er på en måde også en forgrening af Library Genesis, selvom de brugte et andet navn til deres projekt. Libgen.rs Vi beriger også vores samling med kun metadata-kilder, som vi kan matche til filer, f.eks. ved hjælp af ISBN-numre eller andre felter. Nedenfor er en oversigt over disse. Igen, nogle af disse kilder er helt åbne, mens vi for andre er nødt til at skrabe dem. Bemærk, at i metadata-søgning viser vi de originale poster. Vi foretager ingen sammensmeltning af poster. Kun metadata-kilder Open Library er et open source-projekt af Internet Archive til at katalogisere hver bog i verden. Det har en af verdens største bogscanningsoperationer og har mange bøger tilgængelige for digital udlån. Dets bogmetadata-katalog er frit tilgængeligt for download og er inkluderet på Annas Arkiv (dog ikke i øjeblikket i søgning, medmindre du eksplicit søger efter et Open Library ID). Open Library Ekskluderer duplikater Sidst opdateret Procentdel af antal filer %% spejlet af AA / torrents tilgængelige Størrelse Kilde Nedenfor er en hurtig oversigt over kilderne til filerne på Annas Arkiv. Da skyggebibliotekerne ofte synkroniserer data fra hinanden, er der betydelig overlapning mellem bibliotekerne. Derfor stemmer tallene ikke overens med totalen. Procentdelen “spejlet og seedet af Anna’s Arkiv” viser, hvor mange filer vi selv spejler. Vi seeder disse filer i bulk gennem torrents og gør dem tilgængelige for direkte download gennem partnerwebsteder. Oversigt Total Torrents på Annas Arkiv For en baggrund om Sci-Hub, henvises til dens <a %(a_scihub)s>officielle hjemmeside</a>, <a %(a_wikipedia)s>Wikipedia-side</a> og dette <a %(a_radiolab)s>podcast-interview</a>. Bemærk, at Sci-Hub har været <a %(a_reddit)s>frosset siden 2021</a>. Det var frosset før, men i 2021 blev der tilføjet et par millioner artikler. Stadig bliver et begrænset antal artikler tilføjet til Libgen “scimag” samlinger, dog ikke nok til at berettige nye bulk torrents. Vi bruger Sci-Hub metadata som leveret af <a %(a_libgen_li)s>Libgen.li</a> i dens “scimag” samling. Vi bruger også <a %(a_dois)s>dois-2022-02-12.7z</a> datasættet. Bemærk, at “smarch” torrents er <a %(a_smarch)s>udgået</a> og derfor ikke inkluderet i vores torrents liste. Torrents på Libgen.li Torrents på Libgen.rs Metadata og torrents Opdateringer på Reddit Podcast-interview Wikipedia-side Sci-Hub Sci-Hub: frosset siden 2021; det meste tilgængeligt via torrents Libgen.li: mindre tilføjelser siden da</div> Nogle kildelibraries fremmer deling af deres data i bulk gennem torrents, mens andre ikke deler deres samling så let. I sidstnævnte tilfælde forsøger Anna’s Arkiv at skrabe deres samlinger og gøre dem tilgængelige (se vores <a %(a_torrents)s>Torrents</a> side). Der er også mellemliggende situationer, for eksempel hvor kildelibraries er villige til at dele, men ikke har ressourcerne til det. I disse tilfælde forsøger vi også at hjælpe. Nedenfor er en oversigt over, hvordan vi interagerer med de forskellige kildelibraries. Kildebiblioteker %(icon)s Forskellige fil-databaser spredt rundt på det kinesiske internet; dog ofte betalte databaser %(icon)s De fleste filer er kun tilgængelige med premium BaiduYun-konti; langsomme downloadhastigheder. %(icon)s Annas Arkiv administrerer en samling af <a %(duxiu)s>DuXiu-filer</a> %(icon)s Forskellige metadata-databaser spredt rundt på det kinesiske internet; dog ofte betalte databaser %(icon)s Ingen let tilgængelige metadata-dumps tilgængelige for deres samlede samling. %(icon)s Annas Arkiv administrerer en samling af <a %(duxiu)s>DuXiu-metadata</a> Filer %(icon)s Filer kun tilgængelige for udlån på begrænset basis, med forskellige adgangsbegrænsninger %(icon)s Annas Arkiv administrerer en samling af <a %(ia)s>IA-filer</a> %(icon)s Nogle metadata tilgængelige gennem <a %(openlib)s>Open Library database dumps</a>, men de dækker ikke hele IA-samlingen %(icon)s Ingen let tilgængelige metadata dumps tilgængelige for deres samlede samling %(icon)s Annas Arkiv administrerer en samling af <a %(ia)s>IA metadata</a> Sidst opdateret %(icon)s Annas Arkiv og Libgen.li administrerer i fællesskab samlinger af <a %(comics)s>tegneserier</a>, <a %(magazines)s>magasiner</a>, <a %(standarts)s>standarddokumenter</a> og <a %(fiction)s>fiktion (afviget fra Libgen.rs)</a>. %(icon)s Non-Fiction torrenter deles med Libgen.rs (og spejles <a %(libgenli)s>her</a>). %(icon)s Kvartalsvise <a %(dbdumps)s>HTTP-database dumps</a> %(icon)s Automatiserede torrents for <a %(nonfiction)s>Non-Fiction</a> og <a %(fiction)s>Fiction</a> %(icon)s Annas Arkiv administrerer en samling af <a %(covers)s>bogomslag torrents</a> %(icon)s Daglige <a %(dbdumps)s>HTTP-database dumps</a> Metadata %(icon)s Månedlige <a %(dbdumps)s>database dumps</a> %(icon)s Data torrents tilgængelige <a %(scihub1)s>her</a>, <a %(scihub2)s>her</a>, og <a %(libgenli)s>her</a> %(icon)s Nogle nye filer <a %(libgenrs)s>bliver</a> <a %(libgenli)s>tilføjet</a> til Libgens "scimag", men ikke nok til at retfærdiggøre nye torrenter %(icon)s Sci-Hub har frosset nye filer siden 2021. %(icon)s Metadata dumps tilgængelige <a %(scihub1)s>her</a> og <a %(scihub2)s>her</a>, samt som en del af <a %(libgenli)s>Libgen.li-databasen</a> (som vi bruger) Kilde %(icon)s Forskellige mindre eller enkeltstående kilder. Vi opfordrer folk til at uploade til andre skyggebiblioteker først, men nogle gange har folk samlinger, der er for store til, at andre kan sortere dem, men ikke store nok til at berettige deres egen kategori. %(icon)s Ikke tilgængelig direkte i bulk, beskyttet mod scraping %(icon)s Annas Arkiv administrerer en samling af <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Annas Arkiv og Z-Library administrerer i fællesskab en samling af <a %(metadata)s>Z-Library metadata</a> og <a %(files)s>Z-Library filer</a> Datasets Vi kombinerer alle ovenstående kilder til én forenet database, som vi bruger til at betjene denne hjemmeside. Denne forenede database er ikke direkte tilgængelig, men da Anna’s Arkiv er fuldt open source, kan den ret nemt <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>downloades</a> som ElasticSearch og MariaDB databaser. Scripts på den side vil automatisk downloade alle nødvendige metadata fra de ovennævnte kilder. Hvis du gerne vil udforske vores data, før du kører disse scripts lokalt, kan du se på vores JSON-filer, som linker videre til andre JSON-filer. <a %(a_json)s>Denne fil</a> er et godt udgangspunkt. Forenet database Torrents af Annas Arkiv gennemse søg Forskellige mindre eller enkeltstående kilder. Vi opfordrer folk til at uploade til andre skyggebiblioteker først, men nogle gange har folk samlinger, der er for store til, at andre kan sortere dem, men ikke store nok til at berettige deres egen kategori. Oversigt fra <a %(a1)s>Datasets-side</a>. Fra <a %(a_href)s>aaaaarg.fail</a>. Ser ud til at være ret komplet. Fra vores frivillige “cgiym”. Fra en <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ret høj overlap med eksisterende papers-samlinger, men meget få MD5-matches, så vi besluttede at beholde den fuldstændigt. Scrape af <q>iRead eBooks</q> (= fonetisk <q>ai rit i-books</q>; airitibooks.com), af frivillig <q>j</q>. Svarer til <q>airitibooks</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>. Fra en samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvist fra den oprindelige kilde, delvist fra the-eye.eu, delvist fra andre spejle. Fra en privat bøger torrent hjemmeside, <a %(a_href)s>Bibliotik</a> (ofte omtalt som “Bib”), hvor bøger blev samlet i torrents efter navn (A.torrent, B.torrent) og distribueret gennem the-eye.eu. Fra vores frivillige “bpb9v”. For mere information om <a %(a_href)s>CADAL</a>, se noterne på vores <a %(a_duxiu)s>DuXiu dataset side</a>. Mere fra vores frivillige “bpb9v”, mest DuXiu-filer, samt en mappe “WenQu” og “SuperStar_Journals” (SuperStar er firmaet bag DuXiu). Fra vores frivillige “cgiym”, kinesiske tekster fra forskellige kilder (repræsenteret som undermapper), herunder fra <a %(a_href)s>China Machine Press</a> (en stor kinesisk udgiver). Ikke-kinesiske samlinger (repræsenteret som undermapper) fra vores frivillige “cgiym”. Scrape af bøger om kinesisk arkitektur, af frivillig <q>cm</q>: <q>Jeg fik det ved at udnytte en netværkssårbarhed hos forlaget, men den smuthul er siden blevet lukket</q>. Svarer til <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>. Bøger fra det akademiske forlag <a %(a_href)s>De Gruyter</a>, samlet fra nogle få store torrents. Scrape af <a %(a_href)s>docer.pl</a>, en polsk fil-deling hjemmeside fokuseret på bøger og andre skriftlige værker. Scraped i slutningen af 2023 af frivillig “p”. Vi har ikke god metadata fra den oprindelige hjemmeside (ikke engang filendelser), men vi filtrerede for bog-lignende filer og var ofte i stand til at udtrække metadata fra filerne selv. DuXiu epubs, direkte fra DuXiu, samlet af frivillig “w”. Kun nyere DuXiu-bøger er tilgængelige direkte gennem e-bøger, så de fleste af disse må være nyere. Resterende DuXiu-filer fra frivillig “m”, som ikke var i DuXius proprietære PDG-format (det primære <a %(a_href)s>DuXiu dataset</a>). Samlet fra mange originale kilder, desværre uden at bevare disse kilder i filstien. <span></span> <span></span> <span></span> Scrape af erotiske bøger, af frivillig <q>do no harm</q>. Svarer til <q>hentai</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>. <span></span> <span></span> Samling skrabet fra en japansk Manga-udgiver af frivillig “t”. <a %(a_href)s>Udvalgte retsarkiver fra Longquan</a>, leveret af frivillig “c”. Scrape af <a %(a_href)s>magzdb.org</a>, en allieret af Library Genesis (det er linket på libgen.rs hjemmesiden), men som ikke ønskede at levere deres filer direkte. Opnået af frivillig “p” i slutningen af 2023. <span></span> Forskellige små uploads, for små til at være deres egen undersamling, men repræsenteret som mapper. E-bøger fra AvaxHome, en russisk fildelingsside. Arkiv af aviser og magasiner. Svarer til <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andre metadata scrapes</q></a>. Scrape af <a %(a1)s>Philosophy Documentation Center</a>. Samling af frivillig “o” som samlede polske bøger direkte fra originale udgivelses (“scene”) hjemmesider. Kombinerede samlinger af <a %(a_href)s>shuge.org</a> af frivillige “cgiym” og “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (opkaldt efter det fiktive bibliotek), scraped i 2022 af frivillig “t”. <span></span> <span></span> <span></span> Under-under-samlinger (repræsenteret som mapper) fra frivillig “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (af <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mit lille bogrum — woz9ts: “Denne side fokuserer hovedsageligt på at dele høj kvalitet e-bogsfiler, hvoraf nogle er sat op af ejeren selv. Ejeren blev <a %(a_arrested)s>arresteret</a> i 2019, og nogen lavede en samling af de filer, han delte.”). Resterende DuXiu-filer fra frivillig “woz9ts”, som ikke var i DuXiu's proprietære PDG-format (skal stadig konverteres til PDF). "Upload"-samlingen er opdelt i mindre underkollektioner, som er angivet i AACIDs og torrent-navne. Alle underkollektioner blev først deduplikeret mod hovedsamlingen, selvom metadata "upload_records" JSON-filer stadig indeholder mange referencer til de originale filer. Ikke-bogfiler blev også fjernet fra de fleste underkollektioner og er typisk <em>ikke</em> noteret i "upload_records" JSON. Underkollektionerne er: Noter Underkollektion Mange underkollektioner består selv af under-underkollektioner (f.eks. fra forskellige originale kilder), som er repræsenteret som mapper i “filepath”-felterne. Uploads til Annas Arkiv Vores blogindlæg om disse data <a %(a_worldcat)s>WorldCat</a> er en proprietær database af den non-profit organisation <a %(a_oclc)s>OCLC</a>, som samler metadataoptegnelser fra biblioteker over hele verden. Det er sandsynligvis den største biblioteksmetadata-samling i verden. I oktober 2023 <a %(a_scrape)s>udgav</a> vi en omfattende scraping af OCLC (WorldCat) databasen, i <a %(a_aac)s>Annas Arkiv Containers format</a>. Oktober 2023, første udgivelse: OCLC (WorldCat) Torrents af Annas Arkiv Eksempelpost i Annas Arkiv (original samling) Eksempelpost i Annas Arkiv (“zlib3” samling) Torrenter af Annas Arkiv (metadata + indhold) Blogindlæg om Udgivelse 1 Blogindlæg om Udgivelse 2 I slutningen af 2022 blev de påståede grundlæggere af Z-Library arresteret, og domæner blev beslaglagt af amerikanske myndigheder. Siden da har hjemmesiden langsomt fundet vej online igen. Det er ukendt, hvem der i øjeblikket driver den. Opdatering pr. februar 2023. Z-Library har sine rødder i <a %(a_href)s>Library Genesis</a>-fællesskabet og blev oprindeligt startet med deres data. Siden da har det professionaliseret sig betydeligt og har en meget mere moderne grænseflade. De er derfor i stand til at få mange flere donationer, både økonomisk for at fortsætte med at forbedre deres hjemmeside, samt donationer af nye bøger. De har samlet en stor samling ud over Library Genesis. Samlingen består af tre dele. De originale beskrivelsessider for de første to dele er bevaret nedenfor. Du har brug for alle tre dele for at få alle data (undtagen forældede torrents, som er streget ud på torrentsiden). %(title)s: vores første udgivelse. Dette var den allerførste udgivelse af det, der dengang blev kaldt “Pirate Library Mirror” (“pilimi”). %(title)s: anden udgivelse, denne gang med alle filer pakket ind i .tar-filer. %(title)s: inkrementelle nye udgivelser, ved hjælp af <a %(a_href)s>Annas Arkiv Beholdere (AAC) format</a>, nu udgivet i samarbejde med Z-Library teamet. Det første spejl blev møjsommeligt opnået i løbet af 2021 og 2022. På nuværende tidspunkt er det en smule forældet: det afspejler samlingens tilstand i juni 2021. Vi vil opdatere dette i fremtiden. Lige nu fokuserer vi på at få denne første udgivelse ud. Da Library Genesis allerede er bevaret med offentlige torrents og er inkluderet i Z-Library, foretog vi en grundlæggende deduplikation mod Library Genesis i juni 2022. Til dette brugte vi MD5-hashes. Der er sandsynligvis meget mere duplikeret indhold i biblioteket, såsom flere filformater med den samme bog. Dette er svært at opdage præcist, så det gør vi ikke. Efter deduplikationen har vi over 2 millioner filer tilbage, i alt lige under 7TB. Samlingen består af to dele: en MySQL “.sql.gz” dump af metadataene og de 72 torrentfiler på omkring 50-100GB hver. Metadataene indeholder dataene som rapporteret af Z-Library-webstedet (titel, forfatter, beskrivelse, filtype), samt den faktiske filstørrelse og md5sum, som vi observerede, da disse nogle gange ikke stemmer overens. Der ser ud til at være områder af filer, hvor Z-Library selv har forkerte metadata. Vi kan også have downloadet filer forkert i nogle isolerede tilfælde, som vi vil forsøge at opdage og rette i fremtiden. De store torrentfiler indeholder de faktiske bogdata, med Z-Library ID som filnavn. Filendelserne kan rekonstrueres ved hjælp af metadata-dumpen. Samlingen er en blanding af faglitteratur og skønlitteratur (ikke adskilt som i Library Genesis). Kvaliteten varierer også meget. Denne første udgivelse er nu fuldt tilgængelig. Bemærk, at torrentfilerne kun er tilgængelige gennem vores Tor-spejl. Udgivelse 1 (%(date)s) Dette er en enkelt ekstra torrentfil. Den indeholder ikke nogen ny information, men den har nogle data i sig, der kan tage et stykke tid at beregne. Det gør det praktisk at have, da det ofte er hurtigere at downloade denne torrent end at beregne den fra bunden. Specifikt indeholder den SQLite-indekser for tar-filerne, til brug med <a %(a_href)s>ratarmount</a>. Udgivelse 2 tillæg (%(date)s) Vi har fået alle bøger, der blev tilføjet til Z-Library mellem vores sidste spejl og august 2022. Vi har også gået tilbage og skrabet nogle bøger, som vi missede første gang. Alt i alt er denne nye samling omkring 24TB. Igen er denne samling deduplikeret mod Library Genesis, da der allerede er torrents tilgængelige for den samling. Dataene er organiseret på samme måde som den første udgivelse. Der er en MySQL “.sql.gz” dump af metadataene, som også inkluderer alle metadata fra den første udgivelse, og dermed erstatter den. Vi har også tilføjet nogle nye kolonner: Vi nævnte dette sidste gang, men for at præcisere: "filename" og "md5" er de faktiske egenskaber ved filen, mens "filename_reported" og "md5_reported" er, hvad vi har skrabet fra Z-Library. Nogle gange stemmer disse to ikke overens, så vi inkluderede begge. Til denne udgivelse ændrede vi sorteringen til "utf8mb4_unicode_ci", hvilket burde være kompatibelt med ældre versioner af MySQL. Datafilerne ligner dem fra sidste gang, selvom de er meget større. Vi kunne simpelthen ikke være generet med at skabe tonsvis af mindre torrentfiler. "pilimi-zlib2-0-14679999-extra.torrent" indeholder alle de filer, vi missede i den sidste udgivelse, mens de andre torrenter alle er nye ID-rækker.  <strong>Opdatering %(date)s:</strong> Vi gjorde de fleste af vores torrents for store, hvilket fik torrentklienter til at kæmpe. Vi har fjernet dem og udgivet nye torrents. <strong>Opdatering %(date)s:</strong> Der var stadig for mange filer, så vi pakkede dem ind i tar-filer og udgav nye torrents igen. %(key)s: om denne fil allerede er i Library Genesis, enten i faglitteratur- eller skønlitteratursamlingen (matchet ved md5). %(key)s: hvilken torrent denne fil er i. %(key)s: angivet når vi ikke kunne downloade bogen. Udgivelse 2 (%(date)s) Zlib-udgivelser (originale beskrivelsessider) Tor-domæne Hovedwebsted Z-Library scrape Den “kinesiske” samling i Z-Library ser ud til at være den samme som vores DuXiu-samling, men med forskellige MD5'er. Vi ekskluderer disse filer fra torrents for at undgå duplikering, men viser dem stadig i vores søgeindeks. Metadata Du får %(percentage)s%% procent hurtigere downloads, fordi du blev henvist af bruger %(profile_link)s. Dette gælder for hele medlemskabsperioden. Donér Deltag Valgt op til %(percentage)s%% rabat Alipay understøtter internationale kredit-/debetkort. Se <a %(a_alipay)s>denne vejledning</a> for mere information. Send os Amazon.com-gavekort ved hjælp af dit kredit-/debitkort. Du kan købe krypto ved hjælp af kredit-/debitkort. WeChat (Weixin Pay) understøtter internationale kredit-/debitkort. I WeChat-appen skal du gå til “Me => Services => Wallet => Add a Card”. Hvis du ikke ser det, skal du aktivere det ved hjælp af “Me => Settings => General => Tools => Weixin Pay => Enable”. (bruges ved afsendelse af Ethereum fra Coinbase) kopieret! kopiér (laveste minimumsbeløb) (advarsel: højt minimumsbeløb) -%(percentage)s%% 12 måneder 1 måned 24 måneder 3 måneder 48 måneder 6 måneder 96 måneder Vælg, hvor længe du vil abonnere. <div %(div_monthly_cost)s></div><div %(div_after)s>efter <span %(span_discount)s></span> rabatter</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% for 12 måneder for 1 måned for 24 måneder for 3 måneder for 48 måneder for 6 måneder i 96 måneder %(monthly_cost)s / måned kontakt os Direkte <strong>SFTP</strong> servere Donation på virksomhedsniveau eller udveksling for nye samlinger (f.eks. nye scanninger, OCR’ede datasæt). Ekspertadgang <strong>Ubegrænset</strong> højhastighedsadgang <div %(div_question)s>Kan jeg opgradere mit medlemskab eller få flere medlemskaber?</div> <div %(div_question)s>Kan jeg give en donation uden at blive medlem?</div> Selvfølgelig. Vi accepterer donationer af enhver størrelse på denne Monero (XMR) adresse: %(address)s. <div %(div_question)s>Hvad betyder intervallerne pr. måned?</div> Du kan nå den lave ende af et interval ved at anvende alle rabatterne, såsom at vælge en periode længere end en måned. <div %(div_question)s>Fornyes medlemskaber automatisk?</div> Medlemskaber <strong>fornyes ikke</strong> automatisk. Du kan være medlem så længe eller kort, som du ønsker. <div %(div_question)s>Hvad bruger I donationer på?</div> 100%% går til at bevare og gøre verdens viden og kultur tilgængelig. I øjeblikket bruger vi det mest på servere, lagerplads og båndbredde. Ingen penge går til nogen teammedlemmer personligt. <div %(div_question)s>Kan jeg give en stor donation?</div> Det ville være fantastisk! For donationer over et par tusinde dollars, kontakt os venligst direkte på %(email)s. <div %(div_question)s>Har I andre betalingsmetoder?</div> Ikke i øjeblikket. Mange mennesker ønsker ikke, at arkiver som dette skal eksistere, så vi skal være forsigtige. Hvis du kan hjælpe os med at oprette andre (mere bekvemme) betalingsmetoder sikkert, så kontakt os på %(email)s. Donations-FAQ Du har en <a %(a_donation)s>eksisterende donation</a> i gang. Afslut eller annuller venligst denne donation, før du laver en ny donation. <a %(a_all_donations)s>Se alle mine donationer</a> For donationer over $5000 bedes du kontakte os direkte på %(email)s. Vi byder store donationer fra velhavende individer eller institutioner velkommen.  Vær opmærksom på, at selvom medlemskaberne på denne side er "pr. måned", er de engangsdonationer (ikke tilbagevendende). Se <a %(faq)s>Donations-FAQ</a>. Annas Arkiv er et non-profit, open-source og open-data projekt. Ved at donere og blive medlem, støtter du vores drift og udvikling. Til alle vores medlemmer: tak for at holde os i gang! ❤️ For mere information, se <a %(a_donate)s>Donations FAQ</a>. For at blive medlem, venligst <a %(a_login)s>Log ind eller Registrer</a>. Tak for din støtte! $%(cost)s / måned Hvis du lavede en fejl under betalingen, kan vi ikke refundere, men vi vil forsøge at rette det. Find siden “Krypto” i din PayPal-app eller på hjemmesiden. Dette er typisk under “Finanser”. Gå til siden “Bitcoin” i din PayPal-app eller på hjemmesiden. Tryk på knappen “Overfør” %(transfer_icon)s, og derefter “Send”. Alipay Alipay 支付宝 / WeChat 微信 Amazon-gavekort %(amazon)s gavekort Bankkort Bankkort (ved brug af app) Binance Kredit/debit/Apple/Google (BMC) Cash App Kredit-/debitkort Kredit-/debitkort 2 Kredit-/debitkort (backup) Krypto %(bitcoin_icon)s Kort / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (almindelig) Pix (Brazil) Revolut (midlertidigt utilgængelig) WeChat Vælg din foretrukne kryptomønt: Donér ved hjælp af et Amazon-gavekort. <strong>VIGTIGT:</strong> Denne mulighed er for %(amazon)s. Hvis du vil bruge en anden Amazon-hjemmeside, skal du vælge den ovenfor. <strong>VIGTIGT:</strong> Vi understøtter kun Amazon.com, ikke andre Amazon-websteder. For eksempel understøttes .de, .co.uk, .ca IKKE. Skriv venligst IKKE din egen besked. Indtast det præcise beløb: %(amount)s Bemærk, at vi skal runde op til beløb, der accepteres af vores forhandlere (minimum %(minimum)s). Donér ved hjælp af et kredit-/debetkort gennem Alipay-appen (super nemt at sætte op). Installer Alipay-appen fra <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>. Registrer dig med dit telefonnummer. Ingen yderligere personlige oplysninger er nødvendige. <span %(style)s>1</span>Installer Alipay-appen Understøttet: Visa, MasterCard, JCB, Diners Club og Discover. Se <a %(a_alipay)s>denne vejledning</a> for mere information. <span %(style)s>2</span>Tilføj bankkort Med Binance kan du købe Bitcoin med et kredit-/debitkort eller bankkonto og derefter donere den Bitcoin til os. På den måde kan vi forblive sikre og anonyme, når vi modtager din donation. Binance er tilgængelig i næsten alle lande og understøtter de fleste banker og kredit-/debitkort. Dette er i øjeblikket vores hovedanbefaling. Vi værdsætter, at du tager dig tid til at lære, hvordan du donerer ved hjælp af denne metode, da det hjælper os meget. For kreditkort, debitkort, Apple Pay og Google Pay bruger vi “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). I deres system er en “kaffe” lig med $5, så din donation vil blive afrundet til nærmeste multiplum af 5. Donér ved hjælp af Cash App. Hvis du har Cash App, er dette den nemmeste måde at donere på! Bemærk, at for transaktioner under %(amount)s kan Cash App opkræve et %(fee)s gebyr. For %(amount)s eller derover er det gratis! Donér med et kredit- eller debetkort. Denne metode bruger en kryptovalutaudbyder som en mellemmand til konvertering. Dette kan være lidt forvirrende, så brug kun denne metode, hvis andre betalingsmetoder ikke virker. Det virker heller ikke i alle lande. Vi kan ikke understøtte kredit-/debetkort direkte, fordi bankerne ikke vil samarbejde med os. ☹ Der er dog flere måder at bruge kredit-/debetkort alligevel, ved hjælp af andre betalingsmetoder: Med krypto kan du donere ved hjælp af BTC, ETH, XMR og SOL. Brug denne mulighed, hvis du allerede er bekendt med kryptovaluta. Med krypto kan du donere ved hjælp af BTC, ETH, XMR og mere. Krypto ekspres tjenester Hvis du bruger krypto for første gang, foreslår vi at bruge %(options)s til at købe og donere Bitcoin (den oprindelige og mest brugte kryptovaluta). Bemærk, at for små donationer kan kreditkortgebyrerne eliminere vores %(discount)s%% rabat, så vi anbefaler længere abonnementer. Donér ved hjælp af kredit-/debitkort, PayPal eller Venmo. Du kan vælge mellem disse på den næste side. Google Pay og Apple Pay kan også virke. Bemærk, at for små donationer er gebyrerne høje, så vi anbefaler længere abonnementer. For at donere ved hjælp af PayPal US, vil vi bruge PayPal Crypto, som giver os mulighed for at forblive anonyme. Vi sætter pris på, at du tager dig tid til at lære, hvordan du donerer ved hjælp af denne metode, da det hjælper os meget. Donér ved hjælp af PayPal. Doner ved hjælp af din almindelige PayPal-konto. Doner ved hjælp af Revolut. Hvis du har Revolut, er dette den nemmeste måde at donere på! Denne betalingsmetode tillader kun et maksimum på %(amount)s. Vælg venligst en anden varighed eller betalingsmetode. Denne betalingsmetode kræver et minimum på %(amount)s. Vælg venligst en anden varighed eller betalingsmetode. Binance Coinbase Blæksprutte Vælg venligst en betalingsmetode. “Adoptér en torrent”: dit brugernavn eller besked i en torrent-filnavn <div %(div_months)s>én gang hver 12. måned af medlemskab</div> Dit brugernavn eller anonym omtale i krediteringerne Tidlig adgang til nye funktioner Eksklusiv Telegram med opdateringer bag kulisserne %(number)s hurtige downloads per dag hvis du donerer denne måned! <a %(a_api)s>JSON API</a> adgang Legendarisk status i bevarelsen af menneskehedens viden og kultur Tidligere fordele, plus: Tjen <strong>%(percentage)s%% hurtigere downloads</strong> ved at <a %(a_refer)s>henvise venner</a>. SciDB artikler <strong>ubegrænset</strong> uden verifikation Når du stiller spørgsmål om konto eller donation, tilføj dit kontonummer, skærmbilleder, kvitteringer, så meget information som muligt. Vi tjekker kun vores e-mail hver 1-2 uge, så hvis du ikke inkluderer disse oplysninger, vil det forsinke enhver løsning. For at få endnu flere downloads, <a %(a_refer)s>henvis dine venner</a>! Vi er et lille hold af frivillige. Det kan tage os 1-2 uger at svare. Bemærk, at kontonavnet eller billedet kan se mærkeligt ud. Ingen grund til bekymring! Disse konti administreres af vores donationspartnere. Vores konti er ikke blevet hacket. Doner <span %(span_cost)s></span> <span %(span_label)s></span> i 12 måneder “%(tier_name)s” i 1 måned “%(tier_name)s” i 24 måneder “%(tier_name)s” i 3 måneder “%(tier_name)s” i 48 måneder “%(tier_name)s” i 6 måneder “%(tier_name)s” i 96 måneder “%(tier_name)s” Du kan stadig annullere donationen under betalingen. Klik på doner-knappen for at bekræfte denne donation. <strong>Vigtig bemærkning:</strong> Kryptopriser kan svinge voldsomt, nogle gange endda så meget som 20%% på få minutter. Dette er stadig mindre end de gebyrer, vi pådrager os med mange betalingsudbydere, som ofte opkræver 50-60%% for at arbejde med en “skyggevelgørenhed” som os. <u>Hvis du sender os kvitteringen med den oprindelige pris, du betalte, vil vi stadig kreditere din konto for det valgte medlemskab</u> (så længe kvitteringen ikke er ældre end et par timer). Vi sætter virkelig pris på, at du er villig til at håndtere sådanne ting for at støtte os! ❤️ ❌ Noget gik galt. Genindlæs siden og prøv igen. <span %(span_circle)s>1</span>Køb Bitcoin på Paypal <span %(span_circle)s>2</span>Overfør Bitcoin til vores adresse ✅ Omdirigerer til donationssiden… Donér Vent venligst mindst <span %(span_hours)s>24 timer</span> (og opdater denne side) før du kontakter os. Hvis du ønsker at donere (ethvert beløb) uden medlemskab, er du velkommen til at bruge denne Monero (XMR) adresse: %(address)s. Efter at have sendt dit gavekort, vil vores automatiserede system bekræfte det inden for få minutter. Hvis dette ikke virker, prøv at sende dit gavekort igen (<a %(a_instr)s>instruktioner</a>). Hvis det stadig ikke virker, bedes du sende os en e-mail, og Anna vil manuelt gennemgå det (dette kan tage et par dage), og sørg for at nævne, om du har prøvet at sende igen. Eksempel: Brug venligst <a %(a_form)s>den officielle Amazon.com formular</a> til at sende os et gavekort på %(amount)s til e-mailadressen nedenfor. “Til” modtagerens e-mail i formularen: Amazon gavekort Vi kan ikke acceptere andre metoder til gavekort, <strong>kun sendt direkte fra den officielle formular på Amazon.com</strong>. Vi kan ikke returnere dit gavekort, hvis du ikke bruger denne formular. Brug kun én gang. Unik for din konto, del ikke. Venter på gavekort… (opdater siden for at tjekke) Åbn <a %(a_href)s>QR-kode donationssiden</a>. Scan QR-koden med Alipay-appen, eller tryk på knappen for at åbne Alipay-appen. Vær venligst tålmodig; siden kan tage lidt tid at indlæse, da den er i Kina. <span %(style)s>3</span>Foretag donation (scan QR-kode eller tryk på knappen) Køb PYUSD-mønt på PayPal Køb Bitcoin (BTC) på Cash App Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er tilbage. Gå til “Bitcoin” (BTC) siden i Cash App. Overfør Bitcoin til vores adresse For små donationer (under $25), kan du muligvis bruge Rush eller Priority. Klik på “Send bitcoin” knappen for at foretage en “udbetaling”. Skift fra dollars til BTC ved at trykke på %(icon)s ikonet. Indtast BTC-beløbet nedenfor og klik på “Send”. Se <a %(help_video)s>denne video</a> hvis du sidder fast. Ekspres tjenester er bekvemme, men opkræver højere gebyrer. Du kan bruge dette i stedet for en krypto børs, hvis du ønsker hurtigt at lave en større donation og ikke har noget imod et gebyr på $5-10. Sørg for at sende det nøjagtige krypto beløb, der vises på donationssiden, ikke beløbet i $USD. Ellers vil gebyret blive trukket fra, og vi kan ikke automatisk behandle dit medlemskab. Nogle gange kan bekræftelse tage op til 24 timer, så sørg for at opdatere denne side (selv hvis den er udløbet). Kredit- / debetkortinstruktioner Donér via vores kredit- / debetkorts side Nogle af trinene nævner kryptotegnebøger, men bare rolig, du behøver ikke at lære noget om krypto for dette. %(coin_name)s instruktioner Scan denne QR -kode med din Crypto Wallet -app til hurtigt at udfylde betalingsoplysningerne Scan QR -kode for at betale Vi understøtter kun standardversionen af kryptomønter, ingen eksotiske netværk eller versioner af mønter. Det kan tage op til en time at bekræfte transaktionen, afhængigt af mønten. Donér %(amount)s på <a %(a_page)s>denne side</a>. Denne donation er udløbet. Annuller venligst og opret en ny. Hvis du allerede har betalt: Ja, jeg har sendt min kvittering via e-mail Hvis kryptovalutakursen svingede under transaktionen, skal du sørge for at inkludere kvitteringen, der viser den oprindelige valutakurs. Vi sætter virkelig pris på, at du tager besværet med at bruge krypto, det hjælper os meget! ❌ Noget gik galt. Genindlæs siden og prøv igen. <span %(span_circle)s>%(circle_number)s</span>Email os kvitteringen Hvis du støder på problemer, bedes du kontakte os på %(email)s og inkludere så mange oplysninger som muligt (såsom skærmbilleder). ✅ Tak for din donation! Anna vil manuelt aktivere dit medlemskab inden for et par dage. Send en kvittering eller skærmbillede til din personlige verificeringsadresse: Når du har sendt din kvittering via e-mail, klik på denne knap, så Anna kan gennemgå den manuelt (dette kan tage et par dage): Send en kvittering eller skærmbillede til din personlige verifikationsadresse. Brug IKKE denne e-mailadresse til din PayPal-donation. Annuller Ja, venligst annuller Er du sikker på, at du ønsker at annullere? Annuller ikke, hvis du allerede har betalt. ❌ Noget gik galt. Genindlæs venligst siden og prøv igen. Foretag en ny donation ✅ Din donation er blevet annulleret. Dato: %(date)s Identifikator: %(id)s Genbestil Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned i %(duration)s måneder, inklusive %(discounts)s%% rabat)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned i %(duration)s måneder)</span> 1. Indtast din e-mail. 2. Vælg din betalingsmetode. 3. Vælg din betalingsmetode igen. 4. Vælg "Selvhostet" tegnebog. 5. Klik på "Jeg bekræfter ejerskab". 6. Du bør modtage en e-mailkvittering. Send den venligst til os, og vi vil bekræfte din donation så hurtigt som muligt. (du vil måske annullere og oprette en ny donation) Betalingsinstruktionerne er nu forældede. Hvis du ønsker at foretage en ny donation, brug knappen "Genbestil" ovenfor. Du har allerede betalt. Hvis du alligevel vil gennemgå betalingsinstruktionerne, klik her: Vis gamle betalingsinstruktioner Hvis donationssiden bliver blokeret, prøv en anden internetforbindelse (f.eks. VPN eller telefoninternet). Desværre er Alipay-siden ofte kun tilgængelig fra <strong>fastlands-Kina</strong>. Du kan være nødt til midlertidigt at deaktivere din VPN eller bruge en VPN til fastlands-Kina (eller Hongkong fungerer også nogle gange). <span %(span_circle)s>1</span>Doner på Alipay Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne Alipay-konto</a> Alipay-instruktioner <span %(span_circle)s>1</span>Overfør til en af vores kryptokonti Donér det samlede beløb af %(total)s til en af disse adresser: Kryptoinstruktioner Følg instruktionerne for at købe Bitcoin (BTC). Du behøver kun at købe det beløb, du ønsker at donere, %(total)s. Indtast vores Bitcoin (BTC) adresse som modtager, og følg instruktionerne for at sende din donation på %(total)s: <span %(span_circle)s>1</span>Donér på Pix Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne Pix-konto Pix-instruktioner <span %(span_circle)s>1</span>Donér på WeChat Donér det samlede beløb på %(total)s ved at bruge <a %(a_account)s>denne WeChat-konto</a> WeChat-instruktioner Brug en af følgende “kreditkort til Bitcoin” ekspres-tjenester, som kun tager et par minutter: BTC / Bitcoin adresse (ekstern wallet): BTC / Bitcoin beløb: Udfyld følgende oplysninger i formularen: Hvis nogen af disse oplysninger er forældede, bedes du sende os en e-mail for at informere os. Brug venligst dette <span %(underline)s>præcise beløb</span>. Din samlede omkostning kan være højere på grund af kreditkortgebyrer. For små beløb kan dette desværre være mere end vores rabat. (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, ingen verifikation for første transaktion) (minimum: %(minimum)s) (minimum: %(minimum)s afhængig af land, ingen verifikation for første transaktion) Følg instruktionerne for at købe PYUSD-mønt (PayPal USD). Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er til overs. Gå til “PYUSD”-siden i din PayPal-app eller på hjemmesiden. Tryk på “Overfør”-knappen %(icon)s, og derefter “Send”. Opdater status For at nulstille timeren skal du blot oprette en ny donation. Sørg for at bruge BTC-beløbet nedenfor, <em>IKKE</em> euro eller dollars, ellers modtager vi ikke det korrekte beløb og kan ikke automatisk bekræfte dit medlemskab. Køb Bitcoin (BTC) på Revolut Køb lidt mere (vi anbefaler %(more)s mere) end det beløb, du donerer (%(amount)s), for at dække transaktionsgebyrer. Du beholder det, der er til overs. Gå til “Crypto”-siden i Revolut for at købe Bitcoin (BTC). Overfør Bitcoin til vores adresse For små donationer (under $25) kan du være nødt til at bruge Rush eller Priority. Klik på “Send bitcoin”-knappen for at foretage en “udbetaling”. Skift fra euro til BTC ved at trykke på %(icon)s-ikonet. Indtast BTC-beløbet nedenfor og klik på “Send”. Se <a %(help_video)s>denne video</a>, hvis du sidder fast. Status: 1 2 Trin-for-trin guide Se trin-for-trin guiden nedenfor. Ellers risikerer du at blive låst ude af denne konto! Hvis du ikke allerede har gjort det, skriv din hemmelige nøgle ned til at logge ind: Tak for din donation! Tid tilbage: Donation Overfør %(amount)s til %(account)s Venter på bekræftelse (opdater siden for at tjekke)… Venter på overførsel (opdater siden for at tjekke)… Tidligere Hurtige downloads inden for de sidste 24 timer tæller med i den daglige grænse. Downloads fra Hurtige Partner Servere er markeret med %(icon)s. Sidste 18 timer Ingen filer downloadet endnu. Downloadede filer vises ikke offentligt. Alle tider er i UTC. Downloadede filer Hvis du har downloadet en fil med både hurtige og langsomme downloads, vil den vises to gange. Bekymr dig ikke for meget, der er mange mennesker, der downloader fra de hjemmesider, vi linker til, og det er ekstremt sjældent at komme i problemer. For at være på den sikre side anbefaler vi at bruge en VPN (betalt) eller <a %(a_tor)s>Tor</a> (gratis). Jeg downloadede 1984 af George Orwell, vil politiet komme til min dør? Du er Anna! Hvem er Anna? Vi har en stabil JSON API for medlemmer, til at få en hurtig download-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentation inden for JSON selv). Til andre brugsscenarier, såsom at iterere gennem alle vores filer, bygge tilpassede søgninger og så videre, anbefaler vi <a %(a_generate)s>at generere</a> eller <a %(a_download)s>downloade</a> vores ElasticSearch og MariaDB databaser. De rå data kan manuelt udforskes <a %(a_explore)s>gennem JSON-filer</a>. Vores rå torrentliste kan også downloades som <a %(a_torrents)s>JSON</a>. Har I en API? Vi hoster ikke noget ophavsretligt beskyttet materiale her. Vi er en søgemaskine og indekserer derfor kun metadata, der allerede er offentligt tilgængelige. Når du downloader fra disse eksterne kilder, foreslår vi, at du tjekker lovene i din jurisdiktion med hensyn til, hvad der er tilladt. Vi er ikke ansvarlige for indhold hostet af andre. Hvis du har klager over, hvad du ser her, er det bedste at kontakte den oprindelige hjemmeside. Vi trækker regelmæssigt deres ændringer ind i vores database. Hvis du virkelig mener, at du har en gyldig DMCA-klage, som vi bør reagere på, bedes du udfylde <a %(a_copyright)s>DMCA / Copyright-klageformularen</a>. Vi tager dine klager alvorligt og vender tilbage til dig så hurtigt som muligt. Hvordan rapporterer jeg ophavsretskrænkelser? Her er nogle bøger, der har særlig betydning for skyggebiblioteker og digital bevaring: Hvad er jeres yndlingsbøger? Vi vil også gerne minde alle om, at al vores kode og data er fuldstændig open source. Dette er unikt for projekter som vores — vi kender ikke til noget andet projekt med en tilsvarende massiv katalog, der også er fuldstændig open source. Vi byder meget velkommen til alle, der mener, at vi kører vores projekt dårligt, til at tage vores kode og data og oprette deres eget skyggebibliotek! Vi siger ikke dette af trods eller noget — vi synes oprigtigt, at dette ville være fantastisk, da det ville hæve standarden for alle og bedre bevare menneskehedens arv. Jeg hader, hvordan I kører dette projekt! Vi ville elske, hvis folk opsætter <a %(a_mirrors)s>spejle</a>, og vi vil økonomisk støtte dette. Hvordan kan jeg hjælpe? Det gør vi faktisk. Vores inspiration til at samle metadata er Aaron Swartz' mål om “en webside for hver bog, der nogensinde er udgivet”, for hvilket han skabte <a %(a_openlib)s>Open Library</a>. Det projekt har klaret sig godt, men vores unikke position giver os mulighed for at få metadata, som de ikke kan. En anden inspiration var vores ønske om at vide <a %(a_blog)s>hvor mange bøger der er i verden</a>, så vi kan beregne, hvor mange bøger vi stadig har tilbage at redde. Indsamler I metadata? Bemærk, at mhut.org blokerer visse IP-områder, så en VPN kan være nødvendig. <strong>Android:</strong> Klik på tre-punkts menuen øverst til højre, og vælg “Tilføj til startskærm”. <strong>iOS:</strong> Klik på “Del” knappen nederst, og vælg “Tilføj til startskærm”. Vi har ikke en officiel mobilapp, men du kan installere denne hjemmeside som en app. Har I en mobilapp? Send dem venligst til <a %(a_archive)s>Internet Archive</a>. De vil bevare dem korrekt. Hvordan donerer jeg bøger eller andre fysiske materialer? Hvordan anmoder jeg om bøger? <a %(a_blog)s>Annas Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmæssige opdateringer <a %(a_software)s>Annas Software</a> — vores open source-kode <a %(a_datasets)s>Datasets</a> — om dataene <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domæner Er der flere ressourcer om Annas Arkiv? <a %(a_translate)s>Oversæt på Annas Software</a> — vores oversættelsessystem <a %(a_wikipedia)s>Wikipedia</a> — mere om os (hjælp venligst med at holde denne side opdateret, eller opret en for dit eget sprog!) Vælg de indstillinger, du kan lide, lad søgefeltet være tomt, klik på “Søg”, og bogmærk derefter siden ved hjælp af din browsers bogmærkefunktion. Hvordan gemmer jeg mine søgeindstillinger? Vi byder sikkerhedsforskere velkommen til at søge efter sårbarheder i vores systemer. Vi er store tilhængere af ansvarlig offentliggørelse. Kontakt os <a %(a_contact)s>her</a>. Vi er i øjeblikket ikke i stand til at tildele bug bounties, undtagen for sårbarheder, der har <a %(a_link)s>potentiale til at kompromittere vores anonymitet</a>, for hvilke vi tilbyder bounties i området $10k-50k. Vi vil gerne tilbyde bredere scope for bug bounties i fremtiden! Bemærk venligst, at social engineering-angreb er uden for scope. Hvis du er interesseret i offensiv sikkerhed og vil hjælpe med at arkivere verdens viden og kultur, så sørg for at kontakte os. Der er mange måder, hvorpå du kan hjælpe. Har I et program for ansvarlig offentliggørelse? Vi har bogstaveligt talt ikke nok ressourcer til at give alle i verden højhastighedsdownloads, så meget som vi gerne ville. Hvis en rig velgører ville træde til og give os dette, ville det være fantastisk, men indtil da gør vi vores bedste. Vi er et non-profit projekt, der knap nok kan opretholde sig selv gennem donationer. Derfor har vi implementeret to systemer for gratis downloads med vores partnere: delte servere med langsomme downloads og lidt hurtigere servere med en venteliste (for at reducere antallet af personer, der downloader samtidig). Vi har også <a %(a_verification)s>browserverifikation</a> for vores langsomme downloads, fordi bots og skrabere ellers vil misbruge dem, hvilket gør det endnu langsommere for legitime brugere. Bemærk, at når du bruger Tor Browser, kan du være nødt til at justere dine sikkerhedsindstillinger. På den laveste af mulighederne, kaldet “Standard”, lykkes Cloudflare turnstile-udfordringen. På de højere muligheder, kaldet “Sikrere” og “Sikrest”, mislykkes udfordringen. For store filer kan langsomme downloads nogle gange bryde midtvejs. Vi anbefaler at bruge en download manager (såsom JDownloader) til automatisk at genoptage store downloads. Hvorfor er de langsomme downloads så langsomme? Ofte stillede spørgsmål (FAQ) Brug <a %(a_list)s>torrentlistegeneratoren</a> til at generere en liste over torrents, der har mest brug for seeding, inden for dine lagerpladsgrænser. Ja, se <a %(a_llm)s>LLM data</a> siden. De fleste torrents indeholder filerne direkte, hvilket betyder, at du kan instruere torrentklienter til kun at downloade de nødvendige filer. For at bestemme hvilke filer der skal downloades, kan du <a %(a_generate)s>generere</a> vores metadata, eller <a %(a_download)s>downloade</a> vores ElasticSearch- og MariaDB-databaser. Desværre indeholder en række torrent-samlinger .zip- eller .tar-filer i roden, i hvilket tilfælde du skal downloade hele torrenten, før du kan vælge individuelle filer. Der er endnu ikke tilgængelige nemme værktøjer til at filtrere torrents, men vi byder bidrag velkommen. (Vi har dog <a %(a_ideas)s>nogle idéer</a> til sidstnævnte tilfælde.) Langt svar: Kort svar: ikke nemt. Vi forsøger at holde minimal duplikering eller overlap mellem torrents i denne liste, men det kan ikke altid opnås og afhænger meget af politikkerne i kildens biblioteker. For biblioteker, der udgiver deres egne torrents, er det uden for vores kontrol. For torrents udgivet af Annas Arkiv, deduplikerer vi kun baseret på MD5-hash, hvilket betyder, at forskellige versioner af den samme bog ikke bliver deduplikeret. Ja. Disse er faktisk PDF'er og EPUB'er, de har bare ikke en udvidelse i mange af vores torrents. Der er to steder, hvor du kan finde metadata for torrentfiler, inklusive filtyper/udvidelser: 1. Hver samling eller udgivelse har sine egne metadata. For eksempel har <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> en tilsvarende metadatadatabase, der er hostet på Libgen.rs-websitet. Vi linker typisk til relevante metadataressourcer fra hver samlings <a %(a_datasets)s>dataside</a>. 2. Vi anbefaler at <a %(a_generate)s>generere</a> eller <a %(a_download)s>downloade</a> vores ElasticSearch- og MariaDB-databaser. Disse indeholder en mapping for hver post i Annas Arkiv til de tilsvarende torrentfiler (hvis tilgængelige), under “torrent_paths” i ElasticSearch JSON. Nogle torrent-klienter understøtter ikke store stykstørrelser, som mange af vores torrents har (for nyere torrents gør vi ikke dette længere — selvom det er gyldigt ifølge specifikationerne!). Så prøv en anden klient, hvis du støder på dette problem, eller klag til producenten af din torrent-klient. Jeg vil gerne hjælpe med at seede, men jeg har ikke meget diskplads. Torrenterne er for langsomme; kan jeg downloade dataene direkte fra jer? Kan jeg kun downloade et underudvalg af filerne, som kun et bestemt sprog eller emne? Hvordan håndterer I dubletter i torrents? Kan jeg få torrent-listen som JSON? Jeg ser ikke PDF'er eller EPUB'er i torrents, kun binære filer? Hvad gør jeg? Hvorfor kan min torrent-klient ikke åbne nogle af jeres torrent-filer / magnet-links? Torrents FAQ Hvordan uploader jeg nye bøger? Se venligst <a %(a_href)s>dette fremragende projekt</a>. Har du en oppetidsovervågning? Hvad er Anna’s Arkiv? Bliv medlem for at bruge hurtige downloads. Vi understøtter nu Amazon-gavekort, kredit- og debetkort, krypto, Alipay og WeChat. Du har opbrugt dine hurtige downloads for i dag. Adgang Timelige downloads i de sidste 30 dage. Timeligt gennemsnit: %(hourly)s. Dagligt gennemsnit: %(daily)s. Vi arbejder med partnere for at gøre vores samlinger let og frit tilgængelige for alle. Vi mener, at alle har ret til menneskehedens kollektive visdom. Og <a %(a_search)s>ikke på bekostning af forfatterne</a>. Datasættene brugt i Anna’s Arkiv er helt åbne og kan spejles i bulk ved hjælp af torrents. <a %(a_datasets)s>Lær mere…</a> Langtidsarkiv Fuld database Søg Bøger, artikler, magasiner, tegneserier, biblioteksposter, metadata, … Alt vores <a %(a_code)s>kode</a> og <a %(a_datasets)s>data</a> er fuldstændig open source. <span %(span_anna)s>Anna’s Arkiv</span> er et non-profit projekt med to mål: <li><strong>Bevaring:</strong> Sikkerhedskopiering af al menneskelig viden og kultur.</li><li><strong>Adgang:</strong> At gøre denne viden og kultur tilgængelig for alle i verden.</li> Vi har verdens største samling af tekstdata af høj kvalitet. <a %(a_llm)s>Lær mere…</a> LLM training data 🪩 Spejle: opfordring til frivillige Hvis du driver en højrisiko anonym betalingsprocessor, bedes du kontakte os. Vi søger også folk, der ønsker at placere smagfulde små annoncer. Alle indtægter går til vores bevaringsindsats. Bevaring Vi anslår, at vi har bevaret omkring <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% af verdens bøger</a>. Vi bevarer bøger, artikler, tegneserier, magasiner og mere ved at samle disse materialer fra forskellige <a href="https://en.wikipedia.org/wiki/Shadow_library">skyggebiblioteker</a>, officielle biblioteker og andre samlinger ét sted. Alle disse data bevares for evigt ved at gøre det nemt at kopiere dem i bulk — ved hjælp af torrents — hvilket resulterer i mange kopier rundt om i verden. Nogle skyggebiblioteker gør allerede dette selv (f.eks. Sci-Hub, Library Genesis), mens Anna’s Arkiv “frigør” andre biblioteker, der ikke tilbyder bulkdistribution (f.eks. Z-Library) eller slet ikke er skyggebiblioteker (f.eks. Internet Archive, DuXiu). Denne brede distribution, kombineret med open-source kode, gør vores hjemmeside modstandsdygtig over for nedlukninger og sikrer den langsigtede bevaring af menneskehedens viden og kultur. Læs mere om <a href="/datasets">vores datasæt</a>. Hvis du er <a %(a_member)s>medlem</a>, er browserverifikation ikke påkrævet. 🧬&nbsp;SciDB er en fortsættelse af Sci-Hub. SciDB Åben DOI Sci-Hub har <a %(a_paused)s>pauseret</a> upload af nye artikler. Direkte adgang til %(count)s akademiske artikler 🧬&nbsp;SciDB er en fortsættelse af Sci-Hub, med dens velkendte grænseflade og direkte visning af PDF'er. Indtast din DOI for at se. Vi har hele Sci-Hub-samlingen samt nye artikler. De fleste kan ses direkte med en velkendt grænseflade, der ligner Sci-Hub. Nogle kan downloades gennem eksterne kilder, i hvilket tilfælde vi viser links til dem. Du kan hjælpe enormt ved at seede torrents. <a %(a_torrents)s>Lær mere…</a> >%(count)s seedere <%(count)s seedere %(count_min)s–%(count_max)s seedere 🤝 Søger frivillige Som et non-profit, open-source projekt søger vi altid folk til at hjælpe. IPFS-downloads Liste af %(by)s, oprettet <span %(span_time)s>%(time)s</span> Gem ❌ Noget gik galt. Prøv venligst igen. ✅ Gemt. Genindlæs venligst siden. Listen er tom. rediger Tilføj eller fjern fra denne liste ved at finde en fil og åbne fanen “Lister”. Liste Hvordan vi kan hjælpe Fjernelse af overlapning (deduplikation) Tekst- og metadataudtræk OCR Vi er i stand til at give højhastighedsadgang til vores fulde samlinger samt til uudgivne samlinger. Dette er adgang på virksomhedsniveau, som vi kan tilbyde for donationer i størrelsesordenen titusinder af USD. Vi er også villige til at bytte dette for høj-kvalitets samlinger, som vi endnu ikke har. Vi kan refundere dig, hvis du er i stand til at give os berigelse af vores data, såsom: Støt langsigtet arkivering af menneskelig viden, mens du får bedre data til din model! <a %(a_contact)s>Kontakt os</a> for at diskutere, hvordan vi kan arbejde sammen. Det er velkendt, at LLM'er trives med data af høj kvalitet. Vi har verdens største samling af bøger, artikler, magasiner osv., som er nogle af de højeste kvalitetstekstkilder. LLM-data Unik skala og rækkevidde Vores samling indeholder over hundrede millioner filer, herunder akademiske tidsskrifter, lærebøger og magasiner. Vi opnår denne skala ved at kombinere store eksisterende arkiver. Nogle af vores kildekollektioner er allerede tilgængelige i bulk (Sci-Hub og dele af Libgen). Andre kilder har vi selv frigjort. <a %(a_datasets)s>Datasets</a> viser en fuld oversigt. Vores samling inkluderer millioner af bøger, artikler og magasiner fra før e-bogens æra. Store dele af denne samling er allerede blevet OCR'et og har allerede lidt intern overlapning. Fortsæt Hvis du har mistet din nøgle, bedes du <a %(a_contact)s>kontakte os</a> og give så mange oplysninger som muligt. Du skal muligvis midlertidigt oprette en ny konto for at kontakte os. Venligst <a %(a_account)s>log ind</a> for at se denne side.</a> For at forhindre spam-bots i at oprette mange konti, skal vi først verificere din browser. Hvis du sidder fast i en uendelig løkke, anbefaler vi at installere <a %(a_privacypass)s>Privacy Pass</a>. Det kan også hjælpe at slå annonceblokkere og andre browserudvidelser fra. Log ind / Registrer Anna’s Arkiv er midlertidigt nede for vedligeholdelse. Kom venligst tilbage om en time. Alternativ forfatter Alternativ beskrivelse Alternativ udgave Alternativ filtype Alternativ filnavn Alternativ udgiver Alternativ titel dato open sourced Læs mere… beskrivelse Søg i Anna’s Arkiv efter CADAL SSNO nummer Søg i Anna’s Arkiv efter DuXiu SSID nummer Søg i Anna’s Arkiv efter DuXiu DXID nummer Søg i Anna’s Arkiv efter ISBN Søg i Anna’s Arkiv efter OCLC (WorldCat) nummer Søg i Anna’s Arkiv efter Open Library ID Annas Arkiv online fremviser %(count)s berørte sider Efter download: En bedre version af denne fil kan være tilgængelig på %(link)s Bulk torrent downloads samling Brug onlineværktøjer til at konvertere mellem formater. Anbefalede konverteringsværktøjer: %(links)s For store filer anbefaler vi at bruge en download manager for at undgå afbrydelser. Anbefalede download managers: %(links)s EBSCOhost eBook Index (kun for eksperter) (klik også på “GET” øverst) (klik på “GET” øverst) Eksterne downloads <strong>🚀 Hurtige downloads</strong> Du har %(remaining)s tilbage i dag. Tak fordi du er medlem! ❤️ <strong>🚀 Hurtige downloads</strong> Du har opbrugt dine hurtige downloads for i dag. <strong>🚀 Hurtige downloads</strong> Du har downloadet denne fil for nylig. Links forbliver gyldige i et stykke tid. <strong>🚀 Hurtige downloads</strong> Bliv <a %(a_membership)s>medlem</a> for at støtte den langsigtede bevaring af bøger, artikler og mere. For at vise vores taknemmelighed for din støtte, får du hurtige downloads. ❤️ 🚀 Hurtige downloads 🐢 Langsomme downloads Lån fra Internet Archive IPFS Gateway #%(num)d (du skal muligvis prøve flere gange med IPFS) Libgen.li Libgen.rs Skønlitteratur Libgen.rs Non-Fiction deres annoncer er kendt for at indeholde skadelig software, så brug en annonceblokker eller klik ikke på annoncer Amazons “Send to Kindle” djazz’ “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC-filer kan være upålidelige at downloade) Ingen downloads fundet. Alle downloadmuligheder har den samme fil og bør være sikre at bruge. Når det er sagt, vær altid forsigtig, når du downloader filer fra internettet, især fra eksterne sider til Anna’s Arkiv. For eksempel, sørg for at holde dine enheder opdaterede. (ingen omdirigering) Åbn i vores fremviser (åbn i fremviser) Option #%(num)d: %(link)s %(extra)s Find originalposten i CADAL Søg manuelt på DuXiu Find originalposten i ISBNdb Find originalposten i WorldCat Find originalposten i Open Library Søg i forskellige andre databaser efter ISBN (kun for print-handicappede) PubMed Du skal bruge en e-bog eller PDF-læser for at åbne filen, afhængigt af filformatet. Anbefalede e-bogslæsere: %(links)s Anna’s Arkiv 🧬 SciDB Sci-Hub: %(doi)s (tilknyttet DOI er muligvis ikke tilgængelig i Sci-Hub) Du kan sende både PDF- og EPUB-filer til din Kindle eller Kobo eReader. Anbefalede værktøjer: %(links)s Mere information i <a %(a_slow)s>FAQ</a>. Støt forfattere og biblioteker Hvis du kan lide dette og har råd til det, overvej at købe originalen eller støtte forfatterne direkte. Hvis dette er tilgængeligt på dit lokale bibliotek, overvej at låne det gratis der. Partner Server-downloads midlertidigt ikke tilgængelige for denne fil. torrent Fra betroede partnere. Z-Library Z-Library på Tor (kræver Tor Browser) vis eksterne downloads <span class="font-bold">❌ Denne fil kan have problemer og er blevet skjult fra et kildebibliotek.</span> Nogle gange er det på anmodning af en ophavsretshaver, andre gange fordi der er et bedre alternativ tilgængeligt, men nogle gange er det på grund af et problem med selve filen. Det kan stadig være fint at downloade, men vi anbefaler først at søge efter en alternativ fil. Flere detaljer: Hvis du stadig vil downloade denne fil, skal du sørge for kun at bruge betroet, opdateret software til at åbne den. metadata kommentarer AA: Søg i Annas Arkiv efter “%(name)s” Koder Udforsker: Vis i Koder Udforsker “%(name)s” URL: Hjemmeside: Hvis du har denne fil og den endnu ikke er tilgængelig i Anna’s Arkiv, overvej at <a %(a_request)s>uploade den</a>. Internet Archive Controlled Digital Lending fil “%(id)s” Dette er en registrering af en fil fra Internet Archive, ikke en direkte downloadbar fil. Du kan prøve at låne bogen (link nedenfor), eller bruge denne URL, når du <a %(a_request)s>anmoder om en fil</a>. Forbedr metadata CADAL SSNO %(id)s metadataregistrering Dette er en metadata-post, ikke en fil, der kan downloades. Du kan bruge denne URL, når du <a %(a_request)s>anmoder om en fil</a>. DuXiu SSID %(id)s metadataregistrering ISBNdb %(id)s metadataregistrering MagzDB ID %(id)s metadata post Nexus/STC ID %(id)s metadata post OCLC (WorldCat) nummer %(id)s metadataregistrering Open Library %(id)s metadataregistrering Sci-Hub fil “%(id)s” Ikke fundet “%(md5_input)s” blev ikke fundet i vores database. Tilføj kommentar (%(count)s) Du kan få md5 fra URL'en, f.eks. MD5 af en bedre version af denne fil (hvis relevant). Udfyld dette, hvis der er en anden fil, der tæt matcher denne fil (samme udgave, samme filtype, hvis du kan finde en), som folk bør bruge i stedet for denne fil. Hvis du kender til en bedre version af denne fil uden for Anna’s Arkiv, så <a %(a_upload)s>upload den</a> venligst. Noget gik galt. Genindlæs siden og prøv igen. Du har efterladt en kommentar. Det kan tage et minut, før den vises. Venligst brug <a %(a_copyright)s>DMCA / Copyright-krav formularen</a>. Beskriv problemet (påkrævet) Hvis denne fil har høj kvalitet, kan du diskutere alt om den her! Hvis ikke, brug venligst knappen "Rapportér filproblem". Fremragende filkvalitet (%(count)s) Filkvalitet Lær hvordan du selv kan <a %(a_metadata)s>forbedre metadataen</a> for denne fil. Problembeskrivelse Venligst <a %(a_login)s>log ind</a>. Jeg elskede denne bog! Hjælp fællesskabet ved at rapportere kvaliteten af denne fil! 🙌 Noget gik galt. Genindlæs siden og prøv igen. Rapportér filproblem (%(count)s) Tak for din indsendelse af rapporten. Den vil blive vist på denne side og manuelt gennemgået af Anna (indtil vi har et ordentligt moderationssystem). Efterlad kommentar Indsend rapport Hvad er der galt med denne fil? Lån (%(count)s) Kommentarer (%(count)s) Downloads (%(count)s) Udforsk metadata (%(count)s) Lister (%(count)s) Statistikker (%(count)s) For information om denne specifikke fil, tjek dens <a %(a_href)s>JSON-fil</a>. Dette er en fil administreret af <a %(a_ia)s>IA's Controlled Digital Lending</a>-bibliotek og indekseret af Annas Arkiv til søgning. For information om de forskellige datasets, vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>. Metadata fra tilknyttet post Forbedr metadata på Open Library En "fil MD5" er en hash, der beregnes ud fra filens indhold og er rimelig unik baseret på det indhold. Alle skyggebiblioteker, som vi har indekseret her, bruger primært MD5'er til at identificere filer. En fil kan optræde i flere skyggebiblioteker. For information om de forskellige datasets, vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>. Rapportér filkvalitet Samlede downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tjekkisk metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Bøger %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Advarsel: flere tilknyttede poster: Når du ser på en bog på Anna’s Arkiv, kan du se forskellige felter: titel, forfatter, udgiver, udgave, år, beskrivelse, filnavn og mere. Alle disse informationer kaldes <em>metadata</em>. Da vi kombinerer bøger fra forskellige <em>kildelibraries</em>, viser vi de metadata, der er tilgængelige i den pågældende kildelibrary. For eksempel, for en bog vi har fra Library Genesis, viser vi titlen fra Library Genesis’ database. Nogle gange er en bog til stede i <em>flere</em> kildelibraries, som måske har forskellige metadatafelter. I så fald viser vi simpelthen den længste version af hvert felt, da den forhåbentlig indeholder de mest nyttige informationer! Vi viser stadig de andre felter under beskrivelsen, f.eks. som ”alternativ titel” (men kun hvis de er forskellige). Vi udtrækker også <em>koder</em> såsom identifikatorer og klassifikatorer fra kildelibrary. <em>Identifikatorer</em> repræsenterer unikt en bestemt udgave af en bog; eksempler er ISBN, DOI, Open Library ID, Google Books ID eller Amazon ID. <em>Klassifikatorer</em> grupperer flere lignende bøger; eksempler er Dewey Decimal (DCC), UDC, LCC, RVK eller GOST. Nogle gange er disse koder eksplicit linket i kildelibraries, og nogle gange kan vi udtrække dem fra filnavnet eller beskrivelsen (primært ISBN og DOI). Vi kan bruge identifikatorer til at finde poster i <em>metadata-only samlinger</em>, såsom OpenLibrary, ISBNdb eller WorldCat/OCLC. Der er en specifik <em>metadata-fane</em> i vores søgemaskine, hvis du vil gennemse disse samlinger. Vi bruger matchende poster til at udfylde manglende metadatafelter (f.eks. hvis en titel mangler), eller f.eks. som “alternativ titel” (hvis der er en eksisterende titel). For at se præcis hvor metadata for en bog kom fra, se <em>“Tekniske detaljer” fanen</em> på en bogside. Den har et link til den rå JSON for den bog, med henvisninger til den rå JSON af de originale poster. For mere information, se følgende sider: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Søgning (metadata-fane)</a>, <a %(a_codes)s>Koder Explorer</a>, og <a %(a_example)s>Eksempel metadata JSON</a>. Endelig kan alle vores metadata <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>downloades</a> som ElasticSearch og MariaDB databaser. Baggrund Du kan hjælpe med at bevare bøger ved at forbedre metadata! Først, læs baggrunden om metadata på Anna’s Arkiv, og lær derefter hvordan du forbedrer metadata gennem linking med Open Library, og tjen gratis medlemskab på Anna’s Arkiv. Forbedr metadata Så hvis du støder på en fil med dårlige metadata, hvordan skal du så rette det? Du kan gå til kildelibrary og følge dets procedurer for at rette metadata, men hvad skal du gøre, hvis en fil er til stede i flere kildelibraries? Der er en identifikator, der behandles specielt på Anna’s Arkiv. <strong>Feltet annas_archive md5 på Open Library tilsidesætter altid alle andre metadata!</strong> Lad os først tage et skridt tilbage og lære om Open Library. Open Library blev grundlagt i 2006 af Aaron Swartz med målet om “en webside for hver bog, der nogensinde er udgivet”. Det er en slags Wikipedia for bogmetadata: alle kan redigere det, det er frit licenseret og kan downloades i bulk. Det er en bogdatabase, der er mest på linje med vores mission — faktisk er Anna’s Arkiv inspireret af Aaron Swartz’ vision og liv. I stedet for at genopfinde hjulet besluttede vi at omdirigere vores frivillige mod Open Library. Hvis du ser en bog, der har forkerte metadata, kan du hjælpe på følgende måde: Bemærk, at dette kun virker for bøger, ikke akademiske artikler eller andre typer filer. For andre typer filer anbefaler vi stadig at finde kildebiblioteket. Det kan tage et par uger, før ændringerne bliver inkluderet i Anna’s Archive, da vi skal downloade den nyeste Open Library data dump og genskabe vores søgeindeks.  Gå til <a %(a_openlib)s>Open Library website</a>. Find den korrekte bogpost. <strong>ADVARSEL:</strong> sørg for at vælge den korrekte <strong>udgave</strong>. I Open Library har du “værker” og “udgaver”. Et “værk” kunne være “Harry Potter og De Vises Sten”. En “udgave” kunne være: Første udgave fra 1997 udgivet af Bloomsbery med 256 sider. Paperback-udgaven fra 2003 udgivet af Raincoast Books med 223 sider. Den polske oversættelse fra 2000 “Harry Potter I Kamie Filozoficzn” af Media Rodzina med 328 sider. Alle disse udgaver har forskellige ISBN-numre og forskelligt indhold, så sørg for at vælge den rigtige! Rediger posten (eller opret den, hvis den ikke findes), og tilføj så meget nyttig information som muligt! Du er her nu alligevel, så du kan lige så godt gøre posten virkelig fantastisk. Under “ID-numre” vælg “Anna’s Archive” og tilføj bogens MD5 fra Anna’s Archive. Dette er den lange streng af bogstaver og tal efter “/md5/” i URL'en. Prøv at finde andre filer i Anna’s Archive, der også matcher denne post, og tilføj dem også. I fremtiden kan vi gruppere dem som dubletter på Anna’s Archive søgeside. Når du er færdig, skriv URL'en ned, som du lige har opdateret. Når du har opdateret mindst 30 poster med Anna’s Archive MD5'er, send os en <a %(a_contact)s>email</a> og send os listen. Vi giver dig et gratis medlemskab til Anna’s Archive, så du nemmere kan udføre dette arbejde (og som en tak for din hjælp). Disse skal være redigeringer af høj kvalitet, der tilføjer betydelige mængder information, ellers vil din anmodning blive afvist. Din anmodning vil også blive afvist, hvis nogen af redigeringerne bliver tilbageført eller rettet af Open Library-moderatorer. Open Library linking Hvis du bliver væsentligt involveret i udviklingen og driften af vores arbejde, kan vi diskutere at dele mere af donationsindtægterne med dig, så du kan bruge dem efter behov. Vi betaler kun for hosting, når du har alt sat op, og har demonstreret, at du er i stand til at holde arkivet opdateret med opdateringer. Dette betyder, at du skal betale for de første 1-2 måneder af egen lomme. Din tid vil ikke blive kompenseret (og det vil vores heller ikke), da dette er rent frivilligt arbejde. Vi er villige til at dække hosting- og VPN-udgifter, i starten op til $200 pr. måned. Dette er tilstrækkeligt til en grundlæggende søgeserver og en DMCA-beskyttet proxy. Hostingudgifter Venligst <strong>kontakt os ikke</strong> for at bede om tilladelse eller for grundlæggende spørgsmål. Handlinger taler højere end ord! Alle oplysninger er derude, så gå bare i gang med at opsætte dit spejl. Du er velkommen til at oprette billetter eller merge requests på vores Gitlab, når du støder på problemer. Vi kan have brug for at bygge nogle spejl-specifikke funktioner med dig, såsom rebranding fra “Anna’s Archive” til dit websteds navn, (i starten) deaktivering af brugerkonti eller linke tilbage til vores hovedside fra bogsider. Når du har dit spejl kørende, bedes du kontakte os. Vi vil gerne gennemgå din OpSec, og når den er solid, vil vi linke til dit spejl og begynde at arbejde tættere sammen med dig. Tak på forhånd til alle, der er villige til at bidrage på denne måde! Det er ikke for de sarte sjæle, men det vil styrke levetiden for det største virkelig åbne bibliotek i menneskets historie. Kom godt i gang For at øge robustheden af Annas Arkiv søger vi frivillige til at køre spejle. Din version er tydeligt adskilt som et spejl, f.eks. “Bobs Arkiv, et Anna’s Archive spejl”. Du er villig til at tage de risici, der er forbundet med dette arbejde, som er betydelige. Du har en dyb forståelse af den nødvendige operationelle sikkerhed. Indholdet af <a %(a_shadow)s>disse</a> <a %(a_pirate)s>indlæg</a> er selvindlysende for dig. I starten vil vi ikke give dig adgang til vores partner server downloads, men hvis det går godt, kan vi dele det med dig. Du kører Anna’s Archive open source-kodebase, og du opdaterer regelmæssigt både koden og dataene. Du er villig til at bidrage til vores <a %(a_codebase)s>kodebase</a> — i samarbejde med vores team — for at få dette til at ske. Vi leder efter dette: Spejle: opfordring til frivillige Lav en ny donation. Ingen donationer endnu. <a %(a_donate)s>Lav min første donation.</a> Donationsdetaljer vises ikke offentligt. Mine donationer 📡 For bulk-spejling af vores samling, tjek <a %(a_datasets)s>Datasets</a> og <a %(a_torrents)s>Torrents</a> siderne. Downloads fra din IP-adresse i de sidste 24 timer: %(count)s. 🚀 For hurtigere downloads og for at springe browserkontrollerne over, <a %(a_membership)s>bliv medlem</a>. Download fra partnerwebsite Du er velkommen til at fortsætte med at browse Anna’s Arkiv i en anden fane, mens du venter (hvis din browser understøtter opdatering af baggrundsfaner). Du er velkommen til at vente på, at flere download-sider indlæses samtidig (men download venligst kun én fil ad gangen pr. server). Når du får et downloadlink, er det gyldigt i flere timer. Tak fordi du venter, dette holder hjemmesiden tilgængelig gratis for alle! 😊 🔗 Alle downloadlinks for denne fil: <a %(a_main)s>Filens hovedside</a>. ❌ Langsomme downloads er ikke tilgængelige via Cloudflare VPN'er eller fra Cloudflare IP-adresser. ❌ Langsomme downloads er kun tilgængelige via den officielle hjemmeside. Besøg %(websites)s. 📚 Brug følgende URL til at downloade: <a %(a_download)s>Download nu</a>. For at give alle mulighed for at downloade filer gratis, skal du vente, før du kan downloade denne fil. Vent venligst <span %(span_countdown)s>%(wait_seconds)s</span> sekunder for at downloade denne fil. Advarsel: der har været mange downloads fra din IP-adresse inden for de sidste 24 timer. Downloads kan være langsommere end normalt. Hvis du bruger en VPN, delt internetforbindelse eller din internetudbyder deler IP-adresser, kan denne advarsel skyldes det. Gem ❌ Noget gik galt. Prøv venligst igen. ✅ Gemt. Genindlæs venligst siden. Skift dit visningsnavn. Din identifikator (delen efter “#”) kan ikke ændres. Profil oprettet <span %(span_time)s>%(time)s</span> rediger Lister Opret en ny liste ved at finde en fil og åbne fanen “Lister”. Ingen lister endnu Profil ikke fundet. Profil På nuværende tidspunkt kan vi ikke imødekomme boganmodninger. Send os ikke dine boganmodninger. Lav venligst dine anmodninger på Z-Library eller Libgen-foraene. Post i Annas Arkiv DOI: %(doi)s Download SciDB Nexus/STC Ingen forhåndsvisning tilgængelig endnu. Download filen fra <a %(a_path)s>Annas Arkiv</a>. For at støtte tilgængeligheden og den langsigtede bevaring af menneskelig viden, bliv <a %(a_donate)s>medlem</a>. Som en bonus, 🧬&nbsp;SciDB indlæses hurtigere for medlemmer, uden nogen begrænsninger. Virker det ikke? Prøv at <a %(a_refresh)s>opdatere</a>. Sci-Hub Tilføj specifikt søgefelt Søg beskrivelser og metadata kommentarer Udgivelsesår Avanceret Adgang Indhold Visning Liste Tabel Filtype Sprog Sorter efter Største Mest relevant Nyeste (filstørrelse) (open sourced) (udgivelsesår) Ældste Tilfældig Mindste Kilde scrapet og open-sourcet af AA Digital udlån (%(count)s) Tidsskriftsartikler (%(count)s) Vi har fundet match i: %(in)s. Du kan henvise til URL'en fundet der, når du <a %(a_request)s>anmoder om en fil</a>. Metadata (%(count)s) For at udforske søgeindekset efter koder, brug <a %(a_href)s>Codes Explorer</a>. Søgeindekset opdateres månedligt. Det inkluderer i øjeblikket poster op til %(last_data_refresh_date)s. For mere teknisk information, se <a %(link_open_tag)s>datasets-siden</a>. Ekskluder Inkluder kun Ikke markeret mere… Næste … Forrige Dette søgeindeks inkluderer i øjeblikket metadata fra Internet Archive’s Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Mere om vores datasets</a>. For flere digitale udlånsbiblioteker, se <a %(a_wikipedia)s>Wikipedia</a> og <a %(a_mobileread)s>MobileRead Wiki</a>. For DMCA / copyright-krav <a %(a_copyright)s>klik her</a>. Downloadtid Fejl under søgning. Prøv <a %(a_reload)s>at genindlæse siden</a>. Hvis problemet fortsætter, bedes du sende en e-mail til os på %(email)s. Hurtig download Faktisk kan alle hjælpe med at bevare disse filer ved at seede vores <a %(a_torrents)s>samlede liste over torrents</a>. ➡️ Nogle gange sker dette forkert, når søgeserveren er langsom. I sådanne tilfælde kan <a %(a_attrs)s>genindlæsning</a> hjælpe. ❌ Denne fil kan have problemer. Leder du efter artikler? Denne søgeindeks inkluderer i øjeblikket metadata fra forskellige metadatakilder. <a %(a_datasets)s>Mere om vores datasets</a>. Der er mange, mange kilder til metadata for skriftlige værker rundt om i verden. <a %(a_wikipedia)s>Denne Wikipedia-side</a> er et godt udgangspunkt, men hvis du kender andre gode lister, så lad os det vide. For metadata viser vi de originale poster. Vi foretager ingen sammensmeltning af poster. Vi har i øjeblikket verdens mest omfattende åbne katalog af bøger, artikler og andre skriftlige værker. Vi spejler Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>og mere</a>. <span class="font-bold">Ingen filer fundet.</span> Prøv færre eller andre søgeord og filtre. Resultater %(from)s-%(to)s (%(total)s i alt) Hvis du finder andre “skyggebiblioteker”, som vi bør spejle, eller hvis du har spørgsmål, bedes du kontakte os på %(email)s. %(num)d delvise match %(num)d+ delvise match Skriv i boksen for at søge efter filer i digitale udlånsbiblioteker. Skriv i boksen for at søge i vores katalog af %(count)s direkte downloadbare filer, som vi <a %(a_preserve)s>bevarer for evigt</a>. Skriv i boksen for at søge. Skriv i boksen for at søge i vores katalog af %(count)s akademiske artikler og tidsskriftsartikler, som vi <a %(a_preserve)s>bevarer for evigt</a>. Skriv i boksen for at søge efter metadata fra biblioteker. Dette kan være nyttigt, når du <a %(a_request)s>anmoder om en fil</a>. Tip: brug tastaturgenveje “/” (søgefokus), “enter” (søg), “j” (op), “k” (ned), “<” (forrige side), “>” (næste side) for hurtigere navigation. Dette er metadata-poster, <span %(classname)s>ikke</span> downloadbare filer. Søgeindstillinger Søg Digital Udlån Download Tidsskriftsartikler Metadata Ny søgning %(search_input)s - Søg Søgningen tog for lang tid, hvilket betyder, at du måske ser unøjagtige resultater. Nogle gange hjælper det at <a %(a_reload)s>genindlæse</a> siden. Søgningen tog for lang tid, hvilket er almindeligt for brede forespørgsler. Filtertællingerne kan være unøjagtige. For store uploads (over 10.000 filer), der ikke accepteres af Libgen eller Z-Library, kontakt os venligst på %(a_email)s. For Libgen.li, sørg for først at logge ind på <a %(a_forum)s>deres forum</a> med brugernavn %(username)s og adgangskode %(password)s, og vend derefter tilbage til deres <a %(a_upload_page)s>uploadside</a>. Indtil videre foreslår vi at uploade nye bøger til Library Genesis forks. Her er en <a %(a_guide)s>praktisk guide</a>. Bemærk, at begge forks, som vi indekserer på denne hjemmeside, trækker fra dette samme upload-system. For små uploads (op til 10.000 filer) bedes du uploade dem til både %(first)s og %(second)s. Alternativt kan du uploade dem til Z-Library <a %(a_upload)s>her</a>. For at uploade akademiske artikler, bedes du også (ud over Library Genesis) uploade til <a %(a_stc_nexus)s>STC Nexus</a>. De er det bedste skyggebibliotek for nye artikler. Vi har ikke integreret dem endnu, men det vil vi på et tidspunkt. Du kan bruge deres <a %(a_telegram)s>upload-bot på Telegram</a> eller kontakte adressen, der er angivet i deres fastgjorte besked, hvis du har for mange filer til at uploade på denne måde. <span %(label)s>Tungt frivilligt arbejde (USD$50-USD$5,000 belønninger):</span> hvis du er i stand til at dedikere meget tid og/eller ressourcer til vores mission, vil vi meget gerne arbejde tættere sammen med dig. Til sidst kan du blive en del af det indre team. Selvom vi har et stramt budget, er vi i stand til at tildele <span %(bold)s>💰 monetære belønninger</span> for det mest intense arbejde. <span %(label)s>Let frivilligt arbejde:</span> hvis du kun kan afsætte et par timer her og der, er der stadig masser af måder, du kan hjælpe på. Vi belønner konsekvente frivillige med <span %(bold)s>🤝 medlemskaber til Anna’s Archive</span>. Anna’s Archive er afhængig af frivillige som dig. Vi byder alle engagementsniveauer velkommen og har to hovedkategorier af hjælp, vi leder efter: Hvis du ikke kan frivilligt give din tid, kan du stadig hjælpe os meget ved at <a %(a_donate)s>donere penge</a>, <a %(a_torrents)s>seede vores torrents</a>, <a %(a_uploading)s>uploade bøger</a>, eller <a %(a_help)s>fortælle dine venner om Anna’s Arkiv</a>. <span %(bold)s>Virksomheder:</span> vi tilbyder højhastigheds direkte adgang til vores samlinger i bytte for donationer på virksomhedsniveau eller udveksling af nye samlinger (f.eks. nye scanninger, OCR’ede datasets, berigelse af vores data). <a %(a_contact)s>Kontakt os</a> hvis dette er dig. Se også vores <a %(a_llm)s>LLM-side</a>. Dusører Vi leder altid efter folk med solide programmerings- eller offensive sikkerhedsfærdigheder til at involvere sig. Du kan gøre en seriøs forskel i bevarelsen af menneskehedens arv. Som tak giver vi medlemskab for solide bidrag. Som en stor tak giver vi monetære dusører for særligt vigtige og vanskelige opgaver. Dette bør ikke ses som en erstatning for et job, men det er et ekstra incitament og kan hjælpe med påløbne omkostninger. Det meste af vores kode er open source, og vi vil bede om det samme af din kode, når vi tildeler dusøren. Der er nogle undtagelser, som vi kan diskutere individuelt. Dusører tildeles den første person, der fuldfører en opgave. Du er velkommen til at kommentere på en dusørbillet for at lade andre vide, at du arbejder på noget, så andre kan holde sig tilbage eller kontakte dig for at samarbejde. Men vær opmærksom på, at andre stadig er frie til at arbejde på det og forsøge at slå dig. Vi tildeler dog ikke dusører for sjusket arbejde. Hvis to højtkvalitetsindsendelser laves tæt på hinanden (inden for en dag eller to), kan vi vælge at tildele dusører til begge, efter vores skøn, for eksempel 100%% for den første indsendelse og 50%% for den anden indsendelse (så 150%% i alt). For de større dusører (især scraping-dusører), bedes du kontakte os, når du har fuldført ~5%% af det, og du er sikker på, at din metode vil skalere til den fulde milepæl. Du skal dele din metode med os, så vi kan give feedback. På denne måde kan vi også beslutte, hvad vi skal gøre, hvis der er flere personer, der nærmer sig en dusør, såsom potentielt at tildele den til flere personer, opmuntre folk til at samarbejde osv. ADVARSEL: de højtbelønnede opgaver er <span %(bold)s>svære</span> — det kan være klogt at starte med de lettere. Gå til vores <a %(a_gitlab)s>Gitlab-issues-liste</a> og sorter efter "Label priority". Dette viser nogenlunde rækkefølgen af opgaver, vi går op i. Opgaver uden eksplicitte dusører er stadig berettigede til medlemskab, især dem markeret "Accepted" og "Anna’s favorite". Du vil måske starte med et "Starter project". Let frivilligt arbejde Vi har nu også en synkroniseret Matrix-kanal på %(matrix)s. Hvis du har et par timer til overs, kan du hjælpe på flere måder. Sørg for at deltage i <a %(a_telegram)s>frivilligchatten på Telegram</a>. Som et tegn på vores påskønnelse giver vi typisk 6 måneder af “Heldige Bibliotekar” for grundlæggende milepæle, og mere for fortsat frivilligt arbejde. Alle milepæle kræver arbejde af høj kvalitet — sjusket arbejde skader os mere, end det hjælper, og vi vil afvise det. Venligst <a %(a_contact)s>send os en e-mail</a>, når du når en milepæl. %(links)s links eller skærmbilleder af anmodninger, du har opfyldt. Opfylde bog- (eller papir osv.) anmodninger på Z-Library eller Library Genesis fora. Vi har ikke vores eget boganmodningssystem, men vi spejler disse biblioteker, så at gøre dem bedre gør også Anna’s Arkiv bedre. Milepæl Opgave Afhænger af opgaven. Små opgaver, der er postet i vores <a %(a_telegram)s>frivilligchat på Telegram</a>. Normalt for medlemskab, nogle gange for små belønninger. Små opgaver lagt op i vores frivillige chatgruppe. Sørg for at efterlade en kommentar på de problemer, du løser, så andre ikke duplikerer dit arbejde. %(links)s links til optegnelser, du har forbedret. Du kan bruge <a %(a_list)s>listen over tilfældige metadata-problemer</a> som udgangspunkt. Forbedre metadata ved <a %(a_metadata)s>at linke</a> med Open Library. Disse skal vise, at du fortæller nogen om Annas Arkiv, og at de takker dig. %(links)s links eller skærmbilleder. Sprede budskabet om Annas Arkiv. For eksempel ved at anbefale bøger på AA, linke til vores blogindlæg eller generelt henvise folk til vores hjemmeside. Fuldstændig oversættelse af et sprog (hvis det ikke allerede var tæt på færdiggørelse). <a %(a_translate)s>Oversætte</a> hjemmesiden. Link til redigeringshistorik, der viser, at du har lavet betydelige bidrag. Forbedre Wikipedia-siden for Anna’s Arkiv på dit sprog. Inkluder information fra AA’s Wikipedia-side på andre sprog og fra vores hjemmeside og blog. Tilføj referencer til AA på andre relevante sider. Frivilligt arbejde & Præmier 