#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Solicitú inválida. Visita %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Biblioteca de Préstamu del Internet Archive"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " y "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "y más"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Reflexamos %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Raspamos y abrimos el códigu de %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Tol nuesu códigu y datos son completamente de códigu abiertu."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;La biblioteca verdaderamente abierta más grande de la historia humana."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;llibros, %(paper_count)s&nbsp;artículos — preservaos pa siempre."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La biblioteca de datos abiertos y códigu abiertu más grande del mundu. ⭐️&nbsp;Espeyos de Sci-Hub, Library Genesis, Z-Library y más. 📈&nbsp;%(book_any)s llibros, %(journal_article)s artículos, %(book_comic)s cómics, %(magazine)s revistes — preservaos pa siempre."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 La biblioteca de códigu abiertu y datos abiertos más grande del mundu.<br>⭐️ Espeyos de Scihub, Libgen, Zlib y más."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadatos incorreutos (p. ex. títulu, descripción, imaxe de la cubierta)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemes de descarga (p. ex. nun se pue conectar, mensaxe d'error, mui lentu)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Nun se pue abrir l'archivu (p. ex. archivu dañáu, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Mala calidá (p. ex. problemes de formateu, mala calidá d'escaneáu, páxines que falten)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / el ficheru debería ser desaniciáu (p. ex. publicidá, conteníu abusivu)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamación de derechos d'autor"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Otru"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Descargues extra"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Lector Lluminós"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Bibliotecariu Afortunáu"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Acumulador de Datos Deslumbrante"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Archivista Asombrosu"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "ensin pagar"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "pagáu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "canceláu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "caducáu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "esperando a que Anna confirme"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "inválidu"

#, fuzzy
msgid "page.donate.title"
msgstr "Donar"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Tienes una <a %(a_donation)s>donación existente</a> en procesu. Por favor, termina o cancela esa donación enantes de facer una nueva donación."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ver toles mios donaciones</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "L'Archivu d'Anna ye un proyeutu ensin ánimu de lucre, de códigu abiertu y datos abiertos. Al donar y facete miembru, sofites les nuestres operaciones y desendolcu. A tolos nuesos miembros: ¡gracies por ayudanos a siguir! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Pa más información, echa un vistazo a la <a %(a_donate)s>FAQ de Donaciones</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "¡Pa consiguir más descargues, <a %(a_refer)s>recomienda a tos amigos</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Ganes %(percentage)s%% descarges rápides de bonu, porque te recomendó l'usuariu %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Esto aplícase a tol periodu de membresía."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s descargues rápides al día"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "¡si donas esti mes!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mes"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Xunise"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Seleicionáu"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "hasta %(percentage)s%% descuentos"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Artículos de SciDB <strong>ilimitados</strong> ensin verificación"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Accesu API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Gana <strong>%(percentage)s%% descarges bonus</strong> al <a %(a_refer)s>recomendar a amigos</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "El to nome d'usuariu o mención anónima nos créditos"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Ventayes anteriores, más:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Accesu anticipáu a nueves carauterístiques"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram esclusivu con actualizaciones detrás de les escenes"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adopta un torrent”: el to nome d'usuariu o mensaxe nun nome d'archivu torrent <div %(div_months)s>una vegada cada 12 meses de membresía</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Estatu llexendariu na preservación del conocimientu y la cultura de la humanidá"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Accesu d'Expertos"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contáctanos"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Somos un equipu pequeñu de voluntarios. Pue tardar 1-2 selmanes en responder."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Accesu ilimidáu</strong> a alta velocidá"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Servidores direutos <strong>SFTP</strong>"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donación a nivel empresarial o intercambio por colecciones nuevas (p. ej., nuevos escaneos, datasets OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Acoyemos grandes donaciones de persones adinerades o instituciones. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pa donaciones de más de $5000 por favor contáctanos direutamente en %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Ten en cuenta que, anque les membresíes nesta páxina son “per mes”, son donativos d'una sola vegada (non recurrentes). Consulta la <a %(faq)s>FAQ de Donativos</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Si quies facer una donación (cualquier cantidá) ensin membresía, pues usar esti direición de Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Por favor, escueya un métodu de pagu."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporalmente non disponible)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s tarxeta de regalu"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Tarxeta bancaria (usando app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Cripto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Tarxeta de créditu/débito"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (EE.XX.) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regular)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Tarxeta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Créditu/débito/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Tarxeta bancaria"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Tarxeta de créditu/débito (respaldu)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Tarxeta de créditu/débito 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Con criptomoneda pues donar usando BTC, ETH, XMR y SOL. Usa esta opción si yá conoces la criptomoneda."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Con criptomonedes pues donar usando BTC, ETH, XMR, y más."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si tas usando criptomonedes per primer vegada, suxerimos usar %(options)s pa mercar y donar Bitcoin (la criptomoneda orixinal y más usada)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Pa donar usando PayPal US, vamos usar PayPal Crypto, que nos permite quedar anónimos. Apreciamos que tomes el tiempu pa deprender cómo donar usando esti métodu, yá que nos ayuda muncho."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donar usando PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donar usando Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "¡Si tienes Cash App, esta ye la forma más fácil de donar!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Ten en cuenta que pa tresacciones menores de %(amount)s, Cash App pue cobrar una tarifa de %(fee)s. Pa %(amount)s o más, ye de baldre!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Dona usando Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "¡Si tienes Revolut, esta ye la forma más fácil de donar!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donar con tarxeta de créditu o débito."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay y Apple Pay tamién podríen funcionar."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Ten en cuenta que pa donaciones pequeñes les comisiones de tarxeta de créditu puen eliminar el nuesu descuentu %(discount)s%%, asina que recomendamos suscripciones más llargues."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Ten en cuenta que pa donaciones pequeñes les comisiones son altes, asina que recomendamos suscripciones más llargues."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, compras Bitcoin con tarxeta de créditu/débito o cuenta bancaria, y llueu donas esi Bitcoin a nós. D'esta manera podemos siguir seguros y anónimos al aceptar la to donación."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance ta disponible en casi tolos países, y acepta la mayoría de bancos y tarxetes de créditu/débito. Esta ye la nuesa recomendación principal. Agradecémoste que tomes el tiempu pa deprender cómo donar usando esti métodu, yá que nos ayuda muncho."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Dona usando la to cuenta regular de PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donar usando tarxeta de créditu/débito, PayPal o Venmo. Pues escoyer ente estos na páxina siguiente."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donar usando una tarxeta regalu d'Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Ten en cuenta que necesitamos redondear a cantidaes aceutaes polos nuestros revendedores (mínimu %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Namái sofitamos Amazon.com, non otros sitios web d'Amazon. Por exemplu, .de, .co.uk, .ca, nun tán sofitaos."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Esta opción ye pa %(amazon)s. Si quies usar otru sitiu web d'Amazon, seleicionalo enriba."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Esti métodu usa un proveedor de criptomonedes como conversor intermediariu. Esto pue ser un poco confusu, asina que por favor usa esti métodu namái si otros métodos de pagu nun funcionen. Tamién nun funciona en tolos países."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona usando una tarxeta de créditu/débito, al traviés de la app Alipay (super fácil de configurar)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instala la app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instala la app Alipay dende la <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Rexístrate usando el to númberu de teléfonu."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nun se requieren más detalles personales."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Amesta tarxeta bancaria"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Sopórtense: Visa, MasterCard, JCB, Diners Club y Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Consulta <a %(a_alipay)s>esta guía</a> pa más información."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nun podemos sofitar tarxetes de créditu/débito direutamente, porque los bancos nun quieren trabayar con nós. ☹ Sicasí, hai delles maneres d'usar tarxetes de créditu/débito igual, usando otros métodos de pagu:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Tarxeta Regalu d'Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Manda nos tarxetes de regalu d'Amazon.com usando la to tarxeta de créditu/débito."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay sofita tarxetes de créditu/débito internacionales. Consulta <a %(a_alipay)s>esta guía</a> pa más información."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) sofita tarxetes de créitu/débito internacionales. Na aplicación de WeChat, dir a “Yo => Servicios => Billetera => Amestar una Tarxeta”. Si nun ves eso, habilítalo usando “Yo => Configuración => Xeneral => Ferramientes => Weixin Pay => Habilitar”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Pue mercar criptomonedes usando tarxetes de créditu/débito."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servicios exprés de criptomonedes"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Los servicios exprés son convenientes, pero carguen tarifes más altes."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Pue usase esto en llugar d'un cambéu de criptomonedes si busques facer un donativu mayor rápidamente y nun t'importa una tarifa de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Asegúrate d'unviar la cantidá exacta de criptomonedes que se muestra na páxina de donativos, non la cantidá en $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "En casu contrario, la tarifa va ser restada y nun podemos procesar automáticamente la to membresía."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(mínimo: %(minimum)s dependiendo del país, sin verificación para la primera transacción)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(mínimo: %(minimum)s, sin verificación para la primera transacción)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si dalguna d'esta información ta desactualizada, por favor unvíanos un corréu electrónicu pa avisanos."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Pa tarxetes de créitu, tarxetes de débito, Apple Pay y Google Pay, usamos “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Nel so sistema, un “café” ye igual a $5, asina que la to donación redondiaráse al múltiplu más cercanu de 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Seleiciona cuánto tiempu quies suscribite."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mes"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 meses"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 meses"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 meses"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 meses"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 meses"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 meses"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>dempués de <span %(span_discount)s></span> descuentos</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Esti métodu de pagu requiere un mínimu de %(amount)s. Por favor, selecciona una duración o métodu de pagu diferente."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donar"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Esti métodu de pagu namái permite un máximu de %(amount)s. Por favor, escueye una duración o métodu de pagu diferente."

#, fuzzy
msgid "page.donate.login2"
msgstr "Pa facese miembru, por favor <a %(a_login)s>Anicia o Rexístrate</a>. ¡Gracies pol to sofitu!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Escueyi la to criptomoneda preferida:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(cantidad mínima más baxa)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(usar al unviar Ethereum dende Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(avisu: cantidá mínima alta)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Calca'l botón de donar pa confirmar esta donación."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Dona <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Entá pues cancelar la donación mientres el procesu de compra."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirixendo a la páxina de donativos…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Dalgo foi mal. Por favor, recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mes"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "por 1 mes"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "por 3 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "por 6 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "por 12 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "por 24 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "por 48 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "por 96 meses"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "por 1 mes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "por 3 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "por 6 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "por 12 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "pa 24 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "pa 48 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "por 96 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donativos"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Fecha: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses, incluyendo %(discounts)s%% descuentu)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Estáu: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identificador: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Encaboxar"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "¿Tas seguru de que quies cancelar? Nun canceles si yá pagasti."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Sí, por favor cancela"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ La to donación foi cancelada."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Facer un nuevu donativu"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Dalgo foi mal. Por favor recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reordenar"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Yá pagasti. Si quies revisar les instrucciones de pagu de toes formes, calca equí:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Amosar instrucciones de pagu antigües"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "¡Gracies pol to donativu!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Si entá nun lo fixisti, apunta la to clave secreta pa aniciar sesión:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "¡Si non, podríes quedate ensin accesu a esta cuenta!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Les instrucciones de pagu tán desactualizaes. Si quies facer otra donación, usa'l botón “Reordenar” enriba."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> Los precios de les criptomonedes pueden fluctuar de manera salvaxe, a vegaes hasta un 20%% en pocos minutos. Esto ye menos que les comisiones que tenemos con munchos proveedores de pagu, que a menudo cobren un 50-60%% por trabayar con una “caridá en l'ombra” como la nuestra. <u>Si nos unvies el recibu col preciu orixinal que pagasti, igual acreditaremos la to cuenta pola membresía escoyida</u> (siempre que'l recibu nun tenga más de unes poques hores). ¡Apreciamos muncho que teyes dispuestu a aguantar coses como esta pa ayudanos! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Esti donativu caducó. Por favor, cancela y crea ún nuevu."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Instrucciones de criptomonedes"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Traspasar a una de les nuestres cuentes de criptomonedes"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Dona la cantidá total de %(total)s a una d'estes direiciones:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Mercar Bitcoin en Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Atopa la páxina de “Crypto” na to app o sitiu web de PayPal. Esto suele tar baxo “Finances”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Sigue les instrucciones pa mercar Bitcoin (BTC). Namái necesites mercar la cantidá que quies donar, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Traspasa los Bitcoin a la nuesa direición"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Dir a la páxina de “Bitcoin” na to app o sitiu web de PayPal. Prieta'l botón “Transferir” %(transfer_icon)s, y depués “Unviar”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Introduce la nuesa direición de Bitcoin (BTC) como destinatariu, y sigue les instrucciones pa unviar la to donación de %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrucciones de tarxeta de créditu / débito"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona a través de la nuesa páxina de tarxeta de créitu / débito"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donar %(amount)s en <a %(a_page)s>esta páxina</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Consulta la guía pasu a pasu embaxo."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Estáu:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Esperando confirmación (refresca la páxina pa comprobar)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Esperando la transferencia (refresca la páxina pa comprobar)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tiempu restante:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(podrías querer cancelar y crear una donación nueva)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Pa restablecer el temporizador, simplemente crea una nueva donación."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Anovar estáu"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Si tienes dalgún problema, por favor contáctanos en %(email)s y inclúi la mayor cantidá d'información posible (como captures de pantalla)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Si yá pagasti:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "A veces la confirmación pue tardar hasta 24 hores, asina que asegúrate de refrescar esta páxina (mesmo si caducó)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Mercar moneda PYUSD en PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Sigue les instrucciones pa mercar la moneda PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Compra un poco más (recomendamos %(more)s más) de la cantidá que tas donando (%(amount)s), pa cubrir les comisiones de tresacción. Quedarás con lo que sobre."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Dir a la páxina de “PYUSD” na to app o sitiu web de PayPal. Prieta'l botón de “Tresferir” %(icon)s, y depués “Unviar”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Tresferir %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Merca Bitcoin (BTC) en Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Diríxite a la páxina de “Bitcoin” (BTC) en Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compre un poco más (recomendamos %(more)s más) de la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Se quedará con lo que sobre."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfiera el Bitcoin a nuestra dirección"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de dólares a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Para donaciones pequeñas (menos de $25), puede que necesite usar Rush o Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Compre Bitcoin (BTC) en Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Vaya a la página “Crypto” en Revolut para comprar Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Compre un poco más (recomendamos %(more)s más) de la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Se quedará con lo que sobre."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfiera el Bitcoin a nuestra dirección"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de euros a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Asegúrate d'usar la cantidá de BTC embaxo, <em>NON</em> euros o dólares, si non nun recibiremos la cantidá correuta y nun podremos confirmar automáticamente la to membresía."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Para donaciones pequeñas (menos de $25) puede que necesite usar Rush o Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Use cualquiera de los siguientes servicios exprés de “tarjeta de crédito a Bitcoin”, que solo toman unos minutos:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Enllena los siguientes detalles nel formulariu:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Cantidá de BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Por favor, usa esta <span %(underline)s>cantidá exacta</span>. El to costu total pue ser mayor por cuenta de les comisiones de la tarxeta de créditu. Pa cantidaes pequeñes esto pue ser más que'l nuesu descuentu, desafortunadamente."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Direición de BTC / Bitcoin (billetera esterna):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "Instrucciones de %(coin_name)s"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Namás sofitemos la versión estándar de les monedes criptográfiques, nenguna rede o versión esótica de monedes. Pue tardar hasta una hora en confirmase la transacción, dependiendo de la moneda."

msgid "page.donation.crypto_qr_code_title"
msgstr "Escanear el código QR para pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Escanee este código QR con su aplicación Crypto Wallet para completar rápidamente los detalles de pago"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Tarxeta regalu d'Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Por favor, usa'l <a %(a_form)s>formulariu oficial de Amazon.com</a> pa unviar una tarxeta de regalu de %(amount)s a la direición de corréu embaxo."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Nun podemos aceptar otros métodos de tarxetes de regalu, <strong>solo mandaes direutamente dende'l formulariu oficial en Amazon.com</strong>. Nun podemos devolver la to tarxeta de regalu si nun usas esti formulariu."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Introduce la cantidá exacta: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Por favor, nun escribas el to propiu mensaxe."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Email del destinatariu “Pa” na forma:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Únicu pa la to cuenta, nun lo compartas."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usar namái una vegada."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Esperando pola tarxeta de regalu… (refresca la páxina pa comprobar)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Dempués d'unviar la to tarxeta de regalu, el nuesu sistema automatizáu confirmarálo en pocos minutos. Si nun funciona, intenta unviala otra vuelta (<a %(a_instr)s>instrucciones</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Si eso entá nun funciona, por favor unvíanos un corréu y Anna revisarálo manualmente (esto pue tardar dellos díes), y asegúrate de mencionar si yá intentasti reenvialo."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Exemplu:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Ten en cuenta que'l nome de la cuenta o la imaxe pue vese estraña. ¡Nun te preocupes! Estes cuentes tán xestionaes polos nuesos socios de donativos. Les nueses cuentes nun fueron hackeaes."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Instrucciones d'Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donar en Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Dona la cantidá total de %(total)s usando <a %(a_account)s>esti cuenta d'Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Si la páxina de donativos ta bloquiada, prueba con una conexión d'internet diferente (p. ex. VPN o internet del móvil)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Desgraciadamente, la páxina d'Alipay suel ser accesible namái dende <strong>China continental</strong>. Podríes necesitar desactivar temporalmente'l to VPN, o usar un VPN pa China continental (o Hong Kong tamién funciona a vegaes)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Fai'l donativu (escanear códigu QR o presionar botón)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Abre la <a %(a_href)s>páxina de donativos con códigu QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Escanea'l códigu QR cola app Alipay, o presiona'l botón pa abrir la app Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Por favor, ten pacencia; la páxina pue tardar un poco en cargase porque ta en China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instrucciones de WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Dona en WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Dona la cantidá total de %(total)s usando <a %(a_account)s>esti cuenta de WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Instrucciones de Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donar en Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Dona la cantidá total de %(total)s usando <a %(a_account)s>esti cuenta Pix"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Unvíanos el recibu por corréu electrónicu"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Unvia un recibu o captura de pantalla a la to direición personal de verificación. Nun uses esta direición de corréu electrónicu pa la to donación por PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Unvia un recibu o captura de pantalla a la to direición personal de verificación:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Si'l tipu de cambéu de la criptomoneda fluctuó mientres la transacción, asegúrate d'incluyir el recibu que muestre'l tipu de cambéu orixinal. ¡Apreciamos muncho que te tomes la molestia d'usar criptomoneda, ayúdanos un montonazu!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Cuando hayas unviáu'l recibu por corréu, calca esti botón, pa que Anna pueda revisalu manualmente (esto pue tardar dellos díes):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Sí, mandé un corréu col recibu"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ ¡Gracies pola to donación! Anna activará manualmente la to membresía en pocos díes."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Dalgo salió mal. Por favor, recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Guía pasu a pasu"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Dalgunes de les instrucciones mencionen billeteres de criptomonedes, pero nun te preocupes, nun tienes que deprender nada sobre criptomonedes pa esto."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Introduz el to corréu electrónicu."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Selecciona el métodu de pagu."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selecciona otra vez el métodu de pagu."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Seleiciona la cartera “Autoxestionada”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Fai clic en “Confirmo la propiedá”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Deberíes recibir un recibu per corréu electrónicu. Por favor, unvínanoslo y confirmaremos el to donativu lo más rápido posible."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Por favor, espera al menos <span %(span_hours)s>24 hores</span> (y refresca esta páxina) enantes de contactanos."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Si cometisti un error nel pagu, nun podemos facer reembolsos, pero intentaremos soluicionalo."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Los mios donativos"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Los detalles de les donaciones nun se muestren públicamente."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Entá nun hai donativos. <a %(a_donate)s>Faer el mio primer donativu.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fai otru donativu."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Ficheros descargados"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Les descargues dende servidores rápidos tán marcaes por %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Si descargasti un ficheru con descarges rápides y lentes, apaecerá dos veces."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Les descargues rápides nes caberes 24 hores cuenten pal llímite diariu."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Tolos tiempos tán en UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Los ficheros descargados nun se muestran públicamente."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Entá nun se descargaron ficheros."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Caberos 18 hores"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Enantes"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Cuenta"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Aniciar sesión / Rexistrase"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID de cuenta: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Perfil públicu: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clave secreta (¡nun la compartas!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "amosar"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Membresía: <strong>%(tier_name)s</strong> hasta %(until_date)s <a %(a_extend)s>(estender)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Membresía: <strong>Nenguna</strong> <a %(a_become)s>(faite miembru)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Descargues rápides usaes (últimes 24 hores): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "¿Quáles descargues?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupu esclusivu de Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "¡Únete equí!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Actualiza a un <a %(a_tier)s>nivel superior</a> pa unirte al nuesu grupu."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contauta con Anna en %(email)s si tas interesáu en xubir la to membresía a un nivel superior."

#, fuzzy
msgid "page.contact.title"
msgstr "Corréu electrónicu de contautu"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Pue combinase múltiples membresíes (les descargues rápides por 24 hores sumaránse)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Perfil públicu"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Ficheros descargados"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Les mios donaciones"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Zarrar sesión"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Agora tas desconectáu. Recarga la páxina pa coneutate otra vuelta."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Dalgo foi mal. Recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "¡Rexistru con ésitu! La to clave secreta ye: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Guarda esta clave con cuidu. Si la pierdes, vas perder l'accesu a la to cuenta."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Marcapáxines.</strong> Pues marcar esta páxina pa recuperar la to clave.</li><li %(li_item)s><strong>Descargar.</strong> Calca <a %(a_download)s>esti enllaz</a> pa descargar la to clave.</li><li %(li_item)s><strong>Xestor de contraseñes.</strong> Usa un xestor de contraseñes pa guardar la clave cuando la introduz debajo.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Inxerta la to clave secreta pa aniciar sesión:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clave secreta"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Aniciar sesión"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clave secreta inválida. Verifica la to clave y inténtalo otra vuelta, o rexístrate con una cuenta nueva embaxo."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "¡Nun pierdas la to clave!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "¿Nun tienes cuenta entá?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Rexistrar cuenta nueva"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Si perdiste la clave, por favor <a %(a_contact)s>contacta con nós</a> y proporciona tanta información como puedas."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Quiciabes tengas que crear una cuenta temporal pa contactar con nós."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "¿Cuenta antigua basada en corréu electrónicu? Introduz el to <a %(a_open)s>corréu equí</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Llista"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "editar"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Guardar"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Guardáu. Por favor, recarga la páxina."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Dalgo foi mal. Por favor, inténtalo otra vuelta."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Llistar por %(by)s, creadu <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "La llista ta vacía."

#, fuzzy
msgid "page.list.new_item"
msgstr "Amestar o desaniciar d'esta llista atopando un ficheru y abriendo la llingüeta “Llistes”."

#, fuzzy
msgid "page.profile.title"
msgstr "Perfil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Perfil non alcontráu."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "editar"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Cambia'l to nome de pantalla. El to identificador (la parte dempués de “#”) nun se pue cambiar."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Guardar"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Guardáu. Por favor recarga la páxina."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo salió mal. Por favor, inténtalo de nuevo."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Perfil creadu <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Llistes"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Entá nun hai llistes"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Crea una llista nueva atopando un ficheru y abriendo la pestaña “Llistes”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "La reforma de los derechos d'autor ye necesaria pa la seguridá nacional."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Resumíu: Los LLM chinos (incluyendo DeepSeek) tán entrenaos nel mio archivu ilegal de llibros y artículos — el más grande del mundu. Occidente necesita reformar la llei de derechos d'autor como cuestión de seguridá nacional."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundu</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Nun hai muncho tiempu, les “biblioteques sombra” tán desaniciándose. Sci-Hub, el gran archivu ilegal d'artículos académicos, dexó de recibir obres nueves, debíu a demandes. “Z-Library”, la biblioteca ilegal más grande de llibros, vio a los sos presuntos creadores arrestaos por cargos criminales de derechos d'autor. Increíblemente, lograron escapar del so arrestu, pero la so biblioteca nun ta menos amenazada."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Cuando Z-Library enfrentó el cierre, yá tenía una copia de seguridá de tola so biblioteca y taba buscando una plataforma pa aloxala. Esa foi la mio motivación pa entamar l'Archivu d'Anna: una continuación de la misión tres d'esos primeros iniciatives. Dende entós, crecimos hasta ser la biblioteca sombra más grande del mundu, aloxando más de 140 millones de testos con derechos d'autor en diversos formatos — llibros, artículos académicos, revistes, periódicos y más."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "El mio equipu y yo somos ideólogos. Creemos que preservar y aloxar estos archivos ye moralmente correutu. Les biblioteques de tol mundu tán viendo recortes de financiación, y tampoco podemos confiar el patrimoniu de la humanidá a les empreses."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Dempués llegó la IA. Prácticamente toles grandes empreses que tán construyendo LLM contactaron con nós pa entrenar colos nuestros datos. La mayoría (pero non toles) de les empreses con sede nos EE.XX. reconsideraron una vegada que se dieron cuenta de la naturaleza ilegal del nuestru trabayu. En cambiu, les empreses chineses abrazaron con entusiasmu la nuesa colección, aparentemente ensin preocuparse pola so legalidá. Esto ye notable, dado'l papel de China como signataria de casi tolos principales tratados internacionales de derechos d'autor."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Dimos accesu de gran velocidá a unes 30 empreses. La mayoría d'elles son empreses de LLM, y dalgunes son corredores de datos, que revenderán la nuesa colección. La mayoría son chineses, anque tamién trabayamos con empreses de los EE.XX., Europa, Rusia, Corea del Sur y Xapón. DeepSeek <a %(arxiv)s>admitió</a> que una versión anterior foi entrenada con parte de la nuesa colección, anque son reservados sobre'l so modelu más nuevu (probablemente tamién entrenáu colos nuestros datos)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si Occidente quier siguir alantre na carrera de los LLM, y en última instancia, AGI, necesita reconsiderar la so posición sobre los derechos d'autor, y pronto. Tanto si tas d'alcuerdu con nós o non sobre'l nuesu casu moral, esto ta convirtiéndose agora nun casu d'economía, y mesmo de seguridá nacional. Tolos bloques de poder tán construyendo super-científicos artificiales, super-hackers y super-militares. La llibertá d'información ta convirtiéndose nuna cuestión de supervivencia pa estos países — mesmo nuna cuestión de seguridá nacional."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "El nuesu equipu ye de tol mundu, y nun tenemos una alineación particular. Pero animaríamos a los países con lleis de derechos d'autor fuertes a usar esta amenaza existencial pa reformales. Entós, ¿qué facer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "La nuesa primer recomendación ye sencilla: acortar el plazu de los derechos d'autor. Nos EE.XX., los derechos d'autor concédense por 70 años dempués de la muerte del autor. Esto ye absurd. Podemos alinear esto coles patentes, que se conceden por 20 años dempués de la presentación. Esto debería ser más que suficiente tiempu pa que los autores de llibros, artículos, música, arte y otres obres creatives, seyan completamente compensaos polos sos esfuercios (incluyendo proyectos a llargu plazu como adaptaciones cinematográfiques)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Dempués, como mínimu, los responsables de formular polítiques deberían incluyir excepciones pa la preservación masiva y la difusión de testos. Si la pérdida d'ingresos de los clientes individuales ye la principal preocupación, la distribución a nivel personal podría siguir prohibida. A cambiu, aquellos capaces de xestionar vastos repositorios — empreses que entrenen LLM, xunto con biblioteques y otros archivos — taríen cubiertos por estes excepciones."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Dellos países yá tán faciendo una versión d'esto. TorrentFreak <a %(torrentfreak)s>informó</a> que China y Xapón introduxeron excepciones de IA nes sos lleis de derechos d'autor. Nun ta claro pa nós cómo esto interactúa colos tratados internacionales, pero ciertamente da cobertura a les sos empreses doméstiques, lo que esplica lo que tamos viendo."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "En cuanto al Archivu d'Anna — vamos siguir col nuesu trabayu subterráneu arriquecíu en convicción moral. Sicasí, el nuesu mayor deséu ye salir a la lluz, y amplificar el nuesu impactu de manera llegal. Por favor, reformen los derechos d'autor."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Llee los artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundu</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Ganadores del premiu de visualización de ISBN de $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Resumíu: Recibimos presentaciones increíbles pal premiu de visualización de ISBN de $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Hai unos meses anunciamos un <a %(all_isbns)s>premiu de $10,000</a> pa facer la meyor visualización posible de los nuestros datos amosando l'espaciu ISBN. Enfatizamos amosar qué archivos tenemos/no tenemos archivados y más tarde un conxuntu de datos que describen cuántes biblioteques tienen ISBNs (una midida de rareza)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Tamos abrumados pola respuesta. Hubo tanta creatividá. Un gran agradecimientu a toos los que participaron: ¡la vuestra enerxía y entusiasmu son infecciosos!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Al final, queríamos responder a les siguientes preguntes: <strong>¿qué llibros esisten nel mundu, cuántos tenemos archiváu yá, y en qué llibros deberíemos enfocar la nuestra atención próximamente?</strong> Ye estupendu ver que tanta xente se preocupa por estes cuestiones."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Entamamos con una visualización básica nós mesmos. En menos de 300kb, esta imaxe representa de manera sucinta la mayor “llista de llibros” completamente abierta xamás ensamblada na hestoria de la humanidá:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tolos ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Ficheros en l'Archivu d'Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Filtración de datos de CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Índiz d'eBooks d'EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archivu d'Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Rexistru Global d'Editores ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Estatal Rusa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Volver"

#, fuzzy
msgid "common.forward"
msgstr "Alantre"

#, fuzzy
msgid "common.last"
msgstr "Últimu"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Consulte la <a %(all_isbns)s>entrada orixinal del blogue</a> pa más información."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Emitiemos un retu pa meyorar esto. Otorgaríemos un premiu de primer puestu de 6.000 $, segundu puestu de 3.000 $ y tercer puestu de 1.000 $. Debíu a la gran respuesta y les submissions increíbiles, decidimos aumentar un poco'l fondu de premios y otorgar un tercer puestu compartíu a cuatro submissions de 500 $ cada una. Los ganadores tán embaxo, pero asegúrate de ver toles submissions <a %(annas_archive)s>equí</a>, o descarga'l nuesu <a %(a_2025_01_isbn_visualization_files)s>torrent combináu</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primer puestu 6.000 $: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Esta <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>comentariu en Gitlab</a>) ye simplemente too lo que queríemos, ¡y más! Gustónos especialmente les opciones de visualización increíblemente flexibles (incluyendo soporte pa shaders personalizaos), pero con una llista completa de presets. Tamién nos gustó lo rápido y suave que ye too, la implementación simple (que nin siquieru tien un backend), el minimapa intelixente y la esplicación estensa nel so <a %(phiresky_github)s>artículu del blogue</a>. ¡Un trabayu increíble y un ganador bien merecíu!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Segundu puestu 3.000 $: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Otra <a %(annas_archive_note_2913)s>submission</a> increíble. Non tan flexible como'l primer puestu, pero de fechu preferimos la so visualización a nivel macro sobre'l primer puestu (curva de rellenu d'espaciu, bordes, etiquetáu, resaltáu, paneu y zoom). Un <a %(annas_archive_note_2971)s>comentariu</a> de Joe Davis resonó con nós:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Mientres que los cuadros y rectángulos perfectos son matemáticamente agradables, nun proporcionen una localidá superior nun contestu de mapeu. Creo que la asimetría inherente neses Hilbert o Morton clásicos nun ye un defectu sinón una carauterística. Igual que la outline en forma de bota d'Italia faila instantáneamente reconocible nun mapa, los \"toques\" únicos d'estes curves pueden sirvir como puntos de referencia cognitivos. Esta distintividá pue ameyorar la memoria espacial y ayudar a los usuarios a orientase, potencialmente faciendo más fácil localizar rexones específiques o notar patrones.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Y entá munches opciones pa visualizar y renderizar, amás d'una interfaz d'usuariu increíblemente suave y intuitiva. ¡Un segundu puestu sólidu!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tercer puestu 500 $ #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Nesta <a %(annas_archive_note_2940)s>submission</a> gustónos muncho los distintos tipos de vistes, en particular les vistes de comparación y editor."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tercer puestu 500 $ #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Anque nun ye la interfaz más pulida, esta <a %(annas_archive_note_2917)s>submission</a> cumple con munchos de los requisitos. Gustónos especialmente la so carauterística de comparación."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tercer puestu 500 $ #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Como'l primer puestu, esta <a %(annas_archive_note_2975)s>submission</a> impresionónos cola so flexibilidad. En última instancia, esto ye lo que fai un gran ferramienta de visualización: máxima flexibilidad pa los usuarios avanzaos, mientres se mantien les coses simples pa los usuarios medios."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tercer puestu 500 $ #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "La última <a %(annas_archive_note_2947)s>submission</a> en recibir un premiu ye bastante básica, pero tien dalgunes carauterístiques úniques que nos gustaron muncho. Gustónos cómo amuesen cuántos datasets cubren un ISBN particular como midida de popularidá/fiabilidá. Tamién nos gustó muncho la simplicidá pero efectividá d'usar un deslizante d'opacidá pa comparaciones."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idees notables"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Dalgunes idees y implementaciones más que nos gustaron especialmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Rascacielos pa la rareza"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Estadístiques en direuto"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotaciones, y tamién estadístiques en direuto"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista de mapa única y filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Esquema de colores predetermináu guapu y mapa de calor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Cambiáu fácil de datasets pa comparaciones rápides."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etiquetes guapes."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra d'escala con númberu de llibros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Montones de deslizadores pa comparar datasets, como si fueres un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Podríemos siguir un ratu, pero vamos parar equí. Asegúrate de ver toles presentaciones <a %(annas_archive)s>equí</a>, o descarga'l nuesu <a %(a_2025_01_isbn_visualization_files)s>torrent combináu</a>. Tantes presentaciones, y cada una trai una perspeutiva única, ya seya en UI o implementación."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Al menos vamos incorporar la presentación del primer puestu nel nuesu sitiu web principal, y quiciabes delles más. Tamién empezamos a pensar en cómo organizar el procesu d'identificar, confirmar y depués archivar los llibros más raros. Más información próximamente."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Gracies a toos los que participaron. Ye increíble que tanta xente se preocupe."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Los nuesos corazones tán plenos de gratitú."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizando Tolos ISBNs — recompensa de $10,000 pa 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Esta imaxe representa la mayor “llista de llibros” completamente abierta xamás ensamblada na hestoria de la humanidá."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Esta imaxe ye de 1000×800 píxeles. Cada píxel representa 2,500 ISBNs. Si tenemos un archivu pa un ISBN, facemos que esi píxel seya más verde. Si sabemos que se emitió un ISBN, pero nun tenemos un archivu correspondiente, facémoslu más roxu."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En menos de 300kb, esta imaxe representa de manera sucinta la mayor “llista de llibros” completamente abierta xamás ensamblada na hestoria de la humanidá (unos pocos cientos de GB comprimíos en total)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Tamién amuesa: queda muncho trabayu por facer en respaldar llibros (namás tenemos 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Antecedentes"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "¿Cómo puede L'Archivu d'Anna algamar la so misión de respaldar tola conocencia de la humanidá, ensin saber qué llibros tán entá por ehí? Necesitamos una llista de TAREES. Una manera de mapear esto ye al traviés de los númberos ISBN, que dende los años 70 se-yos asignaron a cada llibru publicáu (na mayoría de los países)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nun hai una autoridá central que conozca toles asignaciones de ISBN. En vez d'eso, ye un sistema distribuyíu, onde los países reciben rangos de númberos, que llueu asignen rangos más pequeños a los principales editores, que pueden sub-dividir más los rangos a editores menores. Finalmente, los númberos individuales asígnense a los llibros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Empezamos a mapear los ISBN <a %(blog)s>hai dos años</a> col nuesu raspáu de ISBNdb. Dende entós, raspamos munches más fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, y más. Una llista completa pue atopase nes páxines de “Datasets” y “Torrents” en L'Archivu d'Anna. Agora tenemos, con diferencia, la mayor colección totalmente abierta y fácilmente descargable de metadata de llibros (y polo tanto ISBN) nel mundu."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Escribimos <a %(blog)s>ampliamente</a> sobre por qué nos importa la preservación, y por qué estamos anguaño nun periodu críticu. Debemos identificar llibros raros, poco enfocaos y únicamentemente en riesgu y preservalos. Tener bona metadata de tolos llibros del mundu ayuda con eso."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizando"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Amás de la imaxe de visión xeneral, tamién podemos ver datasets individuales que adquirimos. Usa'l menú desplegable y los botones pa cambiar ente ellos."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Hai munchos patrones interesantes que ver nestes cuadros. ¿Por qué hai dalguna regularidá de llinies y bloques, que paez que pasa a escales diferentes? ¿Cuáles son les árees vacíes? ¿Por qué ciertos datasets tán tan agrupados? Deixaremos estes entrugues como un exerciciu pal llector."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensa de $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Hai muncho por explorar equí, asina que tamos anunciando una recompensa por meyorar la visualización anterior. A diferencia de la mayoría de les nueses recompenses, esta ta llindada nel tiempu. Tienes que <a %(annas_archive)s>unviar</a> el to códigu fonte abiertu enantes del 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La meyor presentación recibirá $6,000, el segundu puestu $3,000, y el tercer puestu $1,000. Toles recompenses van ser entregaes usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Embaxo tán los criterios mínimos. Si nenguna presentación cumple colos criterios, podríamos entá otorgar dalgunes recompenses, pero eso va ser al nuesu criteriu."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forkea esti repositoriu, y edita esti post de blog en HTML (nun se permiten otros backends amás del nuesu backend Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Fai que la imaxe anterior se pueda zomar de manera suave, pa que puedas zomar hasta los ISBN individuales. Al facer clic nos ISBN, deberíes dir a una páxina de metadata o buscar en L'Archivu d'Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Debes poder cambiar ente tolos datasets diferentes."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Los rangos de países y los rangos d'editores deberíen destacase al pasar el cursor. Pue usase, por exemplu, <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> pa la información de países, y el nuesu raspáu “isbngrp” pa los editores (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Tien que funcionar bien en escritorios y dispositivos móviles."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pa puntos extra (estos son solo idees — dexa que la to creatividá corra llibre):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Va considerase fuertemente la usabilidá y lo bien que se vea."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Amuesa metadata real pa ISBN individuales al zomar, como títulu y autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Meyor curva de rellenu d'espaciu. Por exemplu, un zig-zag, yendo de 0 a 4 na primer fila y llueu de vuelta (en reversu) de 5 a 9 na segunda fila — aplicáu recursivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Esquemes de color distintos o personalizables."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vistes especiales pa comparar datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Maneres de depurar problemes, como otros metadata que nun concuerden bien (p. ex. títulos bien distintos)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotar imáxenes con comentarios sobre ISBNs o rangos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Cualesquier heurístiques pa identificar llibros raros o en riesgu."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "¡Cualesquier idees creatives que se te ocurran!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "PUEDEs desviar completamente de los criterios mínimos y facer una visualización completamente distinta. Si ye realmente espectacular, entós califica pa la recompensa, pero a la nuesa discreción."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fai les presentaciones publicando un comentariu en <a %(annas_archive)s>esti asuntu</a> con un enllaz al to repositoriu bifurcáu, solicitú de fusión o diferencia."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Códigu"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "El códigu pa xenerar estes imáxenes, amás d'otros exemplos, pue atopase en <a %(annas_archive)s>esti direutoriu</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Inventamos un formatu de datos compactu, col cual tola información necesaria del ISBN ocupa unos 75MB (comprimíu). La descripción del formatu de datos y el códigu pa xeneralu pue atopase <a %(annas_archive_l1244_1319)s>equí</a>. Pa la recompensa nun tas obligáu a usar esto, pero probablemente ye'l formatu más conveniente pa entamar. Puees tresformar el nuesu metadata como quieras (anque tol to códigu tien que ser de códigu abiertu)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Tamos deseyando ver lo que se te ocurre. ¡Bon suerte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contenedores del Archivu d’Anna (AAC): estandarizando les publicaciones de la mayor biblioteca sombra del mundu"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "El Archivu d’Anna convirtióse na mayor biblioteca sombra del mundu, lo que nos obliga a estandarizar les nueses publicaciones."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>El Archivu d’Anna</a> convirtióse con diferencia na mayor biblioteca sombra del mundu, y la única biblioteca sombra de la so escala que ye completamente de códigu abiertu y datos abiertos. Debaxo hai una tabla de la nuesa páxina de Datasets (ligeramente modificada):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Logramos esto de tres maneres:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Reflexando biblioteques sombra de datos abiertos esistentes (como Sci-Hub y Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Ayudando a biblioteques sombra que quieren ser más abiertes, pero nun teníen el tiempu o los recursos pa facelo (como la colección de cómics de Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspando biblioteques que nun quieren compartir en bloque (como Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Pa (2) y (3) agora xestionamos una considerable colección de torrents nós mesmos (cientos de TBs). Hasta agora tratamos estes colecciones como casos únicos, lo que significa infraestructura y organización de datos a medida pa cada colección. Esto suma una carga significativa a cada llanzamientu, y fae que seya particularmente difícil facer más llanzamientos incrementales."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Por eso decidimos estandarizar los nuestros llanzamientos. Esti ye un artículu técnicu nel que tamos presentando el nuesu estándar: <strong>Contenedores d’Archivu d’Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Oxetivos de diseñu"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "El nuesu casu d’usu principal ye la distribución de ficheros y metadata asociada de colecciones esistentes diferentes. Les consideraciones más importantes son:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Ficheros y metadata heteroxéneos, lo más cercanos posible al formatu orixinal."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificadores heteroxéneos nes biblioteques fonte, o incluso falta d’identificadores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Llanzamientos separaos de metadata frente a datos de ficheros, o llanzamientos solo de metadata (p. ex. el nuesu llanzamientu de ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribución a través de torrents, anque con la posibilidá d’otros métodos de distribución (p. ex. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Rexistros inmutables, yá que deberíamos asumir que los nuestros torrents van vivir pa siempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Llanzamientos incrementales / llanzamientos ampliables."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Lleíble y escribible por máquines, de manera conveniente y rápida, especialmente pa la nuesa pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspección humana relativamente fácil, anque esto ye secundariu a la lleibilidá por máquines."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Fácil de sembrar les nueses colecciones con un seedbox alquiláu estándar."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Los datos binarios pueden sirvese direutamente por servidores web como Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Dalgunos non-oxetivos:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nun nos importa que los ficheros seyan fáciles de navegar manualmente nel discu, o que seyan buscables ensin preprocesamientu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nun nos importa ser direutamente compatibles col software de biblioteques esistente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Anque debería ser fácil pa cualisquier persona sembrar la nuesa colección usando torrents, nun esperamos que los ficheros seyan utilizables ensin un conocimientu técnicu significativu y compromisu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Dende que l’Archivu d’Anna ye de códigu abiertu, queremos usar el nuesu formatu direutamente. Cuando actualizamos el nuesu índiz de busca, namái accedemos a les rutes públicamente disponibles, pa que cualisquier que bifurque la nuesa biblioteca pueda ponese en marcha rápidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "El estándar"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Al final, decidimos un estándar relativamente simple. Ye bastante flexible, non normativu, y un trabayu en procesu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contenedor del Archivu d'Anna) ye un elementu únicu que consiste en <strong>metadata</strong>, y opcionalmente <strong>datos binarios</strong>, dambos inmutables. Tien un identificador únicu global, llamáu <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Colección.</strong> Cada AAC pertenez a una colección, que por definición ye una llista de AACs que son semánticamente consistentes. Esto significa que si faes un cambiu significativu nel formatu de la metadata, entós tienes que crear una nueva colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Colecciones de “registros” y “ficheros”.</strong> Por convención, a menudo ye conveniente llanzar “registros” y “ficheros” como colecciones diferentes, pa que puedan llanzase en distintos calendarios, por exemplu, basáu en les tases de raspáu. Un “rexistru” ye una colección solo de metadata, que contién información como títulos de llibros, autores, ISBNs, etc., mientres que “ficheros” son les colecciones que contién los ficheros reales (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> El formatu del AACID ye esti: <code style=\"color: #0093ff\">aacid__{colección}__{timestamp ISO 8601}__{ID específicu de la colección}__{shortuuid}</code>. Por exemplu, un AACID real que llanzamos ye <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{colección}</code>: el nome de la colección, que pue contener lletres ASCII, númberos y guiones baxos (pero non guiones baxos dobles)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{timestamp ISO 8601}</code>: una versión corta del ISO 8601, siempre en UTC, por exemplu <code>20220723T194746Z</code>. Esti númberu tien que aumentar monótonamente pa cada llanzamientu, anque los sos semánticos exactos pueden diferir por colección. Suxerimos usar el tiempu de raspáu o de xeneración del ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{ID específicu de la colección}</code>: un identificador específicu de la colección, si ye aplicable, por exemplu, el ID de Z-Library. Pue omitise o truncase. Debe omitise o truncase si el AACID, d'otra manera, excedería los 150 caráuteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID pero comprimíu a ASCII, por exemplu usando base57. Actualmente usamos la biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Rangu de AACID.</strong> Dende que los AACIDs contién timestamps que aumenten monótonamente, podemos usar eso pa indicar rangos dientro d'una colección particular. Usamos esti formatu: <code style=\"color: blue\">aacid__{colección}__{from_timestamp}--{to_timestamp}</code>, onde los timestamps son inclusivos. Esto ye consistente cola notación ISO 8601. Los rangos son continuos, y pueden solapase, pero en casu de solape deben contener rexistros idénticos a los llanzados previamente nesa colección (dende que los AAC son inmutables). Nun se permiten rexistros faltaos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Ficheru de metadata.</strong> Un ficheru de metadata contién la metadata d'un rangu de AACs, pa una colección particular. Estos tienen les siguientes propiedaes:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "El nome del ficheru tien que ser un rangu de AACID, prefijáu con <code style=\"color: red\">annas_archive_meta__</code> y siguíu por <code>.jsonl.zstd</code>. Por exemplu, unu de los nuestros llanzamientos llámase<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Como indica la estensión del ficheru, el tipu de ficheru ye <a %(jsonlines)s>JSON Lines</a> comprimíu con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada oxetu JSON tien que contener los siguientes campos al nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Nun se permiten otros campos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> ye metadata arbitraria, según los semánticos de la colección. Debe ser semánticamente consistente dientro de la colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> ye opcional, y ye el nome de la carpeta de datos binarios que contién los datos binarios correspondientes. El nome del ficheru de los datos binarios correspondientes dientro d'esa carpeta ye l'AACID del rexistru."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "El prefiju <code style=\"color: red\">annas_archive_meta__</code> pue adaptase al nome de la to institución, por exemplu <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Carpeta de datos binarios.</strong> Una carpeta colos datos binarios d'un rangu de AACs, pa una colección particular. Estos tienen les siguientes propiedaes:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "El nome del direutoriu tien que ser un rangu de AACID, prefijáu con <code style=\"color: green\">annas_archive_data__</code>, y ensin sufixu. Por exemplu, unu de los nuestros llanzamientos reales tien un direutoriu llamáu<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "El direutoriu tien que contener ficheros de datos pa tolos AACs dientro del rangu especificáu. Cada ficheru de datos tien que tener el so AACID como nome del ficheru (ensin estensiones)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Recomiéndase que estos cartafueyos tengan un tamañu relativamente manexable, por exemplu, que nun seyan más grandes de 100GB-1TB cada ún, anque esta recomendación pue camudar col tiempu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Los ficheros de metadata y los cartafueyos de datos binarios pueden tar empaquetaos en torrents, con un torrent por cada ficheru de metadata, o un torrent por cada cartafueyu de datos binarios. Los torrents han de tener el nome orixinal del ficheru/directoriu más el sufixu <code>.torrent</code> como so nome de ficheru."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplu"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Vamos ver el nuesu llanzamientu reciente de Z-Library como exemplu. Consiste en dos coleiciones: “<span style=\"background: #fffaa3\">zlib3_records</span>” y “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Esto permítenos rascar y llanzar por separáu los rexistros de metadata de los ficheros de llibros reales. Asina, llanzamos dos torrents con ficheros de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Tamién llanzamos un bon númberu de torrents con cartafueyos de datos binarios, pero namái pa la coleición “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 en total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Executando <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver lo que hai dientro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Nesti casu, ye metadata d’un llibru según lo reportáu por Z-Library. Al nivel superior namái tenemos “aacid” y “metadata”, pero nun hai “data_folder”, yá que nun hai datos binarios correspondientes. El AACID contién “22430000” como ID principal, que podemos ver que se toma de “zlibrary_id”. Podemos esperar que otros AACs nesta coleición tengan la mesma estructura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Agora vamos executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Esta ye una metadata AAC muncho más pequeña, anque la mayor parte d'esta AAC ta n'otru llau nun ficheru binariu. Al fin y al cabu, tenemos un “data_folder” esta vuelta, polo que podemos esperar que los datos binarios correspondientes se atopen en <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. La “metadata” contién el “zlibrary_id”, polo que podemos asociar fácilamente col AAC correspondiente na coleición “zlib_records”. Podríemos haber asociáu de munches maneres diferentes, por exemplu, a través del AACID — el estándar nun lo prescribe."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Tenga en cuenta que tampoco ye necesario que el campu “metadata” seya JSON. Podría ser una cadena que contenga XML o cualquier otru formatu de datos. Incluso podríes almacenar información de metadata nel blob binariu asociáu, por exemplu, si ye muncha información."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusión"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con esti estándar, podemos facer llanzamientos de manera más incremental, y amestar más fácilamente nueves fuentes de datos. ¡Yá tenemos dalgunos llanzamientos emocionantes en preparación!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Tamién esperamos que seya más fácil pa otres biblioteques en l'ombra reflejar les nueses coleiciones. Al fin y al cabu, el nuesu oxetivu ye preservar el conocimientu y la cultura humana pa siempre, polo que cuanta más redundancia, meyor."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Actualización d'Anna: archivu completamente de códigu abiertu, ElasticSearch, más de 300GB de portades de llibros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Hemos estado trabayando arreo pa proporcionar una bona alternativa con l'Archivu d'Anna. Equí dalgunes de les coses que llogramos recién."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Con Z-Library cayendo y los sos (supuestos) fundadores arrestaos, hemos estado trabayando arreo pa proporcionar una bona alternativa con l'Archivu d'Anna (nun vamos enllazalo equí, pero pue buscálo en Google). Equí dalgunes de les coses que llogramos recién."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L'Archivu d'Anna ye completamente de códigu abiertu"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Creemos que la información ha ser llibre, y el nuesu propiu códigu nun ye una esceición. Hemos llanzáu tol nuesu códigu na nuesa instancia de Gitlab privada: <a %(annas_archive)s>Software d'Anna</a>. Tamién usamos el rastreador d'incidentes pa organizar el nuesu trabayu. Si quies participar nel nuesu desenvolvimientu, esti ye un bon llugar pa entamar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Pa darvos una idea de les coses nes que tamos trabayando, mirai'l nuesu trabayu reciente en meyores de rendimientu del llau del cliente. Como inda nun implementamos la paginación, a menudo devolvíamos páxines de busca bien llargues, con 100-200 resultaos. Nun queríemos cortar los resultaos de la busca demasiao pronto, pero esto significaba que ralentizaba dalgunes dispositivos. Pa esto, implementamos un pequeñu trucu: envolvimos la mayoría de los resultaos de busca en comentarios HTML (<code><!-- --></code>), y depués escribimos un pequeñu script de Javascript que detectaría cuándo un resultáu debería facese visible, nel momentu en que desenvolveríemos el comentariu:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "La \"virtualización\" del DOM implementada en 23 líneas, ¡sin necesidad de bibliotecas sofisticadas! Este es el tipo de código pragmático y rápido que se obtiene cuando se tiene tiempo limitado y problemas reales que necesitan ser resueltos. ¡Se ha informado que nuestra búsqueda ahora funciona bien en dispositivos lentos!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Otro gran esfuerzo fue automatizar la construcción de la base de datos. Cuando lanzamos, simplemente juntamos diferentes fuentes de manera desordenada. Ahora queremos mantenerlas actualizadas, así que escribimos un montón de scripts para descargar nuevos metadata de los dos forks de Library Genesis, e integrarlos. El objetivo no es solo hacer esto útil para nuestro archivo, sino facilitar las cosas para cualquiera que quiera experimentar con metadata de bibliotecas en la sombra. El objetivo sería un cuaderno de Jupyter que tenga todo tipo de metadata interesante disponible, para que podamos hacer más investigaciones como averiguar qué <a %(blog)s>porcentaje de ISBNs se preservan para siempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, renovamos nuestro sistema de donaciones. Ahora puedes usar una tarjeta de crédito para depositar dinero directamente en nuestras carteras de criptomonedas, sin realmente necesitar saber nada sobre criptomonedas. Seguiremos monitoreando qué tan bien funciona esto en la práctica, pero es un gran avance."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Cambiar a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Uno de nuestros <a %(annas_archive)s>tickets</a> era un conjunto de problemas con nuestro sistema de búsqueda. Usábamos la búsqueda de texto completo de MySQL, ya que teníamos todos nuestros datos en MySQL de todos modos. Pero tenía sus límites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Algunas consultas tomaban mucho tiempo, hasta el punto de acaparar todas las conexiones abiertas."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Por defecto, MySQL tiene una longitud mínima de palabra, o tu índice puede volverse realmente grande. La gente reportó no poder buscar \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La búsqueda solo era algo rápida cuando estaba completamente cargada en memoria, lo que requería que obtuviéramos una máquina más cara para ejecutarla, además de algunos comandos para precargar el índice al inicio."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "No hubiéramos podido extenderlo fácilmente para construir nuevas funciones, como mejor <a %(wikipedia_cjk_characters)s>tokenización para idiomas sin espacios</a>, filtrado/facetas, ordenación, sugerencias de \"¿quisiste decir?\", autocompletar, y así sucesivamente."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Después de hablar con un montón de expertos, nos decidimos por ElasticSearch. No ha sido perfecto (sus sugerencias de \"¿quisiste decir?\" y funciones de autocompletar por defecto son malas), pero en general ha sido mucho mejor que MySQL para la búsqueda. Todavía no estamos <a %(youtube)s>muy entusiasmados</a> con usarlo para cualquier dato crítico (aunque han hecho mucho <a %(elastic_co)s>progreso</a>), pero en general estamos bastante contentos con el cambio."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por ahora, hemos implementado una búsqueda mucho más rápida, mejor soporte de idiomas, mejor ordenación por relevancia, diferentes opciones de ordenación y filtrado por idioma/tipo de libro/tipo de archivo. Si tienes curiosidad sobre cómo funciona, <a %(annas_archive_l140)s>échale</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>vistazo</a>. Es bastante accesible, aunque podría usar algunos comentarios más…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Más de 300GB de portadas de libros liberadas"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, nos complace anunciar un pequeño lanzamiento. En colaboración con las personas que operan el fork Libgen.rs, estamos compartiendo todas sus portadas de libros a través de torrents e IPFS. Esto distribuirá la carga de ver las portadas entre más máquinas y las preservará mejor. En muchos (pero no todos) casos, las portadas de los libros están incluidas en los archivos mismos, por lo que esto es una especie de \"datos derivados\". Pero tenerlo en IPFS sigue siendo muy útil para la operación diaria tanto de l'Archivu d'Anna como de los varios forks de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costumbre, puedes encontrar este lanzamiento en el Espeju de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>l'Archivu d'Anna</a>). No lo enlazaremos aquí, pero puedes encontrarlo fácilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Esperamos poder relajar nuestro ritmo un poco, ahora que tenemos una alternativa decente a Z-Library. Esta carga de trabajo no es particularmente sostenible. Si estás interesado en ayudar con la programación, operaciones de servidor o trabajo de preservación, definitivamente contáctanos. Todavía hay mucho <a %(annas_archive)s>trabajo por hacer</a>. Gracias por tu interés y apoyo."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "L'Archivu d'Anna ha respaldado la biblioteca de cómics en la sombra más grande del mundo (95TB) — puedes ayudar a sembrarla"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La biblioteca de cómics en la sombra más grande del mundo tenía un único punto de fallo... hasta hoy."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discute en Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La biblioteca de cómics en la sombra más grande probablemente sea la de un fork particular de Library Genesis: Libgen.li. El único administrador que ejecuta ese sitio logró recopilar una colección de cómics increíble de más de 2 millones de archivos, totalizando más de 95TB. Sin embargo, a diferencia de otras colecciones de Library Genesis, esta no estaba disponible en masa a través de torrents. Solo podías acceder a estos cómics individualmente a través de su servidor personal lento — un único punto de fallo. ¡Hasta hoy!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Nesta publicación contámosvos más sobre esta colección y sobre la nuestra recaudación de fondos pa sofitar más d'esti trabayu."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>La Dra. Barbara Gordon intenta perdese nel mundu mundanu de la biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Bifurcaciones de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primero, dalgo de fondu. Quiciabes conozas a Library Genesis pola so épica colección de llibros. Menos xente sabe que los voluntarios de Library Genesis crearon otros proyectos, como una gran colección de revistes y documentos estándar, una copia de seguridá completa de Sci-Hub (en collaboración cola fundadora de Sci-Hub, Alexandra Elbakyan), y, de fechu, una masiva colección de cómics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Nalgún momentu, distintos operadores de los espeyos de Library Genesis siguieron caminos distintos, lo que dio llugar a la situación actual de tener una serie de \"bifurcaciones\" diferentes, toles cuales siguen llevando'l nome de Library Genesis. La bifurcación de Libgen.li tien de manera única esta colección de cómics, amás d'una gran colección de revistes (na que tamién tamos trabayando)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaboración"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada la so tamañu, esta colección lleva tiempu na nuestra llista de deseyos, asina que dempués del nuesu ésitu col respaldamientu de Z-Library, fixemos la nuestra mira nesta colección. Al principiu, raspámosla direutamente, lo que foi un verdaderu retu, yá que'l so sirvidor nun taba na meyor condición. Asina conseguimos unos 15TB, pero foi un procesu lentu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Por suerte, conseguimos ponenos en contautu col operador de la biblioteca, quien acordó mandanos tolos datos direutamente, lo que foi muncho más rápido. Aún asina llevó más de mediu añu tresferir y procesar tolos datos, y casi perdimos tolos datos por corrupción de discu, lo que significaría empezar de nuevu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Esta esperiencia fízonos creer que ye importante sacar estos datos al públicu lo más rápido posible, pa que puedan ser espeyaos de manera amplia. ¡Tamos a unu o dos incidentes desafortunados de perder esta colección pa siempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La colección"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Moverse rápido significa que la colección ta un poco desorganizada… Vamos ver. Imagina que tenemos un sistema de ficheros (que na realidá tamos partiendo en torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "El primer direutoriu, <code>/repository</code>, ye la parte más estructurada d'esto. Esti direutoriu contién los llamaos “mil dirs”: direutorios con mil archivos cada ún, que tán numerados incrementalmente na base de datos. El direutoriu <code>0</code> contién archivos con comic_id 0–999, y asina sucesivamente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Esti ye'l mesmu esquema que Library Genesis vien usando pa les sos colecciones de ficción y non ficción. La idea ye que cada “mil dir” se convierta automáticamente nun torrent tan pronto como s'alluñe."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Sicasí, l'operador de Libgen.li nunca fizo torrents pa esta colección, y asina los mil dirs probablemente volvéronse inconvenientes, y dieron pasu a “dirs ensin clasificar”. Estos son <code>/comics0</code> a <code>/comics4</code>. Tolos tienen estructures de direutorios úniques, que probablemente teníen sentíu pa coleccionar los archivos, pero que nun tienen muncho sentíu pa nós agora. Por suerte, el metadata sigue refiriéndose direutamente a toos estos archivos, asina que la so organización d'almacenamientu en discu nun importa realmente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "El metadata ta disponible en forma d'una base de datos MySQL. Esta pue descargase direutamente dende'l sitiu web de Libgen.li, pero tamién la vamos poner disponible nun torrent, xunto cola nuestra propia tabla con tolos hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Análisis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Cuando recibes 95TB volcáos nel to cluster d'almacenamientu, intentes dar sentíu a lo que hai ellí… Fiximos dalgún análisis pa ver si podíemos reducir el tamañu un poco, como eliminando duplicados. Equí tán dalgunes de les nuestres conclusiones:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Los duplicados semánticos (distintes escaneos del mesmu llibru) pueden teóricamente ser filtraos, pero ye complicáu. Al revisar manualmente los cómics atopamos demasiados falsos positivos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Hai dalgunos duplicados puramente por MD5, lo que ye relativamente desperdiciu, pero filtrar esos solo nos daría un aforru d'unos 1% in. A esta escala eso sigue siendo unos 1TB, pero tamién, a esta escala 1TB nun importa realmente. Preferimos nun arriesgamos a destruir datos accidentalmente nesti procesu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Atopamos un bon númberu de datos que nun son llibros, como películes basaes en cómics. Eso tamién paez desperdiciu, yá que estos yá tán ampliamente disponibles por otros medios. Sicasí, dimos cuenta de que nun podíemos simplemente filtrar archivos de películes, yá que tamién hai <em>cómics interactivos</em> que se publicaron na computadora, que dalguién grabó y guardó como películes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Al final, cualquier cosa que pudiéramos esborrar de la colección namás aforraría un porcentaxe pequeñu. Dempués recordamos que somos acumuladores de datos, y les persones que van a espeyar esto tamién lo son, y entós, “¿QUÉ QUIERES DECIR, ESBORRAR?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Por eso, tamos presentándovos la colección completa y ensin modificar. Ye muncha información, pero esperamos que abonde xente que quiera compartila de toes formes."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Recaudación de fondos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Tamos llanzando estos datos en bloques grandes. El primer torrent ye de <code>/comics0</code>, que pusimos nun archivu .tar xigante de 12TB. Ye meyor pa to discu duru y software de torrents que un millón de ficheros más pequeños."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte d'esti llanzamientu, tamos faciendo una recaudación de fondos. Buscamos xuntar $20,000 pa cubrir costes operativos y de contratación pa esta colección, amás de permitir proyectos actuales y futuros. Tenemos dalgunos <em>enormes</em> en marcha."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>¿A quién apoyo col mio donativu?</em> En resume: tamos respaldando tola conocencia y cultura de la humanidá, y faciéndola fácilmente accesible. Tol nuesu códigu y datos son de códigu abiertu, somos un proyeutu xestionáu completamente por voluntarios, y salvamos 125TB de llibros hasta agora (amás de los torrents esistentes de Libgen y Scihub). En última instancia, tamos construyendo un volantín que permite y incentiva a la xente a atopar, escanear y respaldar tolos llibros del mundu. Escribiremos sobre'l nuesu plan maestru nun futuru artículu. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si donas pa una mebresía de 12 meses “Amazing Archivist” ($780), puedes <strong>“adoptar un torrent”</strong>, lo que significa que pondremos el to nome d'usuariu o mensaxe nel nome d'un de los torrents."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Puedes donar yendo a <a %(wikipedia_annas_archive)s>L'Archivu d'Anna</a> y faciendo clic nel botón “Donar”. Tamién tamos buscando más voluntarios: inxenieros de software, investigadores de seguridá, expertos en comerciu anónimu y traductores. Tamién puedes apoyanos proporcionando servicios d'aloxamientu. Y por supuestu, ¡por favor comparte los nuesos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "¡Gracies a toos los que tan xenerosamente nos apoyaron yá! Tán faciendo una verdadera diferencia."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Equí tán los torrents llanzados hasta agora (tamos procesando'l restu):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tolos torrents pueden alcontrase en <a %(wikipedia_annas_archive)s>L'Archivu d'Anna</a> baxo “Datasets” (nun enllazamos direutamente, pa que nun se quiten los enllaces d'esti blog de Reddit, Twitter, etc). Dende ellí, sigue l'enllaz al sitiu web de Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "¿Qué ye lo siguiente?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un montonéu de torrents son estupendos pa la preservación a llargu plazu, pero non tanto pa l'accesu diariu. Tamos trabayando con socios d'aloxamientu pa poner toos estos datos na web (yá que L'Archivu d'Anna nun aloja nada direutamente). Por supuestu podrás atopar estos enllaces de descarga en L'Archivu d'Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Tamién tamos invitando a toos a facer coses con estos datos! Ayúdanos a analizalos meyor, desduplicalos, ponelos en IPFS, remixalos, entrenar los tos modelos d'IA con ellos, y asina. Son toos tuyos, y nun podemos esperar a ver lo que faes con ellos."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como se dixo antes, inda tenemos dalgunos llanzamientos enormes que tán por venir (si <em>dalguien</em> pudiera <em>accidentalmente</em> mandanos un volcu d'una <em>determinada</em> base de datos ACS4, yá sabes onde atopanos…), amás de construyir el volantín pa respaldar tolos llibros del mundu."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Asina que mantente atent@, namás tamos empezando."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nuevos llibros amestaos al Espeyu de la Biblioteca Pirata (+24TB, 3.8 millones de llibros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Na versión orixinal del Espeyu de la Biblioteca Pirata (EDIT: movíu a <a %(wikipedia_annas_archive)s>L'Archivu d'Anna</a>), fiximos un espeyu de Z-Library, una gran colección de llibros ilegal. Como recordatoriu, esto ye lo que escribimos nesi artículu orixinal del blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library ye una biblioteca popular (y ilegal). Tomaron la colección de Library Genesis y fixéronla fácilmente searchable. Amás, volvieron bien efectivos solicitando contribuciones de nuevos llibros, incentivando a los usuarios que contribúin con varios beneficios. Actualmente nun contribúin estos nuevos llibros de vuelta a Library Genesis. Y a diferencia de Library Genesis, nun faen que la so colección seya fácilmente espeyable, lo que impide una amplia preservación. Esto ye importante pal so modelu de negociu, yá que cobren por acceder a la so colección en bloque (más de 10 llibros al día)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "No hacemos juicios morales sobre cobrar dinero por el acceso en masa a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Esa colección data de mediados de 2021. Mentanto, la Z-Library creció a un ritmu asombrosu: amestaron alredor de 3,8 millones de llibros nuevos. Hai dalgunos duplicados, claro, pero la mayoría paez ser llibros verdaderamente nuevos, o escaneos de mayor calidá de llibros presentaos previamente. Esto ye en gran parte gracies al aumentu del númberu de moderadores voluntarios na Z-Library, y al so sistema de cargues masives con deduplicación. Queremos felicitalos por estos llogros."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Tamos contentos d’anunciar que conseguimos tolos llibros que s’amestaron a la Z-Library ente’l nuesu últimu espeyu y agostu de 2022. Tamién volvimos atrás y rascamos dalgunos llibros que nos perdimos la primer vez. En total, esta nueva colección ye d’unos 24TB, que ye muncho más grande que la anterior (7TB). El nuesu espeyu ye agora de 31TB en total. Otra vuelta, deduplicamos contra Library Genesis, yá que yá hai torrents disponibles pa esa colección."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Por favor, dir al Espeyu de la Biblioteca Pirata pa ver la nueva colección (EDIT: movíu a <a %(wikipedia_annas_archive)s>Archivu d’Anna</a>). Hai más información ellí sobre cómo tán estructuraos los ficheros, y qué cambió dende la última vez. Nun vamos enllazalo dende equí, yá que esto ye namás un sitiu web de blogs que nun alcuentra materiales ilegales."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Por supuestu, sembrar tamién ye una manera estupenda d’ayudanos. Gracies a toos los que tán sembrando el nuesu conxuntu anterior de torrents. Tamos agradecíos pola respuesta positiva, y contentos de que haya tanta xente que se preocupa pola preservación del conocimientu y la cultura d’esta manera inusual."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Cómo convertise en un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "El primer desafíu pue ser unu sorprendente. Nun ye un problema técnicu, nin un problema llegal. Ye un problema psicolóxicu."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Enantes d’entrar en materia, dos actualizaciones sobre’l Espeyu de la Biblioteca Pirata (EDIT: movíu a <a %(wikipedia_annas_archive)s>Archivu d’Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Recibimos dalgunes donaciones extremadamente xeneroses. La primera foi de $10k d’una persona anónima que tamién vien apoyando a \"bookwarrior\", el fundador orixinal de Library Genesis. Gracies especiales a bookwarrior por facilitar esta donación. La segunda foi otra de $10k d’un donante anónimu, que se punxo en contautu dempués de la nuesa última publicación, y inspiróse pa ayudar. Tamién tuvimos una serie de donaciones más pequeñes. Gracies muncho por tolu so xenerosu sofitu. Tenemos dalgunos proyectos nuevos y emocionantes en camín que esto va sofitar, asina que mantente atent@."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Tuvimos dalgunes dificultaes téuniques col tamañu de la nuesa segunda publicación, pero los nuesos torrents tán activos y sembrando agora. Tamién recibimos una ufierta xenerosa d’una persona anónima pa sembrar la nuesa colección nos sos servidores de velocidá muy alta, asina que tamos faciendo una carga especial a les sos máquines, dempués de lo cual tolos demás que tán descargando la colección deberíen ver una gran meyora na velocidá."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Pue escribise llibros enteros sobre’l <em>por qué</em> de la preservación dixital en xeneral, y del archivismu pirata en particular, pero vamos dar un resumen rápidu pa los que nun tán mui familiarizaos. El mundu ta produciendo más conocimientu y cultura que nunca, pero tamién más d’ello ta perdiéndose que nunca. La humanidá confía en gran parte en corporaciones como editoriales académicas, servicios de streaming y compañíes de redes sociales pa esti patrimoniu, y a menudo nun demostraron ser grandes guardianes. Consulta’l documental Amnesia Dixital, o realmente cualquier charla de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Hai dalgunes instituciones que faen un bon trabayu archivando tanto como pueden, pero tán lligaes pola llei. Como piratas, tamos nuna posición única pa archivar colecciones que nun pueden tocar, por mor de la aplicación de derechos d’autor u otres restricciones. Tamién podemos espeyar colecciones munches veces, al traviés del mundu, aumentando asina les posibilidaes d’una preservación adecuada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por agora, nun vamos entrar en discussions sobre los pros y los contras de la propiedá intelectual, la moralidá de romper la llei, reflexones sobre la censura, o’l tema del accesu al conocimientu y la cultura. Con tolo eso fuera del camín, vamos sumerginos nel <em>cómo</em>. Vamos compartir cómo’l nuesu equipu se convirtió en archivistes pirata, y les lleiciones que deprendimos al llargu del camín. Hai munchos desafíos cuando te embarques nesti viaxe, y esperamos poder ayudate con dalgunos d’ellos."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunidá"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "El primer desafíu pue ser unu sorprendente. Nun ye un problema técnicu, nin un problema llegal. Ye un problema psicolóxicu: facer esti trabayu a la escontra pue ser increíblemente solitariu. Dependiendo de lo que planifiques facer, y del to modelu d’amenaza, quiciabes tengas que ser bien cuidaosu. Nun estremu del espectru tenemos a xente como Alexandra Elbakyan*, la fundadora de Sci-Hub, que ye bien abierta sobre les sos actividaes. Pero ta en altu riesgu de ser arrestada si visitara un país occidental nesti momentu, y podría enfrentase a décades de prisión. ¿Ye ese un riesgu que taríes dispuestu a tomar? Tamos nel otru estremu del espectru; siendo bien cuidaosos de nun dexar nenguna traza, y teniendo una fuerte seguridá operativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como se mencionó en HN por \"ynno\", Alexandra inicialmente nun quería ser conocida: \"Los sos servidores taben configuraos pa emitir mensaxes d’error detallaos de PHP, incluyendo la ruta completa del ficheru fonte con fallos, que taba so’l direutoriu /home/<USER>" Asina que, usa nomes d’usuariu aleatorios nes computaores que uses pa estes coses, por si configures mal dalgo."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Esi secretu, sicasí, vien con un costu psicolóxicu. A la mayoría de la xente encántai ser reconocida pol trabayu que faen, y sicasí nun puedes llevar el méritu por esto na vida real. Incluso coses simples pueden ser un desafíu, como cuando los amigos te pregunten qué has estado faciendo (a un momentu \"trasteando col mio NAS / homelab\" faise vieyu)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Por eso ye tan importante atopar dalguna comunidá. Puedes ceder dalgo de seguridá operativa confiando en dalgunos amigos bien cercanos, de los que sabes que puedes fiar fondamente. Incluso entós ten cuidáu de nun poner nada por escrito, por si tienen que entregar los sos correos a les autoridaes, o si los sos dispositivos son comprometeos d'otra manera."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Mejor entá ye atopar dalgún compañeru pirata. Si los tos amigos cercanos tán interesaos en unise a ti, ¡fantásticu! Si non, podríes atopar a otros en llinia. Tristemente, esta sigue siendo una comunidá de nichu. Hasta agora namás atopamos un puñáu d'otros que tán activos nesti espaciu. Buenos llugares pa entamar paecen ser los foros de Library Genesis, y r/DataHoarder. L'Equipo d'Archivu tamién tien individuos con mentalidá similar, anque operen dientro de la llei (anque en dalgunes zones grises de la llei). Les escenes tradicionales de \"warez\" y piratería tamién tienen xente que piensa de manera similar."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Tamos abiert@s a idees sobre cómo fomentar la comunidá y explorar idees. Siéntete llibre de mandanos un mensaxe en Twitter o Reddit. Quiciabes podríemos albergar dalgún tipu de foru o grupu de charrera. Un retu ye que esto pue ser censuráu fácilmente al usar plataformes comunes, asina que tendríamos que albergalo nós mesm@s. Tamién hai un equilibriu ente tener estes discussions completamente públiques (más participación potencial) frente a facelo priváu (pa nun dexa-yos saber a los potenciales \"blancos\" que tamos a piques de rascar). Tendremos que pensar en eso. ¡Faínos saber si tas interesáu nesto!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proyectos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Cuando facemos un proyectu, tien un par de fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Seleición de dominiu / filosofía: ¿En qué quies centrate más o menos, y por qué? ¿Cuáles son les tos pasiones úniques, habilidaes y circunstancies que puedes usar al to favor?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Seleición de blancu: ¿Qué colección específica vas a espeyar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Rascáu de metadata: Catalogar información sobre los ficheros, ensin descargar realmente los ficheros (que suelen ser muncho más grandes)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Seleición de datos: Basáu na metadata, afinar qué datos son más relevantes pa archivar agora mesmu. Podría ser too, pero a menudo hai una manera razonable de guardar espaciu y ancho de banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Rascáu de datos: Conseguir realmente los datos."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribución: Empaquetalo en torrents, anuncialo en dalgún sitiu, consiguir que la xente lo esparda."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Estes nun son fases completamente independentes, y a menudo los conocimientos d'una fase posterior te lleven de vuelta a una fase anterior. Por exemplu, mientres el rascáu de metadata podríes darte cuenta de que'l blancu que seleicionasti tien mecanismos defensivos más allá del to nivel d'habilidaes (como bloqueos d'IP), asina que vuelves atrás y atopas un blancu diferente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Seleición de dominiu / filosofía"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nun hai escasez de conocimientu y patrimoniu cultural que salvar, lo que pue ser abrumador. Por eso a menudo ye útil tomase un momentu y pensar en cuál pue ser la to contribución."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Cada persona tien una manera diferente de pensar sobre esto, pero equí hai dalgunes preguntes que podríes facete:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "¿Por qué te interesa esto? ¿Qué ye lo que te apasiona? Si podemos consiguir un grupu de persones que todes archiven los tipos de coses que-yos importen específicamente, ¡eso cubriría muncho! Sabrá más que la persona promedio sobre la to pasión, como qué datos son importantes de guardar, cuáles son les meyores colecciones y comunidaes en llinia, y asina."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "¿Qué habilidaes tienes que puedes usar al to favor? Por exemplu, si yes un/a expertu/a en seguridá en llinia, puedes atopar maneres de derrotar bloqueos d'IP pa blancos seguros. Si yes bon@ organizando comunidaes, quiciabes puedas xuntar a dalguna xente alredor d'un oxetivu. Ye útil saber dalgo de programación, anque solo seya pa mantener una bona seguridá operativa a lo llargo d'esti procesu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "¿Cuánto tiempu tienes pa esto? El nuesu conseyu sería empezar pequeñu y facer proyectos más grandes a medida que-yos cojes el tranquillu, pero pue llegar a consumir tolo tiempu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "¿Cuál sería un área de gran impactu pa centrate? Si vas a pasar X hores en archivu pirata, ¿cómo puedes consiguir el mayor \"rendimientu pol to esfuerciu\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "¿Cuáles son les maneres úniques en que pienses sobre esto? Podrías tener idees o enfoques interesantes que otres persones podríen haber pasáu per altu."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Nel nuesu casu, importábanos en particular la preservación a llargu plazu de la ciencia. Sabíemos de Library Genesis, y de cómo se espeyó completamente munches vegaes usando torrents. Encantábanos esa idea. Entós un día, ún de nós intentó atopar dalgún llibru de testu científicu en Library Genesis, pero nun pudo atopalos, poniendo en duda cuán completa yera realmente. Entós buscamos esos llibros de testu en llinia, y atopámoslos n'otros llugares, lo que plantó la semiente pal nuesu proyectu. Incluso enantes de saber sobre la Z-Library, teníemos la idea de nun intentar coleccionar toos esos llibros manualmente, sinón centranos en espeyar colecciones existentes, y contribuyir con elles de vuelta a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Seleición de blancu"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Entós, tenemos la nuestra área que tamos mirando, ¿agora qué colección específica vamos a espeyar? Hai dalgunes coses que faen que seya un bon oxetivu:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Única: que nun tea yá bien cubierta por otros proyectos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accesible: que nun use montones de capes de protección pa evitar que rasques la so metadata y datos."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Perspectiva especial: tienes dalguna información especial sobre esti oxetivu, como que d'alguna manera tienes accesu especial a esta colección, o descubriste cómo vencer les sos defenses. Esto nun ye necesario (el nuesu próximu proyectu nun fai nada especial), pero ciertamente ayuda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Cuando atopamos los nuesos llibros de ciencia en sitios web distintos a Library Genesis, intentamos averiguar cómo llegaron a internet. Dempués atopamos la Z-Library, y dimosnos cuenta de que, anque la mayoría de los llibros nun apaecen primero ellí, al final acaben ellí. Aprendimos sobre la so relación con Library Genesis, y la estructura d'incentivos (financieros) y la interfaz d'usuariu superior, que faen que seya una colección muncho más completa. Dempués fiximos dalguna rasca de metadata y datos preliminar, y dimosnos cuenta de que podíemos sortear los sos llímites de descarga de IP, aprovechando l'accesu especial d'unu de los nuesos miembros a munchos servidores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "A medida que exploras distintos oxetivos, yá ye importante esconder les traces usando VPNs y direiciones de corréu desechables, de lo que falaremos más alantre."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Rasca de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Vamos ponenos un poco más técnicos equí. Pa rascar la metadata de sitios web, mantuvimos les coses bastante simples. Usamos scripts de Python, a vegaes curl, y una base de datos MySQL pa guardar los resultaos. Nun usamos nengún software de rasca sofisticáu que pueda mapear sitios web complejos, yá que hasta agora namái necesitamos rascar unu o dos tipos de páxines enumerando los IDs y analizando l'HTML. Si nun hai páxines fácilmente enumerables, entós podríes necesitar un rastreador propiu que intente atopar toles páxines."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Enantes de que empieces a rascar un sitiu web enteru, intenta facelo manualmente un poco. Recorre unes decenes de páxines tú mesmu, pa tener una idea de cómo funciona. A vegaes yá te toparás con bloqueos de IP u otru comportamientu interesante d'esta manera. Lo mesmo val pa la rasca de datos: enantes de metete mui a fondo nesti oxetivu, asegúrate de que puedes descargar los sos datos de manera efectiva."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Pa sortear les restricciones, hai dalgunes coses que puedes intentar. ¿Hai otres direiciones IP o servidores que aloxen los mesmos datos pero que nun tengan les mesmes restricciones? ¿Hai dalgún endpoint de API que nun tenga restricciones, mientres que otros sí? ¿A qué velocidá de descarga bloquéase la to IP, y por cuánto tiempu? ¿O nun te bloqueen pero te ralenticen? ¿Qué pasa si crees una cuenta d'usuariu, cómo cambien les coses entós? ¿Puedes usar HTTP/2 pa mantener les conexones abiertes, y eso aumenta la velocidá a la que puedes solicitar páxines? ¿Hai páxines que listan múltiples ficheros a la vez, y la información llistada ellí ye suficiente?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Coses que probablemente quieras guardar inclúin:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Títulu"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome del ficheru / ubicación"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: puede ser dalgún ID internu, pero IDs como ISBN o DOI tamién son útiles."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Tamañu: pa calcular cuánto espaciu en discu necesitas."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): pa confirmar que descargaste'l ficheru correutamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Fecha d'amestáu/modificáu: pa que puedas volver más tarde y descargar ficheros que nun descargaste enantes (anque tamién puedes usar l'ID o hash pa esto)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descripción, categoría, etiquetes, autores, idioma, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Normalmente facemos esto en dos etapes. Primero descargamos los ficheros HTML en bruto, xeneralmente direutamente en MySQL (pa evitar munchos ficheros pequeños, de lo que falamos más abaxo). Dempués, nun pasu separáu, revisamos esos ficheros HTML y analizámolos en tables MySQL reales. D'esta manera nun tienes que volver a descargar too dende cero si descubres un error nel to códigu d'análisis, yá que puedes volver a procesar los ficheros HTML col códigu nuevu. Tamién suel ser más fácil paralelizar el pasu de procesamientu, aforrando asina tiempu (y puedes escribir el códigu de procesamientu mientres la rasca ta en marcha, en llugar de tener que escribir dambos pasos a la vez)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, tenga en cuenta que para algunos objetivos, la extracción de metadata es todo lo que hay. Existen algunas colecciones enormes de metadata que no están debidamente preservadas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selección de datos"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "A menudo puede usar la metadata para determinar un subconjunto razonable de datos para descargar. Incluso si eventualmente desea descargar todos los datos, puede ser útil priorizar los elementos más importantes primero, en caso de que sea detectado y se mejoren las defensas, o porque necesite comprar más discos, o simplemente porque surja algo más en su vida antes de poder descargar todo."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Por ejemplo, una colección podría tener múltiples ediciones del mismo recurso subyacente (como un libro o una película), donde una está marcada como de mejor calidad. Guardar esas ediciones primero tendría mucho sentido. Eventualmente podría querer guardar todas las ediciones, ya que en algunos casos la metadata podría estar etiquetada incorrectamente, o podría haber compensaciones desconocidas entre ediciones (por ejemplo, la \"mejor edición\" podría ser la mejor en la mayoría de los aspectos pero peor en otros, como una película con mayor resolución pero sin subtítulos)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "También puede buscar en su base de datos de metadata para encontrar cosas interesantes. ¿Cuál es el archivo más grande que se aloja y por qué es tan grande? ¿Cuál es el archivo más pequeño? ¿Existen patrones interesantes o inesperados en ciertas categorías, idiomas, etc.? ¿Hay títulos duplicados o muy similares? ¿Existen patrones sobre cuándo se agregaron los datos, como un día en el que se agregaron muchos archivos a la vez? A menudo puede aprender mucho observando el conjunto de datos de diferentes maneras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "En nuestro caso, deduplicamos los libros de Z-Library contra los hashes md5 en Library Genesis, ahorrando así mucho tiempo de descarga y espacio en disco. Sin embargo, esta es una situación bastante única. En la mayoría de los casos, no existen bases de datos completas de qué archivos ya están debidamente preservados por otros piratas. Esto en sí mismo es una gran oportunidad para alguien por ahí. Sería genial tener una visión general actualizada regularmente de cosas como música y películas que ya están ampliamente compartidas en sitios web de torrents, y por lo tanto son de menor prioridad para incluir en espejos piratas."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extracción de datos"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Ahora está listo para descargar realmente los datos en masa. Como se mencionó antes, en este punto ya debería haber descargado manualmente un montón de archivos, para comprender mejor el comportamiento y las restricciones del objetivo. Sin embargo, todavía habrá sorpresas para usted una vez que realmente comience a descargar muchos archivos a la vez."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nuestro consejo aquí es principalmente mantenerlo simple. Comience simplemente descargando un montón de archivos. Puede usar Python y luego expandirse a múltiples hilos. Pero a veces, incluso más simple es generar archivos Bash directamente desde la base de datos y luego ejecutar varios de ellos en múltiples ventanas de terminal para escalar. Un truco técnico rápido que vale la pena mencionar aquí es usar OUTFILE en MySQL, que puede escribir en cualquier lugar si desactiva \"secure_file_priv\" en mysqld.cnf (y asegúrese de también desactivar/anular AppArmor si está en Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Almacenamos los datos en discos duros simples. Comience con lo que tenga y expanda lentamente. Puede ser abrumador pensar en almacenar cientos de TB de datos. Si esa es la situación que enfrenta, simplemente publique un buen subconjunto primero, y en su anuncio pida ayuda para almacenar el resto. Si desea obtener más discos duros usted mismo, entonces r/DataHoarder tiene algunos buenos recursos para obtener buenas ofertas."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Trate de no preocuparse demasiado por sistemas de archivos sofisticados. Es fácil caer en el agujero del conejo al configurar cosas como ZFS. Un detalle técnico a tener en cuenta, sin embargo, es que muchos sistemas de archivos no manejan bien muchos archivos. Hemos encontrado que una solución simple es crear múltiples directorios, por ejemplo, para diferentes rangos de ID o prefijos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Después de descargar los datos, asegúrese de verificar la integridad de los archivos usando hashes en la metadata, si está disponible."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribución"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Tiene los datos, dándole así posesión del primer espejo pirata del mundo de su objetivo (muy probablemente). En muchos sentidos, la parte más difícil ha terminado, pero la parte más arriesgada aún está por delante. Después de todo, hasta ahora ha sido sigiloso; volando bajo el radar. Todo lo que tenía que hacer era usar un buen VPN en todo momento, no llenar sus datos personales en ningún formulario (obvio), y quizás usar una sesión de navegador especial (o incluso una computadora diferente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Ahora tiene que distribuir los datos. En nuestro caso, primero queríamos contribuir los libros de nuevo a Library Genesis, pero rápidamente descubrimos las dificultades en eso (clasificación de ficción vs no ficción). Así que decidimos distribuir usando torrents al estilo de Library Genesis. Si tiene la oportunidad de contribuir a un proyecto existente, eso podría ahorrarle mucho tiempo. Sin embargo, actualmente no hay muchos espejos piratas bien organizados."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Así que digamos que decide distribuir torrents usted mismo. Trate de mantener esos archivos pequeños, para que sean fáciles de reflejar en otros sitios web. Luego tendrá que sembrar los torrents usted mismo, mientras sigue siendo anónimo. Puede usar un VPN (con o sin reenvío de puertos), o pagar con Bitcoins mezclados por un Seedbox. Si no sabe qué significan algunos de esos términos, tendrá mucho que leer, ya que es importante que entienda los riesgos aquí."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Puede alojar los archivos torrent en sitios web de torrents existentes. En nuestro caso, elegimos alojar un sitio web, ya que también queríamos difundir nuestra filosofía de manera clara. Puede hacer esto usted mismo de manera similar (usamos Njalla para nuestros dominios y alojamiento, pagado con Bitcoins mezclados), pero también siéntase libre de contactarnos para que alojemos sus torrents. Estamos buscando construir un índice completo de espejos piratas con el tiempo, si esta idea se populariza."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "En cuanto a la selección de VPN, ya se ha escrito mucho sobre esto, así que solo repetiremos el consejo general de elegir por reputación. Políticas reales de no registro probadas en tribunales con largos historiales de protección de la privacidad es la opción de menor riesgo, en nuestra opinión. Tenga en cuenta que incluso cuando hace todo bien, nunca puede llegar a un riesgo cero. Por ejemplo, al sembrar sus torrents, un actor estatal altamente motivado probablemente pueda observar los flujos de datos entrantes y salientes de los servidores VPN, y deducir quién es usted. O simplemente puede cometer un error de alguna manera. Probablemente ya lo hemos hecho, y lo haremos de nuevo. Afortunadamente, a los estados nacionales no les importa <em>tanto</em> la piratería."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una decisión que debe tomar para cada proyecto es si publicarlo usando la misma identidad que antes o no. Si sigue usando el mismo nombre, entonces los errores en la seguridad operativa de proyectos anteriores podrían volver a morderlo. Pero publicar bajo diferentes nombres significa que no construye una reputación duradera. Elegimos tener una fuerte seguridad operativa desde el principio para poder seguir usando la misma identidad, pero no dudaremos en publicar bajo un nombre diferente si cometemos un error o si las circunstancias lo requieren."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Difundir la palabra puede ser complicado. Como dijimos, esta sigue siendo una comunidad de nicho. Originalmente publicamos en Reddit, pero realmente ganamos tracción en Hacker News. Por ahora, nuestra recomendación es publicarlo en algunos lugares y ver qué sucede. Y nuevamente, contáctenos. Nos encantaría difundir la palabra sobre más esfuerzos de archivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Esperamos que esto sea útil para los archivistas piratas que están comenzando. Estamos emocionados de daros la bienvenida a este mundo, así que no dudéis en poneros en contacto. Vamos a preservar tanto conocimiento y cultura del mundo como podamos, y a replicarlo por todas partes."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentamos el Espejo de la Biblioteca Pirata: Preservando 7TB de libros (que no están en Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Este proyecto (EDIT: trasladado a <a %(wikipedia_annas_archive)s>El Archivu d’Anna</a>) pretende contribuir a la preservación y liberación del conocimiento humano. Hacemos nuestra pequeña y humilde contribución, siguiendo los pasos de los grandes que nos precedieron."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "El enfoque de este proyecto se ilustra con su nombre:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Deliberadamente violamos la ley de derechos de autor en la mayoría de los países. Esto nos permite hacer algo que las entidades legales no pueden hacer: asegurarnos de que los libros se repliquen por todas partes."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Como la mayoría de las bibliotecas, nos centramos principalmente en materiales escritos como libros. Podríamos expandirnos a otros tipos de medios en el futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Espejo</strong> - Somos estrictamente un espejo de bibliotecas existentes. Nos centramos en la preservación, no en hacer que los libros sean fácilmente buscables y descargables (acceso) o en fomentar una gran comunidad de personas que contribuyan con nuevos libros (fuente)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La primera biblioteca que hemos replicado es Z-Library. Esta es una biblioteca popular (e ilegal). Han tomado la colección de Library Genesis y la han hecho fácilmente buscable. Además, se han vuelto muy efectivos en solicitar nuevas contribuciones de libros, incentivando a los usuarios contribuyentes con varios beneficios. Actualmente no contribuyen con estos nuevos libros de vuelta a Library Genesis. Y a diferencia de Library Genesis, no hacen que su colección sea fácilmente replicable, lo que impide una amplia preservación. Esto es importante para su modelo de negocio, ya que cobran dinero por acceder a su colección en masa (más de 10 libros por día)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "No hacemos juicios morales sobre cobrar dinero por el acceso en masa a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Nos gustaría invitarte a ayudar a preservar y liberar el conocimiento humano descargando y compartiendo nuestros torrents. Consulta la página del proyecto para obtener más información sobre cómo están organizados los datos."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "También nos gustaría mucho invitarte a contribuir con tus ideas sobre qué colecciones replicar a continuación y cómo hacerlo. Juntos podemos lograr mucho. Esta es solo una pequeña contribución entre muchas otras. Gracias, por todo lo que haces."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>No enlazamos a los archivos desde este blog. Por favor, encuéntralos tú mismo.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Volcado de ISBNdb, o ¿Cuántos libros se preservan para siempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si deduplicáramos adecuadamente los archivos de las bibliotecas en la sombra, ¿qué porcentaje de todos los libros del mundo hemos preservado?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Con el Espejo de la Biblioteca Pirata (EDIT: trasladado a <a %(wikipedia_annas_archive)s>El Archivu d’Anna</a>), nuestro objetivo es tomar todos los libros del mundo y preservarlos para siempre.<sup>1</sup> Entre nuestros torrents de Z-Library y los torrents originales de Library Genesis, tenemos 11,783,153 archivos. Pero, ¿cuántos son realmente? Si deduplicáramos adecuadamente esos archivos, ¿qué porcentaje de todos los libros del mundo hemos preservado? Realmente nos gustaría tener algo como esto:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of del patrimonio escrito de la humanidad preservado para siempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Para un porcentaje, necesitamos un denominador: el número total de libros publicados.<sup>2</sup> Antes de la desaparición de Google Books, un ingeniero del proyecto, Leonid Taycher, <a %(booksearch_blogspot)s>intentó estimar</a> este número. Llegó — en tono de broma — a 129,864,880 (“al menos hasta el domingo”). Estimó este número construyendo una base de datos unificada de todos los libros del mundo. Para esto, reunió diferentes Datasets y luego los fusionó de varias maneras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como un apunte rápido, hai otra persona que intentó catalogar tolos llibros del mundu: Aaron Swartz, el fináu activista dixital y cofundador de Reddit.<sup>3</sup> Él <a %(youtube)s>entamó Open Library</a> col oxetivu de “una páxina web pa cada llibru que se publicó”, combinando datos de munches fontes distintes. Acabó pagando el preciu últimu pol so trabayu de preservación dixital cuando foi procesáu por descargar masivamente artículos académicos, lo que llevó al so suicidiu. Nun fai falta dicir que esta ye una de les razones poles que el nuesu grupu ye seudónimu, y por qué tamos siendo mui cuidaos. Open Library sigue funcionando heroicamente gracies a la xente del Internet Archive, continuando col legáu d'Aaron. Volveremos a esto más tarde nesti artículu."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Nel artículu del blog de Google, Taycher describe dalgunos de los retos al estimar esti númberu. Primero, ¿qué constitúi un llibru? Hai delles definiciones posibles:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copias físiques.</strong> Obviamente esto nun ye mui útil, yá que namái son duplicáos del mesmu material. Sería guapo si pudiéramos preservar toles anotaciones que la xente fai nos llibros, como los famosos “garabatos nos márgenes” de Fermat. Pero, por desgracia, eso quedará como un suañu d'archiveru."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obres”.</strong> Por exemplu, “Harry Potter y la Cámara Secreta” como un conceutu lóxicu, abarcando toles versiones d'ello, como traducciones y reimpresiones distintes. Esta ye una definición un poco útil, pero pue ser difícil trazar la llinia de lo que cuenta. Por exemplu, probablemente querramos preservar traducciones distintes, anque les reimpresiones con namái diferencies menores quiciabes nun sean tan importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Ediciones”.</strong> Equí cuentas cada versión única d'un llibru. Si dalgo sobre él ye diferente, como una cubierta distinta o un prólogu diferente, cuenta como una edición distinta."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Archivos.</strong> Cuando se trabaya con biblioteques en sombra como Library Genesis, Sci-Hub o Z-Library, hai una consideración adicional. Pue haber múltiples escaneos de la mesma edición. Y la xente pue facer meyores versiones de los archivos esistentes, escaneando el testu usando OCR, o corrigiendo páxines que se escanearon en ángulu. Queremos contar estos archivos como una sola edición, lo que requeriría bon metadata, o deduplicación usando midíes de similitud de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Les “Ediciones” paecen la definición más práctica de lo que son los “llibros”. Convenientemente, esta definición tamién se usa pa asignar númberos ISBN únicos. Un ISBN, o Númberu Internacional Estandarizáu de Llibru, úsase comunmente pa comerciu internacional, yá que ta integráu col sistema internacional de códigu de barres (“Númberu Internacional d'Artículu”). Si quies vender un llibru en tiendes, necesita un códigu de barres, asina que consigues un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "L'artículu del blog de Taycher menciona que anque los ISBN son útiles, nun son universales, yá que namái se adoptaron realmente a mediaos de los setenta, y non en tolos sitios del mundu. Sicasí, l'ISBN ye probablemente el identificador más utilizáu d'ediciones de llibros, asina que ye'l nuesu meyor puntu de partida. Si podemos atopar tolos ISBN del mundu, conseguimos una llista útil de qué llibros entá necesiten ser preservaos."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Entós, ¿dónde conseguimos los datos? Hai una serie d'esfuerzos esistentes que tán intentando compilar una llista de tolos llibros del mundu:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Al fin y al cabu, fixeron esta investigación pa Google Books. Sicasí, el so metadata nun ye accesible en masa y ye bastante difícil de rascar."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como se mencionó enantes, esta ye la so misión entera. Consiguieron enormes cantidaes de datos de biblioteques de biblioteques cooperantes y archivos nacionales, y sigan faciéndolo. Tamién tienen bibliotecarios voluntarios y un equipu técnicu que tán intentando deduplicar rexistros, y etiquetalos con tolos tipos de metadata. Lo meyor de too, el so dataset ye completamente abiertu. Pue simplemente <a %(openlibrary)s>descargalu</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Esta ye una páxina web xestionada pola organización ensin ánimu de lucre OCLC, que vende sistemas de xestión de biblioteques. Agreguen metadata de llibros de munches biblioteques, y faenlu disponible a través de la páxina web de WorldCat. Sicasí, tamién faen dineru vendiendo estos datos, asina que nun tán disponibles pa descarga masiva. Tienen dalgunos datasets masivos más llimitaos disponibles pa descarga, en cooperación con biblioteques específiques."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este ye'l tema d'esti artículu del blog. ISBNdb rasca delles páxines web pa metadata de llibros, en particular datos de precios, que depués venden a vendedores de llibros, pa que puedan fixar los precios de los sos llibros en consonancia col restu del mercáu. Dende que los ISBN son bastante universales anguaño, efectivamente construyeron una “páxina web pa cada llibru”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Diferentes sistemas de biblioteques individuales y archivos.</strong> Hai biblioteques y archivos que nun fueron indexaos y agregaos por nengún de los anteriores, a menudo porque tán infradotaos, o por otres razones nun quieren compartir los sos datos con Open Library, OCLC, Google, y asina. Munchos d'estos tienen rexistros dixitales accesibles a través d'internet, y a menudo nun tán mui bien protexíos, asina que si quies ayudar y divertite aprendiendo sobre sistemas de biblioteques raros, estos son grandes puntos de partida."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Nesti artículu, tamos contentos d'anunciar un pequeñu llanzamientu (comparáu colos nuesos llanzamientos anteriores de Z-Library). Rascamos la mayoría de ISBNdb, y fiximos los datos disponibles pa torrenting na páxina web del Pirate Library Mirror (EDIT: movíu a <a %(wikipedia_annas_archive)s>l'Archivu d'Anna</a>; nun vamos enlazalu equí direutamente, namái búscalu). Estos son unos 30.9 millones de rexistros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4.4GB comprimíos). Na so páxina web afirmen que en realidá tienen 32.6 millones de rexistros, asina que quiciabes de dalguna manera nos perdimos dalgunos, o <em>ellos</em> podíen tar faciendo dalgo mal. En cualquier casu, por agora nun vamos compartir esactamente cómo lo fiximos — dexaremos eso como un exerciciu pal llector. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Lo que vamos compartir ye dalgún análisis preliminar, pa intentar acercanos a estimar el númberu de llibros nel mundu. Miramos tres datasets: esti nuevu dataset de ISBNdb, el nuesu llanzamientu orixinal de metadata que rascamos de la biblioteca en sombra Z-Library (que inclúi Library Genesis), y el volcado de datos de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Empecemos con dalgunes cifres aproximáes:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "En dambes Z-Library/Libgen y Open Library hai munchos más llibros que ISBN únicos. ¿Significa eso que munchos d'esos llibros nun tienen ISBN, o ye que'l metadata del ISBN simplemente falta? Probablemente podamos responder a esta pregunta con una combinación d'emparejamiento automatizáu basáu n'otros atributos (títulu, autor, editor, etc.), incorporando más fontes de datos, y estraendo ISBN de los propios escaneos de llibros (nel casu de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "¿Cuántos d'esos ISBN son únicos? Esto ilústrase meyor con un diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Pa ser más precisos:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "¡Sorprendiónos lo pocu que se solapen! ISBNdb tien una cantidá enorme d'ISBNs que nun apaecen nin en Z-Library nin en Open Library, y lo mesmo pasa (nun grau menor pero entá sustancial) coles otres dos. Esto xenera munches preguntes nueves. ¿Cuánto ayudaría l'apareyamientu automatizáu a etiquetar los llibros que nun teníen ISBNs? ¿Habría munches coincidencies y, polo tanto, un mayor solape? Tamién, ¿qué pasaría si incorporamos un cuartu o quintu dataset? ¿Cuánto solape veríemos entós?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Esto danos un puntu de partida. Agora podemos ver tolos ISBNs que nun taben nel dataset de Z-Library, y que tampocu coinciden colos campos de títulu/autor. Esto pue ayudanos a conservar tolos llibros del mundu: primero rascando internet pa escanear, y llueu saliendo a la vida real pa escanear llibros. Esto postreru podría inclusive financiarse colectivamente, o impulsase por \"recompenses\" de xente que quier ver ciertos llibros dixitalizaos. Toa esa ye una historia pa otru momentu."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si quies ayudar con dalgo d'esto — más análisis; rascar más metadata; atopar más llibros; facer OCR de llibros; facer esto pa otros dominos (p. ex. artículos, audiollibros, películes, series de televisión, revistes) o inclusive facer dalgunos d'estos datos disponibles pa coses como entrenamientu de modelos de llingua granes — por favor, contáctame (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si t'interesa específicamente l'análisis de datos, tamos trabayando en facer que los nuesos datasets y scripts tán disponibles nun formatu más fácil d'usar. Sería estupendu si pudieres forkar un notebook y empezar a xugar con esto."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, si quies sofitar esti trabayu, por favor considera facer una donación. Esta ye una operación llevada enteramente por voluntarios, y la to contribución marca una gran diferencia. Cada poco ayuda. Por agora aceptamos donaciones en criptomoneda; consulta la páxina de Donar en l'Archivu d'Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pa dalguna definición razonable de \"siempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Por supuestu, el patrimoniu escritu de la humanidá ye muncho más que llibros, especialmente anguaño. Pa los fines d'esti artículu y los nuesos llançamientos recientes tamos enfocándonos en llibros, pero los nuesos intereses estiéndense más allá."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Hai muncho más que se pue dicir sobre Aaron Swartz, pero namás quiximos mencioná-y brevemente, yá que xuega un papel crucial nesta historia. A midida que pasa'l tiempu, más xente podría atopar el so nome por primer vegada, y puen sumergise nel so mundu por cuenta propia."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La ventana crítica de les biblioteques de sombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "¿Cómo podemos afirmar que vamos a conservar les nueses coleiciones pa siempre, cuando yá tán llegando a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versión en chinés 中文版</a>, discuti en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "En l'Archivu d'Anna, a menudo pregúntennos cómo podemos afirmar que vamos a conservar les nueses coleiciones pa siempre, cuando el tamañu total yá ta llegando a 1 Petabyte (1000 TB), y sigue creciendo. Nesti artículu vamos ver la nuesa filosofía, y ver por qué la próxima década ye crítica pa la nuesa misión de conservar el conocimientu y la cultura de la humanidá."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "El <a %(annas_archive_stats)s>tamañu total</a> de les nueses coleiciones, nos últimos meses, desglosáu por númberu de semilleros de torrents."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioridaes"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "¿Por qué nos importa tanto los artículos y los llibros? Deixemos de llau la nuesa creencia fundamental na conservación en xeneral — podríamos escribir otru artículu sobre eso. Entós, ¿por qué artículos y llibros específicamente? La respuesta ye sencilla: <strong>densidá d'información</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabyte d'almacenamientu, el testu escritu almacena la mayor cantidá d'información de tolos medios. A pesar de que nos importa tanto'l conocimientu como la cultura, nos importa más el primero. En xeneral, atopamos una xerarquía de densidá d'información y importancia de la conservación que s'aseya más o menos asina:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Artículos académicos, revistes, informes"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Datos orgánicos como secuencies de ADN, semientes de plantes o muestras microbianes"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Llibros de non-ficción"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Software de ciencia y inxeniería"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Datos de midida como midiciones científiques, datos económicos, informes empresariales"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sitios web de ciencia y inxeniería, discusiones en llinia"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistes de non-ficción, periódicos, manuales"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcripciones de charles, documentales, podcasts de non-ficción"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Datos internos de corporaciones o gobiernos (filtraos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Rexistros de metadata xeneralmente (de non-ficción y ficción; d'otros medios, arte, persones, etc.; incluyíes reseñes)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Datos xeográficos (p. ex. mapas, encuestes xeolóxiques)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripciones de procedimientos llexales o xudiciales"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versiones ficticies o d'entretenimientu de tolo anterior"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La clasificación nesta llista ye daqué arbitraria — dellos elementos tán empataos o hai desacuerdos dientro del nuesu equipu — y probablemente tamos escaeciendo dalgunes categoríes importantes. Pero esto ye más o menos cómo priorizamos."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Dalgunes d'estes categoríes son demasiado distintes de les demás como pa que nos preocupemos (o yá tán atendíes por otres instituciones), como los datos orgánicos o los datos xeográficos. Pero la mayoría de los elementos d'esta llista son realmente importantes pa nós."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Otro factor importante na nuesa priorización ye cuán en riesgu ta una obra determinada. Preferimos centranos en obres que son:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rares"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Únicamente desatendíes"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Únicamente en riesgu de destrucción (p. ex. por guerra, recortes de financiación, demandes, o persecución política)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, importanos l'escala. Tenemos tiempu y dineru llindaos, asina que preferimos pasar un mes salvando 10,000 llibros que 1,000 llibros — si son más o menos igual de valiosos y en riesgu."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteques en sombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Hai munches organizaciones que tienen misiones asemeyaes, y prioridaes asemeyaes. De fechu, hai biblioteques, archivos, llaboratorios, museos y otres instituciones encargaes de la preservación d'esti tipu. Munches d'elles tán bien financiaes, por gobiernos, individuos o corporaciones. Pero tienen un gran puntu cegu: el sistema llegal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Equí ye onde xuega un papel únicu les biblioteques en sombra, y la razón pola que esiste l'Archivu d'Anna. Podemos facer coses que otres instituciones nun tán autorizaes a facer. Agora, nun ye (a menudo) que podamos archivar materiales que son ilegales de preservar n'otros llugares. Non, ye llegal en munchos sitios construyir un archivu con cualesquier llibros, papeles, revistes, y asina."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Pero lo que a menudo falta nos archivos llexales ye la <strong>redundancia y la llonxevidá</strong>. Hai llibros de los que namái esiste una copia nuna biblioteca física en dalgún llau. Hai rexistros de metadata protexíos por una única empresa. Hai periódicos que namái se caltienen en microfilme nun únicu archivu. Les biblioteques pueden recibir recortes de financiación, les empreses pueden quebrar, los archivos pueden ser bombardeaos y quemáos hasta los cimientos. Esto nun ye hipóteticu — esto pasa tolos díes."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Lo que podemos facer de manera única en l'Archivu d'Anna ye almacenar munches copies d'obres, a gran escala. Podemos coleccionar artículos, llibros, revistes y más, y distribuyilos en gran cantidá. Actualmente facémoslo a través de torrents, pero les teunoloxíes exactes nun importen y cambiarán col tiempu. La parte importante ye consiguir que munches copies se distribuyan per tol mundu. Esta cita de fai más de 200 años sigue siendo válida:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Lo perdíu nun se pue recuperar; pero salvemos lo que queda: non con bóvedes y cerradures que los aparten de la vista y usu del públicu, condenándolos al desagüe del tiempu, sinón con una multiplicación de copies, que los ponga fuera del alcance del accidente.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Una nota rápida sobre'l dominiu públicu. Dende l'Archivu d'Anna centrámonos de manera única en actividaes que son ilegales en munchos llugares del mundu, nun nos molestamos con colecciones amplamente disponibles, como los llibros de dominiu públicu. Les entidaes llexales a menudo yá cuiden bien d'ello. Sicasí, hai consideraciones que nos faen trabayar a vegaes en colecciones públicamente disponibles:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Los rexistros de metadata pueden vese llibremente na páxina web de Worldcat, pero nun pueden descargase en gran cantidá (hasta que los <a %(worldcat_scrape)s>raspamos</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "El códigu puede ser de fonte abierta en Github, pero Github nel so conxuntu nun pue ser fácil de reflejar y, polo tanto, caltener (anque nesti casu particular hai copies suficientemente distribuyíes de la mayoría de los repositorios de códigu)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit ye de baldre d'usar, pero recién implantó midíes estrictes anti-raspáu, a raíz de la fame de datos pa l'entrenamientu de LLM (más sobre eso más alantre)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Una multiplicación de copies"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Volviendo a la nuestra pregunta orixinal: ¿cómo podemos afirmar que caltenemos les nuestres colecciones de manera perpetua? El principal problema equí ye que la nuestra colección vien <a %(torrents_stats)s>creciendo</a> a un ritmu rápidu, al raspar y abrir dalgunes colecciones masives (además del trabayu increíble que yá faen otres biblioteques de datos abiertos como Sci-Hub y Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Esta crecedera de datos fae más difícil que les colecciones se reflejen al rodiu del mundu. ¡El almacenamientu de datos ye caro! Pero somos optimistes, especialmente al observar los siguientes trés trends."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Recoyimos lo más fácil"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Esto sigue direutamente de les nuestres prioridaes discutíes enriba. Preferimos trabayar en lliberar grandes colecciones primero. Agora que aseguramos delles de les mayores colecciones del mundu, esperamos que la nuestra crecedera seya muncho más lenta."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Entá queda una llarga cola de colecciones más pequeñes, y nuevos llibros escanénse o publíquense tolos díes, pero el ritmu probablemente seya muncho más lentu. Podríamos entá doblar o incluso triplicar en tamañu, pero a lo llargo d'un periodu de tiempu más llargu."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Los costes d'almacenamientu sigan cayendo de manera exponencial"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "A la fecha d'escritura, los <a %(diskprices)s>precios de los discos</a> por TB tán alredor de $12 pa discos nuevos, $8 pa discos usaos, y $4 pa cinta. Si somos conservadores y miramos namái a discos nuevos, eso significa que almacenar un petabyte cuesta alredor de $12,000. Si asumimos que la nuestra biblioteca va triplicar de 900TB a 2.7PB, eso significaría $32,400 pa reflejar la nuestra biblioteca entera. Añadiendo electricidad, coste d'otru hardware, y demás, redondeémoslo a $40,000. O con cinta más como $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Por un llau <strong>$15,000–$40,000 pola suma de tola conocencia humana ye una ganga</strong>. Por otru llau, ye un pocu empináu esperar montones de copies completes, especialmente si tamién quixéramos que esa xente siga sembrando los sos torrents en beneficiu d'otros."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Eso ye güei. Pero'l progresu avanza:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Los costes de los discos duros por TB reduciéronse a un terciu nos últimos 10 años, y probablemente sigan cayendo a un ritmu similar. La cinta paez tar nun trayeutoria similar. Los precios de los SSD tán cayendo incluso más rápido, y podrían superar los precios de los HDD a finales de la década."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendencies de precios de HDD de diferentes fontes (fai clic pa ver l'estudiu)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si esto se cumple, entós en 10 años podríamos tar viendo namái $5,000–$13,000 pa reflejar la nuestra colección entera (1/3), o incluso menos si crecemos menos en tamañu. Anque sigue siendo muncho dineru, esto va ser alcanzable pa munches persones. Y podría ser incluso meyor por mor del siguiente puntu…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Meyores na densidá d'información"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Anguaño almacenamos los llibros nos formatos en bruto que nos dan. Claro, tán comprimiíos, pero davezu siguen siendo escaneos o semeyes de páxines de gran tamañu."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Hasta agora, les úniques opciones pa menguar el tamañu total de la nuesa colección yeren una compresión más agresiva o la deduplicación. Sicasí, pa llograr un aforru significativu, dambes son demasiado destructives pa nós. La compresión fuerte de semeyes pue facer el testu apenas lleíble. Y la deduplicación requiere una gran confianza en que los llibros son exactamente iguales, lo cual ye davezu inexactu, especialmente si los conteníos son los mesmos pero los escaneos se ficieron en momentos distintos."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Siempre hubo una tercer opción, pero la so calidá foi tan abismal que nunca la consideramos: <strong>OCR, o Recoñecimientu Ópticu de Carauteres</strong>. Esti ye'l procesu de convertir semeyes en testu llanu, usando IA pa detectar los carauteres nes semeyes. Les ferramientes pa esto esistieron dende va tiempu, y fueron bastante decentes, pero \"bastante decente\" nun ye abondo pa fines de preservación."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Sicasí, los modelos recientes d'aprendizaxe fonda multimodal fixeron un progresu estremáu y rapidu, anque con costes altos. Esperamos que tanto la precisión como los costes meyoren dramáticamente nos años vinientes, hasta'l puntu en que va ser realista aplicalu a tola nuesa biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Meyores en OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Cuando eso pase, probablemente sigamos preservando los ficheros orixinales, pero amás podríemos tener una versión muncho más pequeña de la nuesa biblioteca que la mayoría de la xente querrá espeyar. Lo interesante ye que'l testu en bruto en sí mesmu comprímese incluso meyor, y ye muncho más fácil de deduplicar, dándonos más aforros."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "En xeneral, nun ye irrealista esperar al menos una reducción de 5-10 veces nel tamañu total de ficheros, quiciabes incluso más. Incluso con una reducción conservadora de 5 veces, taríemos falando de <strong>$1,000–$3,000 en 10 años incluso si la nuesa biblioteca triplica'l so tamañu</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Ventana crítica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si estes previsiones son aciertes, namái <strong>necesitamos esperar un par d'años</strong> enantes de que tola nuesa colección seya amplamente espeyada. Asina, como dixo Thomas Jefferson, \"colocada más allá del alcance del accidente\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Desgraciadamente, l'aparición de LLMs, y el so entrenamientu famentu de datos, puso a munchos titulares de drechos d'autor a la defensiva. Incluso más de lo que yá taben. Munchos sitios web tán faciendo más difícil rascar y archivar, les demandes tán volando, y mientres tanto les biblioteques y archivos físicos sigan siendo descuidaos."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Solo podemos esperar que estes tendencias sigan empeorando, y que munches obres se pierdan muncho enantes de que entren nel dominiu públicu."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Tamos a les puertes d'una revolución na preservación, pero <q>lo perdíu nun se pue recuperar.</q></strong> Tenemos una ventana crítica d'unos 5-10 años na que inda ye bastante caro operar una biblioteca en sombres y crear munchos espeyos al rodiu del mundu, y na que l'accesu inda nun se zarró completamente."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si podemos salvar esta ventana, entós habremos preserváu efectivamente el conocimientu y la cultura de la humanidá pa siempre. Nun debemos dexar que esti tiempu se desperdicie. Nun debemos dexar que esta ventana crítica se nos cierre."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Vamos allá."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accesu esclusivu pa compañíes de LLM a la mayor colección de llibros de non-ficción china del mundu"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versión china 中文版</a>, <a %(news_ycombinator)s>Discutir en Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>En resume:</strong> L'Archivu d'Anna adquirió una colección única de 7.5 millones / 350TB de llibros de non-ficción chinos — más grande que Library Genesis. Tamos dispuestos a dar a una compañía de LLM accesu esclusivu, a cambéu d'un OCR y extracción de testu d'alta calidá.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Esti ye un post curtiu nel blog. Tamos buscando una compañía o institución que nos ayude con OCR y extracción de testu pa una colección masiva que adquirimos, a cambéu d'un accesu esclusivu tempranu. Dempués del periodu d'embargu, por supuestu, vamos llanzar la colección entera."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "El testu académicu de alta calidá ye estremedamente útil pa l'entrenamientu de LLM. Anque la nuesa coleición seya china, esto debería ser incluso útil pa entrenar LLM en inglés: los modelos paecen codificar conceutos y conocimientu independientemente del idioma fonte."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Pa esto, el testu necesita ser estraíu de los escaneos. ¿Qué gana l'Archivu d'Anna con esto? Búsqueda de testu completu de los llibros pa los sos usuarios."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Porque los nuesos oxetivos s'alineen colos de los desarrolladores de LLM, tamos buscando un collaborador. Tamos dispuestos a dar-y <strong>accesu anticipáu esclusivu a esta coleición en bloque por 1 añu</strong>, si pue facer un OCR y estraición de testu correutos. Si ta dispuestu a compartir con nós el códigu completu de la so canalización, tamos dispuestos a embargar la coleición por más tiempu."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Páxines d'exemplu"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Pa demostramos que tienes una bona canalización, equí tienes delles páxines d'exemplu pa entamar, d'un llibru sobre superconductores. La to canalización debería manexar correutamente matemátiques, tables, gráficos, notes a pie de páxina, y asina."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Unvia les tos páxines procesaes al nuesu corréu electrónicu. Si tienen bona pinta, unviarémoste más en priváu, y esperamos que seyes capaz de ejecutar rápidamente la to canalización nelles tamién. Una vez que estemos satisfechos, podemos facer un tratu."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Colección"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Delles más información sobre la coleición. <a %(duxiu)s>Duxiu</a> ye una base de datos masiva de llibros escaneaos, creada pol <a %(chaoxing)s>SuperStar Digital Library Group</a>. La mayoría son llibros académicos, escaneaos pa facelos disponibles dixitalmente a universidaes y biblioteques. Pa la nuesa audiencia de fala inglesa, <a %(library_princeton)s>Princeton</a> y la <a %(guides_lib_uw)s>Universidá de Washington</a> tienen bones reseñes. Tamién hai un artículu escepcional que da más fondu: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (búscalu nel Archivu d'Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Los llibros de Duxiu llevanten tiempu pirateándose na internet china. Xeneralmente tán vendiéndose por menos d'un dólar por revendedores. Suélen distribuyise usando l'equivalente chinu de Google Drive, que davezu foi hackeáu pa permitir más espaciu d'almacenamientu. Dalgunos detalles técnicos puen atopase <a %(github_duty_machine)s>equí</a> y <a %(github_821_github_io)s>equí</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Anque los llibros distribuyéronse de manera semipública, ye bastante difícil consiguilos en bloque. Tuvimos esto alto na nuesa llista de coses por facer, y destinamos múltiples meses de trabayu a tiempu completu pa ello. Sicasí, recién un voluntariu increíble, asombrosu y talentosu contactó con nós, diciéndonos que yá fixera tol trabayu — a gran costu. Compartió con nós la coleición completa, ensin esperar nada a cambéu, salvo la garantía de la so preservación a llargu plazu. Verdaderamente remarcable. Acordaron pidir ayuda d'esta manera pa que la coleición seya OCR'ada."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La coleición tien 7.543.702 ficheros. Esto ye más que la non-ficción de Library Genesis (alredor de 5.3 millones). El tamañu total de los ficheros ye d'aproximadamente 359TB (326TiB) na so forma actual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Tamos abiert@s a otres propuestes y idees. Namái contacta con nós. Consulta l'Archivu d'Anna pa más información sobre les nueses coleiciones, esfuercios de preservación, y cómo pues ayudar. ¡Gracies!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avisu: esti artículu del blogue ta desactualizáu. Decidimos que IPFS inda nun ta preparáu pa la hora punta. Aún vamos a enllazar a ficheros en IPFS dende l'Archivu d'Anna cuando seya posible, pero nun lo vamos a aloxar nós mesmos más, nin recomendamos a otros que lo repliquen usando IPFS. Por favor, consulta la nuesa páxina de Torrents si quies ayudar a preservar la nuesa colección."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Ayuda a sembrar Z-Library en IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cómo executar una biblioteca sombra: operaciones nel Archivu d'Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nun hai <q>AWS pa charities en l'ombra,</q> entós ¿cómo xestionamos l'Archivu d'Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Yo xestiono <a %(wikipedia_annas_archive)s>l'Archivu d'Anna</a>, el motor de busca non-profit de códigu abiertu más grande del mundu pa <a %(wikipedia_shadow_library)s>biblioteques en l'ombra</a>, como Sci-Hub, Library Genesis, y Z-Library. El nuesu oxetivu ye facer el conocimientu y la cultura fácilmente accesibles, y en última instancia, construyir una comunidá de persones que xuntes archiven y preserven <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tolos llibros del mundu</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Nesti artículu amosaré cómo xestionamos esti sitiu web, y los retos únicos que vienen con operar un sitiu web con un estatus llegal cuestionable, yá que nun hai “AWS pa charities en l'ombra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Tamién consulta l'artículu hermanu <a %(blog_how_to_become_a_pirate_archivist)s>Cómo convertise en un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokens d'innovación"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Empecemos col nuesu conxuntu tecnolóxicu. Ye deliberadamente aburríu. Usamos Flask, MariaDB y ElasticSearch. Eso ye literalmente too. La búsqueda ye en gran parte un problema resueltu, y nun tamos interesaos en reinventalu. Amás, tenemos que gastar los nuesos <a %(mcfunley)s>tokens d'innovación</a> en otra cosa: nun ser desactivaos poles autoridaes."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Entós, ¿cuán legal o ilegal ye exactamente l'Archivu d'Anna? Esto depende principalmente de la xurisdicción llegal. La mayoría de los países creen en dalguna forma de drechu d'autor, lo que significa que a les persones o empreses se-yos asigna un monopoliu esclusivu sobre ciertos tipos d'obres por un periodu determináu de tiempu. Como un apunte, nel Archivu d'Anna creemos que, anque hai dalgunes ventaxes, en xeneral el drechu d'autor ye un netu-negativu pa la sociedá — pero esa ye una historia pa otru momentu."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Esti monopoliu esclusivu sobre ciertos trabayos significa que ye ilegal pa cualisquier persona fuera d'esti monopoliu distribuir direutamente esos trabayos — incluyéndonos a nós. Pero l'Archivu d'Anna ye un motor de busca que nun distribúi direutamente esos trabayos (al menos non nel nuesu sitiu web en clearnet), asina que deberíamos tar bien, ¿non? Non exactamente. En munches xurisdicciones nun solo ye ilegal distribuir trabayos con derechos d'autor, sinón tamién enlazar a llugares que lo faigan. Un exemplu clásicu d'esto ye la llei DMCA de los Estaos Xuníos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Esi ye l'extremu más estrictu del espectru. Nel otru estremu del espectru podría haber teóricamente países ensin lleis de drechu d'autor en absoluto, pero estos nun esisten realmente. Prácticamente tolos países tienen dalguna forma de llei de drechu d'autor nos llibros. La aplicación ye otra historia. Hai munchos países con gobiernos que nun se preocupen por aplicar la llei de drechu d'autor. Tamién hai países ente los dos extremos, que prohiben distribuir obres con drechu d'autor, pero nun prohiben enllazar a tales obres."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Otra consideración ye a nivel d'empresa. Si una empresa opera nuna xurisdicción que nun se preocupa pol drechu d'autor, pero la empresa en sí nun ta dispuesta a asumir nengún riesgu, entós podrían zarrar el to sitiu web tan pronto como dalguién se queixe d'ello."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, una gran consideración son los pagos. Dado que necesitamos permanecer anónimos, nun podemos usar métodos de pagu tradicionales. Esto déxanos coles criptomonedes, y namái un pequeñu subconxuntu d'empreses les sofiten (hai tarxetes de débito virtuales pagaes con cripto, pero a menudo nun se acepten)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arquitectura del sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Entós, vamos dicir que atopasti dalgunes empreses que tán dispuestes a aloxar el to sitiu web ensin cerrate — vamos llamales “proveedores amantes de la llibertá” 😄. Rápidamente vas a ver que aloxar too con ellos ye bastante caru, asina que quies atopar dalgún “proveedor baratu” y facer el aloxamientu real ellí, proxectando a través de los proveedores amantes de la llibertá. Si lo faes bien, los proveedores baratos nun van saber qué tas aloxando, y nun van recibir queixes."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con toos estos proveedores hai un riesgu de que te cierren de toes formes, asina que tamién necesites redundancia. Necesitamos esto en tolos niveles de la nuesa pila."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Una empresa un poco amante de la llibertá que se puso nuna posición interesante ye Cloudflare. Ellos <a %(blog_cloudflare)s>argumentaron</a> que nun son un proveedor d'aloxamientu, sinón una utilidá, como un ISP. Por tanto, nun tán suxetos a DMCA o otres solicitúes de retirada, y remiten cualesquier solicitúes al to proveedor d'aloxamientu real. Llegaron a tanto como pa dir a xuiciu pa protexer esta estructura. Podemos, por tanto, usalos como otra capa de caché y protección."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nun acepta pagos anónimos, asina que namái podemos usar el so plan gratuítu. Esto significa que nun podemos usar les sos carauterístiques d'equilibráu de cargues o failover. Poro, <a %(annas_archive_l255)s>implementamos esto nós mesmos</a> al nivel del dominiu. Al cargar la páxina, el navegador comprobará si'l dominiu actual sigue disponible, y si non, reescribe toles URL a un dominiu diferente. Dado que Cloudflare almacena en caché munches páxines, esto significa que un usuariu puede llegar al nuesu dominiu principal, incluso si'l servidor proxy ta caíu, y depués nel siguiente clic ser movíu a otru dominiu."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Tamién tenemos que tratar con preocupaciones operatives normales, como monitorizar la salú de los servidores, rexistrar errores del backend y frontend, y asina sucesivamente. La nuesa arquitectura de failover permite más robustez nesti aspectu tamién, por exemplu, executando un conxuntu completamente diferente de servidores nun de los dominios. Incluso podemos executar versiones más vieyes del códigu y datasets nesti dominiu separáu, en casu de que un error críticu na versión principal pase desapercibíu."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Tamién podemos cubrirnos contra Cloudflare volviéndose en contra de nós, eliminándolu d'un de los dominios, como esti dominiu separáu. Diferentes permutaciones d'estes idees son posibles."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ferramientes"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Veamos qué ferramientes usamos pa llograr too esto. Esto ta evolucionando muncho a medida que topamos con nuevos problemes y atopamos nueves soluciones."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidor d'aplicaciones: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Xestión de servidores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Desarrollu: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Aloxamientu estáticu Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Hai dalgunes decisiones que hemos revisáu una y otra vez. Una ye la comunicación ente servidores: solíemos usar Wireguard pa esto, pero descubrimos que de vez en cuando dexa de tresmitir datos, o namái tresmite datos nun sentíu. Esto pasó con delles configuraciones de Wireguard que probamos, como <a %(github_costela_wesher)s>wesher</a> y <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Tamién intentamos tunelar puertos sobre SSH, usando autossh y sshuttle, pero topamos con <a %(github_sshuttle)s>problemes ellí</a> (anque inda nun ta claro pa mí si autossh sufre de problemes de TCP-sobre-TCP o non — paréceme una solución chapucera, pero quiciabes ta bien en realidá?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "En vez d'eso, volvimos a les conexones direutes ente servidores, ocultando que un servidor ta executándose en proveedores baratos usando filtráu d'IP con UFW. Esto tien la desventaxa de que Docker nun funciona bien con UFW, a menos que uses <code>network_mode: \"host\"</code>. To esto ye un poco más propensu a errores, porque vas a esponer el to servidor a internet con solo una pequeña mala configuración. Quiciabes deberíemos volver a autossh — el feedback sería bien recibíu equí."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Tamién hemos ido y veníu con Varnish vs. Nginx. Actualmente gustános Varnish, pero tien les sos peculiaridaes y cantos aspres. Lo mesmo aplícase a Checkmk: nun nos encanta, pero funciona por agora. Weblate foi aceptable pero non increíble — a veces temo que va perder los mios datos cada vez que intente sincronizalu col nuesu repositoriu git. Flask foi bonu en xeneral, pero tien dalgunes peculiaridaes rares que costaron muncho tiempu de depurar, como configurar dominos personalizaos o problemes cola so integración de SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Hasta agora los otros ferramientes fueron estupendos: nun tenemos queixes serios sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker y Tor. Tolos estos tuvieron dalgún problema, pero nada demasiado seriu o que consuma muncho tiempu."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Foi una esperiencia interesante deprender a configurar un motor de busca de biblioteques en sombres robustu y resiliente. Hai munchos más detalles que compartir en publicaciones futures, asina que faime saber qué te gustaría deprender más."

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como siempre, tamos buscando donaciones pa sofitar esti trabayu, asina que asegúrate de visitar la páxina de Donativos en l'Archivu d'Anna. Tamién tamos buscando otros tipos de sofitu, como subvenciones, patrocinadores a llargu plazu, proveedores de pagos d'altu riesgu, quiciabes incluso anuncios (con gustu!). Y si quies contribuir col to tiempu y habilidaes, siempre tamos buscando desendolcadores, traductores, y demás. Gracies pol to interés y sofitu."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna y el equipu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hola, soy Anna. Creé <a %(wikipedia_annas_archive)s>l'Archivu d'Anna</a>, la biblioteca en sombres más grande del mundu. Esti ye'l mio blog personal, nel que yo y los mios compañeros escribimos sobre piratería, preservación dixital y más."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conéctate conmigo en <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Ten en cuenta que esti sitiu web ye solo un blog. Namás aloxamos les nueses palabres equí. Nun se aloxen nin s'enllacen torrents u otros ficheros con derechos d'autor equí."

#, fuzzy
msgid "blog.index.heading"
msgstr "Publicaciones del blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B Raspáu de WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Poniendo 5,998,794 llibros en IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avisu: esti artículu del blogue ta desactualizáu. Decidimos que IPFS inda nun ta preparáu pa la hora punta. Aún vamos a enllazar a ficheros en IPFS dende l'Archivu d'Anna cuando seya posible, pero nun lo vamos a aloxar nós mesmos más, nin recomendamos a otros que lo repliquen usando IPFS. Por favor, consulta la nuesa páxina de Torrents si quies ayudar a preservar la nuesa colección."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archivu d'Anna raspó tolo WorldCat (la mayor coleición de metadata de biblioteques del mundu) pa facer una llista de llibros que necesiten ser conservaos.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Hai un añu, <a %(blog)s>empezamos</a> a responder esta entruga: <strong>¿Qué porcentaxe de llibros fueron conservaos de manera permanente por biblioteques sombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Una vegada que un llibru llega a una biblioteca sombra de datos abiertos como <a %(wikipedia_library_genesis)s>Library Genesis</a>, y agora <a %(wikipedia_annas_archive)s>L'Archivu d'Anna</a>, espárcese por tol mundu (a través de torrents), conservándolu prácticamente pa siempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Pa contestar a la pregunta de qué porcentaxe de llibros se conservó, necesitemos saber el denominador: ¿cuántos llibros esisten en total? Y idealmente nun solo tener un númberu, sinón metadatos reales. Entós podemos non solo emparejales coles biblioteques en sombres, sinón tamién <strong>crear una llista de llibros que queden por conservar!</strong> Incluso podríemos empezar a soñar con un esfuerciu de collaboración colectiva pa baxar esta llista de coses por facer."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Raspamos <a %(wikipedia_isbndb_com)s>ISBNdb</a> y descargamos el <a %(openlibrary)s>conxuntu de datos d'Open Library</a>, pero los resultaos nun fueron satisfactorios. El principal problema yera que nun había muncha superposición d'ISBNs. Vea esti diagrama de Venn del <a %(blog)s>nuesu artículu de blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Sorprendiónos muncho lo pocu que se solapaben ISBNdb y Open Library, les cualos inclúin datos de diverses fontes, como raspados web y rexistros de biblioteques. Si ambes faen un bon trabayu atopando la mayoría de los ISBNs, los sos círculos habríen de solapase sustancialmente, o unu ser un subconxuntu del otru. Fízonos entrugar, ¿cuántos llibros tán <em>completamente fuera d'estos círculos</em>? Necesitamos una base de datos más grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Foi entós cuando fixemos la mira na base de datos de llibros más grande del mundu: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta ye una base de datos privativa de la organización ensin ánimu de lucre <a %(wikipedia_oclc)s>OCLC</a>, que xunta rexistros de metadatos de biblioteques de tol mundu, a cambéu de dar a eses biblioteques accesu al conxuntu completu de datos y faer que apaezan nos resultaos de busca de los usuarios finales."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Anque OCLC ye una organización ensin ánimu de lucre, el so modelu de negociu requiere protexer la so base de datos. Bueno, sentimos dicilo, amigos de OCLC, vamos a dalo too. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Nel últimu añu, raspamos meticulosamente tolos rexistros de WorldCat. Al principiu, tuvimos un golpe de suerte. WorldCat taba llanzando'l rediseñu completu del so sitiu web (en agostu de 2022). Esto incluyó una revisión sustancial de los sos sistemas de backend, introduciendo munchos fallos de seguridá. Inmediatamente aprovechamos la oportunidá, y fuimos quien a raspar cientos de millones (!) de rexistros en pocos díes."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Rediseñu de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dempués d'eso, los fallos de seguridá fueron corrigiéndose unu a unu, hasta que l'últimu que atopamos foi reparáu hai un mes. Nesi momentu yá teníemos casi tolos rexistros, y namás buscábamos rexistros de calidá un poco mayor. Asina que sentimos que yera hora de llanzalo!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Vamos ver dalguna información básica sobre los datos:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>¿Formatu?</strong> <a %(blog)s>Contenedores del Archivu d'Anna (AAC)</a>, que básicamente son <a %(jsonlines)s>JSON Lines</a> comprimiu con <a %(zstd)s>Zstandard</a>, más dalgunes semántiques estandarizaes. Estos contenedores inclúin varios tipos de rexistros, basáu nos distintos raspados que desplegamos."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Datos"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Ocurrió un error desconocíu. Por favor contáctanos en %(email)s con una captura de pantalla."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Esta moneda tien un mínimu más altu de lo habitual. Por favor, seleiciona una duración diferente o una moneda diferente."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Nun se pudo completar la solicitú. Por favor, inténtalo de nuevo en unos minutos, y si sigue pasando, contáctanos en %(email)s con una captura de pantalla."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Error nel procesu de pagu. Espera un momentu y inténtalo otra vuelta. Si'l problema persiste más de 24 hores, por favor contáctanos en %(email)s con una captura de pantalla."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentariu ocultu"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema col ficheru: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versión meyorada"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "¿Quies informar d'esti usuariu por comportamientu abusivu o inapropriáu?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Informar d'abusu"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abusu informáu:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Informasti d'esti usuariu por abusu."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responder"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Por favor <a %(a_login)s>anicia sesión</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Dexasti un comentariu. Pue tardar un minutu en apaecer."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Hebo un fallu. Por favor, recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "Páxines afeutaes %(count)s"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visible en Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nun visible en Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visible en Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcáu como rotu en Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Falta en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcáu como “spam” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcáu como “ficheru malu” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nun se pudieron convertir toles páxines a PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Falló la execución d'exiftool nesti ficheru"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Llibru (desconocíu)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Llibru (non-ficción)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Llibru (ficción)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artículu de revista"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentu de estándares"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Cómic"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musical"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiollibru"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Otru"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Descarga del Servidor Socio"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Descarga esterior"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Préstamu esternu"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Préstamu esternu (impresión desactivada)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Esplorar metadatos"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Conteníu en torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinu"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Subíes a AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Índiz de llibros electrónicos EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadatos checos"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Estatal Rusa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Títulu"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Autor"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Editorial"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edición"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Añu de publicación"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nome orixinal del ficheru"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Descripción y comentarios de metadatos"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Descargues del Servidor Socio temporalmente non disponibles pa esti ficheru."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Servidor Rápidu Sociu #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomendáu)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sin verificación del navegador o llistes d'espera)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Servidor Socio Lentu #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(lixeramente más rápido pero con llista d'espera)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sin llista d'espera, pero pue ser bien lentu)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficción"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(tamién calca “GET” enriba)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(fai clic en “GET” enriba)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "los sos anuncios son conocíos por contener software maliciosu, asina que usa un bloqueador d’anuncios o nun faigas clic nos anuncios"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Los ficheros de Nexus/STC pueden ser inestables pa descargar)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library en Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requiere el Navegador Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Presta del Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(sólo pa usuarios con discapacidá visual)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(el DOI asociáu podría nun tar disponible en Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "coleición"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Descargues masives por torrent"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(solo expertos)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Buscar nel Archivu d'Anna por ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Busca en delles otres bases de datos por ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Atopa'l rexistru orixinal en ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Buscar en l'Archivu d'Anna por ID de Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Atopar rexistru orixinal en Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Buscar nel Archivu d’Anna por númberu OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Atopa'l rexistru orixinal en WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Buscar en L'Archivu d'Anna el númberu SSID de DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Buscar manualmente en DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Buscar en l'Archivu d'Anna el númberu CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Atopa'l rexistru orixinal en CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Buscar nel Archivu d'Anna por númberu DXID de DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Índiz de llibros electrónicos EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "L'Archivu d'Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(sin necesidá de verificación del navegador)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadatos checos %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadatos"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "descrición"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nome alternativu del ficheru"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Títulu alternativu"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autor alternativu"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editorial alternativa"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edición alternativa"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Estensión alternativa"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "comentarios de metadatos"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descripción alternativa"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "fecha de códigu abiertu"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Ficheru de Sci-Hub “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Ficheru de Préstamu Dixital Controláu del Internet Archive “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Esti ye un rexistru d'un ficheru del Internet Archive, non un ficheru descargable direutamente. Pues intentar prestar el llibru (enllaz embaxo), o usar esta URL cuando <a %(a_request)s>solicites un ficheru</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Si tienes esti ficheru y entá nun ta disponible n'El Archivu d'Anna, considera <a %(a_request)s>subilu</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Rexistru de metadata de ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Rexistru de metadata d'Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Númberu OCLC (WorldCat) %(id)s rexistru de metadata"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Rexistru de metadatos DuXiu SSID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Rexistru de metadata CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Rexistru de metadata de MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Rexistru de metadata de Nexus/STC ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Esti ye un rexistru de metadatos, non un ficheru descargable. Pues usar esta URL cuando <a %(a_request)s>solicites un ficheru</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadatos del rexistru enllazáu"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Ameyorar metadatos en Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Avisu: múltiples rexistros enllazaos:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Ameyorar metadatos"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Informar de la calidá del ficheru"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Tiempu de descarga"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Páxina web:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Buscar n'El Archivu d'Anna por “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorador de Códigos:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Ver en Codes Explorer “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lleer más…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Descargues (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Prestar (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Esplorar metadatos (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentarios (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Llistes (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Estadístiques (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Detalles técnicos"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Esti ficheru pue tener problemes, y foi ocultu d'una biblioteca fonte.</span> A vegaes ye por solicitú d'un titular de drechos d'autor, a vegaes ye porque hai una alternativa meyor disponible, pero a vegaes ye por un problema col ficheru mesmu. Pue que tea bien pa descargar, pero recomendamos primero buscar un ficheru alternativu. Más detalles:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Una versión meyor d'esti ficheru podría tar disponible en %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Si entá quies descargar esti ficheru, asegúrate d'usar software de confianza y actualizáu pa abrilu."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Descargues rápides"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Descargues rápides</strong> Faite <a %(a_membership)s>miembru</a> pa sofitar la conservación a llargu plazu de llibros, artículos y más. Pa amosate la nuesa gratitú pol to sofitu, consigues descargues rápides. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si donas esti mes, consigues <strong>el doble</strong> de descargues rápides."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Descargues rápides</strong> Queden %(remaining)s güei. ¡Gracies por ser miembru! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Descargues rápides</strong> Quedáste ensin descargues rápides güei."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Descargues rápides</strong> Descargasti esti ficheru recién. Los enllaces siguirán válidos por un tiempu."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opción #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(sin redireición)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(abrir nel visor)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Recomienda a un amigu, y tanto tú como'l to amigu recibís %(percentage)s%% descargues rápides de bonu!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Más información…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Descargues lentos"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "De socios de confianza."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Más información na <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(pue requirir <a %(a_browser)s>verificación del navegador</a> — ¡descargues ilimitades!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Dempués de descargar:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Abrir nel nuesu visor"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "amosar descargues esternes"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Descargues esternes"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nun s'atoparon descargues."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Toles opciones de descarga tienen el mesmu ficheru, y deberíen ser segures d'usar. D'igual manera, siempres ten cuidao al descargar ficheros d'internet, especialmente de sitios esternos al Archivu d'Anna. Por exemplu, asegúrate de tener los tos dispositivos actualizaos."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Pa ficheros grandes, recomendamos usar un xestor de descargues pa evitar interrupciones."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Xestores de descargues recomendaos: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Necesitarás un llector d'ebooks o PDF pa abrir el ficheru, dependiendo del formatu del ficheru."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Llectores d'ebooks recomendaos: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visor en llinia d'Archivu d'Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Usa ferramientes en llinia pa convertir ente formatos."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Ferramientes de conversión recomendaes: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Pues unviar ficheros PDF y EPUB al to Kindle o Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ferramientes recomendaes: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Send to Kindle” d'Amazon"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Send to Kobo/Kindle” de djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Sofita a los autores y biblioteques"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si te presta esto y pues permitílu, considera mercar l'original, o sofitar a los autores direutamente."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si esto ta disponible na la to biblioteca llocal, considera lleválo en préstamu de baldre ellí."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Calidá del ficheru"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "¡Ayuda a la comunidá informando de la calidá d'esti ficheru! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Informar d'un problema col ficheru (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Calidá del ficheru excelente (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Amestar comentariu (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "¿Qué ta mal con esti ficheru?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Por favor usa'l <a %(a_copyright)s>formulariu de reclamación de DMCA / Drechos d'autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Descri la cuestión (requeríu)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descripción del problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 d'una versión meyor d'esti ficheru (si aplica)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Enllena esto si hai otru ficheru que s'apañe muncho con esti (mesma edición, mesma estensión de ficheru si lo puedes atopar), que la xente debería usar en llugar d'esti ficheru. Si conoces una versión meyor d'esti ficheru fuera de l'Archivu d'Anna, entós por favor <a %(a_upload)s>súbila</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Puedes sacar l'md5 de la URL, p. ej."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Unviar informe"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Aprende cómo <a %(a_metadata)s>mejorar los metadatos</a> de esti ficheru tú mesmu."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Gracies por unviar el to informe. Amuesarase nesta páxina, y tamién va revisase manualmente por Anna (hasta que tengamos un sistema de moderación propiu)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Hebo un fallu. Por favor, recarga la páxina y inténtalo otra vuelta."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si esti ficheru tien gran calidá, ¡puedes falar de cualquier cosa sobre él equí! Si non, por favor usa’l botón de “Informar d’un problema col ficheru”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "¡Encantóme esti llibru!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Dexar comentariu"

#, fuzzy
msgid "common.english_only"
msgstr "El testu embaxo continúa n'inglés."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Descargues totales: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “MD5 del ficheru” ye un hash que se calcula a partir del conteníu del ficheru, y ye razonablemente únicu basáu nesi conteníu. Toles biblioteques en sombres que tenemos indexaes equí usen principalmente MD5 pa identificar ficheros."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un ficheru pue apaecer en múltiples biblioteques en sombres. Pa información sobre los distintos datasets que compilamos, consulta la <a %(a_datasets)s>páxina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Esti ye un ficheru xestionáu pola <a %(a_ia)s>Biblioteca de Préstamu Dixital Controláu de IA</a>, y indexáu por Anna’s Archive pa la gueta. Pa información sobre los distintos datasets que compilamos, consulta la <a %(a_datasets)s>páxina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pa información sobre esti ficheru en particular, consulta’l so <a %(a_href)s>ficheru JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema cargando esta páxina"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Por favor, recarga pa intentalo otra vuelta. <a %(a_contact)s>Contacta con nós</a> si'l problema persiste por delles hores."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Nun s'alcontró"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” nun se atopó na nuesa base de datos."

#, fuzzy
msgid "page.login.title"
msgstr "Aniciar sesión / Rexistrase"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verificación del navegador"

#, fuzzy
msgid "page.login.text1"
msgstr "Pa evitar que los bots de spam creen munches cuentes, necesitemos verificar el to navegador primero."

#, fuzzy
msgid "page.login.text2"
msgstr "Si quedes atrapáu nun bucle infinitu, recomendamos instalar <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Tamién pue ayudar desactivar los bloqueadores d'anuncios y otres estensiones del navegador."

#, fuzzy
msgid "page.codes.title"
msgstr "Códigos"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorador de Códigos"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explora los códigos con que tán etiquetaos los rexistros, por prefixu. La columna de “rexistros” amuesa la cantidá de rexistros etiquetaos con códigos col prefixu daú, como se ve nel motor de busca (incluyendo rexistros solo de metadatos). La columna de “códigos” amuesa cuántos códigos reales tienen un prefixu daú."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Esta páxina pue tardar un poco en xenerase, por eso necesita un captcha de Cloudflare. <a %(a_donate)s>Los miembros</a> puen saltase'l captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Por favor, nun rasques estes páxines. En vez d'eso, recomendamos <a %(a_import)s>xenerar</a> o <a %(a_download)s>descargar</a> les nueses bases de datos de ElasticSearch y MariaDB, y executar el nuesu <a %(a_software)s>códigu de fonte abiertu</a>. Los datos en bruto puen explorase manualmente a través de ficheros JSON como <a %(a_json_file)s>esti</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefixu"

#, fuzzy
msgid "common.form.go"
msgstr "Dir"

#, fuzzy
msgid "common.form.reset"
msgstr "Reaniciar"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Buscar nel Archivu d'Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Avisu: el códigu tien carauteres Unicode incorreutos y pue comportase de manera incorreuta en delles situaciones. El binariu en bruto pue decodificase de la representación base64 na URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefiju conocíu “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefiju"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiqueta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descripción"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL pa un códigu específicu"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” va ser sustituyíu col valor del códigu"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL xenérica"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Sitiu web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "Rexistru %(count)s que coincide con “%(prefix_label)s”"
msgstr[1] "Rexistros %(count)s que coinciden con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL pa códigu específicu: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Más…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Códigos que entamen con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Índiz de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "rexistros"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "códigos"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s rexistros"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pa reclamaciones de DMCA / drechos d'autor, usa <a %(a_copyright)s>esti formulariu</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Cualesquier otru mediu de contactu sobre reclamaciones de drechos d'autor va ser automáticamente desaniciáu."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "¡Agradecemos muncho los tos comentarios y entrugues!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Sicas, debido a la cantidá de spam y correos ensin sentíu que recibimos, por favor marca les caxes pa confirmar que entiendes estes condiciones pa contactanos."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Les reclamaciones de drechos d'autor a esti corréu van ser ignoraes; usa'l formulariu en cuenta."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Los servidores de los socios tán indisponibles por cuenta de los cierres de l'aloxamientu. Deberíen tar disponibles otra vuelta en breve."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Les membresíes van estenderse en consecuencia."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nun nos mandes un corréu pa <a %(a_request)s>solicitar llibros</a><br>o pequeños (<10k) <a %(a_upload)s>uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Cuando faigas consultes sobre la cuenta o donaciones, amesta'l to ID de cuenta, captures de pantalla, recibos, la mayor cantidá d'información posible. Namás revisamos el nuesu corréu cada 1-2 selmanes, asina que non incluyir esta información retrasará cualquier resolución."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Amosar corréu"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulario de reclamación de derechos de autor / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si tienes una reclamación de DMCA u otra reclamación de derechos d'autor, por favor completa esti formulariu lo más precisu posible. Si tienes dalgún problema, ponte en contactu con nós na nuestra dirección dedicada a DMCA: %(email)s. Ten en cuenta que les reclamaciones mandaes a esta dirección nun van ser procesaes, ye solo pa entrugues. Por favor, usa'l formulariu embaxo pa unviar les tos reclamaciones."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs en l'Archivu d'Anna (requeríu). Una por llinia. Por favor, inclúi namái URLs que describan la mesma edición exacta d'un llibru. Si quies facer una reclamación por múltiples llibros o múltiples ediciones, por favor unvia esti formulariu múltiples vegaes."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Les reclamaciones que agrupen múltiples llibros o ediciones xuntes van ser rechaçaes."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "El to nome (requeríu)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Direición (requeríu)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Númberu de teléfonu (requeríu)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Corréu electrónicu (requeríu)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descripción clara del material orixinal (requeríu)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs del material orixinal (si aplica). Unu por llinia. Por favor, inclúi namái aquellos que coincidan exactamente cola edición pola que tas faciendo una reclamación de derechos d'autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs del material orixinal, una por llinia. Por favor, tómate un momentu pa buscar el to material orixinal en Open Library. Esto va ayudanos a verificar la to reclamación."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs del material orixinal, una por llinia (requeríu). Por favor, inclúi cuantes más meyor, pa ayudanos a verificar la to reclamación (p. ex. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaración y firma (requeríu)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Unviar reclamación"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Gracies por unviar la to reclamación de derechos d'autor. Revisarémosla lo más pronto posible. Por favor, recarga la páxina pa unviar otra."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Dalgo salió mal. Por favor, recarga la páxina y inténtalo otra vez."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si ta interesa en espeyar esti conxuntu de datos pa <a %(a_archival)s>archivu</a> o con fines de <a %(a_llm)s>entrenamientu de LLM</a>, por favor, contáctanos."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "La nuesa misión ye archivar tolos llibros del mundu (asina como artículos, revistes, etc.), y facelos accesibles a gran escala. Creemos que tolos llibros han d’espeyase de manera amplia, pa garantizar redundancia y resiliencia. Por eso tamos xuntando ficheros de delles fontes. Dalgunes fontes son completamente abiertes y puen espeyase en masa (como Sci-Hub). Otres tán zarrades y protexíes, asina que intentamos rascar d’elles pa “liberar” los sos llibros. Otras tán en mediaos."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Tolos nuesos datos puen <a %(a_torrents)s>torrentease</a>, y tolos nuesos metadatos puen <a %(a_anna_software)s>xenerase</a> o <a %(a_elasticsearch)s>descargase</a> como bases de datos ElasticSearch y MariaDB. Los datos en bruto puen explorase manualmente a través de ficheros JSON como <a %(a_dbrecord)s>esti</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Visión xeneral"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Abaxo hai una visión xeneral rápida de les fontes de los ficheros en Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Tamañu"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% espeyáu por AA / torrents disponibles"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Porcentaxes del númberu de ficheros"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Cabera actualización"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Ficción y Ficción"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s ficheru"
msgstr[1] "%(count)s ficheros"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: conxeláu dende 2021; la mayoría disponible a través de torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: amiestes menores dende entós</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Escluyendo “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Los torrents de ficción tán atrasaos (anque los IDs ~4-6M nun tán en torrents porque se solapen colos nuestros de Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "La colección “China” en Z-Library paez ser la mesma que la nuestra de DuXiu, pero con MD5s distintos. Escluímos estos ficheros de los torrents pa evitar duplicaciones, pero seguimos amosándolos nel nuesu índiz de busca."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Préstamu Dixital Controláu"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ de los ficheros son buscables."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Escluyendo duplicados"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Dende que les biblioteques sombra suelen sincronizar datos ente sí, hai un solape considerable ente les biblioteques. Por eso los númberos nun sumen al total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "El porcentaxe de “espeyaos y sembráos por Anna’s Archive” amuesa cuántos ficheros espeyamos nós mesmos. Sembramos esos ficheros en masa a través de torrents, y faémoslos disponibles pa descarga direuta a través de sitios web sociu."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Biblioteques fonte"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Delles biblioteques fonte promueven el compartimientu masivu de los sos datos a través de torrents, mientres que otres nun comparten la so coleición de manera tan fácil. Nesti últimu casu, Anna’s Archive intenta raspar les sos coleiciones y poneles disponibles (consulta la nuesa páxina de <a %(a_torrents)s>Torrents</a>). Tamién hai situaciones intermedies, por exemplu, onde les biblioteques fonte tán dispuestes a compartir, pero nun tienen los recursos pa facelo. Nesi casu, tamién intentamos ayudar."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Abaxo amuésase un resumen de cómo interaccionamos coles distintes biblioteques fonte."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Ficheros"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Volcáu diariu de la base de datos <a %(dbdumps)s>HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizaos pa <a %(nonfiction)s>Non-Fiction</a> y <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(covers)s>torrents de cubiertes de llibros</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub tien conxeláu ficheros nuevos dende 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Vuelcos de metadatos disponibles <a %(scihub1)s>equí</a> y <a %(scihub2)s>equí</a>, amás de como parte de la <a %(libgenli)s>base de datos de Libgen.li</a> (que usamos)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrentes de datos disponibles <a %(scihub1)s>equí</a>, <a %(scihub2)s>equí</a> y <a %(libgenli)s>equí</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Dalgunos ficheros nuevos tán <a %(libgenrs)s>sendo</a> <a %(libgenli)s>añadíos</a> a “scimag” de Libgen, pero nun son abondo pa xustificar nuevos torrentes"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Vuelcos trimestrales de <a %(dbdumps)s>bases de datos HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Los torrentes de Non-Fiction compártense con Libgen.rs (y espéyense <a %(libgenli)s>equí</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s El Archivu d'Anna y Libgen.li xestionen conxuntamente coleiciones de <a %(comics)s>cómics</a>, <a %(magazines)s>revistes</a>, <a %(standarts)s>documentos estándar</a> y <a %(fiction)s>ficción (divergida de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s La so coleición “fiction_rus” (ficción rusa) nun tien torrents dedicaos, pero ta cubierta por torrents d'otros, y mantenemos un <a %(fiction_rus)s>espeyu</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s L'Archivu d'Anna y Z-Library xestionen conxuntamente una coleición de <a %(metadata)s>metadatos de Z-Library</a> y <a %(files)s>ficheros de Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Dalgunos metadatos disponibles a través de <a %(openlib)s>volcáu de la base de datos de Open Library</a>, pero nun cubren la coleición entera de IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nun hai volcáu de metadatos fácilmente accesibles pa la so coleición entera"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(ia)s>metadatos de IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Ficheros disponibles namái pa préstamu de manera llindada, con delles restricciones d'accesu"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(ia)s>ficheros de IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Delles bases de datos de metadatos esparcíes pel internet chinu; anque a menudo son bases de datos de pagu"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nun hai volcáu de metadatos fácilmente accesibles pa la so coleición entera."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(duxiu)s>metadatos de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Delles bases de datos de ficheros esparcíes pel internet chinu; anque a menudo son bases de datos de pagu"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La mayoría de los ficheros solo son accesibles usando cuentes premium de BaiduYun; velocidaes de descarga lentas."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(duxiu)s>ficheros de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Delles fontes más pequeñes o d'una sola vegada. Animamos a la xente a xubir a otres biblioteques sombra primero, pero a vegaes la xente tien coleiciones que son demasiado grandes pa que otros les revisen, anque nun lo suficientemente grandes pa merecer la so propia categoría."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fuentes solo de metadatos"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Tamién enriquezmos la nuesa coleición con fontes solo de metadatos, que podemos empareyar con ficheros, por exemplu, usando númberos ISBN u otros campos. Abaxo amuésase un resumen d'estes fontes. Otra vuelta, delles d'estes fontes son completamente abiertes, mientres que otres tenemos que raspar."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "La nuesa inspiración pa coleicionar metadatos ye l'oxetivu d'Aaron Swartz de “una páxina web pa cada llibru que se publicó”, pa lo cual creó <a %(a_openlib)s>Open Library</a>. Ese proyeutu foi bien, pero la nuesa posición única permítenos consiguir metadatos que ellos nun pueden. Otra inspiración foi el nuesu deséu de saber <a %(a_blog)s>cuántos llibros hai nel mundu</a>, pa poder calcular cuántos llibros nos queden por salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Ten en cuenta que na busca de metadatos, amosamos los rexistros orixinales. Nun facemos nenguna fusión de rexistros."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Cabera actualización"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Vertíos mensuales de <a %(dbdumps)s>bases de datos</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Non disponible direutamente en masa, protexíu contra rascáu"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(worldcat)s>metadatos de OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Base de datos unificada"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Xuntamos toles fontes mencionaes enriba nuna base de datos unificada que usamos pa sirvir esti sitiu web. Esta base de datos unificada nun ta disponible direutamente, pero como Anna’s Archive ye completamente de códigu abiertu, pue xenerase <a %(a_generated)s>fácilmente</a> o <a %(a_downloaded)s>descargase</a> como bases de datos ElasticSearch y MariaDB. Los scripts nesa páxina descargarán automáticamente tolos metadatos necesarios de les fontes mencionaes enriba."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si quies explorar los nuesos datos enantes d'executar esos scripts de manera llocal, pues mirar los nuesos ficheros JSON, que enllacen a otros ficheros JSON. <a %(a_json)s>Esti ficheru</a> ye un bon puntu de partida."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptáu del nuesu <a %(a_href)s>artículu del blogue</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> ye una base de datos masiva de llibros escaneados, creada pol <a %(superstar_link)s>SuperStar Digital Library Group</a>. La mayoría son llibros académicos, escaneados pa facelos disponibles dixitalmente a universidaes y biblioteques. Pa la nuesa audiencia de fala inglesa, <a %(princeton_link)s>Princeton</a> y la <a %(uw_link)s>Universidá de Washington</a> tienen bones reseñes. Tamién hai un artículu escepcional que da más información de fondu: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Los llibros de Duxiu llevanten tiempu pirateándose nel internet chinu. Xeneralmente véndense por menos d'un dólar por revendedores. Suélen distribuyise usando l'equivalente chinu de Google Drive, que davezu foi hackeáu pa permitir más espaciu d'almacenamientu. Dalgunos detalles técnicos puen atopase <a %(link1)s>equí</a> y <a %(link2)s>equí</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Anque los llibros distribuyéronse de manera semi-pública, ye bastante difícil consiguilos en masa. Tuvimos esto alto na nuesa llista de TAREES, y destinamos dellos meses de trabayu a tiempu completu pa ello. Sicasí, a finales de 2023 un voluntariu increíble, asombrosu y talentosu contactó con nós, diciéndonos que yá fixera tol trabayu — a gran costu. Compartió con nós la coleición completa, ensin esperar nada a cambéu, salvo la garantía de la so preservación a llargu plazu. Verdaderamente remarcable."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Ficheros totales: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Tamañu total de ficheros: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Ficheros espeyaos por l'Archivu d'Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Cabera actualización: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents del Archivu d'Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplu de rexistru nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "El nuesu artículu del blog sobre estos datos"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts pa importar metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formatu de Contenedores del Archivu d'Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Más información de los nuestros voluntarios (notes en bruto):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Préstamu Dixital Controláu"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Esti conxuntu de datos ta estrechamente rellacionáu col <a %(a_datasets_openlib)s>conxuntu de datos de Open Library</a>. Contién una raspa de tolos metadatos y una gran parte de ficheros de la Biblioteca de Préstamu Dixital Controláu de IA. Les actualizaciones suéltense nel <a %(a_aac)s>formatu de Contenedores del Archivu d'Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Estos rexistros tán siendo referenciaos direutamente del conxuntu de datos de Open Library, pero tamién contién rexistros que nun tán en Open Library. Tamién tenemos una cantidá de ficheros de datos raspáos por miembros de la comunidá a lo llargo de los años."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La colección consiste en dos partes. Necesites dambes partes pa tener tolos datos (agüeyando los torrents superáos, que tán tachaos na páxina de torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "la nuestra primer versión, enantes de que estandarizáramos nel <a %(a_aac)s>formatu de Contenedores del Archivu d'Anna (AAC)</a>. Contién metadatos (como json y xml), pdfs (de los sistemas de préstamu dixital acsm y lcpdf), y miniatures de portades."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "nuevos llançamientos incrementales, usando AAC. Namái contién metadata con marques de tiempu dempués de 2023-01-01, yá que'l restu ta cubiertu yá por “ia”. Tamién tolos ficheros pdf, esta vuelta dende los sistemas de préstamu acsm y “bookreader” (el llector web de IA). A pesar de que'l nome nun seya exacto, seguimos populando ficheros de bookreader na colección ia2_acsmpdf_files, yá que son mutuamente esclusivos."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sitiu web principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca Dixital de Préstamu"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentación de metadata (la mayoría de los campos)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Información del país del ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "L'Agencia Internacional del ISBN publica regularmente los rangos que destinó a les agències nacionales del ISBN. D'esta manera podemos determinar a qué país, rexón o grupu llingüísticu pertenez esti ISBN. Actualmente usamos estos datos de manera indirecta, a través de la biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Cabera actualización: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sitiu web del ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Pa la hestoria de los distintos forks de Library Genesis, vea la páxina de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "El Libgen.li contién la mayoría del mesmu conteníu y metadatos que Libgen.rs, pero tien delles coleiciones amás d'esto, a saber, cómics, revistes y documentos estándar. Tamién integró <a %(a_scihub)s>Sci-Hub</a> nos sos metadatos y motor de busca, que ye lo que usamos pa la nuesa base de datos."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Los metadatos d'esta biblioteca tán disponibles de baldre <a %(a_libgen_li)s>en libgen.li</a>. Sicasí, esti sirvidor ye lentu y nun sofita retomar conexones rotas. Los mesmos ficheros tamién tán disponibles nun <a %(a_ftp)s>sirvidor FTP</a>, que funciona meyor."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents tán disponibles pa la mayoría del conteníu adicional, especialmente torrents pa cómics, revistes y documentos estándar que se publicaron en collaboración col Archivu d'Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La coleición de ficción tien los sos propios torrents (divergentes de <a %(a_href)s>Libgen.rs</a>) aniciando en %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Según l'administrador de Libgen.li, la coleición “fiction_rus” (ficción rusa) debería tar cubierta por torrents publicaos regularmente por <a %(a_booktracker)s>booktracker.org</a>, especialmente los torrents de <a %(a_flibusta)s>flibusta</a> y <a %(a_librusec)s>lib.rus.ec</a> (que espeyamos <a %(a_torrents)s>equí</a>, anque entá nun establecimos qué torrents correspuenden a qué ficheros)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Les estadístiques de toles coleiciones puen alcontrase <a %(a_href)s>nel sitiu web de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "La non-ficción tamién paez que se desvió, pero ensin nuevos torrentes. Paez que esto pasó dende anicios de 2022, anque nun lo verificamos."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Ciertos rangos ensin torrents (como los rangos de ficción f_3463000 a f_4260000) probablemente sean ficheros de Z-Library (u otros duplicados), anque quiciabes queramos facer deduplicación y crear torrents pa ficheros únicos de lgli n'estos rangos."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Tenga en cuenta que los ficheros torrent que refierense a “libgen.is” son espeyos explícitos de <a %(a_libgen)s>Libgen.rs</a> (“.is” ye un dominiu diferente usáu por Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Un recursu útil pa usar los metadatos ye <a %(a_href)s>esta páxina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de ficción nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de cómics nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de revistes nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos estándar nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de ficción rusa nel Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadatos vía FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Información del campu de metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Espeyu d'otros torrents (y torrents únicos de ficción y cómics)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Foru de discusión"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "La nuesa entrada de blog sobre la llibranza de cómics"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "La historia rápida de los distintos forks de Library Genesis (o “Libgen”) ye que col tiempu, les distintes persones implicaes en Library Genesis tuvieron un desencuentru y siguieron caminos separaos."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La versión “.fun” foi creada pol fundador orixinal. Ta siendo renovada en favor d'una versión nueva y más distribuida."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La versión “.rs” tien datos bien similares, y más consistentemente llanzan la so coleición en torrents masivos. Ta más o menos dividida en una seición de “ficción” y otra de “non-ficción”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Orixinalmente en “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La versión <a %(a_li)s>“.li”</a> tien una coleición masiva de cómics, amás d'otru conteníu, que nun ta (entá) disponible pa descarga masiva a través de torrents. Tien una coleición de torrents separada de llibros de ficción, y contién los metadatos de <a %(a_scihub)s>Sci-Hub</a> na so base de datos."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Según esti <a %(a_mhut)s>post nel foru</a>, Libgen.li foi orixinalmente albergáu en “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> en ciertu sentíu tamién ye un fork de Library Genesis, anque usaron un nome diferente pa'l so proyeutu."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Esta páxina ye sobre la versión “.rs”. Ye conocida por publicar de manera constante tanto los sos metadatos como'l conteníu completu del so catálogu de llibros. La so colección de llibros ta dividida ente una parte de ficción y otra de non-ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Un recursu útil pa usar los metadatos ye <a %(a_metadata)s>esta páxina</a> (bloquea rangos d'IP, pue ser necesario usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de 2024-03, nuevos torrents tán publicándose en <a %(a_href)s>esti filu del foru</a> (bloquea rangos d'IP, pue ser necesario usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de Non-Ficción en l'Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de Ficción en l'Archivu d'Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Información de los campos de metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de Non-ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de Ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Foru de discusión de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents por l'Archivu d'Anna (portades de llibros)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "El nuesu blog sobre la publicación de les portades de llibros"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis ye conocida por xenerosamente poner los sos datos disponibles en bloque a través de torrents. La nuesa colección de Libgen consiste en datos auxiliares que ellos nun llancen direutamente, en collaboración con ellos. ¡Munches gracies a tolos implicaos en Library Genesis por trabayar con nós!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Publicación 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Esta <a %(blog_post)s>primer publicación</a> ye bastante pequeña: unos 300GB de portades de llibros del fork de Libgen.rs, tanto de ficción como de non-ficción. Tán organizaos de la mesma manera que como apaecen en libgen.rs, por exemplu:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pa un llibru de non-ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pa un llibru de ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Igual que cola colección de Z-Library, punximos toos nun gran archivu .tar, que se pue montar usando <a %(a_ratarmount)s>ratarmount</a> si quies sirvir los archivos direutamente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> ye una base de datos propietaria de la organización non llucro <a %(a_oclc)s>OCLC</a>, que xunta rexistros de metadatos de biblioteques de tol mundu. Probablemente seya la mayor coleición de metadatos de biblioteques del mundu."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Ochobre 2023, primer llanzamientu:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "En ochobre de 2023 <a %(a_scrape)s>llanzamos</a> una recopilación completa de la base de datos OCLC (WorldCat), nel <a %(a_aac)s>formatu de Contenedores de Anna’s Archive</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents por Anna’s Archive"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "La nuesa entrada de blog sobre estos datos"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library ye un proyeutu de códigu abiertu del Internet Archive pa catalogar tolos llibros del mundu. Tien una de les operaciones de escaneu de llibros más grandes del mundu, y tien munchos llibros disponibles pa préstamu dixital. El so catálogu de metadatos de llibros ta disponible de baldre pa descargar, y ta incluyíu en Anna’s Archive (anque anguaño nun ta disponible na gueta, salvo que busques explícitamente un ID d'Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Lliberación 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Esti ye un volcu de munches solicitúes a isbndb.com durante setiembre de 2022. Intentamos cubrir tolos rangos del ISBN. Estos son unos 30,9 millones de rexistros. Nel so sitiu web afirmen que tienen en realidá 32,6 millones de rexistros, asina que quiciabes se nos escapó dalgún, o <em>ellos</em> podíen tar faciendo dalgo mal."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Les respuestes JSON tán prácticamente en bruto del so servidor. Un problema de calidá de datos que notamos ye que pa los númberos ISBN-13 que entamen con un prefiju diferente a “978-”, siguen incluyendo un campu “isbn” que simplemente ye'l númberu ISBN-13 colos primeros 3 númberos cortaos (y el díxitu de control recalculáu). Esto ye obviu que ta mal, pero asina ye como lo faen, asina que nun lo alteramos."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Otru problema potencial que podríes atopar ye'l fechu de que'l campu “isbn13” tien duplicados, asina que nun pue usalu como clave primaria nuna base de datos. Los campos “isbn13”+“isbn” combinaos sí paecen ser únicos."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pa más información sobre Sci-Hub, por favor refiérase a la so <a %(a_scihub)s>páxina oficial</a>, <a %(a_wikipedia)s>páxina de Wikipedia</a>, y esti <a %(a_radiolab)s>entrevista en podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Tenga en cuenta que Sci-Hub ta <a %(a_reddit)s>conxeláu dende 2021</a>. Ya se conxeló enantes, pero en 2021 amestáronse unos cuantos millones de artículos. Sicasí, un númberu limitáu d'artículos siguir añadiéndose a les coleiciones “scimag” de Libgen, anque nun ye abondo pa xustificar nuevos torrents en masa."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usamos los metadatos de Sci-Hub como los proporciona <a %(a_libgen_li)s>Libgen.li</a> na so coleición “scimag”. Tamién usamos el dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Tenga en cuenta que los torrents “smarch” tán <a %(a_smarch)s>desactualizaos</a> y poro nun tán incluyíos na nuesa llista de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents en Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadatos y torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents en Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents en Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Actualizaciones en Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Páxina de Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Entrevista en podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Xubes al Archivu d'Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Visión xeneral de la <a %(a1)s>páxina de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Delles fontes más pequeñes o d'una sola vegada. Animamos a la xente a xubir a otres biblioteques sombra primero, pero a vegaes la xente tien coleiciones que son demasiado grandes pa que otros les revisen, anque nun lo suficientemente grandes pa merecer la so propia categoría."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La coleición de “xubes” ta dividida en subcoleiciones más pequeñes, que s'indiquen nos AACIDs y nos nomes de los torrents. Toles subcoleiciones fueron primero desduplicaes contra la coleición principal, anque los ficheros JSON de metadatos “upload_records” entá contienen munches referencies a los ficheros orixinales. Los ficheros non de llibros tamién fueron eliminaos de la mayoría de les subcoleiciones, y típicamente <em>non</em> se señalen nos “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Munches subcoleiciones en sí mesmes tán compuestes de sub-sub-coleiciones (p. ex. de diferentes fontes orixinales), que se representen como direutorios nos campos “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Les subcoleiciones son:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcoleición"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notes"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "explorar"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "buscar"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Paez ser bastante completa. Del nuesu voluntariu “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "D'un torrent de <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tien un solape bastante altu con coleiciones de documentos esistentes, pero poques coincidencies de MD5, asina que decidimos guardalu completu."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raspáu de <q>iRead eBooks</q> (= fonéticamente <q>ai rit i-books</q>; airitibooks.com), por voluntariu <q>j</q>. Corresponde a la metadata de <q>airitibooks</q> en <a %(a1)s><q>Otros raspáos de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "D'una coleición <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parcialmente de la fonte orixinal, parcialmente de the-eye.eu, parcialmente d'otros espeyos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Dende un sitiu web priváu de torrents de llibros, <a %(a_href)s>Bibliotik</a> (a menudo conocíu como “Bib”), del cual los llibros se xuntaben en torrents por nome (A.torrent, B.torrent) y distribuyíense a través de the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Del nuesu voluntariu “bpb9v”. Pa más información sobre <a %(a_href)s>CADAL</a>, consulta les notes na nuesa <a %(a_duxiu)s>páxina del conxuntu de datos de DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Más del nuesu voluntariu “bpb9v”, mayormente ficheros de DuXiu, amás d'una carpeta “WenQu” y “SuperStar_Journals” (SuperStar ye la empresa detrás de DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Dende'l nuesu voluntariu “cgiym”, testos chinos de diverses fontes (representaos como subdirectorios), incluyendo de <a %(a_href)s>China Machine Press</a> (un importante editor chino)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleiciones non chinees (representaes como subdirectorios) del nuesu voluntariu “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raspáu de llibros sobre arquitectura china, por voluntariu <q>cm</q>: <q>Consiguílu explotando una vulnerabilidá de rede na editorial, pero esa brecha yá se zarró</q>. Corresponde a la metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Otros raspáos de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Llibros de la editorial académica <a %(a_href)s>De Gruyter</a>, recoyíos de dellos grandes torrentes."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Raspáu de <a %(a_href)s>docer.pl</a>, un sitiu web polacu de compartición de ficheros enfocáu en llibros y otres obres escrites. Raspáu a finales de 2023 por voluntariu “p”. Nun tenemos bon metadata del sitiu web orixinal (nin siquier estensiones de ficheru), pero filtramos pa ficheros asemeyaos a llibros y a menudo fuimos quien a estraer metadata de los ficheros mesmos."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epubs de DuXiu, direutamente de DuXiu, recoyíos pol voluntariu “w”. Namái los llibros recientes de DuXiu tán disponibles direutamente como ebooks, polo que la mayoría d'estos han de ser recientes."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Ficheros restantes de DuXiu del voluntariu “m”, que nun taben nel formatu propietariu PDG de DuXiu (el principal <a %(a_href)s>conxuntu de datos de DuXiu</a>). Recoyíos de munchos oríxenes orixinales, desafortunadamente ensin preservar esos oríxenes na ruta del ficheru."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raspáu de llibros eróticos, por voluntariu <q>do no harm</q>. Corresponde a la metadata de <q>hentai</q> en <a %(a1)s><q>Otros raspáos de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Coleición raspada d'un editor de Manga xaponés pol voluntariu “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivos xudiciales seleicionaos de Longquan</a>, proporcionaos por voluntariu “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Raspáu de <a %(a_href)s>magzdb.org</a>, un aliáu de Library Genesis (ta enllazáu na páxina d'iniciu de libgen.rs) pero que nun quixo proporcionar los sos ficheros direutamente. Obteníu por voluntariu “p” a finales de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Delles pequeñes subíes, demasiado pequeñes pa ser una subcoleición propia, pero representaes como direutorios."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks de AvaxHome, un sitiu web rusu de compartición de ficheros."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivu de periódicos y revistes. Corresponde a la metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Otros raspáos de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raspáu del <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Coleición del voluntariu “o” que coleicionó llibros polacos direutamente de sitios web de llanzamientu orixinal (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coleiciones combinaes de <a %(a_href)s>shuge.org</a> por voluntarios “cgiym” y “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomada en referencia a la biblioteca ficticia), recopilada en 2022 por un voluntariu “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-coleiciones (representaes como direutorios) del voluntariu “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taiwán), mebook (mebook.cc, 我的小书屋, mi pequeñu cuartu de llibros — woz9ts: “Esti sitiu céntrase principalmente en compartir ficheros d'ebooks d'alta calidá, dalgunos de los cualos tán maquetáos pol propiu dueñu. El dueñu foi <a %(a_arrested)s>deteníu</a> en 2019 y dalguién fixo una coleición de ficheros que compartió.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Ficheros restantes de DuXiu del voluntariu “woz9ts”, que nun taben nel formatu propietariu PDG de DuXiu (tovía por convertir a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents por Archivu d’Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Raspáu de Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library tien les sos raigañes na comunidá de <a %(a_href)s>Library Genesis</a>, y orixinalmente entamó colos sos datos. Dende entós, profesionalizóse considerablemente, y tien una interfaz muncho más moderna. Poro, son quien a recibir muncho más donativos, tanto monetarios pa siguir meyorando la so páxina web, como donativos de llibros nuevos. Amestaron una gran coleición amás de la de Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Actualización a febreru de 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "A finales de 2022, los supuestos fundadores de Z-Library fueron arrestaos, y los dominios fueron incautaos poles autoridaes de los Estaos Xuníos. Dende entós, la páxina web foi volviendo poco a poco en llinia. Desconozse quién la xestiona anguaño."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La coleición consiste en tres partes. Les páxines de descripción orixinales de les dos primeres partes tán conservaes embaxo. Necesites les tres partes pa consiguir tolos datos (agüeyando los torrents que fueron superaes, que tán tachaos na páxina de torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: la nuesa primer llanza. Esta foi la primer llanza de lo que entós se llamaba “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: segunda llanza, esta vuelta con tolos ficheros empaquetaos en ficheros .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: llanzaes nueves incrementales, usando’l formatu <a %(a_href)s>Contenedores del Archivu d’Anna (AAC)</a>, agora llanzaes en collaboración col equipu de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents por Archivu d’Anna (metadatos + conteníu)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplu de rexistru nel Archivu d’Anna (coleición orixinal)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplu de rexistru nel Archivu d’Anna (coleición “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Páxina principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Dominiu Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Entrada de blog sobre la Llanza 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Entrada de blog sobre la Llanza 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Llanzaes de Zlib (páxines de descripción orixinales)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Llanza 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "El espeyu inicial foi obténíu con muncho esfuerciu a lo llargo de 2021 y 2022. Nesti momentu ta un poco desactualizáu: reflexa l’estáu de la coleición en xunu de 2021. Actualizarémoslo nel futuru. Agora mesmo tamos enfocaos en sacar esta primer llanza."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Dende que Library Genesis yá ta preserváu con torrents públicos, y ta incluyíu en Z-Library, fiximos una deduplicación básica contra Library Genesis en xunu de 2022. Pa esto usámos hashes MD5. Probablemente haya muncho más conteníu duplicáu na biblioteca, como múltiples formatos de ficheru col mesmu llibru. Esto ye difícil de detectar con precisión, asina que nun lo facemos. Dempués de la deduplicación quedamos con más de 2 millones de ficheros, sumando poco menos de 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La colección consiste en dos partes: un volcado MySQL “.sql.gz” de los metadatos, y los 72 ficheros torrent d'unos 50-100GB cada ún. Los metadatos contién la información reportada pol sitiu web de Z-Library (títulu, autor, descripción, tipu de ficheru), amás del tamañu real del ficheru y md5sum que observamos, yá que a veces estos nun concuerden. Paecen haber rangos de ficheros pa los cualos Z-Library tien metadatos incorreutos. Tamién podríamos tener ficheros descargados incorreutamente en dalgunos casos aisllaos, que intentaremos detectar y correxir nel futuru."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Los grandes ficheros torrent contién los datos reales de los llibros, col ID de Z-Library como nome del ficheru. Les estensiones de los ficheros pueden reconstruise usando'l volcado de metadatos."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La colección ye una mezcla de conteníu de non-ficción y ficción (non separáu como en Library Genesis). La calidá tamién varía muncho."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Esta primer versión ta agora completamente disponible. Ten en cuenta que los ficheros torrent namái tán disponibles al traviés del nuesu espeyu en Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Versión 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Conseguimos tolos llibros que se amestaron a Z-Library ente'l nuesu postrer espeyu y agostu de 2022. Tamién volvimos atrás y rascamos dalgunos llibros que nos faltaron la primer vez. En total, esta nueva colección ye d'unos 24TB. Otra vuelta, esta colección ta deduplicada contra Library Genesis, yá que yá hai torrents disponibles pa esa colección."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Los datos tán organizaos de manera similar a la primer versión. Hai un volcado MySQL “.sql.gz” de los metadatos, que tamién inclúi tolos metadatos de la primer versión, sustituyéndola. Tamién amestamos dalgunes columnes nueves:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: si esti ficheru yá ta en Library Genesis, en cualesquier de les colecciones de non-ficción o ficción (emparejáu por md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: en qué torrent ta esti ficheru."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: marcáu cuando nun pudimos descargar el llibru."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mencionamos esto la última vez, pero pa aclarar: “filename” y “md5” son les propiedaes reales del ficheru, mientres que “filename_reported” y “md5_reported” son lo que rascamos de Z-Library. A vegaes estos dos nun concuerden ente sigo, asina que incluyimos dambos."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pa esta versión, camudamos la colación a “utf8mb4_unicode_ci”, que debería ser compatible con versiones más vieyes de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Los ficheros de datos son similares a la última vez, anque son muncho más grandes. Simplemente nun nos molestamos en crear montones de ficheros torrent más pequeños. “pilimi-zlib2-0-14679999-extra.torrent” contién tolos ficheros que nos faltaron na última versión, mientres que los otros torrents son toos rangos de ID nuevos. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Actualización %(date)s:</strong> Fiximos la mayoría de los nuesos torrents demasiado grandes, causando que los clientes torrent tuvieran problemes. Eliminámoslos y llanzamos nuevos torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Actualización %(date)s:</strong> Entá había demasiados ficheros, asina que los empaquetamos en ficheros tar y llanzamos nuevos torrents otra vuelta."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Versión 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Esti ye un únicu ficheru torrent adicional. Nun contién información nueva, pero tien dalgunos datos que pueden tardar en computase. Esto failo conveniente de tener, yá que descargar esti torrent ye davezu más rápido que computalu dende cero. En particular, contién índices SQLite pa los ficheros tar, pa usase con <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Preguntes frecuentes (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "¿Qué ye l'Archivu d'Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>L'Archivu d'Anna</span> ye un proyeutu ensin ánimu d'arriquecimientu con dos oxetivos:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservación:</strong> Respaldando tola conocencia y cultura de la humanidá.</li><li><strong>Accesu:</strong> Faiendo esta conocencia y cultura disponible pa cualisquier persona nel mundu.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tol nuesu <a %(a_code)s>códigu</a> y <a %(a_datasets)s>datos</a> son completamente de códigu abiertu."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Conservación"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Preservamos llibros, artículos, cómics, revistes y más, trayendo estos materiales de delles <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteques sombra</a>, biblioteques oficiales y otres coleiciones xuntes nun mesmu llugar. Tolos datos tán preservaos pa siempre faciendo fácil duplicalos en gran cantidá —usando torrents— resultando en munches copies alredor del mundu. Dalgunes biblioteques sombra yá faen esto elles mesmes (p. ex. Sci-Hub, Library Genesis), mientres que l'Archivu d'Anna “libera” otres biblioteques que nun ufierten distribución en masa (p. ex. Z-Library) o nun son biblioteques sombra enforma (p. ex. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Esta amplia distribución, xunto col códigu de fonte abierta, fai que el nuesu sitiu web seya resiliente a los intentos de desactivación y asegura la preservación a llargu plazu del conocimientu y la cultura de la humanidá. Aprende más sobre <a href=\"/datasets\">los nuesos datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Estimamos que guardamos alredor de <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% de los llibros del mundu</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Accesu"

#, fuzzy
msgid "page.home.access.text"
msgstr "Trabayamos con socios pa facer les nuestres coleiciones fácil y llibremente accesibles pa toos. Creemos que toos tienen derechu a la sabiduría colectiva de la humanidá. Y <a %(a_search)s>ensin perxuiciu pa los autores</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Descargues per hora nos últimos 30 díes. Promediu per hora: %(hourly)s. Promediu diariu: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Creemos firmemente na llibre circulación de la información y na preservación del conocimientu y la cultura. Con esti motor de busca, construyimos sobre los güeyos de xigantes. Respetamos fondamente'l trabayu duru de les persones que crearon les delles biblioteques sombra, y esperamos que esti motor de busca amplíe'l so alcuerdu."

#, fuzzy
msgid "page.about.text3"
msgstr "Pa siguir al día col nuesu progresu, siguir a Anna en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pa entrugues y comentarios, por favor contautar con Anna en %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "¿Cómo puedo ayudar?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Síguinos en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Esparde la voz sobre l'Archivu d'Anna en Twitter, Reddit, Tiktok, Instagram, na to cafetería o biblioteca llocal, o ondequier que vayas! Nun creemos en la retención d'información — si nos desanicien, vamos volver aparécer n'otru llau, yá que tol nuesu códigu y datos son completamente de códigu abiertu.</li><li>3. Si pues, considera <a href=\"/donate\">donar</a>.</li><li>4. Ayuda a <a href=\"https://translate.annas-software.org/\">traducir</a> el nuesu sitiu web a otros idiomes.</li><li>5. Si yes inxenieru de software, considera contribuir al nuesu <a href=\"https://annas-software.org/\">códigu abiertu</a>, o sembrar los nuesos <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Agora tamién tenemos un canal de Matrix sincronizáu en %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Si yes un investigador de seguridá, podemos usar les tos habilidaes tanto pa ofensiva como pa defensiva. Consulta la nuesa páxina de <a %(a_security)s>Seguridá</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Tamos buscando espertos en pagos pa comerciantes anónimos. ¿Pue ayudanos a amestar más maneres convenientes de donar? PayPal, WeChat, tarxetes de regalu. Si conoces a dalguién, por favor contáctanos."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Siempre tamos buscando más capacidá de sirvidor."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Puees ayudar informando de problemes con ficheros, dexando comentarios y creando llistes direutamente nesta páxina web. Tamién puees ayudar <a %(a_upload)s>subiendo más llibros</a>, o arreglando problemes de ficheros o formateo de llibros esistentes."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Crea o ayuda a mantener la páxina de Wikipedia del Archivu d'Anna na to llingua."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Tamos buscando poner anuncios pequeños y de bon gustu. Si quies anunciarte n'Anna’s Archive, por favor avísanos."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Encantaríanos que la xente configurara <a %(a_mirrors)s>espeyos</a>, y vamos sofitar económicamente esto."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pa información más estensa sobre cómo ser voluntariu, consulta la nuesa páxina de <a %(a_volunteering)s>Voluntariáu y Recompenses</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "¿Por qué son tan lentas las descargas lentas?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Literalmente nun tenemos recursos abondo pa dar a tola xente del mundu descargues a alta velocidá, por más que nos prestara. Si un benefactor ricu quixera ayudanos y proporcionanos esto, sería increíble, pero hasta entós, tamos faciendo lo meyor que podemos. Somos un proyeutu ensin ánimu de lucre que apenas se sostién a traviés de donaciones."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Por eso implementamos dos sistemas pa descargues de baldre, colos nuesos socios: servidores compartíos con descargues lentos, y servidores un poco más rápidos con llista d'espera (pa reducir el númberu de persones descargando al mesmu tiempu)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Tamién tenemos <a %(a_verification)s>verificación del navegador</a> pa les descargues lentines, porque si non los bots y scrapers abusaríen d'elles, faciendo les coses más lentines pa los usuarios llexítimos."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Ten en cuenta que, al usar el Tor Browser, quiciabes necesites axustar la to configuración de seguridá. Na opción más baxa, llamada “Estándar”, el desafíu de Cloudflare turnstile resulta. Nes opciones más altes, llamaes “Más Seguru” y “El Más Seguru”, el desafíu falla."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Pa ficheros grandes, a veces les descargues lentos pueden interrumpise a la metá. Recomendamos usar un xestor de descargues (como JDownloader) pa reanudar automáticamente les descargues grandes."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "FAQ de donaciones"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>¿Renováronse automáticamente les membresíes?</div> Les membresíes <strong>nun</strong> se renueven automáticamente. Pues uníte por tantu tiempu como quieras."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>¿Puedo ameyorar la mio sociedá o consiguir múltiples sociedaes?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>¿Tienes otros métodos de pagu?</div> De momentu non. Munches persones nun quieren que archivos como esti esistan, asina que tenemos que ser cuidaos. Si pues ayudanos a configurar otros métodos de pagu (más convenientes) de manera segura, por favor contauta con nós en %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>¿Qué quier dicir los rangos per mes?</div> Pues llegar al llau baxu d'un rangu aplicando tolos descuentos, como escoyer un periodu más llargu que un mes."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>¿En qué gastamos les donaciones?</div> 100%% va pa preservar y facer accesible el conocimientu y la cultura del mundu. Anguaño gastámolu mayormente en servidores, almacenamientu y ancho de banda. Nun se destina nengún dineru a miembros del equipu personalmente."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>¿Puedo facer una donación grande?</div> ¡Eso sería increíble! Pa donaciones de más d'unos pocos miles de dólares, por favor contáctanos direutamente en %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>¿Puedo facer una donación ensin faceme miembru?</div> Claro que sí. Aceptamos donaciones de cualquier cantidá nesta direición de Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "¿Cómo pueo xubir llibros nuevos?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativamente, puedes subilos a Z-Library <a %(a_upload)s>equí</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pa subíes pequeñes (hasta 10,000 ficheros) por favor súbelos a dambos %(first)s y %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Por agora, suxerimos xubir llibros nuevos a los forks de Library Genesis. Equí tienes una <a %(a_guide)s>guía útil</a>. Ten en cuenta que dambos forks que indexamos nesti sitiu web tiren d'esti mesmu sistema de xubida."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pa Libgen.li, asegúrate de primero iniciar sesión nel <a %(a_forum)s >so foru</a> con nome d'usuariu %(username)s y contraseña %(password)s, y depués tornar a la so <a %(a_upload_page)s >páxina de subida</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Si la to direición de corréu electrónicu nun funciona nos foros de Libgen, recomendamos usar <a %(a_mail)s>Proton Mail</a> (de baldre). Tamién pues <a %(a_manual)s>solicitar manualmente</a> que se active la to cuenta."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Ten en cuenta que mhut.org bloquia ciertos rangos d'IP, polo que podríes necesitar un VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pa subires grandes (más de 10,000 ficheros) que nun se acepten en Libgen o Z-Library, por favor contáctanos en %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Pa xubir artículos académicos, por favor tamién (amás de Library Genesis) xubilos a <a %(a_stc_nexus)s>STC Nexus</a>. Son la meyor biblioteca sombra pa artículos nuevos. Aún nun los integramos, pero faremolo en dalgún momentu. Pue usase'l so <a %(a_telegram)s>bot de xubida en Telegram</a>, o contautar col direición llistada nel so mensaxe ancláu si tienes munchos ficheros pa xubir d'esta manera."

#, fuzzy
msgid "page.faq.request.title"
msgstr "¿Cómo solicito llibros?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Nesta ocasión, nun podemos atender solicitúes de llibros."

#, fuzzy
msgid "page.request.forums"
msgstr "Por favor, fai les tos solicitúes nos foros de Z-Library o Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nun nos unvíes solicitúes de llibros per corréu electrónicu."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "¿Recoyéis metadatos?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Efectivamente."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Descargué 1984 de George Orwell, ¿vendrá la policía a la mio puerta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Nun te preocupes enforma, hai munches persones descargando de páxines enllazaes por nós, y ye extremadamente raru metese en problemes. Sicasí, pa tar seguru recomendamos usar un VPN (de pagu), o <a %(a_tor)s>Tor</a> (de baldre)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "¿Cómo guardo la configuración de la mio gueta?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Escueye la configuración que prefieras, dexa vacíu'l cuadru de búsqueda, calca “Buscar”, y dempués marca la páxina col sistema de marcadores del to navegador."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "¿Tienes una app móvil?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nun tenemos una aplicación móvil oficial, pero pues instalar esti sitiu web como una aplicación."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Calca nel menú de tres puntos na esquina superior derecha y seleiciona “Amestar a la pantalla d'aniciu”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Calca nel botón “Compartir” al fondu, y seleiciona “Amestar a Pantalla d'Inicio”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "¿Tienes una API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Tenemos una API JSON estable pa miembros, pa consiguir una URL de descarga rápida: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentación dientro del mesmu JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pa otros casos d'usu, como iterar tolos nuestros ficheros, construyir una busca personalizá, y asina, recomendamos <a %(a_generate)s>xenerar</a> o <a %(a_download)s>descargar</a> les nuestres bases de datos ElasticSearch y MariaDB. Los datos en bruto pueden explorase manualmente <a %(a_explore)s>a través de ficheros JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "La nuesa llista de torrents en bruto tamién se pue descargar como <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ de Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Quixera ayudar a sembrar, pero nun tengo muncho espaciu nel discu."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Usa el <a %(a_list)s>xenerador de llistes de torrents</a> pa xenerar una llista de torrents que más necesiten ser compartíos, dientro de los límites del to espaciu d'almacenamientu."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Los torrents son mui lentos; ¿pueo descargar los datos direutamente de vosotros?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Sí, consulta la páxina de <a %(a_llm)s>datos de LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "¿Puedo descargar solo un subconxuntu de los ficheros, como solo un idioma o tema particular?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Respuesta corta: nun ye fácil."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Respuesta llarga:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "La mayoría de los torrents contién los ficheros direutamente, lo que significa que pue indicar a los clientes de torrents que solo descarguen los ficheros necesarios. Pa determinar qué ficheros descargar, pue <a %(a_generate)s>xenerar</a> la nuesa metadata, o <a %(a_download)s>descargar</a> les nueses bases de datos ElasticSearch y MariaDB. Desafortunadamente, delles coleiciones de torrents contién ficheros .zip o .tar na raíz, nesi casu necesites descargar tol torrent enantes de poder seleicionar ficheros individuales."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Sin embargu, tenemos <a %(a_ideas)s>dalgunos idees</a> pa esti casu.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Entáun, nun hai ferramientes fáciles d'usar pa filtrar torrents, pero damos la bienvenida a les contribuciones."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "¿Cómo tratáis los duplicados nos torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Intentamos caltener la mínima duplicación o solape ente los torrents nesta llista, pero esto nun siempre pue llograse, y depende en gran midida de les polítiques de les biblioteques fonte. Pa biblioteques que saquen los sos propios torrents, nun ta en les nueses manes. Pa torrents publicaos por l'Archivu d'Anna, deduplicamos sólo basándonos nel hash MD5, lo que significa que versiones diferentes del mesmu llibru nun se deduplicen."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "¿Puedo consiguir la llista de torrents en formatu JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Sí."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "¿Nun veyo PDFs o EPUBs nos torrents, namái ficheros binarios? ¿Qué faigo?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Estos son realmente PDFs y EPUBs, namái que nun tienen una estensión en munchos de los nuesos torrents. Hai dos llugares onde puedes atopar los metadatos de los ficheros torrent, incluyendo los tipos d'archivu/estensiones:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Cada colección o llanzamientu tien el so propiu metadata. Por exemplu, los <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> tienen una base de datos de metadata correspondiente albergada nel sitiu web de Libgen.rs. Normalmente, enllazamos a recursos de metadata relevantes dende la <a %(a_datasets)s>páxina del conxuntu de datos</a> de cada colección."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomendamos <a %(a_generate)s>xenerar</a> o <a %(a_download)s>descargar</a> les nueses bases de datos ElasticSearch y MariaDB. Estes contién un mapeu pa cada rexistru nel Archivu d'Anna colos sos ficheros de torrent correspondientes (si tán disponibles), baxo “torrent_paths” nel JSON d'ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "¿Por qué el mio cliente de torrent nun pue abrir dalgunos de los ficheros torrent / enllaces magnet?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Dalgunos clientes de torrent nun sofiten tamaños de pieza grandes, que munchos de los nuestros torrents tienen (pa los más nuevos yá nun facemos esto — ¡anque ye válidu según les especificaciones!). Asina que pruebe con un cliente diferente si topes con esto, o queixate a los fabricantes del to cliente de torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "¿Tienes un programa de divulgación responsable?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Damos la bienvenida a los investigadores de seguridá pa buscar vulnerabilidaes nos nuesos sistemas. Somos grandes defensores de la divulgación responsable. Contáctanos <a %(a_contact)s>equí</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Anguaño nun podemos dar recompenses por fallos, escepto por vulnerabilidades que tengan el <a %(a_link)s>potencial de comprometer la nuesa anonimia</a>, pa les cualos ufiertamos recompenses ente $10k-50k. ¡Gustaríanos ufiertar un ámbitu más ampliu pa recompenses por fallos nel futuru! Ten en cuenta que los ataques d'ingeniería social tán fuera del ámbitu."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si tas interesáu na seguridá ofensiva y quies ayudar a archivar el conocimientu y la cultura del mundu, asegúrate de contactanos. Hai munches maneres en que pues ayudar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "¿Hai más recursos sobre l'Archivu d'Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog d’Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizaciones regulares"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software d'Anna</a> — el nuesu códigu de fonte abierta"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traducir en Anna’s Software</a> — el nuesu sistema de traducción"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — sobre los datos"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominos alternativos"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — más sobre nós (¡por favor ayuda a mantener esta páxina actualizada, o crea una na to propia llingua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "¿Cómo informo d'una infracción de drechos d'autor?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nun aloxamos nengún material con drechos d'autor equí. Somos un motor de gueta, y como tal namái indexamos metadatos que yá tán públicamente disponibles. Al descargar d'estos recursos esternos, suxerimos que revises les lleis na to xurisdicción en rellación a lo que ta permitíu. Nun somos responsables del conteníu aloxáu por otros."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si tienes quexes sobre lo que ves equí, lo meyor ye que contactes col sitiu web orixinal. Regularmente actualizamos la nuesa base de datos colos sos cambios. Si de verdá pienses que tienes una quexa válida de DMCA a la que deberíamos responder, por favor, enllena'l <a %(a_copyright)s>formulariu de reclamación de DMCA / Drechos d'autor</a>. Tomamos les tos quexes en serio y responderemos lo más rápido posible."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "¡Odio cómo tán xestionando esti proyeutu!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Tamién queremos recordar a toos que tol nuesu códigu y datos son completamente de códigu abiertu. Esto ye únicu pa proyectos como'l nuesu — nun conocemos nengún otru proyectu con un catálogu tan masivu que tamién seya de códigu abiertu. Damos la bienvenida a cualesquier persona que piense que xestionamos mal el nuesu proyectu a que tomen el nuesu códigu y datos y monten la so propia biblioteca sombra! Nun lo dicimos por rencor nin nada — sinceramente pensamos que esto sería increíble porque elevaría'l nivel pa toos y ayudaría a preservar meyor la herencia de la humanidá."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "¿Tienes un monitor de tiempu d'activu?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Por favor, consulta <a %(a_href)s>esti excelente proyeutu</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "¿Cómo puedo donar llibros u otros materiales físicos?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Por favor, unvialos al <a %(a_archive)s>Internet Archive</a>. Ellos van preservalos correutamente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "¿Quién ye Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "¡Tú yes Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "¿Cuáles son los tos llibros favoritos?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Equí hai dellos llibros que tienen una significancia especial pal mundu de les biblioteques de la solombra y la preservación dixital:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Agotáste los descargues rápides güei."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Faite miembru pa usar descargues rápides."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Agora aceptamos tarxetes de regalu d'Amazon, tarxetes de créitu y débito, criptomonedes, Alipay y WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Base de datos completa"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libros, artículos, revistes, cómics, rexistros de biblioteques, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Buscar"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>pausó</a> la subida de nuevos artículos."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB ye una continuación de Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accesu direutu a %(count)s artículos académicos"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Abrir"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Si yes <a %(a_member)s>miembru</a>, nun se necesita verificación del navegador."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archivu a llargu plazu"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Los datasets usaos en l'Archivu d'Anna son completamente abiertos, y pueden ser espeyaos en bloque usando torrents. <a %(a_datasets)s>Más información…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Puees ayudar enforma sembrando torrents. <a %(a_torrents)s>Más información…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Datos de formación de LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Tenemos la mayor coleición mundial de datos de testu d'alta calidá. <a %(a_llm)s>Más información…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Espeyos: llamamientu a voluntarios"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Buscamos voluntarios"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Como un proyeutu ensin ánimu de llucro y de códigu abiertu, siempre tamos buscando xente pa ayudar."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Si xestiones un procesador de pagos anónimu d'altu riesgu, por favor contáctanos. Tamién tamos buscando xente que quiera poner anuncios pequeños y de bon gustu. Tolos ingresos van pa los nuesos esfuercios de preservación."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog d'Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Descargues IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Tolos enllaces de descarga pa esti ficheru: <a %(a_main)s>Páxina principal del ficheru</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "Puerta d'entrada IPFS n.º %(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(pue que necesites intentalo delles vegaes con IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Pa tener descargues más rápides y saltar los controles del navegador, <a %(a_membership)s>faite miembru</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Pa la duplicación masiva de la nuesa coleición, consulta les páxines de <a %(a_datasets)s>Datasets</a> y <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Datos de LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Ye bien sabíu que los LLM prosperen con datos d'alta calidá. Tenemos la mayor coleición de llibros, artículos, revistes, etc. del mundu, que son d'algamunes de les más altes fontes de testu."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Escala y rangu únicu"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "La nuesa coleición contién más de cien millones de ficheros, incluyendo revistes académicas, llibros de testu y revistes. Alcanzamos esta escala combinando grandes repositorios esistentes."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Dalgunes de les nueses coleiciones fonte yá tán disponibles en masa (Sci-Hub, y partes de Libgen). Otras fontes liberámosles nós mesmos. <a %(a_datasets)s>Datasets</a> amuesa una visión completa."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "La nuesa coleición inclúi millones de llibros, artículos y revistes d'antes de la era del e-book. Grandes partes d'esta coleición yá fueron OCR’eaes, y yá tienen poco solape internu."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Cómo podemos ayudar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Podemos proporcionar accesu d'alta velocidá a les nueses coleiciones completes, amás de coleiciones ensin publicar."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Esti ye un accesu a nivel empresarial que podemos proporcionar por donativos nel rangu de decenes de miles de dólares. Tamién tamos dispuestos a intercambiar esto por coleiciones d'alta calidá que entá nun tenemos."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Podemos reembolsate si nos puedes proporcionar un enriquecimientu de los nuesos datos, como:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Eliminación de solapes (deduplicación)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extracción de testu y metadatos"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "¡Sostén l'archivu a llargu plazu del conocimientu humanu, mientres consigues meyores datos pa'l to modelu!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contáctanos</a> pa falar de cómo podemos trabayar xuntos."

#, fuzzy
msgid "page.login.continue"
msgstr "Continuar"

#, fuzzy
msgid "page.login.please"
msgstr "Por favor, <a %(a_account)s>anicia sesión</a> pa ver esta páxina.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "L'Archivu d'Anna ta temporalmente fuera de serviciu por mantenimientu. Por favor, vuelve en una hora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Mejorar metadatos"

#, fuzzy
msgid "page.metadata.body1"
msgstr "¡Puedes ayudar a la preservación de libros mejorando los metadatos! Primero, lee la información de fondo sobre metadatos en l'Archivu d'Anna, y luego aprende cómo mejorar los metadatos a través del enlace con Open Library, y gana una membresía gratuita en l'Archivu d'Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Información de fondo"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Cuando ves un libro en l'Archivu d'Anna, puedes ver varios campos: título, autor, editorial, edición, año, descripción, nombre de archivo y más. Todas esas piezas de información se llaman <em>metadatos</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Como combinamos libros de varias <em>biblioteques fuente</em>, mostramos los metadatos disponibles en esa biblioteca fuente. Por ejemplo, para un libro que obtuvimos de Library Genesis, mostraremos el título de la base de datos de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "A veces un libro está presente en <em>múltiples</em> biblioteques fuente, que pueden tener diferentes campos de metadatos. En ese caso, simplemente mostramos la versión más larga de cada campo, ya que esa, con suerte, contiene la información más útil. ¡Aún así, mostraremos los otros campos debajo de la descripción, por ejemplo, como \"título alternativo\" (pero solo si son diferentes)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "También extraemos <em>códigos</em> como identificadores y clasificadores de la biblioteca fuente. <em>Identificadores</em> representan de manera única una edición particular de un libro; ejemplos son ISBN, DOI, Open Library ID, Google Books ID o Amazon ID. <em>Clasificadores</em> agrupan varios libros similares; ejemplos son Dewey Decimal (DCC), UDC, LCC, RVK o GOST. A veces estos códigos están explícitamente vinculados en las bibliotecas fuente, y a veces podemos extraerlos del nombre de archivo o la descripción (principalmente ISBN y DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Podemos usar identificadores para encontrar registros en <em>colecciones solo de metadatos</em>, como OpenLibrary, ISBNdb o WorldCat/OCLC. Hay una pestaña específica de <em>metadatos</em> en nuestro motor de búsqueda si deseas explorar esas colecciones. Usamos registros coincidentes para completar campos de metadatos faltantes (por ejemplo, si falta un título), o por ejemplo, como \"título alternativo\" (si hay un título existente)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Para ver exactamente de dónde provienen los metadatos de un libro, consulta la pestaña <em>“Detalles técnicos”</em> en la página de un libro. Tiene un enlace al JSON bruto de ese libro, con enlaces al JSON bruto de los registros originales."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Para más información, consulta las siguientes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Buscar (pestaña de metadatos)</a>, <a %(a_codes)s>Explorador de códigos</a> y <a %(a_example)s>Ejemplo de JSON de metadatos</a>. Finalmente, todos nuestros metadatos pueden ser <a %(a_generated)s>generados</a> o <a %(a_downloaded)s>descargados</a> como bases de datos ElasticSearch y MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Enlace con Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Entonces, si encuentras un archivo con metadatos incorrectos, ¿cómo deberías arreglarlo? Puedes ir a la biblioteca fuente y seguir sus procedimientos para corregir los metadatos, pero ¿qué hacer si un archivo está presente en múltiples bibliotecas fuente?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Hay un identificador que se trata de manera especial en l'Archivu d'Anna. <strong>¡El campo md5 de annas_archive en Open Library siempre anula todos los demás metadatos!</strong> Retrocedamos un poco primero y aprendamos sobre Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library fue fundada en 2006 por Aaron Swartz con el objetivo de \"una página web para cada libro jamás publicado\". Es una especie de Wikipedia para metadatos de libros: todos pueden editarla, tiene una licencia libre y se puede descargar en bloque. Es una base de datos de libros que está más alineada con nuestra misión; de hecho, l'Archivu d'Anna se ha inspirado en la visión y vida de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "En lugar de reinventar la rueda, decidimos redirigir a nuestros voluntarios hacia Open Library. Si ves un libro que tiene metadatos incorrectos, puedes ayudar de la siguiente manera:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Ve al <a %(a_openlib)s>sitio web de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Encuentra el registro correcto del libro. <strong>ADVERTENCIA:</strong> asegúrate de seleccionar la <strong>edición</strong> correcta. En Open Library, tienes \"obras\" y \"ediciones\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Una \"obra\" podría ser \"Harry Potter y la piedra filosofal\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Una \"edición\" podría ser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La primer edición de 1997 publicada por Bloomsbery con 256 páxines."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "La edición de 2003 en rústica publicada por Raincoast Books con 223 páxines."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La traducción polaca de 2000 “Harry Potter I Kamie Filozoficzn” por Media Rodzina con 328 páxines."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Toles eses ediciones tienen distintos ISBN y conteníos distintos, asina que asegúrate de seleicionar la correuta."

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edita'l rexistru (o créalu si nun esiste), y amesta tanta información útil como puedas! Yá tas equí, asina que podríes facer el rexistru realmente increíble."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sota “Números ID” seleiciona “Anna’s Archive” y amesta'l MD5 del llibru d'Anna’s Archive. Esta ye la cadena llarga de lletres y númberos dempués de “/md5/” na URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Intenta alcontrar otros ficheros en Anna’s Archive que tamién coincidan con esti rexistru, y amesta esos tamién. Ensinón, podemos agrupalos como duplicados na páxina de busca d'Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Cuando termines, apunta la URL que acabes d'actualizar. Una vez que hayas actualizáu al menos 30 rexistros con MD5 d'Anna’s Archive, mándanos un <a %(a_contact)s>corréu electrónicu</a> y mándanos la llista. Daremos-te una membresía gratuita pa Anna’s Archive, pa que puedas facer esti trabayu más fácil (y como agradecimientu pola to ayuda). Estes tienen que ser ediciones de gran calidá que amesten cantidá sustancial d'información, en casu contrariu la to solicitú va ser refugada. La to solicitú tamién va ser refugada si dalguna de les ediciones se revierte o corrige por moderadores d'Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Ten en cuenta que esto namás funciona pa llibros, non pa artículos académicos u otros tipos de ficheros. Pa otros tipos de ficheros seguimos recomendando alcontrar la biblioteca fonte. Pue tardar unes selmanes en que los cambios s'incluyan en Anna’s Archive, yá que necesitamos descargar el vuelque de datos más nuevu d'Open Library, y rexenerar el nuesu índiz de busca."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Espeyos: llamáu a voluntarios"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Pa aumentar la resiliencia de l'Archivu d'Anna, tamos buscando voluntarios pa executar espeyos."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Tamos buscando esto:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Executas el códigu fonte abiertu d’Anna’s Archive, y actualices regularmente tanto’l códigu como los datos."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "La to versión ta claramente distinguida como un espeyu, por exemplu “El Archivu de Bob, un espeyu d’Anna’s Archive”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Tás dispuestu a asumir los riesgos asociaos con esti trabayu, que son significativos. Tienes un entendimientu fondiu de la seguridá operativa necesaria. Los conteníos d'estos <a %(a_shadow)s>posts</a> <a %(a_pirate)s>son</a> evidentes pa ti."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Tás dispuestu a contribuir al nuesu <a %(a_codebase)s>códigu fonte</a> — en collaboración col nuesu equipu — pa que esto seya posible."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inicialmente nun te daremos accesu a les descarges del nuesu servidor sociu, pero si les coses van bien, podemos compartir eso contigo."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Gastos d'hospedaxe"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Tamos dispuestos a cubrir los gastos d’hospedaxe y VPN, inicialmente hasta $200 per mes. Esto ye suficiente pa un servidor de busca básicu y un proxy protexíu por DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Solo pagaremos pola hospedaxe una vez que tengas too configuráu, y hayas demostrao que yes capaz de mantener l’archivu actualizáu con les actualizaciones. Esto significa que tendrás que pagar los primeros 1-2 meses de la to bolsu."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "El to tiempu nun va ser compensáu (y el nuesu tampocu), yá que esto ye trabayu puramente voluntariu."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si te involucras significativamente en el desarrollo y las operaciones de nuestro trabajo, podemos discutir compartir más de los ingresos por donaciones contigo, para que los despliegues según sea necesario."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Entamando"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Por favor <strong>nun contactes con nós</strong> pa pidir permisu, o pa entrugues básiques. ¡Les aiciones falen más alto que les palabres! Toles informaciones tán disponibles, asina que namái pon en marcha la to copia."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Siéntete llibre de publicar tickets o solicitúes de fusión nel nuesu Gitlab cuando te topes con problemes. Podríamos necesitar construir dalgunes carauterístiques específiques pa la to copia, como rebrandear de “Anna’s Archive” al nome de la to páxina web, (inicialmente) desactivar cuentes d’usuariu, o enllazar de vuelta a la nuesa páxina principal dende les páxines de llibros."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Una vez que tengas la to copia funcionando, por favor contacta con nós. Encantaríanos revisar la to OpSec, y una vez que tea sólida, enllazaremos a la to copia y empezaremos a trabayar más estrechamente contigo."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "¡Gracies de antemano a tolos que quieran contribuir d’esta manera! Nun ye pa los de corazón débil, pero afitaría la llonxevidá de la biblioteca verdaderamente abierta más grande de la hestoria humana."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Descargar dende la páxina web del collaborador"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Les descargues lentas namái tán disponibles al traviés del sitiu web oficial. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Les descargues lentas nun tán disponibles a través de les VPNs de Cloudflare o dende direiciones IP de Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Por favor, espere <span %(span_countdown)s>%(wait_seconds)s</span> segundos para descargar esti ficheru."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Usa l'URL siguiente pa descargar: <a %(a_download)s>Descargar agora</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "¡Gracies por esperar, esto fai que'l sitiu web tea accesible de baldre pa toos! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Avisu: ha habío munches descargues dende la to direición IP nes caberes 24 hores. Les descargues podríen ser más lentes de lo habitual."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descargues dende la to direición IP nes caberes 24 hores: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si tas usando un VPN, conexón a internet compartida, o to ISP comparte IPs, esta advertencia podría ser por eso."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Pa dar a toos la oportunidá de descargar ficheros de baldre, necesites esperar enantes de poder descargar esti ficheru."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Siéntete llibre de siguir esplorando l'Archivu d'Anna nuna llingüeta distinta mientres esperes (si'l to navegador sofita refrescar llingüetes en segundu planu)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Siéntete llibre de esperar a que carguen múltiples páxines de descarga al mesmu tiempu (pero por favor sólo descarga un ficheru al mesmu tiempu por servidor)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Una vez consigas un enllaz de descarga, ye válidu por delles hores."

#, fuzzy
msgid "layout.index.header.title"
msgstr "L'Archivu d'Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rexistru en l'Archivu d'Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Descargar"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Pa sofitar l'accesibilidá y la preservación a llargu plazu del conocimientu humanu, hazte <a %(a_donate)s>miembru</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Como bonus, 🧬&nbsp;SciDB carga más rápido pa los miembros, ensin nengún llímite."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "¿Nun funciona? Prueba <a %(a_refresh)s>refrescar</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Entá nun hai vista previa disponible. Descarga'l ficheru dende <a %(a_path)s>L'Archivu d'Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB ye una continuación de Sci-Hub, col so interfaz familiar y visualización direuta de PDFs. Introduz el to DOI pa ver."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Tenemos la coleición completa de Sci-Hub, amás de nuevos artículos. La mayoría pue vese direutamente con una interfaz familiar, asemeyada a Sci-Hub. Dalgunos puen descargase a través de fuentes esternes, nesi casu amosamos enllaces a eses."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Buscar"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nueva gueta"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Incluyir namái"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Escluyir"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Non verificado"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Descargar"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Artículos de revistes"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Préstamu Dixital"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadatos"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Títulu, autor, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Buscar"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Configuración de búsqueda"

#, fuzzy
msgid "page.search.submit"
msgstr "Buscar"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "La búsqueda llevó demasiao tiempu, lo cual ye común pa consultes xenerales. Los contos de filtros pueden nun ser precisos."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "La gueta llevó muncho tiempu, lo que quier dicir que quiciabes veyes resultaos inexactos. Dalaveces <a %(a_reload)s>recargar</a> la páxina ayuda."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Amosar"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Llista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabla"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avanzáu"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Buscar descripciones y comentarios de metadatos"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Amestar campu específicu de busca"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(buscar campu específicu)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Añu de publicación"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Conteníu"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Tipu de ficheru"

#, fuzzy
msgid "page.search.more"
msgstr "más…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Accesu"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "raspáu y de códigu abiertu por AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Llingua"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordenar por"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Más relevante"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Más nuevu"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(añu de publicación)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Más vieyos"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "El más grande"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(tamañu del ficheru)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "El más pequeñu"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(códigu abiertu)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Al debalu"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "L'índiz de búsqueda actualízase mensualmente. Anguaño inclúi entrades hasta %(last_data_refresh_date)s. Pa más información técnica, consulta la páxina de %(link_open_tag)sconjuntos de datos</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Pa explorar el índiz de busca por códigos, usa'l <a %(a_href)s>Explorador de Códigos</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Escribi nel cuadru pa buscar nel nuesu catálogu de %(count)s ficheros descargables direutamente, que <a %(a_preserve)s>conservamos pa siempre</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "De fechu, cualisquier persona pue ayudar a caltener estos ficheros sembrando la nuesa <a %(a_torrents)s>llista unificada de torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Anguaño tenemos el catálogu abiertu más completu del mundu de llibros, artículos y otros trabayos escritos. Refleamos Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>y más</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Si atopas otres “biblioteques sombra” que deberíemos espeyar, o si tienes dalguna entruga, por favor contáctanos en %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Pa reclamaciones de DMCA / drechos d'autor <a %(a_copyright)s>calca equí</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Conseyu: usa los atayos del tecláu “/” (enfocar búsqueda), “enter” (buscar), “j” (arriba), “k” (abaxo), “<” (páxina anterior), “>” (siguiente páxina) pa navegar más rápido."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "¿Busques artículos?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Escribi nel cuadru pa buscar nel nuesu catálogu de %(count)s trabayos académicos y artículos de revistes, que <a %(a_preserve)s>conservamos pa siempre</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Escribi nel cuadru pa buscar ficheros nes biblioteques de préstamu dixital."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Esti índiz de búsqueda inclúi anguaño metadatos de la biblioteca de Préstamu Dixital Controláu del Internet Archive. <a %(a_datasets)s>Más sobre los nuestros datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Pa más biblioteques dixitales de préstamu, consulta <a %(a_wikipedia)s>Wikipedia</a> y la <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Escribi nel cuadru pa buscar metadatos de biblioteques. Esto pue ser útil cuando <a %(a_request)s>solicites un ficheru</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Esti índiz de busca inclúi anguaño metadatos de delles fontes de metadatos. <a %(a_datasets)s>Más sobre los nuestros datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Pa metadatos, amosamos los rexistros orixinales. Nun facemos nenguna fusión de rexistros."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Hai munches, munches fontes de metadatos pa obres escrites al rodiu del mundu. <a %(a_wikipedia)s>Esta páxina de Wikipedia</a> ye un bon entamu, pero si conoces otres bones llistes, por favor faínoslo saber."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Escribi equí pa buscar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Estos son rexistros de metadatos, <span %(classname)s>nun</span> ficheros descargables."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Error al buscar."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Prueba <a %(a_reload)s>recargar la páxina</a>. Si'l problema persiste, por favor unvíanos un corréu a %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nun s'atoparon ficheros.</span> Prueba con menos o con otros términos de busca y filtros."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A vega veces esto pasa incorreutamente cuando'l sirvidor de busca ta lentu. Nesi casos, <a %(a_attrs)s>recargar</a> pue ayudar."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Atopamos coincidencies en: %(in)s. Puees referite a la URL que s'atopó ellí cuando <a %(a_request)s>solicites un ficheru</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Artículos de revista (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Préstamu Dixital (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadatos (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultaos %(from)s-%(to)s (%(total)s en total)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ coincidencies parciales"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d coincidencies parciales"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariáu y Recompenses"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive depende de voluntarios como tú. Acoyemos tolos niveles de compromisu, y tenemos dos categoríes principales d’ayuda que tamos buscando:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Trabayu voluntariu lleugeru:</span> si namái pues dedicar unes poques hores de vez en cuando, entá hai munches maneres nes que pues ayudar. Recompensamos a los voluntarios consistentes con <span %(bold)s>🤝 membresíes nel Archivu d'Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Trabayu voluntariu intensu (recompenses de 50 a 5.000 USD):</span> si pues dedicar munchu tiempu y/o recursos a la nuesa misión, encantaríanos trabayar más de cerca contigo. Eventualmente pues uníte al equipu internu. Anque tenemos un presupuestu axustáu, podemos dar <span %(bold)s>💰 recompenses monetaries</span> pol trabayu más intensu."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si nun pues dar el to tiempu como voluntariu, igual puedes ayudanos muncho <a %(a_donate)s>donando dineru</a>, <a %(a_torrents)s>sembrando los nuesos torrents</a>, <a %(a_uploading)s>subiendo llibros</a> o <a %(a_help)s>falando a los tos amigos sobre l’Archivu d’Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Empreses:</span> ufiertamos accesu direutu y de gran velocidá a les nueses coleiciones a cambéu d’una donación a nivel empresarial o a cambéu de nueves coleiciones (p. ex. escaneos nuevos, datasets OCR, enriquecer los nuesos datos). <a %(a_contact)s>Contacta con nós</a> si esti ye’l to casu. Consulta tamién la nuesa <a %(a_llm)s>páxina de LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariáu lleugeru"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si tienes unes poques hores llibre, pues ayudanos de munches maneres. Asegúrate de uníte al <a %(a_telegram)s>chatu de voluntarios en Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Como señal d’agradecimientu, solemos dar 6 meses de “Bibliotecariu Afortunáu” por llogros básicos, y más por trabayu voluntariu continuu. Tolos llogros requierin trabayu de calidá alta — el trabayu descuidado dañanos más de lo que ayuda y vamos rechazalo. Por favor <a %(a_contact)s>mándanos un email</a> cuando llegues a un llogru."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tarea"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Llogru"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Espardiendo la palabra d'El Archivu d'Anna. Por exemplu, recomendando llibros en AA, enllazando a les nueses entrades de blog, o xeneralmente dirixendo a la xente al nuesu sitiu web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s enllaces o captures de pantalla."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Estes deberían amosate informando a dalguién sobre El Archivu d'Anna, y ellos agradeciéndote."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Mejora metadatos <a %(a_metadata)s>enllazando</a> con Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Pue usar la <a %(a_list)s >llista d'incidencies aleatories de metadata</a> como puntu de partida."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Asegúrate de dexar un comentariu sobre les incidencies que fixes, pa que otros nun dupliquen el to trabayu."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s enllaces de rexistros que meyorasti."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traduciendo</a> el sitiu web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traducción completa d’un idioma (si nun taba cerca de completase yá)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Mejora la páxina de Wikipedia del Archivu d’Anna nel to idioma. Inclúi información de la páxina de Wikipedia d’AA n’otros idiomes, y del nuesu sitiu web y blog. Añade referencies a AA n’otres páxines relevantes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Enllaz al historial d’ediciones amosando que fixisti contribuciones significatives."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Cumplir peticiones de llibros (o artículos, etc.) nos foros de Z-Library o Library Genesis. Nun tenemos el nuesu propiu sistema de peticiones de llibros, pero espeyamos eses biblioteques, asina que meyorales fai que l’Archivu d’Anna tamién seya meyor."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s enllaces o captures de pantalla de peticiones que cumplisti."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Tares pequeñes publicaes nel nuesu <a %(a_telegram)s>chatu de voluntarios en Telegram</a>. Xeneralmente pa membresía, a vegaes por pequeñes recompenses."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Tares pequeñes publicaes nel nuesu grupu de charrera de voluntarios."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depende de la tarea."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Recompenses"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Siempre tamos buscando xente con bones habilidaes de programación o seguridá ofensiva pa implicase. Puees faer una contribución importante a la preservación del legáu de la humanidá."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Como agradecimientu, damos membresíes por contribuciones significatives. Como un gran agradecimientu, damos recompenses monetaries por trabayos particularmente importantes y difíciles. Esto nun se debería ver como un sustitutu d'un trabayu, pero ye un incentivu adicional y pue ayudar con costes incurríos."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "La mayoría del nuesu códigu ye de fonte abierta, y tamién vamos pidir que'l to códigu lo seya cuando s'otorgue la recompensa. Hai dalgunes esceiciones que podemos discutir de manera individual."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Les recompenses otórguense a la primer persona que complete una tarea. Siéntete llibre de comentar nun ticket de recompensa pa que otros sepan que tas trabayando en daqué, pa que otros puedan aguantase o contactate pa trabayar en conxuntu. Pero ten en cuenta que otros tamién son llibre de trabayar nel mesmu y tratar de ganate. Sicasí, nun otorgamos recompenses por trabayu descuidado. Si se faen dos presentaciones de alta calidá cerca una de la otra (dientro d'un día o dos), podemos escoyer otorgar recompenses a dambes, a la nuesa discreción, por exemplu 100%% pa la primer presentación y 50%% pa la segunda presentación (asina que 150%% en total)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Pa les recompenses más grandes (especialmente les recompenses de scraping), por favor contacta con nós cuando hayas completáu ~5%% d'ello, y tas seguru de que'l to métodu va escalar al hito completu. Vas tener que compartir el to métodu con nós pa que podamos dar retroalimentación. Tamién, d'esta manera podemos decidir qué faer si hai múltiples persones acercándose a una recompensa, como potencialmente otorgala a múltiples persones, animar a la xente a trabayar en conxuntu, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVISU: les tarees de gran recompensa son <span %(bold)s>difíciles</span> — pue ser sabiu entamar con les más fáciles."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Dir a la nuesa <a %(a_gitlab)s>llista d'issues en Gitlab</a> y ordenar por “Prioridá d'etiqueta”. Esto amuesa más o menos l'orde de les tarees que nos importen. Les tarees ensin recompenses explícites tamién son elegibles pa membresía, especialmente aquelles marcaes como “Aceptáu” y “Favoritu d'Anna”. Pue que quieras entamar con un “Proyeutu inicial”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Actualizaciones sobre <a %(wikipedia_annas_archive)s>L'Archivu d'Anna</a>, la mayor biblioteca verdaderamente abierta de la hestoria humana."

#, fuzzy
msgid "layout.index.title"
msgstr "L'Archivu d'Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "La biblioteca de datos abiertos y códigu abiertu más grande del mundu. Espeyos de Sci-Hub, Library Genesis, Z-Library y más."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Buscar nel Archivu d'Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "¡L'Archivu d'Anna necesita la to ayuda!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Munchos intenten tumbanos, pero nós lluchamos."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si donas agora, recibes <strong>el doble</strong> de descarges rápides."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Válidu hasta'l final d'esti mes."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donativos"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "¡Salvar el conocimientu humanu: un gran regalu de navidá!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Sorprende a un ser queríu, regálai una cuenta con membresía."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Pa aumentar la resiliencia de L'Archivu d'Anna, tamos buscando voluntarios pa executar espeyos."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "¡El regalu perfectu de San Valentín!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Tenemos un métodu nuevu de donación disponible: %(method_name)s. Por favor considera %(donate_link_open_tag)sdonar</a> — nun ye baratu caltener esti sitiu web, y la to donación verdaderamente marca la diferencia. Munches gracies."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Tamos faciendo una recaldación de fondos pa <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">respaldar</a> la mayor biblioteca de cómics en l'ombra del mundu. ¡Gracies pol to sofitu! <a href=\"/donate\">Dona.</a> Si nun pues donar, considera ayudanos comentándolu a los tos amigos y siguiéndonos en <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Descargues recientes:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Buscar"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Ameyorar metadatos"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariáu y Recompenses"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Actividá"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorador de Códigos"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Datos de LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Aniciu"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "El Software d'Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Traducir ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Aniciar sesión / Rexistrase"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Cuenta"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "L'Archivu d'Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Mantente en contautu"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "Reclamaciones de DMCA / drechos d'autor"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avanzáu"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Seguridá"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatives"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non afiliáu"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Esti ficheru pue tener problemes."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Descarga rápida"

#, fuzzy
msgid "page.donate.copy"
msgstr "copiar"

#, fuzzy
msgid "page.donate.copied"
msgstr "¡copiáu!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Anterior"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Siguiente"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "¡namái esti mes!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>pausó</a> la subida de nuevos artículos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Escueya una opción de pagu. Damos descuentos pa pagos con criptomonedes %(bitcoin_icon)s, porque tenemos (muches) menos comisiones."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Escueya una opción de pagu. Anguaño namái tenemos pagos con criptomonedes %(bitcoin_icon)s, yá que los procesadores de pagu tradicionales refuguen trabayar con nós."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nun podemos aceptar tarxetes de créditu/débito direutamente, porque los bancos nun quieren trabayar con nós. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Sicasí, hai delles maneres d'usar tarxetes de créditu/débito de toes formes, usando los nuestros otros métodos de pagu:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Descargues lentes y esternes"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descargues"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si usas criptomonedes por primer vegada, suxerimos usar %(option1)s, %(option2)s o %(option3)s pa comprar y donar Bitcoin (la criptomoneda orixinal y más usada)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 enllaces de rexistros que meyoraste."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 enllaces o captures de pantalla."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 enllaces o captures de pantalla de peticiones que cumplisti."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si tas interesáu en espeyar estos datasets pa <a %(a_faq)s>archivu</a> o <a %(a_llm)s>entrenamientu de LLM</a>, por favor contáctanos."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si tas interesáu en espeyar esti conxuntu de datos pa <a %(a_archival)s>archivu</a> o con fines de <a %(a_llm)s>entrenamientu de LLM</a>, por favor, ponte en contactu con nós."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sitiu web principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Información de países del ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si tas interesáu en espeyar esti conxuntu de datos pa fines de <a %(a_archival)s>archivu</a> o <a %(a_llm)s>entrenamientu de LLM</a>, por favor contáctanos."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L'Agencia Internacional del ISBN llanz regularmente los rangos que-yos destinó a les agancies nacionales del ISBN. D'esta manera podemos derivar a qué país, rexón o grupu llingüísticu pertenez esti ISBN. Actualmente usamos estos datos de manera indirecta, a través de la biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Recursos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Caberos actualizáu: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sitiu web del ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadatos"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Escluyendo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "La nuesa inspiración pa coleutar metadatos ye la meta d'Aaron Swartz de “una páxina web pa cada llibru publicáu”, pa la cual creó <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Esi proyeutu foi bien, pero la nuesa posición única permítenos consiguir metadata que ellos nun puen."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Otra inspiración foi el nuesu deséu de saber <a %(a_blog)s>cuántos llibros hai nel mundu</a>, pa poder calcular cuántos llibros nos queden por salvar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Pa dar a toos la oportunidá de descargar ficheros de baldre, necesites esperar <strong>%(wait_seconds)s segundos</strong> enantes de poder descargar esti ficheru."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Refrescar la páxina automáticamente. Si pierdes la ventana de descarga, el temporizador va reiniciase, asina que se recomienda refrescar automáticamente."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Descargar agora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Convertir: usa ferramientes en llinia pa convertir ente formatos. Por exemplu, pa convertir ente epub y pdf, usa <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descarga'l ficheru (admitense pdf o epub), dempués <a %(a_kindle)s>unvíalu a Kindle</a> usando la web, l'aplicación o corréu electrónicu. Ferramientes útiles: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Sofitar a los autores: Si te presta esto y pues permitílu, considera mercar l'original, o sofitar a los autores direutamente."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Sofita les biblioteques: Si esto ta disponible na to biblioteca llocal, considera pidirlo prestáu de baldre ellí."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Non disponible direutamente en masa, solo en semi-masa detrás d'un muru de pagu"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s L'Archivu d'Anna xestiona una coleición de <a %(isbndb)s>metadatos de ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb ye una empresa que raspa varies llibreríes en llinia pa atopar metadatos del ISBN. L'Archivu d'Anna vien faciendo copies de seguridá de los metadatos de llibros de ISBNdb. Estos metadatos tán disponibles a través del Archivu d'Anna (anque actualmente nun tán nel buscador, salvo que busques explícitamente un númberu ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Pa detalles técnicos, ver embaxo. Nalgún momentu podemos usalu pa determinar qué llibros tán faltando nes biblioteques sombra, col fin de priorizar qué llibros atopar y/o escanear."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "El nuesu artículu del blog sobre estos datos"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Raspáu de ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actualmente tenemos un solu torrent, que contién un archivu de 4,4GB en formatu gzip <a %(a_jsonl)s>JSON Lines</a> (20GB descomprimíu): “isbndb_2022_09.jsonl.gz”. Pa importar un archivu “.jsonl” en PostgreSQL, pues usar dalgo como <a %(a_script)s>esti script</a>. Incluso pues pipealu direutamente usando dalgo como %(example_code)s pa que se descomprima en tiempu real."

#~ msgid "page.donate.wait"
#~ msgstr "Por favor, espera al menos <span %(span_hours)s>dos hores</span> (y recarga esta páxina) enantes de ponete en contautu con nós."

#~ msgid "page.codes.search_archive"
#~ msgstr "Buscar en l’Archivu d’Anna por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donar usando Alipay o WeChat. Pues escoyer ente estos na páxina siguiente."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Espardir la pallabra sobre l’Archivu d’Anna en redes sociales y foros en llinia, recomendando llibros o llistes en AA, o contestando entrugues."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La coleición de ficción desvióse pero entá tien <a %(libgenli)s>torrents</a>, anque nun se modernizó dende 2022 (tenemos descarges direutes)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s L'Archivu d'Anna y Libgen.li xestionen conxuntamente coleiciones de <a %(comics)s>cómics</a> y <a %(magazines)s>revistes</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nun hai torrents pa coleiciones de ficción rusa y documentos estándar."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Nun hai torrents disponibles pa'l conteníu adicional. Los torrents que tán na páxina web de Libgen.li son espeyos d'otros torrents listaos equí. L'únicu esceición son los torrents de ficción que entamen en %(fiction_starting_point)s. Los torrents de cómics y revistes llánzanse como una collaboración ente l'Archivu d'Anna y Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "D'una coleición <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> orixe exactu desconocíu. Parcialmente de the-eye.eu, parcialmente d'otros fontes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

