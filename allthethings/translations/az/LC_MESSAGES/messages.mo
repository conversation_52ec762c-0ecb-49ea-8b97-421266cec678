��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  sd e  �f -   ]h '  �h G  �i �  �k �  �m /  yo N   �p �   �p G   ~q }   �q   Dr y  Ht   �u '  �v   �x b  z G  h| 5  �~   � *  � �  � L   ��   � e   � Q  ~� "   Љ Z  � G   N� #   ��    ��     ، L   �� (   F�    o� 3   �� +   �� )   �    
� .   � 9  N� 4  �� j   �� H   (�    q�    ��    ��    ��    ��    ޑ 	   �    ��    	� '   �    8�    >� 	   T� 
   ^�    l�    x�    ��    �� x  Ԓ �  M� �   ܖ -   �� �  �� �   ?�   � �   � %   � �   � %   �� �   Μ *   ��   �� %   ɞ ~  � 7   n� Y   ��     � ~  	� j  �� �  � �  ݥ    a� S   t� g   ȧ �   0� 9   � 1   &�    X� M   ة _   &� D   ��    ˪ z   Ϫ �  J� M   (� �   v� �    � M   � m  .� ;   �� W  ذ �   0� �   �� e   p� �   ֳ �   �� G   ?� �   �� �   �� s   V� V   ʷ z   !� 
   �� �   �� )  _� G   ��    Ѻ �   ں    ��    �� �  �� �   � 
  ʾ �  ؿ �   u� Y  [�   �� I   �� Z   � b   y� �   �� m   r� Z   �� B   ;� �   ~� i   � K   ��    �� �   �� `   �� �   �� �   ��    P�   j� #  q� R  �� �  �� �   o� �    � [  �� �   U� �   � �   �� X  �� �   �� 
  ��    �� �   �� �   v� �   ?� a  � �   �� �   � �   �� �  W� �   7� ?  ��   � "   *� n   M� �   �� V   k� �  �� �   O� �   � �   �� &   � p  F� �   �� �  ��    @� o   X� �   �� �   �� T  }� 
  ��   �� i  �� .   \� �  �� �  � #  �� �  �� 5  �  8  � c    �   y     �     F  � �  +   �   � �   
    �
 �  �
 �  V �   �
 
   � �   � J  � �   � Q  � �    [   �    6 �  L p  �    J �   Y R  W v  �   ! �  ' {   � ]   W    �  :   �!    " }  !" H  �# I  �$ :   2& G   m& M  �& �   ) �   �) e   F* �  �* )   P,    z, �  �, �   2. �   / \   �/ �   �/ +   �0 1  �0 �   �1    �2 
   �2 -   3 C   33 E   w3 I   �3 b   4 �   j4    �4 /   5 R   15 Y   �5 (   �5 :   6 8   B6 �   {6    7 (   7 
  F7 Q  T8   �9   �: �   �;   |<   �=    �> �   �> �   �? �   K@ t  �@ �  hB   �C �  F �  �G G   zI 1   �I 1   �I �   &J 5  �J �  M J  �N |  2P    �Q Z  �R 2  U +   >V .  jV i  �W �  Y    �Z   �Z w  �[ �   +] R  �] �   _    	` .   `    J` 
   �` �  �` �  �c }  =e �   �g �   �h    �i 
  �i   �j G   �k v   �k   ul   �m �   �n �  Fo j   
q @  xq    �r %  �r �   �t   �u �  �w �  �z �   q|    @} �   I}   +~    E� �  X� �  � q  �� _  "� 4  �� Q  �� G   	� �  Q� �   C� }   ?� U   �� i   � 	   }� 7   �� "   �� ,   � "   �    2� �   J� V  �   Y� =  e� �   ��    �� �  �� K  �� d  ̞ �  1� �  ڡ �  ��    '�   0�    8�    D�   X� �  \�   � �  �� 
   �� �  �� �  q� -  c� 4  ��   ƹ f  ߼ m  F�    �� �   �� �   ��   %�   >� �   B� �   � �   �� �  s�    
� �    � I   �� T   ;�    �� :   �� P   �� n  -� �  �� �  d� 5   3�   i� �   ��    "� �   +� D   
� �   R� �   �� ?   �� l   �    s� v  �� r  � �  t� �  V� �   ��    l� s   �   ��    � �   � �  �� �   s� L   9� I   �� �  �� -   r� �  �� �  2�   ��    � r   6� �   �� ]  M� �  ��    x� �   �� 8   �� �   �� �   �� 	  `� �   j� P   M� -   ��   �� ;   �� �  � e  �  �   ; -  , ]   Z 1   � �   � ^  � �  � "  �	 �  � 5   �
 �       � Z   �   a   [ �  v -   [ �  � /   � �  � _   �     x   F  � �  �   �! S  �" 1  $ x  =& �   �) �  Y* �  @, �   3. >  �. e  2 0   j3 �   �3 �   $4 #   �4    �4 S  �4 �   '6 <   �6 %  7 �  D8 '  : R  8; �  �= �   W? �  @ @  �A    7C �   LC    !D �  *D &   �E    �E    F    F !   'F    IF    [F    tF    �F    �F    �F    �F 
   �F    �F    �F 
    G '   G    3G 	   7G    AG    IG �   MG Y   8H )   �H    �H 7   �H "   I 8   *I <   cI &   �I 
   �I    �I    �I    �I    J    "J    4J    ;J    KJ    RJ .   gJ *   �J    �J    �J 0   �J 2   (K /   [K    �K +   �K :   �K    L Z   &L H   �L    �L b   �L D   4M    yM    �M !   �M    �M    �M !   �M    N    )N    >N    FN    ZN    gN    �N 	   �N 
   �N    �N     �N    �N    �N 	   �N    �N 	    O    
O    "O    (O 	   /O    9O    IO    UO    rO #   zO    �O    �O    �O    �O    �O (   �O 	   P 	   $P     .P    OP    VP    iP    uP    �P 
   �P    �P ~   �P �   4Q s   �Q �   rR   +S �   0U    �U '   �U    �U    V    V    !V 
   9V -   DV `   rV J   �V _   W &   ~W A   �W )   �W s   X m   �X �   �X y   �Y =   MZ +   �Z    �Z    �Z    �Z    �Z    �Z    �Z    [    [    7[    @[    O[    d[    }[    �[    �[    �[    �[    �[ 	   �[    \    \    !\    3\    R\ (  f\    �] 
   �]    �]     �]    �] F   �] ^   ^ 6   w^ E   �^ I   �^    >_    F_    N_ �   Q_    �_    �_ ,   �_ �    `    �`    �` �   �` )   ga �  �a r   %e �   �e �    f �   �f 5   �g (  h �   Ci l  �i   :k    Nl 	   gl X   ql S   �l ^   m d   }m q   �m D   Tn �   �n !   o ,   Ao    no    wo U   �o #   �o    p    p    %p    .p y   Hp    �p 5   �p Q   q     Vq    wq G   �q Z   �q �  5r    �s    �s �   �s    �t    �t    �t    �t    �t ,   �t �  u    �v 
   �v    �v    �v >  �v !   7x    Yx    fx w   nx    �x    �x ,   �x    #y    @y )   Gy �   qy    z    )z b   >z    �z    �z 	   �z    �z /   �z �   {    �{ +   �{ �   �{ y   �| b   }    |} '  �} I   �~    �~ 7       M �   f �   A�    � a   � �   � �   R� "   A� "   d�    �� �  �� +   K� $   w� )   �� *   ƅ #   � �   �    ֆ    �� .   	� S   8� 	   ��    ��     �� $   ч �  �� �  �� d  #� 9   �� 9       ��     	� @  *� �   k� �   U�    .�   K� �  \� (   V� #   � s  �� X  �    p�    }� 0   �� 	   �� o  Ǚ    7� �  N� �  ՜ 	  ��    �� �   �� =   E� *   �� s   �� u  "�   �� �   �� �  u� u   B� 1  �� _   � �   J� �   � Z   �� �   � 3   �� 6   .�    e�    w�    �� *   �� '   ͫ W   �� -   M� 	   {� 8   �� K  �� 7   
�   B� �   G� �   � )   ��    ��    ̰    � &   �� "   "� /   E� !   u� �   �� '   t� �   ��    w� �   �� �   y�    �� 4  |� �   �� ?   �� �   �� 	   V� }  `� x   ޹ !   W� �  y�    0� #   =�    a�    q� :   ��    ˼    Ӽ Z   ۼ �   6� ,  �    <�    K� ,   Q� �   ~� I  D� �   �� �   l� #   �� #   �    =�    V�    u�    ��    �� \   �� 3   �   9� p   Q�    �� �   �� |   `� V   �� �   4� r   �� X   4�    �� k   �� S   � �   U� q   � O   x�    ��   �� o   �� I   L� u   �� d   � K   q�    �� 2   �� z    � �   {� >    � �   _�    %�   -� I   G� e   �� �   ��    �� 4  ��   �� !   �� *   �    .�    6�   <� 1   T� b   �� �   �� �   �� �   �� �   [� �   /� �   �� �   �� x   I� K  �� s   � �  �� �   � �   �� 
   �� 
   �� 
   �� �   
� 
   �� 
   �� V   �� v   '�   �� 
   �� �   �� =   J� �   �� =   � �   N� {   �� 
   d� �   r� 
   � 
   "� 
   0� b  >� �   �� �  7�    ��    �    $� �   3�    �� $   �   9� �   X� !   �    <� ,   L� 8   y� ;   �� ?   �� &   .� &   U�   |�    �� �  ��    n� �   �� W   � �   k� +  '�   S� e  \� �   �� �   u� �        �  �  �  #   H r  l   � 9  � �    W  � �   � �   � �   f	 .   �	 >   "
    a
 7   {
 
   �
    �
    �
 �   �
    � �   � )   g    �    � 
   � '   � �   � M   ^
 3   �
 K  �
 7   ,    d    q     y ,   �    �    �    �    �    �    �    �    � +     �   ,    � 
   �    � 
   �    
 
       % 
   2    @    V *   q �   �     3   . q   b �   � �   � �   J    �   ) ^  � 
   O �   Z 4   � Q   # P   u �   �   m T   z �   � 
   W r   e    � �   X    
         2    J    f (   r    �    �    �    �    �    �        ,    B    a    h    u    �     �    � %   � (   � �     �   �  -   /! (   ]! �   �! n   " {   ~" *   �" ,   %# 7   R# @   �# G   �# 0   $ �   D$ $   % 1  E& *   w' Q   �' �   �' +   �(   �( �   �) �   �* R   :+    �+ �   �+ �   s, �   (- .   �- u   �-   U. %   Y/ 7   / &   �/ P   �/ r   /0 e   �0    1    1    1 *    1 �   K1 ;   �1    2 4   ?2 '   t2    �2 !   �2 J   �2 &   &3 t   M3 E   �3 ,  4 ^   55 Y   �5 �   �5 B   �6 !   7     -7 !   N7     p7 !   �7     �7 !   �7 E   �7 ;   <8 �  x8 X   ; 5   \; A   �; -   �;    < �   < �   �< �   != �   > 	   �> �   ?     �?    �? �   �? "   �@ '   �@ J   A 3   LA b   �A h   �A O   LB    �B !   �B �   �B 6   �C "   �C b   �C    ZD N   [E �   �E u   ^F c   �F �   8G #   �G 9   �G �   )H    �H q   �H $   II �   nI 7   @J L   xJ %   �J *   �J �   K X   �K P   OL �   �L s   RM N   �M �   N �   �N 	   DO    NO U   aO a   �O    P    ,P    IP    YP    pP -   �P �   �P d   ;Q    �Q    �Q (   �Q )   R ?   /R �   oR E   �R �   :S w   �S -   IT �   wT �   �T <   �U Y   +V    �V J   �V C   �V    )W �   >W {   �W 1   AX R   sX    �X 0   �X Z   Y    bY �   wY (   Z    8Z -   RZ t   �Z �   �Z G   �[    0\    G\ G   ^\    �\ b   �\ =    ] �   ^] �   )^    �^ A   �^ �   (_     �_ �   ` I   �` "   +a b   Na �   �a    �b    �b    �b    �b /   �b 6   c I   <c +   �c    �c    �c &   �c @   �c A   /d 	   qd O   {d W   �d    #e $   /e 2   Te &   �e    �e e   �e �   )f I   #g    mg    ~g �   �g j  Ch _   �i    j �  j �  �k A   �m m   �m "   ?n �  bn /   �p �   #q    �q    �q 
  �q "   �s g   �s {   gt |   �t _   `u    �u t   �u I   Ov    �v ~   �v F   7w 5   ~w X   �w 5   
x I   Cx �   �x �   ;y 0   �y �   #z Z  �z �   T| /   :} u  j}   �~ �    � D  �� �   5� +   �    C� �   b� ?   � #  [�    � F   ��    F�    R� �  k�    a� �   h� Q  >� B  �� n  ӌ 5   B� S   x� j   ̎ 6   7� ?   n� W   �� [   �    b� +   o� ,   �� $   Ȑ    � @   � i   I� /   ��    � ^   �    I� �   J�    ��    � 	   (� Q   2� q   �� _   �� �   V� �   3�    �� /   Җ �   �    � �   ��   ��   �� K   �� (   �    +�    1�    8� M   <� 1   �� �   ��   J� d   O�    ��    П %   �    � i   1�    �� ?   ��    � >   �� H   ;�    ��    �� j   ��    �    � 6   0� "   g�    �� s   �� �   � n   � �   \� s   ߤ �   S�    O�    a� �   {� �   8� �   �    ƨ �   Ϩ \   ^� S   �� n   � g   ~� l   �    S� x   p�    �    ��    �    #�    6�    K�    b�    v�    ��    �� B   �� B   � 8   +� !   d� G   �� =   έ (   � %   5�    [� >   q�    �� 
   ή K   ٮ 0   %� �   V� 3   د    � #   %� 3   I� (   }�    �� �   �� f   S�    �� +  :�    f�    ��     ��    �� ;   ֳ 	   �    �    9� �   P� %   ݴ *   �    .� 
   5� 	   @� :   J�    ��   ��     ��    ζ    � #   ��    �     =�     ^� !   � &   �� =   ȷ 7   �    >� a   E� ,   ��    Ը    � 0   � Z   4� %   �� 7   �� 0   � �   � _   �� U   �    ^�    f� 	   ��    ��    ��    �� �  ܻ �   ��    �    )� .   -�    \� 2   w�    ��    �� �   �� D   ;� �   ��    q�     �� �   ��     8�    Y�    v� "   �� /   �� "   ��    �    (� 4   4�    i� 2   �� <   ��   �� [   � A   d� f   ��     
� �   .� !   ��    � c   �    z� .   ��    �� ?   �� [   � /   r� �   �� 
   u�    ��    ��    ��    ��    �� !   ��    �    /� W   F� *  ��    �� /   �� �   � �   �    ��     ��    ��    �    �    5�    H�    a�    y�    ��    ��    �� 
   ��    ��    ��    ��    ��    � (   � �   ?�   #� �  >� R  �� �  � �   � �  �� 	   �� <  ��    ��   ��   �� �  � �   �� �  �� 6   ;� �   r� =   !�    _� N   x� _   �� �   '� �   �� �   ,� �   �� �   �� t  �� #   �� �   � �   �� h   �� �   C�    �� �   � �  �� �   �� �   }� 	   t� v   ~� }   �� �   s� �   n� �   � �   ��    � *   2�    ]� D   t� /   ��    �� �   �� C   �� v   �� !   D� �   f� �    � N   �� g   � ^   i� c   �� w   ,� g   �� u   � r   �� �   �� x   |�    �� 6   � A   8� q   z� 6   ��    #� 
   /� G   :�    ��    ��    �� 3   �� ?   �� =   '�    e�    ��    ��    �� 	   �� a   �� �   � i   �� :   ��    .� &   6� 3   ]� 
   ��    ��    ��    ��    ��    ��    ��    ��    �� 	   ��    �� 
   ��    ��              0     =  
   I  
   T     _  =   g     �      �  �   �     i q   � �   � 
   �    �    �    � 	        	    �    �   � Y   k    � #   � �   �    � �   � �   2 %   �    � �    �   � e   r �   � y   �	 .    
 �   O
    �
     K   0 �   |      �   5 �   � �   [
 Y   I    � 	   �    �    �    �    �    �     �   ) l   � �   A �   � �   � l   � \    �  q g  [   � �   � >  � �  �    x �   � /  Y �   � �  R �  �  �   �" �  �#    B% R   Z% �   �% �  [& S   �'   C( 
   ])    h)    t) �   �) F   6* k   }* 6   �* z    + \   �+ m   �+ 0   f, �   �, M   T- 3   �- P   �- �   '.    %/  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: az
Language-Team: az <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Kitabxana məşhur (və qeyri-qanuni) kitabxanadır. Onlar Library Genesis kolleksiyasını götürüb asan axtarış edilə bilən hala gətiriblər. Üstəlik, yeni kitab töhfələri üçün istifadəçiləri müxtəlif üstünlüklərlə təşviq edərək çox təsirli olublar. Hazırda bu yeni kitabları Library Genesis-ə geri töhfə vermirlər. Library Genesis-dən fərqli olaraq, kolleksiyalarını asanlıqla güzgülənə bilən etmirlər, bu da geniş qorunmanı əngəlləyir. Bu, onların biznes modelinə əhəmiyyətlidir, çünki kolleksiyalarına toplu şəkildə (gündə 10-dan çox kitab) giriş üçün pul alırlar. Qeyri-qanuni kitab kolleksiyasına toplu giriş üçün pul almaqla bağlı mənəvi mühakimələr etmirik. Şübhəsiz ki, Z-Kitabxana biliklərə çıxışı genişləndirməkdə və daha çox kitab əldə etməkdə uğurlu olub. Biz sadəcə öz işimizi görmək üçün buradayıq: bu şəxsi kolleksiyanın uzunmüddətli qorunmasını təmin etmək. - Anna və komanda (<a %(reddit)s>Reddit</a>) Pirate Library Mirror-un orijinal buraxılışında (RED: <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> saytına köçürüldü), biz Z-Library-nin, böyük qanunsuz kitab kolleksiyasının bir nüsxəsini yaratdıq. Xatırlatma olaraq, bu orijinal bloq yazısında yazdığımız budur: Bu kolleksiya 2021-ci ilin ortalarına aiddir. Bu müddət ərzində Z-Kitabxana heyrətamiz sürətlə böyüyüb: təxminən 3.8 milyon yeni kitab əlavə ediblər. Orada bəzi təkrarlamalar var, əlbəttə, amma əksəriyyəti həqiqətən yeni kitablar və ya əvvəllər təqdim edilmiş kitabların daha yüksək keyfiyyətli skanları kimi görünür. Bu, böyük ölçüdə Z-Kitabxanadakı könüllü moderatorların artan sayı və təkrarlamaları aradan qaldıran toplu yükləmə sistemləri sayəsindədir. Bu nailiyyətlərə görə onları təbrik etmək istəyirik. Biz məmnuniyyətlə elan edirik ki, son güzgümüzlə avqust 2022 arasında Z-Kitabxanaya əlavə edilən bütün kitabları əldə etmişik. Həmçinin, ilk dəfə qaçırdığımız bəzi kitabları geri almışıq. Ümumilikdə, bu yeni kolleksiya təxminən 24TB-dır, bu da əvvəlkindən (7TB) çox böyükdür. Güzgümüz indi ümumilikdə 31TB-dır. Yenə də Library Genesis-ə qarşı təkrarlamaları aradan qaldırdıq, çünki bu kolleksiya üçün artıq torrentlər mövcuddur. Zəhmət olmasa, yeni kolleksiyanı yoxlamaq üçün Pirate Library Mirror-a keçin (DÜZƏLİŞ: <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a>na köçürüldü). Orada faylların necə qurulduğu və son dəfə nə dəyişdiyi haqqında daha çox məlumat var. Buradan ona keçid verməyəcəyik, çünki bu, qeyri-qanuni materialları yerləşdirməyən sadəcə bir blog veb saytıdır. Əlbəttə, toxumlama da bizə kömək etməyin əla bir yoludur. Əvvəlki torrentlərimizi toxumlayan hər kəsə təşəkkür edirik. Müsbət reaksiyaya görə minnətdarıq və bu qeyri-adi şəkildə bilik və mədəniyyətin qorunmasına maraq göstərən çox sayda insanın olmasına sevinirik. Pirate Library Mirror-a 3x yeni kitab əlavə edildi (+24TB, 3.8 milyon kitab) TorrentFreak tərəfindən müşayiət olunan məqalələri oxuyun: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a> - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak tərəfindən müşayiət olunan məqalələr: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a> Çox da uzaq olmayan keçmişdə, “kölgə-kitabxanalar” yox olmaq üzrə idi. Akademik məqalələrin böyük qanunsuz arxivi olan Sci-Hub, məhkəmə iddiaları səbəbindən yeni əsərlər qəbul etməyi dayandırmışdı. Kitabların ən böyük qanunsuz kitabxanası olan “Z-Library”, yaradıcılarının cinayət müəllif hüquqları ittihamları ilə həbs edildiyini gördü. Onlar inanılmaz şəkildə həbsdən qaçmağı bacardılar, lakin kitabxanaları heç də az təhlükə altında deyil. Bəzi ölkələr artıq bunun bir versiyasını həyata keçirir. TorrentFreak <a %(torrentfreak)s>xəbər verdi</a> ki, Çin və Yaponiya müəllif hüquqları qanunlarına AI istisnaları daxil ediblər. Bu, beynəlxalq müqavilələrlə necə əlaqələndirilir, bizə aydın deyil, lakin bu, onların yerli şirkətlərinə örtük verir, bu da gördüklərimizi izah edir. Anna Arxivi ilə bağlı — biz mənəvi inancdan köklənmiş yeraltı işimizi davam etdirəcəyik. Lakin ən böyük arzumuz işığımızı artırmaq və təsirimizi qanuni şəkildə gücləndirməkdir. Zəhmət olmasa, müəllif hüquqlarını islah edin. Z-Library bağlanma ilə üzləşəndə, mən artıq onun bütün kitabxanasını ehtiyat nüsxə etmişdim və onu yerləşdirmək üçün bir platforma axtarırdım. Bu, Anna Arxivini başlatmaq üçün mənim motivasiyam idi: əvvəlki təşəbbüslərin arxasındakı missiyanın davamı. O vaxtdan bəri biz dünyada ən böyük kölgə kitabxanasına çevrildik, müxtəlif formatlarda 140 milyondan çox müəllif hüquqları ilə qorunan mətnlərə ev sahibliyi edirik — kitablar, akademik məqalələr, jurnallar, qəzetlər və daha çox. Mənim komandam və mən ideoloqlarıq. Bu faylları qorumağın və yerləşdirməyin mənəvi cəhətdən doğru olduğuna inanırıq. Dünyadakı kitabxanalar maliyyə kəsintiləri ilə üzləşir və biz insanlığın mirasını korporasiyalara da etibar edə bilmərik. Sonra AI gəldi. LLM-lər yaradan demək olar ki, bütün böyük şirkətlər bizim məlumatlarımız üzərində təlim keçmək üçün bizimlə əlaqə saxladılar. Əksəriyyəti (amma hamısı deyil!) ABŞ-da yerləşən şirkətlər işimizin qanunsuz təbiətini anladıqdan sonra yenidən düşündülər. Əksinə, Çin şirkətləri bizim kolleksiyamızı həvəslə qəbul etdilər, görünür, onun qanuniliyindən narahat deyillər. Bu, Çinin demək olar ki, bütün əsas beynəlxalq müəllif hüquqları müqavilələrinə imza atan bir ölkə kimi rolu nəzərə alınaraq diqqətəlayiqdir. Biz təxminən 30 şirkətə yüksək sürətli giriş verdik. Onların əksəriyyəti LLM şirkətləridir və bəziləri məlumat brokerləridir, onlar bizim kolleksiyamızı yenidən satacaqlar. Əksəriyyəti çinlidir, lakin biz ABŞ, Avropa, Rusiya, Cənubi Koreya və Yaponiyadan olan şirkətlərlə də işləmişik. DeepSeek <a %(arxiv)s>etiraf etdi</a> ki, əvvəlki versiya bizim kolleksiyamızın bir hissəsi üzərində təlim keçib, baxmayaraq ki, onların son modeli haqqında çox danışmırlar (yəqin ki, o da bizim məlumatlarımız üzərində təlim keçib). Qərb LLM-lər yarışında və nəticədə AGI-də irəlidə qalmaq istəyirsə, müəllif hüquqları mövqeyini yenidən nəzərdən keçirməli və tezliklə bunu etməlidir. Bizim mənəvi mövqeyimizlə razılaşıb-razılaşmamağınızdan asılı olmayaraq, bu, indi iqtisadiyyat və hətta milli təhlükəsizlik məsələsinə çevrilir. Bütün güc blokları süni super-alimlər, super-hakerlər və super-ordular yaradır. Məlumat azadlığı bu ölkələr üçün sağ qalma məsələsinə çevrilir — hətta milli təhlükəsizlik məsələsinə. Komandamız dünyanın hər yerindən gəlir və bizim xüsusi bir uyğunlaşmamız yoxdur. Lakin biz güclü müəllif hüquqları qanunlarına malik ölkələri bu mövcud təhlükəni islahatlar üçün istifadə etməyə təşviq edərdik. Bəs nə etməli? Bizim ilk tövsiyəmiz sadədir: müəllif hüquqları müddətini qısaltmaq. ABŞ-da müəllif hüquqları müəllifin ölümündən sonra 70 il müddətinə verilir. Bu, absurd bir şeydir. Biz bunu patentlərlə uyğunlaşdıraraq, müraciətdən sonra 20 il müddətinə verə bilərik. Bu, kitablar, məqalələr, musiqi, incəsənət və digər yaradıcı əsərlərin müəlliflərinin öz səyləri üçün tam kompensasiya almaları üçün kifayət qədər vaxt olmalıdır (film adaptasiyaları kimi uzunmüddətli layihələr daxil olmaqla). Sonra, ən azı, siyasətçilər mətnlərin kütləvi qorunması və yayılması üçün istisnalar daxil etməlidirlər. Əgər fərdi müştərilərdən itirilmiş gəlir əsas narahatlıqdırsa, şəxsi səviyyədə yayım qadağan edilə bilər. Bunun əvəzində, geniş arxivləri idarə edə bilənlər — LLM-ləri təlim edən şirkətlər, kitabxanalar və digər arxivlər — bu istisnalarla əhatə olunacaq. Müəllif hüquqları islahatları milli təhlükəsizlik üçün zəruridir Qısa versiya: Çin LLM-ləri (DeepSeek daxil olmaqla) mənim qanunsuz kitab və məqalə arxivim üzərində təlim keçib — dünyada ən böyüyü. Qərb müəllif hüquqları qanununu milli təhlükəsizlik məsələsi olaraq yenidən nəzərdən keçirməlidir. Zəhmət olmasa daha ətraflı məlumat üçün <a %(all_isbns)s>orijinal bloq yazısına</a> baxın. Bunu təkmilləşdirmək üçün bir çağırış elan etdik. Birinci yer üçün 6,000 dollar, ikinci yer üçün 3,000 dollar və üçüncü yer üçün 1,000 dollar mükafat verəcəyik. Böyük maraq və inanılmaz təqdimatlar səbəbindən mükafat fondunu bir qədər artırmağa və dörd nəfərə üçüncü yer üçün hər birinə 500 dollar verməyə qərar verdik. Qaliblər aşağıda göstərilib, lakin bütün təqdimatlara <a %(annas_archive)s>buradan</a> baxmağı və ya <a %(a_2025_01_isbn_visualization_files)s>birləşdirilmiş torrentimizi</a> yükləməyi unutmayın. Birinci yer 6,000 dollar: phiresky Bu <a %(phiresky_github)s>təqdimat</a> (<a %(annas_archive_note_2951)s>Gitlab şərhi</a>) tam olaraq istədiyimiz hər şeydir və daha çox! Xüsusilə inanılmaz dərəcədə çevik vizuallaşdırma seçimlərini (hətta xüsusi kölgələndiriciləri dəstəkləyən) çox bəyəndik, lakin geniş bir əvvəlcədən təyin edilmiş siyahı ilə. Həmçinin hər şeyin nə qədər sürətli və hamar olduğunu, sadə tətbiqi (hətta arxa planı yoxdur), ağıllı minimapı və onların <a %(phiresky_github)s>bloq yazısında</a> geniş izahını bəyəndik. İnanılmaz iş və layiqli qalib! - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Qəlbimiz minnətdarlıqla doludur. Qeyd olunmağa layiq ideyalar Nadirlik üçün göydələnlər Datasets-ləri müqayisə etmək üçün çoxlu sürgülər, sanki DJ-siniz. Kitabların sayı ilə ölçü çubuğu. Gözəl etiketlər. Sərin standart rəng sxemi və istilik xəritəsi. Unikal xəritə görünüşü və filtrlər Qeydlər və həmçinin canlı statistika Canlı statistika Bəyəndiyimiz bəzi ideyalar və tətbiqlər: Bir müddət davam edə bilərik, amma burada dayanaq. Bütün təqdimatlara <a %(annas_archive)s>buradan</a> baxın və ya <a %(a_2025_01_isbn_visualization_files)s>birləşdirilmiş torrentimizi</a> yükləyin. Çoxlu təqdimatlar var və hər biri UI və ya tətbiq baxımından unikal bir perspektiv gətirir. Ən azı birinci yerin təqdimatını əsas veb saytımıza daxil edəcəyik və bəlkə də başqalarını da. Nadir kitabların müəyyən edilməsi, təsdiqlənməsi və sonra arxivləşdirilməsi prosesini necə təşkil edəcəyimizi düşünməyə başlamışıq. Bu sahədə daha çox şey gələcək. İştirak edən hər kəsə təşəkkür edirik. Bu qədər insanın qayğı göstərməsi heyrətamizdir. Datasets-lərin asan dəyişdirilməsi üçün sürətli müqayisələr. Bütün ISBN-lər CADAL SSNO-ları CERLALC məlumat sızması DuXiu SSID-ləri EBSCOhost’un eBook İndeksi Google Kitablar Goodreads İnternet Arxivi ISBNdb ISBN Qlobal Nəşriyyatçılar Reyestri Libby Anna Arxivi faylları Nexus/STC OCLC/Worldcat OpenLibrary Rusiya Dövlət Kitabxanası Trantor İmperial Kitabxanası İkinci yer 3,000 dollar: hypha “Düzbucaqlı və kvadrat formalar riyazi baxımdan xoşdur, lakin xəritə kontekstində üstün lokalite təmin etmir. Məncə, bu Hilbert və ya klassik Mortonun asimmetriyası qüsur deyil, xüsusiyyətdir. İtaliyanın məşhur çəkmə şəklindəki konturu xəritədə dərhal tanınan etdiyi kimi, bu əyrilərin unikal "xüsusiyyətləri" də kognitiv işarələr kimi xidmət edə bilər. Bu fərqlilik məkan yaddaşını gücləndirə və istifadəçilərə özlərini istiqamətləndirməyə kömək edə bilər, potensial olaraq müəyyən bölgələri tapmağı və ya naxışları fərq etməyi asanlaşdırır.” Başqa bir inanılmaz <a %(annas_archive_note_2913)s>təqdimat</a>. Birinci yer qədər çevik deyil, lakin əslində onun makro səviyyəli vizuallaşdırmasını birinci yerdən üstün tutduq (məkan dolduran əyri, sərhədlər, etiketləmə, vurğulama, sürüşdürmə və böyütmə). Joe Davis tərəfindən verilən bir <a %(annas_archive_note_2971)s>şərh</a> bizimlə rezonans doğurdu: Vizuallaşdırma və göstərmə üçün hələ də çoxlu seçimlər, həmçinin inanılmaz dərəcədə hamar və intuitiv istifadəçi interfeysi. Möhkəm ikinci yer! - Anna və komanda (<a %(reddit)s>Reddit</a>) Bir neçə ay əvvəl biz məlumatlarımızın ISBN məkanını göstərən ən yaxşı vizuallaşdırmanı yaratmaq üçün <a %(all_isbns)s>$10,000 mükafat</a> elan etdik. Hansı faylları artıq arxivləşdirib/arxivləşdirmədiyimizi göstərməyə vurğu etdik və daha sonra neçə kitabxananın ISBN-ləri saxladığını təsvir edən bir dataset təqdim etdik (nadirlik ölçüsü). Biz cavabların çoxluğundan təsirləndik. Çoxlu yaradıcılıq var idi. İştirak edən hər kəsə böyük təşəkkür: sizin enerjiniz və həvəsiniz yoluxucudur! Nəticədə biz aşağıdakı suallara cavab vermək istədik: <strong>dünyada hansı kitablar mövcuddur, biz artıq neçə kitabı arxivləşdirmişik və növbəti hansı kitablara diqqət yetirməliyik?</strong> Bu sualların çox insanı maraqlandırdığını görmək əladır. Biz özümüz sadə bir vizualizasiya ilə başladıq. 300 kb-dan az bir ölçüdə, bu şəkil insanlıq tarixində indiyə qədər toplanmış ən böyük tam açıq “kitab siyahısını” qısa şəkildə təmsil edir: Üçüncü yer 500 dollar #1: maxlion Bu <a %(annas_archive_note_2940)s>təqdimatda</a> müxtəlif baxış növlərini, xüsusilə müqayisə və nəşriyyat baxışlarını çox bəyəndik. Üçüncü yer 500 dollar #2: abetusk Ən cilalanmış istifadəçi interfeysi olmasa da, bu <a %(annas_archive_note_2917)s>təqdimat</a> bir çox qutuları işarələyir. Xüsusilə onun müqayisə xüsusiyyətini bəyəndik. Üçüncü yer 500 dollar #3: conundrumer0 Birinci yer kimi, bu <a %(annas_archive_note_2975)s>təqdimat</a> bizi çevikliyi ilə heyran etdi. Nəticədə bu, güclü istifadəçilər üçün maksimal çeviklik təmin edərkən, orta istifadəçilər üçün sadəlik saxlayan əla vizuallaşdırma alətini yaradır. Üçüncü yer 500 dollar #4: charelf Mükafat alan son <a %(annas_archive_note_2947)s>təqdimat</a> olduqca sadədir, lakin çox bəyəndiyimiz bəzi unikal xüsusiyyətlərə malikdir. Xüsusilə bir ISBN-i əhatə edən neçə datasetin populyarlıq/etibarlılıq ölçüsü kimi göstərilməsini bəyəndik. Həmçinin müqayisələr üçün opasite sürgüsünün sadəliyini, lakin effektivliyini çox bəyəndik. $10,000 ISBN vizuallaşdırma mükafatının qalibləri Qısa versiya: $10,000 ISBN vizuallaşdırma mükafatına inanılmaz təqdimatlar aldıq. Arxa fon Anna Arxivi bütün bəşəriyyətin biliklərini ehtiyat nüsxə etmək missiyasını, hansı kitabların hələ də mövcud olduğunu bilmədən necə yerinə yetirə bilər? Bizə bir TODO siyahısı lazımdır. Bunu xəritələşdirməyin bir yolu, 1970-ci illərdən bəri hər bir nəşr olunan kitaba (əksər ölkələrdə) təyin edilmiş ISBN nömrələri vasitəsilədir. Bütün ISBN təyinatlarını bilən mərkəzi bir orqan yoxdur. Bunun əvəzinə, bu, ölkələrin nömrə diapazonları aldığı, daha sonra böyük nəşriyyatlara daha kiçik diapazonlar təyin etdiyi, daha sonra kiçik nəşriyyatlara diapazonları daha da bölə biləcəyi bir paylanmış sistemdir. Nəhayət, fərdi nömrələr kitablara təyin edilir. Biz ISBN-ləri <a %(blog)s>iki il əvvəl</a> ISBNdb-dən məlumat toplama ilə xəritələşdirməyə başladıq. O vaxtdan bəri, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby və daha çox kimi bir çox metadata mənbələrini topladıq. Tam siyahını Anna Arxivinin “Datasets” və “Torrents” səhifələrində tapa bilərsiniz. İndi dünyada ən böyük tam açıq, asanlıqla yüklənə bilən kitab metadata (və beləliklə ISBN-lər) kolleksiyasına sahibik. Biz <a %(blog)s>mühafizəyə niyə əhəmiyyət verdiyimiz</a> və niyə hazırda kritik bir pəncərədə olduğumuz barədə geniş yazmışıq. İndi nadir, diqqətdən kənarda qalan və unikal risk altında olan kitabları müəyyənləşdirməli və onları qorumaq üçün çalışmalıyıq. Dünyadakı bütün kitablar haqqında yaxşı metadata olması bu işdə kömək edir. $10,000 mükafatı İstifadə rahatlığına və necə göründüyünə güclü diqqət yetiriləcək. Böyüdükdə fərdi ISBN-lər üçün faktiki metadata, məsələn, başlıq və müəllif göstərin. Daha yaxşı məkan-doldurma əyrisi. Məsələn, birinci sətirdə 0-dan 4-ə qədər zig-zag, sonra isə ikinci sətirdə 5-dən 9-a qədər geri (əksinə) — rekursiv tətbiq olunur. Fərqli və ya fərdiləşdirilə bilən rəng sxemləri. Datasets müqayisəsi üçün xüsusi baxışlar. Digər metadata ilə yaxşı uyğunlaşmayan məsələləri düzəltmək üçün yollar (məsələn, çox fərqli başlıqlar). ISBN-lər və ya aralıqlar haqqında şərhlərlə şəkilləri qeyd etmək. Nadir və ya risk altında olan kitabları müəyyən etmək üçün hər hansı heuristikalar. Yaradıcılıqla ortaya çıxara biləcəyiniz hər hansı ideyalar! Kod Bu şəkilləri yaratmaq üçün kod, eləcə də digər nümunələr <a %(annas_archive)s>bu qovluqda</a> tapıla bilər. Biz 75MB (sıxılmış) olan bütün tələb olunan ISBN məlumatlarını əhatə edən kompakt bir məlumat formatı hazırladıq. Məlumat formatının təsviri və onu yaratmaq üçün kod <a %(annas_archive_l1244_1319)s>burada</a> tapıla bilər. Mükafat üçün bunu istifadə etmək məcburiyyətində deyilsiniz, lakin başlamaq üçün ən əlverişli formatdır. Metadata-nı istədiyiniz kimi dəyişə bilərsiniz (lakin bütün kodunuz açıq mənbə olmalıdır). Nə ortaya çıxaracağınızı görmək üçün səbirsizlənirik. Uğurlar! Bu repozitoriyanı fork edin və bu blog yazısının HTML-ni redaktə edin (Flask backend-dən başqa heç bir backend icazə verilmir). Yuxarıdakı şəkli hamar şəkildə böyüdülə bilən edin, belə ki, fərdi ISBN-lərə qədər böyüdə biləsiniz. ISBN-lərə klikləmək sizi Anna Arxivində metadata səhifəsinə və ya axtarışa aparmalıdır. Hələ də bütün fərqli datasets-lər arasında keçid edə bilməlisiniz. Ölkə diapazonları və nəşriyyat diapazonları üzərində hover edildikdə vurğulanmalıdır. Məsələn, ölkə məlumatları üçün <a %(github_xlcnd_isbnlib)s>isbnlib-də data4info.py</a> və nəşriyyatlar üçün “isbngrp” məlumat toplamağımızdan istifadə edə bilərsiniz (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Bu, masaüstü və mobil cihazlarda yaxşı işləməlidir. Burada çox şey araşdırmaq mümkündür, buna görə yuxarıdakı vizualizasiyanı yaxşılaşdırmaq üçün bir mükafat elan edirik. Əksər mükafatlarımızdan fərqli olaraq, bu mükafat vaxtla məhdudlaşdırılmışdır. Açıq mənbə kodunuzu 2025-01-31 (23:59 UTC) tarixinə qədər <a %(annas_archive)s>təqdim etməlisiniz</a>. Ən yaxşı təqdimat $6,000, ikinci yer $3,000, üçüncü yer isə $1,000 alacaq. Bütün mükafatlar Monero (XMR) ilə veriləcək. Aşağıda minimal meyarlar verilmişdir. Əgər heç bir təqdimat meyarlara cavab vermirsə, biz hələ də bəzi mükafatlar verə bilərik, lakin bu, bizim mülahizəmizdə olacaq. Bonus xalları üçün (bunlar sadəcə ideyalardır — yaradıcılığınızı sərbəst buraxın): Minimal meyarlardan tamamilə uzaqlaşa və tamamilə fərqli bir vizuallaşdırma edə bilərsiniz. Əgər həqiqətən möhtəşəmdirsə, bu mükafat üçün uyğun ola bilər, lakin bizim mülahizəmizlə. Təqdimatları <a %(annas_archive)s>bu məsələyə</a> şərh yazaraq, fork edilmiş repo, birləşmə tələbi və ya fərqlə birlikdə göndərin. - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Bu şəkil 1000×800 pikseldir. Hər piksel 2,500 ISBN-i təmsil edir. Əgər bir ISBN üçün faylımız varsa, həmin pikseli daha yaşıl edirik. Əgər bir ISBN-in verildiyini bilirik, amma uyğun faylımız yoxdursa, onu daha qırmızı edirik. 300kb-dan az bir ölçüdə, bu şəkil insanlıq tarixində indiyə qədər toplanmış ən böyük tam açıq “kitab siyahısını” qısa şəkildə təmsil edir (tam sıxılmış halda bir neçə yüz GB). Bu həmçinin göstərir: kitabların ehtiyat nüsxəsini çıxarmaq üçün hələ çox iş var (yalnız 16% var). Bütün ISBN-lərin Vizualizasiyası — 2025-01-31 tarixinə qədər $10,000 mükafat Bu şəkil insanlıq tarixində indiyə qədər toplanmış ən böyük tam açıq “kitab siyahısını” təmsil edir. Vizualizasiya Ümumi görüntü ilə yanaşı, əldə etdiyimiz fərdi datasets-lərə də baxa bilərik. Onlar arasında keçid etmək üçün açılan menyu və düymələrdən istifadə edin. Bu şəkillərdə görmək üçün çox maraqlı nümunələr var. Niyə fərqli miqyaslarda baş verən xətlərin və blokların müəyyən bir nizamı var? Boş sahələr nədir? Niyə bəzi datasets-lər bu qədər sıxlaşmışdır? Bu sualları oxucu üçün bir məşq olaraq buraxacağıq. - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nəticə Bu standartla, buraxılışları daha tədricən edə bilərik və yeni məlumat mənbələrini daha asanlıqla əlavə edə bilərik. Artıq bir neçə maraqlı buraxılışımız var! Həmçinin, digər kölgə kitabxanalarının kolleksiyalarımızı əks etdirməsi daha asan olacağını ümid edirik. Axı, məqsədimiz insan biliklərini və mədəniyyətini əbədi qorumaqdır, buna görə də daha çox təkrarlama daha yaxşıdır. Nümunə Son Z-Library buraxılışımıza nümunə olaraq baxaq. Bu, iki kolleksiyadan ibarətdir: “<span style="background: #fffaa3">zlib3_records</span>” və “<span style="background: #ffd6fe">zlib3_files</span>”. Bu, metadata qeydlərini faktiki kitab fayllarından ayrı şəkildə toplamağa və buraxmağa imkan verir. Beləliklə, metadata faylları ilə iki torrent buraxdıq: Həmçinin, yalnız “<span style="background: #ffd6fe">zlib3_files</span>” kolleksiyası üçün ikili məlumat qovluqları ilə bir çox torrent buraxdıq, cəmi 62: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> işlətdikdə içəridə nə olduğunu görə bilərik: Bu halda, Z-Library tərəfindən bildirilən bir kitabın metadata-sıdır. Üst səviyyədə yalnız “aacid” və “metadata” var, lakin “data_folder” yoxdur, çünki uyğun ikili məlumat yoxdur. AACID əsas ID kimi “22430000” ehtiva edir, bu da “zlibrary_id”dən götürüldüyünü göstərir. Bu kolleksiyadakı digər AAC-lərin də eyni struktura malik olacağını gözləyə bilərik. İndi <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> işlətdik: Bu, daha kiçik bir AAC metadata-sıdır, lakin bu AAC-nin əsas hissəsi başqa bir yerdə, ikili faylda yerləşir! Axı, bu dəfə “data_folder” var, buna görə də uyğun ikili məlumatın <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> yerində yerləşəcəyini gözləyə bilərik. “Metadata” “zlibrary_id” ehtiva edir, beləliklə, onu “zlib_records” kolleksiyasındakı uyğun AAC ilə asanlıqla əlaqələndirə bilərik. Fərqli yollarla da əlaqələndirə bilərdik, məsələn, AACID vasitəsilə — standart bunu tələb etmir. Qeyd edək ki, “metadata” sahəsinin özü JSON olması da zəruri deyil. O, XML və ya hər hansı digər məlumat formatını ehtiva edən bir sətir ola bilər. Hətta metadata məlumatını əlaqəli ikili blobda saxlaya bilərsiniz, məsələn, əgər bu çoxlu məlumatdırsa. Mümkün qədər orijinal formata yaxın müxtəlif fayllar və metadata. İkili məlumatlar Nginx kimi veb serverlər tərəfindən birbaşa təqdim edilə bilər. Mənbə kitabxanalarında müxtəlif identifikatorlar və ya hətta identifikatorların olmaması. Metadata ilə fayl məlumatlarının ayrı buraxılışları və ya yalnız metadata buraxılışları (məsələn, bizim ISBNdb buraxılışımız). Torrentlər vasitəsilə paylanma, lakin digər paylanma metodlarının da mümkünlüyü (məsələn, IPFS). Dəyişməz qeydlər, çünki torrentlərimizin əbədi yaşayacağını düşünməliyik. Artımlı buraxılışlar / əlavə edilə bilən buraxılışlar. Maşın tərəfindən oxuna və yazıla bilən, xüsusilə bizim stack üçün (Python, MySQL, ElasticSearch, Transmission, Debian, ext4) rahat və sürətli. İnsan tərəfindən nisbətən asan yoxlanıla bilən, lakin bu, maşın oxunaqlığından sonra gəlir. Standart kirayə seedbox ilə kolleksiyalarımızı asanlıqla seed etmək. Dizayn məqsədləri Faylların disklərdə əl ilə asanlıqla naviqasiya edilə bilməsi və ya əvvəlcədən emal edilmədən axtarıla bilməsi bizim üçün əhəmiyyətli deyil. Mövcud kitabxana proqram təminatı ilə birbaşa uyğunluq bizim üçün əhəmiyyətli deyil. Kolleksiyamızı torrentlər vasitəsilə seed etmək hər kəs üçün asan olmalıdır, lakin faylların əhəmiyyətli texniki bilik və öhdəlik olmadan istifadə edilə biləcəyini gözləmirik. Əsas istifadə məqsədimiz müxtəlif mövcud kolleksiyalardan faylların və əlaqəli metadata-nın paylanmasıdır. Ən vacib nəzərə alınanlar: Bəzi qeyri-məqsədlər: Anna Arxivi açıq mənbə olduğundan, formatımızı birbaşa istifadə etmək istəyirik. Axtarış indeksimizi yenilədikdə, yalnız ictimaiyyətə açıq yolları əldə edirik ki, kitabxanamızı fork edən hər kəs tez bir zamanda işə başlaya bilsin. <strong>AAC.</strong> AAC (Anna Arxivi Konteyneri) <strong>metadata</strong> və istəyə bağlı olaraq <strong>ikili məlumatlar</strong> daxil olan tək bir elementdir və hər ikisi dəyişməzdir. Bu, qlobal olaraq unikal bir identifikatora malikdir və <strong>AACID</strong> adlanır. <strong>AACID.</strong> AACID formatı belədir: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Məsələn, buraxdığımız faktiki bir AACID <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code> şəklindədir. <strong>AACID aralığı.</strong> AACID-lər monoton şəkildə artan zaman möhürləri ehtiva etdiyindən, müəyyən bir kolleksiya daxilində aralıqları göstərmək üçün bunu istifadə edə bilərik. Bu formatı istifadə edirik: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, burada zaman möhürləri daxil edilir. Bu, ISO 8601 notasiya ilə uyğundur. Aralıqlar davamlıdır və üst-üstə düşə bilər, lakin üst-üstə düşdükdə həmin kolleksiyada əvvəlki buraxılışda olan eyni qeydləri ehtiva etməlidir (çünki AAC-lər dəyişməzdir). Əskik qeydlərə icazə verilmir. <code>{collection}</code>: kolleksiya adı, ASCII hərfləri, rəqəmlər və alt xətlər (lakin ikiqat alt xətlər deyil) ehtiva edə bilər. <code>{collection-specific ID}</code>: kolleksiya-spesifik identifikator, əgər tətbiq olunarsa, məsələn, Z-Kitabxana ID-si. Buraxıla və ya qısaldıla bilər. Əgər AACID 150 simvoldan çox olarsa, buraxılmalı və ya qısaldılmalıdır. <code>{ISO 8601 timestamp}</code>: həmişə UTC-də olan ISO 8601-in qısa versiyası, məsələn, <code>20220723T194746Z</code>. Bu rəqəm hər buraxılış üçün monoton şəkildə artmalıdır, lakin onun dəqiq semantikası hər kolleksiya üçün fərqli ola bilər. Skreypinq və ya ID yaratma vaxtını istifadə etməyi təklif edirik. <code>{shortuuid}</code>: ASCII-ə sıxılmış UUID, məsələn, base57 istifadə edərək. Hal-hazırda <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python kitabxanasını istifadə edirik. <strong>İkili məlumat qovluğu.</strong> Bir kolleksiya üçün AAC-lərin bir aralığının ikili məlumatlarını ehtiva edən qovluq. Bunlar aşağıdakı xüsusiyyətlərə malikdir: Kataloq göstərilən diapazondakı bütün AAC-lər üçün məlumat fayllarını ehtiva etməlidir. Hər bir məlumat faylı öz AACID-ni fayl adı kimi (heç bir uzantı olmadan) daşımalıdır. Kataloq adı, <code style="color: green">annas_archive_data__</code> ilə prefiks edilməli və heç bir sonluq olmamalıdır. Məsələn, faktiki buraxılışlarımızdan biri<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> adlanır. Bu qovluqları müəyyən ölçüdə saxlamaq tövsiyə olunur, məsələn, hər biri 100GB-1TB-dan böyük olmamalıdır, baxmayaraq ki, bu tövsiyə zamanla dəyişə bilər. <strong>Kolleksiya.</strong> Hər bir AAC bir kolleksiyaya aiddir, bu isə tərifə görə semantik olaraq uyğun olan AAC-lərin siyahısıdır. Bu o deməkdir ki, metadata formatında əhəmiyyətli bir dəyişiklik etdikdə, yeni bir kolleksiya yaratmalısınız. Standart <strong>Metadata faylı.</strong> Metadata faylı, bir kolleksiya üçün AAC-lərin bir aralığının metadata-sını ehtiva edir. Bunlar aşağıdakı xüsusiyyətlərə malikdir: <code>data_folder</code> istəyə bağlıdır və uyğun ikili məlumatları ehtiva edən ikili məlumat qovluğunun adıdır. Həmin qovluqdakı uyğun ikili məlumatın fayl adı qeydin AACID-sidir. Hər bir JSON obyekti ən üst səviyyədə aşağıdakı sahələri ehtiva etməlidir: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (istəyə bağlı). Başqa sahələrə icazə verilmir. Fayl adı, <code style="color: red">annas_archive_meta__</code> ilə prefiks edilməli və <code>.jsonl.zstd</code> ilə bitməlidir. Məsələn, buraxılışlarımızdan biri<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> adlanır. Fayl uzantısının göstərdiyi kimi, fayl tipi <a %(jsonlines)s>JSON Lines</a> və <a %(zstd)s>Zstandard</a> ilə sıxılmışdır. <code>metadata</code> kolleksiyanın semantikasına uyğun olaraq istənilən metadata-dır. Kolleksiya daxilində semantik olaraq uyğun olmalıdır. <code style="color: red">annas_archive_meta__</code> prefiksi, məsələn, <code style="color: red">my_institute_meta__</code> kimi, sizin qurumunuzun adına uyğunlaşdırıla bilər. <strong>“qeydlər” və “fayllar” kolleksiyaları.</strong> Adətən, “qeydlər” və “fayllar”ı fərqli kolleksiyalar kimi buraxmaq əlverişlidir, belə ki, onlar fərqli cədvəllərdə buraxıla bilər, məsələn, skreypinq sürətlərinə əsasən. “Qeyd” yalnız metadata kolleksiyasıdır və kitab adları, müəlliflər, ISBN-lər və s. kimi məlumatları ehtiva edir, “fayllar” isə faktiki faylları (pdf, epub) ehtiva edən kolleksiyalardır. Nəticədə, nisbətən sadə bir standart üzərində qərarlaşdıq. Bu, kifayət qədər sərbəst, normativ olmayan və inkişafda olan bir işdir. <strong>Torrents.</strong> Metadata faylları və ikili məlumat qovluqları torrentlərdə birləşdirilə bilər, hər metadata faylı üçün bir torrent və ya hər ikili məlumat qovluğu üçün bir torrent. Torrentlər orijinal fayl/qovluq adı və <code>.torrent</code> sonluğu ilə fayl adı kimi olmalıdır. <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> açıq mənbə və açıq məlumat olan miqyasda yeganə kölgə kitabxanası olaraq dünyanın ən böyük kölgə kitabxanası oldu. Aşağıda Datasets səhifəmizdən bir cədvəl (bir qədər dəyişdirilmiş) verilmişdir: Bunu üç yolla həyata keçirdik: Mövcud açıq məlumat kölgə kitabxanalarını (məsələn, Sci-Hub və Library Genesis) güzgüləməklə. Daha açıq olmaq istəyən, lakin bunu etmək üçün vaxtı və ya resursları olmayan kölgə kitabxanalarına kömək etməklə (məsələn, Libgen komiks kolleksiyası). Kitabxanaların toplu paylaşım etmək istəmədiyi hallarda (məsələn, Z-Library). (2) və (3) üçün biz artıq özümüz böyük bir torrent kolleksiyasını idarə edirik (yüzlərlə TB). İndiyə qədər bu kolleksiyalara fərdi yanaşmışıq, yəni hər kolleksiya üçün xüsusi infrastruktur və məlumat təşkilatı. Bu, hər buraxılışa əhəmiyyətli dərəcədə əlavə yük gətirir və daha çox artımlı buraxılışlar etmək xüsusilə çətinləşdirir. Buna görə də buraxılışlarımızı standartlaşdırmağa qərar verdik. Bu, standartımızı təqdim etdiyimiz texniki bir blog yazısıdır: <strong>Anna Arxivi Konteynerləri</strong>. Anna’nın Arxivi Konteynerləri (AAC): dünyanın ən böyük kölgə kitabxanasından buraxılışların standartlaşdırılması Anna’nın Arxivi dünyanın ən böyük kölgə kitabxanası oldu və buraxılışlarımızı standartlaşdırmağımızı tələb etdi. 300GB+ kitab örtükləri yayımlandı Nəhayət, kiçik bir buraxılışı elan etməkdən məmnunuq. Libgen.rs forkunu idarə edən insanlarla əməkdaşlıq edərək, bütün kitab örtüklərini torrentlər və IPFS vasitəsilə paylaşırıq. Bu, örtüklərin baxış yükünü daha çox maşın arasında paylayacaq və onları daha yaxşı qoruyacaq. Bir çox (amma hamısı deyil) hallarda, kitab örtükləri faylların özündə daxil edilir, buna görə də bu bir növ “törəmə məlumat”dır. Amma IPFS-də olması həm Anna Arxivi, həm də müxtəlif Library Genesis forklarının gündəlik fəaliyyəti üçün hələ də çox faydalıdır. Həmişəki kimi, bu buraxılışı Pirate Library Mirror-da tapa bilərsiniz (DÜZƏLİŞ: <a %(wikipedia_annas_archive)s>Anna Arxivi</a>nə köçürüldü). Burada ona keçid verməyəcəyik, amma asanlıqla tapa bilərsiniz. Ümid edirik ki, Z-Kitabxanasına layiqli bir alternativimiz olduğu üçün tempimizi bir az yavaşlada bilərik. Bu iş yükü xüsusilə davamlı deyil. Proqramlaşdırma, server əməliyyatları və ya qoruma işlərində kömək etməkdə maraqlısınızsa, mütləq bizimlə əlaqə saxlayın. Hələ də çox <a %(annas_archive)s>iş görülməlidir</a>. Marağınız və dəstəyiniz üçün təşəkkür edirik. ElasticSearch-ə keçid Bəzi sorğular çox uzun çəkirdi, elə bir nöqtəyə çatırdı ki, bütün açıq bağlantıları tuturdu. Varsayılan olaraq MySQL-də minimum söz uzunluğu var, ya da indeksiniz həqiqətən böyük ola bilər. İnsanlar “Ben Hur” üçün axtarış edə bilmədiklərini bildirirdilər. Axtarış yalnız tamamilə yaddaşda yükləndikdə bir qədər sürətli idi, bu da bizi bu iş üçün daha bahalı bir maşın almağa məcbur etdi, üstəlik başlanğıcda indeksi əvvəlcədən yükləmək üçün bəzi əmrlər tələb etdi. Yeni xüsusiyyətlər qurmaq üçün, məsələn, daha yaxşı <a %(wikipedia_cjk_characters)s>boşluqsuz dillər üçün tokenizasiya</a>, filtrasiya/fasetinqləşdirmə, sıralama, "nəzərdə tutduğunuz bu idi" təklifləri, avtomatik tamamlanma və s. kimi xüsusiyyətlər əlavə etmək üçün asanlıqla genişləndirə bilməzdik. <a %(annas_archive)s>Biletlərimizdən</a> biri axtarış sistemimizlə bağlı bir sıra məsələləri əhatə edirdi. MySQL tam mətn axtarışından istifadə etdik, çünki bütün məlumatlarımız onsuz da MySQL-də idi. Amma bunun məhdudiyyətləri var idi: Bir çox mütəxəssislə danışdıqdan sonra ElasticSearch-də qərarlaşdıq. Bu mükəmməl olmayıb (onların varsayılan “nəzərdə tutduğunuz bu idi” təklifləri və avtomatik tamamlanma xüsusiyyətləri zəifdir), amma ümumilikdə MySQL-dən axtarış üçün daha yaxşı olub. Hələ də bunu hər hansı bir kritik məlumat üçün istifadə etməyə <a %(youtube)s>çox həvəsli deyilik</a> (baxmayaraq ki, çox <a %(elastic_co)s>irəliləyiş</a> əldə ediblər), amma ümumilikdə keçiddən çox məmnunuq. Hələlik, daha sürətli axtarış, daha yaxşı dil dəstəyi, daha yaxşı uyğunluq sıralaması, müxtəlif sıralama seçimləri və dil/kitab növü/fayl növü üzrə filtrasiya tətbiq etdik. Necə işlədiyini maraq edirsinizsə, <a %(annas_archive_l140)s>baxın</a>. Bu olduqca əlçatandır, baxmayaraq ki, bir az daha çox şərhə ehtiyacı var… Anna’nın Arxivi tamamilə açıq mənbədir Məlumatın azad olması lazım olduğuna inanırıq və öz kodumuz da istisna deyil. Bütün kodumuzu özəl host edilmiş Gitlab instansiyamızda yayımladıq: <a %(annas_archive)s>Anna’nın Proqramı</a>. İşimizi təşkil etmək üçün məsələlər izləyicisindən də istifadə edirik. İnkişafımızla məşğul olmaq istəyirsinizsə, bu başlamaq üçün əla bir yerdir. Sizə üzərində işlədiyimiz işlərdən bir dad vermək üçün, müştəri tərəfi performansını yaxşılaşdırmaq üçün son işimizi nəzərdən keçirin. Hələ səhifələmə tətbiq etmədiyimiz üçün, tez-tez 100-200 nəticə ilə çox uzun axtarış səhifələri qaytarırdıq. Axtarış nəticələrini çox tez kəsmək istəmirdik, lakin bu, bəzi cihazları yavaşlatmaq demək idi. Bunun üçün kiçik bir hiylə tətbiq etdik: əksər axtarış nəticələrini HTML şərhlərinə (<code><!-- --></code>) bükdük və sonra nəticənin görünməli olduğunu aşkar edən kiçik bir Javascript yazdıq, bu anda şərhi açardıq: DOM "virtualizasiyası" 23 sətirdə tətbiq edildi, dəbdəbəli kitabxanalara ehtiyac yoxdur! Bu, məhdud vaxtınız və həll edilməli olan real problemləriniz olduqda əldə etdiyiniz sürətli praqmatik koddur. İndi axtarışımızın yavaş cihazlarda yaxşı işlədiyi bildirildi! Başqa bir böyük səy, verilənlər bazasının qurulmasını avtomatlaşdırmaq idi. Başladığımızda, sadəcə müxtəlif mənbələri təsadüfi şəkildə bir araya gətirdik. İndi onları yeniləmək istəyirik, buna görə də iki Library Genesis forkundan yeni metadata yükləmək və onları inteqrasiya etmək üçün bir sıra skriptlər yazdıq. Məqsədimiz yalnız arxivimiz üçün faydalı olmaq deyil, həm də kölgə kitabxana metadata ilə oynamaq istəyən hər kəs üçün işləri asanlaşdırmaqdır. Məqsəd, maraqlı metadata ilə dolu bir Jupyter dəftəri yaratmaqdır ki, <a %(blog)s>ISBN-lərin hansı faizinin əbədi qorunduğunu</a> müəyyənləşdirmək kimi daha çox araşdırma apara bilək. Nəhayət, ianə sistemimizi yenidən qurduq. İndi kredit kartı ilə birbaşa kripto cüzdanlarımıza pul köçürə bilərsiniz, kriptovalyutalar haqqında heç nə bilməyə ehtiyac olmadan. Bunun praktikada nə qədər yaxşı işlədiyini izləməyə davam edəcəyik, amma bu böyük bir məsələdir. Z-Library-in bağlanması və onun (iddia edilən) qurucularının həbs edilməsi ilə, Anna’nın Arxivi ilə yaxşı bir alternativ təmin etmək üçün gecə-gündüz çalışırıq (burada link verməyəcəyik, amma Google-da axtara bilərsiniz). Son zamanlarda əldə etdiyimiz bəzi şeylər bunlardır. Anna’nın Yeniləməsi: tamamilə açıq mənbə arxivi, ElasticSearch, 300GB+ kitab örtükləri Anna’nın Arxivi ilə yaxşı bir alternativ təmin etmək üçün gecə-gündüz çalışırıq. Son zamanlarda əldə etdiyimiz bəzi şeylər bunlardır. Təhlil Semantik təkrarlamalar (eyni kitabın fərqli skanları) nəzəri olaraq süzülə bilər, lakin bu çətindir. Komikləri əl ilə nəzərdən keçirərkən çox sayda yanlış pozitiv tapdıq. MD5 ilə təkrarlanan bəzi fayllar var, bu nisbətən israfçıdır, lakin onları süzmək bizə yalnız təxminən 1% in qənaət verərdi. Bu miqyasda bu hələ də təxminən 1TB-dır, lakin bu miqyasda 1TB əslində əhəmiyyət kəsb etmir. Bu prosesdə təsadüfən məlumatları məhv etmək riskini almaq istəmirik. Biz bir çox qeyri-kitab məlumatları, məsələn, komiks kitablarına əsaslanan filmlər tapdıq. Bu da israf kimi görünür, çünki bunlar artıq digər vasitələrlə geniş şəkildə mövcuddur. Lakin, biz başa düşdük ki, sadəcə film fayllarını süzgəcdən keçirə bilmərik, çünki kompüterdə buraxılmış və kimsə tərəfindən film kimi qeyd edilərək saxlanılmış <em>interaktiv komiks kitabları</em> da var. Nəticədə, kolleksiyadan silə biləcəyimiz hər şey yalnız bir neçə faiz qənaət edərdi. Sonra xatırladıq ki, biz məlumat toplayıcılarıyıq və bunu əks etdirəcək insanlar da məlumat toplayıcılarıdır, və beləliklə, “SİLMƏK NƏ DEMƏKDİR?!” :) 95TB məlumatı saxlama klasterinizə atdığınız zaman, orada nə olduğunu anlamağa çalışırsınız… Biz ölçüsünü bir az azaltmaq üçün, məsələn, təkrarlananları çıxarmaqla bağlı bəzi təhlillər apardıq. Budur bəzi tapıntılarımız: Buna görə də sizə tam, dəyişdirilməmiş kolleksiyanı təqdim edirik. Bu çoxlu məlumatdır, amma ümid edirik ki, kifayət qədər insan onu yaymaq üçün maraq göstərəcək. Əməkdaşlıq Ölçüsünə görə, bu kolleksiya uzun müddətdir ki, arzu siyahımızda idi, buna görə də Z-Kitabxanasını ehtiyat nüsxəsini çıxarmaq uğurumuzdan sonra diqqətimizi bu kolleksiyaya yönəltdik. Əvvəlcə onu birbaşa kopyaladıq, bu olduqca çətin idi, çünki onların serveri ən yaxşı vəziyyətdə deyildi. Bu yolla təxminən 15TB əldə etdik, lakin bu, yavaş gedirdi. Xoşbəxtlikdən, kitabxananın operatoru ilə əlaqə saxlamağı bacardıq və o, bütün məlumatları birbaşa bizə göndərməyə razı oldu, bu da çox daha sürətli idi. Bütün məlumatları köçürmək və emal etmək yarım ildən çox vaxt aldı və biz demək olar ki, disk korlanması səbəbindən hamısını itirdik, bu da hər şeyə yenidən başlamaq demək olardı. Bu təcrübə bizə bu məlumatları mümkün qədər tez yaymağın vacib olduğuna inandırdı ki, geniş şəkildə güzgülənə bilsin. Biz bu kolleksiyanı əbədi itirməkdən yalnız bir və ya iki uğursuz hadisə uzaqdayıq! Kolleksiya Sürətli hərəkət etmək kolleksiyanın bir az təşkilatsız olması deməkdir… Gəlin baxaq. Təsəvvür edin ki, bir fayl sistemi var (əslində biz bunu torrentlər arasında bölüşdürürük): Birinci qovluq, <code>/repository</code>, bunun daha strukturlaşdırılmış hissəsidir. Bu qovluq "minlik qovluqlar" adlanan qovluqları ehtiva edir: hər biri minlərlə fayl olan qovluqlar, verilənlər bazasında ardıcıl olaraq nömrələnmişdir. <code>0</code> qovluğu 0–999 komik_id olan faylları ehtiva edir və s. Bu, Library Genesis-in bədii və qeyri-bədii kolleksiyaları üçün istifadə etdiyi eyni sxemdir. Fikir ondan ibarətdir ki, hər bir "minlik qovluq" dolduqdan sonra avtomatik olaraq torrentə çevrilir. Lakin, Libgen.li operatoru bu kolleksiya üçün heç vaxt torrentlər yaratmadı və buna görə də minlik qovluqlar ehtimal ki, əlverişsiz oldu və "təsnif edilməmiş qovluqlara" yol verdi. Bunlar <code>/comics0</code> ilə <code>/comics4</code> arasındadır. Onların hamısı unikal qovluq strukturlarını ehtiva edir, bu faylları toplamaq üçün məntiqli idi, lakin indi bizə çox məntiqli gəlmir. Xoşbəxtlikdən, metadata hələ də bu faylların hamısına birbaşa istinad edir, buna görə də onların diskdəki saxlama təşkilatı əslində əhəmiyyət kəsb etmir! Metadata MySQL verilənlər bazası şəklində mövcuddur. Bu, Libgen.li veb saytından birbaşa yüklənə bilər, lakin biz onu öz MD5 hash-lərimizlə birlikdə bir torrentdə də əlçatan edəcəyik. <q>Dr. Barbara Gordon kitabxananın adi dünyasında özünü itirməyə çalışır…</q> Libgen çəngəlləri Əvvəlcə, bir az arxa plan. Library Genesis-i onların epik kitab kolleksiyası ilə tanıya bilərsiniz. Daha az insan bilir ki, Library Genesis könüllüləri jurnallar və standart sənədlərdən ibarət böyük bir kolleksiya, Sci-Hub-un tam ehtiyat nüsxəsi (Sci-Hub-un qurucusu Aleksandra Elbakyan ilə əməkdaşlıqda) və həqiqətən də böyük bir komik kolleksiya yaratmışlar. Bir nöqtədə, Library Genesis güzgülərinin müxtəlif operatorları öz yollarını ayırdılar, bu da Library Genesis adını daşıyan bir neçə fərqli "çəngəl" vəziyyətinin yaranmasına səbəb oldu. Libgen.li çəngəli unikal olaraq bu komik kolleksiyaya, həmçinin böyük bir jurnal kolleksiyasına malikdir (biz də bunun üzərində işləyirik). İanə Toplama Bu məlumatı böyük hissələrdə buraxırıq. İlk torrent <code>/comics0</code> üçündür, biz bunu bir böyük 12TB .tar faylına yerləşdirdik. Bu, sizin sərt diskiniz və torrent proqramınız üçün bir çox kiçik fayldan daha yaxşıdır. Bu buraxılışın bir hissəsi olaraq, ianə toplama kampaniyası keçiririk. Bu kolleksiya üçün əməliyyat və müqavilə xərclərini qarşılamaq, həmçinin davam edən və gələcək layihələri təmin etmək üçün 20,000 dollar toplamağı hədəfləyirik. Bizim bəzi <em>böyük</em> layihələrimiz üzərində iş gedir. <em>İanəm ilə kimi dəstəkləyirəm?</em> Qısaca: biz bütün insanlıq bilik və mədəniyyətini ehtiyatlayırıq və onu asanlıqla əlçatan edirik. Bütün kod və məlumatlarımız açıq mənbəlidir, biz tamamilə könüllülər tərəfindən idarə olunan bir layihəyik və indiyə qədər 125TB dəyərində kitabı xilas etmişik (Libgen və Scihub-un mövcud torrentlərinə əlavə olaraq). Nəticədə, biz dünyadakı bütün kitabları tapmaq, skan etmək və ehtiyatlamaq üçün insanları təşviq edən və imkan yaradan bir mexanizm qururuq. Gələcək yazıda əsas planımız haqqında yazacağıq. :) Əgər 12 aylıq “Amazing Archivist” üzvlüyü üçün ianə etsəniz ($780), siz <strong>“torrent qəbul edə”</strong> bilərsiniz, yəni biz sizin istifadəçi adınızı və ya mesajınızı torrentlərdən birinin fayl adında yerləşdirəcəyik! <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> saytına gedərək və “İanə et” düyməsinə basaraq ianə edə bilərsiniz. Biz həmçinin daha çox könüllü axtarırıq: proqram mühəndisləri, təhlükəsizlik tədqiqatçıları, anonim ticarət mütəxəssisləri və tərcüməçilər. Bizə hosting xidmətləri təqdim edərək də dəstək ola bilərsiniz. Və əlbəttə, torrentlərimizi yaymağı unutmayın! Artıq bizə bu qədər səxavətlə dəstək olan hər kəsə təşəkkür edirik! Siz həqiqətən fərq yaradırsınız. İndiyə qədər buraxılmış torrentlər bunlardır (qalanlarını hələ də işləyirik): Bütün torrentlər <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> saytında “Datasets” altında tapıla bilər (biz oraya birbaşa keçid vermirik, belə ki, bu bloq yazısına keçidlər Reddit, Twitter və s. yerlərdən silinməsin). Oradan, Tor veb saytına keçid edin. <a %(news_ycombinator)s>Hacker News-da müzakirə edin</a> Növbəti nədir? Bir çox torrentlər uzunmüddətli qorunma üçün əladır, lakin gündəlik istifadə üçün o qədər də uyğun deyil. Biz hosting tərəfdaşları ilə bu məlumatların hamısını vebdə yerləşdirmək üçün işləyəcəyik (çünki Anna’nın Arxivi birbaşa heç nəyi host etmir). Əlbəttə ki, bu yükləmə linklərini Anna’nın Arxivində tapa biləcəksiniz. Biz həmçinin hər kəsi bu məlumatlarla nəsə etməyə dəvət edirik! Bizə onu daha yaxşı analiz etməyə, təkrarlamaları aradan qaldırmağa, IPFS-ə yerləşdirməyə, AI modellərinizi onunla təlim etməyə və s. kömək edin. Hamısı sizindir və nə edəcəyinizi görmək üçün səbirsizliklə gözləyirik. Nəhayət, əvvəllər dediyimiz kimi, hələ də bəzi böyük buraxılışlarımız var (əgər <em>kimsə</em> təsadüfən <em>müəyyən</em> bir ACS4 verilənlər bazasının dumpunu bizə göndərə bilsə, harada olduğumuzu bilirsiniz...), həmçinin dünyadakı bütün kitabları ehtiyatlamaq üçün mexanizm qururuq. Beləliklə, bizi izləyin, biz hələ yeni başlayırıq. - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ən böyük kölgə kitabxanası olan komik kitablar ehtimal ki, müəyyən bir Library Genesis çəngəlinə aiddir: Libgen.li. Bu saytı idarə edən tək administrator 2 milyondan çox fayldan ibarət, ümumilikdə 95TB-dan çox olan inanılmaz bir komik kolleksiyası toplamağı bacardı. Lakin, digər Library Genesis kolleksiyalarından fərqli olaraq, bu kolleksiya torrentlər vasitəsilə toplu şəkildə əlçatan deyildi. Bu komikləri yalnız onun yavaş şəxsi serveri vasitəsilə fərdi şəkildə əldə edə bilərdiniz — bir uğursuzluq nöqtəsi. Bu günə qədər! Bu yazıda sizə bu kolleksiya haqqında daha çox məlumat verəcəyik və bu işin daha çox dəstəklənməsi üçün təşkil etdiyimiz ianə kampaniyamız haqqında danışacağıq. Anna Arxivi dünyanın ən böyük kölgə komiks kitabxanasını (95TB) ehtiyatladı — siz də onu yaymağa kömək edə bilərsiniz Dünyanın ən böyük komiks kitabxanasının bir uğursuzluq nöqtəsi var idi.. bu günə qədər. Xəbərdarlıq: bu bloq yazısı köhnəlmişdir. Biz qərara gəldik ki, IPFS hələ əsas vaxt üçün hazır deyil. Anna'nın Arxivindən mümkün olduqda IPFS-dəki fayllara hələ də keçid verəcəyik, lakin artıq özümüz host etməyəcəyik və başqalarına IPFS istifadə edərək güzgü etməyi tövsiyə etmirik. Kolleksiyamızı qorumağa kömək etmək istəyirsinizsə, Torrents səhifəmizə baxın. 5,998,794 kitabı IPFS-ə yerləşdirmək Nüsxələrin çoxaldılması Əsas sualımıza qayıdaq: kolleksiyalarımızı əbədi olaraq necə qoruyacağımızı iddia edə bilərik? Buradakı əsas problem, kolleksiyamızın <a %(torrents_stats)s>sürətlə böyüməsidir</a>, bəzi böyük kolleksiyaları qazımaq və açıq mənbə etmək (Sci-Hub və Library Genesis kimi digər açıq məlumat kölgə kitabxanalarının artıq gördüyü möhtəşəm işlərin üstündə). Bu məlumat artımı, kolleksiyaların dünya üzrə güzgülənməsini çətinləşdirir. Məlumat saxlama bahalıdır! Amma biz nikbinik, xüsusilə aşağıdakı üç tendensiyanı müşahidə edərkən. Son bir neçə ay ərzində torrent seederlərinin sayı ilə bölünmüş kolleksiyalarımızın <a %(annas_archive_stats)s>ümumi ölçüsü</a>. Fərqli mənbələrdən HDD qiymət trendləri (araşdırmanı görmək üçün klikləyin). <a %(critical_window_chinese)s>Çin versiyası 中文版</a>, <a %(reddit)s>Reddit</a>də müzakirə edin, <a %(news_ycombinator)s>Hacker News</a> 1. Asan əldə edilə bilənləri topladıq Bu, yuxarıda müzakirə etdiyimiz prioritetlərdən birbaşa gəlir. İlk növbədə böyük kolleksiyaları azad etməklə məşğul olmağı üstün tuturuq. İndi dünyanın ən böyük kolleksiyalarından bəzilərini təmin etdikdən sonra, böyüməmizin çox daha yavaş olacağını gözləyirik. Hələ də kiçik kolleksiyaların uzun bir quyruğu var və hər gün yeni kitablar skan edilir və ya nəşr olunur, amma sürət çox yavaş olacaq. Hələ də ikiqat və ya hətta üçqat böyüyə bilərik, amma daha uzun müddət ərzində. OCR təkmilləşdirmələri. Prioritetlər Elm və mühəndislik proqram təminatı kodu Yuxarıda göstərilənlərin bədii və ya əyləncə versiyaları Coğrafi məlumatlar (məsələn, xəritələr, geoloji tədqiqatlar) Korporasiyalardan və ya hökumətlərdən daxili məlumatlar (sızmalar) Ölçmə məlumatları, məsələn, elmi ölçmələr, iqtisadi məlumatlar, korporativ hesabatlar Ümumiyyətlə metadata qeydləri (qeyri-bədii və bədii; digər media, incəsənət, insanlar və s.; rəylər daxil olmaqla) Qeyri-bədii kitablar Qeyri-bədii jurnallar, qəzetlər, təlimatlar Danışıqların, sənədli filmlərin, podkastların qeyri-bədii transkriptləri DNA ardıcıllığı, bitki toxumları və ya mikrob nümunələri kimi üzvi məlumatlar Elmi məqalələr, jurnallar, hesabatlar Elm və mühəndislik veb saytları, onlayn müzakirələr Hüquqi və ya məhkəmə iclaslarının transkriptləri Unikal olaraq məhv olma riski altında olan (məsələn, müharibə, maliyyə kəsintiləri, məhkəmə iddiaları və ya siyasi təqiblər səbəbindən) Nadir Unikal olaraq diqqətdən kənarda qalan Niyə məqalələrə və kitablara bu qədər əhəmiyyət veririk? Ümumi qoruma inancımızı bir kənara qoyaq — bu barədə başqa bir yazı yaza bilərik. Bəs niyə məqalələr və kitablar xüsusi olaraq? Cavab sadədir: <strong>məlumat sıxlığı</strong>. Saxlama başına hər meqabayt üçün yazılı mətn bütün media arasında ən çox məlumatı saxlayır. Həm bilik, həm də mədəniyyətə əhəmiyyət versək də, birincisinə daha çox əhəmiyyət veririk. Ümumilikdə, məlumat sıxlığı və qorunmanın əhəmiyyəti baxımından təxminən belə bir iyerarxiya tapırıq: Bu siyahıdakı sıralama bir qədər təsadüfidir — bir neçə maddə bərabərdir və ya komandamız daxilində fikir ayrılıqları var — və yəqin ki, bəzi vacib kateqoriyaları unuduruq. Amma bu, təxminən necə prioritetləşdirdiyimizi göstərir. Bu maddələrin bəziləri bizim üçün narahat olmaq üçün çox fərqlidir (və ya artıq digər qurumlar tərəfindən həll olunur), məsələn, orqanik məlumatlar və ya coğrafi məlumatlar. Amma bu siyahıdakı əksər maddələr əslində bizim üçün vacibdir. Prioritetləşdirməmizdə başqa bir böyük amil müəyyən bir əsərin nə qədər risk altında olmasıdır. Biz diqqətimizi aşağıdakı əsərlərə yönəltməyi üstün tuturuq: Nəhayət, miqyas bizim üçün önəmlidir. Məhdud vaxtımız və pulumuz var, ona görə də 1,000 kitabdan daha çox 10,000 kitabı xilas etmək üçün bir ay sərf etməyi üstün tuturuq — əgər onlar təxminən eyni dərəcədə dəyərli və risk altındadırsa. <em><q>İtirilənlər bərpa edilə bilməz; amma qalanları qoruyaq: onları ictimai gözlərdən və istifadədən qoruyan qapılar və kilidlər vasitəsilə deyil, nüsxələrin çoxaldılması ilə, onları təsadüfdən uzaqlaşdıraraq.</q></em><br>— Thomas Jefferson, 1791 Kölgə kitabxanaları Kod Github-da açıq mənbə ola bilər, amma Github bütövlükdə asanlıqla güzgülənə və beləliklə qoruna bilməz (lakin bu xüsusi halda əksər kod anbarlarının kifayət qədər paylanmış nüsxələri var) Metadata qeydləri Worldcat veb saytında sərbəst baxıla bilər, amma kütləvi şəkildə yüklənə bilməz (biz onları <a %(worldcat_scrape)s>qazıyana</a> qədər) Reddit istifadə üçün pulsuzdur, amma son zamanlarda məlumat acgöz LLM təlimi səbəbindən ciddi anti-qazıma tədbirləri tətbiq edib (bu barədə daha sonra) Oxşar missiyaları və prioritetləri olan bir çox təşkilat var. Həqiqətən, bu cür qoruma ilə məşğul olan kitabxanalar, arxivlər, laboratoriyalar, muzeylər və digər qurumlar mövcuddur. Onların bir çoxu hökumətlər, fərdlər və ya korporasiyalar tərəfindən yaxşı maliyyələşdirilir. Amma onların bir böyük kor nöqtəsi var: hüquq sistemi. Burada kölgə kitabxanalarının unikal rolu və Anna’nın Arxivi’nin mövcud olma səbəbi yatır. Biz, digər qurumların etməyə icazə verilmədiyi şeyləri edə bilirik. İndi, bu, (tez-tez) başqa yerlərdə qorunması qanunsuz olan materialları arxivləyə biləcəyimiz demək deyil. Xeyr, bir çox yerdə hər hansı kitablar, məqalələr, jurnallar və s. ilə arxiv qurmaq qanunidir. Amma qanuni arxivlərin tez-tez çatışmadığı şey <strong>təkrarlanma və uzunömürlülükdür</strong>. Elə kitablar var ki, yalnız bir nüsxəsi hansısa fiziki kitabxanada mövcuddur. Elə metadata qeydləri var ki, yalnız bir şirkət tərəfindən qorunur. Elə qəzetlər var ki, yalnız bir arxivdə mikrofilm üzərində saxlanılır. Kitabxanaların maliyyələşdirilməsi kəsilə bilər, şirkətlər iflas edə bilər, arxivlər bombalanıb yandırıla bilər. Bu, hipotetik deyil — bu, hər zaman baş verir. Anna’nın Arxivi’nde unikal olaraq edə biləcəyimiz şey, əsərlərin çoxlu nüsxələrini saxlamaqdır. Biz məqalələr, kitablar, jurnallar və daha çoxunu toplaya və onları kütləvi şəkildə paylaya bilirik. Hazırda bunu torrentlər vasitəsilə edirik, amma dəqiq texnologiyalar əhəmiyyətli deyil və zamanla dəyişəcək. Əsas olan, çoxlu nüsxələrin dünya üzrə paylanmasıdır. 200 ildən çox əvvəlki bu sitat hələ də doğrudur: İctimai domen haqqında qısa bir qeyd. Anna’nın Arxivi, dünyada bir çox yerdə qanunsuz olan fəaliyyətlərə unikal olaraq diqqət yetirdiyi üçün, ictimai domen kitabları kimi geniş yayılmış kolleksiyalarla maraqlanmırıq. Qanuni qurumlar artıq bu işə yaxşı baxırlar. Lakin, bəzən bizi ictimaiyyətə açıq kolleksiyalar üzərində işləməyə vadar edən səbəblər var: - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Saxlama xərcləri eksponensial olaraq azalır 3. Məlumat sıxlığında təkmilləşdirmələr Hal-hazırda bizə təqdim olunan kitabları xam formatlarda saxlayırıq. Əlbəttə, onlar sıxılmışdır, lakin tez-tez hələ də böyük skanlar və ya səhifələrin fotoşəkilləridir. İndiyə qədər kolleksiyamızın ümumi ölçüsünü azaltmaq üçün yeganə seçim daha aqressiv sıxılma və ya təkrarlamanın aradan qaldırılması olub. Lakin əhəmiyyətli qənaət əldə etmək üçün hər ikisi bizim zövqümüz üçün çox itkilidir. Fotoşəkillərin ağır sıxılması mətni çətin oxunan edə bilər. Və təkrarlamanın aradan qaldırılması kitabların tamamilə eyni olduğuna yüksək inam tələb edir ki, bu da tez-tez çox qeyri-dəqiqdir, xüsusən də məzmun eyni olsa da, skanlar müxtəlif vaxtlarda edildikdə. Həmişə üçüncü bir seçim olub, lakin onun keyfiyyəti o qədər bərbad idi ki, biz onu heç vaxt nəzərə almadıq: <strong>OCR, yəni Optik Simvol Tanıma</strong>. Bu, fotoşəkilləri sadə mətnə çevirmək prosesidir, AI-dən istifadə edərək fotoşəkillərdəki simvolları aşkar edir. Bunun üçün alətlər uzun müddətdir mövcuddur və olduqca yaxşıdır, lakin “olduqca yaxşı” qoruma məqsədləri üçün kifayət deyil. Lakin, son çoxmodal dərin öyrənmə modelləri çox sürətlə irəliləmişdir, baxmayaraq ki, hələ də yüksək xərclərlə. Biz gözləyirik ki, həm dəqiqlik, həm də xərclər yaxın illərdə dramatik şəkildə yaxşılaşacaq, elə bir nöqtəyə çatacaq ki, onu bütün kitabxanamıza tətbiq etmək real olacaq. Bu baş verdikdə, biz yəqin ki, orijinal faylları hələ də qoruyacağıq, lakin əlavə olaraq kitabxanamızın çox kiçik bir versiyasına sahib ola bilərik ki, əksər insanlar onu əks etdirmək istəyəcəklər. Əsas məqam odur ki, xam mətn özü daha yaxşı sıxılır və təkrarlamanın aradan qaldırılması daha asandır, bu da bizə daha çox qənaət verir. Ümumilikdə, ümumi fayl ölçüsündə ən azı 5-10 dəfə azalma gözləmək qeyri-real deyil, bəlkə də daha çox. Hətta mühafizəkar 5 dəfə azalma ilə, 10 il ərzində kitabxanamız üç dəfə artsa belə, 1,000–3,000 dollar ödəyəcəyik. Yazı zamanı, <a %(diskprices)s>disk qiymətləri</a> TB başına yeni disklər üçün təxminən 12 dollar, istifadə edilmiş disklər üçün 8 dollar və lent üçün 4 dollardır. Əgər yalnız yeni disklərə baxsaq, bu, bir petabayt saxlamağın təxminən 12,000 dollar başa gəldiyini göstərir. Kitabxanamızın 900TB-dan 2.7PB-ya üçqat artacağını düşünsək, bu, bütün kitabxanamızı güzgüləmək üçün 32,400 dollar deməkdir. Elektrik, digər avadanlıq xərcləri və s. əlavə edərək, bunu 40,000 dollara yuvarlayaq. Və ya lentlə daha çox 15,000–20,000 dollar. Bir tərəfdən <strong>bütün insan biliklərinin cəmi üçün 15,000–40,000 dollar bir oğurluqdur</strong>. Digər tərəfdən, tam nüsxələrin çoxunu gözləmək bir az çətindir, xüsusilə də bu insanların başqalarının xeyrinə torrentlərini davamlı olaraq paylaşmalarını istəsək. Bu, bu gün. Amma irəliləyiş davam edir: Son 10 ildə HDD qiymətləri TB başına təxminən üçdə birinə endirilib və ehtimal ki, oxşar sürətlə düşməyə davam edəcək. Lent də oxşar bir trayektoriya üzərində görünür. SSD qiymətləri daha sürətlə düşür və onilliyin sonuna qədər HDD qiymətlərini keçə bilər. Əgər bu baş verərsə, onda 10 il ərzində bütün kolleksiyamızı (1/3) əks etdirmək üçün yalnız 5,000–13,000 dollar ödəməli ola bilərik və ya əgər ölçü baxımından daha az böyüsək, daha az. Hələ də çox pul olsa da, bu, bir çox insan üçün əlçatan olacaq. Və bu, növbəti məqam səbəbindən daha da yaxşı ola bilər… Anna Arxivində bizdən tez-tez soruşurlar ki, kolleksiyalarımızı əbədi olaraq qoruyacağımızı necə iddia edə bilərik, əgər ümumi ölçü artıq 1 Petabayta (1000 TB) yaxınlaşır və hələ də böyüyürsə. Bu məqalədə biz fəlsəfəmizə baxacağıq və niyə növbəti onilliyin bəşəriyyətin bilik və mədəniyyətini qorumaq missiyamız üçün kritik olduğunu görəcəyik. Kritik pəncərə Əgər bu proqnozlar dəqiqdirsə, biz <strong>sadəcə bir neçə il gözləməliyik</strong> ki, bütün kolleksiyamız geniş şəkildə əks etdirilsin. Beləliklə, Tomas Ceffersonun sözləri ilə desək, “qəza əlindən kənarda yerləşdirilmişdir.” Təəssüf ki, LLM-lərin meydana çıxması və onların məlumat acgöz təlimi bir çox müəllif hüquqları sahiblərini müdafiəyə keçirdi. Artıq olduqlarından daha çox. Bir çox veb saytları skrapinq və arxivləşdirməyi çətinləşdirir, məhkəmə iddiaları uçuşur və bütün bu müddətdə fiziki kitabxanalar və arxivlər laqeyd qalmağa davam edir. Bu meyllərin pisləşməyə davam edəcəyini və bir çox əsərlərin ictimai mülkiyyətə daxil olmadan əvvəl itiriləcəyini gözləyə bilərik. <strong>Biz qoruma sahəsində inqilabın ərəfəsindəyik, lakin <q>itirilən bərpa edilə bilməz.</q></strong> Hələ də kölgə kitabxanası işlətmək və dünyada bir çox güzgü yaratmaq üçün kifayət qədər bahalı olan təxminən 5-10 illik kritik bir pəncərəmiz var və bu müddətdə giriş tamamilə bağlanmayıb. Əgər bu pəncərəni keçə bilsək, onda həqiqətən də bəşəriyyətin bilik və mədəniyyətini əbədi olaraq qoruyacağıq. Bu vaxtı boşa verməməliyik. Bu kritik pəncərənin üzərimizə bağlanmasına imkan verməməliyik. Gəlin başlayaq. Kölgə kitabxanalarının kritik pəncərəsi Kolleksiyalarımızı əbədi olaraq qoruyacağımızı necə iddia edə bilərik, əgər onlar artıq 1 PB-ə yaxınlaşırsa? Kolleksiya Kolleksiya haqqında daha çox məlumat. <a %(duxiu)s>Duxiu</a> skan edilmiş kitabların böyük bir bazasıdır, <a %(chaoxing)s>SuperStar Digital Library Group</a> tərəfindən yaradılmışdır. Əksəriyyəti akademik kitablardır, universitetlərə və kitabxanalara rəqəmsal olaraq təqdim etmək üçün skan edilmişdir. İngilis dilli auditoriyamız üçün, <a %(library_princeton)s>Princeton</a> və <a %(guides_lib_uw)s>Vaşinqton Universiteti</a> yaxşı icmallar təqdim edir. Daha çox məlumat verən əla bir məqalə də var: <a %(doi)s>“Çin Kitablarının Rəqəmsallaşdırılması: SuperStar DuXiu Scholar Axtarış Mühərrikinin Bir Vəziyyət Araşdırması”</a> (Anna'nın Arxivində axtarın). Duxiu-dan olan kitablar uzun müddətdir Çin internetində pirat edilmişdir. Adətən, satıcılar tərəfindən bir dollardan az qiymətə satılır. Onlar adətən Google Drive-ın Çin ekvivalenti ilə paylanır, bu da tez-tez daha çox saxlama sahəsi üçün hack edilir. Bəzi texniki detallar <a %(github_duty_machine)s>burada</a> və <a %(github_821_github_io)s>burada</a> tapıla bilər. Kitablar yarı-ictimai şəkildə paylanmış olsa da, onları toplu şəkildə əldə etmək olduqca çətindir. Bu, bizim TODO siyahımızda yüksək idi və bunun üçün tam zamanlı iş üçün bir neçə ay ayırdıq. Lakin, son zamanlarda inanılmaz, möhtəşəm və istedadlı bir könüllü bizə müraciət etdi və bu işin hamısını artıq böyük xərclə etdiklərini bildirdi. Onlar bizə tam kolleksiyanı heç bir qarşılıq gözləmədən, yalnız uzunmüddətli qorunma zəmanəti ilə paylaşdılar. Həqiqətən diqqətəlayiqdir. Onlar bu şəkildə kömək istəməyə razı oldular ki, kolleksiya OCR edilsin. Kolleksiya 7,543,702 fayldan ibarətdir. Bu, Library Genesis qeyri-bədii ədəbiyyatından (təxminən 5.3 milyon) daha çoxdur. Cari formada ümumi fayl ölçüsü təxminən 359TB (326TiB) təşkil edir. Başqa təkliflərə və fikirlərə açığıq. Sadəcə bizimlə əlaqə saxlayın. Kolleksiyalarımız, qoruma səylərimiz və necə kömək edə biləcəyiniz haqqında daha çox məlumat üçün Anna'nın Arxivinə baxın. Təşəkkürlər! Nümunə səhifələr Bizə yaxşı bir boru xəttiniz olduğunu sübut etmək üçün, superkeçiricilər haqqında bir kitabdan başlamaq üçün bəzi nümunə səhifələr təqdim edirik. Boru xəttiniz riyaziyyat, cədvəllər, qrafiklər, dipnotlar və s. ilə düzgün işləməlidir. Emal etdiyiniz səhifələri elektron poçtumuza göndərin. Əgər yaxşı görünürsə, sizə daha çoxunu özəl olaraq göndərəcəyik və boru xəttinizi tez bir zamanda işlədə biləcəyinizi gözləyirik. Məmnun qaldıqdan sonra, bir razılaşma edə bilərik. - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Çin versiyası 中文版</a>, <a %(news_ycombinator)s>Hacker News-da müzakirə et</a> Bu qısa bir bloq yazısıdır. Bizə OCR və mətn çıxarışı ilə kömək edəcək bir şirkət və ya institut axtarırıq, əvəzində eksklüziv erkən giriş təklif edirik. Embargo müddəti bitdikdən sonra, əlbəttə ki, bütün kolleksiyanı yayımlayacağıq. Yüksək keyfiyyətli akademik mətnlər LLM-lərin təlimi üçün son dərəcə faydalıdır. Kolleksiyamız Çin dilində olsa da, bu, İngilis LLM-lərin təlimi üçün də faydalı ola bilər: modellər mənbə dildən asılı olmayaraq konseptləri və bilikləri kodlaşdırır. Bunun üçün mətnlər skanlardan çıxarılmalıdır. Anna'nın Arxivi bundan nə əldə edir? İstifadəçiləri üçün kitabların tam mətn axtarışı. Çünki məqsədlərimiz LLM inkişaf etdiricilərinin məqsədləri ilə üst-üstə düşür, biz bir əməkdaş axtarırıq. Əgər düzgün OCR və mətn çıxarışı edə bilsəniz, sizə bu kolleksiyaya toplu şəkildə 1 il müddətində <strong>eksklüziv erkən giriş təklif etməyə hazırıq</strong>. Əgər boru xəttinizin bütün kodunu bizimlə paylaşmağa hazırsınızsa, kolleksiyanı daha uzun müddət embargo etməyə hazırıq. Dünyanın ən böyük Çin qeyri-bədii kitab kolleksiyasına LLM şirkətləri üçün eksklüziv giriş <em><strong>Qısa məzmun:</strong> Anna'nın Arxivi 7.5 milyon / 350TB Çin qeyri-bədii kitablarının unikal kolleksiyasını əldə etdi — Library Genesis-dən daha böyükdür. Biz yüksək keyfiyyətli OCR və mətn çıxarışı qarşılığında bir LLM şirkətinə eksklüziv giriş verməyə hazırıq.</em> Sistem arxitekturası Beləliklə, saytınızı bağlamadan host etməyə hazır olan bəzi şirkətlər tapdığınızı deyək — bunları “azadlıqsevər təminatçılar” adlandıraq 😄. Tezliklə görəcəksiniz ki, hər şeyi onlarla host etmək olduqca bahalıdır, buna görə də bəzi “ucuz təminatçılar” tapmaq və əsl hostinqi orada etmək istəyə bilərsiniz, azadlıqsevər təminatçılar vasitəsilə proksi edərək. Düzgün etsəniz, ucuz təminatçılar nəyi host etdiyinizi heç vaxt bilməyəcək və heç bir şikayət almayacaqlar. Bütün bu təminatçılarla sizi bağlamaq riski var, buna görə də ehtiyatlılıq lazımdır. Bizim yığımımızın bütün səviyyələrində buna ehtiyacımız var. Özünü maraqlı bir mövqeyə qoymuş bir azadlıqsevər şirkət Cloudflare-dir. Onlar <a %(blog_cloudflare)s>iddia ediblər</a> ki, onlar hostinq təminatçısı deyil, ISP kimi bir kommunal xidmətlərdir. Buna görə də DMCA və ya digər bağlama tələblərinə tabe deyillər və hər hansı bir tələbi əsl hostinq təminatçınıza yönləndirirlər. Bu strukturu qorumaq üçün məhkəməyə qədər getmişlər. Buna görə də onları başqa bir keşləmə və qoruma qat kimi istifadə edə bilərik. Cloudflare anonim ödənişləri qəbul etmir, buna görə də yalnız onların pulsuz planından istifadə edə bilərik. Bu, onların yük balanslaşdırma və ya failover xüsusiyyətlərindən istifadə edə bilməyəcəyimiz deməkdir. Buna görə də <a %(annas_archive_l255)s>bunu özümüz həyata keçirdik</a> domen səviyyəsində. Səhifə yüklənərkən, brauzer cari domenin hələ də mövcud olub-olmadığını yoxlayır və əgər yoxdursa, bütün URL-ləri başqa bir domene yenidən yazır. Cloudflare bir çox səhifəni keşlədiyi üçün, bu, istifadəçinin əsas domenimizə enə biləcəyi, hətta proksi server aşağı olsa belə, və sonra növbəti klikdə başqa bir domene keçə biləcəyi deməkdir. Hələ də server sağlamlığını izləmək, arxa və ön tərəf səhvlərini qeyd etmək və s. kimi normal əməliyyat məsələləri ilə məşğul olmalıyıq. Failover arxitekturamız bu cəbhədə daha çox dayanıqlılıq təmin edir, məsələn, domenlərdən birində tamamilə fərqli bir server dəsti işlədərək. Hətta əsas versiyada kritik bir səhv gözə çarpmadıqda, bu ayrı domen üzərində kodun və datasets-in köhnə versiyalarını işlədə bilərik. Cloudflare-in bizə qarşı dönməsinə qarşı da tədbir görə bilərik, məsələn, onu bu ayrı domen kimi domenlərdən birindən çıxararaq. Bu ideyaların müxtəlif permutasiyaları mümkündür. Nəticə Kölgə kitabxana axtarış mühərrikini necə qurmağı öyrənmək maraqlı bir təcrübə oldu. Daha sonra paylaşmaq üçün çoxlu detallar var, ona görə də nə haqqında daha çox öyrənmək istədiyinizi bildirin! Həmişə olduğu kimi, bu işi dəstəkləmək üçün ianələr axtarırıq, ona görə də Anna Arxivi saytında İanə səhifəsinə baxmağı unutmayın. Biz həmçinin qrantlar, uzunmüddətli sponsorlar, yüksək riskli ödəniş provayderləri, bəlkə də (zövqlü!) reklamlar kimi digər dəstək növlərini axtarırıq. Vaxtınızı və bacarıqlarınızı töhfə vermək istəyirsinizsə, biz həmişə inkişaf etdiricilər, tərcüməçilər və s. axtarırıq. Marağınız və dəstəyiniz üçün təşəkkür edirik. Yenilik tokenləri Texnologiya yığımımızla başlayaq. O, qəsdən darıxdırıcıdır. Flask, MariaDB və ElasticSearch istifadə edirik. Sözün əsl mənasında bu qədər. Axtarış əsasən həll olunmuş bir problemdir və biz onu yenidən icad etmək niyyətində deyilik. Bundan əlavə, <a %(mcfunley)s>yenilik tokenlərimizi</a> başqa bir şeyə xərcləməliyik: hakimiyyət tərəfindən bağlanmamaq. Bəs Anna’nın Arxivi nə qədər qanuni və ya qeyri-qanunidir? Bu, əsasən hüquqi yurisdiksiyadan asılıdır. Əksər ölkələr müəyyən bir müddət üçün müəyyən növ əsərlərə eksklüziv inhisar verən müəllif hüquqlarına inanır. Yan not olaraq, Anna’nın Arxivində biz inanırıq ki, bəzi faydalar olsa da, ümumilikdə müəllif hüquqları cəmiyyət üçün mənfi bir təsirə malikdir — amma bu başqa bir hekayədir. Müəyyən əsərlərə olan bu eksklüziv inhisar, bu inhisarın xaricində olan hər kəsin həmin əsərləri birbaşa yaymasının qanunsuz olduğu anlamına gəlir — biz də daxil olmaqla. Amma Anna’nın Arxivi birbaşa həmin əsərləri yaymayan bir axtarış mühərrikidir (ən azı bizim açıq internet saytımızda deyil), buna görə də biz yaxşı olmalıyıq, elə deyilmi? Tam olaraq deyil. Bir çox yurisdiksiyalarda müəllif hüquqları ilə qorunan əsərləri yaymaq qanunsuz olmaqla yanaşı, həmin əsərlərə keçid vermək də qanunsuzdur. Bunun klassik bir nümunəsi ABŞ-ın DMCA qanunudur. Bu, spektrin ən sərt ucudur. Spektrin digər ucunda nəzəri olaraq heç bir müəllif hüququ qanunu olmayan ölkələr ola bilər, amma belə ölkələr həqiqətən mövcud deyil. Demək olar ki, hər bir ölkədə kitablarında bir növ müəllif hüququ qanunu var. İcra isə fərqli bir hekayədir. Müəllif hüququ qanunlarını icra etməyə maraq göstərməyən hökumətləri olan bir çox ölkə var. İki ekstrem arasında olan və müəllif hüquqları ilə qorunan əsərlərin yayılmasını qadağan edən, lakin belə əsərlərə keçid verməyi qadağan etməyən ölkələr də var. Başqa bir nəzərə alınmalı məsələ şirkət səviyyəsindədir. Əgər bir şirkət müəllif hüquqlarına əhəmiyyət verməyən bir yurisdiksiyada fəaliyyət göstərirsə, lakin şirkətin özü heç bir riskə girmək istəmirsə, o zaman kimsə şikayət etdikdə saytınızı bağlaya bilər. Nəhayət, böyük bir məsələ ödənişlərdir. Anonim qalmalı olduğumuz üçün ənənəvi ödəniş üsullarından istifadə edə bilmirik. Bu, bizi kriptovalyutalara məcbur edir və yalnız az sayda şirkət onları dəstəkləyir (kriptovalyuta ilə ödənilən virtual debet kartları var, lakin onlar tez-tez qəbul edilmir). - Anna və komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Mən <a %(wikipedia_annas_archive)s>Anna'nın Arxivini</a> idarə edirəm, Sci-Hub, Library Genesis və Z-Kitabxana kimi <a %(wikipedia_shadow_library)s>kölgə kitabxanaları</a> üçün dünyanın ən böyük açıq mənbəli qeyri-kommersiya axtarış mühərriki. Məqsədimiz bilik və mədəniyyəti asanlıqla əlçatan etmək və nəticədə <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>dünyadakı bütün kitabları</a> arxivləşdirən və qoruyan bir icma yaratmaqdır. Bu məqalədə mən bu vebsaytı necə idarə etdiyimizi və kölgə xeyriyyə təşkilatları üçün “AWS” olmadığı üçün hüquqi statusu şübhəli olan bir vebsaytın idarə edilməsi ilə bağlı unikal çətinlikləri göstərəcəyəm. <em>Bacının məqaləsinə də baxın <a %(blog_how_to_become_a_pirate_archivist)s>Necə pirat arxivçi olmaq olar</a>.</em> Kölgə kitabxanasını necə idarə etmək olar: Anna'nın Arxivində əməliyyatlar Kölgə xeyriyyə təşkilatları üçün <q>AWS yoxdur,</q> bəs Anna'nın Arxivini necə idarə edirik? Alətlər Tətbiq serveri: Flask, MariaDB, ElasticSearch, Docker. İnkişaf: Gitlab, Weblate, Zulip. Server idarəetməsi: Ansible, Checkmk, UFW. Soğan statik hostinq: Tor, Nginx. Proksi server: Varnish. Bütün bunları həyata keçirmək üçün hansı alətlərdən istifadə etdiyimizə baxaq. Bu, yeni problemlərlə qarşılaşdıqca və yeni həllər tapdıqca çox inkişaf edir. Bəzi qərarlar var ki, biz onlara dəfələrlə qayıtmışıq. Biri serverlər arasında ünsiyyətdir: əvvəllər bunun üçün Wireguard istifadə edirdik, lakin onun bəzən heç bir məlumat ötürmədiyini və ya yalnız bir istiqamətdə məlumat ötürdüyünü gördük. Bu, <a %(github_costela_wesher)s>wesher</a> və <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a> kimi bir neçə fərqli Wireguard quruluşunda baş verdi. Biz həmçinin autossh və sshuttle istifadə edərək SSH üzərindən portları tunelləşdirməyə çalışdıq, lakin <a %(github_sshuttle)s>orada problemlərlə</a> qarşılaşdıq (baxmayaraq ki, autossh-un TCP-over-TCP problemlərindən əziyyət çəkib-çəkmədiyi hələ də mənə aydın deyil — bu mənə sadəcə olaraq qarmaqarışıq bir həll kimi gəlir, amma bəlkə də əslində yaxşıdır?). Bunun əvəzinə, serverlər arasında birbaşa əlaqələrə qayıtdıq, ucuz provayderlərdə serverin işlədiyini gizlətmək üçün UFW ilə IP-filtrləmə istifadə etdik. Bunun mənfi tərəfi odur ki, Docker UFW ilə yaxşı işləmir, əgər <code>network_mode: "host"</code> istifadə etməsəniz. Bütün bunlar bir az daha səhv meyllidir, çünki kiçik bir yanlış konfiqurasiya ilə serverinizi internetə məruz qoyacaqsınız. Bəlkə də autossh-a qayıtmalıyıq — burada rəy çox xoş qarşılanar. Biz həmçinin Varnish və Nginx arasında gedib-gəlmişik. Hal-hazırda Varnish-i bəyənirik, lakin onun da öz qəribəlikləri və kobud kənarları var. Eyni şey Checkmk üçün də keçərlidir: biz onu sevmirik, amma hələlik işləyir. Weblate yaxşı olub, amma inanılmaz deyil — bəzən qorxuram ki, onu git repo ilə sinxronlaşdırmağa çalışanda məlumatlarımı itirəcək. Flask ümumilikdə yaxşı olub, amma xüsusi domenlərin konfiqurasiyası və ya SqlAlchemy inteqrasiyası ilə bağlı problemlər kimi çox vaxt aparan qəribəlikləri var. İndiyə qədər digər alətlər əla olub: MariaDB, ElasticSearch, Gitlab, Zulip, Docker və Tor haqqında ciddi şikayətimiz yoxdur. Bunların hamısında bəzi problemlər olub, amma heç biri çox ciddi və ya vaxt aparan deyil. İcma İlk problem təəccüblü ola bilər. Bu, texniki və ya hüquqi bir problem deyil. Bu, psixoloji bir problemdir: kölgələrdə bu işi görmək inanılmaz dərəcədə tənha ola bilər. Nə etməyi planlaşdırdığınıza və təhdid modelinizə görə çox diqqətli olmalısınız. Spektrin bir ucunda, fəaliyyətləri haqqında çox açıq olan Sci-Hub-un qurucusu Aleksandra Elbakyan* kimi insanlar var. Ancaq o, bu anda qərb ölkəsinə səfər edərsə, həbs olunma riski yüksəkdir və onilliklər boyu həbs cəzası ilə üzləşə bilər. Bu, qəbul etməyə hazır olduğunuz bir riskdirmi? Biz spektrin digər ucundayıq; heç bir iz buraxmamağa və güclü əməliyyat təhlükəsizliyinə sahib olmağa çox diqqət edirik. * HN-də "ynno" tərəfindən qeyd edildiyi kimi, Aleksandra əvvəlcə tanınmaq istəmirdi: "Onun serverləri PHP-dən ətraflı səhv mesajları yaymaq üçün qurulmuşdu, o cümlədən səhv mənbə faylının tam yolu, bu da /home/<USER>" Beləliklə, bu işlər üçün istifadə etdiyiniz kompüterlərdə təsadüfi istifadəçi adlarından istifadə edin, əgər bir şeyi səhv konfiqurasiya etsəniz. Lakin bu gizlilik psixoloji bir xərc ilə gəlir. Çox insan etdikləri iş üçün tanınmağı sevir, lakin real həyatda bunun üçün heç bir kredit ala bilməzsiniz. Hətta sadə şeylər çətin ola bilər, məsələn, dostlarınız sizdən nə ilə məşğul olduğunuzu soruşduqda ("NAS / homelab ilə qarışmaq" bir müddət sonra köhnəlir). Bu səbəbdən bəzi icma tapmaq çox vacibdir. Çox yaxın dostlarınıza etibar edərək bəzi əməliyyat təhlükəsizliyindən imtina edə bilərsiniz, kimə dərin etibar edə biləcəyinizi bilirsiniz. Hətta o zaman, onların e-poçtlarını hakimiyyətə təhvil verməli olduqları halda və ya cihazları başqa bir şəkildə təhlükəyə düşərsə, heç bir şeyi yazılı şəkildə qoymamağa diqqət edin. Daha yaxşısı, bəzi həmkar piratlarla tanış olmaqdır. Əgər yaxın dostlarınız sizə qoşulmaqda maraqlıdırsa, əla! Əks halda, başqalarını onlayn tapa bilərsiniz. Təəssüf ki, bu hələ də niş bir icmadır. İndiyə qədər bu sahədə aktiv olan yalnız bir neçə nəfər tapmışıq. Yaxşı başlanğıc yerləri Library Genesis forumları və r/DataHoarder kimi görünür. Arxiv Komandasında da eyni düşüncədə olan şəxslər var, baxmayaraq ki, onlar qanun çərçivəsində fəaliyyət göstərirlər (hətta qanunun bəzi boz sahələrində olsa belə). Ənənəvi "warez" və piratlıq səhnələrində də oxşar düşüncədə olan insanlar var. İcmanı necə inkişaf etdirmək və ideyaları araşdırmaq barədə fikirlərə açığıq. Bizə Twitter və ya Reddit vasitəsilə mesaj göndərməkdən çəkinməyin. Bəlkə də bir növ forum və ya söhbət qrupu təşkil edə bilərik. Bir çətinlik odur ki, bu, ümumi platformalardan istifadə edərkən asanlıqla senzura edilə bilər, buna görə də onu özümüz təşkil etməliyik. Bu müzakirələrin tamamilə ictimai (daha çox potensial iştirak) və ya özəl (potensial "hədəflərin" onları qıracağımızı bilməməsi) olması arasında bir ticarət var. Bu barədə düşünməliyik. Əgər bununla maraqlanırsınızsa, bizə bildirin! Nəticə Ümid edirik ki, bu, yeni başlayan pirat arxivçilər üçün faydalıdır. Sizi bu dünyaya salamlamaqdan məmnunuq, ona görə də əlaqə saxlamaqdan çəkinməyin. Dünyanın bilik və mədəniyyətini mümkün qədər çox qoruyub saxlayaq və geniş yayaq. Layihələr 4. Məlumat seçimi Çox vaxt metadata-dan istifadə edərək yükləmək üçün məntiqli bir məlumat alt dəstini müəyyən edə bilərsiniz. Hətta nəticədə bütün məlumatları yükləmək istəsəniz belə, ən vacib elementləri əvvəlcə prioritetləşdirmək faydalı ola bilər, əgər siz aşkar olunursunuzsa və müdafiələr yaxşılaşdırılırsa, ya da daha çox disk almaq məcburiyyətində qalacaqsınızsa, ya da sadəcə həyatınızda başqa bir şey baş verərsə və hər şeyi yükləməkdən əvvəl. Məsələn, bir kolleksiya eyni əsas resursun (kitab və ya film kimi) bir neçə nəşrinə sahib ola bilər, burada biri ən yaxşı keyfiyyət kimi işarələnmişdir. Bu nəşrləri əvvəlcə saxlamaq çox məntiqli olardı. Nəticədə bütün nəşrləri saxlamaq istəyə bilərsiniz, çünki bəzi hallarda metadata səhv etiketlənə bilər və ya nəşrlər arasında bilinməyən kompromislər ola bilər (məsələn, "ən yaxşı nəşr" əksər cəhətdən ən yaxşı ola bilər, lakin digər cəhətlərdə, məsələn, filmin daha yüksək çözünürlükdə olması, lakin altyazıların olmaması kimi, daha pis ola bilər). Metadata bazanızı maraqlı şeylər tapmaq üçün də axtara bilərsiniz. Ən böyük fayl nədir və niyə bu qədər böyükdür? Ən kiçik fayl nədir? Müəyyən kateqoriyalar, dillər və s. ilə bağlı maraqlı və ya gözlənilməz nümunələr varmı? Eyni və ya çox oxşar başlıqlar varmı? Məlumatın nə vaxt əlavə edildiyinə dair nümunələr varmı, məsələn, bir gün bir çox faylın bir anda əlavə edildiyi bir gün? Çox vaxt məlumat dəstinə fərqli yollarla baxaraq çox şey öyrənə bilərsiniz. Bizim vəziyyətimizdə, Z-Kitabxana kitablarını Library Genesis-dəki md5 hash-lara qarşı deduplikasiya etdik, beləliklə çoxlu yükləmə vaxtı və disk sahəsi qənaət etdik. Bu olduqca unikal bir vəziyyətdir. Çox hallarda, hansı faylların artıq düzgün qorunduğunu göstərən hərtərəfli məlumat bazaları yoxdur. Bu, özü-özlüyündə kimsə üçün böyük bir fürsətdir. Torrent veb saytlarında artıq geniş yayılmış musiqi və filmlər kimi şeylərin müntəzəm olaraq yenilənən bir icmalına sahib olmaq əla olardı və buna görə də pirat güzgülərə daxil etmək üçün daha aşağı prioritetdir. 6. Paylama Məlumatlar sizdədir, beləliklə, dünyanın ilk pirat güzgüsünə sahib olursunuz (çox güman ki). Bir çox cəhətdən ən çətin hissə bitdi, lakin ən riskli hissə hələ də qabaqdadır. Axı, indiyə qədər gizli idiniz; radardan kənarda uçurdunuz. Yalnız yaxşı bir VPN istifadə etmək, heç bir formda şəxsi məlumatlarınızı doldurmamaq (duh) və bəlkə də xüsusi bir brauzer sessiyası (və ya hətta fərqli bir kompüter) istifadə etmək lazım idi. İndi məlumatları paylamalısınız. Bizim vəziyyətimizdə əvvəlcə kitabları Library Genesis-ə geri töhfə vermək istədik, lakin tez bir zamanda bunun çətinliklərini (bədii vs qeyri-bədii təsnifatı) kəşf etdik. Beləliklə, Library Genesis tərzində torrentlərdən istifadə edərək paylama qərarına gəldik. Mövcud bir layihəyə töhfə vermək imkanı varsa, bu sizə çox vaxt qazandıra bilər. Ancaq hazırda yaxşı təşkil olunmuş pirat güzgülər çox deyil. Beləliklə, deyək ki, torrentləri özünüz paylamağa qərar verdiniz. Bu faylları kiçik saxlayın ki, digər veb saytlarında güzgüləmək asan olsun. Sonra torrentləri özünüz toxumlamalı olacaqsınız, hələ də anonim qalaraq. VPN istifadə edə bilərsiniz (port yönləndirmə ilə və ya olmadan), ya da bir Seedbox üçün tumbled Bitcoins ilə ödəyə bilərsiniz. Bu terminlərin bəzilərinin nə demək olduğunu bilmirsinizsə, oxumaq üçün bir çox şey olacaq, çünki burada risk ticarətlərini başa düşməyiniz vacibdir. Torrent fayllarını özləri mövcud torrent veb saytlarında yerləşdirə bilərsiniz. Bizim vəziyyətimizdə, əslində bir veb sayt yerləşdirməyi seçdik, çünki fəlsəfəmizi aydın şəkildə yaymaq istəyirdik. Bunu özünüz də oxşar şəkildə edə bilərsiniz (domenlərimiz və hostinqimiz üçün Njalla istifadə edirik, tumbled Bitcoins ilə ödənilir), lakin torrentlərinizi yerləşdirmək üçün bizimlə əlaqə saxlamaqdan çəkinməyin. Bu fikir tutarsa, zamanla pirat güzgülərin hərtərəfli indeksini qurmağı düşünürük. VPN seçimi ilə bağlı çox şey yazılıb, buna görə də sadəcə nüfuzla seçməklə bağlı ümumi məsləhəti təkrarlayacağıq. Məhkəmə tərəfindən sınaqdan keçirilmiş, uzun müddətli məxfilik qoruma təcrübəsi olan qeydsiz siyasətlər, bizim fikrimizcə, ən aşağı riskli seçimdir. Qeyd edək ki, hər şeyi düzgün etsəniz belə, riski sıfıra endirə bilməzsiniz. Məsələn, torrentlərinizi toxumlayarkən, yüksək motivasiyalı bir dövlət aktoru, VPN serverləri üçün gələn və gedən məlumat axınlarına baxa bilər və kim olduğunuzu müəyyən edə bilər. Və ya sadəcə bir şəkildə səhv edə bilərsiniz. Yəqin ki, biz artıq etmişik və yenə edəcəyik. Xoşbəxtlikdən, dövlətlər piratçılıqla o qədər də maraqlanmır. Hər bir layihə üçün bir qərar vermək lazımdır, əvvəlki kimliklə yayımlamaq və ya yox. Eyni adı istifadə etməyə davam etsəniz, əvvəlki layihələrdən əməliyyat təhlükəsizliyindəki səhvlər geri dönüb sizi dişləyə bilər. Ancaq fərqli adlar altında yayımlamaq, daha uzun müddətli bir nüfuz yaratmamağınız deməkdir. Biz, eyni kimliyi istifadə etməyə davam edə bilmək üçün başlanğıcdan güclü əməliyyat təhlükəsizliyinə sahib olmağı seçdik, lakin səhv etsək və ya şərait tələb edərsə, fərqli bir ad altında yayımlamaqdan çəkinməyəcəyik. Sözü yaymaq çətin ola bilər. Dediyimiz kimi, bu hələ də niş bir icmadır. Əvvəlcə Reddit-də paylaşdıq, amma həqiqətən Hacker News-da diqqət çəkdik. Hazırda tövsiyəmiz bir neçə yerdə paylaşmaq və nə baş verdiyini görməkdir. Yenə də bizimlə əlaqə saxlayın. Daha çox pirat arxivçilik səylərinin yayılmasını çox istərdik. 1. Domen seçimi / fəlsəfə Qorunması lazım olan bilik və mədəni irs çatışmazlığı yoxdur, bu da çox ola bilər. Buna görə də, tez-tez bir anlıq dayanıb sizin töhfənizin nə ola biləcəyini düşünmək faydalıdır. Hər kəsin bu barədə düşünmək üçün fərqli bir yolu var, lakin özünüzə verə biləcəyiniz bəzi suallar bunlardır: Bizim vəziyyətimizdə, biz xüsusilə elmin uzunmüddətli qorunmasına əhəmiyyət verdik. Library Genesis haqqında bilirdik və onun torrentlərdən istifadə edərək dəfələrlə tamamilə əks olunduğunu bilirdik. Bu ideyanı çox sevirdik. Sonra bir gün, aramızdan biri Library Genesis-də bəzi elmi dərslikləri tapmağa çalışdı, lakin onları tapa bilmədi, bu da onun nə qədər tam olduğunu şübhə altına aldı. Sonra həmin dərslikləri onlayn axtardıq və onları başqa yerlərdə tapdıq, bu da layihəmizin toxumunu əkdi. Z-Library haqqında bilməzdən əvvəl belə, bütün bu kitabları əl ilə toplamağa çalışmamaq, mövcud kolleksiyaları əks etdirməyə diqqət yetirmək və onları Library Genesis-ə geri töhfə vermək ideyamız var idi. Öz xeyrinizə istifadə edə biləcəyiniz hansı bacarıqlarınız var? Məsələn, əgər siz onlayn təhlükəsizlik mütəxəssisisinizsə, təhlükəsiz hədəflər üçün IP bloklarını məğlub etməyin yollarını tapa bilərsiniz. Əgər icmaları təşkil etməkdə əlasınızsa, bəlkə də bir məqsəd ətrafında bəzi insanları bir araya gətirə bilərsiniz. Bununla belə, bu proses boyunca yaxşı əməliyyat təhlükəsizliyini qorumaq üçün bir az proqramlaşdırma bilmək faydalıdır. Diqqət mərkəzində olmaq üçün yüksək təsirli bir sahə nə ola bilər? Əgər pirat arxivləşdirməyə X saat sərf edəcəksinizsə, onda necə ən böyük "qarşılığını" əldə edə bilərsiniz? Bu barədə düşündüyünüz unikal yollar hansılardır? Başqalarının qaçırmış ola biləcəyi maraqlı ideyalarınız və ya yanaşmalarınız ola bilər. Buna nə qədər vaxtınız var? Məsləhətimiz kiçikdən başlamaq və buna alışdıqca daha böyük layihələr həyata keçirməkdir, lakin bu, tamamilə istehlak edilə bilər. Niyə bu mövzu ilə maraqlanırsınız? Nəyə ehtiraslısınız? Əgər biz hər biri xüsusi olaraq maraqlandığı şeyləri arxivləşdirən bir çox insanı bir araya gətirə bilsək, bu çox şeyi əhatə edər! Ehtirasınız haqqında orta insandan daha çox şey biləcəksiniz, məsələn, hansı məlumatların qorunması vacibdir, ən yaxşı kolleksiyalar və onlayn icmalar hansılardır və s. 3. Metadata qırma Əlavə/modifikasiya tarixi: daha sonra geri qayıdıb əvvəl yükləmədiyiniz faylları yükləyə bilmək üçün (baxmayaraq ki, bunun üçün tez-tez ID və ya hash-dən də istifadə edə bilərsiniz). Hash (md5, sha1): faylı düzgün yüklədiyinizi təsdiqləmək üçün. ID: bəzi daxili ID ola bilər, lakin ISBN və ya DOI kimi ID-lər də faydalıdır. Fayl adı / yeri Təsvir, kateqoriya, etiketlər, müəlliflər, dil və s. Ölçü: nə qədər disk sahəsinə ehtiyacınız olduğunu hesablamaq üçün. Burada bir az daha texniki olaq. Vebsaytlardan metadata qırmaq üçün işləri olduqca sadə saxlamışıq. Nəticələri saxlamaq üçün Python skriptlərindən, bəzən curl-dan və MySQL bazasından istifadə edirik. Kompleks vebsaytları xəritələndirə bilən hər hansı bir dəbdəbəli qırma proqramından istifadə etməmişik, çünki indiyə qədər yalnız id-ləri sadalayaraq və HTML-i təhlil edərək bir və ya iki növ səhifəni qırmağa ehtiyacımız olub. Asanlıqla sadalanan səhifələr yoxdursa, bütün səhifələri tapmağa çalışan düzgün bir tarayıcıya ehtiyacınız ola bilər. Bütün vebsaytı qırmağa başlamazdan əvvəl, bir az əl ilə etməyə çalışın. Bunun necə işlədiyini anlamaq üçün özünüz bir neçə on səhifədən keçin. Bəzən bu yolla artıq IP bloklarına və ya digər maraqlı davranışlara rast gələcəksiniz. Məlumat qırma üçün də eyni şey keçərlidir: bu hədəfə çox dərindən girməzdən əvvəl, onun məlumatlarını effektiv şəkildə yükləyə biləcəyinizə əmin olun. Məhdudiyyətləri aşmaq üçün bir neçə şeyi sınaya bilərsiniz. Eyni məlumatı saxlayan, lakin eyni məhdudiyyətlərə malik olmayan digər IP ünvanları və ya serverlər varmı? Bəzi API son nöqtələri məhdudiyyətlərə malik deyil, digərləri isə varmı? IP-niz hansı yükləmə sürətində bloklanır və nə qədər müddətə? Yoxsa bloklanmırsınız, amma sürətiniz azaldılır? İstifadəçi hesabı yaratsanız, vəziyyət necə dəyişir? HTTP/2 istifadə edərək bağlantıları açıq saxlaya bilərsinizmi və bu, səhifələri tələb etmə sürətinizi artırırmı? Bir neçə faylı bir anda siyahıya alan səhifələr varmı və orada göstərilən məlumatlar kifayətdirmi? Yəqin ki, saxlamaq istədiyiniz şeylər bunlardır: Biz adətən bunu iki mərhələdə edirik. Əvvəlcə xam HTML fayllarını yükləyirik, adətən birbaşa MySQL-ə (çoxlu kiçik fayllardan qaçmaq üçün, bu barədə aşağıda daha çox danışırıq). Sonra, ayrı bir addımda, həmin HTML fayllarını keçib onları faktiki MySQL cədvəllərinə çeviririk. Bu yolla, əgər parçalayıcı kodunuzda bir səhv aşkar etsəniz, hər şeyi sıfırdan yenidən yükləmək məcburiyyətində qalmayacaqsınız, çünki sadəcə yeni kodla HTML fayllarını yenidən işləyə bilərsiniz. Həmçinin, emal addımını paralelləşdirmək daha asan olur, beləliklə bir az vaxt qazanırsınız (və emal kodunu yazarkən, skreypinq işləyərkən yazmaq əvəzinə, hər iki addımı bir anda yazmaq məcburiyyətində qalmırsınız). Nəhayət, bəzi hədəflər üçün metadata skreypinqinin yeganə şey olduğunu qeyd edin. Orada düzgün qorunmayan böyük metadata kolleksiyaları var. Başlıq Domen seçimi / fəlsəfə: Harada təxminən diqqət mərkəzində olmaq istəyirsiniz və niyə? Hansı unikal ehtiraslarınız, bacarıqlarınız və şəraitiniz var ki, bunları öz xeyrinizə istifadə edə bilərsiniz? Hədəf seçimi: Hansı xüsusi kolleksiyanı əks etdirəcəksiniz? Metadata yığımı: Fayllar haqqında məlumatların kataloqu, əslində (çox vaxt daha böyük olan) faylları yükləmədən. Məlumat seçimi: Metadata əsasında hansı məlumatların hazırda arxivləşdirilməsi üçün ən uyğun olduğunu daraltmaq. Hər şey ola bilər, lakin tez-tez məkanı və bant genişliyini qənaət etmək üçün məqbul bir yol var. Məlumat yığımı: Əslində məlumatların əldə edilməsi. Paylama: Onu torrentlərdə paketləmək, bir yerdə elan etmək, insanların onu yaymasını təmin etmək. 5. Məlumat skreypinqi İndi məlumatları toplu şəkildə yükləməyə hazırsınız. Daha əvvəl qeyd edildiyi kimi, bu nöqtədə artıq hədəfin davranışını və məhdudiyyətlərini daha yaxşı başa düşmək üçün bir çox faylı əl ilə yükləmiş olmalısınız. Ancaq bir çox faylı bir anda yükləməyə başladığınızda hələ də sizi gözləyən sürprizlər olacaq. Buradakı məsləhətimiz əsasən sadə saxlamaqdır. Sadəcə bir çox faylı yükləməklə başlayın. Python istifadə edə bilərsiniz və sonra bir neçə ipə genişlənə bilərsiniz. Ancaq bəzən daha sadə olan, birbaşa məlumat bazasından Bash faylları yaratmaq və sonra onları bir neçə terminal pəncərəsində işlədərək miqyası artırmaqdır. Burada qeyd etməyə dəyər bir texniki hiylə, MySQL-də OUTFILE istifadə etməkdir, bunu mysqld.cnf-də "secure_file_priv"i deaktiv etsəniz hər yerdə yaza bilərsiniz (və Linux-da olsanız, AppArmor-u da deaktiv etməyi/üstələməyi unutmayın). Məlumatları sadə sərt disklərdə saxlayırıq. Nə varsa onunla başlayın və yavaş-yavaş genişlənin. Yüzlərlə TB məlumat saxlamağı düşünmək çox çətin ola bilər. Əgər bu vəziyyətlə üzləşirsinizsə, sadəcə yaxşı bir alt dəst qoyun və elanınızda qalanını saxlamaq üçün kömək istəyin. Əgər özünüz daha çox sərt disk almaq istəyirsinizsə, r/DataHoarder-da yaxşı təkliflər əldə etmək üçün bəzi yaxşı mənbələr var. Çox mürəkkəb fayl sistemləri haqqında çox narahat olmağa çalışmayın. ZFS kimi şeylər qurmaq üçün dovşan çuxuruna düşmək asandır. Ancaq xəbərdar olmalı olduğunuz bir texniki detal, bir çox fayl ilə bir çox fayl sistemlərinin yaxşı işləməməsidir. Sadə bir həll yolu, müxtəlif ID aralıqları və ya hash prefiksləri üçün bir neçə qovluq yaratmaqdır. Məlumatları yüklədikdən sonra, mövcud olduqda, metadata-dakı hash-lardan istifadə edərək faylların bütövlüyünü yoxlayın. 2. Hədəf seçimi Əlçatan: metadata və məlumatlarını qırmağınıza mane olmaq üçün çoxlu qoruma qatları istifadə etmir. Xüsusi məlumat: bu hədəf haqqında xüsusi məlumatınız var, məsələn, bu kolleksiyaya xüsusi girişiniz var və ya onların müdafiəsini necə aşacağınızı anladınız. Bu tələb olunmur (gələcək layihəmiz xüsusi bir şey etmir), amma əlbəttə ki, kömək edir! Böyük Beləliklə, baxdığımız sahəni müəyyən etdik, indi hansı xüsusi kolleksiyanı əks etdirəcəyik? Yaxşı bir hədəf üçün bir neçə şey var: Elm dərsliklərimizi Library Genesis-dən başqa vebsaytlarda tapdığımızda, onların internetə necə düşdüyünü anlamağa çalışdıq. Sonra Z-Library-ni tapdıq və başa düşdük ki, əksər kitablar ilk dəfə orada görünməsə də, nəticədə orada bitir. Onun Library Genesis ilə əlaqəsi və (maliyyə) təşviq strukturu və üstün istifadəçi interfeysi haqqında öyrəndik, bunların hər ikisi onu daha tam bir kolleksiya etdi. Sonra bəzi ilkin metadata və məlumat qırma işləri apardıq və üzvlərimizdən birinin çoxlu proxy serverlərə xüsusi girişindən istifadə edərək onların IP yükləmə məhdudiyyətlərini aşmağın mümkün olduğunu anladıq. Fərqli hədəfləri araşdırarkən, izlərinizi gizlətmək üçün VPN-lərdən və birdəfəlik e-poçt ünvanlarından istifadə etmək artıq vacibdir, bu barədə daha sonra danışacağıq. Unikal: digər layihələr tərəfindən artıq yaxşı əhatə olunmamış. Bir layihə həyata keçirdiyimiz zaman onun bir neçə mərhələsi var: Bunlar tamamilə müstəqil mərhələlər deyil və tez-tez sonrakı mərhələdən əldə edilən anlayışlar sizi əvvəlki mərhələyə geri göndərir. Məsələn, metadata yığımı zamanı seçdiyiniz hədəfin bacarıq səviyyənizdən kənarda müdafiə mexanizmlərinə (məsələn, IP blokları) malik olduğunu başa düşə bilərsiniz, buna görə də geri qayıdıb başqa bir hədəf tapırsınız. - Anna və komanda (<a %(reddit)s>Reddit</a>) Ümumiyyətlə rəqəmsal qorunmanın və xüsusilə pirat arxivçiliyin <em>nə üçün</em> olduğuna dair bütöv kitablar yazıla bilər, lakin çox tanış olmayanlar üçün qısa bir giriş verək. Dünya heç vaxt olmadığı qədər çox bilik və mədəniyyət istehsal edir, lakin eyni zamanda heç vaxt olmadığı qədər çoxu itirilir. İnsanlıq bu mirası əsasən akademik nəşriyyatlar, axın xidmətləri və sosial media şirkətləri kimi korporasiyalara etibar edir və onlar tez-tez böyük qəyyumlar olmadıqlarını sübut ediblər. Digital Amnesia sənədli filmini və ya Jason Scott-un hər hansı bir çıxışını izləyin. Bəzi qurumlar əllərindən gələni arxivləşdirməkdə yaxşı iş görürlər, lakin onlar qanunla məhdudlaşdırılırlar. Piratlar olaraq, biz müəllif hüquqlarının tətbiqi və ya digər məhdudiyyətlər səbəbindən toxuna bilmədikləri kolleksiyaları arxivləşdirmək üçün unikal bir mövqedəyik. Həmçinin, kolleksiyaları dünya üzrə bir çox dəfə güzgüləyə bilərik, beləliklə düzgün qorunma şansını artırırıq. Hələlik, intellektual mülkiyyətin üstünlükləri və mənfi cəhətləri, qanunu pozmağın əxlaqı, senzura haqqında düşüncələr və ya bilik və mədəniyyətə çıxış məsələsi haqqında müzakirələrə girməyəcəyik. Bütün bunları bir kənara qoyaraq, <em>necə</em> daldırma edək. Komandamızın necə pirat arxivçilər olduğumuzu və bu yolda öyrəndiyimiz dərsləri paylaşacağıq. Bu səyahətə çıxarkən bir çox çətinliklər var və ümid edirik ki, bəzilərində sizə kömək edə bilərik. Necə pirat arxivçi olmaq olar İlk problem təəccüblü ola bilər. Bu, texniki və ya hüquqi bir problem deyil. Bu, psixoloji bir problemdir. Dərinliyə getməzdən əvvəl, Pirate Library Mirror haqqında iki yenilik (DÜZƏLİŞ: <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a>na köçürüldü): Biz çox səxavətli ianələr aldıq. Birincisi, Library Genesis-in orijinal qurucusu "bookwarrior"u dəstəkləyən anonim bir şəxsdən 10 min dollar idi. Bu ianəni asanlaşdırdığı üçün bookwarrior-a xüsusi təşəkkürlər. İkincisi, son buraxılışımızdan sonra bizimlə əlaqə saxlayan və kömək etmək üçün ilhamlanan başqa bir anonim ianəçidən daha 10 min dollar idi. Həmçinin bir sıra kiçik ianələr də aldıq. Bütün səxavətli dəstəyiniz üçün çox təşəkkür edirik. Bu, dəstəkləyəcək maraqlı yeni layihələrimiz var, buna görə də bizi izləyin. İkinci buraxılışımızın ölçüsü ilə bağlı bəzi texniki çətinliklərimiz oldu, lakin torrentlərimiz indi yüklənir və toxumlanır. Həmçinin, kolleksiyamızı çox yüksək sürətli serverlərində toxumlamaq üçün anonim bir şəxsdən səxavətli bir təklif aldıq, buna görə də onların maşınlarına xüsusi bir yükləmə edirik, bundan sonra kolleksiyanı yükləyən hər kəs sürətdə böyük bir yaxşılaşma görməlidir. Bloq yazıları Salam, mən Anna. Mən <a %(wikipedia_annas_archive)s>Anna Arxivi</a>ni, dünyanın ən böyük kölgə kitabxanasını yaratdım. Bu mənim şəxsi bloqumdur, burada mən və komandam piratçılıq, rəqəmsal qorunma və daha çox mövzularda yazırıq. Mənimlə <a %(reddit)s>Reddit</a>də əlaqə saxlayın. Qeyd edək ki, bu vebsayt sadəcə bir bloqdur. Burada yalnız öz sözlərimizi yerləşdiririk. Burada heç bir torrent və ya digər müəllif hüquqları ilə qorunan fayllar yerləşdirilmir və ya əlaqələndirilmir. <strong>Kitabxana</strong> - Əksər kitabxanalar kimi, biz əsasən kitablar kimi yazılı materiallara diqqət yetiririk. Gələcəkdə digər media növlərinə də genişlənə bilərik. <strong>Güzgü</strong> - Biz mövcud kitabxanaların yalnız bir güzgüsüyük. Biz qorunmaya diqqət yetiririk, kitabların asanlıqla axtarılmasını və yüklənməsini (giriş) və ya yeni kitablar əlavə edən böyük bir icma yaratmağı (mənbə) deyil. <strong>Pirat</strong> - Biz əksər ölkələrdə müəllif hüquqları qanununu qəsdən pozuruq. Bu, bizə hüquqi qurumların edə bilmədiyi bir şeyi etməyə imkan verir: kitabların geniş yayılmasını təmin etmək. <em>Bu bloqdan fayllara keçid vermirik. Zəhmət olmasa, özünüz tapın.</em> - Anna və komanda (<a %(reddit)s>Reddit</a>) Bu layihə (<a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> köçürüldü) insan biliklərinin qorunmasına və azad edilməsinə töhfə verməyi hədəfləyir. Bizdən əvvəl gələn böyük insanların izində kiçik və təvazökar töhfəmizi veririk. Bu layihənin diqqət mərkəzi onun adı ilə izah olunur: Güzgülədiyimiz ilk kitabxana Z-Kitabxanadır. Bu, məşhur (və qeyri-qanuni) bir kitabxanadır. Onlar Library Genesis kolleksiyasını götürüb asanlıqla axtarıla bilən hala gətiriblər. Üstəlik, yeni kitab töhfələrini təşviq edərək, istifadəçiləri müxtəlif üstünlüklərlə təşviq edərək çox təsirli olublar. Hazırda bu yeni kitabları Library Genesis-ə geri töhfə vermirlər. Və Library Genesis-dən fərqli olaraq, kolleksiyalarını asanlıqla güzgülənə bilən etmirlər, bu da geniş qorunmanın qarşısını alır. Bu, onların biznes modelinə vacibdir, çünki kolleksiyalarına toplu şəkildə (gündə 10-dan çox kitab) daxil olmaq üçün pul alırlar. Qeyri-qanuni kitab kolleksiyasına toplu giriş üçün pul almaqla bağlı mənəvi mühakimələr etmirik. Şübhəsiz ki, Z-Kitabxana biliklərə çıxışı genişləndirməkdə və daha çox kitab əldə etməkdə uğurlu olub. Biz sadəcə öz işimizi görmək üçün buradayıq: bu şəxsi kolleksiyanın uzunmüddətli qorunmasını təmin etmək. İnsan biliklərini qorumağa və azad etməyə kömək etmək üçün torrentlərimizi yükləməyə və paylaşmağa dəvət edirik. Məlumatların necə təşkil edildiyi haqqında daha çox məlumat üçün layihə səhifəsinə baxın. Hansı kolleksiyaların növbəti güzgülənməsi və bunu necə etmək barədə fikirlərinizi də çox məmnuniyyətlə qəbul edirik. Birlikdə çox şeyə nail ola bilərik. Bu, saysız-hesabsız digər töhfələr arasında kiçik bir töhfədir. Etdiyiniz hər şey üçün təşəkkür edirik. Pirat Kitabxana Güzgüsünü təqdim edirik: 7TB kitabın qorunması (Libgen-də olmayanlar) 10% o insanlığın yazılı irsi əbədi qorunur <strong>Google.</strong> Axı, onlar bu araşdırmanı Google Books üçün etdilər. Lakin onların metadata toplu şəkildə əlçatan deyil və onu çıxarmaq olduqca çətindir. <strong>Müxtəlif fərdi kitabxana sistemləri və arxivlər.</strong> Yuxarıda qeyd olunanlar tərəfindən indekslənməmiş və toplanmamış kitabxanalar və arxivlər var, çox vaxt maliyyə çatışmazlığı səbəbindən və ya digər səbəblərdən Open Library, OCLC, Google və s. ilə məlumatlarını paylaşmaq istəmirlər. Bunların çoxu internet vasitəsilə əlçatan rəqəmsal qeydlərə malikdir və onlar çox vaxt yaxşı qorunmur, buna görə də kömək etmək və qəribə kitabxana sistemləri haqqında öyrənmək istəyirsinizsə, bunlar əla başlanğıc nöqtələridir. <strong>ISBNdb.</strong> Bu, bu blog yazısının mövzusudur. ISBNdb müxtəlif veb saytları kitab metadata üçün, xüsusilə qiymət məlumatları üçün skan edir, sonra isə bu məlumatları kitab satıcılarına satır, beləliklə, onlar kitablarını bazarın qalan hissəsinə uyğun qiymətləndirə bilərlər. ISBN-lər bu günlərdə olduqca universal olduğundan, onlar effektiv olaraq “hər kitab üçün bir veb səhifə” yaratdılar. <strong>Open Library.</strong> Daha əvvəl qeyd edildiyi kimi, bu onların bütün missiyasıdır. Onlar əməkdaşlıq edən kitabxanalardan və milli arxivlərdən böyük miqdarda kitabxana məlumatları əldə ediblər və bunu davam etdirirlər. Onların həmçinin qeydləri deduplikasiya etməyə və onları hər cür metadata ilə etiketləməyə çalışan könüllü kitabxanaçılar və texniki komandası var. Ən yaxşısı, onların dataseti tamamilə açıqdır. Sadəcə olaraq onu <a %(openlibrary)s>yükləyə</a> bilərsiniz. <strong>WorldCat.</strong> Bu, qeyri-kommersiya OCLC tərəfindən idarə olunan bir veb saytdır, kitabxana idarəetmə sistemləri satır. Onlar bir çox kitabxanadan kitab metadata toplayır və onu WorldCat veb saytı vasitəsilə əlçatan edir. Lakin onlar bu məlumatı sataraq pul qazanırlar, buna görə də toplu yükləmə üçün əlçatan deyil. Onlar bəzi daha məhdud toplu datasetləri müəyyən kitabxanalarla əməkdaşlıqda yükləmə üçün əlçatan edirlər. 1. "Əbədi"nin bəzi məqbul tərifləri üçün. ;) 2. Əlbəttə ki, bəşəriyyətin yazılı irsi kitabdan çoxdur, xüsusən də bu günlərdə. Bu yazı və son buraxılışlarımız üçün biz kitablar üzərində cəmləşirik, lakin maraqlarımız daha genişdir. 3. Aaron Swartz haqqında daha çox şey deyilə bilər, lakin biz sadəcə onu qısaca qeyd etmək istədik, çünki o, bu hekayədə mühüm rol oynayır. Zaman keçdikcə daha çox insan onun adını ilk dəfə eşidə bilər və sonradan özləri bu mövzuya dərinləşə bilərlər. <strong>Fiziki nüsxələr.</strong> Aydındır ki, bu çox faydalı deyil, çünki onlar sadəcə eyni materialın təkrarıdır. İnsanların kitabda etdikləri bütün qeydləri, məsələn, Fermatın məşhur “kənar qeydləri” kimi qoruyub saxlaya bilsək, çox gözəl olardı. Amma təəssüf ki, bu, arxivçinin arzusu olaraq qalacaq. <strong>“Nəşrlər”.</strong> Burada kitabın hər unikal versiyasını sayırsınız. Əgər onun haqqında hər hansı bir şey fərqlidirsə, məsələn, fərqli bir üz qabığı və ya fərqli bir ön söz, bu fərqli bir nəşr sayılır. <strong>Fayllar.</strong> Library Genesis, Sci-Hub və ya Z-Library kimi kölgə kitabxanaları ilə işləyərkən əlavə bir məsələ var. Eyni nəşrin bir neçə skanı ola bilər. Və insanlar mövcud faylların daha yaxşı versiyalarını yarada bilərlər, məsələn, mətnin OCR ilə skan edilməsi və ya bucaqlı skan edilmiş səhifələrin düzəldilməsi ilə. Biz bu faylları yalnız bir nəşr kimi saymaq istəyirik, bu isə yaxşı metadata və ya sənəd oxşarlığı ölçüləri ilə deduplikasiya tələb edər. <strong>“Əsərlər”.</strong> Məsələn, “Harry Potter və Sirlər Otağı” kimi bir məntiqi konsepsiya, onun bütün versiyalarını, məsələn, müxtəlif tərcümələr və yenidən nəşrləri əhatə edir. Bu, bir növ faydalı tərifdir, lakin nəyi saymaq lazım olduğunu müəyyən etmək çətin ola bilər. Məsələn, müxtəlif tərcümələri qorumaq istəyə bilərik, lakin yalnız kiçik fərqlərlə yenidən nəşrlər o qədər də vacib olmaya bilər. - Anna və komanda (<a %(reddit)s>Reddit</a>) Pirat Kitabxana Güzgüsü ilə (EDIT: <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a> köçürüldü), məqsədimiz dünyadakı bütün kitabları götürmək və onları əbədi qorumaqdır.<sup>1</sup> Z-Kitabxana torrentlərimiz və orijinal Library Genesis torrentlərimiz arasında 11,783,153 fayl var. Amma bu, həqiqətən nə qədərdir? Əgər bu faylları düzgün şəkildə təkrarlasaydıq, dünyadakı bütün kitabların neçə faizini qorumuşuq? Belə bir şeyə sahib olmaq istərdik: Gəlin bəzi təxmini rəqəmlərlə başlayaq: Həm Z-Library/Libgen, həm də Open Library-də unikal ISBN-lərdən daha çox kitab var. Bu, o deməkdir ki, bu kitabların çoxunun ISBN-i yoxdur, yoxsa ISBN metadata sadəcə olaraq çatışmır? Yəqin ki, bu suala digər atributlara (başlıq, müəllif, nəşriyyat və s.) əsaslanan avtomatlaşdırılmış uyğunlaşdırma, daha çox məlumat mənbələrinin cəlb edilməsi və ISBN-lərin faktiki kitab skanlarından çıxarılması (Z-Library/Libgen halında) ilə cavab verə bilərik. Bu ISBN-lərin neçəsi unikaldır? Bu, ən yaxşı şəkildə Venn diaqramı ilə izah olunur: Daha dəqiq desək: Biz nə qədər az üst-üstə düşmə olduğunu görəndə təəccübləndik! ISBNdb-də Z-Library və ya Open Library-də görünməyən çoxlu ISBN var və eyni şey digər ikisi üçün də (daha kiçik, lakin hələ də əhəmiyyətli dərəcədə) doğrudur. Bu, bir çox yeni suallar doğurur. ISBN-lərlə işarələnməmiş kitabların işarələnməsində avtomatik uyğunlaşdırma nə qədər kömək edərdi? Çoxlu uyğunluqlar və buna görə də artan üst-üstə düşmə olardımı? Həmçinin, əgər 4-cü və ya 5-ci dataseti daxil etsək nə baş verərdi? O zaman nə qədər üst-üstə düşmə görərdik? Bu bizə başlanğıc nöqtəsi verir. İndi Z-Library datasetində olmayan və başlıq/müəllif sahələri ilə də uyğun gəlməyən bütün ISBN-lərə baxa bilərik. Bu, dünyadakı bütün kitabları qorumaq üçün bizə bir vasitə verə bilər: əvvəlcə internetdən skanlar toplayaraq, sonra isə real həyatda kitabları skan edərək. İkincisi hətta kütləvi maliyyələşdirilə bilər və ya müəyyən kitabların rəqəmsallaşdırılmasını istəyən insanların “mükafatları” ilə idarə oluna bilər. Bütün bunlar başqa bir zamanın hekayəsidir. Əgər bu işlərdən hər hansı birinə kömək etmək istəyirsinizsə — daha çox analiz; daha çox metadata toplamaq; daha çox kitab tapmaq; kitabların OCR edilməsi; bunu digər sahələr üçün etmək (məsələn, məqalələr, audiokitablar, filmlər, televiziya şouları, jurnallar) və ya hətta bu məlumatların bir qismini ML / böyük dil modeli təlimi üçün əlçatan etmək — mənimlə əlaqə saxlayın (<a %(reddit)s>Reddit</a>). Əgər konkret olaraq məlumat analizinə maraqlısınızsa, biz datasetlərimizi və skriptlərimizi daha asan istifadə edilə bilən formatda əlçatan etmək üzərində işləyirik. Sadəcə bir dəftəri fork edib bununla oynamağa başlamağınız əla olardı. Nəhayət, bu işi dəstəkləmək istəyirsinizsə, xahiş edirik ianə etməyi düşünün. Bu, tamamilə könüllülər tərəfindən idarə olunan bir əməliyyatdır və sizin töhfəniz böyük fərq yaradır. Hər bir az kömək edir. Hal-hazırda kriptoda ianələr qəbul edirik; Anna Arxivi saytında İanə səhifəsinə baxın. Faiz üçün bizə məxrəç lazımdır: indiyə qədər nəşr olunmuş kitabların ümumi sayı.<sup>2</sup> Google Books-un bağlanmasından əvvəl, layihədə çalışan mühəndis Leonid Taycher, <a %(booksearch_blogspot)s>bu rəqəmi təxmin etməyə çalışdı</a>. O, zarafatla 129,864,880 rəqəmi ilə gəldi (“ən azı bazar gününə qədər”). O, bu rəqəmi dünyadakı bütün kitabların vahid bazasını quraraq təxmin etdi. Bunun üçün o, müxtəlif datasetləri bir araya gətirdi və sonra onları müxtəlif yollarla birləşdirdi. Qısa bir kənara çıxaraq, dünyadakı bütün kitabları kataloqlaşdırmağa çalışan başqa bir şəxs də var: rəhmətlik rəqəmsal aktivist və Reddit-in həmtəsisçisi Aaron Swartz.<sup>3</sup> O, <a %(youtube)s>Open Library-ni başlatdı</a> və məqsədi “nəşr olunmuş hər kitab üçün bir veb səhifə” yaratmaq idi, müxtəlif mənbələrdən məlumatları birləşdirərək. O, akademik məqalələri kütləvi şəkildə yüklədiyi üçün təqib edildikdə rəqəmsal qoruma işi üçün ən yüksək qiyməti ödəyərək intihar etdi. Təbii ki, bu, qrupumuzun niyə təxəllüslə işlədiyinin və niyə çox diqqətli olduğumuzun səbəblərindən biridir. Open Library hələ də İnternet Arxivindəki insanlar tərəfindən qəhrəmancasına idarə olunur və Aaronun mirasını davam etdirir. Bu yazının sonrakı hissəsində buna qayıdacağıq. Google blog yazısında Taycher bu rəqəmi təxmin etməklə bağlı bəzi çətinlikləri təsvir edir. Birincisi, kitab nədir? Bir neçə mümkün tərif var: “Nəşrlər” “kitablar”ın ən praktik tərifi kimi görünür. Rahatlıqla, bu tərif unikal ISBN nömrələrinin təyin edilməsi üçün də istifadə olunur. ISBN, yəni Beynəlxalq Standart Kitab Nömrəsi, beynəlxalq ticarət üçün geniş istifadə olunur, çünki o, beynəlxalq barkod sistemi ilə inteqrasiya olunub (“Beynəlxalq Məqalə Nömrəsi”). Əgər kitabı mağazalarda satmaq istəyirsinizsə, ona barkod lazımdır, buna görə də ISBN alırsınız. Taycherin blog yazısında qeyd olunur ki, ISBN-lər faydalı olsa da, universal deyil, çünki onlar yalnız yetmişinci illərin ortalarında həqiqətən qəbul edilib və dünyanın hər yerində deyil. Yenə də ISBN kitab nəşrlərinin ən geniş istifadə olunan identifikatorudur, buna görə də bu, bizim üçün ən yaxşı başlanğıc nöqtəsidir. Əgər dünyadakı bütün ISBN-ləri tapa bilsək, hələ də qorunması lazım olan kitabların faydalı siyahısını əldə edirik. Bəs, məlumatı haradan əldə edirik? Dünyadakı bütün kitabların siyahısını tərtib etməyə çalışan bir sıra mövcud səylər var: Bu yazıda, kiçik bir buraxılışı elan etməkdən məmnunuq (əvvəlki Z-Library buraxılışlarımızla müqayisədə). Biz ISBNdb-nin əksəriyyətini skan etdik və məlumatları Pirate Library Mirror veb saytında torrent üçün əlçatan etdik (RED: <a %(wikipedia_annas_archive)s>Anna’nın Arxivi</a>nə köçürüldü; onu burada birbaşa əlaqələndirməyəcəyik, sadəcə axtarın). Bunlar təxminən 30.9 milyon qeyd (20GB <a %(jsonlines)s>JSON Lines</a> kimi; 4.4GB sıxılmış). Onların veb saytında əslində 32.6 milyon qeyd olduğunu iddia edirlər, buna görə də biz nədənsə bəzilərini qaçırmış ola bilərik, ya da <em>onlar</em> nəyisə səhv edə bilərlər. Hər halda, hələlik bunu necə etdiyimizi dəqiq paylaşmayacağıq — bunu oxucu üçün bir məşq olaraq buraxacağıq. ;-) Paylaşacağımız şey bəzi ilkin təhlillərdir, dünyadakı kitabların sayını təxmin etməyə daha yaxınlaşmağa çalışmaq üçün. Biz üç datasetə baxdıq: bu yeni ISBNdb dataset, Z-Library kölgə kitabxanasından (Library Genesis daxil olmaqla) skan etdiyimiz metadata-nın orijinal buraxılışımız və Open Library məlumat yığını. ISBNdb dump, ya da Neçə Kitab Əbədi Qorunur? Əgər kölgə kitabxanalarından faylları düzgün şəkildə təkrarlasaq, dünyadakı bütün kitabların neçə faizini qorumuşuq? <a %(wikipedia_annas_archive)s>Anna Arxivi</a> haqqında yeniliklər, insan tarixində ən böyük həqiqətən açıq kitabxana. <em>WorldCat yenidən dizaynı</em> Məlumat <strong>Format?</strong> <a %(blog)s>Anna’nın Arxivi Konteynerləri (AAC)</a>, əslində <a %(jsonlines)s>JSON Lines</a> ilə sıxılmış <a %(zstd)s>Zstandard</a>, üstəgəl bəzi standartlaşdırılmış semantika. Bu konteynerlər tətbiq etdiyimiz müxtəlif toplama növlərinə əsaslanan müxtəlif növ qeydləri əhatə edir. Bir il əvvəl, bu sualı <a %(blog)s>cavablandırmağa</a> başladıq: <strong>Kitabların hansı faizi kölgə kitabxanaları tərəfindən daimi olaraq qorunub saxlanılıb?</strong> Gəlin məlumatlar haqqında bəzi əsas məlumatlara baxaq: Bir kitab <a %(wikipedia_library_genesis)s>Library Genesis</a> kimi açıq məlumat kölgə kitabxanasına və indi <a %(wikipedia_annas_archive)s>Anna Arxivi</a>nə daxil olduqda, o, bütün dünyada (torrentlər vasitəsilə) əks olunur və beləliklə, onu praktiki olaraq əbədi qoruyur. Kitabların neçə faizinin qorunduğu sualına cavab vermək üçün məxrəci bilməliyik: ümumilikdə neçə kitab mövcuddur? Və ideal olaraq, sadəcə bir rəqəm deyil, əsl metadata da olmalıdır. Sonra onları kölgə kitabxanalarına qarşı uyğunlaşdırmaqla yanaşı, qorunacaq qalan kitabların TODO siyahısını yarada bilərik! Hətta bu TODO siyahısını yerinə yetirmək üçün kütləvi bir səylə xəyal etməyə başlaya bilərik. <a %(wikipedia_isbndb_com)s>ISBNdb</a> məlumatlarını topladıq və <a %(openlibrary)s>Open Library dataset</a>ini yüklədik, lakin nəticələr qənaətbəxş olmadı. Əsas problem ISBN-lərin çox az üst-üstə düşməsi idi. <a %(blog)s>blog yazımızdan</a> bu Venn diaqramına baxın: ISBNdb və Open Library arasında nə qədər az üst-üstə düşmə olduğunu görəndə çox təəccübləndik, hər ikisi müxtəlif mənbələrdən, məsələn, veb toplama və kitabxana qeydlərindən məlumatları geniş şəkildə daxil edir. Əgər hər ikisi oradakı əksər ISBN-ləri tapmaqda yaxşı iş görsələr, onların dairələri mütləq əhəmiyyətli dərəcədə üst-üstə düşərdi və ya biri digərinin alt dəsti olardı. Bu bizi düşündürdü, bu dairələrin tamamilə kənarında neçə kitab var? Daha böyük bir verilənlər bazasına ehtiyacımız var. Bu zaman diqqətimizi dünyanın ən böyük kitab verilənlər bazasına yönəltdik: <a %(wikipedia_worldcat)s>WorldCat</a>. Bu, qeyri-kommersiya <a %(wikipedia_oclc)s>OCLC</a> tərəfindən idarə olunan bir verilənlər bazasıdır, bu da kitabxanalara tam datasetə giriş imkanı vermək və onların son istifadəçilərin axtarış nəticələrində görünmələri qarşılığında dünyanın hər yerindən kitabxana metadata qeydlərini toplayır. OCLC qeyri-kommersiya olsa da, onların biznes modeli verilənlər bazasını qorumağı tələb edir. Yaxşı, OCLC-dəki dostlar, üzr istəyirik, biz hamısını paylayırıq. :-) Son bir il ərzində WorldCat qeydlərini diqqətlə topladıq. Əvvəlcə şanslı bir fasilə ilə qarşılaşdıq. WorldCat tam veb sayt yenidən dizaynını (Avq 2022-də) həyata keçirirdi. Bu, onların arxa sistemlərinin əhəmiyyətli dərəcədə yenidən qurulmasını, bir çox təhlükəsizlik qüsurlarının ortaya çıxmasını əhatə edirdi. Dərhal fürsətdən istifadə etdik və bir neçə gün ərzində yüz milyonlarla (!) qeydləri toplamağa müvəffəq olduq. Bundan sonra, təhlükəsizlik qüsurları bir-bir yavaş-yavaş düzəldildi, sonuncusu təxminən bir ay əvvəl düzəldildi. O vaxta qədər demək olar ki, bütün qeydlərimiz var idi və yalnız bir az daha yüksək keyfiyyətli qeydlər üçün gedirdik. Beləliklə, buraxmağın vaxtı gəldiyini düşündük! 1.3B WorldCat skrepi <em><strong>Qısa məzmun:</strong> Anna Arxivi bütün WorldCat-ı (dünyanın ən böyük kitabxana metadata kolleksiyası) skrep edərək qorunması lazım olan kitabların TODO siyahısını hazırladı.</em> WorldCat Xəbərdarlıq: bu bloq yazısı köhnəlmişdir. Biz qərara gəldik ki, IPFS hələ əsas vaxt üçün hazır deyil. Anna'nın Arxivindən mümkün olduqda IPFS-dəki fayllara hələ də keçid verəcəyik, lakin artıq özümüz host etməyəcəyik və başqalarına IPFS istifadə edərək güzgü etməyi tövsiyə etmirik. Kolleksiyamızı qorumağa kömək etmək istəyirsinizsə, Torrents səhifəmizə baxın. Z-Kitabxanasını IPFS-də toxumlayın Tərəfdaş Server yükləməsi SciDB Xarici borc Xarici borc (çap məhdudiyyəti) Xarici yükləmə Metadatanı araşdırın Torrentlərdə saxlanılır Geri  (+%(num)s bonus) ödənişsiz ödənişli ləğv edildi müddəti bitmiş Anna-nın təsdiqini gözləyir etibarsız Mətn aşağıda ingiliscə davam edir. Get Sıfırla İrəli Son E-poçt ünvanınız Libgen forumlarında işləmirsə, <a %(a_mail)s>Proton Mail</a> (pulsuz) istifadə etməyi tövsiyə edirik. Hesabınızın aktivləşdirilməsi üçün <a %(a_manual)s>əl ilə sorğu göndərə</a> bilərsiniz. (brauzer təsdiqi tələb oluna bilər <a %(a_browser)s>— limitsiz yükləmələr!</a>) Sürətli Tərəfdaş Serveri #%(number)s (tövsiyə olunur) (biraz daha sürətli, amma gözləmə siyahısı ilə) (brauzer təsdiqi tələb olunmur) (brauzer təsdiqi və ya gözləmə siyahıları yoxdur) (gözləmə siyahısı yoxdur, lakin çox yavaş ola bilər) Yavaş Tərəfdaş Serveri #%(number)s Audiokitab Komiks kitabı Kitab (bədii ədəbiyyat) Kitab (qeyri-bədii) Kitab (naməlum) Jurnal məqaləsi Jurnal Musiqi notları Digər Standartlar sənədi Bütün səhifələr PDF-ə çevrilə bilmədi Libgen.li-də qırıq olaraq işarələndi Libgen.li-də görünmür Libgen.rs-də Mövcud deyil Libgen.rs Qeyri-Bədii bölməsində görünmür Bu faylda exiftool işə salınması uğursuz oldu Z-Kitabxanada “pis fayl” kimi işarələnib Z-Kitabxanadan itkin Z-Kitabxanada “spam” kimi işarələnib Fayl açıla bilmir (məsələn, zədələnmiş fayl, DRM) Müəllif hüquqları iddiası Yükləmə problemləri (məsələn, qoşulmaq mümkün deyil, səhv mesajı, çox yavaş) Yanlış metadata (məsələn, başlıq, təsvir, üz qabığı şəkli) Digər Zəif keyfiyyət (məsələn, formatlama problemləri, zəif skan keyfiyyəti, itkin səhifələr) Spam / fayl silinməlidir (məsələn, reklam, təhqiredici məzmun) %(amount)s (%(amount_usd)s) %(amount)s cəmi %(amount)s (%(amount_usd)s) cəmi Parlaq Kitabqurdu Şanslı Kitabxanaçı Möhtəşəm Məlumat Toplayıcı Möhtəşəm Arxivçi Bonus yükləmələr Cerlalc Çexiya metadatası DuXiu 读秀 EBSCOhost eBook İndeksi Google Kitablar Goodreads HathiTrust IA IA Nəzarətli Rəqəmsal Kredit ISBNdb ISBN GRP Libgen.li “scimag” istisna olmaqla Libgen.rs Qeyri-bədii və Bədii Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Rusiya Dövlət Kitabxanası Sci-Hub Libgen.li “scimag” vasitəsilə Sci-Hub / Libgen “scimag” Trantor AA-ya yükləmələr Z-Kitabxana Z-Kitabxana Başlıq, müəllif, DOI, ISBN, MD5, … Axtarış Müəllif Təsvir və metadata şərhləri Nəşr Orijinal fayl adı Nəşriyyat (xüsusi sahəni axtarın) Başlıq Nəşr ili Texniki detallar Bu sikkənin minimum məbləği adi haldan yüksəkdir. Zəhmət olmasa fərqli bir müddət və ya fərqli bir sikkə seçin. Sorğu tamamlanmadı. Zəhmət olmasa bir neçə dəqiqədən sonra yenidən cəhd edin və əgər bu davam edərsə, %(email)s ünvanına ekran görüntüsü ilə birlikdə bizimlə əlaqə saxlayın. Naməlum bir səhv baş verdi. Zəhmət olmasa, %(email)s ilə ekran görüntüsü ilə bizimlə əlaqə saxlayın. Ödəniş emalında səhv. Bir az gözləyin və yenidən cəhd edin. Problem 24 saatdan çox davam edərsə, zəhmət olmasa %(email)s ilə ekran görüntüsü ilə əlaqə saxlayın. Dünyanın ən böyük komiks kölgə kitabxanasını <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">dəstəkləmək</a> üçün ianə kampaniyası keçiririk. Dəstəyiniz üçün təşəkkür edirik! <a href="/donate">İanə edin.</a> İanə edə bilmirsinizsə, dostlarınıza xəbər verməklə və bizi <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> və ya <a href="https://t.me/annasarchiveorg">Telegram</a> üzərindən izləməklə dəstək ola bilərsiniz. Bizə <a %(a_request)s>kitablar tələb etmək</a><br>və ya kiçik (<10k) <a %(a_upload)s>yükləmələr</a> üçün e-poçt göndərməyin. Anna’nın Arxivi DMCA / müəllif hüquqları iddiaları Əlaqədə qalın Reddit Alternativlər SLUM (%(unaffiliated)s) əlaqəsiz Anna’nın Arxivinə köməyiniz lazımdır! Əgər indi ianə etsəniz, <strong>ikiqat</strong> sürətli yükləmə sayı əldə edirsiniz. Çoxları bizi məhv etməyə çalışır, amma biz mübarizə aparırıq. Əgər bu ay ianə etsəniz, <strong>ikiqat</strong> sürətli yükləmə əldə edəcəksiniz. Bu ayın sonuna qədər etibarlıdır. İnsan biliklərini qorumaq: möhtəşəm bir bayram hədiyyəsi! Üzvlüklər müvafiq olaraq uzadılacaq. Tərəfdaş serverlər hosting bağlanmaları səbəbindən əlçatan deyil. Onlar tezliklə yenidən aktiv olacaq. Anna’nın Arxivinin dayanıqlığını artırmaq üçün güzgülər işlədən könüllülər axtarırıq. Yeni bir ianə üsulumuz mövcuddur: %(method_name)s. Zəhmət olmasa %(donate_link_open_tag)sianə etməyi düşünün</a> — bu vebsaytı idarə etmək ucuz deyil və ianəniz həqiqətən fərq yaradır. Çox sağ olun. Bir dostunuza tövsiyə edin və həm siz, həm də dostunuz %(percentage)s%% bonus sürətli yükləmələr əldə edin! Sevdiyinizi təəccübləndirin, ona üzvlüklə hesab verin. Mükəmməl Sevgililər Günü hədiyyəsi! Daha çox öyrənin… Hesab Fəaliyyət İrəli Anna’nın Bloqu ↗ Anna’nın Proqramı ↗ beta Kodlar Kəşfiyyatçısı Datasets Bağışlayın Yüklənmiş fayllar Tez-tez verilən suallar Ana səhifə Metadatanı təkmilləşdirin LLM məlumatları Daxil ol / Qeydiyyatdan keç Mənim ianələrim İctimai profil Axtarış Təhlükəsizlik Torrentlər Tərcümə et ↗ Könüllülük və Mükafatlar Son yükləmələr: 📚&nbsp;Dünyanın ən böyük açıq mənbəli açıq məlumat kitabxanası. ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library və daha çoxunu əks etdirir. 📈&nbsp;%(book_any)s kitablar, %(journal_article)s məqalələr, %(book_comic)s komikslər, %(magazine)s jurnallar — əbədi qorunur.  və  və daha çox DuXiu Internet Archive Lending Library LibGen 📚&nbsp;İnsan tarixində ən böyük həqiqətən açıq kitabxana. 📈&nbsp;%(book_count)s&nbsp;kitablar, %(paper_count)s&nbsp;məqalələr — əbədi qorunur. ⭐️&nbsp;Biz %(libraries)s nüsxəsini yaradırıq. Biz %(scraped)s məlumatlarını toplayır və açıq mənbə edirik. Bütün kodlarımız və məlumatlarımız tamamilə açıq mənbəlidir. OpenLib Sci-Hub ,  📚 Dünyanın ən böyük açıq mənbəli açıq məlumat kitabxanası.<br>⭐️ Scihub, Libgen, Zlib və daha çoxunu əks etdirir. Z-Lib Anna’nın Arxivi Yanlış sorğu. %(websites)s ziyarət edin. Dünyanın ən böyük açıq mənbəli açıq məlumat kitabxanası. Sci-Hub, Library Genesis, Z-Library və daha çoxunu əks etdirir. Anna’nın Arxivində axtar Anna’nın Arxivi Yenidən cəhd etmək üçün səhifəni yeniləyin. Problem bir neçə saat davam edərsə, <a %(a_contact)s>bizimlə əlaqə saxlayın</a>. 🔥 Bu səhifəni yükləməkdə problem <li>1. Bizi <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> və ya <a href="https://t.me/annasarchiveorg">Telegram</a> üzərindən izləyin.</li><li>2. Anna’nın Arxivini Twitter, Reddit, Tiktok, Instagram, yerli kafe və ya kitabxanada və ya getdiyiniz hər yerdə tanıdın! Biz qapalı saxlamağa inanmırıq — əgər biz bağlansaq, bütün kod və məlumatlarımız tamamilə açıq mənbəli olduğundan başqa yerdə yenidən ortaya çıxacağıq.</li><li>3. Əgər bacarırsınızsa, <a href="/donate">ianə etməyi</a> düşünün.</li><li>4. Veb saytımızı müxtəlif dillərə <a href="https://translate.annas-software.org/">tərcümə etməyə</a> kömək edin.</li><li>5. Əgər proqram mühəndisisinizsə, açıq mənbəli <a href="https://annas-software.org/">layihələrimizə</a> töhfə verməyi və ya <a href="/datasets">torrentlərimizi</a> paylaşmağı düşünün.</li> 10. Öz dilinizdə Anna Arxivi üçün Vikipediya səhifəsi yaradın və ya mövcud səhifəni qoruyub saxlayın. 11. Kiçik, zövqlü reklamlar yerləşdirməyi düşünürük. Anna’nın Arxivində reklam vermək istəyirsinizsə, bizə bildirin. 6. Əgər siz təhlükəsizlik tədqiqatçısısınızsa, bacarıqlarınızı həm hücum, həm də müdafiə üçün istifadə edə bilərik. <a %(a_security)s>Təhlükəsizlik</a> səhifəmizə baxın. 7. Anonim tacirlər üçün ödəniş mütəxəssisləri axtarırıq. Daha rahat ianə üsulları əlavə etməyimizə kömək edə bilərsinizmi? PayPal, WeChat, hədiyyə kartları. Əgər tanıdığınız biri varsa, bizimlə əlaqə saxlayın. 8. Biz həmişə daha çox server tutumu axtarırıq. 9. Fayl problemlərini bildirməklə, şərhlər yazmaqla və bu vebsaytda siyahılar yaratmaqla kömək edə bilərsiniz. Həmçinin <a %(a_upload)s>daha çox kitab yükləməklə</a> və ya mövcud kitabların fayl problemlərini və ya formatını düzəltməklə də kömək edə bilərsiniz. Könüllü olmaq haqqında daha ətraflı məlumat üçün <a %(a_volunteering)s>Könüllülük və Mükafatlar</a> səhifəmizə baxın. Biz məlumatın sərbəst axınına, bilik və mədəniyyətin qorunmasına güclü inanırıq. Bu axtarış mühərriki ilə biz nəhənglərin çiyinlərində qururuq. Müxtəlif kölgə kitabxanalarını yaradan insanların ağır zəhmətinə dərin hörmət edirik və ümid edirik ki, bu axtarış mühərriki onların əhatə dairəsini genişləndirəcək. İrəliləyişimizdən xəbərdar olmaq üçün Anna-nı <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> və ya <a href="https://t.me/annasarchiveorg">Telegram</a> üzərindən izləyin. Suallar və rəy üçün Anna ilə %(email)s ünvanında əlaqə saxlayın. Hesab ID: %(account_id)s Çıxış ❌ Bir şey səhv getdi. Zəhmət olmasa səhifəni yeniləyin və yenidən cəhd edin. ✅ Siz indi çıxış etdiniz. Yenidən daxil olmaq üçün səhifəni yeniləyin. Sürətli yükləmələr istifadə olunub (son 24 saat): <strong>%(used)s / %(total)s</strong> Üzvlük: <strong>%(tier_name)s</strong> %(until_date)s tarixinə qədər <a %(a_extend)s>(uzat)</a> Bir neçə üzvlüyü birləşdirə bilərsiniz (24 saat ərzində sürətli yükləmələr birləşdiriləcək). Üzvlük: <strong>Heç biri</strong> <a %(a_become)s>(üzv olun)</a> Anna ilə %(email)s ünvanında əlaqə saxlayın, əgər üzvlüyünüzü daha yüksək səviyyəyə yüksəltmək istəyirsinizsə. İctimai profil: %(profile_link)s Gizli açar (paylaşmayın!): %(secret_key)s göstər Bizə burada qoşulun! Qrupumuza qoşulmaq üçün <a %(a_tier)s>daha yüksək səviyyəyə</a> yüksəldin. Eksklüziv Telegram qrupu: %(link)s Hesab hansı yükləmələr? Daxil ol Açarınızı itirməyin! Yanlış gizli açar. Açarınızı yoxlayın və yenidən cəhd edin, ya da aşağıda yeni hesab qeydiyyatdan keçirin. Gizli açar Giriş etmək üçün gizli açarınızı daxil edin: Köhnə e-poçt əsaslı hesab? <a %(a_open)s>e-poçtunuzu buraya daxil edin</a>. Yeni hesab qeydiyyatdan keçirin Hələ hesabınız yoxdur? Qeydiyyat uğurlu! Gizli açarınız: <span %(span_key)s>%(key)s</span> Bu açarı diqqətlə saxlayın. Onu itirsəniz, hesabınıza girişinizi itirəcəksiniz. <li %(li_item)s><strong>Əlfəcin.</strong> Bu səhifəni əlfəcinləyə bilərsiniz ki, açarınızı geri qaytarasınız.</li><li %(li_item)s><strong>Yüklə.</strong> Açarınızı yükləmək üçün <a %(a_download)s>bu linkə</a> klikləyin.</li><li %(li_item)s><strong>Şifrə meneceri.</strong> Açarı aşağıda daxil edərkən onu saxlamaq üçün şifrə menecerindən istifadə edin.</li> Daxil ol / Qeydiyyatdan keç Brauzer təsdiqi Xəbərdarlıq: kodda səhv Unicode simvolları var və müxtəlif vəziyyətlərdə düzgün işləməyə bilər. Xam ikili məlumat URL-dəki base64 təmsilindən dekod edilə bilər. Təsvir Etiket Prefiks Müəyyən kod üçün URL Vebsayt “%(prefix_label)s” ilə başlayan kodlar Zəhmət olmasa bu səhifələri kazımayın. Bunun əvəzinə <a %(a_import)s>yaratmağı</a> və ya <a %(a_download)s>yükləməyi</a> və ElasticSearch və MariaDB verilənlər bazalarımızı işlətməyi tövsiyə edirik, və <a %(a_software)s>açıq mənbə kodumuzu</a> işlətməyi tövsiyə edirik. Xam məlumatlar <a %(a_json_file)s>bu kimi</a> JSON faylları vasitəsilə əl ilə araşdırıla bilər. %(count)s qeyddən az Ümumi URL Kodlar Kəşfiyyatçısı İndeks Qeydlərin prefiks ilə etiketləndiyi kodları araşdırın. “Qeydlər” sütunu, axtarış motorunda (yalnız metadata qeydləri daxil olmaqla) verilmiş prefiks ilə kodlarla etiketlənmiş qeydlərin sayını göstərir. “Kodlar” sütunu isə verilmiş prefiksə malik faktiki kodların sayını göstərir. Məlum kod prefiksi “%(key)s” Daha çox… Prefiks %(count)s qeydi “%(prefix_label)s” ilə uyğun gəlir %(count)s qeydləri “%(prefix_label)s” ilə uyğun gəlir kodlar qeydlər “%%” kodun dəyəri ilə əvəz olunacaq Anna Arxivində axtarış et Kodlar Müəyyən kod üçün URL: “%(url)s” Bu səhifənin yaradılması bir az vaxt ala bilər, buna görə də Cloudflare captcha tələb olunur. <a %(a_donate)s>Üzvlər</a> captcha-dan keçə bilərlər. Təhqir bildirildi: Daha yaxşı versiya Bu istifadəçini təhqiramiz və ya uyğun olmayan davranışa görə bildirmək istəyirsinizmi? Fayl problemi: %(file_issue)s gizli şərh Cavab ver Təhqir bildirişi Bu istifadəçini təhqirə görə bildirdiniz. Bu e-poçt ünvanına göndərilən müəllif hüquqları iddiaları nəzərə alınmayacaq; bunun əvəzinə formadan istifadə edin. E-poçtu göstər Rəy və suallarınızı çox gözləyirik! Lakin aldığımız spam və mənasız e-poçtların miqdarına görə, bizimlə əlaqə saxlamaq üçün bu şərtləri başa düşdüyünüzü təsdiqləmək üçün qutuları işarələyin. Müəllif hüquqları iddiaları ilə bağlı bizimlə əlaqə saxlamağın digər yolları avtomatik olaraq silinəcək. DMCA / müəllif hüquqları iddiaları üçün <a %(a_copyright)s>bu formadan</a> istifadə edin. Əlaqə emaili Anna’nın Arxivi üzərindəki URL-lər (tələb olunur). Hər sətirdə bir URL. Zəhmət olmasa, yalnız eyni kitabın eyni nəşrini təsvir edən URL-ləri daxil edin. Bir neçə kitab və ya bir neçə nəşr üçün iddia etmək istəyirsinizsə, bu formu bir neçə dəfə təqdim edin. Bir neçə kitab və ya nəşri birləşdirən iddialar rədd ediləcək. Ünvan (tələb olunur) Mənbə materialının dəqiq təsviri (tələb olunur) E-poçt (tələb olunur) Mənbə materialının URL-ləri, hər sətirdə bir URL (tələb olunur). İddianızı təsdiqləməyimizə kömək etmək üçün mümkün qədər çoxunu daxil edin (məsələn, Amazon, WorldCat, Google Books, DOI). Mənbə materialının ISBN-ləri (əgər tətbiq olunursa). Hər sətirdə bir ISBN. Zəhmət olmasa, yalnız müəllif hüquqları iddiası etdiyiniz nəşrə tam uyğun olanları daxil edin. Adınız (tələb olunur) ❌ Bir şey səhv getdi. Zəhmət olmasa səhifəni yenidən yükləyin və yenidən cəhd edin. ✅ Müəllif hüquqları iddianızı təqdim etdiyiniz üçün təşəkkür edirik. Biz onu mümkün qədər tez nəzərdən keçirəcəyik. Başqa birini təqdim etmək üçün səhifəni yenidən yükləyin. <a %(a_openlib)s>Open Library</a> mənbə materialının URL-ləri, hər sətirdə bir URL. Zəhmət olmasa, mənbə materialınızı Open Library-də axtarmağa bir az vaxt ayırın. Bu, iddianızı təsdiqləməyimizə kömək edəcək. Telefon nömrəsi (tələb olunur) Bəyanat və imza (tələb olunur) İddianı təqdim et Əgər sizin DCMA və ya digər müəllif hüquqları iddianız varsa, zəhmət olmasa bu formu mümkün qədər dəqiq doldurun. Hər hansı bir problemlə qarşılaşsanız, bizim xüsusi DMCA ünvanımıza müraciət edin: %(email)s. Qeyd edək ki, bu ünvana göndərilən iddialar işlənməyəcək, bu yalnız suallar üçün nəzərdə tutulub. İddialarınızı təqdim etmək üçün aşağıdakı formdan istifadə edin. DMCA / Müəllif hüquqları iddia forması Anna’nın Arxivində nümunə qeyd Anna’nın Arxivi tərəfindən Torrents Anna’nın Arxivi Konteynerləri formatı Metadata idxalı üçün skriptlər Əgər bu datasetin <a %(a_archival)s>arxivləşdirilməsi</a> və ya <a %(a_llm)s>LLM təlimi</a> məqsədləri üçün güzgüsünü yaratmaqda maraqlısınızsa, bizimlə əlaqə saxlayın. Son yenilənmə: %(date)s Əsas %(source)s vebsayt Metadata sənədləşməsi (əksər sahələr) Anna’nın Arxivi tərəfindən güzgülənmiş fayllar: %(count)s (%(percent)s%%) Resurslar Ümumi fayllar: %(count)s Ümumi fayl ölçüsü: %(size)s Bu məlumat haqqında bloq yazımız <a %(duxiu_link)s>Duxiu</a> <a %(superstar_link)s>SuperStar Rəqəmsal Kitabxana Qrupu</a> tərəfindən yaradılmış nəhəng skan edilmiş kitablar bazasıdır. Əksəriyyəti akademik kitablardır və universitetlərə və kitabxanalara rəqəmsal olaraq təqdim etmək üçün skan edilmişdir. İngiliscə danışan auditoriyamız üçün <a %(princeton_link)s>Princeton</a> və <a %(uw_link)s>Vaşinqton Universiteti</a> yaxşı icmallar təqdim edir. Həmçinin daha çox məlumat verən əla bir məqalə də var: <a %(article_link)s>“Çin Kitablarının Rəqəmsallaşdırılması: SuperStar DuXiu Alim Axtarış Mühərriki üzrə Bir Vəziyyət Araşdırması”</a>. Duxiu kitabları uzun müddətdir ki, Çin internetində pirat edilmişdir. Adətən, onlar satıcılar tərəfindən bir dollardan az qiymətə satılır. Onlar adətən Google Drive-ın Çin ekvivalenti vasitəsilə paylanır, hansı ki, tez-tez daha çox saxlama yeri üçün hack edilir. Bəzi texniki detalları <a %(link1)s>burada</a> və <a %(link2)s>burada</a> tapa bilərsiniz. Kitablar yarı-ictimai şəkildə paylanmış olsa da, onları toplu şəkildə əldə etmək olduqca çətindir. Bu, bizim TODO siyahımızda yüksək prioritetdə idi və buna tam zamanlı iş üçün bir neçə ay ayırdıq. Lakin, 2023-cü ilin sonlarında inanılmaz, möhtəşəm və istedadlı bir könüllü bizimlə əlaqə saxladı və bu işin hamısını artıq böyük xərcə başa gəlmiş şəkildə etdiklərini bildirdi. Onlar bütün kolleksiyanı bizimlə paylaşdılar, yalnız uzunmüddətli qorunma zəmanəti istisna olmaqla heç bir şey gözləmədilər. Həqiqətən diqqətəlayiqdir. Könüllülərimizdən daha çox məlumat (xam qeydlər): <a %(a_href)s>Bloq yazımızdan</a> uyğunlaşdırılıb. DuXiu 读秀 %(count)s fayl %(count)s fayllar Bu dataset <a %(a_datasets_openlib)s>Open Library dataset</a> ilə sıx əlaqəlidir. O, IA-nın Nəzarət Edilən Rəqəmsal Kredit Kitabxanasından bütün metadata və faylların böyük bir hissəsinin skrapını ehtiva edir. Yeniləmələr <a %(a_aac)s>Anna’nın Arxivi Konteynerləri formatında</a> buraxılır. Bu qeydlər birbaşa Open Library datasetindən götürülür, lakin Open Library-də olmayan qeydləri də ehtiva edir. Bizdə həmçinin illər ərzində icma üzvləri tərəfindən skrap edilmiş bir sıra məlumat faylları var. Kolleksiya iki hissədən ibarətdir. Bütün məlumatları əldə etmək üçün hər iki hissəyə ehtiyacınız var (torrents səhifəsində üstündən xətt çəkilmiş köhnəlmiş torrentlər istisna olmaqla). Rəqəmsal Borc Kitabxanası ilk buraxılışımız, <a %(a_aac)s>Anna’nın Arxivi Konteynerləri (AAC) formatı</a> standartlaşdırılmadan əvvəl. Metadata (json və xml formatında), pdf-lər (acsm və lcpdf rəqəmsal borc sistemlərindən) və üz qabığı kiçik şəkillərini ehtiva edir. AAC istifadə edərək artımlı yeni buraxılışlar. Yalnız 2023-01-01 tarixindən sonra zaman möhürləri olan metadata ehtiva edir, çünki qalanı artıq “ia” tərəfindən əhatə olunub. Həmçinin bütün pdf faylları, bu dəfə acsm və “bookreader” (IA-nın veb oxuyucusu) borc sistemlərindən. Adın tam olaraq düzgün olmamasına baxmayaraq, biz hələ də bookreader fayllarını ia2_acsmpdf_files kolleksiyasına daxil edirik, çünki onlar qarşılıqlı olaraq istisna olunur. IA Nəzarətli Rəqəmsal Kreditləşmə 98%%+ fayllar axtarışa uyğundur. Missiyamız dünyadakı bütün kitabları (eləcə də məqalələr, jurnallar və s.) arxivləşdirmək və onları geniş şəkildə əlçatan etməkdir. İnanırıq ki, bütün kitablar geniş şəkildə güzgülənməlidir ki, redundans və dayanıqlıq təmin edilsin. Buna görə də müxtəlif mənbələrdən faylları bir araya gətiririk. Bəzi mənbələr tamamilə açıqdır və toplu şəkildə güzgülənə bilər (məsələn, Sci-Hub). Digərləri qapalı və qoruyucudur, buna görə də onların kitablarını “azad etmək” üçün onları qırmağa çalışırıq. Digərləri isə arada bir yerdədir. Bütün məlumatlarımız <a %(a_torrents)s>torrent</a> edilə bilər və bütün metadata-mız ElasticSearch və MariaDB verilənlər bazaları kimi <a %(a_anna_software)s>yaradıla</a> və ya <a %(a_elasticsearch)s>yüklənə</a> bilər. Xam məlumatlar <a %(a_dbrecord)s>bu</a> kimi JSON faylları vasitəsilə əl ilə araşdırıla bilər. Metaməlumat ISBN vebsaytı Son yeniləmə: %(isbn_country_date)s (%(link)s) Resurslar Beynəlxalq ISBN Agentliyi mütəmadi olaraq milli ISBN agentliklərinə təyin etdiyi diapazonları açıqlayır. Bu məlumatlardan istifadə edərək, ISBN-in hansı ölkəyə, regiona və ya dil qrupuna aid olduğunu müəyyən edə bilərik. Hazırda bu məlumatlardan dolayı yolla, <a %(a_isbnlib)s>isbnlib</a> Python kitabxanası vasitəsilə istifadə edirik. ISBN ölkə məlumatı Bu, 2022-ci ilin sentyabr ayında isbndb.com-a edilən çoxlu zənglərin dumpudur. Bütün ISBN diapazonlarını əhatə etməyə çalışdıq. Bunlar təxminən 30.9 milyon qeyddir. Onların vebsaytında əslində 32.6 milyon qeydləri olduğunu iddia edirlər, buna görə də biz nədənsə bəzi qeydləri qaçırmış ola bilərik, ya da <em>onlar</em> nəyisə səhv edə bilərlər. JSON cavabları demək olar ki, onların serverindən xam şəkildədir. Məlumat keyfiyyəti ilə bağlı bir problem, fərqli prefiks ilə başlayan ISBN-13 nömrələri üçün, hələ də “isbn” sahəsini daxil etmələridir ki, bu da sadəcə ISBN-13 nömrəsinin ilk 3 rəqəmini kəsib (və yoxlama rəqəmini yenidən hesablayıb). Bu açıq-aşkar səhvdir, amma onlar belə edirlər, buna görə də biz bunu dəyişmədik. Qarşılaşa biləcəyiniz başqa bir potensial problem, “isbn13” sahəsinin təkrarlanmasıdır, buna görə də onu verilənlər bazasında əsas açar kimi istifadə edə bilməzsiniz. “isbn13”+“isbn” sahələri birləşdirildikdə unikal görünür. Buraxılış 1 (2022-10-31) Bədii ədəbiyyat torrentləri arxada qalır (ID-lər ~4-6M torrent edilməyib, çünki onlar bizim Zlib torrentlərimizlə üst-üstə düşür). Komiks kitablarının buraxılışı haqqında bloq yazımız Anna’nın Arxivində komiks torrentləri Müxtəlif Library Genesis forklarının arxa planı üçün <a %(a_libgen_rs)s>Libgen.rs</a> səhifəsinə baxın. Libgen.li, Libgen.rs ilə eyni məzmun və metaməlumatların çoxunu ehtiva edir, lakin bunun üzərinə bəzi kolleksiyalar, yəni komikslər, jurnallar və standart sənədlər əlavə olunub. O, həmçinin <a %(a_scihub)s>Sci-Hub</a>-ı metaməlumatlarına və axtarış mühərrikinə inteqrasiya edib, bu da bizim verilənlər bazamız üçün istifadə etdiyimizdir. Bu kitabxananın metaməlumatları <a %(a_libgen_li)s>libgen.li-də</a> sərbəst şəkildə mövcuddur. Lakin, bu server yavaşdır və qırılmış bağlantıların bərpasını dəstəkləmir. Eyni fayllar <a %(a_ftp)s>FTP serverində</a> də mövcuddur, bu daha yaxşı işləyir. Qeyri-bədii ədəbiyyat da fərqlənmiş kimi görünür, lakin yeni torrentlər olmadan. Görünür, bu, 2022-ci ilin əvvəllərindən bəri baş verib, lakin biz bunu təsdiqləməmişik. Libgen.li administratoruna görə, “fiction_rus” (Rus bədii ədəbiyyatı) kolleksiyası <a %(a_booktracker)s>booktracker.org</a> tərəfindən müntəzəm olaraq buraxılan torrentlərlə əhatə olunmalıdır, xüsusilə <a %(a_flibusta)s>flibusta</a> və <a %(a_librusec)s>lib.rus.ec</a> torrentləri (biz bunları <a %(a_torrents)s>burada</a> güzgüləyirik, lakin hələ hansı torrentlərin hansı fayllara uyğun olduğunu müəyyən etməmişik). Bədii ədəbiyyat kolleksiyasının öz torrentləri var (Libgen.rs-dən fərqli olaraq) %(start)s-dən başlayaraq. Torrentlər olmayan müəyyən aralıqlar (məsələn, bədii ədəbiyyat aralıqları f_3463000-dən f_4260000-ə qədər) ehtimal ki, Z-Kitabxana (və ya digər təkrarlanan) fayllardır, lakin biz bu aralıqlarda lgli-unikal fayllar üçün bəzi deduplikasiya və torrentlər etmək istəyə bilərik. Bütün kolleksiyalar üçün statistika <a %(a_href)s>libgen veb saytında</a> tapıla bilər. Əlavə məzmunun əksəriyyəti üçün torrentlər mövcuddur, xüsusilə komiks, jurnal və standart sənədlər üçün torrentlər Anna Arxivi ilə əməkdaşlıqda buraxılmışdır. “libgen.is”ə aid torrent fayllarının <a %(a_libgen)s>Libgen.rs</a> güzgüləri olduğunu qeyd edin (“.is” Libgen.rs tərəfindən istifadə edilən fərqli bir domendir). Metadatanı istifadə etmək üçün faydalı bir resurs <a %(a_href)s>bu səhifə</a>dir. %(icon)s Onların “fiction_rus” kolleksiyası (Rus bədii ədəbiyyatı) üçün xüsusi torrentlər yoxdur, lakin başqalarının torrentləri ilə əhatə olunur və biz bir <a %(fiction_rus)s>güzgü</a> saxlayırıq. Anna Arxivində Rus bədii ədəbiyyat torrentləri Anna’nın Arxivində bədii ədəbiyyat torrentləri Müzakirə forumu Metaməlumat FTP vasitəsilə metaməlumat Anna’nın Arxivində jurnal torrentləri Metaməlumat sahəsi haqqında məlumat Digər torrentlərin güzgüsü (və unikal bədii ədəbiyyat və komiks torrentləri) Anna Arxivində standart sənəd torrentləri Libgen.li Anna’nın Arxivindən torrentlər (kitab üzlükləri) Library Genesis artıq məlumatlarını toplu şəkildə torrentlər vasitəsilə səxavətlə əlçatan etməsi ilə tanınır. Bizim Libgen kolleksiyamız, onlarla əməkdaşlıqda, onların birbaşa buraxmadığı əlavə məlumatlardan ibarətdir. Library Genesis ilə əməkdaşlıq edən hər kəsə çox təşəkkür edirik! Kitab üzlüklərinin buraxılışı haqqında bloqumuz Bu səhifə “.rs” versiyası haqqındadır. O, həm metaməlumatlarını, həm də kitab kataloqunun tam məzmununu müntəzəm olaraq dərc etməsi ilə tanınır. Onun kitab kolleksiyası bədii ədəbiyyat və elmi ədəbiyyat hissələrinə bölünür. Metaməlumatı istifadə etmək üçün faydalı bir resurs <a %(a_metadata)s>bu səhifə</a>dir (IP aralıqlarını bloklayır, VPN tələb oluna bilər). 2024-03 tarixindən etibarən yeni torrentlər <a %(a_href)s>bu forum mövzusunda</a> yerləşdirilir (IP diapazonlarını bloklayır, VPN tələb oluna bilər). Anna’nın Arxivində Bədii torrentlər Libgen.rs Bədii torrentlər Libgen.rs Müzakirə forumu Libgen.rs Metadata Libgen.rs metadata sahə məlumatları Libgen.rs Qeyri-bədii torrentlər Anna’nın Arxivində Qeyri-bədii torrentlər Bədii kitab üçün %(example)s. Bu <a %(blog_post)s>ilk buraxılış</a> olduqca kiçikdir: Libgen.rs forkundan təxminən 300GB kitab üzlükləri, həm bədii, həm də qeyri-bədii. Onlar libgen.rs-də göründüyü kimi təşkil olunub, məsələn: Qeyri-bədii kitab üçün %(example)s. Z-Kitabxana kolleksiyasında olduğu kimi, biz onları böyük .tar faylına yığdıq, faylları birbaşa xidmət etmək istəyirsinizsə, <a %(a_ratarmount)s>ratarmount</a> istifadə edərək quraşdıra bilərsiniz. Buraxılış 1 (%(date)s) Fərqli Library Genesis (və ya “Libgen”) forklarının qısa hekayəsi budur ki, zaman keçdikcə, Library Genesis ilə məşğul olan müxtəlif insanlar arasında anlaşılmazlıq yaranıb və onlar ayrı yollarla gediblər. Bu <a %(a_mhut)s>forum yazısına</a> görə, Libgen.li əvvəlcə “http://free-books.dontexist.com” ünvanında yerləşirdi. “.fun” versiyası orijinal qurucu tərəfindən yaradılıb. Yeni, daha paylanmış bir versiya lehinə yenidən işlənir. <a %(a_li)s>“.li” versiyası</a> böyük bir komiks kolleksiyasına, həmçinin toplu yükləmə üçün (hələ) mövcud olmayan digər məzmunlara malikdir. Ayrı bir bədii ədəbiyyat kitabları torrent kolleksiyasına malikdir və onun bazasında <a %(a_scihub)s>Sci-Hub</a>un metaməlumatları var. “.rs” versiyası çox oxşar məlumatlara malikdir və kolleksiyasını ən çox toplu torrentlərdə buraxır. Təxminən “bədii ədəbiyyat” və “elmi ədəbiyyat” bölmələrinə bölünür. Əslində “http://gen.lib.rus.ec” ünvanında yerləşirdi. <a %(a_zlib)s>Z-Library</a> bir mənada Library Genesis-in bir forkudur, baxmayaraq ki, onlar layihələri üçün fərqli bir ad istifadə ediblər. Libgen.rs Kolleksiyamızı yalnız metaməlumat mənbələri ilə də zənginləşdiririk, hansı ki, fayllarla uyğunlaşdıra bilərik, məsələn, ISBN nömrələri və ya digər sahələrdən istifadə edərək. Aşağıda bu mənbələrin ümumi görünüşü verilmişdir. Yenə də, bu mənbələrin bəziləri tamamilə açıqdır, digərləri üçün isə onları skreyp etməliyik. Qeyd edək ki, metaməlumat axtarışında biz orijinal qeydləri göstəririk. Qeydlərin birləşdirilməsini etmirik. Yalnız metaməlumat mənbələri Open Library, Internet Archive tərəfindən hər kitabı kataloqlaşdırmaq üçün açıq mənbəli bir layihədir. Dünyanın ən böyük kitab skanlama əməliyyatlarından birinə malikdir və rəqəmsal borc üçün çoxlu kitablar mövcuddur. Onun kitab metadata kataloqu yükləmək üçün sərbəstdir və Anna’nın Arxivində mövcuddur (hazırda axtarışda deyil, yalnız Open Library ID-si ilə axtarış etdiyiniz halda). Open Library Təkrarlananları istisna etməklə Son yenilənmə Faylların sayının faizləri %% AA tərəfindən güzgülənib / torrentlər mövcuddur Ölçü Mənbə Aşağıda Anna’nın Arxivindəki faylların mənbələrinin qısa icmalı verilmişdir. Kölgə kitabxanaları tez-tez bir-birindən məlumatları sinxronlaşdırdığı üçün kitabxanalar arasında əhəmiyyətli dərəcədə üst-üstə düşmə var. Buna görə də rəqəmlər cəmi təşkil etmir. “Anna’nın Arxivi tərəfindən güzgülənmiş və toxumlanmış” faizi, özümüzün güzgülədiyimiz faylların sayını göstərir. Bu faylları toplu şəkildə torrentlər vasitəsilə toxumlayırıq və tərəfdaş veb-saytlar vasitəsilə birbaşa yükləmək üçün əlçatan edirik. Ümumi baxış Cəmi Anna’nın Arxivi üzərindəki torrentlər Sci-Hub haqqında məlumat üçün onun <a %(a_scihub)s>rəsmi vebsaytına</a>, <a %(a_wikipedia)s>Vikipediya səhifəsinə</a> və bu <a %(a_radiolab)s>podkast müsahibəsinə</a> müraciət edin. Qeyd edək ki, Sci-Hub <a %(a_reddit)s>2021-ci ildən bəri dondurulub</a>. Əvvəllər də dondurulmuşdu, lakin 2021-ci ildə bir neçə milyon məqalə əlavə edildi. Hələ də bəzi məhdud sayda məqalələr Libgen “scimag” kolleksiyalarına əlavə olunur, lakin yeni toplu torrentlər üçün kifayət qədər deyil. Biz Sci-Hub metadatasını <a %(a_libgen_li)s>Libgen.li</a> tərəfindən təqdim edilən “scimag” kolleksiyasında istifadə edirik. Biz həmçinin <a %(a_dois)s>dois-2022-02-12.7z</a> datasetindən istifadə edirik. Qeyd edək ki, “smarch” torrentləri <a %(a_smarch)s>köhnəlmişdir</a> və buna görə də torrentlər siyahımıza daxil edilmir. Libgen.li üzərindəki torrentlər Libgen.rs üzərindəki torrentlər Metadata və torrentlər Reddit üzərində yeniliklər Podkast müsahibəsi Vikipediya səhifəsi Sci-Hub Sci-Hub: 2021-ci ildən bəri dondurulub; əksəriyyəti torrentlər vasitəsilə mövcuddur Libgen.li: o vaxtdan bəri kiçik əlavələr</div> Bəzi mənbə kitabxanaları məlumatlarını torrentlər vasitəsilə kütləvi şəkildə paylaşmağı təşviq edir, digərləri isə kolleksiyalarını asanlıqla paylaşmır. Sonuncu halda, Anna’nın Arxivi onların kolleksiyalarını skreyp etməyə və əlçatan etməyə çalışır (baxın <a %(a_torrents)s>Torrentlər</a> səhifəmizə). Arada vəziyyətlər də var, məsələn, mənbə kitabxanaları paylaşmağa hazırdır, lakin bunu etmək üçün resursları yoxdur. Bu hallarda da kömək etməyə çalışırıq. Aşağıda müxtəlif mənbə kitabxanaları ilə necə əlaqə qurduğumuzun ümumi görünüşü verilmişdir. Mənbə kitabxanalar %(icon)s Çin internetində müxtəlif fayl verilənlər bazaları yayılmışdır; lakin tez-tez ödənişli verilənlər bazalarıdır %(icon)s Əksər fayllara yalnız premium BaiduYun hesabları ilə daxil olmaq mümkündür; yavaş yükləmə sürətləri. %(icon)s Annanın Arxivi <a %(duxiu)s>DuXiu faylları</a> kolleksiyasını idarə edir %(icon)s Çin internetində müxtəlif metadata verilənlər bazaları yayılmışdır; lakin tez-tez ödənişli verilənlər bazalarıdır %(icon)s Onların bütün kolleksiyası üçün asanlıqla əldə edilə bilən metadata dump-ları mövcud deyil. %(icon)s Anna’nın Arxivi <a %(duxiu)s>DuXiu metadata</a> kolleksiyasını idarə edir Fayllar %(icon)s Fayllar yalnız məhdud əsasda borc üçün mövcuddur, müxtəlif giriş məhdudiyyətləri ilə %(icon)s Anna’nın Arxivi <a %(ia)s>IA faylları</a> kolleksiyasını idarə edir %(icon)s <a %(openlib)s>Open Library verilənlər bazası boşaltmaları</a> vasitəsilə bəzi metadata mövcuddur, lakin bunlar IA kolleksiyasının hamısını əhatə etmir %(icon)s Onların bütün kolleksiyası üçün asanlıqla əldə edilə bilən metadata dump-ları mövcud deyil %(icon)s Annanın Arxivi <a %(ia)s>IA metadata</a> kolleksiyasını idarə edir Son yenilənmə %(icon)s Anna Arxivi və Libgen.li <a %(comics)s>komiks kitabları</a>, <a %(magazines)s>jurnallar</a>, <a %(standarts)s>standart sənədlər</a> və <a %(fiction)s>bədii ədəbiyyat (Libgen.rs-dən ayrılmış)</a> kolleksiyalarını birgə idarə edirlər. %(icon)s Bədii olmayan torrent-lər Libgen.rs ilə paylaşılır (və <a %(libgenli)s>burada</a> əks olunur). %(icon)s Rüblük <a %(dbdumps)s>HTTP verilənlər bazası dump-ları</a> %(icon)s <a %(nonfiction)s>Qeyri-Bədii</a> və <a %(fiction)s>Bədii</a> üçün Avtomatlaşdırılmış torrentlər %(icon)s Anna Arxivi <a %(covers)s>kitab üz qabığı torrentləri</a> kolleksiyasını idarə edir %(icon)s Gündəlik <a %(dbdumps)s>HTTP verilənlər bazası dump-ları</a> Metaməlumatlar %(icon)s Aylıq <a %(dbdumps)s>baza dump-ları</a> %(icon)s Data torrent-ləri <a %(scihub1)s>burada</a>, <a %(scihub2)s>burada</a> və <a %(libgenli)s>burada</a> mövcuddur %(icon)s Bəzi yeni fayllar Libgen-in “scimag”ına <a %(libgenrs)s>əlavə edilir</a>, lakin yeni torrent-lərə ehtiyac yaratmaq üçün kifayət qədər deyil %(icon)s Sci-Hub 2021-ci ildən bəri yeni faylları dondurub. %(icon)s Metadata dump-ları <a %(scihub1)s>burada</a> və <a %(scihub2)s>burada</a> mövcuddur, həmçinin <a %(libgenli)s>Libgen.li bazasının</a> bir hissəsi olaraq (bizim istifadə etdiyimiz) Mənbə %(icon)s Müxtəlif kiçik və ya tək-tək mənbələr. İnsanları əvvəlcə digər kölgə kitabxanalara yükləməyə təşviq edirik, lakin bəzən insanların başqalarının çeşidləməsi üçün çox böyük, lakin öz kateqoriyasına layiq olmayan kolleksiyaları olur. %(icon)s Birbaşa toplu şəkildə mövcud deyil, kazıma qarşı qorunur %(icon)s Anna’nın Arxivi <a %(worldcat)s>OCLC (WorldCat) metadata</a> kolleksiyasını idarə edir %(icon)s Anna’nın Arxivi və Z-Kitabxanası birgə <a %(metadata)s>Z-Kitabxana metadata</a> və <a %(files)s>Z-Kitabxana faylları</a> kolleksiyasını idarə edir Datasets Yuxarıda qeyd olunan bütün mənbələri birləşdirərək bu vebsayta xidmət etmək üçün birləşmiş verilənlər bazası yaradırıq. Bu birləşmiş verilənlər bazası birbaşa əlçatan deyil, lakin Anna’nın Arxivi tamamilə açıq mənbə olduğundan, onu asanlıqla <a %(a_generated)s>yaratmaq</a> və ya <a %(a_downloaded)s>yükləmək</a> mümkündür, ElasticSearch və MariaDB verilənlər bazaları kimi. Həmin səhifədəki skriptlər yuxarıda qeyd olunan mənbələrdən bütün lazımi metaməlumatları avtomatik olaraq yükləyəcək. Əgər bu skriptləri yerli olaraq işə salmadan əvvəl məlumatlarımızı araşdırmaq istəyirsinizsə, JSON fayllarımıza baxa bilərsiniz, hansı ki, digər JSON fayllarına keçid verir. <a %(a_json)s>Bu fayl</a> yaxşı bir başlanğıc nöqtəsidir. Birləşmiş verilənlər bazası Anna-nın Arxivi tərəfindən torrentlər göz at axtar Müxtəlif kiçik və ya bir dəfəlik mənbələr. İnsanları əvvəlcə digər kölgə kitabxanalarına yükləməyə təşviq edirik, lakin bəzən insanların başqalarının çeşidləməsi üçün çox böyük, lakin öz kateqoriyasına layiq olmayan kolleksiyaları olur. Ümumi baxış <a %(a1)s>datasets səhifəsi</a>. <a %(a_href)s>aaaaarg.fail</a> saytından. Olduqca tam görünür. Könüllümüz “cgiym”dən. <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentindən. Mövcud məqalə kolleksiyaları ilə kifayət qədər yüksək üst-üstə düşür, lakin çox az MD5 uyğunluğu var, buna görə də onu tamamilə saxlamağa qərar verdik. <iRead eBooks</q> (fonetik olaraq <q>ai rit i-books</q>; airitibooks.com) saytının könüllü <q>j</q> tərəfindən skrepi. <a %(a1)s><q>Digər metadata skrepləri</q></a>ndəki <q>airitibooks</q> metadata ilə uyğun gəlir. <a %(a1)s><q>Bibliotheca Alexandrina</q></a> kolleksiyasından. Qismən orijinal mənbədən, qismən the-eye.eu-dan, qismən isə digər güzgülərdən. Şəxsi kitab torrent vebsaytından, <a %(a_href)s>Bibliotik</a> (tez-tez “Bib” adlandırılır), kitablar adla (A.torrent, B.torrent) torrentlərə yığılmış və the-eye.eu vasitəsilə paylanmışdır. Könüllümüz “bpb9v”dən. <a %(a_href)s>CADAL</a> haqqında daha çox məlumat üçün <a %(a_duxiu)s>DuXiu dataset səhifəmizdəki</a> qeydlərə baxın. Könüllümüz “bpb9v”dən daha çox, əsasən DuXiu faylları, həmçinin “WenQu” və “SuperStar_Journals” qovluğu (SuperStar DuXiu-nun arxasındakı şirkətdir). Könüllümüz “cgiym”dən, müxtəlif mənbələrdən (alt qovluqlar kimi təmsil olunur) Çin mətnləri, o cümlədən <a %(a_href)s>China Machine Press</a>dən (böyük Çin nəşriyyatı). Könüllümüz “cgiym” tərəfindən təqdim olunan qeyri-Çin kolleksiyaları (alt kataloqlar kimi təmsil olunur). Çin memarlığı haqqında kitabların könüllü <q>cm</q> tərəfindən skrepi: <q>Bu kitabları nəşriyyat evindəki şəbəkə zəifliyindən istifadə edərək əldə etdim, lakin bu boşluq artıq bağlanıb</q>. <a %(a1)s><q>Digər metadata skrepləri</q></a>ndəki <q>chinese_architecture</q> metadata ilə uyğun gəlir. Akademik nəşriyyat evi <a %(a_href)s>De Gruyter</a>dən kitablar, bir neçə böyük torrentdən toplanmışdır. <a %(a_href)s>docer.pl</a>nin, kitablar və digər yazılı əsərlərə yönəlmiş Polşa fayl paylaşma veb saytının kazıması. 2023-cü ilin sonlarında könüllü “p” tərəfindən kazınmışdır. Orijinal veb saytından yaxşı metadata əldə edə bilmədik (hətta fayl uzantıları belə), lakin kitab kimi faylları süzdük və tez-tez metadatanı faylların özlərindən çıxara bildik. DuXiu epubları, birbaşa DuXiu-dan, könüllü “w” tərəfindən toplanmışdır. Yalnız son DuXiu kitabları birbaşa elektron kitablar vasitəsilə mövcuddur, buna görə də bunların əksəriyyəti son olmalıdır. Könüllü “m”dən DuXiu proprietary PDG formatında olmayan qalan DuXiu faylları (əsas <a %(a_href)s>DuXiu dataset</a>). Təəssüf ki, bu mənbələri fayl yolunda saxlamadan bir çox orijinal mənbədən toplanmışdır. <span></span> <span></span> <span></span> Erotik kitabların könüllü <q>do no harm</q> tərəfindən skrepi. <a %(a1)s><q>Digər metadata skrepləri</q></a>ndəki <q>hentai</q> metadata ilə uyğun gəlir. <span></span> <span></span> Könüllü “t” tərəfindən Yapon Manga nəşriyyatından toplanmış kolleksiya. <a %(a_href)s>Longquan-ın seçilmiş məhkəmə arxivləri</a>, könüllü “c” tərəfindən təqdim olunmuşdur. <a %(a_href)s>magzdb.org</a> saytının toplanması, Library Genesis-in müttəfiqi (libgen.rs ana səhifəsində əlaqələndirilmişdir), lakin fayllarını birbaşa təqdim etmək istəməyən. 2023-cü ilin sonlarında könüllü “p” tərəfindən əldə edilmişdir. <span></span> Çox kiçik alt kolleksiya kimi özləri üçün çox kiçik olan müxtəlif kiçik yükləmələr, lakin kataloqlar kimi təmsil olunur. AvaxHome-dan elektron kitablar, Rusiya fayl paylaşma saytı. Qəzet və jurnalların arxivi. <a %(a1)s><q>Digər metadata skrepləri</q></a>ndəki <q>newsarch_magz</q> metadata ilə uyğun gəlir. <a %(a1)s>Fəlsəfə Sənədləşmə Mərkəzi</a>nin skrepi. Könüllü “o” tərəfindən toplanmış, orijinal buraxılış (“scene”) veb saytlarından birbaşa toplanmış Polşa kitabları kolleksiyası. Könüllülər “cgiym” və “woz9ts” tərəfindən <a %(a_href)s>shuge.org</a>nin birləşdirilmiş kolleksiyaları. <span></span> <a %(a_href)s>“Trantor İmperial Kitabxanası”</a> (uydurma kitabxananın adını daşıyan), 2022-ci ildə könüllü “t” tərəfindən toplanmışdır. <span></span> <span></span> <span></span> Könüllü “woz9ts”dən alt-alt kolleksiyalar (qovluqlar kimi təmsil olunur): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (Tayvanda <a %(a_sikuquanshu)s>Dizhi(迪志)</a> tərəfindən), mebook (mebook.cc, 我的小书屋, mənim kiçik kitab otağım — woz9ts: “Bu sayt əsasən yüksək keyfiyyətli elektron kitab fayllarını paylaşmağa yönəlib, bəziləri sayt sahibi tərəfindən tərtib edilmişdir. Sahibi 2019-cu ildə <a %(a_arrested)s>həbs edilmişdir</a> və kimsə onun paylaşdığı faylların kolleksiyasını yaratmışdır.”). Könüllü “woz9ts” tərəfindən təqdim olunan qalan DuXiu faylları, DuXiu-ya məxsus PDG formatında olmayan (hələ PDF-ə çevrilməlidir). “Yükləmə” kolleksiyası daha kiçik alt kolleksiyalara bölünür, bunlar AACID-lərdə və torrent adlarında göstərilir. Bütün alt kolleksiyalar əvvəlcə əsas kolleksiya ilə müqayisə edilib, lakin metadata “upload_records” JSON faylları hələ də orijinal fayllara çoxlu istinadlar ehtiva edir. Əksər alt kolleksiyalardan qeyri-kitab faylları da çıxarılıb və adətən “upload_records” JSON-da <em>qeyd olunmur</em>. Alt kolleksiyalar bunlardır: Qeydlər Alt kolleksiya Bir çox alt kolleksiyalar özləri də alt-alt kolleksiyalardan ibarətdir (məsələn, müxtəlif orijinal mənbələrdən), bunlar “filepath” sahələrində qovluqlar kimi təmsil olunur. Anna Arxivinə yükləmələr Bu məlumat haqqında blog yazımız <a %(a_worldcat)s>WorldCat</a> qeyri-kommersiya <a %(a_oclc)s>OCLC</a> tərəfindən idarə olunan xüsusi bir verilənlər bazasıdır və dünyanın hər yerindən kitabxana metadatası qeydlərini toplayır. Bu, yəqin ki, dünyanın ən böyük kitabxana metadata kolleksiyasıdır. 2023-cü ilin oktyabr ayında biz OCLC (WorldCat) verilənlər bazasının əhatəli bir skreypini <a %(a_scrape)s>buraxdıq</a>, <a %(a_aac)s>Anna’nın Arxivi Konteynerləri formatında</a>. Oktyabr 2023, ilkin buraxılış: OCLC (WorldCat) Anna’nın Arxivi tərəfindən torrentlər Anna-nın Arxivində nümunə qeyd (orijinal kolleksiya) Anna-nın Arxivində nümunə qeyd (“zlib3” kolleksiya) Anna-nın Arxivi tərəfindən torrentlər (metadata + məzmun) Bloq yazısı haqqında Buraxılış 1 Bloq yazısı haqqında Buraxılış 2 2022-ci ilin sonlarında Z-Library-in iddia edilən qurucuları həbs olundu və domenlər ABŞ hakimiyyət orqanları tərəfindən ələ keçirildi. O vaxtdan bəri veb sayt yavaş-yavaş yenidən onlayn olmağa başlayıb. Hazırda kimin idarə etdiyi məlum deyil. Fevral 2023-ə yeniləmə. Z-Kitabxana <a %(a_href)s>Library Genesis</a> icmasında köklənib və əvvəlcə onların məlumatları ilə işə salınıb. O vaxtdan bəri, xeyli peşəkarlaşmış və daha müasir interfeysə malikdir. Buna görə də, həm veb saytlarını təkmilləşdirmək üçün maliyyə yardımları, həm də yeni kitabların ianələri şəklində daha çox ianə əldə edə bilirlər. Library Genesis-ə əlavə olaraq böyük bir kolleksiya toplayıblar. Kolleksiya üç hissədən ibarətdir. İlk iki hissə üçün orijinal təsvir səhifələri aşağıda saxlanılır. Bütün məlumatları əldə etmək üçün hər üç hissəyə ehtiyacınız var (torrentlər səhifəsində üstündən xətt çəkilmiş torrentlər istisna olmaqla). %(title)s: ilk buraxılışımız. Bu, o zaman “Pirate Library Mirror” (“pilimi”) adlandırılanın ilk buraxılışı idi. %(title)s: bu dəfə bütün fayllar .tar fayllarına bükülmüş ikinci buraxılış. %(title)s: <a %(a_href)s>Anna-nın Arxivi Konteynerləri (AAC) formatı</a> istifadə edərək yeni artımlı buraxılışlar, indi Z-Library komandası ilə əməkdaşlıqda buraxılır. İlkin güzgü 2021 və 2022-ci illər ərzində diqqətlə əldə edilmişdir. Bu nöqtədə bir qədər köhnəlmişdir: kolleksiyanın vəziyyətini 2021-ci ilin iyun ayında əks etdirir. Gələcəkdə bunu yeniləyəcəyik. Hazırda biz bu ilk buraxılışı təqdim etməyə diqqət yetiririk. Library Genesis artıq ictimai torrentlər vasitəsilə qorunduğu və Z-Library-ə daxil edildiyi üçün biz 2022-ci ilin iyun ayında Library Genesis-ə qarşı əsas deduplikasiya etdik. Bunun üçün MD5 hash-lərindən istifadə etdik. Kitabxanada çoxlu təkrarlanan məzmunun olması ehtimalı var, məsələn, eyni kitabın müxtəlif fayl formatları. Bunu dəqiq aşkar etmək çətindir, ona görə də biz bunu etmirik. Deduplikasiyadan sonra bizdə 7TB-dan bir qədər az olan 2 milyondan çox fayl qalıb. Kolleksiya iki hissədən ibarətdir: metadatanın MySQL “.sql.gz” dump-u və hər biri təxminən 50-100GB olan 72 torrent faylı. Metadatalar Z-Library vebsaytı tərəfindən bildirilən məlumatları (başlıq, müəllif, təsvir, fayl növü) və müşahidə etdiyimiz faktiki fayl ölçüsü və md5sum-u ehtiva edir, çünki bəzən bunlar uyğun gəlmir. Z-Library-nin özündə yanlış metadata olan fayl diapazonları var kimi görünür. Biz də bəzi təcrid olunmuş hallarda səhv yüklənmiş fayllara malik ola bilərik, bunu gələcəkdə aşkar etməyə və düzəltməyə çalışacağıq. Böyük torrent faylları faktiki kitab məlumatlarını, fayl adı kimi Z-Library ID ilə ehtiva edir. Fayl uzantıları metadata dump-u istifadə edərək bərpa edilə bilər. Kolleksiya qeyri-bədii və bədii məzmunun qarışığıdır (Library Genesis-də olduğu kimi ayrılmamışdır). Keyfiyyət də geniş şəkildə dəyişir. Bu ilk buraxılış indi tamamilə mövcuddur. Qeyd edək ki, torrent faylları yalnız Tor güzgümüz vasitəsilə mövcuddur. Buraxılış 1 (%(date)s) Bu, tək bir əlavə torrent faylıdır. O, heç bir yeni məlumat ehtiva etmir, lakin içində hesablamaq üçün bir az vaxt tələb edə biləcək bəzi məlumatlar var. Bu, onu əlverişli edir, çünki bu torrentin yüklənməsi tez-tez sıfırdan hesablamaqdan daha sürətlidir. Xüsusilə, tar faylları üçün SQLite indekslərini ehtiva edir, <a %(a_href)s>ratarmount</a> ilə istifadə üçün. Buraxılış 2 əlavəsi (%(date)s) Son güzgümüzlə avqust 2022 arasında Z-Kitabxanaya əlavə olunan bütün kitabları əldə etdik. Həmçinin, ilk dəfə əldən verdiyimiz bəzi kitabları geri çəkdik. Ümumilikdə, bu yeni kolleksiya təxminən 24TB-dır. Yenə də, bu kolleksiya Library Genesis-ə qarşı deduplikasiya edilib, çünki həmin kolleksiya üçün artıq torrentlər mövcuddur. Məlumatlar ilk buraxılışa bənzər şəkildə təşkil olunub. Metadatanın MySQL “.sql.gz” dump-u var, bu da ilk buraxılışdan olan bütün metadatanı əhatə edir və beləliklə onu əvəz edir. Biz həmçinin bəzi yeni sütunlar əlavə etdik: Bunu keçən dəfə qeyd etdik, amma aydınlaşdırmaq üçün: “filename” və “md5” faylın faktiki xüsusiyyətləridir, halbuki “filename_reported” və “md5_reported” Z-Kitabxanadan əldə etdiklərimizdir. Bəzən bu ikisi bir-biri ilə razılaşmır, buna görə də hər ikisini daxil etdik. Bu buraxılış üçün biz köhnə MySQL versiyaları ilə uyğun olmalı olan “utf8mb4_unicode_ci” sıralamasını dəyişdirdik. Məlumat faylları əvvəlki kimi oxşardır, lakin onlar daha böyükdür. Biz sadəcə olaraq çoxlu kiçik torrent faylları yaratmaqla məşğul ola bilmədik. “pilimi-zlib2-0-14679999-extra.torrent” əvvəlki buraxılışda qaçırdığımız bütün faylları ehtiva edir, digər torrentlər isə hamısı yeni ID diapazonlarıdır.  <strong>Yeniləmə %(date)s:</strong> Çoxlu torrentlərimizi çox böyük etdik, bu da torrent müştərilərinin çətinlik çəkməsinə səbəb oldu. Onları sildik və yeni torrentlər buraxdıq. <strong>Yeniləmə %(date)s:</strong> Hələ də çoxlu fayl var idi, buna görə də onları tar fayllarına bükdük və yenidən yeni torrentlər buraxdıq. %(key)s: bu faylın artıq Library Genesis-də, ya qeyri-bədii, ya da bədii kolleksiyada olub-olmadığı (md5 ilə uyğunlaşdırılıb). %(key)s: bu faylın hansı torrentdə olduğu. %(key)s: kitabı yükləyə bilmədiyimiz zaman təyin edilir. Buraxılış 2 (%(date)s) Zlib buraxılışları (orijinal təsvir səhifələri) Tor domeni Əsas veb sayt Z-Kitabxana scrape Z-Library-dəki “Çin” kolleksiyası bizim DuXiu kolleksiyamızla eyni görünür, lakin fərqli MD5-lərlə. Təkrarlanmamaq üçün bu faylları torrentlərdən çıxarırıq, lakin hələ də axtarış indeksimizdə göstəririk. Metadata Siz %(profile_link)s istifadəçisi tərəfindən yönləndirildiyiniz üçün %(percentage)s%% bonus sürətli yükləmələr əldə edirsiniz. Bu, bütün üzvlük müddətinə aiddir. Bağış et Qoşulun Seçilmiş %(percentage)s%%-ə qədər endirimlər Alipay beynəlxalq kredit/debet kartlarını dəstəkləyir. Daha çox məlumat üçün <a %(a_alipay)s>bu bələdçiyə</a> baxın. Bizə Amazon.com hədiyyə kartlarını kredit/debit kartınızla göndərin. Kredit/debet kartları ilə kripto ala bilərsiniz. WeChat (Weixin Pay) beynəlxalq kredit/debet kartlarını dəstəkləyir. WeChat tətbiqində “Mən => Xidmətlər => Pulqabı => Kart əlavə et” bölməsinə keçin. Əgər bu seçimi görmürsünüzsə, “Mən => Parametrlər => Ümumi => Alətlər => Weixin Pay => Aktiv et” seçimini istifadə edərək aktivləşdirin. (Coinbase-dən Ethereum göndərərkən istifadə edin) kopyalandı! kopyala (ən aşağı minimum məbləğ) (xəbərdarlıq: yüksək minimum məbləğ) -%(percentage)s%% 12 ay 1 ay 24 ay 3 ay 48 ay 6 ay 96 ay Abunə olmaq istədiyiniz müddəti seçin. <div %(div_monthly_cost)s></div><div %(div_after)s><span %(span_discount)s></span> endirimlərdən sonra</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 ay üçün 1 ay üçün 24 ay üçün 3 ay üçün 48 ay üçün 6 ay üçün 96 ay üçün %(monthly_cost)s / ay bizimlə əlaqə saxlayın Birbaşa <strong>SFTP</strong> serverləri Yeni kolleksiyalar üçün müəssisə səviyyəsində ianə və ya mübadilə (məsələn, yeni skanlar, OCR edilmiş Datasets). Ekspert Girişi <strong>Limitsiz</strong> yüksək sürətli giriş <div %(div_question)s>Üzvlüyümü yüksəldə bilərəm və ya bir neçə üzvlük əldə edə bilərəm?</div> <div %(div_question)s>Üzv olmadan ianə edə bilərəmmi?</div> Əlbəttə. Biz bu Monero (XMR) ünvanında hər hansı məbləğdə ianələri qəbul edirik: %(address)s. <div %(div_question)s>Aylıq aralıqlar nə deməkdir?</div> Bütün endirimləri tətbiq edərək, məsələn, bir aydan uzun bir dövr seçərək, aralığın aşağı tərəfinə çata bilərsiniz. <div %(div_question)s>Üzvlüklər avtomatik olaraq yenilənir?</div> Üzvlüklər <strong>avtomatik olaraq</strong> yenilənmir. İstədiyiniz qədər və ya qısa müddətə qoşula bilərsiniz. <div %(div_question)s>İanələri nəyə xərcləyirsiniz?</div> 100%% dünya bilik və mədəniyyətini qorumağa və əlçatan etməyə gedir. Hal-hazırda əsasən serverlər, saxlama və bant genişliyinə xərcləyirik. Heç bir pul şəxsi olaraq komanda üzvlərinə getmir. <div %(div_question)s>Böyük ianə edə bilərəmmi?</div> Bu möhtəşəm olardı! Bir neçə min dolları keçən ianələr üçün, zəhmət olmasa birbaşa bizimlə əlaqə saxlayın %(email)s. <div %(div_question)s>Başqa ödəniş üsullarınız varmı?</div> Hal-hazırda yoxdur. Çox insan belə arxivlərin mövcud olmasını istəmir, buna görə də diqqətli olmalıyıq. Əgər bizə digər (daha rahat) ödəniş üsullarını təhlükəsiz şəkildə qurmağa kömək edə bilsəniz, %(email)s ünvanında bizimlə əlaqə saxlayın. İanə FAQ Sizdə <a %(a_donation)s>mövcud ianə</a> davam edir. Zəhmət olmasa yeni ianə etməzdən əvvəl həmin ianəni tamamlayın və ya ləğv edin. <a %(a_all_donations)s>Bütün ianələrimi gör</a> $5000-dan çox bağışlar üçün birbaşa bizimlə %(email)s əlaqə saxlayın. Biz zəngin şəxslərdən və ya qurumlardan böyük ianələri qəbul edirik.  Unutmayın ki, bu səhifədəki üzvlüklər “aylıq” olsa da, bunlar bir dəfəlik ianələrdir (təkrarlanmayan). <a %(faq)s>İanə FAQ</a> bölümünə baxın. Anna’nın Arxivi qeyri-kommersiya, açıq mənbə və açıq məlumat layihəsidir. Bağış edərək və üzv olaraq əməliyyatlarımızı və inkişafımızı dəstəkləyirsiniz. Bütün üzvlərimizə: bizi davam etdirdiyiniz üçün təşəkkür edirik! ❤️ Daha ətraflı məlumat üçün <a %(a_donate)s>İanə FAQ</a> səhifəsinə baxın. Üzv olmaq üçün, zəhmət olmasa <a %(a_login)s>Daxil olun və ya Qeydiyyatdan keçin</a>. Dəstəyiniz üçün təşəkkür edirik! %(cost)s / ay Ödəniş zamanı səhv etmisinizsə, geri qaytarma edə bilmərik, amma problemi həll etməyə çalışacağıq. PayPal tətbiqinizdə və ya veb saytınızda “Kripto” səhifəsini tapın. Bu, adətən “Maliyyə” bölməsində olur. PayPal tətbiqinizdə və ya vebsaytınızda “Bitcoin” səhifəsinə keçin. “Transfer” düyməsini %(transfer_icon)s basın və sonra “Göndər” düyməsini basın. Alipay Alipay 支付宝 / WeChat 微信 Amazon Hədiyyə Kartı %(amazon)s hədiyyə kartı Bank kartı Bank kartı (tətbiq istifadə edərək) Binance Kredit/debet/Apple/Google (BMC) Cash App Kredit/debet kartı Kredit/debet kartı 2 Kredit/debet kartı (ehtiyat) Kripto %(bitcoin_icon)s Kart / PayPal / Venmo PayPal (ABŞ) %(bitcoin_icon)s PayPal PayPal (adi) Pix (Braziliya) Revolut (müvəqqəti olaraq əlçatmaz) WeChat Seçdiyiniz kripto valyutanı seçin: Amazon hədiyyə kartı ilə ianə edin. <strong>VACİB:</strong> Bu seçim %(amazon)s üçündür. Başqa bir Amazon veb saytından istifadə etmək istəyirsinizsə, yuxarıdan seçin. <strong>VACİB:</strong> Biz yalnız Amazon.com-u dəstəkləyirik, digər Amazon veb saytlarını deyil. Məsələn, .de, .co.uk, .ca DƏSTƏKLƏNMİR. Zəhmət olmasa, öz mesajınızı yazmayın. Dəqiq məbləği daxil edin: %(amount)s Qeyd edək ki, biz resellerlərimiz tərəfindən qəbul edilən məbləğlərə (minimum %(minimum)s) yuvarlamaq məcburiyyətindəyik. Kredit/debet kartı istifadə edərək, Alipay tətbiqi vasitəsilə ianə edin (quraşdırmaq çox asandır). Alipay tətbiqini <a %(a_app_store)s>Apple App Store</a> və ya <a %(a_play_store)s>Google Play Store</a>dan quraşdırın. Telefon nömrənizlə qeydiyyatdan keçin. Əlavə şəxsi məlumatlar tələb olunmur. <span %(style)s>1</span>Alipay tətbiqini quraşdırın Dəstəklənir: Visa, MasterCard, JCB, Diners Club və Discover. Daha çox məlumat üçün <a %(a_alipay)s>bu bələdçiyə</a> baxın. <span %(style)s>2</span>Bank kartı əlavə edin Binance ilə kredit/debet kartı və ya bank hesabı ilə Bitcoin alırsınız və sonra həmin Bitcoini bizə bağışlayırsınız. Bu yolla biz bağışınızı qəbul edərkən təhlükəsiz və anonim qala bilirik. Binance demək olar ki, hər ölkədə mövcuddur və əksər bankları və kredit/debet kartlarını dəstəkləyir. Bu, hazırda bizim əsas tövsiyəmizdir. Bu üsulla necə ianə etməyi öyrənməyə vaxt ayırdığınız üçün təşəkkür edirik, çünki bu, bizə çox kömək edir. Kredit kartları, debet kartları, Apple Pay və Google Pay üçün “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) istifadə edirik. Onların sistemində bir “qəhvə” $5-a bərabərdir, buna görə də ianəniz 5-in ən yaxın qatına yuvarlanacaq. Cash App istifadə edərək bağış edin. Əgər Cash App istifadə edirsinizsə, bu, ianə etmək üçün ən asan yoldur! Qeyd edək ki, %(amount)s altında olan əməliyyatlar üçün Cash App %(fee)s haqqı ala bilər. %(amount)s və ya daha çox üçün, bu pulsuzdur! Kredit və ya debet kartı ilə ianə edin. Bu metod aralıq çevirmə üçün kriptovalyuta təminatçısından istifadə edir. Bu bir az qarışıq ola bilər, ona görə də yalnız digər ödəniş üsulları işləmədikdə bu metodu istifadə edin. Bu, həmçinin bütün ölkələrdə işləmir. Biz kredit/debet kartlarını birbaşa dəstəkləyə bilmirik, çünki banklar bizimlə işləmək istəmir. ☹ Lakin, kredit/debet kartlarını istifadə etməyin bir neçə yolu var, digər ödəniş üsullarını istifadə edərək: Kripto ilə BTC, ETH, XMR və SOL istifadə edərək ianə edə bilərsiniz. Bu seçimi kriptovalyuta ilə artıq tanışsınızsa istifadə edin. Kripto ilə BTC, ETH, XMR və daha çox istifadə edərək ianə edə bilərsiniz. Kripto ekspres xidmətləri Əgər kriptovalyutadan ilk dəfə istifadə edirsinizsə, Bitcoin (orijinal və ən çox istifadə edilən kriptovalyuta) almaq və ianə etmək üçün %(options)s istifadə etməyi tövsiyə edirik. Kiçik ianələr üçün kredit kartı ödənişlərinin %(discount)s%% endirimimizi aradan qaldıra biləcəyini unutmayın, buna görə daha uzun abunəlikləri tövsiyə edirik. Kredit/debet kartı, PayPal və ya Venmo istifadə edərək ianə edin. Növbəti səhifədə bunlar arasında seçim edə bilərsiniz. Google Pay və Apple Pay də işləyə bilər. Qeyd edək ki, kiçik ianələr üçün rüsumlar yüksəkdir, buna görə daha uzun abunəlikləri tövsiyə edirik. PayPal US istifadə edərək ianə etmək üçün PayPal Crypto istifadə edəcəyik, bu da anonim qalmağımıza imkan verir. Bu üsulla necə ianə edəcəyinizi öyrənməyə vaxt ayırdığınız üçün minnətdarıq, çünki bu, bizə çox kömək edir. PayPal istifadə edərək ianə edin. Adi PayPal hesabınızı istifadə edərək ianə edin. Revolut istifadə edərək ianə edin. Əgər Revolut istifadə edirsinizsə, bu, ianə etmək üçün ən asan yoldur! Bu ödəniş üsulu maksimum %(amount)s üçün icazə verir. Fərqli bir müddət və ya ödəniş üsulu seçin. Bu ödəniş üsulu minimum %(amount)s tələb edir. Fərqli müddət və ya ödəniş üsulu seçin. Binance Coinbase Kraken Zəhmət olmasa ödəniş üsulunu seçin. “Torrent qəbul et”: istifadəçi adınız və ya mesajınız torrent fayl adında <div %(div_months)s>üzvlüyün hər 12 ayında bir dəfə</div> İstifadəçi adınız və ya anonim qeydiniz kreditlərdə Yeni funksiyalara erkən giriş Eksklüziv Telegram ilə səhnə arxası yeniliklər Gündə %(number)s sürətli yükləmə əgər bu ay ianə etsəniz! <a %(a_api)s>JSON API</a> girişi İnsanlığın bilik və mədəniyyətinin qorunmasında əfsanəvi status Əvvəlki üstünlüklər, üstəgəl: <a %(a_refer)s>Dostlarınızı dəvət edərək</a> <strong>%(percentage)s%% bonus yükləmələr</strong> qazanın. SciDB məqalələri <strong>məhdudiyyətsiz</strong> təsdiq olmadan Hesab və ya ianə sualları soruşarkən, hesab ID-nizi, ekran görüntülərini, qəbzləri və mümkün qədər çox məlumatı əlavə edin. Biz emailimizi hər 1-2 həftədə bir yoxlayırıq, buna görə də bu məlumatları daxil etməmək hər hansı bir həllin gecikməsinə səbəb olacaq. Daha çox yükləmə əldə etmək üçün, <a %(a_refer)s>dostlarınıza müraciət edin</a>! Biz kiçik bir könüllü komandayıq. Cavab vermək üçün 1-2 həftə vaxt ala bilər. Hesab adının və ya şəklinin qəribə görünə biləcəyini unutmayın. Narahat olmağa ehtiyac yoxdur! Bu hesablar bizim bağış tərəfdaşlarımız tərəfindən idarə olunur. Hesablarımız hack edilməyib. İanə et <span %(span_cost)s></span> <span %(span_label)s></span> 12 ay üçün “%(tier_name)s” 1 ay üçün “%(tier_name)s” 24 ay üçün “%(tier_name)s” 3 ay üçün “%(tier_name)s” 48 ay üçün “%(tier_name)s” 6 ay üçün “%(tier_name)s” 96 ay üçün “%(tier_name)s” Bağışınızı ödəmə zamanı hələ də ləğv edə bilərsiniz. Bu ianəni təsdiqləmək üçün ianə düyməsini basın. <strong>Vacib qeyd:</strong> Kripto qiymətləri vəhşicəsinə dəyişə bilər, bəzən bir neçə dəqiqə ərzində 20%% qədər. Bu, hələ də bir çox ödəniş təminatçılarımızla işləyərkən qarşılaşdığımız 50-60%% qədər olan xərclərdən azdır, çünki biz “kölgə xeyriyyə” kimi fəaliyyət göstəririk. <u>Əgər bizə ödədiyiniz orijinal qiymətlə qəbzi göndərsəniz, seçdiyiniz üzvlük üçün hesabınıza kredit verəcəyik</u> (qəbz bir neçə saatdan köhnə olmadıqca). Bizə dəstək olmaq üçün belə şeylərlə məşğul olmağa hazır olduğunuz üçün həqiqətən minnətdarıq! ❤️ ❌ Bir şey səhv getdi. Zəhmət olmasa səhifəni yeniləyin və yenidən cəhd edin. <span %(span_circle)s>1</span>Paypal-da Bitcoin alın <span %(span_circle)s>2</span> Bitcoini ünvanımıza köçürün ✅ Bağış səhifəsinə yönləndirilir… Bağış et Zəhmət olmasa, bizimlə əlaqə saxlamazdan əvvəl ən azı <span %(span_hours)s>24 saat</span> gözləyin (və bu səhifəni yeniləyin). Üzvlük olmadan (istənilən məbləğdə) ianə etmək istəyirsinizsə, bu Monero (XMR) ünvanından istifadə edin: %(address)s. Hədiyyə kartınızı göndərdikdən sonra, avtomatlaşdırılmış sistemimiz bir neçə dəqiqə ərzində bunu təsdiqləyəcək. Bu işləmirsə, hədiyyə kartınızı yenidən göndərməyə çalışın (<a %(a_instr)s>təlimatlar</a>). Əgər bu da işləmirsə, zəhmət olmasa bizə e-poçt göndərin və Anna bunu əl ilə nəzərdən keçirəcək (bu bir neçə gün çəkə bilər) və artıq yenidən göndərməyə cəhd etdiyinizi qeyd etməyi unutmayın. Nümunə: Zəhmət olmasa bizə %(amount)s hədiyyə kartı göndərmək üçün <a %(a_form)s>rəsmi Amazon.com formasından</a> istifadə edin. “Alıcı” emaili bu formada: Amazon hədiyyə kartı Biz Amazon.com-un rəsmi formasından birbaşa göndərilən hədiyyə kartları istisna olmaqla, digər hədiyyə kartı üsullarını qəbul edə bilmirik. Bu formadan istifadə etməsəniz, hədiyyə kartınızı geri qaytara bilmərik. Yalnız bir dəfə istifadə edin. Hesabınıza məxsusdur, paylaşmayın. Hədiyyə kartını gözləyir… (yoxlamaq üçün səhifəni yeniləyin) <a %(a_href)s>QR-kod ianə səhifəsini</a> açın. Alipay tətbiqi ilə QR kodunu skan edin və ya Alipay tətbiqini açmaq üçün düyməni basın. Zəhmət olmasa səbirli olun; səhifənin yüklənməsi bir az vaxt ala bilər, çünki bu, Çindədir. <span %(style)s>3</span>İanə edin (QR kodu skan edin və ya düyməni basın) PayPal-da PYUSD coin alın Cash App ilə Bitcoin (BTC) alın Əməliyyat haqlarını qarşılamaq üçün ianə etdiyiniz məbləğdən (%(amount)s) bir az daha çox (biz %(more)s daha çoxunu tövsiyə edirik) alın. Artıq qalan məbləğ sizdə qalacaq. Cash App-də “Bitcoin” (BTC) səhifəsinə keçin. Bitcoini bizim ünvana köçürün Kiçik ianələr üçün (25 dollardan az) Rush və ya Priority istifadə etməli ola bilərsiniz. “Send bitcoin” düyməsini basaraq “çıxarış” edin. %(icon)s ikonuna basaraq dolları BTC-yə dəyişin. Aşağıda BTC məbləğini daxil edin və “Send” düyməsini basın. Əgər ilişib qalsanız, <a %(help_video)s>bu videoya</a> baxın. Ekspres xidmətlər rahatdır, lakin daha yüksək ödənişlər tələb edir. Əgər daha böyük bir ianəni tez bir zamanda etmək istəyirsinizsə və $5-10 ödənişə etiraz etmirsinizsə, bunu kripto mübadiləsi əvəzinə istifadə edə bilərsiniz. İanə səhifəsində göstərilən dəqiq kripto məbləğini göndərdiyinizə əmin olun, $USD məbləğini deyil. Əks halda, ödəniş çıxılacaq və üzvlüyünüzü avtomatik olaraq işləyə bilməyəcəyik. Bəzən təsdiq 24 saata qədər çəkə bilər, ona görə də bu səhifəni yenilədiyinizdən əmin olun (hətta müddəti bitmiş olsa belə). Kredit / debet kartı təlimatları Kredit / debet kartı səhifəmiz vasitəsilə ianə edin Bəzi addımlar kripto cüzdanlarını qeyd edir, lakin narahat olmayın, bunun üçün kripto haqqında heç nə öyrənməyinizə ehtiyac yoxdur. %(coin_name)s təlimatlar Ödəniş təfərrüatlarını tez doldurmaq üçün bu QR kodunuzu kriptovalyutası tətbiqetmənizlə skan edin Ödəmək üçün QR kodunu tarayın Biz yalnız kripto pulların standart versiyasını dəstəkləyirik, ekzotik şəbəkələr və ya versiyalar yox. Əməliyyatı təsdiqləmək, kripto puldan asılı olaraq, bir saata qədər çəkə bilər. <a %(a_page)s>bu səhifədə</a> %(amount)s ianə edin. Bu bağışın müddəti bitib. Lütfən, ləğv edin və yenisini yaradın. Əgər artıq ödəniş etmisinizsə: Bəli, mən qəbzimi e-poçtla göndərdim Əgər kripto mübadilə kursu əməliyyat zamanı dəyişibsə, orijinal mübadilə kursunu göstərən qəbzi daxil etməyi unutmayın. Kriptodan istifadə etdiyiniz üçün çox minnətdarıq, bu bizə çox kömək edir! ❌ Bir şey səhv getdi. Zəhmət olmasa səhifəni yeniləyin və yenidən cəhd edin. <span %(span_circle)s>%(circle_number)s</span>Qəbzi bizə email ilə göndərin Hər hansı bir problemlə qarşılaşsanız, zəhmət olmasa %(email)s ilə əlaqə saxlayın və mümkün qədər çox məlumat (məsələn, ekran görüntüləri) daxil edin. ✅ İanəniz üçün təşəkkür edirik! Anna bir neçə gün ərzində üzvlüyünüzü əl ilə aktiv edəcək. Qəbzi və ya ekran görüntüsünü şəxsi təsdiq ünvanınıza göndərin: Qəbzinizi e-poçtla göndərdikdən sonra, Anna-nın onu əl ilə nəzərdən keçirməsi üçün bu düyməni basın (bu bir neçə gün çəkə bilər): Qəbzi və ya ekran görüntüsünü şəxsi təsdiq ünvanınıza göndərin. Bu e-poçt ünvanını PayPal ianəniz üçün istifadə ETMƏYİN. Ləğv et Bəli, ləğv edin Ləğv etmək istədiyinizə əminsinizmi? Artıq ödəmişsinizsə, ləğv etməyin. ❌ Bir şey səhv getdi. Zəhmət olmasa səhifəni yenidən yükləyin və yenidən cəhd edin. Yeni bağış edin ✅ İanəniz ləğv edildi. Tarix: %(date)s İdentifikator: %(id)s Yenidən sırala Status: <span %(span_label)s>%(label)s</span> Cəmi: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ay üçün %(duration)s ay, %(discounts)s%% endirim daxil olmaqla)</span> Cəmi: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ay üçün %(duration)s ay)</span> 1. E-poçtunuzu daxil edin. 2. Ödəniş üsulunuzu seçin. 3. Ödəniş üsulunuzu yenidən seçin. 4. “Öz-ev sahibli” cüzdanı seçin. 5. “Mülkiyyətimi təsdiq edirəm” düyməsini klikləyin. 6. Sizə bir e-poçt qəbzi gəlməlidir. Lütfən, onu bizə göndərin və bağışınızı ən qısa zamanda təsdiqləyəcəyik. (bağışı ləğv etmək və yenisini yaratmaq istəyə bilərsiniz) Ödəniş təlimatları indi köhnəlib. Başqa bir ianə etmək istəyirsinizsə, yuxarıdakı “Yenidən sifariş et” düyməsini istifadə edin. Siz artıq ödəmisiniz. Yenə də ödəniş təlimatlarını nəzərdən keçirmək istəyirsinizsə, bura klikləyin: Köhnə ödəniş təlimatlarını göstərin Əgər ianə səhifəsi bloklanırsa, fərqli bir internet bağlantısını (məsələn, VPN və ya telefon interneti) sınayın. Təəssüf ki, Alipay səhifəsi tez-tez yalnız <strong>Çin materiki</strong>ndən əlçatan olur. VPN-i müvəqqəti olaraq söndürməyiniz və ya Çin materikinə (bəzən Honq Konq da işləyir) VPN istifadə etməyiniz lazım ola bilər. <span %(span_circle)s>1</span>Alipay vasitəsilə ianə edin <a %(a_account)s>Bu Alipay hesabı</a> vasitəsilə %(total)s məbləğini bağışlayın Alipay təlimatları <span %(span_circle)s>1</span>Kripto hesablarımızdan birinə köçürün Ümumi məbləği %(total)s bu ünvanlardan birinə bağışlayın: Kripto təlimatları Bitcoin (BTC) almaq üçün təlimatları izləyin. Yalnız bağışlamaq istədiyiniz məbləği almağınız kifayətdir, %(total)s. Bitcoin (BTC) ünvanımızı alıcı kimi daxil edin və %(total)s ianənizi göndərmək üçün təlimatlara əməl edin: <span %(span_circle)s>1</span>Pix ilə ianə edin <a %(a_account)s>Bu Pix hesabı</a> vasitəsilə %(total)s məbləğini ianə edin Pix təlimatlar <span %(span_circle)s>1</span>WeChat-da ianə et <a %(a_account)s>Bu WeChat hesabı</a> vasitəsilə ümumi məbləği %(total)s ianə edin WeChat təlimatları Aşağıdakı “kredit kartından Bitcoin-ə” sürətli xidmətlərindən hər hansı birini istifadə edin, bu yalnız bir neçə dəqiqə çəkir: BTC / Bitcoin ünvanı (xarici cüzdan): BTC / Bitcoin məbləği: Formada aşağıdakı məlumatları doldurun: Əgər bu məlumatlardan hər hansı biri köhnəlibsə, zəhmət olmasa bizə e-poçt göndərərək xəbər verin. Zəhmət olmasa bu <span %(underline)s>dəqiq məbləği</span> istifadə edin. Ümumi xərcləriniz kredit kartı rüsumlarına görə daha yüksək ola bilər. Təəssüf ki, kiçik məbləğlər üçün bu, endirimimizdən çox ola bilər. (minimum: %(minimum)s, ilk əməliyyat üçün təsdiq tələb olunmur) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, ilk əməliyyat üçün təsdiq tələb olunmur) (minimum: %(minimum)s) (minimum: %(minimum)s ölkəyə görə dəyişir, ilk əməliyyat üçün təsdiq tələb olunmur) PYUSD coin (PayPal USD) almaq üçün təlimatları izləyin. Bir az daha çox alın (biz %(more)s daha çoxunu tövsiyə edirik) bağışladığınız məbləğdən (%(amount)s), əməliyyat haqlarını qarşılamaq üçün. Artıq qalan məbləğ sizdə qalacaq. PayPal tətbiqinizdə və ya veb saytınızda “PYUSD” səhifəsinə keçin. “Transfer” düyməsini basın %(icon)s, sonra isə “Göndər” düyməsini basın. Statusu yeniləyin Taymeri yenidən qurmaq üçün sadəcə yeni bir ianə yaradın. Aşağıdakı BTC məbləğini istifadə etdiyinizə əmin olun, <em>EURO və ya dollar deyil</em>, əks halda düzgün məbləği ala bilməyəcəyik və üzvlüyünüzü avtomatik təsdiq edə bilməyəcəyik. Revolut ilə Bitcoin (BTC) alın Əməliyyat haqlarını qarşılamaq üçün ianə etdiyiniz məbləğdən (%(amount)s) bir az daha çox (biz %(more)s daha çoxunu tövsiyə edirik) alın. Artıq qalan məbləğ sizdə qalacaq. Bitcoin (BTC) almaq üçün Revolut-da “Crypto” səhifəsinə keçin. Bitcoini bizim ünvana köçürün Kiçik ianələr üçün (25 dollardan az) Rush və ya Priority istifadə etməli ola bilərsiniz. “Send bitcoin” düyməsini basaraq “çıxarış” edin. %(icon)s ikonuna basaraq avronu BTC-yə dəyişin. Aşağıda BTC məbləğini daxil edin və “Send” düyməsini basın. Əgər ilişib qalsanız, <a %(help_video)s>bu videoya</a> baxın. Status: 1 2 Addım-addım bələdçi Addım-addım təlimatları aşağıda görün. Əks halda bu hesabdan kənarlaşdırıla bilərsiniz! Əgər hələ etməmisinizsə, giriş üçün gizli açarınızı yazın: Bağışınız üçün təşəkkür edirik! Qalan vaxt: Bağış %(amount)s -i %(account)s -ə köçür Təsdiq gözlənilir (yoxlamaq üçün səhifəni yeniləyin)… Transfer gözlənilir (yoxlamaq üçün səhifəni yeniləyin)… Əvvəlki Son 24 saat ərzində sürətli yükləmələr gündəlik limitə daxil edilir. Sürətli Tərəfdaş Serverlərindən yükləmələr %(icon)s ilə işarələnmişdir. Son 18 saat Hələ heç bir fayl yüklənməyib. Yüklənmiş fayllar ictimaiyyətə göstərilmir. Bütün vaxtlar UTC ilə göstərilir. Yüklənmiş fayllar Həm sürətli, həm də yavaş yükləmələrlə fayl yükləmisinizsə, o iki dəfə görünəcək. Çox narahat olmayın, bizimlə əlaqəli vebsaytlardan yükləyən çoxlu insan var və problemə düşmək çox nadirdir. Ancaq təhlükəsiz qalmaq üçün VPN (ödənişli) və ya <a %(a_tor)s>Tor</a> (pulsuz) istifadə etməyi tövsiyə edirik. George Orwell-in "1984" əsərini yüklədim, polis qapıma gələcəkmi? Siz Annasınız! Anna kimdir? Üzvlər üçün sürətli yükləmə URL-i əldə etmək üçün bir sabit JSON API-miz var: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON daxilində sənədləşmə). Digər istifadə halları üçün, məsələn, bütün fayllarımızı təkrarlamaq, xüsusi axtarış qurmaq və s., biz <a %(a_generate)s>yaratmağı</a> və ya <a %(a_download)s>yükləməyi</a> tövsiyə edirik ElasticSearch və MariaDB məlumat bazalarımızı. Xam məlumatlar <a %(a_explore)s>JSON faylları vasitəsilə</a> əl ilə araşdırıla bilər. Xam torrentlər siyahımızı <a %(a_torrents)s>JSON</a> formatında da yükləyə bilərsiniz. API-niz varmı? Burada heç bir müəllif hüququ ilə qorunan material yerləşdirmirik. Biz axtarış mühərrikiyik və buna görə də yalnız artıq ictimaiyyətə açıq olan metadataları indeksləyirik. Bu xarici mənbələrdən yükləyərkən, yurisdiksiyanızda nəyin icazəli olduğunu yoxlamağı tövsiyə edirik. Başqaları tərəfindən yerləşdirilən məzmun üçün məsuliyyət daşımırıq. Burada gördüklərinizlə bağlı şikayətləriniz varsa, ən yaxşı yol orijinal vebsaytla əlaqə saxlamaqdır. Onların dəyişikliklərini mütəmadi olaraq məlumat bazamıza daxil edirik. Əgər həqiqətən də cavab verməli olduğumuz etibarlı DMCA şikayətiniz olduğunu düşünürsünüzsə, lütfən, <a %(a_copyright)s>DMCA / Müəllif hüquqları iddia formunu</a> doldurun. Şikayətlərinizə ciddi yanaşırıq və ən qısa zamanda sizə cavab verəcəyik. Müəllif hüquqlarının pozulmasını necə bildirə bilərəm? Kölgə kitabxanaları və rəqəmsal qoruma dünyası üçün xüsusi əhəmiyyət daşıyan bəzi kitablar: Sevdiyiniz kitablar hansılardır? Həmçinin, bütün kod və məlumatlarımızın tamamilə açıq mənbə olduğunu hər kəsə xatırlatmaq istərdik. Bu, bizim kimi layihələr üçün unikal bir xüsusiyyətdir — biz oxşar nəhəng kataloqa malik olan və tamamilə açıq mənbə olan başqa bir layihədən xəbərdar deyilik. Layihəmizi pis idarə etdiyimizi düşünən hər kəsi kodumuzu və məlumatlarımızı götürüb öz kölgə kitabxanalarını qurmağa dəvət edirik! Bunu kinayə ilə demirik — həqiqətən bunun möhtəşəm olacağını düşünürük, çünki bu, hər kəs üçün standartları yüksəldər və bəşəriyyətin irsini daha yaxşı qoruyar. Bu layihəni necə idarə etdiyinizi sevmirəm! İnsanların <a %(a_mirrors)s>güzgülər</a> qurmasını çox istərdik və biz bunu maliyyə cəhətdən dəstəkləyəcəyik. Necə kömək edə bilərəm? Bəli, edirik. Metadatanı toplamaq üçün ilhamımız Aaron Swartzın “hər nəşr olunmuş kitab üçün bir veb səhifə” məqsədidir, bunun üçün o, <a %(a_openlib)s>Open Library</a> yaratdı. Bu layihə yaxşı işlər görüb, lakin bizim unikal mövqeyimiz bizə onların əldə edə bilmədiyi metadatanı əldə etməyə imkan verir. Digər bir ilham mənbəyimiz isə <a %(a_blog)s>dünyada neçə kitab olduğunu</a> bilmək istəyimiz idi ki, hələ də xilas etməli olduğumuz kitabların sayını hesablaya bilək. Metadataları toplayırsınızmı? Qeyd edək ki, mhut.org müəyyən IP aralıqlarını bloklayır, buna görə VPN tələb oluna bilər. <strong>Android:</strong> Yuxarı sağ küncdə üç nöqtəli menyuya klikləyin və “Əsas ekrana əlavə et” seçin. <strong>iOS:</strong> Aşağıdakı “Paylaş” düyməsini klikləyin və “Əsas ekrana əlavə et” seçimini seçin. Bizim rəsmi mobil tətbiqimiz yoxdur, lakin bu vebsaytı tətbiq kimi quraşdıra bilərsiniz. Mobil tətbiqiniz varmı? Zəhmət olmasa onları <a %(a_archive)s>Internet Archive</a>-ə göndərin. Onlar düzgün şəkildə qoruyacaqlar. Kitabları və ya digər fiziki materialları necə ianə edə bilərəm? Kitabları necə tələb edim? <a %(a_blog)s>Anna’nın Bloqu</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — müntəzəm yeniliklər <a %(a_software)s>Anna’nın Proqramı</a> — açıq mənbə kodumuz <a %(a_datasets)s>Datasets</a> — məlumat haqqında <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativ domenlər Anna’nın Arxivi haqqında daha çox resurs varmı? <a %(a_translate)s>Anna’nın Arxivi</a> — bizim tərcümə sistemimiz <a %(a_wikipedia)s>Vikipediya</a> — haqqımızda daha çox məlumat (zəhmət olmasa, bu səhifəni yeniləməyə kömək edin və ya öz diliniz üçün birini yaradın!) Bəyəndiyiniz parametrləri seçin, axtarış qutusunu boş saxlayın, “Axtar” düyməsini basın və sonra brauzerinizin əlfəcin xüsusiyyəti ilə səhifəni əlfəcin edin. Axtarış parametrlərimi necə yadda saxlayım? Təhlükəsizlik tədqiqatçılarını sistemlərimizdə zəiflikləri axtarmağa dəvət edirik. Məsuliyyətli açıqlamanın böyük tərəfdarlarıyıq. Bizimlə <a %(a_contact)s>buradan</a> əlaqə saxlayın. Hazırda yalnız anonimliyimizi təhlükəyə ata biləcək zəifliklər üçün mükafatlar verə bilirik, bu zəifliklər üçün $10k-50k aralığında mükafatlar təklif edirik. Gələcəkdə daha geniş bir mükafat sahəsi təklif etmək istəyirik! Sosial mühəndislik hücumlarının mükafat sahəsinə daxil olmadığını unutmayın. Əgər hücum təhlükəsizliyi ilə maraqlanırsınızsa və dünyanın bilik və mədəniyyətini arxivləşdirməkdə kömək etmək istəyirsinizsə, bizimlə əlaqə saxlayın. Kömək edə biləcəyiniz bir çox yollar var. Məsuliyyətli açıqlama proqramınız varmı? Dünyadakı hər kəsə yüksək sürətli yükləmələr təmin etmək üçün kifayət qədər resursumuz yoxdur, nə qədər istəsək də. Əgər zəngin bir xeyriyyəçi bizə bu imkanı təmin etmək istəsə, bu inanılmaz olardı, amma o vaxta qədər əlimizdən gələni edirik. Biz bağışlarla güclə özünü təmin edə bilən qeyri-kommersiya layihəsiyik. Bu səbəbdən, tərəfdaşlarımızla birlikdə pulsuz yükləmələr üçün iki sistem tətbiq etdik: yavaş yükləmələr üçün paylaşılan serverlər və gözləmə siyahısı ilə bir qədər sürətli serverlər (eyni vaxtda yükləyən insanların sayını azaltmaq üçün). Bizim yavaş yükləmələrimiz üçün <a %(a_verification)s>brauzer təsdiqi</a> də var, çünki əks halda botlar və skreperlər onları sui-istifadə edəcək, bu da qanuni istifadəçilər üçün hər şeyi daha da yavaş edəcək. Qeyd edək ki, Tor Brauzerindən istifadə edərkən təhlükəsizlik parametrlərinizi tənzimləməli ola bilərsiniz. “Standart” adlanan ən aşağı seçimdə, Cloudflare turniketi uğurla keçilir. “Daha Təhlükəsiz” və “Ən Təhlükəsiz” adlanan daha yüksək seçimlərdə isə turniket uğursuz olur. Böyük fayllar üçün bəzən yükləmələr ortasında kəsilə bilər. Biz böyük yükləmələri avtomatik olaraq davam etdirmək üçün yükləmə meneceri (məsələn, JDownloader) istifadə etməyi tövsiyə edirik. Niyə yükləmələr bu qədər yavaşdır? Tez-tez verilən suallar (FAQ) <a %(a_list)s>Torrent siyahısı generatoru</a>ndan istifadə edərək, yaddaş məhdudiyyətləriniz daxilində torrentə ən çox ehtiyacı olan torrentlərin siyahısını yaradın. Bəli, <a %(a_llm)s>LLM məlumatları</a> səhifəsinə baxın. Əksər torrentlər faylları birbaşa ehtiva edir, bu da torrent müştərilərinə yalnız tələb olunan faylları yükləməyi təlimatlandırmağa imkan verir. Hansı faylları yükləmək lazım olduğunu müəyyən etmək üçün metadata <a %(a_generate)s>yarada</a> və ya ElasticSearch və MariaDB bazalarımızı <a %(a_download)s>yükləyə</a> bilərsiniz. Təəssüf ki, bir sıra torrent kolleksiyaları kökdə .zip və ya .tar faylları ehtiva edir, bu halda fərdi faylları seçməzdən əvvəl bütün torrent yüklənməlidir. Hələlik torrentləri süzmək üçün asan istifadə olunan alətlər mövcud deyil, lakin biz töhfələri alqışlayırıq. (Amma sonuncu hal üçün <a %(a_ideas)s>bəzi fikirlərimiz</a> var.) Uzun cavab: Qısa cavab: asan deyil. Bu siyahıdakı torrentlər arasında minimal təkrarlanma və ya üst-üstə düşmə saxlamağa çalışırıq, lakin bu həmişə mümkün olmur və mənbə kitabxanalarının siyasətlərindən çox asılıdır. Öz torrentlərini çıxaran kitabxanalar üçün bu bizim əlimizdə deyil. Anna’nın Arxivi tərəfindən buraxılan torrentlər üçün yalnız MD5 hash əsasında təkrarlanmanı aradan qaldırırıq, bu da eyni kitabın müxtəlif versiyalarının təkrarlanmadığı deməkdir. Bəli. Bunlar əslində PDF və EPUB-lardır, sadəcə bir çox torrentlərimizdə uzantıları yoxdur. Torrent fayllarının metadatasını, o cümlədən fayl növlərini/uzantılarını tapa biləcəyiniz iki yer var: 1. Hər kolleksiya və ya buraxılışın öz metadatası var. Məsələn, <a %(a_libgen_nonfic)s>Libgen.rs torrentləri</a> müvafiq metadata bazasına malikdir və bu baza Libgen.rs saytında yerləşdirilib. Biz adətən hər kolleksiyanın <a %(a_datasets)s>dataset səhifəsindən</a> müvafiq metadata resurslarına keçid veririk. 2. ElasticSearch və MariaDB bazalarımızı <a %(a_generate)s>yaratmağı</a> və ya <a %(a_download)s>yükləməyi</a> tövsiyə edirik. Bunlar Anna’nın Arxivindəki hər bir qeydin müvafiq torrent fayllarına (əgər varsa) uyğun gələn xəritəsini “torrent_paths” altında ElasticSearch JSON-da ehtiva edir. Bəzi torrent müştəriləri böyük parça ölçülərini dəstəkləmir, bu da bizim torrentlərimizin çoxunda var (yenilər üçün bunu artıq etmirik — baxmayaraq ki, spesifikasiyalara görə düzgündür!). Buna görə də, bu problemlə qarşılaşsanız, fərqli bir müştəri sınayın və ya torrent müştərinizin istehsalçılarına şikayət edin. Paylaşmaq istəyirəm, amma çox disk sahəm yoxdur. Torrentlər çox yavaşdır; məlumatları birbaşa sizdən yükləyə bilərəmmi? Yalnız faylların bir hissəsini, məsələn, müəyyən bir dili və ya mövzunu yükləyə bilərəmmi? Torrents-də təkrarlananları necə idarə edirsiniz? Torrent siyahısını JSON formatında əldə edə bilərəmmi? Torrentlərdə PDF və ya EPUB görmürəm, yalnız ikili fayllar var? Nə etməliyəm? Niyə torrent müştərim bəzi torrent fayllarınızı / maqnit linklərinizi aça bilmir? Torrents FAQ Yeni kitabları necə yükləyə bilərəm? <a %(a_href)s>bu əla layihəyə</a> baxın. Sizin iş vaxtı monitorunuz varmı? Anna’nın Arxivi nədir? Sürətli yükləmələrdən istifadə etmək üçün üzv olun. İndi Amazon hədiyyə kartları, kredit və debet kartları, kripto, Alipay və WeChat dəstəkləyirik. Bu gün sürətli yükləmələriniz tükənib. Giriş Son 30 gündə saatlıq yükləmələr. Saatlıq orta: %(hourly)s. Gündəlik orta: %(daily)s. Biz kolleksiyalarımızı hər kəs üçün asan və pulsuz əlçatan etmək üçün tərəfdaşlarla işləyirik. Biz inanırıq ki, hər kəsin bəşəriyyətin kollektiv müdrikliyinə haqqı var. Və <a %(a_search)s>müəlliflərin hesabına deyil</a>. Anna Arxivində istifadə olunan datasets tamamilə açıqdır və torrentlər vasitəsilə toplu şəkildə güzgülənə bilər. <a %(a_datasets)s>Daha çox öyrənin…</a> Uzunmüddətli arxiv Tam verilənlər bazası Axtarış Kitablar, məqalələr, jurnallar, komikslər, kitabxana qeydləri, metadata, … Bütün <a %(a_code)s>kodlarımız</a> və <a %(a_datasets)s>məlumatlarımız</a> tamamilə açıq mənbəlidir. <span %(span_anna)s>Anna’nın Arxivi</span> iki məqsədi olan qeyri-kommersiya layihəsidir: <li><strong>Qoruma:</strong> Bütün insan bilik və mədəniyyətini ehtiyat nüsxə etmək.</li><li><strong>Giriş:</strong> Bu bilik və mədəniyyəti dünyanın hər yerində hər kəs üçün əlçatan etmək.</li> Biz dünyanın ən böyük yüksək keyfiyyətli mətn məlumatları kolleksiyasına sahibik. <a %(a_llm)s>Daha çox öyrənin…</a> LLM təlim məlumatları 🪩 Güzgülər: könüllülərə çağırış Əgər yüksək riskli anonim ödəniş prosessoru idarə edirsinizsə, bizimlə əlaqə saxlayın. Biz həmçinin zərif kiçik reklamlar yerləşdirmək istəyən insanları axtarırıq. Bütün gəlirlər qoruma səylərimizə gedir. Qoruma Təxmin edirik ki, dünya kitablarının təxminən <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% hissəsini qorumuşuq</a>. Biz kitabları, məqalələri, komiksləri, jurnalları və daha çoxunu müxtəlif <a href="https://en.wikipedia.org/wiki/Shadow_library">kölgə kitabxanalarından</a>, rəsmi kitabxanalardan və digər kolleksiyalardan bir yerə toplayaraq qoruyuruq. Bu məlumatların əbədi qorunması üçün onu toplu şəkildə çoxaltmağı asanlaşdırırıq — torrentlərdən istifadə edərək — nəticədə dünyanın müxtəlif yerlərində çoxlu nüsxələr yaranır. Bəzi kölgə kitabxanaları bunu özləri edirlər (məsələn, Sci-Hub, Library Genesis), Anna’nın Arxivi isə toplu paylama təklif etməyən digər kitabxanaları “azad edir” (məsələn, Z-Library) və ya ümumiyyətlə kölgə kitabxanaları deyil (məsələn, Internet Archive, DuXiu). Bu geniş yayılma, açıq mənbə kodu ilə birləşərək, veb saytımızı bağlanmalara qarşı dayanıqlı edir və insanlığın bilik və mədəniyyətinin uzunmüddətli qorunmasını təmin edir. <a href="/datasets">Datasets</a> haqqında daha çox öyrənin. Əgər <a %(a_member)s>üzv</a> olsanız, brauzer təsdiqi tələb olunmur. 🧬&nbsp;SciDB, Sci-Hub-un davamıdır. SciDB Açıq DOI Sci-Hub yeni məqalələrin yüklənməsini <a %(a_paused)s>dayandırıb</a>. %(count)s akademik məqalələrə birbaşa giriş 🧬&nbsp;SciDB, Sci-Hub-un davamıdır, tanış interfeysi və PDF-lərin birbaşa görüntülənməsi ilə. DOI-nizi daxil edin və baxın. Bizdə tam Sci-Hub kolleksiyası, eləcə də yeni məqalələr var. Çoxunu Sci-Hub-a bənzər tanış interfeys ilə birbaşa baxmaq mümkündür. Bəziləri xarici mənbələrdən yüklənə bilər, bu halda biz həmin mənbələrə keçidlər göstəririk. Torrentləri paylaşaraq çox kömək edə bilərsiniz. <a %(a_torrents)s>Daha çox öyrənin…</a> >%(count)s toxumlayıcılar <%(count)s yükləyicilər %(count_min)s–%(count_max)s seeders 🤝 Könüllülər axtarılır Qeyri-kommersiya, açıq mənbə layihəsi olaraq, biz həmişə kömək edəcək insanları axtarırıq. IPFS yükləmələri Siyahı %(by)s, yaradılıb <span %(span_time)s>%(time)s</span> Yadda saxla ❌ Bir şey səhv getdi. Zəhmət olmasa yenidən cəhd edin. ✅ Yadda saxlanıldı. Zəhmət olmasa, səhifəni yenidən yükləyin. Siyahı boşdur. redaktə et Bu siyahıya əlavə etmək və ya çıxarmaq üçün faylı tapın və “Siyahılar” sekmesini açın. Siyahı Necə kömək edə bilərik Təkrarlamanın aradan qaldırılması (deduplikasiya) Mətn və metadata çıxarılması OCR Tam kolleksiyalarımıza, eləcə də buraxılmamış kolleksiyalara yüksək sürətli giriş təmin edə bilirik. Bu, on minlərlə ABŞ dolları məbləğində ianələr üçün təmin edə biləcəyimiz müəssisə səviyyəli girişdir. Həmçinin, hələ sahib olmadığımız yüksək keyfiyyətli kolleksiyalarla bunu dəyişməyə hazırıq. Məlumatlarımızın zənginləşdirilməsini təmin edə bilsəniz, sizə geri ödəyə bilərik, məsələn: Modeliniz üçün daha yaxşı məlumat əldə edərkən insan biliklərinin uzunmüddətli arxivləşdirilməsini dəstəkləyin! <a %(a_contact)s>Bizimlə əlaqə saxlayın</a> birlikdə necə işləyə biləcəyimizi müzakirə etmək üçün. LLM-lərin yüksək keyfiyyətli məlumatlarla inkişaf etdiyi yaxşı başa düşülür. Biz dünyanın ən böyük kitablar, məqalələr, jurnallar və s. kolleksiyasına sahibik ki, bunlar ən yüksək keyfiyyətli mətn mənbələrindən biridir. LLM məlumatları Unikal miqyas və çeşid Kolleksiyamız akademik jurnallar, dərsliklər və jurnallar daxil olmaqla yüz milyondan çox faylı ehtiva edir. Biz bu miqyasa mövcud böyük anbarları birləşdirərək nail oluruq. Bəzi mənbə kolleksiyalarımız artıq toplu şəkildə mövcuddur (Sci-Hub və Libgen-in bəzi hissələri). Digər mənbələri isə özümüz azad etdik. <a %(a_datasets)s>Datasets</a> tam icmalı göstərir. Kolleksiyamızda e-kitab dövründən əvvəlki milyonlarla kitab, məqalə və jurnal var. Bu kolleksiyanın böyük hissələri artıq OCR edilib və daxili təkrarlama çox azdır. Davam et Əgər açarınızı itirmisinizsə, zəhmət olmasa <a %(a_contact)s>bizimlə əlaqə saxlayın</a> və mümkün qədər çox məlumat verin. Bizimlə əlaqə saxlamaq üçün müvəqqəti olaraq yeni hesab yaratmalı ola bilərsiniz. Bu səhifəni görmək üçün zəhmət olmasa <a %(a_account)s>daxil olun</a>.</a> Spam-botların çoxlu hesab yaratmasının qarşısını almaq üçün əvvəlcə brauzerinizi yoxlamalıyıq. Sonsuz döngəyə düşsəniz, <a %(a_privacypass)s>Privacy Pass</a> quraşdırmağı tövsiyə edirik. Reklam bloklayıcılarını və digər brauzer genişləndirmələrini söndürmək də kömək edə bilər. Daxil ol / Qeydiyyatdan keç Anna’nın Arxivi müvəqqəti olaraq texniki xidmət üçün bağlanıb. Zəhmət olmasa, bir saat sonra geri dönün. Alternativ müəllif Alternativ təsvir Alternativ nəşr Alternativ uzantı Alternativ fayl adı Alternativ nəşriyyat Alternativ başlıq açıq mənbə tarixi Daha çox oxu… təsvir CADAL SSNO nömrəsi üçün Anna’nın Arxivində axtarış edin DuXiu SSID nömrəsi üçün Anna’nın Arxivində axtarış edin Anna Arxivində DuXiu DXID nömrəsi ilə axtarış edin Anna’nın Arxivində ISBN axtar OCLC (WorldCat) nömrəsi üçün Anna’nın Arxivində axtarış edin Open Library ID üçün Anna’nın Arxivində axtarış edin Anna’nın Arxivi onlayn baxışçısı %(count)s təsirlənmiş səhifələr Yüklədikdən sonra: Bu faylın daha yaxşı versiyası %(link)s mövcud ola bilər Toplu torrent yükləmələri kolleksiya Formatlar arasında çevirmək üçün onlayn alətlərdən istifadə edin. Tövsiyə olunan çevirmə alətləri: %(links)s Böyük fayllar üçün yükləmə menecerindən istifadə etməyi tövsiyə edirik ki, kəsilmələrin qarşısını alasınız. Tövsiyə olunan yükləmə menecerləri: %(links)s EBSCOhost eBook İndeksi (yalnız mütəxəssislər üçün) (həmçinin yuxarıda “GET” düyməsini basın) (yuxarıda “GET” düyməsini basın) Xarici yükləmələr <strong>🚀 Sürətli yükləmələr</strong> Bu gün %(remaining)s yükləmə haqqınız qalıb. Üzv olduğunuz üçün təşəkkür edirik! ❤️ <strong>🚀 Sürətli yükləmələr</strong> Bu gün üçün sürətli yükləmələriniz tükənib. <strong>🚀 Sürətli yükləmələr</strong> Bu faylı son zamanlarda yükləmisiniz. Linklər bir müddət etibarlı qalır. <strong>🚀 Sürətli yükləmələr</strong> Kitabların, məqalələrin və daha çoxunun uzunmüddətli qorunmasını dəstəkləmək üçün <a %(a_membership)s>üzv olun</a>. Dəstəyinizə görə minnətdarlığımızı bildirmək üçün sizə sürətli yükləmələr təqdim edirik. ❤️ 🚀 Sürətli yükləmələr 🐢 Yavaş yükləmələr Internet Archive-dən borc alın IPFS Gateway #%(num)d (IPFS ilə bir neçə dəfə cəhd etməli ola bilərsiniz) Libgen.li Libgen.rs Bədii Ədəbiyyat Libgen.rs Qeyri-Bədii onların reklamları zərərli proqramlar ehtiva edə bilər, buna görə reklam blokerindən istifadə edin və ya reklamlara klikləməyin Amazon‘un “Kindle‘a Göndər” djazz‘ın “Kobo/Kindle‘a Göndər” MagzDB ManualsLib Nexus/STC (Nexus/STC fayllarını yükləmək etibarsız ola bilər) Yükləmələr tapılmadı. Bütün yükləmə seçimlərində eyni fayl var və istifadəsi təhlükəsiz olmalıdır. Bununla belə, internetdən faylları yükləyərkən, xüsusən də Anna Arxivindən kənar saytlardan ehtiyatlı olun. Məsələn, cihazlarınızı yeniləməyə əmin olun. (heç bir yönləndirmə yoxdur) Bizim baxışçımızda aç (baxışda aç) Seçim #%(num)d: %(link)s %(extra)s CADAL-da orijinal qeydi tapın DuXiu-da əl ilə axtarış edin Orijinal qeydi ISBNdb-də tapın WorldCat-da orijinal qeydi tapın Orijinal qeydi Open Library-də tapın ISBN üçün müxtəlif digər məlumat bazalarını axtarın (yalnız çap məhdudiyyətli istifadəçilər üçün) PubMed Faylı açmaq üçün fayl formatına görə bir ebook və ya PDF oxuyucuya ehtiyacınız olacaq. Tövsiyə olunan ebook oxuyucular: %(links)s Anna’nın Arxivi 🧬 SciDB Sci-Hub: %(doi)s (əlaqəli DOI Sci-Hub-da mövcud olmaya bilər) Həm PDF, həm də EPUB fayllarını Kindle və ya Kobo eReader-ə göndərə bilərsiniz. Tövsiyə olunan alətlər: %(links)s Daha çox məlumat <a %(a_slow)s>FAQ</a> bölməsində. Müəllifləri və kitabxanaları dəstəkləyin Əgər bunu bəyənirsinizsə və imkânınız varsa, orijinalını almağı və ya müəllifləri birbaşa dəstəkləməyi düşünün. Əgər bu kitab yerli kitabxananızda mövcuddursa, onu oradan pulsuz götürməyi düşünün. Bu fayl üçün Tərəfdaş Server yükləmələri müvəqqəti olaraq mövcud deyil. torrent Etibarlı tərəfdaşlardan. Z-Library Z-Kitabxana Tor-da (Tor Brauzeri tələb edir) xarici yükləmələri göstər <span class="font-bold">❌ Bu faylda problemlər ola bilər və mənbə kitabxanadan gizlədilib.</span> Bəzən bu müəllif hüquqları sahibinin tələbi ilə olur, bəzən isə daha yaxşı alternativ mövcud olduğu üçün, lakin bəzən faylın özü ilə bağlı problem olduğu üçün. Yükləmək hələ də mümkün ola bilər, lakin əvvəlcə alternativ fayl axtarmağı tövsiyə edirik. Daha ətraflı məlumat: Bu faylı yükləmək istəyirsinizsə, onu açmaq üçün yalnız etibarlı, yenilənmiş proqramlardan istifadə etdiyinizə əmin olun. metadata şərhləri AA: Anna’nın Arxivində “%(name)s” axtarın Kodlar Kəşfiyyatçısı: Kodlar Kəşfiyyatçısında “%(name)s” baxın URL: Vebsayt: Əgər bu fayl sizdə varsa və Anna’nın Arxivində hələ mövcud deyilsə, <a %(a_request)s>onu yükləməyi</a> düşünün. Internet Archive İdarə Edilən Rəqəmsal Borc faylı “%(id)s” Bu, Internet Archive-dən bir faylın qeydi, birbaşa yüklənə bilən fayl deyil. Kitabı borc almağa cəhd edə bilərsiniz (aşağıdakı link), və ya <a %(a_request)s>fayl tələb edərkən</a> bu URL-dən istifadə edə bilərsiniz. Metadatanı təkmilləşdirin CADAL SSNO %(id)s metadata qeydi Bu metadata qeydidir, yüklənə bilən fayl deyil. <a %(a_request)s>Fayl tələb edərkən</a> bu URL-dən istifadə edə bilərsiniz. DuXiu SSID %(id)s metadata qeydi ISBNdb %(id)s metadata qeydi MagzDB ID %(id)s metadata qeydi Nexus/STC ID %(id)s metadata qeydi OCLC (WorldCat) nömrəsi %(id)s metadata qeydi Open Library %(id)s metadata qeydi Sci-Hub faylı “%(id)s” Tapılmadı “%(md5_input)s” məlumat bazamızda tapılmadı. Şərh əlavə et (%(count)s) MD5-i URL-dən əldə edə bilərsiniz, məsələn Bu faylın daha yaxşı versiyasının MD5-i (əgər varsa). Əgər bu fayla çox yaxın olan başqa bir fayl varsa (eyni nəşr, eyni fayl uzantısı varsa), insanlar bu fayldan istifadə etməlidirlər. Anna’s Archive xaricində bu faylın daha yaxşı versiyasını bilirsinizsə, zəhmət olmasa <a %(a_upload)s>yükləyin</a>. Nəsə səhv getdi. Zəhmət olmasa səhifəni yenidən yükləyin və yenidən cəhd edin. Şərh yazdınız. Onun görünməsi bir dəqiqə çəkə bilər. Zəhmət olmasa <a %(a_copyright)s>DMCA / Müəllif hüquqları iddia forması</a>ndan istifadə edin. Problemi təsvir edin (vacibdir) Bu fayl yüksək keyfiyyətlidirsə, burada onun haqqında hər şeyi müzakirə edə bilərsiniz! Əks halda, “Fayl problemi barədə məlumat ver” düyməsini istifadə edin. Əla fayl keyfiyyəti (%(count)s) Fayl keyfiyyəti Özünüz bu fayl üçün <a %(a_metadata)s>metadata-nı necə yaxşılaşdırmağı</a> öyrənin. Problem təsviri Zəhmət olmasa <a %(a_login)s>daxil olun</a>. Bu kitabı çox bəyəndim! Bu faylın keyfiyyətini bildirərək icmaya kömək edin! 🙌 Nəsə səhv getdi. Zəhmət olmasa səhifəni yenidən yükləyin və yenidən cəhd edin. Fayl problemi barədə məlumat ver (%(count)s) Hesabatınızı təqdim etdiyiniz üçün təşəkkür edirik. O, bu səhifədə göstəriləcək və Anna tərəfindən əl ilə nəzərdən keçiriləcək (düzgün moderasiya sistemi əldə edənə qədər). Şərh yazın Hesabatı təqdim et Bu faylda nə səhvdir? Borclan (%(count)s) Şərhlər (%(count)s) Yükləmələr (%(count)s) Metadatanı araşdır (%(count)s) Siyahılar (%(count)s) Statistika (%(count)s) Bu xüsusi fayl haqqında məlumat üçün onun <a %(a_href)s>JSON faylına</a> baxın. Bu, <a %(a_ia)s>IA-nın İdarə olunan Rəqəmsal Kredit</a> kitabxanası tərəfindən idarə olunan və axtarış üçün Anna’nın Arxivində indeksləşdirilən bir fayldır. Tərtib etdiyimiz müxtəlif datasets haqqında məlumat üçün <a %(a_datasets)s>Datasets səhifəsinə</a> baxın. Bağlı qeyddən metadata Open Library-də metadatanı yaxşılaşdırın “Fayl MD5” fayl məzmunundan hesablanan və həmin məzmuna əsasən kifayət qədər unikal olan bir hashdir. Burada indeksləşdirdiyimiz bütün kölgə kitabxanaları əsasən faylları müəyyən etmək üçün MD5-lərdən istifadə edir. Bir fayl bir neçə kölgə kitabxanada görünə bilər. Tərtib etdiyimiz müxtəlif datasets haqqında məlumat üçün <a %(a_datasets)s>Datasets səhifəsinə</a> baxın. Fayl keyfiyyətini bildirin Ümumi yükləmələr: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Çexiya metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Kitablar %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Xəbərdarlıq: bir neçə bağlı qeyd: Anna’nın Arxivində bir kitaba baxdığınızda müxtəlif sahələri görə bilərsiniz: başlıq, müəllif, nəşriyyat, nəşr, il, təsvir, fayl adı və daha çox. Bu məlumatların hamısı <em>metadata</em> adlanır. Çünki biz kitabları müxtəlif <em>mənbə kitabxanalarından</em> birləşdiririk, həmin mənbə kitabxanasında mövcud olan metadatanı göstəririk. Məsələn, Library Genesis-dən aldığımız bir kitab üçün Library Genesis-in bazasından başlığı göstərəcəyik. Bəzən bir kitab <em>bir neçə</em> mənbə kitabxanada mövcud olur və bu kitabxanalar fərqli metadata sahələrinə sahib ola bilər. Bu halda, sadəcə hər sahənin ən uzun versiyasını göstəririk, çünki bu versiya ən faydalı məlumatı ehtiva edir! Digər sahələri isə təsvirin altında, məsələn, "alternativ başlıq" kimi göstərəcəyik (amma yalnız fərqli olduqda). Biz həmçinin mənbə kitabxanasından <em>kodlar</em> kimi identifikatorlar və təsnifatçılar çıxarırıq. <em>Identifikatorlar</em> müəyyən bir kitab nəşrini unikal şəkildə təmsil edir; nümunələr ISBN, DOI, Open Library ID, Google Books ID və ya Amazon ID-dir. <em>Təsnifatçılar</em> bir neçə oxşar kitabı qruplaşdırır; nümunələr Dewey Decimal (DCC), UDC, LCC, RVK və ya GOST-dur. Bəzən bu kodlar mənbə kitabxanalarda açıq şəkildə əlaqələndirilir və bəzən biz onları fayl adından və ya təsvirindən çıxara bilirik (əsasən ISBN və DOI). Biz identifikatorlardan <em>yalnız metadata kolleksiyalarında</em> qeydləri tapmaq üçün istifadə edə bilərik, məsələn, OpenLibrary, ISBNdb və ya WorldCat/OCLC. Axtarış motorumuzda bu kolleksiyaları nəzərdən keçirmək istəyirsinizsə, xüsusi bir <em>metadata tabı</em> var. Biz uyğun qeydlərdən itkin metadata sahələrini doldurmaq üçün istifadə edirik (məsələn, başlıq itkin olduqda) və ya məsələn, "alternativ başlıq" kimi (əgər mövcud başlıq varsa). Bir kitabın metadatasının haradan gəldiyini dəqiq görmək üçün kitab səhifəsindəki <em>“Texniki detallar” tabına</em> baxın. Orada həmin kitab üçün xam JSON-a və orijinal qeydlərin xam JSON-una keçid var. Daha çox məlumat üçün aşağıdakı səhifələrə baxın: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Axtarış (metadata tabı)</a>, <a %(a_codes)s>Kodlar Kəşfiyyatçısı</a> və <a %(a_example)s>Nümunə metadata JSON</a>. Nəhayət, bütün metadatalarımız <a %(a_generated)s>yaradıla</a> və ya <a %(a_downloaded)s>yüklənə</a> bilər ElasticSearch və MariaDB bazaları kimi. Arxa plan Kitabların qorunmasına metadatanı təkmilləşdirərək kömək edə bilərsiniz! Əvvəlcə Anna’nın Arxivi haqqında metadatanın arxa planını oxuyun və sonra Open Library ilə əlaqələndirərək metadatanı necə təkmilləşdirəcəyinizi öyrənin və Anna’nın Arxivində pulsuz üzvlük qazanın. Metadatanı təkmilləşdirin Beləliklə, pis metadata ilə bir fayla rast gəlsəniz, onu necə düzəltməlisiniz? Mənbə kitabxanasına gedib metadatanı düzəltmək üçün onun prosedurlarını izləyə bilərsiniz, amma bir fayl bir neçə mənbə kitabxanada mövcuddursa nə etməli? Anna’nın Arxivində xüsusi olaraq qəbul edilən bir identifikator var. <strong>Open Library-dəki annas_archive md5 sahəsi həmişə digər bütün metadataları üstələyir!</strong> Əvvəlcə bir az geri dönək və Open Library haqqında öyrənək. Open Library 2006-cı ildə Aaron Swartz tərəfindən "hər zaman nəşr olunmuş hər kitab üçün bir veb səhifə" məqsədi ilə qurulmuşdur. Bu, kitab metadatası üçün bir növ Vikipediyadır: hər kəs onu redaktə edə bilər, sərbəst lisenziyalıdır və toplu şəkildə yüklənə bilər. Bu, bizim missiyamızla ən çox uyğun gələn kitab bazasıdır — əslində, Anna’nın Arxivi Aaron Swartz’ın vizyonu və həyatından ilham almışdır. Təkəri yenidən icad etmək əvəzinə, könüllülərimizi Open Library-yə yönəltməyə qərar verdik. Əgər yanlış metadata olan bir kitab görsəniz, aşağıdakı şəkildə kömək edə bilərsiniz: Qeyd edək ki, bu yalnız kitablar üçün işləyir, akademik məqalələr və ya digər fayl növləri üçün deyil. Digər fayl növləri üçün mənbə kitabxananı tapmağı tövsiyə edirik. Dəyişikliklərin Anna’s Archive-ə daxil edilməsi bir neçə həftə çəkə bilər, çünki biz son Open Library məlumat dumpunu yükləməli və axtarış indeksimizi yenidən yaratmalıyıq.  <a %(a_openlib)s>Open Library veb saytına</a> gedin. Düzgün kitab qeydini tapın. <strong>XƏBƏRDARLIQ:</strong> düzgün <strong>nəşri</strong> seçdiyinizə əmin olun. Open Library-də "əsərlər" və "nəşrlər" var. Bir "əsər" "Harry Potter və Fəlsəfə Daşı" ola bilər. Bir "nəşr" ola bilər: 1997-ci ildə Bloomsbery tərəfindən nəşr olunan ilk nəşr, 256 səhifə. 2003-cü ildə Raincoast Books tərəfindən nəşr olunan kağız üzlü nəşr, 223 səhifə. 2000-ci ildə Media Rodzina tərəfindən nəşr olunan polyak tərcüməsi “Harry Potter I Kamie Filozoficzn”, 328 səhifə. Bütün bu nəşrlərin fərqli ISBN-ləri və fərqli məzmunları var, buna görə də düzgün olanı seçdiyinizə əmin olun! Qeydi redaktə edin (və ya heç biri yoxdursa, yaradın) və mümkün qədər çox faydalı məlumat əlavə edin! Onsuz da buradasınız, qeydi həqiqətən möhtəşəm edin. “ID Nömrələri” altında “Anna’s Archive” seçin və kitabın Anna’s Archive-dən olan MD5-ni əlavə edin. Bu, URL-də “/md5/”dən sonra gələn uzun hərf və rəqəm sırasıdır. Anna’s Archive-də bu qeydə uyğun gələn digər faylları tapmağa çalışın və onları da əlavə edin. Gələcəkdə bu faylları Anna’s Archive axtarış səhifəsində dublikatlar kimi qruplaşdıra bilərik. İşinizi bitirdikdən sonra yenilədiyiniz URL-ni yazın. Anna’s Archive MD5-ləri ilə ən azı 30 qeydi yenilədikdən sonra bizə bir <a %(a_contact)s>email</a> göndərin və siyahını bizə göndərin. Sizə Anna’s Archive üçün pulsuz üzvlük verəcəyik ki, bu işi daha asanlıqla edə biləsiniz (və köməyiniz üçün təşəkkür olaraq). Bunlar yüksək keyfiyyətli redaktələr olmalıdır ki, çoxlu məlumat əlavə etsin, əks halda sorğunuz rədd ediləcək. Sorğunuz həmçinin Open Library moderatorları tərəfindən hər hansı redaktə geri qaytarılsa və ya düzəldilsə rədd ediləcək. Open Library ilə əlaqələndirmə Əgər işimizin inkişafı və əməliyyatlarına əhəmiyyətli dərəcədə cəlb olunsanız, zəruri hallarda istifadə etmək üçün daha çox ianə gəlirini sizinlə bölüşməyi müzakirə edə bilərik. Biz yalnız hər şeyi qurduqdan və arxivi yeniləmələrlə yeniləyə biləcəyinizi nümayiş etdirdikdən sonra hosting üçün ödəniş edəcəyik. Bu o deməkdir ki, ilk 1-2 ayı öz cibinizdən ödəməli olacaqsınız. Sizin vaxtınız kompensasiya edilməyəcək (bizim də deyil), çünki bu, tamamilə könüllü işdir. Biz hosting və VPN xərclərini qarşılamağa hazırıq, əvvəlcə ayda 200 dollara qədər. Bu, əsas axtarış serveri və DMCA ilə qorunan proxy üçün kifayətdir. Hosting xərcləri Zəhmət olmasa <strong>bizimlə əlaqə saxlamayın</strong> icazə istəmək və ya əsas suallar üçün. Hərəkətlər sözlərdən daha yüksək səslənir! Bütün məlumatlar mövcuddur, buna görə də güzgünüzü qurmağa başlayın. Problemlərlə qarşılaşdığınız zaman Gitlabımıza biletlər və ya birləşmə sorğuları göndərməkdən çəkinməyin. Sizinlə bəzi güzgü-spesifik xüsusiyyətlər qurmalı ola bilərik, məsələn, “Anna’s Archive”dən veb sayt adınıza rebrendinq, (əvvəlcə) istifadəçi hesablarını deaktiv etmək və ya kitab səhifələrindən əsas saytımıza keçid vermək. Güzgünüzü işə saldıqdan sonra bizimlə əlaqə saxlayın. Sizin OpSec-i nəzərdən keçirməkdən məmnun olarıq və bu möhkəm olduqdan sonra güzgünüzə keçid verəcəyik və sizinlə daha sıx əməkdaşlıq etməyə başlayacağıq. Bu şəkildə töhfə verməyə hazır olan hər kəsə əvvəlcədən təşəkkür edirik! Bu, ürəyi zəif olanlar üçün deyil, lakin insan tarixində ən böyük həqiqətən açıq kitabxananın uzunömürlülüyünü möhkəmləndirərdi. Başlamaq Anna’nın Arxivinin dayanıqlığını artırmaq üçün güzgülər işlətmək üçün könüllülər axtarırıq. Sizin versiyanız aydın şəkildə güzgü kimi fərqlənir, məsələn, “Bob’s Archive, Anna’s Archive güzgüsü”. Siz bu işlə əlaqəli riskləri qəbul etməyə hazırsınız, bu risklər əhəmiyyətlidir. Əməliyyat təhlükəsizliyini dərindən başa düşürsünüz. <a %(a_shadow)s>bu</a> <a %(a_pirate)s>postların</a> məzmunu sizin üçün aydındır. Əvvəlcə sizə tərəfdaş server yükləmələrinə giriş verməyəcəyik, lakin hər şey yaxşı gedərsə, bunu sizinlə paylaşa bilərik. Siz Anna’s Archive açıq mənbə kod bazasını idarə edirsiniz və mütəmadi olaraq həm kodu, həm də məlumatları yeniləyirsiniz. Siz bu işin baş tutması üçün komandamızla əməkdaşlıq edərək <a %(a_codebase)s>kod bazamıza</a> töhfə verməyə hazırsınız. Biz bunu axtarırıq: Güzgülər: könüllülərə çağırış Başqa bir ianə edin. Hələ bağış yoxdur. <a %(a_donate)s>İlk bağışımı edin.</a> İanə detalları ictimaiyyətə göstərilmir. Bağışlarım 📡 Kolleksiyamızın toplu güzgülənməsi üçün <a %(a_datasets)s>Datasets</a> və <a %(a_torrents)s>Torrents</a> səhifələrinə baxın. Son 24 saat ərzində IP ünvanınızdan yükləmələr: %(count)s. 🚀 Daha sürətli yükləmələr və brauzer yoxlamalarından keçməmək üçün <a %(a_membership)s>üzv olun</a>. Tərəfdaş vebsaytından yüklə Gözləyərkən (əgər brauzeriniz arxa planda yeniləməyi dəstəkləyirsə) Anna’nın Arxivi-ni başqa bir tabda gəzməyə davam edə bilərsiniz. Bir neçə yükləmə səhifəsinin eyni vaxtda yüklənməsini gözləməkdən çəkinməyin (amma zəhmət olmasa hər server üçün eyni vaxtda yalnız bir fayl yükləyin). Yükləmə linkini aldıqdan sonra o, bir neçə saat ərzində etibarlıdır. Gözlədiyiniz üçün təşəkkür edirik, bu, vebsaytı hər kəs üçün pulsuz əlçatan edir! 😊 🔗 Bu fayl üçün bütün yükləmə linkləri: <a %(a_main)s>Faylın əsas səhifəsi</a>. ❌ Yavaş yükləmələr Cloudflare VPN-ləri və ya Cloudflare IP ünvanlarından mümkün deyil. ❌ Yavaş yükləmələr yalnız rəsmi vebsayt vasitəsilə mümkündür. Zəhmət olmasa %(websites)s ziyarət edin. 📚 Yükləmək üçün aşağıdakı URL-dən istifadə edin: <a %(a_download)s>İndi yükləyin</a>. Hər kəsə faylları pulsuz yükləmək imkanı vermək üçün bu faylı yükləməzdən əvvəl gözləməlisiniz. Zəhmət olmasa bu faylı yükləmək üçün <span %(span_countdown)s>%(wait_seconds)s</span> saniyə gözləyin. Xəbərdarlıq: Son 24 saat ərzində IP ünvanınızdan çoxlu yükləmələr olub. Yükləmələr adi haldan daha yavaş ola bilər. VPN, paylaşılan internet bağlantısı və ya ISP-niz IP-ləri paylaşırsa, bu xəbərdarlıq buna görə ola bilər. Yadda saxla Xəta baş verdi. Zəhmət olmasa yenidən cəhd edin. ✅ Saxlanıldı. Zəhmət olmasa səhifəni yenidən yükləyin. Görünən adınızı dəyişin. İdentifikatorunuz ( "#" işarəsindən sonrakı hissə) dəyişdirilə bilməz. Profil yaradıldı <span %(span_time)s>%(time)s</span> redaktə et Siyahılar Faylı tapıb “Siyahılar” sekmesini açaraq yeni siyahı yaradın. Hələ siyahı yoxdur Profil tapılmadı. Profil Hazırda kitab sorğularını qəbul edə bilmirik. Kitab tələblərinizi bizə email vasitəsilə göndərməyin. Z-Library və ya Libgen forumlarında sorğularınızı edin. Anna’nın Arxivində qeyd DOI: %(doi)s Yüklə SciDB Nexus/STC Hələlik önizləmə mövcud deyil. Faylı <a %(a_path)s>Anna’nın Arxivi</a>ndən yükləyin. İnsan biliklərinin əlçatanlığını və uzunmüddətli qorunmasını dəstəkləmək üçün <a %(a_donate)s>üzv</a> olun. Bonus olaraq, 🧬&nbsp;SciDB üzvlər üçün daha sürətli yüklənir, heç bir məhdudiyyət olmadan. İşləmir? <a %(a_refresh)s>Yeniləməyə</a> cəhd edin. Sci-Hub Xüsusi axtarış sahəsi əlavə edin Açıqlamaları və metadata şərhlərini axtarın Nəşr ili İrəli Giriş Məzmun Göstər Siyahı Cədvəl Fayl növü Dil Sıralama Ən böyük Ən uyğun Ən yenilər (fayl ölçüsü) (açıq mənbəli) (nəşr ili) Ən köhnə Təsadüfi Ən kiçik Mənbə AA tərəfindən toplanmış və açıq mənbəli edilmişdir Rəqəmsal Kredit (%(count)s) Jurnal Məqalələri (%(count)s) Biz %(in)s -də uyğunluqlar tapdıq. <a %(a_request)s>fayl tələb edərkən</a> orada tapılan URL-ə müraciət edə bilərsiniz. Metaməlumatlar (%(count)s) Kodlarla axtarış indeksini araşdırmaq üçün <a %(a_href)s>Kodlar Kəşfiyyatçısı</a>ndan istifadə edin. Axtarış indeksi aylıq yenilənir. Hal-hazırda %(last_data_refresh_date)s tarixinə qədər olan qeydləri əhatə edir. Daha texniki məlumat üçün %(link_open_tag)sdatasetlər səhifəsinə</a> baxın. Çıxarın Yalnız daxil edin Yoxlanılmamış daha çox… Növbəti … Əvvəlki Bu axtarış indeksi hazırda Internet Archive-nin İdarə olunan Rəqəmsal Borclanma kitabxanasından metadataları ehtiva edir. <a %(a_datasets)s>Datasets haqqında daha çox</a>. Daha çox rəqəmsal borc kitabxanaları üçün <a %(a_wikipedia)s>Vikipediya</a> və <a %(a_mobileread)s>MobileRead Wiki</a> səhifələrinə baxın. DMCA / müəllif hüquqları iddiaları üçün <a %(a_copyright)s>buraya klikləyin</a>. Yükləmə vaxtı Axtarış zamanı səhv baş verdi. <a %(a_reload)s>Səhifəni yenidən yükləməyə</a> cəhd edin. Problem davam edərsə, zəhmət olmasa bizə %(email)s ünvanına email göndərin. Sürətli yükləmə Əslində, hər kəs <a %(a_torrents)s>birləşdirilmiş torrent siyahımızı</a> paylaşaraq bu faylları qorumağa kömək edə bilər. ➡️ Bəzən bu, axtarış serveri yavaş olduqda səhvən baş verir. Belə hallarda, <a %(a_attrs)s>yenidən yükləmə</a> kömək edə bilər. ❌ Bu faylda problemlər ola bilər. Məqalələr axtarırsınız? Bu axtarış indeksi hazırda müxtəlif metadata mənbələrindən metadata daxildir. <a %(a_datasets)s>Datasets haqqında daha çox</a>. Dünyada yazılı əsərlər üçün çoxlu, çoxlu metadata mənbələri var. <a %(a_wikipedia)s>Bu Vikipediya səhifəsi</a> yaxşı bir başlanğıcdır, lakin başqa yaxşı siyahılar bilirsinizsə, bizə bildirin. Metaməlumatlar üçün biz orijinal qeydləri göstəririk. Qeydlərin birləşdirilməsini etmirik. Hal-hazırda dünyanın ən geniş açıq kitab, məqalə və digər yazılı əsərlər kataloquna sahibik. Biz Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>və daha çoxunu</a> güzgüləyirik. <span class="font-bold">Fayl tapılmadı.</span> Daha az və ya fərqli axtarış terminləri və filtrlərini sınayın. Nəticələr %(from)s-%(to)s (%(total)s cəmi) Əgər güzgüləməli olduğumuz digər “kölgə kitabxanaları” tapsanız və ya hər hansı sualınız varsa, %(email)s ünvanına bizimlə əlaqə saxlayın. %(num)d qismən uyğunluqlar %(num)d+ qismən uyğunluqlar Rəqəmsal borc kitabxanalarında faylları axtarmaq üçün qutuda yazın. Birbaşa yüklənə bilən %(count)s fayllar kataloqumuzda axtarış etmək üçün qutuda yazın, biz onları <a %(a_preserve)s>əbədi qoruyuruq</a>. Axtarış üçün qutuya yazın. Kataloqumuzda olan %(count)s akademik məqalələr və jurnal məqalələrini axtarmaq üçün qutuda yazın, biz onları <a %(a_preserve)s>əbədi qoruyuruq</a>. Kitabxanalardan metadataları axtarmaq üçün qutuda yazın. Bu, <a %(a_request)s>fayl tələb edərkən</a> faydalı ola bilər. İpucu: daha sürətli naviqasiya üçün klaviatura qısayollarından istifadə edin: “/” (axtarış fokus), “enter” (axtarış), “j” (yuxarı), “k” (aşağı), “<” (əvvəlki səhifə), “>” (növbəti səhifə). Bunlar metadata qeydləridir, <span %(classname)s>yüklənə bilən fayllar</span> deyil. Axtarış parametrləri Axtarış Rəqəmsal Kredit Yüklə Jurnal məqalələri Metadata Yeni axtarış %(search_input)s - Axtarış Axtarış çox uzun çəkdi, bu da o deməkdir ki, qeyri-dəqiq nəticələr görə bilərsiniz. Bəzən səhifəni <a %(a_reload)s>yenidən yükləmək</a> kömək edir. Axtarış çox uzun çəkdi, bu geniş sorğular üçün adi haldır. Filtr sayıları dəqiq olmaya bilər. Libgen və ya Z-Library tərəfindən qəbul edilməyən böyük yükləmələr (10,000-dən çox fayl) üçün bizimlə %(a_email)s əlaqə saxlayın. Libgen.li üçün, əvvəlcə <a %(a_forum)s >onların forumuna</a> istifadəçi adı %(username)s və şifrə %(password)s ilə daxil olun və sonra onların <a %(a_upload_page)s >yükləmə səhifəsinə</a> qayıdın. Hələlik, yeni kitabları Library Genesis forklarına yükləməyi təklif edirik. Budur bir <a %(a_guide)s>əlverişli bələdçi</a>. Qeyd edək ki, bu vebsaytda indekslədiyimiz hər iki fork eyni yükləmə sistemindən istifadə edir. Kiçik yükləmələr üçün (10,000 fayla qədər) onları həm %(first)s, həm də %(second)s yükləyin. Alternativ olaraq, onları Z-Kitabxanaya <a %(a_upload)s>buradan</a> yükləyə bilərsiniz. Akademik məqalələri yükləmək üçün (Library Genesis-ə əlavə olaraq) <a %(a_stc_nexus)s>STC Nexus</a>-a da yükləyin. Onlar yeni məqalələr üçün ən yaxşı kölgə kitabxanasıdır. Hələ onları inteqrasiya etməmişik, amma bir nöqtədə edəcəyik. Onların <a %(a_telegram)s>Telegram üzərində yükləmə botundan</a> istifadə edə bilərsiniz və ya çoxlu fayllarınız varsa, onların sabitlənmiş mesajında göstərilən ünvana müraciət edə bilərsiniz. <span %(label)s>Ağır könüllü iş (USD$50-USD$5,000 mükafatlar):</span> əgər missiyamıza çox vaxt və/və ya resurs ayıra bilirsinizsə, sizinlə daha yaxından işləmək istərdik. Nəticədə daxili komandaya qoşula bilərsiniz. Büdcəmiz məhdud olsa da, ən intensiv işlər üçün <span %(bold)s>💰 pul mükafatları</span> verə bilirik. <span %(label)s>Yüngül könüllü iş:</span> əgər yalnız burada və orada bir neçə saat ayıra bilirsinizsə, hələ də kömək edə biləcəyiniz çoxlu yollar var. Davamlı könüllüləri <span %(bold)s>🤝 Anna’s Archive üzvlükləri</span> ilə mükafatlandırırıq. Anna’s Archive sizin kimi könüllülərə güvənir. Biz bütün öhdəlik səviyyələrini qəbul edirik və axtardığımız köməyin iki əsas kateqoriyası var: Əgər vaxtınızı könüllü olaraq ayıra bilmirsinizsə, bizə <a %(a_donate)s>pul bağışlayaraq</a>, <a %(a_torrents)s>torrents-lərimizi paylaşaraq</a>, <a %(a_uploading)s>kitablar yükləyərək</a> və ya <a %(a_help)s>dostlarınıza Anna’s Archive haqqında danışaraq</a> çox kömək edə bilərsiniz. <span %(bold)s>Şirkətlər:</span> kolleksiyalarımıza yüksək sürətli birbaşa giriş təklif edirik, əvəzində isə müəssisə səviyyəsində ianə və ya yeni kolleksiyalar (məsələn, yeni skanlar, OCR edilmiş datasets, məlumatlarımızı zənginləşdirmək) mübadiləsi tələb edirik. Əgər bu sizsinizsə, <a %(a_contact)s>bizimlə əlaqə saxlayın</a>. Həmçinin <a %(a_llm)s>LLM səhifəmizə</a> baxın. Mükafatlar Biz həmişə möhkəm proqramlaşdırma və ya hücum təhlükəsizliyi bacarıqlarına malik insanları cəlb etməyə çalışırıq. Siz insanlığın mirasını qorumaqda ciddi bir töhfə verə bilərsiniz. Təşəkkür olaraq, möhkəm töhfələr üçün üzvlük hədiyyə edirik. Böyük təşəkkür olaraq, xüsusilə vacib və çətin tapşırıqlar üçün pul mükafatları veririk. Bu, iş əvəzi kimi görülməməlidir, lakin əlavə bir təşviqdir və çəkilən xərclərə kömək edə bilər. Kodlarımızın əksəriyyəti açıq mənbəlidir və mükafat verərkən sizin kodunuzun da belə olmasını istəyəcəyik. Bəzi istisnalar var ki, bunları fərdi olaraq müzakirə edə bilərik. Mükafatlar tapşırığı ilk tamamlayan şəxsə verilir. Başqalarına bir şey üzərində işlədiyinizi bildirmək üçün mükafat biletinə şərh yazmaqdan çəkinməyin, beləliklə başqaları gözləyə bilər və ya sizinlə birləşə bilər. Ancaq unutmayın ki, başqaları da üzərində işləməkdə sərbəstdir və sizi keçməyə çalışa bilər. Lakin, biz səthi işlər üçün mükafat vermirik. Yüksək keyfiyyətli iki təqdimat bir-birinə yaxın (bir-iki gün ərzində) edilərsə, öz mülahizəmizə görə hər ikisinə mükafat verə bilərik, məsələn, ilk təqdimat üçün 100%% və ikinci təqdimat üçün 50%% (beləliklə, cəmi 150%%). Daha böyük mükafatlar (xüsusilə scraping mükafatları) üçün, təxminən 5%% tamamladığınız zaman və metodunuzun tam mərhələyə qədər genişlənəcəyinə əmin olduğunuz zaman bizimlə əlaqə saxlayın. Metodunuzu bizimlə paylaşmalısınız ki, biz rəy verə bilək. Həmçinin, bu yolla bir neçə nəfərin mükafata yaxınlaşdığı halda nə edəcəyimizə qərar verə bilərik, məsələn, bir neçə nəfərə mükafat vermək, insanları birləşdirməyə təşviq etmək və s. XƏBƏRDARLIQ: yüksək mükafatlı tapşırıqlar <span %(bold)s>çətindir</span> — daha asan olanlarla başlamaq ağıllı ola bilər. <a %(a_gitlab)s>Gitlab məsələlər siyahısına</a> gedin və “Etiket prioriteti” ilə çeşidləyin. Bu, təxminən bizim üçün vacib olan tapşırıqların sırasını göstərir. Açıq mükafatı olmayan tapşırıqlar hələ də üzvlük üçün uyğun ola bilər, xüsusilə “Qəbul edilmiş” və “Anna’nın sevimlisi” olaraq işarələnmiş olanlar. “Başlanğıc layihəsi” ilə başlamaq istəyə bilərsiniz. Yüngül könüllü iş İndi bizdə %(matrix)s ünvanında sinxronlaşdırılmış Matrix kanalı da var. Əgər bir neçə saat boş vaxtınız varsa, bir çox yolla kömək edə bilərsiniz. <a %(a_telegram)s>Telegram-da könüllülər söhbətinə</a> qoşulmağı unutmayın. Minnətdarlıq əlaməti olaraq, adətən əsas mərhələlər üçün 6 aylıq “Şanslı Kitabxanaçı” veririk və davamlı könüllü iş üçün daha çox veririk. Bütün mərhələlər yüksək keyfiyyətli iş tələb edir — səhlənkar iş bizə köməkdən çox zərər verir və biz onu rədd edəcəyik. Mərhələyə çatdığınız zaman <a %(a_contact)s>bizə e-poçt göndərin</a>. %(links)s yerinə yetirdiyiniz sorğuların linkləri və ya ekran görüntüləri. Z-Library və ya Library Genesis forumlarında kitab (və ya məqalə və s.) tələblərini yerinə yetirmək. Öz kitab tələbi sistemimiz yoxdur, lakin həmin kitabxanaları güzgüləyirik, buna görə də onları yaxşılaşdırmaq Anna’s Archive-ni də yaxşılaşdırır. Mərhələ Tapşırıq Tapşırıqdan asılıdır. <a %(a_telegram)s>Telegram-da könüllülər söhbətində</a> yerləşdirilən kiçik tapşırıqlar. Adətən üzvlük üçün, bəzən kiçik mükafatlar üçün. Könüllü söhbət qrupumuzda yerləşdirilən kiçik tapşırıqlar. Düzəltdiyiniz problemlərə şərh yazdığınızdan əmin olun ki, başqaları işinizi təkrarlamasın. %(links)s təkmilləşdirdiyiniz qeydlərin linkləri. Başlanğıc nöqtəsi kimi <a %(a_list)s >təsadüfi metadata problemləri siyahısından</a> istifadə edə bilərsiniz. Open Library ilə <a %(a_metadata)s>əlaqələndirərək</a> metadata-nı təkmilləşdirin. Bunlar sizə Anna Arxivini kiməsə bildirdiyinizi və onların sizə təşəkkür etdiyini göstərməlidir. %(links)s linklər və ya ekran görüntüləri. Anna Arxivinin sözünü yaymaq. Məsələn, AA-da kitablar tövsiyə etməklə, bloq yazılarımıza keçid verməklə və ya ümumiyyətlə insanları veb saytımıza yönləndirməklə. Bir dili tam tərcümə edin (əgər artıq tamamlanmağa yaxın deyildisə). <a %(a_translate)s>Vebsaytı tərcümə etmək</a>. Əhəmiyyətli töhfələr verdiyinizi göstərən redaktə tarixçəsinə link. Öz dilinizdə Anna’s Archive üçün Wikipedia səhifəsini təkmilləşdirin. Digər dillərdəki AA-nın Wikipedia səhifəsindən, vebsaytımızdan və blogumuzdan məlumat daxil edin. AA-ya digər müvafiq səhifələrdə istinadlar əlavə edin. Könüllülük və Mükafatlar 