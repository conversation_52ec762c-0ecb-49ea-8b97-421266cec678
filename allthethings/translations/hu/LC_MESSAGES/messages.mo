��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ld y  g .   �h 	  �h   �i �  �k �  �m )  2o _   \p v   �p H   3q g   |q �  �q l  �s �   �t �  �u   �w �  �x �  �z   �|   �~   � �  � B   ��   � b   � N  T� '   �� l  ˇ H   8�    ��    ��    �� J   ۊ    &�    D� 2   U� "   �� $   ��    Ћ Q   � K  6� .  �� S   �� @   �    F�    R�    _�    x�    ��    �� 	   ��    ��    ȏ &   Ϗ    ��    �� 	   � 
   "�    0�    <�    U� '   r� �  �� z  H� �   Ô .   y� H  �� �   �   �� �   �� *   }� �   �� *   K� �   v� /   @� 3  p� *   �� �  Ϝ 6   r� [   ��    � �  � 6  �� �  ȡ I  ��    � E    � f   f� �   ͥ 0   � 7   �� �   � T   j� O   �� *   �    :� �   ?� �  Ȩ 6   �� �   Ԫ �   X� \   � B  y� 8   ��   �� �   �� �   �� O   {� �   ˰ �   }� H   � �   [� �   B� Q   � J   ]� |   ��    %� �   4� (  ʵ H   �    <� �   L� �   ��    Ը �  ۸ �   u�    %� �  &� �   ʽ F  ��   � P   � Z   _� U   �� g   � `   x� _   �� 2   9� �   l� a   	� O   k�    �� �   �� f   U� �   �� �   ��    6�   I�   V� H  u� q  �� �   0� �   �� A  �� �   �� �   �� �   j� �  "� �   ��   B�    O� �   [� �   � �   �� �  �� �   5� �   �� �   V�    � �   � V  �� /  ��    � a   1� �   �� Z   J� �  �� �   4� n   �� }   Z�    �� :  �� �   0� �  ��    �� q   �� �    � �   ��   �� �   �� �  �� �  �� -   Y� ]  �� {  �� F  a� �  �� B  ��   �� a   �� ~   7�    �� �   �� S  �� �  �    n �   r �   d    � s  	 �  } (       )	 �   8	 p  �	 �   a ]  / �   � W   }    � �  � �  �    2 �   D :  % A  ` �   � �  � h    P   w   � :   �     J  (   s .  u 1   � H   � %    �   E" w   �" i   E# �  �# (   d%    �% �  �% �   V' �   (( Y   �( �   ) 2   �)   �) �   �*    �+    �+ &    , <   ', ;   d, :   �, `   �, �   <-    �- 5   �- D   . Q   W. -   �. 3   �. +   / �   7/    �/ !   �/ #  0 C  &1 �   j2 �   S3 �   G4 �   �4 e  �5    +7 �   @7 �   8 �   �8 i  V9 �  �: '  T< �  |> �  [@ H   B 8   OB '   �B �   �B ?  nC �  �E J  sG c  �H -  "J n  PK   �M (   �N *  �N ,  (P �  UQ    �R �   
S ?  T z   HU b  �U �   &W 
   X *   *X o   UX    �X �  �X �  �[ �  3] �   �_ �   w`    pa   |a �   �b H   �c n   �c /  @d   pe �   �f �  +g t   �h K  Mi    �j   �j �   �l   �m �  �o �  5r �   )t    �t   �t �  �u    �w b  x �  qy E  a{ T  �}   � T  � H   ]� 
  �� �   �� �   �� O   /� q   � 
   � :   �� $   7� '   \� $   ��    �� �   ��   Y�   y� E  ~� �   ď    �� �  �� m  �� ]  	� �  g� |  �� �  w�    � !  !� 	   C�    M� �  `� o  � �  {� q  n�    � �  � �  ۩ �  ��   �� 1  ��   в �  � %   n� �   �� c   T�   �� �  ˺ �   z� �   � �   �� �  :�    ҿ �   � O   �� `   �    i� 5   z� C   �� i  �� �  ^� �  � C   �� �  � �   ��    � �   �� D   ?� �   �� �   � 1   �� ]   !�    � M  �� O  �� �  -� �  �� {   ��    �� v   � C  ��    �� �   �� �  `� �   4� 5   �� 8   � k  P� .   �� W  �� �  C� �  �� #   �� {   � �   �� w  !� �  ��    C�   T� 1   e� �   �� �   J�    %�   F� V   M� .   ��   �� )   �� �  � y  �� �   Y� �   N� _   I� ;   �� �   �� H  ��   �� �  _� �  [� ;   6  �   r    Y 7  [ �   �   M �  m .   X �  � !   Z
 �  |
 f   ^    � !  � P   �  S   � \     ` p   �   � �  � �  � �   J   � _  �" 2   Z$ �   �$ �   %    �%    �% z  �% �   G' I   �' *  C( �  n) R  [+   �, �  �. �   {0 �  1 2  �2 !   4 �   $4    �4 �  5 4   �6    �6    
7    7 .   )7    X7    k7    �7    �7    �7    �7    �7 
   �7    �7 "   �7 
   8 &   8    98    ?8    P8    W8 �   _8 U   9 !   r9    �9 %   �9 ,   �9 0   �9 +   $: "   P:    s:    �:    �:    �:    �:    �:    �:    �:    �:    �: .   ; #   >;    b; !   ~; %   �; 5   �; 3   �;    0< ,   K< 3   x<    �< M   �< 5   =    E= `   J= B   �=    �=    
> %   >    E>    ]>    u>    �>    �>    �> 
   �>    �>    �>    �> 	   
? 
   ?    ? )   "?    L?    S? 	   \?    f? 	   |?    �?    �?    �? 	   �?    �?    �?    �?    �? #   �?    @    4@    <@ 	   S@    ]@ 9   n@    �@    �@ #   �@    �@    �@    �@    �@    A    A    .A z   HA �   �A h   sB �   �B �  �C |   �E    F    (F    EF    ZF    aF    pF 
   �F $   �F J   �F ;   G R   ?G    �G ;   �G =   �G f   *H {   �H �   
I Y   �I 8   @J &   yJ    �J    �J 
   �J    �J    �J    �J    �J    �J    K 
   K    *K    >K    CK    LK    bK    kK    �K    �K    �K 
   �K 	   �K    �K    �K    �K &  L    2M    8M    GM (   MM    vM P   }M b   �M '   1N =   YN 9   �N    �N    �N    �N �   �N    uO    {O 1   �O �   �O    KP    hP �   xP +   Q j  ;Q c   �T {   
U �   �U �   V -   
W   8W �   NX b  �X �   GZ     3[    T[ O   c[ D   �[ R   �[ a   K\ `   �\ B   ] k   Q] #   �] ,   �]    ^    ^ T   /^ $   �^    �^    �^    �^    �^ s   �^    a_ -   n_ L   �_    �_    ` K   ` c   c` �  �`    jb    �b �   �b    vc    c    �c    �c    �c C   �c p  �c    be    �e    �e    �e k  �e !   g 
   9g    Dg A   Lg    �g    �g +   �g    �g    �g %   �g �   h    �h    �h W   �h    !i    @i    Ti    \i 7   ti g   �i    j ;   +j �   gj t   k X   �k    �k !  �k V   m    lm 4   ~m    �m �   �m �   �n    Ho P   ao �   �o �   Qp    7q '   Qq    yq �  �q !   )s !   Ks    ms %   �s     �s �   �s    �t    �t *   �t F   �t 
   /u    =u    Vu %   tu �  �u t  !x -  �y <   �{ 6   |    8|    E| M  U|   �} �   �~ "   e   � �  �� )   g�    �� N  �� ?  �� 
   8� 
   C� 6   Q� 
   �� a  ��    �� N  � �  a� �   �    � �    � 0   �� ,   ݌ d   
� 8  o�   �� �    �  `� �   �� H  ~� ]   Ǔ �   %� �   � O   �� �   � 2   �� .   ז    � 
   �    � (   9�    b� J   � 5   ʗ 	    � 0   
� E  ;� '   �� �   �� �   �� �   � &   ��    ϛ    �    � &   �     ;� *   \� (   �� �   �� ,   �� �   ��    �� �   �� �   p� w   �� G  v� �   �� @   �� �   ̢ 	   Q� !  [� ~   }�    �� �  �    Ϧ    ܦ    �� )   
� 1   4�    f�    m� h   u� �   ާ   ��    �� 	   ��     �� �   ީ P  Ū �   � {   �    _�    w�    ��    ��    ­    ӭ    � M   � /   :� �  j� h   [�    İ m   ذ h   F� P   �� p    � Y   q� S   ˲    � h   '� K   �� �   ܳ X   u� N   δ    � �   2� c   0� >   �� o   Ӷ \   C� 7   ��    ط 2   � n   � �   �� =   6� �   t�    � <  '� M   d� a   �� �   �    �� �  �� �   ��    z�    ��    ��    �� 3  Ŀ 0   �� e   )� �   �� �   g� �   G� �   �� �   �� �   q� �   (� b   �� 7  ^� c   �� n  �� �   i� 
  5� 
   C� 
   Q� 
   _� �   m� 
   � 
   $� O   2� f   �� �   �� 
   �� �   �� C   n� �   �� <   B� �   � o   � 
   s� �   �� 
   � 
   � 
   +� T  9� �   �� �  (� #   �� 	   #�    -� �   <�     �� %   
�   0� �   I�    ��    � "   !� 8   D� <   }� 9   ��     ��    � �   5�     � �  <�    �� �   �� M   g� �   �� �   f� �  d� i  :� �   �� �   ]� �   ��    �� m  �� %   � �  .� �   �� )  �� �   �� E  [� �   �� �   _� �   �� 2   �� >   ��    �� (   
� 
   3�    >�    K�   `�    t� e   }� ,   �� 
   �    �    +� '   8� �   `� P   �� 8   9� G  r� 5   �� 	   �� 	   �� #   � $   (�    M� 	   _�    i� 	   r�    |� 	   ��    �� 	   �� 8   �� �   ��    |�    �� 
   ��    �� 
   ��    �� 
   ��    ��    ��    �� *   � }   <�    �� :   �� Y   � �   i� �   � �   �� 7  �� �   �� B  ��    �� �   �� >   �� i   �� c   =  �   �  �   e Y   K d   �    
 y    �   � �   !    �    �    �    �     )       < )   D    n    w    � "   �    �    �    �        $    8    H    P    n 2   u 6   � �   � �   h '   � '   " q   J k   � �   (	 #   �	 5   �	 9   

 =   D
 G   �
 2   �
 �   �
   � ;  � '    C   ; �    *    �   I �   8 �    H   �     � �    �   � p   r ,   � n       %   � -   � &   � G    u   ^ y   �    N    V    _ &   f �   � ;   . $   j 1   � #   �     � '    P   .     i   � H      M ?   k G   � �   � F   �    �        -    M    l    �    � 7   � F    �  J M   �  ;   (! @   d! 0   �! 
   �! �   �! ~   k" �   �" �   �# 
   v$ �   �$ +   "%    N% �   f%    T& !   p& O   �& =   �& p    ' \   �' T   �' "   C( )   f( �   �( 1   E) "   w) ^   �)   �) Q   + }   X+ {   �+ ]   R, �   �, '   5- @   ]- v   �-    . p   0. (   �. �   �. <   �/ E   �/    0 %   ,0 �   R0 O   @1 T   �1 �   �1 _   �2 M   �2 �   =3 �   �3    H4    U4 D   i4 U   �4    5    5    =5    N5    b5 /   q5 �   �5 e   (6    �6 "   �6 )   �6 +   �6 5   7 �   U7 G   �7 y   +8 Y   �8 ,   �8 s   ,9 �   �9 4   �: `   �:    6; E   J; I   �;    �; �   �; �   v< 1   �< a   +=    �= 4   �= _   �=    3> �   G> )   �>    �> +   ? _   8? �   �? N   _@    �@    �@ N   �@    +A g   BA F   �A �   �A �   �B    (C N   >C �   �C (   ND �   wD K   ,E "   xE ^   �E 
  �E 	   G    G    G     G 3   4G 4   hG F   �G    �G    H    H -   H A   LH <   �H 	   �H L   �H I   "I    lI    I '   �I $   �I    �I [   �I �   ZJ W   MK 	   �K    �K �   �K P  zL N   �M    N �  'N �  �O 1   [Q �   �Q    R �  /R '   �T s   �T    RU    fU �  yU    kW b   �W �   �W �   �X W   Y    lY k   �Y >   �Y    /Z y   LZ D   �Z 6   [ X   B[ 0   �[ R   �[ �   \ �   �\ 2   t] �   �] �  k^ �   ` %   �` k  �` �   _b �   Bc A  -d �   oe ,   Ef #   rf �   �f 2   @g $  sg �   �i ?   !j    aj    rj �  �j    �l �   �l U  Gm 3  �n -  �o ?   �p K   ?q `   �q 0   �q 0   r W   Nr ]   �r    s %   s B   <s /   s    �s 2   �s |   �s +   yt 
   �t h   �t   u �   v    �v    �v    �v Y   �v a   Lw [   �w �   
x z   �x    `y '   ry �   �y 
   �z �   �z   R{ )  _~ M   � &   �    � 
   �    � J   � A   ^� �   ��   &� U   (�    ~�    �� %   ��    ʂ \   �    E� D   W�    �� O   �� $   �    �    &� x   3�    ��    �� ,   Ǆ    �    �    � �   �� ]   o� g   ͆ c   5� �   �� 
   �� $   �� �   �� �   �� �   W�    � �   +� d   �� N   � t   `� _   Ռ ]   5�    �� j   ��    �    2�    G�    [�    u�    ��    �� '   ��    ׎    � 6   � 2   +� 6   ^� "   �� 7   �� -   ��    �    <�    X� C   j�    ��    ̐ I   ِ ,   #� k   P� '   ��    �    �� +   
� $   9�    ^� n   s� U   � w   8� �   ��    ��    �� &   Д    �� 8   � 	   H�    R�    d� �   z�    
� #   -�    Q� 
   X� 	   c� 7   m�    �� �   ��    ��    ϗ    � #   �� $   #�    H� *   g� -   �� 2   �� 2   � 8   &�    _� d   f� #   ˙    �    	� 8   � L   S�    �� 4   �� ,   �� �   "� W   �� K   ��    G�    O� 	   k�    u�    �� $   �� �  ɜ Y   M�    ��    �� ?       � 5   �    M� 	   R� x   \� E   ՟ �   �    �� !   
� �   /� !   �� &   ӡ     �� #   � ,   ?� ,   l�    ��    �� 8   â '   �� +   $� 5   P� ]  �� S   � O   8� Q   �� "   ڥ �   �� #   ��    �� b   ��    !� +   5�    a� D   z� S   �� %   � �   9�    �    ��     �    2�    L�    i� #   ��    ��    �� [   ԩ &  0�    W� %   v� �   �� �   ��    Z�    z�    ��    ��    ��    ѭ    �    ��    �    '�    :�    I� 
   Z�    h�    w�    ��    ��    ��     �� �   Ӯ *  �� �  ۰ /  r� �  ��   � �  ��    �   �    4� �   J�   8� �  :� �   � �  �� :   O� �   �� F   U�    �� F   �� R   �� u   O� �   �� �   F� �   � �   �� v  ��    -� �   H� �   �� V   �� �   ?�    �� �   �� w  �� �   \� �   (�    � w   � {   �� �   
� y   �� ~   v� �   ��    �� "   ��    �� F   �� *   �    E� �   Q� Q   �� u   7�    �� �   �� �   j� M   �� c   F� U   �� `    � g   a� Z   �� �   $� h   �� �   � �   ��    1� O   9� $   �� ]   �� 6   �    C�    P� Z   X�    ��    ��    �� 3   �� 1   � C   O�    ��    �� 
   ��    �� 	   �� _   �� �   6� Y   �� 9   �    T� '   \� /   ��    ��    �� 
   ��    ��    ��    �� 
   ��     �    � 	   � 
   �    '� 	   7�    A�    P�    g�    z�    �� 	   ��    �� 6   ��    ��    �� �   �    �� j   �� �   �    ��    ��    �� 
   ��    ��    �    � �   � �   �� P   V�    ��    �� �   ��    [� �   l� �   
� !   ��    �� �   �� �   R� c   � �   s� |   n� 1   �� �   �    ��    �� O   �� �   L�    �� �   �� y   �� �   � L   ��    ��    �    � 
   *�    5�    F�    O�    \� �   x� �   
� �   �� �   >�   3� r   9� R   �� �  �� e  �� �   )� �   '� 6  �� g  ��    g� �   p� *  E� �   p� �  � �  �� t   �  �  1    � K   � �   ' {  � D   1 �   v    b    o    w �   � K   $ w   p 0   � `    T   z k   � '   ;	 �   c	 A   
 .   \
 c   �
   �
      blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: hu
Language-Team: hu <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 A Z-Library egy népszerű (és illegális) könyvtár. Átvették a Library Genesis gyűjteményt, és könnyen kereshetővé tették. Ezen felül nagyon hatékonyak lettek az új könyvhozzájárulások ösztönzésében, különféle előnyökkel jutalmazva a hozzájáruló felhasználókat. Jelenleg nem járulnak hozzá ezekkel az új könyvekkel a Library Genesishez. És a Library Genesis-szel ellentétben nem teszik könnyen tükrözhetővé a gyűjteményüket, ami megakadályozza a széles körű megőrzést. Ez fontos az üzleti modelljük szempontjából, mivel pénzt kérnek a gyűjteményük tömeges eléréséért (több mint 10 könyv naponta). Nem teszünk erkölcsi ítéletet az illegális könyvgyűjtemény tömeges hozzáféréséért pénzt kérőkről. Kétségtelen, hogy a Z-Library sikeresen bővítette a tudáshoz való hozzáférést, és több könyvet szerzett be. Mi egyszerűen azért vagyunk itt, hogy a magunk részét tegyük: biztosítsuk ennek a magángyűjteménynek a hosszú távú megőrzését. - Anna és a csapat (<a %(reddit)s>Reddit</a>) A Kalóz Könyvtár Tükör eredeti kiadásában (SZERK: áthelyezve az <a %(wikipedia_annas_archive)s>Anna Archívuma</a> oldalra) tükröztük a Z-Library-t, egy nagy illegális könyvgyűjteményt. Emlékeztetőül, ezt írtuk abban az eredeti blogbejegyzésben: Ez a gyűjtemény 2021 közepére nyúlik vissza. Eközben a Z-Library elképesztő ütemben növekedett: körülbelül 3,8 millió új könyvet adtak hozzá. Vannak benne duplikátumok, de a többségük úgy tűnik, hogy valóban új könyv, vagy korábban beküldött könyvek jobb minőségű szkennelése. Ez nagyrészt a Z-Library önkéntes moderátorainak növekvő számának és a deduplikációval ellátott tömeges feltöltési rendszerüknek köszönhető. Szeretnénk gratulálni nekik ezekhez az eredményekhez. Örömmel jelentjük be, hogy megszereztük az összes könyvet, amelyet a Z-Library-hez adtak hozzá az utolsó tükrünk és 2022 augusztusa között. Visszamentünk és begyűjtöttünk néhány könyvet, amelyet először kihagytunk. Összességében ez az új gyűjtemény körülbelül 24TB, ami sokkal nagyobb, mint az előző (7TB). A tükrünk most összesen 31TB. Ismét deduplikáltunk a Library Genesis ellen, mivel már elérhetők torrentek ehhez a gyűjteményhez. Kérjük, látogasson el a Pirate Library Mirror oldalra, hogy megnézze az új gyűjteményt (SZERK: áthelyezve ide: <a %(wikipedia_annas_archive)s>Anna Archívuma</a>). Ott több információ található arról, hogyan vannak strukturálva a fájlok, és mi változott az utolsó alkalom óta. Innen nem fogunk linkelni, mivel ez csak egy blogoldal, amely nem tárol illegális anyagokat. Természetesen a seedelés is nagyszerű módja annak, hogy segítsen nekünk. Köszönjük mindenkinek, aki seedeli a korábbi torrentjeinket. Hálásak vagyunk a pozitív visszajelzésekért, és örülünk, hogy ennyien törődnek a tudás és kultúra megőrzésével ebben a szokatlan módon. 3x új könyv került hozzáadásra a Kalóz Könyvtár Tükörhöz (+24TB, 3,8 millió könyv) Olvassa el a kísérő cikkeket a TorrentFreak-től: <a %(torrentfreak)s>első</a>, <a %(torrentfreak_2)s>második</a> - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) kísérő cikkek a TorrentFreak-től: <a %(torrentfreak)s>első</a>, <a %(torrentfreak_2)s>második</a> Nem is olyan régen, az „árnyékkönyvtárak” haldokoltak. A Sci-Hub, a hatalmas illegális tudományos papírarchívum, a perek miatt leállt az új művek befogadásával. A „Z-Library”, a legnagyobb illegális könyvtár, állítólagos alkotóit letartóztatták szerzői jogi bűncselekmények miatt. Hihetetlen módon sikerült elkerülniük a letartóztatást, de könyvtáruk továbbra is veszélyben van. Néhány ország már alkalmazza ennek egy változatát. A TorrentFreak <a %(torrentfreak)s>beszámolt</a> arról, hogy Kína és Japán AI kivételeket vezettek be szerzői jogi törvényeikbe. Számunkra nem világos, hogy ez hogyan viszonyul a nemzetközi egyezményekhez, de mindenképpen fedezetet nyújt a hazai cégeiknek, ami megmagyarázza, amit láttunk. Ami az Anna Archívumot illeti — folytatjuk földalatti munkánkat, amely erkölcsi meggyőződésen alapul. Mégis, a legnagyobb vágyunk az, hogy a fénybe lépjünk, és legálisan növeljük hatásunkat. Kérjük, reformálják meg a szerzői jogot. Amikor a Z-Library bezárással nézett szembe, már lementettem az egész könyvtárat, és kerestem egy platformot, ahol elhelyezhetem. Ez volt a motivációm az Anna Archívum elindítására: a korábbi kezdeményezések küldetésének folytatása. Azóta a világ legnagyobb árnyékkönyvtárává nőttünk, több mint 140 millió szerzői joggal védett szöveget tárolva különböző formátumokban — könyvek, tudományos papírok, magazinok, újságok és még sok más. A csapatom és én ideológusok vagyunk. Hisszük, hogy ezeknek a fájloknak a megőrzése és tárolása erkölcsileg helyes. A világ könyvtárai költségvetési megszorításokkal szembesülnek, és az emberiség örökségét sem bízhatjuk a vállalatokra. Aztán jött az AI. Gyakorlatilag minden nagyobb LLM-et építő cég megkeresett minket, hogy az adatainkon képezzék ki magukat. A legtöbb (de nem mind!) amerikai székhelyű cég újragondolta, miután rájött az illegális munkánk természetére. Ezzel szemben a kínai cégek lelkesen fogadták a gyűjteményünket, látszólag nem zavarta őket annak jogszerűsége. Ez figyelemre méltó, tekintve, hogy Kína szinte minden jelentős nemzetközi szerzői jogi egyezmény aláírója. Körülbelül 30 cégnek adtunk nagy sebességű hozzáférést. A legtöbbjük LLM cég, és néhány adatbróker, akik továbbértékesítik a gyűjteményünket. A legtöbbjük kínai, bár dolgoztunk amerikai, európai, orosz, dél-koreai és japán cégekkel is. A DeepSeek <a %(arxiv)s>bevallotta</a>, hogy egy korábbi verzióját a gyűjteményünk egy részén képezték ki, bár az új modelljükről szűkszavúak (valószínűleg azt is az adatainkon képezték ki). Ha a Nyugat meg akarja őrizni előnyét az LLM-ek, és végső soron az AGI versenyében, újra kell gondolnia a szerzői jogi álláspontját, és hamarosan. Akár egyetért velünk az erkölcsi ügyünkben, akár nem, ez most már gazdasági, sőt nemzetbiztonsági kérdéssé válik. Minden hatalmi blokk mesterséges szuper-tudósokat, szuper-hackereket és szuper-katonákat épít. Az információ szabadsága ezeknek az országoknak a túlélés kérdésévé válik — sőt, nemzetbiztonsági kérdéssé. A csapatunk a világ minden tájáról származik, és nincs különösebb irányultságunk. De bátorítanánk az erős szerzői jogi törvényekkel rendelkező országokat, hogy használják ki ezt az egzisztenciális fenyegetést a reform érdekében. Tehát mit tegyünk? Az első ajánlásunk egyszerű: rövidítsük le a szerzői jogi időtartamot. Az Egyesült Államokban a szerzői jogot az író halála után 70 évre adják meg. Ez abszurd. Ezt összhangba hozhatjuk a szabadalmakkal, amelyeket a bejelentés után 20 évre adnak meg. Ez bőven elegendő idő kellene, hogy legyen a könyvek, papírok, zenék, művészetek és más kreatív művek szerzőinek, hogy teljes mértékben kompenzálják erőfeszítéseiket (beleértve a hosszabb távú projekteket, mint például a filmadaptációk). Ezután, legalábbis, a döntéshozóknak be kellene vezetniük kivételeket a szövegek tömeges megőrzésére és terjesztésére. Ha az egyéni ügyfelek elvesztett bevétele a fő aggodalom, a személyes szintű terjesztés továbbra is tiltható maradhat. Cserébe azok, akik képesek kezelni a hatalmas gyűjteményeket — az LLM-eket képző cégek, valamint könyvtárak és más archívumok — ezekkel a kivételekkel lennének fedezve. A szerzői jogi reform szükséges a nemzetbiztonság érdekében. Röviden: A kínai LLM-ek (beleértve a DeepSeek-et) az én illegális könyv- és papírarchívumomon lettek kiképezve — a világ legnagyobb ilyen archívumán. A Nyugatnak át kell alakítania a szerzői jogi törvényeket a nemzetbiztonság érdekében. Kérjük, tekintse meg az <a %(all_isbns)s>eredeti blogbejegyzést</a> további információkért. Kihívást hirdettünk ennek fejlesztésére. Az első helyezettnek 6 000 dolláros, a második helyezettnek 3 000 dolláros, a harmadik helyezettnek pedig 1 000 dolláros díjat ajánlottunk fel. Az elsöprő válaszok és hihetetlen beküldések miatt úgy döntöttünk, hogy kissé növeljük a nyereményalapot, és négy harmadik helyezettnek 500 dollárt ítélünk oda. Az alábbiakban találhatók a nyertesek, de mindenképpen nézze meg az összes beküldést <a %(annas_archive)s>itt</a>, vagy töltse le a <a %(a_2025_01_isbn_visualization_files)s>kombinált torrentünket</a>. Első helyezett 6 000 dollár: phiresky Ez a <a %(phiresky_github)s>beküldés</a> (<a %(annas_archive_note_2951)s>Gitlab megjegyzés</a>) egyszerűen mindent tartalmaz, amit szerettünk volna, és még többet is! Különösen tetszettek a hihetetlenül rugalmas vizualizációs lehetőségek (még egyedi shadereket is támogat), de egy átfogó előre beállított lista is rendelkezésre áll. Emellett tetszett, hogy minden gyors és sima, az egyszerű megvalósítás (amelynek még háttérszervere sincs), az okos minitérkép és a részletes magyarázat a <a %(phiresky_github)s>blogbejegyzésükben</a>. Hihetetlen munka, és megérdemelt győzelem! - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Szívünk tele van hálával. Figyelemre méltó ötletek Felhőkarcolók a ritkaságért Sok csúszka az adatkészletek összehasonlításához, mintha DJ lennél. Skála a könyvek számával. Csinos címkék. Menő alapértelmezett színséma és hőtérkép. Egyedi térképnézet és szűrők Jegyzetek és élő statisztikák is Élő statisztikák Néhány további ötlet és megvalósítás, amelyeket különösen kedveltünk: Még folytathatnánk egy darabig, de itt megállunk. Nézze meg az összes beküldést <a %(annas_archive)s>itt</a>, vagy töltse le a <a %(a_2025_01_isbn_visualization_files)s>kombinált torrentünket</a>. Olyan sok beküldés, és mindegyik egyedi nézőpontot hoz, akár a felhasználói felületben, akár a megvalósításban. Legalább az első helyezett beküldést beépítjük a fő weboldalunkba, és talán néhány másikat is. Elkezdtünk gondolkodni azon is, hogyan szervezzük meg a legritkább könyvek azonosításának, megerősítésének és archiválásának folyamatát. Erről még több információ várható. Köszönjük mindenkinek, aki részt vett. Csodálatos, hogy ennyi embert érdekel. Egyszerű adatkészlet-váltás a gyors összehasonlításokhoz. Minden ISBN CADAL SSNO-k CERLALC adat szivárgás DuXiu SSID-k EBSCOhost eBook Index Google Könyvek Goodreads Internet Archívum ISBNdb ISBN Globális Kiadói Nyilvántartás Libby Fájlok Anna Archívumában Nexus/STC OCLC/Worldcat OpenLibrary Orosz Állami Könyvtár Trantor Birodalmi Könyvtár Második helyezett 3 000 dollár: hypha „Bár a tökéletes négyzetek és téglalapok matematikailag tetszetősek, nem nyújtanak kiváló lokalitást a térképezési kontextusban. Úgy vélem, hogy ezeknek a Hilbert vagy klasszikus Morton görbéknek a benne rejlő aszimmetria nem hiba, hanem jellemző. Ahogy Olaszország híresen csizma alakú körvonala azonnal felismerhetővé teszi a térképen, ezeknek a görbéknek az egyedi „furcsaságai” kognitív tájékozódási pontként szolgálhatnak. Ez a megkülönböztethetőség javíthatja a térbeli memóriát, és segíthet a felhasználóknak tájékozódni, potenciálisan megkönnyítve a konkrét régiók megtalálását vagy minták észlelését.” Egy másik hihetetlen <a %(annas_archive_note_2913)s>beküldés</a>. Nem olyan rugalmas, mint az első helyezett, de valójában jobban tetszett a makroszintű vizualizációja az első helyezettnél (térkitöltő görbe, határok, címkézés, kiemelés, pásztázás és nagyítás). Joe Davis <a %(annas_archive_note_2971)s>megjegyzése</a> különösen megérintett minket: És még mindig rengeteg lehetőség van a vizualizációra és megjelenítésre, valamint egy hihetetlenül sima és intuitív felhasználói felület. Szilárd második helyezés! - Anna és a csapat (<a %(reddit)s>Reddit</a>) Néhány hónappal ezelőtt bejelentettünk egy <a %(all_isbns)s>10 000 dolláros díjat</a> a lehető legjobb vizualizáció elkészítésére, amely bemutatja az ISBN teret. Kiemeltük, hogy mely fájlokat archiváltuk már, és később egy adathalmazt, amely leírja, hány könyvtár tart ISBN-eket (a ritkaság mértéke). Elárasztott minket a válasz. Rengeteg kreativitás volt. Nagy köszönet mindenkinek, aki részt vett: az energiátok és lelkesedésetek ragályos! Végső soron a következő kérdésekre szerettünk volna választ adni: <strong>mely könyvek léteznek a világon, mennyit archiváltunk már, és mely könyvekre kellene legközelebb összpontosítanunk?</strong> Nagyszerű látni, hogy ennyi embert érdekelnek ezek a kérdések. Mi magunk kezdtünk egy alapvető vizualizációval. Kevesebb mint 300 kb-ban ez a kép tömören ábrázolja az emberiség történetének valaha összeállított legnagyobb teljesen nyitott „könyvlistáját”: Harmadik helyezett 500 dollár #1: maxlion Ebben a <a %(annas_archive_note_2940)s>beküldésben</a> különösen tetszettek a különböző nézetek, különösen az összehasonlító és kiadói nézetek. Harmadik helyezett 500 dollár #2: abetusk Bár nem a legcsiszoltabb felhasználói felület, ez a <a %(annas_archive_note_2917)s>beküldés</a> sok szempontból megfelel az elvárásoknak. Különösen tetszett az összehasonlító funkciója. Harmadik helyezett 500 dollár #3: conundrumer0 Mint az első helyezett, ez a <a %(annas_archive_note_2975)s>beküldés</a> is lenyűgözött minket a rugalmasságával. Végső soron ez teszi nagyszerű vizualizációs eszközzé: maximális rugalmasság a haladó felhasználók számára, miközben egyszerű marad az átlagos felhasználók számára. Harmadik helyezett 500 dollár #4: charelf Az utolsó <a %(annas_archive_note_2947)s>beküldés</a>, amely díjat kap, elég alapvető, de van néhány egyedi funkciója, amelyeket nagyon kedveltünk. Tetszett, ahogy megmutatják, hány dataset fedi le egy adott ISBN-t, mint a népszerűség/megbízhatóság mércéjét. Emellett nagyon tetszett az egyszerűség, de hatékonyság, ahogy az átlátszósági csúszkát használják az összehasonlításokhoz. A 10 000 dolláros ISBN vizualizációs díj nyertesei Röviden: Hihetetlen beküldéseket kaptunk a 10 000 dolláros ISBN vizualizációs díjra. Háttér Hogyan tudja Anna Archívuma elérni azt a küldetését, hogy az emberiség összes tudását biztonságba helyezze, anélkül, hogy tudná, mely könyvek vannak még kint? Szükségünk van egy TEENDŐ listára. Az egyik módja ennek feltérképezésére az ISBN számok használata, amelyeket az 1970-es évek óta minden megjelent könyvhöz hozzárendeltek (a legtöbb országban). Nincs központi hatóság, amely ismerné az összes ISBN hozzárendelést. Ehelyett ez egy elosztott rendszer, ahol az országok számköröket kapnak, amelyeket aztán kisebb kiadókhoz rendelnek, akik tovább oszthatják a tartományokat kisebb kiadókhoz. Végül az egyes számokat a könyvekhez rendelik. Két évvel ezelőtt kezdtük el feltérképezni az ISBN-eket <a %(blog)s>az ISBNdb adatbázisának</a> lekaparásával. Azóta sok más metadata forrást is lekapartunk, mint például a <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby és még sok más. A teljes lista megtalálható az Anna Archívum „Datasets” és „Torrents” oldalain. Mostanra messze a legnagyobb, teljesen nyílt, könnyen letölthető könyv metadata (és így ISBN) gyűjteménnyel rendelkezünk a világon. Széleskörűen <a %(blog)s>írtunk</a> arról, miért fontos számunkra a megőrzés, és miért vagyunk jelenleg kritikus időszakban. Most azonosítanunk kell a ritka, kevésbé fókuszált és egyedülállóan veszélyeztetett könyveket, és meg kell őriznünk őket. A világ összes könyvének jó metadata segít ebben. 10 000 dolláros jutalom Erős figyelmet fordítunk a használhatóságra és a megjelenésre. Mutassa meg az egyes ISBN-ek tényleges metadatait, amikor nagyít, például a címet és a szerzőt. Jobb térkitöltő görbe. Például egy cikcakk, amely az első sorban 0-tól 4-ig, majd visszafelé (fordítva) a második sorban 5-től 9-ig halad — rekurzívan alkalmazva. Különböző vagy testreszabható színsémák. Speciális nézetek a Datasets összehasonlításához. Módok a problémák elhárítására, mint például más metadata, amelyek nem egyeznek jól (pl. jelentősen eltérő címek). Képek megjegyzésekkel való ellátása ISBN-ek vagy tartományok megjegyzésével. Bármilyen heurisztika a ritka vagy veszélyeztetett könyvek azonosítására. Bármilyen kreatív ötlet, amit kitalál! Kód Az ezen képek generálásához szükséges kód, valamint más példák megtalálhatók <a %(annas_archive)s>ebben a könyvtárban</a>. Kitaláltunk egy kompakt adatformátumot, amellyel az összes szükséges ISBN információ körülbelül 75MB (tömörítve). Az adatformátum leírása és a generálásához szükséges kód <a %(annas_archive_l1244_1319)s>itt</a> található. A jutalomért nem kötelező ezt használni, de valószínűleg ez a legkényelmesebb formátum a kezdéshez. A metadata-t tetszés szerint átalakíthatja (bár az összes kódjának nyílt forráskódúnak kell lennie). Alig várjuk, hogy lássuk, mit talál ki. Sok sikert! Forkolja ezt a repót, és szerkessze ezt a blogbejegyzés HTML-t (más backendek, mint a Flask backendünk, nem engedélyezettek). Tegye a fenti képet simán nagyíthatóvá, hogy egészen az egyes ISBN-ekig lehessen nagyítani. Az ISBN-ekre kattintva el kell jutnia egy metadata oldalra vagy keresésre az Anna Archívumban. Továbbra is képesnek kell lennie az összes különböző adatbázis közötti váltásra. Az ország- és kiadói tartományokat ki kell emelni, amikor föléjük viszi az egeret. Használhat például <a %(github_xlcnd_isbnlib)s>data4info.py az isbnlib-ben</a> az ország információkhoz, és az „isbngrp” lekaparásunkat a kiadókhoz (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Jól kell működnie asztali és mobil eszközökön is. Sok felfedeznivaló van itt, ezért bejelentünk egy jutalmat a fenti vizualizáció javításáért. A legtöbb jutalmunkkal ellentétben ez időhöz kötött. Nyílt forráskódú kódját 2025-01-31-ig (23:59 UTC) kell <a %(annas_archive)s>benyújtania</a>. A legjobb benyújtás 6 000 dollárt kap, a második helyezett 3 000 dollárt, a harmadik helyezett pedig 1 000 dollárt. Minden jutalmat Monero (XMR) formájában ítélünk oda. Az alábbiakban a minimális kritériumok találhatók. Ha egyetlen benyújtás sem felel meg a kritériumoknak, még mindig odaítélhetünk néhány jutalmat, de ez a mi belátásunk szerint történik. Bónuszpontokért (ezek csak ötletek — engedje szabadjára kreativitását): Teljesen eltérhet a minimális kritériumoktól, és teljesen más vizualizációt készíthet. Ha igazán látványos, akkor az a mi belátásunk szerint jogosult a jutalomra. Küldje el a hozzászólását <a %(annas_archive)s>ehhez a problémához</a> egy linkkel a forked repo-jához, merge kéréséhez vagy diff-jéhez. - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ez a kép 1000×800 pixel méretű. Minden pixel 2500 ISBN-t képvisel. Ha van fájlunk egy ISBN-hez, zöldebbé tesszük azt a pixelt. Ha tudjuk, hogy egy ISBN-t kiadtak, de nincs hozzá illeszkedő fájlunk, pirosabbá tesszük. Kevesebb mint 300 kb-ban ez a kép tömören képviseli az emberiség történetében valaha összeállított legnagyobb teljesen nyitott „könyvlistát” (néhány száz GB teljesen tömörítve). Azt is mutatja: még sok munka van hátra a könyvek mentésében (csak 16% van). Az összes ISBN vizualizálása — 10 000 dolláros jutalom 2025-01-31-ig Ez a kép az emberiség történetében valaha összeállított legnagyobb teljesen nyitott „könyvlista” képviselője. Vizualizálás Az áttekintő képen kívül megtekinthetjük az egyes megszerzett adatbázisokat is. Használja a legördülő menüt és a gombokat a váltáshoz. Sok érdekes mintázatot lehet látni ezekben a képekben. Miért van némi szabályosság a vonalak és blokkok között, ami különböző méretekben látszik? Mik azok az üres területek? Miért vannak bizonyos adatbázisok ennyire csoportosítva? Ezeket a kérdéseket az olvasóra bízzuk. - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Következtetés Ezzel a szabvánnyal fokozatosabban tudunk kiadásokat készíteni, és könnyebben hozzáadhatunk új adatforrásokat. Már van néhány izgalmas kiadásunk a csőben! Reméljük, hogy más árnyékkönyvtárak számára is könnyebbé válik a gyűjteményeink tükrözése. Végül is célunk az emberi tudás és kultúra örök megőrzése, így minél több a redundancia, annál jobb. Példa Nézzük meg a legutóbbi Z-Library kiadásunkat példaként. Két gyűjteményből áll: „<span style="background: #fffaa3">zlib3_records</span>” és „<span style="background: #ffd6fe">zlib3_files</span>”. Ez lehetővé teszi számunkra, hogy külön-külön gyűjtsük és kiadjuk a metadata rekordokat a tényleges könyvfájloktól. Ennek megfelelően két torrentet adtunk ki metadata fájlokkal: Kiadottunk egy sor torrentet bináris adatokat tartalmazó mappákkal is, de csak a „<span style="background: #ffd6fe">zlib3_files</span>” gyűjteményhez, összesen 62-t: A <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> futtatásával láthatjuk, mi van benne: Ebben az esetben ez egy könyv metadata, ahogy azt a Z-Library jelentette. A legfelső szinten csak „aacid” és „metadata” van, de nincs „data_folder”, mivel nincs hozzá tartozó bináris adat. Az AACID „22430000”-t tartalmaz elsődleges azonosítóként, amelyet láthatóan a „zlibrary_id”-ból vettek. Várható, hogy ennek a gyűjteménynek más AAC-jai is hasonló struktúrával rendelkeznek. Most futtassuk a <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> parancsot: Ez egy sokkal kisebb AAC metadata, bár ennek az AAC-nak a nagy része máshol található egy bináris fájlban! Végül is, most van egy „data_folder”, így várható, hogy a hozzá tartozó bináris adat a <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> helyen található. A „metadata” tartalmazza a „zlibrary_id”-t, így könnyen társíthatjuk a megfelelő AAC-hez a „zlib_records” gyűjteményben. Számos különböző módon is társíthattuk volna, például az AACID-n keresztül — a szabvány ezt nem írja elő. Vegye figyelembe, hogy a „metadata” mezőnek nem szükséges JSON-nak lennie. Lehet egy XML-t vagy bármilyen más adatformátumot tartalmazó string is. Akár a metadata információkat is tárolhatja a hozzá tartozó bináris blobban, például ha sok adat van. Heterogén fájlok és metadata, a lehető legközelebb az eredeti formátumhoz. A bináris adatok közvetlenül kiszolgálhatók olyan webszerverek által, mint az Nginx. Heterogén azonosítók a forráskönyvtárakban, vagy akár az azonosítók hiánya. Különálló metadata és fájl adatok kiadása, vagy csak metadata kiadások (pl. ISBNdb kiadásunk). Terjesztés torrenteken keresztül, de más terjesztési módszerek lehetőségével (pl. IPFS). Változatlan rekordok, mivel feltételeznünk kell, hogy a torrentjeink örökké élni fognak. Inkrementális kiadások / bővíthető kiadások. Géppel olvasható és írható, kényelmesen és gyorsan, különösen a mi stackünk számára (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Valamennyire könnyű emberi ellenőrzés, bár ez másodlagos a gépi olvashatósághoz képest. Könnyű a gyűjteményeinket egy szabványos bérelt seedbox-szal feltölteni. Tervezési célok Nem érdekel minket, hogy a fájlok könnyen navigálhatók legyenek manuálisan a lemezen, vagy kereshetők előfeldolgozás nélkül. Nem érdekel minket, hogy közvetlenül kompatibilisek legyünk a meglévő könyvtári szoftverekkel. Bár könnyűnek kell lennie bárki számára, hogy torrentekkel feltöltse a gyűjteményünket, nem várjuk el, hogy a fájlok használhatók legyenek jelentős technikai tudás és elkötelezettség nélkül. Elsődleges felhasználási esetünk a fájlok és a hozzájuk tartozó metadata terjesztése különböző meglévő gyűjteményekből. Legfontosabb szempontjaink: Néhány nem cél: Mivel az Anna Archívum nyílt forráskódú, közvetlenül szeretnénk használni a formátumunkat. Amikor frissítjük a keresési indexünket, csak nyilvánosan elérhető útvonalakat érünk el, hogy bárki, aki fork-olja a könyvtárunkat, gyorsan elindulhasson. <strong>AAC.</strong> Az AAC (Anna Archívuma Konténer) egyetlen elem, amely <strong>metadata</strong>-ból és opcionálisan <strong>bináris adatok</strong>-ból áll, mindkettő változatlan. Globálisan egyedi azonosítóval rendelkezik, amelyet <strong>AACID</strong>-nek hívnak. <strong>AACID.</strong> Az AACID formátuma a következő: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Például egy ténylegesen kiadott AACID: <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID tartomány.</strong> Mivel az AACID-k monoton módon növekvő időbélyegeket tartalmaznak, ezt használhatjuk egy adott gyűjteményen belüli tartományok jelölésére. Ezt a formátumot használjuk: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, ahol az időbélyegek beleértendők. Ez összhangban van az ISO 8601 jelöléssel. A tartományok folytonosak, és átfedhetik egymást, de átfedés esetén azonos rekordokat kell tartalmazniuk, mint a korábban kiadottak az adott gyűjteményben (mivel az AAC-k változatlanok). Hiányzó rekordok nem megengedettek. <code>{collection}</code>: a gyűjtemény neve, amely tartalmazhat ASCII betűket, számokat és aláhúzásokat (de nem dupla aláhúzásokat). <code>{collection-specific ID}</code>: egy gyűjtemény-specifikus azonosító, ha alkalmazható, például a Z-Library ID. Elhagyható vagy csonkolható. El kell hagyni vagy csonkolni kell, ha az AACID egyébként meghaladná a 150 karaktert. <code>{ISO 8601 timestamp}</code>: az ISO 8601 rövidített változata, mindig UTC-ben, például <code>20220723T194746Z</code>. Ennek a számnak minden kiadásnál monoton módon növekednie kell, bár pontos jelentése gyűjteményenként eltérhet. Javasoljuk a kaparás vagy az ID generálásának idejét használni. <code>{shortuuid}</code>: egy UUID, de ASCII-re tömörítve, például base57 használatával. Jelenleg a <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python könyvtárat használjuk. <strong>Bináris adat mappa.</strong> Egy mappa, amely egy adott gyűjtemény AAC tartományának bináris adatait tartalmazza. Ezek a következő tulajdonságokkal rendelkeznek: A könyvtárnak tartalmaznia kell az összes AAC adatfájlját a megadott tartományon belül. Minden adatfájlnak az AACID-jét kell fájlnévként használnia (nincs kiterjesztés). A könyvtár neve egy AACID tartomány kell legyen, amelyet a <code style="color: green">annas_archive_data__</code> előtaggal kell ellátni, és nincs utótag. Például az egyik tényleges kiadásunk egy könyvtár, amelynek neve<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Ajánlott, hogy ezek a mappák kezelhető méretűek legyenek, például ne legyenek nagyobbak 100GB-1TB-nál, bár ez az ajánlás idővel változhat. <strong>Gyűjtemény.</strong> Minden AAC egy gyűjteményhez tartozik, amely definíció szerint egy szemantikailag konzisztens AAC lista. Ez azt jelenti, hogy ha jelentős változtatást hajt végre a metadata formátumán, akkor új gyűjteményt kell létrehoznia. A szabvány <strong>Metadata fájl.</strong> Egy metadata fájl egy adott gyűjtemény AAC tartományának metadataját tartalmazza. Ezek a következő tulajdonságokkal rendelkeznek: <code>data_folder</code> opcionális, és a bináris adatokat tartalmazó mappa neve, amely a megfelelő bináris adatokat tartalmazza. A megfelelő bináris adatok fájlneve ebben a mappában a rekord AACID-je. Minden JSON objektumnak a következő mezőket kell tartalmaznia a legfelső szinten: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcionális). Más mezők nem engedélyezettek. A fájlnévnek egy AACID tartománynak kell lennie, amelyet a <code style="color: red">annas_archive_meta__</code> előtaggal és a <code>.jsonl.zstd</code> utótaggal kell ellátni. Például az egyik kiadásunk neve<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Amint azt a fájlkiterjesztés jelzi, a fájltípus <a %(jsonlines)s>JSON Lines</a>, amely <a %(zstd)s>Zstandard</a>-dal van tömörítve. <code>metadata</code> tetszőleges metadata, a gyűjtemény szemantikája szerint. A gyűjteményen belül szemantikailag konzisztensnek kell lennie. A <code style="color: red">annas_archive_meta__</code> előtagot az intézmény nevéhez lehet igazítani, például <code style="color: red">my_institute_meta__</code>. <strong>„rekordok” és „fájlok” gyűjtemények.</strong> Hagyományosan gyakran kényelmes a „rekordokat” és a „fájlokat” különböző gyűjteményekként kiadni, hogy különböző ütemezések szerint lehessen őket kiadni, például a kaparási sebességek alapján. A „rekord” egy metadata-only gyűjtemény, amely olyan információkat tartalmaz, mint a könyvcímek, szerzők, ISBN-ek stb., míg a „fájlok” azok a gyűjtemények, amelyek magukat a fájlokat tartalmazzák (pdf, epub). Végül egy viszonylag egyszerű szabvány mellett döntöttünk. Ez meglehetősen laza, nem normatív, és folyamatosan fejlődik. <strong>Torrents.</strong> A metadata fájlok és a bináris adatokat tartalmazó mappák torrentekbe csomagolhatók, egy torrent metadata fájlonként vagy egy torrent bináris adatokat tartalmazó mappánként. A torrenteknek az eredeti fájl/könyvtár nevét kell tartalmazniuk, plusz egy <code>.torrent</code> utótagot a fájlnevükben. <a %(wikipedia_annas_archive)s>Anna Archívuma</a> messze a világ legnagyobb árnyékkönyvtárává vált, és az egyetlen ilyen méretű árnyékkönyvtár, amely teljesen nyílt forráskódú és nyílt adatú. Az alábbiakban egy táblázat található a Datasets oldalunkról (kissé módosítva): Ezt három módon értük el: Meglévő nyílt adatú árnyékkönyvtárak tükrözése (mint a Sci-Hub és a Library Genesis). Segítségnyújtás azoknak az árnyékkönyvtáraknak, amelyek nyitottabbá szeretnének válni, de nem volt idejük vagy erőforrásuk erre (mint a Libgen képregénygyűjtemény). Könyvtárak lekaparása, amelyek nem kívánnak tömegesen megosztani (mint a Z-Library). (2) és (3) esetében most már jelentős mennyiségű torrentet kezelünk saját magunk (100 TB-ok). Eddig ezeket a gyűjteményeket egyedi esetekként kezeltük, ami azt jelenti, hogy minden gyűjteményhez külön infrastruktúrát és adatkezelést alakítottunk ki. Ez jelentős többletmunkát jelent minden kiadásnál, és különösen megnehezíti a fokozatos kiadások megvalósítását. Ezért úgy döntöttünk, hogy szabványosítjuk a kiadásainkat. Ez egy technikai blogbejegyzés, amelyben bemutatjuk a szabványunkat: <strong>Anna Archívum Konténerek</strong>. Anna Archívuma Konténerek (AAC): a világ legnagyobb árnyékkönyvtárának kiadásainak szabványosítása Anna Archívuma a világ legnagyobb árnyékkönyvtárává vált, ami szükségessé tette kiadásaink szabványosítását. 300GB+ könyvborító kiadva Végül örömmel jelentjük be egy kis kiadást. A Libgen.rs ágat üzemeltető emberekkel együttműködve megosztjuk az összes könyvborítójukat torrenteken és IPFS-en keresztül. Ez elosztja a borítók megtekintésének terhelését több gép között, és jobban megőrzi őket. Sok esetben (de nem mindegyikben) a könyvborítók magukban a fájlokban is benne vannak, így ez egyfajta „származtatott adat”. De az IPFS-ben való megléte még mindig nagyon hasznos mind az Anna Archívum, mind a különböző Library Genesis ágak napi működéséhez. Mint mindig, ezt a kiadást megtalálhatja a Kalóz Könyvtár Tükörnél (SZERK: áthelyezve <a %(wikipedia_annas_archive)s>Anna Archívum</a>). Itt nem fogunk rá linkelni, de könnyen megtalálhatja. Remélhetőleg kicsit lassíthatunk a tempónkon, most, hogy van egy tisztességes alternatívánk a Z-Library-hez. Ez a munkaterhelés nem különösebben fenntartható. Ha érdekli a programozás, a szerverüzemeltetés vagy a megőrzési munka, mindenképpen vegye fel velünk a kapcsolatot. Még mindig sok <a %(annas_archive)s>munka van hátra</a>. Köszönjük az érdeklődést és a támogatást. Váltás az ElasticSearch-re Néhány lekérdezés szuper hosszú ideig tartott, olyannyira, hogy lefoglalták az összes nyitott kapcsolatot. Alapértelmezés szerint a MySQL-nek van egy minimális szómérete, vagy az indexe nagyon nagy lehet. Az emberek jelentették, hogy nem tudnak keresni „Ben Hur”-ra. A keresés csak akkor volt valamennyire gyors, ha teljesen betöltődött a memóriába, ami megkövetelte, hogy drágább gépet szerezzünk be ennek futtatásához, plusz néhány parancsot az index előzetes betöltéséhez az indításkor. Nem tudtuk volna könnyen kiterjeszteni új funkciók építésére, mint például jobb <a %(wikipedia_cjk_characters)s>tokenizálás a nem szóközös nyelvekhez</a>, szűrés/facettálás, rendezés, „ezt akarta mondani” javaslatok, automatikus kiegészítés stb. Az egyik <a %(annas_archive)s>jegyünk</a> egy csomó probléma volt a keresési rendszerünkkel. MySQL teljes szöveges keresést használtunk, mivel az összes adatunk amúgy is MySQL-ben volt. De ennek megvoltak a korlátai: Miután beszéltünk egy csomó szakértővel, az ElasticSearch mellett döntöttünk. Nem volt tökéletes (az alapértelmezett „ezt akarta mondani” javaslataik és automatikus kiegészítési funkcióik nem jók), de összességében sokkal jobb volt, mint a MySQL a kereséshez. Még mindig nem vagyunk <a %(youtube)s>túl lelkesek</a> arra, hogy bármilyen kritikus adatot használjunk vele (bár sok <a %(elastic_co)s>előrelépést</a> tettek), de összességében elégedettek vagyunk a váltással. Jelenleg sokkal gyorsabb keresést, jobb nyelvi támogatást, jobb relevancia szerinti rendezést, különböző rendezési lehetőségeket és szűrést valósítottunk meg nyelv/könyvtípus/fájltípus szerint. Ha kíváncsi, hogyan működik, <a %(annas_archive_l140)s>nézze</a> <a %(annas_archive_l1115)s>meg</a> <a %(annas_archive_l1635)s>ezt</a>. Elég hozzáférhető, bár elkelne néhány további megjegyzés… Anna Archívuma teljesen nyílt forráskódú Hisszük, hogy az információnak szabadnak kell lennie, és a saját kódunk sem kivétel. Minden kódunkat kiadtuk a saját üzemeltetésű Gitlab példányunkon: <a %(annas_archive)s>Anna Szoftvere</a>. Az issue trackert is használjuk a munkánk szervezésére. Ha részt szeretne venni a fejlesztésünkben, ez egy nagyszerű hely a kezdéshez. Hogy ízelítőt adjunk a munkánkból, nézze meg a legutóbbi munkánkat a kliensoldali teljesítményjavításokon. Mivel még nem valósítottuk meg a lapozást, gyakran nagyon hosszú keresési oldalakat adtunk vissza, 100-200 eredménnyel. Nem akartuk túl korán levágni a keresési eredményeket, de ez azt jelentette, hogy néhány eszközt lelassított. Ehhez egy kis trükköt alkalmaztunk: a legtöbb keresési eredményt HTML megjegyzésekbe (<code><!-- --></code>) csomagoltuk, majd írtunk egy kis Javascriptet, amely érzékeli, mikor kell egy eredménynek láthatóvá válnia, és ekkor kicsomagoljuk a megjegyzést: A DOM „virtualizációja” 23 sorban megvalósítva, nincs szükség bonyolult könyvtárakra! Ez az a fajta gyors, pragmatikus kód, amelyet akkor kapunk, amikor korlátozott idő áll rendelkezésre, és valós problémákat kell megoldani. Jelentették, hogy a keresésünk most már jól működik lassú eszközökön! Egy másik nagy erőfeszítés az adatbázis építésének automatizálása volt. Amikor elindultunk, csak véletlenszerűen összevontunk különböző forrásokat. Most szeretnénk őket naprakészen tartani, ezért írtunk egy csomó szkriptet, hogy letöltsük az új metadata-t a két Library Genesis ágból, és integráljuk őket. A cél nemcsak az, hogy hasznos legyen az archívumunk számára, hanem hogy megkönnyítsük azok számára, akik szeretnének játszani az árnyékkönyvtár metadata-jával. A cél egy Jupyter notebook lenne, amely mindenféle érdekes metadata-t tartalmaz, így több kutatást végezhetünk, például kideríthetjük, hogy <a %(blog)s>az ISBN-ek hány százaléka van örökre megőrizve</a>. Végül átalakítottuk az adományozási rendszerünket. Most már használhat hitelkártyát, hogy közvetlenül pénzt helyezzen el a kripto pénztárcáinkba, anélkül, hogy igazán ismernie kellene a kriptovalutákat. Továbbra is figyelemmel kísérjük, hogy ez mennyire működik a gyakorlatban, de ez nagy dolog. A Z-Library leállása és (állítólagos) alapítóinak letartóztatása miatt éjjel-nappal dolgozunk, hogy jó alternatívát nyújtsunk Anna Archívumával (nem fogjuk itt linkelni, de rákereshet a Google-ben). Íme néhány dolog, amit nemrég elértünk. Anna Frissítése: teljesen nyílt forráskódú archívum, ElasticSearch, 300GB+ könyvborítók Éjjel-nappal dolgozunk, hogy jó alternatívát nyújtsunk Anna Archívumával. Íme néhány dolog, amit nemrég elértünk. Elemzés A szemantikai duplikátumok (ugyanazon könyv különböző szkennelései) elméletileg kiszűrhetők, de ez bonyolult. Amikor manuálisan átnéztük a képregényeket, túl sok hamis pozitívot találtunk. Vannak duplikátumok pusztán MD5 alapján, ami viszonylag pazarló, de ezek kiszűrése csak körülbelül 1% in megtakarítást eredményezne. Ezen a skálán ez még mindig körülbelül 1TB, de ezen a skálán 1TB valójában nem számít. Inkább nem kockáztatnánk, hogy véletlenül adatokat semmisítsünk meg ebben a folyamatban. Találtunk egy csomó nem könyv jellegű adatot, például képregények alapján készült filmeket. Ez is pazarlónak tűnik, mivel ezek már széles körben elérhetők más módon. Azonban rájöttünk, hogy nem tudtuk egyszerűen kiszűrni a filmfájlokat, mivel vannak <em>interaktív képregények</em>, amelyeket számítógépen adtak ki, és valaki felvette és elmentette filmként. Végső soron bármit is törölnénk a gyűjteményből, az csak néhány százalékot takarítana meg. Aztán eszünkbe jutott, hogy mi adatgyűjtők vagyunk, és azok is, akik ezt tükrözni fogják, így hát: „MIT ÉRTESZ AZON, HOGY TÖRÖLJÜK?!” :) Amikor 95TB-ot kap a tároló klaszterébe, megpróbálja megérteni, mi is van benne… Végeztünk némi elemzést, hogy lássuk, csökkenthetjük-e a méretet, például duplikátumok eltávolításával. Íme néhány megállapításunk: Ezért bemutatjuk Önnek a teljes, módosítatlan gyűjteményt. Ez rengeteg adat, de reméljük, hogy elegen fogják érdekelni, hogy megosszák. Együttműködés Méretéből adódóan ez a gyűjtemény régóta szerepel a kívánságlistánkon, így a Z-Library biztonsági mentésének sikerét követően erre a gyűjteményre összpontosítottunk. Eleinte közvetlenül kapartuk le, ami elég nagy kihívás volt, mivel a szerverük nem volt a legjobb állapotban. Így körülbelül 15TB-ot szereztünk meg, de ez lassan haladt. Szerencsére sikerült kapcsolatba lépnünk a könyvtár üzemeltetőjével, aki beleegyezett, hogy közvetlenül küldje el nekünk az összes adatot, ami sokkal gyorsabb volt. Még így is több mint fél évbe telt az összes adat átvitele és feldolgozása, és majdnem elvesztettük az egészet lemezhibák miatt, ami azt jelentette volna, hogy mindent elölről kell kezdenünk. Ez a tapasztalat meggyőzött minket arról, hogy fontos, hogy ezeket az adatokat minél gyorsabban elérhetővé tegyük, hogy széles körben tükrözhetők legyenek. Csak egy-két szerencsétlen időzítésű esemény választ el minket attól, hogy örökre elveszítsük ezt a gyűjteményt! A gyűjtemény A gyors haladás azt jelenti, hogy a gyűjtemény kissé rendezetlen… Nézzük meg. Képzeljük el, hogy van egy fájlrendszerünk (amit valójában torrentek között osztunk meg): Az első könyvtár, <code>/repository</code>, a rendezettebb része ennek. Ez a könyvtár úgynevezett „ezer könyvtárakat” tartalmaz: könyvtárakat, amelyek mindegyike ezer fájlt tartalmaz, amelyek a adatbázisban növekvő számozással vannak ellátva. A <code>0</code> könyvtár a 0–999 comic_id-vel rendelkező fájlokat tartalmazza, és így tovább. Ez ugyanaz a séma, amit a Library Genesis a szépirodalmi és ismeretterjesztő gyűjteményeihez használ. Az ötlet az, hogy minden „ezer könyvtár” automatikusan torrentté alakul, amint megtelik. Azonban a Libgen.li üzemeltetője soha nem készített torrenteket ehhez a gyűjteményhez, így az ezer könyvtárak valószínűleg kényelmetlenné váltak, és helyet adtak a „rendezetlen könyvtáraknak”. Ezek a <code>/comics0</code> és <code>/comics4</code> között találhatók. Mindegyik egyedi könyvtárstruktúrákat tartalmaz, amelyek valószínűleg értelmesek voltak a fájlok gyűjtéséhez, de most már nem sok értelmet nyernek számunkra. Szerencsére a metadata közvetlenül hivatkozik ezekre a fájlokra, így a lemezen való tárolásuk szervezése valójában nem számít! A metadata egy MySQL adatbázis formájában érhető el. Ez közvetlenül letölthető a Libgen.li weboldaláról, de mi is elérhetővé tesszük egy torrentben, a saját táblázatunkkal együtt, amely az összes MD5 hash-t tartalmazza. <q>Dr. Barbara Gordon megpróbál elveszni a könyvtár hétköznapi világában…</q> Libgen elágazások Először is, némi háttérinformáció. Lehet, hogy a Library Genesis-t az epikus könyvgyűjteményéről ismeri. Kevesebben tudják, hogy a Library Genesis önkéntesei más projekteket is létrehoztak, mint például egy jelentős magazin- és szabványdokumentum-gyűjteményt, a Sci-Hub teljes biztonsági mentését (a Sci-Hub alapítójával, Alexandra Elbakyan-nal együttműködve), és valóban, egy hatalmas képregénygyűjteményt. Egy ponton a Library Genesis tükrök különböző üzemeltetői külön utakon indultak el, ami a jelenlegi helyzethez vezetett, hogy több különböző „elágazás” létezik, amelyek mind a Library Genesis nevet viselik. A Libgen.li elágazás egyedülállóan rendelkezik ezzel a képregénygyűjteménnyel, valamint egy jelentős magazin-gyűjteménnyel (amelyen szintén dolgozunk). Adománygyűjtés Ezt az adatot néhány nagy darabban adjuk ki. Az első torrent a <code>/comics0</code>, amelyet egy hatalmas, 12TB-os .tar fájlba tettünk. Ez jobb a merevlemezének és a torrent szoftverének, mint rengeteg kisebb fájl. Ennek a kiadásnak a részeként adománygyűjtést szervezünk. 20 000 dollárt szeretnénk összegyűjteni, hogy fedezzük a gyűjtemény működési és szerződéses költségeit, valamint lehetővé tegyük a folyamatban lévő és jövőbeli projekteket. Néhány <em>hatalmas</em> projekt van készülőben. <em>Kit támogatok az adományommal?</em> Röviden: az emberiség minden tudását és kultúráját archiváljuk, és könnyen hozzáférhetővé tesszük. Minden kódunk és adatunk nyílt forráskódú, teljesen önkéntes alapon működünk, és eddig 125TB könyvet mentettünk meg (a Libgen és a Scihub meglévő torrentjein kívül). Végső soron egy lendkereket építünk, amely lehetővé teszi és ösztönzi az embereket, hogy megtalálják, beszkenneljék és archiválják a világ összes könyvét. A mestertervünkről egy jövőbeli bejegyzésben írunk. :) Ha 12 hónapos „Amazing Archivist” tagságra adományoz ($780), akkor <strong>„örökbe fogadhat egy torrentet”</strong>, ami azt jelenti, hogy a felhasználónevét vagy üzenetét a torrent egyik fájlnevébe tesszük! Adományozhat, ha ellátogat az <a %(wikipedia_annas_archive)s>Anna Archívuma</a> oldalra, és rákattint a „Donate” gombra. További önkénteseket is keresünk: szoftvermérnököket, biztonsági kutatókat, anonim kereskedelmi szakértőket és fordítókat. Támogathat minket tárhelyszolgáltatások biztosításával is. És természetesen, kérjük, ossza meg a torrentjeinket! Köszönet mindenkinek, aki már ilyen nagylelkűen támogatott minket! Valóban különbséget tesznek. Itt vannak az eddig kiadott torrentek (még dolgozunk a többi feldolgozásán): Minden torrent megtalálható az <a %(wikipedia_annas_archive)s>Anna Archívuma</a> alatt a „Datasets” részben (nem linkelünk közvetlenül oda, így a blog linkjei nem kerülnek eltávolításra a Redditről, Twitterről stb.). Innen kövesse a linket a Tor weboldalra. <a %(news_ycombinator)s>Beszélgessen a Hacker News-on</a> Mi következik? Egy csomó torrent nagyszerű a hosszú távú megőrzéshez, de nem annyira a mindennapi hozzáféréshez. Tárhelypartnerekkel fogunk dolgozni azon, hogy az összes adatot feltegyük a webre (mivel Anna Archívuma közvetlenül nem tárol semmit). Természetesen megtalálhatja ezeket a letöltési linkeket Anna Archívumában. Mindenkit meghívunk, hogy tegyen valamit ezekkel az adatokkal! Segítsen nekünk jobban elemezni, deduplikálni, feltenni az IPFS-re, remixelni, AI modelleket tanítani vele, és így tovább. Mindez az Öné, és alig várjuk, hogy lássuk, mit tesz vele. Végül, ahogy korábban mondtuk, még mindig vannak hatalmas kiadások készülőben (ha <em>valaki</em> véletlenül <em>elküldene nekünk egy <em>bizonyos</em> ACS4 adatbázis dumpot, tudja, hol talál meg minket…), valamint a lendkerék építése a világ összes könyvének archiválásához. Tehát maradjon velünk, még csak most kezdünk. - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) A legnagyobb képregény árnyékkönyvtár valószínűleg egy adott Library Genesis ág, a Libgen.li. Az az egyetlen adminisztrátor, aki ezt az oldalt üzemelteti, több mint 2 millió fájlból álló, őrült képregénygyűjteményt gyűjtött össze, összesen több mint 95TB-ot. Azonban, ellentétben más Library Genesis gyűjteményekkel, ez nem volt elérhető tömegesen torrenteken keresztül. Csak egyenként érhették el ezeket a képregényeket a lassú személyes szerverén keresztül — egyetlen hibapont. Egészen a mai napig! Ebben a bejegyzésben többet mesélünk erről a gyűjteményről, és a munkánk támogatására indított adománygyűjtésünkről. Anna Archívuma a világ legnagyobb képregény árnyékkönyvtárát mentette el (95TB) — segíthet a terjesztésben A világ legnagyobb képregény árnyékkönyvtárának egyetlen hibapontja volt... egészen a mai napig. Figyelem: ez a blogbejegyzés elavult. Úgy döntöttünk, hogy az IPFS még nem áll készen a széles körű használatra. Továbbra is linkelünk fájlokat az IPFS-en keresztül Anna Archívumából, amikor lehetséges, de már nem fogjuk magunk hosztolni, és nem is ajánljuk másoknak, hogy tükrözzék az IPFS használatával. Kérjük, tekintse meg a Torrents oldalunkat, ha segíteni szeretne gyűjteményünk megőrzésében. 5 998 794 könyv feltöltése az IPFS-re A példányok megsokszorozása Visszatérve az eredeti kérdésünkre: hogyan állíthatjuk, hogy örökre megőrizzük gyűjteményeinket? A fő probléma itt az, hogy gyűjteményünk <a %(torrents_stats)s>gyorsan növekszik</a>, hatalmas gyűjtemények lekaparásával és nyílt forráskódúvá tételével (a már elvégzett csodálatos munkán felül, amelyet más nyílt adatú árnyékkönyvtárak, mint a Sci-Hub és a Library Genesis végeztek). Ez az adatok növekedése megnehezíti a gyűjtemények világszerte történő tükrözését. Az adatok tárolása drága! De optimisták vagyunk, különösen, ha megfigyeljük a következő három trendet. Gyűjteményeink <a %(annas_archive_stats)s>teljes mérete</a> az elmúlt néhány hónapban, a torrent seederek számának bontásában. A HDD ártrendek különböző forrásokból (kattintson a tanulmány megtekintéséhez). <a %(critical_window_chinese)s>Kínai változat 中文版</a>, beszélgessünk a <a %(reddit)s>Reddit</a>-en, <a %(news_ycombinator)s>Hacker News</a> 1. Leszedtük az alacsonyan lógó gyümölcsöket Ez közvetlenül következik a fentebb tárgyalt prioritásainkból. Előnyben részesítjük a nagy gyűjtemények felszabadítását. Most, hogy biztosítottuk a világ legnagyobb gyűjteményeinek egy részét, várhatóan a növekedésünk sokkal lassabb lesz. Még mindig van egy hosszú sor kisebb gyűjtemény, és minden nap új könyveket szkennelnek vagy adnak ki, de a sebesség valószínűleg sokkal lassabb lesz. Még mindig megduplázódhatunk vagy akár megháromszorozódhatunk, de hosszabb idő alatt. Az OCR fejlesztései. Prioritások Tudományos és mérnöki szoftverkód A fenti kategóriák fiktív vagy szórakoztató változatai Földrajzi adatok (pl. térképek, geológiai felmérések) Vállalati vagy kormányzati belső adatok (szivárgások) Mérési adatok, mint például tudományos mérések, gazdasági adatok, vállalati jelentések Általános metadata rekordok (ismeretterjesztő és szépirodalom; más média, művészet, emberek stb.; beleértve a kritikákat) Ismeretterjesztő könyvek Ismeretterjesztő magazinok, újságok, kézikönyvek Ismeretterjesztő előadások, dokumentumfilmek, podcastok átiratai Szerves adatok, mint DNS-szekvenciák, növényi magvak vagy mikrobiális minták Tudományos cikkek, folyóiratok, jelentések Tudományos és mérnöki weboldalak, online viták Jogi vagy bírósági eljárások átiratai Egyedülállóan veszélyeztetettek a megsemmisülés szempontjából (pl. háború, finanszírozási megszorítások, perek vagy politikai üldöztetés miatt) Ritkák Egyedülállóan alulértékeltek Miért törődünk annyira a tanulmányokkal és könyvekkel? Tegyük félre az általános megőrzés iránti alapvető hitünket — erről talán írunk egy másik bejegyzést. Tehát miért éppen a tanulmányok és könyvek? A válasz egyszerű: <strong>információsűrűség</strong>. Megabájtonként a tárolt írott szöveg tartalmazza a legtöbb információt az összes média közül. Bár mind a tudás, mind a kultúra fontos számunkra, az előbbit fontosabbnak tartjuk. Összességében egy információsűrűségi és megőrzési fontossági hierarchiát találunk, amely nagyjából így néz ki: A lista rangsorolása némileg önkényes — több elem is holtversenyben van, vagy csapatunkon belül nézeteltérések vannak — és valószínűleg elfelejtünk néhány fontos kategóriát. De nagyjából így prioritizálunk. Néhány elem annyira eltér a többitől, hogy nem kell aggódnunk miattuk (vagy más intézmények már gondoskodnak róluk), mint például az organikus adatok vagy földrajzi adatok. De a lista legtöbb eleme valójában fontos számunkra. Egy másik nagy tényező a prioritásainkban az, hogy mennyire van veszélyben egy adott mű. Inkább azokra a művekre összpontosítunk, amelyek: Végül, számunkra fontos a méret. Korlátozott időnk és pénzünk van, így inkább egy hónapot töltünk 10 000 könyv megmentésével, mint 1 000 könyvvel — ha azok nagyjából egyenlő értékűek és veszélyeztetettek. <em><q>Ami elveszett, nem hozható vissza; de mentsük meg, ami megmaradt: nem boltozatokkal és zárakkal, amelyek elzárják őket a nyilvánosság elől és használat elől, az idő pazarlására ítélve őket, hanem a példányok olyan megsokszorozásával, amely a balesetek elérhetetlenségébe helyezi őket.</q></em><br>— Thomas Jefferson, 1791 Árnyékkönyvtárak A kód lehet nyílt forráskódú a Githubon, de a Github egésze nem tükrözhető könnyen, és így nem őrizhető meg (bár ebben az esetben elegendően elosztott példányok vannak a legtöbb kódtárból) A metadata rekordok szabadon megtekinthetők a Worldcat weboldalon, de nem tölthetők le tömegesen (amíg <a %(worldcat_scrape)s>le nem kapartuk</a> őket) A Reddit ingyenesen használható, de nemrégiben szigorú anti-scraping intézkedéseket vezetett be, az adatéhes LLM képzés nyomán (erről később bővebben) Számos szervezet van, amely hasonló küldetéssel és prioritásokkal rendelkezik. Valóban, vannak könyvtárak, archívumok, laboratóriumok, múzeumok és más intézmények, amelyek az ilyen jellegű megőrzésért felelősek. Sokuk jól finanszírozott, kormányok, magánszemélyek vagy vállalatok által. De van egy hatalmas vakfoltjuk: a jogrendszer. Itt rejlik az árnyékkönyvtárak egyedi szerepe, és az oka annak, hogy létezik Anna Archívuma. Olyan dolgokat tudunk megtenni, amelyeket más intézmények nem tehetnek meg. Most, nem (gyakran) arról van szó, hogy olyan anyagokat archiválhatunk, amelyeket máshol illegális megőrizni. Nem, sok helyen legális archívumot építeni bármilyen könyvvel, papírral, magazinnal és így tovább. De amit a jogi archívumok gyakran nélkülöznek, az a <strong>redundancia és a hosszú élettartam</strong>. Léteznek könyvek, amelyekből csak egyetlen példány található valahol egy fizikai könyvtárban. Léteznek metadata rekordok, amelyeket egyetlen vállalat őriz. Léteznek újságok, amelyeket csak mikrofilmen őriznek egyetlen archívumban. A könyvtárak finanszírozását megvághatják, a vállalatok csődbe mehetnek, az archívumokat lebombázhatják és porig égethetik. Ez nem hipotetikus — ez folyamatosan megtörténik. Amit az Anna Archívuma egyedülállóan megtehet, az a művek sok példányának tárolása, nagy mennyiségben. Gyűjthetünk tanulmányokat, könyveket, magazinokat és még sok mást, és ezeket tömegesen terjeszthetjük. Jelenleg ezt torrentek segítségével tesszük, de a pontos technológiák nem számítanak, és idővel változni fognak. A lényeg az, hogy sok példányt terjesszünk világszerte. Ez az idézet több mint 200 évvel ezelőttről még mindig igaz: Egy gyors megjegyzés a közkincsről. Mivel az Anna Archívuma egyedülállóan olyan tevékenységekre összpontosít, amelyek sok helyen illegálisak a világon, nem foglalkozunk széles körben elérhető gyűjteményekkel, mint például a közkincs könyvekkel. A jogi szervezetek gyakran már jól gondoskodnak ezekről. Azonban vannak szempontok, amelyek miatt néha dolgozunk nyilvánosan elérhető gyűjteményeken: - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. A tárolási költségek exponenciálisan csökkennek 3. Az információsűrűség javítása Jelenleg a könyveket abban a nyers formátumban tároljuk, ahogyan megkapjuk őket. Persze, tömörítve vannak, de gyakran még mindig nagy méretű szkennelt vagy fényképezett oldalak. Eddig az egyetlen lehetőség a gyűjteményünk teljes méretének csökkentésére az agresszívebb tömörítés vagy a duplikációk eltávolítása volt. Azonban ahhoz, hogy jelentős megtakarítást érjünk el, mindkettő túl veszteséges a mi ízlésünknek. A fényképek erős tömörítése a szöveget alig olvashatóvá teheti. A duplikációk eltávolítása pedig magas fokú bizalmat igényel abban, hogy a könyvek pontosan ugyanazok, ami gyakran túl pontatlan, különösen, ha a tartalom ugyanaz, de a szkennelések különböző alkalmakkor készültek. Mindig is létezett egy harmadik lehetőség, de a minősége annyira siralmas volt, hogy soha nem vettük figyelembe: <strong>OCR, vagyis optikai karakterfelismerés</strong>. Ez a folyamat a fényképek egyszerű szöveggé alakítását jelenti, az AI segítségével felismerve a fényképeken lévő karaktereket. Az ehhez szükséges eszközök régóta léteznek, és elég jók voltak, de a „elég jó” nem elegendő a megőrzési célokra. Azonban a közelmúltbeli multimodális mélytanulási modellek rendkívül gyors előrelépést tettek, bár még mindig magas költségek mellett. Arra számítunk, hogy a pontosság és a költségek is drámaian javulni fognak az elkövetkező években, olyannyira, hogy reálissá válik az egész könyvtárunkra alkalmazni. Amikor ez bekövetkezik, valószínűleg még mindig megőrizzük az eredeti fájlokat, de emellett lehetne egy sokkal kisebb verziója a könyvtárunknak, amelyet a legtöbben tükrözni szeretnének. A lényeg az, hogy a nyers szöveg önmagában még jobban tömöríthető, és sokkal könnyebb duplikálni, ami még több megtakarítást eredményez. Összességében nem irreális elvárni legalább 5-10-szeres csökkenést a teljes fájlméretben, talán még többet is. Még egy konzervatív 5-szörös csökkenéssel is <strong>1,000–3,000 dollárral számolnánk 10 év alatt, még akkor is, ha a könyvtárunk háromszorosára nőne</strong>. Az írás idején a <a %(diskprices)s>lemezek ára</a> TB-onként körülbelül 12 dollár új lemezek esetén, 8 dollár használt lemezek esetén, és 4 dollár szalag esetén. Ha konzervatívak vagyunk, és csak az új lemezeket nézzük, az azt jelenti, hogy egy petabájt tárolása körülbelül 12 000 dollárba kerül. Ha feltételezzük, hogy könyvtárunk 900 TB-ról 2,7 PB-ra nő, az azt jelentené, hogy 32 400 dollárba kerülne a teljes könyvtárunk tükrözése. Az áram, más hardverek költségeinek hozzáadásával, kerekítsük fel 40 000 dollárra. Vagy szalaggal inkább 15 000–20 000 dollárra. Egyrészt <strong>15 000–40 000 dollár az emberi tudás összességéért egy jó üzlet</strong>. Másrészt kicsit meredek elvárni rengeteg teljes példányt, különösen, ha azt is szeretnénk, hogy ezek az emberek továbbra is seedeljék torrentjeiket mások javára. Ez ma van. De a fejlődés előre halad: A merevlemez költségei TB-onként nagyjából harmadára csökkentek az elmúlt 10 évben, és valószínűleg hasonló ütemben fognak tovább csökkenni. Úgy tűnik, a szalag is hasonló pályán van. Az SSD árak még gyorsabban csökkennek, és az évtized végére átvehetik a HDD árakat. Ha ez így marad, akkor 10 év múlva lehet, hogy csak 5 000–13 000 dollárba kerül majd a teljes gyűjteményünk tükrözése (1/3), vagy még kevesebb, ha kevésbé növekszünk. Bár még mindig sok pénz, ez sok ember számára elérhető lesz. És még jobb lehet a következő pont miatt… Az Anna Archívumánál gyakran kérdezik tőlünk, hogyan állíthatjuk, hogy örökre megőrizzük gyűjteményeinket, amikor a teljes méret már most közelít az 1 Petabyte-hoz (1000 TB), és még mindig növekszik. Ebben a cikkben megvizsgáljuk filozófiánkat, és megnézzük, miért kritikus a következő évtized küldetésünk szempontjából, hogy megőrizzük az emberiség tudását és kultúráját. Kritikus időablak Ha ezek az előrejelzések pontosak, akkor <strong>csak néhány évet kell várnunk</strong>, mielőtt az egész gyűjteményünk széles körben tükrözött lesz. Így, Thomas Jefferson szavaival élve, „a baleset elérhetetlenségébe helyezve”. Sajnos az LLM-ek megjelenése, és az adatéhes képzésük, sok szerzői jogtulajdonost védekező álláspontra helyezett. Még inkább, mint korábban. Sok weboldal nehezebbé teszi a kaparás és archiválás folyamatát, perek repkednek, miközben a fizikai könyvtárak és archívumok továbbra is elhanyagoltak. Csak arra számíthatunk, hogy ezek a trendek tovább romlanak, és sok mű elveszik, mielőtt belépnének a közkincsbe. <strong>A megőrzés forradalmának küszöbén állunk, de <q>az elveszett nem hozható vissza.</q></strong> Körülbelül 5-10 éves kritikus időablakunk van, amely alatt még mindig meglehetősen drága egy árnyékkönyvtár működtetése és sok tükör létrehozása a világ körül, és amely alatt a hozzáférés még nem zárult le teljesen. Ha át tudjuk hidalni ezt az időablakot, akkor valóban megőriztük az emberiség tudását és kultúráját örökre. Nem szabad hagynunk, hogy ez az idő kárba vesszen. Nem szabad hagynunk, hogy ez a kritikus időablak bezáruljon előttünk. Induljunk. Az árnyékkönyvtárak kritikus időszaka Hogyan állíthatjuk, hogy örökre megőrizzük gyűjteményeinket, amikor már most közel 1 PB-ot érnek el? Gyűjtemény Néhány további információ a gyűjteményről. <a %(duxiu)s>Duxiu</a> egy hatalmas adatbázis szkennelt könyvekről, amelyet a <a %(chaoxing)s>SuperStar Digital Library Group</a> hozott létre. Többségük tudományos könyv, amelyeket azért szkenneltek be, hogy digitálisan elérhetővé tegyék az egyetemek és könyvtárak számára. Angolul beszélő közönségünk számára a <a %(library_princeton)s>Princeton</a> és a <a %(guides_lib_uw)s>Washingtoni Egyetem</a> jó áttekintést nyújtanak. Van egy kiváló cikk is, amely további háttérinformációkat ad: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (keresse meg Anna Archívumában). A Duxiu könyveit régóta kalózkodják a kínai interneten. Általában kevesebb mint egy dollárért árulják őket viszonteladók. Jellemzően a Google Drive kínai megfelelőjét használják a terjesztésükre, amelyet gyakran feltörtek, hogy több tárhelyet biztosítsanak. Néhány technikai részlet <a %(github_duty_machine)s>itt</a> és <a %(github_821_github_io)s>itt</a> található. Bár a könyveket félig nyilvánosan terjesztették, meglehetősen nehéz őket nagy mennyiségben megszerezni. Ez magas prioritású volt a teendőink listáján, és több hónapnyi teljes munkaidős munkát szántunk rá. Azonban nemrég egy hihetetlen, csodálatos és tehetséges önkéntes jelentkezett nálunk, aki elmondta, hogy már elvégezte ezt a munkát — nagy költséggel. Megosztotta velünk a teljes gyűjteményt, anélkül, hogy bármit is elvárt volna cserébe, kivéve a hosszú távú megőrzés garanciáját. Igazán figyelemre méltó. Beleegyezett, hogy ilyen módon kérjen segítséget a gyűjtemény OCR-ezéséhez. A gyűjtemény 7 543 702 fájlból áll. Ez több, mint a Library Genesis nem-fikciós része (kb. 5,3 millió). A teljes fájlméret körülbelül 359TB (326TiB) jelenlegi formájában. Nyitottak vagyunk más javaslatokra és ötletekre. Csak vegye fel velünk a kapcsolatot. Nézze meg Anna Archívumát további információkért a gyűjteményeinkről, megőrzési erőfeszítéseinkről és arról, hogyan segíthet. Köszönjük! Példalapok Hogy bebizonyítsa nekünk, hogy jó folyamatot használ, itt van néhány példalap, amellyel elkezdheti a munkát, egy szupravezetőkről szóló könyvből. A folyamatának megfelelően kell kezelnie a matematikai képleteket, táblázatokat, diagramokat, lábjegyzeteket stb. Küldje el a feldolgozott oldalakat az e-mail címünkre. Ha jól néznek ki, továbbiakat küldünk Önnek privátban, és elvárjuk, hogy gyorsan tudja futtatni a folyamatát azokon is. Amint elégedettek vagyunk, megköthetjük az üzletet. - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kínai verzió 中文版</a>, <a %(news_ycombinator)s>Vita a Hacker News-on</a> Ez egy rövid blogbejegyzés. Olyan céget vagy intézményt keresünk, amely segítene nekünk az OCR és szövegkinyerés terén egy hatalmas gyűjteményhez, amelyet megszereztünk, cserébe exkluzív korai hozzáférésért. Az embargó időszak után természetesen az egész gyűjteményt kiadjuk. A kiváló minőségű tudományos szövegek rendkívül hasznosak az LLM-ek képzéséhez. Bár gyűjteményünk kínai, ez mégis hasznos lehet az angol LLM-ek képzéséhez: a modellek úgy tűnik, hogy a fogalmakat és a tudást a forrásnyelvtől függetlenül kódolják. Ehhez a szöveget ki kell nyerni a szkennelt anyagokból. Mit nyer Anna Archívuma ebből? A könyvek teljes szövegű kereshetőségét a felhasználói számára. Mivel céljaink összhangban vannak az LLM fejlesztők céljaival, együttműködőt keresünk. Hajlandóak vagyunk <strong>exkluzív korai hozzáférést biztosítani ehhez a gyűjteményhez nagy mennyiségben 1 évig</strong>, ha megfelelő OCR-t és szövegkinyerést tud végezni. Ha hajlandó megosztani velünk a teljes kódját a folyamatáról, hajlandóak lennénk hosszabb ideig embargó alatt tartani a gyűjteményt. Exkluzív hozzáférés az LLM cégek számára a világ legnagyobb kínai ismeretterjesztő könyvgyűjteményéhez <em><strong>Röviden:</strong> Anna Archívuma megszerezte a világ legnagyobb, 7,5 millió / 350TB méretű kínai ismeretterjesztő könyvgyűjteményét — nagyobbat, mint a Library Genesis. Hajlandóak vagyunk egy LLM cégnek exkluzív hozzáférést biztosítani, cserébe magas minőségű OCR és szövegkinyerésért.</em> Rendszerarchitektúra Tegyük fel, hogy talált néhány céget, amely hajlandó hosztolni a weboldalát anélkül, hogy leállítanák — nevezzük ezeket „szabadságszerető szolgáltatóknak” 😄. Gyorsan rá fog jönni, hogy mindent velük hosztolni meglehetősen drága, így lehet, hogy találni akar néhány „olcsó szolgáltatót”, és ott végezni a tényleges hosztolást, a szabadságszerető szolgáltatókon keresztül proxyzva. Ha jól csinálja, az olcsó szolgáltatók soha nem fogják tudni, mit hosztol, és soha nem kapnak panaszokat. Mindezekkel a szolgáltatókkal fennáll annak a kockázata, hogy mégis leállítják, ezért redundanciára is szükség van. Erre a halmazunk minden szintjén szükség van. Egy viszonylag szabadságszerető cég, amely érdekes helyzetbe hozta magát, a Cloudflare. Ők <a %(blog_cloudflare)s>érveltek</a> azzal, hogy nem hosztolási szolgáltatók, hanem egy közmű, mint egy ISP. Ezért nem vonatkoznak rájuk a DMCA vagy más eltávolítási kérelmek, és továbbítják a kérelmeket a tényleges hosztolási szolgáltatójához. Olyan messzire mentek, hogy bíróság elé vitték ezt a struktúrát, hogy megvédjék. Ezért használhatjuk őket egy másik gyorsítótárazási és védelmi rétegként. A Cloudflare nem fogad el névtelen fizetéseket, így csak az ingyenes csomagjukat használhatjuk. Ez azt jelenti, hogy nem használhatjuk a terheléselosztási vagy hibaátvételi funkcióikat. Ezért <a %(annas_archive_l255)s>ezt magunk valósítottuk meg</a> a domain szinten. Az oldal betöltésekor a böngésző ellenőrzi, hogy az aktuális domain még elérhető-e, és ha nem, átírja az összes URL-t egy másik domainre. Mivel a Cloudflare sok oldalt gyorsítótáraz, ez azt jelenti, hogy a felhasználó a fő domainünkre érkezhet, még akkor is, ha a proxy szerver le van állva, majd a következő kattintáskor átkerülhet egy másik domainre. Még mindig vannak normál működési aggályaink, mint például a szerverek egészségének figyelése, a háttér- és előtérhibák naplózása stb. A hibaátvételi architektúránk ezen a téren is nagyobb robusztusságot tesz lehetővé, például azáltal, hogy egy teljesen más szervercsoportot futtatunk az egyik domainen. Még a kód és a Datasets régebbi verzióit is futtathatjuk ezen a külön domainen, arra az esetre, ha a fő verzióban egy kritikus hiba észrevétlen maradna. A Cloudflare ellen is védekezhetünk, ha eltávolítjuk az egyik domainről, például erről a külön domainről. Ezeknek az ötleteknek különböző permutációi lehetségesek. Következtetés Érdekes tapasztalat volt megtanulni, hogyan állítsunk fel egy robusztus és ellenálló árnyékkönyvtár keresőmotort. Rengeteg részlet van még, amit későbbi bejegyzésekben megoszthatok, szóval jelezze, ha van valami, amiről többet szeretne megtudni! Mint mindig, adományokat keresünk ennek a munkának a támogatására, ezért mindenképpen nézze meg az Adományozás oldalt Anna Archívumában. Más típusú támogatást is keresünk, például támogatásokat, hosszú távú szponzorokat, magas kockázatú fizetési szolgáltatókat, esetleg még (ízléses!) hirdetéseket is. Ha pedig szeretné hozzájárulni idejét és képességeit, mindig keresünk fejlesztőket, fordítókat és így tovább. Köszönjük érdeklődését és támogatását. Innovációs zsetonok Kezdjük a technológiai halmazunkkal. Szándékosan unalmas. Flasket, MariaDB-t és ElasticSearch-t használunk. Ez szó szerint ennyi. A keresés nagyrészt megoldott probléma, és nem szándékozunk újra feltalálni. Emellett az <a %(mcfunley)s>innovációs zsetonjainkat</a> másra kell költenünk: arra, hogy ne zárjanak be minket a hatóságok. Mennyire legális vagy illegális pontosan Anna Archívuma? Ez nagyrészt a joghatóságtól függ. A legtöbb ország hisz valamilyen formában a szerzői jogban, ami azt jelenti, hogy embereknek vagy cégeknek kizárólagos monopóliumot adnak bizonyos típusú művekre egy bizonyos időtartamra. Mellékesen megjegyezve, az Anna Archívumánál úgy gondoljuk, hogy bár vannak előnyei, összességében a szerzői jog negatív hatással van a társadalomra — de ez egy másik történet. Ez a kizárólagos monopólium bizonyos művekre azt jelenti, hogy illegális bárkinek ezen monopóliumon kívül közvetlenül terjeszteni ezeket a műveket — beleértve minket is. De Anna Archívuma egy keresőmotor, amely nem közvetlenül terjeszti ezeket a műveket (legalábbis nem a nyílt weboldalunkon), tehát rendben kellene lennünk, igaz? Nem egészen. Sok joghatóságban nemcsak a szerzői joggal védett művek terjesztése illegális, hanem az is, ha olyan helyekre mutatunk, amelyek ezt teszik. Ennek klasszikus példája az Egyesült Államok DMCA törvénye. Ez a spektrum legszigorúbb vége. A spektrum másik végén elméletileg lehetnek országok, ahol egyáltalán nincsenek szerzői jogi törvények, de ezek valójában nem léteznek. Gyakorlatilag minden országnak van valamilyen szerzői jogi törvénye. A végrehajtás más kérdés. Sok olyan ország van, ahol a kormányok nem törődnek a szerzői jogi törvények betartatásával. Vannak olyan országok is, amelyek a két véglet között helyezkednek el, amelyek tiltják a szerzői joggal védett művek terjesztését, de nem tiltják az ilyen művekre mutató linkek elhelyezését. Egy másik szempont a vállalati szint. Ha egy vállalat olyan joghatóságban működik, amely nem törődik a szerzői jogokkal, de maga a vállalat nem hajlandó kockázatot vállalni, akkor lehet, hogy leállítják a weboldalát, amint valaki panaszt tesz róla. Végül, egy nagy szempont a fizetések. Mivel névteleneknek kell maradnunk, nem használhatunk hagyományos fizetési módszereket. Ez a kriptovalutákra hagy minket, és csak egy kis részhalmaza a cégeknek támogatja ezeket (vannak virtuális betéti kártyák, amelyeket kriptovalutával fizetnek, de ezeket gyakran nem fogadják el). - Anna és a csapat (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Én működtetem <a %(wikipedia_annas_archive)s>Anna Archívumát</a>, a világ legnagyobb nyílt forráskódú non-profit keresőmotorját az <a %(wikipedia_shadow_library)s>árnyékkönyvtárak</a> számára, mint például a Sci-Hub, a Library Genesis és a Z-Library. Célunk, hogy a tudást és a kultúrát könnyen elérhetővé tegyük, és végső soron egy közösséget építsünk, amely együtt archiválja és megőrzi <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>a világ összes könyvét</a>. Ebben a cikkben megmutatom, hogyan működtetjük ezt a weboldalt, és azokat az egyedi kihívásokat, amelyek egy kétes jogi státuszú weboldal működtetésével járnak, mivel nincs „AWS az árnyék jótékonysági szervezetek számára”. <em>Tekintse meg a testvércikket is: <a %(blog_how_to_become_a_pirate_archivist)s>Hogyan váljunk kalóz archivistává</a>.</em> Hogyan működtessünk egy árnyékkönyvtárat: műveletek Anna Archívumában Nincs <q>AWS az árnyék jótékonysági szervezetek számára,</q> így hogyan működtetjük Anna Archívumát? Eszközök Alkalmazásszerver: Flask, MariaDB, ElasticSearch, Docker. Fejlesztés: Gitlab, Weblate, Zulip. Szerverkezelés: Ansible, Checkmk, UFW. Onion statikus tárhely: Tor, Nginx. Proxy szerver: Varnish. Nézzük meg, milyen eszközöket használunk mindehhez. Ez nagyon is változik, ahogy új problémákkal találkozunk és új megoldásokat találunk. Vannak döntések, amelyeken sokat gondolkodtunk. Az egyik ilyen a szerverek közötti kommunikáció: korábban Wireguardot használtunk erre, de azt tapasztaltuk, hogy időnként leáll az adatátvitel, vagy csak egy irányba történik. Ez több különböző Wireguard beállítással is előfordult, amelyeket kipróbáltunk, mint például a <a %(github_costela_wesher)s>wesher</a> és a <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Próbáltuk az SSH-n keresztüli porttovábbítást is, autossh és sshuttle használatával, de <a %(github_sshuttle)s>problémákba ütköztünk</a> (bár még mindig nem világos számomra, hogy az autossh szenved-e a TCP-over-TCP problémáktól vagy sem — csak úgy érzem, hogy ez egy ügyetlen megoldás, de lehet, hogy valójában rendben van?). Ehelyett visszatértünk a szerverek közötti közvetlen kapcsolatokhoz, elrejtve, hogy egy szerver olcsó szolgáltatóknál fut, IP-szűréssel a UFW segítségével. Ennek az a hátránya, hogy a Docker nem működik jól a UFW-vel, hacsak nem használja a <code>network_mode: "host"</code> beállítást. Mindez kicsit hibára hajlamosabb, mert egy apró hibás konfigurációval kiteszi a szerverét az internetnek. Talán vissza kellene térnünk az autossh-hoz — itt szívesen fogadnánk visszajelzéseket. A Varnish és az Nginx között is sokat vacilláltunk. Jelenleg a Varnish-t kedveljük, de vannak furcsaságai és éles szélei. Ugyanez vonatkozik a Checkmk-ra: nem szeretjük, de egyelőre működik. A Weblate rendben van, de nem lenyűgöző — néha attól tartok, hogy elveszíti az adataimat, amikor megpróbálom szinkronizálni a git repónkkal. A Flask összességében jó volt, de vannak furcsa hibái, amelyek sok időt vettek igénybe a hibakeresés során, például az egyedi domainek konfigurálása, vagy a SqlAlchemy integrációjával kapcsolatos problémák. Eddig a többi eszköz nagyszerű volt: nincs komoly panaszunk a MariaDB, az ElasticSearch, a Gitlab, a Zulip, a Docker és a Tor kapcsán. Mindegyiknél voltak problémák, de semmi túl komoly vagy időigényes. Közösség Az első kihívás talán meglepő lehet. Ez nem technikai probléma, vagy jogi probléma. Ez egy pszichológiai probléma: az árnyékban végzett munka hihetetlenül magányos lehet. Attól függően, hogy mit tervez, és milyen fenyegetési modellje van, nagyon óvatosnak kell lennie. A spektrum egyik végén olyan emberek vannak, mint Alexandra Elbakyan*, a Sci-Hub alapítója, aki nagyon nyíltan beszél tevékenységeiről. De nagy a kockázata annak, hogy letartóztatják, ha most egy nyugati országba látogatna, és évtizedekig tartó börtönbüntetéssel nézhet szembe. Vállalná ezt a kockázatot? Mi a spektrum másik végén vagyunk; nagyon óvatosak vagyunk, hogy ne hagyjunk nyomot, és erős operatív biztonsággal rendelkezünk. * Ahogy a HN-en "ynno" említette, Alexandra kezdetben nem akart ismert lenni: "A szerverei úgy voltak beállítva, hogy részletes PHP hibaüzeneteket adjanak ki, beleértve a hibás forrásfájl teljes útvonalát, amely a /home/<USER>" Tehát használjon véletlenszerű felhasználóneveket azokon a számítógépeken, amelyeket erre a célra használ, arra az esetre, ha valamit rosszul konfigurálna. Ez a titoktartás azonban pszichológiai költséggel jár. A legtöbb ember szereti, ha elismerik a munkáját, és mégis nem kaphat elismerést ezért a valós életben. Még az egyszerű dolgok is kihívást jelenthetnek, például amikor a barátok megkérdezik, mivel foglalkozott (egy idő után az "a NAS / homelabommal babrálok" kifárad). Ezért olyan fontos, hogy találjon valamilyen közösséget. Feladhat némi operatív biztonságot azzal, hogy megbízik néhány nagyon közeli barátjában, akikről tudja, hogy mélyen megbízhat bennük. Még akkor is legyen óvatos, hogy ne írjon le semmit, arra az esetre, ha át kellene adniuk az e-mailjeiket a hatóságoknak, vagy ha az eszközeik valamilyen más módon kompromittálódnak. Még jobb, ha talál néhány kalóztársat. Ha a közeli barátai érdeklődnek, hogy csatlakozzanak Önhöz, nagyszerű! Ellenkező esetben találhat másokat online. Sajnos ez még mindig egy rétegközösség. Eddig csak néhány másik aktív személyt találtunk ezen a területen. Jó kiindulópontok lehetnek a Library Genesis fórumok és az r/DataHoarder. Az Archive Team is hasonló gondolkodású egyéneket tartalmaz, bár ők a törvény keretein belül működnek (még ha a törvény szürke területein is). A hagyományos "warez" és kalóz jelenetek is tartalmaznak olyan embereket, akik hasonló módon gondolkodnak. Nyitottak vagyunk ötletekre, hogyan lehetne közösséget építeni és ötleteket felfedezni. Nyugodtan írjon nekünk Twitteren vagy Redditen. Talán szervezhetnénk valamilyen fórumot vagy csevegőcsoportot. Az egyik kihívás az, hogy ez könnyen cenzúrázható, ha közös platformokat használunk, így magunknak kellene üzemeltetnünk. Van egy kompromisszum is aközött, hogy ezeket a beszélgetéseket teljesen nyilvánossá tesszük (több potenciális elköteleződés), vagy priváttá tesszük (nem engedjük, hogy a potenciális "célpontok" tudják, hogy éppen le akarjuk őket kaparni). Ezt át kell gondolnunk. Tudassa velünk, ha érdekli ez! Következtetés Remélhetőleg ez hasznos lesz az újonnan induló kalóz archivisták számára. Izgatottan várjuk, hogy üdvözölhessünk ebben a világban, ezért ne habozz kapcsolatba lépni velünk. Őrizzük meg a világ tudását és kultúráját, és tükrözzük azt minél szélesebb körben. Projektek 4. Adatválasztás Gyakran a metadata segítségével meghatározhat egy ésszerű adathalmazt a letöltéshez. Még ha végül az összes adatot le is szeretné tölteni, hasznos lehet először a legfontosabb elemeket előtérbe helyezni, arra az esetre, ha észlelnék, és javítanák a védelmet, vagy mert több lemezt kellene vásárolnia, vagy egyszerűen azért, mert valami más közbejön az életében, mielőtt mindent letölthetne. Például egy gyűjtemény több kiadást is tartalmazhat ugyanabból az alapvető forrásból (például egy könyv vagy film), ahol az egyik a legjobb minőségűként van megjelölve. Ezeknek a kiadásoknak az elsődleges mentése sok értelmet nyerne. Végül lehet, hogy az összes kiadást el szeretné menteni, mivel egyes esetekben a metadata helytelenül lehet címkézve, vagy ismeretlen kompromisszumok lehetnek a kiadások között (például a "legjobb kiadás" lehet, hogy a legtöbb szempontból a legjobb, de más szempontból rosszabb, például egy film magasabb felbontású, de hiányoznak a feliratok). A metadata adatbázisában is kereshet érdekes dolgokat. Mi a legnagyobb fájl, amelyet tárolnak, és miért olyan nagy? Mi a legkisebb fájl? Vannak érdekes vagy váratlan minták bizonyos kategóriák, nyelvek stb. esetében? Vannak duplikált vagy nagyon hasonló címek? Vannak minták arra vonatkozóan, hogy mikor adták hozzá az adatokat, például egy nap, amikor sok fájlt egyszerre adtak hozzá? Gyakran sokat tanulhat az adathalmaz különböző módokon történő vizsgálatával. Esetünkben a Z-Library könyveket deduplikáltuk a Library Genesis md5 hash-ei ellen, így sok letöltési időt és lemezterületet takarítottunk meg. Ez azonban egy elég egyedi helyzet. A legtöbb esetben nincsenek átfogó adatbázisok arról, hogy mely fájlokat őrizték már meg megfelelően a kalózok. Ez önmagában hatalmas lehetőség valakinek odakint. Nagyszerű lenne, ha lenne egy rendszeresen frissített áttekintés az olyan dolgokról, mint a zene és a filmek, amelyek már széles körben elérhetők torrent weboldalakon, és ezért alacsonyabb prioritásúak a kalóz tükrökbe való felvételhez. 6. Terjesztés Ön rendelkezik az adatokkal, így valószínűleg a világ első kalóz tükrét birtokolja a célpontjának. Sok szempontból a legnehezebb rész véget ért, de a legkockázatosabb rész még előtte áll. Végül is eddig rejtve maradt; a radar alatt repült. Csak annyit kellett tennie, hogy jó VPN-t használt végig, nem töltötte ki személyes adatait semmilyen űrlapon (duh), és esetleg egy speciális böngésző munkamenetet (vagy akár egy másik számítógépet) használt. Most el kell osztania az adatokat. Esetünkben először vissza akartuk adni a könyveket a Library Genesis-nek, de gyorsan felfedeztük ennek nehézségeit (fikció vs. nem fikció rendezés). Ezért a Library Genesis-stílusú torrentekkel való terjesztés mellett döntöttünk. Ha lehetősége van hozzájárulni egy meglévő projekthez, az sok időt takaríthat meg. Azonban jelenleg nem sok jól szervezett kalóz tükör létezik. Tegyük fel, hogy úgy dönt, hogy saját maga terjeszti a torrenteket. Próbálja meg kicsi fájlokat tartani, hogy könnyen tükrözhetők legyenek más weboldalakon. Ezután magának kell seedelnie a torrenteket, miközben továbbra is névtelen marad. Használhat VPN-t (porttovábbítással vagy anélkül), vagy fizethet tumbled Bitcoinokkal egy Seedboxért. Ha nem tudja, mit jelentenek ezek a kifejezések, akkor sok olvasnivalója lesz, mivel fontos, hogy megértse a kockázati kompromisszumokat. A torrent fájlokat magukat meglévő torrent weboldalakon is tárolhatja. Esetünkben úgy döntöttünk, hogy valójában egy weboldalt is üzemeltetünk, mivel világosan szeretnénk terjeszteni a filozófiánkat. Ezt ön is megteheti hasonló módon (mi a Njallát használjuk a domainjeinkhez és a tárhelyünkhöz, tumbled Bitcoinokkal fizetve), de nyugodtan vegye fel velünk a kapcsolatot, hogy mi tároljuk a torrentjeit. Idővel átfogó indexet szeretnénk építeni a kalóz tükrökről, ha ez az ötlet elterjed. Ami a VPN kiválasztását illeti, erről már sokat írtak, így csak megismételjük az általános tanácsot, hogy válasszon hírnév alapján. A tényleges bíróság által tesztelt, hosszú múltra visszatekintő, a magánélet védelmét biztosító naplómentes politikák a legkisebb kockázatú lehetőség, véleményünk szerint. Vegye figyelembe, hogy még ha mindent jól csinál, soha nem érheti el a nulla kockázatot. Például, amikor a torrentjeit seedeli, egy erősen motivált nemzetállami szereplő valószínűleg megvizsgálhatja a VPN szerverek bejövő és kimenő adatfolyamait, és kiderítheti, ki ön. Vagy egyszerűen csak elronthat valamit. Valószínűleg már mi is elrontottuk, és újra el fogjuk. Szerencsére a nemzetállamok nem törődnek <em>annyira</em> a kalózkodással. Minden projekt esetében döntést kell hozni arról, hogy ugyanazzal az identitással publikálja-e, mint korábban, vagy sem. Ha ugyanazt a nevet használja, akkor a korábbi projektek operatív biztonsági hibái visszaüthetnek. De ha különböző neveken publikál, akkor nem épít hosszabb távú hírnevet. Mi úgy döntöttünk, hogy erős operatív biztonsággal kezdünk, hogy továbbra is ugyanazt az identitást használhassuk, de nem habozunk más néven publikálni, ha hibázunk, vagy ha a körülmények ezt megkövetelik. A szó terjesztése trükkös lehet. Ahogy mondtuk, ez még mindig egy rétegzett közösség. Eredetileg a Redditen posztoltunk, de igazán a Hacker News-on kaptunk lendületet. Jelenleg azt javasoljuk, hogy posztolja néhány helyen, és nézze meg, mi történik. És ismét, vegye fel velünk a kapcsolatot. Szeretnénk terjeszteni a kalóz archiválási erőfeszítések hírét. 1. Dómen kiválasztása / filozófia Nincs hiány a megmentendő tudásból és kulturális örökségből, ami nyomasztó lehet. Ezért gyakran hasznos egy pillanatra megállni és átgondolni, hogy mi lehet a hozzájárulása. Mindenkinek más a gondolkodásmódja erről, de itt van néhány kérdés, amit feltehet magának: Esetünkben különösen fontos volt számunkra a tudomány hosszú távú megőrzése. Tudtunk a Library Genesisről, és arról, hogy sokszor teljesen tükrözték torrentek segítségével. Imádtuk ezt az ötletet. Aztán egy nap, egyikünk megpróbált néhány tudományos tankönyvet találni a Library Genesisen, de nem találta őket, ami kétségbe vonta, mennyire teljes is valójában. Ezután online kerestük ezeket a tankönyveket, és más helyeken találtuk meg őket, ami elültette a projektünk magját. Még mielőtt tudtunk volna a Z-Libraryről, már megvolt az ötletünk, hogy nem próbáljuk meg manuálisan összegyűjteni az összes könyvet, hanem a meglévő gyűjtemények tükrözésére összpontosítunk, és visszajuttatjuk őket a Library Genesishez. Milyen készségei vannak, amelyeket a javára fordíthat? Például, ha online biztonsági szakértő, akkor találhat módokat az IP blokkok legyőzésére a biztonságos célpontok esetében. Ha nagyszerű a közösségek szervezésében, akkor talán össze tud gyűjteni néhány embert egy cél köré. Hasznos, ha tud egy kis programozást, ha csak azért is, hogy a folyamat során jó operatív biztonságot tartson fenn. Mi lenne egy nagy hatású terület, amire összpontosíthatna? Ha X órát fog kalóz archiválásra fordítani, akkor hogyan érheti el a legnagyobb "hasznot"? Milyen egyedi módon gondolkodik erről? Lehet, hogy van néhány érdekes ötlete vagy megközelítése, amit mások esetleg kihagytak. Mennyi ideje van erre? A tanácsunk az lenne, hogy kezdjen kicsiben, és ahogy belejön, végezzen nagyobb projekteket, de ez mindent felemészthet. Miért érdekli ez? Mi az, amiért szenvedélyesen rajong? Ha össze tudnánk gyűjteni egy csomó embert, akik mind azokat a dolgokat archiválják, amikért különösen érdeklődnek, az sok mindent lefedne! Sokkal többet fog tudni az átlagembernél a szenvedélyéről, például hogy mi az a fontos adat, amit meg kell menteni, mik a legjobb gyűjtemények és online közösségek, és így tovább. 3. Metadata kaparás Hozzáadás/módosítás dátuma: így később visszatérhetsz és letöltheted azokat a fájlokat, amelyeket korábban nem töltöttél le (bár gyakran az azonosítót vagy a hash-t is használhatod erre). Hash (md5, sha1): hogy megerősítsd, hogy megfelelően letöltötted a fájlt. Azonosító: lehet valamilyen belső azonosító, de az ISBN vagy DOI azonosítók is hasznosak. Fájlnév / hely Leírás, kategória, címkék, szerzők, nyelv, stb. Méret: hogy kiszámolhasd, mennyi lemezterületre van szükséged. Lépjünk egy kicsit technikaibb szintre. A weboldalakról való metadata kaparáshoz egyszerűen tartottuk a dolgokat. Python szkripteket használunk, néha curl-t, és egy MySQL adatbázist az eredmények tárolására. Nem használtunk semmilyen bonyolult kaparó szoftvert, amely képes feltérképezni összetett weboldalakat, mivel eddig csak egy vagy kétféle oldalt kellett lekaparnunk, egyszerűen azonosítók felsorolásával és a HTML elemzésével. Ha nincsenek könnyen felsorolható oldalak, akkor szükséged lehet egy megfelelő feltérképezőre, amely megpróbálja megtalálni az összes oldalt. Mielőtt elkezdenél egy egész weboldalt lekaparni, próbáld meg egy kicsit manuálisan. Menj végig néhány tucat oldalon magad, hogy érzékeld, hogyan működik ez. Néha már így is belefuthatsz IP blokkokba vagy más érdekes viselkedésbe. Ugyanez vonatkozik az adatkaparásra: mielőtt túl mélyen belemerülnél ebbe a célpontba, győződj meg róla, hogy valóban hatékonyan le tudod tölteni az adatait. A korlátozások megkerülésére van néhány dolog, amit kipróbálhatsz. Vannak más IP címek vagy szerverek, amelyek ugyanazt az adatot tárolják, de nem rendelkeznek ugyanazokkal a korlátozásokkal? Vannak API végpontok, amelyek nem rendelkeznek korlátozásokkal, míg mások igen? Milyen letöltési sebességnél blokkolják az IP-det, és mennyi ideig? Vagy nem blokkolnak, hanem lassítanak? Mi van, ha létrehozol egy felhasználói fiókot, hogyan változnak a dolgok akkor? Használhatod a HTTP/2-t a kapcsolatok nyitva tartására, és ez növeli-e az oldalak kérésének sebességét? Vannak oldalak, amelyek egyszerre több fájlt listáznak, és az ott felsorolt információ elegendő-e? A dolgok, amiket valószínűleg el akarsz menteni, a következők: Ezt általában két szakaszban végezzük. Először letöltjük a nyers HTML fájlokat, általában közvetlenül a MySQL-be (hogy elkerüljük a sok kis fájlt, amiről lentebb még beszélünk). Ezután egy külön lépésben átnézzük ezeket a HTML fájlokat, és tényleges MySQL táblákba elemezzük őket. Így nem kell mindent újra letöltened, ha hibát találsz az elemző kódodban, mivel egyszerűen újra feldolgozhatod a HTML fájlokat az új kóddal. Gyakran könnyebb a feldolgozási lépést párhuzamosítani is, így időt takaríthatsz meg (és megírhatod a feldolgozó kódot, miközben a kaparás fut, ahelyett, hogy mindkét lépést egyszerre kellene megírnod). Végül vegye figyelembe, hogy egyes célpontok esetében csak a metadata kaparás áll rendelkezésre. Vannak hatalmas metadata gyűjtemények, amelyek nincsenek megfelelően megőrizve. Cím Dómen kiválasztása / filozófia: Hol szeretne nagyjából fókuszálni, és miért? Milyen egyedi szenvedélyei, készségei és körülményei vannak, amelyeket a javára fordíthat? Cél kiválasztása: Melyik konkrét gyűjteményt fogja tükrözni? Metadata kaparás: Információk katalogizálása a fájlokról, anélkül, hogy ténylegesen letöltenénk a (gyakran sokkal nagyobb) fájlokat. Adat kiválasztása: A metadata alapján szűkítve, hogy mely adatok a legrelevánsabbak az archiváláshoz most. Lehet, hogy minden, de gyakran van ésszerű módja a hely és a sávszélesség megtakarításának. Adat kaparás: Az adatok tényleges megszerzése. Terjesztés: Csomagolás torrentekbe, bejelentés valahol, emberek bevonása a terjesztésbe. 5. Adatkaparás Most készen áll arra, hogy ténylegesen tömegesen letöltse az adatokat. Ahogy korábban említettük, ezen a ponton már manuálisan le kellett töltenie egy csomó fájlt, hogy jobban megértse a célpont viselkedését és korlátozásait. Azonban még mindig lesznek meglepetések, amikor egyszerre sok fájlt kezd letölteni. A tanácsunk itt főként az, hogy tartsa egyszerűen. Kezdje azzal, hogy csak letölt egy csomó fájlt. Használhatja a Pythont, majd bővítheti több szálra. De néha még egyszerűbb, ha közvetlenül a adatbázisból generál Bash fájlokat, majd több terminál ablakban futtatja őket a skálázás érdekében. Egy gyors technikai trükk, amit érdemes megemlíteni, az OUTFILE használata a MySQL-ben, amelyet bárhol írhat, ha letiltja a "secure_file_priv" beállítást a mysqld.cnf-ben (és győződjön meg róla, hogy letiltja/felülírja az AppArmort, ha Linuxot használ). Az adatokat egyszerű merevlemezeken tároljuk. Kezdje azzal, amije van, és lassan bővítse. Elképesztő lehet gondolni arra, hogy több száz TB adatot tároljon. Ha ez a helyzet, amellyel szembesül, először csak egy jó részhalmazt tegyen ki, és a bejelentésében kérjen segítséget a többi tárolásához. Ha mégis több merevlemezt szeretne beszerezni, akkor az r/DataHoarder jó forrásokat kínál a jó ajánlatok megszerzéséhez. Próbáljon meg nem túl sokat aggódni a bonyolult fájlrendszerek miatt. Könnyű beleesni abba a csapdába, hogy olyan dolgokat állítson be, mint a ZFS. Egy technikai részlet, amire érdemes figyelni, hogy sok fájlrendszer nem kezeli jól a sok fájlt. Egyszerű megoldásként több könyvtár létrehozását találtuk, például különböző ID tartományok vagy hash előtagok számára. Az adatok letöltése után győződjön meg a fájlok integritásáról a metadata hash-ek segítségével, ha elérhető. 2. Cél kiválasztása Hozzáférhető: nem használ rengeteg védelmi réteget, hogy megakadályozza a metadata és az adatok lekaparását. Különleges betekintés: van valami különleges információd erről a célpontról, például valahogy különleges hozzáférésed van ehhez a gyűjteményhez, vagy rájöttél, hogyan lehet legyőzni a védelmüket. Ez nem kötelező (a közelgő projektünk nem csinál semmi különlegeset), de mindenképpen segít! Nagy Tehát megvan a terület, amit vizsgálunk, most melyik konkrét gyűjteményt tükrözzük? Van néhány dolog, ami jó célponttá teszi: Amikor a tudományos tankönyveinket a Library Genesisen kívüli weboldalakon találtuk meg, megpróbáltuk kideríteni, hogyan kerültek fel az internetre. Ekkor találtuk meg a Z-Library-t, és rájöttünk, hogy bár a legtöbb könyv nem ott jelenik meg először, végül mégis ott kötnek ki. Megismertük a kapcsolatát a Library Genesis-szel, valamint a (pénzügyi) ösztönző struktúrát és a kiváló felhasználói felületet, amelyek mindegyike sokkal teljesebb gyűjteménnyé tette. Ezután végeztünk némi előzetes metadata és adatkaparást, és rájöttünk, hogy meg tudjuk kerülni az IP letöltési korlátozásokat, kihasználva egyik tagunk különleges hozzáférését sok proxy szerverhez. Amikor különböző célpontokat vizsgálsz, már fontos, hogy elrejtsd a nyomaidat VPN-ek és eldobható e-mail címek használatával, amiről később még beszélünk. Egyedi: más projektek által még nem jól lefedett. Amikor egy projektet végzünk, annak több fázisa van: Ezek nem teljesen független fázisok, és gyakran a későbbi fázisokból származó felismerések visszavezetnek egy korábbi fázisba. Például, a metadata kaparás során rájöhet, hogy a kiválasztott célpontnak olyan védelmi mechanizmusai vannak, amelyek meghaladják a képességeit (például IP blokkok), így visszatér és másik célpontot keres. - Anna és a csapat (<a %(reddit)s>Reddit</a>) Teljes könyveket lehetne írni a digitális megőrzés <em>miértjéről</em> általában, és a kalózarchivizmusról különösen, de adjunk egy gyors bevezetőt azoknak, akik nem túl ismerősek. A világ több tudást és kultúrát termel, mint valaha, de több is vész el belőle, mint valaha. Az emberiség nagyrészt olyan vállalatokra bízza ezt az örökséget, mint az akadémiai kiadók, streaming szolgáltatók és közösségi média cégek, és gyakran nem bizonyultak nagyszerű gondnokoknak. Nézze meg a Digital Amnesia dokumentumfilmet, vagy bármelyik Jason Scott előadást. Vannak intézmények, amelyek jó munkát végeznek, hogy minél többet archiváljanak, de a törvény köti őket. Kalózként egyedülálló helyzetben vagyunk, hogy olyan gyűjteményeket archiváljunk, amelyeket ők nem érinthetnek, a szerzői jogi végrehajtás vagy más korlátozások miatt. Továbbá többször is tükrözhetjük a gyűjteményeket világszerte, ezáltal növelve a megfelelő megőrzés esélyeit. Egyelőre nem fogunk belemenni az értelmi tulajdon előnyeinek és hátrányainak, a törvénysértés erkölcsének, a cenzúra merengetésének vagy a tudáshoz és kultúrához való hozzáférés kérdésének megvitatásába. Mindezeket félretéve, merüljünk el a <em>hogyanban</em>. Megosztjuk, hogyan vált csapatunk kalózarchivistává, és milyen tanulságokat tanultunk meg az út során. Sok kihívás van, amikor elindul ezen az úton, és remélhetőleg segíthetünk néhányukon keresztül. Hogyan válhat kalózarchivistává Az első kihívás talán meglepő lehet. Ez nem technikai probléma, vagy jogi probléma. Ez egy pszichológiai probléma. Mielőtt belevágnánk, két frissítés a Pirate Library Mirror-ról (SZERK: áthelyezve ide: <a %(wikipedia_annas_archive)s>Anna Archívuma</a>): Kaptunk néhány rendkívül nagylelkű adományt. Az első 10 ezer dollár volt egy névtelen személytől, aki szintén támogatta a "bookwarrior"-t, a Library Genesis eredeti alapítóját. Külön köszönet bookwarrior-nak az adomány közvetítéséért. A második egy másik 10 ezer dolláros adomány volt egy névtelen adományozótól, aki az utolsó kiadásunk után lépett kapcsolatba velünk, és inspirálódott, hogy segítsen. Számos kisebb adományt is kaptunk. Nagyon köszönjük a nagylelkű támogatást. Van néhány izgalmas új projektünk a csőben, amelyeket ez támogatni fog, így maradjanak velünk. Néhány technikai nehézségünk volt a második kiadásunk méretével, de a torrentjeink most már elérhetők és seedelhetők. Egy névtelen személytől is kaptunk egy nagylelkű ajánlatot, hogy seedelje a gyűjteményünket a nagyon nagy sebességű szervereiken, így külön feltöltést végzünk az ő gépeikre, ami után mindenki más, aki letölti a gyűjteményt, jelentős sebességnövekedést tapasztalhat. Blogbejegyzések Szia, Anna vagyok. Én hoztam létre <a %(wikipedia_annas_archive)s>Anna Archívumát</a>, a világ legnagyobb árnyékkönyvtárát. Ez az én személyes blogom, amelyben én és csapattársaim a kalózkodásról, a digitális megőrzésről és még sok másról írunk. Kapcsolódjon velem a <a %(reddit)s>Redditen</a>. Vegye figyelembe, hogy ez a weboldal csak egy blog. Csak a saját szavainkat tároljuk itt. Nem tárolunk vagy linkelünk torrenteket vagy más szerzői joggal védett fájlokat. <strong>Könyvtár</strong> - Mint a legtöbb könyvtár, elsősorban az írott anyagokra, például könyvekre összpontosítunk. Lehet, hogy a jövőben más típusú médiákra is kiterjesztjük tevékenységünket. <strong>Tükör</strong> - Szigorúan a meglévő könyvtárak tükre vagyunk. A megőrzésre összpontosítunk, nem pedig a könyvek könnyű kereshetőségére és letölthetőségére (hozzáférés) vagy egy nagy közösség kialakítására, amely új könyveket ad hozzá (forrás). <strong>Kalóz</strong> - Szándékosan megsértjük a szerzői jogi törvényeket a legtöbb országban. Ez lehetővé teszi számunkra, hogy olyasmit tegyünk, amit a jogi személyek nem tehetnek: biztosítjuk, hogy a könyvek széles körben tükröződjenek. <em>Nem linkelünk a fájlokra ebből a blogból. Kérjük, találja meg önmaga.</em> - Anna és a csapat (<a %(reddit)s>Reddit</a>) Ez a projekt (SZERK: áthelyezve ide: <a %(wikipedia_annas_archive)s>Anna Archívuma</a>) célja az emberi tudás megőrzéséhez és felszabadításához való hozzájárulás. Kicsi és szerény hozzájárulásunkat tesszük, a nagy elődök nyomdokain haladva. A projekt fókuszát a neve is tükrözi: Az első könyvtár, amelyet tükröztünk, a Z-Library. Ez egy népszerű (és illegális) könyvtár. Átvették a Library Genesis gyűjteményét, és könnyen kereshetővé tették. Ezen felül nagyon hatékonyak lettek az új könyvek hozzájárulásának ösztönzésében, különféle előnyökkel jutalmazva a hozzájáruló felhasználókat. Jelenleg nem járulnak hozzá ezekkel az új könyvekkel vissza a Library Genesishez. És a Library Genesis-szel ellentétben nem teszik könnyen tükrözhetővé a gyűjteményüket, ami megakadályozza a széles körű megőrzést. Ez fontos az üzleti modelljük szempontjából, mivel pénzt kérnek a gyűjteményük tömeges eléréséért (több mint 10 könyv naponta). Nem teszünk erkölcsi ítéletet az illegális könyvgyűjtemény tömeges hozzáféréséért pénzt kérőkről. Kétségtelen, hogy a Z-Library sikeresen bővítette a tudáshoz való hozzáférést, és több könyvet szerzett be. Mi egyszerűen azért vagyunk itt, hogy a magunk részét tegyük: biztosítsuk ennek a magángyűjteménynek a hosszú távú megőrzését. Szeretnénk meghívni Önt, hogy segítsen megőrizni és felszabadítani az emberi tudást azáltal, hogy letölti és seedeli a torrentjeinket. További információkért arról, hogyan van a data szervezve, látogassa meg a projekt oldalát. Nagyon szívesen fogadjuk az ötleteit is arról, hogy mely gyűjteményeket tükrözzük legközelebb, és hogyan tegyük ezt. Együtt sokat elérhetünk. Ez csak egy kis hozzájárulás a számtalan másik között. Köszönjük mindazt, amit tesz. Bemutatjuk a Kalóz Könyvtár Tükröt: 7TB könyv megőrzése (amelyek nincsenek a Libgenben) 10% of az emberiség írott öröksége örökre megőrizve <strong>Google.</strong> Végül is, ők végezték ezt a kutatást a Google Books számára. Azonban a metadata nem érhető el tömegesen, és meglehetősen nehéz lekaparni. <strong>Különböző egyedi könyvtári rendszerek és archívumok.</strong> Vannak könyvtárak és archívumok, amelyeket a fentiek egyike sem indexelt és aggregált, gyakran azért, mert alulfinanszírozottak, vagy más okokból nem akarják megosztani adataikat az Open Library-val, az OCLC-vel, a Google-lal stb. Sok ilyen rendelkezik digitális rekordokkal, amelyek elérhetők az interneten keresztül, és gyakran nem nagyon jól védettek, így ha segíteni szeretne, és szórakozni szeretne furcsa könyvtári rendszerek megismerésével, ezek nagyszerű kiindulópontok. <strong>ISBNdb.</strong> Ez a blogbejegyzés témája. Az ISBNdb különböző weboldalakról gyűjt könyv metadata-t, különösen árazási adatokat, amelyeket aztán könyvkereskedőknek értékesítenek, hogy könyveiket a piaci árakhoz igazítva árazhassák. Mivel az ISBN-ek manapság meglehetősen univerzálisak, gyakorlatilag „weboldalt építettek minden könyvnek”. <strong>Open Library.</strong> Ahogy korábban említettük, ez az egész küldetésük. Hatalmas mennyiségű könyvtári adatot szereztek be együttműködő könyvtáraktól és nemzeti archívumoktól, és folytatják ezt. Önkéntes könyvtárosokkal és egy technikai csapattal is rendelkeznek, akik megpróbálják deduplikálni a rekordokat, és mindenféle metadata-val címkézni őket. A legjobb az egészben, hogy az adathalmazuk teljesen nyitott. Egyszerűen <a %(openlibrary)s>letöltheti</a>. <strong>WorldCat.</strong> Ez egy weboldal, amelyet a non-profit OCLC működtet, amely könyvtári menedzsment rendszereket értékesít. Könyv metadata-t gyűjtenek sok könyvtárból, és elérhetővé teszik a WorldCat weboldalon keresztül. Azonban pénzt is keresnek az adatok értékesítésével, így nem érhetők el tömeges letöltésre. Vannak azonban korlátozottabb tömeges adathalmazok, amelyek letölthetők, együttműködésben bizonyos könyvtárakkal. 1. Valamilyen ésszerű "örökké" definíció szerint. ;) 2. Természetesen az emberiség írott öröksége sokkal több, mint könyvek, különösen manapság. E bejegyzés és legutóbbi kiadásaink kedvéért a könyvekre összpontosítunk, de érdeklődésünk ennél tovább terjed. 3. Aaron Swartzról sokkal többet lehetne mondani, de csak röviden szeretnénk megemlíteni, mivel kulcsszerepet játszik ebben a történetben. Ahogy telik az idő, egyre több ember találkozhat először a nevével, és merülhet el a nyúl üregében. <strong>Fizikai példányok.</strong> Nyilvánvalóan ez nem túl hasznos, mivel ezek csak azonos anyagok másolatai. Jó lenne, ha megőrizhetnénk az emberek által a könyvekbe tett összes jegyzetet, mint például Fermat híres „margóra írt firkálmányait”. De sajnos ez az archivisták álma marad. <strong>„Kiadások”.</strong> Itt minden egyedi könyvváltozatot számolunk. Ha bármi különbözik rajta, például más borító vagy más előszó, az más kiadásnak számít. <strong>Fájlok.</strong> Amikor árnyékkönyvtárakkal dolgozunk, mint a Library Genesis, a Sci-Hub vagy a Z-Library, van egy további szempont. Lehet több szkennelés ugyanarról a kiadásról. Az emberek jobb verziókat készíthetnek a meglévő fájlokból, például az OCR segítségével beolvasva a szöveget, vagy kijavítva a szögben szkennelt oldalakat. Ezeket a fájlokat csak egy kiadásként szeretnénk számolni, amihez jó metadata vagy a dokumentumok hasonlósági mérése alapján történő deduplikáció szükséges. <strong>„Művek”.</strong> Például a „Harry Potter és a Titkok Kamrája” mint logikai fogalom, amely magában foglalja annak összes változatát, mint például a különböző fordításokat és újranyomásokat. Ez egyfajta hasznos definíció, de nehéz lehet meghúzni a határt, hogy mi számít. Például valószínűleg meg akarjuk őrizni a különböző fordításokat, bár az újranyomások, amelyek csak kisebb eltéréseket tartalmaznak, talán nem olyan fontosak. - Anna és a csapat (<a %(reddit)s>Reddit</a>) A Kalóz Könyvtár Tükörrel (SZERK: áthelyezve ide: <a %(wikipedia_annas_archive)s>Anna Archívuma</a>) az a célunk, hogy a világ összes könyvét megőrizzük örökre.<sup>1</sup> A Z-Library torrentjeink és az eredeti Library Genesis torrentjeink között 11 783 153 fájlunk van. De valójában mennyi is ez? Ha megfelelően deduplikálnánk ezeket a fájlokat, a világ összes könyvének hány százalékát őriztük meg? Valami ilyesmit szeretnénk: Kezdjük néhány durva számmal: Mind a Z-Library/Libgen, mind az Open Library esetében sokkal több könyv van, mint egyedi ISBN. Ez azt jelenti, hogy sok ilyen könyvnek nincs ISBN-je, vagy az ISBN metadata egyszerűen hiányzik? Valószínűleg meg tudjuk válaszolni ezt a kérdést más attribútumok (cím, szerző, kiadó stb.) alapján történő automatikus egyeztetéssel, több adatforrás bevonásával, és az ISBN-ek kinyerésével magukból a könyvszkennelésekből (a Z-Library/Libgen esetében). Hány ezek közül az ISBN-ek közül egyedi? Ezt a legjobban egy Venn-diagrammal lehet szemléltetni: Hogy pontosabbak legyünk: Meglepődtünk, hogy milyen kevés az átfedés! Az ISBNdb rengeteg ISBN-t tartalmaz, amelyek sem a Z-Library-ben, sem az Open Library-ben nem jelennek meg, és ugyanez igaz (kisebb, de még mindig jelentős mértékben) a másik kettőre is. Ez sok új kérdést vet fel. Mennyit segítene az automatikus egyeztetés azoknak a könyveknek a címkézésében, amelyek nem kaptak ISBN-t? Sok egyezés lenne, és így növekedne az átfedés? Továbbá, mi történne, ha behoznánk egy 4. vagy 5. adatbázist? Mennyire látnánk akkor átfedést? Ez ad nekünk egy kiindulópontot. Most már megvizsgálhatjuk az összes ISBN-t, amely nem szerepelt a Z-Library adatbázisban, és amely nem egyezik a cím/szerző mezőkkel sem. Ez segíthet abban, hogy megőrizzük a világ összes könyvét: először az interneten keresztül szkenneléseket gyűjtve, majd a valós életben könyveket szkennelve. Az utóbbi akár közösségi finanszírozással is megvalósulhat, vagy „jutalmak” révén, amelyeket azok az emberek ajánlanak fel, akik szeretnék, ha bizonyos könyvek digitalizálásra kerülnének. Mindez egy másik történet. Ha szeretne segíteni bármelyik tevékenységben — további elemzés; több metadata gyűjtése; több könyv felkutatása; könyvek OCR-ezése; más területeken való alkalmazás (pl. tanulmányok, hangoskönyvek, filmek, tévéműsorok, magazinok) vagy akár az adatok elérhetővé tétele olyan célokra, mint az ML / nagy nyelvi modellek képzése — kérem, vegye fel velem a kapcsolatot (<a %(reddit)s>Reddit</a>). Ha kifejezetten az adatelemzés érdekli, azon dolgozunk, hogy adatbázisainkat és szkriptjeinket könnyebben használható formátumban tegyük elérhetővé. Nagyszerű lenne, ha csak egy notebookot kellene fork-olnia, és máris elkezdhetne vele játszani. Végül, ha támogatni szeretné ezt a munkát, kérjük, fontolja meg az adományozást. Ez egy teljesen önkéntes alapon működő kezdeményezés, és az Ön hozzájárulása óriási különbséget jelent. Minden apró segítség számít. Jelenleg kriptovalutában fogadunk el adományokat; lásd az Adományozás oldalt az Anna Archívumán. Egy százalékhoz szükségünk van egy nevezőre: a valaha kiadott könyvek teljes számára.<sup>2</sup> A Google Books megszűnése előtt a projekt egyik mérnöke, Leonid Taycher, <a %(booksearch_blogspot)s>megpróbálta megbecsülni</a> ezt a számot. Nyelv-in-cheek módon 129 864 880-at hozott ki („legalábbis vasárnapig”). Ezt a számot úgy becsülte meg, hogy egyesített adatbázist épített a világ összes könyvéről. Ehhez különböző adatbázisokat gyűjtött össze, majd különféle módokon egyesítette őket. Csak egy rövid kitérőként, volt egy másik személy is, aki megpróbálta katalogizálni a világ összes könyvét: Aaron Swartz, a néhai digitális aktivista és a Reddit társalapítója.<sup>3</sup> Ő <a %(youtube)s>elindította az Open Library-t</a> azzal a céllal, hogy „minden valaha megjelent könyvnek legyen egy weboldala”, különböző forrásokból származó adatok kombinálásával. Végül a digitális megőrzési munkája miatt a legnagyobb árat fizette, amikor akadémiai cikkek tömeges letöltése miatt vádat emeltek ellene, ami öngyilkosságához vezetett. Mondanunk sem kell, hogy ez az egyik oka annak, hogy csoportunk álnéven működik, és miért vagyunk nagyon óvatosak. Az Open Library-t még mindig hősiesen működtetik az Internet Archive munkatársai, folytatva Aaron örökségét. Később visszatérünk erre a bejegyzésben. A Google blogbejegyzésében Taycher leírja a szám becslésével kapcsolatos néhány kihívást. Először is, mi számít könyvnek? Van néhány lehetséges definíció: A „Kiadások” tűnnek a legpraktikusabb definíciónak arra, hogy mik is a „könyvek”. Kényelmesen, ezt a definíciót használják az egyedi ISBN számok hozzárendelésére is. Az ISBN, vagyis a Nemzetközi Szabványos Könyvszám, általánosan használt a nemzetközi kereskedelemben, mivel integrálva van a nemzetközi vonalkód rendszerrel („Nemzetközi Cikkszám”). Ha könyvet szeretne eladni az üzletekben, szüksége van egy vonalkódra, így kap egy ISBN-t. Taycher blogbejegyzése megemlíti, hogy bár az ISBN-ek hasznosak, nem univerzálisak, mivel csak a hetvenes évek közepén kezdték el igazán alkalmazni őket, és nem mindenhol a világon. Mégis, az ISBN valószínűleg a legszélesebb körben használt azonosító a könyvkiadások számára, így ez a legjobb kiindulópontunk. Ha megtaláljuk a világ összes ISBN-jét, hasznos listát kapunk arról, hogy mely könyveket kell még megőrizni. Tehát honnan szerezzük az adatokat? Számos meglévő erőfeszítés létezik, amelyek megpróbálják összeállítani a világ összes könyvének listáját: Ebben a bejegyzésben örömmel jelentjük be egy kisebb kiadást (az előző Z-Library kiadásainkhoz képest). Az ISBNdb nagy részét lekapartuk, és az adatokat elérhetővé tettük torrentelésre a Pirate Library Mirror weboldalán (SZERK: áthelyezve <a %(wikipedia_annas_archive)s>Anna Archívuma</a>; itt nem fogjuk közvetlenül linkelni, csak keresse meg). Ezek körülbelül 30,9 millió rekordot tartalmaznak (20GB <a %(jsonlines)s>JSON Lines</a> formátumban; 4,4GB tömörítve). A weboldalukon azt állítják, hogy valójában 32,6 millió rekordjuk van, így valahogy kihagyhattunk néhányat, vagy <em>ők</em> csinálhatnak valamit rosszul. Mindenesetre egyelőre nem osztjuk meg pontosan, hogyan csináltuk — ezt meghagyjuk az olvasónak gyakorlatként. ;-) Amit megosztunk, az néhány előzetes elemzés, hogy közelebb kerüljünk a világ könyveinek számának becsléséhez. Három adathalmazt vizsgáltunk: ezt az új ISBNdb adathalmazt, az eredeti metadata kiadásunkat, amelyet a Z-Library árnyékkönyvtárból kapartunk le (amely tartalmazza a Library Genesis-t), és az Open Library adatdumpját. ISBNdb dump, vagy Hány Könyv Marad Meg Örökre? Ha megfelelően deduplikálnánk az árnyékkönyvtárak fájljait, a világ összes könyvének hány százalékát őriztük meg? Frissítések az <a %(wikipedia_annas_archive)s>Anna Archívum</a>-ról, az emberiség történetének legnagyobb valóban nyitott könyvtáráról. <em>WorldCat áttervezés</em> Adatok <strong>Formátum?</strong> <a %(blog)s>Anna Archívum Konténerek (AAC)</a>, amelyek lényegében <a %(jsonlines)s>JSON Lines</a> formátumúak, <a %(zstd)s>Zstandard</a> tömörítéssel, valamint néhány szabványosított szemantikával. Ezek a konténerek különböző típusú rekordokat foglalnak magukba, az általunk alkalmazott különböző adatgyűjtések alapján. Egy évvel ezelőtt <a %(blog)s>elkezdtük</a> megválaszolni ezt a kérdést: <strong>A könyvek hány százalékát őrizték meg véglegesen az árnyékkönyvtárak?</strong> Nézzünk meg néhány alapvető információt az adatokkal kapcsolatban: Amint egy könyv bekerül egy nyílt adatú árnyékkönyvtárba, mint például a <a %(wikipedia_library_genesis)s>Library Genesis</a>, és most <a %(wikipedia_annas_archive)s>Anna Archívuma</a>, az egész világon tükröződik (torrenteken keresztül), így gyakorlatilag örökre megőrződik. Ahhoz, hogy megválaszoljuk a kérdést, hogy a könyvek hány százalékát őrizték meg, tudnunk kell a nevezőt: hány könyv létezik összesen? És ideális esetben nem csak egy számunk van, hanem tényleges metadata. Így nemcsak összevethetjük őket az árnyékkönyvtárakkal, hanem <strong>létrehozhatunk egy TODO listát a megőrzésre váró könyvekről!</strong> Még azt is elkezdhetjük álmodni, hogy egy közösségi erőfeszítéssel végigmegyünk ezen a TODO listán. Lekértük az <a %(wikipedia_isbndb_com)s>ISBNdb</a> adatait, és letöltöttük az <a %(openlibrary)s>Open Library dataset</a> adathalmazt, de az eredmények nem voltak kielégítőek. A fő probléma az volt, hogy nem volt jelentős átfedés az ISBN-ek között. Nézze meg ezt a Venn-diagramot <a %(blog)s>a blogbejegyzésünkben</a>: Nagyon meglepődtünk, hogy milyen kevés átfedés volt az ISBNdb és az Open Library között, amelyek mindkettő bőségesen tartalmaz adatokat különböző forrásokból, például webes adatgyűjtésekből és könyvtári nyilvántartásokból. Ha mindkettő jól végezné a munkáját az ISBN-ek megtalálásában, akkor a köreiknek jelentős átfedése lenne, vagy az egyik a másik részhalmaza lenne. Elgondolkodtunk azon, hány könyv esik <em>teljesen ezen körökön kívül</em>? Nagyobb adatbázisra van szükségünk. Ekkor fordítottuk figyelmünket a világ legnagyobb könyvadatbázisára: <a %(wikipedia_worldcat)s>WorldCat</a>. Ez egy tulajdonosi adatbázis, amelyet a non-profit <a %(wikipedia_oclc)s>OCLC</a> üzemeltet, és amely könyvtárak metadata rekordjait gyűjti össze világszerte, cserébe azért, hogy ezek a könyvtárak hozzáférést kapjanak a teljes adathalmazhoz, és megjelenjenek a végfelhasználók keresési eredményeiben. Bár az OCLC non-profit szervezet, üzleti modelljük megköveteli az adatbázisuk védelmét. Nos, sajnáljuk, OCLC barátaink, mi mindent megosztunk. :-) Az elmúlt év során aprólékosan lekértük a WorldCat összes rekordját. Eleinte szerencsés fordulatot vettünk. A WorldCat éppen akkor vezette be teljes weboldalának áttervezését (2022 augusztusában). Ez magában foglalta a háttérrendszereik jelentős átalakítását, ami számos biztonsági hibát vezetett be. Azonnal megragadtuk a lehetőséget, és néhány nap alatt több száz millió (!) rekordot tudtunk lekérni. Ezt követően a biztonsági hibákat lassan egyenként javították, míg az utolsó, amit találtunk, körülbelül egy hónapja lett kijavítva. Addigra már szinte minden rekordot megszereztünk, és csak kissé jobb minőségű rekordokra törekedtünk. Így úgy éreztük, itt az ideje a kiadásnak! 1,3 milliárd WorldCat lekaparás <em><strong>Röviden:</strong> Anna Archívuma lekaparta az összes WorldCat adatot (a világ legnagyobb könyvtári metadata gyűjteménye), hogy készítsen egy TODO listát a megőrzésre szoruló könyvekről.</em> WorldCat Figyelem: ez a blogbejegyzés elavult. Úgy döntöttünk, hogy az IPFS még nem áll készen a széles körű használatra. Továbbra is linkelünk fájlokat az IPFS-en keresztül Anna Archívumából, amikor lehetséges, de már nem fogjuk magunk hosztolni, és nem is ajánljuk másoknak, hogy tükrözzék az IPFS használatával. Kérjük, tekintse meg a Torrents oldalunkat, ha segíteni szeretne gyűjteményünk megőrzésében. Segítsen a Z-Library IPFS-en való terjesztésében Partner Szerver letöltés SciDB Külső kölcsönzés Külső kölcsönzés (nyomtatás kikapcsolva) Külső letöltés Metaadat felfedezése Torrentekben található Vissza  (+%(num)s bónusz) nem fizetett fizetett visszamondott lejárt várakozás Anna jóváhagyására érvénytelen A szöveg alább folytatódik angolul. Mehet Visszaállítás Előre Utolsó Ha az e-mail címed nem működik a Libgen fórumokon, <a %(a_mail)s>ProtonMail</a> használatát ajánljuk (ingyenes). <a %(a_manual)s>Manuálisan is kérheted</a> fiókod aktiválását. (<a %(a_browser)s>böngésző-ellenőrzést</a> igényelhet - korlátlan letöltés!) Gyors Partner Szerver #%(number)s (ajánlott) (kicsit gyorsabb, de várólistával) (nincs szükség böngésző-ellenőrzésre) (nincs böngészőellenőrzés vagy várólista) (nincs várólista, de nagyon lassú lehet) Lassú Partner Szerver #%(number)s Hangoskönyv Képregény Könyv (fikció) Könyv (nem-fikció) Könyv (ismeretlen) Folyóirati cikk Magazin Zenei partitúra Egyéb Szabványok dokumentuma Nem minden oldalt lehetett PDF-be konvertálni A Libgen.li-en hibásként jelölve Nem látható Libgen.li-ben Nem látható a Libgen.rs Fikció Nem látható a Libgen.rs Nem-Fikció Az exiftool futtatása sikertelen volt ezen a fájlon A Z-Library-ben „rossz fájl”-ként megjelölve Hiányzik a Z-Library-ból A Z-Library-ben „spam”-ként megjelölve A fájl nem nyitható meg (pl. sérült fájl, DRM) Szerzői jogi igény Letöltési problémák (pl. nem tud csatlakozni, hibaüzenet, nagyon lassú) Helytelen metaadat (pl. cím, leírás, borítókép) Más Gyenge minőség (pl. formázási problémák, gyenge szkennelési minőség, hiányzó oldalak) Spam / fájl eltávolítandó (pl. reklám, bántalmazó tartalom) %(amount)s (%(amount_usd)s) %(amount)s összesen %(amount)s (%(amount_usd)s) összesen Káprázatos Könyvmoly Kivételes Könyvtáros Állhatatos Adathalmozó Lélekvarázsló Levéltáros Bónusz letöltések Cerlalc Cseh metaadat DuXiu 读秀 EBSCOhost eBook Index Google Könyvek Goodreads HathiTrust IA IA Ellenőrzött Digitális Kölcsönzés ISBNdb ISBN GRP Libgen.li „scimag” kizárva Libgen.rs Nem-fikció és fikció Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Orosz Állami Könyvtár Sci-Hub A Libgen.li “scimag” keresztül Sci-Hub / Libgen „scimag” Trantor Feltöltések az AA-ra Z-Library Z-Library kínai Keresés cím, szerző, nyelv, fájltípus, ISBN, MD5,… Keresés Szerző Leírás és metaadat-megjegyzések Kiadás Eredeti fájlnév Kiadó (keresés adott mezőben) Cím Megjelenés éve Műszaki adatok (angolul) Ennek az érmének a szokásosnál magasabb a minimális értéke. Kérjük, válassz más időtartamot vagy más érmét. A kérést nem sikerült teljesíteni. Kérjük, próbálja újra néhány perc múlva, és ha ez továbbra is megtörténik, forduljon hozzánk a %(email)s képernyőképpel. Ismeretlen hiba történt. Kérjük, lépj kapcsolatba velünk a %(email)s címen egy képernyőképpel. Hiba a fizetés feldolgozása során. Kérjük, várjon egy pillanatot, majd próbálja újra. Ha a probléma több mint 24 órán keresztül fennáll, kérjük, lépjen kapcsolatba velünk a %(email)s címen egy képernyőképpel. Adományt gyűjtünk a világ legnagyobb képregény-árnyékkönyvtárának <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">kimentésére</a>. Köszönjük a támogatásodat! <a href="/donate">Adományozz.</a> Ha nem tudsz adományozni, támogass minket azzal, hogy elmondod barátaidnak, és követsz minket a <a href="https://www.reddit.com/r/Annas_Archive">Redditen</a> vagy <a href="https://t.me/annasarchiveorg">Telegramon</a>. Ne írjon nekünk e-mailt <a %(a_request)s>kérjen könyveket</a><br>vagy kis fájl (<10k)<a %(a_upload)s>feltöltések</a>. Anna Archívuma DMCA / jogvédelmi kérelmek Tartsd a kapcsolatot Reddit Alternatívák SLUM (%(unaffiliated)s) független Anna Archívuma segítségre szorul! Ha most adományoz, <strong>kétszer</strong> annyi gyors letöltést kap. Sokan próbálnak minket leállítani, de mi visszavágunk. Ha ebben a hónapban adományozol, <strong>dupla</strong> gyors letöltést kapsz. Érvényes a hónap végéig. Az emberi tudás megmentése: nagyszerű ünnepi ajándék! A tagságok ennek megfelelően meghosszabbításra kerülnek. A partner szerverek nem elérhetők a tárhely bezárások miatt. Hamarosan újra elérhetők lesznek. Az Anna Archívuma ellenálló képességének növelése érdekében önkénteseket keresünk tükrözések futtatására. Van egy új adakozási módszerünk: %(method_name)s. Kérjük ha teheted %(donate_link_open_tag)sadakozz</a> — nem olcsó fenntartani ezt a weboldalt, és az adományaitok tényleg segítenek. Nagyon köszönjük. Ajánljon egy barátot, és mindketten %(percentage)s%% bónusz gyors letöltést kapnak! Lepje meg szeretteit, adjon neki számlát a tagsággal. A tökéletes Valentin-napi ajándék! Tudjon meg többet… Fiók Tevékenység Haladó Anna Blogja ↗ Anna Szoftvere ↗ béta Kódok Felfedezője Adatbázisok Adományozás Letöltött fájlok GYIK Főoldal Metaadatok javítása LLM adat Bejelentkezés / Regisztráció Adományaim Nyilvános profil Keresés Biztonság Torrentek Fordítás ↗ Önkéntesség és Jutalmak Legutóbbi letöltések: 📚&nbsp;A világ legnagyobb nyílt forráskódú nyílt adatkönyvtára. ⭐️&nbsp;Tükrözi a Sci-Hub, a Library Genesis, a Z-Library és más könyvtárakat. 📈&nbsp;%(book_any)s könyv, %(journal_article)s cikk, %(book_comic)s képregény, %(magazine)s magazin — örökre megőrizve.  és  és még több DuXiu Internet Archive Kölcsönző Könyvtár LibGen 📚&nbsp;Az emberiség történetének legnagyobb valóban nyitott könyvtára. 📈&nbsp;%(book_count)s&nbsp;könyveket, %(paper_count)s&nbsp;papírokat — örökre megőrizve. ⭐️&nbsp;Tükrözzük %(libraries)s. Kapargatunk és nyílt forráskódúvá tesszük %(scraped)s. Minden kódunk és adatunk teljesen nyílt forráskódú. OpenLib Sci-Hub ,  📚 A világ legnagyobb nyílt forráskódú nyílt adatkönyvtára.<br>⭐️ Tükrözi a Scihub-ot, a Libgen-t, a Zlib-et és még sok mást. Z-Lib Anna Archívuma Érvénytelen kérelem. Látogatás %(websites)s. A világ legnagyobb nyílt forráskódú nyílt adatkönyvtára. Tükrözi a Sci-Hubot, a Library Genesist, a Z-Libraryt, és még sok mást. Keresés Anna Archívumában Anna Archívuma Kérem, frissítse az oldalt, és próbálja újra. <a %(a_contact)s>Lépjen kapcsolatba velünk</a>, ha a probléma több órán keresztül fennáll. 🔥 Hiba történt az oldal betöltésekor <li>1. Kövess minket <a href="https://www.reddit.com/r/Annas_Archive/">Redditen</a>, vagy <a href="https://t.me/annasarchiveorg">Telegramon</a>.</li><li>2. Terjeszd a hírt az Anna Archívumáról Twitteren, Redditen, Tiktokon, Instagramon, a helyi kávézódban, vagy ahol csak jársz! Mi nem hiszünk abban hogy a tudás kiváltság - ha a weboldalunkat letörlik, csak felbukkanunk máshol, hisz az összes kódunk és adatunk teljesen nyílt forráskódú.</li><li>3. Ha teheted <a href="/donate">adakozz</a>.</li><li>4. Segíts <a href="https://translate.annas-software.org/">lefordítani</a> a weboldalunkat különböző nyelvekre.</li><li>5. Ha programozó vagy, járulj hozzá a <a href="https://annas-software.org/">nyílt forráskódunkhoz</a>, vagy seedeld a <a href="https://en.wikipedia.org/wiki/Pirate_Library_Mirror">torrentjeinket és IPFS-ünket</a>.</li> 10. Hozzon létre vagy segítsen fenntartani az Anna archívum Wikipédia-oldalát az Ön nyelvén. 11. Kisméretű, ízléses hirdetéseket keresünk. Ha szeretne hirdetni az Anna archívumában, kérjük, jelezze felénk. 6. Ha Ön biztonságkutató, tudását támadásban és védekezésben egyaránt használhatjuk. Nézze meg a mi <a %(a_security)s>Biztonság</a> page. 7. Fizetési szakértőket keresünk névtelen kereskedők részére. Segítene nekünk kényelmesebb adományozási módok hozzáadásával? PayPal, WeChat, ajándékkártyák. Ha ismer valakit, kérem vegye fel velünk a kapcsolatot. 8. Mindig több szerverkapacitást keresünk. 9. Segíthet a fájlokkal kapcsolatos problémák bejelentésével, megjegyzésekkel és listák létrehozásával ezen a webhelyen. Ezzel is segíthetsz <a %(a_upload)s>további könyvek feltöltése</a>, vagy fájlproblémák kijavítása vagy meglévő könyvek formázása. További részletes információkért arról, hogyan lehet önkéntes, tekintse meg <a %(a_volunteering)s>Önkéntesség és Jutalmak</a> oldalunkat. Erősen hiszünk az információ szabad áramlásában, a tudás és a kultúra megőrzésében. Ezzel a keresővel mi is messzebbre láthatunk, mivel az óriások vállán állhatunk. Mélyen tiszteljük az árnyék könyvtárakat létrehozók kemény munkáját, és reméljük, ezzel a keresővel még több ember számára elérhetővé válnak majd. A munkánkat <a href="https://www.reddit.com/r/Annas_Archive/">Redditen</a>, vagy <a href="https://t.me/annasarchiveorg">Telegramon</a> követheted. Kérdésekkel és visszajelzésekkel kapcsolatban Anna e-mailben elérhető %(email)s. Fiókazonosító: %(account_id)s Kijelentkezés ❌ Valami rosszul sikerült. Töltsd újra az oldalt, és próbáld meg újra. ✅ Kijelentkezve. Töltsd újra az oldalt, hogy újra bejelentkezz. Gyors letöltések (az elmúlt 24 órában): <strong>%(used)s / %(total)s</strong> Tagság: <strong>%(tier_name)s</strong> %(until_date)s -ig<a %(a_extend)s>(meghosszabbítás)</a> Több tagságot is kombinálhatsz (a 24 óránkénti gyors letöltések összege összeadódik). Tagság: <strong>Semmi</strong> <a %(a_become)s>(válj taggá)</a> Lépj kapcsolatba Annával az %(email)s e-mail címen, ha szeretnéd magasabb szintre emelni a tagságodat. Nyilvános profil: %(profile_link)s Titkos kulcs (ne ossza meg!): %(secret_key)s mutat Csatlakozzon hozzánk itt! Frissítsen egy <a %(a_tier)s>magasabb szintre</a>, hogy csatlakozzon csoportunkhoz. Exkluzív Telegram csoport: %(link)s Fiók mely letöltések? Bejelentkezés Ne veszítse el a kulcsát! Érvénytelen titkos kulcs. Ellenőrizd a kulcsodat, és próbáld meg újra, vagy regisztrálj új fiókot alább. Titkos kulcs Add meg a titkos kulcsot a bejelentkezéshez: Régi e-mail alapú fiók? Add meg az <a %(a_open)s>e-mail címedet itt</a>. Új fiók regisztrálása Még nincs fiókod? Sikeres regisztráció! A titkos kulcsod: <span %(span_key)s>%(key)s</span> Gondosan mentsd el ezt a kulcsot. Ha elveszíted, a fiókodhoz való hozzáférést is elveszíted. <li %(li_item)s><strong>Könyvjelző.</strong> A könyvjelzőid közé mentheted az oldalt, hogy visszakeresd a kulcsodat.</li><li %(li_item)s><strong>Letöltés.</strong> Kattints <a %(a_download)s>erre a linkre</a>, hogy letöltsd a kulcsod.</li><li %(li_item)s><strong>Jelszókezelő.</strong>A kényelmed érdekében a kulcsot alább előre kitöltjük, így bejelentkezéskor elmentheted a jelszókezelődbe.</li> Bejelentkezés / Regisztráció Böngésző ellenőrzés Figyelem: a kódban helytelen Unicode karakterek találhatók, és különböző helyzetekben helytelenül viselkedhet. A nyers bináris adatokat az URL-ben található base64 ábrázolásból lehet dekódolni. Leírás Címke Előtag URL egy adott kódhoz Weboldal Kódok, amelyek a következővel kezdődnek: „%(prefix_label)s” Kérjük, ne kaparja le ezeket az oldalakat. Ehelyett javasoljuk <a %(a_import)s>az ElasticSearch és MariaDB adatbázisaink generálását</a> vagy <a %(a_download)s>letöltését</a>, és <a %(a_software)s>nyílt forráskódú kódunk</a> futtatását. A nyers adatok manuálisan is felfedezhetők JSON fájlokon keresztül, mint például <a %(a_json_file)s>ez</a>. Kevesebb mint %(count)s rekord Általános URL Kódok Felfedezője Index Fedezze fel azokat a kódokat, amelyekkel a rekordok meg vannak jelölve, előtag szerint. A „rekordok” oszlop azt mutatja, hogy hány rekord van megjelölve az adott előtagú kódokkal a keresőmotorban (beleértve a csak metaadatokat tartalmazó rekordokat is). A „kódok” oszlop azt mutatja, hogy hány tényleges kód rendelkezik az adott előtaggal. Ismert kód előtag „%(key)s” Tovább… Előtag %(count)s rekord egyezik a következővel: „%(prefix_label)s” kódok rekordok A „%%s” helyére a kód értéke kerül Anna Archívum keresése Kódok URL a konkrét kódhoz: „%(url)s” Ennek az oldalnak a generálása eltarthat egy ideig, ezért Cloudflare captcha szükséges. A <a %(a_donate)s>tagok</a> kihagyhatják a captchát. Jelentett visszaélés: Jobb verzió Szeretné jelenteni ezt a felhasználót bántó vagy nem megfelelő viselkedés miatt? Fájlprobléma: %(file_issue)s rejtett megjegyzés Válasz Visszaélés jelentése Ön jelentette ezt a felhasználót visszaélés miatt. A szerzői jogi igényeket erre az e-mailre figyelmen kívül hagyjuk; használja helyette az űrlapot. E-mail megjelenítése Nagyon örülünk a visszajelzéseidnek és kérdéseidnek! Azonban a sok spam és értelmetlen e-mail miatt kérjük, jelölje be a négyzeteket, hogy megerősítse, megértette ezeket a feltételeket a kapcsolatfelvételhez. A szerzői jogi igényekkel kapcsolatos egyéb módon történő kapcsolatfelvétel automatikusan törlésre kerül. DMCA / szerzői jogi igények esetén használja <a %(a_copyright)s>ezt az űrlapot</a>. Kapcsolattartó e-mail URL-ek az Anna Archívumában (kötelező). Egy sorban egy URL. Kérjük, csak olyan URL-eket adjon meg, amelyek ugyanazon könyv pontosan ugyanazon kiadását írják le. Ha több könyvre vagy több kiadásra szeretne igényt benyújtani, kérjük, töltse ki többször ezt az űrlapot. Azokat az igényeket, amelyek több könyvet vagy kiadást egyesítenek, elutasítjuk. Cím (kötelező) Az eredeti anyag egyértelmű leírása (kötelező) E-mail (kötelező) URL-ek az eredeti anyaghoz, egy sorban egy URL (kötelező). Kérjük, adjon meg minél többet, hogy segítsen nekünk az igényének ellenőrzésében (pl. Amazon, WorldCat, Google Books, DOI). Az eredeti anyag ISBN-jei (ha alkalmazható). Egy sorban egy ISBN. Kérjük, csak azokat adja meg, amelyek pontosan megegyeznek azzal a kiadással, amelyre szerzői jogi igényt jelent be. Az Ön neve (kötelező) ❌ Valami hiba történt. Kérjük, frissítse az oldalt, és próbálja újra. ✅ Köszönjük, hogy benyújtotta szerzői jogi igényét. Amint lehetséges, átnézzük. Kérjük, frissítse az oldalt, hogy újabb igényt nyújtson be. <a %(a_openlib)s>Open Library</a> URL-ek az eredeti anyaghoz, egy sorban egy URL. Kérjük, szánjon egy pillanatot arra, hogy megkeresse az eredeti anyagot az Open Library-ban. Ez segít nekünk az igényének ellenőrzésében. Telefonszám (kötelező) Nyilatkozat és aláírás (kötelező) Igény benyújtása Ha DMCA vagy más szerzői jogi igénye van, kérjük, töltse ki ezt az űrlapot a lehető legpontosabban. Ha bármilyen problémába ütközik, kérjük, lépjen kapcsolatba velünk a dedikált DMCA címünkön: %(email)s. Felhívjuk figyelmét, hogy az erre a címre küldött igényeket nem dolgozzuk fel, ez csak kérdésekre szolgál. Kérjük, használja az alábbi űrlapot az igények benyújtásához. DMCA / Szerzői jogi igénylőlap Példa rekord Anna Archívumában Torrentek Anna Archívumából Anna Archívuma Konténerek formátum Metaadat importálási szkriptek Ha érdekli ennek az adatállománynak a tükrözése <a %(a_archival)s>archiválási</a> vagy <a %(a_llm)s>LLM képzési</a> célokra, kérjük, vegye fel velünk a kapcsolatot. Utoljára frissítve: %(date)s Fő %(source)s weboldal Metaadat dokumentáció (a legtöbb mező) Anna Archívuma által tükrözött fájlok: %(count)s (%(percent)s%%) Erőforrások Összes fájl: %(count)s Összes fájlméret: %(size)s Blogbejegyzésünk erről az adatról <a %(duxiu_link)s>Duxiu</a> egy hatalmas adatbázis szkennelt könyvekről, amelyet a <a %(superstar_link)s>SuperStar Digital Library Group</a> hozott létre. Többségük tudományos könyv, amelyeket azért szkenneltek be, hogy digitálisan elérhetővé tegyék az egyetemek és könyvtárak számára. Angolul beszélő közönségünk számára a <a %(princeton_link)s>Princeton</a> és a <a %(uw_link)s>Washingtoni Egyetem</a> jó áttekintést nyújtanak. Van egy kiváló cikk is, amely további háttérinformációkat ad: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. A Duxiu könyveket régóta kalózkodják a kínai interneten. Általában kevesebb mint egy dollárért árulják őket viszonteladók. Tipikusan a Google Drive kínai megfelelőjét használják a terjesztésükre, amelyet gyakran feltörnek, hogy több tárhelyet biztosítsanak. Néhány technikai részlet megtalálható <a %(link1)s>itt</a> és <a %(link2)s>itt</a>. Bár a könyveket félig nyilvánosan terjesztették, meglehetősen nehéz őket tömegesen beszerezni. Magas prioritásként szerepelt a teendőlistánkon, és több hónapnyi teljes munkaidős munkát szántunk rá. Azonban 2023 végén egy hihetetlen, csodálatos és tehetséges önkéntes felkeresett minket, és elmondta, hogy már elvégezte ezt a munkát — nagy költséggel. Megosztotta velünk a teljes gyűjteményt, anélkül, hogy bármit is várt volna cserébe, kivéve a hosszú távú megőrzés garanciáját. Igazán figyelemre méltó. További információk önkénteseinktől (nyers jegyzetek): Adaptálva a <a %(a_href)s>blogbejegyzésünkből</a>. DuXiu 读秀 %(count)s fájl Ez az adatállomány szorosan kapcsolódik az <a %(a_datasets_openlib)s>Open Library adatállományához</a>. Tartalmazza az összes metaadatot és az IA Ellenőrzött Digitális Kölcsönzési Könyvtárának fájljainak nagy részét. A frissítések az <a %(a_aac)s>Anna Archívum Konténerek formátumban</a> kerülnek kiadásra. Ezek a rekordok közvetlenül az Open Library adatállományából származnak, de tartalmaznak olyan rekordokat is, amelyek nincsenek az Open Library-ban. Számos adatfájllal is rendelkezünk, amelyeket a közösség tagjai évek során gyűjtöttek össze. A gyűjtemény két részből áll. Mindkét részre szüksége van az összes adat megszerzéséhez (kivéve a felülírt torrenteket, amelyek áthúzva jelennek meg a torrentek oldalán). Digitális Kölcsönző Könyvtár első kiadásunk, mielőtt szabványosítottuk volna az <a %(a_aac)s>Anna Archívuma Konténerek (AAC) formátumot</a>. Tartalmaz metaadatokat (json és xml formátumban), pdf-eket (az acsm és lcpdf digitális kölcsönzési rendszerekből), és borító miniatűröket. inkrementális új kiadások, AAC használatával. Csak a 2023-01-01 utáni időbélyeggel ellátott metaadatokat tartalmazza, mivel a többit már lefedi az „ia”. Valamint minden pdf fájlt, ezúttal az acsm és a „bookreader” (IA webes olvasója) kölcsönzési rendszerekből. Annak ellenére, hogy a név nem teljesen pontos, mégis a bookreader fájlokat az ia2_acsmpdf_files gyűjteménybe helyezzük, mivel ezek kölcsönösen kizárják egymást. IA Ellenőrzött Digitális Kölcsönzés 98%%+ fájl kereshető. Küldetésünk, hogy archiváljuk a világ összes könyvét (valamint tanulmányokat, magazinokat stb.), és széles körben elérhetővé tegyük őket. Hisszük, hogy minden könyvet széles körben tükrözni kell, hogy biztosítsuk a redundanciát és a rugalmasságot. Ezért gyűjtjük össze a fájlokat különböző forrásokból. Néhány forrás teljesen nyitott és tömegesen tükrözhető (mint például a Sci-Hub). Mások zártak és védettek, ezért megpróbáljuk őket lekaparni, hogy „felszabadítsuk” könyveiket. Mások valahol a kettő között helyezkednek el. Minden adatunk <a %(a_torrents)s>torrentelhető</a>, és minden metaadatunk <a %(a_anna_software)s>generálható</a> vagy <a %(a_elasticsearch)s>letölthető</a> ElasticSearch és MariaDB adatbázisokként. A nyers adatok manuálisan is felfedezhetők JSON fájlokon keresztül, mint például <a %(a_dbrecord)s>ez</a>. Metaadatok ISBN weboldal Utoljára frissítve: %(isbn_country_date)s (%(link)s) Erőforrások A Nemzetközi ISBN Ügynökség rendszeresen kiadja azokat a tartományokat, amelyeket a nemzeti ISBN ügynökségeknek osztott ki. Ebből megállapíthatjuk, hogy melyik ország, régió vagy nyelvi csoport tulajdonában van az adott ISBN. Jelenleg ezt az adatot közvetetten használjuk, az <a %(a_isbnlib)s>isbnlib</a> Python könyvtáron keresztül. ISBN ország információ Ez egy nagy mennyiségű hívás dumpja az isbndb.com-ra 2022 szeptemberében. Megpróbáltuk lefedni az összes ISBN tartományt. Ez körülbelül 30,9 millió rekordot jelent. A weboldalukon azt állítják, hogy valójában 32,6 millió rekordjuk van, így valahogy kihagyhattunk néhányat, vagy <em>ők</em> hibázhattak valamit. A JSON válaszok nagyjából nyersek a szerverükről. Egy adatminőségi probléma, amit észrevettünk, hogy az ISBN-13 számoknál, amelyek más előtaggal kezdődnek, mint a „978-”, még mindig tartalmaznak egy „isbn” mezőt, amely egyszerűen az ISBN-13 szám az első 3 számjegy levágásával (és az ellenőrző szám újraszámításával). Ez nyilvánvalóan hibás, de így csinálják, ezért nem változtattuk meg. Egy másik potenciális probléma, amivel találkozhat, az az, hogy az „isbn13” mező duplikált, így nem használható elsődleges kulcsként egy adatbázisban. Az „isbn13”+„isbn” mezők kombinációja viszont egyedinek tűnik. 1. kiadás (2022-10-31) A szépirodalmi torrentek lemaradtak (bár az ~4-6M azonosítók nem lettek torrentelve, mivel átfedésben vannak a Zlib torrentjeinkkel). Blogbejegyzésünk a képregények kiadásáról Képregény torrentek az Anna Archívumában A különböző Library Genesis forkok hátteréhez lásd a <a %(a_libgen_rs)s>Libgen.rs</a> oldalt. A Libgen.li tartalmazza a Libgen.rs legtöbb tartalmát és metaadatát, de néhány gyűjteménnyel kiegészítve, nevezetesen képregényekkel, magazinokkal és szabványdokumentumokkal. Integrálta a <a %(a_scihub)s>Sci-Hub</a>-ot is a metaadataiba és keresőmotorjába, amit az adatbázisunkhoz használunk. Ennek a könyvtárnak a metaadatai szabadon elérhetők <a %(a_libgen_li)s>a libgen.li oldalon</a>. Azonban ez a szerver lassú és nem támogatja a megszakadt kapcsolatok folytatását. Ugyanezek a fájlok elérhetők egy <a %(a_ftp)s>FTP szerveren</a> is, amely jobban működik. Úgy tűnik, hogy a nem fikciós művek is eltértek, de új torrentek nélkül. Úgy tűnik, hogy ez 2022 eleje óta történt, bár ezt nem ellenőriztük. A Libgen.li adminisztrátora szerint a „fiction_rus” (orosz szépirodalom) gyűjteményt a <a %(a_booktracker)s>booktracker.org</a> rendszeresen kiadott torrentjei kell, hogy lefedjék, különösen a <a %(a_flibusta)s>flibusta</a> és <a %(a_librusec)s>lib.rus.ec</a> torrentek (amelyeket itt tükrözünk <a %(a_torrents)s>, bár még nem állapítottuk meg, mely torrentek mely fájloknak felelnek meg). A szépirodalmi gyűjteménynek saját torrentjei vannak (eltérő a <a %(a_href)s>Libgen.rs</a>-től) %(start)s-től kezdődően. Bizonyos tartományok, amelyekhez nincsenek torrentek (mint például a f_3463000-től f_4260000-ig terjedő szépirodalmi tartományok), valószínűleg Z-Library (vagy más duplikált) fájlok, bár érdemes lehet némi deduplikációt végezni, és torrenteket készíteni az lgli-egyedi fájlokhoz ezekben a tartományokban. Az összes gyűjtemény statisztikái megtalálhatók <a %(a_href)s>a libgen weboldalán</a>. A legtöbb további tartalomhoz elérhetők torrentek, különösen a képregények, magazinok és szabványos dokumentumok torrentjei jelentek meg az Anna Archívummal együttműködésben. Vegye figyelembe, hogy a „libgen.is” hivatkozású torrent fájlok kifejezetten a <a %(a_libgen)s>Libgen.rs</a> tükrei („.is” egy másik domain, amelyet a Libgen.rs használ). Egy hasznos forrás a metaadatok használatához <a %(a_href)s>ez az oldal</a>. %(icon)s Az ő „fiction_rus” gyűjteményüknek (orosz szépirodalom) nincs dedikált torrentje, de mások torrentjei lefedik, és mi tartunk egy <a %(fiction_rus)s>tükröt</a>. Orosz szépirodalmi torrentek az Anna Archívumban Szépirodalmi torrentek az Anna Archívumában Vita fórum Metaadatok Metaadatok FTP-n keresztül Magazin torrentek az Anna Archívumában Metaadat mező információk Más torrentek tükre (és egyedi szépirodalmi és képregény torrentek) Szabványos dokumentum torrentek az Anna Archívumban Libgen.li Torrentek Anna Archívumából (könyvborítók) A Library Genesis már ismert arról, hogy nagylelkűen elérhetővé teszi adatait tömegesen torrenteken keresztül. Libgen gyűjteményünk kiegészítő adatokat tartalmaz, amelyeket ők közvetlenül nem adnak ki, partnerségben velük. Nagy köszönet mindenkinek a Library Genesis-nél, hogy együttműködtek velünk! Blogunk a könyvborítók kiadásáról Ez az oldal a „.rs” verzióról szól. Ismert arról, hogy következetesen közzéteszi mind a metaadatait, mind a könyvkatalógusának teljes tartalmát. Könyvgyűjteménye szépirodalmi és nem szépirodalmi részekre van osztva. Egy hasznos forrás a metaadatok használatához <a %(a_metadata)s>ez az oldal</a> (IP tartományokat blokkol, VPN szükséges lehet). 2024-03-tól új torrenteket tesznek közzé ebben a <a %(a_href)s>fórumszálban</a> (IP-tartományokat blokkol, VPN szükséges lehet). Fikciós torrentek Anna Archívumában Libgen.rs Fikciós torrentek Libgen.rs Vitafórum Libgen.rs Metaadat Libgen.rs metaadat mező információk Libgen.rs Nem-fikciós torrentek Nem-fikciós torrentek Anna Archívumában %(example)s egy fikciós könyv esetén. Ez az <a %(blog_post)s>első kiadás</a> elég kicsi: körülbelül 300GB könyvborító a Libgen.rs forkból, mind fikciós, mind nem-fikciós. Ugyanúgy vannak szervezve, ahogy a libgen.rs-en megjelennek, pl.: %(example)s egy nem-fikciós könyv esetén. Csakúgy, mint a Z-Library gyűjteménynél, mindet egy nagy .tar fájlba tettük, amelyet fel lehet csatolni a <a %(a_ratarmount)s>ratarmount</a> segítségével, ha közvetlenül szeretné szolgáltatni a fájlokat. 1. kiadás (%(date)s) A különböző Library Genesis (vagy „Libgen”) forkok rövid története az, hogy az idők során a Library Genesis-ben részt vevő különböző emberek összevesztek, és külön utakon folytatták. E szerint a <a %(a_mhut)s>fórumbejegyzés</a> szerint a Libgen.li eredetileg a „http://free-books.dontexist.com” címen volt elérhető. A „.fun” verziót az eredeti alapító hozta létre. Jelenleg egy új, elosztottabb verzió javára újítják fel. A <a %(a_li)s>„.li” verzió</a> hatalmas képregénygyűjteménnyel rendelkezik, valamint más tartalmakkal, amelyek (még) nem érhetők el tömeges letöltésre torrenteken keresztül. Külön torrent gyűjteménye van szépirodalmi könyvekből, és adatbázisában tartalmazza a <a %(a_scihub)s>Sci-Hub</a> metaadatait. A „.rs” verzió nagyon hasonló adatokat tartalmaz, és leggyakrabban tömeges torrentekben adja ki gyűjteményét. Nagyjából „szépirodalom” és „nem szépirodalom” szekciókra van osztva. Eredetileg a „http://gen.lib.rus.ec” címen volt elérhető. <a %(a_zlib)s>Z-Library</a> bizonyos értelemben szintén a Library Genesis egy forkja, bár más nevet használtak projektjükhöz. Libgen.rs Gyűjteményünket csak metaadat forrásokkal is gazdagítjuk, amelyeket fájlokhoz tudunk párosítani, például ISBN számok vagy más mezők használatával. Az alábbiakban ezekről adunk áttekintést. Ismételten, egyes források teljesen nyitottak, míg másokat le kell kaparnunk. Vegye figyelembe, hogy a metaadat keresés során az eredeti rekordokat mutatjuk. Nem végezzük el a rekordok egyesítését. Csak metaadat források Az Open Library az Internet Archive nyílt forráskódú projektje, amelynek célja, hogy minden könyvet katalogizáljon a világon. Az egyik legnagyobb könyvszkennelési művelettel rendelkezik a világon, és sok könyvet elérhetővé tesz digitális kölcsönzésre. Könyv metaadat katalógusa szabadon letölthető, és szerepel Anna Archívumában (bár jelenleg nem kereshető, kivéve, ha kifejezetten egy Open Library ID-t keres). Open Library Duplikátumok kizárása Utoljára frissítve A fájlok számának százalékos aránya %% tükrözve az AA által / elérhető torrentek Méret Forrás Az alábbiakban egy gyors áttekintést talál az Anna Archívumában található fájlok forrásairól. Mivel az árnyékkönyvtárak gyakran szinkronizálják az adatokat egymással, jelentős átfedés van a könyvtárak között. Ezért nem adódnak össze a számok a teljeshez. Az „Anna Archívuma által tükrözött és vetett” százalékos arány azt mutatja, hogy hány fájlt tükrözünk mi magunk. Ezeket a fájlokat tömegesen vetjük torrenteken keresztül, és közvetlen letöltésre elérhetővé tesszük partner weboldalakon keresztül. Áttekintés Összesen Torrentek az Anna Archívumában A Sci-Hubról szóló háttérinformációkért kérjük, látogasson el a <a %(a_scihub)s>hivatalos weboldalára</a>, a <a %(a_wikipedia)s>Wikipédia oldalára</a>, és hallgassa meg ezt a <a %(a_radiolab)s>podcast interjút</a>. Vegye figyelembe, hogy a Sci-Hub <a %(a_reddit)s>2021 óta be van fagyasztva</a>. Korábban is be volt fagyasztva, de 2021-ben néhány millió cikket adtak hozzá. Ennek ellenére korlátozott számú cikk kerül hozzáadásra a Libgen „scimag” gyűjteményeihez, bár nem elegendő ahhoz, hogy új tömeges torrenteket indokoljon. A Sci-Hub metaadatait a <a %(a_libgen_li)s>Libgen.li</a> „scimag” gyűjteményében található adatok alapján használjuk. Emellett használjuk a <a %(a_dois)s>dois-2022-02-12.7z</a> adatbázist is. Vegye figyelembe, hogy a „smarch” torrentek <a %(a_smarch)s>elavultak</a>, ezért nem szerepelnek a torrentlistánkban. Torrentek a Libgen.li-n Torrentek a Libgen.rs-en Metaadatok és torrentek Frissítések a Redditen Podcast interjú Wikipédia oldal Sci-Hub Sci-Hub: 2021 óta befagyasztva; a legtöbb torrenteken keresztül elérhető Libgen.li: azóta kisebb kiegészítések</div> Egyes forráskönyvtárak támogatják adatuk tömeges megosztását torrenteken keresztül, míg mások nem osztják meg könnyen gyűjteményüket. Az utóbbi esetben az Anna Archívum megpróbálja lekaparni gyűjteményeiket, és elérhetővé tenni azokat (lásd <a %(a_torrents)s>Torrents</a> oldalunkat). Vannak köztes helyzetek is, például amikor a forráskönyvtárak hajlandóak megosztani, de nincs meg a szükséges erőforrásuk ehhez. Ilyen esetekben is megpróbálunk segíteni. Az alábbiakban áttekintést adunk arról, hogyan kapcsolódunk a különböző forráskönyvtárakhoz. Forráskönyvtárak %(icon)s Különféle fájl adatbázisok szétszórva a kínai interneten; bár gyakran fizetős adatbázisok %(icon)s A legtöbb fájl csak prémium BaiduYun fiókokkal érhető el; lassú letöltési sebességek. %(icon)s Anna Archívuma kezeli a <a %(duxiu)s>DuXiu fájlok</a> gyűjteményét %(icon)s Különféle metaadat-adatbázisok szétszórva a kínai interneten; bár gyakran fizetős adatbázisok %(icon)s Nincsenek könnyen hozzáférhető metaadat-dumpok a teljes gyűjteményükhöz. %(icon)s Anna Archívuma kezeli a <a %(duxiu)s>DuXiu metaadatok</a> gyűjteményét Fájlok %(icon)s A fájlok csak korlátozottan kölcsönözhetők, különféle hozzáférési korlátozásokkal %(icon)s Anna Archívuma kezeli az <a %(ia)s>IA fájlok</a> gyűjteményét %(icon)s Néhány metaadat elérhető az <a %(openlib)s>Open Library adatbázis dumpokon</a> keresztül, de ezek nem fedik le az egész IA gyűjteményt %(icon)s Nincsenek könnyen hozzáférhető metaadat-dumpok a teljes gyűjteményükhöz %(icon)s Anna Archívuma kezeli az <a %(ia)s>IA metaadatok</a> gyűjteményét Utoljára frissítve %(icon)s Az Anna Archívum és a Libgen.li közösen kezelik a <a %(comics)s>képregények</a>, <a %(magazines)s>magazinok</a>, <a %(standarts)s>szabványos dokumentumok</a> és <a %(fiction)s>szépirodalom (eltér a Libgen.rs-től)</a> gyűjteményeit. %(icon)s Nem-fikciós torrentek megosztva a Libgen.rs-sel (és tükrözve <a %(libgenli)s>itt</a>). %(icon)s Negyedéves <a %(dbdumps)s>HTTP adatbázis dumpok</a> %(icon)s Automatikus torrentek <a %(nonfiction)s>Non-Fiction</a> és <a %(fiction)s>Fiction</a> kategóriákhoz %(icon)s Anna Archívuma kezeli a <a %(covers)s>könyvborító torrentek</a> gyűjteményét %(icon)s Napi <a %(dbdumps)s>HTTP adatbázis-dumpok</a> Metaadat %(icon)s Havi <a %(dbdumps)s>adatbázis-dumpok</a> %(icon)s Adattorrentek elérhetők <a %(scihub1)s>itt</a>, <a %(scihub2)s>itt</a>, és <a %(libgenli)s>itt</a> %(icon)s Néhány új fájl <a %(libgenrs)s>hozzáadásra</a> <a %(libgenli)s>kerül</a> a Libgen „scimag” részéhez, de nem elegendő ahhoz, hogy új torrenteket indokoljon %(icon)s A Sci-Hub 2021 óta befagyasztotta az új fájlokat. %(icon)s Metaadat-dumpok elérhetők <a %(scihub1)s>itt</a> és <a %(scihub2)s>itt</a>, valamint a <a %(libgenli)s>Libgen.li adatbázis</a> részeként (amit használunk) Forrás %(icon)s Különféle kisebb vagy egyszeri források. Arra bátorítjuk az embereket, hogy először más árnyékkönyvtárakba töltsenek fel, de néha az embereknek olyan gyűjteményeik vannak, amelyek túl nagyok ahhoz, hogy mások átnézzék, de nem elég nagyok ahhoz, hogy saját kategóriát érdemeljenek. %(icon)s Nem érhető el közvetlenül tömegesen, védett a lekaparás ellen %(icon)s Anna Archívuma kezeli az <a %(worldcat)s>OCLC (WorldCat) metaadatok</a> gyűjteményét %(icon)s Anna Archívuma és a Z-Library közösen kezelik a <a %(metadata)s>Z-Library metaadatok</a> és <a %(files)s>Z-Library fájlok</a> gyűjteményét Datasets Az összes fenti forrást egy egységes adatbázisba kombináljuk, amelyet ezen a weboldalon használunk. Ez az egységes adatbázis közvetlenül nem elérhető, de mivel az Anna Archívum teljesen nyílt forráskódú, viszonylag könnyen <a %(a_generated)s>generálható</a> vagy <a %(a_downloaded)s>letölthető</a> ElasticSearch és MariaDB adatbázisokként. Az ezen az oldalon található szkriptek automatikusan letöltik az összes szükséges metaadatot a fent említett forrásokból. Ha szeretné felfedezni adatainkat, mielőtt ezeket a szkripteket helyben futtatná, megtekintheti JSON fájljainkat, amelyek további JSON fájlokra mutatnak. <a %(a_json)s>Ez a fájl</a> jó kiindulópont. Egységes adatbázis Torrentek Anna Archívumától böngészés keresés Különféle kisebb vagy egyszeri források. Arra bátorítjuk az embereket, hogy először más árnyékkönyvtárakba töltsenek fel, de néha az embereknek olyan gyűjteményeik vannak, amelyek túl nagyok ahhoz, hogy mások átnézzék, de nem elég nagyok ahhoz, hogy saját kategóriát érdemeljenek. Áttekintés a <a %(a1)s>datasets oldalról</a>. Az <a %(a_href)s>aaaaarg.fail</a>-ról. Elég teljesnek tűnik. Önkéntesünktől, „cgiym”-től. Az <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentből. Elég nagy átfedés van a meglévő tanulmánygyűjteményekkel, de nagyon kevés MD5 egyezés, ezért úgy döntöttünk, hogy teljesen megtartjuk. Az <q>iRead eBooks</q> (fonetikusan <q>ai rit i-books</q>; airitibooks.com) lekaparása, <q>j</q> önkéntes által. Megfelel az <q>airitibooks</q> metadata-nak az <a %(a1)s><q>Egyéb metadata lekaparások</q></a> között. A <a %(a1)s><q>Bibliotheca Alexandrina</q></a> gyűjteményből. Részben az eredeti forrásból, részben a the-eye.eu-ról, részben más tükrökről. Egy privát könyv torrent weboldalról, <a %(a_href)s>Bibliotik</a> (gyakran „Bib”-ként emlegetik), amelynek könyveit név szerint (A.torrent, B.torrent) csomagolták torrentekbe és terjesztették a the-eye.eu-n keresztül. Önkéntesünktől, „bpb9v”-től. További információ a <a %(a_href)s>CADAL</a>-ról, lásd a megjegyzéseket a <a %(a_duxiu)s>DuXiu adatállomány oldalán</a>. További anyagok önkéntesünktől, „bpb9v”-től, főként DuXiu fájlok, valamint egy „WenQu” és „SuperStar_Journals” mappa (a SuperStar a DuXiu mögött álló cég). Önkéntesünk, „cgiym” által, különféle forrásokból származó kínai szövegek (al-könyvtárakként ábrázolva), beleértve a <a %(a_href)s>China Machine Press</a>-t (egy jelentős kínai kiadó). Nem kínai gyűjtemények (al-könyvtárakként ábrázolva) önkéntesünktől, „cgiym”-től. Kínai építészetről szóló könyvek lekaparása, <q>cm</q> önkéntes által: <q>Hálózati sebezhetőség kihasználásával szereztem meg a kiadónál, de azóta ezt a rést bezárták</q>. Megfelel a <q>chinese_architecture</q> metadata-nak az <a %(a1)s><q>Egyéb metadata lekaparások</q></a> között. Könyvek a <a %(a_href)s>De Gruyter</a> tudományos kiadótól, néhány nagy torrentből gyűjtve. A <a %(a_href)s>docer.pl</a> lekaparása, egy lengyel fájlmegosztó weboldal, amely könyvekre és más írott művekre összpontosít. 2023 végén kaparta le önkéntes „p”. Nincs jó metaadatunk az eredeti weboldalról (még fájlkiterjesztések sem), de könyvszerű fájlokat szűrtünk, és gyakran sikerült metaadatokat kinyerni magukból a fájlokból. DuXiu epubok, közvetlenül a DuXiu-tól, „w” önkéntes által gyűjtve. Csak a legújabb DuXiu könyvek érhetők el közvetlenül e-könyveken keresztül, így ezek többsége valószínűleg új. A fennmaradó DuXiu fájlok „m” önkéntestől, amelyek nem voltak a DuXiu saját PDG formátumában (a fő <a %(a_href)s>DuXiu adatállomány</a>). Sok eredeti forrásból gyűjtve, sajnos anélkül, hogy ezeket a forrásokat megőrizték volna a fájlútvonalban. <span></span> <span></span> <span></span> Erotikus könyvek lekaparása, <q>do no harm</q> önkéntes által. Megfelel a <q>hentai</q> metadata-nak az <a %(a1)s><q>Egyéb metadata lekaparások</q></a> között. <span></span> <span></span> Gyűjtemény, amelyet egy japán manga kiadótól kapart le „t” önkéntes. <a %(a_href)s>Longquan válogatott bírósági archívumai</a>, „c” önkéntes által biztosítva. A <a %(a_href)s>magzdb.org</a> lekaparása, a Library Genesis szövetségese (a libgen.rs kezdőlapján van linkelve), de nem akarták közvetlenül biztosítani a fájljaikat. „p” önkéntes szerezte meg 2023 végén. <span></span> Különféle kisebb feltöltések, amelyek túl kicsik ahhoz, hogy saját algyűjteményként szerepeljenek, de könyvtárakként vannak megjelenítve. E-könyvek az AvaxHome-ról, egy orosz fájlmegosztó weboldalról. Újságok és magazinok archívuma. Megfelel a <q>newsarch_magz</q> metadata-nak az <a %(a1)s><q>Egyéb metadata lekaparások</q></a> között. A <a %(a1)s>Philosophy Documentation Center</a> lekaparása. Önkéntes „o” gyűjteménye, aki lengyel könyveket gyűjtött közvetlenül az eredeti kiadási („scene”) weboldalakról. A <a %(a_href)s>shuge.org</a> önkéntesek „cgiym” és „woz9ts” által összeállított gyűjteményei. <span></span> <a %(a_href)s>„Trantor Birodalmi Könyvtára”</a> (a kitalált könyvtár után elnevezve), 2022-ben „t” önkéntes által lekaparva. <span></span> <span></span> <span></span> Al-al-gyűjtemények (könyvtárakként ábrázolva) „woz9ts” önkéntestől: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (által <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Tajvanon), mebook (mebook.cc, 我的小书屋, az én kis könyvszobám — woz9ts: „Ez az oldal főként magas minőségű e-könyv fájlok megosztására összpontosít, amelyek közül néhányat maga a tulajdonos szedett össze. A tulajdonost <a %(a_arrested)s>2019-ben letartóztatták</a>, és valaki gyűjteményt készített az általa megosztott fájlokból.”). A „woz9ts” önkéntestől származó fennmaradó DuXiu fájlok, amelyek nem voltak a DuXiu saját PDG formátumában (még PDF-re kell konvertálni). Az „upload” gyűjtemény kisebb algyűjteményekre van bontva, amelyeket az AACID-k és a torrent nevek jelölnek. Minden algyűjteményt először deduplikáltak a fő gyűjteménnyel szemben, bár a metaadat „upload_records” JSON fájlok még mindig sok hivatkozást tartalmaznak az eredeti fájlokra. A nem könyv típusú fájlokat is eltávolították a legtöbb algyűjteményből, és általában <em>nem</em> szerepelnek az „upload_records” JSON-ban. Az algyűjtemények a következők: Jegyzetek Algyűjtemény Sok algyűjtemény maga is al-algyűjteményekből áll (pl. különböző eredeti forrásokból), amelyeket a „fájlútvonal” mezőkben könyvtárakként ábrázolnak. Feltöltések Anna Archívumába Blogbejegyzésünk erről az adatról <a %(a_worldcat)s>A WorldCat</a> egy tulajdonosi adatbázis, amelyet a non-profit <a %(a_oclc)s>OCLC</a> üzemeltet, és amely könyvtárak metaadat-rekordjait gyűjti össze a világ minden tájáról. Valószínűleg ez a legnagyobb könyvtári metaadat-gyűjtemény a világon. 2023 októberében <a %(a_scrape)s>kiadtunk</a> egy átfogó lementést az OCLC (WorldCat) adatbázisról, az <a %(a_aac)s>Anna Archívum Konténerek formátumban</a>. 2023 október, kezdeti kiadás: OCLC (WorldCat) Torrentek az Anna Archívum által Példa rekord Anna Archívumában (eredeti gyűjtemény) Példa rekord Anna Archívumában („zlib3” gyűjtemény) Torrentezés Anna Archívumából (metaadatok + tartalom) Blogbejegyzés az 1. kiadásról Blogbejegyzés a 2. kiadásról 2022 végén a Z-Library állítólagos alapítóit letartóztatták, és a domaineket az Egyesült Államok hatóságai lefoglalták. Azóta a weboldal lassan újra elérhetővé válik. Nem ismert, hogy jelenleg ki üzemelteti. Frissítés 2023 februárjában. A Z-Library gyökerei a <a %(a_href)s>Library Genesis</a> közösségben találhatók, és eredetileg az ő adataikkal indult. Azóta jelentősen professzionalizálódott, és sokkal modernebb felülettel rendelkezik. Ezért képesek sokkal több adományt szerezni, mind pénzügyileg a weboldaluk fejlesztéséhez, mind új könyvek adományozásával. Nagy gyűjteményt halmoztak fel a Library Genesis mellett. A gyűjtemény három részből áll. Az első két rész eredeti leíró oldalai alább megmaradtak. Az összes adat megszerzéséhez mindhárom részre szükség van (kivéve a felülírt torrenteket, amelyek áthúzva jelennek meg a torrentek oldalán). %(title)s: az első kiadásunk. Ez volt az első kiadás, amelyet akkoriban „Kalóz Könyvtár Tükör” („pilimi”) néven hívtak. %(title)s: második kiadás, ezúttal minden fájl .tar fájlokba csomagolva. %(title)s: új, fokozatos kiadások, az <a %(a_href)s>Anna Archívuma Konténerek (AAC) formátum</a> használatával, most a Z-Library csapatával együttműködésben kiadva. Az első tükröt fáradságos munkával szereztük meg 2021 és 2022 folyamán. Jelenleg kissé elavult: a gyűjtemény 2021 júniusi állapotát tükrözi. A jövőben frissíteni fogjuk. Jelenleg az első kiadás megjelentetésére összpontosítunk. Mivel a Library Genesis már megőrzésre került nyilvános torrentekkel, és szerepel a Z-Library-ben, 2022 júniusában alapvető deduplikációt végeztünk a Library Genesis ellen. Ehhez MD5 hash-eket használtunk. Valószínűleg sokkal több duplikált tartalom van a könyvtárban, például ugyanazon könyv több fájlformátuma. Ezt nehéz pontosan észlelni, ezért nem tesszük. A deduplikáció után több mint 2 millió fájl maradt, összesen alig 7TB. A gyűjtemény két részből áll: egy MySQL „.sql.gz” metaadat-dumpból és a 72 torrent fájlból, amelyek mindegyike körülbelül 50-100GB. A metaadatok tartalmazzák a Z-Library weboldal által jelentett adatokat (cím, szerző, leírás, fájltípus), valamint a tényleges fájlméretet és az általunk megfigyelt md5sum-ot, mivel néha ezek nem egyeznek. Úgy tűnik, hogy vannak fájlok, amelyekhez a Z-Library maga is helytelen metaadatokat tartalmaz. Előfordulhat, hogy néhány esetben helytelenül letöltött fájlokkal rendelkezünk, amelyeket a jövőben megpróbálunk észlelni és javítani. A nagy torrent fájlok tartalmazzák a tényleges könyv adatokat, a Z-Library azonosítóval mint fájlnév. A fájlkiterjesztések a metaadat dump segítségével rekonstruálhatók. A gyűjtemény keveréke a nem-fikciós és fikciós tartalomnak (nem különítve el, mint a Library Genesis-ben). A minőség is széles skálán változik. Ez az első kiadás most teljesen elérhető. Vegye figyelembe, hogy a torrent fájlok csak a Tor tükrünkön keresztül érhetők el. 1. kiadás (%(date)s) Ez egyetlen extra torrent fájl. Nem tartalmaz új információt, de van benne néhány adat, amelynek kiszámítása időbe telhet. Ezért kényelmes, ha megvan, mivel ennek a torrentnek a letöltése gyakran gyorsabb, mint a nulláról való kiszámítása. Különösen tartalmaz SQLite indexeket a tar fájlokhoz, a <a %(a_href)s>ratarmount</a> használatához. 2. kiadás kiegészítése (%(date)s) Minden könyvet megszereztünk, amelyeket a Z-Library-hez adtak hozzá az utolsó tükrünk és 2022 augusztusa között. Visszamentünk és lekapartunk néhány könyvet, amelyeket először kihagytunk. Összességében ez az új gyűjtemény körülbelül 24TB. Ismételten, ez a gyűjtemény deduplikálva van a Library Genesis ellen, mivel már elérhetők torrentek ehhez a gyűjteményhez. Az adatok hasonlóan vannak szervezve, mint az első kiadásban. Van egy MySQL „.sql.gz” dump a metaadatokról, amely az első kiadás összes metaadatát is tartalmazza, így felülírja azt. Néhány új oszlopot is hozzáadtunk: Ezt már említettük legutóbb, de hogy tisztázzuk: a „fájlnév” és az „md5” a fájl tényleges tulajdonságai, míg a „fájlnév_jelentett” és az „md5_jelentett” az, amit a Z-Library-ből kapartunk le. Néha ezek nem egyeznek meg egymással, ezért mindkettőt belefoglaltuk. Ehhez a kiadáshoz megváltoztattuk a rendezést „utf8mb4_unicode_ci”-re, amely kompatibilis kell legyen a MySQL régebbi verzióival. Az adatfájlok hasonlóak, mint legutóbb, bár sokkal nagyobbak. Egyszerűen nem volt kedvünk rengeteg kisebb torrent fájlt létrehozni. A „pilimi-zlib2-0-14679999-extra.torrent” tartalmazza az összes fájlt, amit az utolsó kiadásban kihagytunk, míg a többi torrent mind új azonosító tartományokat tartalmaz.  <strong>Frissítés %(date)s:</strong> A legtöbb torrentünket túl naggyá tettük, ami miatt a torrent kliensek nehezen boldogultak. Eltávolítottuk őket és új torrenteket adtunk ki. <strong>Frissítés %(date)s:</strong> Még mindig túl sok fájl volt, ezért tar fájlokba csomagoltuk őket és új torrenteket adtunk ki ismét. %(key)s: hogy ez a fájl már benne van-e a Library Genesis-ben, akár a nem-fikció, akár a fikció gyűjteményben (md5 alapján egyező). %(key)s: melyik torrentben található ez a fájl. %(key)s: beállítva, amikor nem tudtuk letölteni a könyvet. 2. kiadás (%(date)s) Zlib kiadások (eredeti leíró oldalak) Tor domain Fő weboldal Z-Library lekaparás A Z-Library „kínai” gyűjteménye úgy tűnik, hogy megegyezik a DuXiu gyűjteményünkkel, de különböző MD5-ökkel. Ezeket a fájlokat kizárjuk a torrentekből a duplikáció elkerülése érdekében, de továbbra is megjelenítjük őket a keresési indexünkben. Metaadat Ön %(percentage)s%% bónusz gyors letöltést kap, mert a %(profile_link)s felhasználó ajánlotta. Ez az egész tagsági időszakra vonatkozik. Adományozás Csatlakozás Kiválasztva akár %(percentage)s%%-os kedvezmények Az Alipay támogatja a nemzetközi hitel-/betéti kártyákat. További információkért lásd <a %(a_alipay)s>ezt az útmutatót</a>. Küldjön nekünk Amazon.com ajándékkártyákat hitel-/betéti kártyájával. Kredit-/betéti kártyával vásárolhat kriptovalutát. A WeChat (Weixin Pay) támogatja a nemzetközi hitel-/betéti kártyákat. A WeChat alkalmazásban menjen a „Én => Szolgáltatások => Pénztárca => Kártya hozzáadása” menüpontra. Ha ezt nem látja, engedélyezze a „Én => Beállítások => Általános => Eszközök => Weixin Pay => Engedélyezés” menüpontban. (használja, amikor Ethereumot küld a Coinbase-ről) másolva! másolás (legalacsonyabb minimális összeg) (figyelem: magas minimális összeg) -%(percentage)s%% 12 hónap 1 hónap 24 hónap 3 hónap 48 hónap 6 hónap 96 hónap Válaszd ki, hogy mennyi időre szeretnél feliratkozni. <div %(div_monthly_cost)s></div><div %(div_after)s>után<span %(span_discount)s></span> kedvezmények</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 hónapra 1 hónapra 24 hónapra 3 hónapra 48 hónapra 6 hónapra 96 hónapra %(monthly_cost)s / hó lépjen kapcsolatba velünk Közvetlen <strong>SFTP</strong> szerverek Vállalati szintű adományozás vagy csere új gyűjteményekre (például. új szkennelések, OCR tartalom adatkészletek). Szakértői hozzáférés <strong>Korlátlan</strong> nagy sebességű hozzáférés <div %(div_question)s>Frissíthetem a tagságomat vagy szerezhetek több tagságot?</div> <div %(div_question)s>Adhatok adományt anélkül, hogy tag lennék?</div> Természetesen. Bármilyen összegű adományt elfogadunk erre a Monero (XMR) címre: %(address)s. <div %(div_question)s>Mit jelentenek a havi tartományok?</div> A tartomány alsó részéhez juthat, ha minden kedvezményt alkalmaz, például ha egy hónapnál hosszabb időszakot választ. <div %(div_question)s>Megújul a tagság automatikusan?</div> Tagságok <strong>nem újulnak meg</strong> automatikusan. Annyi hosszú vagy rövid időre csatlakozhatsz, amennyire csak szeretnél. <div %(div_question)s>Mire költitek az adományokat?</div> Az adományok 100%%-át a világ tudásának és kultúrájának megőrzésére és hozzáférhetővé tételére fordítjuk. Jelenleg főleg szerverekre, tárhelyre és sávszélességre költjük. A csapat egyetlen tagja sem kap pénzt személyesen. <div %(div_question)s>Tudok nagyobb összeget is adományozni?</div> An nagyszerű lenne! Ha több mint több ezer dollár értékben adományoznál, vedd fel velünk a kapcsolatot közvetlenül: %(email)s. <div %(div_question)s>Van más fizetési lehetőségetek?</div> Jelenleg nincs. Sokan szeretnék ha ilyen archívumok mint ez az oldal nem léteznének, úgyhogy óvatosnak kell lennünk. Ha tudsz segíteni (könnyebben használható) fizetési módszerek felállításában, lépj velünk kapcsolatba e-mailben %(email)s. Adományozás GYIK Már van egy <a %(a_donation)s>folyamatban lévő adományod</a>. Kérjük, mielőtt új felajánlást tennél, fejezd be vagy töröld azt az adományt. <a %(a_all_donations)s>Az összes adományom megtekintése</a> $5000 feletti adományok esetén lépj velünk közvetlenül kapcsolatba a következő címen: %(email)s. Nagy összegű adományokat szívesen fogadunk gazdag magánszemélyektől vagy intézményektől.  Legyen tisztában azzal, hogy bár az ezen az oldalon található tagságok „havonta” vannak, ezek egyszeri adományok (nem ismétlődőek). Lásd az <a %(faq)s>Adományozás GYIK</a> részt. Anna Archívuma egy nonprofit, nyílt forrású, nyílt adatú projekt. Adományozással és taggá válással támogathatod működésünket és fejlesztésünket. Minden tagunknak: köszönjük, hogy fenntartatok minket! ❤️ További információkért tekintse meg az <a %(a_donate)s>Adományozás GYIK</a> oldalt. Hogy taggá válhass, <a %(a_login)s>Lépj Be vagy Regisztrálj</a>. Köszönjük a támogatásodat! $%(cost)s / hónap Ha hibát követett el a fizetés során, nem tudunk visszatérítést biztosítani, de igyekszünk helyrehozni a dolgot. Találd meg a “Crypto” oldalt a PayPal alkalmazásban vagy a PayPal weboldalon. Ez általában a “Pénzügyek” alatt található. Menj a “Bitcoin” lapra a PayPal alkalmazásban vagy a weboldalon. Nyomd meg az “Átutalás” gombot %(transfer_icon)s, majd “Küldés” gombot. Alipay Alipay / WeChat Amazon Ajándékkártya %(amazon)s ajándékkártya Bankkártya Bankkártya (alkalmazás használatával) Binance Hitel-/betéti kártya/Apple/Google (BMC) Cash App Hitelkártya/bankkártya Hitelkártya/bankkártya 2 Hitel-/betéti kártya (tartalék) Kripto %(bitcoin_icon)s Kártya / PayPal / Venmo PayPal (USA) %(bitcoin_icon)s PayPal PayPal (rendszeres) Pix (Brazília) Revolut (átmenetileg nem elérhető) WeChat Válaszd ki az általad preferált kripto érmét: Adományozás Amazon ajándékkártya használatával. <strong>FONTOS:</strong> Ez az opció a %(amazon)s számára van. Ha egy másik Amazon weboldalt szeretne használni, válassza ki fent. <strong>FONTOS:</strong> Csak az Amazon.com-ot támogatjuk, más Amazon-weboldalakat nem. Például a .de, .co.uk, .ca oldalakat NEM támogatjuk. Kérem, NE írja meg saját üzenetét. Adja meg a pontos összeget: %(amount)s Vedd figyelembe, hogy a viszonteladóink által elfogadott összegekre kell kerekítenünk (minimum %(minimum)s). Adományozzon hitel-/betéti kártyával az Alipay alkalmazáson keresztül (szuper könnyű beállítani). Telepítse az Alipay alkalmazást az <a %(a_app_store)s>Apple App Store</a>-ból vagy a <a %(a_play_store)s>Google Play Store</a>-ból. Regisztráljon a telefonszámával. További személyes adatok megadása nem szükséges. <span %(style)s>1</span>Telepítse az Alipay alkalmazást Támogatott: Visa, MasterCard, JCB, Diners Club és Discover. További információért lásd <a %(a_alipay)s>ezt az útmutatót</a>. <span %(style)s>2</span>Adjon hozzá bankkártyát A Binance segítségével hitel-/betéti kártyával vagy bankszámlával vásárolhat Bitcoint, majd adományozhatja nekünk. Így biztonságosak és névtelenek maradhatunk az adományok fogadásakor. A Binance szinte minden országban elérhető, és támogatja a legtöbb bankot és hitel-/betéti kártyát. Jelenleg ez a fő ajánlásunk. Nagyra értékeljük, hogy időt szán arra, hogy megtanulja, hogyan adományozhat ezen a módon, mivel ez sokat segít nekünk. Hitelkártyák, bankkártyák, Apple Pay és Google Pay esetén a “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) szolgáltatást használjuk. Rendszerükben egy “kávé” 5 dollárnak felel meg, így az adománya az 5 dollár többszörösére lesz kerekítve. Adományozás Cash App használatával. Ha van Cash App-ed, ez a legegyszerűbb módja az adományozásnak! Megjegyzés: az %(amount)s alatti tranzakciók esetében a Cash App %(fee)s díjat számíthat fel. %(amount)s vagy annál nagyobb összegek esetén ingyenes! Adományozás hitel- vagy bankkártyával. Ez a módszer egy kriptovaluta szolgáltatót használ köztes átváltásként. Ez kissé zavaró lehet, ezért kérjük, csak akkor használja ezt a módszert, ha más fizetési módok nem működnek. Ez sem működik minden országban. Nem tudunk közvetlenül hitel-/betéti kártyákat elfogadni, mert a bankok nem akarnak velünk dolgozni. ☹ Azonban több mód is van a hitel-/betéti kártyák használatára más fizetési módokon keresztül: A kriptovaluták segítségével BTC, ETH, XMR és SOL segítségével adományozhatsz. Akkor használd ezt a lehetőséget, ha már jártas vagy a kriptovalutában. Kriptovalutával adományozhatsz BTC, ETH, XMR és más segítségével. Kripto expressz szolgáltatások Ha először használ kriptovalutát, javasoljuk, hogy használja a %(options)s-t Bitcoin (az eredeti és leggyakrabban használt kriptovaluta) vásárlására és adományozására. Megjegyzés: kis összegű adományok esetén a bankkártyadíjak eltörölhetik a %(discount)s%% kedvezményt, ezért hosszabb előfizetéseket ajánlunk. Adományozzon hitel-/bankkártyával, PayPal-lal vagy Venmo-val. A következő oldalon választhat ezek közül. A Google Pay és az Apple Pay is működhet. Vedd figyelembe, hogy a kis adományok esetében a díjak magasak, ezért hosszabb előfizetéseket ajánlunk. A PayPal US segítségével történő adományozáshoz a PayPal Crypto-t fogjuk használni, amely lehetővé teszi számunkra, hogy anonimak maradjunk. Köszönjük, hogy időt szánsz arra, hogy megtanulod, hogyan adományozhatsz ezzel a módszerrel, mivel ez sokat segít nekünk. Adományozás PayPal segítségével. Adományozzon a szokásos PayPal fiókjával. Adományozzon Revolut használatával. Ha van Revolut fiókja, ez a legegyszerűbb módja az adományozásnak! Ez a fizetési mód csak maximum %(amount)s engedélyez. Kérjük válasszon más időtartamot vagy fizetési módot. Ez a fizetési mód legalább %(amount)s összeget igényel. Kérjük válasszon más időtartamot vagy fizetési módot. Binance Coinbase Kraken Kérjük, válasszon fizetési módot. “Torrent örökbefogadása”: a felhasználóneved vagy üzeneted egy torrent fájlnévben <div %(div_months)s>12 havonta egyszer a tagság ideje alatt</div> A felhasználóneved vagy névtelen említés a kreditekben Korai hozzáférés új funkciókhoz Exkluzív Telegram színfalak mögötti hírekkel %(number)s gyors letöltés naponta ha ebben a hónapban adományoz! <a %(a_api)s>JSON API</a> hozzáférés Legendás státusz az emberiség tudásának és kultúrájának megőrzésében Korábbi előnyök, plusz: <strong>%(percentage)s%% bónusz letöltést</strong> szerezhet, ha <a %(a_refer)s>barátokat ajánl</a>. SciDB publikációk <strong>korlátlanul</strong> verifikáció nélkül Amikor fiókkal vagy adománnyal kapcsolatos kérdéseket tesz fel, adja meg fiókazonosítóját, képernyőképeket, nyugtákat, minél több információt. E-mailjeinket csak 1-2 hetente ellenőrizzük, így ha nem tartalmazza ezeket az információkat, az késlelteti a megoldást. További letöltésekhez <a %(a_refer)s>ajánlja barátait</a>! Kis önkéntes csapat vagyunk. 1-2 hétbe is telhet, mire válaszolunk. Lehet hogy az account neve vagy a kép az oldalon furán néz majd ki. Ne aggódj! Ezeket az accountokat az adományozási partnereink kezelik. Az accountjainkat nem törték fel. Adományozás <span %(span_cost)s></span> <span %(span_label)s></span> 12 hónapra “%(tier_name)s” 1 hónapra “%(tier_name)s” 24 hónapra “%(tier_name)s” 3 hónapra “%(tier_name)s” 48 hónapra „%(tier_name)s” 6 hónapra “%(tier_name)s” 96 hónapra “%(tier_name)s” Az adományozást még lemondhatod a kifizetés során. Kattints az adományozás gombra az adományozás megerősítéséhez. <strong>Fontos megjegyzés:</strong> A kriptodevizák árai erősen ingadozhatnak, akár 20%%-ot is változhatnak néhány perc alatt. Ez még mindig kevesebb, mint a díjak, amelyeket számos fizetési szolgáltatóval kell fizetnünk, akik gyakran 50-60%%-ot számítanak fel a hozzánk hasonló “árnyék jótékonysági szervezetekkel” való együttműködésért. <u>Ha a nyugtát az eredeti árral együtt küldöd el nekünk, akkor is jóváírjuk a fiókodon a választott tagságodat.</u> (amennyiben a nyugta nem régebb néhány óránál). Nagyra értékeljük, hogy hajlandó vagy eltűrni az ilyen dolgokat, hogy támogass minket! ❤️ ❌Valami rosszul sikerült. Töltsd újra az oldalt és próbáld meg újra. <span %(span_circle)s>1</span>Bitcoin vásárlása Paypalon <span %(span_circle)s>2</span>A Bitcoin átutalása a címünkre ✅ Átirányítás az adományozási oldalra… Adományozás Kérjük, várjon legalább <span %(span_hours)s>24 órát</span> (és frissítse ezt az oldalt), mielőtt kapcsolatba lépne velünk. Ha szeretne adományozni (bármilyen összeget) tagság nélkül, nyugodtan használja ezt a Monero (XMR) címet: %(address)s. Az ajándékkártya elküldése után automatizált rendszerünk néhány percen belül visszaigazolja azt. Ha ez nem működik, próbáld meg újra elküldeni az ajándékkártyát (<a %(a_instr)s>utasítások</a>). Ha még ez sem működik, írj nekünk egy e-mailt, és Anna kézzel átnézi (ez eltarthat néhány napig), és feltétlenül említsd meg, ha már próbáltad újra elküldeni. Például: Kérjük, használd a <a %(a_form)s>hivatalos Amazon.com űrlapot</a>, hogy küldj nekünk egy %(amount)s értékű ajándékkártyát az alábbi e-mail címre. “To” címzett e-mail címe az űrlapon: Amazon ajándékkártya Más ajándékkártyákat nem tudunk elfogadni, <strong>csak közvetlenül az Amazon.com hivatalos űrlapjáról</strong> küldött ajándékkártyákat. Nem tudjuk visszaküldeni az ajándékkártyát, ha nem ezt az űrlapot használja. Csak egyszer használható. Egyedi a fiókodhoz, ne oszd meg. Várakozás az ajándékkártyára... (frissítsd az oldalt az ellenőrzéshez) Nyissa meg a <a %(a_href)s>QR-kód adományozási oldalt</a>. Olvassa be a QR-kódot az Alipay alkalmazással, vagy nyomja meg a gombot az Alipay alkalmazás megnyitásához. Kérjük, legyen türelemmel; az oldal betöltése eltarthat egy ideig, mivel Kínában van. <span %(style)s>3</span>Tegyen adományt (QR-kód beolvasása vagy gomb megnyomása) Vásárolj PYUSD érmét PayPal-on Vásároljon Bitcoint (BTC) a Cash App-on Vásároljon egy kicsit többet (ajánljuk, hogy %(more)s többet), mint amennyit adományoz (%(amount)s), hogy fedezze a tranzakciós díjakat. A megmaradt összeget megtarthatja. Lépjen a Cash App „Bitcoin” (BTC) oldalára. Utalja át a Bitcoint a címünkre Kis adományok esetén (25 dollár alatt) szükség lehet a Rush vagy Priority használatára. Kattintson a „Send bitcoin” gombra, hogy „kivonást” hajtson végre. Váltson dollárról BTC-re a %(icon)s ikon megnyomásával. Adja meg az alábbi BTC összeget, majd kattintson a „Send” gombra. Ha elakad, nézze meg <a %(help_video)s>ezt a videót</a>. Az expressz szolgáltatások kényelmesek, de magasabb díjakat számítanak fel. Használhatja ezt kriptotőzsde helyett, ha gyorsan szeretne nagyobb adományt tenni, és nem bánja a 5-10 dolláros díjat. Ügyeljen arra, hogy a pontos kripto összeget küldje el, amely az adományozási oldalon látható, ne az $USD összeget. Ellenkező esetben a díjat levonják, és nem tudjuk automatikusan feldolgozni a tagságát. Néha a megerősítés akár 24 órát is igénybe vehet, ezért mindenképpen frissítse ezt az oldalt (még akkor is, ha lejárt). Hitelkártya / bankkártya utasítások Adományozás a hitelkártya / bankkártya oldalunkon keresztül A lépések egy része kriptotárcákat említ, de ne aggódj, ehhez nem kell semmit sem tanulnod a kriptovalutáról. %(coin_name)s instrukciók Vizsgálja be ezt a QR -kódot a Crypto Wallet alkalmazásával, hogy gyorsan kitöltse a fizetési részleteket Szkennelje be a QR -kódot a fizetéshez Csak a kriptovaluták standard verzióját támogatjuk, nem egzotikus hálózatokat vagy érmék verzióit. A tranzakció megerősítése akár egy órát is igénybe vehet, a kriptovalutától függően. %(amount)s adományozása <a %(a_page)s>ezen az oidalon</a>. Ez az adomány lejárt. Kérjük, töröld és hozz létre egy újat. Ha már fizetett: Igen, elküldtem a nyugtát e-mailben Ha a kriptovaluta árfolyama a tranzakció során ingadozott, mindenképpen mellékeld a nyugtát, amelyen az eredeti árfolyam szerepel. Nagyra értékeljük, hogy veszed a fáradtságot a kripto használatára, ez sokat segít nekünk! ❌ Valami rosszul sikerült. Töltsd újra az oldalt, és próbáld meg újra. <span %(span_circle)s>%(circle_number)s</span>Küldd el nekünk e-mailben a nyugtát Ha bármilyen problémába ütköznél, kérjük, lépj velünk kapcsolatba a(z) %(email)s címen, és adj meg minél több információt (például képernyőképeket). ✅ Köszönjük az adományt! Anna néhány napon belül manuálisan aktiválja a tagságodat. Küldd el a nyugtát vagy képernyőképet a személyes ellenőrző címedre: Ha elküldted e-mailben a nyugtát, kattints erre a gombra, hogy Anna manuálisan átnézhesse azt (ez eltarthat néhány napig): Küldjön nyugtát vagy képernyőképet a személyes ellenőrzési címére. NE használja ezt az e-mail címet a PayPal adományához. Visszavonás Igen, mondja vissza Biztos, hogy vissza akarod vonni? Ne vond vissza, ha már fizettél. ❌ Valami rosszul sikerült. Kérjük, töltsd újra az oldalt, és próbáld újra. Új adomány létrehozása ✅ Adományát visszavontuk. Dátum: %(date)s Azonosító: %(id)s Újrarendezés Állapot: <span %(span_label)s>%(label)s</span> Teljes: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / hó %(duration)s hónapra %(discounts)s%% árengedménnyel)</span> Teljes: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / hó %(duration)s hónapra)</span> 1. Add meg az e-mailed. 2. Válaszd ki a fizetési módot. 3. Ismét válaszd ki a fizetési módot. Válaszd a “Self-hosted” pénztárcát. 5. Kattints a "Megerősítem a tulajdonjogot" gombra. 6. Kapni fogsz egy e-mailes átvételi elismervényt. Kérjük, küldd el nekünk, és mi a lehető leghamarabb visszaigazoljuk adományodat. (lehet, hogy törölnöd kell és létre kell hoznod egy új adományt) A fizetési utasítások már elavultak. Ha újabb adományt szeretnél tenni, használd a fenti "Újrarendelés" gombot. Már fizettél. Ha mégis át szeretnéd nézni a fizetési utasításokat, kattints ide: Régi fizetési utasítások megjelenítése Ha a támogatási oldal blokkolva van, próbáljon meg egy másik internetkapcsolatot (pl. VPN vagy mobilinternet). Sajnos az Alipay oldal gyakran csak <strong>Kína szárazföldi területéről</strong> érhető el. Lehet, hogy ideiglenesen le kell tiltania a VPN-jét, vagy használnia kell egy VPN-t Kína szárazföldi területére (vagy néha Hongkong is működik). <span %(span_circle)s>1</span>Adományozás Alipayen Adományozza a teljes összeget %(total)s ezen az <a %(a_account)s>Alipay fiókon</a> keresztül Alipay utasítások <span %(span_circle)s>1</span>Átutalás kriptószámláink egyikére Adományozd a teljes %(total)s értékű összeget ezen címek egyikére: Kripto utasítások Kövesd az utasításokat a Bitcoin (BTC) vásárlásához. Csak annyit kell vásárolnod, amennyit adományozni szeretnél, %(total)s. Add meg a Bitcoin (BTC) címünket címzettként, és kövesd az utasításokat a(z) %(total)s értékű adomány elküldéséhez: <span %(span_circle)s>1</span>Adományozás Pixen Adományozd a teljes %(total)s értékű összeget <a %(a_account)s> ezt a Pix fiókot használva Pix utasítások <span %(span_circle)s>1</span>Adományozzon WeChaten Adományozza a teljes %(total)s összeget <a %(a_account)s>ezen a WeChat fiókon</a> keresztül WeChat utasítások Használja bármelyik alábbi „hitelkártya Bitcoinra” expressz szolgáltatást, amelyek csak néhány percet vesznek igénybe: BTC / Bitcoin cím (külső pénztárca): BTC / Bitcoin összeg: Töltse ki az alábbi adatokat az űrlapon: Ha bármelyik információ elavult, kérjük, írjon nekünk e-mailt, hogy értesítsen minket. Kérjük, használja ezt a <span %(underline)s>pontos összeget</span>. Teljes költsége magasabb lehet a hitelkártya díjak miatt. Kis összegeknél ez sajnos több lehet, mint a kedvezményünk. (minimum: %(minimum)s, az első tranzakcióhoz nincs szükség ellenőrzésre) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, az első tranzakcióhoz nincs szükség ellenőrzésre) (minimum: %(minimum)s) (minimum: %(minimum)s az országtól függően, az első tranzakcióhoz nincs szükség ellenőrzésre) Kövesd az utasításokat a PYUSD érme vásárlásához (PayPal USD). Vásárolj egy kicsit többet (javasoljuk, hogy %(more)s-al többet), mint az adományozandó összeg (%(amount)s), a tranzakciós díjak fedezésére. A maradékot megtartod. Menj a "PYUSD" oldalra a PayPal alkalmazásban vagy a weboldalon. Nyomd meg az "Átutalás" gombot %(icon)s, majd a "Küldés" gombot. Állapot frissítése Az időzítő visszaállításához egyszerűen hozz létre egy új adományt. Feltétlenül az alábbi BTC összeget használja, <em>NE</em> eurót vagy dollárt, különben nem kapjuk meg a megfelelő összeget, és nem tudjuk automatikusan megerősíteni a tagságát. Vásároljon Bitcoint (BTC) a Revolut-on Vásároljon egy kicsit többet (ajánljuk, hogy %(more)s többet), mint amennyit adományoz (%(amount)s), hogy fedezze a tranzakciós díjakat. A megmaradt összeget megtarthatja. Lépjen a Revolut „Crypto” oldalára, hogy Bitcoint (BTC) vásároljon. Utalja át a Bitcoint a címünkre Kis adományok esetén (25 dollár alatt) szükség lehet a Rush vagy Priority használatára. Kattintson a „Send bitcoin” gombra, hogy „kivonást” hajtson végre. Váltson euróról BTC-re a %(icon)s ikon megnyomásával. Adja meg az alábbi BTC összeget, majd kattintson a „Send” gombra. Ha elakad, nézze meg <a %(help_video)s>ezt a videót</a>. Állapot: 1 2 Lépésről-lépésre útmutató Lásd az alábbi lépésről-lépésre útmutatót. Ellenkező esetben kizárhatják ebből a fiókból! Ha még nem tette meg, írja le a titkos kulcsát a bejelentkezéshez: Köszönjük az adományodat! Hátramaradt idő: Adomány %(amount)s átutalása a fiókba: %(account)s Megerősítésre vár (frissítsd az oldalt, hogy ellenőrizd)… Átvitelre vár ( frissítsd az oldalt, hogy ellenőrizd)… Korábban A gyors letöltések az elmúlt 24 órában beleszámítanak a napi limitbe. A Gyors Partner Szerverekről történő letöltéseket %(icon)s jelöli. Az utolsó 18 óra Még nincsenek letöltéseid. A letöltött fájlok nem nyilvánosak. Minden időpont UTC-ben van megadva. Letöltött fájlok Ha egy fájlt gyors és lassú letöltéssel is letöltöttél, az kétszer fog megjelenni. Ne aggódjon túl sokat, sokan töltenek le az általunk linkelt weboldalakról, és rendkívül ritka, hogy bajba kerüljenek. Azonban a biztonság érdekében javasoljuk egy VPN (fizetős) vagy <a %(a_tor)s>Tor</a> (ingyenes) használatát. Letöltöttem George Orwell 1984 című művét, jönni fog a rendőrség az ajtómhoz? Ön Anna! Ki Anna? Van egy stabil JSON API-nk a tagok számára, amely gyors letöltési URL-t biztosít: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (a dokumentáció magában a JSON-ban található). Más felhasználási esetekhez, mint például az összes fájlunk átnézése, egyedi keresés építése stb., javasoljuk <a %(a_generate)s>ElasticSearch</a> és <a %(a_download)s>MariaDB adatbázisaink</a> generálását vagy letöltését. A nyers adatok manuálisan is felfedezhetők <a %(a_explore)s>JSON fájlokon keresztül</a>. Nyers torrentlistánk <a %(a_torrents)s>JSON</a> formátumban is letölthető. Van API-juk? Nem tárolunk itt semmilyen szerzői joggal védett anyagot. Keresőmotor vagyunk, és mint ilyen, csak olyan metaadatokat indexelünk, amelyek már nyilvánosan elérhetők. Amikor ezekről a külső forrásokból tölt le, javasoljuk, hogy ellenőrizze a jogszabályokat az Ön joghatósága szerint, hogy mi engedélyezett. Nem vagyunk felelősek mások által tárolt tartalmakért. Ha panasza van az itt látottakkal kapcsolatban, a legjobb, ha felveszi a kapcsolatot az eredeti weboldallal. Rendszeresen frissítjük adatbázisunkat az ő változásaikkal. Ha valóban úgy gondolja, hogy érvényes DMCA panasszal rendelkezik, amelyre válaszolnunk kellene, kérjük, töltse ki a <a %(a_copyright)s>DMCA / Szerzői jogi panasz űrlapot</a>. Komolyan vesszük a panaszait, és a lehető leghamarabb válaszolunk. Hogyan jelenthetem a szerzői jog megsértését? Itt van néhány könyv, amelyek különleges jelentőséggel bírnak az árnyékkönyvtárak és a digitális megőrzés világában: Melyek a kedvenc könyvei? Szeretnénk emlékeztetni mindenkit, hogy minden kódunk és adatunk teljesen nyílt forráskódú. Ez egyedülálló az ilyen projektek között — nem tudunk más olyan projektről, amely hasonlóan hatalmas katalógussal rendelkezik és teljesen nyílt forráskódú. Nagyon örülnénk, ha bárki, aki úgy gondolja, hogy rosszul működtetjük a projektünket, venné a kódunkat és adatainkat, és létrehozná saját árnyékkönyvtárát! Ezt nem rosszindulatból mondjuk — őszintén úgy gondoljuk, hogy ez nagyszerű lenne, mivel ez mindenki számára magasabb szintet jelentene, és jobban megőrizné az emberiség örökségét. Utálom, ahogy ezt a projektet vezeted! Szeretnénk, ha az emberek <a %(a_mirrors)s>tükröket</a> állítanának fel, és anyagilag támogatni fogjuk ezt. Hogyan segíthetek? Valóban így van. Az inspirációnk az metaadatok gyűjtésére Aaron Swartz célja volt, hogy „minden valaha megjelent könyvnek legyen egy weboldala”, amelyhez létrehozta az <a %(a_openlib)s>Open Library</a>-t. Az a projekt jól haladt, de egyedi helyzetünk lehetővé teszi számunkra, hogy olyan metaadatokat szerezzünk, amelyeket ők nem tudnak. Egy másik inspiráció az volt, hogy megtudjuk, <a %(a_blog)s>hány könyv van a világon</a>, hogy kiszámolhassuk, hány könyvet kell még megmentenünk. Gyűjt metaadatokat? Vegye figyelembe, hogy a mhut.org bizonyos IP-tartományokat blokkol, így szükség lehet VPN-re. <strong>Android:</strong> Kattintson a jobb felső sarokban található hárompontos menüre, és válassza a „Hozzáadás a kezdőképernyőhöz” lehetőséget. <strong>iOS:</strong> Kattintson az „Megosztás” gombra alul, és válassza az „Hozzáadás a kezdőképernyőhöz” lehetőséget. Nincs hivatalos mobilalkalmazásunk, de telepítheted ezt a weboldalt alkalmazásként. Van mobilalkalmazásuk? Kérjük, küldje el őket az <a %(a_archive)s>Internet Archive</a>-nak. Ők megfelelően megőrzik azokat. Hogyan adományozhatok könyveket vagy más fizikai anyagokat? Hogyan kérhetek könyveket? <a %(a_blog)s>Anna Blogja</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — rendszeres frissítések <a %(a_software)s>Anna Archívuma</a> — a mi nyílt forráskódunk <a %(a_datasets)s>Adatkészletek</a> — az adatokról <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatív domainek Vannak további források az Anna Archívumról? <a %(a_translate)s>Fordítás az Anna Archívumán</a> — a fordítórendszerünk <a %(a_wikipedia)s>Wikipedia</a> — többet rólunk (kérjük, segítsen naprakészen tartani ezt az oldalt, vagy hozzon létre egyet a saját nyelvén!) Válassza ki a kívánt beállításokat, hagyja üresen a keresőmezőt, kattintson a „Keresés” gombra, majd könyvjelzőzze az oldalt a böngészője könyvjelző funkciójával. Hogyan menthetem el a keresési beállításaimat? Üdvözöljük a biztonsági kutatókat, hogy keressenek sebezhetőségeket rendszereinkben. Nagy hívei vagyunk a felelős közzétételnek. Lépjen kapcsolatba velünk <a %(a_contact)s>itt</a>. Jelenleg nem tudunk hibajavítási jutalmakat adni, kivéve azokat a sebezhetőségeket, amelyeknek <a %(a_link)s>potenciáljuk van az anonimitásunk veszélyeztetésére</a>, ezekért 10 000-50 000 dollár közötti jutalmakat kínálunk. Szeretnénk a jövőben szélesebb körű hibajavítási jutalmakat kínálni! Kérjük, vegye figyelembe, hogy a szociális mérnöki támadások kívül esnek a hatókörön. Ha érdekli az offenzív biztonság, és szeretne segíteni a világ tudásának és kultúrájának archiválásában, mindenképpen vegye fel velünk a kapcsolatot. Számos módon segíthet. Van felelős bejelentési programjuk? Egyszerűen nincs elég erőforrásunk ahhoz, hogy mindenkinek a világon nagy sebességű letöltéseket biztosítsunk, bármennyire is szeretnénk. Ha egy gazdag jótevő szeretne fellépni és ezt biztosítani számunkra, az hihetetlen lenne, de addig is, mindent megteszünk. Egy non-profit projekt vagyunk, amely alig tudja fenntartani magát adományokból. Ezért vezettünk be két rendszert az ingyenes letöltésekhez, partnereinkkel: megosztott szerverek lassú letöltésekkel, és kissé gyorsabb szerverek várólistával (hogy csökkentsük az egyidejű letöltők számát). Lassú letöltéseinkhez <a %(a_verification)s>böngésző ellenőrzést</a> is használunk, mert különben a botok és adatgyűjtők visszaélnének velük, ami még lassabbá tenné a letöltést a valódi felhasználók számára. Vegye figyelembe, hogy a Tor Böngésző használatakor szükség lehet a biztonsági beállítások módosítására. A legalacsonyabb opció, az úgynevezett „Standard” esetén a Cloudflare turnstile kihívás sikeres. A magasabb opciók, az úgynevezett „Safer” és „Safest” esetén a kihívás sikertelen. Nagy fájlok esetén előfordulhat, hogy a lassú letöltések megszakadnak a közepén. Javasoljuk egy letöltéskezelő (például JDownloader) használatát, amely automatikusan folytatja a nagy letöltéseket. Miért olyan lassúak a lassú letöltések? Gyakran Ismételt Kérdések (GYIK) Használja a <a %(a_list)s>torrent lista generátort</a>, hogy létrehozzon egy listát a leginkább torrentelésre szoruló torrentekről, a tárhely korlátain belül. Igen, lásd az <a %(a_llm)s>LLM adatok</a> oldalt. A legtöbb torrent közvetlenül tartalmazza a fájlokat, ami azt jelenti, hogy utasíthatja a torrent klienseket, hogy csak a szükséges fájlokat töltsék le. Annak meghatározásához, hogy mely fájlokat töltse le, <a %(a_generate)s>generálhatja</a> a metaadatainkat, vagy <a %(a_download)s>letöltheti</a> az ElasticSearch és MariaDB adatbázisainkat. Sajnos számos torrentgyűjtemény .zip vagy .tar fájlokat tartalmaz a gyökérben, ebben az esetben le kell töltenie az egész torrentet, mielőtt kiválaszthatná az egyes fájlokat. Még nem állnak rendelkezésre könnyen használható eszközök a torrentek szűrésére, de szívesen fogadjuk a hozzájárulásokat. (Van néhány <a %(a_ideas)s>ötletünk</a> az utóbbi esetre.) Hosszú válasz: Rövid válasz: nem könnyen. Igyekszünk minimálisra csökkenteni a duplikációt vagy átfedést a listában szereplő torrentek között, de ez nem mindig érhető el, és nagymértékben függ a forráskönyvtárak politikájától. Azoknál a könyvtáraknál, amelyek saját torrenteket bocsátanak ki, ez nem a mi kezünkben van. Az Anna Archívum által kiadott torrentek esetében csak az MD5 hash alapján deduplikálunk, ami azt jelenti, hogy ugyanazon könyv különböző verziói nem kerülnek deduplikálásra. Igen. Ezek valójában PDF-ek és EPUB-ok, csak sok torrentünkben nincs kiterjesztésük. Két helyen találhatja meg a torrent fájlok metaadatait, beleértve a fájltípusokat/kiterjesztéseket: 1. Minden gyűjteménynek vagy kiadásnak megvan a saját metaadata. Például a <a %(a_libgen_nonfic)s>Libgen.rs torrentek</a> rendelkeznek egy megfelelő metaadatbázissal, amely a Libgen.rs weboldalon található. Általában minden gyűjtemény <a %(a_datasets)s>adatkészlet oldaláról</a> hivatkozunk a releváns metaadat forrásokra. 2. Javasoljuk, hogy <a %(a_generate)s>generálja</a> vagy <a %(a_download)s>töltse le</a> az ElasticSearch és MariaDB adatbázisainkat. Ezek tartalmazzák az Anna’s Archive minden rekordjának megfelelő torrent fájlok térképét (ha elérhető), az ElasticSearch JSON „torrent_paths” mezőjében. Néhány torrent kliens nem támogatja a nagy darabméreteket, amelyek sok torrentünkben megtalálhatók (az újabbaknál már nem tesszük ezt — bár a specifikációk szerint érvényes!). Próbáljon ki egy másik klienst, ha ezzel találkozik, vagy panaszkodjon a torrent kliens készítőinek. Szeretnék segíteni a vetésben, de nincs sok lemezterületem. A torrentek túl lassúak; letölthetem közvetlenül az adatokat tőletek? Letölthetek csak egy részhalmazt a fájlokból, például csak egy adott nyelvet vagy témát? Hogyan kezelik a duplikátumokat a torrentekben? Megkaphatom a torrent listát JSON formátumban? Nem látok PDF-eket vagy EPUB-okat a torrentekben, csak bináris fájlokat? Mit tegyek? Miért nem tudja a torrent kliens megnyitni néhány torrent fájljukat / mágnes linkjüket? Torrentezés GYIK Hogyan tölthetek fel új könyveket? Kérjük, tekintse meg <a %(a_href)s>ezt a kiváló projektet</a>. Van rendelkezésére álló üzemidő-figyelő? Mi az az Anna Archívuma? Válj taggá a gyors letöltések használatához. Mostantól támogatjuk az Amazon ajándékkártyákat, hitel- és bankkártyákat, kriptovalutákat, Alipay-t és WeChat-et. A mai napon elfogytak a gyors letöltései. Hozzáférés Óránkénti letöltések az elmúlt 30 napban. Óránkénti átlag: %(hourly)s. Napi átlag: %(daily)s. Partnereinkkel együttműködve igyekszünk gyűjteményeinket bárki számára könnyen és szabadon hozzáférhetővé tenni. Hisszük, hogy mindenkinek joga van az emberiség kollektív bölcsességéhez. És <a %(a_search)s>nem a szerzők rovására</a>. Az Anna Archívumában használt adatkészletek teljesen nyíltak, és torrentek segítségével tömegesen tükrözhetők. <a %(a_datasets)s>Bővebben...</a> Hosszú távú archívum Teljes adatbázis Keresés Könyvek, kiadványok, folyóiratok, képregények, könyvtári rekordok, metaadatok, … Minden <a %(a_code)s>kódunk</a> és <a %(a_datasets)s>adatunk</a> teljesen nyílt forráskódú. <span %(span_anna)s>Anna Archívuma</span> egy nonprofit projekt, amelynek két célja van: <li><strong>Megőrzés:</strong> Az emberiség minden tudásának és kultúrájának megőrzése.</li><li><strong>Hozzáférés:</strong> A tudás és a kultúra elérhetővé tétele bárki számára a világon.</li> Mi rendelkezünk a világ legnagyobb, kiváló minőségű szöveges adatgyűjteményével. <a %(a_llm)s >Bővebben...</a> LLM tréning adat 🪩 Tükrök: önkéntesek felhívása Ha magas kockázatú anonim fizetési feldolgozót üzemeltet, kérjük, lépjen kapcsolatba velünk. Olyan embereket is keresünk, akik ízléses kis hirdetéseket szeretnének elhelyezni. Minden bevétel a megőrzési erőfeszítéseinket támogatja. Megőrzés Becsléseink szerint a világ könyveinek körülbelül <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%%-át őriztük meg.</a>. Könyveket, tanulmányokat, képregényeket, magazinokat és még sok mást őrzünk meg azáltal, hogy ezeket az anyagokat különböző <a href="https://en.wikipedia.org/wiki/Shadow_library">árnyékkönyvtárakból</a>, hivatalos könyvtárakból és más gyűjteményekből egy helyre hozzuk. Mindezeket az adatokat örökre megőrizzük azáltal, hogy könnyen tömegesen másolhatóvá tesszük őket — torrentek használatával —, ami sok másolatot eredményez a világ minden táján. Néhány árnyékkönyvtár már maga is ezt teszi (pl. Sci-Hub, Library Genesis), míg az Anna Archívuma „felszabadít” más könyvtárakat, amelyek nem kínálnak tömeges terjesztést (pl. Z-Library), vagy egyáltalán nem árnyékkönyvtárak (pl. Internet Archive, DuXiu). Ez a széles körű terjesztés, kombinálva a nyílt forráskódú kóddal, ellenállóvá teszi weboldalunkat a leállításokkal szemben, és biztosítja az emberiség tudásának és kultúrájának hosszú távú megőrzését. Tudjon meg többet <a href="/datasets">adatkészleteinkről</a>. Ha <a %(a_member)s>tag</a> vagy, a böngésző ellenőrzése nem szükséges. 🧬&nbsp;SciDB a Sci-Hub folytatása. SciDB Megnyitás DOI A Sci-Hub <a %(a_paused)s>szüneteltette</a> az új cikkek feltöltését. Közvetlen hozzáférés a %(count)s tudományos publikációkhoz 🧬&nbsp;A SciDB a Sci-Hub folytatása, ismerős felülettel és közvetlen PDF-megtekintéssel. Adja meg a DOI-t a megtekintéshez. Rendelkezünk a teljes Sci-Hub gyűjteménnyel, valamint új tanulmányokkal. A legtöbb közvetlenül megtekinthető egy ismerős felületen, hasonlóan a Sci-Hubhoz. Néhány külső forrásból tölthető le, ebben az esetben linkeket mutatunk hozzájuk. Hatalmasat segíthetsz torrentek vetésével. <a %(a_torrents)s>Tanulj többet…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Önkénteseket keresünk Nonprofit, nyílt forráskódú projektként mindig keresünk embereket, akik segítenének. IPFS letöltések Lista %(by)s által, készítve: <span %(span_time)s>%(time)s</span> Mentés ❌ Valami rosszul sikerült. Töltsd újra az oldalt, és próbáld meg újra. ✅ Mentve. Töltsd újra az oldalt. Üres lista. szerkesztés Hozzáadhatsz vagy eltávolíthatsz ebből a listából, ha megkeresel egy fájlt, és megnyitod a “Listák” fület. Lista Hogyan segíthetünk Átfedések eltávolítása (deduplikáció) Szöveg- és metaadat-kivonás OCR Képesek vagyunk nagy sebességű hozzáférést biztosítani teljes gyűjteményeinkhez, valamint kiadatlan gyűjteményekhez. Ez vállalati szintű hozzáférés, amelyet több tízezer USD adományért tudunk biztosítani. Hajlandóak vagyunk ezt cserélni olyan kiváló minőségű gyűjteményekre is, amelyek még nincsenek meg nekünk. Visszatérítést tudunk nyújtani, ha képesek vagyunk adataink gazdagítására, például: Támogassa az emberi tudás hosszú távú archiválását, miközben jobb adatokat kap a modelljéhez! <a %(a_contact)s>Lépjen kapcsolatba velünk</a>, hogy megbeszéljük, hogyan dolgozhatunk együtt. Közismert, hogy az LLM-ek a kiváló minőségű adatokon virágoznak. Nekünk van a világ legnagyobb gyűjteménye könyvekből, tanulmányokból, magazinokból stb., amelyek a legmagasabb minőségű szövegforrások közé tartoznak. LLM adatok Egyedülálló méret és tartomány Gyűjteményünk több mint százmillió fájlt tartalmaz, beleértve tudományos folyóiratokat, tankönyveket és magazinokat. Ezt a méretet úgy érjük el, hogy nagy meglévő tárolókat kombinálunk. Néhány forrásgyűjteményünk már elérhető tömegesen (Sci-Hub és a Libgen egyes részei). Más forrásokat mi magunk szabadítottunk fel. A <a %(a_datasets)s>Datasets</a> teljes áttekintést nyújt. Gyűjteményünk milliónyi könyvet, tanulmányt és magazint tartalmaz az e-könyv korszak előttről. E gyűjtemény nagy része már OCR-elve van, és már kevés belső átfedéssel rendelkezik. Következő Ha elvesztette a kulcsát, kérjük, <a %(a_contact)s>lépjen kapcsolatba velünk</a>, és adjon meg minél több információt. Előfordulhat, hogy ideiglenesen új fiókot kell létrehoznia, hogy kapcsolatba léphessen velünk. Kérjük, <a %(a_account)s>jelentkezzen be</a> az oldal megtekintéséhez.</a> Hogy megakadályozzuk, hogy a spam-botok sok fiókot hozzanak létre, először ellenőriznünk kell a böngészőt. Ha végtelen hurokba kerül, javasoljuk a <a %(a_privacypass)s>Privacy Pass</a> telepítését. Az is segíthet, ha kikapcsolja a hirdetésblokkolókat és más böngészőbővítményeket. Bejelentkezés / Regisztráció Anna Archívuma ideiglenesen karbantartás miatt nem elérhető. Kérjük, térjen vissza egy óra múlva. Alternatív szerző Alternatív leírás Alternatív kiadás Alternatív kiterjesztés Alternatív fájlnév Alternatív kiadó Alternatív cím nyílt forráskódúvá válás dátuma Olvasson tovább… leírás Keresés Anna Archívumában CADAL SSNO szám alapján Keressen DuXiu SSID számot az Anna Archívumában Keresés Anna Archívumában DuXiu DXID szám alapján ISBN keresése Anna Archívumában Keressen Anna archívumában az OCLC (WorldCat) számot Open Library ID keresése Anna Archívumában Anna Archívuma online néző %(count)s érintett oldalak Letöltés után: Ennek a fájlnak egy jobb változata lehet elérhető itt: %(link)s Tömeges torrent letöltések gyűjtemény Használjon online eszközöket a formátumok közötti átalakításhoz. Ajánlott átalakító eszközök: %(links)s Nagy fájlok esetén javasoljuk letöltéskezelő használatát a megszakítások elkerülése érdekében. Ajánlott letöltéskezelők: %(links)s EBSCOhost eBook Index (csak szakértők) (kattints a „GET” gombra is a tetején) (kattints felül a „GET” gombra) Külső letöltések <strong>🚀 Gyors letöltések</strong> Ma még %(remaining)s van hátra. Köszönjük, hogy tag vagy! ❤️ <strong>🚀 Gyors letöltések</strong> A mai napon elfogytak a gyors letöltéseid. <strong>🚀 Gyors letöltések</strong> Ezt a fájlt nemrég töltötted le. A linkek egy ideig érvényesek maradnak. <strong>🚀 Gyors letöltések</strong> Válj <a %(a_membership)s>taggá</a>, hogy támogasd a könyvek, cikkek és egyéb dokumentumok hosszútávú megőrzését. Hálánk jeléül a támogatásodért gyors letöltéseket kapsz. ❤️ 🚀 Gyors letöltések 🐢 Lassú letöltések Kölcsönzés az Internet Archive-ból IPFS Átjáró #%(num)d (lehet, hogy többször meg kell próbálnia az IPFS-el) Libgen.li Libgen.rs Fikció Libgen.rs Nem-Fikció hirdetéseikről ismert, hogy rosszindulatú szoftvereket tartalmaznak, ezért használjon hirdetésblokkolót, vagy ne kattintson a hirdetésekre Amazon „Küldés Kindle-re” djazz „Küldés Kobo/Kindle-re” MagzDB ManualsLib Nexus/STC (A Nexus/STC fájlok letöltése megbízhatatlan lehet) Nem található letöltés. Minden tükör ugyanazt a fájlt szolgálja ki, és biztonságosan használhatónak kell lennie. Ennek ellenére mindig legyen óvatos, amikor fájlokat tölt le az internetről. Például ügyeljen arra, hogy eszközeit folyamatosan frissítse. (nincs átirányítás) Megnyitás a nézőnkben (megnyitás nézőben) Opció #%(num)d: %(link)s %(extra)s Eredeti rekord keresése a CADAL-ban Keresés manuálisan a DuXiu-n Eredeti nyilvántartás keresése ISBNdb-n Keresse meg az eredeti rekordot a WorldCatben Eredeti nyilvántartás keresése Open Library-ben ISBN keresése több különböző adatbázisokban (nyomtatás csak a támogatók számára engedélyezett) PubMed A fájl megnyitásához ebook vagy PDF olvasóra lesz szüksége, a fájl formátumától függően. Ajánlott ebook olvasók: %(links)s Anna Archívuma🧬 SciDB Sci-Hub: %(doi)s (a hozzátartozó DOI lehet nem elérhető a Sci-Hub-on) PDF és EPUB fájlokat is küldhet Kindle vagy Kobo eReader készülékére. Ajánlott eszközök: %(links)s További információk a <a %(a_slow)s>GYIK</a>-ban. Támogassa a szerzőket és a könyvtárakat Ha tetszik ez, és megengedheti magának, fontolja meg az eredeti megvásárlását, vagy támogassa közvetlenül a szerzőket. Ha elérhető a helyi könyvtárában, fontolja meg, hogy ott kölcsönözze ki ingyen. Partner Szerver letöltések ideiglenesen nem elérhetők ehhez a fájlhoz. torrent Megbízható partnerektől. Z-Library Z-Library a Tor-on (Tor Böngészőt igényel) külső letöltések megjelenítése <span class="font-bold">❌Ez a fájl problémás lehet, ezért elrejtettük a forrás könyvtárat.</span> Néha ez a szerzői jog jogosultja kérésére történik, néha pedig azért, mert egy jobb alternatíva elérhető, de néha azért mert maga a fájl problémás. Lehet hogy ettől még le lehet tölteni, de mi azt javasoljuk hogy keress egy alternatív fájlt. Több részlet: Ha mégis letöltöd ezt a fájlt, csak megbízható és naprakész programmal nyisd meg. metaadat megjegyzések AA: Keressen az Anna Archívumában a következőre: “%(name)s” Kódok Felfedezője: Megtekintés a Kódok Felfedezőjében „%(name)s” URL: Weboldal: Ha megvan ez a fájl, és még nem érhető el az Anna Archívumában, fontolja meg <a %(a_request)s>feltöltését</a>. Internet Archive Kontrollált Digitális Kölcsönzés fájl "%(id)s" Ez egy fájl rekordja az Internet Archive-ból, nem közvetlenül letölthető fájl. Megpróbálhatod kölcsönkérni a könyvet (lenti link), vagy használhatod ezt az URL-t, amikor <a %(a_request)s>fájlt kérsz</a>. Metaadatok javítása CADAL SSNO %(id)s metaadat rekord Ez egy metaadat nyilvántartás, nem egy letölthető fájl. Ezt az URL-t használhatja, amikor <a %(a_request)s>fájlt kér</a>. DuXiu SSID %(id)s metaadat rekord ISBNdb %(id)s metaadat nyilvántartás MagzDB ID %(id)s metaadat rekord Nexus/STC ID %(id)s metaadat rekord OCLC (WorldCat) szám %(id)s metaadat rekord Open Library %(id)s metaadat nyilvántartás Sci-Hub fájl “%(id)s” Nincs találat “%(md5_input)s” nem található az adatbázisunkban. Hozzászólás hozzáadása (%(count)s) Az md5-öt az URL-ből szerezheted meg, pl. E fájl jobb verziójának MD5-je (ha alkalmazható). Töltsd ki ezt, ha van egy másik fájl, amely szorosan illeszkedik ehhez a fájlhoz (ugyanaz a kiadás, ugyanaz a fájlkiterjesztés, ha találsz ilyet), amelyet az embereknek ennek a fájlnak a helyett kellene használniuk. Ha tudsz egy jobb verzióról ezen a fájlon kívül Anna’s Archive-ban, akkor kérjük, <a %(a_upload)s>töltsd fel</a>. Valami hiba történt. Kérjük, töltse újra az oldalt, és próbálja meg újra. Hozzászólást hagyott. Előfordulhat, hogy egy percbe telik, mire megjelenik. Kérjük, használd a <a %(a_copyright)s>DMCA / Szerzői jogi igénylőlapot</a>. Írd le a problémát (kötelező) Ha ez a fájl kiváló minőségű, itt bármit megbeszélhet róla! Ha nem, kérjük, használja a „Fájlprobléma jelentése” gombot. Kiváló fájlminőség (%(count)s) Fájl minősége Tanulja meg, hogyan <a %(a_metadata)s>javíthatja a metaadatokat</a> ehhez a fájlhoz saját maga. Probléma leírása Kérjük, <a %(a_login)s>jelentkezz be</a>. Imádtam ezt a könyvet! Segítsd a közösséget a fájl minőségének jelentésével! 🙌 Valami hiba történt. Kérjük, töltse újra az oldalt, és próbálja meg újra. Fájlprobléma jelentése (%(count)s) Köszönjük, hogy beküldte jelentését. Ez az oldalon megjelenik, és manuálisan is át lesz nézve Anna által (amíg nem lesz megfelelő moderációs rendszerünk). Hozzászólás írása Jelentés beküldése Mi a probléma ezzel a fájllal? Kölcsönzés (%(count)s) Hozzászólások (%(count)s) Letöltések (%(count)s) Metaadatok felfedezése (%(count)s) Listák (%(count)s) Statisztikák (%(count)s) Az adott fájlra vonatkozó információkért tekintse meg a <a %(a_href)s>JSON fájlt</a>. Ez egy fájl, amelyet az <a %(a_ia)s>IA irányított digitális kölcsönzési</a> könyvtára kezel, és az Anna Archívuma indexelte a kereséshez. Az általunk összeállított különféle adatállományokról további információkért tekintse meg a <a %(a_datasets)s>Datasets oldalt</a>. Metaadat a kapcsolt rekordból Metaadat javítása az Open Library-n A „fájl MD5” egy hash, amelyet a fájl tartalmából számítanak ki, és ésszerűen egyedi az adott tartalom alapján. Az összes árnyékkönyvtár, amelyet itt indexeltünk, elsősorban az MD5-öket használja a fájlok azonosítására. Egy fájl több árnyékkönyvtárban is megjelenhet. Az általunk összeállított különféle adatállományokról további információkért tekintse meg a <a %(a_datasets)s>Datasets oldalt</a>. Fájl minőségének jelentése Összes letöltés: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Cseh metaadat %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Könyvek %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Figyelem: több kapcsolt rekord: Amikor megnéz egy könyvet Anna Archívumában, különböző mezőket láthat: cím, szerző, kiadó, kiadás, év, leírás, fájlnév és még sok más. Mindezeket az információkat <em>metaadatoknak</em> nevezzük. Mivel különböző <em>forráskönyvtárakból</em> származó könyveket kombinálunk, megjelenítjük az adott forráskönyvtárban elérhető metaadatokat. Például, ha egy könyvet a Library Genesisből szereztünk, akkor a Library Genesis adatbázisából származó címet jelenítjük meg. Néha egy könyv <em>több</em> forráskönyvtárban is megtalálható, amelyek eltérő metaadat mezőkkel rendelkezhetnek. Ilyen esetben egyszerűen a leghosszabb verziót jelenítjük meg minden mezőből, mivel remélhetőleg az tartalmazza a leghasznosabb információkat! Az egyéb mezőket továbbra is megjelenítjük a leírás alatt, pl. „alternatív címként” (de csak akkor, ha eltérnek). Kivonjuk a <em>kódokat</em> is, mint például az azonosítókat és osztályozókat a forráskönyvtárból. Az <em>azonosítók</em> egy adott könyvkiadást egyedileg képviselnek; példák erre az ISBN, DOI, Open Library ID, Google Books ID vagy Amazon ID. Az <em>osztályozók</em> több hasonló könyvet csoportosítanak; példák erre a Dewey Decimal (DCC), UDC, LCC, RVK vagy GOST. Néha ezek a kódok kifejezetten kapcsolódnak a forráskönyvtárakban, és néha a fájlnévből vagy a leírásból tudjuk kivonni őket (elsősorban ISBN és DOI). Az azonosítókat felhasználhatjuk a <em>csak metaadatokat tartalmazó gyűjteményekben</em> található rekordok megtalálására, mint például az OpenLibrary, ISBNdb vagy WorldCat/OCLC. Keresőmotorunkban van egy külön <em>metaadat fül</em>, ha szeretné böngészni ezeket a gyűjteményeket. A megfelelő rekordokat használjuk a hiányzó metaadat mezők kitöltésére (pl. ha hiányzik egy cím), vagy pl. „alternatív címként” (ha már létezik egy cím). Ha pontosan szeretné látni, honnan származik egy könyv metaadata, nézze meg a <em>„Technikai részletek” fület</em> egy könyv oldalán. Itt található egy link a nyers JSON-hoz az adott könyvhöz, amely mutat a nyers JSON-ra az eredeti rekordokból. További információkért lásd a következő oldalakat: <a %(a_datasets)s>Adatkészletek</a>, <a %(a_search_metadata)s>Keresés (metaadat fül)</a>, <a %(a_codes)s>Kódok felfedezője</a>, és <a %(a_example)s>Példa metaadat JSON</a>. Végül, minden metaadatunk <a %(a_generated)s>generálható</a> vagy <a %(a_downloaded)s>letölthető</a> ElasticSearch és MariaDB adatbázisokként. Háttér Segíthet a könyvek megőrzésében a metaadatok javításával! Először olvassa el a metaadatok hátterét Anna Archívumában, majd tanulja meg, hogyan javíthatja a metaadatokat az Open Library-hoz való kapcsolódás révén, és szerezzen ingyenes tagságot Anna Archívumában. Metaadatok javítása Tehát ha talál egy fájlt rossz metaadatokkal, hogyan javíthatja ki? Elmehet a forráskönyvtárba és követheti annak eljárásait a metaadatok javítására, de mit tegyen, ha egy fájl több forráskönyvtárban is megtalálható? Van egy azonosító, amely különleges bánásmódban részesül Anna Archívumában. <strong>Az annas_archive md5 mező az Open Library-ban mindig felülír minden más metaadatot!</strong> Lépjünk egy kicsit vissza, és ismerjük meg az Open Library-t. Az Open Library-t 2006-ban alapította Aaron Swartz azzal a céllal, hogy „minden valaha megjelent könyvnek legyen egy weboldala”. Ez egyfajta Wikipédia a könyv metaadatok számára: mindenki szerkesztheti, szabadon licencelt, és tömegesen letölthető. Ez egy könyvadatbázis, amely leginkább összhangban van küldetésünkkel — valójában Anna Archívuma Aaron Swartz víziója és élete által inspirálódott. Ahelyett, hogy újra feltalálnánk a kereket, úgy döntöttünk, hogy önkénteseinket az Open Library felé irányítjuk. Ha lát egy könyvet, amelynek helytelenek a metaadatai, a következő módon segíthet: Vegye figyelembe, hogy ez csak könyvekre vonatkozik, nem tudományos cikkekre vagy más típusú fájlokra. Más típusú fájlok esetén továbbra is javasoljuk a forráskönyvtár megtalálását. Néhány hétbe telhet, mire a változtatások bekerülnek az Anna’s Archive-ba, mivel le kell töltenünk a legfrissebb Open Library adatdumpot, és újra kell generálnunk a keresési indexünket.  Menjen az <a %(a_openlib)s>Open Library weboldalára</a>. Keresse meg a megfelelő könyvrekordot. <strong>FIGYELEM:</strong> ügyeljen arra, hogy a megfelelő <strong>kiadást</strong> válassza ki. Az Open Library-ban vannak „művek” és „kiadások”. Egy „mű” lehet például „Harry Potter és a bölcsek köve”. Egy „kiadás” lehet: Az 1997-es első kiadás, amelyet a Bloomsbery adott ki, 256 oldallal. A 2003-as puhakötésű kiadás, amelyet a Raincoast Books adott ki, 223 oldallal. A 2000-es lengyel fordítás, „Harry Potter I Kamie Filozoficzn”, amelyet a Media Rodzina adott ki, 328 oldallal. Ezeknek a kiadásoknak mind különböző ISBN-jei és tartalmai vannak, ezért ügyeljen arra, hogy a megfelelőt válassza ki! Szerkessze a rekordot (vagy hozza létre, ha még nem létezik), és adjon hozzá minél több hasznos információt! Ha már itt van, akár igazán lenyűgözővé is teheti a rekordot. Az „ID Numbers” alatt válassza az „Anna’s Archive” lehetőséget, és adja hozzá a könyv MD5-jét az Anna’s Archive-ból. Ez a hosszú betű- és számsor a „/md5/” után az URL-ben. Próbáljon meg más fájlokat is találni az Anna’s Archive-ban, amelyek szintén megfelelnek ennek a rekordnak, és adja hozzá azokat is. A jövőben ezeket csoportosíthatjuk duplikátumként az Anna’s Archive keresőoldalán. Ha végzett, írja le az URL-t, amelyet éppen frissített. Miután legalább 30 rekordot frissített az Anna’s Archive MD5-jeivel, küldjön nekünk egy <a %(a_contact)s>emailt</a>, és küldje el a listát. Ingyenes tagságot adunk az Anna’s Archive-hoz, hogy könnyebben végezhesse ezt a munkát (és köszönetképpen a segítségéért). Ezeknek magas színvonalú szerkesztéseknek kell lenniük, amelyek jelentős mennyiségű információt adnak hozzá, különben a kérését elutasítjuk. A kérését akkor is elutasítjuk, ha bármelyik szerkesztést visszavonják vagy kijavítják az Open Library moderátorai. Open Library kapcsolódás Ha jelentősen részt veszel a munkánk fejlesztésében és működtetésében, megvitathatjuk a több adománybevétel megosztását veled, hogy szükség szerint felhasználhasd. Csak akkor fizetünk a tárhelyért, ha mindent beállítottál, és bebizonyítottad, hogy képes vagy az archívumot naprakészen tartani a frissítésekkel. Ez azt jelenti, hogy az első 1-2 hónapot saját zsebből kell fizetned. Az idődet nem kompenzáljuk (és a miénket sem), mivel ez tisztán önkéntes munka. Hajlandóak vagyunk fedezni a tárhely- és VPN-költségeket, kezdetben havi 200 dollárig. Ez elegendő egy alap keresőszerverhez és egy DMCA-védett proxyhoz. Tárhely költségek Kérjük, <strong>ne lépj velünk kapcsolatba</strong> engedélykérés vagy alapvető kérdések miatt. A tettek hangosabban beszélnek, mint a szavak! Minden információ elérhető, így csak kezdj neki a tükör beállításának. Nyugodtan tegyél fel hibajegyeket vagy egyesítési kérelmeket a Gitlabunkra, ha problémákba ütközöl. Lehet, hogy néhány tükörspecifikus funkciót kell veled együtt fejlesztenünk, például az „Anna’s Archive” átnevezését a weboldalad nevére, a felhasználói fiókok (kezdeti) letiltását, vagy a könyvoldalakról a főoldalunkra való hivatkozást. Miután a tükröd működik, kérjük, lépj velünk kapcsolatba. Szeretnénk áttekinteni az OpSec-edet, és ha az rendben van, hivatkozni fogunk a tükrödre, és szorosabban együttműködünk veled. Előre is köszönjük mindenkinek, aki hajlandó ilyen módon hozzájárulni! Ez nem gyenge szívűeknek való, de megszilárdítaná az emberiség történetének legnagyobb valóban nyitott könyvtárának hosszú élettartamát. Kezdés Anna Archívumának ellenálló képességének növelése érdekében önkénteseket keresünk tükrök futtatására. Az Ön verziója egyértelműen megkülönböztethető tükörként, pl. „Bob Archívuma, egy Anna Archívuma tükör”. Hajlandó vállalni az ezzel a munkával járó jelentős kockázatokat. Mélyen érti a szükséges működési biztonságot. Az <a %(a_shadow)s>ezek</a> <a %(a_pirate)s>a bejegyzések</a> tartalma magától értetődő az Ön számára. Kezdetben nem adunk hozzáférést a partner szerver letöltéseinkhez, de ha minden jól megy, megoszthatjuk azt Önnel. Ön futtatja az Anna Archívum nyílt forráskódú kódbázisát, és rendszeresen frissíti mind a kódot, mind az adatokat. Hajlandó hozzájárulni a <a %(a_codebase)s>kódbázisunkhoz</a> — csapatunkkal együttműködve — annak érdekében, hogy ez megvalósuljon. Ezt keressük: Tükrök: önkéntesek felhívása Újabb adomány tétele. Még nincsenek adományok. <a %(a_donate)s> Adományozz először.</a> Az adományok részletei nem nyilvánosak. Adományaim 📡 A gyűjteményünk tömeges tükrözéséhez tekintsd meg az <a %(a_datasets)s>Adatbázisok</a> és <a %(a_torrents)s>Torrentek</a> oldalakat. Az IP-címedről az elmúlt 24 órában történt letöltések száma: %(count)s. 🚀 A gyorsabb letöltésekhez és a böngésző ellenőrzések kihagyásához <a %(a_membership)s>válj taggá</a>. Letöltés partner weboldalról Nyugodtan böngésszen tovább Anna Archívumában egy másik fülön, miközben várakozik (ha a böngészője támogatja a háttérfülek frissítését). Nyugodtan várjon, amíg több letöltési oldal betöltődik egyszerre (de kérjük, egyszerre csak egy fájlt töltsön le szerverenként). Miután megkapja a letöltési linket, az több órán keresztül érvényes. Köszönjük a türelmét, ezáltal a weboldal mindenki számára ingyenesen elérhető marad! 😊 🔗 Az összes letöltési link ehhez a fájlhoz: <a %(a_main)s>Fájl főoldala</a>. ❌ Lassú letöltések nem érhetők el Cloudflare VPN-eken vagy más Cloudflare IP-címekről. ❌ A lassú letöltések csak a hivatalos webhelyen keresztül érhetők el. Látogatás %(websites)s. 📚 A letöltéshez használd a következő URL-t: <a %(a_download)s>Letöltés most</a>. Annak érdekében, hogy mindenki lehetőséget kapjon a fájlok ingyenes letöltésére, várnia kell, mielőtt letöltheti ezt a fájlt. Kérjük, várjon <span %(span_countdown)s>%(wait_seconds)s</span> másodpercet a fájl letöltéséhez. Figyelmeztetés: sok letöltés történt az Ön IP-címéről az elmúlt 24 órában. A letöltések lassabbak lehetnek a szokásosnál. Ha VPN-t, megosztott internetkapcsolatot használ, vagy az internetszolgáltatója megosztja az IP-címeket, ez a figyelmeztetés emiatt lehet. Mentés ❌ Valami rosszul sikerült. Töltsd újra az oldalt, és próbáld meg újra. ✅ Mentve. Töltsd újra az oldalt. A megjelenített név módosítása. Az azonosítód (a "#" utáni rész) nem módosítható. Profil létrehozva <span %(span_time)s>%(time)s</span> szerkesztés Listák Új listát készíthetsz egy fájl megkeresésével és a "Listák" fül megnyitásával. Még nincsenek listák Profil nem található. Profil Jelenleg nem tudunk könyvkéréseket teljesíteni. Ne küldjön nekünk e-mailben könyvkéréseket. Kérjük, tegye fel kéréseit a Z-Library vagy a Libgen fórumain. Rekord Anna archívumában DOI: %(doi)s Letöltés SciDB Nexus/STC Előnézet még nem érhető el. Töltse le a fájlt az <a %(a_path)s>Anna Archívuma</a>-ból. Az emberi tudás hozzáférhetőségének és hosszú távú megőrzésének támogatása érdekében váljon <a %(a_donate)s>taggá</a>. Bónuszként a 🧬&nbsp;SciDB gyorsabban tölt be a tagok számára, korlátok nélkül. Nem működik? Probálj <a %(a_refresh)s>frissíteni</a>. Sci-Hub Speciális keresési mező hozzáadása Leírások és metaadat-megjegyzések keresése Kiadás éve Haladó Hozzáférés Tartalom Megjelenítés Lista Táblázat Fájltípus Nyelv Rendezés Legnagyobb Legrelevánsabb Legújabb (fájl méret) (nyílt forráskódú) (megjelenési év) Legrégebbi Véletlenszerű Legkisebb Forrás kaparva és nyílt forráskódúvá téve az AA által Digital Lending (%(count)s) Folyóiratcikkek (%(count)s) Találtunk egyezéseket a következőben: %(in)s. Az URL-re hivatkozhat. <a %(a_request)s>Fájl kérésekor</a> ott található. Metadata (%(count)s) A kódok szerinti keresési index felfedezéséhez használja a <a %(a_href)s>Codes Explorer</a> eszközt. A keresési index havonta frissül. Jelenleg %(last_data_refresh_date)s-ig tartalmaz bejegyzéseket. Technikai részletekért lásd az %(link_open_tag)sadatbázisok oldalt</a>. Kizárja Csak tartalmazza Ellenőrizetlen tovább… Következő … Előző Ez a keresési index jelenleg az Internet Archive Kontrollált Digitális Kölcsönzési Könyvtárának metaadatait tartalmazza. <a %(a_datasets)s>Bővebben az adatkészleteinkről</a>. További digitális kölcsönkönyvtárakért lásd: <a %(a_wikipedia)s>Wikipédia</a> és <a %(a_mobileread)s>MobileRead Wiki</a>. DMCA / szerzői jogi igények tekintetében <a %(a_copyright)s>kattints ide</a>. Letöltési idő Hiba a keresés során. Próbáld meg <a %(a_reload)s>az oldal újratöltését</a>. Ha a probléma továbbra is fennáll, írj nekünk a %(email)s e-mail címre. Gyors letöltés Valójában bárki segíthet ezeknek a fájloknak a megőrzésében azáltal, hogy vetőmagként szolgál <a %(a_torrents)s>egységes torrentlistánkhoz</a>. ➡️ Néha ez helytelenül történik, amikor a keresőszerver lassú. Ilyen esetekben az <a %(a_attrs)s>újratöltés</a> segíthet. ❌ Ez a fájl lehet problémás. Cikkeket keres? Ez a keresési index jelenleg az ISBNdb és az Open Library metaadatait tartalmazza. <a %(a_datasets)s>Bővebben az adatkészleteinkről</a>. Világszerte sok-sok metaadat-forrás létezik az írott művekhez. <a %(a_wikipedia)s >Ez a Wikipédia-oldal</a> jó kiindulópont, de ha tudsz más jó listát, kérjük, jelezd nekünk. A metaadatok esetében az eredeti rekordokat mutatjuk. Nem végezzük el a rekordok egyesítését. Jelenleg mi rendelkezünk a világ legátfogóbb nyitott katalógusával a könyvek, publikációk és egyéb írásos művek tekintetében. Tükrözzük a Sci-Hub, a Library Genesis, a Z-Library, <a %(a_datasets)s>és még sok más</a> könyvtárat. <span class="font-bold">Nincs fájl találat.</span> Próbálj meg keresni kevesebb kifejezéssel vagy kevesebb szűrőkkel. Eredmények %(from)s-%(to)s (%(total)s összesen) Ha további "árnyékkönyvtárakat" találsz, amelyeket tükröznünk kellene, vagy ha bármilyen kérdésed van, lépj velünk kapcsolatba az %(email)s címen. %(num)d részleges találatok %(num)d+ részleges találatok Írj a mezőbe, hogy fájlokat keress a digitális kölcsönzőkönyvtárakban. A mezőbe beírva kereshetsz a %(count)s közvetlenül letölthető fájlok katalógusában, amelyeket <a %(a_preserve)s>örökre megőrzünk</a>. Írj a mezőbe a kereséshez. Írj be a mezőbe, hogy keresd katalógusunkban a %(count)s tudományos dolgozatokat és folyóiratcikkeket, amelyeket <a %(a_preserve)s>örökre megőrzünk</a>. Írj a mezőbe a könyvtárak metaadatainak kereséséhez. Ez hasznos lehet<a %(a_request)s>fájlok kérése esetén</a>. Tipp: használd a "/" (keresési fókusz), "enter" (keresés), "j" (fel), "k" (le) billentyűkombinációkat a gyorsabb navigációhoz. Ezek metaadat rekordok, <span %(classname)s>nem</span> letölthető fájlok. Keresési beállítások Keresés Digitális Kölcsönzés Letöltés Folyóiratcikkek Metaadat Új Keresés %(search_input)s - Keresés A keresés túl sokáig tartott, ami azt jelenti, hogy pontatlan eredményeket láthat. Néha az oldal <a %(a_reload)s>újratöltése</a> segít. A keresés túl sokáig tartott, ami általános a széles körű lekérdezéseknél. Előfordulhat, hogy a szűrőszámlálások nem pontosak. Nagy feltöltések esetén (több mint 10 000 fájl), amelyeket a Libgen vagy a Z-Library nem fogad el, kérjük, lépjen kapcsolatba velünk a %(a_email)s címen. A Libgen.li esetében győződjön meg róla, hogy először bejelentkezik a <a %(a_forum)s >fórumukra</a> a %(username)s felhasználónévvel és a %(password)s jelszóval, majd térjen vissza a <a %(a_upload_page)s >feltöltési oldalra</a>. Egyelőre azt javasoljuk, hogy új könyveket a Library Genesis forkokba tölts fel. Itt egy <a %(a_guide)s>praktikus útmutató</a>. Vedd figyelembe, hogy mindkét fork, amelyet ezen a weboldalon indexelünk, ugyanabból a feltöltési rendszerből származik. Kis feltöltésekhez (legfeljebb 10 000 fájl) kérjük, töltse fel őket mindkettőre: %(first)s és %(second)s. Alternatív megoldásként feltöltheti őket a Z-Library <a %(a_upload)s>itt</a>. Akadémiai cikkek feltöltéséhez kérjük, hogy (a Library Genesis mellett) töltse fel azokat a <a %(a_stc_nexus)s>STC Nexus</a>-ra is. Ők a legjobb árnyékkönyvtár az új cikkek számára. Még nem integráltuk őket, de valamikor meg fogjuk tenni. Használhatja a <a %(a_telegram)s>feltöltő botjukat a Telegramon</a>, vagy vegye fel a kapcsolatot a rögzített üzenetükben megadott címmel, ha túl sok fájlja van, hogy így töltse fel. <span %(label)s>Nehéz önkéntes munka (USD$50-USD$5,000 jutalmak):</span> ha sok időt és/vagy erőforrást tudsz szentelni küldetésünknek, szívesen dolgoznánk szorosabban veled. Végül csatlakozhatsz a belső csapathoz. Bár szűkös a költségvetésünk, a legintenzívebb munkákért <span %(bold)s>💰 pénzbeli jutalmakat</span> tudunk adni. <span %(label)s>Könnyű önkéntes munka:</span> ha csak néhány órát tud szánni itt-ott, még mindig rengeteg módja van annak, hogy segítsen. A következetes önkénteseket <span %(bold)s>🤝 tagságokkal jutalmazzuk Anna Archívumában</span>. Anna Archívuma önkéntesekre támaszkodik, mint Ön. Minden elkötelezettségi szintet szívesen fogadunk, és két fő kategóriában keresünk segítséget: Ha nem tudsz időt szánni az önkéntes munkára, még mindig sokat segíthetsz nekünk azzal, ha <a %(a_donate)s>pénzt adományozol</a>, <a %(a_torrents)s>seedelni kezded a torrentjeinket</a>, <a %(a_uploading)s>könyveket töltesz fel</a>, vagy <a %(a_help)s>mesélsz barátaidnak Anna Archívumáról</a>. <span %(bold)s>Vállalatok:</span> nagy sebességű közvetlen hozzáférést kínálunk gyűjteményeinkhez vállalati szintű adomány vagy új gyűjtemények cseréjéért (pl. új szkennelések, OCR-elt datasets, adataink gazdagítása). <a %(a_contact)s>Lépjen kapcsolatba velünk</a>, ha ez Önre vonatkozik. Lásd még <a %(a_llm)s>LLM oldalunkat</a>. Jutalmak Mindig keresünk olyan embereket, akik szilárd programozási vagy támadó biztonsági készségekkel rendelkeznek, hogy bekapcsolódjanak. Komoly hatást gyakorolhat a emberiség örökségének megőrzésére. Köszönetképpen tagságot adunk a jelentős hozzájárulásokért. Nagy köszönetképpen pénzbeli jutalmakat adunk különösen fontos és nehéz feladatokért. Ezt nem szabad munkahelyi pótlékként tekinteni, de ez egy extra ösztönző és segíthet a felmerülő költségek fedezésében. A legtöbb kódunk nyílt forráskódú, és ezt kérjük az Ön kódjától is, amikor jutalmat ítélünk oda. Vannak kivételek, amelyeket egyénileg meg tudunk beszélni. A jutalmakat az első személy kapja, aki teljesít egy feladatot. Nyugodtan kommentáljon egy jutalomjegyet, hogy mások tudják, hogy dolgozik valamin, így mások visszatarthatják magukat vagy kapcsolatba léphetnek Önnel, hogy csapatot alkossanak. De vegye figyelembe, hogy mások is szabadon dolgozhatnak rajta, és megpróbálhatják megelőzni Önt. Azonban nem ítélünk oda jutalmat hanyag munkáért. Ha két magas színvonalú benyújtás közel egy időben történik (egy-két napon belül), saját belátásunk szerint dönthetünk úgy, hogy mindkettőnek jutalmat adunk, például 100%% az első benyújtásért és 50%% a második benyújtásért (tehát összesen 150%%). A nagyobb jutalmak esetén (különösen az adatgyűjtési jutalmaknál) kérjük, vegye fel velünk a kapcsolatot, amikor ~5%%-át teljesítette, és biztos benne, hogy módszere skálázható lesz a teljes mérföldkőre. Meg kell osztania velünk a módszerét, hogy visszajelzést adhassunk. Így dönthetünk arról is, hogy mit tegyünk, ha több ember is közel áll a jutalomhoz, például több embernek is odaítélhetjük, ösztönözhetjük az embereket a csapatmunkára stb. FIGYELMEZTETÉS: a magas jutalmú feladatok <span %(bold)s>nehezek</span> — érdemes lehet könnyebbekkel kezdeni. Látogasson el <a %(a_gitlab)s>Gitlab issues listánkra</a>, és rendezze „Label priority” szerint. Ez nagyjából megmutatja a feladatok sorrendjét, amelyek számunkra fontosak. Az explicit jutalom nélküli feladatok is jogosultak a tagságra, különösen azok, amelyek „Accepted” és „Anna kedvence” jelöléssel vannak ellátva. Érdemes lehet egy „Kezdő projekt”-tel kezdeni. Könnyű önkéntes munka Most már van egy szinkronizált Matrix csatornánk is a %(matrix)s címen. Ha van néhány szabad órád, többféleképpen is segíthetsz. Csatlakozz a <a %(a_telegram)s>önkéntesek csevegéséhez a Telegramon</a>. Hálánk jeléül általában 6 hónap „Szerencsés Könyvtáros” tagságot adunk az alap mérföldkövekért, és többet a folyamatos önkéntes munkáért. Minden mérföldkő magas színvonalú munkát igényel — a hanyag munka többet árt, mint használ, és elutasítjuk. Kérjük, <a %(a_contact)s>emailben értesíts minket</a>, amikor elérsz egy mérföldkövet. %(links)s linkek vagy képernyőképek a teljesített kérésekről. Könyv (vagy cikk, stb.) kérések teljesítése a Z-Library vagy a Library Genesis fórumain. Nincs saját könyvkérő rendszerünk, de tükrözzük ezeket a könyvtárakat, így azok jobbá tétele Anna Archívumát is jobbá teszi. Mérföldkő Feladat A feladattól függ. Kis feladatok, amelyeket az <a %(a_telegram)s>önkéntesek csevegésében a Telegramon</a> posztolunk. Általában tagságért, néha kis jutalmakért. Kis feladatok, amelyeket önkéntes csevegőcsoportunkban teszünk közzé. Ügyeljen arra, hogy hagyjon megjegyzést a megoldott problémákról, hogy mások ne végezzék el ugyanazt a munkát. %(links)s rekordok linkjei, amelyeket javított. Kezdésként használhatja a <a %(a_list)s >véletlenszerű metadata problémák listáját</a>. Metaadatok javítása az <a %(a_metadata)s>Open Library-hoz való kapcsolással</a>. Ezeknek azt kellene mutatniuk, hogy tájékoztatsz valakit Anna Archívumáról, és ő megköszöni neked. %(links)s linkek vagy képernyőképek. Anna Archívumának hírét terjesztjük. Például könyvek ajánlásával az AA-n, blogbejegyzéseink linkelésével, vagy általában az emberek irányításával a weboldalunkra. Egy nyelv teljes fordítása (ha az nem volt már majdnem kész). <a %(a_translate)s>A weboldal fordítása</a>. Link a szerkesztési előzményekhez, amely megmutatja, hogy jelentős hozzájárulásokat tettél. Anna Archívumának Wikipédia oldalának javítása a saját nyelveden. Tartalmazza az AA Wikipédia oldalának információit más nyelveken, valamint a weboldalunkról és blogunkról származó információkat. Hivatkozásokat adj hozzá az AA-ra más releváns oldalakon. Önkéntesség és Jutalmak 