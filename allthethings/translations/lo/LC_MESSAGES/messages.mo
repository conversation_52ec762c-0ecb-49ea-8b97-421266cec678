��    �     �-              |[     }[     �[     �[  "   �[     �[      \     4\  '   S\  ,   {\  (   �\  $   �\  (   �\  -   ]  '   M]  )   u]  $   �]  $   �]  $   �]  $   ^  )   3^  %   ]^  "   �^  %   �^  !   �^  !   �^  $   _     5_  !   S_  "   u_      �_  #   �_     �_     �_     `  "   :`     ]`  #   {`  #   �`  #   �`     �`  #   a     )a  #   Ga     ka  #   �a     �a  #   �a     �a     	b     )b     ?b     \b     yb     �b     �b     �b     �b     
c     'c     Bc     ]c     xc     �c     �c     �c     �c     d     d     3d     Hd     ]d     rd      �d      �d  )   �d  *   �d  )   e  )   Ge  )   qe  )   �e  )   �e  )   �e  )   f  )   Cf  +   mf  ,   �f  ,   �f  ,   �f  )    g  )   Jg  )   tg  *   �g  ,   �g  2   �g  7   )h  :   ah  4   �h  6   �h  -   i  6   6i  2   mi  1   �i  1   �i  .   j  4   3j  @   hj  ;   �j  =   �j  >   #k  =   bk  ;   �k  4   �k  ,   l  #   >l  *   bl  -   �l  0   �l  +   �l  2   m  .   Km  6   zm  6   �m  <   �m  -   %n  6   Sn  1   �n  .   �n  +   �n  3   o  %   Ko  %   qo  %   �o  %   �o     �o     p  !   p  !   @p  &   bp  &   �p  (   �p  $   �p  #   �p     "q     >q     Zq     vq     �q     �q     �q     �q      r  '   r  $   Ar  +   fr  9   �r  -   �r  (   �r  .   #s     Rs  '   ^s  0   �s  0   �s  0   �s  0   t  0   Jt  0   {t     �t     �t     �t     �t     �t     �t  1   u     Bu  ,   bu  '   �u  *   �u  7   �u  #   v     >v  )   ^v  *   �v  ,   �v  /   �v  ,   w  /   =w  (   mw  -   �w  %   �w  2   �w  6   x  +   Tx  ,   �x  /   �x  .   �x  6   y  -   Cy  ,   qy  )   �y  %   �y  (   �y  '   z  '   ?z  $   gz  $   �z  #   �z  1   �z  '   {  0   /{     `{     ~{     �{     �{  !   �{  %   �{  ,    |  #   M|  $   q|  $   �|  '   �|  #   �|      }  #   (}  $   L}  %   q}  "   �}  3   �}  "   �}  9   ~  #   K~  $   o~  &   �~  "   �~      �~  !   �~  $   !  4   F  +   {  %   �  %   �  "   �  $   �     ;�     U�  $   j�  2   ��  +     /   �  '   �  $   F�  #   k�  "   ��     ��      Ɓ  "   �      
�     +�  +   I�     u�      ��  (   ��      ނ      ��       �  #   A�  +   e�  *   ��  )   ��  .   �  0   �  8   F�  '   �  6   ��  1   ބ  "   �  .   3�      b�  #   ��  )   ��     х     ��      �      1�  !   R�  %   t�     ��     ��      Ն     ��  (   �     >�     Z�  (   w�      ��  &   ��  $   �  &   
�     4�      S�      t�  !   ��  $   ��  $   ܈     �     �  $   =�  !   b�     ��  "   ��      Ɖ      �  $   �  $   -�  '   R�  #   z�  "   ��  %   ��  !   �      	�     *�     D�     a�     �     ��     ��     ȋ     �     ��     �     %�     ;�     Q�     g�     }�     ��     ��  !   ��  $   ی  %    �  %   &�  5   L�  *   ��  *   ��  &   ؍  )   ��  %   )�  ,   O�  &   |�  *   ��  -   Ύ  -   ��     *�  &   G�  '   n�  .   ��  ,   ŏ  ,   �  %   �  (   E�  '   n�  '   ��  (   ��  (   �  (   �     9�     W�     w�     ��     ��     ��     ב     �     �     "�     9�     O�     f�     y�     ��     ��  @   ��     ��     �  6   -�     d�     ��     ��     ��     Ó     ��  )   ��     '�     @�     ]�     x�  &   ��  !   ��  )   ܔ     �     $�     B�     [�     r�     ��      ��          ޕ     ��  !   �     :�     T�     m�     ��      ��     ̖     �      �     %�     :�  &   O�      v�     ��  #   ��     ԗ  !   �  !   �  "   3�  (   V�     �      ��  #   ��     �     �      !�      B�  #   c�  !   ��     ��  &   Ù     �     �     %�     C�     _�     v�     ��     ��     Ú     ݚ  '   ��  &   �  &   F�  #   m�     ��     ��  #   ћ  #   ��  #   �  #   =�  $   a�  +   ��  '   ��  $   ڜ  $   ��  $   $�  &   I�  0   p�  5   ��  .   ם  *   �  -   1�  $   _�  $   ��  #   ��  ,   ͞  (   ��     #�  %   A�  )   g�  *   ��  *   ��     �  *   �     2�  !   P�     r�  +   ��  )   ��  ,   �  0   �  (   D�  $   m�  "   ��  %   ��  ,   ۡ  '   �  +   0�  (   \�  &   ��  +   ��  &   آ  &   ��     &�  '   D�  %   l�  $   ��  $   ��  (   ܣ  "   �     (�  )   F�  )   p�  )   ��  !   Ĥ     �  +   �  *   .�  -   Y�  &   ��  "   ��  $   ѥ     ��     �     0�     M�     j�      ��  !   ��  !   ʦ  !   �  !   �  ,   0�  ,   ]�  "   ��      ��  !   Χ  #   �     �     /�     M�  $   k�  $   ��  $   ��  "   ڨ  "   ��  "    �  %   C�  %   i�  %   ��  "   ��     ة     ��  "   �  "   ;�  "   ^�  )   ��  &   ��  &   Ҫ  )   ��  &   #�  &   J�  )   q�  %   ��  '   ��  #   �  #   
�  &   1�  &   X�  #   �  0   ��  (   Ԭ  (   ��  -   &�     T�  $   h�  $   ��  $   ��      ׭  "   ��  "   �      >�     _�  #   }�     ��  '   ��  '   �  %   �  '   7�  (   _�  )   ��  &   ��  0   ٯ  %   
�  !   0�  &   R�  &   y�  $   ��  "   Ű  "   �  "   �  (   .�  !   W�  *   y�  -   ��  "   ұ  &   ��      �  +   =�  )   i�  &   ��  "   ��  !   ݲ  *   ��  #   *�  (   N�  %   w�     ��  )   ��  (   �  #   �  !   4�     V�  '   v�  &   ��     Ŵ  (   �  "   	�  #   ,�  )   P�     z�     ��  -   ��  *   �     �      /�      P�  *   q�  0   ��  $   Ͷ  *   �  0   �  1   N�  @   ��  3   ��  3   ��  3   )�  3   ]�  3   ��  3   Ÿ  ,   ��  <   &�  5   c�  3   ��  3   ͹  3   �  3   5�  3   i�  ;   ��  ;   ٺ  6   �  ;   L�  8   ��  ,   ��  #   �     �     0�     M�     f�  #   ��      ��  "   ˼     �     	�     "�  #   ?�     c�     |�     ��     ��     ˽     �     ��  #   �  )   ,�     V�     k�     ��     ��     ��     վ     �     
�     %�     @�  %   ]�  +   ��  *   ��  +   ڿ  *   �  +   1�  *   ]�  +   ��  )   ��     ��     ��  &   �     B�  #   [�     �  #   ��     ��     ��     ��  #   �  #   %�     I�  +   _�  4   ��  "   ��  *   ��     �     +�     D�     ]�      p�     ��  )   ��  )   ��  "   ��  )   �  "   F�  %   i�  %   ��  )   ��  #   ��     �  #   #�  (   G�  )   p�  /   ��  "   ��  &   ��  "   �  (   7�  %   `�     ��  #   ��  3   ��  "   ��  !   !�     C�  "   c�  #   ��  '   ��  )   ��  %   ��  &   "�  2   I�  2   |�  2   ��  3   ��  2   �  2   I�  3   |�  #   ��  #   ��     ��      �  %   6�  $   \�  %   ��  ,   ��  /   ��     �      $�  0   E�  2   v�  )   ��  #   ��  %   ��  $   �     B�  %   b�  "   ��      ��  %   ��  "   ��  "   �  %   8�  &   ^�  $   ��  !   ��     ��     ��     ��  $   �      B�  *   c�     ��     ��     ��     ��     ��     �     /�     G�     ^�     z�  %   ��  $   ��  %   ��  $   �  %   *�  $   P�  %   u�     ��     ��     ��     ��     �     *�     E�     `�     r�     ��  &   ��      ��     ��  &   �     2�     O�  "   k�  "   ��     ��  &   ��  (   ��  (   �  (   F�  (   o�     ��      ��  %   ��  &   ��      �  .   ?�  )   n�  -   ��  -   ��  -   ��  -   "�  +   P�  ,   |�  (   ��     ��  !   ��  )   �  "   >�     a�  !   �     ��  "   ��     ��      ��     �     4�  #   P�     t�     ��     ��  !   ��  "   ��  *   �  '   7�  #   _�  (   ��  #   ��     ��     ��     �     �  -   ;�  +   i�     ��     ��     ��     ��     �     !�  "   =�  -   `�  )   ��  *   ��     ��  "   ��  $   !�  &   F�  '   m�  $   ��  "   ��  '   ��  "   �  "   (�  !   K�     m�  $   ��  $   ��  "   ��  '   ��     "�  +   B�  *   n�  $   ��  (   ��  %   ��  (   
�  (   6�  '   _�  &   ��  &   ��  )   ��      ��      �     9�     V�     s�  &   ��     ��      ��  !   ��     �  )   /�  $   Y�     ~�     ��     ��     ��     ��  "   ��  "   �      @�     a�     ��      ��  .   ��  *   ��     �  "   '�  !   J�     l�     ��     ��     ��     ��     ��     �     �     +�     ?�     S�     f�     y�     ��     ��     ��     ��     ��     �     �     .�     B�     X�     l�     ��     ��     ��     ��     ��     ��     �     (�     @�     X�     o�  !   ��     ��     ��     ��     ��     �     9�     T�     o�     ��     ��     ��     ��     ��     ��     �     �     3�     G�     V�     k�     ��  .   ��  &   ��  &   ��  !   �     4�     I�     ^�     s�     ��     ��     ��     ��     ��     ��     �     #�     8�     M�     e�     {�     ��     ��     ��     ��     ��     �     4�     K�     a�     x�     ��     ��      ��     ��     �     "�     8�     K�     `�      y�     ��     ��     ��     ��  $   �     4�     Q�     h�     }�     ��     ��     ��     ��      �  !   �     :�     Y�     y�     ��     ��     ��     ��     ��     �     +�     ;�     V�     i�     y�  &   ��  #   ��     ��     ��     �     8�     W�     v�     ��     ��     ��     ��     ��     �     �     1�     E�     a�     s�     ��     ��     ��     ��     ��  $   ��      �  "   5�  !   X�  "   z�     ��  $   ��     ��     ��     �     6�     U�     s�     ��      ��     ��  $   ��  '   �  !   =�  #   _�      ��      ��  &   ��  !   ��  '   �     6�  "   S�  *   v�  %   ��  %   ��  (   ��  9   �  2   P�  +   ��  &   ��  &   ��     ��  "   �  (   @�     i�     ��     ��      ��  )   ��  -   
�     8�      U�     v�  )   ��     ��  &   ��  !   �  (   '�  $   P�     u�  $   ��  $   ��  %   ��  #   �  &   &�      M�  )   n�     ��     ��  #   ��     ��     �  "   0�  "   S�  (   v�     ��     ��  %   ��  '   �  -   *�     X�  &   v�     ��     ��  $   ��     ��     �     6�  $   P�     u�     ��     ��     ��     ��     ��     �     '�     :�      R�     s�     ��     ��     ��     ��  !   �     (�     B�     _�     v�     ��     ��  !   ��  !   ��  !   �     '�     F�     f�  #   ��     ��     ��     ��  %   ��  "   !  !   D     f     �     �     �     �     �         8    W    l    �    �    �    �    �          %   :    `    }    � #   �    �    �        0    L    d    |    �    �    �    �    �        (    >    S    l    �    �    �    �        '    F    e    �    �    �    �    �         "   9 "   \ $    $   � &   � &   � &    $   > "   c "   � $   � "   �    � $   
 .   2 .   a    �    � "   � "   � "   	 "   1	 "   T	    w	 "   �	     �	    �	    �	 '   	
    1
    D
    W
    v
    �
    �
 $   �
 -   �
 &       C    `    z    �    �    � /   � -       F    ` !   { ,   � -   � '   � (    
 (   I
 %   r
    �
    �
    �
    �
            4    G    f    ~    �    �    �    �    �    �    
    #    B    U !   g )   � )   �    � !   � "    "   >     a !   � #   � #   � #   � #    )   4 "   ^ )   � -   � 1   � "    "   . $   Q !   v "   � )   � "   �     "   ' !   J    l    �    �    �    �    � &   
    1 (   M -   v    � !   �     � !     !   " !   D $   f    � &   � !   � &   � '    &   >    e !   ~ %   �    �     � *    $   . "   S #   v #   �    � %   �             1    R    l    �    �    �    �     �     '   #    K    ]    x    �    �    �    �          *   < (   g (   � (   � (   � (    (   4 (   ] '   � &   � %   � %   � 8   ! -   Z (   � #   � &   � !   � "    2   A 4   t 4   � )   � ?    7   H 1   � +   � &   � +    &   1    X �  p �   �  �  �! &   �%   �% Y   �* g   + 6   �+ <   �+ �   �+ O   �, 1   �, ^   - u   b- M   �-    &. s   B. �  �. {  \1 �   �3 �   �4    5 /   5    J5 /   V5    �5 	   �5 9   �5    �5 >   �5    6 	   #6 
   -6    ;6 <   G6 8   �6 )   �6 �  �6 O  �< Y  �? ,   JA -  wA ,   �B e  �B 1   8D �  jD ,   G h  ;G    �J �  �J    xM �   �M �   sN x  ,O v   �P g   Q �   �Q �   gR �   S   �S �  �T �   XV   W �   Y Q  �Y 7  \ �  I] �   �^ 6  �_ �  �a �   �c y   Md    �d j  �e �  3g �   j �   �j �   Tk 	  �k �   m �   �m y   �n �   Zo �   Yp �   Gq <   �q ,  7r �   ds �  Jt �  �u I   �w �  �w   �z �  �| �  B~    ӂ �  ԃ �  {�    A� C  b� 8  �� �  ߋ �  ۍ   x�    �� %  �� �  ͒ w  �� �  � �   �� -  ͘   �� �  � `  ڞ �  ;� M   � �   A� �   ˢ �   P� �   ԣ   ˤ *   ҥ a   �� |   _� �   ܦ �   ~� b   �   g�    ~� 0   �� p  ��   0� q  M� �  �� '   �� B   � �  *� �   �� �  �    �� )  �� �  ͻ   t� �  �� �   %�   �� �  �� `   �� �   � �   �� +  c� �   �� K   �    �� $   �� `   �� 0   W� 0   �� $   ��    ��    �� $   
�    2�    N�    d� '   z�    �� v   ��    8�    ?� '   R�    z� �  �� �   0� ?   ��    � D   4� J   y� y   �� `   >� B   ��    ��    �� $   � 9   <� 0   v� 0   ��    ��    ��    
� -   � V   K� 4   �� %   �� -   �� 1   +� R   ]� Q   �� %   � E   (� g   n� 0   �� �   � x   ��    ;� �   K� �   �    ��    �� .   �� $   � 0   ;� E   l� 9   �� -   ��    � 3   "�    V� F   c�    �� 	   �� 
   ��    �� N   ��    �    %� 	   .� %   8� 	   ^� >   h�    ��    �� 	   ��    ��    �� <   ��    � #   �    C�    a� $   i� 	   ��    �� G   ��    ��    � Z   �    u� 0   ��    �� 8   ��    �    � -   =� �   k� p  h� �   �� �  �� s  B� �   �� ,   �� ;   �� '   �    3�    :�    S�    k� �   �� �   � �   �� �   `� @   9� �   z� |   � &  ��   �� �  �� �   �� �   p� [   � 0   o�    ��    ��    �� !   �� !   �    #� !   (�    J�    S� 6   i�    ��    �� ?   ��    �� 9   � 3   K� *   �    ��    ��    ��    �� 0   �� 1   *� �  \�    � !   %�    G� 9   M�    �� �   �� �   6� B   �� p   � �   ��    '�    /�    7� �   :�    	� #   � ^   3� �   �� H   F� 6   �� �   �� G   �� +  �� �   ) 5  � ]  �   L �   d
   >    O
 M  P w  � .    !   E �   g �   � �   � �   : �   � x   � �   $ ?   � J   =    � L   � �   � /   �    � %   �     7   " �   Z    M d   c �   � 0   ` .   � �   � 
  d �  o 9   ! 3   >! c  r!    �"    �"    # =   $#    b# V   {# �  �# 4   |&    �& *   �& �  �& M   �)    �) N   �)    6*    F* X   Y* H   �*    �* @   +   L+ F   [, *   �, �   �, +   �-    �-    �- 0   �- s   &. �   �.    O/ �   n/ �  �/ �   �1 �   �2 !   3   53 �   D5 '   �5 l   6 $   �6 �  �6 K  P8 3   �9 �   �9 A  b: �  �; 3   %= K   Y= '   �= �  �= D   vA c   �A #   B G   CB `   �B )  �B =   D /   TD x   �D e   �D    cE 8   �E 7   �E f   �E   ZF 4  xJ �  �M �   �R f   S    �S    �S U  �S �  �U d  �W E   DY �  �Y F  n[    �^ F   �^ #  _ p  >d    �f    �f F   �f    0g l  Og &   �i �  �i �  �l �  /p -   r �   6r ~   
s V   �s �   �s x  �t �  :w k  y �  �z �   L} D  ~ �   J� �  �� (  �� �   ك 1  {� n   �� S   � $   p�    �� .   �� _   � 9   C� �   }� z   /� 	   �� ]   �� �  � l   ԋ �  A� �   %� �   � Q   
� 1   _� 7   ��    ɐ :   ܐ F   � f   ^� 7   ő �  �� L   � �  3� 4   �� T  � �   A�   �� F  	� �  P� 2   ��   2� 	   L� �  V� �   � B   � I  D�    �� 3   �� $   ϥ 9   �� N   .�    }�    �� �   �� j  C� 7  ��    � 	   �� (   � N  ,� �  {�   � �   � !   �� !   �    >� +   Y� 6   ��    ��    б y   ر e   R� *  �� �   � -   �� �   ߷ �   ظ �   ͹   [� �   p� �   W�    � �   � �   ǽ �   U� �   H� k   4� $   �� ~  �� �   D� m   �� �   h� w   $� g   �� '   � �   ,� �   �� �   �� q   �� J  ��    =� @  P� �   �� �   E� �   ��    �� 0  �� �  �� $   �� #   �    )�    9� 7  L� O   �� �   �� �  �� Q  V�   �� \  �� Q  � p  o� T  �� �   5� U  � �   l� &  &� �  M� �  �� 
   �� 
   �� 
   ��   �� 
   � 
   &� �   4� �   �� �  �� 
   9�   G� �   [� �   �� s   �� �   K� �   !� 
   �� �   �� 
   �� 
   �� 
   �� �  �� �   m�   W� :   d�    ��    �� m  �� W   6� r   �� �    &  � G   �    C #   S w   w h   � F   X C   � V   �   : H   @ �  �      +
 �   : �   � �  � N  � h  � 1  > 3  p �   � (   � �  �    x! 8  �! �  �$ �  �& �   ) \  * b  x,   �- �   �. =   �/ �   0 (   �0 e   �0    41 $   H1 %   m1   �1 '   �3 �   �3 R   �4    �4    �4    5 ;   05 �   l5 �   Q6 �   �6 n  �7 B   �8    59    U9 A   h9 @   �9    �9    �9    :    ":    5:    G:    Z:    l: |   : �   �:    �; "   �; !   �; "   < !   2< "   T< !   w< "   �< "   �< '   �< 4   = �   <= B   1> i   t> �   �> P  �? �  
A �  �B �  ,D h  �F �  <H i   �J M  @K o   �L �   �L �   �M �  {N N  (P �   wR �   PS    9T "  UT �   xU �   (V    �V     W %   &W )   LW    vW 6   �W    �W >   �W    !X 4   *X 6   _X F   �X #   �X    Y    Y    9Y    @Y    _Y    lY &   tY    �Y I   �Y M   �Y   :Z #  L[ X   p\ B   �\ �   ] �   �] �   �^ ^   #_ m   �_ @   �_ \   1` t   �` E   a �  Ia '  -c %  Ue 1   {g �   �g   Ah `   ai �  �i �  �k   �m �   �n <   -o M  jo �  �p �   =r Y   $s   ~s �  �t /   tv i   �v 0   w �   ?w   �w   �x    �y    �y    z O   z   ^z �   j{ Q   | r   m| 8   �| I   } >   c} �   �} N   3~ �   �~ m    5  � �   �� �   U� �  � N   �� 6   �� 5   ,� 6   b� 5   �� 6   υ 5   � 6   <� �   s� v   � �  �� �   k� C    � f   D� d   ��    � �   &� �   �� |  �� |  r�    �   � D   � %   M� �  s� 1   � c   P� {   �� F   0� �   w� �   �� f   �� 2   � -   J� �  x� =   
� Q   K� �   �� �  =� {   ˝ .  G� �   v� �   � 3  � B   H� u   �� (  � #   *� �   N� =   #� �  a� P   7� �   �� 7   "� q   Z� �  ̨ �   �� �   =�   ̫ �   Ԭ �   ��   \� 9  d�    �� 5   �� �   � �   �� 3   .� k   b�    β    �     � 9   � �   P� �   � 4   �� U   ش U   .� k   �� _   � 7  P� }   �� �   � �   � T   ع #  -� �  Q� F   � �   Y�    � i   	� �   s� '   �� �    � �    � C   �� �   9�    �� F   �� �   �    �� �   �� H   ��    �� R   � �   _� �  #� $   �� $   
� $   /� �   T� $   �� �   � n   �� �  !� �   �� $   t� u   �� �  � )   �� �  �� \   w� Q   �� �   &� �  ��    U�    i�    k� $   m� U   �� v   �� �   _� R   � %   X�    ~� *   �� l   �� c   5�    �� �   �� y   u� *   �� C   � m   ^� ;   �� *   � �   3� �  �� �   �� (   k� "   ��   �� �  �� �   S� !   � �  &� �  �� p   �� �   � =   � �  W� v   �� �   R� :   A� =   |� �  �� G   �� �   �� �   y� �   F� �   �� 4   �� �   �� �   �� @   M� �   �� �   )� 5   �� k   �� a   L� �   �� -  H� d  v� �   �� �  j  @  E �  � g   2 |  � e   �  }
 �  L �  � U   � 3   � <  ' N   d   �   � �   �    � 6   �   � 
   � �  �   t!   z# �  |% �   3( �   �( �   b) |   K* p   �* �   9+ �   , K   �, X   ,- k   �- U   �- *   G. a   r. �   �. d   �/ $   �/ �   #0   1   )3 9   F4 9   �4    �4 �   �4 �   y5 �   `6 �  �6   |8 1   �9 C   �9   �9    �; �   < ~  = n  �B �   �D I   �E    �E    �E     F m   F k   rF   �F #  �G �   J &   �J &   �J 9    K >   ZK �   �K #   �L `   �L    M d   0M [   �M 1   �M    #N �   3N    �N N   �N =   IO @   �O    �O )  �O   �P �   S ,  �S �   U   �U    �W V   X �  _X �   Z �  �[ $   w] �   �] �   t^ |   _   �_ �   �` �   aa 9   8b �   rb *   0c 3   [c '   �c 0   �c -   �c *   d 3   Ad <   ud *   �d    �d Z   �d Z   Te Z   �e 5   
f _   @f X   �f v   �f 4   pg +   �g r   �g ?   Dh    �h y   �h Y   i �   ni S   Pj F   �j D   �j L   0k :   }k 0   �k �   �k �   �l   Jm �  Ln     >p #   _p #   �p    �p [   �p 	   q    #q .   =q   lq %   �r )   �r    �r 
   �r 	   �r j   �r 1   as F  �s /   �u H   
v ,   Sv 5   �v H   �v 9   �v I   9w K   �w O   �w N   x w   nx    �x �   �x ?   �y    �y    z T   z �   gz ;   �z M   /{ T   }{   �{ �   �| �   �}    H~ =   X~ 	   �~    �~ #   �~ ?   �~      1� 6   M�    �� B   �� "   ˃ 3   �    "�    '� �   A� E   � �  Q�    � 7    �   8� 7   :� 3   r� 5   �� 8   ܈ F   � 9   \� !   ��    �� f   ˉ <   2� \   o� k   ̊ *  8� �   c� �   � x   �� 9   %� (  _� 6   �� $   �� �   � *   �� E   �� 7   �� u   1� �   �� 9   8� ?  r� 3   ��    � :   �    @� -   V� !   �� '   ��    Ε    � �   � 
  ݖ ]   � F   F�   �� Z  �� 6   � J   $�    o� q   �� 8   ��    -�    @�    Y�    n�    ��    ��    �� 
   ��    ��    О    �    �    �� c   � �  o�   � L  ,� ^  y� k  ت �  D� "  
�    0� �  I� -   @� S  n� �  ¶ �  �� z  c�    ޽ F   �� =  &� V   d� :   �� u   �� �   l� �   �� �   ~� l  c� ,  �� �  �� �  �� 1   �� �  �� n  ��   � �  )� B   �� �  �� �  �� "  �� �  ��    �� �   �� �   �� r  �� �  �� �   �� L  l� I   �� 2   � 7   6� �   n� y   � 3   �� �   �� �   �� �   N� B   D� <  �� $  �� �   �� �   �� �   p� �   � �   �� �   9�   �� �   ��   u� .  |�    �� X   �� U   � �   m� E   @�    ��    �� �   �� *   6� (   a�    �� {   ��    � v   �� N   �    b�    o�    �� 	   �� �   ��   E� �   T� R    �    S� E   [� c   ��    �    $� $   :�    _�    u�    ��    ��    ��    ��    ��    �� *   �    =�    Y�    w�     �� !   ��    ��    ��      6   #  3   Z  6   �  �   �  *   � �   � �  S    �    � $       +    J    Z    ^ k  t �   � �   � !   _ C   � �   �    �   � 
  �	 5   �
 :    $  A �  f �   �
 W  � �   ) S   � "  ' 8   J 9   � �   �   a =   �   � �   � B  � �    0   �    � 0       = *   S    ~ $   � %   � J  � I  0    z �  � �  < �   3! �   " �  �" *  ^& y  �) y  , 
  }- �  �/    �2 �  �2 �  a4 �  (7 |  9 �  �> �   C ]  D 0   vF q   �F 9  G K  SH �   �K   5L    SN 	   iN    sN '  �N �   �O �   GP b   )Q �   �Q w   7R &  �R D   �S �  T g   �U C   "V �   fV �  .W 9   �X  blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.text4 blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.shadowlib blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.text1 blog.how-to.projects.text2 blog.template.subheading common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.intro page.codes.known_code_prefix page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: lo
Language-Team: lo <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 ກະລຸນາເບິ່ງ <a %(all_isbns)s>ບົດຄວາມຕົ້ນສະບັບ</a> ສໍາລັບຂໍ້ມູນເພີ່ມເຕີມ. ພວກເຮົາໄດ້ອອກຄວາມທ້າທາຍໃນການປັບປຸງສິ່ງນີ້. ພວກເຮົາຈະມອບລາງວັນທີ່ໜຶ່ງມູນຄ່າ $6,000, ທີ່ສອງ $3,000, ແລະທີ່ສາມ $1,000. ເນື່ອງຈາກການຕອບຮັບທີ່ລົດເລີຍແລະຜົນງານທີ່ສຸດຍອດ, ພວກເຮົາຈຶ່ງຕັດສິນໃຈເພີ່ມລາງວັນເພີ່ມອີກເລັກນ້ອຍ, ແລະມອບລາງວັນທີ່ສາມສີ່ທາງມູນຄ່າ $500 ຕໍ່ຄົນ. ຜູ້ຊະນະຢູ່ຂ້າງລຸ່ມນີ້, ແຕ່ຢ່າລືມເບິ່ງຜົນງານທັງໝົດ <a %(annas_archive)s>ທີ່ນີ້</a>, ຫຼືດາວໂຫລດ <a %(a_2025_01_isbn_visualization_files)s>torrent ລວມຂອງພວກເຮົາ</a>. ທີ່ໜຶ່ງ $6,000: phiresky ການ <a %(phiresky_github)s>ສົ່ງຜົນງານ</a> (<a %(annas_archive_note_2951)s>ຄວາມຄິດເຫັນຂອງ Gitlab</a>) ນີ້ແມ່ນທຸກຢ່າງທີ່ພວກເຮົາຕ້ອງການ, ແລະຍັງມີອີກ! ພວກເຮົາຖືກໃຈຫຼາຍກັບຕົວເລືອກການສະແດງຜົນທີ່ຍືດຫຍຸ່ນສຸດໆ (ຍັງສະໜັບສະໜູນການປັບແຕ່ງ shader ທີ່ສ້າງເອງ), ແຕ່ມີລາຍຊື່ທີ່ຄົບຖ້ວນຂອງຄ່າທີ່ກຳນົດໄວ້ລ່ວງໜ້າ. ພວກເຮົາຍັງຖືກໃຈວ່າທຸກຢ່າງໄວແລະລື່ນໄຫວຂະນາດໃດ, ການນຳໃຊ້ທີ່ງ່າຍດາຍ (ທີ່ບໍ່ມີ backend), ແຜນທີ່ນ້ອຍທີ່ສະເລ່ດສະເລີນ, ແລະການອະທິບາຍຢ່າງລະອຽດໃນ <a %(phiresky_github)s>ບົດຄວາມບລ໋ອກ</a> ຂອງພວກເຂົາ. ຜົນງານທີ່ສຸດຍອດ, ແລະຜູ້ຊະນະທີ່ຄວນໄດ້ຮັບລາງວັນ! - ອັນນາ ແລະທີມ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ພວກເຮົາມີຄວາມຮູ້ສຶກຂອບຄຸນເຕັມຫົວໃຈ. ຄວາມຄິດທີ່ໂດດເດັ່ນ ຕຶກສູງສຳລັບຄວາມຫາຍາກ ສະໄລເດີຫຼາຍໆສໍາລັບການປຽບທຽບ Datasets, ຄືວ່າທ່ານເປັນ DJ. ແຖບມາດຕະຖານທີ່ມີຈໍານວນປື້ມ. ປ້າຍຊື່ທີ່ສວຍງາມ. ສີພື້ນທີ່ເຢັນໆແລະແຜນທີ່ຄວາມຮ້ອນ. ມຸມມອງແຜນທີ່ທີ່ເປັນເອກະລັກແລະຕົວກັ່ນຕອງ ຄຳອະທິບາຍ, ແລະສະຖິຕິສົດດ້ວຍ ສະຖິຕິສົດ ຄວາມຄິດແລະການນຳໃຊ້ທີ່ພວກເຮົາຖືກໃຈພິເສດ: ພວກເຮົາສາມາດດໍາເນີນຕໍ່ໄປອີກພັກໜຶ່ງ, ແຕ່ຂໍຢຸດຢູ່ນີ້. ຈົ່ງແນ່ໃຈວ່າໄດ້ເບິ່ງການສົ່ງທັງຫມົດ <a %(annas_archive)s>ທີ່ນີ້</a>, ຫຼືດາວໂຫລດ <a %(a_2025_01_isbn_visualization_files)s>torrent ລວມຂອງພວກເຮົາ</a>. ມີການສົ່ງຫຼາຍໆ, ແລະແຕ່ລະອັນນໍາມາຊຶ່ງມຸມມອງທີ່ເປັນເອກະລັກ, ບໍ່ວ່າຈະເປັນດ້ານ UI ຫຼືການນໍາໃຊ້. ພວກເຮົາຈະຢ່າງນ້ອຍສຸດນໍາການສົ່ງທີ່ໄດ້ອັນດັບທີ່ໜຶ່ງເຂົ້າໄປໃນເວັບໄຊທ໌ຫຼັກຂອງພວກເຮົາ, ແລະອາດຈະບາງອັນອື່ນໆ. ພວກເຮົາຍັງໄດ້ເລີ່ມຄິດວ່າຈະຈັດການຂະບວນການຄົ້ນຫາ, ຢືນຢັນ, ແລະຈຶ່ງຈັດເກັບປື້ມທີ່ຫາຍາກຢ່າງໃດ. ມີຂ່າວເພີ່ມເຕີມຢູ່ດ້ານນີ້. ຂອບໃຈທຸກຄົນທີ່ເຂົ້າຮ່ວມ. ມັນປະຫລາດໃຈທີ່ມີຄົນຫຼາຍໆທີ່ໃສ່ໃຈ. ການປ່ຽນປ່ຽນງ່າຍຂອງ Datasets ສໍາລັບການປຽບທຽບຢ່າງວ່ອງໄວ. CADAL SSNOs ຂໍ້ມູນລົ່ວຂອງ CERLALC DuXiu SSIDs ດັດຊະນີ eBook ຂອງ EBSCOhost Google Books Goodreads ຄັງຂໍ້ມູນອິນເຕີເນັດ ISBNdb ທະບຽນຜູ້ພິມລະດັບໂລກ ISBN Libby Nexus/STC OCLC/Worldcat OpenLibrary ຫໍສະໝຸດແຫ່ງຊາດລັດເຊຍ ຫໍສະໝຸດຈັກພັດຂອງ Trantor ອັນດັບສອງ $3,000: hypha “ໃນຂະນະທີ່ສີ່ຫຼ່ຽມຈັດແລະສີ່ຫຼ່ຽມຜືນຜ້າແມ່ນມີຄວາມພໍໃຈທາງຄະນິດສາດ, ພວກມັນບໍ່ໄດ້ໃຫ້ຄວາມໄດ້ປຽບທາງທີ່ຕັ້ງໃນບົດບາດຂອງການສະແດງແຜນທີ່. ຂ້ອຍເຊື່ອວ່າຄວາມບໍ່ສົມດຸນທີ່ມີຢູ່ໃນ Hilbert ຫຼື Morton ຄລາສສິກບໍ່ແມ່ນຂໍ້ບົກພ່ອງແຕ່ແມ່ນຄຸນສົມບັດ. ເຊັ່ນດຽວກັບຮູບຮ່າງຂອງລົງເທົ້າຂອງອິຕາລີທີ່ມີຊື່ສຽງທີ່ທຳໃຫ້ມັນຈົດຈຳໄດ້ທັນທີໃນແຜນທີ່, ຄວາມພິເສດຂອງເສັ້ນຄວາມໂຄ້ງເຫຼົ່ານີ້ອາດຈະເປັນຈຸດສັງເກດທາງຄວາມຄິດ. ຄວາມແຕກຕ່າງນີ້ສາມາດສົ່ງເສີມຄວາມຈື່ຈຳທາງພື້ນທີ່ແລະຊ່ວຍໃຫ້ຜູ້ໃຊ້ຈັດຕົວເອງໄດ້, ອາດຈະທຳໃຫ້ການຄົ້ນຫາພື້ນທີ່ຈຸດເຈັດຫຼືສັງເກດແບບແຜນທີ່ງ່າຍຂຶ້ນ.” ອີກຫນຶ່ງ <a %(annas_archive_note_2913)s>ຜົນງານສຸດຍອດ</a>. ບໍ່ຍືດຫຍຸ່ນເທົ່າອັນດັບທີ່ໜຶ່ງ, ແຕ່ພວກເຮົາຈິງໆຖືກໃຈການສະແດງຜົນໃນລະດັບພາບລວມຂອງມັນຫຼາຍກວ່າອັນດັບທີ່ໜຶ່ງ (ເສັ້ນຄວາມໂຄ້ງທີ່ເຕັມໄປດ້ວຍພື້ນທີ່, ຂອບ, ການຕິດປ້າຍຊື່, ການເນັ້ນສີ, ການເລື່ອນພາບ, ແລະການຂະຫຍາຍພາບ). ຄວາມຄິດເຫັນ <a %(annas_archive_note_2971)s>ຂອງ Joe Davis</a> ໄດ້ສະທ້ອນຄວາມຮູ້ສຶກຂອງພວກເຮົາ: ແລະຍັງມີຕົວເລືອກຫຼາຍສຳລັບການສະແດງຜົນແລະການສ້າງພາບ, ພ້ອມກັບ UI ທີ່ລື່ນໄຫວແລະງ່າຍຕໍ່ການໃຊ້ງານຢ່າງຫຼາຍ. ອັນດັບສອງທີ່ແຂງແກ່ນ! ອັນດັບສາມ $500 #1: maxlion ໃນ <a %(annas_archive_note_2940)s>ຜົນງານ</a> ນີ້ພວກເຮົາຖືກໃຈກັບມຸມມອງທີ່ແຕກຕ່າງກັນ, ໂດຍສະເພາະມຸມມອງການປຽບທຽບແລະມຸມມອງຜູ້ຈັດພິມ. ອັນດັບສາມ $500 #2: abetusk ໃນຂະນະທີ່ UI ບໍ່ໄດ້ມີຄວາມປະນີດທີ່ສຸດ, <a %(annas_archive_note_2917)s>ຜົນງານ</a> ນີ້ກວດສອບຫຼາຍຂໍ້ກຳນົດ. ພວກເຮົາຖືກໃຈຄຸນສົມບັດການປຽບທຽບຂອງມັນເປັນພິເສດ. ອັນດັບສາມ $500 #3: conundrumer0 ເຊັ່ນດຽວກັບອັນດັບທີ່ໜຶ່ງ, <a %(annas_archive_note_2975)s>ຜົນງານ</a> ນີ້ໄດ້ປະທັບໃຈພວກເຮົາດ້ວຍຄວາມຍືດຫຍຸ່ນຂອງມັນ. ສຸດທ້າຍນີ້ແມ່ນສິ່ງທີ່ທຳໃຫ້ເຄື່ອງມືການສະແດງຜົນທີ່ຍິ່ງໃຫຍ່: ຄວາມຍືດຫຍຸ່ນສູງສຸດສຳລັບຜູ້ໃຊ້ທີ່ມີຄວາມສາມາດ, ໃນຂະນະທີ່ຮັກສາຄວາມງ່າຍສຳລັບຜູ້ໃຊ້ທົ່ວໄປ. ອັນດັບສາມ $500 #4: charelf ຜົນງານ <a %(annas_archive_note_2947)s>ສຸດທ້າຍ</a> ທີ່ໄດ້ຮັບລາງວັນຄ່າຫຍັງແມ່ນຄ່ອນຂ້າງພື້ນຖານ, ແຕ່ມີຄຸນສົມບັດທີ່ເປັນເອກະລັກທີ່ພວກເຮົາຖືກໃຈຫຼາຍ. ພວກເຮົາຖືກໃຈວ່າພວກເຂົາສະແດງວ່າມີຊຸດຂໍ້ມູນຈຳນວນຫຼາຍຄອບຄຸມ ISBN ທີ່ຈະເປັນມາດຕະຖານຂອງຄວາມນິຍົມ/ຄວາມເຊື່ອຖືໄດ້. ພວກເຮົາຍັງຖືກໃຈຄວາມງ່າຍແຕ່ມີປະສິດທິພາບຂອງການໃຊ້ slider ຄວາມທຶບໃສ່ສຳລັບການປຽບທຽບ. ພື້ນຫຼັງ ອາຄາຍອັນນາຈະບັນລຸພາລະກິດຂອງການສໍາຮອງຄວາມຮູ້ຂອງມະນຸດທັງຫມົດໄດ້ຢ່າງໃດ, ຖ້າບໍ່ຮູ້ວ່າປື້ມໃດຍັງຢູ່ທີ່ນັ່ນ? ພວກເຮົາຕ້ອງການລາຍການທີ່ຕ້ອງເຮັດ. ວິທີຫນຶ່ງໃນການວາດແຜນນີ້ຄືຜ່ານເລກ ISBN, ທີ່ຕັ້ງແຕ່ປີ 1970 ໄດ້ຖືກກໍານົດໃຫ້ກັບປື້ມທຸກເລື່ອງທີ່ພິມ (ໃນປະເທດສ່ວນໃຫຍ່). ລາຄາລາວ $10,000 ການພິຈາລະນາຢ່າງເຂັ້ມງວດຈະຖືກໃຫ້ກັບຄວາມສາມາດໃນການໃຊ້ງານແລະຄວາມສວຍງາມຂອງມັນ. ສະແດງ metadata ຈິງສຳລັບ ISBN ລາຍບຸກຄົນເມື່ອຂະຫຍາຍເຂົ້າ, ເຊັ່ນຊື່ແລະຜູ້ຂຽນ. ປັບປຸງສະເປດທີ່ໃຊ້ພື້ນທີ່ດີຂຶ້ນ. ຕົວຢ່າງເຊັ່ນ zig-zag, ຈາກ 0 ຫາ 4 ໃນແຖວທຳອິດແລະກັບຄືນ (ໃນທາງກັບກັນ) ຈາກ 5 ຫາ 9 ໃນແຖວທີສອງ — ປະຍຸກຕໃຊ້ຢ່າງຕໍ່ເນື່ອງ. ແຜນສີທີ່ແຕກຕ່າງກັນຫຼືທີ່ສາມາດປັບແຕ່ງໄດ້. ມຸມມອງພິເສດສຳລັບການປຽບທຽບຊຸດຂໍ້ມູນ. ວິທີການດັບບັກບັນຫາ, ເຊັ່ນ metadata ອື່ນໆທີ່ບໍ່ສົມດຸນກັນດີ (ເຊັ່ນຊື່ທີ່ແຕກຕ່າງກັນຫຼາຍ). ການຈົດບັນທຶກຮູບພາບດ້ວຍຄວາມຄິດເຫັນກ່ຽວກັບ ISBN ຫຼືຂອບເຂດ. ວິທີການສົມມຸດສຳລັບການຈຳແນກປື້ມທີ່ຫາຍາກຫຼືທີ່ມີຄວາມສ່ຽງ. Fork ຄັງນີ້, ແລະແກ້ໄຂບລັອກໂພສ HTML ນີ້ (ບໍ່ອະນຸຍາດໃຫ້ມີພື້ນຫລັງອື່ນໃດນອກຈາກພື້ນຫລັງ Flask ຂອງພວກເຮົາ). ເຮັດໃຫ້ຮູບພາບຂ້າງເທິງສາມາດຂະຫຍາຍໄດ້ຢ່າງລື່ນໄຫລ, ດັ່ງນັ້ນທ່ານສາມາດຂະຫຍາຍໄປທີ່ ISBN ລາຍບຸກຄົນ. ການຄລິກ ISBN ຄວນຈະນຳທ່ານໄປທີ່ໜ້າ metadata ຫຼືການຄົ້ນຫາໃນ ຄັງຂອງອັນນາ. ທ່ານຕ້ອງຍັງສາມາດປ່ຽນລະຫວ່າງຊຸດຂໍ້ມູນທີ່ແຕກຕ່າງກັນທັງຫມົດ. ຂອບເຂດປະເທດແລະຂອບເຂດຜູ້ພິມຄວນຈະຖືກເນັ້ນໃຫ້ເຫັນເມື່ອລາຍເຂົ້າ. ທ່ານສາມາດໃຊ້ຕົວຢ່າງເຊັ່ນ <a %(github_xlcnd_isbnlib)s>data4info.py ໃນ isbnlib</a> ສຳລັບຂໍ້ມູນປະເທດ, ແລະການຂູດ “isbngrp” ຂອງພວກເຮົາສຳລັບຜູ້ພິມ (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). ມັນຕ້ອງເຮັດວຽກໄດ້ດີທັງໃນເຄື່ອງຄອມພິວເຕີແລະໂທລະສັບມືຖື. ມີຫຼາຍທີ່ຈະສຳຫຼວດທີ່ນີ້, ດັ່ງນັ້ນພວກເຮົາຈະປະກາດລາຄາລາວສຳລັບການປັບປຸງການສະແດງພາບຂ້າງເທິງ. ບໍ່ເຫມືອນລາຄາລາວສ່ວນໃຫຍ່ຂອງພວກເຮົາ, ອັນນີ້ມີຂອບເຂດເວລາ. ທ່ານຕ້ອງ <a %(annas_archive)s>ສົ່ງ</a> ລະຫັດເປີດຊອດຂອງທ່ານພາຍໃນວັນທີ 2025-01-31 (23:59 UTC). ການສົ່ງທີ່ດີທີ່ສຸດຈະໄດ້ຮັບ $6,000, ອັນດັບທີສອງແມ່ນ $3,000, ແລະອັນດັບທີສາມແມ່ນ $1,000. ລາຄາລາວທັງຫມົດຈະຖືກມອບໃຫ້ໂດຍໃຊ້ Monero (XMR). ຂ້າງລຸ່ມນີ້ແມ່ນເກນຕົ້ນຕໍ້ຂັ້ນຕ່ຳ. ຖ້າບໍ່ມີການສົ່ງໃດທີ່ສົມບູນກັບເກນ, ພວກເຮົາອາດຈະຍັງມອບລາຄາລາວບາງອັນ, ແຕ່ນັ້ນຈະຢູ່ທີ່ດຸລະພິຈານຂອງພວກເຮົາ. ສຳລັບຄະແນນເພີ່ມ (ເຫຼົ່ານີ້ເປັນພຽງແນວຄິດ — ປ່ອນໃຫ້ຄວາມຄິດສ້າງສັນຂອງທ່ານເດີນທາງໄປໄດ້): ຮູບພາບນີ້ມີຂະໜາດ 1000×800 ພິກເຊວ. ແຕ່ລະພິກເຊວແທນ 2,500 ISBNs. ຖ້າພວກເຮົາມີໄຟລ໌ສໍາລັບ ISBN, ພວກເຮົາຈະເຮັດໃຫ້ພິກເຊວນັ້ນຂຽວຂຶ້ນ. ຖ້າພວກເຮົາຮູ້ວ່າ ISBN ໄດ້ຖືກອອກໃຫ້, ແຕ່ພວກເຮົາບໍ່ມີໄຟລ໌ທີ່ກົງກັນ, ພວກເຮົາຈະເຮັດໃຫ້ມັນແດງຂຶ້ນ. ໃນຂະໜາດບໍ່ເກີນ 300kb, ຮູບພາບນີ້ສະຫຼຸບຄວາມເປັນລາຍຊື່ປື້ມທີ່ເປີດທີ່ໃຫຍ່ທີ່ສຸດທີ່ເຄີຍຖືກຮວບຮວມໃນປະຫວັດສາດຂອງມະນຸດ (ບາງສ່ວນຂອງ GB ບີບອັດໃນຮູບແບບເຕັມ). ມັນຍັງສະແດງວ່າ: ຍັງມີວຽກຫຼາຍທີ່ຕ້ອງສໍາຮອງປື້ມ (ພວກເຮົາມີພຽງແຕ່ 16%%). ການສະແດງພາບຂອງ ISBN ທັງຫມົດ — ລາງວັນ $10,000 ໂດຍ 2025-01-31 ຮູບພາບນີ້ແທນຄວາມເປັນລາຍຊື່ປື້ມທີ່ເປີດທີ່ໃຫຍ່ທີ່ສຸດທີ່ເຄີຍຖືກຮວບຮວມໃນປະຫວັດສາດຂອງມະນຸດ. ນອກຈາກຮູບພາບພາບລວມ, ພວກເຮົາຍັງສາມາດເບິ່ງທີ່ຊຸດຂໍ້ມູນລາຍບຸກຄົນທີ່ພວກເຮົາໄດ້ຮັບ. ໃຊ້ປຸ່ມລົດລົ້ມແລະປຸ່ມເພື່ອປ່ຽນລະຫວ່າງພວກເຂົາ. ມີແບບຢ່າງຫຼາຍທີ່ນ່າສົນໃຈໃນຮູບພາບເຫຼົ່ານີ້. ເປັນຫຍັງຈຶ່ງມີຄວາມສະເຫມີຂອງເສັ້ນແລະບລັອກ, ທີ່ເບິ່ງຄືວ່າຈະເກີດຂຶ້ນທີ່ຂະໜາດທີ່ແຕກຕ່າງກັນ? ພື້ນທີ່ວ່າງເປັນຫຍັງ? ເປັນຫຍັງຊຸດຂໍ້ມູນບາງອັນຈຶ່ງມີຄວາມຫນາແນ້ນຫຼາຍ? ພວກເຮົາຈະປ່ອນຄຳຖາມເຫຼົ່ານີ້ເປັນການຝຶກຫັດສຳລັບຜູ້ອ່ານ. ໄຟລ໌ແລະ metadata ທີ່ຫຼາກຫຼາຍ, ໃນຮູບແບບທີ່ໃກ້ຄຽງກັບຕົ້ນສະບັບທີ່ສຸດ. ຂໍ້ມູນໄບນາຣີສາມາດຖືກສົ່ງໂດຍກົງໂດຍເວັບເຊີເວີເຊັ່ນ Nginx. ຕົວລະບຸທີ່ຫຼາກຫຼາຍໃນຫ້ອງສະໝຸດຕົ້ນສະບັບ, ຫຼືແມ່ນຂາດຕົວລະບຸ. ການປ່ອຍແຍກຂອງ metadata ກັບຂໍ້ມູນໄຟລ໌, ຫຼືການປ່ອຍຂອງ metadata ເທົ່ານັ້ນ (ຕົວຢ່າງເຊັ່ນການປ່ອຍ ISBNdb ຂອງພວກເຮົາ). ການແຈກຈ່າຍຜ່ານ torrents, ແຕ່ມີຄວາມເປັນໄປໄດ້ຂອງວິທີການແຈກຈ່າຍອື່ນໆ (ຕົວຢ່າງເຊັ່ນ IPFS). ບັນທຶກທີ່ບໍ່ສາມາດປ່ຽນແປງໄດ້, ເພາະພວກເຮົາຄວນສົມມຸດວ່າ torrents ຂອງພວກເຮົາຈະມີຊີວິດຢູ່ຕະຫຼອດໄປ. ການປ່ອຍເພີ່ມເຕີມ / ການປ່ອຍທີ່ສາມາດເພີ່ມໄດ້. ສາມາດອ່ານແລະຂຽນໂດຍເຄື່ອງຈັກ, ສະດວກແລະໄວ, ເປັນພິເສດສຳລັບ stack ຂອງພວກເຮົາ (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). ການກວດສອບໂດຍມະນຸດທີ່ຄ່ອນຂ້າງງ່າຍ, ແຕ່ນີ້ເປັນລຳດັບຮອງຈາກຄວາມສາມາດອ່ານໂດຍເຄື່ອງຈັກ. ງ່າຍຕໍ່ການເພີ່ມເມັດຕະຖານຂອງພວກເຮົາດ້ວຍ seedbox ທີ່ເຊົ່າມາມາດຕະຖານ. ຈຸດປະສົງຂອງການອອກແບບ ພວກເຮົາບໍ່ໃສ່ໃຈກ່ຽວກັບໄຟລ໌ທີ່ງ່າຍຕໍ່ການນຳທາງດ້ວຍມືໃນດິສກ໌, ຫຼືສາມາດຄົ້ນຫາໄດ້ໂດຍບໍ່ຕ້ອງປະມວນຜົນລ່ວງໜ້າ. ພວກເຮົາບໍ່ໃສ່ໃຈກ່ຽວກັບການທີ່ຈະເຂົ້າກັນໄດ້ໂດຍກົງກັບຊອບແວຫ້ອງສະໝຸດທີ່ມີຢູ່ແລ້ວ. ໃນຂະນະທີ່ຄວນງ່າຍສຳລັບໃຜກໍ່ຕາມທີ່ຈະເພີ່ມເກັບຂອງພວກເຮົາໂດຍໃຊ້ torrents, ພວກເຮົາບໍ່ຄາດຫວັງໃຫ້ໄຟລ໌ໃຊ້ງານໄດ້ໂດຍບໍ່ມີຄວາມຮູ້ທາງເທັກນິກທີ່ສຳຄັນແລະຄວາມມຸ່ງມັ່ນ. ການນຳໃຊ້ຫຼັກຂອງພວກເຮົາແມ່ນການແຈກຈ່າຍໄຟລ໌ແລະ metadata ທີ່ກ່ຽວຂ້ອງຈາກຄັງເກັບຂໍ້ມູນທີ່ມີຢູ່ແລ້ວຕ່າງໆ. ການພິຈາລະນາທີ່ສຳຄັນທີ່ສຸດຂອງພວກເຮົາແມ່ນ: ບາງສິ່ງທີ່ບໍ່ໃຊ່ຈຸດປະສົງ: ເພາະຄັງເກັບຂໍ້ມູນຂອງອັນນາເປັນໂຄດເປີດ, ພວກເຮົາຕ້ອງການທີ່ຈະໃຊ້ຮູບແບບຂອງພວກເຮົາໂດຍກົງ. ເມື່ອພວກເຮົາຟື້ນຟູດັດຊະນີຄົ້ນຫາຂອງພວກເຮົາ, ພວກເຮົາເຂົ້າເຖິງເສັ້ນທາງທີ່ມີຢູ່ໃນທົ່ວໄປເທົ່ານັ້ນ, ເພື່ອໃຫ້ໃຜກໍ່ຕາມທີ່ສຳຮອງຫ້ອງສະໝຸດຂອງພວກເຮົາສາມາດເລີ່ມຕົ້ນໄດ້ຢ່າງໄວ. <strong>AAC.</strong> AAC (Anna’s Archive Container) ແມ່ນລາຍການດຽວທີ່ປະກອບດ້ວຍ <strong>metadata</strong>, ແລະຂໍ້ມູນໄຟລ໌ທີ່ສາມາດມີໄດ້ <strong>binary data</strong>, ທັງສອງສ່ວນນີ້ບໍ່ສາມາດປ່ຽນແປງໄດ້. ມັນມີຕົວລະບຸທີ່ແຕກຕ່າງກັນທົ່ວໂລກ, ທີ່ເອີ້ນວ່າ <strong>AACID</strong>. <strong>AACID.</strong> ຮູບແບບຂອງ AACID ແມ່ນດັ່ງນີ້: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. ຕົວຢ່າງ, AACID ທີ່ເຮົາໄດ້ປ່ອນປ່ອຍແມ່ນ <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID range.</strong> ເພາະວ່າ AACIDs ປະກອບດ້ວຍເວລາທີ່ເພີ່ມຂຶ້ນຢ່າງຕໍ່ເນື່ອງ, ພວກເຮົາສາມາດໃຊ້ນັ້ນເພື່ອລະບຸຂອບເຂດພາຍໃນຄັງເກັບຂໍ້ມູນທີ່ຈະສະເພາະ. ພວກເຮົາໃຊ້ຮູບແບບນີ້: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, ທີ່ເວລາຈະລວມທັງໝົດ. ນີ້ສອດຄ່ອງກັບຮູບແບບ ISO 8601. ຂອບເຂດແມ່ນຕໍ່ເນື່ອງ, ແລະອາດຈະທັບຊ້ອນກັນ, ແຕ່ໃນກໍລະນີທີ່ທັບຊ້ອນກັນຕ້ອງປະກອບດ້ວຍບັນທຶກທີ່ຄືກັນກັບທີ່ເຄີຍປ່ອນປ່ອຍໃນຄັງເກັບຂໍ້ມູນນັ້ນ (ເພາະວ່າ AAC ບໍ່ສາມາດປ່ຽນແປງໄດ້). ບໍ່ອະນຸຍາດໃຫ້ຂາດບັນທຶກ. <code>{collection}</code>: ຊື່ຄັງເກັບຂໍ້ມູນ, ທີ່ອາດຈະປະກອບດ້ວຍຕົວອັກສອນ ASCII, ຕົວເລກ, ແລະຂີດລົງ (ບໍ່ມີຂີດລົງຄູ່). <code>{collection-specific ID}</code>: ຕົວລະບຸທີ່ຈະສະເພາະຄັງເກັບຂໍ້ມູນ, ຖ້າມີ, ຕົວຢ່າງເຊັ່ນ ID ຂອງ Z-Library. ອາດຈະຖືກລະບຸຫຼືຖືກຕັດທອນ. ຕ້ອງຖືກລະບຸຫຼືຖືກຕັດທອນຖ້າ AACID ຈະເກີນ 150 ຕົວອັກສອນ. <code>{ISO 8601 timestamp}</code>: ຮູບແບບສັ້ນຂອງ ISO 8601, ເສມີໃນ UTC, ຕົວຢ່າງເຊັ່ນ <code>20220723T194746Z</code>. ຕົວເລກນີ້ຈະຕ້ອງເພີ່ມຂຶ້ນຢ່າງຕໍ່ເນື່ອງສຳລັບການປ່ອນປ່ອຍແຕ່ລະຄັ້ງ, ແມ່ນວ່າຄວາມຫມາຍທີ່ແນ່ນອນຂອງມັນສາມາດແຕກຕ່າງກັນໃນແຕ່ລະຄັງເກັບຂໍ້ມູນ. ພວກເຮົາແນະນຳໃຫ້ໃຊ້ເວລາຂອງການຂູດຂໍ້ມູນ ຫຼື ການສ້າງ ID. <code>{shortuuid}</code>: UUID ແຕ່ຖືກບີບໃຫ້ເປັນ ASCII, ຕົວຢ່າງເຊັ່ນໃຊ້ base57. ໃນປັດຈຸບັນພວກເຮົາໃຊ້ຫ້ອງສະໝຸດ Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Binary data folder.</strong> ໂຟນເດີທີ່ປະກອບດ້ວຍຂໍ້ມູນໄຟລ໌ຂອງຂອບເຂດ AACs, ສຳລັບຄັງເກັບຂໍ້ມູນທີ່ຈະສະເພາະ. ນີ້ມີຄຸນສົມບັດດັ່ງຕໍ່ໄປນີ້: ໂຟນເດີຕ້ອງປະກອບດ້ວຍໄຟລ໌ຂໍ້ມູນສຳລັບທຸກ AAC ພາຍໃນຂອບເຂດທີ່ລະບຸ. ແຕ່ລະໄຟລ໌ຂໍ້ມູນຕ້ອງມີ AACID ເປັນຊື່ໄຟລ໌ (ບໍ່ມີສຸກສິນ). ຊື່ໂຟນເດີຕ້ອງເປັນຂອບເຂດ AACID, ນຳໜ້າດ້ວຍ <code style="color: green">annas_archive_data__</code>, ແລະບໍ່ມີສຸກສິນ. ຕົວຢ່າງ, ການປ່ອນປ່ອຍຂອງພວກເຮົາມີໂຟນເດີທີ່ມີຊື່ວ່າ<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. ຂໍແນະນຳໃຫ້ເຮັດໃຫ້ໂຟນເດີເຫຼົ່ານີ້ມີຂະໜາດທີ່ສາມາດຄວບຄຸມໄດ້, ຕົວຢ່າງເຊັ່ນບໍ່ໃຫ້ໃຫຍ່ກວ່າ 100GB-1TB ແຕ່ລະໂຟນເດີ, ແມ່ນວ່າຄຳແນະນຳນີ້ອາດຈະປ່ຽນແປງໄດ້ຕາມເວລາ. <strong>Collection.</strong> ແຕ່ລະ AAC ຈະຢູ່ໃນຄັງເກັບຂໍ້ມູນ, ທີ່ຕາມຄວາມຫມາຍແມ່ນລາຍຊື່ຂອງ AAC ທີ່ມີຄວາມສອດຄ່ອງກັນທາງຄວາມຫມາຍ. ນັ້ນຫມາຍຄວາມວ່າຖ້າທ່ານປ່ຽນແປງຮູບແບບຂອງ metadata ຢ່າງສຳຄັນ, ທ່ານຈະຕ້ອງສ້າງຄັງເກັບຂໍ້ມູນໃໝ່. ມາດຕະຖານ <strong>Metadata file.</strong> ໄຟລ໌ metadata ປະກອບດ້ວຍ metadata ຂອງຂອບເຂດ AACs, ສຳລັບຄັງເກັບຂໍ້ມູນທີ່ຈະສະເພາະ. ນີ້ມີຄຸນສົມບັດດັ່ງຕໍ່ໄປນີ້: <code>data_folder</code> ແມ່ນທີ່ສາມາດມີໄດ້, ແລະແມ່ນຊື່ຂອງໂຟນເດີຂໍ້ມູນໄຟລ໌ທີ່ປະກອບດ້ວຍຂໍ້ມູນໄຟລ໌ທີ່ສອດຄ່ອງ. ຊື່ໄຟລ໌ຂອງຂໍ້ມູນໄຟລ໌ທີ່ສອດຄ່ອງພາຍໃນໂຟນເດີນັ້ນແມ່ນ AACID ຂອງບັນທຶກ. ແຕ່ລະວັດຖຸ JSON ຕ້ອງປະກອບດ້ວຍຂໍ້ມູນຕໍາແໜ່ງສູງສຸດດັ່ງຕໍ່ໄປນີ້: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (ທີ່ສາມາດມີໄດ້). ບໍ່ອະນຸຍາດໃຫ້ມີຂໍ້ມູນອື່ນໃດ. ຊື່ໄຟລ໌ຕ້ອງເປັນຂອບເຂດ AACID, ນຳໜ້າດ້ວຍ <code style="color: red">annas_archive_meta__</code> ແລະຕາມດ້ວຍ <code>.jsonl.zstd</code>. ຕົວຢ່າງ, ການປ່ອນປ່ອຍຂອງພວກເຮົາແມ່ນມີຊື່ວ່າ<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. ຕາມທີ່ບອກໄວ້ໂດຍສະກຸນໄຟລ໌, ປະເພດໄຟລ໌ແມ່ນ <a %(jsonlines)s>JSON Lines</a> ທີ່ຖືກບີບດ້ວຍ <a %(zstd)s>Zstandard</a>. <code>metadata</code> ແມ່ນ metadata ທີ່ສຸ່ມເສີນ, ຕາມຄວາມຫມາຍຂອງຄັງເກັບຂໍ້ມູນ. ມັນຕ້ອງສອດຄ່ອງກັນທາງຄວາມຫມາຍພາຍໃນຄັງເກັບຂໍ້ມູນ. ຄຳນຳໜ້າ <code style="color: red">annas_archive_meta__</code> ອາດຈະຖືກປັບໃຫ້ເປັນຊື່ຂອງສະຖາບັນຂອງທ່ານ, ຕົວຢ່າງເຊັ່ນ <code style="color: red">my_institute_meta__</code>. <strong>“records” and “files” collections.</strong> ຕາມຂະບວນການ, ມັນມັກຈະສະດວກທີ່ຈະປ່ອນປ່ອຍ “records” ແລະ “files” ເປັນຄັງເກັບຂໍ້ມູນທີ່ແຕກຕ່າງກັນ, ດັ່ງນັ້ນພວກເຂົາສາມາດຈະຖືກປ່ອນປ່ອຍໃນຕາຕະລາງທີ່ແຕກຕ່າງກັນ, ຕົວຢ່າງເຊັ່ນຕາມອັດຕາການຂູດຂໍ້ມູນ. “Record” ແມ່ນຄັງເກັບຂໍ້ມູນທີ່ມີແຕ່ metadata, ປະກອບດ້ວຍຂໍ້ມູນເຊັ່ນຊື່ປຶ້ມ, ຜູ້ຂຽນ, ISBN, ເປັນຕົ້ນ, ໃນຂະນະທີ່ “files” ແມ່ນຄັງເກັບຂໍ້ມູນທີ່ປະກອບດ້ວຍໄຟລ໌ຈິງໆ (pdf, epub). ສຸດທ້າຍ, ພວກເຮົາໄດ້ຕົກລົງໃນມາດຕະຖານທີ່ຄ່ອນຂ້າງງ່າຍ. ມັນຄ່ອນຂ້າງຫຼວງຫຼືບໍ່ມີການກຳນົດແບບຕາຍຕົວ, ແລະເປັນງານທີ່ກຳລັງດຳເນີນຢູ່. ພວກເຮົາຈຶ່ງຕັດສິນໃຈທີ່ຈະມາດຕະຖານການປ່ອຍຂອງພວກເຮົາ. ນີ້ແມ່ນບລັອກໂພສທາງເທັກນິກທີ່ພວກເຮົາຈະແນະນຳມາດຕະຖານຂອງພວກເຮົາ: <strong>ພາຫະນະຂອງຄັງເກັບຂໍ້ມູນຂອງອັນນາ</strong>. ຊອບແວວິທະຍາສາດ ແລະ ວິສະວະກຳ ສິ່ງທີ່ເປັນນິຍາຍ ຫຼື ບັນເທິງຂອງທຸກສິ່ງທີ່ກ່າວມາ ຂໍ້ມູນພູມສາດ (ເຊັ່ນ ແຜນທີ່, ການສຳຫຼວດທາງທະນິດິນ) ຂໍ້ມູນພາຍໃນຈາກບໍລິສັດ ຫຼື ລັດຖະບານ (ການຮົ່ວໄຫຼ) ຂໍ້ມູນການວັດທະສະນະ ເຊັ່ນ ການວັດທະສະນະທາງວິທະຍາສາດ, ຂໍ້ມູນເສດຖະກິດ, ບົດລາຍງານຂອງບໍລິສັດ ບັນທຶກ metadata ໂດຍທົ່ວໄປ (ຂອງອີງຄວາມຈິງ ແລະ ນິຍາຍ; ຂອງສື່ອື່ນໆ, ສິລະປະ, ຄົນ, ແລະອື່ນໆ; ລວມທັງການທົບທວນ) ປື້ມອີງຄວາມຈິງ ນິຕິຍະສານອີງຄວາມຈິງ, ຂ່າວສານ, ຄູ່ມື ບັນທຶກການສົນທະນາອີງຄວາມຈິງ, ສາລະຄະດີ, ພອດແຄສ ຂໍ້ມູນທີ່ມີຊີວິດ ເຊັ່ນ ລຳດັບ DNA, ເມັດພືດ, ຫຼື ຕົວຢ່າງຈຸລິນຊີ ເວັບໄຊວິທະຍາສາດ ແລະ ວິສະວະກຳ, ການສົນທະນາອອນໄລນ໌ ບັນທຶກການພິພາກສາ ຫຼື ການດຳເນີນຄະດີ ມີຄວາມສ່ຽງທີ່ຈະຖືກທຳລາຍພິເສດ (ເຊັ່ນ ໂດຍສົງຄາມ, ການຕັດງົບປະມານ, ຄະດີຄວາມ, ຫຼື ການຂົ່ມເຫັງທາງການເມືອງ) ຫາຍາກ ມີຄວາມສຳຄັນພິເສດ ການຈັດອັນດັບໃນລາຍການນີ້ມີຄວາມບໍ່ແນ່ນອນບາງສ່ວນ — ມີຫຼາຍລາຍການທີ່ເປັນຄວາມຄິດເຫັນທີ່ບໍ່ຄົງທີ່ ຫຼື ມີຄວາມຂັດແຍ່ງພາຍໃນທີມຂອງພວກເຮົາ — ແລະພວກເຮົາອາດຈະລືມປະເພດທີ່ສຳຄັນບາງປະເພດ. ແຕ່ນີ້ແມ່ນປະມານວ່າພວກເຮົາໃຫ້ຄວາມສຳຄັນຢ່າງໃດ. ບາງລາຍການໃນນີ້ມີຄວາມແຕກຕ່າງກັນຫຼາຍຈົນພວກເຮົາບໍ່ຈຳເປັນຕ້ອງກັງວົນ (ຫຼື ຖືກດູແລໂດຍສະຖາບັນອື່ນໆແລ້ວ), ເຊັ່ນ ຂໍ້ມູນທີ່ມີຊີວິດ ຫຼື ຂໍ້ມູນພູມສາດ. ແຕ່ສ່ວນໃຫຍ່ຂອງລາຍການໃນນີ້ແມ່ນສຳຄັນຕໍ່ພວກເຮົາຈິງໆ. ອີກປັດໃຈທີ່ສຳຄັນໃນການຈັດລຳດັບຄວາມສຳຄັນຂອງພວກເຮົາແມ່ນຄວາມສ່ຽງທີ່ຜົນງານບາງຢ່າງຈະຖືກທຳລາຍ. ພວກເຮົາມັກຈະໃຫ້ຄວາມສຳຄັນກັບຜົນງານທີ່: ສຸດທ້າຍ, ພວກເຮົາໃຫ້ຄວາມສຳຄັນກັບຂະໜາດ. ພວກເຮົາມີເວລາ ແລະ ງົບປະມານຈຳກັດ, ດັ່ງນັ້ນພວກເຮົາຈະໃຫ້ຄວາມສຳຄັນກັບການບັນທຶກປື້ມ 10,000 ເລື່ອງຫຼາຍກວ່າ 1,000 ເລື່ອງ — ຖ້າມັນມີຄວາມຄຸ້ມຄ່າ ແລະ ສ່ຽງພໍໆກັນ. ຫ້ອງສະໝຸດເງົາ 1. ການເລືອກໂດເມນ / ປັດຊະຍາ ບໍ່ຂາດແຄນຄວາມຮູ້ແລະມໍລະດົກທາງວັດທະນະທຳທີ່ຈະຖືກບັນທຶກ, ຊຶ່ງອາດຈະທຳໃຫ້ຮູ້ສຶກຫຼວງຫຼາຍ. ນັ້ນເປັນເຫດຜົນທີ່ມັກຈະມີປະໂຫຍດໃນການໃຊ້ເວລາຄິດວ່າທ່ານສາມາດມີສ່ວນຮ່ວມຢ່າງໃດ. ທຸກຄົນມີວິທີຄິດທີ່ແຕກຕ່າງກັນກ່ຽວກັບນີ້, ແຕ່ນີ້ແມ່ນຄຳຖາມບາງຢ່າງທີ່ທ່ານອາດຈະຖາມຕົວເອງ: ທ່ານມີທັກສະຫຍັງທີ່ສາມາດນຳໃຊ້ໃນປະໂຫຍດຂອງທ່ານ? ຕົວຢ່າງ, ຖ້າທ່ານເປັນຜູ້ເຊີຍຄວາມປອດໄພອອນໄລນ໌, ທ່ານສາມາດຊອກຫາວິທີທີ່ຈະພິຊິດການປິດ IP ສຳລັບເປົ້າໝາຍທີ່ປອດໄພ. ຖ້າທ່ານເກັ່ງໃນການຈັດການຊຸມຊົນ, ອາດຈະສາມາດຮວມຄົນບາງຄົນໄວ້ຮອບເປົ້າໝາຍໄດ້. ມັນມີປະໂຫຍດທີ່ຈະຮູ້ການຂຽນໂປຣແກຣມບ້າງ, ເຖິງແມ່ນວ່າຈະເພື່ອຮັກສາຄວາມປອດໄພຂອງການປະຕິບັດງານຕະຫຼອດຂະບວນການນີ້. ພື້ນທີ່ທີ່ມີຜົນສູງທີ່ຈະໃຫ້ຄວາມສົນໃຈໃນການຈຸດຈໍາເປັນຄືຫຍັງ? ຖ້າທ່ານຈະໃຊ້ເວລາ X ຊົ່ວໂມງໃນການບັນທຶກຂໍ້ມູນທີ່ບໍ່ຖືກຕ້ອງຕາມກົດໝາຍ, ດັ່ງນັ້ນທ່ານຈະໄດ້ຮັບຜົນປະໂຫຍດທີ່ສູງທີ່ສຸດໄດ້ຢ່າງໃດ? ມີວິທີທີ່ແຕກຕ່າງທີ່ທ່ານຄິດກ່ຽວກັບນີ້ຫຼືບໍ່? ທ່ານອາດຈະມີຄວາມຄິດຫຼືວິທີການທີ່ສົນໃຈທີ່ຄົນອື່ນອາດຈະພາດໄປ. ທ່ານມີເວລາສຳລັບນີ້ຫຼາຍແຄ່ໃດ? ຄຳແນະນຳຂອງພວກເຮົາຄືໃຫ້ເລີ່ມຕົ້ນຂະຫນາດນ້ອຍແລະເຮັດໂຄງການທີ່ໃຫຍ່ຂຶ້ນເມື່ອທ່ານຄຸ້ນເຄີຍກັບມັນ, ແຕ່ມັນສາມາດກິນເວລາທັງໝົດ. ເປັນຫຍັງທ່ານສົນໃຈໃນນີ້? ທ່ານມີຄວາມຫຼົງໄຫຼໃນຫຍັງ? ຖ້າພວກເຮົາສາມາດໄດ້ຄົນຈຳນວນຫຼາຍທີ່ບັນທຶກສິ່ງທີ່ພວກເຂົາສົນໃຈເປັນພິເສດ, ມັນຈະຄອບຄຸມຫຼາຍຫຼາຍ! ທ່ານຈະຮູ້ຫຼາຍກວ່າຄົນທົ່ວໄປກ່ຽວກັບຄວາມຫຼົງໄຫຼຂອງທ່ານ, ເຊັ່ນຂໍ້ມູນທີ່ສຳຄັນທີ່ຈະບັນທຶກ, ຄວາມຮູ້ສຶກທີ່ດີທີ່ສຸດແລະຊຸມຊົນອອນໄລນ໌, ແລະອື່ນໆ. ການເລືອກໂດເມນ / ປັດຊະຍາ: ທ່ານຢາກຈະມຸ່ງເນັ້ນໃສ່ບ່ອນໃດ, ແລະເປັນຫຍັງ? ຄວາມຫລົງໄຫຼພິເສດ, ທັກສະ, ແລະສະພາບການທີ່ທ່ານສາມາດນຳໃຊ້ເພື່ອປະໂຫຍດຂອງທ່ານແມ່ນຫຍັງ? ການເລືອກເປົ້າໝາຍ: ທ່ານຈະສະທ້ອນຄັງເກັບຂໍ້ມູນໃດເປັນພິເສດ? ການຂູດຂໍ້ມູນ metadata: ການລົງທະບຽນຂໍ້ມູນກ່ຽວກັບໄຟລ໌, ໂດຍບໍ່ຈຳເປັນຕ້ອງດາວໂຫລດໄຟລ໌ (ທີ່ມັກຈະໃຫຍ່ກວ່າ) ເອງ. ການເລືອກຂໍ້ມູນ: ຂຶ້ນຢູ່ກັບ metadata, ການຈຳກັດຂໍ້ມູນທີ່ສຳຄັນທີ່ສຸດທີ່ຈະບັນທຶກໃນຂະນະນີ້. ອາດຈະເປັນທຸກຢ່າງ, ແຕ່ມັກຈະມີວິທີທີ່ສົມເຫດສົມຜົນໃນການປະຢັດພື້ນທີ່ແລະຄວາມກວ້າງຂອງສາຍ. ການຂູດຂໍ້ມູນ: ການໄດ້ຮັບຂໍ້ມູນຈິງໆ. ການແຈກຈ່າຍ: ການບັນຈຸໃສ່ໃນ torrents, ປະກາດມັນທີ່ໃດສັກແຫ່ງ, ໃຫ້ຄົນຊ່ວຍແຜ່ຂ່າວ. ເມື່ອພວກເຮົາເຮັດໂຄງການ, ມັນມີຂັ້ນຕອນສອງສາມຂັ້ນ: ນີ້ບໍ່ແມ່ນຂັ້ນຕອນທີ່ອິດສະຫຼະກັນໂດຍສິ້ນເຊິງ, ແລະມັກຈະມີຄວາມເຂົ້າໃຈຈາກຂັ້ນຕອນທີ່ຫຼັງສົ່ງກັບໄປທີ່ຂັ້ນຕອນທີ່ກ່ອນ. ຕົວຢ່າງ, ໃນຂະນະທີ່ຂູດຂໍ້ມູນ metadata ທ່ານອາດຈະຮູ້ວ່າເປົ້າໝາຍທີ່ທ່ານເລືອກມີກົດປ້ອງກັນທີ່ຢູ່ເຫນືອລະດັບຄວາມສາມາດຂອງທ່ານ (ເຊັ່ນການປິດ IP), ດັ່ງນັ້ນທ່ານຈຶ່ງກັບໄປຫາເປົ້າໝາຍທີ່ແຕກຕ່າງ. ຂ່າວສານກ່ຽວກັບ <a %(wikipedia_annas_archive)s>ຄັງເກັບຂອງ Anna</a>, ຫ້ອງສະໝຸດທີ່ເປີດທີ່ສຸດໃນປະຫວັດສາດຂອງມະນຸດ. ດາວໂຫລດຈາກເຊີບເວີພັນທະມິດ SciDB ຢືມຈາກພາຍນອກ ຢືມຈາກພາຍນອກ (ສຳລັບຜູ້ພິການການພິມ) ດາວໂຫລດຈາກພາຍນອກ ສຳຫຼວດຂໍ້ມູນເມຕາ ບັນຈຸໃນທໍເຣນ ກັບຄືນ  (+%(num)s ໂບນັດ) ບໍ່ມີຄ່າຈ້າງ ມີຄ່າຈ້າງ ຍົກເລີກ ໝົດອາຍຸ ລໍຖ້າ Anna ຢືນຢັນ ບໍ່ຖືກຕ້ອງ ຂໍ້ຄວາມຂ້າງລຸ່ມນີ້ດຳເນີນຕໍ່ໃນພາສາອັງກິດ. ໄປ ຣີເຊັດ ຂ້າມໄປຂ້າງໜ້າ ສຸດທ້າຍ ຖ້າທີ່ຢູ່ອີເມວຂອງທ່ານບໍ່ເຮັດວຽກໃນກະດານຂ່າວ Libgen, ພວກເຮົາແນະນຳໃຫ້ໃຊ້ <a %(a_mail)s>Proton Mail</a> (ຟຣີ). ທ່ານສາມາດ <a %(a_manual)s>ຮ້ອງຂໍດ້ວຍຕົວເອງ</a> ໃຫ້ບັນຊີຂອງທ່ານຖືກເປີດໃຊ້ງານ. (ອາດຈະຕ້ອງການ <a %(a_browser)s>ການຢືນຢັນບຣາວເຊີ</a> — ດາວໂຫລດໄດ້ບໍ່ຈຳກັດ!) ເຊີບເວີພັນທະມິດໄວ #%(number)s (ທີ່ແນະນຳ) (ໄວກວ່າແຕ່ມີລາຍຊື່ລໍຖ້າ) (ບໍ່ຕ້ອງກວດສອບຜ່ານບຣາວເຊີ) (ບໍ່ມີການຢືນຢັນຜ່ານບຣາວເຊີ ຫຼື ລາຍຊື່ລໍຖ້າ) (ບໍ່ມີລາຍຊື່ລໍຖ້າ ແຕ່ອາດຈະຊ້າຫຼາຍ) ເຊີບເວີພັນທະມິດຊ້າ #%(number)s ປື້ມສຽງ ປຶ້ມການຕູນ ຫນັງສື (ນິຍາຍ) ຫນັງສື (ບໍ່ແມ່ນນິຍາຍ) ຫນັງສື (ບໍ່ຮູ້ຈັກ) ບົດຄວາມນິຕິຍະສານ ນິຕະສານ ຄະແນນດົນຕີ ອື່ນໆ ເອກະສານມາດຕະຖານ ບໍ່ສາມາດແປງໜ້າທັງໝົດເປັນ PDF ໄດ້ ໝາຍເປັນບໍ່ດີໃນ Libgen.li ບໍ່ເຫັນໃນ Libgen.li ບໍ່ເຫັນໃນ Libgen.rs Fiction ບໍ່ເຫັນໃນ Libgen.rs Non-Fiction ການເຮັດ exiftool ລົ້ມເຫລວໃນໄຟລ໌ນີ້ ຖືກໝາຍເປັນ “ໄຟລ໌ບໍ່ດີ” ໃນ Z-Library ຂາດຫາຍຈາກ Z-Library ຖືກໝາຍເປັນ “ສະແປມ” ໃນ Z-Library ໄຟລບໍ່ສາມາດເປີດໄດ້ (ຕົວຢ່າງ: ໄຟລເສຍ, DRM) ການຮ້ອງຂໍລິຂະສິດ ບັນຫາການດາວໂຫລດ (ຕົວຢ່າງ: ບໍ່ສາມາດເຊື່ອມຕໍ່, ຂໍ້ຄວາມຜິດພາດ, ຊ້າຫຼາຍ) ຂໍ້ມູນຜິດພາດ (ຕົວຢ່າງ: ຊື່, ຄຳອະທິບາຍ, ຮູບປົກ) ອື່ນໆ ຄຸນນະພາບຕ່ຳ (ຕົວຢ່າງ: ບັນຫາການຈັດຮູບແບບ, ຄຸນນະພາບການສະແກນຕ່ຳ, ຫນ້າຫາຍ) ສະແປມ / ໄຟລຄວນຖືກລຶບ (ຕົວຢ່າງ: ການໂຄສະນາ, ເນື້ອຫາທີ່ບໍ່ຫຼາຍ) %(amount)s (%(amount_usd)s) %(amount)s ທັງໝົດ %(amount)s (%(amount_usd)s) ທັງໝົດ ໜັງສືດີເດັ່ນ ບິບຣາຣີແຫ່ງໂຊກດີ ນັກສະສົມຂໍ້ມູນສຸດສະຫຼັງ ນັກຈັດເກັບທີ່ສຸດຍອດ ການດາວໂຫລດໂບນັດ Cerlalc ຂໍ້ມູນເມຕາຂອງເຊັກ DuXiu 读秀 ດັດສະນີປື້ມອີເລັກຂອງ EBSCOhost Google Books Goodreads HathiTrust IA IA ການຢືມຢືມດິຈິຕອນທີ່ຄວບຄຸມ ISBNdb ISBN GRP Libgen.li ຍົກເວັ້ນ “scimag” Libgen.rs ບໍ່ເປັນນິຍາຍ ແລະ ນິຍາຍ Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary ຫໍສະໝຸດແຫ່ງຊາດລັດເຊຍ Sci-Hub ຜ່ານ Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor ອັບໂຫລດໄປຫາ AA Z-Library Z-Library ຈີນ ຊື່ເລື່ອງ, ຜູ້ແຕ່ງ, DOI, ISBN, MD5, … ຄົ້ນຫາ ນັກຂຽນ ຄຳອະທິບາຍແລະຄຳເຫັນເກື່ອງຂໍ້ມູນ ສະບັບ ຊື່ໄຟລ໌ຕົ້ນສະບັບ ຜູ້ພິມ (ຄົ້ນຫາສະເພາະສະຫນາມ) ຊື່ເລື່ອງ ປີທີ່ຕີພິມ ລາຍລະອຽດເທັກນິກ ເງິນສະກຸນນີ້ມີຂັ້ນຕ່ຳທີ່ສູງກວ່າປົກກະຕິ. ກະລຸນາເລືອກໄລຍະເວລາທີ່ແຕກຕ່າງຫຼືເງິນສະກຸນອື່ນ. ບໍ່ສາມາດດຳເນີນການຄຳຮ້ອງໄດ້. ກະລຸນາລອງໃໝ່ໃນອີກບໍ່ກີ່ນາທີ, ແລະຖ້າຍັງຄົງເກີດຂຶ້ນອີກ ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(email)s ພ້ອມກັບຮູບພາບຫນ້າຈໍ. ມີຂໍ້ຜິດພາດທີ່ບໍ່ຮູ້ຈັກ. ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(email)s ພ້ອມກັບຮູບພາບຫນ້າຈໍ. ມີຂໍ້ຜິດພາດໃນການດຳເນີນການຊຳລະເງິນ. ກະລຸນາລໍຖ້າຊົ່ວຄາວແລະລອງໃໝ່. ຖ້າບັນຫາຍັງຄົງຢູ່ເກີນ 24 ຊົ່ວໂມງ, ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(email)s ພ້ອມກັບຮູບພາບຫນ້າຈໍ. ພວກເຮົາກຳລັງຈັດການລະດົມທຶນເພື່ອ <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">ສຳຮອງ</a> ຫ້ອງສະໝຸດການກວດກາຄອມິກທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກ. ຂອບໃຈສຳລັບການສະໜັບສະໜູນຂອງທ່ານ! <a href="/donate">ບໍລິຈາກ.</a> ຖ້າທ່ານບໍ່ສາມາດບໍລິຈາກໄດ້, ກະລຸນາພິຈາລະນາສະໜັບສະໜູນເຮົາໂດຍການບອກເລົ່າໃຫ້ໝູ່ຂອງທ່ານຮູ້, ແລະຕິດຕາມເຮົາຢູ່ <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, ຫຼື <a href="https://t.me/annasarchiveorg">Telegram</a>. ຢ່າສົ່ງອີເມວຫາພວກເຮົາເພື່ອ <a %(a_request)s>ຮ້ອງຂໍປື້ມ</a><br>ຫຼືການອັບໂຫລດນ້ອຍ (<10k) <a %(a_upload)s>ອັບໂຫລດ</a>. ຫໍຈຳເອກສານຂອງ Anna ການຮ້ອງຮຽນ DMCA / ລິຂະສິດ ຕິດຕໍ່ພວກເຮົາ Reddit ທາງເລືອກ SLUM (%(unaffiliated)s) ບໍ່ສັນພັນ ຄັງເອກະສານຂອງອັນນາຕ້ອງການຄວາມຊ່ວຍເຫຼືອຂອງທ່ານ! ຖ້າທ່ານບໍລິຈາກດຽວນີ້, ທ່ານຈະໄດ້ຮັບ <strong>ສອງເທົ່າ</strong> ຂອງການດາວໂຫລດໄວ. ຫຼາຍຄົນພະຍາຍາມທຳລາຍພວກເຮົາ, ແຕ່ພວກເຮົາຕໍ່ສູ້ກັບຄືນ. ຖ້າທ່ານບໍລິຈາກໃນເດືອນນີ້, ທ່ານຈະໄດ້ຮັບ <strong>ຈຳນວນການດາວໂຫຼດໄວຂຶ້ນສອງເທົ່າ</strong>. ໃຊ້ໄດ້ຈົນສິ້ນເດືອນນີ້. ການຮັກສາຄວາມຮູ້ຂອງມະນຸດ: ຂອງຂວັນວັນພັກທີ່ຍິ່ງໃຫຍ່! ການເປັນສະມາຊິກຈະຖືກຂະຫຍາຍອອກຕາມຄວາມຈຳເປັນ. ເຊີບເວີພັນທະມິດບໍ່ສາມາດໃຊ້ງານໄດ້ເນື່ອງຈາກການປິດບໍລິການໂຮສຕິງ. ພວກເຂົາຄວນຈະກັບມາໃຊ້ງານໄດ້ໃນໄມ່ດົນນີ້. ເພື່ອເພີ່ມຄວາມທົນທານຂອງຄັງເອກະສານຂອງອັນນາ, ພວກເຮົາກຳລັງມອງຫາອາສາສະໝັກເພື່ອເຮັດການສະທ້ອນ. ພວກເຮົາມີວິທີການບໍລິຈາກໃໝ່ທີ່ພ້ອມໃຫ້ບໍລິການ: %(method_name)s. ກະລຸນາພິຈາລະນາ %(donate_link_open_tag)sບໍລິຈາກ</a> — ມັນບໍ່ແມ່ນຖືກເລີຍໃນການເດີນເວັບໄຊນີ້, ແລະການບໍລິຈາກຂອງທ່ານແມ່ນມີຄວາມສຳຄັນຫຼາຍ. ຂອບໃຈຫຼາຍໆ. ແນະນຳເພື່ອນ, ແລະທັງທ່ານແລະເພື່ອນຂອງທ່ານຈະໄດ້ຮັບ %(percentage)s%% ໂບນັດດາວໂຫລດໄວ! ປະຫັນປະເຫີດຄົນທີ່ທ່ານຮັກ, ໃຫ້ພວກເຂົາມີບັນຊີທີ່ມີສະມາຊິກ. ຂອງຂວັນວັນວາເລນໄທນທີ່ສົມບູນແບບ! ຮຽນຮູ້ເພີ່ມເຕີມ… ບັນຊີ ກິດຈະກໍາ ຂັ້ນສູງ ບລັອກຂອງ Anna ↗ ຊອບແວຂອງ Anna ↗ beta ຄົ້ນຫາລະຫັດ Datasets ບໍລິຈາກ ໄຟລ໌ທີ່ດາວໂຫລດແລ້ວ FAQ ໜ້າຫຼັກ ປັບປຸງຂໍ້ມູນເພີ່ມເຕີມ ຂໍ້ມູນ LLM ເຂົ້າລະບົບ / ລົງທະບຽນ ການບໍລິຈາກຂອງຂ້ອຍ ໂປຣໄຟລສາທາລະນະ ຄົ້ນຫາ ຄວາມປອດໄພ Torrents ແປພາສາ ↗ ອາສາສະໝັກ & ລາງວັນ ການດາວໂຫລດລ່າສຸດ: 📚&nbsp;ຫ້ອງສະໝຸດທີ່ເປີດທີ່ຍິ່ງໃຫຍ່ທີ່ສຸດໃນໂລກ. ⭐️&nbsp;ສະທ້ອນ Sci-Hub, Library Genesis, Z-Library, ແລະອື່ນໆອີກ. 📈&nbsp;%(book_any)s ປື້ມ, %(journal_article)s ເອກະສານ, %(book_comic)s ການຕູນ, %(magazine)s ວາລະສານ — ຖືກສະຫງວນໄວ້ຕະຫຼອດໄປ.  ແລະ  ແລະອື່ນໆອີກ DuXiu ຫ້ອງສະໝຸດອິນເຕີເນັດ LibGen 📚&nbsp;ຫ້ອງສະໝຸດທີ່ເປີດທີ່ຍິ່ງໃຫຍ່ທີ່ສຸດໃນປະຫວັດສາດຂອງມະນຸດ. 📈&nbsp;%(book_count)s&nbsp;ປື້ມ, %(paper_count)s&nbsp;ເອກະສານ — ຖືກສະຫງວນໄວ້ຕະຫຼອດໄປ. ⭐️&nbsp;ພວກເຮົາສະທ້ອນ %(libraries)s. ພວກເຮົາຂູດຂໍ້ມູນແລະເປີດແຫຼ່ງທີ່ມາ %(scraped)s. ລະຫັດແລະຂໍ້ມູນທັງໝົດຂອງພວກເຮົາເປັນແຫຼ່ງທີ່ມາເປີດທັງໝົດ. OpenLib Sci-Hub ,  📚 ຫ້ອງສະໝຸດທີ່ເປີດທີ່ຍິ່ງໃຫຍ່ທີ່ສຸດໃນໂລກ.<br>⭐️ ສະທ້ອນ Scihub, Libgen, Zlib, ແລະອື່ນໆອີກ. Z-Lib ຄັງເກັບຂອງ Anna ຄຳຮ້ອງຂໍບໍ່ຖືກຕ້ອງ. ເຂົ້າຊົມ %(websites)s. ຄັງເອກະສານເປີດທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກ. ສະທ້ອນ Sci-Hub, Library Genesis, Z-Library, ແລະອື່ນໆ. ຄົ້ນຫາຄັງເອກະສານຂອງອັນນາ ຄັງເອກະສານຂອງອັນນາ ກະລຸນາຟື້ນຟູໃໝ່ເພື່ອລອງອີກຄັ້ງ. <a %(a_contact)s>ຕິດຕໍ່ພວກເຮົາ</a> ຖ້າບັນຫາຍັງຄົງຢູ່ຫຼາຍຊົ່ວໂມງ. 🔥 ມີບັນຫາໃນການໂຫລດໜ້ານີ້ <li>1. ຕິດຕາມພວກເຮົາທີ່ <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, ຫຼື <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. ແຜ່ກະຈາຍຂ່າວກ່ຽວກັບ Anna’s Archive ທີ່ Twitter, Reddit, Tiktok, Instagram, ທີ່ຮ້ານກາເຟທ້ອງຖິ່ນຂອງທ່ານ ຫຼືຫ້ອງສະໝຸດ, ຫຼືບ່ອນໃດກໍໄດ້! ພວກເຮົາບໍ່ເຊື່ອໃນການປິດກັ້ນ — ຖ້າພວກເຮົາຖືກປິດລົງ, ພວກເຮົາຈະກັບມາອີກທີ່ບ່ອນອື່ນ, ເພາະລະຫັດແລະຂໍ້ມູນທັງໝົດຂອງພວກເຮົາເປັນແຫຼ່ງທີ່ເປີດກວ້າງທັງໝົດ.</li><li>3. ຖ້າທ່ານສາມາດ, ພິຈາລະນາ <a href="/donate">ບໍລິຈາກ</a>.</li><li>4. ຊ່ວຍ <a href="https://translate.annas-software.org/">ແປພາສາ</a> ເວັບໄຊຂອງພວກເຮົາເປັນພາສາຕ່າງໆ.</li><li>5. ຖ້າທ່ານເປັນນັກວິຊາການຊອບແວ, ພິຈາລະນາມີສ່ວນຮ່ວມກັບພວກເຮົາໃນ <a href="https://annas-software.org/">ແຫຼ່ງທີ່ເປີດກວ້າງ</a>, ຫຼືການສົ່ງຕໍ່ <a href="/datasets">torrents</a> ຂອງພວກເຮົາ.</li> 10. ສ້າງຫຼືຊ່ວຍດູແລໜ້າ Wikipedia ສຳລັບ Anna’s Archive ໃນພາສາຂອງທ່ານ. 11. ພວກເຮົາກຳລັງມອງຫາການວາງໂຄສະນານ້ອຍໆ ທີ່ມີຄວາມສວຍງາມ. ຖ້າທ່ານຕ້ອງການໂຄສະນາໃນ Anna’s Archive, ກະລຸນາແຈ້ງໃຫ້ພວກເຮົາຮູ້. 6. ຖ້າທ່ານເປັນນັກວິຈັຍຄວາມປອດໄພ, ພວກເຮົາສາມາດໃຊ້ທັກສະຂອງທ່ານໄດ້ທັງການໂຈມຕີແລະການປ້ອງກັນ. ກວດເບິ່ງໜ້າ <a %(a_security)s>Security</a> ຂອງພວກເຮົາ. 7. ພວກເຮົາກຳລັງມອງຫາຜູ້ຊຳນານການໃນການຈ່າຍເງິນສຳລັບພໍ່ຄ້າທີ່ບໍ່ປະຈຳຕົວ. ທ່ານສາມາດຊ່ວຍພວກເຮົາເພີ່ມວິທີການບໍລິຈາກທີ່ສະດວກຫຼາຍຂຶ້ນໄດ້ບໍ? PayPal, WeChat, ບັດຂອງຂວັນ. ຖ້າທ່ານຮູ້ຈັກໃຜ, ກະລຸນາຕິດຕໍ່ພວກເຮົາ. 8. ພວກເຮົາກຳລັງມອງຫາຄວາມສາມາດໃນການເພີ່ມປະສິດທິພາບເຊີເວີຂອງພວກເຮົາເພີ່ມຂຶ້ນ. 9. ທ່ານສາມາດຊ່ວຍໂດຍການລາຍງານບັນຫາໄຟລ໌, ປະກອບຄຳເຫັນ, ແລະສ້າງລາຍຊື່ໃນເວັບໄຊນີ້ໂດຍກົງ. ທ່ານຍັງສາມາດຊ່ວຍໂດຍ <a %(a_upload)s>ອັບໂຫລດປື້ມເພີ່ມເຕີມ</a>, ຫຼືແກ້ໄຂບັນຫາໄຟລ໌ຫຼືການຈັດຮູບແບບຂອງປື້ມທີ່ມີຢູ່ແລ້ວ. ສຳລັບຂໍ້ມູນທີ່ລະອຽດກ່ຽວກັບການອາສາສະໝັກ, ເບິ່ງໜ້າ <a %(a_volunteering)s>ການອາສາສະໝັກ & ລາງວັນ</a> ຂອງພວກເຮົາ. ພວກເຮົາເຊື່ອຢ່າງແຮງກ່ຽວກັບການໄຫຼຂອງຂໍ້ມູນທີ່ເປີດເສຣີ, ແລະການອະນຸຮັກຄວາມຮູ້ແລະວັດທະນະທຳ. ດ້ວຍເຄື່ອງມືຄົ້ນຫານີ້, ພວກເຮົາສ້າງຂຶ້ນບົນບ່າຂອງຍັກຩ໌. ພວກເຮົາເຄົາລົບຢ່າງລຶກຊຶ້ງຕໍ່ການເຮັດວຽກຢ່າງຫນັກຂອງຜູ້ທີ່ໄດ້ສ້າງຫ້ອງສະໝຸດເງົາຕ່າງໆ, ແລະພວກເຮົາຫວັງວ່າເຄື່ອງມືຄົ້ນຫານີ້ຈະຂະຫຍາຍການເຂົ້າເຖິງຂອງພວກເຂົາ. ເພື່ອຕິດຕາມຄວາມຄືບໜ້າຂອງພວກເຮົາ, ຕິດຕາມ Anna ທີ່ <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ຫຼື <a href="https://t.me/annasarchiveorg">Telegram</a>. ສຳລັບຄຳຖາມແລະຄຳຕິຊົມກະລຸນາຕິດຕໍ່ Anna ທີ່ %(email)s. ລະຫັດບັນຊີ: %(account_id)s ອອກຈາກລະບົບ ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ໃໝ່ແລະລອງອີກຄັ້ງ. ✅ ທ່ານໄດ້ອອກຈາກລະບົບແລ້ວ. ໂຫຼດໜ້ານີ້ຄືນເພື່ອເຂົ້າລະບົບອີກຄັ້ງ. ການດາວໂຫລດໄວທີ່ໃຊ້ (24 ຊົ່ວໂມງຜ່ານມາ): <strong>%(used)s / %(total)s</strong> ສະມາຊິກ: <strong>%(tier_name)s</strong> ຫາວັນທີ %(until_date)s <a %(a_extend)s>(ຂະຫຍາຍ)</a> ທ່ານສາມາດລວມສະມາຊິກຫຼາຍອັນເຂົ້າດ້ວຍກັນ (ການດາວໂຫລດໄວຕໍ່ 24 ຊົ່ວໂມງຈະຖືກລວມເຂົ້າກັນ). ສະມາຊິກ: <strong>ບໍ່ມີ</strong> <a %(a_become)s>(ກາຍເປັນສະມາຊິກ)</a> ຕິດຕໍ່ Anna ທີ່ %(email)s ຖ້າທ່ານສົນໃຈທີ່ຈະອັບເກຣດສະມາຊິກຂອງທ່ານໄປທີ່ຂັ້ນທີ່ສູງກວ່າ. ໂປຣໄຟລ໌ສາທາລະນະ: %(profile_link)s ລະຫັດລັບ (ຢ່າແບ່ງປັນ!): %(secret_key)s ສະແດງ ເຂົ້າຮ່ວມກັບພວກເຮົາທີ່ນີ້! ອັບເກຣດໄປທີ່ <a %(a_tier)s>ຂັ້ນທີ່ສູງກວ່າ</a> ເພື່ອເຂົ້າຮ່ວມກຸ່ມຂອງພວກເຮົາ. ກຸ່ມ Telegram ພິເສດ: %(link)s ບັນຊີ ການດາວໂຫລດໃດ? ເຂົ້າລະບົບ ຢ່າເສຍລະຫັດຂອງທ່ານ! ລະຫັດລັບບໍ່ຖືກຕ້ອງ. ກວດສອບລະຫັດຂອງທ່ານແລະລອງອີກຄັ້ງ, ຫຼືລົງທະບຽນບັນຊີໃໝ່ດ້ານລຸ່ມນີ້. ກະແຈລັບ ປ້ອນກະແຈລັບຂອງທ່ານເພື່ອເຂົ້າລະບົບ: ບັນຊີເກົ່າທີ່ໃຊ້ອີເມວ? ປ້ອນ <a %(a_open)s>ອີເມວຂອງທ່ານທີ່ນີ້</a>. ລົງທະບຽນບັນຊີໃໝ່ ຍັງບໍ່ມີບັນຊີບໍ? ການລົງທະບຽນສຳເລັດ! ກະລຸນາຮັກສາກະແຈລັບຂອງທ່ານ: <span %(span_key)s>%(key)s</span> ກະລຸນາຮັກສາກະແຈນີ້ໄວ້ຢ່າງລະມັດລະວັງ. ຖ້າທ່ານສູນເສຍມັນ, ທ່ານຈະສູນເສຍການເຂົ້າເຖິງບັນຊີຂອງທ່ານ. <li %(li_item)s><strong>ບຸກມາກ.</strong> ທ່ານສາມາດບຸກມາກໜ້ານີ້ເພື່ອກູ້ຄືນກະແຈຂອງທ່ານ.</li><li %(li_item)s><strong>ດາວໂຫລດ.</strong> ຄລິກ <a %(a_download)s>ລິ້ງນີ້</a> ເພື່ອດາວໂຫລດກະແຈຂອງທ່ານ.</li><li %(li_item)s><strong>ຜູ້ຈັດການລະຫັດຜ່ານ.</strong> ໃຊ້ຜູ້ຈັດການລະຫັດຜ່ານເພື່ອບັນທຶກກະແຈເມື່ອທ່ານປ້ອນມັນຂ້າງລຸ່ມນີ້.</li> ເຂົ້າລະບົບ / ລົງທະບຽນ ການກວດສອບເບຣາວເຊີ ຄຳເຕືອນ: ລະຫັດມີຕົວອັກສອນ Unicode ທີ່ບໍ່ຖືກຕ້ອງ, ແລະອາດຈະປະພຶດຜິດພາດໃນສະຖານະການຕ່າງໆ. ຂໍ້ມູນດິບສາມາດຖືກຖອດລະຫັດຈາກການປະກອບ base64 ໃນ URL. ຄຳອະທິບາຍ ປ້າຍກຳກັບ ຄຳນຳໜ້າ URL ສຳລັບລະຫັດທີ່ສະເພາະ ເວັບໄຊທ໌ ລະຫັດທີ່ເລີ່ມຕົ້ນດ້ວຍ “%(prefix_label)s” ກະລຸນາຢ່າຂູດໜ້າເຫຼົ່ານີ້. ແທນທີ່ຈະເຮັດແບບນັ້ນ, ພວກເຮົາແນະນຳ <a %(a_import)s>ການສ້າງ</a> ຫຼື <a %(a_download)s>ການດາວໂຫລດ</a> ຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB ຂອງພວກເຮົາ, ແລະເຮັດວຽກກັບ <a %(a_software)s>ໂຄດເປີດແຫຼ່ງຂອງພວກເຮົາ</a>. ຂໍ້ມູນດິບສາມາດຖືກສຳຫຼວດແບບມືໂດຍຜ່ານໄຟລ໌ JSON ເຊັ່ນ <a %(a_json_file)s>ໄຟລ໌ນີ້</a>. ບັນທຶກຫຼາຍກວ່າ %(count)s URL ທົ່ວໄປ ຜູ້ສຳຫຼວດລະຫັດ ສຳຫຼວດລະຫັດທີ່ບັນທຶກຖືກໝາຍດ້ວຍ, ຕາມຄຳນຳໜ້າ. ຄໍລຳ “ບັນທຶກ” ສະແດງຈຳນວນບັນທຶກທີ່ຖືກໝາຍດ້ວຍລະຫັດທີ່ມີຄຳນຳໜ້າທີ່ກຳນົດ, ດັ່ງທີ່ເຫັນໃນເຄື່ອງຄົ້ນຫາ (ລວມທັງບັນທຶກຂໍ້ມູນເທົ່ານັ້ນ). ຄໍລຳ “ລະຫັດ” ສະແດງຈຳນວນລະຫັດທີ່ແທ້ຈິງທີ່ມີຄຳນຳໜ້າທີ່ກຳນົດ. ຄຳນຳໜ້າລະຫັດທີ່ຮູ້ຈັກ “%(key)s” ຄຳນຳໜ້າ %(count)s ບັນທຶກທີ່ກົງກັບ “%(prefix_label)s” ລະຫັດ ບັນທຶກ “%%s” ຈະຖືກແທນທີ່ດ້ວຍຄ່າຂອງລະຫັດ ຄົ້ນຫາ ຄັງເກັບຂໍ້ມູນຂອງ Anna ລະຫັດ URL ສຳລັບລະຫັດພິເສດ: “%(url)s” ໜ້ານີ້ອາດຈະໃຊ້ເວລາພັກໜຶ່ງໃນການສ້າງ, ດັ່ງນັ້ນຈຶ່ງຕ້ອງການ Cloudflare captcha. <a %(a_donate)s>ສະມາຊິກ</a> ສາມາດຂ້າມ captcha ໄດ້. ການລຸກລ້າງຖືກລາຍງານແລ້ວ: ສະບັບທີ່ດີກວ່າ ທ່ານຕ້ອງການລາຍງານຜູ້ໃຊ້ນີ້ສໍາລັບພຶດຕິກໍາທີ່ບໍ່ສົມຄວນຫຼືບໍ່ຫມາຍຄວາມຫຼືບໍ່? ບັນຫາໄຟລ໌: %(file_issue)s hidden comment ຕອບກັບ ລາຍງານການລຸກລ້າງ ທ່ານໄດ້ລາຍງານຜູ້ໃຊ້ນີ້ສໍາລັບການລຸກລ້າງ. ການຮ້ອງຮຽນລິຂະສິດມາທີ່ອີເມວນີ້ຈະຖືກມອບໃຫ້ມອບໃຊ້; ໃຊ້ແບບຟອມແທນ. ສະແດງອີເມວ ພວກເຮົາຍິນດີຕ້ອນຮັບຄຳຄິດເຫັນແລະຄຳຖາມຂອງທ່ານ! ແຕ່ຢ່າງໃດກໍຕາມ, ເນື່ອງຈາກຈຳນວນສະແປມແລະອີເມວທີ່ບໍ່ມີເນື້ອຫາທີ່ພວກເຮົາໄດ້ຮັບ, ກະລຸນາກວດກ່ອງເພື່ອຢືນຢັນວ່າທ່ານເຂົ້າໃຈເງື່ອນໄຂເຫຼົ່ານີ້ໃນການຕິດຕໍ່ພວກເຮົາ. ວິທີການອື່ນໃດໆໃນການຕິດຕໍ່ພວກເຮົາກ່ຽວກັບການຮ້ອງຮຽນລິຂະສິດຈະຖືກລຶບອັດຕະໂນມັດ. ສຳລັບການຮ້ອງຮຽນລິຂະສິດ / DMCA, ໃຊ້ <a %(a_copyright)s>ແບບຟອມນີ້</a>. ອີເມວຕິດຕໍ່ URLs ຢູ່ໃນຄັງເກັບຂໍ້ມູນຂອງ Anna (ຈຳເປັນ). ໜຶ່ງຕໍ່ແຖວ. ກະລຸນາລວມເອົາແຕ່ URLs ທີ່ອະທິບາຍສຳນວນດຽວກັນຂອງປື້ມ. ຖ້າທ່ານຕ້ອງການຮ້ອງຮຽນສຳລັບປື້ມຫຼາຍເລື່ອງຫຼືສຳນວນຫຼາຍສຳນວນ, ກະລຸນາສົ່ງແບບຟອມນີ້ຫຼາຍເທື່ອ. ການຮ້ອງຂໍທີ່ລວມຫຼາຍໆປື້ມຫຼືສະບັບຕ່າງໆເຂົ້າກັນຈະຖືກປະຕິເສດ. ທີ່ຢູ່ (ຈຳເປັນ) ຄຳອະທິບາຍທີ່ຊັດເຈນຂອງວັດຖຸດິບ (ຈຳເປັນ) ອີເມວ (ຈຳເປັນ) URLs ຂອງວັດຖຸດິບ, ໃຫ້ລະບຸແຕ່ລະເສັ້ນ (ຈຳເປັນ). ກະລຸນາລະບຸໃຫ້ຫຼາຍທີ່ສຸດເທົ່າທີ່ຈະເປັນໄປໄດ້, ເພື່ອຊ່ວຍໃຫ້ພວກເຮົາກວດສອບການຮ້ອງຂໍຂອງທ່ານ (ເຊັ່ນ Amazon, WorldCat, Google Books, DOI). ISBN ຂອງວັດຖຸດິບ (ຖ້າມີ). ໃຫ້ລະບຸແຕ່ລະເສັ້ນ. ກະລຸນາລະບຸເພີ່ມເຫຼົ່ານັ້ນທີ່ຕົງກັບສະບັບທີ່ທ່ານກຳລັງລາຍງານການຮ້ອງຂໍລິຂະສິດ. ຊື່ຂອງທ່ານ (ຈຳເປັນ) ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາຣີໂຫລດໜ້ານີ້ແລະລອງອີກຄັ້ງ. ✅ ຂອບໃຈທີ່ສົ່ງການຮ້ອງຂໍລິຂະສິດຂອງທ່ານ. ພວກເຮົາຈະກວດສອບມັນໃນເວລາທີ່ໄວທີ່ສຸດ. ກະລຸນາຣີໂຫລດໜ້ານີ້ເພື່ອສົ່ງອີກຄັ້ງ. <a %(a_openlib)s>Open Library</a> URLs ຂອງວັດຖຸດິບ, ໃຫ້ລະບຸແຕ່ລະເສັ້ນ. ກະລຸນາໃຊ້ເວລາຄົ້ນຫາ Open Library ສຳລັບວັດຖຸດິບຂອງທ່ານ. ນີ້ຈະຊ່ວຍໃຫ້ພວກເຮົາກວດສອບການຮ້ອງຂໍຂອງທ່ານ. ເບີໂທລະສັບ (ຈຳເປັນ) ຄຳແຖງການແລະລາຍເຊັນ (ຈຳເປັນ) ສົ່ງການຮ້ອງຂໍ ຖ້າທ່ານມີການຮ້ອງຮຽນກ່ຽວກັບການລະເມີດລິຂະສິດ DMCA ຫຼືການຮ້ອງຮຽນອື່ນໆ, ກະລຸນາກອກແບບຟອມນີ້ໃຫ້ຖືກຕ້ອງທີ່ສຸດ. ຖ້າທ່ານພົບປັນຫາໃດໆ, ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ທີ່ຢູ່ DMCA ທີ່ສະເພາະ: %(email)s. ຂໍເຫັນໃຈວ່າການຮ້ອງຮຽນທີ່ສົ່ງທາງອີເມວມາທີ່ທີ່ຢູ່ນີ້ຈະບໍ່ໄດ້ຮັບການດຳເນີນການ, ມັນເປັນເພື່ອຄຳຖາມເທົ່ານັ້ນ. ກະລຸນາໃຊ້ແບບຟອມຂ້າງລຸ່ມນີ້ເພື່ອສົ່ງການຮ້ອງຮຽນຂອງທ່ານ. ແບບຟອມຮ້ອງຮຽນ DMCA / ລິຂະສິດ ຕົວຢ່າງບັນທຶກໃນ ຄັງເກັບຂໍ້ມູນຂອງ Anna Torrents ໂດຍ Anna’s Archive ຮູບແບບຄັງເກັບຂໍ້ມູນຂອງ Anna ສະບັບສຳລັບນຳເຂົ້າຂໍ້ມູນເມຕາດາຕ້າ ຖ້າທ່ານສົນໃຈໃນການສຳຮອງຊຸດຂໍ້ມູນນີ້ສຳລັບ <a %(a_archival)s>ການຈັດເກັບ</a> ຫຼື <a %(a_llm)s>ການຝຶກອົບຮົມ LLM</a> ກະລຸນາຕິດຕໍ່ພວກເຮົາ. ອັບເດດຄັ້ງສຸດທ້າຍ: %(date)s ເວັບໄຊທ໌ຫຼັກ %(source)s ເອກະສານຂໍ້ມູນເມຕາດາຕ້າ (ສ່ວນໃຫຍ່ຂອງຂໍ້ມູນ) ໄຟລ໌ທີ່ຖືກສຳເນົາໂດຍ Anna’s Archive: %(count)s (%(percent)s%%) ຊັບພະຍາກອນ ຈຳນວນໄຟລ໌ທັງໝົດ: %(count)s ຂະໜາດໄຟລ໌ທັງໝົດ: %(size)s ບລັອກໂພສຂອງພວກເຮົາກ່ຽວກັບຂໍ້ມູນນີ້ <a %(duxiu_link)s>Duxiu</a> ແມ່ນຖານຂໍ້ມູນຂະຫນາດໃຫຍ່ຂອງປື້ມສະແກນ, ສ້າງໂດຍ <a %(superstar_link)s>ກຸ່ມຫ້ອງສະໝຸດດິຈິຕອນ SuperStar</a>. ສ່ວນໃຫຍ່ແມ່ນປື້ມວິຊາການ, ທີ່ຖືກສະແກນເພື່ອໃຫ້ມີພາບດິຈິຕອນໃຫ້ແກ່ມະຫາວິທະຍາໄລແລະຫ້ອງສະໝຸດ. ສຳລັບຜູ້ຟັງທີ່ເວົ້າພາສາອັງກິດ, <a %(princeton_link)s>Princeton</a> ແລະ <a %(uw_link)s>ມະຫາວິທະຍາໄລ Washington</a> ມີການທຳຄວາມເຂົ້າໃຈທີ່ດີ. ຍັງມີບົດຄວາມທີ່ຍອດເຢັ່ຽທີ່ໃຫ້ຂໍ້ມູນພື້ນຖານເພີ່ມເຕີມ: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. ປື້ມຈາກ Duxiu ໄດ້ຖືກລັກລອບມາດົນແລ້ວໃນອິນເຕີເນັດຈີນ. ປົກກະຕິແລ້ວພວກມັນຖືກຂາຍໃນລາຄາຕໍ່າກວ່າໜຶ່ງໂດລາໂດຍຜູ້ຂາຍປີກກາ. ພວກມັນມັກຈະຖືກແຈກຈ່າຍໂດຍໃຊ້ສິ່ງທີ່ຄຽງຄຽງກັບ Google Drive ຂອງຈີນ, ທີ່ມັກຈະຖືກແຮກເພື່ອໃຫ້ມີພື້ນທີ່ເກັບຂໍ້ມູນຫຼາຍຂຶ້ນ. ລາຍລະອຽດທາງເທັກນິກບາງສ່ວນສາມາດພົບໄດ້ <a %(link1)s>ທີ່ນີ້</a> ແລະ <a %(link2)s>ທີ່ນີ້</a>. ແມ່ນວ່າປື້ມໄດ້ຖືກແຈກຈ່າຍແບບກຶງສາທາລະນະ, ມັນຍາກຫຼາຍທີ່ຈະໄດ້ມາເປັນຈຳນວນຫຼາຍ. ພວກເຮົາໄດ້ມີສິ່ງນີ້ໃນລາຍການທີ່ຕ້ອງເຮັດສູງສຸດ, ແລະໄດ້ຈັດສັນເວລາຫຼາຍເດືອນຂອງການເຮັດວຽກເຕັມເວລາສຳລັບມັນ. ແຕ່ໃນປາຍປີ 2023 ມີອາສາສະໝັກທີ່ຫນ້າທຶງ, ສຸດຍອດ, ແລະມີຄວາມສາມາດໄດ້ຕິດຕໍ່ພວກເຮົາ, ບອກພວກເຮົາວ່າພວກເຂົາໄດ້ເຮັດວຽກນີ້ແລ້ວ — ໃນຄ່າໃຊ້ຈ່າຍທີ່ສູງ. ພວກເຂົາໄດ້ແບ່ງປັນຄອລເລັກຊັນເຕັມໃຫ້ພວກເຮົາ, ໂດຍບໍ່ຄາດຫວັງສິ່ງໃດເລີຍ, ຍົກເວັ້ນການຮັບປະກັນການສະແຫວງຮັກສາລະຍະຍາວ. ນ່າທຶງຫຼາຍ. ຂໍ້ມູນເພີ່ມເຕີມຈາກອາສາສະໝັກຂອງພວກເຮົາ (ບັນທຶກດິບ): ດັດແປງຈາກ <a %(a_href)s>ບລັອກໂພສ</a> ຂອງພວກເຮົາ. DuXiu 读秀 %(count)s ໄຟລ໌ ຊຸດຂໍ້ມູນນີ້ມີຄວາມສຳພັນໃກ້ຊິດກັບ <a %(a_datasets_openlib)s>ຊຸດຂໍ້ມູນ Open Library</a>. ມັນປະກອບດ້ວຍການຂູດຂໍ້ມູນທັງໝົດຂອງເມຕາດາຕ້າແລະສ່ວນໃຫຍ່ຂອງໄຟລ໌ຈາກຫ້ອງສະໝຸດດິຈິຕອນທີ່ຄວບຄຸມໂດຍ IA. ການອັບເດດຈະຖືກປ່ອຍໃນ <a %(a_aac)s>ຮູບແບບຄັງເກັບຂໍ້ມູນຂອງ Anna</a>. ບັນທຶກເຫຼົ່ານີ້ຖືກອ້າງອີງໂດຍກົງຈາກຊຸດຂໍ້ມູນ Open Library, ແຕ່ຍັງມີບັນທຶກທີ່ບໍ່ຢູ່ໃນ Open Library ດ້ວຍ. ພວກເຮົາຍັງມີໄຟລ໌ຂໍ້ມູນຈຳນວນຫຼາຍທີ່ຖືກຂູດໂດຍສະມາຊິກຊຸມຊົນຕະຫຼອດຫຼາຍປີທີ່ຜ່ານມາ. ການລວມລວມປະກອບດ້ວຍສອງສ່ວນ. ທ່ານຕ້ອງການທັງສອງສ່ວນເພື່ອໄດ້ຮັບຂໍ້ມູນທັງໝົດ (ຍົກເວັ້ນທໍເຣນທີ່ຖືກແທນທີ່, ທີ່ຖືກຂີດອອກໃນໜ້າທໍເຣນ). ຫ້ອງສະໝຸດອອນໄລນ໌ດິຈິຕອນ ການປະຕິບັດງານຄັ້ງທຳອິດຂອງພວກເຮົາ, ກ່ອນທີ່ພວກເຮົາຈະມາຕະຖານຢູ່ໃນຮູບແບບ <a %(a_aac)s>ຮູບແບບກັບ Anna’s Archive Containers (AAC)</a>. ປະກອບດ້ວຍ metadata (ເປັນ json ແລະ xml), pdfs (ຈາກລະບົບການຢືມດິຈິຕອນ acsm ແລະ lcpdf), ແລະຮູບພາບປົກ. ການປະຕິບັດງານໃໝ່ເພີ່ມເຕີມ, ໃຊ້ AAC. ປະກອບດ້ວຍ metadata ທີ່ມີເວລາປັບປຸງຫຼັງຈາກ 2023-01-01, ເພາະສ່ວນທີ່ເຫຼືອໄດ້ຖືກຄຸ້ມຄອງແລ້ວໂດຍ “ia”. ຍັງມີໄຟລ໌ pdf ທັງໝົດ, ເທື່ອນີ້ຈາກລະບົບການຢືມ acsm ແລະ “bookreader” (ຕົວອ່ານເວັບ IA). ເຖິງແມ່ນວ່າຊື່ຈະບໍ່ຖືກຕ້ອງແທ້ໆ, ພວກເຮົາຍັງຄົງເພີ່ມໄຟລ໌ bookreader ເຂົ້າໃນຄັງ ia2_acsmpdf_files, ເພາະພວກມັນບໍ່ສາມາດທຳວຽກຮ່ວມກັນໄດ້. IA Controlled Digital Lending 98%%+ ຂອງໄຟລ໌ສາມາດຄົ້ນຫາໄດ້. ພັນທະກິດຂອງພວກເຮົາແມ່ນການຈັດເກັບປຶ້ມທັງໝົດໃນໂລກ (ຮວມທັງເອກະສານ, ວາລະສານ, ແລະອື່ນໆ), ແລະເຮັດໃຫ້ມັນສາມາດເຂົ້າເຖິງໄດ້ຢ່າງກວ້າງຂວາງ. ພວກເຮົາເຊື່ອວ່າປຶ້ມທັງໝົດຄວນຖືກສຳເນົາໃນຫຼາຍສະຖານທີ່, ເພື່ອປ້ອງກັນການສູນເສຍແລະຄວາມທົນທານ. ນີ້ແມ່ນເຫດຜົນທີ່ພວກເຮົາກຳລັງຮວບຮວມໄຟລ໌ຈາກແຫຼ່ງຕ່າງໆ. ແຫຼ່ງບາງແຫຼ່ງແມ່ນເປີດເສັ້ນແລະສາມາດຖືກສຳເນົາໄດ້ເປັນຈຳນວນຫຼາຍ (ເຊັ່ນ Sci-Hub). ແຫຼ່ງອື່ນແມ່ນປິດແລະປົກປ້ອງ, ດັ່ງນັ້ນພວກເຮົາພະຍາຍາມຂູດຂໍ້ມູນເພື່ອ “ປົດປ່ອຍ” ປຶ້ມຂອງພວກເຂົາ. ອື່ນໆກໍຕົກຢູ່ລະຫວ່າງກາງ. ຂໍ້ມູນທັງໝົດຂອງພວກເຮົາສາມາດ <a %(a_torrents)s>ຖືກດາວໂຫຼດຜ່ານ torrent</a>, ແລະຂໍ້ມູນ metadata ທັງໝົດຂອງພວກເຮົາສາມາດ <a %(a_anna_software)s>ຖືກສ້າງ</a> ຫຼື <a %(a_elasticsearch)s>ດາວໂຫຼດ</a> ເປັນຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB. ຂໍ້ມູນດິບສາມາດຖືກສຳຫຼວດແບບດ້ວຍຕົວເອງຜ່ານໄຟລ໌ JSON ເຊັ່ນ <a %(a_dbrecord)s>ນີ້</a>. ຂໍ້ມູນ Metadata ເວັບໄຊທ໌ ISBN ອັບເດດລ່າສຸດ: %(isbn_country_date)s (%(link)s) ຊັບພະຍາກອນ ສຳນັກງານ ISBN ສາກົນໄດ້ປ່ອຍຂໍ້ມູນຊ່ວງເວລາທີ່ມັນໄດ້ຈັດສັນໃຫ້ກັບສຳນັກງານ ISBN ປະເທດຕ່າງໆ. ຈາກນີ້ພວກເຮົາສາມາດລະບຸໄດ້ວ່າ ISBN ນີ້ສັງກັດຢູ່ປະເທດ, ພາກພື້ນ, ຫຼືກຸ່ມພາສາໃດ. ປັດຈຸບັນພວກເຮົາໃຊ້ຂໍ້ມູນນີ້ທາງອ້ອມ, ຜ່ານຫ້ອງສະໝຸດ Python <a %(a_isbnlib)s>isbnlib</a>. ຂໍ້ມູນປະເທດ ISBN ນີ້ແມ່ນການດຳເນີນການດຶງຂໍ້ມູນຈາກ isbndb.com ໃນເດືອນກັນຍາ 2022. ພວກເຮົາພະຍາຍາມຄົບຄຸມທຸກຊ່ວງເວລາ ISBN. ມີບັນທຶກປະມານ 30.9 ລ້ານລາຍການ. ບ່ອນເວັບໄຊທ໌ຂອງພວກເຂົາອ້າງວ່າພວກເຂົາມີບັນທຶກຈຳນວນ 32.6 ລ້ານ, ດັ່ງນັ້ນພວກເຮົາອາດຈະພາດບາງສ່ວນ, ຫຼື <em>ພວກເຂົາ</em> ອາດຈະທຳຜິດພາດບາງຢ່າງ. ການຕອບກັບ JSON ແມ່ນຄ່ອນຂ້າງດິບຈາກເຊີເວີຂອງພວກເຂົາ. ປັນຫາຄຸນນະພາບຂໍ້ມູນທີ່ພວກເຮົາສັງເກດເຫັນແມ່ນ, ສຳລັບເລກ ISBN-13 ທີ່ເລີ່ມຕົ້ນດ້ວຍຄຳນຳຫນ້າທີ່ແຕກຕ່າງຈາກ “978-”, ພວກເຂົາຍັງລວມເຂົ້າກັບຊ່ອງ “isbn” ທີ່ເປັນເລກ ISBN-13 ທີ່ຕັດຕົວເລກ 3 ຕົວແລະຄິດໄລ່ເລກກວດສອບໃໝ່. ນີ້ແມ່ນຜິດພາດຢ່າງແຈ້ງຈະແຈງ, ແຕ່ນີ້ແມ່ນວິທີທີ່ພວກເຂົາເຮັດ, ດັ່ງນັ້ນພວກເຮົາບໍ່ໄດ້ປ່ຽນແປງມັນ. ອີກປັນຫາທີ່ທ່ານອາດຈະເຈີຜົນແມ່ນ, ຄວາມຈິງທີ່ຊ່ອງ “isbn13” ມີການຊ້ຳກັນ, ດັ່ງນັ້ນທ່ານບໍ່ສາມາດໃຊ້ມັນເປັນກຸນແຈສຳຄັນໃນຖານຂໍ້ມູນໄດ້. ຊ່ອງ “isbn13”+“isbn” ທີ່ລວມກັນດູເຫັນວ່າເປັນເອກະລັກ. ປ່ອຍຄັ້ງທີ 1 (2022-10-31) ນິຍາຍ torrents ຢູ່ຫຼັງ (ແມ່ນວ່າ IDs ~4-6M ບໍ່ໄດ້ຖືກ torrent ເພາະວ່າມັນຊ້ອນກັບ Zlib torrents ຂອງພວກເຮົາ). ບົດຄວາມບລັອກຂອງພວກເຮົາກ່ຽວກັບການປ່ອຍການຕູນ ທໍເຣນການຕູນຢູ່ຄັງເອກະສານຂອງ Anna ສຳລັບປະຫວັດຄວາມເປັນມາຂອງການແຍກຄັງເກັບ Library Genesis ຕ່າງໆ, ກະລຸນາເບິ່ງໜ້າ <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li ມີເນື້ອຫາແລະຂໍ້ມູນ Metadata ສ່ວນໃຫຍ່ຄືກັນກັບ Libgen.rs, ແຕ່ມີການລວມຄັງເກັບພິເສດເຊັ່ນການຕູນ, ວາລະສານ, ແລະເອກະສານມາດຕະຖານ. ມັນຍັງໄດ້ລວມ <a %(a_scihub)s>Sci-Hub</a> ເຂົ້າໃນຂໍ້ມູນ Metadata ແລະເຄື່ອງມືຄົ້ນຫາ, ຊຶ່ງເປັນສິ່ງທີ່ພວກເຮົາໃຊ້ສຳລັບຖານຂໍ້ມູນຂອງພວກເຮົາ. ຂໍ້ມູນ Metadata ສຳລັບຄັງເກັບນີ້ມີຢູ່ຟຣີ <a %(a_libgen_li)s>ທີ່ libgen.li</a>. ແຕ່, ເຊີເວີນີ້ຊ້າແລະບໍ່ຮອງຮັບການຕໍ່ການເຊື່ອມຕໍ່ທີ່ຂາດຫາຍ. ໄຟລ໌ດຽວກັນນີ້ຍັງມີຢູ່ທີ່ <a %(a_ftp)s>ເຊີເວີ FTP</a>, ທີ່ເຮັດວຽກໄດ້ດີກວ່າ. ບໍ່ໃຊ່ນິຍາຍກໍມີຄວາມແຕກຕ່າງອອກໄປ, ແຕ່ບໍ່ມີການປ່ອນໃໝ່. ມັນເບິ່ງເຫັນວ່າເກີດຂຶ້ນຕັ້ງແຕ່ຕົ້ນປີ 2022, ແຕ່ພວກເຮົາຍັງບໍ່ໄດ້ຢືນຢັນສິ່ງນີ້. ຕາມທີ່ຜູ້ບໍລິຫານ Libgen.li ກ່າວ, ຄວາມລວມ "fiction_rus" (ນິຍາຍລັດເຊຍ) ຄວນຈະຖືກຄຸ້ມຄອງໂດຍ torrent ທີ່ຖືກປ່ອຍອອກປະຈຳຈາກ <a %(a_booktracker)s>booktracker.org</a>, ທີ່ເປັນທີ່ສັງເກດຄື torrent ຂອງ <a %(a_flibusta)s>flibusta</a> ແລະ <a %(a_librusec)s>lib.rus.ec</a> (ທີ່ພວກເຮົາມີກະຈົກ <a %(a_torrents)s>ທີ່ນີ້</a>, ແຕ່ພວກເຮົາຍັງບໍ່ໄດ້ກຳນົດວ່າ torrent ໃດທີ່ສອດຄ່ອງກັບໄຟລ໌ໃດ). ຄວາມລວມນິຍາຍມີ torrent ຂອງຕົນເອງ (ແຍກອອກຈາກ <a %(a_href)s>Libgen.rs</a>) ເລີ່ມຕົ້ນທີ່ %(start)s. ຂອບເຂດບາງສ່ວນທີ່ບໍ່ມີ torrent (ເຊັ່ນຂອບເຂດນິຍາຍ f_3463000 ຫາ f_4260000) ອາດຈະເປັນໄຟລ໌ຂອງ Z-Library (ຫຼືໄຟລ໌ທີ່ຊ້ຳກັນອື່ນໆ), ແມ່ນວ່າພວກເຮົາອາດຈະຕ້ອງການທຳການລົບຄວາມຊ້ຳແລະສ້າງ torrent ສຳລັບໄຟລ໌ທີ່ມີຄວາມເປັນອັນດຽວຂອງ lgli ໃນຂອບເຂດເຫຼົ່ານີ້. ສະຖິຕິສຳລັບຄວາມລວມທັງໝົດສາມາດພົບໄດ້ <a %(a_href)s>ໃນເວັບໄຊຂອງ libgen</a>. Torrent ມີພ້ອມສຳລັບເນື້ອຫາເພີ່ມເຕີມສ່ວນໃຫຍ່, ທີ່ເປັນທີ່ສັງເກດຄື torrent ສຳລັບປື້ມການຕູນ, ນິຕິຍະສານ, ແລະເອກະສານມາດຕະຖານທີ່ໄດ້ຖືກປ່ອຍອອກມາຮ່ວມກັນກັບ ຄັງເກັບຂໍ້ມູນຂອງ Anna. ຂໍ້ສັງເກດວ່າໄຟລ໌ທໍເຣນທີ່ກ່ຽວກັບ “libgen.is” ແມ່ນການສະທ້ອນຂອງ <a %(a_libgen)s>Libgen.rs</a> (“.is” ແມ່ນໂດເມນທີ່ແຕກຕ່າງທີ່ໃຊ້ໂດຍ Libgen.rs). ຊັບພະຍາກອນທີ່ມີປະໂຫຍດໃນການໃຊ້ຂໍ້ມູນເມຕາຄື <a %(a_href)s>ໜ້ານີ້</a>. %(icon)s ຄວາມລວມ "fiction_rus" (ນິຍາຍລັດເຊຍ) ບໍ່ມີ torrent ທີ່ສະເພາະ, ແຕ່ມີການຄຸ້ມຄອງໂດຍ torrent ຈາກຜູ້ອື່ນ, ແລະພວກເຮົາມີ <a %(fiction_rus)s>ກະຈົກ</a>. Torrent ນິຍາຍລັດເຊຍຢູ່ໃນ ຄັງເກັບຂໍ້ມູນຂອງ Anna ທໍເຣນນິຍາຍຢູ່ຄັງເອກະສານຂອງ Anna ກະດານສົນທະນາ ຂໍ້ມູນເມຕາ ຂໍ້ມູນເມຕາຜ່ານ FTP ທໍເຣນນິຕິຍະສານຢູ່ຄັງເອກະສານຂອງ Anna ຂໍ້ມູນຂອງຂໍ້ມູນເມຕາ ການສະທ້ອນຂອງທໍເຣນອື່ນໆ (ແລະທໍເຣນນິຍາຍແລະການຕູນທີ່ເປັນເອກະລັກ) Torrent ເອກະສານມາດຕະຖານຢູ່ໃນ ຄັງເກັບຂໍ້ມູນຂອງ Anna Libgen.li ທອເຣນໂດຍ ຄັງເອກະສານຂອງ Anna (ປົກປຶ້ມ) Library Genesis ແມ່ນທີ່ຮູ້ຈັກກັນດີສຳລັບການໃຫ້ຂໍ້ມູນຂອງພວກເຂົາໃນຮູບແບບກອງຂໍ້ມູນຜ່ານທອເຣນ. ການຮວບຮວມ Libgen ຂອງພວກເຮົາປະກອບດ້ວຍຂໍ້ມູນຊ່ວຍເຫຼືອທີ່ພວກເຂົາບໍ່ໄດ້ປ່ອຍໂດຍກົງ, ໃນການຮ່ວມມືກັບພວກເຂົາ. ຂອບໃຈຫຼາຍໆຕໍ່ທຸກຄົນທີ່ມີສ່ວນຮ່ວມກັບ Library Genesis ທີ່ໄດ້ຮ່ວມງານກັບພວກເຮົາ! ບລັອກຂອງພວກເຮົາກ່ຽວກັບການປ່ອຍປົກປຶ້ມ ໜ້ານີ້ແມ່ນກ່ຽວກັບເວີຊັນ “.rs”. ມັນເປັນທີ່ຮູ້ຈັກສຳລັບການປະກາດຂໍ້ມູນເມຕາແລະເນື້ອຫາເຕັມຂອງຄັງປື້ມຂອງມັນຢ່າງສະໝໍາເສີຍ. ຄັງປື້ມຂອງມັນຖືກແບ່ງອອກເປັນສ່ວນນິຍາຍແລະບໍ່ແມ່ນນິຍາຍ. ຊັບພະຍາກອນທີ່ມີປະໂຫຍດໃນການໃຊ້ຂໍ້ມູນເມຕາຄື <a %(a_metadata)s>ໜ້ານີ້</a> (ບລັອກຊ່ວງ IP, ອາດຈະຕ້ອງໃຊ້ VPN). ຕັ້ງແຕ່ວັນທີ 2024-03, ທອເຣນໃໝ່ໆກຳລັງຖືກໂພສໃນ <a %(a_href)s>ກະທູ້ຟອຣັມນີ້</a> (ບລັອກຊ່ວງ IP, ອາດຈະຕ້ອງໃຊ້ VPN). ທອເຣນນິຍາຍໃນ ຄັງເອກະສານຂອງ Anna ທອເຣນນິຍາຍຂອງ Libgen.rs ກະທູ້ສົນທະນາຂອງ Libgen.rs Libgen.rs Metadata ຂໍ້ມູນຂ່າວສານຂອງ Libgen.rs ທອເຣນບໍ່ແມ່ນນິຍາຍຂອງ Libgen.rs ທອເຣນບໍ່ແມ່ນນິຍາຍໃນ ຄັງເອກະສານຂອງ Anna %(example)s ສຳລັບປຶ້ມນິຍາຍ. ການ <a %(blog_post)s>ປ່ອຍຄັ້ງທຳອິດນີ້</a> ແມ່ນຂະໜາດນ້ອຍຫຼາຍ: ປະມານ 300GB ຂອງປົກປຶ້ມຈາກການແຍກສາຂາ Libgen.rs, ທັງນິຍາຍແລະບໍ່ແມ່ນນິຍາຍ. ພວກເຂົາຖືກຈັດລຽງໃນລັກສະນະດຽວກັນກັບທີ່ພວກເຂົາປາກົດໃນ libgen.rs, ເຊັ່ນ: %(example)s ສຳລັບປຶ້ມບໍ່ແມ່ນນິຍາຍ. ຄືກັນກັບການຮວບຮວມ Z-Library, ພວກເຮົາໄດ້ໃສ່ພວກເຂົາທັງໝົດໃນໄຟລ໌ .tar ຂະໜາດໃຫຍ່, ທີ່ສາມາດຖືກຕິດຕັ້ງໂດຍໃຊ້ <a %(a_ratarmount)s>ratarmount</a> ຖ້າທ່ານຕ້ອງການເສີບໄຟລ໌ໂດຍກົງ. ການປ່ອຍຄັ້ງທີ 1 (%(date)s) ເລື່ອງສັ້ນໆຂອງການແຍກຂອງ Library Genesis (ຫຼື “Libgen”) ແມ່ນວ່າຕາມເວລາ, ຄົນທີ່ມີສ່ວນຮ່ວມກັບ Library Genesis ໄດ້ມີການແຕກແຍກກັນໄປ, ແລະໄປທາງຂອງຕົນເອງ. ຕາມຂໍ້ຄວາມສົນທະນາ <a %(a_mhut)s>ໃນບອດນີ້</a>, Libgen.li ເຄີຍຖືກໂຮສທີ່ “http://free-books.dontexist.com”. ເວີຊັນ “.fun” ໄດ້ຖືກສ້າງຂຶ້ນໂດຍຜູ້ກໍ່ຕັ້ງດັ້ງເດີມ. ມັນກຳລັງຖືກປັບປຸງໃນທາງທີ່ມີການແຈກຢາຍຫຼາຍຂຶ້ນ. <a %(a_li)s>ເວີຊັນ “.li”</a> ມີຄັງການຕູນຂະໜາດໃຫຍ່, ພ້ອມກັບເນື້ອຫາອື່ນໆ, ທີ່ຍັງບໍ່ (ຍັງ) ມີພ້ອມສຳລັບການດາວໂຫລດຂະໜາດໃຫຍ່ຜ່ານທໍເຣນ. ມັນມີການສະເພາະຂອງຄັງທໍເຣນຂອງປື້ມນິຍາຍ, ແລະມັນມີຂໍ້ມູນເມຕາຂອງ <a %(a_scihub)s>Sci-Hub</a> ໃນຖານຂໍ້ມູນຂອງມັນ. ເວີຊັນ “.rs” ມີຂໍ້ມູນທີ່ຄ້າຍຄືກັນຫຼາຍ, ແລະມັກຈະປ່ອຍຄັງຂໍ້ມູນຂອງພວກເຂົາເປັນທໍເຣນຂະໜາດໃຫຍ່ຢ່າງສະໝໍາເສີຍ. ມັນຖືກແບ່ງອອກເປັນສ່ວນ “ນິຍາຍ” ແລະ “ບໍ່ແມ່ນນິຍາຍ”. ເດີມທີ່ “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> ໃນບາງຄວາມໝາຍກໍເປັນການແຍກຂອງ Library Genesis, ແມ່ນວ່າພວກເຂົາໃຊ້ຊື່ທີ່ແຕກຕ່າງສຳລັບໂຄງການຂອງພວກເຂົາ. Libgen.rs ພວກເຮົາຍັງເພີ່ມພູນການສະສົມຂອງພວກເຮົາດ້ວຍແຫຼ່ງຂໍ້ມູນທີ່ມີແຕ່ Metadata, ທີ່ພວກເຮົາສາມາດຕົກລົງກັບໄຟລ໌, ເຊັ່ນ ໃຊ້ເລກ ISBN ຫຼືສະຫນາມອື່ນໆ. ດ້ານລຸ່ມນີ້ເປັນພາບລວມຂອງເຫຼົ່ານັ້ນ. ອີກຄັ້ງ, ແຫຼ່ງຂໍ້ມູນບາງອັນເປັນເປີດທັງໝົດ, ໃນຂະນະທີ່ບາງອັນພວກເຮົາຕ້ອງຂູດຂໍ້ມູນ. ຂໍເຫັນໃຈວ່າໃນການຄົ້ນຫາ metadata, ພວກເຮົາຈະສະແດງບັນທຶກຕົ້ນສະບັບ. ພວກເຮົາບໍ່ໄດ້ຄວບຮວມບັນທຶກໃດໆ. ແຫຼ່ງຂໍ້ມູນທີ່ມີແຕ່ Metadata Open Library ແມ່ນໂຄງການໂຄດເປັນໂຄດເປີດໂດຍ Internet Archive ເພື່ອຈັດລຽງປຶ້ມທຸກເລື່ອງໃນໂລກ. ມັນມີການສະແກນປຶ້ມທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກແລະມີປຶ້ມຫຼາຍໆທີ່ພ້ອມໃຫ້ຢືມອ່ານດິຈິຕອນ. ລາຍການຂໍ້ມູນຂອງປຶ້ມຂອງມັນສາມາດດາວໂຫລດໄດ້ຟຣີແລະຖືກລວມໄວ້ໃນ ຄັງເອກະສານຂອງ Anna (ແຕ່ບໍ່ໄດ້ຢູ່ໃນການຄົ້ນຫາຕອນນີ້, ຍົກເວັ້ນຖ້າທ່ານຄົ້ນຫາລະຫັດ Open Library ໂດຍກົງ). Open Library ຍົກເວັ້ນການຊ້ຳກັນ ອັບເດດລ່າສຸດ ເປີເຊັນຂອງຈຳນວນໄຟລ໌ %% ຖືກສຳເນົາໂດຍ AA / torrents ທີ່ມີຢູ່ ຂະໜາດ ແຫຼ່ງ ດ້ານລຸ່ມນີ້ແມ່ນພາບລວມຢ່າງຫວ່ອງໄວຂອງແຫຼ່ງຂອງໄຟລ໌ໃນ Anna’s Archive. ເພາະວ່າຄັງເງົາມັກຈະຊິງຂໍ້ມູນຈາກກັນແລະກັນ, ມີການຊ້ອນກັນຫຼາຍລະຫວ່າງຄັງເຫຼົ່ານັ້ນ. ນັ້ນເປັນເຫດຜົນທີ່ຕົວເລກບໍ່ຄືກັນກັບລວມທັງໝົດ. ເປີເຊັນເທັນຂອງ “ສະທ້ອງແລະຫຼັກຖານໂດຍ Anna’s Archive” ສະແດງໃຫ້ເຫັນວ່າພວກເຮົາສະທ້ອງໄຟລ໌ເອງຈຳນວນເທົ່າໃດ. ພວກເຮົາສະທ້ອງໄຟລ໌ເຫຼົ່ານັ້ນເປັນກຸ່ມຜ່ານ torrents, ແລະເຮັດໃຫ້ມີພ້ອມສຳລັບການດາວໂຫລດໂດຍກົງຜ່ານເວັບໄຊພາລະຄູ່ຄ້າ. ພາບລວມ ລວມ Torrent ຢູ່ໃນ Anna’s Archive ສຳລັບພື້ນຫຼັງກ່ຽວກັບ Sci-Hub, ກະລຸນາອ້າງອີງເວັບໄຊທ໌ <a %(a_scihub)s>ທາງການ</a> ຂອງມັນ, <a %(a_wikipedia)s>ໜ້າ Wikipedia</a>, ແລະ <a %(a_radiolab)s>ການສຳພາດຜ່ານວິທະຍຸ</a> ນີ້. ຫມາຍເຫດວ່າ Sci-Hub ໄດ້ຖືກ <a %(a_reddit)s>ແຊ່ແຂງຕັ້ງແຕ່ປີ 2021</a>. ມັນເຄີຍຖືກແຊ່ແຂງມາແລ້ວ, ແຕ່ໃນປີ 2021 ມີເອກະສານຫຼາຍລ້ານສຳນວນຖືກເພີ່ມເຂົ້າມາ. ຢັງໄດ້, ມີຈຳນວນເອກະສານຈຳກັດທີ່ເພີ່ມເຂົ້າໃນຄັງສະມາຊິກ “scimag” ຂອງ Libgen, ແຕ່ບໍ່ພຽງພໍທີ່ຈະສົ່ງເສີມໃຫ້ມີການສ້າງ torrent ໃໝ່ໆ. ພວກເຮົາໃຊ້ metadata ຂອງ Sci-Hub ທີ່ຈັດຫາໂດຍ <a %(a_libgen_li)s>Libgen.li</a> ໃນຄັງ “scimag” ຂອງມັນ. ພວກເຮົາຍັງໃຊ້ dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. ຫມາຍເຫດວ່າ torrent “smarch” ໄດ້ຖືກ <a %(a_smarch)s>ລົບລ້າງ</a> ແລະດັ່ງນັ້ນບໍ່ຖືກລວມໃນລາຍຊື່ torrent ຂອງພວກເຮົາ. Torrent ຢູ່ໃນ Libgen.li Torrent ຢູ່ໃນ Libgen.rs Metadata ແລະ torrent ການອັບເດດຢູ່ Reddit ການສຳພາດຜ່ານວິທະຍຸ ໜ້າ Wikipedia Sci-Hub Sci-Hub: ຖືກແຊ່ແຂງຕັ້ງແຕ່ປີ 2021; ສ່ວນໃຫຍ່ມີຜ່ານ torrents Libgen.li: ເພີ່ມເຕີມເລັກນ້ອຍຕັ້ງແຕ່ນັ້ນ</div> ຄັງຂໍ້ມູນຕົ້ນຕໍບາງອັນສົ່ງເສີມການແບ່ງປັນຂໍ້ມູນຂອງພວກເຂົາເປັນກຸ່ມຜ່ານ torrents, ໃນຂະນະທີ່ຄັງອື່ນບໍ່ພ້ອມທີ່ຈະແບ່ງປັນຄັງຂອງພວກເຂົາ. ໃນກໍລະນີຫຼັງ, Anna’s Archive ພະຍາຍາມທີ່ຈະຂູດຄັງຂອງພວກເຂົາ, ແລະເຮັດໃຫ້ມີພ້ອມ (ເບິ່ງຫນ້າ <a %(a_torrents)s>Torrents</a> ຂອງພວກເຮົາ). ຍັງມີສະຖານະການກາງໆ, ຕົວຢ່າງເຊັ່ນ, ທີ່ຄັງຕົ້ນຕໍຍິນດີທີ່ຈະແບ່ງປັນ, ແຕ່ບໍ່ມີຊັບພະຍາກອນທີ່ຈະເຮັດເຊັ່ນນັ້ນ. ໃນກໍລະນີເຫຼົ່ານັ້ນ, ພວກເຮົາກໍພະຍາຍາມຊ່ວຍເຫຼືອ. ດ້ານລຸ່ມນີ້ເປັນພາບລວມວ່າພວກເຮົາເຊື່ອມຕໍ່ກັບຄັງຂໍ້ມູນຕົ້ນຕໍຕ່າງໆແນວໃດ. ຄັງຂໍ້ມູນຕົ້ນຕໍ %(icon)s ຖານຂໍ້ມູນໄຟລ໌ຕ່າງໆທີ່ກະຈາຍຢູ່ທົ່ວອິນເຕີເນັດຈີນ; ແມ່ນຖານຂໍ້ມູນທີ່ຕ້ອງຈ່າຍເງິນບ່ອຍໆ %(icon)s ໄຟລ໌ສ່ວນໃຫຍ່ສາມາດເຂົ້າເຖິງໄດ້ດ້ວຍບັນຊີ BaiduYun ທີ່ມີຄ່າບໍລິການ; ຄວາມໄວໃນການດາວໂຫລດຊ້າ. %(icon)s ຄັງເອກະສານຂອງ Anna ຈັດການການສະສົມ <a %(duxiu)s>ໄຟລ໌ຂອງ DuXiu</a> %(icon)s ຖານຂໍ້ມູນເມຕາດາຕ້າຕ່າງໆ ທີ່ກະຈາຍຢູ່ທົ່ວໄປໃນອິນເຕີເນັດຈີນ; ແມ່ນຖານຂໍ້ມູນທີ່ຕ້ອງຈ່າຍເງິນບ່ອຍໆ %(icon)s ບໍ່ມີການຖອດຂໍ້ມູນ metadata ທີ່ສາມາດເຂົ້າເຖິງໄດ້ງ່າຍສຳລັບການສະສົມທັງໝົດຂອງພວກເຂົາ. %(icon)s ຄັງເອກະສານຂອງ Anna ຈັດການການສະສົມ <a %(duxiu)s>metadata ຂອງ DuXiu</a> ໄຟລ໌ %(icon)s ໄຟລ໌ມີພຽງແຕ່ສາມາດຢືມໄດ້ໃນຂອບເຂດທີ່ຈຳກັດ, ດ້ວຍຂໍ້ຈຳກັດການເຂົ້າເຖິງຕ່າງໆ %(icon)s ຄັງເກັບຂໍ້ມູນຂອງ Anna ຈັດການການເກັບລວມ <a %(ia)s>ໄຟລ໌ IA</a> %(icon)s metadata ບາງສ່ວນມີຢູ່ຜ່ານ <a %(openlib)s>ການທິ້ງຖານຂໍ້ມູນ Open Library</a>, ແຕ່ບໍ່ຄອບຄຸມທັງໝົດຂອງການລວມລວມ IA %(icon)s ບໍ່ມີການທິ້ງຂໍ້ມູນ metadata ທີ່ສາມາດເຂົ້າເຖິງໄດ້ງ່າຍສຳລັບການລວມລວມທັງໝົດຂອງພວກເຂົາ %(icon)s Anna’s Archive ຈັດການການລວມລວມ <a %(ia)s>metadata ຂອງ IA</a> ອັບເດດລ່າສຸດ %(icon)s ຄັງເກັບຂໍ້ມູນຂອງ Anna ແລະ Libgen.li ຈັດການລວມກັນຂອງ <a %(comics)s>ປື້ມການຕູນ</a>, <a %(magazines)s>ນິຕິຍະສານ</a>, <a %(standarts)s>ເອກະສານມາດຕະຖານ</a>, ແລະ <a %(fiction)s>ນິຍາຍ (ແຍກອອກຈາກ Libgen.rs)</a>. %(icon)s torrent ບົດຄວາມບໍ່ເປັນນິຍາຍແບ່ງປັນກັບ Libgen.rs (ແລະສຳຮອງ <a %(libgenli)s>ທີ່ນີ້</a>). %(icon)s ການທິ້ງຖານຂໍ້ມູນ <a %(dbdumps)s>HTTP ປະຈຳໄຕມາດ</a> %(icon)s torrent ອັດຕະໂນມັດສຳລັບ <a %(nonfiction)s>ບົດຄວາມບໍ່ເປັນນິຍາຍ</a> ແລະ <a %(fiction)s>ນິຍາຍ</a> %(icon)s Anna’s Archive ຈັດການການລວມລວມ <a %(covers)s>torrent ປົກປຶ້ມ</a> %(icon)s ການທິ້ງຖານຂໍ້ມູນ <a %(dbdumps)s>HTTP ປະຈຳວັນ</a> ຂໍ້ມູນອ້າງອີງ %(icon)s ການດຳເນີນການຖອດຂໍ້ມູນຖານຂໍ້ມູນປະຈຳເດືອນ <a %(dbdumps)s>ຖານຂໍ້ມູນດຳເນີນການ</a> %(icon)s torrent ຂໍ້ມູນມີຢູ່ <a %(scihub1)s>ທີ່ນີ້</a>, <a %(scihub2)s>ທີ່ນີ້</a>, ແລະ <a %(libgenli)s>ທີ່ນີ້</a> %(icon)s ໄຟລ໌ໃໝ່ບາງອັນກຳລັງ <a %(libgenrs)s>ເພີ່ມ</a> ໃນ “scimag” ຂອງ Libgen, ແຕ່ບໍ່ພຽງພໍທີ່ຈະສ້າງ torrent ໃໝ່ %(icon)s Sci-Hub ໄດ້ຢຸດການເພີ່ມໄຟລ໌ໃໝ່ຕັ້ງແຕ່ປີ 2021. %(icon)s ການທິ້ງຂໍ້ມູນ metadata ມີຢູ່ <a %(scihub1)s>ທີ່ນີ້</a> ແລະ <a %(scihub2)s>ທີ່ນີ້</a>, ພ້ອມທັງເປັນສ່ວນຫນຶ່ງຂອງ <a %(libgenli)s>ຖານຂໍ້ມູນ Libgen.li</a> (ທີ່ພວກເຮົາໃຊ້) ຕົ້ນຕໍ %(icon)s ແຫຼ່ງຂໍ້ມູນນ້ອຍໆ ຫຼື ບໍ່ມີຄວາມສຳຄັນຫຼາຍ. ພວກເຮົາສົ່ງເສີມໃຫ້ຜູ້ຄົນອັບໂຫລດໄປຫາຫ້ອງສະໝຸດເງົາອື່ນໆກ່ອນ, ແຕ່ບາງຄັ້ງຜູ້ຄົນມີການສະສົມທີ່ໃຫຍ່ເກີນໄປທີ່ຄົ້ນຫາບໍ່ໄດ້, ແຕ່ບໍ່ໃຫຍ່ພໍທີ່ຈະມີໝວດໝູ່ເປັນຂອງຕົນເອງ. %(icon)s ບໍ່ມີພ້ອມໃຫ້ໂດຍກົງເປັນຈຳນວນຫຼາຍ, ຖືກປົກປ້ອງຈາກການຂູດຂໍ້ມູນ %(icon)s ຄັງເອກະສານຂອງ Anna ຈັດການການສະສົມ <a %(worldcat)s>metadata ຂອງ OCLC (WorldCat)</a> %(icon)s Anna’s Archive ແລະ Z-Library ຈັດການລວມລວມຮ່ວມກັນຂອງ <a %(metadata)s>metadata ຂອງ Z-Library</a> ແລະ <a %(files)s>ໄຟລ໌ Z-Library</a> Datasets ພວກເຮົາຄວບຮວມແຫຼ່ງຂໍ້ມູນທັງໝົດທີ່ກ່າວມາເຂົ້າກັນເປັນຖານຂໍ້ມູນຮວມທີ່ພວກເຮົາໃຊ້ເພື່ອບໍລິການເວັບໄຊນີ້. ຖານຂໍ້ມູນຮວມນີ້ບໍ່ສາມາດເຂົ້າເຖິງໂດຍກົງ, ແຕ່ເນື່ອງຈາກ Anna’s Archive ເປັນເວັບໄຊເປີດແຫຼ່ງທີ່ມາທັງໝົດ, ມັນສາມາດຖືກ <a %(a_generated)s>ສ້າງ</a> ຫຼື <a %(a_downloaded)s>ດາວໂຫລດ</a> ເປັນຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB ໄດ້ຄ່ອນຂ້າງງ່າຍດາຍ. ສະບັບສະແກຣິບໃນໜ້ານັ້ນຈະດາວໂຫລດ metadata ທີ່ຕ້ອງການທັງໝົດຈາກແຫຼ່ງທີ່ກ່າວມາຂ້າງເທິງໂດຍອັດຕະໂນມັດ. ຖ້າທ່ານຕ້ອງການສຳຫຼວດຂໍ້ມູນຂອງພວກເຮົາກ່ອນທີ່ຈະລັງສະແກຣິບເຫຼົ່ານັ້ນໃນທ້ອງຖິ່ນ, ທ່ານສາມາດເບິ່ງໄຟລ໌ JSON ຂອງພວກເຮົາ, ທີ່ລິ້ງຕໍ່ໄປຫາໄຟລ໌ JSON ອື່ນໆ. <a %(a_json)s>ໄຟລ໌ນີ້</a> ເປັນຈຸດເລີ່ມຕົ້ນທີ່ດີ. ຖານຂໍ້ມູນຮວມ Torrents ໂດຍ Anna’s Archive ເບິ່ງ ຄົ້ນຫາ ແຫຼ່ງຂໍ້ມູນນ້ອຍໆ ຫຼື ບໍ່ມີຄວາມສຳຄັນຫຼາຍ. ພວກເຮົາສົ່ງເສີມໃຫ້ຜູ້ຄົນອັບໂຫລດໄປຫາຫ້ອງສະໝຸດເງົາອື່ນໆກ່ອນ, ແຕ່ບາງຄັ້ງຜູ້ຄົນມີການສະສົມທີ່ໃຫຍ່ເກີນໄປທີ່ຄົ້ນຫາບໍ່ໄດ້, ແຕ່ບໍ່ໃຫຍ່ພໍທີ່ຈະມີໝວດໝູ່ເປັນຂອງຕົນເອງ. ພາບລວມຈາກ <a %(a1)s>ໜ້າຂໍ້ມູນ Datasets</a>. ຈາກ <a %(a_href)s>aaaaarg.fail</a>. ເຫັນວ່າຄ່ອນຂ້າງຄົບຖ້ວນ. ຈາກອາສາສະໝັກຂອງພວກເຮົາ “cgiym”. ຈາກ <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. ມີການຊ້ອນທັບຄ່ອນຂ້າງສູງກັບການເກັບລວມເອກະສານທີ່ມີຢູ່ແລ້ວ, ແຕ່ມີການຕົກລົງຂອງ MD5 ນ້ອຍຫລາຍ, ດັ່ງນັ້ນພວກເຮົາຕັດສິນໃຈທີ່ຈະເກັບມັນໄວ້ທັງໝົດ. ການຂູດຂໍ້ມູນຂອງ <q>iRead eBooks</q> (= ອ່ານຕາມສຽງ <q>ai rit i-books</q>; airitibooks.com), ໂດຍອາສາສະໝັກ <q>j</q>. ສອດຄ່ອງກັບ metadata <q>airitibooks</q> ໃນ <a %(a1)s><q>ການຂູດຂໍ້ມູນ metadata ອື່ນໆ</q></a>. ຈາກການລວມລວມ <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. ບາງສ່ວນມາຈາກແຫຼ່ງຕົ້ນສະບັບ, ບາງສ່ວນມາຈາກ the-eye.eu, ບາງສ່ວນມາຈາກກະຈົກອື່ນໆ. ຈາກເວັບໄຊທ໌ torrent ຫນັງສືສ່ວນຕົວ, <a %(a_href)s>Bibliotik</a> (ມັກຈະຖືກອ້າງອີງວ່າ “Bib”), ຫນັງສືທີ່ຖືກມັດເປັນ torrents ຕາມຊື່ (A.torrent, B.torrent) ແລະຖືກແຈກຢາຍຜ່ານ the-eye.eu. ຈາກອາສາສະໝັກຂອງພວກເຮົາ “bpb9v”. ສໍາລັບຂໍ້ມູນເພີ່ມເຕີມກ່ຽວກັບ <a %(a_href)s>CADAL</a>, ກະລຸນາເບິ່ງບັນທຶກໃນ <a %(a_duxiu)s>ເວັບໄຊທ໌ DuXiu dataset</a> ຂອງພວກເຮົາ. ຂໍ້ມູນເພີ່ມເຕີມຈາກອາສາສະໝັກຂອງພວກເຮົາ “bpb9v”, ສ່ວນໃຫຍ່ແມ່ນໄຟລ໌ DuXiu, ພ້ອມກັບໂຟນເດີ “WenQu” ແລະ “SuperStar_Journals” (SuperStar ແມ່ນບໍລິສັດທີ່ຢູ່ເບື້ອງຫລັງ DuXiu). ຈາກອາສາສະໝັກຂອງພວກເຮົາ “cgiym”, ຂໍ້ຄວາມຈີນຈາກແຫຼ່ງຕ່າງໆ (ຖືກສະແດງເປັນຫນ່ວຍຍ່ອຍ), ລວມທັງຈາກ <a %(a_href)s>China Machine Press</a> (ຜູ້ພິມຈີນທີ່ສໍາຄັນ). ການເກັບລວມທີ່ບໍ່ແມ່ນຂອງຈີນ (ຖືກສະແດງເປັນຫນ່ວຍຍ່ອຍ) ຈາກອາສາສະໝັກຂອງພວກເຮົາ “cgiym”. ການຂູດຂໍ້ມູນຂອງປື້ມກ່ຽວກັບສະຖາປັດຕະຍະກໍາຈີນ, ໂດຍອາສາສະໝັກ <q>cm</q>: <q>ຂ້ອຍໄດ້ມາໂດຍການໃຊ້ປະໂຫຍດຈາກຊ່ອງໂວ່ຂອງເຄືອຂ່າຍທີ່ສະຖານພິມ, ແຕ່ຊ່ອງໂວ່ນັ້ນໄດ້ຖືກປິດແລ້ວ</q>. ສອດຄ່ອງກັບ metadata <q>chinese_architecture</q> ໃນ <a %(a1)s><q>ການຂູດຂໍ້ມູນ metadata ອື່ນໆ</q></a>. ຫນັງສືຈາກສໍານັກພິມວິຊາການ <a %(a_href)s>De Gruyter</a>, ຖືກເກັບລວມຈາກ torrent ໃຫຍ່ບາງອັນ. ການຂູດຂໍ້ມູນຂອງ <a %(a_href)s>docer.pl</a>, ເວັບໄຊທ໌ແບ່ງປັນໄຟລ໌ຂອງໂປໂລນທີ່ມຸ່ງເນັ້ນຫນັງສືແລະຜົນງານຂຽນອື່ນໆ. ຖືກຂູດຂໍ້ມູນໃນປາຍປີ 2023 ໂດຍອາສາສະໝັກ “p”. ພວກເຮົາບໍ່ມີເມຕາດາຕ້າທີ່ດີຈາກເວັບໄຊທ໌ຕົ້ນສະບັບ (ແມ່ນກະທັ້ງນາມສະກຸນໄຟລ໌), ແຕ່ພວກເຮົາໄດ້ກັດກອນໄຟລ໌ທີ່ຄ້າຍຄືຫນັງສືແລະມັກຈະສາມາດສະກັດເມຕາດາຕ້າຈາກໄຟລ໌ເອງ. DuXiu epubs, ໂດຍກົງຈາກ DuXiu, ເກັບລວມໂດຍອາສາສະໝັກ “w”. ຫນັງສື DuXiu ທີ່ເປັນ ebooks ທີ່ມີຢູ່ໂດຍກົງແມ່ນຫນັງສືທີ່ໃຫມ່ສຸດ, ດັ່ງນັ້ນສ່ວນໃຫຍ່ຂອງເຫຼົ່ານີ້ຕ້ອງເປັນຫນັງສືໃຫມ່. ໄຟລ໌ DuXiu ທີ່ເຫຼືອຈາກອາສາສະໝັກ “m”, ທີ່ບໍ່ແມ່ນຮູບແບບ proprietary PDG ຂອງ DuXiu (ຖານຂໍ້ມູນ <a %(a_href)s>DuXiu</a> ຫລັກ). ເກັບລວມຈາກແຫຼ່ງຕົ້ນສະບັບຫລາຍໆ, ໂຊກບໍ່ດີທີ່ບໍ່ໄດ້ຮັກສາແຫຼ່ງເຫຼົ່ານັ້ນໄວ້ໃນເສັ້ນທາງໄຟລ໌. <span></span> <span></span> <span></span> ການຂູດຂໍ້ມູນຂອງປື້ມເອໂຣຕິກ, ໂດຍອາສາສະໝັກ <q>do no harm</q>. ສອດຄ່ອງກັບ metadata <q>hentai</q> ໃນ <a %(a1)s><q>ການຂູດຂໍ້ມູນ metadata ອື່ນໆ</q></a>. <span></span> <span></span> ການເກັບລວມທີ່ຖືກຂູດຂໍ້ມູນຈາກສໍານັກພິມ Manga ຂອງຍີ່ປຸ່ນໂດຍອາສາສະໝັກ “t”. <a %(a_href)s>ຄັງຂໍ້ມູນຕຸລາການທີ່ເລືອກຂອງ Longquan</a>, ຈັດຫາໂດຍອາສາສະໝັກ “c”. ການຂູດຂໍ້ມູນຂອງ <a %(a_href)s>magzdb.org</a>, ພັນທະມິດຂອງ Library Genesis (ມັນຖືກເຊື່ອມໂຍງຢູ່ໜ້າເວັບ libgen.rs) ແຕ່ບໍ່ຕ້ອງການສະໜອງໄຟລ໌ຂອງພວກເຂົາໂດຍກົງ. ໄດ້ຮັບໂດຍອາສາສະໝັກ "p" ໃນປີ 2023. <span></span> ການອັບໂຫລດນ້ອຍໆຕ່າງໆ, ທີ່ນ້ອຍເກີນໄປທີ່ຈະເປັນຫົວໝວດນ້ອຍເປັນຂອງຕົນເອງ, ແຕ່ຖືກສະແດງເປັນໄດເຣັກໂຕຣີ. ປື້ມອີເລັກທຣອນິກຈາກ AvaxHome, ເວັບໄຊທ໌ແບ່ງປັນໄຟລຂອງຣັດເຊຍ. ຄັງເກັບຂ່າວສານແລະນິຕຍະສານ. ສອດຄ່ອງກັບ metadata <q>newsarch_magz</q> ໃນ <a %(a1)s><q>ການຂູດຂໍ້ມູນ metadata ອື່ນໆ</q></a>. ການຂູດຂໍ້ມູນຂອງ <a %(a1)s>ສູນການເອກະສານປັນຍາ</a>. ການລວມລວມຂອງອາສາສະໝັກ “o” ທີ່ເກັບຮວບຫວຍປື້ມໂປແລນຈາກເວັບໄຊຕ໌ຕົ້ນສະບັບ (“scene”). ການລວມລວມຂອງ <a %(a_href)s>shuge.org</a> ໂດຍອາສາສະໝັກ “cgiym” ແລະ “woz9ts”. <span></span> <a %(a_href)s>“ຫໍສະໝຸດຈັກວານຂອງ Trantor”</a> (ຕັ້ງຊື່ຕາມຫໍສະໝຸດໃນນິຍາຍ), ຖືກລວມລວມໃນປີ 2022 ໂດຍອາສາສະໝັກ “t”. <span></span> <span></span> <span></span> ຊຸດຍ່ອຍ (ຖືກສະແດງເປັນໄດເຣັກໂຕຣີ) ຈາກອາສາສະໝັກ “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (ໂດຍ <a %(a_sikuquanshu)s>Dizhi(迪志)</a> ຢູ່ໄຕ້ຫວັນ), mebook (mebook.cc, 我的小书屋, ຫໍປື້ມນ້ອຍຂອງຂ້ອຍ — woz9ts: “ເວັບໄຊຕ໌ນີ້ເນັ້ນການແບ່ງປັນໄຟລ໌ ebook ທີ່ມີຄຸນນະພາບສູງ, ບາງສ່ວນໄດ້ຖືກຈັດຮູບແບບໂດຍເຈົ້າຂອງເອງ. ເຈົ້າຂອງເວັບໄດ້ຖືກ<a %(a_arrested)s>ຈັບກຸມ</a>ໃນປີ 2019 ແລະມີຄົນເຮັດການລວມລວມໄຟລ໌ທີ່ເຂົາແບ່ງປັນ.”). ໄຟລ໌ DuXiu ທີ່ເຫຼືອຈາກອາສາສະໝັກ “woz9ts”, ທີ່ບໍ່ໄດ້ຢູ່ໃນຮູບແບບ PDG ຂອງ DuXiu (ຍັງຕ້ອງຖືກແປງເປັນ PDF). ການສະສົມ "ອັບໂຫລດ" ແບ່ງອອກເປັນຫົວໝວດນ້ອຍໆ, ທີ່ຖືກລະບຸໃນ AACIDs ແລະຊື່ torrent. ຫົວໝວດນ້ອຍທັງໝົດໄດ້ຖືກກວດສອບກັບການສະສົມຫຼັກແລ້ວ, ແຕ່ໄຟລ໌ JSON "upload_records" ຍັງມີການອ້າງອີງເຖິງໄຟລ໌ຕົ້ນສະບັບຢູ່ຫຼາຍ. ໄຟລ໌ທີ່ບໍ່ແມ່ນປື້ມກໍໄດ້ຖືກລຶບອອກຈາກຫົວໝວດນ້ອຍສ່ວນໃຫຍ່, ແລະປົກກະຕິແມ່ນ <em>ບໍ່</em> ຖືກລະບຸໃນໄຟລ໌ JSON "upload_records". ຫົວໝວດນ້ອຍມີດັ່ງນີ້: ບັນທຶກ ຊຸດຍ່ອຍ ຫົວໝວດນ້ອຍຫຼາຍໆເອງກໍປະກອບດ້ວຍຫົວໝວດນ້ອຍຍ່ອຍ (ຕົວຢ່າງເຊັ່ນຈາກແຫຼ່ງຕົ້ນສະບັບທີ່ແຕກຕ່າງກັນ), ທີ່ຖືກສະແດງເປັນໄດເຣັກໂຕຣີໃນຊ່ອງ "filepath". ການອັບໂຫລດໄປຫາ ຄັງເອກະສານຂອງ Anna ບົດຄວາມບລັອກຂອງພວກເຮົາກ່ຽວກັບຂໍ້ມູນນີ້ <a %(a_worldcat)s>WorldCat</a> ແມ່ນຖານຂໍ້ມູນທີ່ມີລິຂະສິດໂດຍບໍລິສັດບໍ່ສະແຫວງຫາກຳໄລ <a %(a_oclc)s>OCLC</a>, ທີ່ຮວບຮວມບັນທຶກ metadata ຈາກຫໍສະໝຸດທົ່ວໂລກ. ມັນອາດຈະເປັນຄັງສະມາຊິກ metadata ຫໍສະໝຸດທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກ. ໃນເດືອນຕຸລາ 2023 ພວກເຮົາໄດ້ <a %(a_scrape)s>ປ່ອຍ</a> ການຂູດຂໍ້ມູນທີ່ຄົບຖ້ວນຂອງຖານຂໍ້ມູນ OCLC (WorldCat), ໃນ <a %(a_aac)s>ຮູບແບບ Anna’s Archive Containers</a>. ຕຸລາ 2023, ການປ່ອນເບື້ອງຕົ້ນ: OCLC (WorldCat) Torrents ໂດຍ Anna’s Archive ຕົວຢ່າງບັນທຶກໃນ Anna’s Archive (ການລວມລວມຕົ້ນສະບັບ) ຕົວຢ່າງບັນທຶກໃນ Anna’s Archive (“zlib3” ການລວມລວມ) Torrents ໂດຍ Anna’s Archive (metadata + ເນື້ອຫາ) ບົດຄວາມບລັອກກ່ຽວກັບ Release 1 ບົດຄວາມບລັອກກ່ຽວກັບການປ່ອຍທີ 2 ໃນປາຍປີ 2022, ຜູ້ກໍ່ຕັ້ງທີ່ຖືກສົງໄສຂອງ Z-Library ໄດ້ຖືກຈັບກຸມ, ແລະໂດເມນໄດ້ຖືກຍຶດໂດຍທາງການສະຫະລັດ. ຕັ້ງແຕ່ນັ້ນມາເວັບໄຊຕ໌ໄດ້ຄ່ອຍໆກັບຄືນມາອອນໄລນ໌ອີກຄັ້ງ. ບໍ່ຮູ້ວ່າໃຜເປັນຄົນດຳເນີນການໃນປັດຈຸບັນ. ການອັບເດດຂອງເດືອນກຸມພາ 2023. Z-Library ມີຮາກຖານຢູ່ໃນຊຸມຊົນ <a %(a_href)s>Library Genesis</a>, ແລະເລີ່ມຕົ້ນດ້ວຍຂໍ້ມູນຂອງພວກເຂົາ. ຕັ້ງແຕ່ນັ້ນມາ, ມັນໄດ້ພັດທະນາຢ່າງມືອາຊີບຫຼາຍຂຶ້ນ, ແລະມີອິນເຕີເຟສທີ່ທັນສະໄໝຫຼາຍຂຶ້ນ. ດັ່ງນັ້ນ, ພວກເຂົາສາມາດໄດ້ຮັບການບໍລິຈາກຫຼາຍຂຶ້ນ, ທັງເງິນເພື່ອພັດທະນາເວັບໄຊຕ໌ຂອງພວກເຂົາ, ແລະການບໍລິຈາກປື້ມໃໝ່ໆ. ພວກເຂົາໄດ້ສະສົມການລວມລວມຂະໜາດໃຫຍ່ເພີ່ມເຕີມຈາກ Library Genesis. ການລວມລວມປະກອບດ້ວຍສາມສ່ວນ. ໜ້າຄຳອະທິບາຍຕົ້ນສະບັບສຳລັບສອງສ່ວນທຳອິດໄດ້ຖືກຮັກສາໄວ້ທາງລຸ່ມນີ້. ທ່ານຕ້ອງການທັງສາມສ່ວນເພື່ອໄດ້ຮັບຂໍ້ມູນທັງໝົດ (ຍົກເວັ້ນ torrents ທີ່ຖືກແທນທີ່, ທີ່ຖືກຂີດທິ້ງໃນໜ້າ torrents). %(title)s: ການປະກາດປ່ອຍຄັ້ງທຳອິດ. ນີ້ເປັນການປ່ອຍຄັ້ງທຳອິດຂອງສິ່ງທີ່ເມື່ອນັ້ນເອີ້ນວ່າ “Pirate Library Mirror” (“pilimi”). %(title)s: ການປະກາດປ່ອຍຄັ້ງທີສອງ, ເທື່ອນີ້ມີໄຟລ໌ທັງໝົດຢູ່ໃນໄຟລ໌ .tar. %(title)s: ການປະກາດປ່ອຍໃໝ່ເພີ່ມເຕີມ, ໃຊ້ຮູບແບບ <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, ຕອນນີ້ຖືກປ່ອຍຮ່ວມກັນກັບທີມ Z-Library. ກະຈົກສະຫຼອງເລີ່ມຕົ້ນໄດ້ຖືກຮັບມາຢ່າງລະມັດລະວັງຕະຫຼອດປີ 2021 ແລະ 2022. ໃນຂະນະນີ້ມັນມີຄວາມລ້າສະໄໝເລັກນ້ອຍ: ມັນສະທ້ອນໃຫ້ເຫັນສະພາບຂອງການລວມລວມໃນເດືອນມິຖຸນາ 2021. ພວກເຮົາຈະປັບປຸງນີ້ໃນອະນາຄົດ. ໃນຂະນະນີ້ພວກເຮົາກຳລັງມຸ່ງເນັ້ນໃນການປ່ອຍຄັ້ງທຳອິດນີ້ອອກ. ເພາະວ່າ Library Genesis ໄດ້ຖືກສະຫຼອງແລ້ວດ້ວຍ torrent ສາທາລະນະ, ແລະຖືກລວມໃນ Z-Library, ພວກເຮົາໄດ້ທຳການລົບຂໍ້ມູນທີ່ຊ້ຳກັນຂັ້ນພື້ນຖານກັບ Library Genesis ໃນເດືອນມິຖຸນາ 2022. ສຳລັບນີ້ພວກເຮົາໃຊ້ MD5 hashes. ມີຄວາມເປັນໄປໄດ້ວ່າມີເນື້ອຫາທີ່ຊ້ຳກັນຫຼາຍໃນຫ້ອງສະໝຸດ, ເຊັ່ນຮູບແບບໄຟລ໌ທີ່ແຕກຕ່າງກັນຂອງປື້ມດຽວກັນ. ນີ້ຍາກທີ່ຈະກວດຈັບໃຫ້ແມ່ນຍິງ, ດັ່ງນັ້ນພວກເຮົາບໍ່ໄດ້ທຳ. ຫຼັງຈາກການລົບຂໍ້ມູນທີ່ຊ້ຳກັນ, ພວກເຮົາເຫຼືອກັບໄຟລ໌ກວ່າ 2 ລ້ານ, ລວມທັງໝົດປະມານ 7TB. ການລວມລວມປະກອບດ້ວຍສອງສ່ວນ: ການທິ້ງ MySQL “.sql.gz” ຂອງ metadata, ແລະໄຟລ໌ torrent 72 ໄຟລ໌ຂອງປະມານ 50-100GB ຕໍ່ໄຟລ໌. Metadata ປະກອບດ້ວຍຂໍ້ມູນທີ່ລາຍງານໂດຍເວັບໄຊທ໌ Z-Library (ຊື່ເລື່ອງ, ຜູ້ຂຽນ, ຄຳອະທິບາຍ, ປະເພດໄຟລ໌), ພ້ອມທັງຂະໜາດໄຟລ໌ຈິງແລະ md5sum ທີ່ພວກເຮົາສັງເກດ, ເພາະບາງຄັ້ງມັນບໍ່ສອດຄ່ອງກັນ. ມີເຂດຂອງໄຟລ໌ທີ່ Z-Library ເອງມີ metadata ທີ່ບໍ່ຖືກຕ້ອງ. ພວກເຮົາອາດຈະມີການດາວໂຫລດໄຟລ໌ທີ່ບໍ່ຖືກຕ້ອງໃນບາງກໍລະນີທີ່ແຍກຕ່າງຫາກ, ທີ່ພວກເຮົາຈະພະຍາຍາມກວດຈັບແລະແກ້ໄຂໃນອະນາຄົດ. ໄຟລ໌ torrent ຂະໜາດໃຫຍ່ປະກອບດ້ວຍຂໍ້ມູນປື້ມຈິງ, ດ້ວຍ ID ຂອງ Z-Library ເປັນຊື່ໄຟລ໌. ສິ້ນສຸດໄຟລ໌ສາມາດຖືກສ້າງຄືນໃຊ້ການທິ້ງ metadata. ການລວມລວມເປັນການປະສົມຂອງເນື້ອຫາທີ່ບໍ່ໃຊ່ນິຍາຍແລະນິຍາຍ (ບໍ່ແຍກອອກເຫມືອນໃນ Library Genesis). ຄຸນນະພາບກໍ່ແຕກຕ່າງກັນຫຼາຍ. ການປ່ອຍຄັ້ງທຳອິດນີ້ມີພ້ອມແລ້ວ. ຂໍໃຫ້ສັງເກດວ່າໄຟລ໌ torrent ມີພ້ອມພຽງແຕ່ຜ່ານກະຈົກ Tor ຂອງພວກເຮົາ. ການປ່ອຍທີ 1 (%(date)s) ນີ້ແມ່ນໄຟລ໌ torrent ເພີ່ມເຕີມອັນດຽວ. ມັນບໍ່ໄດ້ມີຂໍ້ມູນໃໝ່ໃດໆ, ແຕ່ມັນມີຂໍ້ມູນບາງຢ່າງທີ່ອາດຈະໃຊ້ເວລາຄຳນວນ. ນັ້ນເຮັດໃຫ້ມັນສະດວກທີ່ຈະມີ, ເພາະການດາວໂຫລດ torrent ນີ້ມັກຈະໄວກວ່າການຄຳນວນມັນຈາກຕົ້ນຕໍ່. ໂດຍສະເພາະ, ມັນມີດັດຊະນີ SQLite ສຳລັບໄຟລ໌ tar, ໃຊ້ງານກັບ <a %(a_href)s>ratarmount</a>. Release 2 addendum (%(date)s) ພວກເຮົາໄດ້ຮັບປື້ມທັງຫມົດທີ່ເພີ່ມເຂົ້າໃນ Z-Library ລະຫວ່າງກະຈົກສະຫຼອງຂອງພວກເຮົາຄັ້ງສຸດທ້າຍແລະເດືອນສິງຫາ 2022. ພວກເຮົາຍັງໄດ້ກັບໄປຄົ້ນຫາປື້ມທີ່ພວກເຮົາພາດໃນຄັ້ງທຳອິດ. ທັງຫມົດ, ການລວມລວມໃໝ່ນີ້ມີປະມານ 24TB. ອີກຄັ້ງ, ການລວມລວມນີ້ໄດ້ຖືກລົບຂໍ້ມູນທີ່ຊ້ຳກັນກັບ Library Genesis, ເພາະມີ torrent ທີ່ມີພ້ອມສຳລັບການລວມລວມນັ້ນແລ້ວ. ຂໍ້ມູນຖືກຈັດລຽງຄ້າຍຄືກັບການປ່ອຍຄັ້ງທຳອິດ. ມີການທິ້ງ MySQL “.sql.gz” ຂອງ metadata, ທີ່ລວມທັງ metadata ທັງຫມົດຈາກການປ່ອຍຄັ້ງທຳອິດ, ຈຶ່ງແທນທີ່ມັນ. ພວກເຮົາຍັງເພີ່ມຄໍລຳໃໝ່ບາງຄໍລຳ: ພວກເຮົາໄດ້ກ່າວເຖິງບັນຫານີ້ໃນຄັ້ງທີ່ຜ່ານມາ, ແຕ່ຂໍຊີ້ແຈງອີກຄັ້ງ: “filename” ແລະ “md5” ແມ່ນຄຸນສົມບັດຂອງໄຟລ໌ຈິງ, ໃນຂະນະທີ່ “filename_reported” ແລະ “md5_reported” ແມ່ນສິ່ງທີ່ພວກເຮົາໄດ້ຂຽນມາຈາກ Z-Library. ບາງຄັ້ງສອງສິ່ງນີ້ບໍ່ສອດຄ່ອງກັນ, ດັ່ງນັ້ນພວກເຮົາຈຶ່ງໄດ້ລວມທັງສອງໄວ້. ສຳລັບການປ່ອຍນີ້, ພວກເຮົາໄດ້ປ່ຽນການຈັດລຽງເປັນ “utf8mb4_unicode_ci”, ທີ່ຄວນຈະສອດຄ່ອງກັບສະບັບເກົ່າຂອງ MySQL. ໄຟລ໌ຂໍ້ມູນຄ້າຍຄືກັບຄັ້ງທີ່ແລ້ວ, ແຕ່ພວກເຂົາໃຫຍ່ກວ່າຫຼາຍ. ພວກເຮົາບໍ່ສາມາດທີ່ຈະສ້າງໄຟລ໌ torrent ຂະໜາດນ້ອຍຫຼາຍໆ. “pilimi-zlib2-0-14679999-extra.torrent” ປະກອບດ້ວຍໄຟລ໌ທັງຫມົດທີ່ພວກເຮົາພາດໃນການປ່ອຍຄັ້ງທຳອິດ, ໃນຂະນະທີ່ torrent ອື່ນໆເປັນເຂດ ID ໃໝ່ທັງຫມົດ.  <strong>ປັບປຸງ %(date)s:</strong> ພວກເຮົາໄດ້ສ້າງ torrent ຂອງພວກເຮົາໃຫຍ່ເກີນໄປ, ທຳໃຫ້ລູກຄ້າ torrent ປະສົບຄວາມຍາກລຳບາກ. ພວກເຮົາໄດ້ລຶບພວກເຂົາແລະປ່ອຍ torrent ໃໝ່. <strong>ປັບປຸງ %(date)s:</strong> ຍັງມີໄຟລ໌ຫຼາຍເກີນໄປ, ດັ່ງນັ້ນພວກເຮົາໄດ້ຫໍ່ພວກເຂົາໃນໄຟລ໌ tar ແລະປ່ອຍ torrent ໃໝ່ອີກຄັ້ງ. %(key)s: ວ່າໄຟລ໌ນີ້ມີຢູ່ແລ້ວໃນ Library Genesis ຫຼືບໍ່, ໃນການລວມລວມທີ່ບໍ່ໃຊ່ນິຍາຍຫຼືນິຍາຍ (ຈັບຄູ່ໂດຍ md5). %(key)s: ໄຟລ໌ນີ້ຢູ່ໃນ torrent ໃດ. %(key)s: ຕັ້ງໄວ້ເມື່ອພວກເຮົາບໍ່ສາມາດດາວໂຫລດປື້ມໄດ້. ການປ່ອຍທີ 2 (%(date)s) ການປ່ອຍຂອງ Zlib (ໜ້າຄຳອະທິບາຍຕົ້ນສະບັບ) ໂດເມນ Tor ເວັບໄຊຕ໌ຫຼັກ ການລວມລວມ Z-Library ຄັງ “ຈີນ” ໃນ Z-Library ດູເຫັນວ່າເປັນຄັງດຽວກັນກັບຄັງ DuXiu ຂອງພວກເຮົາ, ແຕ່ມີ MD5 ທີ່ແຕກຕ່າງ. ພວກເຮົາຍົກເວັ້ນໄຟລ໌ເຫຼົ່ານີ້ຈາກ torrents ເພື່ອຫຼີກເວັ້ນການຊ້ຳກັນ, ແຕ່ຍັງຄົງສະແດງໃນດັດຊະນີການຄົ້ນຫາຂອງພວກເຮົາ. ຂໍ້ມູນຂ່າວສານ ທ່ານໄດ້ຮັບ %(percentage)s%% ໂບນັດດາວໂຫລດໄວ, ເພາະທ່ານໄດ້ຮັບການແນະນຳໂດຍຜູ້ໃຊ້ %(profile_link)s. ນີ້ໃຊ້ກັບທັງໄລຍະເວລາສະມາຊິກ. ບໍລິຈາກ ເຂົ້າຮ່ວມ ເລືອກແລ້ວ ສ່ວນຫຼຸດສູງສຸດ %(percentage)s%% Alipay ສະໜັບສະໜູນບັດເຄຣດິດ/ບັດເດບິດສາກົນ. ເບິ່ງ <a %(a_alipay)s>ຄູ່ມືນີ້</a> ເພື່ອຂໍ້ມູນເພີ່ມເຕີມ. ສົ່ງບັດຂອງຂວັນ Amazon.com ໃຫ້ເຮົາໂດຍໃຊ້ບັດເຄຣດິດ/ເດບິດຂອງທ່ານ. ທ່ານສາມາດຊື້ສະກຸນເງິນດິຈິຕອນໂດຍໃຊ້ບັດເຄຣດິດ/ເດບິດ. WeChat (Weixin Pay) ສະໜັບສະໜູນບັດເຄຣດິດ/ເດບິດສາກົນ. ໃນແອັບ WeChat, ໄປທີ່ “Me => Services => Wallet => Add a Card”. ຖ້າທ່ານບໍ່ເຫັນມັນ, ເປີດໃຊ້ມັນໂດຍໃຊ້ “Me => Settings => General => Tools => Weixin Pay => Enable”. (ໃຊ້ເມື່ອສົ່ງ Ethereum ຈາກ Coinbase) ຄັດລອກແລ້ວ! ຄັດລອກ (ຈຳນວນຂັ້ນຕ່ຳຕ່ຳທີ່ສຸດ) (ເຕືອນ: ຈຳນວນຂັ້ນຕ່ຳສູງ) -%(percentage)s%% 12 ເດືອນ 1 ເດືອນ 24 ເດືອນ 3 ເດືອນ 48 ເດືອນ 6 ເດືອນ 96 ເດືອນ ເລືອກວ່າທ່ານຕ້ອງການສະໝັກສະມາຊິກດົນເທົ່າໃດ. <div %(div_monthly_cost)s></div><div %(div_after)s>ຫຼັງຈາກ <span %(span_discount)s></span> ສ່ວນຫຼຸດ</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% ສຳລັບ 12 ເດືອນ ສຳລັບ 1 ເດືອນ ສຳລັບ 24 ເດືອນ ສຳລັບ 3 ເດືອນ ສຳລັບ 48 ເດືອນ ສຳລັບ 6 ເດືອນ ສຳລັບ 96 ເດືອນ %(monthly_cost)s / ເດືອນ ຕິດຕໍ່ພວກເຮົາ ເຊີບເວີຕົງ <strong>SFTP</strong> ການບໍລິຈາກລະດັບອົງການ ຫຼື ການແລກປ່ຽນສຳລັບຄອລເລັກຊັນໃໝ່ (ເຊັ່ນ ການສະແກນໃໝ່, ຊຸດຂໍ້ມູນ OCR). ເຂົ້າເຖິງລະດັບຜູ້ຊຳນານ <strong>ບໍ່ຈຳກັດ</strong> ການເຂົ້າເຖິງຄວາມໄວສູງ <div %(div_question)s>ຂ້ອຍສາມາດຍົກລະດັບການເປັນສະມາຊິກຫຼືມີການເປັນສະມາຊິກຫຼາຍຢ່າງໄດ້ບໍ?</div> <div %(div_question)s>ຂ້ອຍສາມາດບໍລິຈາກໂດຍບໍ່ຕ້ອງເປັນສະມາຊິກໄດ້ບໍ?</div> ໄດ້ແນ່ນອນ. ພວກເຮົາຮັບບໍລິຈາກຈຳນວນໃດກໍໄດ້ທີ່ທີ່ຢູ່ Monero (XMR) ນີ້: %(address)s. <div %(div_question)s>ຂອບເຂດຕໍ່ເດືອນຫມາຍຄວາມວ່າຢ່າງໃດ?</div> ທ່ານສາມາດໄດ້ຮັບຂອບເຂດຕ່ຳຂອງຂອບເຂດໂດຍປະຍຸກຄຸນສ່ວນຫຼຸດທັງຫມົດ, ເຊັ່ນ ເລືອກໄລຍະເວລາທີ່ຍາວກວ່າໜຶ່ງເດືອນ. <div %(div_question)s>ການເປັນສະມາຊິກຈະຕໍ່ອາຍຸອັດຕະໂນມັດຫຼືບໍ່?</div> ການເປັນສະມາຊິກ <strong>ບໍ່</strong> ຕໍ່ອາຍຸອັດຕະໂນມັດ. ທ່ານສາມາດເຂົ້າຮ່ວມໄດ້ຕາມທີ່ທ່ານຕ້ອງການ. <div %(div_question)s>ພວກເຈົ້າໃຊ້ເງິນບໍລິຈາກໃນສິ່ງໃດ?</div> 100%% ຖືກນຳໄປໃຊ້ໃນການອະນຸຮັກແລະເຮັດໃຫ້ຄວາມຮູ້ແລະວັດທະນະທຳຂອງໂລກເຂົ້າເຖິງໄດ້. ປັດຈຸບັນພວກເຮົາໃຊ້ສ່ວນໃຫຍ່ໃນເຊີບເວີ, ການເກັບຮັກສາ, ແລະຄວາມກວ້າງຂອງເຄືອຂ່າຍ. ບໍ່ມີເງິນໃດໆທີ່ໄປຫາສະມາຊິກທີມງານໂດຍສ່ວນຕົວ. <div %(div_question)s>ຂ້ອຍສາມາດບໍລິຈາກຈຳນວນຫຼາຍໄດ້ບໍ?</div> ມັນຈະສຸດຍອດເລີຍ! ສຳລັບການບໍລິຈາກເກີນສອງສາມພັນໂດລາ, ກະລຸນາຕິດຕໍ່ພວກເຮົາໂດຍກົງທີ່ %(email)s. <div %(div_question)s>ທ່ານມີວິທີການຈ່າຍເງິນອື່ນໆບໍ?</div> ປັດຈຸບັນຍັງບໍ່ມີ. ຄົນຈຳນວນຫຼາຍບໍ່ຕ້ອງການໃຫ້ຫ້ອງສະໝຸດແບບນີ້ມີຢູ່, ດັ່ງນັ້ນພວກເຮົາຕ້ອງລະມັດລະວັງ. ຖ້າທ່ານສາມາດຊ່ວຍພວກເຮົາຕັ້ງຄ່າວິທີການຈ່າຍເງິນອື່ນໆ (ທີ່ສະດວກກວ່າ) ຢ່າງປອດໄພ, ກະລຸນາຕິດຕໍ່ທີ່ %(email)s. ຄຳຖາມທີ່ພົບເລື້ອຍໆກ່ຽວກັບການບໍລິຈາກ ທ່ານມີ <a %(a_donation)s>ການບໍລິຈາກທີ່ມີຢູ່ແລ້ວ</a> ກຳລັງດຳເນີນການ. ກະລຸນາສຳເລັດຫຼືຍົກເລີກການບໍລິຈາກນັ້ນກ່ອນທີ່ຈະທຳການບໍລິຈາກໃໝ່. <a %(a_all_donations)s>ເບິ່ງການບໍລິຈາກທັງໝົດຂອງຂ້ອຍ</a> ສຳລັບການບໍລິຈາກເກີນ $5000 ກະລຸນາຕິດຕໍ່ພວກເຮົາໂດຍກົງທີ່ %(email)s. ພວກເຮົາຍິນດີຕ້ອນຮັບການບໍລິຈາກຈຳນວນຫຼາຍຈາກບຸກຄົນທີ່ມີຄວາມຮັ່ງມີ ຫຼື ສະຖາບັນ.  ຂໍໃຫ້ທ່ານຮູ້ວ່າ ໃນຂະນະທີ່ສະມາຊິກໃນໜ້ານີ້ແມ່ນ “ຕໍ່ເດືອນ”, ພວກເຂົາແມ່ນການບໍລິຈາກເທື່ອດຽວ (ບໍ່ມີການຕໍ່ເນື່ອງ). ຊົມ <a %(faq)s>ຄຳຖາມທີ່ພົບບ່ອຍກ່ຽວກັບການບໍລິຈາກ</a>. ຄັງເກັບຂໍ້ມູນຂອງ Anna ເປັນໂຄງການບໍ່ຫາກຳໄລ, ເປີດແຫຼ່ງທີ່ມາ, ແລະເປີດຂໍ້ມູນ. ໂດຍການບໍລິຈາກແລະການເປັນສະມາຊິກ, ທ່ານສະໜັບສະໜູນການດຳເນີນງານແລະການພັດທະນາຂອງພວກເຮົາ. ແກ່ສະມາຊິກທັງຫລາຍ: ຂອບໃຈທີ່ຊ່ວຍເຮົາໃຫ້ດຳເນີນຕໍ່ໄປ! ❤️ ສຳລັບຂໍ້ມູນເພີ່ມເຕີມ, ກະລຸນາກວດສອບ <a %(a_donate)s>ຄຳຖາມທີ່ພົບບ່ອຍກ່ຽວກັບການບໍລິຈາກ</a>. ເພື່ອກາຍເປັນສະມາຊິກ, ກະລຸນາ <a %(a_login)s>ເຂົ້າລະບົບຫຼືລົງທະບຽນ</a>. ຂອບໃຈສຳລັບການສະໜັບສະໜູນ! $%(cost)s / ເດືອນ ຖ້າທ່ານເຮັດຜິດພາດໃນລະຫວ່າງການຈ່າຍເງິນ, ພວກເຮົາບໍ່ສາມາດຄືນເງິນໄດ້, ແຕ່ພວກເຮົາຈະພະຍາຍາມເຮັດໃຫ້ຖືກຕ້ອງ. ຊອກຫາໜ້າ “Crypto” ໃນແອັບ PayPal ຫຼືເວັບໄຊທ໌ຂອງທ່ານ. ປົກກະຕິຢູ່ໃຕ້ “Finances”. ໄປທີ່ໜ້າ “Bitcoin” ໃນແອັບ PayPal ຫຼືເວັບໄຊທ໌ຂອງທ່ານ. ກົດປຸ່ມ “Transfer” %(transfer_icon)s, ແລະຕໍ່ມາ “Send”. Alipay Alipay 支付宝 / WeChat 微信 ບັດຂອງຂວັນ Amazon %(amazon)s ບັດຂອງຂວັນ ບັດທະນາຄານ ບັດທະນາຄານ (ໃຊ້ແອັບ) ບິນານซ์ ບັດເຄຣດິດ/ດີເບດ/Apple/Google (BMC) Cash App ບັດເຄຣດິດ/ບັດເດບິດ ບັດເຄຣດິດ/ບັດເດບິດ 2 ບັດເຄຣດິດ/ບັດເດບິດ (ສຳຮອງ) ຄຣິບໂຕ %(bitcoin_icon)s ບັດ / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (ປົກກະຕິ) Pix (Brazil) Revolut (ຊົ່ວຄາວບໍ່ມີ) WeChat ເລືອກເຫຼີຍທີ່ທ່ານຕ້ອງການ: ບໍລິຈາກໂດຍໃຊ້ບັດຂອງຂວັນ Amazon. <strong>ສຳຄັນ:</strong> ຕົວເລືອກນີ້ເປັນສຳລັບ %(amazon)s. ຖ້າທ່ານຕ້ອງການໃຊ້ເວັບໄຊອື່ນຂອງ Amazon, ກະລຸນາເລືອກມັນຂ້າງເທິງ. <strong>ສຳຄັນ:</strong> ເຮົາສະໜັບສະໜູນ Amazon.com ເທົ່ານັ້ນ, ບໍ່ໃຊ່ເວັບໄຊອື່ນຂອງ Amazon. ຕົວຢ່າງ, .de, .co.uk, .ca, ບໍ່ໄດ້ຮັບການສະໜັບສະໜູນ. ກະລຸນາຢ່າຂຽນຂໍ້ຄວາມຂອງທ່ານເອງ. ໃສ່ຈຳນວນທີ່ແມ່ນຈິງ: %(amount)s ຫມາຍເຫດວ່າເຮົາຈຳເປັນຕ້ອງປັບຈຳນວນໃຫ້ເປັນຈຳນວນທີ່ຜູ້ຂາຍຂອງເຮົາຮັບ (ຂັ້ນຕ່ຳ %(minimum)s). ບໍລິຈາກໂດຍໃຊ້ບັດເຄຣດິດ/ດີເບດ, ຜ່ານແອັບ Alipay (ຕິດຕັ້ງງ່າຍຫຼາຍ). ຕິດຕັ້ງແອັບ Alipay ຈາກ <a %(a_app_store)s>Apple App Store</a> ຫຼື <a %(a_play_store)s>Google Play Store</a>. ລົງທະບຽນໂດຍໃຊ້ເບີໂທລະສັບຂອງທ່ານ. ບໍ່ຈຳເປັນຕ້ອງໃຫ້ລາຍລະອຽດສ່ວນຕົວອື່ນໆ. <span %(style)s>1</span>ຕິດຕັ້ງແອັບ Alipay ສະຫນັບສະຫນູນ: Visa, MasterCard, JCB, Diners Club ແລະ Discover. ຊົມ <a %(a_alipay)s>ຄູ່ມືນີ້</a> ສຳລັບຂໍ້ມູນເພີ່ມເຕີມ. <span %(style)s>2</span>ເພີ່ມບັດທະນາຄານ ດ້ວຍ Binance, ທ່ານສາມາດຊື້ Bitcoin ດ້ວຍບັດເຄຣດິດ/ເດບິດ ຫລື ບັນຊີທະນາຄານ, ແລະຫລັງຈາກນັ້ນບໍລິຈາກ Bitcoin ໃຫ້ເຮົາ. ແນວນີ້ເຮົາສາມາດຢູ່ໃນຄວາມປອດໄພແລະບໍ່ມີການເຜີຍແພ່ເມື່ອຮັບການບໍລິຈາກຂອງທ່ານ. Binance ໃຊ້ໄດ້ເກືອບທຸກປະເທດ, ແລະສະໜັບສະໜູນທະນາຄານ ແລະ ບັດເຄຣດິດ/ເດບິດສ່ວນໃຫຍ່. ນີ້ເປັນຄຳແນະນຳຫລັກຂອງເຮົາໃນປັດຈຸບັນ. ເຮົາຂອບໃຈທີ່ທ່ານໃຊ້ເວລາຮຽນຮູ້ວິທີການບໍລິຈາກໂດຍໃຊ້ວິທີນີ້, ເພາະມັນຊ່ວຍເຮົາໄດ້ຫຼາຍ. ສຳລັບບັດເຄຣດິດ, ບັດເດບິດ, Apple Pay, ແລະ Google Pay, ເຮົາໃຊ້ “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). ໃນລະບົບຂອງພວກເຂົາ, ເຄື່ອງດື່ມ “coffee” ໜຶ່ງຄືກັບ $5, ດັ່ງນັ້ນການບໍລິຈາກຂອງທ່ານຈະຖືກປັບໃຫ້ເປັນຈຳນວນທີ່ໃກ້ຄຽງກັບຫຼາຍຂອງ 5. ບໍລິຈາກໂດຍໃຊ້ Cash App. ຖ້າທ່ານມີ Cash App, ນີ້ແມ່ນວິທີທີ່ງ່າຍທີ່ສຸດໃນການບໍລິຈາກ! ຫມາຍເຫດວ່າສຳລັບການເຮັດທຸລະກຳທີ່ຕ່ຳກວ່າ %(amount)s, Cash App ອາດຈະຄິດຄ່າທຳນຽມ %(fee)s. ສຳລັບ %(amount)s ຫລືຫຼາຍກວ່ານັ້ນ, ມັນຟຣີ! ບໍລິຈາກດ້ວຍບັດເຄຣດິດ ຫລື ບັດເດບິດ. ວິທີນີ້ໃຊ້ຜູ້ໃຫ້ບໍລິການສະກຸນເງິນດິຈິຕອນເປັນການແປງກາງ. ນີ້ອາດຈະສັບສົນໄດ້, ດັ່ງນັ້ນກະລຸນາໃຊ້ວິທີນີ້ເມື່ອວິທີການຈ່າຍເງິນອື່ນບໍ່ໄດ້ຜົນ. ມັນກໍບໍ່ໄດ້ໃຊ້ໄດ້ໃນທຸກປະເທດ. ພວກເຮົາບໍ່ສາມາດສະໜັບສະໜູນບັດເຄຣດິດ/ບັດເດບິດໂດຍກົງໄດ້, ເນື່ອງຈາກທະນາຄານບໍ່ຕ້ອງການເຮັດວຽກກັບພວກເຮົາ. ☹ ແຕ່ມີວິທີການຫຼາຍຢ່າງໃນການໃຊ້ບັດເຄຣດິດ/ບັດເດບິດໂດຍໃຊ້ວິທີການຈ່າຍເງິນອື່ນໆ: ດ້ວຍຄຣິບໂຕ ທ່ານສາມາດບໍລິຈາກໄດ້ໂດຍໃຊ້ BTC, ETH, XMR, ແລະ SOL. ໃຊ້ຕົວເລືອກນີ້ຖ້າທ່ານຄຸ້ນເຄີຍກັບຄຣິບໂຕແລ້ວ. ດ້ວຍຄຣິບໂຕ ທ່ານສາມາດບໍລິຈາກໄດ້ໂດຍໃຊ້ BTC, ETH, XMR, ແລະອື່ນໆ. ບໍລິການດ່ວນຂອງຄຣິບໂຕ ຖ້າທ່ານໃຊ້ຄຣິບໂຕເປັນຄັ້ງທຳອິດ, ພວກເຮົາແນະນຳໃຫ້ໃຊ້ %(options)s ເພື່ອຊື້ແລະບໍລິຈາກ Bitcoin (ຄຣິບໂຕເຄີນທີ່ເກົ່າແລະໃຊ້ງານຫຼາຍທີ່ສຸດ). ຫມາຍເຫດວ່າສຳລັບການບໍລິຈາກຂະໜາດນ້ອຍຄ່າທຳນຽມຂອງບັດເຄຣດິດອາດຈະລົບລ້າງສ່ວນຫຼຸດ %(discount)s%% ຂອງເຮົາ, ດັ່ງນັ້ນເຮົາແນະນຳໃຫ້ສະໝັກສະມາຊິກທີ່ຍາວກວ່າ. ບໍລິຈາກໂດຍໃຊ້ບັດເຄຣດິດ/ເດບິດ, PayPal, ຫລື Venmo. ທ່ານສາມາດເລືອກລະຫວ່າງສາມອັນນີ້ໃນໜ້າຕໍ່ໄປ. Google Pay ແລະ Apple Pay ອາດຈະໃຊ້ໄດ້ເຊັ່ນກັນ. ຫມາຍເຫດວ່າສຳລັບການບໍລິຈາກຂະໜາດນ້ອຍຄ່າທຳນຽມສູງ, ດັ່ງນັ້ນເຮົາແນະນຳໃຫ້ສະໝັກສະມາຊິກທີ່ຍາວກວ່າ. ເພື່ອບໍລິຈາກໂດຍໃຊ້ PayPal US, ພວກເຮົາຈະໃຊ້ PayPal Crypto, ທີ່ອະນຸຍາດໃຫ້ພວກເຮົາຢູ່ໃນສະພາບບໍ່ປະຈຳຕົວ. ພວກເຮົາຊື່ນຊົມທີ່ທ່ານໃຊ້ເວລາຮຽນຮູ້ວິທີການບໍລິຈາກໂດຍໃຊ້ວິທີນີ້, ເພາະມັນຊ່ວຍເຮົາໄດ້ຫຼາຍ. ບໍລິຈາກໂດຍໃຊ້ PayPal. ບໍລິຈາກໂດຍໃຊ້ບັນຊີ PayPal ປົກກະຕິຂອງທ່ານ. ບໍລິຈາກໂດຍໃຊ້ Revolut. ຖ້າທ່ານມີ Revolut, ນີ້ແມ່ນວິທີທີ່ງ່າຍທີ່ສຸດໃນການບໍລິຈາກ! ວິທີການຈ່າຍເງິນນີ້ອະນຸຍາດເພີ່ມສູງສຸດຂອງ %(amount)s. ກະລຸນາເລືອກໄລຍະເວລາຫຼືວິທີການຈ່າຍເງິນທີ່ແຕກຕ່າງ. ວິທີການຈ່າຍເງິນນີ້ຕ້ອງການຂັ້ນຕ່ຳຂອງ %(amount)s. ກະລຸນາເລືອກໄລຍະເວລາຫຼືວິທີການຈ່າຍເງິນທີ່ແຕກຕ່າງ. ບິນານซ์ Coinbase Kraken ກະລຸນາເລືອກວິທີການຈ່າຍເງິນ. “ຮັບອຸປະທານ torrent”: ຊື່ຜູ້ໃຊ້ຂອງທ່ານ ຫຼື ຂໍ້ຄວາມໃນຊື່ໄຟລ໌ torrent <div %(div_months)s>ທຸກ 12 ເດືອນຂອງການເປັນສະມາຊິກ</div> ຊື່ຜູ້ໃຊ້ຂອງທ່ານ ຫຼື ການເອີ້ນຊື່ທີ່ບໍ່ລະບຸຕົວຕົນໃນການຂອງຂວັນ ເຂົ້າເຖິງຄຸນສົມບັດໃໝ່ກ່ອນໃຜ Telegram ພິເສດທີ່ມີການອັບເດດທີ່ຢູ່ເບື້ອງຫຼັງ %(number)s ດາວໂຫລດໄວຕໍ່ມື້ ຖ້າທ່ານບໍລິຈາກໃນເດືອນນີ້! ການເຂົ້າເຖິງ <a %(a_api)s>JSON API</a> ສະຖານະຕຳນານໃນການຮັກສາຄວາມຮູ້ແລະວັດທະນະທຳຂອງມະນຸດ ສິດປະໂຫຍດກ່ອນໜ້ານີ້, ບວກກັບ: ໄດ້ຮັບ <strong>%(percentage)s%% ໂບນັດດາວໂຫລດ</strong> ໂດຍ <a %(a_refer)s>ແນະນຳໝູ່</a>. ເອກະສານ SciDB <strong>ບໍ່ຈຳກັດ</strong> ບໍ່ຕ້ອງຢືນຢັນ ເມື່ອຖາມຄຳຖາມກ່ຽວກັບບັນຊີຫຼືການບໍລິຈາກ, ເພີ່ມ ID ບັນຊີຂອງທ່ານ, ຮູບພາບຫນ້າຈໍ, ໃບຮັບເງິນ, ຂໍ້ມູນທີ່ມີຢູ່ທັງໝົດ. ພວກເຮົາກວດສອບອີເມວຂອງພວກເຮົາທຸກ 1-2 ອາທິດ, ດັ່ງນັ້ນການບໍ່ມີຂໍ້ມູນນີ້ຈະທຳໃຫ້ການແກ້ໄຂລ່າຊ້າລົງ. ເພື່ອໄດ້ຮັບການດາວໂຫລດເພີ່ມເຕີມ, <a %(a_refer)s>ແນະນຳໝູ່ຂອງທ່ານ</a>! ພວກເຮົາເປັນທີມອາສາສະໝັກຂະຫນາດນ້ອຍ. ອາດຈະໃຊ້ເວລາ 1-2 ອາທິດໃນການຕອບກັບ. ຂໍເຫັນວ່າຊື່ບັນຊີຫຼືຮູບພາບອາດຈະເບິ່ງແປກໆ. ບໍ່ຕ້ອງກັງວົນ! ບັນຊີເຫຼົ່ານີ້ຖືກຄຸ້ມຄອງໂດຍຄູ່ຮ່ວມບໍລິຈາກຂອງພວກເຮົາ. ບັນຊີຂອງພວກເຮົາບໍ່ໄດ້ຖືກແຮກ. ບໍລິຈາກ <span %(span_cost)s></span> <span %(span_label)s></span> ສຳລັບ 12 ເດືອນ “%(tier_name)s” ສຳລັບ 1 ເດືອນ “%(tier_name)s” ສຳລັບ 24 ເດືອນ “%(tier_name)s” ສຳລັບ 3 ເດືອນ “%(tier_name)s” ສຳລັບ 48 ເດືອນ “%(tier_name)s” ສຳລັບ 6 ເດືອນ “%(tier_name)s” ສຳລັບ 96 ເດືອນ “%(tier_name)s” ທ່ານຍັງສາມາດຍົກເລີກການບໍລິຈາກໄດ້ໃນລະຫວ່າງການຊຳລະເງິນ. ຄລິກປຸ່ມບໍລິຈາກເພື່ອຢືນຢັນການບໍລິຈາກນີ້. <strong>ໝາຍເຫດສຳຄັນ:</strong> ລາຄາຄຣິບໂຕມີການຜັນຜວນຢ່າງຫນັກ, ບາງຄັ້ງອາດຈະຫຼຸດລົງຫຼາຍກວ່າ 20%% ໃນບໍ່ກີ່ນາທີ. ນີ້ຍັງຫນ້ອຍກວ່າຄ່າທຳນຽມທີ່ເຮົາຕ້ອງຈ່າຍໃຫ້ກັບຜູ້ໃຫ້ບໍລິການການຈ່າຍເງິນຫຼາຍໆ, ທີ່ມັກຈະເອົາ 50-60%% ສຳລັບການເຮັດວຽກກັບ "ມູນນິທິເງົາ" ເຊັ່ນເຮົາ. <u>ຖ້າທ່ານສົ່ງໃບຮັບເງິນທີ່ມີລາຄາຕົ້ນສະບັບທີ່ທ່ານຈ່າຍ, ເຮົາຈະຍັງຄົງເຄຣດບັນຊີຂອງທ່ານສຳລັບການເປັນສະມາຊິກທີ່ເລືອກ</u> (ຕາມທີ່ໃບຮັບເງິນບໍ່ເກີນບໍ່ກີ່ຊົ່ວໂມງ). ເຮົາຂອບໃຈຫຼາຍໆທີ່ທ່ານຍອມຮັບສິ່ງນີ້ເພື່ອສະໜັບສະໜູນເຮົາ! ❤️ ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ໃໝ່ແລະລອງອີກຄັ້ງ. <span %(span_circle)s>1</span>ຊື້ Bitcoin ຜ່ານ Paypal <span %(span_circle)s>2</span>ໂອນ Bitcoin ໄປທີ່ຢູ່ຂອງພວກເຮົາ ✅ ກຳລັງປ່ຽນເສັ້ນທາງໄປຫາໜ້າບໍລິຈາກ… ບໍລິຈາກ ກະລຸນາລໍຖ້າຢ່າງໜ້ອຍ <span %(span_hours)s>24 ຊົ່ວໂມງ</span> (ແລະຟື້ນຟູໜ້ານີ້) ກ່ອນຕິດຕໍ່ພວກເຮົາ. ຖ້າທ່ານຕ້ອງການບໍລິຈາກ (ຈຳນວນໃດກໍໄດ້) ໂດຍບໍ່ຕ້ອງເປັນສະມາຊິກ, ກະລຸນາໃຊ້ທີ່ຢູ່ Monero (XMR) ນີ້: %(address)s. ຫຼັງຈາກສົ່ງບັດຂອງຂວັນຂອງທ່ານ, ລະບົບອັດຕະໂນມັດຂອງພວກເຮົາຈະຢືນຢັນໃນຫຼາຍນາທີ. ຖ້າມັນບໍ່ໄດ້ຜົນ, ລອງສົ່ງບັດຂອງຂວັນອີກຄັ້ງ (<a %(a_instr)s>ຄຳແນະນຳ</a>). ຖ້າມັນຍັງບໍ່ໄດ້ຜົນອີກ, ກະລຸນາສົ່ງອີເມວຫາພວກເຮົາແລະ Anna ຈະກວດສອບມັນເອງ (ອາດຈະໃຊ້ເວລາຫຼາຍວັນ), ແລະຢ່າລືມກ່າວຫາກທ່ານໄດ້ລອງສົ່ງອີກຄັ້ງແລ້ວ. ຕົວຢ່າງ: ກະລຸນາໃຊ້ <a %(a_form)s>ແບບຟອມທາງການຂອງ Amazon.com</a> ເພື່ອສົ່ງບັດຂອງຂວັນຂອງ %(amount)s ໃຫ້ທີ່ຢູ່ອີເມວດ້ານລຸ່ມນີ້. ອີເມວຜູ້ຮັບ “To” ໃນແບບຟອມ: ບັດຂອງຂວັນ Amazon ພວກເຮົາບໍ່ສາມາດຮັບວິທີການອື່ນໆຂອງບັດຂອງຂວັນ, <strong>ສົ່ງໂດຍກົງຈາກແບບຟອມທາງການທີ່ Amazon.com</strong>. ພວກເຮົາບໍ່ສາມາດຄືນບັດຂອງຂວັນຂອງທ່ານໄດ້ຖ້າທ່ານບໍ່ໃຊ້ແບບຟອມນີ້. ໃຊ້ພຽງແຕ່ຄັ້ງດຽວ. ພິເສດສຳລັບບັນຊີຂອງທ່ານ, ຢ່າແບ່ງປັນ. ລໍຖ້າບັດຂອງຂວັນ... (ຟື້ນຟູໜ້າເວັບເພື່ອກວດສອບ) ເປີດ <a %(a_href)s>ໜ້າບໍລິຈາກ QR-code</a>. ສະແກນ QR code ດ້ວຍແອັບ Alipay, ຫຼືກົດປຸ່ມເພື່ອເປີດແອັບ Alipay. ກະລຸນາອົດທົນ; ໜ້າອາດຈະໃຊ້ເວລາບາງຄັ້ງໃນການໂຫຼດເພາະຢູ່ປະເທດຈີນ. <span %(style)s>3</span>ບໍລິຈາກ (ສະແກນ QR code ຫຼືກົດປຸ່ມ) ຊື້ເຫຼີຍ PYUSD ຜ່ານ PayPal ຊື້ Bitcoin (BTC) ຜ່ານ Cash App ຊື້ເພີ່ມອີກໜ້ອຍ (ພວກເຮົາແນະນຳ %(more)s ເພີ່ມ) ກ່ຽວກັບຈຳນວນທີ່ທ່ານກຳລັງບໍລິຈາກ (%(amount)s), ເພື່ອຄຸ້ມຄອງຄ່າທຳນຽມການເຮັດທຸລະກຳ. ທ່ານຈະເກັບເງິນທີ່ເຫຼືອໄວ້ໄດ້. ໄປທີ່ໜ້າ “Bitcoin” (BTC) ໃນ Cash App. ໂອນ Bitcoin ໄປທີ່ທີ່ຢູ່ຂອງພວກເຮົາ ສຳລັບການບໍລິຈາກນ້ອຍ (ຕ່ຳກວ່າ $25) ທ່ານອາດຈະຕ້ອງໃຊ້ Rush ຫຼື Priority. ຄລິກປຸ່ມ “Send bitcoin” ເພື່ອເຮັດການ “ຖອນ”. ປ່ຽນຈາກໂດລາເປັນ BTC ໂດຍການກົດໄອຄອນ %(icon)s. ປ້ອນຈຳນວນ BTC ດ້ານລຸ່ມແລະຄລິກ “Send”. ເບິ່ງ <a %(help_video)s>ວິດີໂອນີ້</a> ຖ້າທ່ານຕິດຂັດ. ບໍລິການດ່ວນແມ່ນສະດວກ, ແຕ່ມີຄ່າທຳນຽມສູງກວ່າ. ທ່ານສາມາດໃຊ້ອັນນີ້ແທນການແລກປ່ຽນຄຣິບໂຕຖ້າທ່ານຕ້ອງການບໍລິຈາກຈຳນວນຫຼາຍໃນເວລາທີ່ຮວດໄວ ແລະບໍ່ຄິດຄ່າທຳນຽມ $5-10. ຂໍໃຫ້ສົ່ງຈຳນວນຄຣິບໂຕທີ່ລະບຸໃນໜ້າບໍລິຈາກ, ບໍ່ໃຊ່ຈຳນວນໃນ $USD. ມິດຊະນັ້ນຄ່າທຳນຽມຈະຖືກຫັກ ແລະພວກເຮົາບໍ່ສາມາດດຳເນີນການສະມາຊິກຂອງທ່ານໂດຍອັດຕະໂນມັດໄດ້. ບາງຄັ້ງການຢືນຢັນອາດຈະໃຊ້ເວລາສູງສຸດ 24 ຊົ່ວໂມງ, ດັ່ງນັ້ນຈົ່ງແນ່ໃຈວ່າໄດ້ຟື້ນຟູໜ້ານີ້ (ແມ່ນວ່າມັນຈະໝົດອາຍຸແລ້ວ). ຄຳແນະນຳບັດເຄຣດິດ / ດີເບດ ບໍລິຈາກຜ່ານໜ້າບັດເຄຣດິດ / ດີເບດຂອງພວກເຮົາ ບາງຂັ້ນຕອນໄດ້ກ່າວເຖິງກະເປົາເງິນຄຣິບໂຕ, ແຕ່ບໍ່ຕ້ອງກັງວົນ, ທ່ານບໍ່ຈຳເປັນຕ້ອງຮຽນຮູ້ກ່ຽວກັບຄຣິບໂຕສຳລັບນີ້. %(coin_name)s ຄຳແນະນຳ ສະແກນລະຫັດ QR ນີ້ກັບ Crypto Wallet Wallet ຂອງທ່ານເພື່ອຕື່ມຂໍ້ມູນໃສ່ໃນລາຍລະອຽດການຈ່າຍເງິນ ສະແກນລະຫັດ QR ເພື່ອຈ່າຍ ພວກເຮົາສະໜັບສະໜູນເພີງແຕ່ສະຕານດາດຂອງເຫຼັຽນຄຣິບໂຕເທົ່ານັ້ນ, ບໍ່ມີເຄືອຂ່າຍຫຼືເວີຊັນທີ່ແປກໃຫມ່. ອາດຈະໃຊ້ເວລາຫາກວ່າໜຶ່ງຊົ່ວໂມງເພື່ອຢືນຢັນການເຮັດທຸລະກຳ, ຂຶ້ນກັບເຫຼັຽນ. ບໍລິຈາກ %(amount)s ທີ່ <a %(a_page)s>ໜ້ານີ້</a>. ການບໍລິຈາກນີ້ໄດ້ໝົດອາຍຸແລ້ວ. ກະລຸນາຍົກເລີກແລະສ້າງໃໝ່. ຖ້າທ່ານໄດ້ຈ່າຍແລ້ວ: ແມ່ນແລ້ວ, ຂ້ອຍໄດ້ສົ່ງອີເມວໃບຮັບເງິນແລ້ວ ຖ້າອັດຕາແລກປ່ຽນຄຣິບໂຕເປຼີ່ມປ່ຽນແປງໃນລະຫວ່າງການທຳທຸລະກຳ, ກະລຸນາໃສ່ໃບຮັບເງິນທີ່ສະແດງອັດຕາແລກປ່ຽນເດີມ. ພວກເຮົາຂອບໃຈທີ່ທ່ານເອົາໃຈໃສ່ໃນການໃຊ້ຄຣິບໂຕ, ມັນຊ່ວຍເຮົາໄດ້ຫຼາຍ! ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ໃໝ່ແລະລອງອີກຄັ້ງ. <span %(span_circle)s>%(circle_number)s</span>ສົ່ງອີເມວໃຫ້ພວກເຮົາພ້ອມໃບຮັບເງິນ ຖ້າທ່ານພົບບັນຫາໃດໆ, ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(email)s ແລະລວມຂໍ້ມູນໃຫ້ຫຼາຍທີ່ສຸດ (ເຊັ່ນຮູບພາບຫນ້າຈໍ). ✅ ຂອບໃຈສຳລັບການບໍລິຈາກຂອງທ່ານ! Anna ຈະເຮັດການເປີດສະມາຊິກຂອງທ່ານໃນຫຼາຍມື້. ສົ່ງໃບຮັບເງິນ ຫຼື ຮູບພາບຫນ້າຈໍໄປທີ່ທີ່ຢູ່ການຢືນຢັນສ່ວນຕົວຂອງທ່ານ: ເມື່ອທ່ານໄດ້ສົ່ງອີເມວໃບຮັບເງິນແລ້ວ, ກົດປຸ່ມນີ້, ເພື່ອໃຫ້ Anna ກວດສອບມື້ນີ້ (ອາດຈະໃຊ້ເວລາຫຼາຍມື້): ສົ່ງໃບຮັບເງິນຫຼືຮູບພາບຫນ້າຈໍໄປທີ່ທີ່ຢູ່ການຢືນຢັນສ່ວນຕົວຂອງທ່ານ. ຢ່າໃຊ້ທີ່ຢູ່ອີເມວນີ້ສຳລັບການບໍລິຈາກຜ່ານ PayPal. ຍົກເລີກ ແມ່ນ, ກະລຸນາຍົກເລີກ ທ່ານແນ່ໃຈບໍ່ວ່າຕ້ອງການຍົກເລີກ? ຢ່າຍົກເລີກຖ້າທ່ານໄດ້ຈ່າຍແລ້ວ. ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ໃໝ່ແລະລອງອີກຄັ້ງ. ສ້າງການບໍລິຈາກໃໝ່ ✅ ການບໍລິຈາກຂອງທ່ານໄດ້ຖືກຍົກເລີກແລ້ວ. ວັນທີ: %(date)s ລະຫັດ: %(id)s ສັ່ງຄືນ ສະຖານະ: <span %(span_label)s>%(label)s</span> ລວມ: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ເດືອນ ສຳລັບ %(duration)s ເດືອນ, ລວມທັງ %(discounts)s%% ສ່ວນຫຼຸດ)</span> ລວມ: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ເດືອນ ສຳລັບ %(duration)s ເດືອນ)</span> 1. ປ້ອນອີເມວຂອງທ່ານ. 2. ເລືອກວິທີການຈ່າຍເງິນຂອງທ່ານ. 3. ເລືອກວິທີການຈ່າຍເງິນອີກຄັ້ງ. 4. ເລືອກ “ກະເປົາເງິນທີ່ເຮົາເປີດໃຊ້ເອງ”. 5. ກົດ “ຂ້ອຍຢືນຢັນການເປັນເຈົ້າຂອງ”. ທ່ານຄວນໄດ້ຮັບໃບຮັບເງິນທາງອີເມວ. ກະລຸນາສົ່ງນັ້ນມາໃຫ້ພວກເຮົາ, ແລະພວກເຮົາຈະຢືນຢັນການບໍລິຈາກຂອງທ່ານໃຫ້ໄວທີ່ສຸດ. (ທ່ານອາດຕ້ອງການຍົກເລີກແລະສ້າງການບໍລິຈາກໃໝ່) ຄຳແນະນຳການຈ່າຍເງິນໄດ້ໝົດອາຍຸແລ້ວ. ຖ້າທ່ານຕ້ອງການບໍລິຈາກອີກຄັ້ງ, ໃຊ້ປຸ່ມ "ສັ່ງຄືນ" ທາງເທິງ. ທ່ານໄດ້ຈ່າຍແລ້ວ. ຖ້າທ່ານຕ້ອງການທົດສອບຄຳແນະນຳການຈ່າຍເງິນອີກຄັ້ງ, ກົດທີ່ນີ້: ສະແດງຄຳແນະນຳການຈ່າຍເງິນເກົ່າ ຖ້າໜ້າບໍລິຈາກຖືກປິດກັ້ນ, ລອງໃຊ້ການເຊື່ອມຕໍ່ອິນເຕີເນັດທີ່ແຕກຕ່າງ (ຕົວຢ່າງເຊັ່ນ VPN ຫຼື ອິນເຕີເນັດໂທລະສັບ). ໂຊກບໍ່ດີ, ໜ້າ Alipay ມັກຈະເຂົ້າເຖິງໄດ້ເທົ່ານັ້ນຈາກ <strong>ຈີນແຜ່ນດິນໃຫຍ່</strong>. ທ່ານອາດຈະຕ້ອງປິດ VPN ຊົ່ວຄາວ, ຫຼືໃຊ້ VPN ໄປທີ່ຈີນແຜ່ນດິນໃຫຍ່ (ຫຼືຮ່ວມກັບຮົງກົງກໍເຮັດໄດ້ບາງຄັ້ງ). <span %(span_circle)s>1</span>ບໍລິຈາກຜ່ານ Alipay ບໍລິຈາກຈຳນວນທັງໝົດຂອງ %(total)s ໂດຍໃຊ້ <a %(a_account)s>ບັນຊີ Alipay ນີ້</a> ຄຳແນະນຳ Alipay <span %(span_circle)s>1</span>ໂອນໄປຫາບັນຊີຄຣິບໂຕຂອງເຮົາ ບໍລິຈາກຈຳນວນທັງໝົດຂອງ %(total)s ໄປຫາທີ່ຢູ່ເຫຼົ່ານີ້: ຄຳແນະນຳຄຣິບໂຕ ປະຕິບັດຕາມຄຳແນະນຳເພື່ອຊື້ Bitcoin (BTC). ທ່ານຕ້ອງການຊື້ຈຳນວນທີ່ທ່ານຕ້ອງການບໍລິຈາກ, %(total)s. ໃສ່ຢູ່ Bitcoin (BTC) ຂອງພວກເຮົາເປັນຜູ້ຮັບ, ແລະປະຕິບັດຕາມຄຳແນະນຳເພື່ອສົ່ງການບໍລິຈາກຂອງທ່ານ %(total)s: <span %(span_circle)s>1</span>ບໍລິຈາກຜ່ານ Pix ບໍລິຈາກຈຳນວນທັງໝົດ %(total)s ໂດຍໃຊ້ <a %(a_account)s>ບັນຊີ Pix ນີ້ ຄຳແນະນຳ Pix <span %(span_circle)s>1</span>ບໍລິຈາກຜ່ານ WeChat ບໍລິຈາກຈຳນວນທັງໝົດ %(total)s ໂດຍໃຊ້ <a %(a_account)s>ບັນຊີ WeChat ນີ້</a> ຄຳແນະນຳ WeChat ໃຊ້ບໍລິການ “ບັດເຄຣດິດເປັນ Bitcoin” ດ່ວນທີ່ລະບຸດ້ານລຸ່ມນີ້, ທີ່ໃຊ້ເວລາພຽງບໍ່ກີ່ນາທີ: ທີ່ຢູ່ BTC / Bitcoin (ກະເປົາພາຍນອກ): ຈຳນວນ BTC / Bitcoin: ກອກລາຍລະອຽດຕໍ່ໄປນີ້ໃນແບບຟອມ: ຖ້າຂໍ້ມູນໃດໆນີ້ບໍ່ທັນສະໄໝ, ກະລຸນາສົ່ງອີເມວຫາພວກເຮົາເພື່ອແຈ້ງໃຫ້ຮູ້. ກະລຸນາໃຊ້ <span %(underline)s>ຈຳນວນທີ່ແນ່ນອນນີ້</span>. ຄ່າໃຊ້ຈ່າຍທັງໝົດຂອງທ່ານອາດຈະສູງກວ່າເພາະຄ່າທຳນຽມບັດເຄຣດິດ. ສຳລັບຈຳນວນນ້ອຍນີ້ອາດຈະຫຼາຍກວ່າສ່ວນຫຼຸດຂອງພວກເຮົາ, ໂຊກບໍ່ດີ. (ຂັ້ນຕ່ຳ: %(minimum)s) (ຂັ້ນຕ່ຳ: %(minimum)s) (ຂັ້ນຕ່ຳ: %(minimum)s) (ຂັ້ນຕ່ຳ: %(minimum)s, ບໍ່ຕ້ອງຢືນຢັນສຳລັບທຸລະກຳຄັ້ງທຳອິດ) (ຂັ້ນຕ່ຳ: %(minimum)s) (ຂັ້ນຕ່ຳ: %(minimum)s ຂຶ້ນກັບປະເທດ, ບໍ່ຕ້ອງຢືນຢັນສຳລັບທຸລະກຳຄັ້ງທຳອິດ) ປະຕິບັດຕາມຄຳແນະນຳເພື່ອຊື້ເຫຼີຍ PYUSD (PayPal USD). ຊື້ເພີ່ມອີກໜ້ອຍ (ພວກເຮົາແນະນຳ %(more)s ເພີ່ມເຕີມ) ຈາກຈຳນວນທີ່ທ່ານກຳລັງບໍລິຈາກ (%(amount)s) ເພື່ອຄຸ້ມຄອງຄ່າທຳນຽມການເຮັດທຸລະກຳ. ທ່ານຈະເກັບເງິນທີ່ເຫຼືອໄວ້ໃຊ້ເອງ. ໄປທີ່ໜ້າ “PYUSD” ໃນແອັບ PayPal ຫຼືເວັບໄຊທ໌. ກົດປຸ່ມ “Transfer” %(icon)s, ແລະຕໍ່ມາ “Send”. ອັບເດດສະຖານະ ເພື່ອຕັ້ງເວລາໃໝ່, ສ້າງການບໍລິຈາກໃໝ່ງ່າຍໆ. ຈົ່ງແນ່ໃຈໃຊ້ຈຳນວນ BTC ຂ້າງລຸ່ມ, <em>ບໍ່ໃຊ້</em> ເອີໂຣ ຫຼື ໂດລາ, ມິຊັ່ນນັ້ນພວກເຮົາຈະບໍ່ໄດ້ຮັບຈຳນວນທີ່ຖືກຕ້ອງ ແລະບໍ່ສາມາດຢືນຢັນການເປັນສະມາຊິກຂອງທ່ານໂດຍອັດຕະໂນມັດ. ຊື້ Bitcoin (BTC) ທີ່ Revolut ຊື້ເພີ່ມອີກໜ້ອຍ (ພວກເຮົາແນະນຳ %(more)s ເພີ່ມ) ກວ່າຈຳນວນທີ່ທ່ານກຳລັງບໍລິຈາກ (%(amount)s) ເພື່ອຄຸ້ມຄ່າຄ່າທຳນຽມການເຮັດທຸລະກຳ. ທ່ານຈະເກັບເງິນທີ່ເຫຼືອໄວ້ໄດ້. ໄປທີ່ໜ້າ “Crypto” ໃນ Revolut ເພື່ອຊື້ Bitcoin (BTC). ໂອນ Bitcoin ໄປທີ່ທີ່ຢູ່ຂອງພວກເຮົາ ສຳລັບການບໍລິຈາກນ້ອຍ (ຕ່ຳກວ່າ $25) ທ່ານອາດຈະຕ້ອງໃຊ້ Rush ຫຼື Priority. ຄລິກປຸ່ມ “Send bitcoin” ເພື່ອເຮັດ “withdrawal”. ປ່ຽນຈາກຢູໂຣເປັນ BTC ໂດຍການກົດທີ່ໄອຄອນ %(icon)s. ປ້ອນຈຳນວນ BTC ດ້ານລຸ່ມແລະຄລິກ “Send”. ເບິ່ງ <a %(help_video)s>ວິດີໂອນີ້</a> ຖ້າທ່ານຕິດຂັດ. ສະຖານະ: 1 2 ຄູ່ມືຂັ້ນຕອນ ເບິ່ງຄູ່ມືຂັ້ນຕອນດ້ານລຸ່ມນີ້. ມິດຊະນັ້ນທ່ານອາດຈະຖືກລັອກອອກຈາກບັນຊີນີ້! ຖ້າທ່ານຍັງບໍ່ໄດ້ທຳ, ຂຽນລະຫັດລັບຂອງທ່ານສຳລັບການເຂົ້າລະບົບ: ຂອບໃຈສຳລັບການບໍລິຈາກຂອງທ່ານ! ເວລາທີ່ເຫຼືອ: ການບໍລິຈາກ ໂອນ %(amount)s ໃຫ້ %(account)s ລໍຖ້າການຢືນຢັນ (ຟື້ນຟູໜ້າເພື່ອກວດສອບ)… ລໍຖ້າການໂອນ (ຟື້ນຟູໜ້າເພື່ອກວດສອບ)… ກ່ອນໜ້ານີ້ ການດາວໂຫຼດໄວໃນ 24 ຊົ່ວໂມງຫຼ້າສຸດນັບເປັນສ່ວນໜຶ່ງຂອງຂີດຈຳກັດປະຈຳວັນ. ການດາວໂຫຼດຈາກເຊີບເວີທີ່ໄວຖືກໝາຍເຫດໂດຍ %(icon)s. 18 ຊົ່ວໂມງຜ່ານມາ ຍັງບໍ່ມີໄຟລ໌ທີ່ດາວໂຫຼດ. ໄຟລ໌ທີ່ດາວໂຫຼດບໍ່ໄດ້ຖືກສະແດງສາທາລະນະ. ເວລາທັງໝົດເປັນເວລາ UTC. ໄຟລ໌ທີ່ດາວໂຫຼດ ຖ້າທ່ານດາວໂຫຼດໄຟລ໌ທີ່ມີທັງການດາວໂຫຼດໄວແລະຊ້າ, ມັນຈະສະແດງຂຶ້ນສອງຄັ້ງ. ຢ່າກັງວົນເກີນໄປ, ມີຄົນຫຼາຍດາວໂຫລດຈາກເວັບໄຊທ໌ທີ່ເຊື່ອມໂຍງກັບພວກເຮົາ, ແລະມັນຫາຍາກຫຼາຍທີ່ຈະເກີດບັນຫາ. ແຕ່ເພື່ອຄວາມປອດໄພ, ພວກເຮົາແນະນຳໃຫ້ໃຊ້ VPN (ທີ່ຕ້ອງຈ່າຍ), ຫຼື <a %(a_tor)s>Tor</a> (ຟຣີ). ຂ້ອຍໄດ້ດາວໂຫລດ 1984 ໂດຍ George Orwell, ຕຳຫຼວດຈະມາທີ່ປະຕູຂ້ອຍບໍ່? ທ່ານແມ່ນແອນນາ! ແອນນາແມ່ນໃຜ? ພວກເຮົາມີ JSON API ທີ່ສະເຫນີໃຫ້ສະມາຊິກ, ເພື່ອໄດ້ຮັບ URL ດາວໂຫລດທີ່ໄວ: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (ເອກະສານຢູ່ໃນ JSON ເອງ). ສຳລັບການນຳໃຊ້ອື່ນໆ, ເຊັ່ນການຄົ້ນຫາທຸກໄຟລ໌ຂອງພວກເຮົາ, ການສ້າງການຄົ້ນຫາທີ່ປັບແຕ່ງເອງ, ແລະອື່ນໆ, ພວກເຮົາແນະນຳ <a %(a_generate)s>ການສ້າງ</a> ຫຼື <a %(a_download)s>ດາວໂຫລດ</a> ຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB ຂອງພວກເຮົາ. ຂໍ້ມູນດິບສາມາດຖືກສຳຫຼວດແບບມື້ເອງ <a %(a_explore)s>ຜ່ານໄຟລ໌ JSON</a>. ລາຍການ torrent ດິບຂອງພວກເຮົາສາມາດດາວໂຫລດໄດ້ເປັນ <a %(a_torrents)s>JSON</a> ເຊັ່ນກັນ. ທ່ານມີ API ບໍ່? ພວກເຮົາບໍ່ໄດ້ເກັບຮັກສາວັດຖຸທີ່ມີລິຂະສິດໃດໆຢູ່ທີ່ນີ້. ພວກເຮົາເປັນເຄື່ອງມືຄົ້ນຫາ, ແລະດັ່ງນັ້ນຈຶ່ງຈະດັດສະນີຂໍ້ມູນທີ່ມີຢູ່ແລ້ວໃນທີ່ສາທາລະນະເທົ່ານັ້ນ. ເມື່ອທ່ານດາວໂຫລດຈາກແຫຼ່ງພາຍນອກເຫຼົ່ານັ້ນ, ພວກເຮົາແນະນຳໃຫ້ທ່ານກວດສອບກົດໝາຍໃນຂອບເຂດຂອງທ່ານກ່ຽວກັບສິ່ງທີ່ຖືກຕ້ອງ. ພວກເຮົາບໍ່ຮັບຜິດຊອບສຳລັບເນື້ອຫາທີ່ຖືກເກັບຮັກສາໂດຍຜູ້ອື່ນ. ຖ້າທ່ານມີຄຳຮ້ອງທຸກກ່ຽວກັບສິ່ງທີ່ທ່ານເຫັນຢູ່ທີ່ນີ້, ທາງທີ່ດີທີ່ສຸດຂອງທ່ານຄືການຕິດຕໍ່ເວັບໄຊດັ້ງເດີມ. ພວກເຮົາດຶງຂໍ້ມູນຂອງພວກເຂົາເຂົ້າມາໃນຖານຂໍ້ມູນຂອງພວກເຮົາຢ່າງສະໝໍ່ສະເມີ. ຖ້າທ່ານຄິດວ່າທ່ານມີຄຳຮ້ອງທຸກ DMCA ທີ່ຖືກຕ້ອງທີ່ພວກເຮົາຄວນຕອບສະໜອງ, ກະລຸນາກອກແບບຟອມ <a %(a_copyright)s>ຄຳຮ້ອງທຸກ DMCA / ລິຂະສິດ</a>. ພວກເຮົາໃຫ້ຄວາມສຳຄັນກັບຄຳຮ້ອງທຸກຂອງທ່ານ, ແລະຈະຕອບກັບທ່ານໃຫ້ໄວທີ່ສຸດ. ຂ້ອຍຈະລາຍງານການລະເມີດລິຂະສິດໄດ້ຢ່າງໃດ? ນີ້ແມ່ນປຶ້ມບາງເລື່ອງທີ່ມີຄວາມສຳຄັນພິເສດຕໍ່ໂລກຂອງຫ້ອງສະໝຸດເງາແລະການຮັກສາຂໍ້ມູນດິຈິຕອນ: ປຶ້ມທີ່ທ່ານມັກຄືຫຍັງ? ພວກເຮົາຍັງຢາກຈະຂໍເຕືອນທຸກຄົນວ່າລະຫັດແລະຂໍ້ມູນທັງໝົດຂອງພວກເຮົາເປັນເປີດແຫຼ່ງທັງໝົດ. ນີ້ເປັນສິ່ງທີ່ເປັນເອກະລັກສຳລັບໂຄງການເຊັ່ນພວກເຮົາ — ພວກເຮົາບໍ່ຮູ້ຈັກໂຄງການອື່ນໃດທີ່ມີຖານຂໍ້ມູນຂະໜາດໃຫຍ່ທີ່ເປັນເປີດແຫຼ່ງດ້ວຍ. ພວກເຮົາຍິນດີຕ້ອນຮັບທຸກຄົນທີ່ຄິດວ່າພວກເຮົາດຳເນີນໂຄງການບໍ່ດີໃຫ້ເອົາລະຫັດແລະຂໍ້ມູນຂອງພວກເຮົາໄປສ້າງຫ້ອງສະໝຸດເງາຂອງຕົນເອງ! ພວກເຮົາບໍ່ໄດ້ກ່າວນີ້ດ້ວຍຄວາມຫມັ້ນໄສຫຼືສິ່ງໃດ — ພວກເຮົາຄິດວ່ານີ້ຈະເປັນສິ່ງທີ່ຍິ່ງໃຫຍ່ເພາະມັນຈະຍົກລະດັບມາດຕະຖານສຳລັບທຸກຄົນ, ແລະຮັກສາມໍລະດົກຂອງມະນຸດໃຫ້ດີຂຶ້ນ. ຂ້ອຍບໍ່ມັກວິທີການທີ່ທ່ານດຳເນີນໂຄງການນີ້! ພວກເຮົາຢາກໃຫ້ຜູ້ຄົນຕັ້ງຄ່າ <a %(a_mirrors)s>ກະຈົກ</a>, ແລະພວກເຮົາຈະສະໜັບສະໜູນທາງການເງິນສຳລັບນີ້. ຂ້ອຍຈະຊ່ວຍໄດ້ຢ່າງໃດ? ເຮົາເກັບຂໍ້ມູນແນ່ນອນ. ແຮງບັນດານໃຈຂອງພວກເຮົາໃນການສະສົມຂໍ້ມູນ metadata ແມ່ນຈາກຈຸດປະສົງຂອງ Aaron Swartz ທີ່ຕ້ອງການ "ໜ້າເວັບທຸກໜ້າສຳລັບປື້ມທຸກເລື່ອງທີ່ເຄີຍຖືກພິມ", ທີ່ລາວໄດ້ສ້າງ <a %(a_openlib)s>Open Library</a>. ໂຄງການນັ້ນໄດ້ປະສົບຄວາມສຳເລັດ, ແຕ່ຕຳແໜ່ງທີ່ເປັນເອກະລັກຂອງພວກເຮົາອະນຸຍາດໃຫ້ພວກເຮົາໄດ້ metadata ທີ່ພວກເຂົາບໍ່ສາມາດໄດ້. ອີກຫນຶ່ງແຮງບັນດານໃຈແມ່ນຄວາມຕ້ອງການຂອງພວກເຮົາທີ່ຈະຮູ້ <a %(a_blog)s>ວ່າມີປື້ມຈຳນວນເທົ່າໃດໃນໂລກ</a>, ເພື່ອທີ່ພວກເຮົາຈະຄິດໄດ້ວ່າຍັງມີປື້ມຈຳນວນເທົ່າໃດທີ່ພວກເຮົາຕ້ອງການບັນທຶກໄວ້. ທ່ານເກັບຂໍ້ມູນ metadata ຫຼືບໍ່? ຂໍໃຫ້ສັງເກດວ່າ mhut.org ບລັອກຊ່ວງ IP ບາງຊ່ວງ, ດັ່ງນັ້ນອາດຈະຕ້ອງໃຊ້ VPN. <strong>Android:</strong> ຄລິກເມນູສາມຈຸດທາງມຸມຂວາດ້ານເທິງ, ແລະເລືອກ “ເພີ່ມໃສ່ໜ້າຈໍຫຼັກ”. <strong>iOS:</strong> ຄລິກປຸ່ມ “ແບ່ງປັນ” ທາງລຸ່ມ, ແລະເລືອກ “ເພີ່ມໃສ່ໜ້າຈໍຫຼັກ”. ພວກເຮົາບໍ່ມີແອັບມືຖືທາງການ, ແຕ່ທ່ານສາມາດຕິດຕັ້ງເວັບໄຊທ໌ນີ້ເປັນແອັບໄດ້. ທ່ານມີແອັບມືຖືບໍ່? ກະລຸນາສົ່ງພວກມັນໄປທີ່ <a %(a_archive)s>Internet Archive</a>. ພວກເຂົາຈະສະຫງວນພວກມັນໃຫ້ຖືກຕ້ອງ. ຂ້ອຍຈະບໍລິຈາກປື້ມຫຼືວັດຖຸທາງກາຍອື່ນໆໄດ້ຢ່າງໃດ? ຂ້ອຍຈະຂໍປື້ມໄດ້ຢ່າງໃດ? <a %(a_blog)s>ບລັອກຂອງ Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — ການອັບເດດປະຈຳ <a %(a_software)s>ຊອບແວຂອງ Anna</a> — ຊອບແວເປີດແຫຼ່ງຂອງພວກເຮົາ <a %(a_datasets)s>ຊຸດຂໍ້ມູນ</a> — <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — ໂດເມນທາງເລືອກ ມີຊັບພະຍາກອນອື່ນໆກ່ຽວກັບ Anna’s Archive ບໍ? <a %(a_translate)s>ແປພາສາໃນຊອບແວຂອງ Anna</a> — ລະບົບການແປພາສາຂອງພວກເຮົາ <a %(a_wikipedia)s>Wikipedia</a> — ເພີ່ມເຕີມກ່ຽວກັບພວກເຮົາ (ກະລຸນາຊ່ວຍຮັກສາໜ້ານີ້ໃຫ້ທັນສະໄໝ, ຫຼືສ້າງໜ້າໃໝ່ສຳລັບພາສາຂອງທ່ານເອງ!) ເລືອກການຕັ້ງຄ່າທີ່ທ່ານຊອບ, ປ່ອຍໃຫ້ຊ່ອງຄົ້ນຫາວ່າງ, ຄລິກ “ຄົ້ນຫາ”, ແລະຈາກນັ້ນບັນທຶກໜ້ານີ້ໂດຍໃຊ້ຟັງຊັນບັນທຶກຂອງເບຣາວເຊີຂອງທ່ານ. ຂ້ອຍຈະບັນທຶກການຕັ້ງຄ່າການຄົ້ນຫາຂອງຂ້ອຍໄດ້ຢ່າງໃດ? ພວກເຮົາຍິນດີຕ້ອນຮັບນັກຄົ້ນຄວ້າຄວາມປອດໄພໃຫ້ຄົ້ນຫາຈຸດອ່ອນໃນລະບົບຂອງພວກເຮົາ. ພວກເຮົາເປັນຜູ້ສະໜັບສະໜູນຢ່າງໃຫຍ່ຂອງການເຜີຍແຜ່ຢ່າງຮັບຜິດຊອບ. ຕິດຕໍ່ພວກເຮົາ <a %(a_contact)s>ທີ່ນີ້</a>. ພວກເຮົາບໍ່ສາມາດມອບລາງວັນສຳລັບການຄົ້ນພົບຂໍ້ບົກພ່ອງໄດ້ໃນປັດຈຸບັນ, ເຊັ່ນດຽວກັນກັບຂໍ້ບົກພ່ອງທີ່ມີ <a %(a_link)s>ສັກຍາພາບທີ່ຈະທຳລາຍຄວາມລັບຂອງພວກເຮົາ</a>, ສຳລັບທີ່ພວກເຮົາມອບລາງວັນໃນຊ່ວງ $10k-50k. ພວກເຮົາຢາກມີຂອບເຂດກວ້າງຂຶ້ນສຳລັບການມອບລາງວັນຂໍ້ບົກພ່ອງໃນອະນາຄົດ! ກະລຸນາສັງເກດວ່າການໂຈມຕີທາງສັງຄົມບໍ່ຢູ່ໃນຂອບເຂດ. ຖ້າທ່ານສົນໃຈໃນຄວາມປອດໄພແບບຮຸນແຮງ, ແລະຕ້ອງການຊ່ວຍເຫຼືອໃນການບັນທຶກຄວາມຮູ້ແລະວັດທະນະທຳຂອງໂລກ, ຢ່າລືມຕິດຕໍ່ພວກເຮົາ. ມີຫຼາຍວິທີທີ່ທ່ານສາມາດຊ່ວຍເຫຼືອໄດ້. ທ່ານມີໂຄງການເຜີຍແຜ່ຢ່າງຮັບຜິດຊອບບໍ? ພວກເຮົາບໍ່ມີທີ່ພຽງພໍທີ່ຈະໃຫ້ທຸກຄົນໃນໂລກດາວໂຫລດຄວາມໄວສູງໄດ້, ເຖິງແມ່ນວ່າພວກເຮົາຢາກຈະເຮັດແບບນັ້ນ. ຖ້າມີຜູ້ມີອຸປະກະກະທີ່ມີຄວາມຮວຍຢາກຈະມາຊ່ວຍພວກເຮົາໃນການນີ້, ມັນຈະເປັນສິ່ງທີ່ດີເລີດ, ແຕ່ຈົນກວ່າຈະມີວັນນັ້ນ, ພວກເຮົາກຳລັງພະຍາຍາມທີ່ດີທີ່ສຸດ. ພວກເຮົາເປັນໂຄງການບໍ່ສະແຫວງກຳໄລທີ່ສາມາດດຳລົງຢູ່ໄດ້ດ້ວຍການບໍລິຈາກເທົ່ານັ້ນ. ນີ້ເປັນເຫດຜົນທີ່ພວກເຮົາໄດ້ນຳໃຊ້ລະບົບສອງຢ່າງສຳລັບການດາວໂຫລດຟຣີ, ກັບຄູ່ຮ່ວມງານຂອງພວກເຮົາ: ເຊີບເວີທີ່ແບ່ງປັນກັນທີ່ມີການດາວໂຫລດຊ້າ, ແລະເຊີບເວີທີ່ໄວກວ່າໜ້ອຍໆທີ່ມີລາຍຊື່ລໍຖ້າ (ເພື່ອຫຼຸດຈຳນວນຄົນທີ່ດາວໂຫລດໃນເວລາດຽວກັນ). ພວກເຮົາຍັງມີ <a %(a_verification)s>ການຢືນຢັນຜ່ານບຣາວເຊີ</a> ສຳລັບການດາວໂຫລດຊ້າ, ເພາະບໍ່ຊັ້ນບອດແລະການຂູດຂໍ້ມູນຈະກະທຳການລວມມືກັນ, ເຮັດໃຫ້ສິ່ງຕ່າງໆຊ້າລົງສຳລັບຜູ້ໃຊ້ທີ່ມີຄວາມຊອບທຳ. ຂໍໃຫ້ສັງເກດວ່າ, ເມື່ອໃຊ້ Tor Browser, ທ່ານອາດຈະຕ້ອງປັບຕັ້ງຄ່າຄວາມປອດໄພຂອງທ່ານ. ໃນຕົວເລືອກທີ່ຕ່ຳທີ່ສຸດ, ທີ່ເອີ້ນວ່າ “ມາດຕະຖານ”, ການທົດສອບ Cloudflare turnstile ຈະສຳເລັດ. ໃນຕົວເລືອກທີ່ສູງກວ່າ, ທີ່ເອີ້ນວ່າ “ປອດໄພກວ່າ” ແລະ “ປອດໄພທີ່ສຸດ”, ການທົດສອບຈະລົ້ມເຫລວ. ສຳລັບໄຟລ໌ຂະໜາດໃຫຍ່ບາງຄັ້ງການດາວໂຫລດຊ້າອາດຈະຂາດກາງຄັນ. ພວກເຮົາແນະນຳໃຫ້ໃຊ້ຕົວຈັດການດາວໂຫລດ (ເຊັ່ນ JDownloader) ເພື່ອການດຳເນີນການດາວໂຫລດໃຫຍ່ໆໃຫ້ຕໍ່ເນື່ອງໂດຍອັດຕະໂນມັດ. ເປັນຫຍັງການດາວໂຫລດຊ້າຈຶ່ງຊ້າ? ຄຳຖາມທີ່ຖາມບ່ອຍ (FAQ) ໃຊ້ <a %(a_list)s>ຕົວສ້າງລາຍການ torrent</a> ເພື່ອສ້າງລາຍການ torrent ທີ່ຕ້ອງການການ torrent ຫຼາຍທີ່ສຸດ, ພາຍໃນຂອບເຂດພື້ນທີ່ເກັບຂໍ້ມູນຂອງທ່ານ. ແມ່ນເບິ່ງ <a %(a_llm)s>ຂໍ້ມູນ LLM</a> ໜ້າ. ໂຕເຣນສ່ວນໃຫຍ່ມີໄຟລ໌ຢູ່ໂດຍກົງ, ຊຶ່ງຫມາຍຄວາມວ່າທ່ານສາມາດສັ່ງໃຫ້ລູກຄ້າໂຕເຣນດາວໂຫລດພຽງແຕ່ໄຟລ໌ທີ່ຕ້ອງການໄດ້. ເພື່ອກຳນົດໄຟລ໌ທີ່ຈະດາວໂຫລດ, ທ່ານສາມາດ <a %(a_generate)s>ສ້າງ</a> ຂໍ້ມູນ metadata ຂອງພວກເຮົາ, ຫຼື <a %(a_download)s>ດາວໂຫລດ</a> ຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB ຂອງພວກເຮົາ. ໂຊກບໍ່ດີ, ຈຳນວນຫຼາຍຂອງການລວມໂຕເຣນມີໄຟລ໌ .zip ຫຼື .tar ຢູ່ຮາກ, ໃນກໍລະນີນີ້ທ່ານຈະຕ້ອງດາວໂຫລດທັງໝົດຂອງໂຕເຣນກ່ອນທີ່ຈະສາມາດເລືອກໄຟລ໌ທີ່ຈະດາວໂຫລດໄດ້. ບໍ່ມີເຄື່ອງມືທີ່ໃຊ້ງ່າຍສໍາລັບການກອງທອຣເຣນທີ່ມີຢູ່ຍັງ, ແຕ່ພວກເຮົາຍິນດີທີ່ຈະຮັບການມີສ່ວນຮ່ວມ. (ພວກເຮົາມີ <a %(a_ideas)s>ບາງຄວາມຄິດ</a> ສໍາລັບກໍລະນີຫຼັງນີ້ແມ່ນແນ່ນອນ.) ຄໍາຕອບຍາວ: ຄໍາຕອບສັ້ນ: ບໍ່ງ່າຍ. ພວກເຮົາພະຍາຍາມຮັກສາການຊ້ຳກັນຫຼືການທັບຊ້ອນລະຫວ່າງໂຕເຣນໃນລາຍການນີ້ໃຫ້ເຫຼືອຫນ້ອຍທີ່ສຸດ, ແຕ່ນີ້ບໍ່ສາມາດບັນລຸໄດ້ເສມອກເສມອະ, ແລະຂຶ້ນຢູ່ກັບນະໂຍບາຍຂອງຫ້ອງສະໝຸດຕົ້ນທາງ. ສຳລັບຫ້ອງສະໝຸດທີ່ອອກໂຕເຣນຂອງຕົນເອງ, ມັນຢູ່ນອກການຄວບຄຸມຂອງພວກເຮົາ. ສຳລັບໂຕເຣນທີ່ຖືກປ່ອຍໂດຍ Anna’s Archive, ພວກເຮົາຈະລົດການຊ້ຳກັນພຽງແຕ່ຕາມ MD5 hash, ເຊິ່ງຫມາຍຄວາມວ່າສຳເນົາຕ່າງໆຂອງປື້ມດຽວກັນຈະບໍ່ຖືກລົດການຊ້ຳກັນ. ໄດ້. ເຫຼົ່ານີ້ແມ່ນ PDF ແລະ EPUB ຈິງໆ, ພວກເຂົາບໍ່ມີນາມສະກຸນໃນຫຼາຍໆໂຕເຣນຂອງພວກເຮົາ. ມີສອງບ່ອນທີ່ທ່ານສາມາດພົບ metadata ສຳລັບໄຟລ໌ໂຕເຣນ, ລວມທັງປະເພດ/ນາມສະກຸນໄຟລ໌: 1. ແຕ່ລະການລວມຫຼືການປ່ອຍມີ metadata ຂອງຕົນເອງ. ເຊັ່ນ, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> ມີຖານຂໍ້ມູນ metadata ທີ່ສອດຄ່ອງຢູ່ເວັບໄຊ Libgen.rs. ພວກເຮົາມັກຈະເຊື່ອມໂຍງໄປຫາຊັບພະຍາກອນ metadata ທີ່ກ່ຽວຂ້ອງຈາກແຕ່ລະການລວມ <a %(a_datasets)s>ໜ້າ dataset</a>. 2. ພວກເຮົາແນະນຳໃຫ້ <a %(a_generate)s>ສ້າງ</a> ຫຼື <a %(a_download)s>ດາວໂຫລດ</a> ຖານຂໍ້ມູນ ElasticSearch ແລະ MariaDB ຂອງພວກເຮົາ. ເຫຼົ່ານີ້ມີການແມບສຳລັບແຕ່ລະບັນທຶກໃນ Anna’s Archive ກັບໄຟລ໌ໂຕເຣນທີ່ສອດຄ່ອງ (ຖ້າມີ), ຢູ່ພາຍໃຕ້ “torrent_paths” ໃນ ElasticSearch JSON. ລູກຄ້າທອຣເຣນບາງອັນບໍ່ຮອງຮັບຂະໜາດຊິ້ນທີ່ໃຫຍ່, ຊຶ່ງທອຣເຣນຂອງພວກເຮົາມີຫຼາຍ (ສໍາລັບອັນໃໝ່ພວກເຮົາບໍ່ໄດ້ເຮັດຢ່າງນີ້ອີກແລ້ວ — ແມ່ນວ່າມັນຖືກຕ້ອງຕາມຂໍ້ກໍານົດ!). ດັ່ງນັ້ນລອງໃຊ້ລູກຄ້າອື່ນຖ້າທ່ານພົບກັບບັນຫານີ້, ຫຼືບໍ່ພໍໃຈກັບຜູ້ຜະລິດລູກຄ້າທອຣເຣນຂອງທ່ານ. ຂ້ອຍຢາກຊ່ວຍ seed, ແຕ່ຂ້ອຍບໍ່ມີພື້ນທີ່ເກັບຂໍ້ມູນຫຼາຍ. torrent ຊ້າເກີນໄປ; ຂ້ອຍສາມາດດາວໂຫລດຂໍ້ມູນໂດຍກົງຈາກທ່ານໄດ້ບໍ່? ຂ້ອຍສາມາດດາວໂຫລດພຽງແຕ່ບາງສ່ວນຂອງໄຟລ໌ໄດ້ບໍ, ເຊັ່ນ ພຽງແຕ່ພາສາຫຼືຫົວຂໍ້ທີ່ກຳນົດໄວ້? ທ່ານຈັດການກັບໄຟລ໌ທີ່ຊ້ຳກັນໃນໂຕເຣນໄດ້ແນວໃດ? ຂ້ອຍສາມາດໄດ້ຮັບລາຍການໂຕເຣນເປັນ JSON ໄດ້ບໍ? ຂ້ອຍບໍ່ເຫັນ PDF ຫຼື EPUB ໃນໂຕເຣນ, ມີແຕ່ໄຟລ໌ທີ່ເປັນ binary ເທົ່ານັ້ນ? ຂ້ອຍຄວນເຮັດແນວໃດ? ເປັນຫຍັງລູກຄ້າທອຣເຣນຂອງຂ້ອຍບໍ່ສາມາດເປີດໄຟລ໌ທອຣເຣນ / ລິ້ງແມເຈັນຂອງທ່ານໄດ້? ຄຳຖາມທີ່ພົບບ່ອຍກ່ຽວກັບ Torrents ຂ້ອຍຈະອັບໂຫລດປື້ມໃໝ່ໄດ້ຢ່າງໃດ? ກະລຸນາເບິ່ງ <a %(a_href)s>ໂຄງການທີ່ດີເລີດນີ້</a>. ທ່ານມີຕົວຈັບຕາການເຂົ້າເຖິງບໍ? Anna’s Archive ແມ່ນຫຍັງ? ກາຍເປັນສະມາຊິກເພື່ອໃຊ້ດາວໂຫລດໄວໆ. ຕອນນີ້ພວກເຮົາຮອງຮັບບັດຂອງຂວັນ Amazon, ບັດເຄຣດິດ ແລະ ດີເບດ, ຄຣິບໂຕ, Alipay, ແລະ WeChat. ທ່ານໄດ້ໃຊ້ດາວໂຫລດໄວໆໝົດແລ້ວວັນນີ້. ການເຂົ້າເຖິງ ການດາວໂຫລດລາຍຊົ່ວໂມງໃນ 30 ວັນທີ່ຜ່ານມາ. ຄ່າເຉລີ່ຍລາຍຊົ່ວໂມງ: %(hourly)s. ຄ່າເຉລີ່ຍລາຍວັນ: %(daily)s. ພວກເຮົາເຮັດວຽກກັບຄູ່ຮ່ວມງານເພື່ອເຮັດໃຫ້ການສະສົມຂອງພວກເຮົາສາມາດເຂົ້າເຖິງໄດ້ງ່າຍແລະຟຣີສຳລັບທຸກຄົນ. ພວກເຮົາເຊື່ອວ່າທຸກຄົນມີສິດໃນຄວາມຮູ້ຮວມຂອງມະນຸດ. ແລະ <a %(a_search)s>ບໍ່ເປັນຄ່າໃຊ້ຈ່າຍຂອງນັກຂຽນ</a>. Datasets ທີ່ໃຊ້ໃນ Anna’s Archive ເປັນເປີດທັງໝົດ, ແລະສາມາດສຳເນົາໄດ້ເປັນຈຳນວນຫຼາຍໂດຍໃຊ້ torrents. <a %(a_datasets)s>ຮຽນຮູ້ເພີ່ມເຕີມ…</a> ການເກັບຮັກສາລະຍະຍາວ ຖານຂໍ້ມູນເຕັມຮູບແບບ ຄົ້ນຫາ ປຶ້ມ, ເອກະສານ, ວາລະສານ, ການຕູນ, ບັນທຶກຫ້ອງສະໝຸດ, ຂໍ້ມູນອ້າງອີງ, … ລະຫັດ <a %(a_code)s>code</a> ແລະ <a %(a_datasets)s>data</a> ທັງໝົດຂອງພວກເຮົາແມ່ນເປີດເປັນແຫຼ່ງທີ່ເປີດກວ້າງທັງໝົດ. <span %(span_anna)s>Anna’s Archive</span> ແມ່ນໂຄງການບໍລິສຸດທີ່ມີສອງເປົ້າໝາຍ: <li><strong>ການສະຫງວນຮັກສາ:</strong> ສຳຮອງຄວາມຮູ້ແລະວັດທະນະທຳທັງໝົດຂອງມະນຸດ.</li><li><strong>ການເຂົ້າເຖິງ:</strong> ເຮັດໃຫ້ຄວາມຮູ້ແລະວັດທະນະທຳນີ້ມີໃຫ້ກັບທຸກຄົນໃນໂລກ.</li> ພວກເຮົາມີການສະສົມຂໍ້ມູນຂໍ້ຄວາມທີ່ມີຄຸນນະພາບສູງທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກ. <a %(a_llm)s>ຮຽນຮູ້ເພີ່ມເຕີມ…</a> ຂໍ້ມູນຝຶກອົບຮົມ LLM 🪩 ການສຳເນົາ: ຂໍອາສາສະໝັກ ຖ້າທ່ານເປັນຜູ້ດຳເນີນການຊຳລະເງິນທີ່ບໍ່ມີຄວາມສຽງສູງ, ກະລຸນາຕິດຕໍ່ພວກເຮົາ. ພວກເຮົາກຳລັງມອງຫາຄົນທີ່ຈະວາງໂຄສະນານ້ອຍໆທີ່ມີຄວາມສຸພາບ. ລາຍໄດ້ທັງໝົດຈະໄປສູ່ຄວາມພະຍາຍາມການຮັກສາຂອງພວກເຮົາ. ການສຳຮອງ ພວກເຮົາຄາດຄະເນວ່າພວກເຮົາໄດ້ສະຫງວນປະມານ <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% ຂອງປື້ມໃນໂລກ</a>. ພວກເຮົາສຳຮອງປື້ມ, ເອກະສານ, ການຕູນ, ວາລະສານ, ແລະອື່ນໆ, ໂດຍການນຳເອົາວັດຖຸຫຼັກເຫຼົ່ານີ້ຈາກ <a href="https://en.wikipedia.org/wiki/Shadow_library">ຫ້ອງສະໝຸດເງົາ</a>, ຫ້ອງສະໝຸດທາງການ, ແລະການສະສົມອື່ນໆມາຢູ່ທີ່ດຽວ. ຂໍ້ມູນທັງໝົດນີ້ຈະຖືກສຳຮອງໄວ້ຕະຫຼອດໄປໂດຍການເຮັດໃຫ້ມັນງ່າຍຕໍ່ການຄັດລອກເປັນຈຳນວນຫຼາຍ — ໂດຍການໃຊ້ torrents — ເຮັດໃຫ້ມີສຳເນົາຫຼາຍສຳເນົາທົ່ວໂລກ. ຫ້ອງສະໝຸດເງົາບາງຫ້ອງໄດ້ເຮັດສິ່ງນີ້ແລ້ວເອງ (ເຊັ່ນ Sci-Hub, Library Genesis), ໃນຂະນະທີ່ Anna’s Archive “ປົດປ່ອຍ” ຫ້ອງສະໝຸດອື່ນໆທີ່ບໍ່ໄດ້ສະໜອງການຈັດສົ່ງເປັນຈຳນວນຫຼາຍ (ເຊັ່ນ Z-Library) ຫຼືບໍ່ແມ່ນຫ້ອງສະໝຸດເງົາເລີຍ (ເຊັ່ນ Internet Archive, DuXiu). ການແຜ່ກະຈາຍກວ້າງຂວາງນີ້, ຜະມວນກັບລະຫັດເປີດແຫຼ່ງ, ເຮັດໃຫ້ເວັບໄຊທ໌ຂອງພວກເຮົາມີຄວາມທົນທານຕໍ່ການຖອນອອກ, ແລະຮັບປະກັນການສະຫງວນຮັກສາຄວາມຮູ້ແລະວັດທະນະທຳຂອງມະນຸດໃນລະຍະຍາວ. ຮຽນຮູ້ເພີ່ມເຕີມກ່ຽວກັບ <a href="/datasets">ຊຸດຂໍ້ມູນຂອງພວກເຮົາ</a>. ຖ້າທ່ານເປັນ <a %(a_member)s>ສະມາຊິກ</a>, ການກວດສອບຜ່ານບຣາວເຊີບໍ່ຈຳເປັນ. 🧬&nbsp;SciDB ແມ່ນການສືບຕໍ່ຂອງ Sci-Hub. SciDB ເປີດ DOI Sci-Hub ໄດ້ <a %(a_paused)s>ຢຸດ</a> ການອັບໂຫລດເອກະສານໃໝ່. ການເຂົ້າເຖິງເອກະສານວິຊາການ %(count)s ໂດຍກົງ 🧬&nbsp;SciDB ແມ່ນການສືບຕໍ່ຂອງ Sci-Hub, ດ້ວຍອິນເຕີເຟສທີ່ຄຸ້ນເຄີຍແລະການເບິ່ງ PDF ໂດຍກົງ. ປ້ອນ DOI ຂອງທ່ານເພື່ອເບິ່ງ. ພວກເຮົາມີຄັງເກັບຂໍ້ມູນ Sci-Hub ທັງໝົດ, ພ້ອມທັງເອກະສານໃໝ່. ສ່ວນໃຫຍ່ສາມາດເບິ່ງໂດຍກົງດ້ວຍອິນເຕີເຟສທີ່ຄຸ້ນເຄີຍ, ຄື Sci-Hub. ບາງສ່ວນສາມາດດາວໂຫລດຜ່ານແຫຼ່ງພາຍນອກ, ໃນກໍລະນີນັ້ນພວກເຮົາຈະສະແດງລິ້ງໄປຫາເຫດການນັ້ນ. ທ່ານສາມາດຊ່ວຍໄດ້ຢ່າງຫຼາຍໂດຍການສຳເນົາ torrents. <a %(a_torrents)s>ຮຽນຮູ້ເພີ່ມເຕີມ…</a> >%(count)s ຜູ້ສຳເນົາ <%(count)s ຜູ້ສຳເນົາ %(count_min)s–%(count_max)s ຜູ້ສຳເນົາ 🤝 ກຳລັງມອງຫາອາສາສະໝັກ ເປັນໂຄງການບໍ່ຫາກຳໄລ, ເປີດແຫຼ່ງທີ່ມີການພັດທະນາຕະຫຼອດ, ພວກເຮົາມັກຈະມອງຫາຄົນທີ່ຈະຊ່ວຍເຫຼືອ. ການດາວໂຫລດ IPFS ລາຍການໂດຍ %(by)s, ສ້າງຂຶ້ນ <span %(span_time)s>%(time)s</span> ບັນທຶກ ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາລອງອີກຄັ້ງ. ✅ ບັນທຶກແລ້ວ. ກະລຸນາຣີໂຫຼດໜ້ານີ້. ລາຍການຫວ່າງເປົ່າ. ແກ້ໄຂ ເພີ່ມຫຼືລຶບອອກຈາກລາຍການນີ້ໂດຍການຊອກຫາໄຟລ໌ແລະເປີດແຖບ “ລາຍການ”. ລາຍການ ວິທີທີ່ພວກເຮົາສາມາດຊ່ວຍໄດ້ ການລຶບການຊ້ຳກັນ (deduplication) ການສະກັດຂໍ້ຄວາມ ແລະ metadata OCR ພວກເຮົາສາມາດສະໜອງການເຂົ້າເຖິງຄວາມໄວສູງໃຫ້ກັບການລວມຂອງພວກເຮົາທັງໝົດ, ພ້ອມທັງການລວມທີ່ຍັງບໍ່ໄດ້ປົດປ່ອຍ. ນີ້ແມ່ນການເຂົ້າເຖິງລະດັບອົງການທີ່ພວກເຮົາສາມາດສະໜອງໄດ້ສຳລັບການບໍລິຈາກໃນຂະໜາດເປັນຫຼາຍພັນດອນລາຣ໌ສະຫະລັດ. ພວກເຮົາຍັງຍິນດີທີ່ຈະແລກປ່ຽນນີ້ສຳລັບການລວມທີ່ມີຄຸນນະພາບສູງທີ່ພວກເຮົາຍັງບໍ່ມີ. ພວກເຮົາສາມາດຄືນເງິນໃຫ້ທ່ານໄດ້ຖ້າທ່ານສາມາດສະໜອງການພັດທະນາຂໍ້ມູນຂອງພວກເຮົາ, ເຊັ່ນ: ສະໜັບສະໜູນການຈັດເກັບຮັກສາຄວາມຮູ້ຂອງມະນຸດໃນລະຍະຍາວ, ໃນຂະນະທີ່ໄດ້ຮັບຂໍ້ມູນທີ່ດີກວ່າສຳລັບແບບຈຳລອງຂອງທ່ານ! <a %(a_contact)s>ຕິດຕໍ່ພວກເຮົາ</a> ເພື່ອຫາລືວ່າພວກເຮົາສາມາດເຮັດວຽກຮ່ວມກັນໄດ້ຢ່າງໃດ. ເຂົາເຂົ້າໃຈກັນດີວ່າ LLM ຈະເຮັດວຽກໄດ້ດີກັບຂໍ້ມູນທີ່ມີຄຸນນະພາບສູງ. ພວກເຮົາມີການລວມປື້ມ, ເອກະສານ, ວາລະສານ, ແລະອື່ນໆທີ່ໃຫຍ່ທີ່ສຸດໃນໂລກ, ເຊິ່ງເປັນແຫຼ່ງຂໍ້ຄວາມທີ່ມີຄຸນນະພາບສູງທີ່ສຸດບາງສ່ວນ. ຂໍ້ມູນ LLM ຂະໜາດ ແລະ ຂອບເຂດທີ່ເປັນເອກະລັກ ການລວມຂອງພວກເຮົາມີເອກະສານຫຼາຍກວ່າຮ້ອຍລ້ານໄຟລ໌, ລວມທັງວາລະສານວິຊາການ, ປື້ມຮຽນ, ແລະ ວາລະສານ. ພວກເຮົາບັນລຸຂະໜາດນີ້ໂດຍການລວມກັນຂອງຄັງຂໍ້ມູນທີ່ມີຢູ່ແລ້ວທີ່ໃຫຍ່ໆ. ບາງສ່ວນຂອງການລວມຂໍ້ມູນຂອງພວກເຮົາມີຢູ່ແລ້ວໃນຮູບແບບຈຳນວນຫຼາຍ (Sci-Hub, ແລະ ບາງສ່ວນຂອງ Libgen). ແຫຼ່ງຂໍ້ມູນອື່ນໆທີ່ພວກເຮົາໄດ້ປົດປ່ອຍເອງ. <a %(a_datasets)s>Datasets</a> ສະແດງພາບລວມເຕັມຮູບແບບ. ການລວມຂອງພວກເຮົາລວມທັງປື້ມ, ເອກະສານ, ແລະ ວາລະສານຫຼາຍລ້ານເລັ້ມກ່ອນຍຸກ e-book. ສ່ວນໃຫຍ່ຂອງການລວມນີ້ໄດ້ຖືກ OCR ແລ້ວ, ແລະ ມີການຊ້ຳກັນພາຍໃນທີ່ນ້ອຍຫຼາຍແລ້ວ. ດຳເນີນການຕໍ່ ຖ້າທ່ານເສຍລະຫັດ, ກະລຸນາ <a %(a_contact)s>ຕິດຕໍ່ພວກເຮົາ</a> ແລະໃຫ້ຂໍ້ມູນທີ່ຫຼາກຫຼາຍທີ່ສຸດ. ທ່ານອາດຈະຕ້ອງສ້າງບັນຊີໃໝ່ຊົ່ວຄາວເພື່ອຕິດຕໍ່ພວກເຮົາ. ກະລຸນາ <a %(a_account)s>ເຂົ້າລະບົບ</a> ເພື່ອເບິ່ງໜ້ານີ້.</a> ເພື່ອປ້ອງກັນບອດສະແປມຈາກການສ້າງບັນຊີຈຳນວນຫຼາຍ, ພວກເຮົາຈຳເປັນຕ້ອງກວດສອບເບຣາວເຊີຂອງທ່ານກ່ອນ. ຖ້າທ່ານຕິດຢູ່ໃນວົງຈອນທີ່ບໍ່ສິ້ນສຸດ, ພວກເຮົາແນະນຳໃຫ້ຕິດຕັ້ງ <a %(a_privacypass)s>Privacy Pass</a>. ມັນອາດຈະຊ່ວຍໄດ້ເຊິ່ງການປິດການປ້ອງກັນໂຄສະນາແລະສ່ວນຂະຫຍາຍອື່ນໆຂອງເບຣາວເຊີ. ເຂົ້າລະບົບ / ລົງທະບຽນ ຄັງເກັບຂອງ Anna ກຳລັງຢູ່ໃນການບຳລຸງຮັກສາ. ກະລຸນາກັບມາໃໝ່ໃນໜຶ່ງຊົ່ວໂມງ. ນັກຂຽນທາງເລືອກ ຄຳອະທິບາຍທາງເລືອກ ສະບັບທາງເລືອກ ນາມສະກຸນທາງເລືອກ ຊື່ໄຟລ໌ທາງເລືອກ ຜູ້ພິມທາງເລືອກ ຊື່ເລື່ອງທາງເລືອກ ວັນທີເປີດໃຊ້ຊອບແວຟຣີ ອ່ານເພີ່ມເຕີມ… ຄຳອະທິບາຍ ຊອກຫາບັນທຶກໃນ Anna’s Archive ສຳລັບ CADAL SSNO number ຊອກຫາບັນທຶກໃນ Anna’s Archive ສຳລັບ DuXiu SSID number ຊອກຫາບັນທຶກໃນ Anna’s Archive ສຳລັບ DuXiu DXID number ຊອກຫາ Anna’s Archive ສຳລັບ ISBN ຊອກຫາບັນທຶກໃນ Anna’s Archive ສຳລັບ OCLC (WorldCat) number ຊອກຫາບັນທຶກໃນ Anna’s Archive ສຳລັບ Open Library ID ຜູ້ເບິ່ງອອນໄລນ໌ຂອງ ຄັງເກັບຂໍ້ມູນຂອງອັນນາ %(count)s ໜ້າທີ່ຖືກກະທົບ ຫຼັງຈາກດາວໂຫລດ: ອາດມີສະບັບທີ່ດີກວ່າຂອງໄຟລ໌ນີ້ຢູ່ທີ່ %(link)s ດາວໂຫລດທໍເຣນຈຳນວນຫຼາຍ ຄັງເກັບ ໃຊ້ເຄື່ອງມືອອນໄລນ໌ເພື່ອປ່ຽນລະຫວ່າງຮູບແບບ. ເຄື່ອງມືປ່ຽນຮູບແບບທີ່ແນະນຳ: %(links)s ສຳລັບໄຟລ໌ຂະໜາດໃຫຍ່, ພວກເຮົາແນະນຳໃຫ້ໃຊ້ຜູ້ຈັດການດາວໂຫລດເພື່ອປ້ອງກັນການຂັດຂ້ອງ. ຜູ້ຈັດການດາວໂຫລດທີ່ແນະນຳ: %(links)s ດັດສະນີປື້ມອີເລັກຂອງ EBSCOhost (ສຳລັບຜູ້ຊຳນານເທົ່ານັ້ນ) (ກະລຸນາຄລິກ “GET” ຢູ່ຂ້າງເທິງ) (ຄລິກ “GET” ຢູ່ຂ້າງເທິງ) ດາວໂຫລດຈາກພາຍນອກ <strong>🚀 ດາວໂຫລດໄວ</strong> ທ່ານຍັງມີ %(remaining)s ຢູ່ມື້ນີ້. ຂອບໃຈທີ່ເປັນສະມາຊິກ! ❤️ <strong>🚀 ດາວໂຫລດໄວ</strong> ທ່ານໄດ້ໃຊ້ດາວໂຫລດໄວຫມົດແລ້ວສຳລັບມື້ນີ້. <strong>🚀 ດາວໂຫລດໄວ</strong> ທ່ານໄດ້ດາວໂຫລດໄຟລ໌ນີ້ໄປແລ້ວໃນໄມ່ດົນນີ້. ລິ້ງຍັງຄົງໃຊ້ໄດ້ຢູ່ຫລາຍຊົ່ວໂມງ. <strong>🚀 ດາວໂຫລດໄວ</strong> ກາຍເປັນ <a %(a_membership)s>ສະມາຊິກ</a> ເພື່ອສະໜັບສະໜູນການຮັກສາລະຍະຍາວຂອງປື້ມ, ເອກະສານ, ແລະອື່ນໆ. ເພື່ອສະແດງຄວາມຂອບຄຸນສຳລັບການສະໜັບສະໜູນຂອງທ່ານ, ທ່ານຈະໄດ້ຮັບການດາວໂຫລດໄວ. ❤️ 🚀 ດາວໂຫລດໄວ 🐢 ດາວໂຫລດຊ້າ ຢືມຈາກ Internet Archive IPFS Gateway #%(num)d (ທ່ານອາດຈະຕ້ອງລອງຫຼາຍເທື່ອກັບ IPFS) Libgen.li Libgen.rs ນິຍາຍ Libgen.rs ບໍ່ແມ່ນນິຍາຍ ໂຄສະນາຂອງພວກເຂົາມີຊື່ສຽງວ່າມີໂປຣແກຣມທີ່ບໍ່ປອດໄພ, ດັ່ງນັ້ນກະລຸນາໃຊ້ຕົວບລັອກໂຄສະນາ ຫຼືບໍ່ຄລິກໂຄສະນາ Amazon ຂອງ “Send to Kindle” djazz ຂອງ “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (ໄຟລ໌ Nexus/STC ອາດຈະບໍ່ເຊື່ອຖືໃນການດາວໂຫຼດ) ບໍ່ພົບການດາວໂຫລດ. ທຸກຕົວເລືອກດາວໂຫລດມີໄຟລ໌ດຽວກັນ, ແລະຄວນຈະປອດໄພໃນການໃຊ້. ຢ່າງໃດກໍຕາມ, ຄວນລະມັດລະວັງເມື່ອດາວໂຫລດໄຟລ໌ຈາກອິນເຕີເນັດ, ພິເສດຢ່າງຍິ່ງຈາກເວັບໄຊທ໌ພາຍນອກຂອງ Anna’s Archive. ຕົວຢ່າງ, ຄວນຮັກສາອຸປະກອນຂອງທ່ານໃຫ້ອັບເດດຢູ່ເສມື່ອ. (ບໍ່ມີການປ່ຽນທາງ) ເປີດໃນຜູ້ເບິ່ງຂອງພວກເຮົາ (ເປີດໃນຜູ້ເບິ່ງ) ຕົວເລືອກ #%(num)d: %(link)s %(extra)s ຊອກຫາບັນທຶກຕົ້ນສະບັບໃນ CADAL ຊອກຫາດ້ວຍຕົນເອງໃນ DuXiu ຊອກຫາບັນທຶກຕົ້ນສະບັບໃນ ISBNdb ຊອກຫາບັນທຶກຕົ້ນສະບັບໃນ WorldCat ຊອກຫາບັນທຶກຕົ້ນສະບັບໃນ Open Library ຊອກຫາຖານຂໍ້ມູນອື່ນໆ ສຳລັບ ISBN (ສຳລັບຜູ້ພິການທີ່ບໍ່ສາມາດພິມໄດ້ເທົ່ານັ້ນ) PubMed ທ່ານຈະຕ້ອງໃຊ້ຜູ້ອ່ານ ebook ຫຼື PDF ເພື່ອເປີດໄຟລ໌, ຂຶ້ນຢູ່ກັບຮູບແບບໄຟລ໌. ຜູ້ອ່ານ ebook ທີ່ແນະນຳ: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (ອາດຈະບໍ່ມີ DOI ທີ່ກ່ຽວຂ້ອງໃນ Sci-Hub) ທ່ານສາມາດສົ່ງໄຟລ໌ PDF ແລະ EPUB ໄປທີ່ Kindle ຫຼື Kobo eReader ຂອງທ່ານ. ເຄື່ອງມືທີ່ແນະນຳ: %(links)s ຂໍ້ມູນເພີ່ມເຕີມຢູ່ <a %(a_slow)s>FAQ</a>. ສະໜັບສະໜູນນັກຂຽນແລະຫ້ອງສະໝຸດ ຖ້າທ່ານມັກສິ່ງນີ້ແລະສາມາດຈ່າຍໄດ້, ພິຈາລະນາຊື້ຕົ້ນສະບັບດັ່ງເດີມ, ຫຼືສະໜັບສະໜູນນັກຂຽນໂດຍກົງ. ຖ້າມີຢູ່ທີ່ຫ້ອງສະໝຸດທ້ອງຖິ່ນຂອງທ່ານ, ພິຈາລະນາຢືມມັນໄດ້ຟຣີທີ່ນັ້ນ. ການດາວໂຫລດຈາກເຊີບເວີພາກສ່ວນຊົ່ວຄາວບໍ່ພ້ອມໃຊ້ງານສຳລັບໄຟລ໌ນີ້. ທໍເຣນ ຈາກຄູ່ຄ້າທີ່ເຊື່ອໄດ້. Z-Library Z-Library ໃນ Tor (ຕ້ອງການ Tor Browser) ສະແດງການດາວໂຫລດພາຍນອກ <span class="font-bold">❌ ໄຟລ໌ນີ້ອາດມີບັນຫາ, ແລະໄດ້ຖືກເຊື່ອງຈາກຫ້ອງສະໝຸດແຫ່ງໜຶ່ງ.</span> ບາງຄັ້ງນີ້ເປັນຕາມຄຳຮ້ອງຂໍຂອງເຈົ້າຂອງລິຂະສິດ, ບາງຄັ້ງເປັນເພາະມີທາງເລືອກທີ່ດີກວ່າ, ແຕ່ບາງຄັ້ງເປັນເພາະມີບັນຫາກັບໄຟລ໌ເອງ. ມັນອາດຍັງຄົງດີທີ່ຈະດາວໂຫລດ, ແຕ່ພວກເຮົາແນະນຳໃຫ້ຄົ້ນຫາໄຟລ໌ທາງເລືອກກ່ອນ. ລາຍລະອຽດເພີ່ມເຕີມ: ຖ້າທ່ານຍັງຕ້ອງການດາວໂຫລດໄຟລ໌ນີ້, ຈົ່ງແນ່ໃຈວ່າໃຊ້ຊອບແວທີ່ເຊື່ອຖືໄດ້ ແລະທີ່ມີການອັບເດດເພື່ອເປີດມັນ. ຄຳເຫັນເກື່ອງຂໍ້ມູນ AA: ຄົ້ນຫາ Anna’s Archive ສຳລັບ “%(name)s” ຄົ້ນຫາ Codes Explorer: ເບິ່ງໃນ Codes Explorer “%(name)s” URL: ເວັບໄຊທ໌: ຖ້າທ່ານມີໄຟລ໌ນີ້ແລະມັນຍັງບໍ່ມີຢູ່ໃນ Anna’s Archive, ພິຈາລະນາ <a %(a_request)s>ອັບໂຫຼດມັນ</a>. ໄຟລ໌ Internet Archive Controlled Digital Lending “%(id)s” ນີ້ແມ່ນບັນທຶກຂອງໄຟລ໌ຈາກ Internet Archive, ບໍ່ແມ່ນໄຟລ໌ທີ່ສາມາດດາວໂຫຼດໂດຍກົງ. ທ່ານສາມາດລອງຢືມປື້ມນີ້ (ລິ້ງຢູ່ຂ້າງລຸ່ມ), ຫຼືໃຊ້ URL ນີ້ເມື່ອ <a %(a_request)s>ຮ້ອງຂໍໄຟລ໌</a>. ປັບປຸງ metadata ບັນທຶກ metadata ຂອງ CADAL SSNO %(id)s ນີ້ແມ່ນບັນທຶກ metadata, ບໍ່ແມ່ນໄຟລ໌ທີ່ສາມາດດາວໂຫລດໄດ້. ທ່ານສາມາດໃຊ້ URL ນີ້ເມື່ອ <a %(a_request)s>ຮ້ອງຂໍໄຟລ໌</a>. ບັນທຶກ metadata ຂອງ DuXiu SSID %(id)s ບັນທຶກ metadata ຂອງ ISBNdb %(id)s ບັນທຶກຂໍ້ມູນ MagzDB ID %(id)s ບັນທຶກຂໍ້ມູນ Nexus/STC ID %(id)s ບັນທຶກ metadata ຂອງ OCLC (WorldCat) ເລກ %(id)s ບັນທຶກ metadata ຂອງ Open Library %(id)s ໄຟລ໌ Sci-Hub “%(id)s” ບໍ່ພົບ “%(md5_input)s” ບໍ່ພົບໃນຖານຂໍ້ມູນຂອງພວກເຮົາ. ເພີ່ມຄວາມຄິດເຫັນ (%(count)s) ທ່ານສາມາດຮັບ MD5 ຈາກ URL, ຕົວຢ່າງເຊັ່ນ MD5 ຂອງເວີຊັນທີ່ດີກວ່າຂອງໄຟລ໌ນີ້ (ຖ້າມີ). ກະລຸນາກອກຂໍ້ມູນນີ້ຖ້າມີໄຟລ໌ອື່ນທີ່ຄ້າຍຄືກັບໄຟລ໌ນີ້ (ສຳນັກພິມດຽວກັນ, ປະເພດໄຟລ໌ດຽວກັນຖ້າຫາໄດ້), ທີ່ຄວນໃຊ້ແທນໄຟລ໌ນີ້. ຖ້າທ່ານຮູ້ວ່າມີເວີຊັນທີ່ດີກວ່າຂອກໄຟລ໌ນີ້ພາຍນອກ Anna’s Archive, ກະລຸນາ <a %(a_upload)s>ອັບໂຫລດມັນ</a>. ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ຄືນແລະລອງໃໝ່. ທ່ານໄດ້ປະເດັນຄວາມຄິດເຫັນແລ້ວ. ອາດຈະໃຊ້ເວລາຫນຶ່ງນາທີໃຫ້ມັນປາກົດຂຶ້ນ. ກະລຸນາໃຊ້ <a %(a_copyright)s>ແບບຟອມຮ້ອງຮຽນ DMCA / ລິຂະສິດ</a>. ອະທິບາຍບັນຫາ (ຈຳເປັນ) ຖ້າໄຟລ໌ນີ້ມີຄຸນນະພາບດີ, ທ່ານສາມາດຖົມຖາມຫຍັງກ່ຽວກັບມັນໄດ້ທີ່ນີ້! ຖ້າບໍ່, ກະລຸນາໃຊ້ປຸ່ມ “ລາຍງານບັນຫາໄຟລ໌”. ຄຸນນະພາບໄຟລ໌ດີ (%(count)s) ຄຸນນະພາບໄຟລ໌ ຮຽນຮູ້ວິທີ <a %(a_metadata)s>ປັບປຸງຂໍ້ມູນເພີ່ມເຕີມ</a> ສຳລັບໄຟລ໌ນີ້ເອງ. ຄຳອະທິບາຍບັນຫາ ກະລຸນາ <a %(a_login)s>ເຂົ້າລະບົບ</a>. ຂ້ອຍມັກປື້ມເລ່ມນີ້! ຊ່ວຍຊຸມຊົນໂດຍລາຍງານຄຸນນະພາບຂອງໄຟລ໌ນີ້! 🙌 ມີບາງຢ່າງຜິດພາດ. ກະລຸນາໂຫຼດໜ້ານີ້ຄືນແລະລອງອີກຄັ້ງ. ລາຍງານບັນຫາໄຟລ໌ (%(count)s) ຂອບໃຈທີ່ສົ່ງລາຍງານຂອງທ່ານ. ມັນຈະຖືກສະແດງໃນໜ້ານີ້, ແລະຈະຖືກກວດສອບໂດຍ Anna (ຈົນກວ່າພວກເຮົາຈະມີລະບົບກວດສອບທີ່ຖືກຕ້ອງ). ປະເດັນຄວາມຄິດເຫັນ ສົ່ງລາຍງານ ມີຫຍັງຜິດກັບໄຟລ໌ນີ້? ຢືມ (%(count)s) ຄວາມຄິດເຫັນ (%(count)s) ດາວໂຫລດ (%(count)s) ສຳຫຼວດ metadata (%(count)s) ລາຍການ (%(count)s) ສະຖິຕິ (%(count)s) ສຳລັບຂໍ້ມູນເກື່ອງກຽວກັບໄຟລ໌ນີ້ໂດຍສະເພາະ, ກະລຸນາເບິ່ງ <a %(a_href)s>ໄຟລ໌ JSON ຂອງມັນ</a>. ນີ້ແມ່ນໄຟລ໌ທີ່ຖືກຄວບຄຸມໂດຍ <a %(a_ia)s>ຫ້ອງສະໝຸດການຢືມຢືມດິຈິຕອນຂອງ IA</a>, ແລະຖືກດັດສະນີໂດຍ Anna’s Archive ເພື່ອການຄົ້ນຫາ. ສຳລັບຂໍ້ມູນເກື່ອງກຽວກັບ datasets ຕ່າງໆທີ່ພວກເຮົາໄດ້ຮວບຮວມ, ກະລຸນາເບິ່ງ <a %(a_datasets)s>ໜ້າ Datasets</a>. ຂໍ້ມູນເມຕາຈາກບັນທຶກທີ່ເຊື່ອມໂຍງ ປັບປຸງຂໍ້ມູນເມຕາທີ່ Open Library “file MD5” ແມ່ນການຫາຄ່າ hash ທີ່ຖືກຄິດໄລ່ຈາກເນື້ອໃນຂອງໄຟລ໌, ແລະມັນມີຄວາມເປັນເອກະລັກທີ່ສົມເຫດສົມຜົນຕາມເນື້ອໃນນັ້ນ. ຫ້ອງສະໝຸດ shadow ທັງຫມົດທີ່ພວກເຮົາໄດ້ດັດສະນີຢູ່ນີ້ໃຊ້ MD5 ເປັນຫຼັກໃນການລະບຸໄຟລ໌. ໄຟລ໌ອາດຈະປາກົດຢູ່ໃນຫ້ອງສະໝຸດ shadow ຫຼາຍແຫ່ງ. ສຳລັບຂໍ້ມູນເກື່ອງກຽວກັບ datasets ຕ່າງໆທີ່ພວກເຮົາໄດ້ຮວບຮວມ, ກະລຸນາເບິ່ງ <a %(a_datasets)s>ໜ້າ Datasets</a>. ລາຍງານຄຸນນະພາບໄຟລ໌ ຈຳນວນການດາວໂຫຼດທັງໝົດ: %(total)s CADAL SSNO %(id)s} ສູນກາງສຳລັບການສຶກສາຂອງລາຕິນອາເມລິກາ %(id)s} ຂໍ້ມູນເມຕາຂອງເຊກ %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} ຄຳເຕືອນ: ບັນທຶກທີ່ເຊື່ອມໂຍງຫຼາຍອັນ: ເມື່ອທ່ານເບິ່ງປຶ້ມຢູ່ Anna’s Archive, ທ່ານຈະເຫັນຂໍ້ມູນຕ່າງໆ: ຊື່ເລື່ອງ, ຜູ້ແຕ່ງ, ຜູ້ພິມ, ສະບັບ, ປີ, ຄຳອະທິບາຍ, ຊື່ໄຟລ໌, ແລະອື່ນໆ. ຂໍ້ມູນເຫຼົ່ານີ້ທັງໝົດເອີ້ນວ່າ <em>metadata</em>. ເພາະພວກເຮົາຮວບຮວມປື້ມຈາກ <em>ຫໍສະໝຸດຕົ້ນທາງ</em> ຫຼາຍໆ, ພວກເຮົາຈະສະແດງ metadata ທີ່ມີຢູ່ໃນຫໍສະໝຸດຕົ້ນທາງນັ້ນ. ຕົວຢ່າງ, ສຳລັບປື້ມທີ່ພວກເຮົາໄດ້ມາຈາກ Library Genesis, ພວກເຮົາຈະສະແດງຊື່ປື້ມຈາກຖານຂໍ້ມູນຂອງ Library Genesis. ບາງຄັ້ງປື້ມຫນຶ່ງມີຢູ່ໃນ <em>ຫໍສະໝຸດຕົ້ນທາງຫຼາຍໆ</em>, ທີ່ອາດມີຊ່ອງຂໍ້ມູນ metadata ທີ່ແຕກຕ່າງກັນ. ໃນກໍລະນີນັ້ນ, ພວກເຮົາຈະສະແດງສະມາຊິກທີ່ຍາວທີ່ສຸດຂອງແຕ່ລະຊ່ອງ, ເພາະວ່າມັນຫວັງວ່າຈະມີຂໍ້ມູນທີ່ມີປະໂຫຍດທີ່ສຸດ! ພວກເຮົາຈະຍັງສະແດງຊ່ອງອື່ນໆຢູ່ຂ້າງລຸ່ມຂອງຄຳອະທິບາຍ, ເຊັ່ນ “ຊື່ທາງເລືອກ” (ແຕ່ຖ້າມັນແຕກຕ່າງກັນເທົ່ານັ້ນ). ພວກເຮົາຍັງສະກັດ <em>ລະຫັດ</em> ເຊັ່ນລະຫັດປະຈຳຕົວແລະລະຫັດຈັດປະເພດຈາກຫ້ອງສະໝຸດຕົ້ນສະບັບ. <em>ລະຫັດປະຈຳຕົວ</em> ຕົວແທນເລກທີ່ສະເພາະຂອງສຳນວນຫນຶ່ງຂອງປື້ມ; ຕົວຢ່າງເຊັ່ນ ISBN, DOI, Open Library ID, Google Books ID, ຫຼື Amazon ID. <em>ລະຫັດຈັດປະເພດ</em> ຈັດກຸ່ມປື້ມທີ່ຄ້າຍຄືກັນຫຼາຍໆ; ຕົວຢ່າງເຊັ່ນ Dewey Decimal (DCC), UDC, LCC, RVK, ຫຼື GOST. ບາງຄັ້ງລະຫັດເຫຼົ່ານີ້ຖືກເຊື່ອມໂຍງໂດຍກົງໃນຫ້ອງສະໝຸດຕົ້ນສະບັບ, ແລະບາງຄັ້ງພວກເຮົາສາມາດສະກັດພວກນັ້ນຈາກຊື່ໄຟລ໌ຫຼືຄຳອະທິບາຍ (ເປັນຫຼັກ ISBN ແລະ DOI). ພວກເຮົາສາມາດໃຊ້ຕົວຊີ້ບອກເພື່ອຄົ້ນຫາບັນທຶກໃນ <em>ຄັງສະມາຊິກທີ່ມີແຕ່ metadata</em>, ເຊັ່ນ OpenLibrary, ISBNdb, ຫຼື WorldCat/OCLC. ມີແຖບ <em>metadata</em> ເຉັດເຈັດໃນເຄື່ອງມືຄົ້ນຫາຂອງພວກເຮົາຖ້າທ່ານຕ້ອງການທີ່ຈະຄົ້ນຫາຄັງສະມາຊິກເຫຼົ່ານັ້ນ. ພວກເຮົາໃຊ້ບັນທຶກທີ່ກົງກັນເພື່ອເຕີມຊ່ອງ metadata ທີ່ຂາດຫາຍ (ເຊັ່ນ ຖ້າຊື່ຂາດຫາຍ), ຫຼືເຊັ່ນ “ຊື່ທາງເລືອກ” (ຖ້າມີຊື່ທີ່ມີຢູ່ແລ້ວ). ເພື່ອເບິ່ງວ່າຂໍ້ມູນເມຕາຂອງປື້ມມາຈາກໃສ, ເບິ່ງທີ່ <em>“ລາຍລະອຽດທາງເທັກນິກ”</em> ຢູ່ໜ້າປື້ມ. ມັນມີລິ້ງໄປຫາ JSON ດິບສຳລັບປື້ມນັ້ນ, ພ້ອມກັບຕຳແໜ່ງໄປຫາ JSON ດິບຂອງບັນທຶກຕົ້ນສະບັບ. ເພື່ອຂໍ້ມູນເພີ່ມເຕີມ, ເບິ່ງຫນ້າຕໍ່ໄປນີ້: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, ແລະ <a %(a_example)s>Example metadata JSON</a>. ສຸດທ້າຍ, ຂໍ້ມູນເມຕາທັງຫມົດຂອງພວກເຮົາສາມາດ <a %(a_generated)s>ສ້າງ</a> ຫຼື <a %(a_downloaded)s>ໂຫຼດ</a> ເປັນ ElasticSearch ແລະ MariaDB databases. ພື້ນຫຼັງ ທ່ານສາມາດຊ່ວຍການຮັກສາປຶ້ມໂດຍການປັບປຸງຂໍ້ມູນ metadata! ກ່ອນອື່ນ, ອ່ານພື້ນຖານກ່ຽວກັບ metadata ຢູ່ Anna’s Archive, ແລະຈາກນັ້ນຮຽນຮູ້ວິທີການປັບປຸງ metadata ຜ່ານການເຊື່ອມຕໍ່ກັບ Open Library, ແລະຮັບສະມາຊິກຟຣີຢູ່ Anna’s Archive. ປັບປຸງຂໍ້ມູນ metadata ດັ່ງນັ້ນຖ້າທ່ານພົບໄຟລ໌ທີ່ມີຂໍ້ມູນເມຕາທີ່ບໍ່ຖືກຕ້ອງ, ທ່ານຄວນແກ້ໄຂຢ່າງໃດ? ທ່ານສາມາດໄປທີ່ຫ້ອງສະໝຸດຕົ້ນສະບັບແລະປະຕິບັດຕາມຂັ້ນຕອນຂອງມັນເພື່ອແກ້ໄຂຂໍ້ມູນເມຕາ, ແຕ່ຈະເຮັດຢ່າງໃດຖ້າໄຟລ໌ຢູ່ໃນຫ້ອງສະໝຸດຕົ້ນສະບັບຫຼາຍໆ? ມີຕົວລະບຸຫນຶ່ງທີ່ຖືກປະຕິບັດເປັນພິເສດໃນ Anna’s Archive. <strong>ຂໍ້ມູນ md5 ຂອງ annas_archive ໃນ Open Library ມັກຈະເຫນືອກວ່າຂໍ້ມູນອື່ນໆທັງໝົດ!</strong> ຂໍໃຫ້ພວກເຮົາຖອຍກັບໄປກ່ອນແລະຮຽນຮູ້ກ່ຽວກັບ Open Library. Open Library ໄດ້ຖືກກໍ່ຕັ້ງຂຶ້ນໃນປີ 2006 ໂດຍ Aaron Swartz ດ້ວຍເປົ້າໝາຍຂອງ “ໜ້າເວັບຫນຶ່ງສຳລັບປື້ມທຸກເລື່ອງທີ່ເຄີຍຖືກພິມ”. ມັນເປັນຄືນຶ່ງຂອງ Wikipedia ສຳລັບຂໍ້ມູນເມຕາຂອງປື້ມ: ທຸກຄົນສາມາດແກ້ໄຂມັນໄດ້, ມັນມີໃບອະນຸຍາດຟຣີ, ແລະສາມາດໂຫຼດໄດ້ເປັນຈຳນວນຫຼາຍ. ມັນເປັນຖານຂໍ້ມູນປື້ມທີ່ສອດຄ້ອງກັບພັນທະກິດຂອງພວກເຮົາຫຼາຍທີ່ສຸດ — ຄວາມຈິງແລ້ວ, Anna’s Archive ໄດ້ຮັບແຮງບັນດານໃຈຈາກວິສັຍທັດແລະຊີວິດຂອງ Aaron Swartz. ແທນທີ່ຈະປະດິດລໍ້ໃໝ່, ພວກເຮົາຕັດສິນໃຈທີ່ຈະສົ່ງອາສາສະໝັກຂອງພວກເຮົາໄປທາງ Open Library. ຖ້າທ່ານເຫັນປຶ້ມທີ່ມີ metadata ຜິດພາດ, ທ່ານສາມາດຊ່ວຍໄດ້ດັ່ງນີ້: ຫມາຍເຫດວ່ານີ້ເຮັດໄດ້ສຳລັບປື້ມເທົ່ານັ້ນ, ບໍ່ໃຊ່ເອກະສານວິຊາການຫຼືໄຟລ໌ປະເພດອື່ນໆ. ສຳລັບໄຟລ໌ປະເພດອື່ນໆພວກເຮົາຍັງຄົງແນະນຳໃຫ້ຫາຫ້ອງສະໝຸດຕົ້ນສະບັບ. ອາດຈະໃຊ້ເວລາຫຼາຍອາທິດໃຫ້ການປ່ຽນແປງຖືກລວມເຂົ້າໃນ Anna’s Archive, ເພາະພວກເຮົາຕ້ອງດາວໂຫລດຂໍ້ມູນ Open Library ລ່າສຸດ, ແລະສ້າງດັດຊະນີການຄົ້ນຫາໃໝ່.  ໄປທີ່ <a %(a_openlib)s>ເວັບໄຊ Open Library</a>. ຊອກຫາບັນທຶກປື້ມທີ່ຖືກຕ້ອງ. <strong>ຄຳເຕືອນ:</strong> ຈົ່ງແນ່ໃຈວ່າເລືອກ <strong>ສຳນວນ</strong> ທີ່ຖືກຕ້ອງ. ໃນ Open Library, ທ່ານຈະມີ “works” ແລະ “editions”. “work” ອາດຈະເປັນ “Harry Potter and the Philosopher's Stone”. ສຳນວນຫນຶ່ງອາດຈະເປັນ: ສຳນວນສະບັບທຳອິດປີ 1997 ທີ່ພິມໂດຍ Bloomsbery ມີ 256 ໜ້າ. ສຳນວນປີ 2003 ສະບັບປົກອ່ອນທີ່ພິມໂດຍ Raincoast Books ມີ 223 ໜ້າ. ສຳນວນແປພາສາໂປໂລນປີ 2000 “Harry Potter I Kamie Filozoficzn” ໂດຍ Media Rodzina ມີ 328 ໜ້າ. ສຳນວນທັງໝົດນີ້ມີ ISBN ແລະເນື້ອຫາທີ່ແຕກຕ່າງກັນ, ດັ່ງນັ້ນກະລຸນາເລືອກສຳນວນທີ່ຖືກຕ້ອງ! ແກ້ໄຂບັນທຶກ (ຫຼືສ້າງໃໝ່ຖ້າບໍ່ມີຢູ່), ແລະເພີ່ມຂໍ້ມູນທີ່ມີປະໂຫຍດໃຫ້ຫຼາຍທີ່ສຸດ! ທ່ານຢູ່ນີ້ແລ້ວ, ກະລຸນາເຮັດໃຫ້ບັນທຶກນີ້ສຸດຍອດໄປເລີຍ. ພາຍໃຕ້ “ID Numbers” ເລືອກ “Anna’s Archive” ແລະເພີ່ມ MD5 ຂອງປື້ມຈາກ Anna’s Archive. ນີ້ແມ່ນສາຍຕົວອັກສອນແລະຕົວເລກທີ່ຍາວຫຼັງຈາກ “/md5/” ໃນ URL. ພະຍາຍາມຫາໄຟລ໌ອື່ນໆໃນ Anna’s Archive ທີ່ກ້ອງກັນກັບບັນທຶກນີ້, ແລະເພີ່ມເຫຼົ່ານັ້ນດ້ວຍ. ໃນອະນາຄົດພວກເຮົາສາມາດຈັດກຸ່ມເຫຼົ່ານັ້ນເປັນສຳເນົາຊ້ຳໃນໜ້າຄົ້ນຫາຂອງ Anna’s Archive. ເມື່ອທ່ານເຮັດສຳເລັດ, ຂຽນ URL ທີ່ທ່ານພຶມອັບເດດໄວ້. ເມື່ອທ່ານອັບເດດຢ່າງນ້ອຍ 30 ບັນທຶກທີ່ມີ MD5 ຂອງ Anna’s Archive, ສົ່ງ <a %(a_contact)s>ອີເມວ</a> ແລະສົ່ງລາຍຊື່ໃຫ້ພວກເຮົາ. ພວກເຮົາຈະໃຫ້ສະມາຊິກຟຣີສຳລັບ Anna’s Archive ໃຫ້ທ່ານ, ເພື່ອໃຫ້ທ່ານສາມາດເຮັດວຽກນີ້ໄດ້ງ່າຍຂຶ້ນ (ແລະເປັນການຂອບໃຈສຳລັບການຊ່ວຍເຫຼືອຂອງທ່ານ). ນີ້ຕ້ອງເປັນການແກ້ໄຂທີ່ມີຄຸນນະພາບສູງທີ່ເພີ່ມຂໍ້ມູນຈຳນວນຫຼາຍ, ມິດັ່ງນັ້ນຄຳຮ້ອງຂໍຂອງທ່ານຈະຖືກປະຕິເສດ. ຄຳຮ້ອງຂໍຂອງທ່ານຈະຖືກປະຕິເສດຫາກການແກ້ໄຂໃດໆຖືກຍົກເລີກຫຼືແກ້ໄຂໂດຍຜູ້ຄຸ້ມຄອງ Open Library. ການເຊື່ອມຕໍ່ Open Library ຖ້າທ່ານເຂົ້າມີສ່ວນຮ່ວມຢ່າງຫຼາຍໃນການພັດທະນາແລະການດຳເນີນງານຂອງພວກເຮົາ, ພວກເຮົາສາມາດຫາລືການແບ່ງປັນລາຍໄດ້ຈາກການບໍລິຈາກໃຫ້ທ່ານ, ເພື່ອໃຫ້ທ່ານນຳໄປໃຊ້ຕາມຄວາມຈຳເປັນ. ພວກເຮົາຈະຈ່າຍຄ່າໂຮສຕິ້ງກໍ່ຕໍ່ເມື່ອທ່ານຕັ້ງຄ່າທຸກຢ່າງສຳເລັດແລ້ວ ແລະໄດ້ສະແດງໃຫ້ເຫັນວ່າທ່ານສາມາດຮັກສາຄັງເອກະສານໃຫ້ທັນສະໄໝດ້ວຍການອັບເດດໄດ້. ນີ້ຫມາຍຄວາມວ່າທ່ານຈະຕ້ອງຈ່າຍຄ່າໃຊ້ຈ່າຍໃນເດືອນທຳອິດ-2 ອອກຈາກກະເປົາຂອງທ່ານເອງ. ບໍ່ມີການຊົດເຊີຍເວລາຂອງທ່ານ (ແລະກໍບໍ່ມີການຊົດເຊີຍເວລາຂອງພວກເຮົາ), ເນື່ອງຈາກນີ້ເປັນວຽກອາສາສະໝັກລ້ວນໆ. ພວກເຮົາຍິນຍອມທີ່ຈະຄຸ້ມຄອງຄ່າໃຊ້ຈ່າຍສຳລັບການໂຮສຕິງແລະ VPN, ໃນຕອນແລກສູງສຸດຖຶງ $200 ຕໍ່ເດືອນ. ນີ້ພຽງພໍສຳລັບເຊີບເວີຄົ້ນຫາພື້ນຖານແລະ proxy ທີ່ປອດໄພຈາກ DMCA. ຄ່າໃຊ້ຈ່າຍໃນການໂຮສຕິ້ງ ກະລຸນາ <strong>ຢ່າຕິດຕໍ່ພວກເຮົາ</strong> ເພື່ອຂໍອະນຸຍາດ, ຫຼືສຳລັບຄຳຖາມພື້ນຖານ. ການກະທຳສຳຄັນກວ່າຄຳພູດ! ຂໍ້ມູນທັງໝົດຢູ່ທີ່ນັ້ນແລ້ວ, ດັ່ງນັ້ນກະລຸນາດຳເນີນການຕັ້ງຄ່າກະຈົກຂອງທ່ານໄດ້ເລີຍ. ກະລຸນາຕິດຕໍ່ພວກເຮົາຜ່ານ Gitlab ຂອງພວກເຮົາເມື່ອທ່ານພົບປັນຫາ. ພວກເຮົາອາດຈະຕ້ອງສ້າງຄຸນສົມບັດພິເສດສຳລັບກະຈົກຂອງທ່ານ, ເຊັ່ນການປັບແຕ່ງຈາກ “Anna’s Archive” ເປັນຊື່ເວັບໄຊທ໌ຂອງທ່ານ, (ເບື້ອງຕົ້ນ) ປິດບັນຊີຜູ້ໃຊ້, ຫຼືການເຊື່ອມໂຍງກັບເວັບໄຊທ໌ຫຼັກຂອງພວກເຮົາຈາກຫນ້າຫນັງສື. ເມື່ອທ່ານມີກະຈົກຂອງທ່ານເຮັດວຽກແລ້ວ, ກະລຸນາຕິດຕໍ່ພວກເຮົາ. ພວກເຮົາຢາກທົດສອບ OpSec ຂອງທ່ານ, ແລະເມື່ອມັນແຂງແຮງແລ້ວ, ພວກເຮົາຈະເຊື່ອມໂຍງກັບກະຈົກຂອງທ່ານ, ແລະເລີ່ມຕົ້ນການເຮັດວຽກຮ່ວມກັນຢ່າງໃກ້ຊິດກັບທ່ານ. ຂອບໃຈລ່ວງໜ້າສຳລັບທຸກຄົນທີ່ຍິນດີທີ່ຈະມີສ່ວນຮ່ວມໃນວິທີນີ້! ມັນບໍ່ແມ່ນສຳລັບຄົນທີ່ມີໃຈອ່ອນແອ, ແຕ່ມັນຈະຊ່ວຍເສີມຄວາມຍືນຍາວຂອງຫ້ອງສະໝຸດທີ່ເປີດກວ້າງທີ່ສຸດໃນປະຫວັດສາດຂອງມະນຸດ. ເລີ່ມຕົ້ນ ເພື່ອເພີ່ມຄວາມທົນທານຂອງ Anna’s Archive, ພວກເຮົາກຳລັງມອງຫາອາສາສະໝັກເພື່ອເຮັດການສະທ້ອນ. ສຳນວນຂອງທ່ານແມ່ນແຍກແຍະຢ່າງຊັດເຈນເປັນການສະທ້ອນ, ເຊັ່ນ “Bob’s Archive, ການສະທ້ອນຂອງ Anna’s Archive”. ທ່ານຍິນຍອມທີ່ຈະຮັບຄວາມສຽງທີ່ມີຄວາມສຳຄັນກັບວຽກງານນີ້, ເຊິ່ງມີຄວາມສຳຄັນຫຼາຍ. ທ່ານມີຄວາມເຂົ້າໃຈຢ່າງລຶກຊຶ້ງກ່ຽວກັບຄວາມປອດໄພທາງການດຳເນີນງານທີ່ຕ້ອງການ. ເນື້ອຫາຂອງ <a %(a_shadow)s>ເຫຼົ່ານີ້</a> <a %(a_pirate)s>ບົດຄວາມ</a> ແມ່ນສິ່ງທີ່ປະຈັກຊັດແກ່ທ່ານ. ຕົ້ນຕໍ່ພວກເຮົາຈະບໍ່ໃຫ້ທ່ານເຂົ້າເຖິງການດາວໂຫລດຈາກເຊີບເວີຂອງຄູ່ຮ່ວມງານຂອງພວກເຮົາ, ແຕ່ຖ້າທຸລະກິດໄປໄດ້ດີ, ພວກເຮົາສາມາດແບ່ງປັນນັ້ນໃຫ້ທ່ານໄດ້. ທ່ານເຮັດການລະຫັດເປີດຊອດຂອງ Anna’s Archive, ແລະທ່ານປັບປຸງທັງລະຫັດແລະຂໍ້ມູນຢ່າງສະໝຳເສມອ. ທ່ານຍິນຍອມທີ່ຈະມີສ່ວນຮ່ວມກັບ <a %(a_codebase)s>ຖານຂໍ້ມູນໂຄດ</a> ຂອງພວກເຮົາ — ໃນການຮ່ວມມືກັບທີມງານຂອງພວກເຮົາ — ເພື່ອໃຫ້ມັນເກີດຂຶ້ນ. ພວກເຮົາກຳລັງມອງຫາສິ່ງນີ້: ກະຈົກ: ຂໍອາສາສະໝັກ ເຮັດການບໍລິຈາກອື່ນ. ຍັງບໍ່ມີການບໍລິຈາກ. <a %(a_donate)s>ເຮັດການບໍລິຈາກຄັ້ງທຳອິດຂອງຂ້ອຍ.</a> ລາຍລະອຽດການບໍລິຈາກບໍ່ໄດ້ຖືກສະແດງສາທາລະນະ. ການບໍລິຈາກຂອງຂ້ອຍ 📡 ສຳລັບການສະທ້ອນຂໍ້ມູນຈຳນວນຫຼາຍຂອງພວກເຮົາ, ກະລຸນາກວດເບິ່ງ <a %(a_datasets)s>Datasets</a> ແລະ <a %(a_torrents)s>Torrents</a> ໜ້າ. ການດາວໂຫຼດຈາກທີ່ຢູ່ IP ຂອງທ່ານໃນ 24 ຊົ່ວໂມງຜ່ານມາ: %(count)s. 🚀 ເພື່ອໄດ້ຮັບການດາວໂຫລດທີ່ໄວກວ່າແລະຂ້າມການກວດສອບຜ່ານບຣາວເຊີ, <a %(a_membership)s>ກາຍເປັນສະມາຊິກ</a>. ດາວໂຫຼດຈາກເວັບໄຊພາລເນີ ກະລຸນາດຳເນີນການຄົ້ນຫາຄັງເກັບຂອງ Anna ໃນແຖບອື່ນໃນຂະນະທີ່ລໍຖ້າ (ຖ້າເບຣາວເຊີຂອງທ່ານສະໜັບສະໜູນການຟື້ນຟູແຖບພື້ນຫຼັງ). ກະລຸນາລໍຖ້າໃຫ້ໜ້າດາວໂຫຼດຫຼາຍໆໜ້າໂຫຼດພ້ອມກັນ (ແຕ່ກະລຸນາດາວໂຫຼດໄຟລ໌ພຽງແຕ່ໜຶ່ງໄຟລ໌ຕໍ່ເວັບເຊີເທົ່ານັ້ນ). ເມື່ອທ່ານໄດ້ລິ້ງດາວໂຫຼດແລ້ວ, ມັນຈະມີອາຍຸໃຊ້ງານຫຼາຍຊົ່ວໂມງ. ຂອບໃຈທີ່ລໍຖ້າ, ນີ້ຊ່ວຍໃຫ້ເວັບໄຊທ໌ສາມາດເຂົ້າເຖິງໄດ້ໂດຍບໍ່ເສຍຄ່າສຳລັບທຸກຄົນ! 😊 🔗 ລິ້ງດາວໂຫລດທັງໝົດສຳລັບໄຟລ໌ນີ້: <a %(a_main)s>ໜ້າຫຼັກໄຟລ໌</a>. ❌ ການດາວໂຫຼດຊ້າບໍ່ມີຜ່ານ Cloudflare VPN ຫຼືຈາກທີ່ຢູ່ IP ຂອງ Cloudflare. ❌ ການດາວໂຫຼດຊ້າມີພຽງແຕ່ຜ່ານເວັບໄຊທ໌ທາງການ. ເຂົ້າຊົມ %(websites)s. 📚 ໃຊ້ URL ຕໍ່ໄປນີ້ເພື່ອດາວໂຫຼດ: <a %(a_download)s>ດາວໂຫຼດດຽວນີ້</a>. ເພື່ອໃຫ້ທຸກຄົນມີໂອກາດດາວໂຫລດໄຟລ໌ໂດຍບໍ່ມີຄ່າໃຊ້ຈ່າຍ, ທ່ານຈະຕ້ອງລໍຖ້າກ່ອນທີ່ຈະດາວໂຫລດໄຟລ໌ນີ້. ກະລຸນາລໍຖ້າ <span %(span_countdown)s>%(wait_seconds)s</span> ວິນາທີເພື່ອດາວໂຫລດໄຟລ໌ນີ້. ຄຳເຕືອນ: ມີການດາວໂຫຼດຈາກທີ່ຢູ່ IP ຂອງທ່ານຫຼາຍໃນ 24 ຊົ່ວໂມງຜ່ານມາ. ການດາວໂຫຼດອາດຈະຊ້າກວ່າປົກກະຕິ. ຖ້າທ່ານກຳລັງໃຊ້ VPN, ການແບ່ງປັນອິນເຕີເນັດຫຼືຜູ້ໃຫ້ບໍລິການ ISP ຂອງທ່ານແບ່ງປັນ IP, ຄຳເຕືອນນີ້ອາດຈະເປັນເພາະເຫດນັ້ນ. ບັນທຶກ ❌ ມີບາງຢ່າງຜິດພາດ. ກະລຸນາລອງໃໝ່. ✅ ບັນທຶກແລ້ວ. ກະລຸນາໂຫຼດໜ້າໃໝ່. ປ່ຽນຊື່ທີ່ສະແດງຂອງທ່ານ. ຕົວລະບຸຂອງທ່ານ (ສ່ວນທີ່ຢູ່ຫຼັງ “#”) ບໍ່ສາມາດປ່ຽນໄດ້. ສ້າງໂປຣໄຟລ໌ <span %(span_time)s>%(time)s</span> ແກ້ໄຂ ລາຍການ ສ້າງລາຍການໃໝ່ໂດຍການຊອກຫາໄຟລ໌ແລະເປີດແຖບ “ລາຍການ”. ຍັງບໍ່ມີລາຍການ ບໍ່ພົບໂປຣໄຟລ໌. ໂປຣໄຟລ໌ ຂະນະນີ້, ພວກເຮົາບໍ່ສາມາດຮັບຄຳຮ້ອງຂໍປຶ້ມໄດ້. ຢ່າສົ່ງອີເມວມາຫາພວກເຮົາດ້ວຍຄຳຮ້ອງຂໍຂອງທ່ານ. ກະລຸນາສົ່ງຄຳຮ້ອງຂໍຂອງທ່ານໃນບອດ Z-Library ຫຼື Libgen. ບັນທຶກໃນ ຄັງເກັບຂໍ້ມູນຂອງ Anna DOI: %(doi)s ດາວໂຫລດ SciDB Nexus/STC ບໍ່ມີພຣີວິວພ້ອມສຳລັບດາວໂຫລດໄຟລ໌ຈາກ <a %(a_path)s>ຄັງເກັບຂໍ້ມູນຂອງ Anna</a>. ເພື່ອສະໜັບສະໜູນການເຂົ້າເຖິງແລະການສະຫງວນຮັກສາຄວາມຮູ້ຂອງມະນຸດໃນລະຍະຍາວ, ກາຍເປັນ <a %(a_donate)s>ສະມາຊິກ</a>. ເປັນໂບນັດ, 🧬&nbsp;SciDB ໂຫລດໄວກວ່າສຳລັບສະມາຊິກ, ໂດຍບໍ່ມີຂໍ້ຈຳກັດໃດໆ. ບໍ່ເຮັດວຽກ? ລອງ <a %(a_refresh)s>ຣີເຟຣດ</a>. Sci-Hub ເພີ່ມສະເພາະສະຫນາມຄົ້ນຫາ ຄົ້ນຫາຄຳອະທິບາຍແລະຄຳເຫັນເມຕາດາຕ້າ ປີທີ່ຕີພິມ ຂັ້ນສູງ ການເຂົ້າເຖິງ ເນື້ອຫາ ສະແດງ ລາຍຊື່ ຕາຕະລາງ ປະເພດໄຟລ໌ ພາສາ ຈັດລຽງຕາມ ໃຫຍ່ທີ່ສຸດ ທີ່ສຳຄັນທີ່ສຸດ ໃໝ່ທີ່ສຸດ (ຂະໜາດໄຟລ໌) (ເປີດແຫຼ່ງ) (ປີທີ່ຕີພິມ) ເກົ່າທີ່ສຸດ ສຸ່ມ ນ້ອຍທີ່ສຸດ ແຫຼ່ງທີ່ມາ ຂຽນແລະເປີດເຜີຍໂດຍ AA ການຢືມດິຈິຕອນ (%(count)s) ບົດຄວາມວາລະສານ (%(count)s) ພວກເຮົາພົບການຕົກລົງໃນ: %(in)s. ທ່ານສາມາດອ້າງອີງ URL ທີ່ພົບເມື່ອ <a %(a_request)s>ຮ້ອງຂໍໄຟລ</a>. ຂໍ້ມູນເມຕາ (%(count)s) ເພື່ອສຳຫຼວດດັດຊະນີການຄົ້ນຫາໂດຍໂຄດ, ໃຊ້ <a %(a_href)s>Codes Explorer</a>. ດັດສະນີການຄົ້ນຫາຖືກປັບປຸງທຸກໆເດືອນ. ປັດຈຸບັນມີລາຍການຈົນເຖິງ %(last_data_refresh_date)s. ສຳລັບຂໍ້ມູນເພີ່ມເຕີມທາງເທັກນິກ, ກະລຸນາເບິ່ງ <a %(link_open_tag)s>ໜ້າຊຸດຂໍ້ມູນ</a>. ຍົກເວັ້ນ ລວມແຕ່ ບໍ່ໄດ້ກວດສອບ ເພີ່ມເຕີມ… ຕໍ່ໄປ … ກ່ອນໜ້າ ດັດສະນີການຄົ້ນຫານີ້ປັດຈຸບັນມີຂໍ້ມູນຈາກຫ້ອງສະໝຸດດິຈິຕອນທີ່ຄວບຄຸມການຢືມຂອງ Internet Archive. <a %(a_datasets)s>ເພີ່ມເຕີມກ່ຽວກັບຊຸດຂໍ້ມູນຂອງພວກເຮົາ</a>. ສຳລັບຫ້ອງສະໝຸດດິຈິຕອນທີ່ໃຫ້ຢືມເພີ່ມເຕີມ, ກະລຸນາເບິ່ງ <a %(a_wikipedia)s>Wikipedia</a> ແລະ <a %(a_mobileread)s>MobileRead Wiki</a>. ສຳລັບການຮ້ອງຮຽນສິດທິບັດ / ລິຂະສິດ <a %(a_copyright)s>ກົດທີ່ນີ້</a>. ເວລາດາວໂຫລດ ມີຂໍ້ຜິດພາດໃນການຄົ້ນຫາ. ລອງ <a %(a_reload)s>ໂຫຼດໜ້າໃໝ່</a>. ຖ້າບັນຫາຍັງຄົງຢູ່, ກະລຸນາສົ່ງອີເມວຫາພວກເຮົາທີ່ %(email)s. ດາວໂຫລດໄວ ແທ້ຈິງແລ້ວ, ຄົນໃດກໍສາມາດຊ່ວຍຮັກສາເອກະສານເຫຼົ່ານີ້ໄດ້ໂດຍການສົ່ງຕໍ່ <a %(a_torrents)s>ລາຍການທໍເຣນທີ່ຮວມກັນ</a>. ➡️ ບາງຄັ້ງນີ້ເກີດຂຶ້ນຜິດພາດເມື່ອເຊີບເວີຄົ້ນຫາຊ້າ. ໃນກໍລະນີນີ້, <a %(a_attrs)s>ການໂຫຼດໃໝ່</a> ສາມາດຊ່ວຍໄດ້. ❌ ໄຟລນີ້ອາດມີບັນຫາ. ກຳລັງມອງຫາເອກະສານບໍ? ດັດສະນີການຄົ້ນຫານີ້ປັດຈຸບັນມີຂໍ້ມູນຈາກແຫຼ່ງຂໍ້ມູນຕ່າງໆ. <a %(a_datasets)s>ເພີ່ມເຕີມກ່ຽວກັບຊຸດຂໍ້ມູນຂອງພວກເຮົາ</a>. ມີແຫຼ່ງຂໍ້ມູນຫຼາຍໆສຳລັບຜົນງານຂຽນທົ່ວໂລກ. <a %(a_wikipedia)s>ໜ້າ Wikipedia ນີ້</a> ເປັນຈຸດເລີ່ມຕົ້ນທີ່ດີ, ແຕ່ຖ້າທ່ານຮູ້ຈັກລາຍການທີ່ດີອື່ນໆ, ກະລຸນາແຈ້ງໃຫ້ພວກເຮົາຮູ້. ສຳລັບຂໍ້ມູນ, ພວກເຮົາສະແດງບັນທຶກຕົ້ນສະບັບ. ພວກເຮົາບໍ່ໄດ້ຄວບຄຸມການລວມບັນທຶກ. ພວກເຮົາມີລາຍການເປີດທີ່ຄົບຖ້ວນທີ່ສຸດໃນໂລກຂອງປື້ມ, ເອກະສານ, ແລະຜົນງານຂຽນອື່ນໆ. ພວກເຮົາສະທ້ອນ Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ແລະອື່ນໆ</a>. <span class="font-bold">ບໍ່ພົບໄຟລ.</span> ລອງໃຊ້ຄຳຄົ້ນຫາຫຼືຕົວກອງທີ່ແຕກຕ່າງກັນ. ຜົນການຄົ້ນຫາ %(from)s-%(to)s (%(total)s ທັງໝົດ) ຖ້າທ່ານພົບຫ້ອງສະໝຸດ “ເງົາ” ອື່ນໆທີ່ພວກເຮົາຄວນສະທ້ອນ, ຫຼືຖ້າທ່ານມີຄຳຖາມໃດໆ, ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(email)s. %(num)d ການຕົກລົງບາງສ່ວນ %(num)d+ ການຕົກລົງບາງສ່ວນ ພິມໃນກ່ອງເພື່ອຄົ້ນຫາເອກະສານໃນຫ້ອງສະໝຸດດິຈິຕອນທີ່ໃຫ້ຢືມ. ພິມໃນກ່ອງເພື່ອຄົ້ນຫາລາຍການຂອງເອກະສານ %(count)s ທີ່ສາມາດດາວໂຫລດໄດ້ໂດຍກົງ, ທີ່ພວກເຮົາ <a %(a_preserve)s>ຮັກສາຕະຫຼອດໄປ</a>. ພິມໃນກ່ອງເພື່ອຄົ້ນຫາ. ພິມໃນກ່ອງເພື່ອຄົ້ນຫາລາຍການຂອງເອກະສານວິຊາການ ແລະບົດຄວາມວາລະສານ %(count)s, ທີ່ພວກເຮົາ <a %(a_preserve)s>ຮັກສາຕະຫຼອດໄປ</a>. ພິມໃນກ່ອງເພື່ອຄົ້ນຫາຂໍ້ມູນຈາກຫ້ອງສະໝຸດ. ນີ້ອາດຈະເປັນປະໂຫຍດເມື່ອ <a %(a_request)s>ຮ້ອງຂໍເອກະສານ</a>. ເຄັມລັບ: ໃຊ້ປຸ່ມລັດ “/” (ໂຟກັດການຄົ້ນຫາ), “enter” (ຄົ້ນຫາ), “j” (ຂຶ້ນ), “k” (ລົງ), “<” (ໜ້າກ່ອນ), “>” (ໜ້າຕໍ່ໄປ) ເພື່ອການນຳທາງທີ່ໄວກວ່າ. ນີ້ແມ່ນບັນທຶກຂໍ້ມູນ, <span %(classname)s>ບໍ່ແມ່ນ</span> ໄຟລ໌ທີ່ສາມາດດາວໂຫລດໄດ້. ຕັ້ງຄ່າການຄົ້ນຫາ ຄົ້ນຫາ ການຢືມຢືມດິຈິຕອນ ດາວໂຫລດ ບົດຄວາມວາລະສານ ເມຕາດາຕ້າ ການຄົ້ນຫາໃໝ່ %(search_input)s - ຄົ້ນຫາ ການຄົ້ນຫາໃຊ້ເວລາດົນເກີນໄປ, ເຊິ່ງຫມາຍຄວາມວ່າທ່ານອາດຈະເຫັນຜົນທີ່ບໍ່ແມ່ນຄວາມຈິງ. ບາງຄັ້ງ <a %(a_reload)s>ການໂຫຼດໜ້າໃໝ່</a> ຊ່ວຍໄດ້. ການຄົ້ນຫາໃຊ້ເວລາດົນເກີນໄປ, ເປັນສິ່ງທີ່ພົບເຫັນໄດ້ບໍ່ຍາກສຳລັບການຄົ້ນຫາທີ່ກວ້າງ. ຈຳນວນການກັ່ນຕອງອາດຈະບໍ່ແມ່ນຄວາມຈິງ. ສຳລັບການອັບໂຫລດຂະໜາດໃຫຍ່ (ເກີນ 10,000 ໄຟລ໌) ທີ່ບໍ່ໄດ້ຮັບການຍອມຮັບໂດຍ Libgen ຫຼື Z-Library, ກະລຸນາຕິດຕໍ່ພວກເຮົາທີ່ %(a_email)s. ສໍາລັບ Libgen.li, ຈົ່ງແນ່ໃຈວ່າໄດ້ເຂົ້າລະບົບທີ່ <a %(a_forum)s >ບອດຂອງພວກເຂົາ</a> ດ້ວຍຊື່ຜູ້ໃຊ້ %(username)s ແລະລະຫັດຜ່ານ %(password)s, ແລ້ວຈຶ່ງກັບໄປທີ່ <a %(a_upload_page)s >ໜ້າອັບໂຫລດ</a> ຂອງພວກເຂົາ. ຕອນນີ້, ພວກເຮົາແນະນຳໃຫ້ອັບໂຫລດປື້ມໃໝ່ໄປທີ່ສາຂາຂອງ Library Genesis. ນີ້ແມ່ນ <a %(a_guide)s>ຄູ່ມືທີ່ມີປະໂຫຍດ</a>. ຂໍໃຫ້ສັງເກດວ່າທັງສອງສາຂາທີ່ພວກເຮົາດັດສະນີໃນເວັບໄຊທ໌ນີ້ດຶງມາຈາກລະບົບການອັບໂຫລດດຽວກັນ. ສໍາລັບການອັບໂຫລດຂະໜາດນ້ອຍ (ສູງສຸດ 10,000 ໄຟລ໌) ກະລຸນາອັບໂຫລດພວກມັນໄປທີ່ທັງ %(first)s ແລະ %(second)s. ທາງເລືອກ, ທ່ານສາມາດອັບໂຫລດພວກມັນໄປທີ່ Z-Library <a %(a_upload)s>ທີ່ນີ້</a>. ເພື່ອອັບໂຫລດເອກະສານວິຊາການ, ກະລຸນາອັບໂຫລດເພີ່ມເຕີມທີ່ <a %(a_stc_nexus)s>STC Nexus</a> (ເພີ່ມເຕີມຈາກ Library Genesis). ພວກເຂົາເປັນຫ້ອງສະໝຸດເງົາທີ່ດີທີ່ສຸດສຳລັບເອກະສານໃໝ່. ພວກເຮົາຍັງບໍ່ໄດ້ປະສານກັບພວກເຂົາ, ແຕ່ພວກເຮົາຈະເຮັດໃນບາງຈຸດ. ທ່ານສາມາດໃຊ້ <a %(a_telegram)s>upload bot on Telegram</a> ຂອງພວກເຂົາ, ຫຼືຕິດຕໍ່ທີ່ຢູ່ທີ່ລະບຸໄວ້ໃນຂໍ້ຄວາມປັກໝຸດຖ້າທ່ານມີເອກະສານຫຼາຍເກີນໄປທີ່ຈະອັບໂຫລດແບບນີ້. <span %(label)s>ວຽກອາສາສະໝັກຫນັກ (ລາຄາລາວັນ USD$50-USD$5,000):</span> ຖ້າທ່ານສາມາດອຸທິດເວລາຫຼາຍແລະ/ຫຼືຊັບພະຍາກອນໃຫ້ກັບພວກເຮົາ, ພວກເຮົາຢາກເຮັດວຽກໃກ້ຊິດກັບທ່ານຫຼາຍຂຶ້ນ. ໃນທ້າຍທີ່ທ່ານສາມາດເຂົ້າຮ່ວມທີມພາຍໃນໄດ້. ເຖິງແມ່ນວ່າພວກເຮົາມີງົບປະມານທີ່ຈຳກັດ, ແຕ່ພວກເຮົາສາມາດມອບ <span %(bold)s>💰 ລາວັນເງິນສົດ</span> ສຳລັບວຽກທີ່ຫນັກທີ່ສຸດ. <span %(label)s>ວຽກອາສາສະໝັກເບົາ:</span> ຖ້າທ່ານສາມາດສະໜອງເວລາພຽງບໍ່ກີ່ຊົ່ວໂມງທີ່ນີ້ແລະທີ່ນັ້ນ, ຍັງມີຫຼາຍວິທີທີ່ທ່ານສາມາດຊ່ວຍເຫຼືອໄດ້. ພວກເຮົາມອບລາງວັນໃຫ້ກັບອາສາສະໝັກທີ່ມີຄວາມສະໝໍາເສມອງດ້ວຍ <span %(bold)s>🤝 ການເປັນສະມາຊິກຂອງຄັງເອກະສານຂອງ Anna</span>. ຄັງເອກະສານຂອງ Anna ພຶງພາອາສາສະໝັກເຊັ່ນທ່ານ. ພວກເຮົາຕ້ອນຮັບທຸກລະດັບຂອງການມອບໝາຍ, ແລະມີສອງປະເພດຫຼັກຂອງການຊ່ວຍເຫຼືອທີ່ພວກເຮົາກຳລັງມອງຫາ: ຖ້າທ່ານບໍ່ສາມາດອາສາສະໝັກເວລາຂອງທ່ານໄດ້, ທ່ານຍັງສາມາດຊ່ວຍພວກເຮົາໄດ້ຫຼາຍໂດຍ <a %(a_donate)s>ບໍລິຈາກເງິນ</a>, <a %(a_torrents)s>ສົ່ງທໍເຣນຂອງພວກເຮົາ</a>, <a %(a_uploading)s>ອັບໂຫລດປື້ມ</a>, ຫຼື <a %(a_help)s>ບອກເພື່ອນຂອງທ່ານກ່ຽວກັບ Anna’s Archive</a>. <span %(bold)s>ບໍລິສັດ:</span> ພວກເຮົາມີການເຂົ້າເຖິງຄວາມໄວສູງຕໍ່າງໆໃນການເກັບຮວບຂອງພວກເຮົາແລກປ່ຽນກັບການບໍລິຈາກລະດັບອົງການຫຼືແລກປ່ຽນກັບການເກັບຮວບໃໝ່ (ເຊັ່ນ ການສະແກນໃໝ່, ຊຸດຂໍ້ມູນ OCR, ການສຸມເພີ່ມຂໍ້ມູນຂອງພວກເຮົາ). <a %(a_contact)s>ຕິດຕໍ່ພວກເຮົາ</a> ຖ້ານີ້ເປັນທ່ານ. ເບິ່ງອີກທີ່ <a %(a_llm)s>ໜ້າ LLM ຂອງພວກເຮົາ</a>. ລາງວັນ ພວກເຮົາກຳລັງມອງຫາຄົນທີ່ມີທັກສະການຂຽນໂປຣແກຣມຫຼືທັກສະການຮຸກຮາຄວາມປອດໄພທີ່ແຂງແກ່ວເພື່ອມາມີສ່ວນຮ່ວມ. ທ່ານສາມາດສ້າງຜົນກະທົບຢ່າງຈິງຈັງໃນການຮັກສາມໍລະດົກຂອງມະນຸດ. ເປັນການຂອບໃຈ, ພວກເຮົາແຈກການເປັນສະມາຊິກສຳລັບການມອບໝາຍທີ່ແຂງແຮງ. ເປັນການຂອບໃຈຢ່າງຫຼາຍ, ພວກເຮົາແຈກລາງວັນເງິນສຳລັບວຽກງານທີ່ສຳຄັນແລະຍາກຫຼາຍ. ນີ້ບໍ່ຄວນຖືກມອງເປັນການແທນທີ່ສຳລັບວຽກງານ, ແຕ່ມັນເປັນການສົ່ງເສີມເພີ່ມເຕີມແລະສາມາດຊ່ວຍກັບຄ່າໃຊ້ຈ່າຍທີ່ເກີດຂຶ້ນ. ສ່ວນໃຫຍ່ຂອງໂຄດຂອງພວກເຮົາແມ່ນເປີດເປັນຊອບແວທີ່ເປີດແຫຼ່ງທີ່ມາ, ແລະພວກເຮົາຈະຂໍໃຫ້ໂຄດຂອງທ່ານເປັນເຊັ່ນນັ້ນເມື່ອມອບລາງວັນ. ມີບາງກໍລະນີທີ່ພວກເຮົາສາມາດຫາລືກັນເປັນລາຍບຸກຄົນໄດ້. ລາງວັນຈະຖືກມອບໃຫ້ກັບຄົນທຳວຽກສຳເລັດຄົນທຳອິດ. ກະລຸນາຄອມເມັນຕໍ່ລາງວັນເພື່ອໃຫ້ຄົນອື່ນຮູ້ວ່າທ່ານກຳລັງເຮັດວຽກຢູ່, ເພື່ອໃຫ້ຄົນອື່ນລໍຖ້າຫຼືຕິດຕໍ່ທ່ານເພື່ອຮ່ວມມື. ແຕ່ກະລຸນາຮູ້ວ່າຄົນອື່ນຍັງສາມາດເຮັດວຽກຢູ່ເຊັ່ນກັນແລະພະຍາຍາມເອົາລາງວັນກ່ອນທ່ານ. ແຕ່ພວກເຮົາບໍ່ມອບລາງວັນສຳລັບວຽກທີ່ບໍ່ມີຄຸນະພາບ. ຖ້າມີການສົ່ງວຽກທີ່ມີຄຸນະພາບສູງສອງຄັ້ງທີ່ໃກ້ກັນ (ພາຍໃນຫນຶ່ງຫຼືສອງມື້), ພວກເຮົາອາດຈະເລືອກມອບລາງວັນໃຫ້ທັງສອງ, ຕາມດຸລິພິນຂອງພວກເຮົາ, ເຊັ່ນ 100%% ສຳລັບການສົ່ງຄັ້ງທຳອິດແລະ 50%% ສຳລັບການສົ່ງຄັ້ງທີສອງ (ດັ່ງນັ້ນລວມເປັນ 150%%). ສຳລັບລາງວັນທີ່ໃຫຍ່ກວ່າ (ເປັນພິເສດລາງວັນການຂູດຂໍ້ມູນ), ກະລຸນາຕິດຕໍ່ພວກເຮົາເມື່ອທ່ານໄດ້ສຳເລັດ ~5%% ຂອງມັນ, ແລະທ່ານມີຄວາມໝັ້ນໃຈວ່າວິທີການຂອງທ່ານຈະຂະຫຍາຍໄປສູ່ເປົ້າໝາຍທີ່ຕັ້ງໄວ້ໄດ້. ທ່ານຈະຕ້ອງແບ່ງປັນວິທີການຂອງທ່ານກັບພວກເຮົາເພື່ອທີ່ພວກເຮົາຈະໃຫ້ຄຳແນະນຳ. ນອກຈາກນີ້, ວິທີນີ້ພວກເຮົາສາມາດຕັດສິນວ່າຈະເຮັດຢ່າງໃດຖ້າມີຄົນຫຼາຍຄົນທີ່ກຳລັງເຂົ້າໃກ້ກັບລາງວັນ, ເຊັ່ນການມອບລາງວັນໃຫ້ຄົນຫຼາຍຄົນ, ການສົ່ງເສີມໃຫ້ຄົນຮ່ວມມືກັນ, ອື່ນໆ. ຄຳເຕືອນ: ວຽກທີ່ມີລາງວັນສູງແມ່ນ <span %(bold)s>ຍາກ</span> — ມັນອາດຈະດີທີ່ຈະເລີ່ມຕົ້ນດ້ວຍວຽກທີ່ງ່າຍກ່ອນ. ໄປທີ່ <a %(a_gitlab)s>ລາຍການປັນຫາຂອງ Gitlab</a> ແລະຈັດລຽງຕາມ “Label priority”. ນີ້ຈະສະແດງລຳດັບຂອງວຽກທີ່ພວກເຮົາໃສ່ໃຈ. ວຽກທີ່ບໍ່ມີລາງວັນຢ່າງເປັນທາງການຍັງຄົງມີສິດໄດ້ຮັບສະມາຊິກ, ເປັນພິເສດວຽກທີ່ຖືກຕິດປ້າຍ “Accepted” ແລະ “Anna’s favorite”. ທ່ານອາດຈະຕ້ອງການເລີ ວຽກອາສາສະໝັກເບົາ ພວກເຮົາມີຊ່ອງ Matrix ທີ່ປັບປຸງໃໝ່ຢູ່ທີ່ %(matrix)s. ຖ້າທ່ານມີເວລາຫຼາຍຊົ່ວໂມງ, ທ່ານສາມາດຊ່ວຍເຮັດວຽກໄດ້ຫຼາຍວິທີ. ຢ່າລືມເຂົ້າຮ່ວມ <a %(a_telegram)s>ການສົນທະນາອາສາສະໝັກທີ່ Telegram</a>. ເປັນການຂອບໃຈ, ພວກເຮົາມັກຈະໃຫ້ 6 ເດືອນຂອງ “ບິບຣາຣີແຫ່ງໂຊກດີ” ສຳລັບການບັນລຸຂັ້ນພື້ນຖານ, ແລະຫຼາຍກວ່ານັ້ນສຳລັບວຽກອາສາສະໝັກຕໍ່ເນື່ອງ. ທຸກຂັ້ນຕອນຕ້ອງການວຽກທີ່ມີຄຸນນະພາບສູງ — ວຽກທີ່ບໍ່ດີຈະເຮັດໃຫ້ພວກເຮົາເສຍຫາຍຫຼາຍກວ່າທີ່ຈະຊ່ວຍແລະພວກເຮົາຈະປະຕິເສດມັນ. ກະລຸນາ <a %(a_contact)s>ອີເມວຫາພວກເຮົາ</a> ເມື່ອທ່ານບັນລຸຂັ້ນຕອນ. %(links)s ລິ້ງຫຼືຮູບພາບຫນ້າຈໍຂອງຄຳຮ້ອງຂໍທີ່ທ່ານໄດ້ສຳເລັດ. ຕອບສະໜອງຄຳຂໍປື້ມ (ຫຼືເອກະສານ, ແລະອື່ນໆ) ໃນຟອຣັມ Z-Library ຫຼື Library Genesis. ພວກເຮົາບໍ່ມີລະບົບຂໍປື້ມຂອງຕົວເອງ, ແຕ່ພວກເຮົາສະທ້ອນຫ້ອງສະໝຸດເຫຼົ່ານັ້ນ, ດັ່ງນັ້ນການເຮັດໃຫ້ພວກເຂົາດີຂຶ້ນຈະເຮັດໃຫ້ Anna’s Archive ດີຂຶ້ນດ້ວຍ. ຂັ້ນຕອນ ວຽກ ຂຶ້ນກັບວຽກ. ວຽກເລັກໆທີ່ໂພສໃນ <a %(a_telegram)s>ການສົນທະນາອາສາສະໝັກທີ່ Telegram</a>. ປົກກະຕິສຳລັບການເປັນສະມາຊິກ, ບາງຄັ້ງສຳລັບລາວັນເລັກໆ. ວຽກນ້ອຍໆທີ່ໂພສໃນກຸ່ມສົນທະນາອາສາສະໝັກຂອງພວກເຮົາ. ໃຫ້ແນ່ໃຈວ່າໃສ່ຄໍາເຫັນໃນບັນຫາທີ່ທ່ານແກ້ໄຂ, ເພື່ອບໍ່ໃຫ້ຜູ້ອື່ນທໍາຊໍ້ງານຂອງທ່ານ. %(links)s ລິ້ງຂອງບັນທຶກທີ່ທ່ານໄດ້ປັບປຸງ. ທ່ານສາມາດໃຊ້ <a %(a_list)s >ລາຍຊື່ຂອງບັນຫາ metadata ສຸ່ມ</a> ເປັນຈຸດເລີ່ມຕົ້ນ. ປັບປຸງຂໍ້ມູນໂດຍ <a %(a_metadata)s>ເຊື່ອມຕໍ່</a> ກັບ Open Library. ເຫຼົ່ານີ້ຄວນສະແດງໃຫ້ເຫັນວ່າທ່ານແຈ້ງໃຫ້ຜູ້ໃດຜູ້ໜຶ່ງຮູ້ກ່ຽວກັບ ຄັງເກັບຂໍ້ມູນຂອງ Anna, ແລະພວກເຂົາຂອບໃຈທ່ານ. %(links)s ລິ້ງຫຼືຮູບພາບຫນ້າຈໍ. ການແຜ່ຂ່າວຂອງ ຄັງເກັບຂໍ້ມູນຂອງ Anna. ຕົວຢ່າງເຊັ່ນ, ໂດຍການແນະນຳປື້ມໃນ AA, ລິ້ງໄປຫາບົດຄວາມບລັອກຂອງພວກເຮົາ, ຫຼືໂດຍທົ່ວໄປຊີ້ນຳຜູ້ຄົນໄປທີ່ເວັບໄຊຂອງພວກເຮົາ. ແປພາສາໃຫ້ຄົບຖ້າມັນບໍ່ໃກ້ສຳເລັດແລ້ວ. <a %(a_translate)s>ແປພາສາ</a> ເວັບໄຊທ໌. ລິ້ງໄປຫາປະຫວັດການແກ້ໄຂທີ່ສະແດງໃຫ້ເຫັນວ່າທ່ານໄດ້ມີສ່ວນຮ່ວມຢ່າງສຳຄັນ. ປັບປຸງໜ້າ Wikipedia ຂອງ Anna’s Archive ໃນພາສາຂອງທ່ານ. ລວມຂໍ້ມູນຈາກໜ້າ Wikipedia ຂອງ AA ໃນພາສາອື່ນໆ, ແລະຈາກເວັບໄຊທ໌ແລະບລັອກຂອງພວກເຮົາ. ເພີ່ມການອ້າງອີງເຖິງ AA ໃນໜ້າທີ່ກ່ຽວຂ້ອງອື່ນໆ. ການອາສາສະໝັກ & ລາງວັນ 