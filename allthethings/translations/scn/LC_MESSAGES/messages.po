#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Richiesta invalida. Visita %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " e "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "e cchiù"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nui mirriamu %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Nui scrapiamu e aprimu u codice %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Tuttu u nostru codice e dati sunnu cumpletamenti open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;A biblioteca cchiù granni e veramente aperta nta la storia umana."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libbra, %(paper_count)s&nbsp;articuli — preservati pi sempri."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;A biblioteca open-source e open-data cchiù granni d'u munnu. ⭐️&nbsp;Mirra Sci-Hub, Library Genesis, Z-Library, e cchiù. 📈&nbsp;%(book_any)s libbra, %(journal_article)s artìculi, %(book_comic)s fumetti, %(magazine)s rivisti — preservati pi sempri."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 La biblioteca open-source e open-data cchiù granni dû munnu.<br>⭐️ Specchi Scihub, Libgen, Zlib, e cchiù."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata sbagghiatu (p'esempiu: tìtulu, discrizzioni, mmàggini di copertina)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Prubbremi di scaricamentu (p'esempiu: nun si pò cunnèttiri, missaggiu d'erruri, assai lentu)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Nun si pò apriri u file (p'esempiu: file curruttu, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Pèssima qualità (p'esempiu: prubbremi di furmattazzioni, qualità di scansioni pèssima, paggini mancanti)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / u file avissi a èssiri livatu (p'esempiu: publicità, cuntinutu abusivu)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Rivendicazzioni di copyright"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Àutru"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Download bonus"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Libru Luminusu"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Bibliutecariu Fortunatu"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Accattaturi di Dati"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Archivista Ammirèvuli"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s tutali"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) tutali"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "nun pagatu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "pagatu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "annullatu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "scadutu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "aspittannu ca Anna cunfirma"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "nvalidu"

#, fuzzy
msgid "page.donate.title"
msgstr "Duna"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Hai na <a %(a_donation)s>donazzioni esistenti</a> in cursu. Pi favuri, finisci o annulla chidda donazzioni prima di fari na nova donazzioni."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Talìa tutti i me donazzioni</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "L'Archiviu di Anna è nu pruggettu senza scopu di lucro, open-source e open-data. Donannu e divintannu membru, susteni i nostri operazzioni e sviluppu. A tutti i nostri membri: grazie pi mantènirini in vita! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Pi cchiù nfurmazzioni, talìa lu <a %(a_donate)s>FAQ di Donazzioni</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Pi aviri ancora cchiù scaricamenti, <a %(a_refer)s>riferisci i tò amici</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Teni %(percentage)s%% bonus di scaricamenti veloci, pirchì fusti rifirutu di l'utenti %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Chistu vali pi tuttu lu pirìudu di membranza."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s scaricamenti veloci pi jornu"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "si doni stu misi!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / misi"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Unisciti"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selezziunatu"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "fino a %(percentage)s%% sconti"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Articuli SciDB <strong>illimitati</strong> senza verificazioni"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Accessu a <a %(a_api)s>API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Guadagna <strong>%(percentage)s%% bonus di scaricamenti</strong> rifirennu <a %(a_refer)s>amici</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Lu tò username o menzioni anonima nti li crèditi"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Previi vantaggi, cchiù:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Accessu anticipatu a novi funzioni"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram esclusivu cu aggiornamenti dietro le quinte"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adotta un torrent”: u vostru username o messaggiu in un nome di file torrent <div %(div_months)s>una vota ogni 12 misi di abbonamentu</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Status leggendariu nella preservazioni di la cunniscenza e cultura di l'umanità"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Accessu Expert"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "cuntattatici"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Semmu na piccula squatra di vuluntari. Pò pigghiari 1-2 simani pi rispunniri."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Illimitatu</strong> accessu ad alta velocità"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Server diretti <strong>SFTP</strong>"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donazioni a livellu d'impresa o scambiu pi novi collezioni (p.es. novi scansioni, datasets OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Accugghiemu granni donazioni di individui ricchi o istituzioni. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pi donazioni supiriori a $5000 cuntattatici direttamenti a %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Teni accura ca mentri i tipi di abbunamentu in sta pàggina sunnu \"per misi\", sunnu donazzioni una tantum (non ricorrenti). Vidi lu <a %(faq)s>FAQ Donazzioni</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Si vuliti fari na donazioni (di qualsiasi importu) senza abbonamentu, sentiti liberi di usari stu indirizzu Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Pi favuri, selezziuna un metudu di pagamentu."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporaneamenti indisponìbbili)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s carta rigalu"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carta bancaria (usannu app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carta di crèditu/debbitu"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regolari)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Carta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Carta di crèditu/debitu/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Carta bancaria"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carta di crèditu/debbitu (riserva)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carta di crèditu/debbitu 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Cù la cripto putiti fari na dunazzioni usannu BTC, ETH, XMR, e SOL. Usati sta opzioni si già canusciti la criptovaluta."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Cù la cripto putiti fari na dunazzioni usannu BTC, ETH, XMR, e àutri."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si è la prima vota ca usi la cripta, ti suggeremu di usari %(options)s pi accattari e dunari Bitcoin (la criptovaluta urigginali e cchiù usata)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Pi fari na dunazzioni usannu PayPal US, useremu PayPal Crypto, ca nni permette di ristari anonimi. Apprezzamu ca pigghiati lu tempu pi amparari comu fari na dunazzioni usannu stu metudu, pirchì nni aiuta assai."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Fai na dunazzioni usannu PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Fai na dunazzioni usannu Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si aviti Cash App, chista è la manera cchiù facili pi fari na dunazzioni!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Notate ca pi transazzioni sutta %(amount)s, Cash App pò addebitari na tassa di %(fee)s. Pi %(amount)s o cchiù, è gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Duna usannu Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Si hai Revolut, chistu è lu modu cchiù fàcili pi dunari!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Duna cu na carta di crèditu o dèbitu."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay pò funzionari puru."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Notate ca pi picculi donazzioni i tariffi di carta di crèditu pò eliminari u nostru scontu di %(discount)s%%, allura ricummannamu abbunamenti cchiù longhi."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Notate ca pi picculi donazzioni i tariffi su auti, allura ricummannamu abbunamenti cchiù longhi."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Cu Binance, accatti Bitcoin cu na carta di crèditu/dèbitu o cuntu bancariu, e poi doni chistu Bitcoin a nuatri. Accussì putemu ristari sicuri e anònimi quannu accittamu a tò donazzioni."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance è dispunìbbili in quasi tutti i paisi, e supporta a maiò parti di banche e carte di crèditu/dèbitu. Chistu è attualmente a nostra raccomandazzioni principali. Apprezzamu ca ti pigghi u tempu pi amparari comu donari usannu chistu metudu, pirchì ci aiuta assai."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Duna usannu lu tò cuntu regolari PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Duna usannu carta di crèditu/dèbitu, PayPal, o Venmo. Pò scegliri tra chisti na pàggina successiva."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Duna usannu na carta rigalu di Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Notate ca avemu bisognu di arrutunnari a quantità accittati di i nostri rivendituri (minimu %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Supportamu sulu Amazon.com, non àutri siti di Amazon. Pi esempiu, .de, .co.uk, .ca, NON su supportati."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Sta opzioni è pi' %(amazon)s. Si voi usari n'autru situ Amazon, sceglilu supra."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Stu metudu usa un fornituri di criptovaluta comu cunversioni intermediaria. Chistu pò essiri un pocu cunfusu, allura usalu sulu si l'àutri metudi di pagamentu non funzionanu. Non funziona puru in tutti i paisi."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Duna usannu na carta di crèditu/debitu, attraversu l'app Alipay (facilissima da cunfigurari)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installa l'app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installa l'app Alipay da l'<a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Rigistrati usannu lu tò numiru di telefunu."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Non sunnu nicissarii àutri dittagli personali."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Aggiungi carta bancaria"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supportati: Visa, MasterCard, JCB, Diners Club e Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Vidi <a %(a_alipay)s>stu guida</a> pi cchiù nfurmazzioni."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nun putemu suppurtari li carti di crèditu/debitu direttamenti, picchì li banche nun vonnu travagghiari cu nuatri. ☹ Tuttavia, ci sunnu diversi modi pi usari li carti di crèditu/debitu, usannu àutri metudi di pagamentu:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Carta Rigalu Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Manna a nuatri carte rigalu di Amazon.com usannu a tò carta di crèditu/dèbitu."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay suppurta carti di crèditu/debitu internazziunali. Vidi <a %(a_alipay)s>stu guida</a> pi cchiù nfurmazzioni."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) supporta carte di crèditu/dèbitu internazziunali. Nall'app di WeChat, vai a “Me => Services => Wallet => Add a Card”. Si non vidi chistu, abilitalu usannu “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Pò accattari cripto usannu carte di crèditu/dèbitu."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servizi cripto express"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Li servizi express sunnu cunvinienti, ma addebitanu tariffi cchiù àuti."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Pò usari chistu inveci di un scambiu cripto si voi fari na donazzioni cchiù granni rapidamenti e non ti dispiaci na tariffa di $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assicurati di mannari l'esatta quantità di cripto mustrata ntâ pàggina di donazzioni, non la quantità in $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Altrimenti la tariffa sarà sottratta e non putemu prucessari automaticamente lu tò abbunamentu."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimu: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimu: %(minimum)s secunnu lu paisi, nudda virificazzioni pi la prima transazzioni)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimu: %(minimum)s, nudda virificazzioni pi la prima transazzioni)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimu: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimu: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimu: %(minimum)s, nudda virificazzioni pi la prima transazzioni)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Siddu qualchi di sti nfurmazzioni è scaduta, pi favuri mannatici na email pi farinni sapiri."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Pi carte di crèditu, carte di dèbitu, Apple Pay, e Google Pay, usamu “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Nto so sistema, un “caffè” è uguali a $5, allura a tò donazzioni sarà arrutunnata a u multiplu cchiù vicinu di 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Scegli quantu tempu voi abbunari."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 misi"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 misi"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 misi"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 misi"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 misi"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 misi"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 misi"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>doppu <span %(span_discount)s></span> sconti</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Stu metudu di pagamentu richiedi un minimu di %(amount)s. Pi favuri, scegghi na durata o un metudu di pagamentu differente."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Duna"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Stu metudu di pagamentu permette sulu un massimu di %(amount)s. Pi favuri, scegghi na durata o un metudu di pagamentu differente."

#, fuzzy
msgid "page.donate.login2"
msgstr "Pi divintari membru, pi favuri <a %(a_login)s>Accedi o Registrati</a>. Grazzi pi lu tò sustegnu!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Scegghi la tò munita cripto preferita:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(minimu ammontu cchiù bassu)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(usari quannu si manda Ethereum da Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(avvertimentu: minimu ammontu àutu)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Clicca lu buttuni di donazioni pi cunfirmari sta donazioni."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Duna <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Pò ancora annullari la donazioni duranti lu checkout."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirigennu a la pàggina di donazioni…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ C'è statu un prubbrema. Pi favuri, ricarica la pàggina e pruva di novu."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "pi 1 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "pi 3 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "pi 6 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "pi 12 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "pi 24 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "pi 48 misi"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "pi 96 misi"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "pi 1 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "pi 3 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "pi 6 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "pi 12 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "pi 24 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "pi 48 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "pi 96 misi “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donazzioni"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Data: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Totali: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / misi pi %(duration)s misi, inclùdennu %(discounts)s%% scontu)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Totali: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / misi pi %(duration)s misi)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Statu: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identificaturi: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Annulla"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Sì sicuru ca voi annullari? Nun annullari si hai già pagatu."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Sì, annulla per piaciri"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ A tò donazioni è stata annullata."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Fai na nova donazioni"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ C'è statu un prubbrema. Pi favuri, ricarica la pàggina e pruva di novu."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Riordina"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Hai già pagatu. Si voi rivediri i struzzioni di pagamentu, clicca ccà:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Mostra i vecchi struzzioni di pagamentu"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Grazii pi a tò donazioni!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Si nun l'hai già fattu, scrivi a tò chiavi segreta pi accedi:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Altrimenti potresti essiri bloccatu fora di stu account!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "I struzzioni di pagamentu sunnu ora scaduti. Si voi fari n'autra donazioni, usa u buttuni “Riordina” supra."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota impurtanti:</strong> I prezzi di a cripta ponnu variari assai, a voti puru finu a 20%% in pochi minuti. Chistu è sempri menu di i tariffi ca avemu cu tanti fornitori di pagamentu, chi spissu caricanu 50-60%% pi travagghiari cu na “carità ombra” comu a nostra. <u>Si ci mandi a ricevuta cu u prezzu originali ca hai pagatu, accrediteremu sempri u tò account pi a membri sceltu</u> (finché a ricevuta nun è più vecchia di pochi uri). Apprezzamu assai ca si dispostu a suppurtari cosi comu chisti pi sustinirini! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Sta donazioni è scaduta. Per piaciri annulla e crea na nova."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Istruzzioni cripta"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Trasferisci a unu di i nostri conti cripta"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Dona u totali di %(total)s a unu di sti indirizzi:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Accatta Bitcoin supra Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Tròva la pàggina “Crypto” ntâ tò app o situ di PayPal. Di sòlitu è sutta “Finanze”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Segui li struzzioni pi accattari Bitcoin (BTC). Bisogna sulu accattari la quantità ca voi dunari, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Trasferisci lu Bitcoin a lu nostru indirizzu"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vai â pàggina “Bitcoin” ntâ tò app o situ di PayPal. Clicca lu buttuni “Trasferisci” %(transfer_icon)s, e poi “Manna”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Mèttici lu nostru indirizzu di Bitcoin (BTC) comu destinatariu, e segui li struzzioni pi mannari la tò dunazzioni di %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Struzzioni pi carta di crèditu / dèbitu"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Duna attraversu la nostra pàggina di carta di crèditu / dèbitu"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Duna %(amount)s supra <a %(a_page)s>sta pàggina</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Vidi la guida passu-pi-passu sutta."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Statu:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Aspittannu cunfirmazzioni (aggiorna la pàggina pi cuntrullari)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Aspittannu trasferimentu (aggiorna la pàggina pi cuntrullari)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tempu rimanenti:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(potresti vuliri annullari e criari na nova dunazzioni)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Pi azzerari lu timer, basta criari na nova dunazzioni."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Aggiorna statu"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Si t'incuntri n qualchi prubblema, pi favuri cuntattànni a %(email)s e inclùdici quantu cchiù nfurmazzioni pussìbbili (comu screenshot)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Si aviti già pagatu:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "A voti la cunfirmazzioni pò pigghiari finu a 24 uri, accussì assicurati di rinfrescari sta pàggina (ancora si è scaduta)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Accatta munita PYUSD supra PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Segui li struzzioni pi accattari munita PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Accatta un pocu cchiù (ricummannamu %(more)s cchiù) di chiddu chi stai dunannu (%(amount)s), pi cupriri li tariffi di transazzioni. Teni tuttu chiddu chi resta."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Vai a la pàggina “PYUSD” ntâ tò app o situ di PayPal. Clicca lu buttuni “Trasferisci” %(icon)s, e poi “Manna”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Trasferisci %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Accatta Bitcoin (BTC) supra Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vai â pàggina “Bitcoin” (BTC) ntâ Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Accatta un pocu cchiù (ricummannamu %(more)s cchiù) dâ quantità ca stai dunannu (%(amount)s), pi cupriri li tariffi di transazzioni. Teni chiddu ca resta."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Trasferisci li Bitcoin â nostra indirizzu"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clicca lu buttuni “Manna bitcoin” pi fari na “ritirata”. Cangia di dollari a BTC pigiannu l’icona %(icon)s. Mèttiti la quantità di BTC sutta e clicca “Manna”. Vidi <a %(help_video)s>stu video</a> si ti blocchi."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Pi picculi dunazzioni (sutta $25), putissi aviri bisognu di usari Rush o Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Accatta Bitcoin (BTC) supra Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Vai â pàggina “Crypto” ntâ Revolut pi accattari Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Accatta un pocu cchiù (ricummannamu %(more)s cchiù) dâ quantità ca stai dunannu (%(amount)s), pi cupriri li tariffi di transazzioni. Teni chiddu ca resta."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Trasferisci li Bitcoin â nostra indirizzu"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Clicca lu buttuni “Manna bitcoin” pi fari na “ritirata”. Cangia di euri a BTC pigiannu l’icona %(icon)s. Mèttiti la quantità di BTC sutta e clicca “Manna”. Vidi <a %(help_video)s>stu video</a> si ti blocchi."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Assicurati di usare l'importo in BTC sottu, <em>NON</em> euri o dollari, altrimenti non riceveremu l'importo currettu e non pudemu cunfirmari automaticamente a vostra adesione."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Pi picculi dunazzioni (sutta $25) putissi aviri bisognu di usari Rush o Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Usa unu di sti servizii “carta di crèditu a Bitcoin” express, ca pigghianu sulu qualchi minutu:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Cumplìti i seguenti dittagli ntô form:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Quantitati BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Pi favuri usati chistu <span %(underline)s>esattu ammontu</span>. U vostru custu tutali putissi èssiri cchiù àutu a causa di tariffi di carta di crèditu. Pi picculi ammonti, chistu putissi èssiri cchiù di lu nostru scontu, purtroppu."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Indirizzu BTC / Bitcoin (portafogghiu esternu):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s struzzioni"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Sustinemu sulu la virsioni standard di muniti cripto, nudda reta esotica o virsioni di muniti. Pò pigghiari finu a un'ura pi cunfirmari la transazzioni, secunnu la munita."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scansionare il codice QR da pagare"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scansiona questo codice QR con l'app Crypto Wallet per compilare rapidamente i dettagli di pagamento"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Carta rigalu di Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Pi favuri usa lu <a %(a_form)s>formu ufficiali di Amazon.com</a> pi mannari nu rigalu di %(amount)s a l'indirizzu email sutta."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Nun putemu accittari àutri mètudi di carti rigalu, <strong>sulu mannati direttamenti dû formu ufficiali supra Amazon.com</strong>. Nun putemu turnari la tò carta rigalu si nun usi stu formu."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Inserisci l'ammontu esattu: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Pi favuri nun scriviri un missaggiu tòiu."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Email dû destinatariu ntô formu:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unicu pi lu tò cuntu, nun cundividiri."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usa sulu na vota."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Aspittannu la carta rigalu… (aggiorna la pàggina pi cuntrullari)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Doppu aviri mannatu la tò carta rigalu, lu nostru sistema autumàticu la cunfirmerà ntra pochi minuti. Si nun funziona, prova a rimannari la tò carta rigalu (<a %(a_instr)s>struzzioni</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Si ancora nun funziona, pi favuri mannaci un'email e Anna la rivedrà manualmenti (chistu pò pigghiari qualchi jornu), e assicurati di menzionari si hai già pruvatu a rimannari."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Esempiu:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Nota chi lu nomu dû cuntu o la fotu pò pariri stranu. Nun ti prioccupari! Sti cunti sunnu gestiti di li nostri partinari di dunazzioni. Li nostri cunti nun sunnu stati attaccati."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Struzzioni di Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Duna su Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Duna la quantità tutali di %(total)s usannu <a %(a_account)s>stu cuntu Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Sè a pàgina di donazione hè bloccata, pruvate una cunnessione internet differente (per esempiu VPN o internet di u telefunu)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Purtroppo, la pàggina di Alipay è spissu accessìbbili sulu di <strong>Cina continentali</strong>. Pò essiri nicissariu disattivari temporaneamenti u vostru VPN, o usari un VPN versu la Cina continentali (o a voti funziona puru Hong Kong)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Fai la donazzioni (scansiona QR code o premi buttuni)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Apri la <a %(a_href)s>pàggina di donazzioni QR-code</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scansiona lu QR code cu l'app Alipay, o premi lu buttuni pi apriri l'app Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Pi favori, la pàggina putissi pigghiari un pocu di tempu pi carricari pirchì si trova in Cina."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Istruzzioni WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Duna su WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Duna la quantità tutali di %(total)s usannu <a %(a_account)s>stu cuntu WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Istruzzioni Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Duna su Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Duna la quantità tutali di %(total)s usannu <a %(a_account)s>stu cuntu Pix"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Mandàtini un'email cu la ricevuta"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Mannati na ricevuta o screenshot a lu vostru indirizzu di virificazzioni persunali. NON usati chistu indirizzu email pi la vostra dunazzioni PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Mandati na ricevuta o na screenshot a l'indirizzu di virìfica persunali:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Si u tassu di scambiu di cripto ha fluttuatu duranti la transazzioni, assicuràtivi di ncludiri la ricevuta ca mostra u tassu di scambiu urigginali. Apprezzamu assai ca vi pigghiati u disturbu di usari cripto, ci aiuta assai!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Quannu aviti mandatu l'email cu la ricevuta, cliccati stu buttuni, accussì Anna pò virificari manualmenti (chistu pò pigghiari qualchi jornu):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Sì, haiu mandatu l'email cu la ricevuta"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Grazii pi la tò dunazzioni! Anna attivarà manualmenti u tò abbunamentu intra qualchi jornu."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ C'è statu un prubbrema. Pi favuri, ricarica la pàggina e pruva di novu."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Guida passu-pi-passu"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Qualchi passu mènziona i portafogghi cripto, ma nun ti prioccupàri, nun hai bisognu di amparari nenti supra a cripto pi chistu."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Mèttiti l'email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Scegghi u tò mètudu di pagamentu."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selezziunate di novu u vostru metudu di pagamentu."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Selezziunate “Portafogliu autu-ospitatu”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Cliccate “Confermu a pruprietà”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Duvissi riceve una ricevuta per email. Per piacè, inviatela à noi, è cunfirmeremu a vostra donazione appena pussibule."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Aspetta almenu <span %(span_hours)s>24 uri</span> (e ricarica sta pàgina) prima di cuntattàrici."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Se avete fattu un sbagliu durante u pagamentu, ùn pudemu micca fà rimborsi, ma pruveremu à risolve u prublema."

#, fuzzy
msgid "page.my_donations.title"
msgstr "E mo donazioni"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "I dettagli di e donazioni ùn sò micca mostrati publicamente."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nisuna donazione ancu. <a %(a_donate)s>Fà a mo prima donazione.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fà un'altra donazione."

#, fuzzy
msgid "page.downloaded.title"
msgstr "File scaricati"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "I scaricamenti da i Servitori Partner Veloci sò marcati da %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Se avete scaricatu un schedariu cù scaricamenti veloci è lenti, apparirà duie volte."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "I scaricamenti veloci in l'ultime 24 ore contanu versu u limitu cutidianu."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Tutti i tempi sò in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "I file scaricati ùn sò micca mostrati publicamente."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nisun schedariu scaricatu ancu."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ultimi 18 uri"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Prima"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Cuntu"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Accedi / Registrati"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID di u cuntu: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profilu pùbbricu: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chiavi sicreta (nun la sparteri!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "mustrari"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Abbonamentu: <strong>%(tier_name)s</strong> nzinu a %(until_date)s <a %(a_extend)s>(proroga)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Abbonamentu: <strong>Nuddu</strong> <a %(a_become)s>(diventa membru)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Scaricamenti veloci usati (ùrtimi 24 uri): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "chi scaricamenti?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Gruppu esclusivu Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Unisciti a nuatri ccà!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Aggiorna a nu <a %(a_tier)s>livellu cchiù àutu</a> pi unìriti ô nostru gruppu."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Cuntatta Anna a %(email)s si sì ntirissatu a aggiornari lu tò abbonamentu a nu livellu cchiù àutu."

#, fuzzy
msgid "page.contact.title"
msgstr "Email di cuntattu"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Poti cumminari cchiù abbunamenti (i scaricamenti veloci pi ogni 24 uri vennu aggiunti nzèmmula)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profilu pùbbricu"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "File scaricati"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "I me dunazzioni"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Esci"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Ora sì disconnessu. Ricarica la pàggina pi trasiri n'àutra vota."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ C'è statu nu prubbrema. Ricarica la pàggina e pruva n'àutra vota."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registrazzioni avvenuta cu successu! La tò chiavi segreta è: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Sarva sta chiavi cu cura. Si la perdi, perdi l'accessu ô tò cuntu."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Segnalibru.</strong> Poti mettiri sta pàggina tra i segnalibri pi ricuperari la chiavi.</li><li %(li_item)s><strong>Scarica.</strong> Clicca <a %(a_download)s>stu link</a> pi scaricari la chiavi.</li><li %(li_item)s><strong>Gestore di password.</strong> Usa nu gestore di password pi sarvari la chiavi quannu la nzirisci ccà sutta.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Nzirisci la tò chiavi segreta pi trasiri:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chiavi segreta"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Trasiri"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Chiavi segreta invalida. Verifica la tò chiavi e pruva n'àutra vota, o alternativamenti registrati cu nu cuntu novu ccà sutta."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Nun perdi la tò chiavi!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Nun hai ancora nu cuntu?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registrati cu nu cuntu novu"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Si hai persu la tò chiavi, pi favuri <a %(a_contact)s>cuntattaci</a> e furnisci quantu cchiù nfurmazzioni pussìbbili."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Pò aviri a criari temporaneamenti un novu account pi cuntattàrini."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vecchiu account basatu supra email? Inserisci lu tò <a %(a_open)s>email ccà</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Lista"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "mòdifica"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Sarva"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Sarvatu. Pi favuri ricarica la pàggina."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Quarchi cosa è andata storta. Pruva di novu."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Lista di %(by)s, criata <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Lista è vacanti."

#, fuzzy
msgid "page.list.new_item"
msgstr "Aggiungi o rimuovi di sta lista truvannu un file e aprennu lu tab “Liste”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profilu"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilu nun truvatu."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "mòdifica"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Cambia lu tò nomu di visualizzazzioni. Lu tò identificaturi (la parti dopu “#”) nun pò èssiri canciatu."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Sarva"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Sarvatu. Pi favuri ricarica la pàggina."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Quarchi cosa è andata storta. Pruva di novu."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profilu criatu <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Liste"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Ancora nudda lista"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Crìa na lista nova truvannu nu file e aprennu la tab \"Liste\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "La riforma di u copyright è necessaria pi a sicurizza naziunali"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: LLM cinesi (inclusu DeepSeek) sunnu furmati supra u me archiviu illegali di libri e articuli — u più granni di u munnu. L'Occidenti ha bisognu di riformari a liggi di copyright comu questione di sicurizza naziunali."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articuli cumpagni di TorrentFreak: <a %(torrentfreak)s>primu</a>, <a %(torrentfreak_2)s>sicunnu</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Non troppu tempu fa, e \"librerie ombra\" eranu in via di estinzione. Sci-Hub, u massicciu archiviu illegali di articuli accademici, avia smessu di accettari novi opiri, a causa di cause legali. \"Z-Library\", a più granni libreria illegali di libri, vidìu i so presunti creatori arrestati per accuse di copyright criminali. Riusciru incredibilmente a sfuggiri a l'arrestu, ma a so libreria non è menu minacciata."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quannu Z-Library affrontò a chiusura, avia già fattu un backup di tutta a so libreria e stava cercannu una piattaforma per ospitarla. Chistu fu u me motivu per iniziari L'Archiviu di Anna: una continuazioni di a missioni darreri a chiddi iniziativi precedenti. Da tandu semu cresciuti a essiri a più granni libreria ombra di u munnu, ospitannu più di 140 milioni di testi coperti da copyright in diversi formati — libri, articuli accademici, riviste, ghjurnali, e oltre."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Io e a me squadra semu ideologi. Credemu chi preservari e ospitari sti file è moralmente giustu. E librerie in tuttu u munnu stannu vidennu tagli di finanziamenti, e non putemu fidarici di l'eredità di l'umanità a e corporazioni."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Poi vinni l'AI. Praticamenti tutte e principali cumpagni chi custruiscenu LLM ci cuntattarunu per furmari supra i nostri dati. A maiò parti (ma non tutte!) di e cumpagni basate in i Stati Uniti ci ripensarunu una volta chi si reseru contu di a natura illegali di u nostru travagghiu. In cuntrastu, e imprese cinesi anu abbracciatu entusiasticamente a nostra collezioni, apparentemente non preoccupati per a so legalità. Chistu è notevuli datu u rolu di a Cina comu firmataria di quasi tutti i principali trattati internaziunali di copyright."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Avemu datu accessu ad alta velocità a circa 30 cumpagni. A maiò parti di elli sunnu cumpagni di LLM, e certi sunnu broker di dati, chi rivenderanu a nostra collezioni. A maiò parti sunnu cinesi, ancu si avemu ancu travagghiatu cu cumpagni da i Stati Uniti, Europa, Russia, Corea di u Sud, e Giappone. DeepSeek <a %(arxiv)s>ammettiu</a> chi una versione precedente fu furmata supra una parti di a nostra collezioni, ancu si sunnu stretti di bocca nantu a u so ultimu mudellu (probabilmente ancu furmatu supra i nostri dati)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si l'Occidenti voli ristari avanti in a corsa di LLM, e infine, AGI, ha bisognu di ripensari a so pusizioni nantu a u copyright, e prestu. Chi siate d'accordu cun noi o no nantu a u nostru casu morale, chistu sta diventannu un casu di ecunumia, e ancu di sicurizza naziunali. Tutti i blocchi di puteri stannu custruiscendu super-scienziati artificiali, super-hacker, e super-militari. A libertà di l'informazioni sta diventannu una questione di sopravvivenza per sti paesi — ancu una questione di sicurizza naziunali."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "A nostra squadra veni da tuttu u munnu, e non avemu un allineamentu particulare. Ma incuraggeriamu i paesi cu forti liggi di copyright a usari sta minaccia esistenziale per riformalli. Allura chi fari?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "A nostra prima raccomandazioni è semplice: accorciari u terminu di copyright. In i Stati Uniti, u copyright è cuncessu per 70 anni dopu a morti di l'autore. Chistu è assurdu. Putemu allineari chistu cu i brevetti, chi sunnu cuncessi per 20 anni dopu a presentazioni. Chistu duvissi essiri più chi abbastanza tempu per l'autori di libri, articuli, musica, arte, e altri opiri creative, per esse pienamente compensati per i so sforzi (inclusi prughjetti a longu terminu comu adattamenti cinematografici)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Allura, almenu, i pulitici avissiru a ncludiri eccezzioni pi la preservazioni e diffusioni di massa di testi. Si a perdita di ricavi da clienti individuali è u prubbrema principali, a distribuzioni a livellu personali putissi ristari pruibita. A so vota, chiddi capaci di gestiri vasti archivi — cumpresi cumpagni chi furmanu LLM, insiemi a librerie e altri archivi — sarianu coperti da sti eccezzioni."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Certi paisi stannu già facennu na versione di chistu. TorrentFreak <a %(torrentfreak)s>ha ripurtatu</a> ca la Cina e lu Giappuni hannu introdottu eccezzioni AI a li so liggi di copyright. Nun è chiaru pi nuatri comu chistu s'interfaccia cu li trattati internazziunali, ma certamenti duna copertura a li so cumpagni domèstichi, chi spiega chiddu ca avemu vistu."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Pi quantu riguarda l'Archiviu di Anna — cuntinueremu lu nostru travagghiu suttirraniu radicatu in cunvinzioni murali. Tuttavia, lu nostru cchiù granni disideriu è di trasiri nta la luci, e amplificari lu nostru impattu legalmenti. Pi favuri, riformati lu copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leggi l'articuli cumpagnu di TorrentFreak: <a %(torrentfreak)s>primu</a>, <a %(torrentfreak_2)s>sicunnu</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vincituri di lu premiu di visualizzazzioni ISBN di $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Avemu ricivutu sottomissioni incredibili pi lu premiu di visualizzazzioni ISBN di $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Qualchi misi fa avemu annunziatu un <a %(all_isbns)s>premiu di $10,000</a> pi fari la megghiu visualizzazzioni pussìbbili di li nostri dati mustrannu lu spaziu ISBN. Avemu enfatizzatu di mustrari quali schedi avemu/unn'avemu archiviatu già, e successivamenti un dataset chi discrivi quanti bibliotechi tèninu ISBN (na misura di rarità)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Semu stati sopraffatti di la risposta. C'è stata tantu criatività. Un granni ringraziamentu a tutti chiddi chi hannu participatu: la vostra energia e entusiasmu sunnu cuntaggiusi!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Ultimamenti vulìamu rispunneri a li seguenti dumanni: <strong>quali libri esìstinu ntô munnu, quanti avemu archiviatu già, e quali libri duvìamu cuncentrarici prossimu?</strong> È bellu vidiri tantu genti ca si curanu di sti dumanni."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Avemu iniziatu cu na visualizzazzioni di basa nuatri stessi. In menu di 300kb, chista immaggini rapprisenta succintamenti la cchiù granni \"lista di libri\" cumpletamenti aperta mai assimbulata ntâ storia di l'umanità:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tutti l'ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Schedi ntô Archiviu di Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNO di CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuga di dati CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSID di DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indice eBook di EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archiviu Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registru Globali di Edituri ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Statali Russa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperiali di Trantor"

#, fuzzy
msgid "common.back"
msgstr "Arreri"

#, fuzzy
msgid "common.forward"
msgstr "Avanti"

#, fuzzy
msgid "common.last"
msgstr "Ultimu"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Ppiaciri tali <a %(all_isbns)s>postu originali dû blog</a> pi cchiù nfurmazzioni."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Avemu lanciatu na sfida pi migliurari chistu. Avremu attribbuitu un premiu di primu postu di $6,000, un sicondu postu di $3,000, e un terzu postu di $1,000. Duvutu a la risposta travolgenti e li sottomissioni incredibili, avemu dicisu di aumentari leggermenti lu montepremi, e attribbuiri quattru terzi posti di $500 ciascunu. Li vincituri sunnu sutta, ma assicurati di taliari tutti li sottomissioni <a %(annas_archive)s>ccà</a>, o scaricari lu nostru <a %(a_2025_01_isbn_visualization_files)s>torrent cumminatu</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primu postu $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Sta <a %(phiresky_github)s>sottomissioni</a> (<a %(annas_archive_note_2951)s>cummentu Gitlab</a>) è semplicimenti tuttu chiddu ca vuliamu, e cchiù! Nni piaciunu particularmenti l'opzioni di visualizzazioni incredibilmenti flessibili (supportannu puru shader personalizzati), ma cu na lista cumplessa di preset. Nni piaciunu puru quantu è tuttu veloci e fluidu, l'implementazioni semplici (ca nun havi mancu un backend), lu minimappa intelligenti, e l'ampia spiegazioni ntô sò <a %(phiresky_github)s>postu dû blog</a>. Travagghiu incredibili, e vincituri ben meritatu!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Sicondu postu $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "N'autra <a %(annas_archive_note_2913)s>sottomissioni</a> incredibili. Nun è flessibili comu lu primu postu, ma nni piaciunu cchiù la sò visualizzazioni a livellu macro rispettu ô primu postu (curva riempimentu spaziu, cunfini, etichettatura, evidenziazioni, panning, e zooming). Un <a %(annas_archive_note_2971)s>cummentu</a> di Joe Davis nni risunau:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Mentri li quadrati e rettanguli perfetti sunnu matematicamenti piacenti, nun furniscinu superiori località ntô cuntestu di mappatura. Cridu ca l'asimmetria intrinseca ntê sti Hilbert o Morton classicu nun è un difettu ma un carattiristica. Comu lu famusu profilu a stivale di l'Italia la fa subitu ricunnoscibili supra na mappa, li \"stranezzi\" unichi di sti curve ponnu serviri comu punti di riferimentu cognitivi. Sta distintività pò migliurari la memoria spaziali e aiutari l'utenti a orientarsi, putenzialmenti facennu cchiù facili truvare regioni specifiche o notari mudelli.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ancora tanti opzioni pi visualizzari e renderizzari, comu puru un'interfaccia utente incredibilmenti fluida e intuitiva. Un solidu sicondu postu!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terzu postu $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Ntâ sta <a %(annas_archive_note_2940)s>sottomissioni</a> nni piaciunu assai li diversi tipi di visualizzazioni, in particulari li visualizzazioni di cumparazioni e di editori."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terzu postu $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Mentri ca l'interfaccia nun è la cchiù raffinata, sta <a %(annas_archive_note_2917)s>sottomissioni</a> rispetta assai criteri. Nni piaci particolarmenti la so funzione di paraguni."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terzu postu $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Comu lu primu postu, sta <a %(annas_archive_note_2975)s>sottomissioni</a> nni ha impressionatu pi la so flessibilità. In definitiva, chistu è chiddu ca fa di un strumentu di visualizzazioni un gran strumentu: massima flessibilità pi l'utenti esperti, mentri manteni le cose semplici pi l'utenti medi."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terzu postu $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "L'ultima <a %(annas_archive_note_2947)s>sottomissioni</a> ca ricevi un premiu è abbastanza basilari, ma havi qualchi funzioni unichi ca nni piaciunu assai. Nni piaci comu mostranu quanti datasets coprinu un particulari ISBN comu misura di pupularità/affidabilità. Nni piaci puru la simplicità ma efficacia di usari un cursore di opacità pi li paraguni."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idei notevuli"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Qualchi autri idei e implementazioni ca nni piaciunu particolarmenti:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Grattacieli pi rarità"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistiche in diretta"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotazioni, e puru statistiche in diretta"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista unica di mappa e filtri"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schema di culuri frescu e mappa di caluri."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facili attivazioni di datasets pi paraguni rapidi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etichette carine."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra di scala cu numaru di libri."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Tanti cursori pi paragunari datasets, comu si fussi un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Pudemu cuntinuari pi un pocu, ma fermamuni ccà. Assicurati di taliari tutti li sottomissioni <a %(annas_archive)s>ccà</a>, o scarica lu nostru <a %(a_2025_01_isbn_visualization_files)s>torrent cumminatu</a>. Tanti sottomissioni, e ognuna porta una prospettiva unica, sia in UI o implementazioni."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Almenu ncorporeremu la sottomissioni di primu postu ntô nostru situ principali, e forsi autri. Avemu puru accuminciau a pinsari comu organizari lu prucessu di identificari, cunfirmari, e poi archiviari li libri cchiù rari. Cchiù novità a veniri."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Grazi a tutti chiddi ca participaru. È stupendu ca tanti pirsuni ci tenunu."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "I nostri cori sunnu chini di gratitudini."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizzannu Tutti l'ISBN — ricumpensa di $10,000 entro lu 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Sta mmàggini rapprisenta la cchiù granni \"lista di libri\" cumpletamenti aperta mai custruita ntâ storia dâ umanità."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Sta mmàggini è 1000×800 pixel. Ogni pixel rapprisenta 2,500 ISBN. Si avemu un schedariu pi un ISBN, facemu chiddu pixel cchiù viridi. Si sapemu ca un ISBN è statu attribbuitu, ma nun avemu un schedariu currispunnenti, lu facemu cchiù russu."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In menu di 300kb, sta mmàggini rapprisenta succintamenti la cchiù granni \"lista di libri\" cumpletamenti aperta mai custruita ntâ storia dâ umanità (qualchi centinara di GB cumpressi ntâ sò tutalitati)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Mostra puru: c'è assai travagghiu ancora pi fari pi arricugghiri i libri (avemu sulu 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Sfunnu"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Comu pò l'Archiviu di Anna arricugghiri tuttu lu sapiri dâ umanità, senza sapiri quali libri sunnu ancora fora? Avemu bisognu di na lista di cose da fari. Un modu pi mappare chistu è attraversu i numeri ISBN, ca di lu 1970 sunnu stati attribbuiti a ogni libru pubblicatu (ntâ maiur parti dî paisi)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nun c'è n'autorità centrali ca sapi tutti l'assignazzioni ISBN. Inveci, è un sistema distribuitu, unni i paisi ottènunu range di numeri, chi poi assignanu range cchiù nichi a editori maiuri, chi putìssiru suddivìdiri ulteriormenti i range a editori minuri. Finalmenti, i numeri individuali sunnu assignati a i libri."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Accuminzammu a mappare l'ISBN <a %(blog)s>du' anni fa</a> cu la nostra raccolta di ISBNdb. Da tandu, avemu raccoltu assai cchiù fonti di metadata, comu <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e cchiù. Na lista completa si pò truvari ntê pagini \"Datasets\" e \"Torrents\" ntô Archiviu di Anna. Ora avemu di gran lunga la cchiù granni raccolta cumpletamenti aperta e facirmenti scaricabbili di metadata di libri (e quindi ISBN) ntô munnu."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Avemu <a %(blog)s>scrittu abbunnantementi</a> supra pi quali mutivi ci tenemu a la preservazzioni, e pi quali semu attualmente ntô na finestra critica. Ora avemu bisognu di identificari i libri rari, pocu focalizzati, e uniciamenti a risicu e preservarli. Aviri boni metadata supra tutti i libri ntô munnu aiuta cu chistu."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizzannu"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Oltre l'immàggini d'insiemi, putemu puru taliari i datasets individuali ca avemu acquistatu. Usa lu menu a tendina e i buttuna pi cambiari tra iddi."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Ci sunnu assai mudelli ntirissanti pi vidiri ntê sti mmàggini. Picchì c'è na certa regularità di linii e blocchi, ca pari succèdiri a diversi scale? Chi sunnu l'aree vacanti? Picchì certi datasets sunnu accucchiati? Lassamu sti dumanni comu un eserciziu pi lu letturi."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Ricumpensa di $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "C'è assai da esplorare ccà, allura annunciamu na ricumpensa pi migliurari la visualizzazzioni supra. A differenza di la maiur parti di li nostri ricumpensi, chista è limitata ntô tempu. Aviti a <a %(annas_archive)s>suttamettiri</a> lu vostru codice open source entro lu 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La megghiu suttamissioni ricevirà $6,000, lu secunnu postu $3,000, e lu terzu postu $1,000. Tutti li ricumpensi sarannu attribbuiti usannu Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Sutta sunnu i criteri minimi. Si nudda suttamissioni rispetta i criteri, putemu ancora attribbuiri qualchi ricumpensa, ma chissu sarà a nostra discrezzioni."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forka stu repo, e edita stu post HTML di blog (nuddu àutru backend oltre lu nostru backend Flask è permessu)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Fai ca a mmàggini supra si pò zummari lisciu, accussì ca si pò zummari finu a l'ISBN individuali. Cliccannu l'ISBN ti porta a na pàggina di metadata o a na ricerca supra l'Archiviu di Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Devi ancora essiri capaci di canciari tra tutti li diversi datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Li range di paisi e li range di editori avissiru a essiri evidenziati quannu ci passi supra. Pò usari p'esempiu <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> pi l'informazioni di paisi, e lu nostru “isbngrp” scrape pi l'editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Avissi a travagghiari bonu sia supra desktop ca supra mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pi punti bonus (chisti sunnu sulu idei — lascia ca la tò creatività si scatenassi):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Si darà forti cunsiderazioni a l'usabilità e a quantu bonu pari."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mustra metadata attuali pi l'ISBN individuali quannu zummi, comu titulu e auturi."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Megghiu curva di riempimentu di spazziu. P'esempiu, un zig-zag, ca va di 0 a 4 supra la prima fila e poi torna (in reversu) di 5 a 9 supra la secunna fila — applicatu ricursivamenti."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Schemi di culuri differenti o personalizzabili."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Viste speciali pi cumparari datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modi pi debug di prubblemi, comu autri metadata ca nun s'accordanu bonu (p'esempiu, tituli assai differenti)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotari l'immàggini cu cummenti supra l'ISBN o range."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualsiasi euristica pi identificari libri rari o a risicu."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Qualsiasi idea creativa ca ti pò veniri!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Pò deviari completamenti di li criteri minimi, e fari na visualizzazioni completamenti differente. Si è veramenti spettaculare, allura chissu qualifica pi la ricumpensa, ma a nostra discrezioni."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fai li sottomissioni postannu un cummentu a <a %(annas_archive)s>stu prubblema</a> cu un link a lu tò repo forked, merge request, o diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Codici"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Lu codici pi generari sti immàggini, comu puru autri esempi, si pò truvari in <a %(annas_archive)s>stu direttoriu</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Avemu vinutu cu un furmatu di dati cumpattu, cu cui tutta l'informazioni richiesti di ISBN è circa 75MB (compressu). La descrizzioni di lu furmatu di dati e lu codici pi generallu si pò truvari <a %(annas_archive_l1244_1319)s>ccà</a>. Pi la ricumpensa nun si richiedi di usari chistu, ma è probabili ca è lu furmatu cchiù cunvenienti pi cuminciari. Pò trasfurmari i nostri metadata comu voi (ancora ca tuttu lu tò codici avissi a essiri open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nun videmu l'ura di vidiri chi veni fora. Bona furtuna!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "I Contenituri di l'Archiviu di Anna (AAC): standardizzannu i rilasci da a più grande biblioteca ombra di u mondu"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "L'Archiviu di Anna hè diventatu a più grande biblioteca ombra di u mondu, richiedendu di standardizzà i nostri rilasci."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a> hè diventatu di gran lunga a più grande biblioteca ombra di u mondu, è l'unica biblioteca ombra di sta scala chì hè cumpletamente open-source è open-data. Sottu hè una tabella da a nostra pagina di Datasets (leggermente modificata):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Avemu realizatu questu in trè modi:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Rispecchiendu e biblioteche ombra open-data esistenti (cum'è Sci-Hub è Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Aiutendu e biblioteche ombra chì volenu esse più aperte, ma chì ùn avianu micca u tempu o e risorse per fà cusì (cum'è a cullezzione di fumetti di Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scraping di biblioteche chì ùn volenu micca sparte in massa (cum'è Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Per (2) è (3) avà gestemu una cullezzione cunsiderevule di torrents noi stessi (centinaia di TBs). Finu à avà avemu trattatu queste cullezzioni cum'è uniche, significendu infrastruttura su misura è organizzazione di dati per ogni cullezzione. Questu aghjusta un overhead significativu à ogni rilascio, è rende particularmente difficiule di fà rilasci più incrementali."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Hè per quessa chì avemu decisu di standardizzà i nostri rilasci. Questu hè un postu tecnicu di blog in u quale stamu introducendu u nostru standard: <strong>I Contenituri di l'Archiviu di Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Obiettivi di design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "U nostru casu d'usu primariu hè a distribuzione di file è metadata associati da diverse cullezzioni esistenti. I nostri cunsiderazioni più impurtanti sò:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "File è metadata eterogenei, u più vicinu pussibule à u furmatu originale."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatori eterogenei in e biblioteche di origine, o ancu mancanza di identificatori."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Rilasci separati di metadata vs dati di file, o rilasci di sola metadata (per esempiu, u nostru rilascio ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuzione attraversu torrents, ancu cù a pussibilità di altri metudi di distribuzione (per esempiu, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registri immutabili, postu chì duvemu assumere chì i nostri torrents campanu per sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Rilasci incrementali / rilasci appendibili."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Leggibile è scrivibile da macchine, convenientemente è rapidamente, soprattuttu per a nostra pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Ispezione umana abbastanza faciule, ancu se questu hè secundariu à a leggibilità da macchine."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facili a disseminari i nostri cullezzioni cu un seedbox standard affittatu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Li dati binari ponnu èssiri sirvuti direttamenti di webserver comu Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Certi non-obiettivi:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nun nni curamu si li file sunnu facili a navigari manualmenti supra u discu, o ricircabili senza prepricissamentu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nun nni curamu di èssiri direttamenti cumpatibili cu software di libreria esistenti."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Mentri avissi a èssiri facili pi chiunqui disseminari a nostra cullezzioni usannu torrents, nun nni aspettemu ca li file sunnu utilizzabili senza cunniscenza tecnica significativa e impegnu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Datu ca l'Archiviu di Anna è open source, vulemu usari direttamente u nostru formatu. Quannu rinfrescamu u nostru indice di ricerca, accedemu sulu a li percorsi pubblicamenti dispunibili, accussì chiunqui ca forka a nostra libreria pò iniziari e curriri rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "U standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Infini, avemu sceltu un standard relativamente semprici. È abbastanza lassu, non-normativu, e un travagghiu in progressu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Container di l'Archiviu di Anna) è un articulu unicu custituitu di <strong>metadata</strong>, e opzionalmenti <strong>dati binari</strong>, tutti dui immutabili. Ha un identificatore unicu globali, chiamatu <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Cullezzioni.</strong> Ogni AAC apparteni a una cullezzioni, ca per definizioni è una lista di AAC ca sunnu semanticamenti consistenti. Chistu significa ca si fai un cambiamentu significativu a u formatu di li metadata, allura devi criari una nova cullezzioni."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Cullezzioni di “records” e “files”.</strong> Per cunvenzioni, è spissu cunvenienti rilassari “records” e “files” comu cullezzioni differenti, accussì ponnu èssiri rilassati a diversi scheduli, p.es. basatu supra i tassi di scraping. Un “record” è una cullezzioni di sulu metadata, ca cunteni infurmazioni comu tituli di libri, auturi, ISBN, ecc., mentri “files” sunnu li cullezzioni ca cunteninu i file attuali stessi (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> U formatu di AACID è chistu: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Per esempiu, un AACID attuali ca avemu rilassatu è <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: u nomu di a cullezzioni, ca pò cunteniri littri ASCII, numeri, e underscores (ma senza doppi underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: una versione curta di l'ISO 8601, sempri in UTC, p.es. <code>20220723T194746Z</code>. Stu numaru ha a aumentari monotonamenti pi ogni rilassu, benché i so semantichi esatti ponnu differiri per cullezzioni. Suggeremu di usari u tempu di scraping o di generazioni di l'ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificatore specificu di cullezzioni, si applicabili, p.es. l'ID di Z-Library. Pò èssiri omessu o troncatu. Devi èssiri omessu o troncatu si l'AACID altrimenti superassi 150 caratteri."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID ma cumpressu a ASCII, p.es. usannu base57. Attualmente usamu a libreria Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Gamma di AACID.</strong> Datu ca l'AACID cunteninu timestamps ca aumentanu monotonamenti, pudemu usari chistu pi denotari gamme intra una cullezzioni particolari. Usamu stu formatu: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, unni i timestamps sunnu inclusivi. Chistu è consistenti cu a notazioni ISO 8601. Li gamme sunnu continui, e ponnu sovrapporsi, ma in casu di sovrapposizioni devunu cunteniri records identici a chiddi rilassati in precedenza in quella cullezzioni (datu ca l'AAC sunnu immutabili). Records mancanti nun sunnu permessi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>File di metadata.</strong> Un file di metadata cunteni i metadata di una gamma di AAC, per una cullezzioni particolari. Chisti hannu i seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "U nomu di u file devi èssiri una gamma di AACID, prefissatu cu <code style=\"color: red\">annas_archive_meta__</code> e seguitu da <code>.jsonl.zstd</code>. Per esempiu, unu di i nostri rilassi è chiamatu<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Comu indicatu dû estensioni dû schedariu, lu tipu di schedariu è <a %(jsonlines)s>JSON Lines</a> cumpressu cu <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Ogni oggettu JSON avi a cunteniri li campi seguenti a lu liveddu supranu: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opzziunali). Nuddu àutru campu è permessu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> è metadata arbitrariu, secunnu li semàntichi dâ cullizzioni. Avi a èssiri semanticamenti cunsistenti ntâ cullizzioni."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> è opzziunali, e rapprisenta lu nomu dû cartularu di dati binari chi cunteni li dati binari currispunnenti. Lu nomu dû schedariu di dati binari currispunnenti ntô cartularu è l'AACID dû record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Lu prefissu <code style=\"color: red\">annas_archive_meta__</code> pò èssiri adattatu ô nomu dâ vostra istituzzioni, p'esempiu <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Cartularu di dati binari.</strong> Un cartularu cu li dati binari di na gamma di AAC, pi na cullizzioni particulari. Chisti hannu li seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Lu nomu dû cartularu avi a èssiri na gamma di AACID, prefissatu cu <code style=\"color: green\">annas_archive_data__</code>, e senza suffissu. P'esempiu, unu di li nostri rilascî attuali avi un cartularu chiamatu<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Lu cartularu avi a cunteniri schedari di dati pi tutti l'AAC ntâ gamma specificata. Ogni schedariu di dati avi a aviri l'AACID comu nomu dû schedariu (senza estensioni)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Si cunsìglia di fari sti cartulari di na misura gestibbili, p'esempiu non cchiù granni di 100GB-1TB ognunu, anchi si stu cunsìgliu pò canciari cu lu tempu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Li schedari di metadata e li cartulari di dati binari ponnu èssiri raggruppati ntô torrents, cu un torrent pi schedariu di metadata, o un torrent pi cartularu di dati binari. Li torrents hannu a aviri lu nomu urigginali dû schedariu/cartularu cchiù un suffissu <code>.torrent</code> comu nomu dû schedariu."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Esempiu"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Talìamu a lu nostru rilascio ricenti di Z-Library comu esempiu. Si cumpuni di dui cullizzioni: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Chistu nni permette di scrappare e rilasciri separatamente li record di metadata di li schedari di libri attuali. Comu tali, avemu rilascatu dui torrents cu schedari di metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Avemu puru rilascatu na quantità di torrents cu cartulari di dati binari, ma sulu pi la cullizzioni “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 in tutali:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Esecùtannu <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> putemu vidiri chiddu chi c'è dintra:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Ntô casu, è metadata di un libru comu ripurtatu di Z-Library. A lu liveddu supranu avemu sulu “aacid” e “metadata”, ma nuddu “data_folder”, picchì nun c'è dati binari currispunnenti. L'AACID cunteni “22430000” comu ID primariu, chi putemu vidiri è pigghiatu di “zlibrary_id”. Putemu aspittarici àutri AAC ntâ cullizzioni cu la stissa struttura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Ora esecutamu <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Chistu è un metadata di AAC assai cchiù nicu, anchi si la parti cchiù granni di stu AAC si trova n'àutra banna ntô schedariu binariu! Doppu tuttu, avemu un “data_folder” sta vota, accussì putemu aspittarici chi li dati binari currispunnenti si trovanu a <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Lu “metadata” cunteni lu “zlibrary_id”, accussì putemu assuciarlu facirmenti cu l'AAC currispunnenti ntâ cullizzioni “zlib_records”. Putemu aviri assuciatu in diversi modi, p'esempiu attraversu AACID — lu standard nun lu prescrivi."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Notati chi nun è nicissariu chi lu campu “metadata” sia stissu JSON. Pò èssiri na stringa chi cunteni XML o qualunqui àutru furmatu di dati. Putiti puru archiviarici nfurmazzioni di metadata ntô blob binariu assuciatu, p'esempiu si è assai dati."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Cunclusioni"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Cu stu standard, putemu fari rilasci cchiù incrementalmenti, e aghiunciri cchiù facirmenti novi fonti di dati. Avemu già qualchi rilascio eccitanti ntô pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Speramu puru ca l'autri bibliutichi ombra addiventa cchiù facili a rispecchiari i nostri cullezzioni. Doppu tuttu, u nostru scopu è di prisirvari pi sempri a cunniscenza e a cultura umana, accussì cchiù ridundanza c'è, megghiu è."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Aggiornamentu di Anna: archiviu cumpletamenti open source, ElasticSearch, 300GB+ di copertini di libri"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Avemu travagghiatu senza sosta pi furniri na bona alternativa cu Archiviu di Anna. Eccu quarchi cosa ca avemu ottinutu ricintimenti."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Cu Z-Library ca scinni e i so (presunti) fundaturi ca vennu arristati, avemu travagghiatu senza sosta pi furniri na bona alternativa cu Archiviu di Anna (un ci mettemu u link ccà, ma putiti circari su Google). Eccu quarchi cosa ca avemu ottinutu ricintimenti."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L'Archiviu di Anna è cumpletamenti open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Cridemu ca l'informazioni duvissi essiri libira, e u nostru stissu codice nun fa eccezzioni. Avemu rilassatu tuttu u nostru codice supra a nostra istanza Gitlab privata: <a %(annas_archive)s>Software di Anna</a>. Usamu puru u tracciaturi di prublemi pi organizzari u nostru travagghiu. Si vuliti participari a u nostru sviluppu, chistu è un bonu puntu di partenza."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Pi dari un'idea di i cosi ca stamu travagghiannu, pigghiamu u nostru travagghiu ricenti supra i migliuramenti di prestazioni client-side. Siccomu nun avemu ancora implementatu a paginazioni, spissu turnavamu pagini di ricerca assai longhi, cu 100-200 risultati. Nun vuliamu tagghiari i risultati di ricerca troppu prestu, ma chistu significava ca rallintava certi dispusitivi. Pi chistu, avemu implementatu un picculu truccu: avvolgiamu a maiur parti di i risultati di ricerca in cummenti HTML (<code><!-- --></code>), e poi scriviamu un picculu Javascript ca rilevava quannu un risultatu duvissi divintari visibili, a quellu puntu scartavamu u cummentu:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "Virtualizzazioni DOM implementata in 23 linee, senza bisognu di librerie sofisticate! Chistu è u tipu di codice pragmaticu e veloci ca finisci cu aviri quannu hai tempu limitatu, e prublemi reali ca necessitanu di essiri risolti. È statu ripurtatu ca a nostra ricerca ora funziona beni supra dispusitivi lenti!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un altru sforzu maiuri fu di automatizari a custruzioni di u database. Quannu avemu lanciatu, aviamu semplicimenti tiratu inseme fonti diversi. Ora vulimu mantiniri li aggiornati, accussì avemu scrittu un gruppu di script pi scaricari novi metadata da i dui fork di Library Genesis, e integrarli. U scopu è di nun fari chistu solu utili pi u nostru archiviu, ma di fari i cosi facili pi chiunque vulissi sperimentari cu metadata di bibliutichi ombra. U scopu saria un notebook Jupyter ca avissi tutti i tipi di metadata interessanti dispunibili, accussì putemu fari cchiù ricerca comu capiri chi <a %(blog)s>percentuali di ISBN sò prisirvati pi sempri</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Infini, avemu rinnuvatu u nostru sistema di donazioni. Ora putiti usari una carta di creditu pi dipositaru direttamente soldi nei nostri portafogli crypto, senza aviri bisognu di sapiri nenti di criptovalute. Continueremu a monitorari comu chistu funziona in pratica, ma chistu è un gran passu avanti."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Passaggiu a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Unu di i nostri <a %(annas_archive)s>biglietti</a> era un sacchettu di prublemi cu u nostru sistema di ricerca. Usavamu a ricerca full-text di MySQL, siccomu aviamu tutti i nostri dati in MySQL comunque. Ma avia i so limiti:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Certi richiesti pigghiavanu assai tempu, finu a u puntu ca occupavanu tutti i cunnessioni aperti."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Per difettu MySQL ha una lunghezza minima di paroli, o u vostru indice pò divintari veramente granni. Genti ripurtavanu di nun putiri circari \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "A ricerca era solu un pocu veloci quannu era cumpletamenti caricata in memoria, ca ci richiedeva di aviri una machina cchiù cara pi eseguirla, più certi cumandi pi precaricari l'indice all'avviu."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nun avissimu pututu estenderla facilmente pi custruiri novi funzioni, comu megghiu <a %(wikipedia_cjk_characters)s>tokenizzazioni pi lingui senza spazi</a>, filtrazioni/faccettazioni, ordinamentu, suggerimenti \"vulevi diri\", autocompletamentu, e accussì via."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Doppu aviri parlatu cu un gruppu di esperti, avemu sceltu ElasticSearch. Nun è statu perfettu (i so suggerimenti \"vulevi diri\" e funzioni di autocompletamentu di difettu nun sunnu boni), ma in generale è statu assai megghiu di MySQL pi a ricerca. Nun semu ancora <a %(youtube)s>troppu entusiasti</a> di usarlu pi dati critichi (ancora ca anu fattu assai <a %(elastic_co)s>progressi</a>), ma in generale semu abbastanza cuntenti di u cambiamentu."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Pi ora, avemu implementatu una ricerca assai cchiù veloci, megghiu supportu linguistico, megghiu ordinamentu di rilevanza, opzioni di ordinamentu diversi, e filtrazioni supra lingua/tipu di libru/tipu di file. Si siti curiosi di comu funziona, <a %(annas_archive_l140)s>date</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>occhiata</a>. È abbastanza accessibile, ancora ca putissi aviri bisognu di cchiù cummenti…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ di copertini di libri rilassati"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmenti, semu cuntenti di annunziari na piccula rilassata. In cullabburazzioni cu li genti ca gestiscinu lu fork di Libgen.rs, stamu spartennu tutti li so copertini di libri attraversu torrents e IPFS. Chistu distribuirà lu caricu di vidiri li copertini tra cchiù machini, e li prisirverà megghiu. In tanti (ma non tutti) casi, li copertini di libri sunnu inclusi ntê schedi stissi, accussì chistu è un tipu di \"dati derivati\". Ma avirli in IPFS è ancora assai utili pi l'operazioni di ogni jornu di l'Archiviu di Anna e li vari fork di Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Comu di solitu, putiti truvari sta rilassata a lu Pirate Library Mirror (EDIT: spustatu a <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>). Non ci mettemu un link ccà, ma putiti truvarlu facirmenti."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Speramu di putiri rilassari un pocu lu nostru ritmu, ora ca avemu na bona alternativa a Z-Library. Stu caricu di travagghiu non è particularmenti sustinibili. Si siti interessati a dari na manu cu la prugrammazzioni, l'operazioni di server, o lu travagghiu di prisirvazzioni, cuntattatici. C'è ancora assai <a %(annas_archive)s>travagghiu da fari</a>. Grazzi pi lu vostru interessu e sustegnu."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "L'Archiviu di Anna ha fattu un backup di la cchiù granni libreria ombra di fumetti di lu munnu (95TB) — putiti aiutari a seedarla"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La cchiù granni libreria ombra di fumetti di lu munnu avia un sulu puntu di fallimentu... finu a oggi."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discuti su Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La cchiù granni libreria ombra di fumetti è probabbilmenti chidda di un particulari fork di Library Genesis: Libgen.li. L'unicu amministraturi ca gestiva chiddu situ riusciu a raccogghiri na cullizzioni di fumetti pazzesca di cchiù di 2 miliuna di schedi, pi un tutali di cchiù di 95TB. Tuttavia, a differenza di àutri cullizzioni di Library Genesis, chista non era dispunibili in bloccu attraversu torrents. Putiti accidiri a sti fumetti sulu individualmenti attraversu lu so server persunali lentu — un sulu puntu di fallimentu. Finu a oggi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In stu postu vi cuntaremu cchiù di sta cullizzioni, e di la nostra raccolta fondi pi sustiniri cchiù di stu travagghiu."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>La Dott.ssa Barbara Gordon prova a pirdirisi ntô munnu mundanu di la libreria…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fork di Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Prima, qualchi sfunnu. Putiti canusciri Library Genesis pi la so epica cullizzioni di libri. Meno genti sapi ca li vuluntari di Library Genesis hannu criatu àutri pruggetti, comu na cullizzioni cunsiderevuli di rivisti e ducumenti standard, un backup cumpletu di Sci-Hub (in cullabburazzioni cu la funnatrice di Sci-Hub, Alexandra Elbakyan), e in effetti, na cullizzioni massiccia di fumetti."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "A un certu puntu, diversi operatori di li specchi di Library Genesis si separaru, chi detti vita a la situazioni attuali di aviri un numaru di diversi \"fork\", tutti ancora purtannu lu nomu di Library Genesis. Lu fork di Libgen.li ha unicamente sta cullizzioni di fumetti, comu puru na cullizzioni cunsiderevuli di rivisti (cu cui stamu puru travagghiannu)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Cullabburazzioni"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Datu lu so tagghiu, sta cullizzioni è stata longu ntâ nostra lista di desideri, accussì dopu lu nostru successu cu lu backup di Z-Library, avemu puntatu a sta cullizzioni. A l'iniziu l'avemu raschiata direttamenti, chi fu na sfida, datu ca lu so server non era ntô megghiu statu. Avemu ottinutu circa 15TB in stu modu, ma era lentu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Fortunatamenti, semu riusciti a mettiri in cuntattu cu l'operaturi di la libreria, chi accunsentiu a mannari tutti li dati direttamenti, chi fu assai cchiù veloci. Ci vulia ancora cchiù di menzu annu pi trasfiriri e processari tutti li dati, e quasi li pirdemu tutti pi na curruzzioni di discu, chi avissi significatu ricuminciari di novu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Sta spirienza ci ha fattu cridiri ca è mpurtanti fari uscirisi sti dati quantu cchiù prestu pussibili, accussì putinu essiri specchiati largamenti. Semu sulu unu o dui incidenti di tempu sfortunatu luntanu di pirdiri sta cullizzioni pi sempri!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La cullizzioni"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Muvirisi veloci significa ca la cullizzioni è un pocu disurganizzata… Facemu un taliu. Immaginamu ca avemu un filesystem (chi in rialità stamu spaccannu tra torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Lu primu direttoriu, <code>/repository</code>, è la parti cchiù strutturata di chistu. Stu direttoriu cunteni li cosiddetti \"mila dirs\": direttori ciascunu cu un mila schedi, chi sunnu numerati incrementalmenti ntâ basa di dati. Lu direttoriu <code>0</code> cunteni schedi cu comic_id 0–999, e accussì via."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Chistu è lu stissu schema ca usa Library Genesis pi li so cullezzioni di fiction e non-fiction. L'idea è ca ogni \"mila dir\" veni autumaticamenti trasfurmatu in un torrent appena si riempi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tuttavia, l'operaturi di Libgen.li nun ha mai fattu torrent pi sta cullezzioni, e accussì li mila dir probabbilmenti addivintaru scomodi, e dettiru locu a \"dir non ordinati\". Chisti sunnu <code>/comics0</code> a <code>/comics4</code>. Tutti cuntenunu strutturi di directory unichi, ca probabbilmenti avianu sensu pi raccogghiri li file, ma ora nun hannu tantu sensu pi nuatri. Fortunatamenti, lu metadata si rifirisci direttamenti a tutti sti file, accussì l'urganizzazioni di archiviazioni supra lu discu nun importa veramenti!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Lu metadata è dispunibili in forma di un database MySQL. Chistu pò essiri scaricatu direttamenti di lu situ web di Libgen.li, ma nuatri lu metteremu puru dispunibili in un torrent, accumpagnatu da la nostra tabella cu tutti li hash MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analisi"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quannu ti trasi 95TB in lu cluster di archiviazioni, provi a capiri chi c'è dintra... Avemu fattu un'analisi pi vidiri si putemu riduciri un pocu la dimensioni, comu eliminannu li duplicati. Eccu quarchi di li nostri scuperti:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Li duplicati semantici (scansioni differenti di lu stissu libru) teuricamenti ponnu essiri filtrati, ma è difficili. Quannu avemu guardatu manualmenti tra li fumetti avemu truvatu troppi falsi positivi."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Ci sunnu quarchi duplicati puramenti per MD5, ca è relativamenti dispendioso, ma filtrari chisti ci darià sulu circa 1% in di risparmiu. A sta scala è ancora circa 1TB, ma puru, a sta scala 1TB nun importa veramenti. Preferemu nun rischiari di distruggiri dati accidentalmenti in stu prucessu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Avemu truvatu un gruppu di dati non-libru, comu film basati supra fumetti. Chistu pari puru dispendioso, postu ca chisti sunnu già largamenti dispunibili per altri mezi. Tuttavia, avemu capitu ca nun putemu semplicimenti filtrari i file di film, postu ca ci sunnu puru <em>fumetti interattivi</em> ca foru rilassati supra lu computer, ca quarcunu ha registratu e salvatu comu film."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "In definitiva, tuttu chiddu ca putemu eliminari di la cullezzioni risparmiassi sulu quarchi percentuali. Poi ci ricurdammu ca semu accumulaturi di dati, e li pirsuni ca mirrerannu chistu sunnu puru accumulaturi di dati, e accussì, \"CHI VUOI DIRE, ELIMINARE?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Pertantu, vi prisintamu la cullezzioni completa e non mudificata. È assai dati, ma speramu ca abbastanza pirsuni si curerannu di seedarla comunque."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Raccolta fondi"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Stamu rilassannu sti dati in quarchi grossi pezzi. Lu primu torrent è di <code>/comics0</code>, ca avemu misi in un enormi file .tar di 12TB. Chistu è megghiu pi lu vostru hard drive e software di torrent ca un milione di file più piccoli."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Comu parti di sta rilassata, stamu facennu una raccolta fondi. Stamu circannu di raccogghiri $20,000 pi copriri i costi operativi e di cuntrattu pi sta cullezzioni, e puru pi abilità pruggetti futuri e in corsu. Avemu quarchi <em>massicci</em> in lavoru."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Chi stai supportannu cu la me donazioni?</em> In sintesi: stamu facennu un backup di tuttu lu sapiri e la cultura di l'umanità, e facennu lu facilmente accessibili. Tutti i nostri codici e dati sunnu open source, semu un pruggettu gestitu interamenti da vuluntari, e avemu salvatu 125TB di libri finu a ora (in aggiunta ai torrent esistenti di Libgen e Scihub). In definitiva stamu custruennu un volano ca abilita e incentiva li pirsuni a truvari, scannerizzari, e faciri backup di tutti i libri di lu munnu. Scriveremu di lu nostru pianu maestru in un post futuru. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si doni pi un abbonamentu di 12 misi \"Archivista Ammirabili\" ($780), poi <strong>“adottari un torrent”</strong>, significannu ca metteremu lu to username o messaggiu nel nome di un di i torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Poi donari andannu a <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a> e cliccannu lu buttuni “Dona”. Stamu puru circannu più vuluntari: ingegneri software, ricercatori di sicurezza, esperti di mercanti anonimi, e traduttori. Poi puru supportarici furnennu servizii di hosting. E ovviamenti, per piaciri seedate i nostri torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Grazii a tutti chiddi ca ci anu supportatu già generosamente! Stai veramenti facennu la differenza."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Eccu i torrent rilassati finu a ora (stamu ancora processannu lu restu):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tutti i torrent ponnu essiri truvati supra <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a> sutta “Datasets” (nun linkamu direttamente, accussì i link a stu blog nun venunu rimossi da Reddit, Twitter, ecc). Da lì, segui lu link a lu situ web di Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Chi c'è dopu?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un gruppu di torrent è ottimu pi la preservazioni a longu terminu, ma nun tantu pi l'accessu cutidianu. Travagghiamu cu partinari di hosting pi purtari tutti sti dati supra lu web (visto ca l'Archiviu di Anna nun ospita nenti direttamenti). Naturalmenti, putiti truvari sti link di scaricamentu supra l'Archiviu di Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Invitamu puru a tutti a fari quarchi cosa cu sti dati! Aiutatici a analizzarli megghiu, deduplicarli, mettirli supra IPFS, remixarli, addistrari i vostri mudelli AI cu iddi, e accussì via. È tuttu vostru, e nun videmu l'ura di vidiri chi faciti cu iddi."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Infini, comu dissi prima, avemu ancora quarchi rilascio massicciu in arrivo (si <em>quarcunu</em> putissi <em>accidentalmenti</em> mannari nu dump di un <em>certu</em> database ACS4, sapiti unni truvarini...), e stamu custruennu lu volanu pi backup di tutti i libri di lu munnu."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Allura, restati sintonizzati, avemu appena cuminciatu."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novi libri aggiunti a lu Specchiu di la Libreria Pirata (+24TB, 3.8 milioni di libri)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Nto rilascio originali di lu Specchiu di la Libreria Pirata (EDIT: spustatu a <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>), facemmu nu specchiu di Z-Library, na granni cullezzioni di libri illegali. Comu ricordu, chistu è chiddu ca scrivemmu nto post originali di lu blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library è na libreria pupulari (e illegali). Hanu pigghiatu la cullezzioni di Library Genesis e l'hanu fatta facili a circari. Supra a chistu, sunnu divintati assai efficaci a solicità novi cuntribuzioni di libri, incentivannu l'utenti cuntribuenti cu vari vantaggi. Attualmente nun cuntribuiscinu sti novi libri a Library Genesis. E a differenza di Library Genesis, nun fannu la so cullezzioni facili a specchiare, chi previene la larga preservazioni. Chistu è impurtanti pi lu so mudellu di business, vistu ca charginu soldi pi l'accessu a la so cullezzioni in bloccu (cchiù di 10 libri pi jornu)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nun facemu giudizi murali supra lu fattu di caricari soldi pi l'accessu in bloccu a na cullezzioni di libri illegali. È senza dubbiu ca Z-Library ha avutu successu a espandiri l'accessu a la cunniscenza, e a truvare cchiù libri. Semu semplicementi ccà pi fari la nostra parti: assicurari la preservazioni a longu terminu di sta cullezzioni privata."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Sta cullezzioni risali a la mità di lu 2021. Intantu, Z-Library ha criscutu a un ritmu stupefacenti: hanu aggiuntu circa 3.8 milioni di novi libri. Ci sunnu quarchi duplicati, sicuru, ma la maggiuranza pari essiri libri veramente novi, o scansioni di qualità supiriuri di libri sottomessi prima. Chistu è in gran parti grazzi a lu numaru accrescitu di muderatori vuluntari a Z-Library, e lu so sistema di caricamentu in bloccu cu deduplicazioni. Vulemu cungratulàrili pi sti successi."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Semu cuntenti di annunziari ca avemu pigghiatu tutti i libri ca foru aggiunti a Z-Library tra lu nostru ultimu specchiu e austu 2022. Avemu puru riturnatu e raschiatu quarchi libri ca ci mancavanu la prima vota. In tuttu, sta nova cullezzioni è circa 24TB, chi è assai cchiù granni di l'ultima (7TB). Lu nostru specchiu è ora 31TB in tuttu. Di novu, avemu deduplicatu contru Library Genesis, vistu ca ci sunnu già torrent dispunibili pi sta cullezzioni."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Pi favuri, andati a lu Specchiu di la Libreria Pirata pi vidiri la nova cullezzioni (EDIT: spustatu a <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>). Ci sunnu cchiù infurmazioni ddà supra comu sunnu strutturati i file, e chi ha cambiatu da l'ultima vota. Nun ci linkamu da ccà, vistu ca chistu è sulu un blog ca nun ospita materiali illegali."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Naturalmenti, seedare è puru un ottimu modu pi aiutàrini. Grazzi a tutti chiddi ca stannu seedannu lu nostru set di torrent precedenti. Semu grati pi la risposta positiva, e cuntenti ca ci sunnu tanti pirsuni ca ci teninu a la preservazioni di la cunniscenza e di la cultura in stu modu inusuali."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Comu divintari un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Lu primu sfidu putissi essiri unu surprendenti. Nun è un prubbrema tecnicu, o un prubbrema legali. È un prubbrema psicologicu."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Prima di addentrarini, dui aggiornamenti supra lu Specchiu di la Libreria Pirata (EDIT: spustatu a <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Avemu ricivutu quarchi donazioni estremamenti generusi. La prima fu $10k da un individuu anonimu ca ha puru supportatu \"bookwarrior\", lu fundaturi originali di Library Genesis. Ringraziamenti speciali a bookwarrior pi aviri facilitatu sta donazioni. La secunna fu un autru $10k da un donaturi anonimu, ca si misi in cuntattu dopu lu nostru ultimu rilascio, e fu ispiratu a aiutari. Avemu puru avutu un numaru di donazioni cchiù picculi. Grazzi tantu pi tuttu lu vostru supportu generusu. Avemu quarchi pruggetti novi eccitanti in pipeline chi chistu supportarà, allura restati sintonizzati."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Avemu avutu quarchi difficoltà tecnichi cu la dimensioni di lu nostru secunnu rilascio, ma i nostri torrent sunnu ora attivi e seedannu. Avemu puru ricivutu un'offerta generusa da un individuu anonimu pi seedare la nostra cullezzioni supra i so server di altissima velocità, allura stamu facennu un caricamentu speciali a li so machini, dopu di chi tutti l'autri ca stannu scaricannu la cullezzioni duvissiru vidiri un granni migliuramentu di velocità."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Libri interi ponnu essiri scritti supra lu <em>perché</em> di la preservazioni digitali in generali, e di l'archivismu pirata in particulari, ma facemu un brevi introduzioni pi chiddi ca nun sunnu troppu familiari. Lu munnu sta pruducennu cchiù cunniscenza e cultura ca mai prima, ma puru cchiù di idda si sta perdennu ca mai prima. L'umanità affida in gran parti a li corporazioni comu editori accademici, servizii di streaming, e cumpagni di social media stu patrimoniu, e spissu nun si sunnu dimustrati granni custodi. Viditi lu documentariu Digital Amnesia, o veramente ogni discorsu di Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ci sunnu certi istituzzioni ca fannu un bonu travagghiu a archivìari quantu cchiù ponnu, ma sunnu ligati dâ liggi. Comu pirati, semu nta na pusizzioni unica pi archivìari cullezzioni ca iddi nun ponnu tuccari, a causa dâ liggi supra lu copyright o àutri restrizzioni. Putemu puru specchiari cullezzioni tanti voti, attraversu lu munnu, accussì aumintannu li probabilità di na prupizia prisirvazzioni."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Pi ora, nun nni mettemu a discutiri supra li pro e li contra di la prupitati intillettuali, la murali di vìulari la liggi, riflissioni supra la censura, o lu prubbrema di l'accessu a la cunniscenza e la cultura. Cù tuttu chistu fora di lu modu, nni mettemu a parlari di lu <em>comu</em>. Nni spartemu comu la nostra squatra addivintò pirati archivisti, e li lezioni ca amparamu longu la strata. Ci sunnu tanti sfidi quannu inizi stu viaggiu, e speramu di putiri aiutari a superari certi di chisti."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Cumunità"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "La prima sfida putissi essiri na sorpresa. Nun è nu prubbrema tecnicu, o legali. È nu prubbrema psicologicu: fari stu travagghiu nta l'umbra pò essiri incredibilmenti suli. Dipinnennu di chiddu ca stai pianificannu di fari, e lu to mudellu di minaccia, putissi aviri a essiri assai attentu. A un capu di lu spettru avemu genti comu Alexandra Elbakyan*, la funnatrice di Sci-Hub, ca è assai aperta supra li so attività. Ma idda è a altu risicu di essiri arristata si visitassi un paisi occidentali a stu puntu, e putissi affruntari dicini di anni di prigiuni. È chistu un risicu ca saresti dispostu a pigghiari? Nuatri semu a l'àutru capu di lu spettru; essennu assai attenti a nun lassari traccia, e avennu na forti sicurizza upirativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Comu menzionatu supra HN di \"ynno\", Alexandra inizialmenti nun vulìa essiri canusciuta: \"Li so server eranu cunfigurati pi emettiri missaggi d'errore dettagliati di PHP, cumpresi li percorsi cumpleti di li file di surgenti difettusi, ca eranu sutta lu direttoriu /home/<USER>" Allura, usa username casuali supra li computer ca usi pi sti cosi, in casu ca cunfiguri mali quarchi cosa."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Chista sicritizza, però, veni cu un costu psicologicu. La maiuranza di li pirsuni amanu essiri ricunnisciuti pi lu travagghiu ca fannu, e puru nun poi pigghiari nuddu creditu pi chistu nta la vita reali. Anchi cosi semplici ponnu essiri sfidanti, comu amici ca ti dumannanu chi hai fattu (a un certu puntu \"smanettannu cu lu me NAS / homelab\" diventa vecchiu)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Chistu è pirchì è tantu mpurtanti truvari na cumunità. Poi rinunciari a quarchi sicurizza upirativa confidannu in quarchi amicu assai vicinu, ca sai ca poi fidarti profondamenti. Anchi allura stai attentu a nun scriviri nenti, in casu ca avissiru a cunsegnari li so email a l'autorità, o si li so dispusitivi sunnu cumpromessi in quarchi àutru modu."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Megghiu ancora è truvari quarchi àutru pirata. Si li to amici vicini sunnu ntirissati a unìrisi a tia, granni! Sennò, putissi truvari àutri online. Purtroppu chista è ancora na cumunità di nicchia. Finu a ora avemu truvatu sulu na manciata di àutri ca sunnu attivi nta stu spaziu. Bonu puntu di partenza parunu essiri li forum di Library Genesis, e r/DataHoarder. L'Archive Team havi puru individui simili, anchi si operanu ntâ liggi (ancora ca in quarchi zona grigia di la liggi). Li sceni tradizziunali di \"warez\" e pirateria hannu puru genti ca pensanu in modi simili."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Semu aperti a idei supra comu favuriri la cumunità e esplorari idei. Sentiti libiru di mannari un missaggiu supra Twitter o Reddit. Forse putemu ospitari quarchi tipu di forum o gruppu di chat. Unu di li sfidi è ca chistu pò essiri facilmente censuratu quannu usamu piattaformi cumuni, allura avissimu a ospitarilu nuatri stissi. Ci è puru un compromissu tra aviri sti discussioni cumpletamenti pubblici (cchiù pussibbilità di ntirazzioni) versus farili privati (nun fari sapiri a li pussibbili \"obiettivi\" ca stamu pi raschiarili). Avemu a pinsari supra chistu. Facci sapiri si si ntirissatu a chistu!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Progetti"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quannu facemu un pruggettu, havi na coppia di fasi:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Scelta di duminiu / filosofia: Unni vuliti approssimativamenti cuncintrari, e pirchì? Chi sunnu li to passioni unichi, abilità, e circustanzi ca poi usari a to vantaggiu?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Scelta di obiettivu: Quali cullezzioni specifica mirrerai?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raschiu di metadata: Catalogari nfurmazzioni supra li file, senza scaricari effittivamenti li file (spissu assai cchiù granni) stissi."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Scelta di dati: Basatu supra li metadata, restringennu quali dati sunnu cchiù rilevanti a archivìari ora. Putissi essiri tuttu, ma spissu c'è un modu ragiunevuli di risparmiari spaziu e larghezza di banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raschiu di dati: Effittivamenti pigghiari li dati."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribbuzzioni: Imballari in torrent, annunziarilu quarchi postu, facennu spargiri a li pirsuni."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Chisti nun sunnu fasi cumpletamenti indipinnenti, e spissu intuizioni di na fasi successiva ti rimandanu a na fasi precedenti. Pi esempiu, duranti lu raschiu di metadata putissi capiri ca l'obiettivu ca hai sceltu havi miccanismi difensivi oltre lu to liveddu di abilità (comu blocchi IP), allura torni arreri e trovi un àutru obiettivu."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Scelta di duminiu / filosofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nun c'è carenza di cunniscenza e patrimoniu culturali da sarvari, ca pò essiri sopraffacenti. Chistu è pirchì spissu è utuli pigghiari un mumentu e pinsari supra chi pò essiri lu to cuntributu."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Ognunu ha na manera differente di pinsari a chistu, ma ccà sunnu quarchi dumanni ca putissi fari a tia stissu:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Picchì t'interessa chistu? Chi è ca t'appassiona? Si putemu attruvari na picca di genti ca archivianu li cosi ca a iddi ci curanu, chissu coprissi assai! Sapirai assai cchiù di la media di na pirsuna supra la tò passione, comu quali sunnu li dati impurtanti di sarvari, quali sunnu li megghiu cullezzioni e cumunità online, e accussì via."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Chi abilità hai ca poi usari a tò vantaggiu? Pi esempiu, si sì un espertu di sicurizza online, poi truvari modi di superari li blocchi IP pi obiettivi sicuri. Si sì bravu a organizzari cumunità, allura forsi poi arricugghiri quarchi pirsuna attornu a un obiettivu. È utili canusciri quarchi programmazioni, puru sulu pi mantèniri bona sicurizza upirativa duranti stu prucessu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quantu tempu hai pi chistu? U nostru cunsigghiu saria di cuminciari picca e fari pruggetti cchiù granni comu ti ci abitui, ma putissi divintari tuttu-assorbenti."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Chi saria un'area di altu impattu supra cui cuncintrariti? Si stai pi passari X uri supra l'archiviazioni pirata, allura comu poi ottèniri u megghiu \"ritornu pi u tò sforzu\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Chi sunnu i modi unici ca stai pinsannu supra chistu? Putissi aviri quarchi idea o approcciu interessanti ca l'àutri putissi aviri mancatu."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Ntô nostru casu, ci curavamu in particulari di la preservazioni a longu termini di la scienza. Sapìamu di Library Genesis, e comu era cumpletamenti mirratu assai voti usannu torrents. Amavamu chista idea. Poi un jornu, unu di nuatri pruvò a truvari quarchi libbru scentificu supra Library Genesis, ma nun ci putìa truvari, mettennu in dubbiu quantu cumpletu era veramenti. Poi circamu chiddi libbra online, e li truvamu in àutri posti, chi piantò u seme pi u nostru pruggettu. Anchi prima ca sapìamu di Z-Library, avìamu l'idea di nun pruvà a raccògghiri tutti chiddi libbra manualmenti, ma di cuncintrarisi supra u mirroring di cullezzioni esistenti, e cuntribbuirli a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Scelta di l'obiettivu"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Allura, avemu a nostra area ca stamu guardannu, ora quali cullezzioni specifica duvemu mirrari? Ci sunnu quarchi cosi ca fannu un bonu obiettivu:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Granni"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unicu: nun già ben copertu di àutri pruggetti."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessìbbili: nun usa tanti strati di prutezioni pi impediri a tia di scrappare i so metadata e dati."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Intuizioni speciali: hai quarchi infurmazioni speciali supra st'obiettivu, comu si avissi accessu speciali a sta cullezzioni, o hai caputu comu superari i so difesi. Chistu nun è nicissariu (u nostru pruggettu futuru nun fa nenti di speciali), ma certamenti aiuta!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quannu truvamu i nostri libbra scentifici supra siti web àutri di Library Genesis, pruvamu a capiri comu fìciru a arrivari supra internet. Poi truvamu Z-Library, e capemu ca mentri a maiò parti di i libbra nun fannu a so prima apparizioni ddà, finisciunu ddà. Imparamu supra a so rilazioni cu Library Genesis, e a struttura di incentivi (finanziari) e l'interfaccia utente superiori, tutti dui fìciru di chistu na cullezzioni assai cchiù cumpleta. Poi facemu quarchi scrapping preliminari di metadata e dati, e capemu ca putìamu superari i so limiti di download IP, sfruttannu l'accessu speciali di unu di i nostri membri a tanti servizii proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Mentri stai esplorannu diversi obiettivi, è già impurtanti di nascondiri i to tracci usannu VPN e indirizzi email usa e getta, di cui parleremu cchiù avanti."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Scraping di metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Facemu un pocu cchiù tecnicu ccà. Pi scrappare effettivamenti i metadata di siti web, avemu mantinutu i cosi abbastanza semplici. Usamu script Python, a voti curl, e un database MySQL pi sarvari i risultati. Nun avemu usatu nenti software di scraping sofisticatu ca pò mappare siti web cumplessi, picchì finu a ora avemu avutu bisognu sulu di scrappare unu o dui tipi di pagini enumerannu semplicimenti i id e parsannu l'HTML. Si nun ci sunnu pagini facìli da enumerare, allura putissi aviri bisognu di un crawler propiu ca prova a truvari tutti i pagini."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Prima di cuminciari a scrappare un situ web interu, prova a farlu manualmenti pi un pocu. Passa pi na decina di pagini tu stissu, pi aviri un'idea di comu funziona. A voti ti truvarai già di fronti a blocchi IP o àutri cumpurtamenti interessanti in chistu modu. U stissu vali pi u scraping di dati: prima di addentrarti troppu in st'obiettivu, assicurati ca poi effettivamenti scaricari i so dati in modu effettivu."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Pi superari i restrizioni, ci sunnu quarchi cosi ca poi pruvare. Ci sunnu àutri indirizzi IP o servizii ca ospitanu i stissi dati ma nun hannu i stissi restrizioni? Ci sunnu àutri endpoint API ca nun hannu restrizioni, mentri àutri sì? A chi ritmu di scaricamentu u tò IP veni bloccatu, e pi quantu tempu? O nun veni bloccatu ma rallentatu? Chi succedi si crei un account utente, comu cambianu i cosi allura? Poi usari HTTP/2 pi mantèniri i cunnissioni aperti, e chissu aumenta u ritmu a cui poi richiediri pagini? Ci sunnu pagini ca elencanu multipli file a na vota, e l'infurmazioni elencati ddà sunnu sufficenti?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Cosi ca probabbilmenti voi sarvari includunu:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Tìtulu"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nomu di file / lucazioni"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pò èssiri un ID internu, ma ID comu ISBN o DOI sunnu utili puru."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dimensioni: pi calcolari quantu spazziu di discu ti servi."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): pi cunfirmari ca hai scaricatu u file currettamenti."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data aggiunta/modificata: accussì poi turnari cchiù tardu e scaricari i file ca nun hai scaricatu prima (ancora ca spissu poi usari puru l'ID o l'hash pi chistu)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrizzioni, categoria, tag, auturi, lingua, ecc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Di solitu facemu chistu in dui fasi. Prima scaricamu i file HTML grezzi, di solitu direttamenti in MySQL (pi evitari tanti file nicareddi, di cui parramu cchiù avanti). Poi, in un passu separatu, passamu chiddi file HTML e li analizziamu in tavuli MySQL veri e propri. Accussì nun hai a riscaricari tuttu di novu si scopri un sbagghiu ntô to còdici di analisi, picchì poi semplicimenti riprocessari i file HTML cu lu novu còdici. È puru spissu cchiù facili parallelizzari u passu di prucessu, risparmiannu tempu (e poi scriviri u còdici di prucessu mentri u scraping è in esecuzioni, inveci di aviri a scriviri tutti dui i passaggi assiemi)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Infini, nota ca pi certi obiettivi u scraping di metadata è tuttu chiddu ca c'è. Ci sunnu certi cullezzioni di metadata enormi ca nun sunnu prupiramenti prisirvati."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selezioni di dati"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Spissu poi usari i metadata pi capiri un sottogruppu ragiunevuli di dati da scaricari. Anchi si alla fini voi scaricari tutti i dati, pò essiri utili dari priorità a l'elementi cchiù mpurtanti prima, in casu veni rivelatu e i difesi sunnu migliurati, o picchì avissi bisognu di accattari cchiù dischi, o semplicimenti picchì succedi n'autra cosa ntâ to vita prima ca poi scaricari tuttu."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Pi esempiu, na cullezzioni pò aviri multipli edizioni di la stissa risorsa di basa (comu un libru o un film), unni una è marcata comu la megghiu qualità. Salvannu chiddi edizioni prima avissi assai sensu. Pò vuliri infini salvari tutti l'edizioni, picchì in certi casi i metadata pò essiri taggati in modu sbagghiatu, o ci pò essiri compromessi scunnuti tra edizioni (pi esempiu, la \"megghiu edizioni\" pò essiri megghiu in tanti modi ma peggiu in autri, comu un film ca havi na risoluzioni cchiù àuta ma manca i sottotituli)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Poi puru circari ntâ to database di metadata pi truvari cosi interessanti. Chi è u file cchiù granni ca è ospitatu, e picchì è accussì granni? Chi è u file cchiù nicu? Ci sunnu mudelli interessanti o inaspettati quannu si tratta di certi categorie, lingue, e accussì via? Ci sunnu tituli duplicati o assai simili? Ci sunnu mudelli su quannu i dati foru aggiunti, comu un jornu unni tanti file foru aggiunti tutti assiemi? Spissu poi amparari assai guardannu u dataset in modi differenti."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Ntô nostru casu, avemu deduplicatu i libri di Z-Library contru l'hash md5 in Library Genesis, risparmiannu assai tempu di scaricamentu e spazziu di discu. Chistu è un situazioni abbastanza unicu però. Ntâ maggiuranza di i casi nun ci sunnu database cumpresi di quali file sunnu già prupiramenti prisirvati di pirati cumpagni. Chistu in sè stissu è na granni opportunità pi quarcunu ca c'è fora. Saria fantasticu aviri na panoramica aggiornatu di cosi comu musica e film ca sunnu già largamenti distribuiti su siti di torrent, e sunnu quindi di priorità cchiù bassa pi essiri inclusi in specchi pirati."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scraping di dati"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Ora sì prontu pi scaricari effettivamenti i dati in massa. Comu accennatu prima, a chistu puntu avissi già scaricatu manualmenti un gruppu di file, pi capiri megghiu u cumpurtamentu e i restrizioni di l'obiettivu. Tuttavia, ci sunnu ancora sorprese in serbo pi tia quannu effettivamenti arrivi a scaricari tanti file tutti assiemi."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "U nostru cunsigghiu ccà è principalmenti di mantèniri tuttu semprici. Inizia scaricannu semplicimenti un gruppu di file. Poi usari Python, e poi espandiri a multipli thread. Ma a voti ancu cchiù semprici è generari file Bash direttamenti di u database, e poi eseguiri multipli di chiddi in multipli finestri di terminali pi scalari. Un truccu tecnicu rapidu ca vali la pena menzionari ccà è usari OUTFILE in MySQL, ca poi scriviri ovunque si disattivi \"secure_file_priv\" in mysqld.cnf (e assicurati di disattivari/sovrascriviri AppArmor si sì su Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Archiviamu i dati su dischi duri semprici. Inizia cu chiddu ca hai, e espanditi lentamenti. Pò essiri opprimente pinsari di archiviariccentinaia di TB di dati. Si chistu è u situazioni ca stai affruntannu, semplicimenti metti fora un bonu sottogruppu prima, e ntô to annunciu chiedi aiutu pi archiviaru u restu. Si voi accattari cchiù dischi duri tu stissu, allura r/DataHoarder havi qualchi boni risorsi pi truvari boni affari."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Prova a nun ti preoccupari troppu di sistemi di file sofisticati. È facili cadiri ntô bucu di cunigghiu di configurari cosi comu ZFS. Un dettu tecnicu di cui essiri cuscienti però, è ca tanti sistemi di file nun gestiscinu beni tanti file. Avemu truvatu ca un semprici workaround è criari multipli directory, p'esempiu pi differenti intervalli di ID o prefissi di hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Doppu aviri scaricatu i dati, assicurati di verificari l'integrità di i file usannu l'hash ntô metadata, si dispunibili."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribbuzzioni"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Aviti li dati, accussì aviti la pussiduta dû primu specchiu pirata dû munnu dû vostru obiettivu (probabilmenti). In tanti modi, la parti cchiù difficili è finuta, ma la parti cchiù risicusa è ancora davanti a vui. Doppu tuttu, finu a ora siti stati furtivi; vulannu sutta lu radar. Tuttu chiddu ca aviti a fari era usari un bon VPN sempri, nun riempiri i vostri dati persunali in nuddu formu (ovviamenti), e forsi usari na sessioni di navigaturi spiciali (o puru un computer diversu)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Ora aviti a distribbuiri li dati. Ntô nostru casu, vulìamu prima cuntribbuiri li libri a Library Genesis, ma poi scoprìemu prestu li difficurtati in chissu (ordinamentu di fiction vs non-fiction). Accussì dicidìemu di distribbuiri usannu torrents in stili Library Genesis. Si aviti l'opportunità di cuntribbuiri a un pruggettu esistenti, chissu putissi risparmiarivi assai tempu. Tuttavia, attualmente nun ci sunnu tanti specchi pirata ben organizati."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Accussì, suppunemu ca diciditi di distribbuiri i torrents da sulu. Pruvati a mantèniri chiddi file picciuli, accussì sunnu facili a specchiari in àutri siti web. Doppu aviti a seminari i torrents da sulu, mentri ristati anonimi. Poti usari un VPN (cu o senza port forwarding), o pagari cu Bitcoins mischiati pi un Seedbox. Si nun sapiti chi significanu certi di sti termini, aviti assai lettura da fari, pirchì è mpurtanti ca capiti i compromessi di risicu ccà."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Poti ospitari i file torrent stissi in siti web di torrent esistenti. Ntô nostru casu, scigghiemu di ospitari effettivamenti un situ web, pirchì vulìamu puru spargiri la nostra filusufìa in modu chiaru. Poti fari chissu da sulu in modu simili (usamu Njalla pi i nostri dumini e hosting, pagatu cu Bitcoins mischiati), ma sentiti libiru di cuntattari a nuatri pi fari ospitari i vostri torrents. Stamu circannu di custruiri un indici cumplessu di specchi pirata col tempu, si chista idea pigghia piede."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Pi quantu riguarda la scigghiuta di VPN, assai è statu scrittu già, accussì ripetemu sulu lu cunsigghiu ginirali di scigghiri pi reputazzioni. Pulitichi di no-log testati in tribunali cu longhi storii di prutezzioni di privacy sunnu l'opzioni di risicu cchiù bassu, a nostra opinioni. Notati ca puru quannu faciti tuttu giustu, nun poti mai arrivari a risicu zero. Pi esempiu, quannu semini i vostri torrents, un atturi di nazzioni-statu altamente motivatu putissi prubbabbilmenti taliari i flussi di dati entranti e uscenti pi i servidori VPN, e deduciri cu siti. O putiti semplicimenti sbagliari in qualchi modu. Probabilmenti già avemu sbagliatu, e sbaglieremu ancora. Fortunatamenti, li stati nazzionali nun ci teninu <em>tantu</em> a la pirateria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una dicisioni da fari pi ogni pruggettu, è si pubbricari usannu la stissa identità di prima, o no. Si cuntinuati a usari lu stissu nomu, allura l'errori in la sicurizza upirativa di pruggetti precedenti putissiru turnari a morderivi. Ma pubbricari sutta nomi diversi significa ca nun custruiti na reputazzioni duratura. Avemu scigghiutu di aviri na forti sicurizza upirativa di l'iniziu accussì putemu cuntinuari a usari la stissa identità, ma nun esiteremu a pubbricari sutta un nomu diversu si sbagliamu o si li circustanzi lu richiedinu."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Diffunniri la parola pò essiri difficili. Comu avemu dittu, chista è ancora na cumunità di nicchia. Originalmenti pubbricamu supra Reddit, ma avemu avutu veru successu supra Hacker News. Pi ora, a nostra raccomandazioni è di pubbricari in pochi posti e vidiri chi succedi. E ancora, cuntattatici. Ci piacissi spargiri la parola di cchiù sforzi di archivismu pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Cunclusioni"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Speramu ca chistu è d'aiutu pi novi archivisti pirata ca stannu iniziannu. Semu eccitati di darvi il benvenuto in stu munnu, accussì nun esitati a cuntattari. Preservamu quantu cchiù cunniscenza e cultura dû munnu putemu, e specchiàmmula luntanu e largu."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introducennu lu Specchiu di la Libreria Pirata: Presirvannu 7TB di libri (ca nun sunnu in Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Stu pruggettu (EDIT: spustatu a <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a>) mira a cuntribbuiri a la preservazioni e liberazioni di lu cunniscenza umanu. Facemu la nostra picciula e umili cuntribbuzzioni, sui passi di li grandi prima di nuatri."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Lu focus di stu pruggettu è illustratu dû so nomu:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Violamu deliberatamenti la liggi di copyright in la maggiuranza di li paisi. Chissu ni permette di fari quarchi cosa ca li entità legali nun ponnu fari: assicurari ca i libri sunnu specchiati luntanu e largu."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Libreria</strong> - Comu la maggiuranza di li librerie, ci focalizzamu principalmenti supra materiali scritti comu i libri. Putemu espanderi a àutri tipi di media in futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Specchiu</strong> - Semu strettamenti un specchiu di librerie esistenti. Ci focalizzamu supra la preservazioni, nun supra fari i libri facili a circari e scaricari (accessu) o favuriri na granni cumunità di genti ca cuntribbuinu novi libri (sourcing)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La prima libreria ca avemu specchiatu è Z-Library. Chista è na libreria pupulari (e illegali). Hanu pigghiatu la cullezzioni di Library Genesis e l'hanu fatta facili a circari. Supra chissu, sunnu diventati assai efficaci a solicità novi cuntribbuzzioni di libri, incentivannu l'utenti cuntribuenti cu vari vantaggi. Attualmente nun cuntribbuinu sti novi libri a Library Genesis. E a differenza di Library Genesis, nun fannu la so cullezzioni facili a specchiari, chi previene la larga preservazioni. Chistu è mpurtanti pi lu so mudellu di business, pirchì charginu soldi pi accedere a la so cullezzioni in massa (cchiù di 10 libri pi jornu)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nun facemu giudizi murali supra lu fattu di caricari soldi pi l'accessu in bloccu a na cullezzioni di libri illegali. È senza dubbiu ca Z-Library ha avutu successu a espandiri l'accessu a la cunniscenza, e a truvare cchiù libri. Semu semplicementi ccà pi fari la nostra parti: assicurari la preservazioni a longu terminu di sta cullezzioni privata."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Ci piacissi invitarivi a aiutari a preservari e liberari lu cunniscenza umanu scaricannu e seminannu i nostri torrents. Viditi la pàggina dû pruggettu pi cchiù nfurmazzioni supra comu sunnu organizati i dati."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Vi invitiamu puru a cuntribbuiri cu li vostri idei supra quali cullezzioni duplicari dopu, e comu fari. Insemi putemu ottiniri assai. Chistu è sulu un picculu cuntributu tra tanti àutri. Grazii, pi tuttu chiddu ca faciti."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nun ligamu li schedi di stu blog. Truvàtili vuatri stissi.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump di ISBNdb, o Quanti Libri Sunnu Preservati Pi Sempri?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si avissimu a deduplicari currettamenti li schedi di li bibliotechi ombra, chi percentuali di tutti li libri di lu munnu avissimu preservatu?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Cù lu Pirate Library Mirror (EDIT: spustatu a <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a>), lu nostru scopu è di pigghiari tutti li libri di lu munnu, e preservarli pi sempri.<sup>1</sup> Tra li nostri torrents di Z-Library, e li torrents originali di Library Genesis, avemu 11,783,153 schedi. Ma quantu sunnu, veramenti? Si avissimu a deduplicari currettamenti chisti schedi, chi percentuali di tutti li libri di lu munnu avissimu preservatu? Avissimu veramenti piaciri di aviri quarchi cosa comu chistu:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of di l'eredità scritta di l'umanità preservata pi sempri"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Pi un percentuali, avemu bisognu di un denominaturi: lu numaru tutali di libri mai pubblicati.<sup>2</sup> Prima di la fine di Google Books, un ingegneri supra lu pruggettu, Leonid Taycher, <a %(booksearch_blogspot)s>pruvò a stimari</a> stu numaru. Arrivò — in modu scherzusu — a 129,864,880 (“almenu finu a duminica”). Stimò stu numaru custruennu un database unificatu di tutti li libri di lu munnu. Pi chistu, riunì diversi datasets e poi li unì in vari modi."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Comu un rapidu aside, c'è un'àutra pirsuna ca pruvò a catalogari tutti li libri di lu munnu: Aaron Swartz, lu tardu attivista digitali e co-fundaturi di Reddit.<sup>3</sup> Iddu <a %(youtube)s>iniziò Open Library</a> cù lu scopu di “una pàggina web pi ogni libru mai pubblicatu”, cumbinannu dati da tanti fonti diversi. Finì pi pagari lu prezzu ultimu pi lu so travagghiu di preservazioni digitali quannu fu perseguitatu pi scaricari in massa articuli accademici, purtannu a lu so suicidiu. Senza diri, chistu è unu di li mutivi pi cui lu nostru gruppu è pseudonimu, e pi cui semu assai attenti. Open Library è ancora eroicamente gestita da li pirsuni di l'Internet Archive, cuntinuannu l'eredità di Aaron. Torneremu a chistu dopu in stu post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Ntô post di blog di Google, Taycher discrivi quarchi di li sfidi cù stimari stu numaru. Prima, chi custituisci un libru? Ci sunnu quarchi definizzioni pussibili:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copii fisichi.</strong> Ovviamenti chistu nun è assai utili, pirchì sunnu sulu duplicati di lu stissu materiali. Saria bellu si putissimu preservari tutti l'annotazioni ca li pirsuni fannu ntê libri, comu li famusi “scarabocchi ntê margini” di Fermat. Ma ahimè, chistu rimarrà un sonnu di l'archivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Opiri”.</strong> Pi esempiu “Harry Potter e la Camera di li Sicreti” comu un cuncettu logicu, ca include tutti li versioni di iddu, comu diversi traduzzioni e ristampe. Chistu è un tipu di definizzioni utili, ma pò essiri difficili tracciari la linia di chi conta. Pi esempiu, probabilmenti vulissimu preservari diversi traduzzioni, ancu si ristampe cù sulu picculi differenzi pò nun essiri tantu impurtanti."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edizioni”.</strong> Qui conti ogni versione unica di un libru. Si quarchi cosa di iddu è diversa, comu una copertina diversa o una prefazioni diversa, conta comu una edizioni diversa."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Schedi.</strong> Quannu si travagghia cù bibliotechi ombra comu Library Genesis, Sci-Hub, o Z-Library, c'è un'àutra considerazioni. Ci pò essiri multipli scansioni di la stissa edizioni. E li pirsuni pò fari versioni migliori di schedi esistenti, scannannu lu testu usannu OCR, o rettificannu pàggini ca foru scannati a un angulu. Vulissimu cuntari sulu chisti schedi comu una edizioni, chi richiediria bonu metadata, o deduplicazioni usannu misuri di similitudini di documenti."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edizioni” parinu la definizzioni cchiù pratica di chi sunnu li “libri”. Convenientementi, chista definizzioni è puru usata pi assignari numeri ISBN unici. Un ISBN, o International Standard Book Number, è cumunimenti usatu pi lu cummerciu internaziunali, pirchì è integratu cù lu sistema internaziunali di codici a barre (“International Article Number”). Si vuliti vinniri un libru ntê negozi, ci voli un codice a barre, allura ottiniti un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Lu post di blog di Taycher menziona ca mentri li ISBN sunnu utili, nun sunnu universali, pirchì foru veramente aduttati sulu a mità di l'anni settanta, e nun dappertuttu ntô munnu. Tuttavia, ISBN è probabilmenti lu identificatori cchiù usatu di edizioni di libri, allura è lu nostru megghiu puntu di partenza. Si putemu truvari tutti li ISBN ntô munnu, ottinimu una lista utili di quali libri ancora bisognu di essiri preservati."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Allura, unni pigghiamu li dati? Ci sunnu un numaru di sforzi esistenti ca stannu pruvannu a cumpilari una lista di tutti li libri ntô munnu:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Dopu tuttu, ficeru sta ricerca pi Google Books. Tuttavia, lu so metadata nun è accessibili in massa e piuttostu difficili a raschiari."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Comu menzionatu prima, chistu è tuttu lu so missioni. Hanu ricavatu massicci quantità di dati di bibliotechi da bibliotechi cooperanti e archivi nazziunali, e cuntinuanu a fari accussì. Hanu puru bibliotecari vuluntari e un gruppu tecnicu ca stannu pruvannu a deduplicari i registri, e taggarli cù tutti i tipi di metadata. Lu megghiu di tuttu, lu so dataset è cumpletamenti apertu. Poti semplicimenti <a %(openlibrary)s>scaricarlu</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Chistu è un situ web gestitu da lu non-profit OCLC, ca veni sistemi di gestione di bibliotechi. Aggreganu metadata di libri da tanti bibliotechi, e lu fannu dispunibili attraversu lu situ web WorldCat. Tuttavia, fannu puru soldi vinnennu sti dati, allura nun sunnu dispunibili pi scaricari in massa. Hanu quarchi dataset in massa cchiù limitati dispunibili pi scaricari, in cooperazioni cù bibliotechi specifici."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Chistu è lu sugettu di stu postu di blog. ISBNdb raccoglie dati di vari siti web pi metadata di libri, in particulari dati di prezzu, chi poi vennu a i libbrai, accussì ponu stabiliri i prezzi di i so libri in cunfurmità cu lu restu di lu mercatu. Dapoi chi l'ISBN sunnu abbastanza universali oghji, anu effittivamenti custruitu una \"pagina web pi ogni libru\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Vari sistemi di bibliuteca individuali e archivi.</strong> Ci sunnu bibliuteci e archivi chi nun sunnu stati indicizzati e aggregati di nuddu di chiddi sopra, spissu pirchì sunnu sottufinanziati, o pi àutri mutivi nun vonnu sparteri i so dati cu Open Library, OCLC, Google, e accussì via. Assai di chisti hannu registri digitali accessibili attraversu l'internet, e spissu nun sunnu assai beni prutetti, accussì si voi aiutari e aviri un pocu di divertimentu amparannu di sistemi di bibliuteca strani, chisti sunnu punti di partenza eccellenti."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In stu postu, semu cuntenti di annunziari una piccula rilascita (paragunatu a i nostri rilasciti precedenti di Z-Library). Avemu raccogghiu la maiur parti di ISBNdb, e fattu i dati dispunibili pi torrenting supra lu situ web di Pirate Library Mirror (EDIT: spustatu a <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a>; nun lu linkamu direttamente ccà, basta circallu). Chisti sunnu circa 30.9 miliuna di registri (20GB comu <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). Supra lu so situ web dichiàranu chi effittivamenti hannu 32.6 miliuna di registri, accussì putemu aviri persu qualchi cosa, o <em>iddi</em> putissiru fari quarchi cosa sbagghiata. In ogni casu, pi ora nun spartemu esattamenti comu l'avemu fattu — lassamu chistu comu un eserciziu pi lu letturi. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Chiddu chi spartemu è quarchi analisi preliminari, pi pruvà a avvicinàrisi a stimari lu numaru di libri ntô munnu. Avemu guardatu a tri datasets: stu novu dataset di ISBNdb, la nostra rilascita originali di metadata chi avemu raccogghiu di la bibliuteca ombra Z-Library (chi include Library Genesis), e lu dump di dati di Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Cumincemu cu quarchi numaru approssimativu:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In tutti dui Z-Library/Libgen e Open Library ci sunnu assai cchiù libri chi ISBN unici. Significa chistu chi assai di chiddi libri nun hannu ISBN, o è semplicimenti chi manca lu metadata di ISBN? Putemu prubabilmenti rispunni a sta dumanna cu una cumminazzioni di accuppiamentu automatizatu basatu supra àutri attributi (tìtulu, auturi, edituri, ecc), tirannu n'àutri fonti di dati, e estraennu ISBN di li scansioni di li libri stessi (ntô casu di Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quanti di chiddi ISBN sunnu unici? Chistu è megghiu illustratu cu un diagramma di Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Pi essiri cchiù precisu:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Fummu surpresi di quantu pocu sovrappusizioni ci sia! ISBNdb hà un'enormi quantità di ISBN chi nun appàrunu né in Z-Library né in Open Library, e lu stessu vali (a un gradu minuri ma ancora sustanziali) pi l'àutri dui. Chistu solleva assai novi dumanni. Quant'è aiuterebbe l'accuppiamentu automatizatu a taggari i libri chi nun eranu taggati cu ISBN? Ci sarìa assai accuppiamenti e quindi un'aumentu di sovrappusizioni? Inoltre, chi succederìa si purtamu un 4u o 5u dataset? Quanta sovrappusizioni vidiremmo allura?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Chistu ci dà un puntu di partenza. Ora putemu guardari a tutti l'ISBN chi nun eranu ntô dataset di Z-Library, e chi nun currisponninu mancu a i campi di tìtulu/auturi. Chistu ci pò dari un manicu supra la preservazioni di tutti i libri ntô munnu: prima raccogghiennu l'internet pi scansioni, poi andannu fora in vita reali pi scanari libri. Stu ultimu putissi essiri ancu finanziatu da a folla, o guidatu da \"ricumpensi\" di genti chi vulissiru vidiri certi libri digitalizzati. Tutto chistu è una storia pi un tempu diversu."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si voi aiutari cu quarchi cosa di chistu — analisi ulteriuri; raccogghiennu cchiù metadata; truvannu cchiù libri; OCR di libri; facennu chistu pi àutri duminii (es. articuli, audiolibri, film, programmi TV, rivisti) o ancu facennu quarchi di sti dati dispunibili pi cosi comu ML / addestramentu di mudelli di lingua larga — pi favuri cuntattami (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si sì specificamenti interessatu a l'analisi di dati, stamu travagghiannu pi fari i nostri datasets e script dispunibili in un formatu cchiù faciuli di usari. Saria fantasticu si putissi semplicimenti forkari un notebook e cuminciari a ghjucari cu chistu."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Infini, si voi sustèniri stu travagghiu, pi favuri cunsidira di fari una donazioni. Chistu è un'operazioni gestita interamenti da vuluntari, e la to cuntribuzioni fa una granni diffirenza. Ogni pocu aiuta. Pi ora accettamu donazioni in cripto; vedi la pagina di Donazioni supra l'Archiviu di Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pi quarchi definizioni ragiunevuli di \"pi sempri\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Naturalmenti, l'eredità scritta di l'umanità è assai cchiù di libri, specialmenti oghji. Pi lu scopu di stu postu e i nostri rilasciti recenti ci stamu cuncentrannu supra i libri, ma i nostri interessi si stendenu cchiù luntanu."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Ci hè assai cchiù chi si pò diri supra Aaron Swartz, ma vulìamu solu mintuvallu brevemente, postu chi ghjoca un rolu cruciale in sta storia. Cumu passa lu tempu, cchiù genti putissi incuntrari lu so nomu pi la prima vota, e poi immersi in lu cunigghiu stessu."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La finestra critica di bibliuteci ombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Comu putemu pretendiri di preservari i nostri cullezzioni in perpetuità, quannu già si avvicinanu a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versione cinese 中文版</a>, discuti supra <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Ntô l'Archiviu di Anna, ni veni spissu dumannatu comu putemu affirmari di prisirvari i nostri cullezzioni in perpetuità, quannu la diminsioni tutali è già vicina a 1 Petabyte (1000 TB), e sta ancora criscennu. In st'articulu ni talieremu la nostra filosofia, e vidiremu pirchì la prossima dicina d'anni è critica pi la nostra missioni di prisirvari la cunniscenza e la cultura di l'umanità."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "La <a %(annas_archive_stats)s>diminsioni tutali</a> di i nostri cullezzioni, nni l'ùrtimi misi, suddivisa pi numaru di seeders di torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorità"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Pirchì ni curamu tantu di carti e libri? Mettimu da parti la nostra cridenza funnamintali ntô prisirvamentu in ginirali — putemu scrìviri n'àutru articulu supra chistu. Allura, pirchì carti e libri in particulari? La risposta è semprici: <strong>dinsità di nfurmazzioni</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Pi ogni megabyte di archiviazzioni, lu testu scrittu archivia la cchiù nfurmazzioni di tutti i media. Mentri ni curamu di tutti dui, cunniscenza e cultura, ni curamu cchiù di la prima. Giniralmenti, truvamu na girarchia di dinsità di nfurmazzioni e mpurtanza di prisirvamentu ca pari all'incirca accussì:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Carti accademichi, giurnali, rapporti"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dati urganici comu sequenzi di DNA, semi di piante, o campioni microbici"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libri di non-finzioni"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Codici di software di scienza e ingegneria"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dati di misurazioni comu misurazioni scentifiche, dati economici, rapporti aziendali"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Siti web di scienza e ingegneria, discussioni online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Rivisti di non-finzioni, giurnali, manuali"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Trascrizioni di non-finzioni di discorsi, documentarii, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dati interni di corporazioni o guverni (fughe)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Registri di metadata giniralmenti (di non-finzioni e finzioni; di àutri media, arti, pirsuni, ecc.; inclùsi recensioni)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dati giugrafici (p.es. mappe, rilievi geologici)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Trascrizioni di prucidimenti legali o di tribunali"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versioni finzionali o d'intrattenimentu di tutti i supracitati"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La classifica in sta lista è alquantu arbitraria — certi articuli sunnu pari o ci sunnu disaccordi ntô nostru gruppu — e prubabilmenti ni stamu scurdannu di qualchi categuria mpurtanti. Ma chistu è all'incirca comu ni prioritizzamu."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Certi di sti articuli sunnu troppu differenti di l'àutri pi ni preoccupari (o sunnu già curati di àutri istituzioni), comu dati urganici o dati giugrafici. Ma la maggiuranza di l'articuli in sta lista sunnu effittivamenti mpurtanti pi nuàutri."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un altru fatturi mpurtanti ntâ nostra prioritizzazzioni è quantu a risicu è un certu travagghiu. Preferemu cuncentrarini supra travagghi ca sunnu:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rari"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamenti sottufucalizzati"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamenti a risicu di distruzzioni (p'esempiu, pi guerra, tagghi di finanziamenti, cause legali, o persecuzzioni pulitica)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Infini, ni curamu dâ scala. Avemu tempu e dinari limitati, accussì preferemu passari un misi a sarvari 10.000 libri piuttostu ca 1.000 libri — si sunnu circa ugualmenti valiusi e a risicu."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteche ombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ci sunnu tanti organizzazzioni ca hannu missioni simili, e prioritati simili. Di fattu, ci sunnu biblioteche, archivi, laburatorî, musei, e àutri istituzzioni incaricati di la preservazzioni di stu tipu. Tanti di chiddi sunnu beni finanziati, di guverni, individui, o corporazzioni. Ma hannu un enormi puntu cecu: lu sistema legali."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Ccà si trova lu rolu unicu di li biblioteche ombra, e la raggiuni pi cui esisti l'Archiviu di Anna. Putemu fari cosi ca àutri istituzzioni nun sunnu permessi di fari. Ora, nun è (spissu) ca putemu archiviari materiali ca sunnu illegali di preservari altrove. No, è legali in tanti posti custruiri un archiviu cu quarsiasi libri, articuli, rivisti, e accussì via."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ma chiddu ca spissu mancanu l'archivi legali è <strong>ridundanza e longevità</strong>. Esistunu libri di cui esisti sulu na copia in quarchi bibliotecca fisica da quarchi parti. Esistunu registri di metadata guardati di na sula corporazzioni. Esistunu giornali preservati sulu supra microfilm in un sulu archiviu. Li biblioteche ponnu aviri tagghi di finanziamenti, li corporazzioni ponnu falliri, li archivi ponnu essiri bombardati e brusciati a terra. Chistu nun è ipoteticu — chistu succedi sempri."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "La cosa ca putemu fari unicamente all'Archiviu di Anna è archiviarini tanti copie di travagghi, a scala. Putemu raccogghiri articuli, libri, rivisti, e cchiù, e distribbuirili in massa. Attualmente facemu chistu attraversu torrent, ma li tecnuluggìi esatti nun sunnu mpurtanti e canciarannu cu lu tempu. La parti mpurtanti è distribbuiri tanti copie attraversu lu munnu. Sta citazzioni di cchiù di 200 anni fa ancora sona vera:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Lu persu nun pò essiri ricuperatu; ma lassamu sarvari chiddu ca resta: nun cu volti e chiavi ca li difennunu di l'occhiu pubblicu e l'usu, in consignarili a lu scartu di lu tempu, ma cu na tali multiplicità di copie, ca li metti fora di la portata di l'accidenti.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Na nota rapida supra lu duminiu pubblicu. Datu ca l'Archiviu di Anna si cuncentra unicamente supra attività ca sunnu illegali in tanti posti ntô munnu, nun ni preoccupamu di cullezzioni largamenti dispunibili, comu li libri di duminiu pubblicu. Li entità legali spissu già si curanu beni di chistu. Tuttavia, ci sunnu considerazzioni ca a voti ni fannu travagghiari supra cullezzioni pubblicamenti dispunibili:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Li registri di metadata ponnu essiri visti liberamenti supra lu situ Worldcat, ma nun scaricati in massa (finu a quannu li <a %(worldcat_scrape)s>scrapemu</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Lu codice pò essiri open source supra Github, ma Github comu un tuttu nun pò essiri facirmenti mirratu e accussì preservatu (ancora ca in stu casu particulari ci sunnu copie sufficientementi distribbuiti di la maiuranza di li repositori di codice)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit è liberu di usari, ma ha recentemente misi misuri anti-scraping stringenti, a causa di l'addestramentu di LLM affamati di dati (cchiù supra chistu dopu)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Na multiplicità di copie"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tornannu a la nostra dumanna originali: comu putemu pretendiri di preservari li nostri cullezzioni in perpetuità? Lu prubbrema principali ccà è ca la nostra cullezzioni ha <a %(torrents_stats)s>crescitu</a> a un ritmu rapidu, scrapannu e open-sourcing quarchi cullezzioni massicci (supra lu travagghiu stupendu già fattu di àutri biblioteche ombra di dati aperti comu Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Stu criscimentu di dati fa cchiù difficili pi li cullezzioni di essiri mirrati ntô munnu. L'archiviazzioni di dati è caru! Ma semu ottimisti, specialmenti quannu osservamu li tri tendenzi seguenti."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Avemu cogghiu lu fruttu bassu"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Chistu segui direttamenti di li nostri prioritati discusse supra. Preferemu travagghiari supra la liberazzioni di cullezzioni granni prima. Ora ca avemu assicuratu quarchi di li cullezzioni cchiù granni ntô munnu, ci aspettemu ca lu nostru criscimentu sia assai cchiù lentu."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "C'è ancora una longa coda di cullezzioni cchiù nichi, e novi libri vennu scannati o pubblicati ogni jornu, ma lu ritmu sarà probabbilmenti assai cchiù lentu. Putemu ancora raddoppiari o puru triplicari in diminsioni, ma supra un pirìudu di tempu cchiù longu."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Li costi di archiviazzioni cuntinuanu a calari esponenzialmenti"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "A lu mumentu di scrittura, li <a %(diskprices)s>prezzi di li dischi</a> pi TB sunnu circa $12 pi dischi novi, $8 pi dischi usati, e $4 pi nastri. Si semu cunsirvativi e guardamu sulu a li dischi novi, chistu significa ca archiviari un petabyte costa circa $12,000. Si assumemu ca la nostra libreria triplica di 900TB a 2.7PB, chistu significassi $32,400 pi rispecchiari tutta la nostra libreria. Aggiungennu l'elettricità, lu costu di l'autri hardware, e accussì via, arrutunnamu a $40,000. O cu nastri cchiù comu $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Da un latu <strong>$15,000–$40,000 pi la summa di tutta la cunniscenza umana è un affaru</strong>. Da l'autru latu, è un pocu caru aspittari tunnillati di copie cumplete, spiciarmenti si vulissimu puru ca chiddi pirsuni cuntinuassiru a seminari li so torrent pi lu beni di l'autri."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Chistu è oggi. Ma lu progressu camina avanti:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Li costi di li hard drive pi TB sunnu stati ridotti circa a un terzu supra l'urtimi 10 anni, e probabbilmenti cuntinuirannu a calari a un ritmu simili. Li nastri parinu essiri supra una traiettoria simili. Li prezzi di l'SSD stannu calannu ancora cchiù veloci, e putissiru superari li prezzi di l'HDD a la fini di la dicada."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendenzi di prezzi di HDD da fonti differenti (clicca pi vidiri lu studiu)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si chistu si manteni, allura in 10 anni putemu guardari a sulu $5,000–$13,000 pi rispecchiari tutta la nostra cullezzioni (1/3), o puru menu si criscemu menu in diminsioni. Mentri ancora assai dinari, chistu sarà accessibili pi tanti pirsuni. E putissi essiri ancora megghiu grazzi a lu prossimu puntu…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Migliuramenti in densità di l'informazioni"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Attualmenti archiviamu li libri nei formati grezzi ca ci vennu dati. Certu, sunnu compressi, ma spissu sunnu ancora grandi scanni o fotografii di pagini."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Finora, l'unichi opzioni pi riduciri la diminsioni tutali di la nostra cullezzioni sunnu stati attraversu una compressioni cchiù aggressiva, o deduplicazioni. Tuttavia, pi ottiniri risparmi significativi, tutti dui sunnu troppu perdenti pi i nostri gusti. La compressioni pesanti di li fotografii pò fari lu testu appena leggibili. E la deduplicazioni richiedi alta fiducia ca li libri sunnu esattamenti li stissi, ca spissu è troppu inaccurata, spiciarmenti si li cuntinuti sunnu li stissi ma li scanni sunnu fatti in occasioni differenti."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "C'è sempri stata una terza opzioni, ma la so qualità è stata accussì pessima ca nun l'avemu mai cunsidirata: <strong>OCR, o Riconoscimentu Otticu di Caratteri</strong>. Chistu è lu prucessu di cunvirtiri fotografii in testu semprici, usannu l'AI pi rilevari li caratteri ne li fotografii. Strumenti pi chistu esistinu da tempu, e sunnu stati abbastanza decenti, ma \"abbastanza decenti\" nun è abbastanti pi scopi di preservazioni."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tuttavia, recenti mudelli di deep-learning multimodali hannu fattu progressi estremamenti rapidi, ancu se ancora a costi elevati. Aspittamu ca sia l'accuratezza ca li costi migliurinu drammaticamenti ne li prossimi anni, a lu puntu ca divintarà realisticu applicari a tutta la nostra libreria."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Migliuramenti di OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quannu chistu succedi, probabbilmenti ancora preserveremu li file originali, ma in aggiunta putemu aviri una versione assai cchiù piccola di la nostra libreria ca la maggiuranza di li pirsuni vorrà rispecchiari. Lu colpu di scena è ca lu testu grezzu stissu si comprimi ancora megghiu, ed è assai cchiù facili da deduplicari, danduci ancora cchiù risparmi."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "In generale, nun è irrealisticu aspittarisi almeno una riduzioni di 5-10x in diminsioni tutali di file, forsi ancora di cchiù. Ancu cu una riduzioni cunsirvativa di 5x, guarderemmu a <strong>$1,000–$3,000 in 10 anni ancu si la nostra libreria triplica in diminsioni</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Finestra critica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si sti pruvvidenzi sunnu accurati, avemu <strong>bisognu sulu di aspittari un paru di anni</strong> prima ca tutta la nostra cullezzioni sarà ampiamenti rispecchiata. Accussì, ne li paroli di Thomas Jefferson, \"posta fora di la portata di l'accidenti.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Sfortunatamenti, l'avventu di LLM, e lu so addestramentu famelicu di dati, ha misi tanti detentori di diritti d'auturi in difensiva. Ancora cchiù di quantu eranu già. Tanti siti web stannu facennu cchiù difficili raschiare e archiviare, li cause legali volanu in giru, e tuttu lu tempu li librerie fisiche e li archivi cuntinuanu a essiri trascurati."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Putemu sulu aspittarici ca sti tendenzi cuntinuinu a peggiorari, e tanti opiri si perderannu assai prima ca entrinu in lu duminiu publicu."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Semu a la vigilia di na rivoluzzioni ntâ prisirvazzioni, ma <q>chiddu ca è persu nun pò èssiri ricuperatu.</q></strong> Avemu na finestra critica di circa 5-10 anni duranti li quali è ancora abbastanza caru operari na libreria ombra e criari tanti specchi ntô munnu, e duranti li quali l'accessu nun è ancora statu cumplitamenti chiusu."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si putemu superari sta finestra, allura avemu veramente prisirvatu lu sapiri e la cultura di l'umanità in perpetuità. Nun avemu a lassari ca stu tempu si spreca. Nun avemu a lassari ca sta finestra critica si chiudi supra di nuatri."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Andamu avanti."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accessu esclusivu pi li cumpagni LLM â cullizzioni cchiù granni di libri di non-fizzioni cinesi ntô munnu"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versioni cinisi 中文版</a>, <a %(news_ycombinator)s>Discuti supra Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archiviu di Anna ha acquistatu na cullizzioni unica di 7,5 miliuna / 350TB di libri di non-fizzioni cinesi — cchiù granni di Library Genesis. Semu disposti a dari a na cumpagnia LLM accessu esclusivu, in cambiu di OCR di alta qualità e estrazzioni di testu.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Chistu è un postu di blogu curtu. Stamu circannu na cumpagnia o istituzzioni ca ni aiuta cu l'OCR e l'estrazzioni di testu pi na cullizzioni massiccia ca avemu acquistatu, in cambiu di accessu esclusivu anticipatu. Doppu lu periodu di embargo, rilasciremu naturalmente tutta la cullizzioni."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Lu testu accademicu di alta qualità è estremamenti utili pi l'addistramentu di LLM. Mentri la nostra cullizzioni è cinisi, chistu pò èssiri ancora utili pi l'addistramentu di LLM in inglesi: li mudelli parunu codificari cuncetti e sapiri indipinnenti di la lingua d'origine."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Pi chistu, lu testu ha bisognu di èssiri estrattu di li scansioni. Chi ci guadagna l'Archiviu di Anna? La ricerca di testu cumpletu di li libri pi li so utenti."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Picchì li nostri obiettivi s'allineanu cu chiddi di li sviluppatori di LLM, stamu circannu un collaboraturi. Semu disposti a dari a vui <strong>accessu esclusivu anticipatu a sta cullizzioni in bloccu pi 1 annu</strong>, si putiti fari un OCR e un'estrazzioni di testu adatti. Si siti disposti a sparteri tuttu lu codice di la vostra pipeline cu nuatri, semu disposti a embargo la cullizzioni pi cchiù tempu."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pàggini d'esempiu"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Pi pruvàrini ca aviti na bona pipeline, ccà sunnu quarchi pàggina d'esempiu pi cuminciari, di un libru supra li superconduttori. La vostra pipeline avissi a gestiri currettamenti la matimàtica, li tavuli, li grafici, li noti a piè di pàggina, e accussì via."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Mandati li vostri pàggini prucissati â nostra email. Si parunu boni, vi mannaremu cchiù in privatu, e ci aspettemu ca siati capaci di curriri rapidamente la vostra pipeline puru supra chiddi. Quannu semu soddisfatti, putemu fari un accordu."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Cullizzioni"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Quarchi infurmazzioni in cchiù supra la cullizzioni. <a %(duxiu)s>Duxiu</a> è na massiccia basa di dati di libri scannati, criata di lu <a %(chaoxing)s>SuperStar Digital Library Group</a>. La maiuranza sunnu libri accademici, scannati pi farili dispunibili digitalmenti a l'università e li bibliotechi. Pi la nostra audienza di lingua inglesi, <a %(library_princeton)s>Princeton</a> e l'<a %(guides_lib_uw)s>Università di Washington</a> hannu boni panoramiche. C'è puru un articulu eccellente ca dà cchiù sfunnu: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercatilu ntô Archiviu di Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Li libri di Duxiu sunnu stati piratati pi assai tempu supra l'internet cinisi. Di solitu vennu vinuti pi menu di un dollaru di li rivenditori. Vennu tipicamenti distribuiti usannu l'equivalenti cinisi di Google Drive, ca spissu è statu hackatu pi permettiri cchiù spaziu di archiviazzioni. Quarchi dittagghiu tecnicu pò èssiri truvatu <a %(github_duty_machine)s>ccà</a> e <a %(github_821_github_io)s>ccà</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Anchi si li libri sunnu stati distribuiti semi-pubblicamenti, è abbastanza difficili ottènirili in bloccu. Avemu avutu chistu altu ntâ nostra lista di cose da fari, e avemu destinatu parechii misi di travagghiu a tempu pienu pi chistu. Tuttavia, ricintimenti un vuluntariu incredibili, straordinariu e talentuatu ci ha cuntattatu, dicennu ca avìa già fattu tuttu stu travagghiu — a granni spisa. Ci ha spartutu tutta la cullizzioni, senza aspittari nenti in cambiu, eccettu la garanzia di prisirvazzioni a longu terminu. Veramenti rimarchevuli. Hanu accettatu di circari aiutu in stu modu pi fari l'OCR di la cullizzioni."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La cullizzioni è di 7.543.702 file. Chistu è cchiù di Library Genesis non-fizzioni (circa 5,3 miliuna). La dimensioni tutali di li file è circa 359TB (326TiB) ntâ so forma attuali."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Semmu aperti a àutri pruposti e idee. Sulu cuntattatici. Visita l'Archiviu di Anna pi cchiù infurmazzioni supra li nostri cullizzioni, li sforzi di prisirvazzioni, e comu putiti aiutari. Grazzi!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Attinzioni: stu postu dû blog è statu deprecatu. Avemu dicisu ca IPFS nun è ancora prontu pi lu primu tempu. Continueremu a ligari a file su IPFS da l'Archiviu di Anna quannu pussìbbili, ma nun lu ospiteremu cchiù nuatri stissi, e mancu ricumandamu a l'àutri di specchiari usannu IPFS. Pi favuri, taliati la nostra pàggina di Torrenti si vuliti aiutari a prisirvari a nostra cullizzioni."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Aiuta a disseminari Z-Library su IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Comu gestiri na libreria ombra: upirazzioni a l'Archiviu di Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nun c'è <q>AWS pi carità ombra,</q> allura comu gestemu l'Archiviu di Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Gestisciu <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>, lu muturi di ricerca open-source non-profit cchiù granni dû munnu pi <a %(wikipedia_shadow_library)s>librerie ombra</a>, comu Sci-Hub, Library Genesis, e Z-Library. Lu nostru scopu è di fari la cunniscenza e la cultura facilmente accessìbbili, e infini di custruiri na cumunità di pirsuni ca insemmula archivianu e prisirvanu <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tutti i libri dû munnu</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In st'artìculu vi mustrarò comu gestemu stu situ web, e i sfidi unichi ca venunu cu l'upirari un situ web cu status ligali discutìbbili, postu ca nun c'è \"AWS pi carità ombra\"."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Vidi puru l'artìculu frati <a %(blog_how_to_become_a_pirate_archivist)s>Comu divintari un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Token d'innovazzioni"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Cumincemu cu la nostra pila tecnologica. È deliberatamente noiosa. Usamu Flask, MariaDB, e ElasticSearch. Chistu è letteralmenti tuttu. La ricerca è in gran parti un prubbrema risoltu, e nun avemu intenzioni di rinvintralla. In più, avemu a spènniri i nostri <a %(mcfunley)s>token d'innovazzioni</a> in àutru: nun essiri abbattuti da l'autorità."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Allura comu è ligali o illegali l'Archiviu di Anna esattamenti? Chistu dipenni principalmenti dâ giurisdizzioni ligali. La maiuranza di li paisi cridinu in na forma di copyright, chi significa ca a pirsuni o cumpagnii veni assignatu un munòpuliu esclusivu supra certi tipi di òpiri pi un certu pirìudu di tempu. Comu na nota a margini, a l'Archiviu di Anna cridemu ca mentri ci sunnu certi benefici, in generali lu copyright è un net-negativu pi la sucità — ma chistu è na storia pi n'àutra vota."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Stu munòpuliu esclusivu supra certi òpiri significa ca è illegali pi chiunqui fora di stu munòpuliu di distribbuiri direttamenti chiddi òpiri — inclusu nuatri. Ma l'Archiviu di Anna è un muturi di ricerca ca nun distribbuisci direttamenti chiddi òpiri (almenu nun supra lu nostru situ web clearnet), allura avemu a essiri a postu, giustu? Nun esattamenti. In tanti giurisdizzioni è nun sulu illegali di distribbuiri òpiri cu copyright, ma puru di ligari a posti ca lu fannu. Un esempiu clàssicu di chistu è la liggi DMCA di li Stati Uniti."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Chistu è lu puntu cchiù strittu dû spettru. Supra l'àutru puntu dû spettru ci putìssiru essiri teoricamenti paisi senza liggi di copyright, ma chisti nun esìstinu veramenti. Praticamenti ogni paisi hà na forma di liggi di copyright supra i libri. L'applicazzioni è na storia diffirenti. Ci sunnu tanti paisi cu guverni ca nun si curanu di applicari la liggi di copyright. Ci sunnu puru paisi tra i dui estremi, ca pruibiscinu di distribbuiri òpiri cu copyright, ma nun pruibiscinu di ligari a tali òpiri."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "N'àutra cunsidirazzioni è a livellu di cumpagnia. Si na cumpagnia opera in na giurisdizzioni ca nun si curanu di copyright, ma la cumpagnia stissa nun è disposta a pigghiari alcunu risicu, allura putìssiru chiudiri u vostru situ web appena quarcunu si lamenta di iddu."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Infini, na granni cunsidirazzioni sunnu i pagamenti. Postu ca avemu a ristari anonimi, nun putemu usari metudi di pagamentu tradizziunali. Chistu ci lassa cu li criptovaluti, e sulu un picculu sottogruppu di cumpagnii li supportanu (ci sunnu carti di debitu virtuali pagati cu cripto, ma spissu nun sunnu accittati)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architettura di sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Allura suppunemu ca aviti truvatu certi cumpagnii ca sunnu disposti a ospitari u vostru situ web senza chiudirivi — chiamamuli \"fornitori amanti di la libbirtà\" 😄. Truveriti prestu ca ospitari tuttu cu iddi è abbastanza caru, allura putiti vuliri truvari certi \"fornitori economici\" e fari l'ospitaggiu effettivu ddà, proxying attraversu i fornitori amanti di la libbirtà. Si lu faciti giustu, i fornitori economici nun saprannu mai chi stai ospitannu, e nun riceverannu mai lagnanzi."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Cu tutti sti fornitori c'è un risicu ca vi chiudinu comunque, allura aviti bisognu puru di ridundanza. Avemu bisognu di chistu a tutti i livelli dâ nostra pila."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Na cumpagnia un pocu amante di la libbirtà ca si è messa in na pusizzioni interessanti è Cloudflare. Hanu <a %(blog_cloudflare)s>argumintatu</a> ca nun sunnu un fornituri di ospitaggiu, ma un'utility, comu un ISP. Nun sunnu quindi soggetti a DMCA o àutri richiesti di rimozzioni, e trasmettenu ogni richiesta al vostru fornituri di ospitaggiu attuali. Hanu puru fattu causa pi pruteggiri sta struttura. Putemu quindi usalli comu n'àutru stratu di caching e prutizzioni."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nun accetta pagamenti anonimi, allura putemu usari sulu u so pianu gratuitu. Chistu significa ca nun putemu usari i so funzioni di bilanciamentu di caricu o failover. Avemu quindi <a %(annas_archive_l255)s>implementatu chistu nuatri stissi</a> a livellu di duminiu. All'apertura di pàggina, u navigaturi controllerà si u duminiu attuali è ancora dispunìbbili, e si nun è, riscrivi tutti i URL a un duminiu diffirenti. Postu ca Cloudflare cache tanti pàggini, chistu significa ca un utilizatori pò arrivari supra u nostru duminiu principali, ancu si u serviziu proxy è giù, e poi al clic successivu essiri trasferitu a un àutru duminiu."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Avemu ancora preoccupazioni operativi normali di cui occuparci, comu monitorari a salute di i servidori, registrari errori di backend e frontend, e accussì via. A nostra architettura di failover permette una maggiore robustezza in stu sensu, per esempiu eseguendu un set di servidori completamente diversu in unu di i duminii. Pudemu ancu eseguiri versioni più vechji di u codice e di i datasets in stu duminiu separatu, in casu chì un bug criticu in a versione principale passi inosservatu."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Pudemu ancu pruteggeci contru Cloudflare girendu contru di noi, rimuovendulu da unu di i duminii, cum'è stu duminiu separatu. Sò pussibuli diverse permutazioni di sti idee."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Strumenti"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Fighjemu à quali strumenti usamu per realizà tuttu què. Stu aspettu hè in continua evoluzione mentre ci imbattimu in novi prublemi è troviamu novi suluzioni."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidore d'applicazione: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidore proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestione di i servidori: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Sviluppu: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hosting staticu Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ci sò alcune decisioni chì avemu rivisitatu più volte. Una hè a cumunicazione trà i servidori: aviamu usatu Wireguard per questu, ma avemu trovu chì occasionalmente smette di trasmette qualsiasi dati, o trasmette dati solu in una direzione. Questu hè accadutu cù diversi setup di Wireguard chì avemu pruvatu, cum'è <a %(github_costela_wesher)s>wesher</a> è <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Avemu ancu pruvatu à tunnellà porti sopra SSH, usendu autossh è sshuttle, ma ci simu imbattuti in <a %(github_sshuttle)s>prublemi quì</a> (ancu s'ellu ùn hè ancu chjaru per mè se autossh soffre di prublemi TCP-over-TCP o micca — mi pare solu una soluzione janky ma forse hè veramente bè?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Invece, simu tornati à cunnessioni dirette trà i servidori, nascondendu chì un servidore hè in esecuzione nantu à i fornitori economici usendu filtrazione IP cù UFW. Questu hà u svantaghju chì Docker ùn funziona micca bè cù UFW, a menu chì ùn usi <code>network_mode: \"host\"</code>. Tuttu què hè un pocu più prunente à errori, perchè esporrai u to servidore à internet cù solu una piccula misconfigurazione. Forse duvemu riturnà à autossh — i feedback seranu assai benvenuti quì."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Avemu ancu rivisitatu Varnish vs. Nginx. Attualmente ci piace Varnish, ma hà i so capricci è spigoli ruvidi. U stessu vale per Checkmk: ùn ci piace micca, ma funziona per avà. Weblate hè statu ok ma micca incredibile — a volte temu chì perderà i mo dati ogni volta chì pruvu à sincronizzallu cù u nostru repo git. Flask hè statu bonu in generale, ma hà qualchì capricciu stranu chì hà custatu assai tempu per debug, cum'è cunfigurà duminii persunalizati, o prublemi cù a so integrazione SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Finora l'altri strumenti sò stati grandi: ùn avemu micca lagnanze serie nantu à MariaDB, ElasticSearch, Gitlab, Zulip, Docker, è Tor. Tutti questi anu avutu qualchì prublema, ma nunda di troppu seriu o chì richiede tempu."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Cunclusioni"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Hè stata una sperienza interessante amparà cumu stabilisce un mutore di ricerca di biblioteca ombra robustu è resiliente. Ci sò assai più dettagli da sparte in posti futuri, allora fatemi sapè ciò chì vulete amparà di più!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Cum'è sempre, cerchemu donazioni per sustene stu travagliu, allora assicuratevi di verificà a pagina di Donazioni in l'Archiviu di Anna. Cerchemu ancu altri tipi di supportu, cum'è sovvenzioni, sponsor à longu andà, fornitori di pagamenti ad alto rischio, forse ancu annunci (di bon gustu!). È se vulete cuntribuisce u vostru tempu è e vostre cumpetenze, cerchemu sempre sviluppatori, traduttori, è cusì via. Grazie per u vostru interessu è supportu."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e lu gruppu (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Ciau, sugnu Anna. Aghju creatu <a %(wikipedia_annas_archive)s>l'Archiviu di Anna</a>, a più grande biblioteca ombra di u mondu. Questu hè u mo blog personale, in u quale io è i mo cumpagni scrivemu nantu à a pirateria, a preservazione digitale, è più."

#, fuzzy
msgid "blog.index.text2"
msgstr "Connettiti cun mè nantu à <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Nota chì stu situ web hè solu un blog. Ospitemu solu e nostre parolle quì. Nisun torrent o altri file protetti da copyright sò ospitati o ligati quì."

#, fuzzy
msgid "blog.index.heading"
msgstr "Articuli dû blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B Scraping di WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Mettiri 5,998,794 libri supra IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Attinzioni: stu postu dû blog è statu deprecatu. Avemu dicisu ca IPFS nun è ancora prontu pi lu primu tempu. Continueremu a ligari a file su IPFS da l'Archiviu di Anna quannu pussìbbili, ma nun lu ospiteremu cchiù nuatri stissi, e mancu ricumandamu a l'àutri di specchiari usannu IPFS. Pi favuri, taliati la nostra pàggina di Torrenti si vuliti aiutari a prisirvari a nostra cullizzioni."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archiviu di Anna ha scrapatu tuttu WorldCat (la cullizzioni di metadata di bibliuteca cchiù granni dû munnu) pi fari na lista di libri ca hannu bisognu d'essiri prisirvati.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Un annu fa, avemu <a %(blog)s>cumminciatu</a> a rispunneri a sta dumanna: <strong>Chi pircintuali di libri sunnu stati prisirvati in modu permanenti di bibliuteci ombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Quannu un libru arriva in na bibliuteca ombra di dati aperti comu <a %(wikipedia_library_genesis)s>Library Genesis</a>, e ora <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a>, veni mirratu in tuttu u munnu (attraversu i torrents), prisirvannulu pràticamente pi sempri."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Pi rispunneri a la dumanna di chi pircintuali di libri sunnu stati prisirvati, avemu bisognu di sapiri u denominaturi: quanti libri esistinu in tuttu? E idealmenti nun avemu sulu un numiru, ma veri metadata. Allura putemu micca sulu cunfruntari chiddi contru i bibliuteci ombra, ma puru <strong>criari na lista di libri rimanenti pi prisirvari!</strong> Putemu puru cuminciari a sunnari di un sforzu cullittivu pi scinniri sta lista."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Avemu scrapatu <a %(wikipedia_isbndb_com)s>ISBNdb</a>, e scaricatu u <a %(openlibrary)s>dataset di Open Library</a>, ma i risultati eranu insodisfacenti. U prubbrema principali era ca nun c'era tantu sovrappusizioni di ISBN. Vidi stu diagramma di Venn di <a %(blog)s>u nostru articulu di blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Eravamu assai surpresi di quantu pocu sovrappusizioni c'era trà ISBNdb e Open Library, tutti dui i quali includunu dati di vari fonti, comu scraping web e registri di bibliuteca. Si facissiru un bonu travagghiu a truvari a maiuranza di l'ISBN in circolazioni, i so circhi avissiru avutu sicuramenti na sustanziali sovrappusizioni, o unu saria statu un sottuinsiemi di l'autru. Ci fici dumannari, quanti libri cascanu <em>completamenti fora di sti circhi</em>? Avemu bisognu di un database cchiù granni."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Fu allura ca avemu puntatu i nostri occhi supra u database di libri cchiù granni dû munnu: <a %(wikipedia_worldcat)s>WorldCat</a>. Chistu è un database prupietariu di la non-profit <a %(wikipedia_oclc)s>OCLC</a>, ca aggrega i registri di metadata di bibliuteci di tuttu u munnu, in cambiu di dari a chiddi bibliuteci accessu a tuttu u dataset, e facennu li appariri nei risultati di ricerca di l'utenti finali."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Anchi si OCLC è una non-profit, u so mudellu di business richiedi di pruteggiri u so database. Ebbè, ci dispiaci diri, amici di OCLC, ca stamu dannu tuttu via. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Duranti l'annu passatu, avemu scrapatu meticulosamenti tutti i registri di WorldCat. A l'iniziu, avemu avutu un colpu di furtuna. WorldCat stava appena lanciannu u so ridisignu cumpletu di u situ web (in Aug 2022). Chistu includeva un sustanziali rinnovu di i so sistemi di backend, introducennu assai difetti di sicurizza. Avemu subitu coltu l'opportunità, e avemu pututu scrapari centinaia di milioni (!) di registri in pochi ghjorni."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Ridisegnu di WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dopu, i difetti di sicurizza eranu riparati lentamenti unu a unu, finu a quannu l'ultimu ca avemu trovu fu riparatu circa un mese fa. A chidd'epoca avemu avutu praticamente tutti i registri, e stavamu andannu solu pi registri di qualità leggermente cchiù alta. Allura avemu sintutu ca era tempu di rilassari!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Fighiamu qualchi infurmazioni di basa supra i dati:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formatu?</strong> <a %(blog)s>Contenituri di l'Archiviu di Anna (AAC)</a>, ca è essenzialmenti <a %(jsonlines)s>JSON Lines</a> cumpressu cu <a %(zstd)s>Zstandard</a>, più qualchi semantica standardizzata. Sti contenituri avvolginu vari tipi di registri, basati supra i diversi scraping ca avemu dispiegatu."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dati"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Si è verificatu nu errore scunnisciutu. Pi favuri cuntattaci a %(email)s cu na screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Sta munita havi na minima cchiù àuta di lu solitu. Pi favuri scegli na durata diversa o na munita diversa."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "La richiesta nun putìu èssiri cumpiuta. Pi favuri pruvati n'àutra vota tra qualchi minutu, e si cuntinua a succèdiri cuntattaci a %(email)s cu na screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Errore ntô prucessu di pagamentu. Pi favuri aspèttati nu mumentu e pruvati n'àutra vota. Si lu prublema persisti pi cchiù di 24 uri, cuntattaci a %(email)s cu na screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "cummentu ammucciatu"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Prubbrema di schedariu: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versioni migliurata"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Voi signalari st'utenti pi cumpurtamentu abusivu o inappropriatu?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Signalari abusu"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abusu signalatu:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Hai signalatu st'utenti pi abusu."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Risponni"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Per piaciri <a %(a_login)s>accedi</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Hai lassatu un cummentu. Pò pigghiari un minutu pi vidillu."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Qualcosa è andatu stortu. Ricarica la pàggina e pruva di novu."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s pàggini affettati"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nun visìbbili ntô Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nun visìbbili ntô Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nun visìbbili ntô Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcatu comu ruttu ntô Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Assenti di Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcatu comu “spam” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcatu comu “file scarsu” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nun tutti li pàggini putìru èssiri cunvertuti a PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Fallimentu ntô curriri exiftool supra stu file"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libru (scunnisciutu)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libru (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libru (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artìculu di giurnali"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentu di standard"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Rivista"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Fumettu"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musicali"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibbru"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Àutru"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Download dû server partner"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Download esternu"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Prestamu esternu"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Prestamu esternu (stampa disattivata)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Esplora metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Cuntinutu ntê torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Caricamenti su AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadati cechi"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca di Statu Russa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Tìtulu"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Auturi"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Edituri"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edizzioni"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Annata di pubblicazzioni"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nomu originali dû file"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Descrizzioni e cummenti di metadata"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "I scaricamenti dal server partner non sono temporaneamente disponibili per questo file."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Server Partner Veloce #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(cunsigghiatu)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(senza virificazzioni dû browser o liste d'attisa)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Server Partner Lentu #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(leggermenti cchiù veloci ma cu lista d'attisa)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(senza lista d'attisa, ma pò èssiri assai lentu)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(ancora clicca “GET” in cima)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(clicca “GET” in cima)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "i loro annunci sono noti per contenere software dannoso, quindi usa un blocco annunci o non cliccare sugli annunci"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(I file Nexus/STC ponnu essiri inaffidabili pi scaricari)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library su Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(richiesti lu Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Prendi in prestito dall'Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(solo per utenti con disabilità di stampa)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(il DOI associato potrebbe non essere disponibile in Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "collezione"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Scaricamenti torrent in massa"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(solo per esperti)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Cerca nell'Archivio di Anna per ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Cerca in vari altri database per ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Trova il record originale in ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Cerca nell'Archivio di Anna per ID di Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Tròva u record originali in Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Circà l'Archiviu di Anna pi nu nùmmiru OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Tròva u record originali in WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Circà l'Archiviu di Anna pi nu nùmmiru DuXiu SSID"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Circà manualmenti supra DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Circà l'Archiviu di Anna pi nu nùmmiru CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Tròva u record originali in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Circà l'Archiviu di Anna pi nu nùmmiru DuXiu DXID"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "L'Archiviu di Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(senza verificazioni di navigaturi)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadati cechi %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadati"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "descrizzioni"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nomu alternativu dû file"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Tìtulu alternativu"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Auturi alternativu"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Edituri alternativu"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edizzioni alternativa"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Estinzioni alternativa"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "cummenti di metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descrizzioni alternativa"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "data open source"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Chistu è nu record di nu file di l'Internet Archive, non è nu file scaricàbbili direttamenti. Pòi pruvà a prèstari u libru (link sutta), o usari chistu URL quannu <a %(a_request)s>richièdi nu file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Si hai chistu file e non è ancora dispunìbbili nall'Archiviu di Anna, cunsìdira di <a %(a_request)s>càrricalu</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nùmmiru %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s recordu di metadata"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s record di metadata"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Chistu è un recordu di metadata, non è un file scaricabile. Poti usari chistu URL quannu <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata di lu record collegatu"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Migliora i metadata su Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Attinzione: multipli record collegati:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Migghiura metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Riporta la qualità di lu file"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Tempu di scaricamentu"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Sito web:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Cerca in l'Archiviu di Anna pi “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Esploratore di Codici:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vidi in l'Esploratore di Codici “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Leggi cchiù…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Download (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Prèstamu (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Esplora metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Cummenti (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Liste (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statìstiche (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Dettagli tecnici"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Stu schedariu putissi aviri prubblemi, e è statu ammucciatu di na libreria di surgenti.</span> A voti è a richesta di nu detenturi di diritti d'auturi, a voti è picchì c'è na megghiu alternativa dispunìbbili, ma a voti è picchì c'è nu prubblema cu lu schedariu stissu. Putissi ancora èssiri bonu pi scaricari, ma ricummannamu di circari prima na alternativa. Cchiù dittagli:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Na megghiu versione di stu schedariu putissi èssiri dispunìbbili a %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Si ancora voi scaricari stu schedariu, assicurati di usari sulu software fidatu e aggiornatu pi aprirlu."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Download veloci"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Scaricamenti veloci</strong> Diventa nu <a %(a_membership)s>mèmmiru</a> pi sustèniri la preservazioni a longu termini di libri, articuli, e cchiù. Pi mustrari la nostra gratitudini pi lu tò sustegnu, otteni scaricamenti veloci. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si donati stu misi, ottiniti <strong>doppia</strong> la quantità di scaricamenti veloci."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Scaricamenti veloci</strong> Ti restanu %(remaining)s oggi. Grazzi pi èssiri nu mèmmiru! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Scaricamenti veloci</strong> Hai finutu li scaricamenti veloci pi oggi."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Scaricamenti veloci</strong> Hai scaricatu stu schedariu di ricenti. Li link restanu vàlidi pi un pocu."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opzioni #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nuddu redirezzioni)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(apri in visualizzaturi)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Raccomanda a nu amicu, e tutti dui ottèniti %(percentage)s%% scaricamenti veloci bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Sapiri cchiù…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Scaricamenti lenti"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Di partinari fidati."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Cchiù nfurmazzioni ntô <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(potissi richiediri <a %(a_browser)s>verifica dû browser</a> — scaricamenti illimitati!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Doppu aviri scaricatu:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Apri ntô nostru visualizzaturi"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostra download esterni"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Scaricamenti esterni"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nisciu scaricamentu truvatu."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Tutti l'opzioni di scaricamentu hannu lu stissu file, e sunnu sicuri di usari. Tuttavia, siate sempri cauti quannu scaricati file da internet, specialmenti da siti esterni a l'Archiviu di Anna. Per esempiu, assicurativi di teniri i vostri dispositivi aggiornati."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Pi li schedi granni, ricumandamu di usari un gesturi di scaricamentu pi evitari interruzzioni."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Gesturi di scaricamentu ricumandati: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Avrai bisognu di un letturi di ebook o PDF pi apriri lu schedariu, a secunna dû furmatu dû schedariu."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Letturi di ebook ricumandati: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visualizzaturi online di l'Archiviu di Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Usa strumenti online pi cunvertiri trà formati."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Strumenti di cunversioni ricumandati: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Puoi mannari sia schedari PDF ca EPUB a lu to Kindle o Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Strumenti ricumandati: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon ‘Manna a Kindle’"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz ‘Manna a Kobo/Kindle’"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Susteni auturi e bibliotechi"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si ti piaci chistu e ti lu poi pèrmitiri, cunsidira di accattari l'uriginali, o di sustèniri direttamenti l'auturi."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si chistu è dispunìbbili nta la tò libreria lucali, cunsìdira di pigghiallu in prestitu gratisi ddà."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Qualità di lu file"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Aiuta la comunità riportannu la qualità di stu file! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Riporta un prubbrema di lu file (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Ottima qualità di lu file (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Aggiungi un cummentu (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Chi c'è di sbagghiatu in stu file?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Per piaciri usa lu <a %(a_copyright)s>modulu di reclamu DMCA / Copyright</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Discrivi lu prubbrema (richiesto)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descrizzioni di lu prubbrema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 di na versione megghiu di stu file (si applicabile)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Cumpleta stu campu si c'è un altru file chi currispondi strettamenti a stu file (stessa edizioni, stessa estensioni di file si la poi truvari), chi la genti avissi a usari inveci di stu file. Si canusci na versione megghiu di stu file fora di l'Archiviu di Anna, allura per piaciri <a %(a_upload)s>caricalu</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Poi ottèniri l'MD5 da l'URL, p'esempiu"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Invia lu rapportu"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Ampara comu <a %(a_metadata)s>migliurari lu metadata</a> di stu schedariu tu stissu."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Grazzi pi aviri mannatu lu tò rapportu. Si virrà mustratu nta sta pàggina, e sarà rivisu manualmenti di Anna (finu a quannu avemu un sistema di muderazzioni adattu)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Qualcosa è andatu stortu. Ricarica la pàggina e pruva di novu."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si stu schedariu havi na granni qualità, poi discutiri di tuttu ccà! Si no, usa lu buttuni “Segnala prubbrema cu lu schedariu”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Mi piaciù stu libbru!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lassa un cummentu"

#, fuzzy
msgid "common.english_only"
msgstr "Lu testu sottu cuntinua in inglesi."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totali di scaricamenti: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “file MD5” è un hash ca veni calcolatu di li cuntenuti di lu schedariu, e è ragiunevolmenti unicu basatu supra chiddu cuntenutu. Tutti li librarii ombra ca avemu indicizzatu ccà usanu principalmenti li MD5 pi identificari li schedari."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un schedariu pò cumpariri in diversi librarii ombra. Pi nfurmazzioni supra li vari datasets ca avemu cumpilatu, vidi la <a %(a_datasets)s>pàggina di Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Stu è un schedariu gestitu di la <a %(a_ia)s>IA’s Controlled Digital Lending</a> libraria, e indicizzatu di l’Archiviu di Anna pi la ricerca. Pi nfurmazzioni supra li vari datasets ca avemu cumpilatu, vidi la <a %(a_datasets)s>pàggina di Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pi nfurmazzioni supra stu schedariu particulari, talìa lu sò <a %(a_href)s>schedariu JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema a carricari sta pàggina"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Rinfresca pà provà di novu. <a %(a_contact)s>Cuntattaci</a> si lu problema persisti pi parechii uri."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Non truvatu"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” non fu truvatu ntâ nostra base di dati."

#, fuzzy
msgid "page.login.title"
msgstr "Accedi / Registrati"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verifica di lu browser"

#, fuzzy
msgid "page.login.text1"
msgstr "Pi evitari ca i spam-bot crìanu tanti account, avemu bisognu di verificari prima lu vostru browser."

#, fuzzy
msgid "page.login.text2"
msgstr "Si vi truvati intrappulati in un loop infinitu, vi cunsigliamu di installari <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Pò aiutari puru disattivari i blocca-pubblicità e altri estensioni di lu browser."

#, fuzzy
msgid "page.codes.title"
msgstr "Codiçi"

#, fuzzy
msgid "page.codes.heading"
msgstr "Esploraturi di Codiçi"

#, fuzzy
msgid "page.codes.intro"
msgstr "Esplora i codiçi cu cui i registri sunnu taggati, pi prefissu. La culonna “registri” mustra u nùmmiru di registri taggati cu codiçi cu lu prefissu datu, comu vistu ntô muturi di ricerca (inclusi i registri sulu metadata). La culonna “codiçi” mustra quanti codiçi reali hannu un prefissu datu."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Sta pàggina pò pigghiari un pocu di tempu pi generari, pi chistu richiedi un captcha di Cloudflare. <a %(a_donate)s>Li membri</a> ponnu evitari u captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Pi favuri, nun scrapi sti pàggini. Inveci, ricummannamu <a %(a_import)s>generari</a> o <a %(a_download)s>scarricari</a> i nostri basi di dati ElasticSearch e MariaDB, e curriri u nostru <a %(a_software)s>codici open source</a>. I dati grezzi ponnu èssiri esplorati manualmenti attraversu file JSON comu <a %(a_json_file)s>chistu</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefissu"

#, fuzzy
msgid "common.form.go"
msgstr "Vai"

#, fuzzy
msgid "common.form.reset"
msgstr "Resetta"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Circà l'Archiviu di Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Attinzione: u codici cunteni caràttiri Unicode incorretti, e pò cumportàrisi in modu incorrettu in vari situazzioni. U binariu grezzu pò èssiri decodificatu dâ rapprisintazzioni base64 ntâ URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefissu di codici canusciutu “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefissu"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etichetta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descrizzioni"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL pi un codici specificu"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sarà sustituitu cu lu valori dû codici"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL genericu"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Situ"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s record ca currispondi “%(prefix_label)s”"
msgstr[1] "%(count)s record ca currispùndinu “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL pi còdici specificu: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Cchiù…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Còdici ca cumìnciunu cu “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Ìnniciu di"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "record"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "còdici"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Mancu di %(count)s record"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pi DMCA / rivendicazioni di copyright, usati <a %(a_copyright)s>stu form</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Qualsiasi autru modu di cuntattari ni pi rivendicazioni di copyright sarà automaticamente eliminatu."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Accogliemu cun piaciri i vostri feedback e dumanni!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tuttavia, a causa di la quantità di spam e email senza sensu ca ricivemu, vi preghiamo di spuntari i caselle pi cunfirmari ca capiti sti cundizioni pi cuntattari ni."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "I reclami di copyright a st'email sarannu ignorati; usati u form invece."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "I servizî partinari sunnu indisponìbbili a causa di chiusuri di hosting. Duvìssiru èssiri di novu attivi prestu."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "I membri sarannu allungati di cunseguenza."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nun n'emailati pi <a %(a_request)s>richieste di libri</a><br>o piccoli (<10k) <a %(a_upload)s>caricamenti</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Quannu dumandati quistioni di contu o donazioni, aggiungeti u vostru ID di contu, screenshot, ricevute, quantu cchiù infurmazioni pussibili. Verificamu l'email ogni 1-2 simani, allura nun includiri st'infurmazioni ritarderà ogni risuluzione."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostra email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulariu di rivendicazzioni di copyright / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si aviti na rivendicazzioni di copyright o àutri rivendicazzioni di DMCA, vi pregu di cumpilàri stu formulariu quantu cchiù pricisamenti pussìbbili. Si aviti quarchi prubblema, cuntattàtini a l'indirizzu dedicatu a DMCA: %(email)s. Notati ca li rivendicazzioni mannati a stu indirizzu nun vennu prucissati, è sulu pi quistioni. Pi favuri, usati lu formulariu sutta pi mannari li vostri rivendicazzioni."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL su l'Archiviu di Anna (richiesti). Unu pi linia. Includiti sulu URL ca discrìvinu la stissa edizzioni di un libru. Si vuliti fari na rivendicazzioni pi cchiù libri o cchiù edizzioni, vi pregu di mannari stu formulariu cchiù voti."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Li rivendicazzioni ca accùppanu cchiù libri o edizzioni vennu rifiutati."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Lu vostru nomu (richiestu)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Indirizzu (richiestu)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Nùmmiru di telefunu (richiestu)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (richiesta)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descrizzioni chiara di lu matiriali d'urìggini (richiesta)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN di lu matiriali d'urìggini (si applicàbbili). Unu pi linia. Includiti sulu chiddi ca currispùnninu esattamenti a l'edizzioni pi cui stai facennu na rivendicazzioni di copyright."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL di lu matiriali d'urìggini, unu pi linia. Vi pregu di pigghiari un mumentu pi circari lu vostru matiriali d'urìggini in Open Library. Chistu ci aiuta a virificari la vostra rivendicazzioni."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL di lu matiriali d'urìggini, unu pi linia (richiesti). Includiti quantu cchiù pussìbbili, pi aiutarini a virificari la vostra rivendicazzioni (p'esempiu Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Dichiarazzioni e firma (richiesti)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Manna rivendicazzioni"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Grazii pi aviri mannatu la vostra rivendicazzioni di copyright. La virificaremu quantu cchiù prestu pussìbbili. Vi pregu di ricaricari la pàggina pi mannari n'àutra."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Quarchi cosa nun ha funziunatu. Vi pregu di ricaricari la pàggina e pruvàri n'àutra vota."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si siti interessatu a fari un mirror di stu dataset pi <a %(a_archival)s>archiviazzioni</a> o pi <a %(a_llm)s>LLM training</a>, cuntattaci."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "La nostra missioni è di archiviari tutti li libbra di lu munnu (comu puru articuli, rivisti, ecc), e farili largamenti accessibili. Cridemu ca tutti li libbra avissiru a essiri rispecchiati largamenti, pi assicurari ridundanza e resilienza. Pi chistu stamu mettennu nzemi schedari di na varietà di fonti. Certi fonti sunnu cumpletamenti aperti e ponnu essiri rispecchiati in massa (comu Sci-Hub). Àutri sunnu chiusi e protettivi, allura pruvamu a raschiarili pi “libbirari” li sò libbra. Àutri ancora sunnu ntra lu menzu."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Tutti i nostri dati ponnu essiri <a %(a_torrents)s>torrenti</a>, e tutti i nostri metadata ponnu essiri <a %(a_anna_software)s>generati</a> o <a %(a_elasticsearch)s>scaricati</a> comu basi di dati ElasticSearch e MariaDB. Li dati grezzi ponnu essiri esplorati manualmenti attraversu schedari JSON comu <a %(a_dbrecord)s>chistu</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Panoramica"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Sutta c’è na rapida panoramica di li fonti di li schedari supra l’Archiviu di Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Surgenti"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Diminsioni"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% mirruratu di AA / torrents dispunìbbili"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Pircintuali di nùmmiru di file"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ultima aggiornamentu"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction e Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s schedariu"
msgstr[1] "%(count)s schedari"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: frizzatu di lu 2021; la màggiu parti dispunìbbili attraversu torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: picculi aggiunti di tannu</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Escludennu “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Li torrents di Fiction sunnu arreri (ancu siddu li ID ~4-6M nun sunnu torrentiati pirchì si sovrapponunu cu li nostri torrents di Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "La cullizzioni “Chinese” in Z-Library pari essiri la stissa di la nostra cullizzioni DuXiu, ma cu MD5 diffirenti. Escludemu sti file di li torrents pi evitari duplicazioni, ma li mustramu ancora ntô nostru indici di circata."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controllu Digitali di Pristamu"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ di file sunnu ricircabili."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totali"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Escludennu i duplicati"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Datu ca li bibliutichi ombra spissu sincronizzanu dati tra iddi, c'è un granni sovrappusizioni tra li bibliutichi. È pi chistu ca i nummira nun aggiunginu a lu totali."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "La percentuali di “mirruratu e seminatu di l'Archiviu di Anna” mustra quanti file mirruramu nuatri. Seminamu chiddi file in granni quantità attraversu torrent, e li facemu dispunibili pi scaricari direttamenti attraversu siti web partner."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliutichi di surghenti"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Certi bibliotechi di surgenti prumovinu la spartera di massa di li sò dati attraversu torrents, mentri àutri nun sparteru facirmenti la sò cullezzioni. In chistu casu, l'Archiviu di Anna prova a scrape li sò cullezzioni, e a farli dispunìbbili (vidi la nostra pàggina di <a %(a_torrents)s>Torrents</a>). Ci sunnu puru situazzioni intermedi, pi esempiu, unni li bibliotechi di surgenti sunnu disposti a sparteri, ma nun hannu li risorsi pi farlu. In chisti casi, provamu puru a dari aiutu."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Sutta c'è una panoramica di comu interfaccemu cu li diversi bibliotechi di surgenti."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Surgenti"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "File"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dump di database HTTP giornalieri <a %(dbdumps)s>qui</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrenti automatizzati per <a %(nonfiction)s>Non-Fiction</a> e <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(covers)s>torrenti di copertini di libri</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub ha firmatu novi file di lu 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dump di metadata dispunibili <a %(scihub1)s>qui</a> e <a %(scihub2)s>qui</a>, e puru comu parti di lu <a %(libgenli)s>database di Libgen.li</a> (chi usamu)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrenti di dati dispunibili <a %(scihub1)s>qui</a>, <a %(scihub2)s>qui</a>, e <a %(libgenli)s>qui</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Certi novi file sunnu <a %(libgenrs)s>essennu</a> <a %(libgenli)s>aggiunti</a> a “scimag” di Libgen, ma nun abbastanu pi giustificari novi torrenti"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dump di database HTTP trimestrali <a %(dbdumps)s>qui</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s I torrenti di Non-Fiction sunnu spartuti cu Libgen.rs (e mirrati <a %(libgenli)s>qui</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s L'Archiviu di Anna e Libgen.li gestiscinu cullezzioni di <a %(comics)s>fumetti</a>, <a %(magazines)s>rivisti</a>, <a %(standarts)s>documenti standard</a>, e <a %(fiction)s>fiction (divergenti da Libgen.rs)</a> in modu cullaborativu."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s La so cullezzioni “fiction_rus” (fiction russa) nun havi torrent dedicati, ma è cuperta di torrent d'àutri, e tenemu un <a %(fiction_rus)s>specchiu</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s L'Archiviu di Anna e Z-Library gestiscinu cullizzioni di <a %(metadata)s>metadata di Z-Library</a> e <a %(files)s>file di Z-Library</a> in cullaburazioni."

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Certi metadata sunnu dispunibili attraversu <a %(openlib)s>dump di database di Open Library</a>, ma nun coprinu tutta la cullizzioni di IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nenti dump di metadata facilmente accessibili pi tutta la so cullizzioni"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(ia)s>metadata di IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s File dispunibili sulu pi prestitu in modu limitatu, cu vari restrizioni di accessu"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(ia)s>file di IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Vari database di metadata sparsi ntô internet cinisi; spissu sunnu database a pagamentu"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nuddu di metadata facilmente accessìbbili pi tutta la so cullizzioni."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(duxiu)s>metadata DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Varii database di file sparpagghiati ntô internet cinisi; spissu sunnu database a pagamentu."

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La maggiuranza di file sunnu accessìbbili sulu cu account premium di BaiduYun; velocità di scaricamentu lenti."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(duxiu)s>file DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Varii fonti cchiù nichi o occasionali. Incurraggiamu li pirsuni a carricari prima n'àutri bibliotechi ombra, ma a voti li pirsuni hannu cullizzioni troppu granni pi l'àutri pi putiri essiri ordinati, ma nun abbastanti granni pi giustificari na categoria propria."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Surgenti di sulu metadati"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Arricchisciamu puru la nostra cullezzioni cu surgenti di sulu metadati, chi putemu abbinaru a file, pi esempiu usannu numeri ISBN o àutri campi. Sutta c'è una panoramica di chisti. Di novu, certi di sti surgenti sunnu cumpletamenti aperti, mentri pi àutri avemu a scrape."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "La nostra ispirazioni pi raccògghiri metadata è lu scopu di Aaron Swartz di “na pàggina web pi ogni libbru mai pubblicatu”, pi cui criò <a %(a_openlib)s>Open Library</a>. Chistu pruggettu ha fattu beni, ma la nostra pusizioni unica nni pirmetti di ottèniri metadata chi iddi nun ponnu. N'àutra ispirazioni fu la nostra disidiriu di sapiri <a %(a_blog)s>quanti libbra ci sunnu ntô munnu</a>, accussì putemu calcolari quanti libbra nni ristanu ancora pi sarvari."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nota chi ntâ ricerca di metadati, mustramu li ricordi originali. Nun facemu nenti fusione di ricordi."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ultima aggiornamentu"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Dump di database mensili <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nun dispunìbbili direttamenti in bulk, prutetti contru scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Database unificatu"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Cumbinamu tutti li surgenti supra ntô stissu database unificatu chi usamu pi sirviri stu situ web. Stu database unificatu nun è dispunìbbili direttamenti, ma siccomu l'Archiviu di Anna è cumpletamenti open source, pò essiri facirmenti <a %(a_generated)s>generatu</a> o <a %(a_downloaded)s>scaricatu</a> comu database ElasticSearch e MariaDB. Li script in chidda pàggina scaricherannu autumaticamenti tutti li metadati nicissarii di li surgenti menzionati supra."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si voi esplorari li nostri dati prima di curriri chiddi script lucalmenti, poi taliari li nostri file JSON, chi linkanu a àutri file JSON. <a %(a_json)s>Stu file</a> è un bonu puntu di partenza."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adattatu di lu nostru <a %(a_href)s>postu di blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> è na granni base di dati di libri scanati, criata di lu <a %(superstar_link)s>SuperStar Digital Library Group</a>. La maiuranza sunnu libri accademici, scanati pi essiri dispunibili digitalmenti a l'università e biblioteche. Pi lu nostru publicu anglofonu, <a %(princeton_link)s>Princeton</a> e la <a %(uw_link)s>University of Washington</a> hannu boni panoramiche. C'è puru un articulu eccellente chi duna cchiù sfunnu: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Li libri di Duxiu sunnu stati piratati pi assai tempu supra l'internet cinisi. Di solitu sunnu vinnuti pi menu di un dollaru di rivendituri. Vènunu tipicamenti distribuiti usannu l'equivalenti cinisi di Google Drive, chi spissu è statu hackatu pi aviri cchiù spaziu di archiviazzioni. Certi dittagli tecnichi ponnu essiri truvati <a %(link1)s>ccà</a> e <a %(link2)s>ccà</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Anchi si li libri sunnu stati distribuiti semi-pubblicamenti, è assai difficili ottèniri li in granni quantità. Avìamu chistu altu supra la nostra lista di cose da fari, e avìamu destinatu parechji misi di travagghiu a tempu pienu pi chistu. Tuttavia, a fini di lu 2023 un vuluntariu incredibili, stupefacenti, e talentuusu ci cuntattò, dicennu chi avìa già fattu tuttu stu travagghiu — a granni spisa. Ci spartìu la cullizzioni completa, senza aspittari nenti in cambiamentu, eccettu la garanzia di preservazioni a longu termini. Veramenti rimarchevuli."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Risorsi"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totali file: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totali dimensioni file: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "File mirrurati da l'Archiviu di Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ultimu aggiornamentu: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrent di l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Esempiu di registru ntô l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Lu nostru postu di blog supra stu dati"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Script pi importari metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formatu di l'Archiviu di Anna Containers"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Cchiù nfurmazzioni di li nostri vuluntari (noti grezzi):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Stu dataset è strittamenti rilazzionatu cu lu <a %(a_datasets_openlib)s>dataset di Open Library</a>. Cunteni na scanzioni di tutti li metadata e na granni parti di file di la Biblioteca di Prestitu Digitale Cuntrollata di IA. L'aggiornamenti vennu rilassati ntô <a %(a_aac)s>formatu di l'Archiviu di Anna Containers</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Sti ricordi vennu rifiruti direttamenti di lu dataset di Open Library, ma cunteni puru ricordi ca nun sunnu ntô Open Library. Avemu puru un nùmmiru di file di dati scanzionati di membri di la cumunità ntô corsu di l'anni."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La cullizzioni è cumposta di dui parti. Aviti bisognu di tutti dui parti pi aviri tutti li dati (eccettu li torrent superati, ca sunnu barrati ntâ pàggina di li torrent)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "a nostra prima pubblicazioni, prima ca standardizzamu lu formatu <a %(a_aac)s>Anna’s Archive Containers (AAC)</a>. Cunteni metadata (comu json e xml), pdf (da sistemi di prestitu digitali acsm e lcpdf), e miniature di copertina."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "rilasci novi incrementali, usannu AAC. Cunteni sulu metadata cu timestamp dopu lu 2023-01-01, picchì lu restu è già cupertu di “ia”. Anchi tutti li file pdf, sta vota da li sistemi di prestitu acsm e “bookreader” (lu letturi web di IA). Anchi si lu nomu nun è esattamenti giustu, continuiamu a pupulàri li file bookreader nta la cullezzioni ia2_acsmpdf_files, picchì sunnu mutuamenti esclusivi."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sito principali %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca di Prestitu Digitali"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentazioni di metadata (la maiò parti di li campi)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informazioni sui paesi ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "L'Agenzia Internazionale ISBN rilascia regolarmente le gamme che ha assegnato alle agenzie nazionali ISBN. Da questo possiamo derivare a quale paese, regione o gruppo linguistico appartiene questo ISBN. Attualmente utilizziamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Risorse"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultimo aggiornamento: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sito web ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Per la storia dei diversi fork di Library Genesis, vedi la pagina per <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Il Libgen.li contiene la maggior parte dello stesso contenuto e metadati del Libgen.rs, ma ha alcune collezioni aggiuntive, come fumetti, riviste e documenti standard. Ha anche integrato <a %(a_scihub)s>Sci-Hub</a> nei suoi metadati e motore di ricerca, che è ciò che usiamo per il nostro database."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "I metadati per questa biblioteca sono liberamente disponibili <a %(a_libgen_li)s>su libgen.li</a>. Tuttavia, questo server è lento e non supporta la ripresa delle connessioni interrotte. Gli stessi file sono anche disponibili su <a %(a_ftp)s>un server FTP</a>, che funziona meglio."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "I torrent sunnu dispunibili pi' la maiò parti di cuntenutu aghiuntivu, in particulari i torrent pi' fumetti, rivisti, e documenti standard sunnu stati rilassati in cullaborazioni cu l'Archiviu di Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La cullezzioni di fiction havi i so torrent (divergenti da <a %(a_href)s>Libgen.rs</a>) a partiri di %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Secunnu l'amministraturi di Libgen.li, la cullezzioni “fiction_rus” (fiction russa) avissi a essiri cuperta di torrent rilassati regularmente da <a %(a_booktracker)s>booktracker.org</a>, in particulari i torrent <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (chi specchiamu <a %(a_torrents)s>ccà</a>, ancu si nun avemu ancora stabilitu quali torrent currisponnunu a quali file)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistichi pi' tutti i cullezzioni ponnu essiri truvati <a %(a_href)s>nni lu situ di libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "La non-fizzioni pari ca s'havi divirgiutu, ma senza novi torrenti. Pari ca chistu è successu di l'iniziu dû 2022, ma nun avemu verificatu chistu."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certi intervalli senza torrent (comu l'intervalli di fiction f_3463000 a f_4260000) sunnu probabbilmenti file di Z-Library (o àutri duplicati), ancu si putemu vuliri fari qualchi deduplicazioni e fari torrent pi' file unichi di lgli in sti intervalli."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Notati ca li file torrent ca rifiriscinu a “libgen.is” sunnu esplicitamenti mirrura di <a %(a_libgen)s>Libgen.rs</a> (“.is” è nu duminiu diversu usatu di Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Na risorsa utìli pi usari li metadata è <a %(a_href)s>sta pàggina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents di narrativa ntô Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents di fumetti ntô Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents di rivisti ntô Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrent di documenti standard nni l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrent di fiction russa nni l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Nfurmazzioni supra li campi di metadata"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirrura di àutri torrents (e torrents unici di narrativa e fumetti)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum di discussioni"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "U nostru post ntô blog supra la rilassata di li fumetti"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "La storia rapida di li diversi fork di Library Genesis (o “Libgen”), è ca cu lu tempu, li diversi pirsuni nvoluti cu Library Genesis si liticàru e si nni jeru pi li so strati."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La virsioni “.fun” fu criata dû funnaturi urigginali. Sta essennu rinnuvata a favuri di na nova virsioni cchiù distribbuita."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La virsioni “.rs” havi dati assai simili, e rilassa cchiù custantimenti la so cullizzioni in torrents di massa. È approssimativamenti divisa in na seczioni di “narrativa” e na di “non-narrativa”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Urìgginarmenti a “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>virsioni “.li”</a> havi na cullizzioni massiccia di fumetti, comu puru àutru cuntinutu, ca nun è (ancora) dispunìbbili pi lu scaricamentu di massa attraversu torrents. Havi na cullizzioni di torrents separata di libri di narrativa, e cunteni li metadata di <a %(a_scihub)s>Sci-Hub</a> ntâ so basi di dati."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Secunnu stu <a %(a_mhut)s>postu ntô forum</a>, Libgen.li era urìgginarmenti ospitatu a “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> in certu sensu è puru nu fork di Library Genesis, anchi si usaru nu nomu diversu pi lu so pruggettu."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Sta pàggina è supra la virsioni “.rs”. È canusciuta pi pubblicari custantimenti sia li so metadata ca lu cuntinutu cumpletu dû so catàlogu di libri. La so cullizzioni di libri è divisa tra na parti di narrativa e na di non-narrativa."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Na risorsa utìli pi usari li metadata è <a %(a_metadata)s>sta pàggina</a> (blocca li range di IP, putissi essiri nicissariu usari nu VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A parti di marzu 2024, novi torrents sunnu pubblicati in <a %(a_href)s>stu thread di forum</a> (blocca intervalli IP, VPN putissi èssiri nicissariu)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents di Non-Fiction su l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents di Fiction su l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Infurmazzioni di campu metadata di Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents di Non-Fiction di Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents di Fiction di Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum di discussioni di Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents di l'Archiviu di Anna (copertini di libri)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "U nostru bloggu nantu a rilascita di copertini di libri"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis è cunnisciutu pi già generosamente fà dispunibili i so dati in massa attraversu torrents. A nostra cullezzioni di Libgen cunsisti di dati ausiliari chi iddi nun rilascianu direttamenti, in cullaburazioni cu iddi. Tantu ringraziamentu a tutti chiddi chi travagghianu cu Library Genesis pi travagghiari cu nuatri!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Rilascita 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Sta <a %(blog_post)s>prima rilascita</a> è abbastanza piccula: circa 300GB di copertini di libri da u fork di Libgen.rs, sia fiction chi non-fiction. Sunnu organizzati comu appaionu in libgen.rs, p.es.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pi un libru di non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pi un libru di fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Comu cu a cullezzioni di Z-Library, li mittimu tutti in un granni file .tar, chi pò èssiri muntatu usannu <a %(a_ratarmount)s>ratarmount</a> si vulete sirviri i file direttamenti."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> è na basa di dati prupietaria di l'organizzazzioni non-profit <a %(a_oclc)s>OCLC</a>, chi aggrega li record di metadata di bibliotechi di tuttu lu munnu. Probabilmenti è la cullezzioni di metadata di bibliotechi cchiù granni di lu munnu."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Uttùviru 2023, rilascio iniziali:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Ntô uttùviru 2023 avemu <a %(a_scrape)s>rilasciatu</a> na scrape cumpleta di la basa di dati OCLC (WorldCat), ntô <a %(a_aac)s>formatu di l'Archiviu di Anna Containers</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrent di l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "U nostru postu di blogu supra sti dati"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library è un pruggettu open source da l'Internet Archive pi catalogari ogni libru ntô munnu. Hà una di li più granni operazioni di scansioni di libri ntô munnu, e hà tanti libri dispunibili pi prestitu digitale. U so catalogu di metadata di libri è liberamenti dispunibili pi scaricari, e è inclusu ntô l'Archiviu di Anna (ancora chi attualmente nun è in ricerca, eccettu si cercate esplicitamenti un ID di Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rilascio 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Questo è un dump di molte chiamate a isbndb.com durante settembre 2022. Abbiamo cercato di coprire tutte le gamme ISBN. Si tratta di circa 30,9 milioni di record. Sul loro sito web affermano di avere effettivamente 32,6 milioni di record, quindi potremmo averne persi alcuni, o <em>loro</em> potrebbero aver commesso qualche errore."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Le risposte JSON sono praticamente grezze dal loro server. Un problema di qualità dei dati che abbiamo notato è che per i numeri ISBN-13 che iniziano con un prefisso diverso da \"978-\", includono comunque un campo \"isbn\" che è semplicemente il numero ISBN-13 con i primi 3 numeri tagliati (e la cifra di controllo ricalcolata). Questo è ovviamente sbagliato, ma sembra che lo facciano così, quindi non lo abbiamo modificato."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Un altro potenziale problema che potresti incontrare è il fatto che il campo \"isbn13\" ha duplicati, quindi non puoi usarlo come chiave primaria in un database. I campi combinati \"isbn13\" + \"isbn\" sembrano essere unici."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pi favori, pi dduppiari Sci-Hub, taliati lu sò <a %(a_scihub)s>situ ufficiali</a>, la <a %(a_wikipedia)s>pàggina Wikipedia</a>, e st'intervista <a %(a_radiolab)s>podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Notati ca Sci-Hub è statu <a %(a_reddit)s>frizzatu di lu 2021</a>. Fu frizzatu prima, ma ntô 2021 foru agghiunciuti na pocu di miliuna di articuli. Ancora, certi articuli limitati vennu agghiunciuti a li cullezzioni “scimag” di Libgen, ma nun abbastanu pi giustificari novi torrent bulk."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usamu li metadata di Sci-Hub comu furnuti di <a %(a_libgen_li)s>Libgen.li</a> ntâ sò cullezzioni “scimag”. Usamu puru lu dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Notati ca li torrent “smarch” sunnu <a %(a_smarch)s>deprecati</a> e pi chistu nun sunnu nclusi ntâ nostra lista di torrent."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent su l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata e torrent"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent su Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent su Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Aggiornamenti su Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pàggina Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Intervista podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Carricamenti all'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Panoramica da <a %(a1)s>pàggina datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Varii fonti cchiù nichi o occasionali. Incurraggiamu li pirsuni a carricari prima n'àutri bibliotechi ombra, ma a voti li pirsuni hannu cullizzioni troppu granni pi l'àutri pi putiri essiri ordinati, ma nun abbastanti granni pi giustificari na categoria propria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La cullizzioni di “upload” è spartuta in suttocullizzioni cchiù nichi, chi sunnu indicati ntê AACIDs e ntê nomi di torrent. Tutti li suttocullizzioni foru prima deduplicati contru la cullizzioni principali, ma li file JSON di metadata “upload_records” cuntenunu ancora assai rifirenzi a li file originali. Li file non-libbru foru puru rimossi di la maggiuranza di suttocullizzioni, e tipicamenti <em>nun</em> sunnu notati ntê file JSON di “upload_records”."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Assai suttocullizzioni stissi sunnu cumposti di suttosuttocullizzioni (p'esempiu di diffirenti fonti originali), chi sunnu rapprisintati comu directory ntê campi “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Li suttocullizzioni sunnu:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Sottuculizzioni"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Nutizzi"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "naviga"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cerca"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Da <a %(a_href)s>aaaaarg.fail</a>. Sembra essiri abbastanti cumpletu. Da lu nostru vuluntariu “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Da un <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Ha un'alta sovrappusizioni cu li cullezzioni di articuli esistenti, ma assai pochi MD5 coincidenu, accussì avemu dicisu di mantèniri tuttu."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scraping di <q>iRead eBooks</q> (= foneticamenti <q>ai rit i-books</q>; airitibooks.com), di vuluntariu <q>j</q>. Corrispondi a <q>airitibooks</q> metadata in <a %(a1)s><q>Àutri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Di na culizzioni <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parti di la surgi originali, parti di the-eye.eu, parti di àutri specchi."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Da un situ privatu di torrent di libri, <a %(a_href)s>Bibliotik</a> (spissu chiamatu “Bib”), di cui li libri eranu raggruppati in torrents pi nomu (A.torrent, B.torrent) e distribbuiti attraversu the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Da lu nostru vuluntariu “bpb9v”. Pi cchiù infurmazzioni supra <a %(a_href)s>CADAL</a>, vidi li nuti nta la nostra <a %(a_duxiu)s>pàggina di dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Cchiù da lu nostru vuluntariu “bpb9v”, principalmenti schedi DuXiu, comu puru na cartella “WenQu” e “SuperStar_Journals” (SuperStar è la cumpagnia darreri DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Da lu nostru vuluntariu “cgiym”, testi cinesi da vari fonti (rappresentati comu sottodirettori), inclusu da <a %(a_href)s>China Machine Press</a> (un granni edituri cinisi)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Cullezzioni non-cinesi (rappresentati comu sottodirettori) da lu nostru vuluntariu “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scraping di libri supra l'architettura cinisi, di vuluntariu <q>cm</q>: <q>Lu pigghiai sfruttannu na vulnerabilità di la riti a la casa editrici, ma chidda falla è stata chiusa</q>. Corrispondi a <q>chinese_architecture</q> metadata in <a %(a1)s><q>Àutri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libri da la casa editrici accademica <a %(a_href)s>De Gruyter</a>, culliggiuti da qualchi granni torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape di <a %(a_href)s>docer.pl</a>, un situ polaccu di condivisioni di schedi focalizzatu supra libri e àutri òpiri scritti. Scraped a fini 2023 da lu vuluntariu “p”. Nun avemu boni metadata da lu situ urigginali (mancu li estensioni di schedi), ma avemu filtratu pi schedi simili a libri e spissu avemu pututu estrariri metadata da li schedi stissi."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direttamenti da DuXiu, culliggiuti da lu vuluntariu “w”. Sulu li libri DuXiu ricenti sunnu dispunìbbili direttamenti attraversu ebooks, accussì la maggiuranza di chisti avissi a essiri ricenti."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Rimanenti schedi DuXiu da lu vuluntariu “m”, chi nun eranu ntô furmatu prupietariu PDG di DuXiu (lu principali <a %(a_href)s>dataset DuXiu</a>). Culliggiuti da tanti fonti urigginali, purtroppu senza prisirvari chiddi fonti ntô percorsu di schedi."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scraping di libri erotici, di vuluntariu <q>do no harm</q>. Corrispondi a <q>hentai</q> metadata in <a %(a1)s><q>Àutri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Cullezzioni scraped da un edituri giappunisi di Manga da lu vuluntariu “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivi ghjudiziari selezziunati di Longquan</a>, furnuti da lu vuluntariu “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape di <a %(a_href)s>magzdb.org</a>, un alliatu di Library Genesis (è linkatu ntâ pàggina principali di libgen.rs) ma chi nun vulìa furniri li so schedi direttamenti. Ottinutu da lu vuluntariu “p” a fini 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Vari piccoli caricamenti, troppu piccoli pi essiri na so sottocullezzioni, ma rapprisintati comu direttori."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks di AvaxHome, un situ russu di condivisioni di file."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archiviu di giurnali e rivisti. Corrispondi a <q>newsarch_magz</q> metadata in <a %(a1)s><q>Àutri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scraping di lu <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Cullezzioni di lu vuluntariu “o” chi ha culliggiutu libri polacchi direttamenti da siti di rilascio urigginali (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Cullezzioni cumminati di <a %(a_href)s>shuge.org</a> da li vuluntari “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperiale di Trantor”</a> (chiamata accussì doppu la biblioteca fictizia), scraped ntô 2022 da lu vuluntariu “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sottosottocullezzioni (rappresentati comu direttori) da lu vuluntariu “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (da <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, la me piccola libreria — woz9ts: “Stu situ si cuncentra principalmenti supra la condivisioni di schedi ebook di alta qualità, qualchi di chiddi sunnu impaginati da lu prupietariu stissu. Lu prupietariu fu <a %(a_arrested)s>arrestatu</a> ntô 2019 e qualchissia ha fattu na cullezzioni di schedi chi iddu ha cundivisu.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Restanti schedi DuXiu di lu vuluntariu “woz9ts”, chi nun eranu ntô furmatu prupietariu PDG di DuXiu (ancora a cunvertiri a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrent di l'Archiviu di Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Scraping di Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library havi li sò radichi ntâ cumunità di <a %(a_href)s>Library Genesis</a>, e urigginariamenti si sviluppau cu li sò dati. Di tannu, s'havi prufissiunatu assai, e havi n'interfaccia assai cchiù muderna. Pi chistu, sunnu capaci di ottèniri assai cchiù dunazzioni, sia munetari pi cuntinuari a migliurari lu sò situ web, sia dunazzioni di libbra novi. Hanu accumulatu na granni cullezioni n'aggiunta a Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Aggiornamentu di frivaru 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "A fini di lu 2022, li presunti funnaturi di Z-Library foru arristati, e li dumini foru cunfiscati di l'auturitati di li Stati Uniti. Di tannu lu situ web ha cuminciatu lentamenti a turnari online. Nun si sapi cu lu gestisci attuarmenti."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La cullezioni è cumposta di tri parti. Li pàggini di discrizzioni urigginali pi li primi dui parti sunnu prisirvati sutta. Aviti bisognu di tutti tri li parti pi aviri tutti li dati (eccettu li torrent superati, chi sunnu barrati ntâ pàggina di li torrent)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: la nostra prima rilascita. Chista fu la primissima rilascita di chiddu chi allura era chiamatu “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: secunna rilascita, chista vota cu tutti li schedi avvolti ntô furmatu .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: rilasciti incrimentali novi, usannu lu <a %(a_href)s>furmatu di l'Archiviu di Anna Containers (AAC)</a>, ora rilascitati in cullabburazzioni cu lu team di Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrent di l'Archiviu di Anna (metadata + cuntinutu)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Esempiu di schedi ntô Archiviu di Anna (cullezioni urigginali)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Esempiu di schedi ntô Archiviu di Anna (cullezioni “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sito principali"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Duminiu Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Post ntô blog riguardu a la Rilascita 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Post ntô blog riguardu a la Rilascita 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Rilasciti di Zlib (pàggini di discrizzioni urigginali)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Rilascita 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Lu specchiu iniziali fu ottinutu cu granni sforzu duranti lu cursu di lu 2021 e 2022. A stu puntu è leggirmenti obsoletu: rifletti lu statu di la cullezioni a giugnu 2021. Nni aggiorneremu chistu ntô futuru. Ora semu cuncentrati a rilasciri chista prima rilascita."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Doppu chi Library Genesis è già prisirvatu cu li torrent pùbbrichi, e veni nclusu ntâ Z-Library, facemmu na deduplicazioni di basi contru Library Genesis ntô giugnu 2022. Pi chistu usammu li hash MD5. Probabilmenti c'è assai cchiù cuntenutu duplicatu ntâ libreria, comu formati di file multipli cu lu stissu libbru. Chistu è difficili di rilevari cu pricisioni, allura nun lu facemmu. Doppu la deduplicazioni, ristammu cu cchiù di 2 miliuna di file, pi un tutali di sutta 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La cullizzioni è cumposta di dui parti: un dump MySQL “.sql.gz” di li metadata, e li 72 file torrent di circa 50-100GB ognunu. Li metadata cuntenunu li dati comu ripurtati dû situ web di Z-Library (titulu, auturi, discrizzioni, tipu di file), comu puru la diminsioni dû file e lu md5sum ca avemu osservatu, pirchì a voti sti dati nun currispùnnunu. Pari ca ci sunnu rangi di file pi cui la Z-Library stissa havi metadata sbagghiati. Pò essiri ca avemu scaricatu file sbagghiati in casi isolati, chi pruvaremu a rilevari e aggiustari ntô futuru."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Li granni file torrent cuntenunu li dati veri di li libbra, cu l'ID di Z-Library comu nomu di file. Li estinzioni di li file ponnu èssiri ricustruiti usannu lu dump di metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La cullizzioni è na mistura di cuntenutu di non-fizzioni e fizzioni (nun separati comu ntâ Library Genesis). La qualità è puru assai variata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Sta prima rilassata è ora cumpletamenti dispunìbbili. Notati ca li file torrent sunnu dispunìbbili sulu attraversu lu nostru specchiu Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Rilassata 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Avemu ottinutu tutti li libbra ca foru aggiunti â Z-Library tra lu nostru ultimu specchiu e austu 2022. Avemu puru turnatu e scaricatu certi libbra ca avemu pirdutu la prima vota. In tuttu, sta nova cullizzioni è circa 24TB. Di novu, sta cullizzioni è deduplicata contru Library Genesis, pirchì ci sunnu già torrent dispunìbbili pi chidda cullizzioni."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Li dati sunnu organizzati simili â prima rilassata. C'è un dump MySQL “.sql.gz” di li metadata, chi ncludi puru tutti li metadata di la prima rilassata, superannu accussì chidda. Avemu puru aggiuntu certi novi culonni:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: siddu stu file è già ntâ Library Genesis, sia ntâ cullizzioni di non-fizzioni o fizzioni (corrispondenza cu md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in quali torrent si trova stu file."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: settatu quannu nun semu stati capaci di scaricari lu libbru."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Avemu menzionatu chistu l'ultima vota, ma sulu pi chiariri: “filename” e “md5” sunnu li prupitati veri dû file, mentri “filename_reported” e “md5_reported” sunnu chiddu ca avemu scaricatu di Z-Library. A voti sti dui nun currispùnnunu, allura avemu nclusu tutti dui."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pi sta rilassata, avemu canciatu la collazioni a “utf8mb4_unicode_ci”, chi avissi a èssiri cumpatìbbili cu li virsioni cchiù vecchi di MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Li file di dati sunnu simili â vota scorsa, anchi siddu sunnu assai cchiù granni. Nun putemmu disturbarini a criari tanti file torrent cchiù nichi. “pilimi-zlib2-0-14679999-extra.torrent” cunteni tutti li file ca avemu pirdutu ntâ rilassata scorsa, mentri l'àutri torrent sunnu tutti novi rangi di ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Aggiornamentu %(date)s:</strong> Avemu fattu la maggiuranza di li nostri torrent troppu granni, facennu strugghiri li client di torrent. Li avemu rimossi e rilassatu novi torrent."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Aggiornamentu %(date)s:</strong> C'eranu ancora troppi file, allura li avemu impacchettati in file tar e rilassatu novi torrent di novu."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Rilassata 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Chistu è un sulu file torrent extra. Nun cunteni nudda nova nfurmazzioni, ma havi certi dati ca ponnu pigghiari tempu a èssiri calcolati. Chistu lu fa cunvinienti aviri, pirchì scaricari stu torrent è spissu cchiù veloci ca calcolallu di novu. In particulari, cunteni indici SQLite pi li file tar, pi usari cu <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Dumanni Fatti Frequentimenti (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Chi è l'Archiviu di Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>L'Archiviu di Anna</span> è nu pruggettu senza scopu di lucro cu dui obiettivi:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Presirvazzioni:</strong> Fari nu backup di tuttu lu sapiri e la cultura di l'umanitati.</li><li><strong>Accessu:</strong> Fari stu sapiri e cultura dispunibili a chiunque ntô munnu.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tuttu u nostru <a %(a_code)s>codici</a> e <a %(a_datasets)s>dati</a> sunnu cumpletamenti open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Presirvazzioni"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Presirvamu libri, articuli, fumetti, rivisti, e cchiù, purtannu sti materiali di vari <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliotechi ombra</a>, bibliotechi ufficiali, e àutri cullizzioni nsemmula in un sulu locu. Tuttu stu dati è presirvatu pi sempri facennu facili a duplicazioni in bloccu — usannu torrents — risultannu in tanti copi ntô munnu. Certi bibliotechi ombra già fannu chistu iddi stissi (p'esempiu Sci-Hub, Library Genesis), mentri l'Archiviu di Anna “libbera” àutri bibliotechi ca nun offrunu distribuzioni in bloccu (p'esempiu Z-Library) o nun sunnu bibliotechi ombra a tutti (p'esempiu Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Sta larga distribuzioni, cummigghiata cu u codici open-source, fa u nostru situ web resilienti a i rimozioni, e assicura a presirvazzioni a longu termini di lu sapiri e la cultura di l'umanitati. Amparati cchiù supra <a href=\"/datasets\">i nostri datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Stimamu ca avemu presirvatu circa <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% di i libri di lu munnu</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Accessu"

#, fuzzy
msgid "page.home.access.text"
msgstr "Travàiemu cu li nostri partinari pi fari li nostri culizzioni facirmenti e libbiramenti accessìbbili a tutti. Cridemu ca ognunu avi lu dirittu a la saggezza cullettiva di l'umanità. E <a %(a_search)s>senza dannu pi l'autura</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Scaricamenti ogni ura nni l'ùrtimi 30 jorna. Media ogni ura: %(hourly)s. Media ogni jornu: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Cridemu fortimenti ntô flussu libbiru di l'infurmazzioni, e ntâ prisirvazzioni di la cunniscenza e la cultura. Cu stu muturi di ricerca, custruemu supra li spaddi di giganti. Rispettemu prufunnamenti lu travagghiu ardu di li pirsuni ca hannu criatu li vari bibliutichi ombra, e spiramu ca stu muturi di ricerca allargherà la sò portata."

#, fuzzy
msgid "page.about.text3"
msgstr "Pi ristari aggiornatu supra lu nostru prugressu, siguiti Anna supra <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pi dumanni e feedback cuntattati Anna a %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Comu pozzu aiutari?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Sèguitini supra <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Spargi la vuci di Anna’s Archive supra Twitter, Reddit, Tiktok, Instagram, ô tò cafè o biblioteca lucali, o unni vai! Nun cridemu ntô \"gatekeeping\" — si ni chiudunu, ni rapiemu n'àutru postu, pirchì tuttu lu nostru còdici e dati sunnu cumplitamenti open source.</li><li>3. Si poi, cunsidira di <a href=\"/donate\">dunari</a>.</li><li>4. Aiutaci a <a href=\"https://translate.annas-software.org/\">tradùciri</a> lu nostru situ web in àutri lingui.</li><li>5. Si sì nu ngigneri di software, cunsidira di cuntribbuiri ô nostru <a href=\"https://annas-software.org/\">open source</a>, o di spargiri i nostri <a href=\"/datasets\">torrenti</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Ora avemu puru un canali Matrix sincrunizzatu a %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Si sì nu ricircaturi di sicurizza, putemu usari i tò capacità sia pi l'attaccu ca pi la difisa. Tàlia la nostra pàggina <a %(a_security)s>Sicurezza</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Circamu spirti nti i pagamenti pi i cummercianti anonimi. Pò aiutàrini a aggiùnciri cchiù modi cunvinienti pi dunari? PayPal, WeChat, carti rigalu. Si canusci quarcunu, cuntattaci."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Circamu sempri cchiù capacità di server."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Pò aiutàrini segnalandu prubbrimi cu i file, lassannu cummenti, e criannu listi direttamente supra stu situ. Pò puru aiutàrini <a %(a_upload)s>caricannu cchiù libri</a>, o sistimannu prubbrimi cu i file o lu furmattazzioni di i libri esistenti."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Cria o aiutà a mantèniri la pàggina Wikipedia di Anna’s Archive nti la tò lingua."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Circamu di piazzari picculi, gustusi publicità. Si vò pubblicizzari supra Anna’s Archive, facci sapiri."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Ci piaciissi ca la genti criassi <a %(a_mirrors)s>specchi</a>, e ni sustinèmu finanziarmenti."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pi nfurmazzioni cchiù estensivi supra comu vuluntariari, vidi la nostra pàggina <a %(a_volunteering)s>Voluntariatu & Ricumpensi</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Pirchì i download lenti sunnu accussì lenti?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Nun avemu risorsi abbastanti pi dari a tutti ntô munnu scaricamenti ad alta vilucità, quantu vulissimu. Si nu benefatturi riccu vulissi aiutàrini a fari chistu, saria incredìbbili, ma nzinu a tannu, facemu lu nostru megghiu. Semu nu pruggettu senza scopu di lucro ca si susteni appena cu i dunazzioni."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Chistu è pirchì avemu implementatu dui sistemi pi li scaricamenti gratuiti, cu li nostri partinari: servidori cundivisi cu scaricamenti lenti, e servidori un pocu cchiù veloci cu una lista d'attisa (pi ridùciri lu nùmmiru di pirsuni ca scaricanu a lu stissu tempu)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Avemu puru <a %(a_verification)s>verifica di navigaturi</a> pi li scaricamenti lenti, pirchì sinnò i bot e i scraper li abusanu, facennu li cosi ancora cchiù lenti pi l'utenti legittimi."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Nota chi, quannu usi lu Tor Browser, putresti aviri a aggiustari li tò impostazioni di sicurizza. Ntâ l'opzioni cchiù bassa, chiamata “Standard”, la sfida di Cloudflare turnstile riesci. Ntâ l'opzioni cchiù àuti, chiamati “Safer” e “Safest”, la sfida fallisci."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "A voti granni a voti li scaricamenti lenti ponnu rumpirisi a menzu. Ricummannamu di usari un gestori di scaricamenti (comu JDownloader) pi ripigghiari autumaticamenti li scaricamenti granni."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "FAQ di donazioni"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Li abbunamenti si rinnòvanu in autumàticu?</div> Li abbunamenti <strong>non</strong> si rinnòvanu in autumàticu. Poti junciri pi quantu tempu voi, longu o curtu."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Posso aghjurnari lu me abbunamentu o aviri cchiù abbunamenti?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Aviti àutri metudi di pagamentu?</div> Attualmenti no. Assai genti non voli ca archivi comu chistu esìstinu, allura avemu a essiri attenti. Si poi aiutàrini a stabbiliri àutri metudi di pagamentu (cchiù cunvinienti) in modu sicuru, cuntattàtini a %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Cosa significanu i range per mese?</div> Pudete ghjunghje à u latu inferiore di un range applicandu tutti i sconti, cum'è sceglie un periodu più longu di un mese."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Chi ci faciti cu li donazioni?</div> 100%% va a prisirvari e fari accessìbbili la cunniscenza e la cultura di lu munnu. Attualmenti li spènnimu principalmenti pi servidori, archiviazzioni, e larghezza di banda. Nuddu sordi va a li membri di lu team personalmenti."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Pòssu fari na granni donazioni?</div> Saria fantastico! Pi donazioni supiriori a qualchi migghiaru di dòllari, cuntattàtini direttamenti a %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Pòssu fari na donazioni senza divintari membru?</div> Certu. Accettamu donazioni di ogni ammontari a chistu indirizzu Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Comu fazzu a carricari novi libri?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "In alternativa, putiti carricari chiddi a Z-Library <a %(a_upload)s>ccà</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pi picculi caricamenti (finu a 10.000 schedari) pi favuri caricàtili sia in %(first)s ca in %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Pi ora, suggerièmu di carricari novi libri a li fork di Library Genesis. Eccu un <a %(a_guide)s>ghidi utili</a>. Notati ca tutti dui i fork ca indicizzamu in stu situ web tiranu di stu stissu sistema di carricamentu."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pi Libgen.li, assicurati di prima accedi a <a %(a_forum)s >lu so forum</a> cu l'username %(username)s e la password %(password)s, e poi torna a la so <a %(a_upload_page)s >pàggina di caricamentu</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Si lu tò indirizzu email non funziona nei forum di Libgen, ricummannamu di usari <a %(a_mail)s>Proton Mail</a> (gratuitu). Poti puru <a %(a_manual)s>richièdiri manualmenti</a> l'attivazzioni di lu tò account."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Notati ca mhut.org blocca certi range di IP, allura pò essiri nicissariu un VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pi carricamenti granni (supra 10,000 schedi) ca nun vennu accittati di Libgen o Z-Library, cuntattatici a %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Pi carricari articuli accademici, vi rugamu di carricari puru (oltre a Library Genesis) a <a %(a_stc_nexus)s>STC Nexus</a>. Sunnu la megghiu bibliuteca ombra pi articuli novi. Ancora nun l'avemu integrati, ma lu faremumu a un certu puntu. Putiti usari lu sò <a %(a_telegram)s>bot di carricamentu supra Telegram</a>, o cuntattari l'indirizzu elencatu ntô sò missaggiu pinnatu si aviti troppi schedi pi carricari in stu modu."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Comu fazzu a richiediri libri?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Al mumentu, nun putemu accuddiri richiesti di libri."

#, fuzzy
msgid "page.request.forums"
msgstr "Fati li vostri richiesti supra i forum di Z-Library o Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nun nni mannati email cu li vostri richiesti di libri."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Cugghiti metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Sì, lu facemu."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Scaricai 1984 di George Orwell, la pulizia veni a la me porta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Nun vi prioccupati troppu, ci sunnu tanti pirsuni ca scaricanu di siti ca linkamu, e è estremamenti raru ca si potti aviri prubbremi. Tuttavia, pi ristari sicuri vi ricumandamu di usari un VPN (a pagamentu), o <a %(a_tor)s>Tor</a> (gratuitu)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Comu fazzu a sarvari li me impostazioni di ricerca?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selezzi i paràmetri ca ti piàcinu, lassa la casella di ricerca vacanti, clicca \"Ricerca\", e poi aggiungi la pàggina ai segnalibri usannu la funzione di segnalibri dû tò navigaturi."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Aviti n'app mòbbili?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nun avemu n'app mòbbili ufficiali, ma poi installari stu situ comu n'app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Clicca lu menù cu tri punti a lu cantu supra a dritta, e selezziuna \"Aggiungi a Home Screen\"."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Clicca lu buttuni \"Condividi\" a lu funnu, e selezziuna \"Aggiungi a Home Screen\"."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Aviti n'API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Avemu n'API JSON stabbili pi li membri, pi ottèniri n'URL di scaricamentu viloci: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentazioni ntâ JSON stissa)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pi àutri casi d'usu, comu iterari tra tutti i nostri file, custruiri ricerchi custumizati, e accussì via, ricummannamu <a %(a_generate)s>generari</a> o <a %(a_download)s>scarricari</a> i nostri basi di dati ElasticSearch e MariaDB. Li dati grezzi ponnu èssiri esplorati manualmenti <a %(a_explore)s>attraversu file JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "La nostra lista di torrent grezzi pò èssiri scarricata comu <a %(a_torrents)s>JSON</a> puru."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ di Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Vurrissi aiutari a seedari, ma nun haiu assai spaziu di discu."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Usa lu <a %(a_list)s>generaturi di lista di torrent</a> pi generari na lista di torrent chi sunnu cchiù nicissarii di torrentari, ntô limiti dû tò spaziu di archiviazioni."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Li torrent sunnu troppu lenti; pozzu scarricari i dati direttamenti di vui?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Sì, vidi la pàggina <a %(a_llm)s>dati LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Pozzu scarricari sulu na sottosezioni di file, comu sulu na lingua o un argomenti particulari?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Risposta curta: nun è facili."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Risposta longa:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "La maiur parti di li torrent cuntenunu li file direttamenti, chi significa ca putiti istruiri li client torrent a scaricari sulu li file nicissarii. Pi ditirminari quali file scaricari, putiti <a %(a_generate)s>ginerari</a> li nostri metadata, o <a %(a_download)s>scaricari</a> li nostri basi di dati ElasticSearch e MariaDB. Sfurtunatamenti, certi cullezzioni di torrent cuntenunu file .zip o .tar a la radici, e in chistu casu aviti a scaricari tuttu lu torrent prima di putiri sèliri li file individuali."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(N'avemu <a %(a_ideas)s>quarchi idea</a> pi stu casu però.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nuddu strumenti facili da usari pi filtrari i torrent sunnu dispunìbbili ancora, ma accugliemu cuntribbuti."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Comu trattati li duplicati ntê torrent?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Pruvamu a mantèniri la duplicazioni o l'ovirlap minimu tra li torrent ntâ sta lista, ma chistu nun pò sempri èssiri raggiuntu, e dipenni assai di li pulìtichi di li bibliotechi di urìggini. Pi li bibliotechi chi rilascianu li sò propri torrent, nun è ntê nostri mani. Pi li torrent rilassati dû Archiviu di Anna, facemu la deduplicazioni sulu basata supra l'hash MD5, chi significa ca diffirenti virsioni dû stissu libru nun vennu deduplicati."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Posso aviri la lista di torrent comu JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Sì."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Nun vecu PDF o EPUB ntê torrent, sulu file binari? Chi fazzu?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Chisti sunnu effittivamenti PDF e EPUB, sulu ca nun hannu na estinzioni ntâ maiur parti di li nostri torrent. Ci sunnu dui posti unni putiti truvari li metadata pi li file torrent, cumpresi li tipi di file/estinzioni:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Ogni cullezzioni o rilascio hà i so metadati. Per esempiu, i <a %(a_libgen_nonfic)s>torrenti di Libgen.rs</a> anu una basa di dati di metadati currispundenti ospitata nantu à u situ web di Libgen.rs. Tipicamenti, ligamu à risorse di metadati rilevanti da ogni <a %(a_datasets)s>pagina di dataset</a> di a cullezzioni."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Ricumandemu di <a %(a_generate)s>generà</a> o <a %(a_download)s>scaricà</a> e nostre basi di dati ElasticSearch è MariaDB. Queste cuntenenu una mappa per ogni registru in l'Archiviu di Anna à i so file torrent currispundenti (se dispunibili), sottu \"torrent_paths\" in u JSON di ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Picchì u me client torrent nun pò apriri certi file torrent / link magnetici vostri?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Certi client torrent nun supportanu granni dimensioni di pezzi, chi assai di i nostri torrent hannu (pi chiddi più novi nun facemu cchiù accussì — anchi si è validu secunnu li specificazioni!). Allura pruvati un client diversu si vi succedi chistu, o lamentativi cu i produttori di u vostru client torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Avete un prugramma di divulgazione responsabile?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Accogliemu i ricercatori di sicurezza per circà vulnerabilità in i nostri sistemi. Semu grandi prumotori di a divulgazione responsabile. Cuntattateci <a %(a_contact)s>quì</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Attuàlmenti nun semu capaci di dari ricumpenzi pi bug, eccettu pi li vulnerabilità ca hannu lu <a %(a_link)s>putenziali di cumpromèttiri la nostra anonimità</a>, pi cui offriemu ricumpenzi tra $10k-50k. Vulemu offriri na gamma cchiù larga di ricumpenzi pi bug ntô futuru! Pi favuri, nota ca l'attacchi di ingegneria suciali sunnu fora di scopu."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si sì ntirissatu â sicurizza offensiva, e voi aiutari a archiviaru lu sapiri e la cultura dû munnu, assicurati di cuntattàrini. Ci sunnu tanti modi pi cui poi dari na manu."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ci sunnu cchiù risorsi supra l'Archiviu di Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>U Blog di Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — aggiornamenti regolari"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software di Anna</a> — u nostru còdici open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduci supra u Software di Anna</a> — u nostru sistema di traduzzioni"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — riguardu i dati"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dumìnii alternativi"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — cchiù nfurmazzioni su di nuatri (pi favuri aiuta a mantèniri sta pàggina aggiornata, o criane una pi la tò lingua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Comu fazzu a signalari una violazioni di copyright?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nun ospitamu nuddu materiale cu diritti d'auturi ccà. Semu nu muturi di ricerca, e comu tali indicizzamu sulu li metadata ca sunnu già dispunìbbili pubblicamenti. Quannu scaricati di sti fonti esterni, suggeremu di cuntrullari li liggi di la tò giurisdizioni rispettu a chiddu ca è permessu. Nun semu rispunzàbbili pi lu cuntinutu ospitatu di àutri."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si aviti lagnanzi supra chiddu ca viditi ccà, la megghiu cosa è cuntattari lu situ urigginali. Nni tiramu regularmenti li sò cambiamenti ntâ nostra basi di dati. Si pinsati ca aviti na lagnanza DMCA valida a cui avemu a rispùnniri, vi prigamu di cumpilari lu <a %(a_copyright)s>modulu di lagnanza DMCA / Copyright</a>. Pigghiamu seriamenti li vostri lagnanzi, e vi rispùnniremu appena pussìbbili."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Odiu comu gestiti stu pruggettu!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Vulemu puru ricurdari a tutti ca tuttu lu nostru còdici e dati è cumpletamenti open source. Chistu è unicu pi prughjetti comu lu nostru — non canùscemu àutri prughjetti cu un catalogu similiarmenti massicciu ca è puru cumpletamenti open source. Accuglièmu cu piaciri quarchidunu ca pensa ca gestemu mali lu nostru prughjettu a pigghiari lu nostru còdici e dati e stabbiliri la sò propria biblioteca ombra! Non lu dicemu cu disprezzu o quarchicosa — cridemu genuinamenti ca saria fantastico pirchì alzaria lu livellu pi tutti, e prisirverebbi megghiu l'eredità di l'umanità."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Hai un monitor di uptime?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Pì favori vidi <a %(a_href)s>stu pruggettu eccellente</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Comu possu donari libri o àutri materiali fisici?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Mandali pi' piaciri a l'<a %(a_archive)s>Internet Archive</a>. Iddri li priservanu currettamenti."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Cu è Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Tu sì Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Chi sunnu i tò libri preferiti?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Eccu quarchi libru ca porta na significanza spiciali pi lu munnu di li biblioteche ombra e la prisirvazzioni digitali:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Hai finutu li scaricamenti veloci pi oggi."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Diventa membru pi usari li scaricamenti veloci."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Ora accittamu carti rigalu Amazon, carti di crèditu e dèbitu, cripto, Alipay, e WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Database cumpletto"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libri, articuli, rivisti, fumetti, registri di biblioteca, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Ricerca"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ha <a %(a_paused)s>suspisu</a> l'uppload di novi articuli."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB è na cuntinuazzioni di Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accessu direttu a %(count)s articuli accademici"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Aperto"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Si sì un <a %(a_member)s>mèmmiru</a>, la virifica di lu browser nun è nicissaria."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archiviu a longu tèrmini"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Li datasets usati ntâ Anna’s Archive sunnu cumplitamenti aperti, e ponnu èssiri mirrurati in bloccu usannu li torrents. <a %(a_datasets)s>Sapine cchiù…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Pò aiutari assai sidennu li torrents. <a %(a_torrents)s>Sapine cchiù…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Avemu la cullizzioni cchiù granni di dati di testu di alta qualità ntô munnu. <a %(a_llm)s>Sapine cchiù…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Mirrura: chiamata pi vuluntari"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Circannu vuluntari"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Comu pruggettu non-profit e open-source, circamu sempri genti pi dari na manu."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Si gestisci un processore di pagamenti anonimi ad altu risicu, cuntattaci. Circamu puru genti pi piazzari picculi annunci di bon gustu. Tutti i proventi vannu ai nostri sforzi di preservazioni."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "U Blog di Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Download IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Tutti i link di download pi stu file: <a %(a_main)s>Pagina principale di u file</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(potresti aviri bisognu di pruvà più voti cu IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Pi ottèniri download più veloci e saltari i cuntrolli di u browser, <a %(a_membership)s>diventa membru</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Pi mirrurari in bloccu a nostra cullizzioni, taliati i pàggini <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Dati LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "È ben caputu ca li LLM prosperanu supra dati di alta qualità. Avemu la cullizzioni cchiù granni di libri, articuli, rivisti, ecc. ntô munnu, chi sunnu qualchi di li fonti di testu cchiù di qualità."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Scala e gamma unica"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "La nostra cullizzioni cunteni cchiù di centu miliuna di file, cumpresi giurnali accademici, manuali, e rivisti. Raggiungemu sta scala cumminannu granni ripusitori esistenti."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Qualchi di li nostri cullizzioni di fonti sunnu già dispunìbbili in massa (Sci-Hub, e parti di Libgen). Àutri fonti li avemu liberati nuàutri stessi. <a %(a_datasets)s>Datasets</a> mustra na panoramica cumpleta."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "La nostra cullizzioni include miliuna di libri, articuli, e rivisti di prima di l'era di l'e-book. Granni parti di sta cullizzioni sunnu già stati OCR’ati, e già hannu picca sovrappusizioni interna."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Comu putemu aiutari"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Semmu capaci di furniri accessu a alta vilucità a li nostri cullizzioni cumpleti, comu puru a cullizzioni nun rilassati."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Chistu è accessu a livellu d'impresa ca putemu furniri pi donazioni ntô gamma di dicini di migghiaia di USD. Semmu puru disposti a scambiari chistu pi cullizzioni di alta qualità ca nun avemu ancora."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Putemu rimbursariti si si' capaci di furnirini arricchimentu di li nostri dati, comu:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Rimuzzioni di sovrappusizioni (deduplicazione)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Estrazione di testu e metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Susteni l'archiviazione a longu terminu di la cunniscenza umana, mentri otteni dati megghiu pi lu to mudellu!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Cuntattaci</a> pi discutiri comu putemu travagghiari nzemi."

#, fuzzy
msgid "page.login.continue"
msgstr "Cuntinuari"

#, fuzzy
msgid "page.login.please"
msgstr "Pi piaciri <a %(a_account)s>accedi</a> pi taliari sta pàggina.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "L'Archiviu di Anna è temporaneamenti fora pi manutenzioni. Pi piaciri turnati tra un'ura."

#, fuzzy
msgid "page.metadata.header"
msgstr "Migliura metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Poti aiutari a prisirvari i libri migliurannu i metadata! Prima, leggi lu sfunnu supra i metadata supra l'Archiviu di Anna, e poi ampara comu migliurari i metadata attravirsu lu linking cu Open Library, e guadagna un'abbonamentu gratuitu supra l'Archiviu di Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Sfunnu"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Quannu guardi un libru supra l'Archiviu di Anna, poi vidiri vari campi: titulu, auturi, edituri, edizioni, annu, discrizzioni, nomu di schedariu, e cchiù. Tutti chisti pezzi d'infurmazzioni sunnu chiamati <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Datu ca cumbinamu i libri di vari <em>bibliotechi di surgenti</em>, mustramu quarsiasi metadata è dispunibili in chidda biblioteca di surgenti. Pi esempiu, pi un libru ca avemu pigghiatu di Library Genesis, mustramu lu titulu di la basa di dati di Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "A voti un libru è prisenti in <em>diversi</em> bibliotechi di surgenti, ca putissiru aviri campi di metadata differenti. In chistu casu, mustramu semplicimenti la versione cchiù longa di ogni campu, pirchì chidda speru ca cunteni l'infurmazzioni cchiù utili! Mustramu ancora l'autri campi sutta la discrizzioni, p'esempiu comu \"titulu alternativu\" (ma sulu si sunnu differenti)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Esprimemu puru <em>codici</em> comu identificatori e classificatori di la biblioteca di surgenti. <em>Identificatori</em> rapprisèntanu un'edizioni particulari di un libru; esempi sunnu ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classificatori</em> raggruppanu inseme diversi libri simili; esempi sunnu Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. A voti sti codici sunnu esplicitamenti ligati in bibliotechi di surgenti, e a voti putemu esprimiri di lu nomu di schedariu o discrizzioni (principalmenti ISBN e DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Putemu usari identificatori pi truvari registri in <em>cullizzioni di metadata sulu</em>, comu OpenLibrary, ISBNdb, o WorldCat/OCLC. C'è un tab specificu di <em>metadata</em> in lu nostru muturi di ricerca si voi navigari chiddi cullizzioni. Usamu registri currispundenti pi riempiri campi di metadata mancanti (p'esempiu si manca un titulu), o p'esempiu comu \"titulu alternativu\" (si c'è un titulu esistenti)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Pi vidiri esattamenti di unni veni lu metadata di un libru, vidi lu tab <em>“Dettagli tecnici”</em> supra una pàggina di libru. Ha un link a lu JSON grezzu pi chiddu libru, cu puntaturi a lu JSON grezzu di i registri originali."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Pi cchiù infurmazzioni, vidi i pàggini seguenti: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Ricerca (tab di metadata)</a>, <a %(a_codes)s>Esploratore di Codici</a>, e <a %(a_example)s>Esempiu di metadata JSON</a>. Finalmenti, tutti i nostri metadata ponnu essiri <a %(a_generated)s>generati</a> o <a %(a_downloaded)s>scaricati</a> comu basi di dati ElasticSearch e MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Linking cu Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Allura, si truvassi un schedariu cu metadata sbagliati, comu l'addristi? Poi iri a la biblioteca di surgenti e siguitari i so pruceduri pi addristari i metadata, ma chi fari si un schedariu è prisenti in diversi bibliotechi di surgenti?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "C'è un identificatori ca è trattatu in modu speciali supra l'Archiviu di Anna. <strong>Lu campu annas_archive md5 supra Open Library supera sempri tutti l'autri metadata!</strong> Facemu un passu arredi e amparamu di Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library fu fundata in 2006 di Aaron Swartz cu l'obbiettivu di \"una pàggina web pi ogni libru mai pubblicatu\". È comu una Wikipedia pi metadata di libri: tutti ponnu editari, è licenziata liberamenti, e pò essiri scaricata in massa. È una basa di dati di libri ca è cchiù alliniata cu la nostra missioni — in fatti, l'Archiviu di Anna è statu ispiratu di la visioni e la vita di Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Inveci di rinventari la rota, avemu dicisu di ridirigiri i nostri vuluntari versu Open Library. Si vidi un libru ca ha metadata sbagliati, poi aiutari di la manera seguenti:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Veni a lu <a %(a_openlib)s>situ web di Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Truva lu registru currettu di lu libru. <strong>ATTENZIONI:</strong> assicurati di selezziunari l'edizioni <strong>curretta</strong>. In Open Library, hai \"opere\" e \"edizioni\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Un'\"opera\" putissi essiri \"Harry Potter e la Pietra Filosofale\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Un'\"edizioni\" putissi essiri:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La prima edizione del 1997 pubblicata da Bloomsbery con 256 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "L'edizione tascabile del 2003 pubblicata da Raincoast Books con 223 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La traduzione polacca del 2000 “Harry Potter I Kamie Filozoficzn” di Media Rodzina con 328 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Tutte queste edizioni hanno ISBN e contenuti diversi, quindi assicurati di selezionare quella giusta!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Modifica il record (o creane uno se non esiste) e aggiungi quante più informazioni utili possibile! Sei già qui, tanto vale rendere il record davvero straordinario."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sotto “ID Numbers” seleziona “L'Archiviu di Anna” e aggiungi l'MD5 del libro da L'Archiviu di Anna. Questa è la lunga stringa di lettere e numeri dopo “/md5/” nell'URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Cerca di trovare altri file in L'Archiviu di Anna che corrispondono a questo record e aggiungi anche quelli. In futuro potremo raggrupparli come duplicati nella pagina di ricerca di L'Archiviu di Anna."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando hai finito, scrivi l'URL che hai appena aggiornato. Una volta che hai aggiornato almeno 30 record con gli MD5 di L'Archiviu di Anna, inviaci un <a %(a_contact)s>email</a> e mandaci l'elenco. Ti daremo un abbonamento gratuito a L'Archiviu di Anna, così potrai fare questo lavoro più facilmente (e come ringraziamento per il tuo aiuto). Queste devono essere modifiche di alta qualità che aggiungono una quantità sostanziale di informazioni, altrimenti la tua richiesta verrà respinta. La tua richiesta verrà anche respinta se una qualsiasi delle modifiche verrà annullata o corretta dai moderatori di Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Nota che questo funziona solo per i libri, non per articoli accademici o altri tipi di file. Per altri tipi di file consigliamo comunque di trovare la biblioteca di origine. Potrebbero volerci alcune settimane prima che le modifiche vengano incluse in L'Archiviu di Anna, poiché dobbiamo scaricare l'ultimo dump di dati di Open Library e rigenerare il nostro indice di ricerca."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Specchi: chiamata pi vuluntari"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Pi aumentari la resilienza di l'Archiviu di Anna, circamu vuluntari pi gestiri specchi."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Gestemu circannu chistu:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Vui gestiti u codice open source di l'Archiviu di Anna, e aggiornati regularmente sia u codice ca i dati."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "A vostra versione è chiaramenti distinta comu un mirror, p'esempiu \"L'Archiviu di Bob, un mirror di l'Archiviu di Anna\"."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Siti disposti a pigghiari i risichi assuciati cu stu travagghiu, chi sunnu significativi. Aviti na prufunna capiscenza di la sicurizza operativa nicissaria. I cuntenuti di <a %(a_shadow)s>chisti</a> <a %(a_pirate)s>posti</a> sunnu evidenti pi vui."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Siti disposti a cuntribbuiri a u nostru <a %(a_codebase)s>codice</a> — in cullabburazioni cu a nostra squatra — pi fari chistu succediri."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inizialmenti nun vi dariemu accessu a i nostri server di partner pi i download, ma si tuttu va beni, putemu cundividiri chistu cu vui."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Spisi di hosting"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Semu disposti a cupriri i spisi di hosting e VPN, inizialmenti finu a $200 pi misi. Chistu è sufficenti pi un server di ricerca di basa e un proxy prutettu da DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Pagarimu pi u hosting sulu quannu aviti tuttu sistematu, e aviti dimustratu ca siti capaci di mantiniri l'archiviu aggiornatu cu l'aggiornamenti. Chistu significa ca aviti a pagari pi i primi 1-2 misi di tasca vostra."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "U vostru tempu nun sarà ricumpinsatu (e mancu u nostru), postu ca chistu è travagghiu puramenti vuluntariu."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si vi impegnati significativamenti in lu sviluppu e l'operazioni di u nostru travagghiu, putemu discutiri di cundividiri cchiù di i ricavati di i donazioni cu vui, pi utilizzarli comu nicissariu."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Cumincianu"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Pi favuri <strong>nun cuntattatici</strong> pi dumannari permissu, o pi dumanni basi. L'azzioni parranu cchiù forti di i paroli! Tuttu l'infurmazioni è ddà fora, allura avanti cu a creazioni di u vostru mirror."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Sentiti liberi di pubblicari ticket o richiesti di merge in u nostru Gitlab quannu vi truvati di fronti a prublemmi. Putemu aviri bisognu di custruiri qualchi funzioni specifici pi u mirror cu vui, comu u rebranding da \"L'Archiviu di Anna\" a u nomu di u vostru situ web, (inizialmenti) disattivannu i conti utenti, o ligannu di novu a u nostru situ principali da i pagini di i libri."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Quannu aviti u vostru mirror in funzioni, pi favuri cuntattatici. Ci piaciurissi rivisari a vostra OpSec, e quannu chista è solida, ligaremu a u vostru mirror, e cuminceremu a travagghiari cchiù vicinu cu vui."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Grazii in anticipu a tutti chiddi disposti a cuntribbuiri in stu modu! Nun è pi i deboli di cori, ma rinfurzassi a longevità di a cchiù granni biblioteca veramente aperta di a storia umana."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Scarricari di lu situ web partner"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ I scarricamenti lenti sunnu dispunibili sulu attraversu u situ ufficiali. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ I scarricamenti lenti nun sunnu dispunibili attraversu VPNs di Cloudflare o àutri indirizzi IP di Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Pi favori aspittari <span %(span_countdown)s>%(wait_seconds)s</span> sicunni pi scaricari stu file."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Usa lu seguenti URL pi scarricari: <a %(a_download)s>Scarrica ora</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Grazzi pi l'aspittari, chistu manteni lu situ accessìbbili gratis pi tutti! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Attinzioni: ci sunnu stati tanti scaricamenti dû vostru indirizzu IP ntê urtimi 24 uri. Li scaricamenti ponnu èssiri cchiù lenti dû solitu."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Scaricamenti dû vostru indirizzu IP ntê urtimi 24 uri: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si stai usannu nu VPN, cunnissioni internet cundivisa, o lu vostru ISP cundividi IP, chista avvirtènza putissi èssiri dûvuta a chistu."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Pi dari a tutti l'opportunità di scaricari i file gratis, hai bisognu di aspittari prima di putiri scaricari stu file."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Sìntiti libbiru di cuntinuari a navigari n'Anna’s Archive n'àutru tab mentri aspittati (si lu vostru navigaturi supporta l'aggiornamentu di li tab in background)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Senti libiru di aspittari ca si carricunu cchiù pàggini di scaricamentu a lu stissu tempu (ma pi favuri scarrica sulu un file a lu stissu tempu pi server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Na vota ca otteni un link di scaricamentu, è validu pi diversi uri."

#, fuzzy
msgid "layout.index.header.title"
msgstr "L'Archiviu di Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Ricurda ntô Archiviu di Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Scarica"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Pi sustèniri l'accessibilità e la prisirvazzioni a longu termini dû sapiri umanu, diventa nu <a %(a_donate)s>mèmmiru</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Comu bonus, 🧬&nbsp;SciDB si carica cchiù veloci pi li mèmmira, senza limiti."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Nun funziona? Pruvati a <a %(a_refresh)s>rinfrescari</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Nuddu preview dispunìbbili ancora. Scarica lu file di <a %(a_path)s>L'Archiviu di Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB hè una cuntinuità di Sci-Hub, cù a so interfaccia familiare è a visualizazione diretta di PDF. Inserisci u to DOI per vede."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Avemu a cullezzioni cumpleta di Sci-Hub, è ancu novi articuli. A maiò parte ponu esse visualizati direttamente cù una interfaccia familiare, simile à Sci-Hub. Alcuni ponu esse scaricati attraversu fonti esterne, in quellu casu mostremu ligami à quelli."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Ricerca"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nova ricerca"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Includi sulu"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Escludi"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Non cuntrullatu"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Scarica"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Articuli di ghjurnali"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Prestito Digitale"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadati"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titulu, autore, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Ricerca"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Impostazioni di ricerca"

#, fuzzy
msgid "page.search.submit"
msgstr "Ricerca"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "La ricerca ha pigghiatu troppu tempu, chi è cumuni pi quistioni larghi. Li cuntaggi di li filtri putissi nun èssiri accurati."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "La ricerca ha pigghiatu troppu tempu, chi significa ca putissi vidiri risultati inaccurati. A voti <a %(a_reload)s>ricaricari</a> la pàggina aiuta."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Mostra"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabella"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avanzatu"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Ricerca descrizzioni è cummenti di metadati"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Aggiungi campu di ricerca specificu"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(ricerca campu specificu)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Annu di pubblicazioni"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Contenutu"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Tipu di file"

#, fuzzy
msgid "page.search.more"
msgstr "cchiù…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Accessu"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scraped e open-sourced di AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Lingua"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordina per"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Cchiù rilevanti"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Cchiù novu"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(annu di pubblicazzioni)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Cchiù anticu"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "U cchiù granni"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(filesize)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Cchiù nicu"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(codici apertu)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Casuali"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "L'indiciu di ricerca è aggiornatu ogni misi. Attualmente include voci finu a %(last_data_refresh_date)s. Pi cchiù infurmazzioni tecnichi, vedi la <a %(link_open_tag)s>pàggina di datasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Per esplorare l'indice di ricerca tramite codici, usa il <a %(a_href)s>Esploratore di Codici</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Scrivi ntâ casella pi circari ntô nostru catalogu di %(count)s file scaricabili direttamenti, chi nui <a %(a_preserve)s>presirvamu pi sempri</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Nfatti, chiunqui pò aiutari a presirvari sti file semminannu la nostra <a %(a_torrents)s>lista unificata di torrent</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Attualmente avemu lu catalogu apertu cchiù cumplessu di libri, articuli, e àutri òpiri scritti dû munnu. Rispecchiamu Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e cchiù</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Si truvati àutri \"bibliotechi ombra\" chi avissimu a rispecchiari, o si aviti quarchi dumanna, cuntattatici a %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Pi rivendicazzioni DMCA / diritti d'auturi <a %(a_copyright)s>clicca ccà</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Suggerimentu: usati scorciatoie di tastiera “/” (focus ricerca), “enter” (ricerca), “j” (su), “k” (giu), “<” (pàggina precidenti), “>” (pàggina successiva) pi navigari cchiù veloci."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Circannu articuli?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Scrivi ntâ casella pi circari ntô nostru catalogu di %(count)s articuli accademici e di rivisti, chi nui <a %(a_preserve)s>presirvamu pi sempri</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Scrivi ntâ casella pi circari file ntê bibliotechi di prestitu digitali."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "St'indiciu di ricerca attualmente include metadata dû Controlled Digital Lending library di Internet Archive. <a %(a_datasets)s>Cchiù infurmazzioni supra i nostri datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Pi cchiù bibliotechi di prestitu digitali, vedi <a %(a_wikipedia)s>Wikipedia</a> e la <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Scrivi ntâ casella pi circari metadata ntê bibliotechi. Chistu pò essiri utuli quannu <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "St'indiciu di ricerca attualmente include metadata di vari fonti di metadata. <a %(a_datasets)s>Cchiù infurmazzioni supra i nostri datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Pi metadata, mustramu i registri originali. Nun facemu nenti fusione di registri."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Ci sunnu tanti, tanti fonti di metadata pi òpiri scritti ntô munnu. <a %(a_wikipedia)s>Sta pàggina di Wikipedia</a> è un bonu puntu di partenza, ma si canusci àutri boni listi, faciticcillu sapiri."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Scrivi ntâ casella pi circari."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Chisti sunnu record di metadata, <span %(classname)s>nun</span> file scaricabili."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Errore duranti la ricerca."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Prova a <a %(a_reload)s>ricaricari la pàggina</a>. Si lu prubbrema persisti, cuntattatici a %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nuddu file truvatu.</span> Prova cchiù pochi o diversi termini di ricerca e filtri."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A voti chistu succedi in modu sbagghiatu quannu lu serviziu di ricerca è lentu. Ntô tali casi, <a %(a_attrs)s>ricaricari</a> pò aiutari."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Avemu truvatu currispundenzi in: %(in)s. Pudete riferirivi à l'URL truvatu quannu <a %(a_request)s>richiedite un schedariu</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Artìculi di Ghjurnali (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Prestamu Digitale (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadati (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Risultati %(from)s-%(to)s (%(total)s in tuttu)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ currispundenzi parziali"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d currispunnenzi parziali"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Vuluntariatu & Ricumpensi"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "L'Archiviu di Anna si basa su vuluntari comu vui. Accugliemu tutti i livelli di impegnu, e avemu dui principali categurii di aiutu chi circamu:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Travaghju vuluntariu ligeru:</span> si putiti dedicari sulu qualchi ura ogni tantu, ci sunnu ancora tanti modi in cui putiti aiutari. Ricumpinsamu i vuluntari consistenti cu <span %(bold)s>🤝 abbunamenti a l'Archiviu di Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Travagghiu di vuluntariatu intensu (ricumpensi di USD$50-USD$5,000):</span> si putiti didicari assai tempu e/o risorsi a la nostra missioni, ni piaciissi travagghiari cchiù vicinu cu vui. Eventualmenti putiti junciri a lu gruppu internu. Anchi si avemu un budgetu strittu, putemu attribuiri <span %(bold)s>💰 ricumpensi monetarii</span> pi lu travagghiu cchiù intensu."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si nun putiti vuluntariari lu vostru tempu, putiti ancora aiutari assai <a %(a_donate)s>dunannu dinari</a>, <a %(a_torrents)s>seedannu i nostri torrents</a>, <a %(a_uploading)s>caricannu libri</a>, o <a %(a_help)s>parrannu di l'Archiviu di Anna cu li vostri amici</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Cumpagnii:</span> offriemu accessu direttu ad alta vilucità a li nostri cullezzioni in cambiu di dunazioni a livellu imprisa o in cambiu di novi cullezzioni (p.e. novi scansioni, datasets OCR, arricchennu i nostri dati). <a %(a_contact)s>Cuntattatici</a> si chistu è lu vostru casu. Viditi puru la nostra <a %(a_llm)s>pàggina LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Vuluntariatu leggiu"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si aviti qualchi ura libbera, putiti aiutari in diversi modi. Assicurativi di juncirivi a lu <a %(a_telegram)s>chat di vuluntari su Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Comu segnu di apprezzamentu, di solitu offriemu 6 misi di “Bibliutecariu Fortunatu” pi i traguardi basi, e cchiù pi travagghiu di vuluntariatu cuntinuatu. Tutti i traguardi richiedunu travagghiu di alta qualità — travagghiu scarsu ni fa cchiù dannu ca aiutu e lu rifiutaremu. Pi favuri <a %(a_contact)s>mandatici un'email</a> quannu raggiungiti un traguardu."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Compitu"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Traguardu"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Spartennu la parola di l'Archiviu di Anna. Per esempiu, ricumandannu libri nantu AA, ligannu à i nostri articuli di blog, o in generale dirigennu a ghjente à u nostru situ web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s liami o screenshot."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Quessi duverianu mustrà vi chì fate sapè à qualcunu di l'Archiviu di Anna, è elli chì vi ringrazianu."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Migghiurari i metadata <a %(a_metadata)s>collegannu</a> cu Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Poti usari la <a %(a_list)s >lista di prubbrimi di metadata casuali</a> comu puntu di partenza."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assicurati di lassari un cummentu supra i prubbrimi chi risolvi, accussì l'àutri nun duplicanu u to travagghiu."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s liami di ricanusci ca migliorasti."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traducennu</a> lu situ web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traduci interamenti na lingua (si nun era già vicinu a essiri cumpletata)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Migghiurari la pàggina Wikipedia di l'Archiviu di Anna nella vostra lingua. Includiti infurmazioni da la pàggina Wikipedia di AA in autri lingui, e da lu nostru situ web e blog. Aggiunci rifirenzi a AA in autri pàggini relevanti."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link a la storia di edizioni ca mostra ca aviti fattu cuntribbuti significativi."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Soddisfari richiesti di libri (o articuli, ecc.) su i forum di Z-Library o Library Genesis. Nun avemu un nostru sistemu di richiesti di libri, ma riflettimu chiddi bibliuteci, allura migliuralli fa migliurari puru l'Archiviu di Anna."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s liami o screenshot di richiesti ca soddisfisti."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Picculi compiti postati su lu nostru <a %(a_telegram)s>chat di vuluntari su Telegram</a>. Di solitu pi l'abbunamentu, a voti pi picculi ricumpensi."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Picculi compiti pubblicati ntô nostru gruppu di chat di vuluntari."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dipenni di lu compitu."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Ricumpensi"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Circamu sempri genti cu avissiru boni capacità di prugrammazzioni o di sicurizza offensiva pi participari. Poti fari na granni differenza ntâ prisirvazzioni dû patrimoniu umanu."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Comu ringraziamentu, offriemu abbunamenti pi cuntribbuti sustanziali. Comu granni ringraziamentu, offriemu ricumpensi munetari pi compiti particolariamenti mpurtanti e difficili. Chistu nun avissi a essiri vistu comu na sustituzzioni pi nu travagghiu, ma è nu incentivu extra e pò aiutari cu li spisi sustinuti."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "La maiur parti dû nostru còdici è open source, e dumannaremu ca puru lu vostru còdici sia tali quannu attribuemu la ricumpensa. Ci sunnu certi eccezzioni chi putemu discutiri a livellu individuali."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Li ricumpensi sunnu attribbuiti â prima pirsuna ca completa nu compitu. Sìntiti libbiri di cummentari supra nu bigliettu di ricumpensa pi fari sapiri a l'àutri ca stai travagghiannu supra quarchi cosa, accussì l'àutri ponnu aspittari o cuntattari ti pi fari squadra. Ma stai attenti ca l'àutri sunnu sempri libbiri di travagghiari supra lu stissu compitu e pruvà a battiri ti. Tuttavia, nun attribbuemu ricumpensi pi travagghiu scarsu. Si dui sottomissioni di alta qualità sunnu fatti vicinu a vicinu (dintâ na jurnata o dui), putemu dicidiri di attribbuiri ricumpensi a tutti dui, a nostra discrezzioni, pi esempiu 100%% pi la prima sottomissioni e 50%% pi la secunna sottomissioni (accussì 150%% in tuttu)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Pi li ricumpensi cchiù granni (soprattuttu li ricumpensi di scraping), cuntattatici quannu hai completatu ~5%% di iddu, e sì sicuru ca lu tò metudu si pò scalari pi lu milestone cumpletu. Avrai a cundividiri lu tò metudu cu nuatri accussì putemu dari feedback. Accussì, putemu dicidiri chi fari si ci sunnu cchiù pirsuni ca si stannu avvicinannu a na ricumpensa, comu putenzialmenti attribbuirla a cchiù pirsuni, incuraggiannu li genti a fari squadra, ecc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ATTENZIONI: li compiti cu ricumpensi àuti sunnu <span %(bold)s>difficili</span> — putissi essiri sàggiu accuminzari cu chiddi cchiù facili."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Vai â nostra <a %(a_gitlab)s>lista di prubbrema di Gitlab</a> e urdinati pi “Label priority”. Chistu mustra approssimativamenti l'òrdini di li compiti ca ni nteressanu. Li compiti senza ricumpensi espliciti sunnu ancora eliggìbbili pi l'abbunamentu, soprattuttu chiddi marcati “Accepted” e “Anna’s favorite”. Putissi vuliri accuminzari cu nu “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Aggiornamenti su <a %(wikipedia_annas_archive)s>L'Archiviu di Anna</a>, la più granni libreria veramente aperta nta la storia umana."

#, fuzzy
msgid "layout.index.title"
msgstr "L'Archiviu di Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "La cchiù granni libreria open-source e open-data dû munnu. Specchi di Sci-Hub, Library Genesis, Z-Library, e cchiù."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Circari n L'Archiviu di Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "L'Archiviu di Anna avi bisognu di lu vostru aiutu!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Tanti pruvanu a farini cadiri, ma nni difennemu."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Sè donate avà, riceverete <strong>doppia</strong> a quantità di scaricamenti veloci."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Validu finu à a fine di stu mese."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Dona"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvare a cunniscenza umana: un gran rigalu di vacanza!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Surprendi una persona cara, regala un contu cu abbonamentu."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Pi aumentari a resilienza di l'Archiviu di Anna, circamu vuluntari pi gestiri specchi."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "U rigalu perfettu pi San Valentinu!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Avemu un novu metudu di donazione dispunibule: %(method_name)s. Per piacè cunsidirete di %(donate_link_open_tag)sdonà</a> — ùn hè micca à bon pattu gestisce stu situ web, è a vostra donazione face veramente una differenza. Grazie mille."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Stemu facendu una raccolta di fondi per <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">sustene</a> a più grande biblioteca ombra di fumetti in u mondu. Grazie per u vostru sustegnu! <a href=\"/donate\">Donate.</a> Se ùn pudete micca donà, cunsidirete di susteneci dicendu à i vostri amichi, è seguitenduci nantu à <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Scaricamenti recenti:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Ricerca"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Migliurà i metadati"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariatu & Ricumpense"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrenti"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Attività"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Esploratore di Codici"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Dati LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Home"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "L'Archiviu di Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Traduci ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Accedi / Regìstrati"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Account"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "L'Archiviu di Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Resta in cuntattu"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / rivendicazzioni di copyright"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avanzatu"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sicurezza"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativi"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nun affilatu"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Stu schedariu putissi aviri prublemi."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Scaricamentu veloci"

#, fuzzy
msgid "page.donate.copy"
msgstr "copia"

#, fuzzy
msgid "page.donate.copied"
msgstr "copiatu!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Precedenti"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Succissivu"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "sulu chistu misi!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ha <a %(a_closed)s>suspisu</a> lu caricamentu di novi articuli."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selezziuna un'opzioni di pagamentu. Damu sconti pi pagamenti basati su cripto %(bitcoin_icon)s, pirchì avemu (moltu) menu commissioni."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selezziuna un'opzioni di pagamentu. Attualmente avemu sulu pagamenti basati su cripto %(bitcoin_icon)s, pirchì i processori di pagamentu tradizionali rifiutanu di travagghiari cu nuatri."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Non putemu supportari direttamente carte di crèditu/dèbitu, pirchì i banche non vonnu travagghiari cu nuatri. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tuttavia, ci su diversi modi pi usari carte di crèditu/dèbitu comunque, usannu i nostri àutri metudi di pagamentu:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Scaricamenti lenti e esterni"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Scaricamenti"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si è la prima vota ca usati la cripto, vi cunsigghiamu di usari %(option1)s, %(option2)s, o %(option3)s pi accattari e dunari Bitcoin (la criptovaluta urigginali e cchiù usata)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 collegamenti di registri ca aviti migghiuratu."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 collegamenti o screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 collegamenti o screenshots di richiesti ca aviti soddisfattu."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si sì ntirissatu a rispecchiari sti datasets pi <a %(a_faq)s>archiviazzioni</a> o <a %(a_llm)s>addistramentu LLM</a>, cuntattaci."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si siti ntirissati a mirrurari stu dataset pi <a %(a_archival)s>archiviazzioni</a> o pi <a %(a_llm)s>addistramentu LLM</a>, cuntattàtini."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sito principali"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Nfurmazzioni supra li paesi di l'ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si sì ntirissatu a mirrari stu dataset pi scopi di <a %(a_archival)s>archiviazzioni</a> o <a %(a_llm)s>addistramentu LLM</a>, cuntattaci."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L'Agenzia Internazziunali di l'ISBN rilascia regularmenti li range chi ha allocatu a l'agenzii naziunali di l'ISBN. Di chistu putemu derivari a quali paisi, riggiuni, o gruppu linguìsticu apparteni chistu ISBN. Attualmenti usamu sti dati in modu indiritto, attraversu la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Risorsi"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultima aggiornamentu: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sito web di l'ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadati"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Escludennu “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "A nostra ispirazioni pi raccogliere metadata è l'obbiettivu di Aaron Swartz di “una pagina web pi ogni libru mai pubblicatu”, pi cui iddu criò <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Chistu pruggettu ha fattu beni, ma a nostra pusizioni unica nni permette di ottèniri metadata chi iddi nun ponnu."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "N'autra ispirazioni fu a nostra disidiriu di sapiri <a %(a_blog)s>quanti libri ci sunnu ntô munnu</a>, accussì putemu calculari quanti libri avemu ancora a sarvari."

#~ msgid "page.partner_download.text1"
#~ msgstr "Pi dari a tutti l'uppurtunità di scaricari i schedi pi nenti, aviti a aspittari <strong>%(wait_seconds)s sicunni</strong> prima di putiri scaricari sta schedi."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Rifrisca pàggina autumaticamenti. Si ti perdi la finestra di scaricamentu, lu timer ripartirà, allura si cunsìgghia rifriscari autumaticamenti."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Scarrica ora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Convertiri: usati strumenti online pi convertiri tra formati. Pi esempiu, pi convertiri tra epub e pdf, usati <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: scaricati lu file (pdf o epub sunnu supportati), poi <a %(a_kindle)s>mannallu a Kindle</a> usannu lu web, app, o email. Strumenti utili: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Supportari l'autura: Si vi piaci chistu e putiti, pinsati di accattari l'urigginali, o supportari l'autura direttamenti."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Supportari li librerii: Si chistu è dispunìbbili ntâ vostra libreria lucali, pinsati di pigghiallu in prestitu pi nenti ddà."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nun dispunìbbili direttamenti in bulk, sulu in semi-bulk darreri a un paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s L'Archiviu di Anna gestisci na cullizzioni di <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb è una società che raccoglie dati da vari negozi di libri online per trovare metadati ISBN. L'Archiviu di Anna ha effettuato backup dei metadati dei libri di ISBNdb. Questi metadati sono disponibili tramite l'Archiviu di Anna (anche se attualmente non nella ricerca, a meno che non si cerchi esplicitamente un numero ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Per dettagli tecnici, vedi sotto. A un certo punto possiamo usarlo per determinare quali libri mancano ancora dalle biblioteche ombra, al fine di dare priorità a quali libri trovare e/o scansionare."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Il nostro post sul blog riguardo questi dati"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Raccolta ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Attualmente abbiamo un singolo torrent, che contiene un file gzipped da 4,4GB <a %(a_jsonl)s>JSON Lines</a> (20GB decompresso): \"isbndb_2022_09.jsonl.gz\". Per importare un file \".jsonl\" in PostgreSQL, puoi usare qualcosa come <a %(a_script)s>questo script</a>. Puoi anche pipearlo direttamente usando qualcosa come %(example_code)s in modo che si decomprima al volo."

#~ msgid "page.donate.wait"
#~ msgstr "Per piacè aspettate almenu <span %(span_hours)s>dui uri</span> (è rinfrescate sta pagina) prima di cuntattà ci."

#~ msgid "page.codes.search_archive"
#~ msgstr "Circà l'Archiviu di Anna pi “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Duna usannu Alipay o WeChat. Pò scegliri tra chisti na pàggina successiva."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Diffunnennu la parola di l'Archiviu di Anna su i social media e i forum online, ricumannannu libri o listi su AA, o rispunnennu a dumanni."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La cullizzioni di Fiction si è divisa ma ha ancora <a %(libgenli)s>torrenti</a>, anchi si nun sunnu stati aggiornati di lu 2022 (avemu download diretti)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s L'Archiviu di Anna e Libgen.li gestiscinu cullizzioni di <a %(comics)s>fumetti</a> e <a %(magazines)s>rivisti</a> in cullaburazioni."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nenti torrenti pi cullizzioni di fiction russa e ducumenti standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Nun ci sunnu torrents dispunìbbili pi lu cuntinutu aghiuntivu. Li torrents ca sunnu ntô situ Libgen.li sunnu mirrura di àutri torrents elencati ccà. L'unica eccezzioni sunnu li torrents di narrativa ca accuminciunu a %(fiction_starting_point)s. Li torrents di fumetti e rivisti sunnu rilassati comu na cullabburazzioni tra l'Archiviu di Anna e Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Da na cullezzioni <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> urìggini esatta nun chiara. Parti da the-eye.eu, parti da àutri fonti."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

