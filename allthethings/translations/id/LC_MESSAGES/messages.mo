��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  sd L  �f )   Fh   ph ,  si 
  �k p  �m W  o Q   wp k   �p C   5q f   yq �  �q �  ns �   �t �  �u 
  �w �  �x )  �z �  �|   �~ �  � �  �� 5   =� �   s� R   B� 1  ��    ǆ -  � C   � #   Y�    }�     �� H   ��    �    %� .   8�    g� $   ��    �� =   �� J  �� +  F� c   r� 9   ֍ 
   � 
   �    &� 
   =�    H�    _� 	   l�    v�    �� "   ��    ��    �� 	   ̎ 
   ֎    �    ��    
�    *� W  E� g  �� �   � )   �� R  �� �   �   �� �   Ֆ    �� �   ��    D� �   c� #   �   +�    C� �  b� 0   � c   �    ~� [  �� _  � �  I� c  "�    �� M   �� Z   � �   >� 0   �� -   ,� l   Z� F   Ǥ C   � ,   R�    � {   �� �   � A   �� p   � �   a� D   � *  _� 7   �� "  ª �   � �   v� M   %� �   s� �   D� C   �   )� �   ?� t   �� ?   t� r   ��    '� �   3� *  ɲ C   �� 
   8� �   C� �   �    ܵ �  � �   m�   � �  !� �   �� ;  ��   � H   � B   7� b   z� a   �� _   ?� c   �� .   � �   2� S   �� ?   � 
   \� t   j� _   �� �   ?� �   �    ��   ��   �� X  �� h  � �   �� �   � 2  �� �   � �   �� �   W� p  �� �   b� �   �    � �   � �   �� �   j� k  /� �   �� ~   ,� �   �� �  S� �    �   ��   �� #   �� U   �� �   O� \   �� o  >� �   �� T   _� n   ��    #� *  >� �   i� �  I�    �� e   �� �   U� �   �� 
  �� �   �� �  �� �  �� $   m� y  �� �  � .  �� �  �� 6  �� !  �� \   �� �   <�    �� �   �� E  �� f  �� �   L� $  B� �   g� 
   &� �  1� _  �  �         �    :  � �   � �  � �   � `   �    � �  	 @  �
    � �   � (  � E    �   F e  6 p   � T   
 �   b 5   ]    � F  � E  � A  1 -   s C   � �  � �   � w   u a   � �  O "   �    � �  
 �   � �   N  ?   �  �   ! /   �!   �!   �"    �# 	   $ #   $ /   <$ .   l$ 7   �$ K   �$ k   %    �% &   �% 5   �% B   �% !   :& (   \& +   �& n   �&     '    ''   B' 8  _( �   �) �   �* �   k+ �   �+ j  �,    =. �   S. �   A/ �   �/ M  �0 \  �1 �  <3 �  )5 �  7 C   �8 6   �8 (   9 �   A9    : z  < 1  �= G  �> *  @ '  7A   _C -   D   �D :  �E h  G    nH �   }H :  dI z   �J :  K �   UL    RM $   bM f   �M    �M �  �M �  �P �  +R �   �T �   {U    @V   OV   ^W C   jX r   �X   !Y   2Z �   7[ n  �[ Y   8]   �]    �^ +  �^ �   �` .  ~a �  �c �  9f �   &h 
   �h �   i �  �i 
   �k �  �k �  Rm P  <o 7  �q   �s S  �t C   5v �  yv �   Xx w   Ay =   �y U   �y    Mz 7   Rz %   �z (   �z !   �z    �z �   { 
  �{ 
  �~ T  ƀ �   � 	   � �  � .  � �  0� �  �� �  G� }  ֎ 
   T�   _�    c�    j� �  |� M  2� �  �� k  b� 
   Κ "  ܚ   ��   � .  %� �  T� �  @� �  ٨    t� �   �� �   e� �  �� �  � �   �� �   J� �   ݱ v  ��    � �   (� J   � J   1�    |� /   �� E   �� Z  � �  `� �  � 0   � �  � �   �    �� �   �� >   :� �   y� �   � 8   �� k   �    |� X  �� �  �� �  o� �  ^� p   ��    Z� s   n� :  ��    � �   #� �  �� �   w� 2   /� 7   b� `  �� )   �� �  %� �  �� �  T� !   N� s   p� �   �� Z  j� �  ��    z� �   �� 2   w� �   �� �   `� �   � �   � M   � )   R� �   |� /   |� �  �� L  ]� �   �� %  �� _   �� >   � �   J� 9  �� �  7� �  �� �  �� :   �� �   �� =  �� M  �� �   *� 
  
� �  � )   �� �  �� ,   �� �  
� _   ��    =  q  S  %  � �  � �   y B  s   � 7  �
 �    �  � �  s t   $ a  � ,  � A   ( �   j u   �    m    � 5  � �   � :   { '  � �  �   � O  � �  .! �   �" �  �# >  >%    }& �   �&    Q' �  Z'    �(    )    )    ") 2   ?)    r)    �)    �)    �)    �)    �) 
   �) 
   �) 
   �) "   �)    * -   ,*    Z* 
   `*    k*    p* �   y* U   ;+ %   �+    �+ 1   �+ %   �+ -   ", 4   P, )   �, 
   �,    �,    �,    �,    �,    �,    -    -    -    - +   .- -   Z-    �- 0   �- 4   �- (   
. .   3.    b. (   x. 7   �.    �. X   �. P   B/    �/ ^   �/ @   �/    ;0    W0 !   h0    �0    �0    �0    �0 
   �0    �0 
   �0    
1    1    .1 	   ;1 
   E1    P1    S1    q1    x1 	   �1    �1 	   �1    �1    �1    �1 	   �1    �1    �1    �1    2    2    92    W2    _2 	   n2    x2 (   �2    �2    �2    �2 
   �2    �2    �2    3    $3    *3 $   <3 {   a3 �   �3 m   �4 �   5 �  �5 �   �7    p8    �8    �8    �8 
   �8    �8    �8 $   �8 ^   9 :   c9 _   �9    �9 B   : %   a: W   �: ^   �: �   >; _   < >   ~<    �<    �<    �< 	   �<    �<    =    = 
   -=    8=    H=    P=    W=    d=    h=    p=    �=    �=    �=    �= 	   �=    �=    �=    �=    �=    �=   �=    ? 
   ?    &? "   ,?    O? =   V? \   �? &   �? ,   @ 0   E@    v@    ~@    �@ `   �@    �@    �@ :   �@ c   :A    �A    �A r   �A    3B c  SB R   �E m   
F �   xF �   :G 5   "H H  XH �   �I X  #J �   |K    rL    �L >   �L C   �L Y   M b   nM q   �M M   CN n   �N     O 1    O 	   RO    \O ^   wO !   �O    �O    �O    P '   P �   >P 
   �P (   Q m   4Q    �Q    �Q Q   �Q j   R �  �R    T    (T �   ;T 	   �T    �T    �T    U    U /   "U _  RU    �V    �V    �V    �V M  �V &   FX    mX    }X [   �X    �X    �X +   �X    Y    )Y &   .Y �   UY    �Y    
Z R    Z    sZ    �Z    �Z    �Z 3   �Z Y   �Z    Q[ 4   a[ �   �[ S   K\ D   �\    �\ �   �\ ?   �]    ^ -   )^    W^ �   f^ �   _    �_ >   �_ �   �_ �   �`    Sa #   ia    �a s  �a    
c    -c    Jc    bc    ~c �   �c    3d    Qd ,   hd @   �d    �d    �d    �d "   e _  7e x  �g D  i >   Uk 4   �k    �k "   �k 
  �k �   m �   �m    n �   �n |  �o    �p    q   9q 0  Es    vt    t 5   �t    �t 9  �t    
v _   v �  �w �   y    z l   z *   �z    �z p   �z 4  ;{   p| �   r} �  ~ g   � �   � T   � �   D� �   � Y   �� �   � !   ��    ܃ 
   ��    �    �    $�    B� ;   \� %   �� 	   �� %   Ȅ N  � #   =� �   a� �   "� |   ��    ,�    H�    `�    x� #   ��    ��    ˈ    � �   	� !   މ �    �    ̊ �   ߊ p   �� n   &� #  �� �   �� '   q� �   �� 	   7� :  A� t   |�    � �  �    ��        ْ    � )   �    .�    5� E   <� �   �� �   5�    2�    ;�    A� �   W� 7  � �   D� �    �    ��    ��    ��    Ø    ט    �    �� F   � +   J� �  v� j   c�    Λ h   � k   K� A   �� r   �� Q   l� E   ��    � ]   � ;   i� �   �� P   )� ?   z�    �� �   Ο d   �� 9   � \   M� K   �� 5   ��    ,� 3   5� v   i� �   � 7   }� �   ��    e� 3  l� M   �� R   � �   A�    Ӧ   ܦ �   ި    ̩    ߩ    ��     � )  � -   /� \   ]� �   �� �   �� �   n� �   �� �   ܮ �   y� �   $� O   հ #  %� b   I� j  �� �   � �   δ 
   �� 
   �� 
   Ƶ �   Ե 
   a� 
   o� G   }� X   Ŷ �   � 
   �� l   	� .   v� ~   �� :   $� n   _� `   ι 
   /� �   =� 
   Ⱥ 
   ֺ 
   �   � s   � �  z�    �    +� 
   3� �   >�    ٿ "   � �   � �   ��    ��    ��    �� +   �� .   � +   0�    \�    y� �   ��    f� �  �� �   /� z   � M   �� �   �� �   }� �  x� $  C� �   h� �   �� u   ��    �� �  �    �� r  �� �   � 3  �� �   #� 0  �� �   �� �   �� r   � &   �� D   ��    �� #   � 
   '�    2�    B� �   X�    I� w   R� !   ��    �� 	   ��    ��    � v   %� F   �� >   ��   "� .   9�    h�    w�    }� #   ��    ��    ��    ��    ��    ��    ��    ��     � *   	� �   4�    ��    ��    ��    ��    �    �    -�    <�    K�    d� ,   q� m   ��    � E   � m   a� �   �� �   {� �   3� ^  �� �   L� ]  �    y� �   �� 6   (� H   _� H   �� �   �� �   �� S   �� c   ��    L� �   ^� i   �� }   Y�    ��     ��    ��    � 
   (� !   3�    U�    ]�    }�    ��    ��    ��    ��    ��    ��    �    �    .�    ;�    C�    ^�    e� *   �� y   �� �   *� .   �� &   �� w   
� [   �� r   �� &   T� -   {� .   �� ;   �� C   � ,   X� �   ��   ^� !  c� "   �� W   �� �    � 2   �� �   �� �   �� �   �� N   7�    �� �   �� �   W� t   �� 5   r� t   �� �   �     � ,   8�    e� E   �� ~   �� n   F�    ��    ��    �� *   �� �   �� 6   ~�    �� 6   �� !   �    -�    L� C   l�     �� b   �� <   4� �   q� W   i� �   �� �   d  ?   
 #   J "   n #   � "   � "   � "   � "    =   A 3    �  � >   s :   � A   � $   /    T o   [ �   � �   P �       	 �   
	 #   �	    �	 �   �	    �
 '   �
 =   �
 .   7 W   f X   � I       a    { �   � 1   L
    ~
 U   �
 �   �
 =   � �   ' m   � c   - �   �     0   2 z   c    � b   �    Y �   w 3   
 B   A    � 5   � �   � @   � X   � �   J k   � M   8 �   � �       �    � Q   � @       O !   c    �    �    � -   � �   � i   q    �     � %    "   A *   d �   � 8   ' �   ` \   � (   ? f   h �   � 4   � Q   �    9 E   I L   �    � {   � |   i 0   � R        j  .   w  Q   �     �  o   	! (   y!    �! "   �! K   �! �   '" @   �"    ,#    C# @   Z#    �# R   �# 6   $ �   <$ r   �$    c% A   s% �   �%    �& �   �& E   P'    �' T   �' �   (    �(    )    	)    ) 0   () /   Y) 8   �)    �)    �)    �) "   �) ;   * 9   Y* 
   �* C   �* D   �*    '+    7+ :   V+    �+    �+ r   �+ �   <, P   *-    {-    �- �   �- P  T. Q   �/    �/ q  0 �  �1 0   ;3 j   l3    �3 d  �3 ,   S6 r   �6    �6    7   ,7 "   G9 U   j9 g   �9 j   (: h   �: &   �: ]   #; ;   �; "   �; p   �; G   Q< /   �< U   �< 7   = U   W= �   �= �   ;> 3   �> �   ? k  �? �   1A A   �A |  )B �   �C �   �D .  sE �   �F %   lG    �G �   �G ,   EH �  rH ^   dJ N   �J    K    #K �  AK    M �   M 4  �M $  O "  *P J   MQ H   �Q Z   �Q 1   <R :   nR ^   �R Z   S    cS *   pS ;   �S *   �S    T 8   T `   NT ,   �T    �T p   �T   SU �   _V    W    W    +W ;   0W X   lW T   �W �   X l   �X    hY    zY �   �Y    WZ �   cZ �  [   �] P   �^ /   _    2_    8_    =_ I   A_ )   �_ �   �_   E` j   La    �a    �a $   �a    b T   #b    xb >   �b    �b )   �b *   �b     c    /c V   7c    �c    �c &   �c    �c    �c n   �c �   hd `   *e p   �e W   �e �   Tf    $g    -g �   Fg �   �g �   �h 	   mi �   wi >   �i ?   8j l   xj y   �j Z   _k    �k O   �k    l    *l    ?l    Pl    dl    yl    �l    �l    �l 	   �l #   �l    �l %   m    <m (   [m (   �m    �m "   �m    �m >   �m "   =n    `n 3   hn .   �n h   �n 2   4o    go    ~o    �o    �o    �o �   �o R   ep �   �p �   Fq    r    r $   /r    Tr 5   jr 	   �r    �r    �r p   �r    ?s #   _s    �s 
   �s 	   �s :   �s !   �s �   �s    �t    �t    �t !   	u    +u 
   Iu    Wu     vu !   �u -   �u ,   �u    v \   v .   xv    �v    �v 9   �v G   w %   Pw 0   vw    �w {   �w d   Cx H   �x    �x    �x 	   y    y    +y    Ey v  ay �   �z    _{    q{ (   u{    �{ '   �{    �{    �{ {   �{ @   e| �   �|    u} "   �} �   �} "   1~    T~ !   s~ $   �~ 5   �~ $   �~        2 5   B    x ,   � 8   � )  � <   '� N   d� G   ��    �� �   �    �� 
   ق Q   �    9� !   K�    m� 9   �� <   �� !   �� �   �    ׄ 
   �    ��    �    4�    I�    c�    ��    �� D   ��   ��    �� !   � �   >� �   �    ǈ    ވ    ��    
�    �    0�    C�    \�    q�    ��    ��    �� 
   ��    ĉ    Ӊ    �    �    �� %   � �   4�   � �  � 0  �� �  � �   �� u  q�    �   ��    ��   
� �   � �  �� �   �� l  V� 6   Ú �   �� 9   ��    �� H   �� O   C� l   �� c    � �   d� �   � �   ʞ x  ��    � �   +� �   � l   ٣ �   F� 
   � �    � [  � �   D� �   �    ܨ W   � `   <� �   �� �   �� w   5� �   ��    7�    I�    i� ?   ~� /   ��    � �   �� =   �� w   ѭ    I� �   _� �   � U   j� W   �� S   � X   l� Y   Ű O   � �   o� _   �� �   X� z   �    d� )   k� *   �� X   �� 1   �    K�    S� B   Z�    ��    ��    ϴ ?   ִ I   � @   `�    ��    ��    ĵ    ̵ 	   ҵ G   ܵ v   $� O   �� 4   �     � 0   (� $   Y�    ~�    ��    ��    �� 	   ��    ��    �� 
   ��    ˷    ҷ    �    �    ��    �    �    (�    7�    ?�    D�    M�    T� !   o�     ��    ��    2� ^   M� �   �� 
   {�    ��    ��    �� 
   ��    �� 
   ĺ �   Ϻ �   n� @   �    2�    >� d   Z� 
   ��    ͼ �   M�    �    � �   � �   �� j   m� �   ؿ u   �� '   � �   3�    ��    �� J   �� �   @� 8   �� �   '� �   �� ~   f� V   ��    <�    K�    P�    `�    h�    w�    ��    �� �   �� i   6� �   �� �   :� 
  � U    � V   v� �  �� q  ��   � �   �   �� P  ��    !� �   (� [  �� �   8� �  �� �  ��    �� R  /�    �� K   �� �   �� �  �� G   � �   ]� 
   Z�    e�    k� �   �� 3   � x   I� 1   �� Y   �� U   N� m   �� &   � �   9� A   �� /   )� P   Y� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: id
Language-Team: id <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library adalah perpustakaan yang populer (dan ilegal). Mereka telah mengambil koleksi Library Genesis dan membuatnya mudah dicari. Selain itu, mereka menjadi sangat efektif dalam meminta kontribusi buku baru, dengan memberi insentif kepada pengguna yang berkontribusi dengan berbagai keuntungan. Saat ini mereka tidak mengembalikan buku-buku baru ini ke Library Genesis. Dan tidak seperti Library Genesis, mereka tidak membuat koleksi mereka mudah dicerminkan, yang mencegah pelestarian yang luas. Ini penting untuk model bisnis mereka, karena mereka mengenakan biaya untuk mengakses koleksi mereka secara massal (lebih dari 10 buku per hari). Kami tidak membuat penilaian moral tentang memungut biaya untuk akses massal ke koleksi buku ilegal. Tidak diragukan lagi bahwa Z-Library telah berhasil memperluas akses ke pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bagian kami: memastikan pelestarian jangka panjang dari koleksi pribadi ini. - Anna dan tim (<a %(reddit)s>Reddit</a>) Dalam rilis asli Cermin Perpustakaan Bajak Laut (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>), kami membuat cermin dari Z-Library, koleksi buku ilegal yang besar. Sebagai pengingat, inilah yang kami tulis dalam postingan blog asli itu: Koleksi tersebut berasal dari pertengahan 2021. Sementara itu, Z-Library telah berkembang dengan kecepatan yang mencengangkan: mereka telah menambahkan sekitar 3,8 juta buku baru. Memang ada beberapa duplikat di sana, tetapi sebagian besar tampaknya adalah buku baru yang sah, atau pemindaian berkualitas lebih tinggi dari buku yang sebelumnya diserahkan. Ini sebagian besar karena peningkatan jumlah moderator sukarelawan di Z-Library, dan sistem unggahan massal mereka dengan deduplikasi. Kami ingin mengucapkan selamat kepada mereka atas pencapaian ini. Kami dengan senang hati mengumumkan bahwa kami telah mendapatkan semua buku yang ditambahkan ke Z-Library antara mirror terakhir kami dan Agustus 2022. Kami juga telah kembali dan mengumpulkan beberapa buku yang kami lewatkan pada putaran pertama. Secara keseluruhan, koleksi baru ini sekitar 24TB, yang jauh lebih besar dari yang terakhir (7TB). Mirror kami sekarang berjumlah 31TB secara total. Sekali lagi, kami melakukan deduplikasi terhadap Library Genesis, karena sudah ada torrent yang tersedia untuk koleksi tersebut. Silakan kunjungi Pirate Library Mirror untuk melihat koleksi baru (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>). Ada lebih banyak informasi di sana tentang bagaimana file-file tersebut disusun, dan apa yang telah berubah sejak terakhir kali. Kami tidak akan menautkannya dari sini, karena ini hanya situs blog yang tidak menyimpan materi ilegal. Tentu saja, seeding juga merupakan cara yang bagus untuk membantu kami. Terima kasih kepada semua orang yang telah melakukan seeding pada set torrent kami sebelumnya. Kami berterima kasih atas tanggapan positifnya, dan senang bahwa ada begitu banyak orang yang peduli dengan pelestarian pengetahuan dan budaya dengan cara yang tidak biasa ini. 3x buku baru ditambahkan ke Cermin Perpustakaan Bajak Laut (+24TB, 3,8 juta buku) Baca artikel pendamping oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a> - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artikel pendamping oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a> Belum lama ini, "perpustakaan bayangan" hampir punah. Sci-Hub, arsip ilegal besar-besaran dari makalah akademis, berhenti menerima karya baru karena tuntutan hukum. "Z-Library", perpustakaan ilegal terbesar dari buku-buku, melihat para penciptanya yang diduga ditangkap atas tuduhan pelanggaran hak cipta. Mereka berhasil melarikan diri dari penangkapan, tetapi perpustakaan mereka tetap terancam. Beberapa negara sudah melakukan versi ini. TorrentFreak <a %(torrentfreak)s>melaporkan</a> bahwa Tiongkok dan Jepang telah memperkenalkan pengecualian AI dalam undang-undang hak cipta mereka. Tidak jelas bagi kami bagaimana ini berinteraksi dengan perjanjian internasional, tetapi ini tentu memberikan perlindungan bagi perusahaan domestik mereka, yang menjelaskan apa yang telah kami lihat. Adapun Arsip Anna — kami akan melanjutkan pekerjaan bawah tanah kami yang berakar pada keyakinan moral. Namun keinginan terbesar kami adalah untuk muncul ke permukaan, dan memperkuat dampak kami secara legal. Mohon reformasi hak cipta. Ketika Z-Library menghadapi penutupan, saya sudah mencadangkan seluruh perpustakaannya dan mencari platform untuk menampungnya. Itulah motivasi saya untuk memulai Arsip Anna: melanjutkan misi dari inisiatif sebelumnya. Sejak itu, kami telah berkembang menjadi perpustakaan bayangan terbesar di dunia, menampung lebih dari 140 juta teks berhak cipta dalam berbagai format — buku, makalah akademis, majalah, surat kabar, dan lainnya. Tim saya dan saya adalah ideolog. Kami percaya bahwa melestarikan dan menampung file-file ini adalah tindakan yang benar secara moral. Perpustakaan di seluruh dunia mengalami pemotongan dana, dan kita juga tidak bisa mempercayakan warisan umat manusia kepada korporasi. Kemudian datanglah AI. Hampir semua perusahaan besar yang membangun LLM menghubungi kami untuk melatih data mereka. Sebagian besar (tapi tidak semua!) perusahaan yang berbasis di AS mempertimbangkan kembali setelah menyadari sifat ilegal dari pekerjaan kami. Sebaliknya, perusahaan-perusahaan Tiongkok dengan antusias menerima koleksi kami, tampaknya tidak terganggu oleh legalitasnya. Ini patut dicatat mengingat peran Tiongkok sebagai penandatangan hampir semua perjanjian hak cipta internasional utama. Kami telah memberikan akses berkecepatan tinggi kepada sekitar 30 perusahaan. Sebagian besar dari mereka adalah perusahaan LLM, dan beberapa adalah pialang data, yang akan menjual kembali koleksi kami. Sebagian besar adalah perusahaan Tiongkok, meskipun kami juga bekerja dengan perusahaan dari AS, Eropa, Rusia, Korea Selatan, dan Jepang. DeepSeek <a %(arxiv)s>mengakui</a> bahwa versi sebelumnya dilatih menggunakan sebagian dari koleksi kami, meskipun mereka enggan berbicara tentang model terbaru mereka (mungkin juga dilatih menggunakan data kami). Jika Barat ingin tetap unggul dalam perlombaan LLM, dan pada akhirnya, AGI, mereka perlu mempertimbangkan kembali posisinya tentang hak cipta, dan segera. Apakah Anda setuju dengan kami atau tidak tentang kasus moral kami, ini sekarang menjadi masalah ekonomi, dan bahkan keamanan nasional. Semua blok kekuatan sedang membangun super-ilmuwan, super-peretas, dan super-militer buatan. Kebebasan informasi menjadi masalah kelangsungan hidup bagi negara-negara ini — bahkan masalah keamanan nasional. Tim kami berasal dari seluruh dunia, dan kami tidak memiliki afiliasi tertentu. Namun, kami mendorong negara-negara dengan undang-undang hak cipta yang kuat untuk menggunakan ancaman eksistensial ini untuk mereformasi mereka. Jadi, apa yang harus dilakukan? Rekomendasi pertama kami sederhana: perpendek masa berlaku hak cipta. Di AS, hak cipta diberikan selama 70 tahun setelah kematian penulis. Ini tidak masuk akal. Kita bisa menyamakannya dengan paten, yang diberikan selama 20 tahun setelah pengajuan. Ini seharusnya lebih dari cukup waktu bagi penulis buku, makalah, musik, seni, dan karya kreatif lainnya, untuk mendapatkan kompensasi penuh atas usaha mereka (termasuk proyek jangka panjang seperti adaptasi film). Kemudian, setidaknya, pembuat kebijakan harus memasukkan pengecualian untuk pelestarian massal dan penyebaran teks. Jika kehilangan pendapatan dari pelanggan individu adalah kekhawatiran utama, distribusi pada tingkat pribadi dapat tetap dilarang. Sebagai gantinya, mereka yang mampu mengelola repositori besar — perusahaan yang melatih LLM, bersama dengan perpustakaan dan arsip lainnya — akan tercakup oleh pengecualian ini. Reformasi hak cipta diperlukan demi keamanan nasional Ringkasan: LLM Tiongkok (termasuk DeepSeek) dilatih menggunakan arsip ilegal buku dan makalah saya — yang terbesar di dunia. Barat perlu merombak undang-undang hak cipta sebagai masalah keamanan nasional. Silakan lihat <a %(all_isbns)s>posting blog asli</a> untuk informasi lebih lanjut. Kami mengeluarkan tantangan untuk meningkatkan ini. Kami akan memberikan hadiah pertama sebesar $6,000, tempat kedua sebesar $3,000, dan tempat ketiga sebesar $1,000. Karena tanggapan yang luar biasa dan kiriman yang luar biasa, kami memutuskan untuk sedikit meningkatkan total hadiah, dan memberikan hadiah tempat ketiga kepada empat pemenang masing-masing sebesar $500. Para pemenang ada di bawah ini, tetapi pastikan untuk melihat semua kiriman <a %(annas_archive)s>di sini</a>, atau unduh <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Tempat pertama $6,000: phiresky <a %(phiresky_github)s>Kiriman</a> ini (<a %(annas_archive_note_2951)s>komentar Gitlab</a>) adalah semua yang kami inginkan, dan lebih! Kami sangat menyukai opsi visualisasi yang sangat fleksibel (bahkan mendukung shader kustom), tetapi dengan daftar preset yang komprehensif. Kami juga menyukai betapa cepat dan halusnya semuanya, implementasi yang sederhana (yang bahkan tidak memiliki backend), minimap yang cerdas, dan penjelasan yang luas dalam <a %(phiresky_github)s>posting blog</a> mereka. Pekerjaan yang luar biasa, dan pemenang yang sangat pantas! - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Hati kami penuh dengan rasa syukur. Ide-ide yang patut diperhatikan Pencakar langit untuk kelangkaan Banyak slider untuk membandingkan datasets, seolah-olah Anda seorang DJ. Bar skala dengan jumlah buku. Label yang cantik. Skema warna default yang keren dan peta panas. Tampilan peta unik dan filter Anotasi, dan juga statistik langsung Statistik langsung Beberapa ide dan implementasi lainnya yang sangat kami sukai: Kami bisa terus berlanjut untuk sementara waktu, tetapi mari berhenti di sini. Pastikan untuk melihat semua kiriman <a %(annas_archive)s>di sini</a>, atau unduh <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Begitu banyak kiriman, dan masing-masing membawa perspektif unik, baik dalam UI maupun implementasi. Kami setidaknya akan menggabungkan kiriman juara pertama ke dalam situs web utama kami, dan mungkin beberapa lainnya. Kami juga mulai memikirkan cara mengatur proses mengidentifikasi, mengonfirmasi, dan kemudian mengarsipkan buku-buku paling langka. Lebih banyak lagi yang akan datang di bidang ini. Terima kasih kepada semua yang berpartisipasi. Sangat menakjubkan bahwa begitu banyak orang peduli. Penggantian datasets yang mudah untuk perbandingan cepat. Semua ISBN SSNO CADAL Kebocoran data CERLALC SSID DuXiu Indeks eBook EBSCOhost Google Books Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Berkas di Arsip Anna Nexus/STC OCLC/Worldcat OpenLibrary Perpustakaan Negara Rusia Perpustakaan Kekaisaran Trantor Tempat kedua $3,000: hypha “Walaupun persegi dan persegi panjang sempurna secara matematis menyenangkan, mereka tidak memberikan superioritas lokalitas dalam konteks pemetaan. Saya percaya asimetri yang melekat dalam Hilbert atau Morton klasik ini bukanlah cacat tetapi fitur. Seperti garis besar berbentuk sepatu bot Italia yang terkenal membuatnya langsung dikenali di peta, "keanehan" unik dari kurva ini dapat berfungsi sebagai landmark kognitif. Keunikan ini dapat meningkatkan memori spasial dan membantu pengguna mengorientasikan diri, yang berpotensi memudahkan menemukan wilayah tertentu atau memperhatikan pola.” <a %(annas_archive_note_2913)s>Kiriman</a> lain yang luar biasa. Tidak sefleksibel tempat pertama, tetapi kami sebenarnya lebih menyukai visualisasi tingkat makronya dibandingkan tempat pertama (kurva pengisi ruang, batas, pelabelan, penyorotan, panning, dan zooming). Sebuah <a %(annas_archive_note_2971)s>komentar</a> oleh Joe Davis beresonansi dengan kami: Dan masih banyak opsi untuk memvisualisasikan dan merender, serta antarmuka pengguna yang sangat halus dan intuitif. Tempat kedua yang solid! - Anna dan tim (<a %(reddit)s>Reddit</a>) Beberapa bulan yang lalu kami mengumumkan <a %(all_isbns)s>hadiah $10,000</a> untuk membuat visualisasi terbaik dari data kami yang menunjukkan ruang ISBN. Kami menekankan untuk menunjukkan file mana yang sudah/tidak kami arsipkan, dan kemudian dataset yang menggambarkan berapa banyak perpustakaan yang memiliki ISBN (ukuran kelangkaan). Kami sangat terkesan dengan tanggapannya. Ada begitu banyak kreativitas. Terima kasih banyak kepada semua yang telah berpartisipasi: energi dan antusiasme Anda menular! Pada akhirnya, kami ingin menjawab pertanyaan-pertanyaan berikut: <strong>buku apa saja yang ada di dunia, berapa banyak yang sudah kami arsipkan, dan buku mana yang harus kami fokuskan selanjutnya?</strong> Senang melihat begitu banyak orang peduli dengan pertanyaan-pertanyaan ini. Kami memulai dengan visualisasi dasar kami sendiri. Dalam kurang dari 300kb, gambar ini secara ringkas mewakili "daftar buku" terbuka terbesar yang pernah disusun dalam sejarah umat manusia: Tempat ketiga $500 #1: maxlion Dalam <a %(annas_archive_note_2940)s>kiriman</a> ini kami sangat menyukai berbagai jenis tampilan, khususnya tampilan perbandingan dan penerbit. Tempat ketiga $500 #2: abetusk Meskipun antarmuka pengguna tidak paling halus, <a %(annas_archive_note_2917)s>kiriman</a> ini mencentang banyak kotak. Kami sangat menyukai fitur perbandingannya. Tempat ketiga $500 #3: conundrumer0 Seperti tempat pertama, <a %(annas_archive_note_2975)s>kiriman</a> ini mengesankan kami dengan fleksibilitasnya. Pada akhirnya inilah yang membuat alat visualisasi hebat: fleksibilitas maksimal untuk pengguna tingkat lanjut, sambil menjaga kesederhanaan untuk pengguna rata-rata. Tempat ketiga $500 #4: charelf <a %(annas_archive_note_2947)s>Kiriman</a> terakhir yang mendapatkan hadiah cukup dasar, tetapi memiliki beberapa fitur unik yang sangat kami sukai. Kami menyukai bagaimana mereka menunjukkan berapa banyak datasets yang mencakup ISBN tertentu sebagai ukuran popularitas/keandalan. Kami juga sangat menyukai kesederhanaan tetapi efektivitas menggunakan penggeser opasitas untuk perbandingan. Pemenang hadiah visualisasi ISBN senilai $10,000 Ringkasan: Kami menerima beberapa kiriman luar biasa untuk hadiah visualisasi ISBN senilai $10,000. Latar Belakang Bagaimana Arsip Anna dapat mencapai misinya untuk mencadangkan semua pengetahuan umat manusia, tanpa mengetahui buku mana yang masih ada di luar sana? Kami memerlukan daftar TODO. Salah satu cara untuk memetakan ini adalah melalui nomor ISBN, yang sejak tahun 1970-an telah diberikan kepada setiap buku yang diterbitkan (di sebagian besar negara). Tidak ada otoritas pusat yang mengetahui semua penugasan ISBN. Sebaliknya, ini adalah sistem terdistribusi, di mana negara-negara mendapatkan rentang nomor, yang kemudian memberikan rentang yang lebih kecil kepada penerbit besar, yang mungkin membagi lebih lanjut rentang tersebut kepada penerbit kecil. Akhirnya, nomor individu diberikan kepada buku. Kami mulai memetakan ISBN <a %(blog)s>dua tahun lalu</a> dengan pengambilan data dari ISBNdb. Sejak itu, kami telah mengambil data dari banyak sumber metadata lainnya, seperti <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, dan lainnya. Daftar lengkap dapat ditemukan di halaman “Datasets” dan “Torrents” di Arsip Anna. Kami sekarang memiliki koleksi metadata buku (dan dengan demikian ISBN) yang sepenuhnya terbuka dan mudah diunduh terbesar di dunia. Kami telah <a %(blog)s>menulis secara ekstensif</a> tentang mengapa kami peduli dengan pelestarian, dan mengapa kami saat ini berada dalam jendela kritis. Kami harus sekarang mengidentifikasi buku-buku langka, kurang diperhatikan, dan yang uniknya berisiko, dan melestarikannya. Memiliki metadata yang baik pada semua buku di dunia membantu dalam hal itu. Hadiah $10,000 Pertimbangan kuat akan diberikan pada kegunaan dan seberapa baik tampilannya. Tampilkan metadata aktual untuk ISBN individu saat memperbesar, seperti judul dan penulis. Kurva pengisian ruang yang lebih baik. Misalnya, zig-zag, dari 0 ke 4 pada baris pertama dan kemudian kembali (secara terbalik) dari 5 ke 9 pada baris kedua — diterapkan secara rekursif. Skema warna yang berbeda atau dapat disesuaikan. Tampilan khusus untuk membandingkan datasets. Cara untuk memecahkan masalah, seperti metadata lain yang tidak sesuai (misalnya judul yang sangat berbeda). Memberi anotasi pada gambar dengan komentar tentang ISBN atau rentang. Heuristik apa pun untuk mengidentifikasi buku langka atau berisiko. Ide kreatif apa pun yang bisa Anda pikirkan! Kode Kode untuk menghasilkan gambar-gambar ini, serta contoh lainnya, dapat ditemukan di <a %(annas_archive)s>direktori ini</a>. Kami membuat format data yang ringkas, dengan semua informasi ISBN yang diperlukan sekitar 75MB (terkompresi). Deskripsi format data dan kode untuk menghasilkannya dapat ditemukan <a %(annas_archive_l1244_1319)s>di sini</a>. Untuk hadiah, Anda tidak diharuskan menggunakan ini, tetapi ini mungkin format yang paling nyaman untuk memulai. Anda dapat mengubah metadata kami sesuka Anda (meskipun semua kode Anda harus open source). Kami tidak sabar untuk melihat apa yang Anda buat. Semoga sukses! Fork repo ini, dan edit HTML posting blog ini (tidak ada backend lain selain backend Flask kami yang diizinkan). Buat gambar di atas dapat di-zoom dengan mulus, sehingga Anda dapat memperbesar hingga ISBN individu. Mengklik ISBN harus membawa Anda ke halaman metadata atau pencarian di Arsip Anna. Anda harus tetap dapat beralih di antara semua dataset yang berbeda. Rentang negara dan rentang penerbit harus disorot saat di-hover. Anda dapat menggunakan misalnya <a %(github_xlcnd_isbnlib)s>data4info.py di isbnlib</a> untuk info negara, dan pengambilan data “isbngrp” kami untuk penerbit (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Ini harus berfungsi dengan baik di desktop dan seluler. Ada banyak yang bisa dieksplorasi di sini, jadi kami mengumumkan hadiah untuk meningkatkan visualisasi di atas. Tidak seperti kebanyakan hadiah kami, yang satu ini memiliki batas waktu. Anda harus <a %(annas_archive)s>mengirimkan</a> kode sumber terbuka Anda sebelum 2025-01-31 (23:59 UTC). Pengiriman terbaik akan mendapatkan $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Semua hadiah akan diberikan menggunakan Monero (XMR). Di bawah ini adalah kriteria minimal. Jika tidak ada pengiriman yang memenuhi kriteria, kami mungkin masih memberikan beberapa hadiah, tetapi itu akan menjadi kebijakan kami. Untuk poin bonus (ini hanya ide — biarkan kreativitas Anda mengalir bebas): Anda BOLEH sepenuhnya menyimpang dari kriteria minimal, dan melakukan visualisasi yang benar-benar berbeda. Jika itu benar-benar spektakuler, maka itu memenuhi syarat untuk hadiah, tetapi atas kebijakan kami. Buat pengajuan dengan memposting komentar ke <a %(annas_archive)s>masalah ini</a> dengan tautan ke repo yang Anda fork, permintaan penggabungan, atau perbedaan. - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Gambar ini berukuran 1000×800 piksel. Setiap piksel mewakili 2.500 ISBN. Jika kami memiliki file untuk sebuah ISBN, kami membuat piksel tersebut lebih hijau. Jika kami tahu sebuah ISBN telah diterbitkan, tetapi kami tidak memiliki file yang cocok, kami membuatnya lebih merah. Dalam kurang dari 300kb, gambar ini secara ringkas mewakili "daftar buku" terbesar yang sepenuhnya terbuka yang pernah disusun dalam sejarah umat manusia (beberapa ratus GB dikompresi penuh). Ini juga menunjukkan: masih banyak pekerjaan yang harus dilakukan dalam mencadangkan buku (kami hanya memiliki 16%). Memvisualisasikan Semua ISBN — hadiah $10,000 pada 2025-01-31 Gambar ini mewakili "daftar buku" terbesar yang sepenuhnya terbuka yang pernah disusun dalam sejarah umat manusia. Visualisasi Selain gambar ikhtisar, kita juga dapat melihat dataset individu yang telah kita peroleh. Gunakan dropdown dan tombol untuk beralih di antara mereka. Ada banyak pola menarik yang dapat dilihat dalam gambar-gambar ini. Mengapa ada keteraturan garis dan blok, yang tampaknya terjadi pada skala yang berbeda? Apa area kosong itu? Mengapa dataset tertentu begitu terkumpul? Kami akan meninggalkan pertanyaan-pertanyaan ini sebagai latihan bagi pembaca. - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Kesimpulan Dengan standar ini, kita dapat membuat rilis lebih bertahap, dan lebih mudah menambahkan sumber data baru. Kami sudah memiliki beberapa rilis menarik dalam pipeline! Kami juga berharap akan lebih mudah bagi perpustakaan bayangan lainnya untuk mencerminkan koleksi kami. Bagaimanapun, tujuan kami adalah untuk melestarikan pengetahuan dan budaya manusia selamanya, jadi semakin banyak redundansi semakin baik. Contoh Mari kita lihat rilis Z-Library terbaru kami sebagai contoh. Ini terdiri dari dua koleksi: “<span style="background: #fffaa3">zlib3_records</span>” dan “<span style="background: #ffd6fe">zlib3_files</span>”. Ini memungkinkan kami untuk secara terpisah mengikis dan merilis catatan metadata dari file buku yang sebenarnya. Oleh karena itu, kami merilis dua torrent dengan file metadata: Kami juga merilis sejumlah torrent dengan folder data biner, tetapi hanya untuk koleksi “<span style="background: #ffd6fe">zlib3_files</span>”, total 62: Dengan menjalankan <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kita dapat melihat apa yang ada di dalamnya: Dalam hal ini, ini adalah metadata buku seperti yang dilaporkan oleh Z-Library. Pada tingkat atas, kami hanya memiliki “aacid” dan “metadata”, tetapi tidak ada “data_folder”, karena tidak ada data biner yang sesuai. AACID berisi “22430000” sebagai ID utama, yang dapat kita lihat diambil dari “zlibrary_id”. Kita dapat mengharapkan AAC lain dalam koleksi ini memiliki struktur yang sama. Sekarang mari kita jalankan <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Ini adalah metadata AAC yang jauh lebih kecil, meskipun sebagian besar AAC ini terletak di tempat lain dalam file biner! Bagaimanapun, kali ini kita memiliki “data_folder”, jadi kita dapat mengharapkan data biner yang sesuai berada di <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” berisi “zlibrary_id”, jadi kita dapat dengan mudah mengaitkannya dengan AAC yang sesuai dalam koleksi “zlib_records”. Kita bisa mengaitkannya dengan berbagai cara, misalnya melalui AACID — standar tidak menetapkan itu. Perhatikan bahwa tidak perlu juga untuk bidang “metadata” itu sendiri menjadi JSON. Itu bisa berupa string yang berisi XML atau format data lainnya. Anda bahkan dapat menyimpan informasi metadata dalam blob biner terkait, misalnya jika itu adalah banyak data. File dan metadata yang heterogen, sedekat mungkin dengan format aslinya. Data biner dapat disajikan langsung oleh server web seperti Nginx. Pengidentifikasi yang heterogen di perpustakaan sumber, atau bahkan tidak adanya pengidentifikasi. Rilis terpisah dari metadata vs data file, atau rilis metadata saja (misalnya rilis ISBNdb kami). Distribusi melalui torrent, meskipun dengan kemungkinan metode distribusi lain (misalnya IPFS). Catatan yang tidak dapat diubah, karena kami harus mengasumsikan torrent kami akan hidup selamanya. Rilis bertahap / rilis yang dapat ditambahkan. Dapat dibaca dan ditulis oleh mesin, dengan mudah dan cepat, terutama untuk tumpukan kami (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspeksi manusia yang agak mudah, meskipun ini sekunder terhadap keterbacaan mesin. Mudah untuk menanam koleksi kami dengan seedbox sewaan standar. Tujuan desain Kami tidak peduli tentang file yang mudah dinavigasi secara manual di disk, atau dapat dicari tanpa pemrosesan awal. Kami tidak peduli tentang kompatibilitas langsung dengan perangkat lunak perpustakaan yang ada. Meskipun seharusnya mudah bagi siapa saja untuk menanam koleksi kami menggunakan torrent, kami tidak mengharapkan file tersebut dapat digunakan tanpa pengetahuan teknis dan komitmen yang signifikan. Kasus penggunaan utama kami adalah distribusi file dan metadata terkait dari berbagai koleksi yang ada. Pertimbangan terpenting kami adalah: Beberapa tujuan non-tujuan: Karena Arsip Anna bersifat open source, kami ingin menggunakan format kami secara langsung. Ketika kami memperbarui indeks pencarian kami, kami hanya mengakses jalur yang tersedia untuk umum, sehingga siapa pun yang menyalin perpustakaan kami dapat memulai dengan cepat. <strong>AAC.</strong> AAC (Kontainer Arsip Anna) adalah satu item yang terdiri dari <strong>metadata</strong>, dan opsional <strong>data biner</strong>, keduanya tidak dapat diubah. Item ini memiliki pengenal unik global, yang disebut <strong>AACID</strong>. <strong>AACID.</strong> Format AACID adalah sebagai berikut: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Sebagai contoh, AACID yang sebenarnya kami rilis adalah <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Rentang AACID.</strong> Karena AACID mengandung stempel waktu yang meningkat secara monoton, kita dapat menggunakannya untuk menunjukkan rentang dalam koleksi tertentu. Kami menggunakan format ini: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, di mana stempel waktu bersifat inklusif. Ini konsisten dengan notasi ISO 8601. Rentang bersifat kontinu, dan dapat tumpang tindih, tetapi dalam kasus tumpang tindih harus berisi catatan identik seperti yang sebelumnya dirilis dalam koleksi tersebut (karena AAC tidak dapat diubah). Catatan yang hilang tidak diperbolehkan. <code>{collection}</code>: nama koleksi, yang dapat berisi huruf ASCII, angka, dan garis bawah (tetapi tidak ada garis bawah ganda). <code>{collection-specific ID}</code>: pengenal khusus koleksi, jika berlaku, misalnya ID Z-Library. Dapat dihilangkan atau dipotong. Harus dihilangkan atau dipotong jika AACID akan melebihi 150 karakter. <code>{ISO 8601 timestamp}</code>: versi pendek dari ISO 8601, selalu dalam UTC, misalnya <code>20220723T194746Z</code>. Angka ini harus meningkat secara monoton untuk setiap rilis, meskipun semantik pastinya dapat berbeda per koleksi. Kami menyarankan menggunakan waktu pengambilan data atau pembuatan ID. <code>{shortuuid}</code>: UUID tetapi dikompresi ke ASCII, misalnya menggunakan base57. Saat ini kami menggunakan pustaka Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Folder data biner.</strong> Sebuah folder dengan data biner dari rentang AAC, untuk satu koleksi tertentu. Folder ini memiliki properti berikut: Direktori harus berisi file data untuk semua AAC dalam rentang yang ditentukan. Setiap file data harus memiliki AACID sebagai nama file (tanpa ekstensi). Nama direktori harus berupa rentang AACID, diawali dengan <code style="color: green">annas_archive_data__</code>, dan tanpa akhiran. Sebagai contoh, salah satu rilis aktual kami memiliki direktori yang disebut<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Disarankan untuk membuat folder-folder ini berukuran cukup terkelola, misalnya tidak lebih besar dari 100GB-1TB masing-masing, meskipun rekomendasi ini dapat berubah seiring waktu. <strong>Koleksi.</strong> Setiap AAC termasuk dalam sebuah koleksi, yang menurut definisi adalah daftar AAC yang konsisten secara semantik. Artinya, jika Anda membuat perubahan signifikan pada format metadata, maka Anda harus membuat koleksi baru. Standar <strong>File metadata.</strong> File metadata berisi metadata dari rentang AAC, untuk satu koleksi tertentu. File ini memiliki properti berikut: <code>data_folder</code> bersifat opsional, dan merupakan nama folder data biner yang berisi data biner yang sesuai. Nama file data biner yang sesuai dalam folder tersebut adalah AACID catatan. Setiap objek JSON harus berisi bidang berikut di tingkat atas: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsional). Tidak ada bidang lain yang diperbolehkan. Nama file harus berupa rentang AACID, diawali dengan <code style="color: red">annas_archive_meta__</code> dan diikuti oleh <code>.jsonl.zstd</code>. Sebagai contoh, salah satu rilis kami disebut<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Seperti yang ditunjukkan oleh ekstensi file, jenis file adalah <a %(jsonlines)s>JSON Lines</a> yang dikompresi dengan <a %(zstd)s>Zstandard</a>. <code>metadata</code> adalah metadata sewenang-wenang, sesuai semantik koleksi. Harus konsisten secara semantik dalam koleksi. Awalan <code style="color: red">annas_archive_meta__</code> dapat disesuaikan dengan nama institusi Anda, misalnya <code style="color: red">my_institute_meta__</code>. <strong>Koleksi “catatan” dan “file”.</strong> Secara konvensi, sering kali lebih mudah untuk merilis “catatan” dan “file” sebagai koleksi yang berbeda, sehingga dapat dirilis pada jadwal yang berbeda, misalnya berdasarkan tingkat pengambilan data. “Catatan” adalah koleksi yang hanya berisi metadata, yang memuat informasi seperti judul buku, penulis, ISBN, dll, sedangkan “file” adalah koleksi yang berisi file sebenarnya (pdf, epub). Pada akhirnya, kami menetapkan standar yang relatif sederhana. Standar ini cukup longgar, tidak normatif, dan masih dalam pengembangan. <strong>Torrents.</strong> File metadata dan folder data biner dapat dibundel dalam torrent, dengan satu torrent per file metadata, atau satu torrent per folder data biner. Torrent harus memiliki nama file/direktori asli ditambah akhiran <code>.torrent</code> sebagai nama file mereka. <a %(wikipedia_annas_archive)s>Arsip Anna</a> telah menjadi shadow library terbesar di dunia, dan satu-satunya shadow library dengan skala sebesar ini yang sepenuhnya open-source dan open-data. Di bawah ini adalah tabel dari halaman Datasets kami (sedikit dimodifikasi): Kami mencapai ini dengan tiga cara: Mencerminkan shadow library open-data yang ada (seperti Sci-Hub dan Library Genesis). Membantu shadow library yang ingin lebih terbuka, tetapi tidak memiliki waktu atau sumber daya untuk melakukannya (seperti koleksi komik Libgen). Mengambil data dari perpustakaan yang tidak ingin berbagi secara massal (seperti Z-Library). Untuk (2) dan (3) kami sekarang mengelola koleksi torrent yang cukup besar sendiri (ratusan TB). Sejauh ini kami mendekati koleksi ini sebagai satu kali, artinya infrastruktur dan organisasi data yang dibuat khusus untuk setiap koleksi. Ini menambah beban kerja yang signifikan pada setiap rilis, dan membuatnya sangat sulit untuk melakukan rilis yang lebih bertahap. Itulah mengapa kami memutuskan untuk menstandarisasi rilis kami. Ini adalah posting blog teknis di mana kami memperkenalkan standar kami: <strong>Kontainer Arsip Anna</strong>. Arsip Anna Kontainer (AAC): standarisasi rilis dari shadow library terbesar di dunia Arsip Anna telah menjadi shadow library terbesar di dunia, mengharuskan kami untuk menstandarisasi rilis kami. 300GB+ sampul buku dirilis Akhirnya, kami senang mengumumkan rilis kecil. Dalam kolaborasi dengan orang-orang yang mengoperasikan cabang Libgen.rs, kami membagikan semua sampul buku mereka melalui torrent dan IPFS. Ini akan mendistribusikan beban melihat sampul di antara lebih banyak mesin, dan akan melestarikannya dengan lebih baik. Dalam banyak (tetapi tidak semua) kasus, sampul buku disertakan dalam file itu sendiri, jadi ini semacam "data turunan". Namun, memilikinya di IPFS masih sangat berguna untuk operasi harian baik Arsip Anna maupun berbagai cabang Library Genesis. Seperti biasa, Anda dapat menemukan rilis ini di Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>). Kami tidak akan menautkannya di sini, tetapi Anda dapat dengan mudah menemukannya. Semoga kami bisa sedikit mengendurkan tempo kami, sekarang kami memiliki alternatif yang layak untuk Z-Library. Beban kerja ini tidak terlalu berkelanjutan. Jika Anda tertarik membantu dalam pemrograman, operasi server, atau pekerjaan pelestarian, jangan ragu untuk menghubungi kami. Masih banyak <a %(annas_archive)s>pekerjaan yang harus dilakukan</a>. Terima kasih atas minat dan dukungan Anda. Beralih ke ElasticSearch Beberapa kueri memakan waktu sangat lama, hingga mereka akan menghabiskan semua koneksi yang terbuka. Secara default, MySQL memiliki panjang kata minimum, atau indeks Anda bisa menjadi sangat besar. Orang melaporkan tidak dapat mencari "Ben Hur". Pencarian hanya agak cepat ketika sepenuhnya dimuat dalam memori, yang mengharuskan kami mendapatkan mesin yang lebih mahal untuk menjalankannya, ditambah beberapa perintah untuk memuat indeks saat startup. Kami tidak akan dapat memperluasnya dengan mudah untuk membangun fitur baru, seperti <a %(wikipedia_cjk_characters)s>tokenisasi yang lebih baik untuk bahasa tanpa spasi</a>, penyaringan/pemfaktoran, pengurutan, saran "maksud Anda", pelengkapan otomatis, dan sebagainya. Salah satu <a %(annas_archive)s>tiket</a> kami adalah kumpulan masalah dengan sistem pencarian kami. Kami menggunakan pencarian teks lengkap MySQL, karena kami memiliki semua data kami di MySQL. Namun, ada batasannya: Setelah berbicara dengan banyak ahli, kami memutuskan untuk menggunakan ElasticSearch. Ini belum sempurna (saran "maksud Anda" dan fitur pelengkapan otomatis default mereka kurang bagus), tetapi secara keseluruhan ini jauh lebih baik daripada MySQL untuk pencarian. Kami masih tidak <a %(youtube)s>terlalu tertarik</a> menggunakannya untuk data yang sangat penting (meskipun mereka telah membuat banyak <a %(elastic_co)s>kemajuan</a>), tetapi secara keseluruhan kami cukup senang dengan peralihan ini. Untuk saat ini, kami telah mengimplementasikan pencarian yang jauh lebih cepat, dukungan bahasa yang lebih baik, pengurutan relevansi yang lebih baik, opsi pengurutan yang berbeda, dan penyaringan berdasarkan bahasa/jenis buku/jenis file. Jika Anda penasaran bagaimana ini bekerja, <a %(annas_archive_l140)s>silakan</a> <a %(annas_archive_l1115)s>lihat</a> <a %(annas_archive_l1635)s>di sini</a>. Ini cukup mudah diakses, meskipun bisa menggunakan beberapa komentar lagi… Arsip Anna sepenuhnya sumber terbuka Kami percaya bahwa informasi harus bebas, dan kode kami sendiri tidak terkecuali. Kami telah merilis semua kode kami di instance Gitlab yang kami host secara pribadi: <a %(annas_archive)s>Perangkat Lunak Anna</a>. Kami juga menggunakan pelacak masalah untuk mengatur pekerjaan kami. Jika Anda ingin terlibat dengan pengembangan kami, ini adalah tempat yang bagus untuk memulai. Untuk memberi Anda gambaran tentang hal-hal yang sedang kami kerjakan, lihat pekerjaan terbaru kami pada peningkatan kinerja sisi klien. Karena kami belum menerapkan paginasi, kami sering mengembalikan halaman pencarian yang sangat panjang, dengan 100-200 hasil. Kami tidak ingin memotong hasil pencarian terlalu cepat, tetapi ini berarti akan memperlambat beberapa perangkat. Untuk ini, kami menerapkan trik kecil: kami membungkus sebagian besar hasil pencarian dalam komentar HTML (<code><!-- --></code>), dan kemudian menulis sedikit Javascript yang akan mendeteksi kapan hasil harus menjadi terlihat, pada saat itu kami akan membuka komentar: DOM "virtualisasi" diimplementasikan dalam 23 baris, tidak perlu pustaka mewah! Ini adalah jenis kode pragmatis cepat yang Anda dapatkan ketika Anda memiliki waktu terbatas, dan masalah nyata yang perlu diselesaikan. Dilaporkan bahwa pencarian kami sekarang berfungsi dengan baik pada perangkat lambat! Upaya besar lainnya adalah mengotomatisasi pembangunan basis data. Ketika kami diluncurkan, kami hanya sembarangan mengumpulkan berbagai sumber. Sekarang kami ingin menjaga mereka tetap diperbarui, jadi kami menulis serangkaian skrip untuk mengunduh metadata baru dari dua cabang Library Genesis, dan mengintegrasikannya. Tujuannya adalah tidak hanya membuat ini berguna untuk arsip kami, tetapi juga memudahkan siapa saja yang ingin bermain-main dengan metadata shadow library. Tujuannya adalah sebuah notebook Jupyter yang memiliki berbagai metadata menarik yang tersedia, sehingga kami dapat melakukan lebih banyak penelitian seperti mencari tahu <a %(blog)s>persentase ISBN yang diawetkan selamanya</a>. Akhirnya, kami memperbarui sistem donasi kami. Anda sekarang dapat menggunakan kartu kredit untuk langsung menyetor uang ke dompet kripto kami, tanpa benar-benar perlu mengetahui apa pun tentang mata uang kripto. Kami akan terus memantau seberapa baik ini bekerja dalam praktiknya, tetapi ini adalah hal besar. Dengan Z-Library yang turun dan pendirinya (diduga) ditangkap, kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arsip Anna (kami tidak akan menautkannya di sini, tetapi Anda dapat mencarinya di Google). Berikut adalah beberapa hal yang kami capai baru-baru ini. Pembaruan Anna: arsip sumber terbuka sepenuhnya, ElasticSearch, lebih dari 300GB sampul buku Kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arsip Anna. Berikut adalah beberapa hal yang kami capai baru-baru ini. Analisis Duplikat semantik (pemindaian berbeda dari buku yang sama) secara teoritis dapat disaring, tetapi itu rumit. Ketika secara manual melihat melalui komik, kami menemukan terlalu banyak positif palsu. Ada beberapa duplikat murni berdasarkan MD5, yang relatif boros, tetapi menyaringnya hanya akan memberi kami penghematan sekitar 1% in. Pada skala ini itu masih sekitar 1TB, tetapi juga, pada skala ini 1TB tidak terlalu penting. Kami lebih suka tidak mengambil risiko secara tidak sengaja menghancurkan data dalam proses ini. Kami menemukan banyak data non-buku, seperti film berdasarkan buku komik. Itu juga tampak boros, karena ini sudah tersedia secara luas melalui cara lain. Namun, kami menyadari bahwa kami tidak bisa begitu saja menyaring file film, karena ada juga <em>buku komik interaktif</em> yang dirilis di komputer, yang direkam dan disimpan sebagai film oleh seseorang. Pada akhirnya, apa pun yang bisa kami hapus dari koleksi hanya akan menghemat beberapa persen. Kemudian kami ingat bahwa kami adalah pengumpul data, dan orang-orang yang akan mencerminkan ini juga pengumpul data, jadi, "APA MAKSUDMU, HAPUS?!" :) Ketika Anda mendapatkan 95TB yang dibuang ke dalam kluster penyimpanan Anda, Anda mencoba memahami apa yang ada di dalamnya… Kami melakukan beberapa analisis untuk melihat apakah kami bisa mengurangi ukurannya sedikit, seperti dengan menghapus duplikat. Berikut adalah beberapa temuan kami: Oleh karena itu, kami menyajikan kepada Anda, koleksi lengkap yang tidak dimodifikasi. Ini adalah banyak data, tetapi kami berharap cukup banyak orang yang peduli untuk tetap menyebarkannya. Kolaborasi Mengingat ukurannya, koleksi ini sudah lama ada dalam daftar keinginan kami, jadi setelah keberhasilan kami dengan mencadangkan Z-Library, kami mengarahkan pandangan kami pada koleksi ini. Pada awalnya kami mengikisnya secara langsung, yang merupakan tantangan besar, karena server mereka tidak dalam kondisi terbaik. Kami mendapatkan sekitar 15TB dengan cara ini, tetapi prosesnya berjalan lambat. Untungnya, kami berhasil menghubungi operator perpustakaan, yang setuju untuk mengirimkan semua data kepada kami secara langsung, yang jauh lebih cepat. Namun, masih memakan waktu lebih dari setengah tahun untuk mentransfer dan memproses semua data, dan kami hampir kehilangan semuanya karena kerusakan disk, yang berarti harus memulai dari awal lagi. Pengalaman ini membuat kami percaya bahwa penting untuk menyebarkan data ini secepat mungkin, sehingga dapat dicerminkan secara luas. Kami hanya satu atau dua insiden yang tidak beruntung dari kehilangan koleksi ini selamanya! Koleksi Bergerak cepat memang berarti bahwa koleksi ini sedikit tidak terorganisir… Mari kita lihat. Bayangkan kita memiliki sistem file (yang sebenarnya kita bagi-bagi dalam torrent): Direktori pertama, <code>/repository</code>, adalah bagian yang lebih terstruktur dari ini. Direktori ini berisi yang disebut "thousand dirs": direktori masing-masing dengan ribuan file, yang diberi nomor secara bertahap dalam database. Direktori <code>0</code> berisi file dengan comic_id 0–999, dan seterusnya. Ini adalah skema yang sama yang telah digunakan Library Genesis untuk koleksi fiksi dan non-fiksinya. Idenya adalah bahwa setiap "thousand dir" secara otomatis diubah menjadi torrent segera setelah terisi. Namun, operator Libgen.li tidak pernah membuat torrent untuk koleksi ini, sehingga thousand dirs mungkin menjadi tidak nyaman, dan memberi jalan kepada "unsorted dirs". Ini adalah <code>/comics0</code> hingga <code>/comics4</code>. Mereka semua memiliki struktur direktori unik, yang mungkin masuk akal untuk mengumpulkan file, tetapi tidak terlalu masuk akal bagi kami sekarang. Untungnya, metadata masih merujuk langsung ke semua file ini, jadi organisasi penyimpanan mereka di disk sebenarnya tidak masalah! Metadata tersedia dalam bentuk database MySQL. Ini dapat diunduh langsung dari situs web Libgen.li, tetapi kami juga akan menyediakannya dalam torrent, bersama dengan tabel kami sendiri dengan semua hash MD5. <q>Dr. Barbara Gordon mencoba menghilangkan dirinya di dunia perpustakaan yang biasa saja…</q> Fork Libgen Pertama, sedikit latar belakang. Anda mungkin mengenal Library Genesis karena koleksi bukunya yang epik. Lebih sedikit orang yang tahu bahwa relawan Library Genesis telah menciptakan proyek lain, seperti koleksi majalah dan dokumen standar yang cukup besar, cadangan penuh Sci-Hub (bekerja sama dengan pendiri Sci-Hub, Alexandra Elbakyan), dan memang, koleksi komik yang sangat besar. Pada suatu titik, operator cermin Library Genesis yang berbeda berpisah, yang menyebabkan situasi saat ini dengan sejumlah "fork" yang berbeda, semuanya masih menggunakan nama Library Genesis. Fork Libgen.li secara unik memiliki koleksi komik ini, serta koleksi majalah yang cukup besar (yang juga sedang kami kerjakan). Penggalangan Dana Kami merilis data ini dalam beberapa bagian besar. Torrent pertama adalah <code>/comics0</code>, yang kami masukkan ke dalam satu file .tar besar berukuran 12TB. Itu lebih baik untuk hard drive dan perangkat lunak torrent Anda daripada banyak file kecil. Sebagai bagian dari rilis ini, kami mengadakan penggalangan dana. Kami berusaha mengumpulkan $20,000 untuk menutupi biaya operasional dan kontrak untuk koleksi ini, serta memungkinkan proyek yang sedang berjalan dan masa depan. Kami memiliki beberapa proyek <em>besar</em> yang sedang dikerjakan. <em>Siapa yang saya dukung dengan donasi saya?</em> Singkatnya: kami mendukung semua pengetahuan dan budaya manusia, dan membuatnya mudah diakses. Semua kode dan data kami bersifat open source, kami adalah proyek yang sepenuhnya dijalankan oleh sukarelawan, dan kami telah menyimpan buku senilai 125TB sejauh ini (selain torrent Libgen dan Scihub yang sudah ada). Pada akhirnya, kami membangun roda gila yang memungkinkan dan mendorong orang untuk menemukan, memindai, dan mencadangkan semua buku di dunia. Kami akan menulis tentang rencana utama kami dalam postingan mendatang. :) Jika Anda berdonasi untuk keanggotaan “Amazing Archivist” selama 12 bulan ($780), Anda bisa <strong>“mengadopsi torrent”</strong>, yang berarti kami akan menempatkan nama pengguna atau pesan Anda dalam nama file salah satu torrent! Anda dapat berdonasi dengan mengunjungi <a %(wikipedia_annas_archive)s>Arsip Anna</a> dan mengklik tombol “Donasi”. Kami juga mencari lebih banyak sukarelawan: insinyur perangkat lunak, peneliti keamanan, ahli pedagang anonim, dan penerjemah. Anda juga dapat mendukung kami dengan menyediakan layanan hosting. Dan tentu saja, silakan sebar torrent kami! Terima kasih kepada semua orang yang telah dengan murah hati mendukung kami! Anda benar-benar membuat perbedaan. Berikut adalah torrent yang telah dirilis sejauh ini (kami masih memproses sisanya): Semua torrent dapat ditemukan di <a %(wikipedia_annas_archive)s>Arsip Anna</a> di bawah “Datasets” (kami tidak menautkan langsung ke sana, jadi tautan ke blog ini tidak dihapus dari Reddit, Twitter, dll). Dari sana, ikuti tautan ke situs web Tor. <a %(news_ycombinator)s>Diskusikan di Hacker News</a> Apa selanjutnya? Sekumpulan torrent bagus untuk pelestarian jangka panjang, tetapi tidak begitu banyak untuk akses sehari-hari. Kami akan bekerja sama dengan mitra hosting untuk mendapatkan semua data ini di web (karena Arsip Anna tidak meng-host apa pun secara langsung). Tentu saja Anda akan dapat menemukan tautan unduhan ini di Arsip Anna. Kami juga mengundang semua orang untuk melakukan sesuatu dengan data ini! Bantu kami menganalisisnya dengan lebih baik, menghapus duplikatnya, menempatkannya di IPFS, mengolahnya kembali, melatih model AI Anda dengannya, dan sebagainya. Semuanya milik Anda, dan kami tidak sabar untuk melihat apa yang Anda lakukan dengannya. Akhirnya, seperti yang dikatakan sebelumnya, kami masih memiliki beberapa rilis besar yang akan datang (jika <em>seseorang</em> bisa <em>secara tidak sengaja</em> mengirimkan dump dari database <em>tertentu</em> ACS4, Anda tahu di mana menemukan kami...), serta membangun roda gila untuk mencadangkan semua buku di dunia. Jadi tetaplah disini, kami baru saja memulai. - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Perpustakaan shadow buku komik terbesar kemungkinan adalah dari cabang Library Genesis tertentu: Libgen.li. Satu administrator yang menjalankan situs itu berhasil mengumpulkan koleksi komik gila lebih dari 2 juta file, dengan total lebih dari 95TB. Namun, tidak seperti koleksi Library Genesis lainnya, yang satu ini tidak tersedia secara massal melalui torrent. Anda hanya bisa mengakses komik ini secara individual melalui server pribadi yang lambat — satu titik kegagalan. Hingga hari ini! Dalam postingan ini kami akan memberi tahu Anda lebih banyak tentang koleksi ini, dan tentang penggalangan dana kami untuk mendukung lebih banyak pekerjaan ini. Arsip Anna telah mencadangkan perpustakaan shadow komik terbesar di dunia (95TB) — Anda dapat membantu menyebarkannya Perpustakaan shadow buku komik terbesar di dunia memiliki satu titik kegagalan.. hingga hari ini. Peringatan: posting blog ini telah dihentikan. Kami memutuskan bahwa IPFS belum siap untuk waktu utama. Kami masih akan menautkan ke file di IPFS dari Arsip Anna jika memungkinkan, tetapi kami tidak akan menghostingnya sendiri lagi, juga tidak merekomendasikan orang lain untuk mirror menggunakan IPFS. Silakan lihat halaman Torrents kami jika Anda ingin membantu melestarikan koleksi kami. Menempatkan 5.998.794 buku di IPFS Penggandaan salinan Kembali ke pertanyaan awal kami: bagaimana kami dapat mengklaim untuk melestarikan koleksi kami selamanya? Masalah utama di sini adalah bahwa koleksi kami telah <a %(torrents_stats)s>tumbuh</a> dengan cepat, dengan mengikis dan membuka sumber beberapa koleksi besar (di atas pekerjaan luar biasa yang sudah dilakukan oleh perpustakaan bayangan data terbuka lainnya seperti Sci-Hub dan Library Genesis). Pertumbuhan data ini membuat koleksi lebih sulit untuk dicerminkan di seluruh dunia. Penyimpanan data mahal! Namun kami optimis, terutama ketika mengamati tiga tren berikut. <a %(annas_archive_stats)s>Ukuran total</a> koleksi kami, selama beberapa bulan terakhir, dipecah berdasarkan jumlah penyebar torrent. Tren harga HDD dari berbagai sumber (klik untuk melihat studi). <a %(critical_window_chinese)s>Versi Cina 中文版</a>, diskusikan di <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Kami telah memetik buah yang mudah dijangkau Yang satu ini mengikuti langsung dari prioritas kami yang dibahas di atas. Kami lebih suka bekerja untuk membebaskan koleksi besar terlebih dahulu. Sekarang setelah kami mengamankan beberapa koleksi terbesar di dunia, kami mengharapkan pertumbuhan kami menjadi jauh lebih lambat. Masih ada ekor panjang dari koleksi yang lebih kecil, dan buku baru dipindai atau diterbitkan setiap hari, tetapi kecepatannya kemungkinan akan jauh lebih lambat. Kami mungkin masih bisa menggandakan atau bahkan melipatgandakan ukuran, tetapi dalam jangka waktu yang lebih lama. Peningkatan OCR. Prioritas Kode perangkat lunak sains & teknik Versi fiksi atau hiburan dari semua hal di atas Data geografis (misalnya peta, survei geologi) Data internal dari perusahaan atau pemerintah (bocoran) Data pengukuran seperti pengukuran ilmiah, data ekonomi, laporan perusahaan Catatan metadata secara umum (dari non-fiksi dan fiksi; dari media lain, seni, orang, dll; termasuk ulasan) Buku non-fiksi Majalah non-fiksi, surat kabar, manual Transkrip non-fiksi dari ceramah, dokumenter, podcast Data organik seperti urutan DNA, biji tanaman, atau sampel mikroba Makalah akademis, jurnal, laporan Situs web sains & teknik, diskusi online Transkrip dari proses hukum atau pengadilan Sangat berisiko dihancurkan (misalnya oleh perang, pemotongan dana, tuntutan hukum, atau penganiayaan politik) Langka Sangat kurang diperhatikan Mengapa kita sangat peduli dengan makalah dan buku? Mari kita kesampingkan keyakinan mendasar kita dalam pelestarian secara umum — kita mungkin menulis posting lain tentang itu. Jadi mengapa makalah dan buku secara khusus? Jawabannya sederhana: <strong>kepadatan informasi</strong>. Per megabyte penyimpanan, teks tertulis menyimpan informasi paling banyak dari semua media. Meskipun kami peduli tentang pengetahuan dan budaya, kami lebih peduli tentang yang pertama. Secara keseluruhan, kami menemukan hierarki kepadatan informasi dan pentingnya pelestarian yang terlihat kira-kira seperti ini: Peringkat dalam daftar ini agak sewenang-wenang — beberapa item adalah seri atau memiliki ketidaksepakatan dalam tim kami — dan kami mungkin melupakan beberapa kategori penting. Namun, ini adalah cara kami memprioritaskan secara kasar. Beberapa item ini terlalu berbeda dari yang lain untuk kami khawatirkan (atau sudah diurus oleh lembaga lain), seperti data organik atau data geografis. Namun, sebagian besar item dalam daftar ini sebenarnya penting bagi kami. Faktor besar lainnya dalam prioritas kami adalah seberapa besar risiko yang dihadapi suatu karya. Kami lebih suka fokus pada karya yang: Akhirnya, kami peduli tentang skala. Kami memiliki waktu dan uang yang terbatas, jadi kami lebih suka menghabiskan sebulan untuk menyelamatkan 10.000 buku daripada 1.000 buku — jika mereka sama berharganya dan berisiko. <em><q>Yang hilang tidak dapat dipulihkan; tetapi mari kita selamatkan apa yang tersisa: bukan dengan brankas dan kunci yang menjauhkan mereka dari pandangan dan penggunaan publik, dengan menyerahkan mereka pada pemborosan waktu, tetapi dengan penggandaan salinan, yang akan menempatkan mereka di luar jangkauan kecelakaan.</q></em><br>— Thomas Jefferson, 1791 Perpustakaan bayangan Kode dapat bersifat open source di Github, tetapi Github secara keseluruhan tidak dapat dengan mudah dicerminkan dan dengan demikian diawetkan (meskipun dalam kasus ini ada salinan yang cukup tersebar dari sebagian besar repositori kode) Catatan metadata dapat dilihat secara bebas di situs web Worldcat, tetapi tidak dapat diunduh secara massal (sampai kami <a %(worldcat_scrape)s>mengikis</a>nya) Reddit gratis untuk digunakan, tetapi baru-baru ini menerapkan langkah-langkah anti-scraping yang ketat, setelah pelatihan LLM yang haus data (lebih lanjut tentang itu nanti) Ada banyak organisasi yang memiliki misi serupa, dan prioritas serupa. Memang, ada perpustakaan, arsip, laboratorium, museum, dan lembaga lain yang ditugaskan untuk pelestarian semacam ini. Banyak dari mereka didanai dengan baik, oleh pemerintah, individu, atau perusahaan. Namun, mereka memiliki satu titik buta besar: sistem hukum. Di sinilah peran unik perpustakaan bayangan, dan alasan Arsip Anna ada. Kami dapat melakukan hal-hal yang tidak diizinkan oleh lembaga lain. Sekarang, bukan (sering) bahwa kami dapat mengarsipkan materi yang ilegal untuk dilestarikan di tempat lain. Tidak, di banyak tempat legal untuk membangun arsip dengan buku, makalah, majalah, dan sebagainya. Tetapi yang sering kali kurang dimiliki arsip hukum adalah <strong>redundansi dan umur panjang</strong>. Ada buku-buku yang hanya memiliki satu salinan di beberapa perpustakaan fisik di suatu tempat. Ada catatan metadata yang dijaga oleh satu perusahaan. Ada surat kabar yang hanya diawetkan dalam bentuk mikrofilm di satu arsip. Perpustakaan bisa mengalami pemotongan dana, perusahaan bisa bangkrut, arsip bisa dibom dan dibakar habis. Ini bukanlah hipotesis — ini terjadi sepanjang waktu. Hal yang dapat kami lakukan secara unik di Arsip Anna adalah menyimpan banyak salinan karya, dalam skala besar. Kami dapat mengumpulkan makalah, buku, majalah, dan lainnya, serta mendistribusikannya secara massal. Saat ini kami melakukannya melalui torrent, tetapi teknologi yang tepat tidak penting dan akan berubah seiring waktu. Bagian pentingnya adalah mendapatkan banyak salinan yang didistribusikan ke seluruh dunia. Kutipan ini dari lebih dari 200 tahun yang lalu masih relevan: Catatan singkat tentang domain publik. Karena Arsip Anna secara unik berfokus pada aktivitas yang ilegal di banyak tempat di seluruh dunia, kami tidak repot-repot dengan koleksi yang sudah tersedia secara luas, seperti buku domain publik. Entitas hukum sering kali sudah merawatnya dengan baik. Namun, ada pertimbangan yang membuat kami kadang-kadang bekerja pada koleksi yang tersedia untuk umum: - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Biaya penyimpanan terus menurun secara eksponensial 3. Peningkatan dalam kepadatan informasi Saat ini, kami menyimpan buku dalam format mentah seperti yang diberikan kepada kami. Memang, mereka sudah dikompresi, tetapi seringkali masih berupa pemindaian atau foto halaman yang besar. Sampai sekarang, satu-satunya pilihan untuk mengurangi ukuran total koleksi kami adalah melalui kompresi yang lebih agresif, atau deduplikasi. Namun, untuk mendapatkan penghematan yang signifikan, keduanya terlalu banyak kehilangan kualitas untuk selera kami. Kompresi berat pada foto dapat membuat teks hampir tidak terbaca. Dan deduplikasi memerlukan keyakinan tinggi bahwa buku-buku tersebut benar-benar sama, yang seringkali terlalu tidak akurat, terutama jika isinya sama tetapi pemindaian dilakukan pada kesempatan yang berbeda. Selalu ada opsi ketiga, tetapi kualitasnya sangat buruk sehingga kami tidak pernah mempertimbangkannya: <strong>OCR, atau Optical Character Recognition</strong>. Ini adalah proses mengubah foto menjadi teks biasa, dengan menggunakan AI untuk mendeteksi karakter dalam foto. Alat untuk ini sudah lama ada, dan cukup baik, tetapi "cukup baik" tidak cukup untuk tujuan pelestarian. Namun, model pembelajaran mendalam multi-modal terbaru telah membuat kemajuan yang sangat cepat, meskipun masih dengan biaya tinggi. Kami mengharapkan akurasi dan biaya akan meningkat secara dramatis dalam beberapa tahun mendatang, hingga menjadi realistis untuk diterapkan pada seluruh perpustakaan kami. Ketika itu terjadi, kami mungkin masih akan menyimpan file asli, tetapi sebagai tambahan kami dapat memiliki versi perpustakaan yang jauh lebih kecil yang kebanyakan orang ingin mirror. Keuntungannya adalah teks mentah itu sendiri lebih mudah dikompresi, dan lebih mudah dideduplikasi, memberikan kami lebih banyak penghematan. Secara keseluruhan, tidaklah tidak realistis untuk mengharapkan pengurangan ukuran file total setidaknya 5-10x, mungkin bahkan lebih. Bahkan dengan pengurangan konservatif 5x, kami akan melihat <strong>$1,000–$3,000 dalam 10 tahun bahkan jika perpustakaan kami tiga kali lipat ukurannya</strong>. Pada saat penulisan, <a %(diskprices)s>harga disk</a> per TB sekitar $12 untuk disk baru, $8 untuk disk bekas, dan $4 untuk pita. Jika kami konservatif dan hanya melihat disk baru, itu berarti menyimpan satu petabyte berharga sekitar $12.000. Jika kami mengasumsikan perpustakaan kami akan tiga kali lipat dari 900TB menjadi 2,7PB, itu berarti $32.400 untuk mencerminkan seluruh perpustakaan kami. Menambahkan listrik, biaya perangkat keras lainnya, dan sebagainya, mari kita bulatkan menjadi $40.000. Atau dengan pita lebih seperti $15.000–$20.000. Di satu sisi <strong>$15.000–$40.000 untuk jumlah semua pengetahuan manusia adalah harga yang murah</strong>. Di sisi lain, agak mahal untuk mengharapkan banyak salinan penuh, terutama jika kami juga ingin orang-orang tersebut terus menanam torrent mereka untuk kepentingan orang lain. Itu hari ini. Tetapi kemajuan terus berjalan: Biaya hard drive per TB telah berkurang sekitar sepertiga selama 10 tahun terakhir, dan kemungkinan akan terus menurun dengan kecepatan yang sama. Pita tampaknya berada pada jalur yang sama. Harga SSD turun lebih cepat, dan mungkin akan mengambil alih harga HDD pada akhir dekade ini. Jika ini bertahan, maka dalam 10 tahun kita mungkin hanya melihat $5.000–$13.000 untuk mencerminkan seluruh koleksi kita (1/3), atau bahkan lebih sedikit jika kita tumbuh lebih kecil. Meskipun masih banyak uang, ini akan dapat dicapai oleh banyak orang. Dan mungkin akan lebih baik lagi karena poin berikutnya… Di Arsip Anna, kami sering ditanya bagaimana kami bisa mengklaim melestarikan koleksi kami selamanya, ketika ukuran totalnya sudah mendekati 1 Petabyte (1000 TB), dan masih terus bertambah. Dalam artikel ini, kita akan melihat filosofi kami, dan melihat mengapa dekade berikutnya sangat penting untuk misi kami melestarikan pengetahuan dan budaya umat manusia. Jendela kritis Jika perkiraan ini akurat, kita <strong>hanya perlu menunggu beberapa tahun</strong> sebelum seluruh koleksi kita akan banyak dimirror. Dengan demikian, dalam kata-kata Thomas Jefferson, "ditempatkan di luar jangkauan kecelakaan." Sayangnya, munculnya LLM, dan pelatihan mereka yang haus data, telah membuat banyak pemegang hak cipta bersikap defensif. Bahkan lebih dari sebelumnya. Banyak situs web membuatnya lebih sulit untuk di-scrape dan diarsipkan, tuntutan hukum bertebaran, dan sementara itu perpustakaan fisik dan arsip terus diabaikan. Kami hanya dapat mengharapkan tren ini terus memburuk, dan banyak karya hilang jauh sebelum mereka memasuki domain publik. <strong>Kami berada di ambang revolusi dalam pelestarian, tetapi <q>yang hilang tidak dapat dipulihkan.</q></strong> Kami memiliki jendela kritis sekitar 5-10 tahun di mana masih cukup mahal untuk mengoperasikan shadow library dan membuat banyak mirror di seluruh dunia, dan di mana akses belum sepenuhnya ditutup. Jika kita dapat menjembatani jendela ini, maka kita benar-benar akan melestarikan pengetahuan dan budaya manusia untuk selamanya. Kita tidak boleh membiarkan waktu ini terbuang sia-sia. Kita tidak boleh membiarkan jendela kritis ini tertutup bagi kita. Ayo kita mulai. Jendela kritis perpustakaan bayangan Bagaimana kita bisa mengklaim melestarikan koleksi kita selamanya, ketika mereka sudah mendekati 1 PB? Koleksi Beberapa informasi lebih lanjut tentang koleksi ini. <a %(duxiu)s>Duxiu</a> adalah database besar buku yang dipindai, dibuat oleh <a %(chaoxing)s>SuperStar Digital Library Group</a>. Sebagian besar adalah buku akademik, dipindai untuk membuatnya tersedia secara digital bagi universitas dan perpustakaan. Untuk audiens berbahasa Inggris kami, <a %(library_princeton)s>Princeton</a> dan <a %(guides_lib_uw)s>University of Washington</a> memiliki ikhtisar yang baik. Ada juga artikel yang sangat baik yang memberikan latar belakang lebih lanjut: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cari di Arsip Anna). Buku-buku dari Duxiu telah lama dibajak di internet Tiongkok. Biasanya mereka dijual kurang dari satu dolar oleh penjual kembali. Mereka biasanya didistribusikan menggunakan setara Google Drive di Tiongkok, yang sering kali diretas untuk memungkinkan lebih banyak ruang penyimpanan. Beberapa detail teknis dapat ditemukan <a %(github_duty_machine)s>di sini</a> dan <a %(github_821_github_io)s>di sini</a>. Meskipun buku-buku tersebut telah didistribusikan secara semi-publik, cukup sulit untuk mendapatkannya dalam jumlah besar. Kami menempatkan ini tinggi dalam daftar TUGAS kami, dan mengalokasikan beberapa bulan kerja penuh waktu untuk itu. Namun, baru-baru ini seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberi tahu kami bahwa mereka telah melakukan semua pekerjaan ini — dengan biaya besar. Mereka berbagi seluruh koleksi dengan kami, tanpa mengharapkan imbalan apa pun, kecuali jaminan pelestarian jangka panjang. Benar-benar luar biasa. Mereka setuju untuk meminta bantuan dengan cara ini untuk mendapatkan koleksi yang di-OCR. Koleksi ini terdiri dari 7.543.702 file. Ini lebih banyak daripada non-fiksi Library Genesis (sekitar 5,3 juta). Ukuran total file sekitar 359TB (326TiB) dalam bentuk saat ini. Kami terbuka untuk proposal dan ide lain. Hubungi kami saja. Lihat Arsip Anna untuk informasi lebih lanjut tentang koleksi kami, upaya pelestarian, dan bagaimana Anda dapat membantu. Terima kasih! Halaman contoh Untuk membuktikan kepada kami bahwa Anda memiliki pipeline yang baik, berikut adalah beberapa halaman contoh untuk memulai, dari sebuah buku tentang superkonduktor. Pipeline Anda harus dapat menangani matematika, tabel, grafik, catatan kaki, dan sebagainya dengan benar. Kirim halaman yang telah diproses ke email kami. Jika terlihat bagus, kami akan mengirimkan lebih banyak secara pribadi, dan kami berharap Anda dapat dengan cepat menjalankan pipeline Anda pada halaman tersebut juga. Setelah kami puas, kami dapat membuat kesepakatan. - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versi Tiongkok 中文版</a>, <a %(news_ycombinator)s>Diskusikan di Hacker News</a> Ini adalah posting blog singkat. Kami mencari beberapa perusahaan atau institusi untuk membantu kami dengan OCR dan ekstraksi teks untuk koleksi besar yang kami peroleh, dengan imbalan akses awal eksklusif. Setelah periode embargo, kami tentu akan merilis seluruh koleksi. Teks akademik berkualitas tinggi sangat berguna untuk pelatihan LLM. Meskipun koleksi kami berbahasa Tionghoa, ini seharusnya tetap berguna untuk melatih LLM berbahasa Inggris: model tampaknya mengenkripsi konsep dan pengetahuan terlepas dari bahasa sumbernya. Untuk ini, teks perlu diekstraksi dari pemindaian. Apa yang didapat Arsip Anna dari ini? Pencarian teks lengkap dari buku-buku untuk penggunanya. Karena tujuan kami sejalan dengan pengembang LLM, kami mencari kolaborator. Kami bersedia memberi Anda <strong>akses awal eksklusif ke koleksi ini dalam jumlah besar selama 1 tahun</strong>, jika Anda dapat melakukan OCR dan ekstraksi teks dengan benar. Jika Anda bersedia berbagi seluruh kode pipeline Anda dengan kami, kami bersedia menahan koleksi ini lebih lama. Akses eksklusif untuk perusahaan LLM ke koleksi buku non-fiksi Tiongkok terbesar di dunia <em><strong>TL;DR:</strong> Arsip Anna memperoleh koleksi unik 7,5 juta / 350TB buku non-fiksi Tiongkok — lebih besar dari Library Genesis. Kami bersedia memberikan akses eksklusif kepada perusahaan LLM, dengan imbalan OCR berkualitas tinggi dan ekstraksi teks.</em> Arsitektur sistem Jadi katakanlah Anda menemukan beberapa perusahaan yang bersedia meng-host situs web Anda tanpa menutupnya — mari kita sebut mereka “penyedia yang mencintai kebebasan” 😄. Anda akan segera menemukan bahwa meng-host semuanya dengan mereka cukup mahal, jadi Anda mungkin ingin menemukan beberapa “penyedia murah” dan melakukan hosting sebenarnya di sana, dengan proxy melalui penyedia yang mencintai kebebasan. Jika Anda melakukannya dengan benar, penyedia murah tidak akan pernah tahu apa yang Anda host, dan tidak akan pernah menerima keluhan. Dengan semua penyedia ini ada risiko mereka menutup Anda bagaimanapun, jadi Anda juga memerlukan redundansi. Kami memerlukan ini di semua tingkat tumpukan kami. Satu perusahaan yang agak mencintai kebebasan yang telah menempatkan dirinya dalam posisi menarik adalah Cloudflare. Mereka telah <a %(blog_cloudflare)s>berargumen</a> bahwa mereka bukan penyedia hosting, tetapi utilitas, seperti ISP. Oleh karena itu, mereka tidak tunduk pada DMCA atau permintaan penghapusan lainnya, dan meneruskan permintaan apa pun ke penyedia hosting Anda yang sebenarnya. Mereka bahkan telah pergi ke pengadilan untuk melindungi struktur ini. Oleh karena itu, kami dapat menggunakannya sebagai lapisan caching dan perlindungan lainnya. Cloudflare tidak menerima pembayaran anonim, jadi kami hanya dapat menggunakan paket gratis mereka. Ini berarti bahwa kami tidak dapat menggunakan fitur load balancing atau failover mereka. Oleh karena itu, kami <a %(annas_archive_l255)s>mengimplementasikan ini sendiri</a> di tingkat domain. Saat halaman dimuat, browser akan memeriksa apakah domain saat ini masih tersedia, dan jika tidak, itu menulis ulang semua URL ke domain yang berbeda. Karena Cloudflare menyimpan banyak halaman, ini berarti bahwa pengguna dapat mendarat di domain utama kami, bahkan jika server proxy sedang down, dan kemudian pada klik berikutnya dipindahkan ke domain lain. Kami juga masih memiliki kekhawatiran operasional normal untuk ditangani, seperti memantau kesehatan server, mencatat kesalahan backend dan frontend, dan sebagainya. Arsitektur failover kami memungkinkan lebih banyak ketahanan di bagian ini juga, misalnya dengan menjalankan satu set server yang sama sekali berbeda di salah satu domain. Kami bahkan dapat menjalankan versi kode dan datasets yang lebih lama di domain terpisah ini, jika ada bug kritis dalam versi utama yang tidak terdeteksi. Kami juga dapat melindungi diri dari kemungkinan Cloudflare berbalik melawan kami, dengan menghapusnya dari salah satu domain, seperti domain terpisah ini. Berbagai kombinasi dari ide-ide ini mungkin dilakukan. Kesimpulan Ini adalah pengalaman menarik untuk belajar bagaimana mengatur mesin pencari shadow library yang kuat dan tangguh. Ada banyak detail lain yang akan dibagikan dalam postingan selanjutnya, jadi beri tahu saya apa yang ingin Anda pelajari lebih lanjut! Seperti biasa, kami mencari donasi untuk mendukung pekerjaan ini, jadi pastikan untuk memeriksa halaman Donasi di Arsip Anna. Kami juga mencari jenis dukungan lain, seperti hibah, sponsor jangka panjang, penyedia pembayaran berisiko tinggi, mungkin bahkan iklan (yang berkelas!). Dan jika Anda ingin menyumbangkan waktu dan keterampilan Anda, kami selalu mencari pengembang, penerjemah, dan sebagainya. Terima kasih atas minat dan dukungan Anda. Token inovasi Mari kita mulai dengan tumpukan teknologi kami. Ini sengaja dibuat membosankan. Kami menggunakan Flask, MariaDB, dan ElasticSearch. Itu saja. Pencarian sebagian besar sudah menjadi masalah yang terpecahkan, dan kami tidak berniat untuk menciptakannya kembali. Selain itu, kami harus menghabiskan <a %(mcfunley)s>token inovasi</a> kami untuk hal lain: agar tidak ditutup oleh pihak berwenang. Jadi seberapa legal atau ilegal sebenarnya Arsip Anna? Ini sebagian besar tergantung pada yurisdiksi hukum. Sebagian besar negara percaya pada beberapa bentuk hak cipta, yang berarti bahwa orang atau perusahaan diberikan monopoli eksklusif pada jenis karya tertentu untuk jangka waktu tertentu. Sebagai catatan, di Arsip Anna kami percaya bahwa meskipun ada beberapa manfaat, secara keseluruhan hak cipta adalah negatif bersih bagi masyarakat — tetapi itu adalah cerita untuk lain waktu. Monopoli eksklusif pada karya tertentu ini berarti bahwa ilegal bagi siapa pun di luar monopoli ini untuk mendistribusikan karya tersebut secara langsung — termasuk kami. Namun, Arsip Anna adalah mesin pencari yang tidak mendistribusikan karya tersebut secara langsung (setidaknya tidak di situs web clearnet kami), jadi kami seharusnya baik-baik saja, bukan? Tidak persis. Di banyak yurisdiksi, tidak hanya ilegal untuk mendistribusikan karya berhak cipta, tetapi juga untuk menautkan ke tempat-tempat yang melakukannya. Contoh klasik dari ini adalah undang-undang DMCA di Amerika Serikat. Itu adalah ujung spektrum yang paling ketat. Di ujung spektrum lainnya, secara teoritis bisa ada negara-negara tanpa undang-undang hak cipta sama sekali, tetapi ini sebenarnya tidak ada. Hampir setiap negara memiliki beberapa bentuk undang-undang hak cipta dalam buku hukum mereka. Penegakan adalah cerita yang berbeda. Ada banyak negara dengan pemerintah yang tidak peduli untuk menegakkan undang-undang hak cipta. Ada juga negara-negara di antara dua ekstrem ini, yang melarang mendistribusikan karya berhak cipta, tetapi tidak melarang menautkan ke karya tersebut. Pertimbangan lain adalah di tingkat perusahaan. Jika sebuah perusahaan beroperasi di yurisdiksi yang tidak peduli tentang hak cipta, tetapi perusahaan itu sendiri tidak mau mengambil risiko, maka mereka mungkin akan menutup situs web Anda segera setelah ada yang mengeluh tentangnya. Akhirnya, pertimbangan besar adalah pembayaran. Karena kami perlu tetap anonim, kami tidak dapat menggunakan metode pembayaran tradisional. Ini membuat kami hanya bisa menggunakan mata uang kripto, dan hanya sebagian kecil perusahaan yang mendukungnya (ada kartu debit virtual yang dibayar dengan kripto, tetapi seringkali tidak diterima). - Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Saya menjalankan <a %(wikipedia_annas_archive)s>Arsip Anna</a>, mesin pencari nirlaba open-source terbesar di dunia untuk <a %(wikipedia_shadow_library)s>perpustakaan bayangan</a>, seperti Sci-Hub, Library Genesis, dan Z-Library. Tujuan kami adalah membuat pengetahuan dan budaya mudah diakses, dan pada akhirnya membangun komunitas orang-orang yang bersama-sama mengarsipkan dan melestarikan <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>semua buku di dunia</a>. Dalam artikel ini saya akan menunjukkan bagaimana kami menjalankan situs web ini, dan tantangan unik yang datang dengan mengoperasikan situs web dengan status hukum yang dipertanyakan, karena tidak ada “AWS untuk amal bayangan”. <em>Juga lihat artikel saudara <a %(blog_how_to_become_a_pirate_archivist)s>Cara menjadi arsiparis bajak laut</a>.</em> Cara menjalankan perpustakaan bayangan: operasi di Arsip Anna Tidak ada <q>AWS untuk amal bayangan,</q> jadi bagaimana kami menjalankan Arsip Anna? Alat Server aplikasi: Flask, MariaDB, ElasticSearch, Docker. Pengembangan: Gitlab, Weblate, Zulip. Manajemen server: Ansible, Checkmk, UFW. Hosting statis Onion: Tor, Nginx. Server proxy: Varnish. Mari kita lihat alat apa yang kami gunakan untuk mencapai semua ini. Ini sangat berkembang saat kami menghadapi masalah baru dan menemukan solusi baru. Ada beberapa keputusan yang telah kami pertimbangkan berulang kali. Salah satunya adalah komunikasi antar server: kami dulu menggunakan Wireguard untuk ini, tetapi menemukan bahwa kadang-kadang berhenti mengirimkan data, atau hanya mengirimkan data dalam satu arah. Ini terjadi dengan beberapa pengaturan Wireguard yang berbeda yang kami coba, seperti <a %(github_costela_wesher)s>wesher</a> dan <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Kami juga mencoba menyalurkan port melalui SSH, menggunakan autossh dan sshuttle, tetapi mengalami <a %(github_sshuttle)s>masalah di sana</a> (meskipun masih belum jelas bagi saya apakah autossh mengalami masalah TCP-over-TCP atau tidak — rasanya seperti solusi yang tidak stabil bagi saya tetapi mungkin sebenarnya baik-baik saja?). Sebagai gantinya, kami kembali ke koneksi langsung antar server, menyembunyikan bahwa server berjalan pada penyedia murah menggunakan pemfilteran IP dengan UFW. Ini memiliki kelemahan bahwa Docker tidak bekerja dengan baik dengan UFW, kecuali Anda menggunakan <code>network_mode: "host"</code>. Semua ini sedikit lebih rentan terhadap kesalahan, karena Anda akan mengekspos server Anda ke internet hanya dengan sedikit kesalahan konfigurasi. Mungkin kita harus kembali ke autossh — umpan balik akan sangat diterima di sini. Kami juga telah mempertimbangkan kembali antara Varnish vs. Nginx. Saat ini kami menyukai Varnish, tetapi memang memiliki keanehan dan kekurangan. Hal yang sama berlaku untuk Checkmk: kami tidak menyukainya, tetapi untuk saat ini berfungsi. Weblate cukup baik tetapi tidak luar biasa — saya kadang-kadang khawatir akan kehilangan data saya setiap kali mencoba menyinkronkannya dengan repo git kami. Flask secara keseluruhan baik, tetapi memiliki beberapa keanehan aneh yang memakan banyak waktu untuk di-debug, seperti mengonfigurasi domain khusus, atau masalah dengan integrasi SqlAlchemy-nya. Sejauh ini alat lainnya sangat baik: kami tidak memiliki keluhan serius tentang MariaDB, ElasticSearch, Gitlab, Zulip, Docker, dan Tor. Semua ini memiliki beberapa masalah, tetapi tidak ada yang terlalu serius atau memakan waktu. Komunitas Tantangan pertama mungkin mengejutkan. Ini bukan masalah teknis, atau masalah hukum. Ini adalah masalah psikologis: melakukan pekerjaan ini dalam bayang-bayang bisa sangat sepi. Tergantung pada apa yang Anda rencanakan untuk dilakukan, dan model ancaman Anda, Anda mungkin harus sangat berhati-hati. Di satu ujung spektrum, kami memiliki orang-orang seperti Alexandra Elbakyan*, pendiri Sci-Hub, yang sangat terbuka tentang kegiatannya. Namun dia berisiko tinggi ditangkap jika dia mengunjungi negara barat saat ini, dan bisa menghadapi hukuman penjara puluhan tahun. Apakah itu risiko yang bersedia Anda ambil? Kami berada di ujung spektrum yang lain; sangat berhati-hati untuk tidak meninggalkan jejak apa pun, dan memiliki keamanan operasional yang kuat. * Seperti yang disebutkan di HN oleh "ynno", Alexandra awalnya tidak ingin dikenal: "Servernya diatur untuk mengeluarkan pesan kesalahan terperinci dari PHP, termasuk jalur lengkap dari file sumber yang bermasalah, yang berada di bawah direktori /home/<USER>" Jadi, gunakan nama pengguna acak di komputer yang Anda gunakan untuk hal-hal ini, jika Anda salah mengonfigurasi sesuatu. Namun, kerahasiaan itu datang dengan biaya psikologis. Kebanyakan orang senang diakui atas pekerjaan yang mereka lakukan, namun Anda tidak dapat mengambil kredit apa pun untuk ini dalam kehidupan nyata. Bahkan hal-hal sederhana bisa menjadi tantangan, seperti teman yang bertanya apa yang telah Anda lakukan (pada titik tertentu "bermain-main dengan NAS / homelab saya" menjadi basi). Inilah mengapa sangat penting untuk menemukan beberapa komunitas. Anda dapat mengorbankan sedikit keamanan operasional dengan mempercayakan kepada beberapa teman dekat, yang Anda tahu dapat Anda percayai sepenuhnya. Bahkan kemudian berhati-hatilah untuk tidak menuliskan apa pun, jika mereka harus menyerahkan email mereka kepada pihak berwenang, atau jika perangkat mereka dikompromikan dengan cara lain. Lebih baik lagi adalah menemukan beberapa sesama bajak laut. Jika teman dekat Anda tertarik untuk bergabung dengan Anda, bagus! Jika tidak, Anda mungkin dapat menemukan orang lain secara online. Sayangnya ini masih merupakan komunitas yang kecil. Sejauh ini kami hanya menemukan segelintir orang lain yang aktif di ruang ini. Tempat awal yang baik tampaknya adalah forum Library Genesis, dan r/DataHoarder. Tim Arsip juga memiliki individu yang berpikiran sama, meskipun mereka beroperasi dalam hukum (bahkan jika dalam beberapa area abu-abu hukum). Adegan "warez" dan pembajakan tradisional juga memiliki orang-orang yang berpikir dengan cara yang sama. Kami terbuka untuk ide-ide tentang bagaimana membangun komunitas dan mengeksplorasi gagasan. Jangan ragu untuk menghubungi kami di Twitter atau Reddit. Mungkin kami bisa mengadakan semacam forum atau grup obrolan. Salah satu tantangannya adalah ini bisa dengan mudah disensor ketika menggunakan platform umum, jadi kami harus mengelolanya sendiri. Ada juga pertimbangan antara membuat diskusi ini sepenuhnya publik (lebih banyak potensi keterlibatan) versus membuatnya privat (tidak membiarkan "target" potensial tahu bahwa kami akan mengumpulkan data mereka). Kami harus memikirkan hal itu. Beri tahu kami jika Anda tertarik dengan ini! Kesimpulan Semoga ini bermanfaat bagi para arsiparis bajak laut yang baru memulai. Kami sangat senang menyambut Anda ke dunia ini, jadi jangan ragu untuk menghubungi kami. Mari kita lestarikan sebanyak mungkin pengetahuan dan budaya dunia, dan mirror-kan seluas mungkin. Proyek 4. Pemilihan Data Seringkali Anda dapat menggunakan metadata untuk menentukan subset data yang masuk akal untuk diunduh. Bahkan jika Anda pada akhirnya ingin mengunduh semua data, akan berguna untuk memprioritaskan item yang paling penting terlebih dahulu, jika Anda terdeteksi dan pertahanan ditingkatkan, atau karena Anda perlu membeli lebih banyak disk, atau hanya karena sesuatu yang lain muncul dalam hidup Anda sebelum Anda dapat mengunduh semuanya. Misalnya, sebuah koleksi mungkin memiliki beberapa edisi dari sumber yang sama (seperti buku atau film), di mana satu ditandai sebagai kualitas terbaik. Menyimpan edisi-edisi tersebut terlebih dahulu akan sangat masuk akal. Anda mungkin pada akhirnya ingin menyimpan semua edisi, karena dalam beberapa kasus metadata mungkin ditandai secara tidak benar, atau mungkin ada kompromi yang tidak diketahui antara edisi-edisi tersebut (misalnya, "edisi terbaik" mungkin terbaik dalam banyak hal tetapi lebih buruk dalam hal lain, seperti film dengan resolusi lebih tinggi tetapi tanpa subtitle). Anda juga dapat mencari database metadata Anda untuk menemukan hal-hal menarik. Apa file terbesar yang di-host, dan mengapa begitu besar? Apa file terkecil? Apakah ada pola menarik atau tak terduga terkait kategori tertentu, bahasa, dan sebagainya? Apakah ada judul yang duplikat atau sangat mirip? Apakah ada pola kapan data ditambahkan, seperti satu hari di mana banyak file ditambahkan sekaligus? Anda sering dapat belajar banyak dengan melihat dataset dengan cara yang berbeda. Dalam kasus kami, kami mendeduplikasi buku Z-Library terhadap hash md5 di Library Genesis, sehingga menghemat banyak waktu unduh dan ruang disk. Ini adalah situasi yang cukup unik. Dalam kebanyakan kasus, tidak ada database komprehensif tentang file mana yang sudah terjaga dengan baik oleh sesama pembajak. Ini sendiri adalah peluang besar bagi seseorang di luar sana. Akan sangat bagus untuk memiliki gambaran yang diperbarui secara teratur tentang hal-hal seperti musik dan film yang sudah banyak disebarkan di situs web torrent, dan karenanya menjadi prioritas lebih rendah untuk dimasukkan dalam mirror bajak laut. 6. Distribusi Anda memiliki data, sehingga memberi Anda kepemilikan mirror bajak laut pertama di dunia dari target Anda (kemungkinan besar). Dalam banyak hal, bagian tersulit sudah selesai, tetapi bagian yang paling berisiko masih di depan Anda. Bagaimanapun, sejauh ini Anda telah beroperasi secara diam-diam; terbang di bawah radar. Yang harus Anda lakukan adalah menggunakan VPN yang baik sepanjang waktu, tidak mengisi detail pribadi Anda dalam formulir apa pun (tentu saja), dan mungkin menggunakan sesi browser khusus (atau bahkan komputer yang berbeda). Sekarang Anda harus mendistribusikan data. Dalam kasus kami, kami pertama kali ingin menyumbangkan buku kembali ke Library Genesis, tetapi kemudian dengan cepat menemukan kesulitan dalam hal itu (penyortiran fiksi vs non-fiksi). Jadi kami memutuskan untuk distribusi menggunakan torrent gaya Library Genesis. Jika Anda memiliki kesempatan untuk berkontribusi pada proyek yang sudah ada, maka itu bisa menghemat banyak waktu Anda. Namun, saat ini tidak banyak mirror bajak laut yang terorganisir dengan baik di luar sana. Jadi katakanlah Anda memutuskan untuk mendistribusikan torrent sendiri. Cobalah untuk menjaga file-file tersebut kecil, sehingga mudah untuk dimirror di situs web lain. Anda kemudian harus menanam torrent tersebut sendiri, sambil tetap anonim. Anda dapat menggunakan VPN (dengan atau tanpa port forwarding), atau membayar dengan Bitcoin yang diacak untuk Seedbox. Jika Anda tidak tahu apa arti beberapa istilah tersebut, Anda akan memiliki banyak bacaan yang harus dilakukan, karena penting bagi Anda untuk memahami kompromi risiko di sini. Anda dapat meng-host file torrent itu sendiri di situs web torrent yang ada. Dalam kasus kami, kami memilih untuk benar-benar meng-host situs web, karena kami juga ingin menyebarkan filosofi kami dengan cara yang jelas. Anda dapat melakukan ini sendiri dengan cara yang serupa (kami menggunakan Njalla untuk domain dan hosting kami, dibayar dengan Bitcoin yang diacak), tetapi juga jangan ragu untuk menghubungi kami agar kami dapat meng-host torrent Anda. Kami ingin membangun indeks komprehensif dari mirror bajak laut seiring waktu, jika ide ini diterima. Adapun pemilihan VPN, banyak yang telah ditulis tentang ini, jadi kami hanya akan mengulangi saran umum untuk memilih berdasarkan reputasi. Kebijakan tanpa log yang telah diuji di pengadilan dengan rekam jejak panjang dalam melindungi privasi adalah opsi risiko terendah, menurut kami. Perhatikan bahwa bahkan ketika Anda melakukan semuanya dengan benar, Anda tidak pernah bisa mencapai risiko nol. Misalnya, saat menanam torrent Anda, aktor negara yang sangat termotivasi mungkin dapat melihat aliran data masuk dan keluar untuk server VPN, dan menyimpulkan siapa Anda. Atau Anda bisa saja melakukan kesalahan. Kami mungkin sudah melakukannya, dan akan melakukannya lagi. Untungnya, negara-negara tidak terlalu peduli <em>tentang</em> pembajakan. Satu keputusan yang harus dibuat untuk setiap proyek adalah apakah akan mempublikasikannya menggunakan identitas yang sama seperti sebelumnya, atau tidak. Jika Anda terus menggunakan nama yang sama, maka kesalahan dalam keamanan operasional dari proyek sebelumnya dapat kembali menghantui Anda. Tetapi mempublikasikan dengan nama yang berbeda berarti Anda tidak membangun reputasi yang lebih lama. Kami memilih untuk memiliki keamanan operasional yang kuat sejak awal sehingga kami dapat terus menggunakan identitas yang sama, tetapi kami tidak akan ragu untuk mempublikasikan dengan nama yang berbeda jika kami melakukan kesalahan atau jika keadaan memerlukannya. Menyebarkan berita bisa jadi rumit. Seperti yang kami katakan, ini masih merupakan komunitas yang niche. Kami awalnya memposting di Reddit, tetapi benar-benar mendapatkan perhatian di Hacker News. Untuk saat ini rekomendasi kami adalah mempostingnya di beberapa tempat dan melihat apa yang terjadi. Dan sekali lagi, hubungi kami. Kami ingin menyebarkan berita tentang lebih banyak upaya pengarsipan bajak laut. 1. Pemilihan domain / filosofi Tidak ada kekurangan pengetahuan dan warisan budaya yang perlu diselamatkan, yang bisa sangat membingungkan. Itulah mengapa seringkali berguna untuk meluangkan waktu sejenak dan memikirkan apa kontribusi Anda. Setiap orang memiliki cara berpikir yang berbeda tentang ini, tetapi berikut adalah beberapa pertanyaan yang dapat Anda tanyakan pada diri sendiri: Dalam kasus kami, kami sangat peduli tentang pelestarian jangka panjang ilmu pengetahuan. Kami tahu tentang Library Genesis, dan bagaimana itu sepenuhnya dimirror berkali-kali menggunakan torrent. Kami menyukai ide itu. Kemudian suatu hari, salah satu dari kami mencoba mencari beberapa buku teks ilmiah di Library Genesis, tetapi tidak menemukannya, yang meragukan seberapa lengkapnya itu. Kami kemudian mencari buku teks tersebut secara online, dan menemukannya di tempat lain, yang menanamkan benih untuk proyek kami. Bahkan sebelum kami tahu tentang Z-Library, kami memiliki ide untuk tidak mencoba mengumpulkan semua buku tersebut secara manual, tetapi fokus pada memirror koleksi yang ada, dan menyumbangkannya kembali ke Library Genesis. Keterampilan apa yang Anda miliki yang dapat Anda manfaatkan? Misalnya, jika Anda adalah ahli keamanan online, Anda dapat menemukan cara untuk mengatasi pemblokiran IP untuk target yang aman. Jika Anda hebat dalam mengorganisir komunitas, maka mungkin Anda bisa mengumpulkan beberapa orang untuk mencapai tujuan. Namun, berguna untuk mengetahui beberapa pemrograman, setidaknya untuk menjaga keamanan operasional yang baik selama proses ini. Apa area dengan pengaruh tinggi yang bisa Anda fokuskan? Jika Anda akan menghabiskan X jam untuk mengarsipkan bajakan, lalu bagaimana Anda bisa mendapatkan "hasil maksimal"? Apa cara unik yang Anda pikirkan tentang ini? Anda mungkin memiliki beberapa ide atau pendekatan menarik yang mungkin terlewatkan oleh orang lain. Berapa banyak waktu yang Anda miliki untuk ini? Saran kami adalah memulai dari yang kecil dan melakukan proyek yang lebih besar seiring Anda terbiasa, tetapi ini bisa sangat menyita waktu. Mengapa Anda tertarik dengan ini? Apa yang Anda minati? Jika kita bisa mendapatkan sekelompok orang yang semuanya mengarsipkan hal-hal yang mereka pedulikan secara khusus, itu akan mencakup banyak hal! Anda akan tahu lebih banyak daripada orang rata-rata tentang minat Anda, seperti data penting apa yang harus disimpan, koleksi dan komunitas online terbaik, dan sebagainya. 3. Pengikisan metadata Tanggal ditambahkan/dimodifikasi: sehingga Anda dapat kembali nanti dan mengunduh file yang belum Anda unduh sebelumnya (meskipun Anda sering juga dapat menggunakan ID atau hash untuk ini). Hash (md5, sha1): untuk memastikan bahwa Anda mengunduh file dengan benar. ID: bisa berupa ID internal, tetapi ID seperti ISBN atau DOI juga berguna. Nama file / lokasi Deskripsi, kategori, tag, penulis, bahasa, dll. Ukuran: untuk menghitung berapa banyak ruang disk yang Anda butuhkan. Mari kita menjadi sedikit lebih teknis di sini. Untuk benar-benar mengikis metadata dari situs web, kami menjaga semuanya tetap sederhana. Kami menggunakan skrip Python, kadang-kadang curl, dan database MySQL untuk menyimpan hasilnya. Kami belum menggunakan perangkat lunak pengikisan canggih yang dapat memetakan situs web yang kompleks, karena sejauh ini kami hanya perlu mengikis satu atau dua jenis halaman dengan hanya menjumlahkan melalui id dan mem-parsing HTML. Jika tidak ada halaman yang mudah dijumlahkan, maka Anda mungkin memerlukan perayap yang tepat yang mencoba menemukan semua halaman. Sebelum Anda mulai mengikis seluruh situs web, cobalah melakukannya secara manual untuk sementara waktu. Telusuri beberapa lusin halaman sendiri, untuk mendapatkan gambaran tentang cara kerjanya. Terkadang Anda akan langsung menghadapi blokir IP atau perilaku menarik lainnya dengan cara ini. Hal yang sama berlaku untuk pengikisan data: sebelum terlalu dalam ke target ini, pastikan Anda benar-benar dapat mengunduh datanya secara efektif. Untuk mengatasi pembatasan, ada beberapa hal yang bisa Anda coba. Apakah ada alamat IP atau server lain yang menyimpan data yang sama tetapi tidak memiliki pembatasan yang sama? Apakah ada titik akhir API yang tidak memiliki pembatasan, sementara yang lain memiliki? Pada tingkat pengunduhan berapa IP Anda diblokir, dan untuk berapa lama? Atau apakah Anda tidak diblokir tetapi diperlambat? Bagaimana jika Anda membuat akun pengguna, bagaimana hal itu mengubah keadaan? Bisakah Anda menggunakan HTTP/2 untuk menjaga koneksi tetap terbuka, dan apakah itu meningkatkan tingkat di mana Anda dapat meminta halaman? Apakah ada halaman yang mencantumkan beberapa file sekaligus, dan apakah informasi yang tercantum di sana cukup? Hal-hal yang mungkin ingin Anda simpan termasuk: Kami biasanya melakukan ini dalam dua tahap. Pertama, kami mengunduh file HTML mentah, biasanya langsung ke MySQL (untuk menghindari banyak file kecil, yang akan kita bahas lebih lanjut di bawah). Kemudian, dalam langkah terpisah, kami melalui file HTML tersebut dan mem-parsingnya ke dalam tabel MySQL yang sebenarnya. Dengan cara ini Anda tidak perlu mengunduh semuanya dari awal jika Anda menemukan kesalahan dalam kode parsing Anda, karena Anda dapat memproses ulang file HTML dengan kode baru. Ini juga sering lebih mudah untuk memparallelkan langkah pemrosesan, sehingga menghemat waktu (dan Anda dapat menulis kode pemrosesan saat pengikisan sedang berjalan, daripada harus menulis kedua langkah sekaligus). Akhirnya, perhatikan bahwa untuk beberapa target, pengambilan metadata adalah satu-satunya yang ada. Ada beberapa koleksi metadata besar di luar sana yang tidak terjaga dengan baik. Judul Pemilihan domain / filosofi: Di mana Anda ingin fokus, dan mengapa? Apa saja minat, keterampilan, dan keadaan unik Anda yang dapat Anda manfaatkan? Pemilihan target: Koleksi spesifik mana yang akan Anda mirror? Pengambilan metadata: Mengkatalogkan informasi tentang file, tanpa benar-benar mengunduh file (yang seringkali jauh lebih besar) itu sendiri. Pemilihan data: Berdasarkan metadata, mempersempit data mana yang paling relevan untuk diarsipkan saat ini. Bisa jadi semuanya, tetapi seringkali ada cara yang masuk akal untuk menghemat ruang dan bandwidth. Pengambilan data: Benar-benar mendapatkan data tersebut. Distribusi: Mengemasnya dalam bentuk torrent, mengumumkannya di suatu tempat, membuat orang menyebarkannya. 5. Pengambilan Data Sekarang Anda siap untuk benar-benar mengunduh data dalam jumlah besar. Seperti yang disebutkan sebelumnya, pada titik ini Anda seharusnya sudah secara manual mengunduh sejumlah file, untuk lebih memahami perilaku dan batasan target. Namun, masih akan ada kejutan yang menanti Anda begitu Anda benar-benar mulai mengunduh banyak file sekaligus. Saran kami di sini adalah untuk tetap sederhana. Mulailah dengan hanya mengunduh sejumlah file. Anda dapat menggunakan Python, dan kemudian memperluas ke beberapa thread. Tetapi kadang-kadang bahkan lebih sederhana adalah dengan menghasilkan file Bash langsung dari database, dan kemudian menjalankan beberapa di antaranya di beberapa jendela terminal untuk meningkatkan skala. Trik teknis cepat yang layak disebutkan di sini adalah menggunakan OUTFILE di MySQL, yang dapat Anda tulis di mana saja jika Anda menonaktifkan "secure_file_priv" di mysqld.cnf (dan pastikan juga untuk menonaktifkan/menggantikan AppArmor jika Anda menggunakan Linux). Kami menyimpan data pada hard disk sederhana. Mulailah dengan apa pun yang Anda miliki, dan kembangkan perlahan. Bisa jadi menakutkan untuk memikirkan menyimpan ratusan TB data. Jika itu adalah situasi yang Anda hadapi, cukup keluarkan subset yang baik terlebih dahulu, dan dalam pengumuman Anda minta bantuan untuk menyimpan sisanya. Jika Anda ingin mendapatkan lebih banyak hard drive sendiri, maka r/DataHoarder memiliki beberapa sumber daya yang baik untuk mendapatkan penawaran yang bagus. Cobalah untuk tidak terlalu khawatir tentang sistem file yang mewah. Mudah terjebak dalam lubang kelinci untuk mengatur hal-hal seperti ZFS. Satu detail teknis yang perlu diperhatikan adalah bahwa banyak sistem file tidak menangani dengan baik banyak file. Kami menemukan bahwa solusi sederhana adalah dengan membuat beberapa direktori, misalnya untuk rentang ID yang berbeda atau prefiks hash. Setelah mengunduh data, pastikan untuk memeriksa integritas file menggunakan hash dalam metadata, jika tersedia. 2. Pemilihan target Dapat diakses: tidak menggunakan banyak lapisan perlindungan untuk mencegah Anda mengikis metadata dan data mereka. Wawasan khusus: Anda memiliki informasi khusus tentang target ini, seperti Anda entah bagaimana memiliki akses khusus ke koleksi ini, atau Anda menemukan cara untuk mengalahkan pertahanan mereka. Ini tidak diperlukan (proyek kami yang akan datang tidak melakukan sesuatu yang istimewa), tetapi tentu saja membantu! Besar Jadi, kita sudah memiliki area yang kita lihat, sekarang koleksi spesifik mana yang akan kita mirror? Ada beberapa hal yang membuat target yang baik: Ketika kami menemukan buku teks sains kami di situs web selain Library Genesis, kami mencoba mencari tahu bagaimana mereka bisa sampai ke internet. Kami kemudian menemukan Z-Library, dan menyadari bahwa meskipun sebagian besar buku tidak pertama kali muncul di sana, mereka akhirnya berakhir di sana. Kami belajar tentang hubungannya dengan Library Genesis, dan struktur insentif (keuangan) serta antarmuka pengguna yang unggul, yang keduanya membuatnya menjadi koleksi yang jauh lebih lengkap. Kami kemudian melakukan beberapa pengikisan metadata dan data awal, dan menyadari bahwa kami bisa mengatasi batas unduhan IP mereka, memanfaatkan akses khusus salah satu anggota kami ke banyak server proxy. Saat Anda menjelajahi target yang berbeda, sudah penting untuk menyembunyikan jejak Anda dengan menggunakan VPN dan alamat email sekali pakai, yang akan kita bahas lebih lanjut nanti. Unik: tidak sudah banyak dicakup oleh proyek lain. Ketika kami melakukan sebuah proyek, ada beberapa fase: Ini bukan fase yang sepenuhnya independen, dan seringkali wawasan dari fase selanjutnya mengarahkan Anda kembali ke fase sebelumnya. Misalnya, selama pengambilan metadata Anda mungkin menyadari bahwa target yang Anda pilih memiliki mekanisme pertahanan di luar kemampuan Anda (seperti pemblokiran IP), jadi Anda kembali dan mencari target yang berbeda. - Anna dan tim (<a %(reddit)s>Reddit</a>) Buku lengkap dapat ditulis tentang <em>mengapa</em> pelestarian digital secara umum, dan arsip bajak laut pada khususnya, tetapi mari kita berikan pengantar singkat bagi mereka yang tidak terlalu akrab. Dunia sedang memproduksi lebih banyak pengetahuan dan budaya daripada sebelumnya, tetapi juga lebih banyak yang hilang daripada sebelumnya. Umat manusia sebagian besar mempercayakan warisan ini kepada perusahaan seperti penerbit akademik, layanan streaming, dan perusahaan media sosial, dan mereka sering kali tidak terbukti menjadi pengelola yang baik. Lihatlah dokumenter Digital Amnesia, atau benar-benar pembicaraan apa pun oleh Jason Scott. Ada beberapa institusi yang melakukan pekerjaan yang baik dalam mengarsipkan sebanyak yang mereka bisa, tetapi mereka terikat oleh hukum. Sebagai bajak laut, kami berada dalam posisi unik untuk mengarsipkan koleksi yang tidak dapat mereka sentuh, karena penegakan hak cipta atau pembatasan lainnya. Kami juga dapat mencerminkan koleksi berkali-kali, di seluruh dunia, sehingga meningkatkan peluang pelestarian yang tepat. Untuk saat ini, kami tidak akan membahas diskusi tentang pro dan kontra dari kekayaan intelektual, moralitas melanggar hukum, renungan tentang sensor, atau masalah akses ke pengetahuan dan budaya. Dengan semua itu disingkirkan, mari kita selami <em>bagaimana</em>. Kami akan berbagi bagaimana tim kami menjadi arsiparis bajak laut, dan pelajaran yang kami pelajari sepanjang jalan. Ada banyak tantangan ketika Anda memulai perjalanan ini, dan semoga kami dapat membantu Anda melalui beberapa di antaranya. Cara menjadi arsiparis bajak laut Tantangan pertama mungkin mengejutkan. Ini bukan masalah teknis, atau masalah hukum. Ini adalah masalah psikologis. Sebelum kita mulai, dua pembaruan tentang Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>): Kami mendapatkan beberapa donasi yang sangat dermawan. Yang pertama adalah $10k dari individu anonim yang juga telah mendukung "bookwarrior", pendiri asli Library Genesis. Terima kasih khusus kepada bookwarrior karena memfasilitasi donasi ini. Yang kedua adalah $10k lagi dari donor anonim, yang menghubungi kami setelah rilis terakhir kami, dan terinspirasi untuk membantu. Kami juga menerima sejumlah donasi yang lebih kecil. Terima kasih banyak atas semua dukungan dermawan Anda. Kami memiliki beberapa proyek baru yang menarik dalam perencanaan yang akan didukung oleh ini, jadi tetaplah terhubung. Kami mengalami beberapa kesulitan teknis dengan ukuran rilis kedua kami, tetapi torrent kami sekarang sudah aktif dan melakukan seeding. Kami juga mendapatkan tawaran dermawan dari individu anonim untuk melakukan seeding koleksi kami di server berkecepatan sangat tinggi mereka, jadi kami melakukan unggahan khusus ke mesin mereka, setelah itu semua orang yang mengunduh koleksi tersebut harus melihat peningkatan besar dalam kecepatan. Postingan blog Hai, saya Anna. Saya menciptakan <a %(wikipedia_annas_archive)s>Arsip Anna</a>, shadow library terbesar di dunia. Ini adalah blog pribadi saya, di mana saya dan rekan tim saya menulis tentang pembajakan, pelestarian digital, dan lainnya. Terhubung dengan saya di <a %(reddit)s>Reddit</a>. Perhatikan bahwa situs web ini hanya blog. Kami hanya menyimpan kata-kata kami sendiri di sini. Tidak ada torrent atau file berhak cipta lainnya yang di-host atau ditautkan di sini. <strong>Perpustakaan</strong> - Seperti kebanyakan perpustakaan, kami fokus terutama pada materi tertulis seperti buku. Kami mungkin akan berkembang ke jenis media lain di masa depan. <strong>Mirror</strong> - Kami murni merupakan mirror dari perpustakaan yang ada. Kami fokus pada pelestarian, bukan pada membuat buku mudah dicari dan diunduh (akses) atau membangun komunitas besar orang yang berkontribusi buku baru (sumber). <strong>Bajak Laut</strong> - Kami dengan sengaja melanggar undang-undang hak cipta di sebagian besar negara. Ini memungkinkan kami melakukan sesuatu yang tidak dapat dilakukan oleh entitas legal: memastikan buku-buku di-mirror-kan seluas mungkin. <em>Kami tidak menautkan ke file dari blog ini. Silakan temukan sendiri.</em> - Anna dan tim (<a %(reddit)s>Reddit</a>) Proyek ini (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>) bertujuan untuk berkontribusi pada pelestarian dan pembebasan pengetahuan manusia. Kami membuat kontribusi kecil dan sederhana kami, mengikuti jejak para pendahulu yang hebat. Fokus dari proyek ini digambarkan oleh namanya: Perpustakaan pertama yang kami mirror adalah Z-Library. Ini adalah perpustakaan yang populer (dan ilegal). Mereka telah mengambil koleksi Library Genesis dan membuatnya mudah dicari. Selain itu, mereka sangat efektif dalam meminta kontribusi buku baru, dengan memberikan insentif kepada pengguna yang berkontribusi dengan berbagai keuntungan. Saat ini mereka tidak mengembalikan buku-buku baru ini ke Library Genesis. Dan tidak seperti Library Genesis, mereka tidak membuat koleksi mereka mudah di-mirror, yang mencegah pelestarian yang luas. Ini penting untuk model bisnis mereka, karena mereka mengenakan biaya untuk mengakses koleksi mereka secara massal (lebih dari 10 buku per hari). Kami tidak membuat penilaian moral tentang memungut biaya untuk akses massal ke koleksi buku ilegal. Tidak diragukan lagi bahwa Z-Library telah berhasil memperluas akses ke pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bagian kami: memastikan pelestarian jangka panjang dari koleksi pribadi ini. Kami ingin mengundang Anda untuk membantu melestarikan dan membebaskan pengetahuan manusia dengan mengunduh dan menyebarkan torrent kami. Lihat halaman proyek untuk informasi lebih lanjut tentang bagaimana data diatur. Kami juga sangat mengundang Anda untuk menyumbangkan ide-ide Anda tentang koleksi mana yang harus di-mirror selanjutnya, dan bagaimana melakukannya. Bersama-sama kita bisa mencapai banyak hal. Ini hanyalah kontribusi kecil di antara banyak lainnya. Terima kasih, untuk semua yang Anda lakukan. Memperkenalkan Mirror Perpustakaan Bajak Laut: Melestarikan 7TB buku (yang tidak ada di Libgen) 10% odari warisan tertulis umat manusia dilestarikan selamanya <strong>Google.</strong> Bagaimanapun, mereka melakukan penelitian ini untuk Google Books. Namun, metadata mereka tidak dapat diakses secara massal dan cukup sulit untuk diambil. <strong>Berbagai sistem perpustakaan dan arsip individu.</strong> Ada perpustakaan dan arsip yang belum diindeks dan digabungkan oleh salah satu yang di atas, sering kali karena mereka kekurangan dana, atau karena alasan lain tidak ingin berbagi data mereka dengan Open Library, OCLC, Google, dan sebagainya. Banyak dari ini memiliki catatan digital yang dapat diakses melalui internet, dan sering kali tidak terlindungi dengan baik, jadi jika Anda ingin membantu dan bersenang-senang mempelajari tentang sistem perpustakaan yang aneh, ini adalah titik awal yang bagus. <strong>ISBNdb.</strong> Ini adalah topik dari postingan blog ini. ISBNdb mengambil data dari berbagai situs web untuk metadata buku, khususnya data harga, yang kemudian mereka jual kepada penjual buku, sehingga mereka dapat menetapkan harga buku mereka sesuai dengan pasar lainnya. Karena ISBN cukup universal saat ini, mereka secara efektif membangun “halaman web untuk setiap buku”. <strong>Open Library.</strong> Seperti yang disebutkan sebelumnya, ini adalah misi utama mereka. Mereka telah mendapatkan sejumlah besar data perpustakaan dari perpustakaan yang bekerja sama dan arsip nasional, dan terus melakukannya. Mereka juga memiliki pustakawan sukarelawan dan tim teknis yang mencoba mendeduplikasi catatan, dan menandainya dengan berbagai jenis metadata. Yang terbaik dari semuanya, dataset mereka sepenuhnya terbuka. Anda dapat dengan mudah <a %(openlibrary)s>mengunduhnya</a>. <strong>WorldCat.</strong> Ini adalah situs web yang dijalankan oleh organisasi nirlaba OCLC, yang menjual sistem manajemen perpustakaan. Mereka mengumpulkan metadata buku dari banyak perpustakaan, dan membuatnya tersedia melalui situs web WorldCat. Namun, mereka juga menghasilkan uang dengan menjual data ini, jadi tidak tersedia untuk diunduh secara massal. Mereka memiliki beberapa dataset massal yang lebih terbatas yang tersedia untuk diunduh, bekerja sama dengan perpustakaan tertentu. 1. Untuk beberapa definisi "selamanya" yang masuk akal. ;) 2. Tentu saja, warisan tertulis umat manusia jauh lebih dari sekadar buku, terutama saat ini. Demi posting ini dan rilis terbaru kami, kami fokus pada buku, tetapi minat kami meluas lebih jauh. 3. Ada banyak hal lain yang bisa dikatakan tentang Aaron Swartz, tetapi kami hanya ingin menyebutkannya secara singkat, karena dia memainkan peran penting dalam cerita ini. Seiring berjalannya waktu, lebih banyak orang mungkin menemukan namanya untuk pertama kalinya, dan kemudian dapat menyelami lebih dalam sendiri. <strong>Salinan fisik.</strong> Jelas ini tidak terlalu membantu, karena mereka hanya duplikat dari materi yang sama. Akan sangat keren jika kita bisa melestarikan semua anotasi yang dibuat orang dalam buku, seperti “coretan di pinggir” yang terkenal dari Fermat. Namun sayangnya, itu akan tetap menjadi impian seorang arsiparis. <strong>“Edisi”.</strong> Di sini Anda menghitung setiap versi unik dari sebuah buku. Jika ada yang berbeda tentangnya, seperti sampul yang berbeda atau kata pengantar yang berbeda, itu dihitung sebagai edisi yang berbeda. <strong>Berkas.</strong> Saat bekerja dengan perpustakaan bayangan seperti Library Genesis, Sci-Hub, atau Z-Library, ada pertimbangan tambahan. Bisa ada beberapa pemindaian dari edisi yang sama. Dan orang-orang dapat membuat versi yang lebih baik dari berkas yang ada, dengan memindai teks menggunakan OCR, atau memperbaiki halaman yang dipindai pada sudut tertentu. Kami ingin hanya menghitung berkas-berkas ini sebagai satu edisi, yang memerlukan metadata yang baik, atau deduplikasi menggunakan ukuran kesamaan dokumen. <strong>“Karya”.</strong> Misalnya “Harry Potter dan Kamar Rahasia” sebagai konsep logis, mencakup semua versinya, seperti terjemahan dan cetakan ulang yang berbeda. Ini adalah definisi yang agak berguna, tetapi bisa sulit untuk menentukan batasan apa yang dihitung. Misalnya, kita mungkin ingin melestarikan terjemahan yang berbeda, meskipun cetakan ulang dengan perbedaan kecil mungkin tidak sepenting itu. - Anna dan tim (<a %(reddit)s>Reddit</a>) Dengan Mirror Perpustakaan Bajak Laut (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>), tujuan kami adalah mengambil semua buku di dunia, dan melestarikannya selamanya.<sup>1</sup> Antara torrent Z-Library kami, dan torrent Library Genesis asli, kami memiliki 11.783.153 file. Tapi berapa banyak sebenarnya? Jika kita benar-benar mendeduplikasi file-file tersebut, berapa persentase dari semua buku di dunia yang telah kita lestarikan? Kami benar-benar ingin memiliki sesuatu seperti ini: Mari kita mulai dengan beberapa angka kasar: Di Z-Library/Libgen dan Open Library ada banyak buku lebih banyak daripada ISBN unik. Apakah itu berarti banyak dari buku-buku tersebut tidak memiliki ISBN, atau apakah metadata ISBNnya hanya hilang? Kami mungkin dapat menjawab pertanyaan ini dengan kombinasi pencocokan otomatis berdasarkan atribut lain (judul, penulis, penerbit, dll), menarik lebih banyak sumber data, dan mengekstraksi ISBN dari pemindaian buku yang sebenarnya (dalam kasus Z-Library/Libgen). Berapa banyak dari ISBN tersebut yang unik? Ini paling baik diilustrasikan dengan diagram Venn: Untuk lebih tepatnya: Kami terkejut dengan betapa sedikitnya tumpang tindih yang ada! ISBNdb memiliki sejumlah besar ISBN yang tidak muncul baik di Z-Library maupun Open Library, dan hal yang sama berlaku (dalam tingkat yang lebih kecil namun tetap signifikan) untuk kedua lainnya. Ini menimbulkan banyak pertanyaan baru. Seberapa banyak pencocokan otomatis akan membantu dalam menandai buku-buku yang tidak ditandai dengan ISBN? Apakah akan ada banyak kecocokan dan oleh karena itu meningkatkan tumpang tindih? Juga, apa yang akan terjadi jika kita menambahkan dataset ke-4 atau ke-5? Seberapa banyak tumpang tindih yang akan kita lihat kemudian? Ini memberi kita titik awal. Kita sekarang dapat melihat semua ISBN yang tidak ada dalam dataset Z-Library, dan yang juga tidak cocok dengan bidang judul/penulis. Itu dapat memberi kita pegangan untuk melestarikan semua buku di dunia: pertama dengan mengumpulkan internet untuk pemindaian, kemudian dengan pergi ke kehidupan nyata untuk memindai buku. Yang terakhir bahkan bisa didanai oleh masyarakat, atau didorong oleh "hadiah" dari orang-orang yang ingin melihat buku-buku tertentu didigitalkan. Semua itu adalah cerita untuk waktu yang berbeda. Jika Anda ingin membantu dengan salah satu dari ini — analisis lebih lanjut; mengumpulkan lebih banyak metadata; menemukan lebih banyak buku; OCR buku; melakukan ini untuk domain lain (misalnya makalah, buku audio, film, acara TV, majalah) atau bahkan membuat beberapa data ini tersedia untuk hal-hal seperti pelatihan model bahasa besar / ML — silakan hubungi saya (<a %(reddit)s>Reddit</a>). Jika Anda tertarik secara khusus pada analisis data, kami sedang bekerja untuk membuat dataset dan skrip kami tersedia dalam format yang lebih mudah digunakan. Akan sangat bagus jika Anda bisa langsung menyalin notebook dan mulai bermain dengan ini. Akhirnya, jika Anda ingin mendukung pekerjaan ini, mohon pertimbangkan untuk memberikan donasi. Ini adalah operasi yang sepenuhnya dijalankan oleh sukarelawan, dan kontribusi Anda membuat perbedaan besar. Setiap sedikit membantu. Untuk saat ini kami menerima donasi dalam bentuk kripto; lihat halaman Donasi di Arsip Anna. Untuk persentase, kita memerlukan penyebut: jumlah total buku yang pernah diterbitkan.<sup>2</sup> Sebelum Google Books berakhir, seorang insinyur di proyek tersebut, Leonid Taycher, <a %(booksearch_blogspot)s>mencoba memperkirakan</a> angka ini. Dia datang — dengan nada bercanda — dengan 129.864.880 (“setidaknya sampai hari Minggu”). Dia memperkirakan angka ini dengan membangun basis data terpadu dari semua buku di dunia. Untuk ini, dia mengumpulkan berbagai datasets dan kemudian menggabungkannya dengan berbagai cara. Sebagai catatan singkat, ada orang lain yang mencoba mengkatalogkan semua buku di dunia: Aaron Swartz, aktivis digital yang telah meninggal dan salah satu pendiri Reddit.<sup>3</sup> Dia <a %(youtube)s>memulai Open Library</a> dengan tujuan “satu halaman web untuk setiap buku yang pernah diterbitkan”, menggabungkan data dari berbagai sumber. Dia akhirnya membayar harga tertinggi untuk pekerjaan pelestarian digitalnya ketika dia dituntut karena mengunduh massal makalah akademis, yang mengarah pada bunuh dirinya. Tak perlu dikatakan, ini adalah salah satu alasan mengapa kelompok kami menggunakan nama samaran, dan mengapa kami sangat berhati-hati. Open Library masih dijalankan dengan heroik oleh orang-orang di Internet Archive, melanjutkan warisan Aaron. Kami akan kembali ke topik ini nanti dalam postingan ini. Dalam postingan blog Google, Taycher menjelaskan beberapa tantangan dalam memperkirakan angka ini. Pertama, apa yang dimaksud dengan buku? Ada beberapa definisi yang mungkin: “Edisi” tampaknya menjadi definisi yang paling praktis tentang apa itu “buku”. Secara kebetulan, definisi ini juga digunakan untuk menetapkan nomor ISBN unik. ISBN, atau International Standard Book Number, umumnya digunakan untuk perdagangan internasional, karena terintegrasi dengan sistem barcode internasional (”International Article Number”). Jika Anda ingin menjual buku di toko, itu memerlukan barcode, jadi Anda mendapatkan ISBN. Postingan blog Taycher menyebutkan bahwa meskipun ISBN berguna, mereka tidak universal, karena baru benar-benar diadopsi pada pertengahan tahun tujuh puluhan, dan tidak di seluruh dunia. Namun, ISBN mungkin adalah pengenal edisi buku yang paling banyak digunakan, jadi ini adalah titik awal terbaik kami. Jika kita dapat menemukan semua ISBN di dunia, kita mendapatkan daftar berguna tentang buku mana yang masih perlu dilestarikan. Jadi, dari mana kita mendapatkan data? Ada sejumlah upaya yang ada yang mencoba menyusun daftar semua buku di dunia: Dalam postingan ini, kami dengan senang hati mengumumkan rilis kecil (dibandingkan dengan rilis Z-Library kami sebelumnya). Kami mengambil sebagian besar ISBNdb, dan membuat data tersebut tersedia untuk diunduh melalui torrent di situs web Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>; kami tidak akan menautkannya langsung di sini, cukup cari saja). Ini adalah sekitar 30,9 juta catatan (20GB sebagai <a %(jsonlines)s>JSON Lines</a>; 4,4GB dikompresi). Di situs web mereka, mereka mengklaim bahwa mereka sebenarnya memiliki 32,6 juta catatan, jadi kami mungkin entah bagaimana melewatkan beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Bagaimanapun, untuk saat ini kami tidak akan membagikan secara persis bagaimana kami melakukannya — kami akan meninggalkannya sebagai latihan untuk pembaca. ;-) Yang akan kami bagikan adalah beberapa analisis awal, untuk mencoba mendekati perkiraan jumlah buku di dunia. Kami melihat tiga dataset: dataset ISBNdb baru ini, rilis metadata asli kami yang kami ambil dari perpustakaan bayangan Z-Library (yang mencakup Library Genesis), dan dump data Open Library. Dump ISBNdb, atau Berapa Banyak Buku yang Dilestarikan Selamanya? Jika kita benar-benar mendeduplikasi file dari perpustakaan bayangan, berapa persentase dari semua buku di dunia yang telah kita lestarikan? Pembaruan tentang <a %(wikipedia_annas_archive)s>Arsip Anna</a>, perpustakaan terbuka terbesar dalam sejarah manusia. <em>Desain ulang WorldCat</em> Data <strong>Format?</strong> <a %(blog)s>Kontainer Arsip Anna (AAC)</a>, yang pada dasarnya adalah <a %(jsonlines)s>JSON Lines</a> yang dikompresi dengan <a %(zstd)s>Zstandard</a>, ditambah beberapa semantik standar. Kontainer ini membungkus berbagai jenis catatan, berdasarkan pengumpulan data yang kami lakukan. Setahun yang lalu, kami <a %(blog)s>memulai</a> untuk menjawab pertanyaan ini: <strong>Berapa persentase buku yang telah dilestarikan secara permanen oleh shadow library?</strong> Mari kita lihat beberapa informasi dasar tentang data ini: Setelah sebuah buku masuk ke dalam shadow library data terbuka seperti <a %(wikipedia_library_genesis)s>Library Genesis</a>, dan sekarang <a %(wikipedia_annas_archive)s>Arsip Anna</a>, buku tersebut akan dicerminkan di seluruh dunia (melalui torrent), sehingga praktis melestarikannya selamanya. Untuk menjawab pertanyaan tentang persentase buku yang telah dilestarikan, kita perlu mengetahui penyebutnya: berapa banyak buku yang ada secara total? Dan idealnya kita tidak hanya memiliki angka, tetapi juga metadata yang sebenarnya. Kemudian kita tidak hanya dapat mencocokkannya dengan shadow library, tetapi juga <strong>membuat daftar TODO buku yang tersisa untuk dilestarikan!</strong> Kita bahkan bisa mulai bermimpi tentang upaya crowdsourcing untuk menelusuri daftar TODO ini. Kami mengumpulkan data dari <a %(wikipedia_isbndb_com)s>ISBNdb</a>, dan mengunduh dataset <a %(openlibrary)s>Open Library</a>, tetapi hasilnya tidak memuaskan. Masalah utamanya adalah tidak banyak tumpang tindih ISBN. Lihat diagram Venn ini dari <a %(blog)s>posting blog kami</a>: Kami sangat terkejut dengan betapa sedikitnya tumpang tindih antara ISBNdb dan Open Library, yang keduanya secara bebas menyertakan data dari berbagai sumber, seperti pengumpulan data web dan catatan perpustakaan. Jika keduanya melakukan pekerjaan yang baik dalam menemukan sebagian besar ISBN yang ada di luar sana, lingkaran mereka pasti akan memiliki tumpang tindih yang substansial, atau salah satunya akan menjadi bagian dari yang lain. Ini membuat kami bertanya-tanya, berapa banyak buku yang jatuh <em>sepenuhnya di luar lingkaran ini</em>? Kami membutuhkan database yang lebih besar. Saat itulah kami mengarahkan pandangan kami pada database buku terbesar di dunia: <a %(wikipedia_worldcat)s>WorldCat</a>. Ini adalah database milik organisasi nirlaba <a %(wikipedia_oclc)s>OCLC</a>, yang mengumpulkan catatan metadata dari perpustakaan di seluruh dunia, dengan imbalan memberikan akses kepada perpustakaan tersebut ke dataset lengkap, dan menampilkan mereka dalam hasil pencarian pengguna akhir. Meskipun OCLC adalah organisasi nirlaba, model bisnis mereka mengharuskan melindungi database mereka. Nah, kami minta maaf, teman-teman di OCLC, kami akan membagikannya semuanya. :-) Selama setahun terakhir, kami dengan teliti mengumpulkan semua catatan WorldCat. Pada awalnya, kami mendapatkan keberuntungan. WorldCat baru saja meluncurkan desain ulang situs web mereka secara lengkap (pada Agustus 2022). Ini termasuk perombakan besar-besaran sistem backend mereka, memperkenalkan banyak kelemahan keamanan. Kami segera memanfaatkan kesempatan ini, dan berhasil mengumpulkan ratusan juta (!) catatan dalam beberapa hari saja. Setelah itu, kelemahan keamanan perlahan-lahan diperbaiki satu per satu, hingga yang terakhir yang kami temukan diperbaiki sekitar sebulan yang lalu. Pada saat itu kami sudah memiliki hampir semua catatan, dan hanya mencari catatan dengan kualitas sedikit lebih tinggi. Jadi kami merasa sudah saatnya untuk merilisnya! 1,3 Miliar Pengambilan WorldCat <em><strong>TL;DR:</strong> Arsip Anna mengambil semua data WorldCat (koleksi metadata perpustakaan terbesar di dunia) untuk membuat daftar TODO buku yang perlu dilestarikan.</em> WorldCat Peringatan: posting blog ini telah dihentikan. Kami memutuskan bahwa IPFS belum siap untuk waktu utama. Kami masih akan menautkan ke file di IPFS dari Arsip Anna jika memungkinkan, tetapi kami tidak akan menghostingnya sendiri lagi, juga tidak merekomendasikan orang lain untuk mirror menggunakan IPFS. Silakan lihat halaman Torrents kami jika Anda ingin membantu melestarikan koleksi kami. Bantu seeding Z-Library di IPFS Unduhan dari Peladen Mitra SciDB Pinjam dari sumber eksternal Pinjam dari sumber eksternal (cetak dinonaktifkan) Unduhan eksternal Jelajahi metadata Terkandung dalam torrent Kembali  (+%(num)s bonus) Belum terbayar Telah Dibayar Dibatalkan Kadaluarsa Menunggu Anna untuk mengkonfirmasi tidak sesuai Teks berlanjut di bawah dalam bahasa Inggris. Pergi Atur Ulang Maju Terakhir Jika alamat email Anda tidak berfungsi di forum Libgen, kami sarankan menggunakan <a %(a_mail)s>Proton Mail</a> (gratis). Anda juga bisa <a %(a_manual)s>meminta aktivasi akun secara manual</a>. (kemungkinan perlu <a %(a_browser)s>verifikasi browser</a> — unduhan tak terbatas!) Unduhan jalur cepat rekan #%(number)s (direkomendasikan) (sedikit lebih cepat tetapi dengan daftar tunggu) (Tidak memerlukan verifikasi browser) (tanpa verifikasi browser atau daftar tunggu) (tidak ada daftar tunggu, tetapi bisa sangat lambat) Server Mitra Kecepatan Lambat #%(number)s Buku Audio Komik Buku (fiksi) Buku (nonfiksi) Buku (tak terkategori) Artikel jurnal Majalah Nilai Musik Lainya Dokumen standar Tidak semua halaman dapat dikonversi ke PDF Diberi tanda rusak (red: broken) di Libgen.li Tidak terlihat di Libgen.li Tidak terlihat di Libgen.rs dalam kategori Fiksi Tidak terlihat di Libgen.rs dalam kategori Non-Fiksi Menjalankan exiftool gagal pada file ini Ditandai sebagai “file buruk” di Z-Library Hilang dari Z-Library Ditandai sebagai “spam” di Z-Library Berkas tidak dapat dibuka (misalnya, berkas rusak, DRM) Klaim hak cipta Masalah saat mengunduh (misalnya, tidak dapat terhubung, pesan kesalahan, sangat lambat) Metadata yang tidak benar (misalnya, judul, deskripsi, gambar sampul yang salah) Lainnya Kualitas buruk (misalnya, masalah format, kualitas pemindaian yang buruk, halaman yang hilang) Spam / berkas seharusnya dihapus (misalnya, iklan, konten kasar) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Pustaka Cemerlang Pustakawan Beruntung penyimpanan data yang mempesona Arsiparis Luar Biasa Bonus unduhan Cerlalc Metadata Ceko DuXiu 读秀 Indeks eBook EBSCOhost Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Tidak termasuk “scimag” Libgen.rs Non-Fiksi dan Fiksi Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Perpustakaan Negara Rusia Sci-Hub Melalui Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Unggahan ke AA Z-Library Z-Library Chinese Cari judul, penulis, DOI, ISBN, MD5, … Cari Penulis Deskripsi dan komentar metadata Penyunting Nama dokumen asli Penerbit (kriteria pencarian spesifik) Judul Tahun diterbitkan Detail teknis (dalam bahasa Inggris) Koin ini memiliki jumlah minimum yang lebih tinggi dari biasanya. Silakan pilih durasi yang berbeda atau koin yang berbeda. Permintaan tidak dapat dijalankan. Mohon coba kembali dalam beberapa menit, apabila masih terjadi kendala harap hubungi kami di %(email)s dengan disertai screenshot dan deskripsi kendala. Terjadi kesalahan yang tidak diketahui. Silakan hubungi kami di %(email)s dengan melampirkan tangkapan layar. Terjadi kesalahan dalam proses pembayaran. Mohon menunggu beberapa saat lagi untuk mencoba kembali. Bila kendala masih terjadi setelah lebih dari 24 jam, mohon hubungi kami di %(email)s dengan menyertakan screenshot dan keterangan lengkap. Kami sedang menjalankan pengalangan dana untuk <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">pencadangan</a> pustaka komik terbesar di dunia. Terimakasih atas dukungan kamu! <a href="/donate">Donasi.</a> Jika kamu belum dapat berdonasi, kamu bisa bantu kami lewat rekomendasi ke temen kamu dan follow medsos kami di <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>. Jangan mengirimkan email kepada kami terkait <a %(a_request)s>pengajuan permintaan buku</a><br>atau <a %(a_upload)s>menggungah</a> dokumen ukuran kecil (<10kb). Anna’s Archive DCMA/klaim hak cipta Hubungi kami Reddit Alternatif SLUM (%(unaffiliated)s) tidak terafiliasi Arsip Anna membutuhkan bantuan Anda! Jika Anda berdonasi sekarang, Anda mendapatkan <strong>dua kali</strong> jumlah unduhan cepat. Banyak yang mencoba menjatuhkan kami, tetapi kami melawan. Jika Anda berdonasi bulan ini, Anda mendapatkan <strong>dua kali</strong> jumlah unduhan cepat. Berlaku hingga akhir bulan ini. Menyelamatkan pengetahuan manusia: hadiah liburan yang luar biasa! Keanggotaan akan diperpanjang sesuai. Server mitra tidak tersedia karena penutupan hosting. Mereka akan segera aktif kembali. Untuk meningkatkan ketahanan Arsip Anna, Kami sedang mencari relawan untuk menjalankan mirror. Tersedia cara donasi terbaru: %(method_name)s. Kami mohon pertimbangan Anda untuk %(donate_link_open_tag)sberdonasi</a> — menjalankan website ini tidaklah murah, dan donasi Anda akan sungguh membantu. Terima kasih banyak. Bagikan ke teman dan dapatkan %(percentage)s%% bonus unduhan jalur cepat buat kamu dan temanmu! Kejutkan orang tersayang, beri mereka akun dengan keanggotaan. Kado Valentin terbaik! Pelajari lebih lanjut… Akun Aktivitas Lanjutan Anna’s Blog ↗ Anna’s Software ↗ versi beta Penjelajah Kode Dataset Donasi Unduh berkas FAQ Beranda Perbaiki metadata Data LLM Masuk/Daftar Donasiku Profil Pencarian Keamanan Torrent Terjemahkan ↗ Sukarelawan & Hadiah Unduhan terahir: 📚&nbsp;Perpustakaan open-source dan open-data terbesar di dunia. ⭐️&nbsp;Menyediakan salinan dari Sci-Hub, Library Genesis, Z-Library, dan lainnya. 📈&nbsp;%(book_any)s buku, %(journal_article)s makalah, %(book_comic)s komik, %(magazine)s majalah — tetap ada selamanya.  dan  dan lain-lain DuXiu Antarmuka Pustaka Internet Archive Libgen 📚&nbsp;Pustaka terbuka dan terbesar dalam sejarah manusia. 📈&nbsp;%(book_count)s&nbsp;buku, %(paper_count)s&nbsp;dokumen — dilestarikan selamanya. ⭐️&nbsp;Mirror kami %(libraries)s. Kami menggunakan sumber terbuka %(scraped)s. Semua kode dan data kami sepenuhnya open source. OpenLib Sci-Hub ,  📚 Pustaka sumber terbuka terbesar di dunia.<br>⭐️ Mirrors Scihub, Libgen, Zlib, and more. Z-Lib Anna's Archive Permintaan tidak ditemukan. Kunjungi halamam %(websites)s. Pustaka sumber terbuka terbesar di dunia. Alternatif dari Sci-Hub, Library Genesis, Z-Library, dkk. Cari di Arsip Anna Anna's Archive Silakan segarkan untuk mencoba lagi. <a %(a_contact)s>Hubungi kami</a> jika masalah berlanjut selama beberapa jam. 🔥 Masalah memuat halaman ini <li>1. Ikuti kami di <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, atau <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Sebarkan berita tentang Anna's Archive di Twitter, Reddit, Tiktok, Instagram, di tongkronganmu atau perpustakaan, atau dimanapun kamu pergi! Kami tidak percaya dengan penjagaan gawang— apabila kami dibubarkan kami hanya akan muncul di tempat lain, karena semua kode dan data kami sepenuhnya open source.</li><li>3. Jika anda mampu, pertimbangkan untuk <a href="/donate">donasi</a>.</li><li>4. Bantu <a href="https://translate.annas-software.org/">menerjemahkan</a> website kami ke berbagai bahasa.</li><li>5. Jika anda seorang insinyur perangkat lunak, pertimbangkan untuk berkontribusi ke <a href="https://annas-software.org/">open source</a> kami, atau dengan seeding <a href="/datasets">torrents</a> kami.</li> 10. Buat atau bantu perawatan halaman Wikipedia terkait Arsip Anna dalam bahasamu. 11. Kami sedang mencari tempat pengiklanan. Bila kamu tertarik mengiklankan Arsip Anna ,mohon beri tahu kami. 6. Jika anda memiliki keahlian dibidang keamanan jaringan, kami dapat menggunakan kemampuan anda dibidang offensif maupun defensif. Silahkan periksa halaman <a %(a_security)s>Keamanan</a> kami. 7. Kami sedang mencari ahli pembayaran secara anonim. Dapatkah kamu membantu kami menambahkan metode donasi yang lebih nyaman? PayPal, WeChat, gift cards. Bila anda mengenal seseorang yang ahli dibidang tersebut mohon hubungi kami. 8. Kami membutuhkan kapasitas server yang lebih baik. 9. Kamu dapat membantu dengan melaporkan dokumen yang bermasalah, meningalkan komentar, dan membangun list buku secara langsung lewat halaman web. Kamu juga dapat membantu demgan cara <a %(a_upload)s>unggah lebih banyak buku</a>, atau memperbaiki dokumen bermasalah atau mengatur ulang fokumen yang telah ada menjadi lebih baik. Untuk informasi lebih lanjut tentang cara menjadi sukarelawan, lihat halaman <a %(a_volunteering)s>Sukarelawan & Hadiah</a> kami. Kami sangat percaya dengan penyebaran informasi yang bebas, dan pelestarian pengetahuan dan budaya. Dengan mesin pencari ini, kami membangun di atas bahu raksasa. Kami menghormati secara mendalam kerja keras dari orang-orang yang telah menciptakan berbagai perpustakaan bayangan, dan kami harap mesin pencari ini akan memperluas cakupan mereka. Ikuti perkembangan terbaru kami dengan mengikuti Anna di <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, atau <a href="https://t.me/annasarchiveorg">Telegram</a>. Untuk pertanyaan dan umpan balik, silakan hubungi Anna di %(email)s. ID Akun: %(account_id)s Keluar ❌ Terjadi kesalahan. Mohon muat ulang halaman dan coba lagi. ✅ Anda kini sudah keluar. Muat ulang halaman untuk masuk kembali. Pengunduhan cepat yang digunakan (24 jam terakhir): <strong>%(used)s / %(total)s</strong> Keanggotaan: <strong>%(tier_name)s</strong> hingga %(until_date)s <a %(a_extend)s>(perpanjang)</a> Kamu dapat menggabungkan beberapa membersip ( unduhan jalur cepat untuk 24 jam akan dijalankan secara bersamaan). Keanggotaan: <strong>Tidak Ada</strong> <a %(a_become)s>(menjadi anggota)</a> Hubungi Anna di %(email)s jika Anda tertarik untuk meningkatkan keanggotaan Anda ke tingkat yang lebih tinggi. Profil publik: %(profile_link)s Kunci rahasia (jangan dibagikan!): %(secret_key)s tampilkan Bergabunglah bersama kami! Lakukan peningkatan ke <a %(a_tier)s>tier lebih tinggi</a> untuk bergabung ke dalam grup kami. Grup Telegram eksklusif: %(link)s Akun unduhan yang mana? Masuk Harap jangan hilangkan kode kunci anda! Kunci rahasia yang Anda masukkan salah. Periksa kembali kunci Anda dan coba lagi, atau alternatifnya, jika Anda belum memiliki akun, Anda bisa mendaftar untuk membuat akun baru di bawah ini. Kunci rahasia Masukkan kunci rahasia Anda untuk masuk: Apakah Anda memiliki akun berdasarkan alamat email sebelumnya? Masukkan <a %(a_open)s>email Anda di sini</a>. Daftar akun baru Belum memiliki akun? Registrasi berhasil! Kunci rahasia Anda adalah: <span %(span_key)s>%(key)s</span> Simpan kunci ini dengan hati-hati. Jika Anda kehilangan kuncinya, Anda akan kehilangan akses ke akun Anda. <li %(li_item)s><strong>Simpan Halaman. </strong>Anda dapat menyimpan halaman ini untuk mengambil kembali kunci Anda.</li><li %(li_item)s><strong>Unduh. </strong>Klik <a %(a_download)s>tautan ini</a> untuk mengunduh kunci Anda.</li><li %(li_item)s><strong>Pengelola kata sandi. </strong>Gunakan Pengelola kata sandi (red:Password manager) untuk menyimpan kunci saat Anda memasukkannya di bawah ini.</li> Masuk / Daftar Verifikasi browser Peringatan: kode memiliki karakter Unicode yang salah, dan mungkin berperilaku tidak benar dalam berbagai situasi. Biner mentah dapat didekode dari representasi base64 di URL. Deskripsi Label Awalan URL untuk kode tertentu Website Kode yang dimulai dengan “%(prefix_label)s” Harap jangan mengikis halaman ini. Sebagai gantinya, kami merekomendasikan <a %(a_import)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami, dan menjalankan <a %(a_software)s>kode sumber terbuka</a> kami. Data mentah dapat dijelajahi secara manual melalui file JSON seperti <a %(a_json_file)s>yang ini</a>. Kurang dari %(count)s catatan URL Generik Penjelajah Kode Indeks dari Jelajahi kode yang ditandai pada catatan, berdasarkan awalan. Kolom “catatan” menunjukkan jumlah catatan yang ditandai dengan kode dengan awalan yang diberikan, seperti yang terlihat di mesin pencari (termasuk catatan metadata saja). Kolom “kode” menunjukkan berapa banyak kode sebenarnya yang memiliki awalan yang diberikan. Awalan kode yang dikenal “%(key)s” Lebih lanjut… Awalan %(count)s catatan yang cocok dengan “%(prefix_label)s” page.codes.records_starting_with kode catatan “%%s” akan digantikan dengan nilai kode Cari Arsip Anna Kode URL untuk kode spesifik: “%(url)s” Halaman ini mungkin memerlukan waktu untuk dihasilkan, itulah sebabnya memerlukan captcha Cloudflare. <a %(a_donate)s>Anggota</a> dapat melewati captcha. Penyalahgunaan dilaporkan: Versi yang lebih baik Apakah Anda ingin melaporkan pengguna ini karena perilaku kasar atau tidak pantas? Masalah file: %(file_issue)s komentar tersembunyi Balas Laporkan penyalahgunaan Anda melaporkan pengguna ini karena penyalahgunaan. Klaim hak cipta melaluiemail ini tidak akan ditanggapi: mohon gunakan form yang tersedia. Tampilkan email Kami menerima masukan dan pertanyaan secara terbuka! Meski demikian, dikarnakan banyaknya spam dan email tidak jelas, mohon untuk memeriksa kotak masuk email anda untuk konfirmasi bahwa anda memahami ketentuan dalam menghubungi kami. Kami akan secara otomatis menghapus klaim hak cipta bila menggunakan metode lainya. Untuk DMCA/klaim hak cipta, gunakan <a %(a_copyright)s>form ini</a>. Alamat email URL di Arsip Anna (wajib). Satu per baris. Harap hanya sertakan URL yang menggambarkan edisi buku yang sama persis. Jika Anda ingin mengajukan klaim untuk beberapa buku atau beberapa edisi, harap kirimkan formulir ini beberapa kali. Klaim yang menggabungkan beberapa buku atau edisi akan ditolak. Alamat (wajib) Deskripsi jelas tentang materi sumber (wajib) E-mail (wajib) URL ke materi sumber, satu per baris (wajib). Harap sertakan sebanyak mungkin, untuk membantu kami memverifikasi klaim Anda (misalnya Amazon, WorldCat, Google Books, DOI). ISBN dari materi sumber (jika ada). Satu per baris. Harap hanya sertakan yang benar-benar sesuai dengan edisi yang Anda laporkan klaim hak ciptanya. Nama Anda (wajib) ❌ Terjadi kesalahan. Harap muat ulang halaman dan coba lagi. ✅ Terima kasih telah mengirimkan klaim hak cipta Anda. Kami akan meninjaunya secepat mungkin. Harap muat ulang halaman untuk mengajukan klaim lainnya. <a %(a_openlib)s>URL Open Library</a> dari materi sumber, satu per baris. Harap luangkan waktu untuk mencari materi sumber Anda di Open Library. Ini akan membantu kami memverifikasi klaim Anda. Nomor telepon (wajib) Pernyataan dan tanda tangan (wajib) Kirim klaim Jika Anda memiliki klaim DMCA atau hak cipta lainnya, harap isi formulir ini seakurat mungkin. Jika Anda mengalami masalah, silakan hubungi kami di alamat DMCA khusus kami: %(email)s. Perhatikan bahwa klaim yang dikirimkan melalui email ke alamat ini tidak akan diproses, alamat ini hanya untuk pertanyaan. Harap gunakan formulir di bawah ini untuk mengajukan klaim Anda. Formulir klaim DMCA / Hak Cipta Contoh catatan di Arsip Anna Torrent oleh Arsip Anna Format Kontainer Arsip Anna Skrip untuk mengimpor metadata Jika Anda tertarik untuk mencerminkan dataset ini untuk <a %(a_archival)s>arsip</a> atau tujuan <a %(a_llm)s>pelatihan LLM</a>, silakan hubungi kami. Terakhir diperbarui: %(date)s Situs utama %(source)s Dokumentasi metadata (sebagian besar bidang) File yang dicerminkan oleh Arsip Anna: %(count)s (%(percent)s%%) Sumber Daya Total file: %(count)s Total ukuran file: %(size)s Posting blog kami tentang data ini <a %(duxiu_link)s>Duxiu</a> adalah database besar buku yang dipindai, dibuat oleh <a %(superstar_link)s>SuperStar Digital Library Group</a>. Sebagian besar adalah buku akademik, dipindai untuk membuatnya tersedia secara digital bagi universitas dan perpustakaan. Untuk audiens berbahasa Inggris kami, <a %(princeton_link)s>Princeton</a> dan <a %(uw_link)s>University of Washington</a> memiliki ikhtisar yang baik. Ada juga artikel yang sangat baik yang memberikan lebih banyak latar belakang: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Buku-buku dari Duxiu telah lama dibajak di internet Tiongkok. Biasanya mereka dijual kurang dari satu dolar oleh penjual kembali. Mereka biasanya didistribusikan menggunakan setara Google Drive di Tiongkok, yang sering kali diretas untuk memungkinkan lebih banyak ruang penyimpanan. Beberapa detail teknis dapat ditemukan <a %(link1)s>di sini</a> dan <a %(link2)s>di sini</a>. Meskipun buku-buku tersebut telah didistribusikan secara semi-publik, cukup sulit untuk mendapatkannya dalam jumlah besar. Kami menempatkan ini tinggi di daftar TODO kami, dan mengalokasikan beberapa bulan kerja penuh waktu untuk itu. Namun, pada akhir 2023 seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberi tahu kami bahwa mereka telah melakukan semua pekerjaan ini — dengan biaya besar. Mereka membagikan koleksi lengkap dengan kami, tanpa mengharapkan imbalan apa pun, kecuali jaminan pelestarian jangka panjang. Benar-benar luar biasa. Informasi lebih lanjut dari sukarelawan kami (catatan mentah): Diadaptasi dari <a %(a_href)s>posting blog</a> kami. DuXiu 读秀 %(count)s file page.datasets.files Dataset ini terkait erat dengan <a %(a_datasets_openlib)s>dataset Open Library</a>. Ini berisi pengikisan semua metadata dan sebagian besar file dari Perpustakaan Peminjaman Digital Terkendali IA. Pembaruan dirilis dalam <a %(a_aac)s>format Kontainer Arsip Anna</a>. Catatan ini dirujuk langsung dari dataset Open Library, tetapi juga berisi catatan yang tidak ada di Open Library. Kami juga memiliki sejumlah file data yang dikumpulkan oleh anggota komunitas selama bertahun-tahun. Koleksi ini terdiri dari dua bagian. Anda memerlukan kedua bagian untuk mendapatkan semua data (kecuali torrent yang digantikan, yang dicoret di halaman torrent). Perpustakaan Peminjaman Digital rilisan pertama kami, sebelum kami menstandarisasi pada format <a %(a_aac)s>Kontainer Arsip Anna (AAC)</a>. Berisi metadata (dalam format json dan xml), pdf (dari sistem peminjaman digital acsm dan lcpdf), dan gambar sampul. rilisan baru bertahap, menggunakan AAC. Hanya berisi metadata dengan stempel waktu setelah 2023-01-01, karena sisanya sudah tercakup oleh "ia". Juga semua file pdf, kali ini dari sistem peminjaman acsm dan "bookreader" (pembaca web IA). Meskipun namanya tidak sepenuhnya tepat, kami tetap mengisi file bookreader ke dalam koleksi ia2_acsmpdf_files, karena mereka saling eksklusif. IA Controlled Digital Lending 98%%+ dari file dapat dicari. Misi kami adalah mengarsipkan semua buku di dunia (serta makalah, majalah, dll), dan membuatnya dapat diakses secara luas. Kami percaya bahwa semua buku harus dicerminkan secara luas, untuk memastikan redundansi dan ketahanan. Inilah sebabnya kami mengumpulkan file dari berbagai sumber. Beberapa sumber sepenuhnya terbuka dan dapat dicerminkan secara massal (seperti Sci-Hub). Yang lain tertutup dan protektif, jadi kami mencoba mengikisnya untuk “membebaskan” buku-buku mereka. Yang lainnya berada di antara keduanya. Semua data kami dapat <a %(a_torrents)s>ditorrent</a>, dan semua metadata kami dapat <a %(a_anna_software)s>dihasilkan</a> atau <a %(a_elasticsearch)s>diunduh</a> sebagai database ElasticSearch dan MariaDB. Data mentah dapat dieksplorasi secara manual melalui file JSON seperti <a %(a_dbrecord)s>ini</a>. Metadata Situs web ISBN Terakhir diperbarui: %(isbn_country_date)s (%(link)s) Sumber Daya Badan ISBN Internasional secara teratur merilis rentang yang telah dialokasikan ke badan ISBN nasional. Dari sini kita dapat mengetahui negara, wilayah, atau kelompok bahasa yang dimiliki ISBN ini. Saat ini kami menggunakan data ini secara tidak langsung, melalui perpustakaan Python <a %(a_isbnlib)s>isbnlib</a>. Informasi negara ISBN Ini adalah dump dari banyak panggilan ke isbndb.com selama September 2022. Kami mencoba mencakup semua rentang ISBN. Ini sekitar 30,9 juta catatan. Di situs web mereka, mereka mengklaim bahwa mereka sebenarnya memiliki 32,6 juta catatan, jadi kami mungkin entah bagaimana melewatkan beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Respon JSON hampir mentah dari server mereka. Satu masalah kualitas data yang kami perhatikan adalah bahwa untuk nomor ISBN-13 yang dimulai dengan awalan berbeda dari "978-", mereka masih menyertakan bidang "isbn" yang hanya merupakan nomor ISBN-13 dengan tiga angka pertama dipotong (dan digit cek dihitung ulang). Ini jelas salah, tetapi begitulah cara mereka melakukannya, jadi kami tidak mengubahnya. Masalah potensial lain yang mungkin Anda temui adalah fakta bahwa bidang "isbn13" memiliki duplikat, sehingga Anda tidak dapat menggunakannya sebagai kunci utama dalam database. Bidang "isbn13" + "isbn" yang digabungkan tampaknya unik. Rilis 1 (2022-10-31) Torrent fiksi tertinggal (meskipun ID ~4-6M tidak ditorrent karena tumpang tindih dengan torrent Zlib kami). Posting blog kami tentang rilis buku komik Torrent komik di Arsip Anna Untuk latar belakang dari berbagai cabang Library Genesis, lihat halaman untuk <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li berisi sebagian besar konten dan metadata yang sama dengan Libgen.rs, tetapi memiliki beberapa koleksi tambahan, yaitu komik, majalah, dan dokumen standar. Ini juga telah mengintegrasikan <a %(a_scihub)s>Sci-Hub</a> ke dalam metadata dan mesin pencariannya, yang kami gunakan untuk basis data kami. Metadata untuk perpustakaan ini tersedia secara gratis <a %(a_libgen_li)s>di libgen.li</a>. Namun, server ini lambat dan tidak mendukung melanjutkan koneksi yang terputus. File yang sama juga tersedia di <a %(a_ftp)s>server FTP</a>, yang bekerja lebih baik. Non-fiksi juga tampaknya telah menyimpang, tetapi tanpa torrent baru. Tampaknya ini telah terjadi sejak awal 2022, meskipun kami belum memverifikasinya. Menurut administrator Libgen.li, koleksi “fiction_rus” (fiksi Rusia) harus tercakup oleh torrent yang dirilis secara teratur dari <a %(a_booktracker)s>booktracker.org</a>, terutama torrent <a %(a_flibusta)s>flibusta</a> dan <a %(a_librusec)s>lib.rus.ec</a> (yang kami mirror <a %(a_torrents)s>di sini</a>, meskipun kami belum menetapkan torrent mana yang sesuai dengan file mana). Koleksi fiksi memiliki torrent sendiri (berbeda dari <a %(a_href)s>Libgen.rs</a>) mulai dari %(start)s. Rentang tertentu tanpa torrent (seperti rentang fiksi f_3463000 hingga f_4260000) kemungkinan adalah file Z-Library (atau duplikat lainnya), meskipun kami mungkin ingin melakukan deduplikasi dan membuat torrent untuk file unik lgli dalam rentang ini. Statistik untuk semua koleksi dapat ditemukan <a %(a_href)s>di situs web libgen</a>. Torrent tersedia untuk sebagian besar konten tambahan, terutama torrent untuk komik, majalah, dan dokumen standar telah dirilis dalam kolaborasi dengan Arsip Anna. Perhatikan bahwa file torrent yang merujuk ke “libgen.is” secara eksplisit adalah mirror dari <a %(a_libgen)s>Libgen.rs</a> (“.is” adalah domain berbeda yang digunakan oleh Libgen.rs). Sumber daya yang berguna dalam menggunakan metadata adalah <a %(a_href)s>halaman ini</a>. %(icon)s Koleksi “fiction_rus” mereka (fiksi Rusia) tidak memiliki torrent khusus, tetapi tercakup oleh torrent dari pihak lain, dan kami menyimpan <a %(fiction_rus)s>mirror</a>. Torrent fiksi Rusia di Arsip Anna Torrent fiksi di Arsip Anna Forum diskusi Metadata Metadata melalui FTP Torrent majalah di Arsip Anna Informasi bidang metadata Mirror dari torrent lain (dan torrent fiksi dan komik unik) Torrent dokumen standar di Arsip Anna Libgen.li Torrent oleh Arsip Anna (sampul buku) Library Genesis dikenal karena sudah dengan murah hati membuat data mereka tersedia dalam jumlah besar melalui torrent. Koleksi Libgen kami terdiri dari data tambahan yang tidak mereka rilis langsung, dalam kemitraan dengan mereka. Terima kasih banyak kepada semua yang terlibat dengan Library Genesis karena bekerja sama dengan kami! Blog kami tentang rilis sampul buku Halaman ini tentang versi “.rs”. Ini dikenal karena secara konsisten menerbitkan metadata dan konten lengkap dari katalog bukunya. Koleksi bukunya dibagi antara bagian fiksi dan non-fiksi. Sumber daya yang berguna dalam menggunakan metadata adalah <a %(a_metadata)s>halaman ini</a> (memblokir rentang IP, VPN mungkin diperlukan). Per Maret 2024, torrent baru diposting di <a %(a_href)s>thread forum ini</a> (memblokir rentang IP, mungkin memerlukan VPN). Torrent Fiksi di Arsip Anna Torrent Fiksi Libgen.rs Forum Diskusi Libgen.rs Metadata Libgen.rs Informasi bidang metadata Libgen.rs Torrent Non-Fiksi Libgen.rs Torrent Non-Fiksi di Arsip Anna %(example)s untuk buku fiksi. <a %(blog_post)s>Rilis pertama</a> ini cukup kecil: sekitar 300GB sampul buku dari fork Libgen.rs, baik fiksi maupun non-fiksi. Mereka diorganisir dengan cara yang sama seperti yang muncul di libgen.rs, misalnya: %(example)s untuk buku non-fiksi. Sama seperti dengan koleksi Z-Library, kami menempatkan semuanya dalam file .tar besar, yang dapat dipasang menggunakan <a %(a_ratarmount)s>ratarmount</a> jika Anda ingin menyajikan file secara langsung. Rilis 1 (%(date)s) Kisah singkat tentang berbagai cabang Library Genesis (atau “Libgen”) adalah bahwa seiring waktu, orang-orang yang terlibat dengan Library Genesis mengalami perpecahan, dan pergi ke jalan mereka masing-masing. Menurut <a %(a_mhut)s>posting forum</a> ini, Libgen.li awalnya di-host di “http://free-books.dontexist.com”. Versi “.fun” dibuat oleh pendiri aslinya. Ini sedang diperbarui untuk versi baru yang lebih terdistribusi. Versi <a %(a_li)s>“.li”</a> memiliki koleksi komik yang sangat besar, serta konten lain, yang belum (belum) tersedia untuk diunduh massal melalui torrent. Ini memiliki koleksi torrent terpisah dari buku fiksi, dan mengandung metadata dari <a %(a_scihub)s>Sci-Hub</a> dalam basis datanya. Versi “.rs” memiliki data yang sangat mirip, dan paling konsisten merilis koleksi mereka dalam torrent massal. Ini kira-kira dibagi menjadi bagian “fiksi” dan “non-fiksi”. Awalnya di “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> dalam beberapa hal juga merupakan cabang dari Library Genesis, meskipun mereka menggunakan nama yang berbeda untuk proyek mereka. Libgen.rs Kami juga memperkaya koleksi kami dengan sumber metadata saja, yang dapat kami cocokkan dengan berkas, misalnya menggunakan nomor ISBN atau bidang lainnya. Di bawah ini adalah gambaran umum dari sumber-sumber tersebut. Sekali lagi, beberapa dari sumber ini sepenuhnya terbuka, sementara yang lain harus kami kikis. Perhatikan bahwa dalam pencarian metadata, kami menampilkan catatan asli. Kami tidak melakukan penggabungan catatan. Sumber metadata saja Open Library adalah proyek sumber terbuka oleh Internet Archive untuk mengatalogkan setiap buku di dunia. Ini memiliki salah satu operasi pemindaian buku terbesar di dunia, dan memiliki banyak buku yang tersedia untuk peminjaman digital. Katalog metadata bukunya tersedia untuk diunduh secara gratis, dan termasuk di Arsip Anna (meskipun saat ini tidak dalam pencarian, kecuali jika Anda secara eksplisit mencari ID Open Library). Open Library Mengecualikan duplikat Terakhir diperbarui Persentase jumlah file %% dicerminkan oleh AA / torrent tersedia Ukuran Sumber Di bawah ini adalah ikhtisar cepat tentang sumber file di Arsip Anna. Karena perpustakaan bayangan sering menyinkronkan data satu sama lain, ada tumpang tindih yang signifikan antara perpustakaan. Itulah mengapa jumlahnya tidak sesuai dengan total. Persentase “mirrored and seeded by Anna’s Archive” menunjukkan berapa banyak file yang kami mirror sendiri. Kami menyebarkan file-file tersebut secara massal melalui torrent, dan membuatnya tersedia untuk diunduh langsung melalui situs web mitra. Ikhtisar Total Torrent di Arsip Anna Untuk latar belakang tentang Sci-Hub, silakan merujuk ke <a %(a_scihub)s>situs resmi</a>, <a %(a_wikipedia)s>halaman Wikipedia</a>, dan <a %(a_radiolab)s>wawancara podcast</a> ini. Perhatikan bahwa Sci-Hub telah <a %(a_reddit)s>dibekukan sejak 2021</a>. Sebelumnya juga pernah dibekukan, tetapi pada tahun 2021 beberapa juta makalah ditambahkan. Namun, beberapa jumlah terbatas makalah masih ditambahkan ke koleksi “scimag” Libgen, meskipun tidak cukup untuk menjamin torrent massal baru. Kami menggunakan metadata Sci-Hub yang disediakan oleh <a %(a_libgen_li)s>Libgen.li</a> dalam koleksi “scimag”-nya. Kami juga menggunakan dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Perhatikan bahwa torrent “smarch” <a %(a_smarch)s>sudah usang</a> dan oleh karena itu tidak termasuk dalam daftar torrent kami. Torrent di Libgen.li Torrent di Libgen.rs Metadata dan torrent Pembaruan di Reddit Wawancara podcast Halaman Wikipedia Sci-Hub Sci-Hub: dibekukan sejak 2021; sebagian besar tersedia melalui torrent Libgen.li: penambahan kecil sejak itu</div> Beberapa perpustakaan sumber mempromosikan berbagi data mereka secara massal melalui torrent, sementara yang lain tidak dengan mudah berbagi koleksi mereka. Dalam kasus yang terakhir, Arsip Anna mencoba mengikis koleksi mereka, dan membuatnya tersedia (lihat halaman <a %(a_torrents)s>Torrents</a> kami). Ada juga situasi di antara, misalnya, di mana perpustakaan sumber bersedia berbagi, tetapi tidak memiliki sumber daya untuk melakukannya. Dalam kasus tersebut, kami juga mencoba membantu. Di bawah ini adalah gambaran umum tentang bagaimana kami berinteraksi dengan berbagai perpustakaan sumber. Perpustakaan sumber %(icon)s Berbagai basis data file tersebar di internet Tiongkok; meskipun seringkali basis data berbayar %(icon)s Sebagian besar file hanya dapat diakses menggunakan akun BaiduYun premium; kecepatan unduh lambat. %(icon)s Arsip Anna mengelola koleksi <a %(duxiu)s>file DuXiu</a> %(icon)s Berbagai database metadata tersebar di internet Tiongkok; meskipun seringkali merupakan database berbayar %(icon)s Tidak ada dump metadata yang mudah diakses untuk seluruh koleksi mereka. %(icon)s Arsip Anna mengelola koleksi <a %(duxiu)s>metadata DuXiu</a> Berkas %(icon)s File hanya tersedia untuk dipinjam secara terbatas, dengan berbagai pembatasan akses %(icon)s Arsip Anna mengelola koleksi <a %(ia)s>file IA</a> %(icon)s Beberapa metadata tersedia melalui <a %(openlib)s>dump database Open Library</a>, tetapi tidak mencakup seluruh koleksi IA %(icon)s Tidak ada dump metadata yang mudah diakses untuk seluruh koleksi mereka %(icon)s Arsip Anna mengelola koleksi <a %(ia)s>metadata IA</a> Terakhir diperbarui %(icon)s Arsip Anna dan Libgen.li secara kolaboratif mengelola koleksi <a %(comics)s>buku komik</a>, <a %(magazines)s>majalah</a>, <a %(standarts)s>dokumen standar</a>, dan <a %(fiction)s>fiksi (berbeda dari Libgen.rs)</a>. %(icon)s Torrent Non-Fiksi dibagikan dengan Libgen.rs (dan dicerminkan <a %(libgenli)s>di sini</a>). %(icon)s <a %(dbdumps)s>Dump database HTTP</a> triwulanan %(icon)s Torrent otomatis untuk <a %(nonfiction)s>Non-Fiksi</a> dan <a %(fiction)s>Fiksi</a> %(icon)s Arsip Anna mengelola koleksi <a %(covers)s>torrent sampul buku</a> %(icon)s Dump database <a %(dbdumps)s>HTTP harian</a> Metadata %(icon)s Dump <a %(dbdumps)s>basis data</a> bulanan %(icon)s Torrent data tersedia <a %(scihub1)s>di sini</a>, <a %(scihub2)s>di sini</a>, dan <a %(libgenli)s>di sini</a> %(icon)s Beberapa file baru <a %(libgenrs)s>sedang</a> <a %(libgenli)s>ditambahkan</a> ke “scimag” Libgen, tetapi tidak cukup untuk membuat torrent baru %(icon)s Sci-Hub telah membekukan file baru sejak 2021. %(icon)s Dump metadata tersedia <a %(scihub1)s>di sini</a> dan <a %(scihub2)s>di sini</a>, serta sebagai bagian dari <a %(libgenli)s>database Libgen.li</a> (yang kami gunakan) Sumber %(icon)s Berbagai sumber yang lebih kecil atau satu kali. Kami mendorong orang untuk mengunggah ke perpustakaan bayangan lainnya terlebih dahulu, tetapi terkadang orang memiliki koleksi yang terlalu besar untuk diurutkan oleh orang lain, meskipun tidak cukup besar untuk mendapatkan kategori mereka sendiri. %(icon)s Tidak tersedia langsung dalam jumlah besar, dilindungi dari scraping %(icon)s Arsip Anna mengelola koleksi <a %(worldcat)s>metadata OCLC (WorldCat)</a> %(icon)s Arsip Anna dan Z-Library secara kolaboratif mengelola koleksi <a %(metadata)s>metadata Z-Library</a> dan <a %(files)s>file Z-Library</a> Datasets Kami menggabungkan semua sumber di atas ke dalam satu basis data terpadu yang kami gunakan untuk melayani situs web ini. Basis data terpadu ini tidak tersedia secara langsung, tetapi karena Arsip Anna sepenuhnya open source, basis data ini dapat dengan cukup mudah <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>diunduh</a> sebagai basis data ElasticSearch dan MariaDB. Skrip di halaman tersebut akan secara otomatis mengunduh semua metadata yang diperlukan dari sumber-sumber yang disebutkan di atas. Jika Anda ingin menjelajahi data kami sebelum menjalankan skrip tersebut secara lokal, Anda dapat melihat berkas JSON kami, yang menghubungkan lebih lanjut ke berkas JSON lainnya. <a %(a_json)s>Berkas ini</a> adalah titik awal yang baik. Basis data terpadu Torrent oleh Arsip Anna jelajahi cari Berbagai sumber yang lebih kecil atau satu kali. Kami mendorong orang untuk mengunggah ke perpustakaan bayangan lainnya terlebih dahulu, tetapi terkadang orang memiliki koleksi yang terlalu besar untuk disortir oleh orang lain, meskipun tidak cukup besar untuk mendapatkan kategori mereka sendiri. Ikhtisar dari <a %(a1)s>halaman datasets</a>. Dari <a %(a_href)s>aaaaarg.fail</a>. Tampaknya cukup lengkap. Dari relawan kami “cgiym”. Dari torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Memiliki tumpang tindih yang cukup tinggi dengan koleksi makalah yang ada, tetapi sangat sedikit kecocokan MD5, jadi kami memutuskan untuk menyimpannya sepenuhnya. Scrape dari <q>iRead eBooks</q> (= secara fonetis <q>ai rit i-books</q>; airitibooks.com), oleh relawan <q>j</q>. Sesuai dengan metadata <q>airitibooks</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>. Dari koleksi <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sebagian dari sumber asli, sebagian dari the-eye.eu, sebagian dari cermin lainnya. Dari situs web torrent buku pribadi, <a %(a_href)s>Bibliotik</a> (sering disebut sebagai “Bib”), di mana buku-buku dibundel menjadi torrent berdasarkan nama (A.torrent, B.torrent) dan didistribusikan melalui the-eye.eu. Dari relawan kami “bpb9v”. Untuk informasi lebih lanjut tentang <a %(a_href)s>CADAL</a>, lihat catatan di <a %(a_duxiu)s>halaman dataset DuXiu</a> kami. Lebih banyak dari relawan kami “bpb9v”, sebagian besar file DuXiu, serta folder “WenQu” dan “SuperStar_Journals” (SuperStar adalah perusahaan di balik DuXiu). Dari sukarelawan “cgiym”, teks Tiongkok dari berbagai sumber (diwakili sebagai subdirektori), termasuk dari <a %(a_href)s>China Machine Press</a> (penerbit besar Tiongkok). Koleksi non-Cina (diwakili sebagai subdirektori) dari relawan kami “cgiym”. Scrape buku tentang arsitektur Tiongkok, oleh relawan <q>cm</q>: <q>Saya mendapatkannya dengan mengeksploitasi kerentanan jaringan di penerbit, tetapi celah itu sekarang sudah ditutup</q>. Sesuai dengan metadata <q>chinese_architecture</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>. Buku dari penerbit akademik <a %(a_href)s>De Gruyter</a>, dikumpulkan dari beberapa torrent besar. Scrape dari <a %(a_href)s>docer.pl</a>, situs berbagi file Polandia yang berfokus pada buku dan karya tulis lainnya. Di-scrape pada akhir 2023 oleh relawan “p”. Kami tidak memiliki metadata yang baik dari situs asli (bahkan tidak ada ekstensi file), tetapi kami memfilter file yang mirip buku dan sering kali dapat mengekstrak metadata dari file itu sendiri. DuXiu epubs, langsung dari DuXiu, dikumpulkan oleh relawan “w”. Hanya buku DuXiu terbaru yang tersedia langsung melalui ebook, jadi sebagian besar dari ini haruslah yang terbaru. Sisa file DuXiu dari sukarelawan “m”, yang tidak dalam format PDG milik DuXiu (dataset utama <a %(a_href)s>DuXiu</a>). Dikumpulkan dari banyak sumber asli, sayangnya tanpa mempertahankan sumber tersebut di filepath. <span></span> <span></span> <span></span> Scrape buku erotis, oleh relawan <q>do no harm</q>. Sesuai dengan metadata <q>hentai</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>. <span></span> <span></span> Koleksi yang di-scrape dari penerbit Manga Jepang oleh relawan “t”. <a %(a_href)s>Arsip yudisial terpilih Longquan</a>, disediakan oleh sukarelawan “c”. Scrape dari <a %(a_href)s>magzdb.org</a>, sekutu Library Genesis (terhubung di halaman utama libgen.rs) tetapi yang tidak ingin menyediakan file mereka secara langsung. Diperoleh oleh sukarelawan “p” pada akhir 2023. <span></span> Berbagai unggahan kecil, terlalu kecil sebagai subkoleksi mereka sendiri, tetapi diwakili sebagai direktori. Ebook dari AvaxHome, situs berbagi file Rusia. Arsip surat kabar dan majalah. Sesuai dengan metadata <q>newsarch_magz</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>. Scrape dari <a %(a1)s>Philosophy Documentation Center</a>. Koleksi sukarelawan “o” yang mengumpulkan buku-buku Polandia langsung dari situs rilis asli (“scene”). Koleksi gabungan dari <a %(a_href)s>shuge.org</a> oleh sukarelawan “cgiym” dan “woz9ts”. <span></span> <a %(a_href)s>“Perpustakaan Kekaisaran Trantor”</a> (dinamai sesuai perpustakaan fiksi), diambil pada tahun 2022 oleh relawan “t”. <span></span> <span></span> <span></span> Sub-sub-koleksi (diwakili sebagai direktori) dari relawan “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (oleh <a %(a_sikuquanshu)s>Dizhi(迪志)</a> di Taiwan), mebook (mebook.cc, 我的小书屋, ruang buku kecil saya — woz9ts: “Situs ini terutama berfokus pada berbagi file ebook berkualitas tinggi, beberapa di antaranya diatur oleh pemiliknya sendiri. Pemiliknya <a %(a_arrested)s>ditangkap</a> pada 2019 dan seseorang membuat koleksi file yang dia bagikan.”). Sisa file DuXiu dari relawan “woz9ts”, yang tidak dalam format PDG milik DuXiu (masih harus dikonversi ke PDF). Koleksi “upload” dibagi menjadi subkoleksi yang lebih kecil, yang ditunjukkan dalam AACID dan nama torrent. Semua subkoleksi pertama kali dideduplicasi terhadap koleksi utama, meskipun file JSON “upload_records” metadata masih mengandung banyak referensi ke file asli. File non-buku juga dihapus dari sebagian besar subkoleksi, dan biasanya <em>tidak</em> dicatat dalam JSON “upload_records”. Subkoleksi tersebut adalah: Catatan Subkoleksi Banyak subkoleksi sendiri terdiri dari sub-sub-koleksi (misalnya dari sumber asli yang berbeda), yang diwakili sebagai direktori di bidang “filepath”. Unggahan ke Arsip Anna Posting blog kami tentang data ini <a %(a_worldcat)s>WorldCat</a> adalah basis data milik nirlaba <a %(a_oclc)s>OCLC</a>, yang mengumpulkan catatan metadata dari perpustakaan di seluruh dunia. Ini kemungkinan merupakan koleksi metadata perpustakaan terbesar di dunia. Pada Oktober 2023 kami <a %(a_scrape)s>merilis</a> pengumpulan lengkap basis data OCLC (WorldCat), dalam <a %(a_aac)s>format Kontainer Arsip Anna</a>. Oktober 2023, rilis awal: OCLC (WorldCat) Torrent oleh Arsip Anna Contoh catatan di Arsip Anna (koleksi asli) Contoh catatan di Arsip Anna (koleksi "zlib3") Torrent oleh Arsip Anna (metadata + konten) Posting blog tentang Rilis 1 Posting blog tentang Rilis 2 Pada akhir 2022, pendiri Z-Library yang diduga ditangkap, dan domain disita oleh otoritas Amerika Serikat. Sejak itu situs web perlahan-lahan kembali online. Tidak diketahui siapa yang saat ini mengelolanya. Pembaruan per Februari 2023. Z-Library berakar dari komunitas <a %(a_href)s>Library Genesis</a>, dan awalnya dimulai dengan data mereka. Sejak itu, Z-Library telah menjadi lebih profesional, dan memiliki antarmuka yang jauh lebih modern. Oleh karena itu, mereka dapat menerima lebih banyak donasi, baik secara finansial untuk terus meningkatkan situs web mereka, maupun donasi buku baru. Mereka telah mengumpulkan koleksi besar selain dari Library Genesis. Koleksi ini terdiri dari tiga bagian. Halaman deskripsi asli untuk dua bagian pertama disimpan di bawah ini. Anda memerlukan ketiga bagian untuk mendapatkan semua data (kecuali torrent yang digantikan, yang dicoret di halaman torrent). %(title)s: rilis pertama kami. Ini adalah rilis pertama dari apa yang kemudian disebut "Pirate Library Mirror" ("pilimi"). %(title)s: rilis kedua, kali ini dengan semua file dibungkus dalam file .tar. %(title)s: rilis baru bertahap, menggunakan <a %(a_href)s>format Kontainer Arsip Anna (AAC)</a>, sekarang dirilis dalam kolaborasi dengan tim Z-Library. Mirror awal diperoleh dengan susah payah selama tahun 2021 dan 2022. Pada titik ini, sedikit ketinggalan zaman: mencerminkan keadaan koleksi pada Juni 2021. Kami akan memperbarui ini di masa depan. Saat ini kami fokus untuk merilis rilis pertama ini. Karena Library Genesis sudah disimpan dengan torrent publik, dan termasuk dalam Z-Library, kami melakukan deduplikasi dasar terhadap Library Genesis pada Juni 2022. Untuk ini kami menggunakan hash MD5. Kemungkinan ada banyak konten duplikat di perpustakaan, seperti beberapa format file dengan buku yang sama. Ini sulit dideteksi secara akurat, jadi kami tidak melakukannya. Setelah deduplikasi, kami memiliki lebih dari 2 juta file, dengan total hampir 7TB. Koleksi ini terdiri dari dua bagian: dump MySQL “.sql.gz” dari metadata, dan 72 file torrent masing-masing sekitar 50-100GB. Metadata berisi data seperti yang dilaporkan oleh situs web Z-Library (judul, penulis, deskripsi, tipe file), serta ukuran file aktual dan md5sum yang kami amati, karena terkadang ini tidak sesuai. Tampaknya ada rentang file di mana Z-Library sendiri memiliki metadata yang salah. Kami mungkin juga telah mengunduh file yang salah dalam beberapa kasus terisolasi, yang akan kami coba deteksi dan perbaiki di masa depan. File torrent besar berisi data buku aktual, dengan ID Z-Library sebagai nama file. Ekstensi file dapat direkonstruksi menggunakan dump metadata. Koleksi ini adalah campuran konten non-fiksi dan fiksi (tidak dipisahkan seperti di Library Genesis). Kualitasnya juga sangat bervariasi. Rilis pertama ini sekarang sepenuhnya tersedia. Perhatikan bahwa file torrent hanya tersedia melalui mirror Tor kami. Rilis 1 (%(date)s) Ini adalah satu file torrent tambahan. Tidak mengandung informasi baru, tetapi memiliki beberapa data di dalamnya yang dapat memakan waktu untuk dihitung. Itu membuatnya nyaman untuk dimiliki, karena mengunduh torrent ini seringkali lebih cepat daripada menghitungnya dari awal. Secara khusus, ini berisi indeks SQLite untuk file tar, untuk digunakan dengan <a %(a_href)s>ratarmount</a>. Tambahan Rilis 2 (%(date)s) Kami telah mendapatkan semua buku yang ditambahkan ke Z-Library antara mirror terakhir kami dan Agustus 2022. Kami juga kembali dan mengumpulkan beberapa buku yang kami lewatkan pertama kali. Secara keseluruhan, koleksi baru ini sekitar 24TB. Sekali lagi, koleksi ini dideduplikasi terhadap Library Genesis, karena sudah ada torrent yang tersedia untuk koleksi tersebut. Data diatur serupa dengan rilis pertama. Ada dump MySQL “.sql.gz” dari metadata, yang juga mencakup semua metadata dari rilis pertama, sehingga menggantikannya. Kami juga menambahkan beberapa kolom baru: Kami menyebutkan ini terakhir kali, tetapi hanya untuk memperjelas: "filename" dan "md5" adalah properti sebenarnya dari file, sedangkan "filename_reported" dan "md5_reported" adalah apa yang kami kumpulkan dari Z-Library. Terkadang kedua hal ini tidak sesuai satu sama lain, jadi kami menyertakan keduanya. Untuk rilis ini, kami mengubah kolasi menjadi "utf8mb4_unicode_ci", yang seharusnya kompatibel dengan versi MySQL yang lebih lama. File data tersebut mirip dengan sebelumnya, meskipun ukurannya jauh lebih besar. Kami tidak bisa repot-repot membuat banyak file torrent yang lebih kecil. “pilimi-zlib2-0-14679999-extra.torrent” berisi semua file yang terlewat pada rilis sebelumnya, sementara torrent lainnya adalah rentang ID baru.  <strong>Pembaruan %(date)s:</strong> Kami membuat sebagian besar torrent kami terlalu besar, menyebabkan klien torrent kesulitan. Kami telah menghapusnya dan merilis torrent baru. <strong>Pembaruan %(date)s:</strong> Masih terlalu banyak file, jadi kami membungkusnya dalam file tar dan merilis torrent baru lagi. %(key)s: apakah file ini sudah ada di Library Genesis, baik dalam koleksi non-fiksi atau fiksi (cocok dengan md5). %(key)s: torrent mana file ini berada. %(key)s: ditetapkan ketika kami tidak dapat mengunduh buku tersebut. Rilis 2 (%(date)s) Rilis Zlib (halaman deskripsi asli) Domain Tor Situs web utama Pengambilan Z-Library Koleksi “Chinese” di Z-Library tampaknya sama dengan koleksi DuXiu kami, tetapi dengan MD5 yang berbeda. Kami mengecualikan file-file ini dari torrent untuk menghindari duplikasi, tetapi tetap menampilkannya dalam indeks pencarian kami. Metadata Selamat kamu memdapatkan %(percentage)s%% bonus unduhan jalur cepat, dikarnakan refrensi dari temanmu %(profile_link)s. Berlaku selama periode membersip. Donasi Bergabung terpilih Diskon hingga %(percentage)s%% Alipay mendukung kartu kredit/debit internasional. Lihat <a %(a_alipay)s>panduan ini</a> untuk informasi lebih lanjut. Kirim kepada kami Amazon.com gift menggunakan kartu kredit/debit kamu. Anda dapat membeli koin kripto menggunakan kartu kredit/debit. WeChat (Weixin Pay) mendukung pembayaran international melalui kartu kredit/debit. Pada aplikasi WeChat, buka "Tentang saya => Layanan => Wallet => Tambahkan kartu". Jika anda tidak menemukanya, aktivkan memalui "Tentang saya => Setting => Umum => Alat => Weixin Pay => Enable". (gunakan saat mengirim Ethereum dari Coinbase) Telah disalin! salin (jumlah minimum terendah) (peringatan: jumlah minimum tinggi) -%(percentage)s%% 12 bulan 1 bulan 24 bulan 3 bulan 48 bulan 6 bulan 96 bulan Pilih berapa lama Anda ingin berlangganan. <div %(div_monthly_cost)s></div><div %(div_after)s>after <span %(span_discount)s></span> diskon</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% selama 12 bulan selama 1 bulan selama 24 bulan selama 3 bulan untuk 48 bulan selama 6 bulan untuk 96 bulan %(monthly_cost)s / bulan Hubungi Kami Server <strong>SFTP</strong> secara langsung Untuk kontributor koleksi baru (cth: scan dokumen baru, Dataset OCR, dll) akan mendapatkan hak akses premium. Akses Lanjutan Akses kecepatan tinggi dengan <strong>kapasitas tak terbatas</strong> <div %(div_question)s>Bisakah saya meningkatkan keanggotaan saya atau mendapatkan beberapa keanggotaan?</div> <div %(div_question)s>Bisakah saya berdonasi tanpa menjadi anggota?</div> Tentu saja. Kami menerima donasi dalam jumlah berapa pun di alamat Monero (XMR) ini: %(address)s. <div %(div_question)s>Apa arti rentang per bulan?</div> Anda dapat mencapai sisi bawah dari rentang dengan menerapkan semua diskon, seperti memilih periode lebih lama dari satu bulan. <div %(div_question)s>Apakah keanggotaan diperbarui secara otomatis?</div> Keanggotaan <strong>tidak</strong> diperbarui secara otomatis. Anda dapat bergabung selama yang Anda inginkan. <div %(div_question)s>Apa yang akan dilakukan dengan donasi yang diberikan?</div> 100%% dari donasi digunakan untuk menjaga dan memudahkan akses pengetahuan dan budaya dunia. Saat ini, sebagian besar digunakan untuk menyewa komputer server, tempat penyimpanan data, dan biaya internet. Tidak ada uang yang diberikan kepada anggota tim secara pribadi. <div %(div_question)s>Bolehkah saya mendonasikan dengan jumlah besar?</div> Itu akan sangat luar biasa! Untuk donasi yang melebihi beberapa ribu dolar, silakan menghubungi kami secara langsung di %(email)s. <div %(div_question)s>Apakah Anda memiliki metode pembayaran lain?</div> Saat ini tidak ada. Ada banyak orang yang tidak ingin proyek arsip seperti ini ada di internet, jadi kami harus berhati-hati. Jika Anda dapat dan ingin membantu kami menyiapkan metode pembayaran lain (yang lebih mudah digunakan) dengan aman, silakan hubungi kami di %(email)s. Pertanyaan Umum tentang Donasi Anda memiliki <a %(a_donation)s>donasi yang sedang berlangsung</a>. Harap selesaikan atau batalkan donasi tersebut sebelum membuat donasi baru. <a %(a_all_donations)s>Tampilkan semua donasi saya</a> Untuk sumbangan di atas $5000, harap hubungi kami langsung di %(email)s. Kami menerima donasi dari perseorangan maupun institusi secara terbuka.  Perhatikan bahwa meskipun keanggotaan di halaman ini adalah “per bulan”, mereka adalah donasi satu kali (tidak berulang). Lihat <a %(faq)s>Tanya Jawab terkait Donasi</a>. Anna's Archive adalah sebuah proyek nirlaba, open-source, dan open-data. Dengan mendonasikan dan menjadi anggota, Anda mendukung operasional dan pengembangan kami. Kepada semua anggota kami: terima kasih telah menjaga kelangsungan kami! ❤️ Untuk informasi lebih lanjut, lihat <a %(a_donate)s>Tanya Jawab terkait Donasi</a>. Untuk menjadi anggota, silakan <a %(a_login)s>Masuk atau Daftar</a>. Terima kasih atas dukungannya! $%(cost)s / bulan Bila terjadi kesalahan selama proses pembayaran, kami tidak melayani pengembalian, akan tetapi akan kami usahakan perbaikan dengan jalan lainya. Temukan halaman "Kripto" di aplikasi atau situs web PayPal Anda. Biasanya ini berada di bawah "Finances". Buka halaman "Bitcoin" di aplikasi atau situs web PayPal Anda. Tekan tombol "Transfer" %(transfer_icon)s, lalu pilih "Kirim". Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s kartu hadiah Kartu bank Kartu bank (menggunakan aplikasi) Binance Kredit/debit/Apple/Google (BMC) Cash App Credit/debit card Credit/debit card 2 Kartu kredit/debit (cadangan) Kripto %(bitcoin_icon)s Kartu / PayPal / Venmo PayPal (US) %(bitcoin_icon)s Paypal PayPal (reguler) Pix (Brazil) Revolut (sementara tidak tersedia) WeChat Pilih koin kripto pilihan Anda: Mendonasikan menggunakan Amazon gift card. <strong>PENTING:</strong> Opsi ini untuk %(amazon)s. Jika Anda ingin menggunakan situs web Amazon lainnya, pilih di atas. <strong>PENTING:</strong> Kami hanya mendukung Amazon.com, tidak mendukung situs Amazon lainnya. Misalnya, .de, .co.uk, .ca, TIDAK didukung. Dimohon untuk TIDAK menuliskan kontak pribadi. Masukkan jumlah yang tepat: %(amount)s Harap dicatat bahwa kita perlu membulatkan jumlah ke angka yang diterima oleh pihak yang menjual (minimum %(minimum)s). Donasi menggunakan kartu kredit/debit, melalui aplikasi Alipay (sangat mudah untuk diatur). Pasang aplikasi Alipay dari <a %(a_app_store)s>Apple App Store</a> atau <a %(a_play_store)s>Google Play Store</a>. Daftar menggunakan nomor telepon Anda. Tidak diperlukan detail pribadi lebih lanjut. <span %(style)s>1</span>Pasang aplikasi Alipay Didukung: Visa, MasterCard, JCB, Diners Club, dan Discover. Lihat <a %(a_alipay)s>panduan ini</a> untuk informasi lebih lanjut. <span %(style)s>2</span>Tambahkan kartu bank Dengan Binance, Anda membeli Bitcoin dengan kartu kredit/debit atau rekening bank, dan kemudian menyumbangkan Bitcoin tersebut kepada kami. Dengan cara ini, kami dapat tetap aman dan anonim saat menerima donasi Anda. Binance tersedia di hampir setiap negara, dan mendukung sebagian besar bank dan kartu kredit/debit. Ini adalah rekomendasi utama kami saat ini. Kami menghargai waktu Anda untuk mempelajari cara berdonasi menggunakan metode ini, karena ini sangat membantu kami. Untuk kartu kredit, kartu debit, Apple Pay, dan Google Pay, kami menggunakan “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Dalam sistem mereka, satu “kopi” setara dengan $5, jadi donasi Anda akan dibulatkan ke kelipatan 5 terdekat. Mendonasikan menggunakan Cash App. Jika Anda memiliki Cash App, ini adalah cara yang paling mudah untuk memberikan donasi! Harap dicatat bahwa untuk transaksi di bawah %(amount)s, Cash App dapat mengenakan biaya sebesar %(fee)s. Untuk jumlah di atas %(amount)s, tidak ada biaya yang dikenakan! Mendonasikan dengan kartu kredit atau kartu debit. Metode ini menggunakan penyedia cryptocurrency sebagai konversi perantara. Ini bisa sedikit membingungkan, jadi harap gunakan metode ini hanya jika metode pembayaran lain tidak berfungsi. Ini juga tidak berfungsi di semua negara. Kami tidak dapat mendukung kartu kredit/debit secara langsung, karena bank tidak ingin bekerja sama dengan kami. ☹ Namun, ada beberapa cara untuk tetap menggunakan kartu kredit/debit, dengan menggunakan metode pembayaran lain: Dengan kripto, Anda dapat mendonasikan menggunakan BTC, ETH, XMR, dan SOL. Gunakan opsi ini jika Anda sudah terbiasa dengan mata uang kripto. Dengan kripto, Anda dapat mendonasikan menggunakan BTC, ETH, XMR, dan lainnya. Layanan ekspres kripto Jika Anda menggunakan kripto untuk pertama kalinya, kami menyarankan menggunakan %(options)s untuk membeli dan menyumbangkan Bitcoin (mata uang kripto asli dan paling banyak digunakan). Harap dicatat bahwa untuk sumbangan kecil, biaya kartu kredit mungkin menghapuskan diskon %(discount)s%% kami, jadi kami menyarankan berlangganan yang lebih panjang. Donasi menggunakan kartu kredit/debit, PayPal, atau Venmo. Anda dapat memilih di antara ini pada halaman berikutnya. Google Pay dan Apple Pay mungkin juga bisa digunakan. Perlu dicatat bahwa untuk donasi kecil, biaya cukup tinggi, jadi kami merekomendasikan berlangganan yang lebih lama. Untuk mendonasikan menggunakan PayPal AS, kami akan menggunakan PayPal Kripto, yang memungkinkan kita tetap anonim. Kami menghargai waktu yang Anda luangkan untuk mempelajari cara mendonasikan menggunakan metode ini, karena itu sangat membantu kami. Mendonasikan menggunakan PayPal. Donasi menggunakan akun PayPal reguler Anda. Donasi menggunakan Revolut. Jika Anda memiliki Revolut, ini adalah cara termudah untuk berdonasi! Metode pembayaran ini hanya memungkinkan hingga maksimum %(amount)s. Silakan pilih durasi atau metode pembayaran yang berbeda. Metode pembayaran ini memerlukan minimum %(amount)s. Silakan pilih durasi atau metode pembayaran yang berbeda. Binance Coinbase Kraken Mohon pilih metode pembayaran yang sesuai. "Adopt a torrent": nama pengguna atau pesan Anda dalam nama file torrent <div %(div_months)s>sekali setiap 12 bulan keanggotaan</div> Nama pengguna Anda atau anonim disebutkan dalam kredit Akses awal ke fitur-fitur baru Telegram eksklusif dengan pembaharuan dibelakang layar %(number)s unduhan cepat per hari jika Anda berdonasi bulan ini! <a %(a_api)s>Akses API JSON</a> Pelestarian ilmu pengetahuan dan budaya umat manusia yang melegenda Keuntungan sebelumnya, ditambah: Dapatkan <strong>%(percentage)s%% unduhan bonus</strong> dengan <a %(a_refer)s>mengajak teman</a>. Makalah SciDB <strong>tak terbatas</strong> tanpa verifikasi Saat menanyakan tentang akun atau donasi, tambahkan ID akun Anda, tangkapan layar, tanda terima, sebanyak mungkin informasi. Kami hanya memeriksa email kami setiap 1-2 minggu, jadi tidak menyertakan informasi ini akan menunda penyelesaian apa pun. Untuk mendapatkan unduhan lebih banyak, rekomendasikan <a %(a_refer)s>pada temanmu</a>! Hal tersebut kemungkinan akan memakan waktu 1-2 minggu untuk kami tindak lanjuti. Dikarnakan sumberdaya kami yang masih terbatas. Terimakasih atas partisipasinya. Harap dicatat bahwa nama akun atau gambar profil mungkin terlihat aneh. Tidak perlu khawatir! Akun-akun ini dikelola oleh mitra donasi kami. Akun kami tidak diretas. Donasi <span %(span_cost)s></span> <span %(span_label)s></span> selama 12 bulan “%(tier_name)s” selama 1 bulan “%(tier_name)s” selama 24 bulan “%(tier_name)s” selama 3 bulan “%(tier_name)s” untuk 48 bulan “%(tier_name)s” selama 6 bulan “%(tier_name)s” untuk 96 bulan “%(tier_name)s” Anda masih dapat membatalkan donasi selama proses pembayaran. Klik tombol donasi untuk mengkonfirmasi donasi ini. <strong>Catatan penting:</strong> Harga kripto bisa naik turun dengan sangat cepat dan tidak terduga, kadang-kadang bahkan bisa berubah sebanyak 20%% dalam waktu singkat. Meskipun begitu, ini masih lebih baik daripada biaya yang kita tanggung saat menggunakan banyak penyedia pembayaran, yang sering kali membebankan biaya sekitar 50-60%% ketika bekerja dengan lembaga amal seperti kami. <u>Jika Anda mengirimkan kepada kami bukti pembayaran dengan harga asli yang Anda bayarkan, kami akan tetap memberikan kredit keanggotaan yang Anda pilih</u> (asalkan bukti tersebut tidak lebih dari beberapa jam). Kami sangat menghargai bahwa Anda bersedia menghadapi hal-hal seperti ini demi mendukung kami! ❤️ ❌ Terjadi kesalahan. Mohon muat ulang halaman dan coba lagi. <span %(span_circle)s>1</span> Beli Bitcoin melalui Paypal <span %(span_circle)s>2</span> Transferkan Bitcoin ke alamat kami ✅ Mengalihkan ke halaman donasi… Donasi Harap tunggu setidaknya <span %(span_hours)s>24 jam</span> (dan segarkan halaman ini) sebelum menghubungi kami. Jika Anda ingin memberikan donasi (dalam jumlah berapa pun) tanpa keanggotaan, silakan gunakan alamat Monero (XMR) ini: %(address)s. Setelah mengirimkan ‘gift card’ Anda, sistem otomatis kami akan mengonfirmasinya dalam beberapa menit. Jika ini tidak berhasil, coba kirimkan kembali ‘gift card’ Anda (<a %(a_instr)s>instruksi</a>). Jika masih tidak berhasil, silakan kirim email kepada kami dan Anna akan meninjaunya secara manual (ini mungkin memerlukan waktu beberapa hari), dan pastikan untuk menyebutkan jika Anda telah mencoba mengirim ulang sebelumnya. Contoh: Silakan gunakan <a %(a_form)s>formulir resmi Amazon.com</a> untuk mengirimkan kami kartu hadiah sebesar %(amount)s ke alamat email di bawah ini. Email penerima 'To' dalam formulir: Amazon gift card Kami tidak dapat menerima metode lain untuk kartu hadiah, <strong>hanya yang dikirimkan langsung melalui formulir resmi di Amazon.com</strong>. Kami tidak dapat mengembalikan kartu hadiah Anda jika Anda tidak menggunakan formulir ini. Hanya gunakan sekali. Unik untuk akun Anda, jangan dibagikan. Menunggu kartu hadiah… (muat ulang halaman untuk memeriksa) Buka <a %(a_href)s>halaman donasi kode QR</a>. Pindai kode QR dengan aplikasi Alipay, atau tekan tombol untuk membuka aplikasi Alipay. Harap bersabar; halaman mungkin memerlukan waktu untuk dimuat karena berada di Tiongkok. <span %(style)s>3</span>Lakukan donasi (pindai kode QR atau tekan tombol) Beli koin PYUSD di PayPal Beli Bitcoin (BTC) di Cash App Beli sedikit lebih banyak (kami merekomendasikan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan menyimpan sisa yang ada. Pergi ke halaman “Bitcoin” (BTC) di Cash App. Transfer Bitcoin ke alamat kami Untuk donasi kecil (di bawah $25), Anda mungkin perlu menggunakan Rush atau Priority. Klik tombol “Kirim bitcoin” untuk melakukan “penarikan”. Beralih dari dolar ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah ini dan klik “Kirim”. Lihat <a %(help_video)s>video ini</a> jika Anda mengalami kesulitan. Layanan ekspres nyaman, tetapi mengenakan biaya lebih tinggi. Anda dapat menggunakan ini sebagai pengganti pertukaran kripto jika Anda ingin cepat membuat donasi lebih besar dan tidak keberatan dengan biaya $5-10. Pastikan untuk mengirim jumlah kripto yang tepat yang ditampilkan di halaman donasi, bukan jumlah dalam $USD. Jika tidak, biaya akan dikurangkan dan kami tidak dapat memproses keanggotaan Anda secara otomatis. Terkadang konfirmasi dapat memakan waktu hingga 24 jam, jadi pastikan untuk menyegarkan halaman ini (meskipun sudah kedaluwarsa). Instruksi kartu kredit / debit Donasi melalui halaman kartu kredit / debit kami Beberapa langkah menyebutkan dompet kripto, tetapi jangan khawatir, Anda tidak perlu belajar tentang kripto untuk hal ini. %(coin_name)s instruksi Pindai kode QR ini dengan aplikasi dompet crypto Anda untuk dengan cepat mengisi detail pembayaran Pindai kode QR untuk membayar Kami hanya menerima donasi koin kripto standar(cth: bitcoin) dengan waktu konfirmasi hingga satu jam, tergantung dari jenis koin yang anda donasikan. Donasi %(amount)s di <a %(a_page)s>halaman ini</a>. Donasi ini telah kedaluwarsa. Mohon dibatalkan dan buat yang baru. Jika Anda sudah membayar: Ya, saya telah mengirimkan tanda terima melalui email Jika nilai tukar kripto berubah-ubah selama transaksi, pastikan Anda menyertakan tanda terima yang menunjukkan nilai tukar awal. Kami benar-benar menghargai usaha Anda menggunakan kripto karena ini sangat membantu kami! ❌ Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi. <span %(span_circle)s>%(circle_number)s</span>Kirimkan email tanda terimanya kepada kami Jika Anda mengalami masalah, silakan hubungi kami di %(email)s dan sertakan sebanyak mungkin informasi (seperti tangkapan layar). ✅ Terima kasih atas donasinya! Anna akan mengaktifkan keanggotaan Anda secara manual dalam beberapa hari. Kirimkan tanda terima atau tangkapan layar ke alamat verifikasi pribadi Anda: Setelah Anda mengirim email tanda terimanya, klik tombol ini agar Anna dapat meninjaunya secara manual (ini mungkin memerlukan waktu beberapa hari): Kirim tanda terima atau tangkapan layar ke alamat verifikasi pribadi Anda. Jangan gunakan alamat email ini untuk donasi PayPal Anda. Batal Ya, tolong dibatalkan Apakah Anda yakin ingin membatalkan? Jangan membatalkan jika Anda sudah membayar. ❌ Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi. Berikan donasi baru ✅ Donasi Anda telah dibatalkan. Tanggal: %(date)s Pengenal: %(id)s Urutkan kembali Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / bulan selama %(duration)s bulan, termasuk %(discounts)s%% diskon)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / bulan selama %(duration)s bulan)</span> 1. Masukkan alamat email Anda. 2. Pilih metode pembayaran Anda. 3. Pilih metode pembayaran Anda lagi. 4. Pilih dompet “Self-hosted”. 5. Klik “Saya konfirmasi kepemilikan”. 6. Anda seharusnya menerima tanda terima melalui email. Kirimkan tanda terimanya kepada kami, dan kami akan mengonfirmasi donasi Anda sesegera mungkin. (Anda mungkin ingin membatalkan dan membuat donasi baru) Petunjuk pembayaran sekarang sudah kadaluwarsa. Jika Anda ingin membuat donasi lainnya, gunakan tombol "Urutkan kembali" di atas. Anda sudah melakukan pembayaran. Jika Anda ingin meninjau petunjuk pembayaran, klik di sini: Tampilkan petunjuk pembayaran sebelumnya Jika halaman donasi terblokir, coba koneksi internet yang berbeda (misalnya VPN atau internet ponsel). Sayangnya, halaman Alipay sering kali hanya dapat diakses dari <strong>Tiongkok daratan</strong>. Anda mungkin perlu menonaktifkan VPN sementara, atau menggunakan VPN ke Tiongkok daratan (atau Hong Kong juga kadang berfungsi). <span %(span_circle)s>1</span> Donasi melalui Alipay Donasikan jumlah total %(total)s menggunakan <a %(a_account)s>akun Alipay ini</a> Petunjuk Alipay <span %(span_circle)s>1</span>Transfer ke salah satu akun kripto kami Sumbangkan jumlah total sebesar %(total)s ke salah satu dari alamat berikut: Instruksi kripto Ikuti instruksi untuk membeli Bitcoin (BTC). Anda hanya perlu membeli jumlah yang ingin Anda sumbangkan, sebesar %(total)s. Masukkan alamat Bitcoin (BTC) kami sebagai penerima, dan ikuti instruksi untuk mengirimkan sumbangan Anda sebesar %(total)s: <span %(span_circle)s>1</span>Donasi melalui Pix Donasikan jumlah total sebesar %(total)s menggunakan <a %(a_account)s>akun Pix ini Petunjuk Pix <span %(span_circle)s>1</span>Donasi di WeChat Donasikan jumlah total %(total)s menggunakan <a %(a_account)s>akun WeChat ini</a> Instruksi WeChat Gunakan salah satu layanan “kartu kredit ke Bitcoin” ekspres berikut, yang hanya memerlukan beberapa menit: Alamat BTC / Bitcoin (dompet eksternal): Jumlah BTC / Bitcoin: Isi detail berikut dalam formulir: Jika ada informasi yang tidak terbaru, harap email kami untuk memberi tahu. Harap gunakan <span %(underline)s>jumlah yang tepat</span> ini. Total biaya Anda mungkin lebih tinggi karena biaya kartu kredit. Untuk jumlah kecil, ini mungkin lebih dari diskon kami, sayangnya. (minimum: %(minimum)s, tanpa verifikasi untuk transaksi pertama) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, tanpa verifikasi untuk transaksi pertama) (minimum: %(minimum)s) (minimum: %(minimum)s tergantung negara, tanpa verifikasi untuk transaksi pertama) Ikuti instruksi untuk membeli koin PYUSD (PayPal USD). Beli sedikit lebih banyak (kami sarankan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan tetap memegang sisa dana tersebut. Buka halaman "PYUSD" di aplikasi atau situs web PayPal Anda. Tekan tombol "Transfer" %(icon)s, lalu pilih "Kirim". Perbarui status Untuk mengatur ulang penghitung waktunya, cukup buat donasi baru. Pastikan untuk menggunakan jumlah BTC di bawah ini, <em>BUKAN</em> euro atau dolar, jika tidak, kami tidak akan menerima jumlah yang benar dan tidak dapat mengonfirmasi keanggotaan Anda secara otomatis. Beli Bitcoin (BTC) di Revolut Beli sedikit lebih banyak (kami merekomendasikan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan menyimpan sisa yang ada. Pergi ke halaman “Crypto” di Revolut untuk membeli Bitcoin (BTC). Transfer Bitcoin ke alamat kami Untuk donasi kecil (di bawah $25) Anda mungkin perlu menggunakan Rush atau Priority. Klik tombol “Kirim bitcoin” untuk melakukan “penarikan”. Beralih dari euro ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah ini dan klik “Kirim”. Lihat <a %(help_video)s>video ini</a> jika Anda mengalami kesulitan. Status: 1 2 Panduan langkah demi langkah Lihat panduan langkah demi langkah di bawah ini. Bila tidak akun anda kemungkinan akan terkunci! Bila anda belum memasukan kode rahasia anda untuk masuk: Terima kasih atas donasinya! Waktu yang tersisa: Donasi Transfer %(amount)s ke %(account)s Menunggu konfirmasi (muat ulang halaman untuk memeriksa)… Menunggu transfer (muat ulang halaman untuk memeriksa)… Sebelumnya Unduhan jalur cepat dalam 24 jam terahir menuju batas limit harian. Jalur unduhan cepat melalui server rekanan ditandai dengan %(icon)s. 18 jam terakhir Belum ada berkas yang diunduh. Berkas yang sudah diunduh tidak ditampilkan secara publik. Semua waktu dalam format UTC. Berkas yang sudah diunduh Bila anda menjalankan unduhan dokumen melalui jalur cepat dan jalur lambat, hasil akan didapatkan sebanyak 2 kali. Jangan terlalu khawatir, ada banyak orang yang mengunduh dari situs web yang kami tautkan, dan sangat jarang mengalami masalah. Namun, untuk tetap aman kami merekomendasikan menggunakan VPN (berbayar), atau <a %(a_tor)s>Tor</a> (gratis). Saya mengunduh 1984 oleh George Orwell, apakah polisi akan datang ke rumah saya? Anda adalah Anna! Siapa Anna? Kami memiliki satu API JSON stabil untuk anggota, untuk mendapatkan URL unduhan cepat: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasi ada di dalam JSON itu sendiri). Untuk kasus penggunaan lainnya, seperti iterasi melalui semua file kami, membangun pencarian kustom, dan sebagainya, kami merekomendasikan <a %(a_generate)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Data mentah dapat dieksplorasi secara manual <a %(a_explore)s>melalui file JSON</a>. Daftar torrent mentah kami dapat diunduh sebagai <a %(a_torrents)s>JSON</a> juga. Apakah Anda memiliki API? Kami tidak meng-host materi berhak cipta apa pun di sini. Kami adalah mesin pencari, dan dengan demikian hanya mengindeks metadata yang sudah tersedia untuk umum. Saat mengunduh dari sumber eksternal ini, kami menyarankan untuk memeriksa undang-undang di yurisdiksi Anda terkait apa yang diizinkan. Kami tidak bertanggung jawab atas konten yang di-host oleh pihak lain. Jika Anda memiliki keluhan tentang apa yang Anda lihat di sini, pilihan terbaik Anda adalah menghubungi situs web asli. Kami secara teratur menarik perubahan mereka ke dalam database kami. Jika Anda benar-benar berpikir Anda memiliki keluhan DMCA yang valid yang harus kami tanggapi, silakan isi <a %(a_copyright)s>formulir klaim DMCA / Hak Cipta</a>. Kami menanggapi keluhan Anda dengan serius, dan akan menghubungi Anda sesegera mungkin. Bagaimana cara melaporkan pelanggaran hak cipta? Berikut adalah beberapa buku yang memiliki makna khusus bagi dunia shadow library dan pelestarian digital: Apa buku favorit Anda? Kami juga ingin mengingatkan semua orang bahwa semua kode dan data kami sepenuhnya sumber terbuka. Ini unik untuk proyek seperti kami — kami tidak mengetahui proyek lain dengan katalog sebesar ini yang juga sepenuhnya sumber terbuka. Kami sangat menyambut siapa pun yang berpikir kami menjalankan proyek kami dengan buruk untuk mengambil kode dan data kami dan mendirikan shadow library mereka sendiri! Kami tidak mengatakan ini karena dendam atau semacamnya — kami benar-benar berpikir ini akan luar biasa karena akan meningkatkan standar untuk semua orang, dan lebih baik melestarikan warisan umat manusia. Saya benci cara Anda menjalankan proyek ini! Kami sangat mendukung kamu untuk membuat <a %(a_mirrors)s>mirrors</a>, dan kami akan memberikan dukungan keuangan. Bagaimana saya dapat membantu? Kami memang melakukannya. Inspirasi kami untuk mengumpulkan metadata adalah tujuan Aaron Swartz untuk “satu halaman web untuk setiap buku yang pernah diterbitkan”, yang mana ia menciptakan <a %(a_openlib)s>Open Library</a>. Proyek tersebut telah berjalan dengan baik, tetapi posisi unik kami memungkinkan kami mendapatkan metadata yang tidak bisa mereka dapatkan. Inspirasi lainnya adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku yang ada di dunia</a>, sehingga kami dapat menghitung berapa banyak buku yang masih harus kami selamatkan. Apakah Anda mengumpulkan metadata? Perhatikan bahwa mhut.org memblokir rentang IP tertentu, jadi VPN mungkin diperlukan. <strong>Android:</strong> Klik menu tiga titik di kanan atas, dan pilih “Tambahkan ke Layar Utama”. <strong>iOS:</strong> Klik tombol “Bagikan” di bagian bawah, dan pilih “Tambahkan ke Layar Utama”. Kami tidak memiliki aplikasi seluler resmi, tetapi Anda dapat menginstal situs web ini sebagai aplikasi. Apakah Anda memiliki aplikasi seluler? Silakan kirim ke <a %(a_archive)s>Internet Archive</a>. Mereka akan menyimpannya dengan baik. Bagaimana cara menyumbangkan buku atau bahan fisik lainnya? Bagaimana saya dapat memesan buku? <a %(a_blog)s>Blog Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — pembaruan reguler <a %(a_software)s>Perangkat Lunak Anna</a> — kode sumber terbuka kami <a %(a_datasets)s>Datasets</a> — tentang data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domain alternatif Apakah ada lebih banyak sumber daya tentang Arsip Anna? <a %(a_translate)s>Terjemahkan di Perangkat Lunak Anna</a> — sistem terjemahan kami <a %(a_wikipedia)s>Wikipedia</a> — lebih banyak tentang kami (tolong bantu perbarui halaman ini, atau buat satu untuk bahasa Anda sendiri!) Pilih pengaturan yang Anda sukai, biarkan kotak pencarian kosong, klik "Cari", dan kemudian tandai halaman tersebut menggunakan fitur bookmark browser Anda. Bagaimana cara menyimpan pengaturan pencarian saya? Kami menyambut peneliti keamanan untuk mencari kerentanan dalam sistem kami. Kami adalah pendukung besar pengungkapan yang bertanggung jawab. Hubungi kami <a %(a_contact)s>di sini</a>. Saat ini kami tidak dapat memberikan hadiah bug bounty, kecuali untuk kerentanan yang memiliki <a %(a_link)s>potensi untuk mengkompromikan anonimitas kami</a>, yang mana kami menawarkan hadiah dalam kisaran $10k-50k. Kami ingin menawarkan cakupan yang lebih luas untuk bug bounty di masa depan! Harap dicatat bahwa serangan rekayasa sosial berada di luar cakupan. Jika Anda tertarik pada keamanan ofensif, dan ingin membantu mengarsipkan pengetahuan dan budaya dunia, pastikan untuk menghubungi kami. Ada banyak cara di mana Anda dapat membantu. Apakah Anda memiliki program pengungkapan yang bertanggung jawab? Kami benar-benar tidak memiliki cukup sumber daya untuk memberikan unduhan berkecepatan tinggi kepada semua orang di dunia, seberapa pun kami menginginkannya. Jika ada dermawan kaya yang ingin membantu menyediakan ini untuk kami, itu akan luar biasa, tetapi sampai saat itu, kami berusaha sebaik mungkin. Kami adalah proyek nirlaba yang hampir tidak dapat bertahan melalui donasi. Inilah sebabnya kami menerapkan dua sistem untuk unduhan gratis, dengan mitra kami: server bersama dengan unduhan lambat, dan server yang sedikit lebih cepat dengan daftar tunggu (untuk mengurangi jumlah orang yang mengunduh pada saat yang sama). Kami juga memiliki <a %(a_verification)s>verifikasi browser</a> untuk unduhan lambat kami, karena jika tidak, bot dan scraper akan menyalahgunakannya, membuat segalanya menjadi lebih lambat bagi pengguna yang sah. Perhatikan bahwa, saat menggunakan Tor Browser, Anda mungkin perlu menyesuaikan pengaturan keamanan Anda. Pada opsi terendah, yang disebut “Standar”, tantangan turnstile Cloudflare berhasil. Pada opsi yang lebih tinggi, yang disebut “Lebih Aman” dan “Paling Aman”, tantangan tersebut gagal. Untuk file besar, terkadang unduhan yang lambat bisa terputus di tengah jalan. Kami merekomendasikan menggunakan pengelola unduhan (seperti JDownloader) untuk melanjutkan unduhan besar secara otomatis. Mengapa unduhan lambat begitu lambat? Pertanyaan umum (FAQ) Gunakan <a %(a_list)s>generator daftar torrent</a> untuk menghasilkan daftar torrent yang paling membutuhkan torrenting, dalam batas ruang penyimpanan Anda. Ya, lihat halaman <a %(a_llm)s>data LLM</a>. Sebagian besar torrent berisi file secara langsung, yang berarti Anda dapat menginstruksikan klien torrent untuk hanya mengunduh file yang diperlukan. Untuk menentukan file mana yang akan diunduh, Anda dapat <a %(a_generate)s>menghasilkan</a> metadata kami, atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Sayangnya, beberapa koleksi torrent berisi file .zip atau .tar di root, dalam hal ini Anda perlu mengunduh seluruh torrent sebelum dapat memilih file individual. Belum ada alat yang mudah digunakan untuk memfilter torrent, tetapi kami menyambut kontribusi. (Kami memiliki <a %(a_ideas)s>beberapa ide</a> untuk kasus yang terakhir ini.) Jawaban panjang: Jawaban singkat: tidak mudah. Kami berusaha untuk meminimalkan duplikasi atau tumpang tindih antara torrent dalam daftar ini, tetapi hal ini tidak selalu dapat dicapai, dan sangat bergantung pada kebijakan perpustakaan sumber. Untuk perpustakaan yang mengeluarkan torrent mereka sendiri, itu di luar kendali kami. Untuk torrent yang dirilis oleh Arsip Anna, kami menduplikasi hanya berdasarkan hash MD5, yang berarti bahwa versi berbeda dari buku yang sama tidak akan diduplikasi. Ya. Ini sebenarnya adalah PDF dan EPUB, mereka hanya tidak memiliki ekstensi di banyak torrent kami. Ada dua tempat di mana Anda dapat menemukan metadata untuk file torrent, termasuk jenis/ekstensi file: 1. Setiap koleksi atau rilis memiliki metadata sendiri. Misalnya, <a %(a_libgen_nonfic)s>torrent Libgen.rs</a> memiliki database metadata yang sesuai yang dihosting di situs web Libgen.rs. Kami biasanya menautkan ke sumber daya metadata yang relevan dari <a %(a_datasets)s>halaman dataset</a> setiap koleksi. 2. Kami merekomendasikan <a %(a_generate)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Ini berisi pemetaan untuk setiap catatan di Arsip Anna ke file torrent yang sesuai (jika tersedia), di bawah “torrent_paths” dalam JSON ElasticSearch. Beberapa klien torrent tidak mendukung ukuran potongan besar, yang dimiliki banyak torrent kami (untuk yang lebih baru kami tidak lagi melakukannya — meskipun ini valid sesuai spesifikasi!). Jadi cobalah klien lain jika Anda mengalami ini, atau keluhkan kepada pembuat klien torrent Anda. Saya ingin membantu seeding, tetapi saya tidak memiliki banyak ruang disk. Torrents terlalu lambat; bisakah saya mengunduh data langsung dari Anda? Bisakah saya mengunduh hanya sebagian dari file, seperti hanya bahasa atau topik tertentu? Bagaimana Anda menangani duplikasi dalam torrent? Bisakah saya mendapatkan daftar torrent dalam format JSON? Saya tidak melihat PDF atau EPUB dalam torrent, hanya file biner? Apa yang harus saya lakukan? Mengapa klien torrent saya tidak dapat membuka beberapa file torrent / tautan magnet Anda? FAQ Torrents Bagaimana saya dapat mengunggah buku baru? Silakan lihat <a %(a_href)s>proyek yang luar biasa ini</a>. Apakah Anda memiliki pemantau waktu aktif? Apasih Arsip anna? Daftar member untuk mengakses fitur unduhan jalur cepat. Kami sekarang mendukung kartu hadiah Amazon, kartu kredit dan debit, kripto, Alipay, dan WeChat. Kamu kehabisan unduhan jalur cepat hari ini. Akses Laporan unduhan per jam dalam kurun waktu 30 hari terahir. Rata-rata/jam: %(hourly)s. Rata-rata/hari: %(daily)s. Kami berkerjasama dengan beberapa rekan dalam mempermudah akses koleksi yang kami miliki untuk semua orang. Karna kami percaya setiap orang memiliki hak yang sama dalam mengakses kumpulan pengetahuan umat manusia, dan <a %(a_search)s>bukan lah beban dari penulis</a>. Datasets yang digunakan dalam Arsip Anna bersifat terbuka, dan dapat digandakan secara besar-besaran mengunakan torrent.<a%(a_datasets)s>Pelajari lebih lanjut...</a> Arsip jangka panjang Database lengkap Cari Buku, artikel, majalah, komik, catatan pustaka,metadata,… Semua <a %(a_code)s>kode</a> dan <a %(a_datasets)s>data</a> kami sepenuhnya open source. <span %(span_anna)s>Arsip Anna</span> merupakan proyek non-profit dengan dua tujuan: <li><strong>Pelestarian:</strong>Membangun pencadangan semua pengetahuan dan budaya umat manusia .</li><li><strong>Akses:</strong> Mewujudkan ketersediaan atas pengetahuan dan budaya tersebut untuk semua orang di dunia.</li> Kami memiliki koleksi data terbesar di dunia dengan kualitas tinggi.<a%(a_llm)s>Pelajari lebih lanjut...</a> LLM training data 🪩 Mirrors: panggilan relawan BIla anda menjalankan servis pembayaran anonimus dengan resiko tinggi, mohon hubungi kami. Kami saat ini sedang mencari tempat iklan. Semua prosedur dapat dilihat diusaha pelestarian kami. Pelestarian Kami telah melestarikan sebanyak <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% dari seluruh buku di dunia</a>. Kami melestarikan buku, artikel, komik, majalah dll dari berbagai sumber <a href="https://en.wikipedia.org/wiki/Shadow_library">shadow libraries</a>, pustaka resmi dll menjadi satu tempat. Semua data ini tersimpan selamanya dengan mempermudah menyalinya dalam jumlah besar — menggunakan torrents — menghasilkan kopian yang sangat banyak di segala penjuru dunia. Yang sebelumnya telah dilakukan oleh beberapa shadow libraries (cth. Sci-Hub, Library Genesis), Yang mana Arsip Anna "membebaskan" pustaka lain yang tidak dapat menyediakan distribusi dalam jumlah besar (cth. Z-Library) atau pun yang bukan merupakan bagian shadow libraries (cth. Internet Archive, DuXiu). Distribusi secara luas ini bergabung dengan kode sumber terbuka, yang membuat halaman web kami dapat bertahan dari takedown, dan memastikan pengetahuan dan budaya unat manusia dilestarikan dalam jangka lama. Pelajari lebih lanjut <a href="/datasets">datasets kami</a>. Bila anda masuk <a %(a_member)s>member</a>, verifikasi browser tidak diperlukan. 🧬&nbsp;SciDB adalah kelanjutan dari Sci-Hub. SicDB Buka DOI Sci-Hub telah <a %(a_paused)s>menghentikan</a> pengunggahan makalah baru. Akses langsung ke %(count)s jurnal ilmiah 🧬&nbsp;SciDB adalah kelanjutan dari Sci-Hub, dengan antarmuka yang sudah dikenal dan tampilan langsung PDF. Masukkan DOI Anda untuk melihat. Kami memiliki koleksi lengkap Sci-Hub, serta makalah baru. Sebagian besar dapat dilihat langsung dengan antarmuka yang sudah dikenal, mirip dengan Sci-Hub. Beberapa dapat diunduh melalui sumber eksternal, dalam hal ini kami menampilkan tautan ke sumber tersebut. Kamu dapat snagat membantu dengan membangun seeding torrent.<a %(a_torrents)s>Pelajari lebih lanjut...</a> >%(count)s jumlah seeder <%(count)s jumlah seeder %(count_min)s–%(count_max)s seeder 🤝 Mencari relawan Sebagai proyek nirlaba dan sumber terbuka, kami selalu mencari orang untuk membantu. Unduhan IPFS Daftar oleh %(by)s, dibuat <span %(span_time)s>%(time)s</span> Simpan ❌ Terjadi kesalahan. Silakan coba lagi. ✅ Tersimpan. Silakan muat ulang halaman. Daftar kosong. Sunting Tambahkan atau hapus dari daftar ini dengan menemukan berkas dan membuka tab "Daftar". Daftar Bagaimana kami bisa membantu Menghapus tumpang tindih (deduplikasi) Ekstraksi teks dan metadata OCR Kami dapat menyediakan akses berkecepatan tinggi ke seluruh koleksi kami, serta ke koleksi yang belum dirilis. Ini adalah akses tingkat perusahaan yang dapat kami sediakan untuk donasi dalam kisaran puluhan ribu USD. Kami juga bersedia menukarnya dengan koleksi berkualitas tinggi yang belum kami miliki. Kami dapat mengembalikan uang Anda jika Anda dapat memberikan kami pengayaan data kami, seperti: Dukung pengarsipan jangka panjang pengetahuan manusia, sambil mendapatkan data yang lebih baik untuk model Anda! <a %(a_contact)s>Hubungi kami</a> untuk mendiskusikan bagaimana kita bisa bekerja sama. Sudah dipahami dengan baik bahwa LLM berkembang dengan data berkualitas tinggi. Kami memiliki koleksi buku, makalah, majalah, dll terbesar di dunia, yang merupakan beberapa sumber teks berkualitas tertinggi. Data LLM Skala dan jangkauan unik Koleksi kami berisi lebih dari seratus juta file, termasuk jurnal akademik, buku teks, dan majalah. Kami mencapai skala ini dengan menggabungkan repositori besar yang sudah ada. Beberapa koleksi sumber kami sudah tersedia dalam jumlah besar (Sci-Hub, dan bagian dari Libgen). Sumber lain kami bebaskan sendiri. <a %(a_datasets)s>Datasets</a> menunjukkan gambaran lengkap. Koleksi kami mencakup jutaan buku, makalah, dan majalah dari sebelum era e-book. Sebagian besar dari koleksi ini sudah di-OCR, dan sudah memiliki sedikit tumpang tindih internal. Lanjutkan Bila anda kehilangan kode rahasia, mohon <a %(a_contact)s>hubungi kami</a> dan berikan keterangan informasi selengkap-lengkapnya. Kamu mungkin memerlukan akun sementara untuk menghubungi kami. Mohon <a %(a_account)s>masuk</a> untuk melihat halaman ini.</a> Untuk mencegah spam dari robot membuat banyak akun, kami memerlukan verifikasi browser kamu terlebih dahulu. Jika Anda terjebak dalam loop tak terbatas, kami merekomendasikan untuk menginstal <a %(a_privacypass)s>Privacy Pass</a>. Hal tersebut juga dapat membantu mematikan pemblokir iklan dan ekstensi browser yang lain. Masuk/Daftar Arsip Anna sedang dalam pemeliharaan sementara. Silakan kembali dalam satu jam. Penulis alternatif Deskripsi alternatif Edisi alternatif Ekstensi alternatif Nama file alternatif Penerbit alternatif Judul alternatif tanggal sumber terbuka Baca lebih lanjut… deskripsi Cari nomor CADAL SSNO di Arsip Anna Cari DuXiu SSID di Arsip Anna Cari nomor DuXiu (DXID) di Arsip Anna Cari di Arsip Anna dengan ISBN Cari nomor OCLC (WorldCat) di Arsip Anna Cari di Arsip Anna untuk ID Open Library Penampil online Arsip Anna %(count)s halaman yang terpengaruh Setelah mengunduh: Versi dokumen yang lebih baik kemungkinan tersedia di %(link)s Unduhan lewat torrent secara besar koleksi Gunakan alat online untuk mengonversi antar format. Alat konversi yang direkomendasikan: %(links)s Untuk file berukuran besar, kami merekomendasikan menggunakan pengelola unduhan untuk mencegah gangguan. Pengelola unduhan yang direkomendasikan: %(links)s Indeks eBook EBSCOhost (Hanya ahli saja) (klik juga “GET” di atas) (klik “GET” di atas) Unduhan eksternal <strong>🚀 Unduhan jalur cepat</strong> yang anda memiliki tersisa %(remaining)s hari ini. Terima kasih telah menjadi member kami! ❤️ <strong>🚀 Jalur unduhan cepat</strong> Kuota unduhan kamu telah habis hari ini. Kamu telah mengunduh berkas ini melalui <strong>🚀 Unduhan jalur cepat</strong> . Link masih dapat digunakan kembali untuk sementara waktu. <strong>🚀 Unduhan jalur cepat</strong> Jadilah <a %(a_membership)s>member</a> untuk dukungan jangka panjang pelestarian buku, jurnal dkk. Dan dapatkan akses unduhan jalur cepat. ❤️ 🚀 Unduhan cepat 🐢 Unduhan jalur lambat Pinjam dari halaman Internet Archive Gateway IPFS #%(num)d (Anda mungkin perlu mencoba berkali-kali dengan IPFS) Libgen.li Libgen.rs Fiksi Libgen.rs Non-Fiksi iklan mereka diketahui mengandung perangkat lunak berbahaya, jadi gunakan pemblokir iklan atau jangan klik iklan Amazon’s “Send to Kindle” djazz’s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (File Nexus/STC bisa tidak dapat diandalkan untuk diunduh) Tidak ada unduhan yang ditemukan. Semua mirror melayani file yang sama, dan harusnya aman untuk digunakan. Walau begitu, selalu berhati-hatilah saat mengunduh file dari internet. Misalnya, pastikan untuk selalu memperbarui perangkat Anda. (tidak ada pengalihan) Buka di penampil kami (buka di penampil) Opsi #%(num)d: %(link)s %(extra)s Temukan dokumen asli di CADAL Cari di DuXiu Temukan dokumen asli di ISBNdb Temukan dokumen asli di WorldCat Cari dokumen asli di Open Library Cari di beberapa sumber data lain dengan ISBN (cetakan dimatikan hanya untuk donatur saja) PubMed Anda akan memerlukan pembaca ebook atau PDF untuk membuka file, tergantung pada format file. Pembaca ebook yang direkomendasikan: %(links)s Arsip Anna 🧬 SciDB Sci-Hub: %(doi)s (DOI yang bersangkutan mungkin tidak tersedia di Sci-Hub) Anda dapat mengirim file PDF dan EPUB ke Kindle atau Kobo eReader Anda. Alat yang direkomendasikan: %(links)s Informasi lebih lanjut di <a %(a_slow)s>FAQ</a>. Dukung penulis dan perpustakaan Jika Anda menyukai ini dan mampu membelinya, pertimbangkan untuk membeli yang asli, atau mendukung penulis secara langsung. Jika ini tersedia di perpustakaan lokal Anda, pertimbangkan untuk meminjamnya secara gratis di sana. Server Mitra untuk sementara tidak tersedia untuk mengunduh dokumen ini. torrent Dari mitra terpercaya. Z-Library Z-Library di Tor (membutuhkan browser TOR) tampilkan unduhan eksternal <span class="font-bold">❌ Berkas ini bermasalah dan telah disembunyikan dari perpustakaan sumber.</span> Terkadang karena ada permintaan pemegang hak cipta, terkadang karena terdapat berkas lain yang lebih baik, namun umumnya karena terdapat masalah pada berkasnya sendiri. Berkas ini mungkin masih layak unduh, namun kami sarankan mencari berkas alternatif. Lebih lanjut: Jika Anda tetap ingin mengunduh berkas ini, pastikan Anda membukanya dengan menggunakan peranti lunak yang terpercaya dan termutakhir. Komentar metadata AA: Pencarian di Arsip Anna untuk "%(name)s" Penjelajah Kode: Lihat di Penjelajah Kode “%(name)s” URL: Halaman Web: Bila anda memiliki dokumen ini dan belum tersedia di Arsip Anna, mohon kiranya bersedia <a %(a_request)s>mengunggahnya</a>. Dokumen Internet Archive Controlled Digital Lending “%(id)s” Ini merupakan record dari dokumen Internet Archive, bukan merupakan dokumen unduhan. Kamu dapat mencoba meminjam buku melalui (link di bawah ini), atau gunakan URL saat <a %(a_request)s>meminta dokumen</a>. Perbaiki metadata Catatan metadata CADAL SSNO %(id)s Ini merupakan catatan metadata, bukan dokumen yang dapat diunduh. Kamu dapat menggunakan URL saat<a %(a_request)s>meminta dokumen</a>. Catatan metadata DuXiu SSID %(id)s Catatan metadata ISBNdb %(id)s Catatan metadata MagzDB ID %(id)s Catatan metadata Nexus/STC ID %(id)s Catatan metadata dokumen OCLC (WorldCat) nomor %(id)s Catatan metadata Open Library %(id)s Dokumen Sci-Hub “%(id)s” Tidak ditemukan “%(md5_input)s” tidak ditemukan di database kami. Tambahkan komentar (%(count)s) Anda bisa mendapatkan md5 dari URL, misalnya MD5 dari versi yang lebih baik dari file ini (jika ada). Isi ini jika ada file lain yang sangat mirip dengan file ini (edisi yang sama, ekstensi file yang sama jika Anda dapat menemukannya), yang seharusnya digunakan orang daripada file ini. Jika Anda mengetahui versi yang lebih baik dari file ini di luar Arsip Anna, silakan <a %(a_upload)s>unggah</a>. Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi. Anda telah meninggalkan komentar. Mungkin butuh waktu satu menit untuk muncul. Silakan gunakan <a %(a_copyright)s>formulir klaim DMCA / Hak Cipta</a>. Deskripsikan masalah (wajib) Jika file ini memiliki kualitas yang baik, Anda dapat mendiskusikan apa saja tentangnya di sini! Jika tidak, silakan gunakan tombol “Laporkan masalah file”. Kualitas file bagus (%(count)s) Kualitas file Pelajari cara <a %(a_metadata)s>meningkatkan metadata</a> untuk file ini sendiri. Deskripsi masalah Silakan <a %(a_login)s>masuk</a>. Saya menyukai buku ini! Bantu komunitas dengan melaporkan kualitas file ini! 🙌 Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi. Laporkan masalah file (%(count)s) Terima kasih telah mengirimkan laporan Anda. Laporan tersebut akan ditampilkan di halaman ini, serta ditinjau secara manual oleh Anna (sampai kami memiliki sistem moderasi yang tepat). Tinggalkan komentar Kirim laporan Apa yang salah dengan file ini? Total Pinjaman (%(count)s) Komentar (%(count)s) Total undihan (%(count)s) Jelajahi metadata (%(count)s) Total lists (%(count)s) Total statistik (%(count)s) Untuk informasi tentang file ini, lihat <a %(a_href)s>file JSON</a>. Ini adalah file yang dikelola oleh perpustakaan <a %(a_ia)s>IA’s Controlled Digital Lending</a>, dan diindeks oleh Arsip Anna untuk pencarian. Untuk informasi tentang berbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>. Metadata dari catatan terkait Perbaiki metadata di Open Library “file MD5” adalah hash yang dihitung dari konten file, dan cukup unik berdasarkan konten tersebut. Semua perpustakaan bayangan yang telah kami indeks di sini terutama menggunakan MD5 untuk mengidentifikasi file. Sebuah file mungkin muncul di beberapa perpustakaan bayangan. Untuk informasi tentang berbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>. Laporkan kualitas file Total unduhan: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadata Ceko %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Peringatan: beberapa catatan terkait: Ketika Anda melihat sebuah buku di Arsip Anna, Anda dapat melihat berbagai bidang: judul, penulis, penerbit, edisi, tahun, deskripsi, nama file, dan lainnya. Semua informasi tersebut disebut <em>metadata</em>. Karena kami menggabungkan buku dari berbagai <em>perpustakaan sumber</em>, kami menampilkan metadata apa pun yang tersedia di perpustakaan sumber tersebut. Misalnya, untuk buku yang kami dapatkan dari Library Genesis, kami akan menampilkan judul dari database Library Genesis. Terkadang sebuah buku ada di <em>beberapa</em> perpustakaan sumber, yang mungkin memiliki bidang metadata yang berbeda. Dalam kasus tersebut, kami hanya menampilkan versi terpanjang dari setiap bidang, karena versi tersebut diharapkan mengandung informasi yang paling berguna! Kami masih akan menampilkan bidang lainnya di bawah deskripsi, misalnya sebagai "judul alternatif" (tetapi hanya jika berbeda). Kami juga mengekstrak <em>kode</em> seperti pengidentifikasi dan pengklasifikasi dari perpustakaan sumber. <em>Pengidentifikasi</em> secara unik mewakili edisi tertentu dari sebuah buku; contohnya adalah ISBN, DOI, Open Library ID, Google Books ID, atau Amazon ID. <em>Pengklasifikasi</em> mengelompokkan beberapa buku serupa; contohnya adalah Dewey Decimal (DCC), UDC, LCC, RVK, atau GOST. Terkadang kode-kode ini secara eksplisit ditautkan di perpustakaan sumber, dan terkadang kami dapat mengekstraknya dari nama file atau deskripsi (terutama ISBN dan DOI). Kami dapat menggunakan pengidentifikasi untuk menemukan catatan di <em>koleksi metadata saja</em>, seperti OpenLibrary, ISBNdb, atau WorldCat/OCLC. Ada <em>tab metadata</em> khusus di mesin pencari kami jika Anda ingin menjelajahi koleksi tersebut. Kami menggunakan catatan yang cocok untuk mengisi bidang metadata yang hilang (misalnya jika judul hilang), atau misalnya sebagai "judul alternatif" (jika ada judul yang sudah ada). Untuk melihat dengan tepat dari mana metadata sebuah buku berasal, lihat <em>tab "Detail teknis"</em> di halaman buku. Ini memiliki tautan ke JSON mentah untuk buku tersebut, dengan petunjuk ke JSON mentah dari catatan asli. Untuk informasi lebih lanjut, lihat halaman berikut: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Pencarian (tab metadata)</a>, <a %(a_codes)s>Penjelajah Kode</a>, dan <a %(a_example)s>Contoh metadata JSON</a>. Akhirnya, semua metadata kami dapat <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>diunduh</a> sebagai database ElasticSearch dan MariaDB. Latar Belakang Anda dapat membantu pelestarian buku dengan memperbaiki metadata! Pertama, baca latar belakang tentang metadata di Arsip Anna, dan kemudian pelajari cara memperbaiki metadata melalui tautan dengan Open Library, dan dapatkan keanggotaan gratis di Arsip Anna. Perbaiki metadata Jadi jika Anda menemukan file dengan metadata yang buruk, bagaimana cara memperbaikinya? Anda dapat pergi ke perpustakaan sumber dan mengikuti prosedurnya untuk memperbaiki metadata, tetapi apa yang harus dilakukan jika sebuah file ada di beberapa perpustakaan sumber? Ada satu pengidentifikasi yang diperlakukan khusus di Arsip Anna. <strong>Bidang annas_archive md5 di Open Library selalu mengesampingkan semua metadata lainnya!</strong> Mari kita mundur sedikit dan belajar tentang Open Library. Open Library didirikan pada tahun 2006 oleh Aaron Swartz dengan tujuan "satu halaman web untuk setiap buku yang pernah diterbitkan". Ini semacam Wikipedia untuk metadata buku: semua orang dapat mengeditnya, berlisensi bebas, dan dapat diunduh secara massal. Ini adalah database buku yang paling selaras dengan misi kami — sebenarnya, Arsip Anna terinspirasi oleh visi dan kehidupan Aaron Swartz. Alih-alih menciptakan kembali roda, kami memutuskan untuk mengarahkan sukarelawan kami ke Open Library. Jika Anda melihat buku yang memiliki metadata yang salah, Anda dapat membantu dengan cara berikut: Perhatikan bahwa ini hanya berlaku untuk buku, bukan makalah akademis atau jenis file lainnya. Untuk jenis file lainnya kami tetap merekomendasikan mencari perpustakaan sumber. Mungkin perlu beberapa minggu untuk perubahan dimasukkan ke dalam Anna’s Archive, karena kami perlu mengunduh data dump terbaru dari Open Library, dan memperbarui indeks pencarian kami.  Pergi ke <a %(a_openlib)s>situs web Open Library</a>. Temukan catatan buku yang benar. <strong>PERINGATAN:</strong> pastikan untuk memilih <strong>edisi</strong> yang benar. Di Open Library, Anda memiliki "karya" dan "edisi". Sebuah "karya" bisa jadi "Harry Potter dan Batu Bertuah". Sebuah "edisi" bisa jadi: Edisi pertama tahun 1997 diterbitkan oleh Bloomsbery dengan 256 halaman. Edisi paperback tahun 2003 diterbitkan oleh Raincoast Books dengan 223 halaman. Terjemahan Polandia tahun 2000 “Harry Potter I Kamie Filozoficzn” oleh Media Rodzina dengan 328 halaman. Semua edisi tersebut memiliki ISBN dan konten yang berbeda, jadi pastikan untuk memilih yang benar! Edit catatan (atau buat jika belum ada), dan tambahkan sebanyak mungkin informasi yang berguna! Anda sudah di sini, jadi buatlah catatan tersebut benar-benar luar biasa. Di bawah “ID Numbers” pilih “Anna’s Archive” dan tambahkan MD5 dari buku tersebut dari Anna’s Archive. Ini adalah rangkaian panjang huruf dan angka setelah “/md5/” di URL. Cobalah untuk menemukan file lain di Anna’s Archive yang juga cocok dengan catatan ini, dan tambahkan juga. Di masa depan kita bisa mengelompokkan mereka sebagai duplikat di halaman pencarian Anna’s Archive. Setelah selesai, tuliskan URL yang baru saja Anda perbarui. Setelah Anda memperbarui setidaknya 30 catatan dengan MD5 dari Anna’s Archive, kirimkan kami sebuah <a %(a_contact)s>email</a> dan kirimkan daftar tersebut. Kami akan memberikan Anda keanggotaan gratis untuk Anna’s Archive, sehingga Anda dapat lebih mudah melakukan pekerjaan ini (dan sebagai ucapan terima kasih atas bantuan Anda). Ini harus merupakan edit berkualitas tinggi yang menambahkan sejumlah besar informasi, jika tidak permintaan Anda akan ditolak. Permintaan Anda juga akan ditolak jika ada edit yang dibatalkan atau dikoreksi oleh moderator Open Library. Tautan Open Library Jika Anda terlibat secara signifikan dalam pengembangan dan operasi pekerjaan kami, kami dapat membahas pembagian lebih banyak pendapatan donasi dengan Anda, untuk Anda gunakan sesuai kebutuhan. Kami hanya akan membayar hosting setelah Anda memiliki semuanya dan telah menunjukkan bahwa Anda mampu menjaga arsip tetap diperbarui dengan pembaruan. Ini berarti Anda harus membayar untuk 1-2 bulan pertama dari kantong Anda sendiri. Waktu Anda tidak akan dikompensasi (dan begitu juga waktu kami), karena ini adalah pekerjaan sukarela murni. Kami bersedia menanggung biaya hosting dan VPN, awalnya hingga $200 per bulan. Ini cukup untuk server pencarian dasar dan proxy yang dilindungi Controlled Digital Lending. Biaya hosting Tolong <strong>jangan hubungi kami</strong> untuk meminta izin, atau untuk pertanyaan dasar. Tindakan berbicara lebih keras daripada kata-kata! Semua informasi sudah tersedia, jadi langsung saja mulai dengan menyiapkan mirror Anda. Jangan ragu untuk memposting tiket atau permintaan penggabungan ke Gitlab kami saat Anda menghadapi masalah. Kami mungkin perlu membangun beberapa fitur khusus mirror dengan Anda, seperti rebranding dari "Anna’s Archive" ke nama situs web Anda, (awalnya) menonaktifkan akun pengguna, atau menautkan kembali ke situs utama kami dari halaman buku. Setelah Anda menjalankan mirror Anda, silakan hubungi kami. Kami akan senang meninjau OpSec Anda, dan setelah itu solid, kami akan menautkan ke mirror Anda, dan mulai bekerja lebih dekat dengan Anda. Terima kasih sebelumnya kepada siapa pun yang bersedia berkontribusi dengan cara ini! Ini bukan untuk yang lemah hati, tetapi akan memperkuat umur panjang perpustakaan terbuka terbesar dalam sejarah manusia. Memulai Untuk meningkatkan ketahanan Arsip Anna, kami mencari relawan untuk menjalankan cermin. Versi Anda jelas dibedakan sebagai mirror, misalnya "Arsip Bob, sebuah mirror Anna’s Archive". Anda bersedia mengambil risiko yang terkait dengan pekerjaan ini, yang cukup signifikan. Anda memiliki pemahaman mendalam tentang keamanan operasional yang diperlukan. Isi dari <a %(a_shadow)s>postingan</a> <a %(a_pirate)s>ini</a> sudah jelas bagi Anda. Awalnya kami tidak akan memberi Anda akses ke unduhan server mitra kami, tetapi jika semuanya berjalan dengan baik, kami dapat membagikannya dengan Anda. Anda menjalankan basis kode sumber terbuka Anna’s Archive, dan Anda secara teratur memperbarui baik kode maupun data. Anda bersedia berkontribusi pada <a %(a_codebase)s>basis kode</a> kami — dalam kolaborasi dengan tim kami — untuk mewujudkan hal ini. Kami mencari ini: Cermin: panggilan untuk relawan Buat donasi lainnya. Belum ada donasi. <a %(a_donate)s>Buat donasi pertama saya.</a> Rincian donasi tidak ditampilkan secara publik. Donasi saya 📡 Untuk membuat pencadangan sekala besar dari koleksi kami, silahkan periksa halaman <a %(a_datasets)s>Dataset</a> dan <a %(a_torrents)s>Torrent</a>. Unduhan dari alamat IP Anda dalam 24 jam terakhir: %(count)s. 🚀 Untuk mengakses unduhan jalur cepat dan lewati pemeriksaan browser, <a %(a_membership)s>daftar sebagai member</a>. Unduh lewat web rekan Silakan lanjutkan menjelajahi Arsip Anna di tab yang berbeda sambil menunggu (jika browser Anda mendukung penyegaran tab latar belakang). Silakan tunggu beberapa halaman unduhan dimuat secara bersamaan (tetapi harap hanya unduh satu file pada satu waktu per server). Setelah Anda mendapatkan tautan unduhan, tautan tersebut berlaku selama beberapa jam. Terima kasih telah menunggu, ini menjaga situs web tetap gratis untuk semua orang! 😊 🔗 Semua link unduhan untuk dokumen ini: <a %(a_main)s>Halaman utama dokumen</a>. ❌ Unduhan lambat tidak tersedia melalui VPN Cloudflare atau dari alamat IP Cloudflare. ❌ Unduhan jalur lambat hanya tersedia melalui halaman web resmi. Kunjungi %(websites)s. 📚 Gunakan URL berikut untuk memgunduh: <a %(a_download)s>Unduh sekarang</a>. Untuk memberikan kesempatan kepada semua orang untuk mengunduh file secara gratis, Anda perlu menunggu sebelum dapat mengunduh file ini. Silakan tunggu <span %(span_countdown)s>%(wait_seconds)s</span> detik untuk mengunduh file ini. Peringatan: telah terjadi unduhan terlalu banyak dari alamat IP kamu dalam kurun waktu 24 jam. Unduhan dapat menjadi lebih lambat dari biasanya. Jika Anda menggunakan VPN, koneksi internet bersama, atau ISP Anda berbagi IP, peringatan ini mungkin disebabkan oleh itu. Simpan ❌ Terjadi kesalahan. Silakan coba lagi. ✅ Tersimpan. Silakan muat ulang halaman. Ubah nama tampilan Anda. Pengidentifikasi Anda (bagian setelah "#" ) tidak dapat diubah. Profil dibuat <span %(span_time)s>%(time)s</span> Sunting Daftar Buat daftar baru dengan menemukan berkas dan membuka tab "Daftar". Belum ada daftar saat ini Profil tidak ditemukan. Profil Untuk saat ini, kami belum dapat membantu dalam pemesanan buku. Mohon jangan mengirimkan email kepada kami terkait dengan pemesanan buku. Mohon ajukan pemesanan kamu melalui forum Z-Library atau Libgen. Catatan di Arsip Anna DOI: %(doi)s Unduhan SicDB Nexus/STC Pratinjau belum tersedia. Unduh file dari <a %(a_path)s>Arsip Anna</a>. Untuk mendukung aksesibilitas dan pelestarian jangka panjang pengetahuan manusia, jadilah <a %(a_donate)s>anggota</a>. Sebagai bonus, 🧬&nbsp;SciDB memuat lebih cepat untuk anggota, tanpa batasan. Tidak berfungsi? Coba <a %(a_refresh)s>segarkan</a>. Sic-Hub Tambahkan kriteria pencarian yang labih spesifik Cari deskripsi dan komentar metadata Tahun diterbitkan Tingkat lanjut Akses Isi Tampilkan Daftar Tabel Jenis file Bahasa Urutkan berdasarkan Terbesar Paling relevan Terbaru (ukuran dokumen) (sumber terbuka) (tahun terbit) Terlama Acak Terkecil Sumber diambil dan dibuka oleh AA Total Digital Lending (%(count)s) Total Artikel Jurnal (%(count)s) Kami mendapatkan kesesuaian sebanyak: %(in)s. Kamu dapat menuju ke URL untuk <a%(a_request)s>memgajukan permintaan dokumen</a>. Total Metadata (%(count)s) Untuk menjelajahi indeks pencarian berdasarkan kode, gunakan <a %(a_href)s>Codes Explorer</a>. Indeks pencarian diperbarui setiap bulan. Saat ini, di dalam indeks pencarian terdapat entri hingga %(last_data_refresh_date)s. Untuk informasi teknis lebih lanjut, lihat %(link_open_tag)slaman dataset</a>. Kecualikan Hanya sertakan Belum diperiksa Lebih lanjut… Berikutnya … Sebelumnya Indeks pencarian saat ini termasuk metadata dari pustaka Internet Archiev Controlled Digital Lending.<a %(a_datasets)s>Pelajari lebih lanjut dataset kami</a>. Untuk mempelajari pustaka digital lebih lanjut, kunjungi <a %(a_wikipedia)s>Wikipedia</a> dan <a %(a_mobileread)s>Wiki Mobail</a>. Untuk DMCA / klaim hak cipta <a %(a_copyright)s>klik disini</a>. Waktu unduh Kesalahan selama pencarian. Coba <a %(a_reload)s>muat ulang halaman</a>. Bila masih bermasalah, mohon hubungi kami di %(email)s. Unduhan cepat Faktanya, siapa pun dapat membantu melestarikan file-file ini dengan menanam <a %(a_torrents)s>daftar torrent terpadu kami</a>. ➡️ Terkadang ini terjadi secara tidak benar ketika server pencarian lambat. Dalam kasus seperti itu, <a %(a_attrs)s>memuat ulang</a> dapat membantu. ❌ Berkas ini bermasalah. Mencari makalah? Indeks pencarian saat ini termasuk metadata dari beberapa sumber metadata. <a %(a_datasets)s>Pelajari lebih lanjut datasets kami</a>. Terdapat banyak sumber metadata untuk karya tulis dari penjuruh dunia. <a %(a_wikipedia)s>Halaman Wikipedia ini</a> merupakan permulaan yang baik, akan tetapi bila anda tahu list yang lain, tolong beri tahu kami. Untuk metadata, kami menampilkan catatan dalam keadaan aslinya. Kami tidak melakukan pengabungan metadata. Kami pada saat ini memiliki katalog terbuka terlengkap mencakup buku, artikel, dan karya tulis lainya. Kami melakukan mirror Sic-Hub, Library, Genesis, Z-Library, <a%(a_datasets)s>dll</a>. <span class="font-bold">Tidak ada file ditemukan.</span> Silahkan gunakan kata kunci lain atau filter pencarian Anda. Hasil %(from)s-%(to)s (%(total)s total) Bila anda mengetahui "pustaka pribadi" lain yang dapat kami mirror, atau bila anda memiliki pertanyaan,silahkan hubungi kami di %(email)s. %(num)d kecocokan sebagian %(num)d+ kecocokan sebagian Ketikan kata kunci pada kotak, untuk pencarian dokumen di pustaka digital. Ketikan kata kunci pada kotak untuk pencarian di katalog kami dari %(count)s dokumen yang dapat langsung diunduh, yang telah kami <a %(a_preserve)s>lestarikan selamanya</a>. Ketikan kata kunci pada kotak untuk melakukan pencarian. Ketikan dalam kotak untuk melakukan pencarian pada %(count)s jurnal dan artikel akademik dalam katalog kami, yang telah kami <a %(a_preserve)s>lestarikan untuk selamanya</a>. Ketikkan kata kunci pada kotak untuk melakukan pencarian metadata dari pustaka. Ini dapat berguna saat <a %(a_request)s>permintaan dokumen</a>. Tip: Untuk navigasi cepat gunakan tombol “/” (fokus pencarian), “enter” (cari), “j” (Ke atas), “k” (Ke bawah). Ini adalah catatan metadata, <span %(classname)s>bukan</span> file yang dapat diunduh. Opsi pencarian Cari Digital Lending Unduhan Artikel jurnal Metadata Pencarian baru %(search_input)s - Cari Pencarian terlalu lama, kemumgkinan hasil yang kamu dapatkan kurang akurat. Terkadang <a %(a_reload)s>memuat ulang</a> halaman dapat membantu. Pencarian terlalu lama, kata kunci yang anda gunakan terlalu umum. Penyaringan kemungkinan kurang akurat. Untuk unggahan ukuran besar(lebih dari 10,000 dokumen) yang tidak dapat dilayani oleh Libgen atau Z-Library, mohon untuk menghubungi kami di %(a_email)s. Untuk Libgen.li, pastikan untuk terlebih dahulu masuk ke <a %(a_forum)s>forum mereka</a> dengan nama pengguna %(username)s dan kata sandi %(password)s, lalu kembali ke <a %(a_upload_page)s>halaman unggah</a> mereka. Untuk saat ini, kami menyarankan untuk mengunggah buku-buku baru ke saluran Library Genesis yang tersedia. Berikut adalah <a %(a_guide)s>panduan yang berguna</a>. Perhatikan bahwa kedua saluran yang kami indeks di situs web ini mengambil dari sistem unggahan yang sama. Untuk unggahan kecil (hingga 10.000 file) silakan unggah ke %(first)s dan %(second)s. Sebagai alternatif, Anda dapat mengunggahnya ke Z-Library <a %(a_upload)s>di sini</a>. Untuk mengunggah makalah akademis, harap juga (selain ke Library Genesis) unggah ke <a %(a_stc_nexus)s>STC Nexus</a>. Mereka adalah shadow library terbaik untuk makalah baru. Kami belum mengintegrasikan mereka, tetapi kami akan melakukannya di suatu titik. Anda dapat menggunakan <a %(a_telegram)s>bot unggah mereka di Telegram</a>, atau hubungi alamat yang tercantum dalam pesan yang disematkan jika Anda memiliki terlalu banyak file untuk diunggah dengan cara ini. <span %(label)s>Kerja sukarela berat (hadiah USD$50-USD$5,000):</span> jika Anda dapat mendedikasikan banyak waktu dan/atau sumber daya untuk misi kami, kami ingin bekerja lebih dekat dengan Anda. Akhirnya Anda bisa bergabung dengan tim inti. Meskipun anggaran kami ketat, kami dapat memberikan <span %(bold)s>💰 hadiah uang</span> untuk pekerjaan yang paling intens. <span %(label)s>Pekerjaan sukarela ringan:</span> jika Anda hanya dapat meluangkan beberapa jam di sana-sini, masih ada banyak cara Anda dapat membantu. Kami menghargai sukarelawan yang konsisten dengan <span %(bold)s>🤝 keanggotaan di Anna’s Archive</span>. Anna’s Archive bergantung pada sukarelawan seperti Anda. Kami menyambut semua tingkat komitmen, dan memiliki dua kategori utama bantuan yang kami cari: Jika Anda tidak dapat menyumbangkan waktu Anda, Anda masih bisa membantu kami dengan <a %(a_donate)s>menyumbangkan uang</a>, <a %(a_torrents)s>menyebarkan torrent kami</a>, <a %(a_uploading)s>mengunggah buku</a>, atau <a %(a_help)s>memberitahu teman-teman Anda tentang Arsip Anna</a>. <span %(bold)s>Perusahaan:</span> kami menawarkan akses langsung berkecepatan tinggi ke koleksi kami sebagai imbalan untuk donasi tingkat perusahaan atau pertukaran koleksi baru (misalnya pemindaian baru, datasets OCR, memperkaya data kami). <a %(a_contact)s>Hubungi kami</a> jika ini Anda. Lihat juga <a %(a_llm)s>halaman LLM kami</a>. Hadiah Kami selalu mencari orang dengan keterampilan pemrograman atau keamanan ofensif yang solid untuk terlibat. Anda dapat membuat dampak besar dalam melestarikan warisan umat manusia. Sebagai ucapan terima kasih, kami memberikan keanggotaan untuk kontribusi yang solid. Sebagai ucapan terima kasih yang besar, kami memberikan hadiah uang untuk tugas-tugas yang sangat penting dan sulit. Ini seharusnya tidak dianggap sebagai pengganti pekerjaan, tetapi ini adalah insentif tambahan dan dapat membantu dengan biaya yang dikeluarkan. Sebagian besar kode kami bersifat open source, dan kami akan meminta hal yang sama dari kode Anda saat memberikan hadiah. Ada beberapa pengecualian yang dapat kita diskusikan secara individual. Hadiah diberikan kepada orang pertama yang menyelesaikan tugas. Jangan ragu untuk mengomentari tiket hadiah untuk memberi tahu orang lain bahwa Anda sedang mengerjakan sesuatu, sehingga orang lain dapat menahan diri atau menghubungi Anda untuk bekerja sama. Namun, perlu diketahui bahwa orang lain masih bebas untuk mengerjakannya juga dan mencoba mengalahkan Anda. Namun, kami tidak memberikan hadiah untuk pekerjaan yang ceroboh. Jika dua pengajuan berkualitas tinggi dibuat berdekatan satu sama lain (dalam satu atau dua hari), kami mungkin memilih untuk memberikan hadiah kepada keduanya, atas kebijaksanaan kami, misalnya 100%% untuk pengajuan pertama dan 50%% untuk pengajuan kedua (jadi total 150%%). Untuk hadiah yang lebih besar (terutama hadiah scraping), harap hubungi kami ketika Anda telah menyelesaikan ~5%% dari itu, dan Anda yakin bahwa metode Anda akan dapat diukur hingga tonggak penuh. Anda harus membagikan metode Anda dengan kami sehingga kami dapat memberikan umpan balik. Juga, dengan cara ini kami dapat memutuskan apa yang harus dilakukan jika ada beberapa orang yang mendekati hadiah, seperti mungkin memberikannya kepada beberapa orang, mendorong orang untuk bekerja sama, dll. PERINGATAN: tugas dengan hadiah tinggi <span %(bold)s>sulit</span> — mungkin bijaksana untuk memulai dengan yang lebih mudah. Pergi ke <a %(a_gitlab)s>daftar masalah Gitlab kami</a> dan urutkan berdasarkan “Label priority”. Ini menunjukkan urutan tugas yang kami pedulikan. Tugas tanpa hadiah eksplisit masih memenuhi syarat untuk keanggotaan, terutama yang ditandai “Accepted” dan “Favorit Anna”. Anda mungkin ingin memulai dengan “Proyek pemula”. Sukarela ringan Kami sekarang juga memiliki saluran Matrix yang disinkronkan di %(matrix)s. Jika Anda memiliki beberapa jam luang, Anda dapat membantu dengan berbagai cara. Pastikan untuk bergabung dengan <a %(a_telegram)s>obrolan sukarelawan di Telegram</a>. Sebagai tanda penghargaan, kami biasanya memberikan 6 bulan “Pustakawan Pemberuntung” untuk pencapaian dasar, dan lebih banyak lagi untuk kerja sukarela yang berkelanjutan. Semua pencapaian memerlukan pekerjaan berkualitas tinggi — pekerjaan yang ceroboh lebih merugikan kami daripada membantu dan kami akan menolaknya. Silakan <a %(a_contact)s>email kami</a> ketika Anda mencapai pencapaian. %(links)s tautan atau tangkapan layar dari permintaan yang Anda penuhi. Memenuhi permintaan buku (atau makalah, dll) di forum Z-Library atau Library Genesis. Kami tidak memiliki sistem permintaan buku sendiri, tetapi kami mencerminkan perpustakaan tersebut, jadi membuat mereka lebih baik juga membuat Arsip Anna lebih baik. Pencapaian Tugas Tergantung pada tugas. Tugas kecil yang diposting di <a %(a_telegram)s>obrolan sukarelawan di Telegram</a>. Biasanya untuk keanggotaan, kadang-kadang untuk hadiah kecil. Tugas kecil diposting di grup obrolan relawan kami. Pastikan untuk meninggalkan komentar pada masalah yang Anda perbaiki, agar orang lain tidak menduplikasi pekerjaan Anda. %(links)s tautan dari catatan yang Anda perbaiki. Anda dapat menggunakan <a %(a_list)s>daftar masalah metadata acak</a> sebagai titik awal. Meningkatkan metadata dengan <a %(a_metadata)s>menghubungkan</a> dengan Open Library. Ini harus menunjukkan Anda memberi tahu seseorang tentang Arsip Anna, dan mereka berterima kasih kepada Anda. %(links)s tautan atau tangkapan layar. Menyebarkan informasi tentang Arsip Anna. Misalnya, dengan merekomendasikan buku di AA, menautkan ke posting blog kami, atau secara umum mengarahkan orang ke situs web kami. Menerjemahkan sepenuhnya satu bahasa (jika belum hampir selesai). <a %(a_translate)s>Menerjemahkan</a> situs web. Tautan ke riwayat suntingan yang menunjukkan Anda membuat kontribusi signifikan. Meningkatkan halaman Wikipedia untuk Arsip Anna dalam bahasa Anda. Sertakan informasi dari halaman Wikipedia AA dalam bahasa lain, dan dari situs web dan blog kami. Tambahkan referensi ke AA di halaman relevan lainnya. Sukarelawan & Hadiah 