msgid "layout.index.invalid_request"
msgstr "Permin<PERSON>an tidak ditemukan. Kunjungi halamam %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "Libgen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Antarmuka Pustaka Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " dan "

msgid "layout.index.header.tagline_and_more"
msgstr "dan lain-lain"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Mirror kami %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Kami menggunakan sumber terbuka %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Semua kode dan data kami sepenuhnya open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Pustaka terbuka dan terbesar dalam sejarah manusia."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;buku, %(paper_count)s&nbsp;dokumen — dilestarikan selamanya."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Perpustakaan open-source dan open-data terbesar di dunia. ⭐️&nbsp;Menyediakan salinan dari Sci-Hub, Library Genesis, Z-Library, dan lainnya. 📈&nbsp;%(book_any)s buku, %(journal_article)s makalah, %(book_comic)s komik, %(magazine)s majalah — tetap ada selamanya."

msgid "layout.index.header.tagline_short"
msgstr "📚 Pustaka sumber terbuka terbesar di dunia.<br>⭐️ Mirrors Scihub, Libgen, Zlib, and more."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata yang tidak benar (misalnya, judul, deskripsi, gambar sampul yang salah)"

msgid "common.md5_report_type_mapping.download"
msgstr "Masalah saat mengunduh (misalnya, tidak dapat terhubung, pesan kesalahan, sangat lambat)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Berkas tidak dapat dibuka (misalnya, berkas rusak, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Kualitas buruk (misalnya, masalah format, kualitas pemindaian yang buruk, halaman yang hilang)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / berkas seharusnya dihapus (misalnya, iklan, konten kasar)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Klaim hak cipta"

msgid "common.md5_report_type_mapping.other"
msgstr "Lainnya"

msgid "common.membership.tier_name.bonus"
msgstr "Bonus unduhan"

msgid "common.membership.tier_name.2"
msgstr "Pustaka Cemerlang"

msgid "common.membership.tier_name.3"
msgstr "Pustakawan Beruntung"

msgid "common.membership.tier_name.4"
msgstr "penyimpanan data yang mempesona"

msgid "common.membership.tier_name.5"
msgstr "Arsiparis Luar Biasa"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "Belum terbayar"

msgid "common.donation.order_processing_status_labels.1"
msgstr "Telah Dibayar"

msgid "common.donation.order_processing_status_labels.2"
msgstr "Dibatalkan"

msgid "common.donation.order_processing_status_labels.3"
msgstr "Kadaluarsa"

msgid "common.donation.order_processing_status_labels.4"
msgstr "Menunggu Anna untuk mengkonfirmasi"

msgid "common.donation.order_processing_status_labels.5"
msgstr "tidak sesuai"

msgid "page.donate.title"
msgstr "Donasi"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Anda memiliki <a %(a_donation)s>donasi yang sedang berlangsung</a>. Harap selesaikan atau batalkan donasi tersebut sebelum membuat donasi baru."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Tampilkan semua donasi saya</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archive adalah sebuah proyek nirlaba, open-source, dan open-data. Dengan mendonasikan dan menjadi anggota, Anda mendukung operasional dan pengembangan kami. Kepada semua anggota kami: terima kasih telah menjaga kelangsungan kami! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Untuk informasi lebih lanjut, lihat <a %(a_donate)s>Tanya Jawab terkait Donasi</a>."

msgid "page.donate.refer.text1"
msgstr "Untuk mendapatkan unduhan lebih banyak, rekomendasikan <a %(a_refer)s>pada temanmu</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Selamat kamu memdapatkan %(percentage)s%% bonus unduhan jalur cepat, dikarnakan refrensi dari temanmu %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Berlaku selama periode membersip."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s unduhan cepat per hari"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "jika Anda berdonasi bulan ini!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / bulan"

msgid "page.donate.buttons.join"
msgstr "Bergabung"

msgid "page.donate.buttons.selected"
msgstr "terpilih"

msgid "page.donate.buttons.up_to_discounts"
msgstr "Diskon hingga %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "Makalah SciDB <strong>tak terbatas</strong> tanpa verifikasi"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Akses API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Dapatkan <strong>%(percentage)s%% unduhan bonus</strong> dengan <a %(a_refer)s>mengajak teman</a>."

msgid "page.donate.perks.credits"
msgstr "Nama pengguna Anda atau anonim disebutkan dalam kredit"

msgid "page.donate.perks.previous_plus"
msgstr "Keuntungan sebelumnya, ditambah:"

msgid "page.donate.perks.early_access"
msgstr "Akses awal ke fitur-fitur baru"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram eksklusif dengan pembaharuan dibelakang layar"

msgid "page.donate.perks.adopt"
msgstr "\"Adopt a torrent\": nama pengguna atau pesan Anda dalam nama file torrent <div %(div_months)s>sekali setiap 12 bulan keanggotaan</div>"

msgid "page.donate.perks.legendary"
msgstr "Pelestarian ilmu pengetahuan dan budaya umat manusia yang melegenda"

msgid "page.donate.expert.title"
msgstr "Akses Lanjutan"

msgid "page.donate.expert.contact_us"
msgstr "Hubungi Kami"

msgid "page.donate.small_team"
msgstr "Hal tersebut kemungkinan akan memakan waktu 1-2 minggu untuk kami tindak lanjuti. Dikarnakan sumberdaya kami yang masih terbatas. Terimakasih atas partisipasinya."

msgid "page.donate.expert.unlimited_access"
msgstr "Akses kecepatan tinggi dengan <strong>kapasitas tak terbatas</strong>"

msgid "page.donate.expert.direct_sftp"
msgstr "Server <strong>SFTP</strong> secara langsung"

msgid "page.donate.expert.enterprise_donation"
msgstr "Untuk kontributor koleksi baru (cth: scan dokumen baru, Dataset OCR, dll) akan mendapatkan hak akses premium."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Kami menerima donasi dari perseorangan maupun institusi secara terbuka. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Untuk sumbangan di atas $5000, harap hubungi kami langsung di %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Perhatikan bahwa meskipun keanggotaan di halaman ini adalah “per bulan”, mereka adalah donasi satu kali (tidak berulang). Lihat <a %(faq)s>Tanya Jawab terkait Donasi</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Jika Anda ingin memberikan donasi (dalam jumlah berapa pun) tanpa keanggotaan, silakan gunakan alamat Monero (XMR) ini: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Mohon pilih metode pembayaran yang sesuai."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(sementara tidak tersedia)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s kartu hadiah"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Kartu bank (menggunakan aplikasi)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Credit/debit card"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (reguler)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kartu / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit/debit/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "Paypal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Kartu bank"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kartu kredit/debit (cadangan)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Credit/debit card 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Dengan kripto, Anda dapat mendonasikan menggunakan BTC, ETH, XMR, dan SOL. Gunakan opsi ini jika Anda sudah terbiasa dengan mata uang kripto."

msgid "page.donate.payment.desc.crypto2"
msgstr "Dengan kripto, Anda dapat mendonasikan menggunakan BTC, ETH, XMR, dan lainnya."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Jika Anda menggunakan kripto untuk pertama kalinya, kami menyarankan menggunakan %(options)s untuk membeli dan menyumbangkan Bitcoin (mata uang kripto asli dan paling banyak digunakan)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Untuk mendonasikan menggunakan PayPal AS, kami akan menggunakan PayPal Kripto, yang memungkinkan kita tetap anonim. Kami menghargai waktu yang Anda luangkan untuk mempelajari cara mendonasikan menggunakan metode ini, karena itu sangat membantu kami."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Mendonasikan menggunakan PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Mendonasikan menggunakan Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Jika Anda memiliki Cash App, ini adalah cara yang paling mudah untuk memberikan donasi!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Harap dicatat bahwa untuk transaksi di bawah %(amount)s, Cash App dapat mengenakan biaya sebesar %(fee)s. Untuk jumlah di atas %(amount)s, tidak ada biaya yang dikenakan!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donasi menggunakan Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Jika Anda memiliki Revolut, ini adalah cara termudah untuk berdonasi!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Mendonasikan dengan kartu kredit atau kartu debit."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay dan Apple Pay mungkin juga bisa digunakan."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Harap dicatat bahwa untuk sumbangan kecil, biaya kartu kredit mungkin menghapuskan diskon %(discount)s%% kami, jadi kami menyarankan berlangganan yang lebih panjang."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Perlu dicatat bahwa untuk donasi kecil, biaya cukup tinggi, jadi kami merekomendasikan berlangganan yang lebih lama."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Dengan Binance, Anda membeli Bitcoin dengan kartu kredit/debit atau rekening bank, dan kemudian menyumbangkan Bitcoin tersebut kepada kami. Dengan cara ini, kami dapat tetap aman dan anonim saat menerima donasi Anda."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance tersedia di hampir setiap negara, dan mendukung sebagian besar bank dan kartu kredit/debit. Ini adalah rekomendasi utama kami saat ini. Kami menghargai waktu Anda untuk mempelajari cara berdonasi menggunakan metode ini, karena ini sangat membantu kami."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donasi menggunakan akun PayPal reguler Anda."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donasi menggunakan kartu kredit/debit, PayPal, atau Venmo. Anda dapat memilih di antara ini pada halaman berikutnya."

msgid "page.donate.payment.desc.amazon"
msgstr "Mendonasikan menggunakan Amazon gift card."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Harap dicatat bahwa kita perlu membulatkan jumlah ke angka yang diterima oleh pihak yang menjual (minimum %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>PENTING:</strong> Kami hanya mendukung Amazon.com, tidak mendukung situs Amazon lainnya. Misalnya, .de, .co.uk, .ca, TIDAK didukung."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>PENTING:</strong> Opsi ini untuk %(amazon)s. Jika Anda ingin menggunakan situs web Amazon lainnya, pilih di atas."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Metode ini menggunakan penyedia cryptocurrency sebagai konversi perantara. Ini bisa sedikit membingungkan, jadi harap gunakan metode ini hanya jika metode pembayaran lain tidak berfungsi. Ini juga tidak berfungsi di semua negara."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donasi menggunakan kartu kredit/debit, melalui aplikasi Alipay (sangat mudah untuk diatur)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Pasang aplikasi Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Pasang aplikasi Alipay dari <a %(a_app_store)s>Apple App Store</a> atau <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Daftar menggunakan nomor telepon Anda."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Tidak diperlukan detail pribadi lebih lanjut."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Tambahkan kartu bank"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Didukung: Visa, MasterCard, JCB, Diners Club, dan Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Lihat <a %(a_alipay)s>panduan ini</a> untuk informasi lebih lanjut."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Kami tidak dapat mendukung kartu kredit/debit secara langsung, karena bank tidak ingin bekerja sama dengan kami. ☹ Namun, ada beberapa cara untuk tetap menggunakan kartu kredit/debit, dengan menggunakan metode pembayaran lain:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Gift Card"

msgid "page.donate.ccexp.amazon_com"
msgstr "Kirim kepada kami Amazon.com gift menggunakan kartu kredit/debit kamu."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay mendukung kartu kredit/debit internasional. Lihat <a %(a_alipay)s>panduan ini</a> untuk informasi lebih lanjut."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) mendukung pembayaran international melalui kartu kredit/debit. Pada aplikasi WeChat, buka \"Tentang saya => Layanan => Wallet => Tambahkan kartu\". Jika anda tidak menemukanya, aktivkan memalui \"Tentang saya => Setting => Umum => Alat => Weixin Pay => Enable\"."

msgid "page.donate.ccexp.crypto"
msgstr "Anda dapat membeli koin kripto menggunakan kartu kredit/debit."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Layanan ekspres kripto"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Layanan ekspres nyaman, tetapi mengenakan biaya lebih tinggi."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Anda dapat menggunakan ini sebagai pengganti pertukaran kripto jika Anda ingin cepat membuat donasi lebih besar dan tidak keberatan dengan biaya $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Pastikan untuk mengirim jumlah kripto yang tepat yang ditampilkan di halaman donasi, bukan jumlah dalam $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Jika tidak, biaya akan dikurangkan dan kami tidak dapat memproses keanggotaan Anda secara otomatis."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s tergantung negara, tanpa verifikasi untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, tanpa verifikasi untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, tanpa verifikasi untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Jika ada informasi yang tidak terbaru, harap email kami untuk memberi tahu."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Untuk kartu kredit, kartu debit, Apple Pay, dan Google Pay, kami menggunakan “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Dalam sistem mereka, satu “kopi” setara dengan $5, jadi donasi Anda akan dibulatkan ke kelipatan 5 terdekat."

msgid "page.donate.duration.intro"
msgstr "Pilih berapa lama Anda ingin berlangganan."

msgid "page.donate.duration.1_mo"
msgstr "1 bulan"

msgid "page.donate.duration.3_mo"
msgstr "3 bulan"

msgid "page.donate.duration.6_mo"
msgstr "6 bulan"

msgid "page.donate.duration.12_mo"
msgstr "12 bulan"

msgid "page.donate.duration.24_mo"
msgstr "24 bulan"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 bulan"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 bulan"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>after <span %(span_discount)s></span> diskon</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Metode pembayaran ini memerlukan minimum %(amount)s. Silakan pilih durasi atau metode pembayaran yang berbeda."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donasi"

msgid "page.donate.payment.maximum_method"
msgstr "Metode pembayaran ini hanya memungkinkan hingga maksimum %(amount)s. Silakan pilih durasi atau metode pembayaran yang berbeda."

msgid "page.donate.login2"
msgstr "Untuk menjadi anggota, silakan <a %(a_login)s>Masuk atau Daftar</a>. Terima kasih atas dukungannya!"

msgid "page.donate.payment.crypto_select"
msgstr "Pilih koin kripto pilihan Anda:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(jumlah minimum terendah)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(gunakan saat mengirim Ethereum dari Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(peringatan: jumlah minimum tinggi)"

msgid "page.donate.submit.confirm"
msgstr "Klik tombol donasi untuk mengkonfirmasi donasi ini."

msgid "page.donate.submit.button"
msgstr "Donasi <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Anda masih dapat membatalkan donasi selama proses pembayaran."

msgid "page.donate.submit.success"
msgstr "✅ Mengalihkan ke halaman donasi…"

msgid "page.donate.submit.failure"
msgstr "❌ Terjadi kesalahan. Mohon muat ulang halaman dan coba lagi."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / bulan"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "selama 1 bulan"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "selama 3 bulan"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "selama 6 bulan"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "selama 12 bulan"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "selama 24 bulan"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "untuk 48 bulan"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "untuk 96 bulan"

msgid "page.donate.submit.button.label.1_mo"
msgstr "selama 1 bulan “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "selama 3 bulan “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "selama 6 bulan “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "selama 12 bulan “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "selama 24 bulan “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "untuk 48 bulan “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "untuk 96 bulan “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donasi"

msgid "page.donation.header.date"
msgstr "Tanggal: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / bulan selama %(duration)s bulan, termasuk %(discounts)s%% diskon)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / bulan selama %(duration)s bulan)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Pengenal: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Batal"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Apakah Anda yakin ingin membatalkan? Jangan membatalkan jika Anda sudah membayar."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Ya, tolong dibatalkan"

msgid "page.donation.header.cancel.success"
msgstr "✅ Donasi Anda telah dibatalkan."

msgid "page.donation.header.cancel.new_donation"
msgstr "Berikan donasi baru"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi."

msgid "page.donation.header.reorder"
msgstr "Urutkan kembali"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Anda sudah melakukan pembayaran. Jika Anda ingin meninjau petunjuk pembayaran, klik di sini:"

msgid "page.donation.old_instructions.show_button"
msgstr "Tampilkan petunjuk pembayaran sebelumnya"

msgid "page.donation.thank_you_donation"
msgstr "Terima kasih atas donasinya!"

msgid "page.donation.thank_you.secret_key"
msgstr "Bila anda belum memasukan kode rahasia anda untuk masuk:"

msgid "page.donation.thank_you.locked_out"
msgstr "Bila tidak akun anda kemungkinan akan terkunci!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Petunjuk pembayaran sekarang sudah kadaluwarsa. Jika Anda ingin membuat donasi lainnya, gunakan tombol \"Urutkan kembali\" di atas."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Catatan penting:</strong> Harga kripto bisa naik turun dengan sangat cepat dan tidak terduga, kadang-kadang bahkan bisa berubah sebanyak 20%% dalam waktu singkat. Meskipun begitu, ini masih lebih baik daripada biaya yang kita tanggung saat menggunakan banyak penyedia pembayaran, yang sering kali membebankan biaya sekitar 50-60%% ketika bekerja dengan lembaga amal seperti kami. <u>Jika Anda mengirimkan kepada kami bukti pembayaran dengan harga asli yang Anda bayarkan, kami akan tetap memberikan kredit keanggotaan yang Anda pilih</u> (asalkan bukti tersebut tidak lebih dari beberapa jam). Kami sangat menghargai bahwa Anda bersedia menghadapi hal-hal seperti ini demi mendukung kami! ❤️"

msgid "page.donation.expired"
msgstr "Donasi ini telah kedaluwarsa. Mohon dibatalkan dan buat yang baru."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instruksi kripto"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfer ke salah satu akun kripto kami"

msgid "page.donation.payment.crypto.text1"
msgstr "Sumbangkan jumlah total sebesar %(total)s ke salah satu dari alamat berikut:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span> Beli Bitcoin melalui Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Temukan halaman \"Kripto\" di aplikasi atau situs web PayPal Anda. Biasanya ini berada di bawah \"Finances\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Ikuti instruksi untuk membeli Bitcoin (BTC). Anda hanya perlu membeli jumlah yang ingin Anda sumbangkan, sebesar %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span> Transferkan Bitcoin ke alamat kami"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Buka halaman \"Bitcoin\" di aplikasi atau situs web PayPal Anda. Tekan tombol \"Transfer\" %(transfer_icon)s, lalu pilih \"Kirim\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Masukkan alamat Bitcoin (BTC) kami sebagai penerima, dan ikuti instruksi untuk mengirimkan sumbangan Anda sebesar %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instruksi kartu kredit / debit"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Donasi melalui halaman kartu kredit / debit kami"

msgid "page.donation.donate_on_this_page"
msgstr "Donasi %(amount)s di <a %(a_page)s>halaman ini</a>."

msgid "page.donation.stepbystep_below"
msgstr "Lihat panduan langkah demi langkah di bawah ini."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Menunggu konfirmasi (muat ulang halaman untuk memeriksa)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Menunggu transfer (muat ulang halaman untuk memeriksa)…"

msgid "page.donation.time_left_header"
msgstr "Waktu yang tersisa:"

msgid "page.donation.might_want_to_cancel"
msgstr "(Anda mungkin ingin membatalkan dan membuat donasi baru)"

msgid "page.donation.reset_timer"
msgstr "Untuk mengatur ulang penghitung waktunya, cukup buat donasi baru."

msgid "page.donation.refresh_status"
msgstr "Perbarui status"

msgid "page.donation.footer.issues_contact"
msgstr "Jika Anda mengalami masalah, silakan hubungi kami di %(email)s dan sertakan sebanyak mungkin informasi (seperti tangkapan layar)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Jika Anda sudah membayar:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Terkadang konfirmasi dapat memakan waktu hingga 24 jam, jadi pastikan untuk menyegarkan halaman ini (meskipun sudah kedaluwarsa)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Beli koin PYUSD di PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Ikuti instruksi untuk membeli koin PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Beli sedikit lebih banyak (kami sarankan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan tetap memegang sisa dana tersebut."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Buka halaman \"PYUSD\" di aplikasi atau situs web PayPal Anda. Tekan tombol \"Transfer\" %(icon)s, lalu pilih \"Kirim\"."

msgid "page.donation.transfer_amount_to"
msgstr "Transfer %(amount)s ke %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Beli Bitcoin (BTC) di Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Pergi ke halaman “Bitcoin” (BTC) di Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Beli sedikit lebih banyak (kami merekomendasikan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan menyimpan sisa yang ada."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfer Bitcoin ke alamat kami"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik tombol “Kirim bitcoin” untuk melakukan “penarikan”. Beralih dari dolar ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah ini dan klik “Kirim”. Lihat <a %(help_video)s>video ini</a> jika Anda mengalami kesulitan."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Untuk donasi kecil (di bawah $25), Anda mungkin perlu menggunakan Rush atau Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Beli Bitcoin (BTC) di Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Pergi ke halaman “Crypto” di Revolut untuk membeli Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Beli sedikit lebih banyak (kami merekomendasikan %(more)s lebih) dari jumlah yang Anda donasikan (%(amount)s), untuk menutupi biaya transaksi. Anda akan menyimpan sisa yang ada."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfer Bitcoin ke alamat kami"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klik tombol “Kirim bitcoin” untuk melakukan “penarikan”. Beralih dari euro ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah ini dan klik “Kirim”. Lihat <a %(help_video)s>video ini</a> jika Anda mengalami kesulitan."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Pastikan untuk menggunakan jumlah BTC di bawah ini, <em>BUKAN</em> euro atau dolar, jika tidak, kami tidak akan menerima jumlah yang benar dan tidak dapat mengonfirmasi keanggotaan Anda secara otomatis."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Untuk donasi kecil (di bawah $25) Anda mungkin perlu menggunakan Rush atau Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Gunakan salah satu layanan “kartu kredit ke Bitcoin” ekspres berikut, yang hanya memerlukan beberapa menit:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Isi detail berikut dalam formulir:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Jumlah BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Harap gunakan <span %(underline)s>jumlah yang tepat</span> ini. Total biaya Anda mungkin lebih tinggi karena biaya kartu kredit. Untuk jumlah kecil, ini mungkin lebih dari diskon kami, sayangnya."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Alamat BTC / Bitcoin (dompet eksternal):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instruksi"

msgid "page.donation.crypto_standard"
msgstr "Kami hanya menerima donasi koin kripto standar(cth: bitcoin) dengan waktu konfirmasi hingga satu jam, tergantung dari jenis koin yang anda donasikan."

msgid "page.donation.crypto_qr_code_title"
msgstr "Pindai kode QR untuk membayar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Pindai kode QR ini dengan aplikasi dompet crypto Anda untuk dengan cepat mengisi detail pembayaran"

msgid "page.donation.amazon.header"
msgstr "Amazon gift card"

msgid "page.donation.amazon.form_instructions"
msgstr "Silakan gunakan <a %(a_form)s>formulir resmi Amazon.com</a> untuk mengirimkan kami kartu hadiah sebesar %(amount)s ke alamat email di bawah ini."

msgid "page.donation.amazon.only_official"
msgstr "Kami tidak dapat menerima metode lain untuk kartu hadiah, <strong>hanya yang dikirimkan langsung melalui formulir resmi di Amazon.com</strong>. Kami tidak dapat mengembalikan kartu hadiah Anda jika Anda tidak menggunakan formulir ini."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Masukkan jumlah yang tepat: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Dimohon untuk TIDAK menuliskan kontak pribadi."

msgid "page.donation.amazon.form_to"
msgstr "Email penerima 'To' dalam formulir:"

msgid "page.donation.amazon.unique"
msgstr "Unik untuk akun Anda, jangan dibagikan."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Hanya gunakan sekali."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Menunggu kartu hadiah… (muat ulang halaman untuk memeriksa)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Setelah mengirimkan ‘gift card’ Anda, sistem otomatis kami akan mengonfirmasinya dalam beberapa menit. Jika ini tidak berhasil, coba kirimkan kembali ‘gift card’ Anda (<a %(a_instr)s>instruksi</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Jika masih tidak berhasil, silakan kirim email kepada kami dan Anna akan meninjaunya secara manual (ini mungkin memerlukan waktu beberapa hari), dan pastikan untuk menyebutkan jika Anda telah mencoba mengirim ulang sebelumnya."

msgid "page.donation.amazon.example"
msgstr "Contoh:"

msgid "page.donate.strange_account"
msgstr "Harap dicatat bahwa nama akun atau gambar profil mungkin terlihat aneh. Tidak perlu khawatir! Akun-akun ini dikelola oleh mitra donasi kami. Akun kami tidak diretas."

msgid "page.donation.payment.alipay.top_header"
msgstr "Petunjuk Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span> Donasi melalui Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donasikan jumlah total %(total)s menggunakan <a %(a_account)s>akun Alipay ini</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Jika halaman donasi terblokir, coba koneksi internet yang berbeda (misalnya VPN atau internet ponsel)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Sayangnya, halaman Alipay sering kali hanya dapat diakses dari <strong>Tiongkok daratan</strong>. Anda mungkin perlu menonaktifkan VPN sementara, atau menggunakan VPN ke Tiongkok daratan (atau Hong Kong juga kadang berfungsi)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Lakukan donasi (pindai kode QR atau tekan tombol)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Buka <a %(a_href)s>halaman donasi kode QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Pindai kode QR dengan aplikasi Alipay, atau tekan tombol untuk membuka aplikasi Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Harap bersabar; halaman mungkin memerlukan waktu untuk dimuat karena berada di Tiongkok."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instruksi WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donasi di WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donasikan jumlah total %(total)s menggunakan <a %(a_account)s>akun WeChat ini</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Petunjuk Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donasi melalui Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Donasikan jumlah total sebesar %(total)s menggunakan <a %(a_account)s>akun Pix ini"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Kirimkan email tanda terimanya kepada kami"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Kirim tanda terima atau tangkapan layar ke alamat verifikasi pribadi Anda. Jangan gunakan alamat email ini untuk donasi PayPal Anda."

msgid "page.donation.footer.text1"
msgstr "Kirimkan tanda terima atau tangkapan layar ke alamat verifikasi pribadi Anda:"

msgid "page.donation.footer.crypto_note"
msgstr "Jika nilai tukar kripto berubah-ubah selama transaksi, pastikan Anda menyertakan tanda terima yang menunjukkan nilai tukar awal. Kami benar-benar menghargai usaha Anda menggunakan kripto karena ini sangat membantu kami!"

msgid "page.donation.footer.text2"
msgstr "Setelah Anda mengirim email tanda terimanya, klik tombol ini agar Anna dapat meninjaunya secara manual (ini mungkin memerlukan waktu beberapa hari):"

msgid "page.donation.footer.button"
msgstr "Ya, saya telah mengirimkan tanda terima melalui email"

msgid "page.donation.footer.success"
msgstr "✅ Terima kasih atas donasinya! Anna akan mengaktifkan keanggotaan Anda secara manual dalam beberapa hari."

msgid "page.donation.footer.failure"
msgstr "❌ Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi."

msgid "page.donation.stepbystep"
msgstr "Panduan langkah demi langkah"

msgid "page.donation.crypto_dont_worry"
msgstr "Beberapa langkah menyebutkan dompet kripto, tetapi jangan khawatir, Anda tidak perlu belajar tentang kripto untuk hal ini."

msgid "page.donation.hoodpay.step1"
msgstr "1. Masukkan alamat email Anda."

msgid "page.donation.hoodpay.step2"
msgstr "2. Pilih metode pembayaran Anda."

msgid "page.donation.hoodpay.step3"
msgstr "3. Pilih metode pembayaran Anda lagi."

msgid "page.donation.hoodpay.step4"
msgstr "4. Pilih dompet “Self-hosted”."

msgid "page.donation.hoodpay.step5"
msgstr "5. Klik “Saya konfirmasi kepemilikan”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Anda seharusnya menerima tanda terima melalui email. Kirimkan tanda terimanya kepada kami, dan kami akan mengonfirmasi donasi Anda sesegera mungkin."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Harap tunggu setidaknya <span %(span_hours)s>24 jam</span> (dan segarkan halaman ini) sebelum menghubungi kami."

msgid "page.donate.mistake"
msgstr "Bila terjadi kesalahan selama proses pembayaran, kami tidak melayani pengembalian, akan tetapi akan kami usahakan perbaikan dengan jalan lainya."

msgid "page.my_donations.title"
msgstr "Donasi saya"

msgid "page.my_donations.not_shown"
msgstr "Rincian donasi tidak ditampilkan secara publik."

msgid "page.my_donations.no_donations"
msgstr "Belum ada donasi. <a %(a_donate)s>Buat donasi pertama saya.</a>"

msgid "page.my_donations.make_another"
msgstr "Buat donasi lainnya."

msgid "page.downloaded.title"
msgstr "Berkas yang sudah diunduh"

msgid "page.downloaded.fast_partner_star"
msgstr "Jalur unduhan cepat melalui server rekanan ditandai dengan %(icon)s."

msgid "page.downloaded.twice"
msgstr "Bila anda menjalankan unduhan dokumen melalui jalur cepat dan jalur lambat, hasil akan didapatkan sebanyak 2 kali."

msgid "page.downloaded.fast_download_time"
msgstr "Unduhan jalur cepat dalam 24 jam terahir menuju batas limit harian."

msgid "page.downloaded.times_utc"
msgstr "Semua waktu dalam format UTC."

msgid "page.downloaded.not_public"
msgstr "Berkas yang sudah diunduh tidak ditampilkan secara publik."

msgid "page.downloaded.no_files"
msgstr "Belum ada berkas yang diunduh."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "18 jam terakhir"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Sebelumnya"

msgid "page.account.logged_in.title"
msgstr "Akun"

msgid "page.account.logged_out.title"
msgstr "Masuk / Daftar"

msgid "page.account.logged_in.account_id"
msgstr "ID Akun: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Profil publik: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Kunci rahasia (jangan dibagikan!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "tampilkan"

msgid "page.account.logged_in.membership_has_some"
msgstr "Keanggotaan: <strong>%(tier_name)s</strong> hingga %(until_date)s <a %(a_extend)s>(perpanjang)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Keanggotaan: <strong>Tidak Ada</strong> <a %(a_become)s>(menjadi anggota)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Pengunduhan cepat yang digunakan (24 jam terakhir): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "unduhan yang mana?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grup Telegram eksklusif: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Bergabunglah bersama kami!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Lakukan peningkatan ke <a %(a_tier)s>tier lebih tinggi</a> untuk bergabung ke dalam grup kami."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Hubungi Anna di %(email)s jika Anda tertarik untuk meningkatkan keanggotaan Anda ke tingkat yang lebih tinggi."

msgid "page.contact.title"
msgstr "Alamat email"

msgid "page.account.logged_in.membership_multiple"
msgstr "Kamu dapat menggabungkan beberapa membersip ( unduhan jalur cepat untuk 24 jam akan dijalankan secara bersamaan)."

msgid "layout.index.header.nav.public_profile"
msgstr "Profil"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Unduh berkas"

msgid "layout.index.header.nav.my_donations"
msgstr "Donasiku"

msgid "page.account.logged_in.logout.button"
msgstr "Keluar"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Anda kini sudah keluar. Muat ulang halaman untuk masuk kembali."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Terjadi kesalahan. Mohon muat ulang halaman dan coba lagi."

msgid "page.account.logged_out.registered.text1"
msgstr "Registrasi berhasil! Kunci rahasia Anda adalah: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Simpan kunci ini dengan hati-hati. Jika Anda kehilangan kuncinya, Anda akan kehilangan akses ke akun Anda."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Simpan Halaman. </strong>Anda dapat menyimpan halaman ini untuk mengambil kembali kunci Anda.</li><li %(li_item)s><strong>Unduh. </strong>Klik <a %(a_download)s>tautan ini</a> untuk mengunduh kunci Anda.</li><li %(li_item)s><strong>Pengelola kata sandi. </strong>Gunakan Pengelola kata sandi (red:Password manager) untuk menyimpan kunci saat Anda memasukkannya di bawah ini.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Masukkan kunci rahasia Anda untuk masuk:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Kunci rahasia"

msgid "page.account.logged_out.key_form.button"
msgstr "Masuk"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Kunci rahasia yang Anda masukkan salah. Periksa kembali kunci Anda dan coba lagi, atau alternatifnya, jika Anda belum memiliki akun, Anda bisa mendaftar untuk membuat akun baru di bawah ini."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Harap jangan hilangkan kode kunci anda!"

msgid "page.account.logged_out.register.header"
msgstr "Belum memiliki akun?"

msgid "page.account.logged_out.register.button"
msgstr "Daftar akun baru"

msgid "page.login.lost_key"
msgstr "Bila anda kehilangan kode rahasia, mohon <a %(a_contact)s>hubungi kami</a> dan berikan keterangan informasi selengkap-lengkapnya."

msgid "page.login.lost_key_contact"
msgstr "Kamu mungkin memerlukan akun sementara untuk menghubungi kami."

msgid "page.account.logged_out.old_email.button"
msgstr "Apakah Anda memiliki akun berdasarkan alamat email sebelumnya? Masukkan <a %(a_open)s>email Anda di sini</a>."

msgid "page.list.title"
msgstr "Daftar"

msgid "page.list.header.edit.link"
msgstr "Sunting"

msgid "page.list.edit.button"
msgstr "Simpan"

msgid "page.list.edit.success"
msgstr "✅ Tersimpan. Silakan muat ulang halaman."

msgid "page.list.edit.failure"
msgstr "❌ Terjadi kesalahan. Silakan coba lagi."

msgid "page.list.by_and_date"
msgstr "Daftar oleh %(by)s, dibuat <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Daftar kosong."

msgid "page.list.new_item"
msgstr "Tambahkan atau hapus dari daftar ini dengan menemukan berkas dan membuka tab \"Daftar\"."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil tidak ditemukan."

msgid "page.profile.header.edit"
msgstr "Sunting"

msgid "page.profile.change_display_name.text"
msgstr "Ubah nama tampilan Anda. Pengidentifikasi Anda (bagian setelah \"#\" ) tidak dapat diubah."

msgid "page.profile.change_display_name.button"
msgstr "Simpan"

msgid "page.profile.change_display_name.success"
msgstr "✅ Tersimpan. Silakan muat ulang halaman."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Terjadi kesalahan. Silakan coba lagi."

msgid "page.profile.created_time"
msgstr "Profil dibuat <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Daftar"

msgid "page.profile.lists.no_lists"
msgstr "Belum ada daftar saat ini"

msgid "page.profile.lists.new_list"
msgstr "Buat daftar baru dengan menemukan berkas dan membuka tab \"Daftar\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reformasi hak cipta diperlukan demi keamanan nasional"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Ringkasan: LLM Tiongkok (termasuk DeepSeek) dilatih menggunakan arsip ilegal buku dan makalah saya — yang terbesar di dunia. Barat perlu merombak undang-undang hak cipta sebagai masalah keamanan nasional."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artikel pendamping oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Belum lama ini, \"perpustakaan bayangan\" hampir punah. Sci-Hub, arsip ilegal besar-besaran dari makalah akademis, berhenti menerima karya baru karena tuntutan hukum. \"Z-Library\", perpustakaan ilegal terbesar dari buku-buku, melihat para penciptanya yang diduga ditangkap atas tuduhan pelanggaran hak cipta. Mereka berhasil melarikan diri dari penangkapan, tetapi perpustakaan mereka tetap terancam."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Ketika Z-Library menghadapi penutupan, saya sudah mencadangkan seluruh perpustakaannya dan mencari platform untuk menampungnya. Itulah motivasi saya untuk memulai Arsip Anna: melanjutkan misi dari inisiatif sebelumnya. Sejak itu, kami telah berkembang menjadi perpustakaan bayangan terbesar di dunia, menampung lebih dari 140 juta teks berhak cipta dalam berbagai format — buku, makalah akademis, majalah, surat kabar, dan lainnya."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Tim saya dan saya adalah ideolog. Kami percaya bahwa melestarikan dan menampung file-file ini adalah tindakan yang benar secara moral. Perpustakaan di seluruh dunia mengalami pemotongan dana, dan kita juga tidak bisa mempercayakan warisan umat manusia kepada korporasi."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Kemudian datanglah AI. Hampir semua perusahaan besar yang membangun LLM menghubungi kami untuk melatih data mereka. Sebagian besar (tapi tidak semua!) perusahaan yang berbasis di AS mempertimbangkan kembali setelah menyadari sifat ilegal dari pekerjaan kami. Sebaliknya, perusahaan-perusahaan Tiongkok dengan antusias menerima koleksi kami, tampaknya tidak terganggu oleh legalitasnya. Ini patut dicatat mengingat peran Tiongkok sebagai penandatangan hampir semua perjanjian hak cipta internasional utama."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Kami telah memberikan akses berkecepatan tinggi kepada sekitar 30 perusahaan. Sebagian besar dari mereka adalah perusahaan LLM, dan beberapa adalah pialang data, yang akan menjual kembali koleksi kami. Sebagian besar adalah perusahaan Tiongkok, meskipun kami juga bekerja dengan perusahaan dari AS, Eropa, Rusia, Korea Selatan, dan Jepang. DeepSeek <a %(arxiv)s>mengakui</a> bahwa versi sebelumnya dilatih menggunakan sebagian dari koleksi kami, meskipun mereka enggan berbicara tentang model terbaru mereka (mungkin juga dilatih menggunakan data kami)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Jika Barat ingin tetap unggul dalam perlombaan LLM, dan pada akhirnya, AGI, mereka perlu mempertimbangkan kembali posisinya tentang hak cipta, dan segera. Apakah Anda setuju dengan kami atau tidak tentang kasus moral kami, ini sekarang menjadi masalah ekonomi, dan bahkan keamanan nasional. Semua blok kekuatan sedang membangun super-ilmuwan, super-peretas, dan super-militer buatan. Kebebasan informasi menjadi masalah kelangsungan hidup bagi negara-negara ini — bahkan masalah keamanan nasional."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Tim kami berasal dari seluruh dunia, dan kami tidak memiliki afiliasi tertentu. Namun, kami mendorong negara-negara dengan undang-undang hak cipta yang kuat untuk menggunakan ancaman eksistensial ini untuk mereformasi mereka. Jadi, apa yang harus dilakukan?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Rekomendasi pertama kami sederhana: perpendek masa berlaku hak cipta. Di AS, hak cipta diberikan selama 70 tahun setelah kematian penulis. Ini tidak masuk akal. Kita bisa menyamakannya dengan paten, yang diberikan selama 20 tahun setelah pengajuan. Ini seharusnya lebih dari cukup waktu bagi penulis buku, makalah, musik, seni, dan karya kreatif lainnya, untuk mendapatkan kompensasi penuh atas usaha mereka (termasuk proyek jangka panjang seperti adaptasi film)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Kemudian, setidaknya, pembuat kebijakan harus memasukkan pengecualian untuk pelestarian massal dan penyebaran teks. Jika kehilangan pendapatan dari pelanggan individu adalah kekhawatiran utama, distribusi pada tingkat pribadi dapat tetap dilarang. Sebagai gantinya, mereka yang mampu mengelola repositori besar — perusahaan yang melatih LLM, bersama dengan perpustakaan dan arsip lainnya — akan tercakup oleh pengecualian ini."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Beberapa negara sudah melakukan versi ini. TorrentFreak <a %(torrentfreak)s>melaporkan</a> bahwa Tiongkok dan Jepang telah memperkenalkan pengecualian AI dalam undang-undang hak cipta mereka. Tidak jelas bagi kami bagaimana ini berinteraksi dengan perjanjian internasional, tetapi ini tentu memberikan perlindungan bagi perusahaan domestik mereka, yang menjelaskan apa yang telah kami lihat."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Adapun Arsip Anna — kami akan melanjutkan pekerjaan bawah tanah kami yang berakar pada keyakinan moral. Namun keinginan terbesar kami adalah untuk muncul ke permukaan, dan memperkuat dampak kami secara legal. Mohon reformasi hak cipta."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Baca artikel pendamping oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Pemenang hadiah visualisasi ISBN senilai $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Ringkasan: Kami menerima beberapa kiriman luar biasa untuk hadiah visualisasi ISBN senilai $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Beberapa bulan yang lalu kami mengumumkan <a %(all_isbns)s>hadiah $10,000</a> untuk membuat visualisasi terbaik dari data kami yang menunjukkan ruang ISBN. Kami menekankan untuk menunjukkan file mana yang sudah/tidak kami arsipkan, dan kemudian dataset yang menggambarkan berapa banyak perpustakaan yang memiliki ISBN (ukuran kelangkaan)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Kami sangat terkesan dengan tanggapannya. Ada begitu banyak kreativitas. Terima kasih banyak kepada semua yang telah berpartisipasi: energi dan antusiasme Anda menular!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Pada akhirnya, kami ingin menjawab pertanyaan-pertanyaan berikut: <strong>buku apa saja yang ada di dunia, berapa banyak yang sudah kami arsipkan, dan buku mana yang harus kami fokuskan selanjutnya?</strong> Senang melihat begitu banyak orang peduli dengan pertanyaan-pertanyaan ini."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Kami memulai dengan visualisasi dasar kami sendiri. Dalam kurang dari 300kb, gambar ini secara ringkas mewakili \"daftar buku\" terbuka terbesar yang pernah disusun dalam sejarah umat manusia:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Semua ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Berkas di Arsip Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNO CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Kebocoran data CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSID DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indeks eBook EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Perpustakaan Negara Rusia"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Perpustakaan Kekaisaran Trantor"

#, fuzzy
msgid "common.back"
msgstr "Kembali"

#, fuzzy
msgid "common.forward"
msgstr "Maju"

#, fuzzy
msgid "common.last"
msgstr "Terakhir"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Silakan lihat <a %(all_isbns)s>posting blog asli</a> untuk informasi lebih lanjut."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Kami mengeluarkan tantangan untuk meningkatkan ini. Kami akan memberikan hadiah pertama sebesar $6,000, tempat kedua sebesar $3,000, dan tempat ketiga sebesar $1,000. Karena tanggapan yang luar biasa dan kiriman yang luar biasa, kami memutuskan untuk sedikit meningkatkan total hadiah, dan memberikan hadiah tempat ketiga kepada empat pemenang masing-masing sebesar $500. Para pemenang ada di bawah ini, tetapi pastikan untuk melihat semua kiriman <a %(annas_archive)s>di sini</a>, atau unduh <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Tempat pertama $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "<a %(phiresky_github)s>Kiriman</a> ini (<a %(annas_archive_note_2951)s>komentar Gitlab</a>) adalah semua yang kami inginkan, dan lebih! Kami sangat menyukai opsi visualisasi yang sangat fleksibel (bahkan mendukung shader kustom), tetapi dengan daftar preset yang komprehensif. Kami juga menyukai betapa cepat dan halusnya semuanya, implementasi yang sederhana (yang bahkan tidak memiliki backend), minimap yang cerdas, dan penjelasan yang luas dalam <a %(phiresky_github)s>posting blog</a> mereka. Pekerjaan yang luar biasa, dan pemenang yang sangat pantas!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Tempat kedua $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "<a %(annas_archive_note_2913)s>Kiriman</a> lain yang luar biasa. Tidak sefleksibel tempat pertama, tetapi kami sebenarnya lebih menyukai visualisasi tingkat makronya dibandingkan tempat pertama (kurva pengisi ruang, batas, pelabelan, penyorotan, panning, dan zooming). Sebuah <a %(annas_archive_note_2971)s>komentar</a> oleh Joe Davis beresonansi dengan kami:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Walaupun persegi dan persegi panjang sempurna secara matematis menyenangkan, mereka tidak memberikan superioritas lokalitas dalam konteks pemetaan. Saya percaya asimetri yang melekat dalam Hilbert atau Morton klasik ini bukanlah cacat tetapi fitur. Seperti garis besar berbentuk sepatu bot Italia yang terkenal membuatnya langsung dikenali di peta, \"keanehan\" unik dari kurva ini dapat berfungsi sebagai landmark kognitif. Keunikan ini dapat meningkatkan memori spasial dan membantu pengguna mengorientasikan diri, yang berpotensi memudahkan menemukan wilayah tertentu atau memperhatikan pola.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Dan masih banyak opsi untuk memvisualisasikan dan merender, serta antarmuka pengguna yang sangat halus dan intuitif. Tempat kedua yang solid!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tempat ketiga $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Dalam <a %(annas_archive_note_2940)s>kiriman</a> ini kami sangat menyukai berbagai jenis tampilan, khususnya tampilan perbandingan dan penerbit."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tempat ketiga $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Meskipun antarmuka pengguna tidak paling halus, <a %(annas_archive_note_2917)s>kiriman</a> ini mencentang banyak kotak. Kami sangat menyukai fitur perbandingannya."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tempat ketiga $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Seperti tempat pertama, <a %(annas_archive_note_2975)s>kiriman</a> ini mengesankan kami dengan fleksibilitasnya. Pada akhirnya inilah yang membuat alat visualisasi hebat: fleksibilitas maksimal untuk pengguna tingkat lanjut, sambil menjaga kesederhanaan untuk pengguna rata-rata."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tempat ketiga $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "<a %(annas_archive_note_2947)s>Kiriman</a> terakhir yang mendapatkan hadiah cukup dasar, tetapi memiliki beberapa fitur unik yang sangat kami sukai. Kami menyukai bagaimana mereka menunjukkan berapa banyak datasets yang mencakup ISBN tertentu sebagai ukuran popularitas/keandalan. Kami juga sangat menyukai kesederhanaan tetapi efektivitas menggunakan penggeser opasitas untuk perbandingan."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ide-ide yang patut diperhatikan"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Beberapa ide dan implementasi lainnya yang sangat kami sukai:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Pencakar langit untuk kelangkaan"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistik langsung"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotasi, dan juga statistik langsung"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Tampilan peta unik dan filter"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Skema warna default yang keren dan peta panas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Penggantian datasets yang mudah untuk perbandingan cepat."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Label yang cantik."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Bar skala dengan jumlah buku."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Banyak slider untuk membandingkan datasets, seolah-olah Anda seorang DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Kami bisa terus berlanjut untuk sementara waktu, tetapi mari berhenti di sini. Pastikan untuk melihat semua kiriman <a %(annas_archive)s>di sini</a>, atau unduh <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Begitu banyak kiriman, dan masing-masing membawa perspektif unik, baik dalam UI maupun implementasi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Kami setidaknya akan menggabungkan kiriman juara pertama ke dalam situs web utama kami, dan mungkin beberapa lainnya. Kami juga mulai memikirkan cara mengatur proses mengidentifikasi, mengonfirmasi, dan kemudian mengarsipkan buku-buku paling langka. Lebih banyak lagi yang akan datang di bidang ini."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Terima kasih kepada semua yang berpartisipasi. Sangat menakjubkan bahwa begitu banyak orang peduli."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Hati kami penuh dengan rasa syukur."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Memvisualisasikan Semua ISBN — hadiah $10,000 pada 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Gambar ini mewakili \"daftar buku\" terbesar yang sepenuhnya terbuka yang pernah disusun dalam sejarah umat manusia."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Gambar ini berukuran 1000×800 piksel. Setiap piksel mewakili 2.500 ISBN. Jika kami memiliki file untuk sebuah ISBN, kami membuat piksel tersebut lebih hijau. Jika kami tahu sebuah ISBN telah diterbitkan, tetapi kami tidak memiliki file yang cocok, kami membuatnya lebih merah."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Dalam kurang dari 300kb, gambar ini secara ringkas mewakili \"daftar buku\" terbesar yang sepenuhnya terbuka yang pernah disusun dalam sejarah umat manusia (beberapa ratus GB dikompresi penuh)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ini juga menunjukkan: masih banyak pekerjaan yang harus dilakukan dalam mencadangkan buku (kami hanya memiliki 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Latar Belakang"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Bagaimana Arsip Anna dapat mencapai misinya untuk mencadangkan semua pengetahuan umat manusia, tanpa mengetahui buku mana yang masih ada di luar sana? Kami memerlukan daftar TODO. Salah satu cara untuk memetakan ini adalah melalui nomor ISBN, yang sejak tahun 1970-an telah diberikan kepada setiap buku yang diterbitkan (di sebagian besar negara)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Tidak ada otoritas pusat yang mengetahui semua penugasan ISBN. Sebaliknya, ini adalah sistem terdistribusi, di mana negara-negara mendapatkan rentang nomor, yang kemudian memberikan rentang yang lebih kecil kepada penerbit besar, yang mungkin membagi lebih lanjut rentang tersebut kepada penerbit kecil. Akhirnya, nomor individu diberikan kepada buku."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Kami mulai memetakan ISBN <a %(blog)s>dua tahun lalu</a> dengan pengambilan data dari ISBNdb. Sejak itu, kami telah mengambil data dari banyak sumber metadata lainnya, seperti <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, dan lainnya. Daftar lengkap dapat ditemukan di halaman “Datasets” dan “Torrents” di Arsip Anna. Kami sekarang memiliki koleksi metadata buku (dan dengan demikian ISBN) yang sepenuhnya terbuka dan mudah diunduh terbesar di dunia."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Kami telah <a %(blog)s>menulis secara ekstensif</a> tentang mengapa kami peduli dengan pelestarian, dan mengapa kami saat ini berada dalam jendela kritis. Kami harus sekarang mengidentifikasi buku-buku langka, kurang diperhatikan, dan yang uniknya berisiko, dan melestarikannya. Memiliki metadata yang baik pada semua buku di dunia membantu dalam hal itu."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisasi"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Selain gambar ikhtisar, kita juga dapat melihat dataset individu yang telah kita peroleh. Gunakan dropdown dan tombol untuk beralih di antara mereka."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Ada banyak pola menarik yang dapat dilihat dalam gambar-gambar ini. Mengapa ada keteraturan garis dan blok, yang tampaknya terjadi pada skala yang berbeda? Apa area kosong itu? Mengapa dataset tertentu begitu terkumpul? Kami akan meninggalkan pertanyaan-pertanyaan ini sebagai latihan bagi pembaca."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Hadiah $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Ada banyak yang bisa dieksplorasi di sini, jadi kami mengumumkan hadiah untuk meningkatkan visualisasi di atas. Tidak seperti kebanyakan hadiah kami, yang satu ini memiliki batas waktu. Anda harus <a %(annas_archive)s>mengirimkan</a> kode sumber terbuka Anda sebelum 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Pengiriman terbaik akan mendapatkan $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Semua hadiah akan diberikan menggunakan Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Di bawah ini adalah kriteria minimal. Jika tidak ada pengiriman yang memenuhi kriteria, kami mungkin masih memberikan beberapa hadiah, tetapi itu akan menjadi kebijakan kami."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork repo ini, dan edit HTML posting blog ini (tidak ada backend lain selain backend Flask kami yang diizinkan)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Buat gambar di atas dapat di-zoom dengan mulus, sehingga Anda dapat memperbesar hingga ISBN individu. Mengklik ISBN harus membawa Anda ke halaman metadata atau pencarian di Arsip Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Anda harus tetap dapat beralih di antara semua dataset yang berbeda."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Rentang negara dan rentang penerbit harus disorot saat di-hover. Anda dapat menggunakan misalnya <a %(github_xlcnd_isbnlib)s>data4info.py di isbnlib</a> untuk info negara, dan pengambilan data “isbngrp” kami untuk penerbit (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Ini harus berfungsi dengan baik di desktop dan seluler."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Untuk poin bonus (ini hanya ide — biarkan kreativitas Anda mengalir bebas):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Pertimbangan kuat akan diberikan pada kegunaan dan seberapa baik tampilannya."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Tampilkan metadata aktual untuk ISBN individu saat memperbesar, seperti judul dan penulis."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Kurva pengisian ruang yang lebih baik. Misalnya, zig-zag, dari 0 ke 4 pada baris pertama dan kemudian kembali (secara terbalik) dari 5 ke 9 pada baris kedua — diterapkan secara rekursif."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Skema warna yang berbeda atau dapat disesuaikan."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Tampilan khusus untuk membandingkan datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Cara untuk memecahkan masalah, seperti metadata lain yang tidak sesuai (misalnya judul yang sangat berbeda)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Memberi anotasi pada gambar dengan komentar tentang ISBN atau rentang."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Heuristik apa pun untuk mengidentifikasi buku langka atau berisiko."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Ide kreatif apa pun yang bisa Anda pikirkan!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Anda BOLEH sepenuhnya menyimpang dari kriteria minimal, dan melakukan visualisasi yang benar-benar berbeda. Jika itu benar-benar spektakuler, maka itu memenuhi syarat untuk hadiah, tetapi atas kebijakan kami."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Buat pengajuan dengan memposting komentar ke <a %(annas_archive)s>masalah ini</a> dengan tautan ke repo yang Anda fork, permintaan penggabungan, atau perbedaan."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kode"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Kode untuk menghasilkan gambar-gambar ini, serta contoh lainnya, dapat ditemukan di <a %(annas_archive)s>direktori ini</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Kami membuat format data yang ringkas, dengan semua informasi ISBN yang diperlukan sekitar 75MB (terkompresi). Deskripsi format data dan kode untuk menghasilkannya dapat ditemukan <a %(annas_archive_l1244_1319)s>di sini</a>. Untuk hadiah, Anda tidak diharuskan menggunakan ini, tetapi ini mungkin format yang paling nyaman untuk memulai. Anda dapat mengubah metadata kami sesuka Anda (meskipun semua kode Anda harus open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Kami tidak sabar untuk melihat apa yang Anda buat. Semoga sukses!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Arsip Anna Kontainer (AAC): standarisasi rilis dari shadow library terbesar di dunia"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Arsip Anna telah menjadi shadow library terbesar di dunia, mengharuskan kami untuk menstandarisasi rilis kami."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Arsip Anna</a> telah menjadi shadow library terbesar di dunia, dan satu-satunya shadow library dengan skala sebesar ini yang sepenuhnya open-source dan open-data. Di bawah ini adalah tabel dari halaman Datasets kami (sedikit dimodifikasi):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Kami mencapai ini dengan tiga cara:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Mencerminkan shadow library open-data yang ada (seperti Sci-Hub dan Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Membantu shadow library yang ingin lebih terbuka, tetapi tidak memiliki waktu atau sumber daya untuk melakukannya (seperti koleksi komik Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Mengambil data dari perpustakaan yang tidak ingin berbagi secara massal (seperti Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Untuk (2) dan (3) kami sekarang mengelola koleksi torrent yang cukup besar sendiri (ratusan TB). Sejauh ini kami mendekati koleksi ini sebagai satu kali, artinya infrastruktur dan organisasi data yang dibuat khusus untuk setiap koleksi. Ini menambah beban kerja yang signifikan pada setiap rilis, dan membuatnya sangat sulit untuk melakukan rilis yang lebih bertahap."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Itulah mengapa kami memutuskan untuk menstandarisasi rilis kami. Ini adalah posting blog teknis di mana kami memperkenalkan standar kami: <strong>Kontainer Arsip Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Tujuan desain"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Kasus penggunaan utama kami adalah distribusi file dan metadata terkait dari berbagai koleksi yang ada. Pertimbangan terpenting kami adalah:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "File dan metadata yang heterogen, sedekat mungkin dengan format aslinya."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Pengidentifikasi yang heterogen di perpustakaan sumber, atau bahkan tidak adanya pengidentifikasi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Rilis terpisah dari metadata vs data file, atau rilis metadata saja (misalnya rilis ISBNdb kami)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribusi melalui torrent, meskipun dengan kemungkinan metode distribusi lain (misalnya IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Catatan yang tidak dapat diubah, karena kami harus mengasumsikan torrent kami akan hidup selamanya."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Rilis bertahap / rilis yang dapat ditambahkan."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Dapat dibaca dan ditulis oleh mesin, dengan mudah dan cepat, terutama untuk tumpukan kami (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspeksi manusia yang agak mudah, meskipun ini sekunder terhadap keterbacaan mesin."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Mudah untuk menanam koleksi kami dengan seedbox sewaan standar."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Data biner dapat disajikan langsung oleh server web seperti Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Beberapa tujuan non-tujuan:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Kami tidak peduli tentang file yang mudah dinavigasi secara manual di disk, atau dapat dicari tanpa pemrosesan awal."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Kami tidak peduli tentang kompatibilitas langsung dengan perangkat lunak perpustakaan yang ada."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Meskipun seharusnya mudah bagi siapa saja untuk menanam koleksi kami menggunakan torrent, kami tidak mengharapkan file tersebut dapat digunakan tanpa pengetahuan teknis dan komitmen yang signifikan."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Karena Arsip Anna bersifat open source, kami ingin menggunakan format kami secara langsung. Ketika kami memperbarui indeks pencarian kami, kami hanya mengakses jalur yang tersedia untuk umum, sehingga siapa pun yang menyalin perpustakaan kami dapat memulai dengan cepat."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standar"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Pada akhirnya, kami menetapkan standar yang relatif sederhana. Standar ini cukup longgar, tidak normatif, dan masih dalam pengembangan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Kontainer Arsip Anna) adalah satu item yang terdiri dari <strong>metadata</strong>, dan opsional <strong>data biner</strong>, keduanya tidak dapat diubah. Item ini memiliki pengenal unik global, yang disebut <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Koleksi.</strong> Setiap AAC termasuk dalam sebuah koleksi, yang menurut definisi adalah daftar AAC yang konsisten secara semantik. Artinya, jika Anda membuat perubahan signifikan pada format metadata, maka Anda harus membuat koleksi baru."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Koleksi “catatan” dan “file”.</strong> Secara konvensi, sering kali lebih mudah untuk merilis “catatan” dan “file” sebagai koleksi yang berbeda, sehingga dapat dirilis pada jadwal yang berbeda, misalnya berdasarkan tingkat pengambilan data. “Catatan” adalah koleksi yang hanya berisi metadata, yang memuat informasi seperti judul buku, penulis, ISBN, dll, sedangkan “file” adalah koleksi yang berisi file sebenarnya (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Format AACID adalah sebagai berikut: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Sebagai contoh, AACID yang sebenarnya kami rilis adalah <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: nama koleksi, yang dapat berisi huruf ASCII, angka, dan garis bawah (tetapi tidak ada garis bawah ganda)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: versi pendek dari ISO 8601, selalu dalam UTC, misalnya <code>20220723T194746Z</code>. Angka ini harus meningkat secara monoton untuk setiap rilis, meskipun semantik pastinya dapat berbeda per koleksi. Kami menyarankan menggunakan waktu pengambilan data atau pembuatan ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: pengenal khusus koleksi, jika berlaku, misalnya ID Z-Library. Dapat dihilangkan atau dipotong. Harus dihilangkan atau dipotong jika AACID akan melebihi 150 karakter."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID tetapi dikompresi ke ASCII, misalnya menggunakan base57. Saat ini kami menggunakan pustaka Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Rentang AACID.</strong> Karena AACID mengandung stempel waktu yang meningkat secara monoton, kita dapat menggunakannya untuk menunjukkan rentang dalam koleksi tertentu. Kami menggunakan format ini: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, di mana stempel waktu bersifat inklusif. Ini konsisten dengan notasi ISO 8601. Rentang bersifat kontinu, dan dapat tumpang tindih, tetapi dalam kasus tumpang tindih harus berisi catatan identik seperti yang sebelumnya dirilis dalam koleksi tersebut (karena AAC tidak dapat diubah). Catatan yang hilang tidak diperbolehkan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>File metadata.</strong> File metadata berisi metadata dari rentang AAC, untuk satu koleksi tertentu. File ini memiliki properti berikut:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Nama file harus berupa rentang AACID, diawali dengan <code style=\"color: red\">annas_archive_meta__</code> dan diikuti oleh <code>.jsonl.zstd</code>. Sebagai contoh, salah satu rilis kami disebut<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Seperti yang ditunjukkan oleh ekstensi file, jenis file adalah <a %(jsonlines)s>JSON Lines</a> yang dikompresi dengan <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Setiap objek JSON harus berisi bidang berikut di tingkat atas: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsional). Tidak ada bidang lain yang diperbolehkan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> adalah metadata sewenang-wenang, sesuai semantik koleksi. Harus konsisten secara semantik dalam koleksi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> bersifat opsional, dan merupakan nama folder data biner yang berisi data biner yang sesuai. Nama file data biner yang sesuai dalam folder tersebut adalah AACID catatan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Awalan <code style=\"color: red\">annas_archive_meta__</code> dapat disesuaikan dengan nama institusi Anda, misalnya <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Folder data biner.</strong> Sebuah folder dengan data biner dari rentang AAC, untuk satu koleksi tertentu. Folder ini memiliki properti berikut:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Nama direktori harus berupa rentang AACID, diawali dengan <code style=\"color: green\">annas_archive_data__</code>, dan tanpa akhiran. Sebagai contoh, salah satu rilis aktual kami memiliki direktori yang disebut<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Direktori harus berisi file data untuk semua AAC dalam rentang yang ditentukan. Setiap file data harus memiliki AACID sebagai nama file (tanpa ekstensi)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Disarankan untuk membuat folder-folder ini berukuran cukup terkelola, misalnya tidak lebih besar dari 100GB-1TB masing-masing, meskipun rekomendasi ini dapat berubah seiring waktu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> File metadata dan folder data biner dapat dibundel dalam torrent, dengan satu torrent per file metadata, atau satu torrent per folder data biner. Torrent harus memiliki nama file/direktori asli ditambah akhiran <code>.torrent</code> sebagai nama file mereka."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Contoh"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Mari kita lihat rilis Z-Library terbaru kami sebagai contoh. Ini terdiri dari dua koleksi: “<span style=\"background: #fffaa3\">zlib3_records</span>” dan “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Ini memungkinkan kami untuk secara terpisah mengikis dan merilis catatan metadata dari file buku yang sebenarnya. Oleh karena itu, kami merilis dua torrent dengan file metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Kami juga merilis sejumlah torrent dengan folder data biner, tetapi hanya untuk koleksi “<span style=\"background: #ffd6fe\">zlib3_files</span>”, total 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Dengan menjalankan <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kita dapat melihat apa yang ada di dalamnya:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Dalam hal ini, ini adalah metadata buku seperti yang dilaporkan oleh Z-Library. Pada tingkat atas, kami hanya memiliki “aacid” dan “metadata”, tetapi tidak ada “data_folder”, karena tidak ada data biner yang sesuai. AACID berisi “22430000” sebagai ID utama, yang dapat kita lihat diambil dari “zlibrary_id”. Kita dapat mengharapkan AAC lain dalam koleksi ini memiliki struktur yang sama."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Sekarang mari kita jalankan <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Ini adalah metadata AAC yang jauh lebih kecil, meskipun sebagian besar AAC ini terletak di tempat lain dalam file biner! Bagaimanapun, kali ini kita memiliki “data_folder”, jadi kita dapat mengharapkan data biner yang sesuai berada di <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” berisi “zlibrary_id”, jadi kita dapat dengan mudah mengaitkannya dengan AAC yang sesuai dalam koleksi “zlib_records”. Kita bisa mengaitkannya dengan berbagai cara, misalnya melalui AACID — standar tidak menetapkan itu."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Perhatikan bahwa tidak perlu juga untuk bidang “metadata” itu sendiri menjadi JSON. Itu bisa berupa string yang berisi XML atau format data lainnya. Anda bahkan dapat menyimpan informasi metadata dalam blob biner terkait, misalnya jika itu adalah banyak data."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Dengan standar ini, kita dapat membuat rilis lebih bertahap, dan lebih mudah menambahkan sumber data baru. Kami sudah memiliki beberapa rilis menarik dalam pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Kami juga berharap akan lebih mudah bagi perpustakaan bayangan lainnya untuk mencerminkan koleksi kami. Bagaimanapun, tujuan kami adalah untuk melestarikan pengetahuan dan budaya manusia selamanya, jadi semakin banyak redundansi semakin baik."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Pembaruan Anna: arsip sumber terbuka sepenuhnya, ElasticSearch, lebih dari 300GB sampul buku"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arsip Anna. Berikut adalah beberapa hal yang kami capai baru-baru ini."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Dengan Z-Library yang turun dan pendirinya (diduga) ditangkap, kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arsip Anna (kami tidak akan menautkannya di sini, tetapi Anda dapat mencarinya di Google). Berikut adalah beberapa hal yang kami capai baru-baru ini."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Arsip Anna sepenuhnya sumber terbuka"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Kami percaya bahwa informasi harus bebas, dan kode kami sendiri tidak terkecuali. Kami telah merilis semua kode kami di instance Gitlab yang kami host secara pribadi: <a %(annas_archive)s>Perangkat Lunak Anna</a>. Kami juga menggunakan pelacak masalah untuk mengatur pekerjaan kami. Jika Anda ingin terlibat dengan pengembangan kami, ini adalah tempat yang bagus untuk memulai."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Untuk memberi Anda gambaran tentang hal-hal yang sedang kami kerjakan, lihat pekerjaan terbaru kami pada peningkatan kinerja sisi klien. Karena kami belum menerapkan paginasi, kami sering mengembalikan halaman pencarian yang sangat panjang, dengan 100-200 hasil. Kami tidak ingin memotong hasil pencarian terlalu cepat, tetapi ini berarti akan memperlambat beberapa perangkat. Untuk ini, kami menerapkan trik kecil: kami membungkus sebagian besar hasil pencarian dalam komentar HTML (<code><!-- --></code>), dan kemudian menulis sedikit Javascript yang akan mendeteksi kapan hasil harus menjadi terlihat, pada saat itu kami akan membuka komentar:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisasi\" diimplementasikan dalam 23 baris, tidak perlu pustaka mewah! Ini adalah jenis kode pragmatis cepat yang Anda dapatkan ketika Anda memiliki waktu terbatas, dan masalah nyata yang perlu diselesaikan. Dilaporkan bahwa pencarian kami sekarang berfungsi dengan baik pada perangkat lambat!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Upaya besar lainnya adalah mengotomatisasi pembangunan basis data. Ketika kami diluncurkan, kami hanya sembarangan mengumpulkan berbagai sumber. Sekarang kami ingin menjaga mereka tetap diperbarui, jadi kami menulis serangkaian skrip untuk mengunduh metadata baru dari dua cabang Library Genesis, dan mengintegrasikannya. Tujuannya adalah tidak hanya membuat ini berguna untuk arsip kami, tetapi juga memudahkan siapa saja yang ingin bermain-main dengan metadata shadow library. Tujuannya adalah sebuah notebook Jupyter yang memiliki berbagai metadata menarik yang tersedia, sehingga kami dapat melakukan lebih banyak penelitian seperti mencari tahu <a %(blog)s>persentase ISBN yang diawetkan selamanya</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Akhirnya, kami memperbarui sistem donasi kami. Anda sekarang dapat menggunakan kartu kredit untuk langsung menyetor uang ke dompet kripto kami, tanpa benar-benar perlu mengetahui apa pun tentang mata uang kripto. Kami akan terus memantau seberapa baik ini bekerja dalam praktiknya, tetapi ini adalah hal besar."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Beralih ke ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Salah satu <a %(annas_archive)s>tiket</a> kami adalah kumpulan masalah dengan sistem pencarian kami. Kami menggunakan pencarian teks lengkap MySQL, karena kami memiliki semua data kami di MySQL. Namun, ada batasannya:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Beberapa kueri memakan waktu sangat lama, hingga mereka akan menghabiskan semua koneksi yang terbuka."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Secara default, MySQL memiliki panjang kata minimum, atau indeks Anda bisa menjadi sangat besar. Orang melaporkan tidak dapat mencari \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Pencarian hanya agak cepat ketika sepenuhnya dimuat dalam memori, yang mengharuskan kami mendapatkan mesin yang lebih mahal untuk menjalankannya, ditambah beberapa perintah untuk memuat indeks saat startup."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Kami tidak akan dapat memperluasnya dengan mudah untuk membangun fitur baru, seperti <a %(wikipedia_cjk_characters)s>tokenisasi yang lebih baik untuk bahasa tanpa spasi</a>, penyaringan/pemfaktoran, pengurutan, saran \"maksud Anda\", pelengkapan otomatis, dan sebagainya."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Setelah berbicara dengan banyak ahli, kami memutuskan untuk menggunakan ElasticSearch. Ini belum sempurna (saran \"maksud Anda\" dan fitur pelengkapan otomatis default mereka kurang bagus), tetapi secara keseluruhan ini jauh lebih baik daripada MySQL untuk pencarian. Kami masih tidak <a %(youtube)s>terlalu tertarik</a> menggunakannya untuk data yang sangat penting (meskipun mereka telah membuat banyak <a %(elastic_co)s>kemajuan</a>), tetapi secara keseluruhan kami cukup senang dengan peralihan ini."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Untuk saat ini, kami telah mengimplementasikan pencarian yang jauh lebih cepat, dukungan bahasa yang lebih baik, pengurutan relevansi yang lebih baik, opsi pengurutan yang berbeda, dan penyaringan berdasarkan bahasa/jenis buku/jenis file. Jika Anda penasaran bagaimana ini bekerja, <a %(annas_archive_l140)s>silakan</a> <a %(annas_archive_l1115)s>lihat</a> <a %(annas_archive_l1635)s>di sini</a>. Ini cukup mudah diakses, meskipun bisa menggunakan beberapa komentar lagi…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ sampul buku dirilis"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Akhirnya, kami senang mengumumkan rilis kecil. Dalam kolaborasi dengan orang-orang yang mengoperasikan cabang Libgen.rs, kami membagikan semua sampul buku mereka melalui torrent dan IPFS. Ini akan mendistribusikan beban melihat sampul di antara lebih banyak mesin, dan akan melestarikannya dengan lebih baik. Dalam banyak (tetapi tidak semua) kasus, sampul buku disertakan dalam file itu sendiri, jadi ini semacam \"data turunan\". Namun, memilikinya di IPFS masih sangat berguna untuk operasi harian baik Arsip Anna maupun berbagai cabang Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Seperti biasa, Anda dapat menemukan rilis ini di Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>). Kami tidak akan menautkannya di sini, tetapi Anda dapat dengan mudah menemukannya."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Semoga kami bisa sedikit mengendurkan tempo kami, sekarang kami memiliki alternatif yang layak untuk Z-Library. Beban kerja ini tidak terlalu berkelanjutan. Jika Anda tertarik membantu dalam pemrograman, operasi server, atau pekerjaan pelestarian, jangan ragu untuk menghubungi kami. Masih banyak <a %(annas_archive)s>pekerjaan yang harus dilakukan</a>. Terima kasih atas minat dan dukungan Anda."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Arsip Anna telah mencadangkan perpustakaan shadow komik terbesar di dunia (95TB) — Anda dapat membantu menyebarkannya"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Perpustakaan shadow buku komik terbesar di dunia memiliki satu titik kegagalan.. hingga hari ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskusikan di Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Perpustakaan shadow buku komik terbesar kemungkinan adalah dari cabang Library Genesis tertentu: Libgen.li. Satu administrator yang menjalankan situs itu berhasil mengumpulkan koleksi komik gila lebih dari 2 juta file, dengan total lebih dari 95TB. Namun, tidak seperti koleksi Library Genesis lainnya, yang satu ini tidak tersedia secara massal melalui torrent. Anda hanya bisa mengakses komik ini secara individual melalui server pribadi yang lambat — satu titik kegagalan. Hingga hari ini!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Dalam postingan ini kami akan memberi tahu Anda lebih banyak tentang koleksi ini, dan tentang penggalangan dana kami untuk mendukung lebih banyak pekerjaan ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon mencoba menghilangkan dirinya di dunia perpustakaan yang biasa saja…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fork Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Pertama, sedikit latar belakang. Anda mungkin mengenal Library Genesis karena koleksi bukunya yang epik. Lebih sedikit orang yang tahu bahwa relawan Library Genesis telah menciptakan proyek lain, seperti koleksi majalah dan dokumen standar yang cukup besar, cadangan penuh Sci-Hub (bekerja sama dengan pendiri Sci-Hub, Alexandra Elbakyan), dan memang, koleksi komik yang sangat besar."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Pada suatu titik, operator cermin Library Genesis yang berbeda berpisah, yang menyebabkan situasi saat ini dengan sejumlah \"fork\" yang berbeda, semuanya masih menggunakan nama Library Genesis. Fork Libgen.li secara unik memiliki koleksi komik ini, serta koleksi majalah yang cukup besar (yang juga sedang kami kerjakan)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Kolaborasi"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Mengingat ukurannya, koleksi ini sudah lama ada dalam daftar keinginan kami, jadi setelah keberhasilan kami dengan mencadangkan Z-Library, kami mengarahkan pandangan kami pada koleksi ini. Pada awalnya kami mengikisnya secara langsung, yang merupakan tantangan besar, karena server mereka tidak dalam kondisi terbaik. Kami mendapatkan sekitar 15TB dengan cara ini, tetapi prosesnya berjalan lambat."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Untungnya, kami berhasil menghubungi operator perpustakaan, yang setuju untuk mengirimkan semua data kepada kami secara langsung, yang jauh lebih cepat. Namun, masih memakan waktu lebih dari setengah tahun untuk mentransfer dan memproses semua data, dan kami hampir kehilangan semuanya karena kerusakan disk, yang berarti harus memulai dari awal lagi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Pengalaman ini membuat kami percaya bahwa penting untuk menyebarkan data ini secepat mungkin, sehingga dapat dicerminkan secara luas. Kami hanya satu atau dua insiden yang tidak beruntung dari kehilangan koleksi ini selamanya!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Koleksi"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Bergerak cepat memang berarti bahwa koleksi ini sedikit tidak terorganisir… Mari kita lihat. Bayangkan kita memiliki sistem file (yang sebenarnya kita bagi-bagi dalam torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Direktori pertama, <code>/repository</code>, adalah bagian yang lebih terstruktur dari ini. Direktori ini berisi yang disebut \"thousand dirs\": direktori masing-masing dengan ribuan file, yang diberi nomor secara bertahap dalam database. Direktori <code>0</code> berisi file dengan comic_id 0–999, dan seterusnya."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Ini adalah skema yang sama yang telah digunakan Library Genesis untuk koleksi fiksi dan non-fiksinya. Idenya adalah bahwa setiap \"thousand dir\" secara otomatis diubah menjadi torrent segera setelah terisi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Namun, operator Libgen.li tidak pernah membuat torrent untuk koleksi ini, sehingga thousand dirs mungkin menjadi tidak nyaman, dan memberi jalan kepada \"unsorted dirs\". Ini adalah <code>/comics0</code> hingga <code>/comics4</code>. Mereka semua memiliki struktur direktori unik, yang mungkin masuk akal untuk mengumpulkan file, tetapi tidak terlalu masuk akal bagi kami sekarang. Untungnya, metadata masih merujuk langsung ke semua file ini, jadi organisasi penyimpanan mereka di disk sebenarnya tidak masalah!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata tersedia dalam bentuk database MySQL. Ini dapat diunduh langsung dari situs web Libgen.li, tetapi kami juga akan menyediakannya dalam torrent, bersama dengan tabel kami sendiri dengan semua hash MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analisis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Ketika Anda mendapatkan 95TB yang dibuang ke dalam kluster penyimpanan Anda, Anda mencoba memahami apa yang ada di dalamnya… Kami melakukan beberapa analisis untuk melihat apakah kami bisa mengurangi ukurannya sedikit, seperti dengan menghapus duplikat. Berikut adalah beberapa temuan kami:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplikat semantik (pemindaian berbeda dari buku yang sama) secara teoritis dapat disaring, tetapi itu rumit. Ketika secara manual melihat melalui komik, kami menemukan terlalu banyak positif palsu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Ada beberapa duplikat murni berdasarkan MD5, yang relatif boros, tetapi menyaringnya hanya akan memberi kami penghematan sekitar 1% in. Pada skala ini itu masih sekitar 1TB, tetapi juga, pada skala ini 1TB tidak terlalu penting. Kami lebih suka tidak mengambil risiko secara tidak sengaja menghancurkan data dalam proses ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Kami menemukan banyak data non-buku, seperti film berdasarkan buku komik. Itu juga tampak boros, karena ini sudah tersedia secara luas melalui cara lain. Namun, kami menyadari bahwa kami tidak bisa begitu saja menyaring file film, karena ada juga <em>buku komik interaktif</em> yang dirilis di komputer, yang direkam dan disimpan sebagai film oleh seseorang."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Pada akhirnya, apa pun yang bisa kami hapus dari koleksi hanya akan menghemat beberapa persen. Kemudian kami ingat bahwa kami adalah pengumpul data, dan orang-orang yang akan mencerminkan ini juga pengumpul data, jadi, \"APA MAKSUDMU, HAPUS?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Oleh karena itu, kami menyajikan kepada Anda, koleksi lengkap yang tidak dimodifikasi. Ini adalah banyak data, tetapi kami berharap cukup banyak orang yang peduli untuk tetap menyebarkannya."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Penggalangan Dana"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Kami merilis data ini dalam beberapa bagian besar. Torrent pertama adalah <code>/comics0</code>, yang kami masukkan ke dalam satu file .tar besar berukuran 12TB. Itu lebih baik untuk hard drive dan perangkat lunak torrent Anda daripada banyak file kecil."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Sebagai bagian dari rilis ini, kami mengadakan penggalangan dana. Kami berusaha mengumpulkan $20,000 untuk menutupi biaya operasional dan kontrak untuk koleksi ini, serta memungkinkan proyek yang sedang berjalan dan masa depan. Kami memiliki beberapa proyek <em>besar</em> yang sedang dikerjakan."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Siapa yang saya dukung dengan donasi saya?</em> Singkatnya: kami mendukung semua pengetahuan dan budaya manusia, dan membuatnya mudah diakses. Semua kode dan data kami bersifat open source, kami adalah proyek yang sepenuhnya dijalankan oleh sukarelawan, dan kami telah menyimpan buku senilai 125TB sejauh ini (selain torrent Libgen dan Scihub yang sudah ada). Pada akhirnya, kami membangun roda gila yang memungkinkan dan mendorong orang untuk menemukan, memindai, dan mencadangkan semua buku di dunia. Kami akan menulis tentang rencana utama kami dalam postingan mendatang. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Jika Anda berdonasi untuk keanggotaan “Amazing Archivist” selama 12 bulan ($780), Anda bisa <strong>“mengadopsi torrent”</strong>, yang berarti kami akan menempatkan nama pengguna atau pesan Anda dalam nama file salah satu torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Anda dapat berdonasi dengan mengunjungi <a %(wikipedia_annas_archive)s>Arsip Anna</a> dan mengklik tombol “Donasi”. Kami juga mencari lebih banyak sukarelawan: insinyur perangkat lunak, peneliti keamanan, ahli pedagang anonim, dan penerjemah. Anda juga dapat mendukung kami dengan menyediakan layanan hosting. Dan tentu saja, silakan sebar torrent kami!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Terima kasih kepada semua orang yang telah dengan murah hati mendukung kami! Anda benar-benar membuat perbedaan."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Berikut adalah torrent yang telah dirilis sejauh ini (kami masih memproses sisanya):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Semua torrent dapat ditemukan di <a %(wikipedia_annas_archive)s>Arsip Anna</a> di bawah “Datasets” (kami tidak menautkan langsung ke sana, jadi tautan ke blog ini tidak dihapus dari Reddit, Twitter, dll). Dari sana, ikuti tautan ke situs web Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Apa selanjutnya?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Sekumpulan torrent bagus untuk pelestarian jangka panjang, tetapi tidak begitu banyak untuk akses sehari-hari. Kami akan bekerja sama dengan mitra hosting untuk mendapatkan semua data ini di web (karena Arsip Anna tidak meng-host apa pun secara langsung). Tentu saja Anda akan dapat menemukan tautan unduhan ini di Arsip Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Kami juga mengundang semua orang untuk melakukan sesuatu dengan data ini! Bantu kami menganalisisnya dengan lebih baik, menghapus duplikatnya, menempatkannya di IPFS, mengolahnya kembali, melatih model AI Anda dengannya, dan sebagainya. Semuanya milik Anda, dan kami tidak sabar untuk melihat apa yang Anda lakukan dengannya."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Akhirnya, seperti yang dikatakan sebelumnya, kami masih memiliki beberapa rilis besar yang akan datang (jika <em>seseorang</em> bisa <em>secara tidak sengaja</em> mengirimkan dump dari database <em>tertentu</em> ACS4, Anda tahu di mana menemukan kami...), serta membangun roda gila untuk mencadangkan semua buku di dunia."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Jadi tetaplah disini, kami baru saja memulai."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x buku baru ditambahkan ke Cermin Perpustakaan Bajak Laut (+24TB, 3,8 juta buku)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Dalam rilis asli Cermin Perpustakaan Bajak Laut (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>), kami membuat cermin dari Z-Library, koleksi buku ilegal yang besar. Sebagai pengingat, inilah yang kami tulis dalam postingan blog asli itu:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library adalah perpustakaan yang populer (dan ilegal). Mereka telah mengambil koleksi Library Genesis dan membuatnya mudah dicari. Selain itu, mereka menjadi sangat efektif dalam meminta kontribusi buku baru, dengan memberi insentif kepada pengguna yang berkontribusi dengan berbagai keuntungan. Saat ini mereka tidak mengembalikan buku-buku baru ini ke Library Genesis. Dan tidak seperti Library Genesis, mereka tidak membuat koleksi mereka mudah dicerminkan, yang mencegah pelestarian yang luas. Ini penting untuk model bisnis mereka, karena mereka mengenakan biaya untuk mengakses koleksi mereka secara massal (lebih dari 10 buku per hari)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Kami tidak membuat penilaian moral tentang memungut biaya untuk akses massal ke koleksi buku ilegal. Tidak diragukan lagi bahwa Z-Library telah berhasil memperluas akses ke pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bagian kami: memastikan pelestarian jangka panjang dari koleksi pribadi ini."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Koleksi tersebut berasal dari pertengahan 2021. Sementara itu, Z-Library telah berkembang dengan kecepatan yang mencengangkan: mereka telah menambahkan sekitar 3,8 juta buku baru. Memang ada beberapa duplikat di sana, tetapi sebagian besar tampaknya adalah buku baru yang sah, atau pemindaian berkualitas lebih tinggi dari buku yang sebelumnya diserahkan. Ini sebagian besar karena peningkatan jumlah moderator sukarelawan di Z-Library, dan sistem unggahan massal mereka dengan deduplikasi. Kami ingin mengucapkan selamat kepada mereka atas pencapaian ini."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Kami dengan senang hati mengumumkan bahwa kami telah mendapatkan semua buku yang ditambahkan ke Z-Library antara mirror terakhir kami dan Agustus 2022. Kami juga telah kembali dan mengumpulkan beberapa buku yang kami lewatkan pada putaran pertama. Secara keseluruhan, koleksi baru ini sekitar 24TB, yang jauh lebih besar dari yang terakhir (7TB). Mirror kami sekarang berjumlah 31TB secara total. Sekali lagi, kami melakukan deduplikasi terhadap Library Genesis, karena sudah ada torrent yang tersedia untuk koleksi tersebut."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Silakan kunjungi Pirate Library Mirror untuk melihat koleksi baru (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>). Ada lebih banyak informasi di sana tentang bagaimana file-file tersebut disusun, dan apa yang telah berubah sejak terakhir kali. Kami tidak akan menautkannya dari sini, karena ini hanya situs blog yang tidak menyimpan materi ilegal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Tentu saja, seeding juga merupakan cara yang bagus untuk membantu kami. Terima kasih kepada semua orang yang telah melakukan seeding pada set torrent kami sebelumnya. Kami berterima kasih atas tanggapan positifnya, dan senang bahwa ada begitu banyak orang yang peduli dengan pelestarian pengetahuan dan budaya dengan cara yang tidak biasa ini."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Cara menjadi arsiparis bajak laut"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Tantangan pertama mungkin mengejutkan. Ini bukan masalah teknis, atau masalah hukum. Ini adalah masalah psikologis."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Sebelum kita mulai, dua pembaruan tentang Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Kami mendapatkan beberapa donasi yang sangat dermawan. Yang pertama adalah $10k dari individu anonim yang juga telah mendukung \"bookwarrior\", pendiri asli Library Genesis. Terima kasih khusus kepada bookwarrior karena memfasilitasi donasi ini. Yang kedua adalah $10k lagi dari donor anonim, yang menghubungi kami setelah rilis terakhir kami, dan terinspirasi untuk membantu. Kami juga menerima sejumlah donasi yang lebih kecil. Terima kasih banyak atas semua dukungan dermawan Anda. Kami memiliki beberapa proyek baru yang menarik dalam perencanaan yang akan didukung oleh ini, jadi tetaplah terhubung."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Kami mengalami beberapa kesulitan teknis dengan ukuran rilis kedua kami, tetapi torrent kami sekarang sudah aktif dan melakukan seeding. Kami juga mendapatkan tawaran dermawan dari individu anonim untuk melakukan seeding koleksi kami di server berkecepatan sangat tinggi mereka, jadi kami melakukan unggahan khusus ke mesin mereka, setelah itu semua orang yang mengunduh koleksi tersebut harus melihat peningkatan besar dalam kecepatan."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Buku lengkap dapat ditulis tentang <em>mengapa</em> pelestarian digital secara umum, dan arsip bajak laut pada khususnya, tetapi mari kita berikan pengantar singkat bagi mereka yang tidak terlalu akrab. Dunia sedang memproduksi lebih banyak pengetahuan dan budaya daripada sebelumnya, tetapi juga lebih banyak yang hilang daripada sebelumnya. Umat manusia sebagian besar mempercayakan warisan ini kepada perusahaan seperti penerbit akademik, layanan streaming, dan perusahaan media sosial, dan mereka sering kali tidak terbukti menjadi pengelola yang baik. Lihatlah dokumenter Digital Amnesia, atau benar-benar pembicaraan apa pun oleh Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ada beberapa institusi yang melakukan pekerjaan yang baik dalam mengarsipkan sebanyak yang mereka bisa, tetapi mereka terikat oleh hukum. Sebagai bajak laut, kami berada dalam posisi unik untuk mengarsipkan koleksi yang tidak dapat mereka sentuh, karena penegakan hak cipta atau pembatasan lainnya. Kami juga dapat mencerminkan koleksi berkali-kali, di seluruh dunia, sehingga meningkatkan peluang pelestarian yang tepat."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Untuk saat ini, kami tidak akan membahas diskusi tentang pro dan kontra dari kekayaan intelektual, moralitas melanggar hukum, renungan tentang sensor, atau masalah akses ke pengetahuan dan budaya. Dengan semua itu disingkirkan, mari kita selami <em>bagaimana</em>. Kami akan berbagi bagaimana tim kami menjadi arsiparis bajak laut, dan pelajaran yang kami pelajari sepanjang jalan. Ada banyak tantangan ketika Anda memulai perjalanan ini, dan semoga kami dapat membantu Anda melalui beberapa di antaranya."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Komunitas"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Tantangan pertama mungkin mengejutkan. Ini bukan masalah teknis, atau masalah hukum. Ini adalah masalah psikologis: melakukan pekerjaan ini dalam bayang-bayang bisa sangat sepi. Tergantung pada apa yang Anda rencanakan untuk dilakukan, dan model ancaman Anda, Anda mungkin harus sangat berhati-hati. Di satu ujung spektrum, kami memiliki orang-orang seperti Alexandra Elbakyan*, pendiri Sci-Hub, yang sangat terbuka tentang kegiatannya. Namun dia berisiko tinggi ditangkap jika dia mengunjungi negara barat saat ini, dan bisa menghadapi hukuman penjara puluhan tahun. Apakah itu risiko yang bersedia Anda ambil? Kami berada di ujung spektrum yang lain; sangat berhati-hati untuk tidak meninggalkan jejak apa pun, dan memiliki keamanan operasional yang kuat."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Seperti yang disebutkan di HN oleh \"ynno\", Alexandra awalnya tidak ingin dikenal: \"Servernya diatur untuk mengeluarkan pesan kesalahan terperinci dari PHP, termasuk jalur lengkap dari file sumber yang bermasalah, yang berada di bawah direktori /home/<USER>" Jadi, gunakan nama pengguna acak di komputer yang Anda gunakan untuk hal-hal ini, jika Anda salah mengonfigurasi sesuatu."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Namun, kerahasiaan itu datang dengan biaya psikologis. Kebanyakan orang senang diakui atas pekerjaan yang mereka lakukan, namun Anda tidak dapat mengambil kredit apa pun untuk ini dalam kehidupan nyata. Bahkan hal-hal sederhana bisa menjadi tantangan, seperti teman yang bertanya apa yang telah Anda lakukan (pada titik tertentu \"bermain-main dengan NAS / homelab saya\" menjadi basi)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Inilah mengapa sangat penting untuk menemukan beberapa komunitas. Anda dapat mengorbankan sedikit keamanan operasional dengan mempercayakan kepada beberapa teman dekat, yang Anda tahu dapat Anda percayai sepenuhnya. Bahkan kemudian berhati-hatilah untuk tidak menuliskan apa pun, jika mereka harus menyerahkan email mereka kepada pihak berwenang, atau jika perangkat mereka dikompromikan dengan cara lain."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Lebih baik lagi adalah menemukan beberapa sesama bajak laut. Jika teman dekat Anda tertarik untuk bergabung dengan Anda, bagus! Jika tidak, Anda mungkin dapat menemukan orang lain secara online. Sayangnya ini masih merupakan komunitas yang kecil. Sejauh ini kami hanya menemukan segelintir orang lain yang aktif di ruang ini. Tempat awal yang baik tampaknya adalah forum Library Genesis, dan r/DataHoarder. Tim Arsip juga memiliki individu yang berpikiran sama, meskipun mereka beroperasi dalam hukum (bahkan jika dalam beberapa area abu-abu hukum). Adegan \"warez\" dan pembajakan tradisional juga memiliki orang-orang yang berpikir dengan cara yang sama."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Kami terbuka untuk ide-ide tentang bagaimana membangun komunitas dan mengeksplorasi gagasan. Jangan ragu untuk menghubungi kami di Twitter atau Reddit. Mungkin kami bisa mengadakan semacam forum atau grup obrolan. Salah satu tantangannya adalah ini bisa dengan mudah disensor ketika menggunakan platform umum, jadi kami harus mengelolanya sendiri. Ada juga pertimbangan antara membuat diskusi ini sepenuhnya publik (lebih banyak potensi keterlibatan) versus membuatnya privat (tidak membiarkan \"target\" potensial tahu bahwa kami akan mengumpulkan data mereka). Kami harus memikirkan hal itu. Beri tahu kami jika Anda tertarik dengan ini!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proyek"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Ketika kami melakukan sebuah proyek, ada beberapa fase:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Pemilihan domain / filosofi: Di mana Anda ingin fokus, dan mengapa? Apa saja minat, keterampilan, dan keadaan unik Anda yang dapat Anda manfaatkan?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Pemilihan target: Koleksi spesifik mana yang akan Anda mirror?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Pengambilan metadata: Mengkatalogkan informasi tentang file, tanpa benar-benar mengunduh file (yang seringkali jauh lebih besar) itu sendiri."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Pemilihan data: Berdasarkan metadata, mempersempit data mana yang paling relevan untuk diarsipkan saat ini. Bisa jadi semuanya, tetapi seringkali ada cara yang masuk akal untuk menghemat ruang dan bandwidth."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Pengambilan data: Benar-benar mendapatkan data tersebut."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribusi: Mengemasnya dalam bentuk torrent, mengumumkannya di suatu tempat, membuat orang menyebarkannya."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ini bukan fase yang sepenuhnya independen, dan seringkali wawasan dari fase selanjutnya mengarahkan Anda kembali ke fase sebelumnya. Misalnya, selama pengambilan metadata Anda mungkin menyadari bahwa target yang Anda pilih memiliki mekanisme pertahanan di luar kemampuan Anda (seperti pemblokiran IP), jadi Anda kembali dan mencari target yang berbeda."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Pemilihan domain / filosofi"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Tidak ada kekurangan pengetahuan dan warisan budaya yang perlu diselamatkan, yang bisa sangat membingungkan. Itulah mengapa seringkali berguna untuk meluangkan waktu sejenak dan memikirkan apa kontribusi Anda."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Setiap orang memiliki cara berpikir yang berbeda tentang ini, tetapi berikut adalah beberapa pertanyaan yang dapat Anda tanyakan pada diri sendiri:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Mengapa Anda tertarik dengan ini? Apa yang Anda minati? Jika kita bisa mendapatkan sekelompok orang yang semuanya mengarsipkan hal-hal yang mereka pedulikan secara khusus, itu akan mencakup banyak hal! Anda akan tahu lebih banyak daripada orang rata-rata tentang minat Anda, seperti data penting apa yang harus disimpan, koleksi dan komunitas online terbaik, dan sebagainya."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Keterampilan apa yang Anda miliki yang dapat Anda manfaatkan? Misalnya, jika Anda adalah ahli keamanan online, Anda dapat menemukan cara untuk mengatasi pemblokiran IP untuk target yang aman. Jika Anda hebat dalam mengorganisir komunitas, maka mungkin Anda bisa mengumpulkan beberapa orang untuk mencapai tujuan. Namun, berguna untuk mengetahui beberapa pemrograman, setidaknya untuk menjaga keamanan operasional yang baik selama proses ini."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Berapa banyak waktu yang Anda miliki untuk ini? Saran kami adalah memulai dari yang kecil dan melakukan proyek yang lebih besar seiring Anda terbiasa, tetapi ini bisa sangat menyita waktu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Apa area dengan pengaruh tinggi yang bisa Anda fokuskan? Jika Anda akan menghabiskan X jam untuk mengarsipkan bajakan, lalu bagaimana Anda bisa mendapatkan \"hasil maksimal\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Apa cara unik yang Anda pikirkan tentang ini? Anda mungkin memiliki beberapa ide atau pendekatan menarik yang mungkin terlewatkan oleh orang lain."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Dalam kasus kami, kami sangat peduli tentang pelestarian jangka panjang ilmu pengetahuan. Kami tahu tentang Library Genesis, dan bagaimana itu sepenuhnya dimirror berkali-kali menggunakan torrent. Kami menyukai ide itu. Kemudian suatu hari, salah satu dari kami mencoba mencari beberapa buku teks ilmiah di Library Genesis, tetapi tidak menemukannya, yang meragukan seberapa lengkapnya itu. Kami kemudian mencari buku teks tersebut secara online, dan menemukannya di tempat lain, yang menanamkan benih untuk proyek kami. Bahkan sebelum kami tahu tentang Z-Library, kami memiliki ide untuk tidak mencoba mengumpulkan semua buku tersebut secara manual, tetapi fokus pada memirror koleksi yang ada, dan menyumbangkannya kembali ke Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Pemilihan target"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Jadi, kita sudah memiliki area yang kita lihat, sekarang koleksi spesifik mana yang akan kita mirror? Ada beberapa hal yang membuat target yang baik:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Besar"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unik: tidak sudah banyak dicakup oleh proyek lain."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Dapat diakses: tidak menggunakan banyak lapisan perlindungan untuk mencegah Anda mengikis metadata dan data mereka."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Wawasan khusus: Anda memiliki informasi khusus tentang target ini, seperti Anda entah bagaimana memiliki akses khusus ke koleksi ini, atau Anda menemukan cara untuk mengalahkan pertahanan mereka. Ini tidak diperlukan (proyek kami yang akan datang tidak melakukan sesuatu yang istimewa), tetapi tentu saja membantu!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Ketika kami menemukan buku teks sains kami di situs web selain Library Genesis, kami mencoba mencari tahu bagaimana mereka bisa sampai ke internet. Kami kemudian menemukan Z-Library, dan menyadari bahwa meskipun sebagian besar buku tidak pertama kali muncul di sana, mereka akhirnya berakhir di sana. Kami belajar tentang hubungannya dengan Library Genesis, dan struktur insentif (keuangan) serta antarmuka pengguna yang unggul, yang keduanya membuatnya menjadi koleksi yang jauh lebih lengkap. Kami kemudian melakukan beberapa pengikisan metadata dan data awal, dan menyadari bahwa kami bisa mengatasi batas unduhan IP mereka, memanfaatkan akses khusus salah satu anggota kami ke banyak server proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Saat Anda menjelajahi target yang berbeda, sudah penting untuk menyembunyikan jejak Anda dengan menggunakan VPN dan alamat email sekali pakai, yang akan kita bahas lebih lanjut nanti."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Pengikisan metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Mari kita menjadi sedikit lebih teknis di sini. Untuk benar-benar mengikis metadata dari situs web, kami menjaga semuanya tetap sederhana. Kami menggunakan skrip Python, kadang-kadang curl, dan database MySQL untuk menyimpan hasilnya. Kami belum menggunakan perangkat lunak pengikisan canggih yang dapat memetakan situs web yang kompleks, karena sejauh ini kami hanya perlu mengikis satu atau dua jenis halaman dengan hanya menjumlahkan melalui id dan mem-parsing HTML. Jika tidak ada halaman yang mudah dijumlahkan, maka Anda mungkin memerlukan perayap yang tepat yang mencoba menemukan semua halaman."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Sebelum Anda mulai mengikis seluruh situs web, cobalah melakukannya secara manual untuk sementara waktu. Telusuri beberapa lusin halaman sendiri, untuk mendapatkan gambaran tentang cara kerjanya. Terkadang Anda akan langsung menghadapi blokir IP atau perilaku menarik lainnya dengan cara ini. Hal yang sama berlaku untuk pengikisan data: sebelum terlalu dalam ke target ini, pastikan Anda benar-benar dapat mengunduh datanya secara efektif."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Untuk mengatasi pembatasan, ada beberapa hal yang bisa Anda coba. Apakah ada alamat IP atau server lain yang menyimpan data yang sama tetapi tidak memiliki pembatasan yang sama? Apakah ada titik akhir API yang tidak memiliki pembatasan, sementara yang lain memiliki? Pada tingkat pengunduhan berapa IP Anda diblokir, dan untuk berapa lama? Atau apakah Anda tidak diblokir tetapi diperlambat? Bagaimana jika Anda membuat akun pengguna, bagaimana hal itu mengubah keadaan? Bisakah Anda menggunakan HTTP/2 untuk menjaga koneksi tetap terbuka, dan apakah itu meningkatkan tingkat di mana Anda dapat meminta halaman? Apakah ada halaman yang mencantumkan beberapa file sekaligus, dan apakah informasi yang tercantum di sana cukup?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Hal-hal yang mungkin ingin Anda simpan termasuk:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Judul"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nama file / lokasi"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: bisa berupa ID internal, tetapi ID seperti ISBN atau DOI juga berguna."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Ukuran: untuk menghitung berapa banyak ruang disk yang Anda butuhkan."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): untuk memastikan bahwa Anda mengunduh file dengan benar."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Tanggal ditambahkan/dimodifikasi: sehingga Anda dapat kembali nanti dan mengunduh file yang belum Anda unduh sebelumnya (meskipun Anda sering juga dapat menggunakan ID atau hash untuk ini)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Deskripsi, kategori, tag, penulis, bahasa, dll."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Kami biasanya melakukan ini dalam dua tahap. Pertama, kami mengunduh file HTML mentah, biasanya langsung ke MySQL (untuk menghindari banyak file kecil, yang akan kita bahas lebih lanjut di bawah). Kemudian, dalam langkah terpisah, kami melalui file HTML tersebut dan mem-parsingnya ke dalam tabel MySQL yang sebenarnya. Dengan cara ini Anda tidak perlu mengunduh semuanya dari awal jika Anda menemukan kesalahan dalam kode parsing Anda, karena Anda dapat memproses ulang file HTML dengan kode baru. Ini juga sering lebih mudah untuk memparallelkan langkah pemrosesan, sehingga menghemat waktu (dan Anda dapat menulis kode pemrosesan saat pengikisan sedang berjalan, daripada harus menulis kedua langkah sekaligus)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Akhirnya, perhatikan bahwa untuk beberapa target, pengambilan metadata adalah satu-satunya yang ada. Ada beberapa koleksi metadata besar di luar sana yang tidak terjaga dengan baik."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Pemilihan Data"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Seringkali Anda dapat menggunakan metadata untuk menentukan subset data yang masuk akal untuk diunduh. Bahkan jika Anda pada akhirnya ingin mengunduh semua data, akan berguna untuk memprioritaskan item yang paling penting terlebih dahulu, jika Anda terdeteksi dan pertahanan ditingkatkan, atau karena Anda perlu membeli lebih banyak disk, atau hanya karena sesuatu yang lain muncul dalam hidup Anda sebelum Anda dapat mengunduh semuanya."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Misalnya, sebuah koleksi mungkin memiliki beberapa edisi dari sumber yang sama (seperti buku atau film), di mana satu ditandai sebagai kualitas terbaik. Menyimpan edisi-edisi tersebut terlebih dahulu akan sangat masuk akal. Anda mungkin pada akhirnya ingin menyimpan semua edisi, karena dalam beberapa kasus metadata mungkin ditandai secara tidak benar, atau mungkin ada kompromi yang tidak diketahui antara edisi-edisi tersebut (misalnya, \"edisi terbaik\" mungkin terbaik dalam banyak hal tetapi lebih buruk dalam hal lain, seperti film dengan resolusi lebih tinggi tetapi tanpa subtitle)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Anda juga dapat mencari database metadata Anda untuk menemukan hal-hal menarik. Apa file terbesar yang di-host, dan mengapa begitu besar? Apa file terkecil? Apakah ada pola menarik atau tak terduga terkait kategori tertentu, bahasa, dan sebagainya? Apakah ada judul yang duplikat atau sangat mirip? Apakah ada pola kapan data ditambahkan, seperti satu hari di mana banyak file ditambahkan sekaligus? Anda sering dapat belajar banyak dengan melihat dataset dengan cara yang berbeda."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Dalam kasus kami, kami mendeduplikasi buku Z-Library terhadap hash md5 di Library Genesis, sehingga menghemat banyak waktu unduh dan ruang disk. Ini adalah situasi yang cukup unik. Dalam kebanyakan kasus, tidak ada database komprehensif tentang file mana yang sudah terjaga dengan baik oleh sesama pembajak. Ini sendiri adalah peluang besar bagi seseorang di luar sana. Akan sangat bagus untuk memiliki gambaran yang diperbarui secara teratur tentang hal-hal seperti musik dan film yang sudah banyak disebarkan di situs web torrent, dan karenanya menjadi prioritas lebih rendah untuk dimasukkan dalam mirror bajak laut."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Pengambilan Data"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Sekarang Anda siap untuk benar-benar mengunduh data dalam jumlah besar. Seperti yang disebutkan sebelumnya, pada titik ini Anda seharusnya sudah secara manual mengunduh sejumlah file, untuk lebih memahami perilaku dan batasan target. Namun, masih akan ada kejutan yang menanti Anda begitu Anda benar-benar mulai mengunduh banyak file sekaligus."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Saran kami di sini adalah untuk tetap sederhana. Mulailah dengan hanya mengunduh sejumlah file. Anda dapat menggunakan Python, dan kemudian memperluas ke beberapa thread. Tetapi kadang-kadang bahkan lebih sederhana adalah dengan menghasilkan file Bash langsung dari database, dan kemudian menjalankan beberapa di antaranya di beberapa jendela terminal untuk meningkatkan skala. Trik teknis cepat yang layak disebutkan di sini adalah menggunakan OUTFILE di MySQL, yang dapat Anda tulis di mana saja jika Anda menonaktifkan \"secure_file_priv\" di mysqld.cnf (dan pastikan juga untuk menonaktifkan/menggantikan AppArmor jika Anda menggunakan Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Kami menyimpan data pada hard disk sederhana. Mulailah dengan apa pun yang Anda miliki, dan kembangkan perlahan. Bisa jadi menakutkan untuk memikirkan menyimpan ratusan TB data. Jika itu adalah situasi yang Anda hadapi, cukup keluarkan subset yang baik terlebih dahulu, dan dalam pengumuman Anda minta bantuan untuk menyimpan sisanya. Jika Anda ingin mendapatkan lebih banyak hard drive sendiri, maka r/DataHoarder memiliki beberapa sumber daya yang baik untuk mendapatkan penawaran yang bagus."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Cobalah untuk tidak terlalu khawatir tentang sistem file yang mewah. Mudah terjebak dalam lubang kelinci untuk mengatur hal-hal seperti ZFS. Satu detail teknis yang perlu diperhatikan adalah bahwa banyak sistem file tidak menangani dengan baik banyak file. Kami menemukan bahwa solusi sederhana adalah dengan membuat beberapa direktori, misalnya untuk rentang ID yang berbeda atau prefiks hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Setelah mengunduh data, pastikan untuk memeriksa integritas file menggunakan hash dalam metadata, jika tersedia."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribusi"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Anda memiliki data, sehingga memberi Anda kepemilikan mirror bajak laut pertama di dunia dari target Anda (kemungkinan besar). Dalam banyak hal, bagian tersulit sudah selesai, tetapi bagian yang paling berisiko masih di depan Anda. Bagaimanapun, sejauh ini Anda telah beroperasi secara diam-diam; terbang di bawah radar. Yang harus Anda lakukan adalah menggunakan VPN yang baik sepanjang waktu, tidak mengisi detail pribadi Anda dalam formulir apa pun (tentu saja), dan mungkin menggunakan sesi browser khusus (atau bahkan komputer yang berbeda)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Sekarang Anda harus mendistribusikan data. Dalam kasus kami, kami pertama kali ingin menyumbangkan buku kembali ke Library Genesis, tetapi kemudian dengan cepat menemukan kesulitan dalam hal itu (penyortiran fiksi vs non-fiksi). Jadi kami memutuskan untuk distribusi menggunakan torrent gaya Library Genesis. Jika Anda memiliki kesempatan untuk berkontribusi pada proyek yang sudah ada, maka itu bisa menghemat banyak waktu Anda. Namun, saat ini tidak banyak mirror bajak laut yang terorganisir dengan baik di luar sana."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Jadi katakanlah Anda memutuskan untuk mendistribusikan torrent sendiri. Cobalah untuk menjaga file-file tersebut kecil, sehingga mudah untuk dimirror di situs web lain. Anda kemudian harus menanam torrent tersebut sendiri, sambil tetap anonim. Anda dapat menggunakan VPN (dengan atau tanpa port forwarding), atau membayar dengan Bitcoin yang diacak untuk Seedbox. Jika Anda tidak tahu apa arti beberapa istilah tersebut, Anda akan memiliki banyak bacaan yang harus dilakukan, karena penting bagi Anda untuk memahami kompromi risiko di sini."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Anda dapat meng-host file torrent itu sendiri di situs web torrent yang ada. Dalam kasus kami, kami memilih untuk benar-benar meng-host situs web, karena kami juga ingin menyebarkan filosofi kami dengan cara yang jelas. Anda dapat melakukan ini sendiri dengan cara yang serupa (kami menggunakan Njalla untuk domain dan hosting kami, dibayar dengan Bitcoin yang diacak), tetapi juga jangan ragu untuk menghubungi kami agar kami dapat meng-host torrent Anda. Kami ingin membangun indeks komprehensif dari mirror bajak laut seiring waktu, jika ide ini diterima."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Adapun pemilihan VPN, banyak yang telah ditulis tentang ini, jadi kami hanya akan mengulangi saran umum untuk memilih berdasarkan reputasi. Kebijakan tanpa log yang telah diuji di pengadilan dengan rekam jejak panjang dalam melindungi privasi adalah opsi risiko terendah, menurut kami. Perhatikan bahwa bahkan ketika Anda melakukan semuanya dengan benar, Anda tidak pernah bisa mencapai risiko nol. Misalnya, saat menanam torrent Anda, aktor negara yang sangat termotivasi mungkin dapat melihat aliran data masuk dan keluar untuk server VPN, dan menyimpulkan siapa Anda. Atau Anda bisa saja melakukan kesalahan. Kami mungkin sudah melakukannya, dan akan melakukannya lagi. Untungnya, negara-negara tidak terlalu peduli <em>tentang</em> pembajakan."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Satu keputusan yang harus dibuat untuk setiap proyek adalah apakah akan mempublikasikannya menggunakan identitas yang sama seperti sebelumnya, atau tidak. Jika Anda terus menggunakan nama yang sama, maka kesalahan dalam keamanan operasional dari proyek sebelumnya dapat kembali menghantui Anda. Tetapi mempublikasikan dengan nama yang berbeda berarti Anda tidak membangun reputasi yang lebih lama. Kami memilih untuk memiliki keamanan operasional yang kuat sejak awal sehingga kami dapat terus menggunakan identitas yang sama, tetapi kami tidak akan ragu untuk mempublikasikan dengan nama yang berbeda jika kami melakukan kesalahan atau jika keadaan memerlukannya."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Menyebarkan berita bisa jadi rumit. Seperti yang kami katakan, ini masih merupakan komunitas yang niche. Kami awalnya memposting di Reddit, tetapi benar-benar mendapatkan perhatian di Hacker News. Untuk saat ini rekomendasi kami adalah mempostingnya di beberapa tempat dan melihat apa yang terjadi. Dan sekali lagi, hubungi kami. Kami ingin menyebarkan berita tentang lebih banyak upaya pengarsipan bajak laut."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Semoga ini bermanfaat bagi para arsiparis bajak laut yang baru memulai. Kami sangat senang menyambut Anda ke dunia ini, jadi jangan ragu untuk menghubungi kami. Mari kita lestarikan sebanyak mungkin pengetahuan dan budaya dunia, dan mirror-kan seluas mungkin."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Memperkenalkan Mirror Perpustakaan Bajak Laut: Melestarikan 7TB buku (yang tidak ada di Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Proyek ini (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>) bertujuan untuk berkontribusi pada pelestarian dan pembebasan pengetahuan manusia. Kami membuat kontribusi kecil dan sederhana kami, mengikuti jejak para pendahulu yang hebat."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Fokus dari proyek ini digambarkan oleh namanya:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Bajak Laut</strong> - Kami dengan sengaja melanggar undang-undang hak cipta di sebagian besar negara. Ini memungkinkan kami melakukan sesuatu yang tidak dapat dilakukan oleh entitas legal: memastikan buku-buku di-mirror-kan seluas mungkin."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Perpustakaan</strong> - Seperti kebanyakan perpustakaan, kami fokus terutama pada materi tertulis seperti buku. Kami mungkin akan berkembang ke jenis media lain di masa depan."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Kami murni merupakan mirror dari perpustakaan yang ada. Kami fokus pada pelestarian, bukan pada membuat buku mudah dicari dan diunduh (akses) atau membangun komunitas besar orang yang berkontribusi buku baru (sumber)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Perpustakaan pertama yang kami mirror adalah Z-Library. Ini adalah perpustakaan yang populer (dan ilegal). Mereka telah mengambil koleksi Library Genesis dan membuatnya mudah dicari. Selain itu, mereka sangat efektif dalam meminta kontribusi buku baru, dengan memberikan insentif kepada pengguna yang berkontribusi dengan berbagai keuntungan. Saat ini mereka tidak mengembalikan buku-buku baru ini ke Library Genesis. Dan tidak seperti Library Genesis, mereka tidak membuat koleksi mereka mudah di-mirror, yang mencegah pelestarian yang luas. Ini penting untuk model bisnis mereka, karena mereka mengenakan biaya untuk mengakses koleksi mereka secara massal (lebih dari 10 buku per hari)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Kami tidak membuat penilaian moral tentang memungut biaya untuk akses massal ke koleksi buku ilegal. Tidak diragukan lagi bahwa Z-Library telah berhasil memperluas akses ke pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bagian kami: memastikan pelestarian jangka panjang dari koleksi pribadi ini."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Kami ingin mengundang Anda untuk membantu melestarikan dan membebaskan pengetahuan manusia dengan mengunduh dan menyebarkan torrent kami. Lihat halaman proyek untuk informasi lebih lanjut tentang bagaimana data diatur."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Kami juga sangat mengundang Anda untuk menyumbangkan ide-ide Anda tentang koleksi mana yang harus di-mirror selanjutnya, dan bagaimana melakukannya. Bersama-sama kita bisa mencapai banyak hal. Ini hanyalah kontribusi kecil di antara banyak lainnya. Terima kasih, untuk semua yang Anda lakukan."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Kami tidak menautkan ke file dari blog ini. Silakan temukan sendiri.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump ISBNdb, atau Berapa Banyak Buku yang Dilestarikan Selamanya?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Jika kita benar-benar mendeduplikasi file dari perpustakaan bayangan, berapa persentase dari semua buku di dunia yang telah kita lestarikan?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Dengan Mirror Perpustakaan Bajak Laut (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>), tujuan kami adalah mengambil semua buku di dunia, dan melestarikannya selamanya.<sup>1</sup> Antara torrent Z-Library kami, dan torrent Library Genesis asli, kami memiliki 11.783.153 file. Tapi berapa banyak sebenarnya? Jika kita benar-benar mendeduplikasi file-file tersebut, berapa persentase dari semua buku di dunia yang telah kita lestarikan? Kami benar-benar ingin memiliki sesuatu seperti ini:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% odari warisan tertulis umat manusia dilestarikan selamanya"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Untuk persentase, kita memerlukan penyebut: jumlah total buku yang pernah diterbitkan.<sup>2</sup> Sebelum Google Books berakhir, seorang insinyur di proyek tersebut, Leonid Taycher, <a %(booksearch_blogspot)s>mencoba memperkirakan</a> angka ini. Dia datang — dengan nada bercanda — dengan 129.864.880 (“setidaknya sampai hari Minggu”). Dia memperkirakan angka ini dengan membangun basis data terpadu dari semua buku di dunia. Untuk ini, dia mengumpulkan berbagai datasets dan kemudian menggabungkannya dengan berbagai cara."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Sebagai catatan singkat, ada orang lain yang mencoba mengkatalogkan semua buku di dunia: Aaron Swartz, aktivis digital yang telah meninggal dan salah satu pendiri Reddit.<sup>3</sup> Dia <a %(youtube)s>memulai Open Library</a> dengan tujuan “satu halaman web untuk setiap buku yang pernah diterbitkan”, menggabungkan data dari berbagai sumber. Dia akhirnya membayar harga tertinggi untuk pekerjaan pelestarian digitalnya ketika dia dituntut karena mengunduh massal makalah akademis, yang mengarah pada bunuh dirinya. Tak perlu dikatakan, ini adalah salah satu alasan mengapa kelompok kami menggunakan nama samaran, dan mengapa kami sangat berhati-hati. Open Library masih dijalankan dengan heroik oleh orang-orang di Internet Archive, melanjutkan warisan Aaron. Kami akan kembali ke topik ini nanti dalam postingan ini."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Dalam postingan blog Google, Taycher menjelaskan beberapa tantangan dalam memperkirakan angka ini. Pertama, apa yang dimaksud dengan buku? Ada beberapa definisi yang mungkin:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Salinan fisik.</strong> Jelas ini tidak terlalu membantu, karena mereka hanya duplikat dari materi yang sama. Akan sangat keren jika kita bisa melestarikan semua anotasi yang dibuat orang dalam buku, seperti “coretan di pinggir” yang terkenal dari Fermat. Namun sayangnya, itu akan tetap menjadi impian seorang arsiparis."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Karya”.</strong> Misalnya “Harry Potter dan Kamar Rahasia” sebagai konsep logis, mencakup semua versinya, seperti terjemahan dan cetakan ulang yang berbeda. Ini adalah definisi yang agak berguna, tetapi bisa sulit untuk menentukan batasan apa yang dihitung. Misalnya, kita mungkin ingin melestarikan terjemahan yang berbeda, meskipun cetakan ulang dengan perbedaan kecil mungkin tidak sepenting itu."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edisi”.</strong> Di sini Anda menghitung setiap versi unik dari sebuah buku. Jika ada yang berbeda tentangnya, seperti sampul yang berbeda atau kata pengantar yang berbeda, itu dihitung sebagai edisi yang berbeda."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Berkas.</strong> Saat bekerja dengan perpustakaan bayangan seperti Library Genesis, Sci-Hub, atau Z-Library, ada pertimbangan tambahan. Bisa ada beberapa pemindaian dari edisi yang sama. Dan orang-orang dapat membuat versi yang lebih baik dari berkas yang ada, dengan memindai teks menggunakan OCR, atau memperbaiki halaman yang dipindai pada sudut tertentu. Kami ingin hanya menghitung berkas-berkas ini sebagai satu edisi, yang memerlukan metadata yang baik, atau deduplikasi menggunakan ukuran kesamaan dokumen."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edisi” tampaknya menjadi definisi yang paling praktis tentang apa itu “buku”. Secara kebetulan, definisi ini juga digunakan untuk menetapkan nomor ISBN unik. ISBN, atau International Standard Book Number, umumnya digunakan untuk perdagangan internasional, karena terintegrasi dengan sistem barcode internasional (”International Article Number”). Jika Anda ingin menjual buku di toko, itu memerlukan barcode, jadi Anda mendapatkan ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Postingan blog Taycher menyebutkan bahwa meskipun ISBN berguna, mereka tidak universal, karena baru benar-benar diadopsi pada pertengahan tahun tujuh puluhan, dan tidak di seluruh dunia. Namun, ISBN mungkin adalah pengenal edisi buku yang paling banyak digunakan, jadi ini adalah titik awal terbaik kami. Jika kita dapat menemukan semua ISBN di dunia, kita mendapatkan daftar berguna tentang buku mana yang masih perlu dilestarikan."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Jadi, dari mana kita mendapatkan data? Ada sejumlah upaya yang ada yang mencoba menyusun daftar semua buku di dunia:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Bagaimanapun, mereka melakukan penelitian ini untuk Google Books. Namun, metadata mereka tidak dapat diakses secara massal dan cukup sulit untuk diambil."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Seperti yang disebutkan sebelumnya, ini adalah misi utama mereka. Mereka telah mendapatkan sejumlah besar data perpustakaan dari perpustakaan yang bekerja sama dan arsip nasional, dan terus melakukannya. Mereka juga memiliki pustakawan sukarelawan dan tim teknis yang mencoba mendeduplikasi catatan, dan menandainya dengan berbagai jenis metadata. Yang terbaik dari semuanya, dataset mereka sepenuhnya terbuka. Anda dapat dengan mudah <a %(openlibrary)s>mengunduhnya</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Ini adalah situs web yang dijalankan oleh organisasi nirlaba OCLC, yang menjual sistem manajemen perpustakaan. Mereka mengumpulkan metadata buku dari banyak perpustakaan, dan membuatnya tersedia melalui situs web WorldCat. Namun, mereka juga menghasilkan uang dengan menjual data ini, jadi tidak tersedia untuk diunduh secara massal. Mereka memiliki beberapa dataset massal yang lebih terbatas yang tersedia untuk diunduh, bekerja sama dengan perpustakaan tertentu."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Ini adalah topik dari postingan blog ini. ISBNdb mengambil data dari berbagai situs web untuk metadata buku, khususnya data harga, yang kemudian mereka jual kepada penjual buku, sehingga mereka dapat menetapkan harga buku mereka sesuai dengan pasar lainnya. Karena ISBN cukup universal saat ini, mereka secara efektif membangun “halaman web untuk setiap buku”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Berbagai sistem perpustakaan dan arsip individu.</strong> Ada perpustakaan dan arsip yang belum diindeks dan digabungkan oleh salah satu yang di atas, sering kali karena mereka kekurangan dana, atau karena alasan lain tidak ingin berbagi data mereka dengan Open Library, OCLC, Google, dan sebagainya. Banyak dari ini memiliki catatan digital yang dapat diakses melalui internet, dan sering kali tidak terlindungi dengan baik, jadi jika Anda ingin membantu dan bersenang-senang mempelajari tentang sistem perpustakaan yang aneh, ini adalah titik awal yang bagus."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Dalam postingan ini, kami dengan senang hati mengumumkan rilis kecil (dibandingkan dengan rilis Z-Library kami sebelumnya). Kami mengambil sebagian besar ISBNdb, dan membuat data tersebut tersedia untuk diunduh melalui torrent di situs web Pirate Library Mirror (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arsip Anna</a>; kami tidak akan menautkannya langsung di sini, cukup cari saja). Ini adalah sekitar 30,9 juta catatan (20GB sebagai <a %(jsonlines)s>JSON Lines</a>; 4,4GB dikompresi). Di situs web mereka, mereka mengklaim bahwa mereka sebenarnya memiliki 32,6 juta catatan, jadi kami mungkin entah bagaimana melewatkan beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Bagaimanapun, untuk saat ini kami tidak akan membagikan secara persis bagaimana kami melakukannya — kami akan meninggalkannya sebagai latihan untuk pembaca. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Yang akan kami bagikan adalah beberapa analisis awal, untuk mencoba mendekati perkiraan jumlah buku di dunia. Kami melihat tiga dataset: dataset ISBNdb baru ini, rilis metadata asli kami yang kami ambil dari perpustakaan bayangan Z-Library (yang mencakup Library Genesis), dan dump data Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Mari kita mulai dengan beberapa angka kasar:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Di Z-Library/Libgen dan Open Library ada banyak buku lebih banyak daripada ISBN unik. Apakah itu berarti banyak dari buku-buku tersebut tidak memiliki ISBN, atau apakah metadata ISBNnya hanya hilang? Kami mungkin dapat menjawab pertanyaan ini dengan kombinasi pencocokan otomatis berdasarkan atribut lain (judul, penulis, penerbit, dll), menarik lebih banyak sumber data, dan mengekstraksi ISBN dari pemindaian buku yang sebenarnya (dalam kasus Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Berapa banyak dari ISBN tersebut yang unik? Ini paling baik diilustrasikan dengan diagram Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Untuk lebih tepatnya:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Kami terkejut dengan betapa sedikitnya tumpang tindih yang ada! ISBNdb memiliki sejumlah besar ISBN yang tidak muncul baik di Z-Library maupun Open Library, dan hal yang sama berlaku (dalam tingkat yang lebih kecil namun tetap signifikan) untuk kedua lainnya. Ini menimbulkan banyak pertanyaan baru. Seberapa banyak pencocokan otomatis akan membantu dalam menandai buku-buku yang tidak ditandai dengan ISBN? Apakah akan ada banyak kecocokan dan oleh karena itu meningkatkan tumpang tindih? Juga, apa yang akan terjadi jika kita menambahkan dataset ke-4 atau ke-5? Seberapa banyak tumpang tindih yang akan kita lihat kemudian?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Ini memberi kita titik awal. Kita sekarang dapat melihat semua ISBN yang tidak ada dalam dataset Z-Library, dan yang juga tidak cocok dengan bidang judul/penulis. Itu dapat memberi kita pegangan untuk melestarikan semua buku di dunia: pertama dengan mengumpulkan internet untuk pemindaian, kemudian dengan pergi ke kehidupan nyata untuk memindai buku. Yang terakhir bahkan bisa didanai oleh masyarakat, atau didorong oleh \"hadiah\" dari orang-orang yang ingin melihat buku-buku tertentu didigitalkan. Semua itu adalah cerita untuk waktu yang berbeda."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Jika Anda ingin membantu dengan salah satu dari ini — analisis lebih lanjut; mengumpulkan lebih banyak metadata; menemukan lebih banyak buku; OCR buku; melakukan ini untuk domain lain (misalnya makalah, buku audio, film, acara TV, majalah) atau bahkan membuat beberapa data ini tersedia untuk hal-hal seperti pelatihan model bahasa besar / ML — silakan hubungi saya (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Jika Anda tertarik secara khusus pada analisis data, kami sedang bekerja untuk membuat dataset dan skrip kami tersedia dalam format yang lebih mudah digunakan. Akan sangat bagus jika Anda bisa langsung menyalin notebook dan mulai bermain dengan ini."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Akhirnya, jika Anda ingin mendukung pekerjaan ini, mohon pertimbangkan untuk memberikan donasi. Ini adalah operasi yang sepenuhnya dijalankan oleh sukarelawan, dan kontribusi Anda membuat perbedaan besar. Setiap sedikit membantu. Untuk saat ini kami menerima donasi dalam bentuk kripto; lihat halaman Donasi di Arsip Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Untuk beberapa definisi \"selamanya\" yang masuk akal. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Tentu saja, warisan tertulis umat manusia jauh lebih dari sekadar buku, terutama saat ini. Demi posting ini dan rilis terbaru kami, kami fokus pada buku, tetapi minat kami meluas lebih jauh."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Ada banyak hal lain yang bisa dikatakan tentang Aaron Swartz, tetapi kami hanya ingin menyebutkannya secara singkat, karena dia memainkan peran penting dalam cerita ini. Seiring berjalannya waktu, lebih banyak orang mungkin menemukan namanya untuk pertama kalinya, dan kemudian dapat menyelami lebih dalam sendiri."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Jendela kritis perpustakaan bayangan"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Bagaimana kita bisa mengklaim melestarikan koleksi kita selamanya, ketika mereka sudah mendekati 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versi Cina 中文版</a>, diskusikan di <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Di Arsip Anna, kami sering ditanya bagaimana kami bisa mengklaim melestarikan koleksi kami selamanya, ketika ukuran totalnya sudah mendekati 1 Petabyte (1000 TB), dan masih terus bertambah. Dalam artikel ini, kita akan melihat filosofi kami, dan melihat mengapa dekade berikutnya sangat penting untuk misi kami melestarikan pengetahuan dan budaya umat manusia."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Ukuran total</a> koleksi kami, selama beberapa bulan terakhir, dipecah berdasarkan jumlah penyebar torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritas"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Mengapa kita sangat peduli dengan makalah dan buku? Mari kita kesampingkan keyakinan mendasar kita dalam pelestarian secara umum — kita mungkin menulis posting lain tentang itu. Jadi mengapa makalah dan buku secara khusus? Jawabannya sederhana: <strong>kepadatan informasi</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte penyimpanan, teks tertulis menyimpan informasi paling banyak dari semua media. Meskipun kami peduli tentang pengetahuan dan budaya, kami lebih peduli tentang yang pertama. Secara keseluruhan, kami menemukan hierarki kepadatan informasi dan pentingnya pelestarian yang terlihat kira-kira seperti ini:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Makalah akademis, jurnal, laporan"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Data organik seperti urutan DNA, biji tanaman, atau sampel mikroba"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Buku non-fiksi"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Kode perangkat lunak sains & teknik"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Data pengukuran seperti pengukuran ilmiah, data ekonomi, laporan perusahaan"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Situs web sains & teknik, diskusi online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Majalah non-fiksi, surat kabar, manual"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transkrip non-fiksi dari ceramah, dokumenter, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Data internal dari perusahaan atau pemerintah (bocoran)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Catatan metadata secara umum (dari non-fiksi dan fiksi; dari media lain, seni, orang, dll; termasuk ulasan)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Data geografis (misalnya peta, survei geologi)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkrip dari proses hukum atau pengadilan"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versi fiksi atau hiburan dari semua hal di atas"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Peringkat dalam daftar ini agak sewenang-wenang — beberapa item adalah seri atau memiliki ketidaksepakatan dalam tim kami — dan kami mungkin melupakan beberapa kategori penting. Namun, ini adalah cara kami memprioritaskan secara kasar."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Beberapa item ini terlalu berbeda dari yang lain untuk kami khawatirkan (atau sudah diurus oleh lembaga lain), seperti data organik atau data geografis. Namun, sebagian besar item dalam daftar ini sebenarnya penting bagi kami."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Faktor besar lainnya dalam prioritas kami adalah seberapa besar risiko yang dihadapi suatu karya. Kami lebih suka fokus pada karya yang:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Langka"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Sangat kurang diperhatikan"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Sangat berisiko dihancurkan (misalnya oleh perang, pemotongan dana, tuntutan hukum, atau penganiayaan politik)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Akhirnya, kami peduli tentang skala. Kami memiliki waktu dan uang yang terbatas, jadi kami lebih suka menghabiskan sebulan untuk menyelamatkan 10.000 buku daripada 1.000 buku — jika mereka sama berharganya dan berisiko."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Perpustakaan bayangan"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ada banyak organisasi yang memiliki misi serupa, dan prioritas serupa. Memang, ada perpustakaan, arsip, laboratorium, museum, dan lembaga lain yang ditugaskan untuk pelestarian semacam ini. Banyak dari mereka didanai dengan baik, oleh pemerintah, individu, atau perusahaan. Namun, mereka memiliki satu titik buta besar: sistem hukum."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Di sinilah peran unik perpustakaan bayangan, dan alasan Arsip Anna ada. Kami dapat melakukan hal-hal yang tidak diizinkan oleh lembaga lain. Sekarang, bukan (sering) bahwa kami dapat mengarsipkan materi yang ilegal untuk dilestarikan di tempat lain. Tidak, di banyak tempat legal untuk membangun arsip dengan buku, makalah, majalah, dan sebagainya."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Tetapi yang sering kali kurang dimiliki arsip hukum adalah <strong>redundansi dan umur panjang</strong>. Ada buku-buku yang hanya memiliki satu salinan di beberapa perpustakaan fisik di suatu tempat. Ada catatan metadata yang dijaga oleh satu perusahaan. Ada surat kabar yang hanya diawetkan dalam bentuk mikrofilm di satu arsip. Perpustakaan bisa mengalami pemotongan dana, perusahaan bisa bangkrut, arsip bisa dibom dan dibakar habis. Ini bukanlah hipotesis — ini terjadi sepanjang waktu."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Hal yang dapat kami lakukan secara unik di Arsip Anna adalah menyimpan banyak salinan karya, dalam skala besar. Kami dapat mengumpulkan makalah, buku, majalah, dan lainnya, serta mendistribusikannya secara massal. Saat ini kami melakukannya melalui torrent, tetapi teknologi yang tepat tidak penting dan akan berubah seiring waktu. Bagian pentingnya adalah mendapatkan banyak salinan yang didistribusikan ke seluruh dunia. Kutipan ini dari lebih dari 200 tahun yang lalu masih relevan:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Yang hilang tidak dapat dipulihkan; tetapi mari kita selamatkan apa yang tersisa: bukan dengan brankas dan kunci yang menjauhkan mereka dari pandangan dan penggunaan publik, dengan menyerahkan mereka pada pemborosan waktu, tetapi dengan penggandaan salinan, yang akan menempatkan mereka di luar jangkauan kecelakaan.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Catatan singkat tentang domain publik. Karena Arsip Anna secara unik berfokus pada aktivitas yang ilegal di banyak tempat di seluruh dunia, kami tidak repot-repot dengan koleksi yang sudah tersedia secara luas, seperti buku domain publik. Entitas hukum sering kali sudah merawatnya dengan baik. Namun, ada pertimbangan yang membuat kami kadang-kadang bekerja pada koleksi yang tersedia untuk umum:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Catatan metadata dapat dilihat secara bebas di situs web Worldcat, tetapi tidak dapat diunduh secara massal (sampai kami <a %(worldcat_scrape)s>mengikis</a>nya)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kode dapat bersifat open source di Github, tetapi Github secara keseluruhan tidak dapat dengan mudah dicerminkan dan dengan demikian diawetkan (meskipun dalam kasus ini ada salinan yang cukup tersebar dari sebagian besar repositori kode)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit gratis untuk digunakan, tetapi baru-baru ini menerapkan langkah-langkah anti-scraping yang ketat, setelah pelatihan LLM yang haus data (lebih lanjut tentang itu nanti)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Penggandaan salinan"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Kembali ke pertanyaan awal kami: bagaimana kami dapat mengklaim untuk melestarikan koleksi kami selamanya? Masalah utama di sini adalah bahwa koleksi kami telah <a %(torrents_stats)s>tumbuh</a> dengan cepat, dengan mengikis dan membuka sumber beberapa koleksi besar (di atas pekerjaan luar biasa yang sudah dilakukan oleh perpustakaan bayangan data terbuka lainnya seperti Sci-Hub dan Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Pertumbuhan data ini membuat koleksi lebih sulit untuk dicerminkan di seluruh dunia. Penyimpanan data mahal! Namun kami optimis, terutama ketika mengamati tiga tren berikut."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Kami telah memetik buah yang mudah dijangkau"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Yang satu ini mengikuti langsung dari prioritas kami yang dibahas di atas. Kami lebih suka bekerja untuk membebaskan koleksi besar terlebih dahulu. Sekarang setelah kami mengamankan beberapa koleksi terbesar di dunia, kami mengharapkan pertumbuhan kami menjadi jauh lebih lambat."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Masih ada ekor panjang dari koleksi yang lebih kecil, dan buku baru dipindai atau diterbitkan setiap hari, tetapi kecepatannya kemungkinan akan jauh lebih lambat. Kami mungkin masih bisa menggandakan atau bahkan melipatgandakan ukuran, tetapi dalam jangka waktu yang lebih lama."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Biaya penyimpanan terus menurun secara eksponensial"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Pada saat penulisan, <a %(diskprices)s>harga disk</a> per TB sekitar $12 untuk disk baru, $8 untuk disk bekas, dan $4 untuk pita. Jika kami konservatif dan hanya melihat disk baru, itu berarti menyimpan satu petabyte berharga sekitar $12.000. Jika kami mengasumsikan perpustakaan kami akan tiga kali lipat dari 900TB menjadi 2,7PB, itu berarti $32.400 untuk mencerminkan seluruh perpustakaan kami. Menambahkan listrik, biaya perangkat keras lainnya, dan sebagainya, mari kita bulatkan menjadi $40.000. Atau dengan pita lebih seperti $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Di satu sisi <strong>$15.000–$40.000 untuk jumlah semua pengetahuan manusia adalah harga yang murah</strong>. Di sisi lain, agak mahal untuk mengharapkan banyak salinan penuh, terutama jika kami juga ingin orang-orang tersebut terus menanam torrent mereka untuk kepentingan orang lain."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Itu hari ini. Tetapi kemajuan terus berjalan:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Biaya hard drive per TB telah berkurang sekitar sepertiga selama 10 tahun terakhir, dan kemungkinan akan terus menurun dengan kecepatan yang sama. Pita tampaknya berada pada jalur yang sama. Harga SSD turun lebih cepat, dan mungkin akan mengambil alih harga HDD pada akhir dekade ini."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tren harga HDD dari berbagai sumber (klik untuk melihat studi)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Jika ini bertahan, maka dalam 10 tahun kita mungkin hanya melihat $5.000–$13.000 untuk mencerminkan seluruh koleksi kita (1/3), atau bahkan lebih sedikit jika kita tumbuh lebih kecil. Meskipun masih banyak uang, ini akan dapat dicapai oleh banyak orang. Dan mungkin akan lebih baik lagi karena poin berikutnya…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Peningkatan dalam kepadatan informasi"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Saat ini, kami menyimpan buku dalam format mentah seperti yang diberikan kepada kami. Memang, mereka sudah dikompresi, tetapi seringkali masih berupa pemindaian atau foto halaman yang besar."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Sampai sekarang, satu-satunya pilihan untuk mengurangi ukuran total koleksi kami adalah melalui kompresi yang lebih agresif, atau deduplikasi. Namun, untuk mendapatkan penghematan yang signifikan, keduanya terlalu banyak kehilangan kualitas untuk selera kami. Kompresi berat pada foto dapat membuat teks hampir tidak terbaca. Dan deduplikasi memerlukan keyakinan tinggi bahwa buku-buku tersebut benar-benar sama, yang seringkali terlalu tidak akurat, terutama jika isinya sama tetapi pemindaian dilakukan pada kesempatan yang berbeda."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Selalu ada opsi ketiga, tetapi kualitasnya sangat buruk sehingga kami tidak pernah mempertimbangkannya: <strong>OCR, atau Optical Character Recognition</strong>. Ini adalah proses mengubah foto menjadi teks biasa, dengan menggunakan AI untuk mendeteksi karakter dalam foto. Alat untuk ini sudah lama ada, dan cukup baik, tetapi \"cukup baik\" tidak cukup untuk tujuan pelestarian."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Namun, model pembelajaran mendalam multi-modal terbaru telah membuat kemajuan yang sangat cepat, meskipun masih dengan biaya tinggi. Kami mengharapkan akurasi dan biaya akan meningkat secara dramatis dalam beberapa tahun mendatang, hingga menjadi realistis untuk diterapkan pada seluruh perpustakaan kami."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Peningkatan OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Ketika itu terjadi, kami mungkin masih akan menyimpan file asli, tetapi sebagai tambahan kami dapat memiliki versi perpustakaan yang jauh lebih kecil yang kebanyakan orang ingin mirror. Keuntungannya adalah teks mentah itu sendiri lebih mudah dikompresi, dan lebih mudah dideduplikasi, memberikan kami lebih banyak penghematan."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Secara keseluruhan, tidaklah tidak realistis untuk mengharapkan pengurangan ukuran file total setidaknya 5-10x, mungkin bahkan lebih. Bahkan dengan pengurangan konservatif 5x, kami akan melihat <strong>$1,000–$3,000 dalam 10 tahun bahkan jika perpustakaan kami tiga kali lipat ukurannya</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Jendela kritis"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Jika perkiraan ini akurat, kita <strong>hanya perlu menunggu beberapa tahun</strong> sebelum seluruh koleksi kita akan banyak dimirror. Dengan demikian, dalam kata-kata Thomas Jefferson, \"ditempatkan di luar jangkauan kecelakaan.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Sayangnya, munculnya LLM, dan pelatihan mereka yang haus data, telah membuat banyak pemegang hak cipta bersikap defensif. Bahkan lebih dari sebelumnya. Banyak situs web membuatnya lebih sulit untuk di-scrape dan diarsipkan, tuntutan hukum bertebaran, dan sementara itu perpustakaan fisik dan arsip terus diabaikan."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Kami hanya dapat mengharapkan tren ini terus memburuk, dan banyak karya hilang jauh sebelum mereka memasuki domain publik."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Kami berada di ambang revolusi dalam pelestarian, tetapi <q>yang hilang tidak dapat dipulihkan.</q></strong> Kami memiliki jendela kritis sekitar 5-10 tahun di mana masih cukup mahal untuk mengoperasikan shadow library dan membuat banyak mirror di seluruh dunia, dan di mana akses belum sepenuhnya ditutup."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Jika kita dapat menjembatani jendela ini, maka kita benar-benar akan melestarikan pengetahuan dan budaya manusia untuk selamanya. Kita tidak boleh membiarkan waktu ini terbuang sia-sia. Kita tidak boleh membiarkan jendela kritis ini tertutup bagi kita."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Ayo kita mulai."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Akses eksklusif untuk perusahaan LLM ke koleksi buku non-fiksi Tiongkok terbesar di dunia"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versi Tiongkok 中文版</a>, <a %(news_ycombinator)s>Diskusikan di Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Arsip Anna memperoleh koleksi unik 7,5 juta / 350TB buku non-fiksi Tiongkok — lebih besar dari Library Genesis. Kami bersedia memberikan akses eksklusif kepada perusahaan LLM, dengan imbalan OCR berkualitas tinggi dan ekstraksi teks.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Ini adalah posting blog singkat. Kami mencari beberapa perusahaan atau institusi untuk membantu kami dengan OCR dan ekstraksi teks untuk koleksi besar yang kami peroleh, dengan imbalan akses awal eksklusif. Setelah periode embargo, kami tentu akan merilis seluruh koleksi."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Teks akademik berkualitas tinggi sangat berguna untuk pelatihan LLM. Meskipun koleksi kami berbahasa Tionghoa, ini seharusnya tetap berguna untuk melatih LLM berbahasa Inggris: model tampaknya mengenkripsi konsep dan pengetahuan terlepas dari bahasa sumbernya."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Untuk ini, teks perlu diekstraksi dari pemindaian. Apa yang didapat Arsip Anna dari ini? Pencarian teks lengkap dari buku-buku untuk penggunanya."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Karena tujuan kami sejalan dengan pengembang LLM, kami mencari kolaborator. Kami bersedia memberi Anda <strong>akses awal eksklusif ke koleksi ini dalam jumlah besar selama 1 tahun</strong>, jika Anda dapat melakukan OCR dan ekstraksi teks dengan benar. Jika Anda bersedia berbagi seluruh kode pipeline Anda dengan kami, kami bersedia menahan koleksi ini lebih lama."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Halaman contoh"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Untuk membuktikan kepada kami bahwa Anda memiliki pipeline yang baik, berikut adalah beberapa halaman contoh untuk memulai, dari sebuah buku tentang superkonduktor. Pipeline Anda harus dapat menangani matematika, tabel, grafik, catatan kaki, dan sebagainya dengan benar."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Kirim halaman yang telah diproses ke email kami. Jika terlihat bagus, kami akan mengirimkan lebih banyak secara pribadi, dan kami berharap Anda dapat dengan cepat menjalankan pipeline Anda pada halaman tersebut juga. Setelah kami puas, kami dapat membuat kesepakatan."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Koleksi"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Beberapa informasi lebih lanjut tentang koleksi ini. <a %(duxiu)s>Duxiu</a> adalah database besar buku yang dipindai, dibuat oleh <a %(chaoxing)s>SuperStar Digital Library Group</a>. Sebagian besar adalah buku akademik, dipindai untuk membuatnya tersedia secara digital bagi universitas dan perpustakaan. Untuk audiens berbahasa Inggris kami, <a %(library_princeton)s>Princeton</a> dan <a %(guides_lib_uw)s>University of Washington</a> memiliki ikhtisar yang baik. Ada juga artikel yang sangat baik yang memberikan latar belakang lebih lanjut: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cari di Arsip Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Buku-buku dari Duxiu telah lama dibajak di internet Tiongkok. Biasanya mereka dijual kurang dari satu dolar oleh penjual kembali. Mereka biasanya didistribusikan menggunakan setara Google Drive di Tiongkok, yang sering kali diretas untuk memungkinkan lebih banyak ruang penyimpanan. Beberapa detail teknis dapat ditemukan <a %(github_duty_machine)s>di sini</a> dan <a %(github_821_github_io)s>di sini</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Meskipun buku-buku tersebut telah didistribusikan secara semi-publik, cukup sulit untuk mendapatkannya dalam jumlah besar. Kami menempatkan ini tinggi dalam daftar TUGAS kami, dan mengalokasikan beberapa bulan kerja penuh waktu untuk itu. Namun, baru-baru ini seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberi tahu kami bahwa mereka telah melakukan semua pekerjaan ini — dengan biaya besar. Mereka berbagi seluruh koleksi dengan kami, tanpa mengharapkan imbalan apa pun, kecuali jaminan pelestarian jangka panjang. Benar-benar luar biasa. Mereka setuju untuk meminta bantuan dengan cara ini untuk mendapatkan koleksi yang di-OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Koleksi ini terdiri dari 7.543.702 file. Ini lebih banyak daripada non-fiksi Library Genesis (sekitar 5,3 juta). Ukuran total file sekitar 359TB (326TiB) dalam bentuk saat ini."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Kami terbuka untuk proposal dan ide lain. Hubungi kami saja. Lihat Arsip Anna untuk informasi lebih lanjut tentang koleksi kami, upaya pelestarian, dan bagaimana Anda dapat membantu. Terima kasih!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Peringatan: posting blog ini telah dihentikan. Kami memutuskan bahwa IPFS belum siap untuk waktu utama. Kami masih akan menautkan ke file di IPFS dari Arsip Anna jika memungkinkan, tetapi kami tidak akan menghostingnya sendiri lagi, juga tidak merekomendasikan orang lain untuk mirror menggunakan IPFS. Silakan lihat halaman Torrents kami jika Anda ingin membantu melestarikan koleksi kami."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Bantu seeding Z-Library di IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cara menjalankan perpustakaan bayangan: operasi di Arsip Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Tidak ada <q>AWS untuk amal bayangan,</q> jadi bagaimana kami menjalankan Arsip Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Saya menjalankan <a %(wikipedia_annas_archive)s>Arsip Anna</a>, mesin pencari nirlaba open-source terbesar di dunia untuk <a %(wikipedia_shadow_library)s>perpustakaan bayangan</a>, seperti Sci-Hub, Library Genesis, dan Z-Library. Tujuan kami adalah membuat pengetahuan dan budaya mudah diakses, dan pada akhirnya membangun komunitas orang-orang yang bersama-sama mengarsipkan dan melestarikan <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>semua buku di dunia</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Dalam artikel ini saya akan menunjukkan bagaimana kami menjalankan situs web ini, dan tantangan unik yang datang dengan mengoperasikan situs web dengan status hukum yang dipertanyakan, karena tidak ada “AWS untuk amal bayangan”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Juga lihat artikel saudara <a %(blog_how_to_become_a_pirate_archivist)s>Cara menjadi arsiparis bajak laut</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Token inovasi"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Mari kita mulai dengan tumpukan teknologi kami. Ini sengaja dibuat membosankan. Kami menggunakan Flask, MariaDB, dan ElasticSearch. Itu saja. Pencarian sebagian besar sudah menjadi masalah yang terpecahkan, dan kami tidak berniat untuk menciptakannya kembali. Selain itu, kami harus menghabiskan <a %(mcfunley)s>token inovasi</a> kami untuk hal lain: agar tidak ditutup oleh pihak berwenang."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Jadi seberapa legal atau ilegal sebenarnya Arsip Anna? Ini sebagian besar tergantung pada yurisdiksi hukum. Sebagian besar negara percaya pada beberapa bentuk hak cipta, yang berarti bahwa orang atau perusahaan diberikan monopoli eksklusif pada jenis karya tertentu untuk jangka waktu tertentu. Sebagai catatan, di Arsip Anna kami percaya bahwa meskipun ada beberapa manfaat, secara keseluruhan hak cipta adalah negatif bersih bagi masyarakat — tetapi itu adalah cerita untuk lain waktu."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Monopoli eksklusif pada karya tertentu ini berarti bahwa ilegal bagi siapa pun di luar monopoli ini untuk mendistribusikan karya tersebut secara langsung — termasuk kami. Namun, Arsip Anna adalah mesin pencari yang tidak mendistribusikan karya tersebut secara langsung (setidaknya tidak di situs web clearnet kami), jadi kami seharusnya baik-baik saja, bukan? Tidak persis. Di banyak yurisdiksi, tidak hanya ilegal untuk mendistribusikan karya berhak cipta, tetapi juga untuk menautkan ke tempat-tempat yang melakukannya. Contoh klasik dari ini adalah undang-undang DMCA di Amerika Serikat."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Itu adalah ujung spektrum yang paling ketat. Di ujung spektrum lainnya, secara teoritis bisa ada negara-negara tanpa undang-undang hak cipta sama sekali, tetapi ini sebenarnya tidak ada. Hampir setiap negara memiliki beberapa bentuk undang-undang hak cipta dalam buku hukum mereka. Penegakan adalah cerita yang berbeda. Ada banyak negara dengan pemerintah yang tidak peduli untuk menegakkan undang-undang hak cipta. Ada juga negara-negara di antara dua ekstrem ini, yang melarang mendistribusikan karya berhak cipta, tetapi tidak melarang menautkan ke karya tersebut."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Pertimbangan lain adalah di tingkat perusahaan. Jika sebuah perusahaan beroperasi di yurisdiksi yang tidak peduli tentang hak cipta, tetapi perusahaan itu sendiri tidak mau mengambil risiko, maka mereka mungkin akan menutup situs web Anda segera setelah ada yang mengeluh tentangnya."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Akhirnya, pertimbangan besar adalah pembayaran. Karena kami perlu tetap anonim, kami tidak dapat menggunakan metode pembayaran tradisional. Ini membuat kami hanya bisa menggunakan mata uang kripto, dan hanya sebagian kecil perusahaan yang mendukungnya (ada kartu debit virtual yang dibayar dengan kripto, tetapi seringkali tidak diterima)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arsitektur sistem"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Jadi katakanlah Anda menemukan beberapa perusahaan yang bersedia meng-host situs web Anda tanpa menutupnya — mari kita sebut mereka “penyedia yang mencintai kebebasan” 😄. Anda akan segera menemukan bahwa meng-host semuanya dengan mereka cukup mahal, jadi Anda mungkin ingin menemukan beberapa “penyedia murah” dan melakukan hosting sebenarnya di sana, dengan proxy melalui penyedia yang mencintai kebebasan. Jika Anda melakukannya dengan benar, penyedia murah tidak akan pernah tahu apa yang Anda host, dan tidak akan pernah menerima keluhan."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Dengan semua penyedia ini ada risiko mereka menutup Anda bagaimanapun, jadi Anda juga memerlukan redundansi. Kami memerlukan ini di semua tingkat tumpukan kami."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Satu perusahaan yang agak mencintai kebebasan yang telah menempatkan dirinya dalam posisi menarik adalah Cloudflare. Mereka telah <a %(blog_cloudflare)s>berargumen</a> bahwa mereka bukan penyedia hosting, tetapi utilitas, seperti ISP. Oleh karena itu, mereka tidak tunduk pada DMCA atau permintaan penghapusan lainnya, dan meneruskan permintaan apa pun ke penyedia hosting Anda yang sebenarnya. Mereka bahkan telah pergi ke pengadilan untuk melindungi struktur ini. Oleh karena itu, kami dapat menggunakannya sebagai lapisan caching dan perlindungan lainnya."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare tidak menerima pembayaran anonim, jadi kami hanya dapat menggunakan paket gratis mereka. Ini berarti bahwa kami tidak dapat menggunakan fitur load balancing atau failover mereka. Oleh karena itu, kami <a %(annas_archive_l255)s>mengimplementasikan ini sendiri</a> di tingkat domain. Saat halaman dimuat, browser akan memeriksa apakah domain saat ini masih tersedia, dan jika tidak, itu menulis ulang semua URL ke domain yang berbeda. Karena Cloudflare menyimpan banyak halaman, ini berarti bahwa pengguna dapat mendarat di domain utama kami, bahkan jika server proxy sedang down, dan kemudian pada klik berikutnya dipindahkan ke domain lain."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Kami juga masih memiliki kekhawatiran operasional normal untuk ditangani, seperti memantau kesehatan server, mencatat kesalahan backend dan frontend, dan sebagainya. Arsitektur failover kami memungkinkan lebih banyak ketahanan di bagian ini juga, misalnya dengan menjalankan satu set server yang sama sekali berbeda di salah satu domain. Kami bahkan dapat menjalankan versi kode dan datasets yang lebih lama di domain terpisah ini, jika ada bug kritis dalam versi utama yang tidak terdeteksi."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Kami juga dapat melindungi diri dari kemungkinan Cloudflare berbalik melawan kami, dengan menghapusnya dari salah satu domain, seperti domain terpisah ini. Berbagai kombinasi dari ide-ide ini mungkin dilakukan."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Alat"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Mari kita lihat alat apa yang kami gunakan untuk mencapai semua ini. Ini sangat berkembang saat kami menghadapi masalah baru dan menemukan solusi baru."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Server aplikasi: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Server proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Manajemen server: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Pengembangan: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hosting statis Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ada beberapa keputusan yang telah kami pertimbangkan berulang kali. Salah satunya adalah komunikasi antar server: kami dulu menggunakan Wireguard untuk ini, tetapi menemukan bahwa kadang-kadang berhenti mengirimkan data, atau hanya mengirimkan data dalam satu arah. Ini terjadi dengan beberapa pengaturan Wireguard yang berbeda yang kami coba, seperti <a %(github_costela_wesher)s>wesher</a> dan <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Kami juga mencoba menyalurkan port melalui SSH, menggunakan autossh dan sshuttle, tetapi mengalami <a %(github_sshuttle)s>masalah di sana</a> (meskipun masih belum jelas bagi saya apakah autossh mengalami masalah TCP-over-TCP atau tidak — rasanya seperti solusi yang tidak stabil bagi saya tetapi mungkin sebenarnya baik-baik saja?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Sebagai gantinya, kami kembali ke koneksi langsung antar server, menyembunyikan bahwa server berjalan pada penyedia murah menggunakan pemfilteran IP dengan UFW. Ini memiliki kelemahan bahwa Docker tidak bekerja dengan baik dengan UFW, kecuali Anda menggunakan <code>network_mode: \"host\"</code>. Semua ini sedikit lebih rentan terhadap kesalahan, karena Anda akan mengekspos server Anda ke internet hanya dengan sedikit kesalahan konfigurasi. Mungkin kita harus kembali ke autossh — umpan balik akan sangat diterima di sini."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Kami juga telah mempertimbangkan kembali antara Varnish vs. Nginx. Saat ini kami menyukai Varnish, tetapi memang memiliki keanehan dan kekurangan. Hal yang sama berlaku untuk Checkmk: kami tidak menyukainya, tetapi untuk saat ini berfungsi. Weblate cukup baik tetapi tidak luar biasa — saya kadang-kadang khawatir akan kehilangan data saya setiap kali mencoba menyinkronkannya dengan repo git kami. Flask secara keseluruhan baik, tetapi memiliki beberapa keanehan aneh yang memakan banyak waktu untuk di-debug, seperti mengonfigurasi domain khusus, atau masalah dengan integrasi SqlAlchemy-nya."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Sejauh ini alat lainnya sangat baik: kami tidak memiliki keluhan serius tentang MariaDB, ElasticSearch, Gitlab, Zulip, Docker, dan Tor. Semua ini memiliki beberapa masalah, tetapi tidak ada yang terlalu serius atau memakan waktu."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Ini adalah pengalaman menarik untuk belajar bagaimana mengatur mesin pencari shadow library yang kuat dan tangguh. Ada banyak detail lain yang akan dibagikan dalam postingan selanjutnya, jadi beri tahu saya apa yang ingin Anda pelajari lebih lanjut!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Seperti biasa, kami mencari donasi untuk mendukung pekerjaan ini, jadi pastikan untuk memeriksa halaman Donasi di Arsip Anna. Kami juga mencari jenis dukungan lain, seperti hibah, sponsor jangka panjang, penyedia pembayaran berisiko tinggi, mungkin bahkan iklan (yang berkelas!). Dan jika Anda ingin menyumbangkan waktu dan keterampilan Anda, kami selalu mencari pengembang, penerjemah, dan sebagainya. Terima kasih atas minat dan dukungan Anda."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna dan tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hai, saya Anna. Saya menciptakan <a %(wikipedia_annas_archive)s>Arsip Anna</a>, shadow library terbesar di dunia. Ini adalah blog pribadi saya, di mana saya dan rekan tim saya menulis tentang pembajakan, pelestarian digital, dan lainnya."

#, fuzzy
msgid "blog.index.text2"
msgstr "Terhubung dengan saya di <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Perhatikan bahwa situs web ini hanya blog. Kami hanya menyimpan kata-kata kami sendiri di sini. Tidak ada torrent atau file berhak cipta lainnya yang di-host atau ditautkan di sini."

#, fuzzy
msgid "blog.index.heading"
msgstr "Postingan blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 Miliar Pengambilan WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Menempatkan 5.998.794 buku di IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Peringatan: posting blog ini telah dihentikan. Kami memutuskan bahwa IPFS belum siap untuk waktu utama. Kami masih akan menautkan ke file di IPFS dari Arsip Anna jika memungkinkan, tetapi kami tidak akan menghostingnya sendiri lagi, juga tidak merekomendasikan orang lain untuk mirror menggunakan IPFS. Silakan lihat halaman Torrents kami jika Anda ingin membantu melestarikan koleksi kami."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Arsip Anna mengambil semua data WorldCat (koleksi metadata perpustakaan terbesar di dunia) untuk membuat daftar TODO buku yang perlu dilestarikan.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Setahun yang lalu, kami <a %(blog)s>memulai</a> untuk menjawab pertanyaan ini: <strong>Berapa persentase buku yang telah dilestarikan secara permanen oleh shadow library?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Setelah sebuah buku masuk ke dalam shadow library data terbuka seperti <a %(wikipedia_library_genesis)s>Library Genesis</a>, dan sekarang <a %(wikipedia_annas_archive)s>Arsip Anna</a>, buku tersebut akan dicerminkan di seluruh dunia (melalui torrent), sehingga praktis melestarikannya selamanya."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Untuk menjawab pertanyaan tentang persentase buku yang telah dilestarikan, kita perlu mengetahui penyebutnya: berapa banyak buku yang ada secara total? Dan idealnya kita tidak hanya memiliki angka, tetapi juga metadata yang sebenarnya. Kemudian kita tidak hanya dapat mencocokkannya dengan shadow library, tetapi juga <strong>membuat daftar TODO buku yang tersisa untuk dilestarikan!</strong> Kita bahkan bisa mulai bermimpi tentang upaya crowdsourcing untuk menelusuri daftar TODO ini."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Kami mengumpulkan data dari <a %(wikipedia_isbndb_com)s>ISBNdb</a>, dan mengunduh dataset <a %(openlibrary)s>Open Library</a>, tetapi hasilnya tidak memuaskan. Masalah utamanya adalah tidak banyak tumpang tindih ISBN. Lihat diagram Venn ini dari <a %(blog)s>posting blog kami</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Kami sangat terkejut dengan betapa sedikitnya tumpang tindih antara ISBNdb dan Open Library, yang keduanya secara bebas menyertakan data dari berbagai sumber, seperti pengumpulan data web dan catatan perpustakaan. Jika keduanya melakukan pekerjaan yang baik dalam menemukan sebagian besar ISBN yang ada di luar sana, lingkaran mereka pasti akan memiliki tumpang tindih yang substansial, atau salah satunya akan menjadi bagian dari yang lain. Ini membuat kami bertanya-tanya, berapa banyak buku yang jatuh <em>sepenuhnya di luar lingkaran ini</em>? Kami membutuhkan database yang lebih besar."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Saat itulah kami mengarahkan pandangan kami pada database buku terbesar di dunia: <a %(wikipedia_worldcat)s>WorldCat</a>. Ini adalah database milik organisasi nirlaba <a %(wikipedia_oclc)s>OCLC</a>, yang mengumpulkan catatan metadata dari perpustakaan di seluruh dunia, dengan imbalan memberikan akses kepada perpustakaan tersebut ke dataset lengkap, dan menampilkan mereka dalam hasil pencarian pengguna akhir."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Meskipun OCLC adalah organisasi nirlaba, model bisnis mereka mengharuskan melindungi database mereka. Nah, kami minta maaf, teman-teman di OCLC, kami akan membagikannya semuanya. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Selama setahun terakhir, kami dengan teliti mengumpulkan semua catatan WorldCat. Pada awalnya, kami mendapatkan keberuntungan. WorldCat baru saja meluncurkan desain ulang situs web mereka secara lengkap (pada Agustus 2022). Ini termasuk perombakan besar-besaran sistem backend mereka, memperkenalkan banyak kelemahan keamanan. Kami segera memanfaatkan kesempatan ini, dan berhasil mengumpulkan ratusan juta (!) catatan dalam beberapa hari saja."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Desain ulang WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Setelah itu, kelemahan keamanan perlahan-lahan diperbaiki satu per satu, hingga yang terakhir yang kami temukan diperbaiki sekitar sebulan yang lalu. Pada saat itu kami sudah memiliki hampir semua catatan, dan hanya mencari catatan dengan kualitas sedikit lebih tinggi. Jadi kami merasa sudah saatnya untuk merilisnya!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Mari kita lihat beberapa informasi dasar tentang data ini:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Kontainer Arsip Anna (AAC)</a>, yang pada dasarnya adalah <a %(jsonlines)s>JSON Lines</a> yang dikompresi dengan <a %(zstd)s>Zstandard</a>, ditambah beberapa semantik standar. Kontainer ini membungkus berbagai jenis catatan, berdasarkan pengumpulan data yang kami lakukan."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

msgid "dyn.buy_membership.error.unknown"
msgstr "Terjadi kesalahan yang tidak diketahui. Silakan hubungi kami di %(email)s dengan melampirkan tangkapan layar."

msgid "dyn.buy_membership.error.minimum"
msgstr "Koin ini memiliki jumlah minimum yang lebih tinggi dari biasanya. Silakan pilih durasi yang berbeda atau koin yang berbeda."

msgid "dyn.buy_membership.error.try_again"
msgstr "Permintaan tidak dapat dijalankan. Mohon coba kembali dalam beberapa menit, apabila masih terjadi kendala harap hubungi kami di %(email)s dengan disertai screenshot dan deskripsi kendala."

msgid "dyn.buy_membership.error.wait"
msgstr "Terjadi kesalahan dalam proses pembayaran. Mohon menunggu beberapa saat lagi untuk mencoba kembali. Bila kendala masih terjadi setelah lebih dari 24 jam, mohon hubungi kami di %(email)s dengan menyertakan screenshot dan keterangan lengkap."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "komentar tersembunyi"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Masalah file: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versi yang lebih baik"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Apakah Anda ingin melaporkan pengguna ini karena perilaku kasar atau tidak pantas?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Laporkan penyalahgunaan"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Penyalahgunaan dilaporkan:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Anda melaporkan pengguna ini karena penyalahgunaan."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Balas"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Silakan <a %(a_login)s>masuk</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Anda telah meninggalkan komentar. Mungkin butuh waktu satu menit untuk muncul."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s halaman yang terpengaruh"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Tidak terlihat di Libgen.rs dalam kategori Non-Fiksi"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Tidak terlihat di Libgen.rs dalam kategori Fiksi"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Tidak terlihat di Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Diberi tanda rusak (red: broken) di Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Hilang dari Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Ditandai sebagai “spam” di Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Ditandai sebagai “file buruk” di Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Tidak semua halaman dapat dikonversi ke PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Menjalankan exiftool gagal pada file ini"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Buku (tak terkategori)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Buku (nonfiksi)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Buku (fiksi)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artikel jurnal"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Dokumen standar"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Majalah"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komik"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Nilai Musik"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Buku Audio"

msgid "common.md5_content_type_mapping.other"
msgstr "Lainya"

msgid "common.access_types_mapping.aa_download"
msgstr "Unduhan dari Peladen Mitra"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Unduhan eksternal"

msgid "common.access_types_mapping.external_borrow"
msgstr "Pinjam dari sumber eksternal"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Pinjam dari sumber eksternal (cetak dinonaktifkan)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Jelajahi metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Terkandung dalam torrent"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Unggahan ke AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Indeks eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadata Ceko"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Perpustakaan Negara Rusia"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Judul"

msgid "common.specific_search_fields.author"
msgstr "Penulis"

msgid "common.specific_search_fields.publisher"
msgstr "Penerbit"

msgid "common.specific_search_fields.edition_varia"
msgstr "Penyunting"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Tahun diterbitkan"

msgid "common.specific_search_fields.original_filename"
msgstr "Nama dokumen asli"

msgid "common.specific_search_fields.description_comments"
msgstr "Deskripsi dan komentar metadata"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Server Mitra untuk sementara tidak tersedia untuk mengunduh dokumen ini."

msgid "common.md5.servers.fast_partner"
msgstr "Unduhan jalur cepat rekan #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(direkomendasikan)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(tanpa verifikasi browser atau daftar tunggu)"

msgid "common.md5.servers.slow_partner"
msgstr "Server Mitra Kecepatan Lambat #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(sedikit lebih cepat tetapi dengan daftar tunggu)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(tidak ada daftar tunggu, tetapi bisa sangat lambat)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiksi"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiksi"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klik juga “GET” di atas)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(klik “GET” di atas)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "iklan mereka diketahui mengandung perangkat lunak berbahaya, jadi gunakan pemblokir iklan atau jangan klik iklan"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(File Nexus/STC bisa tidak dapat diandalkan untuk diunduh)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library di Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(membutuhkan browser TOR)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Pinjam dari halaman Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(cetakan dimatikan hanya untuk donatur saja)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI yang bersangkutan mungkin tidak tersedia di Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "koleksi"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Unduhan lewat torrent secara besar"

msgid "page.md5.box.download.experts_only"
msgstr "(Hanya ahli saja)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Cari di Arsip Anna dengan ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Cari di beberapa sumber data lain dengan ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Temukan dokumen asli di ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Cari di Arsip Anna untuk ID Open Library"

msgid "page.md5.box.download.original_openlib"
msgstr "Cari dokumen asli di Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Cari nomor OCLC (WorldCat) di Arsip Anna"

msgid "page.md5.box.download.original_oclc"
msgstr "Temukan dokumen asli di WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Cari DuXiu SSID di Arsip Anna"

msgid "page.md5.box.download.original_duxiu"
msgstr "Cari di DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Cari nomor CADAL SSNO di Arsip Anna"

msgid "page.md5.box.download.original_cadal"
msgstr "Temukan dokumen asli di CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Cari nomor DuXiu (DXID) di Arsip Anna"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Indeks eBook EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Arsip Anna 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(Tidak memerlukan verifikasi browser)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata Ceko %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

msgid "page.md5.box.descr_title"
msgstr "deskripsi"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nama file alternatif"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Judul alternatif"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Penulis alternatif"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Penerbit alternatif"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edisi alternatif"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Ekstensi alternatif"

msgid "page.md5.box.metadata_comments_title"
msgstr "Komentar metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Deskripsi alternatif"

msgid "page.md5.box.date_open_sourced_title"
msgstr "tanggal sumber terbuka"

msgid "page.md5.header.scihub"
msgstr "Dokumen Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Dokumen Internet Archive Controlled Digital Lending “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Ini merupakan record dari dokumen Internet Archive, bukan merupakan dokumen unduhan. Kamu dapat mencoba meminjam buku melalui (link di bawah ini), atau gunakan URL saat <a %(a_request)s>meminta dokumen</a>."

msgid "page.md5.header.consider_upload"
msgstr "Bila anda memiliki dokumen ini dan belum tersedia di Arsip Anna, mohon kiranya bersedia <a %(a_request)s>mengunggahnya</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Catatan metadata ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Catatan metadata Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Catatan metadata dokumen OCLC (WorldCat) nomor %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Catatan metadata DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Catatan metadata CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Catatan metadata MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Catatan metadata Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Ini merupakan catatan metadata, bukan dokumen yang dapat diunduh. Kamu dapat menggunakan URL saat<a %(a_request)s>meminta dokumen</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata dari catatan terkait"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Perbaiki metadata di Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Peringatan: beberapa catatan terkait:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Perbaiki metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Laporkan kualitas file"

msgid "page.search.results.download_time"
msgstr "Waktu unduh"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Halaman Web:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Pencarian di Arsip Anna untuk \"%(name)s\""

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Penjelajah Kode:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Lihat di Penjelajah Kode “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Baca lebih lanjut…"

msgid "page.md5.tabs.downloads"
msgstr "Total undihan (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Total Pinjaman (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Jelajahi metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentar (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Total lists (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Total statistik (%(count)s)"

msgid "common.tech_details"
msgstr "Detail teknis (dalam bahasa Inggris)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Berkas ini bermasalah dan telah disembunyikan dari perpustakaan sumber.</span> Terkadang karena ada permintaan pemegang hak cipta, terkadang karena terdapat berkas lain yang lebih baik, namun umumnya karena terdapat masalah pada berkasnya sendiri. Berkas ini mungkin masih layak unduh, namun kami sarankan mencari berkas alternatif. Lebih lanjut:"

msgid "page.md5.box.download.better_file"
msgstr "Versi dokumen yang lebih baik kemungkinan tersedia di %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Jika Anda tetap ingin mengunduh berkas ini, pastikan Anda membukanya dengan menggunakan peranti lunak yang terpercaya dan termutakhir."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Unduhan cepat"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Unduhan jalur cepat</strong> Jadilah <a %(a_membership)s>member</a> untuk dukungan jangka panjang pelestarian buku, jurnal dkk. Dan dapatkan akses unduhan jalur cepat. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Jika Anda berdonasi bulan ini, Anda mendapatkan <strong>dua kali</strong> jumlah unduhan cepat."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Unduhan jalur cepat</strong> yang anda memiliki tersisa %(remaining)s hari ini. Terima kasih telah menjadi member kami! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Jalur unduhan cepat</strong> Kuota unduhan kamu telah habis hari ini."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Kamu telah mengunduh berkas ini melalui <strong>🚀 Unduhan jalur cepat</strong> . Link masih dapat digunakan kembali untuk sementara waktu."

msgid "page.md5.box.download.option"
msgstr "Opsi #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(tidak ada pengalihan)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(buka di penampil)"

msgid "layout.index.header.banner.refer"
msgstr "Bagikan ke teman dan dapatkan %(percentage)s%% bonus unduhan jalur cepat buat kamu dan temanmu!"

msgid "layout.index.header.learn_more"
msgstr "Pelajari lebih lanjut…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Unduhan jalur lambat"

msgid "page.md5.box.download.trusted_partners"
msgstr "Dari mitra terpercaya."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Informasi lebih lanjut di <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kemungkinan perlu <a %(a_browser)s>verifikasi browser</a> — unduhan tak terbatas!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Setelah mengunduh:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Buka di penampil kami"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "tampilkan unduhan eksternal"

msgid "page.md5.box.download.header_external"
msgstr "Unduhan eksternal"

msgid "page.md5.box.download.no_found"
msgstr "Tidak ada unduhan yang ditemukan."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Semua mirror melayani file yang sama, dan harusnya aman untuk digunakan. Walau begitu, selalu berhati-hatilah saat mengunduh file dari internet. Misalnya, pastikan untuk selalu memperbarui perangkat Anda."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Untuk file berukuran besar, kami merekomendasikan menggunakan pengelola unduhan untuk mencegah gangguan."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Pengelola unduhan yang direkomendasikan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Anda akan memerlukan pembaca ebook atau PDF untuk membuka file, tergantung pada format file."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Pembaca ebook yang direkomendasikan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Penampil online Arsip Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Gunakan alat online untuk mengonversi antar format."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Alat konversi yang direkomendasikan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Anda dapat mengirim file PDF dan EPUB ke Kindle atau Kobo eReader Anda."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Alat yang direkomendasikan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon’s “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz’s “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Dukung penulis dan perpustakaan"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Jika Anda menyukai ini dan mampu membelinya, pertimbangkan untuk membeli yang asli, atau mendukung penulis secara langsung."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Jika ini tersedia di perpustakaan lokal Anda, pertimbangkan untuk meminjamnya secara gratis di sana."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Kualitas file"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Bantu komunitas dengan melaporkan kualitas file ini! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Laporkan masalah file (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Kualitas file bagus (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Tambahkan komentar (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Apa yang salah dengan file ini?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Silakan gunakan <a %(a_copyright)s>formulir klaim DMCA / Hak Cipta</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Deskripsikan masalah (wajib)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Deskripsi masalah"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 dari versi yang lebih baik dari file ini (jika ada)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Isi ini jika ada file lain yang sangat mirip dengan file ini (edisi yang sama, ekstensi file yang sama jika Anda dapat menemukannya), yang seharusnya digunakan orang daripada file ini. Jika Anda mengetahui versi yang lebih baik dari file ini di luar Arsip Anna, silakan <a %(a_upload)s>unggah</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Anda bisa mendapatkan md5 dari URL, misalnya"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Kirim laporan"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Pelajari cara <a %(a_metadata)s>meningkatkan metadata</a> untuk file ini sendiri."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Terima kasih telah mengirimkan laporan Anda. Laporan tersebut akan ditampilkan di halaman ini, serta ditinjau secara manual oleh Anna (sampai kami memiliki sistem moderasi yang tepat)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Terjadi kesalahan. Silakan muat ulang halaman dan coba lagi."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Jika file ini memiliki kualitas yang baik, Anda dapat mendiskusikan apa saja tentangnya di sini! Jika tidak, silakan gunakan tombol “Laporkan masalah file”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Saya menyukai buku ini!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Tinggalkan komentar"

msgid "common.english_only"
msgstr "Teks berlanjut di bawah dalam bahasa Inggris."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total unduhan: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“file MD5” adalah hash yang dihitung dari konten file, dan cukup unik berdasarkan konten tersebut. Semua perpustakaan bayangan yang telah kami indeks di sini terutama menggunakan MD5 untuk mengidentifikasi file."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Sebuah file mungkin muncul di beberapa perpustakaan bayangan. Untuk informasi tentang berbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Ini adalah file yang dikelola oleh perpustakaan <a %(a_ia)s>IA’s Controlled Digital Lending</a>, dan diindeks oleh Arsip Anna untuk pencarian. Untuk informasi tentang berbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Untuk informasi tentang file ini, lihat <a %(a_href)s>file JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Masalah memuat halaman ini"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Silakan segarkan untuk mencoba lagi. <a %(a_contact)s>Hubungi kami</a> jika masalah berlanjut selama beberapa jam."

msgid "page.md5.invalid.header"
msgstr "Tidak ditemukan"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” tidak ditemukan di database kami."

msgid "page.login.title"
msgstr "Masuk/Daftar"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verifikasi browser"

msgid "page.login.text1"
msgstr "Untuk mencegah spam dari robot membuat banyak akun, kami memerlukan verifikasi browser kamu terlebih dahulu."

#, fuzzy
msgid "page.login.text2"
msgstr "Jika Anda terjebak dalam loop tak terbatas, kami merekomendasikan untuk menginstal <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Hal tersebut juga dapat membantu mematikan pemblokir iklan dan ekstensi browser yang lain."

#, fuzzy
msgid "page.codes.title"
msgstr "Kode"

#, fuzzy
msgid "page.codes.heading"
msgstr "Penjelajah Kode"

#, fuzzy
msgid "page.codes.intro"
msgstr "Jelajahi kode yang ditandai pada catatan, berdasarkan awalan. Kolom “catatan” menunjukkan jumlah catatan yang ditandai dengan kode dengan awalan yang diberikan, seperti yang terlihat di mesin pencari (termasuk catatan metadata saja). Kolom “kode” menunjukkan berapa banyak kode sebenarnya yang memiliki awalan yang diberikan."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Halaman ini mungkin memerlukan waktu untuk dihasilkan, itulah sebabnya memerlukan captcha Cloudflare. <a %(a_donate)s>Anggota</a> dapat melewati captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Harap jangan mengikis halaman ini. Sebagai gantinya, kami merekomendasikan <a %(a_import)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami, dan menjalankan <a %(a_software)s>kode sumber terbuka</a> kami. Data mentah dapat dijelajahi secara manual melalui file JSON seperti <a %(a_json_file)s>yang ini</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Awalan"

#, fuzzy
msgid "common.form.go"
msgstr "Pergi"

#, fuzzy
msgid "common.form.reset"
msgstr "Atur Ulang"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Cari Arsip Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Peringatan: kode memiliki karakter Unicode yang salah, dan mungkin berperilaku tidak benar dalam berbagai situasi. Biner mentah dapat didekode dari representasi base64 di URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Awalan kode yang dikenal “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Awalan"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Deskripsi"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL untuk kode tertentu"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” akan digantikan dengan nilai kode"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL Generik"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s catatan yang cocok dengan “%(prefix_label)s”"
msgstr[1] ""

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL untuk kode spesifik: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Lebih lanjut…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kode yang dimulai dengan “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks dari"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "catatan"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kode"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Kurang dari %(count)s catatan"

msgid "page.contact.dmca.form"
msgstr "Untuk DMCA/klaim hak cipta, gunakan <a %(a_copyright)s>form ini</a>."

msgid "page.contact.dmca.delete"
msgstr "Kami akan secara otomatis menghapus klaim hak cipta bila menggunakan metode lainya."

msgid "page.contact.checkboxes.text1"
msgstr "Kami menerima masukan dan pertanyaan secara terbuka!"

msgid "page.contact.checkboxes.text2"
msgstr "Meski demikian, dikarnakan banyaknya spam dan email tidak jelas, mohon untuk memeriksa kotak masuk email anda untuk konfirmasi bahwa anda memahami ketentuan dalam menghubungi kami."

msgid "page.contact.checkboxes.copyright"
msgstr "Klaim hak cipta melaluiemail ini tidak akan ditanggapi: mohon gunakan form yang tersedia."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Server mitra tidak tersedia karena penutupan hosting. Mereka akan segera aktif kembali."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Keanggotaan akan diperpanjang sesuai."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Jangan mengirimkan email kepada kami terkait <a %(a_request)s>pengajuan permintaan buku</a><br>atau <a %(a_upload)s>menggungah</a> dokumen ukuran kecil (<10kb)."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Saat menanyakan tentang akun atau donasi, tambahkan ID akun Anda, tangkapan layar, tanda terima, sebanyak mungkin informasi. Kami hanya memeriksa email kami setiap 1-2 minggu, jadi tidak menyertakan informasi ini akan menunda penyelesaian apa pun."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Tampilkan email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulir klaim DMCA / Hak Cipta"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Jika Anda memiliki klaim DMCA atau hak cipta lainnya, harap isi formulir ini seakurat mungkin. Jika Anda mengalami masalah, silakan hubungi kami di alamat DMCA khusus kami: %(email)s. Perhatikan bahwa klaim yang dikirimkan melalui email ke alamat ini tidak akan diproses, alamat ini hanya untuk pertanyaan. Harap gunakan formulir di bawah ini untuk mengajukan klaim Anda."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL di Arsip Anna (wajib). Satu per baris. Harap hanya sertakan URL yang menggambarkan edisi buku yang sama persis. Jika Anda ingin mengajukan klaim untuk beberapa buku atau beberapa edisi, harap kirimkan formulir ini beberapa kali."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Klaim yang menggabungkan beberapa buku atau edisi akan ditolak."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Nama Anda (wajib)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Alamat (wajib)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Nomor telepon (wajib)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (wajib)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Deskripsi jelas tentang materi sumber (wajib)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN dari materi sumber (jika ada). Satu per baris. Harap hanya sertakan yang benar-benar sesuai dengan edisi yang Anda laporkan klaim hak ciptanya."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>URL Open Library</a> dari materi sumber, satu per baris. Harap luangkan waktu untuk mencari materi sumber Anda di Open Library. Ini akan membantu kami memverifikasi klaim Anda."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL ke materi sumber, satu per baris (wajib). Harap sertakan sebanyak mungkin, untuk membantu kami memverifikasi klaim Anda (misalnya Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Pernyataan dan tanda tangan (wajib)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Kirim klaim"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Terima kasih telah mengirimkan klaim hak cipta Anda. Kami akan meninjaunya secepat mungkin. Harap muat ulang halaman untuk mengajukan klaim lainnya."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Terjadi kesalahan. Harap muat ulang halaman dan coba lagi."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Jika Anda tertarik untuk mencerminkan dataset ini untuk <a %(a_archival)s>arsip</a> atau tujuan <a %(a_llm)s>pelatihan LLM</a>, silakan hubungi kami."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Misi kami adalah mengarsipkan semua buku di dunia (serta makalah, majalah, dll), dan membuatnya dapat diakses secara luas. Kami percaya bahwa semua buku harus dicerminkan secara luas, untuk memastikan redundansi dan ketahanan. Inilah sebabnya kami mengumpulkan file dari berbagai sumber. Beberapa sumber sepenuhnya terbuka dan dapat dicerminkan secara massal (seperti Sci-Hub). Yang lain tertutup dan protektif, jadi kami mencoba mengikisnya untuk “membebaskan” buku-buku mereka. Yang lainnya berada di antara keduanya."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Semua data kami dapat <a %(a_torrents)s>ditorrent</a>, dan semua metadata kami dapat <a %(a_anna_software)s>dihasilkan</a> atau <a %(a_elasticsearch)s>diunduh</a> sebagai database ElasticSearch dan MariaDB. Data mentah dapat dieksplorasi secara manual melalui file JSON seperti <a %(a_dbrecord)s>ini</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Ikhtisar"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Di bawah ini adalah ikhtisar cepat tentang sumber file di Arsip Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Ukuran"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% dicerminkan oleh AA / torrent tersedia"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Persentase jumlah file"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Terakhir diperbarui"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiksi dan Fiksi"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s file"
msgstr[1] ""

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Melalui Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: dibekukan sejak 2021; sebagian besar tersedia melalui torrent"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: penambahan kecil sejak itu</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Tidak termasuk “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrent fiksi tertinggal (meskipun ID ~4-6M tidak ditorrent karena tumpang tindih dengan torrent Zlib kami)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Koleksi “Chinese” di Z-Library tampaknya sama dengan koleksi DuXiu kami, tetapi dengan MD5 yang berbeda. Kami mengecualikan file-file ini dari torrent untuk menghindari duplikasi, tetapi tetap menampilkannya dalam indeks pencarian kami."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dari file dapat dicari."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Mengecualikan duplikat"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Karena perpustakaan bayangan sering menyinkronkan data satu sama lain, ada tumpang tindih yang signifikan antara perpustakaan. Itulah mengapa jumlahnya tidak sesuai dengan total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Persentase “mirrored and seeded by Anna’s Archive” menunjukkan berapa banyak file yang kami mirror sendiri. Kami menyebarkan file-file tersebut secara massal melalui torrent, dan membuatnya tersedia untuk diunduh langsung melalui situs web mitra."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Perpustakaan sumber"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Beberapa perpustakaan sumber mempromosikan berbagi data mereka secara massal melalui torrent, sementara yang lain tidak dengan mudah berbagi koleksi mereka. Dalam kasus yang terakhir, Arsip Anna mencoba mengikis koleksi mereka, dan membuatnya tersedia (lihat halaman <a %(a_torrents)s>Torrents</a> kami). Ada juga situasi di antara, misalnya, di mana perpustakaan sumber bersedia berbagi, tetapi tidak memiliki sumber daya untuk melakukannya. Dalam kasus tersebut, kami juga mencoba membantu."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Di bawah ini adalah gambaran umum tentang bagaimana kami berinteraksi dengan berbagai perpustakaan sumber."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Berkas"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dump database <a %(dbdumps)s>HTTP harian</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrent otomatis untuk <a %(nonfiction)s>Non-Fiksi</a> dan <a %(fiction)s>Fiksi</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(covers)s>torrent sampul buku</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub telah membekukan file baru sejak 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dump metadata tersedia <a %(scihub1)s>di sini</a> dan <a %(scihub2)s>di sini</a>, serta sebagai bagian dari <a %(libgenli)s>database Libgen.li</a> (yang kami gunakan)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrent data tersedia <a %(scihub1)s>di sini</a>, <a %(scihub2)s>di sini</a>, dan <a %(libgenli)s>di sini</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Beberapa file baru <a %(libgenrs)s>sedang</a> <a %(libgenli)s>ditambahkan</a> ke “scimag” Libgen, tetapi tidak cukup untuk membuat torrent baru"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dump database HTTP</a> triwulanan"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrent Non-Fiksi dibagikan dengan Libgen.rs (dan dicerminkan <a %(libgenli)s>di sini</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Arsip Anna dan Libgen.li secara kolaboratif mengelola koleksi <a %(comics)s>buku komik</a>, <a %(magazines)s>majalah</a>, <a %(standarts)s>dokumen standar</a>, dan <a %(fiction)s>fiksi (berbeda dari Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Koleksi “fiction_rus” mereka (fiksi Rusia) tidak memiliki torrent khusus, tetapi tercakup oleh torrent dari pihak lain, dan kami menyimpan <a %(fiction_rus)s>mirror</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Arsip Anna dan Z-Library secara kolaboratif mengelola koleksi <a %(metadata)s>metadata Z-Library</a> dan <a %(files)s>file Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Beberapa metadata tersedia melalui <a %(openlib)s>dump database Open Library</a>, tetapi tidak mencakup seluruh koleksi IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Tidak ada dump metadata yang mudah diakses untuk seluruh koleksi mereka"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(ia)s>metadata IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s File hanya tersedia untuk dipinjam secara terbatas, dengan berbagai pembatasan akses"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(ia)s>file IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Berbagai database metadata tersebar di internet Tiongkok; meskipun seringkali merupakan database berbayar"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Tidak ada dump metadata yang mudah diakses untuk seluruh koleksi mereka."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(duxiu)s>metadata DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Berbagai basis data file tersebar di internet Tiongkok; meskipun seringkali basis data berbayar"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Sebagian besar file hanya dapat diakses menggunakan akun BaiduYun premium; kecepatan unduh lambat."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(duxiu)s>file DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Berbagai sumber yang lebih kecil atau satu kali. Kami mendorong orang untuk mengunggah ke perpustakaan bayangan lainnya terlebih dahulu, tetapi terkadang orang memiliki koleksi yang terlalu besar untuk diurutkan oleh orang lain, meskipun tidak cukup besar untuk mendapatkan kategori mereka sendiri."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Sumber metadata saja"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Kami juga memperkaya koleksi kami dengan sumber metadata saja, yang dapat kami cocokkan dengan berkas, misalnya menggunakan nomor ISBN atau bidang lainnya. Di bawah ini adalah gambaran umum dari sumber-sumber tersebut. Sekali lagi, beberapa dari sumber ini sepenuhnya terbuka, sementara yang lain harus kami kikis."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Inspirasi kami untuk mengumpulkan metadata adalah tujuan Aaron Swartz untuk “satu halaman web untuk setiap buku yang pernah diterbitkan”, yang mana ia menciptakan <a %(a_openlib)s>Open Library</a>. Proyek tersebut telah berjalan dengan baik, tetapi posisi unik kami memungkinkan kami mendapatkan metadata yang tidak bisa mereka dapatkan. Inspirasi lainnya adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku yang ada di dunia</a>, sehingga kami dapat menghitung berapa banyak buku yang masih harus kami selamatkan."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Perhatikan bahwa dalam pencarian metadata, kami menampilkan catatan asli. Kami tidak melakukan penggabungan catatan."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Terakhir diperbarui"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Dump <a %(dbdumps)s>basis data</a> bulanan"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Tidak tersedia langsung dalam jumlah besar, dilindungi dari scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Basis data terpadu"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Kami menggabungkan semua sumber di atas ke dalam satu basis data terpadu yang kami gunakan untuk melayani situs web ini. Basis data terpadu ini tidak tersedia secara langsung, tetapi karena Arsip Anna sepenuhnya open source, basis data ini dapat dengan cukup mudah <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>diunduh</a> sebagai basis data ElasticSearch dan MariaDB. Skrip di halaman tersebut akan secara otomatis mengunduh semua metadata yang diperlukan dari sumber-sumber yang disebutkan di atas."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Jika Anda ingin menjelajahi data kami sebelum menjalankan skrip tersebut secara lokal, Anda dapat melihat berkas JSON kami, yang menghubungkan lebih lanjut ke berkas JSON lainnya. <a %(a_json)s>Berkas ini</a> adalah titik awal yang baik."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Diadaptasi dari <a %(a_href)s>posting blog</a> kami."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> adalah database besar buku yang dipindai, dibuat oleh <a %(superstar_link)s>SuperStar Digital Library Group</a>. Sebagian besar adalah buku akademik, dipindai untuk membuatnya tersedia secara digital bagi universitas dan perpustakaan. Untuk audiens berbahasa Inggris kami, <a %(princeton_link)s>Princeton</a> dan <a %(uw_link)s>University of Washington</a> memiliki ikhtisar yang baik. Ada juga artikel yang sangat baik yang memberikan lebih banyak latar belakang: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Buku-buku dari Duxiu telah lama dibajak di internet Tiongkok. Biasanya mereka dijual kurang dari satu dolar oleh penjual kembali. Mereka biasanya didistribusikan menggunakan setara Google Drive di Tiongkok, yang sering kali diretas untuk memungkinkan lebih banyak ruang penyimpanan. Beberapa detail teknis dapat ditemukan <a %(link1)s>di sini</a> dan <a %(link2)s>di sini</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Meskipun buku-buku tersebut telah didistribusikan secara semi-publik, cukup sulit untuk mendapatkannya dalam jumlah besar. Kami menempatkan ini tinggi di daftar TODO kami, dan mengalokasikan beberapa bulan kerja penuh waktu untuk itu. Namun, pada akhir 2023 seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberi tahu kami bahwa mereka telah melakukan semua pekerjaan ini — dengan biaya besar. Mereka membagikan koleksi lengkap dengan kami, tanpa mengharapkan imbalan apa pun, kecuali jaminan pelestarian jangka panjang. Benar-benar luar biasa."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Sumber Daya"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total file: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Total ukuran file: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "File yang dicerminkan oleh Arsip Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Terakhir diperbarui: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrent oleh Arsip Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Contoh catatan di Arsip Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Posting blog kami tentang data ini"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skrip untuk mengimpor metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Format Kontainer Arsip Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Informasi lebih lanjut dari sukarelawan kami (catatan mentah):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dataset ini terkait erat dengan <a %(a_datasets_openlib)s>dataset Open Library</a>. Ini berisi pengikisan semua metadata dan sebagian besar file dari Perpustakaan Peminjaman Digital Terkendali IA. Pembaruan dirilis dalam <a %(a_aac)s>format Kontainer Arsip Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Catatan ini dirujuk langsung dari dataset Open Library, tetapi juga berisi catatan yang tidak ada di Open Library. Kami juga memiliki sejumlah file data yang dikumpulkan oleh anggota komunitas selama bertahun-tahun."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Koleksi ini terdiri dari dua bagian. Anda memerlukan kedua bagian untuk mendapatkan semua data (kecuali torrent yang digantikan, yang dicoret di halaman torrent)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "rilisan pertama kami, sebelum kami menstandarisasi pada format <a %(a_aac)s>Kontainer Arsip Anna (AAC)</a>. Berisi metadata (dalam format json dan xml), pdf (dari sistem peminjaman digital acsm dan lcpdf), dan gambar sampul."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "rilisan baru bertahap, menggunakan AAC. Hanya berisi metadata dengan stempel waktu setelah 2023-01-01, karena sisanya sudah tercakup oleh \"ia\". Juga semua file pdf, kali ini dari sistem peminjaman acsm dan \"bookreader\" (pembaca web IA). Meskipun namanya tidak sepenuhnya tepat, kami tetap mengisi file bookreader ke dalam koleksi ia2_acsmpdf_files, karena mereka saling eksklusif."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Situs utama %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Perpustakaan Peminjaman Digital"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dokumentasi metadata (sebagian besar bidang)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informasi negara ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Badan ISBN Internasional secara teratur merilis rentang yang telah dialokasikan ke badan ISBN nasional. Dari sini kita dapat mengetahui negara, wilayah, atau kelompok bahasa yang dimiliki ISBN ini. Saat ini kami menggunakan data ini secara tidak langsung, melalui perpustakaan Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Sumber Daya"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Terakhir diperbarui: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Situs web ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Untuk latar belakang dari berbagai cabang Library Genesis, lihat halaman untuk <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li berisi sebagian besar konten dan metadata yang sama dengan Libgen.rs, tetapi memiliki beberapa koleksi tambahan, yaitu komik, majalah, dan dokumen standar. Ini juga telah mengintegrasikan <a %(a_scihub)s>Sci-Hub</a> ke dalam metadata dan mesin pencariannya, yang kami gunakan untuk basis data kami."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadata untuk perpustakaan ini tersedia secara gratis <a %(a_libgen_li)s>di libgen.li</a>. Namun, server ini lambat dan tidak mendukung melanjutkan koneksi yang terputus. File yang sama juga tersedia di <a %(a_ftp)s>server FTP</a>, yang bekerja lebih baik."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrent tersedia untuk sebagian besar konten tambahan, terutama torrent untuk komik, majalah, dan dokumen standar telah dirilis dalam kolaborasi dengan Arsip Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Koleksi fiksi memiliki torrent sendiri (berbeda dari <a %(a_href)s>Libgen.rs</a>) mulai dari %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Menurut administrator Libgen.li, koleksi “fiction_rus” (fiksi Rusia) harus tercakup oleh torrent yang dirilis secara teratur dari <a %(a_booktracker)s>booktracker.org</a>, terutama torrent <a %(a_flibusta)s>flibusta</a> dan <a %(a_librusec)s>lib.rus.ec</a> (yang kami mirror <a %(a_torrents)s>di sini</a>, meskipun kami belum menetapkan torrent mana yang sesuai dengan file mana)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistik untuk semua koleksi dapat ditemukan <a %(a_href)s>di situs web libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fiksi juga tampaknya telah menyimpang, tetapi tanpa torrent baru. Tampaknya ini telah terjadi sejak awal 2022, meskipun kami belum memverifikasinya."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Rentang tertentu tanpa torrent (seperti rentang fiksi f_3463000 hingga f_4260000) kemungkinan adalah file Z-Library (atau duplikat lainnya), meskipun kami mungkin ingin melakukan deduplikasi dan membuat torrent untuk file unik lgli dalam rentang ini."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Perhatikan bahwa file torrent yang merujuk ke “libgen.is” secara eksplisit adalah mirror dari <a %(a_libgen)s>Libgen.rs</a> (“.is” adalah domain berbeda yang digunakan oleh Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Sumber daya yang berguna dalam menggunakan metadata adalah <a %(a_href)s>halaman ini</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrent fiksi di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrent komik di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrent majalah di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrent dokumen standar di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrent fiksi Rusia di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata melalui FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informasi bidang metadata"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror dari torrent lain (dan torrent fiksi dan komik unik)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum diskusi"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Posting blog kami tentang rilis buku komik"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Kisah singkat tentang berbagai cabang Library Genesis (atau “Libgen”) adalah bahwa seiring waktu, orang-orang yang terlibat dengan Library Genesis mengalami perpecahan, dan pergi ke jalan mereka masing-masing."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Versi “.fun” dibuat oleh pendiri aslinya. Ini sedang diperbarui untuk versi baru yang lebih terdistribusi."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Versi “.rs” memiliki data yang sangat mirip, dan paling konsisten merilis koleksi mereka dalam torrent massal. Ini kira-kira dibagi menjadi bagian “fiksi” dan “non-fiksi”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Awalnya di “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Versi <a %(a_li)s>“.li”</a> memiliki koleksi komik yang sangat besar, serta konten lain, yang belum (belum) tersedia untuk diunduh massal melalui torrent. Ini memiliki koleksi torrent terpisah dari buku fiksi, dan mengandung metadata dari <a %(a_scihub)s>Sci-Hub</a> dalam basis datanya."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Menurut <a %(a_mhut)s>posting forum</a> ini, Libgen.li awalnya di-host di “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> dalam beberapa hal juga merupakan cabang dari Library Genesis, meskipun mereka menggunakan nama yang berbeda untuk proyek mereka."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Halaman ini tentang versi “.rs”. Ini dikenal karena secara konsisten menerbitkan metadata dan konten lengkap dari katalog bukunya. Koleksi bukunya dibagi antara bagian fiksi dan non-fiksi."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Sumber daya yang berguna dalam menggunakan metadata adalah <a %(a_metadata)s>halaman ini</a> (memblokir rentang IP, VPN mungkin diperlukan)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Per Maret 2024, torrent baru diposting di <a %(a_href)s>thread forum ini</a> (memblokir rentang IP, mungkin memerlukan VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrent Non-Fiksi di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrent Fiksi di Arsip Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informasi bidang metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrent Non-Fiksi Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrent Fiksi Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum Diskusi Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrent oleh Arsip Anna (sampul buku)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Blog kami tentang rilis sampul buku"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis dikenal karena sudah dengan murah hati membuat data mereka tersedia dalam jumlah besar melalui torrent. Koleksi Libgen kami terdiri dari data tambahan yang tidak mereka rilis langsung, dalam kemitraan dengan mereka. Terima kasih banyak kepada semua yang terlibat dengan Library Genesis karena bekerja sama dengan kami!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Rilis 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "<a %(blog_post)s>Rilis pertama</a> ini cukup kecil: sekitar 300GB sampul buku dari fork Libgen.rs, baik fiksi maupun non-fiksi. Mereka diorganisir dengan cara yang sama seperti yang muncul di libgen.rs, misalnya:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s untuk buku non-fiksi."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s untuk buku fiksi."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Sama seperti dengan koleksi Z-Library, kami menempatkan semuanya dalam file .tar besar, yang dapat dipasang menggunakan <a %(a_ratarmount)s>ratarmount</a> jika Anda ingin menyajikan file secara langsung."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> adalah basis data milik nirlaba <a %(a_oclc)s>OCLC</a>, yang mengumpulkan catatan metadata dari perpustakaan di seluruh dunia. Ini kemungkinan merupakan koleksi metadata perpustakaan terbesar di dunia."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, rilis awal:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Pada Oktober 2023 kami <a %(a_scrape)s>merilis</a> pengumpulan lengkap basis data OCLC (WorldCat), dalam <a %(a_aac)s>format Kontainer Arsip Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrent oleh Arsip Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Posting blog kami tentang data ini"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library adalah proyek sumber terbuka oleh Internet Archive untuk mengatalogkan setiap buku di dunia. Ini memiliki salah satu operasi pemindaian buku terbesar di dunia, dan memiliki banyak buku yang tersedia untuk peminjaman digital. Katalog metadata bukunya tersedia untuk diunduh secara gratis, dan termasuk di Arsip Anna (meskipun saat ini tidak dalam pencarian, kecuali jika Anda secara eksplisit mencari ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rilis 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Ini adalah dump dari banyak panggilan ke isbndb.com selama September 2022. Kami mencoba mencakup semua rentang ISBN. Ini sekitar 30,9 juta catatan. Di situs web mereka, mereka mengklaim bahwa mereka sebenarnya memiliki 32,6 juta catatan, jadi kami mungkin entah bagaimana melewatkan beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Respon JSON hampir mentah dari server mereka. Satu masalah kualitas data yang kami perhatikan adalah bahwa untuk nomor ISBN-13 yang dimulai dengan awalan berbeda dari \"978-\", mereka masih menyertakan bidang \"isbn\" yang hanya merupakan nomor ISBN-13 dengan tiga angka pertama dipotong (dan digit cek dihitung ulang). Ini jelas salah, tetapi begitulah cara mereka melakukannya, jadi kami tidak mengubahnya."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Masalah potensial lain yang mungkin Anda temui adalah fakta bahwa bidang \"isbn13\" memiliki duplikat, sehingga Anda tidak dapat menggunakannya sebagai kunci utama dalam database. Bidang \"isbn13\" + \"isbn\" yang digabungkan tampaknya unik."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Untuk latar belakang tentang Sci-Hub, silakan merujuk ke <a %(a_scihub)s>situs resmi</a>, <a %(a_wikipedia)s>halaman Wikipedia</a>, dan <a %(a_radiolab)s>wawancara podcast</a> ini."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Perhatikan bahwa Sci-Hub telah <a %(a_reddit)s>dibekukan sejak 2021</a>. Sebelumnya juga pernah dibekukan, tetapi pada tahun 2021 beberapa juta makalah ditambahkan. Namun, beberapa jumlah terbatas makalah masih ditambahkan ke koleksi “scimag” Libgen, meskipun tidak cukup untuk menjamin torrent massal baru."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Kami menggunakan metadata Sci-Hub yang disediakan oleh <a %(a_libgen_li)s>Libgen.li</a> dalam koleksi “scimag”-nya. Kami juga menggunakan dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Perhatikan bahwa torrent “smarch” <a %(a_smarch)s>sudah usang</a> dan oleh karena itu tidak termasuk dalam daftar torrent kami."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent di Arsip Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata dan torrent"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent di Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent di Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Pembaruan di Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Halaman Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Wawancara podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Unggahan ke Arsip Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Ikhtisar dari <a %(a1)s>halaman datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Berbagai sumber yang lebih kecil atau satu kali. Kami mendorong orang untuk mengunggah ke perpustakaan bayangan lainnya terlebih dahulu, tetapi terkadang orang memiliki koleksi yang terlalu besar untuk disortir oleh orang lain, meskipun tidak cukup besar untuk mendapatkan kategori mereka sendiri."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Koleksi “upload” dibagi menjadi subkoleksi yang lebih kecil, yang ditunjukkan dalam AACID dan nama torrent. Semua subkoleksi pertama kali dideduplicasi terhadap koleksi utama, meskipun file JSON “upload_records” metadata masih mengandung banyak referensi ke file asli. File non-buku juga dihapus dari sebagian besar subkoleksi, dan biasanya <em>tidak</em> dicatat dalam JSON “upload_records”."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Banyak subkoleksi sendiri terdiri dari sub-sub-koleksi (misalnya dari sumber asli yang berbeda), yang diwakili sebagai direktori di bidang “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Subkoleksi tersebut adalah:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkoleksi"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Catatan"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "jelajahi"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cari"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Dari <a %(a_href)s>aaaaarg.fail</a>. Tampaknya cukup lengkap. Dari relawan kami “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Dari torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Memiliki tumpang tindih yang cukup tinggi dengan koleksi makalah yang ada, tetapi sangat sedikit kecocokan MD5, jadi kami memutuskan untuk menyimpannya sepenuhnya."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape dari <q>iRead eBooks</q> (= secara fonetis <q>ai rit i-books</q>; airitibooks.com), oleh relawan <q>j</q>. Sesuai dengan metadata <q>airitibooks</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Dari koleksi <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sebagian dari sumber asli, sebagian dari the-eye.eu, sebagian dari cermin lainnya."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Dari situs web torrent buku pribadi, <a %(a_href)s>Bibliotik</a> (sering disebut sebagai “Bib”), di mana buku-buku dibundel menjadi torrent berdasarkan nama (A.torrent, B.torrent) dan didistribusikan melalui the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Dari relawan kami “bpb9v”. Untuk informasi lebih lanjut tentang <a %(a_href)s>CADAL</a>, lihat catatan di <a %(a_duxiu)s>halaman dataset DuXiu</a> kami."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Lebih banyak dari relawan kami “bpb9v”, sebagian besar file DuXiu, serta folder “WenQu” dan “SuperStar_Journals” (SuperStar adalah perusahaan di balik DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Dari sukarelawan “cgiym”, teks Tiongkok dari berbagai sumber (diwakili sebagai subdirektori), termasuk dari <a %(a_href)s>China Machine Press</a> (penerbit besar Tiongkok)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Koleksi non-Cina (diwakili sebagai subdirektori) dari relawan kami “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape buku tentang arsitektur Tiongkok, oleh relawan <q>cm</q>: <q>Saya mendapatkannya dengan mengeksploitasi kerentanan jaringan di penerbit, tetapi celah itu sekarang sudah ditutup</q>. Sesuai dengan metadata <q>chinese_architecture</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Buku dari penerbit akademik <a %(a_href)s>De Gruyter</a>, dikumpulkan dari beberapa torrent besar."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape dari <a %(a_href)s>docer.pl</a>, situs berbagi file Polandia yang berfokus pada buku dan karya tulis lainnya. Di-scrape pada akhir 2023 oleh relawan “p”. Kami tidak memiliki metadata yang baik dari situs asli (bahkan tidak ada ekstensi file), tetapi kami memfilter file yang mirip buku dan sering kali dapat mengekstrak metadata dari file itu sendiri."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, langsung dari DuXiu, dikumpulkan oleh relawan “w”. Hanya buku DuXiu terbaru yang tersedia langsung melalui ebook, jadi sebagian besar dari ini haruslah yang terbaru."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Sisa file DuXiu dari sukarelawan “m”, yang tidak dalam format PDG milik DuXiu (dataset utama <a %(a_href)s>DuXiu</a>). Dikumpulkan dari banyak sumber asli, sayangnya tanpa mempertahankan sumber tersebut di filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape buku erotis, oleh relawan <q>do no harm</q>. Sesuai dengan metadata <q>hentai</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Koleksi yang di-scrape dari penerbit Manga Jepang oleh relawan “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arsip yudisial terpilih Longquan</a>, disediakan oleh sukarelawan “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape dari <a %(a_href)s>magzdb.org</a>, sekutu Library Genesis (terhubung di halaman utama libgen.rs) tetapi yang tidak ingin menyediakan file mereka secara langsung. Diperoleh oleh sukarelawan “p” pada akhir 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Berbagai unggahan kecil, terlalu kecil sebagai subkoleksi mereka sendiri, tetapi diwakili sebagai direktori."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebook dari AvaxHome, situs berbagi file Rusia."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arsip surat kabar dan majalah. Sesuai dengan metadata <q>newsarch_magz</q> dalam <a %(a1)s><q>Scrape metadata lainnya</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape dari <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Koleksi sukarelawan “o” yang mengumpulkan buku-buku Polandia langsung dari situs rilis asli (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Koleksi gabungan dari <a %(a_href)s>shuge.org</a> oleh sukarelawan “cgiym” dan “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Perpustakaan Kekaisaran Trantor”</a> (dinamai sesuai perpustakaan fiksi), diambil pada tahun 2022 oleh relawan “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-koleksi (diwakili sebagai direktori) dari relawan “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (oleh <a %(a_sikuquanshu)s>Dizhi(迪志)</a> di Taiwan), mebook (mebook.cc, 我的小书屋, ruang buku kecil saya — woz9ts: “Situs ini terutama berfokus pada berbagi file ebook berkualitas tinggi, beberapa di antaranya diatur oleh pemiliknya sendiri. Pemiliknya <a %(a_arrested)s>ditangkap</a> pada 2019 dan seseorang membuat koleksi file yang dia bagikan.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Sisa file DuXiu dari relawan “woz9ts”, yang tidak dalam format PDG milik DuXiu (masih harus dikonversi ke PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrent oleh Arsip Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Pengambilan Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library berakar dari komunitas <a %(a_href)s>Library Genesis</a>, dan awalnya dimulai dengan data mereka. Sejak itu, Z-Library telah menjadi lebih profesional, dan memiliki antarmuka yang jauh lebih modern. Oleh karena itu, mereka dapat menerima lebih banyak donasi, baik secara finansial untuk terus meningkatkan situs web mereka, maupun donasi buku baru. Mereka telah mengumpulkan koleksi besar selain dari Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Pembaruan per Februari 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Pada akhir 2022, pendiri Z-Library yang diduga ditangkap, dan domain disita oleh otoritas Amerika Serikat. Sejak itu situs web perlahan-lahan kembali online. Tidak diketahui siapa yang saat ini mengelolanya."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Koleksi ini terdiri dari tiga bagian. Halaman deskripsi asli untuk dua bagian pertama disimpan di bawah ini. Anda memerlukan ketiga bagian untuk mendapatkan semua data (kecuali torrent yang digantikan, yang dicoret di halaman torrent)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: rilis pertama kami. Ini adalah rilis pertama dari apa yang kemudian disebut \"Pirate Library Mirror\" (\"pilimi\")."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: rilis kedua, kali ini dengan semua file dibungkus dalam file .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: rilis baru bertahap, menggunakan <a %(a_href)s>format Kontainer Arsip Anna (AAC)</a>, sekarang dirilis dalam kolaborasi dengan tim Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrent oleh Arsip Anna (metadata + konten)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Contoh catatan di Arsip Anna (koleksi asli)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Contoh catatan di Arsip Anna (koleksi \"zlib3\")"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Situs web utama"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domain Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Posting blog tentang Rilis 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Posting blog tentang Rilis 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Rilis Zlib (halaman deskripsi asli)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Rilis 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Mirror awal diperoleh dengan susah payah selama tahun 2021 dan 2022. Pada titik ini, sedikit ketinggalan zaman: mencerminkan keadaan koleksi pada Juni 2021. Kami akan memperbarui ini di masa depan. Saat ini kami fokus untuk merilis rilis pertama ini."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Karena Library Genesis sudah disimpan dengan torrent publik, dan termasuk dalam Z-Library, kami melakukan deduplikasi dasar terhadap Library Genesis pada Juni 2022. Untuk ini kami menggunakan hash MD5. Kemungkinan ada banyak konten duplikat di perpustakaan, seperti beberapa format file dengan buku yang sama. Ini sulit dideteksi secara akurat, jadi kami tidak melakukannya. Setelah deduplikasi, kami memiliki lebih dari 2 juta file, dengan total hampir 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Koleksi ini terdiri dari dua bagian: dump MySQL “.sql.gz” dari metadata, dan 72 file torrent masing-masing sekitar 50-100GB. Metadata berisi data seperti yang dilaporkan oleh situs web Z-Library (judul, penulis, deskripsi, tipe file), serta ukuran file aktual dan md5sum yang kami amati, karena terkadang ini tidak sesuai. Tampaknya ada rentang file di mana Z-Library sendiri memiliki metadata yang salah. Kami mungkin juga telah mengunduh file yang salah dalam beberapa kasus terisolasi, yang akan kami coba deteksi dan perbaiki di masa depan."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "File torrent besar berisi data buku aktual, dengan ID Z-Library sebagai nama file. Ekstensi file dapat direkonstruksi menggunakan dump metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Koleksi ini adalah campuran konten non-fiksi dan fiksi (tidak dipisahkan seperti di Library Genesis). Kualitasnya juga sangat bervariasi."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Rilis pertama ini sekarang sepenuhnya tersedia. Perhatikan bahwa file torrent hanya tersedia melalui mirror Tor kami."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Rilis 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Kami telah mendapatkan semua buku yang ditambahkan ke Z-Library antara mirror terakhir kami dan Agustus 2022. Kami juga kembali dan mengumpulkan beberapa buku yang kami lewatkan pertama kali. Secara keseluruhan, koleksi baru ini sekitar 24TB. Sekali lagi, koleksi ini dideduplikasi terhadap Library Genesis, karena sudah ada torrent yang tersedia untuk koleksi tersebut."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Data diatur serupa dengan rilis pertama. Ada dump MySQL “.sql.gz” dari metadata, yang juga mencakup semua metadata dari rilis pertama, sehingga menggantikannya. Kami juga menambahkan beberapa kolom baru:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: apakah file ini sudah ada di Library Genesis, baik dalam koleksi non-fiksi atau fiksi (cocok dengan md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: torrent mana file ini berada."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: ditetapkan ketika kami tidak dapat mengunduh buku tersebut."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Kami menyebutkan ini terakhir kali, tetapi hanya untuk memperjelas: \"filename\" dan \"md5\" adalah properti sebenarnya dari file, sedangkan \"filename_reported\" dan \"md5_reported\" adalah apa yang kami kumpulkan dari Z-Library. Terkadang kedua hal ini tidak sesuai satu sama lain, jadi kami menyertakan keduanya."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Untuk rilis ini, kami mengubah kolasi menjadi \"utf8mb4_unicode_ci\", yang seharusnya kompatibel dengan versi MySQL yang lebih lama."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "File data tersebut mirip dengan sebelumnya, meskipun ukurannya jauh lebih besar. Kami tidak bisa repot-repot membuat banyak file torrent yang lebih kecil. “pilimi-zlib2-0-14679999-extra.torrent” berisi semua file yang terlewat pada rilis sebelumnya, sementara torrent lainnya adalah rentang ID baru. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Pembaruan %(date)s:</strong> Kami membuat sebagian besar torrent kami terlalu besar, menyebabkan klien torrent kesulitan. Kami telah menghapusnya dan merilis torrent baru."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Pembaruan %(date)s:</strong> Masih terlalu banyak file, jadi kami membungkusnya dalam file tar dan merilis torrent baru lagi."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Tambahan Rilis 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Ini adalah satu file torrent tambahan. Tidak mengandung informasi baru, tetapi memiliki beberapa data di dalamnya yang dapat memakan waktu untuk dihitung. Itu membuatnya nyaman untuk dimiliki, karena mengunduh torrent ini seringkali lebih cepat daripada menghitungnya dari awal. Secara khusus, ini berisi indeks SQLite untuk file tar, untuk digunakan dengan <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Pertanyaan umum (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Apasih Arsip anna?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Arsip Anna</span> merupakan proyek non-profit dengan dua tujuan:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Pelestarian:</strong>Membangun pencadangan semua pengetahuan dan budaya umat manusia .</li><li><strong>Akses:</strong> Mewujudkan ketersediaan atas pengetahuan dan budaya tersebut untuk semua orang di dunia.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Semua <a %(a_code)s>kode</a> dan <a %(a_datasets)s>data</a> kami sepenuhnya open source."

msgid "page.home.preservation.header"
msgstr "Pelestarian"

msgid "page.home.preservation.text1"
msgstr "Kami melestarikan buku, artikel, komik, majalah dll dari berbagai sumber <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>, pustaka resmi dll menjadi satu tempat. Semua data ini tersimpan selamanya dengan mempermudah menyalinya dalam jumlah besar — menggunakan torrents — menghasilkan kopian yang sangat banyak di segala penjuru dunia. Yang sebelumnya telah dilakukan oleh beberapa shadow libraries (cth. Sci-Hub, Library Genesis), Yang mana Arsip Anna \"membebaskan\" pustaka lain yang tidak dapat menyediakan distribusi dalam jumlah besar (cth. Z-Library) atau pun yang bukan merupakan bagian shadow libraries (cth. Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Distribusi secara luas ini bergabung dengan kode sumber terbuka, yang membuat halaman web kami dapat bertahan dari takedown, dan memastikan pengetahuan dan budaya unat manusia dilestarikan dalam jangka lama. Pelajari lebih lanjut <a href=\"/datasets\">datasets kami</a>."

msgid "page.home.preservation.label"
msgstr "Kami telah melestarikan sebanyak <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% dari seluruh buku di dunia</a>."

msgid "page.home.access.header"
msgstr "Akses"

msgid "page.home.access.text"
msgstr "Kami berkerjasama dengan beberapa rekan dalam mempermudah akses koleksi yang kami miliki untuk semua orang. Karna kami percaya setiap orang memiliki hak yang sama dalam mengakses kumpulan pengetahuan umat manusia, dan <a %(a_search)s>bukan lah beban dari penulis</a>."

msgid "page.home.access.label"
msgstr "Laporan unduhan per jam dalam kurun waktu 30 hari terahir. Rata-rata/jam: %(hourly)s. Rata-rata/hari: %(daily)s."

msgid "page.about.text2"
msgstr "Kami sangat percaya dengan penyebaran informasi yang bebas, dan pelestarian pengetahuan dan budaya. Dengan mesin pencari ini, kami membangun di atas bahu raksasa. Kami menghormati secara mendalam kerja keras dari orang-orang yang telah menciptakan berbagai perpustakaan bayangan, dan kami harap mesin pencari ini akan memperluas cakupan mereka."

msgid "page.about.text3"
msgstr "Ikuti perkembangan terbaru kami dengan mengikuti Anna di <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Untuk pertanyaan dan umpan balik, silakan hubungi Anna di %(email)s."

msgid "page.faq.help.title"
msgstr "Bagaimana saya dapat membantu?"

msgid "page.about.help.text"
msgstr "<li>1. Ikuti kami di <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Sebarkan berita tentang Anna's Archive di Twitter, Reddit, Tiktok, Instagram, di tongkronganmu atau perpustakaan, atau dimanapun kamu pergi! Kami tidak percaya dengan penjagaan gawang— apabila kami dibubarkan kami hanya akan muncul di tempat lain, karena semua kode dan data kami sepenuhnya open source.</li><li>3. Jika anda mampu, pertimbangkan untuk <a href=\"/donate\">donasi</a>.</li><li>4. Bantu <a href=\"https://translate.annas-software.org/\">menerjemahkan</a> website kami ke berbagai bahasa.</li><li>5. Jika anda seorang insinyur perangkat lunak, pertimbangkan untuk berkontribusi ke <a href=\"https://annas-software.org/\">open source</a> kami, atau dengan seeding <a href=\"/datasets\">torrents</a> kami.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Kami sekarang juga memiliki saluran Matrix yang disinkronkan di %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Jika anda memiliki keahlian dibidang keamanan jaringan, kami dapat menggunakan kemampuan anda dibidang offensif maupun defensif. Silahkan periksa halaman <a %(a_security)s>Keamanan</a> kami."

msgid "page.about.help.text7"
msgstr "7. Kami sedang mencari ahli pembayaran secara anonim. Dapatkah kamu membantu kami menambahkan metode donasi yang lebih nyaman? PayPal, WeChat, gift cards. Bila anda mengenal seseorang yang ahli dibidang tersebut mohon hubungi kami."

msgid "page.about.help.text8"
msgstr "8. Kami membutuhkan kapasitas server yang lebih baik."

msgid "page.about.help.text9"
msgstr "9. Kamu dapat membantu dengan melaporkan dokumen yang bermasalah, meningalkan komentar, dan membangun list buku secara langsung lewat halaman web. Kamu juga dapat membantu demgan cara <a %(a_upload)s>unggah lebih banyak buku</a>, atau memperbaiki dokumen bermasalah atau mengatur ulang fokumen yang telah ada menjadi lebih baik."

msgid "page.about.help.text10"
msgstr "10. Buat atau bantu perawatan halaman Wikipedia terkait Arsip Anna dalam bahasamu."

msgid "page.about.help.text11"
msgstr "11. Kami sedang mencari tempat pengiklanan. Bila kamu tertarik mengiklankan Arsip Anna ,mohon beri tahu kami."

msgid "page.faq.help.mirrors"
msgstr "Kami sangat mendukung kamu untuk membuat <a %(a_mirrors)s>mirrors</a>, dan kami akan memberikan dukungan keuangan."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Untuk informasi lebih lanjut tentang cara menjadi sukarelawan, lihat halaman <a %(a_volunteering)s>Sukarelawan & Hadiah</a> kami."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Mengapa unduhan lambat begitu lambat?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Kami benar-benar tidak memiliki cukup sumber daya untuk memberikan unduhan berkecepatan tinggi kepada semua orang di dunia, seberapa pun kami menginginkannya. Jika ada dermawan kaya yang ingin membantu menyediakan ini untuk kami, itu akan luar biasa, tetapi sampai saat itu, kami berusaha sebaik mungkin. Kami adalah proyek nirlaba yang hampir tidak dapat bertahan melalui donasi."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Inilah sebabnya kami menerapkan dua sistem untuk unduhan gratis, dengan mitra kami: server bersama dengan unduhan lambat, dan server yang sedikit lebih cepat dengan daftar tunggu (untuk mengurangi jumlah orang yang mengunduh pada saat yang sama)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Kami juga memiliki <a %(a_verification)s>verifikasi browser</a> untuk unduhan lambat kami, karena jika tidak, bot dan scraper akan menyalahgunakannya, membuat segalanya menjadi lebih lambat bagi pengguna yang sah."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Perhatikan bahwa, saat menggunakan Tor Browser, Anda mungkin perlu menyesuaikan pengaturan keamanan Anda. Pada opsi terendah, yang disebut “Standar”, tantangan turnstile Cloudflare berhasil. Pada opsi yang lebih tinggi, yang disebut “Lebih Aman” dan “Paling Aman”, tantangan tersebut gagal."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Untuk file besar, terkadang unduhan yang lambat bisa terputus di tengah jalan. Kami merekomendasikan menggunakan pengelola unduhan (seperti JDownloader) untuk melanjutkan unduhan besar secara otomatis."

msgid "page.donate.faq.title"
msgstr "Pertanyaan Umum tentang Donasi"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Apakah keanggotaan diperbarui secara otomatis?</div> Keanggotaan <strong>tidak</strong> diperbarui secara otomatis. Anda dapat bergabung selama yang Anda inginkan."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Bisakah saya meningkatkan keanggotaan saya atau mendapatkan beberapa keanggotaan?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Apakah Anda memiliki metode pembayaran lain?</div> Saat ini tidak ada. Ada banyak orang yang tidak ingin proyek arsip seperti ini ada di internet, jadi kami harus berhati-hati. Jika Anda dapat dan ingin membantu kami menyiapkan metode pembayaran lain (yang lebih mudah digunakan) dengan aman, silakan hubungi kami di %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Apa arti rentang per bulan?</div> Anda dapat mencapai sisi bawah dari rentang dengan menerapkan semua diskon, seperti memilih periode lebih lama dari satu bulan."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Apa yang akan dilakukan dengan donasi yang diberikan?</div> 100%% dari donasi digunakan untuk menjaga dan memudahkan akses pengetahuan dan budaya dunia. Saat ini, sebagian besar digunakan untuk menyewa komputer server, tempat penyimpanan data, dan biaya internet. Tidak ada uang yang diberikan kepada anggota tim secara pribadi."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Bolehkah saya mendonasikan dengan jumlah besar?</div> Itu akan sangat luar biasa! Untuk donasi yang melebihi beberapa ribu dolar, silakan menghubungi kami secara langsung di %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Bisakah saya berdonasi tanpa menjadi anggota?</div> Tentu saja. Kami menerima donasi dalam jumlah berapa pun di alamat Monero (XMR) ini: %(address)s."

msgid "page.faq.upload.title"
msgstr "Bagaimana saya dapat mengunggah buku baru?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Sebagai alternatif, Anda dapat mengunggahnya ke Z-Library <a %(a_upload)s>di sini</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Untuk unggahan kecil (hingga 10.000 file) silakan unggah ke %(first)s dan %(second)s."

msgid "page.upload.text1"
msgstr "Untuk saat ini, kami menyarankan untuk mengunggah buku-buku baru ke saluran Library Genesis yang tersedia. Berikut adalah <a %(a_guide)s>panduan yang berguna</a>. Perhatikan bahwa kedua saluran yang kami indeks di situs web ini mengambil dari sistem unggahan yang sama."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Untuk Libgen.li, pastikan untuk terlebih dahulu masuk ke <a %(a_forum)s>forum mereka</a> dengan nama pengguna %(username)s dan kata sandi %(password)s, lalu kembali ke <a %(a_upload_page)s>halaman unggah</a> mereka."

msgid "common.libgen.email"
msgstr "Jika alamat email Anda tidak berfungsi di forum Libgen, kami sarankan menggunakan <a %(a_mail)s>Proton Mail</a> (gratis). Anda juga bisa <a %(a_manual)s>meminta aktivasi akun secara manual</a>."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Perhatikan bahwa mhut.org memblokir rentang IP tertentu, jadi VPN mungkin diperlukan."

msgid "page.upload.large.text"
msgstr "Untuk unggahan ukuran besar(lebih dari 10,000 dokumen) yang tidak dapat dilayani oleh Libgen atau Z-Library, mohon untuk menghubungi kami di %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Untuk mengunggah makalah akademis, harap juga (selain ke Library Genesis) unggah ke <a %(a_stc_nexus)s>STC Nexus</a>. Mereka adalah shadow library terbaik untuk makalah baru. Kami belum mengintegrasikan mereka, tetapi kami akan melakukannya di suatu titik. Anda dapat menggunakan <a %(a_telegram)s>bot unggah mereka di Telegram</a>, atau hubungi alamat yang tercantum dalam pesan yang disematkan jika Anda memiliki terlalu banyak file untuk diunggah dengan cara ini."

msgid "page.faq.request.title"
msgstr "Bagaimana saya dapat memesan buku?"

msgid "page.request.cannot_accomodate"
msgstr "Untuk saat ini, kami belum dapat membantu dalam pemesanan buku."

msgid "page.request.forums"
msgstr "Mohon ajukan pemesanan kamu melalui forum Z-Library atau Libgen."

msgid "page.request.dont_email"
msgstr "Mohon jangan mengirimkan email kepada kami terkait dengan pemesanan buku."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Apakah Anda mengumpulkan metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Kami memang melakukannya."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Saya mengunduh 1984 oleh George Orwell, apakah polisi akan datang ke rumah saya?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Jangan terlalu khawatir, ada banyak orang yang mengunduh dari situs web yang kami tautkan, dan sangat jarang mengalami masalah. Namun, untuk tetap aman kami merekomendasikan menggunakan VPN (berbayar), atau <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Bagaimana cara menyimpan pengaturan pencarian saya?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Pilih pengaturan yang Anda sukai, biarkan kotak pencarian kosong, klik \"Cari\", dan kemudian tandai halaman tersebut menggunakan fitur bookmark browser Anda."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Apakah Anda memiliki aplikasi seluler?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Kami tidak memiliki aplikasi seluler resmi, tetapi Anda dapat menginstal situs web ini sebagai aplikasi."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik menu tiga titik di kanan atas, dan pilih “Tambahkan ke Layar Utama”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik tombol “Bagikan” di bagian bawah, dan pilih “Tambahkan ke Layar Utama”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Apakah Anda memiliki API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Kami memiliki satu API JSON stabil untuk anggota, untuk mendapatkan URL unduhan cepat: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasi ada di dalam JSON itu sendiri)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Untuk kasus penggunaan lainnya, seperti iterasi melalui semua file kami, membangun pencarian kustom, dan sebagainya, kami merekomendasikan <a %(a_generate)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Data mentah dapat dieksplorasi secara manual <a %(a_explore)s>melalui file JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Daftar torrent mentah kami dapat diunduh sebagai <a %(a_torrents)s>JSON</a> juga."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Saya ingin membantu seeding, tetapi saya tidak memiliki banyak ruang disk."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Gunakan <a %(a_list)s>generator daftar torrent</a> untuk menghasilkan daftar torrent yang paling membutuhkan torrenting, dalam batas ruang penyimpanan Anda."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrents terlalu lambat; bisakah saya mengunduh data langsung dari Anda?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ya, lihat halaman <a %(a_llm)s>data LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Bisakah saya mengunduh hanya sebagian dari file, seperti hanya bahasa atau topik tertentu?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Jawaban singkat: tidak mudah."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Jawaban panjang:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Sebagian besar torrent berisi file secara langsung, yang berarti Anda dapat menginstruksikan klien torrent untuk hanya mengunduh file yang diperlukan. Untuk menentukan file mana yang akan diunduh, Anda dapat <a %(a_generate)s>menghasilkan</a> metadata kami, atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Sayangnya, beberapa koleksi torrent berisi file .zip atau .tar di root, dalam hal ini Anda perlu mengunduh seluruh torrent sebelum dapat memilih file individual."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Kami memiliki <a %(a_ideas)s>beberapa ide</a> untuk kasus yang terakhir ini.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Belum ada alat yang mudah digunakan untuk memfilter torrent, tetapi kami menyambut kontribusi."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Bagaimana Anda menangani duplikasi dalam torrent?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Kami berusaha untuk meminimalkan duplikasi atau tumpang tindih antara torrent dalam daftar ini, tetapi hal ini tidak selalu dapat dicapai, dan sangat bergantung pada kebijakan perpustakaan sumber. Untuk perpustakaan yang mengeluarkan torrent mereka sendiri, itu di luar kendali kami. Untuk torrent yang dirilis oleh Arsip Anna, kami menduplikasi hanya berdasarkan hash MD5, yang berarti bahwa versi berbeda dari buku yang sama tidak akan diduplikasi."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Bisakah saya mendapatkan daftar torrent dalam format JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ya."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Saya tidak melihat PDF atau EPUB dalam torrent, hanya file biner? Apa yang harus saya lakukan?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Ini sebenarnya adalah PDF dan EPUB, mereka hanya tidak memiliki ekstensi di banyak torrent kami. Ada dua tempat di mana Anda dapat menemukan metadata untuk file torrent, termasuk jenis/ekstensi file:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Setiap koleksi atau rilis memiliki metadata sendiri. Misalnya, <a %(a_libgen_nonfic)s>torrent Libgen.rs</a> memiliki database metadata yang sesuai yang dihosting di situs web Libgen.rs. Kami biasanya menautkan ke sumber daya metadata yang relevan dari <a %(a_datasets)s>halaman dataset</a> setiap koleksi."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Kami merekomendasikan <a %(a_generate)s>menghasilkan</a> atau <a %(a_download)s>mengunduh</a> database ElasticSearch dan MariaDB kami. Ini berisi pemetaan untuk setiap catatan di Arsip Anna ke file torrent yang sesuai (jika tersedia), di bawah “torrent_paths” dalam JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Mengapa klien torrent saya tidak dapat membuka beberapa file torrent / tautan magnet Anda?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Beberapa klien torrent tidak mendukung ukuran potongan besar, yang dimiliki banyak torrent kami (untuk yang lebih baru kami tidak lagi melakukannya — meskipun ini valid sesuai spesifikasi!). Jadi cobalah klien lain jika Anda mengalami ini, atau keluhkan kepada pembuat klien torrent Anda."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Apakah Anda memiliki program pengungkapan yang bertanggung jawab?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Kami menyambut peneliti keamanan untuk mencari kerentanan dalam sistem kami. Kami adalah pendukung besar pengungkapan yang bertanggung jawab. Hubungi kami <a %(a_contact)s>di sini</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Saat ini kami tidak dapat memberikan hadiah bug bounty, kecuali untuk kerentanan yang memiliki <a %(a_link)s>potensi untuk mengkompromikan anonimitas kami</a>, yang mana kami menawarkan hadiah dalam kisaran $10k-50k. Kami ingin menawarkan cakupan yang lebih luas untuk bug bounty di masa depan! Harap dicatat bahwa serangan rekayasa sosial berada di luar cakupan."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Jika Anda tertarik pada keamanan ofensif, dan ingin membantu mengarsipkan pengetahuan dan budaya dunia, pastikan untuk menghubungi kami. Ada banyak cara di mana Anda dapat membantu."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Apakah ada lebih banyak sumber daya tentang Arsip Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — pembaruan reguler"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Perangkat Lunak Anna</a> — kode sumber terbuka kami"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Terjemahkan di Perangkat Lunak Anna</a> — sistem terjemahan kami"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — tentang data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domain alternatif"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — lebih banyak tentang kami (tolong bantu perbarui halaman ini, atau buat satu untuk bahasa Anda sendiri!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Bagaimana cara melaporkan pelanggaran hak cipta?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Kami tidak meng-host materi berhak cipta apa pun di sini. Kami adalah mesin pencari, dan dengan demikian hanya mengindeks metadata yang sudah tersedia untuk umum. Saat mengunduh dari sumber eksternal ini, kami menyarankan untuk memeriksa undang-undang di yurisdiksi Anda terkait apa yang diizinkan. Kami tidak bertanggung jawab atas konten yang di-host oleh pihak lain."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Jika Anda memiliki keluhan tentang apa yang Anda lihat di sini, pilihan terbaik Anda adalah menghubungi situs web asli. Kami secara teratur menarik perubahan mereka ke dalam database kami. Jika Anda benar-benar berpikir Anda memiliki keluhan DMCA yang valid yang harus kami tanggapi, silakan isi <a %(a_copyright)s>formulir klaim DMCA / Hak Cipta</a>. Kami menanggapi keluhan Anda dengan serius, dan akan menghubungi Anda sesegera mungkin."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Saya benci cara Anda menjalankan proyek ini!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Kami juga ingin mengingatkan semua orang bahwa semua kode dan data kami sepenuhnya sumber terbuka. Ini unik untuk proyek seperti kami — kami tidak mengetahui proyek lain dengan katalog sebesar ini yang juga sepenuhnya sumber terbuka. Kami sangat menyambut siapa pun yang berpikir kami menjalankan proyek kami dengan buruk untuk mengambil kode dan data kami dan mendirikan shadow library mereka sendiri! Kami tidak mengatakan ini karena dendam atau semacamnya — kami benar-benar berpikir ini akan luar biasa karena akan meningkatkan standar untuk semua orang, dan lebih baik melestarikan warisan umat manusia."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Apakah Anda memiliki pemantau waktu aktif?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Silakan lihat <a %(a_href)s>proyek yang luar biasa ini</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Bagaimana cara menyumbangkan buku atau bahan fisik lainnya?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Silakan kirim ke <a %(a_archive)s>Internet Archive</a>. Mereka akan menyimpannya dengan baik."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Siapa Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Anda adalah Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Apa buku favorit Anda?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Berikut adalah beberapa buku yang memiliki makna khusus bagi dunia shadow library dan pelestarian digital:"

msgid "page.fast_downloads.no_more_new"
msgstr "Kamu kehabisan unduhan jalur cepat hari ini."

msgid "page.fast_downloads.no_member"
msgstr "Daftar member untuk mengakses fitur unduhan jalur cepat."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Kami sekarang mendukung kartu hadiah Amazon, kartu kredit dan debit, kripto, Alipay, dan WeChat."

msgid "page.home.full_database.header"
msgstr "Database lengkap"

msgid "page.home.full_database.subtitle"
msgstr "Buku, artikel, majalah, komik, catatan pustaka,metadata,…"

msgid "page.home.full_database.search"
msgstr "Cari"

msgid "page.home.scidb.header"
msgstr "SicDB"

msgid "layout.index.header.nav.beta"
msgstr "versi beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub telah <a %(a_paused)s>menghentikan</a> pengunggahan makalah baru."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB adalah kelanjutan dari Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Akses langsung ke %(count)s jurnal ilmiah"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Buka"

msgid "page.home.scidb.browser_verification"
msgstr "Bila anda masuk <a %(a_member)s>member</a>, verifikasi browser tidak diperlukan."

msgid "page.home.archive.header"
msgstr "Arsip jangka panjang"

msgid "page.home.archive.body"
msgstr "Datasets yang digunakan dalam Arsip Anna bersifat terbuka, dan dapat digandakan secara besar-besaran mengunakan torrent.<a%(a_datasets)s>Pelajari lebih lanjut...</a>"

msgid "page.home.torrents.body"
msgstr "Kamu dapat snagat membantu dengan membangun seeding torrent.<a %(a_torrents)s>Pelajari lebih lanjut...</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s jumlah seeder"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeder"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s jumlah seeder"

msgid "page.home.llm.header"
msgstr "LLM training data"

msgid "page.home.llm.body"
msgstr "Kami memiliki koleksi data terbesar di dunia dengan kualitas tinggi.<a%(a_llm)s>Pelajari lebih lanjut...</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: panggilan relawan"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Mencari relawan"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Sebagai proyek nirlaba dan sumber terbuka, kami selalu mencari orang untuk membantu."

msgid "page.home.payment_processor.body"
msgstr "BIla anda menjalankan servis pembayaran anonimus dengan resiko tinggi, mohon hubungi kami. Kami saat ini sedang mencari tempat iklan. Semua prosedur dapat dilihat diusaha pelestarian kami."

msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Unduhan IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Semua link unduhan untuk dokumen ini: <a %(a_main)s>Halaman utama dokumen</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Gateway IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(Anda mungkin perlu mencoba berkali-kali dengan IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Untuk mengakses unduhan jalur cepat dan lewati pemeriksaan browser, <a %(a_membership)s>daftar sebagai member</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Untuk membuat pencadangan sekala besar dari koleksi kami, silahkan periksa halaman <a %(a_datasets)s>Dataset</a> dan <a %(a_torrents)s>Torrent</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Data LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Sudah dipahami dengan baik bahwa LLM berkembang dengan data berkualitas tinggi. Kami memiliki koleksi buku, makalah, majalah, dll terbesar di dunia, yang merupakan beberapa sumber teks berkualitas tertinggi."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Skala dan jangkauan unik"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Koleksi kami berisi lebih dari seratus juta file, termasuk jurnal akademik, buku teks, dan majalah. Kami mencapai skala ini dengan menggabungkan repositori besar yang sudah ada."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Beberapa koleksi sumber kami sudah tersedia dalam jumlah besar (Sci-Hub, dan bagian dari Libgen). Sumber lain kami bebaskan sendiri. <a %(a_datasets)s>Datasets</a> menunjukkan gambaran lengkap."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Koleksi kami mencakup jutaan buku, makalah, dan majalah dari sebelum era e-book. Sebagian besar dari koleksi ini sudah di-OCR, dan sudah memiliki sedikit tumpang tindih internal."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Bagaimana kami bisa membantu"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Kami dapat menyediakan akses berkecepatan tinggi ke seluruh koleksi kami, serta ke koleksi yang belum dirilis."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Ini adalah akses tingkat perusahaan yang dapat kami sediakan untuk donasi dalam kisaran puluhan ribu USD. Kami juga bersedia menukarnya dengan koleksi berkualitas tinggi yang belum kami miliki."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Kami dapat mengembalikan uang Anda jika Anda dapat memberikan kami pengayaan data kami, seperti:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Menghapus tumpang tindih (deduplikasi)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Ekstraksi teks dan metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Dukung pengarsipan jangka panjang pengetahuan manusia, sambil mendapatkan data yang lebih baik untuk model Anda!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Hubungi kami</a> untuk mendiskusikan bagaimana kita bisa bekerja sama."

msgid "page.login.continue"
msgstr "Lanjutkan"

msgid "page.login.please"
msgstr "Mohon <a %(a_account)s>masuk</a> untuk melihat halaman ini.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Arsip Anna sedang dalam pemeliharaan sementara. Silakan kembali dalam satu jam."

#, fuzzy
msgid "page.metadata.header"
msgstr "Perbaiki metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Anda dapat membantu pelestarian buku dengan memperbaiki metadata! Pertama, baca latar belakang tentang metadata di Arsip Anna, dan kemudian pelajari cara memperbaiki metadata melalui tautan dengan Open Library, dan dapatkan keanggotaan gratis di Arsip Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Latar Belakang"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Ketika Anda melihat sebuah buku di Arsip Anna, Anda dapat melihat berbagai bidang: judul, penulis, penerbit, edisi, tahun, deskripsi, nama file, dan lainnya. Semua informasi tersebut disebut <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Karena kami menggabungkan buku dari berbagai <em>perpustakaan sumber</em>, kami menampilkan metadata apa pun yang tersedia di perpustakaan sumber tersebut. Misalnya, untuk buku yang kami dapatkan dari Library Genesis, kami akan menampilkan judul dari database Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Terkadang sebuah buku ada di <em>beberapa</em> perpustakaan sumber, yang mungkin memiliki bidang metadata yang berbeda. Dalam kasus tersebut, kami hanya menampilkan versi terpanjang dari setiap bidang, karena versi tersebut diharapkan mengandung informasi yang paling berguna! Kami masih akan menampilkan bidang lainnya di bawah deskripsi, misalnya sebagai \"judul alternatif\" (tetapi hanya jika berbeda)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Kami juga mengekstrak <em>kode</em> seperti pengidentifikasi dan pengklasifikasi dari perpustakaan sumber. <em>Pengidentifikasi</em> secara unik mewakili edisi tertentu dari sebuah buku; contohnya adalah ISBN, DOI, Open Library ID, Google Books ID, atau Amazon ID. <em>Pengklasifikasi</em> mengelompokkan beberapa buku serupa; contohnya adalah Dewey Decimal (DCC), UDC, LCC, RVK, atau GOST. Terkadang kode-kode ini secara eksplisit ditautkan di perpustakaan sumber, dan terkadang kami dapat mengekstraknya dari nama file atau deskripsi (terutama ISBN dan DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Kami dapat menggunakan pengidentifikasi untuk menemukan catatan di <em>koleksi metadata saja</em>, seperti OpenLibrary, ISBNdb, atau WorldCat/OCLC. Ada <em>tab metadata</em> khusus di mesin pencari kami jika Anda ingin menjelajahi koleksi tersebut. Kami menggunakan catatan yang cocok untuk mengisi bidang metadata yang hilang (misalnya jika judul hilang), atau misalnya sebagai \"judul alternatif\" (jika ada judul yang sudah ada)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Untuk melihat dengan tepat dari mana metadata sebuah buku berasal, lihat <em>tab \"Detail teknis\"</em> di halaman buku. Ini memiliki tautan ke JSON mentah untuk buku tersebut, dengan petunjuk ke JSON mentah dari catatan asli."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Untuk informasi lebih lanjut, lihat halaman berikut: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Pencarian (tab metadata)</a>, <a %(a_codes)s>Penjelajah Kode</a>, dan <a %(a_example)s>Contoh metadata JSON</a>. Akhirnya, semua metadata kami dapat <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>diunduh</a> sebagai database ElasticSearch dan MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Tautan Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Jadi jika Anda menemukan file dengan metadata yang buruk, bagaimana cara memperbaikinya? Anda dapat pergi ke perpustakaan sumber dan mengikuti prosedurnya untuk memperbaiki metadata, tetapi apa yang harus dilakukan jika sebuah file ada di beberapa perpustakaan sumber?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Ada satu pengidentifikasi yang diperlakukan khusus di Arsip Anna. <strong>Bidang annas_archive md5 di Open Library selalu mengesampingkan semua metadata lainnya!</strong> Mari kita mundur sedikit dan belajar tentang Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library didirikan pada tahun 2006 oleh Aaron Swartz dengan tujuan \"satu halaman web untuk setiap buku yang pernah diterbitkan\". Ini semacam Wikipedia untuk metadata buku: semua orang dapat mengeditnya, berlisensi bebas, dan dapat diunduh secara massal. Ini adalah database buku yang paling selaras dengan misi kami — sebenarnya, Arsip Anna terinspirasi oleh visi dan kehidupan Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Alih-alih menciptakan kembali roda, kami memutuskan untuk mengarahkan sukarelawan kami ke Open Library. Jika Anda melihat buku yang memiliki metadata yang salah, Anda dapat membantu dengan cara berikut:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Pergi ke <a %(a_openlib)s>situs web Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Temukan catatan buku yang benar. <strong>PERINGATAN:</strong> pastikan untuk memilih <strong>edisi</strong> yang benar. Di Open Library, Anda memiliki \"karya\" dan \"edisi\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Sebuah \"karya\" bisa jadi \"Harry Potter dan Batu Bertuah\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Sebuah \"edisi\" bisa jadi:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Edisi pertama tahun 1997 diterbitkan oleh Bloomsbery dengan 256 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Edisi paperback tahun 2003 diterbitkan oleh Raincoast Books dengan 223 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Terjemahan Polandia tahun 2000 “Harry Potter I Kamie Filozoficzn” oleh Media Rodzina dengan 328 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Semua edisi tersebut memiliki ISBN dan konten yang berbeda, jadi pastikan untuk memilih yang benar!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edit catatan (atau buat jika belum ada), dan tambahkan sebanyak mungkin informasi yang berguna! Anda sudah di sini, jadi buatlah catatan tersebut benar-benar luar biasa."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Di bawah “ID Numbers” pilih “Anna’s Archive” dan tambahkan MD5 dari buku tersebut dari Anna’s Archive. Ini adalah rangkaian panjang huruf dan angka setelah “/md5/” di URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Cobalah untuk menemukan file lain di Anna’s Archive yang juga cocok dengan catatan ini, dan tambahkan juga. Di masa depan kita bisa mengelompokkan mereka sebagai duplikat di halaman pencarian Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Setelah selesai, tuliskan URL yang baru saja Anda perbarui. Setelah Anda memperbarui setidaknya 30 catatan dengan MD5 dari Anna’s Archive, kirimkan kami sebuah <a %(a_contact)s>email</a> dan kirimkan daftar tersebut. Kami akan memberikan Anda keanggotaan gratis untuk Anna’s Archive, sehingga Anda dapat lebih mudah melakukan pekerjaan ini (dan sebagai ucapan terima kasih atas bantuan Anda). Ini harus merupakan edit berkualitas tinggi yang menambahkan sejumlah besar informasi, jika tidak permintaan Anda akan ditolak. Permintaan Anda juga akan ditolak jika ada edit yang dibatalkan atau dikoreksi oleh moderator Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Perhatikan bahwa ini hanya berlaku untuk buku, bukan makalah akademis atau jenis file lainnya. Untuk jenis file lainnya kami tetap merekomendasikan mencari perpustakaan sumber. Mungkin perlu beberapa minggu untuk perubahan dimasukkan ke dalam Anna’s Archive, karena kami perlu mengunduh data dump terbaru dari Open Library, dan memperbarui indeks pencarian kami."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Cermin: panggilan untuk relawan"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Untuk meningkatkan ketahanan Arsip Anna, kami mencari relawan untuk menjalankan cermin."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Kami mencari ini:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Anda menjalankan basis kode sumber terbuka Anna’s Archive, dan Anda secara teratur memperbarui baik kode maupun data."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Versi Anda jelas dibedakan sebagai mirror, misalnya \"Arsip Bob, sebuah mirror Anna’s Archive\"."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Anda bersedia mengambil risiko yang terkait dengan pekerjaan ini, yang cukup signifikan. Anda memiliki pemahaman mendalam tentang keamanan operasional yang diperlukan. Isi dari <a %(a_shadow)s>postingan</a> <a %(a_pirate)s>ini</a> sudah jelas bagi Anda."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Anda bersedia berkontribusi pada <a %(a_codebase)s>basis kode</a> kami — dalam kolaborasi dengan tim kami — untuk mewujudkan hal ini."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Awalnya kami tidak akan memberi Anda akses ke unduhan server mitra kami, tetapi jika semuanya berjalan dengan baik, kami dapat membagikannya dengan Anda."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Biaya hosting"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Kami bersedia menanggung biaya hosting dan VPN, awalnya hingga $200 per bulan. Ini cukup untuk server pencarian dasar dan proxy yang dilindungi Controlled Digital Lending."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Kami hanya akan membayar hosting setelah Anda memiliki semuanya dan telah menunjukkan bahwa Anda mampu menjaga arsip tetap diperbarui dengan pembaruan. Ini berarti Anda harus membayar untuk 1-2 bulan pertama dari kantong Anda sendiri."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Waktu Anda tidak akan dikompensasi (dan begitu juga waktu kami), karena ini adalah pekerjaan sukarela murni."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Jika Anda terlibat secara signifikan dalam pengembangan dan operasi pekerjaan kami, kami dapat membahas pembagian lebih banyak pendapatan donasi dengan Anda, untuk Anda gunakan sesuai kebutuhan."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Memulai"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Tolong <strong>jangan hubungi kami</strong> untuk meminta izin, atau untuk pertanyaan dasar. Tindakan berbicara lebih keras daripada kata-kata! Semua informasi sudah tersedia, jadi langsung saja mulai dengan menyiapkan mirror Anda."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Jangan ragu untuk memposting tiket atau permintaan penggabungan ke Gitlab kami saat Anda menghadapi masalah. Kami mungkin perlu membangun beberapa fitur khusus mirror dengan Anda, seperti rebranding dari \"Anna’s Archive\" ke nama situs web Anda, (awalnya) menonaktifkan akun pengguna, atau menautkan kembali ke situs utama kami dari halaman buku."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Setelah Anda menjalankan mirror Anda, silakan hubungi kami. Kami akan senang meninjau OpSec Anda, dan setelah itu solid, kami akan menautkan ke mirror Anda, dan mulai bekerja lebih dekat dengan Anda."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Terima kasih sebelumnya kepada siapa pun yang bersedia berkontribusi dengan cara ini! Ini bukan untuk yang lemah hati, tetapi akan memperkuat umur panjang perpustakaan terbuka terbesar dalam sejarah manusia."

msgid "page.partner_download.header"
msgstr "Unduh lewat web rekan"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Unduhan jalur lambat hanya tersedia melalui halaman web resmi. Kunjungi %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Unduhan lambat tidak tersedia melalui VPN Cloudflare atau dari alamat IP Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Silakan tunggu <span %(span_countdown)s>%(wait_seconds)s</span> detik untuk mengunduh file ini."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Gunakan URL berikut untuk memgunduh: <a %(a_download)s>Unduh sekarang</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Terima kasih telah menunggu, ini menjaga situs web tetap gratis untuk semua orang! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Peringatan: telah terjadi unduhan terlalu banyak dari alamat IP kamu dalam kurun waktu 24 jam. Unduhan dapat menjadi lebih lambat dari biasanya."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Unduhan dari alamat IP Anda dalam 24 jam terakhir: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Jika Anda menggunakan VPN, koneksi internet bersama, atau ISP Anda berbagi IP, peringatan ini mungkin disebabkan oleh itu."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Untuk memberikan kesempatan kepada semua orang untuk mengunduh file secara gratis, Anda perlu menunggu sebelum dapat mengunduh file ini."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Silakan lanjutkan menjelajahi Arsip Anna di tab yang berbeda sambil menunggu (jika browser Anda mendukung penyegaran tab latar belakang)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Silakan tunggu beberapa halaman unduhan dimuat secara bersamaan (tetapi harap hanya unduh satu file pada satu waktu per server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Setelah Anda mendapatkan tautan unduhan, tautan tersebut berlaku selama beberapa jam."

msgid "layout.index.header.title"
msgstr "Anna's Archive"

msgid "page.scidb.header"
msgstr "SicDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Catatan di Arsip Anna"

msgid "page.scidb.download"
msgstr "Unduhan"

msgid "page.scidb.scihub"
msgstr "Sic-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Untuk mendukung aksesibilitas dan pelestarian jangka panjang pengetahuan manusia, jadilah <a %(a_donate)s>anggota</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Sebagai bonus, 🧬&nbsp;SciDB memuat lebih cepat untuk anggota, tanpa batasan."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Tidak berfungsi? Coba <a %(a_refresh)s>segarkan</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Pratinjau belum tersedia. Unduh file dari <a %(a_path)s>Arsip Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB adalah kelanjutan dari Sci-Hub, dengan antarmuka yang sudah dikenal dan tampilan langsung PDF. Masukkan DOI Anda untuk melihat."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Kami memiliki koleksi lengkap Sci-Hub, serta makalah baru. Sebagian besar dapat dilihat langsung dengan antarmuka yang sudah dikenal, mirip dengan Sci-Hub. Beberapa dapat diunduh melalui sumber eksternal, dalam hal ini kami menampilkan tautan ke sumber tersebut."

msgid "page.search.title.results"
msgstr "%(search_input)s - Cari"

msgid "page.search.title.new"
msgstr "Pencarian baru"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Hanya sertakan"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Kecualikan"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Belum diperiksa"

msgid "page.search.tabs.download"
msgstr "Unduhan"

msgid "page.search.tabs.journals"
msgstr "Artikel jurnal"

msgid "page.search.tabs.digital_lending"
msgstr "Digital Lending"

msgid "page.search.tabs.metadata"
msgstr "Metadata"

msgid "common.search.placeholder"
msgstr "Cari judul, penulis, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Cari"

msgid "page.search.search_settings"
msgstr "Opsi pencarian"

msgid "page.search.submit"
msgstr "Cari"

msgid "page.search.too_long_broad_query"
msgstr "Pencarian terlalu lama, kata kunci yang anda gunakan terlalu umum. Penyaringan kemungkinan kurang akurat."

msgid "page.search.too_inaccurate"
msgstr "Pencarian terlalu lama, kemumgkinan hasil yang kamu dapatkan kurang akurat. Terkadang <a %(a_reload)s>memuat ulang</a> halaman dapat membantu."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Tampilkan"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Daftar"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

msgid "page.search.advanced.header"
msgstr "Tingkat lanjut"

msgid "page.search.advanced.description_comments"
msgstr "Cari deskripsi dan komentar metadata"

msgid "page.search.advanced.add_specific"
msgstr "Tambahkan kriteria pencarian yang labih spesifik"

msgid "common.specific_search_fields.select"
msgstr "(kriteria pencarian spesifik)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Tahun diterbitkan"

msgid "page.search.filters.content.header"
msgstr "Isi"

msgid "page.search.filters.filetype.header"
msgstr "Jenis file"

msgid "page.search.more"
msgstr "Lebih lanjut…"

msgid "page.search.filters.access.header"
msgstr "Akses"

msgid "page.search.filters.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "diambil dan dibuka oleh AA"

msgid "page.search.filters.language.header"
msgstr "Bahasa"

msgid "page.search.filters.order_by.header"
msgstr "Urutkan berdasarkan"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Paling relevan"

msgid "page.search.filters.sorting.newest"
msgstr "Terbaru"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(tahun terbit)"

msgid "page.search.filters.sorting.oldest"
msgstr "Terlama"

msgid "page.search.filters.sorting.largest"
msgstr "Terbesar"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(ukuran dokumen)"

msgid "page.search.filters.sorting.smallest"
msgstr "Terkecil"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(sumber terbuka)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Acak"

msgid "page.search.header.update_info"
msgstr "Indeks pencarian diperbarui setiap bulan. Saat ini, di dalam indeks pencarian terdapat entri hingga %(last_data_refresh_date)s. Untuk informasi teknis lebih lanjut, lihat %(link_open_tag)slaman dataset</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Untuk menjelajahi indeks pencarian berdasarkan kode, gunakan <a %(a_href)s>Codes Explorer</a>."

msgid "page.search.results.search_downloads"
msgstr "Ketikan kata kunci pada kotak untuk pencarian di katalog kami dari %(count)s dokumen yang dapat langsung diunduh, yang telah kami <a %(a_preserve)s>lestarikan selamanya</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Faktanya, siapa pun dapat membantu melestarikan file-file ini dengan menanam <a %(a_torrents)s>daftar torrent terpadu kami</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Kami pada saat ini memiliki katalog terbuka terlengkap mencakup buku, artikel, dan karya tulis lainya. Kami melakukan mirror Sic-Hub, Library, Genesis, Z-Library, <a%(a_datasets)s>dll</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Bila anda mengetahui \"pustaka pribadi\" lain yang dapat kami mirror, atau bila anda memiliki pertanyaan,silahkan hubungi kami di %(email)s."

msgid "page.search.results.dmca"
msgstr "Untuk DMCA / klaim hak cipta <a %(a_copyright)s>klik disini</a>."

msgid "page.search.results.shortcuts"
msgstr "Tip: Untuk navigasi cepat gunakan tombol “/” (fokus pencarian), “enter” (cari), “j” (Ke atas), “k” (Ke bawah)."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Mencari makalah?"

msgid "page.search.results.search_journals"
msgstr "Ketikan dalam kotak untuk melakukan pencarian pada %(count)s jurnal dan artikel akademik dalam katalog kami, yang telah kami <a %(a_preserve)s>lestarikan untuk selamanya</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Ketikan kata kunci pada kotak, untuk pencarian dokumen di pustaka digital."

msgid "page.search.results.digital_lending_info"
msgstr "Indeks pencarian saat ini termasuk metadata dari pustaka Internet Archiev Controlled Digital Lending.<a %(a_datasets)s>Pelajari lebih lanjut dataset kami</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Untuk mempelajari pustaka digital lebih lanjut, kunjungi <a %(a_wikipedia)s>Wikipedia</a> dan <a %(a_mobileread)s>Wiki Mobail</a>."

msgid "page.search.results.search_metadata"
msgstr "Ketikkan kata kunci pada kotak untuk melakukan pencarian metadata dari pustaka. Ini dapat berguna saat <a %(a_request)s>permintaan dokumen</a>."

msgid "page.search.results.metadata_info"
msgstr "Indeks pencarian saat ini termasuk metadata dari beberapa sumber metadata. <a %(a_datasets)s>Pelajari lebih lanjut datasets kami</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Untuk metadata, kami menampilkan catatan dalam keadaan aslinya. Kami tidak melakukan pengabungan metadata."

msgid "page.search.results.metadata_info_more"
msgstr "Terdapat banyak sumber metadata untuk karya tulis dari penjuruh dunia. <a %(a_wikipedia)s>Halaman Wikipedia ini</a> merupakan permulaan yang baik, akan tetapi bila anda tahu list yang lain, tolong beri tahu kami."

msgid "page.search.results.search_generic"
msgstr "Ketikan kata kunci pada kotak untuk melakukan pencarian."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Ini adalah catatan metadata, <span %(classname)s>bukan</span> file yang dapat diunduh."

msgid "page.search.results.error.header"
msgstr "Kesalahan selama pencarian."

msgid "page.search.results.error.unknown"
msgstr "Coba <a %(a_reload)s>muat ulang halaman</a>. Bila masih bermasalah, mohon hubungi kami di %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Tidak ada file ditemukan.</span> Silahkan gunakan kata kunci lain atau filter pencarian Anda."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Terkadang ini terjadi secara tidak benar ketika server pencarian lambat. Dalam kasus seperti itu, <a %(a_attrs)s>memuat ulang</a> dapat membantu."

msgid "page.search.found_matches.main"
msgstr "Kami mendapatkan kesesuaian sebanyak: %(in)s. Kamu dapat menuju ke URL untuk <a%(a_request)s>memgajukan permintaan dokumen</a>."

msgid "page.search.found_matches.journals"
msgstr "Total Artikel Jurnal (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Total Digital Lending (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Total Metadata (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Hasil %(from)s-%(to)s (%(total)s total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ kecocokan sebagian"

msgid "page.search.results.partial"
msgstr "%(num)d kecocokan sebagian"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Sukarelawan & Hadiah"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive bergantung pada sukarelawan seperti Anda. Kami menyambut semua tingkat komitmen, dan memiliki dua kategori utama bantuan yang kami cari:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Pekerjaan sukarela ringan:</span> jika Anda hanya dapat meluangkan beberapa jam di sana-sini, masih ada banyak cara Anda dapat membantu. Kami menghargai sukarelawan yang konsisten dengan <span %(bold)s>🤝 keanggotaan di Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Kerja sukarela berat (hadiah USD$50-USD$5,000):</span> jika Anda dapat mendedikasikan banyak waktu dan/atau sumber daya untuk misi kami, kami ingin bekerja lebih dekat dengan Anda. Akhirnya Anda bisa bergabung dengan tim inti. Meskipun anggaran kami ketat, kami dapat memberikan <span %(bold)s>💰 hadiah uang</span> untuk pekerjaan yang paling intens."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Jika Anda tidak dapat menyumbangkan waktu Anda, Anda masih bisa membantu kami dengan <a %(a_donate)s>menyumbangkan uang</a>, <a %(a_torrents)s>menyebarkan torrent kami</a>, <a %(a_uploading)s>mengunggah buku</a>, atau <a %(a_help)s>memberitahu teman-teman Anda tentang Arsip Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Perusahaan:</span> kami menawarkan akses langsung berkecepatan tinggi ke koleksi kami sebagai imbalan untuk donasi tingkat perusahaan atau pertukaran koleksi baru (misalnya pemindaian baru, datasets OCR, memperkaya data kami). <a %(a_contact)s>Hubungi kami</a> jika ini Anda. Lihat juga <a %(a_llm)s>halaman LLM kami</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Sukarela ringan"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Jika Anda memiliki beberapa jam luang, Anda dapat membantu dengan berbagai cara. Pastikan untuk bergabung dengan <a %(a_telegram)s>obrolan sukarelawan di Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Sebagai tanda penghargaan, kami biasanya memberikan 6 bulan “Pustakawan Pemberuntung” untuk pencapaian dasar, dan lebih banyak lagi untuk kerja sukarela yang berkelanjutan. Semua pencapaian memerlukan pekerjaan berkualitas tinggi — pekerjaan yang ceroboh lebih merugikan kami daripada membantu dan kami akan menolaknya. Silakan <a %(a_contact)s>email kami</a> ketika Anda mencapai pencapaian."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tugas"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Pencapaian"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Menyebarkan informasi tentang Arsip Anna. Misalnya, dengan merekomendasikan buku di AA, menautkan ke posting blog kami, atau secara umum mengarahkan orang ke situs web kami."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s tautan atau tangkapan layar."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Ini harus menunjukkan Anda memberi tahu seseorang tentang Arsip Anna, dan mereka berterima kasih kepada Anda."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Meningkatkan metadata dengan <a %(a_metadata)s>menghubungkan</a> dengan Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Anda dapat menggunakan <a %(a_list)s>daftar masalah metadata acak</a> sebagai titik awal."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Pastikan untuk meninggalkan komentar pada masalah yang Anda perbaiki, agar orang lain tidak menduplikasi pekerjaan Anda."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s tautan dari catatan yang Anda perbaiki."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Menerjemahkan</a> situs web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Menerjemahkan sepenuhnya satu bahasa (jika belum hampir selesai)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Meningkatkan halaman Wikipedia untuk Arsip Anna dalam bahasa Anda. Sertakan informasi dari halaman Wikipedia AA dalam bahasa lain, dan dari situs web dan blog kami. Tambahkan referensi ke AA di halaman relevan lainnya."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Tautan ke riwayat suntingan yang menunjukkan Anda membuat kontribusi signifikan."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Memenuhi permintaan buku (atau makalah, dll) di forum Z-Library atau Library Genesis. Kami tidak memiliki sistem permintaan buku sendiri, tetapi kami mencerminkan perpustakaan tersebut, jadi membuat mereka lebih baik juga membuat Arsip Anna lebih baik."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s tautan atau tangkapan layar dari permintaan yang Anda penuhi."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Tugas kecil yang diposting di <a %(a_telegram)s>obrolan sukarelawan di Telegram</a>. Biasanya untuk keanggotaan, kadang-kadang untuk hadiah kecil."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Tugas kecil diposting di grup obrolan relawan kami."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Tergantung pada tugas."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Hadiah"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Kami selalu mencari orang dengan keterampilan pemrograman atau keamanan ofensif yang solid untuk terlibat. Anda dapat membuat dampak besar dalam melestarikan warisan umat manusia."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Sebagai ucapan terima kasih, kami memberikan keanggotaan untuk kontribusi yang solid. Sebagai ucapan terima kasih yang besar, kami memberikan hadiah uang untuk tugas-tugas yang sangat penting dan sulit. Ini seharusnya tidak dianggap sebagai pengganti pekerjaan, tetapi ini adalah insentif tambahan dan dapat membantu dengan biaya yang dikeluarkan."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Sebagian besar kode kami bersifat open source, dan kami akan meminta hal yang sama dari kode Anda saat memberikan hadiah. Ada beberapa pengecualian yang dapat kita diskusikan secara individual."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Hadiah diberikan kepada orang pertama yang menyelesaikan tugas. Jangan ragu untuk mengomentari tiket hadiah untuk memberi tahu orang lain bahwa Anda sedang mengerjakan sesuatu, sehingga orang lain dapat menahan diri atau menghubungi Anda untuk bekerja sama. Namun, perlu diketahui bahwa orang lain masih bebas untuk mengerjakannya juga dan mencoba mengalahkan Anda. Namun, kami tidak memberikan hadiah untuk pekerjaan yang ceroboh. Jika dua pengajuan berkualitas tinggi dibuat berdekatan satu sama lain (dalam satu atau dua hari), kami mungkin memilih untuk memberikan hadiah kepada keduanya, atas kebijaksanaan kami, misalnya 100%% untuk pengajuan pertama dan 50%% untuk pengajuan kedua (jadi total 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Untuk hadiah yang lebih besar (terutama hadiah scraping), harap hubungi kami ketika Anda telah menyelesaikan ~5%% dari itu, dan Anda yakin bahwa metode Anda akan dapat diukur hingga tonggak penuh. Anda harus membagikan metode Anda dengan kami sehingga kami dapat memberikan umpan balik. Juga, dengan cara ini kami dapat memutuskan apa yang harus dilakukan jika ada beberapa orang yang mendekati hadiah, seperti mungkin memberikannya kepada beberapa orang, mendorong orang untuk bekerja sama, dll."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "PERINGATAN: tugas dengan hadiah tinggi <span %(bold)s>sulit</span> — mungkin bijaksana untuk memulai dengan yang lebih mudah."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Pergi ke <a %(a_gitlab)s>daftar masalah Gitlab kami</a> dan urutkan berdasarkan “Label priority”. Ini menunjukkan urutan tugas yang kami pedulikan. Tugas tanpa hadiah eksplisit masih memenuhi syarat untuk keanggotaan, terutama yang ditandai “Accepted” dan “Favorit Anna”. Anda mungkin ingin memulai dengan “Proyek pemula”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Pembaruan tentang <a %(wikipedia_annas_archive)s>Arsip Anna</a>, perpustakaan terbuka terbesar dalam sejarah manusia."

msgid "layout.index.title"
msgstr "Anna's Archive"

msgid "layout.index.meta.description"
msgstr "Pustaka sumber terbuka terbesar di dunia. Alternatif dari Sci-Hub, Library Genesis, Z-Library, dkk."

msgid "layout.index.meta.opensearch"
msgstr "Cari di Arsip Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Arsip Anna membutuhkan bantuan Anda!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Banyak yang mencoba menjatuhkan kami, tetapi kami melawan."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Jika Anda berdonasi sekarang, Anda mendapatkan <strong>dua kali</strong> jumlah unduhan cepat."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Berlaku hingga akhir bulan ini."

msgid "layout.index.header.nav.donate"
msgstr "Donasi"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Menyelamatkan pengetahuan manusia: hadiah liburan yang luar biasa!"

msgid "layout.index.header.banner.surprise"
msgstr "Kejutkan orang tersayang, beri mereka akun dengan keanggotaan."

msgid "layout.index.header.banner.mirrors"
msgstr "Untuk meningkatkan ketahanan Arsip Anna, Kami sedang mencari relawan untuk menjalankan mirror."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Kado Valentin terbaik!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Tersedia cara donasi terbaru: %(method_name)s. Kami mohon pertimbangan Anda untuk %(donate_link_open_tag)sberdonasi</a> — menjalankan website ini tidaklah murah, dan donasi Anda akan sungguh membantu. Terima kasih banyak."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Kami sedang menjalankan pengalangan dana untuk <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">pencadangan</a> pustaka komik terbesar di dunia. Terimakasih atas dukungan kamu! <a href=\"/donate\">Donasi.</a> Jika kamu belum dapat berdonasi, kamu bisa bantu kami lewat rekomendasi ke temen kamu dan follow medsos kami di <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Unduhan terahir:"

msgid "layout.index.header.nav.search"
msgstr "Pencarian"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Perbaiki metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Sukarelawan & Hadiah"

msgid "layout.index.header.nav.datasets"
msgstr "Dataset"

msgid "layout.index.header.nav.torrents"
msgstr "Torrent"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktivitas"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Penjelajah Kode"

msgid "layout.index.header.nav.llm_data"
msgstr "Data LLM"

msgid "layout.index.header.nav.home"
msgstr "Beranda"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Terjemahkan ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Masuk/Daftar"

msgid "layout.index.header.nav.account"
msgstr "Akun"

msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

msgid "layout.index.footer.list2.header"
msgstr "Hubungi kami"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DCMA/klaim hak cipta"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Lanjutan"

msgid "layout.index.header.nav.security"
msgstr "Keamanan"

msgid "layout.index.footer.list3.header"
msgstr "Alternatif"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "tidak terafiliasi"

msgid "page.search.results.issues"
msgstr "❌ Berkas ini bermasalah."

msgid "page.search.results.fast_download"
msgstr "Unduhan cepat"

msgid "page.donate.copy"
msgstr "salin"

msgid "page.donate.copied"
msgstr "Telah disalin!"

msgid "page.search.pagination.prev"
msgstr "Sebelumnya"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Berikutnya"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Cermin #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% dari warisan tertulis umat manusia dilestarikan selamanya %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr ""

#~ msgid "page.md5.box.download.text"
#~ msgstr "Unduh ebook/file %(extension)s gratis dari:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr ""

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Mirror Anonim Library-Z #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Donasi"

#~ msgid "page.donate.header"
#~ msgstr "Donasi"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive adalah sebuah proyek open-source nirlaba yang digerakkan penuh oleh sukarelawan. Kami menerima donasi untuk memenuhi kebutuhan biaya, termasuk hosting, nama domain, pengembangan, dan biaya-biaya lain."

#~ msgid "page.donate.text2"
#~ msgstr "Dengan kontribusi Anda, kami dapat terus menjalankan situs ini, mengembangkan fitur-fiturnya, dan mempreservasikan lebih banyak koleksi."

#~ msgid "page.donate.text3"
#~ msgstr "Donasi terkini: %(donations)s. Terima kasih atas kedermawanan Anda. Kami sangat mengapresiasi kepercayaan Anda kepada kami, serta berapapun donasi yang dapat Anda sisihkan."

#~ msgid "page.donate.text4"
#~ msgstr "Untuk berdonasi, silakan pilih metode donasi yang Anda inginkan. Jika terjadi masalah, silakan hubungi kami di %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Kartu debit / kredit"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Uang kripto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Pertanyaan Umum"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Silahkan buka %(link_open_tag)slaman ini</a> dan ikuti petunjuk didalamnya dengan memindai kode QR atau meng-klik link \"paypal.me\". Jika tidak berhasil, cobalah me-refresh laman, karena mungkin Anda akan mendapatkan akun yang berbeda."

#~ msgid "page.donate.cc.header"
#~ msgstr "Kartu debit / kredit"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Kami menggunakan Sendwyre untuk menyetorkan uang langsung ke dompet Bitcoin (BTC) kami. Proses ini kira-kira membutuhkan waktu 5 menit."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Metode pembayaran ini memiliki jumlah minimal transaksi sebesar $30, dan biaya sebesar $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Langkah-langkah:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Salinlah alamat dompet Bitcoin (BTC) kami: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Buka %(link_open_tag)slaman ini</a> dan klik \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Tempelkan (paste) alamat dompet kami, dalan ikutilah petunjuk yang diberikan"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Uang kripto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(juga bisa digunakan untuk BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Gunakanlah %(link_open_tag)sakun Alipay ini</a> untuk mengirimkan donasi Anda. Jika tidak bekerja dengan baik, cobalah me-refresh laman untuk mencoba mendapatkan akun yang berbeda."

#~ msgid "page.donate.alipay.url"
#~ msgstr "Pranala"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Metode donasi ini sedang tidak berfungsi untuk sementara waktu. Mohon coba lagi nanti. Terima kasih sudah ingin berdonasi, kami sangat menghargainya!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Gunakanlah %(link_open_tag)slaman Pix ini</a> untuk mengirimkan donasi Anda. Jika tidak bekerja dengan baik, cobalah me-refresh laman untuk mencoba mendapatkan akun yang berbeda."

#~ msgid "page.donate.faq.header"
#~ msgstr "Pertanyaan Umum"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna's Archive</span> adalah sebuah proyek yang bertujuan untuk mengatalogkan seluruh buku yang pernah ada, dengan cara menyatukan agregat data dari berbagai sumber. Kami juga melacak progres sejauh mana umat manusia menjadikan semua buku tersedia secara mudah dalam bentuk digital, melalui \"<a href=\"https://id.wikipedia.org/wiki/Perpustakaan_bayangan\">perpustakaan bayangan (shadow libraries)</a>\". Pelajari lebih lanjut <a href=\"/about\">tentang kami.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Buku (apapun)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Halaman Utama"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Dataset ▶ ISBN ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Tidak ditemukan"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "\"%(isbn_input)s\" bukan merupakan nomor ISBN yang valid. ISBN memiliki panjang 10 atau 13 karakter, tanpa menghitung tanda hubung opsional. Semua karakter harus berupa angka, kecuali karakter terakhir, yang bisa jadi berupa \"X\". Karakter yang terakhir adalah\"digit pengecek (check digit)\", yang harus sesuai dengan nilai checksum yang dihitung dari angka-angka lainnya. Karakter ini juga harus berada dalam range yang benar, dialokasikan oleh International ISBN Agency (Agensi ISBN Internasional)."

#~ msgid "page.isbn.results.text"
#~ msgstr "Berkas-berkas yang sesuai di database kami:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Tidak ada berkas yang sesuai di database kami."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Pencarian ▶ %(num)d+ hasil untuk <span class=\"italic\">%(search_input)s</span> (dalam metadata perpustakaan bayangan)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Pencarian ▶ %(num)d hasil untuk <span class=\"italic\">%(search_input)s</span> (dalam metadata perpustakaan bayangan)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Pencarian ▶ Error pencarian untuk <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Pencarian ▶ Pencarian baru"

#~ msgid "page.donate.header.text3"
#~ msgstr ""

#~ msgid "page.donate.buttons.one_time"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Jika Anda sudah memiliki uang kripto, berikut alamat kami."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Terima kasih banyak! Proyek ini tidak akan terwujud tanpa bantuan dari Anda."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr "Cobalah <a href=\"javascript:location.reload()\">memuat ulang laman ini</a>. Jika masih tetap bermasalah, silakan hubungi kami melalui <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Halaman Utama"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Tentang"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donasi"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Dataset"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Aplikasi mobile"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Terjemahkan"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;termasuk isi dari %(libraries)s, dan banyak lagi."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Dataset ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Tidak ditemukan"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" tidak tampak seperti kode DOI. Kode DOI seharusnya dimulai dengan angka \"10.\" dan terdapat garis miring di dalamnya."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL Resmi: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Berkas ini mungkin terdapat di %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Berkas-berkas yang sesuai di database kami:"

#~ msgid "page.doi.results.none"
#~ msgstr "Tidak ada berkas yang sesuai di database kami."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Apakah saya bisa berkontribusi dengan cara lain?</div> Ya! Lihat laman <a href=\"/about\">tentang kami</a> pada bagian “Cara Membantu”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Saya tidak suka Anda “mencari keuntungan” dari Arsip Anna!</div> Jika Anda tidak menyukai cara kami menjalankan proyek kami, Anda bisa menjalankan perpustakaan sembunyi Anda sendiri! Semua kode dan data kami tersedia untuk umum (red: open source), jadi tidak ada halangan untuk Anda melakukannya. ;)"

#~ msgid "page.request.title"
#~ msgstr "Ajukan permintaan buku"

#~ msgid "page.request.text1"
#~ msgstr "Untuk saat ini, bisakah Anda meminta eBook di forum <a %(a_forum)s>Libgen.rs</a>? Anda dapat membuat akun di sana dan posting dalam salah satu utas berikut:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Untuk eBook, gunakan <a %(a_ebook)s>utas ini</a>.</li><li %(li_item)s>Untuk buku yang tidak tersedia dalam format eBook, gunakan <a %(a_regular)s>utas ini</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Dalam kedua kasus tersebut, pastikan untuk mengikuti aturan yang disebutkan dalam utas tersebut."

#~ msgid "page.upload.title"
#~ msgstr "Unggah"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Tentang"

#~ msgid "page.about.header"
#~ msgstr "Tentang"

#~ msgid "page.home.search.header"
#~ msgstr "Pencarian"

#~ msgid "page.home.search.intro"
#~ msgstr "Telusuri katalog perpustakaan bayangan kami."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr "Anna's Archive adalah mesin pencari bersifat non-profit dan open-source untuk menelusuri \"<a href=\"https://id.wikipedia.org/wiki/Perpustakaan_bayangan\">perpustakaan bayangan</a>\" (shadow library). Dikembangkan oleh <a href=\"http://annas-blog.org\">Anna</a>, seseorang yang merasa perlu adanya tempat terpusat untuk mencari buku, makalah, komik, majalah, dan dokumen lainnya."

#~ msgid "page.about.text4"
#~ msgstr "Jika Anda memiliki komplain DMCA yang sah, silahkan lihat bagian bawah laman ini, atau hubungi kami di %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Jelajahi buku"

#~ msgid "page.home.explore.intro"
#~ msgstr "Buku-buku ini adalah kombinasi dari buku-buku populer serta buku-buku yang penting di dunia perpustakaan bayangan dan preservasi digital."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Tentang"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Aplikasi mobile"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Cara Membantu"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Donasikan jumlah total sebesar %(total)s menggunakan <a %(a_account)s>akun Alipay ini"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Sebagai alternatif, kamu dapat menggungahnya melalui Z-Library<a %(a_upload)s>disini</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Untuk meningkatkan ketahanan Arsip Anna, kami saat ini sedang mencari relawan untuk menjalankan mirror. <a href=\"/mirrors\">Pelajari lebih lanjut…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "MIrror: panggilan untuk relawan"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "hanya bulan ini!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub telah <a %(a_closed)s>menghentikan sementara</a> pengunggahan makalah baru."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Pilih opsi pembayaran. Kami memberikan diskon untuk pembayaran berbasis kripto %(bitcoin_icon)s, karena kami mengalami (jauh) lebih sedikit biaya."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Pilih opsi pembayaran. Saat ini kami hanya menerima pembayaran berbasis kripto %(bitcoin_icon)s, karena pembayaran tradisional menolak untuk bekerja sama dengan kami."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Kami tidak dapat mendukung kartu kredit/debit secara langsung, karena bank tidak ingin bekerja sama dengan kami. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Namun, ada beberapa cara untuk menggunakan kartu kredit/debit, menggunakan metode pembayaran kami yang lain:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Jalur lambat melalui link download eksternal"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Unduhan"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Jika Anda baru pertama kali menggunakan kriptokurensi, kami menyarankan menggunakan %(option1)s, %(option2)s, atau %(option3)s untuk membeli dan mendonasikan Bitcoin (mata uang kripto asli dan paling banyak digunakan)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 tautan catatan yang Anda tingkatkan."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 tautan atau tangkapan layar."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 tautan atau tangkapan layar permintaan yang Anda penuhi."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Jika Anda tertarik untuk mencerminkan datasets ini untuk tujuan <a %(a_faq)s>arsip</a> atau <a %(a_llm)s>pelatihan LLM</a>, silakan hubungi kami."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Jika Anda tertarik untuk mencerminkan dataset ini untuk tujuan <a %(a_archival)s>arsip</a> atau <a %(a_llm)s>pelatihan LLM</a>, silakan hubungi kami."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Situs utama"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informasi negara ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Jika Anda tertarik untuk mencerminkan dataset ini untuk tujuan <a %(a_archival)s>arsip</a> atau <a %(a_llm)s>pelatihan LLM</a>, silakan hubungi kami."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Badan ISBN Internasional secara teratur merilis rentang yang telah dialokasikan ke badan ISBN nasional. Dari sini kami dapat mengetahui negara, wilayah, atau kelompok bahasa mana ISBN ini berasal. Saat ini kami menggunakan data ini secara tidak langsung, melalui pustaka Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Sumber daya"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Terakhir diperbarui: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Situs web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Mengecualikan “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Inspirasi kami untuk mengumpulkan metadata adalah tujuan Aaron Swartz untuk “satu halaman web untuk setiap buku yang pernah diterbitkan”, yang mana ia menciptakan <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Proyek itu telah berjalan dengan baik, tetapi posisi unik kami memungkinkan kami mendapatkan metadata yang tidak bisa mereka dapatkan."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Inspirasi lain adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku di dunia</a>, sehingga kami dapat menghitung berapa banyak buku yang masih harus kami selamatkan."

#~ msgid "page.partner_download.text1"
#~ msgstr "Untuk memberikan kesempatan kepada semua orang untuk mengunduh file secara gratis, Anda perlu menunggu <strong>%(wait_seconds)s detik</strong> sebelum Anda dapat mengunduh file ini."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Segarkan halaman secara otomatis. Jika Anda melewatkan jendela unduhan, pengatur waktu akan dimulai ulang, jadi penyegaran otomatis sangat disarankan."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Unduh sekarang"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konversi: gunakan alat online untuk mengonversi antara format. Misalnya, untuk mengonversi antara epub dan pdf, gunakan <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: unduh file (pdf atau epub didukung), lalu <a %(a_kindle)s>kirim ke Kindle</a> menggunakan web, aplikasi, atau email. Alat bantu: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Dukung penulis: Bila kamu suka karya ini dan mampu mendapatkanya, pertimbangkan untuk membeli karya tulis aslinya dan dukung penulis secara langsumg."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Dukung pustaka: bila proyek ini tersedia di perpustakaan daerah kamu mohon pertimbangkan meminjamnya secara geratis disana."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Tidak tersedia langsung dalam jumlah besar, hanya dalam jumlah semi-besar di balik paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Arsip Anna mengelola koleksi <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb adalah perusahaan yang mengumpulkan metadata ISBN dari berbagai toko buku online. Arsip Anna telah membuat cadangan metadata buku ISBNdb. Metadata ini tersedia melalui Arsip Anna (meskipun saat ini tidak dalam pencarian, kecuali jika Anda secara eksplisit mencari nomor ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Untuk detail teknis, lihat di bawah. Pada titik tertentu kita dapat menggunakannya untuk menentukan buku mana yang masih hilang dari perpustakaan bayangan, untuk memprioritaskan buku mana yang harus ditemukan dan/atau dipindai."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Posting blog kami tentang data ini"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Pengumpulan ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Saat ini kami memiliki satu torrent, yang berisi file <a %(a_jsonl)s>JSON Lines</a> 4,4GB yang dikompresi gzip (20GB tidak dikompresi): \"isbndb_2022_09.jsonl.gz\". Untuk mengimpor file \".jsonl\" ke PostgreSQL, Anda dapat menggunakan sesuatu seperti <a %(a_script)s>skrip ini</a>. Anda bahkan dapat mengalirkannya langsung menggunakan sesuatu seperti %(example_code)s sehingga terdekompresi secara langsung."

#~ msgid "page.donate.wait"
#~ msgstr "Mohon menunggu hingga <span %(span_hours)s>dua jam kemudian</span> (dan muat ulang halaman ini) sebelum menghubungi kami."

#~ msgid "page.codes.search_archive"
#~ msgstr "Cari Arsip Anna untuk “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Mendonasikan menggunakan Alipay atau WeChat. Anda dapat memilih di antara keduanya di halaman berikutnya."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Menyebarkan berita tentang Arsip Anna di media sosial dan forum online, dengan merekomendasikan buku atau daftar di AA, atau menjawab pertanyaan."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Koleksi Fiksi telah berbeda tetapi masih memiliki <a %(libgenli)s>torrent</a>, meskipun tidak diperbarui sejak 2022 (kami memiliki unduhan langsung)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Arsip Anna dan Libgen.li secara kolaboratif mengelola koleksi <a %(comics)s>buku komik</a> dan <a %(magazines)s>majalah</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Tidak ada torrent untuk koleksi fiksi Rusia dan dokumen standar."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Tidak ada torrent yang tersedia untuk konten tambahan. Torrent yang ada di situs web Libgen.li adalah mirror dari torrent lain yang tercantum di sini. Satu pengecualian adalah torrent fiksi yang dimulai dari %(fiction_starting_point)s. Torrent komik dan majalah dirilis sebagai kolaborasi antara Arsip Anna dan Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Dari koleksi <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> asal usulnya tidak jelas. Sebagian dari the-eye.eu, sebagian dari sumber lain."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

