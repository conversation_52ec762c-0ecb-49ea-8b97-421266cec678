��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b ~  sd   �g -   j x  5j �  �k �  �n O  eq �  �s d   �u v   �u G   lv m   �v r  "w -  �y b  �{ �  &} �  � �  p� �  l� �  V� h  8� �  �� Y  B� O   �� n  � X   [� �  �� (   �� �  �� G   �� B   �    3� <   L� t   �� 4   ��    3� 5   L� +   �� 1   ��    �� d   �� �  ^� �  :� �   � X   l� 
   ş    П    ܟ    �� !    �    "� 
   2�    @�    ^� ,   e�    �� *   �� 	   Ġ 
   Π    ܠ &   �� +   �    I� �  i� �  � �   ަ -   �� 0  �� �   � �  � )  �� $   �� �   � $   ��   ® )   ȯ �  � $   u� E  �� >   � �   � 
   �� �  �� �  �� �  ~� �   �    м c   � x   G� �   �� ;   �� ?   � �   0� Q   � a   :� ?   ��    �� �   �� �  �� n   5� �   �� 6  j� |   ��   � Q   �� �  �� �   �� �   f� z   ^� C  �� �   � G   �� �  $� (  �� �   � G   �� �   ��    �� 
  �� �  �� G   Y� 
   ��   �� `  ��    +�   4� �   7� "  "� L  E� �   ��   �� �  �� e   /� y   �� }   � �   ��    � �   �� 8   7� �   p� �   2� w   ��    :� �   U� t   � (  �� �   ��    �� �  �� �  �� q  ,� !  �� �   �� K  �� �  �� �   }� �   q� �   M� �  2� �   �� �  ��    � �   � )  j �   � �  � �   I �   � �   � Q  � �   �
 �  � �  6
 7   � �    �   � |   V �  � 6  � �   � �   � 4   m   � F  � H  	 *   R �   } �    <  � �  ; /  �  �  �! a  �$ H   =' f  �' �  �) �  �- �  �/ �  n3 �  ^5 �   7 �   �7 
   o8   z8 �  �9 R  U; �  �= e  H?   �@ 
   �A J  �A   D }  9F    �G   �G �  �H c  �J    L O  O �   `P    �P ?  �P =  7S    uU g  �U �  �V �  �X �  %\   �] �   �_ �   i` d  a <   mb    �b �  �b �  �d �  lf Z   *h G   �h �  �h �   �k �   �l �   om �  �m *   �p    �p b  �p )  7s �   at u   u �   �u C   ,v �  pv L  x    fy    yy 9   �y @   �y X   z P   \z s   �z �   !{    �{ @   �{ j   +| |   �| 1   } Z   E} J   �} �   �}    z~ (   �~ �  �~   K� ]  S� s  �� �   %� \  �� �  W�    :� ^  X� �   �� �   �� �  �� G  �� M  ې �  )� �  � G   �� S   � /   :� "  j� �  �� X  �� �  �   �� �  ܤ S  {� y  ϩ B   I� �  ��   {� u  ��    � m  "� �  �� �   ��   ^� x  a�    ڹ =   � �   '�    �� k  � ^  o� �  �� �   V� L  P�    �� �  �� �  J� G   � |   ^� �  �� t  |� �   �� b  �� �   >� �  ��    g� ^  ��   �� 
  �� �  �� �  ��   Z� 
   ^� J  i� �  ��    o� P  �� �  �� <  �� "  �� �  �   �� G   �� �  ,� I  �� �   �� b   �� �   �� 
   �� F   �� #   �� 5   � +   F�    r�   �� ?  ��   � �   M  �    �	 �  �	 �  � ?  g v  � �   �  � 
   � �  �    7     H  �  ]  o  ?#   �& Q  �) 
    -   .- �  B0 j  %3 �  �6 �  ,: x  �> /  pB #   �D 0  �D �   �E �  �F �  kK J  BN �   �O   _P k  vQ    �S :  �S }   :U k   �U !   $V B   FV j   �V �  �V �  �Z j  K] X   �a �  b �   �f 
   �g $  �g [   �h �   'i K  �i >   $k �   ck    l \  l �  {n �  'r c  u �   yw    -x �   @x �  �x    �z   �z @  �{ 0  � _   H� j   �� =  � -   Q� f  � Y  � �  @� 2   .� �   a� �   7�   �� {  �    �� P  �� 7   ��   4�   I� u  ^� H  ԙ |   � -   �� o  ț J   8� -  ��   �� M  ƣ �  � o   �� \   
� �   j� �  [� f  �   N� �  S� I   C� 0  �� �  �� �  e� *  F�   q� g  �� -   � �  � F   � |  X� �   �� "   g� -  �� $  ��   �� �  �� �  �� �  %� �  	� �   �� �  �� �  |� �   H� �  0�   �� P   �� �   4� �   � .   ��    �� �  � �   �� V   �� �  � �  �� s  @� �  �� [  ]� ,  �� �  �� �  �� !   �� 5  ��    �� �  �� (   | '   �    �    � 1   �     !    B    _    z    �    � 
   �    �    � (   �    	 7       H    O    [    d Y  m o   � (   7    ` :   t 4   � P   � [   5 (   � 
   �    �    �     �        -    C    P    f    m 8   � #   � !   � 0    7   9 4   q B   �    � ;   	 P   ?	    �	 �   �	 V   7
    �
 y   �
 i       y    �     �     � "   �    
 $   '    L    g    o    �     �    �    �    �    � /   �    
    $
 	   -
 #   7
 	   [
 '   e
    �
    �
 	   �
    �
    �
 &   �
    �
 !   �
        /    7 	   R    \ .   o    �    � *   �    �    � 
    "    
   6    A    X �   r    �   
 @  � "  � �        � )   �    �    �            * N   < �   � q   $ �   � 0   / P   ` ;   � �   � �   w a   �   { g    ,   �    �    �    �    �    � !       *    3    O    a %   s    �    � #   �    � !   �        *    D    M    \    k    �    � ^  �              *      0     Q  ]   X  n   �  E   %! G   k! T   �!    "    "    " �   "    �"    �" 6   �" �    # /   �#    �# �   $ 3   �$ �  % �   �) �   y*   K+ j  h, W   �- �  +. �   �/ �  G0 A  2 !   U3    w3 w   �3 �   �3 l   �4 e   �4 �   X5 T   �5 �   <6 +   �6 ;   7    W7 '   i7 n   �7 "    8    #8    28    O8 $   b8 �   �8    99 K   I9 u   �9 +   : +   7: h   c: �   �: N  �; !   �=    > �   $> 
   ?    #? 
   ,? #   :?    ^? 8   p? �  �? "   �A 
   �A    �A    �A �  �A '   �C    �C 
   �C {   �C    oD 
   vD C   �D *   �D    �D -   �D �   'E (   �E    F {   /F #   �F    �F    �F "   �F H   G �   _G    �G d   H �   wH �   `I p   �I    kJ �  �J x   L    �L 9   �L    �L   �L   N %   (O w   NO   �O    �P    �Q #   R    4R �  OR .   �T 3   U 4   8U 6   mU ;   �U �   �U    �V %   �V 4   W _   RW 
   �W    �W "   �W 6   �W   4X U  @[ $  �] N   �` K   
a    Va )   ca �  �a v  c 
  �d 2   �e @  �e E  g /   Ji ,   zi �  �i �  Hm    �n +    o 5   ,o 
   bo 8  mo ,   �q   �q �  �s [  lv    �w �   �w F   �x -   �x �    y �  �y m  8{ �   �| /  �} �   � \  m� x   ʁ �   C�   6� j   P� �   �� G   �� 1   ��    1�    A�     S� /   t� )   �� ]   Ά O   ,� 	   |� A   �� �  ȇ F   S� C  �� �   ފ �   �� 3   f� '   ��        ܌ 3   � .   #� :   R� 2   �� E  �� 9   � C  @�    ��   �� �   �� �   K� �  ��    �� +   �� �   �� 	   �� �  �� �   �� #   S� j  w�    �    �    
� *   !� /   L� 
   |� 
   �� g   �� �   �� x  � 
   e�    p� $   u� �   �� �  ��   ;� �   H�    ��    � #   0� !   T�    v�    ��    �� R   �� =   
� �  H� k   6�    �� �   ¨ �   M� n   �� �   i� �   � s   ��    � �   � h   �� �   � �   �� m   U�    î &  ׮ �   �� I   �� z   а ~   K� C   ʱ    � >    � �   _� �   � Y   �� �   � 
   � �  �� t   �� �   j� �   �    �� �  �� d  l�    Ѽ 4   � 
    �    +� �  2� 2   !� |   T� &  ѿ �   �� �   �� *  �� �   �� �   �   :� �   ?� �  �� �   c�   �� �   �� E  �� 
   7� 
   E� 
   S� �   a� 
   .� 
   <� d   J� �   �� =  1� 
   o� �   }� T   =� �   �� @   8� �   y� m   
� 
   x� �   �� 
   1� 
   ?� 
   M� �  [� �   B� d  ��    [�    y�    �� �   �� *   �� 6   �� T  �� �   N� ,   �    <� *   L� I   w� J   �� S   � %   `� %   �� x  �� +   %� �  Q� d  �� �   M� [   �� �   9� �   � �  �� !  >�   `� �   c� �   +�    ��   �     � 6  =� D  t� �  �� �   \� �  �    �� �   �� �   �� 5   O� X   ��    �� *   ��    �    .�    I� T  i�    �� �   �� C   �     �     �     �  ,   �  �   & x   � l   Q h  � L   '    t    �    � +   �    � 
   �    � 
        
   !    / 
   < `   J �   �    V    g    ~    �    �    �    �    �     #   # %   G �   m    � <    �   H �   �   �	 �   �
 �  �   �
 
  �    � �   � <   � s   � b   ; �   � e  ~ K   � �   0    � �   � �   � �   E    �     �        6    S 2   e    � +   � 
   �    �    � *       H #   d +   �    �    �    �    � .   �     ?   * A   j �   � �   j :   1 -   l �   � �   B �   � 8   e 4   � ;   � \    L   l ;   � K  � �  A! �  �" 8   U$ n   �$ �   �$ ;   �% {  & `  ~' �   �( q   �) (   9* 2  b* �   �+ �   �, 7   3- �   k- �  . 6   �/ Y   �/ 7   #0 y   [0 �   �0 �   �1    E2    T2 
   d2 @   o2 �   �2 F   �3 :   �3 7   4 3   @4 8   t4 $   �4 U   �4    (5 �   F5 G   �5 �  6 x   �7 �   8 ,  �8 L   �9 *   : )   B: *   l: )   �: *   �: )   �: *   ; a   A; N   �; �  �; w   ~? P   �? R   G@ P   �@    �@ �   �@ �   �A *  �B :  �C 	   �D �   �D /   �E    �E I  F -   ZG L   �G \   �G 9   2H l   lH �   �H `   cI *   �I 2   �I   "J 6   2K 5   iK y   �K N  L \   hM �   �M |   �N �   2O �   �O 4   �P Q   �P �    Q #   �Q �   �Q I   �R :  �R B   %T j   hT B   �T 6   U r  MU w   �V Y   8W �   �W �   �X e   1Y �   �Y �   }Z    Z[ 0   n[ �   �[ w   ?\    �\ =   �\    ]    #]    6] /   Q] �   �] t   ^ .   �^ <   �^ E   �^ /   C_ L   s_ �   �_ L   �` �   �` �   �a B   �b �   �b w  qc ;   �d a   %e    �e v   �e U   f    qf �   �f �   ig 6   h t   Nh    �h ;   �h a   i    {i �   �i +   Ij    uj .   �j �   �j 7  Pk \   �l    �l    m \   %m    �m t   �m U   n    mn �   �o    (p R   Cp )  �p ,   �q   �q P   �r 5   Ns w   �s N  �s 	   Ku    Uu    Wu    Yu 1   wu J   �u �   �u -   {v    �v    �v /   �v Z   �v \   Nw    �w c   �w u   x    �x N   �x g   �x    Xy %   wy �   �y b  Cz z   �{    !|    =| �   T| �  (} �   �~ !   t l  � �  � R   � �   6� 9   ȅ �  � h   �� �   $� /   �� )   � �  � :   � �   � �   �� v   <� �   �� 1   Y� �   �� \   @� ;   �� �   ّ [   \� 4   �� [   � ;   I� p   �� �   ��   Ȕ S   ԕ   (� &  7� .  ^� P   �� F  ޚ �  %� E  �� �  �� E  �� &   � 4   +� 
  `� 5   n� A  �� �   � ^   ��    �� ,   � �  9�    �   � �  &� �  ��   �� ^   �� �   �� �   �� I   � V   h� v   �� �   6�    ̵ =   ݵ U   � :   q� (   �� L   ն �   "� G   �� 
   �� �   � �  �� �   �    �    &�    >� g   G� ~   �� �   .�   �� �   �� *   K� +   v� �  ��    1� �   C�   � h  4� �   �� /    �    P� 
   V�    a� Y   e� >   �� �   �� e  �� }   �    ��    �� (   �� $   �� �   � +   �� R   ��    '� R   ;� T   ��    �� 
   �� _   
� 
   j� /   u� :   �� &   ��    � �   � P  �� �   � �   �� �   �� >  �    P�    ]�   }�   ��   ��    �� �   �� �   �� g   � �   �� �   <� �   �� !   �� �   ��    I�    _�    w�    �� #   ��    ��    �� *   ��    (� 
   =� L   H� L   �� L   �� )   /� i   Y� ^   �� 3   "� $   V� (   {� ]   �� '   �    *� h   7� 4   �� �   �� ;   ��     ��    �� #   �� #   �     C� �   d�    �� �   w� 8  %�    ^�    |� 6   �� .   �� g    � 	   h�    r�    �� �   ��     �� $   ��    �� 
   �� 	   �� b   �� *   J� �  u� #   !� &   E�    l� %   �� %   �� %   �� &   �� /    � 9   P� >   �� )   ��    �� �   �� /   �� %   ��    �� ?   �� z   >� '   �� 1   �� 5   � �   I� �   "� q   ��    )� ,   6� 	   c�    m� .   �� -   �� �  �� �   s�    h�    �� 3   ��    �� 4   ��    �    � �   '� Z   �� o  K� #   �� -   �� �   
� -   �� )   � -   ;� 0   i� B   �� <   ��    � 
   8� B   F� (   �� I   �� M   ��   J� s   ]� `   �� t   2� .   �� �   �� -   ��     � |   �    �� >   �� *   �� ^     s   z  =   �    ,    F    _ )   z    �    �    � (   �        2 r   K �  � 0   X B   � v  � �   C 1   ; !   m    �    �    �    �    �    �    
	    	    8	    G	 
   X	    f	    u	    �	    �	    �	 /   �	 B  �	 �  # F  � �  � �  � 2  { �  � 
   [ |  i #   � �  
 d  � �  * A  � ;    G   T" �   �" T   �# +   �# r   $ ~   t$ �   �$ �   t%   & �   "' 4  �' �  %)    �,   �, �  �- �   �/ �   .0    1 Z  -1 <  �2 �  �4 &  Q6    x7 �   �7 �   8 j  �8   -: �   :; �   �; 7   �< $   �<    = k   /= ^   �=    �= �   > Z   �> �   ? <   �?   �? �   �@ �   �A �   jB n   C ~   {C �   �C �   {D �   E �   �E �   oF �   *G    H R   "H T   uH �   �H B   sI 
   �I    �I X   �I (   )J    RJ    pJ _   J n   �J e   NK (   �K    �K    �K    L 	   L �   L �   �L }   2M N   �M    �M 5   N 9   =N    wN    �N 
   �N    �N 
   �N    �N    �N    �N    �N    �N    O #   O    :O    LO    bO    vO    �O    �O    �O 
   �O =   �O $   P #   1P �   UP    
Q �   +Q �   �Q    �R    �R    �R    �R 
   �R    S 
   S �   S �   T _   �T    U    U �   =U    �U �   V �   �V 7   �W %   �W �   X C  �X �   �Y   �Z �   �[ 3   "\ 
  V\    a]    y] k   �] �   �] 0   �^ �   _ �   ` �   �` �   �a    @b    _b    hb    �b    �b    �b    �b    �b �   �b �   �c �   ld "  <e n  _f �   �g y   kh �  �h   �k �  �m �   Mo �  &p �  �q 
   �s H  �s �  u &  �v �  �w 8  �{ �   �~   �    �� \   ́ �   *� (  � n   @� �  �� 
   5�    C�    H� �   e� S   5� �   �� I   7� �   �� c   � �   �� .   /�   ^� s   y� 9   � \   '� 3  �� %   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: sd
Language-Team: sd <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library هڪ مشهور (۽ غير قانوني) لائبريري آهي. انهن Library Genesis جي مجموعي کي وٺي ان کي آساني سان ڳولا لائق بڻايو آهي. ان کان علاوه، انهن نون ڪتابن جي تعاون کي ترغيب ڏيڻ سان نون ڪتابن جي تعاون کي تمام اثرائتو بڻايو آهي. هن وقت اهي نون ڪتابن کي واپس Library Genesis ۾ شامل نه ٿا ڪن. ۽ Library Genesis جي برعڪس، اهي پنهنجي مجموعي کي آساني سان آئيني بڻائڻ لائق نه ٿا بڻائين، جيڪو وسيع تحفظ کي روڪي ٿو. اهو انهن جي ڪاروباري ماڊل لاءِ اهم آهي، ڇو ته اهي پنهنجي مجموعي تائين بلڪ ۾ رسائي لاءِ پئسا چارج ڪن ٿا (روزانو 10 کان وڌيڪ ڪتاب). اسان غير قانوني ڪتابن جي مجموعي تائين وڏي پيماني تي رسائي لاءِ پئسا چارج ڪرڻ بابت اخلاقي فيصلو نه ٿا ڪريون. اهو بلاشبہ آهي ته Z-Library علم تائين رسائي کي وڌائڻ ۽ وڌيڪ ڪتابن کي حاصل ڪرڻ ۾ ڪامياب ٿي چڪو آهي. اسان هتي صرف پنهنجو حصو ادا ڪرڻ لاءِ آهيون: هن نجي مجموعي جي ڊگهي مدي واري حفاظت کي يقيني بڻائڻ. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>) Pirate Library Mirror جي اصل رليز ۾ (ترميم: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>Anna’s Archive</a> تي)، اسان Z-Library جو هڪ آئيني ٺاهيو، جيڪو هڪ وڏو غير قانوني ڪتاب مجموعو آهي. ياد ڏيارڻ لاءِ، هي اهو آهي جيڪو اسان ان اصل بلاگ پوسٽ ۾ لکيو: اهو مجموعو 2021 جي وچ تائين واپس ٿيو. ان دوران، Z-Library حيرت انگيز رفتار سان وڌي رهيو آهي: انهن تقريباً 3.8 ملين نوان ڪتاب شامل ڪيا آهن. اتي ڪجهه نقل موجود آهن، پر ان جو اڪثريت جائز طور تي نوان ڪتاب لڳي ٿو، يا اڳ ۾ جمع ڪيل ڪتابن جا اعليٰ معيار جا اسڪين. اهو وڏي حصي ۾ Z-Library ۾ رضاڪارانه منتظمين جي وڌندڙ تعداد جي ڪري آهي، ۽ انهن جو بلڪ اپلوڊ سسٽم ڊپليڪيشن سان. اسان انهن کي انهن ڪاميابين تي مبارڪباد ڏيڻ چاهيون ٿا. اسان کي خوشي آهي ته اسان Z-Library ۾ شامل ڪيل سڀ ڪتاب حاصل ڪيا آهن جيڪي اسان جي آخري آئيني ۽ آگسٽ 2022 جي وچ ۾ شامل ڪيا ويا هئا. اسان پڻ واپس وڃي ڪجهه ڪتابن کي ڇڪيو آهي جيڪي اسان پهريون ڀيرو ياد ڪيا هئا. مجموعي طور تي، هي نئون مجموعو تقريباً 24TB آهي، جيڪو گذريل هڪ کان گهڻو وڏو آهي (7TB). اسان جو آئيني هاڻي مجموعي طور تي 31TB آهي. ٻيهر، اسان لائبريري جينيسس جي خلاف ڊپليڪيشن ڪئي، ڇاڪاڻ ته ان مجموعي لاءِ اڳ ۾ ئي ٽورنٽ موجود آهن. مهرباني ڪري نئين مجموعي کي چيڪ ڪرڻ لاءِ Pirate Library Mirror ڏانهن وڃو (ترميم: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a>). اتي وڌيڪ معلومات موجود آهي ته فائلون ڪيئن جوڙيل آهن، ۽ گذريل وقت کان ڇا تبديل ٿيو آهي. اسان هتي کان ان کي لنڪ نه ڪنداسين، ڇاڪاڻ ته هي صرف هڪ بلاگ ويب سائيٽ آهي جيڪا ڪنهن به غير قانوني مواد جي ميزباني نه ڪندي آهي. يقيناً، سيڊنگ پڻ اسان جي مدد ڪرڻ جو هڪ بهترين طريقو آهي. مهرباني هر ڪنهن جو جيڪو اسان جي گذريل ٽورنٽ سيٽ کي سيڊ ڪري رهيو آهي. اسان مثبت جواب لاءِ شڪرگذار آهيون، ۽ خوش آهيون ته اتي ڪيترائي ماڻهو آهن جيڪي علم ۽ ثقافت جي حفاظت بابت هن غير معمولي طريقي سان پرواهه ڪن ٿا. Pirate Library Mirror ۾ 3x نوان ڪتاب شامل ڪيا ويا (+24TB، 3.8 ملين ڪتاب) TorrentFreak جا ساٿي مضمون پڙهو: <a %(torrentfreak)s>پهريون</a>, <a %(torrentfreak_2)s>ٻيو</a> - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak جا ساٿي مضمون: <a %(torrentfreak)s>پهريون</a>, <a %(torrentfreak_2)s>ٻيو</a> ڪجهه وقت اڳ، "شيڊو-لائبريريون" ختم ٿي رهيون هيون. Sci-Hub، اڪيڊمڪ مقالن جي وڏي غير قانوني آرڪائيو، قانوني ڪيسن جي ڪري نوان ڪم وٺڻ بند ڪري ڇڏيو هو. "Z-Library"، ڪتابن جي سڀ کان وڏي غير قانوني لائبريري، ان جي مبينا تخليقڪارن کي مجرمانه ڪاپي رائيٽ الزامن تي گرفتار ڪيو ويو. اهي حيرت انگيز طور تي پنهنجي گرفتاري کان بچي ويا، پر انهن جي لائبريري اڃا به خطري ۾ آهي. ڪجهه ملڪ اڳ ۾ ئي ان جو هڪ نسخو ڪري رهيا آهن. TorrentFreak <a %(torrentfreak)s>رپورٽ ڪيو</a> ته چين ۽ جاپان پنهنجي ڪاپي رائيٽ قانونن ۾ AI استثنا متعارف ڪرايا آهن. اسان لاءِ اهو واضح ناهي ته اهو بين الاقوامي معاهدن سان ڪيئن لاڳو ٿئي ٿو، پر اهو يقيناً انهن جي گهريلو ڪمپنين کي تحفظ ڏئي ٿو، جيڪو اسان ڏسي رهيا آهيون ان جي وضاحت ڪري ٿو. جيئن ته Anna’s Archive — اسان اخلاقي يقين سان جڙيل پنهنجي زميني ڪم کي جاري رکنداسين. پر اسان جي وڏي خواهش آهي ته اسان روشني ۾ اچون، ۽ قانوني طور تي اسان جي اثر کي وڌايون. مهرباني ڪري ڪاپي رائيٽ کي سڌاريو. جڏهن Z-Library کي بند ٿيڻ جو خطرو هو، مون اڳ ۾ ئي ان جي سڄي لائبريري کي بيڪ اپ ڪري ڇڏيو هو ۽ ان کي رکڻ لاءِ هڪ پليٽ فارم ڳولي رهيو هوس. اهو ئي منهنجو محرڪ هو Anna’s Archive شروع ڪرڻ جو: انهن اڳوڻين قدمن جي مشن کي جاري رکڻ. اسان ان کان پوءِ دنيا جي سڀ کان وڏي شيڊو لائبريري بڻجي چڪا آهيون، 140 ملين کان وڌيڪ ڪاپي رائيٽ ٿيل متنن کي مختلف فارميٽس ۾ ميزباني ڪندي - ڪتاب، اڪيڊمڪ مقالا، ميگزين، اخبارون، ۽ وڌيڪ. منهنجي ٽيم ۽ مان نظرياتي آهيون. اسان يقين رکون ٿا ته انهن فائلن کي محفوظ ڪرڻ ۽ ميزباني ڪرڻ اخلاقي طور تي صحيح آهي. دنيا جي لائبريريون فنڊنگ ۾ ڪٽوتيون ڏسي رهيون آهن، ۽ اسان انسانيت جي ورثي تي ڪمپنين تي به ڀروسو نٿا ڪري سگهون. پوءِ AI آيو. تقريباً سڀئي وڏيون ڪمپنيون جيڪي LLMs ٺاهي رهيون آهن، اسان سان رابطو ڪيو ته اسان جي ڊيٽا تي تربيت حاصل ڪن. گهڻا (پر سڀ نه!) آمريڪا ۾ ٻڌل ڪمپنيون ٻيهر غور ڪرڻ لڳيون جڏهن انهن اسان جي ڪم جي غير قانوني نوعيت کي محسوس ڪيو. ان جي برعڪس، چيني ڪمپنين اسان جي مجموعي کي جوش سان قبول ڪيو، بغير ان جي قانوني حيثيت جي پرواهه ڪرڻ جي. اهو قابل ذڪر آهي، ڏنو ويو ته چين تقريباً سڀني وڏن بين الاقوامي ڪاپي رائيٽ معاهدن جو دستخط ڪندڙ آهي. اسان تقريباً 30 ڪمپنين کي تيز رفتار رسائي ڏني آهي. انهن مان گهڻا LLM ڪمپنيون آهن، ۽ ڪجهه ڊيٽا بروڪرز آهن، جيڪي اسان جي مجموعي کي ٻيهر وڪرو ڪندا. گهڻا چيني آهن، جيتوڻيڪ اسان آمريڪا، يورپ، روس، ڏکڻ ڪوريا، ۽ جاپان جي ڪمپنين سان به ڪم ڪيو آهي. DeepSeek <a %(arxiv)s>اعتراف ڪيو</a> ته هڪ اڳوڻو ورزن اسان جي مجموعي جي حصي تي تربيت حاصل ڪئي هئي، جيتوڻيڪ اهي پنهنجي جديد ماڊل بابت خاموش آهن (شايد اهو به اسان جي ڊيٽا تي تربيت حاصل ڪئي آهي). جيڪڏهن مغرب LLMs جي دوڙ ۾ اڳتي رهڻ چاهي ٿو، ۽ آخرڪار، AGI ۾، ان کي ڪاپي رائيٽ تي پنهنجي موقف تي ٻيهر غور ڪرڻ جي ضرورت آهي، ۽ جلد. ڇا توهان اسان سان اخلاقي معاملي تي متفق آهيو يا نه، اهو هاڻي هڪ معاشي معاملو بڻجي رهيو آهي، ۽ اڃا تائين قومي سلامتي جو. سڀئي طاقت بلاڪ مصنوعي سپر-سائنسدان، سپر-هيڪرز، ۽ سپر-ملٽريز ٺاهي رهيا آهن. معلومات جي آزادي انهن ملڪن لاءِ بقا جو معاملو بڻجي رهي آهي - اڃا تائين قومي سلامتي جو معاملو. اسان جي ٽيم دنيا جي هر ڪنڊ کان آهي، ۽ اسان جو ڪو خاص لاڳاپو ناهي. پر اسان انهن ملڪن کي همٿائينداسين جن وٽ مضبوط ڪاپي رائيٽ قانون آهن ته اهي هن وجودي خطري کي انهن کي سڌارڻ لاءِ استعمال ڪن. ته پوءِ ڇا ڪجي؟ اسان جي پهرين سفارش سڌي آهي: ڪاپي رائيٽ جي مدت کي مختصر ڪريو. آمريڪا ۾، ڪاپي رائيٽ ليکڪ جي موت کان 70 سال تائين ڏني ويندي آهي. اهو بيوقوف آهي. اسان ان کي پيٽنٽس سان هم آهنگ ڪري سگهون ٿا، جيڪي فائلنگ کان 20 سال بعد ڏنا ويندا آهن. هي ڪتابن، مقالن، موسيقي، آرٽ، ۽ ٻين تخليقي ڪمن جي ليکڪن لاءِ مڪمل معاوضي لاءِ ڪافي وقت هجڻ گهرجي (جن ۾ ڊگهي مدت وارا منصوبا جهڙوڪ فلمي موافقت شامل آهن). پوءِ، گهٽ ۾ گهٽ، پاليسي سازن کي متنن جي وڏي پيماني تي تحفظ ۽ ورڇ لاءِ استثنا شامل ڪرڻ گهرجي. جيڪڏهن انفرادي گراهڪن کان وڃايل آمدني مکيه پريشاني آهي، ته ذاتي سطح تي ورڇ کي منع ٿيل رهڻ گهرجي. ان جي بدلي ۾، اهي جيڪي وسيع ذخيرا منظم ڪرڻ جي قابل آهن - LLMs کي تربيت ڏيندڙ ڪمپنيون، گڏوگڏ لائبريريون ۽ ٻيا آرڪائيوز - انهن استثنائن سان ڍڪيل هوندا. ڪاپي رائيٽ سڌارن قومي سلامتي لاءِ ضروري آهي مختصر: چيني LLMs (جن ۾ DeepSeek شامل آهي) منهنجي غير قانوني ڪتابن ۽ مقالن جي آرڪائيو تي تربيت حاصل ڪئي آهي - جيڪا دنيا ۾ سڀ کان وڏي آهي. مغرب کي قومي سلامتي جي معاملي طور ڪاپي رائيٽ قانون کي تبديل ڪرڻ جي ضرورت آهي. <a %(all_isbns)s>اصلي بلاگ پوسٽ</a> لاءِ وڌيڪ معلومات ڏسو. اسان هڪ چئلينج جاري ڪيو ته ان کي بهتر بڻايو وڃي. اسان پهرين جڳهه لاءِ $6,000، ٻي جڳهه لاءِ $3,000، ۽ ٽئين جڳهه لاءِ $1,000 انعام ڏيڻ جو اعلان ڪيو. زبردست جواب ۽ شاندار پيشڪشون ڏسڻ کان پوءِ، اسان انعامي پول کي ٿورو وڌائڻ جو فيصلو ڪيو، ۽ ٽئين جڳهه لاءِ چار ماڻهن کي $500 هر هڪ انعام ڏيڻ جو فيصلو ڪيو. فاتح هيٺ ڏنل آهن، پر سڀني پيشڪشن کي ضرور ڏسو <a %(annas_archive)s>هتي</a>، يا اسان جو <a %(a_2025_01_isbn_visualization_files)s> گڏيل ٽورينٽ</a> ڊائونلوڊ ڪريو. پهريون انعام $6,000: phiresky هي <a %(phiresky_github)s>پيشڪش</a> (<a %(annas_archive_note_2951)s>Gitlab تبصرو</a>) بلڪل اهو آهي جيڪو اسان چاهيو، ۽ وڌيڪ! اسان خاص طور تي ان جي انتهائي لچڪدار ويژولائيزيشن آپشنز کي پسند ڪيو (جيڪو ڪسٽم شيڊرز کي به سپورٽ ڪري ٿو)، پر هڪ جامع فهرست سان گڏ. اسان کي اهو به پسند آيو ته هر شيءِ ڪيئن تيز ۽ هموار آهي، سادي عملدرآمد (جنهن ۾ بيڪ اينڊ به ناهي)، هوشيار منيمپ، ۽ انهن جي <a %(phiresky_github)s>بلاگ پوسٽ</a> ۾ وسيع وضاحت. شاندار ڪم، ۽ چڱيءَ طرح مستحق فاتح! - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) اسان جا دل شڪرگذاري سان ڀرجي ويا آهن. قابل ذڪر خيال نایابیت لاءِ آسمان ڇهندڙ عمارتون ڊيٽا سيٽس جي مقابلي لاءِ گهڻا سلائيڊر، ڄڻ ته توهان هڪ ڊي جي آهيو. ڪتابن جي تعداد سان اسڪيل بار. خوبصورت ليبل. ٿڌي ڊفالٽ رنگ اسڪيم ۽ هيٽ ميپ. منفرد نقشي جو ڏيک ۽ فلٽر وضاحتون، ۽ پڻ لائيو انگ اکر لائيو انگ اکر ڪجهه وڌيڪ خيال ۽ عملدرآمد جيڪي اسان خاص طور تي پسند ڪيا: اسان ڪجهه وقت لاءِ جاري رکي سگهون ٿا، پر اچو ته هتي روڪيون. سڀني جمع ڪرايل مواد کي ڏسو <a %(annas_archive)s>هتي</a>، يا اسان جو <a %(a_2025_01_isbn_visualization_files)s> گڏيل ٽورينٽ</a> ڊائونلوڊ ڪريو. ايتريون ساريون جمع ڪرايون ويون آهن، ۽ هر هڪ منفرد نقطه نظر آڻي ٿي، ڇا يو آءِ ۾ هجي يا عملدرآمد ۾. اسان گهٽ ۾ گهٽ پهرين نمبر تي آيل جمع ڪرايل مواد کي پنهنجي مکيه ويب سائيٽ ۾ شامل ڪنداسين، ۽ شايد ڪجهه ٻين کي به. اسان ناياب ترين ڪتابن جي سڃاڻپ، تصديق، ۽ پوءِ آرڪائيو ڪرڻ جي عمل کي منظم ڪرڻ بابت سوچڻ شروع ڪيو آهي. هن محاذ تي وڌيڪ اچڻ وارو آهي. شامل ٿيڻ وارن سڀني جو شڪريو. اهو حيرت انگيز آهي ته ايترا ماڻهو خيال ڪن ٿا. جلدي مقابلي لاءِ ڊيٽا سيٽس کي آساني سان ٽوگل ڪرڻ. سڀ ISBNs CADAL SSNOs CERLALC ڊيٽا ليڪ DuXiu SSIDs EBSCOhost جو eBook انڊيڪس گوگل بڪس گڊ ريڊز انٽرنيٽ آرڪائيو ISBNdb ISBN گلوبل رجسٽر آف پبلشرز لبي انا جي آرڪائيو ۾ فائلون Nexus/STC OCLC/Worldcat اوپن لائبريري روسي رياستي لائبريري ٽرينٽر جي شاهي لائبريري ٻيو انعام $3,000: hypha “جڏهن ته مڪمل چورس ۽ مستطيل رياضيءَ جي لحاظ کان خوشگوار آهن، اهي نقشي جي حوالي سان بهتر مقامييت مهيا نٿا ڪن. مون کي يقين آهي ته انهن هلبرٽ يا ڪلاسڪ مورٽن ۾ موجود عدم توازن هڪ خامي نه پر هڪ خاصيت آهي. بلڪل ائين جيئن اٽلي جي مشهور بوٽ جي شڪل واري خاڪي کي نقشي تي فوري طور تي سڃاڻپ لائق بڻائي ٿو، انهن وکرن جي منفرد "خاصيتون" ذهني نشان طور ڪم ڪري سگهن ٿيون. هي خاصيت جڳهه جي يادگيري کي وڌائي سگهي ٿي ۽ صارفين کي پاڻ کي واقف ڪرڻ ۾ مدد ڏئي سگهي ٿي، ممڪن آهي ته مخصوص علائقن کي ڳولڻ يا نمونن کي نوٽ ڪرڻ آسان بڻائي.” هڪ ٻي شاندار <a %(annas_archive_note_2913)s>پيشڪش</a>. پهرين جڳهه جيتري لچڪدار نه آهي، پر اسان حقيقت ۾ ان جي ميڪرو-ليول ويژولائيزيشن کي پهرين جڳهه کان وڌيڪ پسند ڪيو (جڳهه ڀرڻ وارو وکر، سرحدون، ليبلنگ، نمايان ڪرڻ، پيننگ، ۽ زومنگ). جو ڊيوس جو هڪ <a %(annas_archive_note_2971)s>تبصرو</a> اسان سان هم آهنگ ٿيو: ۽ اڃا به ويژولائيزنگ ۽ رينڊرنگ لاءِ ڪيترائي آپشن، ۽ انتهائي هموار ۽ وجداني UI. هڪ مضبوط ٻيو نمبر! - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>) ڪجهه مهينا اڳ اسان هڪ <a %(all_isbns)s>$10,000 انعام</a> جو اعلان ڪيو ته اسان جي ڊيٽا جي بهترين ممڪن ويزولائيزيشن ٺاهڻ لاءِ جيڪو ISBN جڳهه ڏيکاري ٿو. اسان زور ڏنو ته ڏيکاريو ته ڪهڙيون فائلون اسان اڳ ۾ ئي آرڪائيو نه ڪيون آهن، ۽ اسان بعد ۾ هڪ ڊيٽا سيٽ شامل ڪيو جيڪو ڏيکاري ٿو ته ڪيتريون لائبريريون ISBNs رکن ٿيون (نادر هجڻ جو هڪ ماپ). اسان جواب کان حيران ٿي ويا آهيون. اتي تمام گهڻي تخليقيت رهي آهي. هر ڪنهن جو وڏو شڪريو جنهن حصو ورتو: توهان جي توانائي ۽ جوش متاثر ڪندڙ آهي! آخرڪار اسان هيٺين سوالن جا جواب ڏيڻ چاهيا: <strong>ڪهڙيون ڪتابون دنيا ۾ موجود آهن، اسان ڪيتريون ئي اڳ ۾ ئي محفوظ ڪري چڪا آهيون، ۽ اسان کي ايندڙ ڪهڙين ڪتابن تي ڌيان ڏيڻ گهرجي؟</strong> اهو ڏسڻ ۾ سٺو لڳي ٿو ته ڪيترائي ماڻهو انهن سوالن جي پرواهه ڪن ٿا. اسان پاڻ هڪ بنيادي تصوير سان شروع ڪيو. 300kb کان گهٽ ۾، هي تصوير انسانيت جي تاريخ ۾ ڪڏهن به گڏ ڪيل سڀ کان وڏي مڪمل طور تي کليل "ڪتابن جي فهرست" کي مختصر طور تي ظاهر ڪري ٿي: ٽيون انعام $500 #1: maxlion هن <a %(annas_archive_note_2940)s>پيشڪش</a> ۾ اسان کي مختلف قسم جا نظارا واقعي پسند آيا، خاص طور تي مقابلي ۽ پبلشر نظارا. ٽيون انعام $500 #2: abetusk جڏهن ته سڀ کان وڌيڪ پالش ٿيل UI نه آهي، هي <a %(annas_archive_note_2917)s>پيشڪش</a> ڪيترن ئي خانن کي چيڪ ڪري ٿي. اسان خاص طور تي ان جي مقابلي واري خاصيت کي پسند ڪيو. ٽيون انعام $500 #3: conundrumer0 پهريون نمبر وانگر، هي <a %(annas_archive_note_2975)s>پيشڪش</a> اسان کي ان جي لچڪ سان متاثر ڪيو. آخرڪار اهو ئي آهي جيڪو هڪ بهترين ويژولائيزيشن اوزار ٺاهيندو آهي: پاور يوزرز لاءِ وڌ کان وڌ لچڪ، جڏهن ته اوسط صارفين لاءِ شيون سادي رکڻ. ٽيون انعام $500 #4: charelf آخري <a %(annas_archive_note_2947)s>پيشڪش</a> جيڪا انعام حاصل ڪري ٿي بلڪل بنيادي آهي، پر ڪجهه منفرد خاصيتون آهن جيڪي اسان کي واقعي پسند آيون. اسان کي پسند آيو ته اهي ڪيئن ڏيکارين ٿا ته ڪيترا ڊيٽا سيٽس هڪ خاص ISBN کي مقبوليت/اعتماد جي ماپ طور ڍڪين ٿا. اسان کي پڻ واقعي پسند آيو سادگي پر اثرائتي طور تي مقابلي لاءِ اوپيسيٽي سلائيڊر استعمال ڪرڻ. $10,000 ISBN ويزولائيزيشن انعام جا فاتح مختصر: اسان کي $10,000 ISBN ويزولائيزيشن انعام لاءِ ڪجهه حيرت انگيز پيشڪشون مليون. پس منظر آنا جي آرڪائيو ڪيئن انسانيت جي سموري ڄاڻ کي بيڪ اپ ڪرڻ جو مشن حاصل ڪري سگهي ٿو، بغير ان جي ڄاڻڻ جي ته ڪهڙيون ڪتابون اڃا به موجود آهن؟ اسان کي هڪ TODO لسٽ جي ضرورت آهي. ان کي نقشو ٺاهڻ جو هڪ طريقو ISBN نمبرن ذريعي آهي، جيڪي 1970ع کان وٺي هر شايع ٿيل ڪتاب کي (گهڻن ملڪن ۾) ڏنا ويا آهن. ڪو به مرڪزي اختيار ناهي جيڪو سڀني ISBN اسائنمينٽس کي ڄاڻي ٿو. ان جي بدران، اهو هڪ ورهايل نظام آهي، جتي ملڪن کي نمبرن جا رينجز ملن ٿا، جيڪي پوءِ وڏن پبلشرز کي ننڍا رينجز ڏين ٿا، جيڪي شايد ننڍن پبلشرز کي رينجز کي وڌيڪ ورهائين. آخرڪار انفرادي نمبر ڪتابن کي ڏنا ويندا آهن. اسان ISBNs کي <a %(blog)s>ٻن سالن اڳ</a> ISBNdb جي اسڪراپ سان نقشو ٺاهڻ شروع ڪيو. ان کان پوءِ، اسان ڪيترن ئي وڌيڪ metadata ذريعن کي اسڪراپ ڪيو آهي، جهڙوڪ <a %(blog_2)s>Worldcat</a>, گوگل بڪس، Goodreads، Libby، ۽ وڌيڪ. مڪمل لسٽ "Datasets" ۽ "Torrents" صفحن تي آنا جي آرڪائيو تي ملي سگهي ٿي. اسان وٽ هاڻي دنيا ۾ سڀ کان وڏي مڪمل طور تي کليل، آساني سان ڊائون لوڊ ڪرڻ جي قابل ڪتاب metadata (۽ ان ڪري ISBNs) جو مجموعو آهي. اسان <a %(blog)s>وڏي پيماني تي لکيو آهي</a> ته اسان کي تحفظ جي پرواهه ڇو آهي، ۽ ڇو اسان في الحال هڪ نازڪ ونڊو ۾ آهيون. اسان کي هاڻي ناياب، گهٽ ڌيان ڏنل، ۽ منفرد طور تي خطري ۾ موجود ڪتابن کي سڃاڻڻ ۽ محفوظ ڪرڻ گهرجي. دنيا جي سڀني ڪتابن تي سٺي metadata هجڻ سان ان ۾ مدد ملي ٿي. $10,000 انعام استعمال جي سهولت ۽ ان جي سٺي نظر کي مضبوط غور ڏنو ويندو. زوم ڪرڻ تي انفرادي ISBNs لاءِ حقيقي metadata ڏيکاريو، جهڙوڪ عنوان ۽ ليکڪ. بهتر جڳهه ڀرڻ وارو وکر. مثال طور هڪ زگ-زيگ، پهرين قطار ۾ 0 کان 4 تائين وڃڻ ۽ پوءِ ٻي قطار ۾ (الٽي ۾) 5 کان 9 تائين واپس وڃڻ — ورجائي لاڳو ٿيل. مختلف يا حسب ضرورت رنگن جا اسڪيم. ڊيٽا سيٽس جي مقابلي لاءِ خاص نظارا. مسئلن کي ڊيبگ ڪرڻ جا طريقا، جهڙوڪ ٻيو metadata جيڪو چڱيءَ طرح سان متفق نه هجي (مثال طور، تمام مختلف عنوان). ISBNs يا رينجز تي تبصرن سان تصويرن کي تشريح ڪرڻ. نادر يا خطري ۾ پيل ڪتابن جي سڃاڻپ لاءِ ڪا به حڪمت عملي. جيڪي به تخليقي خيال توهان سوچيو ٿا! ڪوڊ انهن تصويرن کي پيدا ڪرڻ لاءِ ڪوڊ، گڏوگڏ ٻيا مثال، <a %(annas_archive)s>هن ڊائريڪٽري</a> ۾ ملي سگهن ٿا. اسان هڪ جامع ڊيٽا فارميٽ سان آيا آهيون، جنهن سان سڀ گهربل ISBN معلومات تقريباً 75MB (ڪمپريس ٿيل) آهي. ڊيٽا فارميٽ جو وضاحت ۽ ان کي پيدا ڪرڻ لاءِ ڪوڊ <a %(annas_archive_l1244_1319)s>هتي</a> ملي سگھي ٿو. انعام لاءِ توهان کي اهو استعمال ڪرڻ جي ضرورت ناهي، پر اهو شايد شروع ڪرڻ لاءِ سڀ کان وڌيڪ آسان فارميٽ آهي. توهان اسان جي metadata کي جيئن چاهيو تبديل ڪري سگهو ٿا (جيتوڻيڪ توهان جو سڄو ڪوڊ اوپن سورس هجڻ گهرجي). اسان کي انتظار نٿو ڪري سگھي ته توهان ڇا سوچيو ٿا. نيڪ تمنائون! هن ريپو کي فورڪ ڪريو، ۽ هن بلاگ پوسٽ HTML کي ايڊٽ ڪريو (اسان جي Flask بيڪ اينڊ کان سواءِ ٻيا بيڪ اينڊز اجازت نه آهن). مٿي ڏنل تصوير کي هموار زوم ڪرڻ جي قابل بڻايو، ته جيئن توهان انفرادي ISBNs تائين زوم ڪري سگهو. ISBNs تي ڪلڪ ڪرڻ سان توهان کي آنا جي آرڪائيو تي هڪ metadata صفحي يا ڳولا تي وٺي وڃڻ گهرجي. توهان کي اڃا تائين سڀني مختلف Datasets جي وچ ۾ سوئچ ڪرڻ جي قابل هجڻ گهرجي. ملڪ جي رينجز ۽ پبلشر رينجز کي هور تي نمايان ڪيو وڃي. توهان استعمال ڪري سگهو ٿا مثال طور <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> ملڪ جي معلومات لاءِ، ۽ اسان جي "isbngrp" اسڪراپ پبلشرز لاءِ (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). اهو ڊيسڪ ٽاپ ۽ موبائل تي چڱي طرح ڪم ڪرڻ گهرجي. هتي ڳولڻ لاءِ گهڻو ڪجهه آهي، تنهنڪري اسان مٿي ڏنل تصويري ڏيک کي بهتر ڪرڻ لاءِ هڪ انعام جو اعلان ڪري رهيا آهيون. اسان جي گهڻن انعامن جي برعڪس، هي هڪ وقت سان محدود آهي. توهان کي پنهنجو اوپن سورس ڪوڊ 2025-01-31 (23:59 UTC) تائين <a %(annas_archive)s>جمع ڪرائڻ</a>و آهي. بهترين جمع ڪرائڻ کي $6,000 ملندا، ٻئي نمبر تي $3,000، ۽ ٽئين نمبر تي $1,000. سڀ انعام Monero (XMR) استعمال ڪندي ڏنا ويندا. هيٺيان گهٽ ۾ گهٽ معيار آهن. جيڪڏهن ڪا به جمع ڪرائڻ معيار تي پوري نه لهي، اسان شايد اڃا به ڪجهه انعام ڏيون، پر اهو اسان جي صوابديد تي هوندو. اضافي پوائنٽس لاءِ (اهي صرف خيال آهن — پنهنجي تخليقيت کي آزاد ڪريو): توهان مڪمل طور تي گهٽ ۾ گهٽ معيار کان هٽي سگهو ٿا، ۽ مڪمل طور تي مختلف ويژوئلائيزيشن ڪري سگهو ٿا. جيڪڏهن اهو واقعي شاندار آهي، ته پوءِ اهو انعام لاءِ اهل آهي، پر اسان جي صوابديد تي. پنهنجي فورڪ ٿيل ريپو، مرج درخواست، يا فرق سان گڏ <a %(annas_archive)s>هن مسئلي</a> تي تبصرو پوسٽ ڪندي جمع ڪرائڻ ڪريو. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) هي تصوير 1000×800 پکسلز آهي. هر پکسل 2,500 ISBNs جي نمائندگي ڪري ٿو. جيڪڏهن اسان وٽ ڪنهن ISBN لاءِ فائل آهي، اسان ان پکسل کي وڌيڪ سائي بڻايون ٿا. جيڪڏهن اسان ڄاڻون ٿا ته هڪ ISBN جاري ڪيو ويو آهي، پر اسان وٽ هڪ ميچنگ فائل ناهي، اسان ان کي وڌيڪ ڳاڙهو بڻايون ٿا. 300kb کان گهٽ ۾، هي تصوير مختصر طور تي انسانيت جي تاريخ ۾ ڪڏهن به گڏ ڪيل سڀ کان وڏي مڪمل طور تي کليل "ڪتابن جي فهرست" جي نمائندگي ڪري ٿي (مڪمل طور تي ڪمپريس ٿيل ڪجهه سو GB). اهو پڻ ڏيکاري ٿو: ڪتابن کي بيڪ اپ ڪرڻ ۾ اڃا گهڻو ڪم باقي آهي (اسان وٽ صرف 16% آهن). سڀني ISBNs کي ڏسڻ — $10,000 انعام 2025-01-31 تائين هي تصوير انسانيت جي تاريخ ۾ ڪڏهن به گڏ ڪيل سڀ کان وڏي مڪمل طور تي کليل "ڪتابن جي فهرست" جي نمائندگي ڪري ٿي. تصويري ڏيک جائزي واري تصوير کان علاوه، اسان انهن انفرادي Datasets کي به ڏسي سگهون ٿا جيڪي اسان حاصل ڪيا آهن. انهن جي وچ ۾ سوئچ ڪرڻ لاءِ ڊراپ ڊائون ۽ بٽڻ استعمال ڪريو. انهن تصويرن ۾ ڏسڻ لاءِ ڪيترائي دلچسپ نمونا آهن. ڇو ڪجهه لائينن ۽ بلاڪن جي باقاعدگي آهي، جيڪا مختلف پيمانن تي ٿيندي نظر اچي ٿي؟ خالي علائقا ڇا آهن؟ ڇو ڪجهه Datasets ايترا گڏ ٿيل آهن؟ اسان انهن سوالن کي پڙهندڙ لاءِ هڪ مشق طور ڇڏي ڏينداسين. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) نتيجو هن معيار سان، اسان رليز کي وڌيڪ تدريجي طور تي ٺاهي سگهون ٿا، ۽ نوان ڊيٽا ذريعا وڌيڪ آساني سان شامل ڪري سگهون ٿا. اسان وٽ اڳ ۾ ئي ڪجهه دلچسپ رليز پائپ لائن ۾ آهن! اسان کي پڻ اميد آهي ته ٻين شيڊو لائبريرين لاءِ اسان جي مجموعن کي آئيني ڪرڻ آسان ٿي ويندو. آخرڪار، اسان جو مقصد انساني علم ۽ ثقافت کي هميشه لاءِ محفوظ ڪرڻ آهي، تنهنڪري جيتري وڌيڪ اضافي، اوترو بهتر. مثال اچو ته اسان جي تازو Z-Library رليز کي هڪ مثال طور ڏسون. اهو ٻن مجموعن تي مشتمل آهي: “<span style="background: #fffaa3">zlib3_records</span>” ۽ “<span style="background: #ffd6fe">zlib3_files</span>”. هي اسان کي اصل ڪتاب فائلن کان الڳ ميٽا ڊيٽا ريڪارڊ اسڪراپ ۽ رليز ڪرڻ جي اجازت ڏئي ٿو. اهڙي طرح، اسان ميٽا ڊيٽا فائلن سان ٻه ٽورينٽس جاري ڪيا: اسان باينري ڊيٽا فولڊرن سان گڏ ٽورينٽس جو هڪ جٿو پڻ جاري ڪيو، پر صرف “<span style="background: #ffd6fe">zlib3_files</span>” مجموعي لاءِ، مجموعي طور تي 62: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> هلائڻ سان اسان ڏسي سگهون ٿا ته اندر ڇا آهي: هن صورت ۾، اهو Z-Library پاران رپورٽ ڪيل هڪ ڪتاب جو ميٽا ڊيٽا آهي. مٿين سطح تي اسان وٽ صرف “aacid” ۽ “ميٽا ڊيٽا” آهي، پر “data_folder” ناهي، ڇاڪاڻ ته ڪو به لاڳاپيل باينري ڊيٽا ناهي. AACID ۾ “22430000” بنيادي ID طور شامل آهي، جيڪو اسان ڏسي سگهون ٿا ته “zlibrary_id” مان ورتو ويو آهي. اسان توقع ڪري سگهون ٿا ته هن مجموعي ۾ ٻيا AACs ساڳي جوڙجڪ رکندڙ هوندا. هاڻي اچو ته <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> هلائي ڏسون: هي هڪ تمام ننڍڙو AAC ميٽا ڊيٽا آهي، جيتوڻيڪ هن AAC جو وڏو حصو ٻئي هنڌ هڪ باينري فائل ۾ واقع آهي! آخرڪار، اسان وٽ هن وقت “data_folder” آهي، تنهنڪري اسان توقع ڪري سگهون ٿا ته لاڳاپيل باينري ڊيٽا <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> تي واقع آهي. “ميٽا ڊيٽا” ۾ “zlibrary_id” شامل آهي، تنهنڪري اسان آساني سان ان کي “zlib_records” مجموعي ۾ لاڳاپيل AAC سان ڳنڍي سگهون ٿا. اسان مختلف طريقن سان ڳنڍي سگهون ٿا، مثلاً AACID ذريعي — معيار ان کي مقرر نٿو ڪري. نوٽ ڪريو ته “ميٽا ڊيٽا” فيلڊ جو پاڻ JSON هجڻ ضروري ناهي. اهو هڪ اسٽنگ ٿي سگهي ٿو جنهن ۾ XML يا ڪو ٻيو ڊيٽا فارميٽ شامل هجي. توهان ميٽا ڊيٽا معلومات کي لاڳاپيل باينري بلاڪ ۾ به محفوظ ڪري سگهو ٿا، مثلاً جيڪڏهن اهو گهڻو ڊيٽا آهي. مختلف فائلون ۽ metadata، جيترو ممڪن هجي اصل فارميٽ جي ويجهو. ٻاينري ڊيٽا کي سڌو سنئون ويب سرورز جهڙوڪ Nginx ذريعي پيش ڪري سگهجي ٿو. ذريعن جي لائبريرين ۾ مختلف سڃاڻپ ڪندڙ، يا سڃاڻپ ڪندڙن جي غير موجودگي. metadata جي الڳ رليز بمقابلہ فائل ڊيٽا، يا صرف metadata رليز (مثال طور اسان جي ISBNdb رليز). ٽورنٽس ذريعي تقسيم، جيتوڻيڪ ٻين تقسيم طريقن جي امڪان سان (مثال طور IPFS). ناقابل تبديلي ريڪارڊ، ڇو ته اسان کي فرض ڪرڻ گهرجي ته اسان جا ٽورنٽس هميشه لاءِ رهندا. اضافو رليز / شامل ڪرڻ وارا رليز. مشين پڙهڻ ۽ لکڻ جي قابل، آساني سان ۽ جلدي، خاص طور تي اسان جي اسٽيڪ لاءِ (Python، MySQL، ElasticSearch، Transmission، Debian، ext4). ڪجهه حد تائين انساني معائنو آسان، جيتوڻيڪ هي مشين پڙهڻ جي صلاحيت کان ثانوي آهي. اسان جي مجموعن کي معياري ڪرائي تي ورتل seedbox سان آساني سان شروع ڪرڻ. ڊيزائن جا مقصد اسان کي پرواهه ناهي ته فائلون ڊسڪ تي دستي طور تي نيويگيٽ ڪرڻ ۾ آسان آهن، يا بغير preprocessing جي ڳولا لائق آهن. اسان کي موجوده لائبريري سافٽ ويئر سان سڌو مطابقت جي پرواهه ناهي. جڏهن ته ڪنهن لاءِ اسان جي مجموعي کي ٽورنٽس استعمال ڪندي شروع ڪرڻ آسان هجڻ گهرجي، اسان کي توقع ناهي ته فائلون بغير اهم ٽيڪنيڪل ڄاڻ ۽ وابستگي جي استعمال لائق هونديون. اسان جو بنيادي استعمال ڪيس مختلف موجوده مجموعن مان فائلن ۽ لاڳاپيل metadata جي تقسيم آهي. اسان جا سڀ کان اهم غور و فڪر آهن: ڪجهه غير مقصد: ڇو ته انا جو آرڪائيو اوپن سورس آهي، اسان چاهيون ٿا ته اسان جي فارميٽ کي سڌو استعمال ڪريون. جڏهن اسان پنهنجي سرچ انڊيڪس کي تازه ڪاري ڪريون ٿا، اسان صرف عوامي طور تي دستياب رستن تائين رسائي حاصل ڪريون ٿا، ته جيئن جيڪو به اسان جي لائبريري کي فورڪ ڪري سگهي، جلدي سان شروع ٿي سگهي. <strong>AAC.</strong> AAC (آنا جو آرڪائيو ڪنٽينر) هڪ واحد شيءِ آهي جنهن ۾ <strong>ميٽا ڊيٽا</strong> شامل آهي، ۽ اختياري طور تي <strong>بائنري ڊيٽا</strong>، جيڪي ٻئي تبديل نه ٿيندڙ آهن. ان ۾ هڪ عالمي منفرد سڃاڻپ ڪندڙ آهي، جنهن کي <strong>AACID</strong> سڏيو ويندو آهي. <strong>AACID.</strong> AACID جو فارميٽ هي آهي: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. مثال طور، هڪ حقيقي AACID جيڪو اسان جاري ڪيو آهي <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID حد.</strong> جتان AACIDs ۾ مسلسل وڌندڙ ٽائم اسٽيمپس شامل آهن، اسان ان کي هڪ خاص مجموعي اندر حدون ظاهر ڪرڻ لاءِ استعمال ڪري سگهون ٿا. اسان هي فارميٽ استعمال ڪريون ٿا: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, جتي ٽائم اسٽيمپس شامل آهن. هي ISO 8601 نوٽيش سان مطابقت رکي ٿو. حدون مسلسل آهن، ۽ اوورليپ ٿي سگهن ٿيون، پر اوورليپ جي صورت ۾ ان مجموعي ۾ اڳ ۾ جاري ڪيل هڪ جهڙا رڪارڊ شامل ڪرڻ لازمي آهن (ڇو ته AACs تبديل نه ٿيندڙ آهن). غائب رڪارڊ جي اجازت ناهي. <code>{collection}</code>: مجموعي جو نالو، جيڪو ASCII اکرن، انگن، ۽ انڊر اسڪورز تي مشتمل ٿي سگهي ٿو (پر ٻه انڊر اسڪورز نه). <code>{collection-specific ID}</code>: مجموعي-مخصوص سڃاڻپ ڪندڙ، جيڪڏهن لاڳو ٿئي، مثال طور Z-Library ID. ان کي ڇڏي يا مختصر ڪري سگهجي ٿو. ان کي ڇڏي يا مختصر ڪرڻ لازمي آهي جيڪڏهن AACID ٻي صورت ۾ 150 اکرن کان وڌي وڃي. <code>{ISO 8601 timestamp}</code>: ISO 8601 جو هڪ مختصر ورزن، هميشه UTC ۾، مثال طور <code>20220723T194746Z</code>. هي نمبر هر جاري ٿيڻ لاءِ مسلسل وڌڻ گهرجي، جيتوڻيڪ ان جي صحيح معنوي هر مجموعي لاءِ مختلف ٿي سگهي ٿي. اسان اسڪراپنگ يا ID ٺاهڻ جي وقت کي استعمال ڪرڻ جي صلاح ڏيون ٿا. <code>{shortuuid}</code>: هڪ UUID پر ASCII ۾ دٻايل، مثال طور base57 استعمال ڪندي. اسان في الحال <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python لائبريري استعمال ڪريون ٿا. <strong>بائنري ڊيٽا فولڊر.</strong> هڪ فولڊر جنهن ۾ هڪ خاص مجموعي لاءِ AACs جي حد جي بائنري ڊيٽا شامل آهي. انهن ۾ هيٺيان خاصيتون آهن: ڊائريڪٽري ۾ مخصوص حد اندر سڀني AACs لاءِ ڊيٽا فائلون شامل هجڻ گهرجن. هر ڊيٽا فائل جو فائل نالو ان جو AACID هجڻ گهرجي (ڪو به توسيع نه). ڊائريڪٽري جو نالو هڪ AACID حد هجڻ گهرجي، جنهن جي اڳيان <code style="color: green">annas_archive_data__</code> ۽ ڪو به لاحقو نه هجي. مثال طور، اسان جي هڪ حقيقي جاري ڪيل ڊائريڪٽري جو نالو آهي<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. اهي فولڊر ڪجهه حد تائين منظم رکڻ جي سفارش ڪئي وئي آهي، مثلاً هر هڪ 100GB-1TB کان وڏو نه هجي، جيتوڻيڪ وقت سان گڏ هي سفارش تبديل ٿي سگهي ٿي. <strong>Collection.</strong> هر AAC هڪ مجموعي سان تعلق رکي ٿو، جيڪو تعريف مطابق AACs جي هڪ فهرست آهي جيڪا معنوي طور تي مستقل آهي. ان جو مطلب آهي ته جيڪڏهن توهان ميٽا ڊيٽا جي فارميٽ ۾ اهم تبديلي ڪندا آهيو، ته پوءِ توهان کي هڪ نئون مجموعو ٺاهڻو پوندو. معياري <strong>ميٽا ڊيٽا فائل.</strong> هڪ ميٽا ڊيٽا فائل هڪ خاص مجموعي لاءِ AACs جي حد جي ميٽا ڊيٽا تي مشتمل آهي. انهن ۾ هيٺيان خاصيتون آهن: <code>data_folder</code> اختياري آهي، ۽ اهو بائنري ڊيٽا فولڊر جو نالو آهي جيڪو لاڳاپيل بائنري ڊيٽا تي مشتمل آهي. ان فولڊر اندر لاڳاپيل بائنري ڊيٽا جو فائل نالو رڪارڊ جو AACID آهي. هر JSON اعتراض ۾ هيٺين شعبن کي مٿين سطح تي شامل ڪرڻ لازمي آهي: <strong>aacid</strong>, <strong>ميٽا ڊيٽا</strong>, <strong>data_folder</strong> (اختياري). ٻيا شعبا اجازت نه آهن. فائل جو نالو هڪ AACID حد هجڻ گهرجي، جنهن جي اڳيان <code style="color: red">annas_archive_meta__</code> ۽ <code>.jsonl.zstd</code> سان ختم ٿئي. مثال طور، اسان جي هڪ جاري ڪيل فائل جو نالو آهي<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. جيئن فائل جي توسيع ظاهر ڪري ٿي، فائل جو قسم <a %(jsonlines)s>JSON Lines</a> آهي جيڪو <a %(zstd)s>Zstandard</a> سان دٻايل آهي. <code>ميٽا ڊيٽا</code> مجموعي جي معنوي مطابق، من ماني ميٽا ڊيٽا آهي. ان کي مجموعي اندر معنوي طور تي مستقل هجڻ گهرجي. <code style="color: red">annas_archive_meta__</code> اڳوڻو توهان جي اداري جي نالي سان ترتيب ڏئي سگهجي ٿو، مثال طور <code style="color: red">my_institute_meta__</code>. <strong>“records” ۽ “files” مجموعا.</strong> رواجي طور تي، "records" ۽ "files" کي مختلف مجموعن جي طور تي جاري ڪرڻ آسان آهي، ته جيئن اهي مختلف شيڊول تي جاري ڪري سگهجن، مثال طور، اسڪراپنگ جي شرح تي ٻڌل. هڪ "record" هڪ ميٽا ڊيٽا-صرف مجموعو آهي، جنهن ۾ ڪتابن جا عنوان، ليکڪ، ISBNs وغيره شامل آهن، جڏهن ته "files" اهي مجموعا آهن جيڪي اصل فائلون پاڻ (pdf، epub) شامل ڪن ٿا. آخرڪار، اسان هڪ نسبتا سادي معيار تي فيصلو ڪيو. اهو ڪافي لچڪدار، غير معياري، ۽ جاري ڪم آهي. <strong>ٽورينٽس.</strong> ميٽا ڊيٽا فائلون ۽ باينري ڊيٽا فولڊر ٽورينٽس ۾ گڏ ڪري سگهجن ٿيون، هڪ ٽورينٽ في ميٽا ڊيٽا فائل، يا هڪ ٽورينٽ في باينري ڊيٽا فولڊر سان. ٽورينٽس کي اصل فائل/ڊائريڪٽري نالو ۽ <code>.torrent</code> جو اضافو بطور فائل نالو هجڻ گهرجي. <a %(wikipedia_annas_archive)s>آنا جو آرڪائيو</a> دنيا جي سڀ کان وڏي شيڊو لائبريري بڻجي چڪو آهي، ۽ پنهنجي پيماني تي واحد شيڊو لائبريري آهي جيڪا مڪمل طور تي اوپن سورس ۽ اوپن ڊيٽا آهي. هيٺ اسان جي ڊيٽا سيٽس صفحي مان هڪ جدول آهي (ٿورو تبديل ٿيل): اسان اهو ٽن طريقن سان حاصل ڪيو: موجوده اوپن ڊيٽا شيڊو لائبريرين کي آئيني بڻائڻ (جهڙوڪ Sci-Hub ۽ لائبريري جينيسس). شيڊو لائبريرين جي مدد ڪرڻ جيڪي وڌيڪ اوپن ٿيڻ چاهين ٿا، پر انهن وٽ وقت يا وسيلا نه هئا (جهڙوڪ Libgen ڪامڪس ڪليڪشن). لائبريرين کي اسڪراپ ڪرڻ جيڪي بلڪ ۾ شيئر ڪرڻ نٿيون چاهين (جهڙوڪ Z-Library). (2) ۽ (3) لاءِ اسان هاڻي پاڻ وٽ ٽورنٽس جو هڪ وڏو مجموعو منظم ڪريون ٿا (100s of TBs). اڃا تائين اسان انهن مجموعن کي هڪ دفعو طور تي ڏٺو آهي، جنهن جو مطلب آهي ته هر مجموعي لاءِ خاص infrastructure ۽ ڊيٽا تنظيم. هي هر رليز تي اهم اضافي خرچ وڌائي ٿو، ۽ وڌيڪ اضافي رليز ڪرڻ کي خاص طور تي مشڪل بڻائي ٿو. ان ڪري اسان فيصلو ڪيو آهي ته اسان جي رليز کي معياري بڻايو وڃي. هي هڪ ٽيڪنيڪل بلاگ پوسٽ آهي جنهن ۾ اسان پنهنجو معيار متعارف ڪرائي رهيا آهيون: <strong>انا جو آرڪائيو ڪنٽينرز</strong>. آنا جو آرڪائيو ڪنٽينرز (AAC): دنيا جي سڀ کان وڏي شيڊو لائبريري مان رليز کي معياري بڻائڻ آنا جو آرڪائيو دنيا جي سڀ کان وڏي شيڊو لائبريري بڻجي چڪو آهي، جنهن جي ڪري اسان کي پنهنجي رليز کي معياري بڻائڻ جي ضرورت آهي. 300GB+ ڪتابن جا ڪور جاري ڪيا ويا آخرڪار، اسان کي هڪ ننڍي رليز جو اعلان ڪندي خوشي ٿي رهي آهي. Libgen.rs فورڪ هلائيندڙ ماڻهن سان تعاون ۾، اسان انهن جي سڀني ڪتابن جا ڪور ٽورينٽس ۽ IPFS ذريعي شيئر ڪري رهيا آهيون. هي ڪور ڏسڻ جي لوڊ کي وڌيڪ مشينن ۾ ورهائيندو، ۽ انهن کي بهتر محفوظ ڪندو. ڪيترن ئي (پر سڀني نه) ڪيسن ۾، ڪتاب جا ڪور فائلن ۾ شامل آهن، تنهنڪري هي قسم جو "نڪتل ڊيٽا" آهي. پر ان کي IPFS ۾ هجڻ اڃا تائين روزاني آپريشن لاءِ تمام مفيد آهي ٻنهي Anna جي آرڪائيو ۽ مختلف لائبريري جينيسس فورڪز لاءِ. هميشه وانگر، توهان هن رليز کي Pirate Library Mirror تي ڳولي سگهو ٿا (ايڊيٽ: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>Anna جي آرڪائيو</a>). اسان هتي ان کي لنڪ نه ڪنداسين، پر توهان آساني سان ان کي ڳولي سگهو ٿا. اميد آهي ته اسان پنهنجي رفتار کي ٿورو آرام ڏئي سگهون ٿا، هاڻي جڏهن اسان وٽ Z-Library جو هڪ مناسب متبادل آهي. هي ڪم لوڊ خاص طور تي پائيدار نه آهي. جيڪڏهن توهان پروگرامنگ، سرور آپريشنز، يا تحفظ جي ڪم ۾ مدد ڪرڻ ۾ دلچسپي رکو ٿا، ته ضرور اسان سان رابطو ڪريو. اڃا تائين گهڻو <a %(annas_archive)s>ڪم ڪرڻو آهي</a>. توهان جي دلچسپي ۽ حمايت لاءِ مهرباني. ElasticSearch ڏانهن سوئچ ڪريو ڪجهه سوالن ۾ تمام گهڻو وقت لڳندو هو، جيستائين اهي سڀ کليل ڪنيڪشن کي روڪيندا هئا. MySQL ۾ ڊفالٽ طور تي هڪ گهٽ ۾ گهٽ لفظ جي ڊيگهه آهي، يا توهان جو انڊيڪس واقعي وڏو ٿي سگهي ٿو. ماڻهن رپورٽ ڪيو ته "Ben Hur" لاءِ ڳولا نه ٿي سگهي. ڳولا صرف تڏهن تيز هئي جڏهن مڪمل طور تي ميموري ۾ لوڊ ٿيل هئي، جنهن لاءِ اسان کي هن تي هلائڻ لاءِ وڌيڪ مهانگي مشين حاصل ڪرڻي پئي، ۽ ڪجهه حڪمن کي شروعات ۾ انڊيڪس کي اڳ لوڊ ڪرڻ لاءِ. اسان ان کي آساني سان نئين خاصيتون ٺاهڻ لاءِ وڌائي نه سگهندا هئاسين، جهڙوڪ بهتر <a %(wikipedia_cjk_characters)s>غير اسپيس ٿيل ٻولين لاءِ ٽوڪنائيزيشن</a>, فلٽرنگ/فيسٽنگ، ترتيب ڏيڻ، "ڇا توهان جو مطلب آهي" تجويزون، خودڪار مڪمل ڪرڻ، وغيره. اسان جي هڪ <a %(annas_archive)s>ٽڪيٽس</a> اسان جي ڳولا واري نظام سان مسئلن جو هڪ مجموعو هو. اسان MySQL مڪمل متن ڳولا استعمال ڪئي، ڇاڪاڻ ته اسان وٽ سڀ ڊيٽا MySQL ۾ هئي. پر ان جون حدون هيون: ڪجهه ماهرن سان ڳالهائڻ کان پوءِ، اسان ElasticSearch تي فيصلو ڪيو. اهو مڪمل نه رهيو آهي (انهن جي ڊفالٽ "ڇا توهان جو مطلب آهي" تجويزون ۽ خودڪار مڪمل ڪرڻ جون خاصيتون خراب آهن)، پر مجموعي طور تي اهو MySQL کان ڳولا لاءِ گهڻو بهتر رهيو آهي. اسان اڃا تائين ان کي ڪنهن به مشن-نازڪ ڊيٽا لاءِ استعمال ڪرڻ تي <a %(youtube)s>تمام گهڻو شوقين</a> نه آهيون (جيتوڻيڪ انهن ڪافي <a %(elastic_co)s>ترقي</a> ڪئي آهي)، پر مجموعي طور تي اسان سوئچ سان ڪافي خوش آهيون. هاڻي لاءِ، اسان تيز ترين ڳولا، بهتر ٻولي جي مدد، بهتر مطابقت واري ترتيب، مختلف ترتيب جا اختيار، ۽ ٻولي/ڪتاب جي قسم/فائل جي قسم تي فلٽرنگ لاڳو ڪئي آهي. جيڪڏهن توهان کي ڄاڻڻ ۾ دلچسپي آهي ته هي ڪيئن ڪم ڪري ٿو، <a %(annas_archive_l140)s>هڪ</a> <a %(annas_archive_l1115)s>نظر</a> <a %(annas_archive_l1635)s>وجهيو</a>. اهو ڪافي رسائي وارو آهي، جيتوڻيڪ ان کي ڪجهه وڌيڪ تبصرن جي ضرورت آهي… انا جو آرڪائيو مڪمل طور تي اوپن سورس آهي اسان يقين رکون ٿا ته معلومات آزاد هجڻ گهرجي، ۽ اسان جو پنهنجو ڪوڊ به ان جو استثنا نه آهي. اسان پنهنجو سڄو ڪوڊ پنهنجي ذاتي ميزبان Gitlab انسٽنس تي جاري ڪيو آهي: <a %(annas_archive)s>انا جو سافٽ ويئر</a>. اسان پنهنجي ڪم کي منظم ڪرڻ لاءِ مسئلي ٽريڪر پڻ استعمال ڪندا آهيون. جيڪڏهن توهان اسان جي ترقي سان مشغول ٿيڻ چاهيو ٿا، ته هي هڪ بهترين جڳهه آهي شروع ڪرڻ لاءِ. توهان کي انهن شين جو ذائقو ڏيڻ لاءِ جيڪي اسان ڪم ڪري رهيا آهيون، اسان جي حاليه ڪم تي ڪلائنٽ سائڊ ڪارڪردگي جي بهتري تي غور ڪريو. جئين اسان اڃا تائين صفحا بندي لاڳو نه ڪئي آهي، اسان اڪثر تمام ڊگها ڳولا جا صفحا واپس ڪندا هئاسين، 100-200 نتيجا سان. اسان ڳولا جا نتيجا تمام جلد ختم نه ڪرڻ چاهيندا هئاسين، پر ان جو مطلب اهو هو ته اهو ڪجهه ڊوائيسز کي سست ڪري ڇڏيندو. ان لاءِ، اسان هڪ ننڍي چال لاڳو ڪئي: اسان اڪثر ڳولا جا نتيجا HTML تبصرن ۾ لپيٽيا (<code><!-- --></code>), ۽ پوءِ هڪ ننڍڙو جاوا اسڪرپٽ لکيو جيڪو معلوم ڪندو ته جڏهن هڪ نتيجو نظر اچڻ گهرجي، ان وقت اسان تبصري کي ان لپيٽ مان ڪڍي ڇڏيندا: DOM "ورچوئلائيزيشن" 23 لائينن ۾ لاڳو ڪيو ويو، ڪنهن به فئنسي لائبريرين جي ضرورت ناهي! هي اهو قسم جو تيز عملي ڪوڊ آهي جيڪو توهان وٽ محدود وقت ۽ حقيقي مسئلا حل ڪرڻ لاءِ هوندو آهي. رپورٽ ڪيو ويو آهي ته اسان جي ڳولا هاڻي سست ڊوائيسز تي چڱي طرح ڪم ڪري ٿي! هڪ ٻيو وڏو ڪوشش ڊيٽابيس جي تعمير کي خودڪار ڪرڻ هو. جڏهن اسان لانچ ڪيو، اسان مختلف ذريعن کي گڏ ڪيو. هاڻي اسان انهن کي تازه ترين رکڻ چاهيون ٿا، تنهنڪري اسان ٻن لائبريري جينيسس فورڪز مان نئين metadata ڊائونلوڊ ڪرڻ لاءِ ڪيترائي اسڪرپٽ لکيا، ۽ انهن کي ضم ڪيو. مقصد صرف اسان جي آرڪائيو لاءِ مفيد بڻائڻ نه آهي، پر ڪنهن لاءِ به شيڊو لائبريري metadata سان کيڏڻ لاءِ آسان بڻائڻ آهي. مقصد هڪ Jupyter نوٽ بڪ هوندو جنهن ۾ هر قسم جي دلچسپ metadata موجود هوندي، تنهنڪري اسان وڌيڪ تحقيق ڪري سگهون ٿا جهڙوڪ اهو معلوم ڪرڻ لاءِ ته <a %(blog)s>ISBNs جو ڪهڙو سيڪڙو هميشه لاءِ محفوظ آهي</a>. آخرڪار، اسان پنهنجي عطيا واري نظام کي نئين سر ترتيب ڏني. توهان هاڻي ڪريڊٽ ڪارڊ استعمال ڪري سڌو سنئون اسان جي ڪرپٽو والٽس ۾ پئسا جمع ڪري سگهو ٿا، بغير واقعي ڪرپٽو ڪرنسيز بابت ڪجهه ڄاڻڻ جي ضرورت. اسان ڏسندا رهنداسين ته هي عملي طور تي ڪيترو سٺو ڪم ڪري ٿو، پر هي هڪ وڏو معاملو آهي. Z-Library جي بند ٿيڻ ۽ ان جي (مبينا) باني جي گرفتاري سان، اسان انا جي آرڪائيو سان هڪ سٺو متبادل مهيا ڪرڻ لاءِ رات ڏينهن ڪم ڪري رهيا آهيون (اسان هتي ان جو لنڪ نه ڏينداسين، پر توهان ان کي گوگل ڪري سگهو ٿا). هتي ڪجهه شيون آهن جيڪي اسان تازو حاصل ڪيون. انا جي تازه ڪاري: مڪمل طور تي اوپن سورس آرڪائيو، ElasticSearch، 300GB+ ڪتابن جا ڪور اسان انا جي آرڪائيو سان هڪ سٺو متبادل مهيا ڪرڻ لاءِ رات ڏينهن ڪم ڪري رهيا آهيون. هتي ڪجهه شيون آهن جيڪي اسان تازو حاصل ڪيون. تجزيو سيمينٽڪ نقل (ساڳئي ڪتاب جا مختلف اسڪين) نظرياتي طور تي فلٽر ڪري سگهجن ٿا، پر اهو مشڪل آهي. جڏهن اسان ڪاميڪس کي دستي طور تي ڏٺو ته اسان کي تمام گهڻا غلط مثبت مليا. ڪجهه نقل صرف MD5 جي ذريعي آهن، جيڪو نسبتا فضول آهي، پر انهن کي فلٽر ڪرڻ سان اسان کي صرف 1% in بچت ملندي. هن پيماني تي اهو اڃا به تقريباً 1TB آهي، پر پڻ، هن پيماني تي 1TB واقعي اهم ناهي. اسان ان عمل ۾ ڊيٽا کي حادثاتي طور تي تباهه ڪرڻ جو خطرو نه وٺڻ چاهيون ٿا. اسان کي ڪجهه غير ڪتابي ڊيٽا مليا، جهڙوڪ ڪاميڪ بڪ تي ٻڌل فلمون. اهو پڻ فضول لڳي ٿو، ڇاڪاڻ ته اهي اڳ ۾ ئي ٻين ذريعن ذريعي وڏي پيماني تي دستياب آهن. بهرحال، اسان محسوس ڪيو ته اسان صرف فلمي فائلن کي فلٽر نه ڪري سگهون ٿا، ڇاڪاڻ ته اتي پڻ <em>انٽرايڪٽو ڪاميڪ بڪ</em> آهن جيڪي ڪمپيوٽر تي جاري ڪيا ويا، جن کي ڪنهن رڪارڊ ڪيو ۽ فلمون طور محفوظ ڪيو. آخرڪار، اسان جي مجموعي مان جيڪو ڪجهه به ختم ڪري سگهجي ٿو، اهو صرف ڪجهه سيڪڙو بچائي سگهندو. پوءِ اسان کي ياد آيو ته اسان ڊيٽا گڏ ڪندڙ آهيون، ۽ جيڪي ماڻهو ان کي آئيني بڻائيندا اهي به ڊيٽا گڏ ڪندڙ آهن، ۽ پوءِ، "ڇا مطلب آهي، ختم ڪريو؟!" :) جڏهن توهان جي اسٽوريج ڪلسٽر ۾ 95TB ڊمپ ڪيو وڃي ٿو، توهان ڪوشش ڪريو ٿا ته ان ۾ ڇا آهي... اسان ڪجهه تجزيو ڪيو ته ڏسو ته ڇا اسان سائيز کي ٿورو گهٽائي سگهون ٿا، جهڙوڪ نقل هٽائڻ سان. هتي اسان جي ڪجهه ڳولها آهن: تنهن ڪري اسان توهان کي مڪمل، غير تبديل ٿيل مجموعو پيش ڪري رهيا آهيون. اهو تمام گهڻو ڊيٽا آهي، پر اسان کي اميد آهي ته ڪافي ماڻهو ان کي سيڊ ڪرڻ جي پرواهه ڪندا. تعاون ان جي سائيز کي ڏسندي، هي مجموعو اسان جي خواهش جي فهرست ۾ ڊگهي عرصي کان آهي، تنهنڪري Z-Library جي بيڪ اپ ۾ اسان جي ڪاميابي کان پوءِ، اسان هن مجموعي تي ڌيان ڏنو. پهرين اسان ان کي سڌو سنئون اسڪراپ ڪيو، جيڪو هڪ وڏو چئلينج هو، ڇاڪاڻ ته انهن جو سرور بهتر حالت ۾ نه هو. اسان هن طريقي سان تقريباً 15TB حاصل ڪيو، پر اهو سست رفتاري سان ٿي رهيو هو. خوشقسمتي سان، اسان لائبريري جي آپريٽر سان رابطو ڪرڻ ۾ ڪامياب ٿياسين، جنهن اسان کي سڌو سنئون سڀ ڊيٽا موڪلڻ تي راضي ڪيو، جيڪو گهڻو تيز هو. اڃا به سڀ ڊيٽا منتقل ڪرڻ ۽ پروسيس ڪرڻ ۾ اڌ سال کان وڌيڪ وقت لڳو، ۽ اسان تقريباً ان کي ڊسڪ ڪرپشن جي ڪري وڃائي ويٺاسين، جنهن جو مطلب هو ته سڀ ڪجهه ٻيهر شروع ڪرڻو پوندو. هن تجربي اسان کي يقين ڏياريو آهي ته اهو ضروري آهي ته هن ڊيٽا کي جلدي کان جلدي ٻاهر ڪڍيو وڃي، ته جيئن اهو هر هنڌ آئيني ٿي سگهي. اسان صرف هڪ يا ٻه بدقسمتي سان وقت ٿيل واقعا پري آهيون هن مجموعي کي هميشه لاءِ وڃائڻ کان! مجموعو جلدي ڪرڻ جو مطلب آهي ته مجموعو ٿورو غير منظم آهي... اچو ته هڪ نظر وجهون. تصور ڪريو ته اسان وٽ هڪ فائل سسٽم آهي (جيڪو حقيقت ۾ اسان ٽورنٽس ۾ ورهائي رهيا آهيون): پهريون ڊائريڪٽري، <code>/repository</code>, هن جو وڌيڪ منظم حصو آهي. هي ڊائريڪٽري نام نهاد "هزار ڊائرز" تي مشتمل آهي: هر هڪ هزارين فائلن سان ڊائريڪٽريز، جيڪي ڊيٽابيس ۾ اضافي طور تي نمبر ٿيل آهن. ڊائريڪٽري <code>0</code> ۾ ڪاميڪ_آئي ڊي 0–999 سان فائلون شامل آهن، ۽ ائين ئي. هي ساڳيو اسڪيم آهي جيڪو لائبريري جينيسس پنهنجي افسانوي ۽ غير افسانوي مجموعن لاءِ استعمال ڪري رهيو آهي. خيال اهو آهي ته هر "هزار ڊائر" خودڪار طريقي سان ٽورنٽ ۾ تبديل ٿي وڃي ٿو جيئن ئي اهو ڀرجي وڃي ٿو. بهرحال، Libgen.li آپريٽر هن مجموعي لاءِ ڪڏهن به ٽورنٽس نه ٺاهيا، ۽ تنهن ڪري هزار ڊائرز شايد غير آرامده ٿي ويا، ۽ "غير ترتيب وار ڊائرز" کي رستو ڏنو. اهي آهن <code>/comics0</code> کان <code>/comics4</code> تائين. انهن سڀني ۾ منفرد ڊائريڪٽري ڍانچا شامل آهن، جيڪي شايد فائلن کي گڏ ڪرڻ لاءِ معقول هئا، پر هاڻي اسان لاءِ گهڻو معنيٰ نٿا رکن. خوشقسمتي سان، metadata اڃا تائين سڌو سنئون انهن سڀني فائلن ڏانهن اشارو ڪري ٿو، تنهنڪري انهن جي ڊسڪ تي اسٽوريج تنظيم اصل ۾ اهم ناهي! metadata MySQL ڊيٽابيس جي صورت ۾ موجود آهي. اهو سڌو سنئون Libgen.li ويب سائيٽ تان ڊائونلوڊ ڪري سگهجي ٿو، پر اسان ان کي ٽورنٽ ۾ پڻ دستياب ڪنداسين، اسان جي پنهنجي ٽيبل سان گڏ جنهن ۾ سڀ MD5 هاشز شامل آهن. <q>ڊاڪٽر باربرا گورڊن پاڻ کي لائبريري جي عام دنيا ۾ وڃائڻ جي ڪوشش ڪري ٿي...</q> Libgen فورڪ پهريون، ڪجهه پس منظر. توهان شايد لائبريري جينيسس کي انهن جي شاندار ڪتابن جي مجموعي لاءِ ڄاڻو ٿا. گهٽ ماڻهو ڄاڻن ٿا ته لائبريري جينيسس جي رضاڪارن ٻين منصوبن کي به تخليق ڪيو آهي، جهڙوڪ رسالن ۽ معياري دستاويزن جو وڏو مجموعو، Sci-Hub جو مڪمل بيڪ اپ (Sci-Hub جي باني، اليگزينڊرا البڪيان سان تعاون ۾)، ۽ يقيناً، ڪاميڪس جو وڏو مجموعو. هڪ وقت تي لائبريري جينيسس جي آئيني سائيٽن جي مختلف آپريٽرن پنهنجا الڳ رستا اختيار ڪيا، جنهن سان موجوده صورتحال پيدا ٿي، جنهن ۾ ڪيترائي مختلف "فورڪ" آهن، جيڪي اڃا تائين لائبريري جينيسس جو نالو کڻي رهيا آهن. Libgen.li فورڪ خاص طور تي هن ڪاميڪس مجموعي کي رکي ٿو، گڏوگڏ هڪ وڏو رسالن جو مجموعو (جنهن تي اسين پڻ ڪم ڪري رهيا آهيون). فنڊ گڏ ڪرڻ اسان هي ڊيٽا ڪجهه وڏن حصن ۾ جاري ڪري رهيا آهيون. پهريون ٽورينٽ <code>/comics0</code> جو آهي، جنهن کي اسان هڪ وڏو 12TB .tar فائل ۾ رکيو آهي. اهو توهان جي هارڊ ڊرائيو ۽ ٽورينٽ سافٽ ويئر لاءِ بهتر آهي جيترا ننڍا فائلون. هن رليز جي حصي طور، اسان فنڊ گڏ ڪرڻ جو ڪم ڪري رهيا آهيون. اسان $20,000 گڏ ڪرڻ جي ڪوشش ڪري رهيا آهيون ته جيئن هن مجموعي جي آپريشنل ۽ معاهدي جي خرچن کي پورو ڪري سگهجي، ۽ جاري ۽ مستقبل جي منصوبن کي فعال ڪري سگهجي. اسان وٽ ڪجهه <em> وڏا </em> منصوبا آهن. <em>مون پنهنجي عطئي سان ڪنهن جي مدد ڪري رهيو آهيان؟</em> مختصر ۾: اسان انسانيت جي سڀني علم ۽ ثقافت کي بيڪ اپ ڪري رهيا آهيون، ۽ ان کي آساني سان رسائي لائق بڻائي رهيا آهيون. اسان جو سڄو ڪوڊ ۽ ڊيٽا اوپن سورس آهي، اسان مڪمل طور تي رضاڪارانه هلندڙ منصوبو آهيون، ۽ اسان 125TB ڪتابن کي محفوظ ڪيو آهي (Libgen ۽ Scihub جي موجوده ٽورينٽس کان علاوه). آخرڪار اسان هڪ فلائي ويل ٺاهي رهيا آهيون جيڪو ماڻهن کي دنيا جي سڀني ڪتابن کي ڳولڻ، اسڪين ڪرڻ، ۽ بيڪ اپ ڪرڻ جي قابل بڻائي ٿو. اسان پنهنجي ماسٽر پلان بابت مستقبل جي پوسٽ ۾ لکنداسين. :) جيڪڏهن توهان 12 مهينن جي "Amazing Archivist" ميمبرشپ ($780) لاءِ عطيو ڪندا آهيو، ته توهان کي <strong> "ٽورينٽ کي اپنائڻ" </strong> جو موقعو ملندو، جنهن جو مطلب آهي ته اسان توهان جو يوزر نيم يا پيغام ٽورينٽس مان هڪ جي فائل نالي ۾ شامل ڪنداسين! توهان <a %(wikipedia_annas_archive)s>Anna’s Archive</a> تي وڃي "Donate" بٽڻ تي ڪلڪ ڪري عطيو ڪري سگهو ٿا. اسان وڌيڪ رضاڪارن جي ڳولا ۾ پڻ آهيون: سافٽ ويئر انجنيئرز، سيڪيورٽي ريسرچرز، گمنام مرچنٽ ماهر، ۽ مترجم. توهان اسان جي مدد ميزباني خدمتون مهيا ڪري ڪري سگهو ٿا. ۽ يقيناً، مهرباني ڪري اسان جي ٽورينٽس کي سيڊ ڪريو! انهن سڀني ماڻهن جو شڪريو جن اڳ ۾ ئي اسان جي سخاوت سان مدد ڪئي آهي! توهان واقعي فرق پيدا ڪري رهيا آهيو. هتي اهي ٽورينٽس آهن جيڪي اڃا تائين جاري ڪيا ويا آهن (اسان باقي کي پروسيس ڪري رهيا آهيون): سڀ ٽورينٽس <a %(wikipedia_annas_archive)s>Anna’s Archive</a> تي "Datasets" هيٺ ملي سگهن ٿا (اسان اتي سڌو سنئون لنڪ نه ٿا ڏيون، ته جيئن هن بلاگ جا لنڪ Reddit، Twitter، وغيره تان نه هٽايا وڃن). اتي کان، Tor ويب سائيٽ تي لنڪ جي پيروي ڪريو. <a %(news_ycombinator)s>Hacker News تي بحث ڪريو</a> اڳتي ڇا آهي؟ ڪجهه ٽورينٽس ڊگهي مدي واري حفاظت لاءِ بهترين آهن، پر روزاني رسائي لاءِ ايترا نه. اسان ميزباني پارٽنرز سان گڏجي ڪم ڪنداسين ته جيئن اهو سڄو ڊيٽا ويب تي آڻي سگهجي (ڇو ته Anna’s Archive سڌو سنئون ڪجهه ميزباني نه ٿو ڪري). يقيناً توهان انهن ڊائون لوڊ لنڪس کي Anna’s Archive تي ڳولي سگهو ٿا. اسان هر ڪنهن کي دعوت ڏئي رهيا آهيون ته هن ڊيٽا سان ڪجهه ڪري! اسان کي ان کي بهتر تجزيو ڪرڻ، ان کي ڊپليڪيٽ ڪرڻ، ان کي IPFS تي رکڻ، ان سان توهان جي AI ماڊلز کي تربيت ڏيڻ، وغيره ۾ مدد ڪريو. اهو سڀ توهان جو آهي، ۽ اسان انتظار نٿا ڪري سگهون ته توهان ان سان ڇا ڪندا. آخرڪار، جيئن اڳ چيو ويو، اسان وٽ اڃا ڪجهه وڏا رليز اچي رهيا آهن (جيڪڏهن <em>ڪو ماڻهو</em> <em>حادثاتي طور</em> اسان کي <em>هڪ خاص</em> ACS4 ڊيٽابيس جو ڊمپ موڪلي سگهي، توهان ڄاڻو ٿا ته اسان کي ڪٿي ڳولڻو آهي...)، ۽ دنيا جي سڀني ڪتابن کي بيڪ اپ ڪرڻ لاءِ فلائي ويل ٺاهڻ. تنهن ڪري تيار رهو، اسان صرف شروعات ڪري رهيا آهيون. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ڪامڪس ڪتابن جي وڏي ۾ وڏي شيڊو لائبريري شايد هڪ خاص لائبريري جينيسس فورڪ جي آهي: Libgen.li. ان سائيٽ کي هلائيندڙ هڪ منتظم 2 ملين کان وڌيڪ فائلن جو هڪ پاڳل ڪامڪس مجموعو گڏ ڪرڻ ۾ ڪامياب ٿيو، جنهن جو مجموعي وزن 95TB کان وڌيڪ آهي. بهرحال، ٻين لائبريري جينيسس مجموعن جي برعڪس، هي هڪ ٽورينٽس ذريعي وڏي پيماني تي دستياب نه هو. توهان صرف انهن ڪامڪس تائين رسائي حاصل ڪري سگهو ٿا ان جي سست ذاتي سرور ذريعي — هڪ واحد ناڪامي جو نقطو. اڄ تائين! هن پوسٽ ۾ اسين توهان کي هن مجموعي بابت وڌيڪ ٻڌائينداسين، ۽ اسان جي فنڊ گڏ ڪرڻ واري مهم بابت، جيڪو هن ڪم جي وڌيڪ حمايت لاءِ آهي. انا جي آرڪائيو دنيا جي وڏي ۾ وڏي ڪامڪس شيڊو لائبريري (95TB) کي بيڪ اپ ڪيو آهي — توهان ان کي سيڊ ڪرڻ ۾ مدد ڪري سگهو ٿا دنيا جي وڏي ۾ وڏي ڪامڪس شيڊو لائبريري وٽ هڪ واحد ناڪامي جو نقطو هو.. اڄ تائين. خبردار: هن بلاگ پوسٽ کي ختم ڪيو ويو آهي. اسان فيصلو ڪيو آهي ته IPFS اڃا تائين وقت لاءِ تيار ناهي. اسان اڃا تائين اننا جي آرڪائيو مان IPFS تي فائلن کي لنڪ ڪنداسين جڏهن ممڪن هجي، پر اسان ان کي پاڻ ميزبان نه ڪنداسين، ۽ نه ئي ٻين کي IPFS استعمال ڪندي آئيني ڪرڻ جي سفارش ڪنداسين. جيڪڏهن توهان اسان جي مجموعي کي محفوظ ڪرڻ ۾ مدد ڪرڻ چاهيو ٿا ته مهرباني ڪري اسان جي ٽورنٽس صفحي کي ڏسو. 5,998,794 ڪتابن کي IPFS تي رکڻ ڪاپين جي واڌاري اسان جي اصل سوال ڏانهن واپس: اسان ڪيئن دعويٰ ڪري سگهون ٿا ته اسان جا مجموعا هميشه لاءِ محفوظ رهندا؟ هتي جو مکيه مسئلو اهو آهي ته اسان جو مجموعو تيزي سان <a %(torrents_stats)s>وڌي رهيو آهي</a>, ڪجهه وڏي مجموعن کي اسڪريپنگ ۽ اوپن سورسنگ ڪندي (انهن شاندار ڪم جي مٿان جيڪو اڳ ۾ ئي ٻين اوپن-ڊيٽا شيڊو لائبريرين جهڙوڪ Sci-Hub ۽ لائبريري جينيسس پاران ڪيو ويو آهي). هن ڊيٽا جي واڌاري سان گڏ مجموعن کي دنيا ۾ آئيني بڻائڻ مشڪل ٿي وڃي ٿو. ڊيٽا اسٽوريج مهانگو آهي! پر اسان پراميد آهيون، خاص طور تي جڏهن هيٺين ٽن رجحانن کي ڏسي رهيا آهيون. اسان جي مجموعن جو <a %(annas_archive_stats)s>مجموعي سائيز</a>, گذريل ڪجهه مهينن ۾، ٽورينٽ سيڊرز جي تعداد سان ٽوڙيو ويو. مختلف ذريعن مان HDD جي قيمتن جا رجحان (مطالعي کي ڏسڻ لاءِ ڪلڪ ڪريو). <a %(critical_window_chinese)s>چيني ورزن 中文版</a>, تي بحث ڪريو <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. اسان گهٽ لٽڪندڙ ميوو چونڊي ورتو آهي هي سڌو سنئون اسان جي مٿي ذڪر ڪيل ترجيحات مان نڪتل آهي. اسان پهرين وڏن مجموعن کي آزاد ڪرڻ تي ڪم ڪرڻ کي ترجيح ڏيون ٿا. هاڻي جڏهن اسان دنيا جي ڪجهه وڏن مجموعن کي محفوظ ڪري چڪا آهيون، اسان کي اميد آهي ته اسان جي واڌاري جي رفتار تمام سست ٿيندي. اڃا تائين ننڍن مجموعن جي هڪ ڊگهي قطار آهي، ۽ نوان ڪتاب هر روز اسڪين يا شايع ٿين ٿا، پر رفتار شايد تمام سست ٿيندي. اسان اڃا تائين سائيز ۾ ٻيڻو يا ٽرپل ٿي سگهون ٿا، پر هڪ ڊگهي وقت جي عرصي ۾. OCR ۾ بهتري. ترجيحات سائنس ۽ انجنيئرنگ سافٽ ويئر ڪوڊ مٿين سڀني جو افسانوي يا تفريحي نسخو جاگرافيائي ڊيٽا (مثال طور نقشا، جاگرافيائي سروي) ڪارپوريشنز يا حڪومتن کان اندروني ڊيٽا (ليڪس) ماپ جا ڊيٽا جهڙوڪ سائنسي ماپون، اقتصادي ڊيٽا، ڪارپوريٽ رپورٽون ميٽا ڊيٽا رڪارڊ عام طور تي (غير افسانوي ۽ افسانوي؛ ٻين ميڊيا، آرٽ، ماڻهن وغيره جا؛ جائزا سميت) غير افسانوي ڪتاب غير افسانوي رسالا، اخبارون، دستياب غير افسانوي تقريرن جا ٽرانسڪرپٽ، دستاويزي فلمون، پوڊ ڪاسٽ نامياتي ڊيٽا جهڙوڪ ڊي اين اي سيڪونس، ٻوٽن جا ٻج، يا مائڪروبيئل نمونا علمي پيپرز، جرنلز، رپورٽون سائنس ۽ انجنيئرنگ ويب سائيٽون، آن لائن بحث مباحثا قانوني يا عدالت جي ڪارروائي جا ٽرانسڪرپٽ منفرد طور تي تباهي جي خطري ۾ (مثال طور جنگ، فنڊنگ جي ڪٽوتي، مقدما، يا سياسي ظلم) نادر منفرد طور تي غير مرڪوز اسان پيپرز ۽ ڪتابن جي باري ۾ ايترو ڇو پرواهه ڪندا آهيون؟ اچو ته عام طور تي تحفظ ۾ اسان جي بنيادي عقيدي کي هڪ طرف رکون - اسان ان بابت هڪ ٻي پوسٽ لکي سگهون ٿا. ته پوءِ خاص طور تي پيپرز ۽ ڪتاب ڇو؟ جواب سادو آهي: <strong>معلومات جي کثافت</strong>. اسٽوريج جي في ميگابائيٽ تي، لکيل متن سڀني ميڊيا مان سڀ کان وڌيڪ معلومات محفوظ ڪري ٿو. جڏهن ته اسان علم ۽ ثقافت ٻنهي جي پرواهه ڪندا آهيون، اسان اڳوڻي جي وڌيڪ پرواهه ڪندا آهيون. مجموعي طور تي، اسان معلومات جي کثافت ۽ تحفظ جي اهميت جي هڪ درجي بندي ڳوليندا آهيون جيڪا تقريباً هن طرح نظر اچي ٿي: هن فهرست ۾ درجه بندي ڪجهه حد تائين من ماني آهي - ڪيترائي شيون ٽائيز آهن يا اسان جي ٽيم اندر اختلاف آهن - ۽ اسان شايد ڪجهه اهم درجا وساري رهيا آهيون. پر هي تقريباً اهو آهي ته اسان ڪيئن ترجيح ڏيون ٿا. انهن مان ڪجهه شيون ٻين کان تمام مختلف آهن ته اسان کي پريشان ٿيڻ جي ضرورت ناهي (يا اڳ ۾ ئي ٻين ادارن طرفان سنڀاليل آهن)، جهڙوڪ نامياتي ڊيٽا يا جاگرافيائي ڊيٽا. پر هن فهرست ۾ گهڻيون شيون اصل ۾ اسان لاءِ اهم آهن. اسان جي ترجيحات ۾ هڪ ٻيو وڏو عنصر اهو آهي ته هڪ خاص ڪم ڪيترو خطري ۾ آهي. اسان انهن ڪمن تي ڌيان ڏيڻ کي ترجيح ڏيون ٿا جيڪي: آخرڪار، اسان کي پيماني جي پرواهه آهي. اسان وٽ محدود وقت ۽ پئسا آهن، تنهنڪري اسان 10,000 ڪتاب بچائڻ لاءِ هڪ مهينو خرچ ڪرڻ پسند ڪنداسين 1,000 ڪتابن جي ڀيٽ ۾ - جيڪڏهن اهي تقريباً برابر قيمتي ۽ خطري ۾ آهن. <em><q>جيڪو وڃائجي چڪو آهي اهو بحال نه ٿو ٿي سگهي؛ پر اچو ته جيڪو باقي آهي ان کي بچايون: نه ته انهن کي عوام جي نظر ۽ استعمال کان بچائڻ لاءِ والٽس ۽ تالا لڳائي، وقت جي ضايع ٿيڻ لاءِ، پر اهڙي ڪاپين جي واڌاري سان، جيڪي انهن کي حادثي جي پهچ کان ٻاهر رکن.</q></em><br>— ٿامس جيفرسن، 1791 شيڊو لائبريريون ڪوڊ گٿب تي اوپن سورس ٿي سگهي ٿو، پر گٿب کي مجموعي طور تي آساني سان آئيني نه ٿو بڻائي سگهجي ۽ ان ڪري محفوظ نه ٿو ڪري سگهجي (جيتوڻيڪ هن خاص صورت ۾ اڪثر ڪوڊ ريپوزيٽريز جا ڪافي ورهايل ڪاپيون موجود آهن) Metadata ريڪارڊ ورلڊ ڪيٽ ويب سائيٽ تي مفت ۾ ڏسي سگهجن ٿا، پر وڏي مقدار ۾ ڊائونلوڊ نه ٿي سگهجن (جيستائين اسان انهن کي <a %(worldcat_scrape)s>اسڪريپ</a> نه ڪيو) Reddit استعمال ڪرڻ لاءِ مفت آهي، پر تازو ئي ڊيٽا-بھوڪ LLM ٽريننگ جي نتيجي ۾ سخت اينٽي-اسڪريپنگ قدمن کي لاڳو ڪيو ويو آهي (ان بابت وڌيڪ بعد ۾) اتي ڪيترائي ادارا آهن جن جا ساڳيا مشن ۽ ساڳيون ترجيحات آهن. حقيقت ۾، لائبريريون، آرڪائيوز، ليب، ميوزيم، ۽ ٻين ادارن کي هن قسم جي تحفظ جي ذميواري ڏني وئي آهي. انهن مان ڪيترائي حڪومتن، فردن، يا ڪارپوريشنز طرفان چڱي طرح فنڊ ٿيل آهن. پر انهن وٽ هڪ وڏو انڌو نقطو آهي: قانوني نظام. هتي شيڊو لائبريرين جو منفرد ڪردار آهي، ۽ ان جو سبب آهي جو انا جو آرڪائيو موجود آهي. اسان اهي شيون ڪري سگهون ٿا جيڪي ٻيا ادارا ڪرڻ جي اجازت نه آهن. هاڻي، اهو (اڪثر) ناهي ته اسان مواد کي آرڪائيو ڪري سگهون ٿا جيڪي ٻئي هنڌ محفوظ ڪرڻ غير قانوني آهن. نه، ڪيترن ئي هنڌن تي ڪنهن به ڪتابن، پيپرز، رسالن وغيره سان آرڪائيو ٺاهڻ قانوني آهي. پر ڇاڪاڻ ته قانوني آرڪائيوز ۾ اڪثر ڪري <strong>اضافي ۽ ڊگهي عمر</strong> جي کوٽ هوندي آهي. اهڙا ڪتاب موجود آهن جن جو صرف هڪ نقل ڪنهن جسماني لائبريري ۾ موجود آهي. اهڙا metadata ريڪارڊ موجود آهن جيڪي هڪ واحد ڪارپوريشن جي حفاظت ۾ آهن. اهڙا اخبارون موجود آهن جيڪي صرف هڪ واحد آرڪائيو ۾ مائڪرو فلم تي محفوظ ٿيل آهن. لائبريريون فنڊنگ ۾ ڪٽوتي جو شڪار ٿي سگهن ٿيون، ڪارپوريشن ديوالي ٿي سگهن ٿيون، آرڪائيوز بمباري ۽ ساڙڻ جو شڪار ٿي سگهن ٿيون. اهو ڪو نظرياتي ڳالهه ناهي - اهو هميشه ٿيندو رهي ٿو. انا جي آرڪائيو ۾ جيڪو اسان منفرد طور تي ڪري سگهون ٿا اهو آهي ڪيترن ئي ڪاپين کي وڏي پيماني تي محفوظ ڪرڻ. اسان پيپرز، ڪتاب، ميگزين ۽ وڌيڪ گڏ ڪري سگهون ٿا، ۽ انهن کي وڏي مقدار ۾ ورهائي سگهون ٿا. اسان هن وقت اهو ٽورينٽس ذريعي ڪري رهيا آهيون، پر صحيح ٽيڪنالاجيون اهم نه آهن ۽ وقت سان تبديل ٿينديون رهنديون. اهم حصو اهو آهي ته ڪيترن ئي ڪاپين کي دنيا ۾ ورهايو وڃي. هي اقتباس 200 سالن کان وڌيڪ پراڻو آهي پر اڃا تائين صحيح آهي: عوامي ڊومين بابت هڪ جلدي نوٽ. ڇاڪاڻ ته انا جي آرڪائيو منفرد طور تي انهن سرگرمين تي ڌيان ڏئي ٿو جيڪي دنيا جي ڪيترن ئي هنڌن تي غير قانوني آهن، اسان عوامي ڊومين ڪتابن جهڙين وسيع طور تي موجود مجموعن سان پريشان نه ٿا ڪريون. قانوني ادارا اڪثر ڪري ان جو سٺو خيال رکندا آهن. بهرحال، اهڙا غور و فڪر آهن جيڪي ڪڏهن ڪڏهن اسان کي عوامي طور تي موجود مجموعن تي ڪم ڪرڻ تي مجبور ڪن ٿا: - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. اسٽوريج جي قيمتن ۾ تيزي سان گهٽتائي جاري آهي 3. معلومات جي کثافت ۾ بهتري اسان هن وقت ڪتابن کي انهن جي خام شڪلن ۾ ذخيرو ڪريون ٿا جيڪي اسان کي ڏنا وڃن ٿا. يقينن، اهي ڪمپريس ٿيل آهن، پر اڪثر ڪري اهي اڃا به وڏا اسڪين يا صفحن جون تصويرون آهن. هينئر تائين، اسان جي مجموعي سائيز کي گهٽائڻ جا واحد آپشن وڌيڪ جارحاڻي ڪمپريشن يا ڊيوپليڪيشن ذريعي رهيا آهن. بهرحال، ڪافي بچت حاصل ڪرڻ لاءِ، ٻئي اسان جي پسند لاءِ تمام گهڻا نقصانڪار آهن. تصويرن جي ڳري ڪمپريشن متن کي بمشڪل پڙهڻ لائق بڻائي سگهي ٿي. ۽ ڊيوپليڪيشن لاءِ ڪتابن جي بلڪل ساڳي هجڻ جي اعليٰ اعتماد جي ضرورت آهي، جيڪا اڪثر ڪري تمام گهڻي غير صحيح آهي، خاص طور تي جيڪڏهن مواد ساڳيو آهي پر اسڪين مختلف موقعن تي ڪيا ويا آهن. هميشه هڪ ٽيون آپشن رهيو آهي، پر ان جي معيار ايتري خراب رهي آهي جو اسان ڪڏهن به ان تي غور نه ڪيو: <strong>OCR، يا آپٽيڪل ڪيريڪٽر ريڪگنيشن</strong>. هي عمل آهي تصويرن کي سادي متن ۾ تبديل ڪرڻ جو، AI استعمال ڪندي تصويرن ۾ ڪيريڪٽرز کي سڃاڻڻ لاءِ. هن لاءِ اوزار گهڻي وقت کان موجود آهن، ۽ ڪافي سٺا رهيا آهن، پر "ڪافي سٺو" محفوظ ڪرڻ جي مقصدن لاءِ ڪافي ناهي. بهرحال، تازو ملٽي موڊل ڊيپ لرننگ ماڊلز انتهائي تيزي سان ترقي ڪئي آهي، جيتوڻيڪ اڃا تائين اعليٰ خرچن تي. اسان توقع ڪريون ٿا ته ايندڙ سالن ۾ درستگي ۽ خرچن ۾ ڊرامائي طور تي بهتري ايندي، ان حد تائين جتي اهو اسان جي سڄي لائبريري تي لاڳو ڪرڻ لاءِ حقيقت پسندانه بڻجي ويندو. جڏهن اهو ٿيندو، اسان شايد اڃا به اصل فائلن کي محفوظ ڪنداسين، پر ان کان علاوه اسان وٽ اسان جي لائبريري جو هڪ تمام ننڍڙو ورزن ٿي سگهي ٿو جيڪو اڪثر ماڻهو آئيني ڪرڻ چاهيندا. دلچسپ ڳالهه اها آهي ته خام متن پاڻ کي اڃا به بهتر ڪمپريس ڪري ٿو، ۽ ڊيوپليڪيٽ ڪرڻ ۾ تمام آسان آهي، جيڪو اسان کي اڃا به وڌيڪ بچت ڏئي ٿو. مجموعي طور تي اهو غير حقيقي ناهي ته ڪل فائل سائيز ۾ گهٽ ۾ گهٽ 5-10x گهٽتائي جي توقع ڪئي وڃي، شايد ان کان به وڌيڪ. جيتوڻيڪ هڪ محتاط 5x گهٽتائي سان، اسان ڏسندا سين <strong> $1,000–$3,000 10 سالن ۾ جيتوڻيڪ اسان جي لائبريري جي سائيز ٽرپل ٿي وڃي </strong>. لکڻ جي وقت، <a %(diskprices)s>ڊسڪ جي قيمتون</a> في ٽي بي نئين ڊسڪ لاءِ تقريباً $12 آهن، استعمال ٿيل ڊسڪ لاءِ $8، ۽ ٽيپ لاءِ $4. جيڪڏهن اسان محتاط ٿيون ۽ صرف نئين ڊسڪ ڏسون، ته ان جو مطلب آهي ته هڪ پيٽابائيٽ کي محفوظ ڪرڻ ۾ تقريباً $12,000 خرچ ٿيندو. جيڪڏهن اسان فرض ڪريون ته اسان جي لائبريري 900TB کان 2.7PB تائين ٽرپل ٿي ويندي، ته ان جو مطلب $32,400 ٿيندو ته اسان جي سڄي لائبريري کي آئيني بڻايو وڃي. بجلي، ٻين هارڊويئر جي قيمت، وغيره شامل ڪندي، اچو ته ان کي $40,000 تائين گول ڪريون. يا ٽيپ سان وڌيڪ $15,000–$20,000. هڪ طرف <strong>سڄي انساني علم جي رقم لاءِ $15,000–$40,000 هڪ سستي آهي</strong>. ٻئي طرف، اهو ٿورو مهانگو آهي ته اميد رکجي ته مڪمل ڪاپيون، خاص طور تي جيڪڏهن اسان چاهيون ٿا ته اهي ماڻهو ٻين جي فائدي لاءِ پنهنجا ٽورينٽ جاري رکن. اهو اڄ آهي. پر ترقي اڳتي وڌندي رهي ٿي: هارڊ ڊرائيو جي قيمتن في ٽي بي گذريل 10 سالن ۾ تقريباً ٽئين حصي ۾ گهٽجي چڪيون آهن، ۽ شايد ساڳئي رفتار سان گهٽجڻ جاري رهنديون. ٽيپ به ساڳئي رفتار تي نظر اچي ٿو. ايس ايس ڊي جي قيمتون اڃا به تيزي سان گهٽجي رهيون آهن، ۽ شايد ڏهاڪي جي آخر تائين ايڇ ڊي ڊي جي قيمتن تي قبضو ڪري سگهن ٿيون. جيڪڏهن اهو صحيح آهي، ته 10 سالن ۾ اسان شايد صرف $5,000–$13,000 تي پنهنجي سڄي مجموعي کي آئيني بڻائڻ لاءِ ڏسي رهيا هونداسين (1/3rd)، يا اڃا به گهٽ جيڪڏهن اسان سائيز ۾ گهٽ وڌون. جڏهن ته اڃا تائين گهڻو پئسو آهي، اهو ڪيترن ئي ماڻهن لاءِ حاصل ڪرڻ لائق هوندو. ۽ اهو شايد اڃا به بهتر ٿي سگهي ٿو ڇاڪاڻ ته ايندڙ نقطي جي ڪري… Anna’s Archive تي، اسان کان اڪثر پڇيو ويندو آهي ته اسان ڪيئن دعويٰ ڪري سگهون ٿا ته اسان جا مجموعا هميشه لاءِ محفوظ رهندا، جڏهن ته مجموعي سائيز اڳ ۾ ئي 1 پيٽابائيٽ (1000 ٽي بي) تائين پهچي رهيو آهي، ۽ اڃا به وڌي رهيو آهي. هن آرٽيڪل ۾ اسان پنهنجي فلسفي تي نظر وجهنداسين، ۽ ڏسنداسين ته ڇو ايندڙ ڏهاڪو اسان جي انسانيت جي علم ۽ ثقافت کي محفوظ ڪرڻ جي مشن لاءِ نازڪ آهي. نازڪ ونڊو جيڪڏهن اهي اڳڪٿيون صحيح آهن، اسان کي <strong>صرف ڪجهه سالن جو انتظار ڪرڻو پوندو</strong> ان کان اڳ جو اسان جو سڄو مجموعو وڏي پيماني تي آئيني ٿي ويندو. تنهن ڪري، ٿامس جيفرسن جي لفظن ۾، "حادثي جي پهچ کان ٻاهر رکيل." بدقسمتي سان، LLMs جي آمد، ۽ انهن جي ڊيٽا-بھوڪ تربيت، ڪيترن ئي ڪاپي رائيٽ هولڊرز کي دفاعي تي رکيو آهي. اڃا به وڌيڪ جيترو اهي اڳ ۾ هئا. ڪيترائي ويب سائيٽون اسڪراپ ۽ آرڪائيو ڪرڻ کي مشڪل بڻائي رهيون آهن، مقدما اڏامي رهيا آهن، ۽ ان دوران جسماني لائبريريون ۽ آرڪائيوز نظرانداز ٿيڻ جاري آهن. اسان صرف انهن رجحانن کي بدتر ٿيڻ جي توقع ڪري سگهون ٿا، ۽ ڪيترائي ڪم عوامي ڊومين ۾ داخل ٿيڻ کان گهڻو اڳ وڃائي سگهجن ٿا. <strong>اسان محفوظ ڪرڻ ۾ انقلاب جي شام تي آهيون، پر <q>جيڪو وڃائي چڪو آهي اهو بحال نه ٿي سگهي ٿو.</q></strong> اسان وٽ تقريباً 5-10 سالن جي نازڪ ونڊو آهي جنهن دوران هڪ شيڊو لائبريري هلائڻ ۽ دنيا جي چوڌاري ڪيترائي آئيني ٺاهڻ اڃا تائين ڪافي مهانگو آهي، ۽ جنهن دوران رسائي مڪمل طور تي بند نه ڪئي وئي آهي. جيڪڏهن اسان هن ونڊو کي پل ڪري سگهون ٿا، ته پوءِ اسان واقعي ۾ انسانيت جي علم ۽ ثقافت کي هميشه لاءِ محفوظ ڪري ڇڏينداسين. اسان کي هن وقت کي ضايع نه ٿيڻ ڏيڻ گهرجي. اسان کي هن نازڪ ونڊو کي اسان تي بند ٿيڻ نه ڏيڻ گهرجي. هلون ٿا. ڇانوَ واري لائبريرين جي نازڪ ونڊو اسان ڪيئن دعويٰ ڪري سگهون ٿا ته اسان جا مجموعا هميشه لاءِ محفوظ رهندا، جڏهن اهي اڳ ۾ ئي 1 پي بي تائين پهچي رهيا آهن؟ مجموعو مجموعي بابت ڪجهه وڌيڪ معلومات. <a %(duxiu)s>Duxiu</a> هڪ وڏو ڊيٽابيس آهي اسڪين ڪيل ڪتابن جو، جيڪو <a %(chaoxing)s>SuperStar Digital Library Group</a> پاران ٺاهيو ويو آهي. اڪثر علمي ڪتاب آهن، جيڪي يونيورسٽين ۽ لائبريرين کي ڊجيٽل طور تي دستياب ڪرڻ لاءِ اسڪين ڪيا ويا آهن. اسان جي انگريزي ڳالهائيندڙ سامعين لاءِ، <a %(library_princeton)s>Princeton</a> ۽ <a %(guides_lib_uw)s>University of Washington</a> وٽ سٺا جائزا آهن. هتي هڪ بهترين مضمون پڻ آهي جيڪو وڌيڪ پس منظر ڏئي ٿو: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (اننا جي آرڪائيو ۾ ان کي ڳوليو). Duxiu جا ڪتاب چيني انٽرنيٽ تي گهڻي وقت کان پائيرٽ ڪيا ويا آهن. عام طور تي اهي ٻيهر وڪڻندڙن پاران هڪ ڊالر کان گهٽ ۾ وڪرو ڪيا ويندا آهن. اهي عام طور تي گوگل ڊرائيو جي چيني برابر استعمال ڪندي ورهايا ويندا آهن، جنهن کي اڪثر ڪري وڌيڪ اسٽوريج جي جاءِ لاءِ هيڪ ڪيو ويو آهي. ڪجهه ٽيڪنيڪل تفصيل <a %(github_duty_machine)s>هتي</a> ۽ <a %(github_821_github_io)s>هتي</a> ملي سگهن ٿا. جيتوڻيڪ ڪتاب نيم عوامي طور تي ورهايا ويا آهن، انهن کي وڏي پيماني تي حاصل ڪرڻ ڏاڍو مشڪل آهي. اسان ان کي پنهنجي TODO لسٽ ۾ اعليٰ ترجيح تي رکيو، ۽ ان لاءِ مڪمل وقت جي ڪم جا ڪيترائي مهينا مختص ڪيا. بهرحال، تازو هڪ ناقابل يقين، شاندار، ۽ باصلاحيت رضاڪار اسان سان رابطو ڪيو، ٻڌائيندي ته انهن اڳ ۾ ئي اهو سڀ ڪم ڪيو هو - وڏي خرچ تي. انهن اسان سان مڪمل مجموعو شيئر ڪيو، بغير ڪنهن بدلي جي توقع جي، سواءِ ڊگهي مدي واري حفاظت جي ضمانت جي. واقعي شاندار. انهن مجموعي کي OCR ڪرڻ لاءِ هن طريقي سان مدد لاءِ پڇڻ تي راضپو ڏيکاريو. مجموعو 7,543,702 فائلون آهن. اهو لائبريري جينيسس جي غير افسانوي (تقريباً 5.3 ملين) کان وڌيڪ آهي. موجوده صورت ۾ ڪل فائل سائيز تقريباً 359TB (326TiB) آهي. اسان ٻين تجويزن ۽ خيالن لاءِ کليل آهيون. بس اسان سان رابطو ڪريو. اسان جي مجموعن، حفاظت جي ڪوششن، ۽ توهان ڪيئن مدد ڪري سگهو ٿا بابت وڌيڪ معلومات لاءِ اننا جي آرڪائيو کي چيڪ ڪريو. مهرباني! مثال صفحا اسان کي ثابت ڪرڻ لاءِ ته توهان وٽ هڪ سٺي پائيپ لائين آهي، هتي ڪجهه مثال صفحا آهن جن تي ڪم شروع ڪرڻ لاءِ، هڪ ڪتاب مان جيڪو سپر ڪنڊڪٽرز تي آهي. توهان جي پائيپ لائين کي صحيح طريقي سان رياضي، ٽيبل، چارٽ، فوٽ نوٽ وغيره کي سنڀالڻ گهرجي. پنهنجا پروسيس ڪيل صفحا اسان جي اي ميل تي موڪليو. جيڪڏهن اهي سٺا لڳن ٿا، ته اسان توهان کي وڌيڪ خانگي طور تي موڪلينداسين، ۽ اسان توقع ڪنداسين ته توهان انهن تي به جلدي پنهنجي پائيپ لائين هلائي سگهو ٿا. هڪ دفعو اسان مطمئن ٿي ويا، ته اسان هڪ معاهدو ڪري سگهون ٿا. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>چيني ورزن 中文版</a>, <a %(news_ycombinator)s>Hacker News تي بحث ڪريو</a> هي هڪ مختصر بلاگ پوسٽ آهي. اسان ڪنهن ڪمپني يا اداري کي ڳولي رهيا آهيون جيڪو اسان جي حاصل ڪيل وڏي مجموعي لاءِ OCR ۽ متن جي استخراج ۾ مدد ڪري سگهي، خاص ابتدائي رسائي جي بدلي ۾. پابندي جي مدت کان پوءِ، اسان يقيناً سڄو مجموعو جاري ڪنداسين. LLM جي تربيت لاءِ اعليٰ معيار جو علمي متن انتهائي ڪارائتو آهي. جڏهن ته اسان جو مجموعو چيني آهي، اهو انگريزي LLM جي تربيت لاءِ به ڪارائتو ٿي سگهي ٿو: ماڊل لڳي ٿو ته تصور ۽ ڄاڻ کي ڪنهن به ٻولي کان سواءِ انڪوڊ ڪن ٿا. ان لاءِ، متن کي اسڪين مان ڪڍڻ جي ضرورت آهي. اننا جي آرڪائيو کي ان مان ڇا ملي ٿو؟ ان جي استعمال ڪندڙن لاءِ ڪتابن جي مڪمل متن جي ڳولا. ڇو ته اسان جا مقصد LLM ڊولپرز سان هم آهنگ آهن، اسان هڪ ساٿي ڳولي رهيا آهيون. جيڪڏهن توهان صحيح OCR ۽ متن ڪڍڻ ڪري سگهو ٿا، ته اسان توهان کي <strong>هن مجموعي تائين خاص ابتدائي رسائي 1 سال لاءِ</strong> ڏيڻ لاءِ تيار آهيون. جيڪڏهن توهان اسان سان پنهنجي پائيپ لائين جو سڄو ڪوڊ شيئر ڪرڻ لاءِ تيار آهيو، ته اسان مجموعي کي وڌيڪ وقت تائين روڪڻ لاءِ تيار آهيون. LLM ڪمپنين لاءِ دنيا جي سڀ کان وڏي چيني غير افسانوي ڪتابن جي مجموعي تائين خاص رسائي <em><strong>مختصر خلاصو:</strong> انا جو آرڪائيو 7.5 ملين / 350TB چيني غير افسانوي ڪتابن جو هڪ منفرد مجموعو حاصل ڪيو - لائبريري جينيسس کان وڏو. اسان هڪ LLM ڪمپني کي خاص رسائي ڏيڻ لاءِ تيار آهيون، اعليٰ معيار جي OCR ۽ متن جي استخراج جي بدلي ۾.</em> سسٽم آرڪيٽيڪچر ته اچو ته چئون ته توهان ڪجهه ڪمپنيون ڳوليون جيڪي توهان جي ويب سائيٽ کي ميزباني ڪرڻ لاءِ تيار آهن بغير توهان کي بند ڪرڻ جي - اچو ته انهن کي "آزادي پسند فراهم ڪندڙ" سڏيون 😄. توهان جلدي ڳوليندا ته انهن سان سڀ ڪجهه ميزباني ڪرڻ ڪافي مهانگو آهي، تنهنڪري توهان شايد ڪجهه "سستا فراهم ڪندڙ" ڳولڻ چاهيو ٿا ۽ اتي اصل ميزباني ڪريو، آزادي پسند فراهم ڪندڙن ذريعي پراکسي. جيڪڏهن توهان اهو صحيح ڪريو، سستا فراهم ڪندڙ ڪڏهن به نه ڄاڻندا ته توهان ڇا ميزباني ڪري رهيا آهيو، ۽ ڪڏهن به ڪا شڪايت نه ملندي. انهن سڀني فراهم ڪندڙن سان توهان کي بند ڪرڻ جو خطرو آهي، تنهنڪري توهان کي پڻ اضافي ضرورت آهي. اسان کي پنهنجي اسٽيڪ جي سڀني سطحن تي هن جي ضرورت آهي. هڪ ڪجهه حد تائين آزادي پسند ڪمپني جيڪا پاڻ کي هڪ دلچسپ پوزيشن ۾ رکيو آهي Cloudflare آهي. انهن <a %(blog_cloudflare)s>دليل ڏنو</a> آهي ته اهي ميزباني فراهم ڪندڙ نه آهن، پر هڪ يوٽيلٽي، جيئن هڪ ISP. اهي ان ڪري DMCA يا ٻين ٽيڪ ڊائون درخواستن جي تابع نه آهن، ۽ توهان جي اصل ميزباني فراهم ڪندڙ ڏانهن ڪنهن به درخواست کي اڳتي وڌائيندا آهن. انهن هن ڍانچي کي بچائڻ لاءِ عدالت ۾ وڃڻ تائين ويا آهن. اسان ان ڪري انهن کي ڪيشنگ ۽ تحفظ جي هڪ ٻئي پرت طور استعمال ڪري سگهون ٿا. Cloudflare گمنام ادائيگيون قبول نٿو ڪري، تنهنڪري اسان صرف انهن جي مفت منصوبي کي استعمال ڪري سگهون ٿا. ان جو مطلب آهي ته اسان انهن جي لوڊ بيالنسنگ يا فيلوور خاصيتن کي استعمال نٿا ڪري سگهون. اسان ان ڪري <a %(annas_archive_l255)s>ان کي پاڻ لاڳو ڪيو</a> ڊومين سطح تي. صفحي جي لوڊ تي، برائوزر چيڪ ڪندو ته موجوده ڊومين اڃا تائين موجود آهي، ۽ جيڪڏهن نه، اهو سڀني URLs کي مختلف ڊومين ڏانهن ٻيهر لکي ٿو. جئين Cloudflare ڪيترن ئي صفحن کي ڪيش ڪري ٿو، ان جو مطلب آهي ته هڪ صارف اسان جي مکيه ڊومين تي لهي سگهي ٿو، جيتوڻيڪ پراکسي سرور بند آهي، ۽ پوءِ ايندڙ ڪلڪ تي هڪ ٻئي ڊومين ڏانهن منتقل ٿي سگهي ٿو. اسان وٽ اڃا تائين عام آپريشنل خدشات آهن، جهڙوڪ سرور جي صحت جي نگراني، بيڪ اينڊ ۽ فرنٽ اينڊ غلطيون لاگ ڪرڻ، ۽ انهي تي. اسان جي فيلوور آرڪيٽيڪچر هن محاذ تي وڌيڪ مضبوط ٿيڻ جي اجازت ڏئي ٿي، مثال طور هڪ ڊومين تي سرورز جو هڪ مڪمل مختلف سيٽ هلائڻ سان. اسان ان الڳ ڊومين تي ڪوڊ ۽ ڊيٽا سيٽس جا پراڻا ورزن به هلائي سگهون ٿا، جيڪڏهن مکيه ورزن ۾ ڪو اهم بگ نظرانداز ٿي وڃي. اسان Cloudflare جي خلاف ٿيڻ جي خلاف پڻ حفاظتي انتظام ڪري سگهون ٿا، ان کي هڪ ڊومين مان هٽائي، جهڙوڪ هي الڳ ڊومين. انهن خيالن جي مختلف ترتيبون ممڪن آهن. نتيجو اهو هڪ دلچسپ تجربو رهيو آهي ته ڪيئن هڪ مضبوط ۽ لچڪدار شيڊو لائبريري سرچ انجڻ قائم ڪجي. بعد ۾ پوسٽن ۾ حصيداري ڪرڻ لاءِ وڌيڪ تفصيل آهن، تنهنڪري مون کي ٻڌايو ته توهان وڌيڪ ڇا سکڻ چاهيو ٿا! هميشه وانگر، اسان هن ڪم جي حمايت لاءِ عطيا ڳولي رهيا آهيون، تنهنڪري پڪ ڪريو ته Anna جي آرڪائيو تي ڊونيٽ صفحو چيڪ ڪريو. اسان ٻين قسمن جي حمايت پڻ ڳولي رهيا آهيون، جهڙوڪ گرانٽس، ڊگهي مدي وارا اسپانسر، هاءِ رسڪ ادائيگي فراهم ڪندڙ، شايد (ذوق سان!) اشتهار پڻ. ۽ جيڪڏهن توهان پنهنجو وقت ۽ مهارتون ڏيڻ چاهيو ٿا، اسان هميشه ڊولپرز، مترجم، وغيره ڳولي رهيا آهيون. توهان جي دلچسپي ۽ حمايت لاءِ مهرباني. جدت جا ٽوڪن اچو ته اسان جي ٽيڪ اسٽيڪ سان شروع ڪريون. اهو ڄاڻي واڻي بورنگ آهي. اسان Flask، MariaDB، ۽ ElasticSearch استعمال ڪندا آهيون. اهو لفظي طور تي اهو آهي. ڳولا وڏي حد تائين حل ٿيل مسئلو آهي، ۽ اسان ان کي ٻيهر ايجاد ڪرڻ جو ارادو نٿا رکون. ان کان علاوه، اسان کي پنهنجا <a %(mcfunley)s>جدت جا ٽوڪن</a> ڪنهن ٻئي شيءِ تي خرچ ڪرڻا پوندا: اختيارين طرفان بند ٿيڻ کان بچڻ. ته پوءِ انا جي آرڪائيو ڪيترو قانوني يا غير قانوني آهي؟ اهو گهڻو ڪري قانوني دائري تي منحصر آهي. گهڻا ملڪ ڪنهن نه ڪنهن قسم جي ڪاپي رائيٽ تي يقين رکندا آهن، جنهن جو مطلب آهي ته ماڻهن يا ڪمپنين کي ڪجهه قسم جي ڪم تي هڪ خاص وقت لاءِ خاص اجاراداري ڏني ويندي آهي. هڪ طرف، انا جي آرڪائيو تي اسان يقين رکون ٿا ته جڏهن ڪجهه فائدا آهن، مجموعي طور تي ڪاپي رائيٽ سماج لاءِ هڪ منفي آهي - پر اهو ڪنهن ٻئي وقت جي ڪهاڻي آهي. ڪجهه ڪم تي هي خاص اجاراداري جو مطلب آهي ته ان اجاراداري کان ٻاهر ڪنهن لاءِ به انهن ڪم کي سڌو سنئون ورهائڻ غير قانوني آهي - بشمول اسان. پر انا جي آرڪائيو هڪ سرچ انجڻ آهي جيڪا انهن ڪم کي سڌو سنئون ورهائي نٿي (گهٽ ۾ گهٽ اسان جي ڪليئرنيٽ ويب سائيٽ تي نه)، تنهنڪري اسان کي ٺيڪ هجڻ گهرجي، صحيح؟ بلڪل نه. ڪيترن ئي دائري ۾ اهو نه رڳو ڪاپي رائيٽ ٿيل ڪم کي ورهائڻ غير قانوني آهي، پر انهن هنڌن سان ڳنڍڻ به غير قانوني آهي جيڪي ڪندا آهن. ان جو هڪ کلاسک مثال آمريڪا جو DMCA قانون آهي. اهو اسپيڪٽرم جي سخت ترين پڇاڙي آهي. اسپيڪٽرم جي ٻئي پڇاڙي تي نظرياتي طور تي اهڙا ملڪ ٿي سگهن ٿا جن ۾ ڪاپي رائيٽ قانون نه هجي، پر اهي حقيقت ۾ موجود نه آهن. تقريباً هر ملڪ وٽ ڪجهه نه ڪجهه قسم جو ڪاپي رائيٽ قانون آهي. عملدرآمد هڪ مختلف ڪهاڻي آهي. اهڙا ڪيترائي ملڪ آهن جن جي حڪومتون ڪاپي رائيٽ قانون لاڳو ڪرڻ جي پرواهه نٿيون ڪن. اهڙا ملڪ پڻ آهن جيڪي ٻن انتهاين جي وچ ۾ آهن، جيڪي ڪاپي رائيٽ ٿيل ڪم کي ورهائڻ کان منع ڪن ٿا، پر اهڙن ڪم سان ڳنڍڻ کان منع نٿا ڪن. هڪ ٻيو غور ڪرڻ وارو عنصر ڪمپني جي سطح تي آهي. جيڪڏهن هڪ ڪمپني اهڙي دائري ۾ ڪم ڪري ٿي جيڪا ڪاپي رائيٽ جي پرواهه نٿي ڪري، پر ڪمپني پاڻ ڪنهن به خطري کي وٺڻ لاءِ تيار نه آهي، ته پوءِ اهي توهان جي ويب سائيٽ کي بند ڪري سگهن ٿا جيترو جلد ڪو به ان بابت شڪايت ڪري. آخرڪار، هڪ وڏو غور ادائيگيون آهن. جئين اسان کي گمنام رهڻ جي ضرورت آهي، اسان روايتي ادائيگي جي طريقن کي استعمال نٿا ڪري سگهون. اهو اسان کي cryptocurrencies سان ڇڏي ٿو، ۽ صرف ڪمپنين جو هڪ ننڍڙو ذيلي سيٽ انهن کي سپورٽ ڪري ٿو (اتي ورچوئل ڊيبٽ ڪارڊ آهن جيڪي crypto سان ادا ڪيا ويا آهن، پر اهي اڪثر قبول نه ڪيا ويا آهن). - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) مان <a %(wikipedia_annas_archive)s>اننا جي آرڪائيو</a> هلائيندو آهيان، دنيا جو سڀ کان وڏو اوپن سورس غير منافع بخش سرچ انجڻ <a %(wikipedia_shadow_library)s>شيڊو لائبريريز</a> لاءِ، جهڙوڪ Sci-Hub، لائبريري جينيسس، ۽ Z-Library. اسان جو مقصد علم ۽ ثقافت کي آساني سان دستياب ڪرڻ آهي، ۽ آخرڪار ماڻهن جي هڪ ڪميونٽي ٺاهڻ آهي جيڪي گڏجي <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>دنيا جا سڀ ڪتاب</a> آرڪائيو ۽ محفوظ ڪن. هن مضمون ۾ مان ڏيکاريندس ته اسان هي ويب سائيٽ ڪيئن هلائيندا آهيون، ۽ هڪ ويب سائيٽ هلائڻ سان گڏ ايندڙ منفرد چئلينجز جيڪي قانوني حيثيت ۾ شڪ ۾ آهن، ڇاڪاڻ ته شيڊو چيريٽيز لاءِ ڪو "AWS" ناهي. <em>ساٿي مضمون کي پڻ چيڪ ڪريو <a %(blog_how_to_become_a_pirate_archivist)s>ڪيئن هڪ پائيرٽ آرڪائيوسٽ بڻجي</a>.</em> ڪيئن هڪ شيڊو لائبريري هلائجي: اننا جي آرڪائيو ۾ آپريشن شيڊو چيريٽيز لاءِ ڪو <q>AWS ناهي،</q> ته پوءِ اسان اننا جي آرڪائيو کي ڪيئن هلائيندا آهيون؟ اوزار ايپليڪيشن سرور: Flask، MariaDB، ElasticSearch، Docker. ترقي: Gitlab، Weblate، Zulip. سرور مينيجمينٽ: Ansible، Checkmk، UFW. Onion جامد ميزباني: Tor، Nginx. پراڪسي سرور: Varnish. اچو ته ڏسون ته اسان هن سڀني کي حاصل ڪرڻ لاءِ ڪهڙا اوزار استعمال ڪريون ٿا. جيئن اسان نون مسئلن ۾ هلون ٿا ۽ نوان حل ڳوليون ٿا، اهو تمام گهڻو ترقي ڪري رهيو آهي. ڪجهه فيصلا آهن جن تي اسان اڳتي پوئتي ويا آهيون. هڪ آهي سرورز جي وچ ۾ رابطي: اسان اڳ ۾ ان لاءِ Wireguard استعمال ڪندا هئاسين، پر اهو معلوم ٿيو ته اهو ڪڏهن ڪڏهن ڪنهن به ڊيٽا کي منتقل ڪرڻ بند ڪري ٿو، يا صرف هڪ طرف ڊيٽا منتقل ڪري ٿو. اهو ڪيترن ئي مختلف Wireguard سيٽ اپس سان ٿيو جيڪي اسان ڪوشش ڪئي، جهڙوڪ <a %(github_costela_wesher)s>wesher</a> ۽ <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. اسان SSH جي ذريعي پورٽس کي ٽنل ڪرڻ جي ڪوشش ڪئي، autossh ۽ sshuttle استعمال ڪندي، پر <a %(github_sshuttle)s>اتي مسئلن</a> ۾ اچي ويا (جيتوڻيڪ اهو اڃا تائين مون لاءِ واضح ناهي ته autossh TCP-over-TCP مسئلن کان متاثر ٿئي ٿو يا نه - اهو صرف مون کي هڪ جينڪي حل وانگر محسوس ٿئي ٿو پر شايد اهو اصل ۾ ٺيڪ آهي؟). ان جي بدران، اسان سڌي رابطن ڏانهن واپس موٽي آيا آهيون سرورز جي وچ ۾، لڪائڻ لاءِ ته هڪ سرور سستا فراهم ڪندڙن تي هلندڙ آهي IP-filtering سان UFW استعمال ڪندي. ان جو نقصان اهو آهي ته Docker UFW سان چڱي طرح ڪم نٿو ڪري، جيستائين توهان <code>network_mode: "host"</code> استعمال نه ڪريو. اهو سڀ ڪجهه ٿورو وڌيڪ غلطيءَ جو شڪار آهي، ڇو ته توهان پنهنجي سرور کي انٽرنيٽ تي صرف هڪ ننڍڙي غلط ترتيب سان ظاهر ڪندا. شايد اسان کي autossh ڏانهن واپس وڃڻ گهرجي - هتي راءِ تمام گهڻي خوش آمديد هوندي. اسان Varnish ۽ Nginx تي به اڳتي پوئتي ويا آهيون. اسان هن وقت Varnish پسند ڪندا آهيون، پر ان ۾ ان جا عجيب ۽ سخت ڪنارا آهن. ساڳيو Checkmk تي لاڳو ٿئي ٿو: اسان ان کي پسند نٿا ڪريون، پر اهو هن وقت لاءِ ڪم ڪري ٿو. Weblate ٺيڪ رهيو آهي پر ناقابل يقين نه - مان ڪڏهن ڪڏهن ڊڄان ٿو ته اهو منهنجي ڊيٽا وڃائي ڇڏيندو جڏهن به مان ان کي اسان جي git repo سان هم وقت ڪرڻ جي ڪوشش ڪريان ٿو. Flask مجموعي طور تي سٺو رهيو آهي، پر ان ۾ ڪجهه عجيب عادتون آهن جن کي ڊيبگ ڪرڻ ۾ گهڻو وقت لڳو آهي، جهڙوڪ ڪسٽم ڊومينز کي ترتيب ڏيڻ، يا ان جي SqlAlchemy انٽيگريشن سان مسئلا. هينئر تائين ٻيا اوزار بهترين رهيا آهن: اسان کي MariaDB، ElasticSearch، Gitlab، Zulip، Docker، ۽ Tor بابت ڪو به سنجيده شڪايت ناهي. انهن سڀني ۾ ڪجهه مسئلا رهيا آهن، پر ڪجھ به تمام سنجيده يا وقت ضايع ڪندڙ ناهي. ڪميونٽي پهريون چئلينج شايد هڪ حيرت انگيز هجي. اهو نه ته هڪ ٽيڪنيڪل مسئلو آهي، ۽ نه ئي هڪ قانوني مسئلو. اهو هڪ نفسياتي مسئلو آهي: هن ڪم کي ڇانو ۾ ڪرڻ انتهائي اڪيلائي وارو ٿي سگهي ٿو. ان تي منحصر آهي ته توهان ڇا ڪرڻ جي منصوبابندي ڪري رهيا آهيو، ۽ توهان جو خطري جو ماڊل، توهان کي تمام گهڻو محتاط رهڻو پوندو. اسپيڪٽرم جي هڪ پڇاڙي تي اسان وٽ اهڙا ماڻهو آهن جهڙوڪ اليگزينڊرا البڪيان*، Sci-Hub جي باني، جيڪا پنهنجي سرگرمين بابت تمام گهڻو کليل آهي. پر هوءَ هن وقت ڪنهن مغربي ملڪ جو دورو ڪندي ته گرفتار ٿيڻ جي اعليٰ خطري ۾ آهي، ۽ ڏهاڪن جي قيد جي سزا کي منهن ڏئي سگهي ٿي. ڇا اهو هڪ خطرو آهي جيڪو توهان وٺڻ لاءِ تيار هوندا؟ اسان اسپيڪٽرم جي ٻئي پڇاڙي تي آهيون؛ ڪنهن به نشان کي نه ڇڏڻ لاءِ تمام گهڻو محتاط رهڻ، ۽ مضبوط آپريشنل سيڪيورٽي هجڻ. * جيئن HN تي "ynno" پاران ذڪر ڪيو ويو، اليگزينڊرا شروعات ۾ سڃاڻپ نه ٿيڻ چاهيو: "هن جا سرور PHP کان تفصيلي غلطي جا پيغام خارج ڪرڻ لاءِ سيٽ ڪيا ويا هئا، بشمول فالٽنگ سورس فائل جو مڪمل رستو، جيڪو ڊائريڪٽري /home/<USER>" تنهن ڪري، انهن ڪمپيوٽرن تي بي ترتيب صارف نام استعمال ڪريو جيڪي توهان هن مواد لاءِ استعمال ڪندا آهيو، جيڪڏهن توهان ڪجهه غلط ترتيب ڏيو. تنهن هوندي، اها رازداري نفسياتي قيمت سان گڏ اچي ٿي. اڪثر ماڻهو ان ڪم لاءِ سڃاڻپ حاصل ڪرڻ پسند ڪن ٿا جيڪو اهي ڪن ٿا، ۽ اڃا تائين توهان حقيقي زندگي ۾ ان لاءِ ڪو به ڪريڊٽ نه وٺي سگهو ٿا. اڃا تائين سادي شيون چئلينج ٿي سگهن ٿيون، جهڙوڪ دوستن کان پڇڻ ته توهان ڇا ڪري رهيا آهيو (هڪ نقطي تي "منهنجي NAS / homelab سان گڙٻڙ" پراڻو ٿي وڃي ٿو). اهو ئي سبب آهي ته ڪجهه ڪميونٽي ڳولڻ ايترو ضروري آهي. توهان ڪجهه آپريشنل سيڪيورٽي کي ڇڏي سگهو ٿا ڪجهه تمام ويجهن دوستن تي اعتماد ڪندي، جن کي توهان ڄاڻو ٿا ته توهان کي گهڻو اعتماد ڪري سگهو ٿا. اڃا تائين محتاط رهو ته ڪجهه به لکڻ ۾ نه وجھو، جيڪڏهن انهن کي پنهنجا اي ميلون اختيارين کي ڏيڻيون پون، يا جيڪڏهن انهن جا ڊوائيس ڪنهن ٻئي طريقي سان سمجهوتو ٿيل آهن. بهتر اڃا تائين ڪجهه ساٿي قزاق ڳولڻ آهي. جيڪڏهن توهان جا ويجها دوست توهان سان شامل ٿيڻ ۾ دلچسپي رکن ٿا، بهترين! ٻي صورت ۾، توهان شايد ٻين کي آن لائن ڳولي سگهو ٿا. افسوس، هي اڃا تائين هڪ خاص ڪميونٽي آهي. ايتري تائين اسان کي صرف ڪجهه ٻيا مليا آهن جيڪي هن جڳهه ۾ سرگرم آهن. سٺي شروعاتي جڳهون لڳي ٿي لائبريري جينيسس فورمز، ۽ r/DataHoarder. آرڪائيو ٽيم وٽ پڻ هم خيال فرد آهن، جيتوڻيڪ اهي قانون جي اندر ڪم ڪن ٿا (جيڪڏهن قانون جي ڪجهه سرمائي علائقن ۾). روايتي "warez" ۽ pirating منظرن ۾ پڻ اهڙا ماڻهو آهن جيڪي ساڳئي طريقي سان سوچين ٿا. اسان ڪميونٽي کي وڌائڻ ۽ خيالن کي ڳولڻ لاءِ خيالن لاءِ کليل آهيون. مهرباني ڪري اسان کي Twitter يا Reddit تي پيغام موڪليو. شايد اسان ڪنهن قسم جي فورم يا چيٽ گروپ جي ميزباني ڪري سگهون ٿا. هڪ چئلينج اهو آهي ته اهو آساني سان سينسر ٿي سگهي ٿو جڏهن عام پليٽ فارمن کي استعمال ڪندي، تنهنڪري اسان کي ان کي پاڻ ميزباني ڪرڻو پوندو. هتي پڻ هڪ واپار آهي انهن بحثن کي مڪمل طور تي عوامي بڻائڻ (وڌيڪ ممڪن مصروفيت) بمقابله ان کي نجي بڻائڻ (ممڪن "هدفن" کي نه ٻڌائڻ ته اسان انهن کي اسڪراپ ڪرڻ وارا آهيون). اسان کي ان بابت سوچڻو پوندو. اسان کي ٻڌايو ته جيڪڏهن توهان هن ۾ دلچسپي رکو ٿا! نتيجو اميد آهي ته هي نوان شروع ٿيندڙ قزاق آرڪائيوسٽن لاءِ مددگار ثابت ٿيندو. اسان توهان کي هن دنيا ۾ ڀليڪار ڪرڻ لاءِ پرجوش آهيون، تنهنڪري رابطو ڪرڻ ۾ نه هٻڪيو. اچو ته دنيا جي علم ۽ ثقافت کي جيترو ٿي سگهي محفوظ ڪريون، ۽ ان کي هر هنڌ پهچايون. پروجيڪٽس 4. ڊيٽا چونڊ اڪثر ڪري توهان metadata استعمال ڪري سگهو ٿا ته ڊيٽا جو مناسب ذيلي سيٽ ڊائونلوڊ ڪرڻ لاءِ. جيتوڻيڪ جيڪڏهن توهان آخرڪار سڄو ڊيٽا ڊائونلوڊ ڪرڻ چاهيو ٿا، اهو مفيد ٿي سگهي ٿو ته پهريان سڀ کان اهم شيون ترجيح ڏيو، انهي صورت ۾ جيڪڏهن توهان کي پڪڙيو وڃي ۽ دفاع بهتر ڪيو وڃي، يا ڇو ته توهان کي وڌيڪ ڊسڪون خريد ڪرڻ جي ضرورت پوندي، يا صرف انهي ڪري ته توهان جي زندگي ۾ ڪجهه ٻيو اچي وڃي ان کان اڳ جو توهان سڀ ڪجهه ڊائونلوڊ ڪري سگهو. مثال طور، هڪ مجموعو شايد ساڳئي بنيادي وسيلن (جهڙوڪ ڪتاب يا فلم) جا ڪيترائي ايڊيشن هجن، جتي هڪ کي بهترين معيار جي طور تي نشان لڳايو ويو آهي. انهن ايڊيشن کي پهرين محفوظ ڪرڻ جو وڏو مطلب ٿيندو. توهان شايد آخرڪار سڀني ايڊيشن کي محفوظ ڪرڻ چاهيو ٿا، ڇاڪاڻ ته ڪجهه حالتن ۾ metadata غلط طور تي ٽيگ ٿيل ٿي سگهي ٿو، يا شايد ايڊيشن جي وچ ۾ اڻڄاتل سوديبازي هجي (مثال طور، "بهترين ايڊيشن" گهڻن طريقن سان بهترين ٿي سگهي ٿو پر ٻين طريقن سان خراب ٿي سگهي ٿو، جهڙوڪ فلم جو وڌيڪ ريزوليوشن هجڻ پر سب ٽائيٽلز غائب هجڻ). توهان پنهنجي metadata ڊيٽابيس کي دلچسپ شيون ڳولڻ لاءِ پڻ ڳولي سگهو ٿا. سڀ کان وڏو فائل ڪهڙو آهي جيڪو ميزباني ڪئي وئي آهي، ۽ اهو ايترو وڏو ڇو آهي؟ سڀ کان ننڍو فائل ڪهڙو آهي؟ ڇا ڪجهه درجي بندي، ٻولين وغيره جي لحاظ سان دلچسپ يا غير متوقع نمونا آهن؟ ڇا اتي نقل يا تمام گهڻا ساڳيا عنوان آهن؟ ڇا ڊيٽا شامل ٿيڻ وقت نمونا آهن، جهڙوڪ هڪ ڏينهن جنهن ۾ ڪيترائي فائلون هڪ ئي وقت شامل ڪيون ويون؟ توهان اڪثر ڪري مختلف طريقن سان ڊيٽا سيٽ کي ڏسي گهڻو ڪجهه سکي سگهو ٿا. اسان جي صورت ۾، اسان Z-Library ڪتابن کي Library Genesis ۾ md5 hashes جي خلاف deduplicate ڪيو، اهڙي طرح ڊائونلوڊ وقت ۽ ڊسڪ جي جاءِ کي بچايو. بهرحال، هي هڪ بلڪل منفرد صورتحال آهي. اڪثر حالتن ۾ اتي جامع ڊيٽابيس نه آهن جن ۾ ڪهڙيون فائلون اڳ ۾ ئي صحيح طريقي سان محفوظ ڪيون ويون آهن. هي پاڻ ۾ ڪنهن لاءِ هڪ وڏو موقعو آهي. اهو عظيم ٿيندو ته هڪ باقاعده تازه ڪاري ٿيل جائزو هجي جهڙوڪ موسيقي ۽ فلمون جيڪي اڳ ۾ ئي ٽورينٽ ويب سائيٽن تي وڏي پيماني تي سيڊ ٿيل آهن، ۽ ان ڪري قزاقي آئيني ۾ شامل ڪرڻ لاءِ گهٽ ترجيح آهن. 6. تقسيم توهان وٽ ڊيٽا آهي، اهڙي طرح توهان کي دنيا جي پهرين قزاقي آئيني جو مالڪ بڻائي ٿو (گهڻو ڪري). ڪيترن ئي طريقن سان سڀ کان ڏکيو حصو ختم ٿي چڪو آهي، پر سڀ کان وڌيڪ خطري وارو حصو اڃا تائين توهان جي اڳيان آهي. آخرڪار، ايتري تائين توهان پوشيده رهيا آهيو؛ ريڊار جي هيٺان اڏامندي. توهان کي صرف سڄي وقت هڪ سٺو VPN استعمال ڪرڻو هو، ڪنهن به فارم ۾ پنهنجا ذاتي تفصيل نه ڀرڻ (ظاهري ڳالهه)، ۽ شايد هڪ خاص برائوزر سيشن استعمال ڪرڻ (يا اڃا تائين هڪ مختلف ڪمپيوٽر). هاڻي توهان کي ڊيٽا کي تقسيم ڪرڻو آهي. اسان جي صورت ۾ اسان پهرين ڪتابن کي واپس Library Genesis ۾ حصو ڏيڻ چاهيو، پر پوءِ جلدي ان ۾ مشڪلاتون دريافت ڪيون (افسانه بمقابلہ غير افسانه ترتيب ڏيڻ). تنهن ڪري اسان Library Genesis-طرز ٽورينٽس استعمال ڪندي تقسيم ڪرڻ جو فيصلو ڪيو. جيڪڏهن توهان وٽ موجوده منصوبي ۾ حصو ڏيڻ جو موقعو آهي، ته پوءِ اهو توهان کي گهڻو وقت بچائي سگهي ٿو. بهرحال، اتي في الحال تمام گهڻا منظم قزاقي آئيني موجود نه آهن. ته پوءِ چئو ته توهان پاڻ ٽورينٽس تقسيم ڪرڻ جو فيصلو ڪيو. انهن فائلن کي ننڍو رکڻ جي ڪوشش ڪريو، ته جيئن اهي ٻين ويب سائيٽن تي آئيني ڪرڻ ۾ آسان هجن. پوءِ توهان کي پاڻ ٽورينٽس کي سيڊ ڪرڻو پوندو، جڏهن ته اڃا تائين گمنام رهڻ. توهان هڪ VPN استعمال ڪري سگهو ٿا (پورٽ فارورڊنگ سان يا بغير)، يا هڪ Seedbox لاءِ تمل ٿيل Bitcoins سان ادا ڪريو. جيڪڏهن توهان کي انهن مان ڪجهه اصطلاحات جو مطلب نه ٿو اچي، ته پوءِ توهان کي پڙهڻ لاءِ گهڻو ڪجهه ڪرڻو پوندو، ڇاڪاڻ ته اهو ضروري آهي ته توهان هتي خطري جي سوديبازي کي سمجهو. توهان موجوده ٽورينٽ ويب سائيٽن تي ٽورينٽ فائلون پاڻ ميزباني ڪري سگهو ٿا. اسان جي صورت ۾، اسان اصل ۾ هڪ ويب سائيٽ ميزباني ڪرڻ جو انتخاب ڪيو، ڇاڪاڻ ته اسان پڻ پنهنجي فلسفي کي واضح طريقي سان ڦهلائڻ چاهيو. توهان اهو پاڻ ساڳئي طريقي سان ڪري سگهو ٿا (اسان پنهنجي ڊومينز ۽ ميزباني لاءِ Njalla استعمال ڪندا آهيون، تمل ٿيل Bitcoins سان ادا ڪيل)، پر اسان سان رابطو ڪرڻ ۾ پڻ آزاد محسوس ڪريو ته اسان توهان جي ٽورينٽس کي ميزباني ڪريون. اسان وقت سان گڏ قزاقي آئيني جو هڪ جامع انڊيڪس ٺاهڻ جي ڪوشش ڪري رهيا آهيون، جيڪڏهن هي خيال مقبول ٿئي. جيئن ته VPN جي چونڊ لاءِ، هن بابت اڳ ۾ ئي گهڻو ڪجهه لکيو ويو آهي، تنهن ڪري اسان صرف شهرت جي بنياد تي چونڊڻ جي عام صلاح کي ورجائينداسين. اصل عدالت-آزمائشي ڪو-لاگ پاليسيون جن جي ڊگهي ٽريڪ رڪارڊ سان رازداري جي حفاظت ڪرڻ جو سڀ کان گهٽ خطري وارو اختيار آهي، اسان جي راءِ ۾. نوٽ ڪريو ته جيتوڻيڪ توهان سڀ ڪجهه صحيح ڪريو، توهان ڪڏهن به صفر خطري تائين نه پهچي سگهو ٿا. مثال طور، جڏهن توهان جي ٽورينٽس کي سيڊ ڪندي، هڪ انتهائي متحرڪ قوم-رياست جو عملدار شايد VPN سرورز لاءِ ايندڙ ۽ ٻاهرين ڊيٽا فلو ڏسي سگهي ٿو، ۽ اندازو لڳائي سگهي ٿو ته توهان ڪير آهيو. يا توهان بس ڪنهن طريقي سان گڙٻڙ ڪري سگهو ٿا. اسان شايد اڳ ۾ ئي ڪري چڪا آهيون، ۽ ٻيهر ڪنداسين. خوش قسمتي سان، قومون قزاقي بابت ايترو <em>پريشان</em> نه آهن. هر منصوبي لاءِ هڪ فيصلو ڪرڻو آهي، ڇا ان کي اڳئين سڃاڻپ سان شايع ڪرڻ يا نه. جيڪڏهن توهان ساڳيو نالو استعمال ڪندا رهو، ته پوءِ اڳين منصوبن مان آپريشنل سيڪيورٽي ۾ غلطيون توهان کي نقصان پهچائي سگهن ٿيون. پر مختلف نالن سان شايع ڪرڻ جو مطلب آهي ته توهان هڪ ڊگهي عرصي تائين شهرت نه ٺاهي سگهو ٿا. اسان شروع کان مضبوط آپريشنل سيڪيورٽي رکڻ جو انتخاب ڪيو ته جيئن اسان ساڳيو سڃاڻپ استعمال ڪري سگهون، پر اسان هچڪچائڻ کان سواءِ مختلف نالي سان شايع ڪنداسين جيڪڏهن اسان گڙٻڙ ڪريون يا جيڪڏهن حالتون ان کي گهرجن. لفظ کي ٻاهر نڪرڻ مشڪل ٿي سگهي ٿو. جيئن اسان چيو، هي اڃا تائين هڪ خاص ڪميونٽي آهي. اسان اصل ۾ Reddit تي پوسٽ ڪيو، پر حقيقت ۾ Hacker News تي ڪشش حاصل ڪئي. في الحال اسان جي سفارش آهي ته ان کي ڪجهه هنڌن تي پوسٽ ڪريو ۽ ڏسو ته ڇا ٿئي ٿو. ۽ ٻيهر، اسان سان رابطو ڪريو. اسان وڌيڪ قزاقي آرڪائيوزم جي ڪوششن جي لفظ کي ڦهلائڻ پسند ڪنداسين. 1. ڊومين چونڊ / فلسفو اها ڄاڻ ۽ ثقافتي ورثي جي ڪا به کوٽ ناهي جيڪا بچائڻ لاءِ، جيڪا زبردست ٿي سگهي ٿي. اهو ئي سبب آهي ته اڪثر ڪري هڪ لمحو وٺڻ ۽ سوچڻ لاءِ مفيد آهي ته توهان جو تعاون ڇا ٿي سگهي ٿو. هر ڪنهن وٽ هن بابت سوچڻ جو مختلف طريقو آهي، پر هتي ڪجهه سوال آهن جيڪي توهان پاڻ کان پڇي سگهو ٿا: اسان جي صورت ۾، اسان خاص طور تي سائنس جي ڊگهي مدت جي حفاظت بابت پرواهه ڪندا هئاسين. اسان کي لائبريري جينيسس بابت خبر هئي، ۽ اهو ڪيئن مڪمل طور تي ڪيترائي ڀيرا ٽورينٽس استعمال ڪندي آئيني بڻايو ويو. اسان کي اهو خيال پسند آيو. پوءِ هڪ ڏينهن، اسان مان هڪ لائبريري جينيسس تي ڪجهه سائنسي درسي ڪتاب ڳولڻ جي ڪوشش ڪئي، پر انهن کي نه مليو، جنهن کي شڪ ۾ آندو ته اهو واقعي ڪيترو مڪمل هو. پوءِ اسان انهن درسي ڪتابن کي آن لائن ڳوليو، ۽ انهن کي ٻين هنڌن تي مليو، جنهن اسان جي پروجيڪٽ لاءِ ٻج پوکيو. جيتوڻيڪ اسان کي Z-Library بابت خبر نه هئي، اسان وٽ اهو خيال هو ته انهن سڀني ڪتابن کي دستي طور تي گڏ ڪرڻ جي ڪوشش نه ڪئي وڃي، پر موجوده مجموعن کي آئيني بڻائڻ تي ڌيان ڏيڻ، ۽ انهن کي لائبريري جينيسس ڏانهن واپس ڏيڻ. توهان وٽ ڪهڙيون مهارتون آهن جيڪي توهان پنهنجي فائدي لاءِ استعمال ڪري سگهو ٿا؟ مثال طور، جيڪڏهن توهان هڪ آن لائن سيڪيورٽي ماهر آهيو، ته توهان محفوظ هدفن لاءِ IP بلاڪ کي شڪست ڏيڻ جا طريقا ڳولي سگهو ٿا. جيڪڏهن توهان ڪميونٽيز کي منظم ڪرڻ ۾ بهترين آهيو، ته شايد توهان ڪجهه ماڻهن کي گڏ ڪري سگهو ٿا هڪ مقصد جي چوڌاري. ڪجهه پروگرامنگ ڄاڻڻ مفيد آهي، جيتوڻيڪ، جيڪڏهن صرف هن عمل دوران سٺي آپريشنل سيڪيورٽي رکڻ لاءِ. هڪ اعليٰ ليوريج وارو علائقو ڪهڙو هوندو جنهن تي ڌيان ڏيڻ؟ جيڪڏهن توهان X ڪلاڪ پائرٽ آرڪائيو تي خرچ ڪرڻ وارا آهيو، ته پوءِ توهان ڪيئن حاصل ڪري سگهو ٿا "توهان جي بڪ لاءِ سڀ کان وڏو ڌماڪو"؟ توهان هن بابت سوچڻ جا منفرد طريقا ڪهڙا آهن؟ توهان وٽ ڪجهه دلچسپ خيال يا طريقا ٿي سگهن ٿا جيڪي ٻيا شايد وڃائي چڪا هجن. توهان وٽ هن لاءِ ڪيترو وقت آهي؟ اسان جي صلاح اها هوندي ته ننڍي شروعات ڪريو ۽ وڏا پروجيڪٽ ڪرڻ جيئن توهان ان کي حاصل ڪريو، پر اهو سڀ ڪجهه استعمال ڪري سگهي ٿو. توهان هن ۾ ڇو دلچسپي رکو ٿا؟ توهان کي ڪهڙي شيءِ جو شوق آهي؟ جيڪڏهن اسان ماڻهن جو هڪ گروپ حاصل ڪري سگهون ٿا جيڪي سڀئي انهن شين کي آرڪائيو ڪن ٿا جن جي انهن کي خاص طور تي پرواهه آهي، ته اهو گهڻو ڪجهه ڍڪي وٺندو! توهان پنهنجي جذبي بابت اوسط شخص کان گهڻو ڪجهه ڄاڻيندا، جهڙوڪ ڪهڙو اهم ڊيٽا بچائڻ لاءِ، ڪهڙا بهترين مجموعا ۽ آن لائن ڪميونٽيز آهن، ۽ پوء تي. 3. Metadata اسڪراپنگ تاريخ شامل/ترميم ٿيل: ته جيئن توهان بعد ۾ واپس اچي سگهو ۽ فائلون ڊائون لوڊ ڪري سگهو جيڪي توهان اڳ ۾ ڊائون لوڊ نه ڪيون (جيتوڻيڪ توهان اڪثر ڪري ID يا هاش کي پڻ استعمال ڪري سگهو ٿا). هاش (md5، sha1): تصديق ڪرڻ لاءِ ته توهان فائل صحيح طريقي سان ڊائون لوڊ ڪئي. ID: ڪجهه اندروني ID ٿي سگهي ٿو، پر IDs جهڙوڪ ISBN يا DOI پڻ مفيد آهن. فائل جو نالو / مقام وضاحت، درجو، ٽيگ، مصنف، ٻولي، وغيره. ماپ: حساب ڪرڻ لاءِ ته توهان کي ڪيتري ڊسڪ اسپيس جي ضرورت آهي. هتي ٿورو وڌيڪ ٽيڪنيڪل ٿي وڃون. ويب سائيٽن مان اصل ۾ metadata اسڪراپ ڪرڻ لاءِ، اسان شيون ڪافي ساديون رکيون آهن. اسان Python اسڪرپٽ استعمال ڪندا آهيون، ڪڏهن ڪڏهن curl، ۽ هڪ MySQL ڊيٽابيس نتيجن کي ذخيرو ڪرڻ لاءِ. اسان ڪنهن به شاندار اسڪراپنگ سافٽ ويئر استعمال نه ڪيو آهي جيڪو پيچيده ويب سائيٽن کي نقشي ۾ آڻي سگهي، ڇاڪاڻ ته اڃا تائين اسان کي صرف هڪ يا ٻه قسم جا صفحا اسڪراپ ڪرڻ جي ضرورت هئي صرف IDs ذريعي ڳڻپ ڪندي ۽ HTML کي پارس ڪندي. جيڪڏهن آساني سان ڳڻپيل صفحا نه آهن، ته پوءِ توهان کي هڪ مناسب ڪرالر جي ضرورت پوندي جيڪا سڀني صفحن کي ڳولڻ جي ڪوشش ڪري. پوري ويب سائيٽ کي اسڪراپ ڪرڻ شروع ڪرڻ کان اڳ، ٿوري دير لاءِ ان کي دستي طور تي ڪرڻ جي ڪوشش ڪريو. پاڻ ڪجهه درجن صفحا ڏسو، ته جيئن توهان کي اهو سمجهڻ ۾ اچي ته اهو ڪيئن ڪم ڪري ٿو. ڪڏهن ڪڏهن توهان اڳ ۾ ئي IP بلاڪ يا ٻين دلچسپ روين ۾ اچي ويندا آهيو. ڊيٽا اسڪراپنگ لاءِ به ساڳيو آهي: هن هدف ۾ گهڻو گهرو ٿيڻ کان اڳ، پڪ ڪريو ته توهان اصل ۾ ان جي ڊيٽا کي مؤثر طريقي سان ڊائون لوڊ ڪري سگهو ٿا. پابندين کي نظرانداز ڪرڻ لاءِ، ڪجهه شيون آهن جيڪي توهان ڪوشش ڪري سگهو ٿا. ڇا ڪا ٻي IP پتي يا سرور آهن جيڪي ساڳي ڊيٽا کي ميزباني ڪن ٿا پر ساڳيون پابنديون نه آهن؟ ڇا ڪو API اينڊ پوائنٽس آهن جن تي پابنديون نه آهن، جڏهن ته ٻين تي آهن؟ ڪهڙي شرح تي ڊائون لوڊ ڪرڻ تي توهان جي IP بلاڪ ٿي وڃي ٿي، ۽ ڪيتري دير تائين؟ يا توهان بلاڪ نه ٿيو پر گهٽجي ويو؟ جيڪڏهن توهان هڪ صارف اڪائونٽ ٺاهيو ٿا، ته پوءِ شيون ڪيئن تبديل ٿين ٿيون؟ ڇا توهان HTTP/2 استعمال ڪري سگهو ٿا ته ڪنيڪشن کي کليل رکڻ لاءِ، ۽ ڇا اهو ان شرح کي وڌائي ٿو جنهن تي توهان صفحا درخواست ڪري سگهو ٿا؟ ڇا اهڙا صفحا آهن جيڪي هڪ ئي وقت ۾ ڪيترن ئي فائلن کي لسٽ ڪن ٿا، ۽ ڇا اتي درج ٿيل معلومات ڪافي آهي؟ شيون جيڪي توهان شايد محفوظ ڪرڻ چاهيو ٿا شامل آهن: اسان عام طور تي اهو ٻه مرحلن ۾ ڪندا آهيون. پهرين اسان خام HTML فائلون ڊائون لوڊ ڪندا آهيون، عام طور تي سڌو سنئون MySQL ۾ (گهڻيون ننڍيون فائلون کان بچڻ لاءِ، جنهن بابت اسان هيٺ وڌيڪ ڳالهائينداسين). پوءِ، هڪ الڳ مرحلي ۾، اسان انهن HTML فائلن کي ڏسون ٿا ۽ انهن کي اصل MySQL ٽيبلز ۾ پارس ڪندا آهيون. هن طريقي سان توهان کي هر شيءِ کي ٻيهر ڊائون لوڊ ڪرڻ جي ضرورت نه آهي جيڪڏهن توهان پنهنجي پارسنگ ڪوڊ ۾ ڪا غلطي ڳوليندا آهيو، ڇاڪاڻ ته توهان صرف نئين ڪوڊ سان HTML فائلن کي ٻيهر پروسيس ڪري سگهو ٿا. اهو پڻ اڪثر ڪري پروسيسنگ مرحلي کي متوازي ڪرڻ آسان آهي، اهڙيءَ طرح ڪجهه وقت بچائيندي (۽ توهان اسڪراپنگ هلندي پروسيسنگ ڪوڊ لکي سگهو ٿا، ان جي بدران ٻنهي مرحلن کي هڪ ئي وقت لکڻ جي ضرورت آهي). آخر ۾، نوٽ ڪريو ته ڪجهه هدفن لاءِ metadata scraping ئي سڀ ڪجهه آهي. اتي ڪجهه وڏيون metadata مجموعا موجود آهن جيڪي صحيح طريقي سان محفوظ نه آهن. عنوان ڊومين چونڊ / فلسفو: توهان ڪٿي تقريباً ڌيان ڏيڻ چاهيو ٿا، ۽ ڇو؟ توهان جا منفرد جذبا، مهارتون، ۽ حالتون ڪهڙيون آهن جيڪي توهان پنهنجي فائدي لاءِ استعمال ڪري سگهو ٿا؟ هدف چونڊ: ڪهڙي خاص مجموعي کي توهان آئيني بڻائيندا؟ ميٽا ڊيٽا اسڪراپنگ: فائلن بابت معلومات کي ڪيٽلاگ ڪرڻ، بغير اصل ۾ (گهڻو وڏيون) فائلون ڊائونلوڊ ڪرڻ. ڊيٽا چونڊ: ميٽا ڊيٽا جي بنياد تي، اهو تنگ ڪرڻ ته ڪهڙو ڊيٽا هن وقت آرڪائيو ڪرڻ لاءِ سڀ کان وڌيڪ لاڳاپيل آهي. اهو سڀ ڪجهه ٿي سگهي ٿو، پر اڪثر ڪري جاءِ ۽ بينڊوڊٿ بچائڻ جو هڪ مناسب طريقو آهي. ڊيٽا اسڪراپنگ: اصل ۾ ڊيٽا حاصل ڪرڻ. ورڇ: ان کي ٽورينٽس ۾ پيڪيج ڪرڻ، ان کي ڪٿي اعلان ڪرڻ، ماڻهن کي ان کي ڦهلائڻ لاءِ حاصل ڪرڻ. 5. ڊيٽا اسڪراپنگ هاڻي توهان اصل ۾ ڊيٽا کي وڏي پيماني تي ڊائونلوڊ ڪرڻ لاءِ تيار آهيو. جيئن اڳ ذڪر ڪيو ويو آهي، هن نقطي تي توهان کي اڳ ۾ ئي دستي طور تي ڪيترائي فائلون ڊائونلوڊ ڪيون ويون آهن، هدف جي رويي ۽ پابندين کي بهتر سمجهڻ لاءِ. بهرحال، اتي اڃا تائين توهان لاءِ حيران ڪندڙ شيون هونديون جڏهن توهان اصل ۾ هڪ ئي وقت ۾ ڪيترائي فائلون ڊائونلوڊ ڪرڻ شروع ڪندا. اسان جي صلاح هتي بنيادي طور تي ان کي سادو رکڻ آهي. بس ڪيترائي فائلون ڊائونلوڊ ڪرڻ سان شروع ڪريو. توهان Python استعمال ڪري سگهو ٿا، ۽ پوءِ ڪيترن ئي ٿريڊز تائين وڌايو. پر ڪڏهن ڪڏهن اڃا به سادو آهي ته سڌو سنئون ڊيٽابيس مان Bash فائلون پيدا ڪريو، ۽ پوءِ انهن مان ڪيترن کي ڪيترن ئي ٽرمينل ونڊوز ۾ هلائي اسڪيل اپ ڪريو. هتي هڪ جلدي ٽيڪنيڪل ٽرڪ جو ذڪر ڪرڻ لائق آهي MySQL ۾ OUTFILE استعمال ڪرڻ، جيڪو توهان ڪٿي به لکي سگهو ٿا جيڪڏهن توهان mysqld.cnf ۾ "secure_file_priv" کي غير فعال ڪريو (۽ پڪ ڪريو ته AppArmor کي به غير فعال/اوور رائيڊ ڪريو جيڪڏهن توهان Linux تي آهيو). اسان ڊيٽا کي سادي هارڊ ڊسڪ تي ذخيرو ڪندا آهيون. جيڪو توهان وٽ آهي ان سان شروع ڪريو، ۽ آهستي آهستي وڌايو. اهو سوين TBs جي ڊيٽا کي ذخيرو ڪرڻ بابت سوچڻ لاءِ زبردست ٿي سگهي ٿو. جيڪڏهن اهو صورتحال آهي جنهن کي توهان منهن ڏئي رهيا آهيو، بس پهرين هڪ سٺو ذيلي سيٽ رکجو، ۽ پنهنجي اعلان ۾ باقي ذخيرو ڪرڻ ۾ مدد لاءِ پڇو. جيڪڏهن توهان پاڻ وڌيڪ هارڊ ڊرائيو حاصل ڪرڻ چاهيو ٿا، ته پوءِ r/DataHoarder وٽ ڪجهه سٺا وسيلا آهن سٺا سودو حاصل ڪرڻ لاءِ. ڪوشش ڪريو ته فانسيسي فائل سسٽم بابت گهڻو پريشان نه ٿيو. اهو ZFS جهڙيون شيون سيٽ اپ ڪرڻ جي خرگوش جي سوراخ ۾ وڃڻ آسان آهي. بهرحال، هڪ ٽيڪنيڪل تفصيل جنهن کان آگاهه ٿيڻ گهرجي، اهو آهي ته ڪيترائي فائل سسٽم ڪيترن ئي فائلن سان سٺو نه ٿا ڪن. اسان اهو معلوم ڪيو آهي ته هڪ سادي حل ڪيترن ئي ڊائريڪٽريز ٺاهڻ آهي، مثال طور مختلف ID رينجز يا هاش پري فيڪس لاءِ. ڊيٽا ڊائونلوڊ ڪرڻ کان پوءِ، فائلن جي سالميت کي هاشز استعمال ڪندي چيڪ ڪرڻ پڪ ڪريو، جيڪڏهن موجود هجي. 2. هدف چونڊ پهچڻ لائق: توهان جي metadata ۽ ڊيٽا کي اسڪراپ ڪرڻ کان روڪڻ لاءِ ڪيترن ئي تحفظ جي تہن جو استعمال نه ٿو ڪري. خاص بصيرت: توهان وٽ هن هدف بابت ڪجهه خاص معلومات آهي، جهڙوڪ توهان وٽ ڪنهن طرح سان هن مجموعي تائين خاص رسائي آهي، يا توهان انهن جي دفاع کي شڪست ڏيڻ جو طريقو ڳولي ورتو آهي. اهو ضروري ناهي (اسان جو ايندڙ منصوبو ڪجهه خاص نه ٿو ڪري)، پر اهو يقيناً مدد ڪري ٿو! وڏي تنهنڪري، اسان وٽ اسان جو علائقو آهي جنهن کي اسان ڏسي رهيا آهيون، هاڻي ڪهڙي خاص مجموعي کي اسان نقل ڪريون؟ هتي ڪجهه شيون آهن جيڪي هڪ سٺي هدف لاءِ ٺاهين ٿيون: جڏهن اسان پنهنجا سائنس جا درسي ڪتاب لائبريري جينيسس کان سواءِ ٻين ويب سائيٽن تي مليا، ته اسان ڪوشش ڪئي ته اهو معلوم ڪريون ته اهي انٽرنيٽ تي ڪيئن پهتا. پوءِ اسان Z-Library کي ڳوليو، ۽ محسوس ڪيو ته جڏهن ته اڪثر ڪتاب پهريون ڀيرو اتي ظاهر نه ٿيندا آهن، اهي آخرڪار اتي پهچي ويندا آهن. اسان ان جي لائبريري جينيسس سان تعلق، ۽ (مالي) ترغيب جي جوڙجڪ ۽ بهتر صارف انٽرفيس بابت سکيو، جيڪي ٻنهي ان کي هڪ وڌيڪ مڪمل مجموعو بڻايو. پوءِ اسان ڪجهه ابتدائي metadata ۽ ڊيٽا اسڪراپنگ ڪئي، ۽ محسوس ڪيو ته اسان انهن جي IP ڊائون لوڊ حدن کي نظرانداز ڪري سگهون ٿا، اسان جي ميمبرن مان هڪ جي خاص رسائي کي ڪيترن ئي پراڪسي سرورز تائين استعمال ڪندي. جيئن توهان مختلف هدفن کي ڳولي رهيا آهيو، اهو اڳ ۾ ئي ضروري آهي ته توهان جي ٽريڪ کي لڪائڻ لاءِ VPNs ۽ عارضي اي ميل پتي استعمال ڪندي، جنهن بابت اسان بعد ۾ وڌيڪ ڳالهائينداسين. منفرد: اڳ ۾ ئي ٻين منصوبن طرفان چڱيءَ طرح ڍڪيل نه آهي. جڏهن اسان هڪ پروجيڪٽ ڪندا آهيون، ان ۾ ڪجهه مرحلا هوندا آهن: اهي مڪمل طور تي آزاد مرحلا نه آهن، ۽ اڪثر ڪري هڪ بعد واري مرحلي مان بصيرت توهان کي هڪ اڳئين مرحلي ڏانهن موڪليندي آهي. مثال طور، ميٽا ڊيٽا اسڪراپنگ دوران توهان محسوس ڪري سگهو ٿا ته توهان جي چونڊيل هدف ۾ دفاعي ميڪانيزم آهن جيڪي توهان جي مهارت جي سطح کان ٻاهر آهن (جهڙوڪ IP بلاڪ)، تنهنڪري توهان واپس وڃو ۽ هڪ مختلف هدف ڳوليو. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>) پوري ڪتابن کي ڊجيٽل تحفظ جي عام طور تي، ۽ خاص طور تي قزاق آرڪائيوزم جي <em>ڇو</em> بابت لکيو وڃي ٿو، پر اچو ته انهن لاءِ هڪ جلدي پرائمر ڏيون جيڪي تمام گهڻو واقف نه آهن. دنيا اڳ کان وڌيڪ علم ۽ ثقافت پيدا ڪري رهي آهي، پر ان مان وڌيڪ به وڃائي رهي آهي. انسانيت وڏي حد تائين اڪيڊمڪ پبلشرز، اسٽريمنگ سروسز، ۽ سوشل ميڊيا ڪمپنين جهڙين ڪارپوريشنن تي هن ورثي سان ڀروسو ڪري ٿي، ۽ انهن اڪثر ڪري وڏا سرپرست ثابت نه ٿيا آهن. ڊاڪيومينٽري ڊجيٽل ايمينشيا، يا واقعي ڪنهن به ڳالهه کي چيڪ ڪريو جيسن اسڪاٽ طرفان. ڪجهه ادارا آهن جيڪي جيترو ٿي سگهي آرڪائيو ڪرڻ ۾ سٺو ڪم ڪن ٿا، پر اهي قانون سان پابند آهن. قزاقن جي حيثيت ۾، اسان هڪ منفرد پوزيشن ۾ آهيون ته اهڙن مجموعن کي آرڪائيو ڪري سگهون ٿا جن کي اهي ڇهي نٿا سگهن، ڪاپي رائيٽ جي نفاذ يا ٻين پابندين جي ڪري. اسان پڻ دنيا جي ڪيترن ئي ڀيرا مجموعن کي آئيني ڪري سگهون ٿا، ان ڪري صحيح تحفظ جا امڪان وڌي وڃن ٿا. هاڻي لاءِ، اسان دانشورانه ملڪيت جي فائدن ۽ نقصانن بابت بحث ۾ نه وينداسين، قانون ٽوڙڻ جي اخلاقيات، سينسرشپ تي سوچ، يا علم ۽ ثقافت تائين رسائي جو مسئلو. ان سڀني کي پاسي تي رکندي، اچو ته <em>ڪيئن</em> ۾ وڃون. اسان شيئر ڪنداسين ته اسان جي ٽيم ڪيئن قزاق آرڪائيوسٽ بڻجي وئي، ۽ انهن سبقن کي جيڪي اسان رستي ۾ سکيا. جڏهن توهان هن سفر تي نڪرو ٿا ته ڪيترائي چئلينج آهن، ۽ اميد آهي ته اسان توهان کي انهن مان ڪجهه ذريعي مدد ڪري سگهون ٿا. ڪيئن هڪ قزاق آرڪائيوسٽ بڻجي پهريون چئلينج شايد هڪ حيرت انگيز هجي. اهو نه ته هڪ ٽيڪنيڪل مسئلو آهي، ۽ نه ئي هڪ قانوني مسئلو. اهو هڪ نفسياتي مسئلو آهي. اسان جي اندر وڃڻ کان اڳ، Pirate Library Mirror تي ٻه تازه ڪاريون (ترميم: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a>): اسان کي ڪجهه انتهائي فراخدلانه عطيا مليا. پهريون $10k هڪ گمنام فرد کان هو جيڪو پڻ "بڪ وارير" جي مدد ڪري رهيو آهي، لائبريري جينيسس جو اصل باني. هن عطيا کي آسان بڻائڻ لاءِ بڪ وارير جو خاص شڪريو. ٻيو هڪ ٻيو $10k هڪ گمنام ڊونر کان هو، جيڪو اسان جي آخري رليز کان پوءِ رابطي ۾ آيو، ۽ مدد لاءِ متاثر ٿيو. اسان وٽ ڪجهه ننڍا عطيا پڻ هئا. توهان جي سڀني فراخدلانه مدد لاءِ تمام گهڻو مهرباني. اسان وٽ ڪجهه دلچسپ نوان منصوبا آهن جيڪي هي مدد ڪندا، تنهنڪري تيار رهو. اسان جي ٻئي رليز جي سائيز سان ڪجهه ٽيڪنيڪل مشڪلاتون هيون، پر اسان جا ٽورنٽ هاڻي اپ ۽ سيڊنگ آهن. اسان کي هڪ گمنام فرد کان هڪ فراخدلانه آڇ پڻ ملي ته اسان جي مجموعي کي انهن جي تمام تيز رفتار سرورز تي سيڊ ڪيو وڃي، تنهنڪري اسان انهن جي مشينن تي هڪ خاص اپلوڊ ڪري رهيا آهيون، جنهن کان پوءِ هرڪو جيڪو مجموعو ڊائونلوڊ ڪري رهيو آهي ان کي رفتار ۾ وڏي بهتري نظر ايندي. بلاگ پوسٽون هاءِ، مان آنا آهيان. مون <a %(wikipedia_annas_archive)s>آنا جو آرڪائيو</a> ٺاهيو، دنيا جي سڀ کان وڏي شيڊو لائبريري. هي منهنجو ذاتي بلاگ آهي، جنهن ۾ مان ۽ منهنجا ساٿي قزاقي، ڊجيٽل تحفظ، ۽ وڌيڪ بابت لکن ٿا. مون سان <a %(reddit)s>Reddit</a> تي ڳنڍيو. نوٽ ڪريو ته هي ويب سائيٽ صرف هڪ بلاگ آهي. اسان هتي صرف پنهنجا لفظ ميزباني ڪندا آهيون. هتي ڪوبه ٽورينٽ يا ٻيا ڪاپي رائيٽ ٿيل فائلون ميزباني يا ڳنڍيل نه آهن. <strong>لائبريري</strong> - گهڻين لائبريرين وانگر، اسان جو مرڪز بنيادي طور تي لکيل مواد جهڙوڪ ڪتابن تي آهي. اسان شايد مستقبل ۾ ٻين قسم جي ميڊيا ۾ به واڌارو ڪريون. <strong>مرر</strong> - اسان سختي سان موجوده لائبريرين جو مرر آهيون. اسان جو مرڪز تحفظ تي آهي، ڪتابن کي آساني سان ڳولڻ ۽ ڊائونلوڊ ڪرڻ (رسائي) يا نون ڪتابن جي تعاون ڪندڙ ماڻهن جي وڏي ڪميونٽي کي فروغ ڏيڻ (ذريعو) تي نه آهي. <strong>قزاق</strong> - اسان گهڻن ملڪن ۾ جان بوجھ ڪري ڪاپي رائيٽ قانون جي ڀڃڪڙي ڪندا آهيون. اهو اسان کي ڪجهه ڪرڻ جي اجازت ڏئي ٿو جيڪو قانوني ادارا نٿا ڪري سگهن: يقيني بڻائڻ ته ڪتاب هر هنڌ پهچن. <em>اسان هن بلاگ مان فائلن کي لنڪ نه ٿا ڪريون. مهرباني ڪري پاڻ ڳوليو.</em> - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>) هي منصوبو (ترميم: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a>) انسان جي علم جي تحفظ ۽ آزادي ۾ حصو وٺڻ جو مقصد رکي ٿو. اسان پنهنجو ننڍڙو ۽ عاجزانه حصو پيش ڪريون ٿا، اسان کان اڳ جي عظيم ماڻهن جي نقش قدم تي. هن منصوبي جو مرڪز ان جي نالي سان واضح آهي: پهرين لائبريري جيڪا اسان مرر ڪئي آهي اها Z-Library آهي. هي هڪ مشهور (۽ غير قانوني) لائبريري آهي. انهن لائبريري جينيسس جي مجموعي کي آساني سان ڳولڻ لائق بڻايو آهي. ان کان علاوه، انهن نون ڪتابن جي تعاون کي ترغيب ڏيڻ ۾ تمام اثرائتو بڻجي ويا آهن، تعاون ڪندڙ استعمال ڪندڙن کي مختلف فائدن سان ترغيب ڏئي. اهي في الحال اهي نوان ڪتاب لائبريري جينيسس ڏانهن واپس نه ٿا ڏين. ۽ لائبريري جينيسس جي برعڪس، اهي پنهنجو مجموعو آساني سان مرر ڪرڻ لائق نه ٿا بڻائين، جيڪو وسيع تحفظ کي روڪي ٿو. اهو انهن جي ڪاروباري ماڊل لاءِ اهم آهي، ڇاڪاڻ ته اهي پنهنجي مجموعي تائين رسائي لاءِ وڏي رقم چارج ڪن ٿا (هڪ ڏينهن ۾ 10 کان وڌيڪ ڪتاب). اسان غير قانوني ڪتابن جي مجموعي تائين وڏي پيماني تي رسائي لاءِ پئسا چارج ڪرڻ بابت اخلاقي فيصلو نه ٿا ڪريون. اهو بلاشبہ آهي ته Z-Library علم تائين رسائي کي وڌائڻ ۽ وڌيڪ ڪتابن کي حاصل ڪرڻ ۾ ڪامياب ٿي چڪو آهي. اسان هتي صرف پنهنجو حصو ادا ڪرڻ لاءِ آهيون: هن نجي مجموعي جي ڊگهي مدي واري حفاظت کي يقيني بڻائڻ. اسان توهان کي دعوت ڏيڻ چاهيون ٿا ته انساني علم کي محفوظ ۽ آزاد ڪرڻ ۾ مدد ڪريو اسان جي ٽورنٽس کي ڊائونلوڊ ۽ سيڊنگ ڪندي. ڊيٽا ڪيئن منظم ٿيل آهي ان بابت وڌيڪ معلومات لاءِ منصوبي جي صفحي ڏسو. اسان توهان کي پڻ دعوت ڏيڻ چاهيون ٿا ته توهان پنهنجا خيال پيش ڪريو ته ڪهڙيون مجموعا مرر ڪيون وڃن، ۽ ان کي ڪيئن ڪجي. گڏجي اسان گهڻو ڪجهه حاصل ڪري سگهون ٿا. هي بيشمار ٻين ۾ هڪ ننڍڙو حصو آهي. توهان جي سڀني ڪوششن لاءِ مهرباني. قزاق لائبريري مرر جو تعارف: 7TB ڪتابن جو تحفظ (جيڪي Libgen ۾ نه آهن) 10% oانسانيت جي لکيل ورثي کي هميشه لاءِ محفوظ ڪيو ويو <strong>گوگل.</strong> آخرڪار، انهن گوگل بڪس لاءِ هي تحقيق ڪئي. بهرحال، انهن جو metadata وڏي مقدار ۾ رسائي لائق نه آهي ۽ ان کي اسڪراپ ڪرڻ ڏکيو آهي. <strong>مختلف انفرادي لائبريري سسٽم ۽ آرڪائيوز.</strong> اهڙيون لائبريريون ۽ آرڪائيوز آهن جيڪي مٿي ذڪر ڪيل ڪنهن به طرفان انڊيڪس ۽ گڏ نه ڪيا ويا آهن، اڪثر ڪري ڇاڪاڻ ته اهي گهٽ فنڊ ٿيل آهن، يا ٻين سببن جي ڪري اهي پنهنجو ڊيٽا اوپن لائبريري، OCLC، گوگل، وغيره سان شيئر ڪرڻ نٿا چاهين. انهن مان ڪيترن وٽ ڊجيٽل ريڪارڊ آهن جيڪي انٽرنيٽ ذريعي رسائي لائق آهن، ۽ اهي اڪثر ڪري تمام سٺي طرح سان محفوظ نه آهن، تنهنڪري جيڪڏهن توهان مدد ڪرڻ چاهيو ٿا ۽ عجيب لائبريري سسٽم بابت سکڻ ۾ مزو وٺڻ چاهيو ٿا، ته اهي بهترين شروعاتي نقطا آهن. <strong>ISBNdb.</strong> هي هن بلاگ پوسٽ جو موضوع آهي. ISBNdb مختلف ويب سائيٽن مان ڪتابن جي metadata کي اسڪراپ ڪري ٿو، خاص طور تي قيمتن جي ڊيٽا، جنهن کي اهي پوءِ ڪتاب وڪڻندڙن کي وڪرو ڪن ٿا، ته جيئن اهي پنهنجي ڪتابن جي قيمت مارڪيٽ جي باقي حصي سان مطابقت ۾ مقرر ڪري سگهن. ڇاڪاڻ ته ISBNs هاڻي ڪافي عالمي آهن، انهن اثرائتي طور تي "هر ڪتاب لاءِ هڪ ويب صفحو" ٺاهي ڇڏيو آهي. <strong>اوپن لائبريري.</strong> جيئن اڳ ذڪر ڪيو ويو، اهو انهن جو سڄو مشن آهي. انهن تعاون ڪندڙ لائبريرين ۽ قومي آرڪائيوز مان وڏي مقدار ۾ لائبريري ڊيٽا حاصل ڪئي آهي، ۽ ائين جاري رکندا آهن. انهن وٽ رضاڪار لائبريرين ۽ هڪ ٽيڪنيڪل ٽيم پڻ آهي جيڪي ريڪارڊ کي ڊپليڪيٽ ڪرڻ جي ڪوشش ڪري رهيا آهن، ۽ انهن کي هر قسم جي metadata سان ٽيگ ڪري رهيا آهن. سڀ کان بهتر، انهن جو ڊيٽا سيٽ مڪمل طور تي کليل آهي. توهان سادي طور تي <a %(openlibrary)s>ان کي ڊائون لوڊ ڪري سگهو ٿا</a>. <strong>ورلڊ ڪيٽ.</strong> هي هڪ ويب سائيٽ آهي جيڪا غير منافع بخش OCLC پاران هلائي وڃي ٿي، جيڪا لائبريري مينيجمينٽ سسٽم وڪرو ڪري ٿي. اهي ڪيترين ئي لائبريرين مان ڪتابن جي metadata کي گڏ ڪن ٿا، ۽ ان کي ورلڊ ڪيٽ ويب سائيٽ ذريعي دستياب ڪن ٿا. بهرحال، اهي پڻ هن ڊيٽا کي وڪرو ڪندي پئسا ڪمائين ٿا، تنهنڪري اهو وڏي مقدار ۾ ڊائون لوڊ لاءِ دستياب نه آهي. انهن وٽ ڪجهه وڌيڪ محدود وڏي ڊيٽا سيٽ ڊائون لوڊ لاءِ دستياب آهن، خاص لائبريرين سان تعاون ۾. 1. "هميشه لاءِ" جي ڪجهه مناسب تعريف لاءِ. ;) 2. يقيناً، انسانيت جي لکيل ورثي ۾ ڪتابن کان گهڻو ڪجهه آهي، خاص طور تي اڄڪلهه. هن پوسٽ ۽ اسان جي تازين رليز لاءِ اسان ڪتابن تي ڌيان ڏئي رهيا آهيون، پر اسان جا مفاد وڌيڪ آهن. 3. ايرون سوارتز بابت گهڻو ڪجهه چيو وڃي ٿو، پر اسان صرف مختصر طور تي هن جو ذڪر ڪرڻ چاهيون ٿا، ڇاڪاڻ ته هو هن ڪهاڻي ۾ هڪ اهم ڪردار ادا ڪري ٿو. جيئن وقت گذري ٿو، وڌيڪ ماڻهو پهريون ڀيرو هن جو نالو ٻڌي سگهن ٿا، ۽ پوءِ پاڻ کي ان ۾ غرق ڪري سگهن ٿا. <strong>جسماني ڪاپيون.</strong> واضح طور تي اهو تمام مددگار ناهي، ڇاڪاڻ ته اهي صرف ساڳئي مواد جا نقل آهن. اهو سٺو ٿيندو جيڪڏهن اسان انهن سڀني تشريح کي محفوظ ڪري سگهون جيڪي ماڻهو ڪتابن ۾ ڪن ٿا، جهڙوڪ فيرميٽ جا مشهور "حاشين ۾ لکيل نوٽس". پر افسوس، اهو هڪ آرڪائيوسٽ جو خواب رهندو. <strong>“ايڊيشنز”.</strong> هتي توهان هر منفرد نسخي کي ڳڻيو ٿا. جيڪڏهن ان بابت ڪجهه به مختلف آهي، جهڙوڪ مختلف ڪور يا مختلف پيش لفظ، اهو هڪ مختلف ايڊيشن جي طور تي شمار ٿئي ٿو. <strong>فائلون.</strong> جڏهن لائبريري جينيسس، Sci-Hub، يا Z-Library جهڙين شيڊو لائبريرين سان ڪم ڪندي، هڪ اضافي غور آهي. هڪ ئي ايڊيشن جا ڪيترائي اسڪين ٿي سگهن ٿا. ۽ ماڻهو موجوده فائلن جا بهتر نسخا ٺاهي سگهن ٿا، ٽيڪسٽ کي OCR استعمال ڪندي اسڪين ڪندي، يا صفحن کي درست ڪندي جيڪي هڪ زاويه تي اسڪين ڪيا ويا هئا. اسان چاهيون ٿا ته انهن فائلن کي صرف هڪ ايڊيشن جي طور تي ڳڻيو وڃي، جنهن لاءِ سٺي metadata، يا دستاويزي مشابهت جي ماپن کي استعمال ڪندي deduplication جي ضرورت پوندي. <strong>“ڪم”.</strong> مثال طور "هيري پوٽر ۽ چيمبر آف سيڪريٽس" هڪ منطقي تصور جي طور تي، ان جي سڀني نسخن کي شامل ڪندي، جهڙوڪ مختلف ترجما ۽ ٻيهر ڇپائي. هي هڪ قسم جي مددگار تعريف آهي، پر اهو فيصلو ڪرڻ مشڪل ٿي سگهي ٿو ته ڇا شمار ٿئي ٿو. مثال طور، اسان شايد مختلف ترجمن کي محفوظ ڪرڻ چاهيون ٿا، جيتوڻيڪ ٻيهر ڇپائي جن ۾ صرف معمولي فرق هجي شايد ايترو اهم نه هجي. - انا ۽ ٽيم (<a %(reddit)s>Reddit</a>) قزاق لائبريري مرر سان (ترميم: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a>)، اسان جو مقصد آهي ته دنيا جي سڀني ڪتابن کي وٺي، ۽ انهن کي هميشه لاءِ محفوظ ڪريون.<sup>1</sup> اسان جي Z-Library ٽورنٽس ۽ اصل لائبريري جينيسس ٽورنٽس جي وچ ۾، اسان وٽ 11,783,153 فائلون آهن. پر اهو واقعي ڪيترو آهي؟ جيڪڏهن اسان انهن فائلن کي صحيح طريقي سان ڊپليڪيٽ ڪريون، ته دنيا جي سڀني ڪتابن جو ڪهڙو سيڪڙو اسان محفوظ ڪيو آهي؟ اسان واقعي ڪجهه اهڙو پسند ڪنداسين: اچو ته ڪجهه اڻپوري انگن سان شروع ڪريون: ٻنهي Z-Library/Libgen ۽ اوپن لائبريري ۾ ڪيترائي وڌيڪ ڪتاب آهن جيترا منفرد ISBNs. ڇا ان جو مطلب آهي ته انهن مان ڪيترن ئي ڪتابن وٽ ISBNs نه آهن، يا ڇا ISBN metadata صرف غائب آهي؟ اسان شايد هن سوال جو جواب خودڪار ميچنگ جي ميلاپ سان ڏئي سگهون ٿا ٻين خاصيتن جي بنياد تي (عنوان، ليکڪ، پبلشر، وغيره)، وڌيڪ ڊيٽا ذريعن کي شامل ڪندي، ۽ اصل ڪتابن جي اسڪين مان ISBNs ڪڍي (Z-Library/Libgen جي صورت ۾). انهن ISBNs مان ڪيترا منفرد آهن؟ هي بهترين طور تي هڪ وين ڊاگرام سان بيان ڪيو ويو آهي: وڌيڪ صحيح هجڻ لاءِ: اسان حيران ٿي ويا ته ڪيترو ٿورو اوورليپ آهي! ISBNdb وٽ وڏي تعداد ۾ ISBNs آهن جيڪي نه ته Z-Library ۾ ظاهر ٿين ٿا ۽ نه ئي Open Library ۾، ۽ ساڳيو ئي (ننڍي پر اڃا تائين اهم حد تائين) ٻين ٻن لاءِ به آهي. هي ڪيترائي نوان سوال اٿاري ٿو. ڪيترو خودڪار ميچنگ مددگار ثابت ٿيندو انهن ڪتابن کي ٽيگ ڪرڻ ۾ جيڪي ISBNs سان ٽيگ نه ڪيا ويا هئا؟ ڇا اتي گهڻا ميچ هوندا ۽ ان ڪري اوورليپ وڌي ويندو؟ پڻ، جيڪڏهن اسان هڪ چوٿون يا پنجون ڊيٽا سيٽ شامل ڪريون ته ڇا ٿيندو؟ پوءِ اسان ڪيترو اوورليپ ڏسنداسين؟ هي اسان کي هڪ شروعاتي نقطو ڏئي ٿو. اسان هاڻي انهن سڀني ISBNs کي ڏسي سگهون ٿا جيڪي Z-Library ڊيٽا سيٽ ۾ نه هئا، ۽ جيڪي عنوان/ليکڪ جي شعبن سان به نه ملن ٿا. اهو اسان کي دنيا جي سڀني ڪتابن کي محفوظ ڪرڻ ۾ مدد ڏئي سگهي ٿو: پهرين انٽرنيٽ تان اسڪينز لاءِ اسڪراپنگ ڪندي، پوءِ حقيقي زندگي ۾ وڃي ڪتابن کي اسڪين ڪندي. ٻيو به ڪراوڊ فنڊنگ ذريعي ٿي سگهي ٿو، يا ماڻهن کان "انعام" ذريعي هلائي سگهجي ٿو جيڪي خاص ڪتابن کي ڊجيٽل ڏسڻ چاهين ٿا. اهو سڀ ڪجهه هڪ مختلف وقت جي ڪهاڻي آهي. جيڪڏهن توهان هن مان ڪنهن ۾ مدد ڪرڻ چاهيو ٿا - وڌيڪ تجزيو؛ وڌيڪ metadata اسڪراپنگ؛ وڌيڪ ڪتاب ڳولڻ؛ ڪتابن جو OCR ڪرڻ؛ ٻين شعبن لاءِ اهو ڪرڻ (مثال طور پيپرز، آڊيو بڪ، فلمون، ٽي وي شو، ميگزين) يا ان مان ڪجهه ڊيٽا ML / وڏا ٻولي ماڊل ٽريننگ لاءِ دستياب ڪرڻ - مهرباني ڪري مون سان رابطو ڪريو (<a %(reddit)s>Reddit</a>). جيڪڏهن توهان خاص طور تي ڊيٽا تجزيو ۾ دلچسپي رکو ٿا، اسان پنهنجي ڊيٽا سيٽس ۽ اسڪرپٽس کي وڌيڪ آسان استعمال جي شڪل ۾ دستياب ڪرڻ تي ڪم ڪري رهيا آهيون. اهو عظيم ٿيندو جيڪڏهن توهان صرف هڪ نوٽ بڪ فورڪ ڪري سگهو ۽ ان سان کيڏڻ شروع ڪري سگهو. آخر ۾، جيڪڏهن توهان هن ڪم جي حمايت ڪرڻ چاهيو ٿا، مهرباني ڪري عطيو ڏيڻ تي غور ڪريو. هي مڪمل طور تي رضاڪارانه آپريشن آهي، ۽ توهان جي مدد وڏي فرق پيدا ڪري ٿي. هر حصو مددگار آهي. في الحال اسان ڪرپٽو ۾ عطيا وٺون ٿا؛ ڏسو عطيو صفحو Anna’s Archive تي. هڪ سيڪڙو لاءِ، اسان کي هڪ ڊينومينيٽر جي ضرورت آهي: ڪل ڪتابن جو تعداد جيڪو ڪڏهن به شايع ٿيو آهي.<sup>2</sup> گوگل بڪس جي زوال کان اڳ، منصوبي تي هڪ انجنيئر، ليونڊ ٽائچر، <a %(booksearch_blogspot)s>هن نمبر جو اندازو لڳائڻ جي ڪوشش ڪئي</a>. هن زبان ۾ چٻاڙيندي 129,864,880 ("گهٽ ۾ گهٽ آچر تائين") سان آيو. هن دنيا جي سڀني ڪتابن جو هڪ متحد ڊيٽابيس ٺاهي هن نمبر جو اندازو لڳايو. ان لاءِ، هن مختلف ڊيٽا سيٽ گڏ ڪيا ۽ پوءِ انهن کي مختلف طريقن سان ضم ڪيو. هڪ مختصر ضمني ڳالهه طور، هڪ ٻيو شخص جيڪو دنيا جي سڀني ڪتابن کي فهرست ڪرڻ جي ڪوشش ڪئي: آرون سوارتز، مرحوم ڊجيٽل سرگرم ڪار ۽ Reddit جو هم-باني. هن <a %(youtube)s>اوپن لائبريري شروع ڪئي</a> جنهن جو مقصد "هر شايع ٿيل ڪتاب لاءِ هڪ ويب صفحو" هو، مختلف ذريعن مان ڊيٽا گڏ ڪندي. هن کي پنهنجي ڊجيٽل تحفظاتي ڪم لاءِ آخري قيمت ادا ڪرڻي پئي جڏهن هن کي اڪيڊمڪ پيپرز جي وڏي مقدار ۾ ڊائون لوڊ ڪرڻ تي مقدمو ڪيو ويو، جنهن جي نتيجي ۾ هن جي خودڪشي ٿي. بغير چوڻ جي، اهو اسان جي گروپ جي نالي جي هڪ سبب آهي، ۽ ڇو اسان تمام محتاط آهيون. اوپن لائبريري اڃا تائين انٽرنيٽ آرڪائيو ۾ ماڻهن طرفان بهادري سان هلائي رهي آهي، آرون جي ورثي کي جاري رکندي. اسان هن تي هن پوسٽ ۾ بعد ۾ واپس اينداسين. گوگل جي بلاگ پوسٽ ۾، ٽائچر هن انگ کي اندازو لڳائڻ سان لاڳاپيل ڪجهه چئلينجن کي بيان ڪري ٿو. پهريون، ڪتاب ڇا آهي؟ هتي ڪجهه ممڪن تعريفون آهن: “ايڊيشنز” لڳي ٿو ته “ڪتاب” ڇا آهن جي سڀ کان عملي تعريف آهي. سهولت سان، هي تعريف پڻ منفرد ISBN نمبرن جي تفويض لاءِ استعمال ڪئي ويندي آهي. هڪ ISBN، يا انٽرنيشنل اسٽينڊرڊ بڪ نمبر، بين الاقوامي واپار لاءِ عام طور تي استعمال ٿيندو آهي، ڇاڪاڻ ته اهو بين الاقوامي بارڪوڊ سسٽم سان ضم ٿيل آهي ("انٽرنيشنل آرٽيڪل نمبر"). جيڪڏهن توهان چاهيو ٿا ته هڪ ڪتاب دڪانن ۾ وڪرو ڪيو وڃي، ان کي بارڪوڊ جي ضرورت آهي، تنهنڪري توهان کي هڪ ISBN حاصل ٿئي ٿو. ٽائچر جي بلاگ پوسٽ ۾ ذڪر ڪيو ويو آهي ته جڏهن ISBN مددگار آهن، اهي عالمي نه آهن، ڇاڪاڻ ته اهي صرف ستر جي وچ ۾ واقعي اپنائڻ شروع ڪيا ويا، ۽ دنيا جي هر جاءِ تي نه. اڃا تائين، ISBN شايد ڪتابن جي ايڊيشنز جو سڀ کان وڌيڪ استعمال ٿيل سڃاڻپ ڪندڙ آهي، تنهنڪري اهو اسان جو بهترين شروعاتي نقطو آهي. جيڪڏهن اسان دنيا ۾ سڀني ISBNs کي ڳولي سگهون ٿا، اسان وٽ هڪ مددگار فهرست آهي ته ڪهڙا ڪتاب اڃا تائين محفوظ ڪرڻ جي ضرورت آهي. ته پوءِ، اسان کي ڊيٽا ڪٿان ملي ٿي؟ هتي ڪيترائي موجوده ڪوششون آهن جيڪي دنيا جي سڀني ڪتابن جي فهرست مرتب ڪرڻ جي ڪوشش ڪري رهيون آهن: هن پوسٽ ۾، اسان خوشي سان هڪ ننڍي رليز جو اعلان ڪري رهيا آهيون (اسان جي اڳوڻي Z-Library رليز جي مقابلي ۾). اسان ISBNdb جو اڪثر حصو اسڪراپ ڪيو، ۽ ڊيٽا کي Pirate Library Mirror جي ويب سائيٽ تي ٽورينٽنگ لاءِ دستياب ڪيو (ايڊيٽ: منتقل ڪيو ويو <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a>; اسان هتي ان کي سڌو سنئون لنڪ نه ڪنداسين، بس ان کي ڳوليو). اهي تقريباً 30.9 ملين ريڪارڊ آهن (20GB جيئن <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). انهن جي ويب سائيٽ تي اهي دعويٰ ڪن ٿا ته انهن وٽ اصل ۾ 32.6 ملين ريڪارڊ آهن، تنهنڪري اسان شايد ڪنهن طرح ڪجهه وڃائي ڇڏيو آهي، يا <em>اهي</em> ڪجهه غلط ڪري رهيا آهن. ڪنهن به صورت ۾، في الحال اسان صحيح طور تي نه ٻڌائينداسين ته اسان اهو ڪيئن ڪيو - اسان ان کي پڙهندڙ لاءِ هڪ مشق طور ڇڏي ڏينداسين. ;-) جيڪو اسان شيئر ڪنداسين اهو ڪجهه ابتدائي تجزيو آهي، دنيا ۾ ڪتابن جي انگ کي اندازو لڳائڻ جي ويجهو ٿيڻ جي ڪوشش ڪرڻ لاءِ. اسان ٽن ڊيٽا سيٽن تي نظر وڌي: هي نئون ISBNdb ڊيٽا سيٽ، اسان جي اصل رليز جو metadata جيڪو اسان Z-Library شيڊو لائبريري مان اسڪراپ ڪيو (جنهن ۾ لائبريري جينيسس شامل آهي)، ۽ اوپن لائبريري ڊيٽا ڊمپ. ISBNdb ڊمپ، يا ڪيترا ڪتاب هميشه لاءِ محفوظ آهن؟ جيڪڏهن اسان شيڊو لائبريرين مان فائلن کي صحيح طريقي سان ڊپليڪيٽ ڪريون، ته دنيا جي سڀني ڪتابن جو ڪهڙو سيڪڙو اسان محفوظ ڪيو آهي؟ <a %(wikipedia_annas_archive)s>انا جو آرڪائيو</a> بابت تازه ڪاريون، انساني تاريخ ۾ سڀ کان وڏي حقيقي کليل لائبريري. <em>ورلڊ ڪيٽ نئين ڊيزائن</em> ڊيٽا <strong>فارميٽ؟</strong> <a %(blog)s>انا جو آرڪائيو ڪنٽينرز (AAC)</a>، جيڪو بنيادي طور تي <a %(jsonlines)s>JSON Lines</a> آهي جيڪو <a %(zstd)s>Zstandard</a> سان ڪمپريس ٿيل آهي، گڏوگڏ ڪجهه معياري سيمينٽڪس. اهي ڪنٽينرز مختلف قسم جي رڪارڊن کي ڍڪيندا آهن، بنياد تي مختلف اسڪراپ جيڪي اسان استعمال ڪيا. هڪ سال اڳ، اسان <a %(blog)s>هن سوال جو جواب ڏيڻ لاءِ نڪتا</a>: <strong>ڪهڙو سيڪڙو ڪتاب مستقل طور تي شيڊو لائبريرين طرفان محفوظ ڪيا ويا آهن؟</strong> اچو ته ڊيٽا تي ڪجهه بنيادي معلومات تي نظر وجهون: هڪ دفعو هڪ ڪتاب هڪ اوپن-ڊيٽا شيڊو لائبريري ۾ اچي ٿو جهڙوڪ <a %(wikipedia_library_genesis)s>Library Genesis</a>، ۽ هاڻي <a %(wikipedia_annas_archive)s>آنا جو آرڪائيو</a>، اهو سڄي دنيا ۾ آئيني بڻجي ٿو (ٽورينٽس ذريعي)، ان کي عملي طور تي هميشه لاءِ محفوظ ڪري ٿو. ڪتابن جي ڪهڙي سيڪڙو محفوظ ڪيا ويا آهن ان سوال جو جواب ڏيڻ لاءِ، اسان کي ڄاڻڻ جي ضرورت آهي: ڪل ڪيترا ڪتاب موجود آهن؟ ۽ مثالي طور تي اسان وٽ صرف هڪ نمبر نه آهي، پر اصل metadata. پوءِ اسان انهن کي شيڊو لائبريرين سان نه رڳو ملائي سگهون ٿا، پر <strong>باقي ڪتابن کي محفوظ ڪرڻ لاءِ TODO لسٽ ٺاهي سگهون ٿا!</strong> اسان شايد هڪ ڪراوڊ سورس ڪوشش جو خواب ڏسڻ شروع ڪري سگهون ٿا ته هن TODO لسٽ کي ختم ڪيو وڃي. اسان <a %(wikipedia_isbndb_com)s>ISBNdb</a> کي اسڪراپ ڪيو، ۽ <a %(openlibrary)s>Open Library dataset</a> ڊائونلوڊ ڪيو، پر نتيجا غير اطمينان بخش هئا. مکيه مسئلو اهو هو ته ISBNs جو گهڻو اوورليپ نه هو. ڏسو هي وین ڊاگرام <a %(blog)s>اسان جي بلاگ پوسٽ</a> مان: اسان کي حيرت ٿي ته ISBNdb ۽ Open Library جي وچ ۾ ڪيترو ٿورو اوورليپ هو، جيڪي ٻئي مختلف ذريعن مان ڊيٽا شامل ڪن ٿا، جهڙوڪ ويب اسڪراپ ۽ لائبريري رڪارڊ. جيڪڏهن اهي ٻئي اڪثر ISBNs کي ڳولڻ ۾ سٺو ڪم ڪن ٿا، ته انهن جا دائرا ضرور ڪافي اوورليپ ڪندا، يا هڪ ٻئي جو ذيلي سيٽ هوندو. اهو اسان کي حيران ڪري ڇڏيو، ڪيتريون ئي ڪتابون <em> مڪمل طور تي انهن دائري کان ٻاهر </em> آهن؟ اسان کي هڪ وڏي ڊيٽابيس جي ضرورت آهي. اهو وقت هو جڏهن اسان دنيا جي سڀ کان وڏي ڪتاب ڊيٽابيس تي نظرون مرکوز ڪيون: <a %(wikipedia_worldcat)s>ورلڊ ڪيٽ</a>. هي هڪ غير منافع بخش <a %(wikipedia_oclc)s>OCLC</a> پاران مالڪيت واري ڊيٽابيس آهي، جيڪا دنيا جي لائبريرين کان metadata رڪارڊ گڏ ڪري ٿي، ان جي بدلي ۾ انهن لائبريرين کي مڪمل ڊيٽا سيٽ تائين رسائي ڏئي ٿي، ۽ انهن کي آخري صارفين جي ڳولا جي نتيجن ۾ ڏيکاريندي آهي. جيتوڻيڪ OCLC هڪ غير منافع بخش آهي، انهن جو ڪاروباري ماڊل انهن جي ڊيٽابيس جي حفاظت ڪرڻ جي ضرورت آهي. چڱو، اسان کي افسوس آهي، OCLC جا دوست، اسان اهو سڀ ڪجهه ڏئي رهيا آهيون. :-) گذريل سال دوران، اسان ورلڊ ڪيٽ جا سڀ رڪارڊ احتياط سان اسڪراپ ڪيا. شروعات ۾، اسان کي هڪ خوش قسمت موقعو مليو. ورلڊ ڪيٽ پنهنجي مڪمل ويب سائيٽ جي نئين ڊيزائن کي جاري ڪري رهيو هو (آگسٽ 2022 ۾). هن ۾ انهن جي بيڪ اينڊ سسٽم جو وڏو اوور هال شامل هو، جنهن ڪيترائي سيڪيورٽي خاميون متعارف ڪرايون. اسان فوري طور تي ان موقعي کي استعمال ڪيو، ۽ صرف ڪجهه ڏينهن ۾ لکين (!) رڪارڊ اسڪراپ ڪرڻ ۾ ڪامياب ٿي ويا. ان کان پوءِ، سيڪيورٽي خاميون آهستي آهستي هڪ هڪ ڪري درست ڪيون ويون، جيستائين آخري هڪ کي لڳ ڀڳ هڪ مهينو اڳ پيچ ڪيو ويو. ان وقت تائين اسان وٽ تقريباً سڀئي رڪارڊ هئا، ۽ صرف ٿورو وڌيڪ معيار وارا رڪارڊ حاصل ڪرڻ لاءِ وڃي رهيا هئاسين. تنهن ڪري اسان محسوس ڪيو ته اهو جاري ڪرڻ جو وقت آهي! 1.3B ورلڊ ڪيٽ اسڪراپ <em><strong>مختصر طور تي:</strong> آنا جي آرڪائيو ورلڊ ڪيٽ (دنيا جي سڀ کان وڏي لائبريري metadata مجموعي) کي اسڪراپ ڪيو ته جيئن انهن ڪتابن جي TODO لسٽ ٺاهي وڃي جيڪي محفوظ ڪرڻ جي ضرورت آهي.</em> ورلڊ ڪيٽ خبردار: هن بلاگ پوسٽ کي ختم ڪيو ويو آهي. اسان فيصلو ڪيو آهي ته IPFS اڃا تائين وقت لاءِ تيار ناهي. اسان اڃا تائين اننا جي آرڪائيو مان IPFS تي فائلن کي لنڪ ڪنداسين جڏهن ممڪن هجي، پر اسان ان کي پاڻ ميزبان نه ڪنداسين، ۽ نه ئي ٻين کي IPFS استعمال ڪندي آئيني ڪرڻ جي سفارش ڪنداسين. جيڪڏهن توهان اسان جي مجموعي کي محفوظ ڪرڻ ۾ مدد ڪرڻ چاهيو ٿا ته مهرباني ڪري اسان جي ٽورنٽس صفحي کي ڏسو. IPFS تي Z-Library کي مدد ڪريو پارٽنر سرور ڊائون لوڊ SciDB ٻاهريون ادھارو ٻاهريون ادھارو (پرنٽ معذور) ٻاهريون ڊائون لوڊ ميٽا ڊيٽا ڳوليو ٽورينٽس ۾ شامل واپس  (+%(num)s بونس) ادا نه ٿيل ادا ٿيل منسوخ ٿيل مدت ختم ٿيل انا جي تصديق جو انتظار غلط هيٺ ڏنل متن انگريزي ۾ جاري آهي. وڃو ري سيٽ اڳتي آخري جيڪڏهن توهان جو اي ميل پتو Libgen فورمز تي ڪم نٿو ڪري، اسان صلاح ڏيون ٿا ته <a %(a_mail)s>Proton Mail</a> (مفت) استعمال ڪريو. توهان پڻ <a %(a_manual)s>دستي طور تي درخواست</a> ڪري سگهو ٿا ته توهان جو اڪائونٽ فعال ڪيو وڃي. (شايد گهربل <a %(a_browser)s>برائوزر جي تصديق</a> — لامحدود ڊائون لوڊ!) تيز پارٽنر سرور #%(number)s (سفارش ٿيل) (ٿورو تيز پر انتظار جي فهرست سان) (ڪو برائوزر تصديق گهربل ناهي) (ڪو به برائوزر تصديق يا انتظار جي فهرست ناهي) (ڪو به انتظار جي فهرست ناهي، پر تمام سست ٿي سگهي ٿو) سست پارٽنر سرور #%(number)s آڊيو بڪ ڪامڪ ڪتاب ڪتاب (افسانوي) ڪتاب (غير افسانوي) ڪتاب (اڻڄاتل) جرنل آرٽيڪل ميگزين ميوزڪ اسڪور ٻيو معياري دستاويز سڀئي صفحا PDF ۾ تبديل نه ٿي سگهيا Libgen.li ۾ ٽٽل نشان لڳل Libgen.li ۾ نظر نٿو اچي Libgen.rs افسانوي ۾ نظر نٿو اچي Libgen.rs غير افسانوي ۾ نظر نٿو اچي هن فائل تي exiftool هلائڻ ۾ ناڪام Z-Library ۾ "خراب فائل" طور نشان لڳايو ويو Z-Library مان غائب Z-Library ۾ "اسپام" طور نشان لڳايو ويو فائل نٿو کلي سگهي (مثال طور خراب ٿيل فائل، DRM) ڪاپي رائيٽ دعويٰ ڊائون لوڊ ڪرڻ جا مسئلا (مثال طور ڪنيڪٽ نٿو ٿي سگهي، غلطي جو پيغام، تمام سست) غلط ميٽا ڊيٽا (مثال طور عنوان، وضاحت، ڪور تصوير) ٻيو گهٽ معيار (مثال طور، فارميٽنگ مسئلا، خراب اسڪين معيار، گم ٿيل صفحا) اسپام / فائل کي هٽايو وڃي (مثال طور، اشتهارن، بدتميزي مواد) %(amount)s (%(amount_usd)s) %(amount)s ڪل %(amount)s (%(amount_usd)s) ڪل شاندار ڪتابي ڪيڙو خوش قسمت لائبريرين دلڪش ڊيٽا هورڊر حيرت انگيز آرڪائوسٽ بونس ڊائون لوڊ Cerlalc چيڪ ميٽا ڊيٽا DuXiu 读秀 EBSCOhost اي بڪ انڊيڪس گوگل بڪس گوڊ ريڊز ھاتھی ٽرسٽ IA IA ڪنٽرول ٿيل ڊجيٽل قرض ڏيڻ ISBNdb ISBN GRP Libgen.li ”scimag“ کي خارج ڪندي Libgen.rs غير افسانوي ۽ افسانوي Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary روسي رياستي لائبريري Sci-Hub Libgen.li “scimag” ذريعي Sci-Hub / Libgen “scimag” Trantor AA ڏانهن اپلوڊز Z-Library Z-Library چيني عنوان، ليکڪ، DOI، ISBN، MD5، … ڳولا ليکڪ وضاحت ۽ ميٽا ڊيٽا تبصرا ايڊيشن اصلي فائل جو نالو پبلشر (مخصوص ميدان ڳوليو) عنوان سال شايع ٿيل ٽيڪنيڪل تفصيل هن سڪو جو گهٽ ۾ گهٽ حد کان وڌيڪ آهي. مهرباني ڪري هڪ مختلف مدت يا هڪ مختلف سڪو چونڊيو. درخواست مڪمل نه ٿي سگهي. مهرباني ڪري ڪجهه منٽن ۾ ٻيهر ڪوشش ڪريو، ۽ جيڪڏهن اهو جاري رهي ته مهرباني ڪري اسان سان %(email)s تي اسڪرين شاٽ سان رابطو ڪريو. هڪ اڻڄاتل غلطي پيش آئي. مهرباني ڪري اسان سان %(email)s تي اسڪرين شاٽ سان رابطو ڪريو. ادائيگي جي پروسيسنگ ۾ غلطي. مهرباني ڪري هڪ لمحي لاءِ انتظار ڪريو ۽ ٻيهر ڪوشش ڪريو. جيڪڏهن مسئلو 24 ڪلاڪن کان وڌيڪ جاري رهي، مهرباني ڪري اسان سان %(email)s تي اسڪرين شاٽ سان رابطو ڪريو. اسان دنيا جي سڀ کان وڏي ڪامڪس شيڊو لائبريري جي بيڪ اپ لاءِ فنڊ گڏ ڪري رهيا آهيون. توهان جي مدد لاءِ مهرباني! <a href="/donate">عطيو ڏيو.</a> جيڪڏهن توهان عطيو نٿا ڏئي سگهو، ته مهرباني ڪري اسان جي مدد ڪريو پنهنجي دوستن کي ٻڌائي، ۽ اسان کي <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>، يا <a href="https://t.me/annasarchiveorg">Telegram</a> تي فالو ڪري. اسان کي <a %(a_request)s>ڪتابن جي درخواست</a> ڪرڻ لاءِ اي ميل نه ڪريو<br>يا ننڍا (<10k) <a %(a_upload)s>اپلوڊ</a>. انا جو آرڪائيو DMCA / ڪاپي رائيٽ دعوائون رابطو ۾ رهو Reddit متبادل SLUM (%(unaffiliated)s) غير منسلڪ انا جي آرڪائيو کي توهان جي مدد جي ضرورت آهي! جيڪڏهن توهان هاڻي عطيو ڪندا، ته توهان کي تيز ڊائون لوڊز جو <strong>ڊبل</strong> تعداد ملندو. ڪيترا ئي اسان کي ختم ڪرڻ جي ڪوشش ڪن ٿا، پر اسان مقابلو ڪريون ٿا. جيڪڏهن توهان هن مهيني چندو ڪيو، توهان کي <strong>ٻيڻو</strong> تيز ڊائون لوڊ جو تعداد ملندو. هن مهيني جي آخر تائين صحيح. انساني علم کي بچائڻ: هڪ بهترين موڪلن جو تحفو! رڪنيتون ان مطابق وڌايون وينديون. شريڪ سرور ميزباني جي بندش سبب دستياب نه آهن. اهي جلد ئي ٻيهر دستياب ٿي ويندا. انا جي آرڪائيو جي لچڪ وڌائڻ لاءِ، اسان رضاڪارن جي ڳولا ۾ آهيون جيڪي آئيني سائيٽون هلائين. اسان وٽ هڪ نئون عطيو ڏيڻ جو طريقو موجود آهي: %(method_name)s. مهرباني ڪري غور ڪريو %(donate_link_open_tag)sعطيو ڏيڻ</a> — هي ويب سائيٽ هلائڻ سستو ناهي، ۽ توهان جو عطيو واقعي فرق پيدا ڪري ٿو. توهان جي تمام گهڻي مهرباني. هڪ دوست کي ريفر ڪريو، ۽ توهان ۽ توهان جو دوست ٻئي حاصل ڪندا %(percentage)s%% بونس تيز ڊائون لوڊ! پيار واري کي حيران ڪريو، انهن کي ميمبرشپ سان اڪائونٽ ڏيو. محبوبن لاءِ بهترين تحفو! وڌيڪ سکو… اکاؤنٹ سرگرمي ترقي يافته انا جو بلاگ ↗ انا جو سافٽ ويئر ↗ بيٽا ڪوڊز ايڪسپلورر ڊيٽا سيٽس عطيو ڪريو ڊائون لوڊ ٿيل فائلون عام سوال گهر ميٽا ڊيٽا بهتر ڪريو ايل ايل ايم ڊيٽا لاگ ان / رجسٽر ڪريو منهنجا چندا عوامي پروفائل ڳولا سڪيورٽي ٽورينٽس ترجمو ڪريو ↗ رضاڪار ۽ انعام تازا ڊائون لوڊ: 📚&nbsp;دنيا جي سڀ کان وڏي اوپن سورس اوپن ڊيٽا لائبريري. ⭐️&nbsp;Sci-Hub، Library Genesis، Z-Library، ۽ وڌيڪ کي آئيني بڻائي ٿو. 📈&nbsp;%(book_any)s ڪتاب، %(journal_article)s پيپرز، %(book_comic)s ڪامڪس، %(magazine)s ميگزين — هميشه لاءِ محفوظ.  ۽  ۽ وڌيڪ DuXiu Internet Archive Lending Library LibGen 📚&nbsp;انساني تاريخ ۾ سڀ کان وڏي واقعي کليل لائبريري. 📈&nbsp;%(book_count)s&nbsp; ڪتاب، %(paper_count)s&nbsp; پيپرز — هميشه لاءِ محفوظ. ⭐️&nbsp;اسان %(libraries)s کي آئيني بڻايون ٿا. اسان اسڪراپ ڪريون ٿا ۽ اوپن سورس %(scraped)s. اسان جو سڀ ڪوڊ ۽ ڊيٽا مڪمل طور تي اوپن سورس آهي. OpenLib Sci-Hub ،  📚 دنيا جي سڀ کان وڏي اوپن سورس اوپن ڊيٽا لائبريري.<br>⭐️ Scihub، Libgen، Zlib، ۽ وڌيڪ کي آئيني بڻائي ٿو. Z-Lib انا جو آرڪائيو غلط درخواست. دورو ڪريو %(websites)s. دنيا جي سڀ کان وڏي اوپن سورس اوپن ڊيٽا لائبريري. Sci-Hub، Library Genesis، Z-Library، ۽ وڌيڪ جي آئيني. انا جي آرڪائيو ۾ ڳولا ڪريو انا جو آرڪائيو مهرباني ڪري ٻيهر ڪوشش ڪرڻ لاءِ ريفريش ڪريو. <a %(a_contact)s>اسان سان رابطو ڪريو</a> جيڪڏهن مسئلو ڪيترن ئي ڪلاڪن تائين جاري رهي. 🔥 ھن صفحي کي لوڊ ڪرڻ ۾ مسئلو <li>1. اسان کي <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>، يا <a href="https://t.me/annasarchiveorg">Telegram</a> تي فالو ڪريو.</li><li>2. Twitter، Reddit، Tiktok، Instagram، پنهنجي مقامي ڪيفي يا لائبريري ۾، يا جتي به وڃو، انا جي آرڪائيو بابت ڳالهه ڪريو! اسان گيٽ ڪيپنگ ۾ يقين نٿا رکون — جيڪڏهن اسان کي هٽايو ويو ته اسان ٻيهر ڪنهن ٻئي هنڌ تي ظاهر ٿي وينداسين، ڇو ته اسان جو سڄو ڪوڊ ۽ ڊيٽا مڪمل طور تي اوپن سورس آهي.</li><li>3. جيڪڏهن توهان قابل آهيو، ته <a href="/donate">عطيو ڏيڻ</a> تي غور ڪريو.</li><li>4. اسان جي ويب سائيٽ کي مختلف ٻولين ۾ <a href="https://translate.annas-software.org/">ترجمو ڪرڻ</a> ۾ مدد ڪريو.</li><li>5. جيڪڏهن توهان هڪ سافٽ ويئر انجنيئر آهيو، ته اسان جي <a href="https://annas-software.org/">اوپن سورس</a> ۾ حصو وٺڻ تي غور ڪريو، يا اسان جي <a href="/datasets">ٽورينٽس</a> کي سيڊ ڪرڻ ۾ مدد ڪريو.</li> 10. پنهنجي ٻوليءَ ۾ Anna’s Archive لاءِ وڪيپيڊيا صفحو ٺاهيو يا ان کي برقرار رکڻ ۾ مدد ڪريو. 11. اسان ننڍا، ذوقائتي اشتهار رکڻ چاهيون ٿا. جيڪڏهن توهان Anna’s Archive تي اشتهار ڏيڻ چاهيو ٿا، مهرباني ڪري اسان کي ٻڌايو. 6. جيڪڏهن توهان هڪ سيڪيورٽي محقق آهيو، ته اسان توهان جي صلاحيتن کي جارحيت ۽ دفاع ٻنهي لاءِ استعمال ڪري سگهون ٿا. اسان جي <a %(a_security)s>سيڪيورٽي</a> صفحي کي چيڪ ڪريو. 7. اسان گمنام واپارين لاءِ ادائيگي جي ماهرن جي ڳولا ۾ آهيون. ڇا توهان اسان کي وڌيڪ آسان طريقا شامل ڪرڻ ۾ مدد ڪري سگهو ٿا؟ PayPal، WeChat، گفٽ ڪارڊ. جيڪڏهن توهان ڪنهن کي ڄاڻو ٿا، مهرباني ڪري اسان سان رابطو ڪريو. 8. اسان هميشه وڌيڪ سرور جي گنجائش جي ڳولا ۾ آهيون. 9. توهان فائل جي مسئلن جي رپورٽ ڪرڻ، تبصرا ڇڏڻ، ۽ هن ويب سائيٽ تي فهرستون ٺاهڻ سان مدد ڪري سگهو ٿا. توهان وڌيڪ ڪتاب <a %(a_upload)s>اپلوڊ ڪرڻ</a>، يا موجوده ڪتابن جي فائلن جي مسئلن يا فارميٽنگ کي درست ڪرڻ سان پڻ مدد ڪري سگهو ٿا. رضاڪار ٿيڻ بابت وڌيڪ ڄاڻ لاءِ، اسان جي <a %(a_volunteering)s>رضاڪار ۽ انعام</a> صفحي ڏانهن ڏسو. اسان ڄاڻ ۽ ثقافت جي مفت وهڪري ۽ حفاظت ۾ مضبوط يقين رکون ٿا. هن سرچ انجڻ سان، اسان وڏن ماڻهن جي ڪلهن تي بيٺا آهيون. اسان انهن ماڻهن جي محنت جو گهڻو احترام ڪريون ٿا جن مختلف شيڊو لائبريريز ٺاهيون آهن، ۽ اسان اميد رکون ٿا ته هي سرچ انجڻ انهن جي پهچ کي وسيع ڪندو. اسان جي ترقي تي تازه ڪاري رهڻ لاءِ، انا کي <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> يا <a href="https://t.me/annasarchiveorg">Telegram</a> تي فالو ڪريو. سوالن ۽ راءِ لاءِ مهرباني ڪري انا سان %(email)s تي رابطو ڪريو. اکائونٽ ID: %(account_id)s لاگ آئوٽ ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. ✅ توهان هاڻي لاگ آئوٽ ٿي ويا آهيو. ٻيهر لاگ ان ٿيڻ لاءِ صفحو ٻيهر لوڊ ڪريو. تيز ڊائون لوڊ استعمال ٿيل (آخري 24 ڪلاڪ): <strong>%(used)s / %(total)s</strong> رڪنيت: <strong>%(tier_name)s</strong> تائين %(until_date)s <a %(a_extend)s>(وڌايو)</a> توهان ڪيترن ئي ميمبرشپس کي گڏ ڪري سگهو ٿا (24 ڪلاڪن ۾ تيز ڊائون لوڊ گڏ ڪيا ويندا). رڪنيت: <strong>ڪابه ناهي</strong> <a %(a_become)s>(رڪن بڻجو)</a> جيڪڏهن توهان پنهنجي ميمبرشپ کي وڏي سطح تي اپگريڊ ڪرڻ ۾ دلچسپي رکو ٿا ته انا سان %(email)s تي رابطو ڪريو. عوامي پروفائل: %(profile_link)s خفيه چاٻي (شيئر نه ڪريو!): %(secret_key)s ظاهر ڪريو اسان سان هتي شامل ٿيو! اسان جي گروپ ۾ شامل ٿيڻ لاءِ <a %(a_tier)s>وڏي سطح</a> تي اپگريڊ ڪريو. خاص Telegram گروپ: %(link)s اکائونٽ ڪهڙا ڊائون لوڊ؟ لاگ ان ٿيو پنهنجو ڪنج نه وڃايو! غلط خفيه ڪنج. پنهنجي ڪنج جي تصديق ڪريو ۽ ٻيهر ڪوشش ڪريو، يا متبادل طور هيٺ نئون اڪائونٽ رجسٽر ڪريو. خفيه ڪنج لاگ ان ٿيڻ لاءِ پنهنجو خفيه ڪنج داخل ڪريو: پراڻو اي ميل بيسڊ اڪائونٽ؟ پنهنجو <a %(a_open)s>اي ميل هتي داخل ڪريو</a>. نئون اڪائونٽ رجسٽر ڪريو اڃا تائين اڪائونٽ ناهي؟ رجسٽريشن ڪامياب! توهان جو خفيه ڪنج آهي: <span %(span_key)s>%(key)s</span> هن ڪنج کي احتياط سان محفوظ ڪريو. جيڪڏهن توهان ان کي وڃائي ڇڏيو، توهان پنهنجي اڪائونٽ تائين رسائي وڃائي ڇڏيندؤ. <li %(li_item)s><strong>بڪ مارڪ.</strong> توهان هن صفحي کي بڪ مارڪ ڪري سگهو ٿا ته جيئن توهان جو ڪنج حاصل ڪري سگهو.</li><li %(li_item)s><strong>ڊائون لوڊ.</strong> ڪلڪ ڪريو <a %(a_download)s>هن لنڪ</a> تي ته جيئن توهان جو ڪنج ڊائون لوڊ ڪري سگهو.</li><li %(li_item)s><strong>پاسورڊ مينيجر.</strong> جڏهن توهان هيٺ ڏنل ڪنج داخل ڪريو ته پاسورڊ مينيجر استعمال ڪريو ته جيئن ڪنج محفوظ ڪري سگهو.</li> لاگ ان / رجسٽر ڪريو برائوزر جي تصديق خبردار: ڪوڊ ۾ غلط يونيڪوڊ ڪردار آهن، ۽ مختلف حالتن ۾ غلط طريقي سان ڪم ڪري سگهي ٿو. خام بائنري کي URL ۾ base64 نمائندگي مان ڊيڪوڊ ڪري سگهجي ٿو. وضاحت ليبل پري فڪس هڪ مخصوص ڪوڊ لاءِ URL ويب سائيٽ ڪوڊ “%(prefix_label)s” سان شروع ٿيندڙ مهرباني ڪري انهن صفحن کي اسڪراپ نه ڪريو. ان جي بدران اسان سفارش ڪريون ٿا <a %(a_import)s>جنريٽ ڪرڻ</a> يا <a %(a_download)s>ڊائونلوڊ ڪرڻ</a> اسان جي ElasticSearch ۽ MariaDB ڊيٽابيسز، ۽ اسان جو <a %(a_software)s>اوپن سورس ڪوڊ</a> هلائڻ. خام ڊيٽا کي دستي طور تي JSON فائلن ذريعي ڳولي سگهجي ٿو جهڙوڪ <a %(a_json_file)s>هي هڪ</a>. %(count)s رڪارڊ کان گهٽ عام URL ڪوڊس ايڪسپلورر انڊيڪس آف ڪوڊس کي ڳوليو جيڪي ريڪارڊن سان ٽيگ ٿيل آهن، پري فڪس سان. "ريڪارڊن" ڪالم ڏيکاري ٿو ته ڪيترا ريڪارڊ ڪوڊس سان ٽيگ ٿيل آهن ڏنل پري فڪس سان، جيئن سرچ انجڻ ۾ ڏٺو ويو آهي (صرف ميٽا ڊيٽا ريڪارڊن سميت). "ڪوڊس" ڪالم ڏيکاري ٿو ته ڪيترا اصل ڪوڊس ڏنل پري فڪس سان آهن. ڄاتل ڪوڊ پري فڪس "%(key)s" وڌيڪ… پري فڪس %(count)s رڪارڊ “%(prefix_label)s” سان ملندڙ %(count)s رڪارڊ “%(prefix_label)s” سان ملندڙ ڪوڊ رڪارڊ "%%s" کي ڪوڊ جي قيمت سان تبديل ڪيو ويندو انا جي آرڪائيو کي ڳوليو ڪوڊس مخصوص ڪوڊ لاءِ URL: “%(url)s” هي صفحو ٺاهڻ ۾ ڪجهه وقت لڳي سگهي ٿو، جنهن ڪري اهو Cloudflare ڪئپچا جي ضرورت آهي. <a %(a_donate)s>ممبران</a> ڪئپچا کي ڇڏي سگهن ٿا. بدتميزي رپورٽ ڪئي وئي: بهتر ورزن ڇا توهان هن صارف کي بدتميزي يا نامناسب رويي لاءِ رپورٽ ڪرڻ چاهيو ٿا؟ فائل مسئلو: %(file_issue)s لڪيل تبصرو جواب ڏيو بدتميزي رپورٽ ڪريو توهان هن صارف کي بدتميزي لاءِ رپورٽ ڪيو. هن اي ميل تي ڪاپي رائيٽ دعوائن کي نظرانداز ڪيو ويندو؛ ان جي بدران فارم استعمال ڪريو. اي ميل ڏيکاريو اسان توهان جي راءِ ۽ سوالن کي تمام گهڻو ڀليڪار ڪريون ٿا! تنهن هوندي، اسپام ۽ بيڪار اي ميلن جي مقدار جي ڪري، مهرباني ڪري باڪس چيڪ ڪريو ته توهان اسان سان رابطو ڪرڻ جي انهن شرطن کي سمجهو ٿا. ڪاپي رائيٽ دعوائن بابت اسان سان رابطو ڪرڻ جا ٻيا طريقا خودڪار طريقي سان حذف ٿي ويندا. DMCA / ڪاپي رائيٽ دعوائن لاءِ، <a %(a_copyright)s>هي فارم</a> استعمال ڪريو. رابطي جو اي ميل Anna’s Archive تي URLs (ضروري). هڪ قطار ۾ هڪ. مهرباني ڪري صرف اهي URLs شامل ڪريو جيڪي ڪتاب جي ساڳي ايڊيشن کي بيان ڪن. جيڪڏهن توهان ڪيترن ئي ڪتابن يا ڪيترن ئي ايڊيشنن لاءِ دعويٰ ڪرڻ چاهيو ٿا، مهرباني ڪري هي فارم ڪيترائي ڀيرا جمع ڪريو. دعويٰ جيڪي ڪيترن ئي ڪتابن يا ايڊيشنن کي گڏ ڪن ٿيون رد ڪيون وينديون. پتو (ضروري) ذريعو مواد جي واضح وضاحت (ضروري) اي ميل (ضروري) ذريعو مواد جا URLs، هڪ قطار ۾ هڪ (ضروري). مهرباني ڪري جيترا ٿي سگهن شامل ڪريو، اسان کي توهان جي دعويٰ جي تصديق ڪرڻ ۾ مدد ڏيڻ لاءِ (مثال طور Amazon، WorldCat، Google Books، DOI). ذريعو مواد جا ISBNs (جيڪڏهن لاڳو هجي). هڪ قطار ۾ هڪ. مهرباني ڪري صرف اهي شامل ڪريو جيڪي بلڪل ان ايڊيشن سان ملن ٿا جنهن لاءِ توهان ڪاپي رائيٽ دعويٰ ڪري رهيا آهيو. توهان جو نالو (ضروري) ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. ✅ توهان جي ڪاپي رائيٽ دعويٰ جمع ڪرڻ لاءِ مهرباني. اسان ان کي جيترو جلد ٿي سگهي جائزو وٺنداسين. مهرباني ڪري ٻي دعويٰ داخل ڪرڻ لاءِ صفحو ٻيهر لوڊ ڪريو. <a %(a_openlib)s>Open Library</a> URLs ذريعو مواد جا، هڪ قطار ۾ هڪ. مهرباني ڪري هڪ لمحو وٺي Open Library ۾ پنهنجي ذريعو مواد کي ڳوليو. هي اسان کي توهان جي دعويٰ جي تصديق ڪرڻ ۾ مدد ڏيندو. فون نمبر (ضروري) بيان ۽ دستخط (ضروري) دعويٰ جمع ڪريو جيڪڏهن توهان وٽ DCMA يا ٻئي ڪاپي رائيٽ دعويٰ آهي، مهرباني ڪري هي فارم جيترو ممڪن هجي اوترو صحيح ڀريو. جيڪڏهن توهان کي ڪنهن مسئلي جو منهن ڏسڻو پوي، مهرباني ڪري اسان سان اسان جي مخصوص DMCA پتي تي رابطو ڪريو: %(email)s. نوٽ ڪريو ته هن پتي تي موڪليل دعوائون پروسيس نه ڪيون وينديون، هي صرف سوالن لاءِ آهي. مهرباني ڪري پنهنجي دعوائن کي جمع ڪرڻ لاءِ هيٺ ڏنل فارم استعمال ڪريو. DMCA / ڪاپي رائيٽ دعويٰ فارم انا جي آرڪائيو تي مثال رڪارڊ انا جي آرڪائيو پاران ٽورينٽس انا جي آرڪائيو ڪنٽينرز فارميٽ ميٽا ڊيٽا درآمد ڪرڻ لاءِ اسڪرپٽس جيڪڏهن توهان هن ڊيٽا سيٽ کي <a %(a_archival)s>آرڪائيو</a> يا <a %(a_llm)s>LLM ٽريننگ</a> مقصدن لاءِ مرر ڪرڻ ۾ دلچسپي رکو ٿا، مهرباني ڪري اسان سان رابطو ڪريو. آخري اپڊيٽ: %(date)s مکيه %(source)s ويب سائيٽ ميٽا ڊيٽا دستاويز (گهڻا فيلڊ) انا جي آرڪائيو پاران آئينه ٿيل فائلون: %(count)s (%(percent)s%%) وسيلن ڪل فائلون: %(count)s ڪل فائل سائيز: %(size)s هن ڊيٽا بابت اسان جي بلاگ پوسٽ <a %(duxiu_link)s>Duxiu</a> هڪ وڏي ڊيٽابيس آهي اسڪين ٿيل ڪتابن جو، جيڪو <a %(superstar_link)s>SuperStar Digital Library Group</a> پاران ٺاهيو ويو آهي. گهڻا اڪيڊمڪ ڪتاب آهن، جيڪي يونيورسٽين ۽ لائبريرين کي ڊجيٽل طور تي دستياب ڪرڻ لاءِ اسڪين ڪيا ويا آهن. اسان جي انگريزي ڳالهائيندڙ سامعين لاءِ، <a %(princeton_link)s>Princeton</a> ۽ <a %(uw_link)s>University of Washington</a> وٽ سٺا جائزا آهن. هتي هڪ بهترين مضمون پڻ آهي جيڪو وڌيڪ پس منظر ڏئي ٿو: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Duxiu جا ڪتاب چيني انٽرنيٽ تي گهڻي وقت کان پائيرٽ ڪيا ويا آهن. عام طور تي اهي ٻيهر وڪڻندڙن پاران هڪ ڊالر کان گهٽ ۾ وڪرو ڪيا ويندا آهن. اهي عام طور تي گوگل ڊرائيو جي چيني برابر استعمال ڪندي ورهايا ويندا آهن، جنهن کي اڪثر ڪري وڌيڪ اسٽوريج اسپيس جي اجازت ڏيڻ لاءِ هيڪ ڪيو ويو آهي. ڪجهه ٽيڪنيڪل تفصيل <a %(link1)s>هتي</a> ۽ <a %(link2)s>هتي</a> ملي سگهن ٿا. جيتوڻيڪ ڪتاب نيم عوامي طور تي ورهايا ويا آهن، انهن کي وڏي مقدار ۾ حاصل ڪرڻ ڏاڍو مشڪل آهي. اسان هن کي پنهنجي TODO لسٽ ۾ اعليٰ ترجيح ڏني هئي، ۽ ان لاءِ مڪمل وقت جي ڪم جا ڪيترائي مهينا مختص ڪيا هئا. بهرحال، 2023 جي آخر ۾ هڪ ناقابل يقين، حيرت انگيز، ۽ باصلاحيت رضاڪار اسان سان رابطو ڪيو، ٻڌائيندي ته انهن اڳ ۾ ئي اهو سڀ ڪم ڪيو هو — وڏي خرچ تي. انهن اسان سان مڪمل مجموعو شيئر ڪيو، بغير ڪنهن بدلي جي توقع جي، سواءِ ڊگهي مدي واري حفاظت جي ضمانت جي. واقعي قابل ذڪر. اسان جي رضاڪارن کان وڌيڪ معلومات (خام نوٽس): اسان جي <a %(a_href)s>بلاگ پوسٽ</a> مان ترتيب ڏنل. DuXiu 读秀 %(count)s فائل %(count)s فائلون هي ڊيٽا سيٽ <a %(a_datasets_openlib)s>Open Library ڊيٽا سيٽ</a> سان ويجهي لاڳاپيل آهي. ان ۾ IA جي ڪنٽرولڊ ڊجيٽل لينڊنگ لائبريري مان سڀني ميٽا ڊيٽا ۽ فائلن جو وڏو حصو شامل آهي. اپڊيٽس <a %(a_aac)s>Anna’s Archive Containers فارميٽ</a> ۾ جاري ڪيون وينديون. اهي رڪارڊ سڌو سنئون Open Library ڊيٽا سيٽ مان حوالو ڏنا ويا آهن، پر ان ۾ اهڙا رڪارڊ پڻ شامل آهن جيڪي Open Library ۾ نه آهن. اسان وٽ پڻ ڪيترن ئي ڊيٽا فائلن جو تعداد آهي جيڪي ڪميونٽي ميمبرن پاران سالن کان اسڪراپ ڪيا ويا آهن. مجموعو ٻن حصن تي مشتمل آهي. توهان کي سڀني ڊيٽا حاصل ڪرڻ لاءِ ٻنهي حصن جي ضرورت آهي (سواءِ سپرسيڊ ٿيل ٽورنٽس جي، جيڪي ٽورنٽس صفحي تي ڪراس ڪيا ويا آهن). ڊجيٽل قرض ڏيڻ واري لائبريري اسان جي پهرين رليز، <a %(a_aac)s>انا جي آرڪائيو ڪنٽينرز (AAC) فارميٽ</a> تي معياري ٿيڻ کان اڳ. ميٽا ڊيٽا (جيئن json ۽ xml)، pdfs (acsm ۽ lcpdf ڊجيٽل قرض ڏيڻ وارن نظامن مان)، ۽ ڪور ٿمبنيئلز تي مشتمل آهي. AAC استعمال ڪندي نئين رليز. صرف 2023-01-01 کان پوءِ ٽائم اسٽيمپس سان ميٽا ڊيٽا تي مشتمل آهي، ڇو ته باقي اڳ ۾ ئي "ia" پاران ڍڪيل آهي. پڻ سڀ pdf فائلون، هن ڀيري acsm ۽ "bookreader" (IA جو ويب ريڊر) قرض ڏيڻ وارن نظامن مان. نالي جي بلڪل صحيح نه هجڻ جي باوجود، اسان اڃا تائين bookreader فائلن کي ia2_acsmpdf_files مجموعي ۾ شامل ڪريون ٿا، ڇو ته اهي هڪ ٻئي سان خاص آهن. IA ڪنٽرول ٿيل ڊجيٽل قرض ڏيڻ 98%%+ فائلون ڳولڻ لائق آهن. اسان جو مشن آهي ته دنيا جي سڀني ڪتابن (۽ پيپرز، ميگزين وغيره) کي آرڪائيو ڪيو وڃي، ۽ انهن کي وسيع طور تي رسائي لائق بڻايو وڃي. اسان يقين رکون ٿا ته سڀني ڪتابن کي وڏي پيماني تي مرر ڪيو وڃي، ته جيئن ريڊنڊنسي ۽ ريزيلينسي کي يقيني بڻائي سگهجي. اهو ئي سبب آهي جو اسان مختلف ذريعن مان فائلون گڏ ڪري رهيا آهيون. ڪجهه ذريعا مڪمل طور تي کليل آهن ۽ انهن کي وڏي پيماني تي مرر ڪري سگهجي ٿو (جهڙوڪ Sci-Hub). ٻيا بند ۽ حفاظتي آهن، تنهن ڪري اسان انهن کي اسڪراپ ڪرڻ جي ڪوشش ڪريون ٿا ته جيئن انهن جي ڪتابن کي "آزاد" ڪري سگهجي. ٻيا ڪجهه وچ ۾ اچن ٿا. اسان جا سڀ ڊيٽا <a %(a_torrents)s>ٽورينٽ</a> ڪري سگهجن ٿا، ۽ اسان جا سڀ ميٽا ڊيٽا <a %(a_anna_software)s>جنريٽ</a> يا <a %(a_elasticsearch)s>ڊائون لوڊ</a> ڪري سگهجن ٿا جيئن ElasticSearch ۽ MariaDB ڊيٽابيس. را ڊيٽا کي دستي طور تي JSON فائلن ذريعي ڳولهي سگهجي ٿو جهڙوڪ <a %(a_dbrecord)s>هي</a>. ميٽا ڊيٽا آءِ ايس بي اين ويب سائيٽ آخري اپڊيٽ: %(isbn_country_date)s (%(link)s) وسيلن بين الاقوامي آءِ ايس بي اين ايجنسي باقاعدگي سان حدون جاري ڪري ٿي جيڪي هن قومي آءِ ايس بي اين ايجنسين کي الاٽ ڪيون آهن. ان مان اسان اهو نڪتو ڪري سگهون ٿا ته هي آءِ ايس بي اين ڪهڙي ملڪ، علائقي، يا ٻولي گروپ سان واسطو رکي ٿو. اسان هن ڊيٽا کي في الحال غير سڌي طرح استعمال ڪري رهيا آهيون، <a %(a_isbnlib)s>isbnlib</a> Python لائبريري ذريعي. آءِ ايس بي اين ملڪ جي ڄاڻ هي هڪ dump آهي ڪيترن ئي ڪالن جو isbndb.com ڏانهن سيپٽمبر 2022 دوران. اسان ڪوشش ڪئي ته سڀ آءِ ايس بي اين حدون ڍڪيون وڃن. هي تقريباً 30.9 ملين رڪارڊ آهن. انهن جي ويب سائيٽ تي اهي دعويٰ ڪن ٿا ته انهن وٽ اصل ۾ 32.6 ملين رڪارڊ آهن، تنهن ڪري اسان شايد ڪنهن طرح ڪجهه وڃائي ڇڏيو آهي، يا <em>اهي</em> ڪجهه غلط ڪري رهيا آهن. JSON جواب تقريباً انهن جي سرور کان خام آهن. هڪ ڊيٽا جي معيار جو مسئلو جيڪو اسان ڏٺو، اهو آهي ته آءِ ايس بي اين-13 نمبرن لاءِ جيڪي مختلف پري فيڪس سان شروع ٿين ٿا “978-”، اهي اڃا تائين هڪ “isbn” فيلڊ شامل ڪن ٿا جيڪو صرف آءِ ايس بي اين-13 نمبر آهي پهرين 3 نمبرن کي ڪٽڻ سان (۽ چيڪ ڊجيٽ ٻيهر ڳڻپيل). هي واضح طور تي غلط آهي، پر اهي ائين ئي ڪندا آهن، تنهن ڪري اسان ان کي تبديل نه ڪيو. هڪ ٻيو ممڪن مسئلو جيڪو توهان کي منهن ڏئي سگهي ٿو، اهو آهي ته “isbn13” فيلڊ ۾ نقل آهن، تنهن ڪري توهان ان کي ڊيٽابيس ۾ پرائمري ڪي طور استعمال نٿا ڪري سگهو. “isbn13”+“isbn” فيلڊ گڏيل طور تي منفرد لڳن ٿا. ريليز 1 (2022-10-31) افسانوي torrents پوئتي آهن (جيتوڻيڪ IDs ~4-6M نه torrented آهن ڇو ته اهي اسان جي Zlib torrents سان اوورليپ ڪن ٿا). اسان جو بلاگ پوسٽ ڪامڪس بڪ جي رليز بابت Anna’s Archive تي ڪامڪس ٽورينٽ مختلف لائبريري جينيسس فورڪ جي پس منظر لاءِ، ڏسو صفحو <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li ۾ گهڻو ڪري ساڳيو مواد ۽ ميٽا ڊيٽا آهي جيئن Libgen.rs، پر هن ۾ ڪجهه مجموعا وڌيڪ آهن، يعني ڪامڪس، ميگزين، ۽ معياري دستاويز. هن ۾ پڻ <a %(a_scihub)s>Sci-Hub</a> کي پنهنجي ميٽا ڊيٽا ۽ ڳولا واري انجڻ ۾ ضم ڪيو آهي، جيڪو اسان پنهنجي ڊيٽابيس لاءِ استعمال ڪريون ٿا. هن لائبريري جي ميٽا ڊيٽا مفت ۾ موجود آهي <a %(a_libgen_li)s>libgen.li تي</a>. بهرحال، هي سرور سست آهي ۽ ٽٽل ڪنيڪشن کي ٻيهر شروع ڪرڻ جي حمايت نٿو ڪري. ساڳيون فائلون پڻ موجود آهن <a %(a_ftp)s>هڪ FTP سرور تي</a>، جيڪو بهتر ڪم ڪري ٿو. غير افسانوي مواد پڻ الڳ ٿي ويو آهي، پر بغير ڪنهن نئين ٽورنٽ جي. لڳي ٿو ته اهو 2022 جي شروعات کان وٺي ٿيو آهي، جيتوڻيڪ اسان ان جي تصديق نه ڪئي آهي. Libgen.li جي منتظم مطابق، "fiction_rus" (روسي افسانه) مجموعي کي <a %(a_booktracker)s>booktracker.org</a> کان باقاعده جاري ڪيل ٽورينٽن سان ڍڪيل هجڻ گهرجي، خاص طور تي <a %(a_flibusta)s>flibusta</a> ۽ <a %(a_librusec)s>lib.rus.ec</a> ٽورينٽ (جنهن کي اسان <a %(a_torrents)s>هتي</a> آئينو رکون ٿا، جيتوڻيڪ اسان اڃا تائين اهو طئي نه ڪيو آهي ته ڪهڙا ٽورينٽ ڪهڙن فائلن سان مطابقت رکن ٿا). افسانه مجموعي جا پنهنجا ٽورينٽ آهن (الڳ ٿيل <a %(a_href)s>Libgen.rs</a> کان) %(start)s تي شروع ٿين ٿا. ڪجهه حدون بغير ٽورينٽن جي (جهڙوڪ افسانه حدون f_3463000 کان f_4260000 تائين) شايد Z-Library (يا ٻيا نقل ٿيل) فائلون آهن، جيتوڻيڪ اسان کي شايد ڪجهه نقل ختم ڪرڻ ۽ انهن حدن ۾ lgli-منفرد فائلن لاءِ ٽورينٽ ٺاهڻ گهرجن. سڀني مجموعن جي شماريات <a %(a_href)s>libgen جي ويب سائيٽ تي</a> ملي سگهن ٿيون. اضافي مواد لاءِ ٽورينٽ موجود آهن، خاص طور تي ڪامڪس، ميگزين، ۽ معياري دستاويزن لاءِ ٽورينٽ انا جي آرڪائيو سان تعاون ۾ جاري ڪيا ويا آهن. نوٽ ڪريو ته ٽورينٽ فائلون جيڪي “libgen.is” ڏانهن اشارو ڪن ٿيون اهي واضح طور تي <a %(a_libgen)s>Libgen.rs</a> جا آئيني آهن (“.is” هڪ مختلف ڊومين آهي جيڪو Libgen.rs استعمال ڪري ٿو). ميٽا ڊيٽا استعمال ڪرڻ ۾ مددگار وسيلو <a %(a_href)s>هي صفحو</a> آهي. %(icon)s انهن جي "fiction_rus" مجموعي (روسي افسانه) لاءِ ڪو خاص ٽورينٽ نه آهي، پر اهو ٻين جي ٽورينٽن سان ڍڪيل آهي، ۽ اسان هڪ <a %(fiction_rus)s>آئينو</a> رکون ٿا. انا جي آرڪائيو تي روسي افسانه جا ٽورينٽ Anna’s Archive تي افسانوي ٽورينٽ بحث فورم ميٽا ڊيٽا FTP ذريعي ميٽا ڊيٽا Anna’s Archive تي ميگزين ٽورينٽ ميٽا ڊيٽا فيلڊ معلومات ٻين ٽورينٽ جا آئيني (۽ منفرد افسانوي ۽ ڪامڪس ٽورينٽ) انا جي آرڪائيو تي معياري دستاويزن جا ٽورينٽ Libgen.li Anna’s Archive پاران ٽورينٽس (ڪتاب جا ڪور) Library Genesis پنهنجي ڊيٽا کي ٽورينٽس ذريعي وڏي پيماني تي دستياب ڪرڻ لاءِ مشهور آهي. اسان جو Libgen مجموعو اضافي ڊيٽا تي مشتمل آهي جيڪو اهي سڌو سنئون جاري نٿا ڪن، انهن سان شراڪت ۾. Library Genesis سان ڪم ڪرڻ لاءِ سڀني جو تمام گهڻو شڪريو! اسان جو بلاگ ڪتابن جي ڪورز جي رليز بابت هي صفحو “.rs” ورزن بابت آهي. اهو پنهنجي ميٽا ڊيٽا ۽ پنهنجي ڪتابن جي فهرست جي مڪمل مواد کي مسلسل شايع ڪرڻ لاءِ مشهور آهي. ان جو ڪتابن جو مجموعو افسانوي ۽ غير افسانوي حصن ۾ ورهايل آهي. ميٽا ڊيٽا استعمال ڪرڻ ۾ مددگار وسيلو <a %(a_metadata)s>هي صفحو</a> آهي (IP رينجز کي بلاڪ ڪري ٿو، VPN جي ضرورت ٿي سگهي ٿي). 2024-03 تائين، نوان ٽورينٽس <a %(a_href)s>هن فورم ٿريڊ</a> ۾ پوسٽ ڪيا پيا وڃن (IP رينجز بلاڪ ڪري ٿو، VPN جي ضرورت ٿي سگهي ٿي). افسانوي ٽورينٽس Anna’s Archive تي Libgen.rs افسانوي ٽورينٽس Libgen.rs بحث فورم Libgen.rs Metadata Libgen.rs ميٽا ڊيٽا فيلڊ معلومات Libgen.rs غير افسانوي ٽورينٽس غير افسانوي ٽورينٽس Anna’s Archive تي %(example)s هڪ افسانوي ڪتاب لاءِ. هي <a %(blog_post)s>پهريون رليز</a> ڪافي ننڍو آهي: Libgen.rs فورڪ مان تقريباً 300GB ڪتابن جا ڪور، ٻنهي افسانوي ۽ غير افسانوي. اهي ساڳي طريقي سان ترتيب ڏنل آهن جيئن اهي libgen.rs تي ظاهر ٿين ٿا، مثال طور: %(example)s هڪ غير افسانوي ڪتاب لاءِ. بلڪل Z-Library مجموعي وانگر، اسان انهن سڀني کي هڪ وڏي .tar فائل ۾ رکيو آهي، جنهن کي <a %(a_ratarmount)s>ratarmount</a> استعمال ڪندي مائونٽ ڪري سگهجي ٿو جيڪڏهن توهان فائلن کي سڌو سنئون سرور ڪرڻ چاهيو ٿا. رليز 1 (%(date)s) Library Genesis (يا “Libgen”) جي مختلف فورڪز جي جلدي ڪهاڻي اها آهي ته وقت سان گڏ، Library Genesis سان لاڳاپيل مختلف ماڻهن ۾ اختلاف ٿي ويو، ۽ اهي پنهنجن الڳ رستن تي هليا ويا. هن <a %(a_mhut)s>فورم پوسٽ</a> مطابق، Libgen.li اصل ۾ “http://free-books.dontexist.com” تي ميزباني ڪئي وئي هئي. “.fun” ورزن اصل باني طرفان ٺاهيو ويو هو. اهو هڪ نئين، وڌيڪ ورهايل ورزن جي حق ۾ ٻيهر ترتيب ڏنو پيو وڃي. <a %(a_li)s>“.li” ورزن</a> وٽ ڪامڪس جو وڏو مجموعو آهي، گڏوگڏ ٻيو مواد، جيڪو (اڃا تائين) ٽورينٽ ذريعي بلڪ ڊائون لوڊ لاءِ موجود ناهي. ان وٽ افسانوي ڪتابن جو الڳ ٽورينٽ مجموعو آهي، ۽ ان جي ڊيٽابيس ۾ <a %(a_scihub)s>Sci-Hub</a> جو ميٽا ڊيٽا شامل آهي. “.rs” ورزن ۾ تمام گهڻو ساڳيو ڊيٽا آهي، ۽ سڀ کان وڌيڪ مسلسل پنهنجي مجموعي کي بلڪ ٽورينٽ ۾ جاري ڪري ٿو. اهو تقريباً “افسانوي” ۽ “غير افسانوي” سيڪشن ۾ ورهايل آهي. اصل ۾ “http://gen.lib.rus.ec” تي. <a %(a_zlib)s>Z-Library</a> ڪنهن حد تائين Library Genesis جو هڪ فورڪ پڻ آهي، جيتوڻيڪ انهن پنهنجي منصوبي لاءِ مختلف نالو استعمال ڪيو. Libgen.rs اسان پڻ پنهنجي مجموعي کي صرف ميٽا ڊيٽا ذريعن سان مالا مال ڪندا آهيون، جن کي اسان فائلن سان ملائي سگهون ٿا، مثال طور ISBN نمبر يا ٻين فيلڊن کي استعمال ڪندي. هيٺ انهن جو جائزو آهي. ٻيهر، انهن مان ڪجهه ذريعا مڪمل طور تي کليل آهن، جڏهن ته ٻين لاءِ اسان کي انهن کي اسڪراپ ڪرڻو پوندو. نوٽ ڪريو ته ميٽا ڊيٽا ڳولا ۾، اسان اصل رڪارڊ ڏيکاريندا آهيون. اسان رڪارڊن جو ڪو به مرجنگ نٿا ڪندا. صرف ميٽا ڊيٽا ذريعا Open Library هڪ اوپن سورس پروجيڪٽ آهي Internet Archive پاران هر ڪتاب کي دنيا ۾ ڪيٽلاگ ڪرڻ لاءِ. ان وٽ دنيا جي سڀ کان وڏي ڪتاب اسڪيننگ آپريشن مان هڪ آهي، ۽ ڪيترائي ڪتاب ڊجيٽل قرض ڏيڻ لاءِ دستياب آهن. ان جو ڪتاب ميٽا ڊيٽا ڪيٽلاگ ڊائون لوڊ لاءِ مفت ۾ دستياب آهي، ۽ Anna’s Archive تي شامل آهي (هاڻي تائين ڳولا ۾ نه، سواءِ جيڪڏهن توهان خاص طور تي Open Library ID لاءِ ڳولا ڪريو). Open Library نقل کي خارج ڪندي آخري اپڊيٽ فائلن جو تعداد جي سيڪڙو %% AA پاران mirrored / torrents موجود سائيز ذريعو هيٺ انا جي آرڪائيو تي فائلن جي ذريعن جو هڪ تڪڙو جائزو آهي. ڇاڪاڻ ته شيڊو لائبريريون اڪثر هڪ ٻئي کان ڊيٽا هم وقت سازي ڪن ٿيون، لائبريرين جي وچ ۾ ڪافي اوورليپ آهي. ان ڪري انگ اکر ڪل ۾ شامل نٿا ٿين. “Anna’s Archive پاران mirrored ۽ seeded” سيڪڙو ڏيکاري ٿو ته اسان ڪيتريون فائلون پاڻ mirror ڪريون ٿا. اسان اهي فائلون وڏي پيماني تي torrents ذريعي seed ڪريون ٿا، ۽ انهن کي سڌو ڊائون لوڊ لاءِ پارٽنر ويب سائيٽن ذريعي دستياب ڪريون ٿا. جائزو ڪل Anna’s Archive تي ٽورينٽس Sci-Hub بابت وڌيڪ ڄاڻ لاءِ، مهرباني ڪري ان جي <a %(a_scihub)s>سرڪاري ويب سائيٽ</a>, <a %(a_wikipedia)s>وڪيپيڊيا صفحو</a>, ۽ هي <a %(a_radiolab)s>پوڊ ڪاسٽ انٽرويو</a> ڏسو. نوٽ ڪريو ته Sci-Hub کي <a %(a_reddit)s>2021 کان منجمد ڪيو ويو آهي</a>. اهو اڳ ۾ به منجمد ڪيو ويو هو، پر 2021 ۾ ڪجهه ملين پيپر شامل ڪيا ويا. اڃا تائين، ڪجهه محدود تعداد ۾ پيپر Libgen جي “scimag” مجموعن ۾ شامل ڪيا وڃن ٿا، جيتوڻيڪ نئين بلڪ ٽورينٽس لاءِ ڪافي نه آهن. اسان Sci-Hub ميٽا ڊيٽا کي <a %(a_libgen_li)s>Libgen.li</a> جي “scimag” مجموعي ۾ مهيا ڪيل طور استعمال ڪندا آهيون. اسان پڻ <a %(a_dois)s>dois-2022-02-12.7z</a> ڊيٽا سيٽ استعمال ڪندا آهيون. نوٽ ڪريو ته “smarch” ٽورينٽس <a %(a_smarch)s>غير فعال</a> آهن ۽ تنهن ڪري اسان جي ٽورينٽس لسٽ ۾ شامل نه آهن. Libgen.li تي ٽورينٽس Libgen.rs تي ٽورينٽس ميٽا ڊيٽا ۽ ٽورينٽس Reddit تي تازه ڪاريون پوڊ ڪاسٽ انٽرويو وڪيپيڊيا صفحو Sci-Hub Sci-Hub: 2021 کان منجمند؛ گهڻو ڪري torrents ذريعي موجود Libgen.li: ان کان پوءِ ٿوريون اضافو</div> ڪجهه ذريعو لائبريريون پنهنجا ڊيٽا ٽورينٽس ذريعي وڏي پيماني تي شيئر ڪرڻ کي فروغ ڏين ٿيون، جڏهن ته ٻيا پنهنجو مجموعو آساني سان شيئر نٿا ڪن. اهڙي صورت ۾، Anna’s Archive انهن جي مجموعن کي اسڪراپ ڪرڻ جي ڪوشش ڪري ٿو، ۽ انهن کي دستياب ڪري ٿو (اسان جي <a %(a_torrents)s>Torrents</a> صفحي ڏسو). وچ ۾ حالتون به آهن، مثال طور، جتي ذريعو لائبريريون شيئر ڪرڻ لاءِ تيار آهن، پر انهن وٽ وسيلن جي کوٽ آهي. اهڙين حالتن ۾، اسان پڻ مدد ڪرڻ جي ڪوشش ڪندا آهيون. هيٺ مختلف ذريعو لائبريرين سان اسان جي انٽرفيس جو جائزو آهي. ذريعو لائبريريون %(icon)s چيني انٽرنيٽ تي پکڙيل مختلف فائل ڊيٽابيس؛ جيتوڻيڪ اڪثر ادا ڪيل ڊيٽابيس %(icon)s اڪثر فائلون صرف پريميئم BaiduYun اڪائونٽس استعمال ڪندي رسائي لائق آهن؛ سست ڊائون لوڊنگ رفتار. %(icon)s انا جي آرڪائيو <a %(duxiu)s>DuXiu فائلون</a> جو مجموعو منظم ڪري ٿو %(icon)s چيني انٽرنيٽ تي مختلف ميٽا ڊيٽا ڊيٽابيس منتشر آهن؛ جيتوڻيڪ اڪثر ادا ڪيل ڊيٽابيس %(icon)s انهن جي سڄي مجموعي لاءِ آساني سان رسائي لائق ميٽا ڊيٽا ڊمپ موجود نه آهن. %(icon)s انا جي آرڪائيو <a %(duxiu)s>DuXiu ميٽا ڊيٽا</a> جو مجموعو منظم ڪري ٿو فائلون %(icon)s فائلون صرف محدود بنيادن تي ادھار لاءِ موجود آهن، مختلف رسائي پابندين سان %(icon)s اينا جو آرڪائيو <a %(ia)s>IA فائلن</a> جو مجموعو منظم ڪري ٿو %(icon)s ڪجهه ميٽا ڊيٽا <a %(openlib)s>Open Library ڊيٽابيس ڊمپ</a> ذريعي موجود آهي، پر اهي سڄي IA مجموعي کي ڍڪيندا نه آهن %(icon)s انهن جي سڄي مجموعي لاءِ ڪو به آساني سان رسائي لائق ميٽا ڊيٽا ڊمپ موجود نه آهي %(icon)s انا جو آرڪائيو <a %(ia)s>IA ميٽا ڊيٽا</a> جو مجموعو منظم ڪري ٿو آخري اپڊيٽ %(icon)s انا جي آرڪائيو ۽ Libgen.li گڏجي <a %(comics)s>ڪامڪ ڪتابن</a>, <a %(magazines)s>ميگزين</a>, <a %(standarts)s>معياري دستاويزن</a>, ۽ <a %(fiction)s>افسانه (Libgen.rs کان الڳ ٿيل)</a> جي مجموعن کي منظم ڪن ٿا. %(icon)s غير افسانوي ٽورنٽ Libgen.rs سان شيئر ڪيا ويا آهن (۽ <a %(libgenli)s>هتي</a> عڪس ٿيل). %(icon)s هر ٽه ماهي <a %(dbdumps)s>HTTP ڊيٽابيس ڊمپ</a> %(icon)s <a %(nonfiction)s>غير افسانوي</a> ۽ <a %(fiction)s>افسانوي</a> لاءِ خودڪار ٽورنٽ %(icon)s انا جو آرڪائيو <a %(covers)s>ڪتاب جي ڪور ٽورنٽس</a> جو مجموعو منظم ڪري ٿو %(icon)s روزاني <a %(dbdumps)s>HTTP ڊيٽابيس ڊمپ</a> ميٽا ڊيٽا %(icon)s ماهوار <a %(dbdumps)s>ڊيٽابيس ڊمپ</a> %(icon)s ڊيٽا ٽورنٽ <a %(scihub1)s>هتي</a>, <a %(scihub2)s>هتي</a>, ۽ <a %(libgenli)s>هتي</a> موجود آهن %(icon)s ڪجهه نوان فائلون <a %(libgenrs)s>شامل</a> <a %(libgenli)s>ڪيون</a> پيون وڃن Libgen جي “scimag” ۾، پر نوان ٽورنٽ ٺاهڻ لاءِ ڪافي نه آهن %(icon)s Sci-Hub 2021 کان نيون فائلون منجمند ڪري ڇڏيون آهن. %(icon)s ميٽا ڊيٽا ڊمپ <a %(scihub1)s>هتي</a> ۽ <a %(scihub2)s>هتي</a> موجود آهن، ۽ <a %(libgenli)s>Libgen.li ڊيٽابيس</a> جو حصو پڻ (جنهن کي اسان استعمال ڪريون ٿا) ذريعو %(icon)s مختلف ننڍا يا هڪ دفعا استعمال ٿيندڙ ذريعا. اسان ماڻهن کي پهرين ٻين شيڊو لائبريرين ۾ اپلوڊ ڪرڻ جي ترغيب ڏيون ٿا، پر ڪڏهن ڪڏهن ماڻهن وٽ اهڙا مجموعا هوندا آهن جيڪي ٻين لاءِ ترتيب ڏيڻ لاءِ تمام وڏا هوندا آهن، جيتوڻيڪ ايترا وڏا نه هوندا آهن جو انهن جي پنهنجي ڪيٽيگري جي ضرورت هجي. %(icon)s سڌو سنئون وڏي مقدار ۾ دستياب نه آهي، اسڪريپنگ کان محفوظ ٿيل %(icon)s انا جي آرڪائيو <a %(worldcat)s>OCLC (WorldCat) ميٽا ڊيٽا</a> جو مجموعو منظم ڪري ٿو %(icon)s انا جو آرڪائيو ۽ Z-Library گڏجي <a %(metadata)s>Z-Library ميٽا ڊيٽا</a> ۽ <a %(files)s>Z-Library فائلون</a> جو مجموعو منظم ڪن ٿا Datasets اسان مٿي ڄاڻايل سڀني ذريعن کي هڪ متحد ڊيٽابيس ۾ گڏ ڪندا آهيون جيڪو اسان هن ويب سائيٽ کي خدمت ڪرڻ لاءِ استعمال ڪندا آهيون. هي متحد ڊيٽابيس سڌو سنئون دستياب ناهي، پر جيئن ته Anna’s Archive مڪمل طور تي اوپن سورس آهي، ان کي آساني سان <a %(a_generated)s>جنريٽ</a> يا <a %(a_downloaded)s>ڊائونلوڊ</a> ڪري سگھجي ٿو جيئن ElasticSearch ۽ MariaDB ڊيٽابيس. ان صفحي تي اسڪرپٽ پاڻمرادو مٿي ڄاڻايل ذريعن مان سڀ ضروري ميٽا ڊيٽا ڊائونلوڊ ڪندا. جيڪڏهن توهان مقامي طور تي اهي اسڪرپٽ هلائڻ کان اڳ اسان جي ڊيٽا کي ڳولڻ چاهيو ٿا، توهان اسان جي JSON فائلن کي ڏسي سگهو ٿا، جيڪي وڌيڪ ٻين JSON فائلن سان ڳنڍيل آهن. <a %(a_json)s>هي فائل</a> هڪ سٺو شروعاتي نقطو آهي. متحده ڊيٽابيس انا جي آرڪائيو پاران ٽورينٽس براؤز سرچ مختلف ننڍا يا هڪ دفعا استعمال ٿيندڙ ذريعا. اسان ماڻهن کي پهرين ٻين شيڊو لائبريرين ۾ اپلوڊ ڪرڻ جي ترغيب ڏيون ٿا، پر ڪڏهن ڪڏهن ماڻهن وٽ اهڙا مجموعا هوندا آهن جيڪي ٻين لاءِ ترتيب ڏيڻ لاءِ تمام وڏا هوندا آهن، جيتوڻيڪ ايترا وڏا نه هوندا آهن جو انهن جي پنهنجي ڪيٽيگري جي ضرورت هجي. <a %(a1)s>Datasets صفحو</a> تان جائزو. <a %(a_href)s>aaaaarg.fail</a> تان. لڳي ٿو ته ڪافي مڪمل آهي. اسان جي رضاڪار "cgiym" کان. <a %(a_href)s><q>ACM Digital Library 2020</q></a> ٽورينٽ تان. موجوده پيپرز ڪليڪشن سان ڪافي گهڻو اوورليپ آهي، پر تمام ٿورا MD5 ميچ آهن، تنهنڪري اسان فيصلو ڪيو ته ان کي مڪمل طور تي رکيو وڃي. <q>iRead eBooks</q> (آواز ۾ <q>ai rit i-books</q>; airitibooks.com) جو اسڪريپ، رضاڪار <q>j</q> پاران. <a %(a1)s><q>ٻيا metadata اسڪريپس</q></a> ۾ <q>airitibooks</q> metadata سان مطابقت رکي ٿو. هڪ مجموعي مان <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. حصو اصل ذريعن مان، حصو the-eye.eu مان، حصو ٻين آئيني سائيٽن مان. هڪ نجي ڪتابن جي ٽورينٽ ويب سائيٽ تان، <a %(a_href)s>Bibliotik</a> (جنهن کي اڪثر "Bib" چيو ويندو آهي)، جنهن جا ڪتاب نالي سان ٽورينٽس ۾ گڏ ڪيا ويا (A.torrent, B.torrent) ۽ the-eye.eu ذريعي ورهايا ويا. اسان جي رضاڪار "bpb9v" کان. <a %(a_href)s>CADAL</a> بابت وڌيڪ معلومات لاءِ، اسان جي <a %(a_duxiu)s>DuXiu dataset page</a> ۾ نوٽس ڏسو. اسان جي رضاڪار "bpb9v" کان وڌيڪ، اڪثر DuXiu فائلون، ۽ هڪ فولڊر "WenQu" ۽ "SuperStar_Journals" (SuperStar DuXiu جي پويان ڪمپني آهي). اسان جي رضاڪار "cgiym" کان، مختلف ذريعن مان چيني متن (سب ڊائريڪٽريز جي طور تي نمائندگي ٿيل)، جنهن ۾ <a %(a_href)s>China Machine Press</a> (هڪ وڏو چيني پبلشر) شامل آهي. اسان جي رضاڪار "cgiym" کان غير چيني ڪليڪشن (سب ڊائريڪٽريز جي طور تي نمائندگي ٿيل). چيني آرڪيٽيڪچر بابت ڪتابن جو اسڪريپ، رضاڪار <q>cm</q> پاران: <q>مون اهو پبلشنگ هائوس ۾ نيٽورڪ جي ڪمزوري جو فائدو وٺي حاصل ڪيو، پر اهو سوراخ هاڻي بند ٿي چڪو آهي</q>. <a %(a1)s><q>ٻيا metadata اسڪريپس</q></a> ۾ <q>chinese_architecture</q> metadata سان مطابقت رکي ٿو. علمي پبلشنگ هائوس <a %(a_href)s>De Gruyter</a> جا ڪتاب، ڪجهه وڏن ٽورينٽس مان گڏ ڪيا ويا. <a %(a_href)s>docer.pl</a> جو اسڪريپ، هڪ پولش فائل شيئرنگ ويب سائيٽ جيڪا ڪتابن ۽ ٻين لکيل ڪم تي ڌيان ڏئي ٿي. 2023 جي آخر ۾ رضاڪار "p" پاران اسڪريپ ڪيو ويو. اسان وٽ اصل ويب سائيٽ مان سٺا ميٽا ڊيٽا نه آهن (فائل ايڪسٽينشنز به نه)، پر اسان ڪتابن جهڙين فائلن لاءِ فلٽر ڪيو ۽ اڪثر فائلن مان ميٽا ڊيٽا ڪڍڻ ۾ ڪامياب ٿياسين. DuXiu epubs، سڌو DuXiu تان، رضاڪار "w" پاران گڏ ڪيا ويا. صرف تازا DuXiu ڪتاب سڌو سنئون اي بڪ ذريعي موجود آهن، تنهنڪري انهن مان اڪثر تازا هجڻ گهرجن. باقي DuXiu فائلون رضاڪار "m" کان، جيڪي DuXiu جي مالڪي PDG فارميٽ ۾ نه هيون (مکيه <a %(a_href)s>DuXiu dataset</a>). ڪيترن ئي اصل ذريعن مان گڏ ڪيا ويا، بدقسمتي سان انهن ذريعن کي فائل پٿ ۾ محفوظ ڪرڻ کان سواءِ. <span></span> <span></span> <span></span> شهوت انگيز ڪتابن جو اسڪريپ، رضاڪار <q>do no harm</q> پاران. <a %(a1)s><q>ٻيا metadata اسڪريپس</q></a> ۾ <q>hentai</q> metadata سان مطابقت رکي ٿو. <span></span> <span></span> هڪ جاپاني منگا پبلشر کان گڏ ڪيل ڪليڪشن، رضاڪار "t" پاران. <a %(a_href)s>لونگڪوان جي چونڊيل عدالتي آرڪائيوز</a>، رضاڪار "c" پاران مهيا ڪيل. <a %(a_href)s>magzdb.org</a> جو اسڪريپ، جيڪو Library Genesis جو اتحادي آهي (اهو libgen.rs هوم پيج تي ڳنڍيل آهي) پر جن پنهنجون فائلون سڌو سنئون مهيا ڪرڻ نٿا چاهين. 2023 جي آخر ۾ رضاڪار "p" پاران حاصل ڪيو ويو. <span></span> مختلف ننڍا اپلوڊ، جيڪي پنهنجي سب ڪليڪشن جي طور تي تمام ننڍا آهن، پر ڊائريڪٽريز جي طور تي نمائندگي ٿيل آهن. AvaxHome مان اي بڪس، هڪ روسي فائل شيئرنگ ويب سائيٽ. اخبارن ۽ رسالن جو آرڪائيو. <a %(a1)s><q>ٻيا metadata اسڪريپس</q></a> ۾ <q>newsarch_magz</q> metadata سان مطابقت رکي ٿو. <a %(a1)s>Philosophy Documentation Center</a> جو اسڪريپ. رضاڪاري "o" جي ڪليڪشن جنهن پولش ڪتابن کي سڌو اصل رليز ("سين") ويب سائيٽن تان گڏ ڪيو. <a %(a_href)s>shuge.org</a> جون گڏيل ڪليڪشنون، رضاڪارن "cgiym" ۽ "woz9ts" پاران. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (افسانوي لائبريري جي نالي تي)، 2022 ۾ رضاڪار "t" پاران اسڪريپ ڪيو ويو. <span></span> <span></span> <span></span> رضاڪاري "woz9ts" کان سب-سب-ڪليڪشن (ڊائريڪٽريز جي طور تي نمائندگي ٿيل): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (by <a %(a_sikuquanshu)s>Dizhi(迪志)</a> تائيوان ۾), mebook (mebook.cc, 我的小书屋, منهنجو ننڍڙو ڪتاب خانو — woz9ts: "هي سائيٽ خاص طور تي اعلي معيار جي اي بڪ فائلن کي شيئر ڪرڻ تي ڌيان ڏئي ٿي، جن مان ڪجهه مالڪ پاڻ ٽائيپ سيٽ ڪيا آهن. مالڪ کي 2019 ۾ <a %(a_arrested)s>گرفتار</a> ڪيو ويو ۽ ڪنهن انهن فائلن جو مجموعو ٺاهيو جيڪي هن شيئر ڪيون هيون."). رضاکار "woz9ts" جا باقي بچيل DuXiu فائل، جيڪي DuXiu جي مالڪي PDG فارميٽ ۾ نه هئا (اڃا تائين PDF ۾ تبديل ٿيڻا آهن). "اپلوڊ" مجموعو ننڍن ذيلي مجموعن ۾ ورهايل آهي، جيڪي AACIDs ۽ ٽورينٽ نالن ۾ ظاهر ڪيا ويا آهن. سڀ ذيلي مجموعا پهرين مکيه مجموعي جي خلاف ڊپليڪيٽ ڪيا ويا، جيتوڻيڪ ميٽا ڊيٽا "upload_records" JSON فائلون اڃا تائين اصل فائلن ڏانهن ڪيترن ئي حوالن تي مشتمل آهن. غير ڪتابي فائلون پڻ اڪثر ذيلي مجموعن مان هٽايون ويون، ۽ عام طور تي <em>نه</em> "upload_records" JSON ۾ نوٽ ڪيون ويون. ذيلي مجموعا آهن: نوٽس ذيلي مجموعو ڪيتريون ئي ذيلي مجموعا پاڻ ۾ ذيلي-ذيلي مجموعن تي مشتمل آهن (مثال طور مختلف اصل ذريعن کان)، جيڪي "filepath" فيلڊز ۾ ڊائريڪٽريز طور ظاهر ڪيا ويا آهن. انا جي آرڪائيو ۾ اپلوڊس اسان جي هن ڊيٽا بابت بلاگ پوسٽ <a %(a_worldcat)s>WorldCat</a> هڪ غير منافع بخش <a %(a_oclc)s>OCLC</a> پاران هڪ مالڪي ڊيٽابيس آهي، جيڪو سڄي دنيا جي لائبريرين مان ميٽا ڊيٽا رڪارڊ گڏ ڪري ٿو. اهو شايد دنيا ۾ سڀ کان وڏو لائبريري ميٽا ڊيٽا مجموعو آهي. آڪٽوبر 2023 ۾ اسان <a %(a_scrape)s>جاري ڪيو</a> OCLC (WorldCat) ڊيٽابيس جو هڪ جامع اسڪراپ، <a %(a_aac)s>Anna’s Archive Containers فارميٽ</a> ۾. آڪٽوبر 2023، شروعاتي رليز: OCLC (WorldCat) Anna’s Archive پاران ٽورينٽس انا جي آرڪائيو تي مثال رڪارڊ (اصل مجموعو) انا جي آرڪائيو تي مثال رڪارڊ ("zlib3" مجموعو) انا جي آرڪائيو پاران ٽورينٽس (ميٽا ڊيٽا + مواد) رليز 1 بابت بلاگ پوسٽ رليز 2 بابت بلاگ پوسٽ 2022 جي آخر ۾، Z-لائبريري جي مبينه باني کي گرفتار ڪيو ويو، ۽ ڊومينز کي آمريڪي اختيارين طرفان ضبط ڪيو ويو. ان کان پوءِ ويب سائيٽ آهستي آهستي ٻيهر آن لائن ٿي رهي آهي. اهو معلوم ناهي ته هن وقت ان کي ڪير هلائي رهيو آهي. فيبروري 2023 جي تازه ڪاري. Z-لائبريري جي جڙون <a %(a_href)s>Library Genesis</a> ڪميونٽي ۾ آهن، ۽ اصل ۾ انهن جي ڊيٽا سان بوٽ اسٽرپ ڪئي وئي هئي. ان کان پوءِ، ان کي ڪافي حد تائين پروفيشنل بڻايو ويو آهي، ۽ هاڻي ان جو انٽرفيس گهڻو جديد آهي. تنهن ڪري، اهي پنهنجي ويب سائيٽ کي بهتر بڻائڻ لاءِ مالي طور تي ۽ نون ڪتابن جي عطين جي صورت ۾ گهڻو وڌيڪ عطيا حاصل ڪرڻ جي قابل آهن. انهن Library Genesis کان علاوه هڪ وڏي مجموعي کي گڏ ڪيو آهي. مجموعو ٽن حصن تي مشتمل آهي. پهرين ٻن حصن لاءِ اصل وضاحت صفحا هيٺ محفوظ ڪيا ويا آهن. توهان کي سڀني ڊيٽا حاصل ڪرڻ لاءِ ٽنهي حصن جي ضرورت آهي (سواءِ انهن ٽورينٽس جي جيڪي ٽورينٽس صفحي تي ڪراس ڪيا ويا آهن). %(title)s: اسان جي پهرين رليز. هي پهرين رليز هئي جنهن کي "Pirate Library Mirror" ("pilimi") سڏيو ويو. %(title)s: ٻي رليز، هن ڀيري سڀ فائلون .tar فائلن ۾ لپيٽيل. %(title)s: نيون اضافي رليز، <a %(a_href)s>انا جي آرڪائيو ڪنٽينرز (AAC) فارميٽ</a> استعمال ڪندي، هاڻي Z-لائبريري ٽيم سان تعاون ۾ جاري ڪيون ويون. شروعاتي آئيني کي 2021 ۽ 2022 دوران محنت سان حاصل ڪيو ويو. هن وقت اهو ٿورو پراڻو آهي: اهو جون 2021 ۾ مجموعي جي حالت کي ظاهر ڪري ٿو. اسان مستقبل ۾ ان کي اپڊيٽ ڪنداسين. هن وقت اسان هن پهرين رليز کي جاري ڪرڻ تي ڌيان ڏئي رهيا آهيون. Library Genesis اڳ ئي عوامي ٽورنٽس سان محفوظ ٿيل آهي، ۽ Z-Library ۾ شامل آهي، اسان جون 2022 ۾ Library Genesis خلاف بنيادي ڊپليڪيشن ڪئي. ان لاءِ اسان MD5 هاشز استعمال ڪيا. لائبريري ۾ شايد گهڻو وڌيڪ نقل مواد موجود آهي، جهڙوڪ هڪ ئي ڪتاب جا مختلف فائل فارميٽس. اهو صحيح طريقي سان ڳولڻ مشڪل آهي، تنهنڪري اسان نٿا ڪريون. ڊپليڪيشن کان پوءِ اسان وٽ 2 ملين کان وڌيڪ فائلون بچيون آهن، جيڪي تقريباً 7TB آهن. مجموعو ٻن حصن تي مشتمل آهي: ميٽا ڊيٽا جو MySQL “.sql.gz” ڊمپ، ۽ 72 ٽورنٽ فائلون جيڪي هر هڪ تقريباً 50-100GB آهن. ميٽا ڊيٽا ۾ Z-Library ويب سائيٽ پاران رپورٽ ڪيل ڊيٽا (عنوان، ليکڪ، وضاحت، فائل ٽائپ) شامل آهي، گڏوگڏ اصل فائل سائيز ۽ md5sum جيڪي اسان مشاهدو ڪيا، ڇاڪاڻ ته ڪڏهن ڪڏهن اهي متفق ناهن. لڳي ٿو ته فائلن جا سلسلا آهن جن لاءِ Z-Library پاڻ غلط ميٽا ڊيٽا آهي. اسان شايد ڪجهه الڳ ڪيسن ۾ غلط فائلون ڊائون لوڊ ڪيون آهن، جن کي اسان مستقبل ۾ ڳولڻ ۽ درست ڪرڻ جي ڪوشش ڪنداسين. وڏيون ٽورنٽ فائلون اصل ڪتاب جو ڊيٽا شامل ڪن ٿيون، Z-Library ID سان فائل نالي طور. فائل جي توسيع کي ميٽا ڊيٽا ڊمپ استعمال ڪندي ٻيهر تعمير ڪري سگهجي ٿو. مجموعو غير افسانوي ۽ افسانوي مواد جو ميلاپ آهي (Library Genesis ۾ الڳ نه ڪيو ويو آهي). معيار پڻ وڏي پيماني تي مختلف آهي. هي پهريون رليز هاڻي مڪمل طور تي دستياب آهي. نوٽ ڪريو ته ٽورنٽ فائلون صرف اسان جي ٽور آئيني ذريعي دستياب آهن. رليز 1 (%(date)s) هي هڪ اضافي ٽورنٽ فائل آهي. ان ۾ ڪا نئين معلومات شامل ناهي، پر ان ۾ ڪجهه ڊيٽا آهي جيڪا حساب ڪرڻ ۾ وقت وٺي سگهي ٿي. اهو ان کي آسان بڻائي ٿو، ڇاڪاڻ ته هي ٽورنٽ ڊائون لوڊ ڪرڻ اڪثر ڪري ان کي شروع کان حساب ڪرڻ کان تيز آهي. خاص طور تي، ان ۾ ٽار فائلن لاءِ SQLite انڊيڪس شامل آهن، استعمال لاءِ <a %(a_href)s>ratarmount</a> سان. رليز 2 اضافو (%(date)s) اسان سڀ ڪتاب حاصل ڪيا آهن جيڪي اسان جي آخري آئيني ۽ آگسٽ 2022 جي وچ ۾ Z-Library ۾ شامل ڪيا ويا هئا. اسان پڻ واپس وڃي ڪجهه ڪتابن کي اسڪراپ ڪيو جيڪي اسان پهريون ڀيرو چوڌاري وڃائي ڇڏيا. مجموعي طور تي، هي نئون مجموعو تقريباً 24TB آهي. ٻيهر، هي مجموعو Library Genesis خلاف ڊپليڪيٽ ٿيل آهي، ڇاڪاڻ ته ان مجموعي لاءِ اڳ ۾ ئي ٽورنٽس موجود آهن. ڊيٽا پهرين رليز وانگر ترتيب ڏنل آهي. ميٽا ڊيٽا جو MySQL “.sql.gz” ڊمپ آهي، جنهن ۾ پهرين رليز جي سڀني ميٽا ڊيٽا شامل آهي، تنهنڪري اهو ان کي سپرسيڊ ڪري ٿو. اسان پڻ ڪجهه نوان ڪالمن شامل ڪيا: اسان اهو آخري ڀيرو ذڪر ڪيو، پر صرف وضاحت ڪرڻ لاءِ: “filename” ۽ “md5” فائل جا اصل خاصيتون آهن، جڏهن ته “filename_reported” ۽ “md5_reported” اهي آهن جيڪي اسان Z-Library مان اسڪراپ ڪيا. ڪڏهن ڪڏهن اهي ٻئي هڪ ٻئي سان متفق ناهن، تنهنڪري اسان ٻنهي کي شامل ڪيو. هن رليز لاءِ، اسان ڪوليشن کي “utf8mb4_unicode_ci” ۾ تبديل ڪيو، جيڪو MySQL جي پراڻين ورزن سان مطابقت رکندڙ هجڻ گهرجي. ڊيٽا فائلون آخري ڀيري وانگر آهن، جيتوڻيڪ اهي تمام وڏيون آهن. اسان کي تمام گهڻيون ننڍيون ٽورنٽ فائلون ٺاهڻ جي تڪليف نه ٿي. “pilimi-zlib2-0-14679999-extra.torrent” ۾ سڀ فائلون شامل آهن جيڪي اسان آخري رليز ۾ وڃائي ڇڏيون، جڏهن ته ٻيا ٽورنٽ سڀ نوان ID رينجز آهن.  <strong>تازه ڪاري %(date)s:</strong> اسان جا گهڻا ٽورنٽ تمام وڏا ٺاهيا، جنهن سبب ٽورنٽ ڪلائنٽس کي مشڪلات ٿي. اسان انهن کي هٽائي ڇڏيو ۽ نوان ٽورنٽ جاري ڪيا. <strong>تازه ڪاري %(date)s:</strong> اڃا به تمام گهڻيون فائلون هيون، تنهنڪري اسان انهن کي ٽار فائلن ۾ لپيٽيو ۽ ٻيهر نوان ٽورنٽ جاري ڪيا. %(key)s: ڇا هي فائل اڳ ۾ ئي Library Genesis ۾ آهي، يا ته غير افسانوي يا افسانوي مجموعي ۾ (md5 سان ملندڙ). %(key)s: ڪهڙي ٽورنٽ ۾ هي فائل آهي. %(key)s: جڏهن اسان ڪتاب ڊائون لوڊ ڪرڻ ۾ ناڪام ٿياسين. رليز 2 (%(date)s) Zlib رليز (اصل وضاحت صفحا) Tor ڊومين مکيه ويب سائيٽ Z-لائبريري اسڪريپ Z-Library ۾ "چيني" مجموعو اسان جي DuXiu مجموعي سان ساڳيو لڳي ٿو، پر مختلف MD5s سان. اسان نقل کان بچڻ لاءِ انهن فائلن کي ٽورنٽس مان خارج ڪريون ٿا، پر اڃا تائين انهن کي اسان جي ڳولا انڊيڪس ۾ ڏيکاريون ٿا. ميٽا ڊيٽا توهان کي %(percentage)s%% بونس تيز ڊائون لوڊ ملن ٿا، ڇو ته توهان کي صارف %(profile_link)s پاران حوالو ڏنو ويو آهي. اهو سڄي ميمبرشپ جي مدت تي لاڳو ٿئي ٿو. عطيو ڪريو شامل ٿيو منتخب ٿيل وڌ ۾ وڌ %(percentage)s%% رعايتون علي پے بين الاقوامي ڪريڊٽ/ڊيبٽ ڪارڊن جي حمايت ڪري ٿو. وڌيڪ معلومات لاءِ <a %(a_alipay)s>هن گائيڊ</a> کي ڏسو. اسان کي Amazon.com گفٽ ڪارڊ موڪليو پنهنجي ڪريڊٽ/ڊيبٽ ڪارڊ استعمال ڪندي. توهان ڪريڊٽ/ڊيبٽ ڪارڊن کي استعمال ڪندي crypto خريد ڪري سگهو ٿا. WeChat (Weixin Pay) بين الاقوامي ڪريڊٽ/ڊيبٽ ڪارڊن کي سپورٽ ڪري ٿو. WeChat ايپ ۾، "Me => Services => Wallet => Add a Card" ڏانهن وڃو. جيڪڏهن توهان کي اهو نظر نٿو اچي، ته "Me => Settings => General => Tools => Weixin Pay => Enable" استعمال ڪندي ان کي فعال ڪريو. (جڏهن Coinbase مان Ethereum موڪليندي استعمال ڪريو) ڪاپي ٿي ويو! ڪاپي (گهٽ ۾ گهٽ رقم) (خبردار: وڌيڪ ۾ وڌيڪ رقم) -%(percentage)s%% 12 مهينا 1 مهينو 24 مهينا 3 مهينا 48 مهينا 6 مهينا 96 مهينا چونڊيو ته توهان ڪيتري دير لاءِ سبسڪرائب ڪرڻ چاهيو ٿا. <div %(div_monthly_cost)s></div><div %(div_after)s>رعايتن کان پوءِ <span %(span_discount)s></span></div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 مهينن لاءِ 1 مهيني لاءِ 24 مهينن لاءِ 3 مهينن لاءِ 48 مهينن لاءِ 6 مهينن لاءِ 96 مهينن لاءِ %(monthly_cost)s / مهينو اسان سان رابطو ڪريو سڌو <strong>SFTP</strong> سرور نئين مجموعن لاءِ نئين اسڪين، OCR’ed Datasets وغيره جي بدلي ۾ ڪاروباري سطح جي عطيا. ماهر رسائي <strong>لامحدود</strong> تيز رفتار رسائي <div %(div_question)s>ڇا مان پنهنجي ميمبرشپ کي اپگريڊ ڪري سگهان ٿو يا گهڻيون ميمبرشپس حاصل ڪري سگهان ٿو؟</div> <div %(div_question)s>ڇا مان ميمبر بڻجي بغير عطيو ڏئي سگهان ٿو؟</div> بلڪل. اسان ڪنهن به رقم جا عطيا هن Monero (XMR) پتي تي قبول ڪندا آهيون: %(address)s. <div %(div_question)s>ماھوار حدون ڇا مطلب رکن ٿيون؟</div> توهان سڀني رعايتن کي لاڳو ڪندي حد جي هيٺين پاسي تائين پهچي سگهو ٿا، جهڙوڪ هڪ مهيني کان وڌيڪ عرصو چونڊڻ. <div %(div_question)s>ڇا ميمبرشپ پاڻمرادو تجديد ٿيندي آهي؟</div> ميمبرشپ <strong>پاڻمرادو</strong> تجديد نٿي ٿئي. توهان جيترو وقت چاهيو شامل ٿي سگهو ٿا. <div %(div_question)s>توهان عطين تي ڇا خرچ ڪندا آهيو؟</div> 100%% دنيا جي علم ۽ ثقافت کي محفوظ ڪرڻ ۽ قابل رسائي بڻائڻ تي خرچ ٿيندو آهي. في الحال اسان ان کي گهڻو ڪري سرورز، اسٽوريج، ۽ بينڊوڊٿ تي خرچ ڪندا آهيون. ڪو به پئسو ڪنهن به ٽيم ميمبر کي ذاتي طور تي نٿو وڃي. <div %(div_question)s>ڇا مان وڏي رقم جو عطيو ڏئي سگهان ٿو؟</div> اهو شاندار هوندو! ڪجهه هزار ڊالرن کان وڌيڪ عطين لاءِ، مهرباني ڪري سڌو سنئون اسان سان %(email)s تي رابطو ڪريو. <div %(div_question)s>ڇا توهان وٽ ٻيا ادائيگي جا طريقا آهن؟</div> في الحال نه. ڪيترائي ماڻهو نٿا چاهين ته اهڙا آرڪائيوز موجود هجن، تنهن ڪري اسان کي محتاط رهڻو پوندو. جيڪڏهن توهان اسان کي ٻين (وڌيڪ آسان) ادائيگي جا طريقا محفوظ طريقي سان سيٽ اپ ڪرڻ ۾ مدد ڪري سگهو ٿا، مهرباني ڪري %(email)s تي اسان سان رابطو ڪريو. عطيا FAQ توهان وٽ هڪ <a %(a_donation)s>موجوده عطيو</a> جاري آهي. مهرباني ڪري نئون عطيو ڪرڻ کان اڳ ان عطئي کي مڪمل يا منسوخ ڪريو. <a %(a_all_donations)s>منهنجا سڀ عطيا ڏسو</a> $5000 کان وڌيڪ عطيا لاءِ مهرباني ڪري سڌو اسان سان رابطو ڪريو %(email)s. اسان امير فردن يا ادارن کان وڏيون عطيا ڀليڪار ڪيون ٿا.  مهرباني ڪري ڄاڻو ته جڏهن ته هن صفحي تي ميمبرشپ "في مهينو" آهن، اهي هڪ ڀيرو ڏيڻ واريون آهن (غير ورجائيندڙ). ڏسو <a %(faq)s>عطيا FAQ</a>. Anna’s Archive هڪ غير منافع بخش، اوپن سورس، اوپن ڊيٽا پروجيڪٽ آهي. عطيو ڏيڻ ۽ ميمبر بڻجڻ سان، توهان اسان جي آپريشن ۽ ترقي جي مدد ڪندا آهيو. سڀني ميمبرن کي: توهان جي مهرباني جو اسان کي جاري رکڻ ۾ مدد ڪئي! ❤️ وڌيڪ معلومات لاءِ، <a %(a_donate)s>عطيو FAQ</a> ڏسو. ممبر ٿيڻ لاءِ، مهرباني ڪري <a %(a_login)s>لاگ ان يا رجسٽر ٿيو</a>. توهان جي حمايت لاءِ مهرباني! $%(cost)s / مهينو جيڪڏهن توهان ادائيگي دوران ڪا غلطي ڪئي، اسان واپسي نٿا ڪري سگهون، پر اسان ان کي درست ڪرڻ جي ڪوشش ڪنداسين. پنهنجي پے پال ايپ يا ويب سائيٽ ۾ "ڪرپٽو" صفحو ڳوليو. اهو عام طور تي "ماليات" جي تحت هوندو آهي. پنهنجي PayPal ايپ يا ويب سائيٽ ۾ "Bitcoin" صفحي تي وڃو. "Transfer" بٽڻ %(transfer_icon)s دٻايو، ۽ پوءِ "Send" دٻايو. علي پے Alipay 支付宝 / WeChat 微信 ايميزون گفٽ ڪارڊ %(amazon)s تحفي تحفي بينڪ ڪارڊ بينڪ ڪارڊ (ايپ استعمال ڪندي) بائنانس ڪريڊٽ/ڊيبٽ/ايپل/گوگل (BMC) ڪيش ايپ ڪريڊٽ/ڊيبٽ ڪارڊ ڪريڊٽ/ڊيبٽ ڪارڊ 2 ڪريڊٽ/ڊيبٽ ڪارڊ (بيڪ اپ) ڪرپٽو %(bitcoin_icon)s ڪارڊ / پے پال / وينمو پي پال (آمريڪا) %(bitcoin_icon)s پي پال پے پال (عام) Pix (Brazil) ريوولٽ (عارضي طور تي دستياب ناهي) وي چيٽ پنهنجي پسنديده ڪرپٽو ڪوائن چونڊيو: Amazon گفٽ ڪارڊ استعمال ڪندي عطيو ڪريو. <strong>اهم: </strong> هي آپشن %(amazon)s لاءِ آهي. جيڪڏهن توهان ٻئي Amazon ويب سائيٽ استعمال ڪرڻ چاهيو ٿا، ته مٿي چونڊيو. <strong>اهم: </strong> اسان صرف Amazon.com کي سپورٽ ڪريون ٿا، ٻيا Amazon ويب سائيٽون نه. مثال طور، .de، .co.uk، .ca، سپورٽ ٿيل نه آهن. مھرباني ڪري پنهنجو پيغام نه لکو. صحيح رقم داخل ڪريو: %(amount)s نوٽ ڪريو ته اسان کي پنهنجي ري سيلرز طرفان قبول ڪيل رقم ڏانهن گول ڪرڻو پوندو (گهٽ ۾ گهٽ %(minimum)s). ڪريڊٽ/ڊيبٽ ڪارڊ استعمال ڪندي عطيو ڪريو، Alipay ايپ ذريعي (سيٽ اپ ڪرڻ ۾ تمام آسان). <a %(a_app_store)s>ايپل ايپ اسٽور</a> يا <a %(a_play_store)s>گوگل پلي اسٽور</a> تان Alipay ايپ انسٽال ڪريو. پنهنجي فون نمبر سان رجسٽر ڪريو. وڌيڪ ذاتي تفصيل گهربل نه آهن. <span %(style)s>1</span>Alipay ايپ انسٽال ڪريو سپورٽ ٿيل: ويزا، ماسٽر ڪارڊ، JCB، ڊائنرز ڪلب ۽ ڊسڪور. وڌيڪ معلومات لاءِ <a %(a_alipay)s>هي گائيڊ</a> ڏسو. <span %(style)s>2</span>بينڪ ڪارڊ شامل ڪريو Binance سان، توهان ڪريڊٽ/ڊيبٽ ڪارڊ يا بئنڪ اڪائونٽ سان Bitcoin خريد ڪري سگهو ٿا، ۽ پوءِ اهو Bitcoin اسان کي عطيو ڪري سگهو ٿا. اهڙي طرح اسان توهان جي عطئي کي قبول ڪرڻ وقت محفوظ ۽ گمنام رهي سگهون ٿا. Binance تقريباً هر ملڪ ۾ موجود آهي، ۽ اڪثر بئنڪن ۽ ڪريڊٽ/ڊيبٽ ڪارڊن کي سپورٽ ڪري ٿو. هي هن وقت اسان جي مکيه سفارش آهي. اسان توهان جي وقت جي قدر ڪريون ٿا جيڪو توهان هن طريقي سان عطيو ڪرڻ سکڻ ۾ لڳايو، ڇو ته اهو اسان جي مدد ڪري ٿو. ڪريڊٽ ڪارڊ، ڊيبٽ ڪارڊ، Apple Pay، ۽ Google Pay لاءِ، اسان "Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) استعمال ڪريون ٿا. انهن جي سسٽم ۾، هڪ "ڪافي" $5 جي برابر آهي، تنهن ڪري توهان جو عطيو 5 جي ويجھي گھڻا ڏانهن گول ڪيو ويندو. ڪيش ايپ استعمال ڪندي عطيو ڪريو. جيڪڏهن توهان وٽ ڪيش ايپ آهي، ته هي عطيو ڪرڻ جو آسان طريقو آهي! نوٽ ڪريو ته %(amount)s کان گهٽ ٽرانزيڪشن لاءِ، ڪيش ايپ شايد %(fee)s فيس چارج ڪري. %(amount)s يا ان کان وڌيڪ لاءِ، اهو مفت آهي! ڪريڊٽ يا ڊيبٽ ڪارڊ سان عطيو ڪريو. هي طريقو هڪ cryptocurrency فراهم ڪندڙ کي وچولي تبديلي طور استعمال ڪري ٿو. هي ٿورو پيچيده ٿي سگهي ٿو، تنهن ڪري مهرباني ڪري صرف هن طريقي کي استعمال ڪريو جيڪڏهن ٻيا ادائيگي جا طريقا ڪم نٿا ڪن. اهو پڻ سڀني ملڪن ۾ ڪم نٿو ڪري. اسان سڌو سنئون ڪريڊٽ/ڊيبٽ ڪارڊن جي حمايت نٿا ڪري سگهون، ڇو ته بئنڪون اسان سان ڪم ڪرڻ نٿيون چاهين. ☹ بهرحال، ڪريڊٽ/ڊيبٽ ڪارڊ استعمال ڪرڻ جا ڪيترائي طريقا آهن، ٻين ادائيگي جي طريقن کي استعمال ڪندي: ڪرپٽو سان توهان BTC، ETH، XMR، ۽ SOL استعمال ڪندي عطيو ڪري سگهو ٿا. هي آپشن استعمال ڪريو جيڪڏهن توهان اڳ ۾ ئي ڪرپٽو ڪرنسي سان واقف آهيو. ڪرپٽو سان توهان BTC، ETH، XMR، ۽ وڌيڪ استعمال ڪندي عطيو ڪري سگهو ٿا. ڪرپٽو ايڪسپريس خدمتون جيڪڏهن توهان پهريون ڀيرو ڪرپٽو استعمال ڪري رهيا آهيو، اسان صلاح ڏيون ٿا ته %(options)s استعمال ڪريو Bitcoin (اصل ۽ سڀ کان وڌيڪ استعمال ٿيندڙ ڪرپٽو ڪرنسي) خريد ڪرڻ ۽ عطيو ڪرڻ لاءِ۔ نوٽ ڪريو ته ننڍن عطين لاءِ ڪريڊٽ ڪارڊ فيس اسان جي %(discount)s%% رعايت کي ختم ڪري سگهن ٿيون، تنهنڪري اسان ڊگهي سبسڪرپشن جي سفارش ڪريون ٿا. ڪريڊٽ/ڊيبٽ ڪارڊ، PayPal، يا Venmo استعمال ڪندي عطيو ڪريو. توهان ايندڙ صفحي تي انهن مان چونڊ ڪري سگهو ٿا. گوگل پي ۽ ايپل پي شايد پڻ ڪم ڪن. نوٽ ڪريو ته ننڍن عطين لاءِ فيس وڌيڪ آهن، تنهنڪري اسان ڊگهي سبسڪرپشن جي سفارش ڪريون ٿا. PayPal US استعمال ڪندي عطيو ڪرڻ لاءِ، اسان PayPal Crypto استعمال ڪرڻ وارا آهيون، جيڪو اسان کي گمنام رهڻ جي اجازت ڏئي ٿو. اسان توهان جي وقت جي تعريف ڪريون ٿا ته توهان هن طريقي سان عطيو ڪرڻ بابت سکڻ لاءِ، ڇو ته اهو اسان جي مدد ڪري ٿو. پي پال استعمال ڪندي عطيو ڪريو. پنهنجي عام پے پال اڪائونٽ استعمال ڪندي عطيو ڪريو. ريوولٽ استعمال ڪندي عطيو ڪريو. جيڪڏهن توهان وٽ ريوولٽ آهي، ته هي عطيو ڏيڻ جو سڀ کان آسان طريقو آهي! هن ادائيگي جي طريقي سان وڌ ۾ وڌ %(amount)s جي اجازت آهي. مهرباني ڪري مختلف مدت يا ادائيگي جو طريقو چونڊيو. هن ادائيگي جي طريقي لاءِ گهٽ ۾ گهٽ %(amount)s جي ضرورت آهي. مهرباني ڪري مختلف مدت يا ادائيگي جو طريقو چونڊيو. بائنانس ڪائن بيس ڪريڪن مهرباني ڪري ادائگي جو طريقو چونڊيو. “هڪ ٽورينٽ کي اپنائڻ”: توهان جو يوزر نيم يا پيغام هڪ ٽورينٽ فائل نالي ۾ <div %(div_months)s>هر 12 مهينن جي ميمبرشپ تي هڪ ڀيرو</div> توهان جو يوزر نيم يا گمنام ذڪر ڪريڊٽس ۾ نئين خاصيتن تائين ابتدائي رسائي خاص Telegram پويان-منظر اپڊيٽس سان %(number)s تيز ڊائون لوڊ في ڏينهن جيڪڏهن توهان هن مهيني عطيو ڪيو! <a %(a_api)s>JSON API</a> رسائي انسانيت جي علم ۽ ثقافت جي حفاظت ۾ افسانوي حيثيت پويون فائدا، گڏ: <strong>%(percentage)s%% بونس ڊائون لوڊ</strong> حاصل ڪريو <a %(a_refer)s>دوستن کي حوالو ڏيڻ</a> سان. SciDB پيپر <strong>لامحدود</strong> بغير تصديق جي اڪائونٽ يا عطيا بابت سوال پڇڻ وقت، پنهنجو اڪائونٽ ID، اسڪرين شاٽس، رسيدون، جيتري معلومات ممڪن هجي شامل ڪريو. اسان هر 1-2 هفتن ۾ پنهنجي اي ميل چيڪ ڪندا آهيون، تنهن ڪري هن معلومات کي شامل نه ڪرڻ سان ڪنهن به حل ۾ دير ٿيندي. وڌيڪ ڊائون لوڊ حاصل ڪرڻ لاءِ، <a %(a_refer)s>پنهنجن دوستن کي حوالو ڏيو</a>! اسان رضاڪارن جي هڪ ننڍڙي ٽيم آهيون. اسان کي جواب ڏيڻ ۾ 1-2 هفتا لڳي سگهن ٿا. نوٽ ڪريو ته اڪائونٽ جو نالو يا تصوير عجيب لڳي سگهي ٿي. پريشان ٿيڻ جي ضرورت ناهي! اهي اڪائونٽ اسان جي عطيا پارٽنرز طرفان منظم ڪيا ويا آهن. اسان جا اڪائونٽ هيڪ نه ٿيا آهن. ڊونيٽ ڪريو <span %(span_cost)s></span> <span %(span_label)s></span> 12 مهينن لاءِ “%(tier_name)s” 1 مهيني لاءِ “%(tier_name)s” 24 مهينن لاءِ “%(tier_name)s” 3 مهينن لاءِ “%(tier_name)s” 48 مهينن لاءِ “%(tier_name)s” 6 مهينن لاءِ “%(tier_name)s” 96 مهينن لاءِ “%(tier_name)s” توهان چيڪ آئوٽ دوران اڃا تائين عطيو منسوخ ڪري سگهو ٿا. هن عطئي جي تصديق لاءِ ڊونيٽ بٽڻ تي ڪلڪ ڪريو. <strong>اهم نوٽ:</strong> ڪرپٽو قيمتون وڏي حد تائين مٽجي سگهن ٿيون، ڪڏهن ڪڏهن صرف ڪجهه منٽن ۾ 20%% تائين. اهو اڃا به گهٽ آهي انهن فيسن کان جيڪي اسان کي ڪيترن ئي ادائيگي فراهم ڪندڙن سان ٿينديون آهن، جيڪي اڪثر ڪري 50-60%% چارج ڪندا آهن "شيڊو چيريٽي" سان ڪم ڪرڻ لاءِ. <u>جيڪڏهن توهان اسان کي اصل قيمت سان رسيد موڪليندا، ته اسان توهان جي اڪائونٽ کي چونڊيل ميمبرشپ لاءِ ڪريڊٽ ڪنداسين</u> (جيستائين رسيد ڪجهه ڪلاڪن کان پراڻي نه هجي). اسان واقعي ساراهيون ٿا ته توهان اسان جي مدد ڪرڻ لاءِ اهڙين شين کي برداشت ڪرڻ لاءِ تيار آهيو! ❤️ ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. <span %(span_circle)s>1</span> پے پال تي بٽڪوائن خريد ڪريو <span %(span_circle)s>2</span>اسانجي پتي تي Bitcoin منتقل ڪريو ✅ عطئي واري صفحي ڏانهن رهنمائي ڪئي پئي وڃي… عطيو ڪريو مهرباني ڪري اسان سان رابطو ڪرڻ کان اڳ گهٽ ۾ گهٽ <span %(span_hours)s>24 ڪلاڪ</span> انتظار ڪريو (۽ هن صفحي کي ريفريش ڪريو). جيڪڏهن توهان بغير ميمبرشپ جي (ڪنهن به رقم) عطيو ڏيڻ چاهيو ٿا، ته مهرباني ڪري هي Monero (XMR) پتو استعمال ڪريو: %(address)s. پنهنجو گفٽ ڪارڊ موڪلڻ کان پوءِ، اسان جو خودڪار نظام ان کي چند منٽن ۾ تصديق ڪندو. جيڪڏهن اهو ڪم نٿو ڪري، ته پنهنجو گفٽ ڪارڊ ٻيهر موڪلڻ جي ڪوشش ڪريو (<a %(a_instr)s>ھدايتون</a>). جيڪڏهن اڃا به ڪم نٿو ڪري ته مهرباني ڪري اسان کي اي ميل ڪريو ۽ انا ان کي دستي طور تي جائزو وٺندي (اهو ڪجهه ڏينهن وٺي سگهي ٿو)، ۽ پڪ ڪريو ته توهان ٻيهر موڪلڻ جي ڪوشش جو ذڪر ڪيو آهي. مثال: مھرباني ڪري <a %(a_form)s>سرڪاري Amazon.com فارم</a> استعمال ڪريو اسان کي %(amount)s جو گفٽ ڪارڊ ھيٺ ڏنل اي ميل پتي تي موڪلڻ لاءِ. فارم ۾ "To" وصول ڪندڙ اي ميل: ايميزون گفٽ ڪارڊ اسان ٻين طريقن جا گفٽ ڪارڊ قبول نٿا ڪري سگهون، <strong>صرف سرڪاري فارم ذريعي سڌو Amazon.com تان موڪليل</strong>. جيڪڏهن توهان هي فارم استعمال نٿا ڪريو ته اسان توهان جو گفٽ ڪارڊ واپس نٿا ڪري سگهون. صرف هڪ ڀيرو استعمال ڪريو. توهان جي اڪائونٽ لاءِ منفرد، شيئر نه ڪريو. گفٽ ڪارڊ جو انتظار… (چيڪ ڪرڻ لاءِ صفحو ريفريش ڪريو) <a %(a_href)s>QR ڪوڊ عطيا صفحو</a> کوليو. Alipay ايپ سان QR ڪوڊ اسڪين ڪريو، يا Alipay ايپ کولڻ لاءِ بٽڻ دٻايو. مهرباني ڪري صبر ڪريو؛ صفحو لوڊ ٿيڻ ۾ ٿورو وقت لڳي سگهي ٿو ڇو ته اهو چين ۾ آهي. <span %(style)s>3</span>عطيو ڪريو (QR ڪوڊ اسڪين ڪريو يا بٽڻ دٻايو) PayPal تي PYUSD سڪو خريد ڪريو ڪيش ايپ تي Bitcoin (BTC) خريد ڪريو ٿورو وڌيڪ خريد ڪريو (اسان سفارش ڪريون ٿا %(more)s وڌيڪ) جيترو توهان عطيو ڪري رهيا آهيو (%(amount)s), ٽرانزيڪشن فيس کي ڍڪڻ لاءِ. جيڪو بچي ويندو اهو توهان وٽ رهندو. ڪيش ايپ ۾ "Bitcoin" (BTC) صفحي تي وڃو. Bitcoin اسان جي پتي تي منتقل ڪريو ننڍين عطين لاءِ ($25 کان گهٽ)، توهان کي Rush يا Priority استعمال ڪرڻي پوندي. "Send bitcoin" بٽڻ تي ڪلڪ ڪريو "withdrawal" ڪرڻ لاءِ. ڊالرز کان BTC ۾ تبديل ڪرڻ لاءِ %(icon)s آئڪن دٻايو. هيٺ ڏنل BTC رقم داخل ڪريو ۽ "Send" تي ڪلڪ ڪريو. جيڪڏهن توهان کي ڪا پريشاني ٿئي ته <a %(help_video)s>هي وڊيو</a> ڏسو. ايڪسپريس خدمتون آسان آهن، پر وڌيڪ فيس چارج ڪن ٿيون. توهان هن کي ڪرپٽو ايڪسچينج جي بدران استعمال ڪري سگهو ٿا جيڪڏهن توهان جلدي سان وڏو عطيو ڪرڻ چاهيو ٿا ۽ $5-10 جي فيس کي نظرانداز نٿا ڪريو. پڪ ڪريو ته عطيا صفحي تي ڏيکاريل صحيح ڪرپٽو رقم موڪليو، نه ته $USD ۾ رقم. ٻي صورت ۾ فيس ڪٽي ويندي ۽ اسان توهان جي ميمبرشپ کي خودڪار طريقي سان پروسيس نه ڪري سگھنداسين. ڪڏهن ڪڏهن تصديق ۾ 24 ڪلاڪ لڳي سگهن ٿا، تنهنڪري هن صفحي کي ريفريش ڪرڻ کي يقيني بڻايو (چاهي اهو ختم ٿي چڪو هجي). ڪريڊٽ / ڊيبٽ ڪارڊ جون هدايتون اسانجي ڪريڊٽ / ڊيبٽ ڪارڊ صفحي ذريعي عطيو ڪريو ڪجهه قدمن ۾ ڪرپٽو والٽ جو ذڪر آهي، پر پريشان نه ٿيو، توهان کي ڪرپٽو بابت ڪجهه سکڻ جي ضرورت ناهي. %(coin_name)s جون ھدايتون ﻮﻴڪ ﻦﻴڪﺳﺍ ﻲﮐ ڊﻮڪ QR ﻦﻫ ﻥﺎﺳ ﭗﻳﺍ ٽﻠﻳﻭ ﻮٽﭙﭘﺮڪ ﻲﺟ ﻥﺎﻫﻮﺗ ﻥﺎﺳ ﻞﻴﺼﻔﺗ ﻲﺟ ﻲﮕﻴﺋﺍﺩﺍ ﻮﻴڪ ﻦﻴڪﺳﺍ ڊﻮڪ ﺭﺁ ﺭﺁ ءﻻ﻿ ﮠﺮڪ ﺍﺩﺍ اسان رڳو معياري ورزن جي ڪرپٽو ڪوائنز جي حمايت ڪندا آهيون، ڪو به غير معمولي نيٽ ورڪ يا ڪوائنز جا ورزن نه. ٽرانزيڪشن جي تصديق ڪرڻ ۾ هڪ ڪلاڪ تائين لڳي سگهي ٿو، ڪوائن تي منحصر ڪري. <a %(a_page)s>هن صفحي</a> تي %(amount)s عطيو ڪريو. هي عطيو ختم ٿي چڪو آهي. مهرباني ڪري منسوخ ڪريو ۽ نئون ٺاهيو. جيڪڏهن توهان اڳ ۾ ئي ادائيگي ڪئي آهي: ها، مون پنهنجي رسيد اي ميل ڪئي جيڪڏهن ٽرانزيڪشن دوران ڪرپٽو جي شرح مٽا سٽا ۾ تبديلي آئي، ته مهرباني ڪري اصل شرح ڏيکاريندڙ رسيد شامل ڪريو. اسان واقعي توهان جي ڪرپٽو استعمال ڪرڻ جي تڪليف کي ساراهيون ٿا، اهو اسان لاءِ تمام گهڻو مددگار آهي! ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. <span %(span_circle)s>%(circle_number)s</span>رسيد اسان کي اي ميل ڪريو جيڪڏهن توهان کي ڪنهن مسئلي جو سامنا ٿئي، مهرباني ڪري اسان سان %(email)s تي رابطو ڪريو ۽ جيترو ممڪن ٿي سگهي معلومات شامل ڪريو (جهڙوڪ اسڪرين شاٽس). ✅ توهان جي چندي جي مهرباني! انا ڪجهه ڏينهن اندر توهان جي ميمبرشپ کي دستي طور تي فعال ڪندي. پنهنجي ذاتي تصديق واري پتي تي رسيد يا اسڪرين شاٽ موڪليو: جڏهن توهان پنهنجي رسيد اي ميل ڪئي آهي، ته هي بٽڻ ڪلڪ ڪريو، جيئن انا ان کي دستي طور تي جائزو وٺي سگهي (اهو ڪجهه ڏينهن وٺي سگهي ٿو): رسيد يا اسڪرين شاٽ پنهنجي ذاتي تصديق واري پتي تي موڪليو. مهرباني ڪري هن اي ميل پتي کي پنهنجي PayPal عطئي لاءِ استعمال نه ڪريو. منسوخ ڪريو ها، مهرباني ڪري منسوخ ڪريو ڇا توهان پڪ آهيو ته منسوخ ڪرڻ چاهيو ٿا؟ جيڪڏهن توهان اڳ ۾ ئي ادا ڪيو آهي ته منسوخ نه ڪريو. ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. نئون عطيو ڏيو ✅ توهان جي عطيا منسوخ ڪئي وئي آهي. تاريخ: %(date)s سڃاڻپ: %(id)s ٻيهر ترتيب ڏيو حالت: <span %(span_label)s>%(label)s</span> ڪل: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / مهينو لاءِ %(duration)s مهينا، شامل %(discounts)s%% رعايت)</span> ڪل: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / مهينو لاءِ %(duration)s مهينا)</span> 1. پنهنجو اي ميل داخل ڪريو. 2. پنهنجو ادائيگي جو طريقو چونڊيو. 3. پنهنجو ادائيگي جو طريقو ٻيهر چونڊيو. 4. "سيلف-هوسٽڊ" والٽ چونڊيو. 5. "مان ملڪيت جي تصديق ڪريان ٿو" تي ڪلڪ ڪريو. 6. توهان کي اي ميل رسيد ملڻ گهرجي. مهرباني ڪري اهو اسان ڏانهن موڪليو، ۽ اسان توهان جي چندي جي تصديق جلد کان جلد ڪنداسين. (توهان منسوخ ڪرڻ ۽ نئون عطيو ٺاهڻ چاهيو ٿا) ادائيگي جون هدايتون هاڻي پراڻيون ٿي چڪيون آهن. جيڪڏهن توهان ٻيهر عطيو ڏيڻ چاهيو ٿا، ته مٿي ڏنل "ٻيهر ترتيب ڏيو" بٽڻ استعمال ڪريو. توهان اڳ ۾ ئي ادا ڪري چڪا آهيو. جيڪڏهن توهان ادائيگي جي هدايتن جو جائزو وٺڻ چاهيو ٿا، ته هتي ڪلڪ ڪريو: پراڻيون ادائيگي جون هدايتون ڏيکاريو جيڪڏهن ڊونيشن پيج بلاڪ ٿي وڃي، ته مختلف انٽرنيٽ ڪنيڪشن (مثال طور VPN يا فون انٽرنيٽ) استعمال ڪريو. بدقسمتي سان، Alipay صفحو اڪثر ڪري رڳو <strong>مين لينڊ چين</strong> مان رسائي لائق آهي. توهان کي عارضي طور تي پنهنجو VPN غير فعال ڪرڻو پوندو، يا مين لينڊ چين (يا ڪڏهن ڪڏهن هانگ ڪانگ به ڪم ڪري ٿو) ڏانهن VPN استعمال ڪرڻو پوندو. <span %(span_circle)s>1</span>Alipay تي عطيو ڪريو %(total)s جي ڪل رقم <a %(a_account)s>هن Alipay اڪائونٽ</a> تي عطيو ڪريو Alipay جون ھدايتون <span %(span_circle)s>1</span> اسان جي ڪرپٽو اڪائونٽن مان ڪنهن هڪ ۾ منتقلي ڪريو هنن پتن مان ڪنهن هڪ تي %(total)s جي ڪل رقم عطيو ڪريو: ڪرپٽو هدايتون بٽڪوائن (BTC) خريد ڪرڻ جي هدايتن تي عمل ڪريو. توهان کي صرف اهو مقدار خريد ڪرڻ جي ضرورت آهي جيڪو توهان عطيو ڪرڻ چاهيو ٿا، %(total)s. اسانجي Bitcoin (BTC) پتي کي وصول ڪندڙ طور داخل ڪريو، ۽ پنهنجي عطيو %(total)s موڪلڻ لاءِ هدايتن تي عمل ڪريو: <span %(span_circle)s>1</span>Pix تي چندو ڏيو <a %(a_account)s>هن Pix اڪائونٽ</a> کي استعمال ڪندي %(total)s جي ڪل رقم چندو ڏيو Pix جون ھدايتون <span %(span_circle)s>1</span>WeChat تي عطيو ڪريو %(total)s جي ڪل رقم <a %(a_account)s>هن WeChat اڪائونٽ</a> تي عطيو ڪريو WeChat جون هدايتون هيٺين "ڪريڊٽ ڪارڊ کان Bitcoin" ايڪسپريس سروسز مان ڪنهن کي به استعمال ڪريو، جيڪي صرف ڪجهه منٽ وٺن ٿيون: BTC / Bitcoin پتو (خارجي والٽ): BTC / Bitcoin رقم: فارم ۾ هيٺ ڏنل تفصيل ڀريو: جيڪڏهن ڪا به ڄاڻ پراڻي آهي، مهرباني ڪري اسان کي اي ميل ڪريو ته جيئن اسان کي خبر پوي. مهرباني ڪري هن <span %(underline)s>بلڪل رقم</span> استعمال ڪريو. توهان جي ڪل قيمت ڪريڊٽ ڪارڊ فيس جي ڪري وڌيڪ ٿي سگهي ٿي. ننڍين رقم لاءِ، بدقسمتي سان، هي اسان جي رعايت کان وڌيڪ ٿي سگهي ٿو. (گهٽ ۾ گهٽ: %(minimum)s, پهرين ٽرانزيڪشن لاءِ ڪا تصديق نه) (گهٽ ۾ گهٽ: %(minimum)s) (گهٽ ۾ گهٽ: %(minimum)s) (گهٽ ۾ گهٽ: %(minimum)s, پهرين ٽرانزيڪشن لاءِ ڪا تصديق نه) (گهٽ ۾ گهٽ: %(minimum)s) (گهٽ ۾ گهٽ: %(minimum)s ملڪ تي منحصر، پهرين ٽرانزيڪشن لاءِ ڪا تصديق نه) PYUSD سڪو (PayPal USD) خريد ڪرڻ لاءِ هدايتن تي عمل ڪريو. ٿورو وڌيڪ خريد ڪريو (اسان سفارش ڪريون ٿا %(more)s وڌيڪ) جيترو توهان عطيو ڪري رهيا آهيو (%(amount)s)، ٽرانزيڪشن فيس کي ڍڪڻ لاءِ. توهان وٽ جيڪو بچي ويندو اهو توهان وٽ رهندو. پنهنجي PayPal ايپ يا ويب سائيٽ ۾ "PYUSD" صفحي تي وڃو. "Transfer" بٽڻ %(icon)s دٻايو، ۽ پوءِ "Send" دٻايو. حالت تازه ڪريو ٽائمر کي ري سيٽ ڪرڻ لاءِ، صرف نئون عطيو ٺاهيو. مهرباني ڪري ھيٺ ڏنل BTC رقم استعمال ڪريو، <em>يوروز يا ڊالر</em> نه، ٻي صورت ۾ اسان صحيح رقم حاصل نه ڪنداسين ۽ اوهان جي ميمبرشپ کي خودڪار طريقي سان تصديق نه ڪري سگهنداسين. Revolut تي Bitcoin (BTC) خريد ڪريو ٿورو وڌيڪ خريد ڪريو (اسان سفارش ڪريون ٿا %(more)s وڌيڪ) جيترو توهان عطيو ڪري رهيا آهيو (%(amount)s), ٽرانزيڪشن فيس کي ڍڪڻ لاءِ. جيڪو بچي ويندو اهو توهان وٽ رهندو. Bitcoin (BTC) خريد ڪرڻ لاءِ Revolut ۾ "Crypto" صفحي تي وڃو. Bitcoin اسان جي پتي تي منتقل ڪريو ننڍين عطين لاءِ ($25 کان گهٽ) توهان کي Rush يا Priority استعمال ڪرڻي پوندي. "Send bitcoin" بٽڻ تي ڪلڪ ڪريو "withdrawal" ڪرڻ لاءِ. يوروز کان BTC ۾ تبديل ڪرڻ لاءِ %(icon)s آئڪن دٻايو. هيٺ ڏنل BTC رقم داخل ڪريو ۽ "Send" تي ڪلڪ ڪريو. جيڪڏهن توهان کي ڪا پريشاني ٿئي ته <a %(help_video)s>هي وڊيو</a> ڏسو. حالت: 1 2 قدم بہ قدم گائيڊ هيٺ ڏنل مرحليوار گائيڊ ڏسو. نه ته توهان هن اڪائونٽ مان لاڪ ٿي سگهو ٿا! جيڪڏهن توهان اڃا تائين نه ڪيو آهي، ته پنهنجي لاگ ان ڪرڻ لاءِ خفيه ڪنجي لکو: توهان جي عطيا جي مهرباني! باقي وقت: عطيو %(amount)s کي %(account)s منتقل ڪريو تصديق جي انتظار ۾ (چيڪ ڪرڻ لاءِ صفحو ريفريش ڪريو)… منتقلي جي انتظار ۾ (چيڪ ڪرڻ لاءِ صفحو ريفريش ڪريو)… اڳي آخري 24 ڪلاڪن ۾ تيز ڊائون لوڊ روزاني حد ۾ شمار ٿيندا آهن. فاسٽ پارٽنر سرورز تان ڊائون لوڊ ٿيل فائلون %(icon)s سان نشان لڳل آهن. آخري 18 ڪلاڪ اڃا تائين ڪا به فائل ڊائون لوڊ ناهي ڪئي وئي. ڊائون لوڊ ٿيل فائلون عوامي طور تي ظاهر ناهن ڪيون وينديون. سڀ وقتون UTC ۾ آهن. ڊائون لوڊ ٿيل فائلون جيڪڏهن توهان هڪ فائل کي تيز ۽ سست ڊائون لوڊ سان ڊائون لوڊ ڪيو آهي، ته اها ٻه ڀيرا ظاهر ٿيندي. گهڻو پريشان نه ٿيو، اسان سان ڳنڍيل ويب سائيٽن تان ڪيترائي ماڻهو ڊائونلوڊ ڪري رهيا آهن، ۽ مسئلن ۾ پوڻ تمام گهٽ آهي. پر، محفوظ رهڻ لاءِ اسان VPN (ادا ڪيل) يا <a %(a_tor)s>Tor</a> (مفت) استعمال ڪرڻ جي صلاح ڏيون ٿا. مون 1984 جارج آرويل جو ڊائونلوڊ ڪيو، ڇا پوليس منهنجي دروازي تي ايندي؟ توهان انا آهيو! انا ڪير آهي؟ اسان وٽ ميمبرن لاءِ هڪ مستحڪم JSON API آهي، تيز ڊائونلوڊ URL حاصل ڪرڻ لاءِ: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON ۾ ئي دستاويز). ٻين استعمالن لاءِ، جهڙوڪ اسان جي سڀني فائلن کي ورجائڻ، ڪسٽم ڳولا ٺاهڻ، وغيره، اسان <a %(a_generate)s>جنريٽ ڪرڻ</a> يا <a %(a_download)s>ڊائونلوڊ ڪرڻ</a> اسان جي ElasticSearch ۽ MariaDB ڊيٽابيسن جي صلاح ڏيون ٿا. خام ڊيٽا کي دستي طور تي <a %(a_explore)s>JSON فائلن ذريعي</a> ڳولي سگهجي ٿو. اسان جي خام ٽورنٽس جي فهرست کي پڻ <a %(a_torrents)s>JSON</a> جي طور تي ڊائونلوڊ ڪري سگهجي ٿو. ڇا توهان وٽ API آهي؟ اسان هتي ڪنهن به ڪاپي رائيٽ ٿيل مواد جي ميزباني نٿا ڪريون. اسان هڪ سرچ انجڻ آهيون، ۽ اهڙي طرح صرف ميٽا ڊيٽا کي انڊيڪس ڪندا آهيون جيڪو اڳ ۾ ئي عوامي طور تي موجود آهي. جڏهن انهن خارجي ذريعن مان ڊائون لوڊ ڪيو وڃي، اسان توهان کي صلاح ڏينداسين ته توهان جي دائري اختيار ۾ قانونن کي چيڪ ڪريو ته ڇا اجازت آهي. اسان ٻين جي ميزباني ڪيل مواد جي ذميوار ناهيون. جيڪڏهن توهان کي هتي ڏسڻ ۾ ڪا شڪايت آهي، ته توهان جي بهترين شرط اصل ويب سائيٽ سان رابطو ڪرڻ آهي. اسان باقاعدگي سان انهن جي تبديلين کي پنهنجي ڊيٽابيس ۾ شامل ڪندا آهيون. جيڪڏهن توهان واقعي سوچيو ٿا ته توهان وٽ هڪ صحيح DMCA شڪايت آهي جنهن جو اسان کي جواب ڏيڻ گهرجي، مهرباني ڪري <a %(a_copyright)s>DMCA / ڪاپي رائيٽ دعويٰ فارم</a> ڀريو. اسان توهان جي شڪايتن کي سنجيدگي سان وٺون ٿا، ۽ جيترو جلدي ٿي سگهي توهان کي واپس جواب ڏينداسين. مان ڪاپي رائيٽ جي خلاف ورزي ڪيئن رپورٽ ڪريان؟ هتي ڪجهه ڪتاب آهن جيڪي شيڊو لائبريرين ۽ ڊجيٽل تحفظ جي دنيا لاءِ خاص اهميت رکن ٿا: توهان جا پسنديده ڪتاب ڪهڙا آهن؟ اسان پڻ هر ڪنهن کي ياد ڏيارڻ چاهيون ٿا ته اسان جو سڄو ڪوڊ ۽ ڊيٽا مڪمل طور تي اوپن سورس آهي. هي اسان جي منصوبن لاءِ منفرد آهي — اسان کي ڪنهن ٻئي منصوبي جي ڄاڻ ناهي جنهن وٽ اهڙي وڏي ڪيٽلاگ آهي جيڪو مڪمل طور تي اوپن سورس پڻ آهي. اسان ڪنهن به ماڻهوءَ کي ڀليڪار ڪيون ٿا جيڪو سوچيندو آهي ته اسان پنهنجو منصوبو خراب هلائي رهيا آهيون ته اسان جو ڪوڊ ۽ ڊيٽا وٺي پنهنجو شيڊو لائبريري قائم ڪري! اسان اهو ڪاوڙ مان نٿا چئون — اسان واقعي سوچيون ٿا ته هي شاندار هوندو ڇاڪاڻ ته اهو هر ڪنهن لاءِ معيار کي وڌائيندو، ۽ انسانيت جي ورثي کي بهتر محفوظ ڪندو. مان نفرت ڪريان ٿو ته توهان هي منصوبو ڪيئن هلائي رهيا آهيو! اسان چاهيون ٿا ته ماڻهو <a %(a_mirrors)s>مرر</a> سيٽ اپ ڪن، ۽ اسان ان کي مالي مدد فراهم ڪنداسين. مان ڪيئن مدد ڪري سگهان ٿو؟ ها، اسان گڏ ڪندا آهيون. اسان جي ميٽا ڊيٽا گڏ ڪرڻ جي ترغيب آرون سوارتز جو مقصد آهي "هر ڪڏهن به شايع ٿيل ڪتاب لاءِ هڪ ويب پيج"، جنهن لاءِ هن <a %(a_openlib)s>Open Library</a> ٺاهي. اهو منصوبو سٺو ڪيو آهي، پر اسان جي منفرد پوزيشن اسان کي ميٽا ڊيٽا حاصل ڪرڻ جي اجازت ڏئي ٿي جيڪا اهي نٿا ڪري سگهن. هڪ ٻيو ترغيب اسان جي خواهش هئي ته اسان ڄاڻون <a %(a_blog)s>دنيا ۾ ڪيترا ڪتاب آهن</a>, ته جيئن اسان حساب ڪري سگهون ته اسان وٽ ڪيترا ڪتاب بچائڻ لاءِ باقي آهن. ڇا توهان ميٽا ڊيٽا گڏ ڪندا آهيو؟ نوٽ ڪريو ته mhut.org ڪجهه IP رينجز کي بلاڪ ڪري ٿو، تنهن ڪري شايد هڪ VPN جي ضرورت پوي. <strong>Android:</strong> مٿين ساڄي ڪنڊ ۾ ٽي نقطن واري مينيو تي ڪلڪ ڪريو، ۽ "Add to Home Screen" چونڊيو. <strong>iOS:</strong> هيٺين حصي ۾ "Share" بٽڻ تي ڪلڪ ڪريو، ۽ "Add to Home Screen" چونڊيو. اسان وٽ ڪا سرڪاري موبائل ايپ ناهي، پر توهان هن ويب سائيٽ کي ايپ جي طور تي انسٽال ڪري سگهو ٿا. ڇا توهان وٽ موبائل ايپ آهي؟ مهرباني ڪري انهن کي <a %(a_archive)s>انٽرنيٽ آرڪائيو</a> ڏانهن موڪليو. اهي انهن کي صحيح طريقي سان محفوظ ڪندا. مان ڪيئن ڪتاب يا ٻيو جسماني مواد عطيو ڪري سگهان ٿو؟ مان ڪتابن جي درخواست ڪيئن ڪريان؟ <a %(a_blog)s>Anna’s بلاگ</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — باقاعده اپڊيٽس <a %(a_software)s>Anna’s سافٽ ويئر</a> — اسان جو اوپن سورس ڪوڊ <a %(a_datasets)s>Datasets</a> — ڊيٽا بابت <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — متبادل ڊومين ڇا Anna’s Archive بابت وڌيڪ وسيلا آهن؟ <a %(a_translate)s>Anna’s سافٽ ويئر تي ترجمو ڪريو</a> — اسان جو ترجمو نظام <a %(a_wikipedia)s>وڪيپيڊيا</a> — اسان بابت وڌيڪ (مهرباني ڪري هن صفحي کي اپڊيٽ رکڻ ۾ مدد ڪريو، يا پنهنجي ٻوليءَ لاءِ هڪ ٺاهيو!) پنهنجي پسنديده سيٽنگون چونڊيو، ڳولا جي باڪس کي خالي رکو، "ڳولا" تي ڪلڪ ڪريو، ۽ پوءِ پنهنجي برائوزر جي بڪ مارڪ فيچر استعمال ڪندي صفحي کي بڪ مارڪ ڪريو. مان پنهنجي ڳولا جون سيٽنگون ڪيئن محفوظ ڪريان؟ اسان سيڪيورٽي محققن کي اسان جي سسٽمن ۾ ڪمزوريون ڳولڻ لاءِ ڀليڪار ڪيون ٿا. اسان ذميوار افشاء ڪرڻ جا وڏا حامي آهيون. اسان سان <a %(a_contact)s>هتي</a> رابطو ڪريو. اسان في الحال بگ بائونٽيز انعام ڏيڻ جي قابل ناهيون، سواءِ انهن ڪمزوريون جيڪي اسان جي گمنامي کي <a %(a_link)s>سمجهڻ جي صلاحيت</a> رکن ٿيون، جن لاءِ اسان $10k-50k جي حد ۾ بائونٽيز پيش ڪندا آهيون. اسان مستقبل ۾ بگ بائونٽيز لاءِ وسيع دائرو پيش ڪرڻ چاهيون ٿا! مهرباني ڪري نوٽ ڪريو ته سوشل انجنيئرنگ حملا دائري کان ٻاهر آهن. جيڪڏهن توهان جارحتي سيڪيورٽي ۾ دلچسپي رکو ٿا، ۽ دنيا جي علم ۽ ثقافت کي محفوظ ڪرڻ ۾ مدد ڪرڻ چاهيو ٿا، ته اسان سان رابطو ڪرڻ پڪ ڪريو. توهان جي مدد ڪرڻ جا ڪيترائي طريقا آهن. ڇا توهان وٽ ذميوار افشاء ڪرڻ جو پروگرام آهي؟ اسان وٽ لفظي طور تي ڪافي وسيلا ناهن ته دنيا جي هر ماڻهو کي تيز رفتار ڊائون لوڊ فراهم ڪري سگهون، جيترو اسان چاهيون ٿا. جيڪڏهن ڪو امير مددگار اسان لاءِ اهو مهيا ڪرڻ چاهي، ته اهو شاندار هوندو، پر ان وقت تائين، اسان پنهنجي بهترين ڪوشش ڪري رهيا آهيون. اسان هڪ غير منافع بخش منصوبو آهيون جيڪو بمشڪل عطين ذريعي پاڻ کي برقرار رکي ٿو. اهو ئي سبب آهي جو اسان پنهنجي ڀائيوارن سان گڏ مفت ڊائون لوڊ لاءِ ٻه نظام لاڳو ڪيا آهن: سست ڊائون لوڊ سان گڏ شيئر ڪيل سرور، ۽ ٿوري تيز سرور هڪ انتظار جي فهرست سان (ته جيئن هڪ ئي وقت ۾ ڊائون لوڊ ڪندڙ ماڻهن جو تعداد گهٽجي وڃي). اسان وٽ سست ڊائون لوڊ لاءِ <a %(a_verification)s>برائوزر جي تصديق</a> پڻ آهي، ڇو ته ٻي صورت ۾ بوٽس ۽ اسڪراپر انهن جو غلط استعمال ڪندا، جنهن سان جائز استعمال ڪندڙن لاءِ شيون وڌيڪ سست ٿي وينديون. نوٽ ڪريو ته، جڏهن Tor برائوزر استعمال ڪندي، توهان کي پنهنجي سيڪيورٽي سيٽنگز کي ترتيب ڏيڻ جي ضرورت پوندي. سڀ کان گهٽ آپشن تي، جنهن کي “Standard” سڏيو وڃي ٿو، Cloudflare turnstile چيلنج ڪامياب ٿئي ٿو. وڌيڪ آپشنن تي، جن کي “Safer” ۽ “Safest” سڏيو وڃي ٿو، چيلنج ناڪام ٿئي ٿو. وڏين فائلن لاءِ ڪڏهن ڪڏهن سست ڊائون لوڊ وچ ۾ ٽٽي سگهن ٿا. اسان سفارش ڪريون ٿا ته هڪ ڊائون لوڊ مينيجر (جهڙوڪ JDownloader) استعمال ڪريو ته جيئن وڏن ڊائون لوڊن کي پاڻمرادو ٻيهر شروع ڪري سگهو. ڊائون لوڊ سست ڇو آهن؟ وچان وچان پڇيا ويندڙ سوال (FAQ) <a %(a_list)s>ٽورنٽ لسٽ جنريٽر</a> استعمال ڪريو ته جيئن توهان جي اسٽوريج اسپيس جي حدن اندر ٽورنٽس جي فهرست ٺاهي سگهجي، جيڪي ٽورنٽنگ جي تمام گهڻي ضرورت ۾ آهن. ها، ڏسو <a %(a_llm)s>LLM ڊيٽا</a> صفحو. گهڻا ٽورينٽس سڌو سنئون فائلون شامل ڪن ٿا، جنهن جو مطلب آهي ته توهان ٽورينٽ ڪلائنٽس کي صرف گهربل فائلون ڊائونلوڊ ڪرڻ جي هدايت ڪري سگهو ٿا. ڪهڙيون فائلون ڊائونلوڊ ڪرڻيون آهن، اهو طئي ڪرڻ لاءِ، توهان اسان جو <a %(a_generate)s>ميٽا ڊيٽا</a> ٺاهي سگهو ٿا، يا اسان جا ElasticSearch ۽ MariaDB ڊيٽابيس <a %(a_download)s>ڊائونلوڊ</a> ڪري سگهو ٿا. بدقسمتي سان، ڪجهه ٽورينٽ مجموعن ۾ بنيادي سطح تي .zip يا .tar فائلون شامل آهن، جنهن صورت ۾ توهان کي انفرادي فائلون چونڊڻ کان اڳ سڄو ٽورينٽ ڊائونلوڊ ڪرڻو پوندو. اڃا تائين ٽورنٽ کي فلٽر ڪرڻ لاءِ استعمال ۾ آسان اوزار موجود نه آهن، پر اسان تعاون کي ڀليڪار ڪيون ٿا. (اسان وٽ <a %(a_ideas)s>ڪجهه خيال</a> آهن پوئين صورت لاءِ به.) ڊگهو جواب: مختصر جواب: آساني سان نه. اسان هن فهرست ۾ ٽورينٽس جي وچ ۾ گهٽ ۾ گهٽ نقل يا اوورليپ رکڻ جي ڪوشش ڪندا آهيون، پر اهو هميشه حاصل نٿو ڪري سگهجي، ۽ اهو گهڻو ڪري ذريعو لائبريرين جي پاليسين تي منحصر آهي. انهن لائبريرين لاءِ جيڪي پنهنجا ٽورينٽس جاري ڪن ٿيون، اهو اسان جي هٿ ۾ ناهي. انهن ٽورينٽس لاءِ جيڪي Anna’s Archive پاران جاري ڪيا ويا آهن، اسان صرف MD5 هاش جي بنياد تي نقل ختم ڪندا آهيون، جنهن جو مطلب آهي ته ساڳئي ڪتاب جا مختلف نسخا نقل ختم نٿا ٿين. ها. اهي حقيقت ۾ PDFs ۽ EPUBs آهن، انهن وٽ صرف ڪيترن ئي اسان جي ٽورينٽس ۾ توسيع ناهي. ٽورينٽ فائلن لاءِ ميٽا ڊيٽا ڳولڻ جا ٻه هنڌ آهن، بشمول فائل جا قسم/توسيع: 1. هر مجموعو يا رليز پنهنجي ميٽا ڊيٽا آهي. مثال طور، <a %(a_libgen_nonfic)s>Libgen.rs ٽورينٽس</a> وٽ هڪ لاڳاپيل ميٽا ڊيٽا ڊيٽابيس آهي جيڪو Libgen.rs ويب سائيٽ تي ميزباني ٿيل آهي. اسان عام طور تي هر مجموعي جي <a %(a_datasets)s>ڊيٽا سيٽ صفحي</a> کان لاڳاپيل ميٽا ڊيٽا وسيلن ڏانهن لنڪ ڪندا آهيون. 2. اسان سفارش ڪريون ٿا اسان جا ElasticSearch ۽ MariaDB ڊيٽابيس <a %(a_generate)s>ٺاهڻ</a> يا <a %(a_download)s>ڊائونلوڊ</a> ڪرڻ. انهن ۾ Anna’s Archive ۾ هر رڪارڊ لاءِ ان جي لاڳاپيل ٽورينٽ فائلن (جيڪڏهن موجود هجي) لاءِ هڪ ميپنگ شامل آهي، ElasticSearch JSON ۾ "torrent_paths" تحت. ڪجهه ٽورنٽ ڪلائنٽ وڏن ٽڪرن جي سائيز کي سپورٽ نٿا ڪن، جيڪي اسان جي ڪيترن ئي ٽورنٽن ۾ آهن (نئين لاءِ اسان هاڻي ائين نٿا ڪريون - جيتوڻيڪ اهو وضاحتن مطابق صحيح آهي!). تنهن ڪري جيڪڏهن توهان کي هن مسئلي سان منهن ڏيڻو پوي، ته مختلف ڪلائنٽ کي آزمائي ڏسو، يا پنهنجي ٽورنٽ ڪلائنٽ ٺاهيندڙن کي شڪايت ڪريو. مان مدد ڪرڻ چاهيان ٿو، پر مون وٽ گهڻي ڊسڪ اسپيس ناهي. ٽورنٽس تمام سست آهن؛ ڇا مان ڊيٽا سڌو سنئون توهان کان ڊائونلوڊ ڪري سگهان ٿو؟ ڇا مان صرف فائلن جو هڪ ذيلي سيٽ ڊائونلوڊ ڪري سگهان ٿو، جهڙوڪ صرف هڪ خاص ٻولي يا موضوع؟ توهان ٽورينٽس ۾ نقلن کي ڪيئن سنڀاليو ٿا؟ ڇا مان ٽورينٽ فهرست کي JSON طور حاصل ڪري سگهان ٿو؟ مان ٽورينٽس ۾ PDFs يا EPUBs نٿو ڏسان، صرف بائنري فائلون؟ مان ڇا ڪريان؟ منهنجو ٽورنٽ ڪلائنٽ توهان جي ڪجهه ٽورنٽ فائلن / مقناطيسي لنڪ کي ڇو نه کولي سگهي ٿو؟ ٽورنٽس FAQ مان نيون ڪتابون ڪيئن اپلوڊ ڪريان؟ مهرباني ڪري <a %(a_href)s>هن شاندار پروجيڪٽ</a> کي ڏسو. ڇا توهان وٽ اپ ٽائيم مانيٽر آهي؟ انا جي آرڪائيو ڇا آهي؟ تيز ڊائون لوڊ استعمال ڪرڻ لاءِ ميمبر بڻجو. اسان هاڻي Amazon گفٽ ڪارڊ، ڪريڊٽ ۽ ڊيبٽ ڪارڊ، ڪرپٽو، Alipay، ۽ WeChat کي سپورٽ ڪريون ٿا. توهان اڄ تيز ڊائون لوڊ ختم ڪري ڇڏيا آهن. رسائي آخري 30 ڏينهن ۾ هر ڪلاڪ ڊائون لوڊ. ڪلاڪ جي اوسط: %(hourly)s. روزاني اوسط: %(daily)s. اسان پنهنجي شريڪدارن سان گڏجي پنهنجن مجموعن کي ڪنهن به ماڻهو لاءِ آساني سان ۽ مفت ۾ دستياب بڻائڻ لاءِ ڪم ڪريون ٿا. اسان يقين رکون ٿا ته هر ڪنهن کي انسانيت جي گڏيل حڪمت تائين رسائي جو حق آهي. ۽ <a %(a_search)s>ليکڪن جي خرچ تي نه</a>. انا جي آرڪائيو ۾ استعمال ٿيل ڊيٽا سيٽ مڪمل طور تي کوليو آهن، ۽ ٽورنٽس استعمال ڪندي وڏي پيماني تي مرر ڪري سگهجن ٿا. <a %(a_datasets)s>وڌيڪ سکو…</a> ڊگهي مدت آرڪائيو مڪمل ڊيٽابيس ڳولا ڪتاب، پيپر، ميگزين، ڪامڪس، لائبريري رڪارڊ، ميٽا ڊيٽا، … اسان جو سڄو <a %(a_code)s>ڪوڊ</a> ۽ <a %(a_datasets)s>ڊيٽا</a> مڪمل طور تي اوپن سورس آهي. <span %(span_anna)s>انا جي آرڪائيو</span> هڪ غير منافع بخش منصوبو آهي جنهن جا ٻه مقصد آهن: <li><strong>محفوظ ڪرڻ:</strong> انسانيت جي سموري ڄاڻ ۽ ثقافت کي محفوظ ڪرڻ.</li><li><strong>رسائي:</strong> هن ڄاڻ ۽ ثقافت کي دنيا جي ڪنهن به ماڻهو لاءِ دستياب ڪرڻ.</li> اسان وٽ دنيا جو سڀ کان وڏو اعليٰ معيار جو ٽيڪسٽ ڊيٽا گڏ آهي. <a %(a_llm)s>وڌيڪ سکو…</a> ايل ايل ايم ٽريننگ ڊيٽا 🪩 مررز: رضاڪارن جي گهرج جيڪڏهن توهان هڪ اعليٰ خطري واري گمنام ادائيگي پروسيسر هلائيندا آهيو، مهرباني ڪري اسان سان رابطو ڪريو. اسان پڻ اهڙن ماڻهن جي ڳولا ۾ آهيون جيڪي ذوق سان ننڍا اشتهار ڏيڻ چاهين ٿا. سڀئي آمدني اسان جي تحفظ جي ڪوششن ڏانهن ويندي. محفوظ ڪرڻ اسان جو اندازو آهي ته اسان دنيا جي ڪتابن جو تقريباً <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% محفوظ ڪيو آهي</a>. اسان ڪتابون، مقالا، ڪامڪس، رسالا، ۽ وڌيڪ محفوظ ڪريون ٿا، انهن موادن کي مختلف <a href="https://en.wikipedia.org/wiki/Shadow_library">شيڊو لائبريريز</a>، سرڪاري لائبريريز، ۽ ٻين مجموعن مان گڏ ڪري هڪ هنڌ آڻڻ سان. هي سڀ ڊيٽا هميشه لاءِ محفوظ آهي ڇو ته ان کي وڏي پيماني تي نقل ڪرڻ آسان بڻايو ويو آهي — ٽورينٽس استعمال ڪندي — جنهن جي نتيجي ۾ دنيا جي ڪيترن ئي حصن ۾ ڪاپيون موجود آهن. ڪجهه شيڊو لائبريريز اڳ ۾ ئي اهو پاڻ ڪري رهيون آهن (مثال طور Sci-Hub، Library Genesis)، جڏهن ته انا جي آرڪائيو انهن لائبريريز کي "آزاد" ڪري ٿو جيڪي وڏي پيماني تي تقسيم پيش نٿيون ڪن (مثال طور Z-Library) يا شيڊو لائبريريز نه آهن (مثال طور Internet Archive، DuXiu). هي وسيع تقسيم، اوپن سورس ڪوڊ سان گڏ، اسان جي ويب سائيٽ کي ٽيڪ ڊائونز کان محفوظ بڻائي ٿو، ۽ انسانيت جي ڄاڻ ۽ ثقافت جي ڊگهي مدي واري حفاظت کي يقيني بڻائي ٿو. وڌيڪ ڄاڻو <a href="/datasets">اسان جي ڊيٽا سيٽس</a> بابت. جيڪڏهن توهان هڪ <a %(a_member)s>ممبر</a> آهيو، ته برائوزر جي تصديق جي ضرورت ناهي. 🧬&nbsp;SciDB Sci-Hub جو تسلسل آهي. SciDB کوليو DOI Sci-Hub نوان پيپر اپلوڊ ڪرڻ <a %(a_paused)s>روڪي</a> ڇڏيا آهن. %(count)s اڪيڊمڪ پيپرن تائين سڌو رسائي 🧬&nbsp;SciDB Sci-Hub جو تسلسل آهي، ان جي واقف انٽرفيس ۽ PDFs جي سڌي ڏسڻ سان. ڏسڻ لاءِ پنهنجو DOI داخل ڪريو. اسان وٽ مڪمل Sci-Hub مجموعو آهي، گڏوگڏ نوان پيپر پڻ. گهڻا سڌو سنئون واقف انٽرفيس سان ڏسي سگهجن ٿا، Sci-Hub جيان. ڪجهه ٻاهرين ذريعن ذريعي ڊائون لوڊ ڪري سگهجن ٿا، جنهن صورت ۾ اسان انهن جا لنڪ ڏيکاريندا آهيون. توهان ٽورنٽس کي سيڊنگ ڪندي وڏي مدد ڪري سگهو ٿا. <a %(a_torrents)s>وڌيڪ سکو…</a> >%(count)s سيڊرز <%(count)s سيڊرز %(count_min)s–%(count_max)s سيڊرز 🤝 رضاڪارن جي ڳولا ۾ هڪ غير منافع بخش، اوپن سورس پروجيڪٽ جي حيثيت سان، اسان هميشه ماڻهن جي مدد جي ڳولا ۾ آهيون. آءِ پي ايف ايس ڊائون لوڊ %(by)s پاران فهرست، ٺاهي وئي <span %(span_time)s>%(time)s</span> محفوظ ڪريو ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري ٻيهر ڪوشش ڪريو. ✅ محفوظ ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو. فهرست خالي آهي. ترميم فهرست ۾ شامل يا هٽايو فائل ڳولي ۽ "فهرستون" ٽيب کوليو. فهرست اسان ڪيئن مدد ڪري سگهون ٿا اوورليپ کي ختم ڪرڻ (ڊيڊوپليڪيشن) ٽيڪسٽ ۽ ميٽا ڊيٽا ڪڍڻ OCR اسان پنهنجي مڪمل مجموعن تائين تيز رفتار رسائي فراهم ڪرڻ جي قابل آهيون، گڏوگڏ اڻ ڇپيل مجموعن تائين. اهو انٽرپرائز-سطح جي رسائي آهي جيڪا اسان هزارين ڊالر جي عطين جي حد ۾ فراهم ڪري سگهون ٿا. اسان پڻ تيار آهيون ته ان کي اعليٰ معيار جي مجموعن لاءِ مٽائي سگهون ٿا جيڪي اسان وٽ اڃا تائين نه آهن. اسان توهان کي رقم واپس ڪري سگهون ٿا جيڪڏهن توهان اسان کي اسان جي ڊيٽا جي افزودگي سان مهيا ڪري سگهو ٿا، جهڙوڪ: انساني علم جي ڊگهي مدي واري آرڪائيو جي حمايت ڪريو، جڏهن ته توهان جي ماڊل لاءِ بهتر ڊيٽا حاصل ڪريو! <a %(a_contact)s>اسان سان رابطو ڪريو</a> بحث ڪرڻ لاءِ ته اسان ڪيئن گڏجي ڪم ڪري سگهون ٿا. اهو چڱيءَ طرح سمجهيو وڃي ٿو ته LLM اعليٰ معيار جي ڊيٽا تي ترقي ڪن ٿا. اسان وٽ دنيا ۾ ڪتابن، پيپرن، ميگزينن وغيره جو سڀ کان وڏو مجموعو آهي، جيڪي ڪجهه اعليٰ معيار جا ٽيڪسٽ ذريعا آهن. LLM ڊيٽا منفرد پيمانو ۽ حد اسان جي مجموعي ۾ سؤ ملين کان وڌيڪ فائلون شامل آهن، جن ۾ اڪيڊمڪ جرنلز، ٽيڪسٽ بڪ، ۽ ميگزين شامل آهن. اسان اهو پيمانو وڏن موجوده ذخيرن کي گڏ ڪري حاصل ڪريون ٿا. اسان جي ڪجهه ذريعن جا مجموعا اڳ ۾ ئي وڏي پيماني تي موجود آهن (Sci-Hub، ۽ Libgen جا حصا). ٻيا ذريعا اسان پاڻ آزاد ڪيا. <a %(a_datasets)s>Datasets</a> مڪمل جائزو ڏيکاري ٿو. اسان جي مجموعي ۾ اي-بڪ دور کان اڳ جا لکين ڪتاب، پيپر، ۽ ميگزين شامل آهن. هن مجموعي جا وڏا حصا اڳ ۾ ئي OCR ڪيا ويا آهن، ۽ اڳ ۾ ئي ٿورو اندروني اوورليپ آهي. جاري رکو جيڪڏهن توهان پنهنجو چاٻي وڃائي ڇڏيو آهي، مهرباني ڪري <a %(a_contact)s>اسان سان رابطو ڪريو</a> ۽ جيتري معلومات ڏئي سگهو ٿا، مهيا ڪريو. توهان کي عارضي طور تي نئون اڪائونٽ ٺاهڻو پوندو اسان سان رابطو ڪرڻ لاءِ. مھرباني ڪري ھن صفحي کي ڏسڻ لاءِ <a %(a_account)s>لاگ ان ڪريو</a>.</a> اسپام-بوٽس کي ڪيترائي اڪائونٽ ٺاهڻ کان روڪڻ لاءِ، اسان کي پهريان توهان جي برائوزر جي تصديق ڪرڻي پوندي. جيڪڏهن توهان هڪ لامحدود لوپ ۾ ڦاسي پيا آهيو، ته اسان <a %(a_privacypass)s>پرائيويسي پاس</a> انسٽال ڪرڻ جي صلاح ڏيون ٿا. اهو پڻ مدد ڪري سگهي ٿو ته اشتهارن کي بلاڪ ڪندڙ ۽ ٻيا برائوزر ايڪسٽينشن بند ڪريو. لاگ ان / رجسٽر ڪريو انا جو آرڪائيو عارضي طور تي سار سنڀال لاءِ بند آهي. مهرباني ڪري هڪ ڪلاڪ بعد واپس اچو. متبادل ليکڪ متبادل وضاحت متبادل ايڊيشن متبادل توسيع متبادل فائل جو نالو متبادل پبلشر متبادل عنوان تاريخ اوپن سورس ڪيو ويو وڌيڪ پڙھو… وضاحت انا جي آرڪائيو ۾ CADAL SSNO نمبر لاءِ ڳولا ڪريو انا جي آرڪائيو ۾ DuXiu SSID نمبر لاءِ ڳولا ڪريو انا جي آرڪائيو ۾ DuXiu DXID نمبر لاءِ ڳولا ڪريو ISBN لاءِ Anna’s Archive ڳوليو انا جي آرڪائيو ۾ او سي ايل سي (ورلڊ ڪيٽ) نمبر لاءِ ڳولا ڪريو انا جي آرڪائيو ۾ اوپن لائبريري آءِ ڊي لاءِ ڳولا ڪريو آنا جو آرڪائيو آن لائن ڏسندڙ %(count)s متاثر ٿيل صفحا ڊائونلوڊ ڪرڻ کان پوءِ: ھن فائيل جو بھتر ورزن ھيٺين تي موجود ٿي سگھي ٿو %(link)s بلڪ ٽورينٽ ڊائون لوڊز مجموعو فارميٽس جي وچ ۾ تبديل ڪرڻ لاءِ آن لائن اوزار استعمال ڪريو. سفارش ڪيل تبديلي اوزار: %(links)s وڏين فائلن لاءِ، اسان تجويز ڏيون ٿا ته ڊائون لوڊ مينيجر استعمال ڪيو وڃي ته جيئن رڪاوٽون نه اچن. سفارش ڪيل ڊائون لوڊ مينيجر: %(links)s EBSCOhost اي بڪ انڊيڪس (صرف ماهرن لاءِ) (مٿي "GET" تي ڪلڪ ڪريو) (مٿي "GET" تي ڪلڪ ڪريو) ٻاهريون ڊائون لوڊ <strong>🚀 تيز ڊائون لوڊز</strong> توھان وٽ اڄ %(remaining)s باقي آھن. ميمبر ٿيڻ جي مهرباني! ❤️ <strong>🚀 تيز ڊائون لوڊ</strong> توهان اڄ لاءِ تيز ڊائون لوڊ ختم ڪري ڇڏيا آهن. <strong>🚀 تيز ڊائون لوڊ</strong> توهان تازو ئي هي فائل ڊائون لوڊ ڪئي آهي. لنڪس ڪجهه وقت لاءِ صحيح رهندا. <strong>🚀 تيز ڊائون لوڊز</strong> ڪتابن، پيپرز، ۽ وڌيڪ جي ڊگھي مدي واري حفاظت جي حمايت ڪرڻ لاءِ <a %(a_membership)s>ممبر</a> بڻجو. توھان جي حمايت جي شڪريي ۾، توھان کي تيز ڊائون لوڊز ملندا. ❤️ 🚀 تيز ڊائون لوڊ 🐢 سست ڊائون لوڊ انٽرنيٽ آرڪائيو مان ادھار وٺو آءِ پي ايف ايس گيٽ وي #%(num)d (توهان کي آءِ پي ايف ايس سان ڪيترائي ڀيرا ڪوشش ڪرڻي پوندي) Libgen.li Libgen.rs افسانوي Libgen.rs غير افسانوي انهن جي اشتهارن ۾ نقصانڪار سافٽ ويئر شامل هجڻ جي خبر آهي، تنهنڪري هڪ اشتهار بلاڪر استعمال ڪريو يا اشتهارن تي ڪلڪ نه ڪريو Amazon جو “Send to Kindle” djazz جو “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC فائلون ڊائونلوڊ ڪرڻ لاءِ غير معتبر ٿي سگهن ٿيون) ڪو به ڊائون لوڊ نه مليو. سڀ ڊائون لوڊ آپشنز ۾ ساڳي فائل آهي، ۽ استعمال ڪرڻ لاءِ محفوظ هجڻ گهرجي. اهو چيو، انٽرنيٽ تان فائلون ڊائون لوڊ ڪرڻ وقت هميشه محتاط رهو، خاص طور تي اينا جي آرڪائيو کان ٻاهرين سائيٽن کان. مثال طور، پنهنجي ڊوائيسز کي اپڊيٽ رکڻ کي يقيني بڻايو. (ڪو به ريڊائريڪٽ نه) اسان جي ڏسندڙ ۾ کوليو (ڏسندڙ ۾ کوليو) آپشن #%(num)d: %(link)s %(extra)s CADAL ۾ اصل رڪارڊ ڳوليو DuXiu تي دستي ڳولا ڪريو ISBNdb ۾ اصل رڪارڊ ڳوليو ورلڊ ڪيٽ ۾ اصل رڪارڊ ڳوليو اوپن لائبريري ۾ اصل رڪارڊ ڳوليو ISBN لاءِ مختلف ٻين ڊيٽابيسن ۾ ڳوليو (صرف پرنٽ معذور سرپرست) PubMed توھان کي فائل کولڻ لاءِ اي بڪ يا PDF ريڊر جي ضرورت پوندي، فائل فارميٽ تي دارومدار رکي ٿو. سفارش ڪيل اي بڪ ريڊر: %(links)s انا جي آرڪائيو 🧬 SciDB Sci-Hub: %(doi)s (وابسته DOI شايد Sci-Hub ۾ دستياب نه هجي) توهان PDF ۽ EPUB ٻنهي فائلن کي پنهنجي Kindle يا Kobo eReader ڏانهن موڪلي سگهو ٿا. سفارش ڪيل اوزار: %(links)s وڌيڪ معلومات <a %(a_slow)s>FAQ</a> ۾. ليکڪن ۽ لائبريرين جي مدد ڪريو جيڪڏهن توهان کي هي پسند آهي ۽ توهان ان کي برداشت ڪري سگهو ٿا، ته اصل خريد ڪرڻ تي غور ڪريو، يا سڌو سنئون ليکڪن جي مدد ڪريو. جيڪڏهن اهو توهان جي مقامي لائبريري ۾ موجود آهي، ته اتي مفت ۾ ادھار وٺڻ تي غور ڪريو. پارٽنر سرور ڊائون لوڊز عارضي طور تي هن فائل لاءِ دستياب نه آهن. ٽورينٽ قابل اعتماد پارٽنرز کان. Z-Library Z-لائبريري ٽور تي (ٽور برائوزر جي ضرورت آهي) ٻاهرين ڊائون لوڊ ڏيکاريو <span class="font-bold">❌ ھي فائيل مسئلن سان ٿي سگھي ٿو، ۽ ھڪ ذريعو لائبريري کان لڪايو ويو آھي.</span> ڪڏهن ڪڏهن اھو ڪاپي رائيٽ ھولڊر جي درخواست تي ھوندو آھي، ڪڏهن ڪڏهن اھو بھتر متبادل جي موجودگي جي ڪري ھوندو آھي، پر ڪڏهن ڪڏهن اھو فائيل جي مسئلي جي ڪري ھوندو آھي. اھو اڃا تائين ڊائون لوڊ ڪرڻ لاءِ ٺيڪ ٿي سگھي ٿو، پر اسان صلاح ڏيون ٿا ته پھريائين متبادل فائيل ڳوليو. وڌيڪ تفصيل: جيڪڏھن توھان اڃا تائين ھي فائيل ڊائون لوڊ ڪرڻ چاھيو ٿا، ته پڪ ڪريو ته صرف قابل اعتماد، اپڊيٽ ٿيل سافٽ ويئر استعمال ڪريو ان کي کولڻ لاءِ. ميٽا ڊيٽا تبصرا AA: “%(name)s” لاءِ Anna’s Archive ڳوليو ڪوڊز ايڪسپلورر: ڪوڊز ايڪسپلورر ۾ ڏسو “%(name)s” URL: ويب سائيٽ: جيڪڏهن توهان وٽ هي فائل آهي ۽ اها اڃا تائين انا جي آرڪائيو ۾ موجود ناهي، ته غور ڪريو <a %(a_request)s>ان کي اپلوڊ ڪرڻ</a>. انٽرنيٽ آرڪائيو ڪنٽرولڊ ڊجيٽل لينڊنگ فائل “%(id)s” هي انٽرنيٽ آرڪائيو مان فائل جو رڪارڊ آهي، سڌو ڊائون لوڊ ڪرڻ جي فائل ناهي. توهان ڪتاب کي ادھار وٺڻ جي ڪوشش ڪري سگهو ٿا (هيٺ ڏنل لنڪ)، يا جڏهن <a %(a_request)s>فائل جي درخواست</a> ڪري رهيا آهيو ته هن URL کي استعمال ڪريو. ميٽا ڊيٽا بھتر ڪريو CADAL SSNO %(id)s ميٽاڊاٽا رڪارڊ ھي ميٽا ڊيٽا ريڪارڊ آھي، ڊائون لوڊ ڪرڻ جو فائيل ناھي. توھان ھي URL استعمال ڪري سگھو ٿا <a %(a_request)s>فائيل جي درخواست ڪندي</a>. DuXiu SSID %(id)s ميٽاڊاٽا رڪارڊ ISBNdb %(id)s ميٽاڊاٽا رڪارڊ MagzDB ID %(id)s ميٽا ڊيٽا رڪارڊ Nexus/STC ID %(id)s ميٽا ڊيٽا رڪارڊ OCLC (ورلڊ ڪيٽ) نمبر %(id)s ميٽاڊاٽا رڪارڊ اوپن لائبريري %(id)s ميٽاڊاٽا رڪارڊ Sci-Hub فائل “%(id)s” نه مليو “%(md5_input)s” اسان جي ڊيٽابيس ۾ نه مليو. تبصرو شامل ڪريو (%(count)s) توهان URL مان md5 حاصل ڪري سگهو ٿا، مثال طور هن فائل جي بهتر ورزن جو MD5 (جيڪڏهن لاڳو ٿئي). هن کي ڀريو جيڪڏهن ڪا ٻي فائل آهي جيڪا هن فائل سان ويجهي ميچ ڪري ٿي (ساڳي ايڊيشن، ساڳي فائل ايڪسٽينشن جيڪڏهن توهان ڳولي سگهو ٿا)، جنهن کي ماڻهن کي هن فائل جي بدران استعمال ڪرڻ گهرجي. جيڪڏهن توهان کي انا جي آرڪائيو کان ٻاهر هن فائل جو بهتر ورزن معلوم آهي، ته مهرباني ڪري <a %(a_upload)s>ان کي اپلوڊ ڪريو</a>. ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. توهان هڪ تبصرو ڇڏيو. ان کي ڏيکارڻ ۾ هڪ منٽ لڳي سگهي ٿو. مهرباني ڪري <a %(a_copyright)s>DMCA / ڪاپي رائيٽ دعويٰ فارم</a> استعمال ڪريو. مسئلي کي بيان ڪريو (ضروري) جيڪڏهن هن فائل جي معيار بهترين آهي، توهان هتي ان بابت ڪجهه به بحث ڪري سگهو ٿا! جيڪڏهن نه، مهرباني ڪري "فائل مسئلي جي رپورٽ" بٽڻ استعمال ڪريو. عظيم فائل جي معيار (%(count)s) فائل جي معيار سکو پاڻمرادو هن فائل لاءِ <a %(a_metadata)s>ميٽا ڊيٽا کي بهتر ڪرڻ</a> بابت سکو. مسئلي جي وضاحت مهرباني ڪري <a %(a_login)s>لاگ ان</a> ڪريو. مون کي هي ڪتاب پسند آيو! هن فائل جي معيار جي رپورٽ ڪري ڪميونٽي جي مدد ڪريو! 🙌 ڪجهه غلط ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو ۽ ٻيهر ڪوشش ڪريو. فائل جي مسئلي جي رپورٽ ڪريو (%(count)s) توهان جي رپورٽ جمع ڪرائڻ جي مهرباني. اها هن صفحي تي ڏيکاري ويندي، ۽ ان کي دستي طور تي انا پاران جائزو ورتو ويندو (جيستائين اسان وٽ مناسب ماڊريشن سسٽم نه هجي). تبصرو ڇڏي ڏيو رپورٽ جمع ڪريو هن فائل سان ڇا غلط آهي؟ ادھار (%(count)s) تبصرا (%(count)s) ڊائون لوڊز (%(count)s) ميٽا ڊيٽا ڳوليو (%(count)s) لسٽون (%(count)s) اسٽاٽس (%(count)s) هن خاص فائل بابت معلومات لاءِ، ان جي <a %(a_href)s>JSON فائل</a> چيڪ ڪريو. هي هڪ فائل آهي جيڪا <a %(a_ia)s>IA جي ڪنٽرولڊ ڊجيٽل لينڊنگ</a> لائبريري پاران منظم ڪئي وئي آهي، ۽ انا جي آرڪائيو پاران ڳولا لاءِ انڊيڪس ڪئي وئي آهي. انهن مختلف ڊيٽا سيٽن بابت معلومات لاءِ جيڪي اسان گڏ ڪيا آهن، ڏسو <a %(a_datasets)s>Datasets صفحو</a>. ڳنڍيل ريڪارڊ مان ميٽا ڊيٽا اوپن لائبريري تي ميٽا ڊيٽا بهتر ڪريو "فائل MD5" هڪ هاش آهي جيڪو فائل جي مواد مان ڳڻپيو ويندو آهي، ۽ ان مواد جي بنياد تي مناسب طور تي منفرد آهي. سڀني شيڊو لائبريريون جيڪي اسان هتي انڊيڪس ڪيون آهن، بنيادي طور تي فائلن کي سڃاڻڻ لاءِ MD5s استعمال ڪن ٿيون. هڪ فائل ڪيترن ئي شيڊو لائبريرين ۾ ظاهر ٿي سگهي ٿي. انهن مختلف ڊيٽا سيٽن بابت معلومات لاءِ جيڪي اسان گڏ ڪيا آهن، ڏسو <a %(a_datasets)s>Datasets صفحو</a>. فائل جي معيار جي رپورٽ ڪريو ڪل ڊائون لوڊ: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Czech metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} ھاتھی ٽرسٽ %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} خبردار: گهڻا ڳنڍيل ريڪارڊ: جڏهن توهان انا جي آرڪائيو تي هڪ ڪتاب ڏسو ٿا، توهان مختلف فيلڊ ڏسي سگهو ٿا: عنوان، ليکڪ، پبلشر، ايڊيشن، سال، وضاحت، فائل نالو، ۽ وڌيڪ. اهي سڀ معلومات جا ٽڪرا <em>ميٽا ڊيٽا</em> سڏجن ٿا. ڇو ته اسان مختلف <em>ذريعو لائبريرين</em> مان ڪتاب گڏ ڪريون ٿا، اسان جيڪو به ميٽا ڊيٽا موجود آهي اهو ڏيکاريون ٿا. مثال طور، جيڪڏهن اسان کي ڪو ڪتاب Library Genesis مان مليو آهي، ته اسان Library Genesis جي ڊيٽابيس مان عنوان ڏيکارينداسين. ڪڏهن ڪڏهن هڪ ڪتاب <em>گهڻن</em> ذريعو لائبريرين ۾ موجود هوندو آهي، جن ۾ مختلف ميٽا ڊيٽا فيلڊ ٿي سگهن ٿيون. اهڙي صورت ۾، اسان هر فيلڊ جو ڊگهو ورزن ڏيکارينداسين، ڇو ته اهو اميد آهي ته سڀ کان وڌيڪ مفيد معلومات تي مشتمل هوندو! اسان اڃا به ٻين فيلڊن کي وضاحت جي هيٺان ڏيکارينداسين، جهڙوڪ ”متبادل عنوان“ (پر صرف جيڪڏهن اهي مختلف آهن). اسان پڻ <em>ڪوڊ</em> جهڙوڪ سڃاڻپ ڪندڙ ۽ درجہ بندي ڪندڙ ذريعو لائبريري مان ڪڍون ٿا. <em>سڃاڻپ ڪندڙ</em> هڪ خاص ايڊيشن جي نمائندگي ڪن ٿا؛ مثال طور ISBN، DOI، Open Library ID، Google Books ID، يا Amazon ID. <em>درجہ بندي ڪندڙ</em> ڪيترن ئي ساڳين ڪتابن کي گڏ ڪن ٿا؛ مثال طور Dewey Decimal (DCC)، UDC، LCC، RVK، يا GOST. ڪڏهن ڪڏهن اهي ڪوڊ ذريعو لائبريرين ۾ واضح طور تي ڳنڍيل هوندا آهن، ۽ ڪڏهن ڪڏهن اسان انهن کي فائل نالي يا وضاحت مان ڪڍي سگهون ٿا (خاص طور تي ISBN ۽ DOI). اسان سڃاڻپ ڪندڙن کي استعمال ڪري سگهون ٿا <em>صرف ميٽا ڊيٽا گڏ ڪرڻ واريون مجموعا</em> ۾ رڪارڊ ڳولڻ لاءِ، جهڙوڪ OpenLibrary، ISBNdb، يا WorldCat/OCLC. اسان جي سرچ انجڻ ۾ هڪ خاص <em>ميٽا ڊيٽا ٽيب</em> آهي جيڪڏهن توهان انهن مجموعن کي براؤز ڪرڻ چاهيو ٿا. اسان ميچنگ رڪارڊ استعمال ڪريون ٿا گم ٿيل ميٽا ڊيٽا فيلڊن کي ڀرڻ لاءِ (مثال طور جيڪڏهن عنوان غائب آهي)، يا جهڙوڪ ”متبادل عنوان“ (جيڪڏهن ڪو موجود عنوان آهي). ڪتاب جي ميٽا ڊيٽا ڪٿي کان آئي آهي، اهو ڏسڻ لاءِ، ڪتاب جي صفحي تي <em>“ٽيڪنيڪل تفصيل” ٽيب</em> ڏسو. ان ۾ ان ڪتاب جي خام JSON لاءِ هڪ لنڪ آهي، اصل رڪارڊن جي خام JSON ڏانهن اشارن سان. وڌيڪ معلومات لاءِ، هيٺين صفحن کي ڏسو: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, ۽ <a %(a_example)s>Example metadata JSON</a>. آخر ۾، اسان جو سڀ ميٽا ڊيٽا <a %(a_generated)s>generated</a> يا <a %(a_downloaded)s>downloaded</a> ڪري سگھجي ٿو جيئن ElasticSearch ۽ MariaDB ڊيٽابيس. پس منظر توهان ڪتابن جي حفاظت ۾ مدد ڪري سگهو ٿا ميٽا ڊيٽا بهتر ڪرڻ سان! پهرين، انا جي آرڪائيو تي ميٽا ڊيٽا بابت پس منظر پڙهو، ۽ پوءِ Open Library سان ڳنڍڻ ذريعي ميٽا ڊيٽا بهتر ڪرڻ سکيو، ۽ انا جي آرڪائيو تي مفت ميمبرشپ حاصل ڪريو. ميٽا ڊيٽا بهتر ڪريو ته جيڪڏهن توهان کي خراب ميٽا ڊيٽا سان ڪو فائل ملي، ته توهان ان کي ڪيئن درست ڪريو؟ توهان ذريعو لائبريري ڏانهن وڃي سگهو ٿا ۽ ان جي ميٽا ڊيٽا کي درست ڪرڻ جي طريقيڪار تي عمل ڪري سگهو ٿا، پر ڇا ڪرڻ گهرجي جيڪڏهن هڪ فائل گهڻن ذريعو لائبريرين ۾ موجود آهي؟ هڪ سڃاڻپ ڪندڙ آهي جيڪو انا جي آرڪائيو تي خاص طور تي علاج ڪيو ويندو آهي. <strong>Open Library تي annas_archive md5 فيلڊ هميشه ٻين سڀني ميٽا ڊيٽا کي اوور رائيڊ ڪري ٿو!</strong> اچو ته پهرين ٿورو پوئتي هٽي ۽ Open Library بابت سکون. Open Library 2006 ۾ Aaron Swartz پاران قائم ڪئي وئي هئي جنهن جو مقصد ”هر ڪڏهن به شايع ٿيل ڪتاب لاءِ هڪ ويب پيج“ هو. اهو ڪتاب ميٽا ڊيٽا لاءِ هڪ قسم جو Wikipedia آهي: هرڪو ان کي ايڊٽ ڪري سگهي ٿو، اهو آزادانه طور تي لائسنس يافته آهي، ۽ ان کي بلڪ ۾ ڊائون لوڊ ڪري سگهجي ٿو. اهو هڪ ڪتاب ڊيٽابيس آهي جيڪو اسان جي مشن سان سڀ کان وڌيڪ هم آهنگ آهي — حقيقت ۾، انا جي آرڪائيو Aaron Swartz جي ويزن ۽ زندگي کان متاثر ٿي آهي. پهرين کان نئين سر شروعات ڪرڻ بدران، اسان فيصلو ڪيو ته اسان جا رضاڪار Open Library ڏانهن موڪليو. جيڪڏهن توهان ڏسو ته ڪنهن ڪتاب ۾ غلط ميٽا ڊيٽا آهي، توهان هيٺين طريقي سان مدد ڪري سگهو ٿا: نوٽ ڪريو ته هي صرف ڪتابن لاءِ ڪم ڪري ٿو، اڪيڊمڪ پيپرز يا ٻين قسمن جي فائلن لاءِ نه. ٻين قسمن جي فائلن لاءِ اسان اڃا تائين ماخذ لائبريري ڳولڻ جي سفارش ڪريون ٿا. اهو ڪجهه هفتا وٺي سگهي ٿو ته تبديليون Anna’s Archive ۾ شامل ٿين، ڇو ته اسان کي جديد Open Library ڊيٽا ڊمپ ڊائونلوڊ ڪرڻو پوندو، ۽ اسان جي سرچ انڊيڪس کي ٻيهر پيدا ڪرڻو پوندو.  <a %(a_openlib)s>Open Library ويب سائيٽ</a> ڏانهن وڃو. صحیح ڪتاب جو رڪارڊ ڳوليو. <strong>خبردار:</strong> صحيح <strong>ايڊيشن</strong> چونڊڻ لاءِ پڪ ڪريو. Open Library ۾، توهان وٽ ”ورڪ“ ۽ ”ايڊيشن“ آهن. هڪ ”ورڪ“ ٿي سگهي ٿو ”Harry Potter and the Philosopher's Stone“. هڪ ”ايڊيشن“ ٿي سگهي ٿو: 1997 جي پهرين ايڊيشن جيڪا بلومسبي طرفان شايع ٿيل آهي، 256 صفحن سان. 2003 جي پيپر بيڪ ايڊيشن جيڪا رينڪوسٽ بڪس طرفان شايع ٿيل آهي، 223 صفحن سان. 2000 جي پولش ترجمو “Harry Potter I Kamie Filozoficzn” ميڊيا روڊزينا طرفان، 328 صفحن سان. انهن سڀني ايڊيشنن جا مختلف ISBN ۽ مختلف مواد آهن، تنهنڪري صحيح چونڊ ڪرڻ جي پڪ ڪريو! ريڪارڊ کي ايڊٽ ڪريو (يا جيڪڏهن ڪو موجود نه هجي ته ٺاهيو)، ۽ جيترو مفيد معلومات شامل ڪري سگهو، شامل ڪريو! توهان هتي آهيو، تنهنڪري ريڪارڊ کي واقعي شاندار بڻايو. "ID Numbers" هيٺ "Anna’s Archive" چونڊيو ۽ ڪتاب جو MD5 Anna’s Archive مان شامل ڪريو. هي URL ۾ "/md5/" کان پوءِ ڊگهو اکرن ۽ انگن جو سلسلو آهي. Anna’s Archive ۾ ٻيا فائلون ڳولڻ جي ڪوشش ڪريو جيڪي هن ريڪارڊ سان پڻ ملن ٿيون، ۽ انهن کي پڻ شامل ڪريو. مستقبل ۾ اسان انهن کي Anna’s Archive جي سرچ پيج تي نقلن جي طور تي گروپ ڪري سگهون ٿا. جڏهن توهان مڪمل ڪريو، ته URL لکو جيڪو توهان تازو ڪيو آهي. جڏهن توهان گهٽ ۾ گهٽ 30 ريڪارڊ Anna’s Archive MD5s سان اپڊيٽ ڪريو، ته اسان کي هڪ <a %(a_contact)s>ايميل</a> موڪليو ۽ اسان کي فهرست موڪليو. اسان توهان کي Anna’s Archive لاءِ مفت ميمبرشپ ڏينداسين، ته جيئن توهان هي ڪم وڌيڪ آساني سان ڪري سگهو (۽ توهان جي مدد جي شڪريه طور). اهي اعليٰ معيار جا ايڊٽس هجڻ گهرجن جيڪي وڏي مقدار ۾ معلومات شامل ڪن، ٻي صورت ۾ توهان جي درخواست رد ڪئي ويندي. توهان جي درخواست پڻ رد ڪئي ويندي جيڪڏهن ڪنهن به ايڊٽ کي Open Library جي موڊريٽرن طرفان واپس ورتو يا درست ڪيو ويو. Open Library سان ڳنڍڻ جيڪڏهن توهان اسان جي ڪم جي ترقي ۽ آپريشن ۾ اهم طور تي شامل ٿي وڃو ٿا، ته اسان توهان سان وڌيڪ عطيا جي آمدني شيئر ڪرڻ تي بحث ڪري سگهون ٿا، جيئن توهان ضروري سمجهو. اسان صرف ميزباني لاءِ ادائيگي ڪنداسين جڏهن توهان سڀ ڪجهه سيٽ اپ ڪري ڇڏيو آهي، ۽ ثابت ڪيو آهي ته توهان اپڊيٽس سان آرڪائيو کي اپڊيٽ رکڻ جي قابل آهيو. ان جو مطلب آهي ته توهان کي پهرين 1-2 مهينن لاءِ پنهنجي کيسي مان ادائيگي ڪرڻي پوندي. توهان جو وقت معاوضي ۾ نه ڏنو ويندو (۽ نه ئي اسان جو)، ڇو ته هي خالص رضاڪارانه ڪم آهي. اسان ميزباني ۽ VPN خرچن کي ڍڪڻ لاءِ تيار آهيون، شروعات ۾ $200 في مهيني تائين. اهو هڪ بنيادي ڳولا سرور ۽ DMCA-محفوظ پراکسي لاءِ ڪافي آهي. ميزباني خرچ مهرباني ڪري <strong>اسان سان رابطو نه ڪريو</strong> اجازت لاءِ پڇڻ، يا بنيادي سوالن لاءِ. عمل لفظن کان وڌيڪ بلند ڳالهائيندا آهن! سڄي معلومات موجود آهي، تنهنڪري بس پنهنجو آئيني سيٽ اپ ڪرڻ سان اڳتي وڌو. مسئلن ۾ اچڻ تي اسان جي Gitlab تي ٽڪيٽون يا مرج درخواستون پوسٽ ڪرڻ لاءِ آزاد محسوس ڪريو. اسان کي توهان سان گڏ ڪجهه آئيني-مخصوص خاصيتون ٺاهڻ جي ضرورت پئجي سگهي ٿي، جهڙوڪ "Anna’s Archive" کان توهان جي ويب سائيٽ جي نالي ڏانهن ري برانڊنگ، (شروعات ۾) صارف اڪائونٽس کي غير فعال ڪرڻ، يا ڪتابن جي صفحن کان اسان جي مکيه سائيٽ ڏانهن لنڪ ڪرڻ. هڪ دفعو توهان جو آئيني هلڻ شروع ٿي وڃي، مهرباني ڪري اسان سان رابطو ڪريو. اسان توهان جي OpSec جو جائزو وٺڻ چاهينداسين، ۽ هڪ دفعو اهو مضبوط ٿي وڃي، اسان توهان جي آئيني ڏانهن لنڪ ڪنداسين، ۽ توهان سان ويجهو ڪم ڪرڻ شروع ڪنداسين. هن طريقي سان تعاون ڪرڻ لاءِ ڪنهن به رضاڪار جو اڳواٽ شڪريو! اهو ڪم ڪمزور دلين لاءِ ناهي، پر اهو انساني تاريخ جي سڀ کان وڏي واقعي ۾ کليل لائبريري جي عمر کي مضبوط ڪندو. شروع ڪرڻ Anna’s Archive جي لچڪ وڌائڻ لاءِ، اسان مرر هلائڻ لاءِ رضاڪارن جي ڳولا ڪري رهيا آهيون. توهان جو ورزن واضح طور تي هڪ آئيني طور تي ممتاز آهي، جهڙوڪ "باب جو آرڪائيو، هڪ Anna’s Archive آئيني". توهان هن ڪم سان لاڳاپيل خطرن کي قبول ڪرڻ لاءِ تيار آهيو، جيڪي اهم آهن. توهان کي آپريشنل سيڪيورٽي جي گہرائي سان سمجهه آهي جيڪا گهربل آهي. <a %(a_shadow)s>انهن</a> <a %(a_pirate)s>پوسٽن</a> جو مواد توهان لاءِ خود واضح آهي. شروعات ۾ اسان توهان کي اسان جي پارٽنر سرور ڊائون لوڊ تائين رسائي نه ڏينداسين، پر جيڪڏهن شيون سٺيون ٿين ٿيون، ته اسان اهو توهان سان شيئر ڪري سگهون ٿا. توهان Anna’s Archive جو اوپن سورس ڪوڊبيس هلائيندا آهيو، ۽ توهان باقاعدگي سان ڪوڊ ۽ ڊيٽا ٻنهي کي اپڊيٽ ڪندا آهيو. توهان اسان جي <a %(a_codebase)s>ڪوڊبيس</a> ۾ تعاون ڪرڻ لاءِ تيار آهيو — اسان جي ٽيم سان گڏجي — هن کي ممڪن بڻائڻ لاءِ. اسان هن جي ڳولا ڪري رهيا آهيون: مرر: رضاڪارن لاءِ سڏ ٻي عطيا ڏيو. اڃا تائين ڪا به عطيا ناهي. <a %(a_donate)s>منهنجي پهرين عطيا ڏيو.</a> عطيا جي تفصيلات عوامي طور تي ظاهر ناهن ڪيون وينديون. منهنجا چندا 📡 اسان جي مجموعي جي بلڪ مررنگ لاءِ، <a %(a_datasets)s>Datasets</a> ۽ <a %(a_torrents)s>Torrents</a> صفحا چيڪ ڪريو. گذريل 24 ڪلاڪن ۾ توهان جي IP پتي مان ڊائون لوڊ: %(count)s. 🚀 تيز ڊائون لوڊ حاصل ڪرڻ ۽ برائوزر چيڪس کي ڇڏي ڏيڻ لاءِ، <a %(a_membership)s>ممبر بڻجو</a>. ساٿي ويب سائيٽ تان ڊائون لوڊ ڪريو مهرباني ڪري انا جو آرڪائيو کي مختلف ٽيب ۾ برائوز ڪرڻ جاري رکو جڏهن انتظار ڪري رهيا آهيو (جيڪڏهن توهان جو برائوزر پس منظر ٽيبز کي ريفريش ڪرڻ جي حمايت ڪري ٿو). مهرباني ڪري ڪيترن ئي ڊائون لوڊ صفحن کي هڪ ئي وقت لوڊ ڪرڻ لاءِ انتظار ڪريو (پر مهرباني ڪري هر سرور تي هڪ ئي وقت صرف هڪ فائل ڊائون لوڊ ڪريو). هڪ دفعو توهان کي ڊائون لوڊ لنڪ ملي ٿي ته اها ڪيترن ئي ڪلاڪن لاءِ صحيح آهي. انتظار ڪرڻ لاءِ مهرباني، هي ويب سائيٽ کي هر ڪنهن لاءِ مفت ۾ رسائي لائق رکڻ ۾ مدد ڪري ٿو! 😊 🔗 هن فائل لاءِ سڀئي ڊائون لوڊ لنڪس: <a %(a_main)s>فائل مکيه صفحو</a>. ❌ سست ڊائون لوڊ Cloudflare VPNs يا ٻي صورت ۾ Cloudflare IP پتيون ذريعي موجود نه آهن. ❌ سست ڊائون لوڊ صرف سرڪاري ويب سائيٽ ذريعي موجود آهن. دورو ڪريو %(websites)s. 📚 ڊائون لوڊ ڪرڻ لاءِ هيٺين URL استعمال ڪريو: <a %(a_download)s>هاڻي ڊائون لوڊ ڪريو</a>. هر ڪنهن کي مفت ۾ فائلون ڊائونلوڊ ڪرڻ جو موقعو ڏيڻ لاءِ، توهان کي هن فائل کي ڊائونلوڊ ڪرڻ کان اڳ انتظار ڪرڻو پوندو. مهرباني ڪري <span %(span_countdown)s>%(wait_seconds)s</span> سيڪنڊ انتظار ڪريو هن فائل کي ڊائونلوڊ ڪرڻ لاءِ. خبردار: گذريل 24 ڪلاڪن ۾ توهان جي IP پتي مان ڪيترائي ڊائون لوڊ ٿي چڪا آهن. ڊائون لوڊ عام کان سست ٿي سگهن ٿا. جيڪڏهن توهان VPN، شيئر ٿيل انٽرنيٽ ڪنيڪشن استعمال ڪري رهيا آهيو، يا توهان جو ISP IPs شيئر ڪري ٿو، ته هي خبردار ان جي ڪري ٿي سگهي ٿو. محفوظ ڪريو ❌ ڪجهه غلط ٿي ويو. مهرباني ڪري ٻيهر ڪوشش ڪريو. ✅ محفوظ ٿي ويو. مهرباني ڪري صفحو ٻيهر لوڊ ڪريو. پنهنجو ڊسپلي نالو تبديل ڪريو. توهان جو سڃاڻپ ڪندڙ (”#“ کان پوءِ وارو حصو) تبديل نٿو ڪري سگهجي. پروفائل ٺاهي وئي <span %(span_time)s>%(time)s</span> ترميم فهرستون هڪ نئون فهرست ٺاهيو فائل ڳولي ۽ "فهرست" ٽيب کوليو. ڪو به فهرست موجود ناهي پروفائل نه مليو. پروفائل هن وقت، اسان ڪتابن جي درخواستن کي پورو نٿا ڪري سگهون. مھرباني ڪري اسان کي پنھنجا ڪتابن جا درخواستون اي ميل نه ڪريو. مھرباني ڪري پنھنجا درخواستون Z-Library يا Libgen فورمن تي ڪريو. انا جي آرڪائيو ۾ رڪارڊ DOI: %(doi)s ڊائون لوڊ ڪريو SciDB Nexus/STC اڃا تائين ڪا اڳڪٿي موجود ناهي. فائل ڊائون لوڊ ڪريو <a %(a_path)s>انا جي آرڪائيو</a> مان. انساني علم جي رسائي ۽ ڊگهي مدي واري حفاظت جي حمايت لاءِ، <a %(a_donate)s>ممبر</a> بڻجو. بونس طور، 🧬&nbsp;SciDB ميمبرن لاءِ تيزيءَ سان لوڊ ٿئي ٿو، بغير ڪنهن حد جي. ڪم نٿو ڪري؟ <a %(a_refresh)s>تازو ڪريو</a> ڪوشش ڪريو. Sci-Hub مخصوص ڳولا جو ميدان شامل ڪريو تفصيلات ۽ ميٽا ڊيٽا تبصرا ڳوليو شايع ٿيڻ جو سال ترقي يافته رسائي مواد ڊسپلي لسٽ ٽيبل فائل جو قسم ٻولي ترتيب ڏيو وڏو ترين سڀ کان وڌيڪ لاڳاپيل نئون ترين (فائل سائيز) (اوپن سورس) (اشاعت جو سال) قديم ترين بي ترتيب ننڍو ترين ذريعو AA پاران اسڪراپ ۽ اوپن سورس ڪيو ويو ڊجيٽل قرض ڏيڻ (%(count)s) جرنل آرٽيڪلز (%(count)s) اسان کي %(in)s ۾ ميچ مليا آهن. توهان اتي مليو URL استعمال ڪري سگهو ٿا جڏهن <a %(a_request)s>فائل جي درخواست ڪريو</a>. ميٽا ڊيٽا (%(count)s) ڪوڊز ذريعي سرچ انڊيڪس کي ڳولڻ لاءِ، <a %(a_href)s>ڪوڊز ايڪسپلورر</a> استعمال ڪريو. سرچ انڊيڪس هر مهيني اپڊيٽ ڪيو ويندو آهي. هن وقت ان ۾ %(last_data_refresh_date)s تائين داخلا شامل آهن. وڌيڪ ٽيڪنيڪل معلومات لاءِ، %(link_open_tag)sdatasets صفحو</a> ڏسو. خارج ڪريو صرف شامل ڪريو اڻ چيڪ ٿيل وڌيڪ… اڳيون … پويون هي سرچ انڊيڪس هن وقت انٽرنيٽ آرڪائيو جي ڪنٽرولڊ ڊجيٽل لينڊنگ لائبريري مان ميٽا ڊيٽا شامل آهي. <a %(a_datasets)s>اسان جي datasets بابت وڌيڪ</a>. وڌيڪ ڊجيٽل لينڊنگ لائبريرين لاءِ، ڏسو <a %(a_wikipedia)s>وڪيپيڊيا</a> ۽ <a %(a_mobileread)s>موبائل ريڊ وڪي</a>. DMCA / ڪاپي رائيٽ دعوائن لاءِ <a %(a_copyright)s>هتي ڪلڪ ڪريو</a>. ڊائون لوڊ وقت ڳولا دوران غلطي. ڪوشش ڪريو <a %(a_reload)s>صفحو ٻيهر لوڊ ڪرڻ</a>. جيڪڏهن مسئلو جاري رهي، مهرباني ڪري اسان کي %(email)s تي اي ميل ڪريو. تيز ڊائون لوڊ حقيقت ۾، ڪو به اسان جي <a %(a_torrents)s>متحده ٽورنٽس جي فهرست</a> کي سيڊنگ ڪري انهن فائلن کي محفوظ ڪرڻ ۾ مدد ڪري سگهي ٿو. ➡️ ڪڏهن ڪڏهن اهو غلط طريقي سان ٿئي ٿو جڏهن سرچ سرور سست آهي. اهڙين حالتن ۾، <a %(a_attrs)s>ري لوڊ ڪرڻ</a> مدد ڪري سگهي ٿو. ❌ هي فائل مسئلن سان ٿي سگهي ٿي. پيپر ڳولي رهيا آهيو؟ هي سرچ انڊيڪس هن وقت مختلف ميٽا ڊيٽا ذريعن مان ميٽا ڊيٽا شامل آهي. <a %(a_datasets)s>اسان جي datasets بابت وڌيڪ</a>. لکيل ڪم لاءِ دنيا ۾ ڪيترائي، ڪيترائي ميٽا ڊيٽا ذريعا آهن. <a %(a_wikipedia)s>هي وڪيپيڊيا صفحو</a> هڪ سٺو شروعات آهي، پر جيڪڏهن توهان کي ٻين سٺين فهرستن جي ڄاڻ آهي، مهرباني ڪري اسان کي ٻڌايو. ميٽا ڊيٽا لاءِ، اسان اصل رڪارڊ ڏيکاريون ٿا. اسان رڪارڊز جو ڪو به مرجنگ نٿا ڪريون. اسان وٽ هن وقت دنيا جو سڀ کان جامع اوپن ڪيٽلاگ آهي ڪتابن، پيپرن، ۽ ٻين لکيل ڪم جو. اسان Sci-Hub، Library Genesis، Z-Library، <a %(a_datasets)s>۽ وڌيڪ</a> کي مرر ڪريون ٿا. <span class="font-bold">ڪو به فائل نه مليو.</span> گهٽ يا مختلف ڳولا جا لفظ ۽ فلٽر استعمال ڪريو. نتيجا %(from)s-%(to)s (%(total)s مجموعي) جيڪڏهن توهان کي ٻيون "شيڊو لائبريريون" ملن ٿيون جيڪي اسان کي مرر ڪرڻ گهرجن، يا جيڪڏهن توهان وٽ ڪو سوال آهي، مهرباني ڪري اسان سان %(email)s تي رابطو ڪريو. %(num)d جزوي ميچ %(num)d+ جزوي ميچ ڊجيٽل لينڊنگ لائبريرين ۾ فائلون ڳولڻ لاءِ باڪس ۾ ٽائپ ڪريو. اسان جي ڪيٽلاگ ۾ ڳولا ڪرڻ لاءِ باڪس ۾ ٽائپ ڪريو %(count)s سڌو ڊائون لوڊ ٿيندڙ فائلون، جن کي اسان <a %(a_preserve)s>هميشه لاءِ محفوظ رکون ٿا</a>. خاني ۾ ٽائپ ڪريو ڳولا لاءِ. اسان جي ڪيٽلاگ ۾ ڳولا ڪرڻ لاءِ باڪس ۾ ٽائپ ڪريو %(count)s اڪيڊمڪ پيپر ۽ جرنل آرٽيڪلز، جن کي اسان <a %(a_preserve)s>هميشه لاءِ محفوظ رکون ٿا</a>. لائبريرين مان ميٽا ڊيٽا ڳولڻ لاءِ باڪس ۾ ٽائپ ڪريو. هي مفيد ٿي سگهي ٿو جڏهن <a %(a_request)s>فائل جي درخواست ڪندي</a>. ٽپ: تيز نيويگيشن لاءِ ڪي بورڊ شارٽ ڪٽس استعمال ڪريو “/” (سرچ فوڪس)، “انٽر” (سرچ)، “j” (مٿي)، “k” (هيٺ)، “<” (پويون صفحو)، “>” (اڳيون صفحو). اهي ميٽا ڊيٽا رڪارڊ آهن، <span %(classname)s>ڊائون لوڊ ڪرڻ لائق فائلون نه آهن</span>. ڳولا جون سيٽنگون ڳولا ڊجيٽل قرض ڏيڻ ڊائون لوڊ ڪريو جرنل آرٽيڪل ميٽا ڊيٽا نئين ڳولا %(search_input)s - ڳولا ڳولا ۾ گهڻو وقت لڳي ويو، جنهن جو مطلب آهي ته توهان کي غلط نتيجا نظر اچي سگهن ٿا. ڪڏهن ڪڏهن <a %(a_reload)s>صفحو ٻيهر لوڊ ڪرڻ</a> مدد ڪري ٿو. ڳولا ۾ گهڻو وقت لڳي ويو، جيڪو وسيع سوالن لاءِ عام آهي. فلٽر ڳڻپون صحيح نه ٿي سگهن. وڏن اپلوڊز (10,000 فائلن کان وڌيڪ) لاءِ جيڪي Libgen يا Z-Library طرفان قبول نٿا ٿين، مهرباني ڪري اسان سان %(a_email)s تي رابطو ڪريو. Libgen.li لاءِ، پهريان <a %(a_forum)s >انهن جي فورم</a> تي صارف نالو %(username)s ۽ پاسورڊ %(password)s سان لاگ ان ٿيڻ کي يقيني بڻايو، ۽ پوءِ انهن جي <a %(a_upload_page)s >اپلوڊ صفحي</a> تي واپس وڃو. في الحال، اسان صلاح ڏيون ٿا ته نيون ڪتابون Library Genesis فورڪس تي اپلوڊ ڪريو. هتي هڪ <a %(a_guide)s>سولو گائيڊ</a> آهي. نوٽ ڪريو ته اسان هن ويب سائيٽ تي جيڪي ٻئي فورڪس انڊيڪس ڪندا آهيون، اهي ساڳئي اپلوڊ سسٽم مان ڪڍن ٿا. ننڍن اپلوڊز لاءِ (10,000 فائلن تائين) مهرباني ڪري انهن کي ٻنهي %(first)s ۽ %(second)s تي اپلوڊ ڪريو. متبادل طور، توهان انهن کي Z-Library تي <a %(a_upload)s>هتي</a> اپلوڊ ڪري سگهو ٿا. علمي مقالا اپلوڊ ڪرڻ لاءِ، مهرباني ڪري (Library Genesis کان علاوه) انهن کي <a %(a_stc_nexus)s>STC Nexus</a> تي پڻ اپلوڊ ڪريو. اهي نون مقالن لاءِ بهترين شيڊو لائبريري آهن. اسان اڃا تائين انهن کي ضم نه ڪيو آهي، پر اسان ڪنهن وقت ڪندا. توهان انهن جو <a %(a_telegram)s>اپلوڊ بوٽ Telegram تي</a> استعمال ڪري سگهو ٿا، يا جيڪڏهن توهان وٽ تمام گهڻا فائلون آهن ته هن طريقي سان اپلوڊ ڪرڻ لاءِ، انهن جي پن ٿيل پيغام ۾ ڏنل پتي سان رابطو ڪريو. <span %(label)s>گهڻو رضاڪار ڪم (USD$50-USD$5,000 انعام):</span> جيڪڏهن توهان اسان جي مشن لاءِ گهڻو وقت ۽/يا وسيلا وقف ڪري سگهو ٿا، ته اسان توهان سان ويجهو ڪم ڪرڻ چاهينداسين. آخرڪار، توهان اندروني ٽيم ۾ شامل ٿي سگهو ٿا. جيتوڻيڪ اسان جو بجيٽ تنگ آهي، اسان سڀ کان وڌيڪ شديد ڪم لاءِ <span %(bold)s>💰 مالي انعام</span> ڏيڻ جي قابل آهيون. <span %(label)s>هلڪو رضاڪارانه ڪم:</span> جيڪڏهن توهان صرف هتي ۽ اتي ڪجهه ڪلاڪ بچائي سگهو ٿا، ته اڃا تائين ڪيترائي طريقا آهن جن سان توهان مدد ڪري سگهو ٿا. اسان مستقل رضاڪارن کي <span %(bold)s>🤝 Anna’s Archive جي ميمبرشپ سان انعام ڏيون ٿا</span>. Anna’s Archive توهان جهڙن رضاڪارن تي ڀاڙيندو آهي. اسان سڀني عزم جي سطحن کي ڀليڪار ڪيون ٿا، ۽ اسان جي مدد لاءِ ٻه مکيه درجا آهن: جيڪڏهن توهان وقت رضاڪار نٿا ڪري سگهو، ته توهان اڃا به اسان جي مدد ڪري سگهو ٿا <a %(a_donate)s>پئسا عطيو ڪرڻ</a>، <a %(a_torrents)s>اسان جي ٽورنٽس کي سيڊ ڪرڻ</a>، <a %(a_uploading)s>ڪتاب اپلوڊ ڪرڻ</a>، يا <a %(a_help)s>پنهنجن دوستن کي Anna’s Archive بابت ٻڌائڻ</a>. <span %(bold)s>ڪمپنيون:</span> اسان پنهنجي مجموعن تائين تيز رفتار سڌو رسائي پيش ڪريون ٿا انٽرپرائز-سطح جي عطئي جي بدلي ۾ يا نون مجموعن جي بدلي ۾ (مثال طور نوان اسڪين، OCR’ed datasets، اسان جي ڊيٽا کي مالا مال ڪرڻ). <a %(a_contact)s>اسان سان رابطو ڪريو</a> جيڪڏهن توهان اهو آهيو. ڏسو پڻ اسان جو <a %(a_llm)s>LLM صفحو</a>. انعام اسان هميشه اهڙن ماڻهن جي ڳولا ۾ رهندا آهيون جن وٽ مضبوط پروگرامنگ يا جارحتي سيڪيورٽي مهارتون هجن ته جيئن شامل ٿي سگهن. توهان انسانيت جي ورثي کي محفوظ ڪرڻ ۾ هڪ سنجيده ڪوشش ڪري سگهو ٿا. شڪريي جي طور تي، اسان مضبوط تعاون لاءِ ميمبرشپ ڏيندا آهيون. وڏي شڪريي جي طور تي، اسان خاص طور تي اهم ۽ مشڪل ڪمن لاءِ مالي انعام ڏيندا آهيون. هن کي نوڪري جي متبادل طور نه ڏٺو وڃي، پر اهو هڪ اضافي ترغيب آهي ۽ خرچن ۾ مدد ڪري سگهي ٿو. اسان جو گهڻو ڪوڊ اوپن سورس آهي، ۽ اسان توهان جي ڪوڊ کان به اهو ئي مطالبو ڪنداسين جڏهن انعام ڏنو ويندو. ڪجهه استثنا آهن جن تي اسان انفرادي بنيادن تي بحث ڪري سگهون ٿا. انعام پهرين شخص کي ڏنو ويندو جيڪو ڪم مڪمل ڪري. بائونٽي ٽڪيٽ تي تبصرو ڪرڻ ۾ آزاد محسوس ڪريو ته ٻين کي ڄاڻ ڏيو ته توهان ڪجهه تي ڪم ڪري رهيا آهيو، ته جيئن ٻيا روڪي سگهن يا توهان سان گڏجي ڪم ڪرڻ لاءِ رابطو ڪري سگهن. پر خبردار رهو ته ٻيا اڃا به ان تي ڪم ڪرڻ لاءِ آزاد آهن ۽ توهان کان اڳتي وڌڻ جي ڪوشش ڪري سگهن ٿا. بهرحال، اسان خراب ڪم لاءِ انعام نٿا ڏيون. جيڪڏهن ٻه اعليٰ معيار جون جمع ڪرايون ويجهي وقت ۾ ڪيون وڃن (هڪ يا ٻن ڏينهن اندر)، اسان پنهنجي صوابديد تي ٻنهي کي انعام ڏيڻ جو انتخاب ڪري سگهون ٿا، مثال طور پهرين جمع ڪرائڻ لاءِ 100%% ۽ ٻي جمع ڪرائڻ لاءِ 50%% (تنهنڪري 150%% ڪل). وڏن انعامن لاءِ (خاص طور تي اسڪراپنگ انعامن لاءِ)، مهرباني ڪري اسان سان رابطو ڪريو جڏهن توهان ~5%% مڪمل ڪري چڪا آهيو، ۽ توهان کي يقين آهي ته توهان جو طريقو مڪمل سنگ ميل تائين پهچندو. توهان کي اسان سان پنهنجو طريقو شيئر ڪرڻو پوندو ته جيئن اسان راءِ ڏئي سگهون. پڻ، هن طريقي سان اسان فيصلو ڪري سگهون ٿا ته ڇا ڪرڻو آهي جيڪڏهن ڪيترائي ماڻهو انعام جي ويجهو اچي رهيا آهن، جهڙوڪ ممڪن طور تي انعام کي ڪيترن ئي ماڻهن کي ڏيڻ، ماڻهن کي گڏجي ڪم ڪرڻ جي حوصلا افزائي ڪرڻ، وغيره. خبردار: اعليٰ انعام وارا ڪم <span %(bold)s>مشڪل</span> آهن — اهو شايد آسان ڪمن سان شروع ڪرڻ ۾ عقلمندي آهي. اسان جي <a %(a_gitlab)s>Gitlab مسئلن جي فهرست</a> ڏانهن وڃو ۽ "Label priority" ذريعي ترتيب ڏيو. اهو تقريباً انهن ڪمن جو ترتيب ڏيکاري ٿو جن جي اسان کي پرواهه آهي. ڪمن جن ۾ واضح انعام نه آهن اڃا تائين ميمبرشپ لاءِ اهل آهن، خاص طور تي اهي جيڪي "Accepted" ۽ "Anna’s favorite" سان نشان لڳل آهن. توهان شايد "Starter project" سان شروع ڪرڻ چاهيو ٿا. هلڪو رضاڪار ڪم اسان وٽ هاڻي هڪ هم وقت ٿيل ميٽرڪس چينل پڻ آهي %(matrix)s. جيڪڏهن توهان وٽ ڪجهه ڪلاڪ آهن، ته توهان ڪيترن ئي طريقن سان مدد ڪري سگهو ٿا. پڪ ڪريو ته <a %(a_telegram)s>Telegram تي رضاڪارن جي چيٽ</a> ۾ شامل ٿيو. تعريف جي نشاني طور، اسان عام طور تي 6 مهينن جي “لکي لائبريرين” بنيادي سنگ ميلن لاءِ ڏيندا آهيون، ۽ وڌيڪ جاري رضاڪار ڪم لاءِ. سڀ سنگ ميلن لاءِ اعليٰ معيار جو ڪم گهربل آهي — خراب ڪم اسان کي وڌيڪ نقصان پهچائيندو آهي ۽ اسان ان کي رد ڪنداسين. مهرباني ڪري <a %(a_contact)s>اسان کي اي ميل ڪريو</a> جڏهن توهان سنگ ميل تي پهچو. %(links)s درخواستن جا لنڪس يا اسڪرين شاٽس جيڪي توهان پوريون ڪيون. Z-Library يا Library Genesis فورمز تي ڪتاب (يا پيپر، وغيره) جي درخواستن کي پورو ڪرڻ. اسان وٽ پنهنجو ڪتاب درخواست سسٽم ناهي، پر اسان انهن لائبريرين کي آئيني بڻائيندا آهيون، تنهنڪري انهن کي بهتر بڻائڻ Anna’s Archive کي به بهتر بڻائي ٿو. سنگ ميل ڪم ڪم تي منحصر آهي. اسان جي <a %(a_telegram)s>Telegram تي رضاڪارن جي چيٽ</a> تي پوسٽ ڪيل ننڍا ڪم. عام طور تي ميمبرشپ لاءِ، ڪڏهن ڪڏهن ننڍن انعامن لاءِ. اسان جي رضاڪار چيٽ گروپ ۾ ننڍا ڪم پوسٽ ڪيا ويا. جن مسئلن کي توهان حل ڪريو، انهن تي تبصرو ڪرڻ کي يقيني بڻايو، ته جيئن ٻيا توهان جي ڪم کي نقل نه ڪن. %(links)s رڪارڊن جا لنڪس جيڪي توهان بهتر ڪيا. توهان <a %(a_list)s >بي ترتيب metadata مسئلن جي فهرست</a> کي شروعاتي نقطي طور استعمال ڪري سگهو ٿا. ميٽا ڊيٽا کي بهتر ڪريو <a %(a_metadata)s>Open Library سان ڳنڍڻ</a> سان. اهي توهان کي انا جي آرڪائيو بابت ڪنهن کي ٻڌائيندي ۽ انهن جو توهان کي شڪريو ادا ڪندي ڏيکارڻ گهرجن. %(links)s لنڪس يا اسڪرين شاٽس. انا جي آرڪائيو جو پيغام ڦهلائڻ. مثال طور، AA تي ڪتابن جي سفارش ڪندي، اسان جي بلاگ پوسٽن سان ڳنڍيندي، يا عام طور تي ماڻهن کي اسان جي ويب سائيٽ ڏانهن هدايت ڪندي. پوري زبان جو ترجمو ڪريو (جيڪڏهن اهو اڳ ۾ مڪمل ٿيڻ جي ويجهو نه هو). <a %(a_translate)s>ويب سائيٽ جو ترجمو</a>. ايڊٽ تاريخ جو لنڪ ڏيکاريندي ته توهان اهم تعاون ڪيو. پنهنجي ٻولي ۾ Anna’s Archive لاءِ Wikipedia صفحو بهتر ڪريو. ٻين ٻولين ۾ AA جي Wikipedia صفحي مان معلومات شامل ڪريو، ۽ اسان جي ويب سائيٽ ۽ بلاگ مان. ٻين لاڳاپيل صفحن تي AA جا حوالا شامل ڪريو. رضاڪارانه ڪم ۽ انعام 