��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b 5  �d   �f &   h �   /h �  'i �  �j g  �l   �m J   �n k   Bo A   �o _   �o �  Pp 8  �q �   s �  �s �   �u �  �v �  ux �  0z �   �{ �  �| �  �~ 9   2� �   l� N   E� �  �� "   |� +  �� A   ˅    
�    -�    =� 3   S�    ��    �� "   �� !   ц "   �    � D   (� @  m�   �� G   �� 4   	�    >�    K�    Z�    s�    �� 
   �� 	   ��    ��    Ċ     ˊ    �    � 	   � 
   �    $�    0�    J�     j� <  �� f  ȍ �   /� &   �� Q  ޏ �   0� �   �� �   �� $   j� �   �� $    � �   E� )   �   � $   <� }  a� ;   ߗ ]   �    y� .  �� 3  �� �  �   ��    Ý <   ֝ h   � �   |� (   '� &   P� x   w� :   � E   +� *   q�    �� t   �� �  � 2   �� y   � �   `� S   7� .  �� ?   ��   �� �   �� �   �� B   $� �   g� �   � A   �� �   �� �   Ъ ^   �� G   � j   ,� 
   �� �   ��   <� A   \� 
   �� �   �� �   =�    � l  � �   x�   � �  � �   ��   �� �   �� U   �� O   � V   ?� m   �� X   � ^   ]� '   �� �   � b   k� O   μ    � s   .� L   �� �   � �   ��    6� �   G�   4� G  6� Q  ~� �   �� �   T� $  <� �   a� �   � �   �� `  C� �   �� �   7�    � �   %� �   �� �   �� c  L�    �� �   0� �   �� �  Y� v   � =  �� �   ��    �� ]   �� �   A� U   �� P  � �   k� [   � j   ^� #   ��   �� �   �� l  ��    5� M   O� �   �� �   6� �   �� �   �� �  �� �  �� '   :� R  b� r  ��   (� u  @�   ��   �� Z   �� z   5�    �� �   �� ,  }� r  �� �   � �   � �   
�    �� N  �� 6  � �   :�    �� �   � !  �� �   �� �  i� �   N� T   �    p� e  � 7  ��     �   4 �    �   �   � V  � Q   3 J   � �   � 9   �    	 '  	 �   =
 0  2 &   c A   � �  � t   � }   , V   � q   #   s    � j  � �    �   � J   D �   �      �   @   +    4 
   H (   S /   | 7   � =   � Z   " �   }     � 8    E   X J   � (   � 7    )   J k   t    �    �   �    �    �   � �   � �   b        <! �   M! �   " �   �" @  e# \  �$ �  & �  �' X  �) A   + 7   G+ &   + �   �+ �  X, �  !. (  �/ >  �0 	  2 (  3   >5 '   J6   r6 '  z7 L  �8    �9 �    : /  �: �   < =  �< �   �=    �> !   �> V   �> 	   7? t  A? {  �A )  2C �   \E �   F    �F �   �F �   �G A   �H r   �H �   7I �   1J �   (K g  �K Z   M 	  qM    {N �  �N �   jP �  �P �  �R   NU �   PW 
   X �   X �  �X    �Z @  �Z �  �[ �  �] �  |_ �   Ta   Cb A   _c �  �c �   Me y   f 8   �f V   �f    )g A   /g    qg 2   �g $   �g    �g �   h �  �h �  mk   Mm �   eo 	   0p �  :p B  �r ]  Au x  �v I  x Z  bz 
   �| �   �|    �}    �} �  �}      �� I  ��    � �  � �  �� �  k� �  0� �  � "  �� b  ��    C� �   a� l   � �  �� �  B� �   Ș �   u� �   � J  ��    �� �   � ?   �� V   ��    N� -   h� >   ��   ՝ �  � �  �� 4   8� y  m� �   �    � �   �� 5   9� y   o� �   � 2   �� V   �    :� G  O� 2  �� �  ʭ g  q� |   ٰ    V� g   f� �   α    �� �   �� �  L� �   ε B   m� .   �� "  ߶ &   � %  )� S  O� �  ��    P� q   o� �   � .  j� v  ��    � �   � 2   � �   :� �   �� �   �� �   �� V   `� &   �� �   �� 2   �� V  ��   M� �   b� �   7� V   %� 1   |� �   ��   c� k  j� �  �� �  �� ,   N� �   {� �   8� #  )� �   M� �  � �  �� &   p� �  �� "   g� �  �� W   @�    �� �  ��   �� �  �� �   ?�   1� �  7� �  �� �   �� �  u� y   � z   �� �  � S   � 5   T� w   �� �   �    ��    �� 6  �� �   �� /   ~�   �� u  ��   '� �  B� �  � �   �� u  ;    �    � �   �    � q  � $    !   >    `    f /   {    �    �    �    �    � 
               !    ) 
   E (   P    y    }    �    � �   � c   b (   �    � #   � $   " -   G /   u )   �    �    �    �    �    	    	    .	    7	    F	    M	 -   `	 $   �	    �	 !   �	 %   �	 3   
 .   I
    x
 $   �
 =   �
    �
 S    9   _    � _   � L        M    i "   {    �    �    �    �    �    
    
    #
    )
 
   @
 	   N
 
   X
    c
 &   f
    �
    �
 	   �
    �
 	   �
    �
    �
    �
 	   �
                 6    >    [    y    �    �    � "   � 	   �    �    �        	        #    A    H    Y l   k �   � f   t �   � �  � v   d    � !   �    	        $    0 	   H !   R R   t 1   � V   �    P 6   o -   � i   � V   > �   � R   j 9   �    �        ) 	   0    :    C    R    d    i    }    �    �    �    �    � 
   �    � 
   �    	     	   %    /    @    L    c   x    �    �    � &   �    � K   � Z    *   o /   � 0   �    �         {       �    � )   � t   �    <    T z   a (   � 5   ^   ;! �   �! �   !" �   �" 5   �#   �# u   �$ 	  O% �   Y&    S'    n' T   u' ?   �' Y   
( Z   d( S   �( F   ) `   Z)    �) +   �)    *    * S   )* $   }*    �*    �*    �*    �* w   �*    Y+ %   f+ R   �+    �+    �+ O   
, T   ], k  �,    .    5. �   J.    �.    �.    /    	/     / -   -/ T  [/ L   �0 	   �0    1    1 )  "1 #   L2    p2    y2 �   �2    -3 9   43 /   n3    �3    �3 %   �3 �   �3    }4 
   �4 T   �4 #   �4    5    15    :5 0   P5 k   �5    �5 7   �5 �   56 b   �6 Z   B7    �7 �   �7 G   �8    �8 )   �8    9 �   .9 �   �9    �: T   �: �   �: �   �;    o<    �<    �< k  �< 2   !> Q   T>    �>    �>    �> �   �>    �?    �? *   �? =    @    >@    F@ #   a@    �@ 8  �@ T  �B �  2D 9   F 4   XF    �F 8   �F   �F �   �G �   �H    XI �   xI �  aJ &   �K    L �  0L C  0N 
   tO    O 4   �O    �O   �O    �P   
Q �  )R �   �S    �T n   �T !   2U #   TU i   xU   �U �   �V �   �W v  vX c   �Y   QZ T   S[ �   �[ �   I\ M   �\ �   G] *   �] "   ^    9^ 
   K^    V^ #   m^ "   �^ F   �^ /   �^ 	   +_ 1   5_ '  g_ %   �` �   �` �   �a �   !b $   �b    �b    �b    c /   c    Gc .   fc %   �c �   �c 0   �d �   �d    ue �   �e y   9f g   �f   g �   (h '   �h �   �h 	   yi   �i ^   �j    �j �  
k    �l    �l    �l    �l 3   �l 	   -m    7m 9   =m �   wm �   "n    �n    o    
o �   (o   �o �   �p }   �q    'r    =r    Sr    ir    �r    �r    �r ;   �r %   �r �  s E   �t    u d   u d   �u G   �u f   1v M   �v N   �v    5w b   >w D   �w �   �w L   ix H   �x    �x �   y Y   �y B   Lz `   �z S   �z <   D{ 
   �{ :   �{ p   �{ �   8| 3   �| �   }    �} �   �} N   �~ X   �~ �   F    � �  � �   ��    ��    �� 	   ˂ 	   Ղ �   ߂ +   ȃ `   � �   U� �   � �   � �   u� �   =� �   ؇ �   k� T   $�   y� l   �� o  �� �   o� �   � 
   �� 
   � 
   � �   #� 
   �� 
   �� L   Ύ Z   � �   v� 
   @� W   N� ?   �� q   � 5   X� s   �� ^   � 
   a� �   o� 
   �� 
   
� 
   �   &� �   3� �  �� 
   f� 	   t� 	   ~� �   ��    &�    A� �   `� �   H� !   �    �    � 2   :� 6   m� 9   ��    ޚ    �� �   �    � x  � �   �� |   h� O   � �   5� �   ӟ �    !  ~� �   �� �   K� v   ϥ    F� [  [�    �� I  ԧ �   � �   �� �   �    e� �   �� �   2� l   ƭ 1   3� 8   e�    �� %   �� 
   ٮ    �    �� �   	� 
   � g   �� ,   f� 	   ��    ��    ��    �� x   ϰ S   H� @   ��   ݱ /   � 	   �    �    #� $   >�    c� 
   u�    �� 
   �� 	   �� 
   �� 	   �� 
   �� (   �� �   �    �� 
   ��    �� 
   ��    �� 
   ɴ    ״ 
   �    �    � +   � i   J�    �� 5   ŵ \   �� �   X� �   �� �   �� �   \� �   L�   �    � �   %� 8   �� L   � D   9� �   ~� �   9� F   � d   N�    �� m   ƾ s   4� �   ��    A�     H�    i�    �    �� )   ��    �� #   ��     �    	�    "� #   =�    a�    y�    ��    ��    ��    ��    ��    ��    �� *   �� +   '� z   S� �   �� )   R�     |� o   �� i   
� w   w� /   �� $   � 6   D� 9   {� @   �� 0   �� �   '� �   �� *  ��    � 8   /� �   h� *   �� �   � �   �� ~   �� C   4�    x� �   �� �   %� }   �� 0   4� X   e� �   ��    �� 4   ��    �� 7   �� w   0� j   ��    �    �    $� "   +� �   N� :   ��    � /   8� #   h�    �� $   �� 7   ��    � q   � <   �� �   �� Y   �� K   � �   e� B   
� !   M�    o� !   ��     �� !   ��     �� !   � 6   7� <   n� +  �� M   �� 7   %� ?   ]� .   ��    �� z   �� n   O� �   �� �   ��    >� �   G� !   ��    �� �   	�    �� (   �� ?   
� ;   M� Z   �� N   �� R   3�    �� !   �� �   �� 2   i� !   �� O   �� �   � @   �� |   9� j   �� Q   !� j   s� #   �� <   � `   ?�    �� `   ��    � �   5� 8   �� :   �    X� '   n� �   �� T   X� N   �� �   �� ^   �� M   �� �   6� �   ��    N�    V� N   j� T   ��    �    &�    F�    V�    l� -   |� �   �� i   :�    �� #   �� +   �� 2   � *   G� �   r� 7   �� y   /� Q   �� #   �� w   � �   �� 5   k� V   ��    �� I   � <   O�    �� e   �� j   � 2   m� S   ��    �� 5   � V   7�    �� v   �� )   �    ?� %   T� ^   z� �   ��    ��    ��    �� 8   ��    !� H   8� 6   �� �   �� �   \�    �� D   �� �   =�     �� �   � B   �� !   �� N   � �   Z�    9�    A�    C�    E� ,   [� 4   �� 9   ��    ��    �    (� #   1� 8   U� 9   ��    �� N   �� ;   �    Z�    n� '   ��    ��    �� P   �� �   -� K   !     m     y  �   �  Z  4 O   � 
   � T  � �  B (   � b   �    ^   }    � a   �    	    /	 �  @	    
 S   & y   z f   � ]   [    � Z   � 8   1
    j
 t   �
 <   �
 -   9 W   g ,   � Q   � �   > �   � &   | �   � W  N �   � )   Z J  � �   � �   �   � �   � '   S     { �   � 8   I �  � `   l 6   � 
        �  +    � �   � 4  � S  � (  &! @   O" >   �" I   �" *   # -   D# Q   r# ^   �#    #$     0$ :   Q$    �$    �$ 6   �$ \   �$ +   W%    �% `   �% �   �% �   �&    K'    ]'    r' E   �' X   �' K    ( �   l( i   .)    �)    �) �   �) 	   �* �   �* �  ,+ �   �- F   �. %   �.    /    /    %/ A   )/ -   k/ v   �/ �   0 U   �0    F1    Y1 %   p1    �1 K   �1    �1 <   2    H2 7   O2 2   �2    �2    �2 Z   �2    ,3    23 &   G3 !   n3    �3 a   �3 �   �3 ^   �4 Z   5 R   l5 �   �5 
   u6    �6 �   �6 �   U7 �   8    �8 n   �8 P   B9 E   �9 e   �9 h   ?: Q   �:    �: `   ;    r;    �;    �;    �;    �;    �;    �;    
<    (<    =< *   B< *   m< *   �<    �< /   �< *   =    >=    ^=    {= 9   �=    �=    �= 6   �= ,   #> e   P> /   �>    �>    �> %   ?    9?    V? A   j? +   �? H   �? �   !@    �@    �@    A    A 4   5A 	   jA    tA    �A m   �A    
B !   (B    JB 
   QB 	   \B 8   fB    �B �   �B    �C    �C    �C #   D V   )D    �D %   �D W   �D -   E -   FE &   tE    �E a   �E )   F    .F    FF -   WF F   �F    �F *   �F    G o   3G V   �G I   �G    DH    LH 	   cH    mH    H    �H ^  �H t   J    �J    �J (   �J    �J )   �J    K 
   K k   !K J   �K �   �K    �L )   �L �   �L )   dM %   �M (   �M +   �M 3   	N +   =N    iN    �N ;   �N    �N !   �N 4   O ,  EO P   rP @   �P [   Q    `Q �   |Q &   R    /R Q   BR 
   �R '   �R    �R 8   �R P    S '   qS �   �S    IT    [T "   oT    �T    �T    �T     �T    �T    U ^   U   }U N   �V &   �V �   �V �   �W    tX    �X    �X    �X    �X    �X    �X    Y    +Y    =Y    PY    _Y 
   pY    ~Y    �Y    �Y    �Y    �Y %   �Y �   �Y �   �Z ^  �[   ] �  _ �   �` �  �a    Bc   Kc    Pd �   id �   [e �  7f �   �g d  �h 9   �i �   %j =   �j    k G   /k U   wk d   �k i   2l �   �l �   >m �   �m 2  �n    �p �   �p �   �q X   dr �   �r    cs �   vs i  Et �   �u �   qv    4w V   =w X   �w �   �w �   �x V   Py y   �y 
   !z    /z    Lz D   kz &   �z 
   �z �   �z >   n{ s   �{ !   !| �   C| �   �| I   g} O   �} @   ~ X   B~ \   �~ )   �~ �   " h   � x   � q   ��    �� 7   � +   >� _   j� 2   ʁ    ��    � L   
�    W�    h�    � /   �� 6   �� H   �    6�    P�    ]�    e� 	   k� P   u� l   ƃ S   3� 4   ��    �� %   Ą (   � 
   �    !�    *�    2�    ;�    B�    H�    P�    _� 
   e�    p�    y� 	   ��    ��    ��    �� 
   ȅ 
   Ӆ    ޅ    � #   � "   �     4� }   U�    ӆ e   � �   P�    �    "�    2�    B� 	   K�    U� 	   Y� �   c� �   � K   ��    �� !   � y   �    �� z   �� �   � &   ��    � �   �� �   ~� D   G� �   �� �   9� ,   �� �   �    u�     �� Q   �� �   � #   �� �   �� �   S� �   ֑ R   ��    �� 	   �    �    -�    9� 
   N�    Y�    m� �   �� c    � �   �� �   � �   ە Y   �� I   � �  b� Y  � �   l�    b�   � E  ��    D� �   L� �   �� �   � K  �� �  � u   �� m  �    �� >   �� �   ߦ I  m� B   �� �   ��    ک    �    � �   �� <   �� ]   ª 0    � b   Q� K   �� T    � '   U� �   }� =   &� 0   d� N   �� �   �    ή  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: hr
Language-Team: hr <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library je popularna (i ilegalna) knjižnica. Preuzeli su kolekciju Library Genesis i učinili je lako pretraživom. Osim toga, postali su vrlo učinkoviti u poticanju novih doprinosa knjigama, motivirajući korisnike koji doprinose raznim pogodnostima. Trenutno ne vraćaju te nove knjige natrag u Library Genesis. I za razliku od Library Genesis, ne čine svoju kolekciju lako preslikivom, što sprječava široko očuvanje. To je važno za njihov poslovni model, budući da naplaćuju pristup svojoj kolekciji u velikim količinama (više od 10 knjiga dnevno). Ne donosimo moralne sudove o naplaćivanju novca za masovni pristup ilegalnoj zbirci knjiga. Nema sumnje da je Z-Library uspješno proširio pristup znanju i nabavio više knjiga. Mi smo ovdje samo da obavimo svoj dio posla: osiguramo dugoročno očuvanje ove privatne zbirke. - Ana i tim (<a %(reddit)s>Reddit</a>) U originalnom izdanju Pirate Library Mirror (UREDI: premješteno na <a %(wikipedia_annas_archive)s>Annin Arhiv</a>), napravili smo presliku Z-Library, velike ilegalne kolekcije knjiga. Kao podsjetnik, ovo smo napisali u tom originalnom blog postu: Ta zbirka datira iz sredine 2021. U međuvremenu, Z-Library raste nevjerojatnom brzinom: dodali su oko 3,8 milijuna novih knjiga. Naravno, ima nekih duplikata, ali većina se čini kao legitimno nove knjige ili kvalitetniji skenovi prethodno poslanih knjiga. To je velikim dijelom zbog povećanog broja volontera moderatora u Z-Libraryju i njihovog sustava masovnog učitavanja s deduplikacijom. Želimo im čestitati na tim postignućima. Sretni smo što možemo objaviti da smo dobili sve knjige koje su dodane u Z-Library između našeg posljednjeg zrcala i kolovoza 2022. Također smo se vratili i prikupili neke knjige koje smo propustili prvi put. Sve u svemu, ova nova zbirka ima oko 24TB, što je mnogo veće od prethodne (7TB). Naše zrcalo sada ukupno ima 31TB. Opet smo deduplicirali protiv Library Genesis, budući da su već dostupni torrenti za tu zbirku. Molimo posjetite Pirate Library Mirror kako biste provjerili novu zbirku (UREDI: premješteno na <a %(wikipedia_annas_archive)s>Annin Arhiv</a>). Tamo ima više informacija o tome kako su datoteke strukturirane i što se promijenilo od prošlog puta. Nećemo povezivati s njim odavde, jer je ovo samo blog stranica koja ne hostira nikakve ilegalne materijale. Naravno, seedanje je također odličan način da nam pomognete. Hvala svima koji seedaju naš prethodni set torrenta. Zahvalni smo na pozitivnom odgovoru i sretni što postoji toliko ljudi koji se brinu o očuvanju znanja i kulture na ovaj neobičan način. 3x nove knjige dodane u Pirate Library Mirror (+24TB, 3,8 milijuna knjiga) Pročitajte prateće članke od TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a> - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) prateći članci od TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a> Ne tako davno, "sjene-knjižnice" su bile na izdisaju. Sci-Hub, masivna ilegalna arhiva akademskih radova, prestala je primati nova djela zbog tužbi. "Z-Library", najveća ilegalna knjižnica knjiga, vidjela je kako su njezini navodni tvorci uhićeni zbog kaznenih optužbi za kršenje autorskih prava. Nevjerojatno su uspjeli pobjeći od uhićenja, ali njihova knjižnica nije ništa manje ugrožena. Neke zemlje već provode verziju ovoga. TorrentFreak <a %(torrentfreak)s>izvijestio</a> da su Kina i Japan uveli AI iznimke u svoje zakone o autorskim pravima. Nije nam jasno kako to djeluje s međunarodnim ugovorima, ali svakako pruža zaštitu njihovim domaćim tvrtkama, što objašnjava ono što smo vidjeli. Što se tiče Annine Arhive — nastavit ćemo naš podzemni rad ukorijenjen u moralnom uvjerenju. Ipak, naša najveća želja je izaći na svjetlo i pojačati naš utjecaj legalno. Molimo reformirajte autorska prava. Kada se Z-Library suočila s gašenjem, već sam napravio sigurnosnu kopiju cijele knjižnice i tražio platformu za njezino smještanje. To je bila moja motivacija za pokretanje Annine Arhive: nastavak misije iza tih ranijih inicijativa. Od tada smo narasli do najveće sjene-knjižnice na svijetu, s više od 140 milijuna zaštićenih tekstova u raznim formatima — knjige, akademski radovi, časopisi, novine i dalje. Moj tim i ja smo ideolozi. Vjerujemo da je očuvanje i smještanje ovih datoteka moralno ispravno. Knjižnice diljem svijeta suočavaju se s rezovima u financiranju, a ne možemo ni povjeriti naslijeđe čovječanstva korporacijama. Tada je došla umjetna inteligencija. Gotovo sve velike tvrtke koje grade LLM-ove kontaktirale su nas kako bi trenirale na našim podacima. Većina (ali ne sve!) američkih tvrtki preispitala je svoju odluku kada su shvatile ilegalnu prirodu našeg rada. Nasuprot tome, kineske tvrtke su s oduševljenjem prihvatile našu kolekciju, očito ne uznemirene njezinom legalnošću. Ovo je značajno s obzirom na ulogu Kine kao potpisnice gotovo svih glavnih međunarodnih ugovora o autorskim pravima. Omogućili smo brzi pristup za oko 30 tvrtki. Većina njih su LLM tvrtke, a neke su posrednici podataka, koji će preprodavati našu kolekciju. Većina su kineske, iako smo također surađivali s tvrtkama iz SAD-a, Europe, Rusije, Južne Koreje i Japana. DeepSeek <a %(arxiv)s>priznao</a> da je ranija verzija trenirana na dijelu naše kolekcije, iako su šutljivi o svom najnovijem modelu (vjerojatno također treniranom na našim podacima). Ako Zapad želi ostati ispred u utrci LLM-ova, i na kraju, AGI-ja, mora preispitati svoj stav o autorskim pravima, i to uskoro. Bez obzira slažete li se s nama ili ne u našem moralnom slučaju, ovo sada postaje pitanje ekonomije, pa čak i nacionalne sigurnosti. Svi blokovi moći grade umjetne super-znanstvenike, super-hakere i super-vojske. Sloboda informacija postaje pitanje opstanka za te zemlje — čak i pitanje nacionalne sigurnosti. Naš tim dolazi iz cijelog svijeta i nemamo određenu usklađenost. Ali bismo potaknuli zemlje s jakim zakonima o autorskim pravima da iskoriste ovu egzistencijalnu prijetnju kako bi ih reformirale. Što učiniti? Naša prva preporuka je jednostavna: skratiti rok autorskih prava. U SAD-u, autorska prava se dodjeljuju na 70 godina nakon smrti autora. Ovo je apsurdno. Možemo to uskladiti s patentima, koji se dodjeljuju na 20 godina nakon podnošenja. Ovo bi trebalo biti više nego dovoljno vremena za autore knjiga, radova, glazbe, umjetnosti i drugih kreativnih djela, da budu u potpunosti kompenzirani za svoj trud (uključujući dugoročne projekte kao što su filmske adaptacije). Zatim, barem, kreatori politika trebali bi uključiti iznimke za masovno očuvanje i širenje tekstova. Ako je izgubljeni prihod od pojedinačnih kupaca glavna briga, distribucija na osobnoj razini mogla bi ostati zabranjena. Zauzvrat, oni koji su sposobni upravljati velikim spremištima — tvrtke koje treniraju LLM-ove, zajedno s knjižnicama i drugim arhivima — bili bi pokriveni ovim iznimkama. Reforma autorskih prava je nužna za nacionalnu sigurnost Ukratko: Kineski LLM-ovi (uključujući DeepSeek) trenirani su na mojoj ilegalnoj arhivi knjiga i radova — najvećoj na svijetu. Zapad mora preoblikovati zakon o autorskim pravima kao pitanje nacionalne sigurnosti. Molimo pogledajte <a %(all_isbns)s>izvorni blog post</a> za više informacija. Postavili smo izazov da se ovo poboljša. Dodijelili bismo nagradu za prvo mjesto od 6.000 dolara, drugo mjesto od 3.000 dolara i treće mjesto od 1.000 dolara. Zbog ogromnog odaziva i nevjerojatnih prijava, odlučili smo malo povećati nagradni fond i dodijeliti četiri treća mjesta po 500 dolara. Pobjednici su navedeni u nastavku, ali svakako pogledajte sve prijave <a %(annas_archive)s>ovdje</a> ili preuzmite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>. Prvo mjesto 6.000 dolara: phiresky Ova <a %(phiresky_github)s>prijava</a> (<a %(annas_archive_note_2951)s>komentar na Gitlabu</a>) je jednostavno sve što smo željeli, i više! Posebno su nam se svidjele nevjerojatno fleksibilne opcije vizualizacije (čak podržavaju prilagođene shadere), ali s opsežnim popisom unaprijed postavljenih postavki. Također nam se svidjelo koliko je sve brzo i glatko, jednostavna implementacija (koja čak nema ni backend), pametna minimapa i opsežno objašnjenje u njihovom <a %(phiresky_github)s>blog postu</a>. Nevjerojatan rad i zasluženi pobjednik! - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Naša srca su puna zahvalnosti. Značajne ideje Neboderi za rijetkost Puno klizača za usporedbu datasets, kao da ste DJ. Skala s brojem knjiga. Lijepi natpisi. Zadana shema boja i karta topline. Jedinstven prikaz karte i filteri Bilješke, kao i statistike uživo Statistike uživo Neke dodatne ideje i implementacije koje su nam se posebno svidjele: Mogli bismo nastaviti još neko vrijeme, ali zaustavimo se ovdje. Obavezno pogledajte sve prijave <a %(annas_archive)s>ovdje</a>, ili preuzmite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>. Toliko prijava, a svaka donosi jedinstvenu perspektivu, bilo u korisničkom sučelju ili implementaciji. Barem ćemo uključiti prijavu koja je osvojila prvo mjesto na našu glavnu web stranicu, a možda i neke druge. Također smo počeli razmišljati o tome kako organizirati proces identificiranja, potvrđivanja i zatim arhiviranja najrjeđih knjiga. Više informacija uskoro. Hvala svima koji su sudjelovali. Nevjerojatno je da toliko ljudi brine. Jednostavno prebacivanje datasets za brze usporedbe. Svi ISBN-ovi CADAL SSNO-ovi CERLALC curenje podataka DuXiu SSID-ovi EBSCOhost-ov eBook Indeks Google Knjige Goodreads Internet Arhiva ISBNdb ISBN Globalni Registar Izdavača Libby Datoteke u Anninoj Arhivi Nexus/STC OCLC/Worldcat OpenLibrary Ruska Državna Knjižnica Imperijalna Knjižnica Trantora Drugo mjesto 3.000 dolara: hypha "Iako su savršeni kvadrati i pravokutnici matematički ugodni, ne pružaju superiornu lokalnost u kontekstu mapiranja. Vjerujem da asimetrija inherentna u ovim Hilbertovim ili klasičnim Mortonovim krivuljama nije mana već značajka. Baš kao što je Italija prepoznatljiva po svom obliku čizme na karti, jedinstvene 'osobitosti' ovih krivulja mogu poslužiti kao kognitivne orijentire. Ova prepoznatljivost može poboljšati prostornu memoriju i pomoći korisnicima da se orijentiraju, potencijalno olakšavajući lociranje specifičnih regija ili uočavanje uzoraka." Još jedna nevjerojatna <a %(annas_archive_note_2913)s>prijava</a>. Nije tako fleksibilna kao prvo mjesto, ali zapravo smo preferirali njezinu makro razinu vizualizacije u odnosu na prvo mjesto (kriva koja ispunjava prostor, granice, označavanje, isticanje, pomicanje i zumiranje). <a %(annas_archive_note_2971)s>Komentar</a> Joea Davisa odjeknuo je s nama: I dalje puno opcija za vizualizaciju i renderiranje, kao i nevjerojatno glatko i intuitivno korisničko sučelje. Čvrsto drugo mjesto! - Ana i tim (<a %(reddit)s>Reddit</a>) Prije nekoliko mjeseci najavili smo <a %(all_isbns)s>nagradu od 10.000 dolara</a> za najbolju moguću vizualizaciju naših podataka koji prikazuju ISBN prostor. Naglasili smo prikazivanje kojih datoteka već imamo/nemamo arhivirane, a kasnije smo dodali skup podataka koji opisuje koliko knjižnica posjeduje ISBN-ove (mjera rijetkosti). Bili smo preplavljeni odgovorima. Bilo je toliko kreativnosti. Veliko hvala svima koji su sudjelovali: vaša energija i entuzijazam su zarazni! Na kraju smo željeli odgovoriti na sljedeća pitanja: <strong>koje knjige postoje u svijetu, koliko smo ih već arhivirali i na koje bismo se knjige trebali usredotočiti sljedeće?</strong> Sjajno je vidjeti da toliko ljudi brine o ovim pitanjima. Započeli smo s osnovnom vizualizacijom. Na manje od 300kb, ova slika sažeto predstavlja najveći potpuno otvoreni "popis knjiga" ikada sastavljen u povijesti čovječanstva: Treće mjesto 500 dolara #1: maxlion U ovoj <a %(annas_archive_note_2940)s>prijavi</a> stvarno su nam se svidjele različite vrste prikaza, posebno prikazi za usporedbu i izdavače. Treće mjesto 500 dolara #2: abetusk Iako nije najuglađenije korisničko sučelje, ova <a %(annas_archive_note_2917)s>prijava</a> ispunjava mnoge kriterije. Posebno nam se svidjela njezina značajka usporedbe. Treće mjesto 500 dolara #3: conundrumer0 Kao i prvo mjesto, ova <a %(annas_archive_note_2975)s>prijava</a> impresionirala nas je svojom fleksibilnošću. U konačnici, to je ono što čini izvrstan alat za vizualizaciju: maksimalna fleksibilnost za napredne korisnike, dok se stvari drže jednostavnima za prosječne korisnike. Treće mjesto 500 dolara #4: charelf Zadnja <a %(annas_archive_note_2947)s>prijava</a> koja dobiva nagradu je prilično osnovna, ali ima neke jedinstvene značajke koje su nam se stvarno svidjele. Svidjelo nam se kako prikazuju koliko datasetova pokriva određeni ISBN kao mjeru popularnosti/pouzdanosti. Također nam se jako svidjela jednostavnost, ali učinkovitost korištenja klizača za neprozirnost za usporedbe. Pobjednici nagrade od 10.000 dolara za vizualizaciju ISBN-a Ukratko: Dobili smo nevjerojatne prijave za nagradu od 10.000 dolara za vizualizaciju ISBN-a. Pozadina Kako Arhiva Anne može ostvariti svoju misiju sigurnosnog kopiranja cjelokupnog ljudskog znanja, a da ne zna koje knjige još uvijek postoje? Trebamo popis zadataka. Jedan od načina za mapiranje ovoga je putem ISBN brojeva, koji su od 1970-ih dodijeljeni svakoj objavljenoj knjizi (u većini zemalja). Ne postoji središnja vlast koja zna sve dodjele ISBN brojeva. Umjesto toga, to je distribuirani sustav, gdje zemlje dobivaju raspon brojeva, koji zatim dodjeljuju manje raspone velikim izdavačima, koji mogu dalje podijeliti raspone manjim izdavačima. Na kraju se pojedinačni brojevi dodjeljuju knjigama. Počeli smo mapirati ISBN brojeve <a %(blog)s>prije dvije godine</a> s našim skeniranjem ISBNdb-a. Od tada smo skenirali mnoge druge izvore metadata, kao što su <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby i još mnogo toga. Cijeli popis može se pronaći na stranicama “Datasets” i “Torrents” na Arhivi Anne. Sada imamo daleko najveću potpuno otvorenu, lako preuzimljivu zbirku metadata knjiga (i time ISBN brojeva) na svijetu. Opširno smo <a %(blog)s>pisali</a> o tome zašto nam je stalo do očuvanja i zašto smo trenutno u kritičnom razdoblju. Sada moramo identificirati rijetke, zanemarene i jedinstveno ugrožene knjige i sačuvati ih. Imati dobre metadata o svim knjigama na svijetu pomaže u tome. Nagrada od $10,000 Velika pažnja bit će posvećena upotrebljivosti i izgledu. Prikažite stvarne metadata za pojedinačne ISBN brojeve prilikom zumiranja, kao što su naslov i autor. Bolja krivulja popunjavanja prostora. Npr. cik-cak, idući od 0 do 4 u prvom redu, a zatim natrag (u obrnutom smjeru) od 5 do 9 u drugom redu — rekurzivno primijenjeno. Različite ili prilagodljive sheme boja. Posebni prikazi za usporedbu datasets. Načini za otklanjanje problema, kao što su drugačiji metadata koji se ne slažu dobro (npr. vrlo različiti naslovi). Označavanje slika komentarima o ISBN-ovima ili rasponima. Bilo koje heuristike za prepoznavanje rijetkih ili ugroženih knjiga. Koje god kreativne ideje možete smisliti! Kod Kod za generiranje ovih slika, kao i drugih primjera, može se pronaći u <a %(annas_archive)s>ovom direktoriju</a>. Smislili smo kompaktan format podataka, s kojim su sve potrebne informacije o ISBN-u oko 75MB (komprimirano). Opis formata podataka i kod za njegovo generiranje možete pronaći <a %(annas_archive_l1244_1319)s>ovdje</a>. Za nagradu nije potrebno koristiti ovo, ali je vjerojatno najprikladniji format za početak. Možete transformirati naš metadata kako god želite (iako sav vaš kod mora biti otvorenog koda). Jedva čekamo vidjeti što ćete smisliti. Sretno! Forkajte ovaj repo i uredite ovaj HTML blog post (nije dopušteno koristiti druge backendove osim našeg Flask backenda). Napravite da slika iznad bude glatko zumabilna, tako da možete zumirati sve do pojedinačnih ISBN brojeva. Klikom na ISBN brojeve trebali biste biti preusmjereni na stranicu s metadata ili pretragu na Arhivi Anne. I dalje morate biti u mogućnosti prebacivati se između svih različitih datasets. Rasponi zemalja i izdavača trebaju biti istaknuti kada se pređe mišem preko njih. Možete koristiti npr. <a %(github_xlcnd_isbnlib)s>data4info.py u isbnlib</a> za informacije o zemljama, i naš “isbngrp” scrape za izdavače (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Mora dobro raditi na stolnim računalima i mobilnim uređajima. Ovdje ima mnogo toga za istražiti, pa najavljujemo nagradu za poboljšanje gore navedene vizualizacije. Za razliku od većine naših nagrada, ova je vremenski ograničena. Morate <a %(annas_archive)s>predati</a> svoj open source kod do 2025-01-31 (23:59 UTC). Najbolja prijava dobit će $6,000, drugo mjesto $3,000, a treće mjesto $1,000. Sve nagrade će biti dodijeljene koristeći Monero (XMR). Ispod su minimalni kriteriji. Ako nijedna prijava ne zadovolji kriterije, možda ćemo ipak dodijeliti neke nagrade, ali to će biti po našem nahođenju. Za dodatne bodove (ovo su samo ideje — pustite mašti na volju): Možete potpuno odstupiti od minimalnih kriterija i napraviti potpuno drugačiju vizualizaciju. Ako je zaista spektakularna, onda to kvalificira za nagradu, ali po našem nahođenju. Podnesite prijave objavljivanjem komentara na <a %(annas_archive)s>ovom problemu</a> s poveznicom na vaš forked repo, zahtjev za spajanje ili razliku. - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ova slika je 1000×800 piksela. Svaki piksel predstavlja 2.500 ISBN-ova. Ako imamo datoteku za ISBN, taj piksel činimo zelenijim. Ako znamo da je ISBN izdan, ali nemamo odgovarajuću datoteku, činimo ga crvenijim. U manje od 300kb, ova slika sažeto predstavlja najveći potpuno otvoreni "popis knjiga" ikad sastavljen u povijesti čovječanstva (nekoliko stotina GB komprimiranih u cijelosti). Također pokazuje: još uvijek ima puno posla u sigurnosnom kopiranju knjiga (imamo samo 16%). Vizualizacija svih ISBN-ova — nagrada od 10.000 dolara do 31.01.2025. Ova slika predstavlja najveći potpuno otvoreni "popis knjiga" ikad sastavljen u povijesti čovječanstva. Vizualizacija Osim pregledne slike, možemo pogledati i pojedinačne datasets koje smo prikupili. Koristite padajući izbornik i gumbe za prebacivanje između njih. Postoji mnogo zanimljivih uzoraka za vidjeti na ovim slikama. Zašto postoji neka pravilnost linija i blokova, koja se čini da se događa na različitim razinama? Što su prazna područja? Zašto su određeni datasets tako grupirani? Ostavit ćemo ova pitanja kao vježbu za čitatelja. - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Zaključak S ovim standardom možemo objavljivati izdanja postupno i lakše dodavati nove izvore podataka. Već imamo nekoliko uzbudljivih izdanja u pripremi! Također se nadamo da će drugim sjenskim knjižnicama biti lakše zrcaliti naše kolekcije. Uostalom, naš cilj je zauvijek očuvati ljudsko znanje i kulturu, pa što više redundancije, to bolje. Primjer Pogledajmo našu nedavnu Z-Library objavu kao primjer. Sastoji se od dvije kolekcije: “<span style="background: #fffaa3">zlib3_records</span>” i “<span style="background: #ffd6fe">zlib3_files</span>”. To nam omogućuje da odvojeno prikupljamo i objavljujemo metapodatke od stvarnih datoteka knjiga. Tako smo objavili dva torrenta s datotekama metapodataka: Također smo objavili niz torrenta s mapama binarnih podataka, ali samo za kolekciju “<span style="background: #ffd6fe">zlib3_files</span>”, ukupno 62: Pokretanjem <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> možemo vidjeti što je unutra: U ovom slučaju, to su metapodaci knjige kako ih prijavljuje Z-Library. Na najvišoj razini imamo samo “aacid” i “metadata”, ali ne i “data_folder”, budući da ne postoji odgovarajući binarni podaci. AACID sadrži “22430000” kao primarni ID, što možemo vidjeti da je preuzeto iz “zlibrary_id”. Možemo očekivati da će drugi AAC-ovi u ovoj kolekciji imati istu strukturu. Sada pokrenimo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Ovo su mnogo manji AAC metapodaci, iako se većina ovog AAC-a nalazi negdje drugdje u binarnoj datoteci! Uostalom, ovaj put imamo “data_folder”, pa možemo očekivati da će odgovarajući binarni podaci biti smješteni na <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” sadrži “zlibrary_id”, pa ga lako možemo povezati s odgovarajućim AAC-om u kolekciji “zlib_records”. Mogli smo ga povezati na različite načine, npr. putem AACID-a — standard to ne propisuje. Napominjemo da nije nužno da polje “metadata” samo po sebi bude JSON. Može biti niz koji sadrži XML ili bilo koji drugi format podataka. Čak možete pohraniti informacije o metapodacima u pridruženi binarni blob, npr. ako je to puno podataka. Heterogene datoteke i metadata, što je bliže izvornom formatu koliko je to moguće. Binarni podaci mogu se poslužiti izravno putem web poslužitelja poput Nginxa. Heterogeni identifikatori u izvornim knjižnicama, ili čak nedostatak identifikatora. Odvojena izdanja metadata naspram podataka datoteka, ili izdanja samo s metadata (npr. naše ISBNdb izdanje). Distribucija putem torrenta, ali s mogućnošću drugih metoda distribucije (npr. IPFS). Neizmjenjivi zapisi, budući da trebamo pretpostaviti da će naši torrenti živjeti zauvijek. Inkrementalna izdanja / dodana izdanja. Strojno čitljivo i zapisivo, prikladno i brzo, posebno za našu platformu (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Djelomično jednostavna ljudska inspekcija, iako je to sekundarno u odnosu na strojnu čitljivost. Jednostavno za pokretanje naših kolekcija s standardnim unajmljenim seedboxom. Ciljevi dizajna Ne brinemo o tome da datoteke budu jednostavne za navigaciju ručno na disku, ili pretražive bez prethodne obrade. Ne brinemo o izravnoj kompatibilnosti s postojećim softverom za knjižnice. Iako bi svatko trebao lako moći pokrenuti našu kolekciju koristeći torrente, ne očekujemo da će datoteke biti upotrebljive bez značajnog tehničkog znanja i predanosti. Naš primarni slučaj upotrebe je distribucija datoteka i pridruženih metadata iz različitih postojećih kolekcija. Naša najvažnija razmatranja su: Neki ne-ciljevi: Budući da je Annina Arhiva otvorenog koda, želimo koristiti naš format izravno. Kada osvježavamo naš indeks pretraživanja, pristupamo samo javno dostupnim putovima, tako da svatko tko forkira našu knjižnicu može brzo započeti. <strong>AAC.</strong> AAC (Anna’s Arhiva Kontejner) je jedna stavka koja se sastoji od <strong>metadata</strong>, i opcionalno <strong>binarnih podataka</strong>, oba su nepromjenjiva. Ima globalno jedinstveni identifikator, nazvan <strong>AACID</strong>. <strong>AACID.</strong> Format AACID-a je sljedeći: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Na primjer, stvarni AACID koji smo objavili je <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Raspon AACID-a.</strong> Budući da AACID-i sadrže monotonijski rastuće vremenske oznake, možemo ih koristiti za označavanje raspona unutar određene kolekcije. Koristimo ovaj format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, gdje su vremenske oznake uključive. Ovo je u skladu s ISO 8601 notacijom. Rasponi su kontinuirani i mogu se preklapati, ali u slučaju preklapanja moraju sadržavati identične datoteke kao one prethodno objavljene u toj kolekciji (budući da su AAC-ovi nepromjenjivi). Nedostajuće datoteke nisu dopuštene. <code>{collection}</code>: naziv kolekcije, koji može sadržavati ASCII slova, brojeve i donje crte (ali ne dvostruke donje crte). <code>{collection-specific ID}</code>: kolekcijski specifičan identifikator, ako je primjenjivo, npr. Z-Library ID. Može biti izostavljen ili skraćen. Mora biti izostavljen ili skraćen ako bi AACID inače premašio 150 znakova. <code>{ISO 8601 timestamp}</code>: kratka verzija ISO 8601, uvijek u UTC, npr. <code>20220723T194746Z</code>. Ovaj broj mora monotonijski rasti za svako izdanje, iako se njegova točna semantika može razlikovati po kolekciji. Predlažemo korištenje vremena preuzimanja ili generiranja ID-a. <code>{shortuuid}</code>: UUID, ali komprimiran u ASCII, npr. korištenjem base57. Trenutno koristimo <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python biblioteku. <strong>Mapa binarnih podataka.</strong> Mapa s binarnim podacima raspona AAC-ova, za jednu određenu kolekciju. Imaju sljedeća svojstva: Direktorij mora sadržavati datoteke podataka za sve AAC-ove unutar navedenog raspona. Svaka datoteka podataka mora imati svoj AACID kao naziv datoteke (bez ekstenzija). Naziv direktorija mora biti raspon AACID-a, s prefiksom <code style="color: green">annas_archive_data__</code>, i bez sufiksa. Na primjer, jedno od naših stvarnih izdanja ima direktorij nazvan<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Preporučuje se da ove mape budu donekle upravljive veličine, npr. ne veće od 100GB-1TB svaka, iako se ova preporuka može mijenjati s vremenom. <strong>Kolekcija.</strong> Svaki AAC pripada kolekciji, koja je po definiciji popis AAC-ova koji su semantički konzistentni. To znači da ako napravite značajnu promjenu u formatu metapodataka, morate stvoriti novu kolekciju. Standard <strong>Datoteka metapodataka.</strong> Datoteka metapodataka sadrži metapodatke raspona AAC-ova, za jednu određenu kolekciju. Imaju sljedeća svojstva: <code>data_folder</code> je opcionalan, i to je naziv mape binarnih podataka koja sadrži odgovarajuće binarne podatke. Naziv datoteke odgovarajućih binarnih podataka unutar te mape je AACID zapisa. Svaki JSON objekt mora sadržavati sljedeća polja na najvišoj razini: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcionalno). Nisu dopuštena druga polja. Naziv datoteke mora biti raspon AACID-a, s prefiksom <code style="color: red">annas_archive_meta__</code> i sufiksom <code>.jsonl.zstd</code>. Na primjer, jedno od naših izdanja zove se<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kao što pokazuje ekstenzija datoteke, tip datoteke je <a %(jsonlines)s>JSON Lines</a> komprimiran s <a %(zstd)s>Zstandard</a>. <code>metadata</code> su proizvoljni metapodaci, prema semantici kolekcije. Moraju biti semantički konzistentni unutar kolekcije. Prefiks <code style="color: red">annas_archive_meta__</code> može se prilagoditi nazivu vaše institucije, npr. <code style="color: red">my_institute_meta__</code>. <strong>Kolekcije “datoteka” i “zapisa”.</strong> Po konvenciji, često je zgodno objaviti “zapise” i “datoteke” kao različite kolekcije, kako bi se mogle objaviti prema različitim rasporedima, npr. na temelju brzine preuzimanja. “Zapis” je kolekcija samo metapodataka, koja sadrži informacije poput naslova knjiga, autora, ISBN-ova itd., dok su “datoteke” kolekcije koje sadrže stvarne datoteke (pdf, epub). Na kraju smo se odlučili za relativno jednostavan standard. Prilično je labav, nenormativan i još uvijek u razvoju. <strong>Torrenti.</strong> Datoteke s metapodacima i mape s binarnim podacima mogu se povezati u torrentima, s jednim torrentom po datoteci s metapodacima ili jednim torrentom po mapi s binarnim podacima. Torrenti moraju imati izvorno ime datoteke/direktorija plus <code>.torrent</code> sufiks kao svoje ime datoteke. <a %(wikipedia_annas_archive)s>Annin Arhiv</a> postao je daleko najveća sjena knjižnica na svijetu i jedina sjena knjižnica te veličine koja je potpuno otvorenog koda i otvorenih podataka. Ispod je tablica s naše stranice Datasets (malo izmijenjena): To smo postigli na tri načina: Ogledanjem postojećih sjena knjižnica otvorenih podataka (poput Sci-Hub i Library Genesis). Pomažući sjena knjižnicama koje žele biti otvorenije, ali nisu imale vremena ili resursa za to (poput zbirke stripova Libgen). Skrepanjem knjižnica koje ne žele dijeliti u velikim količinama (poput Z-Library). Za (2) i (3) sada sami upravljamo značajnom kolekcijom torrenta (stotine TB-a). Do sada smo pristupali tim kolekcijama kao jednokratnim projektima, što znači prilagođenu infrastrukturu i organizaciju podataka za svaku kolekciju. To dodaje značajan teret svakom izdanju i čini posebno teškim izraditi više inkrementalnih izdanja. Zato smo odlučili standardizirati naša izdanja. Ovo je tehnički blog post u kojem uvodimo naš standard: <strong>Annina Arhiva Kontejneri</strong>. Annin Arhiv Kontejneri (AAC): standardizacija izdanja iz najveće svjetske sjene knjižnice Annin Arhiv postao je najveća sjena knjižnica na svijetu, što zahtijeva standardizaciju naših izdanja. 300GB+ naslovnica knjiga objavljeno Na kraju, sretni smo što možemo najaviti malo izdanje. U suradnji s ljudima koji upravljaju Libgen.rs forkom, dijelimo sve njihove naslovnice knjiga putem torrenta i IPFS-a. Ovo će raspodijeliti opterećenje gledanja naslovnica među više strojeva i bolje ih sačuvati. U mnogim (ali ne svim) slučajevima, naslovnice knjiga su uključene u same datoteke, pa je ovo neka vrsta "izvedenih podataka". Ali imati ih u IPFS-u je i dalje vrlo korisno za svakodnevno funkcioniranje i Anine Arhive i raznih Library Genesis forkova. Kao i obično, ovo izdanje možete pronaći na Pirate Library Mirroru (UREDI: premješteno na <a %(wikipedia_annas_archive)s>Aninu Arhivu</a>). Nećemo ga ovdje povezivati, ali ga možete lako pronaći. Nadamo se da možemo malo usporiti tempo, sada kada imamo pristojnu alternativu Z-Libraryju. Ovaj radni opterećenje nije osobito održivo. Ako ste zainteresirani za pomoć u programiranju, radu na poslužiteljima ili radu na očuvanju, svakako nas kontaktirajte. Još uvijek ima puno <a %(annas_archive)s>posla za obaviti</a>. Hvala na vašem interesu i podršci. Prelazak na ElasticSearch Neki upiti su trajali jako dugo, do te mjere da bi zauzeli sve otvorene veze. Po defaultu MySQL ima minimalnu duljinu riječi, ili vaš indeks može postati stvarno velik. Ljudi su prijavljivali da ne mogu pretraživati "Ben Hur". Pretraga je bila samo donekle brza kada je potpuno učitana u memoriju, što je zahtijevalo da nabavimo skuplji stroj za pokretanje, plus neke naredbe za predopterećenje indeksa pri pokretanju. Ne bismo ga mogli lako proširiti za izgradnju novih značajki, poput bolje <a %(wikipedia_cjk_characters)s>tokenizacije za jezike bez razmaka</a>, filtriranja/facetinga, sortiranja, prijedloga "jeste li mislili", automatskog dovršavanja i tako dalje. Jedan od naših <a %(annas_archive)s>tiketa</a> bio je zbirka problema s našim sustavom pretrage. Koristili smo MySQL pretragu punog teksta, budući da smo sve naše podatke imali u MySQL-u. Ali imao je svoja ograničenja: Nakon razgovora s hrpom stručnjaka, odlučili smo se za ElasticSearch. Nije bio savršen (njihovi defaultni prijedlozi "jeste li mislili" i značajke automatskog dovršavanja su loši), ali općenito je bio puno bolji od MySQL-a za pretragu. Još uvijek nismo <a %(youtube)s>previše skloni</a> koristiti ga za bilo koje kritične podatke (iako su napravili puno <a %(elastic_co)s>napretka</a>), ali općenito smo prilično zadovoljni prelaskom. Za sada smo implementirali puno bržu pretragu, bolju podršku za jezike, bolje sortiranje po relevantnosti, različite opcije sortiranja i filtriranje po jeziku/vrsti knjige/vrsti datoteke. Ako vas zanima kako to funkcionira, <a %(annas_archive_l140)s>pogledajte</a> <a %(annas_archive_l1115)s>ovdje</a> <a %(annas_archive_l1635)s>više</a>. Prilično je pristupačno, iako bi moglo koristiti još nekoliko komentara… Annina Arhiva je potpuno otvorenog koda Vjerujemo da informacije trebaju biti slobodne, a naš vlastiti kod nije iznimka. Objavili smo sav naš kod na našoj privatno hostiranoj Gitlab instanci: <a %(annas_archive)s>Annin Softver</a>. Također koristimo praćenje problema za organizaciju našeg rada. Ako se želite uključiti u naš razvoj, ovo je odlično mjesto za početak. Da bismo vam dali uvid u stvari na kojima radimo, uzmite naš nedavni rad na poboljšanju performansi na strani klijenta. Budući da još nismo implementirali paginaciju, često bismo vraćali vrlo duge stranice s rezultatima pretraživanja, s 100-200 rezultata. Nismo htjeli prerano prekinuti rezultate pretraživanja, ali to je značilo da bi usporilo neke uređaje. Za to smo implementirali mali trik: većinu rezultata pretraživanja omotali smo u HTML komentare (<code><!-- --></code>), a zatim napisali mali Javascript koji bi detektirao kada bi rezultat trebao postati vidljiv, u kojem trenutku bismo odmotali komentar: DOM "virtualizacija" implementirana u 23 linije, nema potrebe za fensi bibliotekama! Ovo je vrsta brzog pragmatičnog koda koji dobijete kada imate ograničeno vrijeme i stvarne probleme koje treba riješiti. Prijavljeno je da naša pretraga sada dobro radi na sporim uređajima! Još jedan veliki napor bio je automatizirati izgradnju baze podataka. Kada smo pokrenuli, jednostavno smo nasumično spojili različite izvore. Sada ih želimo ažurirati, pa smo napisali hrpu skripti za preuzimanje novih metadata iz dva Library Genesis forka i integrirali ih. Cilj nije samo učiniti ovo korisnim za našu arhivu, već i olakšati stvari svima koji žele eksperimentirati s metadata sjene knjižnice. Cilj bi bio Jupyter bilježnica koja ima sve vrste zanimljivih metadata dostupnih, kako bismo mogli provesti više istraživanja poput utvrđivanja <a %(blog)s>postotka ISBN-ova koji su zauvijek sačuvani</a>. Na kraju, obnovili smo naš sustav donacija. Sada možete koristiti kreditnu karticu za izravno polaganje novca u naše kripto novčanike, bez potrebe da znate išta o kriptovalutama. Nastavit ćemo pratiti koliko dobro ovo funkcionira u praksi, ali ovo je velika stvar. S obzirom na to da je Z-Library ugašen i da su njegovi (navodni) osnivači uhićeni, radili smo danonoćno kako bismo pružili dobru alternativu s Anninom Arhivom (nećemo je ovdje povezivati, ali možete je potražiti na Googleu). Evo nekih stvari koje smo nedavno postigli. Annina Ažuriranja: potpuno otvoreni izvor arhive, ElasticSearch, 300GB+ naslovnica knjiga Radili smo danonoćno kako bismo pružili dobru alternativu s Anninom Arhivom. Evo nekih stvari koje smo nedavno postigli. Analiza Semantički duplikati (različiti skenovi iste knjige) teoretski se mogu filtrirati, ali to je nezgodno. Kada smo ručno pregledavali stripove, pronašli smo previše lažnih pozitivnih rezultata. Postoje neki duplikati isključivo po MD5, što je relativno rasipno, ali njihovo filtriranje bi nam donijelo samo oko 1% in uštede. Na ovoj skali to je još uvijek oko 1TB, ali također, na ovoj skali 1TB zapravo nije bitan. Radije ne bismo riskirali slučajno uništavanje podataka u ovom procesu. Pronašli smo hrpu podataka koji nisu knjige, poput filmova temeljenih na stripovima. To se također čini rasipnim, budući da su već široko dostupni na druge načine. Međutim, shvatili smo da ne možemo jednostavno filtrirati filmske datoteke, jer postoje i <em>interaktivne strip knjige</em> koje su izdane na računalu, a netko ih je snimio i spremio kao filmove. U konačnici, sve što bismo mogli izbrisati iz kolekcije uštedjelo bi samo nekoliko postotaka. Tada smo se sjetili da smo mi sakupljači podataka, a ljudi koji će ovo preslikavati također su sakupljači podataka, pa, "ŠTO MISLITE, IZBRISATI?!" :) Kada dobijete 95TB podataka u svoj skladišni klaster, pokušavate shvatiti što se uopće nalazi unutra… Napravili smo analizu kako bismo vidjeli možemo li malo smanjiti veličinu, na primjer uklanjanjem duplikata. Evo nekih naših nalaza: Stoga vam predstavljamo kompletnu, nemodificiranu kolekciju. To je puno podataka, ali se nadamo da će dovoljno ljudi ipak biti zainteresirano za dijeljenje. Suradnja S obzirom na svoju veličinu, ova zbirka je dugo bila na našem popisu želja, pa smo nakon uspjeha s sigurnosnom kopijom Z-Library-a usmjerili pažnju na ovu zbirku. Isprva smo je izravno preuzimali, što je bio pravi izazov, jer njihov poslužitelj nije bio u najboljem stanju. Na taj način smo dobili oko 15TB, ali je išlo sporo. Srećom, uspjeli smo stupiti u kontakt s operaterom knjižnice, koji je pristao poslati nam sve podatke izravno, što je bilo puno brže. Ipak, trebalo je više od pola godine da se svi podaci prenesu i obrade, a gotovo smo ih sve izgubili zbog oštećenja diska, što bi značilo da moramo početi ispočetka. Ovo iskustvo nas je uvjerilo da je važno što prije objaviti ove podatke, kako bi se mogli široko zrcaliti. Samo smo jedan ili dva nesretna incidenta udaljeni od gubitka ove zbirke zauvijek! Zbirka Brzo kretanje znači da je zbirka malo neorganizirana… Pogledajmo. Zamislite da imamo datotečni sustav (koji u stvarnosti dijelimo preko torrenta): Prvi direktorij, <code>/repository</code>, je strukturiraniji dio ovoga. Ovaj direktorij sadrži takozvane "tisućne direktorije": direktorije s tisuću datoteka, koje su inkrementalno numerirane u bazi podataka. Direktorij <code>0</code> sadrži datoteke s comic_id 0–999, i tako dalje. Ovo je isti sustav koji Library Genesis koristi za svoje zbirke fikcije i nefikcije. Ideja je da se svaki "tisućni direktorij" automatski pretvori u torrent čim se popuni. Međutim, operater Libgen.li nikada nije napravio torrente za ovu zbirku, pa su tisućni direktoriji vjerojatno postali nezgodni i ustupili mjesto "nesortiranim direktorijima". To su <code>/comics0</code> do <code>/comics4</code>. Svi oni sadrže jedinstvene strukture direktorija, koje su vjerojatno imale smisla za prikupljanje datoteka, ali sada nam ne znače puno. Srećom, metadata i dalje izravno upućuje na sve te datoteke, pa njihova organizacija na disku zapravo nije bitna! Metadata je dostupna u obliku MySQL baze podataka. Može se izravno preuzeti s web stranice Libgen.li, ali ćemo je također učiniti dostupnom u torrentu, zajedno s našom tablicom sa svim MD5 hashovima. <q>Dr. Barbara Gordon pokušava se izgubiti u svakodnevnom svijetu knjižnice…</q> Libgen forkovi Prvo, malo pozadine. Možda poznajete Library Genesis po njihovoj epskoj zbirci knjiga. Manje ljudi zna da su volonteri Library Genesis-a stvorili i druge projekte, poput značajne zbirke časopisa i standardnih dokumenata, potpune sigurnosne kopije Sci-Hub-a (u suradnji s osnivačicom Sci-Hub-a, Alexandrom Elbakyan), te, naravno, masivne zbirke stripova. U jednom trenutku različiti operateri Library Genesis ogledala krenuli su svojim putem, što je dovelo do trenutne situacije s nekoliko različitih "forkova", koji i dalje nose ime Library Genesis. Libgen.li fork jedinstveno ima ovu zbirku stripova, kao i značajnu zbirku časopisa (na kojoj također radimo). Prikupljanje sredstava Objavljujemo ove podatke u velikim dijelovima. Prvi torrent je <code>/comics0</code>, koji smo stavili u jednu ogromnu .tar datoteku od 12TB. To je bolje za vaš tvrdi disk i torrent softver nego milijun manjih datoteka. Kao dio ovog izdanja, organiziramo prikupljanje sredstava. Cilj nam je prikupiti 20.000 dolara za pokrivanje operativnih i ugovornih troškova za ovu kolekciju, kao i omogućiti tekuće i buduće projekte. Imamo neke <em>ogromne</em> u pripremi. <em>Koga podržavam svojom donacijom?</em> Ukratko: podržavamo sigurnosno kopiranje cjelokupnog znanja i kulture čovječanstva i činimo ih lako dostupnima. Sav naš kod i podaci su otvorenog koda, potpuno smo volonterski projekt, i do sada smo sačuvali 125TB knjiga (uz postojeće torrente Libgena i Scihuba). U konačnici gradimo zamašnjak koji omogućuje i potiče ljude da pronađu, skeniraju i sigurnosno kopiraju sve knjige na svijetu. O našem glavnom planu pisat ćemo u budućem postu. :) Ako donirate za 12-mjesečno članstvo “Amazing Archivist” (780 dolara), možete <strong>“usvojiti torrent”</strong>, što znači da ćemo vaše korisničko ime ili poruku staviti u naziv jedne od torrent datoteka! Možete donirati odlaskom na <a %(wikipedia_annas_archive)s>Annin Arhiv</a> i klikom na gumb “Doniraj”. Također tražimo više volontera: softverske inženjere, istraživače sigurnosti, stručnjake za anonimnu trgovinu i prevoditelje. Možete nas podržati i pružanjem usluga hostinga. I naravno, molimo vas da dijelite naše torrente! Hvala svima koji su nas već tako velikodušno podržali! Zaista činite razliku. Evo torrenta koji su do sada objavljeni (još uvijek obrađujemo ostatak): Svi torrenti mogu se pronaći na <a %(wikipedia_annas_archive)s>Anninom Arhivu</a> pod “Datasets” (ne povezujemo se izravno tamo, kako se linkovi na ovaj blog ne bi uklonili s Reddita, Twittera itd.). Odatle slijedite link na Tor web stranicu. <a %(news_ycombinator)s>Raspravljajte na Hacker Newsu</a> Što je sljedeće? Hrpa torrenta je odlična za dugoročno očuvanje, ali ne toliko za svakodnevni pristup. Radit ćemo s partnerima za hosting kako bismo sve te podatke postavili na web (budući da Annin Arhiv ne hostira ništa izravno). Naravno, moći ćete pronaći ove linkove za preuzimanje na Anninom Arhivu. Također pozivamo sve da rade s ovim podacima! Pomozite nam da ih bolje analiziramo, dedupliciramo, postavimo na IPFS, remiksiramo, treniramo vaše AI modele s njima i tako dalje. Sve je vaše, i jedva čekamo vidjeti što ćete učiniti s tim. Konačno, kao što je već rečeno, još uvijek imamo neka ogromna izdanja koja dolaze (ako bi <em>netko</em> mogao <em>slučajno</em> poslati nam ispis <em>određene</em> ACS4 baze podataka, znate gdje nas možete pronaći...), kao i izgradnju zamašnjaka za sigurnosno kopiranje svih knjiga na svijetu. Zato ostanite s nama, tek smo počeli. - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Najveća sjena knjižnica stripova vjerojatno je ona određenog Library Genesis forka: Libgen.li. Jedan administrator koji vodi tu stranicu uspio je prikupiti nevjerojatnu kolekciju stripova od preko 2 milijuna datoteka, ukupno preko 95TB. Međutim, za razliku od drugih Library Genesis kolekcija, ova nije bila dostupna u velikim količinama putem torrenta. Mogli ste pristupiti tim stripovima samo pojedinačno putem njegovog sporog osobnog poslužitelja — jedne točke kvara. Do danas! U ovom postu reći ćemo vam više o ovoj zbirci i o našoj akciji prikupljanja sredstava za podršku daljnjem radu. Anina Arhiva je napravila sigurnosnu kopiju najveće svjetske sjene knjižnice stripova (95TB) — možete pomoći u seedanju Najveća sjena knjižnica stripova na svijetu imala je jednu točku kvara... do danas. Upozorenje: ovaj blog post je zastario. Odlučili smo da IPFS još nije spreman za široku upotrebu. I dalje ćemo povezivati datoteke na IPFS iz Annine Arhive kad god je to moguće, ali ih više nećemo sami hostirati, niti preporučujemo drugima da koriste IPFS za zrcaljenje. Molimo pogledajte našu stranicu Torrents ako želite pomoći u očuvanju naše kolekcije. Stavljanje 5.998.794 knjiga na IPFS Umnožavanje kopija Vratimo se na naše izvorno pitanje: kako možemo tvrditi da ćemo sačuvati naše zbirke zauvijek? Glavni problem ovdje je što naša zbirka <a %(torrents_stats)s>brzo raste</a>, zahvaljujući scrapingu i otvorenom kodu nekih masivnih zbirki (uz nevjerojatan rad koji su već obavili drugi shadow knjižnice otvorenih podataka poput Sci-Huba i Library Genesis). Ovaj rast podataka otežava preslikavanje zbirki diljem svijeta. Pohrana podataka je skupa! Ali optimistični smo, posebno kada promatramo sljedeća tri trenda. <a %(annas_archive_stats)s>Ukupna veličina</a> naših zbirki, tijekom posljednjih nekoliko mjeseci, razložena prema broju torrent seedera. Trendovi cijena HDD-a iz različitih izvora (kliknite za pregled studije). <a %(critical_window_chinese)s>Kineska verzija 中文版</a>, raspravljajte na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Ubrali smo najlakše plodove Ovo izravno slijedi iz naših prioriteta o kojima smo gore raspravljali. Preferiramo rad na oslobađanju velikih zbirki prvo. Sada kada smo osigurali neke od najvećih zbirki na svijetu, očekujemo da će naš rast biti mnogo sporiji. Još uvijek postoji dugačak rep manjih zbirki, a nove knjige se skeniraju ili objavljuju svaki dan, ali stopa će vjerojatno biti mnogo sporija. Možda ćemo se još uvijek udvostručiti ili čak utrostručiti u veličini, ali tijekom duljeg vremenskog razdoblja. Poboljšanja OCR-a. Prioriteti Znanstveni i inženjerski softverski kod Fikcionalne ili zabavne verzije svega navedenog Geografski podaci (npr. karte, geološka istraživanja) Interni podaci iz korporacija ili vlada (curenja informacija) Podaci mjerenja poput znanstvenih mjerenja, ekonomskih podataka, korporativnih izvještaja Općenito zapisi o metapodacima (o publicistici i fikciji; o drugim medijima, umjetnosti, ljudima itd.; uključujući recenzije) Knjige iz područja publicistike Časopisi, novine, priručnici iz područja publicistike Transkripti govora, dokumentaraca, podcasta iz područja publicistike Organski podaci poput DNK sekvenci, biljnih sjemenki ili mikrobnih uzoraka Akademski radovi, časopisi, izvještaji Znanstvene i inženjerske web stranice, online rasprave Transkripti pravnih ili sudskih postupaka Jedinstveno ugrožena uništenjem (npr. ratom, smanjenjem financiranja, tužbama ili političkim progonima) Rijetka Jedinstveno zanemarena Zašto nam je toliko stalo do radova i knjiga? Ostavimo po strani naše temeljno uvjerenje u očuvanje općenito — mogli bismo napisati još jedan post o tome. Dakle, zašto radovi i knjige posebno? Odgovor je jednostavan: <strong>gustoća informacija</strong>. Po megabajtu pohrane, pisani tekst pohranjuje najviše informacija od svih medija. Iako nam je stalo i do znanja i do kulture, više nam je stalo do prvog. Sveukupno, nalazimo hijerarhiju gustoće informacija i važnosti očuvanja koja otprilike izgleda ovako: Poredak na ovom popisu donekle je proizvoljan — nekoliko stavki je izjednačeno ili postoje nesuglasice unutar našeg tima — i vjerojatno zaboravljamo neke važne kategorije. Ali ovo je otprilike kako dajemo prioritet. Neke od ovih stavki su previše različite od ostalih da bismo se brinuli o njima (ili su već zbrinute od strane drugih institucija), poput organskih podataka ili geografskih podataka. Ali većina stavki na ovom popisu zapravo nam je važna. Još jedan veliki faktor u našem davanju prioriteta je koliko je određeno djelo ugroženo. Radije se fokusiramo na djela koja su: Na kraju, brinemo o razmjeru. Imamo ograničeno vrijeme i novac, pa bismo radije proveli mjesec dana spašavajući 10.000 knjiga nego 1.000 knjiga — ako su jednako vrijedne i ugrožene. <em><q>Izgubljeno se ne može povratiti; ali spasimo ono što ostaje: ne trezorima i bravama koje ih štite od javnog pogleda i upotrebe, prepuštajući ih zubu vremena, već takvim umnožavanjem kopija koje će ih staviti izvan dosega nesreće.</q></em><br>— Thomas Jefferson, 1791. Sjene knjižnice Kod može biti otvorenog koda na Githubu, ali Github kao cjelina ne može se lako preslikati i tako sačuvati (iako u ovom konkretnom slučaju postoje dovoljno distribuirane kopije većine repozitorija koda) Zapise o metapodacima možete slobodno pregledati na web stranici Worldcat, ali ih ne možete preuzeti u velikim količinama (dok ih nismo <a %(worldcat_scrape)s>scrapali</a>) Reddit je besplatan za korištenje, ali je nedavno uveo stroge mjere protiv scrapinga, zbog gladnih podataka za treniranje LLM-a (više o tome kasnije) Postoji mnogo organizacija koje imaju slične misije i slične prioritete. Doista, postoje knjižnice, arhivi, laboratoriji, muzeji i druge institucije zadužene za očuvanje ove vrste. Mnoge od njih su dobro financirane, od strane vlada, pojedinaca ili korporacija. Ali imaju jednu veliku slijepu točku: pravni sustav. U tome leži jedinstvena uloga sjene knjižnica i razlog postojanja Annine Arhive. Možemo raditi stvari koje drugim institucijama nisu dopuštene. Sada, nije (često) da možemo arhivirati materijale koji su ilegalni za očuvanje drugdje. Ne, u mnogim je mjestima legalno izgraditi arhivu s bilo kojim knjigama, radovima, časopisima i tako dalje. Ali ono što pravne arhive često nemaju je <strong>redundancija i dugovječnost</strong>. Postoje knjige od kojih postoji samo jedan primjerak u nekoj fizičkoj knjižnici negdje. Postoje zapisi o metapodacima koje čuva samo jedna korporacija. Postoje novine sačuvane samo na mikrofilmu u jednoj arhivi. Knjižnice mogu doživjeti smanjenje financiranja, korporacije mogu bankrotirati, arhive mogu biti bombardirane i spaljene do temelja. Ovo nije hipotetičko — to se događa cijelo vrijeme. Ono što možemo jedinstveno učiniti u Anninoj Arhivi je pohraniti mnoge kopije djela, u velikom opsegu. Možemo prikupljati radove, knjige, časopise i još mnogo toga, te ih distribuirati u velikim količinama. Trenutno to radimo putem torrenta, ali točne tehnologije nisu važne i mijenjat će se s vremenom. Važan dio je dobivanje mnogih kopija distribuiranih diljem svijeta. Ovaj citat star više od 200 godina i dalje je istinit: Kratka napomena o javnoj domeni. Budući da se Annina Arhiva jedinstveno fokusira na aktivnosti koje su ilegalne u mnogim dijelovima svijeta, ne bavimo se široko dostupnim zbirkama, poput knjiga u javnoj domeni. Pravne institucije često već dobro brinu o tome. Međutim, postoje razlozi zbog kojih ponekad radimo na javno dostupnim zbirkama: - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Troškovi pohrane nastavljaju eksponencijalno padati 3. Poboljšanja u gustoći informacija Trenutno pohranjujemo knjige u sirovim formatima u kojima su nam dostavljene. Naravno, one su komprimirane, ali često su to još uvijek veliki skenovi ili fotografije stranica. Do sada su jedine opcije za smanjenje ukupne veličine naše zbirke bile agresivnija kompresija ili deduplikacija. Međutim, da bismo postigli značajne uštede, obje su previše gubitne za naš ukus. Teška kompresija fotografija može učiniti tekst jedva čitljivim. A deduplikacija zahtijeva visoku sigurnost da su knjige potpuno iste, što je često previše netočno, pogotovo ako je sadržaj isti, ali su skenovi napravljeni u različitim prilikama. Uvijek je postojala treća opcija, ali njezina je kvaliteta bila toliko loša da je nikada nismo razmatrali: <strong>OCR, ili optičko prepoznavanje znakova</strong>. To je proces pretvaranja fotografija u običan tekst, koristeći AI za prepoznavanje znakova na fotografijama. Alati za to postoje već dugo i prilično su dobri, ali "prilično dobri" nije dovoljno za svrhe očuvanja. Međutim, nedavni multimodalni modeli dubokog učenja postigli su izuzetno brz napredak, iako još uvijek uz visoke troškove. Očekujemo da će se i točnost i troškovi dramatično poboljšati u nadolazećim godinama, do točke kada će postati realno primijeniti ih na cijelu našu knjižnicu. Kada se to dogodi, vjerojatno ćemo i dalje čuvati izvorne datoteke, ali uz to bismo mogli imati mnogo manju verziju naše knjižnice koju će većina ljudi željeti zrcaliti. Ključ je u tome da se sirovi tekst sam po sebi još bolje komprimira i mnogo ga je lakše deduplicirati, što nam donosi još više ušteda. Sveukupno, nije nerealno očekivati barem 5-10 puta smanjenje ukupne veličine datoteka, možda čak i više. Čak i uz konzervativno smanjenje od 5 puta, gledali bismo na <strong>1.000–3.000 dolara u 10 godina čak i ako se naša knjižnica utrostruči</strong>. U vrijeme pisanja, <a %(diskprices)s>cijene diskova</a> po TB su oko 12 dolara za nove diskove, 8 dolara za rabljene diskove i 4 dolara za trake. Ako smo konzervativni i gledamo samo nove diskove, to znači da pohrana petabajta košta oko 12.000 dolara. Ako pretpostavimo da će naša knjižnica utrostručiti s 900TB na 2,7PB, to bi značilo 32.400 dolara za preslikavanje cijele naše knjižnice. Dodajući troškove električne energije, ostalog hardvera i tako dalje, zaokružimo to na 40.000 dolara. Ili s trakama više kao 15.000–20.000 dolara. S jedne strane <strong>15.000–40.000 dolara za zbroj cjelokupnog ljudskog znanja je povoljno</strong>. S druge strane, malo je previše očekivati tone punih kopija, pogotovo ako bismo također željeli da ti ljudi nastave seedati svoje torrente za dobrobit drugih. To je danas. Ali napredak ide naprijed: Troškovi tvrdih diskova po TB su otprilike prepolovljeni u posljednjih 10 godina i vjerojatno će nastaviti padati sličnim tempom. Čini se da su trake na sličnom putu. Cijene SSD-ova padaju još brže i mogle bi nadmašiti cijene HDD-ova do kraja desetljeća. Ako se ovo održi, za 10 godina mogli bismo gledati na samo 5.000–13.000 dolara za preslikavanje cijele naše zbirke (1/3), ili čak manje ako rastemo manje u veličini. Iako je to još uvijek puno novca, to će biti dostupno mnogim ljudima. A moglo bi biti još bolje zbog sljedeće točke… Na Aninoj Arhivi često nas pitaju kako možemo tvrditi da ćemo sačuvati naše zbirke zauvijek, kada ukupna veličina već doseže 1 petabajt (1000 TB) i još uvijek raste. U ovom članku ćemo pogledati našu filozofiju i vidjeti zašto je sljedeće desetljeće ključno za našu misiju očuvanja znanja i kulture čovječanstva. Kritični prozor Ako su ove prognoze točne, <strong>samo trebamo pričekati nekoliko godina</strong> prije nego što će cijela naša zbirka biti široko zrcaljena. Tako će, riječima Thomasa Jeffersona, biti "postavljena izvan dosega nesreće". Nažalost, pojava LLM-ova i njihova glad za podacima stavila je mnoge nositelje autorskih prava u obrambeni položaj. Još više nego što su već bili. Mnogi web stranice otežavaju struganje i arhiviranje, tužbe lete na sve strane, a sve to vrijeme fizičke knjižnice i arhivi i dalje su zanemareni. Možemo samo očekivati da će se ovi trendovi nastaviti pogoršavati, a mnoga djela biti izgubljena prije nego što uđu u javnu domenu. <strong>Na pragu smo revolucije u očuvanju, ali <q>izgubljeno se ne može povratiti.</q></strong> Imamo kritični prozor od oko 5-10 godina tijekom kojeg je još uvijek prilično skupo upravljati sjenskom knjižnicom i stvarati mnoge zrcalne kopije diljem svijeta, a tijekom kojeg pristup još nije potpuno zatvoren. Ako uspijemo premostiti ovaj prozor, tada ćemo doista sačuvati ljudsko znanje i kulturu zauvijek. Ne smijemo dopustiti da ovo vrijeme propadne. Ne smijemo dopustiti da se ovaj kritični prozor zatvori pred nama. Krenimo. Kritični prozor sjene knjižnica Kako možemo tvrditi da ćemo sačuvati naše zbirke zauvijek, kada već dosežu 1 PB? Kolekcija Neke dodatne informacije o kolekciji. <a %(duxiu)s>Duxiu</a> je ogromna baza podataka skeniranih knjiga, koju je stvorila <a %(chaoxing)s>SuperStar Digital Library Group</a>. Većina su akademske knjige, skenirane kako bi bile dostupne digitalno sveučilištima i knjižnicama. Za našu publiku koja govori engleski, <a %(library_princeton)s>Princeton</a> i <a %(guides_lib_uw)s>University of Washington</a> imaju dobre preglede. Također postoji izvrstan članak koji daje više pozadine: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (potražite ga u Anninoj Arhivi). Knjige iz Duxiua dugo su piratizirane na kineskom internetu. Obično se prodaju za manje od dolara od strane preprodavača. Obično se distribuiraju koristeći kineski ekvivalent Google Drivea, koji je često hakiran kako bi omogućio više prostora za pohranu. Neki tehnički detalji mogu se pronaći <a %(github_duty_machine)s>ovdje</a> i <a %(github_821_github_io)s>ovdje</a>. Iako su knjige polujavno distribuirane, prilično ih je teško nabaviti u velikim količinama. Imali smo to visoko na našem popisu zadataka, i dodijelili smo nekoliko mjeseci punog radnog vremena za to. Međutim, nedavno nam se obratio nevjerojatan, izvanredan i talentiran volonter, rekavši da je već obavio sav taj posao — uz velike troškove. Podijelio je cijelu kolekciju s nama, ne očekujući ništa zauzvrat, osim jamstva dugoročnog očuvanja. Zaista izvanredno. Složili su se tražiti pomoć na ovaj način kako bi se kolekcija OCR-irala. Kolekcija sadrži 7.543.702 datoteke. To je više nego Library Genesis non-fiction (oko 5,3 milijuna). Ukupna veličina datoteka je oko 359TB (326TiB) u trenutnom obliku. Otvoreni smo za druge prijedloge i ideje. Samo nas kontaktirajte. Pogledajte Anninu Arhivu za više informacija o našim kolekcijama, naporima za očuvanje i kako možete pomoći. Hvala! Primjer stranica Kako biste nam dokazali da imate dobar sustav, ovdje su neke primjer stranice za početak, iz knjige o superprovodnicima. Vaš sustav treba pravilno obraditi matematiku, tablice, grafikone, fusnote i slično. Pošaljite svoje obrađene stranice na naš email. Ako izgledaju dobro, poslat ćemo vam više privatno, i očekujemo da ćete moći brzo pokrenuti svoj sustav i na njima. Kada budemo zadovoljni, možemo sklopiti dogovor. - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kineska verzija 中文版</a>, <a %(news_ycombinator)s>Rasprava na Hacker Newsu</a> Ovo je kratki blog post. Tražimo neku tvrtku ili instituciju koja bi nam pomogla s OCR-om i ekstrakcijom teksta za ogromnu zbirku koju smo stekli, u zamjenu za ekskluzivan rani pristup. Nakon razdoblja embarga, naravno, objavit ćemo cijelu zbirku. Visokokvalitetni akademski tekst izuzetno je koristan za treniranje LLM-ova. Iako je naša kolekcija kineska, ovo bi trebalo biti korisno i za treniranje engleskih LLM-ova: čini se da modeli kodiraju pojmove i znanje bez obzira na izvorni jezik. Za to je potrebno izvući tekst iz skenova. Što dobiva Annina Arhiva iz toga? Pretraživanje punog teksta knjiga za svoje korisnike. Budući da su naši ciljevi usklađeni s ciljevima LLM developera, tražimo suradnika. Spremni smo vam dati <strong>ekskluzivan rani pristup ovoj kolekciji u velikim količinama na 1 godinu</strong>, ako možete pravilno obaviti OCR i ekstrakciju teksta. Ako ste voljni podijeliti cijeli kod vašeg sustava s nama, spremni smo produžiti embargo na kolekciju. Ekskluzivan pristup za LLM tvrtke najvećoj zbirci kineskih nefikcijskih knjiga na svijetu <em><strong>Ukratko:</strong> Annina Arhiva stekla je jedinstvenu zbirku od 7,5 milijuna / 350TB kineskih nefikcijskih knjiga — veću od Library Genesis. Spremni smo dati LLM tvrtki ekskluzivan pristup, u zamjenu za visokokvalitetni OCR i ekstrakciju teksta.</em> Arhitektura sustava Dakle, recimo da ste pronašli neke tvrtke koje su spremne ugostiti vašu web stranicu bez da vas zatvore — nazovimo ih “pružatelji slobode” 😄. Brzo ćete otkriti da je hosting svega kod njih prilično skup, pa biste možda htjeli pronaći neke “jeftine pružatelje” i tamo obaviti stvarni hosting, proksirajući kroz pružatelje slobode. Ako to učinite ispravno, jeftini pružatelji nikada neće znati što hostirate i nikada neće primiti nikakve pritužbe. Uz sve te pružatelje postoji rizik da vas ipak zatvore, pa vam je potrebna i redundancija. To nam je potrebno na svim razinama naše hrpe. Jedna donekle slobodoljubiva tvrtka koja se stavila u zanimljiv položaj je Cloudflare. Oni su <a %(blog_cloudflare)s>tvrdili</a> da nisu pružatelj hostinga, već usluga, poput ISP-a. Stoga nisu podložni DMCA ili drugim zahtjevima za uklanjanje i prosljeđuju sve zahtjeve vašem stvarnom pružatelju hostinga. Čak su išli toliko daleko da su išli na sud kako bi zaštitili ovu strukturu. Stoga ih možemo koristiti kao još jedan sloj keširanja i zaštite. Cloudflare ne prihvaća anonimna plaćanja, pa možemo koristiti samo njihov besplatni plan. To znači da ne možemo koristiti njihove značajke balansiranja opterećenja ili prebacivanja u slučaju kvara. Stoga smo <a %(annas_archive_l255)s>to implementirali sami</a> na razini domene. Prilikom učitavanja stranice, preglednik će provjeriti je li trenutna domena još uvijek dostupna, a ako nije, prepisuje sve URL-ove na drugu domenu. Budući da Cloudflare kešira mnoge stranice, to znači da korisnik može doći na našu glavnu domenu, čak i ako je proxy poslužitelj isključen, a zatim na sljedećem kliku biti prebačen na drugu domenu. Također imamo i normalne operativne brige s kojima se moramo nositi, kao što su praćenje zdravlja poslužitelja, bilježenje pogrešaka na pozadini i prednjem dijelu, i tako dalje. Naša arhitektura prebacivanja u slučaju kvara omogućuje veću robusnost i na ovom području, na primjer pokretanjem potpuno različitog skupa poslužitelja na jednoj od domena. Čak možemo pokrenuti starije verzije koda i datasets na ovoj zasebnoj domeni, u slučaju da kritična greška u glavnoj verziji prođe nezapaženo. Također se možemo zaštititi od Cloudflare-a koji se okrene protiv nas, uklanjanjem s jedne od domena, kao što je ova zasebna domena. Moguće su različite permutacije ovih ideja. Zaključak Bilo je zanimljivo iskustvo naučiti kako postaviti robusnu i otpornu tražilicu za shadow knjižnicu. Ima još puno detalja za podijeliti u kasnijim postovima, pa mi javite o čemu biste željeli saznati više! Kao i uvijek, tražimo donacije za podršku ovom radu, pa svakako pogledajte stranicu Doniraj na Anninoj Arhivi. Također tražimo druge vrste podrške, kao što su grantovi, dugoročni sponzori, pružatelji usluga plaćanja visokog rizika, možda čak i (ukusne!) reklame. A ako želite doprinijeti svojim vremenom i vještinama, uvijek tražimo programere, prevoditelje i slično. Hvala vam na interesu i podršci. Inovacijski tokeni Počnimo s našom tehnološkom hrpom. Namjerno je dosadna. Koristimo Flask, MariaDB i ElasticSearch. To je doslovno to. Pretraživanje je uglavnom riješen problem i ne namjeravamo ga ponovno izmišljati. Osim toga, moramo potrošiti naše <a %(mcfunley)s>inovacijske tokene</a> na nešto drugo: da nas vlasti ne uklone. Dakle, koliko je točno legalan ili ilegalan Annin Arhiv? To uglavnom ovisi o pravnoj jurisdikciji. Većina zemalja vjeruje u neki oblik autorskih prava, što znači da su ljudima ili tvrtkama dodijeljeni ekskluzivni monopol na određene vrste djela za određeno vremensko razdoblje. Usput rečeno, u Anninom Arhivu vjerujemo da, iako postoje neke koristi, autorska prava su u cjelini negativna za društvo — ali to je priča za neki drugi put. Ovaj ekskluzivni monopol na određena djela znači da je ilegalno za bilo koga izvan ovog monopola izravno distribuirati ta djela — uključujući nas. Ali Annin Arhiv je tražilica koja ne distribuira izravno ta djela (barem ne na našoj clearnet web stranici), pa bismo trebali biti u redu, zar ne? Ne baš. U mnogim jurisdikcijama nije samo ilegalno distribuirati zaštićena djela, već i povezivati se s mjestima koja to čine. Klasičan primjer toga je američki DMCA zakon. To je najstroži kraj spektra. Na drugom kraju spektra teoretski bi mogle postojati zemlje bez ikakvih zakona o autorskim pravima, ali takve zapravo ne postoje. Gotovo svaka zemlja ima neki oblik zakona o autorskim pravima. Provedba je druga priča. Postoje mnoge zemlje s vladama koje ne mare za provedbu zakona o autorskim pravima. Postoje i zemlje između dvaju ekstrema, koje zabranjuju distribuciju zaštićenih djela, ali ne zabranjuju povezivanje s takvim djelima. Još jedno razmatranje je na razini tvrtke. Ako tvrtka posluje u jurisdikciji koja ne mari za autorska prava, ali sama tvrtka nije spremna preuzeti bilo kakav rizik, tada bi mogli zatvoriti vašu web stranicu čim se netko požali na nju. Na kraju, veliko razmatranje su plaćanja. Budući da moramo ostati anonimni, ne možemo koristiti tradicionalne metode plaćanja. To nas ostavlja s kriptovalutama, a samo mali broj tvrtki ih podržava (postoje virtualne debitne kartice plaćene kripto, ali često nisu prihvaćene). - Anna i tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Vodim <a %(wikipedia_annas_archive)s>Anninu Arhivu</a>, najveći svjetski open-source neprofitni pretraživač za <a %(wikipedia_shadow_library)s>sjene knjižnice</a>, poput Sci-Huba, Library Genesis i Z-Library. Naš cilj je učiniti znanje i kulturu lako dostupnima, i na kraju izgraditi zajednicu ljudi koji zajedno arhiviraju i čuvaju <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>sve knjige na svijetu</a>. U ovom članku pokazat ću kako vodimo ovu web stranicu i jedinstvene izazove koji dolaze s vođenjem web stranice s upitnim pravnim statusom, budući da ne postoji “AWS za sjene dobrotvorne organizacije”. <em>Pogledajte i sestrinski članak <a %(blog_how_to_become_a_pirate_archivist)s>Kako postati piratski arhivist</a>.</em> Kako voditi sjenu knjižnicu: operacije u Anninoj Arhivi Ne postoji <q>AWS za sjene dobrotvorne organizacije,</q> pa kako vodimo Anninu Arhivu? Alati Aplikacijski poslužitelj: Flask, MariaDB, ElasticSearch, Docker. Razvoj: Gitlab, Weblate, Zulip. Upravljanje poslužiteljem: Ansible, Checkmk, UFW. Onion statički hosting: Tor, Nginx. Proxy poslužitelj: Varnish. Pogledajmo koje alate koristimo za postizanje svega ovoga. Ovo se vrlo brzo razvija kako nailazimo na nove probleme i pronalazimo nova rješenja. Postoje neke odluke oko kojih smo se dvoumili. Jedna od njih je komunikacija između servera: prije smo koristili Wireguard za to, ali smo otkrili da povremeno prestaje prenositi bilo kakve podatke ili prenosi podatke samo u jednom smjeru. To se dogodilo s nekoliko različitih Wireguard postavki koje smo isprobali, kao što su <a %(github_costela_wesher)s>wesher</a> i <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Također smo pokušali tunelirati portove preko SSH-a, koristeći autossh i sshuttle, ali smo naišli na <a %(github_sshuttle)s>probleme tamo</a> (iako mi još uvijek nije jasno pati li autossh od problema TCP-over-TCP ili ne — samo mi se čini kao nespretno rješenje, ali možda je zapravo u redu?). Umjesto toga, vratili smo se na izravne veze između servera, skrivajući da server radi na jeftinim pružateljima usluga koristeći IP-filtriranje s UFW. Ovo ima nedostatak da Docker ne radi dobro s UFW, osim ako ne koristite <code>network_mode: "host"</code>. Sve ovo je malo sklonije greškama, jer ćete izložiti svoj server internetu s samo malom pogrešnom konfiguracijom. Možda bismo se trebali vratiti na autossh — povratne informacije bi bile vrlo dobrodošle ovdje. Također smo se dvoumili između Varnish i Nginx. Trenutno nam se sviđa Varnish, ali ima svoje hirove i grube rubove. Isto vrijedi i za Checkmk: ne volimo ga, ali za sada radi. Weblate je bio u redu, ali ne i nevjerojatan — ponekad se bojim da će izgubiti moje podatke kad god pokušam sinkronizirati s našim git repo-om. Flask je bio dobar u cjelini, ali ima neke čudne hirove koji su koštali puno vremena za otklanjanje grešaka, kao što su konfiguriranje prilagođenih domena ili problemi s njegovom integracijom SqlAlchemy. Do sada su ostali alati bili izvrsni: nemamo ozbiljnih pritužbi na MariaDB, ElasticSearch, Gitlab, Zulip, Docker i Tor. Svi su imali neke probleme, ali ništa previše ozbiljno ili vremenski zahtjevno. Zajednica Prvi izazov mogao bi biti iznenađujući. Nije tehnički problem, niti pravni problem. To je psihološki problem: raditi ovaj posao u sjeni može biti nevjerojatno usamljeno. Ovisno o tome što planirate raditi i vašem modelu prijetnje, možda ćete morati biti vrlo oprezni. Na jednom kraju spektra imamo ljude poput Alexandre Elbakyan*, osnivačice Sci-Huba, koja je vrlo otvorena o svojim aktivnostima. Ali ona je u velikom riziku od uhićenja ako bi posjetila zapadnu zemlju u ovom trenutku i mogla bi se suočiti s desetljećima zatvora. Je li to rizik koji biste bili spremni preuzeti? Mi smo na drugom kraju spektra; vrlo smo oprezni da ne ostavimo nikakav trag i imamo snažnu operativnu sigurnost. * Kao što je spomenuto na HN od strane "ynno", Alexandra u početku nije željela biti poznata: "Njezini serveri su bili postavljeni da emitiraju detaljne poruke o greškama iz PHP-a, uključujući puni put do izvornog datoteke koja je bila pod direktorijem /home/<USER>" Dakle, koristite nasumična korisnička imena na računalima koja koristite za ove stvari, u slučaju da nešto pogrešno konfigurirate. Ta tajnost, međutim, dolazi s psihološkim troškom. Većina ljudi voli biti prepoznata za posao koji rade, a ipak ne možete preuzeti nikakve zasluge za to u stvarnom životu. Čak i jednostavne stvari mogu biti izazovne, poput prijatelja koji vas pitaju čime ste se bavili (u nekom trenutku "igranje s mojim NAS-om / homelabom" postaje dosadno). Zato je toliko važno pronaći neku zajednicu. Možete se odreći dijela operativne sigurnosti povjeravajući se nekim vrlo bliskim prijateljima, za koje znate da im možete duboko vjerovati. Čak i tada budite oprezni da ništa ne stavljate u pisani oblik, u slučaju da moraju predati svoje e-mailove vlastima ili ako su njihovi uređaji kompromitirani na neki drugi način. Još bolje je pronaći neke kolege pirate. Ako su vaši bliski prijatelji zainteresirani pridružiti vam se, odlično! Inače, možda ćete moći pronaći druge online. Nažalost, ovo je još uvijek nišna zajednica. Do sada smo pronašli samo nekolicinu drugih koji su aktivni u ovom prostoru. Dobra početna mjesta čine se forumi Library Genesis i r/DataHoarder. Tim za arhiviranje također ima istomišljenike, iako djeluju unutar zakona (čak i ako u nekim sivim područjima zakona). Tradicionalne "warez" i piratske scene također imaju ljude koji razmišljaju na sličan način. Otvoreni smo za ideje o tome kako potaknuti zajednicu i istražiti ideje. Slobodno nam pošaljite poruku na Twitteru ili Redditu. Možda bismo mogli organizirati neku vrstu foruma ili grupe za razgovor. Jedan izazov je što to lako može biti cenzurirano kada se koriste uobičajene platforme, pa bismo to morali sami hostati. Također postoji kompromis između vođenja ovih rasprava potpuno javno (više potencijalnog angažmana) naspram privatnog (ne dopuštajući potencijalnim "metama" da znaju da ih planiramo pretražiti). Morat ćemo razmisliti o tome. Javite nam ako ste zainteresirani za ovo! Zaključak Nadamo se da će ovo biti korisno za novopečene piratske arhiviste. Uzbuđeni smo što vas možemo pozdraviti u ovom svijetu, stoga se ne ustručavajte obratiti nam se. Sačuvajmo što više svjetskog znanja i kulture, i proširimo ih daleko i široko. Projekti 4. Odabir podataka Često možete koristiti metadata kako biste odredili razuman podskup podataka za preuzimanje. Čak i ako na kraju želite preuzeti sve podatke, može biti korisno prioritizirati najvažnije stavke prvo, u slučaju da budete otkriveni i obrana se poboljša, ili zato što ćete trebati kupiti više diskova, ili jednostavno zato što se nešto drugo pojavi u vašem životu prije nego što uspijete preuzeti sve. Na primjer, kolekcija može imati više izdanja istog osnovnog resursa (poput knjige ili filma), gdje je jedno označeno kao najbolje kvalitete. Spremanje tih izdanja prvo bi imalo puno smisla. Možda ćete na kraju htjeti spremiti sva izdanja, jer u nekim slučajevima metadata može biti pogrešno označena, ili mogu postojati nepoznati kompromisi između izdanja (na primjer, "najbolje izdanje" može biti najbolje na većinu načina, ali lošije na druge načine, poput filma s višom rezolucijom, ali bez titlova). Također možete pretraživati svoju bazu podataka metadata kako biste pronašli zanimljive stvari. Koja je najveća datoteka koja je hostirana i zašto je tako velika? Koja je najmanja datoteka? Postoje li zanimljivi ili neočekivani obrasci kada je riječ o određenim kategorijama, jezicima i slično? Postoje li duplikati ili vrlo slični naslovi? Postoje li obrasci kada su podaci dodani, poput jednog dana kada je mnogo datoteka dodano odjednom? Često možete puno naučiti gledajući skup podataka na različite načine. U našem slučaju, deduplicirali smo knjige iz Z-Library koristeći md5 hashove u Library Genesis, čime smo uštedjeli puno vremena za preuzimanje i prostora na disku. Ovo je prilično jedinstvena situacija. U većini slučajeva ne postoje sveobuhvatne baze podataka o tome koje su datoteke već pravilno sačuvane od strane drugih pirata. Ovo je samo po sebi velika prilika za nekoga. Bilo bi sjajno imati redovito ažurirani pregled stvari poput glazbe i filmova koji su već široko dostupni na torrent web stranicama, i stoga su niži prioritet za uključivanje u piratske zrcale. 6. Distribucija Imate podatke, čime ste stekli posjed prvog piratskog zrcala vašeg cilja (najvjerojatnije). Na mnogo načina najteži dio je gotov, ali najrizičniji dio je još pred vama. Uostalom, do sada ste bili neprimjetni; letjeli ste ispod radara. Sve što ste trebali učiniti bilo je koristiti dobar VPN cijelo vrijeme, ne ispunjavati svoje osobne podatke u bilo kojim obrascima (naravno), i možda koristiti posebnu sesiju preglednika (ili čak drugo računalo). Sada morate distribuirati podatke. U našem slučaju prvo smo htjeli vratiti knjige natrag u Library Genesis, ali smo brzo otkrili poteškoće u tome (razvrstavanje fikcije i nefikcije). Stoga smo se odlučili za distribuciju koristeći Library Genesis-stil torrente. Ako imate priliku doprinijeti postojećem projektu, to bi vam moglo uštedjeti puno vremena. Međutim, trenutno nema mnogo dobro organiziranih piratskih zrcala. Dakle, recimo da se odlučite sami distribuirati torrente. Pokušajte te datoteke držati malima, kako bi ih bilo lako zrcaliti na drugim web stranicama. Tada ćete morati sami seedati torrente, dok još uvijek ostajete anonimni. Možete koristiti VPN (s ili bez prosljeđivanja portova), ili platiti s tumbled Bitcoinima za Seedbox. Ako ne znate što neki od tih pojmova znače, imat ćete puno čitanja za obaviti, jer je važno da razumijete rizike. Možete hostati same torrent datoteke na postojećim torrent web stranicama. U našem slučaju, odlučili smo zapravo hostati web stranicu, jer smo također željeli širiti našu filozofiju na jasan način. Možete to učiniti sami na sličan način (koristimo Njalla za naše domene i hosting, plaćeno s tumbled Bitcoinima), ali slobodno nas kontaktirajte kako bismo mi hostali vaše torrente. Želimo izgraditi sveobuhvatan indeks piratskih zrcala tijekom vremena, ako ova ideja zaživi. Što se tiče odabira VPN-a, o tome je već puno napisano, pa ćemo samo ponoviti opći savjet da birate prema reputaciji. Stvarne sudski testirane politike bez logova s dugim zapisima o zaštiti privatnosti su opcija s najnižim rizikom, po našem mišljenju. Imajte na umu da čak i kada sve radite ispravno, nikada ne možete doći do nultog rizika. Na primjer, kada seedate svoje torrente, visoko motivirani akter države može vjerojatno pogledati dolazne i odlazne tokove podataka za VPN servere i zaključiti tko ste. Ili jednostavno možete negdje pogriješiti. Vjerojatno smo već pogriješili, i opet ćemo. Srećom, države ne brinu <em>toliko</em> o piratstvu. Jedna odluka koju treba donijeti za svaki projekt je hoćete li ga objaviti koristeći isti identitet kao prije ili ne. Ako nastavite koristiti isto ime, tada bi pogreške u operativnoj sigurnosti iz ranijih projekata mogle doći da vas ugrizu. Ali objavljivanje pod različitim imenima znači da ne gradite dugotrajniju reputaciju. Odabrali smo imati snažnu operativnu sigurnost od početka kako bismo mogli nastaviti koristiti isti identitet, ali nećemo oklijevati objaviti pod drugim imenom ako pogriješimo ili ako to okolnosti zahtijevaju. Širenje vijesti može biti nezgodno. Kao što smo rekli, ovo je još uvijek nišna zajednica. Prvotno smo objavili na Redditu, ali smo stvarno dobili zamah na Hacker Newsu. Za sada je naša preporuka da objavite na nekoliko mjesta i vidite što će se dogoditi. I opet, kontaktirajte nas. Voljeli bismo širiti vijest o više piratskih arhivskih napora. 1. Odabir domene / filozofija Nema nedostatka znanja i kulturne baštine koju treba sačuvati, što može biti neodoljivo. Zato je često korisno uzeti trenutak i razmisliti o tome kakav može biti vaš doprinos. Svatko ima drugačiji način razmišljanja o tome, ali evo nekoliko pitanja koja biste mogli postaviti sebi: U našem slučaju, posebno nam je bilo stalo do dugoročnog očuvanja znanosti. Znali smo za Library Genesis i kako je u potpunosti zrcaljen mnogo puta putem torrenta. Voljeli smo tu ideju. Onda je jednog dana netko od nas pokušao pronaći neke znanstvene udžbenike na Library Genesis, ali ih nije mogao pronaći, što je dovelo u pitanje koliko je zapravo kompletan. Zatim smo te udžbenike potražili online i pronašli ih na drugim mjestima, što je posijalo sjeme za naš projekt. Čak i prije nego što smo znali za Z-Library, imali smo ideju da ne pokušavamo ručno prikupiti sve te knjige, već da se usredotočimo na zrcaljenje postojećih zbirki i doprinos njima natrag u Library Genesis. Koje vještine imate koje možete iskoristiti u svoju korist? Na primjer, ako ste stručnjak za online sigurnost, možete pronaći načine za prevladavanje IP blokova za sigurne ciljeve. Ako ste izvrsni u organiziranju zajednica, možda možete okupiti neke ljude oko cilja. Korisno je znati nešto programiranja, makar samo za održavanje dobre operativne sigurnosti tijekom ovog procesa. Na koje područje s visokim utjecajem biste se trebali usredotočiti? Ako ćete provesti X sati na piratskom arhiviranju, kako možete dobiti najveći "bang for your buck"? Koji su jedinstveni načini na koje razmišljate o ovome? Možda imate neke zanimljive ideje ili pristupe koje drugi možda nisu primijetili. Koliko vremena imate za ovo? Naš savjet bi bio da počnete s malim projektima i radite na većim projektima kako se budete navikavali, ali to može postati sveobuhvatno. Zašto vas ovo zanima? Što vas strastveno zanima? Ako možemo okupiti grupu ljudi koji svi arhiviraju vrste stvari do kojih im je posebno stalo, to bi pokrilo mnogo toga! Znati ćete puno više od prosječne osobe o svojoj strasti, kao što su važni podaci za spremanje, koje su najbolje zbirke i online zajednice, i tako dalje. 3. Preuzimanje metadata Datum dodavanja/modifikacije: kako biste se kasnije mogli vratiti i preuzeti datoteke koje niste preuzeli prije (iako često možete koristiti i ID ili hash za ovo). Hash (md5, sha1): za potvrdu da ste ispravno preuzeli datoteku. ID: može biti neki interni ID, ali ID-ovi poput ISBN-a ili DOI-a su također korisni. Naziv datoteke / lokacija Opis, kategorija, oznake, autori, jezik, itd. Veličina: za izračunavanje koliko prostora na disku trebate. Idemo malo tehničkije. Za stvarno preuzimanje metadata s web stranica, stvari smo zadržali prilično jednostavnima. Koristimo Python skripte, ponekad curl, i MySQL bazu podataka za pohranu rezultata. Nismo koristili nikakav napredni softver za preuzimanje koji može mapirati složene web stranice, jer smo do sada trebali preuzeti samo jednu ili dvije vrste stranica jednostavnim nabrajanjem kroz ID-ove i parsiranjem HTML-a. Ako nema lako nabrojivih stranica, možda će vam trebati pravi crawler koji pokušava pronaći sve stranice. Prije nego što počnete preuzimati cijelu web stranicu, pokušajte to učiniti ručno neko vrijeme. Prođite kroz nekoliko desetaka stranica sami, kako biste stekli osjećaj kako to funkcionira. Ponekad ćete već na ovaj način naići na IP blokade ili drugo zanimljivo ponašanje. Isto vrijedi i za preuzimanje podataka: prije nego što se previše udubite u ovu metu, provjerite možete li zapravo učinkovito preuzeti njezine podatke. Da biste zaobišli ograničenja, postoji nekoliko stvari koje možete pokušati. Postoje li druge IP adrese ili poslužitelji koji hostiraju iste podatke, ali nemaju ista ograničenja? Postoje li API krajnje točke koje nemaju ograničenja, dok druge imaju? Kojom brzinom preuzimanja vaša IP adresa biva blokirana i koliko dugo? Ili niste blokirani, već usporeni? Što ako kreirate korisnički račun, kako se tada stvari mijenjaju? Možete li koristiti HTTP/2 za održavanje otvorenih veza i povećava li to brzinu kojom možete zahtijevati stranice? Postoje li stranice koje navode više datoteka odjednom i jesu li informacije navedene tamo dovoljne? Stvari koje vjerojatno želite sačuvati uključuju: Obično to radimo u dvije faze. Prvo preuzimamo sirove HTML datoteke, obično izravno u MySQL (kako bismo izbjegli puno malih datoteka, o čemu ćemo više govoriti u nastavku). Zatim, u zasebnom koraku, prolazimo kroz te HTML datoteke i parsiramo ih u stvarne MySQL tablice. Na taj način ne morate sve ponovno preuzimati ispočetka ako otkrijete grešku u svom kodu za parsiranje, jer možete jednostavno ponovno obraditi HTML datoteke s novim kodom. Također je često lakše paralelizirati korak obrade, čime se štedi vrijeme (i možete pisati kod za obradu dok preuzimanje traje, umjesto da morate pisati oba koraka odjednom). Na kraju, imajte na umu da je za neke ciljeve sve što postoji samo scraping metadata. Postoje ogromne kolekcije metadata koje nisu pravilno sačuvane. Naslov Odabir domene / filozofija: Na što se otprilike želite usredotočiti i zašto? Koje su vaše jedinstvene strasti, vještine i okolnosti koje možete iskoristiti u svoju korist? Odabir cilja: Koju specifičnu zbirku ćete zrcaliti? Prikupljanje metadata: Katalogiziranje informacija o datotekama, bez stvarnog preuzimanja (često mnogo većih) datoteka. Odabir podataka: Na temelju metadata, sužavanje izbora podataka koji su trenutno najrelevantniji za arhiviranje. Može biti sve, ali često postoji razuman način za uštedu prostora i propusnosti. Prikupljanje podataka: Stvarno dobivanje podataka. Distribucija: Pakiranje u torrente, objavljivanje negdje, poticanje ljudi da ih šire. 5. Scraping podataka Sada ste spremni za stvarno preuzimanje podataka u velikim količinama. Kao što je ranije spomenuto, u ovom trenutku biste već trebali ručno preuzeti hrpu datoteka kako biste bolje razumjeli ponašanje i ograničenja cilja. Međutim, još uvijek će biti iznenađenja kada zapravo počnete preuzimati puno datoteka odjednom. Naš savjet ovdje je uglavnom da to bude jednostavno. Počnite s preuzimanjem hrpe datoteka. Možete koristiti Python, a zatim proširiti na više niti. Ali ponekad je čak i jednostavnije generirati Bash datoteke izravno iz baze podataka, a zatim ih pokrenuti više u više terminalskih prozora kako biste povećali opseg. Brzi tehnički trik vrijedan spomena ovdje je korištenje OUTFILE u MySQL-u, koji možete pisati bilo gdje ako onemogućite "secure_file_priv" u mysqld.cnf (i budite sigurni da također onemogućite/zaobiđete AppArmor ako ste na Linuxu). Podatke pohranjujemo na jednostavne tvrde diskove. Počnite s onim što imate i polako proširujte. Može biti zastrašujuće razmišljati o pohranjivanju stotina TB podataka. Ako je to situacija s kojom se suočavate, samo prvo izložite dobar podskup, a u svojoj najavi zatražite pomoć u pohranjivanju ostatka. Ako želite sami nabaviti više tvrdih diskova, r/DataHoarder ima dobre resurse za dobivanje dobrih ponuda. Pokušajte se ne brinuti previše o naprednim datotečnim sustavima. Lako je upasti u zamku postavljanja stvari poput ZFS-a. Jedan tehnički detalj na koji treba obratiti pažnju je da mnogi datotečni sustavi ne podnose dobro puno datoteka. Otkrili smo da je jednostavno rješenje stvaranje više direktorija, npr. za različite ID raspona ili hash prefikse. Nakon preuzimanja podataka, budite sigurni da provjerite integritet datoteka koristeći hashove u metadata, ako su dostupni. 2. Odabir cilja Dostupna: ne koristi puno slojeva zaštite kako bi spriječila da se preuzmu njihovi metadata i podaci. Poseban uvid: imate neke posebne informacije o ovoj meti, kao što je poseban pristup ovoj kolekciji ili ste shvatili kako zaobići njihove obrane. Ovo nije nužno (naš nadolazeći projekt ne radi ništa posebno), ali svakako pomaže! Velika Dakle, imamo područje koje promatramo, sada koju specifičnu kolekciju želimo preslikati? Postoji nekoliko stvari koje čine dobru metu: Kada smo pronašli naše udžbenike iz znanosti na web stranicama osim Library Genesis, pokušali smo shvatiti kako su dospjeli na internet. Tada smo pronašli Z-Library i shvatili da, iako većina knjiga ne dolazi prvo tamo, one na kraju završe tamo. Saznali smo o njegovom odnosu s Library Genesis, te (financijskoj) strukturi poticaja i superiornom korisničkom sučelju, što ga je učinilo mnogo potpunijom kolekcijom. Zatim smo napravili preliminarno preuzimanje metadata i podataka, i shvatili da možemo zaobići njihova IP ograničenja preuzimanja, koristeći poseban pristup jednog od naših članova mnogim proxy poslužiteljima. Dok istražujete različite mete, već je važno sakriti svoje tragove korištenjem VPN-ova i jednokratnih email adresa, o čemu ćemo više govoriti kasnije. Jedinstvena: nije već dobro pokrivena od strane drugih projekata. Kada radimo na projektu, on ima nekoliko faza: Ovo nisu potpuno neovisne faze, i često vas uvidi iz kasnije faze vraćaju na raniju fazu. Na primjer, tijekom prikupljanja metadata možete shvatiti da cilj koji ste odabrali ima obrambene mehanizme izvan vaše razine vještine (poput IP blokova), pa se vraćate i pronalazite drugi cilj. - Ana i tim (<a %(reddit)s>Reddit</a>) Cijele knjige mogu se napisati o <em>zašto</em> digitalnog očuvanja općenito, a posebno piratskog arhivizma, ali dopustite nam da damo kratak uvod za one koji nisu previše upoznati. Svijet proizvodi više znanja i kulture nego ikad prije, ali također se više toga gubi nego ikad prije. Čovječanstvo uglavnom povjerava korporacijama poput akademskih izdavača, streaming servisa i društvenih mreža ovo nasljeđe, a one se često nisu pokazale kao veliki čuvari. Pogledajte dokumentarac Digitalna amnezija ili bilo koji govor Jasona Scotta. Postoje neke institucije koje dobro arhiviraju koliko god mogu, ali su vezane zakonom. Kao pirati, u jedinstvenoj smo poziciji arhivirati zbirke koje oni ne mogu dodirnuti, zbog provođenja autorskih prava ili drugih ograničenja. Također možemo zrcaliti zbirke mnogo puta, diljem svijeta, čime povećavamo šanse za pravilno očuvanje. Za sada, nećemo ulaziti u rasprave o prednostima i nedostacima intelektualnog vlasništva, moralnosti kršenja zakona, razmišljanjima o cenzuri ili pitanju pristupa znanju i kulturi. S tim izvan puta, zaronimo u <em>kako</em>. Podijelit ćemo kako je naš tim postao piratski arhivist i lekcije koje smo naučili putem. Postoji mnogo izazova kada krenete na ovo putovanje, i nadamo se da vam možemo pomoći kroz neke od njih. Kako postati piratski arhivist Prvi izazov mogao bi biti iznenađujući. Nije tehnički problem, niti pravni problem. To je psihološki problem. Prije nego što zaronimo, dvije novosti o Pirate Library Mirror (UREDI: premješteno na <a %(wikipedia_annas_archive)s>Annin Arhiv</a>): Dobili smo iznimno velikodušne donacije. Prva je bila 10.000 dolara od anonimne osobe koja također podržava "bookwarrior", originalnog osnivača Library Genesis. Posebna zahvala bookwarrioru za posredovanje u ovoj donaciji. Druga je bila još 10.000 dolara od anonimnog donatora, koji nas je kontaktirao nakon našeg posljednjeg izdanja i bio inspiriran da pomogne. Također smo imali niz manjih donacija. Hvala vam puno na vašoj velikodušnoj podršci. Imamo nekoliko uzbudljivih novih projekata u pripremi koje će ovo podržati, stoga ostanite s nama. Imali smo tehničkih poteškoća s veličinom našeg drugog izdanja, ali naši torrenti su sada gore i seedaju se. Također smo dobili velikodušnu ponudu od anonimne osobe da seedaju našu zbirku na svojim vrlo brzim serverima, pa radimo posebno učitavanje na njihove strojeve, nakon čega bi svi ostali koji preuzimaju zbirku trebali vidjeti veliko poboljšanje u brzini. Blog postovi Bok, ja sam Anna. Stvorila sam <a %(wikipedia_annas_archive)s>Anninu Arhivu</a>, najveću shadow knjižnicu na svijetu. Ovo je moj osobni blog, na kojem ja i moji suradnici pišemo o piratstvu, digitalnom očuvanju i još mnogo toga. Povežite se sa mnom na <a %(reddit)s>Redditu</a>. Napominjemo da je ova web stranica samo blog. Ovdje hostiramo samo naše vlastite riječi. Ovdje se ne hostiraju niti povezuju torrenti ili druge datoteke zaštićene autorskim pravima. <strong>Knjižnica</strong> - Kao i većina knjižnica, prvenstveno se fokusiramo na pisane materijale poput knjiga. Možda ćemo se u budućnosti proširiti na druge vrste medija. <strong>Zrcalo</strong> - Strogo smo zrcalo postojećih knjižnica. Fokusiramo se na očuvanje, a ne na olakšavanje pretraživanja i preuzimanja knjiga (pristup) ili poticanje velike zajednice ljudi koji doprinose novim knjigama (izvor). <strong>Pirat</strong> - Namjerno kršimo zakon o autorskim pravima u većini zemalja. To nam omogućuje da radimo nešto što pravne osobe ne mogu: osigurati da su knjige zrcaljene daleko i široko. <em>Ne povezujemo se s datotekama s ovog bloga. Molimo vas da ih pronađete sami.</em> - Ana i tim (<a %(reddit)s>Reddit</a>) Ovaj projekt (UREĐENO: premješten na <a %(wikipedia_annas_archive)s>Annina Arhiva</a>) ima za cilj doprinijeti očuvanju i oslobađanju ljudskog znanja. Dajemo svoj mali i skromni doprinos, slijedeći stope velikana prije nas. Fokus ovog projekta ilustriran je njegovim imenom: Prva knjižnica koju smo zrcalili je Z-Library. Ovo je popularna (i ilegalna) knjižnica. Oni su uzeli zbirku Library Genesis i učinili je lako pretraživom. Osim toga, postali su vrlo učinkoviti u poticanju novih doprinosa knjigama, nagrađujući korisnike koji doprinose raznim pogodnostima. Trenutno ne doprinose tim novim knjigama natrag u Library Genesis. I za razliku od Library Genesis, ne čine svoju zbirku lako zrcaljivom, što sprječava široko očuvanje. To je važno za njihov poslovni model, jer naplaćuju pristup njihovoj zbirci u velikim količinama (više od 10 knjiga dnevno). Ne donosimo moralne sudove o naplaćivanju novca za masovni pristup ilegalnoj zbirci knjiga. Nema sumnje da je Z-Library uspješno proširio pristup znanju i nabavio više knjiga. Mi smo ovdje samo da obavimo svoj dio posla: osiguramo dugoročno očuvanje ove privatne zbirke. Željeli bismo vas pozvati da pomognete u očuvanju i oslobađanju ljudskog znanja preuzimanjem i dijeljenjem naših torrenta. Pogledajte stranicu projekta za više informacija o tome kako su podaci organizirani. Također bismo vas vrlo rado pozvali da doprinesete svojim idejama o tome koje zbirke sljedeće zrcaliti i kako to učiniti. Zajedno možemo postići mnogo. Ovo je samo mali doprinos među bezbroj drugih. Hvala vam na svemu što činite. Predstavljamo Piratsko knjižnično zrcalo: Očuvanje 7TB knjiga (koje nisu u Libgenu) 10% of ljudske pisane baštine sačuvano zauvijek <strong>Google.</strong> Uostalom, oni su radili ovo istraživanje za Google Books. Međutim, njihovi metadata nisu dostupni u velikim količinama i prilično ih je teško izvući. <strong>Razni pojedinačni knjižnični sustavi i arhivi.</strong> Postoje knjižnice i arhivi koji nisu indeksirani i agregirani od strane bilo kojeg od gore navedenih, često zato što su nedovoljno financirani ili iz drugih razloga ne žele dijeliti svoje podatke s Open Library, OCLC, Googleom i tako dalje. Mnogi od njih imaju digitalne zapise dostupne putem interneta, i često nisu vrlo dobro zaštićeni, pa ako želite pomoći i zabaviti se učeći o čudnim knjižničnim sustavima, ovo su sjajna polazišta. <strong>ISBNdb.</strong> Ovo je tema ovog blog posta. ISBNdb pretražuje razne web stranice za metadata knjiga, posebno podatke o cijenama, koje zatim prodaju prodavačima knjiga, kako bi mogli odrediti cijene svojih knjiga u skladu s ostatkom tržišta. Budući da su ISBN-ovi danas prilično univerzalni, učinkovito su izgradili "web stranicu za svaku knjigu". <strong>Open Library.</strong> Kao što je ranije spomenuto, to je njihova cijela misija. Prikupili su ogromne količine podataka iz knjižnica koje surađuju i nacionalnih arhiva, i nastavljaju to činiti. Također imaju volontere knjižničare i tehnički tim koji pokušava deduplicirati zapise i označiti ih svim vrstama metadata. Najbolje od svega, njihov dataset je potpuno otvoren. Možete ga jednostavno <a %(openlibrary)s>preuzeti</a>. <strong>WorldCat.</strong> Ovo je web stranica koju vodi neprofitna organizacija OCLC, koja prodaje sustave za upravljanje knjižnicama. Oni agregiraju metadata knjiga iz mnogih knjižnica i čine ih dostupnima putem web stranice WorldCat. Međutim, oni također zarađuju prodajom tih podataka, pa nisu dostupni za masovno preuzimanje. Imaju neke ograničenije skupove podataka dostupne za preuzimanje, u suradnji s određenim knjižnicama. 1. Za neku razumnu definiciju "zauvijek". ;) 2. Naravno, pisana baština čovječanstva je mnogo više od knjiga, posebno danas. Za potrebe ovog posta i naših nedavnih izdanja fokusiramo se na knjige, ali naši interesi sežu dalje. 3. Mnogo više se može reći o Aaronu Swartzu, ali željeli smo ga samo ukratko spomenuti, budući da igra ključnu ulogu u ovoj priči. Kako vrijeme prolazi, sve više ljudi može naići na njegovo ime po prvi put i potom sami istražiti. <strong>Fizičke kopije.</strong> Očito, ovo nije vrlo korisno, jer su to samo duplikati istog materijala. Bilo bi sjajno kada bismo mogli sačuvati sve bilješke koje ljudi prave u knjigama, poput Fermatovih poznatih "škrabotina na marginama". Ali, nažalost, to će ostati san arhivista. <strong>“Izdanja”.</strong> Ovdje brojite svaku jedinstvenu verziju knjige. Ako je bilo što drugačije, poput drugačije naslovnice ili drugačijeg predgovora, računa se kao drugo izdanje. <strong>Datoteke.</strong> Kada radite s sjenskim knjižnicama poput Library Genesis, Sci-Hub ili Z-Library, postoji dodatno razmatranje. Može postojati više skenova istog izdanja. I ljudi mogu napraviti bolje verzije postojećih datoteka, skeniranjem teksta pomoću OCR-a ili ispravljanjem stranica koje su skenirane pod kutom. Želimo brojati te datoteke kao jedno izdanje, što bi zahtijevalo dobar metadata ili deduplikaciju koristeći mjere sličnosti dokumenata. <strong>“Djela”.</strong> Na primjer, “Harry Potter i Odaja tajni” kao logički koncept, obuhvaćajući sve verzije, poput različitih prijevoda i ponovnih izdanja. Ovo je donekle korisna definicija, ali može biti teško povući granicu što se računa. Na primjer, vjerojatno želimo sačuvati različite prijevode, iako ponovna izdanja s manjim razlikama možda nisu toliko važna. - Ana i tim (<a %(reddit)s>Reddit</a>) S Piratskim knjižničnim zrcalom (UREĐENO: premješteno na <a %(wikipedia_annas_archive)s>Annina Arhiva</a>), naš cilj je uzeti sve knjige na svijetu i sačuvati ih zauvijek.<sup>1</sup> Između naših Z-Library torrenta i originalnih Library Genesis torrenta, imamo 11,783,153 datoteka. Ali koliko je to zapravo? Ako bismo pravilno deduplicirali te datoteke, koliki postotak svih knjiga na svijetu smo sačuvali? Zaista bismo voljeli imati nešto poput ovoga: Počnimo s nekim grubim brojevima: U Z-Library/Libgen i Open Library ima mnogo više knjiga nego jedinstvenih ISBN-ova. Znači li to da mnoge od tih knjiga nemaju ISBN-ove, ili jednostavno nedostaje ISBN metadata? Vjerojatno možemo odgovoriti na ovo pitanje kombinacijom automatskog podudaranja na temelju drugih atributa (naslov, autor, izdavač, itd.), povlačenjem više izvora podataka i izvlačenjem ISBN-ova iz stvarnih skenova knjiga (u slučaju Z-Library/Libgen). Koliko od tih ISBN-ova je jedinstveno? Ovo je najbolje ilustrirano Vennovim dijagramom: Da budemo precizniji: Iznenadilo nas je koliko malo preklapanja postoji! ISBNdb ima ogroman broj ISBN-ova koji se ne pojavljuju ni u Z-Library ni u Open Library, a isto vrijedi (u manjoj, ali još uvijek značajnoj mjeri) i za druga dva. Ovo postavlja mnoga nova pitanja. Koliko bi automatsko usklađivanje pomoglo u označavanju knjiga koje nisu označene s ISBN-ovima? Bi li bilo puno podudaranja i stoga povećanog preklapanja? Također, što bi se dogodilo ako uvedemo 4. ili 5. dataset? Koliko bismo tada vidjeli preklapanja? Ovo nam daje početnu točku. Sada možemo pogledati sve ISBN-ove koji nisu bili u datasetu Z-Library, a koji se također ne podudaraju s poljima naslova/autora. To nam može pomoći u očuvanju svih knjiga na svijetu: prvo pretraživanjem interneta za skenove, zatim odlaskom u stvarni život kako bismo skenirali knjige. Potonje bi čak moglo biti financirano od strane zajednice ili potaknuto "nagradama" od ljudi koji bi željeli vidjeti određene knjige digitalizirane. Sve to je priča za neko drugo vrijeme. Ako želite pomoći u bilo kojem od ovih zadataka — daljnja analiza; prikupljanje više metadata; pronalaženje više knjiga; OCR-iranje knjiga; rad na ovome za druge domene (npr. radovi, audioknjige, filmovi, TV emisije, časopisi) ili čak omogućavanje dostupnosti nekih od ovih podataka za stvari poput ML / obuke velikih jezičnih modela — molimo kontaktirajte me (<a %(reddit)s>Reddit</a>). Ako ste posebno zainteresirani za analizu podataka, radimo na tome da naši Datasets i skripte budu dostupni u formatu koji je lakši za korištenje. Bilo bi sjajno kada biste mogli jednostavno forkati bilježnicu i početi se igrati s ovim. Na kraju, ako želite podržati ovaj rad, molimo razmislite o donaciji. Ovo je potpuno volonterska operacija, a vaš doprinos čini veliku razliku. Svaka pomoć je važna. Trenutno primamo donacije u kriptovalutama; pogledajte stranicu Doniraj na Aninoj Arhivi. Za postotak, trebamo nazivnik: ukupan broj ikad objavljenih knjiga.<sup>2</sup> Prije propasti Google Books, inženjer na projektu, Leonid Taycher, <a %(booksearch_blogspot)s>pokušao je procijeniti</a> ovaj broj. Došao je — u šali — do 129,864,880 (“barem do nedjelje”). Procijenio je ovaj broj izgradnjom jedinstvene baze podataka svih knjiga na svijetu. Za to je spojio različite Datasets i zatim ih spojio na razne načine. Usput, postoji još jedna osoba koja je pokušala katalogizirati sve knjige na svijetu: Aaron Swartz, pokojni digitalni aktivist i suosnivač Reddita.<sup>3</sup> On je <a %(youtube)s>pokrenuo Open Library</a> s ciljem "jedne web stranice za svaku knjigu ikad objavljenu", kombinirajući podatke iz mnogih različitih izvora. Na kraju je platio najvišu cijenu za svoj rad na digitalnom očuvanju kada je bio procesuiran zbog masovnog preuzimanja akademskih radova, što je dovelo do njegovog samoubojstva. Nepotrebno je reći, to je jedan od razloga zašto je naša grupa pseudonimna i zašto smo vrlo oprezni. Open Library još uvijek herojski vode ljudi iz Internet Archivea, nastavljajući Aaronovo nasljeđe. Vratit ćemo se na ovo kasnije u ovom postu. U Googleovom blog postu, Taycher opisuje neke od izazova s procjenom ovog broja. Prvo, što čini knjigu? Postoji nekoliko mogućih definicija: “Izdanja” se čine najpraktičnijom definicijom onoga što su “knjige”. Prikladno, ova definicija se također koristi za dodjeljivanje jedinstvenih ISBN brojeva. ISBN, ili Međunarodni standardni broj knjige, obično se koristi za međunarodnu trgovinu, jer je integriran s međunarodnim sustavom barkodova (“Međunarodni broj artikla”). Ako želite prodati knjigu u trgovinama, treba vam barkod, pa dobijete ISBN. Taycherov blog post spominje da, iako su ISBN-ovi korisni, nisu univerzalni, jer su stvarno prihvaćeni tek sredinom sedamdesetih, i to ne svugdje u svijetu. Ipak, ISBN je vjerojatno najšire korišten identifikator izdanja knjiga, pa je to naš najbolji početni korak. Ako možemo pronaći sve ISBN-ove na svijetu, dobit ćemo koristan popis knjiga koje još treba sačuvati. Dakle, gdje dobivamo podatke? Postoji nekoliko postojećih napora koji pokušavaju sastaviti popis svih knjiga na svijetu: U ovom postu, sretni smo što možemo najaviti malo izdanje (u usporedbi s našim prethodnim izdanjima Z-Library). Pretražili smo većinu ISBNdb-a i učinili podatke dostupnima za preuzimanje putem torrenta na web stranici Pirate Library Mirror (UREDI: premješteno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>; nećemo ga ovdje izravno povezati, samo ga potražite). To je oko 30,9 milijuna zapisa (20GB kao <a %(jsonlines)s>JSON Lines</a>; 4,4GB komprimirano). Na njihovoj web stranici tvrde da zapravo imaju 32,6 milijuna zapisa, pa smo možda nekako propustili neke, ili <em>oni</em> možda rade nešto pogrešno. U svakom slučaju, za sada nećemo dijeliti točno kako smo to učinili — ostavit ćemo to kao vježbu za čitatelja. ;-) Ono što ćemo podijeliti je neka preliminarna analiza, kako bismo pokušali doći bliže procjeni broja knjiga na svijetu. Pogledali smo tri skupa podataka: ovaj novi skup podataka ISBNdb, naše originalno izdanje metadata koje smo preuzeli iz sjenske knjižnice Z-Library (koja uključuje Library Genesis) i Open Library podatkovni dump. ISBNdb dump, ili Koliko je knjiga sačuvano zauvijek? Ako bismo pravilno deduplicirali datoteke iz sjenskih knjižnica, koliki postotak svih knjiga na svijetu smo sačuvali? Ažuriranja o <a %(wikipedia_annas_archive)s>Aninoj Arhivi</a>, najvećoj istinski otvorenoj knjižnici u povijesti čovječanstva. <em>WorldCat redizajn</em> Podaci <strong>Format?</strong> <a %(blog)s>Anina Arhiva Kontejneri (AAC)</a>, što je u suštini <a %(jsonlines)s>JSON Lines</a> komprimiran sa <a %(zstd)s>Zstandard</a>, plus neki standardizirani semantici. Ovi kontejneri obuhvaćaju različite vrste zapisa, na temelju različitih izvlačenja koje smo primijenili. Prije godinu dana, <a %(blog)s>krenuli smo</a> odgovoriti na ovo pitanje: <strong>Koji postotak knjiga je trajno očuvan od strane shadow knjižnica?</strong> Pogledajmo neke osnovne informacije o podacima: Kada knjiga uđe u shadow knjižnicu otvorenih podataka poput <a %(wikipedia_library_genesis)s>Library Genesis</a>, a sada i <a %(wikipedia_annas_archive)s>Annine Arhive</a>, ona se zrcali diljem svijeta (putem torrenta), čime se praktički očuva zauvijek. Da bismo odgovorili na pitanje koji postotak knjiga je očuvan, trebamo znati nazivnik: koliko knjiga ukupno postoji? I idealno ne samo broj, već stvarne metadata. Tada ih možemo ne samo usporediti s shadow knjižnicama, već i <strong>stvoriti popis knjiga koje treba očuvati!</strong> Mogli bismo čak početi sanjati o crowdsourcing naporu da prođemo kroz taj popis. Izvukli smo <a %(wikipedia_isbndb_com)s>ISBNdb</a> i preuzeli <a %(openlibrary)s>Open Library dataset</a>, ali rezultati nisu bili zadovoljavajući. Glavni problem bio je što nije bilo puno preklapanja ISBN-ova. Pogledajte ovaj Vennov dijagram iz <a %(blog)s>našeg blog posta</a>: Bili smo vrlo iznenađeni koliko je malo preklapanja bilo između ISBNdb i Open Library, od kojih oba velikodušno uključuju podatke iz različitih izvora, kao što su web scrapes i knjižnične evidencije. Ako oboje dobro obavljaju posao pronalaženja većine ISBN-ova, njihovi krugovi bi sigurno imali značajno preklapanje, ili bi jedan bio podskup drugog. Pitali smo se, koliko knjiga pada <em>potpuno izvan ovih krugova</em>? Trebamo veću bazu podataka. Tada smo usmjerili pogled na najveću bazu podataka knjiga na svijetu: <a %(wikipedia_worldcat)s>WorldCat</a>. Ovo je vlasnička baza podataka neprofitne organizacije <a %(wikipedia_oclc)s>OCLC</a>, koja prikuplja metadata zapise iz knjižnica diljem svijeta, u zamjenu za davanje tim knjižnicama pristup punom datasetu i prikazivanje u rezultatima pretraživanja krajnjih korisnika. Iako je OCLC neprofitna organizacija, njihov poslovni model zahtijeva zaštitu njihove baze podataka. Pa, žao nam je reći, prijatelji iz OCLC-a, mi sve to dijelimo. :-) Tijekom protekle godine, pažljivo smo izvukli sve WorldCat zapise. U početku smo imali sreće. WorldCat je upravo uvodio potpuni redizajn svoje web stranice (u kolovozu 2022). To je uključivalo značajnu obnovu njihovih backend sustava, uvodeći mnoge sigurnosne propuste. Odmah smo iskoristili priliku i uspjeli izvući stotine milijuna (!) zapisa u samo nekoliko dana. Nakon toga, sigurnosni propusti su polako popravljani jedan po jedan, sve dok posljednji koji smo pronašli nije zakrpan prije otprilike mjesec dana. Do tada smo imali gotovo sve zapise i išli smo samo za nešto kvalitetnijim zapisima. Tako da smo osjetili da je vrijeme za objavu! 1,3 milijarde WorldCat scrape <em><strong>TL;DR:</strong> Annina Arhiva je pretražila cijeli WorldCat (najveću svjetsku zbirku knjižničnih metadata) kako bi napravila popis knjiga koje treba očuvati.</em> WorldCat Upozorenje: ovaj blog post je zastario. Odlučili smo da IPFS još nije spreman za široku upotrebu. I dalje ćemo povezivati datoteke na IPFS iz Annine Arhive kad god je to moguće, ali ih više nećemo sami hostirati, niti preporučujemo drugima da koriste IPFS za zrcaljenje. Molimo pogledajte našu stranicu Torrents ako želite pomoći u očuvanju naše kolekcije. Pomozite seedati Z-Library na IPFS-u Preuzimanje s partnerskog servera SciDB Vanjsko posuđivanje Vanjsko posuđivanje (onemogućeno ispisivanje) Vanjsko preuzimanje Istraži metapodatke Sadržano u torrentima Natrag  (+%(num)s bonus) neplaćeno plaćeno otkazano isteklo čeka se na Annu da potvrdi nevažeće Tekst u nastavku je na engleskom jeziku. Idi Poništi Naprijed Zadnje Ako vaša email adresa ne radi na Libgen forumima, preporučujemo korištenje <a %(a_mail)s>Proton Maila</a> (besplatno). Također možete <a %(a_manual)s>ručno zatražiti</a> aktivaciju vašeg računa. (možda će biti potrebna <a %(a_browser)s>provjera preglednika</a> — neograničeno preuzimanje!) Brzi partnerski poslužitelj #%(number)s (preporučeno) (malo brže, ali s listom čekanja) (nije potrebna provjera preglednika) (bez provjere preglednika ili lista čekanja) (bez liste čekanja, ali može biti vrlo sporo) Spori partnerski poslužitelj #%(number)s Audioknjiga Strip Knjiga (fikcija) Knjiga (ne-fikcija) Knjiga (nepoznato) Članci iz časopisa Časopis Glazbeni zapis Ostalo Dokument standarda Nisu sve stranice mogle biti pretvorene u PDF Označeno kao neispravno u Libgen.li Nije vidljivo u Libgen.li Nije vidljivo u Libgen.rs Fiction Nije vidljivo u Libgen.rs Non-Fiction Pokretanje exiftool-a nije uspjelo na ovoj datoteci Označeno kao “loša datoteka” u Z-Library Nedostaje u Z-Library Označeno kao “spam” u Z-Library Datoteka se ne može otvoriti (npr. oštećena datoteka, DRM) Zahtjev za autorska prava Problemi s preuzimanjem (npr. ne može se povezati, poruka o pogrešci, vrlo sporo) Netočni metapodaci (npr. naslov, opis, slika naslovnice) Ostalo Loša kvaliteta (npr. problemi s formatiranjem, loša kvaliteta skeniranja, nedostaju stranice) Spam / datoteka treba biti uklonjena (npr. oglašavanje, uvredljiv sadržaj) %(amount)s (%(amount_usd)s) %(amount)s ukupno %(amount)s (%(amount_usd)s) ukupno Briljantni Bibliofil Sretni Knjižničar Sjajni Skupljač Podataka Izvanredni Arhivist Bonus preuzimanja Cerlalc Češka metapodatci DuXiu EBSCOhost eBook Indeks Google Knjige Goodreads HathiTrust IA IA Kontrolirano digitalno posuđivanje ISBNdb ISBN GRP Libgen.li Isključujući “scimag” Libgen.rs Publicistika i Beletristika Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Ruska državna knjižnica Sci-Hub Preko Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Prijenosi na AA Z-Knjižnica Z-Library kineski Naslov, autor, DOI, ISBN, MD5, … Pretraži Autor Opis i komentari metapodataka Izdanje Izvorna datoteka Izdavač (pretraži specifično polje) Naslov Godina izdavanja Tehnički detalji Ova kovanica ima viši minimalni iznos nego inače. Molimo odaberite drugačije trajanje ili drugu kovanicu. Zahtjev nije mogao biti dovršen. Molimo pokušajte ponovno za nekoliko minuta, a ako se problem nastavi, kontaktirajte nas na %(email)s s snimkom zaslona. Došlo je do nepoznate pogreške. Molimo kontaktirajte nas na %(email)s s priloženim snimkom zaslona. Greška u obradi plaćanja. Molimo pričekajte trenutak i pokušajte ponovno. Ako problem potraje dulje od 24 sata, kontaktirajte nas na %(email)s s priloženim snimkom zaslona. Organiziramo prikupljanje sredstava za <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">sigurnosno kopiranje</a> najveće sjene knjižnice stripova na svijetu. Hvala vam na podršci! <a href="/donate">Donirajte.</a> Ako ne možete donirati, razmislite o podršci tako da kažete svojim prijateljima i pratite nas na <a href="https://www.reddit.com/r/Annas_Archive">Redditu</a> ili <a href="https://t.me/annasarchiveorg">Telegramu</a>. Nemojte nam slati e-poštu za <a %(a_request)s>zahtjev za knjige</a><br>ili male (<10k) <a %(a_upload)s>uploadove</a>. Annin Arhiv DMCA / zahtjevi za autorska prava Ostanite u kontaktu Reddit Alternative SLUM (%(unaffiliated)s) nepovezan Annina Arhiva treba vašu pomoć! Ako donirate sada, dobit ćete <strong>dvostruko</strong> više brzih preuzimanja. Mnogi nas pokušavaju srušiti, ali mi se borimo. Ako donirate ovaj mjesec, dobivate <strong>dvostruko</strong> više brzih preuzimanja. Vrijedi do kraja ovog mjeseca. Spašavanje ljudskog znanja: sjajan blagdanski poklon! Članstva će biti produžena u skladu s tim. Partnerski poslužitelji nisu dostupni zbog zatvaranja hostinga. Trebali bi uskoro ponovno biti dostupni. Kako bismo povećali otpornost Annine Arhive, tražimo volontere za pokretanje zrcala. Imamo novu metodu donacije dostupnu: %(method_name)s. Molimo razmislite o %(donate_link_open_tag)sdoniranju</a> — nije jeftino održavati ovu web stranicu, a vaša donacija zaista čini razliku. Hvala vam puno. Preporučite prijatelja i oboje dobivate %(percentage)s%% bonus brzih preuzimanja! Iznenadite voljenu osobu, darujte im račun s članstvom. Savršen poklon za Valentinovo! Saznajte više… Račun Aktivnost Napredno Annin Blog ↗ Annin Softver ↗ beta Istraživač kodova Skupovi podataka Doniraj Preuzete datoteke Česta pitanja Početna Poboljšajte metapodatke LLM podaci Prijava / Registracija Moje donacije Javni profil Pretraživanje Sigurnost Torrent datoteke Prevedi ↗ Volontiranje i Nagrade Nedavna preuzimanja: 📚&nbsp;Najveća svjetska knjižnica otvorenog koda i otvorenih podataka. ⭐️&nbsp;Zrcali Sci-Hub, Library Genesis, Z-Library i više. 📈&nbsp;%(book_any)s knjige, %(journal_article)s članci, %(book_comic)s stripovi, %(magazine)s časopisi — sačuvani zauvijek.  i  i više DuXiu Internet Archive knjižnica za posudbu Libgen 📚&nbsp;Najveća istinski otvorena knjižnica u povijesti čovječanstva. 📈&nbsp;%(book_count)s&nbsp;knjige, %(paper_count)s&nbsp;članci — sačuvani zauvijek. ⭐️&nbsp;Ogledalo smo za %(libraries)s. Mi prikupljamo i otvoreno dijelimo %(scraped)s. Sav naš kod i podaci su potpuno otvorenog koda. OpenLib Sci-Hub ,  📚 Najveća svjetska otvorena knjižnica s otvorenim podacima.<br>⭐️ Zrcalimo Scihub, Libgen, Zlib i još mnogo toga. Z-Lib Anina Arhiva Nevaljan zahtjev. Posjetite %(websites)s. Najveća svjetska otvorena knjižnica s otvorenim podacima. Ogledalo za Sci-Hub, Library Genesis, Z-Library i više. Pretraži Anninu Arhivu Anina Arhiva Molimo osvježite stranicu da pokušate ponovno. <a %(a_contact)s>Kontaktirajte nas</a> ako problem potraje nekoliko sati. 🔥 Problem s učitavanjem ove stranice <li>1. Pratite nas na <a href="https://www.reddit.com/r/Annas_Archive/">Redditu</a> ili <a href="https://t.me/annasarchiveorg">Telegramu</a>.</li><li>2. Širite riječ o Anninoj Arhivi na Twitteru, Redditu, TikToku, Instagramu, u lokalnom kafiću ili knjižnici, ili gdje god idete! Ne vjerujemo u čuvanje tajni — ako nas uklone, jednostavno ćemo se pojaviti negdje drugdje, budući da su sav naš kod i podaci potpuno otvoreni.</li><li>3. Ako ste u mogućnosti, razmislite o <a href="/donate">doniranju</a>.</li><li>4. Pomozite <a href="https://translate.annas-software.org/">prevesti</a> našu web stranicu na različite jezike.</li><li>5. Ako ste softverski inženjer, razmislite o doprinosu našem <a href="https://annas-software.org/">otvorenom kodu</a> ili seedanju naših <a href="/datasets">torrenta</a>.</li> 10. Kreirajte ili pomozite održavati Wikipedijsku stranicu za Anninu Arhivu na vašem jeziku. 11. Tražimo mogućnost postavljanja malih, ukusnih oglasa. Ako želite oglašavati na Anninoj Arhivi, molimo vas da nas obavijestite. 6. Ako ste istraživač sigurnosti, možemo koristiti vaše vještine i za napad i za obranu. Pogledajte našu <a %(a_security)s>Sigurnosnu</a> stranicu. 7. Tražimo stručnjake za plaćanja za anonimne trgovce. Možete li nam pomoći dodati prikladnije načine za doniranje? PayPal, WeChat, poklon kartice. Ako poznajete nekoga, molimo vas da nas kontaktirate. 8. Uvijek tražimo više kapaciteta za poslužitelje. 9. Možete pomoći prijavljivanjem problema s datotekama, ostavljanjem komentara i stvaranjem popisa izravno na ovoj web stranici. Također možete pomoći <a %(a_upload)s>učitavanjem više knjiga</a> ili popravljanjem problema s datotekama ili formatiranjem postojećih knjiga. Za opsežnije informacije o volontiranju, pogledajte našu stranicu <a %(a_volunteering)s>Volontiranje i Nagrade</a>. Snažno vjerujemo u slobodan protok informacija i očuvanje znanja i kulture. Ovim pretraživačem gradimo na ramenima divova. Duboko poštujemo težak rad ljudi koji su stvorili razne shadow knjižnice i nadamo se da će ovaj pretraživač proširiti njihov doseg. Kako biste ostali informirani o našem napretku, pratite Annu na <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ili <a href="https://t.me/annasarchiveorg">Telegram</a>. Za pitanja i povratne informacije kontaktirajte Annu na %(email)s. ID računa: %(account_id)s Odjava ❌ Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. ✅ Sada ste odjavljeni. Ponovno učitajte stranicu za prijavu. Iskorištena brza preuzimanja (posljednja 24 sata): <strong>%(used)s / %(total)s</strong> Članstvo: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(produži)</a> Možete kombinirati više članstava (brza preuzimanja po 24 sata će se zbrajati). Članstvo: <strong>Nema</strong> <a %(a_become)s>(postanite član)</a> Kontaktirajte Annu na %(email)s ako ste zainteresirani za nadogradnju članstva na višu razinu. Javni profil: %(profile_link)s Tajni ključ (ne dijelite!): %(secret_key)s prikaži Pridružite nam se ovdje! Nadogradite na <a %(a_tier)s>viši nivo</a> kako biste se pridružili našoj grupi. Ekskluzivna Telegram grupa: %(link)s Račun koja preuzimanja? Prijava Nemojte izgubiti svoj ključ! Nevažeći tajni ključ. Provjerite svoj ključ i pokušajte ponovno, ili alternativno registrirajte novi račun dolje. Tajni ključ Unesite svoj tajni ključ za prijavu: Stari račun temeljen na e-pošti? Unesite svoju <a %(a_open)s>e-poštu ovdje</a>. Registrirajte novi račun Još nemate račun? Registracija uspješna! Vaš tajni ključ je: <span %(span_key)s>%(key)s</span> Pažljivo spremite ovaj ključ. Ako ga izgubite, izgubit ćete pristup svom računu. <li %(li_item)s><strong>Označi.</strong> Možete označiti ovu stranicu kako biste dohvatili svoj ključ.</li><li %(li_item)s><strong>Preuzmi.</strong> Kliknite <a %(a_download)s>ovu poveznicu</a> za preuzimanje vašeg ključa.</li><li %(li_item)s><strong>Upravitelj lozinki.</strong> Koristite upravitelj lozinki za spremanje ključa kada ga unesete dolje.</li> Prijava / Registracija Provjera preglednika Upozorenje: kod sadrži netočne Unicode znakove i može se ponašati neispravno u raznim situacijama. Sirovi binarni podaci mogu se dekodirati iz base64 prikaza u URL-u. Opis Oznaka Prefiks URL za specifičan kod Web stranica Kodovi koji počinju s “%(prefix_label)s” Molimo nemojte strugati ove stranice. Umjesto toga preporučujemo <a %(a_import)s>generiranje</a> ili <a %(a_download)s>preuzimanje</a> naših ElasticSearch i MariaDB baza podataka te pokretanje našeg <a %(a_software)s>open source koda</a>. Sirovi podaci mogu se ručno istražiti kroz JSON datoteke kao što je <a %(a_json_file)s>ova</a>. Manje od %(count)s datoteka ili skupova datoteka u arhiviranoj bazi podataka Opći URL Istraživač kodova Indeks Istražite kodove s kojima su datoteke označene, prema prefiksu. Stupac “datoteke” prikazuje broj datoteka označenih kodovima s danim prefiksom, kako je vidljivo u tražilici (uključujući samo metapodatkovne datoteke). Stupac “kodovi” prikazuje koliko stvarnih kodova ima dani prefiks. Poznati kodni prefiks “%(key)s” Više… Prefiks %(count)s datoteka koja odgovara “%(prefix_label)s” %(count)s datoteka koje odgovaraju “%(prefix_label)s” %(count)s datoteka koje odgovaraju “%(prefix_label)s” kodovi datoteke ili skupovi datoteka u arhiviranoj bazi podataka “%%” će biti zamijenjen vrijednošću koda Pretraži Anninu Arhivu Kodovi URL za specifičan kod: “%(url)s” Ova stranica može potrajati neko vrijeme za generiranje, zbog čega zahtijeva Cloudflare captcha. <a %(a_donate)s>Članovi</a> mogu preskočiti captcha. Zlostavljanje prijavljeno: Bolja verzija Želite li prijaviti ovog korisnika zbog zlostavljanja ili neprimjerenog ponašanja? Problem s datotekom: %(file_issue)s skriveni komentar Odgovori Prijavi zlostavljanje Prijavili ste ovog korisnika zbog zlostavljanja. Zahtjevi za autorska prava poslani na ovu e-mail adresu bit će ignorirani; umjesto toga koristite obrazac. Prikaži e-mail Vrlo rado primamo vaše povratne informacije i pitanja! Međutim, zbog količine neželjene pošte i besmislenih e-mailova koje primamo, molimo označite kućice kako biste potvrdili da razumijete ove uvjete za kontaktiranje. Bilo koji drugi načini kontaktiranja nas u vezi s autorskim pravima bit će automatski izbrisani. Za DMCA / prijave kršenja autorskih prava, koristite <a %(a_copyright)s>ovaj obrazac</a>. Kontakt e-pošta URL-ovi na Anninoj Arhivi (obavezno). Jedan po liniji. Molimo uključite samo URL-ove koji opisuju točno isto izdanje knjige. Ako želite podnijeti zahtjev za više knjiga ili više izdanja, molimo podnesite ovaj obrazac više puta. Prijave koje spajaju više knjiga ili izdanja zajedno bit će odbijene. Adresa (obavezno) Jasan opis izvornog materijala (obavezno) E-mail (obavezno) URL-ovi izvornog materijala, jedan po liniji (obavezno). Molimo uključite što više možete, kako bismo mogli potvrditi vaš zahtjev (npr. Amazon, WorldCat, Google Books, DOI). ISBN-ovi izvornog materijala (ako je primjenjivo). Jedan po liniji. Molimo uključite samo one koji točno odgovaraju izdanju za koje prijavljujete zahtjev za autorska prava. Vaše ime (obavezno) ❌ Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. ✅ Hvala vam na podnošenju prijave za kršenje autorskih prava. Pregledat ćemo je što je prije moguće. Molimo ponovno učitajte stranicu da biste podnijeli novu. <a %(a_openlib)s>Open Library</a> URL-ovi izvornog materijala, jedan po liniji. Molimo odvojite trenutak da pretražite Open Library za svoj izvorni materijal. To će nam pomoći da potvrdimo vaš zahtjev. Broj telefona (obavezno) Izjava i potpis (obavezno) Podnesite zahtjev Ako imate DCMA ili drugi zahtjev za autorska prava, molimo ispunite ovaj obrazac što preciznije. Ako naiđete na bilo kakve probleme, kontaktirajte nas na našu posvećenu DMCA adresu: %(email)s. Imajte na umu da zahtjevi poslani e-poštom na ovu adresu neće biti obrađeni, ona je samo za pitanja. Molimo koristite obrazac ispod za podnošenje svojih zahtjeva. DMCA / Obrazac za prijavu kršenja autorskih prava Primjer datoteke ili skupa datoteka u arhiviranoj bazi podataka na Anninoj Arhivi Torrenti od Annine Arhive Format kontejnera Anine Arhive Skripte za uvoz metapodataka Ako ste zainteresirani za zrcaljenje ovog skupa podataka za <a %(a_archival)s>arhivske</a> ili <a %(a_llm)s>LLM trening</a> svrhe, molimo kontaktirajte nas. Zadnje ažuriranje: %(date)s Glavna %(source)s web stranica Dokumentacija metapodataka (većina polja) Datoteke zrcaljene od Anine Arhive: %(count)s (%(percent)s%%) Resursi Ukupno datoteka: %(count)s Ukupna veličina datoteke: %(size)s Naš blog post o ovim podacima <a %(duxiu_link)s>Duxiu</a> je ogromna baza podataka skeniranih knjiga, koju je stvorila <a %(superstar_link)s>SuperStar Digital Library Group</a>. Većina su akademske knjige, skenirane kako bi bile dostupne digitalno sveučilištima i knjižnicama. Za našu publiku koja govori engleski, <a %(princeton_link)s>Princeton</a> i <a %(uw_link)s>University of Washington</a> imaju dobre preglede. Također postoji izvrstan članak koji daje više pozadine: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Knjige iz Duxiua dugo su piratizirane na kineskom internetu. Obično ih preprodavači prodaju za manje od dolara. Obično se distribuiraju koristeći kineski ekvivalent Google Drivea, koji je često hakiran kako bi omogućio više prostora za pohranu. Neki tehnički detalji mogu se pronaći <a %(link1)s>ovdje</a> i <a %(link2)s>ovdje</a>. Iako su knjige bile polujavno distribuirane, prilično ih je teško dobiti u velikim količinama. Imali smo to visoko na našem popisu zadataka, i dodijelili smo više mjeseci punog radnog vremena za to. Međutim, krajem 2023. nevjerojatan, izvanredan i talentiran volonter stupio je u kontakt s nama, rekavši da su već obavili sav taj posao — uz velike troškove. Podijelili su s nama cijelu zbirku, ne očekujući ništa zauzvrat, osim jamstva dugoročnog očuvanja. Zaista izvanredno. Više informacija od naših volontera (sirove bilješke): Prilagođeno iz našeg <a %(a_href)s>blog posta</a>. DuXiu 读秀 %(count)s datoteka %(count)s datoteke %(count)s datoteke Ovaj skup podataka usko je povezan s <a %(a_datasets_openlib)s>Open Library datasetom</a>. Sadrži struganje svih metapodataka i velikog dijela datoteka iz IA-ove Kontrolirane digitalne knjižnice. Ažuriranja se objavljuju u <a %(a_aac)s>formatu kontejnera Anine Arhive</a>. Ovi zapisi se izravno preuzimaju iz Open Library skupa podataka, ali također sadrže zapise koji nisu u Open Library. Također imamo brojne datoteke s podacima koje su članovi zajednice prikupili tijekom godina. Zbirka se sastoji od dva dijela. Potrebna su oba dijela za dobivanje svih podataka (osim zamijenjenih torrenta, koji su prekriženi na stranici torrenta). Digitalna knjižnica za posudbu naše prvo izdanje, prije nego što smo standardizirali na <a %(a_aac)s>Anina Arhiva Kontejneri (AAC) format</a>. Sadrži metapodatke (kao json i xml), pdf-ove (iz acsm i lcpdf sustava digitalnog posuđivanja) i sličice naslovnica. inkrementalna nova izdanja, koristeći AAC. Sadrži samo metapodatke s vremenskim oznakama nakon 2023-01-01, budući da je ostatak već pokriven s "ia". Također svi pdf-ovi, ovaj put iz acsm i "bookreader" (IA-ov web čitač) sustava posudbe. Unatoč tome što naziv nije potpuno točan, i dalje stavljamo bookreader datoteke u zbirku ia2_acsmpdf_files, budući da su međusobno isključive. IA Kontrolirano digitalno posuđivanje 98%%+ datoteka je pretraživo. Naša misija je arhivirati sve knjige na svijetu (kao i radove, časopise itd.) i učiniti ih široko dostupnima. Vjerujemo da sve knjige trebaju biti zrcaljene daleko i široko, kako bi se osigurala redundancija i otpornost. Zato okupljamo datoteke iz raznih izvora. Neki izvori su potpuno otvoreni i mogu se zrcaliti u velikim količinama (kao što je Sci-Hub). Drugi su zatvoreni i zaštitnički nastrojeni, pa ih pokušavamo strugati kako bismo "oslobodili" njihove knjige. Drugi pak spadaju negdje između. Svi naši podaci mogu se <a %(a_torrents)s>preuzeti putem torrenta</a>, a svi naši metapodaci mogu se <a %(a_anna_software)s>generirati</a> ili <a %(a_elasticsearch)s>preuzeti</a> kao ElasticSearch i MariaDB baze podataka. Sirovi podaci mogu se ručno istraživati kroz JSON datoteke kao što je <a %(a_dbrecord)s>ova</a>. Metapodaci ISBN web stranica Zadnje ažuriranje: %(isbn_country_date)s (%(link)s) Resursi Međunarodna ISBN agencija redovito objavljuje raspon koji je dodijelila nacionalnim ISBN agencijama. Iz toga možemo zaključiti kojoj zemlji, regiji ili jezičnoj skupini pripada ovaj ISBN. Trenutno koristimo te podatke neizravno, putem <a %(a_isbnlib)s>isbnlib</a> Python biblioteke. Informacije o zemlji za ISBN Ovo je ispis mnogih poziva na isbndb.com tijekom rujna 2022. Pokušali smo pokriti sve ISBN raspon. To je oko 30,9 milijuna zapisa. Na njihovoj web stranici tvrde da zapravo imaju 32,6 milijuna zapisa, pa smo možda nekako propustili neke, ili <em>oni</em> možda rade nešto pogrešno. JSON odgovori su uglavnom sirovi s njihovog servera. Jedan problem s kvalitetom podataka koji smo primijetili je da za ISBN-13 brojeve koji počinju s različitim prefiksom od “978-”, i dalje uključuju polje “isbn” koje jednostavno predstavlja ISBN-13 broj s prva 3 broja odsječena (i kontrolna znamenka preračunata). Ovo je očito pogrešno, ali čini se da oni tako rade, pa to nismo mijenjali. Još jedan potencijalni problem na koji možete naići je činjenica da polje “isbn13” ima duplikate, pa ga ne možete koristiti kao primarni ključ u bazi podataka. Kombinacija polja “isbn13”+“isbn” čini se jedinstvenom. Izdanje 1 (2022-10-31) Torrenti fikcije su zaostali (iako ID-ovi ~4-6M nisu torrentirani jer se preklapaju s našim Zlib torrentima). Naš blog post o izdanju stripova Torrenti stripova na Anninoj arhivi Za pozadinu različitih forkova Library Genesis, pogledajte stranicu za <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li sadrži većinu istog sadržaja i metapodataka kao Libgen.rs, ali ima neke zbirke povrh toga, naime stripove, časopise i standardne dokumente. Također je integrirao <a %(a_scihub)s>Sci-Hub</a> u svoje metapodatke i pretraživač, što koristimo za našu bazu podataka. Metapodaci za ovu knjižnicu su slobodno dostupni <a %(a_libgen_li)s>na libgen.li</a>. Međutim, ovaj server je spor i ne podržava nastavak prekinutih veza. Iste datoteke su također dostupne na <a %(a_ftp)s>FTP serveru</a>, koji radi bolje. Čini se da se i publicistika odvojila, ali bez novih torrenata. Izgleda da se to dogodilo od početka 2022., iako to nismo provjerili. Prema administratoru Libgen.li, zbirka “fiction_rus” (ruska fikcija) trebala bi biti pokrivena redovito objavljivanim torrentima s <a %(a_booktracker)s>booktracker.org</a>, posebno torrentima <a %(a_flibusta)s>flibusta</a> i <a %(a_librusec)s>lib.rus.ec</a> (koje zrcalimo <a %(a_torrents)s>ovdje</a>, iako još nismo utvrdili koji torrenti odgovaraju kojim datotekama). Zbirka fikcije ima svoje torrente (odvojeno od <a %(a_href)s>Libgen.rs</a>) počevši od %(start)s. Određeni rasponi bez torrenta (kao što su rasponi fikcije f_3463000 do f_4260000) vjerojatno su Z-Library (ili druge duplicirane) datoteke, iako bismo možda trebali napraviti deduplikaciju i izraditi torrente za lgli-jedinstvene datoteke u tim rasponima. Statistike za sve zbirke mogu se pronaći <a %(a_href)s>na web stranici libgena</a>. Torrenti su dostupni za većinu dodatnog sadržaja, a posebno su torrenti za stripove, časopise i standardne dokumente objavljeni u suradnji s Anninom Arhivom. Napominjemo da su torrent datoteke koje se odnose na “libgen.is” izričito zrcalne kopije <a %(a_libgen)s>Libgen.rs</a> (“.is” je druga domena koju koristi Libgen.rs). Korisni resurs za korištenje metapodataka je <a %(a_href)s>ova stranica</a>. %(icon)s Njihova zbirka “fiction_rus” (ruska fikcija) nema posvećene torrente, ali je pokrivena torrentima drugih, a mi držimo <a %(fiction_rus)s>zrcalo</a>. Ruski fikcijski torrenti na Anninoj Arhivi Torrenti fikcije na Anninoj Arhivi Forum za raspravu Metapodaci Metapodaci putem FTP-a Torrenti časopisa na Aninoj Arhivi Informacije o poljima metapodataka Zrcaljenje drugih torrenta (i jedinstveni torrenti fikcije i stripova) Standardni dokumenti torrenti na Anninoj Arhivi Libgen.li Torrenti prema Anninoj Arhivi (naslovnice knjiga) Library Genesis je poznat po tome što već velikodušno omogućava masovno preuzimanje svojih podataka putem torrenta. Naša Libgen zbirka sastoji se od pomoćnih podataka koje oni ne objavljuju direktno, u suradnji s njima. Veliko hvala svima uključenima u Library Genesis na suradnji s nama! Naš blog o izdanju naslovnica knjiga Ova stranica se odnosi na verziju “.rs”. Poznata je po dosljednom objavljivanju i svojih metapodataka i cjelokupnog sadržaja svog kataloga knjiga. Njena zbirka knjiga podijeljena je na beletristički i nebeletristički dio. Korisni resurs za korištenje metapodataka je <a %(a_metadata)s>ova stranica</a> (blokira IP rasponove, možda će biti potreban VPN). Od 2024-03, novi torrenti se objavljuju u <a %(a_href)s>ovoj forumskoj temi</a> (blokira IP raspon, možda će biti potreban VPN). Fikcijski torrenti na Anninoj Arhivi Libgen.rs fikcijski torrenti Libgen.rs forum za raspravu Libgen.rs Metapodaci Informacije o poljima metapodataka na Libgen.rs Libgen.rs nefikcijski torrenti Torrenti nefikcijskih knjiga na Anninoj Arhivi %(example)s za beletrističku knjigu. Ovo <a %(blog_post)s>prvo izdanje</a> je prilično malo: oko 300GB naslovnica knjiga iz Libgen.rs forka, i beletristike i publicistike. Organizirane su na isti način kao što se pojavljuju na libgen.rs, npr.: %(example)s za knjigu iz područja publicistike. Kao i s kolekcijom Z-Library, sve smo ih stavili u veliku .tar datoteku, koju možete montirati koristeći <a %(a_ratarmount)s>ratarmount</a> ako želite izravno poslužiti datoteke. Izdanja 1 (%(date)s) Brza priča o različitim Library Genesis (ili “Libgen”) forkovima je da su se s vremenom različiti ljudi uključeni u Library Genesis posvađali i krenuli svojim putem. Prema ovom <a %(a_mhut)s>postu na forumu</a>, Libgen.li je izvorno bio hostiran na “http://free-books.dontexist.com”. “.fun” verziju stvorio je izvorni osnivač. Obnavlja se u korist nove, više distribuirane verzije. <a %(a_li)s>Verzija “.li”</a> ima ogromnu zbirku stripova, kao i drugog sadržaja, koji još nije dostupan za masovno preuzimanje putem torrenta. Ima zasebnu torrent zbirku beletristike, a u svojoj bazi podataka sadrži i metapodatke <a %(a_scihub)s>Sci-Hub-a</a>. “.rs” verzija ima vrlo slične podatke i najdosljednije objavljuje svoju zbirku u masovnim torrentima. Otprilike je podijeljena na dio "fikcija" i "ne-fikcija". Izvorno na “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> je na neki način također ogranak Library Genesis-a, iako su za svoj projekt koristili drugačije ime. Libgen.rs Također obogaćujemo našu zbirku izvorima koji sadrže samo metapodatke, koje možemo povezati s datotekama, npr. koristeći ISBN brojeve ili druga polja. Ispod je pregled tih izvora. Opet, neki od tih izvora su potpuno otvoreni, dok za druge moramo koristiti scraping. Napominjemo da u pretraživanju metapodataka prikazujemo originalne zapise. Ne spajamo zapise. Izvori samo za metapodatke Open Library je projekt otvorenog koda od strane Internet Archivea za katalogizaciju svake knjige na svijetu. Ima jednu od najvećih operacija skeniranja knjiga na svijetu i mnoge knjige dostupne za digitalno posuđivanje. Njegov katalog metapodataka knjiga slobodno je dostupan za preuzimanje i uključen je u Anninu Arhivu (iako trenutno nije u pretraživanju, osim ako izričito ne tražite Open Library ID). Open Library Isključujući duplikate Zadnje ažurirano Postotci broja datoteka %% zrcaljeno od strane AA / dostupno putem torrenta Veličina Izvor Ispod je kratak pregled izvora datoteka na Aninoj Arhivi. Budući da sjene knjižnice često sinkroniziraju podatke jedna s drugom, postoji značajno preklapanje između knjižnica. Zato se brojevi ne zbrajaju do ukupnog iznosa. Postotak "zrcaljeno i posijano od strane Anine Arhive" pokazuje koliko datoteka sami zrcalimo. Te datoteke masovno posijavamo putem torrenta i činimo ih dostupnima za izravno preuzimanje putem partnerskih web stranica. Pregled Ukupno Torrenti na Anninoj Arhivi Za pozadinske informacije o Sci-Hub-u, molimo pogledajte njegovu <a %(a_scihub)s>službenu web stranicu</a>, <a %(a_wikipedia)s>Wikipedia stranicu</a> i ovaj <a %(a_radiolab)s>podcast intervju</a>. Napominjemo da je Sci-Hub <a %(a_reddit)s>zamrznut od 2021.</a> godine. Bio je zamrznut i ranije, ali je 2021. dodano nekoliko milijuna radova. Ipak, ograničen broj radova se dodaje u Libgen “scimag” zbirke, ali ne dovoljno da bi se opravdali novi masovni torrenti. Koristimo metapodatke Sci-Hub-a koje pruža <a %(a_libgen_li)s>Libgen.li</a> u svojoj “scimag” zbirci. Također koristimo dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Napominjemo da su “smarch” torrent datoteke <a %(a_smarch)s>zastarjele</a> i stoga nisu uključene u naš popis torrenta. Torrenti na Libgen.li Torrenti na Libgen.rs Metapodaci i torrenti Ažuriranja na Redditu Podcast intervju Wikipedia stranica Sci-Hub Sci-Hub: zamrznut od 2021.; većina dostupna putem torrenta Libgen.li: manji dodaci od tada</div> Neke izvorne knjižnice promoviraju masovno dijeljenje svojih podataka putem torrenta, dok druge ne dijele lako svoju zbirku. U potonjem slučaju, Annina Arhiva pokušava strugati njihove zbirke i učiniti ih dostupnima (pogledajte našu <a %(a_torrents)s>Torrents</a> stranicu). Postoje i situacije između, na primjer, gdje su izvorne knjižnice voljne dijeliti, ali nemaju resurse za to. U tim slučajevima također pokušavamo pomoći. Ispod je pregled kako komuniciramo s različitim izvorima knjižnica. Izvorne knjižnice %(icon)s Razne baze podataka raspršene po kineskom internetu; iako su često plaćene baze podataka %(icon)s Većina datoteka dostupna je samo putem premium BaiduYun računa; spore brzine preuzimanja. %(icon)s Annina Arhiva upravlja zbirkom <a %(duxiu)s>DuXiu datoteka</a> %(icon)s Razne baze metapodataka raspršene po kineskom internetu; često su to plaćene baze podataka %(icon)s Nema lako dostupnih ispisa metapodataka za cijelu njihovu kolekciju. %(icon)s Annina Arhiva upravlja kolekcijom <a %(duxiu)s>DuXiu metapodataka</a> Datoteke %(icon)s Datoteke dostupne samo za posudbu u ograničenom opsegu, s raznim ograničenjima pristupa %(icon)s Annina Arhiva upravlja kolekcijom <a %(ia)s>IA datoteka</a> %(icon)s Neki metapodaci dostupni putem <a %(openlib)s>Open Library ispisa baze podataka</a>, ali ne pokrivaju cijelu IA kolekciju %(icon)s Nema lako dostupnih ispisa metapodataka za cijelu njihovu kolekciju %(icon)s Annina Arhiva upravlja kolekcijom <a %(ia)s>IA metapodataka</a> Zadnje ažurirano %(icon)s Annina Arhiva i Libgen.li zajednički upravljaju zbirkama <a %(comics)s>stripova</a>, <a %(magazines)s>časopisa</a>, <a %(standarts)s>standardnih dokumenata</a> i <a %(fiction)s>fikcije (odvojeno od Libgen.rs)</a>. %(icon)s Non-Fiction torrenti se dijele s Libgen.rs (i zrcale <a %(libgenli)s>ovdje</a>). %(icon)s Tromjesečni <a %(dbdumps)s>HTTP ispisi baze podataka</a> %(icon)s Automatizirani torrenti za <a %(nonfiction)s>Nefikciju</a> i <a %(fiction)s>Fikciju</a> %(icon)s Anina Arhiva upravlja zbirkom <a %(covers)s>torrenta naslovnica knjiga</a> %(icon)s Dnevni <a %(dbdumps)s>HTTP ispisi baze podataka</a> Metapodaci %(icon)s Mjesečni <a %(dbdumps)s>izvodi baze podataka</a> %(icon)s Podaci torrenti dostupni <a %(scihub1)s>ovdje</a>, <a %(scihub2)s>ovdje</a> i <a %(libgenli)s>ovdje</a> %(icon)s Neke nove datoteke <a %(libgenrs)s>se</a> <a %(libgenli)s>dodaju</a> na Libgen’s “scimag”, ali nedovoljno da bi trebale nove torrente %(icon)s Sci-Hub je zamrznuo nove datoteke od 2021. %(icon)s Metapodaci dostupni <a %(scihub1)s>ovdje</a> i <a %(scihub2)s>ovdje</a>, kao i kao dio <a %(libgenli)s>Libgen.li baze podataka</a> (koju koristimo) Izvor %(icon)s Razni manji ili jednokratni izvori. Potičemo ljude da prvo prenesu na druge sjene knjižnice, ali ponekad ljudi imaju zbirke koje su prevelike da bi ih drugi mogli pregledati, iako nisu dovoljno velike da bi zaslužile vlastitu kategoriju. %(icon)s Nije dostupno izravno u velikim količinama, zaštićeno od scrapinga %(icon)s Annina arhiva upravlja zbirkom <a %(worldcat)s>OCLC (WorldCat) metapodataka</a> %(icon)s Annina Arhiva i Z-Library zajednički upravljaju kolekcijom <a %(metadata)s>Z-Library metapodataka</a> i <a %(files)s>Z-Library datoteka</a> Skupovi podataka Kombiniramo sve gore navedene izvore u jednu objedinjenu bazu podataka koju koristimo za posluživanje ove web stranice. Ova objedinjena baza podataka nije dostupna izravno, ali budući da je Annina Arhiva potpuno otvorenog koda, može se prilično lako <a %(a_generated)s>generirati</a> ili <a %(a_downloaded)s>preuzeti</a> kao ElasticSearch i MariaDB baze podataka. Skripte na toj stranici automatski će preuzeti sve potrebne metapodatke iz gore navedenih izvora. Ako želite istražiti naše podatke prije nego što pokrenete te skripte lokalno, možete pogledati naše JSON datoteke, koje dalje povezuju na druge JSON datoteke. <a %(a_json)s>Ova datoteka</a> je dobar početak. Ujedinjena baza podataka Torrenti od Annine Arhive pregledaj pretraži Razni manji ili jednokratni izvori. Potičemo ljude da prvo prenesu u druge sjene knjižnice, ali ponekad ljudi imaju zbirke koje su prevelike da bi ih drugi pregledali, iako nisu dovoljno velike da bi opravdale vlastitu kategoriju. Pregled sa <a %(a1)s>stranice datasets</a>. Iz <a %(a_href)s>aaaaarg.fail</a>. Čini se prilično potpunim. Od našeg volontera “cgiym”. Iz <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrenta. Ima prilično velik preklapanje s postojećim zbirkama radova, ali vrlo malo MD5 podudaranja, pa smo odlučili zadržati ga u potpunosti. Scrape <q>iRead eBooks</q> (= fonetski <q>ai rit i-books</q>; airitibooks.com), od strane volontera <q>j</q>. Odgovara <q>airitibooks</q> metadata u <a %(a1)s><q>Ostali metadata scrapes</q></a>. Iz zbirke <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Djelomično iz originalnog izvora, djelomično s the-eye.eu, djelomično s drugih ogledala. S privatne torrent stranice za knjige, <a %(a_href)s>Bibliotik</a> (često se naziva “Bib”), čije su knjige grupirane u torrente po imenu (A.torrent, B.torrent) i distribuirane putem the-eye.eu. Od našeg volontera “bpb9v”. Za više informacija o <a %(a_href)s>CADAL</a>, pogledajte bilješke na našoj <a %(a_duxiu)s>DuXiu dataset stranici</a>. Više od našeg volontera “bpb9v”, uglavnom DuXiu datoteke, kao i mapa “WenQu” i “SuperStar_Journals” (SuperStar je tvrtka iza DuXiu). Iz naše volonterke “cgiym”, kineski tekstovi iz raznih izvora (predstavljeni kao poddirektoriji), uključujući iz <a %(a_href)s>China Machine Press</a> (veliki kineski izdavač). Nekineske zbirke (predstavljene kao poddirektoriji) od naše volonterke “cgiym”. Scrape knjiga o kineskoj arhitekturi, od strane volontera <q>cm</q>: <q>Dobio sam ih iskorištavanjem mrežne ranjivosti u izdavačkoj kući, ali ta je rupa u međuvremenu zatvorena</q>. Odgovara <q>chinese_architecture</q> metadata u <a %(a1)s><q>Ostali metadata scrapes</q></a>. Knjige iz akademske izdavačke kuće <a %(a_href)s>De Gruyter</a>, prikupljene iz nekoliko velikih torrenta. Scrape sa <a %(a_href)s>docer.pl</a>, poljske web stranice za dijeljenje datoteka fokusirane na knjige i druge pisane radove. Scrape je obavljen krajem 2023. od strane volontera “p”. Nemamo dobre metapodatke s originalne web stranice (čak ni ekstenzije datoteka), ali smo filtrirali datoteke nalik knjigama i često uspjeli izvući metapodatke iz samih datoteka. DuXiu epubovi, izravno iz DuXiu, prikupljeni od volontera “w”. Samo su nedavne DuXiu knjige dostupne izravno putem e-knjiga, pa većina ovih mora biti nedavna. Preostale DuXiu datoteke od volontera “m”, koje nisu bile u DuXiu vlasničkom PDG formatu (glavni <a %(a_href)s>DuXiu dataset</a>). Prikupljene iz mnogih izvornih izvora, nažalost bez očuvanja tih izvora u putanji datoteke. <span></span> <span></span> <span></span> Scrape erotskih knjiga, od strane volontera <q>do no harm</q>. Odgovara <q>hentai</q> metadata u <a %(a1)s><q>Ostali metadata scrapes</q></a>. <span></span> <span></span> Zbirka prikupljena od japanskog izdavača mange od strane volontera “t”. <a %(a_href)s>Odabrani sudski arhivi Longquana</a>, osigurani od strane volontera “c”. Struganje <a %(a_href)s>magzdb.org</a>, saveznika Library Genesis (povezano je na početnoj stranici libgen.rs) ali koji nisu htjeli izravno pružiti svoje datoteke. Dobio volonter “p” krajem 2023. <span></span> Razni mali prijenosi, premali za vlastitu podzbirku, ali predstavljeni kao direktoriji. E-knjige s AvaxHome, ruske web stranice za dijeljenje datoteka. Arhiva novina i časopisa. Odgovara <q>newsarch_magz</q> metadata u <a %(a1)s><q>Ostali metadata scrapes</q></a>. Scrape <a %(a1)s>Philosophy Documentation Center</a>. Zbirka volontera “o” koji je prikupio poljske knjige direktno s originalnih izdanja (“scene”) web stranica. Kombinirane zbirke <a %(a_href)s>shuge.org</a> od strane volontera “cgiym” i “woz9ts”. <span></span> <a %(a_href)s>“Imperijalna knjižnica Trantora”</a> (nazvana po izmišljenoj knjižnici), prikupljena 2022. od strane volontera “t”. <span></span> <span></span> <span></span> Pod-pod-zbirke (predstavljene kao direktoriji) od volontera “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (od <a %(a_sikuquanshu)s>Dizhi(迪志)</a> na Tajvanu), mebook (mebook.cc, 我的小书屋, moja mala knjižara — woz9ts: “Ova stranica se uglavnom fokusira na dijeljenje visokokvalitetnih e-knjiga, od kojih su neke postavljene od strane vlasnika. Vlasnik je <a %(a_arrested)s>uhapšen</a> 2019. i netko je napravio zbirku datoteka koje je dijelio.”). Preostale DuXiu datoteke od volontera “woz9ts”, koje nisu bile u DuXiu vlasničkom PDG formatu (još uvijek za pretvorbu u PDF). Kolekcija “upload” podijeljena je u manje podkolekcije, koje su označene u AACID-ovima i imenima torrenta. Sve podkolekcije su prvo deduplicirane u odnosu na glavnu kolekciju, iako metapodaci “upload_records” JSON datoteke još uvijek sadrže mnogo referenci na izvorne datoteke. Datoteke koje nisu knjige također su uklonjene iz većine podkolekcija, i obično <em>nisu</em> zabilježene u “upload_records” JSON-u. Podzbirke su: Bilješke Podzbirka Mnoge podkolekcije same po sebi sastoje se od pod-pod-kolekcija (npr. iz različitih izvora), koje su predstavljene kao direktoriji u poljima “filepath”. Prijenosi na Anninu Arhivu Naš blog post o ovim podacima <a %(a_worldcat)s>WorldCat</a> je vlasnička baza podataka neprofitne organizacije <a %(a_oclc)s>OCLC</a>, koja agregira metapodatke iz knjižnica širom svijeta. Vjerojatno je najveća zbirka knjižničnih metapodataka na svijetu. U listopadu 2023. smo <a %(a_scrape)s>objavili</a> sveobuhvatno struganje baze podataka OCLC (WorldCat), u formatu <a %(a_aac)s>Anninih Arhivskih Kontejnera</a>. Listopad 2023., početno izdanje: OCLC (WorldCat) Torrenti prema Anninoj Arhivi Primjer datoteke u Anninoj Arhivi (izvorna zbirka) Primjer datoteke u Anninoj Arhivi (zbirka “zlib3”) Torrent datoteke od Annine Arhive (metapodaci + sadržaj) Objava na blogu o Izdanju 1 Objava na blogu o Izdanju 2 Krajem 2022. godine, navodni osnivači Z-Libraryja su uhićeni, a domene su zaplijenile vlasti Sjedinjenih Američkih Država. Od tada se web stranica polako vraća na mrežu. Trenutno nije poznato tko je vodi. Ažuriranje od veljače 2023. Z-Library ima svoje korijene u zajednici <a %(a_href)s>Library Genesis</a> i izvorno je pokrenut s njihovim podacima. Od tada se znatno profesionalizirao i ima mnogo modernije sučelje. Stoga su u mogućnosti dobiti mnogo više donacija, kako financijskih za daljnje poboljšanje svoje web stranice, tako i donacija novih knjiga. Prikupili su veliku zbirku uz Library Genesis. Zbirka se sastoji od tri dijela. Izvorne stranice opisa za prva dva dijela sačuvane su u nastavku. Potrebna su vam sva tri dijela kako biste dobili sve podatke (osim zastarjelih torrenta, koji su prekriženi na stranici torrenta). %(title)s: naše prvo izdanje. Ovo je bilo prvo izdanje onoga što se tada zvalo “Pirate Library Mirror” (“pilimi”). %(title)s: drugo izdanje, ovaj put sa svim datotekama umotanim u .tar datoteke. %(title)s: inkrementalna nova izdanja, koristeći <a %(a_href)s>format Anna’s Archive Containers (AAC)</a>, sada objavljena u suradnji s timom Z-Libraryja. Početno zrcalo je mukotrpno dobiveno tijekom 2021. i 2022. godine. U ovom trenutku je malo zastarjelo: odražava stanje zbirke u lipnju 2021. Ažurirat ćemo ovo u budućnosti. Trenutno smo fokusirani na objavljivanje ovog prvog izdanja. Budući da je Library Genesis već sačuvan s javnim torrentima i uključen u Z-Library, napravili smo osnovnu deduplikaciju protiv Library Genesis u lipnju 2022. Za to smo koristili MD5 hashove. Vjerojatno postoji mnogo više dupliciranog sadržaja u knjižnici, poput više formata datoteka iste knjige. Ovo je teško točno otkriti, pa ne pokušavamo. Nakon deduplikacije ostalo nam je preko 2 milijuna datoteka, ukupno nešto manje od 7TB. Zbirka se sastoji od dva dijela: MySQL “.sql.gz” dump metapodataka i 72 torrent datoteke od oko 50-100GB svaka. Metapodaci sadrže podatke kako ih je prijavio Z-Library web stranica (naslov, autor, opis, tip datoteke), kao i stvarnu veličinu datoteke i md5sum koji smo primijetili, budući da se ponekad ne podudaraju. Čini se da postoje rasponi datoteka za koje Z-Library sam ima netočne metapodatke. Možda smo također pogrešno preuzeli datoteke u nekim izoliranim slučajevima, što ćemo pokušati otkriti i ispraviti u budućnosti. Velike torrent datoteke sadrže stvarne podatke o knjigama, s Z-Library ID-om kao nazivom datoteke. Ekstenzije datoteka mogu se rekonstruirati pomoću dumpa metapodataka. Zbirka je mješavina nefikcionalnog i fikcionalnog sadržaja (nije odvojena kao u Library Genesis). Kvaliteta također jako varira. Ovo prvo izdanje sada je potpuno dostupno. Imajte na umu da su torrent datoteke dostupne samo putem našeg Tor zrcala. Izdanje 1 (%(date)s) Ovo je jedna dodatna torrent datoteka. Ne sadrži nove informacije, ali ima neke podatke koji mogu potrajati neko vrijeme za izračunavanje. To je čini praktičnom za imati, jer je preuzimanje ovog torrenta često brže nego izračunavanje od nule. Konkretno, sadrži SQLite indekse za tar datoteke, za korištenje s <a %(a_href)s>ratarmount</a>. Izdanje 2 dodatak (%(date)s) Dobili smo sve knjige koje su dodane u Z-Library između našeg posljednjeg zrcala i kolovoza 2022. Također smo se vratili i prikupili neke knjige koje smo propustili prvi put. Sve u svemu, ova nova zbirka ima oko 24TB. Opet, ova zbirka je deduplicirana protiv Library Genesis, budući da su već dostupni torrenti za tu zbirku. Podaci su organizirani slično kao u prvom izdanju. Postoji MySQL “.sql.gz” dump metapodataka, koji također uključuje sve metapodatke iz prvog izdanja, čime ga nadmašuje. Također smo dodali neke nove stupce: Spomenuli smo ovo prošli put, ali samo da pojasnimo: "filename" i "md5" su stvarna svojstva datoteke, dok su "filename_reported" i "md5_reported" ono što smo preuzeli iz Z-Library. Ponekad se ova dva ne slažu, pa smo uključili oba. Za ovo izdanje promijenili smo kolaciju na "utf8mb4_unicode_ci", što bi trebalo biti kompatibilno sa starijim verzijama MySQL-a. Datoteke podataka su slične kao prošli put, iako su mnogo veće. Jednostavno nismo imali volje stvarati mnoštvo manjih torrent datoteka. "pilimi-zlib2-0-14679999-extra.torrent" sadrži sve datoteke koje smo propustili u posljednjem izdanju, dok su ostali torrenti svi novi ID rasponi.  <strong>Ažuriranje %(date)s:</strong> Većina naših torrenta bila je prevelika, što je uzrokovalo probleme torrent klijentima. Uklonili smo ih i objavili nove torrente. <strong>Ažuriranje %(date)s:</strong> Još uvijek je bilo previše datoteka, pa smo ih zapakirali u tar datoteke i ponovno objavili nove torrente. %(key)s: je li ova datoteka već u Library Genesis, bilo u zbirci nefikcije ili fikcije (podudara se s md5). %(key)s: u kojem torrentu se nalazi ova datoteka. %(key)s: postavljeno kada nismo uspjeli preuzeti knjigu. Izdanje 2 (%(date)s) Zlib izdanja (izvorne stranice opisa) Tor domena Glavna web stranica Z-Library scrape Čini se da je zbirka "Kineski" u Z-Library ista kao naša DuXiu zbirka, ali s različitim MD5-ovima. Isključujemo te datoteke iz torrenta kako bismo izbjegli dupliciranje, ali ih i dalje prikazujemo u našem indeksu pretraživanja. Metapodaci Dobivate %(percentage)s%% dodatnih brzih preuzimanja, jer vas je preporučio korisnik %(profile_link)s. Ovo se odnosi na cijelo razdoblje članstva. Donirajte Pridruži se Odabrano do %(percentage)s%% popusta Alipay podržava međunarodne kreditne/debitne kartice. Pogledajte <a %(a_alipay)s>ovaj vodič</a> za više informacija. Pošaljite nam Amazon.com poklon kartice koristeći svoju kreditnu/debitnu karticu. Možete kupiti kriptovalute koristeći kreditne/debitne kartice. WeChat (Weixin Pay) podržava međunarodne kreditne/debitne kartice. U aplikaciji WeChat idite na “Me → Services → Wallet → Add a Card”. Ako to ne vidite, omogućite opciju putem “Me → Settings → General → Tools → Weixin Pay → Enable”. (koristite kada šaljete Ethereum s Coinbase-a) kopirano! kopiraj (najniži minimalni iznos) (upozorenje: visoki minimalni iznos) -%(percentage)s%% 12 mjeseci 1 mjesec 24 mjeseca 3 mjeseca 48 mjeseci 6 mjeseci 96 mjeseci Odaberite koliko dugo želite pretplatu. <div %(div_monthly_cost)s></div><div %(div_after)s>nakon <span %(span_discount)s></span> popusta</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% za 12 mjeseci za 1 mjesec za 24 mjeseca za 3 mjeseca za 48 mjeseci za 6 mjeseci za 96 mjeseci %(monthly_cost)s / mjesec kontaktirajte nas Izravni <strong>SFTP</strong> poslužitelji Donacija na razini poduzeća ili razmjena za nove zbirke (npr. novi skenovi, OCR-irani skupovi podataka). Stručni Pristup <strong>Neograničen</strong> pristup velikom brzinom <div %(div_question)s>Mogu li nadograditi svoje članstvo ili dobiti više članstava?</div> <div %(div_question)s>Mogu li donirati bez da postanem član?</div> Naravno. Prihvaćamo donacije bilo kojeg iznosa na ovu Monero (XMR) adresu: %(address)s. <div %(div_question)s>Što znače rasponi po mjesecu?</div> Možete doći do donje strane raspona primjenom svih popusta, kao što je odabir razdoblja duljeg od mjesec dana. <div %(div_question)s>Obnavljaju li se članstva automatski?</div> Članstva se <strong>ne</strong> obnavljaju automatski. Možete se pridružiti na koliko god dugo ili kratko želite. <div %(div_question)s>Na što trošite donacije?</div> 100%% ide na očuvanje i omogućavanje pristupa svjetskom znanju i kulturi. Trenutno ih uglavnom trošimo na servere, pohranu i propusnost. Nijedan novac ne ide osobno članovima tima. <div %(div_question)s>Mogu li dati veliku donaciju?</div> To bi bilo nevjerojatno! Za donacije veće od nekoliko tisuća dolara, molimo vas da nas izravno kontaktirate na %(email)s. <div %(div_question)s>Imate li druge metode plaćanja?</div> Trenutno ne. Mnogi ljudi ne žele da arhive poput ove postoje, pa moramo biti oprezni. Ako nam možete pomoći da sigurno postavimo druge (prikladnije) metode plaćanja, molimo vas da nas kontaktirate na %(email)s. Donacijski FAQ Imate <a %(a_donation)s>postojeću donaciju</a> u tijeku. Molimo dovršite ili otkažite tu donaciju prije nego što napravite novu donaciju. <a %(a_all_donations)s>Vidjeti sve vlastite donacije</a> Za donacije veće od 5000 USD molimo kontaktirajte nas izravno na %(email)s. Pozdravljamo velike donacije od bogatih pojedinaca ili institucija.  Imajte na umu da, iako su članstva na ovoj stranici "mjesečna", radi se o jednokratnim donacijama (ne ponavljaju se). Pogledajte <a %(faq)s>Često postavljana pitanja o donacijama</a>. Anna’s Archive je neprofitni projekt otvorenog koda i otvorenih podataka. Doniranjem i postajanjem članom podržavate naše operacije i razvoj. Svima našim članovima: hvala što nas održavate! ❤️ Za više informacija, pogledajte <a %(a_donate)s>FAQ o donacijama</a>. Da biste postali član, molimo <a %(a_login)s>Prijavite se ili Registrirajte</a>. Hvala na podršci! $%(cost)s / mjesec Ako ste pogriješili tijekom plaćanja, ne možemo izvršiti povrat, ali ćemo pokušati ispraviti situaciju. Pronađite stranicu “Kripto” u svojoj PayPal aplikaciji ili na web stranici. To je obično pod “Financije”. Idite na stranicu “Bitcoin” u svojoj PayPal aplikaciji ili na web stranici. Pritisnite gumb “Prebaci” %(transfer_icon)s, a zatim “Pošalji”. Alipay Alipay 支付宝 / WeChat 微信 Amazon poklon kartica %(amazon)s poklon kartica Bankovna kartica Bankovna kartica (korištenje aplikacije) Binance Kreditna/debitna/Apple/Google (BMC) Cash App Kreditna/debitna kartica Kreditna/debitna kartica 2 Kreditna/debitna kartica (rezervna) Kripto %(bitcoin_icon)s Kartica / PayPal / Venmo PayPal (SAD) %(bitcoin_icon)s PayPal PayPal (redovni) Pix (Brazil) Revolut (privremeno nedostupno) WeChat Odaberite svoju preferiranu kripto valutu: Donirajte koristeći Amazon poklon karticu. <strong>VAŽNO:</strong> Ova opcija je za %(amazon)s. Ako želite koristiti drugu Amazon web stranicu, odaberite je iznad. <strong>VAŽNO:</strong> Podržavamo samo Amazon.com, a ne druge Amazon web stranice. Na primjer, .de, .co.uk, .ca, NISU podržane. Molimo vas da NE pišete vlastitu poruku. Unesite točan iznos: %(amount)s Napominjemo da moramo zaokružiti na iznose prihvaćene od strane naših preprodavača (minimalno %(minimum)s). Donirajte koristeći kreditnu/debitnu karticu putem Alipay aplikacije (vrlo jednostavno za postavljanje). Instalirajte Alipay aplikaciju iz <a %(a_app_store)s>Apple App Store</a> ili <a %(a_play_store)s>Google Play Store</a>. Registrirajte se koristeći svoj broj telefona. Nisu potrebni dodatni osobni podaci. <span %(style)s>1</span>Instalirajte Alipay aplikaciju Podržano: Visa, MasterCard, JCB, Diners Club i Discover. Pogledajte <a %(a_alipay)s>ovaj vodič</a> za više informacija. <span %(style)s>2</span>Dodajte bankovnu karticu S Binanceom, kupujete Bitcoin kreditnom/debitnom karticom ili bankovnim računom, a zatim donirate taj Bitcoin nama. Na taj način možemo ostati sigurni i anonimni prilikom prihvaćanja vaše donacije. Binance je dostupan u gotovo svakoj zemlji i podržava većinu banaka te kreditne/debitne kartice. Trenutno je to naša glavna preporuka. Cijenimo što ste odvojili vrijeme da naučite kako donirati koristeći ovu metodu, jer nam to puno pomaže. Za kreditne kartice, debitne kartice, Apple Pay i Google Pay koristimo “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). U njihovom sustavu, jedna “kava” je jednaka 5$, tako da će vaša donacija biti zaokružena na najbliži višekratnik od 5. Donirajte putem Cash Appa. Ako imate Cash App, ovo je najlakši način za donaciju! Napominjemo da za transakcije ispod %(amount)s, Cash App može naplatiti naknadu od %(fee)s. Za %(amount)s ili više, besplatno je! Donirajte kreditnom ili debitnom karticom. Ova metoda koristi pružatelja kriptovaluta kao posrednika za konverziju. To može biti malo zbunjujuće, stoga koristite ovu metodu samo ako druge metode plaćanja ne rade. Također, ne radi u svim zemljama. Ne možemo izravno podržati kreditne/debitne kartice jer banke ne žele surađivati s nama. ☹ Međutim, postoji nekoliko načina za korištenje kreditnih/debitnih kartica putem drugih metoda plaćanja: S kriptovalutama možete donirati koristeći BTC, ETH, XMR i SOL. Koristite ovu opciju ako ste već upoznati s kriptovalutama. S kriptovalutama možete donirati koristeći BTC, ETH, XMR i više. Kripto ekspresne usluge Ako prvi put koristite kriptovalute, predlažemo da koristite %(options)s za kupnju i donaciju Bitcoina (originalne i najkorištenije kriptovalute). Napominjemo da kod malih donacija naknade za kreditne kartice mogu eliminirati naš %(discount)s%% popust, stoga preporučujemo duže pretplate. Donirajte koristeći kreditnu/debitnu karticu, PayPal ili Venmo. Možete odabrati između ovih opcija na sljedećoj stranici. Google Pay i Apple Pay također bi mogli raditi. Napominjemo da su za male donacije naknade visoke, stoga preporučujemo duže pretplate. Za donaciju putem PayPal US, koristit ćemo PayPal kripto, što nam omogućuje da ostanemo anonimni. Cijenimo što ste odvojili vrijeme da naučite kako donirati koristeći ovu metodu, jer nam to puno pomaže. Donirajte putem PayPala. Donirajte koristeći svoj uobičajeni PayPal račun. Donirajte putem Revoluta. Ako imate Revolut, ovo je najlakši način za donaciju! Ovaj način plaćanja omogućuje samo maksimalno %(amount)s. Molimo odaberite drugačije trajanje ili način plaćanja. Ova metoda plaćanja zahtijeva najmanje %(amount)s. Molimo odaberite drugo razdoblje ili metodu plaćanja. Binance Coinbase Kraken Molimo odaberite način plaćanja. “Usvojite torrent”: vaše korisničko ime ili poruka u nazivu torrent datoteke <div %(div_months)s>jednom svakih 12 mjeseci članstva</div> Vaše korisničko ime ili anonimno spominjanje u zaslugama Rani pristup novim značajkama Ekskluzivni Telegram s ažuriranjima iza kulisa %(number)s brzih preuzimanja dnevno ako donirate ovaj mjesec! <a %(a_api)s>Pristup JSON API-ju</a> Legendarni status u očuvanju ljudskog znanja i kulture Prethodne pogodnosti, plus: Zaradite <strong>%(percentage)s%% dodatnih preuzimanja</strong> <a %(a_refer)s>preporučivanjem prijateljima</a>. SciDB radovi <strong>neograničeno</strong> bez verifikacije Kada postavljate pitanja o računu ili donacijama, dodajte svoj ID računa, snimke zaslona, potvrde, što više informacija. Provjeravamo našu e-poštu svakih 1-2 tjedna, pa će izostavljanje ovih informacija odgoditi bilo kakvo rješenje. Da biste dobili još više preuzimanja, <a %(a_refer)s>preporučite nas prijateljima</a>! Mi smo mali tim volontera. Možda će nam trebati 1-2 tjedna da odgovorimo. Imajte na umu da ime računa ili slika mogu izgledati čudno. Nema potrebe za brigom! Ove račune upravljaju naši donacijski partneri. Naši računi nisu hakirani. Donirajte <span %(span_cost)s></span> <span %(span_label)s></span> za 12 mjeseci “%(tier_name)s” za 1 mjesec “%(tier_name)s” za 24 mjeseca “%(tier_name)s” za 3 mjeseca “%(tier_name)s” za 48 mjeseci “%(tier_name)s” za 6 mjeseci “%(tier_name)s” za 96 mjeseci “%(tier_name)s” Još uvijek možete otkazati donaciju tijekom naplate. Kliknite gumb za donaciju kako biste potvrdili ovu donaciju. <strong>Važna napomena:</strong> Cijene kriptovaluta mogu se divlje mijenjati, ponekad čak i do 20%% u nekoliko minuta. To je još uvijek manje od naknada koje imamo s mnogim pružateljima plaćanja, koji često naplaćuju 50-60%% za rad s "sjenovitom dobrotvornom organizacijom" poput nas. <u>Ako nam pošaljete račun s originalnom cijenom koju ste platili, i dalje ćemo vam pripisati odabrano članstvo</u> (sve dok račun nije stariji od nekoliko sati). Zaista cijenimo što ste spremni nositi se s ovakvim stvarima kako biste nas podržali! ❤️ ❌ Nešto je pošlo po zlu. Molimo osvježite stranicu i pokušajte ponovno. <span %(span_circle)s>1</span>Kupite Bitcoin na PayPalu <span %(span_circle)s>2</span>Prebacite Bitcoin na našu adresu ✅ Preusmjeravanje na stranicu za donacije… Doniraj Molimo pričekajte barem <span %(span_hours)s>24 sata</span> (i osvježite ovu stranicu) prije nego što nas kontaktirate. Ako želite donirati (bilo koji iznos) bez članstva, slobodno koristite ovu Monero (XMR) adresu: %(address)s. Nakon slanja vaše poklon kartice, naš automatizirani sustav će je potvrditi unutar nekoliko minuta. Ako to ne uspije, pokušajte ponovno poslati vašu poklon karticu (<a %(a_instr)s>upute</a>). Ako to još uvijek ne uspije, molimo pošaljite nam e-mail i Anna će ga ručno pregledati (to može potrajati nekoliko dana), i svakako spomenite jeste li već pokušali ponovno slanje. Primjer: Molimo koristite <a %(a_form)s>službeni obrazac Amazon.com</a> za slanje poklon kartice od %(amount)s na dolje navedenu adresu e-pošte. E-mail primatelja "Za" u obrascu: Amazon poklon kartica Ne možemo prihvatiti druge metode poklon kartica, <strong>samo poslane izravno s službenog obrasca na Amazon.com</strong>. Ne možemo vratiti vašu poklon karticu ako ne koristite ovaj obrazac. Koristite samo jednom. Jedinstveno za vaš račun, ne dijelite. Čekanje na poklon karticu… (osvježite stranicu za provjeru) Otvorite <a %(a_href)s>stranicu za donacije s QR kodom</a>. Skenirajte QR kod s Alipay aplikacijom ili pritisnite gumb za otvaranje Alipay aplikacije. Molimo budite strpljivi; stranica može potrajati dok se učita jer je u Kini. <span %(style)s>3</span>Napravite donaciju (skenirajte QR kod ili pritisnite gumb) Kupite PYUSD coin na PayPalu Kupite Bitcoin (BTC) na Cash Appu Kupite malo više (preporučujemo %(more)s više) od iznosa koji donirate (%(amount)s), kako biste pokrili naknade za transakcije. Sve što ostane, zadržat ćete. Idite na stranicu “Bitcoin” (BTC) u Cash Appu. Prenesite Bitcoin na našu adresu Za male donacije (ispod $25), možda ćete trebati koristiti Rush ili Priority. Kliknite gumb “Pošalji bitcoin” za izvršenje “isplate”. Prebacite se s dolara na BTC pritiskom na ikonu %(icon)s. Unesite iznos BTC-a ispod i kliknite “Pošalji”. Pogledajte <a %(help_video)s>ovaj video</a> ako zapnete. Ekspresne usluge su praktične, ali naplaćuju se veće naknade. Možete koristiti ovo umjesto kripto mjenjačnice ako želite brzo napraviti veću donaciju i ne smeta vam naknada od 5-10$. Obavezno pošaljite točan iznos kriptovalute prikazan na stranici za donacije, a ne iznos u dolarima ($). Inače će naknada biti oduzeta i ne možemo automatski obraditi vaše članstvo. Ponekad potvrda može potrajati do 24 sata, stoga svakako osvježite ovu stranicu (čak i ako je istekla). Upute za kreditne / debitne kartice Donirajte putem naše stranice za kreditne / debitne kartice Neki koraci spominju kripto novčanike, ali ne brinite, ne morate učiti ništa o kriptu za ovo. %(coin_name)s upute Skenirajte ovaj QR kôd s aplikacijom Crypto Wallet kako biste brzo ispunili podatke o plaćanju Skenirajte QR kôd za plaćanje Podržavamo samo standardnu verziju kripto kovanica, bez egzotičnih mreža ili verzija kovanica. Može potrajati do sat vremena da se transakcija potvrdi, ovisno o kovanici. Donirajte %(amount)s na <a %(a_page)s>ovoj stranici</a>. Ova donacija je istekla. Molimo otkažite i stvorite novu. Ako ste već platili: Da, poslao/la sam svoj račun e-poštom Ako je tečaj kriptovalute varirao tijekom transakcije, obavezno uključite potvrdu koja pokazuje originalni tečaj. Zaista cijenimo što ste se potrudili koristiti kripto, to nam puno pomaže! ❌ Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. <span %(span_circle)s>%(circle_number)s</span>Pošaljite nam potvrdu e-poštom Ako naiđete na bilo kakve probleme, molimo kontaktirajte nas na %(email)s i uključite što više informacija (kao što su snimke zaslona). ✅ Hvala na vašoj donaciji! Anna će ručno aktivirati vaše članstvo unutar nekoliko dana. Pošaljite potvrdu ili snimku zaslona na vašu osobnu adresu za verifikaciju: Kada pošaljete svoj račun e-poštom, kliknite ovaj gumb kako bi ga Annina arhiva mogla ručno pregledati (to može potrajati nekoliko dana): Pošaljite račun ili snimku zaslona na svoju osobnu adresu za provjeru. NEMOJTE koristiti ovu adresu e-pošte za svoju PayPal donaciju. Otkaži Da, molim otkažite Jeste li sigurni da želite otkazati? Nemojte otkazivati ako ste već platili. ❌ Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. Napravite novu donaciju ✅ Vaša donacija je otkazana. Datum: %(date)s Identifikator: %(id)s Ponovno naruči Status: <span %(span_label)s>%(label)s</span> Ukupno: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mjesec za %(duration)s mjeseci, uključujući %(discounts)s%% popust)</span> Ukupno: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mjesec za %(duration)s mjeseci)</span> 1. Unesite svoju e-mail adresu. 2. Odaberite svoj način plaćanja. 3. Ponovno odaberite svoj način plaćanja. 4. Odaberite “Samostalno hostirani” novčanik. 5. Kliknite “Potvrđujem vlasništvo”. 6. Trebali biste primiti račun putem e-pošte. Molimo pošaljite nam ga, a mi ćemo potvrditi vašu donaciju što je prije moguće. (možda biste htjeli otkazati i stvoriti novu donaciju) Upute za plaćanje su sada zastarjele. Ako želite napraviti još jednu donaciju, koristite gumb "Ponovno naruči" iznad. Već ste platili. Ako ipak želite pregledati upute za plaćanje, kliknite ovdje: Prikaži starije upute za plaćanje Ako je stranica za donacije blokirana, pokušajte s drugačijom internetskom vezom (npr. VPN ili internet na mobitelu). Nažalost, Alipay stranica je često dostupna samo iz <strong>kopnene Kine</strong>. Možda ćete trebati privremeno onemogućiti svoj VPN ili koristiti VPN za kopnenu Kinu (ili Hong Kong također ponekad radi). <span %(span_circle)s>1</span>Donirajte putem Alipaya Donirajte ukupni iznos od %(total)s koristeći <a %(a_account)s>ovaj Alipay račun</a> Alipay upute <span %(span_circle)s>1</span>Prebacite na jedan od naših kripto računa Donirajte ukupni iznos od %(total)s na jednu od ovih adresa: Upute za kripto Slijedite upute za kupnju Bitcoina (BTC). Trebate kupiti samo iznos koji želite donirati, %(total)s. Unesite našu Bitcoin (BTC) adresu kao primatelja i slijedite upute za slanje vaše donacije od %(total)s: <span %(span_circle)s>1</span>Donirajte putem Pixa Donirajte ukupni iznos od %(total)s koristeći <a %(a_account)s>ovaj Pix račun</a> Upute za Pix <span %(span_circle)s>1</span>Donirajte putem WeChata Donirajte ukupni iznos od %(total)s koristeći <a %(a_account)s>ovaj WeChat račun</a> Upute za WeChat Koristite bilo koju od sljedećih “kreditna kartica u Bitcoin” ekspresnih usluga, koje traju samo nekoliko minuta: BTC / Bitcoin adresa (vanjski novčanik): BTC / Bitcoin iznos: Ispunite sljedeće podatke u obrascu: Ako su bilo koje od ovih informacija zastarjele, molimo pošaljite nam email kako bismo znali. Molimo koristite ovaj <span %(underline)s>točan iznos</span>. Vaš ukupni trošak može biti veći zbog naknada za kreditne kartice. Za male iznose to može biti više od našeg popusta, nažalost. (najmanje: %(minimum)s) (minimum: %(minimum)s) (minimalno: %(minimum)s) (minimum: %(minimum)s, bez provjere za prvu transakciju) (minimum: %(minimum)s) (minimum: %(minimum)s ovisno o zemlji, bez provjere za prvu transakciju) Slijedite upute za kupnju PYUSD kovanice (PayPal USD). Kupite malo više (preporučujemo %(more)s više) od iznosa koji donirate (%(amount)s), kako biste pokrili naknade za transakciju. Sve što ostane, zadržat ćete. Idite na stranicu “PYUSD” u svojoj PayPal aplikaciji ili na web stranici. Pritisnite gumb “Transfer” %(icon)s, a zatim “Send”. Ažuriraj status Za ponovno postavljanje timera, jednostavno napravite novu donaciju. Svakako koristite iznos u BTC-u ispod, <em>NE</em> u eurima ili dolarima, inače nećemo primiti točan iznos i nećemo moći automatski potvrditi vaše članstvo. Kupite Bitcoin (BTC) na Revolutu Kupite malo više (preporučujemo %(more)s više) od iznosa koji donirate (%(amount)s), kako biste pokrili naknade za transakcije. Sve što ostane, zadržat ćete. Idite na stranicu “Crypto” u Revolutu da kupite Bitcoin (BTC). Prenesite Bitcoin na našu adresu Za male donacije (ispod $25) možda ćete trebati koristiti Rush ili Priority. Kliknite gumb “Pošalji bitcoin” za “povlačenje”. Prebacite se s eura na BTC pritiskom na ikonu %(icon)s. Unesite BTC iznos ispod i kliknite “Pošalji”. Pogledajte <a %(help_video)s>ovaj video</a> ako zapnete. Status: 1 2 Vodič korak po korak Pogledajte vodič korak po korak u nastavku. Inače biste mogli biti zaključani iz ovog računa! Ako već niste, zapišite svoj tajni ključ za logiranje: Hvala vam na vašoj donaciji! Preostalo vrijeme: Donacija Prenesite %(amount)s na %(account)s Čekanje na potvrdu (osvježite stranicu za provjeru)… Čekanje na prijenos (osvježite stranicu za provjeru)… Ranije Brza preuzimanja u posljednja 24 sata računaju se prema dnevnom ograničenju. Preuzimanja s Fast Partner Servera označena su s %(icon)s. Posljednjih 18 sati Još nema preuzetih datoteka. Preuzete datoteke nisu javno prikazane. Sva vremena su u UTC. Preuzete datoteke Ako ste preuzeli datoteku s brzim i sporim preuzimanjima, pojavit će se dvaput. Ne brinite previše, mnogo je ljudi koji preuzimaju s web stranica povezanih s nama, i izuzetno je rijetko upasti u nevolje. Međutim, da biste ostali sigurni, preporučujemo korištenje VPN-a (plaćenog) ili <a %(a_tor)s>Tora</a> (besplatno). Preuzeo sam 1984 od Georgea Orwella, hoće li policija doći na moja vrata? Vi ste Ana! Tko je Ana? Imamo jedan stabilan JSON API za članove, za dobivanje URL-a za brzo preuzimanje: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacija unutar samog JSON-a). Za druge slučajeve korištenja, kao što je iteracija kroz sve naše datoteke, izgradnja prilagođenog pretraživanja i slično, preporučujemo <a %(a_generate)s>generiranje</a> ili <a %(a_download)s>preuzimanje</a> naših ElasticSearch i MariaDB baza podataka. Sirovi podaci mogu se ručno istraživati <a %(a_explore)s>putem JSON datoteka</a>. Naš popis sirovih torrenta može se preuzeti i kao <a %(a_torrents)s>JSON</a>. Imate li API? Ovdje ne hostiramo nikakve materijale zaštićene autorskim pravima. Mi smo tražilica i kao takvi samo indeksiramo metapodatke koji su već javno dostupni. Prilikom preuzimanja s ovih vanjskih izvora, predlažemo da provjerite zakone u svojoj jurisdikciji u vezi s onim što je dopušteno. Nismo odgovorni za sadržaj koji hostiraju drugi. Ako imate pritužbe na ono što vidite ovdje, najbolje je kontaktirati izvornu web stranicu. Redovito povlačimo njihove promjene u našu bazu podataka. Ako stvarno mislite da imate valjanu DMCA pritužbu na koju bismo trebali odgovoriti, molimo ispunite <a %(a_copyright)s>DMCA / obrazac za prijavu autorskih prava</a>. Vaše pritužbe shvaćamo ozbiljno i javit ćemo vam se što je prije moguće. Kako prijaviti kršenje autorskih prava? Evo nekoliko knjiga koje imaju posebno značenje za svijet sjene knjižnica i digitalne očuvanje: Koje su vaše omiljene knjige? Također bismo željeli podsjetiti sve da su sav naš kod i podaci potpuno otvorenog koda. Ovo je jedinstveno za projekte poput našeg — nismo svjesni nijednog drugog projekta s tako masivnim katalogom koji je također potpuno otvorenog koda. Vrlo rado pozdravljamo svakoga tko misli da loše vodimo naš projekt da uzme naš kod i podatke i postavi vlastitu sjenu knjižnicu! Ne govorimo ovo iz inata ili nečeg sličnog — iskreno mislimo da bi to bilo sjajno jer bi podiglo ljestvicu za sve i bolje očuvalo naslijeđe čovječanstva. Mrzim kako vodite ovaj projekt! Voljeli bismo da ljudi postave <a %(a_mirrors)s>mirrore</a>, a mi ćemo to financijski podržati. Kako mogu pomoći? Da, prikupljamo. Naša inspiracija za prikupljanje metapodataka je cilj Aarona Swartza “jedna web stranica za svaku knjigu ikad objavljenu”, za što je stvorio <a %(a_openlib)s>Open Library</a>. Taj projekt je uspješan, ali naša jedinstvena pozicija omogućuje nam dobivanje metapodataka koje oni ne mogu. Druga inspiracija bila je naša želja da saznamo <a %(a_blog)s>koliko knjiga ima na svijetu</a>, kako bismo mogli izračunati koliko knjiga još trebamo spasiti. Prikupljate li metapodatke? Napominjemo da mhut.org blokira određene IP raspon, pa bi VPN mogao biti potreban. <strong>Android:</strong> Kliknite na izbornik s tri točke u gornjem desnom kutu i odaberite "Dodaj na početni zaslon". <strong>iOS:</strong> Kliknite na gumb “Dijeli” na dnu i odaberite “Dodaj na početni zaslon”. Nemamo službenu mobilnu aplikaciju, ali možete instalirati ovu web stranicu kao aplikaciju. Imate li mobilnu aplikaciju? Molimo pošaljite ih <a %(a_archive)s>Internet Archive</a>. Oni će ih pravilno sačuvati. Kako mogu donirati knjige ili druge fizičke materijale? Kako mogu zatražiti knjige? <a %(a_blog)s>Annin blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — redovita ažuriranja <a %(a_software)s>Annin Softver</a> — naš open source kod <a %(a_datasets)s>Datasets</a> — o podacima <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativne domene Postoje li dodatni resursi o Anninoj Arhivi? <a %(a_translate)s>Prevedi na Anninom Softveru</a> — naš sustav za prevođenje <a %(a_wikipedia)s>Wikipedia</a> — više o nama (molimo pomozite održavati ovu stranicu ažuriranom ili stvorite jednu za svoj jezik!) Odaberite postavke koje vam se sviđaju, ostavite okvir za pretraživanje prazan, kliknite “Pretraži”, a zatim označite stranicu pomoću značajke oznaka vašeg preglednika. Kako spremiti postavke pretraživanja? Pozdravljamo istraživače sigurnosti da traže ranjivosti u našim sustavima. Veliki smo zagovornici odgovornog otkrivanja. Kontaktirajte nas <a %(a_contact)s>ovdje</a>. Trenutno nismo u mogućnosti dodijeliti nagrade za bugove, osim za ranjivosti koje imaju <a %(a_link)s>potencijal ugroziti našu anonimnost</a>, za koje nudimo nagrade u rasponu od 10.000 do 50.000 dolara. Željeli bismo u budućnosti ponuditi širi opseg za nagrade za bugove! Imajte na umu da su napadi socijalnog inženjeringa izvan opsega. Ako ste zainteresirani za ofenzivnu sigurnost i želite pomoći u arhiviranju svjetskog znanja i kulture, svakako nas kontaktirajte. Postoji mnogo načina na koje možete pomoći. Imate li program za odgovorno otkrivanje? Doslovno nemamo dovoljno resursa da svima u svijetu omogućimo preuzimanja velikom brzinom, koliko god bismo to željeli. Ako bi se neki bogati dobročinitelj želio uključiti i omogućiti nam to, bilo bi nevjerojatno, ali do tada, trudimo se najbolje što možemo. Mi smo neprofitni projekt koji se jedva održava kroz donacije. Zbog toga smo implementirali dva sustava za besplatna preuzimanja s našim partnerima: zajednički serveri sa sporim preuzimanjima i nešto brži serveri s listom čekanja (kako bismo smanjili broj ljudi koji preuzimaju u isto vrijeme). Također imamo <a %(a_verification)s>provjeru preglednika</a> za naša spora preuzimanja, jer bi ih inače botovi i strugači zloupotrebljavali, čineći stvari još sporijima za legitimne korisnike. Napominjemo da, kada koristite Tor preglednik, možda ćete trebati prilagoditi svoje sigurnosne postavke. Na najnižoj opciji, nazvanoj “Standard”, Cloudflare izazov uspijeva. Na višim opcijama, nazvanim “Sigurnije” i “Najsigurnije”, izazov ne uspijeva. Za velike datoteke ponekad spora preuzimanja mogu se prekinuti usred. Preporučujemo korištenje upravitelja preuzimanja (kao što je JDownloader) za automatsko nastavljanje velikih preuzimanja. Zašto su spora preuzimanja tako spora? Često postavljana pitanja (FAQ) Koristite <a %(a_list)s>generator popisa torrenta</a> za generiranje popisa torrenta koji su najpotrebniji za torrentiranje, unutar vaših ograničenja prostora za pohranu. Da, pogledajte <a %(a_llm)s>stranicu s LLM podacima</a>. Većina torrenta sadrži datoteke izravno, što znači da možete uputiti torrent klijente da preuzmu samo potrebne datoteke. Da biste odredili koje datoteke preuzeti, možete <a %(a_generate)s>generirati</a> naše metapodatke ili <a %(a_download)s>preuzeti</a> naše ElasticSearch i MariaDB baze podataka. Nažalost, brojni torrent kolekcije sadrže .zip ili .tar datoteke u korijenu, u kojem slučaju morate preuzeti cijeli torrent prije nego što možete odabrati pojedinačne datoteke. Još uvijek nisu dostupni jednostavni alati za filtriranje torrenta, ali pozdravljamo doprinose. (Imamo <a %(a_ideas)s>neke ideje</a> za ovaj slučaj.) Dugi odgovor: Kratki odgovor: ne lako. Pokušavamo održati minimalno dupliciranje ili preklapanje između torrenta na ovom popisu, ali to nije uvijek moguće postići i uvelike ovisi o politikama izvorišnih knjižnica. Za knjižnice koje objavljuju vlastite torrente, to je izvan naše kontrole. Za torrente koje objavljuje Annina Arhiva, dedupliciramo samo na temelju MD5 hasha, što znači da različite verzije iste knjige ne budu deduplicirane. Da. To su zapravo PDF-ovi i EPUB-ovi, samo što nemaju ekstenziju u mnogim našim torrentima. Postoje dva mjesta na kojima možete pronaći metapodatke za torrent datoteke, uključujući vrste/ekstenzije datoteka: 1. Svaka kolekcija ili izdanje ima svoje metapodatke. Na primjer, <a %(a_libgen_nonfic)s>Libgen.rs torrenti</a> imaju odgovarajuću bazu metapodataka koja se nalazi na web stranici Libgen.rs. Obično povezujemo relevantne resurse metapodataka sa stranice <a %(a_datasets)s>skupa podataka</a> svake kolekcije. 2. Preporučujemo <a %(a_generate)s>generiranje</a> ili <a %(a_download)s>preuzimanje</a> naših ElasticSearch i MariaDB baza podataka. One sadrže mapiranje za svaku datoteku ili skup datoteka u arhiviranoj bazi podataka u Anninoj Arhivi na odgovarajuće torrent datoteke (ako su dostupne), pod “torrent_paths” u ElasticSearch JSON-u. Neki torrent klijenti ne podržavaju velike veličine dijelova, što mnogi naši torrenti imaju (za novije to više ne radimo — iako je to valjano prema specifikacijama!). Stoga pokušajte s drugim klijentom ako naiđete na ovaj problem ili se požalite proizvođačima vašeg torrent klijenta. Želio bih pomoći u seedanju, ali nemam puno prostora na disku. Torrenti su prespori; mogu li preuzeti podatke izravno od vas? Mogu li preuzeti samo podskup datoteka, poput određenog jezika ili teme? Kako se nosite s duplikatima u torrentima? Mogu li dobiti popis torrenta u JSON formatu? Ne vidim PDF-ove ili EPUB-ove u torrentima, samo binarne datoteke? Što da radim? Zašto moj torrent klijent ne može otvoriti neke od vaših torrent datoteka / magnet linkova? Torrents FAQ Kako mogu prenijeti nove knjige? Molimo pogledajte <a %(a_href)s>ovaj izvrstan projekt</a>. Imate li monitor za dostupnost? Što je Annina Arhiva? Postanite član kako biste koristili brza preuzimanja. Sada podržavamo Amazon poklon kartice, kreditne i debitne kartice, kripto, Alipay i WeChat. Danas ste iskoristili sve brze preuzimanja. Pristup Sati preuzimanja u posljednjih 30 dana. Prosjek po satu: %(hourly)s. Prosjek po danu: %(daily)s. Radimo s partnerima kako bismo naše zbirke učinili lako i besplatno dostupnima svima. Vjerujemo da svatko ima pravo na kolektivnu mudrost čovječanstva. I <a %(a_search)s>ne na štetu autora</a>. Skupovi podataka korišteni u Anninoj Arhivi potpuno su otvoreni i mogu se masovno preslikati pomoću torrenta. <a %(a_datasets)s>Saznajte više…</a> Dugoročna arhiva Cijela baza podataka Pretraživanje Knjige, radovi, časopisi, stripovi, knjižni zapisi, metapodaci, … Sav naš <a %(a_code)s>kod</a> i <a %(a_datasets)s>podaci</a> su potpuno otvorenog koda. <span %(span_anna)s>Annina Arhiva</span> je neprofitni projekt s dva cilja: <li><strong>Očuvanje:</strong> Sigurnosno kopiranje cjelokupnog znanja i kulture čovječanstva.</li><li><strong>Pristup:</strong> Omogućavanje ovog znanja i kulture bilo kome u svijetu.</li> Imamo najveću svjetsku zbirku visokokvalitetnih tekstualnih podataka. <a %(a_llm)s>Saznajte više…</a> Podaci za obuku LLM-a 🪩 Zrcala: poziv za volontere Ako vodite visokorizični anonimni procesor plaćanja, molimo kontaktirajte nas. Također tražimo ljude koji žele postaviti ukusne male oglase. Sav prihod ide u naše napore očuvanja. Očuvanje Procjenjujemo da smo sačuvali oko <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% svjetskih knjiga</a>. Čuvamo knjige, radove, stripove, časopise i još mnogo toga, okupljajući ove materijale iz raznih <a href="https://en.wikipedia.org/wiki/Shadow_library">sjenskih knjižnica</a>, službenih knjižnica i drugih zbirki na jednom mjestu. Svi ovi podaci zauvijek su sačuvani omogućavanjem jednostavnog dupliciranja u velikim količinama — koristeći torrente — što rezultira mnogim kopijama diljem svijeta. Neke sjenske knjižnice to već same rade (npr. Sci-Hub, Library Genesis), dok Annina Arhiva "oslobađa" druge knjižnice koje ne nude distribuciju u velikim količinama (npr. Z-Library) ili uopće nisu sjenske knjižnice (npr. Internet Archive, DuXiu). Ova široka distribucija, u kombinaciji s open-source kodom, čini našu web stranicu otpornom na uklanjanja i osigurava dugoročno očuvanje ljudskog znanja i kulture. Saznajte više o <a href="/datasets">našim datasetima</a>. Ako ste <a %(a_member)s>član</a>, provjera preglednika nije potrebna. 🧬&nbsp;SciDB je nastavak Sci-Huba. SciDB Otvori DOI Sci-Hub je <a %(a_paused)s>pauzirao</a> učitavanje novih radova. Izravan pristup %(count)s akademskim radovima 🧬&nbsp;SciDB je nastavak Sci-Hub-a, s poznatim sučeljem i izravnim pregledom PDF-ova. Unesite svoj DOI za pregled. Imamo cijelu Sci-Hub kolekciju, kao i nove radove. Većina se može izravno pregledati s poznatim sučeljem, slično Sci-Hub-u. Neki se mogu preuzeti putem vanjskih izvora, u tom slučaju prikazujemo poveznice na te izvore. Možete uvelike pomoći dijeljenjem torrenta. <a %(a_torrents)s>Saznajte više…</a> >%(count)s seedera <%(count)s dijelitelja %(count_min)s–%(count_max)s seedera 🤝 Tražimo volontere Kao neprofitni, open-source projekt, uvijek tražimo ljude koji bi pomogli. IPFS preuzimanja Popis po %(by)s, kreiran <span %(span_time)s>%(time)s</span> Spremi ❌ Nešto je pošlo po zlu. Molimo pokušajte ponovno. ✅ Spremljeno. Molimo ponovno učitajte stranicu. Popis je prazan. uredi Dodajte ili uklonite s ovog popisa pronalaskom datoteke i otvaranjem kartice „Popisi”. Popis Kako možemo pomoći Uklanjanje preklapanja (deduplikacija) Ekstrakcija teksta i metapodataka OCR U mogućnosti smo pružiti brz pristup našim cjelokupnim zbirkama, kao i neobjavljenim zbirkama. Ovo je pristup na razini poduzeća koji možemo pružiti za donacije u rasponu od desetaka tisuća USD. Također smo spremni zamijeniti ovo za visokokvalitetne kolekcije koje još nemamo. Možemo vam vratiti novac ako nam možete pružiti obogaćivanje naših podataka, kao što su: Podržite dugoročno arhiviranje ljudskog znanja, dok poboljšavate podatke za svoj model! <a %(a_contact)s>Kontaktirajte nas</a> kako bismo razgovarali o mogućoj suradnji. Dobro je poznato da LLM-ovi napreduju na visokokvalitetnim podacima. Imamo najveću zbirku knjiga, radova, časopisa itd. na svijetu, koji su neki od najkvalitetnijih izvora teksta. LLM podaci Jedinstvena skala i raspon Naša zbirka sadrži preko stotinu milijuna datoteka, uključujući akademske časopise, udžbenike i časopise. Ovu veličinu postižemo kombiniranjem velikih postojećih repozitorija. Neke od naših izvornih zbirki već su dostupne u velikim količinama (Sci-Hub i dijelovi Libgena). Druge izvore smo sami oslobodili. <a %(a_datasets)s>Datasets</a> prikazuje potpuni pregled. Naša kolekcija uključuje milijune knjiga, radova i časopisa iz razdoblja prije e-knjiga. Veliki dijelovi ove kolekcije već su OCR-irani i već imaju malo unutarnjeg preklapanja. Nastavi Ako ste izgubili svoj ključ, molimo <a %(a_contact)s>kontaktirajte nas</a> i pružite što više informacija. Možda ćete privremeno morati stvoriti novi račun kako biste nas kontaktirali. Molimo <a %(a_account)s>prijavite se</a> za pregled ove stranice.</a> Kako bismo spriječili spam-botove da stvaraju mnogo računa, prvo moramo provjeriti vaš preglednik. Ako se nađete u beskonačnoj petlji, preporučujemo instaliranje <a %(a_privacypass)s>Privacy Pass</a>. Može pomoći i isključivanje blokatora oglasa i drugih proširenja preglednika. Prijava / Registracija Anina Arhiva je privremeno nedostupna zbog održavanja. Molimo vas da se vratite za sat vremena. Alternativni autor Alternativni opis Alternativno izdanje Alternativna ekstenzija Alternativni naziv datoteke Alternativni izdavač Alternativni naslov datum otvaranja izvornog koda Pročitajte više… opis Pretraži Anninu Arhivu za CADAL SSNO broj Pretraži Anninu Arhivu za DuXiu SSID broj Pretraži Anninu Arhivu za DuXiu DXID broj Pretraži Anninu Arhivu za ISBN Pretraži Anninu Arhivu za OCLC (WorldCat) broj Pretraži Anninu Arhivu za Open Library ID Online preglednik Annine Arhive %(count)s pogođene stranice Nakon preuzimanja: Bolja verzija ove datoteke možda je dostupna na %(link)s Masovno preuzimanje torrenta zbirka Koristite online alate za pretvaranje između formata. Preporučeni alati za pretvaranje: %(links)s Za velike datoteke preporučujemo korištenje upravitelja preuzimanja kako biste spriječili prekide. Preporučeni upravitelji preuzimanja: %(links)s EBSCOhost eBook Indeks (samo za stručnjake) (također kliknite “GET” na vrhu) (kliknite “GET” na vrhu) Vanjska preuzimanja Imate %(remaining)s preostalo danas. Hvala što ste član! ❤️ Danas ste iskoristili sve brze preuzimanja. Nedavno ste preuzeli ovu datoteku. Linkovi ostaju važeći neko vrijeme. Postanite <a %(a_membership)s>član</a> kako biste podržali dugoročno očuvanje knjiga, radova i više. Kao znak zahvalnosti za vašu podršku, dobivate brza preuzimanja. ❤️ 🚀 Brza preuzimanja 🐢 Spora preuzimanja Posudite iz Internet Archive IPFS Gateway #%(num)d (možda ćete morati pokušati više puta s IPFS-om) Libgen.li Libgen.rs fikcija Libgen.rs Non-Fiction njihovi oglasi poznati su po zlonamjernom softveru, stoga koristite blokator oglasa ili ne klikajte na oglase Amazonov „Send to Kindle” djazzov „Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Datoteke Nexus/STC mogu biti nepouzdane za preuzimanje) Nema pronađenih preuzimanja. Sve opcije preuzimanja imaju istu datoteku i trebale bi biti sigurne za korištenje. Ipak, uvijek budite oprezni pri preuzimanju datoteka s interneta, posebno s web stranica izvan Annine Arhive. Na primjer, budite sigurni da su vaši uređaji ažurirani. (bez preusmjeravanja) Otvori u našem pregledniku (otvori u pregledniku) Opcija #%(num)d: %(link)s %(extra)s Pronađite originalnu datoteku ili skup datoteka u arhiviranoj bazi podataka u CADAL-u Pretraži ručno na DuXiu Pronađi originalnu datoteku u ISBNdb Pronađi originalnu datoteku ili skup datoteka u arhiviranoj bazi podataka u WorldCat-u Pronađite originalnu datoteku u Open Library Pretražite razne druge baze podataka za ISBN (ispis samo za onemogućene korisnike) PubMed Trebat će vam čitač e-knjiga ili PDF čitač za otvaranje datoteke, ovisno o formatu datoteke. Preporučeni čitači e-knjiga: %(links)s Anina Arhiva 🧬 SciDB Sci-Hub: %(doi)s (povezani DOI možda nije dostupan u Sci-Hub) Možete poslati i PDF i EPUB datoteke na svoj Kindle ili Kobo eReader. Preporučeni alati: %(links)s Više informacija u <a %(a_slow)s>FAQ</a>. Podržite autore i knjižnice Ako vam se ovo sviđa i možete si to priuštiti, razmislite o kupnji originala ili izravnoj podršci autorima. Ako je ovo dostupno u vašoj lokalnoj knjižnici, razmislite o posudbi besplatno tamo. Preuzimanja s partnerskog servera trenutno nisu dostupna za ovu datoteku. torrent Od pouzdanih partnera. Z-Library Z-Library na Toru (zahtijeva Tor preglednik) prikaži vanjska preuzimanja <span class="font-bold">❌ Ova datoteka može imati problema i skrivena je iz izvorne knjižnice.</span> Ponekad je to na zahtjev nositelja autorskih prava, ponekad zato što je dostupna bolja alternativa, ali ponekad zbog problema s datotekom. Možda je ipak u redu preuzeti, ali preporučujemo prvo potražiti alternativnu datoteku. Više detalja: Ako i dalje želite preuzeti ovu datoteku, budite sigurni da koristite samo pouzdan, ažuriran softver za otvaranje. komentari metapodataka AA: Pretraži Aninu Arhivu za “%(name)s” Istraživač kodova: Pogledaj u Codes Exploreru “%(name)s” URL: Web stranica: Ako imate ovu datoteku i još nije dostupna u Aninoj Arhivi, razmislite o <a %(a_request)s>učitavanju</a>. Internet Archive Kontrolirano digitalno posuđivanje datoteke “%(id)s” Ovo je datoteka iz Internet Archivea, a ne izravno preuzimljiva datoteka. Možete pokušati posuditi knjigu (link ispod) ili koristiti ovaj URL kada <a %(a_request)s>zahtijevate datoteku</a>. Poboljšajte metapodatke CADAL SSNO %(id)s metapodatkovna datoteka Ovo je metapodatkovna datoteka, a ne datoteka za preuzimanje. Možete koristiti ovaj URL kada <a %(a_request)s>zahtijevate datoteku</a>. DuXiu SSID %(id)s metapodatkovna datoteka ISBNdb %(id)s metapodatkovna datoteka MagzDB ID %(id)s metapodatkovna datoteka Nexus/STC ID %(id)s metapodatkovna datoteka OCLC (WorldCat) broj %(id)s metapodatkovna datoteka Open Library %(id)s metapodatkovna datoteka Sci-Hub datoteka “%(id)s” Nije pronađeno “%(md5_input)s” nije pronađeno u našoj bazi podataka. Dodaj komentar (%(count)s) Možete dobiti md5 iz URL-a, npr. MD5 bolje verzije ove datoteke (ako je primjenjivo). Ispunite ovo ako postoji druga datoteka koja se blisko podudara s ovom datotekom (isto izdanje, isti nastavak datoteke ako ga možete pronaći), koju bi ljudi trebali koristiti umjesto ove datoteke. Ako znate za bolju verziju ove datoteke izvan Annine Arhive, molimo <a %(a_upload)s>učitajte je</a>. Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. Ostavili ste komentar. Možda će trebati minutu da se prikaže. Molimo koristite <a %(a_copyright)s>DMCA / obrazac za prijavu kršenja autorskih prava</a>. Opišite problem (obavezno) Ako ova datoteka ima izvrsnu kvalitetu, možete ovdje raspravljati o njoj! Ako ne, molimo koristite gumb “Prijavi problem s datotekom”. Izvrsna kvaliteta datoteke (%(count)s) Kvaliteta datoteke Naučite kako <a %(a_metadata)s>poboljšati metapodatke</a> za ovu datoteku sami. Opis problema Molimo <a %(a_login)s>prijavite se</a>. Obožavao/la sam ovu knjigu! Pomozite zajednici prijavom kvalitete ove datoteke! 🙌 Nešto je pošlo po zlu. Molimo ponovno učitajte stranicu i pokušajte ponovno. Prijavi problem s datotekom (%(count)s) Hvala vam što ste poslali svoje izvješće. Bit će prikazano na ovoj stranici, kao i ručno pregledano od strane Anne (dok ne budemo imali odgovarajući sustav moderiranja). Ostavite komentar Pošalji izvještaj Što nije u redu s ovom datotekom? Posudi (%(count)s) Komentari (%(count)s) Preuzimanja (%(count)s) Istraži metapodatke (%(count)s) Popisi (%(count)s) Statistike (%(count)s) Za informacije o ovoj određenoj datoteci, pogledajte njezinu <a %(a_href)s>JSON datoteku</a>. Ovo je datoteka kojom upravlja knjižnica <a %(a_ia)s>IA-ovog Kontroliranog digitalnog posuđivanja</a>, a indeksirana je u Anninoj Arhivi za pretraživanje. Za informacije o raznim datasetima koje smo sastavili, pogledajte <a %(a_datasets)s>stranicu Datasets</a>. Metapodaci iz povezane datoteke ili skupa datoteka u arhiviranoj bazi podataka Poboljšaj metapodatke na Open Library "MD5 datoteke" je hash koji se izračunava iz sadržaja datoteke i razumno je jedinstven na temelju tog sadržaja. Sve sjene knjižnice koje smo ovdje indeksirali prvenstveno koriste MD5 za identifikaciju datoteka. Datoteka se može pojaviti u više shadow knjižnica. Za informacije o raznim datasetima koje smo sastavili, pogledajte <a %(a_datasets)s>stranicu Datasets</a>. Prijavi kvalitetu datoteke Ukupno preuzimanja: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Češka metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Knjige %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Upozorenje: više povezanih datoteka: Kada pogledate knjigu na Anninoj Arhivi, možete vidjeti razna polja: naslov, autor, izdavač, izdanje, godina, opis, naziv datoteke i više. Svi ti dijelovi informacija nazivaju se <em>metapodaci</em>. Budući da kombiniramo knjige iz raznih <em>izvornih knjižnica</em>, prikazujemo sve dostupne metapodatke iz te izvorne knjižnice. Na primjer, za knjigu koju smo dobili iz Library Genesis, prikazat ćemo naslov iz baze podataka Library Genesis. Ponekad je knjiga prisutna u <em>više</em> izvorišnih knjižnica, koje mogu imati različita polja metapodataka. U tom slučaju jednostavno prikazujemo najdužu verziju svakog polja, jer ona vjerojatno sadrži najkorisnije informacije! I dalje ćemo prikazivati ostala polja ispod opisa, npr. kao "alternativni naslov" (ali samo ako su različita). Također izdvajamo <em>kodove</em> kao što su identifikatori i klasifikatori iz izvorišne knjižnice. <em>Identifikatori</em> jedinstveno predstavljaju određeno izdanje knjige; primjeri su ISBN, DOI, Open Library ID, Google Books ID ili Amazon ID. <em>Klasifikatori</em> grupiraju više sličnih knjiga; primjeri su Dewey Decimal (DCC), UDC, LCC, RVK ili GOST. Ponekad su ovi kodovi eksplicitno povezani u izvorišnim knjižnicama, a ponekad ih možemo izvući iz naziva datoteke ili opisa (prvenstveno ISBN i DOI). Možemo koristiti identifikatore za pronalaženje zapisa u <em>kolekcijama samo s metapodacima</em>, kao što su OpenLibrary, ISBNdb ili WorldCat/OCLC. Postoji specifična <em>kartica metapodataka</em> u našem pretraživaču ako želite pregledavati te kolekcije. Koristimo odgovarajuće zapise za popunjavanje nedostajućih polja metapodataka (npr. ako nedostaje naslov), ili npr. kao “alternativni naslov” (ako postoji postojeći naslov). Da biste točno vidjeli odakle potječu metapodaci knjige, pogledajte karticu <em>“Tehnički detalji”</em> na stranici knjige. Ona sadrži poveznicu na sirovi JSON za tu knjigu, s pokazivačima na sirovi JSON izvornih datoteka. Za više informacija, pogledajte sljedeće stranice: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Pretraga (kartica metapodataka)</a>, <a %(a_codes)s>Istraživač kodova</a>, i <a %(a_example)s>Primjer metapodataka JSON</a>. Na kraju, svi naši metapodaci mogu se <a %(a_generated)s>generirati</a> ili <a %(a_downloaded)s>preuzeti</a> kao ElasticSearch i MariaDB baze podataka. Pozadina Možete pomoći u očuvanju knjiga poboljšanjem metapodataka! Prvo, pročitajte pozadinske informacije o metapodacima na Aninoj Arhivi, a zatim naučite kako poboljšati metapodatke povezivanjem s Open Library, i zaradite besplatno članstvo na Aninoj Arhivi. Poboljšajte metapodatke Dakle, ako naiđete na datoteku s lošim metapodacima, kako biste to trebali popraviti? Možete otići u izvornu knjižnicu i slijediti njezine postupke za ispravljanje metapodataka, ali što učiniti ako je datoteka prisutna u više izvora? Postoji jedan identifikator koji se tretira posebno u Anninoj Arhivi. <strong>Polje annas_archive md5 na Open Library uvijek nadjačava sve ostale metapodatke!</strong> Vratimo se malo unatrag i naučimo o Open Library. Open Library osnovao je 2006. godine Aaron Swartz s ciljem “jedne web stranice za svaku knjigu ikad objavljenu”. To je svojevrsna Wikipedija za metapodatke knjiga: svatko je može uređivati, slobodno je licencirana i može se preuzeti u velikim količinama. To je baza podataka knjiga koja je najviše usklađena s našom misijom — zapravo, Annina Arhiva je inspirirana vizijom i životom Aarona Swartza. Umjesto da izmišljamo toplu vodu, odlučili smo preusmjeriti naše volontere prema Open Library. Ako vidite knjigu s netočnim metapodacima, možete pomoći na sljedeći način: Napominjemo da ovo funkcionira samo za knjige, ne za akademske radove ili druge vrste datoteka. Za druge vrste datoteka i dalje preporučujemo pronalaženje izvorne knjižnice. Može proći nekoliko tjedana da se promjene uključe u Aninu Arhivu, budući da trebamo preuzeti najnoviji Open Library podatkovni dump i regenerirati naš indeks pretraživanja.  Idite na <a %(a_openlib)s>web stranicu Open Library</a>. Pronađite ispravnu datoteku knjige. <strong>UPOZORENJE:</strong> budite sigurni da ste odabrali ispravno <strong>izdanje</strong>. U Open Library imate “djela” i “izdanja”. “Djelo” bi moglo biti “Harry Potter i Kamen mudraca”. "Izdanje" može biti: Prvo izdanje iz 1997. godine koje je objavio Bloomsbery s 256 stranica. Izdanje u mekom uvezu iz 2003. godine koje je objavio Raincoast Books s 223 stranice. Poljski prijevod iz 2000. godine “Harry Potter i Kamen mudraca” od Media Rodzina s 328 stranica. Sve te edicije imaju različite ISBN-ove i različite sadržaje, stoga budite sigurni da odaberete pravu! Uredite datoteku (ili je stvorite ako ne postoji) i dodajte što više korisnih informacija! Ionako ste ovdje, pa možete učiniti datoteku zaista nevjerojatnom. Pod “ID brojevi” odaberite “Anina Arhiva” i dodajte MD5 knjige iz Anine Arhive. To je dugi niz slova i brojeva nakon “/md5/” u URL-u. Pokušajte pronaći druge datoteke u Anninoj Arhivi koje također odgovaraju ovoj datoteci i dodajte ih također. U budućnosti možemo grupirati te datoteke kao duplikate na stranici pretraživanja Annine Arhive. Kada završite, zapišite URL koji ste upravo ažurirali. Kada ažurirate barem 30 zapisa s MD5-ovima iz Anine Arhive, pošaljite nam <a %(a_contact)s>email</a> i pošaljite nam popis. Dat ćemo vam besplatno članstvo za Aninu Arhivu, kako biste lakše obavljali ovaj posao (i kao zahvalu za vašu pomoć). Ovo moraju biti visokokvalitetne izmjene koje dodaju značajnu količinu informacija, inače će vaš zahtjev biti odbijen. Vaš zahtjev će također biti odbijen ako bilo koja od izmjena bude poništena ili ispravljena od strane moderatora Open Library. Povezivanje s Open Library Ako se značajno uključite u razvoj i operacije našeg rada, možemo razgovarati o dijeljenju više prihoda od donacija s vama, kako biste ih rasporedili prema potrebi. Plaćat ćemo hosting tek kada sve postavite i pokažete da ste u mogućnosti održavati arhivu ažuriranom s ažuriranjima. To znači da ćete morati platiti prvih 1-2 mjeseca iz vlastitog džepa. Vaše vrijeme neće biti kompenzirano (kao ni naše), jer je ovo čisti volonterski rad. Spremni smo pokriti troškove hostinga i VPN-a, u početku do 200 USD mjesečno. To je dovoljno za osnovni poslužitelj za pretraživanje i proxy zaštićen DMCA-om. Troškovi hostinga Molimo <strong>ne kontaktirajte nas</strong> za traženje dopuštenja ili za osnovna pitanja. Djela govore glasnije od riječi! Sve informacije su dostupne, stoga samo nastavite s postavljanjem svog zrcala. Slobodno postavite tikete ili zahtjeve za spajanje na naš Gitlab kada naiđete na probleme. Možda ćemo trebati izgraditi neke značajke specifične za zrcalo s vama, kao što je rebrendiranje iz “Anina Arhiva” u naziv vaše web stranice, (u početku) onemogućavanje korisničkih računa ili povezivanje natrag na našu glavnu stranicu s stranica knjiga. Kada pokrenete svoje zrcalo, molimo vas da nas kontaktirate. Voljeli bismo pregledati vašu OpSec, i kada to bude čvrsto, povezat ćemo se s vašim zrcalom i početi bliže surađivati s vama. Unaprijed zahvaljujemo svima koji su voljni doprinijeti na ovaj način! Nije za one slabog srca, ali bi učvrstilo dugovječnost najveće istinski otvorene knjižnice u povijesti čovječanstva. Početak Kako bismo povećali otpornost Annine Arhive, tražimo volontere za pokretanje zrcala. Vaša verzija je jasno označena kao zrcalo, npr. "Bobova arhiva, zrcalo Annine arhive". Spremni ste preuzeti rizike povezane s ovim radom, koji su značajni. Imate duboko razumijevanje potrebne operativne sigurnosti. Sadržaj <a %(a_shadow)s>ovih</a> <a %(a_pirate)s>objava</a> vam je samorazumljiv. U početku vam nećemo dati pristup preuzimanjima s poslužitelja naših partnera, ali ako sve bude išlo dobro, možemo to podijeliti s vama. Vi pokrećete otvoreni izvorni kod Anine Arhive i redovito ažurirate i kod i podatke. Spremni ste pridonijeti našoj <a %(a_codebase)s>bazi koda</a> — u suradnji s našim timom — kako bi se to ostvarilo. Tražimo ovo: Ogledala: poziv za volontere Napravite još jednu donaciju. Još nema donacija. <a %(a_donate)s>Učinite moju prvu donaciju.</a> Detalji donacija nisu javno prikazani. Moje donacije 📡 Za masovno preslikavanje naše zbirke, pogledajte stranice <a %(a_datasets)s>Skupovi podataka</a> i <a %(a_torrents)s>Torrenti</a>. Preuzimanja s vaše IP adrese u posljednja 24 sata: %(count)s. 🚀 Da biste dobili brža preuzimanja i preskočili provjere preglednika, <a %(a_membership)s>postanite član</a>. Preuzmi s partnerske web stranice Slobodno nastavite pregledavati Anninu Arhivu u drugoj kartici dok čekate (ako vaš preglednik podržava osvježavanje kartica u pozadini). Slobodno pričekajte da se učita više stranica za preuzimanje istovremeno (ali molimo preuzimajte samo jednu datoteku istovremeno po poslužitelju). Jednom kada dobijete poveznicu za preuzimanje, ona vrijedi nekoliko sati. Hvala što čekate, to omogućuje da web stranica ostane besplatna za sve! 😊 <a %(a_main)s>&lt; Sve poveznice za preuzimanje ove datoteke</a> ❌ Spora preuzimanja nisu dostupna putem Cloudflare VPN-ova ili s Cloudflare IP adresa. ❌ Spora preuzimanja dostupna su samo putem službene web stranice. Posjetite %(websites)s. <a %(a_download)s>📚 Preuzmite sada</a> Kako bismo svima omogućili priliku za besplatno preuzimanje datoteka, trebate pričekati prije nego što možete preuzeti ovu datoteku. Molimo pričekajte <span %(span_countdown)s>%(wait_seconds)s</span> sekundi za preuzimanje ove datoteke. Upozorenje: bilo je mnogo preuzimanja s vaše IP adrese u posljednja 24 sata. Preuzimanja mogu biti sporija nego inače. Ako koristite VPN, dijeljenu internetsku vezu ili vaš ISP dijeli IP adrese, ovo upozorenje može biti zbog toga. Spremi ❌ Nešto je pošlo po zlu. Molimo pokušajte ponovno. ✅ Spremljeno. Molimo osvježite stranicu. Promijenite svoje prikazno ime. Vaš identifikator (dio nakon “#”) ne može se promijeniti. Profil kreiran <span %(span_time)s>%(time)s</span> uredi Popisi Kreirajte novi popis pronalaskom datoteke i otvaranjem kartice “Popisi”. Još nema popisa Profil nije pronađen. Profil Trenutno ne možemo primati zahtjeve za knjige. Nemojte nam slati email s vašim zahtjevima za knjige. Molimo vas da svoje zahtjeve postavite na Z-Library ili Libgen forumima. Datoteka u Anninoj Arhivi DOI: %(doi)s Preuzmi SciDB Nexus/STC Pregled još nije dostupan. Preuzmite datoteku s <a %(a_path)s>Anine Arhive</a>. Kako biste podržali dostupnost i dugoročno očuvanje ljudskog znanja, postanite <a %(a_donate)s>član</a>. Kao bonus, 🧬&nbsp;SciDB se učitava brže za članove, bez ikakvih ograničenja. Ne radi? Pokušajte <a %(a_refresh)s>osvježiti</a>. Sci-Hub Dodajte specifično polje za pretragu Pretraži opise i komentare metapodataka Godina objave Napredno Pristup Sadržaj Prikaz Popis Tablica Vrsta datoteke Jezik Poredaj po Najveći Najrelevantnije Najnovije (veličina datoteke) (otvoreni kod) (godina izdanja) Najstarije Nasumično Najmanji Izvor prikupljeno i otvoreno od strane AA Digitalno posuđivanje (%(count)s) Članci iz časopisa (%(count)s) Pronašli smo podudaranja u: %(in)s. Možete se pozvati na URL pronađen tamo kada <a %(a_request)s>zahtijevate datoteku</a>. Metapodaci (%(count)s) Za istraživanje indeksa pretraživanja po kodovima, koristite <a %(a_href)s>Istraživač kodova</a>. Indeks pretraživanja ažurira se mjesečno. Trenutno uključuje unose do %(last_data_refresh_date)s. Za više tehničkih informacija, pogledajte <a %(link_open_tag)s>stranicu skupova podataka</a>. Isključite Uključite samo Nije provjereno više… Sljedeće … Prethodno Ovaj indeks pretraživanja trenutno uključuje metapodatke iz knjižnice Kontroliranog digitalnog posudbe Internet Archivea. <a %(a_datasets)s>Više o našim datasetima</a>. Za više digitalnih knjižnica za posudbu, pogledajte <a %(a_wikipedia)s>Wikipediju</a> i <a %(a_mobileread)s>MobileRead Wiki</a>. Za DMCA / zahtjeve za autorska prava <a %(a_copyright)s>kliknite ovdje</a>. Vrijeme preuzimanja Pogreška tijekom pretraživanja. Pokušajte <a %(a_reload)s>ponovno učitati stranicu</a>. Ako problem potraje, molimo pošaljite nam e-mail na %(email)s. Brzo preuzimanje Zapravo, svatko može pomoći u očuvanju ovih datoteka dijeljenjem naše <a %(a_torrents)s>ujedinjene liste torrenta</a>. ➡️ Ponekad se to događa pogrešno kada je poslužitelj za pretraživanje spor. U takvim slučajevima, <a %(a_attrs)s>ponovno učitavanje</a> može pomoći. ❌ Ova datoteka može imati problema. Tražite radove? Ovaj indeks pretraživanja trenutno uključuje metapodatke iz raznih izvora metapodataka. <a %(a_datasets)s>Više o našim Datasets</a>. Postoji mnogo, mnogo izvora metapodataka za pisana djela diljem svijeta. <a %(a_wikipedia)s>Ova stranica Wikipedije</a> je dobar početak, ali ako znate za druge dobre liste, molimo vas da nam javite. Za metapodatke prikazujemo originalne datoteke. Ne spajamo datoteke. Trenutno imamo najopsežniji otvoreni katalog knjiga, radova i drugih pisanih djela na svijetu. Ogledamo Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>i više</a>. <span %(classname)s>Nema pronađenih datoteka.</span> Pokušajte s manje ili drugačijim pojmovima za pretraživanje i filtrima. Rezultati %(from)s-%(to)s (%(total)s ukupno) Ako pronađete druge "sjene knjižnice" koje bismo trebali zrcaliti, ili ako imate bilo kakva pitanja, molimo kontaktirajte nas na %(email)s. %(num)d djelomična podudaranja %(num)d+ djelomična podudaranja Upišite u okvir za pretraživanje datoteka u digitalnim knjižnicama za posudbu. Upišite u okvir za pretraživanje našeg kataloga %(count)s datoteka koje se mogu izravno preuzeti, koje <a %(a_preserve)s>čuvamo zauvijek</a>. Upišite u okvir za pretraživanje. Upišite u okvir za pretraživanje našeg kataloga %(count)s akademskih radova i članaka iz časopisa, koje <a %(a_preserve)s>čuvamo zauvijek</a>. Upišite u okvir za pretraživanje metapodataka iz knjižnica. Ovo može biti korisno kada <a %(a_request)s>tražite datoteku</a>. Savjet: koristite tipkovničke prečace “/” (fokus pretrage), “enter” (pretraga), “j” (gore), “k” (dolje), “<” (prethodna stranica), “>” (sljedeća stranica) za bržu navigaciju. Ovo su zapisi metapodataka, <span %(classname)s>ne</span> datoteke za preuzimanje. Postavke pretraživanja Pretraži Digitalno posuđivanje Preuzimanje Članci iz časopisa Metapodaci Novo pretraživanje %(search_input)s - Pretraži Pretraga je trajala predugo, što znači da biste mogli vidjeti netočne rezultate. Ponekad <a %(a_reload)s>ponovno učitavanje</a> stranice pomaže. Pretraga je trajala predugo, što je uobičajeno za široke upite. Broj filtera možda nije točan. Za velike prijenose (preko 10.000 datoteka) koji nisu prihvaćeni od strane Libgen ili Z-Library, molimo kontaktirajte nas na %(a_email)s. Za Libgen.li, prvo se prijavite na <a %(a_forum)s>njihov forum</a> s korisničkim imenom %(username)s i lozinkom %(password)s, a zatim se vratite na njihovu <a %(a_upload_page)s>stranicu za prijenos</a>. Za sada predlažemo učitavanje novih knjiga na Library Genesis forkove. Ovdje je <a %(a_guide)s>koristan vodič</a>. Imajte na umu da oba forka koja indeksiramo na ovoj web stranici povlače iz ovog istog sustava učitavanja. Za male prijenose (do 10.000 datoteka) molimo prenesite ih na oba %(first)s i %(second)s. Alternativno, možete ih učitati na Z-Library <a %(a_upload)s>ovdje</a>. Za učitavanje akademskih radova, molimo vas da ih (uz Library Genesis) učitate i na <a %(a_stc_nexus)s>STC Nexus</a>. Oni su najbolja sjena knjižnica za nove radove. Još ih nismo integrirali, ali hoćemo u nekom trenutku. Možete koristiti njihov <a %(a_telegram)s>bot za učitavanje na Telegramu</a>, ili kontaktirati adresu navedenu u njihovoj pričvršćenoj poruci ako imate previše datoteka za učitavanje na ovaj način. <span %(label)s>Intenzivan volonterski rad (nagrade od 50 do 5.000 USD):</span> ako možete posvetiti puno vremena i/ili resursa našoj misiji, voljeli bismo raditi bliže s vama. Na kraju možete postati dio unutarnjeg tima. Iako imamo ograničen proračun, možemo dodijeliti <span %(bold)s>💰 novčane nagrade</span> za najintenzivniji rad. <span %(label)s>Lagan volonterski rad:</span> ako možete odvojiti samo nekoliko sati tu i tamo, još uvijek postoji mnogo načina na koje možete pomoći. Dosljedne volontere nagrađujemo <span %(bold)s>🤝 članstvima u Anninoj Arhivi</span>. Annina Arhiva oslanja se na volontere poput vas. Pozdravljamo sve razine angažmana i tražimo dvije glavne kategorije pomoći: Ako niste u mogućnosti volontirati svoje vrijeme, još uvijek nam možete puno pomoći <a %(a_donate)s>doniranjem novca</a>, <a %(a_torrents)s>dijeljenjem naših torrenta</a>, <a %(a_uploading)s>učitavanjem knjiga</a> ili <a %(a_help)s>pričanjem prijateljima o Anninoj Arhivi</a>. <span %(bold)s>Tvrtke:</span> nudimo brzi izravan pristup našim zbirkama u zamjenu za donaciju na razini poduzeća ili zamjenu za nove zbirke (npr. nove skenove, OCR-irane datasete, obogaćivanje naših podataka). <a %(a_contact)s>Kontaktirajte nas</a> ako ste to vi. Pogledajte također našu <a %(a_llm)s>LLM stranicu</a>. Nagrade Uvijek tražimo ljude s čvrstim programerskim ili ofenzivnim sigurnosnim vještinama da se uključe. Možete napraviti ozbiljan doprinos očuvanju naslijeđa čovječanstva. Kao zahvalu, dajemo članstvo za solidne doprinose. Kao veliku zahvalu, dajemo novčane nagrade za posebno važne i teške zadatke. Ovo se ne bi trebalo smatrati zamjenom za posao, ali je dodatni poticaj i može pomoći s nastalim troškovima. Većina našeg koda je otvorenog koda, i tražit ćemo to i od vašeg koda kada dodjeljujemo nagradu. Postoje neke iznimke o kojima možemo razgovarati pojedinačno. Nagrade se dodjeljuju prvoj osobi koja završi zadatak. Slobodno komentirajte na kartici nagrade kako biste obavijestili druge da radite na nečemu, tako da drugi mogu pričekati ili vas kontaktirati za suradnju. No, budite svjesni da drugi i dalje mogu raditi na tome i pokušati vas preteći. Međutim, ne dodjeljujemo nagrade za loše obavljen posao. Ako su dvije visokokvalitetne prijave napravljene blizu jedna drugoj (unutar dan ili dva), možemo odlučiti dodijeliti nagrade objema, prema našem nahođenju, na primjer 100%% za prvu prijavu i 50%% za drugu prijavu (ukupno 150%%). Za veće nagrade (posebno nagrade za prikupljanje podataka), molimo kontaktirajte nas kada završite ~5%% toga i sigurni ste da će vaša metoda biti primjenjiva na cijeli cilj. Morat ćete podijeliti svoju metodu s nama kako bismo mogli dati povratne informacije. Također, na ovaj način možemo odlučiti što učiniti ako više ljudi bude blizu nagrade, kao što je potencijalno dodjeljivanje nagrade više osobama, poticanje ljudi da se udruže itd. UPOZORENJE: zadaci s visokim nagradama su <span %(bold)s>teški</span> — možda bi bilo mudro započeti s lakšima. Idite na naš <a %(a_gitlab)s>popis problema na Gitlabu</a> i sortirajte prema “Prioritet oznake”. Ovo otprilike pokazuje redoslijed zadataka koji su nam važni. Zadaci bez eksplicitnih nagrada i dalje su prihvatljivi za članstvo, posebno oni označeni kao “Prihvaćeno” i “Annin favorit”. Možda biste htjeli započeti s “Početničkim projektom”. Lagano volontiranje Sada također imamo sinkronizirani Matrix kanal na %(matrix)s. Ako imate nekoliko sati viška, možete pomoći na više načina. Obavezno se pridružite <a %(a_telegram)s>chatu volontera na Telegramu</a>. Kao znak zahvalnosti, obično dajemo 6 mjeseci “Sretni Knjižničar” za osnovne prekretnice, i više za kontinuirani volonterski rad. Sve prekretnice zahtijevaju visokokvalitetan rad — neuredan rad nas više šteti nego pomaže i odbit ćemo ga. Molimo <a %(a_contact)s>pošaljite nam email</a> kada postignete prekretnicu. %(links)s poveznice ili snimke zaslona zahtjeva koje ste ispunili. Ispunjavanje zahtjeva za knjige (ili radove, itd.) na forumima Z-Library ili Library Genesis. Nemamo vlastiti sustav za zahtjeve knjiga, ali preslikavamo te knjižnice, pa ih poboljšavajući, poboljšavamo i Anninu Arhivu. Prekretnica Zadatak Ovisi o zadatku. Mali zadaci objavljeni u našem <a %(a_telegram)s>volonterskom chatu na Telegramu</a>. Obično za članstvo, ponekad za male nagrade. Mali zadaci objavljeni u našoj grupi za razgovor volontera. Obavezno ostavite komentar na probleme koje riješite, kako drugi ne bi duplicirali vaš rad. %(links)s poveznica zapisa koje ste poboljšali. Možete koristiti <a %(a_list)s>popis nasumičnih problema s metapodacima</a> kao početnu točku. Poboljšajte metapodatke <a %(a_metadata)s>povezivanjem</a> s Open Library. Ovo bi trebalo pokazati kako nekome govorite o Anninoj Arhivi, a oni vam zahvaljuju. %(links)s poveznice ili snimke zaslona. Širenje vijesti o Anninoj Arhivi. Na primjer, preporučivanjem knjiga na AA, povezivanjem na naše blog objave ili općenito usmjeravanjem ljudi na našu web stranicu. Potpuno prevedite jezik (ako već nije bio blizu završetka.) <a %(a_translate)s>Prevođenje</a> web stranice. Poveznica za uređivanje povijesti koja pokazuje da ste značajno doprinijeli. Poboljšajte Wikipedijsku stranicu za Anninu Arhivu na vašem jeziku. Uključite informacije s Wikipedijine stranice AA na drugim jezicima, kao i s naše web stranice i bloga. Dodajte reference na AA na drugim relevantnim stranicama. Volontiranje i nagrade 