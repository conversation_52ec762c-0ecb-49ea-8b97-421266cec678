��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  �d a  6g ,   �h   �h 	  �i �  �k t  �m )  Do Y   np r   �p F   ;q j   �q �  �q K  �s 
  �t �  �u   �w R  �x    ${ �  %} �    �  � �  ׁ E   �� �   у V   �� "  � $   %� @  J� F   �� &   ҉    ��    � =   ,�    j�    �� 0   ��    Ǌ "   �    � L   � P  h�   �� g   Ս 6   =�    t�    ��    ��    ��    ��    ̎ 	   ݎ    �    ��    ��    �    %� 	   ;� 
   E�    S�    _�    ~�    �� n  �� v  ,� �   �� ,   1� M  ^� �   ��   [� �   x� #   B� �   f� #   � �   )� (   �� >  �     \� �  }� ?   	� e   I�    �� n  �� U  #� �  y� I  E�    �� 9   �� b   ڣ �   =� -    � +   .� z   Z� >   ե M   � ;   b�    �� z   �� �  � 6   � x   � �   �� O   a� )  �� M   ۫   )� �   2� �   �� ]   h� �   Ʈ �   �� F   5�   |� �   �� {   W� A   Ӳ �   �    �� �   �� )  _� F   ��    е �   ݵ �   �� 	   k� �  u� �   �� 	  �� �  �� �   ?� 2  $�   W� J   l� [   �� Z   � t   n� W   �� b   ;� -   �� �   �� f   ^� T   ��    � ~   *� `   �� �   
� �   ��    c�   ~�   �� S  ��   �� �   � �   �� /  � �   �� �   \� �   �� i  �� �   �� �   ��    �� �   �� �   :� �   � j  �� �   A� {   �� �   ?� �  �� �   �� #  4�   X� #   v� g   �� �   � b   �� :  �� �   -� _   �� t   8� (   �� B  �� �   � �  ��    s� j   �� �   �� �   ��   |� �   �� �  q� �  S� '   �� w  � �  �� 0  8� �  i�   �   %� e   8� �   ��    !� �   )� @  �� �  3� 
  ��   �� �   ��    �  t  �  `   �   x    Y �   b '  
 �   5 &   �   >	 V   
    k
 k  x
 i  �    N
    `
   a 7  � �   � �  � Y   R ?   � 	  � 3   � 	   * O  4 �   � P   *   � F   �   B e   N }   � ]   2 p  � (       * �  = �   �  �   �! I   " �   g" #   �"   #   $$    *% 
   7% .   B% 5   q% 1   �% 4   �% Q   & d   `&    �& +   �& 8   ' V   ?' "   �' ;   �' 0   �' v   &(    �(    �( #  �( <  �) �   + �   	, �   �, �   �- 6  ^.    �/ �   �/ �   �0 �   11 S  �1 �  *3   �4 �  �6 �  �8 F    : 4   g:     �: �   �:    o; �  p= D  �> �  D@ :  �A c   C   dE ,   yF +  �F 6  �G Y  	I    cJ �   rJ 6  ZK �   �L S  M �   lN 	   [O    eO a   �O    �O �  �O �  |R ]  T �   wV �   W     X �   X �   �X F   �Y q   )Z �   �Z   �[ �   �\ �  +] e   �^   _    .` �  C` �   =b �  �b �  �d �  Kg �   ;i    j   j �  "k    �l [  �l �  ;n L  
p 
  Wr �   et 9  \u F   �v �  �v �   �x    �y :   $z [   _z 
   �z 8   �z     �z -    { '   N{    v{ �   �{   ,| �  / &  � �   F�    �     � V  !� d  x� �  ݊ �  s� �  ��    ��   ��    ��    �� �  Ē $  u� ?  �� u  ژ    P�   _� �  p�   U� 2  u�   �� b  �� �  �    Ϊ �   � Z   �� �   � �  �� �   ǰ �   �� �   � h  ��    � �   3� B   ߴ ^   "�    �� +   �� :   ǵ �  � u  �� �  �� 9   �� �  �� �   ��    G� �   N� A   � �   R� �   �� .   �� c   ��    G� 1  [� N  �� �  �� �  �� ~   4�    �� c   �� 
  &�    4� �   :� �  �� �   �� ;   l� +   �� B  �� ,   � �  D� �  �� �  ��     Y� p   z� �   �� {  {� �  ��    �� �   �� 2   �� �   �� �   �� %  `� �   �� R   W� ,   �� �   �� 6   �� �  �� a  �� �   �   �� ^   
� ;   i� �   �� !  R� �  t� �  
� �  �� 4   �� �   �   � H  � �   W� �  � �  �� ,   �� �  �� (   �� �  �� Y   ��    �� 2  � ^  K �  �   H   J "  Z Y  }	 �   �   �
 �  � �   Y �  � e  � =   , �   j �   �    x    � ?  � �   � 8   r   � �  � "  � �  � �  � �   k! �  " 6  �#     % �   /%    �% p  �% #   o'    �'    �'    �' 9   �'    
(    !(    6(    K(    R(    e( 	   r( 	   |(    �( !   �(    �( 1   �(    �(    �(    �(    ) �   
) \   �) "   ,* 
   O* ,   Z* '   �* 7   �* 7   �* !   + 	   A+    K+    R+    k+    �+    �+ 
   �+    �+ 
   �+    �+ ;   , )   ?, "   i, 1   �, /   �, 5   �, *   $-    O- %   `- H   �- $   �- X   �- 7   M.    �. X   �. P   �.    4/    P/ !   a/    �/    �/    �/    �/    �/    �/    0    0    #0    =0 	   J0 
   T0    _0 %   b0    �0    �0 	   �0    �0 	   �0    �0    �0    �0 	   �0    �0    
1    1    51    =1    Z1    x1    �1 	   �1    �1 :   �1    �1    �1    �1    2    2    )2     12    R2    Y2    e2 b   |2 �   �2 a   �3 �    4 �  �4 �   �6    07 ,   A7    n7    v7    }7    �7 #   �7 '   �7 `   �7 5   V8 l   �8     �8 9   9 0   T9 m   �9 b   �9 �   V: S   ; :   r;    �;    �;    �;    �;    �; 
   <    <    )<    5< 
   H< 	   V< 
   `<    n<    �<    �<    �<    �<    �<    �<    �<    �<    =    =    =    3=   F=    V>    Z>    f>    l>    �> @   �> d   �> $   @? )   e? ;   �?    �?    �?    �? c   �?    B@    H@ 0   Y@ �   �@    ,A    HA �   YA %   �A F  
B R   TE �   �E �   <F �   �F <   �G   H �   I   �I �   �J    �K    �K K   �K :   L \   OL [   �L `   M N   iM _   �M "   N 9   ;N    uN    |N O   �N #   �N    O    O    O    O    8O    �O /   �O T   �O    LP    cP ^   xP h   �P �  @Q    �R    �R �   S    �S    �S    �S    �S    �S 0   T ]  6T    �U    �U    �U    �U   �U     �V 
   W    !W �   )W    �W    �W /   �W    �W    
X '   X �   :X    �X 
   �X S   �X !   GY    iY 	   zY    �Y -   �Y n   �Y 
   4Z 2   BZ �   uZ t   [ [   �[    �[    \ C   ]    b] 2   s]    �] �   �] �   �^ !   Z_ M   |_ �   �_ �   �`    ia !   �a    �a �  �a 7   lc #   �c    �c     �c "    d �   #d    �d    �d +   e @   .e    oe "   ve $   �e     �e H  �e �  (h /  �i =   �k 8   l    Rl 2   _l   �l �   �m �   �n    _o �   o �  lp    r $   ;r   `r D  t    �u    �u 7   �u    v =  $v    bw <  zw �  �x �   ez    L{ �   c{ &   �{ %   | i   2|   �|   �} �   �~ �  t o   � �   s� X   p� �   ɂ �   �� P   >� �   �� *   @� 0   k�    ��    ��    �� &   Ʌ    �� Q   � 2   a� 	   �� -   �� 5  ̆ &   � �   )� �   $� �   �� -   5� &   c�    ��    �� (   �� #   � *   � .   0� �   _� +   K� �   w�    O� �   d� }   � �   �� 9  (� �   b� +   &� �   R� 	   ؑ !  � ^   �    c� �  ��    P�    ]�    u�    �� -   ��    ѕ 	   ٕ J   � �   .� �   � 	   ��    ��    Ǘ �   � /  �� �   ֙ �   ��    �    *�    @�    T�    k�    �    �� B   �� -   ۛ �  	� ^   �    e� e   |� m   � I   P� h   �� M   � L   Q�    �� n   �� C   � �   W� L   ܡ F   )�    p� �   �� c   r� =   ֣ h   � X   }� =   ֤    � :   � q   X� �   ʥ 3   P� �   �� 	   2� 
  <� T   J� Y   �� �   ��    �� �  �� �   ��    f�    �    ��    �� �   �� *   �� j   ֭ �   A� �   � �   � �   �� �   d� �   � �   �� ]   h� 4  Ƴ g   �� �  c� �   � �   �� 
   �� 
   �� 
   �� �   �� 
   _� 
   m� K   {� \   ǹ �   $� 
   � m   !� ?   �� s   ϻ A   C� �   �� `   � 
   g� �   u� 
   � 
   � 
   '� G  5� �   }� �  �    ��    ��    �� �   ��    ��     ��   �� �   �� $   ��    ��    �� 9   �� :   � 2   R�    ��    �� �   ��    �� �  ��   Y� �   q� U   �� �   N�   ��   �� Y  �� �   V� �   � �   ��    +� l  @�    �� �  �� �   Y� '  :� �   b� F  �� �   8� �   �� w   �� 4   �� >   0�    o� (   �� 
   ��    ��    �� �   ��    �� l   �� '   1�    Y�    m�    v�    ~� �   �� F   � ;   b�   �� 8   ��    ��    ��    � (   !�    J�    \�    i�    r�    �    ��    ��    �� ,   �� �   ��    w�    ��    ��    ��    ��    ��    ��    ��    ��    � 6   %� i   \�    �� ;   �� b   � �   �� �   2� �   �� %  �� �   �� \  �� 5   � r   8� ;   �� V   �� Q   >� �   �� 0  H� o   y� o   ��    Y� n   n� �   �� �   o�    �     �    ?�    X�    u� )   ��    �� +   ��    ��    ��    �    $�    @� !   ^�    ��    ��    ��    ��    ��    ��    �� +   �� /   � �   M� �   ��    V� '   v� u   �� r   � ~   �� 5   � (   <� 4   e� <   �� L   �� 0   $� �   U�   +� -  7�    e� f   �� �   �� '   ��   �� �   �� �   �� H   [�    �� �   �� �   p� �   � .   �� R   �� �        �  >   �  '   < U   d u   � d   0    �    �    �    � �   � >   ~ '   � \   � &   B ,   i $   � U   � (    i   : R   � �   � L   � V   6 �   � B   O    �    �    � "   � #   	    9	 #   X	 <   |	 9   �	   �	 K   

 @   Y
 @   �
 @   �
 	    �   & �   � �   C �   � 
   � �   � (   Z    � �   �    � .   � I   � 7    d   T a   � R       n &   � �   � <   H    � [   � �   � S   � �   B {   � s   M �   � !   � Q   � �   �    { f   �    � �    4   � ;   �    7 "   R �   u K   m _   � �    m   � W     �   x �   �    �     �  R   �  K   �     ;! '   U!    }!    �!    �! +   �! �   �! r   q"    �"    �" '   # !   B# %   d# z   �# 0   $ �   6$ j   �$ 6   2% {   i% �   �% 9   �& U   �&    R' X   o' F   �' !   ( �   1( �   �( @   Z) X   �)    �) ;   * U   J*    �* h   �* (   +    D+ &   Y+ �   �+ �   , B   �,    (-    ?- B   V-    �- Z   �- @   . �   L. z   �.    b/ >   u/ �   �/    �0 �   �0 O   n1 +   �1 [   �1 �   F2    ;3    A3    C3    E3 )   ^3 7   �3 K   �3 "   4    /4 	   @4 "   J4 C   m4 >   �4 
   �4 N   �4 H   J5    �5    �5 @   �5    �5 
   6 `   %6 �   �6 E   �7    �7    �7 �   �7 L  �8 Z   �9    B: �  Q: �  �; )   �= l   �= #   6> Q  Z> )   �@ q   �@    HA    ZA �  gA    TC V   lC z   �C l   >D ^   �D    
E a   )E <   �E    �E u   �E B   ]F +   �F W   �F <   $G Q   aG �   �G �   9H %   �H �   
I ~  �I �   HK 2   L E  >L �   �M �   kN R  WO �   �P #   ^Q    �Q �   �Q 0   NR �  R f   yT I   �T    *U    =U �  \U    W �   W 2  �W !  Y 3  =Z C   q[ E   �[ k   �[ .   g\ 6   �\ R   �\ m    ]    �] $   �] ;   �]    �]    ^ ?   3^ i   s^ 0   �^    _ w   _ �   �_ �   �`    9a    Va    ia T   pa Z   �a S    b �   tb z   Gc    �c '   �c �   �c 
   �d �   �d �  �e �   eh [   ai *   �i    �i    �i    �i G   �i 0   Bj �   sj �   k P   l    Ul    hl %   {l    �l L   �l 
   
m ;   m    Wm )   ^m     �m    �m    �m k   �m    .n    4n (   Gn    pn    �n q   �n �   o q   �o q   Up X   �p �    q    r    r �   $r �   �r �   �s 	   Xt    bt N   �t I   1u i   {u g   �u j   Mv    �v ^   �v    -w    @w    Rw    gw    �w    �w    �w    �w 
   �w    �w )   �w (   
x )   6x "   `x 1   �x -   �x "   �x    y    #y B   0y 2   sy    �y 6   �y +   �y Z   z )   mz    �z    �z %   �z    �z    { �   #{ W   �{ t   �{ 
  t|    }    �}    �}    �} 6   �} 	   ~    ~    6~ r   Q~    �~ "   �~     
   
 	    6   "    Y �   s    b�    w�    �� "   �� !   Ӏ    �� "   � $   1� (   V� &   � )   ��    Ё d   ׁ %   <�    b�    ~� 2   �� R       � +   4�    `� w   � W   �� P   O�    ��    �� 	   ��    ˄    �    �� �  � x   ��    �    -� *   1�    \� ,   p�    ��    �� r   �� >   � �   ]�    /� #   ?� �   c� #   �     � "   2� %   U� )   {� &   ��    ̊    � 9   ��    0�    L� /   l� 5  �� J   Ҍ @   � ^   ^�    �� �   ֍ #   j�    �� M   �� 
   � (   ��    "� >   <� J   {� %   Ə �   �    ��    ��    Ð    �    ��    � $   &�    K�    \� Z   r� �   ͑    ʒ    � �   	� �   ��    �� "   ��    ۔    �    ��    �    *�    C�    X�    j�    }�    �� 
   ��    ��    ��    ̕    ٕ    � +   �� �   !�   � y  �   l� �  r� �   $� ~  �    ��   ��    ��   �� �   �� �  �� �   �� p  \� 7   ͦ �   � C   ��    �� O   � `   _� z   �� t   ;� �   �� �   f� �    � X  ҫ    +� �   E� �   � k   �� �   d�    � �   � |  � �   �� �   [�    -� Z   :� a   �� �   �� �   ׶ b   `� �   ÷ 
   L� "   Z�    }� K   �� 4   �    � �   ,� J   ù |   �    �� �   �� �   3� A   Ȼ c   
� L   n� ^   �� t   � C   �� v   ӽ `   J� �   �� �   1�    ޿ )   �     � m   0� 4   ��    ��    �� N   �� 	   /�    9�    P� 9   W� .   �� <   ��    ��    � 
   $�    /� 	   5� ?   ?� q   � W   �� 8   I�    �� !   �� (   ��    ��    ��    ��    �� 	   ��    �    � 	   �    �    &�    -�    B� 	   V�    `� 
   p�    ~� 
   ��    ��    �� 	   �� 4   ��    �� !   
� �   /�    �� S   �� �   (�    ��    �� 
   � 
   � 	   $�    .� 
   2� �   =� {   �� R   Q�    ��    �� �   ��    T� �   g� �   �� .   ��    �� p   �� �   R� F   � �   R� �   � ,   �� �   �� (   d� )   �� 1   �� �   ��    p� �   �� r   � �   �� L   $�    q�    �� 
   �� 
   ��    ��    ��    ��    �� �   ��    x� �   �� �   ��   u� p   �� L   �� �  H� �  � �   �� �   �� %  T� j  z�    �� �   �� -  �� �   �� �  �� �  f� }   X� v  ��    M� K   _� �   �� �  ;� ?   �� �   �     �    �    � �   +� E   �� m   � /   s� \   �� C    � S   D� "   �� �   �� K   ~� 8   �� K   � �   O�    ,�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: pl
Language-Team: pl <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library to popularna (i nielegalna) biblioteka. Przejęli kolekcję Library Genesis i uczynili ją łatwo przeszukiwalną. Ponadto, stali się bardzo skuteczni w pozyskiwaniu nowych książek, zachęcając użytkowników do ich dodawania różnymi korzyściami. Obecnie nie przekazują tych nowych książek z powrotem do Library Genesis. I w przeciwieństwie do Library Genesis, nie umożliwiają łatwego tworzenia mirrorów swojej kolekcji, co uniemożliwia szerokie przechowywanie. Jest to ważne dla ich modelu biznesowego, ponieważ pobierają opłaty za dostęp do swojej kolekcji w dużych ilościach (więcej niż 10 książek dziennie). Nie oceniamy moralnie pobierania opłat za dostęp do nielegalnej kolekcji książek w dużych ilościach. Nie ma wątpliwości, że Z-Library odniosła sukces w rozszerzaniu dostępu do wiedzy i pozyskiwaniu większej liczby książek. Jesteśmy tutaj po to, aby zrobić swoją część: zapewnić długoterminowe przechowywanie tej prywatnej kolekcji. - Anna i zespół (<a %(reddit)s>Reddit</a>) W oryginalnym wydaniu Lustrzanej Biblioteki Piratów (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>), stworzyliśmy mirror Z-Library, dużej nielegalnej kolekcji książek. Przypominamy, co napisaliśmy w tym oryginalnym wpisie na blogu: Ta kolekcja pochodziła z połowy 2021 roku. W międzyczasie Z-Library rozwijała się w zawrotnym tempie: dodali około 3,8 miliona nowych książek. Oczywiście, są tam pewne duplikaty, ale większość z nich wydaje się być rzeczywiście nowymi książkami lub lepszej jakości skanami wcześniej przesłanych książek. Jest to w dużej mierze zasługa zwiększonej liczby moderatorów-wolontariuszy w Z-Library i ich systemu masowego przesyłania z deduplikacją. Chcielibyśmy pogratulować im tych osiągnięć. Z przyjemnością ogłaszamy, że zdobyliśmy wszystkie książki, które zostały dodane do Z-Library między naszym ostatnim mirror a sierpniem 2022. Cofnęliśmy się również i zeskrobaliśmy niektóre książki, które przegapiliśmy za pierwszym razem. W sumie, ta nowa kolekcja ma około 24TB, co jest znacznie większe niż poprzednia (7TB). Nasz mirror ma teraz łącznie 31TB. Ponownie, deduplikowaliśmy w stosunku do Library Genesis, ponieważ już są dostępne torrenty dla tej kolekcji. Proszę przejść do Pirate Library Mirror, aby sprawdzić nową kolekcję (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>). Tam znajdziesz więcej informacji o tym, jak są zorganizowane pliki i co się zmieniło od ostatniego razu. Nie podamy do niego linku stąd, ponieważ to tylko blog, który nie hostuje żadnych nielegalnych materiałów. Oczywiście, seedowanie to także świetny sposób, aby nam pomóc. Dziękujemy wszystkim, którzy seedują nasz poprzedni zestaw torrentów. Jesteśmy wdzięczni za pozytywną reakcję i cieszymy się, że jest tak wiele osób, które dbają o zachowanie wiedzy i kultury w ten nietypowy sposób. 3x nowe książki dodane do Lustrzanej Biblioteki Piratów (+24TB, 3,8 miliona książek) Przeczytaj towarzyszące artykuły TorrentFreak: <a %(torrentfreak)s>pierwszy</a>, <a %(torrentfreak_2)s>drugi</a> - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artykuły towarzyszące od TorrentFreak: <a %(torrentfreak)s>pierwszy</a>, <a %(torrentfreak_2)s>drugi</a> Niedawno „biblioteki cieni” były na wymarciu. Sci-Hub, ogromne nielegalne archiwum prac naukowych, przestało przyjmować nowe prace z powodu pozwów sądowych. „Z-Library”, największa nielegalna biblioteka książek, widziała, jak jej rzekomi twórcy zostali aresztowani pod zarzutem naruszenia praw autorskich. Niesamowicie udało im się uciec z aresztu, ale ich biblioteka jest nadal zagrożona. Niektóre kraje już wprowadzają wersję tego. TorrentFreak <a %(torrentfreak)s>doniósł</a>, że Chiny i Japonia wprowadziły wyjątki AI do swoich praw autorskich. Nie jest dla nas jasne, jak to współgra z międzynarodowymi traktatami, ale z pewnością daje to ochronę ich krajowym firmom, co wyjaśnia to, co widzieliśmy. Jeśli chodzi o Archiwum Anny — będziemy kontynuować naszą podziemną pracę zakorzenioną w moralnym przekonaniu. Jednak naszym największym pragnieniem jest wyjście na światło dzienne i legalne zwiększenie naszego wpływu. Prosimy o reformę praw autorskich. Kiedy Z-Library stanęła w obliczu zamknięcia, już wcześniej zrobiłem kopię zapasową całej jej biblioteki i szukałem platformy, aby ją umieścić. To była moja motywacja do założenia Archiwum Anny: kontynuacja misji stojącej za wcześniejszymi inicjatywami. Od tego czasu staliśmy się największą biblioteką cieni na świecie, goszcząc ponad 140 milionów chronionych prawem autorskim tekstów w różnych formatach — książki, prace naukowe, czasopisma, gazety i inne. Mój zespół i ja jesteśmy ideologami. Wierzymy, że zachowanie i udostępnianie tych plików jest moralnie słuszne. Biblioteki na całym świecie doświadczają cięć w finansowaniu, a nie możemy również powierzyć dziedzictwa ludzkości korporacjom. Potem pojawiła się sztuczna inteligencja. Praktycznie wszystkie duże firmy budujące LLM skontaktowały się z nami, aby szkolić się na naszych danych. Większość (ale nie wszystkie!) firm z USA zrewidowała swoje podejście, gdy zdała sobie sprawę z nielegalnego charakteru naszej pracy. W przeciwieństwie do nich, chińskie firmy z entuzjazmem przyjęły naszą kolekcję, najwyraźniej nie martwiąc się jej legalnością. Jest to godne uwagi, biorąc pod uwagę rolę Chin jako sygnatariusza niemal wszystkich głównych międzynarodowych traktatów dotyczących praw autorskich. Udostępniliśmy szybki dostęp około 30 firmom. Większość z nich to firmy LLM, a niektóre to brokerzy danych, którzy będą odsprzedawać naszą kolekcję. Większość z nich pochodzi z Chin, ale współpracowaliśmy także z firmami z USA, Europy, Rosji, Korei Południowej i Japonii. DeepSeek <a %(arxiv)s>przyznał</a>, że wcześniejsza wersja była trenowana na części naszej kolekcji, choć są powściągliwi w kwestii najnowszego modelu (prawdopodobnie również trenowanego na naszych danych). Jeśli Zachód chce pozostać na czele wyścigu LLM, a ostatecznie AGI, musi szybko przemyśleć swoje stanowisko w sprawie praw autorskich. Niezależnie od tego, czy zgadzasz się z nami w kwestii moralnej, czy nie, staje się to teraz kwestią ekonomiczną, a nawet bezpieczeństwa narodowego. Wszystkie bloki sił budują sztucznych supernaukowców, superhakerów i superarmie. Wolność informacji staje się kwestią przetrwania dla tych krajów — nawet kwestią bezpieczeństwa narodowego. Nasz zespół pochodzi z całego świata i nie mamy konkretnego ukierunkowania. Ale zachęcamy kraje z silnymi prawami autorskimi do wykorzystania tego egzystencjalnego zagrożenia do ich reformy. Co więc zrobić? Nasza pierwsza rekomendacja jest prosta: skrócić okres ochrony praw autorskich. W USA prawa autorskie są przyznawane na 70 lat po śmierci autora. To absurdalne. Możemy to dostosować do patentów, które są przyznawane na 20 lat po złożeniu. To powinno być więcej niż wystarczający czas, aby autorzy książek, artykułów, muzyki, sztuki i innych dzieł twórczych zostali w pełni wynagrodzeni za swoje wysiłki (w tym długoterminowe projekty, takie jak adaptacje filmowe). Następnie, co najmniej, decydenci powinni uwzględnić wyjątki dla masowego zachowania i rozpowszechniania tekstów. Jeśli głównym zmartwieniem jest utrata przychodów od indywidualnych klientów, dystrybucja na poziomie osobistym mogłaby pozostać zabroniona. Z kolei ci, którzy są w stanie zarządzać ogromnymi repozytoriami — firmy szkolące LLM, wraz z bibliotekami i innymi archiwami — byliby objęci tymi wyjątkami. Reforma praw autorskich jest konieczna dla bezpieczeństwa narodowego TL;DR: Chińskie LLM (w tym DeepSeek) są szkolone na moim nielegalnym archiwum książek i artykułów — największym na świecie. Zachód musi zreformować prawo autorskie jako kwestię bezpieczeństwa narodowego. Proszę zobaczyć <a %(all_isbns)s>oryginalny wpis na blogu</a> po więcej informacji. Wydaliśmy wyzwanie, aby to ulepszyć. Przyznalibyśmy nagrodę za pierwsze miejsce w wysokości 6 000 USD, za drugie miejsce 3 000 USD, a za trzecie miejsce 1 000 USD. Ze względu na ogromną liczbę odpowiedzi i niesamowite zgłoszenia, postanowiliśmy nieco zwiększyć pulę nagród i przyznać cztery nagrody za trzecie miejsce po 500 USD każda. Zwycięzcy są poniżej, ale koniecznie zapoznaj się ze wszystkimi zgłoszeniami <a %(annas_archive)s>tutaj</a> lub pobierz nasz <a %(a_2025_01_isbn_visualization_files)s>łączony torrent</a>. Pierwsze miejsce 6 000 USD: phiresky To <a %(phiresky_github)s>zgłoszenie</a> (<a %(annas_archive_note_2951)s>komentarz na Gitlab</a>) to po prostu wszystko, czego chcieliśmy, i więcej! Szczególnie podobały nam się niesamowicie elastyczne opcje wizualizacji (nawet z obsługą niestandardowych shaderów), ale z obszerną listą ustawień wstępnych. Podobało nam się również, jak szybkie i płynne jest wszystko, prosta implementacja (która nawet nie ma backendu), sprytna minimapa i obszerne wyjaśnienie w ich <a %(phiresky_github)s>poście na blogu</a>. Niesamowita praca i zasłużone zwycięstwo! - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nasze serca są pełne wdzięczności. Zauważalne pomysły Drapacze chmur dla rzadkości Wiele suwaków do porównywania datasets, jakbyś był DJ-em. Skala z liczbą książek. Ładne etykiety. Fajny domyślny schemat kolorów i mapa cieplna. Unikalny widok mapy i filtry Adnotacje oraz statystyki na żywo Statystyki na żywo Kilka innych pomysłów i wdrożeń, które szczególnie nam się podobały: Moglibyśmy kontynuować jeszcze długo, ale zatrzymajmy się tutaj. Koniecznie zobacz wszystkie prace <a %(annas_archive)s>tutaj</a>, lub pobierz nasz <a %(a_2025_01_isbn_visualization_files)s>połączony torrent</a>. Tak wiele prac, a każda z nich wnosi unikalną perspektywę, czy to w interfejsie użytkownika, czy w implementacji. Przynajmniej włączymy zgłoszenie z pierwszego miejsca do naszej głównej strony, a być może także inne. Zaczęliśmy również myśleć o tym, jak zorganizować proces identyfikacji, potwierdzania, a następnie archiwizacji najrzadszych książek. Więcej informacji wkrótce. Dziękujemy wszystkim, którzy wzięli udział. To niesamowite, że tak wiele osób się tym przejmuje. Łatwe przełączanie datasets do szybkich porównań. Wszystkie ISBN CADAL SSNOs Wycieki danych CERLALC DuXiu SSIDs Indeks eBooków EBSCOhost Google Książki Goodreads Internet Archive ISBNdb Globalny Rejestr Wydawców ISBN Libby Pliki w Archiwum Anny Nexus/STC OCLC/Worldcat OpenLibrary Rosyjska Biblioteka Państwowa Imperialna Biblioteka Trantora Drugie miejsce 3 000 USD: hypha „Podczas gdy idealne kwadraty i prostokąty są matematycznie przyjemne, nie zapewniają lepszej lokalności w kontekście mapowania. Uważam, że asymetria inherentna w tych krzywych Hilberta lub klasycznych Mortona nie jest wadą, lecz cechą. Podobnie jak słynny kształt buta Włoch sprawia, że jest on natychmiast rozpoznawalny na mapie, unikalne „dziwactwa” tych krzywych mogą służyć jako poznawcze punkty orientacyjne. Ta wyjątkowość może poprawić pamięć przestrzenną i pomóc użytkownikom w orientacji, potencjalnie ułatwiając lokalizację konkretnych regionów lub dostrzeganie wzorców.” Kolejne niesamowite <a %(annas_archive_note_2913)s>zgłoszenie</a>. Nie tak elastyczne jak pierwsze miejsce, ale faktycznie woleliśmy jego wizualizację na poziomie makro od pierwszego miejsca (krzywa wypełniająca przestrzeń, granice, etykietowanie, podświetlanie, przesuwanie i powiększanie). <a %(annas_archive_note_2971)s>Komentarz</a> Joe Davisa rezonował z nami: I wciąż wiele opcji wizualizacji i renderowania, a także niesamowicie płynny i intuicyjny interfejs użytkownika. Solidne drugie miejsce! - Anna i zespół (<a %(reddit)s>Reddit</a>) Kilka miesięcy temu ogłosiliśmy <a %(all_isbns)s>nagrodę o wartości 10 000 USD</a> za stworzenie najlepszej możliwej wizualizacji naszych danych pokazujących przestrzeń ISBN. Podkreśliliśmy pokazanie, które pliki już zarchiwizowaliśmy, a później zestaw danych opisujący, ile bibliotek posiada ISBN (miara rzadkości). Jesteśmy przytłoczeni odpowiedzią. Było tak wiele kreatywności. Wielkie podziękowania dla wszystkich, którzy wzięli udział: wasza energia i entuzjazm są zaraźliwe! Ostatecznie chcieliśmy odpowiedzieć na następujące pytania: <strong>które książki istnieją na świecie, ile już zarchiwizowaliśmy i na których książkach powinniśmy się skupić następnie?</strong> Wspaniale jest widzieć, że tak wiele osób troszczy się o te pytania. Zaczęliśmy od podstawowej wizualizacji sami. W mniej niż 300kb, ten obraz zwięźle przedstawia największą w pełni otwartą „listę książek” kiedykolwiek zgromadzoną w historii ludzkości: Trzecie miejsce 500 USD #1: maxlion W tym <a %(annas_archive_note_2940)s>zgłoszeniu</a> naprawdę podobały nam się różne rodzaje widoków, w szczególności widoki porównawcze i wydawców. Trzecie miejsce 500 USD #2: abetusk Chociaż nie jest to najbardziej dopracowany interfejs użytkownika, to <a %(annas_archive_note_2917)s>zgłoszenie</a> spełnia wiele wymagań. Szczególnie podobała nam się jego funkcja porównawcza. Trzecie miejsce 500 USD #3: conundrumer0 Podobnie jak pierwsze miejsce, ta <a %(annas_archive_note_2975)s>praca</a> zaimponowała nam swoją elastycznością. Ostatecznie to właśnie czyni świetne narzędzie do wizualizacji: maksymalna elastyczność dla zaawansowanych użytkowników, przy jednoczesnym zachowaniu prostoty dla przeciętnych użytkowników. Trzecie miejsce $500 #4: charelf Ostatnia <a %(annas_archive_note_2947)s>praca</a>, która otrzymała nagrodę, jest dość podstawowa, ale ma kilka unikalnych cech, które naprawdę nam się podobały. Podobało nam się, jak pokazują, ile datasets obejmuje konkretny ISBN jako miarę popularności/wiarygodności. Bardzo podobała nam się również prostota, ale skuteczność użycia suwaka przezroczystości do porównań. Zwycięzcy nagrody za wizualizację ISBN o wartości 10 000 USD TL;DR: Otrzymaliśmy niesamowite zgłoszenia do nagrody za wizualizację ISBN o wartości 10 000 USD. Tło Jak Archiwum Anny może osiągnąć swoją misję tworzenia kopii zapasowych całej wiedzy ludzkości, nie wiedząc, które książki wciąż istnieją? Potrzebujemy listy zadań do wykonania. Jednym ze sposobów na jej stworzenie jest wykorzystanie numerów ISBN, które od lat 70. XX wieku są przypisywane każdej opublikowanej książce (w większości krajów). Nie ma centralnej władzy, która zna wszystkie przypisania ISBN. Zamiast tego jest to system rozproszony, w którym kraje otrzymują zakresy numerów, które następnie przypisują mniejsze zakresy głównym wydawcom, którzy mogą dalej dzielić zakresy na mniejszych wydawców. Ostatecznie indywidualne numery są przypisywane książkom. Zaczęliśmy mapować ISBN-y <a %(blog)s>dwa lata temu</a> z naszym zbiorem danych z ISBNdb. Od tego czasu zebraliśmy wiele więcej źródeł metadata, takich jak <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby i inne. Pełna lista znajduje się na stronach „Datasets” i „Torrents” w Archiwum Anny. Obecnie mamy zdecydowanie największą w pełni otwartą, łatwo dostępną kolekcję metadata książek (a tym samym ISBN-ów) na świecie. Napisaliśmy <a %(blog)s>obszernie</a> o tym, dlaczego zależy nam na zachowaniu, i dlaczego obecnie znajdujemy się w krytycznym oknie czasowym. Musimy teraz zidentyfikować rzadkie, niedoceniane i unikalnie zagrożone książki i je zachować. Posiadanie dobrego metadata na temat wszystkich książek na świecie pomaga w tym. Nagroda 10 000 $ Duże znaczenie będzie miała użyteczność i estetyka. Pokaż rzeczywiste metadata dla pojedynczych ISBN podczas powiększania, takie jak tytuł i autor. Lepsza krzywa wypełniająca przestrzeń. Np. zygzak, idący od 0 do 4 w pierwszym rzędzie, a następnie z powrotem (w odwrotnym kierunku) od 5 do 9 w drugim rzędzie — stosowane rekursywnie. Różne lub konfigurowalne schematy kolorów. Specjalne widoki do porównywania Datasets. Sposoby debugowania problemów, takich jak inne metadata, które nie zgadzają się dobrze (np. znacznie różne tytuły). Annotowanie obrazów komentarzami na temat ISBN lub zakresów. Jakiekolwiek heurystyki do identyfikacji rzadkich lub zagrożonych książek. Jakiekolwiek kreatywne pomysły, które możesz wymyślić! Kod Kod do generowania tych obrazów, jak również inne przykłady, można znaleźć w <a %(annas_archive)s>tym katalogu</a>. Opracowaliśmy kompaktowy format danych, w którym wszystkie wymagane informacje ISBN zajmują około 75 MB (skompresowane). Opis formatu danych i kod do jego generowania można znaleźć <a %(annas_archive_l1244_1319)s>tutaj</a>. Nie musisz z tego korzystać, aby zdobyć nagrodę, ale jest to prawdopodobnie najwygodniejszy format na początek. Możesz przekształcać nasze metadata w dowolny sposób (choć cały twój kod musi być open source). Nie możemy się doczekać, co wymyślisz. Powodzenia! Sforkuj to repozytorium i edytuj ten post na blogu w HTML (nie są dozwolone inne backendy poza naszym backendem Flask). Spraw, aby powyższy obrazek był płynnie powiększany, tak aby można było powiększać aż do pojedynczych ISBN. Kliknięcie ISBN powinno przenosić na stronę z metadata lub wyszukiwanie w Archiwum Anny. Musisz nadal mieć możliwość przełączania się między różnymi Datasets. Zakresy krajów i wydawców powinny być podświetlane po najechaniu myszką. Możesz użyć np. <a %(github_xlcnd_isbnlib)s>data4info.py w isbnlib</a> dla informacji o krajach oraz naszego skryptu „isbngrp” dla wydawców (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Musi działać dobrze na komputerach stacjonarnych i urządzeniach mobilnych. Jest tu wiele do odkrycia, dlatego ogłaszamy nagrodę za ulepszenie wizualizacji powyżej. W przeciwieństwie do większości naszych nagród, ta jest ograniczona czasowo. Musisz <a %(annas_archive)s>przesłać</a> swój kod open source do 2025-01-31 (23:59 UTC). Najlepsze zgłoszenie otrzyma 6 000 $, drugie miejsce 3 000 $, a trzecie miejsce 1 000 $. Wszystkie nagrody zostaną przyznane w Monero (XMR). Poniżej znajdują się minimalne kryteria. Jeśli żadne zgłoszenie nie spełni kryteriów, możemy nadal przyznać nagrody, ale będzie to według naszego uznania. Dla dodatkowych punktów (to tylko pomysły — pozwól swojej kreatywności się rozwijać): MOŻESZ całkowicie odejść od minimalnych kryteriów i stworzyć zupełnie inną wizualizację. Jeśli będzie naprawdę spektakularna, to kwalifikuje się do nagrody, ale według naszego uznania. Zgłaszaj swoje prace, dodając komentarz do <a %(annas_archive)s>tego zgłoszenia</a> z linkiem do swojego rozwidlenia repozytorium, żądania scalenia lub różnicy. - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ten obrazek ma rozmiar 1000×800 pikseli. Każdy piksel reprezentuje 2 500 ISBN-ów. Jeśli mamy plik dla danego ISBN-u, piksel staje się bardziej zielony. Jeśli wiemy, że ISBN został wydany, ale nie mamy pasującego pliku, piksel staje się bardziej czerwony. W mniej niż 300 kb, ten obrazek zwięźle przedstawia największą w pełni otwartą „listę książek”, jaka kiedykolwiek została zebrana w historii ludzkości (kilkaset GB skompresowanych w całości). Pokazuje również: jest jeszcze wiele pracy do wykonania w zakresie tworzenia kopii zapasowych książek (mamy tylko 16%). Wizualizacja wszystkich ISBN — nagroda 10 000 USD do 2025-01-31 Ten obrazek przedstawia największą w pełni otwartą „listę książek”, jaka kiedykolwiek została zebrana w historii ludzkości. Wizualizacja Oprócz obrazu ogólnego, możemy również przyjrzeć się poszczególnym datasets, które zdobyliśmy. Użyj rozwijanego menu i przycisków, aby przełączać się między nimi. W tych obrazach można dostrzec wiele interesujących wzorców. Dlaczego występuje pewna regularność linii i bloków, która wydaje się występować na różnych skalach? Czym są puste obszary? Dlaczego niektóre datasets są tak skupione? Zostawimy te pytania jako ćwiczenie dla czytelnika. - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Podsumowanie Dzięki temu standardowi możemy wydawać wersje bardziej stopniowo i łatwiej dodawać nowe źródła danych. Mamy już kilka ekscytujących wydań w przygotowaniu! Mamy również nadzieję, że stanie się łatwiejsze dla innych bibliotek cieni do mirrorowania naszych kolekcji. W końcu naszym celem jest zachowanie ludzkiej wiedzy i kultury na zawsze, więc im więcej redundancji, tym lepiej. Przykład Przyjrzyjmy się naszemu ostatniemu wydaniu Z-Library jako przykład. Składa się ono z dwóch kolekcji: „<span style="background: #fffaa3">zlib3_records</span>” i „<span style="background: #ffd6fe">zlib3_files</span>”. Pozwala to na osobne zeskrobanie i wydanie rekordów metadata z rzeczywistych plików książek. W związku z tym wydaliśmy dwa torrenty z plikami metadata: Wydaliśmy również kilka torrentów z folderami danych binarnych, ale tylko dla kolekcji „<span style="background: #ffd6fe">zlib3_files</span>”, łącznie 62: Uruchamiając <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> możemy zobaczyć, co jest w środku: W tym przypadku jest to metadata książki zgłoszona przez Z-Library. Na najwyższym poziomie mamy tylko „aacid” i „metadata”, ale brak „data_folder”, ponieważ nie ma odpowiadających danych binarnych. AACID zawiera „22430000” jako główny identyfikator, który widzimy, że pochodzi z „zlibrary_id”. Możemy oczekiwać, że inne AAC w tej kolekcji będą miały tę samą strukturę. Teraz uruchommy <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: To jest znacznie mniejsze metadata AAC, chociaż większość tego AAC znajduje się gdzie indziej w pliku binarnym! W końcu mamy tym razem „data_folder”, więc możemy oczekiwać, że odpowiadające dane binarne będą znajdować się w <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata” zawiera „zlibrary_id”, więc możemy łatwo powiązać je z odpowiadającym AAC w kolekcji „zlib_records”. Moglibyśmy powiązać na wiele różnych sposobów, np. przez AACID — standard tego nie określa. Zauważ, że nie jest również konieczne, aby pole „metadata” samo w sobie było JSON-em. Może to być ciąg zawierający XML lub dowolny inny format danych. Można nawet przechowywać informacje o metadata w powiązanym blobie binarnym, np. jeśli jest to dużo danych. Heterogeniczne pliki i metadata, w formacie jak najbliższym oryginałowi. Dane binarne mogą być serwowane bezpośrednio przez serwery internetowe, takie jak Nginx. Heterogeniczne identyfikatory w bibliotekach źródłowych, a nawet brak identyfikatorów. Oddzielne wydania metadata w porównaniu do danych plikowych, lub wydania tylko metadata (np. nasze wydanie ISBNdb). Dystrybucja przez torrenty, choć z możliwością innych metod dystrybucji (np. IPFS). Niezmienne rekordy, ponieważ powinniśmy zakładać, że nasze torrenty będą istnieć wiecznie. Wydania inkrementalne / wydania dołączalne. Czytelne i zapisywalne przez maszyny, wygodnie i szybko, zwłaszcza dla naszego stosu (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Dość łatwa inspekcja przez ludzi, choć jest to drugorzędne w stosunku do czytelności maszynowej. Łatwe do seedowania naszych kolekcji za pomocą standardowego wynajętego seedboxa. Cele projektowe Nie zależy nam na tym, aby pliki były łatwe do nawigacji ręcznej na dysku lub przeszukiwalne bez wstępnego przetwarzania. Nie zależy nam na bezpośredniej kompatybilności z istniejącym oprogramowaniem bibliotecznym. Chociaż powinno być łatwo dla każdego seedować naszą kolekcję za pomocą torrentów, nie oczekujemy, że pliki będą użyteczne bez znacznej wiedzy technicznej i zaangażowania. Naszym głównym przypadkiem użycia jest dystrybucja plików i powiązanych metadata z różnych istniejących kolekcji. Nasze najważniejsze rozważania to: Niektóre cele nieistotne: Ponieważ Archiwum Anny jest open source, chcemy bezpośrednio używać naszego formatu. Kiedy odświeżamy nasz indeks wyszukiwania, uzyskujemy dostęp tylko do publicznie dostępnych ścieżek, aby każdy, kto forkuje naszą bibliotekę, mógł szybko zacząć działać. <strong>AAC.</strong> AAC (Kontener Archiwum Anny) to pojedynczy element składający się z <strong>metadata</strong> i opcjonalnie <strong>danych binarnych</strong>, z których oba są niezmienne. Posiada globalnie unikalny identyfikator, zwany <strong>AACID</strong>. <strong>AACID.</strong> Format AACID jest następujący: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Na przykład, rzeczywisty AACID, który wydaliśmy, to <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Zakres AACID.</strong> Ponieważ AACID zawierają monotonicznie rosnące znaczniki czasu, możemy użyć tego do oznaczania zakresów w obrębie konkretnej kolekcji. Używamy tego formatu: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, gdzie znaczniki czasu są włączone. Zakresy są ciągłe i mogą się nakładać, ale w przypadku nakładania muszą zawierać identyczne rekordy jak te wcześniej wydane w tej kolekcji (ponieważ AAC są niezmienne). Brakujące rekordy nie są dozwolone. <code>{collection}</code>: nazwa kolekcji, która może zawierać litery ASCII, cyfry i podkreślenia (ale bez podwójnych podkreśleń). <code>{collection-specific ID}</code>: identyfikator specyficzny dla kolekcji, jeśli dotyczy, np. ID Z-Library. Może być pominięty lub skrócony. Musi być pominięty lub skrócony, jeśli AACID przekroczyłby 150 znaków. <code>{ISO 8601 timestamp}</code>: krótka wersja ISO 8601, zawsze w UTC, np. <code>20220723T194746Z</code>. Ta liczba musi monotonicznie rosnąć przy każdym wydaniu, choć jej dokładna semantyka może się różnić w zależności od kolekcji. Sugerujemy użycie czasu skanowania lub generowania ID. <code>{shortuuid}</code>: UUID, ale skompresowany do ASCII, np. przy użyciu base57. Obecnie używamy biblioteki Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Folder danych binarnych.</strong> Folder z danymi binarnymi zakresu AAC, dla jednej konkretnej kolekcji. Mają one następujące właściwości: Katalog musi zawierać pliki danych dla wszystkich AAC w określonym zakresie. Każdy plik danych musi mieć AACID jako nazwę pliku (bez rozszerzeń). Nazwa katalogu musi być zakresem AACID, poprzedzonym <code style="color: green">annas_archive_data__</code>, bez żadnego sufiksu. Na przykład, jedna z naszych rzeczywistych wersji ma katalog o nazwie<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Zaleca się, aby te foldery były w miarę możliwości zarządzalne pod względem rozmiaru, np. nie większe niż 100GB-1TB każdy, chociaż ta rekomendacja może się zmieniać z czasem. <strong>Kolekcja.</strong> Każde AAC należy do kolekcji, która z definicji jest listą AAC, które są semantycznie spójne. Oznacza to, że jeśli dokonasz znaczącej zmiany w formacie metadata, musisz utworzyć nową kolekcję. Standard <strong>Plik metadata.</strong> Plik metadata zawiera metadata zakresu AAC, dla jednej konkretnej kolekcji. Mają one następujące właściwości: <code>data_folder</code> jest opcjonalne i jest nazwą folderu danych binarnych, który zawiera odpowiadające dane binarne. Nazwa pliku odpowiadających danych binarnych w tym folderze to AACID rekordu. Każdy obiekt JSON musi zawierać następujące pola na najwyższym poziomie: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcjonalne). Żadne inne pola nie są dozwolone. Nazwa pliku musi być zakresem AACID, poprzedzonym <code style="color: red">annas_archive_meta__</code> i zakończonym <code>.jsonl.zstd</code>. Na przykład, jedna z naszych wersji nazywa się<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Jak wskazuje rozszerzenie pliku, typ pliku to <a %(jsonlines)s>JSON Lines</a> skompresowany za pomocą <a %(zstd)s>Zstandard</a>. <code>metadata</code> to dowolne metadata, zgodnie z semantyką kolekcji. Musi być semantycznie spójne w ramach kolekcji. Prefiks <code style="color: red">annas_archive_meta__</code> może być dostosowany do nazwy Twojej instytucji, np. <code style="color: red">my_institute_meta__</code>. <strong>„rekordy” i „pliki” kolekcje.</strong> Zgodnie z konwencją, często wygodnie jest wydawać „rekordy” i „pliki” jako różne kolekcje, aby mogły być wydawane w różnych harmonogramach, np. w oparciu o tempo skanowania. „Rekord” to kolekcja zawierająca tylko metadata, zawierająca informacje takie jak tytuły książek, autorzy, ISBN-y itp., podczas gdy „pliki” to kolekcje zawierające rzeczywiste pliki (pdf, epub). Ostatecznie zdecydowaliśmy się na stosunkowo prosty standard. Jest dość luźny, nienormatywny i wciąż w trakcie opracowywania. <strong>Torrenty.</strong> Pliki metadata i foldery danych binarnych mogą być pakowane w torrenty, z jednym torrentem na plik metadata lub jednym torrentem na folder danych binarnych. Torrenty muszą mieć oryginalną nazwę pliku/katalogu plus <code>.torrent</code> jako ich nazwę pliku. <a %(wikipedia_annas_archive)s>Archiwum Anny</a> stało się zdecydowanie największą na świecie biblioteką cieni i jedyną biblioteką cieni na taką skalę, która jest w pełni open-source i open-data. Poniżej znajduje się tabela z naszej strony Datasets (nieco zmodyfikowana): Osiągnęliśmy to na trzy sposoby: Odwzorowywanie istniejących bibliotek cieni z otwartymi danymi (takich jak Sci-Hub i Library Genesis). Pomoc bibliotekom cieni, które chcą być bardziej otwarte, ale nie miały na to czasu ani zasobów (takich jak kolekcja komiksów Libgen). Zbieranie danych z bibliotek, które nie chcą dzielić się danymi masowo (takich jak Z-Library). Dla (2) i (3) zarządzamy teraz znaczną kolekcją torrentów (setki TB). Do tej pory traktowaliśmy te kolekcje jako jednorazowe, co oznaczało dedykowaną infrastrukturę i organizację danych dla każdej kolekcji. To dodaje znaczne koszty do każdego wydania i utrudnia wprowadzanie bardziej stopniowych wydań. Dlatego zdecydowaliśmy się na standaryzację naszych wydań. To techniczny wpis na blogu, w którym wprowadzamy nasz standard: <strong>Kontenery Archiwum Anny</strong>. Kontenery Archiwum Anny (AAC): standaryzacja wydań z największej na świecie biblioteki cieni Archiwum Anny stało się największą na świecie biblioteką cieni, co wymaga od nas standaryzacji naszych wydań. Ponad 300 GB okładek książek wydanych Na koniec z przyjemnością ogłaszamy małe wydanie. Współpracując z osobami obsługującymi fork Libgen.rs, udostępniamy wszystkie ich okładki książek za pośrednictwem torrentów i IPFS. To rozłoży obciążenie związane z przeglądaniem okładek na więcej maszyn i lepiej je zachowa. W wielu (ale nie we wszystkich) przypadkach okładki książek są zawarte w samych plikach, więc jest to rodzaj „danych pochodnych”. Jednak posiadanie ich w IPFS jest nadal bardzo przydatne do codziennej pracy zarówno Archiwum Anny, jak i różnych forków Library Genesis. Jak zwykle, to wydanie można znaleźć w Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>). Nie podamy tutaj linku, ale można go łatwo znaleźć. Mamy nadzieję, że możemy nieco zwolnić tempo, teraz gdy mamy przyzwoitą alternatywę dla Z-Library. Ten nakład pracy nie jest szczególnie zrównoważony. Jeśli jesteś zainteresowany pomocą w programowaniu, obsłudze serwerów lub pracy nad zachowaniem danych, koniecznie skontaktuj się z nami. Wciąż jest wiele <a %(annas_archive)s>do zrobienia</a>. Dziękujemy za zainteresowanie i wsparcie. Przejście na ElasticSearch Niektóre zapytania trwały bardzo długo, do tego stopnia, że zajmowały wszystkie otwarte połączenia. Domyślnie MySQL ma minimalną długość słowa, lub indeks może stać się naprawdę duży. Ludzie zgłaszali, że nie mogą wyszukiwać „Ben Hur”. Wyszukiwanie było tylko częściowo szybkie, gdy było w pełni załadowane do pamięci, co wymagało od nas zakupu droższego urządzenia do jego uruchomienia, plus kilku poleceń do wstępnego załadowania indeksu przy starcie. Nie bylibyśmy w stanie łatwo go rozszerzyć, aby zbudować nowe funkcje, takie jak lepsza <a %(wikipedia_cjk_characters)s>tokenizacja dla języków bez spacji</a>, filtrowanie/faceting, sortowanie, sugestie „czy miałeś na myśli”, autouzupełnianie i inne. Jednym z naszych <a %(annas_archive)s>biletów</a> był zbiór problemów z naszym systemem wyszukiwania. Używaliśmy pełnotekstowego wyszukiwania MySQL, ponieważ wszystkie nasze dane były w MySQL. Ale miało to swoje ograniczenia: Po rozmowach z wieloma ekspertami zdecydowaliśmy się na ElasticSearch. Nie było to idealne (ich domyślne sugestie „czy miałeś na myśli” i funkcje autouzupełniania są kiepskie), ale ogólnie było znacznie lepsze niż MySQL do wyszukiwania. Nadal nie jesteśmy <a %(youtube)s>zbyt chętni</a> do używania go do jakichkolwiek danych krytycznych dla misji (choć poczynili wiele <a %(elastic_co)s>postępów</a>), ale ogólnie jesteśmy całkiem zadowoleni z tej zmiany. Na razie wdrożyliśmy znacznie szybsze wyszukiwanie, lepsze wsparcie językowe, lepsze sortowanie według trafności, różne opcje sortowania i filtrowanie według języka/typu książki/typu pliku. Jeśli jesteś ciekawy, jak to działa, <a %(annas_archive_l140)s>zajrzyj</a> <a %(annas_archive_l1115)s>do</a> <a %(annas_archive_l1635)s>tego</a>. Jest dość dostępne, choć przydałoby się więcej komentarzy… Archiwum Anny jest w pełni open source Wierzymy, że informacje powinny być darmowe, a nasz własny kod nie jest wyjątkiem. Udostępniliśmy cały nasz kod na naszej prywatnie hostowanej instancji Gitlaba: <a %(annas_archive)s>Oprogramowanie Anny</a>. Używamy również systemu śledzenia problemów do organizacji naszej pracy. Jeśli chcesz zaangażować się w nasz rozwój, to świetne miejsce na początek. Aby dać Ci przedsmak rzeczy, nad którymi pracujemy, przyjrzyj się naszej ostatniej pracy nad poprawą wydajności po stronie klienta. Ponieważ nie wdrożyliśmy jeszcze paginacji, często zwracaliśmy bardzo długie strony wyników wyszukiwania, z 100-200 wynikami. Nie chcieliśmy zbyt wcześnie odcinać wyników wyszukiwania, ale to oznaczało, że spowalniało to niektóre urządzenia. W tym celu zastosowaliśmy mały trik: większość wyników wyszukiwania opakowaliśmy w komentarze HTML (<code><!-- --></code>), a następnie napisaliśmy mały skrypt Javascript, który wykrywał, kiedy wynik powinien stać się widoczny, w tym momencie usuwaliśmy komentarz: "Wirtualizacja" DOM zaimplementowana w 23 liniach, bez potrzeby używania zaawansowanych bibliotek! To rodzaj szybkiego, pragmatycznego kodu, który powstaje, gdy masz ograniczony czas i prawdziwe problemy do rozwiązania. Zgłoszono, że nasze wyszukiwanie teraz działa dobrze na wolnych urządzeniach! Innym dużym wysiłkiem było zautomatyzowanie budowy bazy danych. Kiedy startowaliśmy, po prostu chaotycznie łączyliśmy różne źródła. Teraz chcemy je aktualizować, więc napisaliśmy zestaw skryptów do pobierania nowych metadanych z dwóch forków Library Genesis i ich integracji. Celem jest nie tylko uczynienie tego przydatnym dla naszego archiwum, ale także ułatwienie każdemu, kto chce eksperymentować z metadanymi bibliotek cieni. Celem byłby notebook Jupyter, który zawierałby wszelkiego rodzaju interesujące metadane, abyśmy mogli prowadzić więcej badań, takich jak ustalanie, jaki <a %(blog)s>procent ISBN jest zachowywany na zawsze</a>. Na koniec odnowiliśmy nasz system darowizn. Teraz możesz użyć karty kredytowej, aby bezpośrednio wpłacić pieniądze na nasze portfele kryptowalutowe, bez potrzeby posiadania wiedzy o kryptowalutach. Będziemy monitorować, jak dobrze to działa w praktyce, ale to duża sprawa. Wraz z zamknięciem Z-Library i aresztowaniem jej (rzekomych) założycieli, pracujemy bez przerwy, aby zapewnić dobrą alternatywę w postaci Archiwum Anny (nie podamy tutaj linku, ale można je znaleźć w Google). Oto niektóre z rzeczy, które ostatnio osiągnęliśmy. Aktualizacja Anny: w pełni otwarte źródło archiwum, ElasticSearch, ponad 300GB okładek książek Pracujemy bez przerwy, aby zapewnić dobrą alternatywę z Archiwum Anny. Oto niektóre z rzeczy, które ostatnio osiągnęliśmy. Analiza Duplikaty semantyczne (różne skany tej samej książki) teoretycznie można odfiltrować, ale jest to trudne. Podczas ręcznego przeglądania komiksów znaleźliśmy zbyt wiele fałszywych trafień. Istnieją pewne duplikaty wyłącznie według MD5, co jest stosunkowo marnotrawne, ale ich odfiltrowanie dałoby nam tylko około 1% in oszczędności. W tej skali to wciąż około 1TB, ale także, w tej skali 1TB nie ma większego znaczenia. Wolelibyśmy nie ryzykować przypadkowego zniszczenia danych w tym procesie. Znaleźliśmy sporo danych nieksiążkowych, takich jak filmy oparte na komiksach. To również wydaje się marnotrawstwem, ponieważ są one już szeroko dostępne innymi środkami. Jednak zdaliśmy sobie sprawę, że nie możemy po prostu odfiltrować plików filmowych, ponieważ istnieją również <em>interaktywne komiksy</em>, które zostały wydane na komputerze, a ktoś je nagrał i zapisał jako filmy. Ostatecznie, cokolwiek moglibyśmy usunąć z kolekcji, zaoszczędziłoby tylko kilka procent. Wtedy przypomnieliśmy sobie, że jesteśmy zbieraczami danych, a osoby, które będą to kopiować, również są zbieraczami danych, więc „CO TO ZNACZY, USUNĄĆ?!” :) Kiedy otrzymujesz 95TB wrzuconych do swojego klastra pamięci, próbujesz zrozumieć, co tam właściwie jest… Przeprowadziliśmy analizę, aby sprawdzić, czy możemy nieco zmniejszyć rozmiar, na przykład usuwając duplikaty. Oto niektóre z naszych ustaleń: Dlatego przedstawiamy Państwu pełną, niezmodyfikowaną kolekcję. To dużo danych, ale mamy nadzieję, że wystarczająco dużo osób będzie chciało ją udostępniać. Współpraca Ze względu na swój rozmiar, ta kolekcja od dawna była na naszej liście życzeń, więc po naszym sukcesie z tworzeniem kopii zapasowej Z-Library, skupiliśmy się na tej kolekcji. Na początku zgrywaliśmy ją bezpośrednio, co było sporym wyzwaniem, ponieważ ich serwer nie był w najlepszym stanie. W ten sposób uzyskaliśmy około 15TB, ale postęp był powolny. Na szczęście udało nam się skontaktować z operatorem biblioteki, który zgodził się przesłać nam wszystkie dane bezpośrednio, co było znacznie szybsze. Mimo to, transfer i przetwarzanie wszystkich danych zajęło ponad pół roku, a prawie straciliśmy wszystko z powodu uszkodzenia dysku, co oznaczałoby konieczność rozpoczęcia od nowa. To doświadczenie sprawiło, że wierzymy, iż ważne jest, aby jak najszybciej udostępnić te dane, aby mogły być szeroko kopiowane. Jesteśmy tylko o jedno lub dwa niefortunne zdarzenia od utraty tej kolekcji na zawsze! Kolekcja Szybkie działanie oznacza, że kolekcja jest nieco nieuporządkowana… Spójrzmy na to. Wyobraźmy sobie system plików (który w rzeczywistości dzielimy na torrenty): Pierwszy katalog, <code>/repository</code>, to bardziej uporządkowana część tego. Ten katalog zawiera tzw. „tysiąc katalogów”: katalogi, z których każdy zawiera tysiąc plików, numerowanych kolejno w bazie danych. Katalog <code>0</code> zawiera pliki z comic_id 0–999 i tak dalej. To ten sam schemat, który Library Genesis używa do swoich kolekcji fikcji i literatury faktu. Pomysł polega na tym, że każdy „tysiąc katalogów” automatycznie zamienia się w torrent, gdy tylko zostanie zapełniony. Jednak operator Libgen.li nigdy nie stworzył torrentów dla tej kolekcji, więc tysiące katalogów prawdopodobnie stały się niewygodne i ustąpiły miejsca „nieposortowanym katalogom”. Są to <code>/comics0</code> do <code>/comics4</code>. Wszystkie zawierają unikalne struktury katalogów, które prawdopodobnie miały sens przy zbieraniu plików, ale teraz nie mają dla nas większego sensu. Na szczęście metadata nadal bezpośrednio odnosi się do wszystkich tych plików, więc ich organizacja na dysku nie ma tak naprawdę znaczenia! Metadata są dostępne w formie bazy danych MySQL. Można je pobrać bezpośrednio ze strony Libgen.li, ale udostępnimy je również w torrentach, wraz z naszą własną tabelą zawierającą wszystkie hashe MD5. <q>Dr. Barbara Gordon próbuje zatracić się w przyziemnym świecie biblioteki…</q> Forki Libgen Na początek trochę tła. Możesz znać Library Genesis z ich epickiej kolekcji książek. Mniej osób wie, że wolontariusze Library Genesis stworzyli inne projekty, takie jak pokaźna kolekcja magazynów i dokumentów standardowych, pełna kopia zapasowa Sci-Hub (we współpracy z założycielką Sci-Hub, Alexandrą Elbakyan) oraz ogromna kolekcja komiksów. W pewnym momencie różni operatorzy lustrzanych stron Library Genesis poszli własnymi drogami, co doprowadziło do obecnej sytuacji, w której istnieje wiele różnych „forków”, wszystkie nadal noszące nazwę Library Genesis. Fork Libgen.li ma unikalnie tę kolekcję komiksów, a także pokaźną kolekcję magazynów (nad którą również pracujemy). Zbiórka funduszy Udostępniamy te dane w dużych kawałkach. Pierwszy torrent to <code>/comics0</code>, który umieściliśmy w jednym ogromnym pliku .tar o rozmiarze 12TB. To lepsze dla twojego dysku twardego i oprogramowania torrentowego niż mnóstwo mniejszych plików. W ramach tego wydania organizujemy zbiórkę funduszy. Chcemy zebrać 20 000 dolarów na pokrycie kosztów operacyjnych i kontraktowych związanych z tą kolekcją, a także umożliwić realizację bieżących i przyszłych projektów. Mamy w planach kilka <em>ogromnych</em> projektów. <em>Kogo wspieram moją darowizną?</em> W skrócie: tworzymy kopie zapasowe całej wiedzy i kultury ludzkości, czyniąc je łatwo dostępnymi. Cały nasz kod i dane są open source, jesteśmy projektem prowadzonym całkowicie przez wolontariuszy i do tej pory uratowaliśmy 125TB książek (oprócz istniejących torrentów Libgen i Scihub). Ostatecznie budujemy koło zamachowe, które umożliwia i zachęca ludzi do znajdowania, skanowania i tworzenia kopii zapasowych wszystkich książek na świecie. Napiszemy o naszym głównym planie w przyszłym poście. :) Jeśli przekażesz darowiznę na 12-miesięczne członkostwo „Amazing Archivist” (780 USD), możesz <strong>„adoptować torrent”</strong>, co oznacza, że umieścimy Twoją nazwę użytkownika lub wiadomość w nazwie pliku jednego z torrentów! Możesz przekazać darowiznę, przechodząc na <a %(wikipedia_annas_archive)s>Archiwum Anny</a> i klikając przycisk „Przekaż darowiznę”. Szukamy również więcej wolontariuszy: inżynierów oprogramowania, badaczy bezpieczeństwa, ekspertów od anonimowych transakcji i tłumaczy. Możesz nas również wesprzeć, zapewniając usługi hostingowe. I oczywiście, prosimy o seedowanie naszych torrentów! Dziękujemy wszystkim, którzy już tak hojnie nas wsparli! Naprawdę robicie różnicę. Oto torrenty wydane do tej pory (wciąż przetwarzamy resztę): Wszystkie torrenty można znaleźć na <a %(wikipedia_annas_archive)s>Archiwum Anny</a> w sekcji „Datasets” (nie linkujemy tam bezpośrednio, aby linki do tego bloga nie zostały usunięte z Reddita, Twittera itp.). Stamtąd, podążaj za linkiem do strony Tor. <a %(news_ycombinator)s>Dyskutuj na Hacker News</a> Co dalej? Wiele torrentów jest świetnych do długoterminowego przechowywania, ale niekoniecznie do codziennego dostępu. Będziemy współpracować z partnerami hostingowymi, aby umieścić te dane w sieci (ponieważ Archiwum Anny nie hostuje niczego bezpośrednio). Oczywiście będziesz mógł znaleźć te linki do pobrania w Archiwum Anny. Zapraszamy również wszystkich do pracy z tymi danymi! Pomóż nam lepiej je analizować, deduplikować, umieszczać na IPFS, remiksować, trenować swoje modele AI i tak dalej. To wszystko jest Twoje, i nie możemy się doczekać, co z tym zrobisz. Na koniec, jak już wcześniej wspomniano, wciąż mamy przed sobą kilka ogromnych wydań (jeśli <em>ktoś</em> mógłby <em>przypadkowo</em> przesłać nam zrzut bazy danych <em>pewnej</em> bazy ACS4, wiesz, gdzie nas znaleźć...), a także budowanie koła zamachowego do tworzenia kopii zapasowych wszystkich książek na świecie. Więc bądźcie czujni, dopiero zaczynamy. - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Największa biblioteka cieni komiksów to prawdopodobnie ta z konkretnego forka Library Genesis: Libgen.li. Jeden administrator prowadzący tę stronę zdołał zebrać niesamowitą kolekcję komiksów liczącą ponad 2 miliony plików, o łącznej wielkości ponad 95TB. Jednak w przeciwieństwie do innych kolekcji Library Genesis, ta nie była dostępna w całości przez torrenty. Można było uzyskać dostęp do tych komiksów tylko indywidualnie przez jego wolny osobisty serwer — jeden punkt awarii. Aż do dziś! W tym poście opowiemy więcej o tej kolekcji i o naszej zbiórce funduszy na wsparcie dalszej pracy. Archiwum Anny zarchiwizowało największą na świecie bibliotekę cieni komiksów (95TB) — możesz pomóc w jej seedowaniu Największa na świecie biblioteka cieni komiksów miała jeden punkt awarii... aż do dziś. Uwaga: ten wpis na blogu został wycofany. Zdecydowaliśmy, że IPFS nie jest jeszcze gotowy na główny czas. Nadal będziemy linkować do plików na IPFS z Archiwum Anny, gdy to możliwe, ale nie będziemy już ich sami hostować, ani nie zalecamy innym mirrorowania za pomocą IPFS. Zobacz naszą stronę Torrents, jeśli chcesz pomóc w zachowaniu naszej kolekcji. Umieszczanie 5 998 794 książek na IPFS Rozmnożenie kopii Wracając do naszego pierwotnego pytania: jak możemy twierdzić, że zachowujemy nasze zbiory na zawsze? Głównym problemem jest to, że nasza kolekcja <a %(torrents_stats)s>rośnie</a> w szybkim tempie, poprzez skrobanie i otwarte źródła niektórych ogromnych zbiorów (oprócz niesamowitej pracy już wykonanej przez inne biblioteki cieni z otwartymi danymi, takie jak Sci-Hub i Library Genesis). Ten wzrost danych utrudnia mirrorowanie zbiorów na całym świecie. Przechowywanie danych jest drogie! Ale jesteśmy optymistyczni, zwłaszcza obserwując następujące trzy trendy. <a %(annas_archive_stats)s>Całkowity rozmiar</a> naszych kolekcji, w ciągu ostatnich kilku miesięcy, rozbity według liczby seederów torrentów. Trendy cenowe HDD z różnych źródeł (kliknij, aby zobaczyć badanie). <a %(critical_window_chinese)s>Wersja chińska 中文版</a>, dyskusja na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Zebraliśmy nisko wiszące owoce To wynika bezpośrednio z naszych priorytetów omówionych powyżej. Wolimy najpierw pracować nad uwolnieniem dużych zbiorów. Teraz, gdy zabezpieczyliśmy niektóre z największych zbiorów na świecie, spodziewamy się, że nasz wzrost będzie znacznie wolniejszy. Wciąż istnieje długa lista mniejszych kolekcji, a nowe książki są skanowane lub publikowane każdego dnia, ale tempo prawdopodobnie będzie znacznie wolniejsze. Możemy jeszcze podwoić lub nawet potroić naszą wielkość, ale w dłuższym okresie czasu. Poprawa OCR. Priorytety Kod oprogramowania naukowego i inżynieryjnego Fikcyjne lub rozrywkowe wersje wszystkich powyższych Dane geograficzne (np. mapy, badania geologiczne) Dane wewnętrzne z korporacji lub rządów (wycieki) Dane pomiarowe, takie jak pomiary naukowe, dane ekonomiczne, raporty korporacyjne Rekordy metadata ogólnie (lit. faktu i fikcji; innych mediów, sztuki, ludzi, itp.; w tym recenzje) Książki lit. faktu Czasopisma, gazety, podręczniki lit. faktu Transkrypcje rozmów, dokumentów, podcastów lit. faktu Dane organiczne, takie jak sekwencje DNA, nasiona roślin czy próbki mikroorganizmów Prace naukowe, czasopisma, raporty Strony internetowe naukowe i inżynieryjne, dyskusje online Transkrypcje postępowań prawnych lub sądowych Wyjątkowo zagrożone zniszczeniem (np. przez wojnę, cięcia finansowe, pozwy sądowe lub prześladowania polityczne) Rzadkie Wyjątkowo niedoceniane Dlaczego tak bardzo zależy nam na artykułach i książkach? Odłóżmy na bok nasze fundamentalne przekonanie o zachowaniu w ogóle — być może napiszemy o tym kolejny post. Dlaczego więc artykuły i książki konkretnie? Odpowiedź jest prosta: <strong>gęstość informacji</strong>. Na megabajt przechowywania, tekst pisany przechowuje najwięcej informacji ze wszystkich mediów. Chociaż dbamy zarówno o wiedzę, jak i kulturę, bardziej zależy nam na tej pierwszej. Ogólnie rzecz biorąc, znajdujemy hierarchię gęstości informacji i ważności zachowania, która wygląda mniej więcej tak: Ranking na tej liście jest nieco arbitralny — kilka pozycji jest równorzędnych lub budzi kontrowersje w naszym zespole — i prawdopodobnie zapominamy o niektórych ważnych kategoriach. Ale mniej więcej tak ustalamy priorytety. Niektóre z tych pozycji są zbyt różne od innych, abyśmy się nimi martwili (lub są już obsługiwane przez inne instytucje), takie jak dane organiczne lub geograficzne. Ale większość pozycji na tej liście jest dla nas naprawdę ważna. Kolejnym dużym czynnikiem w naszych priorytetach jest to, jak bardzo zagrożone jest dane dzieło. Wolimy skupić się na dziełach, które są: Wreszcie, zależy nam na skali. Mamy ograniczony czas i pieniądze, więc wolimy spędzić miesiąc na ratowaniu 10 000 książek niż 1 000 książek — jeśli są one równie wartościowe i zagrożone. <em><q>To, co utracone, nie może być odzyskane; ale ocalmy to, co pozostało: nie przez skarbce i zamki, które chronią je przed okiem publicznym i użyciem, skazując je na zapomnienie, ale przez takie rozmnożenie kopii, które umieści je poza zasięgiem przypadku.</q></em><br>— Thomas Jefferson, 1791 Biblioteki cieni Kod może być open source na Githubie, ale Github jako całość nie może być łatwo zmirrorowany i tym samym zachowany (choć w tym konkretnym przypadku istnieją wystarczająco rozproszone kopie większości repozytoriów kodu) Rekordy metadata można swobodnie przeglądać na stronie Worldcat, ale nie można ich pobrać masowo (dopóki ich nie <a %(worldcat_scrape)s>zeskrobaliśmy</a>) Reddit jest darmowy w użyciu, ale niedawno wprowadził surowe środki przeciwko skrobaniu danych, w obliczu głodnych danych szkoleń LLM (więcej o tym później) Istnieje wiele organizacji, które mają podobne misje i priorytety. Rzeczywiście, istnieją biblioteki, archiwa, laboratoria, muzea i inne instytucje zajmujące się tego rodzaju zachowaniem. Wiele z nich jest dobrze finansowanych przez rządy, osoby prywatne lub korporacje. Ale mają one jedną ogromną ślepą plamkę: system prawny. Tutaj leży unikalna rola bibliotek cieni, i powód, dla którego istnieje Archiwum Anny. Możemy robić rzeczy, których inne instytucje nie mogą robić. Teraz, to nie jest (często) tak, że możemy archiwizować materiały, które są nielegalne do zachowania gdzie indziej. Nie, w wielu miejscach jest legalne budowanie archiwum z dowolnymi książkami, artykułami, magazynami i tak dalej. Ale czego często brakuje legalnym archiwom, to <strong>nadmiarowość i trwałość</strong>. Istnieją książki, z których tylko jedna kopia istnieje w jakiejś fizycznej bibliotece gdzieś na świecie. Istnieją rekordy metadata strzeżone przez jedną korporację. Istnieją gazety zachowane tylko na mikrofilmie w jednym archiwum. Biblioteki mogą mieć cięcia w finansowaniu, korporacje mogą zbankrutować, archiwa mogą zostać zbombardowane i spalone do ziemi. To nie jest hipotetyczne — to dzieje się cały czas. Rzeczą, którą możemy unikalnie robić w Archiwum Anny, jest przechowywanie wielu kopii dzieł, na dużą skalę. Możemy zbierać artykuły, książki, magazyny i więcej, i dystrybuować je masowo. Obecnie robimy to przez torrenty, ale dokładne technologie nie mają znaczenia i będą się zmieniać z czasem. Ważne jest, aby wiele kopii było dystrybuowanych na całym świecie. Ten cytat sprzed ponad 200 lat wciąż jest aktualny: Krótka uwaga na temat domeny publicznej. Ponieważ Archiwum Anny koncentruje się unikalnie na działaniach, które są nielegalne w wielu miejscach na świecie, nie zajmujemy się szeroko dostępnymi zbiorami, takimi jak książki z domeny publicznej. Legalne podmioty często już dobrze się tym zajmują. Jednak istnieją względy, które czasami sprawiają, że pracujemy nad publicznie dostępnymi zbiorami: - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Koszty przechowywania nadal spadają wykładniczo 3. Poprawa gęstości informacji Obecnie przechowujemy książki w surowych formatach, w jakich są nam dostarczane. Oczywiście są one skompresowane, ale często są to nadal duże skany lub fotografie stron. Do tej pory jedynymi opcjami zmniejszenia całkowitego rozmiaru naszej kolekcji były bardziej agresywna kompresja lub deduplikacja. Jednak, aby uzyskać wystarczająco duże oszczędności, obie są zbyt stratne dla naszego gustu. Mocna kompresja zdjęć może sprawić, że tekst będzie ledwo czytelny. A deduplikacja wymaga dużej pewności, że książki są dokładnie takie same, co często jest zbyt niedokładne, zwłaszcza jeśli treści są takie same, ale skany wykonano w różnych okolicznościach. Zawsze istniała trzecia opcja, ale jej jakość była tak niska, że nigdy jej nie rozważaliśmy: <strong>OCR, czyli optyczne rozpoznawanie znaków</strong>. Jest to proces konwersji zdjęć na zwykły tekst, przy użyciu AI do wykrywania znaków na zdjęciach. Narzędzia do tego istnieją od dawna i były całkiem przyzwoite, ale „całkiem przyzwoite” nie wystarcza do celów archiwizacji. Jednak ostatnie modele głębokiego uczenia multimodalnego zrobiły niezwykle szybki postęp, choć nadal przy wysokich kosztach. Oczekujemy, że zarówno dokładność, jak i koszty znacznie się poprawią w nadchodzących latach, do tego stopnia, że stanie się to realistyczne do zastosowania w całej naszej bibliotece. Kiedy to nastąpi, prawdopodobnie nadal będziemy przechowywać oryginalne pliki, ale dodatkowo moglibyśmy mieć znacznie mniejszą wersję naszej biblioteki, którą większość ludzi będzie chciała mirrorować. Kluczowe jest to, że surowy tekst sam w sobie kompresuje się jeszcze lepiej i jest znacznie łatwiejszy do deduplikacji, co daje nam jeszcze większe oszczędności. Ogólnie rzecz biorąc, nie jest nierealistyczne oczekiwać co najmniej 5-10-krotnego zmniejszenia całkowitego rozmiaru plików, a może nawet więcej. Nawet przy konserwatywnym 5-krotnym zmniejszeniu, patrzylibyśmy na <strong>1 000–3 000 dolarów za 10 lat, nawet jeśli nasza biblioteka potroi się</strong>. Na moment pisania tego tekstu <a %(diskprices)s>ceny dysków</a> na TB wynoszą około 12 dolarów za nowe dyski, 8 dolarów za używane dyski i 4 dolary za taśmę. Jeśli będziemy konserwatywni i spojrzymy tylko na nowe dyski, oznacza to, że przechowywanie petabajta kosztuje około 12 000 dolarów. Jeśli założymy, że nasza biblioteka potroi się z 900 TB do 2,7 PB, oznaczałoby to 32 400 dolarów na mirrorowanie całej naszej biblioteki. Dodając koszty energii elektrycznej, inne koszty sprzętu i tak dalej, zaokrąglijmy to do 40 000 dolarów. Lub z taśmą bardziej jak 15 000–20 000 dolarów. Z jednej strony <strong>15 000–40 000 dolarów za sumę całej ludzkiej wiedzy to okazja</strong>. Z drugiej strony, to trochę dużo, aby oczekiwać mnóstwa pełnych kopii, zwłaszcza jeśli chcielibyśmy, aby ci ludzie nadal udostępniali swoje torrenty dla dobra innych. To jest dzisiaj. Ale postęp idzie naprzód: Koszty dysków twardych na TB zostały mniej więcej zmniejszone o jedną trzecią w ciągu ostatnich 10 lat i prawdopodobnie będą nadal spadać w podobnym tempie. Taśmy wydają się podążać podobną trajektorią. Ceny SSD spadają jeszcze szybciej i mogą przejąć ceny HDD do końca dekady. Jeśli to się utrzyma, to za 10 lat możemy patrzeć na jedynie 5 000–13 000 dolarów na mirrorowanie całej naszej kolekcji (1/3), a nawet mniej, jeśli mniej urośniemy. Chociaż to wciąż dużo pieniędzy, będzie to osiągalne dla wielu ludzi. A może być jeszcze lepiej z powodu następnego punktu… W Archiwum Anny często pytają nas, jak możemy twierdzić, że zachowamy nasze zbiory na zawsze, skoro ich całkowity rozmiar już zbliża się do 1 Petabajta (1000 TB) i nadal rośnie. W tym artykule przyjrzymy się naszej filozofii i zobaczymy, dlaczego następna dekada jest kluczowa dla naszej misji zachowania wiedzy i kultury ludzkości. Krytyczne okno Jeśli te prognozy są dokładne, <strong>wystarczy poczekać kilka lat</strong>, zanim cała nasza kolekcja będzie szeroko mirrorowana. W ten sposób, jak powiedział Thomas Jefferson, „umieszczona poza zasięgiem przypadku”. Niestety, pojawienie się LLM i ich głodnego danych treningu, postawiło wielu posiadaczy praw autorskich w defensywie. Jeszcze bardziej niż już byli. Wiele stron internetowych utrudnia skrobanie i archiwizację, toczą się procesy sądowe, a tymczasem fizyczne biblioteki i archiwa nadal są zaniedbywane. Możemy się spodziewać, że te trendy będą się pogarszać, a wiele dzieł zostanie utraconych, zanim wejdą do domeny publicznej. <strong>Jesteśmy u progu rewolucji w dziedzinie zachowywania, ale <q>utraconego nie można odzyskać.</q></strong> Mamy krytyczne okno czasowe około 5-10 lat, w którym prowadzenie biblioteki cieni i tworzenie wielu lustrzanych kopii na całym świecie jest jeszcze dość kosztowne, a dostęp nie został jeszcze całkowicie zamknięty. Jeśli uda nam się przetrwać to okno czasowe, to rzeczywiście zachowamy wiedzę i kulturę ludzkości na zawsze. Nie powinniśmy pozwolić, aby ten czas się zmarnował. Nie powinniśmy pozwolić, aby to krytyczne okno się zamknęło. Chodźmy. Krytyczne okno bibliotek cieni Jak możemy twierdzić, że zachowamy nasze zbiory na zawsze, skoro już zbliżają się do 1 PB? Kolekcja Kilka dodatkowych informacji o kolekcji. <a %(duxiu)s>Duxiu</a> to ogromna baza danych zeskanowanych książek, stworzona przez <a %(chaoxing)s>SuperStar Digital Library Group</a>. Większość to książki akademickie, zeskanowane w celu udostępnienia ich cyfrowo uniwersytetom i bibliotekom. Dla naszej anglojęzycznej publiczności, <a %(library_princeton)s>Princeton</a> i <a %(guides_lib_uw)s>University of Washington</a> mają dobre przeglądy. Istnieje również doskonały artykuł, który daje więcej tła: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (znajdź go w Archiwum Anny). Książki z Duxiu od dawna są piratowane w chińskim internecie. Zazwyczaj są sprzedawane przez resellerów za mniej niż dolara. Zwykle są dystrybuowane za pomocą chińskiego odpowiednika Google Drive, który często jest hakowany, aby umożliwić większą przestrzeń dyskową. Niektóre szczegóły techniczne można znaleźć <a %(github_duty_machine)s>tutaj</a> i <a %(github_821_github_io)s>tutaj</a>. Chociaż książki były półpublicznie dystrybuowane, dość trudno jest je zdobyć w dużych ilościach. Mieliśmy to wysoko na naszej liście rzeczy do zrobienia i przeznaczyliśmy na to kilka miesięcy pełnoetatowej pracy. Jednak niedawno niesamowity, zdolny i utalentowany wolontariusz skontaktował się z nami, mówiąc, że wykonał już całą tę pracę — za wielką cenę. Podzielił się z nami pełną kolekcją, nie oczekując niczego w zamian, poza gwarancją długoterminowego zachowania. Naprawdę niezwykłe. Zgodził się poprosić o pomoc w ten sposób, aby uzyskać kolekcję OCR. Kolekcja to 7 543 702 pliki. To więcej niż lit. faktu w Library Genesis (około 5,3 miliona). Całkowity rozmiar plików to około 359TB (326TiB) w obecnej formie. Jesteśmy otwarci na inne propozycje i pomysły. Po prostu skontaktuj się z nami. Sprawdź Archiwum Anny, aby uzyskać więcej informacji o naszych kolekcjach, wysiłkach na rzecz zachowania i jak możesz pomóc. Dziękujemy! Przykładowe strony Aby udowodnić nam, że masz dobry pipeline, oto kilka przykładowych stron do rozpoczęcia, z książki o nadprzewodnikach. Twój pipeline powinien prawidłowo obsługiwać matematykę, tabele, wykresy, przypisy itp. Wyślij przetworzone strony na nasz adres e-mail. Jeśli będą wyglądały dobrze, wyślemy Ci więcej prywatnie i oczekujemy, że będziesz w stanie szybko uruchomić na nich swój pipeline. Gdy będziemy zadowoleni, możemy zawrzeć umowę. - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Wersja chińska 中文版</a>, <a %(news_ycombinator)s>Dyskutuj na Hacker News</a> To krótki post na blogu. Szukamy firmy lub instytucji, która pomoże nam w OCR i ekstrakcji tekstu dla ogromnej kolekcji, którą zdobyliśmy, w zamian za ekskluzywny wczesny dostęp. Po okresie embarga oczywiście udostępnimy całą kolekcję. Wysokiej jakości teksty akademickie są niezwykle przydatne do szkolenia LLM. Chociaż nasza kolekcja jest chińska, powinna być przydatna nawet do szkolenia angielskich LLM: modele wydają się kodować koncepcje i wiedzę niezależnie od języka źródłowego. Do tego tekst musi być wyodrębniony ze skanów. Co zyskuje Archiwum Anny? Pełnotekstowe przeszukiwanie książek dla swoich użytkowników. Ponieważ nasze cele są zgodne z celami deweloperów LLM, szukamy współpracownika. Jesteśmy gotowi dać Ci <strong>ekskluzywny wczesny dostęp do tej kolekcji w całości na 1 rok</strong>, jeśli możesz przeprowadzić odpowiednie OCR i ekstrakcję tekstu. Jeśli jesteś gotów podzielić się z nami całym kodem swojego pipeline'u, bylibyśmy skłonni przedłużyć embargo na kolekcję. Ekskluzywny dostęp dla firm LLM do największej na świecie kolekcji chińskich książek lit. faktu <em><strong>TL;DR:</strong> Archiwum Anny zdobyło unikalną kolekcję 7,5 miliona / 350TB chińskich książek lit. faktu — większą niż Library Genesis. Jesteśmy gotowi dać firmie LLM ekskluzywny dostęp w zamian za wysokiej jakości OCR i ekstrakcję tekstu.</em> Architektura systemu Załóżmy więc, że znalazłeś kilka firm, które są gotowe hostować twoją stronę bez zamykania jej — nazwijmy je „dostawcami kochającymi wolność” 😄. Szybko odkryjesz, że hostowanie wszystkiego u nich jest dość kosztowne, więc możesz chcieć znaleźć „taniego dostawcę” i tam faktycznie hostować, przekierowując przez dostawców kochających wolność. Jeśli zrobisz to dobrze, tani dostawcy nigdy nie będą wiedzieć, co hostujesz, i nigdy nie otrzymają żadnych skarg. Przy wszystkich tych dostawcach istnieje ryzyko, że i tak cię zamkną, więc potrzebujesz również redundancji. Potrzebujemy tego na wszystkich poziomach naszego stosu. Jedną z firm, która kocha wolność i postawiła się w interesującej pozycji, jest Cloudflare. Twierdzili, że nie są dostawcą hostingu, ale usługą, jak ISP. Dlatego nie podlegają DMCA ani innym żądaniom usunięcia, a wszelkie żądania przekazują do rzeczywistego dostawcy hostingu. Posunęli się nawet do sądu, aby chronić tę strukturę. Możemy więc używać ich jako kolejnej warstwy buforowania i ochrony. Cloudflare nie akceptuje anonimowych płatności, więc możemy korzystać tylko z ich darmowego planu. Oznacza to, że nie możemy korzystać z ich funkcji równoważenia obciążenia ani przełączania awaryjnego. Dlatego <a %(annas_archive_l255)s>zaimplementowaliśmy to sami</a> na poziomie domeny. Przy ładowaniu strony przeglądarka sprawdzi, czy bieżąca domena jest nadal dostępna, a jeśli nie, przepisze wszystkie adresy URL na inną domenę. Ponieważ Cloudflare buforuje wiele stron, oznacza to, że użytkownik może trafić na naszą główną domenę, nawet jeśli serwer proxy jest wyłączony, a następnie przy następnym kliknięciu zostać przeniesiony na inną domenę. Mamy również normalne obawy operacyjne, takie jak monitorowanie kondycji serwera, rejestrowanie błędów backendu i frontendu i tak dalej. Nasza architektura przełączania awaryjnego pozwala na większą odporność również w tym zakresie, na przykład poprzez uruchamianie zupełnie innego zestawu serwerów na jednej z domen. Możemy nawet uruchamiać starsze wersje kodu i Datasets na tej oddzielnej domenie, na wypadek gdyby krytyczny błąd w głównej wersji pozostał niezauważony. Możemy również zabezpieczyć się przed odwróceniem się Cloudflare od nas, usuwając go z jednej z domen, na przykład z tej oddzielnej domeny. Możliwe są różne permutacje tych pomysłów. Podsumowanie To było interesujące doświadczenie, aby nauczyć się, jak skonfigurować solidną i odporną wyszukiwarkę biblioteki cieni. Jest mnóstwo więcej szczegółów do podzielenia się w późniejszych postach, więc daj mi znać, o czym chciałbyś się dowiedzieć więcej! Jak zawsze, szukamy darowizn, aby wesprzeć tę pracę, więc koniecznie odwiedź stronę Darowizny w Archiwum Anny. Szukamy również innych form wsparcia, takich jak dotacje, długoterminowi sponsorzy, dostawcy płatności wysokiego ryzyka, a może nawet (gustowne!) reklamy. A jeśli chcesz poświęcić swój czas i umiejętności, zawsze szukamy deweloperów, tłumaczy i innych. Dziękujemy za zainteresowanie i wsparcie. Tokeny innowacji Zacznijmy od naszego stosu technologicznego. Jest celowo nudny. Używamy Flask, MariaDB i ElasticSearch. I to dosłownie wszystko. Wyszukiwanie to w dużej mierze rozwiązany problem i nie zamierzamy go na nowo wymyślać. Poza tym musimy wydać nasze <a %(mcfunley)s>żetony innowacji</a> na coś innego: nie dać się zlikwidować przez władze. Jak legalne lub nielegalne jest dokładnie Archiwum Anny? To w dużej mierze zależy od jurysdykcji prawnej. Większość krajów wierzy w jakąś formę praw autorskich, co oznacza, że ludziom lub firmom przyznaje się wyłączny monopol na określone rodzaje dzieł na określony czas. Na marginesie, w Archiwum Anny wierzymy, że choć istnieją pewne korzyści, to ogólnie prawa autorskie są negatywne dla społeczeństwa — ale to historia na inny czas. Ten wyłączny monopol na określone dzieła oznacza, że nielegalne jest bezpośrednie rozpowszechnianie tych dzieł przez kogokolwiek spoza tego monopolu — w tym przez nas. Ale Archiwum Anny jest wyszukiwarką, która nie rozpowszechnia bezpośrednio tych dzieł (przynajmniej nie na naszej stronie w sieci otwartej), więc powinniśmy być w porządku, prawda? Nie do końca. W wielu jurysdykcjach nie tylko nielegalne jest rozpowszechnianie chronionych prawem autorskim dzieł, ale także linkowanie do miejsc, które to robią. Klasycznym przykładem jest amerykańskie prawo DMCA. To najostrzejszy koniec spektrum. Na drugim końcu spektrum teoretycznie mogą istnieć kraje bez jakichkolwiek praw autorskich, ale takie kraje praktycznie nie istnieją. Prawie każdy kraj ma jakieś prawo autorskie w swoich przepisach. Egzekwowanie to inna historia. Istnieje wiele krajów, których rządy nie dbają o egzekwowanie prawa autorskiego. Są też kraje pomiędzy tymi dwoma skrajnościami, które zakazują rozpowszechniania chronionych prawem autorskim dzieł, ale nie zakazują linkowania do takich dzieł. Innym czynnikiem jest poziom firmy. Jeśli firma działa w jurysdykcji, która nie dba o prawa autorskie, ale sama firma nie chce podejmować żadnego ryzyka, to mogą zamknąć twoją stronę internetową, gdy tylko ktoś się na nią poskarży. Ostatecznie, dużym czynnikiem są płatności. Ponieważ musimy pozostać anonimowi, nie możemy korzystać z tradycyjnych metod płatności. Pozostają nam kryptowaluty, a tylko niewielka część firm je obsługuje (istnieją wirtualne karty debetowe opłacane kryptowalutami, ale często nie są akceptowane). - Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Prowadzę <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największą na świecie otwartoźródłową, non-profit wyszukiwarkę dla <a %(wikipedia_shadow_library)s>bibliotek cieni</a>, takich jak Sci-Hub, Library Genesis i Z-Library. Naszym celem jest uczynienie wiedzy i kultury łatwo dostępnymi, a ostatecznie zbudowanie społeczności ludzi, którzy razem archiwizują i zachowują <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>wszystkie książki na świecie</a>. W tym artykule pokażę, jak prowadzimy tę stronę internetową i jakie unikalne wyzwania wiążą się z prowadzeniem strony o wątpliwym statusie prawnym, ponieważ nie ma „AWS dla charytatywnych organizacji cieni”. <em>Sprawdź także artykuł siostrzany <a %(blog_how_to_become_a_pirate_archivist)s>Jak zostać pirackim archiwistą</a>.</em> Jak prowadzić bibliotekę cieni: operacje w Archiwum Anny Nie ma <q>AWS dla charytatywnych organizacji cieni,</q> więc jak prowadzimy Archiwum Anny? Narzędzia Serwer aplikacji: Flask, MariaDB, ElasticSearch, Docker. Rozwój: Gitlab, Weblate, Zulip. Zarządzanie serwerem: Ansible, Checkmk, UFW. Statyczne hostowanie Onion: Tor, Nginx. Serwer proxy: Varnish. Przyjrzyjmy się, jakich narzędzi używamy, aby to wszystko osiągnąć. To bardzo się rozwija, gdy napotykamy nowe problemy i znajdujemy nowe rozwiązania. Istnieją pewne decyzje, nad którymi się wahaliśmy. Jedną z nich jest komunikacja między serwerami: kiedyś używaliśmy do tego Wireguard, ale okazało się, że czasami przestaje on przesyłać jakiekolwiek dane lub przesyła je tylko w jednym kierunku. Działo się to w przypadku kilku różnych konfiguracji Wireguard, które próbowaliśmy, takich jak <a %(github_costela_wesher)s>wesher</a> i <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Próbowaliśmy również tunelować porty przez SSH, używając autossh i sshuttle, ale napotkaliśmy <a %(github_sshuttle)s>problemy</a> (choć nadal nie jest dla mnie jasne, czy autossh cierpi na problemy TCP-over-TCP, czy nie — po prostu wydaje mi się to niepewnym rozwiązaniem, ale może jest w porządku?). Zamiast tego wróciliśmy do bezpośrednich połączeń między serwerami, ukrywając, że serwer działa na tanich dostawcach, używając filtrowania IP z UFW. Ma to tę wadę, że Docker nie działa dobrze z UFW, chyba że użyjesz <code>network_mode: "host"</code>. Wszystko to jest nieco bardziej podatne na błędy, ponieważ przy najmniejszej błędnej konfiguracji narażasz swój serwer na internet. Może powinniśmy wrócić do autossh — wszelkie opinie byłyby bardzo mile widziane. Również wahaliśmy się między Varnish a Nginx. Obecnie preferujemy Varnish, ale ma on swoje kaprysy i ostre krawędzie. To samo dotyczy Checkmk: nie jesteśmy nim zachwyceni, ale na razie działa. Weblate jest w porządku, ale nie jest niesamowity — czasami obawiam się, że straci moje dane, gdy próbuję je zsynchronizować z naszym repozytorium git. Flask ogólnie był dobry, ale ma pewne dziwne kaprysy, które kosztowały dużo czasu na debugowanie, takie jak konfigurowanie niestandardowych domen czy problemy z integracją SqlAlchemy. Jak dotąd inne narzędzia były świetne: nie mamy poważnych skarg na MariaDB, ElasticSearch, Gitlab, Zulip, Docker i Tor. Wszystkie miały pewne problemy, ale nic zbyt poważnego ani czasochłonnego. Społeczność Pierwsze wyzwanie może być zaskakujące. Nie jest to problem techniczny ani prawny. To problem psychologiczny: wykonywanie tej pracy w cieniu może być niezwykle samotne. W zależności od tego, co planujesz zrobić i jaki masz model zagrożeń, możesz musieć być bardzo ostrożny. Na jednym końcu spektrum mamy osoby takie jak Alexandra Elbakyan*, założycielka Sci-Hub, która jest bardzo otwarta na temat swojej działalności. Ale jest ona w dużym ryzyku aresztowania, jeśli odwiedziłaby kraj zachodni w tym momencie, i mogłaby stanąć przed dziesięcioleciami więzienia. Czy to ryzyko, które byłbyś gotów podjąć? My jesteśmy na drugim końcu spektrum; bardzo uważamy, aby nie zostawić żadnego śladu i mamy silne zabezpieczenia operacyjne. * Jak wspomniano na HN przez "ynno", Alexandra początkowo nie chciała być znana: "Jej serwery były skonfigurowane do emitowania szczegółowych komunikatów o błędach z PHP, w tym pełnej ścieżki do pliku źródłowego, który znajdował się w katalogu /home/<USER>" Więc używaj losowych nazw użytkowników na komputerach, których używasz do tych rzeczy, na wypadek, gdybyś coś źle skonfigurował. Ta tajemnica jednak wiąże się z kosztem psychologicznym. Większość ludzi uwielbia być doceniana za swoją pracę, a jednak nie możesz wziąć za to żadnego uznania w prawdziwym życiu. Nawet proste rzeczy mogą być wyzwaniem, jak pytania przyjaciół o to, co robiłeś (w pewnym momencie "majstrowanie przy moim NAS / homelab" staje się nudne). Dlatego tak ważne jest znalezienie jakiejś społeczności. Możesz zrezygnować z części zabezpieczeń operacyjnych, zwierzając się kilku bardzo bliskim przyjaciołom, którym wiesz, że możesz głęboko zaufać. Nawet wtedy bądź ostrożny, aby niczego nie zapisywać, na wypadek gdyby musieli przekazać swoje e-maile władzom lub gdyby ich urządzenia zostały w jakiś sposób skompromitowane. Jeszcze lepiej jest znaleźć kilku innych piratów. Jeśli twoi bliscy przyjaciele są zainteresowani dołączeniem do ciebie, świetnie! W przeciwnym razie możesz znaleźć innych online. Niestety, to wciąż niszowa społeczność. Jak dotąd znaleźliśmy tylko garstkę innych, którzy są aktywni w tej przestrzeni. Dobrymi miejscami na start wydają się być fora Library Genesis i r/DataHoarder. Zespół Archiwum również ma podobnie myślących ludzi, chociaż działają oni w granicach prawa (nawet jeśli w niektórych szarych strefach prawa). Tradycyjne sceny "warez" i pirackie również mają osoby myślące w podobny sposób. Jesteśmy otwarci na pomysły, jak rozwijać społeczność i eksplorować pomysły. Zapraszamy do kontaktu z nami na Twitterze lub Reddicie. Może moglibyśmy zorganizować jakieś forum lub grupę czatową. Jednym z wyzwań jest to, że to może łatwo zostać ocenzurowane przy użyciu popularnych platform, więc musielibyśmy to hostować sami. Istnieje również kompromis między prowadzeniem tych dyskusji w pełni publicznie (więcej potencjalnego zaangażowania) a uczynieniem ich prywatnymi (nie pozwalając potencjalnym "celom" wiedzieć, że zamierzamy je zeskrobać). Musimy to przemyśleć. Daj nam znać, jeśli jesteś tym zainteresowany! Podsumowanie Mamy nadzieję, że to jest pomocne dla nowo zaczynających pirackich archiwistów. Jesteśmy podekscytowani, że możemy powitać cię w tym świecie, więc nie wahaj się skontaktować. Zachowajmy jak najwięcej wiedzy i kultury świata, i mirrorujmy ją szeroko i daleko. Projekty 4. Wybór danych Często możesz użyć metadanych, aby określić rozsądny podzbiór danych do pobrania. Nawet jeśli ostatecznie chcesz pobrać wszystkie dane, warto najpierw priorytetowo traktować najważniejsze elementy, na wypadek gdybyś został wykryty i obrony zostały wzmocnione, lub ponieważ musiałbyś kupić więcej dysków, lub po prostu dlatego, że coś innego pojawi się w Twoim życiu, zanim będziesz mógł pobrać wszystko. Na przykład, kolekcja może mieć wiele edycji tego samego zasobu (jak książka lub film), gdzie jedna jest oznaczona jako najlepsza jakość. Zachowanie tych edycji najpierw miałoby dużo sensu. Możesz ostatecznie chcieć zachować wszystkie edycje, ponieważ w niektórych przypadkach metadane mogą być niepoprawnie oznaczone, lub mogą istnieć nieznane kompromisy między edycjami (na przykład, "najlepsza edycja" może być najlepsza pod wieloma względami, ale gorsza pod innymi, jak film o wyższej rozdzielczości, ale bez napisów). Możesz także przeszukać swoją bazę danych metadanych, aby znaleźć interesujące rzeczy. Jaki jest największy plik, który jest hostowany, i dlaczego jest tak duży? Jaki jest najmniejszy plik? Czy istnieją interesujące lub nieoczekiwane wzorce, jeśli chodzi o określone kategorie, języki itp.? Czy są duplikaty lub bardzo podobne tytuły? Czy istnieją wzorce dotyczące tego, kiedy dane zostały dodane, na przykład jeden dzień, w którym wiele plików zostało dodanych naraz? Często można się wiele nauczyć, patrząc na zestaw danych w różny sposób. W naszym przypadku zduplikowaliśmy książki z Z-Library w stosunku do skrótów md5 w Library Genesis, oszczędzając w ten sposób dużo czasu na pobieranie i przestrzeni dyskowej. To jednak dość unikalna sytuacja. W większości przypadków nie ma kompleksowych baz danych, które pliki są już odpowiednio zachowane przez innych piratów. To samo w sobie jest ogromną szansą dla kogoś tam. Byłoby świetnie mieć regularnie aktualizowany przegląd rzeczy, takich jak muzyka i filmy, które są już szeroko seedowane na stronach torrentowych, i dlatego mają niższy priorytet do uwzględnienia w pirackich mirrorach. 6. Dystrybucja Masz dane, co daje ci posiadanie pierwszego na świecie pirackiego mirroru twojego celu (najprawdopodobniej). W wielu aspektach najtrudniejsza część jest już za tobą, ale najniebezpieczniejsza część jest jeszcze przed tobą. W końcu do tej pory byłeś niewidoczny; latając pod radarem. Wszystko, co musiałeś zrobić, to używać dobrego VPN przez cały czas, nie wypełniać swoich danych osobowych w żadnych formularzach (oczywiście), a może używać specjalnej sesji przeglądarki (lub nawet innego komputera). Teraz musisz rozprowadzić dane. W naszym przypadku najpierw chcieliśmy przekazać książki z powrotem do Library Genesis, ale szybko odkryliśmy trudności z tym związane (sortowanie fikcji vs lit. faktu). Dlatego zdecydowaliśmy się na dystrybucję za pomocą torrentów w stylu Library Genesis. Jeśli masz możliwość przyczynienia się do istniejącego projektu, może to zaoszczędzić ci dużo czasu. Jednak obecnie nie ma wielu dobrze zorganizowanych pirackich mirrorów. Załóżmy więc, że decydujesz się na samodzielne dystrybuowanie torrentów. Staraj się, aby te pliki były małe, aby łatwo było je mirrorować na innych stronach internetowych. Będziesz musiał samodzielnie seedować torrenty, pozostając anonimowym. Możesz użyć VPN (z przekierowaniem portów lub bez), lub zapłacić za Seedbox za pomocą tumbled Bitcoins. Jeśli nie wiesz, co oznaczają niektóre z tych terminów, będziesz musiał trochę poczytać, ponieważ ważne jest, aby zrozumieć ryzyko związane z tymi działaniami. Możesz hostować same pliki torrent na istniejących stronach torrentowych. W naszym przypadku zdecydowaliśmy się faktycznie hostować stronę internetową, ponieważ chcieliśmy również w jasny sposób szerzyć naszą filozofię. Możesz to zrobić samodzielnie w podobny sposób (używamy Njalla do naszych domen i hostingu, opłacanych za pomocą tumbled Bitcoins), ale możesz również skontaktować się z nami, abyśmy mogli hostować twoje torrenty. Chcemy zbudować kompleksowy indeks pirackich mirrorów z czasem, jeśli ten pomysł się przyjmie. Jeśli chodzi o wybór VPN, wiele już na ten temat napisano, więc powtórzymy tylko ogólną radę, aby wybierać według reputacji. Faktyczne, przetestowane w sądzie polityki no-log z długą historią ochrony prywatności to najniższe ryzyko, naszym zdaniem. Należy zauważyć, że nawet jeśli zrobisz wszystko dobrze, nigdy nie osiągniesz zerowego ryzyka. Na przykład, podczas seedowania swoich torrentów, wysoce zmotywowany aktor państwowy może prawdopodobnie przyjrzeć się przepływom danych przychodzących i wychodzących dla serwerów VPN i wywnioskować, kim jesteś. Albo możesz po prostu popełnić jakiś błąd. Prawdopodobnie już to zrobiliśmy i zrobimy to ponownie. Na szczęście państwa narodowe nie przejmują się <em>aż tak</em> bardzo piractwem. Jedną z decyzji do podjęcia dla każdego projektu jest to, czy publikować go pod tą samą tożsamością, co wcześniej, czy nie. Jeśli nadal używasz tej samej nazwy, błędy w bezpieczeństwie operacyjnym z wcześniejszych projektów mogą wrócić, by cię ugryźć. Ale publikowanie pod różnymi nazwami oznacza, że nie budujesz długotrwałej reputacji. Zdecydowaliśmy się na silne bezpieczeństwo operacyjne od samego początku, aby móc nadal używać tej samej tożsamości, ale nie zawahamy się opublikować pod inną nazwą, jeśli popełnimy błąd lub jeśli okoliczności tego wymagają. Rozpowszechnianie informacji może być trudne. Jak powiedzieliśmy, to wciąż niszowa społeczność. Początkowo zamieściliśmy post na Reddicie, ale naprawdę zyskaliśmy popularność na Hacker News. Na razie nasza rekomendacja to zamieszczenie go w kilku miejscach i zobaczenie, co się stanie. I jeszcze raz, skontaktuj się z nami. Chcielibyśmy rozprzestrzenić wieść o większej liczbie wysiłków pirackiego archiwizmu. 1. Wybór domeny / filozofia Nie brakuje wiedzy i dziedzictwa kulturowego do ocalenia, co może być przytłaczające. Dlatego często warto poświęcić chwilę na zastanowienie się, jaki może być Twój wkład. Każdy ma inny sposób myślenia o tym, ale oto kilka pytań, które możesz sobie zadać: W naszym przypadku szczególnie zależało nam na długoterminowym zachowaniu nauki. Wiedzieliśmy o Library Genesis i o tym, jak było wielokrotnie mirrorowane za pomocą torrentów. Uwielbialiśmy ten pomysł. Pewnego dnia jeden z nas próbował znaleźć podręczniki naukowe w Library Genesis, ale nie mógł ich znaleźć, co poddało w wątpliwość, jak kompletne to naprawdę było. Następnie szukaliśmy tych podręczników online i znaleźliśmy je w innych miejscach, co zasadziło ziarno dla naszego projektu. Nawet zanim dowiedzieliśmy się o Z-Library, mieliśmy pomysł, aby nie próbować zbierać wszystkich tych książek ręcznie, ale skupić się na mirrorowaniu istniejących kolekcji i przekazywaniu ich z powrotem do Library Genesis. Jakie umiejętności posiadasz, które możesz wykorzystać na swoją korzyść? Na przykład, jeśli jesteś ekspertem ds. bezpieczeństwa online, możesz znaleźć sposoby na pokonanie blokad IP dla bezpiecznych celów. Jeśli jesteś świetny w organizowaniu społeczności, być może możesz zebrać ludzi wokół jakiegoś celu. Warto jednak znać trochę programowania, choćby po to, aby zachować dobrą operacyjną bezpieczeństwo w całym tym procesie. Na jakim obszarze warto się skupić, aby uzyskać największy efekt? Jeśli zamierzasz poświęcić X godzin na pirackie archiwizowanie, to jak możesz uzyskać największy "efekt za swoje pieniądze"? Jakie są unikalne sposoby, w jakie o tym myślisz? Możesz mieć ciekawe pomysły lub podejścia, które inni mogli przeoczyć. Ile masz na to czasu? Nasza rada to zacząć od małych projektów i przechodzić do większych, gdy nabierzesz wprawy, ale może to stać się pochłaniające. Dlaczego Cię to interesuje? Co Cię pasjonuje? Jeśli uda nam się zebrać grupę ludzi, którzy archiwizują rzeczy, na których im szczególnie zależy, to pokryje to wiele! Będziesz wiedział znacznie więcej niż przeciętna osoba o swojej pasji, na przykład jakie dane są ważne do zachowania, jakie są najlepsze kolekcje i społeczności online itp. 3. Zbieranie metadata Data dodania/modyfikacji: abyś mógł wrócić później i pobrać pliki, których wcześniej nie pobrałeś (choć często możesz do tego użyć również ID lub hash). Hash (md5, sha1): aby potwierdzić, że pobrałeś plik poprawnie. ID: może być jakimś wewnętrznym ID, ale przydatne są również ID takie jak ISBN lub DOI. Nazwa pliku / lokalizacja Opis, kategoria, tagi, autorzy, język itp. Rozmiar: aby obliczyć, ile miejsca na dysku potrzebujesz. Przejdźmy teraz do bardziej technicznych kwestii. Aby faktycznie zbierać metadata z witryn internetowych, utrzymaliśmy wszystko w prostocie. Używamy skryptów w Pythonie, czasami curl, oraz bazy danych MySQL do przechowywania wyników. Nie używaliśmy żadnego zaawansowanego oprogramowania do zbierania danych, które potrafi mapować złożone strony, ponieważ jak dotąd potrzebowaliśmy tylko zbierać dane z jednego lub dwóch rodzajów stron, po prostu enumerując przez identyfikatory i parsując HTML. Jeśli nie ma łatwo enumerowanych stron, może być potrzebny odpowiedni crawler, który spróbuje znaleźć wszystkie strony. Zanim zaczniesz skrobać całą stronę, spróbuj zrobić to ręcznie przez chwilę. Przejdź przez kilka tuzinów stron samodzielnie, aby zrozumieć, jak to działa. Czasami już w ten sposób napotkasz blokady IP lub inne interesujące zachowania. To samo dotyczy skrobania danych: zanim zagłębisz się w ten cel, upewnij się, że możesz skutecznie pobrać jego dane. Aby obejść ograniczenia, możesz spróbować kilku rzeczy. Czy są inne adresy IP lub serwery, które hostują te same dane, ale nie mają tych samych ograniczeń? Czy są jakieś punkty końcowe API, które nie mają ograniczeń, podczas gdy inne je mają? Przy jakiej szybkości pobierania Twój IP jest blokowany i na jak długo? A może nie jesteś blokowany, ale ograniczany? Co się stanie, jeśli utworzysz konto użytkownika, jak wtedy zmieniają się rzeczy? Czy możesz użyć HTTP/2, aby utrzymać otwarte połączenia i czy to zwiększa szybkość, z jaką możesz żądać stron? Czy są strony, które wymieniają wiele plików naraz i czy informacje tam wymienione są wystarczające? Rzeczy, które prawdopodobnie chcesz zapisać, obejmują: Zazwyczaj robimy to w dwóch etapach. Najpierw pobieramy surowe pliki HTML, zazwyczaj bezpośrednio do MySQL (aby uniknąć wielu małych plików, o czym mówimy więcej poniżej). Następnie, w osobnym kroku, przechodzimy przez te pliki HTML i parsujemy je do rzeczywistych tabel MySQL. W ten sposób nie musisz ponownie pobierać wszystkiego od zera, jeśli odkryjesz błąd w swoim kodzie parsującym, ponieważ możesz po prostu przetworzyć pliki HTML z nowym kodem. Często łatwiej jest również zrównoleglić krok przetwarzania, oszczędzając w ten sposób trochę czasu (i możesz napisać kod przetwarzania, gdy skryptowanie jest w toku, zamiast pisać oba kroki naraz). Na koniec, zauważ, że dla niektórych celów skrobanie metadanych to wszystko, co jest. Istnieją ogromne kolekcje metadanych, które nie są odpowiednio zachowane. Tytuł Wybór domeny / filozofia: Na czym mniej więcej chcesz się skupić i dlaczego? Jakie są twoje unikalne pasje, umiejętności i okoliczności, które możesz wykorzystać na swoją korzyść? Wybór celu: Którą konkretną kolekcję zamierzasz mirrorować? Zbieranie metadata: Katalogowanie informacji o plikach, bez faktycznego pobierania (często znacznie większych) samych plików. Wybór danych: Na podstawie metadata, zawężenie, które dane są najbardziej istotne do archiwizacji w tej chwili. Może to być wszystko, ale często istnieje rozsądny sposób na oszczędność miejsca i przepustowości. Zbieranie danych: Faktyczne pobieranie danych. Dystrybucja: Pakowanie w torrenty, ogłaszanie gdzieś, zachęcanie ludzi do ich rozpowszechniania. 5. Skrobanie danych Teraz jesteś gotowy do pobrania danych w dużej ilości. Jak wspomniano wcześniej, na tym etapie powinieneś już ręcznie pobrać kilka plików, aby lepiej zrozumieć zachowanie i ograniczenia celu. Jednak nadal mogą pojawić się niespodzianki, gdy faktycznie zaczniesz pobierać wiele plików naraz. Nasza rada tutaj to przede wszystkim trzymać się prostoty. Zacznij od pobrania kilku plików. Możesz użyć Pythona, a następnie rozszerzyć na wiele wątków. Ale czasami jeszcze prościej jest generować pliki Bash bezpośrednio z bazy danych, a następnie uruchamiać je w wielu oknach terminala, aby zwiększyć skalę. Szybka sztuczka techniczna, którą warto tu wspomnieć, to użycie OUTFILE w MySQL, które można napisać wszędzie, jeśli wyłączysz "secure_file_priv" w mysqld.cnf (i upewnij się, że również wyłączysz/nadpiszesz AppArmor, jeśli jesteś na Linuksie). Przechowujemy dane na prostych dyskach twardych. Zacznij od tego, co masz, i powoli się rozwijaj. Może być przytłaczające myślenie o przechowywaniu setek TB danych. Jeśli to jest sytuacja, z którą się zmagasz, najpierw umieść dobry podzbiór, a w swoim ogłoszeniu poproś o pomoc w przechowywaniu reszty. Jeśli chcesz samodzielnie zdobyć więcej dysków twardych, r/DataHoarder ma dobre zasoby dotyczące uzyskiwania dobrych ofert. Staraj się nie martwić zbytnio o zaawansowane systemy plików. Łatwo jest wpaść w pułapkę konfigurowania takich rzeczy jak ZFS. Jednym z technicznych szczegółów, o których warto wiedzieć, jest to, że wiele systemów plików nie radzi sobie dobrze z dużą ilością plików. Odkryliśmy, że prostym obejściem jest tworzenie wielu katalogów, np. dla różnych zakresów ID lub prefiksów hash. Po pobraniu danych upewnij się, że sprawdziłeś integralność plików za pomocą hashów w metadata, jeśli są dostępne. 2. Wybór celu Dostępne: nie używa wielu warstw ochrony, aby uniemożliwić zeskrobywanie ich metadata i danych. Specjalna wiedza: masz jakieś specjalne informacje o tym celu, na przykład masz specjalny dostęp do tej kolekcji lub odkryłeś, jak pokonać ich zabezpieczenia. Nie jest to wymagane (nasz nadchodzący projekt nie robi niczego specjalnego), ale z pewnością pomaga! Duży Więc mamy obszar, na który patrzymy, teraz którą konkretną kolekcję mirrorujemy? Jest kilka rzeczy, które sprawiają, że cel jest dobry: Kiedy znaleźliśmy nasze podręczniki naukowe na stronach innych niż Library Genesis, próbowaliśmy dowiedzieć się, jak trafiły do internetu. Następnie odkryliśmy Z-Library i zdaliśmy sobie sprawę, że chociaż większość książek nie pojawia się tam jako pierwsza, to ostatecznie tam trafiają. Dowiedzieliśmy się o jej związku z Library Genesis oraz strukturze motywacyjnej (finansowej) i lepszym interfejsie użytkownika, które sprawiły, że była to znacznie bardziej kompletna kolekcja. Następnie przeprowadziliśmy wstępne zeskrobywanie metadata i danych i zdaliśmy sobie sprawę, że możemy obejść ich limity pobierania IP, wykorzystując specjalny dostęp jednego z naszych członków do wielu serwerów proxy. Podczas eksploracji różnych celów, już teraz ważne jest, aby ukrywać swoje ślady, używając VPN-ów i jednorazowych adresów e-mail, o czym będziemy mówić więcej później. Unikalne: nie jest już dobrze pokryte przez inne projekty. Kiedy realizujemy projekt, ma on kilka faz: To nie są całkowicie niezależne fazy i często wnioski z późniejszej fazy odsyłają cię z powrotem do wcześniejszej fazy. Na przykład, podczas zbierania metadata możesz zdać sobie sprawę, że wybrany cel ma mechanizmy obronne poza twoimi umiejętnościami (jak blokady IP), więc wracasz i znajdujesz inny cel. - Anna i zespół (<a %(reddit)s>Reddit</a>) Całe książki można napisać o <em>dlaczego</em> cyfrowa ochrona jest ważna ogólnie, a piracki archiwizm w szczególności, ale pozwólcie, że przedstawimy krótki wstęp dla tych, którzy nie są zbyt zaznajomieni. Świat produkuje więcej wiedzy i kultury niż kiedykolwiek wcześniej, ale także więcej z tego jest tracone niż kiedykolwiek wcześniej. Ludzkość w dużej mierze powierza to dziedzictwo korporacjom, takim jak wydawcy akademiccy, serwisy streamingowe i firmy zajmujące się mediami społecznościowymi, które często nie okazały się być dobrymi opiekunami. Sprawdźcie dokument "Digital Amnesia" lub dowolny wykład Jasona Scotta. Istnieją instytucje, które dobrze radzą sobie z archiwizowaniem tak dużo, jak tylko mogą, ale są one ograniczone prawem. Jako piraci, jesteśmy w unikalnej pozycji, aby archiwizować kolekcje, których nie mogą dotknąć, z powodu egzekwowania praw autorskich lub innych ograniczeń. Możemy również wielokrotnie mirrorować kolekcje na całym świecie, zwiększając tym samym szanse na ich właściwe zachowanie. Na razie nie będziemy wchodzić w dyskusje na temat zalet i wad własności intelektualnej, moralności łamania prawa, rozważań na temat cenzury czy kwestii dostępu do wiedzy i kultury. Z tym wszystkim na boku, zanurzmy się w <em>jak</em>. Podzielimy się, jak nasz zespół stał się pirackimi archiwistami i jakie lekcje wynieśliśmy po drodze. Istnieje wiele wyzwań, gdy wyruszasz w tę podróż, i mamy nadzieję, że możemy pomóc ci przez nie przejść. Jak zostać pirackim archiwistą Pierwsze wyzwanie może być zaskakujące. Nie jest to problem techniczny ani prawny. To problem psychologiczny. Zanim zaczniemy, dwie aktualizacje dotyczące Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>): Otrzymaliśmy kilka niezwykle hojnych darowizn. Pierwsza to 10 tys. dolarów od anonimowej osoby, która również wspierała "bookwarriora", oryginalnego założyciela Library Genesis. Specjalne podziękowania dla bookwarriora za ułatwienie tej darowizny. Druga to kolejne 10 tys. dolarów od anonimowego darczyńcy, który skontaktował się z nami po naszej ostatniej publikacji i został zainspirowany do pomocy. Mieliśmy również kilka mniejszych darowizn. Dziękujemy bardzo za całe Wasze hojne wsparcie. Mamy kilka ekscytujących nowych projektów w przygotowaniu, które to wsparcie umożliwi, więc bądźcie na bieżąco. Mieliśmy pewne trudności techniczne z rozmiarem naszego drugiego wydania, ale nasze torrenty są już dostępne i działają. Otrzymaliśmy również hojną ofertę od anonimowej osoby, która zgodziła się udostępnić naszą kolekcję na swoich bardzo szybkich serwerach, więc przeprowadzamy specjalne przesyłanie na ich maszyny, po czym wszyscy inni pobierający kolekcję powinni zauważyć znaczne przyspieszenie. Posty na blogu Cześć, jestem Anna. Stworzyłam <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największą na świecie bibliotekę cieni. To mój osobisty blog, na którym ja i moi współpracownicy piszemy o piractwie, cyfrowej archiwizacji i nie tylko. Połącz się ze mną na <a %(reddit)s>Reddit</a>. Zauważ, że ta strona to tylko blog. Hostujemy tutaj tylko nasze własne słowa. Nie hostujemy ani nie linkujemy tutaj żadnych torrentów ani innych plików objętych prawami autorskimi. <strong>Biblioteka</strong> - Jak większość bibliotek, skupiamy się głównie na materiałach pisanych, takich jak książki. W przyszłości możemy rozszerzyć naszą ofertę o inne rodzaje mediów. <strong>Mirror</strong> - Jesteśmy wyłącznie mirror'em istniejących bibliotek. Skupiamy się na zachowaniu, a nie na ułatwianiu wyszukiwania i pobierania książek (dostęp) ani na tworzeniu dużej społeczności osób, które przyczyniają się do dodawania nowych książek (źródła). <strong>Piracki</strong> - Celowo naruszamy prawo autorskie w większości krajów. Pozwala nam to robić coś, czego legalne podmioty nie mogą: upewnić się, że książki są mirrorowane szeroko i daleko. <em>Nie linkujemy do plików z tego bloga. Proszę znaleźć je samodzielnie.</em> - Anna i zespół (<a %(reddit)s>Reddit</a>) Ten projekt (EDYCJA: przeniesiony do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>) ma na celu przyczynienie się do zachowania i uwolnienia ludzkiej wiedzy. Wnosimy nasz mały i skromny wkład, idąc śladami wielkich przed nami. Skupienie tego projektu jest zilustrowane jego nazwą: Pierwszą biblioteką, którą zmirrorowaliśmy, jest Z-Library. To popularna (i nielegalna) biblioteka. Przejęli oni kolekcję Library Genesis i uczynili ją łatwo przeszukiwalną. Ponadto, stali się bardzo skuteczni w pozyskiwaniu nowych książek, zachęcając użytkowników do ich dodawania różnymi korzyściami. Obecnie nie przekazują tych nowych książek z powrotem do Library Genesis. I w przeciwieństwie do Library Genesis, nie umożliwiają łatwego mirrorowania swojej kolekcji, co uniemożliwia szerokie zachowanie. Jest to ważne dla ich modelu biznesowego, ponieważ pobierają opłaty za dostęp do swojej kolekcji w dużych ilościach (więcej niż 10 książek dziennie). Nie oceniamy moralnie pobierania opłat za dostęp do nielegalnej kolekcji książek w dużych ilościach. Nie ma wątpliwości, że Z-Library odniosła sukces w rozszerzaniu dostępu do wiedzy i pozyskiwaniu większej liczby książek. Jesteśmy tutaj po to, aby zrobić swoją część: zapewnić długoterminowe przechowywanie tej prywatnej kolekcji. Chcielibyśmy zaprosić Cię do pomocy w zachowaniu i uwolnieniu ludzkiej wiedzy poprzez pobieranie i seedowanie naszych torrentów. Zobacz stronę projektu, aby uzyskać więcej informacji o tym, jak dane są zorganizowane. Chcielibyśmy również bardzo zaprosić Cię do podzielenia się swoimi pomysłami na temat tego, które kolekcje zmirrorować następnie i jak to zrobić. Razem możemy osiągnąć wiele. To tylko mały wkład wśród niezliczonych innych. Dziękujemy za wszystko, co robisz. Przedstawiamy Piracką Bibliotekę Mirror: Zachowując 7TB książek (które nie są w Libgen) 10% o% pisemnego dziedzictwa ludzkości zachowane na zawsze <strong>Google.</strong> W końcu przeprowadzili te badania dla Google Books. Jednak ich metadata nie są dostępne w dużych ilościach i są dość trudne do zeskrobania. <strong>Różne indywidualne systemy biblioteczne i archiwa.</strong> Istnieją biblioteki i archiwa, które nie zostały zindeksowane i zebrane przez żadną z powyższych, często dlatego, że są niedofinansowane lub z innych powodów nie chcą dzielić się swoimi danymi z Open Library, OCLC, Google i tak dalej. Wiele z nich ma cyfrowe zapisy dostępne przez internet, i często nie są one dobrze chronione, więc jeśli chcesz pomóc i dobrze się bawić, ucząc się o dziwnych systemach bibliotecznych, to są świetne punkty wyjścia. <strong>ISBNdb.</strong> To jest temat tego wpisu na blogu. ISBNdb przeszukuje różne strony internetowe w poszukiwaniu metadata książek, w szczególności danych o cenach, które następnie sprzedają księgarniom, aby mogły ustalać ceny swoich książek zgodnie z resztą rynku. Ponieważ ISBN-y są obecnie dość uniwersalne, skutecznie stworzyli „stronę internetową dla każdej książki”. <strong>Open Library.</strong> Jak wspomniano wcześniej, to jest ich całkowita misja. Pozyskali ogromne ilości danych bibliotecznych z bibliotek współpracujących i archiwów narodowych, i nadal to robią. Mają również wolontariuszy bibliotekarzy i zespół techniczny, który stara się usuwać duplikaty rekordów i oznaczać je wszelkiego rodzaju metadata. Co najlepsze, ich zbiór danych jest całkowicie otwarty. Możesz po prostu <a %(openlibrary)s>pobrać go</a>. <strong>WorldCat.</strong> To jest strona internetowa prowadzona przez organizację non-profit OCLC, która sprzedaje systemy zarządzania bibliotekami. Agregują metadata książek z wielu bibliotek i udostępniają je za pośrednictwem strony WorldCat. Jednakże, zarabiają również na sprzedaży tych danych, więc nie są one dostępne do masowego pobrania. Mają jednak dostępne do pobrania niektóre bardziej ograniczone zbiory danych masowych, we współpracy z konkretnymi bibliotekami. 1. Dla jakiejś rozsądnej definicji "na zawsze". ;) 2. Oczywiście, pisemne dziedzictwo ludzkości to znacznie więcej niż książki, zwłaszcza w dzisiejszych czasach. Na potrzeby tego wpisu i naszych ostatnich wydań skupiamy się na książkach, ale nasze zainteresowania sięgają dalej. 3. Jest wiele więcej do powiedzenia o Aaronie Swartzu, ale chcieliśmy tylko krótko go wspomnieć, ponieważ odgrywa kluczową rolę w tej historii. Z czasem więcej osób może natknąć się na jego nazwisko po raz pierwszy i samodzielnie zagłębić się w temat. <strong>Kopie fizyczne.</strong> Oczywiście nie jest to zbyt pomocne, ponieważ są to tylko duplikaty tego samego materiału. Byłoby fajnie, gdybyśmy mogli zachować wszystkie adnotacje, które ludzie robią w książkach, jak słynne „bazgroły na marginesach” Fermata. Ale niestety, to pozostanie marzeniem archiwisty. <strong>„Wydania”.</strong> Tutaj liczymy każdą unikalną wersję książki. Jeśli cokolwiek w niej jest inne, jak inna okładka czy inna przedmowa, liczy się jako inne wydanie. <strong>Pliki.</strong> Pracując z bibliotekami cieni, takimi jak Library Genesis, Sci-Hub czy Z-Library, istnieje dodatkowe rozważenie. Może być wiele skanów tego samego wydania. A ludzie mogą tworzyć lepsze wersje istniejących plików, skanując tekst za pomocą OCR lub prostując strony, które były skanowane pod kątem. Chcemy liczyć te pliki jako jedno wydanie, co wymagałoby dobrego metadata lub deduplikacji za pomocą miar podobieństwa dokumentów. <strong>„Dzieła”.</strong> Na przykład „Harry Potter i Komnata Tajemnic” jako logiczna koncepcja, obejmująca wszystkie jej wersje, takie jak różne tłumaczenia i wznowienia. To dość użyteczna definicja, ale może być trudno określić, co się liczy. Na przykład, prawdopodobnie chcemy zachować różne tłumaczenia, chociaż wznowienia z tylko drobnymi różnicami mogą nie być tak ważne. - Anna i zespół (<a %(reddit)s>Reddit</a>) Z Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>), naszym celem jest zebranie wszystkich książek na świecie i zachowanie ich na zawsze.<sup>1</sup> Między naszymi torrentami Z-Library a oryginalnymi torrentami Library Genesis mamy 11 783 153 pliki. Ale ile to naprawdę jest? Gdybyśmy odpowiednio zdeduplikowali te pliki, jaki procent wszystkich książek na świecie udało nam się zachować? Naprawdę chcielibyśmy mieć coś takiego: Zacznijmy od kilku przybliżonych liczb: W obu Z-Library/Libgen i Open Library jest znacznie więcej książek niż unikalnych ISBN-ów. Czy to oznacza, że wiele z tych książek nie ma ISBN-ów, czy po prostu brakuje metadata ISBN? Prawdopodobnie możemy odpowiedzieć na to pytanie, łącząc automatyczne dopasowywanie na podstawie innych atrybutów (tytuł, autor, wydawca itp.), wciągając więcej źródeł danych i wyodrębniając ISBN-y z rzeczywistych skanów książek (w przypadku Z-Library/Libgen). Ile z tych ISBN-ów jest unikalnych? Najlepiej to zilustrować za pomocą diagramu Venna: Aby być bardziej precyzyjnym: Byliśmy zaskoczeni, jak mało jest nakładania się! ISBNdb ma ogromną ilość ISBN-ów, które nie pojawiają się ani w Z-Library, ani w Open Library, i to samo dotyczy (w mniejszym, ale wciąż znaczącym stopniu) pozostałych dwóch. To rodzi wiele nowych pytań. Jak bardzo pomogłoby automatyczne dopasowywanie w oznaczaniu książek, które nie były oznaczone ISBN-ami? Czy byłoby wiele dopasowań, a tym samym zwiększone nakładanie się? Co by się stało, gdybyśmy wprowadzili 4. lub 5. zbiór danych? Ile nakładania się byśmy wtedy zobaczyli? To daje nam punkt wyjścia. Możemy teraz przyjrzeć się wszystkim ISBN-om, które nie były w zbiorze danych Z-Library, i które nie pasują również do pól tytuł/autor. To może dać nam możliwość zachowania wszystkich książek na świecie: najpierw poprzez przeszukiwanie internetu w poszukiwaniu skanów, a następnie poprzez wyjście w rzeczywistość, aby skanować książki. To ostatnie mogłoby być nawet finansowane społecznościowo lub napędzane przez „nagrody” od osób, które chciałyby zobaczyć konkretne książki zdigitalizowane. Wszystko to jest opowieścią na inny czas. Jeśli chcesz pomóc w którejkolwiek z tych rzeczy — dalsza analiza; zbieranie więcej metadata; znajdowanie więcej książek; OCR książek; robienie tego dla innych dziedzin (np. artykuły, audiobooki, filmy, seriale, czasopisma) lub nawet udostępnianie niektórych z tych danych do rzeczy takich jak ML / szkolenie dużych modeli językowych — proszę skontaktuj się ze mną (<a %(reddit)s>Reddit</a>). Jeśli jesteś szczególnie zainteresowany analizą danych, pracujemy nad udostępnieniem naszych zbiorów danych i skryptów w bardziej łatwym do użycia formacie. Byłoby świetnie, gdybyś mógł po prostu rozwidlić notatnik i zacząć się tym bawić. Na koniec, jeśli chcesz wesprzeć tę pracę, rozważ dokonanie darowizny. To całkowicie wolontariacka operacja, a Twój wkład robi ogromną różnicę. Każda pomoc się liczy. Na razie przyjmujemy darowizny w kryptowalutach; zobacz stronę Darowizny w Archiwum Anny. Aby uzyskać procent, potrzebujemy mianownika: całkowitej liczby książek, które kiedykolwiek zostały opublikowane.<sup>2</sup> Przed upadkiem Google Books, inżynier pracujący nad projektem, Leonid Taycher, <a %(booksearch_blogspot)s>próbował oszacować</a> tę liczbę. Wyszedł — z przymrużeniem oka — z liczbą 129 864 880 („przynajmniej do niedzieli”). Oszacował tę liczbę, budując zintegrowaną bazę danych wszystkich książek na świecie. W tym celu zebrał różne zestawy danych i połączył je na różne sposoby. Na marginesie, jest jeszcze jedna osoba, która próbowała skatalogować wszystkie książki na świecie: Aaron Swartz, zmarły aktywista cyfrowy i współzałożyciel Reddita.<sup>3</sup> Rozpoczął <a %(youtube)s>Open Library</a> z celem „jedna strona internetowa dla każdej książki, która kiedykolwiek została opublikowana”, łącząc dane z wielu różnych źródeł. Ostatecznie zapłacił najwyższą cenę za swoją pracę nad zachowaniem cyfrowym, gdy został oskarżony o masowe pobieranie artykułów naukowych, co doprowadziło do jego samobójstwa. Nie trzeba dodawać, że jest to jeden z powodów, dla których nasza grupa jest pseudonimowa i dlaczego jesteśmy bardzo ostrożni. Open Library jest nadal heroicznie prowadzona przez ludzi z Internet Archive, kontynuując dziedzictwo Aarona. Wrócimy do tego później w tym poście. W poście na blogu Google, Taycher opisuje niektóre z wyzwań związanych z oszacowaniem tej liczby. Po pierwsze, co stanowi książkę? Istnieje kilka możliwych definicji: „Wydania” wydają się najbardziej praktyczną definicją tego, czym są „książki”. Wygodnie, ta definicja jest również używana do przypisywania unikalnych numerów ISBN. ISBN, czyli Międzynarodowy Standardowy Numer Książki, jest powszechnie używany w międzynarodowym handlu, ponieważ jest zintegrowany z międzynarodowym systemem kodów kreskowych („Międzynarodowy Numer Artykułu”). Jeśli chcesz sprzedawać książkę w sklepach, potrzebuje ona kodu kreskowego, więc otrzymujesz ISBN. W poście na blogu Taychera wspomniano, że chociaż ISBN-y są przydatne, nie są uniwersalne, ponieważ zostały naprawdę przyjęte dopiero w połowie lat siedemdziesiątych i nie wszędzie na świecie. Mimo to, ISBN jest prawdopodobnie najczęściej używanym identyfikatorem wydań książek, więc to nasz najlepszy punkt wyjścia. Jeśli możemy znaleźć wszystkie ISBN-y na świecie, uzyskamy użyteczną listę książek, które wciąż trzeba zachować. Więc skąd wziąć dane? Istnieje kilka istniejących inicjatyw, które próbują skompilować listę wszystkich książek na świecie: W tym wpisie z radością ogłaszamy małe wydanie (w porównaniu do naszych poprzednich wydań Z-Library). Przeszukaliśmy większość ISBNdb i udostępniliśmy dane do pobrania na stronie Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>; nie podamy tutaj bezpośredniego linku, po prostu poszukajcie). To około 30,9 miliona rekordów (20GB jako <a %(jsonlines)s>JSON Lines</a>; 4,4GB skompresowane). Na swojej stronie twierdzą, że mają faktycznie 32,6 miliona rekordów, więc mogliśmy jakoś pominąć niektóre, lub <em>oni</em> mogą coś robić źle. W każdym razie, na razie nie podzielimy się dokładnie, jak to zrobiliśmy — zostawimy to jako ćwiczenie dla czytelnika. ;-) To, czym się podzielimy, to wstępna analiza, aby spróbować zbliżyć się do oszacowania liczby książek na świecie. Przyjrzeliśmy się trzem zbiorom danych: temu nowemu zbiorowi danych ISBNdb, naszemu oryginalnemu wydaniu metadata, które zebraliśmy z biblioteki cieni Z-Library (która obejmuje Library Genesis), oraz zrzutowi danych Open Library. Zrzut ISBNdb, czyli ile książek jest zachowanych na zawsze? Gdybyśmy mieli odpowiednio zdeduplikować pliki z bibliotek cieni, jaki procent wszystkich książek na świecie zachowaliśmy? Aktualizacje dotyczące <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największej prawdziwie otwartej biblioteki w historii ludzkości. <em>Redesign WorldCat</em> Dane <strong>Format?</strong> <a %(blog)s>Kontenery Archiwum Anny (AAC)</a>, które są w zasadzie <a %(jsonlines)s>JSON Lines</a> skompresowane za pomocą <a %(zstd)s>Zstandard</a>, plus pewne znormalizowane semantyki. Te kontenery zawierają różne typy rekordów, w oparciu o różne zeskrobywania, które wdrożyliśmy. Rok temu <a %(blog)s>zaczęliśmy</a> odpowiadać na to pytanie: <strong>Jaki procent książek został trwale zachowany przez biblioteki cieni?</strong> Przyjrzyjmy się kilku podstawowym informacjom o danych: Gdy książka trafia do biblioteki cieni z otwartymi danymi, takiej jak <a %(wikipedia_library_genesis)s>Library Genesis</a>, a teraz <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, jest kopiowana na całym świecie (przez torrenty), co praktycznie zapewnia jej zachowanie na zawsze. Aby odpowiedzieć na pytanie, jaki procent książek został zachowany, musimy znać mianownik: ile książek istnieje w sumie? I najlepiej, abyśmy mieli nie tylko liczbę, ale rzeczywiste metadata. Wtedy możemy nie tylko porównać je z bibliotekami cieni, ale także <strong>stworzyć listę TODO pozostałych książek do zachowania!</strong> Moglibyśmy nawet zacząć marzyć o crowdsourcingowym wysiłku, aby przejść przez tę listę TODO. Zeskrobaliśmy <a %(wikipedia_isbndb_com)s>ISBNdb</a> i pobraliśmy <a %(openlibrary)s>zbiór danych Open Library</a>, ale wyniki były niezadowalające. Głównym problemem był brak dużego nakładania się numerów ISBN. Zobacz ten diagram Venna z <a %(blog)s>naszego wpisu na blogu</a>: Byliśmy bardzo zaskoczeni, jak mało było nakładania się między ISBNdb a Open Library, które obie hojnie włączają dane z różnych źródeł, takich jak zeskrobywanie stron internetowych i zapisy biblioteczne. Jeśli obie dobrze wykonują swoją pracę w znajdowaniu większości ISBN-ów, ich okręgi z pewnością miałyby znaczne nakładanie się, lub jeden byłby podzbiorem drugiego. Zastanawiało nas, ile książek wypada <em>całkowicie poza te okręgi</em>? Potrzebujemy większej bazy danych. To wtedy skierowaliśmy nasze spojrzenia na największą bazę danych książek na świecie: <a %(wikipedia_worldcat)s>WorldCat</a>. Jest to własnościowa baza danych prowadzona przez non-profit <a %(wikipedia_oclc)s>OCLC</a>, która agreguje rekordy metadata z bibliotek na całym świecie, w zamian za udostępnienie tym bibliotekom pełnego zbioru danych i umożliwienie im pojawiania się w wynikach wyszukiwania użytkowników końcowych. Mimo że OCLC jest organizacją non-profit, ich model biznesowy wymaga ochrony ich bazy danych. Cóż, przykro nam to mówić, przyjaciele z OCLC, ale udostępniamy wszystko. :-) W ciągu ostatniego roku skrupulatnie zeskrobaliśmy wszystkie rekordy WorldCat. Na początku mieliśmy szczęście. WorldCat właśnie wprowadzał kompletny redesign swojej strony internetowej (w sierpniu 2022). Obejmowało to znaczne przekształcenie ich systemów zaplecza, wprowadzając wiele luk w zabezpieczeniach. Natychmiast skorzystaliśmy z okazji i byliśmy w stanie zeskrobać setki milionów (!) rekordów w zaledwie kilka dni. Po tym, luki w zabezpieczeniach były powoli naprawiane jedna po drugiej, aż ostatnia, którą znaleźliśmy, została załatana około miesiąc temu. Do tego czasu mieliśmy już praktycznie wszystkie rekordy i dążyliśmy jedynie do nieco wyższej jakości rekordów. Więc uznaliśmy, że czas na wydanie! 1,3 miliarda zeskrobań WorldCat <em><strong>TL;DR:</strong> Archiwum Anny zeskrobało cały WorldCat (największą na świecie kolekcję metadata bibliotecznych), aby stworzyć listę TODO książek, które trzeba zachować.</em> WorldCat Uwaga: ten wpis na blogu został wycofany. Zdecydowaliśmy, że IPFS nie jest jeszcze gotowy na główny czas. Nadal będziemy linkować do plików na IPFS z Archiwum Anny, gdy to możliwe, ale nie będziemy już ich sami hostować, ani nie zalecamy innym mirrorowania za pomocą IPFS. Zobacz naszą stronę Torrents, jeśli chcesz pomóc w zachowaniu naszej kolekcji. Pomóż seedować Z-Library na IPFS Partnerski serwer pobierania SciDB Wypożyczanie zewnętrzne Wypożyczanie zewnętrzne (osoby z zaburzeniami widzenia) Pobieranie zewnętrzne Przeglądaj metadane Zawarte w torrentach Wstecz  (+%(num)s ekstra) nieopłacony opłacony anulowany wygasły oczekuje na potwierdzenie od Anny niepoprawne Kontynuacja tekstu poniżej w języku angielskim. Idź Resetuj Dalej Ostatni Jeżeli Twój adres e-mail nie działa na forum Libgen, sugerujemy użycie <a %(a_mail)s> Proton Mail </a> (konto darmowe). Można także <a %(a_manual)s>poprosić ręcznie</a> o aktywację konta. (może wymagać <a %(a_browser)s>weryfikacji przeglądarki</a> —nielimitowane pobieranie!) Szybki serwer partnera #%(number)s (polecane) (nieco szybciej, ale z listą oczekujących) (weryfikacja przeglądarki niewymagana) (brak weryfikacji przeglądarki ani list oczekujących) (brak listy oczekujących, ale może być bardzo wolno) Wolny serwer partnera #%(number)s Audiobook Komiks Książka (beletrystyka) Książka (literatura faktu) Książka (nieznana) Artykuł z literatury fachowej Czasopismo Partytury muzyczne Pozostałe Dokument dotyczący standardu Nie wszystkie strony mogły zostać przekonwertowane na PDF Oznaczone jako nieprawidłowe w Libgen.li Brak w Library Genesis "libgen.li" Brak w Library Genesis "libgen.rs" (beletrystyka) Brak w Library Genesis "libgen.rs" (lit. faktu) Uruchomienie exiftool nie powiodło się na tym pliku Oznaczone jako „zły plik” w Z-Library Brak w Z-library Oznaczone jako „spam” w Z-Library Nie można otworzyć pliku (np. plik uszkodzony, z zabezpieczeniami DRM) Roszczenie z tytułu praw autorskich Problemy z pobieraniem (np. nie można połączyć, oznaczone kodem błędu, zbyt wolne) Błędne metadane (np. tytuł, opis, zdjęcie okładki) Inne Słaba jakość (np. problemy z formatowaniem, słaba jakość skanu, brakujące strony) Spam / plik powinien zostać usunięty (np. zawiera reklamy, treści obraźliwe) %(amount)s (%(amount_usd)s) %(amount)s razem %(amount)s (%(amount_usd)s) razem Wspaniały Mól Książkowy Błyskotliwy Bibliotekarz Olśniewający Danoskarbnik Cudowny Archiwista Dodatkowe pobierania Cerlalc Czeskie metadane DuXiu 读秀 Indeks eBooków EBSCOhost Google Books Goodreads HathiTrust IA IA Kontrolowane Wypożyczanie Cyfrowe ISBNdb ISBN GRP Libgen.li Z wyłączeniem „scimag” Libgen.rs Lit. faktu i beletrystyka Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Rosyjska Biblioteka Państwowa Sci-Hub Przez Libgen.li „scimag” Sci-Hub / Libgen „scimag” Trantor Przesyłki do AA Z-Library Z-Library Chinese Wyszukaj tytuł, autora, język, typ pliku, ISBN, MD5, … Szukaj Autor Komentarze metadanych Edycja Oryginalna nazwa pliku Wydawca (wyszukaj konkretną dziedzinę) Tytuł Rok wydania Szczegóły techniczne Ta kryptowaluta ma wyższe niż zwykle minimum. Wybierz inny czas trwania lub inną kryptowalutę. Żądanie nie mogło zostać ukończone. Spróbuj ponownie za kilka minut, a jeśli nadal będzie się to powtarzać, skontaktuj się z nami pod adresem %(email)s załączając zrzut ekranu. Wystąpił nieznany błąd. Prosimy o kontakt pod adresem %(email)s i załączenie zrzutu ekranu. Błąd w przetwarzaniu płatności. Proszę poczekać chwilę i spróbować ponownie. Jeśli problem będzie się utrzymywał przez ponad 24 godziny, prosimy o kontakt na %(email)s z zrzutem ekranu. Przeprowadzamy zbiórkę na <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">duplikację</a> największej ukrytej biblioteki komiksów na świecie. Dziękujemy za wsparcie! <a href="/donate">Wesprzyj</a> Jeżeli nie jesteś w stanie wesprzeć nas finansowo, rozważ wspomnienie o nas swoim przyjaciołom i śledzenie nas na <a href="https://www.reddit.com/r/Annas_Archive">Reddicie</a> oraz <a href="https://t.me/annasarchiveorg">Telegramie</a>. Nie wysyłaj do nas e-maili z prośbą o przesłanie <a %(a_request)s>książek</a><br>lub małych (<10k) <a %(a_upload)s>plików</a>. Anna’s Archive DMCA / roszczenia dotyczące praw autorskich Kontakt Reddit Alternatywne domeny SLUM (%(unaffiliated)s) niezwiązany z żadną organizacją Archiwum Anny potrzebuje Twojej pomocy! Jeśli dokonasz darowizny teraz, otrzymasz <strong>podwójną</strong> liczbę szybkich pobrań. Wielu próbuje nas zniszczyć, ale my walczymy dalej. Jeśli wpłacisz darowiznę w tym miesiącu, otrzymasz <strong>podwójną</strong> liczbę szybkich pobrań. Ważne do końca tego miesiąca. Ratowanie ludzkiej wiedzy: wspaniały prezent na wakacje! Członkostwa zostaną odpowiednio przedłużone. Serwery partnerskie są niedostępne z powodu zamknięcia hostingu. Powinny być wkrótce ponownie dostępne. Aby zwiększyć odporność Archiwum Anny, szukamy wolontariuszy do prowadzenia lustrzanych kopii. Dostępna jest nowa metoda donacji: %(method_name)s. Proszę, rozważ %(donate_link_open_tag)swsparcie nas</a> — utrzymanie tej strony nie jest tanie, więc twoja donacja ma znaczenie. Dziękujemy. Poleć znajomego, a oboje otrzymacie %(percentage)s%% dodatkowych szybkich pobrań! Zaskocz bliską osobę, podaruj jej konto z członkostwem. Idealny prezent na Walentynki! Dowiedz się więcej… Konto Aktywność Zaawansowane Blog Anny ↗ Oprogramowanie Anny ↗ wersja beta Eksplorator kodów Zbiory danych Wspomóż Pobrane pliki Najczęściej Zadawane Pytania Strona główna Popraw metadane Dane LLM Zaloguj / Zarejestruj Moje darowizny Profil publiczny Szukaj Bezpieczeństwo Torrenty Przetłumacz ↗ Wolontariat i nagrody Ostatnie pobrania: 📚&nbsp;Największa wyszukiwarka ukrytych bibliotek.⭐️&nbsp;Publikacje z Z-Library, Library Genesis, Sci-Hub i innych. 📈&nbsp;%(book_any)s książek, %(journal_article)s prac naukowych, %(book_comic)s komiksów, %(magazine)s magazynów — zachowanych na zawsze.  i  i nie tylko DuXiu Biblioteka "Internet Archive" Library Genesis 📚&nbsp;Największa całkowicie otwarta biblioteka w historii. 📈&nbsp;%(book_count)s&nbsp;książek, %(paper_count)s&nbsp;dokumentów — zachowanych na zawsze. ⭐️&nbsp;Utrwalamy %(libraries)s. Przetwarzamy i udostępniamy %(scraped)s. Cały nasz kod i zebrane dane są dostępne dla wszystkich. OpenLib Sci-Hub ,  📚 Największa otwartoźródłowa biblioteka. <br>⭐️ Dane ze Sci-Hub, Lib-gen, Zlib i innych. Z-Lib Anna’s Archive Nieprawidłowe żądanie. Odwiedź %(websites)s. Największa biblioteka z otwartym dostępem do danych oraz otwartym kodem źródłowym. Umożliwia dostęp do Sci-Hub, Library Genesis, Z-Library i wielu innych. Przeszukaj Anna’s Archive Anna’s Archive Odśwież, aby spróbować ponownie. <a %(a_contact)s>Skontaktuj się z nami</a>, jeśli problem będzie się utrzymywał przez kilka godzin. 🔥 Problem z ładowaniem tej strony <li>1. Śledź nas na <a href="https://www.reddit.com/r/Annas_Archive/">Reddicie</a> lub <a href="https://t.me/annasarchiveorg">Telegramie</a>.</li><li>2. Szerz wiedzę o Anna’s Archive na Twitterze, Tiktoku, Instagramie, w lokalnej kawiarni, bibliotece, czy też gdziekolwiek indziej! Nie wierzymy w obwarowanie dostępu do informacji — nawet jeśli ta strona zostanie zamknięta, powrócimy w innym miejscu, ponieważ cały nasz kod i dane są otwarte.</li><li>3. Jeśli możesz, rozważ <a href="/donate">wsparcie nas finansowo</a>.</li><li>4. Pomóż <a href="https://translate.annas-software.org/">przetłumaczyć</a> naszą stronę na inne języki.</li><li>5. Jeśli jesteś programistą, rozważ pracę nad naszym <a href="https://annas-software.org/">otwartym kodem</a>, lub seedowanie <a href="/datasets">torrentów</a>.</li> 10. Stwórz lub pomóż w utrzymaniu Anna’s Archive w Wikipedii w swoim języku. 11. Poszukujemy możliwości zamieszczania niewielkich, estetycznych reklam. Jeśli chciałbyś zareklamować się na Anna's Archive, daj nam znać. 6. Jeśli jesteś badaczem bezpieczeństwa, możemy wykorzystać twoje umiejętności zarówno w ataku, jak i obronie. Sprawdź naszą stronę <a %(a_security)s>Bezpieczeństwo</a>. 7. Poszukujemy specjalistów od anonimowych płatności. Czy możesz pomóc nam zaimplementować wygodniejsze sposoby przekazywania darowizn? PayPal, WeChat, karty podarunkowe. Jeśli znasz kogoś takiego, skontaktuj się z nami. 8. Zawsze szukamy lepszych serwerów z dużą pojemnością. 9. Możesz pomóc, zgłaszając problemy z plikami, komentując i tworząc listy bezpośrednio na tej stronie. Możesz także pomóc, <a %(a_upload)s>przesyłając więcej książek</a> lub naprawiając problemy z plikami lub formatowaniem istniejących książek. Aby uzyskać bardziej szczegółowe informacje na temat wolontariatu, zobacz naszą stronę <a %(a_volunteering)s>Wolontariat i Nagrody</a>. Mocno wierzymy w wolny przepływ informacji, jak i w ochronę kultury i wiedzy. Tą wyszukiwarką stoimy na barkach olbrzymów. Głęboko szanujemy ciężką pracę ludzi, którzy utworzyli przeróżne ukryte biblioteki, i mamy nadzieję, że ta wyszukiwarka poszerzy ich zasięg. Bądź na bieżąco, śledź Annę na <a href="https://www.reddit.com/r/Annas_Archive/">Reddicie</a> lub <a href="https://t.me/annasarchiveorg">Telegramie</a>. Pytania i opinie odnośnie projektu kieruj do Anny, pod %(email)s. ID konta: %(account_id)s Wyloguj ❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie. ✅ Wylogowano. Odśwież stronę aby zalogować ponownie. Wykorzystane szybkie pobieranie (ostatnie 24 godziny): <strong>%(used)s / %(total)s</strong> Członkostwo: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(extend)</a> Można połączyć kilka subskrypcji (szybkie pobieranie w ciągu 24 godzin zostanie zsumowane). Członkostwo: <strong> Brak </strong> <a %(a_become)s>(zostań członkiem)</a> Skontaktuj się z Anną pod %(email)s, w przypadku chęci zmiany progu członkostwa na wyższy. Profil publiczny: %(profile_link)s Tajny klucz (nie udostępniaj go nikomu!): %(secret_key)s pokaż Dołącz do nas! Ulepsz do <a %(a_tier)s>wyższego poziomu</a>, aby dołączyć do naszej grupy. Tajna grupa na Telegramie: %(link)s Konto jakie pobrania? Zaloguj Nie zgub swojego klucza! Sekretny klucz jest błędny. Zweryfikuj prawidłowość swojego klucza i spóbuj ponownie lub zarejestruj nowe konto poniżej. Sekretny klucz Wprowadź Twój sekretny klucz, aby zalogować: Starsze konto oparte o adres e-mail? Wprowadź swój <a %(a_open)s> adres tutaj</a>. Zarejestruj nowe konto Nie posiadasz konta? Rejestracja zakończona pomyślnie! Twój sekretny klucz to: <span %(span_key)s>%(key)s</span> Zapisz ten klucz w bezpiecznym miejscu, ponieważ utracenie go wiąże się z utratą dostępu do konta. <li %(li_item)s<strong> Zakładka. </strong> Możesz dodać tę stronę do zakładek aby odzyskać swój klucz.</li><li %(li_item)s><strong> Pobierz. </strong> naciśnij <a %(a_download)s>ten odnośnik</a> aby pobrać swój klucz.</li><li %(li_item)s><strong> Menedżer haseł.</strong> Dla Twojej wygody, klucz poniżej jest uzupełniony wstępnie, więc po zalogowaniu będzie on dostępny w menedżerze haseł. </li> Zaloguj / Zarejestruj Weryfikacja przeglądarki Ostrzeżenie: kod zawiera nieprawidłowe znaki Unicode i może działać nieprawidłowo w różnych sytuacjach. Surowy binarny można dekodować z reprezentacji base64 w URL. Opis Etykieta Prefiks URL dla konkretnego kodu Strona internetowa Kody zaczynające się od „%(prefix_label)s” Prosimy nie skrobać tych stron. Zamiast tego zalecamy <a %(a_import)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB oraz uruchamianie naszego <a %(a_software)s>otwartego kodu źródłowego</a>. Surowe dane można ręcznie przeglądać za pomocą plików JSON, takich jak <a %(a_json_file)s>ten</a>. Mniej niż %(count)s rekordów Ogólny URL Eksplorator Kodów Indeks Eksploruj kody, którymi oznaczone są rekordy, według prefiksu. Kolumna „rekordy” pokazuje liczbę rekordów oznaczonych kodami z danym prefiksem, jak widać w wyszukiwarce (w tym rekordy tylko z metadanymi). Kolumna „kody” pokazuje, ile faktycznych kodów ma dany prefiks. Znany prefiks kodu „%(key)s” Więcej… Prefiks %(count)s rekord pasujący do „%(prefix_label)s” page.codes.records_starting_with %(count)s rekordów pasujących do „%(prefix_label)s” kody rekordy „%%s” zostanie zastąpione wartością kodu Szukaj w Archiwum Anny Kody URL dla konkretnego kodu: „%(url)s” Ta strona może zająć trochę czasu na wygenerowanie, dlatego wymaga captcha Cloudflare. <a %(a_donate)s>Członkowie</a> mogą pominąć captcha. Nadużycie zgłoszone: Lepsza wersja Czy chcesz zgłosić tego użytkownika za obraźliwe lub nieodpowiednie zachowanie? Problem z plikiem: %(file_issue)s ukryty komentarz Odpowiedz Zgłoś nadużycie Zgłosiłeś tego użytkownika za nadużycie. Roszczenia dotyczące praw autorskich wysłane na ten e-mail będą ignorowane; zamiast tego użyj formularza. Pokaż e-mail Bardzo chętnie przyjmiemy Twoje opinie i pytania! Jednak ze względu na ilość spamu i bezsensownych e-maili, które otrzymujemy, prosimy o zaznaczenie pól, aby potwierdzić, że rozumiesz te warunki kontaktu z nami. Wszelkie inne sposoby kontaktu z nami w sprawie roszczeń dotyczących praw autorskich będą automatycznie usuwane. W przypadku roszczeń DMCA / praw autorskich, użyj <a %(a_copyright)s>tego formularza</a>. Email kontaktowy Adresy URL na Archiwum Anny (wymagane). Jeden na linię. Prosimy o uwzględnienie tylko adresów URL opisujących dokładnie tę samą edycję książki. Jeśli chcesz zgłosić roszczenie dotyczące wielu książek lub wielu edycji, prosimy o wielokrotne wypełnienie tego formularza. Zgłoszenia łączące wiele książek lub edycji będą odrzucane. Adres (wymagane) Dokładny opis materiału źródłowego (wymagane) E-mail (wymagane) Adresy URL do materiału źródłowego, jeden na linię (wymagane). Prosimy o uwzględnienie jak największej liczby, aby pomóc nam zweryfikować Twoje zgłoszenie (np. Amazon, WorldCat, Google Books, DOI). Numery ISBN materiału źródłowego (jeśli dotyczy). Jeden na linię. Prosimy o uwzględnienie tylko tych, które dokładnie odpowiadają edycji, dla której zgłaszasz roszczenie dotyczące praw autorskich. Twoje imię i nazwisko (wymagane) ❌ Coś poszło nie tak. Prosimy o odświeżenie strony i ponowienie próby. ✅ Dziękujemy za złożenie zgłoszenia dotyczącego praw autorskich. Przeanalizujemy je tak szybko, jak to możliwe. Prosimy o odświeżenie strony, aby złożyć kolejne zgłoszenie. <a %(a_openlib)s>Open Library</a> adresy URL materiału źródłowego, jeden na linię. Prosimy o chwilę na przeszukanie Open Library w celu znalezienia materiału źródłowego. Pomoże nam to zweryfikować Twoje zgłoszenie. Numer telefonu (wymagane) Oświadczenie i podpis (wymagane) Złóż zgłoszenie Jeśli masz zgłoszenie DMCA lub inne zgłoszenie naruszenia praw autorskich, prosimy o wypełnienie tego formularza tak dokładnie, jak to możliwe. W przypadku problemów prosimy o kontakt pod dedykowanym adresem DMCA: %(email)s. Zwróć uwagę, że zgłoszenia wysłane na ten adres e-mail nie będą przetwarzane, jest on przeznaczony wyłącznie do pytań. Prosimy o korzystanie z poniższego formularza do składania zgłoszeń. Formularz zgłoszenia DMCA / naruszenia praw autorskich Przykładowy rekord w Archiwum Anny Torrenty Archiwum Anny Format Kontenerów Archiwum Anny Skrypty do importowania metadanych Jeśli jesteś zainteresowany mirrorowaniem tego zestawu danych do celów <a %(a_archival)s>archiwalnych</a> lub <a %(a_llm)s>szkolenia LLM</a>, prosimy o kontakt. Ostatnia aktualizacja: %(date)s Główna strona %(source)s Dokumentacja metadanych (większość pól) Pliki zmirorowane przez Archiwum Anny: %(count)s (%(percent)s%%) Zasoby Łączna liczba plików: %(count)s Całkowity rozmiar plików: %(size)s Nasz post na blogu o tych danych <a %(duxiu_link)s>Duxiu</a> to ogromna baza danych zeskanowanych książek, stworzona przez <a %(superstar_link)s>SuperStar Digital Library Group</a>. Większość to książki akademickie, zeskanowane w celu udostępnienia ich cyfrowo uniwersytetom i bibliotekom. Dla naszej anglojęzycznej publiczności, <a %(princeton_link)s>Princeton</a> i <a %(uw_link)s>University of Washington</a> mają dobre przeglądy. Jest również doskonały artykuł dający więcej tła: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Książki z Duxiu od dawna są piratowane w chińskim internecie. Zazwyczaj są sprzedawane za mniej niż dolara przez resellerów. Zazwyczaj są dystrybuowane za pomocą chińskiego odpowiednika Google Drive, który często jest hakowany, aby umożliwić większą przestrzeń dyskową. Niektóre szczegóły techniczne można znaleźć <a %(link1)s>tutaj</a> i <a %(link2)s>tutaj</a>. Chociaż książki były półpublicznie dystrybuowane, zdobycie ich w dużych ilościach jest dość trudne. Mieliśmy to wysoko na naszej liście rzeczy do zrobienia i przeznaczyliśmy na to kilka miesięcy pełnoetatowej pracy. Jednak pod koniec 2023 roku niesamowity, zdumiewający i utalentowany wolontariusz skontaktował się z nami, informując, że wykonał już całą tę pracę — za wielkie koszty. Podzielił się z nami pełną kolekcją, nie oczekując niczego w zamian, poza gwarancją długoterminowego przechowywania. Naprawdę niezwykłe. Więcej informacji od naszych wolontariuszy (surowe notatki): Zaadaptowane z naszego <a %(a_href)s>postu na blogu</a>. DuXiu 读秀 %(count)s plik page.datasets.files %(count)s pliki Ten zestaw danych jest ściśle powiązany z <a %(a_datasets_openlib)s>zestawem danych Open Library</a>. Zawiera zrzut wszystkich metadanych i dużą część plików z Kontrolowanej Biblioteki Cyfrowej IA. Aktualizacje są wydawane w <a %(a_aac)s>formacie kontenerów Archiwum Anny</a>. Te rekordy są bezpośrednio odwoływane z zestawu danych Open Library, ale zawierają również rekordy, które nie znajdują się w Open Library. Mamy także szereg plików danych zebranych przez członków społeczności na przestrzeni lat. Kolekcja składa się z dwóch części. Potrzebujesz obu części, aby uzyskać wszystkie dane (z wyjątkiem zastąpionych torrentów, które są przekreślone na stronie torrentów). Cyfrowa Biblioteka Wypożyczeń nasze pierwsze wydanie, zanim ustandaryzowaliśmy format <a %(a_aac)s>Kontenerów Archiwum Anny (AAC)</a>. Zawiera metadane (w formatach json i xml), pliki pdf (z systemów cyfrowego wypożyczania acsm i lcpdf) oraz miniaturki okładek. inkrementalne nowe wydania, używające AAC. Zawiera tylko metadane z znacznikami czasu po 2023-01-01, ponieważ reszta jest już pokryta przez „ia”. Zawiera również wszystkie pliki pdf, tym razem z systemów wypożyczania acsm i „bookreader” (webowy czytnik IA). Pomimo że nazwa nie jest całkowicie trafna, nadal umieszczamy pliki bookreader w kolekcji ia2_acsmpdf_files, ponieważ są one wzajemnie wykluczające się. IA Controlled Digital Lending 98%%+ plików jest przeszukiwalnych. Naszą misją jest archiwizowanie wszystkich książek na świecie (a także artykułów, magazynów itp.) i udostępnianie ich szeroko. Wierzymy, że wszystkie książki powinny być szeroko mirroringowane, aby zapewnić redundancję i odporność. Dlatego zbieramy pliki z różnych źródeł. Niektóre źródła są całkowicie otwarte i mogą być mirroringowane hurtowo (takie jak Sci-Hub). Inne są zamknięte i chronione, więc staramy się je skrobać, aby „uwolnić” ich książki. Jeszcze inne znajdują się gdzieś pomiędzy. Wszystkie nasze dane mogą być <a %(a_torrents)s>torrenty</a>, a wszystkie nasze metadane mogą być <a %(a_anna_software)s>generowane</a> lub <a %(a_elasticsearch)s>pobrane</a> jako bazy danych ElasticSearch i MariaDB. Surowe dane można ręcznie przeglądać za pomocą plików JSON, takich jak <a %(a_dbrecord)s>ten</a>. Metadane Strona internetowa ISBN Ostatnia aktualizacja: %(isbn_country_date)s (%(link)s) Zasoby Międzynarodowa Agencja ISBN regularnie publikuje zakresy, które przydzieliła krajowym agencjom ISBN. Na tej podstawie możemy określić, do jakiego kraju, regionu lub grupy językowej należy dany ISBN. Obecnie korzystamy z tych danych pośrednio, za pośrednictwem biblioteki Python <a %(a_isbnlib)s>isbnlib</a>. Informacje o kraju ISBN To jest zrzut wielu zapytań do isbndb.com z września 2022 roku. Próbowaliśmy objąć wszystkie zakresy ISBN. To około 30,9 miliona rekordów. Na swojej stronie internetowej twierdzą, że mają faktycznie 32,6 miliona rekordów, więc mogło nam coś umknąć, albo <em>oni</em> mogą popełniać jakiś błąd. Odpowiedzi JSON są praktycznie surowe z ich serwera. Jednym z problemów z jakością danych, który zauważyliśmy, jest to, że dla numerów ISBN-13 zaczynających się od innego prefiksu niż „978-”, nadal zawierają pole „isbn”, które jest po prostu numerem ISBN-13 z obciętymi pierwszymi 3 cyframi (i przeliczonym numerem kontrolnym). To jest oczywiście błędne, ale tak to robią, więc tego nie zmienialiśmy. Innym potencjalnym problemem, na który możesz natrafić, jest fakt, że pole „isbn13” ma duplikaty, więc nie można go używać jako klucza głównego w bazie danych. Pola „isbn13”+„isbn” wydają się być unikalne. Wydanie 1 (2022-10-31) Torrenty beletrystyki są opóźnione (choć ID ~4-6M nie są torrentyzowane, ponieważ nakładają się na nasze torrenty Zlib). Nasz wpis na blogu o wydaniu komiksów Torrenty z komiksami na Archiwum Anny Aby poznać historię różnych forków Library Genesis, zobacz stronę <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li zawiera większość tej samej treści i metadanych co Libgen.rs, ale ma dodatkowe kolekcje, mianowicie komiksy, magazyny i dokumenty standardowe. Zintegrował również <a %(a_scihub)s>Sci-Hub</a> ze swoimi metadanymi i wyszukiwarką, co wykorzystujemy w naszej bazie danych. Metadane tej biblioteki są dostępne bezpłatnie <a %(a_libgen_li)s>na libgen.li</a>. Jednak ten serwer jest wolny i nie obsługuje wznawiania przerwanych połączeń. Te same pliki są również dostępne na <a %(a_ftp)s>serwerze FTP</a>, który działa lepiej. Literatura faktu również wydaje się być rozdzielona, ale bez nowych torrentów. Wygląda na to, że stało się to od początku 2022 roku, choć nie zweryfikowaliśmy tego. Według administratora Libgen.li, kolekcja „fiction_rus” (rosyjska fikcja) powinna być objęta regularnie wydawanymi torrentami z <a %(a_booktracker)s>booktracker.org</a>, w szczególności torrentami <a %(a_flibusta)s>flibusta</a> i <a %(a_librusec)s>lib.rus.ec</a> (które mirrorujemy <a %(a_torrents)s>tutaj</a>, choć jeszcze nie ustaliliśmy, które torrenty odpowiadają którym plikom). Kolekcja fikcji ma własne torrenty (oddzielone od <a %(a_href)s>Libgen.rs</a>) zaczynające się od %(start)s. Pewne zakresy bez torrentów (takie jak zakresy fikcji f_3463000 do f_4260000) to prawdopodobnie pliki Z-Library (lub inne duplikaty), choć możemy chcieć przeprowadzić deduplikację i stworzyć torrenty dla unikalnych plików lgli w tych zakresach. Statystyki dla wszystkich kolekcji można znaleźć <a %(a_href)s>na stronie libgen</a>. Torrenty są dostępne dla większości dodatkowej zawartości, w szczególności torrenty dla komiksów, czasopism i dokumentów standardowych zostały wydane we współpracy z Archiwum Anny. Należy zauważyć, że pliki torrent odnoszące się do „libgen.is” są wyraźnie mirrorami <a %(a_libgen)s>Libgen.rs</a> („.is” to inna domena używana przez Libgen.rs). Pomocnym źródłem w korzystaniu z metadanych jest <a %(a_href)s>ta strona</a>. %(icon)s Ich kolekcja „fiction_rus” (rosyjska fikcja) nie ma dedykowanych torrentów, ale jest objęta torrentami od innych, a my utrzymujemy <a %(fiction_rus)s>mirror</a>. Torrenty rosyjskiej fikcji w Archiwum Anny Torrenty z literaturą piękną na Archiwum Anny Forum dyskusyjne Metadane Metadane przez FTP Torrenty z magazynami na Archiwum Anny Informacje o polach metadanych Mirror innych torrentów (i unikalne torrenty z literaturą piękną i komiksami) Torrenty dokumentów standardowych w Archiwum Anny Libgen.li Torrenty z Archiwum Anny (okładki książek) Library Genesis jest znane z hojnego udostępniania swoich danych w formie torrentów. Nasza kolekcja Libgen składa się z danych pomocniczych, które nie są bezpośrednio udostępniane, we współpracy z nimi. Wielkie podziękowania dla wszystkich zaangażowanych w Library Genesis za współpracę z nami! Nasz blog o wydaniu okładek książek Ta strona dotyczy wersji „.rs”. Jest ona znana z konsekwentnego publikowania zarówno swoich metadanych, jak i pełnej zawartości swojego katalogu książek. Jej kolekcja książek jest podzielona na część z literaturą piękną i lit. faktu. Pomocnym źródłem w korzystaniu z metadanych jest <a %(a_metadata)s>ta strona</a> (blokuje zakresy IP, może być wymagane użycie VPN). Od marca 2024 r. nowe torrenty są publikowane w <a %(a_href)s>tym wątku na forum</a> (blokuje zakresy IP, może być wymagany VPN). Torrenty literatury pięknej na Archiwum Anny Torrenty literatury pięknej Libgen.rs Forum dyskusyjne Libgen.rs Metadane Libgen.rs Informacje o polach metadanych Libgen.rs Torrenty literatury faktu Libgen.rs Torrenty literatury faktu na Archiwum Anny %(example)s dla książki literatury pięknej. To <a %(blog_post)s>pierwsze wydanie</a> jest dość małe: około 300 GB okładek książek z forka Libgen.rs, zarówno literatury pięknej, jak i faktu. Są one zorganizowane w taki sam sposób, jak pojawiają się na libgen.rs, np.: %(example)s dla książki literatury faktu. Podobnie jak w przypadku kolekcji Z-Library, umieściliśmy je wszystkie w dużym pliku .tar, który można zamontować za pomocą <a %(a_ratarmount)s>ratarmount</a>, jeśli chcesz bezpośrednio udostępniać pliki. Wydanie 1 (%(date)s) Krótka historia różnych forków Library Genesis (lub „Libgen”) jest taka, że z czasem różne osoby zaangażowane w Library Genesis poróżniły się i poszły własnymi drogami. Według tego <a %(a_mhut)s>postu na forum</a>, Libgen.li pierwotnie była hostowana na „http://free-books.dontexist.com”. Wersja „.fun” została stworzona przez oryginalnego założyciela. Jest ona odświeżana na rzecz nowej, bardziej rozproszonej wersji. <a %(a_li)s>Wersja „.li”</a> ma ogromną kolekcję komiksów, a także inną zawartość, która nie jest (jeszcze) dostępna do zbiorczego pobierania przez torrenty. Ma ona oddzielną kolekcję torrentów z książkami literatury pięknej i zawiera metadane <a %(a_scihub)s>Sci-Hub</a> w swojej bazie danych. Wersja „.rs” ma bardzo podobne dane i najczęściej wydaje swoją kolekcję w zbiorczych torrentach. Jest ona w przybliżeniu podzielona na sekcje „literatura piękna” i „lit. faktu”. Oryginalnie na „http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> w pewnym sensie jest również forkiem Library Genesis, chociaż użyli innej nazwy dla swojego projektu. Libgen.rs Wzbogacamy również naszą kolekcję o źródła tylko z metadanymi, które możemy dopasować do plików, np. za pomocą numerów ISBN lub innych pól. Poniżej znajduje się ich przegląd. Ponownie, niektóre z tych źródeł są całkowicie otwarte, podczas gdy inne musimy zeskrobać. Zauważ, że w wyszukiwaniu metadanych pokazujemy oryginalne rekordy. Nie łączymy rekordów. Źródła tylko z metadanymi Open Library to projekt open source prowadzony przez Internet Archive, mający na celu katalogowanie każdej książki na świecie. Posiada jedną z największych na świecie operacji skanowania książek i wiele książek dostępnych do cyfrowego wypożyczania. Jego katalog metadanych książek jest dostępny do pobrania za darmo i jest uwzględniony w Archiwum Anny (choć obecnie nie w wyszukiwarce, chyba że wyraźnie wyszukasz identyfikator Open Library). Open Library Wykluczanie duplikatów Ostatnia aktualizacja Procenty liczby plików %% zmirrorowane przez AA / dostępne torrenty Rozmiar Źródło Poniżej znajduje się szybki przegląd źródeł plików w Archiwum Anny. Ponieważ biblioteki cieni często synchronizują dane między sobą, istnieje znaczne nakładanie się zasobów między bibliotekami. Dlatego liczby nie sumują się do całości. Procent „mirrorowane i seedowane przez Archiwum Anny” pokazuje, ile plików mirrorujemy sami. Seedujemy te pliki masowo przez torrenty i udostępniamy je do bezpośredniego pobrania przez strony partnerskie. Przegląd Razem Torrenty na Archiwum Anny Aby uzyskać więcej informacji na temat Sci-Hub, odwiedź jego <a %(a_scihub)s>oficjalną stronę</a>, <a %(a_wikipedia)s>stronę Wikipedii</a> oraz ten <a %(a_radiolab)s>wywiad w podcaście</a>. Zauważ, że Sci-Hub został <a %(a_reddit)s>zamrożony od 2021 roku</a>. Był zamrożony wcześniej, ale w 2021 roku dodano kilka milionów artykułów. Nadal jednak do kolekcji „scimag” w Libgen dodawana jest ograniczona liczba artykułów, choć nie na tyle, aby uzasadniać nowe masowe torrenty. Używamy metadanych Sci-Hub dostarczonych przez <a %(a_libgen_li)s>Libgen.li</a> w jego kolekcji „scimag”. Używamy również zestawu danych <a %(a_dois)s>dois-2022-02-12.7z</a>. Zauważ, że torrenty „smarch” są <a %(a_smarch)s>przestarzałe</a> i dlatego nie są uwzględnione na naszej liście torrentów. Torrenty na Libgen.li Torrenty na Libgen.rs Metadane i torrenty Aktualizacje na Reddit Wywiad w podcaście Strona Wikipedii Sci-Hub Sci-Hub: zamrożony od 2021; większość dostępna przez torrenty Libgen.li: drobne dodatki od tego czasu</div> Niektóre biblioteki źródłowe promują masowe udostępnianie swoich danych za pomocą torrentów, podczas gdy inne niechętnie dzielą się swoimi zbiorami. W tym drugim przypadku, Archiwum Anny stara się zeskrobać ich zbiory i udostępnić je (zobacz naszą stronę <a %(a_torrents)s>Torrenty</a>). Istnieją również sytuacje pośrednie, na przykład gdy biblioteki źródłowe są chętne do udostępniania, ale nie mają zasobów, aby to zrobić. W takich przypadkach również staramy się pomóc. Poniżej znajduje się przegląd, jak współpracujemy z różnymi bibliotekami źródłowymi. Biblioteki źródłowe %(icon)s Różne bazy danych plików rozproszone po chińskim internecie; często płatne bazy danych %(icon)s Większość plików dostępna tylko za pomocą kont premium BaiduYun; wolne prędkości pobierania. %(icon)s Archiwum Anny zarządza kolekcją <a %(duxiu)s>plików DuXiu</a> %(icon)s Różne bazy danych metadanych rozproszone po chińskim internecie; często płatne bazy danych %(icon)s Brak łatwo dostępnych zrzutów metadanych dla całej ich kolekcji. %(icon)s Archiwum Anny zarządza kolekcją <a %(duxiu)s>metadanych DuXiu</a> Pliki %(icon)s Pliki dostępne tylko do wypożyczenia na ograniczonych zasadach, z różnymi ograniczeniami dostępu %(icon)s Archiwum Anny zarządza kolekcją <a %(ia)s>plików IA</a> %(icon)s Niektóre metadane dostępne przez <a %(openlib)s>zrzuty bazy danych Open Library</a>, ale nie obejmują całej kolekcji IA %(icon)s Brak łatwo dostępnych zrzutów metadanych dla całej ich kolekcji %(icon)s Archiwum Anny zarządza kolekcją <a %(ia)s>metadanych IA</a> Ostatnia aktualizacja %(icon)s Archiwum Anny i Libgen.li wspólnie zarządzają kolekcjami <a %(comics)s>komiksów</a>, <a %(magazines)s>czasopism</a>, <a %(standarts)s>dokumentów standardowych</a> oraz <a %(fiction)s>fikcji (oddzielonej od Libgen.rs)</a>. %(icon)s Torrenty lit. faktu są udostępniane z Libgen.rs (i lustrzane <a %(libgenli)s>tutaj</a>). %(icon)s Kwartalne <a %(dbdumps)s>zrzuty bazy danych HTTP</a> %(icon)s Zautomatyzowane torrenty dla <a %(nonfiction)s>lit. faktu</a> i <a %(fiction)s>beletrystyki</a> %(icon)s Archiwum Anny zarządza kolekcją <a %(covers)s>torrenty okładek książek</a> %(icon)s Codzienne <a %(dbdumps)s>zrzuty bazy danych HTTP</a> Metadane %(icon)s Miesięczne <a %(dbdumps)s>zrzuty bazy danych</a> %(icon)s Torrenty danych dostępne <a %(scihub1)s>tutaj</a>, <a %(scihub2)s>tutaj</a> i <a %(libgenli)s>tutaj</a> %(icon)s Niektóre nowe pliki są <a %(libgenrs)s>dodawane</a> do „scimag” Libgen, ale nie na tyle, aby uzasadniać nowe torrenty %(icon)s Sci-Hub zamroził nowe pliki od 2021 roku. %(icon)s Zrzuty metadanych dostępne <a %(scihub1)s>tutaj</a> i <a %(scihub2)s>tutaj</a>, a także jako część <a %(libgenli)s>bazy danych Libgen.li</a> (której używamy) Źródło %(icon)s Różne mniejsze lub jednorazowe źródła. Zachęcamy ludzi do przesyłania do innych bibliotek cieni, ale czasami ludzie mają kolekcje, które są zbyt duże, aby inni mogli je przeglądać, choć nie na tyle duże, aby zasługiwały na własną kategorię. %(icon)s Niedostępne bezpośrednio w dużych ilościach, chronione przed skrobaniem %(icon)s Archiwum Anny zarządza kolekcją <a %(worldcat)s>metadanych OCLC (WorldCat)</a> %(icon)s Archiwum Anny i Z-Library wspólnie zarządzają kolekcją <a %(metadata)s>metadanych Z-Library</a> i <a %(files)s>plików Z-Library</a> Datasets Łączymy wszystkie powyższe źródła w jedną zunifikowaną bazę danych, którą wykorzystujemy do obsługi tej strony internetowej. Ta zunifikowana baza danych nie jest dostępna bezpośrednio, ale ponieważ Archiwum Anny jest w pełni open source, można ją dość łatwo <a %(a_generated)s>wygenerować</a> lub <a %(a_downloaded)s>pobrać</a> jako bazy danych ElasticSearch i MariaDB. Skrypty na tej stronie automatycznie pobiorą wszystkie wymagane metadane z wymienionych powyżej źródeł. Jeśli chcesz zbadać nasze dane przed uruchomieniem tych skryptów lokalnie, możesz spojrzeć na nasze pliki JSON, które linkują dalej do innych plików JSON. <a %(a_json)s>Ten plik</a> jest dobrym punktem wyjścia. Zunifikowana baza danych Torrenty od Archiwum Anny przeglądaj szukaj Różne mniejsze lub jednorazowe źródła. Zachęcamy ludzi do przesyłania do innych bibliotek cieni, ale czasami ludzie mają kolekcje, które są zbyt duże, aby inni mogli je przejrzeć, choć nie na tyle duże, aby uzasadniały własną kategorię. Przegląd z <a %(a1)s>strony datasets</a>. Z <a %(a_href)s>aaaaarg.fail</a>. Wydaje się być dość kompletne. Od naszego wolontariusza „cgiym”. Z <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentu. Ma dość duże nakładanie się z istniejącymi kolekcjami artykułów, ale bardzo mało dopasowań MD5, więc zdecydowaliśmy się zachować go w całości. Zbieranie danych z <q>iRead eBooks</q> (= fonetycznie <q>ai rit i-books</q>; airitibooks.com), przez wolontariusza <q>j</q>. Odpowiada <q>airitibooks</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>. Z kolekcji <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Częściowo z oryginalnego źródła, częściowo z the-eye.eu, częściowo z innych luster. Z prywatnej strony torrentowej z książkami, <a %(a_href)s>Bibliotik</a> (często nazywanej „Bib”), z której książki były pakowane w torrenty według nazw (A.torrent, B.torrent) i dystrybuowane przez the-eye.eu. Od naszego wolontariusza „bpb9v”. Więcej informacji o <a %(a_href)s>CADAL</a> znajdziesz w notatkach na naszej <a %(a_duxiu)s>stronie datasetu DuXiu</a>. Więcej od naszego wolontariusza „bpb9v”, głównie pliki DuXiu, a także foldery „WenQu” i „SuperStar_Journals” (SuperStar to firma stojąca za DuXiu). Od naszego wolontariusza „cgiym”, chińskie teksty z różnych źródeł (reprezentowane jako podkatalogi), w tym z <a %(a_href)s>China Machine Press</a> (głównego chińskiego wydawcy). Niechińskie kolekcje (reprezentowane jako podkatalogi) od naszego wolontariusza „cgiym”. Zbieranie danych o książkach na temat chińskiej architektury, przez wolontariusza <q>cm</q>: <q>Zdobyłem to, wykorzystując lukę w zabezpieczeniach sieci wydawnictwa, ale ta luka została już zamknięta</q>. Odpowiada <q>chinese_architecture</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>. Książki z akademickiego wydawnictwa <a %(a_href)s>De Gruyter</a>, zebrane z kilku dużych torrentów. Zrzut z <a %(a_href)s>docer.pl</a>, polskiej strony do udostępniania plików skupiającej się na książkach i innych pracach pisemnych. Zebrane pod koniec 2023 roku przez wolontariusza „p”. Nie mamy dobrych metadanych z oryginalnej strony (nawet rozszerzeń plików), ale przefiltrowaliśmy pliki przypominające książki i często byliśmy w stanie wyodrębnić metadane z samych plików. DuXiu epuby, bezpośrednio z DuXiu, zebrane przez wolontariusza „w”. Tylko najnowsze książki DuXiu są dostępne bezpośrednio jako ebooki, więc większość z nich musi być nowa. Pozostałe pliki DuXiu od wolontariusza „m”, które nie były w formacie PDG DuXiu (główna <a %(a_href)s>kolekcja DuXiu</a>). Zebrane z wielu oryginalnych źródeł, niestety bez zachowania tych źródeł w ścieżce pliku. <span></span> <span></span> <span></span> Zbieranie danych z książek erotycznych, przez wolontariusza <q>do no harm</q>. Odpowiada <q>hentai</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>. <span></span> <span></span> Kolekcja zebrana od japońskiego wydawcy mangi przez wolontariusza „t”. <a %(a_href)s>Wybrane archiwa sądowe Longquan</a>, dostarczone przez wolontariusza „c”. Zrzut z <a %(a_href)s>magzdb.org</a>, sojusznika Library Genesis (jest linkowany na stronie głównej libgen.rs), ale który nie chciał udostępnić swoich plików bezpośrednio. Uzyskane przez wolontariusza „p” pod koniec 2023 roku. <span></span> Różne małe przesyłki, zbyt małe, aby stanowiły własną podkolekcję, ale reprezentowane jako katalogi. Ebooki z AvaxHome, rosyjskiej strony do udostępniania plików. Archiwum gazet i czasopism. Odpowiada <q>newsarch_magz</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>. Zbieranie danych z <a %(a1)s>Philosophy Documentation Center</a>. Kolekcja wolontariusza „o”, który zbierał polskie książki bezpośrednio z oryginalnych stron wydawniczych („scene”). Połączone kolekcje <a %(a_href)s>shuge.org</a> przez wolontariuszy „cgiym” i „woz9ts”. <span></span> <a %(a_href)s>„Imperialna Biblioteka Trantora”</a> (nazwana na cześć fikcyjnej biblioteki), zeskrobana w 2022 roku przez wolontariusza „t”. <span></span> <span></span> <span></span> Pod-pod-kolekcje (reprezentowane jako katalogi) od wolontariusza „woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (przez <a %(a_sikuquanshu)s>Dizhi(迪志)</a> na Tajwanie), mebook (mebook.cc, 我的小书屋, moja mała biblioteczka — woz9ts: „Ta strona głównie skupia się na udostępnianiu wysokiej jakości plików ebooków, z których niektóre są składane przez samego właściciela. Właściciel został <a %(a_arrested)s>aresztowany</a> w 2019 roku i ktoś zrobił kolekcję plików, które udostępnił.”). Pozostałe pliki DuXiu od wolontariusza „woz9ts”, które nie były w zastrzeżonym formacie PDG DuXiu (jeszcze do konwersji na PDF). Kolekcja „upload” jest podzielona na mniejsze podkolekcje, które są oznaczone w AACID i nazwach torrentów. Wszystkie podkolekcje zostały najpierw zdeduplikowane w stosunku do głównej kolekcji, choć pliki JSON „upload_records” metadanych nadal zawierają wiele odniesień do oryginalnych plików. Pliki nieksiążkowe zostały również usunięte z większości podkolekcji i zazwyczaj <em>nie</em> są odnotowane w plikach JSON „upload_records”. Podkolekcje to: Notatki Podkolekcja Wiele podkolekcji składa się z pod-podkolekcji (np. z różnych oryginalnych źródeł), które są reprezentowane jako katalogi w polach „filepath”. Przesyłanie do Archiwum Anny Nasz wpis na blogu o tych danych <a %(a_worldcat)s>WorldCat</a> to własnościowa baza danych prowadzona przez organizację non-profit <a %(a_oclc)s>OCLC</a>, która agreguje rekordy metadanych z bibliotek na całym świecie. Jest to prawdopodobnie największa kolekcja metadanych bibliotecznych na świecie. W październiku 2023 roku <a %(a_scrape)s>opublikowaliśmy</a> kompleksowy zrzut bazy danych OCLC (WorldCat) w <a %(a_aac)s>formacie kontenerów Archiwum Anny</a>. Październik 2023, pierwsze wydanie: OCLC (WorldCat) Torrenty od Archiwum Anny Przykładowy rekord w Archiwum Anny (oryginalna kolekcja) Przykładowy rekord w Archiwum Anny (kolekcja „zlib3”) Torrenty od Archiwum Anny (metadane + zawartość) Post na blogu o Wydaniu 1 Post na blogu o Wydaniu 2 Pod koniec 2022 roku rzekomi założyciele Z-Library zostali aresztowani, a domeny zostały przejęte przez władze Stanów Zjednoczonych. Od tego czasu strona powoli wraca do sieci. Nie wiadomo, kto obecnie nią zarządza. Aktualizacja z lutego 2023. Z-Library ma swoje korzenie w społeczności <a %(a_href)s>Library Genesis</a> i początkowo korzystała z ich danych. Od tego czasu znacznie się profesjonalizowała i ma znacznie nowocześniejszy interfejs. Dzięki temu mogą uzyskać znacznie więcej darowizn, zarówno pieniężnych na dalsze ulepszanie swojej strony, jak i darowizn w postaci nowych książek. Zgromadzili dużą kolekcję oprócz Library Genesis. Kolekcja składa się z trzech części. Oryginalne strony opisowe dla pierwszych dwóch części są zachowane poniżej. Potrzebujesz wszystkich trzech części, aby uzyskać wszystkie dane (z wyjątkiem zastąpionych torrentów, które są przekreślone na stronie torrentów). %(title)s: nasze pierwsze wydanie. Było to pierwsze wydanie tego, co wtedy nazywało się „Pirate Library Mirror” („pilimi”). %(title)s: drugie wydanie, tym razem ze wszystkimi plikami zapakowanymi w pliki .tar. %(title)s: incrementalne nowe wydania, używające formatu <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, teraz wydawane we współpracy z zespołem Z-Library. Początkowy mirror został mozolnie uzyskany w ciągu 2021 i 2022 roku. W tym momencie jest nieco przestarzały: odzwierciedla stan kolekcji z czerwca 2021 roku. Zaktualizujemy to w przyszłości. Obecnie koncentrujemy się na wydaniu tego pierwszego wydania. Ponieważ Library Genesis jest już zachowane z publicznymi torrentami i jest uwzględnione w Z-Library, przeprowadziliśmy podstawową deduplikację względem Library Genesis w czerwcu 2022 roku. Do tego użyliśmy skrótów MD5. Prawdopodobnie w bibliotece jest dużo więcej zduplikowanej zawartości, takiej jak wiele formatów plików z tą samą książką. Trudno to dokładnie wykryć, więc tego nie robimy. Po deduplikacji pozostało nam ponad 2 miliony plików, o łącznej wielkości nieco poniżej 7TB. Kolekcja składa się z dwóch części: zrzutu MySQL „.sql.gz” metadanych oraz 72 plików torrent o wielkości od około 50 do 100GB każdy. Metadane zawierają dane zgłoszone przez stronę Z-Library (tytuł, autor, opis, typ pliku), a także rzeczywisty rozmiar pliku i md5sum, które zaobserwowaliśmy, ponieważ czasami te dane się nie zgadzają. Wydaje się, że istnieją zakresy plików, dla których sama Z-Library ma nieprawidłowe metadane. W niektórych odosobnionych przypadkach mogliśmy również pobrać pliki nieprawidłowo, co postaramy się wykryć i naprawić w przyszłości. Duże pliki torrent zawierają rzeczywiste dane książek, z identyfikatorem Z-Library jako nazwą pliku. Rozszerzenia plików można odtworzyć za pomocą zrzutu metadanych. Kolekcja jest mieszanką treści literatury faktu i beletrystyki (nie oddzielonych jak w Library Genesis). Jakość również jest bardzo zróżnicowana. To pierwsze wydanie jest teraz w pełni dostępne. Należy zauważyć, że pliki torrent są dostępne tylko przez nasz mirror w sieci Tor. Wydanie 1 (%(date)s) To jest pojedynczy dodatkowy plik torrent. Nie zawiera żadnych nowych informacji, ale ma pewne dane, które mogą zająć trochę czasu na obliczenie. To sprawia, że jest wygodny, ponieważ pobranie tego torrenta jest często szybsze niż obliczenie go od zera. W szczególności zawiera indeksy SQLite dla plików tar, do użycia z <a %(a_href)s>ratarmount</a>. Dodatek do wydania 2 (%(date)s) Uzyskaliśmy wszystkie książki, które zostały dodane do Z-Library między naszym ostatnim mirrorem a sierpniem 2022 roku. Cofnęliśmy się również i zeskrobaliśmy niektóre książki, które przegapiliśmy za pierwszym razem. W sumie nowa kolekcja ma około 24TB. Ponownie, ta kolekcja jest deduplikowana względem Library Genesis, ponieważ dla tej kolekcji są już dostępne torrenty. Dane są zorganizowane podobnie jak w pierwszym wydaniu. Jest zrzut MySQL „.sql.gz” metadanych, który zawiera również wszystkie metadane z pierwszego wydania, zastępując je. Dodaliśmy również kilka nowych kolumn: Wspomnieliśmy o tym ostatnim razem, ale dla jasności: „filename” i „md5” to rzeczywiste właściwości pliku, podczas gdy „filename_reported” i „md5_reported” to dane, które zeskrobaliśmy z Z-Library. Czasami te dwie wartości się nie zgadzają, więc uwzględniliśmy obie. Dla tego wydania zmieniliśmy porządek sortowania na „utf8mb4_unicode_ci”, który powinien być kompatybilny ze starszymi wersjami MySQL. Pliki danych są podobne do tych z poprzedniego razu, choć są znacznie większe. Po prostu nie chciało nam się tworzyć mnóstwa mniejszych plików torrent. „pilimi-zlib2-0-14679999-extra.torrent” zawiera wszystkie pliki, które przegapiliśmy w poprzednim wydaniu, podczas gdy pozostałe torrenty to nowe zakresy ID.  <strong>Aktualizacja %(date)s:</strong> Zrobiliśmy większość naszych torrentów zbyt dużymi, co powodowało problemy z klientami torrent. Usunęliśmy je i wydaliśmy nowe torrenty. <strong>Aktualizacja %(date)s:</strong> Nadal było zbyt wiele plików, więc spakowaliśmy je w pliki tar i ponownie wydaliśmy nowe torrenty. %(key)s: czy ten plik jest już w Library Genesis, w kolekcji literatury faktu lub beletrystyki (dopasowane przez md5). %(key)s: w którym torrencie znajduje się ten plik. %(key)s: ustawione, gdy nie udało nam się pobrać książki. Wydanie 2 (%(date)s) Wydania Zlib (oryginalne strony opisowe) Domena Tor Główna strona Z-Library scrape Kolekcja „chińska” w Z-Library wydaje się być taka sama jak nasza kolekcja DuXiu, ale z różnymi MD5. Wykluczamy te pliki z torrentów, aby uniknąć duplikacji, ale nadal pokazujemy je w naszym indeksie wyszukiwania. Metadane Otrzymujesz %(percentage)s%% szybkich pobrań, ze względu na polecenie przez użytkownika %(profile_link)s. Dotyczy to całego okresu członkostwa. Przekaż darowiznę Dołącz Wybrane do %(percentage)s%% zniżki Alipay obsługuje międzynarodowe karty płatnicze. Sprawdź <a %(a_alipay)s>ten przewodnik</a> aby uzyskać więcej informacji. Wyślij nam karty podarunkowe Amazon.com używając karty płatniczej. Możesz zakupić kryptowaluty używając karty płatniczej. WeChat (Weixin Pay) obsługuje międzynarodowe karty płatnicze. W aplikacji WeChat przejdź do opcji „Me → Services → Wallet → Add a Card”. Jeśli tego nie widzisz, włącz tę opcję, przechodząc do „Me → Settings → General → Tools → Weixin Pay → Enable”. (użyj podczas wysyłania Ethereum przy pomocy Coinbase) skopiowano! kopiuj (najniższa możliwa kwota) (uwaga: wymagana wysoka kwota minimalna) -%(percentage)s%% 12 miesięcy Miesiąc 24 miesiące 3 miesiące 48 miesięcy 6 miesięcy 96 miesięcy Wybierz jak długo zamierzasz nas wspierać. <div %(div_monthly_cost)s></div><div %(div_after)s>Po <span %(span_discount)s></span> rabacie</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% za 12 miesięcy za miesiąc za 24 miesiące za 3 miesiące za 48 miesięcy za 6 miesięcy za 96 miesięcy %(monthly_cost)s / miesiąc skontaktuj się z nami Bezpośredni dostęp do serwerów<strong>SFTP</strong> Darowizna na poziomie przedsiębiorstwa lub wymiana na nowe kolekcje (np. nowe skany, zbiory danych OCR). Tytuł i dostęp ekspercki <strong>Nieograniczony</strong> dostęp do szybkich łączy <div %(div_question)s>Czy mogę ulepszyć swoje członkostwo lub uzyskać wiele członkostw?</div> <div %(div_question)s>Czy mogę przekazać darowiznę bez zostania członkiem?</div> Oczywiście. Akceptujemy darowizny w dowolnej kwocie na ten adres Monero (XMR): %(address)s. <div %(div_question)s>Co oznaczają zakresy na miesiąc?</div> Możesz osiągnąć dolną granicę zakresu, stosując wszystkie zniżki, takie jak wybór okresu dłuższego niż miesiąc. <div %(div_question)s> Czy członkostwo zostaje automatycznie odnowione?</div> Członkostwa <strong> nie są </strong> automatycznie odnawiane. Możesz dołączyć na krótki, bądź długi okres, w zależności od potrzeby. <div %(div_question)s> Na co przeznaczacie darowizny?</div> 100%% środków jest przeznaczane na utrwalanie i udostępnianie wiedzy oraz kultury całego świata. Obecnie zostają one przeznaczane na serwery, przestrzeń dyskową oraz łącza. Członkowie projektu nie otrzymują wynagrodzenia. <div %(div_question)s>Czy mogę przelać większe kwoty?</div> Byłoby to niesamowite! Dla kwot powyżej kilku tysięcy dolarów, prosimy o kontakt bezpośredni pod %(email)s. <div %(div_question)s>Czy obsługujecie inne metody płatności?</div> Obecnie nie. Wielu ludzi nie chce, aby istniały archiwa takie jak nasze, więc ciągle musimy zachowywać ostrożność. Jeżeli jesteś w stanie pomóc nam bezpiecznie obsługiwać inne (zdecydowanie bardziej wygodne) metody płatności, skontaktuj się z nami pod %(email)s. Najczęściej zadawane pytania - Wsparcie pieniężne Twoja <a %(a_donation)s>płatność</a> jest przetwarzana. Dokończ lub anuluj transakcję przed wykonaniem nowej. <a %(a_all_donations)s> Wyświetl wszystkie płatności</a> Przy wsparciu projektu kwotą powyżej 5000$, prosimy o kontakt pod adresem %(email)s. Z radością przyjmiemy wpłaty od zamożnych osób indywidualnych i instytucji.  Należy pamiętać, że chociaż członkostwa na tej stronie są „miesięczne”, są to jednorazowe darowizny (nieodnawialne). Zobacz <a %(faq)s>Najczęściej zadawane pytania</a>. Anna's Archive jest projektem opierającym się o otwarte źródła i otwarty dostęp do danych. Projekt nie jest tworzony w celu osiągnięcia zysku, w związku z czym wspierając nas finansowo pomagasz nam kontynuować dalsze działanie i rozwój. Wszystkim członkom dziękujemy za bycie z nami!❤️ Aby uzyskać więcej informacji, sprawdź <a %(a_donate)s> Najczęściej zadawane pytania o płatnościach</a>. Aby zostać członkiem, prosimy się <a %(a_login)s>Zalogować lub Zarejestrować</a>. Dziękujemy za wsparcie! $%(cost)s / miesiąc Jeśli popełniłeś błąd podczas płatności, nie możemy dokonać zwrotu, ale postaramy się to naprawić. Odnajdź stronę "Kryptowaluty" w swojej aplikacji PayPal lub na stronie internetowej. Zazwyczaj jest ona zlokalizowana pod zakładką "Finanse". Przejdź do podstrony "Bitcoin" znajdującej się w aplikacji PayPal lub na stronie Internetowej. Naciśnij przycisk %(transfer_icon)s "Transfer", a następnie "Send". Alipay Alipay 支付宝 / WeChat 微信 Karta Podarunkowa Amazon Karta podarunkowa %(amazon)s Karta płatnicza Karta płatnicza (przy użyciu aplikacji) Binance Apple/Google (BMC)/Karta kredytowa/debetowa CashApp (aplikacja) Karta kredytowa/debetowa Karta płatnicza 2 Karta płatnicza (zapasowa) Kryptowaluty %(bitcoin_icon)s Karta płatnicza / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (zwykły) Pix (Brazylia) Revolut (na tę chwilę niedostępny) WeChat Wybierz kryptowalutę, którą preferujesz: Wspomóż za pomocą karty podarunkowej Amazon. <strong>WAŻNE:</strong> Ta opcja jest przeznaczona dla %(amazon)s. Jeśli chcesz użyć innej strony Amazon, wybierz ją powyżej. <strong>WAŻNE:</strong> Mamy wsparcie tylko na Amazon.com, nie obsługujemy domen krajowych, takich jak: .de, .co.uk, .ca i innych. NIE pisz własnych wiadomości. Wprowadź dokładną kwotę: %(amount)s Musimy zaokrąglać płatności do wartości akceptowanych przez naszych partnerów handlowych (minimum %(minimum)s). Przekaż darowiznę za pomocą karty płatniczej, używając aplikacji Alipay (bardzo łatwej do skonfigurowania). Zainstaluj aplikację Alipay przy pomocy <a %(a_app_store)s>Apple App Store</a> lub <a %(a_play_store)s>Google Play Store</a>. Zarejestruj się, używając swojego numeru telefonu. Nie są wymagane dodatkowe dane osobowe. <span %(style)s>1</span>Zainstaluj aplikację Alipay Obsługiwane: Visa, MasterCard, JCB, Diners Club i Discover. Sprawdź <a %(a_alipay)s>ten przewodnik</a> aby uzyskać więcej informacji. <span %(style)s>2</span>Dodaj kartę płatniczą Za pomocą Binance możesz zakupić Bitcoin przy pomocy karty płatniczej lub konta bankowego, który następnie możesz nam przekazać. W ten sposób możemy pozostać bezpieczni i anonimowi przy Twojej wpłacie. Binance jest dostępny w prawie każdym kraju i obsługuje większość banków oraz kart płatniczych. Polecamy przede wszystkim tę metodę. Doceniamy, że poświęcasz czas na naukę, jak przekazać darowiznę przy pomocy tej metody, ponieważ jest to nam pomocne. Dla kart płątniczych, Apple Pay i Google Pay używamy systemu „Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Używając tej metody, jedna „kawa” jest równa 5$, przez co Twoja darowizna zostanie zaokrąglona do najbliższej wielokrotności 5. Wspomóż za pomocą Cash App. Wsparcie za pomocą Cash App będzie najlepszą i najprostszą metodą wsparcia naszej działalności! Zwróć uwagę, że przy transakcjach poniżej %(amount)s, Cash App może pobierać opłatę w wysokości %(fee)s. Dla %(amount)s lub więcej usługa jest bezpłatna! Wspomóż za pomocą karty płatniczej. Ta metoda wykorzystuje dostawcę kryptowalut jako podmiot przeprowadzający konwersję pośrednią między walutami. Mechanizm działania może być kłopotliwy, więc prosimy o korzystanie ze sposobu tylko wtedy, gdy inne metody płatności zawiodą. Nie działa we wszystkich krajach. Nie możemy bezpośrednio obsługiwać kart płatniczych, ponieważ banki nie chcą z nami współpracować ☹. Jednakże istnieje kilka sposobów na użycie kart płatniczych przy pomocy innych sposobów płatności: Przy wyborze płatności kryptowalutami możliwym jest wsparcie przy pomocy BTC, ETH, XMR lub SOL. Wybierz tę opcję, jeżeli posiadasz wiedzę na temat kryptowalut. Przy płatnościach kryptowalutami można użyć BTC, ETH, XMR i innych. Usługi ekspresowe Crypto Jeżeli używasz kryptowalut po raz pierwszy, sugerujemy użycie %(options)s do zakupu i przekazania darowizny w Bitcoinie (najstarszej i najczęściej używanej kryptowalucie). Dla mniejszych darowizn opłaty z kart kredytowych mogą wyeliminować naszą zniżkę w wysokości %(discount)s, więc zalecamy dłuższe subskrypcje. Przekaż darowiznę za pomocą karty płatniczej, PayPala lub Venmo. Możesz wybrać spośród tych opcji na następnej stronie. Google Pay i Apple Pay też mogą zadziałać. Dla mniejszych darowizn opłaty są wysokie, więc zalecamy dłuższe subskrypcje. By wspomóc nas przez PayPal, należy użyć PayPal Crypto, które pozwoli nam na pozostanie anonimowymi. Doceniamy Twój wkład w nauczenie się sposobu wykonywania płatności tą metodą, to bardzo nam pomaga. Wspomóż za pomocą PayPal. Przekaż darowiznę za pomocą swojego zwykłego konta PayPal. Przekaż darowiznę za pomocą Revolut. Jeżeli posiadasz Revolut, przekazanie darowizny za jego pomocą będzie najprostsze! Ta metoda płatności umożliwia przekazanie maksimum %(amount)s. Proszę wybierz inny okres lub metodę płatności. Ta metoda płatności wymaga minimum %(amount)s. Proszę wybierz inny okres lub metodę płatności. Binance Coinbase Kraken Wybierz metodę płatności. "Adoptuj plik .torrent": Twoja nazwa użytkownika lub wiadomość zawarta w nazwie pliku .torrent <div %(div_months)s>jednorazowo co 12 miesięcy przy aktywnym członkostwie</div> Twój pseudonim lub anonimowa wzmianka na liście darczyńców Wcześniejszy dostęp do nowych funkcji Dostęp do grupy na Telegramie z informacjami "zza kulis" dotyczących aktualizacji projektu %(number)s szybkich pobierań dziennie jeśli dokonasz płatności w tym miesiącu! <a %(a_api)s>Dostęp do API JSON</a> Odznaczenie jako zasłużony za zachowywanie dziedzictwa, wiedzy i kultury ludzkości Zestaw korzyści z niższego progu oraz: Zdobądź <strong>%(percentage)s%% dodatkowych pobrań</strong><a %(a_refer)s>zapraszając znajomych</a>. <strong>Nielimitowany</strong> dostęp do badań naukowych z SciDB bez weryfikacji Podczas zadawania pytań dotyczących konta lub darowizn, dodaj swój identyfikator konta, zrzuty ekranu, paragony, jak najwięcej informacji. Sprawdzamy nasz e-mail co 1-2 tygodnie, więc brak tych informacji opóźni rozwiązanie problemu. <a %(a_refer)s>Poleć nas znajomym</a>, aby uzyskać większy limit pobrań! Jesteśmy małą grupą wolontariuszy. Na odpowiedź możesz poczekać od 7 do 14 dni. Nazwa lub zdjęcie konta może wydać się podejrzane, lecz nie ma powodu do obaw! Konta te są wykorzystywane przez naszych partnerów do obsługi darczyńców. Nie są ofiarą wycieku danych. Wspomóż <span %(span_cost)s></span> <span %(span_label)s></span> za 12 miesięcy "%(tier_name)s" za miesiąc “%(tier_name)s” za 24 miesiące "%(tier_name)s" za 3 miesiące “%(tier_name)s” za 48 miesięcy „%(tier_name)s” za 6 miesięcy "%(tier_name)s" za 96 miesięcy „%(tier_name)s” Wciąż możesz wycofać płatność podczas jej realizacji. Naciśnij przycisk "Wspomóż" aby potwierdzić wsparcie. <strong>Ważna uwaga;</strong> Ceny kryptowalut mogą zmieniać się z godziny na godzinę, w niektórych momentach ich różnica może wynosić nawet 20%% w ciągu paru minut. Mimo wszystko jest to mniej niż zapłacilibyśmy u wielu operatorów płatności, którzy często obciążają "skryte organizacje charytatywne" takie jak nasza w granicach od 50 do 60 %% kwoty wpłat. <u>Jeżeli wyślesz nam informację o kwocie, która była wyświetlana w chwili przelewu, a różniłaby się znacznie (byłaby za niska lub za wysoka) od progu który został wybrany, członkostwo zostanie zmienione na wybrane podczas dokonywania płatności</u> (do momentu, w którym informacja o płatności nie będzie starsza niż kilka godzin). Doceniamy wysiłek włożony w to, aby nas wspierać! ❤️ ❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie. <span %(span_circle)s>1</span>Zakup Bitcoin na platformie PayPal <span %(span_circle)s> 2 </span> Przekaż Bitcoiny na nasz adres ✅ Przekierowywanie na stronę wsparcia finansowego projektu… Wspomóż Proszę poczekać co najmniej <span %(span_hours)s>24 godziny</span> (i odświeżyć tę stronę) przed skontaktowaniem się z nami. Jeśli chcesz dokonać wpłaty (w dowolnej wysokości) bez uzyskiwania członkostwa, możesz użyć tego adresu kryptowaluty Monero (XMR): %(address)s. Po wysłaniu karty podarunkowej system potwierdzi ją w ciągu kilku minut. Jeśli to nie zadziała, spróbuj ponownie wysłać kartę podarunkową (<a %(a_instr)s>instrukcje</a>). Jeśli to nadal nie zadziała, napisz do nas, a ręcznie sprawdzimy zgłoszenie (może to potrwać kilka dni) i pamiętaj, aby wspomnieć, czy próbowałeś już ponownie wysłać zgłoszenie. Przykład: Skorzystaj z <a %(a_form)s>oficjalnego formularza Amazon.com</a>, aby wysłać nam kartę podarunkową o wartości %(amount)s na podany adres e-mail. Adres e-mail odbiorcy "Do" w formularzu: Karta podarunkowa Amazon Nie akceptujemy innych metod wysyłania kart podarunkowych, <strong>tylko wysłane bezpośrednio z oficjalnego formularza na stronie Amazon.com</strong>. Nie będziemy mogli zwrócić karty podarunkowej, jeśli nie skorzystasz z tego formularza. Użyj tylko raz. Unikatowe dla twojego konta, nie udostępniaj. Oczekiwanie na kartę podarunkową... (odśwież stronę, aby sprawdzić) Otwórz <a %(a_href)s>stronę darowizny z kodem QR</a>. Zeskanuj kod QR za pomocą aplikacji Alipay lub naciśnij przycisk, aby otworzyć aplikację Alipay. Prosimy o cierpliwość; strona może się ładować dłużej, ponieważ znajduje się w Chinach. <span %(style)s>3</span>Dokonaj darowizny (zeskanuj kod QR lub naciśnij przycisk) Kup PYUSD na PayPal Kup Bitcoin (BTC) w aplikacji Cash App Kup trochę więcej (zalecamy %(more)s więcej) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Resztę zachowasz dla siebie. Przejdź do strony „Bitcoin” (BTC) w aplikacji Cash App. Przelej Bitcoin na nasz adres W przypadku małych darowizn (poniżej $25) może być konieczne użycie Rush lub Priority. Kliknij przycisk „Wyślij bitcoin”, aby dokonać „wypłaty”. Przełącz z dolarów na BTC, naciskając ikonę %(icon)s. Wprowadź poniżej kwotę BTC i kliknij „Wyślij”. Jeśli utkniesz, zobacz <a %(help_video)s>ten film</a>. Usługi ekspresowe są wygodne, lecz pobierane są przy ich pomocy wyższe opłaty. W zamian można użyć giełdy kryptowalut aby szybko dokonać większej darowizny i nie masz nic przeciwko opłacie w wysokości od 5 do 10$. Upewnij się, że wysyłasz dokładną kwotę kryptowaluty pokazaną na stronie darowizny, a nie kwotę w walucie lokalnej. W przeciwnym razie opłata zostanie pominięta i nie będziemy mogli automatycznie aktywować Twojego członkostwa. Czasami potwierdzenie może zająć do 24 godzin, więc należy pamiętać o tym, aby strona została odświeżona (nawet jeśli wygasła ze względu wcześniejszego zaksięgowania transakcji). Instrukcje dot. kart płatniczych Przekaż darowiznę przy pomocy karty płatniczej za pośrednictwem naszej strony Niektóre z kroków wspominają o portfelach kryptowalutowych, ale nie martw się, nie musisz uczyć się niczego o kryptowalutach. Instrukcje %(coin_name)s Zeskanuj ten kod QR za pomocą aplikacji Portfel Crypto, aby szybko wypełnić szczegóły płatności Zeskanuj kod QR, aby zapłacić Obsługujemy tylko standardową wersję kryptowalut, bez egzotycznych sieci lub wersji monet. Potwierdzenie transakcji może zająć do godziny, w zależności od monety. Wpłać %(amount)s na <a %(a_page)s>tej stronie</a>. Ta darowizna wygasła. Prosimy anulować i utworzyć nową. Jeśli już zapłaciłeś: Tak, wysłałem moje potwierdzenie Jeżeli kurs wymiany kryptowalut zmienił się podczas transakcji, upewnij się, czy załączone zostało potwierdzenie pokazujące oryginalną kwotę wymiany. Doceniamy Twój wysiłek włożony w użytkowanie kryptowalut, wiele to dla nas znaczy! ❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie. <span %(span_circle)s>%(circle_number)s</span> Wyślij potwierdzenie transakcji na adres e-mail Jeśli napotkałeś na problemy, prosimy skontaktować się na %(email)s w treści zawierając tak dużo informacji jak to możliwe (np. zrzuty ekranu). ✅ Dziękujemy za Twoją darowiznę! Anna aktywuje Twoje członkostwo w sposób manualny w ciągu kilku dni. Wyślij potwierdzenie transakcji lub zrzut ekranu na indywidualny adres do weryfikacji: Gdy potwierdzenie zostało wysłane, naciśnij ten przycisk, aby Anna mogła go ręcznie zweryfikować (może to potrwać kilka dni): Wyślij paragon lub zrzut ekranu na swój osobisty adres weryfikacyjny. NIE używaj tego adresu e-mail do darowizny przez PayPal. Anuluj Tak, chcę anulować Czy na pewno chcesz anulować? Anulowanie opłaconej transakcji nie jest wskazane. ❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie. Przekaż nową darowiznę ✅ Twoja darowizna została anulowana. Data: %(date)s Identyfikator: %(id)s Wykonaj ponownie Stan: <span %(span_label)s>%(label)s</span> Suma: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / miesiąc za okres %(duration)s miesięcy, wraz ze%(discounts)s zniżką)</span> Suma: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / miesiąc za okres %(duration)s miesięcy)</span> 1. Podaj swój e-mail. 2. Wybierz formę płatności. 3. Wybierz ponownie formę płatności. 4. Wybierz portfel "Self-hosted". 5. Kliknij “I confirm ownership”. 6. Powinieneś otrzymać potwierdzenie e-mailem. Prześlij je do nas, a my jak najszybciej potwierdzimy twoją darowiznę. (możesz anulować i utworzyć nową darowiznę) Instrukcje dotyczące płatności stały się nieaktualne. Jeżeli chcesz ponownie przekazać darowiznę, naciśnij przycisk "Wykonaj ponownie". Darowizna opłacona. Jeżeli chcesz zobaczyć ponownie instrukcje dotyczące płatności, naciśnij tutaj: Wyświetl nieaktualne instrukcje odnośnie płatności Jeśli strona darowizn zostanie zablokowana, spróbuj innego połączenia internetowego (np. VPN lub internet w telefonie). Niestety, strona Alipay jest często dostępna tylko z <strong>Chin kontynentalnych</strong>. Może być konieczne tymczasowe wyłączenie VPN lub użycie VPN do Chin kontynentalnych (czasami działa również Hongkong). <span %(span_circle)s> 1 </span> Darowizna poprzez Alipay Przekaż łączną kwotę %(total)s za pomocą <a %(a_account)s>tego konta Alipay</a> Instrukcje dotyczące Alipay <span %(span_circle)s>1</span> Przekaż środki na jedno z naszych kont kryptowalutowych Przekaż darowiznę równą %(total)s na jeden z poniższych adresów: Instrukcje dotyczące kryptowalut Podążaj za instrukcjami aby zakupić Bitcoin (BTC). Wystarczającym jest kupienie takiej ilości kryptowaluty, która będzie przekazana w darowiznie, %(total)s. Wprowadź nasz adres Bitcoin (BTC) jako odbiorcę, a następnie podążaj za instrukcjami aby wysłać swoją darowiznę, %(total)s: <span %(span_circle)s> 1 </span> Przekaż darowiznę poprzez Pix Przekaż darowiznę równą %(total)s używając <a %(a_account)s> poniższego konta Pix Instrukcje dotyczące Pix <span %(span_circle)s>1</span>Przekaż darowiznę na WeChat Przekaż łączną kwotę %(total)s za pomocą <a %(a_account)s>tego konta WeChat</a> Instrukcje WeChat Użyj dowolnej z poniższych usług „karta kredytowa na Bitcoin”, które zajmują tylko kilka minut: Adres Bitcoin/BTC (zewnętrzny portfel): Kwota BTC / Bitcoin: Wypełnij poniższe dane w formularzu: Jeśli którakolwiek z tych informacji jest nieaktualna, prosimy o ich aktualizację kontaktując się z nami przy pomocy poczty elektronicznej. Proszę użyć <span %(underline)s>dokładnie tej kwoty</span>. Całkowity koszt może być wyższy z powodu opłat za kartę kredytową. W przypadku małych kwot może to niestety przewyższać naszą zniżkę. (minimum: %(minimum)s, brak weryfikacji przy pierwszej transakcji) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, brak weryfikacji przy pierwszej transakcji) (minimum: %(minimum)s) (minimum: %(minimum)s w zależności od kraju, brak weryfikacji przy pierwszej transakcji) Postępuj zgodnie z instrukcjami, aby kupić PYUSD (PayPal USD). Kup nieco więcej (zalecamy %(more)s) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Zatrzymasz wszystko, co pozostało. Przejdź do strony "PYUSD" w aplikacji lub witrynie PayPal. Naciśnij przycisk "Przelew" %(icon)s, a następnie "Wyślij". Aktualizacja stanu Aby zresetować licznik, wystarczy utworzyć nową darowiznę. Upewnij się, że używasz poniższej kwoty związanej z BTC, <em>NIE</em> natomiast Euro, tudzież dolarów, w przeciwnym razie nie otrzymamy poprawnej kwoty i nie będziemy mogli automatycznie potwierdzić Twojego członkostwa. Kup Bitcoin (BTC) w Revolut Kup trochę więcej (sugerujemy powiększenie salda o %(more)s więcej ) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Resztę zachowasz dla siebie. Przejdź do strony „Crypto” w aplikacji Revolut , aby kupić Bitcoin (BTC). Przelej kryptowalutę Bitcoin na nasz adres W przypadku małych darowizn (poniżej $25) może być konieczne użycie Rush lub Priority. Naciśnij przycisk „Wyślij bitcoin”, aby dokonać "przelewu". Przełącz z Euro na BTC, naciskając ikonę %(icon)s. Wprowadź poniżej kwotę BTC i kliknij „Wyślij”. Jeśli utkniesz, zapoznaj się z tym <a %(help_video)s> filmem</a>. Stan: 1 2 Instrukcja krok-po-kroku Zobacz przewodnik krok po kroku poniżej. W innym wypadku możesz utracić dostęp do tego konta! Zapisz swój tajny klucz do logowania, jeżeli jeszcze tego nie zrobiłeś: Dziękujemy za wsparcie finansowe! Pozostały czas: Darowizna Przekaż %(amount)s do %(account)s Oczekiwanie na potwierdzenie (odśwież stronę, aby sprawdzić)… Oczekiwanie na przelew (odśwież stronę żeby sprawdzić)… Wcześniej Szybkie pobrania w ciągu ostatnich 24 godzin liczą się do dziennego limitu. Pobrania z szybkich serwerów partnerskich są oznaczone przez %(icon)s. Ostatnie 18 godzin Nie pobrano plików. Informacja o pobranych plikach nie jest wyświetlana publicznie. Wszystkie czasy są w UTC. Pobrane pliki Jeśli pobrałeś plik zarówno z szybkimi, jak i wolnymi pobraniami, pojawi się on dwukrotnie. Nie martw się zbytnio, wiele osób pobiera z witryn, do których linkujemy, i niezwykle rzadko zdarza się, aby ktoś miał z tego powodu problemy. Jednak dla bezpieczeństwa zalecamy korzystanie z VPN (płatnego) lub <a %(a_tor)s>Tor</a> (darmowego). Pobrałem "1984" George'a Orwella, czy policja zapuka do moich drzwi? Ty jesteś Anną! Kim jest Anna? Mamy stabilne API JSON dla członków, umożliwiające uzyskanie szybkiego URL do pobrania: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacja w samym JSON). Do innych zastosowań, takich jak iterowanie przez wszystkie nasze pliki, budowanie niestandardowych wyszukiwarek i tak dalej, zalecamy <a %(a_generate)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB. Surowe dane można ręcznie przeglądać <a %(a_explore)s>poprzez pliki JSON</a>. Naszą surową listę torrentów można również pobrać jako <a %(a_torrents)s>JSON</a>. Czy macie API? Nie hostujemy tutaj żadnych materiałów objętych prawami autorskimi. Jesteśmy wyszukiwarką i jako taka indeksujemy tylko metadane, które są już publicznie dostępne. Podczas pobierania z tych zewnętrznych źródeł, sugerujemy sprawdzenie przepisów w Twojej jurysdykcji w odniesieniu do tego, co jest dozwolone. Nie ponosimy odpowiedzialności za treści hostowane przez innych. Jeśli masz skargi dotyczące tego, co widzisz tutaj, najlepiej skontaktować się z oryginalną stroną internetową. Regularnie wciągamy ich zmiany do naszej bazy danych. Jeśli naprawdę uważasz, że masz uzasadnioną skargę DMCA, na którą powinniśmy odpowiedzieć, wypełnij <a %(a_copyright)s>formularz zgłoszenia DMCA / naruszenia praw autorskich</a>. Traktujemy Twoje skargi poważnie i skontaktujemy się z Tobą tak szybko, jak to możliwe. Jak zgłosić naruszenie praw autorskich? Oto kilka książek, które mają szczególne znaczenie dla świata bibliotek cieni i cyfrowej archiwizacji: Jakie są Twoje ulubione książki? Chcielibyśmy również przypomnieć wszystkim, że cały nasz kod i dane są całkowicie open source. To wyjątkowe dla projektów takich jak nasz — nie znamy żadnego innego projektu z równie ogromnym katalogiem, który byłby również w pełni open source. Bardzo chętnie przyjmiemy każdego, kto uważa, że źle prowadzimy nasz projekt, aby wziął nasz kod i dane i założył własną bibliotekę cieni! Nie mówimy tego złośliwie — naprawdę uważamy, że byłoby to niesamowite, ponieważ podniosłoby to poprzeczkę dla wszystkich i lepiej zachowało dziedzictwo ludzkości. Nienawidzę, jak prowadzicie ten projekt! Chcielibyśmy, aby ludzie zakładali <a %(a_mirrors)s>lustrzane kopie</a>, a my będziemy to finansowo wspierać. Jak mogę pomóc? Zgadza się. Naszą inspiracją do zbierania metadanych jest cel Aarona Swartza „jedna strona internetowa dla każdej książki, jaka kiedykolwiek została opublikowana”, dla którego stworzył <a %(a_openlib)s>Open Library</a>. Ten projekt odniósł sukces, ale nasza unikalna pozycja pozwala nam uzyskać metadane, których oni nie mogą. Inną inspiracją była nasza chęć poznania <a %(a_blog)s>ile książek jest na świecie</a>, abyśmy mogli obliczyć, ile książek jeszcze musimy uratować. Czy zbieracie metadane? Zauważ, że mhut.org blokuje pewne zakresy IP, więc może być wymagane użycie VPN. <strong>Android:</strong> Kliknij menu z trzema kropkami w prawym górnym rogu i wybierz „Dodaj do ekranu głównego”. <strong>iOS:</strong> Kliknij przycisk „Udostępnij” na dole i wybierz „Dodaj do ekranu głównego”. Nie mamy oficjalnej aplikacji mobilnej, ale możesz zainstalować tę stronę jako aplikację. Czy macie aplikację mobilną? Proszę wysłać je do <a %(a_archive)s>Internet Archive</a>. Zostaną one odpowiednio zachowane. Jak mogę przekazać książki lub inne materiały fizyczne? Jak mogę zamówić książki? <a %(a_blog)s>Blog Anny</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regularne aktualizacje <a %(a_software)s>Oprogramowanie Anny</a> — nasz kod open source <a %(a_datasets)s>Datasets</a> — o danych <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatywne domeny Czy są dostępne dodatkowe zasoby dotyczące Archiwum Anny? <a %(a_translate)s>Tłumacz w Oprogramowaniu Anny</a> — nasz system tłumaczeń <a %(a_wikipedia)s>Wikipedia</a> — więcej o nas (prosimy o pomoc w aktualizacji tej strony lub utworzenie jednej w swoim języku!) Wybierz preferowane ustawienia, pozostaw pole wyszukiwania puste, kliknij „Szukaj”, a następnie dodaj stronę do zakładek za pomocą funkcji zakładek w przeglądarce. Jak zapisać ustawienia wyszukiwania? Zapraszamy badaczy bezpieczeństwa do poszukiwania luk w naszych systemach. Jesteśmy wielkimi zwolennikami odpowiedzialnego ujawniania. Skontaktuj się z nami <a %(a_contact)s>tutaj</a>. Obecnie nie jesteśmy w stanie przyznawać nagród za znalezione błędy, z wyjątkiem luk, które mają <a %(a_link)s>potencjał do naruszenia naszej anonimowości</a>, za które oferujemy nagrody w zakresie 10-50 tys. dolarów. Chcielibyśmy w przyszłości zaoferować szerszy zakres nagród za znalezione błędy! Proszę pamiętać, że ataki socjotechniczne są poza zakresem. Jeśli interesujesz się bezpieczeństwem ofensywnym i chcesz pomóc w archiwizacji wiedzy i kultury świata, koniecznie skontaktuj się z nami. Istnieje wiele sposobów, w jakie możesz pomóc. Czy macie program odpowiedzialnego ujawniania luk? Dosłownie nie mamy wystarczających zasobów, aby zapewnić wszystkim na świecie szybkie pobieranie, choć bardzo byśmy chcieli. Gdyby bogaty dobroczyńca chciał nam to zapewnić, byłoby to niesamowite, ale do tego czasu staramy się jak najlepiej. Jesteśmy projektem non-profit, który ledwo utrzymuje się z darowizn. Dlatego wdrożyliśmy dwa systemy darmowych pobrań z naszymi partnerami: współdzielone serwery z wolnym pobieraniem oraz nieco szybsze serwery z listą oczekujących (aby zmniejszyć liczbę osób pobierających jednocześnie). Mamy również <a %(a_verification)s>weryfikację przeglądarki</a> dla naszych wolnych pobrań, ponieważ w przeciwnym razie boty i skrypty będą je nadużywać, co jeszcze bardziej spowolni pobieranie dla prawdziwych użytkowników. Zauważ, że podczas korzystania z przeglądarki Tor może być konieczne dostosowanie ustawień bezpieczeństwa. Na najniższym poziomie opcji, zwanym „Standard”, wyzwanie Cloudflare turnstile kończy się sukcesem. Na wyższych opcjach, zwanych „Bezpieczniejszy” i „Najbezpieczniejszy”, wyzwanie kończy się niepowodzeniem. Czasami pobieranie dużych plików może zostać przerwane w trakcie. Zalecamy użycie menedżera pobierania (takiego jak JDownloader), aby automatycznie wznawiać duże pobrania. Dlaczego pobieranie jest tak wolne? Często zadawane pytania (FAQ) Użyj <a %(a_list)s>generatora listy torrentów</a>, aby wygenerować listę torrentów, które najbardziej potrzebują seedowania, w granicach twojej przestrzeni dyskowej. Tak, zobacz stronę <a %(a_llm)s>danych LLM</a>. Większość torrentów zawiera pliki bezpośrednio, co oznacza, że możesz polecić klientom torrent, aby pobierali tylko wymagane pliki. Aby określić, które pliki pobrać, możesz <a %(a_generate)s>wygenerować</a> nasze metadane lub <a %(a_download)s>pobrać</a> nasze bazy danych ElasticSearch i MariaDB. Niestety, niektóre kolekcje torrentów zawierają pliki .zip lub .tar w katalogu głównym, w takim przypadku musisz pobrać cały torrent, zanim będziesz mógł wybrać poszczególne pliki. Nie ma jeszcze łatwych w użyciu narzędzi do filtrowania torrentów, ale zachęcamy do współpracy. (Mamy jednak <a %(a_ideas)s>kilka pomysłów</a> na ten drugi przypadek.) Długa odpowiedź: Krótka odpowiedź: niełatwo. Staramy się minimalizować duplikaty lub nakładanie się torrentów na tej liście, ale nie zawsze jest to możliwe i zależy w dużej mierze od polityki bibliotek źródłowych. W przypadku bibliotek, które wydają własne torrenty, nie mamy na to wpływu. W przypadku torrentów wydanych przez Anna’s Archive, deduplikujemy tylko na podstawie hasha MD5, co oznacza, że różne wersje tej samej książki nie są deduplikowane. Tak. To są właściwie pliki PDF i EPUB, po prostu nie mają rozszerzenia w wielu naszych torrentach. Istnieją dwa miejsca, w których można znaleźć metadane dla plików torrent, w tym typy/rozszerzenia plików: 1. Każda kolekcja lub wydanie ma swoje własne metadane. Na przykład, <a %(a_libgen_nonfic)s>torrenty Libgen.rs</a> mają odpowiadającą im bazę metadanych hostowaną na stronie Libgen.rs. Zazwyczaj linkujemy do odpowiednich zasobów metadanych z <a %(a_datasets)s>strony datasetu</a> każdej kolekcji. 2. Zalecamy <a %(a_generate)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB. Zawierają one mapowanie każdego rekordu w Archiwum Anny do odpowiadających mu plików torrent (jeśli są dostępne), pod "torrent_paths" w JSON ElasticSearch. Niektóre klienty torrentów nie obsługują dużych rozmiarów kawałków, które mają wiele naszych torrentów (dla nowszych już tego nie robimy — mimo że jest to zgodne ze specyfikacją!). Spróbuj użyć innego klienta, jeśli napotkasz ten problem, lub zgłoś to twórcom swojego klienta torrent. Chciałbym pomóc w seedowaniu, ale nie mam dużo miejsca na dysku. Torrenty są zbyt wolne; czy mogę pobrać dane bezpośrednio od was? Czy mogę pobrać tylko część plików, na przykład tylko w określonym języku lub na określony temat? Jak radzicie sobie z duplikatami w torrentach? Czy mogę otrzymać listę torrentów w formacie JSON? Nie widzę plików PDF ani EPUB w torrentach, tylko pliki binarne? Co mam zrobić? Dlaczego mój klient torrent nie może otworzyć niektórych waszych plików torrent / linków magnetycznych? Torrenty FAQ Jak mogę przesłać nowe książki? Proszę zobaczyć <a %(a_href)s>ten doskonały projekt</a>. Czy masz monitor czasu pracy? Czym jest Archiwum Anny? Zostań członkiem, aby uzyskać dostęp do szybkich pobierań. Obecnie obsługujemy karty podarunkowe Amazon, karty kredytowe i debetowe, kryptowaluty, Alipay i WeChat. Wyczerpałeś dzisiejszy limit szybkich pobrań. Dostęp Ilość pobrań w odstępie cogodzinnym z ostatnich 30 dni. Godzinna średnia: %(hourly)s. Dzienna średnia: %(daily)s. Współpracujemy z partnerami zewnętrznymi, aby umożliwić dostęp do naszych zasobów łatwym i dostępnym dla każdego. Uważamy, że każdy powinien mieć dostęp do wiedzy oraz mądrości całej ludzkości i <a %(a_search)s>nie kosztem autorów</a>. Zbiory danych wykorzystywane w Anna’s Archive są całkowicie otwarte i mogą być kopiowane masowo za pomocą torrentów. <a %(a_datasets)s>Dowiedz się więcej…</a> Archiwizacja długoterminowa Pełna baza danych Szukaj Książki, prace naukowe, czasopisma, komiksy, rekordy biblioteczne, metadane itd… Cały nasz <a %(a_code)s>kod</a> i <a %(a_datasets)s>dane</a> są całkowicie open source. <span %(span_anna)sAnna’s Archive</span> to niekomercyjny projekt z dwoma celami: <li><strong>Utrwalenie:</strong> Zachowywanie od utraty przepastnych zasobów wiedzy i kultury ludzkości.</li><li><strong>Dostęp:</strong> Uczynienie wiedzy i kultury dostępnej dla każdego na świecie.</li> Posiadamy największą na świecie kolekcję wysokiej jakości danych tekstowych. <a %(a_llm)s>Dowiedz się więcej…</a> Dane treningowe LLM 🪩 Lustra: wezwanie dla wolontariuszy Jeśli prowadzisz wysokiego ryzyka anonimowy procesor płatności, skontaktuj się z nami. Szukamy również osób chcących umieścić gustowne małe reklamy. Wszystkie dochody przeznaczamy na nasze działania związane z zachowaniem zasobów. Utrwalenie Przewidujemy, że udało nam się utrwalić około <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html"> 5%% światowych zasobów książkowych </a>. Zachowujemy książki, artykuły, komiksy, czasopisma i inne materiały, łącząc te zasoby z różnych <a href="https://en.wikipedia.org/wiki/Shadow_library">bibliotek cieni</a>, oficjalnych bibliotek i innych kolekcji w jednym miejscu. Wszystkie te dane są zachowywane na zawsze, dzięki łatwości ich duplikowania w dużych ilościach — za pomocą torrentów — co skutkuje wieloma kopiami na całym świecie. Niektóre biblioteki cieni już to robią same (np. Sci-Hub, Library Genesis), podczas gdy Archiwum Anny „wyzwala” inne biblioteki, które nie oferują dystrybucji masowej (np. Z-Library) lub w ogóle nie są bibliotekami cieni (np. Internet Archive, DuXiu). Ta szeroka dystrybucja, w połączeniu z kodem open source, sprawia, że nasza strona jest odporna na usunięcia i zapewnia długoterminowe zachowanie wiedzy i kultury ludzkości. Dowiedz się więcej o <a href="/datasets">naszych zbiorach danych</a>. Jeśli jesteś <a %(a_member)s>członkiem</a>, weryfikacja przeglądarki nie jest wymagana. 🧬&nbsp;SciDB jest kontynuacją Sci-Hub. SciDB Otwórz DOI Sci-Hub <a %(a_paused)s>wstrzymał</a> przesyłanie nowych artykułów. Bezpośredni dostęp do prac naukowych %(count)s 🧬&nbsp;SciDB jest kontynuacją Sci-Hub, z jego znanym interfejsem i bezpośrednim podglądem plików PDF. Wprowadź swój DOI, aby wyświetlić. Mamy pełną kolekcję Sci-Hub, a także nowe artykuły. Większość można przeglądać bezpośrednio za pomocą znanego interfejsu, podobnego do Sci-Hub. Niektóre można pobrać z zewnętrznych źródeł, w takim przypadku pokazujemy linki do nich. Możesz pomóc seedując torrenty. <a %(a_torrents)s>Dowiedz się więcej…</a> >%(count)s seedów <%(count)s seedów %(count_min)s–%(count_max)s seedów 🤝 Poszukujemy wolontariuszy Jako non-profitowy, open-source'owy projekt, zawsze szukamy osób do pomocy. Pobrania IPFS Lista %(by)s, utworzona <span %(span_time)s>%(time)s</span> Zapisz ❌ Wystąpił błąd. Spróbuj ponownie. ✅ Zapisano. Odśwież stronę. Lista jest pusta. Edytuj Dodaj lub usuń pozycje z tej listy poprzez znalezienie odpowiedniego pliku i otworzenie zakładki "Lista". Lista Jak możemy pomóc Usuwanie nakładania się (deduplikacja) Ekstrakcja tekstu i metadanych OCR Jesteśmy w stanie zapewnić szybki dostęp do naszych pełnych kolekcji, a także do nieopublikowanych zbiorów. To dostęp na poziomie przedsiębiorstwa, który możemy zapewnić za darowizny w wysokości dziesiątek tysięcy USD. Jesteśmy również gotowi wymienić to na wysokiej jakości kolekcje, których jeszcze nie posiadamy. Możemy zwrócić Ci pieniądze, jeśli będziesz w stanie dostarczyć nam wzbogacenie naszych danych, takie jak: Wspieraj długoterminowe archiwizowanie ludzkiej wiedzy, jednocześnie uzyskując lepsze dane dla swojego modelu! <a %(a_contact)s>Skontaktuj się z nami</a>, aby omówić, jak możemy współpracować. Jest powszechnie wiadomo, że LLM-y rozwijają się na wysokiej jakości danych. Mamy największą na świecie kolekcję książek, artykułów, magazynów itp., które są jednymi z najwyższej jakości źródeł tekstowych. Dane LLM Unikalna skala i zasięg Nasza kolekcja zawiera ponad sto milionów plików, w tym czasopisma naukowe, podręczniki i magazyny. Osiągamy tę skalę, łącząc duże istniejące repozytoria. Niektóre z naszych źródłowych kolekcji są już dostępne w dużych ilościach (Sci-Hub i części Libgen). Inne źródła wyzwoliliśmy sami. <a %(a_datasets)s>Datasets</a> pokazuje pełny przegląd. Nasza kolekcja obejmuje miliony książek, artykułów i magazynów sprzed ery e-booków. Duże części tej kolekcji zostały już poddane OCR i mają niewielkie wewnętrzne nakładanie się. Kontynuuj Jeśli straciłeś swój klucz, <a %(a_contact)s>skontaktuj się z nami</a> podając jak najwięcej szczegółowych informacji. Konieczne może być stworzenie nowego, tymczasowego konta do kontaktu z nami. Proszę <a %(a_account)s>zaloguj się</a>, aby zobaczyć tę stronę.</a> Aby zapobiec botom spamującym tworzenie wielu kont, koniecznym jest zweryfikowanie Twojej przeglądarki. Jeśli utkniesz w nieskończonej pętli, zalecamy zainstalowanie <a %(a_privacypass)s>Privacy Pass</a>. Pomocne może być także wyłączenie programów blokujących reklamy i innych rozszerzeń przeglądarki. Zaloguj / Zarejestruj Archiwum Anny jest tymczasowo niedostępne z powodu konserwacji. Proszę wrócić za godzinę. Alternatywny autor Alternatywny opis Alternatywne wydanie Alternatywne rozszerzenie Alternatywna nazwa pliku Alternatywny wydawca Alternatywny tytuł data uwolnienia Więcej… opis Wyszukaj w Archiwum Anny numer SSNO CADAL Szukaj w Archiwum Anny numeru SSID DuXiu Wyszukaj w Archiwum Anny numer DXID DuXiu Przeszukaj ISBN w Anna’s Archive Wyszukaj numer OCLC (WorldCat) w Anna’s Archive Przeszukaj Open Library ID w Anna’s Archive Przeglądarka online Archiwum Anny %(count)s dotkniętych stron Po pobraniu: Lepsza wersja tego pliku może być dostępna pod adresem %(link)s Pobieranie masowe za pomocą protokołu BitTorrent kolekcja Użyj narzędzi online do konwersji między formatami. Zalecane narzędzia do konwersji: %(links)s W przypadku dużych plików zalecamy użycie menedżera pobierania, aby zapobiec przerwom. Zalecane menedżery pobierania: %(links)s Indeks eBooków EBSCOhost (wyłącznie dla ekspertów) (kliknij również „GET” u góry) (kliknij "GET" u góry) Zewnętrzne pobierania <strong> 🚀 Szybkie pobieranie</strong> Pozostało Ci %(remaining)s w dniu dzisiejszym. Dziękujemy za Twoje członkostwo! ❤️ <strong>🚀 Szybkie pobieranie</strong> Wyczerpałeś limit szybkich pobrań na dziś. <strong> 🚀 Szybkie pobierania</strong> Plik został pobrany niedawno. Odnośnik pozostanie aktywny przez chwilę. <strong> 🚀 Szybkie pobieranie</strong> Zostań <a %(a_membership)s>członkiem</a>, aby wesprzeć utrwalanie książek, prac naukowych i innych w długofalowym procesie. Aby okazać ci naszą wdzięczność za pomoc, otrzymasz dostęp do szybkich serwerów. ❤️ 🚀 Szybkie pobieranie 🐢 Wolne pobieranie Wypożycz z Internet Archive Brama IPFS #%(num)d (udane pobranie przez IPFS może wymagać kilku prób) Libgen.li Libgen.rs Beletrystyka Libgen.rs Literatura faktu ich reklamy są znane z zawierania złośliwego oprogramowania, więc używaj blokera reklam lub nie klikaj reklam Amazon „Wyślij do Kindle” djazz „Wyślij do Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Pliki Nexus/STC mogą być niewiarygodne do pobrania) Nie znaleziono pobierań. Wszystkie serwery lustrzane obsługują ten sam plik i powinny być bezpieczne w użyciu. To powiedziawszy, zawsze zachowaj ostrożność podczas pobierania plików z Internetu. Na przykład pamiętaj, aby aktualizować swoje urządzenia. (bez przekierowania) Otwórz w naszej przeglądarce (otwórz w przeglądarce) Opcja #%(num)d: %(link)s %(extra)s Znajdź oryginalny rekord w CADAL Szukaj ręcznie na DuXiu Znajdź oryginalny rekord w ISBNdb Znajdź oryginalny rekord w WorldCat Znajdź oryginalny rekord w Open Library Przeszukaj ISBN w innych bazach danych (tylko dla osób z zaburzeniami widzenia) PubMed Do otwarcia pliku będziesz potrzebować czytnika ebooków lub PDF, w zależności od formatu pliku. Zalecane czytniki ebooków: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (zawarte DOI może nie być dostępne w "Sci-Hub") Możesz wysyłać zarówno pliki PDF, jak i EPUB na swój czytnik Kindle lub Kobo. Zalecane narzędzia: %(links)s Więcej informacji w <a %(a_slow)s>FAQ</a>. Wspieraj autorów i biblioteki Jeśli podoba Ci się to i możesz sobie na to pozwolić, rozważ zakup oryginału lub bezpośrednie wsparcie autorów. Jeśli jest dostępna w Twojej lokalnej bibliotece, rozważ wypożyczenie jej za darmo. Pobieranie przez serwery partnerskie chwilowo nie jest dostępne dla tego pliku. torrent Od zaufanych partnerów. Z-Library Z-Library (poprzez Tor) (wymaga Tor Browser) pokaż zewnętrzne pobierania <span class="font-bold">❌ Ten plik może mieć problemy i został ukryty w jednej z bibliotek.</span> Czasem jest to spowodowane żądaniem właściciela praw autorskich, czasem dostępnością lepszej alternatywy, a czasem jest to spowodowane problemem z samym plikiem. Być może dalej nadaje się do pobrania, ale polecamy najpierw poszukać alternatywnego pliku. Więcej szczegółów: Jeśli mimo tego chcesz pobrać ten plik, upewnij się, by otwierać go tylko zaufanym, zaktualizowanym oprogramowaniem. komentarze metadanych AA: Szukanie frazy "%(name)s" w Anna's Archive Eksplorator kodów: Zobacz w Eksploratorze kodów „%(name)s” Link: Strona: Jeśli posiadasz ten plik, a nie jest on dostępny w Anna's Archive, sugerujemy <a %(a_request)s>wrzucenie go</a>. “%(id)s” pliku Internet Archive Controlled Digital Lending To jest rekord pliku z Internet Archive, a nie plik do bezpośredniego pobrania. Możesz spróbować wypożyczyć książkę (link poniżej) lub użyć tego adresu podczas <a %(a_request)s>żądania pliku</a>. Popraw metadane Rekord metadanych CADAL SSNO %(id)s Jest to rekord metadanych, a nie plik do pobrania. Możesz użyć tego adresu, gdy chcesz przesłać<a %(a_request)s>żądanie pliku</a>. Rekord metadanych SSID DuXiu %(id)s %(id)s rekordu metadanych ISBNdb Rekord metadanych MagzDB ID %(id)s Rekord metadanych Nexus/STC ID %(id)s %(id)s rekordu metadanych OCLC (WorldCat) %(id)s rekordu metadanych Open Library Sci-Hub “%(id)s” pliku Nie znaleziono Nie znaleziono “%(md5_input)s” w naszej bazie danych. Dodaj komentarz (%(count)s) Możesz uzyskać md5 z URL, np. MD5 lepszej wersji tego pliku (jeśli dotyczy). Wypełnij to, jeśli istnieje inny plik, który ściśle odpowiada temu plikowi (ta sama edycja, to samo rozszerzenie pliku, jeśli można go znaleźć), którego ludzie powinni używać zamiast tego pliku. Jeśli znasz lepszą wersję tego pliku poza Archiwum Anny, proszę <a %(a_upload)s>prześlij ją</a>. Coś poszło nie tak. Proszę odświeżyć stronę i spróbować ponownie. Zostawiłeś komentarz. Może minąć chwila, zanim się pojawi. Proszę użyj <a %(a_copyright)s>formularza zgłoszenia DMCA / naruszenia praw autorskich</a>. Opisz problem (wymagane) Jeśli ten plik jest wysokiej jakości, możesz tutaj omówić wszystko na jego temat! Jeśli nie, użyj przycisku „Zgłoś problem z plikiem”. Świetna jakość pliku (%(count)s) Jakość pliku Naucz się <a %(a_metadata)s>poprawiać metadane</a> tego pliku samodzielnie. Opis problemu Proszę <a %(a_login)s>zaloguj się</a>. Uwielbiam tę książkę! Pomóż społeczności, zgłaszając jakość tego pliku! 🙌 Coś poszło nie tak. Proszę odświeżyć stronę i spróbować ponownie. Zgłoś problem z plikiem (%(count)s) Dziękujemy za przesłanie raportu. Zostanie on wyświetlony na tej stronie oraz ręcznie sprawdzony przez Annę (dopóki nie będziemy mieli odpowiedniego systemu moderacji). Zostaw komentarz Prześlij zgłoszenie Co jest nie tak z tym plikiem? (%(count)s) wypożyczeń Komentarze (%(count)s) (%(count)s) Pobrań (%(count)s) przeszukiwań metadanych (%(count)s) list (%(count)s) statystyk Aby uzyskać informacje o tym konkretnym pliku, sprawdź jego <a %(a_href)s>plik JSON</a>. To jest plik zarządzany przez <a %(a_ia)s>IA’s Controlled Digital Lending</a> i zindeksowany przez Archiwum Anny do wyszukiwania. Aby uzyskać informacje o różnych datasetach, które skompilowaliśmy, zobacz <a %(a_datasets)s>stronę Datasets</a>. Metadane z powiązanego rekordu Popraw metadane w Open Library „Plik MD5” to hash, który jest obliczany na podstawie zawartości pliku i jest w miarę unikalny w oparciu o tę zawartość. Wszystkie biblioteki cieni, które tutaj indeksujemy, używają głównie MD5 do identyfikacji plików. Plik może pojawić się w wielu bibliotekach cieni. Aby uzyskać informacje o różnych datasetach, które skompilowaliśmy, zobacz <a %(a_datasets)s>stronę Datasets</a>. Zgłoś jakość pliku Łączna liczba pobrań: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Czeskie metadane %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Ostrzeżenie: wiele powiązanych rekordów: Kiedy przeglądasz książkę na Archiwum Anny, możesz zobaczyć różne pola: tytuł, autor, wydawca, edycja, rok, opis, nazwa pliku i inne. Wszystkie te informacje nazywane są <em>metadanymi</em>. Ponieważ łączymy książki z różnych <em>bibliotek źródłowych</em>, pokazujemy wszelkie dostępne metadane z tej biblioteki źródłowej. Na przykład, dla książki, którą otrzymaliśmy z Library Genesis, pokażemy tytuł z bazy danych Library Genesis. Czasami książka jest obecna w <em>wielu</em> bibliotekach źródłowych, które mogą mieć różne pola metadanych. W takim przypadku po prostu pokazujemy najdłuższą wersję każdego pola, ponieważ ta najprawdopodobniej zawiera najbardziej przydatne informacje! Nadal pokażemy inne pola poniżej opisu, np. jako „alternatywny tytuł” (ale tylko jeśli są różne). Wyciągamy również <em>kody</em> takie jak identyfikatory i klasyfikatory z biblioteki źródłowej. <em>Identyfikatory</em> jednoznacznie reprezentują konkretną edycję książki; przykłady to ISBN, DOI, Open Library ID, Google Books ID lub Amazon ID. <em>Klasyfikatory</em> grupują podobne książki; przykłady to Dewey Decimal (DCC), UDC, LCC, RVK lub GOST. Czasami te kody są jawnie powiązane w bibliotekach źródłowych, a czasami możemy je wyciągnąć z nazwy pliku lub opisu (głównie ISBN i DOI). Możemy używać identyfikatorów do znajdowania rekordów w <em>kolekcjach tylko z metadanymi</em>, takich jak OpenLibrary, ISBNdb lub WorldCat/OCLC. W naszej wyszukiwarce jest specjalna <em>zakładka metadanych</em>, jeśli chcesz przeglądać te kolekcje. Używamy pasujących rekordów do wypełniania brakujących pól metadanych (np. jeśli brakuje tytułu) lub np. jako „alternatywny tytuł” (jeśli istnieje już tytuł). Aby zobaczyć dokładnie, skąd pochodzą metadane książki, zobacz <em>zakładkę „Szczegóły techniczne”</em> na stronie książki. Zawiera ona link do surowego JSON dla tej książki, z odnośnikami do surowego JSON oryginalnych rekordów. Więcej informacji znajdziesz na następujących stronach: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> i <a %(a_example)s>Example metadata JSON</a>. Wreszcie, wszystkie nasze metadane mogą być <a %(a_generated)s>generowane</a> lub <a %(a_downloaded)s>pobierane</a> jako bazy danych ElasticSearch i MariaDB. Tło Możesz pomóc w zachowaniu książek, poprawiając metadane! Najpierw przeczytaj tło dotyczące metadanych na Archiwum Anny, a następnie dowiedz się, jak poprawiać metadane poprzez łączenie z Open Library i zdobądź darmowe członkostwo na Archiwum Anny. Popraw metadane Więc jeśli napotkasz plik z błędnymi metadanymi, jak powinieneś go naprawić? Możesz przejść do biblioteki źródłowej i postępować zgodnie z jej procedurami naprawy metadanych, ale co zrobić, jeśli plik jest obecny w wielu bibliotekach źródłowych? Jest jeden identyfikator, który jest traktowany szczególnie na Archiwum Anny. <strong>Pole annas_archive md5 w Open Library zawsze nadpisuje wszystkie inne metadane!</strong> Najpierw cofnijmy się trochę i dowiedzmy się więcej o Open Library. Open Library została założona w 2006 roku przez Aarona Swartza z celem „jednej strony internetowej dla każdej książki, jaka kiedykolwiek została opublikowana”. Jest to coś w rodzaju Wikipedii dla metadanych książek: każdy może ją edytować, jest wolno licencjonowana i może być pobierana masowo. To baza danych książek, która najbardziej odpowiada naszej misji — w rzeczywistości Archiwum Anny zostało zainspirowane wizją i życiem Aarona Swartza. Zamiast wymyślać koło na nowo, postanowiliśmy skierować naszych wolontariuszy do Open Library. Jeśli zobaczysz książkę z nieprawidłowymi metadanymi, możesz pomóc w następujący sposób: Zauważ, że to działa tylko dla książek, nie dla prac naukowych ani innych typów plików. W przypadku innych typów plików nadal zalecamy znalezienie biblioteki źródłowej. Może minąć kilka tygodni, zanim zmiany zostaną uwzględnione w Anna’s Archive, ponieważ musimy pobrać najnowszy zrzut danych Open Library i zregenerować nasz indeks wyszukiwania.  Przejdź na <a %(a_openlib)s>stronę Open Library</a>. Znajdź poprawny rekord książki. <strong>UWAGA:</strong> upewnij się, że wybierasz poprawną <strong>edycję</strong>. W Open Library masz „dzieła” i „edycje”. „Dzieło” może być „Harry Potter i Kamień Filozoficzny”. „Edycja” może być: Pierwsze wydanie z 1997 roku opublikowane przez Bloomsbery, liczące 256 stron. Wydanie w miękkiej oprawie z 2003 roku opublikowane przez Raincoast Books, liczące 223 strony. Polskie tłumaczenie z 2000 roku „Harry Potter i Kamień Filozoficzny” wydane przez Media Rodzina, liczące 328 stron. Wszystkie te wydania mają różne numery ISBN i różną zawartość, więc upewnij się, że wybierasz właściwe! Edytuj rekord (lub stwórz go, jeśli nie istnieje) i dodaj jak najwięcej przydatnych informacji! Skoro już tu jesteś, możesz sprawić, że rekord będzie naprawdę niesamowity. W sekcji „Numery ID” wybierz „Anna’s Archive” i dodaj MD5 książki z Anna’s Archive. To jest długi ciąg liter i cyfr po „/md5/” w URL. Spróbuj znaleźć inne pliki w Anna’s Archive, które również pasują do tego rekordu, i dodaj je również. W przyszłości możemy je pogrupować jako duplikaty na stronie wyszukiwania Anna’s Archive. Gdy skończysz, zapisz URL, który właśnie zaktualizowałeś. Gdy zaktualizujesz co najmniej 30 rekordów z MD5 z Anna’s Archive, wyślij nam <a %(a_contact)s>email</a> i prześlij listę. Otrzymasz darmowe członkostwo w Anna’s Archive, abyś mógł łatwiej wykonywać tę pracę (i jako podziękowanie za pomoc). Muszą to być wysokiej jakości edycje, które dodają znaczną ilość informacji, w przeciwnym razie twoja prośba zostanie odrzucona. Twoja prośba zostanie również odrzucona, jeśli którakolwiek z edycji zostanie cofnięta lub poprawiona przez moderatorów Open Library. Łączenie z Open Library Jeśli zaangażujesz się znacząco w rozwój i operacje naszej pracy, możemy omówić dzielenie się większą częścią dochodów z darowizn, abyś mógł je wykorzystać według potrzeb. Zapłacimy za hosting dopiero po tym, jak wszystko skonfigurujesz i udowodnisz, że jesteś w stanie utrzymać archiwum na bieżąco z aktualizacjami. Oznacza to, że będziesz musiał zapłacić za pierwsze 1-2 miesiące z własnej kieszeni. Twój czas nie będzie rekompensowany (i nasz również nie), ponieważ jest to czysta praca wolontariacka. Jesteśmy gotowi pokryć koszty hostingu i VPN, początkowo do 200 USD miesięcznie. To wystarczy na podstawowy serwer wyszukiwania i proxy chronione przez DMCA. Koszty hostingu Prosimy <strong>nie kontaktować się z nami</strong> w celu uzyskania zgody lub zadawania podstawowych pytań. Czyny mówią głośniej niż słowa! Wszystkie informacje są dostępne, więc po prostu przystąp do konfiguracji swojego mirrora. Nie wahaj się zgłaszać ticketów lub prośby o połączenie na naszym Gitlabie, gdy napotkasz problemy. Może będziemy musieli zbudować z Tobą niektóre funkcje specyficzne dla mirrorów, takie jak rebranding z „Archiwum Anny” na nazwę Twojej strony, (początkowo) wyłączenie kont użytkowników lub linkowanie z powrotem do naszej głównej strony z stron książek. Gdy Twój mirror będzie działał, prosimy o kontakt z nami. Chętnie przejrzymy Twoje zabezpieczenia operacyjne, a gdy będą solidne, podlinkujemy Twój mirror i zaczniemy ściślej z Tobą współpracować. Z góry dziękujemy każdemu, kto jest gotów przyczynić się w ten sposób! To nie jest dla osób o słabym sercu, ale wzmocni to trwałość największej naprawdę otwartej biblioteki w historii ludzkości. Rozpoczęcie Aby zwiększyć odporność Archiwum Anny, szukamy wolontariuszy do prowadzenia mirrorów. Twoja wersja jest wyraźnie oznaczona jako mirror, np. „Archiwum Boba, mirror Archiwum Anny”. Jesteś gotów podjąć ryzyko związane z tą pracą, które jest znaczące. Masz głębokie zrozumienie wymaganej operacyjnej ochrony. Treści <a %(a_shadow)s>tych</a> <a %(a_pirate)s>postów</a> są dla Ciebie oczywiste. Początkowo nie damy Ci dostępu do pobrań z serwera partnerskiego, ale jeśli wszystko pójdzie dobrze, możemy to z Tobą podzielić. Uruchamiasz otwartą bazę kodu Archiwum Anny i regularnie aktualizujesz zarówno kod, jak i dane. Jesteś gotów przyczynić się do naszej <a %(a_codebase)s>bazy kodu</a> — we współpracy z naszym zespołem — aby to się stało. Szukamy tego: Mirrory: wezwanie do wolontariuszy Wykonaj dodatkową darowiznę. Nie posiadasz darowizn. <a %(a_donate)s> Wykonaj pierwszą darowiznę. </a> Szczegóły dotyczące darowizn nie są publikowane. Moje darowizny 📡 W celu wykonywania kopii lustrzanych naszej kolekcji, przejdź do stron: <%(a_datasets)s>Zbiory danych</a> lub <%(a_torrents)s>Pliki Torrent</a>. Pobrane pliki z Twojego adresu IP w ciągu ostatnich 24 godzin: %(count)s. 🚀 Aby uzyskać szybkie pobieranie oraz pominąć weryfikacji przeglądarki, <a %(a_membership)s>uzyskaj członkostwo</a>. Pobierz ze strony partnera Możesz kontynuować przeglądanie Archiwum Anny w innej karcie, czekając (jeśli Twoja przeglądarka obsługuje odświeżanie kart w tle). Możesz poczekać, aż załaduje się kilka stron pobierania jednocześnie (ale prosimy o pobieranie tylko jednego pliku na raz z każdego serwera). Po uzyskaniu linku do pobrania jest on ważny przez kilka godzin. Dziękujemy za cierpliwość, to pozwala utrzymać stronę dostępną za darmo dla wszystkich! 😊 🔗 Wszystkie linki do pobrania tego pliku: <a %(a_main)s>Strona pliku</a>. ❌ Powolne pobieranie nie jest dostępne przez VPN-y Cloudflare ani z adresów IP Cloudflare. ❌ Powolne pobieranie jest dostępne tylko za pośrednictwem oficjalnej strony internetowej. Odwiedź %(websites)s. 📚 Użyj linku, aby pobrać: <a %(a_download)s>Pobierz teraz</a>. Aby dać każdemu możliwość pobierania plików za darmo, musisz poczekać, zanim będziesz mógł pobrać ten plik. Proszę poczekać <span %(span_countdown)s>%(wait_seconds)s</span> sekund, aby pobrać ten plik. Ostrzeżenie: w ciągu ostatnich 24 godzin z tego adresu IP pobrano zbyt wiele plików. Pobieranie może być wolniejsze niż zwykle. Jeśli używasz VPN, współdzielonego połączenia internetowego lub Twój dostawca usług internetowych udostępnia adresy IP, to ostrzeżenie może być z tym związane. Zapisz ❌ Wystąpił błąd. Spróbuj ponownie. ✅ Zapisano. Odśwież stronę. Zmień wyświetlaną nazwę. Twój identyfikator (część następująca po "#") nie może zostać zmieniona. Profil utworzony <span %(span_time)s>%(time)s</span> Edytuj Listy Utwórz nową listę poprzez znalezienie pliku i otworzenie zakładki "Lista". Brak list Profil nie znaleziony. Profil W tej chwili nie możemy przyjmować próśb o książki. Nie wysyłaj nam próśb o książki e-mailem. Prosimy o składanie próśb na forach Z-Library lub Libgen. Rekord w Anna’s Archive DOI: %(doi)s Pobieranie SciDB Nexus/STC Brak podglądu. Pobierz plik z <a %(a_path)s>Archiwum Anny</a>. Aby wspierać dostępność i długoterminowe zachowanie ludzkiej wiedzy, zostań <a %(a_donate)s>członkiem</a>. Dodatkowo, 🧬&nbsp;SciDB ładuje się szybciej dla członków, bez żadnych limitów. Nie działa? Spróbuj <a %(a_refresh)s>odświeżyć</a>. Sci-Hub Dodaj konkretne pole wyszukiwania Przeszukuj opisy i komentarze metadanych Rok wydania Zaawansowane Dostęp Treść Wyświetl Lista Tabela Typ pliku Język Sortuj Malejąco rozmiarami Najbardziej istotne Najnowszy (rozmiar pliku) (open source) (rok publikacji) Najstarszy Losowe Rosnąco rozmiarami Źródło zeskrobane i udostępnione jako open source przez AA (%(count)s) wypożyczeń Artykuły z czasopism (%(count)s) Znaleźliśmy dopasowania w: %(in)s. Możesz odwołać się do znalezionego tam adresu URL, wysyłając <a %(a_request)s>żądanie pliku</a>. (%(count)s) metadanych Aby przeszukać indeks według kodów, użyj <a %(a_href)s>Eksploratora Kodów</a>. Zasoby wyszukiwarki są aktualizowane co miesiąc. Teraz zawierają dane aż do %(last_data_refresh_date)s. Dla bardziej szczegółowych informacji, spójrz na %(link_open_tag)sstronę o zasobach</a>. Wyklucz Uwzględnij tylko Niesprawdzone więcej… Następne … Poprzednie Ten indeks wyszukiwania zawiera metadane z biblioteki Internet Archive’s Controlled Digital Lending. <a %(a_datasets)s>Więcej o zbiorach danych</a>. Więcej cyfrowych bibliotek można znaleźć na <a %(a_wikipedia)s>Wikipedii</a> i <a %(a_mobileread)s>MobileRead Wiki</a>. W przypadku roszczeń DMCA / praw autorskich <a %(a_copyright)s>kliknij tutaj</a>. Czas pobierania Błąd podczas wyszukiwania. Spróbuj <a %(a_reload)s>odświeżyć stronę</a>. Jeśli problem nie ustąpi, prosimy o wysłanie wiadomości na adres %(email)s. Szybkie pobieranie W rzeczywistości każdy może pomóc w zachowaniu tych plików, udostępniając naszą <a %(a_torrents)s>zunifikowaną listę torrentów</a>. ➡️ Czasami dzieje się to nieprawidłowo, gdy serwer wyszukiwania jest wolny. W takich przypadkach <a %(a_attrs)s>ponowne załadowanie</a> może pomóc. ❌ Z tym plikiem mogą występować problemy. Szukasz artykułów naukowych? Ten indeks wyszukiwania zawiera metadane z różnych źródeł. <a %(a_datasets)s>Więcej o zbiorach danych</a>. Istnieje wiele źródeł metadanych dla dzieł napisanych na całym świecie. <a %(a_wikipedia)s>Ta strona Wikipedii</a> to dobry początek, ale jeśli znasz inne listy, daj nam znać. Dla metadanych pokazujemy oryginalne rekordy. Nie łączymy rekordów. Posiadamy największy na świecie otwarty katalog książek, prac naukowych i innych prac pisemnych. Kopiujemy bazy Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>i więcej</a>. <span class="font-bold">Brak wyników.</span> Spróbuj inaczej sformułować zapytanie, używając mniej wyrazów i filtrów lub zmieniając je. Wyniki %(from)s-%(to)s (%(total)s łącznie) Jeśli znasz inne "ukryte biblioteki", które powinniśmy skopiować, lub jeśli masz jakiekolwiek pytania, skontaktuj się z nami pod adresem %(email)s. %(num)d częściowo pasujących wyników %(num)d+ częściowo pasujących wyników Wpisz w pole, aby wyszukać pliki w bibliotekach. Wpisz frazę w pole, aby przeszukać nasz katalog %(count)s plików do pobrania, które <a %(a_preserve)s>zachowaliśmy na zawsze</a>. Wpisz w pole żeby wyszukać. Wpisz w polu, aby przeszukać nasz katalog %(count)s artykułów naukowych i czasopism, które <a %(a_preserve)s>zachowujemy na zawsze</a>. Wpisz w pole, aby wyszukać metadane z bibliotek. Może być przydatne, przy <a %(a_request)s>żądaniu pliku</a>. Wskazówka: użyj skrótów klawiszowych "/" (pole wyszukiwania), "enter" (wyszukiwanie), "j" (w górę), "k" (w dół), aby przyspieszyć nawigację. Są to rekordy metadanych, <span %(classname)s>nie</span> pliki do pobrania. Szukaj ustawień Szukaj Wypożyczenie Pobieranie Artykuły z czasopism Metadane Nowe wyszukiwanie %(search_input)s - Szukaj Wyszukiwanie trwało zbyt długo, co oznacza, że wyniki mogą być niedokładne. Czasami pomaga <a %(a_reload)s>odświeżenie</a> strony. Wyszukiwanie trwało zbyt długo, co jest częste w przypadku zbyt ogólnych zapytań. Liczba filtrów może być niedokładna. Dla dużych wrzutów (ponad 10,000 plików) które nie zostały zaakceptowane przez Libgen lub Z-Library, skontaktuj się z nami pod adresem %(a_email)s. Dla Libgen.li, upewnij się, że najpierw zalogujesz się na <a %(a_forum)s >ich forum</a> z nazwą użytkownika %(username)s i hasłem %(password)s, a następnie wrócisz na ich <a %(a_upload_page)s >stronę przesyłania</a>. W chwili obecnej prosimy o przekazywanie plików przy pomocy gałęzi projektu Library Genesis. W tym miejscu znajduje się <a %(a_guide)s>przyjazny przewodnik </a>. Zauważ, że odnogi Library Genesis które indeksujemy współdzielą ten sam system współdzielenia plików. Dla małych przesyłek (do 10 000 plików) prosimy o przesłanie ich zarówno do %(first)s, jak i do %(second)s. Alternatywnie, możesz przesłać je do Z-Library <a %(a_upload)s>tutaj</a>. Aby przesłać prace naukowe, prosimy również (oprócz Library Genesis) przesyłać je do <a %(a_stc_nexus)s>STC Nexus</a>. Jest to najlepsza biblioteka cieni dla nowych prac. Jeszcze ich nie zintegrowaliśmy, ale zrobimy to w przyszłości. Możesz skorzystać z ich <a %(a_telegram)s>bota do przesyłania na Telegramie</a> lub skontaktować się z adresem podanym w przypiętej wiadomości, jeśli masz zbyt wiele plików do przesłania w ten sposób. <span %(label)s>Intensywna praca wolontariacka (nagrody od 50 do 5 000 USD):</span> jeśli możesz poświęcić dużo czasu i/lub zasobów na naszą misję, chętnie będziemy z Tobą ściślej współpracować. Ostatecznie możesz dołączyć do wewnętrznego zespołu. Mimo że mamy ograniczony budżet, możemy przyznać <span %(bold)s>💰 nagrody pieniężne</span> za najbardziej intensywną pracę. <span %(label)s>Lekkie prace wolontariackie:</span> jeśli możesz poświęcić tylko kilka godzin tu i tam, nadal jest wiele sposobów, w jakie możesz pomóc. Nagrodzimy stałych wolontariuszy <span %(bold)s>🤝 członkostwami w Archiwum Anny</span>. Archiwum Anny opiera się na wolontariuszach takich jak Ty. Zapraszamy do współpracy na każdym poziomie zaangażowania i poszukujemy pomocy w dwóch głównych kategoriach: Jeśli nie możesz poświęcić swojego czasu na wolontariat, nadal możesz nam bardzo pomóc, <a %(a_donate)s>przekazując pieniądze</a>, <a %(a_torrents)s>seedując nasze torrenty</a>, <a %(a_uploading)s>uploadując książki</a> lub <a %(a_help)s>opowiadając znajomym o Archiwum Anny</a>. <span %(bold)s>Firmy:</span> oferujemy bezpośredni dostęp do naszych zbiorów z dużą prędkością w zamian za darowizny na poziomie przedsiębiorstwa lub wymianę na nowe zbiory (np. nowe skany, zbiory OCR, wzbogacenie naszych danych). <a %(a_contact)s>Skontaktuj się z nami</a>, jeśli to Ciebie dotyczy. Zobacz także naszą <a %(a_llm)s>stronę LLM</a>. Nagrody Zawsze szukamy osób z solidnymi umiejętnościami programistycznymi lub w zakresie ofensywnego bezpieczeństwa, które chcą się zaangażować. Możesz znacząco przyczynić się do zachowania dziedzictwa ludzkości. W ramach podziękowania, oferujemy członkostwo za solidne wkłady. Jako ogromne podziękowanie, oferujemy nagrody pieniężne za szczególnie ważne i trudne zadania. Nie powinno to być traktowane jako zamiennik pracy, ale jest to dodatkowa motywacja i może pomóc w pokryciu poniesionych kosztów. Większość naszego kodu jest open source, i będziemy tego wymagać również od Twojego kodu przy przyznawaniu nagrody. Istnieją pewne wyjątki, które możemy omówić indywidualnie. Nagrody są przyznawane pierwszej osobie, która ukończy zadanie. Zachęcamy do komentowania zgłoszeń nagród, aby poinformować innych, że nad czymś pracujesz, aby inni mogli się wstrzymać lub skontaktować się z Tobą w celu współpracy. Pamiętaj jednak, że inni również mogą nad tym pracować i próbować Cię wyprzedzić. Nie przyznajemy jednak nagród za niedbałą pracę. Jeśli dwie wysokiej jakości prace zostaną złożone w krótkim odstępie czasu (w ciągu jednego lub dwóch dni), możemy zdecydować się na przyznanie nagród obu osobom, według naszego uznania, na przykład 100%% za pierwsze zgłoszenie i 50%% za drugie zgłoszenie (czyli łącznie 150%%). W przypadku większych nagród (zwłaszcza za zadania związane ze scrapingiem), prosimy o kontakt, gdy ukończysz ~5%% zadania i jesteś pewny, że Twoja metoda będzie skalowalna do pełnego kamienia milowego. Będziesz musiał podzielić się swoją metodą, abyśmy mogli udzielić informacji zwrotnej. W ten sposób możemy również zdecydować, co zrobić, jeśli kilka osób zbliża się do nagrody, na przykład potencjalnie przyznając ją kilku osobom, zachęcając do współpracy itp. OSTRZEŻENIE: zadania o wysokiej nagrodzie są <span %(bold)s>trudne</span> — może być mądrze zacząć od łatwiejszych. Przejdź do naszej <a %(a_gitlab)s>listy problemów na Gitlabie</a> i posortuj według „Priorytet etykiety”. To pokazuje mniej więcej kolejność zadań, na których nam zależy. Zadania bez wyraźnych nagród nadal kwalifikują się do członkostwa, zwłaszcza te oznaczone jako „Zaakceptowane” i „Ulubione Anny”. Możesz zacząć od „Projektu startowego”. Lekki wolontariat Mamy teraz również zsynchronizowany kanał Matrix pod adresem %(matrix)s. Jeśli masz kilka godzin wolnego czasu, możesz pomóc na różne sposoby. Dołącz do <a %(a_telegram)s>czatu wolontariuszy na Telegramie</a>. W ramach podziękowania zazwyczaj przyznajemy 6 miesięcy członkostwa „Szczęśliwy Bibliotekarz” za podstawowe kamienie milowe, a więcej za kontynuowaną pracę wolontariacką. Wszystkie kamienie milowe wymagają wysokiej jakości pracy — niedbała praca szkodzi nam bardziej niż pomaga i zostanie odrzucona. Prosimy <a %(a_contact)s>napisz do nas</a>, gdy osiągniesz kamień milowy. %(links)s linki lub zrzuty ekranu próśb, które spełniłeś. Spełnianie próśb o książki (lub artykuły itp.) na forach Z-Library lub Library Genesis. Nie mamy własnego systemu próśb o książki, ale mirrorujemy te biblioteki, więc ich ulepszanie sprawia, że Archiwum Anny również staje się lepsze. Kamień milowy Zadanie Zależy od zadania. Małe zadania zamieszczone na naszym <a %(a_telegram)s>czacie wolontariuszy na Telegramie</a>. Zazwyczaj za członkostwo, czasami za małe nagrody. Małe zadania publikowane w naszej grupie czatowej dla wolontariuszy. Upewnij się, że zostawiasz komentarz do problemów, które naprawiasz, aby inni nie dublowali twojej pracy. %(links)s linki rekordów, które poprawiłeś. Możesz użyć <a %(a_list)s >listy losowych problemów z metadata</a> jako punktu wyjścia. Popraw metadane, <a %(a_metadata)s>łącząc</a> je z Open Library. Te powinny pokazywać, jak informujesz kogoś o Archiwum Anny, a oni ci dziękują. %(links)s linki lub zrzuty ekranu. Rozpowszechnianie informacji o Archiwum Anny. Na przykład poprzez polecanie książek na AA, linkowanie do naszych wpisów na blogu lub ogólnie kierowanie ludzi na naszą stronę internetową. Pełne przetłumaczenie języka (jeśli nie było już blisko ukończenia). <a %(a_translate)s>Tłumaczenie</a> strony internetowej. Link do historii edycji pokazujący, że dokonałeś znaczących wkładów. Popraw stronę Wikipedii Archiwum Anny w Twoim języku. Uwzględnij informacje ze strony Wikipedii AA w innych językach oraz z naszej strony internetowej i bloga. Dodaj odniesienia do AA na innych odpowiednich stronach. Wolontariat i nagrody 