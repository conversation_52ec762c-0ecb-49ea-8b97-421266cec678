msgid "layout.index.invalid_request"
msgstr "Nieprawidłowe żądanie. Odwiedź %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "Library Genesis"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Biblioteka \"Internet Archive\""

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " i "

msgid "layout.index.header.tagline_and_more"
msgstr "i nie tylko"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Utrwalamy %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Przetwarzamy i udostępniamy %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Cały nasz kod i zebrane dane są dostępne dla wszystkich."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Największa całkowicie otwarta biblioteka w historii."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;książek, %(paper_count)s&nbsp;dokumentów — zachowanych na zawsze."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Największa wyszukiwarka ukrytych bibliotek.⭐️&nbsp;Publikacje z Z-Library, Library Genesis, Sci-Hub i innych. 📈&nbsp;%(book_any)s książek, %(journal_article)s prac naukowych, %(book_comic)s komiksów, %(magazine)s magazynów — zachowanych na zawsze."

msgid "layout.index.header.tagline_short"
msgstr "📚 Największa otwartoźródłowa biblioteka. <br>⭐️ Dane ze Sci-Hub, Lib-gen, Zlib i innych."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Błędne metadane (np. tytuł, opis, zdjęcie okładki)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemy z pobieraniem (np. nie można połączyć, oznaczone kodem błędu, zbyt wolne)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Nie można otworzyć pliku (np. plik uszkodzony, z zabezpieczeniami DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Słaba jakość (np. problemy z formatowaniem, słaba jakość skanu, brakujące strony)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / plik powinien zostać usunięty (np. zawiera reklamy, treści obraźliwe)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Roszczenie z tytułu praw autorskich"

msgid "common.md5_report_type_mapping.other"
msgstr "Inne"

msgid "common.membership.tier_name.bonus"
msgstr "Dodatkowe pobierania"

msgid "common.membership.tier_name.2"
msgstr "Wspaniały Mól Książkowy"

msgid "common.membership.tier_name.3"
msgstr "Błyskotliwy Bibliotekarz"

msgid "common.membership.tier_name.4"
msgstr "Olśniewający Danoskarbnik"

msgid "common.membership.tier_name.5"
msgstr "Cudowny Archiwista"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s razem"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) razem"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s ekstra)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "nieopłacony"

msgid "common.donation.order_processing_status_labels.1"
msgstr "opłacony"

msgid "common.donation.order_processing_status_labels.2"
msgstr "anulowany"

msgid "common.donation.order_processing_status_labels.3"
msgstr "wygasły"

msgid "common.donation.order_processing_status_labels.4"
msgstr "oczekuje na potwierdzenie od Anny"

msgid "common.donation.order_processing_status_labels.5"
msgstr "niepoprawne"

msgid "page.donate.title"
msgstr "Wspomóż"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Twoja <a %(a_donation)s>płatność</a> jest przetwarzana. Dokończ lub anuluj transakcję przed wykonaniem nowej."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s> Wyświetl wszystkie płatności</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archive jest projektem opierającym się o otwarte źródła i otwarty dostęp do danych. Projekt nie jest tworzony w celu osiągnięcia zysku, w związku z czym wspierając nas finansowo pomagasz nam kontynuować dalsze działanie i rozwój. Wszystkim członkom dziękujemy za bycie z nami!❤️"

msgid "page.donate.header.text2"
msgstr "Aby uzyskać więcej informacji, sprawdź <a %(a_donate)s> Najczęściej zadawane pytania o płatnościach</a>."

msgid "page.donate.refer.text1"
msgstr "<a %(a_refer)s>Poleć nas znajomym</a>, aby uzyskać większy limit pobrań!"

msgid "page.donate.bonus_downloads.main"
msgstr "Otrzymujesz %(percentage)s%% szybkich pobrań, ze względu na polecenie przez użytkownika %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Dotyczy to całego okresu członkostwa."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s szybkich pobierań dziennie"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "jeśli dokonasz płatności w tym miesiącu!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / miesiąc"

msgid "page.donate.buttons.join"
msgstr "Dołącz"

msgid "page.donate.buttons.selected"
msgstr "Wybrane"

msgid "page.donate.buttons.up_to_discounts"
msgstr "do %(percentage)s%% zniżki"

msgid "page.donate.perks.scidb"
msgstr "<strong>Nielimitowany</strong> dostęp do badań naukowych z SciDB bez weryfikacji"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Dostęp do API JSON</a>"

msgid "page.donate.perks.refer"
msgstr "Zdobądź <strong>%(percentage)s%% dodatkowych pobrań</strong><a %(a_refer)s>zapraszając znajomych</a>."

msgid "page.donate.perks.credits"
msgstr "Twój pseudonim lub anonimowa wzmianka na liście darczyńców"

msgid "page.donate.perks.previous_plus"
msgstr "Zestaw korzyści z niższego progu oraz:"

msgid "page.donate.perks.early_access"
msgstr "Wcześniejszy dostęp do nowych funkcji"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Dostęp do grupy na Telegramie z informacjami \"zza kulis\" dotyczących aktualizacji projektu"

msgid "page.donate.perks.adopt"
msgstr "\"Adoptuj plik .torrent\": Twoja nazwa użytkownika lub wiadomość zawarta w nazwie pliku .torrent <div %(div_months)s>jednorazowo co 12 miesięcy przy aktywnym członkostwie</div>"

msgid "page.donate.perks.legendary"
msgstr "Odznaczenie jako zasłużony za zachowywanie dziedzictwa, wiedzy i kultury ludzkości"

msgid "page.donate.expert.title"
msgstr "Tytuł i dostęp ekspercki"

msgid "page.donate.expert.contact_us"
msgstr "skontaktuj się z nami"

msgid "page.donate.small_team"
msgstr "Jesteśmy małą grupą wolontariuszy. Na odpowiedź możesz poczekać od 7 do 14 dni."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Nieograniczony</strong> dostęp do szybkich łączy"

msgid "page.donate.expert.direct_sftp"
msgstr "Bezpośredni dostęp do serwerów<strong>SFTP</strong>"

msgid "page.donate.expert.enterprise_donation"
msgstr "Darowizna na poziomie przedsiębiorstwa lub wymiana na nowe kolekcje (np. nowe skany, zbiory danych OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Z radością przyjmiemy wpłaty od zamożnych osób indywidualnych i instytucji. "

msgid "page.donate.header.large_donations"
msgstr "Przy wsparciu projektu kwotą powyżej 5000$, prosimy o kontakt pod adresem %(email)s."

msgid "page.donate.header.recurring"
msgstr "Należy pamiętać, że chociaż członkostwa na tej stronie są „miesięczne”, są to jednorazowe darowizny (nieodnawialne). Zobacz <a %(faq)s>Najczęściej zadawane pytania</a>."

msgid "page.donate.without_membership"
msgstr "Jeśli chcesz dokonać wpłaty (w dowolnej wysokości) bez uzyskiwania członkostwa, możesz użyć tego adresu kryptowaluty Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Wybierz metodę płatności."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(na tę chwilę niedostępny)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Karta podarunkowa %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Karta płatnicza (przy użyciu aplikacji)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kryptowaluty %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "CashApp (aplikacja)"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Karta kredytowa/debetowa"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (zwykły)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Karta płatnicza / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Apple/Google (BMC)/Karta kredytowa/debetowa"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazylia)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Karta płatnicza"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Karta płatnicza (zapasowa)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Karta płatnicza 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Przy wyborze płatności kryptowalutami możliwym jest wsparcie przy pomocy BTC, ETH, XMR lub SOL. Wybierz tę opcję, jeżeli posiadasz wiedzę na temat kryptowalut."

msgid "page.donate.payment.desc.crypto2"
msgstr "Przy płatnościach kryptowalutami można użyć BTC, ETH, XMR i innych."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Jeżeli używasz kryptowalut po raz pierwszy, sugerujemy użycie %(options)s do zakupu i przekazania darowizny w Bitcoinie (najstarszej i najczęściej używanej kryptowalucie)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "By wspomóc nas przez PayPal, należy użyć PayPal Crypto, które pozwoli nam na pozostanie anonimowymi. Doceniamy Twój wkład w nauczenie się sposobu wykonywania płatności tą metodą, to bardzo nam pomaga."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Wspomóż za pomocą PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Wspomóż za pomocą Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Wsparcie za pomocą Cash App będzie najlepszą i najprostszą metodą wsparcia naszej działalności!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Zwróć uwagę, że przy transakcjach poniżej %(amount)s, Cash App może pobierać opłatę w wysokości %(fee)s. Dla %(amount)s lub więcej usługa jest bezpłatna!"

msgid "page.donate.payment.desc.revolut"
msgstr "Przekaż darowiznę za pomocą Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Jeżeli posiadasz Revolut, przekazanie darowizny za jego pomocą będzie najprostsze!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Wspomóż za pomocą karty płatniczej."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay i Apple Pay też mogą zadziałać."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Dla mniejszych darowizn opłaty z kart kredytowych mogą wyeliminować naszą zniżkę w wysokości %(discount)s, więc zalecamy dłuższe subskrypcje."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Dla mniejszych darowizn opłaty są wysokie, więc zalecamy dłuższe subskrypcje."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Za pomocą Binance możesz zakupić Bitcoin przy pomocy karty płatniczej lub konta bankowego, który następnie możesz nam przekazać. W ten sposób możemy pozostać bezpieczni i anonimowi przy Twojej wpłacie."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance jest dostępny w prawie każdym kraju i obsługuje większość banków oraz kart płatniczych. Polecamy przede wszystkim tę metodę. Doceniamy, że poświęcasz czas na naukę, jak przekazać darowiznę przy pomocy tej metody, ponieważ jest to nam pomocne."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Przekaż darowiznę za pomocą swojego zwykłego konta PayPal."

msgid "page.donate.payment.desc.givebutter"
msgstr "Przekaż darowiznę za pomocą karty płatniczej, PayPala lub Venmo. Możesz wybrać spośród tych opcji na następnej stronie."

msgid "page.donate.payment.desc.amazon"
msgstr "Wspomóż za pomocą karty podarunkowej Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Musimy zaokrąglać płatności do wartości akceptowanych przez naszych partnerów handlowych (minimum %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>WAŻNE:</strong> Mamy wsparcie tylko na Amazon.com, nie obsługujemy domen krajowych, takich jak: .de, .co.uk, .ca i innych."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>WAŻNE:</strong> Ta opcja jest przeznaczona dla %(amazon)s. Jeśli chcesz użyć innej strony Amazon, wybierz ją powyżej."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Ta metoda wykorzystuje dostawcę kryptowalut jako podmiot przeprowadzający konwersję pośrednią między walutami. Mechanizm działania może być kłopotliwy, więc prosimy o korzystanie ze sposobu tylko wtedy, gdy inne metody płatności zawiodą. Nie działa we wszystkich krajach."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Przekaż darowiznę za pomocą karty płatniczej, używając aplikacji Alipay (bardzo łatwej do skonfigurowania)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Zainstaluj aplikację Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Zainstaluj aplikację Alipay przy pomocy <a %(a_app_store)s>Apple App Store</a> lub <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Zarejestruj się, używając swojego numeru telefonu."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nie są wymagane dodatkowe dane osobowe."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Dodaj kartę płatniczą"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Obsługiwane: Visa, MasterCard, JCB, Diners Club i Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Sprawdź <a %(a_alipay)s>ten przewodnik</a> aby uzyskać więcej informacji."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nie możemy bezpośrednio obsługiwać kart płatniczych, ponieważ banki nie chcą z nami współpracować ☹. Jednakże istnieje kilka sposobów na użycie kart płatniczych przy pomocy innych sposobów płatności:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Karta Podarunkowa Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Wyślij nam karty podarunkowe Amazon.com używając karty płatniczej."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay obsługuje międzynarodowe karty płatnicze. Sprawdź <a %(a_alipay)s>ten przewodnik</a> aby uzyskać więcej informacji."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) obsługuje międzynarodowe karty płatnicze. W aplikacji WeChat przejdź do opcji „Me → Services → Wallet → Add a Card”. Jeśli tego nie widzisz, włącz tę opcję, przechodząc do „Me → Settings → General → Tools → Weixin Pay → Enable”."

msgid "page.donate.ccexp.crypto"
msgstr "Możesz zakupić kryptowaluty używając karty płatniczej."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Usługi ekspresowe Crypto"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Usługi ekspresowe są wygodne, lecz pobierane są przy ich pomocy wyższe opłaty."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "W zamian można użyć giełdy kryptowalut aby szybko dokonać większej darowizny i nie masz nic przeciwko opłacie w wysokości od 5 do 10$."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Upewnij się, że wysyłasz dokładną kwotę kryptowaluty pokazaną na stronie darowizny, a nie kwotę w walucie lokalnej."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "W przeciwnym razie opłata zostanie pominięta i nie będziemy mogli automatycznie aktywować Twojego członkostwa."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s w zależności od kraju, brak weryfikacji przy pierwszej transakcji)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, brak weryfikacji przy pierwszej transakcji)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, brak weryfikacji przy pierwszej transakcji)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Jeśli którakolwiek z tych informacji jest nieaktualna, prosimy o ich aktualizację kontaktując się z nami przy pomocy poczty elektronicznej."

msgid "page.donate.payment.desc.bmc"
msgstr "Dla kart płątniczych, Apple Pay i Google Pay używamy systemu „Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Używając tej metody, jedna „kawa” jest równa 5$, przez co Twoja darowizna zostanie zaokrąglona do najbliższej wielokrotności 5."

msgid "page.donate.duration.intro"
msgstr "Wybierz jak długo zamierzasz nas wspierać."

msgid "page.donate.duration.1_mo"
msgstr "Miesiąc"

msgid "page.donate.duration.3_mo"
msgstr "3 miesiące"

msgid "page.donate.duration.6_mo"
msgstr "6 miesięcy"

msgid "page.donate.duration.12_mo"
msgstr "12 miesięcy"

msgid "page.donate.duration.24_mo"
msgstr "24 miesiące"

msgid "page.donate.duration.48_mo"
msgstr "48 miesięcy"

msgid "page.donate.duration.96_mo"
msgstr "96 miesięcy"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>Po <span %(span_discount)s></span> rabacie</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Ta metoda płatności wymaga minimum %(amount)s. Proszę wybierz inny okres lub metodę płatności."

msgid "page.donate.buttons.donate"
msgstr "Przekaż darowiznę"

msgid "page.donate.payment.maximum_method"
msgstr "Ta metoda płatności umożliwia przekazanie maksimum %(amount)s. Proszę wybierz inny okres lub metodę płatności."

msgid "page.donate.login2"
msgstr "Aby zostać członkiem, prosimy się <a %(a_login)s>Zalogować lub Zarejestrować</a>. Dziękujemy za wsparcie!"

msgid "page.donate.payment.crypto_select"
msgstr "Wybierz kryptowalutę, którą preferujesz:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(najniższa możliwa kwota)"

msgid "page.donate.coinbase_eth"
msgstr "(użyj podczas wysyłania Ethereum przy pomocy Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(uwaga: wymagana wysoka kwota minimalna)"

msgid "page.donate.submit.confirm"
msgstr "Naciśnij przycisk \"Wspomóż\" aby potwierdzić wsparcie."

msgid "page.donate.submit.button"
msgstr "Wspomóż <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Wciąż możesz wycofać płatność podczas jej realizacji."

msgid "page.donate.submit.success"
msgstr "✅ Przekierowywanie na stronę wsparcia finansowego projektu…"

msgid "page.donate.submit.failure"
msgstr "❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / miesiąc"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "za miesiąc"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "za 3 miesiące"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "za 6 miesięcy"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "za 12 miesięcy"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "za 24 miesiące"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "za 48 miesięcy"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "za 96 miesięcy"

msgid "page.donate.submit.button.label.1_mo"
msgstr "za miesiąc “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "za 3 miesiące “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "za 6 miesięcy \"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "za 12 miesięcy \"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "za 24 miesiące \"%(tier_name)s\""

msgid "page.donate.submit.button.label.48_mo"
msgstr "za 48 miesięcy „%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "za 96 miesięcy „%(tier_name)s”"

msgid "page.donation.title"
msgstr "Darowizna"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Suma: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / miesiąc za okres %(duration)s miesięcy, wraz ze%(discounts)s zniżką)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Suma: %(total)s <span %(span_details)s> (%(monthly_amount_usd)s / miesiąc za okres %(duration)s miesięcy)</span>"

msgid "page.donation.header.status"
msgstr "Stan: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identyfikator: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Anuluj"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Czy na pewno chcesz anulować? Anulowanie opłaconej transakcji nie jest wskazane."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Tak, chcę anulować"

msgid "page.donation.header.cancel.success"
msgstr "✅ Twoja darowizna została anulowana."

msgid "page.donation.header.cancel.new_donation"
msgstr "Przekaż nową darowiznę"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie."

msgid "page.donation.header.reorder"
msgstr "Wykonaj ponownie"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Darowizna opłacona. Jeżeli chcesz zobaczyć ponownie instrukcje dotyczące płatności, naciśnij tutaj:"

msgid "page.donation.old_instructions.show_button"
msgstr "Wyświetl nieaktualne instrukcje odnośnie płatności"

msgid "page.donation.thank_you_donation"
msgstr "Dziękujemy za wsparcie finansowe!"

msgid "page.donation.thank_you.secret_key"
msgstr "Zapisz swój tajny klucz do logowania, jeżeli jeszcze tego nie zrobiłeś:"

msgid "page.donation.thank_you.locked_out"
msgstr "W innym wypadku możesz utracić dostęp do tego konta!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Instrukcje dotyczące płatności stały się nieaktualne. Jeżeli chcesz ponownie przekazać darowiznę, naciśnij przycisk \"Wykonaj ponownie\"."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Ważna uwaga;</strong> Ceny kryptowalut mogą zmieniać się z godziny na godzinę, w niektórych momentach ich różnica może wynosić nawet 20%% w ciągu paru minut. Mimo wszystko jest to mniej niż zapłacilibyśmy u wielu operatorów płatności, którzy często obciążają \"skryte organizacje charytatywne\" takie jak nasza w granicach od 50 do 60 %% kwoty wpłat. <u>Jeżeli wyślesz nam informację o kwocie, która była wyświetlana w chwili przelewu, a różniłaby się znacznie (byłaby za niska lub za wysoka) od progu który został wybrany, członkostwo zostanie zmienione na wybrane podczas dokonywania płatności</u> (do momentu, w którym informacja o płatności nie będzie starsza niż kilka godzin). Doceniamy wysiłek włożony w to, aby nas wspierać! ❤️"

msgid "page.donation.expired"
msgstr "Ta darowizna wygasła. Prosimy anulować i utworzyć nową."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instrukcje dotyczące kryptowalut"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span> Przekaż środki na jedno z naszych kont kryptowalutowych"

msgid "page.donation.payment.crypto.text1"
msgstr "Przekaż darowiznę równą %(total)s na jeden z poniższych adresów:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Zakup Bitcoin na platformie PayPal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Odnajdź stronę \"Kryptowaluty\" w swojej aplikacji PayPal lub na stronie internetowej. Zazwyczaj jest ona zlokalizowana pod zakładką \"Finanse\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Podążaj za instrukcjami aby zakupić Bitcoin (BTC). Wystarczającym jest kupienie takiej ilości kryptowaluty, która będzie przekazana w darowiznie, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s> 2 </span> Przekaż Bitcoiny na nasz adres"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Przejdź do podstrony \"Bitcoin\" znajdującej się w aplikacji PayPal lub na stronie Internetowej. Naciśnij przycisk %(transfer_icon)s \"Transfer\", a następnie \"Send\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Wprowadź nasz adres Bitcoin (BTC) jako odbiorcę, a następnie podążaj za instrukcjami aby wysłać swoją darowiznę, %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrukcje dot. kart płatniczych"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Przekaż darowiznę przy pomocy karty płatniczej za pośrednictwem naszej strony"

msgid "page.donation.donate_on_this_page"
msgstr "Wpłać %(amount)s na <a %(a_page)s>tej stronie</a>."

msgid "page.donation.stepbystep_below"
msgstr "Zobacz przewodnik krok po kroku poniżej."

msgid "page.donation.status_header"
msgstr "Stan:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Oczekiwanie na potwierdzenie (odśwież stronę, aby sprawdzić)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Oczekiwanie na przelew (odśwież stronę żeby sprawdzić)…"

msgid "page.donation.time_left_header"
msgstr "Pozostały czas:"

msgid "page.donation.might_want_to_cancel"
msgstr "(możesz anulować i utworzyć nową darowiznę)"

msgid "page.donation.reset_timer"
msgstr "Aby zresetować licznik, wystarczy utworzyć nową darowiznę."

msgid "page.donation.refresh_status"
msgstr "Aktualizacja stanu"

msgid "page.donation.footer.issues_contact"
msgstr "Jeśli napotkałeś na problemy, prosimy skontaktować się na %(email)s w treści zawierając tak dużo informacji jak to możliwe (np. zrzuty ekranu)."

msgid "page.donation.expired_already_paid"
msgstr "Jeśli już zapłaciłeś:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Czasami potwierdzenie może zająć do 24 godzin, więc należy pamiętać o tym, aby strona została odświeżona (nawet jeśli wygasła ze względu wcześniejszego zaksięgowania transakcji)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Kup PYUSD na PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Postępuj zgodnie z instrukcjami, aby kupić PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Kup nieco więcej (zalecamy %(more)s) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Zatrzymasz wszystko, co pozostało."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Przejdź do strony \"PYUSD\" w aplikacji lub witrynie PayPal. Naciśnij przycisk \"Przelew\" %(icon)s, a następnie \"Wyślij\"."

msgid "page.donation.transfer_amount_to"
msgstr "Przekaż %(amount)s do %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Kup Bitcoin (BTC) w aplikacji Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Przejdź do strony „Bitcoin” (BTC) w aplikacji Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Kup trochę więcej (zalecamy %(more)s więcej) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Resztę zachowasz dla siebie."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Przelej Bitcoin na nasz adres"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Kliknij przycisk „Wyślij bitcoin”, aby dokonać „wypłaty”. Przełącz z dolarów na BTC, naciskając ikonę %(icon)s. Wprowadź poniżej kwotę BTC i kliknij „Wyślij”. Jeśli utkniesz, zobacz <a %(help_video)s>ten film</a>."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "W przypadku małych darowizn (poniżej $25) może być konieczne użycie Rush lub Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Kup Bitcoin (BTC) w Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Przejdź do strony „Crypto” w aplikacji Revolut , aby kupić Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Kup trochę więcej (sugerujemy powiększenie salda o %(more)s więcej ) niż kwota, którą przekazujesz (%(amount)s), aby pokryć opłaty transakcyjne. Resztę zachowasz dla siebie."

msgid "page.donation.revolut.step2"
msgstr "Przelej kryptowalutę Bitcoin na nasz adres"

msgid "page.donation.revolut.step2.transfer"
msgstr "Naciśnij przycisk „Wyślij bitcoin”, aby dokonać \"przelewu\". Przełącz z Euro na BTC, naciskając ikonę %(icon)s. Wprowadź poniżej kwotę BTC i kliknij „Wyślij”. Jeśli utkniesz, zapoznaj się z tym <a %(help_video)s> filmem</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Upewnij się, że używasz poniższej kwoty związanej z BTC, <em>NIE</em> natomiast Euro, tudzież dolarów, w przeciwnym razie nie otrzymamy poprawnej kwoty i nie będziemy mogli automatycznie potwierdzić Twojego członkostwa."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "W przypadku małych darowizn (poniżej $25) może być konieczne użycie Rush lub Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Użyj dowolnej z poniższych usług „karta kredytowa na Bitcoin”, które zajmują tylko kilka minut:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Wypełnij poniższe dane w formularzu:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Kwota BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Proszę użyć <span %(underline)s>dokładnie tej kwoty</span>. Całkowity koszt może być wyższy z powodu opłat za kartę kredytową. W przypadku małych kwot może to niestety przewyższać naszą zniżkę."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Adres Bitcoin/BTC (zewnętrzny portfel):"

msgid "page.donation.crypto_instructions"
msgstr "Instrukcje %(coin_name)s"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Obsługujemy tylko standardową wersję kryptowalut, bez egzotycznych sieci lub wersji monet. Potwierdzenie transakcji może zająć do godziny, w zależności od monety."

msgid "page.donation.crypto_qr_code_title"
msgstr "Zeskanuj kod QR, aby zapłacić"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Zeskanuj ten kod QR za pomocą aplikacji Portfel Crypto, aby szybko wypełnić szczegóły płatności"

msgid "page.donation.amazon.header"
msgstr "Karta podarunkowa Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Skorzystaj z <a %(a_form)s>oficjalnego formularza Amazon.com</a>, aby wysłać nam kartę podarunkową o wartości %(amount)s na podany adres e-mail."

msgid "page.donation.amazon.only_official"
msgstr "Nie akceptujemy innych metod wysyłania kart podarunkowych, <strong>tylko wysłane bezpośrednio z oficjalnego formularza na stronie Amazon.com</strong>. Nie będziemy mogli zwrócić karty podarunkowej, jeśli nie skorzystasz z tego formularza."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Wprowadź dokładną kwotę: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "NIE pisz własnych wiadomości."

msgid "page.donation.amazon.form_to"
msgstr "Adres e-mail odbiorcy \"Do\" w formularzu:"

msgid "page.donation.amazon.unique"
msgstr "Unikatowe dla twojego konta, nie udostępniaj."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Użyj tylko raz."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Oczekiwanie na kartę podarunkową... (odśwież stronę, aby sprawdzić)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Po wysłaniu karty podarunkowej system potwierdzi ją w ciągu kilku minut. Jeśli to nie zadziała, spróbuj ponownie wysłać kartę podarunkową (<a %(a_instr)s>instrukcje</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Jeśli to nadal nie zadziała, napisz do nas, a ręcznie sprawdzimy zgłoszenie (może to potrwać kilka dni) i pamiętaj, aby wspomnieć, czy próbowałeś już ponownie wysłać zgłoszenie."

msgid "page.donation.amazon.example"
msgstr "Przykład:"

msgid "page.donate.strange_account"
msgstr "Nazwa lub zdjęcie konta może wydać się podejrzane, lecz nie ma powodu do obaw! Konta te są wykorzystywane przez naszych partnerów do obsługi darczyńców. Nie są ofiarą wycieku danych."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instrukcje dotyczące Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s> 1 </span> Darowizna poprzez Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Przekaż łączną kwotę %(total)s za pomocą <a %(a_account)s>tego konta Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Jeśli strona darowizn zostanie zablokowana, spróbuj innego połączenia internetowego (np. VPN lub internet w telefonie)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Niestety, strona Alipay jest często dostępna tylko z <strong>Chin kontynentalnych</strong>. Może być konieczne tymczasowe wyłączenie VPN lub użycie VPN do Chin kontynentalnych (czasami działa również Hongkong)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Dokonaj darowizny (zeskanuj kod QR lub naciśnij przycisk)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Otwórz <a %(a_href)s>stronę darowizny z kodem QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Zeskanuj kod QR za pomocą aplikacji Alipay lub naciśnij przycisk, aby otworzyć aplikację Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Prosimy o cierpliwość; strona może się ładować dłużej, ponieważ znajduje się w Chinach."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instrukcje WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Przekaż darowiznę na WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Przekaż łączną kwotę %(total)s za pomocą <a %(a_account)s>tego konta WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instrukcje dotyczące Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s> 1 </span> Przekaż darowiznę poprzez Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Przekaż darowiznę równą %(total)s używając <a %(a_account)s> poniższego konta Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span> Wyślij potwierdzenie transakcji na adres e-mail"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Wyślij paragon lub zrzut ekranu na swój osobisty adres weryfikacyjny. NIE używaj tego adresu e-mail do darowizny przez PayPal."

msgid "page.donation.footer.text1"
msgstr "Wyślij potwierdzenie transakcji lub zrzut ekranu na indywidualny adres do weryfikacji:"

msgid "page.donation.footer.crypto_note"
msgstr "Jeżeli kurs wymiany kryptowalut zmienił się podczas transakcji, upewnij się, czy załączone zostało potwierdzenie pokazujące oryginalną kwotę wymiany. Doceniamy Twój wysiłek włożony w użytkowanie kryptowalut, wiele to dla nas znaczy!"

msgid "page.donation.footer.text2"
msgstr "Gdy potwierdzenie zostało wysłane, naciśnij ten przycisk, aby Anna mogła go ręcznie zweryfikować (może to potrwać kilka dni):"

msgid "page.donation.footer.button"
msgstr "Tak, wysłałem moje potwierdzenie"

msgid "page.donation.footer.success"
msgstr "✅ Dziękujemy za Twoją darowiznę! Anna aktywuje Twoje członkostwo w sposób manualny w ciągu kilku dni."

msgid "page.donation.footer.failure"
msgstr "❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie."

msgid "page.donation.stepbystep"
msgstr "Instrukcja krok-po-kroku"

msgid "page.donation.crypto_dont_worry"
msgstr "Niektóre z kroków wspominają o portfelach kryptowalutowych, ale nie martw się, nie musisz uczyć się niczego o kryptowalutach."

msgid "page.donation.hoodpay.step1"
msgstr "1. Podaj swój e-mail."

msgid "page.donation.hoodpay.step2"
msgstr "2. Wybierz formę płatności."

msgid "page.donation.hoodpay.step3"
msgstr "3. Wybierz ponownie formę płatności."

msgid "page.donation.hoodpay.step4"
msgstr "4. Wybierz portfel \"Self-hosted\"."

msgid "page.donation.hoodpay.step5"
msgstr "5. Kliknij “I confirm ownership”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Powinieneś otrzymać potwierdzenie e-mailem. Prześlij je do nas, a my jak najszybciej potwierdzimy twoją darowiznę."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Proszę poczekać co najmniej <span %(span_hours)s>24 godziny</span> (i odświeżyć tę stronę) przed skontaktowaniem się z nami."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Jeśli popełniłeś błąd podczas płatności, nie możemy dokonać zwrotu, ale postaramy się to naprawić."

msgid "page.my_donations.title"
msgstr "Moje darowizny"

msgid "page.my_donations.not_shown"
msgstr "Szczegóły dotyczące darowizn nie są publikowane."

msgid "page.my_donations.no_donations"
msgstr "Nie posiadasz darowizn. <a %(a_donate)s> Wykonaj pierwszą darowiznę. </a>"

msgid "page.my_donations.make_another"
msgstr "Wykonaj dodatkową darowiznę."

msgid "page.downloaded.title"
msgstr "Pobrane pliki"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Pobrania z szybkich serwerów partnerskich są oznaczone przez %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Jeśli pobrałeś plik zarówno z szybkimi, jak i wolnymi pobraniami, pojawi się on dwukrotnie."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Szybkie pobrania w ciągu ostatnich 24 godzin liczą się do dziennego limitu."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Wszystkie czasy są w UTC."

msgid "page.downloaded.not_public"
msgstr "Informacja o pobranych plikach nie jest wyświetlana publicznie."

msgid "page.downloaded.no_files"
msgstr "Nie pobrano plików."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ostatnie 18 godzin"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Wcześniej"

msgid "page.account.logged_in.title"
msgstr "Konto"

msgid "page.account.logged_out.title"
msgstr "Zaloguj / Zarejestruj"

msgid "page.account.logged_in.account_id"
msgstr "ID konta: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Profil publiczny: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Tajny klucz (nie udostępniaj go nikomu!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "pokaż"

msgid "page.account.logged_in.membership_has_some"
msgstr "Członkostwo: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(extend)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Członkostwo: <strong> Brak </strong> <a %(a_become)s>(zostań członkiem)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Wykorzystane szybkie pobieranie (ostatnie 24 godziny): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "jakie pobrania?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Tajna grupa na Telegramie: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Dołącz do nas!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Ulepsz do <a %(a_tier)s>wyższego poziomu</a>, aby dołączyć do naszej grupy."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Skontaktuj się z Anną pod %(email)s, w przypadku chęci zmiany progu członkostwa na wyższy."

msgid "page.contact.title"
msgstr "Email kontaktowy"

msgid "page.account.logged_in.membership_multiple"
msgstr "Można połączyć kilka subskrypcji (szybkie pobieranie w ciągu 24 godzin zostanie zsumowane)."

msgid "layout.index.header.nav.public_profile"
msgstr "Profil publiczny"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Pobrane pliki"

msgid "layout.index.header.nav.my_donations"
msgstr "Moje darowizny"

msgid "page.account.logged_in.logout.button"
msgstr "Wyloguj"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Wylogowano. Odśwież stronę aby zalogować ponownie."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Wystąpił błąd. Proszę odświeżyć stronę i spróbować ponownie."

msgid "page.account.logged_out.registered.text1"
msgstr "Rejestracja zakończona pomyślnie! Twój sekretny klucz to: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Zapisz ten klucz w bezpiecznym miejscu, ponieważ utracenie go wiąże się z utratą dostępu do konta."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s<strong> Zakładka. </strong> Możesz dodać tę stronę do zakładek aby odzyskać swój klucz.</li><li %(li_item)s><strong> Pobierz. </strong> naciśnij <a %(a_download)s>ten odnośnik</a> aby pobrać swój klucz.</li><li %(li_item)s><strong> Menedżer haseł.</strong> Dla Twojej wygody, klucz poniżej jest uzupełniony wstępnie, więc po zalogowaniu będzie on dostępny w menedżerze haseł. </li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Wprowadź Twój sekretny klucz, aby zalogować:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Sekretny klucz"

msgid "page.account.logged_out.key_form.button"
msgstr "Zaloguj"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Sekretny klucz jest błędny. Zweryfikuj prawidłowość swojego klucza i spóbuj ponownie lub zarejestruj nowe konto poniżej."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Nie zgub swojego klucza!"

msgid "page.account.logged_out.register.header"
msgstr "Nie posiadasz konta?"

msgid "page.account.logged_out.register.button"
msgstr "Zarejestruj nowe konto"

msgid "page.login.lost_key"
msgstr "Jeśli straciłeś swój klucz, <a %(a_contact)s>skontaktuj się z nami</a> podając jak najwięcej szczegółowych informacji."

msgid "page.login.lost_key_contact"
msgstr "Konieczne może być stworzenie nowego, tymczasowego konta do kontaktu z nami."

msgid "page.account.logged_out.old_email.button"
msgstr "Starsze konto oparte o adres e-mail? Wprowadź swój <a %(a_open)s> adres tutaj</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "Edytuj"

msgid "page.list.edit.button"
msgstr "Zapisz"

msgid "page.list.edit.success"
msgstr "✅ Zapisano. Odśwież stronę."

msgid "page.list.edit.failure"
msgstr "❌ Wystąpił błąd. Spróbuj ponownie."

msgid "page.list.by_and_date"
msgstr "Lista %(by)s, utworzona <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Lista jest pusta."

msgid "page.list.new_item"
msgstr "Dodaj lub usuń pozycje z tej listy poprzez znalezienie odpowiedniego pliku i otworzenie zakładki \"Lista\"."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil nie znaleziony."

msgid "page.profile.header.edit"
msgstr "Edytuj"

msgid "page.profile.change_display_name.text"
msgstr "Zmień wyświetlaną nazwę. Twój identyfikator (część następująca po \"#\") nie może zostać zmieniona."

msgid "page.profile.change_display_name.button"
msgstr "Zapisz"

msgid "page.profile.change_display_name.success"
msgstr "✅ Zapisano. Odśwież stronę."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Wystąpił błąd. Spróbuj ponownie."

msgid "page.profile.created_time"
msgstr "Profil utworzony <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listy"

msgid "page.profile.lists.no_lists"
msgstr "Brak list"

msgid "page.profile.lists.new_list"
msgstr "Utwórz nową listę poprzez znalezienie pliku i otworzenie zakładki \"Lista\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reforma praw autorskich jest konieczna dla bezpieczeństwa narodowego"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Chińskie LLM (w tym DeepSeek) są szkolone na moim nielegalnym archiwum książek i artykułów — największym na świecie. Zachód musi zreformować prawo autorskie jako kwestię bezpieczeństwa narodowego."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artykuły towarzyszące od TorrentFreak: <a %(torrentfreak)s>pierwszy</a>, <a %(torrentfreak_2)s>drugi</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Niedawno „biblioteki cieni” były na wymarciu. Sci-Hub, ogromne nielegalne archiwum prac naukowych, przestało przyjmować nowe prace z powodu pozwów sądowych. „Z-Library”, największa nielegalna biblioteka książek, widziała, jak jej rzekomi twórcy zostali aresztowani pod zarzutem naruszenia praw autorskich. Niesamowicie udało im się uciec z aresztu, ale ich biblioteka jest nadal zagrożona."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Kiedy Z-Library stanęła w obliczu zamknięcia, już wcześniej zrobiłem kopię zapasową całej jej biblioteki i szukałem platformy, aby ją umieścić. To była moja motywacja do założenia Archiwum Anny: kontynuacja misji stojącej za wcześniejszymi inicjatywami. Od tego czasu staliśmy się największą biblioteką cieni na świecie, goszcząc ponad 140 milionów chronionych prawem autorskim tekstów w różnych formatach — książki, prace naukowe, czasopisma, gazety i inne."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mój zespół i ja jesteśmy ideologami. Wierzymy, że zachowanie i udostępnianie tych plików jest moralnie słuszne. Biblioteki na całym świecie doświadczają cięć w finansowaniu, a nie możemy również powierzyć dziedzictwa ludzkości korporacjom."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Potem pojawiła się sztuczna inteligencja. Praktycznie wszystkie duże firmy budujące LLM skontaktowały się z nami, aby szkolić się na naszych danych. Większość (ale nie wszystkie!) firm z USA zrewidowała swoje podejście, gdy zdała sobie sprawę z nielegalnego charakteru naszej pracy. W przeciwieństwie do nich, chińskie firmy z entuzjazmem przyjęły naszą kolekcję, najwyraźniej nie martwiąc się jej legalnością. Jest to godne uwagi, biorąc pod uwagę rolę Chin jako sygnatariusza niemal wszystkich głównych międzynarodowych traktatów dotyczących praw autorskich."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Udostępniliśmy szybki dostęp około 30 firmom. Większość z nich to firmy LLM, a niektóre to brokerzy danych, którzy będą odsprzedawać naszą kolekcję. Większość z nich pochodzi z Chin, ale współpracowaliśmy także z firmami z USA, Europy, Rosji, Korei Południowej i Japonii. DeepSeek <a %(arxiv)s>przyznał</a>, że wcześniejsza wersja była trenowana na części naszej kolekcji, choć są powściągliwi w kwestii najnowszego modelu (prawdopodobnie również trenowanego na naszych danych)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Jeśli Zachód chce pozostać na czele wyścigu LLM, a ostatecznie AGI, musi szybko przemyśleć swoje stanowisko w sprawie praw autorskich. Niezależnie od tego, czy zgadzasz się z nami w kwestii moralnej, czy nie, staje się to teraz kwestią ekonomiczną, a nawet bezpieczeństwa narodowego. Wszystkie bloki sił budują sztucznych supernaukowców, superhakerów i superarmie. Wolność informacji staje się kwestią przetrwania dla tych krajów — nawet kwestią bezpieczeństwa narodowego."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Nasz zespół pochodzi z całego świata i nie mamy konkretnego ukierunkowania. Ale zachęcamy kraje z silnymi prawami autorskimi do wykorzystania tego egzystencjalnego zagrożenia do ich reformy. Co więc zrobić?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Nasza pierwsza rekomendacja jest prosta: skrócić okres ochrony praw autorskich. W USA prawa autorskie są przyznawane na 70 lat po śmierci autora. To absurdalne. Możemy to dostosować do patentów, które są przyznawane na 20 lat po złożeniu. To powinno być więcej niż wystarczający czas, aby autorzy książek, artykułów, muzyki, sztuki i innych dzieł twórczych zostali w pełni wynagrodzeni za swoje wysiłki (w tym długoterminowe projekty, takie jak adaptacje filmowe)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Następnie, co najmniej, decydenci powinni uwzględnić wyjątki dla masowego zachowania i rozpowszechniania tekstów. Jeśli głównym zmartwieniem jest utrata przychodów od indywidualnych klientów, dystrybucja na poziomie osobistym mogłaby pozostać zabroniona. Z kolei ci, którzy są w stanie zarządzać ogromnymi repozytoriami — firmy szkolące LLM, wraz z bibliotekami i innymi archiwami — byliby objęci tymi wyjątkami."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Niektóre kraje już wprowadzają wersję tego. TorrentFreak <a %(torrentfreak)s>doniósł</a>, że Chiny i Japonia wprowadziły wyjątki AI do swoich praw autorskich. Nie jest dla nas jasne, jak to współgra z międzynarodowymi traktatami, ale z pewnością daje to ochronę ich krajowym firmom, co wyjaśnia to, co widzieliśmy."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Jeśli chodzi o Archiwum Anny — będziemy kontynuować naszą podziemną pracę zakorzenioną w moralnym przekonaniu. Jednak naszym największym pragnieniem jest wyjście na światło dzienne i legalne zwiększenie naszego wpływu. Prosimy o reformę praw autorskich."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Przeczytaj towarzyszące artykuły TorrentFreak: <a %(torrentfreak)s>pierwszy</a>, <a %(torrentfreak_2)s>drugi</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Zwycięzcy nagrody za wizualizację ISBN o wartości 10 000 USD"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Otrzymaliśmy niesamowite zgłoszenia do nagrody za wizualizację ISBN o wartości 10 000 USD."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Kilka miesięcy temu ogłosiliśmy <a %(all_isbns)s>nagrodę o wartości 10 000 USD</a> za stworzenie najlepszej możliwej wizualizacji naszych danych pokazujących przestrzeń ISBN. Podkreśliliśmy pokazanie, które pliki już zarchiwizowaliśmy, a później zestaw danych opisujący, ile bibliotek posiada ISBN (miara rzadkości)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Jesteśmy przytłoczeni odpowiedzią. Było tak wiele kreatywności. Wielkie podziękowania dla wszystkich, którzy wzięli udział: wasza energia i entuzjazm są zaraźliwe!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Ostatecznie chcieliśmy odpowiedzieć na następujące pytania: <strong>które książki istnieją na świecie, ile już zarchiwizowaliśmy i na których książkach powinniśmy się skupić następnie?</strong> Wspaniale jest widzieć, że tak wiele osób troszczy się o te pytania."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Zaczęliśmy od podstawowej wizualizacji sami. W mniej niż 300kb, ten obraz zwięźle przedstawia największą w pełni otwartą „listę książek” kiedykolwiek zgromadzoną w historii ludzkości:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Wszystkie ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Pliki w Archiwum Anny"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Wycieki danych CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indeks eBooków EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Książki"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Globalny Rejestr Wydawców ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Rosyjska Biblioteka Państwowa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperialna Biblioteka Trantora"

#, fuzzy
msgid "common.back"
msgstr "Wstecz"

#, fuzzy
msgid "common.forward"
msgstr "Dalej"

#, fuzzy
msgid "common.last"
msgstr "Ostatni"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Proszę zobaczyć <a %(all_isbns)s>oryginalny wpis na blogu</a> po więcej informacji."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Wydaliśmy wyzwanie, aby to ulepszyć. Przyznalibyśmy nagrodę za pierwsze miejsce w wysokości 6 000 USD, za drugie miejsce 3 000 USD, a za trzecie miejsce 1 000 USD. Ze względu na ogromną liczbę odpowiedzi i niesamowite zgłoszenia, postanowiliśmy nieco zwiększyć pulę nagród i przyznać cztery nagrody za trzecie miejsce po 500 USD każda. Zwycięzcy są poniżej, ale koniecznie zapoznaj się ze wszystkimi zgłoszeniami <a %(annas_archive)s>tutaj</a> lub pobierz nasz <a %(a_2025_01_isbn_visualization_files)s>łączony torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Pierwsze miejsce 6 000 USD: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "To <a %(phiresky_github)s>zgłoszenie</a> (<a %(annas_archive_note_2951)s>komentarz na Gitlab</a>) to po prostu wszystko, czego chcieliśmy, i więcej! Szczególnie podobały nam się niesamowicie elastyczne opcje wizualizacji (nawet z obsługą niestandardowych shaderów), ale z obszerną listą ustawień wstępnych. Podobało nam się również, jak szybkie i płynne jest wszystko, prosta implementacja (która nawet nie ma backendu), sprytna minimapa i obszerne wyjaśnienie w ich <a %(phiresky_github)s>poście na blogu</a>. Niesamowita praca i zasłużone zwycięstwo!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Drugie miejsce 3 000 USD: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Kolejne niesamowite <a %(annas_archive_note_2913)s>zgłoszenie</a>. Nie tak elastyczne jak pierwsze miejsce, ale faktycznie woleliśmy jego wizualizację na poziomie makro od pierwszego miejsca (krzywa wypełniająca przestrzeń, granice, etykietowanie, podświetlanie, przesuwanie i powiększanie). <a %(annas_archive_note_2971)s>Komentarz</a> Joe Davisa rezonował z nami:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Podczas gdy idealne kwadraty i prostokąty są matematycznie przyjemne, nie zapewniają lepszej lokalności w kontekście mapowania. Uważam, że asymetria inherentna w tych krzywych Hilberta lub klasycznych Mortona nie jest wadą, lecz cechą. Podobnie jak słynny kształt buta Włoch sprawia, że jest on natychmiast rozpoznawalny na mapie, unikalne „dziwactwa” tych krzywych mogą służyć jako poznawcze punkty orientacyjne. Ta wyjątkowość może poprawić pamięć przestrzenną i pomóc użytkownikom w orientacji, potencjalnie ułatwiając lokalizację konkretnych regionów lub dostrzeganie wzorców.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "I wciąż wiele opcji wizualizacji i renderowania, a także niesamowicie płynny i intuicyjny interfejs użytkownika. Solidne drugie miejsce!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Trzecie miejsce 500 USD #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "W tym <a %(annas_archive_note_2940)s>zgłoszeniu</a> naprawdę podobały nam się różne rodzaje widoków, w szczególności widoki porównawcze i wydawców."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Trzecie miejsce 500 USD #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Chociaż nie jest to najbardziej dopracowany interfejs użytkownika, to <a %(annas_archive_note_2917)s>zgłoszenie</a> spełnia wiele wymagań. Szczególnie podobała nam się jego funkcja porównawcza."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Trzecie miejsce 500 USD #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Podobnie jak pierwsze miejsce, ta <a %(annas_archive_note_2975)s>praca</a> zaimponowała nam swoją elastycznością. Ostatecznie to właśnie czyni świetne narzędzie do wizualizacji: maksymalna elastyczność dla zaawansowanych użytkowników, przy jednoczesnym zachowaniu prostoty dla przeciętnych użytkowników."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Trzecie miejsce $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Ostatnia <a %(annas_archive_note_2947)s>praca</a>, która otrzymała nagrodę, jest dość podstawowa, ale ma kilka unikalnych cech, które naprawdę nam się podobały. Podobało nam się, jak pokazują, ile datasets obejmuje konkretny ISBN jako miarę popularności/wiarygodności. Bardzo podobała nam się również prostota, ale skuteczność użycia suwaka przezroczystości do porównań."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Zauważalne pomysły"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Kilka innych pomysłów i wdrożeń, które szczególnie nam się podobały:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Drapacze chmur dla rzadkości"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statystyki na żywo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Adnotacje oraz statystyki na żywo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unikalny widok mapy i filtry"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Fajny domyślny schemat kolorów i mapa cieplna."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Łatwe przełączanie datasets do szybkich porównań."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Ładne etykiety."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skala z liczbą książek."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Wiele suwaków do porównywania datasets, jakbyś był DJ-em."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Moglibyśmy kontynuować jeszcze długo, ale zatrzymajmy się tutaj. Koniecznie zobacz wszystkie prace <a %(annas_archive)s>tutaj</a>, lub pobierz nasz <a %(a_2025_01_isbn_visualization_files)s>połączony torrent</a>. Tak wiele prac, a każda z nich wnosi unikalną perspektywę, czy to w interfejsie użytkownika, czy w implementacji."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Przynajmniej włączymy zgłoszenie z pierwszego miejsca do naszej głównej strony, a być może także inne. Zaczęliśmy również myśleć o tym, jak zorganizować proces identyfikacji, potwierdzania, a następnie archiwizacji najrzadszych książek. Więcej informacji wkrótce."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Dziękujemy wszystkim, którzy wzięli udział. To niesamowite, że tak wiele osób się tym przejmuje."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nasze serca są pełne wdzięczności."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Wizualizacja wszystkich ISBN — nagroda 10 000 USD do 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Ten obrazek przedstawia największą w pełni otwartą „listę książek”, jaka kiedykolwiek została zebrana w historii ludzkości."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Ten obrazek ma rozmiar 1000×800 pikseli. Każdy piksel reprezentuje 2 500 ISBN-ów. Jeśli mamy plik dla danego ISBN-u, piksel staje się bardziej zielony. Jeśli wiemy, że ISBN został wydany, ale nie mamy pasującego pliku, piksel staje się bardziej czerwony."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "W mniej niż 300 kb, ten obrazek zwięźle przedstawia największą w pełni otwartą „listę książek”, jaka kiedykolwiek została zebrana w historii ludzkości (kilkaset GB skompresowanych w całości)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Pokazuje również: jest jeszcze wiele pracy do wykonania w zakresie tworzenia kopii zapasowych książek (mamy tylko 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Tło"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Jak Archiwum Anny może osiągnąć swoją misję tworzenia kopii zapasowych całej wiedzy ludzkości, nie wiedząc, które książki wciąż istnieją? Potrzebujemy listy zadań do wykonania. Jednym ze sposobów na jej stworzenie jest wykorzystanie numerów ISBN, które od lat 70. XX wieku są przypisywane każdej opublikowanej książce (w większości krajów)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nie ma centralnej władzy, która zna wszystkie przypisania ISBN. Zamiast tego jest to system rozproszony, w którym kraje otrzymują zakresy numerów, które następnie przypisują mniejsze zakresy głównym wydawcom, którzy mogą dalej dzielić zakresy na mniejszych wydawców. Ostatecznie indywidualne numery są przypisywane książkom."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Zaczęliśmy mapować ISBN-y <a %(blog)s>dwa lata temu</a> z naszym zbiorem danych z ISBNdb. Od tego czasu zebraliśmy wiele więcej źródeł metadata, takich jak <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby i inne. Pełna lista znajduje się na stronach „Datasets” i „Torrents” w Archiwum Anny. Obecnie mamy zdecydowanie największą w pełni otwartą, łatwo dostępną kolekcję metadata książek (a tym samym ISBN-ów) na świecie."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Napisaliśmy <a %(blog)s>obszernie</a> o tym, dlaczego zależy nam na zachowaniu, i dlaczego obecnie znajdujemy się w krytycznym oknie czasowym. Musimy teraz zidentyfikować rzadkie, niedoceniane i unikalnie zagrożone książki i je zachować. Posiadanie dobrego metadata na temat wszystkich książek na świecie pomaga w tym."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Wizualizacja"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Oprócz obrazu ogólnego, możemy również przyjrzeć się poszczególnym datasets, które zdobyliśmy. Użyj rozwijanego menu i przycisków, aby przełączać się między nimi."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "W tych obrazach można dostrzec wiele interesujących wzorców. Dlaczego występuje pewna regularność linii i bloków, która wydaje się występować na różnych skalach? Czym są puste obszary? Dlaczego niektóre datasets są tak skupione? Zostawimy te pytania jako ćwiczenie dla czytelnika."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Nagroda 10 000 $"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Jest tu wiele do odkrycia, dlatego ogłaszamy nagrodę za ulepszenie wizualizacji powyżej. W przeciwieństwie do większości naszych nagród, ta jest ograniczona czasowo. Musisz <a %(annas_archive)s>przesłać</a> swój kod open source do 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Najlepsze zgłoszenie otrzyma 6 000 $, drugie miejsce 3 000 $, a trzecie miejsce 1 000 $. Wszystkie nagrody zostaną przyznane w Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Poniżej znajdują się minimalne kryteria. Jeśli żadne zgłoszenie nie spełni kryteriów, możemy nadal przyznać nagrody, ale będzie to według naszego uznania."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Sforkuj to repozytorium i edytuj ten post na blogu w HTML (nie są dozwolone inne backendy poza naszym backendem Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Spraw, aby powyższy obrazek był płynnie powiększany, tak aby można było powiększać aż do pojedynczych ISBN. Kliknięcie ISBN powinno przenosić na stronę z metadata lub wyszukiwanie w Archiwum Anny."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Musisz nadal mieć możliwość przełączania się między różnymi Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Zakresy krajów i wydawców powinny być podświetlane po najechaniu myszką. Możesz użyć np. <a %(github_xlcnd_isbnlib)s>data4info.py w isbnlib</a> dla informacji o krajach oraz naszego skryptu „isbngrp” dla wydawców (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Musi działać dobrze na komputerach stacjonarnych i urządzeniach mobilnych."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Dla dodatkowych punktów (to tylko pomysły — pozwól swojej kreatywności się rozwijać):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Duże znaczenie będzie miała użyteczność i estetyka."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Pokaż rzeczywiste metadata dla pojedynczych ISBN podczas powiększania, takie jak tytuł i autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Lepsza krzywa wypełniająca przestrzeń. Np. zygzak, idący od 0 do 4 w pierwszym rzędzie, a następnie z powrotem (w odwrotnym kierunku) od 5 do 9 w drugim rzędzie — stosowane rekursywnie."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Różne lub konfigurowalne schematy kolorów."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Specjalne widoki do porównywania Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Sposoby debugowania problemów, takich jak inne metadata, które nie zgadzają się dobrze (np. znacznie różne tytuły)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotowanie obrazów komentarzami na temat ISBN lub zakresów."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Jakiekolwiek heurystyki do identyfikacji rzadkich lub zagrożonych książek."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Jakiekolwiek kreatywne pomysły, które możesz wymyślić!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "MOŻESZ całkowicie odejść od minimalnych kryteriów i stworzyć zupełnie inną wizualizację. Jeśli będzie naprawdę spektakularna, to kwalifikuje się do nagrody, ale według naszego uznania."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Zgłaszaj swoje prace, dodając komentarz do <a %(annas_archive)s>tego zgłoszenia</a> z linkiem do swojego rozwidlenia repozytorium, żądania scalenia lub różnicy."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Kod do generowania tych obrazów, jak również inne przykłady, można znaleźć w <a %(annas_archive)s>tym katalogu</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Opracowaliśmy kompaktowy format danych, w którym wszystkie wymagane informacje ISBN zajmują około 75 MB (skompresowane). Opis formatu danych i kod do jego generowania można znaleźć <a %(annas_archive_l1244_1319)s>tutaj</a>. Nie musisz z tego korzystać, aby zdobyć nagrodę, ale jest to prawdopodobnie najwygodniejszy format na początek. Możesz przekształcać nasze metadata w dowolny sposób (choć cały twój kod musi być open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nie możemy się doczekać, co wymyślisz. Powodzenia!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Kontenery Archiwum Anny (AAC): standaryzacja wydań z największej na świecie biblioteki cieni"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Archiwum Anny stało się największą na świecie biblioteką cieni, co wymaga od nas standaryzacji naszych wydań."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Archiwum Anny</a> stało się zdecydowanie największą na świecie biblioteką cieni i jedyną biblioteką cieni na taką skalę, która jest w pełni open-source i open-data. Poniżej znajduje się tabela z naszej strony Datasets (nieco zmodyfikowana):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Osiągnęliśmy to na trzy sposoby:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Odwzorowywanie istniejących bibliotek cieni z otwartymi danymi (takich jak Sci-Hub i Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Pomoc bibliotekom cieni, które chcą być bardziej otwarte, ale nie miały na to czasu ani zasobów (takich jak kolekcja komiksów Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Zbieranie danych z bibliotek, które nie chcą dzielić się danymi masowo (takich jak Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Dla (2) i (3) zarządzamy teraz znaczną kolekcją torrentów (setki TB). Do tej pory traktowaliśmy te kolekcje jako jednorazowe, co oznaczało dedykowaną infrastrukturę i organizację danych dla każdej kolekcji. To dodaje znaczne koszty do każdego wydania i utrudnia wprowadzanie bardziej stopniowych wydań."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Dlatego zdecydowaliśmy się na standaryzację naszych wydań. To techniczny wpis na blogu, w którym wprowadzamy nasz standard: <strong>Kontenery Archiwum Anny</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Cele projektowe"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Naszym głównym przypadkiem użycia jest dystrybucja plików i powiązanych metadata z różnych istniejących kolekcji. Nasze najważniejsze rozważania to:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogeniczne pliki i metadata, w formacie jak najbliższym oryginałowi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogeniczne identyfikatory w bibliotekach źródłowych, a nawet brak identyfikatorów."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Oddzielne wydania metadata w porównaniu do danych plikowych, lub wydania tylko metadata (np. nasze wydanie ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Dystrybucja przez torrenty, choć z możliwością innych metod dystrybucji (np. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Niezmienne rekordy, ponieważ powinniśmy zakładać, że nasze torrenty będą istnieć wiecznie."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Wydania inkrementalne / wydania dołączalne."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Czytelne i zapisywalne przez maszyny, wygodnie i szybko, zwłaszcza dla naszego stosu (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Dość łatwa inspekcja przez ludzi, choć jest to drugorzędne w stosunku do czytelności maszynowej."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Łatwe do seedowania naszych kolekcji za pomocą standardowego wynajętego seedboxa."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dane binarne mogą być serwowane bezpośrednio przez serwery internetowe, takie jak Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Niektóre cele nieistotne:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nie zależy nam na tym, aby pliki były łatwe do nawigacji ręcznej na dysku lub przeszukiwalne bez wstępnego przetwarzania."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nie zależy nam na bezpośredniej kompatybilności z istniejącym oprogramowaniem bibliotecznym."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Chociaż powinno być łatwo dla każdego seedować naszą kolekcję za pomocą torrentów, nie oczekujemy, że pliki będą użyteczne bez znacznej wiedzy technicznej i zaangażowania."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Ponieważ Archiwum Anny jest open source, chcemy bezpośrednio używać naszego formatu. Kiedy odświeżamy nasz indeks wyszukiwania, uzyskujemy dostęp tylko do publicznie dostępnych ścieżek, aby każdy, kto forkuje naszą bibliotekę, mógł szybko zacząć działać."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Ostatecznie zdecydowaliśmy się na stosunkowo prosty standard. Jest dość luźny, nienormatywny i wciąż w trakcie opracowywania."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Kontener Archiwum Anny) to pojedynczy element składający się z <strong>metadata</strong> i opcjonalnie <strong>danych binarnych</strong>, z których oba są niezmienne. Posiada globalnie unikalny identyfikator, zwany <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Kolekcja.</strong> Każde AAC należy do kolekcji, która z definicji jest listą AAC, które są semantycznie spójne. Oznacza to, że jeśli dokonasz znaczącej zmiany w formacie metadata, musisz utworzyć nową kolekcję."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>„rekordy” i „pliki” kolekcje.</strong> Zgodnie z konwencją, często wygodnie jest wydawać „rekordy” i „pliki” jako różne kolekcje, aby mogły być wydawane w różnych harmonogramach, np. w oparciu o tempo skanowania. „Rekord” to kolekcja zawierająca tylko metadata, zawierająca informacje takie jak tytuły książek, autorzy, ISBN-y itp., podczas gdy „pliki” to kolekcje zawierające rzeczywiste pliki (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Format AACID jest następujący: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Na przykład, rzeczywisty AACID, który wydaliśmy, to <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: nazwa kolekcji, która może zawierać litery ASCII, cyfry i podkreślenia (ale bez podwójnych podkreśleń)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: krótka wersja ISO 8601, zawsze w UTC, np. <code>20220723T194746Z</code>. Ta liczba musi monotonicznie rosnąć przy każdym wydaniu, choć jej dokładna semantyka może się różnić w zależności od kolekcji. Sugerujemy użycie czasu skanowania lub generowania ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: identyfikator specyficzny dla kolekcji, jeśli dotyczy, np. ID Z-Library. Może być pominięty lub skrócony. Musi być pominięty lub skrócony, jeśli AACID przekroczyłby 150 znaków."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, ale skompresowany do ASCII, np. przy użyciu base57. Obecnie używamy biblioteki Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Zakres AACID.</strong> Ponieważ AACID zawierają monotonicznie rosnące znaczniki czasu, możemy użyć tego do oznaczania zakresów w obrębie konkretnej kolekcji. Używamy tego formatu: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, gdzie znaczniki czasu są włączone. Zakresy są ciągłe i mogą się nakładać, ale w przypadku nakładania muszą zawierać identyczne rekordy jak te wcześniej wydane w tej kolekcji (ponieważ AAC są niezmienne). Brakujące rekordy nie są dozwolone."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Plik metadata.</strong> Plik metadata zawiera metadata zakresu AAC, dla jednej konkretnej kolekcji. Mają one następujące właściwości:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Nazwa pliku musi być zakresem AACID, poprzedzonym <code style=\"color: red\">annas_archive_meta__</code> i zakończonym <code>.jsonl.zstd</code>. Na przykład, jedna z naszych wersji nazywa się<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Jak wskazuje rozszerzenie pliku, typ pliku to <a %(jsonlines)s>JSON Lines</a> skompresowany za pomocą <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Każdy obiekt JSON musi zawierać następujące pola na najwyższym poziomie: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcjonalne). Żadne inne pola nie są dozwolone."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> to dowolne metadata, zgodnie z semantyką kolekcji. Musi być semantycznie spójne w ramach kolekcji."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> jest opcjonalne i jest nazwą folderu danych binarnych, który zawiera odpowiadające dane binarne. Nazwa pliku odpowiadających danych binarnych w tym folderze to AACID rekordu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Prefiks <code style=\"color: red\">annas_archive_meta__</code> może być dostosowany do nazwy Twojej instytucji, np. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Folder danych binarnych.</strong> Folder z danymi binarnymi zakresu AAC, dla jednej konkretnej kolekcji. Mają one następujące właściwości:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Nazwa katalogu musi być zakresem AACID, poprzedzonym <code style=\"color: green\">annas_archive_data__</code>, bez żadnego sufiksu. Na przykład, jedna z naszych rzeczywistych wersji ma katalog o nazwie<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Katalog musi zawierać pliki danych dla wszystkich AAC w określonym zakresie. Każdy plik danych musi mieć AACID jako nazwę pliku (bez rozszerzeń)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Zaleca się, aby te foldery były w miarę możliwości zarządzalne pod względem rozmiaru, np. nie większe niż 100GB-1TB każdy, chociaż ta rekomendacja może się zmieniać z czasem."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrenty.</strong> Pliki metadata i foldery danych binarnych mogą być pakowane w torrenty, z jednym torrentem na plik metadata lub jednym torrentem na folder danych binarnych. Torrenty muszą mieć oryginalną nazwę pliku/katalogu plus <code>.torrent</code> jako ich nazwę pliku."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Przykład"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Przyjrzyjmy się naszemu ostatniemu wydaniu Z-Library jako przykład. Składa się ono z dwóch kolekcji: „<span style=\"background: #fffaa3\">zlib3_records</span>” i „<span style=\"background: #ffd6fe\">zlib3_files</span>”. Pozwala to na osobne zeskrobanie i wydanie rekordów metadata z rzeczywistych plików książek. W związku z tym wydaliśmy dwa torrenty z plikami metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Wydaliśmy również kilka torrentów z folderami danych binarnych, ale tylko dla kolekcji „<span style=\"background: #ffd6fe\">zlib3_files</span>”, łącznie 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Uruchamiając <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> możemy zobaczyć, co jest w środku:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "W tym przypadku jest to metadata książki zgłoszona przez Z-Library. Na najwyższym poziomie mamy tylko „aacid” i „metadata”, ale brak „data_folder”, ponieważ nie ma odpowiadających danych binarnych. AACID zawiera „22430000” jako główny identyfikator, który widzimy, że pochodzi z „zlibrary_id”. Możemy oczekiwać, że inne AAC w tej kolekcji będą miały tę samą strukturę."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Teraz uruchommy <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "To jest znacznie mniejsze metadata AAC, chociaż większość tego AAC znajduje się gdzie indziej w pliku binarnym! W końcu mamy tym razem „data_folder”, więc możemy oczekiwać, że odpowiadające dane binarne będą znajdować się w <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata” zawiera „zlibrary_id”, więc możemy łatwo powiązać je z odpowiadającym AAC w kolekcji „zlib_records”. Moglibyśmy powiązać na wiele różnych sposobów, np. przez AACID — standard tego nie określa."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Zauważ, że nie jest również konieczne, aby pole „metadata” samo w sobie było JSON-em. Może to być ciąg zawierający XML lub dowolny inny format danych. Można nawet przechowywać informacje o metadata w powiązanym blobie binarnym, np. jeśli jest to dużo danych."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Podsumowanie"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Dzięki temu standardowi możemy wydawać wersje bardziej stopniowo i łatwiej dodawać nowe źródła danych. Mamy już kilka ekscytujących wydań w przygotowaniu!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Mamy również nadzieję, że stanie się łatwiejsze dla innych bibliotek cieni do mirrorowania naszych kolekcji. W końcu naszym celem jest zachowanie ludzkiej wiedzy i kultury na zawsze, więc im więcej redundancji, tym lepiej."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Aktualizacja Anny: w pełni otwarte źródło archiwum, ElasticSearch, ponad 300GB okładek książek"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Pracujemy bez przerwy, aby zapewnić dobrą alternatywę z Archiwum Anny. Oto niektóre z rzeczy, które ostatnio osiągnęliśmy."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Wraz z zamknięciem Z-Library i aresztowaniem jej (rzekomych) założycieli, pracujemy bez przerwy, aby zapewnić dobrą alternatywę w postaci Archiwum Anny (nie podamy tutaj linku, ale można je znaleźć w Google). Oto niektóre z rzeczy, które ostatnio osiągnęliśmy."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Archiwum Anny jest w pełni open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Wierzymy, że informacje powinny być darmowe, a nasz własny kod nie jest wyjątkiem. Udostępniliśmy cały nasz kod na naszej prywatnie hostowanej instancji Gitlaba: <a %(annas_archive)s>Oprogramowanie Anny</a>. Używamy również systemu śledzenia problemów do organizacji naszej pracy. Jeśli chcesz zaangażować się w nasz rozwój, to świetne miejsce na początek."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Aby dać Ci przedsmak rzeczy, nad którymi pracujemy, przyjrzyj się naszej ostatniej pracy nad poprawą wydajności po stronie klienta. Ponieważ nie wdrożyliśmy jeszcze paginacji, często zwracaliśmy bardzo długie strony wyników wyszukiwania, z 100-200 wynikami. Nie chcieliśmy zbyt wcześnie odcinać wyników wyszukiwania, ale to oznaczało, że spowalniało to niektóre urządzenia. W tym celu zastosowaliśmy mały trik: większość wyników wyszukiwania opakowaliśmy w komentarze HTML (<code><!-- --></code>), a następnie napisaliśmy mały skrypt Javascript, który wykrywał, kiedy wynik powinien stać się widoczny, w tym momencie usuwaliśmy komentarz:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "\"Wirtualizacja\" DOM zaimplementowana w 23 liniach, bez potrzeby używania zaawansowanych bibliotek! To rodzaj szybkiego, pragmatycznego kodu, który powstaje, gdy masz ograniczony czas i prawdziwe problemy do rozwiązania. Zgłoszono, że nasze wyszukiwanie teraz działa dobrze na wolnych urządzeniach!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Innym dużym wysiłkiem było zautomatyzowanie budowy bazy danych. Kiedy startowaliśmy, po prostu chaotycznie łączyliśmy różne źródła. Teraz chcemy je aktualizować, więc napisaliśmy zestaw skryptów do pobierania nowych metadanych z dwóch forków Library Genesis i ich integracji. Celem jest nie tylko uczynienie tego przydatnym dla naszego archiwum, ale także ułatwienie każdemu, kto chce eksperymentować z metadanymi bibliotek cieni. Celem byłby notebook Jupyter, który zawierałby wszelkiego rodzaju interesujące metadane, abyśmy mogli prowadzić więcej badań, takich jak ustalanie, jaki <a %(blog)s>procent ISBN jest zachowywany na zawsze</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Na koniec odnowiliśmy nasz system darowizn. Teraz możesz użyć karty kredytowej, aby bezpośrednio wpłacić pieniądze na nasze portfele kryptowalutowe, bez potrzeby posiadania wiedzy o kryptowalutach. Będziemy monitorować, jak dobrze to działa w praktyce, ale to duża sprawa."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Przejście na ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Jednym z naszych <a %(annas_archive)s>biletów</a> był zbiór problemów z naszym systemem wyszukiwania. Używaliśmy pełnotekstowego wyszukiwania MySQL, ponieważ wszystkie nasze dane były w MySQL. Ale miało to swoje ograniczenia:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Niektóre zapytania trwały bardzo długo, do tego stopnia, że zajmowały wszystkie otwarte połączenia."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Domyślnie MySQL ma minimalną długość słowa, lub indeks może stać się naprawdę duży. Ludzie zgłaszali, że nie mogą wyszukiwać „Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Wyszukiwanie było tylko częściowo szybkie, gdy było w pełni załadowane do pamięci, co wymagało od nas zakupu droższego urządzenia do jego uruchomienia, plus kilku poleceń do wstępnego załadowania indeksu przy starcie."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nie bylibyśmy w stanie łatwo go rozszerzyć, aby zbudować nowe funkcje, takie jak lepsza <a %(wikipedia_cjk_characters)s>tokenizacja dla języków bez spacji</a>, filtrowanie/faceting, sortowanie, sugestie „czy miałeś na myśli”, autouzupełnianie i inne."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Po rozmowach z wieloma ekspertami zdecydowaliśmy się na ElasticSearch. Nie było to idealne (ich domyślne sugestie „czy miałeś na myśli” i funkcje autouzupełniania są kiepskie), ale ogólnie było znacznie lepsze niż MySQL do wyszukiwania. Nadal nie jesteśmy <a %(youtube)s>zbyt chętni</a> do używania go do jakichkolwiek danych krytycznych dla misji (choć poczynili wiele <a %(elastic_co)s>postępów</a>), ale ogólnie jesteśmy całkiem zadowoleni z tej zmiany."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Na razie wdrożyliśmy znacznie szybsze wyszukiwanie, lepsze wsparcie językowe, lepsze sortowanie według trafności, różne opcje sortowania i filtrowanie według języka/typu książki/typu pliku. Jeśli jesteś ciekawy, jak to działa, <a %(annas_archive_l140)s>zajrzyj</a> <a %(annas_archive_l1115)s>do</a> <a %(annas_archive_l1635)s>tego</a>. Jest dość dostępne, choć przydałoby się więcej komentarzy…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Ponad 300 GB okładek książek wydanych"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Na koniec z przyjemnością ogłaszamy małe wydanie. Współpracując z osobami obsługującymi fork Libgen.rs, udostępniamy wszystkie ich okładki książek za pośrednictwem torrentów i IPFS. To rozłoży obciążenie związane z przeglądaniem okładek na więcej maszyn i lepiej je zachowa. W wielu (ale nie we wszystkich) przypadkach okładki książek są zawarte w samych plikach, więc jest to rodzaj „danych pochodnych”. Jednak posiadanie ich w IPFS jest nadal bardzo przydatne do codziennej pracy zarówno Archiwum Anny, jak i różnych forków Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Jak zwykle, to wydanie można znaleźć w Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>). Nie podamy tutaj linku, ale można go łatwo znaleźć."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Mamy nadzieję, że możemy nieco zwolnić tempo, teraz gdy mamy przyzwoitą alternatywę dla Z-Library. Ten nakład pracy nie jest szczególnie zrównoważony. Jeśli jesteś zainteresowany pomocą w programowaniu, obsłudze serwerów lub pracy nad zachowaniem danych, koniecznie skontaktuj się z nami. Wciąż jest wiele <a %(annas_archive)s>do zrobienia</a>. Dziękujemy za zainteresowanie i wsparcie."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Archiwum Anny zarchiwizowało największą na świecie bibliotekę cieni komiksów (95TB) — możesz pomóc w jej seedowaniu"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Największa na świecie biblioteka cieni komiksów miała jeden punkt awarii... aż do dziś."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Dyskutuj na Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Największa biblioteka cieni komiksów to prawdopodobnie ta z konkretnego forka Library Genesis: Libgen.li. Jeden administrator prowadzący tę stronę zdołał zebrać niesamowitą kolekcję komiksów liczącą ponad 2 miliony plików, o łącznej wielkości ponad 95TB. Jednak w przeciwieństwie do innych kolekcji Library Genesis, ta nie była dostępna w całości przez torrenty. Można było uzyskać dostęp do tych komiksów tylko indywidualnie przez jego wolny osobisty serwer — jeden punkt awarii. Aż do dziś!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "W tym poście opowiemy więcej o tej kolekcji i o naszej zbiórce funduszy na wsparcie dalszej pracy."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon próbuje zatracić się w przyziemnym świecie biblioteki…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Forki Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Na początek trochę tła. Możesz znać Library Genesis z ich epickiej kolekcji książek. Mniej osób wie, że wolontariusze Library Genesis stworzyli inne projekty, takie jak pokaźna kolekcja magazynów i dokumentów standardowych, pełna kopia zapasowa Sci-Hub (we współpracy z założycielką Sci-Hub, Alexandrą Elbakyan) oraz ogromna kolekcja komiksów."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "W pewnym momencie różni operatorzy lustrzanych stron Library Genesis poszli własnymi drogami, co doprowadziło do obecnej sytuacji, w której istnieje wiele różnych „forków”, wszystkie nadal noszące nazwę Library Genesis. Fork Libgen.li ma unikalnie tę kolekcję komiksów, a także pokaźną kolekcję magazynów (nad którą również pracujemy)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Współpraca"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Ze względu na swój rozmiar, ta kolekcja od dawna była na naszej liście życzeń, więc po naszym sukcesie z tworzeniem kopii zapasowej Z-Library, skupiliśmy się na tej kolekcji. Na początku zgrywaliśmy ją bezpośrednio, co było sporym wyzwaniem, ponieważ ich serwer nie był w najlepszym stanie. W ten sposób uzyskaliśmy około 15TB, ale postęp był powolny."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Na szczęście udało nam się skontaktować z operatorem biblioteki, który zgodził się przesłać nam wszystkie dane bezpośrednio, co było znacznie szybsze. Mimo to, transfer i przetwarzanie wszystkich danych zajęło ponad pół roku, a prawie straciliśmy wszystko z powodu uszkodzenia dysku, co oznaczałoby konieczność rozpoczęcia od nowa."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "To doświadczenie sprawiło, że wierzymy, iż ważne jest, aby jak najszybciej udostępnić te dane, aby mogły być szeroko kopiowane. Jesteśmy tylko o jedno lub dwa niefortunne zdarzenia od utraty tej kolekcji na zawsze!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Kolekcja"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Szybkie działanie oznacza, że kolekcja jest nieco nieuporządkowana… Spójrzmy na to. Wyobraźmy sobie system plików (który w rzeczywistości dzielimy na torrenty):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Pierwszy katalog, <code>/repository</code>, to bardziej uporządkowana część tego. Ten katalog zawiera tzw. „tysiąc katalogów”: katalogi, z których każdy zawiera tysiąc plików, numerowanych kolejno w bazie danych. Katalog <code>0</code> zawiera pliki z comic_id 0–999 i tak dalej."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "To ten sam schemat, który Library Genesis używa do swoich kolekcji fikcji i literatury faktu. Pomysł polega na tym, że każdy „tysiąc katalogów” automatycznie zamienia się w torrent, gdy tylko zostanie zapełniony."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Jednak operator Libgen.li nigdy nie stworzył torrentów dla tej kolekcji, więc tysiące katalogów prawdopodobnie stały się niewygodne i ustąpiły miejsca „nieposortowanym katalogom”. Są to <code>/comics0</code> do <code>/comics4</code>. Wszystkie zawierają unikalne struktury katalogów, które prawdopodobnie miały sens przy zbieraniu plików, ale teraz nie mają dla nas większego sensu. Na szczęście metadata nadal bezpośrednio odnosi się do wszystkich tych plików, więc ich organizacja na dysku nie ma tak naprawdę znaczenia!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata są dostępne w formie bazy danych MySQL. Można je pobrać bezpośrednio ze strony Libgen.li, ale udostępnimy je również w torrentach, wraz z naszą własną tabelą zawierającą wszystkie hashe MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analiza"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Kiedy otrzymujesz 95TB wrzuconych do swojego klastra pamięci, próbujesz zrozumieć, co tam właściwie jest… Przeprowadziliśmy analizę, aby sprawdzić, czy możemy nieco zmniejszyć rozmiar, na przykład usuwając duplikaty. Oto niektóre z naszych ustaleń:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplikaty semantyczne (różne skany tej samej książki) teoretycznie można odfiltrować, ale jest to trudne. Podczas ręcznego przeglądania komiksów znaleźliśmy zbyt wiele fałszywych trafień."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Istnieją pewne duplikaty wyłącznie według MD5, co jest stosunkowo marnotrawne, ale ich odfiltrowanie dałoby nam tylko około 1% in oszczędności. W tej skali to wciąż około 1TB, ale także, w tej skali 1TB nie ma większego znaczenia. Wolelibyśmy nie ryzykować przypadkowego zniszczenia danych w tym procesie."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Znaleźliśmy sporo danych nieksiążkowych, takich jak filmy oparte na komiksach. To również wydaje się marnotrawstwem, ponieważ są one już szeroko dostępne innymi środkami. Jednak zdaliśmy sobie sprawę, że nie możemy po prostu odfiltrować plików filmowych, ponieważ istnieją również <em>interaktywne komiksy</em>, które zostały wydane na komputerze, a ktoś je nagrał i zapisał jako filmy."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Ostatecznie, cokolwiek moglibyśmy usunąć z kolekcji, zaoszczędziłoby tylko kilka procent. Wtedy przypomnieliśmy sobie, że jesteśmy zbieraczami danych, a osoby, które będą to kopiować, również są zbieraczami danych, więc „CO TO ZNACZY, USUNĄĆ?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Dlatego przedstawiamy Państwu pełną, niezmodyfikowaną kolekcję. To dużo danych, ale mamy nadzieję, że wystarczająco dużo osób będzie chciało ją udostępniać."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Zbiórka funduszy"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Udostępniamy te dane w dużych kawałkach. Pierwszy torrent to <code>/comics0</code>, który umieściliśmy w jednym ogromnym pliku .tar o rozmiarze 12TB. To lepsze dla twojego dysku twardego i oprogramowania torrentowego niż mnóstwo mniejszych plików."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "W ramach tego wydania organizujemy zbiórkę funduszy. Chcemy zebrać 20 000 dolarów na pokrycie kosztów operacyjnych i kontraktowych związanych z tą kolekcją, a także umożliwić realizację bieżących i przyszłych projektów. Mamy w planach kilka <em>ogromnych</em> projektów."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Kogo wspieram moją darowizną?</em> W skrócie: tworzymy kopie zapasowe całej wiedzy i kultury ludzkości, czyniąc je łatwo dostępnymi. Cały nasz kod i dane są open source, jesteśmy projektem prowadzonym całkowicie przez wolontariuszy i do tej pory uratowaliśmy 125TB książek (oprócz istniejących torrentów Libgen i Scihub). Ostatecznie budujemy koło zamachowe, które umożliwia i zachęca ludzi do znajdowania, skanowania i tworzenia kopii zapasowych wszystkich książek na świecie. Napiszemy o naszym głównym planie w przyszłym poście. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Jeśli przekażesz darowiznę na 12-miesięczne członkostwo „Amazing Archivist” (780 USD), możesz <strong>„adoptować torrent”</strong>, co oznacza, że umieścimy Twoją nazwę użytkownika lub wiadomość w nazwie pliku jednego z torrentów!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Możesz przekazać darowiznę, przechodząc na <a %(wikipedia_annas_archive)s>Archiwum Anny</a> i klikając przycisk „Przekaż darowiznę”. Szukamy również więcej wolontariuszy: inżynierów oprogramowania, badaczy bezpieczeństwa, ekspertów od anonimowych transakcji i tłumaczy. Możesz nas również wesprzeć, zapewniając usługi hostingowe. I oczywiście, prosimy o seedowanie naszych torrentów!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dziękujemy wszystkim, którzy już tak hojnie nas wsparli! Naprawdę robicie różnicę."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Oto torrenty wydane do tej pory (wciąż przetwarzamy resztę):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Wszystkie torrenty można znaleźć na <a %(wikipedia_annas_archive)s>Archiwum Anny</a> w sekcji „Datasets” (nie linkujemy tam bezpośrednio, aby linki do tego bloga nie zostały usunięte z Reddita, Twittera itp.). Stamtąd, podążaj za linkiem do strony Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Co dalej?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Wiele torrentów jest świetnych do długoterminowego przechowywania, ale niekoniecznie do codziennego dostępu. Będziemy współpracować z partnerami hostingowymi, aby umieścić te dane w sieci (ponieważ Archiwum Anny nie hostuje niczego bezpośrednio). Oczywiście będziesz mógł znaleźć te linki do pobrania w Archiwum Anny."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Zapraszamy również wszystkich do pracy z tymi danymi! Pomóż nam lepiej je analizować, deduplikować, umieszczać na IPFS, remiksować, trenować swoje modele AI i tak dalej. To wszystko jest Twoje, i nie możemy się doczekać, co z tym zrobisz."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Na koniec, jak już wcześniej wspomniano, wciąż mamy przed sobą kilka ogromnych wydań (jeśli <em>ktoś</em> mógłby <em>przypadkowo</em> przesłać nam zrzut bazy danych <em>pewnej</em> bazy ACS4, wiesz, gdzie nas znaleźć...), a także budowanie koła zamachowego do tworzenia kopii zapasowych wszystkich książek na świecie."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Więc bądźcie czujni, dopiero zaczynamy."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nowe książki dodane do Lustrzanej Biblioteki Piratów (+24TB, 3,8 miliona książek)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "W oryginalnym wydaniu Lustrzanej Biblioteki Piratów (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>), stworzyliśmy mirror Z-Library, dużej nielegalnej kolekcji książek. Przypominamy, co napisaliśmy w tym oryginalnym wpisie na blogu:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library to popularna (i nielegalna) biblioteka. Przejęli kolekcję Library Genesis i uczynili ją łatwo przeszukiwalną. Ponadto, stali się bardzo skuteczni w pozyskiwaniu nowych książek, zachęcając użytkowników do ich dodawania różnymi korzyściami. Obecnie nie przekazują tych nowych książek z powrotem do Library Genesis. I w przeciwieństwie do Library Genesis, nie umożliwiają łatwego tworzenia mirrorów swojej kolekcji, co uniemożliwia szerokie przechowywanie. Jest to ważne dla ich modelu biznesowego, ponieważ pobierają opłaty za dostęp do swojej kolekcji w dużych ilościach (więcej niż 10 książek dziennie)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nie oceniamy moralnie pobierania opłat za dostęp do nielegalnej kolekcji książek w dużych ilościach. Nie ma wątpliwości, że Z-Library odniosła sukces w rozszerzaniu dostępu do wiedzy i pozyskiwaniu większej liczby książek. Jesteśmy tutaj po to, aby zrobić swoją część: zapewnić długoterminowe przechowywanie tej prywatnej kolekcji."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Ta kolekcja pochodziła z połowy 2021 roku. W międzyczasie Z-Library rozwijała się w zawrotnym tempie: dodali około 3,8 miliona nowych książek. Oczywiście, są tam pewne duplikaty, ale większość z nich wydaje się być rzeczywiście nowymi książkami lub lepszej jakości skanami wcześniej przesłanych książek. Jest to w dużej mierze zasługa zwiększonej liczby moderatorów-wolontariuszy w Z-Library i ich systemu masowego przesyłania z deduplikacją. Chcielibyśmy pogratulować im tych osiągnięć."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Z przyjemnością ogłaszamy, że zdobyliśmy wszystkie książki, które zostały dodane do Z-Library między naszym ostatnim mirror a sierpniem 2022. Cofnęliśmy się również i zeskrobaliśmy niektóre książki, które przegapiliśmy za pierwszym razem. W sumie, ta nowa kolekcja ma około 24TB, co jest znacznie większe niż poprzednia (7TB). Nasz mirror ma teraz łącznie 31TB. Ponownie, deduplikowaliśmy w stosunku do Library Genesis, ponieważ już są dostępne torrenty dla tej kolekcji."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Proszę przejść do Pirate Library Mirror, aby sprawdzić nową kolekcję (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>). Tam znajdziesz więcej informacji o tym, jak są zorganizowane pliki i co się zmieniło od ostatniego razu. Nie podamy do niego linku stąd, ponieważ to tylko blog, który nie hostuje żadnych nielegalnych materiałów."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Oczywiście, seedowanie to także świetny sposób, aby nam pomóc. Dziękujemy wszystkim, którzy seedują nasz poprzedni zestaw torrentów. Jesteśmy wdzięczni za pozytywną reakcję i cieszymy się, że jest tak wiele osób, które dbają o zachowanie wiedzy i kultury w ten nietypowy sposób."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Jak zostać pirackim archiwistą"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Pierwsze wyzwanie może być zaskakujące. Nie jest to problem techniczny ani prawny. To problem psychologiczny."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Zanim zaczniemy, dwie aktualizacje dotyczące Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Otrzymaliśmy kilka niezwykle hojnych darowizn. Pierwsza to 10 tys. dolarów od anonimowej osoby, która również wspierała \"bookwarriora\", oryginalnego założyciela Library Genesis. Specjalne podziękowania dla bookwarriora za ułatwienie tej darowizny. Druga to kolejne 10 tys. dolarów od anonimowego darczyńcy, który skontaktował się z nami po naszej ostatniej publikacji i został zainspirowany do pomocy. Mieliśmy również kilka mniejszych darowizn. Dziękujemy bardzo za całe Wasze hojne wsparcie. Mamy kilka ekscytujących nowych projektów w przygotowaniu, które to wsparcie umożliwi, więc bądźcie na bieżąco."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Mieliśmy pewne trudności techniczne z rozmiarem naszego drugiego wydania, ale nasze torrenty są już dostępne i działają. Otrzymaliśmy również hojną ofertę od anonimowej osoby, która zgodziła się udostępnić naszą kolekcję na swoich bardzo szybkich serwerach, więc przeprowadzamy specjalne przesyłanie na ich maszyny, po czym wszyscy inni pobierający kolekcję powinni zauważyć znaczne przyspieszenie."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Całe książki można napisać o <em>dlaczego</em> cyfrowa ochrona jest ważna ogólnie, a piracki archiwizm w szczególności, ale pozwólcie, że przedstawimy krótki wstęp dla tych, którzy nie są zbyt zaznajomieni. Świat produkuje więcej wiedzy i kultury niż kiedykolwiek wcześniej, ale także więcej z tego jest tracone niż kiedykolwiek wcześniej. Ludzkość w dużej mierze powierza to dziedzictwo korporacjom, takim jak wydawcy akademiccy, serwisy streamingowe i firmy zajmujące się mediami społecznościowymi, które często nie okazały się być dobrymi opiekunami. Sprawdźcie dokument \"Digital Amnesia\" lub dowolny wykład Jasona Scotta."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Istnieją instytucje, które dobrze radzą sobie z archiwizowaniem tak dużo, jak tylko mogą, ale są one ograniczone prawem. Jako piraci, jesteśmy w unikalnej pozycji, aby archiwizować kolekcje, których nie mogą dotknąć, z powodu egzekwowania praw autorskich lub innych ograniczeń. Możemy również wielokrotnie mirrorować kolekcje na całym świecie, zwiększając tym samym szanse na ich właściwe zachowanie."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Na razie nie będziemy wchodzić w dyskusje na temat zalet i wad własności intelektualnej, moralności łamania prawa, rozważań na temat cenzury czy kwestii dostępu do wiedzy i kultury. Z tym wszystkim na boku, zanurzmy się w <em>jak</em>. Podzielimy się, jak nasz zespół stał się pirackimi archiwistami i jakie lekcje wynieśliśmy po drodze. Istnieje wiele wyzwań, gdy wyruszasz w tę podróż, i mamy nadzieję, że możemy pomóc ci przez nie przejść."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Społeczność"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Pierwsze wyzwanie może być zaskakujące. Nie jest to problem techniczny ani prawny. To problem psychologiczny: wykonywanie tej pracy w cieniu może być niezwykle samotne. W zależności od tego, co planujesz zrobić i jaki masz model zagrożeń, możesz musieć być bardzo ostrożny. Na jednym końcu spektrum mamy osoby takie jak Alexandra Elbakyan*, założycielka Sci-Hub, która jest bardzo otwarta na temat swojej działalności. Ale jest ona w dużym ryzyku aresztowania, jeśli odwiedziłaby kraj zachodni w tym momencie, i mogłaby stanąć przed dziesięcioleciami więzienia. Czy to ryzyko, które byłbyś gotów podjąć? My jesteśmy na drugim końcu spektrum; bardzo uważamy, aby nie zostawić żadnego śladu i mamy silne zabezpieczenia operacyjne."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Jak wspomniano na HN przez \"ynno\", Alexandra początkowo nie chciała być znana: \"Jej serwery były skonfigurowane do emitowania szczegółowych komunikatów o błędach z PHP, w tym pełnej ścieżki do pliku źródłowego, który znajdował się w katalogu /home/<USER>" Więc używaj losowych nazw użytkowników na komputerach, których używasz do tych rzeczy, na wypadek, gdybyś coś źle skonfigurował."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Ta tajemnica jednak wiąże się z kosztem psychologicznym. Większość ludzi uwielbia być doceniana za swoją pracę, a jednak nie możesz wziąć za to żadnego uznania w prawdziwym życiu. Nawet proste rzeczy mogą być wyzwaniem, jak pytania przyjaciół o to, co robiłeś (w pewnym momencie \"majstrowanie przy moim NAS / homelab\" staje się nudne)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dlatego tak ważne jest znalezienie jakiejś społeczności. Możesz zrezygnować z części zabezpieczeń operacyjnych, zwierzając się kilku bardzo bliskim przyjaciołom, którym wiesz, że możesz głęboko zaufać. Nawet wtedy bądź ostrożny, aby niczego nie zapisywać, na wypadek gdyby musieli przekazać swoje e-maile władzom lub gdyby ich urządzenia zostały w jakiś sposób skompromitowane."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Jeszcze lepiej jest znaleźć kilku innych piratów. Jeśli twoi bliscy przyjaciele są zainteresowani dołączeniem do ciebie, świetnie! W przeciwnym razie możesz znaleźć innych online. Niestety, to wciąż niszowa społeczność. Jak dotąd znaleźliśmy tylko garstkę innych, którzy są aktywni w tej przestrzeni. Dobrymi miejscami na start wydają się być fora Library Genesis i r/DataHoarder. Zespół Archiwum również ma podobnie myślących ludzi, chociaż działają oni w granicach prawa (nawet jeśli w niektórych szarych strefach prawa). Tradycyjne sceny \"warez\" i pirackie również mają osoby myślące w podobny sposób."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Jesteśmy otwarci na pomysły, jak rozwijać społeczność i eksplorować pomysły. Zapraszamy do kontaktu z nami na Twitterze lub Reddicie. Może moglibyśmy zorganizować jakieś forum lub grupę czatową. Jednym z wyzwań jest to, że to może łatwo zostać ocenzurowane przy użyciu popularnych platform, więc musielibyśmy to hostować sami. Istnieje również kompromis między prowadzeniem tych dyskusji w pełni publicznie (więcej potencjalnego zaangażowania) a uczynieniem ich prywatnymi (nie pozwalając potencjalnym \"celom\" wiedzieć, że zamierzamy je zeskrobać). Musimy to przemyśleć. Daj nam znać, jeśli jesteś tym zainteresowany!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekty"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Kiedy realizujemy projekt, ma on kilka faz:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Wybór domeny / filozofia: Na czym mniej więcej chcesz się skupić i dlaczego? Jakie są twoje unikalne pasje, umiejętności i okoliczności, które możesz wykorzystać na swoją korzyść?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Wybór celu: Którą konkretną kolekcję zamierzasz mirrorować?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Zbieranie metadata: Katalogowanie informacji o plikach, bez faktycznego pobierania (często znacznie większych) samych plików."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Wybór danych: Na podstawie metadata, zawężenie, które dane są najbardziej istotne do archiwizacji w tej chwili. Może to być wszystko, ale często istnieje rozsądny sposób na oszczędność miejsca i przepustowości."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Zbieranie danych: Faktyczne pobieranie danych."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Dystrybucja: Pakowanie w torrenty, ogłaszanie gdzieś, zachęcanie ludzi do ich rozpowszechniania."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "To nie są całkowicie niezależne fazy i często wnioski z późniejszej fazy odsyłają cię z powrotem do wcześniejszej fazy. Na przykład, podczas zbierania metadata możesz zdać sobie sprawę, że wybrany cel ma mechanizmy obronne poza twoimi umiejętnościami (jak blokady IP), więc wracasz i znajdujesz inny cel."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Wybór domeny / filozofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nie brakuje wiedzy i dziedzictwa kulturowego do ocalenia, co może być przytłaczające. Dlatego często warto poświęcić chwilę na zastanowienie się, jaki może być Twój wkład."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Każdy ma inny sposób myślenia o tym, ale oto kilka pytań, które możesz sobie zadać:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Dlaczego Cię to interesuje? Co Cię pasjonuje? Jeśli uda nam się zebrać grupę ludzi, którzy archiwizują rzeczy, na których im szczególnie zależy, to pokryje to wiele! Będziesz wiedział znacznie więcej niż przeciętna osoba o swojej pasji, na przykład jakie dane są ważne do zachowania, jakie są najlepsze kolekcje i społeczności online itp."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Jakie umiejętności posiadasz, które możesz wykorzystać na swoją korzyść? Na przykład, jeśli jesteś ekspertem ds. bezpieczeństwa online, możesz znaleźć sposoby na pokonanie blokad IP dla bezpiecznych celów. Jeśli jesteś świetny w organizowaniu społeczności, być może możesz zebrać ludzi wokół jakiegoś celu. Warto jednak znać trochę programowania, choćby po to, aby zachować dobrą operacyjną bezpieczeństwo w całym tym procesie."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Ile masz na to czasu? Nasza rada to zacząć od małych projektów i przechodzić do większych, gdy nabierzesz wprawy, ale może to stać się pochłaniające."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Na jakim obszarze warto się skupić, aby uzyskać największy efekt? Jeśli zamierzasz poświęcić X godzin na pirackie archiwizowanie, to jak możesz uzyskać największy \"efekt za swoje pieniądze\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Jakie są unikalne sposoby, w jakie o tym myślisz? Możesz mieć ciekawe pomysły lub podejścia, które inni mogli przeoczyć."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "W naszym przypadku szczególnie zależało nam na długoterminowym zachowaniu nauki. Wiedzieliśmy o Library Genesis i o tym, jak było wielokrotnie mirrorowane za pomocą torrentów. Uwielbialiśmy ten pomysł. Pewnego dnia jeden z nas próbował znaleźć podręczniki naukowe w Library Genesis, ale nie mógł ich znaleźć, co poddało w wątpliwość, jak kompletne to naprawdę było. Następnie szukaliśmy tych podręczników online i znaleźliśmy je w innych miejscach, co zasadziło ziarno dla naszego projektu. Nawet zanim dowiedzieliśmy się o Z-Library, mieliśmy pomysł, aby nie próbować zbierać wszystkich tych książek ręcznie, ale skupić się na mirrorowaniu istniejących kolekcji i przekazywaniu ich z powrotem do Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Wybór celu"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Więc mamy obszar, na który patrzymy, teraz którą konkretną kolekcję mirrorujemy? Jest kilka rzeczy, które sprawiają, że cel jest dobry:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Duży"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unikalne: nie jest już dobrze pokryte przez inne projekty."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Dostępne: nie używa wielu warstw ochrony, aby uniemożliwić zeskrobywanie ich metadata i danych."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Specjalna wiedza: masz jakieś specjalne informacje o tym celu, na przykład masz specjalny dostęp do tej kolekcji lub odkryłeś, jak pokonać ich zabezpieczenia. Nie jest to wymagane (nasz nadchodzący projekt nie robi niczego specjalnego), ale z pewnością pomaga!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Kiedy znaleźliśmy nasze podręczniki naukowe na stronach innych niż Library Genesis, próbowaliśmy dowiedzieć się, jak trafiły do internetu. Następnie odkryliśmy Z-Library i zdaliśmy sobie sprawę, że chociaż większość książek nie pojawia się tam jako pierwsza, to ostatecznie tam trafiają. Dowiedzieliśmy się o jej związku z Library Genesis oraz strukturze motywacyjnej (finansowej) i lepszym interfejsie użytkownika, które sprawiły, że była to znacznie bardziej kompletna kolekcja. Następnie przeprowadziliśmy wstępne zeskrobywanie metadata i danych i zdaliśmy sobie sprawę, że możemy obejść ich limity pobierania IP, wykorzystując specjalny dostęp jednego z naszych członków do wielu serwerów proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Podczas eksploracji różnych celów, już teraz ważne jest, aby ukrywać swoje ślady, używając VPN-ów i jednorazowych adresów e-mail, o czym będziemy mówić więcej później."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Zbieranie metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Przejdźmy teraz do bardziej technicznych kwestii. Aby faktycznie zbierać metadata z witryn internetowych, utrzymaliśmy wszystko w prostocie. Używamy skryptów w Pythonie, czasami curl, oraz bazy danych MySQL do przechowywania wyników. Nie używaliśmy żadnego zaawansowanego oprogramowania do zbierania danych, które potrafi mapować złożone strony, ponieważ jak dotąd potrzebowaliśmy tylko zbierać dane z jednego lub dwóch rodzajów stron, po prostu enumerując przez identyfikatory i parsując HTML. Jeśli nie ma łatwo enumerowanych stron, może być potrzebny odpowiedni crawler, który spróbuje znaleźć wszystkie strony."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Zanim zaczniesz skrobać całą stronę, spróbuj zrobić to ręcznie przez chwilę. Przejdź przez kilka tuzinów stron samodzielnie, aby zrozumieć, jak to działa. Czasami już w ten sposób napotkasz blokady IP lub inne interesujące zachowania. To samo dotyczy skrobania danych: zanim zagłębisz się w ten cel, upewnij się, że możesz skutecznie pobrać jego dane."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Aby obejść ograniczenia, możesz spróbować kilku rzeczy. Czy są inne adresy IP lub serwery, które hostują te same dane, ale nie mają tych samych ograniczeń? Czy są jakieś punkty końcowe API, które nie mają ograniczeń, podczas gdy inne je mają? Przy jakiej szybkości pobierania Twój IP jest blokowany i na jak długo? A może nie jesteś blokowany, ale ograniczany? Co się stanie, jeśli utworzysz konto użytkownika, jak wtedy zmieniają się rzeczy? Czy możesz użyć HTTP/2, aby utrzymać otwarte połączenia i czy to zwiększa szybkość, z jaką możesz żądać stron? Czy są strony, które wymieniają wiele plików naraz i czy informacje tam wymienione są wystarczające?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Rzeczy, które prawdopodobnie chcesz zapisać, obejmują:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Tytuł"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nazwa pliku / lokalizacja"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: może być jakimś wewnętrznym ID, ale przydatne są również ID takie jak ISBN lub DOI."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Rozmiar: aby obliczyć, ile miejsca na dysku potrzebujesz."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): aby potwierdzić, że pobrałeś plik poprawnie."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data dodania/modyfikacji: abyś mógł wrócić później i pobrać pliki, których wcześniej nie pobrałeś (choć często możesz do tego użyć również ID lub hash)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Opis, kategoria, tagi, autorzy, język itp."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Zazwyczaj robimy to w dwóch etapach. Najpierw pobieramy surowe pliki HTML, zazwyczaj bezpośrednio do MySQL (aby uniknąć wielu małych plików, o czym mówimy więcej poniżej). Następnie, w osobnym kroku, przechodzimy przez te pliki HTML i parsujemy je do rzeczywistych tabel MySQL. W ten sposób nie musisz ponownie pobierać wszystkiego od zera, jeśli odkryjesz błąd w swoim kodzie parsującym, ponieważ możesz po prostu przetworzyć pliki HTML z nowym kodem. Często łatwiej jest również zrównoleglić krok przetwarzania, oszczędzając w ten sposób trochę czasu (i możesz napisać kod przetwarzania, gdy skryptowanie jest w toku, zamiast pisać oba kroki naraz)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Na koniec, zauważ, że dla niektórych celów skrobanie metadanych to wszystko, co jest. Istnieją ogromne kolekcje metadanych, które nie są odpowiednio zachowane."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Wybór danych"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Często możesz użyć metadanych, aby określić rozsądny podzbiór danych do pobrania. Nawet jeśli ostatecznie chcesz pobrać wszystkie dane, warto najpierw priorytetowo traktować najważniejsze elementy, na wypadek gdybyś został wykryty i obrony zostały wzmocnione, lub ponieważ musiałbyś kupić więcej dysków, lub po prostu dlatego, że coś innego pojawi się w Twoim życiu, zanim będziesz mógł pobrać wszystko."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Na przykład, kolekcja może mieć wiele edycji tego samego zasobu (jak książka lub film), gdzie jedna jest oznaczona jako najlepsza jakość. Zachowanie tych edycji najpierw miałoby dużo sensu. Możesz ostatecznie chcieć zachować wszystkie edycje, ponieważ w niektórych przypadkach metadane mogą być niepoprawnie oznaczone, lub mogą istnieć nieznane kompromisy między edycjami (na przykład, \"najlepsza edycja\" może być najlepsza pod wieloma względami, ale gorsza pod innymi, jak film o wyższej rozdzielczości, ale bez napisów)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Możesz także przeszukać swoją bazę danych metadanych, aby znaleźć interesujące rzeczy. Jaki jest największy plik, który jest hostowany, i dlaczego jest tak duży? Jaki jest najmniejszy plik? Czy istnieją interesujące lub nieoczekiwane wzorce, jeśli chodzi o określone kategorie, języki itp.? Czy są duplikaty lub bardzo podobne tytuły? Czy istnieją wzorce dotyczące tego, kiedy dane zostały dodane, na przykład jeden dzień, w którym wiele plików zostało dodanych naraz? Często można się wiele nauczyć, patrząc na zestaw danych w różny sposób."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "W naszym przypadku zduplikowaliśmy książki z Z-Library w stosunku do skrótów md5 w Library Genesis, oszczędzając w ten sposób dużo czasu na pobieranie i przestrzeni dyskowej. To jednak dość unikalna sytuacja. W większości przypadków nie ma kompleksowych baz danych, które pliki są już odpowiednio zachowane przez innych piratów. To samo w sobie jest ogromną szansą dla kogoś tam. Byłoby świetnie mieć regularnie aktualizowany przegląd rzeczy, takich jak muzyka i filmy, które są już szeroko seedowane na stronach torrentowych, i dlatego mają niższy priorytet do uwzględnienia w pirackich mirrorach."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Skrobanie danych"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Teraz jesteś gotowy do pobrania danych w dużej ilości. Jak wspomniano wcześniej, na tym etapie powinieneś już ręcznie pobrać kilka plików, aby lepiej zrozumieć zachowanie i ograniczenia celu. Jednak nadal mogą pojawić się niespodzianki, gdy faktycznie zaczniesz pobierać wiele plików naraz."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nasza rada tutaj to przede wszystkim trzymać się prostoty. Zacznij od pobrania kilku plików. Możesz użyć Pythona, a następnie rozszerzyć na wiele wątków. Ale czasami jeszcze prościej jest generować pliki Bash bezpośrednio z bazy danych, a następnie uruchamiać je w wielu oknach terminala, aby zwiększyć skalę. Szybka sztuczka techniczna, którą warto tu wspomnieć, to użycie OUTFILE w MySQL, które można napisać wszędzie, jeśli wyłączysz \"secure_file_priv\" w mysqld.cnf (i upewnij się, że również wyłączysz/nadpiszesz AppArmor, jeśli jesteś na Linuksie)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Przechowujemy dane na prostych dyskach twardych. Zacznij od tego, co masz, i powoli się rozwijaj. Może być przytłaczające myślenie o przechowywaniu setek TB danych. Jeśli to jest sytuacja, z którą się zmagasz, najpierw umieść dobry podzbiór, a w swoim ogłoszeniu poproś o pomoc w przechowywaniu reszty. Jeśli chcesz samodzielnie zdobyć więcej dysków twardych, r/DataHoarder ma dobre zasoby dotyczące uzyskiwania dobrych ofert."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Staraj się nie martwić zbytnio o zaawansowane systemy plików. Łatwo jest wpaść w pułapkę konfigurowania takich rzeczy jak ZFS. Jednym z technicznych szczegółów, o których warto wiedzieć, jest to, że wiele systemów plików nie radzi sobie dobrze z dużą ilością plików. Odkryliśmy, że prostym obejściem jest tworzenie wielu katalogów, np. dla różnych zakresów ID lub prefiksów hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Po pobraniu danych upewnij się, że sprawdziłeś integralność plików za pomocą hashów w metadata, jeśli są dostępne."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Dystrybucja"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Masz dane, co daje ci posiadanie pierwszego na świecie pirackiego mirroru twojego celu (najprawdopodobniej). W wielu aspektach najtrudniejsza część jest już za tobą, ale najniebezpieczniejsza część jest jeszcze przed tobą. W końcu do tej pory byłeś niewidoczny; latając pod radarem. Wszystko, co musiałeś zrobić, to używać dobrego VPN przez cały czas, nie wypełniać swoich danych osobowych w żadnych formularzach (oczywiście), a może używać specjalnej sesji przeglądarki (lub nawet innego komputera)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Teraz musisz rozprowadzić dane. W naszym przypadku najpierw chcieliśmy przekazać książki z powrotem do Library Genesis, ale szybko odkryliśmy trudności z tym związane (sortowanie fikcji vs lit. faktu). Dlatego zdecydowaliśmy się na dystrybucję za pomocą torrentów w stylu Library Genesis. Jeśli masz możliwość przyczynienia się do istniejącego projektu, może to zaoszczędzić ci dużo czasu. Jednak obecnie nie ma wielu dobrze zorganizowanych pirackich mirrorów."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Załóżmy więc, że decydujesz się na samodzielne dystrybuowanie torrentów. Staraj się, aby te pliki były małe, aby łatwo było je mirrorować na innych stronach internetowych. Będziesz musiał samodzielnie seedować torrenty, pozostając anonimowym. Możesz użyć VPN (z przekierowaniem portów lub bez), lub zapłacić za Seedbox za pomocą tumbled Bitcoins. Jeśli nie wiesz, co oznaczają niektóre z tych terminów, będziesz musiał trochę poczytać, ponieważ ważne jest, aby zrozumieć ryzyko związane z tymi działaniami."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Możesz hostować same pliki torrent na istniejących stronach torrentowych. W naszym przypadku zdecydowaliśmy się faktycznie hostować stronę internetową, ponieważ chcieliśmy również w jasny sposób szerzyć naszą filozofię. Możesz to zrobić samodzielnie w podobny sposób (używamy Njalla do naszych domen i hostingu, opłacanych za pomocą tumbled Bitcoins), ale możesz również skontaktować się z nami, abyśmy mogli hostować twoje torrenty. Chcemy zbudować kompleksowy indeks pirackich mirrorów z czasem, jeśli ten pomysł się przyjmie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Jeśli chodzi o wybór VPN, wiele już na ten temat napisano, więc powtórzymy tylko ogólną radę, aby wybierać według reputacji. Faktyczne, przetestowane w sądzie polityki no-log z długą historią ochrony prywatności to najniższe ryzyko, naszym zdaniem. Należy zauważyć, że nawet jeśli zrobisz wszystko dobrze, nigdy nie osiągniesz zerowego ryzyka. Na przykład, podczas seedowania swoich torrentów, wysoce zmotywowany aktor państwowy może prawdopodobnie przyjrzeć się przepływom danych przychodzących i wychodzących dla serwerów VPN i wywnioskować, kim jesteś. Albo możesz po prostu popełnić jakiś błąd. Prawdopodobnie już to zrobiliśmy i zrobimy to ponownie. Na szczęście państwa narodowe nie przejmują się <em>aż tak</em> bardzo piractwem."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Jedną z decyzji do podjęcia dla każdego projektu jest to, czy publikować go pod tą samą tożsamością, co wcześniej, czy nie. Jeśli nadal używasz tej samej nazwy, błędy w bezpieczeństwie operacyjnym z wcześniejszych projektów mogą wrócić, by cię ugryźć. Ale publikowanie pod różnymi nazwami oznacza, że nie budujesz długotrwałej reputacji. Zdecydowaliśmy się na silne bezpieczeństwo operacyjne od samego początku, aby móc nadal używać tej samej tożsamości, ale nie zawahamy się opublikować pod inną nazwą, jeśli popełnimy błąd lub jeśli okoliczności tego wymagają."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Rozpowszechnianie informacji może być trudne. Jak powiedzieliśmy, to wciąż niszowa społeczność. Początkowo zamieściliśmy post na Reddicie, ale naprawdę zyskaliśmy popularność na Hacker News. Na razie nasza rekomendacja to zamieszczenie go w kilku miejscach i zobaczenie, co się stanie. I jeszcze raz, skontaktuj się z nami. Chcielibyśmy rozprzestrzenić wieść o większej liczbie wysiłków pirackiego archiwizmu."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Podsumowanie"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Mamy nadzieję, że to jest pomocne dla nowo zaczynających pirackich archiwistów. Jesteśmy podekscytowani, że możemy powitać cię w tym świecie, więc nie wahaj się skontaktować. Zachowajmy jak najwięcej wiedzy i kultury świata, i mirrorujmy ją szeroko i daleko."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Przedstawiamy Piracką Bibliotekę Mirror: Zachowując 7TB książek (które nie są w Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Ten projekt (EDYCJA: przeniesiony do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>) ma na celu przyczynienie się do zachowania i uwolnienia ludzkiej wiedzy. Wnosimy nasz mały i skromny wkład, idąc śladami wielkich przed nami."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Skupienie tego projektu jest zilustrowane jego nazwą:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piracki</strong> - Celowo naruszamy prawo autorskie w większości krajów. Pozwala nam to robić coś, czego legalne podmioty nie mogą: upewnić się, że książki są mirrorowane szeroko i daleko."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteka</strong> - Jak większość bibliotek, skupiamy się głównie na materiałach pisanych, takich jak książki. W przyszłości możemy rozszerzyć naszą ofertę o inne rodzaje mediów."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Jesteśmy wyłącznie mirror'em istniejących bibliotek. Skupiamy się na zachowaniu, a nie na ułatwianiu wyszukiwania i pobierania książek (dostęp) ani na tworzeniu dużej społeczności osób, które przyczyniają się do dodawania nowych książek (źródła)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Pierwszą biblioteką, którą zmirrorowaliśmy, jest Z-Library. To popularna (i nielegalna) biblioteka. Przejęli oni kolekcję Library Genesis i uczynili ją łatwo przeszukiwalną. Ponadto, stali się bardzo skuteczni w pozyskiwaniu nowych książek, zachęcając użytkowników do ich dodawania różnymi korzyściami. Obecnie nie przekazują tych nowych książek z powrotem do Library Genesis. I w przeciwieństwie do Library Genesis, nie umożliwiają łatwego mirrorowania swojej kolekcji, co uniemożliwia szerokie zachowanie. Jest to ważne dla ich modelu biznesowego, ponieważ pobierają opłaty za dostęp do swojej kolekcji w dużych ilościach (więcej niż 10 książek dziennie)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nie oceniamy moralnie pobierania opłat za dostęp do nielegalnej kolekcji książek w dużych ilościach. Nie ma wątpliwości, że Z-Library odniosła sukces w rozszerzaniu dostępu do wiedzy i pozyskiwaniu większej liczby książek. Jesteśmy tutaj po to, aby zrobić swoją część: zapewnić długoterminowe przechowywanie tej prywatnej kolekcji."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Chcielibyśmy zaprosić Cię do pomocy w zachowaniu i uwolnieniu ludzkiej wiedzy poprzez pobieranie i seedowanie naszych torrentów. Zobacz stronę projektu, aby uzyskać więcej informacji o tym, jak dane są zorganizowane."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Chcielibyśmy również bardzo zaprosić Cię do podzielenia się swoimi pomysłami na temat tego, które kolekcje zmirrorować następnie i jak to zrobić. Razem możemy osiągnąć wiele. To tylko mały wkład wśród niezliczonych innych. Dziękujemy za wszystko, co robisz."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nie linkujemy do plików z tego bloga. Proszę znaleźć je samodzielnie.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Zrzut ISBNdb, czyli ile książek jest zachowanych na zawsze?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Gdybyśmy mieli odpowiednio zdeduplikować pliki z bibliotek cieni, jaki procent wszystkich książek na świecie zachowaliśmy?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Z Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>), naszym celem jest zebranie wszystkich książek na świecie i zachowanie ich na zawsze.<sup>1</sup> Między naszymi torrentami Z-Library a oryginalnymi torrentami Library Genesis mamy 11 783 153 pliki. Ale ile to naprawdę jest? Gdybyśmy odpowiednio zdeduplikowali te pliki, jaki procent wszystkich książek na świecie udało nam się zachować? Naprawdę chcielibyśmy mieć coś takiego:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% o% pisemnego dziedzictwa ludzkości zachowane na zawsze"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Aby uzyskać procent, potrzebujemy mianownika: całkowitej liczby książek, które kiedykolwiek zostały opublikowane.<sup>2</sup> Przed upadkiem Google Books, inżynier pracujący nad projektem, Leonid Taycher, <a %(booksearch_blogspot)s>próbował oszacować</a> tę liczbę. Wyszedł — z przymrużeniem oka — z liczbą 129 864 880 („przynajmniej do niedzieli”). Oszacował tę liczbę, budując zintegrowaną bazę danych wszystkich książek na świecie. W tym celu zebrał różne zestawy danych i połączył je na różne sposoby."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Na marginesie, jest jeszcze jedna osoba, która próbowała skatalogować wszystkie książki na świecie: Aaron Swartz, zmarły aktywista cyfrowy i współzałożyciel Reddita.<sup>3</sup> Rozpoczął <a %(youtube)s>Open Library</a> z celem „jedna strona internetowa dla każdej książki, która kiedykolwiek została opublikowana”, łącząc dane z wielu różnych źródeł. Ostatecznie zapłacił najwyższą cenę za swoją pracę nad zachowaniem cyfrowym, gdy został oskarżony o masowe pobieranie artykułów naukowych, co doprowadziło do jego samobójstwa. Nie trzeba dodawać, że jest to jeden z powodów, dla których nasza grupa jest pseudonimowa i dlaczego jesteśmy bardzo ostrożni. Open Library jest nadal heroicznie prowadzona przez ludzi z Internet Archive, kontynuując dziedzictwo Aarona. Wrócimy do tego później w tym poście."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "W poście na blogu Google, Taycher opisuje niektóre z wyzwań związanych z oszacowaniem tej liczby. Po pierwsze, co stanowi książkę? Istnieje kilka możliwych definicji:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Kopie fizyczne.</strong> Oczywiście nie jest to zbyt pomocne, ponieważ są to tylko duplikaty tego samego materiału. Byłoby fajnie, gdybyśmy mogli zachować wszystkie adnotacje, które ludzie robią w książkach, jak słynne „bazgroły na marginesach” Fermata. Ale niestety, to pozostanie marzeniem archiwisty."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Dzieła”.</strong> Na przykład „Harry Potter i Komnata Tajemnic” jako logiczna koncepcja, obejmująca wszystkie jej wersje, takie jak różne tłumaczenia i wznowienia. To dość użyteczna definicja, ale może być trudno określić, co się liczy. Na przykład, prawdopodobnie chcemy zachować różne tłumaczenia, chociaż wznowienia z tylko drobnymi różnicami mogą nie być tak ważne."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Wydania”.</strong> Tutaj liczymy każdą unikalną wersję książki. Jeśli cokolwiek w niej jest inne, jak inna okładka czy inna przedmowa, liczy się jako inne wydanie."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Pliki.</strong> Pracując z bibliotekami cieni, takimi jak Library Genesis, Sci-Hub czy Z-Library, istnieje dodatkowe rozważenie. Może być wiele skanów tego samego wydania. A ludzie mogą tworzyć lepsze wersje istniejących plików, skanując tekst za pomocą OCR lub prostując strony, które były skanowane pod kątem. Chcemy liczyć te pliki jako jedno wydanie, co wymagałoby dobrego metadata lub deduplikacji za pomocą miar podobieństwa dokumentów."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Wydania” wydają się najbardziej praktyczną definicją tego, czym są „książki”. Wygodnie, ta definicja jest również używana do przypisywania unikalnych numerów ISBN. ISBN, czyli Międzynarodowy Standardowy Numer Książki, jest powszechnie używany w międzynarodowym handlu, ponieważ jest zintegrowany z międzynarodowym systemem kodów kreskowych („Międzynarodowy Numer Artykułu”). Jeśli chcesz sprzedawać książkę w sklepach, potrzebuje ona kodu kreskowego, więc otrzymujesz ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "W poście na blogu Taychera wspomniano, że chociaż ISBN-y są przydatne, nie są uniwersalne, ponieważ zostały naprawdę przyjęte dopiero w połowie lat siedemdziesiątych i nie wszędzie na świecie. Mimo to, ISBN jest prawdopodobnie najczęściej używanym identyfikatorem wydań książek, więc to nasz najlepszy punkt wyjścia. Jeśli możemy znaleźć wszystkie ISBN-y na świecie, uzyskamy użyteczną listę książek, które wciąż trzeba zachować."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Więc skąd wziąć dane? Istnieje kilka istniejących inicjatyw, które próbują skompilować listę wszystkich książek na świecie:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> W końcu przeprowadzili te badania dla Google Books. Jednak ich metadata nie są dostępne w dużych ilościach i są dość trudne do zeskrobania."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Jak wspomniano wcześniej, to jest ich całkowita misja. Pozyskali ogromne ilości danych bibliotecznych z bibliotek współpracujących i archiwów narodowych, i nadal to robią. Mają również wolontariuszy bibliotekarzy i zespół techniczny, który stara się usuwać duplikaty rekordów i oznaczać je wszelkiego rodzaju metadata. Co najlepsze, ich zbiór danych jest całkowicie otwarty. Możesz po prostu <a %(openlibrary)s>pobrać go</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> To jest strona internetowa prowadzona przez organizację non-profit OCLC, która sprzedaje systemy zarządzania bibliotekami. Agregują metadata książek z wielu bibliotek i udostępniają je za pośrednictwem strony WorldCat. Jednakże, zarabiają również na sprzedaży tych danych, więc nie są one dostępne do masowego pobrania. Mają jednak dostępne do pobrania niektóre bardziej ograniczone zbiory danych masowych, we współpracy z konkretnymi bibliotekami."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> To jest temat tego wpisu na blogu. ISBNdb przeszukuje różne strony internetowe w poszukiwaniu metadata książek, w szczególności danych o cenach, które następnie sprzedają księgarniom, aby mogły ustalać ceny swoich książek zgodnie z resztą rynku. Ponieważ ISBN-y są obecnie dość uniwersalne, skutecznie stworzyli „stronę internetową dla każdej książki”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Różne indywidualne systemy biblioteczne i archiwa.</strong> Istnieją biblioteki i archiwa, które nie zostały zindeksowane i zebrane przez żadną z powyższych, często dlatego, że są niedofinansowane lub z innych powodów nie chcą dzielić się swoimi danymi z Open Library, OCLC, Google i tak dalej. Wiele z nich ma cyfrowe zapisy dostępne przez internet, i często nie są one dobrze chronione, więc jeśli chcesz pomóc i dobrze się bawić, ucząc się o dziwnych systemach bibliotecznych, to są świetne punkty wyjścia."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "W tym wpisie z radością ogłaszamy małe wydanie (w porównaniu do naszych poprzednich wydań Z-Library). Przeszukaliśmy większość ISBNdb i udostępniliśmy dane do pobrania na stronie Pirate Library Mirror (EDYCJA: przeniesiono do <a %(wikipedia_annas_archive)s>Archiwum Anny</a>; nie podamy tutaj bezpośredniego linku, po prostu poszukajcie). To około 30,9 miliona rekordów (20GB jako <a %(jsonlines)s>JSON Lines</a>; 4,4GB skompresowane). Na swojej stronie twierdzą, że mają faktycznie 32,6 miliona rekordów, więc mogliśmy jakoś pominąć niektóre, lub <em>oni</em> mogą coś robić źle. W każdym razie, na razie nie podzielimy się dokładnie, jak to zrobiliśmy — zostawimy to jako ćwiczenie dla czytelnika. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "To, czym się podzielimy, to wstępna analiza, aby spróbować zbliżyć się do oszacowania liczby książek na świecie. Przyjrzeliśmy się trzem zbiorom danych: temu nowemu zbiorowi danych ISBNdb, naszemu oryginalnemu wydaniu metadata, które zebraliśmy z biblioteki cieni Z-Library (która obejmuje Library Genesis), oraz zrzutowi danych Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Zacznijmy od kilku przybliżonych liczb:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "W obu Z-Library/Libgen i Open Library jest znacznie więcej książek niż unikalnych ISBN-ów. Czy to oznacza, że wiele z tych książek nie ma ISBN-ów, czy po prostu brakuje metadata ISBN? Prawdopodobnie możemy odpowiedzieć na to pytanie, łącząc automatyczne dopasowywanie na podstawie innych atrybutów (tytuł, autor, wydawca itp.), wciągając więcej źródeł danych i wyodrębniając ISBN-y z rzeczywistych skanów książek (w przypadku Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Ile z tych ISBN-ów jest unikalnych? Najlepiej to zilustrować za pomocą diagramu Venna:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Aby być bardziej precyzyjnym:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Byliśmy zaskoczeni, jak mało jest nakładania się! ISBNdb ma ogromną ilość ISBN-ów, które nie pojawiają się ani w Z-Library, ani w Open Library, i to samo dotyczy (w mniejszym, ale wciąż znaczącym stopniu) pozostałych dwóch. To rodzi wiele nowych pytań. Jak bardzo pomogłoby automatyczne dopasowywanie w oznaczaniu książek, które nie były oznaczone ISBN-ami? Czy byłoby wiele dopasowań, a tym samym zwiększone nakładanie się? Co by się stało, gdybyśmy wprowadzili 4. lub 5. zbiór danych? Ile nakładania się byśmy wtedy zobaczyli?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "To daje nam punkt wyjścia. Możemy teraz przyjrzeć się wszystkim ISBN-om, które nie były w zbiorze danych Z-Library, i które nie pasują również do pól tytuł/autor. To może dać nam możliwość zachowania wszystkich książek na świecie: najpierw poprzez przeszukiwanie internetu w poszukiwaniu skanów, a następnie poprzez wyjście w rzeczywistość, aby skanować książki. To ostatnie mogłoby być nawet finansowane społecznościowo lub napędzane przez „nagrody” od osób, które chciałyby zobaczyć konkretne książki zdigitalizowane. Wszystko to jest opowieścią na inny czas."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Jeśli chcesz pomóc w którejkolwiek z tych rzeczy — dalsza analiza; zbieranie więcej metadata; znajdowanie więcej książek; OCR książek; robienie tego dla innych dziedzin (np. artykuły, audiobooki, filmy, seriale, czasopisma) lub nawet udostępnianie niektórych z tych danych do rzeczy takich jak ML / szkolenie dużych modeli językowych — proszę skontaktuj się ze mną (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Jeśli jesteś szczególnie zainteresowany analizą danych, pracujemy nad udostępnieniem naszych zbiorów danych i skryptów w bardziej łatwym do użycia formacie. Byłoby świetnie, gdybyś mógł po prostu rozwidlić notatnik i zacząć się tym bawić."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Na koniec, jeśli chcesz wesprzeć tę pracę, rozważ dokonanie darowizny. To całkowicie wolontariacka operacja, a Twój wkład robi ogromną różnicę. Każda pomoc się liczy. Na razie przyjmujemy darowizny w kryptowalutach; zobacz stronę Darowizny w Archiwum Anny."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Dla jakiejś rozsądnej definicji \"na zawsze\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Oczywiście, pisemne dziedzictwo ludzkości to znacznie więcej niż książki, zwłaszcza w dzisiejszych czasach. Na potrzeby tego wpisu i naszych ostatnich wydań skupiamy się na książkach, ale nasze zainteresowania sięgają dalej."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Jest wiele więcej do powiedzenia o Aaronie Swartzu, ale chcieliśmy tylko krótko go wspomnieć, ponieważ odgrywa kluczową rolę w tej historii. Z czasem więcej osób może natknąć się na jego nazwisko po raz pierwszy i samodzielnie zagłębić się w temat."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Krytyczne okno bibliotek cieni"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Jak możemy twierdzić, że zachowamy nasze zbiory na zawsze, skoro już zbliżają się do 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Wersja chińska 中文版</a>, dyskusja na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "W Archiwum Anny często pytają nas, jak możemy twierdzić, że zachowamy nasze zbiory na zawsze, skoro ich całkowity rozmiar już zbliża się do 1 Petabajta (1000 TB) i nadal rośnie. W tym artykule przyjrzymy się naszej filozofii i zobaczymy, dlaczego następna dekada jest kluczowa dla naszej misji zachowania wiedzy i kultury ludzkości."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Całkowity rozmiar</a> naszych kolekcji, w ciągu ostatnich kilku miesięcy, rozbity według liczby seederów torrentów."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorytety"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Dlaczego tak bardzo zależy nam na artykułach i książkach? Odłóżmy na bok nasze fundamentalne przekonanie o zachowaniu w ogóle — być może napiszemy o tym kolejny post. Dlaczego więc artykuły i książki konkretnie? Odpowiedź jest prosta: <strong>gęstość informacji</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Na megabajt przechowywania, tekst pisany przechowuje najwięcej informacji ze wszystkich mediów. Chociaż dbamy zarówno o wiedzę, jak i kulturę, bardziej zależy nam na tej pierwszej. Ogólnie rzecz biorąc, znajdujemy hierarchię gęstości informacji i ważności zachowania, która wygląda mniej więcej tak:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Prace naukowe, czasopisma, raporty"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dane organiczne, takie jak sekwencje DNA, nasiona roślin czy próbki mikroorganizmów"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Książki lit. faktu"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Kod oprogramowania naukowego i inżynieryjnego"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dane pomiarowe, takie jak pomiary naukowe, dane ekonomiczne, raporty korporacyjne"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Strony internetowe naukowe i inżynieryjne, dyskusje online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Czasopisma, gazety, podręczniki lit. faktu"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transkrypcje rozmów, dokumentów, podcastów lit. faktu"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dane wewnętrzne z korporacji lub rządów (wycieki)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Rekordy metadata ogólnie (lit. faktu i fikcji; innych mediów, sztuki, ludzi, itp.; w tym recenzje)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dane geograficzne (np. mapy, badania geologiczne)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkrypcje postępowań prawnych lub sądowych"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fikcyjne lub rozrywkowe wersje wszystkich powyższych"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Ranking na tej liście jest nieco arbitralny — kilka pozycji jest równorzędnych lub budzi kontrowersje w naszym zespole — i prawdopodobnie zapominamy o niektórych ważnych kategoriach. Ale mniej więcej tak ustalamy priorytety."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Niektóre z tych pozycji są zbyt różne od innych, abyśmy się nimi martwili (lub są już obsługiwane przez inne instytucje), takie jak dane organiczne lub geograficzne. Ale większość pozycji na tej liście jest dla nas naprawdę ważna."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Kolejnym dużym czynnikiem w naszych priorytetach jest to, jak bardzo zagrożone jest dane dzieło. Wolimy skupić się na dziełach, które są:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rzadkie"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Wyjątkowo niedoceniane"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Wyjątkowo zagrożone zniszczeniem (np. przez wojnę, cięcia finansowe, pozwy sądowe lub prześladowania polityczne)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Wreszcie, zależy nam na skali. Mamy ograniczony czas i pieniądze, więc wolimy spędzić miesiąc na ratowaniu 10 000 książek niż 1 000 książek — jeśli są one równie wartościowe i zagrożone."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteki cieni"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Istnieje wiele organizacji, które mają podobne misje i priorytety. Rzeczywiście, istnieją biblioteki, archiwa, laboratoria, muzea i inne instytucje zajmujące się tego rodzaju zachowaniem. Wiele z nich jest dobrze finansowanych przez rządy, osoby prywatne lub korporacje. Ale mają one jedną ogromną ślepą plamkę: system prawny."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Tutaj leży unikalna rola bibliotek cieni, i powód, dla którego istnieje Archiwum Anny. Możemy robić rzeczy, których inne instytucje nie mogą robić. Teraz, to nie jest (często) tak, że możemy archiwizować materiały, które są nielegalne do zachowania gdzie indziej. Nie, w wielu miejscach jest legalne budowanie archiwum z dowolnymi książkami, artykułami, magazynami i tak dalej."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ale czego często brakuje legalnym archiwom, to <strong>nadmiarowość i trwałość</strong>. Istnieją książki, z których tylko jedna kopia istnieje w jakiejś fizycznej bibliotece gdzieś na świecie. Istnieją rekordy metadata strzeżone przez jedną korporację. Istnieją gazety zachowane tylko na mikrofilmie w jednym archiwum. Biblioteki mogą mieć cięcia w finansowaniu, korporacje mogą zbankrutować, archiwa mogą zostać zbombardowane i spalone do ziemi. To nie jest hipotetyczne — to dzieje się cały czas."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Rzeczą, którą możemy unikalnie robić w Archiwum Anny, jest przechowywanie wielu kopii dzieł, na dużą skalę. Możemy zbierać artykuły, książki, magazyny i więcej, i dystrybuować je masowo. Obecnie robimy to przez torrenty, ale dokładne technologie nie mają znaczenia i będą się zmieniać z czasem. Ważne jest, aby wiele kopii było dystrybuowanych na całym świecie. Ten cytat sprzed ponad 200 lat wciąż jest aktualny:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>To, co utracone, nie może być odzyskane; ale ocalmy to, co pozostało: nie przez skarbce i zamki, które chronią je przed okiem publicznym i użyciem, skazując je na zapomnienie, ale przez takie rozmnożenie kopii, które umieści je poza zasięgiem przypadku.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Krótka uwaga na temat domeny publicznej. Ponieważ Archiwum Anny koncentruje się unikalnie na działaniach, które są nielegalne w wielu miejscach na świecie, nie zajmujemy się szeroko dostępnymi zbiorami, takimi jak książki z domeny publicznej. Legalne podmioty często już dobrze się tym zajmują. Jednak istnieją względy, które czasami sprawiają, że pracujemy nad publicznie dostępnymi zbiorami:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Rekordy metadata można swobodnie przeglądać na stronie Worldcat, ale nie można ich pobrać masowo (dopóki ich nie <a %(worldcat_scrape)s>zeskrobaliśmy</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kod może być open source na Githubie, ale Github jako całość nie może być łatwo zmirrorowany i tym samym zachowany (choć w tym konkretnym przypadku istnieją wystarczająco rozproszone kopie większości repozytoriów kodu)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit jest darmowy w użyciu, ale niedawno wprowadził surowe środki przeciwko skrobaniu danych, w obliczu głodnych danych szkoleń LLM (więcej o tym później)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Rozmnożenie kopii"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Wracając do naszego pierwotnego pytania: jak możemy twierdzić, że zachowujemy nasze zbiory na zawsze? Głównym problemem jest to, że nasza kolekcja <a %(torrents_stats)s>rośnie</a> w szybkim tempie, poprzez skrobanie i otwarte źródła niektórych ogromnych zbiorów (oprócz niesamowitej pracy już wykonanej przez inne biblioteki cieni z otwartymi danymi, takie jak Sci-Hub i Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Ten wzrost danych utrudnia mirrorowanie zbiorów na całym świecie. Przechowywanie danych jest drogie! Ale jesteśmy optymistyczni, zwłaszcza obserwując następujące trzy trendy."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Zebraliśmy nisko wiszące owoce"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "To wynika bezpośrednio z naszych priorytetów omówionych powyżej. Wolimy najpierw pracować nad uwolnieniem dużych zbiorów. Teraz, gdy zabezpieczyliśmy niektóre z największych zbiorów na świecie, spodziewamy się, że nasz wzrost będzie znacznie wolniejszy."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Wciąż istnieje długa lista mniejszych kolekcji, a nowe książki są skanowane lub publikowane każdego dnia, ale tempo prawdopodobnie będzie znacznie wolniejsze. Możemy jeszcze podwoić lub nawet potroić naszą wielkość, ale w dłuższym okresie czasu."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Koszty przechowywania nadal spadają wykładniczo"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Na moment pisania tego tekstu <a %(diskprices)s>ceny dysków</a> na TB wynoszą około 12 dolarów za nowe dyski, 8 dolarów za używane dyski i 4 dolary za taśmę. Jeśli będziemy konserwatywni i spojrzymy tylko na nowe dyski, oznacza to, że przechowywanie petabajta kosztuje około 12 000 dolarów. Jeśli założymy, że nasza biblioteka potroi się z 900 TB do 2,7 PB, oznaczałoby to 32 400 dolarów na mirrorowanie całej naszej biblioteki. Dodając koszty energii elektrycznej, inne koszty sprzętu i tak dalej, zaokrąglijmy to do 40 000 dolarów. Lub z taśmą bardziej jak 15 000–20 000 dolarów."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Z jednej strony <strong>15 000–40 000 dolarów za sumę całej ludzkiej wiedzy to okazja</strong>. Z drugiej strony, to trochę dużo, aby oczekiwać mnóstwa pełnych kopii, zwłaszcza jeśli chcielibyśmy, aby ci ludzie nadal udostępniali swoje torrenty dla dobra innych."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "To jest dzisiaj. Ale postęp idzie naprzód:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Koszty dysków twardych na TB zostały mniej więcej zmniejszone o jedną trzecią w ciągu ostatnich 10 lat i prawdopodobnie będą nadal spadać w podobnym tempie. Taśmy wydają się podążać podobną trajektorią. Ceny SSD spadają jeszcze szybciej i mogą przejąć ceny HDD do końca dekady."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Trendy cenowe HDD z różnych źródeł (kliknij, aby zobaczyć badanie)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Jeśli to się utrzyma, to za 10 lat możemy patrzeć na jedynie 5 000–13 000 dolarów na mirrorowanie całej naszej kolekcji (1/3), a nawet mniej, jeśli mniej urośniemy. Chociaż to wciąż dużo pieniędzy, będzie to osiągalne dla wielu ludzi. A może być jeszcze lepiej z powodu następnego punktu…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Poprawa gęstości informacji"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Obecnie przechowujemy książki w surowych formatach, w jakich są nam dostarczane. Oczywiście są one skompresowane, ale często są to nadal duże skany lub fotografie stron."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Do tej pory jedynymi opcjami zmniejszenia całkowitego rozmiaru naszej kolekcji były bardziej agresywna kompresja lub deduplikacja. Jednak, aby uzyskać wystarczająco duże oszczędności, obie są zbyt stratne dla naszego gustu. Mocna kompresja zdjęć może sprawić, że tekst będzie ledwo czytelny. A deduplikacja wymaga dużej pewności, że książki są dokładnie takie same, co często jest zbyt niedokładne, zwłaszcza jeśli treści są takie same, ale skany wykonano w różnych okolicznościach."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Zawsze istniała trzecia opcja, ale jej jakość była tak niska, że nigdy jej nie rozważaliśmy: <strong>OCR, czyli optyczne rozpoznawanie znaków</strong>. Jest to proces konwersji zdjęć na zwykły tekst, przy użyciu AI do wykrywania znaków na zdjęciach. Narzędzia do tego istnieją od dawna i były całkiem przyzwoite, ale „całkiem przyzwoite” nie wystarcza do celów archiwizacji."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Jednak ostatnie modele głębokiego uczenia multimodalnego zrobiły niezwykle szybki postęp, choć nadal przy wysokich kosztach. Oczekujemy, że zarówno dokładność, jak i koszty znacznie się poprawią w nadchodzących latach, do tego stopnia, że stanie się to realistyczne do zastosowania w całej naszej bibliotece."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Poprawa OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Kiedy to nastąpi, prawdopodobnie nadal będziemy przechowywać oryginalne pliki, ale dodatkowo moglibyśmy mieć znacznie mniejszą wersję naszej biblioteki, którą większość ludzi będzie chciała mirrorować. Kluczowe jest to, że surowy tekst sam w sobie kompresuje się jeszcze lepiej i jest znacznie łatwiejszy do deduplikacji, co daje nam jeszcze większe oszczędności."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Ogólnie rzecz biorąc, nie jest nierealistyczne oczekiwać co najmniej 5-10-krotnego zmniejszenia całkowitego rozmiaru plików, a może nawet więcej. Nawet przy konserwatywnym 5-krotnym zmniejszeniu, patrzylibyśmy na <strong>1 000–3 000 dolarów za 10 lat, nawet jeśli nasza biblioteka potroi się</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Krytyczne okno"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Jeśli te prognozy są dokładne, <strong>wystarczy poczekać kilka lat</strong>, zanim cała nasza kolekcja będzie szeroko mirrorowana. W ten sposób, jak powiedział Thomas Jefferson, „umieszczona poza zasięgiem przypadku”."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Niestety, pojawienie się LLM i ich głodnego danych treningu, postawiło wielu posiadaczy praw autorskich w defensywie. Jeszcze bardziej niż już byli. Wiele stron internetowych utrudnia skrobanie i archiwizację, toczą się procesy sądowe, a tymczasem fizyczne biblioteki i archiwa nadal są zaniedbywane."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Możemy się spodziewać, że te trendy będą się pogarszać, a wiele dzieł zostanie utraconych, zanim wejdą do domeny publicznej."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Jesteśmy u progu rewolucji w dziedzinie zachowywania, ale <q>utraconego nie można odzyskać.</q></strong> Mamy krytyczne okno czasowe około 5-10 lat, w którym prowadzenie biblioteki cieni i tworzenie wielu lustrzanych kopii na całym świecie jest jeszcze dość kosztowne, a dostęp nie został jeszcze całkowicie zamknięty."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Jeśli uda nam się przetrwać to okno czasowe, to rzeczywiście zachowamy wiedzę i kulturę ludzkości na zawsze. Nie powinniśmy pozwolić, aby ten czas się zmarnował. Nie powinniśmy pozwolić, aby to krytyczne okno się zamknęło."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Chodźmy."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ekskluzywny dostęp dla firm LLM do największej na świecie kolekcji chińskich książek lit. faktu"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Wersja chińska 中文版</a>, <a %(news_ycombinator)s>Dyskutuj na Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Archiwum Anny zdobyło unikalną kolekcję 7,5 miliona / 350TB chińskich książek lit. faktu — większą niż Library Genesis. Jesteśmy gotowi dać firmie LLM ekskluzywny dostęp w zamian za wysokiej jakości OCR i ekstrakcję tekstu.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "To krótki post na blogu. Szukamy firmy lub instytucji, która pomoże nam w OCR i ekstrakcji tekstu dla ogromnej kolekcji, którą zdobyliśmy, w zamian za ekskluzywny wczesny dostęp. Po okresie embarga oczywiście udostępnimy całą kolekcję."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Wysokiej jakości teksty akademickie są niezwykle przydatne do szkolenia LLM. Chociaż nasza kolekcja jest chińska, powinna być przydatna nawet do szkolenia angielskich LLM: modele wydają się kodować koncepcje i wiedzę niezależnie od języka źródłowego."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Do tego tekst musi być wyodrębniony ze skanów. Co zyskuje Archiwum Anny? Pełnotekstowe przeszukiwanie książek dla swoich użytkowników."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Ponieważ nasze cele są zgodne z celami deweloperów LLM, szukamy współpracownika. Jesteśmy gotowi dać Ci <strong>ekskluzywny wczesny dostęp do tej kolekcji w całości na 1 rok</strong>, jeśli możesz przeprowadzić odpowiednie OCR i ekstrakcję tekstu. Jeśli jesteś gotów podzielić się z nami całym kodem swojego pipeline'u, bylibyśmy skłonni przedłużyć embargo na kolekcję."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Przykładowe strony"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Aby udowodnić nam, że masz dobry pipeline, oto kilka przykładowych stron do rozpoczęcia, z książki o nadprzewodnikach. Twój pipeline powinien prawidłowo obsługiwać matematykę, tabele, wykresy, przypisy itp."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Wyślij przetworzone strony na nasz adres e-mail. Jeśli będą wyglądały dobrze, wyślemy Ci więcej prywatnie i oczekujemy, że będziesz w stanie szybko uruchomić na nich swój pipeline. Gdy będziemy zadowoleni, możemy zawrzeć umowę."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Kolekcja"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Kilka dodatkowych informacji o kolekcji. <a %(duxiu)s>Duxiu</a> to ogromna baza danych zeskanowanych książek, stworzona przez <a %(chaoxing)s>SuperStar Digital Library Group</a>. Większość to książki akademickie, zeskanowane w celu udostępnienia ich cyfrowo uniwersytetom i bibliotekom. Dla naszej anglojęzycznej publiczności, <a %(library_princeton)s>Princeton</a> i <a %(guides_lib_uw)s>University of Washington</a> mają dobre przeglądy. Istnieje również doskonały artykuł, który daje więcej tła: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (znajdź go w Archiwum Anny)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Książki z Duxiu od dawna są piratowane w chińskim internecie. Zazwyczaj są sprzedawane przez resellerów za mniej niż dolara. Zwykle są dystrybuowane za pomocą chińskiego odpowiednika Google Drive, który często jest hakowany, aby umożliwić większą przestrzeń dyskową. Niektóre szczegóły techniczne można znaleźć <a %(github_duty_machine)s>tutaj</a> i <a %(github_821_github_io)s>tutaj</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Chociaż książki były półpublicznie dystrybuowane, dość trudno jest je zdobyć w dużych ilościach. Mieliśmy to wysoko na naszej liście rzeczy do zrobienia i przeznaczyliśmy na to kilka miesięcy pełnoetatowej pracy. Jednak niedawno niesamowity, zdolny i utalentowany wolontariusz skontaktował się z nami, mówiąc, że wykonał już całą tę pracę — za wielką cenę. Podzielił się z nami pełną kolekcją, nie oczekując niczego w zamian, poza gwarancją długoterminowego zachowania. Naprawdę niezwykłe. Zgodził się poprosić o pomoc w ten sposób, aby uzyskać kolekcję OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Kolekcja to 7 543 702 pliki. To więcej niż lit. faktu w Library Genesis (około 5,3 miliona). Całkowity rozmiar plików to około 359TB (326TiB) w obecnej formie."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Jesteśmy otwarci na inne propozycje i pomysły. Po prostu skontaktuj się z nami. Sprawdź Archiwum Anny, aby uzyskać więcej informacji o naszych kolekcjach, wysiłkach na rzecz zachowania i jak możesz pomóc. Dziękujemy!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Uwaga: ten wpis na blogu został wycofany. Zdecydowaliśmy, że IPFS nie jest jeszcze gotowy na główny czas. Nadal będziemy linkować do plików na IPFS z Archiwum Anny, gdy to możliwe, ale nie będziemy już ich sami hostować, ani nie zalecamy innym mirrorowania za pomocą IPFS. Zobacz naszą stronę Torrents, jeśli chcesz pomóc w zachowaniu naszej kolekcji."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Pomóż seedować Z-Library na IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Jak prowadzić bibliotekę cieni: operacje w Archiwum Anny"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nie ma <q>AWS dla charytatywnych organizacji cieni,</q> więc jak prowadzimy Archiwum Anny?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Prowadzę <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największą na świecie otwartoźródłową, non-profit wyszukiwarkę dla <a %(wikipedia_shadow_library)s>bibliotek cieni</a>, takich jak Sci-Hub, Library Genesis i Z-Library. Naszym celem jest uczynienie wiedzy i kultury łatwo dostępnymi, a ostatecznie zbudowanie społeczności ludzi, którzy razem archiwizują i zachowują <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>wszystkie książki na świecie</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "W tym artykule pokażę, jak prowadzimy tę stronę internetową i jakie unikalne wyzwania wiążą się z prowadzeniem strony o wątpliwym statusie prawnym, ponieważ nie ma „AWS dla charytatywnych organizacji cieni”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Sprawdź także artykuł siostrzany <a %(blog_how_to_become_a_pirate_archivist)s>Jak zostać pirackim archiwistą</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokeny innowacji"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Zacznijmy od naszego stosu technologicznego. Jest celowo nudny. Używamy Flask, MariaDB i ElasticSearch. I to dosłownie wszystko. Wyszukiwanie to w dużej mierze rozwiązany problem i nie zamierzamy go na nowo wymyślać. Poza tym musimy wydać nasze <a %(mcfunley)s>żetony innowacji</a> na coś innego: nie dać się zlikwidować przez władze."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Jak legalne lub nielegalne jest dokładnie Archiwum Anny? To w dużej mierze zależy od jurysdykcji prawnej. Większość krajów wierzy w jakąś formę praw autorskich, co oznacza, że ludziom lub firmom przyznaje się wyłączny monopol na określone rodzaje dzieł na określony czas. Na marginesie, w Archiwum Anny wierzymy, że choć istnieją pewne korzyści, to ogólnie prawa autorskie są negatywne dla społeczeństwa — ale to historia na inny czas."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ten wyłączny monopol na określone dzieła oznacza, że nielegalne jest bezpośrednie rozpowszechnianie tych dzieł przez kogokolwiek spoza tego monopolu — w tym przez nas. Ale Archiwum Anny jest wyszukiwarką, która nie rozpowszechnia bezpośrednio tych dzieł (przynajmniej nie na naszej stronie w sieci otwartej), więc powinniśmy być w porządku, prawda? Nie do końca. W wielu jurysdykcjach nie tylko nielegalne jest rozpowszechnianie chronionych prawem autorskim dzieł, ale także linkowanie do miejsc, które to robią. Klasycznym przykładem jest amerykańskie prawo DMCA."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "To najostrzejszy koniec spektrum. Na drugim końcu spektrum teoretycznie mogą istnieć kraje bez jakichkolwiek praw autorskich, ale takie kraje praktycznie nie istnieją. Prawie każdy kraj ma jakieś prawo autorskie w swoich przepisach. Egzekwowanie to inna historia. Istnieje wiele krajów, których rządy nie dbają o egzekwowanie prawa autorskiego. Są też kraje pomiędzy tymi dwoma skrajnościami, które zakazują rozpowszechniania chronionych prawem autorskim dzieł, ale nie zakazują linkowania do takich dzieł."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Innym czynnikiem jest poziom firmy. Jeśli firma działa w jurysdykcji, która nie dba o prawa autorskie, ale sama firma nie chce podejmować żadnego ryzyka, to mogą zamknąć twoją stronę internetową, gdy tylko ktoś się na nią poskarży."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Ostatecznie, dużym czynnikiem są płatności. Ponieważ musimy pozostać anonimowi, nie możemy korzystać z tradycyjnych metod płatności. Pozostają nam kryptowaluty, a tylko niewielka część firm je obsługuje (istnieją wirtualne karty debetowe opłacane kryptowalutami, ale często nie są akceptowane)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architektura systemu"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Załóżmy więc, że znalazłeś kilka firm, które są gotowe hostować twoją stronę bez zamykania jej — nazwijmy je „dostawcami kochającymi wolność” 😄. Szybko odkryjesz, że hostowanie wszystkiego u nich jest dość kosztowne, więc możesz chcieć znaleźć „taniego dostawcę” i tam faktycznie hostować, przekierowując przez dostawców kochających wolność. Jeśli zrobisz to dobrze, tani dostawcy nigdy nie będą wiedzieć, co hostujesz, i nigdy nie otrzymają żadnych skarg."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Przy wszystkich tych dostawcach istnieje ryzyko, że i tak cię zamkną, więc potrzebujesz również redundancji. Potrzebujemy tego na wszystkich poziomach naszego stosu."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Jedną z firm, która kocha wolność i postawiła się w interesującej pozycji, jest Cloudflare. Twierdzili, że nie są dostawcą hostingu, ale usługą, jak ISP. Dlatego nie podlegają DMCA ani innym żądaniom usunięcia, a wszelkie żądania przekazują do rzeczywistego dostawcy hostingu. Posunęli się nawet do sądu, aby chronić tę strukturę. Możemy więc używać ich jako kolejnej warstwy buforowania i ochrony."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nie akceptuje anonimowych płatności, więc możemy korzystać tylko z ich darmowego planu. Oznacza to, że nie możemy korzystać z ich funkcji równoważenia obciążenia ani przełączania awaryjnego. Dlatego <a %(annas_archive_l255)s>zaimplementowaliśmy to sami</a> na poziomie domeny. Przy ładowaniu strony przeglądarka sprawdzi, czy bieżąca domena jest nadal dostępna, a jeśli nie, przepisze wszystkie adresy URL na inną domenę. Ponieważ Cloudflare buforuje wiele stron, oznacza to, że użytkownik może trafić na naszą główną domenę, nawet jeśli serwer proxy jest wyłączony, a następnie przy następnym kliknięciu zostać przeniesiony na inną domenę."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Mamy również normalne obawy operacyjne, takie jak monitorowanie kondycji serwera, rejestrowanie błędów backendu i frontendu i tak dalej. Nasza architektura przełączania awaryjnego pozwala na większą odporność również w tym zakresie, na przykład poprzez uruchamianie zupełnie innego zestawu serwerów na jednej z domen. Możemy nawet uruchamiać starsze wersje kodu i Datasets na tej oddzielnej domenie, na wypadek gdyby krytyczny błąd w głównej wersji pozostał niezauważony."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Możemy również zabezpieczyć się przed odwróceniem się Cloudflare od nas, usuwając go z jednej z domen, na przykład z tej oddzielnej domeny. Możliwe są różne permutacje tych pomysłów."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Narzędzia"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Przyjrzyjmy się, jakich narzędzi używamy, aby to wszystko osiągnąć. To bardzo się rozwija, gdy napotykamy nowe problemy i znajdujemy nowe rozwiązania."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Serwer aplikacji: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Serwer proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Zarządzanie serwerem: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Rozwój: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Statyczne hostowanie Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Istnieją pewne decyzje, nad którymi się wahaliśmy. Jedną z nich jest komunikacja między serwerami: kiedyś używaliśmy do tego Wireguard, ale okazało się, że czasami przestaje on przesyłać jakiekolwiek dane lub przesyła je tylko w jednym kierunku. Działo się to w przypadku kilku różnych konfiguracji Wireguard, które próbowaliśmy, takich jak <a %(github_costela_wesher)s>wesher</a> i <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Próbowaliśmy również tunelować porty przez SSH, używając autossh i sshuttle, ale napotkaliśmy <a %(github_sshuttle)s>problemy</a> (choć nadal nie jest dla mnie jasne, czy autossh cierpi na problemy TCP-over-TCP, czy nie — po prostu wydaje mi się to niepewnym rozwiązaniem, ale może jest w porządku?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Zamiast tego wróciliśmy do bezpośrednich połączeń między serwerami, ukrywając, że serwer działa na tanich dostawcach, używając filtrowania IP z UFW. Ma to tę wadę, że Docker nie działa dobrze z UFW, chyba że użyjesz <code>network_mode: \"host\"</code>. Wszystko to jest nieco bardziej podatne na błędy, ponieważ przy najmniejszej błędnej konfiguracji narażasz swój serwer na internet. Może powinniśmy wrócić do autossh — wszelkie opinie byłyby bardzo mile widziane."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Również wahaliśmy się między Varnish a Nginx. Obecnie preferujemy Varnish, ale ma on swoje kaprysy i ostre krawędzie. To samo dotyczy Checkmk: nie jesteśmy nim zachwyceni, ale na razie działa. Weblate jest w porządku, ale nie jest niesamowity — czasami obawiam się, że straci moje dane, gdy próbuję je zsynchronizować z naszym repozytorium git. Flask ogólnie był dobry, ale ma pewne dziwne kaprysy, które kosztowały dużo czasu na debugowanie, takie jak konfigurowanie niestandardowych domen czy problemy z integracją SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Jak dotąd inne narzędzia były świetne: nie mamy poważnych skarg na MariaDB, ElasticSearch, Gitlab, Zulip, Docker i Tor. Wszystkie miały pewne problemy, ale nic zbyt poważnego ani czasochłonnego."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Podsumowanie"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "To było interesujące doświadczenie, aby nauczyć się, jak skonfigurować solidną i odporną wyszukiwarkę biblioteki cieni. Jest mnóstwo więcej szczegółów do podzielenia się w późniejszych postach, więc daj mi znać, o czym chciałbyś się dowiedzieć więcej!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Jak zawsze, szukamy darowizn, aby wesprzeć tę pracę, więc koniecznie odwiedź stronę Darowizny w Archiwum Anny. Szukamy również innych form wsparcia, takich jak dotacje, długoterminowi sponsorzy, dostawcy płatności wysokiego ryzyka, a może nawet (gustowne!) reklamy. A jeśli chcesz poświęcić swój czas i umiejętności, zawsze szukamy deweloperów, tłumaczy i innych. Dziękujemy za zainteresowanie i wsparcie."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna i zespół (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Cześć, jestem Anna. Stworzyłam <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największą na świecie bibliotekę cieni. To mój osobisty blog, na którym ja i moi współpracownicy piszemy o piractwie, cyfrowej archiwizacji i nie tylko."

#, fuzzy
msgid "blog.index.text2"
msgstr "Połącz się ze mną na <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Zauważ, że ta strona to tylko blog. Hostujemy tutaj tylko nasze własne słowa. Nie hostujemy ani nie linkujemy tutaj żadnych torrentów ani innych plików objętych prawami autorskimi."

#, fuzzy
msgid "blog.index.heading"
msgstr "Posty na blogu"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 miliarda zeskrobań WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Umieszczanie 5 998 794 książek na IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Uwaga: ten wpis na blogu został wycofany. Zdecydowaliśmy, że IPFS nie jest jeszcze gotowy na główny czas. Nadal będziemy linkować do plików na IPFS z Archiwum Anny, gdy to możliwe, ale nie będziemy już ich sami hostować, ani nie zalecamy innym mirrorowania za pomocą IPFS. Zobacz naszą stronę Torrents, jeśli chcesz pomóc w zachowaniu naszej kolekcji."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Archiwum Anny zeskrobało cały WorldCat (największą na świecie kolekcję metadata bibliotecznych), aby stworzyć listę TODO książek, które trzeba zachować.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Rok temu <a %(blog)s>zaczęliśmy</a> odpowiadać na to pytanie: <strong>Jaki procent książek został trwale zachowany przez biblioteki cieni?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Gdy książka trafia do biblioteki cieni z otwartymi danymi, takiej jak <a %(wikipedia_library_genesis)s>Library Genesis</a>, a teraz <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, jest kopiowana na całym świecie (przez torrenty), co praktycznie zapewnia jej zachowanie na zawsze."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Aby odpowiedzieć na pytanie, jaki procent książek został zachowany, musimy znać mianownik: ile książek istnieje w sumie? I najlepiej, abyśmy mieli nie tylko liczbę, ale rzeczywiste metadata. Wtedy możemy nie tylko porównać je z bibliotekami cieni, ale także <strong>stworzyć listę TODO pozostałych książek do zachowania!</strong> Moglibyśmy nawet zacząć marzyć o crowdsourcingowym wysiłku, aby przejść przez tę listę TODO."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Zeskrobaliśmy <a %(wikipedia_isbndb_com)s>ISBNdb</a> i pobraliśmy <a %(openlibrary)s>zbiór danych Open Library</a>, ale wyniki były niezadowalające. Głównym problemem był brak dużego nakładania się numerów ISBN. Zobacz ten diagram Venna z <a %(blog)s>naszego wpisu na blogu</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Byliśmy bardzo zaskoczeni, jak mało było nakładania się między ISBNdb a Open Library, które obie hojnie włączają dane z różnych źródeł, takich jak zeskrobywanie stron internetowych i zapisy biblioteczne. Jeśli obie dobrze wykonują swoją pracę w znajdowaniu większości ISBN-ów, ich okręgi z pewnością miałyby znaczne nakładanie się, lub jeden byłby podzbiorem drugiego. Zastanawiało nas, ile książek wypada <em>całkowicie poza te okręgi</em>? Potrzebujemy większej bazy danych."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "To wtedy skierowaliśmy nasze spojrzenia na największą bazę danych książek na świecie: <a %(wikipedia_worldcat)s>WorldCat</a>. Jest to własnościowa baza danych prowadzona przez non-profit <a %(wikipedia_oclc)s>OCLC</a>, która agreguje rekordy metadata z bibliotek na całym świecie, w zamian za udostępnienie tym bibliotekom pełnego zbioru danych i umożliwienie im pojawiania się w wynikach wyszukiwania użytkowników końcowych."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Mimo że OCLC jest organizacją non-profit, ich model biznesowy wymaga ochrony ich bazy danych. Cóż, przykro nam to mówić, przyjaciele z OCLC, ale udostępniamy wszystko. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "W ciągu ostatniego roku skrupulatnie zeskrobaliśmy wszystkie rekordy WorldCat. Na początku mieliśmy szczęście. WorldCat właśnie wprowadzał kompletny redesign swojej strony internetowej (w sierpniu 2022). Obejmowało to znaczne przekształcenie ich systemów zaplecza, wprowadzając wiele luk w zabezpieczeniach. Natychmiast skorzystaliśmy z okazji i byliśmy w stanie zeskrobać setki milionów (!) rekordów w zaledwie kilka dni."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redesign WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Po tym, luki w zabezpieczeniach były powoli naprawiane jedna po drugiej, aż ostatnia, którą znaleźliśmy, została załatana około miesiąc temu. Do tego czasu mieliśmy już praktycznie wszystkie rekordy i dążyliśmy jedynie do nieco wyższej jakości rekordów. Więc uznaliśmy, że czas na wydanie!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Przyjrzyjmy się kilku podstawowym informacjom o danych:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Kontenery Archiwum Anny (AAC)</a>, które są w zasadzie <a %(jsonlines)s>JSON Lines</a> skompresowane za pomocą <a %(zstd)s>Zstandard</a>, plus pewne znormalizowane semantyki. Te kontenery zawierają różne typy rekordów, w oparciu o różne zeskrobywania, które wdrożyliśmy."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dane"

msgid "dyn.buy_membership.error.unknown"
msgstr "Wystąpił nieznany błąd. Prosimy o kontakt pod adresem %(email)s i załączenie zrzutu ekranu."

msgid "dyn.buy_membership.error.minimum"
msgstr "Ta kryptowaluta ma wyższe niż zwykle minimum. Wybierz inny czas trwania lub inną kryptowalutę."

msgid "dyn.buy_membership.error.try_again"
msgstr "Żądanie nie mogło zostać ukończone. Spróbuj ponownie za kilka minut, a jeśli nadal będzie się to powtarzać, skontaktuj się z nami pod adresem %(email)s załączając zrzut ekranu."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Błąd w przetwarzaniu płatności. Proszę poczekać chwilę i spróbować ponownie. Jeśli problem będzie się utrzymywał przez ponad 24 godziny, prosimy o kontakt na %(email)s z zrzutem ekranu."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "ukryty komentarz"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problem z plikiem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Lepsza wersja"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Czy chcesz zgłosić tego użytkownika za obraźliwe lub nieodpowiednie zachowanie?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Zgłoś nadużycie"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Nadużycie zgłoszone:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Zgłosiłeś tego użytkownika za nadużycie."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Odpowiedz"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Proszę <a %(a_login)s>zaloguj się</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Zostawiłeś komentarz. Może minąć chwila, zanim się pojawi."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Coś poszło nie tak. Proszę odświeżyć stronę i spróbować ponownie."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s dotkniętych stron"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Brak w Library Genesis \"libgen.rs\" (lit. faktu)"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Brak w Library Genesis \"libgen.rs\" (beletrystyka)"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Brak w Library Genesis \"libgen.li\""

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Oznaczone jako nieprawidłowe w Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Brak w Z-library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Oznaczone jako „spam” w Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Oznaczone jako „zły plik” w Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nie wszystkie strony mogły zostać przekonwertowane na PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Uruchomienie exiftool nie powiodło się na tym pliku"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Książka (nieznana)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Książka (literatura faktu)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Książka (beletrystyka)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artykuł z literatury fachowej"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Dokument dotyczący standardu"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Czasopismo"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komiks"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partytury muzyczne"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiobook"

msgid "common.md5_content_type_mapping.other"
msgstr "Pozostałe"

msgid "common.access_types_mapping.aa_download"
msgstr "Partnerski serwer pobierania"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Pobieranie zewnętrzne"

msgid "common.access_types_mapping.external_borrow"
msgstr "Wypożyczanie zewnętrzne"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Wypożyczanie zewnętrzne (osoby z zaburzeniami widzenia)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Przeglądaj metadane"

msgid "common.access_types_mapping.torrents_available"
msgstr "Zawarte w torrentach"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Przesyłki do AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Indeks eBooków EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Czeskie metadane"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Rosyjska Biblioteka Państwowa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Tytuł"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Wydawca"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edycja"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Rok wydania"

msgid "common.specific_search_fields.original_filename"
msgstr "Oryginalna nazwa pliku"

msgid "common.specific_search_fields.description_comments"
msgstr "Komentarze metadanych"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Pobieranie przez serwery partnerskie chwilowo nie jest dostępne dla tego pliku."

msgid "common.md5.servers.fast_partner"
msgstr "Szybki serwer partnera #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(polecane)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(brak weryfikacji przeglądarki ani list oczekujących)"

msgid "common.md5.servers.slow_partner"
msgstr "Wolny serwer partnera #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(nieco szybciej, ale z listą oczekujących)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(brak listy oczekujących, ale może być bardzo wolno)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Literatura faktu"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Beletrystyka"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(kliknij również „GET” u góry)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(kliknij \"GET\" u góry)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "ich reklamy są znane z zawierania złośliwego oprogramowania, więc używaj blokera reklam lub nie klikaj reklam"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Pliki Nexus/STC mogą być niewiarygodne do pobrania)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library (poprzez Tor)"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(wymaga Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Wypożycz z Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(tylko dla osób z zaburzeniami widzenia)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(zawarte DOI może nie być dostępne w \"Sci-Hub\")"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "kolekcja"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Pobieranie masowe za pomocą protokołu BitTorrent"

msgid "page.md5.box.download.experts_only"
msgstr "(wyłącznie dla ekspertów)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Przeszukaj ISBN w Anna’s Archive"

msgid "page.md5.box.download.other_isbn"
msgstr "Przeszukaj ISBN w innych bazach danych"

msgid "page.md5.box.download.original_isbndb"
msgstr "Znajdź oryginalny rekord w ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Przeszukaj Open Library ID w Anna’s Archive"

msgid "page.md5.box.download.original_openlib"
msgstr "Znajdź oryginalny rekord w Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Wyszukaj numer OCLC (WorldCat) w Anna’s Archive"

msgid "page.md5.box.download.original_oclc"
msgstr "Znajdź oryginalny rekord w WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Szukaj w Archiwum Anny numeru SSID DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Szukaj ręcznie na DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Wyszukaj w Archiwum Anny numer SSNO CADAL"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Znajdź oryginalny rekord w CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Wyszukaj w Archiwum Anny numer DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Indeks eBooków EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(weryfikacja przeglądarki niewymagana)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Czeskie metadane %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadane"

msgid "page.md5.box.descr_title"
msgstr "opis"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternatywna nazwa pliku"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternatywny tytuł"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternatywny autor"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternatywny wydawca"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternatywne wydanie"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternatywne rozszerzenie"

msgid "page.md5.box.metadata_comments_title"
msgstr "komentarze metadanych"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternatywny opis"

msgid "page.md5.box.date_open_sourced_title"
msgstr "data uwolnienia"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub “%(id)s” pliku"

msgid "page.md5.header.ia"
msgstr "“%(id)s” pliku Internet Archive Controlled Digital Lending"

msgid "page.md5.header.ia_desc"
msgstr "To jest rekord pliku z Internet Archive, a nie plik do bezpośredniego pobrania. Możesz spróbować wypożyczyć książkę (link poniżej) lub użyć tego adresu podczas <a %(a_request)s>żądania pliku</a>."

msgid "page.md5.header.consider_upload"
msgstr "Jeśli posiadasz ten plik, a nie jest on dostępny w Anna's Archive, sugerujemy <a %(a_request)s>wrzucenie go</a>."

msgid "page.md5.header.meta_isbn"
msgstr "%(id)s rekordu metadanych ISBNdb"

msgid "page.md5.header.meta_openlib"
msgstr "%(id)s rekordu metadanych Open Library"

msgid "page.md5.header.meta_oclc"
msgstr "%(id)s rekordu metadanych OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Rekord metadanych SSID DuXiu %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Rekord metadanych CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Rekord metadanych MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Rekord metadanych Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Jest to rekord metadanych, a nie plik do pobrania. Możesz użyć tego adresu, gdy chcesz przesłać<a %(a_request)s>żądanie pliku</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadane z powiązanego rekordu"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Popraw metadane w Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Ostrzeżenie: wiele powiązanych rekordów:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Popraw metadane"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Zgłoś jakość pliku"

msgid "page.search.results.download_time"
msgstr "Czas pobierania"

msgid "page.md5.codes.url"
msgstr "Link:"

msgid "page.md5.codes.website"
msgstr "Strona:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Szukanie frazy \"%(name)s\" w Anna's Archive"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Eksplorator kodów:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Zobacz w Eksploratorze kodów „%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Więcej…"

msgid "page.md5.tabs.downloads"
msgstr "(%(count)s) Pobrań"

msgid "page.md5.tabs.borrow"
msgstr "(%(count)s) wypożyczeń"

msgid "page.md5.tabs.explore_metadata"
msgstr "(%(count)s) przeszukiwań metadanych"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentarze (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "(%(count)s) list"

msgid "page.md5.tabs.stats"
msgstr "(%(count)s) statystyk"

msgid "common.tech_details"
msgstr "Szczegóły techniczne"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Ten plik może mieć problemy i został ukryty w jednej z bibliotek.</span> Czasem jest to spowodowane żądaniem właściciela praw autorskich, czasem dostępnością lepszej alternatywy, a czasem jest to spowodowane problemem z samym plikiem. Być może dalej nadaje się do pobrania, ale polecamy najpierw poszukać alternatywnego pliku. Więcej szczegółów:"

msgid "page.md5.box.download.better_file"
msgstr "Lepsza wersja tego pliku może być dostępna pod adresem %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Jeśli mimo tego chcesz pobrać ten plik, upewnij się, by otwierać go tylko zaufanym, zaktualizowanym oprogramowaniem."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Szybkie pobieranie"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong> 🚀 Szybkie pobieranie</strong> Zostań <a %(a_membership)s>członkiem</a>, aby wesprzeć utrwalanie książek, prac naukowych i innych w długofalowym procesie. Aby okazać ci naszą wdzięczność za pomoc, otrzymasz dostęp do szybkich serwerów. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Jeśli wpłacisz darowiznę w tym miesiącu, otrzymasz <strong>podwójną</strong> liczbę szybkich pobrań."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong> 🚀 Szybkie pobieranie</strong> Pozostało Ci %(remaining)s w dniu dzisiejszym. Dziękujemy za Twoje członkostwo! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Szybkie pobieranie</strong> Wyczerpałeś limit szybkich pobrań na dziś."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong> 🚀 Szybkie pobierania</strong> Plik został pobrany niedawno. Odnośnik pozostanie aktywny przez chwilę."

msgid "page.md5.box.download.option"
msgstr "Opcja #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(bez przekierowania)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(otwórz w przeglądarce)"

msgid "layout.index.header.banner.refer"
msgstr "Poleć znajomego, a oboje otrzymacie %(percentage)s%% dodatkowych szybkich pobrań!"

msgid "layout.index.header.learn_more"
msgstr "Dowiedz się więcej…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Wolne pobieranie"

msgid "page.md5.box.download.trusted_partners"
msgstr "Od zaufanych partnerów."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Więcej informacji w <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(może wymagać <a %(a_browser)s>weryfikacji przeglądarki</a> —nielimitowane pobieranie!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Po pobraniu:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Otwórz w naszej przeglądarce"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "pokaż zewnętrzne pobierania"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Zewnętrzne pobierania"

msgid "page.md5.box.download.no_found"
msgstr "Nie znaleziono pobierań."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Wszystkie serwery lustrzane obsługują ten sam plik i powinny być bezpieczne w użyciu. To powiedziawszy, zawsze zachowaj ostrożność podczas pobierania plików z Internetu. Na przykład pamiętaj, aby aktualizować swoje urządzenia."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "W przypadku dużych plików zalecamy użycie menedżera pobierania, aby zapobiec przerwom."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Zalecane menedżery pobierania: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Do otwarcia pliku będziesz potrzebować czytnika ebooków lub PDF, w zależności od formatu pliku."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Zalecane czytniki ebooków: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Przeglądarka online Archiwum Anny"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Użyj narzędzi online do konwersji między formatami."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Zalecane narzędzia do konwersji: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Możesz wysyłać zarówno pliki PDF, jak i EPUB na swój czytnik Kindle lub Kobo."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Zalecane narzędzia: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon „Wyślij do Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz „Wyślij do Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Wspieraj autorów i biblioteki"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Jeśli podoba Ci się to i możesz sobie na to pozwolić, rozważ zakup oryginału lub bezpośrednie wsparcie autorów."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Jeśli jest dostępna w Twojej lokalnej bibliotece, rozważ wypożyczenie jej za darmo."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Jakość pliku"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Pomóż społeczności, zgłaszając jakość tego pliku! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Zgłoś problem z plikiem (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Świetna jakość pliku (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Dodaj komentarz (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Co jest nie tak z tym plikiem?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Proszę użyj <a %(a_copyright)s>formularza zgłoszenia DMCA / naruszenia praw autorskich</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Opisz problem (wymagane)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Opis problemu"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 lepszej wersji tego pliku (jeśli dotyczy)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Wypełnij to, jeśli istnieje inny plik, który ściśle odpowiada temu plikowi (ta sama edycja, to samo rozszerzenie pliku, jeśli można go znaleźć), którego ludzie powinni używać zamiast tego pliku. Jeśli znasz lepszą wersję tego pliku poza Archiwum Anny, proszę <a %(a_upload)s>prześlij ją</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Możesz uzyskać md5 z URL, np."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Prześlij zgłoszenie"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Naucz się <a %(a_metadata)s>poprawiać metadane</a> tego pliku samodzielnie."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Dziękujemy za przesłanie raportu. Zostanie on wyświetlony na tej stronie oraz ręcznie sprawdzony przez Annę (dopóki nie będziemy mieli odpowiedniego systemu moderacji)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Coś poszło nie tak. Proszę odświeżyć stronę i spróbować ponownie."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Jeśli ten plik jest wysokiej jakości, możesz tutaj omówić wszystko na jego temat! Jeśli nie, użyj przycisku „Zgłoś problem z plikiem”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Uwielbiam tę książkę!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Zostaw komentarz"

msgid "common.english_only"
msgstr "Kontynuacja tekstu poniżej w języku angielskim."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Łączna liczba pobrań: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "„Plik MD5” to hash, który jest obliczany na podstawie zawartości pliku i jest w miarę unikalny w oparciu o tę zawartość. Wszystkie biblioteki cieni, które tutaj indeksujemy, używają głównie MD5 do identyfikacji plików."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Plik może pojawić się w wielu bibliotekach cieni. Aby uzyskać informacje o różnych datasetach, które skompilowaliśmy, zobacz <a %(a_datasets)s>stronę Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "To jest plik zarządzany przez <a %(a_ia)s>IA’s Controlled Digital Lending</a> i zindeksowany przez Archiwum Anny do wyszukiwania. Aby uzyskać informacje o różnych datasetach, które skompilowaliśmy, zobacz <a %(a_datasets)s>stronę Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Aby uzyskać informacje o tym konkretnym pliku, sprawdź jego <a %(a_href)s>plik JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problem z ładowaniem tej strony"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Odśwież, aby spróbować ponownie. <a %(a_contact)s>Skontaktuj się z nami</a>, jeśli problem będzie się utrzymywał przez kilka godzin."

msgid "page.md5.invalid.header"
msgstr "Nie znaleziono"

msgid "page.md5.invalid.text"
msgstr "Nie znaleziono “%(md5_input)s” w naszej bazie danych."

msgid "page.login.title"
msgstr "Zaloguj / Zarejestruj"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Weryfikacja przeglądarki"

msgid "page.login.text1"
msgstr "Aby zapobiec botom spamującym tworzenie wielu kont, koniecznym jest zweryfikowanie Twojej przeglądarki."

#, fuzzy
msgid "page.login.text2"
msgstr "Jeśli utkniesz w nieskończonej pętli, zalecamy zainstalowanie <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Pomocne może być także wyłączenie programów blokujących reklamy i innych rozszerzeń przeglądarki."

#, fuzzy
msgid "page.codes.title"
msgstr "Kody"

#, fuzzy
msgid "page.codes.heading"
msgstr "Eksplorator Kodów"

#, fuzzy
msgid "page.codes.intro"
msgstr "Eksploruj kody, którymi oznaczone są rekordy, według prefiksu. Kolumna „rekordy” pokazuje liczbę rekordów oznaczonych kodami z danym prefiksem, jak widać w wyszukiwarce (w tym rekordy tylko z metadanymi). Kolumna „kody” pokazuje, ile faktycznych kodów ma dany prefiks."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Ta strona może zająć trochę czasu na wygenerowanie, dlatego wymaga captcha Cloudflare. <a %(a_donate)s>Członkowie</a> mogą pominąć captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Prosimy nie skrobać tych stron. Zamiast tego zalecamy <a %(a_import)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB oraz uruchamianie naszego <a %(a_software)s>otwartego kodu źródłowego</a>. Surowe dane można ręcznie przeglądać za pomocą plików JSON, takich jak <a %(a_json_file)s>ten</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefiks"

#, fuzzy
msgid "common.form.go"
msgstr "Idź"

#, fuzzy
msgid "common.form.reset"
msgstr "Resetuj"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Szukaj w Archiwum Anny"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Ostrzeżenie: kod zawiera nieprawidłowe znaki Unicode i może działać nieprawidłowo w różnych sytuacjach. Surowy binarny można dekodować z reprezentacji base64 w URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Znany prefiks kodu „%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefiks"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etykieta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Opis"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL dla konkretnego kodu"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "„%%s” zostanie zastąpione wartością kodu"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Ogólny URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Strona internetowa"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s rekord pasujący do „%(prefix_label)s”"
msgstr[1] ""
msgstr[2] "%(count)s rekordów pasujących do „%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL dla konkretnego kodu: „%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Więcej…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kody zaczynające się od „%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "rekordy"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kody"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Mniej niż %(count)s rekordów"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "W przypadku roszczeń DMCA / praw autorskich, użyj <a %(a_copyright)s>tego formularza</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Wszelkie inne sposoby kontaktu z nami w sprawie roszczeń dotyczących praw autorskich będą automatycznie usuwane."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Bardzo chętnie przyjmiemy Twoje opinie i pytania!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Jednak ze względu na ilość spamu i bezsensownych e-maili, które otrzymujemy, prosimy o zaznaczenie pól, aby potwierdzić, że rozumiesz te warunki kontaktu z nami."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Roszczenia dotyczące praw autorskich wysłane na ten e-mail będą ignorowane; zamiast tego użyj formularza."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Serwery partnerskie są niedostępne z powodu zamknięcia hostingu. Powinny być wkrótce ponownie dostępne."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Członkostwa zostaną odpowiednio przedłużone."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nie wysyłaj do nas e-maili z prośbą o przesłanie <a %(a_request)s>książek</a><br>lub małych (<10k) <a %(a_upload)s>plików</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Podczas zadawania pytań dotyczących konta lub darowizn, dodaj swój identyfikator konta, zrzuty ekranu, paragony, jak najwięcej informacji. Sprawdzamy nasz e-mail co 1-2 tygodnie, więc brak tych informacji opóźni rozwiązanie problemu."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Pokaż e-mail"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formularz zgłoszenia DMCA / naruszenia praw autorskich"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Jeśli masz zgłoszenie DMCA lub inne zgłoszenie naruszenia praw autorskich, prosimy o wypełnienie tego formularza tak dokładnie, jak to możliwe. W przypadku problemów prosimy o kontakt pod dedykowanym adresem DMCA: %(email)s. Zwróć uwagę, że zgłoszenia wysłane na ten adres e-mail nie będą przetwarzane, jest on przeznaczony wyłącznie do pytań. Prosimy o korzystanie z poniższego formularza do składania zgłoszeń."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "Adresy URL na Archiwum Anny (wymagane). Jeden na linię. Prosimy o uwzględnienie tylko adresów URL opisujących dokładnie tę samą edycję książki. Jeśli chcesz zgłosić roszczenie dotyczące wielu książek lub wielu edycji, prosimy o wielokrotne wypełnienie tego formularza."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Zgłoszenia łączące wiele książek lub edycji będą odrzucane."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Twoje imię i nazwisko (wymagane)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adres (wymagane)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Numer telefonu (wymagane)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (wymagane)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Dokładny opis materiału źródłowego (wymagane)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "Numery ISBN materiału źródłowego (jeśli dotyczy). Jeden na linię. Prosimy o uwzględnienie tylko tych, które dokładnie odpowiadają edycji, dla której zgłaszasz roszczenie dotyczące praw autorskich."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> adresy URL materiału źródłowego, jeden na linię. Prosimy o chwilę na przeszukanie Open Library w celu znalezienia materiału źródłowego. Pomoże nam to zweryfikować Twoje zgłoszenie."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "Adresy URL do materiału źródłowego, jeden na linię (wymagane). Prosimy o uwzględnienie jak największej liczby, aby pomóc nam zweryfikować Twoje zgłoszenie (np. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Oświadczenie i podpis (wymagane)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Złóż zgłoszenie"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Dziękujemy za złożenie zgłoszenia dotyczącego praw autorskich. Przeanalizujemy je tak szybko, jak to możliwe. Prosimy o odświeżenie strony, aby złożyć kolejne zgłoszenie."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Coś poszło nie tak. Prosimy o odświeżenie strony i ponowienie próby."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Jeśli jesteś zainteresowany mirrorowaniem tego zestawu danych do celów <a %(a_archival)s>archiwalnych</a> lub <a %(a_llm)s>szkolenia LLM</a>, prosimy o kontakt."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Naszą misją jest archiwizowanie wszystkich książek na świecie (a także artykułów, magazynów itp.) i udostępnianie ich szeroko. Wierzymy, że wszystkie książki powinny być szeroko mirroringowane, aby zapewnić redundancję i odporność. Dlatego zbieramy pliki z różnych źródeł. Niektóre źródła są całkowicie otwarte i mogą być mirroringowane hurtowo (takie jak Sci-Hub). Inne są zamknięte i chronione, więc staramy się je skrobać, aby „uwolnić” ich książki. Jeszcze inne znajdują się gdzieś pomiędzy."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Wszystkie nasze dane mogą być <a %(a_torrents)s>torrenty</a>, a wszystkie nasze metadane mogą być <a %(a_anna_software)s>generowane</a> lub <a %(a_elasticsearch)s>pobrane</a> jako bazy danych ElasticSearch i MariaDB. Surowe dane można ręcznie przeglądać za pomocą plików JSON, takich jak <a %(a_dbrecord)s>ten</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Przegląd"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Poniżej znajduje się szybki przegląd źródeł plików w Archiwum Anny."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Źródło"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Rozmiar"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% zmirrorowane przez AA / dostępne torrenty"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Procenty liczby plików"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ostatnia aktualizacja"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Lit. faktu i beletrystyka"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s plik"
msgstr[1] ""
msgstr[2] "%(count)s pliki"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Przez Libgen.li „scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: zamrożony od 2021; większość dostępna przez torrenty"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: drobne dodatki od tego czasu</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Z wyłączeniem „scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrenty beletrystyki są opóźnione (choć ID ~4-6M nie są torrentyzowane, ponieważ nakładają się na nasze torrenty Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Kolekcja „chińska” w Z-Library wydaje się być taka sama jak nasza kolekcja DuXiu, ale z różnymi MD5. Wykluczamy te pliki z torrentów, aby uniknąć duplikacji, ale nadal pokazujemy je w naszym indeksie wyszukiwania."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrolowane Wypożyczanie Cyfrowe"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ plików jest przeszukiwalnych."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Razem"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Wykluczanie duplikatów"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Ponieważ biblioteki cieni często synchronizują dane między sobą, istnieje znaczne nakładanie się zasobów między bibliotekami. Dlatego liczby nie sumują się do całości."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Procent „mirrorowane i seedowane przez Archiwum Anny” pokazuje, ile plików mirrorujemy sami. Seedujemy te pliki masowo przez torrenty i udostępniamy je do bezpośredniego pobrania przez strony partnerskie."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Biblioteki źródłowe"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Niektóre biblioteki źródłowe promują masowe udostępnianie swoich danych za pomocą torrentów, podczas gdy inne niechętnie dzielą się swoimi zbiorami. W tym drugim przypadku, Archiwum Anny stara się zeskrobać ich zbiory i udostępnić je (zobacz naszą stronę <a %(a_torrents)s>Torrenty</a>). Istnieją również sytuacje pośrednie, na przykład gdy biblioteki źródłowe są chętne do udostępniania, ale nie mają zasobów, aby to zrobić. W takich przypadkach również staramy się pomóc."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Poniżej znajduje się przegląd, jak współpracujemy z różnymi bibliotekami źródłowymi."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Źródło"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Pliki"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Codzienne <a %(dbdumps)s>zrzuty bazy danych HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Zautomatyzowane torrenty dla <a %(nonfiction)s>lit. faktu</a> i <a %(fiction)s>beletrystyki</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(covers)s>torrenty okładek książek</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen „scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub zamroził nowe pliki od 2021 roku."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Zrzuty metadanych dostępne <a %(scihub1)s>tutaj</a> i <a %(scihub2)s>tutaj</a>, a także jako część <a %(libgenli)s>bazy danych Libgen.li</a> (której używamy)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrenty danych dostępne <a %(scihub1)s>tutaj</a>, <a %(scihub2)s>tutaj</a> i <a %(libgenli)s>tutaj</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Niektóre nowe pliki są <a %(libgenrs)s>dodawane</a> do „scimag” Libgen, ale nie na tyle, aby uzasadniać nowe torrenty"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kwartalne <a %(dbdumps)s>zrzuty bazy danych HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrenty lit. faktu są udostępniane z Libgen.rs (i lustrzane <a %(libgenli)s>tutaj</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Archiwum Anny i Libgen.li wspólnie zarządzają kolekcjami <a %(comics)s>komiksów</a>, <a %(magazines)s>czasopism</a>, <a %(standarts)s>dokumentów standardowych</a> oraz <a %(fiction)s>fikcji (oddzielonej od Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Ich kolekcja „fiction_rus” (rosyjska fikcja) nie ma dedykowanych torrentów, ale jest objęta torrentami od innych, a my utrzymujemy <a %(fiction_rus)s>mirror</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Archiwum Anny i Z-Library wspólnie zarządzają kolekcją <a %(metadata)s>metadanych Z-Library</a> i <a %(files)s>plików Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Niektóre metadane dostępne przez <a %(openlib)s>zrzuty bazy danych Open Library</a>, ale nie obejmują całej kolekcji IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Brak łatwo dostępnych zrzutów metadanych dla całej ich kolekcji"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(ia)s>metadanych IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Pliki dostępne tylko do wypożyczenia na ograniczonych zasadach, z różnymi ograniczeniami dostępu"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(ia)s>plików IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Różne bazy danych metadanych rozproszone po chińskim internecie; często płatne bazy danych"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Brak łatwo dostępnych zrzutów metadanych dla całej ich kolekcji."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(duxiu)s>metadanych DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Różne bazy danych plików rozproszone po chińskim internecie; często płatne bazy danych"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Większość plików dostępna tylko za pomocą kont premium BaiduYun; wolne prędkości pobierania."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(duxiu)s>plików DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Różne mniejsze lub jednorazowe źródła. Zachęcamy ludzi do przesyłania do innych bibliotek cieni, ale czasami ludzie mają kolekcje, które są zbyt duże, aby inni mogli je przeglądać, choć nie na tyle duże, aby zasługiwały na własną kategorię."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Źródła tylko z metadanymi"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Wzbogacamy również naszą kolekcję o źródła tylko z metadanymi, które możemy dopasować do plików, np. za pomocą numerów ISBN lub innych pól. Poniżej znajduje się ich przegląd. Ponownie, niektóre z tych źródeł są całkowicie otwarte, podczas gdy inne musimy zeskrobać."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Naszą inspiracją do zbierania metadanych jest cel Aarona Swartza „jedna strona internetowa dla każdej książki, jaka kiedykolwiek została opublikowana”, dla którego stworzył <a %(a_openlib)s>Open Library</a>. Ten projekt odniósł sukces, ale nasza unikalna pozycja pozwala nam uzyskać metadane, których oni nie mogą. Inną inspiracją była nasza chęć poznania <a %(a_blog)s>ile książek jest na świecie</a>, abyśmy mogli obliczyć, ile książek jeszcze musimy uratować."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Zauważ, że w wyszukiwaniu metadanych pokazujemy oryginalne rekordy. Nie łączymy rekordów."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ostatnia aktualizacja"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Miesięczne <a %(dbdumps)s>zrzuty bazy danych</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Niedostępne bezpośrednio w dużych ilościach, chronione przed skrobaniem"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(worldcat)s>metadanych OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Zunifikowana baza danych"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Łączymy wszystkie powyższe źródła w jedną zunifikowaną bazę danych, którą wykorzystujemy do obsługi tej strony internetowej. Ta zunifikowana baza danych nie jest dostępna bezpośrednio, ale ponieważ Archiwum Anny jest w pełni open source, można ją dość łatwo <a %(a_generated)s>wygenerować</a> lub <a %(a_downloaded)s>pobrać</a> jako bazy danych ElasticSearch i MariaDB. Skrypty na tej stronie automatycznie pobiorą wszystkie wymagane metadane z wymienionych powyżej źródeł."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Jeśli chcesz zbadać nasze dane przed uruchomieniem tych skryptów lokalnie, możesz spojrzeć na nasze pliki JSON, które linkują dalej do innych plików JSON. <a %(a_json)s>Ten plik</a> jest dobrym punktem wyjścia."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Zaadaptowane z naszego <a %(a_href)s>postu na blogu</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> to ogromna baza danych zeskanowanych książek, stworzona przez <a %(superstar_link)s>SuperStar Digital Library Group</a>. Większość to książki akademickie, zeskanowane w celu udostępnienia ich cyfrowo uniwersytetom i bibliotekom. Dla naszej anglojęzycznej publiczności, <a %(princeton_link)s>Princeton</a> i <a %(uw_link)s>University of Washington</a> mają dobre przeglądy. Jest również doskonały artykuł dający więcej tła: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Książki z Duxiu od dawna są piratowane w chińskim internecie. Zazwyczaj są sprzedawane za mniej niż dolara przez resellerów. Zazwyczaj są dystrybuowane za pomocą chińskiego odpowiednika Google Drive, który często jest hakowany, aby umożliwić większą przestrzeń dyskową. Niektóre szczegóły techniczne można znaleźć <a %(link1)s>tutaj</a> i <a %(link2)s>tutaj</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Chociaż książki były półpublicznie dystrybuowane, zdobycie ich w dużych ilościach jest dość trudne. Mieliśmy to wysoko na naszej liście rzeczy do zrobienia i przeznaczyliśmy na to kilka miesięcy pełnoetatowej pracy. Jednak pod koniec 2023 roku niesamowity, zdumiewający i utalentowany wolontariusz skontaktował się z nami, informując, że wykonał już całą tę pracę — za wielkie koszty. Podzielił się z nami pełną kolekcją, nie oczekując niczego w zamian, poza gwarancją długoterminowego przechowywania. Naprawdę niezwykłe."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Zasoby"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Łączna liczba plików: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Całkowity rozmiar plików: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Pliki zmirorowane przez Archiwum Anny: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ostatnia aktualizacja: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrenty Archiwum Anny"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Przykładowy rekord w Archiwum Anny"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Nasz post na blogu o tych danych"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skrypty do importowania metadanych"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Format Kontenerów Archiwum Anny"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Więcej informacji od naszych wolontariuszy (surowe notatki):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Ten zestaw danych jest ściśle powiązany z <a %(a_datasets_openlib)s>zestawem danych Open Library</a>. Zawiera zrzut wszystkich metadanych i dużą część plików z Kontrolowanej Biblioteki Cyfrowej IA. Aktualizacje są wydawane w <a %(a_aac)s>formacie kontenerów Archiwum Anny</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Te rekordy są bezpośrednio odwoływane z zestawu danych Open Library, ale zawierają również rekordy, które nie znajdują się w Open Library. Mamy także szereg plików danych zebranych przez członków społeczności na przestrzeni lat."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Kolekcja składa się z dwóch części. Potrzebujesz obu części, aby uzyskać wszystkie dane (z wyjątkiem zastąpionych torrentów, które są przekreślone na stronie torrentów)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "nasze pierwsze wydanie, zanim ustandaryzowaliśmy format <a %(a_aac)s>Kontenerów Archiwum Anny (AAC)</a>. Zawiera metadane (w formatach json i xml), pliki pdf (z systemów cyfrowego wypożyczania acsm i lcpdf) oraz miniaturki okładek."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementalne nowe wydania, używające AAC. Zawiera tylko metadane z znacznikami czasu po 2023-01-01, ponieważ reszta jest już pokryta przez „ia”. Zawiera również wszystkie pliki pdf, tym razem z systemów wypożyczania acsm i „bookreader” (webowy czytnik IA). Pomimo że nazwa nie jest całkowicie trafna, nadal umieszczamy pliki bookreader w kolekcji ia2_acsmpdf_files, ponieważ są one wzajemnie wykluczające się."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Główna strona %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Cyfrowa Biblioteka Wypożyczeń"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dokumentacja metadanych (większość pól)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informacje o kraju ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Międzynarodowa Agencja ISBN regularnie publikuje zakresy, które przydzieliła krajowym agencjom ISBN. Na tej podstawie możemy określić, do jakiego kraju, regionu lub grupy językowej należy dany ISBN. Obecnie korzystamy z tych danych pośrednio, za pośrednictwem biblioteki Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Zasoby"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ostatnia aktualizacja: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Strona internetowa ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadane"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Aby poznać historię różnych forków Library Genesis, zobacz stronę <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li zawiera większość tej samej treści i metadanych co Libgen.rs, ale ma dodatkowe kolekcje, mianowicie komiksy, magazyny i dokumenty standardowe. Zintegrował również <a %(a_scihub)s>Sci-Hub</a> ze swoimi metadanymi i wyszukiwarką, co wykorzystujemy w naszej bazie danych."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadane tej biblioteki są dostępne bezpłatnie <a %(a_libgen_li)s>na libgen.li</a>. Jednak ten serwer jest wolny i nie obsługuje wznawiania przerwanych połączeń. Te same pliki są również dostępne na <a %(a_ftp)s>serwerze FTP</a>, który działa lepiej."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrenty są dostępne dla większości dodatkowej zawartości, w szczególności torrenty dla komiksów, czasopism i dokumentów standardowych zostały wydane we współpracy z Archiwum Anny."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Kolekcja fikcji ma własne torrenty (oddzielone od <a %(a_href)s>Libgen.rs</a>) zaczynające się od %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Według administratora Libgen.li, kolekcja „fiction_rus” (rosyjska fikcja) powinna być objęta regularnie wydawanymi torrentami z <a %(a_booktracker)s>booktracker.org</a>, w szczególności torrentami <a %(a_flibusta)s>flibusta</a> i <a %(a_librusec)s>lib.rus.ec</a> (które mirrorujemy <a %(a_torrents)s>tutaj</a>, choć jeszcze nie ustaliliśmy, które torrenty odpowiadają którym plikom)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statystyki dla wszystkich kolekcji można znaleźć <a %(a_href)s>na stronie libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Literatura faktu również wydaje się być rozdzielona, ale bez nowych torrentów. Wygląda na to, że stało się to od początku 2022 roku, choć nie zweryfikowaliśmy tego."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Pewne zakresy bez torrentów (takie jak zakresy fikcji f_3463000 do f_4260000) to prawdopodobnie pliki Z-Library (lub inne duplikaty), choć możemy chcieć przeprowadzić deduplikację i stworzyć torrenty dla unikalnych plików lgli w tych zakresach."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Należy zauważyć, że pliki torrent odnoszące się do „libgen.is” są wyraźnie mirrorami <a %(a_libgen)s>Libgen.rs</a> („.is” to inna domena używana przez Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Pomocnym źródłem w korzystaniu z metadanych jest <a %(a_href)s>ta strona</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrenty z literaturą piękną na Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrenty z komiksami na Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrenty z magazynami na Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrenty dokumentów standardowych w Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrenty rosyjskiej fikcji w Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadane"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadane przez FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informacje o polach metadanych"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror innych torrentów (i unikalne torrenty z literaturą piękną i komiksami)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum dyskusyjne"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nasz wpis na blogu o wydaniu komiksów"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Krótka historia różnych forków Library Genesis (lub „Libgen”) jest taka, że z czasem różne osoby zaangażowane w Library Genesis poróżniły się i poszły własnymi drogami."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Wersja „.fun” została stworzona przez oryginalnego założyciela. Jest ona odświeżana na rzecz nowej, bardziej rozproszonej wersji."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Wersja „.rs” ma bardzo podobne dane i najczęściej wydaje swoją kolekcję w zbiorczych torrentach. Jest ona w przybliżeniu podzielona na sekcje „literatura piękna” i „lit. faktu”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oryginalnie na „http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Wersja „.li”</a> ma ogromną kolekcję komiksów, a także inną zawartość, która nie jest (jeszcze) dostępna do zbiorczego pobierania przez torrenty. Ma ona oddzielną kolekcję torrentów z książkami literatury pięknej i zawiera metadane <a %(a_scihub)s>Sci-Hub</a> w swojej bazie danych."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Według tego <a %(a_mhut)s>postu na forum</a>, Libgen.li pierwotnie była hostowana na „http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> w pewnym sensie jest również forkiem Library Genesis, chociaż użyli innej nazwy dla swojego projektu."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Ta strona dotyczy wersji „.rs”. Jest ona znana z konsekwentnego publikowania zarówno swoich metadanych, jak i pełnej zawartości swojego katalogu książek. Jej kolekcja książek jest podzielona na część z literaturą piękną i lit. faktu."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Pomocnym źródłem w korzystaniu z metadanych jest <a %(a_metadata)s>ta strona</a> (blokuje zakresy IP, może być wymagane użycie VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Od marca 2024 r. nowe torrenty są publikowane w <a %(a_href)s>tym wątku na forum</a> (blokuje zakresy IP, może być wymagany VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrenty literatury faktu na Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrenty literatury pięknej na Archiwum Anny"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadane Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informacje o polach metadanych Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrenty literatury faktu Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrenty literatury pięknej Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum dyskusyjne Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrenty z Archiwum Anny (okładki książek)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Nasz blog o wydaniu okładek książek"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis jest znane z hojnego udostępniania swoich danych w formie torrentów. Nasza kolekcja Libgen składa się z danych pomocniczych, które nie są bezpośrednio udostępniane, we współpracy z nimi. Wielkie podziękowania dla wszystkich zaangażowanych w Library Genesis za współpracę z nami!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Wydanie 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "To <a %(blog_post)s>pierwsze wydanie</a> jest dość małe: około 300 GB okładek książek z forka Libgen.rs, zarówno literatury pięknej, jak i faktu. Są one zorganizowane w taki sam sposób, jak pojawiają się na libgen.rs, np.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s dla książki literatury faktu."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s dla książki literatury pięknej."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Podobnie jak w przypadku kolekcji Z-Library, umieściliśmy je wszystkie w dużym pliku .tar, który można zamontować za pomocą <a %(a_ratarmount)s>ratarmount</a>, jeśli chcesz bezpośrednio udostępniać pliki."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> to własnościowa baza danych prowadzona przez organizację non-profit <a %(a_oclc)s>OCLC</a>, która agreguje rekordy metadanych z bibliotek na całym świecie. Jest to prawdopodobnie największa kolekcja metadanych bibliotecznych na świecie."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Październik 2023, pierwsze wydanie:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "W październiku 2023 roku <a %(a_scrape)s>opublikowaliśmy</a> kompleksowy zrzut bazy danych OCLC (WorldCat) w <a %(a_aac)s>formacie kontenerów Archiwum Anny</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrenty od Archiwum Anny"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nasz wpis na blogu o tych danych"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library to projekt open source prowadzony przez Internet Archive, mający na celu katalogowanie każdej książki na świecie. Posiada jedną z największych na świecie operacji skanowania książek i wiele książek dostępnych do cyfrowego wypożyczania. Jego katalog metadanych książek jest dostępny do pobrania za darmo i jest uwzględniony w Archiwum Anny (choć obecnie nie w wyszukiwarce, chyba że wyraźnie wyszukasz identyfikator Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadane"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Wydanie 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "To jest zrzut wielu zapytań do isbndb.com z września 2022 roku. Próbowaliśmy objąć wszystkie zakresy ISBN. To około 30,9 miliona rekordów. Na swojej stronie internetowej twierdzą, że mają faktycznie 32,6 miliona rekordów, więc mogło nam coś umknąć, albo <em>oni</em> mogą popełniać jakiś błąd."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Odpowiedzi JSON są praktycznie surowe z ich serwera. Jednym z problemów z jakością danych, który zauważyliśmy, jest to, że dla numerów ISBN-13 zaczynających się od innego prefiksu niż „978-”, nadal zawierają pole „isbn”, które jest po prostu numerem ISBN-13 z obciętymi pierwszymi 3 cyframi (i przeliczonym numerem kontrolnym). To jest oczywiście błędne, ale tak to robią, więc tego nie zmienialiśmy."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Innym potencjalnym problemem, na który możesz natrafić, jest fakt, że pole „isbn13” ma duplikaty, więc nie można go używać jako klucza głównego w bazie danych. Pola „isbn13”+„isbn” wydają się być unikalne."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Aby uzyskać więcej informacji na temat Sci-Hub, odwiedź jego <a %(a_scihub)s>oficjalną stronę</a>, <a %(a_wikipedia)s>stronę Wikipedii</a> oraz ten <a %(a_radiolab)s>wywiad w podcaście</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Zauważ, że Sci-Hub został <a %(a_reddit)s>zamrożony od 2021 roku</a>. Był zamrożony wcześniej, ale w 2021 roku dodano kilka milionów artykułów. Nadal jednak do kolekcji „scimag” w Libgen dodawana jest ograniczona liczba artykułów, choć nie na tyle, aby uzasadniać nowe masowe torrenty."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Używamy metadanych Sci-Hub dostarczonych przez <a %(a_libgen_li)s>Libgen.li</a> w jego kolekcji „scimag”. Używamy również zestawu danych <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Zauważ, że torrenty „smarch” są <a %(a_smarch)s>przestarzałe</a> i dlatego nie są uwzględnione na naszej liście torrentów."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrenty na Archiwum Anny"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadane i torrenty"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrenty na Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrenty na Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Aktualizacje na Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Strona Wikipedii"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Wywiad w podcaście"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Przesyłanie do Archiwum Anny"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Przegląd z <a %(a1)s>strony datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Różne mniejsze lub jednorazowe źródła. Zachęcamy ludzi do przesyłania do innych bibliotek cieni, ale czasami ludzie mają kolekcje, które są zbyt duże, aby inni mogli je przejrzeć, choć nie na tyle duże, aby uzasadniały własną kategorię."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Kolekcja „upload” jest podzielona na mniejsze podkolekcje, które są oznaczone w AACID i nazwach torrentów. Wszystkie podkolekcje zostały najpierw zdeduplikowane w stosunku do głównej kolekcji, choć pliki JSON „upload_records” metadanych nadal zawierają wiele odniesień do oryginalnych plików. Pliki nieksiążkowe zostały również usunięte z większości podkolekcji i zazwyczaj <em>nie</em> są odnotowane w plikach JSON „upload_records”."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Wiele podkolekcji składa się z pod-podkolekcji (np. z różnych oryginalnych źródeł), które są reprezentowane jako katalogi w polach „filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Podkolekcje to:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Podkolekcja"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notatki"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "przeglądaj"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "szukaj"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Z <a %(a_href)s>aaaaarg.fail</a>. Wydaje się być dość kompletne. Od naszego wolontariusza „cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Z <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentu. Ma dość duże nakładanie się z istniejącymi kolekcjami artykułów, ale bardzo mało dopasowań MD5, więc zdecydowaliśmy się zachować go w całości."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Zbieranie danych z <q>iRead eBooks</q> (= fonetycznie <q>ai rit i-books</q>; airitibooks.com), przez wolontariusza <q>j</q>. Odpowiada <q>airitibooks</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Z kolekcji <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Częściowo z oryginalnego źródła, częściowo z the-eye.eu, częściowo z innych luster."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Z prywatnej strony torrentowej z książkami, <a %(a_href)s>Bibliotik</a> (często nazywanej „Bib”), z której książki były pakowane w torrenty według nazw (A.torrent, B.torrent) i dystrybuowane przez the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Od naszego wolontariusza „bpb9v”. Więcej informacji o <a %(a_href)s>CADAL</a> znajdziesz w notatkach na naszej <a %(a_duxiu)s>stronie datasetu DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Więcej od naszego wolontariusza „bpb9v”, głównie pliki DuXiu, a także foldery „WenQu” i „SuperStar_Journals” (SuperStar to firma stojąca za DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Od naszego wolontariusza „cgiym”, chińskie teksty z różnych źródeł (reprezentowane jako podkatalogi), w tym z <a %(a_href)s>China Machine Press</a> (głównego chińskiego wydawcy)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Niechińskie kolekcje (reprezentowane jako podkatalogi) od naszego wolontariusza „cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Zbieranie danych o książkach na temat chińskiej architektury, przez wolontariusza <q>cm</q>: <q>Zdobyłem to, wykorzystując lukę w zabezpieczeniach sieci wydawnictwa, ale ta luka została już zamknięta</q>. Odpowiada <q>chinese_architecture</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Książki z akademickiego wydawnictwa <a %(a_href)s>De Gruyter</a>, zebrane z kilku dużych torrentów."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Zrzut z <a %(a_href)s>docer.pl</a>, polskiej strony do udostępniania plików skupiającej się na książkach i innych pracach pisemnych. Zebrane pod koniec 2023 roku przez wolontariusza „p”. Nie mamy dobrych metadanych z oryginalnej strony (nawet rozszerzeń plików), ale przefiltrowaliśmy pliki przypominające książki i często byliśmy w stanie wyodrębnić metadane z samych plików."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epuby, bezpośrednio z DuXiu, zebrane przez wolontariusza „w”. Tylko najnowsze książki DuXiu są dostępne bezpośrednio jako ebooki, więc większość z nich musi być nowa."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Pozostałe pliki DuXiu od wolontariusza „m”, które nie były w formacie PDG DuXiu (główna <a %(a_href)s>kolekcja DuXiu</a>). Zebrane z wielu oryginalnych źródeł, niestety bez zachowania tych źródeł w ścieżce pliku."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Zbieranie danych z książek erotycznych, przez wolontariusza <q>do no harm</q>. Odpowiada <q>hentai</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Kolekcja zebrana od japońskiego wydawcy mangi przez wolontariusza „t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Wybrane archiwa sądowe Longquan</a>, dostarczone przez wolontariusza „c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Zrzut z <a %(a_href)s>magzdb.org</a>, sojusznika Library Genesis (jest linkowany na stronie głównej libgen.rs), ale który nie chciał udostępnić swoich plików bezpośrednio. Uzyskane przez wolontariusza „p” pod koniec 2023 roku."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Różne małe przesyłki, zbyt małe, aby stanowiły własną podkolekcję, ale reprezentowane jako katalogi."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooki z AvaxHome, rosyjskiej strony do udostępniania plików."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archiwum gazet i czasopism. Odpowiada <q>newsarch_magz</q> metadata w <a %(a1)s><q>Inne zbieranie metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Zbieranie danych z <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Kolekcja wolontariusza „o”, który zbierał polskie książki bezpośrednio z oryginalnych stron wydawniczych („scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Połączone kolekcje <a %(a_href)s>shuge.org</a> przez wolontariuszy „cgiym” i „woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>„Imperialna Biblioteka Trantora”</a> (nazwana na cześć fikcyjnej biblioteki), zeskrobana w 2022 roku przez wolontariusza „t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Pod-pod-kolekcje (reprezentowane jako katalogi) od wolontariusza „woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (przez <a %(a_sikuquanshu)s>Dizhi(迪志)</a> na Tajwanie), mebook (mebook.cc, 我的小书屋, moja mała biblioteczka — woz9ts: „Ta strona głównie skupia się na udostępnianiu wysokiej jakości plików ebooków, z których niektóre są składane przez samego właściciela. Właściciel został <a %(a_arrested)s>aresztowany</a> w 2019 roku i ktoś zrobił kolekcję plików, które udostępnił.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Pozostałe pliki DuXiu od wolontariusza „woz9ts”, które nie były w zastrzeżonym formacie PDG DuXiu (jeszcze do konwersji na PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrenty od Archiwum Anny"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ma swoje korzenie w społeczności <a %(a_href)s>Library Genesis</a> i początkowo korzystała z ich danych. Od tego czasu znacznie się profesjonalizowała i ma znacznie nowocześniejszy interfejs. Dzięki temu mogą uzyskać znacznie więcej darowizn, zarówno pieniężnych na dalsze ulepszanie swojej strony, jak i darowizn w postaci nowych książek. Zgromadzili dużą kolekcję oprócz Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Aktualizacja z lutego 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Pod koniec 2022 roku rzekomi założyciele Z-Library zostali aresztowani, a domeny zostały przejęte przez władze Stanów Zjednoczonych. Od tego czasu strona powoli wraca do sieci. Nie wiadomo, kto obecnie nią zarządza."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Kolekcja składa się z trzech części. Oryginalne strony opisowe dla pierwszych dwóch części są zachowane poniżej. Potrzebujesz wszystkich trzech części, aby uzyskać wszystkie dane (z wyjątkiem zastąpionych torrentów, które są przekreślone na stronie torrentów)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nasze pierwsze wydanie. Było to pierwsze wydanie tego, co wtedy nazywało się „Pirate Library Mirror” („pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: drugie wydanie, tym razem ze wszystkimi plikami zapakowanymi w pliki .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: incrementalne nowe wydania, używające formatu <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, teraz wydawane we współpracy z zespołem Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenty od Archiwum Anny (metadane + zawartość)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Przykładowy rekord w Archiwum Anny (oryginalna kolekcja)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Przykładowy rekord w Archiwum Anny (kolekcja „zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Główna strona"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domena Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Post na blogu o Wydaniu 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Post na blogu o Wydaniu 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Wydania Zlib (oryginalne strony opisowe)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Wydanie 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Początkowy mirror został mozolnie uzyskany w ciągu 2021 i 2022 roku. W tym momencie jest nieco przestarzały: odzwierciedla stan kolekcji z czerwca 2021 roku. Zaktualizujemy to w przyszłości. Obecnie koncentrujemy się na wydaniu tego pierwszego wydania."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Ponieważ Library Genesis jest już zachowane z publicznymi torrentami i jest uwzględnione w Z-Library, przeprowadziliśmy podstawową deduplikację względem Library Genesis w czerwcu 2022 roku. Do tego użyliśmy skrótów MD5. Prawdopodobnie w bibliotece jest dużo więcej zduplikowanej zawartości, takiej jak wiele formatów plików z tą samą książką. Trudno to dokładnie wykryć, więc tego nie robimy. Po deduplikacji pozostało nam ponad 2 miliony plików, o łącznej wielkości nieco poniżej 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Kolekcja składa się z dwóch części: zrzutu MySQL „.sql.gz” metadanych oraz 72 plików torrent o wielkości od około 50 do 100GB każdy. Metadane zawierają dane zgłoszone przez stronę Z-Library (tytuł, autor, opis, typ pliku), a także rzeczywisty rozmiar pliku i md5sum, które zaobserwowaliśmy, ponieważ czasami te dane się nie zgadzają. Wydaje się, że istnieją zakresy plików, dla których sama Z-Library ma nieprawidłowe metadane. W niektórych odosobnionych przypadkach mogliśmy również pobrać pliki nieprawidłowo, co postaramy się wykryć i naprawić w przyszłości."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Duże pliki torrent zawierają rzeczywiste dane książek, z identyfikatorem Z-Library jako nazwą pliku. Rozszerzenia plików można odtworzyć za pomocą zrzutu metadanych."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Kolekcja jest mieszanką treści literatury faktu i beletrystyki (nie oddzielonych jak w Library Genesis). Jakość również jest bardzo zróżnicowana."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "To pierwsze wydanie jest teraz w pełni dostępne. Należy zauważyć, że pliki torrent są dostępne tylko przez nasz mirror w sieci Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Wydanie 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Uzyskaliśmy wszystkie książki, które zostały dodane do Z-Library między naszym ostatnim mirrorem a sierpniem 2022 roku. Cofnęliśmy się również i zeskrobaliśmy niektóre książki, które przegapiliśmy za pierwszym razem. W sumie nowa kolekcja ma około 24TB. Ponownie, ta kolekcja jest deduplikowana względem Library Genesis, ponieważ dla tej kolekcji są już dostępne torrenty."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Dane są zorganizowane podobnie jak w pierwszym wydaniu. Jest zrzut MySQL „.sql.gz” metadanych, który zawiera również wszystkie metadane z pierwszego wydania, zastępując je. Dodaliśmy również kilka nowych kolumn:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: czy ten plik jest już w Library Genesis, w kolekcji literatury faktu lub beletrystyki (dopasowane przez md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: w którym torrencie znajduje się ten plik."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: ustawione, gdy nie udało nam się pobrać książki."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Wspomnieliśmy o tym ostatnim razem, ale dla jasności: „filename” i „md5” to rzeczywiste właściwości pliku, podczas gdy „filename_reported” i „md5_reported” to dane, które zeskrobaliśmy z Z-Library. Czasami te dwie wartości się nie zgadzają, więc uwzględniliśmy obie."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Dla tego wydania zmieniliśmy porządek sortowania na „utf8mb4_unicode_ci”, który powinien być kompatybilny ze starszymi wersjami MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Pliki danych są podobne do tych z poprzedniego razu, choć są znacznie większe. Po prostu nie chciało nam się tworzyć mnóstwa mniejszych plików torrent. „pilimi-zlib2-0-14679999-extra.torrent” zawiera wszystkie pliki, które przegapiliśmy w poprzednim wydaniu, podczas gdy pozostałe torrenty to nowe zakresy ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Aktualizacja %(date)s:</strong> Zrobiliśmy większość naszych torrentów zbyt dużymi, co powodowało problemy z klientami torrent. Usunęliśmy je i wydaliśmy nowe torrenty."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Aktualizacja %(date)s:</strong> Nadal było zbyt wiele plików, więc spakowaliśmy je w pliki tar i ponownie wydaliśmy nowe torrenty."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Dodatek do wydania 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "To jest pojedynczy dodatkowy plik torrent. Nie zawiera żadnych nowych informacji, ale ma pewne dane, które mogą zająć trochę czasu na obliczenie. To sprawia, że jest wygodny, ponieważ pobranie tego torrenta jest często szybsze niż obliczenie go od zera. W szczególności zawiera indeksy SQLite dla plików tar, do użycia z <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Często zadawane pytania (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Czym jest Archiwum Anny?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)sAnna’s Archive</span> to niekomercyjny projekt z dwoma celami:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Utrwalenie:</strong> Zachowywanie od utraty przepastnych zasobów wiedzy i kultury ludzkości.</li><li><strong>Dostęp:</strong> Uczynienie wiedzy i kultury dostępnej dla każdego na świecie.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Cały nasz <a %(a_code)s>kod</a> i <a %(a_datasets)s>dane</a> są całkowicie open source."

msgid "page.home.preservation.header"
msgstr "Utrwalenie"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Zachowujemy książki, artykuły, komiksy, czasopisma i inne materiały, łącząc te zasoby z różnych <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliotek cieni</a>, oficjalnych bibliotek i innych kolekcji w jednym miejscu. Wszystkie te dane są zachowywane na zawsze, dzięki łatwości ich duplikowania w dużych ilościach — za pomocą torrentów — co skutkuje wieloma kopiami na całym świecie. Niektóre biblioteki cieni już to robią same (np. Sci-Hub, Library Genesis), podczas gdy Archiwum Anny „wyzwala” inne biblioteki, które nie oferują dystrybucji masowej (np. Z-Library) lub w ogóle nie są bibliotekami cieni (np. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Ta szeroka dystrybucja, w połączeniu z kodem open source, sprawia, że nasza strona jest odporna na usunięcia i zapewnia długoterminowe zachowanie wiedzy i kultury ludzkości. Dowiedz się więcej o <a href=\"/datasets\">naszych zbiorach danych</a>."

msgid "page.home.preservation.label"
msgstr "Przewidujemy, że udało nam się utrwalić około <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\"> 5%% światowych zasobów książkowych </a>."

msgid "page.home.access.header"
msgstr "Dostęp"

msgid "page.home.access.text"
msgstr "Współpracujemy z partnerami zewnętrznymi, aby umożliwić dostęp do naszych zasobów łatwym i dostępnym dla każdego. Uważamy, że każdy powinien mieć dostęp do wiedzy oraz mądrości całej ludzkości i <a %(a_search)s>nie kosztem autorów</a>."

msgid "page.home.access.label"
msgstr "Ilość pobrań w odstępie cogodzinnym z ostatnich 30 dni. Godzinna średnia: %(hourly)s. Dzienna średnia: %(daily)s."

msgid "page.about.text2"
msgstr "Mocno wierzymy w wolny przepływ informacji, jak i w ochronę kultury i wiedzy. Tą wyszukiwarką stoimy na barkach olbrzymów. Głęboko szanujemy ciężką pracę ludzi, którzy utworzyli przeróżne ukryte biblioteki, i mamy nadzieję, że ta wyszukiwarka poszerzy ich zasięg."

msgid "page.about.text3"
msgstr "Bądź na bieżąco, śledź Annę na <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddicie</a> lub <a href=\"https://t.me/annasarchiveorg\">Telegramie</a>. Pytania i opinie odnośnie projektu kieruj do Anny, pod %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Jak mogę pomóc?"

msgid "page.about.help.text"
msgstr "<li>1. Śledź nas na <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddicie</a> lub <a href=\"https://t.me/annasarchiveorg\">Telegramie</a>.</li><li>2. Szerz wiedzę o Anna’s Archive na Twitterze, Tiktoku, Instagramie, w lokalnej kawiarni, bibliotece, czy też gdziekolwiek indziej! Nie wierzymy w obwarowanie dostępu do informacji — nawet jeśli ta strona zostanie zamknięta, powrócimy w innym miejscu, ponieważ cały nasz kod i dane są otwarte.</li><li>3. Jeśli możesz, rozważ <a href=\"/donate\">wsparcie nas finansowo</a>.</li><li>4. Pomóż <a href=\"https://translate.annas-software.org/\">przetłumaczyć</a> naszą stronę na inne języki.</li><li>5. Jeśli jesteś programistą, rozważ pracę nad naszym <a href=\"https://annas-software.org/\">otwartym kodem</a>, lub seedowanie <a href=\"/datasets\">torrentów</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Mamy teraz również zsynchronizowany kanał Matrix pod adresem %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Jeśli jesteś badaczem bezpieczeństwa, możemy wykorzystać twoje umiejętności zarówno w ataku, jak i obronie. Sprawdź naszą stronę <a %(a_security)s>Bezpieczeństwo</a>."

msgid "page.about.help.text7"
msgstr "7. Poszukujemy specjalistów od anonimowych płatności. Czy możesz pomóc nam zaimplementować wygodniejsze sposoby przekazywania darowizn? PayPal, WeChat, karty podarunkowe. Jeśli znasz kogoś takiego, skontaktuj się z nami."

msgid "page.about.help.text8"
msgstr "8. Zawsze szukamy lepszych serwerów z dużą pojemnością."

msgid "page.about.help.text9"
msgstr "9. Możesz pomóc, zgłaszając problemy z plikami, komentując i tworząc listy bezpośrednio na tej stronie. Możesz także pomóc, <a %(a_upload)s>przesyłając więcej książek</a> lub naprawiając problemy z plikami lub formatowaniem istniejących książek."

msgid "page.about.help.text10"
msgstr "10. Stwórz lub pomóż w utrzymaniu Anna’s Archive w Wikipedii w swoim języku."

msgid "page.about.help.text11"
msgstr "11. Poszukujemy możliwości zamieszczania niewielkich, estetycznych reklam. Jeśli chciałbyś zareklamować się na Anna's Archive, daj nam znać."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Chcielibyśmy, aby ludzie zakładali <a %(a_mirrors)s>lustrzane kopie</a>, a my będziemy to finansowo wspierać."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Aby uzyskać bardziej szczegółowe informacje na temat wolontariatu, zobacz naszą stronę <a %(a_volunteering)s>Wolontariat i Nagrody</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Dlaczego pobieranie jest tak wolne?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Dosłownie nie mamy wystarczających zasobów, aby zapewnić wszystkim na świecie szybkie pobieranie, choć bardzo byśmy chcieli. Gdyby bogaty dobroczyńca chciał nam to zapewnić, byłoby to niesamowite, ale do tego czasu staramy się jak najlepiej. Jesteśmy projektem non-profit, który ledwo utrzymuje się z darowizn."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dlatego wdrożyliśmy dwa systemy darmowych pobrań z naszymi partnerami: współdzielone serwery z wolnym pobieraniem oraz nieco szybsze serwery z listą oczekujących (aby zmniejszyć liczbę osób pobierających jednocześnie)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Mamy również <a %(a_verification)s>weryfikację przeglądarki</a> dla naszych wolnych pobrań, ponieważ w przeciwnym razie boty i skrypty będą je nadużywać, co jeszcze bardziej spowolni pobieranie dla prawdziwych użytkowników."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Zauważ, że podczas korzystania z przeglądarki Tor może być konieczne dostosowanie ustawień bezpieczeństwa. Na najniższym poziomie opcji, zwanym „Standard”, wyzwanie Cloudflare turnstile kończy się sukcesem. Na wyższych opcjach, zwanych „Bezpieczniejszy” i „Najbezpieczniejszy”, wyzwanie kończy się niepowodzeniem."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Czasami pobieranie dużych plików może zostać przerwane w trakcie. Zalecamy użycie menedżera pobierania (takiego jak JDownloader), aby automatycznie wznawiać duże pobrania."

msgid "page.donate.faq.title"
msgstr "Najczęściej zadawane pytania - Wsparcie pieniężne"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s> Czy członkostwo zostaje automatycznie odnowione?</div> Członkostwa <strong> nie są </strong> automatycznie odnawiane. Możesz dołączyć na krótki, bądź długi okres, w zależności od potrzeby."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Czy mogę ulepszyć swoje członkostwo lub uzyskać wiele członkostw?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Czy obsługujecie inne metody płatności?</div> Obecnie nie. Wielu ludzi nie chce, aby istniały archiwa takie jak nasze, więc ciągle musimy zachowywać ostrożność. Jeżeli jesteś w stanie pomóc nam bezpiecznie obsługiwać inne (zdecydowanie bardziej wygodne) metody płatności, skontaktuj się z nami pod %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Co oznaczają zakresy na miesiąc?</div> Możesz osiągnąć dolną granicę zakresu, stosując wszystkie zniżki, takie jak wybór okresu dłuższego niż miesiąc."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s> Na co przeznaczacie darowizny?</div> 100%% środków jest przeznaczane na utrwalanie i udostępnianie wiedzy oraz kultury całego świata. Obecnie zostają one przeznaczane na serwery, przestrzeń dyskową oraz łącza. Członkowie projektu nie otrzymują wynagrodzenia."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Czy mogę przelać większe kwoty?</div> Byłoby to niesamowite! Dla kwot powyżej kilku tysięcy dolarów, prosimy o kontakt bezpośredni pod %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Czy mogę przekazać darowiznę bez zostania członkiem?</div> Oczywiście. Akceptujemy darowizny w dowolnej kwocie na ten adres Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Jak mogę przesłać nowe książki?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternatywnie, możesz przesłać je do Z-Library <a %(a_upload)s>tutaj</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Dla małych przesyłek (do 10 000 plików) prosimy o przesłanie ich zarówno do %(first)s, jak i do %(second)s."

msgid "page.upload.text1"
msgstr "W chwili obecnej prosimy o przekazywanie plików przy pomocy gałęzi projektu Library Genesis. W tym miejscu znajduje się <a %(a_guide)s>przyjazny przewodnik </a>. Zauważ, że odnogi Library Genesis które indeksujemy współdzielą ten sam system współdzielenia plików."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Dla Libgen.li, upewnij się, że najpierw zalogujesz się na <a %(a_forum)s >ich forum</a> z nazwą użytkownika %(username)s i hasłem %(password)s, a następnie wrócisz na ich <a %(a_upload_page)s >stronę przesyłania</a>."

msgid "common.libgen.email"
msgstr "Jeżeli Twój adres e-mail nie działa na forum Libgen, sugerujemy użycie <a %(a_mail)s> Proton Mail </a> (konto darmowe). Można także <a %(a_manual)s>poprosić ręcznie</a> o aktywację konta."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Zauważ, że mhut.org blokuje pewne zakresy IP, więc może być wymagane użycie VPN."

msgid "page.upload.large.text"
msgstr "Dla dużych wrzutów (ponad 10,000 plików) które nie zostały zaakceptowane przez Libgen lub Z-Library, skontaktuj się z nami pod adresem %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Aby przesłać prace naukowe, prosimy również (oprócz Library Genesis) przesyłać je do <a %(a_stc_nexus)s>STC Nexus</a>. Jest to najlepsza biblioteka cieni dla nowych prac. Jeszcze ich nie zintegrowaliśmy, ale zrobimy to w przyszłości. Możesz skorzystać z ich <a %(a_telegram)s>bota do przesyłania na Telegramie</a> lub skontaktować się z adresem podanym w przypiętej wiadomości, jeśli masz zbyt wiele plików do przesłania w ten sposób."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Jak mogę zamówić książki?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "W tej chwili nie możemy przyjmować próśb o książki."

#, fuzzy
msgid "page.request.forums"
msgstr "Prosimy o składanie próśb na forach Z-Library lub Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nie wysyłaj nam próśb o książki e-mailem."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Czy zbieracie metadane?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Zgadza się."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Pobrałem \"1984\" George'a Orwella, czy policja zapuka do moich drzwi?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Nie martw się zbytnio, wiele osób pobiera z witryn, do których linkujemy, i niezwykle rzadko zdarza się, aby ktoś miał z tego powodu problemy. Jednak dla bezpieczeństwa zalecamy korzystanie z VPN (płatnego) lub <a %(a_tor)s>Tor</a> (darmowego)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Jak zapisać ustawienia wyszukiwania?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Wybierz preferowane ustawienia, pozostaw pole wyszukiwania puste, kliknij „Szukaj”, a następnie dodaj stronę do zakładek za pomocą funkcji zakładek w przeglądarce."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Czy macie aplikację mobilną?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nie mamy oficjalnej aplikacji mobilnej, ale możesz zainstalować tę stronę jako aplikację."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Kliknij menu z trzema kropkami w prawym górnym rogu i wybierz „Dodaj do ekranu głównego”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Kliknij przycisk „Udostępnij” na dole i wybierz „Dodaj do ekranu głównego”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Czy macie API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Mamy stabilne API JSON dla członków, umożliwiające uzyskanie szybkiego URL do pobrania: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacja w samym JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Do innych zastosowań, takich jak iterowanie przez wszystkie nasze pliki, budowanie niestandardowych wyszukiwarek i tak dalej, zalecamy <a %(a_generate)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB. Surowe dane można ręcznie przeglądać <a %(a_explore)s>poprzez pliki JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Naszą surową listę torrentów można również pobrać jako <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrenty FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Chciałbym pomóc w seedowaniu, ale nie mam dużo miejsca na dysku."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Użyj <a %(a_list)s>generatora listy torrentów</a>, aby wygenerować listę torrentów, które najbardziej potrzebują seedowania, w granicach twojej przestrzeni dyskowej."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrenty są zbyt wolne; czy mogę pobrać dane bezpośrednio od was?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Tak, zobacz stronę <a %(a_llm)s>danych LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Czy mogę pobrać tylko część plików, na przykład tylko w określonym języku lub na określony temat?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Krótka odpowiedź: niełatwo."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Długa odpowiedź:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Większość torrentów zawiera pliki bezpośrednio, co oznacza, że możesz polecić klientom torrent, aby pobierali tylko wymagane pliki. Aby określić, które pliki pobrać, możesz <a %(a_generate)s>wygenerować</a> nasze metadane lub <a %(a_download)s>pobrać</a> nasze bazy danych ElasticSearch i MariaDB. Niestety, niektóre kolekcje torrentów zawierają pliki .zip lub .tar w katalogu głównym, w takim przypadku musisz pobrać cały torrent, zanim będziesz mógł wybrać poszczególne pliki."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Mamy jednak <a %(a_ideas)s>kilka pomysłów</a> na ten drugi przypadek.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nie ma jeszcze łatwych w użyciu narzędzi do filtrowania torrentów, ale zachęcamy do współpracy."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Jak radzicie sobie z duplikatami w torrentach?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Staramy się minimalizować duplikaty lub nakładanie się torrentów na tej liście, ale nie zawsze jest to możliwe i zależy w dużej mierze od polityki bibliotek źródłowych. W przypadku bibliotek, które wydają własne torrenty, nie mamy na to wpływu. W przypadku torrentów wydanych przez Anna’s Archive, deduplikujemy tylko na podstawie hasha MD5, co oznacza, że różne wersje tej samej książki nie są deduplikowane."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Czy mogę otrzymać listę torrentów w formacie JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Tak."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Nie widzę plików PDF ani EPUB w torrentach, tylko pliki binarne? Co mam zrobić?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "To są właściwie pliki PDF i EPUB, po prostu nie mają rozszerzenia w wielu naszych torrentach. Istnieją dwa miejsca, w których można znaleźć metadane dla plików torrent, w tym typy/rozszerzenia plików:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Każda kolekcja lub wydanie ma swoje własne metadane. Na przykład, <a %(a_libgen_nonfic)s>torrenty Libgen.rs</a> mają odpowiadającą im bazę metadanych hostowaną na stronie Libgen.rs. Zazwyczaj linkujemy do odpowiednich zasobów metadanych z <a %(a_datasets)s>strony datasetu</a> każdej kolekcji."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Zalecamy <a %(a_generate)s>generowanie</a> lub <a %(a_download)s>pobieranie</a> naszych baz danych ElasticSearch i MariaDB. Zawierają one mapowanie każdego rekordu w Archiwum Anny do odpowiadających mu plików torrent (jeśli są dostępne), pod \"torrent_paths\" w JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Dlaczego mój klient torrent nie może otworzyć niektórych waszych plików torrent / linków magnetycznych?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Niektóre klienty torrentów nie obsługują dużych rozmiarów kawałków, które mają wiele naszych torrentów (dla nowszych już tego nie robimy — mimo że jest to zgodne ze specyfikacją!). Spróbuj użyć innego klienta, jeśli napotkasz ten problem, lub zgłoś to twórcom swojego klienta torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Czy macie program odpowiedzialnego ujawniania luk?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Zapraszamy badaczy bezpieczeństwa do poszukiwania luk w naszych systemach. Jesteśmy wielkimi zwolennikami odpowiedzialnego ujawniania. Skontaktuj się z nami <a %(a_contact)s>tutaj</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Obecnie nie jesteśmy w stanie przyznawać nagród za znalezione błędy, z wyjątkiem luk, które mają <a %(a_link)s>potencjał do naruszenia naszej anonimowości</a>, za które oferujemy nagrody w zakresie 10-50 tys. dolarów. Chcielibyśmy w przyszłości zaoferować szerszy zakres nagród za znalezione błędy! Proszę pamiętać, że ataki socjotechniczne są poza zakresem."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Jeśli interesujesz się bezpieczeństwem ofensywnym i chcesz pomóc w archiwizacji wiedzy i kultury świata, koniecznie skontaktuj się z nami. Istnieje wiele sposobów, w jakie możesz pomóc."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Czy są dostępne dodatkowe zasoby dotyczące Archiwum Anny?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog Anny</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regularne aktualizacje"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Oprogramowanie Anny</a> — nasz kod open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Tłumacz w Oprogramowaniu Anny</a> — nasz system tłumaczeń"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — o danych"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatywne domeny"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — więcej o nas (prosimy o pomoc w aktualizacji tej strony lub utworzenie jednej w swoim języku!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Jak zgłosić naruszenie praw autorskich?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nie hostujemy tutaj żadnych materiałów objętych prawami autorskimi. Jesteśmy wyszukiwarką i jako taka indeksujemy tylko metadane, które są już publicznie dostępne. Podczas pobierania z tych zewnętrznych źródeł, sugerujemy sprawdzenie przepisów w Twojej jurysdykcji w odniesieniu do tego, co jest dozwolone. Nie ponosimy odpowiedzialności za treści hostowane przez innych."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Jeśli masz skargi dotyczące tego, co widzisz tutaj, najlepiej skontaktować się z oryginalną stroną internetową. Regularnie wciągamy ich zmiany do naszej bazy danych. Jeśli naprawdę uważasz, że masz uzasadnioną skargę DMCA, na którą powinniśmy odpowiedzieć, wypełnij <a %(a_copyright)s>formularz zgłoszenia DMCA / naruszenia praw autorskich</a>. Traktujemy Twoje skargi poważnie i skontaktujemy się z Tobą tak szybko, jak to możliwe."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Nienawidzę, jak prowadzicie ten projekt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Chcielibyśmy również przypomnieć wszystkim, że cały nasz kod i dane są całkowicie open source. To wyjątkowe dla projektów takich jak nasz — nie znamy żadnego innego projektu z równie ogromnym katalogiem, który byłby również w pełni open source. Bardzo chętnie przyjmiemy każdego, kto uważa, że źle prowadzimy nasz projekt, aby wziął nasz kod i dane i założył własną bibliotekę cieni! Nie mówimy tego złośliwie — naprawdę uważamy, że byłoby to niesamowite, ponieważ podniosłoby to poprzeczkę dla wszystkich i lepiej zachowało dziedzictwo ludzkości."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Czy masz monitor czasu pracy?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Proszę zobaczyć <a %(a_href)s>ten doskonały projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Jak mogę przekazać książki lub inne materiały fizyczne?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Proszę wysłać je do <a %(a_archive)s>Internet Archive</a>. Zostaną one odpowiednio zachowane."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Kim jest Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Ty jesteś Anną!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Jakie są Twoje ulubione książki?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Oto kilka książek, które mają szczególne znaczenie dla świata bibliotek cieni i cyfrowej archiwizacji:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Wyczerpałeś dzisiejszy limit szybkich pobrań."

msgid "page.fast_downloads.no_member"
msgstr "Zostań członkiem, aby uzyskać dostęp do szybkich pobierań."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Obecnie obsługujemy karty podarunkowe Amazon, karty kredytowe i debetowe, kryptowaluty, Alipay i WeChat."

msgid "page.home.full_database.header"
msgstr "Pełna baza danych"

msgid "page.home.full_database.subtitle"
msgstr "Książki, prace naukowe, czasopisma, komiksy, rekordy biblioteczne, metadane itd…"

msgid "page.home.full_database.search"
msgstr "Szukaj"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "wersja beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>wstrzymał</a> przesyłanie nowych artykułów."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB jest kontynuacją Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Bezpośredni dostęp do prac naukowych %(count)s"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Otwórz"

msgid "page.home.scidb.browser_verification"
msgstr "Jeśli jesteś <a %(a_member)s>członkiem</a>, weryfikacja przeglądarki nie jest wymagana."

msgid "page.home.archive.header"
msgstr "Archiwizacja długoterminowa"

msgid "page.home.archive.body"
msgstr "Zbiory danych wykorzystywane w Anna’s Archive są całkowicie otwarte i mogą być kopiowane masowo za pomocą torrentów. <a %(a_datasets)s>Dowiedz się więcej…</a>"

msgid "page.home.torrents.body"
msgstr "Możesz pomóc seedując torrenty. <a %(a_torrents)s>Dowiedz się więcej…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seedów"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seedów"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seedów"

msgid "page.home.llm.header"
msgstr "Dane treningowe LLM"

msgid "page.home.llm.body"
msgstr "Posiadamy największą na świecie kolekcję wysokiej jakości danych tekstowych. <a %(a_llm)s>Dowiedz się więcej…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Lustra: wezwanie dla wolontariuszy"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Poszukujemy wolontariuszy"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Jako non-profitowy, open-source'owy projekt, zawsze szukamy osób do pomocy."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Jeśli prowadzisz wysokiego ryzyka anonimowy procesor płatności, skontaktuj się z nami. Szukamy również osób chcących umieścić gustowne małe reklamy. Wszystkie dochody przeznaczamy na nasze działania związane z zachowaniem zasobów."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog Anny ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Pobrania IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Wszystkie linki do pobrania tego pliku: <a %(a_main)s>Strona pliku</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Brama IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(udane pobranie przez IPFS może wymagać kilku prób)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Aby uzyskać szybkie pobieranie oraz pominąć weryfikacji przeglądarki, <a %(a_membership)s>uzyskaj członkostwo</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 W celu wykonywania kopii lustrzanych naszej kolekcji, przejdź do stron: <%(a_datasets)s>Zbiory danych</a> lub <%(a_torrents)s>Pliki Torrent</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Dane LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Jest powszechnie wiadomo, że LLM-y rozwijają się na wysokiej jakości danych. Mamy największą na świecie kolekcję książek, artykułów, magazynów itp., które są jednymi z najwyższej jakości źródeł tekstowych."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unikalna skala i zasięg"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Nasza kolekcja zawiera ponad sto milionów plików, w tym czasopisma naukowe, podręczniki i magazyny. Osiągamy tę skalę, łącząc duże istniejące repozytoria."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Niektóre z naszych źródłowych kolekcji są już dostępne w dużych ilościach (Sci-Hub i części Libgen). Inne źródła wyzwoliliśmy sami. <a %(a_datasets)s>Datasets</a> pokazuje pełny przegląd."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Nasza kolekcja obejmuje miliony książek, artykułów i magazynów sprzed ery e-booków. Duże części tej kolekcji zostały już poddane OCR i mają niewielkie wewnętrzne nakładanie się."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Jak możemy pomóc"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Jesteśmy w stanie zapewnić szybki dostęp do naszych pełnych kolekcji, a także do nieopublikowanych zbiorów."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "To dostęp na poziomie przedsiębiorstwa, który możemy zapewnić za darowizny w wysokości dziesiątek tysięcy USD. Jesteśmy również gotowi wymienić to na wysokiej jakości kolekcje, których jeszcze nie posiadamy."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Możemy zwrócić Ci pieniądze, jeśli będziesz w stanie dostarczyć nam wzbogacenie naszych danych, takie jak:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Usuwanie nakładania się (deduplikacja)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Ekstrakcja tekstu i metadanych"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Wspieraj długoterminowe archiwizowanie ludzkiej wiedzy, jednocześnie uzyskując lepsze dane dla swojego modelu!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Skontaktuj się z nami</a>, aby omówić, jak możemy współpracować."

msgid "page.login.continue"
msgstr "Kontynuuj"

#, fuzzy
msgid "page.login.please"
msgstr "Proszę <a %(a_account)s>zaloguj się</a>, aby zobaczyć tę stronę.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Archiwum Anny jest tymczasowo niedostępne z powodu konserwacji. Proszę wrócić za godzinę."

#, fuzzy
msgid "page.metadata.header"
msgstr "Popraw metadane"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Możesz pomóc w zachowaniu książek, poprawiając metadane! Najpierw przeczytaj tło dotyczące metadanych na Archiwum Anny, a następnie dowiedz się, jak poprawiać metadane poprzez łączenie z Open Library i zdobądź darmowe członkostwo na Archiwum Anny."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Tło"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Kiedy przeglądasz książkę na Archiwum Anny, możesz zobaczyć różne pola: tytuł, autor, wydawca, edycja, rok, opis, nazwa pliku i inne. Wszystkie te informacje nazywane są <em>metadanymi</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Ponieważ łączymy książki z różnych <em>bibliotek źródłowych</em>, pokazujemy wszelkie dostępne metadane z tej biblioteki źródłowej. Na przykład, dla książki, którą otrzymaliśmy z Library Genesis, pokażemy tytuł z bazy danych Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Czasami książka jest obecna w <em>wielu</em> bibliotekach źródłowych, które mogą mieć różne pola metadanych. W takim przypadku po prostu pokazujemy najdłuższą wersję każdego pola, ponieważ ta najprawdopodobniej zawiera najbardziej przydatne informacje! Nadal pokażemy inne pola poniżej opisu, np. jako „alternatywny tytuł” (ale tylko jeśli są różne)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Wyciągamy również <em>kody</em> takie jak identyfikatory i klasyfikatory z biblioteki źródłowej. <em>Identyfikatory</em> jednoznacznie reprezentują konkretną edycję książki; przykłady to ISBN, DOI, Open Library ID, Google Books ID lub Amazon ID. <em>Klasyfikatory</em> grupują podobne książki; przykłady to Dewey Decimal (DCC), UDC, LCC, RVK lub GOST. Czasami te kody są jawnie powiązane w bibliotekach źródłowych, a czasami możemy je wyciągnąć z nazwy pliku lub opisu (głównie ISBN i DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Możemy używać identyfikatorów do znajdowania rekordów w <em>kolekcjach tylko z metadanymi</em>, takich jak OpenLibrary, ISBNdb lub WorldCat/OCLC. W naszej wyszukiwarce jest specjalna <em>zakładka metadanych</em>, jeśli chcesz przeglądać te kolekcje. Używamy pasujących rekordów do wypełniania brakujących pól metadanych (np. jeśli brakuje tytułu) lub np. jako „alternatywny tytuł” (jeśli istnieje już tytuł)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Aby zobaczyć dokładnie, skąd pochodzą metadane książki, zobacz <em>zakładkę „Szczegóły techniczne”</em> na stronie książki. Zawiera ona link do surowego JSON dla tej książki, z odnośnikami do surowego JSON oryginalnych rekordów."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Więcej informacji znajdziesz na następujących stronach: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> i <a %(a_example)s>Example metadata JSON</a>. Wreszcie, wszystkie nasze metadane mogą być <a %(a_generated)s>generowane</a> lub <a %(a_downloaded)s>pobierane</a> jako bazy danych ElasticSearch i MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Łączenie z Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Więc jeśli napotkasz plik z błędnymi metadanymi, jak powinieneś go naprawić? Możesz przejść do biblioteki źródłowej i postępować zgodnie z jej procedurami naprawy metadanych, ale co zrobić, jeśli plik jest obecny w wielu bibliotekach źródłowych?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Jest jeden identyfikator, który jest traktowany szczególnie na Archiwum Anny. <strong>Pole annas_archive md5 w Open Library zawsze nadpisuje wszystkie inne metadane!</strong> Najpierw cofnijmy się trochę i dowiedzmy się więcej o Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library została założona w 2006 roku przez Aarona Swartza z celem „jednej strony internetowej dla każdej książki, jaka kiedykolwiek została opublikowana”. Jest to coś w rodzaju Wikipedii dla metadanych książek: każdy może ją edytować, jest wolno licencjonowana i może być pobierana masowo. To baza danych książek, która najbardziej odpowiada naszej misji — w rzeczywistości Archiwum Anny zostało zainspirowane wizją i życiem Aarona Swartza."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Zamiast wymyślać koło na nowo, postanowiliśmy skierować naszych wolontariuszy do Open Library. Jeśli zobaczysz książkę z nieprawidłowymi metadanymi, możesz pomóc w następujący sposób:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Przejdź na <a %(a_openlib)s>stronę Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Znajdź poprawny rekord książki. <strong>UWAGA:</strong> upewnij się, że wybierasz poprawną <strong>edycję</strong>. W Open Library masz „dzieła” i „edycje”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "„Dzieło” może być „Harry Potter i Kamień Filozoficzny”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "„Edycja” może być:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Pierwsze wydanie z 1997 roku opublikowane przez Bloomsbery, liczące 256 stron."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Wydanie w miękkiej oprawie z 2003 roku opublikowane przez Raincoast Books, liczące 223 strony."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Polskie tłumaczenie z 2000 roku „Harry Potter i Kamień Filozoficzny” wydane przez Media Rodzina, liczące 328 stron."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Wszystkie te wydania mają różne numery ISBN i różną zawartość, więc upewnij się, że wybierasz właściwe!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edytuj rekord (lub stwórz go, jeśli nie istnieje) i dodaj jak najwięcej przydatnych informacji! Skoro już tu jesteś, możesz sprawić, że rekord będzie naprawdę niesamowity."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "W sekcji „Numery ID” wybierz „Anna’s Archive” i dodaj MD5 książki z Anna’s Archive. To jest długi ciąg liter i cyfr po „/md5/” w URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Spróbuj znaleźć inne pliki w Anna’s Archive, które również pasują do tego rekordu, i dodaj je również. W przyszłości możemy je pogrupować jako duplikaty na stronie wyszukiwania Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Gdy skończysz, zapisz URL, który właśnie zaktualizowałeś. Gdy zaktualizujesz co najmniej 30 rekordów z MD5 z Anna’s Archive, wyślij nam <a %(a_contact)s>email</a> i prześlij listę. Otrzymasz darmowe członkostwo w Anna’s Archive, abyś mógł łatwiej wykonywać tę pracę (i jako podziękowanie za pomoc). Muszą to być wysokiej jakości edycje, które dodają znaczną ilość informacji, w przeciwnym razie twoja prośba zostanie odrzucona. Twoja prośba zostanie również odrzucona, jeśli którakolwiek z edycji zostanie cofnięta lub poprawiona przez moderatorów Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Zauważ, że to działa tylko dla książek, nie dla prac naukowych ani innych typów plików. W przypadku innych typów plików nadal zalecamy znalezienie biblioteki źródłowej. Może minąć kilka tygodni, zanim zmiany zostaną uwzględnione w Anna’s Archive, ponieważ musimy pobrać najnowszy zrzut danych Open Library i zregenerować nasz indeks wyszukiwania."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Mirrory: wezwanie do wolontariuszy"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Aby zwiększyć odporność Archiwum Anny, szukamy wolontariuszy do prowadzenia mirrorów."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Szukamy tego:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Uruchamiasz otwartą bazę kodu Archiwum Anny i regularnie aktualizujesz zarówno kod, jak i dane."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Twoja wersja jest wyraźnie oznaczona jako mirror, np. „Archiwum Boba, mirror Archiwum Anny”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Jesteś gotów podjąć ryzyko związane z tą pracą, które jest znaczące. Masz głębokie zrozumienie wymaganej operacyjnej ochrony. Treści <a %(a_shadow)s>tych</a> <a %(a_pirate)s>postów</a> są dla Ciebie oczywiste."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Jesteś gotów przyczynić się do naszej <a %(a_codebase)s>bazy kodu</a> — we współpracy z naszym zespołem — aby to się stało."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Początkowo nie damy Ci dostępu do pobrań z serwera partnerskiego, ale jeśli wszystko pójdzie dobrze, możemy to z Tobą podzielić."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Koszty hostingu"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Jesteśmy gotowi pokryć koszty hostingu i VPN, początkowo do 200 USD miesięcznie. To wystarczy na podstawowy serwer wyszukiwania i proxy chronione przez DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Zapłacimy za hosting dopiero po tym, jak wszystko skonfigurujesz i udowodnisz, że jesteś w stanie utrzymać archiwum na bieżąco z aktualizacjami. Oznacza to, że będziesz musiał zapłacić za pierwsze 1-2 miesiące z własnej kieszeni."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Twój czas nie będzie rekompensowany (i nasz również nie), ponieważ jest to czysta praca wolontariacka."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Jeśli zaangażujesz się znacząco w rozwój i operacje naszej pracy, możemy omówić dzielenie się większą częścią dochodów z darowizn, abyś mógł je wykorzystać według potrzeb."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Rozpoczęcie"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Prosimy <strong>nie kontaktować się z nami</strong> w celu uzyskania zgody lub zadawania podstawowych pytań. Czyny mówią głośniej niż słowa! Wszystkie informacje są dostępne, więc po prostu przystąp do konfiguracji swojego mirrora."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Nie wahaj się zgłaszać ticketów lub prośby o połączenie na naszym Gitlabie, gdy napotkasz problemy. Może będziemy musieli zbudować z Tobą niektóre funkcje specyficzne dla mirrorów, takie jak rebranding z „Archiwum Anny” na nazwę Twojej strony, (początkowo) wyłączenie kont użytkowników lub linkowanie z powrotem do naszej głównej strony z stron książek."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Gdy Twój mirror będzie działał, prosimy o kontakt z nami. Chętnie przejrzymy Twoje zabezpieczenia operacyjne, a gdy będą solidne, podlinkujemy Twój mirror i zaczniemy ściślej z Tobą współpracować."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Z góry dziękujemy każdemu, kto jest gotów przyczynić się w ten sposób! To nie jest dla osób o słabym sercu, ale wzmocni to trwałość największej naprawdę otwartej biblioteki w historii ludzkości."

msgid "page.partner_download.header"
msgstr "Pobierz ze strony partnera"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Powolne pobieranie jest dostępne tylko za pośrednictwem oficjalnej strony internetowej. Odwiedź %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Powolne pobieranie nie jest dostępne przez VPN-y Cloudflare ani z adresów IP Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Proszę poczekać <span %(span_countdown)s>%(wait_seconds)s</span> sekund, aby pobrać ten plik."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Użyj linku, aby pobrać: <a %(a_download)s>Pobierz teraz</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Dziękujemy za cierpliwość, to pozwala utrzymać stronę dostępną za darmo dla wszystkich! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Ostrzeżenie: w ciągu ostatnich 24 godzin z tego adresu IP pobrano zbyt wiele plików. Pobieranie może być wolniejsze niż zwykle."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Pobrane pliki z Twojego adresu IP w ciągu ostatnich 24 godzin: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Jeśli używasz VPN, współdzielonego połączenia internetowego lub Twój dostawca usług internetowych udostępnia adresy IP, to ostrzeżenie może być z tym związane."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Aby dać każdemu możliwość pobierania plików za darmo, musisz poczekać, zanim będziesz mógł pobrać ten plik."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Możesz kontynuować przeglądanie Archiwum Anny w innej karcie, czekając (jeśli Twoja przeglądarka obsługuje odświeżanie kart w tle)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Możesz poczekać, aż załaduje się kilka stron pobierania jednocześnie (ale prosimy o pobieranie tylko jednego pliku na raz z każdego serwera)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Po uzyskaniu linku do pobrania jest on ważny przez kilka godzin."

msgid "layout.index.header.title"
msgstr "Anna’s Archive"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Rekord w Anna’s Archive"

msgid "page.scidb.download"
msgstr "Pobieranie"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Aby wspierać dostępność i długoterminowe zachowanie ludzkiej wiedzy, zostań <a %(a_donate)s>członkiem</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Dodatkowo, 🧬&nbsp;SciDB ładuje się szybciej dla członków, bez żadnych limitów."

msgid "page.scidb.refresh"
msgstr "Nie działa? Spróbuj <a %(a_refresh)s>odświeżyć</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Brak podglądu. Pobierz plik z <a %(a_path)s>Archiwum Anny</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB jest kontynuacją Sci-Hub, z jego znanym interfejsem i bezpośrednim podglądem plików PDF. Wprowadź swój DOI, aby wyświetlić."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Mamy pełną kolekcję Sci-Hub, a także nowe artykuły. Większość można przeglądać bezpośrednio za pomocą znanego interfejsu, podobnego do Sci-Hub. Niektóre można pobrać z zewnętrznych źródeł, w takim przypadku pokazujemy linki do nich."

msgid "page.search.title.results"
msgstr "%(search_input)s - Szukaj"

msgid "page.search.title.new"
msgstr "Nowe wyszukiwanie"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Uwzględnij tylko"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Wyklucz"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Niesprawdzone"

msgid "page.search.tabs.download"
msgstr "Pobieranie"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Artykuły z czasopism"

msgid "page.search.tabs.digital_lending"
msgstr "Wypożyczenie"

msgid "page.search.tabs.metadata"
msgstr "Metadane"

msgid "common.search.placeholder"
msgstr "Wyszukaj tytuł, autora, język, typ pliku, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Szukaj"

msgid "page.search.search_settings"
msgstr "Szukaj ustawień"

msgid "page.search.submit"
msgstr "Szukaj"

msgid "page.search.too_long_broad_query"
msgstr "Wyszukiwanie trwało zbyt długo, co jest częste w przypadku zbyt ogólnych zapytań. Liczba filtrów może być niedokładna."

msgid "page.search.too_inaccurate"
msgstr "Wyszukiwanie trwało zbyt długo, co oznacza, że wyniki mogą być niedokładne. Czasami pomaga <a %(a_reload)s>odświeżenie</a> strony."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Wyświetl"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabela"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Zaawansowane"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Przeszukuj opisy i komentarze metadanych"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Dodaj konkretne pole wyszukiwania"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(wyszukaj konkretną dziedzinę)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Rok wydania"

msgid "page.search.filters.content.header"
msgstr "Treść"

msgid "page.search.filters.filetype.header"
msgstr "Typ pliku"

msgid "page.search.more"
msgstr "więcej…"

msgid "page.search.filters.access.header"
msgstr "Dostęp"

msgid "page.search.filters.source.header"
msgstr "Źródło"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "zeskrobane i udostępnione jako open source przez AA"

msgid "page.search.filters.language.header"
msgstr "Język"

msgid "page.search.filters.order_by.header"
msgstr "Sortuj"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Najbardziej istotne"

msgid "page.search.filters.sorting.newest"
msgstr "Najnowszy"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(rok publikacji)"

msgid "page.search.filters.sorting.oldest"
msgstr "Najstarszy"

msgid "page.search.filters.sorting.largest"
msgstr "Malejąco rozmiarami"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(rozmiar pliku)"

msgid "page.search.filters.sorting.smallest"
msgstr "Rosnąco rozmiarami"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open source)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Losowe"

msgid "page.search.header.update_info"
msgstr "Zasoby wyszukiwarki są aktualizowane co miesiąc. Teraz zawierają dane aż do %(last_data_refresh_date)s. Dla bardziej szczegółowych informacji, spójrz na %(link_open_tag)sstronę o zasobach</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Aby przeszukać indeks według kodów, użyj <a %(a_href)s>Eksploratora Kodów</a>."

msgid "page.search.results.search_downloads"
msgstr "Wpisz frazę w pole, aby przeszukać nasz katalog %(count)s plików do pobrania, które <a %(a_preserve)s>zachowaliśmy na zawsze</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "W rzeczywistości każdy może pomóc w zachowaniu tych plików, udostępniając naszą <a %(a_torrents)s>zunifikowaną listę torrentów</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Posiadamy największy na świecie otwarty katalog książek, prac naukowych i innych prac pisemnych. Kopiujemy bazy Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>i więcej</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Jeśli znasz inne \"ukryte biblioteki\", które powinniśmy skopiować, lub jeśli masz jakiekolwiek pytania, skontaktuj się z nami pod adresem %(email)s."

msgid "page.search.results.dmca"
msgstr "W przypadku roszczeń DMCA / praw autorskich <a %(a_copyright)s>kliknij tutaj</a>."

msgid "page.search.results.shortcuts"
msgstr "Wskazówka: użyj skrótów klawiszowych \"/\" (pole wyszukiwania), \"enter\" (wyszukiwanie), \"j\" (w górę), \"k\" (w dół), aby przyspieszyć nawigację."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Szukasz artykułów naukowych?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Wpisz w polu, aby przeszukać nasz katalog %(count)s artykułów naukowych i czasopism, które <a %(a_preserve)s>zachowujemy na zawsze</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Wpisz w pole, aby wyszukać pliki w bibliotekach."

msgid "page.search.results.digital_lending_info"
msgstr "Ten indeks wyszukiwania zawiera metadane z biblioteki Internet Archive’s Controlled Digital Lending. <a %(a_datasets)s>Więcej o zbiorach danych</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Więcej cyfrowych bibliotek można znaleźć na <a %(a_wikipedia)s>Wikipedii</a> i <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Wpisz w pole, aby wyszukać metadane z bibliotek. Może być przydatne, przy <a %(a_request)s>żądaniu pliku</a>."

msgid "page.search.results.metadata_info"
msgstr "Ten indeks wyszukiwania zawiera metadane z różnych źródeł. <a %(a_datasets)s>Więcej o zbiorach danych</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Dla metadanych pokazujemy oryginalne rekordy. Nie łączymy rekordów."

msgid "page.search.results.metadata_info_more"
msgstr "Istnieje wiele źródeł metadanych dla dzieł napisanych na całym świecie. <a %(a_wikipedia)s>Ta strona Wikipedii</a> to dobry początek, ale jeśli znasz inne listy, daj nam znać."

msgid "page.search.results.search_generic"
msgstr "Wpisz w pole żeby wyszukać."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Są to rekordy metadanych, <span %(classname)s>nie</span> pliki do pobrania."

msgid "page.search.results.error.header"
msgstr "Błąd podczas wyszukiwania."

msgid "page.search.results.error.unknown"
msgstr "Spróbuj <a %(a_reload)s>odświeżyć stronę</a>. Jeśli problem nie ustąpi, prosimy o wysłanie wiadomości na adres %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Brak wyników.</span> Spróbuj inaczej sformułować zapytanie, używając mniej wyrazów i filtrów lub zmieniając je."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Czasami dzieje się to nieprawidłowo, gdy serwer wyszukiwania jest wolny. W takich przypadkach <a %(a_attrs)s>ponowne załadowanie</a> może pomóc."

msgid "page.search.found_matches.main"
msgstr "Znaleźliśmy dopasowania w: %(in)s. Możesz odwołać się do znalezionego tam adresu URL, wysyłając <a %(a_request)s>żądanie pliku</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Artykuły z czasopism (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "(%(count)s) wypożyczeń"

msgid "page.search.found_matches.metadata"
msgstr "(%(count)s) metadanych"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Wyniki %(from)s-%(to)s (%(total)s łącznie)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ częściowo pasujących wyników"

msgid "page.search.results.partial"
msgstr "%(num)d częściowo pasujących wyników"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Wolontariat i nagrody"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Archiwum Anny opiera się na wolontariuszach takich jak Ty. Zapraszamy do współpracy na każdym poziomie zaangażowania i poszukujemy pomocy w dwóch głównych kategoriach:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lekkie prace wolontariackie:</span> jeśli możesz poświęcić tylko kilka godzin tu i tam, nadal jest wiele sposobów, w jakie możesz pomóc. Nagrodzimy stałych wolontariuszy <span %(bold)s>🤝 członkostwami w Archiwum Anny</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Intensywna praca wolontariacka (nagrody od 50 do 5 000 USD):</span> jeśli możesz poświęcić dużo czasu i/lub zasobów na naszą misję, chętnie będziemy z Tobą ściślej współpracować. Ostatecznie możesz dołączyć do wewnętrznego zespołu. Mimo że mamy ograniczony budżet, możemy przyznać <span %(bold)s>💰 nagrody pieniężne</span> za najbardziej intensywną pracę."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Jeśli nie możesz poświęcić swojego czasu na wolontariat, nadal możesz nam bardzo pomóc, <a %(a_donate)s>przekazując pieniądze</a>, <a %(a_torrents)s>seedując nasze torrenty</a>, <a %(a_uploading)s>uploadując książki</a> lub <a %(a_help)s>opowiadając znajomym o Archiwum Anny</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Firmy:</span> oferujemy bezpośredni dostęp do naszych zbiorów z dużą prędkością w zamian za darowizny na poziomie przedsiębiorstwa lub wymianę na nowe zbiory (np. nowe skany, zbiory OCR, wzbogacenie naszych danych). <a %(a_contact)s>Skontaktuj się z nami</a>, jeśli to Ciebie dotyczy. Zobacz także naszą <a %(a_llm)s>stronę LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Lekki wolontariat"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Jeśli masz kilka godzin wolnego czasu, możesz pomóc na różne sposoby. Dołącz do <a %(a_telegram)s>czatu wolontariuszy na Telegramie</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "W ramach podziękowania zazwyczaj przyznajemy 6 miesięcy członkostwa „Szczęśliwy Bibliotekarz” za podstawowe kamienie milowe, a więcej za kontynuowaną pracę wolontariacką. Wszystkie kamienie milowe wymagają wysokiej jakości pracy — niedbała praca szkodzi nam bardziej niż pomaga i zostanie odrzucona. Prosimy <a %(a_contact)s>napisz do nas</a>, gdy osiągniesz kamień milowy."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Zadanie"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Kamień milowy"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Rozpowszechnianie informacji o Archiwum Anny. Na przykład poprzez polecanie książek na AA, linkowanie do naszych wpisów na blogu lub ogólnie kierowanie ludzi na naszą stronę internetową."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s linki lub zrzuty ekranu."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Te powinny pokazywać, jak informujesz kogoś o Archiwum Anny, a oni ci dziękują."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Popraw metadane, <a %(a_metadata)s>łącząc</a> je z Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Możesz użyć <a %(a_list)s >listy losowych problemów z metadata</a> jako punktu wyjścia."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Upewnij się, że zostawiasz komentarz do problemów, które naprawiasz, aby inni nie dublowali twojej pracy."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s linki rekordów, które poprawiłeś."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Tłumaczenie</a> strony internetowej."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Pełne przetłumaczenie języka (jeśli nie było już blisko ukończenia)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Popraw stronę Wikipedii Archiwum Anny w Twoim języku. Uwzględnij informacje ze strony Wikipedii AA w innych językach oraz z naszej strony internetowej i bloga. Dodaj odniesienia do AA na innych odpowiednich stronach."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link do historii edycji pokazujący, że dokonałeś znaczących wkładów."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Spełnianie próśb o książki (lub artykuły itp.) na forach Z-Library lub Library Genesis. Nie mamy własnego systemu próśb o książki, ale mirrorujemy te biblioteki, więc ich ulepszanie sprawia, że Archiwum Anny również staje się lepsze."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s linki lub zrzuty ekranu próśb, które spełniłeś."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Małe zadania zamieszczone na naszym <a %(a_telegram)s>czacie wolontariuszy na Telegramie</a>. Zazwyczaj za członkostwo, czasami za małe nagrody."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Małe zadania publikowane w naszej grupie czatowej dla wolontariuszy."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Zależy od zadania."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Nagrody"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Zawsze szukamy osób z solidnymi umiejętnościami programistycznymi lub w zakresie ofensywnego bezpieczeństwa, które chcą się zaangażować. Możesz znacząco przyczynić się do zachowania dziedzictwa ludzkości."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "W ramach podziękowania, oferujemy członkostwo za solidne wkłady. Jako ogromne podziękowanie, oferujemy nagrody pieniężne za szczególnie ważne i trudne zadania. Nie powinno to być traktowane jako zamiennik pracy, ale jest to dodatkowa motywacja i może pomóc w pokryciu poniesionych kosztów."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Większość naszego kodu jest open source, i będziemy tego wymagać również od Twojego kodu przy przyznawaniu nagrody. Istnieją pewne wyjątki, które możemy omówić indywidualnie."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Nagrody są przyznawane pierwszej osobie, która ukończy zadanie. Zachęcamy do komentowania zgłoszeń nagród, aby poinformować innych, że nad czymś pracujesz, aby inni mogli się wstrzymać lub skontaktować się z Tobą w celu współpracy. Pamiętaj jednak, że inni również mogą nad tym pracować i próbować Cię wyprzedzić. Nie przyznajemy jednak nagród za niedbałą pracę. Jeśli dwie wysokiej jakości prace zostaną złożone w krótkim odstępie czasu (w ciągu jednego lub dwóch dni), możemy zdecydować się na przyznanie nagród obu osobom, według naszego uznania, na przykład 100%% za pierwsze zgłoszenie i 50%% za drugie zgłoszenie (czyli łącznie 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "W przypadku większych nagród (zwłaszcza za zadania związane ze scrapingiem), prosimy o kontakt, gdy ukończysz ~5%% zadania i jesteś pewny, że Twoja metoda będzie skalowalna do pełnego kamienia milowego. Będziesz musiał podzielić się swoją metodą, abyśmy mogli udzielić informacji zwrotnej. W ten sposób możemy również zdecydować, co zrobić, jeśli kilka osób zbliża się do nagrody, na przykład potencjalnie przyznając ją kilku osobom, zachęcając do współpracy itp."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "OSTRZEŻENIE: zadania o wysokiej nagrodzie są <span %(bold)s>trudne</span> — może być mądrze zacząć od łatwiejszych."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Przejdź do naszej <a %(a_gitlab)s>listy problemów na Gitlabie</a> i posortuj według „Priorytet etykiety”. To pokazuje mniej więcej kolejność zadań, na których nam zależy. Zadania bez wyraźnych nagród nadal kwalifikują się do członkostwa, zwłaszcza te oznaczone jako „Zaakceptowane” i „Ulubione Anny”. Możesz zacząć od „Projektu startowego”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Aktualizacje dotyczące <a %(wikipedia_annas_archive)s>Archiwum Anny</a>, największej prawdziwie otwartej biblioteki w historii ludzkości."

msgid "layout.index.title"
msgstr "Anna’s Archive"

msgid "layout.index.meta.description"
msgstr "Największa biblioteka z otwartym dostępem do danych oraz otwartym kodem źródłowym. Umożliwia dostęp do Sci-Hub, Library Genesis, Z-Library i wielu innych."

msgid "layout.index.meta.opensearch"
msgstr "Przeszukaj Anna’s Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Archiwum Anny potrzebuje Twojej pomocy!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Wielu próbuje nas zniszczyć, ale my walczymy dalej."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Jeśli dokonasz darowizny teraz, otrzymasz <strong>podwójną</strong> liczbę szybkich pobrań."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Ważne do końca tego miesiąca."

msgid "layout.index.header.nav.donate"
msgstr "Wspomóż"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Ratowanie ludzkiej wiedzy: wspaniały prezent na wakacje!"

msgid "layout.index.header.banner.surprise"
msgstr "Zaskocz bliską osobę, podaruj jej konto z członkostwem."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Aby zwiększyć odporność Archiwum Anny, szukamy wolontariuszy do prowadzenia lustrzanych kopii."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Idealny prezent na Walentynki!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Dostępna jest nowa metoda donacji: %(method_name)s. Proszę, rozważ %(donate_link_open_tag)swsparcie nas</a> — utrzymanie tej strony nie jest tanie, więc twoja donacja ma znaczenie. Dziękujemy."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Przeprowadzamy zbiórkę na <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">duplikację</a> największej ukrytej biblioteki komiksów na świecie. Dziękujemy za wsparcie! <a href=\"/donate\">Wesprzyj</a> Jeżeli nie jesteś w stanie wesprzeć nas finansowo, rozważ wspomnienie o nas swoim przyjaciołom i śledzenie nas na <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddicie</a> oraz <a href=\"https://t.me/annasarchiveorg\">Telegramie</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Ostatnie pobrania:"

msgid "layout.index.header.nav.search"
msgstr "Szukaj"

msgid "layout.index.header.nav.faq"
msgstr "Najczęściej Zadawane Pytania"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Popraw metadane"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Wolontariat i nagrody"

msgid "layout.index.header.nav.datasets"
msgstr "Zbiory danych"

msgid "layout.index.header.nav.torrents"
msgstr "Torrenty"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktywność"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Eksplorator kodów"

msgid "layout.index.header.nav.llm_data"
msgstr "Dane LLM"

msgid "layout.index.header.nav.home"
msgstr "Strona główna"

msgid "layout.index.header.nav.annassoftware"
msgstr "Oprogramowanie Anny ↗"

msgid "layout.index.header.nav.translate"
msgstr "Przetłumacz ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Zaloguj / Zarejestruj"

msgid "layout.index.header.nav.account"
msgstr "Konto"

msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

msgid "layout.index.footer.list2.header"
msgstr "Kontakt"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / roszczenia dotyczące praw autorskich"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Zaawansowane"

msgid "layout.index.header.nav.security"
msgstr "Bezpieczeństwo"

msgid "layout.index.footer.list3.header"
msgstr "Alternatywne domeny"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "niezwiązany z żadną organizacją"

msgid "page.search.results.issues"
msgstr "❌ Z tym plikiem mogą występować problemy."

msgid "page.search.results.fast_download"
msgstr "Szybkie pobieranie"

msgid "page.donate.copy"
msgstr "kopiuj"

msgid "page.donate.copied"
msgstr "skopiowano!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Poprzednie"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Następne"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Lustro nr %(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% pisanego dziedzictwa ludzkości zachowane na zawsze %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Zbiory danych ▶ Pliki ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Pobierz:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Mamy kilka metod pobierania, na wypadek gdyby jedna z nich nie działała. Każdą z nich pobierzesz dokładnie ten sam plik."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Anna's Archive nie przechowuje żadnych pokazanych tutaj plików. To są tylko i wyłącznie linki do innych stron. Jeśli masz prawomocną skargę DMCA, proszę spojrzeć na %(about_link)sstronę \"O nas\"</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Anonimowy mirror Z-Library #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Wesprzyj"

#~ msgid "page.donate.header"
#~ msgstr "Wesprzyj"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive jest otwarto-źródłowym projektem non-profit, obsługiwanym w pełni przez wolontariuszy. Przyjmujemy donacje na pokrycie naszych kosztów, w tym hostingu, nazw domen, rozwoju oprogramowania, i innych wydatków."

#~ msgid "page.donate.text2"
#~ msgstr "Dzięki waszym datkom jesteśmy w stanie utrzymać tą stronę, poprawiać ją, i archiwizować jeszcze więcej książek."

#~ msgid "page.donate.text3"
#~ msgstr "Ostatnie donacje: %(donations)s. Dziękujemy wszystkim za waszą szczodrość. Naprawdę doceniamy zaufanie nam z jakąkolwiek kwotą, na jaką możecie sobie pozwolić."

#~ msgid "page.donate.text4"
#~ msgstr "By nas wesprzeć, wybierz metodę płatności poniżej. Jeśli napotkasz jakieś problemy, proszę, skontaktuj się z nami pod %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Karta płatnicza"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Kryptowaluty"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Pytania"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Otwórz %(link_open_tag)stą stronę</a> i podążaj za instrukcjami, skanując kod QR, albo klikając link do “paypal.me”. Jeśli coś nie działa, możesz spróbować odświeżyć stronę, by dostać inne, działające konto."

#~ msgid "page.donate.cc.header"
#~ msgstr "Karta płatnicza"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Używamy Sendwyre, by pieniądze trafiły prosto do naszego portfela Bitcoin. To powinno potrwać około 5 minut."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Ta metoda pozwala na transakcje o wartości przynajmniej $30, i pobiera około $5 opłaty."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Kroki:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Skopiuj adres naszego portfela Bitcoin (BTC): %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Przejdź do %(link_open_tag)stej strony</a> i kliknij \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Skopiuj adres naszego portfela i podążaj za instrukcjami"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Kryptowaluty"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(także działa dla BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Proszę użyć %(link_open_tag)stego konta Alipay</a> żeby przesłać dotację. Jeśli wystąpi jakiś problem, prosimy o odświeżenie strony."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "W tej chwili nie obsługujemy tej metody donacji. Zajrzyj tu później. Dziękujemy za chęć wsparcia, bardzo ją doceniamy!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Prosimy użyć%(link_open_tag)stej strony Pix</a> żeby wysłać dotację. Jeśli to nie zadziała, prosimy o odświeżenie strony."

#~ msgid "page.donate.faq.header"
#~ msgstr "Często zadawane pytania"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna's Archive</span> jest projektem chcącym skatalogować wszystkie istniejące książki, zbierając dane z różnych źródeł. Śledzimy także postęp ludzkości ku umożliwieniu łatwego dostępu do wszystkich tych książek w formie cyfrowej poprzez “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteki cienia</a>” <a href=\"/about\">Dowiedz się więcej.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Książka (Dowolna)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Dom"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Zbiory danych ▶ISBN ▶ %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Nie znaleziono"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” nie jest poprawnym numerem ISBN. Mają one po 10 lub 13 znaków, nie licząc opcjonalnych myślników. Wszystkie znaki muszą być cyframi, poza ostatnim, który może także być “X”em. Jest on “znakiem kontrolnym”, wyliczonym na podstawie pozostałych cyfr. Cały ten numer musi także być w poprawnym zakresie przyznanym przez Międzynarodową Agencję ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Pasujące pliki w naszej bazie danych:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Nie znaleziono pasujących plików w naszej bazie danych."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Wyszukiwarka ▶ %(num)d wyników dla <span class=\"italic\">%(search_input)s</span> (w metadanych ukrytych bibioltek)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Wyszukiwarka ▶ %(num)d wyników dla <span class=\"italic\">%(search_input)s</span> (w metadanych ukrytych bibliotek)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Wyszukiwarka ▶ Błąd wyszukiwania dla <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Szukaj ▶ Nowe wyszukiwanie"

#~ msgid "page.donate.header.text3"
#~ msgstr "Można także dokonać wsparcia bez konieczności zakładania konta:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Wspomóż jednorazowo (brak dodatkowych korzyści)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Wybierz opcję płatności. Prosimy o wybór płatności za pomocą kryptowalut %(bitcoin_icon)s, ponieważ ponosimy (dużo) mniej kosztów."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Jeśli posiadasz już kryptowaluty, te adresy należą do nas."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Bardzo dziękujemy za wsparcie! Ten projekt nie byłby możliwy bez ciebie."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Aby wspomóc za pomocą PayPal, należy użyć PayPal Crypto, które pozwoli nam pozostać anonimowymi. Doceniamy Twój wysiłek włożony w naukę dokonywania wsparcia za pomocą tej metody, ponieważ wiele to dla nas znaczy."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Wykonaj instrukcje aby zakupić Bitcoin (BTC). Wystarczającym jest zakupienie takiej ilości kryptowaluty, która będzie podlegała darowiźnie."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Jeżeli utracisz Bitcoiny ze względu na wahania kursu lub opłat, <em>nie martw się</em>. Taka sytuacja na rynku kryptowalut jest czymś zwyczajnym, kiedy nam pozwala funkcjonować anonimowo."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Wprowadź nasz adres Bitcoin (BTC) jako odbiorcę, a następnie podążaj za instrukcjami aby wysłać swoją darowiznę:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Proszę, użyj <a %(a_account)s> tego konta Alipay</a> aby wysłać swoją darowiznę."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Proszę, użyj <a %(a_account)s tego konta Pix </a> aby wysłać swoją darowiznę."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Jeśli twojej preferowanej metody płatności nie ma na liście, najłatwiej będzie ci pobrać <a href=\"https://paypal.com/\">PayPal </a> lub <a href=\"https://coinbase.com/\">Coinbase</a> na swoim telefonie, i kupić trochę Bitcoinów (BTC). Możesz później wysłać je na nasz adres: %(address)s. W większości krajów powinno to zająć nie więcej niż kilka minut."

#~ msgid "page.search.results.error.text"
#~ msgstr "Spróbuj <a href=\"javascript:location.reload()\">odświeżyć stronę</a>. Jeśli problemy nie ustąpią, skontaktuj się z nami na <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddicie</a> oraz <a href=\"https://t.me/annasarchiveorg\">Telegramie</a>."

#~ msgid "page.donate.login"
#~ msgstr "Aby został członkiem, <a href=\"/login\">Zaloguj się lub Zarejestruj</a>. Jeżeli nie chcesz zakładać konta, wybierz opcję \"Wspomóż jednorazowo\", znajdującą się powyżej. Dziękujemy za wsparcie!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Strona Główna"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "O nas"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Wesprzyj"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Zbiory danych"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Aplikacja mobilna"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog Anny"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Oprogramowanie Anny"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Przetłumacz"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Serwery lustrzane %(libraries)s i więcej."

#~ msgid "page.home.preservation.text"
#~ msgstr "Utrwalamy książki, prace naukowe, komiksy, magazyny oraz inne materiały, poprzez uzyskanie dostępu do nich z <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">różnych ukrytych bibliotek</a> w jednym miejscu. Całość danych jest zachowana wiecznie, poprzez ułatwienie masowej duplikacji danych. W wyniku tego kopie pozostają bezpiecznie zachowane w wielu lokalizacjach na całym świecie. To szerokie rozpowszechnienie danych wraz z otwartoźródłowym kodem czyni naszą stronę internetową odporną na zajęcie i przymusowe zdjęcie witryny. Dowiedz się więcej na temat naszych <a href=\"/datasets\">zbiorów danych</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Zbiory ▶ DOI ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Nie znaleziono"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" nie wygląda jak DOI. Powinien on się zaczynać od \"10.\" i mieć w sobie ukośnik przedni."

#~ msgid "page.doi.box.header"
#~ msgstr "Cyfrowy identyfikator dokumentu elektronicznego: %(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Kanoniczny URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Ten plik może być w %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Pasujące pliki w naszej bazie danych:"

#~ msgid "page.doi.results.none"
#~ msgstr "Nie znaleziono żadnych pasujących plików w naszej bazie danych."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Szybkie pobieranie</strong> Wyczerpano wszystkie dostępne szybkie pobrania na dziś. Skontaktuj się z Anną pod %(email)s, jeżeli potrzebujesz zmienić próg swojego członkostwa."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Wykorzystano limit szybkich pobierań w dniu dzisiejszym. Skontaktuj się z Anną pod %(email)s, jeżeli potrzebujesz zmienić próg swojego członkostwa."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Czy mogę pomóc w inny sposób?</div> Tak! Spójrz na sekcję \"Jak możesz pomóc?\" na <a href=\"/about\">stronie \"O nas\"</a>."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Nie podoba mi się w jaki sposób \"zarabiacie\" na Anna's Archive!</div> Jeżeli nie lubisz w jaki sposób prowadzimy projekt, zachęcamy do prowadzenia własnej, ukrytej biblioteki! Cały nasz kod oraz dane są otwartoźródłowe, więc nic stoi na przeszkodzie. ;)"

#~ msgid "page.request.title"
#~ msgstr "Poproś o książki"

#~ msgid "page.request.text1"
#~ msgstr "W chwili obecnej prosimy o składanie próśb o książki na <a %(a_forum)s> forum Libgen.rs</a>. Można w tym miejscu utworzyć konto i zgłosić zapotrzebowanie w jednym z poniższych wątków:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s> Dla książek elektronicznych, użyj <a %(a_ebook)s>tego wątku</a>.</li><li %(li_item)s> Dla książek które nie są dostępne jako książki elektroniczne, użyj <a %(a_regular)s> tego wątku</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "W obu przypadkach należy podążać za instrukcjami wspomnianymi w wątkach."

#~ msgid "page.upload.title"
#~ msgstr "Przekaż plik"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Duże pliki"

#~ msgid "page.about.title"
#~ msgstr "O nas"

#~ msgid "page.about.header"
#~ msgstr "O nas"

#~ msgid "page.home.search.header"
#~ msgstr "Szukaj"

#~ msgid "page.home.search.intro"
#~ msgstr "Przeszukaj nasz katalog z zawartością z ukrytych bibliotek."

#~ msgid "page.home.random_book.header"
#~ msgstr "Losowa publikacja"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Przejdź do losowej publikacji z katalogu."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Losowa publikacja"

#~ msgid "page.about.text1"
#~ msgstr "Anna's Archive jest niekomercyjną, otwartoźródłową wyszukiwarką dla \"<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ukrytych bibiliotek</a>\". Została stworzona przez <a href=\"http://annas-blog.org\">Annę</a>, która uznała, że potrzebna jest centralna wyszukiwarka książek, prac naukowych, komiksów, magazynów, i innych dokumentów."

#~ msgid "page.about.text4"
#~ msgstr "Jeśli chcesz wnieść skargę opartą na DMCA, spójrz na stopkę, lub skontaktuj się z nami pod %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Przeglądaj książki"

#~ msgid "page.home.explore.intro"
#~ msgstr "Wybraliśmy zarówno popularne książki, jak i te niosące specjalne znaczenie w świecie ukrytych bibliotek oraz archiwizacji cyfrowych danych."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat (nieoficjalny)"

#~ msgid "page.wechat.body"
#~ msgstr "Posiadamy nieoficjalny WeChat, prowadzony przez członka społeczności. Aby uzyskać dostęp, użyj dostępnego kodu."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "O nas"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Aplikacja mobilna"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "WeChat (nieoficjalny)"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Poproś o książki"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Przekaż plik"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Jak możesz pomóc"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Przekaż darowiznę równą %(total)s używając <a %(a_account)s> poniższego konta Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Lub, możesz wrzucić je do Z-Library <a %(a_upload)s>tutaj</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "tylko w tym miesiącu!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>wstrzymał</a> przesyłanie nowych artykułów."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Wybierz opcję płatności. Przyznajemy zniżki przy płatości kryptowalutami %(bitcoin_icon)s, ponieważ ponosimy (dużo) mniejsze koszty."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Wybierz opcję płatności. W chwili obecnej obsługiwane są jedynie płatności kryptowalutami %(bitcoin_icon)s, ponieważ komercyjni dostawcy płatności odmawiają współpracy z nami."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nie możemy bezpośrednio obsługiwać kart kredytowych/debetowych, ponieważ banki nie chcą z nami współpracować. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Jednak istnieje kilka sposobów na użycie kart kredytowych/debetowych, korzystając z naszych innych metod płatności:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Pobierania wolne oraz z zewnętrznych serwerów"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Pobrania"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Jeżeli korzystasz z kryptowalut po raz pierwszy, proponujemy użycie %(option1)s, %(option2)s, lub %(option3)s by kupić i podarować Bitcoin (najpowszechniejsza kryptowaluta)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 linków do rekordów, które poprawiłeś."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 linków lub zrzutów ekranu."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 linków lub zrzutów ekranu spełnionych próśb."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Jeśli jesteś zainteresowany mirroringiem tych datasetów do celów <a %(a_faq)s>archiwalnych</a> lub <a %(a_llm)s>szkolenia LLM</a>, skontaktuj się z nami."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Jeśli jesteś zainteresowany mirroringiem tego zestawu danych do celów <a %(a_archival)s>archiwalnych</a> lub <a %(a_llm)s>szkolenia LLM</a>, prosimy o kontakt."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Strona główna"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informacje o krajach ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Jeśli jesteś zainteresowany lustrzanym odbiciem tego zestawu danych do celów <a %(a_archival)s>archiwalnych</a> lub <a %(a_llm)s>szkolenia LLM</a>, prosimy o kontakt."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Międzynarodowa Agencja ISBN regularnie wydaje zakresy, które przydzieliła krajowym agencjom ISBN. Na tej podstawie możemy określić, do jakiego kraju, regionu lub grupy językowej należy dany ISBN. Obecnie korzystamy z tych danych pośrednio, za pomocą biblioteki Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Zasoby"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ostatnia aktualizacja: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Strona internetowa ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadane"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Z wyłączeniem „scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Naszą inspiracją do zbierania metadanych jest cel Aarona Swartza „jedna strona internetowa dla każdej książki, jaka kiedykolwiek została opublikowana”, dla którego stworzył <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ten projekt radzi sobie dobrze, ale nasza unikalna pozycja pozwala nam uzyskać metadane, których oni nie mogą."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Inną inspiracją była nasza chęć poznania <a %(a_blog)s>ile książek jest na świecie</a>, abyśmy mogli obliczyć, ile książek musimy jeszcze uratować."

#~ msgid "page.partner_download.text1"
#~ msgstr "Aby dać wszystkim możliwość bezpłatnego pobierania plików, musisz poczekać <strong>%(wait_seconds)s sekund</strong> przed pobraniem tego pliku."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automatycznie odśwież stronę. Jeśli przegapisz okno pobierania, licznik czasu zostanie zresetowany, więc zaleca się automatyczne odświeżanie."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Pobierz teraz"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konwertuj: użyj narzędzi online do konwersji między formatami. Na przykład, aby konwertować między epub a pdf, użyj <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: pobierz plik (obsługiwane są pdf lub epub), a następnie <a %(a_kindle)s>wyślij go na Kindle</a> za pomocą strony internetowej, aplikacji lub e-maila. Przydatne narzędzia: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Wspieraj autorów: Jeśli ci się podoba publikacja i możesz sobie na to pozwolić, rozważ zakup oryginału lub bezpośrednie wsparcie autorów."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Wspieraj biblioteki: Jeśli ta publikacja jest dostępna w lokalnej bibliotece, rozważ jej bezpłatne wypożyczenie."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Niedostępne bezpośrednio w dużych ilościach, tylko w półhurtowych ilościach za paywallem"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Archiwum Anny zarządza kolekcją <a %(isbndb)s>metadanych ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb to firma, która zbiera metadane ISBN z różnych księgarni internetowych. Archiwum Anny tworzy kopie zapasowe metadanych książek z ISBNdb. Te metadane są dostępne przez Archiwum Anny (choć obecnie nie w wyszukiwarce, chyba że wyraźnie wyszukasz numer ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Szczegóły techniczne znajdują się poniżej. W pewnym momencie możemy użyć tych danych, aby określić, które książki nadal brakuje w bibliotekach cieni, aby priorytetowo znaleźć i/lub zeskanować te książki."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nasz wpis na blogu o tych danych"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Zbieranie danych z ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Obecnie mamy pojedynczy torrent, który zawiera 4,4 GB skompresowany plik <a %(a_jsonl)s>JSON Lines</a> (20 GB po rozpakowaniu): „isbndb_2022_09.jsonl.gz”. Aby zaimportować plik „.jsonl” do PostgreSQL, możesz użyć czegoś takiego jak <a %(a_script)s>ten skrypt</a>. Możesz nawet przesyłać go bezpośrednio, używając czegoś takiego jak %(example_code)s, aby rozpakowywał się na bieżąco."

#~ msgid "page.donate.wait"
#~ msgstr "Zaczekaj przuynajmniej <span %(span_hours)s>dwie godziny</span> (a potem odśwież stronę) zanim skontaktujesz się z prośbą o pomoc."

#~ msgid "page.codes.search_archive"
#~ msgstr "Szukaj w Archiwum Anny dla „%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Wspomóż za pomocą Alipay lub WeChat. Możesz wybrać którąś z tych opcji na kolejnej stronie."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Rozpowszechnianie informacji o Archiwum Anny w mediach społecznościowych i na forach internetowych, polecając książki lub listy na AA, lub odpowiadając na pytania."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Kolekcja literatury pięknej się rozeszła, ale nadal ma <a %(libgenli)s>torrenty</a>, choć nieaktualizowane od 2022 roku (mamy bezpośrednie pobierania)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Archiwum Anny i Libgen.li wspólnie zarządzają kolekcjami <a %(comics)s>komiksów</a> i <a %(magazines)s>magazynów</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Brak torrentów dla kolekcji rosyjskiej literatury pięknej i standardowych dokumentów."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Nie ma dostępnych torrentów dla dodatkowej zawartości. Torrenty znajdujące się na stronie Libgen.li są mirrorami innych torrentów wymienionych tutaj. Jedynym wyjątkiem są torrenty z literaturą piękną zaczynające się od %(fiction_starting_point)s. Torrenty z komiksami i magazynami są wydawane we współpracy między Archiwum Anny a Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Z kolekcji <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> dokładne pochodzenie niejasne. Częściowo z the-eye.eu, częściowo z innych źródeł."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

