��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b O  sd =  �f .   h �   0h   ,i �  .k `  �l   Rn J   bo l   �o H   p c   cp r  �p T  :r �   �s �  St �   v �  �v �  �x �  Lz �   "| �  �| �  �~ 3   9� �   m� L   5� �  ��    \�   y� H   �� #   օ 
   ��    � 9   �    Y�    y� &   ��    ��     ˆ 
   � :   �� )  2�   \� J   a� 0   �� 	   ݉    �    �    �    �    +� 	   8�    B�    S� "   Z�    }�    �� 	   �� 
   ��    ��    ��    ׊    �   � M  -�    {� .   �� ,  *� �   W� �   � �   ��    �� �   В    f� �   �� !   � �   A�    ?� V  \� 0   �� S   � 
   8� &  C� *  j� �  ��   D�    ]� F   l� T   �� �   � (   �� %   ҝ d   �� 3   ]� 5   �� -   Ǟ    �� v   �� �  q� 8   &� g   _� �   ǡ @   s�   �� (   ǣ �   � �   � �   p� I   �� �   G� �   � H   �� �   ӧ �   �� S   g� 6   �� m   �    `� �   l�   �� H   � 
   [� �   f� �   �    ­ l  ʭ �   7� �   ۯ u  ذ �   N� :  3� �   n� Q   g� <   �� O   �� `   F� e   �� I   
� +   W� �   �� P   � <   c�    �� i   �� O   � �   g� �   �    �� �   �� �   �� N  ��    � �   � �   �� (  j� �   �� �   9� �   �� Y  g� �   ��    ]�    ^� �   k� �   	� �   �� a  ��    �� �   |� �   � �  �� s   n�   �� �   �� #   �� Q    � �   r� F    � O  G� �   �� c   C� n   ��    �   5� �   K� p  
�    {� Y   �� �   �� �   y� �   *� �    � �  �� |  �� %   � C  2� O  v�   �� q  ��   L�   X� P   ]� �   ��    ;� �   D� )  �� j  &� �   �� �   �� �   j� 
   �� 9  
� /  D� �   t�    [� �   j� /  � �   H� �  � �   � V   ��    >� p  K� \  �� 
   � �   $� �   �   �� �    U  � b   1 J   � �   � 2   �     6   �   U   @ 1   W	 H   �	 �  �	 p   � j    `   � U  �    9    Y j  t �   � �   � >   & �   e (   � �    �   �    � 
   � #   � 7    /   S 6   � O   � l   
    w *   � 9   � B   � "   1 2   T )   � _   �            ,   - �   D �    u    �   | 2  3    f �   w �   B �   � I  ` X  �  �  " �  �# p  h% H   �& /   "' &   R' �   y' �  ( �  �)   x+ )  �, �   �- �  �.   �0 -   �1   �1   �2 T  �3    <5 �   L5 ;  .6 z   j7 J  �7 �   09     : '   : c   4: 
   �: w  �: �  = .  �> �   �@ �   oA 
   &B �   4B �   C H   �C p    D   �D �   �E �   �F p  G ^   �H   �H    �I �  J �   �K �  �L 4  [N �  �P �   KR 
   �R �   	S �  �S    �U G  �U �  �V �  �X   �Z    �\   �] H   �^ �  _ �   �` }   �a ;   b O   Nb    �b :   �b $   �b )   c !   .c    Pc �   gc �  �c �  �f �  �h �   xj 	   Mk �  Wk �  )n @  p [  Iq 3  �r .  �t 
   w �   w    x    x �  x   �y �  �{ 0  �}    � �  � �  �� �  Q� �  $� �  ��   �� A  ��     ݌ �   �� m   �� �  )� �  Ð �   M� �   � �   t� h  �    w� �   �� C   (� E   l�    �� 4   Ɩ 0   �� �  ,� p  )� v  �� )   � |  ;� �   ��    R� �   X� <   � �   ?� �   �� )   � `   ��    
� O  �   k� �  t� Y  � s   o�    � k   ��   c�    u� �   {� �  � �   �� 3   <� 0   p� >  �� .   � <  � t  L� �  ��     �� �   �� �   7�   �� {  ӹ 
   O� �   Z� ,   9� �   f� �   � �   �� �   }� M   ?� .   �� �   �� 5   �� �  �� =  c� �   �� �   _� W   P� 7   �� �   ��   z� ]  �� �  �� �  �� 2   H� �   {�   E� 1  _� �   �� �  U� �  (� .   �� �  �� &   �� �  �� Q   ��    �   � �  � `  �� �   `�   E� �  N� �  :� �    � �  �� �  l� �   �� �  }� @  H� 5   �� �   �� s   D�    ��    �� )  �� �   � 3   ��   �� �  ��   �� �  �� �  b� �   �� �  �� �   �    � �   !�    �� U  ��    6�    R�    j�    p�     ��    ��    ��    ��    ��    ��    ��    �� 	   ��    �    �    +�     3�    T�    W�    ]�    e� �   j� R   2     �  
   �  #   �  "   �  &   �  #   !    E 	   e 
   o    z    �    �    �    � 
   �    �    � '   �        .     G $   h $   � %   �    � !   � 1       C E   S 9   �    � G   � A   !    c     !   �    �    �    �    �    �            #    0    F 	   S 
   ]    h    k    �    � 	   �    � 	   �    �    �    � 	   �    �            %    -    H    f 
   n 	   |    � "   �    �    � !   �    �    � 	           '    -    < b   N �   � L   9 �   � �  '	 c   �
    G    X 
   p    ~    �    �    � !   � P   � ,   * W   W "   � -   � )    
 V   *
 ^   �
 �   �
 W   � ;       M 
   m    {    �    �    �    �    �    �    �    �    �    �    �    �            '    4    C    J    S 
   \    j    �    �    �    �    �     �    � :   � W   
 $   e &   � 1   �    �    �    � k   �    b    h $   y k   �    
    " f   3    �   � U   � �   # �   � �   2 2   � �    s   � 8  r �   �    �    � ?   � <   � J   , \   w Z   � G   / _   w     � +   �    $  
   )  =   7  "   u     �     �     �     �  a   �  
   0!     ;! A   \!    �!    �! M   �! N   " B  m"    �#    �# �   �#    �$    �$    �$    �$    �$ +   �$ =  �$    -&    J&    V&    e& "  n&     �'    �'    �' b   �'    $(    *( 5   2(    h(    �( $   �( �   �(    4)    D) F   S)    �)    �)    �)    �) !   �) E   �) 
   ?* 1   J* �   |* U   + B   f+ 
   �+ �   �+ H   �,    �, 3   �,    0- �   B- �   �-    �. ?   �. �   �. �   \/    0 "   (0    K0 C  X0    �1 "   �1    �1 "   �1    2 �   :2    �2    �2 $   �2 =   3 	   ]3    g3    ~3    �3 5  �3 d  �5 �  P7 1   =9 -   o9    �9    �9 !  �9 �   �: �   �;    T< �   l< �  G=    �>    �> �  ? #  A    ,B    5B .   BB 	   qB   {B    �C /  �C �  �D �   |F    XG g   oG +   �G #   H p   'H (  �H �   �I �   �J �  WK o   �L �   \M R   DN �   �N �   QO H   �O �   @P ,   �P $   Q    @Q    QQ    ZQ %   kQ    �Q A   �Q .   �Q 	   R *   'R   RR &   qS �   �S v   oT ~   �T $   eU    �U    �U    �U $   �U    �U (   V    @V �   `V #   5W �   YW    
X �   "X y   �X }   ]Y -  �Y �   	[ *   �[ �   �[ 	   o\ �   y\ a   s]    �] �  �]    x_    �_    �_    �_ &   �_    �_    �_ J   �_ �   D` �   �`    �a    �a    �a �   �a   �b �   �c w   Xd    �d    �d    �d    e    $e    6e    Ee ;   Me +   �e �  �e M   yg    �g b   �g ]   ;h N   �h f   �h S   Oi Q   �i    �i `   �i H   \j �   �j R   /k K   �k    �k �   �k `   �l 9   ,m `   fm W   �m 5   n    Un 2   ^n o   �n �   o 1   �o �   �o    mp �   tp C   hq ^   �q �   r    �r �  �r �   ht    0u    Au    ^u    eu �   lu *   Wv d   �v �   �v �   �w �   lx �    y �   �y �   dz �   { X   �{   | g   6} R  �} �   �~ �   � 
   �� 
   �� 
   �� �   �� 
   A� 
   O� I   ]� Y   �� �   � 
   ҂ �   �� 5   Ƀ �   �� <   �� s   �� a   2� 
   �� �   �� 
   z� 
   �� 
   ��   �� �   �� �  1�    �    �� 
   � �   �    ��    ۋ �   �� �   �    ��    ��    �� 8   ԍ ;   
� 1   I�    {�    �� �   ��    �� �  �� �   G� �   .� J   �� �   ��   �� �  �� "  h� �   �� �   *� q   ��    &� c  ;�    �� e  �� �   #�   � �   � $  �� �   �� �   ]� {   � '   ]� 6   ��    �� *   Ѣ 
   ��    �    � �   %�    �� b   � -   h�    ��    ��    ��     �� j   ̤ ;   7� ,   s� �   �� )   ��    æ    ˦    Ц    �    � 	   �    #� 	   +�    5� 	   >�    H� 	   Q� *   [� �   ��    $� 
   5�    C� 
   O�    ]� 
   j�    x� 
   ��    �� 
   �� $   �� ^   ܨ 
   ;� ,   I� T   v� �   ˩ �   q� �   '�   ϫ �   Ԭ 
  x�    �� �   �� 0   � C   I� E   �� �   ӯ �   k� E   7� Z   }�    ر ^   � a   I� �   ��    /�     6�    W�    h� 	   }�    ��    ��    ��    ų    γ    �    ��    �    '�    =�    Z�    a�    r�    �    ��    �� "   �� !   ˴ v   � �   d� %   � "   � V   2� P   �� t   ڶ !   O� )   q� *   �� ;   Ʒ 8   � %   ;� �   a� �   �   	�    � 8   6� u   o� #   � �   	� �   ߼ y   �� 9   �    R� �   j� �   �� `   � )   � V   
� �   a�    .� )   C�    m� 7   �� r   �� k   .�    ��    ��    ��    �� �   �� 1   U�    �� 1   �� !   ��    ��     � F   3�    z� _   �� <   �� �   -� B   � J   N� �   �� ?   =� !   }�    �� !   ��     �� !   �     $� !   E� 2   g� 1   �� '  �� ?   �� 3   4� A   h� '   ��    �� g   �� |   A� �   �� �   o�    � z    � %   ��    �� �   ��    �� &   �� 4   �� 1   �� Q   $� J   v� D   ��    �    � �   =� /   �� #   � H   /� �   x� 8   Z� �   �� [   � Z   t� i   ��     9� +   Z� v   ��    �� T   �    m� �   �� 1   -� >   _�    ��    �� �   �� ?   �� B   �� }   %� \   �� C    � s   D� {   ��    4�    ;� H   N� ?   ��    �� $   ��    �    �    2� -   :� �   h� g   ��    \�    q� $   �� #   �� #   �� v   �� 4   t� z   �� Y   $�    ~� d   �� �   � .   �� S   �    V� D   j� ?   ��    �� q   � q   u� +   �� L   �    `� .   q� S   ��    �� f   � (   o�    �� *   �� J   �� �   $�    ��    ��    
� =   $�    b� R   y� 7   �� �   � x   �� 
   � 1   )� �   [�    � �   � <   �� #   �� G   � �   f�    F�    N�    P�    R� !   e� 3   �� D   ��     � 
   �    (� "   1� 7   T� 3   ��    �� B   �� ;   � 
   G�    U� (   n�    ��    �� R   �� �   � D   �� 
   5�    C� �   P� +  �� N   !�    p� F  �� }  �� '   I� n   q�    ��   �� )   � f   F�    �� 
   �� �  ��    |� H   �� i   �� f   H� S   ��    � `   � 2   ~�    �� r   �� @   <� 1   }� W   �� 0   � P   8� }   �� �   � !   �� �   �� C  _� �   �  -   P N  ~ �   � �   � 
  c �   n #        9 �   Z ,   � �   a    	 F   b	    �	    �	 �  �	    l �   q $  A #  f
   � >   � E   � T    -   p #   � M   � K       \    i 4   �    �    � &   � U    )   o    � [   � �   � �   �    _ 
   q     @   � Y   � S   ! �   u b   4    � !   � �   �    � �   � �  5 �   � O   � -   �        !    & ?   * *   j }   � �    T   �    F    Y %   l    � T   �     ;       N +   S "       �    � O   �                    <     Y  h   ]  �   �  V   �! X   �! D   9" �   ~"    =#    F# �   ]# �   
$ �   �$    �% i   �% A   �% 8   :& Z   s& e   �& F   4'    {' R   �'    �'    �'    (    (    5(    J(    `(    r(    �(    �( -   �( -   �( -   �(     ') 2   H) +   {)    �)    �)    �) <   �)    /* 
   F* ,   Q* '   ~* P   �* (   �*     +    6+ !   E+    g+    �+ D   �+ -   �+ B   
, �   M,    �,    -     &-    G- 0   ]- 	   �-    �-    �- \   �-    . #   =.    a. 
   h. 	   s. /   }.    �. �   �. 
   �/    �/    �/ #   �/    
0    (0    A0     `0 $   �0 '   �0    �0    �0 T   �0 $   H1    m1    �1 2   �1 D   �1    2 .   /2    ^2 e   |2 Q   �2 A   43    v3    ~3 	   �3    �3    �3    �3 �  �3 b   g5    �5    �5 *   �5    6 %   6    A6    F6 s   O6 =   �6 �   7    �7 !   �7 u   �7 !   l8    �8     �8 #   �8 -   �8 #   9    C9 	   ]9 2   g9    �9 &   �9 5   �9    : ;   0; =   l; B   �;    �; ~   <    �<    �< O   �<    = !   =    := B   M= ;   �=    �= �   �= 
   �> 
   �>    �>    �>    �>    �>    �>    ?    '? V   9? �   �?    �@     �@ �   �@ �   �A    0B    DB    _B    rB    �B    �B    �B    �B    �B    �B    �B    C 
   C    -C    <C    NC    [C    gC !   wC �   �C �   jD j  [E �  �F �  �H �   LJ e   K 
   �L �   �L    �M �   �M �   nN }  OO �   �P A  �Q 5   �R �   �R E   �S    �S >   T G   CT c   �T f   �T �   VU �   �U �   �V '  cW    �Y �   �Y �   OZ [   +[ �   �[    #\ �   4\ 1  �\ �   0^ �   �^    �_ ^   �_ j   ` �   �` y   va g   �a �   Xb    �b    �b    c =   .c )   lc    �c �   �c ?   %d b   ed    �d �   �d �   ne ;   �e Q   2f 7   �f g   �f W   $g '   |g x   �g [   h {   yh t   �h    ji +   oi "   �i U   �i 3   j    Hj    Mj D   Sj    �j    �j    �j 1   �j #   �j 8   k    Ok    jk    wk    �k 	   �k P   �k p   �k I   Rl 1   �l    �l    �l )   �l    m    )m    2m    9m    Am    Im    Nm    Tm    ]m    fm    om 
   wm    �m 
   �m    �m    �m    �m    �m    �m    �m    �m    �m    n s   /n    �n O   �n �   o    �o    �o 	   �o    �o    �o    �o    �o �   �o y   �p >   q 
   Jq    Xq b   mq 
   �q l   �q    Kr     �r    �r {    s �   |s Q   >t �   �t [   Ju )   �u }   �u    Nv    fv A   v �   �v    Bw �   ]w v   �w �   ^x N   y    Vy    fy    my    }y    �y    �y 
   �y    �y    �y c   Ez y   �z �   #{ �   �{ [   �| I   } �  P} f  �~ �   >� �   2�   �� 0      � �   ��   �� �   �� B  i� �  �� r   `� D  ӊ    � 7   +� �   c� @  � 9   3� �   m� 	   K�    U�    Z� �   o� /   � V   "� (   y� V   �� D   �� Y   >�    �� �   �� H   S� /   �� @   ̒ �   
�    ߓ  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: en
Language-Team: en <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library is a popular (and illegal) library. They have taken the Library Genesis collection and made it easily searchable. On top of that, they have become very effective at solliciting new book contributions, by incentivizing contributing users with various perks. They currently do not contribute these new books back to Library Genesis. And unlike Library Genesis, they do not make their collection easily mirrorable, which prevents wide preservation. This is important to their business model, since they charge money for accessing their collection in bulk (more than 10 books per day). We do not make moral judgements about charging money for bulk access to an illegal book collection. It is beyond a doubt that the Z-Library has been successful in expanding access to knowledge, and sourcing more books. We are simply here to do our part: ensuring the long-term preservation of this private collection. - Anna and the team (<a %(reddit)s>Reddit</a>) In the original release of the Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), we made a mirror of Z-Library, a large illegal book collection. As a reminder, this is what we wrote in that original blog post: That collection dated back to mid-2021. In the meantime, the Z-Library has been growing at a staggering rate: they have added about 3.8 million new books. There are some duplicates in there, sure, but the majority of it seems to be legitimately new books, or higher quality scans of previously submitted books. This is in large part because of the increased number of volunteer moderators at the Z-Library, and their bulk-upload system with deduplication. We would like to congratulate them on these achievements. We are happy to announce that we have gotten all books that were added to the Z-Library between our last mirror and August 2022. We have also gone back and scraped some books that we missed the first time around. All in all, this new collection is about 24TB, which is much bigger than the last one (7TB). Our mirror is now 31TB in total. Again, we deduplicated against Library Genesis, since there are already torrents available for that collection. Please go to the Pirate Library Mirror to check out the new collection (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). There is more information there about how the files are structured, and what has changed since last time. We won't link to it from here, since this is just a blog website that doesn't host any illegal materials. Of course, seeding is also a great way to help us out. Thanks everyone who is seeding our previous set of torrents. We're grateful for the positive response, and happy that there are so many people who care about preservation of knowledge and culture in this unusual way. 3x new books added to the Pirate Library Mirror (+24TB, 3.8 million books) Read the companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a> - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a> Not too long ago, “shadow-libraries” were dying. Sci-Hub, the massive illegal archive of academic papers, had stopped taking in new works, due to lawsuits. “Z-Library”, the largest illegal library of books, saw its alleged creators arrested on criminal copyright charges. They incredibly managed to escape their arrest, but their library is no less under threat. Some countries are already doing a version of this. TorrentFreak <a %(torrentfreak)s>reported</a> that China and Japan have introduced AI exceptions to their copyright laws. It is unclear to us how this interacts with international treaties, but it certainly gives cover to their domestic companies, which explains what we’ve been seeing. As for Anna’s Archive — we will continue our underground work rooted in moral conviction. Yet our greatest wish is to enter the light, and amplify our impact legally. Please reform copyright. When Z-Library faced shutdown, I had already backed up its entire library and was searching for a platform to house it. That was my motivation for starting Anna’s Archive: a continuation of the mission behind those earlier initiatives. We’ve since grown to be the largest shadow library in the world, hosting more than 140 million copyrighted texts across numerous formats — books, academic papers, magazines, newspapers, and beyond. My team and I are ideologues. We believe that preserving and hosting these files is morally right. Libraries around the world are seeing funding cuts, and we can’t trust humanity’s heritage to corporations either. Then came AI. Virtually all major companies building LLMs contacted us to train on our data. Most (but not all!) US-based companies reconsidered once they realized the illegal nature of our work. By contrast, Chinese firms have enthusiastically embraced our collection, apparently untroubled by its legality. This is notable given China’s role as a signatory to nearly all major international copyright treaties. We have given high-speed access to about 30 companies. Most of them are LLM companies, and some are data brokers, who will resell our collection. Most are Chinese, though we’ve also worked with companies from the US, Europe, Russia, South Korea, and Japan. DeepSeek <a %(arxiv)s>admitted</a> that an earlier version was trained on part of our collection, though they’re tight-lipped about their latest model (probably also trained on our data though). If the West wants to stay ahead in the race of LLMs, and ultimately, AGI, it needs to reconsider its position on copyright, and soon. Whether you agree with us or not on our moral case, this is now becoming a case of economics, and even of national security. All power blocs are building artificial super-scientists, super-hackers, and super-militaries. Freedom of information is becoming a matter of survival for these countries — even a matter of national security. Our team is from all over the world, and we don’t have a particular alignment. But we’d encourage countries with strong copyright laws to use this existential threat to reform them. So what to do? Our first recommendation is straightforward: shorten the copyright term. In the US, copyright is granted for 70 years after the author’s death. This is absurd. We can bring this in line with patents, which are granted for 20 years after filing. This should be more than enough time for authors of books, papers, music, art, and other creative works, to get fully compensated for their efforts (including longer-term projects such as movie adaptations). Then, at a minimum, policymakers should include carve-outs for the mass-preservation and dissemination of texts. If lost revenue from individual customers is the main worry, personal-level distribution could remain prohibited. In turn, those capable of managing vast repositories — companies training LLMs, along with libraries and other archives — would be covered by these exceptions. Copyright reform is necessary for national security TL;DR: Chinese LLMs (including DeepSeek) are trained on my illegal archive of books and papers — the largest in the world. The West needs to overhaul copyright law as a matter of national security. Please see the <a %(all_isbns)s>original blog post</a> for more information. We issued a challenge to improve on this. We would award a first place bounty of $6,000, second place of $3,000, and third place of $1,000. Due to the overwhelming response and incredible submissions, we’ve decided to increase the prize pool slightly, and award a four-way third place of $500 each. The winners are below, but be sure to look at all submissions <a %(annas_archive)s>here</a>, or download our <a %(a_2025_01_isbn_visualization_files)s>combined torrent</a>. First place $6,000: phiresky This <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>Gitlab comment</a>) is simply everything we wanted, and more! We especially liked the incredibly flexible visualization options (even supporting custom shaders), but with a comprehensive list of presets. We also liked how fast and smooth everything is, the simple implementation (that doesn’t even have a backend), the clever minimap, and extensive explanation in their <a %(phiresky_github)s>blog post</a>. Incredible work, and the well-deserved winner! - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Our hearts are full with gratitude. Notable ideas Skyscrapers for rarity Lots of sliders to compare datasets, as if you’re a DJ. Scale bar with number of books. Pretty labels. Cool default color scheme and heatmap. Unique map view and filters Annotations, and also live stats Live stats Some more ideas and implementations we particularly liked: We could keep going for a while, but let’s stop here. Be sure to look at all submissions <a %(annas_archive)s>here</a>, or download our <a %(a_2025_01_isbn_visualization_files)s>combined torrent</a>. So many submissions, and each one brings a unique perspective, whether in UI or implementation. We’ll at least incorporate the first place submission into our main website, and perhaps some others. We’ve also started thinking about how to organize the process of identifying, confirming, and then archiving the rarest books. More to come on this front. Thanks everyone who participated. It’s amazing that so many people care. Easy toggling of datasets for quick comparisons. All ISBNs CADAL SSNOs CERLALC data leak DuXiu SSIDs EBSCOhost’s eBook Index Google Books Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Files in Anna’s Archive Nexus/STC OCLC/Worldcat OpenLibrary Russian State Library Imperial Library of Trantor Second place $3,000: hypha “While perfect squares and rectangles are mathematically pleasing, they don't provide superior locality in a mapping context. I believe the asymmetry inherent in these Hilbert or classic Morton is not a flaw but a feature. Just like Italy's famously boot-shaped outline makes it instantly recognizable on a map, the unique "quirks" of these curves may serve as cognitive landmarks. This distinctiveness can enhance spatial memory and help users orient themselves, potentially making locating specific regions or noticing patterns easier.” Another incredible <a %(annas_archive_note_2913)s>submission</a>. Not as flexible as the first place, but we actually preferred its macro-level visualization over the first place (space-filling curve, borders, labeling, highlighting, panning, and zooming). A <a %(annas_archive_note_2971)s>comment</a> by Joe Davis resonated with us: And still lots of options for visualizing and rendering, as well as an incredibly smooth an intuitive UI. A solid second place! - Anna and the team (<a %(reddit)s>Reddit</a>) A few months ago we announced a <a %(all_isbns)s>$10,000 bounty</a> to make the best possible visualization of our data showing the ISBN space. We emphasized showing which files we have/haven’t archived already, and we later a dataset describing how many libraries hold ISBNs (a measure of rarity). We’ve been overwhelmed by the response. There has been so much creativity. A big thank you to everyone who has participated: your energy and enthusiasm are infectious! Ultimately we wanted to answer the following questions: <strong>which books exist in the world, how many have we archived already, and which books should we focus on next?</strong> It’s great to see so many people care about these questions. We started with a basic visualization ourselves. In less than 300kb, this picture succinctly represents the largest fully open “list of books” ever assembled in the history of humanity: Third place $500 #1: maxlion In this <a %(annas_archive_note_2940)s>submission</a> we really liked the different kinds of views, in particular the comparison and publisher views. Third place $500 #2: abetusk While not the most polished UI, this <a %(annas_archive_note_2917)s>submission</a> checks a lot of the boxes. We particularly liked its comparison feature. Third place $500 #3: conundrumer0 Like the first place, this <a %(annas_archive_note_2975)s>submission</a> impressed us with its flexibility. Ultimately this is what makes for a great visualization tool: maximal flexibility for power users, while keeping things simple for average users. Third place $500 #4: charelf The final <a %(annas_archive_note_2947)s>submission</a> to get a bounty is pretty basic, but has some unique features that we really liked. We liked how they show how many datasets cover a particular ISBN as a measure of popularity/reliability. We also really liked the simplicity but effectiveness of using an opacity slider for comparisons. Winners of the $10,000 ISBN visualization bounty TL;DR: We got some incredible submissions to the $10,000 ISBN visualization bounty. Background How can Anna’s Archive achieve its mission of backing up all of humanity’s knowledge, without knowing which books are still out there? We need a TODO list. One way to map this out is through ISBN numbers, which since the 1970s have been assigned to every book published (in most countries). There is no central authority that knows all ISBN assignments. Instead, it’s a distributed system, where countries get ranges of numbers, who then assign smaller ranges to major publishers, who might further sub-divide ranges to minor publishers. Finally individual numbers are assigned to books. We started mapping ISBNs <a %(blog)s>two years ago</a> with our scrape of ISBNdb. Since then, we have scraped many more metadata sources, such as <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, and more. A full list can be found on the “Datasets” and “Torrents” pages on Anna’s Archive. We now have by far the largest fully open, easily downloadable collection of book metadata (and thus ISBNs) in the world. We’ve <a %(blog)s>written extensively</a> about why we care about preservation, and why we’re currently in a critical window. We must now identify rare, underfocused, and uniquely at-risk books and preserve them. Having good metadata on all books in the world helps with that. $10,000 bounty Strong consideration will be given to usability and how good it looks. Show actual metadata for individual ISBNs when zooming in, such as title and author. Better space-filling curve. E.g. a zig-zag, going from 0 to 4 on the first row and then back (in reverse) from 5 to 9 on the second row — recursively applied. Different or customizable color schemes. Special views for comparing datasets. Ways to debug issues, such as other metadata that don’t agree well (e.g. vastly different titles). Annotating images with comments on ISBNs or ranges. Any heuristics for identifying rare or at-risk books. Whatever creative ideas you can come up with! Code The code to generate these images, as well as other examples, can be found in <a %(annas_archive)s>this directory</a>. We came up with a compact data format, with which all the required ISBN information is about 75MB (compressed). The description of the data format and code to generate it can be found <a %(annas_archive_l1244_1319)s>here</a>. For the bounty you’re not required to use this, but it is probably the most convenient format to get started with. You can transform our metadata however you want (though all your code has to be open source). We can’t wait to see what you come up with. Good luck! Fork this repo, and edit this blog post HTML (no other backends besides our Flask backend are allowed). Make the picture above smoothly zoomable, so you can zoom all the way to individual ISBNs. Clicking ISBNs should take you to a metadata page or search on Anna’s Archive. You must still be able to switch between all different datasets. Country ranges and publisher ranges should be highlighted on hover. You can use e.g. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> for country info, and our “isbngrp” scrape for publishers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). It must work well on desktop and mobile. There is much to explore here, so we’re announcing a bounty for improving the visualization above. Unlike most of our bounties, this one is time-bound. You have to <a %(annas_archive)s>submit</a> your open source code by 2025-01-31 (23:59 UTC). The best submission will get $6,000, second place is $3,000, and third place is $1,000. All bounties will be awarded using Monero (XMR). Below are the minimal criteria. If no submission meets the criteria, we might still award some bounties, but that will be at our discretion. For bonus points (these are just ideas — let your creativity run wild): You MAY completely veer off from the minimal criteria, and do a completely different visualization. If it’s really spectacular, then that qualifies for the bounty, but at our discretion. Make submissions by posting a comment to <a %(annas_archive)s>this issue</a> with a link to your forked repo, merge request, or diff. - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) This picture is 1000×800 pixels. Each pixel represents 2,500 ISBNs. If we have a file for an ISBN, we make that pixel more green. If we know an ISBN has been issued, but we don’t have a matching file, we make it more red. In less than 300kb, this picture succinctly represents the largest fully open “list of books” ever assembled in the history of humanity (a few hundred GB compressed in full). It also shows: there is a lot of work left in backing up books (we only have 16%%). Visualizing All ISBNs — $10,000 bounty by 2025-01-31 This picture represents the largest fully open “list of books” ever assembled in the history of humanity. Visualizing Besides the overview image, we can also look at individual datasets we’ve acquired. Use the dropdown and buttons to switch between them. There are lots of interesting patterns to see in these pictures. Why is there some regularity of lines and blocks, that seems to happen at different scales? What are the empty areas? Why are certain datasets so clustered? We’ll leave these questions as an exercise for the reader. - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusion With this standard, we can make releases more incrementally, and more easily add new data sources. We already have a few exciting releases in the pipeline! We also hope it becomes easier for other shadow libraries to mirror our collections. After all, our goal is to preserve human knowledge and culture forever, so the more redundancy the better. Example Let’s look at our recent Z-Library release as an example. It consists of two collections: “<span style="background: #fffaa3">zlib3_records</span>” and “<span style="background: #ffd6fe">zlib3_files</span>”. This allows us to separately scrape and release metadata records from the actual book files. As such, we released two torrents with metadata files: We also released a bunch of torrents with binary data folders, but only for the “<span style="background: #ffd6fe">zlib3_files</span>” collection, 62 in total: By running <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> we can see what’s inside: In this case, it’s metadata of a book as reported by Z-Library. At the top-level we only have “aacid” and “metadata”, but no “data_folder”, since there is no corresponding binary data. The AACID contains “22430000” as the primary ID, which we can see is taken from “zlibrary_id”. We can expect other AACs in this collection to have the same structure. Now let’s run <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: This is a much smaller AAC metadata, though the bulk of this AAC is located elsewhere in a binary file! After all, we have a “data_folder” this time, so we can expect the corresponding binary data to be located at <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. The “metadata” contains the “zlibrary_id”, so we can easily associate it with the corresponding AAC in the “zlib_records” collection. We could’ve associated in a number of different ways, e.g. through AACID — the standard doesn’t prescribe that. Note that it’s also not necessary for the “metadata” field to itself be JSON. It could be a string containing XML or any other data format. You could even store metadata information in the associated binary blob, e.g. if it’s a lot of data. Heterogeneous files and metadata, in as close to the original format as possible. Binary data can be served directly by webservers like Nginx. Heterogeneous identifiers in the source libraries, or even lack of identifiers. Separate releases of metadata vs file data, or metadata-only releases (e.g. our ISBNdb release). Distribution through torrents, though with the possibility of other distribution methods (e.g. IPFS). Immutable records, since we should assume our torrents will live forever. Incremental releases / appendable releases. Machine-readable and writeable, conveniently and quickly, especially for our stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Somewhat easy human inspection, though this is secondary to machine readability. Easy to seed our collections with a standard rented seedbox. Design goals We don’t care about files being easy to navigate manually on disk, or searchable without preprocessing. We don’t care about being directly compatible with existing library software. While it should be easy for anyone to seed our collection using torrents, we don’t expect the files to be usable without significant technical knowledge and commitment. Our primary use case is the distribution of files and associated metadata from different existing collections. Our most important considerations are: Some non-goals: Since Anna’s Archive is open source, we want to dogfood our format directly. When we refresh our search index, we only access publicly available paths, so that anyone who forks our library can get up and running quickly. <strong>AAC.</strong> AAC (Anna’s Archive Container) is a single item consisting of <strong>metadata</strong>, and optionally <strong>binary data</strong>, both of which are immutable. It has a globally unique identifier, called <strong>AACID</strong>. <strong>AACID.</strong> The format of AACID is this: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. For example, an actual AACID that we’re released is <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID range.</strong> Since AACIDs contain monotonically increasing timestamps, we can use that to denote ranges within a particular collection. We use this format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, where the timestamps are inclusive. This is consistent with ISO 8601 notation. Ranges are continuous, and may overlap, but in case of overlap must contain identical records as the one previously released in that collection (since AACs are immutable). Missing records are not allowed. <code>{collection}</code>: the collection name, which may contain ASCII letters, numbers, and underscores (but no double underscores). <code>{collection-specific ID}</code>: a collection-specific identifier, if applicable, e.g. the Z-Library ID. May be omitted or truncated. Must be omitted or truncated if the AACID would otherwise exceed 150 characters. <code>{ISO 8601 timestamp}</code>: a short version of the ISO 8601, always in UTC, e.g. <code>20220723T194746Z</code>. This number has to monotonically increase for every release, though its exact semantics can differ per collection. We suggest using the time of scraping or of generating the ID. <code>{shortuuid}</code>: a UUID but compressed to ASCII, e.g. using base57. We currently use the <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python library. <strong>Binary data folder.</strong> A folder with the binary data of a range of AACs, for one particular collection. These have the following properties: The directory must contain data files for all AACs within the specified range. Each data file must have its AACID as the filename (no extensions). Directory name must be an AACID range, prefixed with <code style="color: green">annas_archive_data__</code>, and no suffix. For example, one of our actual releases has a directory called<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. It’s recommended to make these folders somewhat manageable in size, e.g. not larger than 100GB-1TB each, though this recommendation may change over time. <strong>Collection.</strong> Each AAC belongs to a collection, which by definition is a list of AACs that are semantically consistent. That means that if you make a significant change to the format of the metadata, then you have to create a new collection. The standard <strong>Metadata file.</strong> A metadata file contains the metadata of a range of AACs, for one particular collection. These have the following properties: <code>data_folder</code> is optional, and is the name of binary data folder that contains the corresponding binary data. The filename of the corresponding binary data within that folder is the record’s AACID. Each JSON object must contain the following fields at the top level: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). No other fields are allowed. Filename must be an AACID range, prefixed with <code style="color: red">annas_archive_meta__</code> and followed by <code>.jsonl.zstd</code>. For example, one of our releases is called<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. As indicated by the file extension, the file type is <a %(jsonlines)s>JSON Lines</a> compressed with <a %(zstd)s>Zstandard</a>. <code>metadata</code> is arbitrary metadata, per the semantics of the collection. It must be semantically consistent within the collection. The <code style="color: red">annas_archive_meta__</code> prefix may be adapted to the name of your institution, e.g. <code style="color: red">my_institute_meta__</code>. <strong>“records” and “files” collections.</strong> By convention, it’s often convenient to release “records” and “files” as different collections, so they can be released at different schedules, e.g. based on scraping rates. A “record” is a metadata-only collection, containing information like book titles, authors, ISBNs, etc, while “files” are the collections that contain the actual files themselves (pdf, epub). Ultimately, we settled on a relatively simple standard. It’s fairly loose, non-normative, and a work in progress. <strong>Torrents.</strong> The metadata files and binary data folders may be bundled in torrents, with one torrent per metadata file, or one torrent per binary data folder. The torrents must have the original file/directory name plus a <code>.torrent</code> suffix as their filename. <a %(wikipedia_annas_archive)s>Anna’s Archive</a> has become by far the largest shadow library in the world, and the only shadow library of its scale that is fully open-source and open-data. Below is a table from our Datasets page (slightly modified): We accomplished this in three ways: Mirroring existing open-data shadow libraries (like Sci-Hub and Library Genesis). Helping out shadow libraries that want to be more open, but didn’t have the time or resources to do so (like the Libgen comics collection). Scraping libraries that do not wish to share in bulk (like Z-Library). For (2) and (3) we now manage a considerable collection of torrents ourselves (100s of TBs). So far we have approached these collections as one-offs, meaning bespoke infrastructure and data organization for each collection. This adds significant overhead to each release, and makes it particularly hard to do more incremental releases. That’s why we decided to standardize our releases. This is a technical blog post in which we’re introducing our standard: <strong>Anna’s Archive Containers</strong>. Anna’s Archive Containers (AAC): standardizing releases from the world’s largest shadow library Anna’s Archive has become the largest shadow library in the world, requiring us to standardize our releases. 300GB+ of book covers released Finally, we’re happy to announce a small release. In collaboration with the folks who operate the Libgen.rs fork, we’re sharing all their book covers through torrents and IPFS. This will distribute the load of viewing the covers among more machines, and will preserve them better. In many (but not all) cases, the book covers are included in the files themselves, so this is kind of “derived data”. But having it in IPFS is still very useful for daily operation of both Anna’s Archive and the various Library Genesis forks. As usual, you can find this release at the Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). We won’t link to it here, but you can easily find it. Hopefully we can relax our pace a little, now that we have a decent alternative to Z-Library. This workload is not particularly sustainable. If you are interested in helping out with programming, server operations, or preservation work, definitely reach out to us. There is still a lot of <a %(annas_archive)s>work to be done</a>. Thanks for your interest and support. Switch to ElasticSearch Some queries took super long, to the point where they would hog all the open connections. By default MySQL has a minimum word length, or your index can get really large. People reported not being able to search for “Ben Hur”. Search was only somewhat fast when fully loaded in memory, which required us to get a more expensive machine to run this on, plus some commands to preload the index on startup. We wouldn’t have been able to extend it easily to build new features, like better <a %(wikipedia_cjk_characters)s>tokenization for non-whitespaced languages</a>, filtering/faceting, sorting, "did you mean" suggestions, autocomplete, and so on. One of our <a %(annas_archive)s>tickets</a> was a grab-bag of issues with our search system. We used MySQL full-text search, since we had all our data in MySQL anyway. But it had its limits: After talking to a bunch of experts, we settled on ElasticSearch. It hasn’t been perfect (their default “did you mean” suggestions and autocomplete features suck), but overall it’s been a lot better than MySQL for search. We’re still not <a %(youtube)s>too keen</a> on using it for any mission-critical data (though they’ve made a lot of <a %(elastic_co)s>progress</a>), but overall we’re quite happy with the switch. For now, we’ve implemented much faster search, better language support, better relevancy sorting, different sorting options, and filtering on language/book type/file type. If you’re curious how it works, <a %(annas_archive_l140)s>have</a> <a %(annas_archive_l1115)s>a</a> <a %(annas_archive_l1635)s>look</a>. It’s fairly accessible, though it could use some more comments… Anna’s Archive is fully open source We believe that information should be free, and our own code is no exception. We have released all of our code on our privately hosted Gitlab instance: <a %(annas_archive)s>Anna’s Software</a>. We also use the issue tracker to organize our work. If you want to engage with our development, this is a great place to start. To give you a taste of the things we are working on, take our recent work on client-side performance improvements. Since we haven’t implemented pagination yet, we would often return very long search pages, with 100-200 results. We didn’t want to cut off the search results too soon, but this did mean that it would slow down some devices. For this, we implemented a little trick: we wrapped most search results in HTML comments (<code><!-- --></code>), and then wrote a little Javascript that would detect when a result should become visible, at which moment we would unwrap the comment: DOM "virtualization" implemented in 23 lines, no need for fancy libraries! This is the sort of quick pragmatic code that you end up with when you have limited time, and real problems that need to be solved. It has been reported that our search now works well on slow devices! Another big effort was to automate building the database. When we launched, we just haphazardly pulled different sources together. Now we want to keep them updated, so we wrote a bunch of scripts to download new metadata from the two Library Genesis forks, and integrates them. The goal is to not just make this useful for our archive, but to make things easy for anyone who wants to play around with shadow library metadata. The goal would be a Jupyter notebook that has all sorts of interesting metadata available, so we can do more research like figuring out what <a %(blog)s>percentage of ISBNs are preserved forever</a>. Finally, we revamped our donation system. You can now use a credit card to directly deposit money into our crypto wallets, without really needing to know anything about cryptocurrencies. We’ll keep monitoring how well this works in practice, but this is a big deal. With Z-Library going down and its (alleged) founders getting arrested, we’ve been working around the clock to provide a good alternative with Anna’s Archive (we won’t link it here, but you can Google it). Here are some of the things we achieved recently. Anna’s Update: fully open source archive, ElasticSearch, 300GB+ of book covers We’ve been working around the clock to provide a good alternative with Anna’s Archive. Here are some of the things we achieved recently. Analysis Semantic duplicates (different scans of the same book) can theoretically be filtered out, but it is tricky. When manually looking through the comics we found too many false positives. There are some duplicates purely by MD5, which is relatively wasteful, but filtering those out would only give us about 1%% in savings. At this scale that’s still about 1TB, but also, at this scale 1TB doesn’t really matter. We’d rather not risk accidentally destroying data in this process. We found a bunch of non-book data, such as movies based on comic books. That also seems wasteful, since these are already widely available through other means. However, we realized that we couldn’t just filter out movie files, since there are also <em>interactive comic books</em> that were released on the computer, which someone recorded and saved as movies. Ultimately, anything we could delete from the collection would only save a few percent. Then we remembered that we’re data hoarders, and the people who will be mirroring this are also data hoarders, and so, “WHAT DO YOU MEAN, DELETE?!” :) When you get 95TB dumped into your storage cluster, you try to make sense of what is even in there… We did some analysis to see if we could reduce the size a bit, such as by removing duplicates. Here are some of our findings: We are therefore presenting to you, the full, unmodified collection. It’s a lot of data, but we hope enough people will care to seed it anyway. Collaboration Given its size, this collection has long been on our wishlist, so after our success with backing up Z-Library, we set our sights on this collection. At first we scraped it directly, which was quite the challenge, since their server was not in the best condition. We got about 15TB this way, but it was slow-going. Luckily, we managed to get in touch with the operator of the library, who agreed to send us all the data directly, which was a lot faster. It still took more than half a year to transfer and process all the data, and we nearly lost all of it to disk corruption, which would have meant starting all over. This experience has made us believe it is important to get this data out there as quickly as possible, so it can be mirrored far and wide. We’re just one or two unluckily timed incidents away from losing this collection forever! The collection Moving fast does mean that the collection is a little unorganized… Let's have a look. Imagine we have a filesystem (which in reality we’re splitting up across torrents): The first directory, <code>/repository</code>, is the more structured part of this. This directory contains so-called “thousand dirs”: directories each with a thousands files, which are incrementally numbered in the database. Directory <code>0</code> contains files with comic_id 0–999, and so on. This is the same scheme as Library Genesis has been using for its fiction and non-fiction collections. The idea is that every “thousand dir” gets automatically turned into a torrent as soon as it’s filled up. However, the Libgen.li operator never made torrents for this collection, and so the thousand dirs likely became inconvenient, and gave way to “unsorted dirs”. These are <code>/comics0</code> through <code>/comics4</code>. They all contain unique directory structures, that probably made sense for collecting the files, but don’t make too much sense to us now. Luckily, the metadata still refers directly to all these files, so their storage organization on disk doesn’t actually matter! The metadata is available in the form of a MySQL database. This can be downloaded directly from the Libgen.li website, but we’ll also make it available in a torrent, alongside our own table with all the MD5 hashes. <q>Dr. Barbara Gordon tries to lose herself in the mundane world of the library…</q> Libgen forks First, some background. You might know Library Genesis for their epic book collection. Fewer people know that Library Genesis volunteers have created other projects, such as a sizable collection of magazines and standard documents, a full backup of Sci-Hub (in collaboration with the founder of Sci-Hub, Alexandra Elbakyan), and indeed, a massive collection of comics. At some point different operators of Library Genesis mirrors went their separate ways, which gave rise to the current situation of having a number of different “forks”, all still carrying the name Library Genesis. The Libgen.li fork uniquely has this comics collection, as well as a sizeable magazines collection (which we are also working on). Fundraiser We’re releasing this data in some big chunks. The first torrent is of <code>/comics0</code>, which we put into one huge 12TB .tar file. That’s better for your hard drive and torrent software than a gazillion smaller files. As part of this release, we’re doing a fundraiser. We’re looking to raise $20,000 to cover operational and contracting costs for this collection, as well as enable ongoing and future projects. We have some <em>massive</em> ones in the works. <em>Who am I supporting with my donation?</em> In short: we’re backing up all knowledge and culture of humanity, and making it easily accessible. All our code and data are open source, we are a completely volunteer-run project, and we have saved 125TB worth of books so far (in addition to Libgen and Scihub’s existing torrents). Ultimately we’re building a flywheel that enables and incentivizes people to find, scan, and backup all the books in the world. We’ll write about our master plan in a future post. :) If you donate for a 12 month “Amazing Archivist” membership ($780), you get to <strong>“adopt a torrent”</strong>, meaning that we’ll put your username or message in the filename of one of the torrents! You can donate by going to <a %(wikipedia_annas_archive)s>Anna’s Archive</a> and clicking the “Donate” button. We’re also looking for more volunteers: software engineers, security researchers, anonymous merchant experts, and translators. You can also support us by providing hosting services. And of course, please seed our torrents! Thanks to everyone who has so generously supported us already! You’re truly making a difference. Here are the torrents released so far (we’re still processing the rest): All torrents can be found on <a %(wikipedia_annas_archive)s>Anna’s Archive</a> under “Datasets” (we don’t link there directly, so links to this blog don’t get removed from Reddit, Twitter, etc). From there, follow the link to the Tor website. <a %(news_ycombinator)s>Discuss on Hacker News</a> What’s next? A bunch of torrents are great for long-term preservation, but not so much for everyday access. We’ll be working with hosting partners on getting all this data up on the web (since Anna’s Archive doesn’t host anything directly). Of course you’ll be able to find these download links on Anna’s Archive. We’re also inviting everyone to do stuff with this data! Help us better analyze it, deduplicate it, put it on IPFS, remix it, train your AI models with it, and so on. It’s all yours, and we can’t wait to see what you do with it. Finally, as said before, we still have some massive releases coming up (if <em>someone</em> could <em>accidentally</em> send us a dump of a <em>certain</em> ACS4 database, you know where to find us…), as well as building the flywheel for backing up all the books in the world. So stay tuned, we’re only just getting started. - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) The largest shadow library of comic books is likely that of a particular Library Genesis fork: Libgen.li. The one administrator running that site managed to collect an insane comics collection of over 2 million files, totalling over 95TB. However, unlike other Library Genesis collections, this one was not available in bulk through torrents. You could only access these comics individually through his slow personal server — a single point of failure. Until today! In this post we’ll tell you more about this collection, and about our fundraiser to support more of this work. Anna’s Archive has backed up the world’s largest comics shadow library (95TB) — you can help seed it The largest comic books shadow library in the world had a single point of failure.. until today. Warning: this blog post has been deprecated. We’ve decided that IPFS is not yet ready for prime time. We’ll still link to files on IPFS from Anna’s Archive when possible, but we won’t host it ourselves anymore, nor do we recommend others to mirror using IPFS. Please see our Torrents page if you want to help preserve our collection. Putting 5,998,794 books on IPFS A multiplication of copies Back to our original question: how can we claim to preserve our collections in perpetuity? The main problem here is that our collection has been <a %(torrents_stats)s>growing</a> at a rapid clip, by scraping and open-sourcing some massive collections (on top of the amazing work already done by other open-data shadow libraries like Sci-Hub and Library Genesis). This growth in data makes it harder for the collections to be mirrored around the world. Data storage is expensive! But we are optimistic, especially when observing the following three trends. The <a %(annas_archive_stats)s>total size</a> of our collections, over the last few months, broken down by number of torrent seeders. HDD price trends from different sources (click to view study). <a %(critical_window_chinese)s>Chinese version 中文版</a>, discuss on <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. We’ve plucked the low-hanging fruit This one follow directly from our priorities discussed above. We prefer to work on liberating large collections first. Now that we’ve secured some of the largest collections in the world, we expect our growth to be much slower. There is still a long tail of smaller collections, and new books get scanned or published every day, but the rate will likely be much slower. We might still double or even triple in size, but over a longer time period. OCR improvements. Priorities Science & engineering software code Fictional or entertainment versions of all of the above Geographic data (e.g. maps, geological surveys) Internal data from corporations or governments (leaks) Measurement data like scientific measurements, economic data, corporate reports Metadata records generally (of non-fiction and fiction; of other media, art, people, etc; including reviews) Non-fiction books Non-fiction magazines, newspapers, manuals Non-fiction transcripts of talks, documentaries, podcasts Organic data like DNA sequences, plant seeds, or microbial samples Academic papers, journals, reports Science & engineering websites, online discussions Transcripts of legal or court proceedings Uniquely at risk of destruction (e.g. by war, funding cuts, lawsuits, or political persecution) Rare Uniquely underfocused Why do we care so much about papers and books? Let’s set aside our fundamental belief in preservation in general — we might write another post about that. So why papers and books specifically? The answer is simple: <strong>information density</strong>. Per megabyte of storage, written text stores the most information out of all media. While we care about both knowledge and culture, we do care more about the former. Overall, we find a hierarchy of information density and importance of preservation that looks roughly like this: The ranking in this list is somewhat arbitrary — several items are ties or have disagreements within our team — and we’re probably forgetting some important categories. But this is roughly how we prioritize. Some of these items are too different from the others for us to worry about (or are already taken care of by other institutions), such as organic data or geographic data. But most of the items in this list are actually important to us. Another big factor in our prioritization is how much at risk a certain work is. We prefer to focus on works that are: Finally, we care about scale. We have limited time and money, so we’d rather spend a month saving 10,000 books than 1,000 books — if they’re about equally valuable and at risk. <em><q>The lost cannot be recovered; but let us save what remains: not by vaults and locks which fence them from the public eye and use, in consigning them to the waste of time, but by such a multiplication of copies, as shall place them beyond the reach of accident.</q></em><br>— Thomas Jefferson, 1791 Shadow libraries Code can be open source on Github, but Github as a whole cannot be easily mirrored and thus preserved (though in this particular case there are sufficiently distributed copies of most code repositories) Metadata records can be freely viewed on the Worldcat website, but not downloaded in bulk (until we <a %(worldcat_scrape)s>scraped</a> them) Reddit is free to use, but has recently put up stringent anti-scraping measures, in the wake of data-hungry LLM training (more about that later) There are many organizations that have similar missions, and similar priorities. Indeed, there are libraries, archives, labs, museums, and other institutions tasked with preservation of this kind. Many of those are well-funded, by governments, individuals, or corporations. But they have one massive blind spot: the legal system. Herein lies the unique role of shadow libraries, and the reason Anna’s Archive exists. We can do things that other institutions are not allowed to do. Now, it’s not (often) that we can archive materials that are illegal to preserve elsewhere. No, it’s legal in many places to build an archive with any books, papers, magazines, and so on. But what legal archives often lack is <strong>redundancy and longevity</strong>. There exist books of which only one copy exists in some physical library somewhere. There exist metadata records guarded by a single corporation. There exist newspapers only preserved on microfilm in a single archive. Libraries can get funding cuts, corporations can go bankrupt, archives can be bombed and burned to the ground. This is not hypothetical — this happens all the time. The thing we can uniquely do at Anna’s Archive is store many copies of works, at scale. We can collect papers, books, magazines, and more, and distribute them in bulk. We currently do this through torrents, but the exact technologies don’t matter and will change over time. The important part is getting many copies distributed across the world. This quote from over 200 years ago still rings true: A quick note about public domain. Since Anna’s Archive uniquely focus on activities that are illegal in many places around the world, we don’t bother with widely available collections, such as public domain books. Legal entities often already take good care of that. However, there are considerations which make us sometimes work on publicly available collections: - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Storage costs continue to drop exponentially 3. Improvements in information density We currently store books in the raw formats that they are given to us. Sure, they are compressed, but often they are still large scans or photographs of pages. Until now, the only options to shrink the total size of our collection has been through more aggressive compression, or deduplication. However, to get significant enough savings, both are too lossy for our taste. Heavy compression of photos can make text barely readable. And deduplication requires high confidence of books being exactly the same, which is often too inaccurate, especially if the contents are the same but the scans are made on different occasions. There has always been a third option, but its quality has been so abysmal that we never considered it: <strong>OCR, or Optical Character Recognition</strong>. This is the process of converting photos into plain text, by using AI to detect the characters in the photos. Tools for this have long existed, and have been pretty decent, but “pretty decent” is not enough for preservation purposes. However, recent multi-modal deep-learning models have made extremely rapid progress, though still at high costs. We expect both accuracy and costs to improve dramatically in coming years, to the point where it will become realistic to apply to our entire library. When that happens, we will likely still preserve the original files, but in addition we could have a much smaller version of our library that most people will want to mirror. The kicker is that raw text itself compresses even better, and is much easier to deduplicate, giving us even more savings. Overall it’s not unrealistic to expect at least a 5-10x reduction in total file size, perhaps even more. Even with a conservative 5x reduction, we’d be looking at <strong>$1,000–$3,000 in 10 years even if our library triples in size</strong>. As of the time of writing, <a %(diskprices)s>disk prices</a> per TB are around $12 for new disks, $8 for used disks, and $4 for tape. If we’re conservative and look only at new disks, that means that storing a petabyte costs about $12,000. If we assume our library will triple from 900TB to 2.7PB, that would mean $32,400 to mirror our entire library. Adding electricity, cost of other hardware, and so on, let’s round it up to $40,000. Or with tape more like $15,000–$20,000. On one hand <strong>$15,000–$40,000 for the sum of all human knowledge is a steal</strong>. On the other hand, it is a bit steep to expect tons of full copies, especially if we’d also like those people to keep seeding their torrents for the benefit of others. That is today. But progress marches forwards: Hard drive costs per TB have been roughly slashed in third over the last 10 years, and will likely continue to drop at a similar pace. Tape appears to be on a similar trajectory. SSD prices are dropping even faster, and might take over HDD prices by the end of the decade. If this holds, then in 10 years we might be looking at only $5,000–$13,000 to mirror our entire collection (1/3rd), or even less if we grow less in size. While still a lot of money, this will be attainable for many people. And it might be even better because of the next point… At Anna’s Archive, we are often asked how we can claim to preserve our collections in perpetuity, when the total size is already approaching 1 Petabyte (1000 TB), and is still growing. In this article we’ll look at our philosophy, and see why the next decade is critical for our mission of preserving humanity’s knowledge and culture. Critical window If these forecasts are accurate, we <strong>just need to wait a couple of years</strong> before our entire collection will be widely mirrored. Thus, in the words of Thomas Jefferson, “placed beyond the reach of accident.” Unfortunately, the advent of LLMs, and their data-hungry training, has put a lot of copyright holders on the defensive. Even more than they already were. Many websites are making it harder to scrape and archive, lawsuits are flying around, and all the while physical libraries and archives continue to be neglected. We can only expect these trends to continue to worsen, and many works to be lost well before they enter the public domain. <strong>We are on the eve of a revolution in preservation, but <q>the lost cannot be recovered.</q></strong> We have a critical window of about 5-10 years during which it’s still fairly expensive to operate a shadow library and create many mirrors around the world, and during which access has not been completely shut down yet. If we can bridge this window, then we’ll indeed have preserved humanity’s knowledge and culture in perpetuity. We should not let this time go to waste. We should not let this critical window close on us. Let’s go. The critical window of shadow libraries How can we claim to preserve our collections in perpetuity, when they are already approaching 1 PB? Collection Some more information about the collection. <a %(duxiu)s>Duxiu</a> is a massive database of scanned books, created by the <a %(chaoxing)s>SuperStar Digital Library Group</a>. Most are academic books, scanned in order to make them available digitally to universities and libraries. For our English-speaking audience, <a %(library_princeton)s>Princeton</a> and the <a %(guides_lib_uw)s>University of Washington</a> have good overviews. There is also an excellent article giving more background: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (look it up in Anna’s Archive). The books from Duxiu have long been pirated on the Chinese internet. Usually they are being sold for less than a dollar by resellers. They are typically distributed using the Chinese equivalent of Google Drive, which has often been hacked to allow for more storage space. Some technical details can be found <a %(github_duty_machine)s>here</a> and <a %(github_821_github_io)s>here</a>. Though the books have been semi-publicly distributed, it is quite difficult to obtain them in bulk. We had this high on our TODO-list, and allocated multiple months of full-time work for it. However, recently an incredible, amazing, and talented volunteer reached out to us, telling us they had done all this work already — at great expense. They shared the full collection with us, without expecting anything in return, except the guarantee of long-term preservation. Truly remarkable. They agreed to ask for help in this way to get the collection OCR'ed. The collection is 7,543,702 files. This is more than Library Genesis non-fiction (about 5.3 million). Total file size is about 359TB (326TiB) in its current form. We’re open to other proposals and ideas. Just contact us. Check out Anna’s Archive for more information about our collections, preservation efforts, and how you can help. Thanks! Example pages To prove to us that you have a good pipeline, here are some example pages to get started on, from a book on superconductors. Your pipeline should properly handle math, tables, charts, footnotes, and so on. Send your processed pages to our email. If they look good, we will send you more in private, and we expect you to be able to quickly run your pipeline on those as well. Once we’re satisfied, we can make a deal. - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Chinese version 中文版</a>, <a %(news_ycombinator)s>Discuss on Hacker News</a> This is a short blog post. We’re looking for some company or institution to help us with OCR and text extraction for a massive collection we acquired, in exchange for exclusive early access. After the embargo period, we will of course release the entire collection. High-quality academic text is extremely useful for training of LLMs. While our collection is Chinese, this should be even useful for training English LLMs: models seem encode concepts and knowledge regardless of the source language. For this, text needs to be extracted from the scans. What does Anna’s Archive get out of it? Full-text search of the books for its users. Because our goals align with that of LLM developers, we’re looking for a collaborator. We’re willing to give you <strong>exclusive early access to this collection in bulk for 1 year</strong>, if you can do proper OCR and text extraction. If you’re willing to share the entire code of your pipeline with us, we’d be willing to embargo the collection for longer. Exclusive access for LLM companies to largest Chinese non-fiction book collection in the world <em><strong>TL;DR:</strong> Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction.</em> System architecture So let’s say that you found some companies that are willing to host your website without shutting you down — let’s call these “freedom-loving providers” 😄. You’ll quickly find that hosting everything with them is rather expensive, so you might want to find some “cheap providers” and do the actual hosting there, proxying through the freedom-loving providers. If you do it right, the cheap providers will never know what you are hosting, and never receive any complaints. With all of these providers there is a risk of them shutting you down anyway, so you also need redundancy. We need this on all levels of our stack. One somewhat freedom-loving company that has put itself in an interesting position is Cloudflare. They have <a %(blog_cloudflare)s>argued</a> that they are not a hosting provider, but a utility, like an ISP. They are therefore not subject to DMCA or other takedown requests, and forward any requests to your actual hosting provider. They have gone as far as going to court to protect this structure. We can therefore use them as another layer of caching and protection. Cloudflare does not accept anonymous payments, so we can only use their free plan. This means that we can’t use their load balancing or failover features. We therefore <a %(annas_archive_l255)s>implemented this ourselves</a> at the domain level. On page load, the browser will check if the current domain is still available, and if not, it rewrites all URLs to a different domain. Since Cloudflare caches many pages, this means that a user can land on our main domain, even if the proxy server is down, and then on the next click be moved over to another domain. We still also have normal operational concerns to deal with, such as monitoring server health, logging backend and frontend errors, and so on. Our failover architecture allows for more robustness on this front as well, for example by running a completely different set of servers on one of the domains. We can even run older versions of the code and datasets on this separate domain, in case a critical bug in the main version goes unnoticed. We can also hedge against Cloudflare turning against us, by removing it from one of the domains, such as this separate domain. Different permutations of these ideas are possible. Conclusion It has been an interesting experience to learn how to set up a robust and resilient shadow library search engine. There are tons more details to share in later posts, so let me know what you would like to learn more about! As always, we’re looking for donations to support this work, so be sure to check out the Donate page on Anna’s Archive. We’re also looking for other types of support, such as grants, long-term sponsors, high-risk payment providers, perhaps even (tasteful!) ads. And if you want to contribute your time and skills, we’re always looking for developers, translators, and so on. Thanks for your interest and support. Innovation tokens Let’s start with our tech stack. It is deliberately boring. We use Flask, MariaDB, and ElasticSearch. That is literally it. Search is largely a solved problem, and we don’t intend to reinvent it. Besides, we have to spend our <a %(mcfunley)s>innovation tokens</a> on something else: not being taken down by the authorities. So how legal or illegal is Anna’s Archive exactly? This mostly depends on the legal jurisdiction. Most countries believe in some form of copyright, which means that people or companies are assigned an exclusive monopoly on certain types of works for a certain period of time. As an aside, at Anna’s Archive we believe while there are some benefits, overall copyright is a net-negative for society — but that is a story for another time. This exclusive monopoly on certain works means that it is illegal for anyone outside of this monopoly to directly distribute those works — including us. But Anna’s Archive is a search engine that doesn’t directly distribute those works (at least not on our clearnet website), so we should be okay, right? Not exactly. In many jurisdictions it is not only illegal to distribute copyrighted works, but also to link to places that do. A classic example of this is the United States’ DMCA law. That is the strictest end of the spectrum. On the other end of the spectrum there could theoretically be countries with no copyright laws whatsoever, but these don’t really exist. Pretty much every country has some form of copyright law on the books. Enforcement is a different story. There are plenty of countries with governments that do not care to enforce copyright law. There are also countries in between the two extremes, which prohibit distributing copyrighted works, but do not prohibit linking to such works. Another consideration is at the company-level. If a company operates in a jurisdiction that doesn’t care about copyright, but the company itself is not willing to take any risk, then they might shut down your website as soon as anyone complains about it. Finally, a big consideration is payments. Since we need to stay anonymous, we cannot use traditional payment methods. This leaves us with cryptocurrencies, and only a small subset of companies support those (there are virtual debit cards paid by crypto, but they are often not accepted). - Anna and the team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) I run <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, the world’s largest open-source non-profit search engine for <a %(wikipedia_shadow_library)s>shadow libraries</a>, like Sci-Hub, Library Genesis, and Z-Library. Our goal is to make knowledge and culture readily accessible, and ultimately to build a community of people who together archive and preserve <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>all the books in the world</a>. In this article I’ll show how we run this website, and the unique challenges that come with operating a website with questionable legal status, since there is no “AWS for shadow charities”. <em>Also check out the sister article <a %(blog_how_to_become_a_pirate_archivist)s>How to become a pirate archivist</a>.</em> How to run a shadow library: operations at Anna’s Archive There is no <q>AWS for shadow charities,</q> so how do we run Anna’s Archive? Tools Application server: Flask, MariaDB, ElasticSearch, Docker. Development: Gitlab, Weblate, Zulip. Server management: Ansible, Checkmk, UFW. Onion static hosting: Tor, Nginx. Proxy server: Varnish. Let’s look at what tools we use to accomplish all of this. This is very much evolving as we run into new problems and find new solutions. There are some decisions that we have gone back and forth on. One is the communication between servers: we used to use Wireguard for this, but found that it occasionally stops transmitting any data, or only transmits data in one direction. This happened with several different Wireguard setups that we tried, such as <a %(github_costela_wesher)s>wesher</a> and <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. We also tried tunneling ports over SSH, using autossh and sshuttle, but ran into <a %(github_sshuttle)s>problems there</a> (though it is still not clear to me if autossh suffers from TCP-over-TCP issues or not — it just feels like a janky solution to me but maybe it is actually fine?). Instead, we reverted back to direct connections between servers, hiding that a server is running on the cheap providers using IP-filtering with UFW. This has the downside that Docker doesn't work well with UFW, unless you use <code>network_mode: "host"</code>. All of this is a bit more error-prone, because you will expose your server to the internet with just a tiny misconfiguration. Perhaps we should move back to autossh — feedback would be very welcome here. We’ve also gone back and forth on Varnish vs. Nginx. We currently like Varnish, but it does have its quirks and rough edges. The same applies to Checkmk: we don’t love it, but it works for now. Weblate has been okay but not incredible — I sometimes fear it will lose my data whenever I try to sync it with our git repo. Flask has been good overall, but it has some weird quirks that have cost a lot of time to debug, such as configuring custom domains, or issues with its SqlAlchemy integration. So far the other tools have been great: we have no serious complaints about MariaDB, ElasticSearch, Gitlab, Zulip, Docker, and Tor. All of these have had some issues, but nothing overly serious or time-consuming. Community The first challenge might be a surprising one. It is not a technical problem, or a legal problem. It is a psychological problem: doing this work in the shadows can be incredibly lonely. Depending on what you're planning to do, and your threat model, you might have to be very careful. On the one end of the spectrum we have people like Alexandra Elbakyan*, the founder of Sci-Hub, who is very open about her activities. But she is at high risk of being arrested if she would visit a western country at this point, and could face decades of prison time. Is that a risk you would be willing to take? We are at the other end of the spectrum; being very careful not to leave any trace, and having strong operational security. * As mentioned on HN by "ynno", Alexandra initially didn't want to be known: "Her servers were set up to emit detailed error messages from PHP, including full path of faulting source file, which was under directory /home/<USER>" So, use random usernames on the computers you use for this stuff, in case you misconfigure something. That secrecy, however, comes with a psychological cost. Most people love being recognized for the work that they do, and yet you cannot take any credit for this in real life. Even simple things can be challenging, like friends asking you what you have been up to (at some point "messing with my NAS / homelab" gets old). This is why it is so important to find some community. You can give up some operational security by confiding in some very close friends, who you know you can trust deeply. Even then be careful not to put anything in writing, in case they have to turn over their emails to the authorities, or if their devices are compromised in some other manner. Better still is to find some fellow pirates. If your close friends are interested in joining you, great! Otherwise, you might be able to find others online. Sadly this is still a niche community. So far we have found only a handful of others who are active in this space. Good starting places seem to be the Library Genesis forums, and r/DataHoarder. The Archive Team also has likeminded individuals, though they operate within the law (even if in some grey areas of the law). The traditional "warez" and pirating scenes also have folks who think in similar ways. We are open to ideas on how to foster community and explore ideas. Feel free to message us on Twitter or Reddit. Perhaps we could host some sort of forum or chat group. One challenge is that this can easily get censored when using common platforms, so we would have to host it ourselves. There is also a tradeoff between having these discussions fully public (more potential engagement) versus making it private (not letting potential "targets" know that we're about to scrape them). We'll have to think about that. Let us know if you are interested in this! Conclusion Hopefully this is helpful for newly starting pirate archivists. We're excited to welcome you to this world, so don't hesitate to reach out. Let's preserve as much of the world's knowledge and culture as we can, and mirror it far and wide. Projects 4. Data selection Often you can use the metadata to figure out a reasonable subset of data to download. Even if you eventually want to download all the data, it can be useful to prioritize the most important items first, in case you get detected and defences are improved, or because you would need to buy more disks, or simply because something else comes up in your life before you can download everything. For example, a collection might have multiple editions of the same underlying resource (like a book or a film), where one is marked as being the best quality. Saving those editions first would make a lot of sense. You might eventually want to save all editions, since in some cases the metadata might be tagged incorrectly, or there might be unknown tradeoffs between editions (for example, the "best edition" might be best in most ways but worse in other ways, like a film having a higher resolution but missing subtitles). You can also search your metadata database to find interesting things. What is the biggest file that is hosted, and why is it so big? What is the smallest file? Are there interesting or unexpected patterns when it comes to certain categories, languages, and so on? Are there duplicate or very similar titles? Are there patterns to when data was added, like one day in which many files were added at once? You can often learn a lot by looking at the dataset in different ways. In our case, we deduplicated Z-Library books against the md5 hashes in Library Genesis, thereby saving a lot of download time and disk space. This is a pretty unique situation though. In most cases there are no comprehensive databases of which files are already properly preserved by fellow pirates. This in itself is a huge opportunity for someone out there. It would be great to have a regularly updated overview of things like music and films that are already widely seeded on torrent websites, and are therefore lower priority to include in pirate mirrors. 6. Distribution You have the data, thereby giving you possession of the world's first pirate mirror of your target (most likely). In many ways the hardest part is over, but the riskiest part is still ahead of you. After all, so far you've been stealth; flying under the radar. All you had to do was using a good VPN throughout, not filling in your personal details in any forms (duh), and perhaps using a special browser session (or even a different computer). Now you have to distribute the data. In our case we first wanted to contribute the books back to Library Genesis, but then quickly discovered the difficulties in that (fiction vs non-fiction sorting). So we decided on distribution using Library Genesis-style torrents. If you have the opportunity to contribute to an existing project, then that could save you a lot of time. However, there are not many well-organized pirate mirrors out there currently. So let's say you decide on distributing torrents yourself. Try to keep those files small, so they are easy to mirror on other websites. You will then have to seed the torrents yourself, while still staying anonymous. You can use a VPN (with or without port forwarding), or pay with tumbled Bitcoins for a Seedbox. If you don't know what some of those terms mean, you'll have a bunch of reading to do, since it's important that you understand the risk tradeoffs here. You can host the torrent files themselves on existing torrent websites. In our case, we chose to actually host a website, since we also wanted to spread our philosophy in a clear way. You can do this yourself in a similar manner (we use Njalla for our domains and hosting, paid for with tumbled Bitcoins), but also feel free to contact us to have us host your torrents. We are looking to build a comprehensive index of pirate mirrors over time, if this idea catches on. As for VPN selection, much has been written about this already, so we'll just repeat the general advice of choosing by reputation. Actual court-tested no-log policies with long track records of protecting privacy is the lowest risk option, in our opinion. Note that even when you do everything right, you can never get to zero risk. For example, when seeding your torrents, a highly motivated nation-state actor can probably look at incoming and outgoing data flows for VPN servers, and deduce who you are. Or you can just simply mess up somehow. We probably already have, and will again. Luckily, nation states don't care <em>that</em> much about piracy. One decision to make for each project, is whether to publish it using the same identity as before, or not. If you keep using the same name, then mistakes in operational security from earlier projects could come back to bite you. But publishing under different names means that you don't build a longer lasting reputation. We chose to have strong operational security from the start so we can keep using the same identity, but we won't hesitate to publish under a different name if we mess up or if the circumstances call for it. Getting the word out can be tricky. As we said, this is still a niche community. We originally posted on Reddit, but really got traction on Hacker News. For now our recommendation is to post it in a few places and see what happens. And again, contact us. We would love to spread the word of more pirate archivism efforts. 1. Domain selection / philosophy There is no shortage of knowledge and cultural heritage to be saved, which can be overwhelming. That's why it's often useful to take a moment and think about what your contribution can be. Everyone has a different way of thinking about this, but here are some questions that you could ask yourself: In our case, we cared in particular about the long term preservation of science. We knew about Library Genesis, and how it was fully mirrored many times over using torrents. We loved that idea. Then one day, one of us tried to find some scientific textbooks on Library Genesis, but couldn't find them, bringing into doubt how complete it really was. We then searched those textbooks online, and found them in other places, which planted the seed for our project. Even before we knew about the Z-Library, we had the idea of not trying to collect all those books manually, but to focus on mirroring existing collections, and contributing them back to Library Genesis. What skills do you have that you can use to your benefit? For example, if you are an online security expert, you can find ways of defeating IP blocks for secure targets. If you are great at organizing communities, then perhaps you can rally some people together around a goal. It is useful to know some programming though, if only for keeping good operational security throughout this process. What would be a high-leverage area to focus on? If you're going to spend X hours on pirate archiving, then how can you get the biggest "bang for your buck"? What are unique ways that you are thinking about this? You might have some interesting ideas or approaches that others might have missed. How much time do you have for this? Our advice would be to start small and doing bigger projects as you get the hang of it, but it can get all-consuming. Why are you interested in this? What are you passionate about? If we can get a bunch of people who all archive the kinds of things that they specifically care about, that would cover a lot! You will know a lot more than the average person about your passion, like what is important data to save, what are the best collections and online communities, and so on. 3. Metadata scraping Date added/modified: so you can come back later and download files that you didn't download before (though you can often also use the ID or hash for this). Hash (md5, sha1): to confirm that you downloaded the file properly. ID: can be some internal ID, but IDs like ISBN or DOI are useful too. Filename / location Description, category, tags, authors, language, etc. Size: to calculate how much disk space you need. Let's get a bit more technical here. For actually scraping the metadata from websites, we have kept things pretty simple. We use Python scripts, sometimes curl, and a MySQL database to store the results in. We haven't used any fancy scraping software which can map complex websites, since so far we only needed to scrape one or two kinds of pages by just enumerating through ids and parsing the HTML. If there aren't easily enumerated pages, then you might need a proper crawler that tries to find all pages. Before you start scraping a whole website, try doing it manually for a bit. Go through a few dozen pages yourself, to get a sense for how that works. Sometimes you will already run into IP blocks or other interesting behavior this way. The same goes for data scraping: before getting too deep into this target, make sure you can actually download its data effectively. To get around restrictions, there are a few things you can try. Are there any other IP addresses or servers that host the same data but do not have the same restrictions? Are there any API endpoints that do not have restrictions, while others do? At what rate of downloading does your IP get blocked, and for how long? Or are you not blocked but throttled down? What if you create a user account, how do things change then? Can you use HTTP/2 to keep connections open, and does that increase the rate at which you can request pages? Are there pages that list multiple files at once, and is the information listed there sufficient? Things you probably want to save include: We typically do this in two stages. First we download the raw HTML files, usually directly into MySQL (to avoid lots of small files, which we talk more about below). Then, in a separate step, we go through those HTML files and parse them into actual MySQL tables. This way you don't have to re-download everything from scratch if you discover a mistake in your parsing code, since you can just reprocess the HTML files with the new code. It's also often easier to parallelize the processing step, thus saving some time (and you can write the processing code while the scraping is running, instead of having to write both steps at once). Finally, note that for some targets metadata scraping is all there is. There are some huge metadata collections out there that aren't properly preserved. Title Domain selection / philosophy: Where do you roughly want to focus on, and why? What are your unique passions, skills, and circumstances that you can use to your benefit? Target selection: Which specific collection will you mirror? Metadata scraping: Cataloging information about the files, without actually downloading the (often much larger) files themselves. Data selection: Based on the metadata, narrowing down which data is most relevant to archive right now. Could be everything, but often there is a reasonable way to save space and bandwidth. Data scraping: Actually getting the data. Distribution: Packaging it up in torrents, announcing it somewhere, getting people to spread it. 5. Data scraping Now you're ready to actually download the data in bulk. As mentioned before, at this point you should already manually have downloaded a bunch of files, to better understand the  behavior and restrictions of the target. However, there will still be surprises in store for you once you actually get to downloading lots of files at once. Our advice here is mainly to keep it simple. Start by just downloading a bunch of files. You can use Python, and then expand to multiple threads. But sometimes even simpler is to generate Bash files directly from the database, and then running multiple of them in multiple terminal windows to scale up. A quick technical trick worth mentioning here is using OUTFILE in MySQL, which you can write anywhere if you disable "secure_file_priv" in mysqld.cnf (and be sure to also disable/override AppArmor if you're on Linux). We store the data on simple hard disks. Start out with whatever you have, and expand slowly. It can be overwhelming to think about storing hundreds of TBs of data. If that is the situation that you're facing, just put out a good subset first, and in your announcement ask for help in storing the rest. If you do want to get more hard drives yourself, then r/DataHoarder has some good resources on getting good deals. Try not to worry too much about fancy filesystems. It is easy to fall into the rabbit hole of setting up things like ZFS. One technical detail to be aware of though, is that many filesystems don't deal well with lots of files. We've found that a simple workaround is to create multiple directories, e.g. for different ID ranges or hash prefixes. After downloading the data, be sure to check the integrity of the files using hashes in the metadata, if available. 2. Target selection Accessible: does not use tons of layers of protection to prevent you from scraping their metadata and data. Special insight: you have some special information about this target, like you somehow have special access to this collection, or you figured out how to defeat their defenses. This is not required (our upcoming project does not do anything special), but it certainly helps! Large So, we have our area that we are looking at, now which specific collection do we mirror? There are a couple of things that make for a good target: When we found our science textbooks on websites other than Library Genesis, we tried to figure out how they made their way onto the internet. We then found the Z-Library, and realized that while most books don't first make their appearance there, they do eventually end up there. We learned about its relationship to Library Genesis, and the (financial) incentive structure and superior user interface, both of which made it a much more complete collection. We then did some preliminary metadata and data scraping, and realized that we could get around their IP download limits, leveraging one of our members' special access to lots of proxy servers. As you're exploring different targets, it is already important to hide your tracks by using VPNs and throwaway email addresses, which we'll talk about more later. Unique: not already well-covered by other projects. When we do a project, it has a couple of phases: These are not completely independent phases, and often insights from a later phase send you back to an earlier phase. For example, during metadata scraping you might realize that the target that you selected has defensive mechanisms beyond your skill level (like IP blocks), so you go back and find a different target. - Anna and the team (<a %(reddit)s>Reddit</a>) Entire books can be written about the <em>why</em> of digital preservation in general, and pirate archivism in particular, but let us give a quick primer for those who are not too familiar. The world is producing more knowledge and culture than ever before, but also more of it is being lost than ever before. Humanity largely entrusts corporations like academic publishers, streaming services, and social media companies with this heritage, and they have often not proven to be great stewards. Check out the documentary Digital Amnesia, or really any talk by Jason Scott. There are some institutions that do a good job archiving as much as they can, but they are bound by the law. As pirates, we are in a unique position to archive collections that they cannot touch, because of copyright enforcement or other restrictions. We can also mirror collections many times over, across the world, thereby increasing the chances of proper preservation. For now, we won't get into discussions about the pros and cons of intellectual property, the morality of breaking the law, musings on censorship, or the issue of access to knowledge and culture. With all that out of the way, let's dive into the <em>how</em>. We'll share how our team became pirate archivists, and the lessons that we learned along the way. There are many challenges when you embark on this journey, and hopefully we can help you through some of them. How to become a pirate archivist The first challenge might be a surprising one. It is not a technical problem, or a legal problem. It is a psychological problem. Before we dive in, two updates on the Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>): We got some extremely generous donations. The first was $10k from the anonymous individual who also has been supporting "bookwarrior", the original founder of Library Genesis. Special thanks to bookwarrior for facilitating this donation. The second was another $10k from an anonymous donor, who got in touch after our last release, and was inspired to help. We also had a number of smaller donations. Thanks so much for all your generous support. We have some exciting new projects in the pipeline which this will support, so stay tuned. We had some technical difficulties with the size of our second release, but our torrents are up and seeding now. We also got a generous offer from an anonymous individual to seed our collection on their very-high-speed servers, so we're doing a special upload to their machines, after which everyone else who is downloading the collection should see a large improvement in speed. Blog posts Hi, I’m Anna. I created <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, the world’s largest shadow library. This is my personal blog, in which I and my teammates write about piracy, digital preservation, and more. Connect with me on <a %(reddit)s>Reddit</a>. Note that this website is just a blog. We only host our own words here. No torrents or other copyrighted files are hosted or linked here. <strong>Library</strong> - Like most libraries, we focus primarily on written materials like books. We might expand into other types of media in the future. <strong>Mirror</strong> - We are strictly a mirror of existing libraries. We focus on preservation, not on making books easily searchable and downloadable (access) or fostering a big community of people who contribute new books (sourcing). <strong>Pirate</strong> - We deliberately violate the copyright law in most countries. This allows us to do something that legal entities cannot do: making sure books are mirrored far and wide. <em>We do not link to the files from this blog. Please find it yourself.</em> - Anna and the team (<a %(reddit)s>Reddit</a>) This project (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>) aims to contribute to the preservation and libration of human knowledge. We make our small and humble contribution, in the footsteps of the greats before us. The focus of this project is illustrated by its name: The first library that we have mirrored is Z-Library. This is a popular (and illegal) library. They have taken the Library Genesis collection and made it easily searchable. On top of that, they have become very effective at solliciting new book contributions, by incentivizing contributing users with various perks. They currently do not contribute these new books back to Library Genesis. And unlike Library Genesis, they do not make their collection easily mirrorable, which prevents wide preservation. This is important to their business model, since they charge money for accessing their collection in bulk (more than 10 books per day). We do not make moral judgements about charging money for bulk access to an illegal book collection. It is beyond a doubt that the Z-Library has been successful in expanding access to knowledge, and sourcing more books. We are simply here to do our part: ensuring the long-term preservation of this private collection. We would like to invite you to help preserve and liberate human knowledge by downloading and seeding our torrents. See the project page for more information about how the data is organized. We would also very much invite you to contribute your ideas for which collections to mirror next, and how to go about it. Together we can achieve much. This is but a small contribution among countless others. Thank you, for all that you do. Introducing the Pirate Library Mirror: Preserving 7TB of books (that are not in Libgen) 10%% of humanity’s written heritage preserved forever <strong>Google.</strong> After all, they did this research for Google Books. However, their metadata is not accessible in bulk and rather hard to scrape. <strong>Various individual library systems and archives.</strong> There are libraries and archives that have not been indexed and aggregated by any of the ones above, often because they are underfunded, or for other reasons do not want to share their data with Open Library, OCLC, Google, and so on. A lot of these do have digital records accessible through the internet, and they are often not very well protected, so if you want to help out and have some fun learning about weird library systems, these are great starting points. <strong>ISBNdb.</strong> This is the topic of this blog post. ISBNdb scrapes various websites for book metadata, in particular pricing data, which they then sell to booksellers, so they can price their books in accordance with the rest of the market. Since ISBNs are fairly universal nowadays, they effectively built a “web page for every book”. <strong>Open Library.</strong> As mentioned before, this is their entire mission. They have sourced massive amounts of library data from cooperating libraries and national archives, and continue to do so. They also have volunteer librarians and a technical team that are trying to deduplicate records, and tag them with all sorts of metadata. Best of all, their dataset is completely open. You can simply <a %(openlibrary)s>download it</a>. <strong>WorldCat.</strong> This is a website run by the non-profit OCLC, which sells library management systems. They aggregate book metadata from lots of libraries, and make it available through the WorldCat website. However, they also make money selling this data, so it is not available for bulk download. They do have some more limited bulk datasets available for download, in coorperation with specific libraries. 1. For some reasonable definition of "forever". ;) 2. Of course, humanity’s written heritage is much more than books, especially nowadays. For the sake of this post and our recent releases we’re focusing on books, but our interests stretch further. 3. There is a lot more that can be said about Aaron Swartz, but we just wanted to mention him briefly, since he plays a pivotal part in this story. As time passes, more people might come across his name for the first time, and can subsequently dive into the rabbit hole themselves. <strong>Physical copies.</strong> Obviously this is not very helpful, since they’re just duplicates of the same material. It would be cool if we could preserve all annotations people make in books, like Fermat’s famous “scribbles in the margins”. But alas, that will remain an archivist’s dream. <strong>“Editions”.</strong> Here you count every unique version of a book. If anything about it is different, like a different cover or a different preface, it counts as a different edition. <strong>Files.</strong> When working with shadow libraries like Library Genesis, Sci-Hub, or Z-Library, there is an additional consideration. There can be multiple scans of the same edition. And people can make better versions of existing files, by scanning the text using OCR, or rectifying pages that were scanned at an angle. We want to only count these files as one edition, which would require good metadata, or deduplication using document similarity measures. <strong>“Works”.</strong> For example “Harry Potter and the Chamber of Secrets” as a logical concept, encompassing all versions of it, like different translations and reprints. This is kind of a useful definition, but it can be hard to draw the line of what counts. For example, we probably want to preserve different translations, though reprints with only minor differences might not be as important. - Anna and the team (<a %(reddit)s>Reddit</a>) With the Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), our aim is to take all the books in the world, and preserve them forever.<sup>1</sup> Between our Z-Library torrents, and the original Library Genesis torrents, we have 11,783,153 files. But how many is that, really? If we properly deduplicated those files, what percentage of all the books in the world have we preserved? We’d really like to have something like this: Let’s start with some rough numbers: In both Z-Library/Libgen and Open Library there are many more books than unique ISBNs. Does that mean that lots of those books don’t have ISBNs, or is the ISBN metadata simply missing? We can probably answer this question with a combination of automated matching based on other attributes (title, author, publisher, etc), pulling in more data sources, and extracting ISBNs from the actual book scans themselves (in the case of Z-Library/Libgen). How many of those ISBNs are unique? This is best illustrated with a Venn diagram: To be more precise: We were surprised by how little overlap there is! ISBNdb has a huge amount of ISBNs that do not show up in either Z-Library or Open Library, and the same holds (to a smaller but still substantial degree) for the other two. This raises a lot of new questions. How much would automated matching help in tagging the books that were not tagged with ISBNs? Would there be a lot of matches and therefore increased overlap? Also, what would happen if we bring in a 4th or 5th dataset? How much overlap would we see then? This does give us a starting point. We can now look at all the ISBNs that were not in the Z-Library dataset, and that do not match title/author fields either. That can give us a handle on preserving all the books in the world: first by scraping the internet for scans, then by going out in real life to scan books. The latter could even be crowd-funded, or driven by “bounties” from people who would like to see particular books digitized. All that is a story for a different time. If you want to help out with any of this — further analysis; scraping more metadata; finding more books; OCR’ing of books; doing this for other domains (eg papers, audiobooks, movies, tv shows, magazines) or even making some of this data available for things like ML / large language model training — please contact me (<a %(reddit)s>Reddit</a>). If you’re specifically interested in the data analysis, we are working on making our datasets and scripts available in a more easy to use format. It would be great if you could just fork a notebook and start playing with this. Finally, if you want to support this work, please consider making a donation. This is an entirely volunteer-run operation, and your contribution makes a huge difference. Every bit helps. For now we take donations in crypto; see the Donate page on Anna’s Archive. For a percentage, we need a denominator: the total number of books ever published.<sup>2</sup> Before the demise of Google Books, an engineer on the project, Leonid Taycher, <a %(booksearch_blogspot)s>tried to estimate</a> this number. He came up — tongue-in-cheek — with 129,864,880 (“at least until Sunday”). He estimated this number by building a unified database of all the books in the world. For this, he pulled together different datasets and then merged them in various ways. As a quick aside, there is another person who attempted to catalog all the books in the world: Aaron Swartz, the late digital activist and Reddit co-founder.<sup>3</sup> He <a %(youtube)s>started Open Library</a> with the goal of “one web page for every book ever published”, combining data from lots of different sources. He ended up paying the ultimate price for his digital preservation work when he got prosecuted for bulk-downloading academic papers, leading to his suicide. Needless to say, this is one of the reasons our group is pseudonymous, and why we’re being very careful. Open Library is still heroically being run by folks at the Internet Archive, continuing Aaron’s legacy. We’ll get back to this later in this post. In the Google blog post, Taycher describes some of the challenges with estimating this number. First, what constitutes a book? There are a few possible definitions: “Editions” seem the most practical definition of what “books” are. Conveniently, this definition is also used for assigning unique ISBN numbers. An ISBN, or International Standard Book Number, is commonly used for international commerce, since it is integrated with the international barcode system (”International Article Number”). If you want to sell a book in stores, it needs a barcode, so you get an ISBN. Taycher’s blog post mentions that while ISBNs are useful, they are not universal, since they were only really adopted in the mid-seventies, and not everywhere around the world. Still, ISBN is probably the most widely used identifier of book editions, so it’s our best starting point. If we can find all the ISBNs in the world, we get a useful list of which books still need to be preserved. So, where do we get the data? There are a number of existing efforts that are trying to compile a list of all the books in the world: In this post, we are happy to announce a small release (compared to our previous Z-Library releases). We scraped most of ISBNdb, and made the data available for torrenting on the website of the Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>; we won’t link it here directly, just search for it). These are about 30.9 million records (20GB as <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). On their website they claim that they actually have 32.6 million records, so we might somehow have missed some, or <em>they</em> could be doing something wrong. In any case, for now we will not share exactly how we did it — we will leave that as an exercise for the reader. ;-) What we will share is some preliminary analysis, to try to get closer to estimating the number of books in the world. We looked at three datasets: this new ISBNdb dataset, our original release of metadata that we scraped from the Z-Library shadow library (which includes Library Genesis), and the Open Library data dump. ISBNdb dump, or How Many Books Are Preserved Forever? If we were to properly deduplicate the files from shadow libraries, what percentage of all the books in the world have we preserved? Updates about <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, the largest truly open library in human history. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Anna’s Archive Containers (AAC)</a>, which is essentially <a %(jsonlines)s>JSON Lines</a> compressed with <a %(zstd)s>Zstandard</a>, plus some standardized semantics. These containers wrap various types of records, based on the different scrapes we deployed. A year ago, we <a %(blog)s>set out</a> to answer this question: <strong>What percentage of books have been permanently preserved by shadow libraries?</strong> Let’s look at some basic information on the data: Once a book makes it into an open-data shadow library like <a %(wikipedia_library_genesis)s>Library Genesis</a>, and now <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, it gets mirrored all over the world (through torrents), thereby practically preserving it forever. To answer the question of which percentage of books has been preserved, we need to know the denominator: how many books exist in total? And ideally we don’t just have a number, but actual metadata. Then we can not only match them against shadow libraries, but also <strong>create a TODO list of remaining books to preserve!</strong> We could even start dreaming of a crowdsourced effort to go down this TODO list. We scraped <a %(wikipedia_isbndb_com)s>ISBNdb</a>, and downloaded the <a %(openlibrary)s>Open Library dataset</a>, but the results were unsatisfactory. The main problem was that there was not a ton of overlap of ISBNs. See this Venn diagram from <a %(blog)s>our blog post</a>: We were very surprised by how little overlap there was between ISBNdb and Open Library, both of which liberally include data from various sources, such as web scrapes and library records. If they both do a good job at finding most ISBNs in out there, their circles surely would have substantial overlap, or one would be a subset of the other. It made us wonder, how many books fall <em>completely outside of these circles</em>? We need a bigger database. That is when we set our sights on the largest book database in the world: <a %(wikipedia_worldcat)s>WorldCat</a>. This is a proprietary database by the non-profit <a %(wikipedia_oclc)s>OCLC</a>, which aggregates metadata records from libraries all over the world, in exchange for giving those libraries access to the full dataset, and having them show up in end-users’ search results. Even though OCLC is a non-profit, their business model requires protecting their database. Well, we’re sorry to say, friends at OCLC, we’re giving it all away. :-) Over the past year, we’ve meticulously scraped all WorldCat records. At first, we hit a lucky break. WorldCat was just rolling out their complete website redesign (in Aug 2022). This included a substantial overhaul of their backend systems, introducing many security flaws. We immediately seized the opportunity, and were able scrape hundreds of millions (!) of records in mere days. After that, security flaws were slowly fixed one by one, until the final one we found was patched about a month ago. By that time we had pretty much all records, and were only going for slightly higher quality records. So we felt it is time to release! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Anna’s Archive scraped all of WorldCat (the world’s largest library metadata collection) to make a TODO list of books that need to be preserved.</em> WorldCat Warning: this blog post has been deprecated. We’ve decided that IPFS is not yet ready for prime time. We’ll still link to files on IPFS from Anna’s Archive when possible, but we won’t host it ourselves anymore, nor do we recommend others to mirror using IPFS. Please see our Torrents page if you want to help preserve our collection. Help seed Z-Library on IPFS Partner Server download SciDB External borrow External borrow (print disabled) External download Explore metadata Contained in torrents Back  (+%(num)s bonus) unpaid paid cancelled expired waiting for Anna to confirm invalid Text below continues in English. Go Reset Forward Last If your email address doesn’t work on the Libgen forums, we recommend using <a %(a_mail)s>Proton Mail</a> (free). You can also <a %(a_manual)s>manually request</a> for your account to be activated. (might require <a %(a_browser)s>browser verification</a> — unlimited downloads!) Fast Partner Server #%(number)s (recommended) (slightly faster but with waitlist) (no browser verification required) (no browser verification or waitlists) (no waitlist, but can be very slow) Slow Partner Server #%(number)s Audiobook Comic book Book (fiction) Book (non-fiction) Book (unknown) Journal article Magazine Musical score Other Standards document Not all pages could be converted to PDF Marked broken in Libgen.li Not visible in Libgen.li Not visible in Libgen.rs Fiction Not visible in Libgen.rs Non-Fiction Running exiftool failed on this file Marked as “bad file” in Z-Library Missing from Z-Library Marked as “spam” in Z-Library File can’t be opened (e.g. corrupted file, DRM) Copyright claim Downloading problems (e.g. can’t connect, error message, very slow) Incorrect metadata (e.g. title, description, cover image) Other Poor quality (e.g. formatting issues, poor scan quality, missing pages) Spam / file should be removed (e.g. advertising, abusive content) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brilliant Bookworm Lucky Librarian Dazzling Datahoarder Amazing Archivist Bonus downloads Cerlalc Czech metadata DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Excluding “scimag” Libgen.rs Non-Fiction and Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russian State Library Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads to AA Z-Library Z-Library Chinese Title, author, DOI, ISBN, MD5, … Search Author Description and metadata comments Edition Original filename Publisher (search specific field) Title Year published Technical details This coin has a higher than usual minimum. Please select a different duration or a different coin. Request could not be completed. Please try again in a few minutes, and if it keeps happening contact us at %(email)s with a screenshot. An unknown error occurred. Please contact us at %(email)s with a screenshot. Error in payment processing. Please wait a moment and try again. If the issue persists for more than 24 hours, please contact us at %(email)s with a screenshot. We’re running a fundraiser for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">backing up</a> the largest comics shadow library in the world. Thanks for your support! <a href="/donate">Donate.</a> If you can’t donate, consider supporting us by telling your friends, and following us on <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>. Don’t email us to <a %(a_request)s>request books</a> or small (<10k) <a %(a_upload)s>uploads</a>. Anna’s Archive DMCA / copyright claims Stay in touch Reddit Alternatives SLUM (%(unaffiliated)s) unaffiliated Anna’s Archive needs your help! If you donate now, you get <strong>double</strong> the number of fast downloads. Many try to take us down, but we fight back. If you donate this month, you get <strong>double</strong> the number of fast downloads. Valid until the end of this month. Saving human knowledge: a great holiday gift! Memberships will be extended accordingly. Partner servers are unavailable due to hosting closures. They should be up again soon. To increase the resiliency of Anna’s Archive, we’re looking for volunteers to run mirrors. We have a new donation method available: %(method_name)s. Please consider %(donate_link_open_tag)sdonating</a> — it’s not cheap running this website, and your donation truly makes a difference. Thank you so much. Refer a friend, and both you and your friend get %(percentage)s%% bonus fast downloads! Surprise a loved one, give them an account with membership. The perfect Valentine’s gift! Learn more… Account Activity Advanced Anna’s Blog ↗ Anna’s Software ↗ beta Codes Explorer Datasets Donate Downloaded files FAQ Home Improve metadata LLM data Log in / Register My donations Public profile Search Security Torrents Translate ↗ Volunteering & Bounties Recent downloads: 📚&nbsp;The world’s largest open-source open-data library. ⭐️&nbsp;Mirrors Sci-Hub, Library Genesis, Z-Library, and more. 📈&nbsp;%(book_any)s books, %(journal_article)s papers, %(book_comic)s comics, %(magazine)s magazines — preserved forever.  and  and more DuXiu Internet Archive Lending Library LibGen 📚&nbsp;The largest truly open library in human history. 📈&nbsp;%(book_count)s&nbsp;books, %(paper_count)s&nbsp;papers — preserved forever. ⭐️&nbsp;We mirror %(libraries)s. We scrape and open-source %(scraped)s. All our code and data are completely open source. OpenLib Sci-Hub ,  📚 The world’s largest open-source open-data library.<br>⭐️ Mirrors Scihub, Libgen, Zlib, and more. Z-Lib Anna’s Archive Invalid request. Visit %(websites)s. The world’s largest open-source open-data library. Mirrors Sci-Hub, Library Genesis, Z-Library, and more. Search Anna’s Archive Anna’s Archive Please refresh to try again. <a %(a_contact)s>Contact us</a> if the issue persists for multiple hours. 🔥 Issue loading this page <li>1. Follow us on <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spread the word about Anna’s Archive on Twitter, Reddit, Tiktok, Instagram, at your local cafe or library, or wherever you go! We don’t believe in gatekeeping — if we get taken down we’ll just pop right up elsewhere, since all our code and data is fully open source.</li><li>3. If you are able, consider <a href="/donate">donating</a>.</li><li>4. Help <a href="https://translate.annas-software.org/">translate</a> our website into different languages.</li><li>5. If you are a software engineer, consider contributing to our <a href="https://annas-software.org/">open source</a>, or seeding our <a href="/datasets">torrents</a>.</li> 10. Create or help maintain the Wikipedia page for Anna’s Archive in your language. 11. We are looking to place small, tasteful advertisements. If you’d like to advertise on Anna’s Archive, please let us know. 6. If you are a security researcher, we can use your skills both for offense and defense. Check out our <a %(a_security)s>Security</a> page. 7. We are looking for experts in payments for anonymous merchants. Can you help us add more convenient ways to donate? PayPal, WeChat, gift cards. If you know anyone, please contact us. 8. We are always looking for more server capacity. 9. You can help by reporting file issues, leaving comments, and creating lists right on this website. You can also help by <a %(a_upload)s>uploading more books</a>, or fixing up file issues or formatting of existing books. For more extensive information on how to volunteer, see our <a %(a_volunteering)s>Volunteering & Bounties</a> page. We strongly believe in the free flow of information, and preservation of knowledge and culture. With this search engine, we build on the shoulders of giants. We deeply respect the hard work of the people who have created the various shadow libraries, and we hope that this search engine will broaden their reach. To stay updated on our progress, follow Anna on <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> or <a href="https://t.me/annasarchiveorg">Telegram</a>. For questions and feedback please contact Anna at %(email)s. Account ID: %(account_id)s Logout ❌ Something went wrong. Please reload the page and try again. ✅ You are now logged out. Reload the page to log in again. Fast downloads used (last 24 hours): <strong>%(used)s / %(total)s</strong> Membership: <strong>%(tier_name)s</strong> until %(until_date)s <a %(a_extend)s>(extend)</a> You can combine multiple memberships (fast downloads per 24 hours will be added together). Membership: <strong>None</strong> <a %(a_become)s>(become a member)</a> Contact Anna at %(email)s if you’re interested in upgrading your membership to a higher tier. Public profile: %(profile_link)s Secret key (don’t share!): %(secret_key)s show Join us here! Upgrade to a <a %(a_tier)s>higher tier</a> to join our group. Exclusive Telegram group: %(link)s Account which downloads? Log in Do not lose your key! Invalid secret key. Verify your key and try again, or alternatively register a new account below. Secret key Enter your secret key to log in: Old email-based account? Enter your <a %(a_open)s>email here</a>. Register new account Don’t have an account yet? Registration succesful! Your secret key is: <span %(span_key)s>%(key)s</span> Save this key carefully. If you lose it, you will lose access to your account. <li %(li_item)s><strong>Bookmark.</strong> You can bookmark this page to retrieve your key.</li><li %(li_item)s><strong>Download.</strong> Click <a %(a_download)s>this link</a> to download your key.</li><li %(li_item)s><strong>Password manager.</strong> Use a password manager to save the key when you enter it below.</li> Log in / Register Browser verification Warning: code has incorrect Unicode characters in it, and might behave incorrectly in various situations. The raw binary can be decoded from the base64 representation in the URL. Description Label Prefix URL for a specific code Website Codes starting with <q>%(prefix_label)s</q> Please do not scrape these pages. Instead we recommend <a %(a_import)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases, and running our <a %(a_software)s>open source code</a>. The raw data can be manually explored through JSON files such as <a %(a_json_file)s>this one</a>. Fewer than %(count)s records Generic URL Codes Explorer Index of Explore the codes that records are tagged with, by prefix. The <q>records</q> column shows the number of records tagged with codes with the given prefix, as seen in the search engine (including metadata-only records). The <q>codes</q> column shows how many actual codes have a given prefix. Known code prefix <q>%(key)s</q> More… Prefix %(count)s record matching “%(prefix_label)s” %(count)s records matching “%(prefix_label)s” codes records “%%s” will be substituted with the code’s value Search Anna’s Archive Codes URL for specific code: “%(url)s” This page can take a while to generate, which is why it requires a Cloudflare captcha. <a %(a_donate)s>Members</a> can skip the captcha. Abuse reported: Better version Do you want to report this user for abusive or inappropriate behavior? File issue: %(file_issue)s hidden comment Reply Report abuse You reported this user for abuse. Copyright claims to this email will be ignored; use the form instead. Show email We very much welcome your feedback and questions! However, due to the amount of spam and nonsense emails we get, please check the boxes to confirm you understand these conditions for contacting us. Any other ways of contacting us about copyright claims will be automatically deleted. For DMCA / copyright claims, use <a %(a_copyright)s>this form</a>. Contact email URLs on Anna’s Archive (required). One per line. Please only include URLs that describe the exact same edition of a book. If you want to make a claim for multiple books or multiple editions, please submit this form multiple times. Claims that bundle multiple books or editions together will be rejected. Address (required) Clear description of the source material (required) E-mail (required) URLs to source material, one per line (required). Please include as many as possible, to help us verify your claim (e.g. Amazon, WorldCat, Google Books, DOI). ISBNs of source material (if applicable). One per line. Please only include those that exactly match the edition for which you are reporting a copyright claim. Your name (required) ❌ Something went wrong. Please reload the page and try again. ✅ Thank you for submitting your copyright claim. We will review it as soon as possible. Please reload the page to file another one. <a %(a_openlib)s>Open Library</a> URLs of source material, one per line. Please take a moment to search Open Library for your source material. This will help us verify your claim. Phone number (required) Statement and signature (required) Submit claim If you have a DMCA or other copyright claim, please fill out this form as precisely as possible. If you run into any issues, please contact us at our dedicated DMCA address: %(email)s. Note that claims emailed to this address will not be processed; it is only for questions. Please use the form below to submit your claims. DMCA / Copyright claim form Example record on Anna’s Archive Torrents by Anna’s Archive Anna’s Archive Containers format Scripts for importing metadata If you are interested in mirroring this dataset for <a %(a_archival)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, please contact us. Last updated: %(date)s Main %(source)s website Metadata documentation (most fields) Files mirrored by Anna’s Archive: %(count)s (%(percent)s%%) Resources Total files: %(count)s Total filesize: %(size)s Our blog post about this data <a %(duxiu_link)s>Duxiu</a> is a massive database of scanned books, created by the <a %(superstar_link)s>SuperStar Digital Library Group</a>. Most are academic books, scanned in order to make them available digitally to universities and libraries. For our English-speaking audience, <a %(princeton_link)s>Princeton</a> and the <a %(uw_link)s>University of Washington</a> have good overviews. There is also an excellent article giving more background: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. The books from Duxiu have long been pirated on the Chinese internet. Usually they are being sold for less than a dollar by resellers. They are typically distributed using the Chinese equivalent of Google Drive, which has often been hacked to allow for more storage space. Some technical details can be found <a %(link1)s>here</a> and <a %(link2)s>here</a>. Though the books have been semi-publicly distributed, it is quite difficult to obtain them in bulk. We had this high on our TODO-list, and allocated multiple months of full-time work for it. However, in late 2023 an incredible, amazing, and talented volunteer reached out to us, telling us they had done all this work already — at great expense. They shared the full collection with us, without expecting anything in return, except the guarantee of long-term preservation. Truly remarkable. More information from our volunteers (raw notes): Adapted from our <a %(a_href)s>blog post</a>. DuXiu 读秀 %(count)s file %(count)s files This dataset is closely related to the <a %(a_datasets_openlib)s>Open Library dataset</a>. It contains a scrape of all metadata and a large portion of files from the IA’s Controlled Digital Lending Library. Updates get released in the <a %(a_aac)s>Anna’s Archive Containers format</a>. These records are being referred to directly from the Open Library dataset, but also contains records that are not in Open Library. We also have a number of data files scraped by community members over the years. The collection consists of two parts. You need both parts to get all data (except superseded torrents, which are crossed out on the torrents page). Digital Lending Library our first release, before we standardized on the <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Contains metadata (as json and xml), pdfs (from acsm and lcpdf digital lending systems), and cover thumbnails. incremental new releases, using AAC. Only contains metadata with timestamps after 2023-01-01, since the rest is covered already by “ia”. Also all pdf files, this time from the acsm and “bookreader” (IA’s web reader) lending systems. Despite the name not being exactly right, we still populate bookreader files into the ia2_acsmpdf_files collection, since they are mutually exclusive. IA Controlled Digital Lending 98%%+ of files are searchable. Our mission is to archive all the books in the world (as well as papers, magazines, etc), and make them widely accessible. We believe that all books should be mirrored far and wide, to ensure redundancy and resiliency. This is why we’re pooling together files from a variety of sources. Some sources are completely open and can be mirrored in bulk (such as Sci-Hub). Others are closed and protective, so we try to scrape them in order to “liberate” their books. Yet others fall somewhere in between. All our data can be <a %(a_torrents)s>torrented</a>, and all our metadata can be <a %(a_anna_software)s>generated</a> or <a %(a_elasticsearch)s>downloaded</a> as ElasticSearch and MariaDB databases. The raw data can be manually explored through JSON files such as <a %(a_dbrecord)s>this</a>. Metadata ISBN website Last updated: %(isbn_country_date)s (%(link)s) Resources The International ISBN Agency regularly releases the ranges that it has allocated to national ISBN agencies. From this we can derive what country, region, or language group this ISBN belongs. We currently use this data indirectly, through the <a %(a_isbnlib)s>isbnlib</a> Python library. ISBN country information This is a dump of a lot of calls to isbndb.com during September 2022. We tried to cover all ISBN ranges. These are about 30.9 million records. On their website they claim that they actually have 32.6 million records, so we might somehow have missed some, or <em>they</em> could be doing something wrong. The JSON responses are pretty much raw from their server. One data quality issue that we noticed, is that for ISBN-13 numbers that start with a different prefix than “978-”, they still include an “isbn” field that simply is the ISBN-13 number with the first 3 numbers chopped off (and the check digit recalculated). This is obviously wrong, but this is how they seem to do it, so we didn't alter it. Another potential issue that you might run into, is the fact that the “isbn13” field has duplicates, so you cannot use it as a primary key in a database. “isbn13”+“isbn” fields combined do seem to be unique. Release 1 (2022-10-31) Fiction torrents are behind (though IDs ~4-6M not torrented since they overlap with our Zlib torrents). Our blog post about the comic books release Comics torrents on Anna’s Archive For the backstory of the different Library Genesis forks, see the page for the <a %(a_libgen_rs)s>Libgen.rs</a>. The Libgen.li contains most of the same content and metadata as the Libgen.rs, but has some collections on top of this, namely comics, magazines, and standard documents. It has also integrated <a %(a_scihub)s>Sci-Hub</a> into its metadata and search engine, which is what we use for our database. The metadata for this library is freely available <a %(a_libgen_li)s>at libgen.li</a>. However, this server is slow and doesn’t support resuming broken connections. The same files are also available on <a %(a_ftp)s>an FTP server</a>, which works better. Non-fiction also appears to have diverged, but without new torrents. It appears this has happened since early 2022, though we have not verified this. According to the Libgen.li administrator, the “fiction_rus” (Russian fiction) collection should be covered by regularly released torrents from <a %(a_booktracker)s>booktracker.org</a>, most notably the <a %(a_flibusta)s>flibusta</a> and <a %(a_librusec)s>lib.rus.ec</a> torrents (which we mirror <a %(a_torrents)s>here</a>, though we haven't yet established which torrents correspond to which files). The fiction collection has its own torrents (divergent from <a %(a_href)s>Libgen.rs</a>) starting at %(start)s. Certain ranges without torrents (such as fiction ranges f_3463000 to f_4260000) are likely Z-Library (or other duplicate) files, though we might want to do some deduplication and make torrents for lgli-unique files in these ranges. Statistics for all collections can be found <a %(a_href)s>on libgen's website</a>. Torrents are available for most of the additional content, most notably torrents for comics, magazines, and standard documents have been released in collaboration with Anna’s Archive. Note that the torrent files referring to “libgen.is” are explicitly mirrors of <a %(a_libgen)s>Libgen.rs</a> (“.is” is a different domain used by Libgen.rs). A helpful resource in using the metadata is <a %(a_href)s>this page</a>. %(icon)s Their “fiction_rus” collection (Russian fiction) has no dedicated torrents, but is covered by torrents from others, and we keep a <a %(fiction_rus)s>mirror</a>. Russian fiction torrents on Anna’s Archive Fiction torrents on Anna’s Archive Discussion forum Metadata Metadata via FTP Magazine torrents on Anna’s Archive Metadata field information Mirror of other torrents (and unique fiction and comics torrents) Standard document torrents on Anna’s Archive Libgen.li Torrents by Anna’s Archive (book covers) Library Genesis is known for already generously making their data available in bulk through torrents. Our Libgen collection consists of auxiliary data that they do not release directly, in partnership with them. Much thanks to everyone involved with Library Genesis for working with us! Our blog about the book covers release This page is about the “.rs” version. It is known for consistently publishing both its metadata and the full contents of its book catalog. Its book collection is split between a fiction and non-fiction portion. A helpful resource in using the metadata is <a %(a_metadata)s>this page</a> (blocks IP ranges, VPN might be required). As of 2024-03, new torrents are being posted in <a %(a_href)s>this forum thread</a> (blocks IP ranges, VPN might be required). Fiction torrents on Anna’s Archive Libgen.rs Fiction torrents Libgen.rs Discussion forum Libgen.rs Metadata Libgen.rs metadata field information Libgen.rs Non-fiction torrents Non-Fiction torrents on Anna’s Archive %(example)s for a fiction book. This <a %(blog_post)s>first release</a> is pretty small: about 300GB of book covers from the Libgen.rs fork, both fiction and non-fiction. They are organized in the same way as how they appear on libgen.rs, e.g.: %(example)s for a non-fiction book. Just like with the Z-Library collection, we put them all in a big .tar file, which can be mounted using <a %(a_ratarmount)s>ratarmount</a> if you want to serve the files directly. Release 1 (%(date)s) The quick story of the different Library Genesis (or “Libgen”) forks, is that over time, the different people involved with Library Genesis had a falling out, and went their separate ways. According to this <a %(a_mhut)s>forum post</a>, Libgen.li was originally hosted at “http://free-books.dontexist.com”. The “.fun” version was created by the original founder. It is being revamped in favor of a new, more distributed version. The <a %(a_li)s>“.li” version</a> has a massive collection of comics, as well as other content, that is not (yet) available for bulk download through torrents. It does have a separate torrent collection of fiction books, and it contains the metadata of <a %(a_scihub)s>Sci-Hub</a> in its database. The “.rs” version has very similar data, and most consistently releases their collection in bulk torrents. It is roughly split into a “fiction” and a “non-fiction” section. Originally at “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> in some sense is also a fork of Library Genesis, though they used a different name for their project. Libgen.rs We also enrich our collection with metadata-only sources, which we can match to files, e.g. using ISBN numbers or other fields. Below is an overview of those. Again, some of these sources are completely open, while for others we have to scrape them. Note that in metadata search, we show the original records. We don’t do any merging of records. Metadata-only sources Open Library is an open source project by the Internet Archive to catalog every book in the world. It has one of the world’s largest book scanning operations, and has many books available for digital lending. Its book metadata catalog is freely available for download, and is included on Anna’s Archive (though not currently in search, except if you explicitly search for an Open Library ID). Open Library Excluding duplicates Last updated Percentages of number of files %% mirrored by AA / torrents available Size Source Below is a quick overview of the sources of the files on Anna’s Archive. Since the shadow libraries often sync data from each other, there is considerable overlap between the libraries. That’s why the numbers don’t add up to the total. The “mirrored and seeded by Anna’s Archive” percentage shows how many files we mirror ourselves. We seed those files in bulk through torrents, and make them available for direct download through partner websites. Overview Total Torrents on Anna’s Archive For a background on Sci-Hub, please refer to its <a %(a_scihub)s>official website</a>, <a %(a_wikipedia)s>Wikipedia page</a>, and this <a %(a_radiolab)s>podcast interview</a>. Note that Sci-Hub has been <a %(a_reddit)s>frozen since 2021</a>. It was frozen before, but in 2021 a few million papers were added. Still, some limited number of papers get added to the Libgen “scimag” collections, though not enough to warrant new bulk torrents. We use the Sci-Hub metadata as provided by <a %(a_libgen_li)s>Libgen.li</a> in its “scimag” collection. We also use the <a %(a_dois)s>dois-2022-02-12.7z</a> dataset. Note that the “smarch” torrents are <a %(a_smarch)s>deprecated</a> and therefore not included in our torrents list. Torrents on Libgen.li Torrents on Libgen.rs Metadata and torrents Updates on Reddit Podcast interview Wikipedia page Sci-Hub Sci-Hub: frozen since 2021; most available through torrents Libgen.li: minor additions since then</div> Some source libraries promote the bulk sharing of their data through torrents, while others do not readily share their collection. In the latter case, Anna’s Archive tries to scrape their collections, and make them available (see our <a %(a_torrents)s>Torrents</a> page). There are also in-between situations, for example, where source libraries are willing to share, but don’t have the resources to do so. In those cases, we also try to help out. Below is an overview of how we interface with the different source libraries. Source libraries %(icon)s Various file databases scattered around the Chinese internet; though often paid databases %(icon)s Most files only accessible using premium BaiduYun accounts; slow downloading speeds. %(icon)s Anna’s Archive manages a collection of <a %(duxiu)s>DuXiu files</a> %(icon)s Various metadata databases scattered around the Chinese internet; though often paid databases %(icon)s No easily accessible metadata dumps available for their entire collection. %(icon)s Anna’s Archive manages a collection of <a %(duxiu)s>DuXiu metadata</a> Files %(icon)s Files only available for borrowing on a limited basis, with various access restrictions %(icon)s Anna’s Archive manages a collection of <a %(ia)s>IA files</a> %(icon)s Some metadata available through <a %(openlib)s>Open Library database dumps</a>, but those don’t cover the entire IA collection %(icon)s No easily accessible metadata dumps available for their entire collection %(icon)s Anna’s Archive manages a collection of <a %(ia)s>IA metadata</a> Last updated %(icon)s Anna’s Archive and Libgen.li collaboratively manage collections of <a %(comics)s>comic books</a>, <a %(magazines)s>magazines</a>, <a %(standarts)s>standard documents</a>, and <a %(fiction)s>fiction (diverged from Libgen.rs)</a>. %(icon)s Non-Fiction torrents are shared with Libgen.rs (and mirrored <a %(libgenli)s>here</a>). %(icon)s Quarterly <a %(dbdumps)s>HTTP database dumps</a> %(icon)s Automated torrents for <a %(nonfiction)s>Non-Fiction</a> and <a %(fiction)s>Fiction</a> %(icon)s Anna’s Archive manages a collection of <a %(covers)s>book cover torrents</a> %(icon)s Daily <a %(dbdumps)s>HTTP database dumps</a> Metadata %(icon)s Monthly <a %(dbdumps)s>database dumps</a> %(icon)s Data torrents available <a %(scihub1)s>here</a>, <a %(scihub2)s>here</a>, and <a %(libgenli)s>here</a> %(icon)s Some new files are <a %(libgenrs)s>being</a> <a %(libgenli)s>added</a> to Libgen’s “scimag”, but not enough to warrant new torrents %(icon)s Sci-Hub has frozen new files since 2021. %(icon)s Metadata dumps available <a %(scihub1)s>here</a> and <a %(scihub2)s>here</a>, as well as as part of the <a %(libgenli)s>Libgen.li database</a> (which we use) Source %(icon)s Various smaller or one-off sources. We encourage people to upload to other shadow libraries first, but sometimes people have collections that are too big for others to sort through, though not big enough to warrant their own category. %(icon)s Not available directly in bulk, protected against scraping %(icon)s Anna’s Archive manages a collection of <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Anna’s Archive and Z-Library collaboratively manage a collection of <a %(metadata)s>Z-Library metadata</a> and <a %(files)s>Z-Library files</a> Datasets We combine all the above sources into one unified database that we use to serve this website. This unified database is not available directly, but since Anna’s Archive is fully open source, it can be fairly easily <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases. The scripts on that page will automatically download all the requisite metadata from the sources mentioned above. If you’d like to explore our data before running those scripts locally, you can look at our JSON files, which link further to other JSON files. <a %(a_json)s>This file</a> is a good starting point. Unified database Torrents by Anna’s Archive browse search Various smaller or one-off sources. We encourage people to upload to other shadow libraries first, but sometimes people have collections that are too big for others to sort through, though not big enough to warrant their own category. Overview from <a %(a1)s>datasets page</a>. From <a %(a_href)s>aaaaarg.fail</a>. Appears to be fairly complete. From our volunteer <q>cgiym</q>. From an <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Has fairly high overlap with existing papers collections, but very few MD5 matches, so we decided to keep it completely. Scrape of <q>iRead eBooks</q> (= phonetically <q>ai rit i-books</q>; airitibooks.com), by volunteer <q>j</q>. Corresponds to <q>airitibooks</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. From a collection <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Partly from the original source, partly from the-eye.eu, partly from other mirrors. From a private books torrent website, <a %(a_href)s>Bibliotik</a> (often referred to as <q>Bib</q>), of which books were bundled into torrents by name (A.torrent, B.torrent) and distributed through the-eye.eu. From our volunteer <q>bpb9v</q>. From more information about <a %(a_href)s>CADAL</a>, see the notes in our <a %(a_duxiu)s>DuXiu dataset page</a>. More from our volunteer <q>bpb9v</q>, mostly DuXiu files, as well as a folder <q>WenQu</q> and <q>SuperStar_Journals</q> (SuperStar is the company behind DuXiu). From our volunteer <q>cgiym</q>, Chinese texts from various sources (represented as subdirectories), including from <a %(a_href)s>China Machine Press</a> (a major Chinese publisher). Non-Chinese collections (represented as subdirectories) from our volunteer <q>cgiym</q>. Scrape of books about Chinese architecture, by volunteer <q>cm</q>: <q>I got it by exploiting a network vulnerability at the publishing house, but that loophole has since been closed</q>. Corresponds to <q>chinese_architecture</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. Books from academic publishing house <a %(a_href)s>De Gruyter</a>, collected from a few large torrents. Scrape of <a %(a_href)s>docer.pl</a>, a polish file sharing website focused on books and other written works. Scraped in late 2023 by volunteer <q>p</q>. We don't have good metadata from the original website (not even file extensions), but we filtered for book-like files and were often able to extract metadata from the files themselves. DuXiu epubs, directly from DuXiu, collected by volunteer <q>w</q>. Only recent DuXiu books are available directly through ebooks, so most of these must be recent. Remaining DuXiu files from volunteer <q>m</q>, which weren’t in the DuXiu proprietary PDG format (the main <a %(a_href)s>DuXiu dataset</a>). Collected from many original sources, unfortunately without preserving those sources in the filepath. <span></span> <span></span> <span></span> Scrape of erotic books, by volunteer <q>do no harm</q>. Corresponds to <q>hentai</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. <span></span> <span></span> Collection scraped from a Japanese Manga publisher by volunteer <q>t</q>. <a %(a_href)s>Selected judicial archives of Longquan</a>, provided by volunteer <q>c</q>. Scrape of <a %(a_href)s>magzdb.org</a>, an ally of Library Genesis (it’s linked on the libgen.rs homepage) but who didn’t want to provide their files directly. Obtained by volunteer <q>p</q> in late 2023. <span></span> Various small uploads, too small as their own subcollection, but represented as directories. The <q>oo42hcksBxZYAOjqwGWu</q> directory corresponds to the <q>czech_oo42hcks</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. Ebooks from AvaxHome, a Russian file sharing website. Archive of newspapers and magazines. Corresponds to <q>newsarch_magz</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. Scrape of the <a %(a1)s>Philosophy Documentation Center</a>. Collection of volunteer <q>o</q> who collected Polish books directly from original release (<q>scene</q>) websites. Combined collections of <a %(a_href)s>shuge.org</a> by volunteers <q>cgiym</q> and <q>woz9ts</q>. <span></span> <a %(a_href)s><q>Imperial Library of Trantor</q></a> (named after the fictional library), scraped in 2022 by volunteer <q>t</q>. Corresponds to <q>trantor</q> metadata in <a %(a1)s><q>Other metadata scrapes</q></a>. <span></span> <span></span> <span></span> Sub-sub-collections (represented as directories) from volunteer <q>woz9ts</q>: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (by <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: <q>This site mainly focused on sharing high quality ebook files, some of which are typeset by the owner himself. The owner was <a %(a_arrested)s>arrested in 2019</a>, and someone made a collection of files he shared.</q>). Remaining DuXiu files from volunteer <q>woz9ts</q>, which weren’t in the DuXiu proprietary PDG format (still to be converted to PDF). The <q>upload</q> collection is split up in smaller subcollections, which are indicated in the AACIDs and torrent names. All subcollections were first deduplicated against the main collection, though the metadata <q>upload_records</q> JSON files still contain a lot of references to the original files. Non-book files were also removed from most subcollections, and are typically <em>not</em> noted in the <q>upload_records</q> JSON. The subcollections are: Notes Subcollection Many subcollections themselves are comprised of sub-sub-collections (e.g. from different original sources), which are represented as directories in the <q>filepath</q> fields. Uploads to Anna’s Archive Our blog post about this data <a %(a_worldcat)s>WorldCat</a> is a proprietary database by the non-profit <a %(a_oclc)s>OCLC</a>, which aggregates metadata records from libraries all over the world. It is likely the largest library metadata collection in the world. In October 2023 we <a %(a_scrape)s>released</a> a comprehensive scrape of the OCLC (WorldCat) database, in the <a %(a_aac)s>Anna’s Archive Containers format</a>. October 2023, initial release: OCLC (WorldCat) Torrents by Anna’s Archive Example record on Anna’s Archive (original collection) Example record on Anna’s Archive (“zlib3” collection) Torrents by Anna’s Archive (metadata + content) Blog post about Release 1 Blog post about Release 2 In late 2022, the alleged founders of Z-Library were arrested, and domains were seized by United States authorities. Since then the website has slowly been making its way online again. It is unknown who currently runs it. Update as of February 2023. Z-Library has its roots in the <a %(a_href)s>Library Genesis</a> community, and originally bootstrapped with their data. Since then, it has professionalized considerably, and has a much more modern interface. They are therefore able to get many more donations, both monetarily to keep improving their website, as well as donations of new books. They have amassed a large collection in addition to Library Genesis. The collection consists of three parts. The original description pages for the first two parts are preserved below. You need all three parts to get all data (except superseded torrents, which are crossed out on the torrents page). %(title)s: our first release. This was the very first release of what was then called the “Pirate Library Mirror” (“pilimi”). %(title)s: second release, this time with all files wrapped in .tar files. %(title)s: incremental new releases, using the <a %(a_href)s>Anna’s Archive Containers (AAC) format</a>, now released in collaboration with the Z-Library team. The initial mirror was painstakingly obtained over the course of 2021 and 2022. At this point it is slightly outdated: it reflects the state of the collection in June 2021. We will update this in the future. Right now we are focused on getting this first release out. Since Library Genesis is already preserved with public torrents, and is included in the Z-Library, we did a basic deduplication against Library Genesis in June 2022. For this we used MD5 hashes. There is likely a lot more duplicate content in the library, such as multiple file formats with the same book. This is hard to detect accurately, so we don't. After the deduplication we are left with over 2 million files, totalling just under 7TB. The collection consists of two parts: a MySQL “.sql.gz” dump of the metadata, and the 72 torrent files of around 50-100GB each. The metadata contains the data as reported by the Z-Library website (title, author, description, filetype), as well as the actual filesize and md5sum that we observed, since sometimes these do not agree. There seem to be ranges of files for which the Z-Library itself has incorrect metadata. We might also have incorrectly downloaded files in some isolated cases, which we will try to detect and fix in the future. The large torrent files contain the actual book data, with the Z-Library ID as the filename. The file extensions can be reconstructed using the metadata dump. The collection is a mix of non-fiction and fiction content (not separated out as in Library Genesis). The quality is also widely varying. This first release is now fully available. Note that the torrent files are only available through our Tor mirror. Release 1 (%(date)s) This is a single extra torrent file. It does not contain any new information, but it has some data in it that can take a while to compute. That makes it convenient to have, since downloading this torrent is often faster than computing it from scratch. In particular, it contains SQLite indexes for the tar files, for use with <a %(a_href)s>ratarmount</a>. Release 2 addendum (%(date)s) We have gotten all books that were added to the Z-Library between our last mirror and August 2022. We have also gone back and scraped some books that we missed the first time around. All in all, this new collection is about 24TB. Again, this collection is deduplicated against Library Genesis, since there are already torrents available for that collection. The data is organized similarly to the first release. There is a MySQL “.sql.gz” dump of the metadata, which also includes all the metadata from the first release, thereby superseding it. We also added some new columns: We mentioned this last time, but just to clarify: “filename” and “md5” are the actual properties of the file, whereas “filename_reported” and “md5_reported” are what we scraped from Z-Library. Sometimes these two don't agree with each other, so we included both. For this release, we changed the collation to “utf8mb4_unicode_ci”, which should be compatible with older versions of MySQL. The data files are similar to last time, though they are much bigger. We simply couldn't be bothered creating tons of smaller torrent files. “pilimi-zlib2-0-14679999-extra.torrent” contains all the files that we missed in the last release, while the other torrents are all new ID ranges.  <strong>Update %(date)s:</strong> We made most of our torrents too big, causing torrent clients to struggle. We have removed them and released new torrents. <strong>Update %(date)s:</strong> There were still too many files, so we wrapped them in tar files and released new torrents again. %(key)s: whether this file is already in Library Genesis, in either the non-fiction or fiction collection (matched by md5). %(key)s: which torrent this file is in. %(key)s: set when we were unable to download the book. Release 2 (%(date)s) Zlib releases (original description pages) Tor domain Main website Z-Library scrape The “Chinese” collection in Z-Library appears to be the same as our DuXiu collection, but with different MD5s. We exclude these files from torrents to avoid duplication, but still show them in our search index. Metadata You get %(percentage)s%% bonus fast downloads, because you were referred by user %(profile_link)s. This applies to the entire membership period. Donate Join Selected up to %(percentage)s%% discounts Alipay supports international credit/debit cards. See <a %(a_alipay)s>this guide</a> for more information. Send us Amazon.com gift cards using your credit/debit card. You can buy crypto using credit/debit cards. WeChat (Weixin Pay) supports international credit/debit cards. In the WeChat app, go to “Me → Services → Wallet → Add a Card”. If you don’t see that, enable it using “Me → Settings → General → Tools → Weixin Pay → Enable”. (use when sending Ethereum from Coinbase) copied! copy (lowest minimum amount) (warning: high minimum amount) -%(percentage)s%% 12 months 1 month 24 months 3 months 48 months 6 months 96 months Select how long you want to subscribe for. <div %(div_monthly_cost)s></div><div %(div_after)s>after <span %(span_discount)s></span> discounts</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% for 12 months for 1 month for 24 months for 3 months for 48 months for 6 months for 96 months %(monthly_cost)s / month contact us Direct <strong>SFTP</strong> servers Enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets). Expert Access <strong>Unlimited</strong> high-speed access <div %(div_question)s>Can I upgrade my membership or get multiple memberships?</div> <div %(div_question)s>Can I make a donation without becoming a member?</div> Sure thing. We accept donations of any amount on this Monero (XMR) address: %(address)s. <div %(div_question)s>What do the ranges per month mean?</div> You can get to the lower side of a range by applying all the discounts, such as choosing a period longer than a month. <div %(div_question)s>Do memberships automatically renew?</div> Memberships <strong>do not</strong> automatically renew. You can join for as long or short as you want. <div %(div_question)s>What do you spend donations on?</div> 100%% is going to preserving and making accessible the world's knowledge and culture. Currently we spend it mostly on servers, storage, and bandwidth. No money is going to any team members personally. <div %(div_question)s>Can I make a large donation?</div> That would be amazing! For donations over a few thousand dollars, please contact us directly at %(email)s. <div %(div_question)s>Do you have other payment methods?</div> Currently not. A lot of people don’t want archives like this to exist, so we have to be careful. If you can help us set up other (more convenient) payment methods safely, please get in touch at %(email)s. Donation FAQ You have an <a %(a_donation)s>existing donation</a> in progress. Please finish or cancel that donation before making a new donation. <a %(a_all_donations)s>View all my donations</a> For donations over $5,000, please contact us directly at %(email)s. We welcome large donations from wealthy individuals or institutions.  Be aware that while the memberships on this page are “per month”, they are one-time donations (non-recurring). See the <a %(faq)s>Donation FAQ</a>. Anna’s Archive is a non-profit, open-source, open-data project. By donating and becoming a member, you support our operations and development. To all our members: thank you for keeping us going! ❤️ For more information, check out the <a %(a_donate)s>Donation FAQ</a>. To become a member, please <a %(a_login)s>Log in or Register</a>. Thanks for your support! $%(cost)s / month If you made a mistake during payment, we can’t do refunds, but we’ll try to make it right. Find the “Crypto” page in your PayPal app or website. This is typically under “Finances”. Go to the “Bitcoin” page in your PayPal app or website. Press the “Transfer” button %(transfer_icon)s, and then “Send”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s gift card Bank card Bank card (using app) Binance Credit/debit/Apple/Google (BMC) Cash App Credit/debit card Credit/debit card 2 Credit/debit card (backup) Crypto %(bitcoin_icon)s Card / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regular) Pix (Brazil) Revolut (temporarily unavailable) WeChat Select your preferred crypto coin: Donate using an Amazon gift card. <strong>IMPORTANT:</strong> This option is for %(amazon)s. If you want to use another Amazon website, select it above. <strong>IMPORTANT:</strong> We only support Amazon.com, not other Amazon websites. For example, .de, .co.uk, .ca, are NOT supported. Please do NOT write your own message. Enter the exact amount: %(amount)s Note that we need to round to amounts accepted by our resellers (minimum %(minimum)s). Donate using a credit/debit card, through the Alipay app (super easy to set up). Install the Alipay app from the <a %(a_app_store)s>Apple App Store</a> or <a %(a_play_store)s>Google Play Store</a>. Register using your phone number. No further personal details are required. <span %(style)s>1</span>Install Alipay app Supported: Visa, MasterCard, JCB, Diners Club and Discover. See <a %(a_alipay)s>this guide</a> for more information. <span %(style)s>2</span>Add bank card With Binance, you buy Bitcoin with a credit/debit card or bank account, and then donate that Bitcoin to us. That way we can remain secure and anonymous when accepting your donation. Binance is available in almost every country, and supports most banks and credit/debit cards. This is currently our main recommendation. We appreciate you taking the time to learn how to donate using this method, since it helps us out a lot. For credit cards, debit cards, Apple Pay, and Google Pay, we use “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). In their system, one “coffee” is equal to $5, so your donation will be rounded to the nearest multiple of 5. Donate using Cash App. If you have Cash App, this is the easiest way to donate! Note that for transactions under %(amount)s, Cash App may charge a %(fee)s fee. For %(amount)s or above, it’s free! Donate with a credit or debit card. This method uses a cryptocurrency provider as an intermediate conversion. This can be a bit confusing, so please only use this method if other payment methods don’t work. It also doesn’t work in all countries. We can’t support credit/debit cards directly, because banks don’t want to work with us. ☹ However, there are several ways to use credit/debit cards anyway, using other payment methods: With crypto you can donate using BTC, ETH, XMR, and SOL. Use this option if you are already familiar with cryptocurrency. With crypto you can donate using BTC, ETH, XMR, and more. Crypto express services If you’re using crypto for the first time, we suggest using %(options)s to buy and donate Bitcoin (the original and most used cryptocurrency). Note that for small donations the credit card fees may eliminate our %(discount)s%% discount, so we recommend longer subscriptions. Donate using credit/debit card, PayPal, or Venmo. You can choose between these on the next page. Google Pay and Apple Pay might also work. Note that for small donations the fees are high, so we recommend longer subscriptions. To donate using PayPal US, we’re going to use PayPal Crypto, which allows us to remain anonymous. We appreciate you taking the time to learn how to donate using this method, since it helps us out a lot. Donate using PayPal. Donate using your regular PayPal account. Donate using Revolut. If you have Revolut, this is the easiest way to donate! This payment method only allows for a maximum of %(amount)s. Please select a different duration or payment method. This payment method requires a minimum of %(amount)s. Please select a different duration or payment method. Binance Coinbase Kraken Please select a payment method. “Adopt a torrent”: your username or message in a torrent filename <div %(div_months)s>once every 12 months of membership</div> Your username or anonymous mention in the credits Early access to new features Exclusive Telegram with behind-the-scenes updates %(number)s fast downloads per day if you donate this month! <a %(a_api)s>JSON API</a> access Legendary status in preservation of humanity’s knowledge and culture Previous perks, plus: Earn <strong>%(percentage)s%% bonus downloads</strong> by <a %(a_refer)s>referring friends</a>. SciDB papers <strong>unlimited</strong> without verification When asking account or donation questions, add your account ID, screenshots, receipts, as much information as possible. We only check our email every 1-2 weeks, so not including this information will delay any resolution. To get even more downloads, <a %(a_refer)s>refer your friends</a>! We’re a small team of volunteers. It might take us 1-2 weeks to respond. Note that the account name or picture might look strange. No need to worry! These accounts are managed by our donation partners. Our accounts have not been hacked. Donate <span %(span_cost)s></span> <span %(span_label)s></span> for 12 months “%(tier_name)s” for 1 month “%(tier_name)s” for 24 months “%(tier_name)s” for 3 months “%(tier_name)s” for 48 months “%(tier_name)s” for 6 months “%(tier_name)s” for 96 months “%(tier_name)s” You can still cancel the donation during checkout. Click the donate button to confirm this donation. <strong>Important note:</strong> Crypto prices can fluctuate wildly, sometimes even as much as 20%% in a few minutes. This is still less than the fees we incur with many payment providers, who often charge 50-60%% for working with a “shadow charity” like us. <u>If you send us the receipt with the original price you paid, we will still credit your account for the chosen membership</u> (as long as the receipt is not older than a few hours). We really appreciate that you’re willing to put up with stuff like this in order to support us! ❤️ ❌ Something went wrong. Please reload the page and try again. <span %(span_circle)s>1</span>Buy Bitcoin on Paypal <span %(span_circle)s>2</span>Transfer the Bitcoin to our address ✅ Redirecting to the donation page… Donate Please wait at least <span %(span_hours)s>24 hours</span> (and refresh this page) before contacting us. If you’d like to make a donation (any amount) without membership, feel free to use this Monero (XMR) address: %(address)s. After sending your gift card, our automated system will confirm it within a few minutes. If this doesn’t work, try resending your gift card (<a %(a_instr)s>instructions</a>). If that still doesn’t work please email us and Anna will manually review it (this might take a few days), and be sure to mention if you’ve tried resending already. Example: Please use the <a %(a_form)s>official Amazon.com form</a> to send us a gift card of %(amount)s to the email address below. “To” recipient email in the form: Amazon gift card We cannot accept other methods of gift cards, <strong>only sent directly from the official form on Amazon.com</strong>. We cannot return your gift card if you do not use this form. Only use once. Unique to your account, don’t share. Waiting for gift card… (refresh the page to check) Open the <a %(a_href)s>QR-code donation page</a>. Scan the QR code with the Alipay app, or press the button to open the Alipay app. Please be patient; the page might take a while to load as it’s in China. <span %(style)s>3</span>Make donation (scan QR code or press button) Buy PYUSD coin on PayPal Buy Bitcoin (BTC) on Cash App Buy a bit more (we recommend %(more)s more) than the amount that you’re donating (%(amount)s), to cover transaction fees. You will keep anything left over. Go to the “Bitcoin” (BTC) page in Cash App. Transfer the Bitcoin to our address For small donations (under $25), you might need to use Rush or Priority. Click the “Send bitcoin” button to make a “withdrawal”. Switch from dollars to BTC by pressing the %(icon)s icon. Enter the BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck. Express services are convenient, but charge higher fees. You can use this instead of a crypto exchange if you’re looking to quickly make a larger donation and don’t mind a fee of $5-10. Be sure to send the exact crypto amount shown on the donation page, not the amount in $USD. Otherwise the fee will be subtracted and we can’t automatically process your membership. Sometimes confirmation can take up to 24 hours, so be sure to refresh this page (even if it has expired). Credit / debit card instructions Donate through our credit / debit card page Some of the steps mention crypto wallets, but don’t worry, you don’t have to learn anything about crypto for this. %(coin_name)s instructions Scan this QR code with your crypto wallet app to quickly fill in the payment details Scan QR Code to Pay We only support the standard version of crypto coins, no exotic networks or versions of coins. It can take up to an hour to confirm the transaction, depending on the coin. Donate %(amount)s on <a %(a_page)s>this page</a>. This donation has expired. Please cancel and create a new one. If you have already paid: Yes, I emailed my receipt If the crypto exchange rate fluctuated during the transaction, be sure to include the receipt showing the original exchange rate. We really appreciate you taking the trouble to use crypto, it helps us a lot! ❌ Something went wrong. Please reload the page and try again. <span %(span_circle)s>%(circle_number)s</span>Email us the receipt If you run into any issues, please contact us at %(email)s and include as much information as possible (such as screenshots). ✅ Thanks for your donation! Anna will manually activate your membership within a few days. Send a receipt or screenshot to your personal verification address: When you have emailed your receipt, click this button, so Anna can manually review it (this might take a few days): Send a receipt or screenshot to your personal verification address. Do NOT use this email address for your PayPal donation. Cancel Yes, please cancel Are you sure you wish to cancel? Do not cancel if you have already paid. ❌ Something went wrong. Please reload the page and try again. Make a new donation ✅ Your donation has been canceled. Date: %(date)s Identifier: %(id)s Reorder Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months, including %(discounts)s%% discount)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months)</span> 1. Enter your email. 2. Select your payment method. 3. Select your payment method again. 4. Select “Self-hosted” wallet. 5. Click “I confirm ownership”. 6. You should receive an email receipt. Please send that to us, and we’ll confirm your donation as soon as possible. (you might want to cancel and create a new donation) The payment instructions are now outdated. If you would like to make another donation, use the “Reorder” button above. You have already paid. If you want to review the payment instructions anyway, click here: Show old payment instructions If the donation page gets blocked, try a different internet connection (e.g. VPN or phone internet). Unfortunately, the Alipay page is often only accessible from <strong>mainland China</strong>. You might need to temporarily disable your VPN, or use a VPN to mainland China (or Hong Kong also works sometimes). <span %(span_circle)s>1</span>Donate on Alipay Donate the total amount of %(total)s using <a %(a_account)s>this Alipay account</a> Alipay instructions <span %(span_circle)s>1</span>Transfer to one of our crypto accounts Donate the total amount of %(total)s to one of these addresses: Crypto instructions Follow the instructions to buy Bitcoin (BTC). You only need to buy the amount that you want to donate, %(total)s. Enter our Bitcoin (BTC) address as the recipient, and follow the instructions to send your donation of %(total)s: <span %(span_circle)s>1</span>Donate on Pix Donate the total amount of %(total)s using <a %(a_account)s>this Pix account Pix instructions <span %(span_circle)s>1</span>Donate on WeChat Donate the total amount of %(total)s using <a %(a_account)s>this WeChat account</a> WeChat instructions Use any of the following “credit card to Bitcoin” express services, which only take a few minutes: BTC / Bitcoin address (external wallet): BTC / Bitcoin amount: Fill in the following details in the form: If any of this information is out of date, please email us to let us know. Please use this <span %(underline)s>exact amount</span>. Your total cost might be higher because of credit card fees. For small amounts this may be more than our discount, unfortunately. (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, no verification for first transaction) (minimum: %(minimum)s) (minimum: %(minimum)s depending on country, no verification for first transaction) Follow the instructions to buy PYUSD coin (PayPal USD). Buy a bit more (we recommend %(more)s more) than the amount that you’re donating (%(amount)s), to cover transaction fees. You will keep anything left over. Go to the “PYUSD” page in your PayPal app or website. Press the “Transfer” button %(icon)s, and then “Send”. Update status To reset the timer, simply create a new donation. Be sure to use the BTC amount below, <em>NOT</em> euros or dollars, otherwise we won’t receive the correct amount and can’t automatically confirm your membership. Buy Bitcoin (BTC) on Revolut Buy a bit more (we recommend %(more)s more) than the amount that you’re donating (%(amount)s), to cover transaction fees. You will keep anything left over. Go to the “Crypto” page in Revolut to buy Bitcoin (BTC). Transfer the Bitcoin to our address For small donations (under $25) you might need to use Rush or Priority. Click the “Send bitcoin” button to make a “withdrawal”. Switch from euros to BTC by pressing the %(icon)s icon. Enter the BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck. Status: 1 2 Step-by-step guide See the step-by-step guide below. Otherwise you might get locked out of this account! If you haven’t already, write down your secret key for logging in: Thank you for your donation! Time left: Donation Transfer %(amount)s to %(account)s Waiting for confirmation (refresh the page to check)… Waiting for transfer (refresh the page to check)… Earlier Fast downloads in the last 24 hours count towards the daily limit. Downloads from Fast Partner Servers are marked by %(icon)s. Last 18 hours No files downloaded yet. Downloaded files are not publicly shown. All times are in UTC. Downloaded files If you downloaded a file with both fast and slow downloads, it will show up twice. Don’t worry too much, there are many people downloading from websites linked to by us, and it’s extremely rare to get into trouble. However, to stay safe we recommend using a VPN (paid), or <a %(a_tor)s>Tor</a> (free). I downloaded 1984 by George Orwell, will the police come at my door? You are Anna! Who is Anna? We have one stable JSON API for members, for getting a fast download URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation within JSON itself). For other use cases, such as iterating through all our files, building custom search, and so on, we recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. The raw data can be manually explored <a %(a_explore)s>through JSON files</a>. Our raw torrents list can be downloaded as <a %(a_torrents)s>JSON</a> as well. Do you have an API? We do not host any copyrighted materials here. We are a search engine, and as such only index metadata that is already publicly available. When downloading from these external sources, we would suggest to check the laws in your jurisdiction with respect to what is allowed. We are not responsible for content hosted by others. If you have complaints about what you see on here, your best bet is to contact the original website. We regularly pull their changes into our database. If you really do think you have a valid DMCA complaint we should respond to, please fill out the <a %(a_copyright)s>DMCA / Copyright claim form</a>. We take your complaints seriously, and will get back to you as soon as possible. How do I report copyright infringement? Here are some books that carry special significance to the world of shadow libraries and digital preservation: What are your favorite books? We would also like to remind everyone that all our code and data is completely open source. This is unique for projects like ours — we're not aware of any other project with a similarly massive catalog that is fully open source as well. We very much welcome anyone who thinks we run our project poorly to take our code and data and set up their own shadow library! We're not saying this out of spite or something — we genuinely think this would be awesome since it would raise the bar for everyone, and better preserve humanity's legacy. I hate how you’re running this project! We would love for people to set up <a %(a_mirrors)s>mirrors</a>, and we will financially support this. How can I help? We do indeed. Our inspiration for collecting metadata is Aaron Swartz’ goal of “one web page for every book ever published”, for which he created <a %(a_openlib)s>Open Library</a>. That project has done well, but our unique position allows us to get metadata that they can’t. Another inspiration was our desire to know <a %(a_blog)s>how many books there are in the world</a>, so we can calculate how many books we still have left to save. Do you collect metadata? Note that mhut.org blocks certain IP ranges, so a VPN might be required. <strong>Android:</strong> Click the three-dot menu in the top right, and select “Add to Home Screen”. <strong>iOS:</strong> Click the “Share” button at the bottom, and select “Add to Home Screen”. We don’t have an official mobile app, but you can install this website as an app. Do you have a mobile app? Please send them to the <a %(a_archive)s>Internet Archive</a>. They will properly preserve them. How do I donate books or other physical materials? How do I request books? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regular updates <a %(a_software)s>Anna’s Software</a> — our open source code <a %(a_datasets)s>Datasets</a> — about the data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domains Are there more resources about Anna’s Archive? <a %(a_translate)s>Translate on Anna’s Software</a> — our translation system <a %(a_wikipedia)s>Wikipedia</a> — more about us (please help keep this page updated, or create one for your own language!) Select the settings you like, keep the search box empty, click “Search”, and then bookmark the page using your browser’s bookmark feature. How do I save my search settings? We welcome security researchers to search for vulnerabilities in our systems. We are big proponents of responsible disclosure. Contact us <a %(a_contact)s>here</a>. We are currently unable to award bug bounties, except for vulnerabilities that have the <a %(a_link)s >potential to compromise our anonymity</a>, for which we offer bounties in the $10k-50k range. We’d like to offer wider scope for bug bounties in the future! Please note that social engineering attacks are out of scope. If you are interested in offensive security, and want to help archive the world’s knowledge and culture, be sure to contact us. There are many ways in which you can help. Do you have a responsible disclosure program? We literally do not have enough resources to give everyone in the world high-speed downloads, as much as we’d like to. If a rich benefactor would like to step up and provide this for us, that would be incredible, but until then, we’re trying our best. We’re a non-profit project that can barely sustain itself through donations. This is why we implemented two systems for free downloads, with our partners: shared servers with slow downloads, and slightly faster servers with a waitlist (to reduce the number of people downloading at the same time). We also have <a %(a_verification)s>browser verification</a> for our slow downloads, because otherwise bots and scrapers will abuse them, making things even slower for legitimate users. Note that, when using the Tor Browser, you might need to adjust your security settings. On the lowest of the options, called “Standard”, the Cloudflare turnstile challenge succeeds. On the higher options, called “Safer” and “Safest”, the challenge fails. For large files sometimes slow downloads can break in the middle. We recommend using a download manager (such as JDownloader) to automatically resume large downloads. Why are the slow downloads so slow? Frequently Asked Questions (FAQ) Use the <a %(a_list)s>torrent list generator</a> to generate a list of torrents that are most in need of torrenting, within your storage space limits. Yes, see the <a %(a_llm)s>LLM data</a> page. Most torrents contain the files directly, which means that you can instruct torrent clients to only download the required files. To determine which files to download, you can <a %(a_generate)s>generate</a> our metadata, or <a %(a_download)s>download</a> our ElasticSearch and MariaDB databases. Unfortunately, a number of torrent collections contain .zip or .tar files at the root, in which case you need to download the entire torrent before being able to select individual files. No easy to use tools for filtering down torrents are available yet, but we welcome contributions. (We do have <a %(a_ideas)s>some ideas</a> for the latter case though.) Long answer: Short answer: not easily. We try to keep minimal duplication or overlap between the torrents in this list, but this can’t always be achieved, and depends heavily on the policies of the source libraries. For libraries that put out their own torrents, it’s out of our hands. For torrents released by Anna’s Archive, we deduplicate only based on MD5 hash, which means that different versions of the same book don’t get deduplicated. Yes. These are actually PDFs and EPUBs, they just don’t have an extension in many of our torrents. There are two places in which you can find the metadata for torrent files, including the file types/extensions: 1. Each collection or release has its own metadata. For example, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> have a corresponding metadata database hosted on the Libgen.rs website. We typically link to relevant metadata resources from each collection’s <a %(a_datasets)s>dataset page</a>. 2. We recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. These contains a mapping for each record in Anna’s Archive to its corresponding torrent files (if available), under “torrent_paths” in the ElasticSearch JSON. Some torrent clients don't support large piece sizes, which a lot of our torrents have (for newer ones we don't do this anymore — even though it’s valid per specs!). So try a different client if you run into this, or complain to the makers of your torrent client. I would like to help seed, but I don’t have much disk space. The torrents are too slow; can I download the data directly from you? Can I download only a subset of the files, like only a particular language or topic? How do you handle duplicates in the torrents? Can I get the torrent list as JSON? I don’t see PDFs or EPUBs in the torrents, only binary files? What do I do? Why can't my torrent client open some of your torrent files / magnet links? Torrents FAQ How do I upload new books? Please see <a %(a_href)s>this excellent project</a>. Do you have an uptime monitor? What is Anna’s Archive? Become a member to use fast downloads. We now support Amazon gift cards, credit and debit cards, crypto, Alipay, and WeChat. You’ve run out of fast downloads today. Access Hourly downloads in the last 30 days. Hourly average: %(hourly)s. Daily average: %(daily)s. We work with partners to make our collections easily and freely accessible to anyone. We believe that everyone has a right to the collective wisdom of humanity. And <a %(a_search)s>not at the expense of authors</a>. The datasets used in Anna’s Archive are completely open, and can be mirrored in bulk using torrents. <a %(a_datasets)s>Learn more…</a> Long-term archive Full database Search Books, papers, magazines, comics, library records, metadata, … All our <a %(a_code)s>code</a> and <a %(a_datasets)s>data</a> are completely open source. <span %(span_anna)s>Anna’s Archive</span> is a non-profit project with two goals: <li><strong>Preservation:</strong> Backing up all knowledge and culture of humanity.</li><li><strong>Access:</strong> Making this knowledge and culture available to anyone in the world.</li> We have the world’s largest collection of high-quality text data. <a %(a_llm)s>Learn more…</a> LLM training data 🪩 Mirrors: call for volunteers If you run a high-risk anonymous payment processor, please contact us. We are also looking for people looking to place tasteful small ads. All proceeds go to our preservation efforts. Preservation We estimate that we have preserved about <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% of the world’s books</a>. We preserve books, papers, comics, magazines, and more, by bringing these materials from various <a href="https://en.wikipedia.org/wiki/Shadow_library">shadow libraries</a>, official libraries, and other collections together in one place. All this data is preserved forever by making it easy to duplicate it in bulk — using torrents — resulting in many copies around the world. Some shadow libraries already do this themselves (e.g. Sci-Hub, Library Genesis), while Anna’s Archive “liberates” other libraries that don’t offer bulk distribution (e.g. Z-Library) or aren’t shadow libraries at all (e.g. Internet Archive, DuXiu). This wide distribution, combined with open-source code, makes our website resilient to takedowns, and ensures the long-term preservation of humanity’s knowledge and culture. Learn more about <a href="/datasets">our datasets</a>. If you’re a <a %(a_member)s>member</a>, browser verification is not required. 🧬&nbsp;SciDB is a continuation of Sci-Hub. SciDB Open DOI Sci-Hub has <a %(a_paused)s>paused</a> uploading of new papers. Direct access to %(count)s academic papers 🧬&nbsp;SciDB is a continuation of Sci-Hub, with its familiar interface and direct viewing of PDFs. Enter your DOI to view. We have the full Sci-Hub collection, as well as new papers. Most can be viewed directly with a familiar interface, similar to Sci-Hub. Some can be downloaded through external sources, in which case we show links to those. You can help out enormously by seeding torrents. <a %(a_torrents)s>Learn more…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Looking for volunteers As a non-profit, open-source project, we’re always looking for people to help out. IPFS downloads List by %(by)s, created <span %(span_time)s>%(time)s</span> Save ❌ Something went wrong. Please try again. ✅ Saved. Please reload the page. List is empty. edit Add or remove from this list by finding a file and opening the “Lists” tab. List How we can help Removing overlap (deduplication) Text and metadata extraction OCR We’re able to provide high-speed access to our full collections, as well as to unreleased collections. This is enterprise-level access that we can provide for donations in the range of tens of thousands USD. We’re also willing to trade this for high-quality collections that we don’t have yet. We can refund you if you’re able to provide us with enrichment of our data, such as: Support long-term archival of human knowledge, while getting better data for your model! <a %(a_contact)s>Contact us</a> to discuss how we can work together. It is well understood that LLMs thrive on high-quality data. We have the largest collection of books, papers, magazines, etc in the world, which are some of the highest quality text sources. LLM data Unique scale and range Our collection contains over a hundred million files, including academic journals, textbooks, and magazines. We achieve this scale by combining large existing repositories. Some of our source collections are already available in bulk (Sci-Hub, and parts of Libgen). Other sources we liberated ourselves. <a %(a_datasets)s>Datasets</a> shows a full overview. Our collection includes millions of books, papers, and magazines from before the e-book era. Large parts of this collection have already been OCR’ed, and already have little internal overlap. Continue If you lost your key, please <a %(a_contact)s>contact us</a> and provide as much information as possible. You might have to temporarily create a new account to contact us. Please <a %(a_account)s>login</a> to view this page.</a> To prevent spam-bots from creating lots of accounts, we need to verify your browser first. If you get caught in an infinite loop, we recommend installing <a %(a_privacypass)s>Privacy Pass</a>. It may also help to turn off ad blockers and other browser extensions. Log in / Register Anna’s Archive is temporarily down for maintenance. Please come back in an hour. Alternative author Alternative description Alternative edition Alternative extension Alternative filename Alternative publisher Alternative title date open sourced Read more… description Search Anna’s Archive for CADAL SSNO number Search Anna’s Archive for DuXiu SSID number Search Anna’s Archive for DuXiu DXID number Search Anna’s Archive for ISBN Search Anna’s Archive for OCLC (WorldCat) number Search Anna’s Archive for Open Library ID Anna’s Archive online viewer %(count)s affected pages After downloading: A better version of this file might be available at %(link)s Bulk torrent downloads collection Use online tools to convert between formats. Recommended conversion tools: %(links)s For large files, we recommend using a download manager to prevent interruptions. Recommended download managers: %(links)s EBSCOhost eBook Index (experts only) (also click “GET” at the top) (click “GET” at the top) External downloads You have %(remaining)s left today. Thanks for being a member! ❤️ You’ve run out of fast downloads for today. You downloaded this file recently. Links remain valid for a while. Become a <a %(a_membership)s>member</a> to support the long-term preservation of books, papers, and more. To show our gratitude for your support, you get fast downloads. ❤️ 🚀 Fast downloads 🐢 Slow downloads Borrow from the Internet Archive IPFS Gateway #%(num)d (you might need to try multiple times with IPFS) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction their ads are known to contain malicious software, so use an ad blocker or don’t click ads Amazon‘s “Send to Kindle” djazz‘s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC files can be unreliable to download) No downloads found. All download options have the same file, and should be safe to use. That said, always be cautious when downloading files from the internet, especially from sites external to Anna’s Archive. For example, be sure to keep your devices updated. (no redirect) Open in our viewer (open in viewer) Option #%(num)d: %(link)s %(extra)s Find original record in CADAL Search manually on DuXiu Find original record in ISBNdb Find original record in WorldCat Find original record in Open Library Search various other databases for ISBN (print disabled patrons only) PubMed You will need an ebook or PDF reader to open the file, depending on the file format. Recommended ebook readers: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (associated DOI might not be available in Sci-Hub) You can send both PDF and EPUB files to your Kindle or Kobo eReader. Recommended tools: %(links)s More information in the <a %(a_slow)s>FAQ</a>. Support authors and libraries If you like this and can afford it, consider buying the original, or supporting the authors directly. If this is available at your local library, consider borrowing it for free there. Partner Server downloads temporarily not available for this file. torrent From trusted partners. Z-Library Z-Library on Tor (requires the Tor Browser) show external downloads <span class="font-bold">❌ This file might have issues, and has been hidden from a source library.</span> Sometimes this is by request of a copyright holder, sometimes it is because a better alternative is available, but sometimes it is because of an issue with the file itself. It might still be fine to download, but we recommend first searching for an alternative file. More details: If you still want to download this file, be sure to only use trusted, updated software to open it. metadata comments AA: Search Anna’s Archive for “%(name)s” Codes Explorer: View in Codes Explorer “%(name)s” URL: Website: If you have this file and it’s not yet available in Anna’s Archive, consider <a %(a_request)s>uploading it</a>. Internet Archive Controlled Digital Lending file “%(id)s” This is a record of a file from the Internet Archive, not a directly downloadable file. You can try to borrow the book (link below), or use this URL when <a %(a_request)s>requesting a file</a>. Improve metadata CADAL SSNO %(id)s metadata record This is a metadata record, not a downloadable file. You can use this URL when <a %(a_request)s>requesting a file</a>. DuXiu SSID %(id)s metadata record ISBNdb %(id)s metadata record MagzDB ID %(id)s metadata record Nexus/STC ID %(id)s metadata record OCLC (WorldCat) number %(id)s metadata record Open Library %(id)s metadata record Sci-Hub file “%(id)s” Not found “%(md5_input)s” was not found in our database. Add comment (%(count)s) You can get the md5 from the URL, e.g. MD5 of a better version of this file (if applicable). Fill this in if there is another file that closely matches this file (same edition, same file extension if you can find one), which people should use instead of this file. If you know of a better version of this file outside of Anna’s Archive, then please <a %(a_upload)s>upload it</a>. Something went wrong. Please reload the page and try again. You left a comment. It might take a minute for it to show up. Please use the <a %(a_copyright)s>DMCA / Copyright claim form</a>. Describe the issue (required) If this file has great quality, you can discuss anything about it here! If not, please use the “Report file issue” button. Great file quality (%(count)s) File quality Learn how to <a %(a_metadata)s>improve the metadata</a> for this file yourself. Issue description Please <a %(a_login)s>log in</a>. I loved this book! Help out the community by reporting the quality of this file! 🙌 Something went wrong. Please reload the page and try again. Report file issue (%(count)s) Thank you for submitting your report. It will be shown on this page, as well as reviewed manually by Anna (until we have a proper moderation system). Leave comment Submit report What is wrong with this file? Borrow (%(count)s) Comments (%(count)s) Downloads (%(count)s) Explore metadata (%(count)s) Lists (%(count)s) Stats (%(count)s) For information about this particular file, check out its <a %(a_href)s>JSON file</a>. This is a file managed by the <a %(a_ia)s>IA’s Controlled Digital Lending</a> library, and indexed by Anna’s Archive for search. For information about the various datasets that we have compiled, see the <a %(a_datasets)s>Datasets page</a>. Metadata from linked record Improve metadata on Open Library A “file MD5” is a hash that gets computed from the file contents, and is reasonably unique based on that content. All shadow libraries that we have indexed on here primarily use MD5s to identify files. A file might appear in multiple shadow libraries. For information about the various datasets that we have compiled, see the <a %(a_datasets)s>Datasets page</a>. Report file quality Total downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Czech metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Warning: multiple linked records: When you look at a book on Anna’s Archive, you can see various fields: title, author, publisher, edition, year, description, filename, and more. All those pieces of information are called <em>metadata</em>. Since we combine books from various <em>source libraries</em>, we show whatever metadata is available in that source library. For example, for a book that we got from Library Genesis, we’ll show the title from Library Genesis’ database. Sometimes a book is present in <em>multiple</em> source libraries, which might have different metadata fields. In that case, we simply show the longest version of each field, since that one hopefully contains the most useful information! We’ll still show the other fields below the description, e.g. as ”alternative title” (but only if they are different). We also extract <em>codes</em> such as identifiers and classifiers from the source library. <em>Identifiers</em> uniquely represent a particular edition of a book; examples are ISBN, DOI, Open Library ID, Google Books ID, or Amazon ID. <em>Classifiers</em> group together multiple similar books; examples are Dewey Decimal (DCC), UDC, LCC, RVK, or GOST. Sometimes these codes are explicitly linked in source libraries, and sometimes we can extract them from the filename or description (primarily ISBN and DOI). We can use identifiers to find records in <em>metadata-only collections</em>, such as OpenLibrary, ISBNdb, or WorldCat/OCLC. There is a specific <em>metadata tab</em> in our search engine if you’d like to browse those collections. We use matching records to fill in missing metadata fields (e.g. if a title is missing), or e.g. as “alternative title” (if there is an existing title). To see exactly where metadata of a book came from, see the <em>“Technical details” tab</em> on a book page. It has a link to the raw JSON for that book, with pointers to the raw JSON of the original records. For more information, see the following pages: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, and <a %(a_example)s>Example metadata JSON</a>. Finally, all our metadata can be <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases. Background You can help out preservation of books by improving metadata! First, read the background about metadata on Anna’s Archive, and then learn how to improve metadata through linking with Open Library, and earn free membership on Anna’s Archive. Improve metadata So if you encounter a file with bad metadata, how should you fix it? You can go to the source library and follow its procedures for fixing metadata, but what to do if a file is present in multiple source libraries? There is one identifier that is treated special on Anna’s Archive. <strong>The annas_archive md5 field on Open Library always overrides all other metadata!</strong> Let’s back up a bit first and learn about Open Library. Open Library was founded in 2006 by Aaron Swartz with the goal of “one web page for every book ever published”. It is kind of a Wikipedia for book metadata: everyone can edit it, it is freely licensed, and can be downloaded in bulk. It’s a book database that is most aligned with our mission — in fact, Anna’s Archive has been inspired by Aaron Swartz’ vision and life. Instead of reinventing the wheel, we decided to redirect our volunteers towards Open Library. If you see a book that has incorrect metadata, you can help out in the following way: Note that this only works for books, not academic papers or other types of files. For other types of files we still recommend finding the source library. It might take a few weeks for changes to be included in Anna’s Archive, since we need to download the latest Open Library data dump, and regenerate our search index.  Go to the <a %(a_openlib)s>Open Library website</a>. Find the correct book record. <strong>WARNING:</strong> be sure to select the correct <strong>edition</strong>. In Open Library, you have “works” and “editions”. A “work” could be “Harry Potter and the Philosopher's Stone”. An “edition” could be: The 1997 first edition published by Bloomsbery with 256 pages. The 2003 paperback edition published by Raincoast Books with 223 pages. The 2000 Polish translation “Harry Potter I Kamie Filozoficzn” by Media Rodzina with 328 pages. All of those editions have different ISBNs and different contents, so be sure to select the right one! Edit the record (or create it if none exist), and add as much as useful information as you can! You’re here now anyway, might as well make the record really amazing. Under “ID Numbers” select “Anna’s Archive” and add the MD5 of the book from Anna’s Archive. This is the long string of letters and numbers after “/md5/” in the URL. Try to find other files in Anna’s Archive that also match this record, and add those as well. In the future we can group those as duplicates on Anna’s Archive search page. When you’re done, write down the URL that you just updated. Once you’ve updated at least 30 records with Anna’s Archive MD5s, send us an <a %(a_contact)s>email</a> and send us the list. We’ll give you a free membership for Anna’s Archive, so you can more easily do this work (and as a thank you for your help). These have to be high quality edits that add substantial amounts of information, otherwise your request will be rejected. Your request will also be rejected if any of the edits get reverted or corrected by Open Library moderators. Open Library linking If you get significantly involved in the development and operations of our work, we can discuss sharing more of the donation revenue with you, for you to deploy as necessary. We will only pay for hosting once you have everything set up, and have demonstrated that you’re able to keep the archive up to date with updates. This means you’ll have to pay for the first 1-2 months out of pocket. Your time will not be compensated (and neither is ours), since this is pure volunteer work. We’re willing to cover hosting and VPN expenses, initially up to $200 per month. This is sufficient for a basic search server and a DMCA-protected proxy. Hosting expenses Please <strong>do not contact us</strong> to ask for permission, or for basic questions. Actions speak louder than words! All the information is out there, so just go ahead with setting up your mirror. Do feel free to post tickets or merge requests to our Gitlab when you run into issues. We might need to build some mirror-specific features with you, such as rebranding from “Anna’s Archive” to your website name, (initially) disabling user accounts, or linking back to our main site from book pages. Once you have your mirror running, please do contact us. We’d love to review your OpSec, and once that’s solid, we’ll link to your mirror, and start working closer together with you. Thanks in advance to anyone willing to contribute in this way! It’s not for the faint of heart, but it would solidify the longevity of the largest truly open library in human history. Getting started To increase the resiliency of Anna’s Archive, we’re looking for volunteers to run mirrors. Your version is clearly distinguished as a mirror, e.g. “Bob’s Archive, an Anna’s Archive mirror”. You are willing to take the risks associated with this work, which are significant. You have a deep understanding of the operational security required. The contents of <a %(a_shadow)s>these</a> <a %(a_pirate)s>posts</a> are self-evident to you. Initially we will not give you access to our partner server downloads, but if things go well, we can share that with you. You run the Anna’s Archive open source codebase, and you regularly update both the code and the data. You are willing to contribute to our <a %(a_codebase)s>codebase</a> — in collaboration with our team — in order to make this happen. We are looking for this: Mirrors: call for volunteers Make another donation. No donations yet. <a %(a_donate)s>Make my first donation.</a> Donations details are not publicly shown. My donations 📡 For bulk mirroring of our collection, check out the <a %(a_datasets)s>Datasets</a> and <a %(a_torrents)s>Torrents</a> pages. Downloads from your IP address in the last 24 hours: %(count)s. 🚀 To get faster downloads and skip the browser checks, <a %(a_membership)s>become a member</a>. Download from partner website Feel free to continue browsing Anna’s Archive in a different tab while waiting (if your browser supports refreshing background tabs). Feel free to wait for multiple download pages to load at the same time (but please only download one file at the same time per server). Once you get a download link it is valid for several hours. Thanks for waiting, this keeps the website accessible for free for everyone! 😊 <a %(a_main)s>&lt; All download links for this file</a> ❌ Slow downloads are not available through Cloudflare VPNs or otherwise from Cloudflare IP addresses. ❌ Slow downloads are only available through the official website. Visit %(websites)s. <a %(a_download)s>📚 Download now</a> In order to give everyone an opportunity to download files for free, you need to wait before you can download this file. Please wait <span %(span_countdown)s>%(wait_seconds)s</span> seconds to download this file. Warning: there have been lots of downloads from your IP address in the last 24 hours. Downloads might be slower than usual. If you’re using a VPN, shared internet connection, or your ISP shares IPs, this warning this might be due to that. Save ❌ Something went wrong. Please try again. ✅ Saved. Please reload the page. Change your display name. Your identifier (the part after “#”) cannot be changed. Profile created <span %(span_time)s>%(time)s</span> edit Lists Create a new list by finding a file and opening the “Lists” tab. No lists yet Profile not found. Profile At this time, we cannot accomodate book requests. Do not email us your book requests. Please make your requests on Z-Library or Libgen forums. Record in Anna’s Archive DOI: %(doi)s Download SciDB Nexus/STC No preview available yet. Download file from <a %(a_path)s>Anna’s Archive</a>. To support the accessibility and long-term preservation of human knowledge, become a <a %(a_donate)s>member</a>. As a bonus, 🧬&nbsp;SciDB loads faster for members, without any limits. Not working? Try <a %(a_refresh)s>refreshing</a>. Sci-Hub Add specific search field Search descriptions and metadata comments Year published Advanced Access Content Display List Table Filetype Language Order by Largest Most relevant Newest (filesize) (open sourced) (publication year) Oldest Random Smallest Source scraped and open-sourced by AA Digital Lending (%(count)s) Journal Articles (%(count)s) We have found matches in: %(in)s. You can refer to the URL found there when <a %(a_request)s>requesting a file</a>. Metadata (%(count)s) To explore the search index by codes, use the <a %(a_href)s>Codes Explorer</a>. The search index is updated monthly. It currently includes entries up to %(last_data_refresh_date)s. For more technical information, see the %(link_open_tag)sdatasets page</a>. Exclude Include only Unchecked more… Next … Previous This search index currently includes metadata from the Internet Archive’s Controlled Digital Lending library. <a %(a_datasets)s>More about our datasets</a>. For more digital lending libraries, see <a %(a_wikipedia)s>Wikipedia</a> and the <a %(a_mobileread)s>MobileRead Wiki</a>. For DMCA / copyright claims <a %(a_copyright)s>click here</a>. Download time Error during search. Try <a %(a_reload)s>reloading the page</a>. If the problem persists, please email us at %(email)s. Fast download In fact, anyone can help preserve these files by seeding our <a %(a_torrents)s>unified list of torrents</a>. ➡️ Sometimes this happens incorrectly when the search server is slow. In such cases, <a %(a_attrs)s>reloading</a> can help. ❌ This file might have issues. Looking for papers? This search index currently includes metadata from various metadata sources. <a %(a_datasets)s>More about our datasets</a>. There are many, many sources of metadata for written works around the world. <a %(a_wikipedia)s>This Wikipedia page</a> is a good start, but if you know of other good lists, please let us know. For metadata, we show the original records. We don’t do any merging of records. We currently have the world’s most comprehensive open catalog of books, papers, and other written works. We mirror Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>and more</a>. <span %(classname)s>No files found.</span> Try fewer or different search terms and filters. Results %(from)s-%(to)s (%(total)s total) If you find other “shadow libraries” that we should mirror, or if you have any questions, please contact us at %(email)s. %(num)d partial matches %(num)d+ partial matches Type in the box to search for files in digital lending libraries. Type in the box to search our catalog of %(count)s directly downloadable files, which we <a %(a_preserve)s>preserve forever</a>. Type in the box to search. Type in the box to search our catalog of %(count)s academic papers and journal articles, which we <a %(a_preserve)s>preserve forever</a>. Type in the box to search for metadata from libraries. This can be useful when <a %(a_request)s>requesting a file</a>. Tip: use keyboard shortcuts “/” (search focus), “enter” (search), “j” (up), “k” (down), “<” (prev page), “>” (next page) for quicker navigation. These are metadata records, <span %(classname)s>not</span> downloadable files. Search settings Search Digital Lending Download Journal articles Metadata New search %(search_input)s - Search The search took too long, which means you might see inaccurate results. Sometimes <a %(a_reload)s>reloading</a> the page helps. The search took too long, which is common for broad queries. The filter counts may not be accurate. For large uploads (over 10,000 files) that don’t get accepted by Libgen or Z-Library, please contact us at %(a_email)s. For Libgen.li, make sure to first login on <a %(a_forum)s >their forum</a> with username %(username)s and password %(password)s, and then return to their <a %(a_upload_page)s >upload page</a>. For now, we suggest uploading new books to the Library Genesis forks. Here is a <a %(a_guide)s>handy guide</a>. Note that both forks that we index on this website pull from this same upload system. For small uploads (up to 10,000 files) please upload them to both %(first)s and %(second)s. Alternatively, you can upload them to Z-Library <a %(a_upload)s>here</a>. To upload academic papers, please also (in addition to Library Genesis) upload to <a %(a_stc_nexus)s>STC Nexus</a>. They are the best shadow library for new papers. We haven’t integrated them yet, but we will at some point. You can use their <a %(a_telegram)s>upload bot on Telegram</a>, or contact the address listed in their pinned message if you have too many files to upload this way. <span %(label)s>Heavy volunteering work (USD$50-USD$5,000 bounties):</span> if you’re able to dedicate lots of time and/or resources to our mission, we’d love to work more closely with you. Eventually you can join the inner team. Though we have a tight budget, we’re able to award <span %(bold)s>💰 monetary bounties</span> for the most intense work. <span %(label)s>Light volunteering work:</span> if you can only spare a few hours here and there, there are still plenty of ways you can help out. We reward consistent volunteers with <span %(bold)s>🤝 memberships to Anna’s Archive</span>. Anna’s Archive relies on volunteers like you. We welcome all commitment levels, and have two main categories of help we’re looking for: If you’re unable to volunteer your time, you can still help us a lot by <a %(a_donate)s>donating money</a>, <a %(a_torrents)s>seeding our torrents</a>, <a %(a_uploading)s>uploading books</a>, or <a %(a_help)s>telling your friends about Anna’s Archive</a>. <span %(bold)s>Companies:</span> we offer high-speed direct access to our collections in exchange for enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets, enriching our data). <a %(a_contact)s>Contact us</a> if this is you. See also our <a %(a_llm)s>LLM page</a>. Bounties We’re always looking for people with solid programming or offensive security skills to get involved. You can make a serious dent in preserving humanity’s legacy. As a thank you, we give away membership for solid contributions. As a huge thank you, we give away monetary bounties for particularly important and difficult tasks. This shouldn’t be viewed as a replacement for a job, but it is an extra incentive and can help with incurred costs. Most of our code is open source, and we’ll ask that of your code as well when awarding the bounty. There are some exceptions which we can discuss on an individual basis. Bounties are awarded to the first person to complete a task. Feel free to comment on a bounty ticket to let others know you’re working on something, so others can hold off or contact you to team up. But be aware that others are still free to work on it also and try to beat you to it. However, we do not award bounties for sloppy work. If two high quality submissions are made close to each other (within a day or two), we might choose to award bounties to both, at our discretion, for example 100%% for the fist submission and 50%% for the second submission (so 150%% total). For the larger bounties (especially scraping bounties), please contact us when you’ve completed ~5%% of it, and you’re confident that your method will scale to the full milestone. You will have to share your method with us so we can give feedback. Also, this way we can decide what to do if there are multiple people getting close to a bounty, such as potentially awarding it to multiple people, encouraging people to team up, etc. WARNING: the high-bounty tasks are <span %(bold)s>difficult</span> — it might be wise to start with easier ones. Go to our <a %(a_gitlab)s>Gitlab issues list</a> and sort by “Label priority”. This shows roughly the order of tasks we care about. Tasks without explicit bounties are still eligible for membership, especially those marked “Accepted” and “Anna’s favorite”. You might want to start with a “Starter project”. Light volunteering We now also have a synced Matrix channel at %(matrix)s. If you have a few hours to spare, you can help out in a number of ways. Be sure to join the <a %(a_telegram)s>volunteers chat on Telegram</a>. As a token of appreciation, we typically give out 6 months of “Lucky Librarian” for basic milestones, and more for continued volunteering work. All milestones require high quality work — sloppy work hurts us more than it helps and we’ll reject it. Please <a %(a_contact)s>email us</a> when you reach a milestone. %(links)s links or screenshots of requests you fulfilled. Fulfilling book (or paper, etc) requests on the Z-Library or the Library Genesis forums. We don’t have our own book request system, but we mirror those libraries, so making them better makes Anna’s Archive better too. Milestone Task Depends on the task. Small tasks posted on our <a %(a_telegram)s>volunteers chat on Telegram</a>. Usually for membership, sometimes for small bounties. Small tasks posted in our volunteer chat group. Make sure to leave a comment on issues you fix, so others won’t duplicate your work. %(links)s links of records you improved. You can use the <a %(a_list)s >list of random metadata issues</a> as a starting point. Improve metadata by <a %(a_metadata)s>linking</a> with Open Library. These should show you letting someone know about Anna’s Archive, and them thanking you. %(links)s links or screenshots. Spreading the word of Anna’s Archive. For example, by recommending books on AA, linking to our blog posts, or generally directing people to our website. Fully translate a language (if it wasn’t close to completion already.) <a %(a_translate)s>Translating</a> the website. Link to edit history showing you made significant contributions. Improve the Wikipedia page for Anna’s Archive in your language. Include information from AA’s Wikipedia page in other languages, and from our website and blog. Add references to AA on other relevant pages. Volunteering & Bounties 