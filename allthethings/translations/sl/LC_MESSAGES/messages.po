msgid "layout.index.invalid_request"
msgstr "Neveljav<PERSON> zahteva. Obišči %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

msgid "layout.index.header.tagline_duxiu"
msgstr "Du<PERSON>iu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " in "

msgid "layout.index.header.tagline_and_more"
msgstr "in več"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp; Preslikuje<PERSON> %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Preslikujemo in odpiramo %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Vsa programska koda in podatki so odprtokodni."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Največja odprta knjižnica v zgodovini človeštva."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;knjig, %(paper_count)s&nbsp;člankov — ohranjenih za vedno."

msgid "layout.index.header.tagline"
msgstr "📚 Največja odprtokodna knjižnica na svetu. ⭐️ Preslikava strani Sci-Hub, Library Genesis, Z-Library in drugih. 📈 %(book_any)s knjig, %(journal_article)s člankov, %(book_comic)s stripov, %(magazine)s revij — ohranjeno za vedno."

msgid "layout.index.header.tagline_short"
msgstr "📚 Največja svetovna odprtokodna in odprtopodatkovna knjižnica.<br>⭐️ Preslikava Scihub, Libgen, Zlib, in druge."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Nepravilni meta podatki (npr. naslov, opis, grafika naslovnice)"

msgid "common.md5_report_type_mapping.download"
msgstr "Težave s prenosom (npr. ni povezave, sporočilo o napaki, zelo počasno delovanje)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Datoteke ni mogoče odpreti (npr. okvarjena datoteka, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Slaba kakovost (npr. težave z oblikovanjem, slaba kakovost skena, manjkajoče strani)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Neželjeno / datoteka bi morala biti odstranjena (npr. oglaševanje, neprimerna vsebina)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Zahtevek glede avtorskih pravic"

msgid "common.md5_report_type_mapping.other"
msgstr "Drugo"

msgid "common.membership.tier_name.bonus"
msgstr "Bonus prenosi"

msgid "common.membership.tier_name.2"
msgstr "Sijajni knjižni molj"

msgid "common.membership.tier_name.3"
msgstr "Srečni knjižničar"

msgid "common.membership.tier_name.4"
msgstr "Bleščeči hranilec podatkov"

msgid "common.membership.tier_name.5"
msgstr "Čudovit arhivar"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s skupaj"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) skupaj"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "neplačljivo"

msgid "common.donation.order_processing_status_labels.1"
msgstr "plačljivo"

msgid "common.donation.order_processing_status_labels.2"
msgstr "preklicano"

msgid "common.donation.order_processing_status_labels.3"
msgstr "potečeno"

msgid "common.donation.order_processing_status_labels.4"
msgstr "čakam na Annino potrditev"

msgid "common.donation.order_processing_status_labels.5"
msgstr "neveljaven"

msgid "page.donate.title"
msgstr "Doniraj"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "V teku je že <a %(a_donation)s>obstoječa donacija</a>. Prosimo, dokončajte ali prekličite to donacijo, preden začnete z novo donacijo."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ogled vseh mojih donacij</a>"

msgid "page.donate.header.text1"
msgstr "Annin arhiv je neprofitni, odprtokodni projekt z javno dostopnimi podatki. Z donacijo in članstvom podpirate naše delovanje in razvoj. Vsem našim članom: hvala, ker nas podpirate! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Za več informacij si oglejte <a %(a_donate)s>Pogosta vprašanja o donacijah</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Za še več prenosov, <a %(a_refer)s>priporočite prijatelje</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Dobili ste %(percentage)s%% bonusa hitrih prenosov, ker vas je priporočil uporabnik %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "To velja za celotno obdobje članstva."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s hitrih prenosov na dan"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "če boste donirali ta mesec!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mesec"

msgid "page.donate.buttons.join"
msgstr "Pridruži se"

msgid "page.donate.buttons.selected"
msgstr "Izbrano"

msgid "page.donate.buttons.up_to_discounts"
msgstr "do %(percentage)s%% popustov"

msgid "page.donate.perks.scidb"
msgstr "<strong>Neomejeni</strong> dokumenti SciDB brez preverjanja"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> dostop"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Zaslužite <strong>%(percentage)s%% bonus prenosov</strong> z <a %(a_refer)s>povabilom prijateljev</a>."

msgid "page.donate.perks.credits"
msgstr "Vaše uporabniško ime ali anonimna omemba v zaslugah"

msgid "page.donate.perks.previous_plus"
msgstr "Prejšnje ugodnosti, plus:"

msgid "page.donate.perks.early_access"
msgstr "Zgodnji dostop do novih funkcij"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Ekskluzivni Telegram z informacijami iz zakulisja"

msgid "page.donate.perks.adopt"
msgstr "»Posvojite torrent«: vaše uporabniško ime ali sporočilo v imenu datoteke torrent <div %(div_months)s>enkrat na vsakih 12 mesecev članstva</div>"

msgid "page.donate.perks.legendary"
msgstr "Legendarni status pri ohranjanju znanja in kulture človeštva"

msgid "page.donate.expert.title"
msgstr "Strokovni dostop"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontaktirajte nas"

msgid "page.donate.small_team"
msgstr "Smo majhna ekipa prostovoljcev. Morda bomo potrebovali 1-2 tedna, da odgovorimo."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Neomejen</strong> hitri dostop"

msgid "page.donate.expert.direct_sftp"
msgstr "Neposredni <strong>SFTP</strong> strežniki"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donacija na ravni podjetja ali zamenjava za nove zbirke (npr. novi skeni, OCR-ani nabori podatkov)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Pozdravljamo velike donacije premožnih posameznikov ali ustanov. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Za donacije nad 5000 USD nas kontaktirajte neposredno na %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Bodite pozorni, da so članstva na tej strani »na mesec«, vendar gre za enkratne donacije (ne ponavljajoče se). Oglejte si <a %(faq)s>pogosta vprašanja o donacijah</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Če želite prispevati (poljuben znesek) brez članstva, uporabite ta Monero (XMR) naslov: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Prosim izberite način plačila."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(začasno nedosegljivo)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s darilna kartica"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bančna kartica (z uporabo aplikacije)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kreditna/debetna kartica"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (ZDA) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (redni)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kartica / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kreditna/debetna/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazilija)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bančna kartica"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kreditna/debetna kartica (rezerva)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kreditna/debetna kartica 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "S kriptovalutami lahko donirate z uporabo BTC, ETH, XMR in SOL. Uporabite to možnost, če ste že seznanjeni s kriptovalutami."

msgid "page.donate.payment.desc.crypto2"
msgstr "S kriptovalutami lahko donirate z uporabo valut BTC, ETH, XMR in drugimi."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Če prvič uporabljate kriptovalute, vam priporočamo uporabo %(options)s za nakup in donacijo Bitcoina (prvotne in najbolj uporabljene kriptovalute)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Za doniranje s PayPal US bomo uporabili PayPal Crypto, ki nam omogoča, da ostanemo anonimni. Cenimo, da ste si vzeli čas in se naučili darovati s to metodo, saj nam zelo pomaga."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Donirajte z uporabo PayPala."

msgid "page.donate.payment.desc.cashapp"
msgstr "Donirajte z aplikacijo Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Če imate aplikacijo Cash App, je to najlažji način za doniranje!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Upoštevajte, da lahko aplikacija Cash App za transakcije pod %(amount)s zaračuna provizijo %(fee)s. Za %(amount)s ali več je brezplačno!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donirajte z uporabo Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Če imate Revolut, je to najlažji način za donacijo!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Donirajte s kreditno ali debetno kartico."

msgid "page.donate.payment.desc.google_apple"
msgstr "Morda bosta delovala tudi Google Pay in Apple Pay."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Upoštevajte, da lahko provizija kreditne kartice za majhne donacije izniči naš %(discount)s%% popust, zato priporočamo naročnino za daljši čas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Upoštevajte, da so provizije za majhne donacije visoke, zato priporočamo daljše naročnine."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Z Binance lahko kupite Bitcoin s kreditno/debetno kartico ali bančnim računom in nato donirate ta Bitcoin nam. Na ta način lahko ostanemo varni in anonimni pri sprejemanju vaše donacije."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance je na voljo v skoraj vsaki državi in podpira večino bank ter kreditnih/debetnih kartic. To je trenutno naša glavna priporočena metoda. Cenimo, da si vzamete čas za učenje, kako donirati s to metodo, saj nam to zelo pomaga."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donirajte z uporabo vašega rednega PayPal računa."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donirajte s kreditno/debetno kartico, PayPal ali Venmo. Na naslednji strani lahko izberete med temi možnostmi."

msgid "page.donate.payment.desc.amazon"
msgstr "Donirajte z darilno kartico Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Upoštevajte, da moramo zaokrožiti na zneske, ki jih sprejemajo naši prodajalci (najmanj %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>POMEMBNO:</strong> Podpiramo samo Amazon.com, ne pa drugih spletnih mest Amazon. Na primer, .de, .co.uk, .ca NISO podprti."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>POMEMBNO:</strong> Ta možnost je za %(amazon)s. Če želite uporabiti drugo spletno stran Amazon, jo izberite zgoraj."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Ta metoda uporablja ponudnika kriptovalut kot vmesno pretvorbo. To je lahko nekoliko zmedeno, zato uporabite to metodo le, če druge plačilne metode ne delujejo. Prav tako ne deluje v vseh državah."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donirajte s kreditno/debetno kartico prek aplikacije Alipay (zelo enostavno za nastavitev)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Namestite aplikacijo Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Namestite aplikacijo Alipay iz <a %(a_app_store)s>Apple App Store</a> ali <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrirajte se s svojo telefonsko številko."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nadaljnji osebni podatki niso potrebni."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Dodajte bančno kartico"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Podprte: Visa, MasterCard, JCB, Diners Club in Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Za več informacij si oglejte <a %(a_alipay)s>ta vodnik</a>."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Ne moremo neposredno podpirati kreditnih/debetnih kartic, ker banke nočejo sodelovati z nami. ☹ Vendar pa obstaja več načinov za uporabo kreditnih/debetnih kartic preko drugih plačilnih metod:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Darilna kartica Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Pošljite nam darilne kartice Amazon.com z uporabo vaše kreditne/debetne kartice."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay podpira mednarodne kreditne/debetne kartice. Za več informacij si oglejte <a %(a_alipay)s>ta vodnik</a>."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) podpira mednarodne kreditne/debetne kartice. V aplikaciji WeChat pojdite na “Me => Services => Wallet => Add a Card”. Če tega ne vidite, omogočite z “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Lahko kupite kripto z uporabo kreditnih/debetnih kartic."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Izrazite kripto storitve"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Izrazite storitve so priročne, vendar zaračunavajo višje provizije."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "To lahko uporabite namesto kripto borze, če želite hitro opraviti večjo donacijo in vam ni mar za provizijo v višini 5-10 $."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Bodite prepričani, da pošljete točen znesek kriptovalute, prikazan na strani za donacije, ne zneska v $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "V nasprotnem primeru bo provizija odšteta in vašega članstva ne bomo mogli samodejno obdelati."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s odvisno od države, brez preverjanja za prvo transakcijo)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, brez preverjanja za prvo transakcijo)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, brez preverjanja za prvo transakcijo)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Če so te informacije zastarele, nam prosim pošljite e-pošto, da nas obvestite."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Za kreditne kartice, debetne kartice, Apple Pay in Google Pay uporabljamo “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). V njihovem sistemu je ena “kava” enaka 5 $, zato bo vaša donacija zaokrožena na najbližji večkratnik 5."

msgid "page.donate.duration.intro"
msgstr "Izberite, za koliko časa se želite naročiti."

msgid "page.donate.duration.1_mo"
msgstr "1 mesec"

msgid "page.donate.duration.3_mo"
msgstr "3 mesece"

msgid "page.donate.duration.6_mo"
msgstr "6 mesecev"

msgid "page.donate.duration.12_mo"
msgstr "12 mesecev"

msgid "page.donate.duration.24_mo"
msgstr "24 mesecev"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 mesecev"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 mesecev"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>po <span %(span_discount)s></span> popustih</div><div %(div_total)s></div> <div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Ta način plačila zahteva najmanj %(amount)s. Izberite drugo obdobje trajanja ali način plačila."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Doniraj"

msgid "page.donate.payment.maximum_method"
msgstr "Ta način plačila omogoča le največ %(amount)s. Izberite drugo obdobje trajanja ali način plačila."

msgid "page.donate.login2"
msgstr "Če želite postati član, se <a %(a_login)s>prijavite ali registrirajte</a>. Hvala za podporo!"

msgid "page.donate.payment.crypto_select"
msgstr "Izberite želeni kripto kovanec:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(najnižji minimalni znesek)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(uporabite pri pošiljanju Ethereuma iz Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(opozorilo: visok minimalni znesek)"

msgid "page.donate.submit.confirm"
msgstr "Kliknite gumb doniraj, da potrdite to donacijo."

msgid "page.donate.submit.button"
msgstr "Donirajte <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Donacijo lahko še vedno prekličete postopkom."

msgid "page.donate.submit.success"
msgstr "✅ Preusmeritev na stran za donacije…"

msgid "page.donate.submit.failure"
msgstr "❌ Nekaj je šlo narobe. Prosimo ponovno naložite stran in poskusite znova."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s/mesec"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "za 1 mesec"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "za 3 mesece"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "za 6 mesecev"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "za 12 mesecev"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "za 24 mesecev"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "za 48 mesecev"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "za 96 mesecev"

msgid "page.donate.submit.button.label.1_mo"
msgstr "za 1 mesec »%(tier_name)s«"

msgid "page.donate.submit.button.label.3_mo"
msgstr "za 3 mesece »%(tier_name)s«"

msgid "page.donate.submit.button.label.6_mo"
msgstr "za 6 mesecev »%(tier_name)s«"

msgid "page.donate.submit.button.label.12_mo"
msgstr "za 12 mesecev »%(tier_name)s«"

msgid "page.donate.submit.button.label.24_mo"
msgstr "za 24 mesecev »%(tier_name)s«"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "za 48 mesecev “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "za 96 mesecev “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donacija"

msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Skupaj: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mesec za %(duration)s mesecev, vključno z %(discounts)s%% popustom)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Skupaj: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mesec za %(duration)s mesecev)</span>"

msgid "page.donation.header.status"
msgstr "Stanje: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identifikator: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Prekliči"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Ali ste prepričani, da želite preklicati? Ne prekličite, če ste že plačali."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Da, prosim prekliči"

msgid "page.donation.header.cancel.success"
msgstr "✅ Vaša donacija je bila preklicana."

msgid "page.donation.header.cancel.new_donation"
msgstr "Naredi novo donacijo"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova."

msgid "page.donation.header.reorder"
msgstr "Preuredi vrstni red"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Plačilo je že bilo izvedeno. Če vseeno želite pregledati plačilna navodila, kliknite tukaj:"

msgid "page.donation.old_instructions.show_button"
msgstr "Prikaži stara plačilna navodila"

msgid "page.donation.thank_you_donation"
msgstr "Hvala za vašo donacijo!"

msgid "page.donation.thank_you.secret_key"
msgstr "Če še niste, si zapišite svoj skrivni ključ za prijavo:"

msgid "page.donation.thank_you.locked_out"
msgstr "V nasprotnem primeru lahko izgubite dostop do tega računa!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Navodila za plačilo so zdaj zastarela. Če želite prispevati še eno donacijo, uporabite zgornji gumb »Preuredi vrstni red."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Pomembna opomba:</strong> Cene kriptovalut lahko močno nihajo, včasih tudi do 20 %% v nekaj minutah. To je še vedno manj od provizij, ki jih imamo pri številnih ponudnikih plačil, ki pogosto zaračunavajo 50–60 %% za sodelovanje z »dobrodelno organizacijo v senci«, kot je mi. <u>Če nam pošljete potrdilo s prvotno ceno, ki ste jo plačali, bomo vaš račun spremenili v izbrano članstvo</u> (če potrdilo ni starejše od nekaj ur). Resnično cenimo, da ste pripravljeni prenašati takšne stvari, da bi nas podprli! ❤️"

msgid "page.donation.expired"
msgstr "Ta donacija je potekla. Prekličite in ustvarite novo."

msgid "page.donation.payment.crypto.top_header"
msgstr "Kripto navodila"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Prenos na enega od naših kripto računov"

msgid "page.donation.payment.crypto.text1"
msgstr "Donirajte skupni znesek %(total)s na enega od teh naslovov:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Kupite Bitcoin na Paypalu"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Poiščite stran »Kripto« v svoji aplikaciji ali spletnem mestu PayPal. To se običajno nahaja pod opcijo »Finance«."

msgid "page.donation.payment.paypal.text3"
msgstr "Sledite navodilom za nakup Bitcoin (BTC). Kupiti morate samo znesek, ki ga želite donirati, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Prenesite Bitcoin na naš naslov"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Pojdite na stran »Bitcoin« v aplikaciji ali spletnem mestu PayPal. Pritisnite gumb »Prenos« %(transfer_icon)s in nato »Pošlji«."

msgid "page.donation.payment.paypal.text5"
msgstr "Vnesite naš naslov Bitcoin (BTC) kot prejemnika in sledite navodilom za pošiljanje donacije %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Navodila za kreditno/debetno kartico"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Donirajte prek naše strani za donacije s kreditno/debetno kartico"

msgid "page.donation.donate_on_this_page"
msgstr "Donirajte %(amount)s na <a %(a_page)s>tej strani</a>."

msgid "page.donation.stepbystep_below"
msgstr "Oglejte si spodnji vodnik po korakih."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Čakanje na potrditev (za preveritev osvežite stran)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Čakanje na prenos (za preveritev osvežite stran)…"

msgid "page.donation.time_left_header"
msgstr "Preostali čas:"

msgid "page.donation.might_want_to_cancel"
msgstr "(morda boste želeli preklicati in ustvariti novo donacijo)"

msgid "page.donation.reset_timer"
msgstr "Če želite ponastaviti časovnik, preprosto ustvarite novo donacijo."

msgid "page.donation.refresh_status"
msgstr "Posodobi status"

msgid "page.donation.footer.issues_contact"
msgstr "Če naletite na kakršne koli težave, nas kontaktirajte na %(email)s in vključite čim več informacij (na primer posnetke zaslona)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Če ste že plačali:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Včasih lahko potrditev traja do 24 ur, zato poskrbite, da osvežite to stran (tudi če je potekla)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Kupite kovanec PYUSD s PayPal-om"

msgid "page.donation.pyusd.instructions"
msgstr "Sledite navodilom za nakup kovanca PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Kupite malo več (priporočamo %(more)s več) od zneska, ki ga donirate (%(amount)s), da pokrijete stroške transakcije. Vse, kar ostane, boste obdržali."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Pojdite na stran »PYUSD« v aplikaciji ali spletnem mestu PayPal. Pritisnite gumb \"Prenos\" %(icon)s in nato \"Pošlji\"."

msgid "page.donation.transfer_amount_to"
msgstr "Prenesite %(amount)s na %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Kupite Bitcoin (BTC) na aplikaciji Cash"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Pojdite na stran »Bitcoin« (BTC) v aplikaciji Cash."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Kupite malo več (priporočamo %(more)s več) kot znesek, ki ga donirate (%(amount)s), da pokrijete transakcijske stroške. Kar ostane, obdržite."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Prenesite Bitcoin na naš naslov"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Kliknite gumb »Pošlji bitcoin«, da izvedete »dvig«. Preklopite iz dolarjev v BTC s pritiskom na ikono %(icon)s. Vnesite znesek BTC spodaj in kliknite »Pošlji«. Če se zataknete, si oglejte <a %(help_video)s>ta video</a>."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Za majhne donacije (pod $25) boste morda morali uporabiti Rush ali Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Kupite Bitcoin (BTC) na Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Pojdite na stran »Crypto« v Revolut, da kupite Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Kupite malo več (priporočamo %(more)s več) kot znesek, ki ga donirate (%(amount)s), da pokrijete transakcijske stroške. Kar ostane, obdržite."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Prenesite Bitcoin na naš naslov"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Kliknite gumb »Pošlji bitcoin«, da izvedete »dvig«. Preklopite iz evrov v BTC s pritiskom na ikono %(icon)s. Vnesite znesek BTC spodaj in kliknite »Pošlji«. Če se zataknete, si oglejte <a %(help_video)s>ta video</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Bodite prepričani, da uporabite spodnji znesek v BTC, <em>NE</em> v evrih ali dolarjih, sicer ne bomo prejeli pravilnega zneska in ne bomo mogli samodejno potrditi vašega članstva."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Za majhne donacije (pod $25) boste morda morali uporabiti Rush ali Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Uporabite katero koli od naslednjih storitev »kreditna kartica za Bitcoin«, ki trajajo le nekaj minut:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Izpolnite naslednje podatke v obrazcu:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin znesek:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Prosimo, uporabite ta <span %(underline)s>točen znesek</span>. Vaši skupni stroški so lahko višji zaradi provizij kreditne kartice. Pri majhnih zneskih je to lahko več kot naš popust, žal."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin naslov (zunanji denarnik):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s navodila"

msgid "page.donation.crypto_standard"
msgstr "Podpiramo samo standardno različico kripto kovancev, brez eksotičnih omrežij ali različic kovancev. Potrditev transakcije lahko traja do ene ure, odvisno od kovanca."

msgid "page.donation.crypto_qr_code_title"
msgstr "Skeniraj QR kodo za plačilo"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Skeniraj to QR kodo s svojo aplikacijo za kripto denarnico, da hitro izpolniš podatke za plačilo"

msgid "page.donation.amazon.header"
msgstr "Amazonova darilna kartica"

msgid "page.donation.amazon.form_instructions"
msgstr "Uporabite <a %(a_form)s>uradni obrazec Amazon.com</a>, da nam pošljete darilno kartico v vrednosti %(amount)s na spodnji e-poštni naslov."

msgid "page.donation.amazon.only_official"
msgstr "Ne moremo sprejeti drugih načinov darilnih kartic, <strong>samo poslanih neposredno iz uradnega obrazca na Amazon.com</strong>. Vaše darilne kartice ne moremo vrniti, če ne uporabite tega obrazca."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Vnesite točen znesek: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Prosim, NE napišite svojega sporočila."

msgid "page.donation.amazon.form_to"
msgstr "E-poštno sporočilo prejemnika »Za« v obliki:"

msgid "page.donation.amazon.unique"
msgstr "Edinstveno za vaš račun, ne delite."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Uporabite samo enkrat."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Čakanje na darilno kartico ... (za preverjanje osvežite stran)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Po pošiljanju vaše darilne kartice jo bo naš avtomatiziran sistem potrdil v nekaj minutah. Če to ne deluje, poskusite znova poslati darilno kartico (<a %(a_instr)s>navodila</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Če to še vedno ne deluje, nam pošljite e-pošto in Anna ga bo ročno pregledala (to lahko traja nekaj dni) in ne pozabite omeniti, da ste že poskusili znova poslati."

msgid "page.donation.amazon.example"
msgstr "Primer:"

msgid "page.donate.strange_account"
msgstr "Ime računa ali slika sta lahko videti nenavadna. Brez skrbi! Te račune upravljajo naši donatorski partnerji. Naši računi niso bili vdrti."

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay navodila"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donirajte na Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donirajte skupni znesek %(total)s z uporabo <a %(a_account)s>tega Alipay računa</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Če je stran za donacije blokirana, poskusite z drugo internetno povezavo (npr. VPN ali internet na telefonu)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Na žalost je stran Alipay pogosto dostopna samo iz <strong>celinske Kitajske</strong>. Morda boste morali začasno onemogočiti svoj VPN ali uporabiti VPN za celinsko Kitajsko (včasih deluje tudi Hong Kong)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Opravite donacijo (skenirajte QR kodo ali pritisnite gumb)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Odprite <a %(a_href)s>stran za donacije s QR kodo</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Skenirajte QR kodo z aplikacijo Alipay ali pritisnite gumb za odpiranje aplikacije Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Prosimo, bodite potrpežljivi; stran se lahko nalaga dlje časa, saj je na Kitajskem."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Navodila za WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donirajte na WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donirajte skupni znesek %(total)s z uporabo <a %(a_account)s>tega WeChat računa</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix navodila"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donirajte za Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Donirajte skupni znesek %(total)s z <a %(a_account)s>tem računom Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Pošljite nam potrdilo po e-pošti"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Pošljite potrdilo ali posnetek zaslona na vaš osebni naslov za preverjanje. NE uporabljajte tega e-poštnega naslova za vašo PayPal donacijo."

msgid "page.donation.footer.text1"
msgstr "Pošljite potrdilo ali posnetek zaslona na svoj osebni naslov za preverjanje:"

msgid "page.donation.footer.crypto_note"
msgstr "Če je tečaj kriptovalute med transakcijo nihal, obvezno priložite potrdilo, ki prikazuje prvotni menjalni tečaj. Resnično cenimo, da ste se potrudili pri uporabi kriptovalute, zelo nam pomaga!"

msgid "page.donation.footer.text2"
msgstr "Ko pošljete potrdilo po e-pošti, kliknite ta gumb, da ga bo Anna lahko ročno pregledala (to lahko traja nekaj dni):"

msgid "page.donation.footer.button"
msgstr "Da, potrdilo sem poslal po e-pošti"

msgid "page.donation.footer.success"
msgstr "✅ Hvala za vašo donacijo! Anna bo v nekaj dneh ročno aktivirala vaše članstvo."

msgid "page.donation.footer.failure"
msgstr "❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova."

msgid "page.donation.stepbystep"
msgstr "Vodnik po korakih"

msgid "page.donation.crypto_dont_worry"
msgstr "Nekateri koraki omenjajo kripto denarnice, vendar ne skrbite, za to se vam ni treba naučiti ničesar o kripto denarnicah."

msgid "page.donation.hoodpay.step1"
msgstr "1. Vnesite svoj e-poštni naslov."

msgid "page.donation.hoodpay.step2"
msgstr "2. Izberite način plačila."

msgid "page.donation.hoodpay.step3"
msgstr "3. Ponovno izberite način plačila."

msgid "page.donation.hoodpay.step4"
msgstr "4. Izberite »Self-hosted« denarnico."

msgid "page.donation.hoodpay.step5"
msgstr "5. Kliknite »Potrjujem lastništvo«."

msgid "page.donation.hoodpay.step6"
msgstr "6. Prejeti bi morali e-poštno potrdilo. Pošljite nam ga in vašo donacijo bomo potrdili v najkrajšem možnem času."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Prosimo, počakajte vsaj <span %(span_hours)s>24 ur</span> (in osvežite to stran), preden nas kontaktirate."

msgid "page.donate.mistake"
msgstr "Če ste pri plačilu naredili napako, ne moremo izvesti vračila, vendar jo bomo poskušali popraviti."

msgid "page.my_donations.title"
msgstr "Moje donacije"

msgid "page.my_donations.not_shown"
msgstr "Podrobnosti o donacijah niso prikazane javno."

msgid "page.my_donations.no_donations"
msgstr "Ni še nobenih donacij. <a %(a_donate)s>Izvedi prvo donacijo.</a>"

msgid "page.my_donations.make_another"
msgstr "Izvedi še eno donacijo."

msgid "page.downloaded.title"
msgstr "Prenesene datoteke"

msgid "page.downloaded.fast_partner_star"
msgstr "Prenosi s hitrih partnerskih strežnikov so označeni z %(icon)s."

msgid "page.downloaded.twice"
msgstr "Če ste datoteko prenesli hkrati s hitrim in počasnim prenosom, se bo prikazala dvakrat."

msgid "page.downloaded.fast_download_time"
msgstr "Hitri prenosi v zadnjih 24 urah se štejejo k dnevni omejitvi."

msgid "page.downloaded.times_utc"
msgstr "Vsi časi so v UTC."

msgid "page.downloaded.not_public"
msgstr "Prenesene datoteke niso javno prikazane."

msgid "page.downloaded.no_files"
msgstr "Nobena datoteka še ni bila prenesena."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Zadnjih 18 ur"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Prej"

msgid "page.account.logged_in.title"
msgstr "Račun"

msgid "page.account.logged_out.title"
msgstr "Prijavite se / Registrirajte se"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID računa: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Javni profil: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Skrivni ključ (ne delite!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "prikaži"

msgid "page.account.logged_in.membership_has_some"
msgstr "Članstvo: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(podaljšaj)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Članstvo: <strong>Brez</strong> <a %(a_become)s>(postanite član)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Uporabljeni hitri prenosi (zadnjih 24 ur): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "kateri prenosi?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Ekskluzivna Telegram skupina: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Pridruži se nam tukaj!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Nadgradite na <a %(a_tier)s>višjo raven</a> za pridružitev naši skupini."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontaktirajte Ano na %(email)s, če vas zanima nadgradnja članstva na višjo raven."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontaktni email"

msgid "page.account.logged_in.membership_multiple"
msgstr "Kombinirate lahko več članstev (hitri prenosi v roku 24 ur bodo sešteti)."

msgid "layout.index.header.nav.public_profile"
msgstr "Javni profil"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Prenesene datoteke"

msgid "layout.index.header.nav.my_donations"
msgstr "Moje donacije"

msgid "page.account.logged_in.logout.button"
msgstr "Odjava"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Zdaj ste odjavljeni. Ponovno naložite stran, da se znova prijavite."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova."

msgid "page.account.logged_out.registered.text1"
msgstr "Registracija uspešna! Vaš skrivni ključ je: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Ta ključ skrbno shranite. Če ga izgubite, boste izgubili dostop do svojega računa."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Zaznamek.</strong> To stran lahko dodate med zaznamke, da pridobite svoj ključ.</li><li %(li_item)s><strong>Prenos.</strong> Kliknite <a %(a_download)s>to povezavo</a> za prenos ključa.</li><li %(li_item)s><strong>Upravitelj gesel.</strong> Za vaše udobje je ključ vnaprej izpolnjen spodaj, tako da ga lahko ob prijavi shranite v upravitelja gesel.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Za prijavo vnesite svoj skrivni ključ:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Skrivni ključ"

msgid "page.account.logged_out.key_form.button"
msgstr "Vpiši se"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Neveljaven skrivni ključ. Preverite svoj ključ in poskusite znova ali pa spodaj registrirajte nov račun."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Ne izgubite ključa!"

msgid "page.account.logged_out.register.header"
msgstr "Še nimate računa?"

msgid "page.account.logged_out.register.button"
msgstr "Registrirajte nov račun"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Če ste izgubili ključ, prosimo <a %(a_contact)s>kontaktirajte nas</a> in navedite čim več informacij."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Morda boste morali začasno ustvariti nov račun, da nas kontaktirate."

msgid "page.account.logged_out.old_email.button"
msgstr "Stari e-poštni račun? Tukaj vnesite svoj <a %(a_open)s>e-poštni naslov</a>."

msgid "page.list.title"
msgstr "Seznam"

msgid "page.list.header.edit.link"
msgstr "uredi"

msgid "page.list.edit.button"
msgstr "Shrani"

msgid "page.list.edit.success"
msgstr "✅ Shranjeno. Ponovno naložite stran."

msgid "page.list.edit.failure"
msgstr "❌ Nekaj je šlo narobe. Prosim poskusite ponovno."

msgid "page.list.by_and_date"
msgstr "Seznam avtorja %(by)s, ustvarjen <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Seznam je prazen."

msgid "page.list.new_item"
msgstr "Dodajte ali odstranite s tega seznama tako, da poiščete datoteko in odprete zavihek »Seznami«."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil ni bil najden."

msgid "page.profile.header.edit"
msgstr "uredi"

msgid "page.profile.change_display_name.text"
msgstr "Spremenite svoje prikazno ime. Vašega identifikatorja (del za \"#\") ni mogoče spremeniti."

msgid "page.profile.change_display_name.button"
msgstr "Shrani"

msgid "page.profile.change_display_name.success"
msgstr "✅ Shranjeno. Ponovno naložite stran."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Nekaj je šlo narobe. Prosim poskusite ponovno."

msgid "page.profile.created_time"
msgstr "Profil ustvarjen <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Seznami"

msgid "page.profile.lists.no_lists"
msgstr "Seznamov še ni"

msgid "page.profile.lists.new_list"
msgstr "Ustvarite nov seznam tako, da poiščete datoteko in odprete zavihek »Seznami«."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reforma avtorskih prav je nujna za nacionalno varnost."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Na kratko: Kitajski LLM-ji (vključno z DeepSeek) so usposobljeni na mojem nezakonitem arhivu knjig in člankov — največjem na svetu. Zahod mora prenoviti zakonodajo o avtorskih pravicah zaradi nacionalne varnosti."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "spremljevalni članki TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Pred kratkim so \"sence knjižnic\" izumirale. Sci-Hub, masivni nezakoniti arhiv akademskih člankov, je prenehal sprejemati nova dela zaradi tožb. \"Z-Library\", največja nezakonita knjižnica knjig, je videla svoje domnevne ustvarjalce aretirane zaradi kaznivih dejanj povezanih z avtorskimi pravicami. Neverjetno so uspeli pobegniti aretaciji, vendar njihova knjižnica ni nič manj ogrožena."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Ko se je Z-Library soočila z zaprtjem, sem že varnostno kopiral celotno knjižnico in iskal platformo, kjer bi jo lahko gostil. To je bila moja motivacija za začetek Anninega Arhiva: nadaljevanje misije teh prejšnjih pobud. Od takrat smo zrasli v največjo senco knjižnico na svetu, ki gosti več kot 140 milijonov avtorsko zaščitenih besedil v številnih formatih — knjige, akademske članke, revije, časopise in še več."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Moja ekipa in jaz smo ideologi. Verjamemo, da je ohranjanje in gostovanje teh datotek moralno pravilno. Knjižnice po svetu se soočajo z zmanjšanjem financiranja, in ne moremo zaupati dediščine človeštva korporacijam."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Potem je prišla umetna inteligenca. Skoraj vsa večja podjetja, ki gradijo LLM-je, so nas kontaktirala za usposabljanje na naših podatkih. Večina (vendar ne vsa!) podjetij s sedežem v ZDA je premislila, ko so spoznala nezakonito naravo našega dela. Nasprotno pa so kitajska podjetja z navdušenjem sprejela našo zbirko, očitno neobremenjena z njeno zakonitostjo. To je opazno, glede na to, da je Kitajska podpisnica skoraj vseh večjih mednarodnih pogodb o avtorskih pravicah."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Omogočili smo hiter dostop približno 30 podjetjem. Večina jih je LLM podjetij, nekateri pa so posredniki podatkov, ki bodo našo zbirko preprodajali. Večina jih je kitajskih, vendar smo sodelovali tudi s podjetji iz ZDA, Evrope, Rusije, Južne Koreje in Japonske. DeepSeek je <a %(arxiv)s>priznal</a>, da je bila prejšnja različica usposobljena na delu naše zbirke, čeprav so glede najnovejšega modela zadržani (verjetno je tudi ta usposobljen na naših podatkih)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Če želi Zahod ostati v ospredju v tekmi LLM-jev in končno AGI, mora ponovno premisliti svoj položaj glede avtorskih pravic, in to kmalu. Ne glede na to, ali se strinjate z nami glede našega moralnega primera ali ne, to postaja primer ekonomije in celo nacionalne varnosti. Vse sile gradijo umetne super-znanstvenike, super-hekerje in super-vojske. Svoboda informacij postaja vprašanje preživetja za te države — celo vprašanje nacionalne varnosti."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Naša ekipa je z vsega sveta in nimamo posebne usmeritve. Vendar bi spodbudili države z močnimi zakoni o avtorskih pravicah, da uporabijo to eksistencialno grožnjo za njihovo reformo. Kaj torej storiti?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Naše prvo priporočilo je preprosto: skrajšajte obdobje avtorskih pravic. V ZDA so avtorske pravice podeljene za 70 let po avtorjevi smrti. To je absurdno. To lahko uskladimo s patenti, ki so podeljeni za 20 let po vložitvi. To bi moralo biti več kot dovolj časa, da avtorji knjig, člankov, glasbe, umetnosti in drugih ustvarjalnih del dobijo popolno nadomestilo za svoje napore (vključno z dolgoročnejšimi projekti, kot so filmske priredbe)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Nato bi morali oblikovalci politik vsaj vključiti izjeme za množično ohranjanje in razširjanje besedil. Če je izgubljeni prihodek od posameznih strank glavna skrb, bi lahko osebna distribucija ostala prepovedana. V zameno bi tisti, ki so sposobni upravljati obsežne zbirke — podjetja, ki usposabljajo LLM-je, skupaj s knjižnicami in drugimi arhivi — bili zajeti v teh izjemah."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Nekatere države že izvajajo različico tega. TorrentFreak je <a %(torrentfreak)s>poročal</a>, da sta Kitajska in Japonska uvedli izjeme za umetno inteligenco v svoje zakone o avtorskih pravicah. Ni nam jasno, kako to vpliva na mednarodne pogodbe, vendar zagotovo daje kritje njihovim domačim podjetjem, kar pojasnjuje, kar smo videli."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Kar se tiče Anninega Arhiva — nadaljevali bomo naše podzemno delo, ki temelji na moralnem prepričanju. Vendar je naša največja želja, da stopimo na svetlobo in legalno okrepimo naš vpliv. Prosimo, reformirajte avtorske pravice."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Preberite spremljevalne članke TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Zmagovalci nagrade za vizualizacijo ISBN v vrednosti 10.000 $"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Na kratko: Prejeli smo nekaj neverjetnih prispevkov za nagrado za vizualizacijo ISBN v vrednosti 10.000 $."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Pred nekaj meseci smo objavili <a %(all_isbns)s>nagrado v vrednosti 10.000 $</a> za najboljšo možno vizualizacijo naših podatkov, ki prikazuje prostor ISBN. Poudarili smo prikaz, katere datoteke smo že arhivirali in katere ne, ter kasneje dodali podatkovno zbirko, ki opisuje, koliko knjižnic ima ISBN-je (merilo redkosti)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Preplavljeni smo bili z odzivom. Bilo je toliko ustvarjalnosti. Velika zahvala vsem, ki ste sodelovali: vaša energija in navdušenje sta nalezljiva!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Na koncu smo želeli odgovoriti na naslednja vprašanja: <strong>kateri knjige obstajajo na svetu, koliko smo jih že arhivirali in na katere knjige bi se morali osredotočiti naslednje?</strong> Čudovito je videti, da toliko ljudi skrbi za ta vprašanja."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Sami smo začeli z osnovno vizualizacijo. V manj kot 300 kb ta slika jedrnato predstavlja največji popolnoma odprt \"seznam knjig\", ki je bil kdajkoli sestavljen v zgodovini človeštva:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Vsi ISBN-ji"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Datoteke v Anninem arhivu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO-ji"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC uhajanje podatkov"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID-ji"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhostov eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Knjige"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internetni arhiv"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Globalni register založnikov"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Ruska državna knjižnica"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperialna knjižnica Trantorja"

#, fuzzy
msgid "common.back"
msgstr "Nazaj"

#, fuzzy
msgid "common.forward"
msgstr "Naprej"

#, fuzzy
msgid "common.last"
msgstr "Zadnji"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Za več informacij si oglejte <a %(all_isbns)s>izvirno objavo na blogu</a>."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Izdali smo izziv za izboljšanje tega. Za prvo mesto bi podelili nagrado v višini 6.000 $, za drugo mesto 3.000 $ in za tretje mesto 1.000 $. Zaradi izjemnega odziva in neverjetnih prispevkov smo se odločili, da nekoliko povečamo nagradni sklad in podelimo štiri tretja mesta po 500 $ vsakemu. Zmagovalci so spodaj, vendar si oglejte vse prispevke <a %(annas_archive)s>tukaj</a> ali prenesite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Prvo mesto 6.000 $: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Ta <a %(phiresky_github)s>prispevek</a> (<a %(annas_archive_note_2951)s>komentar na Gitlabu</a>) je preprosto vse, kar smo želeli, in še več! Še posebej so nam bile všeč izjemno prilagodljive možnosti vizualizacije (celo podpora za prilagojene shaderje), vendar s celovitim seznamom prednastavitev. Prav tako nam je bilo všeč, kako hitro in gladko vse deluje, preprosta izvedba (ki sploh nima zaledja), pametna minimapa in obsežna razlaga v njihovem <a %(phiresky_github)s>blogu</a>. Neverjetno delo in zaslužena zmaga!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Drugo mesto 3.000 $: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Še en neverjeten <a %(annas_archive_note_2913)s>prispevek</a>. Ni tako prilagodljiv kot prvo mesto, vendar smo dejansko raje imeli njegovo makro raven vizualizacije pred prvim mestom (krivulja zapolnjevanja prostora, meje, označevanje, poudarjanje, premikanje in povečava). <a %(annas_archive_note_2971)s>Komentar</a> Joeja Davisa je z nami odmeval:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "\"Medtem ko so popolni kvadrati in pravokotniki matematično prijetni, ne zagotavljajo boljše lokalnosti v kontekstu kartiranja. Verjamem, da asimetrija, ki je lastna tem Hilbertovim ali klasičnim Mortonovim krivuljam, ni pomanjkljivost, temveč lastnost. Tako kot je Italija s svojo znamenito obliko škornja takoj prepoznavna na zemljevidu, lahko edinstvene 'posebnosti' teh krivulj služijo kot kognitivne znamenitosti. Ta posebnost lahko izboljša prostorski spomin in pomaga uporabnikom pri orientaciji, kar lahko olajša iskanje določenih regij ali opazovanje vzorcev.\""

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "In še vedno veliko možnosti za vizualizacijo in upodabljanje ter neverjetno gladek in intuitiven uporabniški vmesnik. Trdno drugo mesto!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tretje mesto 500 $ #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "V tem <a %(annas_archive_note_2940)s>prispevku</a> so nam bile res všeč različne vrste pogledov, zlasti primerjalni in založniški pogledi."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tretje mesto 500 $ #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Čeprav uporabniški vmesnik ni najbolj izpopolnjen, ta <a %(annas_archive_note_2917)s>prispevek</a> izpolnjuje veliko zahtev. Še posebej nam je bila všeč njegova primerjalna funkcija."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tretje mesto 500 $ #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Tako kot prvo mesto nas je ta <a %(annas_archive_note_2975)s>prispevek</a> navdušil s svojo prilagodljivostjo. Na koncu je to tisto, kar naredi odlično orodje za vizualizacijo: maksimalna prilagodljivost za napredne uporabnike, hkrati pa ohranja stvari preproste za povprečne uporabnike."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tretje mesto 500 $ #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Zadnji <a %(annas_archive_note_2947)s>prispevek</a>, ki prejme nagrado, je precej osnovni, vendar ima nekaj edinstvenih funkcij, ki so nam bile res všeč. Všeč nam je bilo, kako prikazujejo, koliko Datasets pokriva določen ISBN kot merilo priljubljenosti/zanesljivosti. Prav tako nam je bila zelo všeč preprostost, a učinkovitost uporabe drsnika za prosojnost pri primerjavah."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Opazne ideje"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Nekatere dodatne ideje in izvedbe, ki so nam bile še posebej všeč:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Nebotičniki za redkost"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Žive statistike"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Opombe in tudi žive statistike"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Edinstven pogled na zemljevid in filtri"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Kul privzeta barvna shema in toplotna karta."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Enostavno preklapljanje med datasets za hitre primerjave."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Lepi napisi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Merilna vrstica s številom knjig."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Veliko drsnikov za primerjavo datasets, kot da ste DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Lahko bi nadaljevali še nekaj časa, vendar se tukaj ustavimo. Bodite prepričani, da si ogledate vse prispevke <a %(annas_archive)s>tukaj</a>, ali prenesite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>. Toliko prispevkov, in vsak prinaša edinstveno perspektivo, bodisi v uporabniškem vmesniku ali izvedbi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Vsaj prvo mesto bomo vključili v našo glavno spletno stran, morda pa tudi nekatere druge. Prav tako smo začeli razmišljati o tem, kako organizirati proces prepoznavanja, potrjevanja in nato arhiviranja najredkejših knjig. Več sledi na tem področju."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Hvala vsem, ki ste sodelovali. Neverjetno je, da toliko ljudi skrbi."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Naša srca so polna hvaležnosti."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Vizualizacija vseh ISBN-jev — nagrada 10.000 $ do 31. 1. 2025"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Ta slika predstavlja največji popolnoma odprt \"seznam knjig\", ki je bil kdajkoli sestavljen v zgodovini človeštva."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Ta slika je 1000×800 pikslov. Vsak piksel predstavlja 2.500 ISBN-jev. Če imamo datoteko za ISBN, naredimo ta piksel bolj zelen. Če vemo, da je bil ISBN izdan, vendar nimamo ustrezne datoteke, ga naredimo bolj rdečega."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "V manj kot 300 kb ta slika jedrnato predstavlja največji popolnoma odprt \"seznam knjig\", ki je bil kdajkoli sestavljen v zgodovini človeštva (nekaj sto GB stisnjenih v celoti)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Prav tako prikazuje: še veliko dela je treba opraviti pri varnostnem kopiranju knjig (imamo le 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Ozadje"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Kako lahko Arhiv Ane doseže svoj cilj varnostnega kopiranja vsega človeškega znanja, ne da bi vedel, katere knjige so še tam zunaj? Potrebujemo seznam NALOG. Eden od načinov za to je s pomočjo ISBN številk, ki so bile od 70-ih let prejšnjega stoletja dodeljene vsaki objavljeni knjigi (v večini držav)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Ne obstaja osrednja avtoriteta, ki bi poznala vse dodelitve ISBN. Namesto tega gre za porazdeljen sistem, kjer države dobijo obsege številk, ki jih nato dodelijo večjim založnikom, ti pa lahko nadalje razdelijo obsege manjšim založnikom. Na koncu so posamezne številke dodeljene knjigam."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Začeli smo z mapiranjem ISBN <a %(blog)s>pred dvema letoma</a> z našim strganjem ISBNdb. Od takrat smo strgali še veliko več virov metapodatkov, kot so <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby in drugi. Celoten seznam je na voljo na straneh »Datasets« in »Torrents« na Arhivu Ane. Zdaj imamo daleč največjo popolnoma odprto, enostavno prenosljivo zbirko metapodatkov knjig (in s tem ISBN) na svetu."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "<a %(blog)s>Obširno smo pisali</a> o tem, zakaj nam je mar za ohranjanje, in zakaj smo trenutno v kritičnem obdobju. Zdaj moramo identificirati redke, premalo osredotočene in edinstveno ogrožene knjige ter jih ohraniti. Dobri metapodatki o vseh knjigah na svetu pri tem pomagajo."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Vizualizacija"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Poleg pregledne slike si lahko ogledamo tudi posamezne pridobljene datasets. Uporabite spustni meni in gumbe za preklapljanje med njimi."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "V teh slikah je veliko zanimivih vzorcev. Zakaj je neka pravilnost linij in blokov, ki se zdi, da se pojavlja na različnih lestvicah? Kaj so prazna območja? Zakaj so določeni datasets tako zgoščeni? Ta vprašanja bomo pustili kot nalogo za bralca."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Nagrada 10.000 $"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Tukaj je veliko za raziskati, zato razpisujemo nagrado za izboljšanje zgornje vizualizacije. Za razliko od večine naših nagrad je ta časovno omejena. Svojo odprtokodno kodo morate <a %(annas_archive)s>oddati</a> do 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Najboljša oddaja bo prejela 6.000 $, drugo mesto 3.000 $, tretje mesto pa 1.000 $. Vse nagrade bodo podeljene v Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Spodaj so minimalna merila. Če nobena oddaja ne izpolnjuje meril, lahko še vedno podelimo nekaj nagrad, vendar bo to po naši presoji."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Razvejite ta repozitorij in uredite to HTML objavo na blogu (drugega zaledja poleg našega Flask zaledja ni dovoljeno)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Naredite zgornjo sliko gladko povečljivo, tako da lahko povečate vse do posameznih ISBN. Klik na ISBN naj vas popelje na stran z metapodatki ali iskanje na Arhivu Ane."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Še vedno morate biti sposobni preklapljati med vsemi različnimi datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Obsegi držav in založnikov naj bodo poudarjeni ob premiku miške. Uporabite lahko npr. <a %(github_xlcnd_isbnlib)s>data4info.py v isbnlib</a> za informacije o državah in našo »isbngrp« strganje za založnike (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Delovati mora dobro na namizju in mobilnih napravah."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Za dodatne točke (to so le ideje — pustite svoji ustvarjalnosti prosto pot):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Močno se bo upoštevala uporabnost in vizualna privlačnost."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Prikažite dejanske metapodatke za posamezne ISBN pri povečavi, kot so naslov in avtor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Boljša krivulja zapolnjevanja prostora. Npr. cik-cak, ki gre od 0 do 4 v prvi vrstici in nato nazaj (v obratni smeri) od 5 do 9 v drugi vrstici — rekurzivno uporabljeno."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Različne ali prilagodljive barvne sheme."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Posebni pogledi za primerjavo datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Načini za odpravljanje težav, kot so drugačna metadata, ki se ne ujemajo dobro (npr. zelo različni naslovi)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Označevanje slik s komentarji o ISBN-jih ali razponih."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Kakršne koli heuristike za prepoznavanje redkih ali ogroženih knjig."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Kakršne koli ustvarjalne ideje, ki jih lahko zamislite!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Lahko se popolnoma oddaljite od minimalnih kriterijev in naredite popolnoma drugačno vizualizacijo. Če je res spektakularna, potem se kvalificira za nagrado, vendar po naši presoji."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Oddajte prispevke tako, da objavite komentar na <a %(annas_archive)s>to težavo</a> s povezavo do vašega forked repo, merge request ali diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Koda"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Koda za generiranje teh slik, kot tudi drugi primeri, se nahajajo v <a %(annas_archive)s>tem imeniku</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Izumili smo kompakten format podatkov, s katerim je vsa potrebna ISBN informacija približno 75MB (stisnjeno). Opis formata podatkov in koda za njegovo generiranje sta na voljo <a %(annas_archive_l1244_1319)s>tukaj</a>. Za nagrado ni potrebno, da to uporabite, vendar je verjetno najbolj priročen format za začetek. Našo metadata lahko preoblikujete, kakor želite (vsa vaša koda pa mora biti odprtokodna)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Komaj čakamo, da vidimo, kaj boste ustvarili. Srečno!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annin Arhiv Kontejnerji (AAK): standardizacija izdaj iz največje senčne knjižnice na svetu"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annin Arhiv je postal največja senčna knjižnica na svetu, kar zahteva standardizacijo naših izdaj."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annin Arhiv</a> je postal daleč največja senčna knjižnica na svetu in edina senčna knjižnica te velikosti, ki je popolnoma odprtokodna in odprta za podatke. Spodaj je tabela z naše strani Datasets (rahlo spremenjena):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "To smo dosegli na tri načine:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Zrcaljenje obstoječih odprtokodnih senčnih knjižnic (kot sta Sci-Hub in Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Pomoč senčnim knjižnicam, ki želijo biti bolj odprte, vendar niso imele časa ali sredstev za to (kot je zbirka stripov Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Strganje knjižnic, ki ne želijo deliti v velikem obsegu (kot je Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Za (2) in (3) zdaj sami upravljamo z obsežno zbirko torrentov (100-ine TB-jev). Do sedaj smo te zbirke obravnavali kot enkratne, kar pomeni prilagojeno infrastrukturo in organizacijo podatkov za vsako zbirko. To dodaja znatne stroške vsakemu izidu in otežuje izvedbo bolj postopnih izdaj."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Zato smo se odločili standardizirati naše izdaje. To je tehnična objava na blogu, v kateri predstavljamo naš standard: <strong>Annini Arhivski Kontejnerji</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Cilji oblikovanja"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Naš primarni primer uporabe je distribucija datotek in pripadajočih metadata iz različnih obstoječih zbirk. Naše najpomembnejše premisleke so:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene datoteke in metadata, čim bližje izvirni obliki."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogeni identifikatorji v izvornih knjižnicah ali celo pomanjkanje identifikatorjev."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Ločene izdaje metadata v primerjavi s podatki datotek ali izdaje samo metadata (npr. naša izdaja ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribucija preko torrentov, vendar z možnostjo drugih metod distribucije (npr. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Nespremenljivi zapisi, saj moramo predvidevati, da bodo naši torrenti živeli večno."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Postopne izdaje / dodajljive izdaje."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Strojno berljivo in zapisljivo, priročno in hitro, še posebej za našo tehnologijo (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Nekoliko enostaven človeški pregled, čeprav je to sekundarno glede na strojno berljivost."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Enostavno sejanje naših zbirk s standardno najeto seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binarne podatke lahko neposredno strežejo spletni strežniki, kot je Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Nekateri ne-cilji:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Ne zanima nas, da bi bile datoteke enostavne za ročno navigacijo na disku ali iskanje brez predhodne obdelave."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Ne zanima nas, da bi bili neposredno združljivi z obstoječo knjižnično programsko opremo."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Čeprav bi moralo biti enostavno za vsakogar, da seje našo zbirko z uporabo torrentov, ne pričakujemo, da bodo datoteke uporabne brez znatnega tehničnega znanja in zavezanosti."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Ker je Annin Arhiv odprtokoden, želimo neposredno uporabljati naš format. Ko osvežimo naš iskalni indeks, dostopamo le do javno dostopnih poti, tako da lahko vsak, ki razveja našo knjižnico, hitro začne delovati."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Na koncu smo se odločili za razmeroma preprost standard. Je precej ohlapen, nenormativen in še v razvoju."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Kontejner Anninega Arhiva) je en sam element, ki vsebuje <strong>metadata</strong> in po želji <strong>binarne podatke</strong>, ki so nespremenljivi. Ima globalno edinstven identifikator, imenovan <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Zbirka.</strong> Vsak AAC pripada zbirki, ki je po definiciji seznam AAC-jev, ki so semantično skladni. To pomeni, da če naredite pomembno spremembo v formatu metadata, morate ustvariti novo zbirko."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Zbirke “zapisov” in “datotek”.</strong> Po konvenciji je pogosto priročno izdati “zapise” in “datoteke” kot različne zbirke, da jih lahko izdajamo po različnih urnikih, npr. glede na stopnje strganja. “Zapis” je zbirka samo z metadata, ki vsebuje informacije, kot so naslovi knjig, avtorji, ISBN-ji itd., medtem ko “datoteke” so zbirke, ki vsebujejo dejanske datoteke (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Format AACID je naslednji: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Na primer, dejanski AACID, ki smo ga izdali, je <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: ime zbirke, ki lahko vsebuje ASCII črke, številke in podčrtaje (vendar ne dvojnih podčrtajev)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: kratka različica ISO 8601, vedno v UTC, npr. <code>20220723T194746Z</code>. Ta številka se mora monotono povečevati za vsako izdajo, čeprav se lahko njeni natančni pomeni razlikujejo glede na zbirko. Predlagamo uporabo časa strganja ali generiranja ID-ja."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: zbirki specifičen identifikator, če je to primerno, npr. Z-Library ID. Lahko se izpusti ali skrajša. Mora biti izpuščen ali skrajšan, če bi AACID sicer presegel 150 znakov."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, vendar stisnjen v ASCII, npr. z uporabo base57. Trenutno uporabljamo Python knjižnico <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Obseg AACID.</strong> Ker AACID-ji vsebujejo monotono naraščajoče časovne žige, jih lahko uporabimo za označevanje obsegov znotraj določene zbirke. Uporabljamo ta format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kjer so časovni žigi vključeni. To je skladno z ISO 8601 notacijo. Obsegi so neprekinjeni in se lahko prekrivajo, vendar morajo v primeru prekrivanja vsebovati enake zapise kot tisti, ki so bili prej izdani v tej zbirki (saj so AAC-ji nespremenljivi). Manjkajoči zapisi niso dovoljeni."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Datoteka z metadata.</strong> Datoteka z metadata vsebuje metadata obsega AAC-jev za eno določeno zbirko. Imajo naslednje lastnosti:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Ime datoteke mora biti obseg AACID, pred katerim je <code style=\"color: red\">annas_archive_meta__</code> in sledi <code>.jsonl.zstd</code>. Na primer, ena od naših izdaj se imenuje<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Kot nakazuje pripona datoteke, je tip datoteke <a %(jsonlines)s>JSON Lines</a> stisnjen z <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Vsak JSON objekt mora vsebovati naslednja polja na najvišji ravni: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (neobvezno). Druga polja niso dovoljena."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> so poljubni metadata, glede na semantiko zbirke. Morajo biti semantično skladni znotraj zbirke."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> je neobvezno in je ime mape z binarnimi podatki, ki vsebuje ustrezne binarne podatke. Ime datoteke ustreznih binarnih podatkov v tej mapi je AACID zapisa."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Predpona <code style=\"color: red\">annas_archive_meta__</code> se lahko prilagodi imenu vaše institucije, npr. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Mapa z binarnimi podatki.</strong> Mapa z binarnimi podatki obsega AAC-jev za eno določeno zbirko. Imajo naslednje lastnosti:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Ime mape mora biti obseg AACID, pred katerim je <code style=\"color: green\">annas_archive_data__</code>, brez pripone. Na primer, ena od naših dejanskih izdaj ima mapo z imenom<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Mapa mora vsebovati datoteke z podatki za vse AAC-je znotraj določenega obsega. Vsaka datoteka z podatki mora imeti AACID kot ime datoteke (brez pripon)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Priporočljivo je, da so te mape nekoliko obvladljive po velikosti, npr. ne večje od 100 GB-1 TB vsaka, čeprav se lahko to priporočilo sčasoma spremeni."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrenti.</strong> Datoteke z metadata in mape z binarnimi podatki so lahko združene v torrentih, z enim torrentom na datoteko z metadata ali enim torrentom na mapo z binarnimi podatki. Torrenti morajo imeti izvirno ime datoteke/imenika plus pripono <code>.torrent</code> kot svoje ime datoteke."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Primer"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Poglejmo si našo nedavno izdajo Z-Library kot primer. Sestavljena je iz dveh zbirk: “<span style=\"background: #fffaa3\">zlib3_records</span>” in “<span style=\"background: #ffd6fe\">zlib3_files</span>”. To nam omogoča, da ločeno zajamemo in izdamo zapise metadata od dejanskih datotek knjig. Tako smo izdali dva torrenta z datotekami metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Prav tako smo izdali kup torrentov z mapami binarnih podatkov, vendar le za zbirko “<span style=\"background: #ffd6fe\">zlib3_files</span>”, skupno 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Z zagonom <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> lahko vidimo, kaj je znotraj:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "V tem primeru gre za metadata knjige, kot poroča Z-Library. Na najvišji ravni imamo le “aacid” in “metadata”, vendar ni “data_folder”, saj ni ustreznih binarnih podatkov. AACID vsebuje “22430000” kot primarni ID, ki ga lahko vidimo, da je vzet iz “zlibrary_id”. Pričakujemo lahko, da bodo imeli drugi AAC-ji v tej zbirki enako strukturo."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Zdaj zaženimo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "To je veliko manjša metadata AAC, čeprav se večina tega AAC nahaja drugje v binarni datoteki! Konec koncev imamo tokrat “data_folder”, zato lahko pričakujemo, da se ustrezni binarni podatki nahajajo na <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” vsebuje “zlibrary_id”, zato ga lahko enostavno povežemo z ustreznim AAC v zbirki “zlib_records”. Povezali bi lahko na več različnih načinov, npr. preko AACID — standard tega ne predpisuje."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Upoštevajte, da tudi ni nujno, da je polje “metadata” samo JSON. Lahko je niz, ki vsebuje XML ali katerikoli drug podatkovni format. Metadata informacije bi lahko celo shranili v povezani binarni blob, npr. če gre za veliko podatkov."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Zaključek"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "S tem standardom lahko izdaje izvajamo bolj postopoma in lažje dodajamo nove vire podatkov. Že imamo nekaj vznemirljivih izdaj v pripravi!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Upamo tudi, da bo drugim senčnim knjižnicam lažje zrcaliti naše zbirke. Konec koncev je naš cilj ohraniti človeško znanje in kulturo za vedno, zato je večja redundanca boljša."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annina posodobitev: popolnoma odprtokodni arhiv, ElasticSearch, 300 GB+ naslovnic knjig"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Delali smo neprekinjeno, da bi zagotovili dobro alternativo z Anninim Arhivom. Tukaj je nekaj stvari, ki smo jih nedavno dosegli."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Z zaprtjem Z-Library in aretacijo (domnevnih) ustanoviteljev smo delali neprekinjeno, da bi zagotovili dobro alternativo z Anninim Arhivom (tukaj ga ne bomo povezali, vendar ga lahko poiščete na Googlu). Tukaj je nekaj stvari, ki smo jih nedavno dosegli."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annin Arhiv je popolnoma odprtokoden"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Verjamemo, da bi morale biti informacije brezplačne, in naša lastna koda ni izjema. Vso našo kodo smo objavili na naši zasebno gostovani instance Gitlaba: <a %(annas_archive)s>Annina Programska Oprema</a>. Prav tako uporabljamo sledilnik težav za organizacijo našega dela. Če želite sodelovati pri našem razvoju, je to odličen kraj za začetek."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Da vam damo okus stvari, na katerih delamo, si oglejte naše nedavno delo na izboljšavah zmogljivosti na strani odjemalca. Ker še nismo implementirali straniščanja, bi pogosto vrnili zelo dolge strani z rezultati iskanja, s 100-200 rezultati. Nismo želeli prehitro prekiniti rezultatov iskanja, vendar je to pomenilo, da bi upočasnilo nekatere naprave. Za to smo implementirali majhen trik: večino rezultatov iskanja smo zavili v HTML komentarje (<code><!-- --></code>), nato pa napisali majhen Javascript, ki bi zaznal, kdaj naj rezultat postane viden, v tem trenutku pa bi razpakirali komentar:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualizacija\" izvedena v 23 vrsticah, brez potrebe po naprednih knjižnicah! To je vrsta hitre pragmatične kode, ki jo ustvarite, ko imate omejen čas in resnične težave, ki jih je treba rešiti. Poročali so, da naše iskanje zdaj dobro deluje na počasnih napravah!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Še en velik napor je bil avtomatizirati gradnjo baze podatkov. Ko smo začeli, smo preprosto naključno združili različne vire. Zdaj jih želimo posodabljati, zato smo napisali kup skriptov za prenos novih metadata iz dveh forkov Library Genesis in jih integrirali. Cilj ni le, da je to koristno za naš arhiv, ampak tudi, da olajšamo delo vsakomur, ki želi raziskovati metadata senčnih knjižnic. Cilj bi bil Jupyter zvezek, ki ima na voljo vse vrste zanimivih metadata, da lahko opravimo več raziskav, kot je ugotavljanje, kakšen <a %(blog)s>odstotek ISBN-jev je ohranjen za vedno</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Na koncu smo prenovili naš sistem donacij. Zdaj lahko uporabite kreditno kartico za neposredno nakazilo denarja v naše kripto denarnice, ne da bi morali vedeti kaj o kriptovalutah. Še naprej bomo spremljali, kako dobro to deluje v praksi, vendar je to velik korak naprej."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Preklop na ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Ena od naših <a %(annas_archive)s>vstopnic</a> je bila zbirka težav z našim iskalnim sistemom. Uporabljali smo MySQL iskanje s polnim besedilom, saj smo imeli vse podatke v MySQL. Vendar je imelo svoje omejitve:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Nekatera iskanja so trajala zelo dolgo, do te mere, da so zasedla vse odprte povezave."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Privzeto ima MySQL minimalno dolžino besede, ali pa lahko vaš indeks postane res velik. Ljudje so poročali, da ne morejo iskati \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Iskanje je bilo le nekoliko hitro, ko je bilo popolnoma naloženo v pomnilnik, kar je zahtevalo, da smo dobili dražji stroj za izvajanje tega, poleg nekaterih ukazov za prednalaganje indeksa ob zagonu."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Ne bi ga mogli enostavno razširiti za gradnjo novih funkcij, kot so boljša <a %(wikipedia_cjk_characters)s>tokenizacija za jezike brez presledkov</a>, filtriranje/faceting, razvrščanje, predlogi \"ste mislili\", samodokončanje in tako naprej."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Po pogovorih z več strokovnjaki smo se odločili za ElasticSearch. Ni bil popoln (njihovi privzeti predlogi \"ste mislili\" in funkcije samodokončanja so slabe), vendar je bil na splošno veliko boljši od MySQL za iskanje. Še vedno nismo <a %(youtube)s>preveč navdušeni</a> nad uporabo za katero koli kritično misijo (čeprav so naredili veliko <a %(elastic_co)s>napredka</a>), vendar smo na splošno zelo zadovoljni s prehodom."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Za zdaj smo implementirali veliko hitrejše iskanje, boljšo podporo za jezike, boljše razvrščanje po pomembnosti, različne možnosti razvrščanja in filtriranje po jeziku/vrsti knjige/vrsti datoteke. Če vas zanima, kako deluje, <a %(annas_archive_l140)s>si</a> <a %(annas_archive_l1115)s>oglejte</a> <a %(annas_archive_l1635)s>to</a>. Je precej dostopno, čeprav bi lahko uporabili še nekaj komentarjev…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ naslovnic knjig izdanih"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Na koncu z veseljem objavljamo majhno izdajo. V sodelovanju z ljudmi, ki upravljajo fork Libgen.rs, delimo vse njihove naslovnice knjig prek torrentov in IPFS. To bo porazdelilo obremenitev ogledovanja naslovnic med več strojev in jih bolje ohranilo. V mnogih (vendar ne vseh) primerih so naslovnice knjig vključene v same datoteke, zato so to nekakšni \"izvedeni podatki\". Vendar pa je imeti jih v IPFS še vedno zelo koristno za vsakodnevno delovanje tako Anninega Arhiva kot različnih forkov Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Kot običajno, lahko to izdajo najdete v Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Annin Arhiv</a>). Tukaj ne bomo povezali, vendar jo lahko zlahka najdete."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Upamo, da lahko malo upočasnimo tempo, zdaj ko imamo dostojno alternativo Z-Library. Ta delovna obremenitev ni posebej trajnostna. Če vas zanima pomoč pri programiranju, strežniških operacijah ali delu na ohranjanju, se nam vsekakor oglasite. Še vedno je veliko <a %(annas_archive)s>dela, ki ga je treba opraviti</a>. Hvala za vaše zanimanje in podporo."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annin Arhiv je varnostno kopiral največjo senčno knjižnico stripov na svetu (95TB) — lahko pomagate pri sejanju"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Največja senčna knjižnica stripov na svetu je imela eno samo točko odpovedi.. do danes."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Razpravljajte na Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Največja senčna knjižnica stripov je verjetno tista določenega forka Library Genesis: Libgen.li. En sam administrator, ki vodi to spletno stran, je uspel zbrati neverjetno zbirko stripov z več kot 2 milijonoma datotek, ki skupaj obsegajo več kot 95TB. Vendar pa, za razliko od drugih zbirk Library Genesis, ta ni bila na voljo v celoti prek torrentov. Do teh stripov ste lahko dostopali le posamezno prek njegovega počasnega osebnega strežnika — ena sama točka odpovedi. Do danes!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "V tej objavi vam bomo povedali več o tej zbirki in o naši zbiralni akciji za podporo več takšnega dela."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon se poskuša izgubiti v vsakdanjem svetu knjižnice…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen vilice"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Najprej nekaj ozadja. Morda poznate Library Genesis zaradi njihove epske zbirke knjig. Manj ljudi ve, da so prostovoljci Library Genesis ustvarili tudi druge projekte, kot so obsežna zbirka revij in standardnih dokumentov, popolna varnostna kopija Sci-Hub (v sodelovanju z ustanoviteljico Sci-Hub, Alexandro Elbakyan) in resnično ogromna zbirka stripov."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Na neki točki so se različni operaterji zrcal Library Genesis razšli, kar je povzročilo trenutno situacijo z več različnimi \"vilicami\", ki še vedno nosijo ime Library Genesis. Vilica Libgen.li ima edinstveno to zbirko stripov, pa tudi obsežno zbirko revij (na kateri tudi delamo)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Sodelovanje"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Glede na svojo velikost je bila ta zbirka že dolgo na našem seznamu želja, zato smo po uspehu z varnostno kopijo Z-Library usmerili pogled na to zbirko. Sprva smo jo neposredno strgali, kar je bil kar izziv, saj njihov strežnik ni bil v najboljšem stanju. Na ta način smo pridobili približno 15TB, vendar je šlo počasi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Na srečo smo uspeli stopiti v stik z operaterjem knjižnice, ki se je strinjal, da nam pošlje vse podatke neposredno, kar je bilo veliko hitreje. Kljub temu je trajalo več kot pol leta, da smo prenesli in obdelali vse podatke, in skoraj smo jih izgubili zaradi okvare diska, kar bi pomenilo začeti znova."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Ta izkušnja nas je prepričala, da je pomembno, da te podatke čim prej spravimo v javnost, da jih lahko zrcalimo široko in daleč. Smo le en ali dva nesrečno časovno usklajena incidenta stran od tega, da bi to zbirko za vedno izgubili!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Zbirka"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Hitro premikanje pomeni, da je zbirka nekoliko neorganizirana… Poglejmo si jo. Predstavljajte si, da imamo datotečni sistem (ki ga v resnici razdeljujemo po torrentih):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Prva mapa, <code>/repository</code>, je bolj strukturiran del tega. Ta mapa vsebuje tako imenovane \"tisoč mape\": mape, vsaka s tisoč datotekami, ki so postopoma oštevilčene v bazi podatkov. Mapa <code>0</code> vsebuje datoteke s comic_id 0–999 in tako naprej."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "To je isti sistem, ki ga Library Genesis uporablja za svoje zbirke fikcije in nefikcije. Ideja je, da se vsaka \"tisoč mapa\" samodejno spremeni v torrent, takoj ko je napolnjena."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Vendar pa operater Libgen.li nikoli ni ustvaril torrentov za to zbirko, zato so tisoč mape verjetno postale nepraktične in so se umaknile \"neurejenim mapam\". Te so <code>/comics0</code> do <code>/comics4</code>. Vse vsebujejo edinstvene strukture map, ki so verjetno imele smisel za zbiranje datotek, vendar nam zdaj ne pomenijo veliko. Na srečo metadata še vedno neposredno sklicuje na vse te datoteke, zato njihova organizacija shranjevanja na disku dejansko ni pomembna!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata je na voljo v obliki MySQL baze podatkov. To lahko prenesete neposredno s spletne strani Libgen.li, vendar jo bomo tudi mi naredili na voljo v torrentu, skupaj z našo lastno tabelo z vsemi MD5 hashi."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analiza"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Ko dobite 95TB podatkov v svoj shranjevalni grozd, poskušate ugotoviti, kaj sploh je tam notri… Naredili smo nekaj analiz, da bi videli, ali lahko nekoliko zmanjšamo velikost, na primer z odstranitvijo podvojenih datotek. Tukaj je nekaj naših ugotovitev:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantične podvojitve (različni skeni iste knjige) je teoretično mogoče filtrirati, vendar je to zapleteno. Ko smo ročno pregledovali stripe, smo našli preveč lažnih pozitivnih rezultatov."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Obstajajo nekatere podvojitve zgolj po MD5, kar je relativno potratno, vendar bi njihovo filtriranje prineslo le približno 1% in prihranka. Na tej ravni je to še vedno približno 1TB, vendar tudi na tej ravni 1TB ni res pomemben. Raje ne bi tvegali nenamernega uničenja podatkov v tem procesu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Našli smo kup neknjižnih podatkov, kot so filmi, ki temeljijo na stripih. To se zdi tudi potratno, saj so ti že široko dostopni po drugih poteh. Vendar smo ugotovili, da ne moremo preprosto filtrirati filmskih datotek, saj obstajajo tudi <em>interaktivne stripovske knjige</em>, ki so bile izdane na računalniku, nekdo pa jih je posnel in shranil kot filme."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Na koncu bi lahko z brisanjem iz zbirke prihranili le nekaj odstotkov. Potem smo se spomnili, da smo zbiratelji podatkov, in ljudje, ki bodo to zrcalili, so prav tako zbiratelji podatkov, zato, \"KAJ MISLITE, BRISATI?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Zato vam predstavljamo celotno, nespremenjeno zbirko. To je veliko podatkov, vendar upamo, da bo dovolj ljudi, ki bodo kljub temu skrbeli za njeno deljenje."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Zbiranje sredstev"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Te podatke objavljamo v velikih kosih. Prvi torrent je <code>/comics0</code>, ki smo ga združili v eno ogromno 12TB .tar datoteko. To je boljše za vaš trdi disk in torrent programsko opremo kot milijon manjših datotek."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Kot del te izdaje organiziramo zbiranje sredstev. Želimo zbrati 20.000 $ za kritje operativnih in pogodbenih stroškov za to zbirko ter omogočiti tekoče in prihodnje projekte. Imamo nekaj <em>ogromnih</em> v pripravi."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Koga podpirate z vašo donacijo?</em> Na kratko: podpiramo varnostno kopiranje vsega znanja in kulture človeštva ter omogočamo enostaven dostop do tega. Vsa naša koda in podatki so odprtokodni, smo popolnoma prostovoljski projekt in do sedaj smo shranili 125TB knjig (poleg obstoječih torrentov Libgen in Scihub). Na koncu gradimo vztrajnik, ki omogoča in spodbuja ljudi, da najdejo, skenirajo in varnostno kopirajo vse knjige na svetu. O našem glavnem načrtu bomo pisali v prihodnji objavi. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Če donirate za 12-mesečno članstvo \"Amazing Archivist\" (780 $), lahko <strong>“posvojite torrent”</strong>, kar pomeni, da bomo vaše uporabniško ime ali sporočilo vključili v ime ene od torrent datotek!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Donirate lahko tako, da obiščete <a %(wikipedia_annas_archive)s>Anin Arhiv</a> in kliknete gumb \"Doniraj\". Prav tako iščemo več prostovoljcev: programerje, raziskovalce varnosti, strokovnjake za anonimno trgovanje in prevajalce. Podprete nas lahko tudi z zagotavljanjem gostiteljskih storitev. In seveda, prosimo, delite naše torrente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Hvala vsem, ki ste nas že tako velikodušno podprli! Resnično delate razliko."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Tukaj so torrenti, ki so bili do sedaj izdani (še vedno obdelujemo ostale):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Vse torrente lahko najdete na <a %(wikipedia_annas_archive)s>Anin Arhiv</a> pod \"Datasets\" (ne povezujemo se neposredno tja, da povezave do tega bloga ne bi bile odstranjene z Reddita, Twitterja itd.). Od tam sledite povezavi na spletno stran Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Kaj sledi?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Veliko torrentov je odličnih za dolgoročno ohranjanje, vendar ne toliko za vsakodnevni dostop. Sodelovali bomo z gostiteljskimi partnerji, da vse te podatke spravimo na splet (saj Anin Arhiv ničesar ne gosti neposredno). Seveda boste te povezave za prenos lahko našli na Aninem Arhivu."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Prav tako vabimo vse, da kaj naredijo s temi podatki! Pomagajte nam jih bolje analizirati, odstraniti podvojene, jih postaviti na IPFS, jih premešati, trenirati svoje AI modele z njimi in tako naprej. Vse je vaše, in komaj čakamo, da vidimo, kaj boste storili z njimi."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Nazadnje, kot smo že povedali, imamo še nekaj velikih izdaj v pripravi (če bi <em>nekdo</em> lahko <em>po nesreči</em> poslal izpisek <em>določene</em> ACS4 baze podatkov, veste, kje nas najti...), kot tudi gradnjo vztrajnika za varnostno kopiranje vseh knjig na svetu."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Zato ostanite z nami, šele začenjamo."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nove knjige dodane v Pirate Library Mirror (+24TB, 3,8 milijona knjig)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "V prvotni izdaji Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>) smo ustvarili zrcalo Z-Library, velike nezakonite zbirke knjig. Kot opomnik, to smo zapisali v tisti prvotni objavi na blogu:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library je priljubljena (in nezakonita) knjižnica. Vzeli so zbirko Library Genesis in jo naredili enostavno iskalno. Poleg tega so postali zelo učinkoviti pri pridobivanju novih prispevkov knjig, saj spodbujajo uporabnike k prispevanju z različnimi ugodnostmi. Trenutno teh novih knjig ne prispevajo nazaj v Library Genesis. In za razliko od Library Genesis, ne omogočajo enostavnega zrcaljenja svoje zbirke, kar preprečuje široko ohranjanje. To je pomembno za njihov poslovni model, saj zaračunavajo denar za dostop do svoje zbirke v večjih količinah (več kot 10 knjig na dan)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Ne izrekamo moralnih sodb o zaračunavanju denarja za množični dostop do nezakonite zbirke knjig. Nedvomno je, da je Z-Library uspešno razširila dostop do znanja in pridobila več knjig. Tukaj smo preprosto zato, da opravimo svoj del: zagotovimo dolgoročno ohranitev te zasebne zbirke."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Ta zbirka sega v sredino leta 2021. Medtem je Z-Library rasla z osupljivo hitrostjo: dodali so približno 3,8 milijona novih knjig. Seveda so tam tudi nekatere podvojene, vendar se zdi, da je večina resnično novih knjig ali pa gre za bolj kakovostne skene že predloženih knjig. To je v veliki meri posledica povečanega števila prostovoljnih moderatorjev v Z-Library in njihovega sistema množičnega nalaganja z deduplikacijo. Radi bi jim čestitali za te dosežke."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Z veseljem sporočamo, da smo pridobili vse knjige, ki so bile dodane v Z-Library med našim zadnjim zrcalom in avgustom 2022. Prav tako smo se vrnili in zbrali nekatere knjige, ki smo jih prvič zgrešili. Vse skupaj je ta nova zbirka velika približno 24TB, kar je precej več kot prejšnja (7TB). Naše zrcalo je zdaj skupno 31TB. Ponovno smo izvedli deduplikacijo proti Library Genesis, saj so za to zbirko že na voljo torrenti."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Prosimo, obiščite Pirate Library Mirror, da si ogledate novo zbirko (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>). Tam je več informacij o tem, kako so datoteke strukturirane in kaj se je spremenilo od zadnjič. Ne bomo povezovali od tukaj, saj je to le blog spletna stran, ki ne gosti nobenih nezakonitih materialov."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Seveda je tudi deljenje odličen način, da nam pomagate. Hvala vsem, ki delite naš prejšnji nabor torrentov. Hvaležni smo za pozitiven odziv in veseli, da je toliko ljudi, ki jim je mar za ohranitev znanja in kulture na ta nenavaden način."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Kako postati piratski arhivist"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Prvi izziv je lahko presenetljiv. Ni tehnični problem ali pravni problem. Je psihološki problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Preden se poglobimo, dve posodobitvi o Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Prejeli smo nekaj izjemno velikodušnih donacij. Prva je bila 10.000 $ od anonimne osebe, ki je prav tako podpirala \"bookwarriorja\", prvotnega ustanovitelja Library Genesis. Posebna zahvala bookwarriorju za posredovanje te donacije. Druga je bila še ena donacija v višini 10.000 $ od anonimnega darovalca, ki se je obrnil na nas po naši zadnji izdaji in bil navdihnjen, da pomaga. Imeli smo tudi več manjših donacij. Najlepša hvala za vso vašo velikodušno podporo. Imamo nekaj vznemirljivih novih projektov v pripravi, ki jih bo to podprlo, zato ostanite z nami."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Imeli smo nekaj tehničnih težav z velikostjo naše druge izdaje, vendar so naši torrenti zdaj na voljo in se delijo. Prav tako smo prejeli velikodušno ponudbo od anonimne osebe, da deli našo zbirko na njihovih zelo hitrih strežnikih, zato izvajamo poseben prenos na njihove naprave, po katerem bi morali vsi ostali, ki prenašajo zbirko, opaziti veliko izboljšanje hitrosti."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "O celotnih knjigah bi lahko pisali o <em>zakaj</em> digitalnega ohranjanja na splošno in piratskega arhivizma posebej, vendar naj vam podamo kratek uvod za tiste, ki niso preveč seznanjeni. Svet proizvaja več znanja in kulture kot kdajkoli prej, vendar se tudi več tega izgublja kot kdajkoli prej. Človeštvo v veliki meri zaupa korporacijam, kot so akademski založniki, storitve pretakanja in podjetja za družbena omrežja, s to dediščino, vendar se pogosto niso izkazali za odlične skrbnike. Oglejte si dokumentarec Digitalna amnezija ali katerikoli govor Jasona Scotta."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Obstajajo nekatere institucije, ki dobro arhivirajo, kolikor lahko, vendar so omejene z zakonom. Kot pirati smo v edinstvenem položaju, da arhiviramo zbirke, ki jih ne morejo dotakniti, zaradi uveljavljanja avtorskih pravic ali drugih omejitev. Prav tako lahko zrcalimo zbirke večkrat po vsem svetu, s čimer povečamo možnosti za pravilno ohranitev."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Za zdaj se ne bomo spuščali v razprave o prednostih in slabostih intelektualne lastnine, morali o kršenju zakona, razmišljanjih o cenzuri ali vprašanju dostopa do znanja in kulture. Ko je vse to izven poti, se poglobimo v <em>kako</em>. Delili bomo, kako je naša ekipa postala piratski arhivisti in lekcije, ki smo se jih naučili na poti. Obstaja veliko izzivov, ko se podate na to pot, in upamo, da vam lahko pomagamo skozi nekatere izmed njih."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Skupnost"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Prvi izziv je lahko presenetljiv. Ni tehnični problem ali pravni problem. Je psihološki problem: opravljanje tega dela v senci je lahko izjemno osamljeno. Glede na to, kaj nameravate storiti, in vaš model grožnje, boste morda morali biti zelo previdni. Na enem koncu spektra imamo ljudi, kot je Alexandra Elbakyan*, ustanoviteljica Sci-Hub, ki je zelo odprta glede svojih dejavnosti. Vendar je v veliki nevarnosti, da bo aretirana, če bi v tem trenutku obiskala zahodno državo, in bi se lahko soočila z desetletji zaporne kazni. Je to tveganje, ki bi ga bili pripravljeni sprejeti? Mi smo na drugem koncu spektra; zelo pazimo, da ne puščamo nobenih sledi in imamo močno operativno varnost."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Kot je omenil \"ynno\" na HN, Alexandra sprva ni želela biti znana: \"Njeni strežniki so bili nastavljeni tako, da so oddajali podrobna sporočila o napakah iz PHP, vključno s celotno potjo do izvorne datoteke, ki je bila pod imenikom /home/<USER>" Zato uporabite naključna uporabniška imena na računalnikih, ki jih uporabljate za te stvari, v primeru, da nekaj napačno konfigurirate."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Ta skrivnostnost pa ima psihološko ceno. Večina ljudi ljubi, da so prepoznani za delo, ki ga opravljajo, in vendar ne morete prejeti nobenega priznanja za to v resničnem življenju. Tudi preproste stvari so lahko izziv, kot na primer, ko vas prijatelji vprašajo, kaj ste počeli (na neki točki \"igranje z mojim NAS / homelab\" postane staro)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Zato je tako pomembno najti neko skupnost. Lahko se odpoveste nekaj operativne varnosti, če se zaupate nekaterim zelo bližnjim prijateljem, za katere veste, da jim lahko globoko zaupate. Tudi takrat bodite previdni, da ne zapišete ničesar, v primeru, da bi morali predati svoje e-pošte oblastem ali če bi bile njihove naprave kako drugače ogrožene."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Še bolje je najti nekaj somišljenikov piratov. Če so vaši bližnji prijatelji zainteresirani, da se vam pridružijo, odlično! V nasprotnem primeru boste morda lahko našli druge na spletu. Na žalost je to še vedno nišna skupnost. Do sedaj smo našli le peščico drugih, ki so aktivni na tem področju. Dobri začetni kraji se zdijo forumi Library Genesis in r/DataHoarder. Ekipa Archive Team ima tudi somišljenike, čeprav delujejo znotraj zakona (tudi če v nekaterih sivih območjih zakona). Tradicionalne \"warez\" in piratske scene imajo tudi ljudi, ki razmišljajo na podoben način."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Odprti smo za ideje o tem, kako spodbujati skupnost in raziskovati ideje. Prosto nam pošljite sporočilo na Twitterju ali Redditu. Morda bi lahko gostili nekakšen forum ali klepetalno skupino. Eden od izzivov je, da je to lahko hitro cenzurirano, če uporabljamo običajne platforme, zato bi morali to gostiti sami. Obstaja tudi kompromis med tem, da so te razprave popolnoma javne (večja možnost sodelovanja) in tem, da so zasebne (da potencialni \"cilji\" ne vedo, da jih bomo kmalu kopirali). O tem bomo morali razmisliti. Sporočite nam, če vas to zanima!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekti"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Ko izvajamo projekt, ima ta več faz:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Izbira domene / filozofija: Na katerem področju se želite osredotočiti in zakaj? Katere so vaše edinstvene strasti, veščine in okoliščine, ki jih lahko izkoristite v svojo korist?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Izbira cilja: Katero specifično zbirko boste zrcalili?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Pridobivanje metadata: Katalogiziranje informacij o datotekah, brez dejanskega prenosa (pogosto veliko večjih) datotek samih."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Izbira podatkov: Na podlagi metadata zožitev, kateri podatki so trenutno najbolj pomembni za arhiviranje. Lahko je vse, vendar pogosto obstaja razumen način za prihranek prostora in pasovne širine."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Pridobivanje podatkov: Dejanski prenos podatkov."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribucija: Pakiranje v torrente, objava nekje, pridobivanje ljudi, da jih širijo."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "To niso popolnoma neodvisne faze, pogosto pa vas spoznanja iz kasnejše faze vrnejo v prejšnjo fazo. Na primer, med pridobivanjem metadata lahko ugotovite, da ima izbrani cilj obrambne mehanizme, ki presegajo vašo raven znanja (kot so blokade IP), zato se vrnete in poiščete drug cilj."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Izbira domene / filozofija"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Ni pomanjkanja znanja in kulturne dediščine, ki bi jo bilo treba ohraniti, kar je lahko preobremenjujoče. Zato je pogosto koristno, da si vzamete trenutek in premislite, kakšen je lahko vaš prispevek."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Vsak ima drugačen način razmišljanja o tem, vendar so tukaj nekatera vprašanja, ki si jih lahko zastavite:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Zakaj vas to zanima? Kaj vas navdušuje? Če lahko zberemo skupino ljudi, ki vsi arhivirajo stvari, ki jih posebej zanimajo, bi to pokrilo veliko! O svoji strasti boste vedeli veliko več kot povprečna oseba, na primer, kateri podatki so pomembni za shranjevanje, katere so najboljše zbirke in spletne skupnosti itd."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Katere veščine imate, ki jih lahko izkoristite v svojo korist? Na primer, če ste strokovnjak za spletno varnost, lahko najdete načine za premagovanje blokad IP za varne cilje. Če ste odlični v organiziranju skupnosti, lahko morda zberete nekaj ljudi okoli cilja. Vendar je koristno poznati nekaj programiranja, če ne drugega, za ohranjanje dobre operativne varnosti skozi ta proces."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Koliko časa imate za to? Naš nasvet bi bil, da začnete z majhnimi projekti in se lotite večjih, ko se navadite, vendar lahko postane vseobsegajoče."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Na katerem področju bi bilo smiselno osredotočiti se? Če boste porabili X ur za piratsko arhiviranje, kako lahko dosežete največji \"učinek za svoj denar\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Kakšni so edinstveni načini, kako razmišljate o tem? Morda imate nekaj zanimivih idej ali pristopov, ki jih drugi morda niso opazili."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "V našem primeru smo se posebej zavzemali za dolgoročno ohranjanje znanosti. Poznali smo Library Genesis in kako je bil večkrat popolnoma zrcaljen s pomočjo torrentov. Ta ideja nam je bila všeč. Potem je nekega dne eden od nas poskušal najti nekaj znanstvenih učbenikov na Library Genesis, vendar jih ni mogel najti, kar je vzbudilo dvom o tem, kako popoln je v resnici. Nato smo te učbenike poiskali na spletu in jih našli drugje, kar je zasadilo seme za naš projekt. Še preden smo vedeli za Z-Library, smo imeli idejo, da ne poskušamo zbrati vseh teh knjig ročno, ampak se osredotočimo na zrcaljenje obstoječih zbirk in jih prispevamo nazaj v Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Izbira cilja"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Torej, imamo naše področje, ki ga preučujemo, zdaj pa katero specifično zbirko naj zrcalimo? Obstaja nekaj stvari, ki so dober cilj:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Velika"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Edinstvena: ni že dobro pokrita z drugimi projekti."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Dostopna: ne uporablja veliko plasti zaščite, da bi preprečila pridobivanje njihove metadata in podatkov."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Posebno znanje: imate nekaj posebnih informacij o tem cilju, kot na primer, da imate nekako poseben dostop do te zbirke, ali pa ste ugotovili, kako premagati njihove obrambe. To ni nujno (naš prihajajoči projekt ne počne nič posebnega), vendar zagotovo pomaga!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Ko smo našli naše učbenike za znanost na spletnih straneh, ki niso Library Genesis, smo poskušali ugotoviti, kako so prišli na internet. Nato smo našli Z-Library in ugotovili, da čeprav večina knjig ne pride najprej tja, se tam sčasoma znajdejo. Spoznali smo njegovo povezavo z Library Genesis in (finančno) spodbujevalno strukturo ter superiorni uporabniški vmesnik, ki sta oba prispevala k temu, da je bila zbirka veliko bolj popolna. Nato smo izvedli nekaj predhodnega pridobivanja metadata in podatkov ter ugotovili, da lahko obidemo njihove omejitve prenosa IP, s čimer smo izkoristili poseben dostop enega od naših članov do številnih proxy strežnikov."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Ko raziskujete različne cilje, je že pomembno, da skrijete svoje sledi z uporabo VPN-jev in začasnih e-poštnih naslovov, o čemer bomo govorili več kasneje."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Pridobivanje metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Pojdimo malo bolj tehnično. Za dejansko pridobivanje metadata s spletnih strani smo stvari ohranili precej preproste. Uporabljamo Python skripte, včasih curl, in MySQL bazo podatkov za shranjevanje rezultatov. Nismo uporabili nobene napredne programske opreme za pridobivanje, ki bi lahko mapirala kompleksne spletne strani, saj smo doslej morali pridobiti le eno ali dve vrsti strani z enostavnim naštevanjem ID-jev in razčlenjevanjem HTML-ja. Če ni enostavno naštevanih strani, boste morda potrebovali ustreznega pajka, ki poskuša najti vse strani."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Preden začnete pridobivati celotno spletno stran, poskusite to narediti ročno za nekaj časa. Prebrskajte nekaj deset strani sami, da dobite občutek, kako to deluje. Včasih boste na ta način že naleteli na IP blokade ali drugo zanimivo vedenje. Enako velja za pridobivanje podatkov: preden se preveč poglobite v ta cilj, se prepričajte, da lahko dejansko učinkovito prenesete njegove podatke."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Za obhod omejitev lahko poskusite nekaj stvari. Ali obstajajo drugi IP naslovi ali strežniki, ki gostijo iste podatke, vendar nimajo enakih omejitev? Ali obstajajo API končne točke, ki nimajo omejitev, medtem ko jih druge imajo? Pri kakšni hitrosti prenosa je vaš IP blokiran in za koliko časa? Ali pa niste blokirani, ampak upočasnjeni? Kaj se zgodi, če ustvarite uporabniški račun, kako se stvari spremenijo potem? Ali lahko uporabite HTTP/2 za ohranjanje odprtih povezav in ali to poveča hitrost, s katero lahko zahtevate strani? Ali obstajajo strani, ki naenkrat navajajo več datotek, in ali so tam navedene informacije zadostne?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Stvari, ki jih verjetno želite shraniti, vključujejo:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Naslov"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Ime datoteke / lokacija"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: lahko je nek notranji ID, vendar so ID-ji, kot sta ISBN ali DOI, prav tako uporabni."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Velikost: za izračun, koliko prostora na disku potrebujete."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): za potrditev, da ste datoteko pravilno prenesli."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum dodajanja/spremembe: da se lahko kasneje vrnete in prenesete datoteke, ki jih prej niste prenesli (čeprav lahko pogosto uporabite tudi ID ali hash za to)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Opis, kategorija, oznake, avtorji, jezik itd."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "To običajno naredimo v dveh fazah. Najprej prenesemo surove HTML datoteke, običajno neposredno v MySQL (da se izognemo številnim majhnim datotekam, o čemer bomo govorili več spodaj). Nato v ločenem koraku pregledamo te HTML datoteke in jih razčlenimo v dejanske MySQL tabele. Na ta način vam ni treba vsega ponovno prenesti od začetka, če odkrijete napako v vaši kodi za razčlenjevanje, saj lahko preprosto ponovno obdelate HTML datoteke z novo kodo. Prav tako je pogosto lažje paralelizirati korak obdelave, s čimer prihranite nekaj časa (in lahko napišete kodo za obdelavo, medtem ko pridobivanje teče, namesto da bi morali napisati oba koraka hkrati)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Na koncu, upoštevajte, da je za nekatere cilje vse, kar obstaja, le pridobivanje metapodatkov. Obstajajo ogromne zbirke metapodatkov, ki niso ustrezno ohranjene."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Izbor podatkov"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Pogosto lahko uporabite metapodatke, da ugotovite razumen podnabor podatkov za prenos. Tudi če želite na koncu prenesti vse podatke, je lahko koristno, da najprej določite prednost najpomembnejšim elementom, v primeru, da vas zaznajo in izboljšajo obrambo, ali ker bi morali kupiti več diskov, ali preprosto zato, ker se v vašem življenju zgodi kaj drugega, preden lahko prenesete vse."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Na primer, zbirka lahko vsebuje več izdaj iste osnovne vsebine (kot je knjiga ali film), kjer je ena označena kot najboljša kakovost. Shranjevanje teh izdaj najprej bi imelo veliko smisla. Morda boste želeli na koncu shraniti vse izdaje, saj so v nekaterih primerih metapodatki lahko napačno označeni, ali pa obstajajo neznane razlike med izdajami (na primer, \"najboljša izdaja\" je lahko najboljša v večini pogledov, vendar slabša v drugih, kot je film z višjo ločljivostjo, vendar brez podnapisov)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Prav tako lahko preiščete svojo bazo metapodatkov, da najdete zanimive stvari. Katera je največja datoteka, ki je gostovana, in zakaj je tako velika? Katera je najmanjša datoteka? Ali obstajajo zanimivi ali nepričakovani vzorci, ko gre za določene kategorije, jezike in tako naprej? Ali obstajajo podvojeni ali zelo podobni naslovi? Ali obstajajo vzorci, kdaj so bili podatki dodani, kot na primer en dan, ko je bilo dodanih veliko datotek naenkrat? Pogosto se lahko veliko naučite, če si ogledate nabor podatkov na različne načine."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "V našem primeru smo odstranili podvojene knjige Z-Library glede na md5 hashe v Library Genesis, s čimer smo prihranili veliko časa za prenos in prostora na disku. To je precej edinstvena situacija. V večini primerov ni celovitih baz podatkov, ki bi kazale, katere datoteke so že ustrezno ohranjene s strani drugih piratov. To je samo po sebi velika priložnost za nekoga tam zunaj. Bilo bi odlično imeti redno posodobljen pregled stvari, kot so glasba in filmi, ki so že široko razširjeni na torrent spletnih straneh, in so zato nižja prioriteta za vključitev v piratska zrcala."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Pridobivanje podatkov"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Zdaj ste pripravljeni, da dejansko prenesete podatke v velikem obsegu. Kot je bilo že omenjeno, bi morali do te točke že ročno prenesti nekaj datotek, da bi bolje razumeli vedenje in omejitve cilja. Vendar pa vas bodo še vedno čakale presenečenja, ko boste dejansko začeli prenašati veliko datotek naenkrat."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Naš nasvet tukaj je predvsem, da ostanete preprosti. Začnite tako, da preprosto prenesete nekaj datotek. Uporabite lahko Python, nato pa razširite na več niti. Včasih pa je še enostavneje, da neposredno iz baze podatkov ustvarite Bash datoteke in jih nato zaženete v več terminalskih oknih, da povečate obseg. Hiter tehnični trik, ki ga je vredno omeniti, je uporaba OUTFILE v MySQL, ki ga lahko napišete kjerkoli, če onemogočite \"secure_file_priv\" v mysqld.cnf (in se prepričajte, da onemogočite/prekličete AppArmor, če ste na Linuxu)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Podatke shranjujemo na preproste trde diske. Začnite s tem, kar imate, in počasi širite. Lahko je zastrašujoče razmišljati o shranjevanju stotin TB podatkov. Če je to situacija, s katero se soočate, najprej objavite dober podnabor in v svoji objavi prosite za pomoč pri shranjevanju preostalega. Če želite sami pridobiti več trdih diskov, ima r/DataHoarder nekaj dobrih virov za pridobivanje dobrih ponudb."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Poskusite se ne obremenjevati preveč s sofisticiranimi datotečnimi sistemi. Enostavno je pasti v zajčjo luknjo pri nastavitvi stvari, kot je ZFS. Ena tehnična podrobnost, ki se je zavedajte, je, da se mnogi datotečni sistemi ne spopadajo dobro z veliko datotekami. Ugotovili smo, da je preprosta rešitev ustvariti več imenikov, npr. za različne razpone ID-jev ali predpone hashov."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Po prenosu podatkov preverite celovitost datotek z uporabo hashov v metapodatkih, če so na voljo."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribucija"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Imate podatke, s čimer imate v lasti prvi piratski zrcalni strežnik vašega cilja (najverjetneje). Na mnoge načine je najtežji del za vami, vendar je najnevarnejši del še pred vami. Navsezadnje ste bili do zdaj prikriti; leteli ste pod radarjem. Vse, kar ste morali storiti, je bilo uporabljati dober VPN ves čas, ne izpolnjevati svojih osebnih podatkov v nobenih obrazcih (seveda), in morda uporabljati posebno sejo brskalnika (ali celo drug računalnik)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Zdaj morate razdeliti podatke. V našem primeru smo najprej želeli prispevati knjige nazaj v Library Genesis, vendar smo hitro odkrili težave pri tem (razvrščanje fikcije proti nefikciji). Zato smo se odločili za distribucijo z uporabo torrentov v slogu Library Genesis. Če imate priložnost prispevati k obstoječemu projektu, vam to lahko prihrani veliko časa. Vendar pa trenutno ni veliko dobro organiziranih piratskih zrcal."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Recimo, da se odločite za distribucijo torrentov sami. Poskusite ohraniti te datoteke majhne, da jih je enostavno zrcaliti na drugih spletnih straneh. Nato boste morali sami sejati torrente, medtem ko ostajate anonimni. Uporabite lahko VPN (z ali brez posredovanja vrat) ali plačate s premešanimi Bitcoini za Seedbox. Če ne veste, kaj nekateri od teh izrazov pomenijo, boste morali veliko prebrati, saj je pomembno, da razumete tveganja tukaj."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Torrent datoteke lahko gostite na obstoječih torrent spletnih straneh. V našem primeru smo se odločili dejansko gostiti spletno stran, saj smo želeli tudi jasno širiti našo filozofijo. To lahko storite sami na podoben način (uporabljamo Njalla za naše domene in gostovanje, plačano s premešanimi Bitcoini), vendar se tudi počutite svobodno, da nas kontaktirate, da gostimo vaše torrente. Želimo zgraditi celovit indeks piratskih zrcal skozi čas, če se ta ideja prime."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Kar se tiče izbire VPN, je bilo o tem že veliko napisanega, zato bomo le ponovili splošni nasvet, da izberete glede na ugled. Dejanske sodno preizkušene politike brez beleženja z dolgo zgodovino zaščite zasebnosti so po našem mnenju najnižja tveganja. Upoštevajte, da tudi če naredite vse prav, nikoli ne morete doseči ničelnega tveganja. Na primer, ko sejate svoje torrente, lahko zelo motiviran akter države verjetno pogleda na dohodne in odhodne podatkovne tokove za VPN strežnike in ugotovi, kdo ste. Ali pa lahko preprosto nekako naredite napako. Verjetno smo že, in bomo spet. Na srečo, države ne skrbijo <em>toliko</em> za piratstvo."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Ena odločitev, ki jo morate sprejeti za vsak projekt, je, ali ga objaviti z isto identiteto kot prej ali ne. Če še naprej uporabljate isto ime, se lahko napake v operativni varnosti iz prejšnjih projektov vrnejo, da vas ugriznejo. Toda objavljanje pod različnimi imeni pomeni, da ne gradite dolgotrajnejšega ugleda. Odločili smo se za močno operativno varnost od začetka, da lahko še naprej uporabljamo isto identiteto, vendar ne bomo oklevali objaviti pod drugim imenom, če naredimo napako ali če to zahtevajo okoliščine."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Razširjanje besede je lahko težavno. Kot smo rekli, je to še vedno nišna skupnost. Prvotno smo objavili na Redditu, vendar smo resnično pridobili pozornost na Hacker News. Za zdaj je naša priporočilo, da objavite na nekaj mestih in vidite, kaj se zgodi. In spet, kontaktirajte nas. Radi bi širili besedo o več piratskih arhivskih prizadevanjih."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Zaključek"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Upamo, da je to v pomoč novim piratskim arhivistom. Veselimo se, da vas lahko pozdravimo v tem svetu, zato ne oklevajte in se obrnite na nas. Ohranjajmo čim več svetovnega znanja in kulture ter ga razširimo daleč naokoli."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Predstavljamo Piratsko knjižnično zrcalo: Ohranjanje 7TB knjig (ki niso v Libgenu)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Ta projekt (UREDI: preseljen na <a %(wikipedia_annas_archive)s>Annin arhiv</a>) si prizadeva prispevati k ohranjanju in osvoboditvi človeškega znanja. Prispevamo svoj majhen in skromen prispevek, po stopinjah velikih pred nami."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Osredotočenost tega projekta je ponazorjena z njegovim imenom:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piratsko</strong> - Namerno kršimo zakon o avtorskih pravicah v večini držav. To nam omogoča, da naredimo nekaj, česar pravne osebe ne morejo: zagotoviti, da so knjige zrcaljene daleč naokoli."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Knjižnica</strong> - Tako kot večina knjižnic se osredotočamo predvsem na pisne materiale, kot so knjige. Morda se bomo v prihodnosti razširili na druge vrste medijev."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Zrcalo</strong> - Smo strogo zrcalo obstoječih knjižnic. Osredotočamo se na ohranjanje, ne na omogočanje enostavnega iskanja in prenosa knjig (dostop) ali spodbujanje velike skupnosti ljudi, ki prispevajo nove knjige (pridobivanje)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Prva knjižnica, ki smo jo zrcalili, je Z-Knjižnica. To je priljubljena (in nezakonita) knjižnica. Vzeli so zbirko Library Genesis in jo naredili enostavno iskalno. Poleg tega so postali zelo učinkoviti pri pridobivanju novih prispevkov knjig, tako da spodbujajo uporabnike, ki prispevajo, z različnimi ugodnostmi. Trenutno teh novih knjig ne prispevajo nazaj v Library Genesis. In za razliko od Library Genesis, ne omogočajo enostavnega zrcaljenja svoje zbirke, kar preprečuje široko ohranjanje. To je pomembno za njihov poslovni model, saj zaračunavajo denar za dostop do svoje zbirke v večjih količinah (več kot 10 knjig na dan)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Ne izrekamo moralnih sodb o zaračunavanju denarja za množični dostop do nezakonite zbirke knjig. Nedvomno je, da je Z-Library uspešno razširila dostop do znanja in pridobila več knjig. Tukaj smo preprosto zato, da opravimo svoj del: zagotovimo dolgoročno ohranitev te zasebne zbirke."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Radi bi vas povabili, da pomagate ohranjati in osvobajati človeško znanje z nalaganjem in sejanjem naših torrentov. Za več informacij o tem, kako so podatki organizirani, si oglejte stran projekta."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Zelo bi vas radi povabili, da prispevate svoje ideje o tem, katere zbirke naj zrcalimo naslednje in kako to storiti. Skupaj lahko dosežemo veliko. To je le majhen prispevek med neštetimi drugimi. Hvala vam, za vse, kar počnete."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Ne povezujemo se na datoteke s tega bloga. Prosimo, poiščite jih sami.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb izpis, ali Koliko knjig je ohranjenih za vedno?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Če bi pravilno deduplicirali datoteke iz senčnih knjižnic, kakšen odstotek vseh knjig na svetu smo ohranili?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "S Piratskim knjižničnim zrcalom (UREDI: preseljen na <a %(wikipedia_annas_archive)s>Annin arhiv</a>) je naš cilj vzeti vse knjige na svetu in jih ohraniti za vedno.<sup>1</sup> Med našimi Z-Knjižničnimi torrenti in originalnimi Library Genesis torrenti imamo 11.783.153 datotek. Toda koliko je to v resnici? Če bi pravilno deduplicirali te datoteke, kakšen odstotek vseh knjig na svetu smo ohranili? Resnično bi si želeli imeti nekaj takega:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of človeške pisne dediščine ohranjene za vedno"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Za odstotek potrebujemo imenovalec: skupno število vseh kdajkoli objavljenih knjig.<sup>2</sup> Pred propadom Google Books je inženir na projektu, Leonid Taycher, <a %(booksearch_blogspot)s>poskušal oceniti</a> to število. Prišel je — v šali — do 129.864.880 (“vsaj do nedelje”). To število je ocenil z gradnjo enotne baze podatkov vseh knjig na svetu. Za to je združil različne Datasets in jih nato združil na različne načine."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Kot zanimivost, obstaja še ena oseba, ki je poskušala katalogizirati vse knjige na svetu: Aaron Swartz, pokojni digitalni aktivist in soustanovitelj Reddita.<sup>3</sup> On je <a %(youtube)s>začel Open Library</a> z namenom \"ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena\", združevanje podatkov iz različnih virov. Na koncu je plačal najvišjo ceno za svoje delo na področju digitalnega ohranjanja, ko je bil preganjan zaradi množičnega prenosa akademskih člankov, kar je vodilo v njegov samomor. Seveda je to eden od razlogov, zakaj je naša skupina psevdonimna in zakaj smo zelo previdni. Open Library še vedno herojsko vodijo ljudje pri Internet Archive, nadaljujejo Aaronovo zapuščino. K temu se bomo vrnili kasneje v tej objavi."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "V blogu na Googlu Taycher opisuje nekatere izzive pri ocenjevanju te številke. Najprej, kaj šteje kot knjiga? Obstaja nekaj možnih definicij:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fizične kopije.</strong> Očitno to ni zelo koristno, saj so le dvojniki istega gradiva. Bilo bi super, če bi lahko ohranili vse opombe, ki jih ljudje naredijo v knjigah, kot so Fermatove slavne \"črke na robovih\". A žal, to bo ostalo sanje arhivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Dela”.</strong> Na primer \"Harry Potter in Dvorana skrivnosti\" kot logični koncept, ki zajema vse njegove različice, kot so različni prevodi in ponatisi. To je nekako uporabna definicija, vendar je težko določiti mejo, kaj šteje. Na primer, verjetno želimo ohraniti različne prevode, čeprav ponatisi z le manjšimi razlikami morda niso tako pomembni."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Izdaje”.</strong> Tukaj štejete vsako edinstveno različico knjige. Če je karkoli drugače, kot na primer drugačna naslovnica ali drugačen predgovor, šteje kot druga izdaja."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Datoteke.</strong> Pri delu s senčnimi knjižnicami, kot so Library Genesis, Sci-Hub ali Z-Library, je treba upoštevati še nekaj. Lahko obstaja več skenov iste izdaje. In ljudje lahko ustvarijo boljše različice obstoječih datotek, tako da skenirajo besedilo z OCR ali popravijo strani, ki so bile skenirane pod kotom. Želimo šteti te datoteke kot eno izdajo, kar bi zahtevalo dobro metadata ali deduplikacijo z uporabo meril podobnosti dokumentov."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Izdaje” se zdijo najbolj praktična definicija, kaj so “knjige”. Ta definicija se priročno uporablja tudi za dodeljevanje edinstvenih ISBN številk. ISBN ali mednarodna standardna knjižna številka se pogosto uporablja za mednarodno trgovino, saj je integrirana z mednarodnim sistemom črtnih kod (\"Mednarodna številka artikla\"). Če želite prodajati knjigo v trgovinah, potrebuje črtno kodo, zato dobite ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycherjeva objava na blogu omenja, da čeprav so ISBN-ji koristni, niso univerzalni, saj so bili resnično sprejeti šele sredi sedemdesetih let in ne povsod po svetu. Kljub temu je ISBN verjetno najpogosteje uporabljen identifikator knjižnih izdaj, zato je to naša najboljša izhodiščna točka. Če lahko najdemo vse ISBN-je na svetu, dobimo uporaben seznam knjig, ki jih je še treba ohraniti."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Torej, kje dobimo podatke? Obstaja več obstoječih prizadevanj, ki poskušajo sestaviti seznam vseh knjig na svetu:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Navsezadnje so opravili to raziskavo za Google Books. Vendar njihovi metadata niso dostopni v velikem obsegu in jih je precej težko strgati."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Kot že omenjeno, je to njihovo celotno poslanstvo. Zbrali so ogromne količine knjižničnih podatkov iz sodelujočih knjižnic in nacionalnih arhivov ter to še naprej počnejo. Imajo tudi prostovoljne knjižničarje in tehnično ekipo, ki poskušajo deduplicirati zapise in jih označiti z vsemi vrstami metadata. Najboljše od vsega je, da je njihov nabor podatkov popolnoma odprt. Preprosto ga lahko <a %(openlibrary)s>preneseš</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> To je spletna stran, ki jo vodi neprofitna organizacija OCLC, ki prodaja sisteme za upravljanje knjižnic. Združujejo knjižnične metadata iz številnih knjižnic in jih omogočajo prek spletne strani WorldCat. Vendar pa tudi zaslužijo s prodajo teh podatkov, zato niso na voljo za množični prenos. Imajo pa nekaj bolj omejenih množičnih naborov podatkov, ki so na voljo za prenos, v sodelovanju z določenimi knjižnicami."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> To je tema te objave na blogu. ISBNdb strga različne spletne strani za knjižne metadata, zlasti podatke o cenah, ki jih nato prodajajo prodajalcem knjig, da lahko svoje knjige cenijo v skladu s preostalim trgom. Ker so ISBN-ji danes precej univerzalni, so učinkovito zgradili \"spletno stran za vsako knjigo\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Različni posamezni knjižnični sistemi in arhivi.</strong> Obstajajo knjižnice in arhivi, ki niso bili indeksirani in združeni z nobenim od zgoraj navedenih, pogosto zato, ker so podfinancirani ali iz drugih razlogov ne želijo deliti svojih podatkov z Open Library, OCLC, Google in tako naprej. Veliko teh ima digitalne zapise, dostopne prek interneta, in pogosto niso zelo dobro zaščiteni, zato, če želite pomagati in se zabavati pri učenju o nenavadnih knjižničnih sistemih, so to odlične izhodiščne točke."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "V tej objavi z veseljem napovedujemo majhno izdajo (v primerjavi z našimi prejšnjimi izdajami Z-Library). Strgali smo večino ISBNdb in podatke omogočili za prenos prek torrentov na spletni strani Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>; tukaj ne bomo neposredno povezali, samo poiščite ga). To je približno 30,9 milijona zapisov (20 GB kot <a %(jsonlines)s>JSON Lines</a>; 4,4 GB stisnjeno). Na njihovi spletni strani trdijo, da imajo dejansko 32,6 milijona zapisov, zato smo morda nekako zgrešili nekaj, ali pa <em>oni</em> delajo nekaj narobe. V vsakem primeru za zdaj ne bomo delili, kako smo to storili — to bomo pustili kot vajo za bralca. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Kar bomo delili, je nekaj predhodne analize, da bi se približali oceni števila knjig na svetu. Pregledali smo tri nabore podatkov: ta novi nabor podatkov ISBNdb, našo prvotno izdajo metadata, ki smo jih strgali iz senčne knjižnice Z-Library (ki vključuje Library Genesis), in podatkovni dump Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Začnimo z nekaj grobimi številkami:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "V obeh Z-Library/Libgen in Open Library je veliko več knjig kot edinstvenih ISBN-jev. Ali to pomeni, da veliko teh knjig nima ISBN-jev, ali pa preprosto manjkajo metadata ISBN? Na to vprašanje lahko verjetno odgovorimo s kombinacijo avtomatiziranega ujemanja na podlagi drugih atributov (naslov, avtor, založnik itd.), vključevanjem več virov podatkov in izvlečenjem ISBN-jev iz dejanskih skenov knjig (v primeru Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Koliko teh ISBN-jev je edinstvenih? To je najbolje ponazoriti z Vennovim diagramom:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Da bi bili bolj natančni:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Presenetili smo se, kako malo prekrivanja obstaja! ISBNdb ima ogromno število ISBN-jev, ki se ne pojavijo niti v Z-Library niti v Open Library, in enako velja (v manjši, a še vedno pomembni meri) za drugi dve. To odpira veliko novih vprašanj. Koliko bi avtomatizirano ujemanje pomagalo pri označevanju knjig, ki niso bile označene z ISBN-ji? Bi bilo veliko ujemanj in s tem povečano prekrivanje? Prav tako, kaj bi se zgodilo, če bi vključili 4. ali 5. nabor podatkov? Koliko prekrivanja bi videli takrat?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "To nam daje izhodišče. Zdaj lahko pogledamo vse ISBN-je, ki niso bili v naboru podatkov Z-Library, in ki se ne ujemajo niti z naslovom/avtorjem. To nam lahko pomaga pri ohranjanju vseh knjig na svetu: najprej z iskanjem skenov po internetu, nato pa z dejanskim skeniranjem knjig v resničnem življenju. Slednje bi lahko celo financirali množično, ali pa bi ga spodbudili z \"nagradami\" ljudi, ki bi želeli videti določene knjige digitalizirane. Vse to je zgodba za drugič."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Če želite pomagati pri katerem koli od tega — nadaljnja analiza; iskanje več metadata; iskanje več knjig; OCR knjig; izvajanje tega za druga področja (npr. članki, avdio knjige, filmi, TV oddaje, revije) ali celo omogočanje dostopa do nekaterih teh podatkov za stvari, kot so ML / usposabljanje velikih jezikovnih modelov — me prosim kontaktirajte (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Če vas posebej zanima analiza podatkov, delamo na tem, da bi naši nabori podatkov in skripti postali dostopni v bolj enostavni obliki. Bilo bi super, če bi lahko preprosto razdelili zvezek in začeli raziskovati."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Nazadnje, če želite podpreti to delo, prosimo, razmislite o donaciji. To je popolnoma prostovoljno vodena operacija, in vaš prispevek naredi veliko razliko. Vsak prispevek pomaga. Trenutno sprejemamo donacije v kriptovalutah; glejte stran Donacije na Anninem Arhivu."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Za neko razumno definicijo \"za vedno\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Seveda je pisna dediščina človeštva veliko več kot le knjige, še posebej danes. Zaradi tega prispevka in naših nedavnih izdaj se osredotočamo na knjige, vendar se naši interesi raztezajo dlje."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. O Aaronu Swartzu je mogoče povedati veliko več, vendar smo ga želeli le na kratko omeniti, saj igra ključno vlogo v tej zgodbi. Sčasoma bo več ljudi morda prvič naletelo na njegovo ime in se nato sami poglobili v to temo."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Kritično obdobje senčnih knjižnic"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Kako lahko trdimo, da bomo naše zbirke ohranili za vedno, ko pa že dosegajo 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Kitajska različica 中文版</a>, razpravljajte na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Na Anninem Arhivu nas pogosto sprašujejo, kako lahko trdimo, da bomo naše zbirke ohranili za vedno, ko pa skupna velikost že dosega 1 petabajt (1000 TB) in še vedno raste. V tem članku bomo pogledali našo filozofijo in videli, zakaj je naslednje desetletje ključno za našo misijo ohranjanja človeškega znanja in kulture."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Skupna velikost</a> naših zbirk v zadnjih nekaj mesecih, razčlenjena po številu torrent sejalcev."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritete"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Zakaj nam je tako mar za članke in knjige? Pustimo ob strani naše temeljno prepričanje o ohranjanju na splošno — o tem bi lahko napisali še en prispevek. Zakaj torej članki in knjige posebej? Odgovor je preprost: <strong>gostota informacij</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Na megabajt shranjevanja pisano besedilo shrani največ informacij med vsemi mediji. Medtem ko nam je mar za znanje in kulturo, nam je za prvo bolj mar. Na splošno najdemo hierarhijo gostote informacij in pomembnosti ohranjanja, ki je videti približno takole:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademski članki, revije, poročila"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organski podatki, kot so DNK sekvence, semena rastlin ali mikrobni vzorci"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Strokovne knjige"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Znanstvena in inženirska programska koda"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Merilni podatki, kot so znanstvene meritve, ekonomski podatki, korporativna poročila"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Znanstvena in inženirska spletna mesta, spletne razprave"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revije, časopisi, priročniki o nefikciji"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Prepisi govorov, dokumentarcev, podcastov o nefikciji"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Notranji podatki podjetij ali vlad (puščanja)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Zapiski o metadata na splošno (o nefikciji in fikciji; o drugih medijih, umetnosti, ljudeh itd.; vključno z ocenami)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografski podatki (npr. zemljevidi, geološke raziskave)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Prepisi pravnih ali sodnih postopkov"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fikcijske ali zabavne različice vsega zgoraj naštetega"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Razvrstitev na tem seznamu je nekoliko arbitrarna — več elementov je izenačenih ali pa se naša ekipa ne strinja — in verjetno pozabljamo na nekatere pomembne kategorije. Vendar je to približno, kako dajemo prednost."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Nekateri od teh elementov so preveč različni od drugih, da bi nas skrbelo (ali pa so že poskrbljeni s strani drugih institucij), kot so organski podatki ali geografski podatki. Vendar je večina elementov na tem seznamu dejansko pomembna za nas."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Drug velik dejavnik pri naši prioritizaciji je, koliko je določeno delo ogroženo. Raje se osredotočamo na dela, ki so:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Redka"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Edinstveno zapostavljena"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Edinstveno ogrožena uničenja (npr. zaradi vojne, zmanjšanja financiranja, tožb ali političnega preganjanja)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Na koncu nam je pomemben obseg. Imamo omejen čas in denar, zato bi raje porabili mesec dni za reševanje 10.000 knjig kot 1.000 knjig — če so približno enako dragocene in ogrožene."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Sence knjižnic"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Obstaja veliko organizacij s podobnimi misijami in podobnimi prioritetami. Dejansko obstajajo knjižnice, arhivi, laboratoriji, muzeji in druge institucije, zadolžene za tovrstno ohranjanje. Mnoge od teh so dobro financirane, s strani vlad, posameznikov ali podjetij. Vendar imajo eno veliko slepo točko: pravni sistem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Tukaj leži edinstvena vloga senčnih knjižnic in razlog, zakaj obstaja Annin Arhiv. Lahko počnemo stvari, ki jih drugim institucijam ni dovoljeno. Zdaj, ni (pogosto) tako, da lahko arhiviramo materiale, ki jih drugje ni dovoljeno ohranjati. Ne, v mnogih krajih je zakonito zgraditi arhiv s katerimikoli knjigami, članki, revijami in tako naprej."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Toda pravna arhiva pogosto primanjkuje <strong>redundance in dolgotrajnosti</strong>. Obstajajo knjige, od katerih obstaja le en izvod v neki fizični knjižnici nekje. Obstajajo zapisi o metadata, ki jih varuje samo eno podjetje. Obstajajo časopisi, ki so ohranjeni le na mikrofilmu v enem arhivu. Knjižnice lahko doživijo zmanjšanje financiranja, podjetja lahko bankrotirajo, arhivi pa so lahko bombardirani in požgani do tal. To ni hipotetično — to se dogaja ves čas."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Kar lahko edinstveno storimo v Anninem arhivu, je shranjevanje številnih kopij del v velikem obsegu. Lahko zbiramo članke, knjige, revije in še več ter jih distribuiramo v velikih količinah. Trenutno to počnemo prek torrentov, vendar natančne tehnologije niso pomembne in se bodo sčasoma spreminjale. Pomembno je, da se številne kopije razdelijo po vsem svetu. Ta citat izpred več kot 200 let še vedno drži:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Izgubljenega ni mogoče povrniti; vendar rešimo, kar ostane: ne z zakladi in ključavnicami, ki jih varujejo pred očmi in uporabo javnosti, s tem da jih prepustimo zobu časa, temveč z množičnim razmnoževanjem kopij, ki jih postavi izven dosega nesreče.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Kratka opomba o javni domeni. Ker se Annin arhiv edinstveno osredotoča na dejavnosti, ki so nezakonite v mnogih delih sveta, se ne ukvarjamo s široko dostopnimi zbirkami, kot so knjige v javni domeni. Pravne osebe pogosto že dobro skrbijo za to. Vendar pa obstajajo premisleki, zaradi katerih včasih delamo na javno dostopnih zbirkah:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Zapise o metadata je mogoče prosto pregledovati na spletni strani Worldcat, vendar jih ni mogoče prenesti v velikem obsegu (dokler jih nismo <a %(worldcat_scrape)s>strgali</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Koda je lahko odprtokodna na Githubu, vendar Github kot celota ne more biti enostavno zrcaljen in tako ohranjen (čeprav v tem primeru obstajajo dovolj razširjene kopije večine repozitorijev kode)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit je brezplačen za uporabo, vendar je nedavno uvedel stroge ukrepe proti strganju, zaradi podatkovno lačnih LLM treningov (več o tem kasneje)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Množično razmnoževanje kopij"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Nazaj k našemu prvotnemu vprašanju: kako lahko trdimo, da bomo naše zbirke ohranili za vedno? Glavni problem tukaj je, da se je naša zbirka <a %(torrents_stats)s>hitro povečevala</a> z zbiranjem in odprtokodnim deljenjem nekaterih velikih zbirk (poleg neverjetnega dela, ki so ga že opravile druge knjižnice s prostimi podatki, kot sta Sci-Hub in Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Ta rast podatkov otežuje zrcaljenje zbirk po vsem svetu. Shranjevanje podatkov je drago! Vendar smo optimistični, še posebej ob opazovanju naslednjih treh trendov."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Pobirali smo nizko viseče sadove"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "To neposredno sledi iz naših zgoraj obravnavanih prioritet. Raje delamo na osvobajanju velikih zbirk najprej. Zdaj, ko smo si zagotovili nekatere največje zbirke na svetu, pričakujemo, da bo naša rast veliko počasnejša."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Še vedno obstaja dolg rep manjših zbirk, in nove knjige se skenirajo ali objavljajo vsak dan, vendar bo stopnja verjetno veliko počasnejša. Morda se bomo še vedno podvojili ali celo potrojili v velikosti, vendar v daljšem časovnem obdobju."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Stroški shranjevanja se še naprej eksponentno znižujejo"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "V času pisanja so <a %(diskprices)s>cene diskov</a> na TB približno 12 $ za nove diske, 8 $ za rabljene diske in 4 $ za trak. Če smo konservativni in gledamo samo na nove diske, to pomeni, da shranjevanje petabajta stane približno 12.000 $. Če predpostavimo, da se bo naša knjižnica potrojila z 900 TB na 2,7 PB, bi to pomenilo 32.400 $ za zrcaljenje celotne knjižnice. Če dodamo elektriko, stroške druge strojne opreme in tako naprej, zaokrožimo na 40.000 $. Ali s trakom bolj kot 15.000–20.000 $."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Po eni strani <strong>15.000–40.000 $ za vsoto vsega človeškega znanja je ugodno</strong>. Po drugi strani pa je nekoliko strmo pričakovati tone popolnih kopij, še posebej, če bi želeli, da ti ljudje še naprej delijo svoje torrente v korist drugih."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "To je danes. Toda napredek koraka naprej:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Stroški trdih diskov na TB so bili v zadnjih 10 letih približno prepolovljeni in bodo verjetno še naprej padali s podobnim tempom. Trak se zdi na podobni poti. Cene SSD-jev padajo še hitreje in bi lahko do konca desetletja prehitele cene HDD-jev."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Trendi cen HDD iz različnih virov (kliknite za ogled študije)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Če to drži, bi lahko čez 10 let gledali na samo 5.000–13.000 $ za zrcaljenje celotne zbirke (1/3), ali še manj, če bomo manj rasli v velikosti. Čeprav je to še vedno veliko denarja, bo to dosegljivo za mnoge ljudi. In morda bo še bolje zaradi naslednje točke…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Izboljšave v gostoti informacij"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Trenutno shranjujemo knjige v surovih formatih, kot so nam bile posredovane. Seveda so stisnjene, vendar so pogosto še vedno veliki skeni ali fotografije strani."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Do zdaj so bile edine možnosti za zmanjšanje skupne velikosti naše zbirke bolj agresivna kompresija ali deduplikacija. Vendar pa sta obe možnosti preveč izgubljivi za naš okus, da bi dosegli dovolj velike prihranke. Močna kompresija fotografij lahko naredi besedilo komaj berljivo. Dedupikacija pa zahteva visoko zaupanje, da so knjige popolnoma enake, kar je pogosto preveč netočno, še posebej, če je vsebina enaka, vendar so skeni narejeni ob različnih priložnostih."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Vedno je obstajala tretja možnost, vendar je bila njena kakovost tako obupna, da je nismo nikoli upoštevali: <strong>OCR ali optično prepoznavanje znakov</strong>. To je proces pretvorbe fotografij v navadno besedilo z uporabo umetne inteligence za prepoznavanje znakov na fotografijah. Orodja za to obstajajo že dolgo in so bila precej spodobna, vendar \"precej spodobno\" ni dovolj za namene ohranjanja."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Vendar so nedavni multimodalni modeli globokega učenja dosegli izjemno hiter napredek, čeprav še vedno z visokimi stroški. Pričakujemo, da se bosta natančnost in stroški v prihodnjih letih dramatično izboljšala, do točke, ko bo postalo realno uporabiti to na celotno našo knjižnico."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Izboljšave OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Ko se to zgodi, bomo verjetno še vedno ohranili izvirne datoteke, vendar bi lahko poleg tega imeli veliko manjšo različico naše knjižnice, ki bi jo večina ljudi želela zrcaliti. Ključna točka je, da se surovo besedilo še bolje stisne in je veliko lažje za deduplikacijo, kar nam prinaša še več prihrankov."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Na splošno ni nerealno pričakovati vsaj 5- do 10-kratno zmanjšanje skupne velikosti datotek, morda celo več. Tudi pri konservativnem 5-kratnem zmanjšanju bi gledali na <strong>1.000–3.000 $ v 10 letih, tudi če se naša knjižnica potroji po velikosti</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritično obdobje"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Če so te napovedi točne, moramo <strong>samo počakati nekaj let</strong>, preden bo naša celotna zbirka široko zrcaljena. Tako bo, po besedah Thomasa Jeffersona, \"postavljena izven dosega nesreče.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Na žalost je pojav LLM-jev in njihovega podatkovno lačnega učenja veliko imetnikov avtorskih pravic postavil v obrambni položaj. Še bolj kot so že bili. Mnogi spletni strani otežujejo strganje in arhiviranje, tožbe letijo naokoli, medtem pa fizične knjižnice in arhivi še naprej ostajajo zanemarjeni."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Lahko pričakujemo, da se bodo ti trendi še poslabšali, in da bo veliko del izgubljenih, preden bodo vstopila v javno domeno."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Smo na pragu revolucije v ohranjanju, vendar <q>izgubljenega ni mogoče povrniti.</q></strong> Imamo kritično obdobje približno 5-10 let, v katerem je še vedno precej drago upravljati senčno knjižnico in ustvariti veliko zrcal po svetu, in v katerem dostop še ni popolnoma zaprt."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Če lahko premostimo to obdobje, bomo resnično ohranili človeško znanje in kulturo za vedno. Ne smemo dovoliti, da ta čas gre v nič. Ne smemo dovoliti, da se to kritično obdobje zapre pred nami."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Gremo."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ekskluziven dostop za podjetja LLM do največje zbirke kitajskih nefikcijskih knjig na svetu"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Kitajska različica 中文版</a>, <a %(news_ycombinator)s>Razpravljaj na Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Annin Arhiv je pridobil edinstveno zbirko 7,5 milijona / 350TB kitajskih nefikcijskih knjig — večjo kot Library Genesis. Pripravljeni smo dati podjetju LLM ekskluziven dostop v zameno za visokokakovostno OCR in ekstrakcijo besedila.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "To je kratek blog prispevek. Iščemo podjetje ali institucijo, ki bi nam pomagala z OCR in ekstrakcijo besedila za ogromno zbirko, ki smo jo pridobili, v zameno za ekskluziven zgodnji dostop. Po obdobju embarga bomo seveda izdali celotno zbirko."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Visokokakovostno akademsko besedilo je izjemno koristno za usposabljanje LLM-jev. Čeprav je naša zbirka kitajska, bi to moralo biti koristno tudi za usposabljanje angleških LLM-jev: modeli se zdijo, da kodirajo koncepte in znanje ne glede na izvorni jezik."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Za to je treba besedilo izvleči iz skenov. Kaj pridobi Annin arhiv iz tega? Iskanje po celotnem besedilu knjig za svoje uporabnike."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Ker so naši cilji usklajeni s cilji razvijalcev LLM, iščemo sodelavca. Pripravljeni smo vam dati <strong>ekskluziven zgodnji dostop do te zbirke v velikem obsegu za 1 leto</strong>, če lahko izvedete pravilno OCR in izvlečete besedilo. Če ste pripravljeni z nami deliti celotno kodo vašega postopka, smo pripravljeni podaljšati embargo na zbirko."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Primeri strani"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Da nam dokažete, da imate dober postopek, so tukaj nekaj primerov strani, s katerimi lahko začnete, iz knjige o superprevodnikih. Vaš postopek bi moral pravilno obravnavati matematiko, tabele, grafe, opombe in tako naprej."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Pošljite svoje obdelane strani na naš e-poštni naslov. Če bodo videti dobro, vam bomo zasebno poslali več, in pričakujemo, da boste lahko hitro izvedli svoj postopek tudi na teh. Ko bomo zadovoljni, lahko sklenemo dogovor."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Zbirka"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Nekaj več informacij o zbirki. <a %(duxiu)s>Duxiu</a> je ogromna baza podatkov skeniranih knjig, ki jo je ustvarila <a %(chaoxing)s>SuperStar Digital Library Group</a>. Večina so akademske knjige, skenirane, da bi jih digitalno omogočili univerzam in knjižnicam. Za našo angleško govorečo publiko imata <a %(library_princeton)s>Princeton</a> in <a %(guides_lib_uw)s>University of Washington</a> dobre preglede. Obstaja tudi odličen članek, ki daje več ozadja: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (poiščite ga v Anninem arhivu)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Knjige iz Duxiu so bile dolgo piratizirane na kitajskem internetu. Običajno jih preprodajalci prodajajo za manj kot dolar. Običajno se distribuirajo z uporabo kitajskega ekvivalenta Google Drive, ki je bil pogosto vdrt, da omogoča več prostora za shranjevanje. Nekatere tehnične podrobnosti najdete <a %(github_duty_machine)s>tukaj</a> in <a %(github_821_github_io)s>tukaj</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Čeprav so bile knjige poljavnostno distribuirane, jih je precej težko pridobiti v velikem obsegu. To smo imeli visoko na našem seznamu opravil in dodelili več mesecev polnega delovnega časa za to. Vendar pa se je pred kratkim neverjeten, izjemen in nadarjen prostovoljec obrnil na nas in nam povedal, da je že opravil vse to delo — z velikimi stroški. Delili so celotno zbirko z nami, ne da bi pričakovali karkoli v zameno, razen zagotovila dolgoročne ohranitve. Resnično izjemno. Strinjali so se, da prosijo za pomoč na ta način, da se zbirka OCR-ira."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Zbirka obsega 7.543.702 datotek. To je več kot Library Genesis nefikcija (približno 5,3 milijona). Skupna velikost datotek je približno 359TB (326TiB) v trenutni obliki."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Odprti smo za druge predloge in ideje. Samo kontaktirajte nas. Oglejte si Annin arhiv za več informacij o naših zbirkah, prizadevanjih za ohranitev in kako lahko pomagate. Hvala!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Opozorilo: ta objava na blogu je zastarela. Odločili smo se, da IPFS še ni pripravljen za glavni čas. Še vedno bomo povezovali datoteke na IPFS iz Anninega arhiva, ko bo mogoče, vendar jih ne bomo več gostili sami, niti ne priporočamo drugim, da zrcalijo z uporabo IPFS. Prosimo, oglejte si našo stran Torrents, če želite pomagati pri ohranjanju naše zbirke."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Pomagajte sejati Z-Library na IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Kako voditi senčno knjižnico: delovanje v Anninem arhivu"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Ni <q>AWS za senčne dobrodelne organizacije,</q> kako torej vodimo Annin arhiv?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Vodim <a %(wikipedia_annas_archive)s>Annin arhiv</a>, največji odprtokodni neprofitni iskalnik za <a %(wikipedia_shadow_library)s>senčne knjižnice</a>, kot so Sci-Hub, Library Genesis in Z-Library. Naš cilj je omogočiti enostaven dostop do znanja in kulture ter na koncu zgraditi skupnost ljudi, ki skupaj arhivirajo in ohranjajo <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>vse knjige na svetu</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "V tem članku bom pokazal, kako vodimo to spletno stran in edinstvene izzive, ki jih prinaša upravljanje spletne strani s spornim pravnim statusom, saj ni \"AWS za senčne dobrodelne organizacije\"."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Oglejte si tudi sestrski članek <a %(blog_how_to_become_a_pirate_archivist)s>Kako postati piratski arhivar</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Inovacijski žetoni"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Začnimo z našim tehnološkim skladom. Namenoma je dolgočasen. Uporabljamo Flask, MariaDB in ElasticSearch. To je dobesedno vse. Iskanje je v veliki meri rešen problem in ga ne nameravamo ponovno izumiti. Poleg tega moramo naše <a %(mcfunley)s>inovacijske žetone</a> porabiti za nekaj drugega: da nas oblasti ne odstranijo."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Kako zakonit ali nezakonit je torej pravzaprav Annin arhiv? To je večinoma odvisno od pravne jurisdikcije. Večina držav verjame v neko obliko avtorskih pravic, kar pomeni, da so ljudem ali podjetjem dodeljeni izključni monopol nad določenimi vrstami del za določeno obdobje. Mimogrede, v Anninem arhivu verjamemo, da čeprav obstajajo nekatere koristi, so avtorske pravice na splošno negativne za družbo — vendar je to zgodba za drugič."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ta izključni monopol nad določenimi deli pomeni, da je nezakonito, da kdorkoli zunaj tega monopola neposredno distribuira ta dela — vključno z nami. Vendar je Annin arhiv iskalnik, ki teh del ne distribuira neposredno (vsaj ne na naši spletni strani v jasnem omrežju), zato bi morali biti v redu, kajne? Ne ravno. V mnogih jurisdikcijah ni le nezakonito distribuirati avtorsko zaščitena dela, ampak tudi povezovati na mesta, ki to počnejo. Klasičen primer tega je ameriški zakon DMCA."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "To je najstrožji konec spektra. Na drugem koncu spektra bi teoretično lahko obstajale države brez kakršnih koli zakonov o avtorskih pravicah, vendar te v resnici ne obstajajo. Skoraj vsaka država ima v zakonih neko obliko avtorskih pravic. Izvrševanje je druga zgodba. Obstaja veliko držav z vladami, ki jih ne zanima izvrševanje zakonov o avtorskih pravicah. Obstajajo tudi države med obema skrajnostma, ki prepovedujejo distribucijo avtorsko zaščitenih del, vendar ne prepovedujejo povezovanja na taka dela."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Druga stvar, ki jo je treba upoštevati, je na ravni podjetja. Če podjetje deluje v jurisdikciji, ki ne skrbi za avtorske pravice, vendar samo podjetje ni pripravljeno tvegati, lahko zaprejo vašo spletno stran takoj, ko se kdo pritoži nad njo."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Nazadnje, velika skrb so plačila. Ker moramo ostati anonimni, ne moremo uporabljati tradicionalnih plačilnih metod. To nas pušča s kriptovalutami, in le majhen del podjetij jih podpira (obstajajo virtualne debetne kartice, plačane s kripto, vendar jih pogosto ne sprejemajo)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arhitektura sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Recimo, da ste našli nekaj podjetij, ki so pripravljena gostiti vašo spletno stran, ne da bi vas zaprli — imenujmo jih “ponudniki, ki ljubijo svobodo” 😄. Hitro boste ugotovili, da je gostovanje vsega pri njih precej drago, zato boste morda želeli najti nekaj “poceni ponudnikov” in dejansko gostovanje opraviti tam, s posredovanjem prek ponudnikov, ki ljubijo svobodo. Če to storite pravilno, poceni ponudniki nikoli ne bodo vedeli, kaj gostite, in nikoli ne bodo prejeli nobenih pritožb."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Pri vseh teh ponudnikih obstaja tveganje, da vas vseeno zaprejo, zato potrebujete tudi redundanco. To potrebujemo na vseh ravneh našega sklada."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Ena nekoliko svobodoljubna družba, ki se je postavila v zanimiv položaj, je Cloudflare. Trdili so, da niso ponudnik gostovanja, ampak pripomoček, kot je ISP. Zato niso podvrženi zahtevam za odstranitev po DMCA ali drugih zahtevah in posredujejo vse zahteve vašemu dejanskemu ponudniku gostovanja. Šli so celo tako daleč, da so šli na sodišče, da bi zaščitili to strukturo. Zato jih lahko uporabimo kot še eno plast predpomnjenja in zaščite."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare ne sprejema anonimnih plačil, zato lahko uporabljamo le njihov brezplačni načrt. To pomeni, da ne moremo uporabljati njihovih funkcij za uravnoteženje obremenitve ali preklop v primeru napake. Zato smo to <a %(annas_archive_l255)s>implementirali sami</a> na ravni domene. Ob nalaganju strani bo brskalnik preveril, ali je trenutna domena še vedno na voljo, in če ni, prepiše vse URL-je na drugo domeno. Ker Cloudflare predpomni veliko strani, to pomeni, da lahko uporabnik pristane na naši glavni domeni, tudi če je proxy strežnik izklopljen, in nato ob naslednjem kliku preide na drugo domeno."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Še vedno imamo tudi običajne operativne skrbi, kot so spremljanje zdravja strežnikov, beleženje napak v ozadju in sprednjem delu in tako naprej. Naša arhitektura preklopa v primeru napake omogoča večjo robustnost tudi na tem področju, na primer z zagonom popolnoma drugačnega nabora strežnikov na eni od domen. Na tej ločeni domeni lahko celo poganjamo starejše različice kode in podatkovnih nizov, v primeru da kritična napaka v glavni različici ostane neopažena."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Lahko se tudi zaščitimo pred tem, da bi se Cloudflare obrnil proti nam, tako da ga odstranimo z ene od domen, kot je ta ločena domena. Možne so različne permutacije teh idej."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Orodja"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Poglejmo, katera orodja uporabljamo za dosego vsega tega. To se zelo razvija, ko naletimo na nove težave in najdemo nove rešitve."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Aplikacijski strežnik: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy strežnik: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Upravljanje strežnikov: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Razvoj: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Statično gostovanje Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Obstajajo nekatere odločitve, pri katerih smo se večkrat premislili. Ena izmed njih je komunikacija med strežniki: prej smo za to uporabljali Wireguard, vendar smo ugotovili, da občasno preneha prenašati podatke ali pa jih prenaša le v eno smer. To se je zgodilo pri več različnih nastavitvah Wireguard, ki smo jih preizkusili, kot sta <a %(github_costela_wesher)s>wesher</a> in <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Prav tako smo poskusili tunelirati porte preko SSH, z uporabo autossh in sshuttle, vendar smo naleteli na <a %(github_sshuttle)s>težave tam</a> (čeprav mi še vedno ni jasno, ali autossh trpi zaradi težav TCP-over-TCP ali ne — zdi se mi kot neurejena rešitev, vendar morda je v resnici v redu?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Namesto tega smo se vrnili k neposrednim povezavam med strežniki, pri čemer smo skrili, da strežnik deluje na poceni ponudnikih z uporabo IP-filtriranja z UFW. To ima pomanjkljivost, da Docker ne deluje dobro z UFW, razen če uporabite <code>network_mode: \"host\"</code>. Vse to je nekoliko bolj nagnjeno k napakam, saj boste z le majhno napačno konfiguracijo izpostavili svoj strežnik internetu. Morda bi se morali vrniti k autossh — povratne informacije bi bile tukaj zelo dobrodošle."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Prav tako smo se večkrat premislili glede Varnish proti Nginx. Trenutno nam je Varnish všeč, vendar ima svoje posebnosti in grobe robove. Enako velja za Checkmk: ni nam všeč, vendar za zdaj deluje. Weblate je bil v redu, vendar ne neverjeten — včasih se bojim, da bo izgubil moje podatke, kadar koli ga poskušam sinhronizirati z našim git repozitorijem. Flask je bil na splošno dober, vendar ima nekaj čudnih posebnosti, ki so zahtevale veliko časa za odpravljanje napak, kot so konfiguriranje prilagojenih domen ali težave z integracijo SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Do sedaj so bili drugi orodji odlični: nimamo resnih pritožb glede MariaDB, ElasticSearch, Gitlab, Zulip, Docker in Tor. Vsa ta orodja so imela nekaj težav, vendar nič preveč resnega ali časovno potratnega."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Zaključek"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Bila je zanimiva izkušnja naučiti se, kako vzpostaviti robusten in odporen iskalnik senčne knjižnice. Obstaja še veliko podrobnosti, ki jih bomo delili v prihodnjih objavah, zato mi sporočite, o čem bi želeli izvedeti več!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Kot vedno iščemo donacije za podporo temu delu, zato si oglejte stran Doniraj na Anninem Arhivu. Prav tako iščemo druge vrste podpore, kot so nepovratna sredstva, dolgoročni sponzorji, ponudniki plačil z visokim tveganjem, morda celo (okusni!) oglasi. In če želite prispevati svoj čas in veščine, vedno iščemo razvijalce, prevajalce in podobno. Hvala za vaše zanimanje in podporo."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Živjo, sem Anna. Ustvarila sem <a %(wikipedia_annas_archive)s>Annin Arhiv</a>, največjo senčno knjižnico na svetu. To je moj osebni blog, v katerem jaz in moji sodelavci pišemo o piratstvu, digitalnem ohranjanju in še več."

#, fuzzy
msgid "blog.index.text2"
msgstr "Povežite se z mano na <a %(reddit)s>Redditu</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Upoštevajte, da je ta spletna stran le blog. Tukaj gostimo le svoje besede. Tukaj niso gostene ali povezane nobene torrent datoteke ali druge datoteke, zaščitene z avtorskimi pravicami."

#, fuzzy
msgid "blog.index.heading"
msgstr "Objave na blogu"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 milijarde WorldCat strganje"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Postavljanje 5.998.794 knjig na IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Opozorilo: ta objava na blogu je zastarela. Odločili smo se, da IPFS še ni pripravljen za glavni čas. Še vedno bomo povezovali datoteke na IPFS iz Anninega arhiva, ko bo mogoče, vendar jih ne bomo več gostili sami, niti ne priporočamo drugim, da zrcalijo z uporabo IPFS. Prosimo, oglejte si našo stran Torrents, če želite pomagati pri ohranjanju naše zbirke."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Annin Arhiv je strgal celoten WorldCat (največjo zbirko knjižničnih metadata na svetu), da bi ustvaril seznam knjig, ki jih je treba ohraniti.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Pred letom dni smo <a %(blog)s>začeli</a> odgovarjati na to vprašanje: <strong>Kakšen odstotek knjig je bil trajno ohranjen s senčnimi knjižnicami?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Ko knjiga pride v senčno knjižnico z odprtimi podatki, kot je <a %(wikipedia_library_genesis)s>Library Genesis</a>, in zdaj <a %(wikipedia_annas_archive)s>Annin Arhiv</a>, se zrcali po vsem svetu (prek torrentov), s čimer se praktično ohrani za vedno."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Da bi odgovorili na vprašanje, kakšen odstotek knjig je bil ohranjen, moramo vedeti imenovalec: koliko knjig obstaja v celoti? In idealno ne le številko, ampak dejanske metadata. Nato jih lahko ne le primerjamo s senčnimi knjižnicami, ampak tudi <strong>ustvarimo seznam knjig, ki jih je treba še ohraniti!</strong> Lahko bi celo začeli sanjati o množičnem prizadevanju, da bi šli po tem seznamu."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Podatke smo pridobili iz <a %(wikipedia_isbndb_com)s>ISBNdb</a> in prenesli <a %(openlibrary)s>podatkovno zbirko Open Library</a>, vendar rezultati niso bili zadovoljivi. Glavni problem je bil, da ni bilo veliko prekrivanja ISBN-jev. Oglejte si ta Vennov diagram iz <a %(blog)s>naše objave na blogu</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Zelo nas je presenetilo, kako malo prekrivanja je bilo med ISBNdb in Open Library, ki oba obsežno vključujeta podatke iz različnih virov, kot so spletni strganji in knjižnične evidence. Če bi oba dobro opravljala svoje delo pri iskanju večine ISBN-jev, bi se njuni krogi zagotovo precej prekrivali ali pa bi bil eden podmnožica drugega. To nas je spodbudilo k razmišljanju, koliko knjig je <em>popolnoma zunaj teh krogov</em>? Potrebujemo večjo podatkovno zbirko."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Takrat smo se osredotočili na največjo podatkovno zbirko knjig na svetu: <a %(wikipedia_worldcat)s>WorldCat</a>. To je lastniška podatkovna zbirka neprofitne organizacije <a %(wikipedia_oclc)s>OCLC</a>, ki zbira metapodatke iz knjižnic po vsem svetu, v zameno za to, da tem knjižnicam omogoča dostop do celotne podatkovne zbirke in da se pojavijo v rezultatih iskanja končnih uporabnikov."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Čeprav je OCLC neprofitna organizacija, njihov poslovni model zahteva zaščito njihove podatkovne zbirke. No, žal nam je, prijatelji pri OCLC, mi jo bomo vseeno delili. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "V preteklem letu smo natančno pridobili vse zapise iz WorldCat. Na začetku smo imeli srečo. WorldCat je ravno uvajal popolno prenovo svoje spletne strani (avgust 2022). To je vključevalo obsežno prenovo njihovih zalednih sistemov, kar je uvedlo številne varnostne pomanjkljivosti. Takoj smo izkoristili priložnost in v nekaj dneh pridobili stotine milijonov (!) zapisov."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Prenova WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Po tem so bile varnostne pomanjkljivosti počasi odpravljene ena za drugo, dokler zadnja, ki smo jo našli, ni bila odpravljena pred približno mesecem dni. Do takrat smo imeli skoraj vse zapise in smo se osredotočali le na nekoliko kakovostnejše zapise. Zato smo menili, da je čas za izdajo!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Poglejmo nekaj osnovnih informacij o podatkih:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Zabojniki Arhiva Anne (AAC)</a>, ki so v bistvu <a %(jsonlines)s>JSON Lines</a> stisnjeni z <a %(zstd)s>Zstandard</a>, plus nekaj standardizirane semantike. Ti zabojniki vključujejo različne vrste zapisov, glede na različne strganje, ki smo jih izvedli."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Podatki"

msgid "dyn.buy_membership.error.unknown"
msgstr "Prišlo je do neznane napake. Pišite nam na %(email)s s posnetkom zaslona."

msgid "dyn.buy_membership.error.minimum"
msgstr "Ta kovanec ima višji minimum od običajnega. Izberite drugo trajanje ali drug kovanec."

msgid "dyn.buy_membership.error.try_again"
msgstr "Zahteve ni bilo mogoče dokončati. Prosimo, poskusite znova čez nekaj minut in če se to ponovi, nam pišite na %(email)s in nam priložite posnetek zaslona."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Napaka pri obdelavi plačila. Prosimo, počakajte trenutek in poskusite znova. Če težava traja več kot 24 ur, nas kontaktirajte na %(email)s s posnetkom zaslona."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "skriti komentar"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Težava z datoteko: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Boljša različica"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ali želite prijaviti tega uporabnika zaradi zlorabe ali neprimernega vedenja?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Prijavi zlorabo"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Zloraba prijavljena:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Prijavili ste tega uporabnika zaradi zlorabe."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Odgovori"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Prosimo, <a %(a_login)s>prijavite se</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Pustili ste komentar. Lahko traja nekaj minut, da se prikaže."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Nekaj je šlo narobe. Prosimo, ponovno naložite stran in poskusite znova."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s prizadete strani"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Ni vidno v Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Ni vidno v Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Ni vidno v Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Označeno kot pokvarjeno v Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Manjka v Z-knjižnici"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Označeno kot »spam« v Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Označeno kot »slaba datoteka« v Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nekaterih strani ni bilo mogoče pretvoriti v PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Zagon exiftool je na tej datoteki spodletel"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Knjiga (neznano)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Knjiga (neleposlovje)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Knjiga (leposlovje)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Članek v reviji"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standardni dokument"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revija"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Strip"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Glasbena partitura"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Avdioknjiga"

msgid "common.md5_content_type_mapping.other"
msgstr "Drugo"

msgid "common.access_types_mapping.aa_download"
msgstr "Prenos iz partnerskega strežnika"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Zunanji prenos"

msgid "common.access_types_mapping.external_borrow"
msgstr "Zunanja izposoja"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Zunanja izposoja (tisk onemogočen)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Raziščite metapodatke"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Vsebuje v torrentih"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Naloženo v AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Češki meta podatki"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Knjige"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Ruska državna knjižnica"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Naslov"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Avtor"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Založnik"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Izdaja"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Leto izdaje"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Izvirno ime datoteke"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Opis in meta podatki"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Prenosi s partnerskega strežnika za to datoteko začasno niso na voljo."

msgid "common.md5.servers.fast_partner"
msgstr "Hitri partnerski strežnik #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(priporočeno)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(brez preverjanja brskalnika ali čakalnih seznamov)"

msgid "common.md5.servers.slow_partner"
msgstr "Počasen partnerski strežnik #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(malo hitreje, vendar s čakalnim seznamom)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(brez čakalnega seznama, vendar lahko zelo počasi)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Neleposlovje"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Leposlovje"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(kliknite tudi \"GET\" na vrhu)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(kliknite \"GET\" na vrhu)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "njihovi oglasi so znani po zlonamerni programski opremi, zato uporabite blokator oglasov ali ne klikajte oglasov"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Datoteke Nexus/STC so lahko nezanesljive za prenos)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library na Tor omrežju"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(zahteva brskalnik Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Izposodi si iz Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(samo pokrovitelji z onemogočenim tiskanjem)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(povezani DOI morda ni na voljo v Sci-Hubu)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "zbirka"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Množični prenosi torrentov"

msgid "page.md5.box.download.experts_only"
msgstr "(samo strokovnjaki)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Poiščite ISBN v Aninem arhivu"

msgid "page.md5.box.download.other_isbn"
msgstr "Iščite po različnih drugih zbirkah podatkov za ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Poiščite izvirni zapis v ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "V Anninem arhivu poiščite Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Poiščite izvirni zapis v Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "V Aninem arhivu poiščite številko OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Poiščite izvirni zapis v WorldCat-u"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Išči po Anninem arhivu za DuXiu SSID številko"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Ročno iskanje na DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Išči po Arhivu Anne za številko CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Poišči originalni zapis v CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Išči po Arhivu Anne za DuXiu DXID številko"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Anin arhiv 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(preverjanje brskalnika ni potrebno)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Češki meta podatki %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Meta podatki"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "opis"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternativno ime datoteke"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternativni naslov"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternativni avtor"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternativni založnik"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternativna izdaja"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternativna razširitev"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "meta podatki komentarji"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternativni opis"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "datum odprtokodne objave"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub datoteka “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending datoteka “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "To je zapis datoteke iz Internet Archive in ne neposredno prenesena datoteka. Lahko si poskusite izposoditi knjigo (povezava spodaj) ali uporabite ta URL, ko <a %(a_request)s>zahtevate datoteko</a>."

msgid "page.md5.header.consider_upload"
msgstr "Če imate to datoteko in še ni na voljo v Aninem arhivu, jo <a %(a_request)s>naložite</a>."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s zapis meta podatkov"

msgid "page.md5.header.meta_openlib"
msgstr "Zapis knjižnice Open Library %(id)s meta podatkov"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) številka %(id)s zapis meta podatkov"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s meta podatki zapis"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s meta podatki zapis"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s meta podatki zapis"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s meta podatki zapis"

msgid "page.md5.header.meta_desc"
msgstr "To je meta podatkovni zapis in ne datoteka za prenos. Ta URL lahko uporabite, ko <a %(a_request)s>zahtevate datoteko</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Meta podatki iz povezanega zapisa"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Izboljšaj meta podatke na Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Opozorilo: več povezanih zapisov:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Izboljšaj meta podatke"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Prijavi kakovost datoteke"

msgid "page.search.results.download_time"
msgstr "Čas prenosa"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Spletna stran:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Poiščite »%(name)s« v Aninem arhivu"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Raziskovalec kod:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Poglej v Raziskovalcu kod “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Preberi več …"

msgid "page.md5.tabs.downloads"
msgstr "Prenosi (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Izposoja (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Raziščite meta podatke (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentarji (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Seznami (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistika (%(count)s)"

msgid "common.tech_details"
msgstr "Tehnične podrobnosti"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Ta datoteka ima morda težave in je bila skrita v izvorni knjižnici.</span> Včasih je to na zahtevo imetnika avtorskih pravic, včasih zato, ker je na voljo boljša alternativa, včasih pa je to zaradi težave s samo datoteko. Prenos morda še vedno ustreza, vendar priporočamo, da najprej poiščete drugo datoteko. Več podrobnosti:"

msgid "page.md5.box.download.better_file"
msgstr "Boljša različica te datoteke je morda na voljo na %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Če še vedno želite prenesti to datoteko, jo odprite samo z zaupanja vredno in posodobljeno programsko opremo."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Hitri prenosi"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Hitri prenosi</strong> Postanite <a %(a_membership)s>član</a> in podprite dolgoročno hrambo knjig, papirjev in drugega. V znak hvaležnosti za vašo podporo prejmete hitre prenose. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Če donirate ta mesec, dobite <strong>dvojno</strong> število hitrih prenosov."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Hitri prenosi</strong> Danes imate še %(remaining)s. Hvala, ker ste član! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Hitra prenosa</strong> Danes ste porabili vse hitre prenose."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Hitri prenosi</strong> To datoteko ste prenesli nedavno tega. Povezave veljajo še nekaj časa."

msgid "page.md5.box.download.option"
msgstr "Možnost #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(brez preusmeritve)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(odpri v pregledovalniku)"

msgid "layout.index.header.banner.refer"
msgstr "Priporočite prijatelja in tako vi kot vaš prijatelj prejmeta %(percentage)s%% bonus hitrih prenosov!"

msgid "layout.index.header.learn_more"
msgstr "Preberi več o tem…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Počasni prenosi"

msgid "page.md5.box.download.trusted_partners"
msgstr "Od zaupanja vrednih partnerjev."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Več informacij v <a %(a_slow)s>Pogosta vprašanja</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(morda zahteva <a %(a_browser)s>preverjanje brskalnika</a> — neomejeni prenosi!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Po prenosu:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Odpri v našem pregledovalniku"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "prikaži zunanje prenose"

msgid "page.md5.box.download.header_external"
msgstr "Zunanji prenosi"

msgid "page.md5.box.download.no_found"
msgstr "Ni najdenih prenosov."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Vse možnosti prenosa imajo isto datoteko in morajo biti varne za uporabo. Kljub temu bodite vedno previdni, ko prenašate datoteke iz interneta, zlasti s spletnih mest zunaj Anninega arhiva. Poskrbite tudi, da bodo vaše naprave posodobljene."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Za velike datoteke priporočamo uporabo upravitelja prenosov, da preprečite prekinitve."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Priporočeni upravitelji prenosov: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Za odpiranje datoteke boste potrebovali bralnik e-knjig ali PDF, odvisno od formata datoteke."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Priporočeni bralniki e-knjig: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Spletni pregledovalnik Anninega Arhiva"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Uporabite spletna orodja za pretvorbo med formati."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Priporočena orodja za pretvorbo: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Na svoj Kindle ali Kobo e-bralnik lahko pošljete tako PDF kot EPUB datoteke."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Priporočena orodja: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazonovo »Pošlji na Kindle«"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazzovo »Pošlji na Kobo/Kindle«"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Podprite avtorje in knjižnice"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Če vam je to všeč in si to lahko privoščite, razmislite o nakupu izvirnika ali neposredni podpori avtorjem."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Če je to na voljo v vaši lokalni knjižnici, razmislite o brezplačnem izposoji tam."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Kakovost datoteke"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Pomagajte skupnosti s prijavo kakovosti te datoteke! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Prijavi težavo z datoteko (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Odlična kakovost datoteke (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Dodaj komentar (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Kaj je narobe s to datoteko?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Prosimo, uporabite <a %(a_copyright)s>obrazec za DMCA / zahtevek za avtorske pravice</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Opišite težavo (obvezno)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Opis težave"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 boljše različice te datoteke (če je na voljo)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Izpolnite to, če obstaja druga datoteka, ki se tesno ujema s to datoteko (ista izdaja, ista pripona datoteke, če jo lahko najdete), ki bi jo ljudje morali uporabiti namesto te datoteke. Če poznate boljšo različico te datoteke zunaj Anninega arhiva, jo prosimo <a %(a_upload)s>naložite</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "MD5 lahko dobite iz URL-ja, npr."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Pošlji poročilo"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Naučite se, kako lahko <a %(a_metadata)s>izboljšate meta podatke</a> za to datoteko sami."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Hvala za oddajo vašega poročila. Prikazano bo na tej strani in ročno pregledano s strani Anne (dokler ne bomo imeli ustreznega sistema moderiranja)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Nekaj je šlo narobe. Prosimo, ponovno naložite stran in poskusite znova."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Če je ta datoteka visoke kakovosti, lahko tukaj razpravljate o njej! Če ni, uporabite gumb »Prijavi težavo z datoteko«."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Oboževal sem to knjigo!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Pustite komentar"

msgid "common.english_only"
msgstr "Besedilo spodaj se nadaljuje v angleščini."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Skupaj prenosov: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "»MD5 datoteke« je hash, ki se izračuna iz vsebine datoteke in je na podlagi te vsebine razmeroma edinstven. Vse sence knjižnic, ki smo jih tukaj indeksirali, primarno uporabljajo MD5 za identifikacijo datotek."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Datoteka se lahko pojavi v več sence knjižnicah. Za informacije o različnih datasets, ki smo jih sestavili, si oglejte <a %(a_datasets)s>stran Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "To je datoteka, ki jo upravlja knjižnica <a %(a_ia)s>IA’s Controlled Digital Lending</a> in jo je Anna’s Archive indeksirala za iskanje. Za informacije o različnih datasets, ki smo jih sestavili, si oglejte <a %(a_datasets)s>stran Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Za informacije o tej določeni datoteki si oglejte njen <a %(a_href)s>JSON datoteko</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Težava pri nalaganju te strani"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Osvežite stran in poskusite znova. <a %(a_contact)s>Kontaktirajte nas</a> če težava traja več ur."

msgid "page.md5.invalid.header"
msgstr "Ni najdeno"

msgid "page.md5.invalid.text"
msgstr "»%(md5_input)s« ni bilo mogoče najti v naši bazi podatkov."

msgid "page.login.title"
msgstr "Prijavite se / Registrirajte se"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Preverjanje brskalnika"

msgid "page.login.text1"
msgstr "Da preprečimo, da bi roboti za neželeno pošto ustvarili veliko računov, moramo najprej preveriti vaš brskalnik."

#, fuzzy
msgid "page.login.text2"
msgstr "Če se znajdete v neskončni zanki, priporočamo namestitev <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Pomaga lahko tudi, če izklopite blokado oglasov in druge razširitve brskalnika."

#, fuzzy
msgid "page.codes.title"
msgstr "Kode"

#, fuzzy
msgid "page.codes.heading"
msgstr "Raziskovalec kod"

#, fuzzy
msgid "page.codes.intro"
msgstr "Raziskujte kode, s katerimi so označeni zapisi, po predponi. Stolpec »zapisi« prikazuje število zapisov, označenih s kodami z dano predpono, kot je vidno v iskalniku (vključno z zapisi samo z meta podatki). Stolpec »kode« prikazuje, koliko dejanskih kod ima dano predpono."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Ta stran lahko traja nekaj časa za generiranje, zato zahteva Cloudflare captcha. <a %(a_donate)s>Člani</a> lahko preskočijo captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Prosimo, ne strgajte teh strani. Namesto tega priporočamo <a %(a_import)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov ter zagon naše <a %(a_software)s>odprtokodne kode</a>. Surove podatke lahko ročno raziskujete prek JSON datotek, kot je <a %(a_json_file)s>ta</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Predpona"

#, fuzzy
msgid "common.form.go"
msgstr "Pojdi"

#, fuzzy
msgid "common.form.reset"
msgstr "Ponastavi"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Išči po Anninem arhivu"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Opozorilo: koda vsebuje nepravilne Unicode znake in se lahko obnaša nepravilno v različnih situacijah. Surovi binarni podatki se lahko dekodirajo iz base64 predstavitve v URL-ju."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Znana predpona kode »%(key)s«"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Predpona"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Oznaka"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Opis"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL za določeno kodo"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "»%%s« bo nadomeščen z vrednostjo kode"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generični URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Spletna stran"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] "%(count)s zapisi, ki se ujemajo z “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL za določeno kodo: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Več…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kode, ki se začnejo z “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Kazalo"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "zapisi"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kode"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Manj kot %(count)s zapisov"

msgid "page.contact.dmca.form"
msgstr "Za zahtevke glede DMCA / avtorskih pravic uporabite <a %(a_copyright)s>ta obrazec</a>."

msgid "page.contact.dmca.delete"
msgstr "Vsi drugi načini stika z nami v zvezi z avtorskimi pravicami bodo samodejno izbrisani."

msgid "page.contact.checkboxes.text1"
msgstr "Zelo bomo veseli vaših povratnih informacij in vprašanj!"

msgid "page.contact.checkboxes.text2"
msgstr "Vendar pa zaradi količine neželene in nesmiselne e-pošte, ki jo prejemamo, označite polja, da potrdite, da razumete te pogoje za stik z nami."

msgid "page.contact.checkboxes.copyright"
msgstr "Zahtevki glede avtorskih pravic v tem e-poštnem sporočilu bodo prezrti; namesto tega uporabite obrazec."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partnerski strežniki niso na voljo zaradi zaprtja gostovanja. Kmalu bi morali biti spet dostopni."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Članstva bodo ustrezno podaljšana."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Ne pošiljajte nam e-pošte, da bi <a %(a_request)s>zahtevali knjige</a><br>ali majhne (<10k) <a %(a_upload)s>naložene vsebine</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Ko sprašujete o računu ali donacijah, dodajte svojo ID številko računa, posnetke zaslona, potrdila in čim več informacij. Na e-pošto odgovarjamo le vsakih 1-2 tedna, zato bo pomanjkanje teh informacij povzročilo zamudo pri reševanju."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Prikaži e-pošto"

#, fuzzy
msgid "page.copyright.title"
msgstr "Obrazec za prijavo kršitve avtorskih pravic / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Če imate zahtevek za kršitev avtorskih pravic ali DMCA, prosimo, da izpolnite ta obrazec čim bolj natančno. Če naletite na kakršne koli težave, nas kontaktirajte na našem posebnem DMCA naslovu: %(email)s. Upoštevajte, da zahtevki, poslani na ta naslov, ne bodo obdelani, namenjen je le za vprašanja. Prosimo, uporabite spodnji obrazec za oddajo svojih zahtevkov."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL-ji na Anninem arhivu (obvezno). En na vrstico. Prosimo, vključite samo URL-je, ki opisujejo točno isto izdajo knjige. Če želite vložiti zahtevek za več knjig ali več izdaj, prosimo, da ta obrazec oddate večkrat."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Zahtevki, ki združujejo več knjig ali izdaj, bodo zavrnjeni."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Vaše ime (obvezno)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Naslov (obvezno)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefonska številka (obvezno)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-pošta (obvezno)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Jasen opis izvornega gradiva (obvezno)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN-ji izvornega gradiva (če je primerno). En na vrstico. Prosimo, vključite samo tiste, ki natančno ustrezajo izdaji, za katero prijavljate kršitev avtorskih pravic."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL-ji izvornega gradiva, en na vrstico. Prosimo, vzemite si trenutek in poiščite svoje izvorno gradivo v Open Library. To nam bo pomagalo preveriti vaš zahtevek."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL-ji do izvornega gradiva, en na vrstico (obvezno). Prosimo, vključite čim več, da nam pomagate preveriti vaš zahtevek (npr. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Izjava in podpis (obvezno)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Oddaj zahtevek"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Hvala za oddajo vašega zahtevka za kršitev avtorskih pravic. Pregledali ga bomo čim prej. Prosimo, osvežite stran, da oddate novega."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Nekaj je šlo narobe. Prosimo, osvežite stran in poskusite znova."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Če vas zanima zrcaljenje tega nabora podatkov za <a %(a_archival)s>arhivske</a> ali <a %(a_llm)s>LLM trening</a> namene, nas prosimo kontaktirajte."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Naše poslanstvo je arhivirati vse knjige na svetu (kot tudi članke, revije itd.) in jih narediti široko dostopne. Verjamemo, da bi morale biti vse knjige zrcaljene široko in daleč, da se zagotovi redundanca in odpornost. Zato zbiramo datoteke iz različnih virov. Nekateri viri so popolnoma odprti in jih je mogoče zrcaliti v velikem obsegu (kot je Sci-Hub). Drugi so zaprti in zaščitniški, zato jih poskušamo strgati, da »osvobodimo« njihove knjige. Spet drugi so nekje vmes."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Vse naše podatke je mogoče <a %(a_torrents)s>torrenti</a>, in vse naše meta podatke je mogoče <a %(a_anna_software)s>generirati</a> ali <a %(a_elasticsearch)s>prenesti</a> kot ElasticSearch in MariaDB baze podatkov. Surove podatke je mogoče ročno raziskovati prek JSON datotek, kot je <a %(a_dbrecord)s>ta</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Pregled"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Spodaj je hiter pregled virov datotek na Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Vir"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Velikost"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% zrcaljeno s strani AA / na voljo torrenti"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Odstotki števila datotek"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Zadnja posodobitev"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Ne-leposlovje in leposlovje"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] "%(count)s datotek"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Preko Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: zamrznjeno od leta 2021; večina na voljo preko torrentov"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: manjši dodatki od takrat</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Izključujoč \"scimag\""

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Leposlovni torrenti so zaostali (čeprav ID-ji ~4-6M niso torrentirani, ker se prekrivajo z našimi Zlib torrenti)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Zbirka “kitajskih” knjig v Z-Library se zdi enaka naši zbirki DuXiu, vendar z različnimi MD5-ji. Te datoteke izključujemo iz torrentov, da se izognemo podvajanju, vendar jih še vedno prikazujemo v našem iskalnem indeksu."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Nadzorovano digitalno posojanje"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ datotek je mogoče iskati."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Skupaj"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Brez podvojenih"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Ker senčne knjižnice pogosto sinhronizirajo podatke med seboj, je med knjižnicami precejšnje prekrivanje. Zato se številke ne ujemajo s skupnim številom."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Odstotek »zrcaljeno in posejano s strani Anninega arhiva« prikazuje, koliko datotek zrcalimo sami. Te datoteke množično posejemo prek torrentov in jih omogočimo za neposreden prenos prek partnerskih spletnih strani."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Izvorne knjižnice"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Nekatere izvorne knjižnice spodbujajo množično deljenje svojih podatkov prek torrentov, medtem ko druge svoje zbirke ne delijo zlahka. V slednjem primeru Anna’s Archive poskuša strgati njihove zbirke in jih narediti dostopne (glejte našo stran <a %(a_torrents)s>Torrenti</a>). Obstajajo tudi vmesne situacije, na primer, ko so izvorne knjižnice pripravljene deliti, vendar nimajo sredstev za to. V teh primerih tudi poskušamo pomagati."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Spodaj je pregled, kako sodelujemo z različnimi izvornimi knjižnicami."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Vir"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Datoteke"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dnevni <a %(dbdumps)s>HTTP podatkovni prenosi</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Avtomatizirani torrenti za <a %(nonfiction)s>neleposlovje</a> in <a %(fiction)s>leposlovje</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(covers)s>torrentov knjižnih ovitkov</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub je zamrznil nove datoteke od leta 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Meta podatkovni prenosi so na voljo <a %(scihub1)s>tukaj</a> in <a %(scihub2)s>tukaj</a>, kot tudi kot del <a %(libgenli)s>Libgen.li baze podatkov</a> (ki jo uporabljamo)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Podatkovni torrenti so na voljo <a %(scihub1)s>tukaj</a>, <a %(scihub2)s>tukaj</a> in <a %(libgenli)s>tukaj</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Nekatere nove datoteke se <a %(libgenrs)s>dodajajo</a> v Libgenov “scimag”, vendar ne dovolj, da bi upravičile nove torrente"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Četrtletni <a %(dbdumps)s>HTTP podatkovni prenosi</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Nefikcijski torrenti so deljeni z Libgen.rs (in zrcaljeni <a %(libgenli)s>tukaj</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annin arhiv in Libgen.li skupaj upravljata zbirke <a %(comics)s>stripov</a>, <a %(magazines)s>revij</a>, <a %(standarts)s>standardnih dokumentov</a> in <a %(fiction)s>leposlovja (ločeno od Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Njihova zbirka \"fiction_rus\" (rusko leposlovje) nima posebnih torrentov, vendar je pokrita s torrenti drugih, mi pa ohranjamo <a %(fiction_rus)s>zrcalo</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annin arhiv in Z-Library skupaj upravljata zbirko <a %(metadata)s>meta podatkov Z-Library</a> in <a %(files)s>datotek Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Nekateri meta podatki so na voljo prek <a %(openlib)s>Open Library podatkovnih baz</a>, vendar ne pokrivajo celotne zbirke IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Ni enostavno dostopnih meta podatkov za celotno zbirko"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(ia)s>IA meta podatkov</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Datoteke so na voljo za izposojo le v omejenem obsegu, z različnimi omejitvami dostopa"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(ia)s>IA datotek</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Različne baze meta podatkov so razpršene po kitajskem internetu; pogosto so to plačljive baze"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Ni na voljo enostavno dostopnih metapodatkov za celotno zbirko."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(duxiu)s>DuXiu metapodatkov</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Različne baze podatkov razpršene po kitajskem internetu; pogosto plačljive baze podatkov"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Večina datotek je dostopna le z uporabo premium računov BaiduYun; počasne hitrosti prenosa."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(duxiu)s>DuXiu datotek</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Različni manjši ali enkratni viri. Spodbujamo ljudi, da najprej naložijo v druge senčne knjižnice, vendar včasih ljudje imajo zbirke, ki so prevelike, da bi jih drugi lahko pregledali, vendar ne dovolj velike, da bi si zaslužile svojo kategorijo."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Viri samo z meta podatki"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Našo zbirko obogatimo tudi z viri samo z meta podatki, ki jih lahko povežemo z datotekami, npr. z uporabo ISBN številk ali drugih polj. Spodaj je pregled teh virov. Spet, nekateri od teh virov so popolnoma odprti, medtem ko jih pri drugih moramo strgati."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Naš navdih za zbiranje metapodatkov je cilj Aarona Swartza \"ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena\", za kar je ustvaril <a %(a_openlib)s>Open Library</a>. Ta projekt je uspešen, vendar nam naš edinstven položaj omogoča pridobivanje metapodatkov, ki jih oni ne morejo. Drug navdih je bila naša želja vedeti <a %(a_blog)s>koliko knjig je na svetu</a>, da lahko izračunamo, koliko knjig moramo še rešiti."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Upoštevajte, da pri iskanju meta podatkov prikazujemo izvirne zapise. Ne združujemo zapisov."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Zadnja posodobitev"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Mesečni <a %(dbdumps)s>izvozi baze podatkov</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Ni na voljo neposredno v velikih količinah, zaščiteno pred strganjem"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(worldcat)s>OCLC (WorldCat) metapodatkov</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Enotna baza podatkov"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Vse zgoraj navedene vire združimo v eno enotno bazo podatkov, ki jo uporabljamo za to spletno stran. Ta enotna baza podatkov ni neposredno dostopna, vendar ker je Anna’s Archive popolnoma odprtokodna, jo je mogoče precej enostavno <a %(a_generated)s>ustvariti</a> ali <a %(a_downloaded)s>prenesti</a> kot ElasticSearch in MariaDB baze podatkov. Skripti na tej strani bodo samodejno prenesli vse potrebne meta podatke iz zgoraj omenjenih virov."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Če želite raziskati naše podatke, preden te skripte zaženete lokalno, si lahko ogledate naše JSON datoteke, ki se povezujejo z drugimi JSON datotekami. <a %(a_json)s>Ta datoteka</a> je dober začetek."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Prilagojeno iz našega <a %(a_href)s>blog prispevka</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> je ogromna baza skeniranih knjig, ki jo je ustvarila <a %(superstar_link)s>SuperStar Digital Library Group</a>. Večina so akademske knjige, skenirane z namenom, da bi jih digitalno omogočili univerzam in knjižnicam. Za našo angleško govorečo publiko imata <a %(princeton_link)s>Princeton</a> in <a %(uw_link)s>University of Washington</a> dobre preglede. Obstaja tudi odličen članek, ki ponuja več ozadja: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Knjige iz Duxiu so že dolgo piratizirane na kitajskem internetu. Običajno jih preprodajalci prodajajo za manj kot dolar. Običajno se distribuirajo z uporabo kitajskega ekvivalenta Google Drive, ki je pogosto vdrt, da omogoča več prostora za shranjevanje. Nekatere tehnične podrobnosti najdete <a %(link1)s>tukaj</a> in <a %(link2)s>tukaj</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Čeprav so bile knjige poljavnostno distribuirane, jih je precej težko pridobiti v večjih količinah. To smo imeli visoko na našem seznamu opravil in smo za to namenili več mesecev polnega delovnega časa. Vendar pa se je konec leta 2023 oglasil neverjeten, izjemen in nadarjen prostovoljec, ki nam je povedal, da je že opravil vse to delo — z velikimi stroški. Delil je celotno zbirko z nami, ne da bi pričakoval karkoli v zameno, razen zagotovila za dolgoročno ohranitev. Resnično izjemno."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Viri"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Skupno število datotek: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Skupna velikost datotek: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Datoteke, zrcaljene s strani Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Zadnja posodobitev: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrenti s strani Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Primer zapisa na Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Naš blog prispevek o teh podatkih"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripti za uvoz meta podatkov"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Format Anna’s Archive Containers"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Več informacij od naših prostovoljcev (surovi zapiski):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Nadzorovano digitalno posojanje"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Ta nabor podatkov je tesno povezan z <a %(a_datasets_openlib)s>naborom podatkov Open Library</a>. Vsebuje strganje vseh meta podatkov in velik del datotek iz IA-jeve knjižnice nadzorovanega digitalnega izposojanja. Posodobitve se izdajajo v <a %(a_aac)s>formatu Anninih arhivskih vsebnikov</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Ti zapisi se neposredno nanašajo na nabor podatkov Open Library, vendar vsebujejo tudi zapise, ki niso v Open Library. Imamo tudi številne podatkovne datoteke, ki so jih skozi leta strgali člani skupnosti."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Zbirka je sestavljena iz dveh delov. Potrebujete oba dela, da dobite vse podatke (razen nadomeščenih torrentov, ki so prečrtani na strani torrentov)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "naša prva izdaja, preden smo standardizirali na <a %(a_aac)s>format Anna’s Archive Containers (AAC)</a>. Vsebuje meta podatke (kot json in xml), pdf-je (iz digitalnih sistemov izposoje acsm in lcpdf) in sličice naslovnic."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementalne nove izdaje, z uporabo AAC. Vsebujejo samo meta podatke s časovnimi žigi po 2023-01-01, saj je preostalo že pokrito z “ia”. Prav tako vsi pdf-ji, tokrat iz sistemov izposoje acsm in “bookreader” (IA-jev spletni bralnik). Kljub temu, da ime ni povsem pravilno, še vedno vključujemo datoteke bookreader v zbirko ia2_acsmpdf_files, saj so medsebojno izključujoče."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Glavna %(source)s spletna stran"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitalna knjižnica"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dokumentacija meta podatkov (večina polj)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informacije o državi ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Mednarodna agencija za ISBN redno objavlja razpone, ki jih je dodelila nacionalnim agencijam za ISBN. Iz tega lahko ugotovimo, kateri državi, regiji ali jezikovni skupini pripada ta ISBN. Trenutno te podatke uporabljamo posredno, preko <a %(a_isbnlib)s>isbnlib</a> Python knjižnice."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Viri"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Zadnja posodobitev: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Spletna stran ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Meta podatki"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Za ozadje različnih razcepov Library Genesis, glejte stran za <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li vsebuje večino iste vsebine in meta podatkov kot Libgen.rs, vendar ima nekaj dodatnih zbirk, in sicer stripe, revije in standardne dokumente. Prav tako je integriral <a %(a_scihub)s>Sci-Hub</a> v svoje meta podatke in iskalnik, kar uporabljamo za našo bazo podatkov."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Meta podatki za to knjižnico so prosto dostopni <a %(a_libgen_li)s>na libgen.li</a>. Vendar je ta strežnik počasen in ne podpira nadaljevanja prekinjenih povezav. Iste datoteke so na voljo tudi na <a %(a_ftp)s>FTP strežniku</a>, ki deluje bolje."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrenti so na voljo za večino dodatne vsebine, še posebej torrenti za stripe, revije in standardne dokumente so bili izdani v sodelovanju z Anninim arhivom."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Zbirka leposlovja ima svoje torrente (ločeno od <a %(a_href)s>Libgen.rs</a>) od %(start)s naprej."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Po besedah administratorja Libgen.li naj bi zbirka \"fiction_rus\" (rusko leposlovje) bila pokrita z redno izdanimi torrenti iz <a %(a_booktracker)s>booktracker.org</a>, še posebej torrenti <a %(a_flibusta)s>flibusta</a> in <a %(a_librusec)s>lib.rus.ec</a> (ki jih zrcalimo <a %(a_torrents)s>tukaj</a>, čeprav še nismo ugotovili, kateri torrenti ustrezajo katerim datotekam)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistike za vse zbirke so na voljo <a %(a_href)s>na spletni strani libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Zdi se, da se je tudi neleposlovje razširilo, vendar brez novih tokov. Zdi se, da se je to zgodilo od začetka leta 2022, čeprav tega nismo preverili."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Določeni razponi brez torrentov (kot so razponi leposlovja f_3463000 do f_4260000) so verjetno datoteke Z-Library (ali druge podvojene) datoteke, čeprav bi morda želeli narediti nekaj deduplikacije in ustvariti torrente za lgli-edinstvene datoteke v teh razponih."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Upoštevajte, da so torrent datoteke, ki se nanašajo na “libgen.is”, izrecno zrcalne kopije <a %(a_libgen)s>Libgen.rs</a> (“.is” je druga domena, ki jo uporablja Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Koristen vir za uporabo meta podatkov je <a %(a_href)s>ta stran</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Leposlovni torrenti na Anninem Arhivu"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Stripovski torrenti na Anninem Arhivu"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Revijalni torrenti na Anninem Arhivu"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standardni dokumentni torrenti na Anninem arhivu"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Ruski leposlovni torrenti na Anninem arhivu"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Meta podatki"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Meta podatki preko FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informacije o poljih meta podatkov"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Zrcalna kopija drugih torrentov (in edinstveni leposlovni in stripovski torrenti)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum za razprave"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Naša objava na blogu o izdaji stripov"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Kratka zgodba o različnih različicah Library Genesis (ali “Libgen”) je, da so se skozi čas različni ljudje, vključeni v Library Genesis, sprli in šli vsak svojo pot."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Različico “.fun” je ustvaril prvotni ustanovitelj. Prenavlja se v korist nove, bolj razpršene različice."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Različica “.rs” ima zelo podobne podatke in najpogosteje izdaja svojo zbirko v obliki torrentov. Približno je razdeljena na “leposlovni” in “neleposlovni” del."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Prvotno na “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Različica “.li”</a> ima ogromno zbirko stripov, pa tudi druge vsebine, ki (še) niso na voljo za množični prenos preko torrentov. Ima ločeno zbirko torrentov za leposlovne knjige in vsebuje meta podatke <a %(a_scihub)s>Sci-Hub</a> v svoji bazi podatkov."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Po tem <a %(a_mhut)s>forumskem prispevku</a> je bil Libgen.li prvotno gostovan na “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> je v nekem smislu tudi različica Library Genesis, čeprav so za svoj projekt uporabili drugo ime."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Ta stran je o različici “.rs”. Znana je po doslednem objavljanju tako svojih meta podatkov kot celotne vsebine svojega kataloga knjig. Njena zbirka knjig je razdeljena na leposlovni in neleposlovni del."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Koristen vir za uporabo meta podatkov je <a %(a_metadata)s>ta stran</a> (blokira IP razpone, morda bo potreben VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Od marca 2024 se novi torrenti objavljajo v <a %(a_href)s>tej forum temi</a> (blokira IP razpone, morda bo potreben VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrent za nefikcijo na Anninem arhivu"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrent za fikcijo na Anninem arhivu"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Meta podatki"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informacije o meta podatkih Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Torrenti za nefikcijo"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Torrenti za fikcijo"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskusijski forum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrenti po Anninem arhivu (naslovnice knjig)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Naš blog o izdaji naslovnic knjig"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis je znan po tem, da že velikodušno omogoča dostop do svojih podatkov v velikih količinah prek torrentov. Naša zbirka Libgen vsebuje pomožne podatke, ki jih oni ne objavljajo neposredno, v sodelovanju z njimi. Velika zahvala vsem, ki sodelujejo z Library Genesis za sodelovanje z nami!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Izdaja 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Ta <a %(blog_post)s>prva izdaja</a> je precej majhna: približno 300 GB naslovnic knjig iz vilice Libgen.rs, tako fikcijo kot nefikcijo. Organizirane so na enak način, kot se pojavljajo na libgen.rs, npr.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s za nefikcijsko knjigo."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s za fikcijsko knjigo."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Tako kot pri zbirki Z-Library, smo jih vse postavili v veliko .tar datoteko, ki jo lahko namestite z uporabo <a %(a_ratarmount)s>ratarmount</a>, če želite datoteke neposredno servirati."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> je lastniška baza podatkov neprofitne organizacije <a %(a_oclc)s>OCLC</a>, ki združuje meta podatke iz knjižnic po vsem svetu. Verjetno je to največja zbirka knjižničnih meta podatkov na svetu."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, začetna izdaja:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Oktobra 2023 smo <a %(a_scrape)s>objavili</a> obsežno strganje baze podatkov OCLC (WorldCat) v <a %(a_aac)s>formatu Anninih arhivskih vsebnikov</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrenti na Anninem arhivu"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Naša objava na blogu o teh podatkih"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library je odprtokodni projekt Internet Archive za katalogizacijo vsake knjige na svetu. Ima eno največjih operacij skeniranja knjig na svetu in ima veliko knjig na voljo za digitalno izposojo. Njegov katalog meta podatkov knjig je prosto dostopen za prenos in je vključen v Anninem arhivu (čeprav trenutno ni v iskanju, razen če izrecno iščete Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Meta podatki"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Izdaja 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "To je zbirka številnih klicev na isbndb.com med septembrom 2022. Poskušali smo zajeti vse razpone ISBN. Gre za približno 30,9 milijona zapisov. Na njihovi spletni strani trdijo, da imajo dejansko 32,6 milijona zapisov, tako da smo morda kakšnega izpustili, ali pa <em>oni</em> delajo nekaj narobe."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON odgovori so skoraj surovi iz njihovega strežnika. Ena od težav s kakovostjo podatkov, ki smo jo opazili, je, da za številke ISBN-13, ki se začnejo z drugačnim predpono kot “978-”, še vedno vključujejo polje “isbn”, ki je preprosto številka ISBN-13 s prvimi 3 številkami odrezanimi (in kontrolna številka ponovno izračunana). To je očitno napačno, vendar tako to počnejo, zato tega nismo spreminjali."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Druga možna težava, na katero lahko naletite, je dejstvo, da polje “isbn13” vsebuje podvojene vrednosti, zato ga ne morete uporabiti kot primarni ključ v bazi podatkov. Kombinacija polj “isbn13”+“isbn” pa se zdi edinstvena."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Za več informacij o Sci-Hub obiščite njegovo <a %(a_scihub)s>uradno spletno stran</a>, <a %(a_wikipedia)s>stran na Wikipediji</a> in ta <a %(a_radiolab)s>podcast intervju</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Upoštevajte, da je Sci-Hub <a %(a_reddit)s>zamrznjen od leta 2021</a>. Bil je zamrznjen že prej, vendar je bilo leta 2021 dodanih nekaj milijonov člankov. Kljub temu se v zbirke “scimag” na Libgen še vedno dodaja omejeno število člankov, vendar ne dovolj, da bi upravičili nove obsežne torrente."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Uporabljamo meta podatke Sci-Hub, ki jih zagotavlja <a %(a_libgen_li)s>Libgen.li</a> v svoji zbirki “scimag”. Uporabljamo tudi podatkovno zbirko <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Upoštevajte, da so “smarch” torrenti <a %(a_smarch)s>zastareli</a> in zato niso vključeni na našem seznamu torrentov."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrenti na Anninem arhivu"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Meta podatki in torrenti"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrenti na Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrenti na Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Posodobitve na Redditu"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Stran na Wikipediji"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast intervju"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Nalaganja v Annin arhiv"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Pregled s <a %(a1)s>strani datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Različni manjši ali enkratni viri. Spodbujamo ljudi, da najprej naložijo v druge senčne knjižnice, vendar včasih ljudje imajo zbirke, ki so prevelike, da bi jih drugi lahko pregledali, vendar ne dovolj velike, da bi si zaslužile svojo kategorijo."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Zbirka \"naloži\" je razdeljena na manjše podzbirke, ki so označene v AACID-jih in imenih torrentov. Vse podzbirke so bile najprej deduplicirane proti glavni zbirki, vendar metapodatkovne datoteke \"upload_records\" JSON še vedno vsebujejo veliko referenc na izvirne datoteke. Ne-knjižne datoteke so bile prav tako odstranjene iz večine podzbir, in običajno <em>niso</em> navedene v \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Mnoge podzbirke same po sebi sestavljajo pod-podzbirke (npr. iz različnih izvirnih virov), ki so predstavljene kot imeniki v poljih \"filepath\"."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Podzbirke so:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Podzbirka"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Opombe"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "brskaj"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "išči"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Iz <a %(a_href)s>aaaaarg.fail</a>. Zdi se, da je precej popoln. Od našega prostovoljca “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Iz <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrenta. Ima precej visoko prekrivanje z obstoječimi zbirkami člankov, vendar zelo malo ujemanj MD5, zato smo se odločili, da ga obdržimo v celoti."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Zajem podatkov iz <q>iRead eBooks</q> (= fonetično <q>ai rit i-books</q>; airitibooks.com), s strani prostovoljca <q>j</q>. Ustreza <q>airitibooks</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Iz zbirke <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delno iz izvirnega vira, delno iz the-eye.eu, delno iz drugih zrcal."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Iz zasebne torrent strani za knjige, <a %(a_href)s>Bibliotik</a> (pogosto imenovan kot “Bib”), kjer so bile knjige združene v torrente po imenu (A.torrent, B.torrent) in distribuirane preko the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Od našega prostovoljca “bpb9v”. Za več informacij o <a %(a_href)s>CADAL</a> glejte opombe na naši <a %(a_duxiu)s>DuXiu dataset strani</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Več od našega prostovoljca “bpb9v”, večinoma DuXiu datoteke, kot tudi mapa “WenQu” in “SuperStar_Journals” (SuperStar je podjetje za DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Od našega prostovoljca “cgiym”, kitajski teksti iz različnih virov (predstavljeni kot podimeniki), vključno z <a %(a_href)s>China Machine Press</a> (velik kitajski založnik)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Ne-kitajske zbirke (predstavljene kot podimeniki) od našega prostovoljca “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Zajem podatkov o knjigah o kitajski arhitekturi, s strani prostovoljca <q>cm</q>: <q>Pridobil sem jih z izkoriščanjem ranljivosti omrežja v založbi, vendar je bila ta luknja že zaprta</q>. Ustreza <q>chinese_architecture</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Knjige iz akademske založbe <a %(a_href)s>De Gruyter</a>, zbrane iz nekaj velikih torrentov."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape iz <a %(a_href)s>docer.pl</a>, poljske spletne strani za deljenje datotek, osredotočene na knjige in druga pisna dela. Scrape izveden konec leta 2023 s strani prostovoljca “p”. Nimamo dobrih meta podatkov iz izvorne spletne strani (niti končnic datotek), vendar smo filtrirali datoteke, ki so bile podobne knjigam, in pogosto uspeli izvleči meta podatke iz samih datotek."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubi, neposredno iz DuXiu, zbrani s strani prostovoljca “w”. Samo nedavne DuXiu knjige so na voljo neposredno preko e-knjig, zato morajo biti večinoma nedavne."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Preostale DuXiu datoteke od prostovoljca “m”, ki niso bile v DuXiu lastniškem PDG formatu (glavni <a %(a_href)s>DuXiu dataset</a>). Zbrane iz mnogih izvornih virov, žal brez ohranjanja teh virov v poti datoteke."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Zajem podatkov o erotičnih knjigah, s strani prostovoljca <q>do no harm</q>. Ustreza <q>hentai</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Zbirka, zbrana iz japonskega Manga založnika s strani prostovoljca “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Izbrani sodni arhivi Longquan</a>, zagotovljeni s strani prostovoljca “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape iz <a %(a_href)s>magzdb.org</a>, zaveznik Library Genesis (povezan je na libgen.rs domači strani), vendar niso želeli neposredno zagotoviti svojih datotek. Pridobljeno s strani prostovoljca “p” konec leta 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Različni majhni prenosi, premajhni za svojo lastno podzbirko, vendar predstavljeni kot imeniki."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-knjige iz AvaxHome, ruske spletne strani za deljenje datotek."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arhiv časopisov in revij. Ustreza <q>newsarch_magz</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Zajem podatkov iz <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Zbirka prostovoljca “o”, ki je zbral poljske knjige neposredno iz izvornih “scene” spletnih strani."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Združene zbirke <a %(a_href)s>shuge.org</a> s strani prostovoljcev “cgiym” in “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (imenovana po izmišljeni knjižnici), zbrana leta 2022 s strani prostovoljca “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Pod-pod-zbirke (predstavljene kot imeniki) od prostovoljca “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (od <a %(a_sikuquanshu)s>Dizhi(迪志)</a> v Tajvanu), mebook (mebook.cc, 我的小书屋, moja mala knjižnica — woz9ts: “Ta stran se osredotoča predvsem na deljenje visokokakovostnih e-knjig, nekatere od njih so bile postavljene s strani lastnika samega. Lastnik je bil <a %(a_arrested)s>aretiran</a> leta 2019 in nekdo je naredil zbirko datotek, ki jih je delil.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Preostale datoteke DuXiu od prostovoljca »woz9ts«, ki niso bile v lastniškem formatu PDG DuXiu (še vedno jih je treba pretvoriti v PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrenti pri Anninem arhivu"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library strganje"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ima svoje korenine v skupnosti <a %(a_href)s>Library Genesis</a> in je prvotno začel z njihovimi podatki. Od takrat se je precej profesionaliziral in ima veliko bolj sodoben vmesnik. Zato lahko prejmejo veliko več donacij, tako denarnih za nadaljnje izboljšanje njihove spletne strani kot tudi donacij novih knjig. Poleg Library Genesis so zbrali veliko zbirko."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Posodobitev od februarja 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Konec leta 2022 so bili domnevni ustanovitelji Z-Library aretirani, domene pa so zasegle oblasti Združenih držav. Od takrat se spletna stran počasi vrača na splet. Ni znano, kdo jo trenutno vodi."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Zbirka je sestavljena iz treh delov. Spodaj so ohranjene izvirne opisne strani za prva dva dela. Potrebujete vse tri dele, da dobite vse podatke (razen nadomeščenih torrentov, ki so prečrtani na strani torrentov)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: naša prva izdaja. To je bila prva izdaja tistega, kar je bilo takrat imenovano »Pirate Library Mirror« (»pilimi«)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: druga izdaja, tokrat z vsemi datotekami zavitimi v .tar datoteke."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementalne nove izdaje, z uporabo <a %(a_href)s>formatov Anninih arhivskih vsebnikov (AAC)</a>, zdaj izdane v sodelovanju z ekipo Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenti pri Anninem arhivu (meta podatki + vsebina)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Primer zapisa pri Anninem arhivu (izvirna zbirka)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Primer zapisa pri Anninem arhivu (zbirka »zlib3«)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Glavna spletna stran"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domena"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Objava na blogu o Izdaji 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Objava na blogu o Izdaji 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib izdaje (izvirne opisne strani)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Izdaja 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Začetno zrcalo je bilo skrbno pridobljeno v letih 2021 in 2022. Trenutno je nekoliko zastarelo: odraža stanje zbirke junija 2021. To bomo posodobili v prihodnosti. Trenutno se osredotočamo na izdajo te prve izdaje."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Ker je Library Genesis že ohranjen z javnimi torrenti in je vključen v Z-Library, smo junija 2022 izvedli osnovno deduplikacijo proti Library Genesis. Za to smo uporabili MD5 hashe. Verjetno je v knjižnici še veliko podvojene vsebine, kot so več formatov datotek iste knjige. To je težko natančno zaznati, zato tega ne počnemo. Po deduplikaciji nam ostane več kot 2 milijona datotek, skupaj nekaj manj kot 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Zbirka je sestavljena iz dveh delov: MySQL “.sql.gz” dumpa meta podatkov in 72 torrent datotek, velikih približno 50-100GB vsaka. Meta podatki vsebujejo podatke, kot jih poroča spletna stran Z-Library (naslov, avtor, opis, vrsta datoteke), pa tudi dejansko velikost datoteke in md5sum, ki smo ju opazili, saj se včasih ti podatki ne ujemajo. Zdi se, da obstajajo obsegi datotek, za katere ima Z-Library napačne meta podatke. V nekaterih izoliranih primerih smo morda tudi napačno prenesli datoteke, kar bomo poskušali zaznati in popraviti v prihodnosti."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Velike torrent datoteke vsebujejo dejanske podatke o knjigah, z ID-jem Z-Library kot imenom datoteke. Končnice datotek je mogoče rekonstruirati z uporabo dumpa meta podatkov."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Zbirka je mešanica nefikcijske in fikcijske vsebine (ni ločena kot v Library Genesis). Kakovost je tudi zelo različna."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Ta prva izdaja je zdaj popolnoma na voljo. Upoštevajte, da so torrent datoteke na voljo samo prek našega Tor zrcala."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Izdaja 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Dobili smo vse knjige, ki so bile dodane v Z-Library med našim zadnjim zrcalom in avgustom 2022. Prav tako smo se vrnili in zajeli nekaj knjig, ki smo jih prvič zgrešili. Vse skupaj je ta nova zbirka velika približno 24TB. Tudi to zbirko smo deduplicirali proti Library Genesis, saj so za to zbirko že na voljo torrenti."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Podatki so organizirani podobno kot pri prvi izdaji. Obstaja MySQL “.sql.gz” dump meta podatkov, ki vključuje tudi vse meta podatke iz prve izdaje, s čimer jo nadomešča. Dodali smo tudi nekaj novih stolpcev:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ali je ta datoteka že v Library Genesis, bodisi v nefikcijski ali fikcijski zbirki (ujemanje po md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: v katerem torrentu je ta datoteka."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: nastavljeno, ko nismo mogli prenesti knjige."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "To smo omenili že zadnjič, vendar za pojasnilo: “filename” in “md5” sta dejanski lastnosti datoteke, medtem ko sta “filename_reported” in “md5_reported” tisto, kar smo zajeli iz Z-Library. Včasih se ti dve ne ujemata, zato smo vključili obe."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Za to izdajo smo spremenili kolacijo v “utf8mb4_unicode_ci”, ki bi morala biti združljiva s starejšimi različicami MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Podatkovne datoteke so podobne kot zadnjič, čeprav so veliko večje. Preprosto se nam ni dalo ustvarjati veliko manjših torrent datotek. “pilimi-zlib2-0-14679999-extra.torrent” vsebuje vse datoteke, ki smo jih zgrešili v zadnji izdaji, medtem ko so drugi torrenti vsi novi ID obsegi. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Posodobitev %(date)s:</strong> Večino naših torrentov smo naredili prevelike, kar je povzročilo težave torrent odjemalcem. Odstranili smo jih in izdali nove torrente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Posodobitev %(date)s:</strong> Še vedno je bilo preveč datotek, zato smo jih zavili v tar datoteke in ponovno izdali nove torrente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Dodatek k izdaji 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "To je ena dodatna torrent datoteka. Ne vsebuje nobenih novih informacij, vendar ima nekaj podatkov, ki lahko trajajo nekaj časa za izračun. To je priročno imeti, saj je prenos tega torrenta pogosto hitrejši kot izračunavanje od začetka. Zlasti vsebuje SQLite indekse za tar datoteke, za uporabo z <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Pogosto zastavljena vprašanja (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Kaj je Annin arhiv?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anin arhiv</span> je neprofitni projekt z dvema ciljema:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Ohranjanje:</strong> Varnostno kopiranje vsega znanja in kulture človeštva.</li><li><strong>Dostop:</strong> Dajanje tega znanja in kulture na voljo vsem na svetu.</li>"

msgid "page.home.intro.open_source"
msgstr "Vsa naša <a %(a_code)s>koda</a> in <a %(a_datasets)s>podatki</a> so popolnoma odprtokodni."

msgid "page.home.preservation.header"
msgstr "Ohranjanje"

msgid "page.home.preservation.text1"
msgstr "Hranimo knjige, časopise, stripe, revije in drugo tako, da to gradivo prenašamo iz različnih <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">senčnih knjižnic</a>, uradnih knjižnic in druge zbirke skupaj na enem mestu. Vsi ti podatki so ohranjeni za vedno, saj jih je enostavno podvajati v velikem obsegu - z uporabo torrentov - kar ima za posledico veliko kopij po vsem svetu. Nekatere senčne knjižnice to že počnejo same (npr. Sci-Hub, Library Genesis), medtem ko Anin arhiv »osvobaja« druge knjižnice, ki ne ponujajo množične distribucije (npr. Z-Library) ali sploh niso senčne knjižnice (npr. Internet Archive , DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Zaradi te široke distribucije v kombinaciji z odprtokodno kodo je naše spletno mesto odporno na odstranitve in zagotavlja dolgoročno ohranjanje znanja in kulture človeštva. Več o <a href=\"/datasets\">naših naborih podatkov</a>."

msgid "page.home.preservation.label"
msgstr "Ocenjujemo, da smo ohranili približno <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5 %% knjig na svetu </a>."

msgid "page.home.access.header"
msgstr "Dostop"

msgid "page.home.access.text"
msgstr "Sodelujemo s partnerji, da naredimo naše zbirke enostavno in prosto dostopne vsem. Verjamemo, da ima vsakdo pravico do kolektivne modrosti človeštva. In <a %(a_search)s>ne na račun avtorjev</a>."

msgid "page.home.access.label"
msgstr "Prenosi/uro v zadnjih 30 dneh. Urno povprečje: %(hourly)s. Dnevno povprečje: %(daily)s."

msgid "page.about.text2"
msgstr "Trdno verjamemo v prost pretok informacij ter ohranjanje znanja in kulture. S tem iskalnikom gradimo na plečih velikanov. Globoko spoštujemo trdo delo ljudi, ki so ustvarili različne senčne knjižnice, in upamo, da bo ta iskalnik razširil njihov doseg."

msgid "page.about.text3"
msgstr "Če želite biti obveščeni o našem napredku, spremljajte Anno na <a href=\"https://www.reddit.com/r/Annas_Archive /\">Reddit-i</a> ali <a href=\"https://t.me/annasarchiveorg\">Telegram-u</a>. Za vprašanja in povratne informacije se obrnite na Anno na %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Kako lahko pomagam?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Sledite nam na <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ali <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Širite besedo o Anninem arhivu na Twitterju, Redditu, Tiktoku, Instagramu, v lokalni kavarni ali knjižnici, ali kjerkoli že ste! Ne verjamemo v omejevanje dostopa — če nas odstranijo, se bomo preprosto pojavili drugje, saj je vsa naša koda in podatki popolnoma odprtokodni.</li><li>3. Če lahko, razmislite o <a href=\"/donate\">donaciji</a>.</li><li>4. Pomagajte <a href=\"https://translate.annas-software.org/\">prevesti</a> našo spletno stran v različne jezike.</li><li>5. Če ste programski inženir, razmislite o prispevanju k našemu <a href=\"https://annas-software.org/\">odprtokodnemu</a> projektu ali sejanju naših <a href=\"/datasets\">torrentov</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Zdaj imamo tudi sinhroniziran Matrix kanal na %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Če ste varnostni raziskovalec, lahko vaše sposobnosti uporabimo tako za napad kot za obrambo. Oglejte si našo stran <a %(a_security)s>Varnost</a>."

msgid "page.about.help.text7"
msgstr "7. Iščemo strokovnjake za plačila za anonimne trgovce. Ali nam lahko pomagate dodati bolj priročne načine za darovanje? PayPal, WeChat, darilne kartice. Če poznate koga, se obrnite na nas."

msgid "page.about.help.text8"
msgstr "8. Vedno iščemo možnosti za povečanje zmogljivost strežnikov."

msgid "page.about.help.text9"
msgstr "9. Pomagate lahko tako, da prijavite težave z datotekami, objavite komentarje in ustvarite sezname kar na tej spletni strani. Pomagate lahko tudi tako, da <a %(a_upload)s>naložite več knjig</a> ali odpravite težave z datotekami ali formatirate obstoječe knjige."

msgid "page.about.help.text10"
msgstr "10. Ustvarite ali pomagajte vzdrževati stran Wikipedije za Anin arhiv v svojem jeziku."

msgid "page.about.help.text11"
msgstr "11. Objavljali bomo male, estetske oglase. Če želite oglaševati v Aninem arhivu, nam to sporočite."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Radi bi, da ljudje postavijo <a %(a_mirrors)s>zrcalne strežnike</a>, in to bomo finančno podprli."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Za obsežnejše informacije o tem, kako prostovoljno sodelovati, si oglejte našo stran <a %(a_volunteering)s>Prostovoljstvo in nagrade</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Zakaj so počasni prenosi tako počasni?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Preprosto nimamo dovolj virov, da bi vsem na svetu omogočili hitre prenose, čeprav bi si to želeli. Če bi se našel bogat dobrotnik, ki bi nam to omogočil, bi bilo neverjetno, vendar do takrat se trudimo po svojih najboljših močeh. Smo neprofitni projekt, ki se komaj vzdržuje z donacijami."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Zato smo uvedli dva sistema za brezplačne prenose s partnerji: deljene strežnike s počasnimi prenosi in nekoliko hitrejše strežnike s čakalno listo (za zmanjšanje števila ljudi, ki prenašajo hkrati)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Za naše počasne prenose imamo tudi <a %(a_verification)s>preverjanje brskalnika</a>, ker bi jih sicer zlorabljali boti in strgalniki, kar bi še bolj upočasnilo stvari za legitimne uporabnike."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Upoštevajte, da boste pri uporabi brskalnika Tor morda morali prilagoditi svoje varnostne nastavitve. Pri najnižji možnosti, imenovani “Standard”, izziv Cloudflare turnstile uspe. Pri višjih možnostih, imenovanih “Varnejši” in “Najvarnejši”, izziv ne uspe."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Za velike datoteke lahko počasni prenosi včasih prekinejo sredi prenosa. Priporočamo uporabo upravitelja prenosov (kot je JDownloader) za samodejno nadaljevanje velikih prenosov."

msgid "page.donate.faq.title"
msgstr "Pogosta vprašanja o donacijah"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Ali se članstva samodejno podaljšujejo?</div> Članstva se <strong>ne</strong> podaljšujejo samodejno. Pridružite se nam lahko za kakor dolgo želite."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Ali lahko nadgradim svoje članstvo ali pridobim več članstev?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Ali imate druge načine plačila?</div> Trenutno ne. Veliko ljudi si ne želi, da bi takšni arhivi obstajali, zato moramo biti previdni. Če nam lahko pomagate vzpostaviti druge (priročnejše) načine varnega plačila, se obrnite na %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Kaj pomenijo razponi na mesec?</div> Do spodnje meje razpona lahko pridete z uporabo vseh popustov, kot je izbira daljšega obdobja od enega meseca."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Za kaj porabite donacije?</div> 100%% gre za ohranjanje in omogočanje dostopa do svetovnega znanja in kulture. Trenutno ga večinoma porabimo za strežnike, diskovni prostor in pasovno širino. Noben denar ne gre nobenemu članu ekipe osebno."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Ali lahko prispevam veliko?</div> To bi bilo neverjetno! Za donacije nad nekaj tisoč dolarjev nas kontaktirajte neposredno na %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Ali lahko doniram brez članstva?</div> Seveda. Sprejemamo donacije v poljubnem znesku na ta Monero (XMR) naslov: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Kako naložim nove knjige?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Lahko jih tudi naložite na Z-Library <a %(a_upload)s>tukaj</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Za manjše prenose (do 10.000 datotek) jih prosimo naložite na oba %(first)s in %(second)s."

msgid "page.upload.text1"
msgstr "Zaenkrat predlagamo nalaganje novih knjig v vejitve Library Genesis. Tukaj je <a %(a_guide)s>priročen vodnik</a>. Upoštevajte, da obe vejitvi kode, ki ju indeksiramo na tem spletnem mestu, črpata iz istega sistema za nalaganje."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Za Libgen.li se prepričajte, da se najprej prijavite na <a %(a_forum)s >njihovem forumu</a> z uporabniškim imenom %(username)s in geslom %(password)s, nato pa se vrnite na njihovo <a %(a_upload_page)s >stran za nalaganje</a>."

msgid "common.libgen.email"
msgstr "Če vaš e-poštni naslov ne deluje na forumih Libgen, priporočamo uporabo <a %(a_mail)s>Proton Mail-a</a> (brezplačno). Lahko tudi <a %(a_manual)s>ročno zahtevate</a>, da se vaš račun aktivira."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Upoštevajte, da mhut.org blokira določene IP razpone, zato bo morda potreben VPN."

msgid "page.upload.large.text"
msgstr "Za velika nalaganja (več kot 10.000 datotek), ki jih Libgen ali Z-Library ne sprejmeta, nas kontaktirajte na %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Za nalaganje akademskih člankov jih poleg Library Genesis naložite tudi na <a %(a_stc_nexus)s>STC Nexus</a>. So najboljša senčna knjižnica za nove članke. Še jih nismo integrirali, vendar jih bomo nekoč. Lahko uporabite njihov <a %(a_telegram)s>nalagalni bot na Telegramu</a> ali kontaktirate naslov, naveden v njihovem pripetem sporočilu, če imate preveč datotek za nalaganje na ta način."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Kako zahtevam knjige?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Trenutno ne moremo sprejemati zahtevkov za knjige."

#, fuzzy
msgid "page.request.forums"
msgstr "Prosimo, podajte svoje zahteve na forumih Z-Library ali Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Ne pošiljajte nam e-pošte z zahtevki za knjige."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Ali zbirate meta podatke?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Res je."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Prenesel sem 1984 od Georgea Orwella, ali bo policija prišla na moja vrata?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Ne skrbite preveč, veliko ljudi prenaša s spletnih strani, na katere povezujemo, in zelo redko pride do težav. Vendar pa za varnost priporočamo uporabo VPN (plačljivega) ali <a %(a_tor)s>Tor</a> (brezplačnega)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Kako shranim nastavitve iskanja?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Izberite želene nastavitve, pustite iskalno polje prazno, kliknite »Išči« in nato zaznamujte stran z zaznamki vašega brskalnika."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Ali imate mobilno aplikacijo?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nimamo uradne mobilne aplikacije, vendar lahko to spletno stran namestite kot aplikacijo."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Kliknite meni s tremi pikami v zgornjem desnem kotu in izberite »Dodaj na začetni zaslon«."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Kliknite gumb »Deli« na dnu in izberite »Dodaj na domači zaslon«."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Ali imate API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Imamo eno stabilno JSON API za člane, za pridobitev hitrega URL-ja za prenos: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacija znotraj samega JSON-a)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Za druge primere uporabe, kot so iteracija skozi vse naše datoteke, izdelava prilagojenega iskanja in podobno, priporočamo <a %(a_generate)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov. Surove podatke lahko ročno raziskujete <a %(a_explore)s>prek JSON datotek</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Naš seznam surovih torrentov je prav tako na voljo za prenos kot <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Pogosta vprašanja o torrentih"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Rad bi pomagal pri sejanju, vendar nimam veliko prostora na disku."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Uporabite <a %(a_list)s>generator seznama torrentov</a> za ustvarjanje seznama torrentov, ki najbolj potrebujejo sejanje, znotraj vaših omejitev prostora za shranjevanje."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrenti so prepočasni; ali lahko podatke prenesem neposredno od vas?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Da, glejte stran <a %(a_llm)s>LLM podatki</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Ali lahko prenesem samo podmnožico datotek, na primer samo določen jezik ali temo?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kratek odgovor: ne zlahka."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Dolg odgovor:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Večina torrentov vsebuje datoteke neposredno, kar pomeni, da lahko torrent odjemalcem naročite, naj prenesejo samo zahtevane datoteke. Za določitev, katere datoteke prenesti, lahko <a %(a_generate)s>generirate</a> naše meta podatke ali <a %(a_download)s>preneste</a> naše ElasticSearch in MariaDB baze podatkov. Na žalost številne zbirke torrentov vsebujejo .zip ali .tar datoteke v korenu, v tem primeru morate prenesti celoten torrent, preden lahko izberete posamezne datoteke."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Vendar imamo <a %(a_ideas)s>nekaj idej</a> za slednji primer.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Orodja za enostavno filtriranje torrentov še niso na voljo, vendar pozdravljamo prispevke."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Kako ravnate z dvojniki v torrentih?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Poskušamo ohraniti minimalno podvajanje ali prekrivanje med torrenti na tem seznamu, vendar to ni vedno mogoče in je močno odvisno od politik izvornih knjižnic. Za knjižnice, ki izdajajo svoje torrente, to ni v naših rokah. Za torrente, ki jih izda Annin Arhiv, dedupliciramo samo na podlagi MD5 hasha, kar pomeni, da različne različice iste knjige niso deduplicirane."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Ali lahko dobim seznam torrentov kot JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Da."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Ne vidim PDF-jev ali EPUB-jev v torrentih, samo binarne datoteke? Kaj naj storim?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "To so dejansko PDF-ji in EPUB-ji, le da v mnogih naših torrentih nimajo pripone. Obstajata dva mesta, kjer lahko najdete meta podatke za torrent datoteke, vključno z vrstami/priponami datotek:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Vsaka zbirka ali izdaja ima svoje meta podatke. Na primer, <a %(a_libgen_nonfic)s>Libgen.rs torrenti</a> imajo ustrezno bazo meta podatkov, ki je gostovana na spletni strani Libgen.rs. Običajno povezujemo na ustrezne vire meta podatkov iz vsake <a %(a_datasets)s>strani podatkovne zbirke</a> zbirke."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Priporočamo <a %(a_generate)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov. Te vsebujejo preslikavo za vsak zapis v Anninem Arhivu na ustrezne torrent datoteke (če so na voljo), pod »torrent_paths« v ElasticSearch JSON-u."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Zakaj moj torrent odjemalec ne more odpreti nekaterih vaših torrent datotek / magnetnih povezav?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Nekateri torrent odjemalci ne podpirajo velikih velikosti kosov, kar imajo številni naši torrenti (pri novejših tega ne počnemo več — čeprav je to v skladu s specifikacijami!). Če naletite na to težavo, poskusite z drugim odjemalcem ali se pritožite proizvajalcem vašega torrent odjemalca."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Ali imate program za odgovorno razkritje?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Varnostne raziskovalce spodbujamo k iskanju ranljivosti v naših sistemih. Močno podpiramo odgovorno razkritje. Kontaktirajte nas <a %(a_contact)s>tukaj</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Trenutno ne moremo podeljevati nagrad za odkritje napak, razen za ranljivosti, ki imajo <a %(a_link)s>potencial ogroziti našo anonimnost</a>, za katere ponujamo nagrade v razponu od 10.000 do 50.000 USD. V prihodnosti bi radi razširili obseg nagrad za odkritje napak! Upoštevajte, da so napadi socialnega inženiringa izven obsega."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Če vas zanima ofenzivna varnost in želite pomagati pri arhiviranju svetovnega znanja in kulture, nas kontaktirajte. Obstaja veliko načinov, kako lahko pomagate."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ali obstajajo dodatni viri o Anninem Arhivu?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Annin Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — redne posodobitve"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Annina Programska Oprema</a> — naša odprtokodna koda"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Prevedi na Annini Programska Oprema</a> — naš prevajalski sistem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — o podatkih"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativne domene"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — več o nas (prosimo, pomagajte ohranjati to stran posodobljeno ali ustvarite eno za svoj jezik!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Kako prijavim kršitev avtorskih pravic?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Tukaj ne gostimo nobenih avtorsko zaščitenih materialov. Smo iskalnik in kot takšni indeksiramo le meta podatke, ki so že javno dostopni. Pri prenosu iz teh zunanjih virov vam priporočamo, da preverite zakone v vaši jurisdikciji glede tega, kaj je dovoljeno. Nismo odgovorni za vsebine, ki jih gostijo drugi."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Če imate pritožbe glede tega, kar vidite tukaj, je najbolje, da se obrnete na izvirno spletno stran. Redno posodabljamo njihove spremembe v naši bazi podatkov. Če resnično menite, da imate veljavno pritožbo po DMCA, na katero bi morali odgovoriti, izpolnite <a %(a_copyright)s>obrazec za pritožbo DMCA / avtorskih pravic</a>. Vaše pritožbe jemljemo resno in se bomo odzvali čim prej."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Sovražim, kako vodite ta projekt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Radi bi tudi spomnili vse, da je vsa naša koda in podatki popolnoma odprtokodni. To je edinstveno za projekte, kot je naš — ne poznamo nobenega drugega projekta s tako obsežnim katalogom, ki bi bil prav tako popolnoma odprtokoden. Zelo pozdravljamo vsakogar, ki misli, da naš projekt slabo vodimo, da vzame našo kodo in podatke ter postavi svojo lastno knjižnico v senci! To ne govorimo iz kljubovanja ali kaj podobnega — resnično mislimo, da bi bilo to super, saj bi dvignilo standard za vse in bolje ohranilo dediščino človeštva."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Ali imate monitor za čas delovanja?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Prosimo, oglejte si <a %(a_href)s>ta odličen projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Kako lahko doniram knjige ali druge fizične materiale?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Prosimo, pošljite jih na <a %(a_archive)s>Internet Archive</a>. Tam jih bodo ustrezno ohranili."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Kdo je Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Vi ste Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Kateri so vaši najljubši knjigi?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Tukaj je nekaj knjig, ki imajo poseben pomen za svet senčnih knjižnic in digitalnega ohranjanja:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Danes ste porabili vse hitre prenose."

msgid "page.fast_downloads.no_member"
msgstr "Postanite član, če želite uporabljati hitre prenose."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Zdaj podpiramo Amazon darilne kartice, kreditne in debetne kartice, kripto, Alipay in WeChat."

msgid "page.home.full_database.header"
msgstr "Celotna zbirka podatkov"

msgid "page.home.full_database.subtitle"
msgstr "Knjige, časopisi, revije, stripi, knjižnični zapisi, metapodatki, …"

msgid "page.home.full_database.search"
msgstr "Poišči"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub je <a %(a_paused)s>začasno ustavil</a> nalaganje novih člankov."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB je nadaljevanje Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Neposreden dostop do %(count)s akademskih člankov"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Odpri"

msgid "page.home.scidb.browser_verification"
msgstr "Če ste <a %(a_member)s>član</a>, preverjanje brskalnika ni potrebno."

msgid "page.home.archive.header"
msgstr "Dolgoročni arhiv"

msgid "page.home.archive.body"
msgstr "Nabori podatkov, uporabljeni v Aninem arhivu, so popolnoma odprti in jih je mogoče zrcaliti v velikem obsegu s pomočjo torrentov. <a %(a_datasets)s>Več o tem…</a>"

msgid "page.home.torrents.body"
msgstr "S sejanjem torrentov lahko ogromno pomagate. <a %(a_torrents)s>Več o tem...</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s sejalcev"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s sejalcev"

msgid "page.home.torrents.legend_greater"
msgstr "> %(count)s sejalcev"

msgid "page.home.llm.header"
msgstr "Podatki o urjenju LLM"

msgid "page.home.llm.body"
msgstr "Imamo največjo zbirko visokokakovostnih besedil na svetu. <a %(a_llm)s>Več o tem…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Zrcaljenje: poziv za prostovoljce"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Iščemo prostovoljce"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Kot neprofitni, odprtokodni projekt vedno iščemo ljudi, ki bi nam pomagali."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Če vodite visoko tvegano anonimno plačilno procesor, nas kontaktirajte. Prav tako iščemo ljudi, ki želijo postaviti okusne majhne oglase. Vsi prihodki gredo v naše prizadevanja za ohranitev."

msgid "layout.index.header.nav.annasblog"
msgstr "Anin Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Prenosi IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Vse povezave za prenos te datoteke: <a %(a_main)s>Glavna stran datoteke</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Prehod IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(morda boste morali z IPFS poskusiti večkrat)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Za hitrejše prenose in preskok preverjanja brskalnika <a %(a_membership)s>postanite član</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Za množično zrcaljenje naše zbirke si oglejte strani <a %(a_datasets)s>Nabori podatkov</a> in <a %(a_torrents)s>Torrenti</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM podatki"

#, fuzzy
msgid "page.llm.intro"
msgstr "Dobro je razumljeno, da LLM-ji uspevajo na visokokakovostnih podatkih. Imamo največjo zbirko knjig, člankov, revij itd. na svetu, ki so nekateri izmed najkakovostnejših besedilnih virov."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Edinstvena velikost in razpon"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Naša zbirka vsebuje več kot sto milijonov datotek, vključno z akademskimi revijami, učbeniki in revijami. To velikost dosežemo s kombiniranjem velikih obstoječih repozitorijev."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Nekatere naše izvorne zbirke so že na voljo v velikih količinah (Sci-Hub in deli Libgen). Druge vire smo osvobodili sami. <a %(a_datasets)s>Datasets</a> prikazuje celoten pregled."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Naša zbirka vključuje milijone knjig, člankov in revij iz obdobja pred e-knjigami. Veliki deli te zbirke so že OCR-irani in imajo že malo notranjega prekrivanja."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Kako vam lahko pomagamo"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Sposobni smo zagotoviti hiter dostop do naših celotnih zbirk, kot tudi do neizdanih zbirk."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "To je dostop na ravni podjetja, ki ga lahko zagotovimo za donacije v višini deset tisoč USD. Prav tako smo pripravljeni zamenjati to za visokokakovostne zbirke, ki jih še nimamo."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Lahko vam povrnemo, če nam lahko zagotovite obogatitev naših podatkov, kot so:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Odstranjevanje prekrivanja (deduplikacija)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Izvleček besedila in meta podatkov"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Podprite dolgoročno arhiviranje človeškega znanja, medtem ko pridobivate boljše podatke za svoj model!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontaktirajte nas</a> za pogovor o tem, kako lahko sodelujemo."

msgid "page.login.continue"
msgstr "Nadaljuj"

#, fuzzy
msgid "page.login.please"
msgstr "Prosimo, <a %(a_account)s>prijavite se</a> za ogled te strani.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Annin Arhiv je začasno nedostopen zaradi vzdrževanja. Prosimo, vrnite se čez eno uro."

#, fuzzy
msgid "page.metadata.header"
msgstr "Izboljšaj meta podatke"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Lahko pomagate pri ohranjanju knjig z izboljšanjem meta podatkov! Najprej preberite ozadje o meta podatkih na Anninem arhivu, nato pa se naučite, kako izboljšati meta podatke s povezovanjem z Open Library in si prislužite brezplačno članstvo na Anninem arhivu."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Ozadje"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Ko si ogledate knjigo na Anninem arhivu, lahko vidite različna polja: naslov, avtor, založnik, izdaja, leto, opis, ime datoteke in še več. Vse te informacije se imenujejo <em>meta podatki</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Ker združujemo knjige iz različnih <em>izvornih knjižnic</em>, prikazujemo vse meta podatke, ki so na voljo v tej izvorni knjižnici. Na primer, za knjigo, ki smo jo dobili iz Library Genesis, bomo prikazali naslov iz baze podatkov Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Včasih je knjiga prisotna v <em>več izvornih knjižnicah</em>, ki imajo lahko različna polja meta podatkov. V tem primeru preprosto prikažemo najdaljšo različico vsakega polja, saj ta verjetno vsebuje najbolj uporabne informacije! Še vedno bomo prikazali druga polja pod opisom, npr. kot \"alternativni naslov\" (vendar le, če so različni)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Prav tako izvlečemo <em>kode</em>, kot so identifikatorji in klasifikatorji, iz izvorne knjižnice. <em>Identifikatorji</em> edinstveno predstavljajo določeno izdajo knjige; primeri so ISBN, DOI, Open Library ID, Google Books ID ali Amazon ID. <em>Klasifikatorji</em> združujejo več podobnih knjig; primeri so Dewey Decimal (DCC), UDC, LCC, RVK ali GOST. Včasih so te kode izrecno povezane v izvornih knjižnicah, včasih pa jih lahko izvlečemo iz imena datoteke ali opisa (predvsem ISBN in DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Identifikatorje lahko uporabimo za iskanje zapisov v <em>zbirke samo z meta podatki</em>, kot so OpenLibrary, ISBNdb ali WorldCat/OCLC. V našem iskalniku je poseben <em>zavihek z meta podatki</em>, če želite brskati po teh zbirkah. Ujemajoče se zapise uporabljamo za izpolnjevanje manjkajočih polj meta podatkov (npr. če manjka naslov) ali npr. kot \"alternativni naslov\" (če že obstaja naslov)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Če želite natančno videti, od kod izvirajo meta podatki knjige, si oglejte <em>zavihek \"Tehnične podrobnosti\"</em> na strani knjige. Ima povezavo do surovega JSON-a za to knjigo, s kazalci na surovi JSON izvirnih zapisov."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Za več informacij si oglejte naslednje strani: <a %(a_datasets)s>Zbirke podatkov</a>, <a %(a_search_metadata)s>Iskanje (zavihek z meta podatki)</a>, <a %(a_codes)s>Raziskovalec kod</a> in <a %(a_example)s>Primer meta podatkov JSON</a>. Nazadnje lahko vse naše meta podatke <a %(a_generated)s>generirate</a> ali <a %(a_downloaded)s>preneste</a> kot baze podatkov ElasticSearch in MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Povezovanje z Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Torej, če naletite na datoteko s slabimi meta podatki, kako jo popraviti? Lahko greste v izvorno knjižnico in sledite njenim postopkom za popravljanje meta podatkov, vendar kaj storiti, če je datoteka prisotna v več izvornih knjižnicah?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Obstaja en identifikator, ki je na Anninem arhivu obravnavan posebej. <strong>Polje annas_archive md5 na Open Library vedno preglasi vse druge meta podatke!</strong> Najprej se vrnimo nazaj in se naučimo o Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library je leta 2006 ustanovil Aaron Swartz z namenom \"ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena\". Je nekakšna Wikipedija za meta podatke knjig: vsakdo jo lahko ureja, je prosto licencirana in jo je mogoče prenesti v celoti. Je baza podatkov o knjigah, ki je najbolj usklajena z našim poslanstvom — pravzaprav je Annin arhiv navdihnjen z vizijo in življenjem Aarona Swartza."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Namesto da bi izumljali kolo, smo se odločili preusmeriti naše prostovoljce na Open Library. Če vidite knjigo z napačnimi meta podatki, lahko pomagate na naslednji način:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Pojdite na <a %(a_openlib)s>spletno stran Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Poiščite pravilen zapis knjige. <strong>OPOZORILO:</strong> bodite prepričani, da izberete pravilno <strong>izdajo</strong>. V Open Library imate \"dela\" in \"izdaje\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "\"Delo\" bi lahko bilo \"Harry Potter in Kamen modrosti\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "\"Izdaja\" bi lahko bila:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Prva izdaja iz leta 1997, ki jo je izdal Bloomsbery, s 256 stranmi."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Mehka izdaja iz leta 2003, ki jo je izdal Raincoast Books, s 223 stranmi."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Poljski prevod iz leta 2000 “Harry Potter I Kamie Filozoficzn” pri založbi Media Rodzina s 328 stranmi."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Vse te izdaje imajo različne ISBN-je in različne vsebine, zato pazite, da izberete pravo!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Uredite zapis (ali ga ustvarite, če ne obstaja) in dodajte čim več uporabnih informacij! Zdaj ste tukaj, zato lahko zapis resnično izboljšate."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Pod “ID številke” izberite “Annin arhiv” in dodajte MD5 knjige iz Anninega arhiva. To je dolga vrsta črk in številk po “/md5/” v URL-ju."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Poskusite najti druge datoteke v Anninem arhivu, ki se prav tako ujemajo s tem zapisom, in jih dodajte. V prihodnosti jih lahko združimo kot podvojene na iskalni strani Anninega arhiva."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Ko končate, zapišite URL, ki ste ga pravkar posodobili. Ko boste posodobili vsaj 30 zapisov z MD5-ji iz Anninega arhiva, nam pošljite <a %(a_contact)s>e-pošto</a> in nam pošljite seznam. Dobili boste brezplačno članstvo za Annin arhiv, da boste lažje opravljali to delo (in kot zahvalo za vašo pomoč). To morajo biti visokokakovostne spremembe, ki dodajo znatne količine informacij, sicer bo vaša zahteva zavrnjena. Vaša zahteva bo prav tako zavrnjena, če bodo katere koli spremembe razveljavili ali popravili moderatorji Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Upoštevajte, da to velja samo za knjige, ne za akademske članke ali druge vrste datotek. Za druge vrste datotek še vedno priporočamo iskanje izvorne knjižnice. Lahko traja nekaj tednov, da se spremembe vključijo v Annin arhiv, saj moramo prenesti najnovejši podatkovni prenos Open Library in ponovno ustvariti naš iskalni indeks."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Zrcalniki: poziv za prostovoljce"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Za povečanje odpornosti Anninega arhiva iščemo prostovoljce za zagon zrcalnikov."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Iščemo naslednje:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Upravljate odprtokodno bazo kode Anninega arhiva in redno posodabljate tako kodo kot podatke."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Vaša različica je jasno označena kot zrcalo, npr. »Bobov arhiv, zrcalo Anninega arhiva«."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Pripravljeni ste prevzeti tveganja, povezana s tem delom, ki so precejšnja. Imate globoko razumevanje potrebne operativne varnosti. Vsebina <a %(a_shadow)s>teh</a> <a %(a_pirate)s>objav</a> vam je samoumevna."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Pripravljeni ste prispevati k naši <a %(a_codebase)s>bazi kode</a> — v sodelovanju z našo ekipo — da bi to uresničili."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Sprva vam ne bomo omogočili dostopa do prenosov s partnerskih strežnikov, vendar če bo šlo vse dobro, lahko to delimo z vami."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Stroški gostovanja"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Pripravljeni smo kriti stroške gostovanja in VPN, sprva do 200 $ na mesec. To je dovolj za osnovni iskalni strežnik in proxy zaščiten z DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Stroške gostovanja bomo plačali šele, ko boste vse nastavili in dokazali, da lahko arhiv posodabljate z novostmi. To pomeni, da boste morali prve 1-2 mesece plačati iz lastnega žepa."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Vaš čas ne bo plačan (in tudi naš ne), saj gre za čisto prostovoljno delo."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Če se boste znatno vključili v razvoj in delovanje našega dela, lahko razpravljamo o delitvi večjega dela donacijskih prihodkov z vami, da jih uporabite po potrebi."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Začetek"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Prosimo <strong>ne kontaktirajte nas</strong> za dovoljenje ali za osnovna vprašanja. Dejanja govorijo glasneje kot besede! Vse informacije so na voljo, zato kar nadaljujte z nastavitvijo svojega zrcala."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Prosto objavite vstopnice ali zahteve za združitev na našem Gitlabu, ko naletite na težave. Morda bomo morali z vami razviti nekatere funkcije, specifične za zrcalo, kot so preimenovanje iz »Annin arhiv« v ime vaše spletne strani, (sprva) onemogočanje uporabniških računov ali povezovanje nazaj na našo glavno stran s strani knjig."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Ko imate svoje zrcalo v delovanju, nas prosim kontaktirajte. Radi bi pregledali vašo OpSec, in ko bo to trdno, bomo povezali vaše zrcalo in začeli tesneje sodelovati z vami."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Hvala vnaprej vsem, ki so pripravljeni prispevati na ta način! To ni za tiste s šibkim srcem, vendar bi utrdilo dolgoživost največje resnično odprte knjižnice v zgodovini človeštva."

msgid "page.partner_download.header"
msgstr "Prenesite s partnerskega spletnega mesta"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Počasni prenosi so na voljo samo prek uradne spletne strani. Obiščite %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Počasni prenosi niso na voljo prek Cloudflare VPN-jev ali z drugih Cloudflare IP-naslovov."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Prosimo, počakajte <span %(span_countdown)s>%(wait_seconds)s</span> sekund, da prenesete to datoteko."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Za prenos uporabite naslednji URL: <a %(a_download)s>Prenesi zdaj</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Hvala za čakanje, to omogoča brezplačen dostop do spletne strani za vse! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Opozorilo: v zadnjih 24 urah je bilo veliko prenosov z vašega naslova IP. Prenosi so lahko počasnejši kot običajno."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Prenosi z vašega IP naslova v zadnjih 24 urah: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Če uporabljate VPN, deljeno internetno povezavo ali vaš ponudnik internetnih storitev deli IP naslove, je to opozorilo morda posledica tega."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Da bi vsem omogočili prenos datotek brezplačno, morate počakati, preden lahko prenesete to datoteko."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Med čakanjem lahko še naprej brskate po Anninem Arhivu v drugem zavihku (če vaš brskalnik podpira osveževanje zavihkov v ozadju)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Prosto počakajte, da se naloži več strani za prenos hkrati (vendar prosimo, da naenkrat prenesete samo eno datoteko na strežnik)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Ko prejmete povezavo za prenos, je veljavna več ur."

msgid "layout.index.header.title"
msgstr "Anin arhiv"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Zapis v Aninem arhivu"

msgid "page.scidb.download"
msgstr "Prenesi"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Za podporo dostopnosti in dolgoročno ohranitev človeškega znanja postanite <a %(a_donate)s>član</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Kot bonus, 🧬&nbsp;SciDB se nalaga hitreje za člane, brez omejitev."

msgid "page.scidb.refresh"
msgstr "Ne deluje? Poskusite <a %(a_refresh)s>osvežiti stran</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Predogled še ni na voljo. Prenesite datoteko iz <a %(a_path)s>Annin arhiv</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB je nadaljevanje Sci-Hub, z znanim vmesnikom in neposrednim ogledom PDF-jev. Vnesite svoj DOI za ogled."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Imamo celotno zbirko Sci-Hub, kot tudi nove članke. Večino si lahko ogledate neposredno z znanim vmesnikom, podobnim Sci-Hub. Nekatere lahko prenesete preko zunanjih virov, v tem primeru vam prikažemo povezave do teh."

msgid "page.search.title.results"
msgstr "%(search_input)s - Iskanje"

msgid "page.search.title.new"
msgstr "Novo iskanje"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Vključite samo"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Izključite"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nepreverjeno"

msgid "page.search.tabs.download"
msgstr "Prenesi"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Revijalni članki"

msgid "page.search.tabs.digital_lending"
msgstr "Digitalno posojanje"

msgid "page.search.tabs.metadata"
msgstr "Meta podatki"

msgid "common.search.placeholder"
msgstr "Naslov, avtor, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Iskanje"

msgid "page.search.search_settings"
msgstr "Nastavitve iskanja"

msgid "page.search.submit"
msgstr "Poišči"

msgid "page.search.too_long_broad_query"
msgstr "Iskanje je trajalo predolgo, kar je običajno za široke poizvedbe. Število filtrov morda ni natančno."

msgid "page.search.too_inaccurate"
msgstr "Iskanje je trajalo predolgo, kar pomeni, da boste morda videli napačne rezultate. Včasih <a %(a_reload)s>ponovno nalaganje</a> strani pomaga."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Prikaz"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Seznam"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabela"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Napredno"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Išči opise in komentarje meta podatkov"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Dodaj specifično iskalno polje"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(iskanje specifičnega polja)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Leto izdaje"

msgid "page.search.filters.content.header"
msgstr "Vsebina"

msgid "page.search.filters.filetype.header"
msgstr "File type"

msgid "page.search.more"
msgstr "več…"

msgid "page.search.filters.access.header"
msgstr "Dostop"

msgid "page.search.filters.source.header"
msgstr "Vir"

msgid "page.search.filters.source.scraped"
msgstr "postrgal in odprl kodo AA"

msgid "page.search.filters.language.header"
msgstr "Jezik"

msgid "page.search.filters.order_by.header"
msgstr "Razvrsti po"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Najbolj relevantno"

msgid "page.search.filters.sorting.newest"
msgstr "Najnovejša"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(leto objave)"

msgid "page.search.filters.sorting.oldest"
msgstr "Najstarejši"

msgid "page.search.filters.sorting.largest"
msgstr "Največji"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(velikost datoteke)"

msgid "page.search.filters.sorting.smallest"
msgstr "Najmanjši"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(odprtokodno)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Naključno"

msgid "page.search.header.update_info"
msgstr "Iskalni indeks se posodablja mesečno. Trenutno vključuje vnose do %(last_data_refresh_date)s. Za več tehničnih informacij glejte %(link_open_tag)sstran z nabori podatkov</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Za raziskovanje iskalnega indeksa po kodah uporabite <a %(a_href)s>Raziskovalec kod</a>."

msgid "page.search.results.search_downloads"
msgstr "Vnesite iskalni pojem v polje za iskanje po našem katalogu %(count)s datotek, ki jih je mogoče neposredno prenesti in jih <a %(a_preserve)s>hranimo za vedno</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Pravzaprav lahko vsakdo pomaga ohraniti te datoteke z deljenjem našega <a %(a_torrents)s>enotnega seznama torrentov</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Trenutno imamo najobsežnejši odprti katalog knjig, člankov in drugih pisnih del na svetu. Zrcalimo Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>in še več</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Če najdete druge »senčne knjižnice«, ki bi jih morali zrcaliti, ali če imate kakršna koli vprašanja, nam pišite na %(email)s."

msgid "page.search.results.dmca"
msgstr "Za zahtevke glede DMCA / avtorskih pravic <a %(a_copyright)s>kliknite tukaj</a>."

msgid "page.search.results.shortcuts"
msgstr "Namig: za hitrejšo navigacijo uporabite bližnjične tipke »/« (fokus iskanja), »enter« (iskanje), »j« (gor), »k« (dol)."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Iščete članke?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Vnesite v polje za iskanje našega kataloga %(count)s akademskih člankov in revij, ki jih <a %(a_preserve)s>ohranjamo za vedno</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Vnesite v polje iskalni pojem za iskanje datotek v knjižnicah digitalne izposoje."

msgid "page.search.results.digital_lending_info"
msgstr "Ta iskalni indeks trenutno vključuje meta podatke iz knjižnice Internet Archive’s Controlled Digital Lending. <a %(a_datasets)s>Več o naših naborih podatkov</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Za več digitalnih knjižnic za izposojo poglejte na <a %(a_wikipedia)s>Wikipedia</a> in <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Vnesite v polje islani pojem za iskanje meta podatkov iz knjižnic. To je lahko uporabno, ko <a %(a_request)s>zahtevate datoteko</a>."

msgid "page.search.results.metadata_info"
msgstr "Ta iskalni indeks trenutno vključuje meta podatke iz različnih virov meta podatkov. <a %(a_datasets)s>Več o naših naborih podatkov</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Za meta podatke prikazujemo izvirne zapise. Ne združujemo zapisov."

msgid "page.search.results.metadata_info_more"
msgstr "Obstaja veliko, veliko virov meta podatkov za pisna dela po vsem svetu. <a %(a_wikipedia)s>Ta stran Wikipedije</a> je dober začetek, vendar če poznate tudi druge dobre sezname, nam to sporočite."

msgid "page.search.results.search_generic"
msgstr "Vnesite iskalni pojem v polje za iskanje."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "To so meta podatki, <span %(classname)s>ne</span> datoteke za prenos."

msgid "page.search.results.error.header"
msgstr "Napaka med iskanjem."

msgid "page.search.results.error.unknown"
msgstr "Poskusite <a %(a_reload)s>ponovno naložiti stran</a>. Če težave ne odpravite, nam pišite na %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Ni najdenih datotek.</span> Poskusite z manj iskalnimi izrazi in filtri ali z različnimi iskalnimi izrazi."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Včasih se to zgodi nepravilno, ko je strežnik za iskanje počasen. V takih primerih lahko pomaga <a %(a_attrs)s>ponovno nalaganje</a>."

msgid "page.search.found_matches.main"
msgstr "Našli smo ujemanja v: %(in)s. Ko <a %(a_request)s>zahtevate datoteko</a>, se lahko sklicujete na tamkajšnji URL."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Revijalni članki (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Digitalno posojanje (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Meta podatki (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Rezultati %(from)s-%(to)s (%(total)s skupaj)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ delna ujemanja"

msgid "page.search.results.partial"
msgstr "%(num)d delna ujemanja"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Prostovoljstvo in nagrade"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Annin arhiv se zanaša na prostovoljce, kot ste vi. Pozdravljamo vse ravni zavezanosti in iščemo pomoč v dveh glavnih kategorijah:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lahka prostovoljska dela:</span> če lahko le občasno namenite nekaj ur, je še vedno veliko načinov, kako lahko pomagate. Dosledne prostovoljce nagrajujemo z <span %(bold)s>🤝 članstvi v Anninem arhivu</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Težko prostovoljno delo (nagrade od 50 do 5.000 USD):</span> če lahko posvetite veliko časa in/ali virov naši misiji, bi radi tesneje sodelovali z vami. Sčasoma se lahko pridružite notranji ekipi. Čeprav imamo omejen proračun, lahko za najintenzivnejše delo podelimo <span %(bold)s>💰 denarne nagrade</span>."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Če ne morete prostovoljno prispevati svojega časa, nam lahko še vedno veliko pomagate z <a %(a_donate)s>doniranjem denarja</a>, <a %(a_torrents)s>sejanjem naših torrentov</a>, <a %(a_uploading)s>nalaganjem knjig</a> ali <a %(a_help)s>povedovanjem prijateljem o Anninem arhivu</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Podjetja:</span> ponujamo hiter neposreden dostop do naših zbirk v zameno za donacijo na ravni podjetja ali izmenjavo za nove zbirke (npr. novi skeni, OCR-irani datasets, obogatitev naših podatkov). <a %(a_contact)s>Kontaktirajte nas</a>, če ste to vi. Oglejte si tudi našo <a %(a_llm)s>stran LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Lahko prostovoljno delo"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Če imate nekaj ur na voljo, lahko pomagate na več načinov. Bodite prepričani, da se pridružite <a %(a_telegram)s>prostovoljnemu klepetu na Telegramu</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Kot znak hvaležnosti običajno podelimo 6 mesecev “Srečnega knjižničarja” za osnovne mejnike in več za nadaljnje prostovoljno delo. Vsi mejniki zahtevajo visokokakovostno delo — površno delo nam škodi bolj kot koristi in ga bomo zavrnili. Prosimo, <a %(a_contact)s>pošljite nam e-pošto</a>, ko dosežete mejnik."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Naloga"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Mejnik"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Širjenje besede o Anninem arhivu. Na primer, z priporočanjem knjig na AA, povezovanjem na naše objave na blogu ali splošnim usmerjanjem ljudi na našo spletno stran."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s povezav ali posnetkov zaslona."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "To bi moralo pokazati, da nekomu poveste o Anninem arhivu, in da se vam zahvalijo."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Izboljšajte meta podatke z <a %(a_metadata)s>povezovanjem</a> z Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Lahko uporabite <a %(a_list)s >seznam naključnih težav z metapodatki</a> kot izhodišče."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Poskrbite, da boste pustili komentar na težave, ki jih odpravite, da drugi ne bodo podvajali vašega dela."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s povezav zapisov, ki ste jih izboljšali."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Prevedite</a> spletno stran."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Popolnoma prevedite jezik (če ni bil že skoraj dokončan)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Izboljšajte stran Wikipedije za Annin arhiv v vašem jeziku. Vključite informacije iz Wikipedijine strani AA v drugih jezikih ter z naše spletne strani in bloga. Dodajte reference na AA na drugih ustreznih straneh."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Povezava do zgodovine urejanja, ki prikazuje vaše pomembne prispevke."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Izpolnjevanje zahtev za knjige (ali članke itd.) na forumih Z-Library ali Library Genesis. Nimamo lastnega sistema za zahteve po knjigah, vendar zrcalimo te knjižnice, zato jih izboljšanje izboljša tudi Annin arhiv."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s povezav ali posnetkov zaslona izpolnjenih zahtev."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Majhne naloge, objavljene v našem <a %(a_telegram)s>prostovoljnem klepetu na Telegramu</a>. Običajno za članstvo, včasih za majhne nagrade."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Majhne naloge objavljene v naši prostovoljski klepetalni skupini."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Odvisno od naloge."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Nagrade"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Vedno iščemo ljudi s trdnimi programerskimi ali ofenzivnimi varnostnimi veščinami, da se vključijo. Lahko naredite resen prispevek k ohranjanju dediščine človeštva."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Kot zahvalo podarjamo članstvo za trdne prispevke. Kot veliko zahvalo podarjamo denarne nagrade za posebej pomembne in težke naloge. To ne bi smelo biti nadomestilo za službo, vendar je dodatna spodbuda in lahko pomaga pri nastalih stroških."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Večina naše kode je odprtokodna, in to bomo zahtevali tudi od vaše kode, ko bomo podeljevali nagrado. Obstajajo nekatere izjeme, o katerih se lahko pogovorimo individualno."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Nagrade se podeljujejo prvi osebi, ki dokonča nalogo. Prosto komentirajte na vstopnici za nagrado, da drugim sporočite, da delate na nečem, da se lahko drugi zadržijo ali vas kontaktirajo za sodelovanje. Vendar bodite pozorni, da so tudi drugi še vedno svobodni delati na tem in poskušati biti hitrejši od vas. Vendar pa ne podeljujemo nagrad za površno delo. Če sta dve visokokakovostni oddaji narejeni blizu skupaj (v enem ali dveh dneh), se lahko odločimo, da podelimo nagrade obema, po naši presoji, na primer 100%% za prvo oddajo in 50%% za drugo oddajo (torej skupno 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Za večje nagrade (zlasti nagrade za strganje podatkov), nas prosimo kontaktirajte, ko ste dokončali ~5%% tega, in ste prepričani, da bo vaša metoda obsegala celoten mejnik. Morali boste deliti svojo metodo z nami, da bomo lahko podali povratne informacije. Prav tako lahko na ta način odločimo, kaj storiti, če se več ljudi približuje nagradi, na primer potencialno podelitev nagrade več ljudem, spodbujanje ljudi k sodelovanju itd."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "OPOZORILO: naloge z visokimi nagradami so <span %(bold)s>težke</span> — morda bi bilo pametno začeti z lažjimi."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Pojdite na naš <a %(a_gitlab)s>seznam težav na Gitlabu</a> in razvrstite po “Label priority”. To približno prikazuje vrstni red nalog, ki so nam pomembne. Naloge brez izrecnih nagrad so še vedno upravičene do članstva, zlasti tiste označene kot “Accepted” in “Annin favorit”. Morda bi želeli začeti s “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Posodobitve o <a %(wikipedia_annas_archive)s>Arhivu Anne</a>, največji resnično odprti knjižnici v zgodovini človeštva."

msgid "layout.index.title"
msgstr "Anin arhiv"

msgid "layout.index.meta.description"
msgstr "Največja odprtokodna knjižnica odprtih podatkov na svetu. Mirrors Sci-Hub, Library Genesis, Z-Library in drugo."

msgid "layout.index.meta.opensearch"
msgstr "Preiščite Annin arhiv"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annin arhiv potrebuje vašo pomoč!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Mnogi nas poskušajo odstraniti, vendar se ne damo."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Če donirate zdaj, prejmete <strong>dvojno</strong> število hitrih prenosov."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Velja do konca tega meseca."

msgid "layout.index.header.nav.donate"
msgstr "Donirajte"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Shranjevanje človeškega znanja: odlično darilo za praznike!"

msgid "layout.index.header.banner.surprise"
msgstr "Presenetite ljubljeno osebo, podarite ji račun s članstvom."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Za povečanje odpornosti Anninega arhiva iščemo prostovoljce za zagon zrcalnih strežnikov."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Popolno valentinovo darilo!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Na voljo imamo nov način darovanja: %(method_name)s. Razmislite o %(donate_link_open_tag)sdoniranju</a> – vodenje tega spletnega mesta ni poceni in vaša donacija resnično naredi razliko. Najlepša hvala."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Izvajamo zbiranje sredstev za <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">varnostno kopiranje</a> največje senčne knjižnice za stripe na svetu. Hvala za tvojo podporo! <a href=\"/donate\">Donirajte.</a> Če ne morete donirati, nas podprite tako, da to sporočite svojim prijateljem in nas spremljate na <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> ali <a href=\"https://t.me/annasarchiveorg\">Telegram </a>."

msgid "layout.index.header.recent_downloads"
msgstr "Nedavni prenosi:"

msgid "layout.index.header.nav.search"
msgstr "Iskanje"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Pogosta vprašanja"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Izboljšaj meta podatke"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Prostovoljstvo & Nagrade"

msgid "layout.index.header.nav.datasets"
msgstr "Nabori podatkov"

msgid "layout.index.header.nav.torrents"
msgstr "Torrenti"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Dejavnost"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Raziskovalec kod"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM podatki"

msgid "layout.index.header.nav.home"
msgstr "Vstopna stran"

msgid "layout.index.header.nav.annassoftware"
msgstr "Annina programska oprema ↗"

msgid "layout.index.header.nav.translate"
msgstr "Prevedi ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Prijavite se / Registrirajte se"

msgid "layout.index.header.nav.account"
msgstr "Račun"

msgid "layout.index.footer.list1.header"
msgstr "Anin arhiv"

msgid "layout.index.footer.list2.header"
msgstr "Ostanite v stiku"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / zahtevki glede avtorskih pravic"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Napredno"

msgid "layout.index.header.nav.security"
msgstr "Varnost"

msgid "layout.index.footer.list3.header"
msgstr "Alternative"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nepovezan"

msgid "page.search.results.issues"
msgstr "❌ Ta datoteka ima lahko težave."

msgid "page.search.results.fast_download"
msgstr "Hitri prenos"

msgid "page.donate.copy"
msgstr "kopiraj"

msgid "page.donate.copied"
msgstr "kopirano!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Prejšnje"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Naslednji"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Knjiga (katerakoli)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Domov"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datasets ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Ni najdeno"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "»%(isbn_input)s« ni veljavna ISBN številka. ISBN števike so dolge 10 ali 13 znakov, ne da bi šteli neobvezne pomišljaje. Vsi znaki morajo biti številke, razen zadnjega znaka, ki je lahko tudi »X«. Zadnji znak je \"kontrolna številka\", ki se mora ujemati z vrednostjo kontrolne vsote, ki je izračunana iz drugih števil. Prav tako mora biti v veljavnem obsegu, ki ga dodeli Mednarodna agencija za ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Ujemajoče se datoteke v naši bazi podatkov:"

#~ msgid "page.isbn.results.none"
#~ msgstr "V naši zbirki podatkov ni bilo najdenih ustreznih datotek."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Iskanje ▶ %(num)d+ rezultatov za <span class=\"italic\">%(search_input)s</span> (v metapodatkih knjižnice v senci)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Iskanje ▶ %(num)d rezultatov za <span class=\"italic\">%(search_input)s</span> (v metapodatkih senčne knjižnice)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Iskanje ▶ Napaka pri iskanju <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Iskanje ▶ Novo iskanje"

#~ msgid "page.donate.header.text3"
#~ msgstr "Donirate lahko tudi brez ustvarjanja računa:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Enkratna donacija (brez ugodnosti)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Izberite možnost plačila. Prosimo, razmislite o uporabi kripto plačila %(bitcoin_icon)s, ker imamo (veliko) manj stroškov."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Če že imate kripto valute, so to naši naslovi."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Najlepša hvala za pomoč! Brez vas tega projekta ne bi bilo."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Za doniranje s PayPal US bomo uporabili PayPal Crypto, ki nam omogoča, da ostanemo anonimni. Cenimo, da ste si vzeli čas in se naučili darovati s to metodo, saj nam zelo pomaga."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Sledite navodilom za nakup Bitcoin (BTC). Kupiti morate le znesek, ki ga želite donirati."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Če izgubite nekaj bitcoinov zaradi nihanj ali provizij, <em>ne skrbite</em>. To je normalno pri kriptovaluti, vendar nam omogoča, da delujemo anonimno."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Vnesite naš Bitcoin (BTC) naslov kot prejemnika in sledite navodilom za pošiljanje donacije:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Za pošiljanje donacije uporabite <a %(a_account)s>ta račun Alipay</a>."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Za pošiljanje donacije uporabite <a %(a_account)s>ta račun Pix</a>."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Če vašega plačilnega sredstva ni na seznamu, bi bilo najlažje prenesti <a href=\"https://paypal.com/\">PayPal</a> ali <a href=\"https://coinbase .com/\">Coinbase</a> v telefonu in tam kupite delček Bitcoina (BTC). Nato ga lahko pošljete na naš naslov: %(address)s. V večini držav bi ta nastavitev trajala le nekaj minut."

#~ msgid "page.search.results.error.text"
#~ msgstr "Poskusite <a href=\"javascript:location.reload()\">ponovno naložiti stran</a>. Če težave ne odpravite, nam to sporočite na <a href=\"https://www.reddit.com/r/Annas_Archive /\">Reddit</a> ali <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Če želite postati član, se <a href=\"/login\">prijavite ali registrirajte</a>. Če ne želite ustvariti računa, izberite zgoraj »Izvedi enkratno anonimno donacijo«. Hvala za tvojo podporo!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Domov"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "O tem"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donirajte"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Nabori podatkov"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Mobilna aplikacija"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anin blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Annina programska oprema"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Prevedite"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr "Hranimo knjige, časopise, stripe, revije in druge publikacije tako, da ta gradiva iz različnih <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">senčnih knjižnic</a> združimo na enem mestu. Vsi ti podatki so ohranjeni za vedno, saj jih je enostavno podvajati v velikem obsegu, kar ima za posledico veliko kopij po vsem svetu. Zaradi te široke distribucije v kombinaciji z odprtokodno kodo je naše spletno mesto odporno na odstranitve. Več o <a href=\"/datasets\">naših naborih podatkov</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datasets ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Ni najdeno"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" ni videti kot DOI. Začeti bi se moralo z \"10.\" in vsebovati poševnico (/)."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Kanonični URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Ta datoteka je morda v %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Ujemajoče se datoteke v naši bazi podatkov:"

#~ msgid "page.doi.results.none"
#~ msgstr "V naši zbirki podatkov ni bilo najdenih ustreznih datotek."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Hitri prenosi</strong> Za danes vam je zmanjkalo hitrih prenosov. Prosimo, kontaktirajte Anno na %(email)s, če vas zanima nadgradnja članstva."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Danes vam je zmanjkalo hitrih prenosov. Kontaktirajte Anno na %(email)s, če vas zanima nadgradnja članstva."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Ali lahko prispevam na druge načine?</div> Da! Oglejte si <a href=\"/about\">stran o tem</a> pod “Kako pomagati”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Ni mi všeč, da »monetizirate« Annin arhiv!</div> Če vam ni všeč, kako upravljamo naš projekt, zaženite svojo knjižnico v senci! Vsa naša koda in podatki so odprtokodni, zato za to ovir. ;)"

#~ msgid "page.request.title"
#~ msgstr "Zahtevajte knjige"

#~ msgid "page.request.text1"
#~ msgstr "Ali lahko zaenkrat izrazite željo po e-knjigah na <a %(a_forum)s>forumu Libgen.rs</a>? Tam lahko ustvarite račun in objavite željo v eni od teh niti:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Za e-knjige uporabite <a %(a_ebook)s>to nit</a>.</li><li %(li_item)s>Za knjige, ki niso na voljo kot e-knjige, uporabite <a %(a_regular)s>to nit</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "V obeh primerih upoštevajte pravila, navedena v temah."

#~ msgid "page.upload.title"
#~ msgstr "Naloži"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Velika nalaganja"

#~ msgid "page.about.title"
#~ msgstr "O projektu"

#~ msgid "page.about.header"
#~ msgstr "O projektu"

#~ msgid "page.home.search.header"
#~ msgstr "Iskanje"

#~ msgid "page.home.search.intro"
#~ msgstr "Preiščite naš katalog."

#~ msgid "page.home.random_book.header"
#~ msgstr "Naključna knjiga"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Pojdite na naključno izbrano knjigo iz kataloga."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Naključna knjiga"

#~ msgid "page.about.text1"
#~ msgstr "Annin arhiv je neprofitni odprtokodni iskalnik za »<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">senčne knjižnice</a>«. Ustvarila ga je <a href=\"http://annas-blog.org\">Anna</a>, ki je menila, da obstaja potreba po osrednjem spletnem mestu za iskanje knjig, časopisov, stripov, revij in drugih dokumentov."

#~ msgid "page.about.text4"
#~ msgstr "Če imate veljavno pritožbo DMCA, si oglejte dno te strani ali nas kontaktirajte na %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Raziščite knjige"

#~ msgid "page.home.explore.intro"
#~ msgstr "To je kombinacija priljubljenih knjig in knjig, ki imajo poseben pomen v svetu senčnih knjižnic in digitalnega arhiviranja."

#~ msgid "page.wechat.header"
#~ msgstr "Neuradni WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "Imamo neuradno stran WeChat, ki jo vzdržuje član skupnosti. Za dostop uporabite spodnjo kodo."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "O tem"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Mobilna aplikacija"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Neuradni WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Zahtevajte knjige"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Naloži"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Priporoči prijatelje"

#~ msgid "page.about.help.header"
#~ msgstr "Kako pomagati"

#~ msgid "page.refer.title"
#~ msgstr "Priporočite prijatelje, da prejmete dodatne hitre prenose"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Člani lahko priporočijo prijatelje in si prislužijo dodatne prenose."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Za vsakega prijatelja, ki postane član:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Oni</strong> prejmejo %(percentage)s%% dodatnih prenosov poleg običajnih dnevnih prenosov v času trajanja članstva."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Vi</strong> prejmete enako število dodatnih prenosov poleg običajnih dnevnih prenosov, za enako obdobje, kot se je prijavil vaš prijatelj (do skupno %(max)s skupnih dodatnih prenosov v danem trenutku). Če želite uporabljati dodatne prenose, morate biti vaše članstvo aktivno."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Primer:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Vaš prijatelj uporabi vašo referenčno povezavo, da se prijavi za 3-mesečno članstvo v »Lucky Librarian«, ki vključuje %(num)s hitrih prenosov."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Vsak dan prejmejo %(num)s bonus prenosov v obdobju teh 3 mesecev."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Prav tako prejmete %(num)s bonus prenosov vsak dan za iste obdobje 3 mesecev."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Napotitvena povezava:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Prijavite se</a> in postanite član, da napotite prijatelje."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Postanite član</a> in priporočite prijatelje."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Ali dodajte %(referral_suffix)s na koncu katere koli druge povezave in napotitev bo shranjena, ko uporabnik postane član."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Donirajte skupni znesek %(total)s z uporabo <a %(a_account)s>tega računa Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Lahko pa jih <a %(a_upload)s>naložite</a> v Z-Library."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Da bi povečali odpornost Aninega arhiva, iščemo prostovoljce za vodenje zrcalnih strežnikov. <a href=\"/mirrors\">Več o tem…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Zrcalni strežniki: razpis za prostovoljce"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "samo ta mesec!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub je <a %(a_closed)s>začasno ustavil</a> nalaganje novih člankov."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Izberite možnost plačila. Dajemo popuste za kripto plačila %(bitcoin_icon)s, ker imamo (veliko) manj stroškov."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Izberite možnost plačila. Trenutno so na voljo le kripto-osnovana plačila %(bitcoin_icon)s, ker tradicionalni plačilni procesorji nočejo sodelovati z nami."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Ne moremo neposredno podpirati kreditnih/debetnih kartic, ker banke nočejo sodelovati z nami. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Vendar pa obstaja več načinov za uporabo kreditnih/debetnih kartic, z uporabo naših drugih plačilnih metod:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Počasni in zunanji prenosi"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Prenosi"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Če prvič uporabljate kriptovalute, predlagamo, da uporabite %(option1)s, %(option2)s ali %(option3)s za nakup in donacijo Bitcoin-ov (prvotna in najpogosteje uporabljena kriptovaluta)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 povezav zapisov, ki ste jih izboljšali."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 povezav ali posnetkov zaslona."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 povezav ali posnetkov zaslona izpolnjenih zahtev."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Če vas zanima zrcaljenje teh datasets za <a %(a_faq)s>arhivske</a> ali <a %(a_llm)s>LLM trening</a> namene, nas prosimo kontaktirajte."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Če vas zanima zrcaljenje tega nabora podatkov za <a %(a_archival)s>arhivske</a> ali <a %(a_llm)s>LLM trening</a> namene, nas prosimo kontaktirajte."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Glavna spletna stran"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informacije o državah ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Če vas zanima zrcaljenje tega nabora podatkov za namene <a %(a_archival)s>arhiviranja</a> ali <a %(a_llm)s>usposabljanja LLM</a>, nas prosimo kontaktirajte."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Mednarodna agencija za ISBN redno objavlja obsege, ki jih je dodelila nacionalnim agencijam za ISBN. Iz tega lahko izpeljemo, kateri državi, regiji ali jezikovni skupini pripada ta ISBN. Trenutno te podatke uporabljamo posredno, prek Python knjižnice <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Viri"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Zadnja posodobitev: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Spletna stran ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Meta podatki"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Izključujoč “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Naš navdih za zbiranje metapodatkov je cilj Aarona Swartza o »eni spletni strani za vsako kdaj objavljeno knjigo«, za kar je ustvaril <a %(a_openlib)s>Odprto knjižnico</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ta projekt je uspel, vendar nam naš edinstven položaj omogoča, da dobimo metapodatke, ki jih oni ne morejo."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Navdih je bila tudi naša želja vedeti, <a %(a_blog)s>koliko knjig je na svetu</a>, da bi lahko izračunali, koliko knjig nam je še ostalo za shraniti."

#~ msgid "page.partner_download.text1"
#~ msgstr "Da bi vsem omogočili brezplačen prenos datotek, morate počakati <strong>%(wait_seconds)s sekund</strong>, preden lahko prenesete to datoteko."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Samodejno osveži stran. Če zamudite okno za prenos, se bo časovnik ponovno zagnal, zato je priporočljivo samodejno osveževanje."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Prenesi zdaj"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Pretvori: uporabite spletna orodja za pretvorbo med formati. Na primer, za pretvorbo med epub in pdf uporabite <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: prenesite datoteko (podprta sta pdf ali epub), nato jo <a %(a_kindle)s>pošljite na Kindle</a> prek spleta, aplikacije ali e-pošte. Uporabna orodja: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Podprite avtorje: če vam je to všeč in si lahko privoščite, razmislite o nakupu izvirnika ali podpori neposredno avtorjem."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Podprite knjižnice: Če je to na voljo v vaši lokalni knjižnici, razmislite o tem, da bi si ga tam brezplačno izposodili."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Ni na voljo neposredno v velikih količinah, le v pol-velikih količinah za plačilnim zidom"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annin arhiv upravlja zbirko <a %(isbndb)s>ISBNdb metapodatkov</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb je podjetje, ki zbira meta podatke o ISBN iz različnih spletnih knjigarn. Annin Arhiv je izdelal varnostne kopije meta podatkov o knjigah iz ISBNdb. Ti meta podatki so na voljo preko Anninega Arhiva (trenutno niso vključeni v iskanje, razen če izrecno iščete številko ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Za tehnične podrobnosti glejte spodaj. Na neki točki jih lahko uporabimo za določitev, katere knjige še manjkajo v senčnih knjižnicah, da bi lahko dali prednost iskanju in/ali skeniranju teh knjig."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Naša objava na blogu o teh podatkih"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Zbiranje podatkov iz ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Trenutno imamo en sam torrent, ki vsebuje 4,4GB stisnjeno <a %(a_jsonl)s>JSON Lines</a> datoteko (20GB razpakirano): “isbndb_2022_09.jsonl.gz”. Za uvoz “.jsonl” datoteke v PostgreSQL, lahko uporabite nekaj takega kot <a %(a_script)s>ta skripta</a>. Lahko jo celo neposredno usmerite z nečim podobnim %(example_code)s, da se sproti dekompresira."

#~ msgid "page.donate.wait"
#~ msgstr "Prosimo, počakajte vsaj <span %(span_hours)s>dve uri</span> (in osvežite to stran) preden nas kontaktirate."

#~ msgid "page.codes.search_archive"
#~ msgstr "Išči po Anninem arhivu za “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donirajte z Alipay ali WeChat. Med njimi lahko izbirate na naslednji strani."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Širite besedo o Anninem arhivu na družbenih omrežjih in spletnih forumih, priporočajte knjige ali sezname na AA ali odgovarjajte na vprašanja."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Zbirka leposlovja se je razšla, vendar še vedno ima <a %(libgenli)s>torrente</a>, čeprav niso posodobljeni od leta 2022 (imamo pa neposredne prenose)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annin arhiv in Libgen.li skupaj upravljata zbirke <a %(comics)s>stripov</a> in <a %(magazines)s>revij</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Ni torrentov za rusko leposlovje in zbirke standardnih dokumentov."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Ni torrentov za dodatno vsebino. Torrenti, ki so na spletni strani Libgen.li, so zrcalne kopije drugih torrentov, navedenih tukaj. Edina izjema so torrenti za leposlovje, ki se začnejo pri %(fiction_starting_point)s. Torrenti za stripe in revije so izdani v sodelovanju med Anninim Arhivom in Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Iz zbirke <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> natančen izvor ni jasen. Delno iz the-eye.eu, delno iz drugih virov."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

