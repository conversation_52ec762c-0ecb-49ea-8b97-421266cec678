��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b N  �d "  �f *   h �   Dh �  /i �  k X  �l �   n I   o k   Qo D   �o a   p �  dp R  �q �   Bs �  /t �   �u �  �v �  �x �  �z �   J| �  } �  �~ 6   `� �   �� K   q� �  ��    ��   �� D    !   �    )�    6� 6   N� "   ��    �� ,   �� '   �    
�    *� E   ;� O  �� �   ш D   щ 9   �    P� 
   \�    j� 
   ��    �� 
   �� 	   ��        ӊ "   ڊ    ��    � 	   � 
   '�    5�    A�    [�    {� A  �� `  ؍ �   9� *   ŏ H  �� �   9�    ϑ �   В    �� �   ��    :� �   Y� #   � "  9�    \�   {� =   �� j   9�    �� 8  �� &  � �  �   ��    ҝ =   � X   !� �   z� )   '� '   Q� p   y� 7   � F   "� 8   i�    �� i   �� �  � 7   �� w   � �   \� K   �   R� 4   p� �   �� ~   �� �   � O   �� �   � �   �� D   8� �   }� �   [� e   � ?   u� u   �� 
   +� �   9� �   ¬ D   �� 
   � �   � �   ��    U� _  \� �   �� �   V� h  T� �   ��   �� �   �� =   �� L   Ը X   !� k   z� X   � V   ?� $   �� �   �� \   L� ;   ��    � o   �� ]   g� �   ż �   y�    � �   !� �   �� F  �� 3  >� }   r� �   �� (  �� �   �� �   �� �   '� O  �� �   � �   ��    �� �   �� �   � �   �� ^  �� t   �� v   \� �   �� �  w� k   � 0  �� �   ��    �� \   �� �   0� M   �� #  � �   '� ]   �� f   .�    ��   �� �   �� h  v�    �� V   �� �   O� �   �� �   �� �   �� �  u� �  '� $   �� b  �� [  M�   �� S  ��   �    &� W   '� �   �    � �   	� (  �� j  �� �   b�   @� �   D�    �� H  �� 4  6� �   k�    \� �   c� 	  � �   � �  �� �   �� O   |� 
   �� c  ��    >�    _� �   q� �   P� �  -  �   ( U  � O   T L   � �   � 8   � 
   " !  -   O   _ '   r	 D   �	 �  �	 k   � t   7 [   � q  
 $   z    � s  � �   3    � @   Z �   � $   0 �   U �   7    . 
   ? )   J 8   t 9   � /   � U    v   m    � *   � 5     I   V $   � 9   � $   � p   $    �    �   �   � �   � �   � z   � �    2  �    � �    �   � �   � A    ]  `! �  �" �  �$ R  B& D   �' =   �' #   ( �   <( �  �( �  �* &  Y, ?  �-   �. �  �/   �1 )   �2 �   �2   �3 J  5    P6 �   b6 7  .7    f8 %  �8 �   :    �: $   �: S   ;    U; `  \; ~  �= 6  <? �   sA �    B    �B �   �B �   �C D   �D x   �D �   iE   `F �   dG b  �G \   LI   �I    �J �  �J �   �L �  VM g  O �  �Q �   gS 
   T �   &T �  U    �V H  �V �  �W �  �Y   �[ �   �]   �^ D   �_ �  ` �   �a y   pb :   �b P   %c    vc >   }c    �c /   �c '   d    4d �   Nd �  �d �  �g 3  �i �   �k    �l �  �l 0  qo Z  �q d  �r T  bt 2  �v 
   �x �   �x    �y    �y �  �y �  }{   }} M  �    � �  �� �  ʃ �  ~� �  >� �  !�   �� a  ͍    /� �   M� n   � �  �� �  /� �   �� �   V� �   ߕ >  x�    �� �   З B   r� X   ��    � -   &� <   T� -  �� �  �� �  Q� 7   ן �  � �   ��    R� �   Y� 7   � ~   M� �   ̤ 0   �� U   ƥ    � <  5� )  r� �  �� �  ?� b   Ĭ    '� l   7�   ��    �� �   �� �  =� �   � 4   �� %   �� !  ݲ *   �� F  *� a  q� �  ӷ    �� b   �� �   � :  �� }  ټ    W� �   g� 1   M� �   � �   <� �   �� �   �� Q   �� *   � �   2� ?   � �  X� "  �� �   �� �   �� T   �� 6   � �   <�   �� P  �� �  I� �  � ,   �� �   � �   ��   �� �   �� �  �� t  c� *   �� �  � %   �� �  �� S   ��    ��   � �  � �  �� �   w� 
  O� �  ]� �  � �   � �  �� �  U� t   �� �  [� 7  !� 6   Y� p   �� |   �    ~�    �� %  �� �   �� .   h� �   �� �  �� 0  .� �  _� �  9� �   �� y  u� '  ��    � �   7�    �� q  �� "   e !   �    �    � #   �    �    �             &    8 
   E 
   P 	   [    e 
   � ,   �    � 	   �    �    � �   � R   � &   �     +   ' $   S 4   x 4   � )   �                2    H    Y    j    q    �    � 1   � $   �    �    
     ' +   H ,   t    � "   � :   �     S   5 ?   �    � V   � X   &        � "   �    �    �    �    	 
   *	    8	    @	    U	    b	 
   x	 	   �	 
   �	    �	 "   �	    �	    �	 	   �	    �	 	   �	    �	    
    
 	   %
    /
    ?
    K
    e
    m
    �
    �
    �
 	   �
    �
 "   �
    �
            !    ( 	   =    G    e    l    x W   � �   � K   � �   � �  w
 �   F 
   � &   �    �            ! 	   9 #   C M   g 3   � O   �    9 >   U $   � b   � ]    �   z f   K =   �    �        " 	   )    3 
   <    J    g    l    } 	   �    �    � 
   �    �    �    � 
           *    2    :    C    O    h �   y    m    r    z     �    � >   � [   � (   C %   l .   �    �    �    � x   �    M 
   S *   ^ q   �    � 
    e    $   � 3  � W   � f   5 �   � �   5 B   � 
  ; �   F    �    �!    �"    �" E   �" H   C# P   �# \   �# L   :$ F   �$ T   �$    #% +   B%    n%    w% K   �% &   �%    &    	& 	   &    #& k   8&    �& '   �& N   �&    *'    C' P   W' U   �' }  �'    |)    �) �   �)    h*    m*    t*    }* 
   �* -   �* =  �*    
,    (,    7,    H,   O,    h-    �-    �- �   �-    4.    9. )   @.    j.    �. $   �. �   �.    5/    J/ N   ]/ "   �/    �/    �/    �/ -   �/ i   &0    �0 :   �0 �   �0 W   o1 V   �1    2 �   .2 >   3    M3 &   ^3    �3 �   �3 �   A4    �4 F   5 �   H5 �   �5    �6    �6    �6 t  �6 3   [8 !   �8 "   �8 "   �8    �8 �   9    �9    �9 *   �9 H   :    [: "   `: !   �: "   �: -  �: \  �< �  S> 9   J@ 8   �@    �@ L   �@ '  A �   ?B �   C    �C �   �C �  �D "   &F     IF �  jF <  SH    �I    �I 4   �I    �I   �I    K -  "K �  PL �   �M    �N s    O &   tO %   �O `   �O   "P �   9Q �   3R x  �R b   ET 
  �T P   �U �   V �   �V D   [W �   �W +   FX %   rX    �X    �X    �X $   �X "   �X Q   Y 0   hY 	   �Y -   �Y 1  �Y "   [ �   &[ t   �[ y   k\ $   �\    
]    (]    D] %   []    �] &   �]     �] �   �] "   �^ �   �^    �_ �   �_ x   [` o   �`   Da �   Vb '   c ~   ,c 	   �c   �c ^   �d    e u  /e    �f    �f    �f    �f ,   �f    g    %g :   )g �   dg �   h    �h    �h    �h �   i 3  �i �   �j |   �k    *l    @l    Vl    ol    �l    �l    �l B   �l *   �l �  !m H   �n    'o d   :o g   �o C   p i   Kp H   �p H   �p    Gq `   Pq =   �q �   �q ?   vr C   �r    �r �   
s ^   �s ?   Ct g   �t R   �t :   >u    yu 9   �u x   �u �   9v 8   �v �   �v    �w   �w P   �x U   
y �   cy    �y �  �y �   �{    �|    �|    �|    �| �   �| (   �} d   �} �   R~ �     |   � �   l� �   ;� �   ́ �   i� V    �   w� ]   �� �  � �   t� �    � 
   �� 
   � 
   � �   $� 
   �� 
   ͈ L   ۈ \   (� �   �� 
   e� `   s� ?   Ԋ n   � @   �� k   ċ `   0� 
   �� �   �� 
   *� 
   8� 
   F�   T� �   r� �  �� 
   ��    �� 	   �� �   ��    H� $   `� �   �� �   l�    �    !�    1� 1   L� 3   ~� 4   ��    �    � �   �    � v  � �   {� �   T� L   ט �   $� �   �� �  �� 3  =� �   q� y   "� v   ��    � L  '�    t� E  �� �   آ   �� �   �� $  7� �   \� �   � o   �� +   
� 5   9�    o� #   �� 
   ��    ��    Ǩ �   ڨ    �� f   ͩ &   4�    [�    c�    p�    x� p   �� R   � 8   Y� �   �� 1   �� 	   ��    ��    Ǭ #   �    � 
   �    %� 
   -�    8� 
   A� 	   L� 
   V� /   a� �   ��    ,� 
   =� 
   K� 
   V�    d� 
   p�    ~� 
   ��    ��    �� +   ® c   �    R� &   c� \   �� �   � �   |� �   '� 	  ر �   �   ��    �� �   �� 3   9� C   m� B   �� �   �� �   �� Q   `� _   ��    � f   $� x   �� �   �    ��     ��    ��    ʹ    � &   ��    � #   $�    H�    Q�    j� "   ��    ��    ��    ٺ    ��    ��    
�    �    %�    =�     D� #   e� ~   �� �   � (   �� !   �� h   ּ [   ?� t   �� .   � '   ?� 3   g� 8   �� <   Ծ /   � �   A� �    �   ��     � C   ,� �   p� )   �� �   '� �   ��    �� I   6�    �� �   �� �   0� o   �� 2   7� ^   j� �   ��    ~� 3   ��    �� 6   �� g   #� c   ��    ��    ��     �     � �   (� 5   ��    �� 1   � !   F�    h�     �� >   ��    �� g    � ;   h� �   �� A   �� P   �� �   *� B   ��    ��    �    9�    Y� !   w�    �� !   �� /   �� /   
�   :� M   Z� 7   �� >   �� (   �    H� l   P� g   �� �   %� �   ��    �� �   �� 0   �    K� �   e�    -� %   D� @   j� 6   �� Z   �� U   =� R   ��     �� '   � �   /� 5   ��     �� L   � �   f� F   K� �   �� n   � a   �� d   �� $   I� B   n� z   ��    ,� b   C�    �� �   �� 5   m� 6   ��    �� #   �� �   � E   �� P    � �   q� T   �� M   M� v   �� �   � 	   ��    �� Q   �� E   �    Z� &   o�    ��    ��    �� -   �� �   �� h   �� !   ��    � $   5� &   Z� &   �� v   �� ;   � ~   [� `   �� !   ;� n   ]� �   �� 1   �� T   ��    %� G   5� ;   }�    �� g   �� h   1� .   �� E   ��    � 1   � T   N�    �� h   �� (   �    H� &   ^� Q   �� �   �� <   ��    ��    �� <   �    C� O   Z� 6   �� �   �� w   |�    �� E   � �   J�    � �   "� ?   ��     �� L   � �   c�    E�    M�    O�    Q� %   c� ;   �� ;   ��    �    �    *� #   3� 8   W� 5   ��    �� >   �� A   
� 
   L� &   Z� (   ��    ��    �� Y   �� �   +� L   �    Q�    ^� �   k� =  � ]   V�    �� :  �� �  �� (   �� b   �� "   � "  7� "   Z  c   }     �     �  �  �     � S   � w   " \   � Y   �    Q `   o 7   �     q    I   � -   � W    ,   ` V   � �   � �   j     � �    N  � �    
 )   �
 *  �
 �   � �   �   �
 �   � (   X $   � �   � .   R �  � [   h ?   � 
        x  -    � �   � /  m   � -  � B   � F   $ T   k $   � *   � Q    a   b    �    � :   � $   9    ^ 7   r ]   � %       . Y   5 �   � �   V    �        ( H   1 [   z L   � �   # Y   �    4  &   J  �   q  
   6! �   A! �  �! �   e$ F   O% (   �%    �%    �%    �% I   �% 2   & v   L& �   �& P   �'    �'    ( &   (    A( M   \(    �( D   �(    �( 3   ) '   7)    _)    q) b   w)    �)    �) *   �) #   $*    H* [   L* �   �* P   ^+ j   �+ O   , �   j,    (-    4- �   R- �   	. �   �.    g/ i   p/ F   �/ B   !0 t   d0 b   �0 Q   <1    �1 X   �1    2    2    ,2    @2    Y2    s2    �2    �2    �2    �2 -   �2 0   �2 -   ,3    Z3 4   z3 +   �3 &   �3    4    4 <   )4    f4    �4 2   �4 +   �4 X   �4 ,   B5    o5    �5    �5    �5    �5 _   �5 I   @6 l   �6 �   �6    �7    �7    �7    8 .    8 	   O8    Y8    n8 p   �8    �8 #   9    :9 
   A9 	   L9 4   V9    �9 �   �9    �:    �:    �: %   �: !   ;    *; !   B; %   d; '   �; 6   �; -   �;    < ]   < (   |<    �<    �< +   �< M   �<    F= 7   e=    �= p   �= V   -> H   �>    �>    �> 	   �>    �>    ?    1? m  J? p   �@    )A    AA '   EA    mA (   A    �A    �A \   �A A   B �   [B    "C $   :C y   _C $   �C !   �C #    D &   DD 4   kD 2   �D    �D 
   �D >   �D    ;E     VE 5   wE '  �E J   �F >    G Y   _G    �G |   �G &   QH    xH [   �H    �H )   �H    I 9   6I J   pI &   �I �   �I    zJ    �J    �J    �J    �J    �J $   �J    K    3K X   JK �   �K !   �L '   �L �   �L �   �M    ^N    xN    �N    �N    �N    �N    �N    �N    O    &O    9O    HO 
   YO    gO    vO    �O    �O    �O "   �O �   �O �   �P [  �Q �  �R �  �T �   |V �  ^W    �X 
  �X    �Y �   Z �    [ �  �[ �   w] Q  '^ <   y_ �   �_ 6   _`    �` C   �` I   �` l   <a [   �a �   b �   �b �   1c %  �c    f �   -f �   �f O   �g �   �g    th �   �h V  Ui �   �j �   ]k    l S   $l ]   xl �   �l �   �m ]   *n }   �n    o     o    ;o A   To -   �o 
   �o �   �o :   Xp d   �p (   �p �   !q �   �q 4   .r P   cr Q   �r _   s Z   fs K   �s g   
t f   ut w   �t �   Tu    �u 3   �u '   v Z   Fv 4   �v    �v    �v Q   �v    6w    Fw    \w 2   cw 1   �w ?   �w    x    x    +x    3x 	   9x O   Cx h   �x F   �x :   Cy    ~y    �y (   �y    �y    �y    �y    �y    �y    �y    z 	   z    z    z 	   $z    .z    Az    Mz 
   az 
   oz    }z 
   �z 
   �z    �z    �z    �z    �z r   �z    o{ X   �{ �   �{    �|    �|    �|    �| 	   �|    �| 	   �| �   �| �   �} P   ~    X~    e~ j   z~    �~ y   �~ �   l "   �    � �   1� �   �� C   �� �   ǁ �   u� ,   �� �   &�    ��    ă R   ܃ �   /� )   ӄ �   �� �   �� �   � E   ��    І    �    �     �    �    �    '�    4� �   O� h   ߇ z   H� �   È �   �� \   �� @   � �  ,� O  �� �   � �   ��   |� @  ��    ڑ �   � �   �� �   �� O  6� �  �� t   A� X  ��    � 9   '� �   a� F   � ;   G� �   ��    _�    f�    m� �   �� B   � k   S� 2   �� [   � N   N� R   �� (   � �   � <   à /    � F   0� �   w�    Q�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: sl
Language-Team: sl <<EMAIL>>
Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library je priljubljena (in nezakonita) knjižnica. Vzeli so zbirko Library Genesis in jo naredili enostavno iskalno. Poleg tega so postali zelo učinkoviti pri pridobivanju novih prispevkov knjig, saj spodbujajo uporabnike k prispevanju z različnimi ugodnostmi. Trenutno teh novih knjig ne prispevajo nazaj v Library Genesis. In za razliko od Library Genesis, ne omogočajo enostavnega zrcaljenja svoje zbirke, kar preprečuje široko ohranjanje. To je pomembno za njihov poslovni model, saj zaračunavajo denar za dostop do svoje zbirke v večjih količinah (več kot 10 knjig na dan). Ne izrekamo moralnih sodb o zaračunavanju denarja za množični dostop do nezakonite zbirke knjig. Nedvomno je, da je Z-Library uspešno razširila dostop do znanja in pridobila več knjig. Tukaj smo preprosto zato, da opravimo svoj del: zagotovimo dolgoročno ohranitev te zasebne zbirke. - Anna in ekipa (<a %(reddit)s>Reddit</a>) V prvotni izdaji Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>) smo ustvarili zrcalo Z-Library, velike nezakonite zbirke knjig. Kot opomnik, to smo zapisali v tisti prvotni objavi na blogu: Ta zbirka sega v sredino leta 2021. Medtem je Z-Library rasla z osupljivo hitrostjo: dodali so približno 3,8 milijona novih knjig. Seveda so tam tudi nekatere podvojene, vendar se zdi, da je večina resnično novih knjig ali pa gre za bolj kakovostne skene že predloženih knjig. To je v veliki meri posledica povečanega števila prostovoljnih moderatorjev v Z-Library in njihovega sistema množičnega nalaganja z deduplikacijo. Radi bi jim čestitali za te dosežke. Z veseljem sporočamo, da smo pridobili vse knjige, ki so bile dodane v Z-Library med našim zadnjim zrcalom in avgustom 2022. Prav tako smo se vrnili in zbrali nekatere knjige, ki smo jih prvič zgrešili. Vse skupaj je ta nova zbirka velika približno 24TB, kar je precej več kot prejšnja (7TB). Naše zrcalo je zdaj skupno 31TB. Ponovno smo izvedli deduplikacijo proti Library Genesis, saj so za to zbirko že na voljo torrenti. Prosimo, obiščite Pirate Library Mirror, da si ogledate novo zbirko (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>). Tam je več informacij o tem, kako so datoteke strukturirane in kaj se je spremenilo od zadnjič. Ne bomo povezovali od tukaj, saj je to le blog spletna stran, ki ne gosti nobenih nezakonitih materialov. Seveda je tudi deljenje odličen način, da nam pomagate. Hvala vsem, ki delite naš prejšnji nabor torrentov. Hvaležni smo za pozitiven odziv in veseli, da je toliko ljudi, ki jim je mar za ohranitev znanja in kulture na ta nenavaden način. 3x nove knjige dodane v Pirate Library Mirror (+24TB, 3,8 milijona knjig) Preberite spremljevalne članke TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a> - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) spremljevalni članki TorrentFreak: <a %(torrentfreak)s>prvi</a>, <a %(torrentfreak_2)s>drugi</a> Pred kratkim so "sence knjižnic" izumirale. Sci-Hub, masivni nezakoniti arhiv akademskih člankov, je prenehal sprejemati nova dela zaradi tožb. "Z-Library", največja nezakonita knjižnica knjig, je videla svoje domnevne ustvarjalce aretirane zaradi kaznivih dejanj povezanih z avtorskimi pravicami. Neverjetno so uspeli pobegniti aretaciji, vendar njihova knjižnica ni nič manj ogrožena. Nekatere države že izvajajo različico tega. TorrentFreak je <a %(torrentfreak)s>poročal</a>, da sta Kitajska in Japonska uvedli izjeme za umetno inteligenco v svoje zakone o avtorskih pravicah. Ni nam jasno, kako to vpliva na mednarodne pogodbe, vendar zagotovo daje kritje njihovim domačim podjetjem, kar pojasnjuje, kar smo videli. Kar se tiče Anninega Arhiva — nadaljevali bomo naše podzemno delo, ki temelji na moralnem prepričanju. Vendar je naša največja želja, da stopimo na svetlobo in legalno okrepimo naš vpliv. Prosimo, reformirajte avtorske pravice. Ko se je Z-Library soočila z zaprtjem, sem že varnostno kopiral celotno knjižnico in iskal platformo, kjer bi jo lahko gostil. To je bila moja motivacija za začetek Anninega Arhiva: nadaljevanje misije teh prejšnjih pobud. Od takrat smo zrasli v največjo senco knjižnico na svetu, ki gosti več kot 140 milijonov avtorsko zaščitenih besedil v številnih formatih — knjige, akademske članke, revije, časopise in še več. Moja ekipa in jaz smo ideologi. Verjamemo, da je ohranjanje in gostovanje teh datotek moralno pravilno. Knjižnice po svetu se soočajo z zmanjšanjem financiranja, in ne moremo zaupati dediščine človeštva korporacijam. Potem je prišla umetna inteligenca. Skoraj vsa večja podjetja, ki gradijo LLM-je, so nas kontaktirala za usposabljanje na naših podatkih. Večina (vendar ne vsa!) podjetij s sedežem v ZDA je premislila, ko so spoznala nezakonito naravo našega dela. Nasprotno pa so kitajska podjetja z navdušenjem sprejela našo zbirko, očitno neobremenjena z njeno zakonitostjo. To je opazno, glede na to, da je Kitajska podpisnica skoraj vseh večjih mednarodnih pogodb o avtorskih pravicah. Omogočili smo hiter dostop približno 30 podjetjem. Večina jih je LLM podjetij, nekateri pa so posredniki podatkov, ki bodo našo zbirko preprodajali. Večina jih je kitajskih, vendar smo sodelovali tudi s podjetji iz ZDA, Evrope, Rusije, Južne Koreje in Japonske. DeepSeek je <a %(arxiv)s>priznal</a>, da je bila prejšnja različica usposobljena na delu naše zbirke, čeprav so glede najnovejšega modela zadržani (verjetno je tudi ta usposobljen na naših podatkih). Če želi Zahod ostati v ospredju v tekmi LLM-jev in končno AGI, mora ponovno premisliti svoj položaj glede avtorskih pravic, in to kmalu. Ne glede na to, ali se strinjate z nami glede našega moralnega primera ali ne, to postaja primer ekonomije in celo nacionalne varnosti. Vse sile gradijo umetne super-znanstvenike, super-hekerje in super-vojske. Svoboda informacij postaja vprašanje preživetja za te države — celo vprašanje nacionalne varnosti. Naša ekipa je z vsega sveta in nimamo posebne usmeritve. Vendar bi spodbudili države z močnimi zakoni o avtorskih pravicah, da uporabijo to eksistencialno grožnjo za njihovo reformo. Kaj torej storiti? Naše prvo priporočilo je preprosto: skrajšajte obdobje avtorskih pravic. V ZDA so avtorske pravice podeljene za 70 let po avtorjevi smrti. To je absurdno. To lahko uskladimo s patenti, ki so podeljeni za 20 let po vložitvi. To bi moralo biti več kot dovolj časa, da avtorji knjig, člankov, glasbe, umetnosti in drugih ustvarjalnih del dobijo popolno nadomestilo za svoje napore (vključno z dolgoročnejšimi projekti, kot so filmske priredbe). Nato bi morali oblikovalci politik vsaj vključiti izjeme za množično ohranjanje in razširjanje besedil. Če je izgubljeni prihodek od posameznih strank glavna skrb, bi lahko osebna distribucija ostala prepovedana. V zameno bi tisti, ki so sposobni upravljati obsežne zbirke — podjetja, ki usposabljajo LLM-je, skupaj s knjižnicami in drugimi arhivi — bili zajeti v teh izjemah. Reforma avtorskih prav je nujna za nacionalno varnost. Na kratko: Kitajski LLM-ji (vključno z DeepSeek) so usposobljeni na mojem nezakonitem arhivu knjig in člankov — največjem na svetu. Zahod mora prenoviti zakonodajo o avtorskih pravicah zaradi nacionalne varnosti. Za več informacij si oglejte <a %(all_isbns)s>izvirno objavo na blogu</a>. Izdali smo izziv za izboljšanje tega. Za prvo mesto bi podelili nagrado v višini 6.000 $, za drugo mesto 3.000 $ in za tretje mesto 1.000 $. Zaradi izjemnega odziva in neverjetnih prispevkov smo se odločili, da nekoliko povečamo nagradni sklad in podelimo štiri tretja mesta po 500 $ vsakemu. Zmagovalci so spodaj, vendar si oglejte vse prispevke <a %(annas_archive)s>tukaj</a> ali prenesite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>. Prvo mesto 6.000 $: phiresky Ta <a %(phiresky_github)s>prispevek</a> (<a %(annas_archive_note_2951)s>komentar na Gitlabu</a>) je preprosto vse, kar smo želeli, in še več! Še posebej so nam bile všeč izjemno prilagodljive možnosti vizualizacije (celo podpora za prilagojene shaderje), vendar s celovitim seznamom prednastavitev. Prav tako nam je bilo všeč, kako hitro in gladko vse deluje, preprosta izvedba (ki sploh nima zaledja), pametna minimapa in obsežna razlaga v njihovem <a %(phiresky_github)s>blogu</a>. Neverjetno delo in zaslužena zmaga! - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Naša srca so polna hvaležnosti. Opazne ideje Nebotičniki za redkost Veliko drsnikov za primerjavo datasets, kot da ste DJ. Merilna vrstica s številom knjig. Lepi napisi. Kul privzeta barvna shema in toplotna karta. Edinstven pogled na zemljevid in filtri Opombe in tudi žive statistike Žive statistike Nekatere dodatne ideje in izvedbe, ki so nam bile še posebej všeč: Lahko bi nadaljevali še nekaj časa, vendar se tukaj ustavimo. Bodite prepričani, da si ogledate vse prispevke <a %(annas_archive)s>tukaj</a>, ali prenesite naš <a %(a_2025_01_isbn_visualization_files)s>kombinirani torrent</a>. Toliko prispevkov, in vsak prinaša edinstveno perspektivo, bodisi v uporabniškem vmesniku ali izvedbi. Vsaj prvo mesto bomo vključili v našo glavno spletno stran, morda pa tudi nekatere druge. Prav tako smo začeli razmišljati o tem, kako organizirati proces prepoznavanja, potrjevanja in nato arhiviranja najredkejših knjig. Več sledi na tem področju. Hvala vsem, ki ste sodelovali. Neverjetno je, da toliko ljudi skrbi. Enostavno preklapljanje med datasets za hitre primerjave. Vsi ISBN-ji CADAL SSNO-ji CERLALC uhajanje podatkov DuXiu SSID-ji EBSCOhostov eBook Index Google Knjige Goodreads Internetni arhiv ISBNdb ISBN Globalni register založnikov Libby Datoteke v Anninem arhivu Nexus/STC OCLC/Worldcat OpenLibrary Ruska državna knjižnica Imperialna knjižnica Trantorja Drugo mesto 3.000 $: hypha "Medtem ko so popolni kvadrati in pravokotniki matematično prijetni, ne zagotavljajo boljše lokalnosti v kontekstu kartiranja. Verjamem, da asimetrija, ki je lastna tem Hilbertovim ali klasičnim Mortonovim krivuljam, ni pomanjkljivost, temveč lastnost. Tako kot je Italija s svojo znamenito obliko škornja takoj prepoznavna na zemljevidu, lahko edinstvene 'posebnosti' teh krivulj služijo kot kognitivne znamenitosti. Ta posebnost lahko izboljša prostorski spomin in pomaga uporabnikom pri orientaciji, kar lahko olajša iskanje določenih regij ali opazovanje vzorcev." Še en neverjeten <a %(annas_archive_note_2913)s>prispevek</a>. Ni tako prilagodljiv kot prvo mesto, vendar smo dejansko raje imeli njegovo makro raven vizualizacije pred prvim mestom (krivulja zapolnjevanja prostora, meje, označevanje, poudarjanje, premikanje in povečava). <a %(annas_archive_note_2971)s>Komentar</a> Joeja Davisa je z nami odmeval: In še vedno veliko možnosti za vizualizacijo in upodabljanje ter neverjetno gladek in intuitiven uporabniški vmesnik. Trdno drugo mesto! - Anna in ekipa (<a %(reddit)s>Reddit</a>) Pred nekaj meseci smo objavili <a %(all_isbns)s>nagrado v vrednosti 10.000 $</a> za najboljšo možno vizualizacijo naših podatkov, ki prikazuje prostor ISBN. Poudarili smo prikaz, katere datoteke smo že arhivirali in katere ne, ter kasneje dodali podatkovno zbirko, ki opisuje, koliko knjižnic ima ISBN-je (merilo redkosti). Preplavljeni smo bili z odzivom. Bilo je toliko ustvarjalnosti. Velika zahvala vsem, ki ste sodelovali: vaša energija in navdušenje sta nalezljiva! Na koncu smo želeli odgovoriti na naslednja vprašanja: <strong>kateri knjige obstajajo na svetu, koliko smo jih že arhivirali in na katere knjige bi se morali osredotočiti naslednje?</strong> Čudovito je videti, da toliko ljudi skrbi za ta vprašanja. Sami smo začeli z osnovno vizualizacijo. V manj kot 300 kb ta slika jedrnato predstavlja največji popolnoma odprt "seznam knjig", ki je bil kdajkoli sestavljen v zgodovini človeštva: Tretje mesto 500 $ #1: maxlion V tem <a %(annas_archive_note_2940)s>prispevku</a> so nam bile res všeč različne vrste pogledov, zlasti primerjalni in založniški pogledi. Tretje mesto 500 $ #2: abetusk Čeprav uporabniški vmesnik ni najbolj izpopolnjen, ta <a %(annas_archive_note_2917)s>prispevek</a> izpolnjuje veliko zahtev. Še posebej nam je bila všeč njegova primerjalna funkcija. Tretje mesto 500 $ #3: conundrumer0 Tako kot prvo mesto nas je ta <a %(annas_archive_note_2975)s>prispevek</a> navdušil s svojo prilagodljivostjo. Na koncu je to tisto, kar naredi odlično orodje za vizualizacijo: maksimalna prilagodljivost za napredne uporabnike, hkrati pa ohranja stvari preproste za povprečne uporabnike. Tretje mesto 500 $ #4: charelf Zadnji <a %(annas_archive_note_2947)s>prispevek</a>, ki prejme nagrado, je precej osnovni, vendar ima nekaj edinstvenih funkcij, ki so nam bile res všeč. Všeč nam je bilo, kako prikazujejo, koliko Datasets pokriva določen ISBN kot merilo priljubljenosti/zanesljivosti. Prav tako nam je bila zelo všeč preprostost, a učinkovitost uporabe drsnika za prosojnost pri primerjavah. Zmagovalci nagrade za vizualizacijo ISBN v vrednosti 10.000 $ Na kratko: Prejeli smo nekaj neverjetnih prispevkov za nagrado za vizualizacijo ISBN v vrednosti 10.000 $. Ozadje Kako lahko Arhiv Ane doseže svoj cilj varnostnega kopiranja vsega človeškega znanja, ne da bi vedel, katere knjige so še tam zunaj? Potrebujemo seznam NALOG. Eden od načinov za to je s pomočjo ISBN številk, ki so bile od 70-ih let prejšnjega stoletja dodeljene vsaki objavljeni knjigi (v večini držav). Ne obstaja osrednja avtoriteta, ki bi poznala vse dodelitve ISBN. Namesto tega gre za porazdeljen sistem, kjer države dobijo obsege številk, ki jih nato dodelijo večjim založnikom, ti pa lahko nadalje razdelijo obsege manjšim založnikom. Na koncu so posamezne številke dodeljene knjigam. Začeli smo z mapiranjem ISBN <a %(blog)s>pred dvema letoma</a> z našim strganjem ISBNdb. Od takrat smo strgali še veliko več virov metapodatkov, kot so <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby in drugi. Celoten seznam je na voljo na straneh »Datasets« in »Torrents« na Arhivu Ane. Zdaj imamo daleč največjo popolnoma odprto, enostavno prenosljivo zbirko metapodatkov knjig (in s tem ISBN) na svetu. <a %(blog)s>Obširno smo pisali</a> o tem, zakaj nam je mar za ohranjanje, in zakaj smo trenutno v kritičnem obdobju. Zdaj moramo identificirati redke, premalo osredotočene in edinstveno ogrožene knjige ter jih ohraniti. Dobri metapodatki o vseh knjigah na svetu pri tem pomagajo. Nagrada 10.000 $ Močno se bo upoštevala uporabnost in vizualna privlačnost. Prikažite dejanske metapodatke za posamezne ISBN pri povečavi, kot so naslov in avtor. Boljša krivulja zapolnjevanja prostora. Npr. cik-cak, ki gre od 0 do 4 v prvi vrstici in nato nazaj (v obratni smeri) od 5 do 9 v drugi vrstici — rekurzivno uporabljeno. Različne ali prilagodljive barvne sheme. Posebni pogledi za primerjavo datasets. Načini za odpravljanje težav, kot so drugačna metadata, ki se ne ujemajo dobro (npr. zelo različni naslovi). Označevanje slik s komentarji o ISBN-jih ali razponih. Kakršne koli heuristike za prepoznavanje redkih ali ogroženih knjig. Kakršne koli ustvarjalne ideje, ki jih lahko zamislite! Koda Koda za generiranje teh slik, kot tudi drugi primeri, se nahajajo v <a %(annas_archive)s>tem imeniku</a>. Izumili smo kompakten format podatkov, s katerim je vsa potrebna ISBN informacija približno 75MB (stisnjeno). Opis formata podatkov in koda za njegovo generiranje sta na voljo <a %(annas_archive_l1244_1319)s>tukaj</a>. Za nagrado ni potrebno, da to uporabite, vendar je verjetno najbolj priročen format za začetek. Našo metadata lahko preoblikujete, kakor želite (vsa vaša koda pa mora biti odprtokodna). Komaj čakamo, da vidimo, kaj boste ustvarili. Srečno! Razvejite ta repozitorij in uredite to HTML objavo na blogu (drugega zaledja poleg našega Flask zaledja ni dovoljeno). Naredite zgornjo sliko gladko povečljivo, tako da lahko povečate vse do posameznih ISBN. Klik na ISBN naj vas popelje na stran z metapodatki ali iskanje na Arhivu Ane. Še vedno morate biti sposobni preklapljati med vsemi različnimi datasets. Obsegi držav in založnikov naj bodo poudarjeni ob premiku miške. Uporabite lahko npr. <a %(github_xlcnd_isbnlib)s>data4info.py v isbnlib</a> za informacije o državah in našo »isbngrp« strganje za založnike (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Delovati mora dobro na namizju in mobilnih napravah. Tukaj je veliko za raziskati, zato razpisujemo nagrado za izboljšanje zgornje vizualizacije. Za razliko od večine naših nagrad je ta časovno omejena. Svojo odprtokodno kodo morate <a %(annas_archive)s>oddati</a> do 2025-01-31 (23:59 UTC). Najboljša oddaja bo prejela 6.000 $, drugo mesto 3.000 $, tretje mesto pa 1.000 $. Vse nagrade bodo podeljene v Monero (XMR). Spodaj so minimalna merila. Če nobena oddaja ne izpolnjuje meril, lahko še vedno podelimo nekaj nagrad, vendar bo to po naši presoji. Za dodatne točke (to so le ideje — pustite svoji ustvarjalnosti prosto pot): Lahko se popolnoma oddaljite od minimalnih kriterijev in naredite popolnoma drugačno vizualizacijo. Če je res spektakularna, potem se kvalificira za nagrado, vendar po naši presoji. Oddajte prispevke tako, da objavite komentar na <a %(annas_archive)s>to težavo</a> s povezavo do vašega forked repo, merge request ali diff. - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ta slika je 1000×800 pikslov. Vsak piksel predstavlja 2.500 ISBN-jev. Če imamo datoteko za ISBN, naredimo ta piksel bolj zelen. Če vemo, da je bil ISBN izdan, vendar nimamo ustrezne datoteke, ga naredimo bolj rdečega. V manj kot 300 kb ta slika jedrnato predstavlja največji popolnoma odprt "seznam knjig", ki je bil kdajkoli sestavljen v zgodovini človeštva (nekaj sto GB stisnjenih v celoti). Prav tako prikazuje: še veliko dela je treba opraviti pri varnostnem kopiranju knjig (imamo le 16%). Vizualizacija vseh ISBN-jev — nagrada 10.000 $ do 31. 1. 2025 Ta slika predstavlja največji popolnoma odprt "seznam knjig", ki je bil kdajkoli sestavljen v zgodovini človeštva. Vizualizacija Poleg pregledne slike si lahko ogledamo tudi posamezne pridobljene datasets. Uporabite spustni meni in gumbe za preklapljanje med njimi. V teh slikah je veliko zanimivih vzorcev. Zakaj je neka pravilnost linij in blokov, ki se zdi, da se pojavlja na različnih lestvicah? Kaj so prazna območja? Zakaj so določeni datasets tako zgoščeni? Ta vprašanja bomo pustili kot nalogo za bralca. - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Zaključek S tem standardom lahko izdaje izvajamo bolj postopoma in lažje dodajamo nove vire podatkov. Že imamo nekaj vznemirljivih izdaj v pripravi! Upamo tudi, da bo drugim senčnim knjižnicam lažje zrcaliti naše zbirke. Konec koncev je naš cilj ohraniti človeško znanje in kulturo za vedno, zato je večja redundanca boljša. Primer Poglejmo si našo nedavno izdajo Z-Library kot primer. Sestavljena je iz dveh zbirk: “<span style="background: #fffaa3">zlib3_records</span>” in “<span style="background: #ffd6fe">zlib3_files</span>”. To nam omogoča, da ločeno zajamemo in izdamo zapise metadata od dejanskih datotek knjig. Tako smo izdali dva torrenta z datotekami metadata: Prav tako smo izdali kup torrentov z mapami binarnih podatkov, vendar le za zbirko “<span style="background: #ffd6fe">zlib3_files</span>”, skupno 62: Z zagonom <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> lahko vidimo, kaj je znotraj: V tem primeru gre za metadata knjige, kot poroča Z-Library. Na najvišji ravni imamo le “aacid” in “metadata”, vendar ni “data_folder”, saj ni ustreznih binarnih podatkov. AACID vsebuje “22430000” kot primarni ID, ki ga lahko vidimo, da je vzet iz “zlibrary_id”. Pričakujemo lahko, da bodo imeli drugi AAC-ji v tej zbirki enako strukturo. Zdaj zaženimo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: To je veliko manjša metadata AAC, čeprav se večina tega AAC nahaja drugje v binarni datoteki! Konec koncev imamo tokrat “data_folder”, zato lahko pričakujemo, da se ustrezni binarni podatki nahajajo na <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” vsebuje “zlibrary_id”, zato ga lahko enostavno povežemo z ustreznim AAC v zbirki “zlib_records”. Povezali bi lahko na več različnih načinov, npr. preko AACID — standard tega ne predpisuje. Upoštevajte, da tudi ni nujno, da je polje “metadata” samo JSON. Lahko je niz, ki vsebuje XML ali katerikoli drug podatkovni format. Metadata informacije bi lahko celo shranili v povezani binarni blob, npr. če gre za veliko podatkov. Heterogene datoteke in metadata, čim bližje izvirni obliki. Binarne podatke lahko neposredno strežejo spletni strežniki, kot je Nginx. Heterogeni identifikatorji v izvornih knjižnicah ali celo pomanjkanje identifikatorjev. Ločene izdaje metadata v primerjavi s podatki datotek ali izdaje samo metadata (npr. naša izdaja ISBNdb). Distribucija preko torrentov, vendar z možnostjo drugih metod distribucije (npr. IPFS). Nespremenljivi zapisi, saj moramo predvidevati, da bodo naši torrenti živeli večno. Postopne izdaje / dodajljive izdaje. Strojno berljivo in zapisljivo, priročno in hitro, še posebej za našo tehnologijo (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Nekoliko enostaven človeški pregled, čeprav je to sekundarno glede na strojno berljivost. Enostavno sejanje naših zbirk s standardno najeto seedbox. Cilji oblikovanja Ne zanima nas, da bi bile datoteke enostavne za ročno navigacijo na disku ali iskanje brez predhodne obdelave. Ne zanima nas, da bi bili neposredno združljivi z obstoječo knjižnično programsko opremo. Čeprav bi moralo biti enostavno za vsakogar, da seje našo zbirko z uporabo torrentov, ne pričakujemo, da bodo datoteke uporabne brez znatnega tehničnega znanja in zavezanosti. Naš primarni primer uporabe je distribucija datotek in pripadajočih metadata iz različnih obstoječih zbirk. Naše najpomembnejše premisleke so: Nekateri ne-cilji: Ker je Annin Arhiv odprtokoden, želimo neposredno uporabljati naš format. Ko osvežimo naš iskalni indeks, dostopamo le do javno dostopnih poti, tako da lahko vsak, ki razveja našo knjižnico, hitro začne delovati. <strong>AAC.</strong> AAC (Kontejner Anninega Arhiva) je en sam element, ki vsebuje <strong>metadata</strong> in po želji <strong>binarne podatke</strong>, ki so nespremenljivi. Ima globalno edinstven identifikator, imenovan <strong>AACID</strong>. <strong>AACID.</strong> Format AACID je naslednji: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Na primer, dejanski AACID, ki smo ga izdali, je <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Obseg AACID.</strong> Ker AACID-ji vsebujejo monotono naraščajoče časovne žige, jih lahko uporabimo za označevanje obsegov znotraj določene zbirke. Uporabljamo ta format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kjer so časovni žigi vključeni. To je skladno z ISO 8601 notacijo. Obsegi so neprekinjeni in se lahko prekrivajo, vendar morajo v primeru prekrivanja vsebovati enake zapise kot tisti, ki so bili prej izdani v tej zbirki (saj so AAC-ji nespremenljivi). Manjkajoči zapisi niso dovoljeni. <code>{collection}</code>: ime zbirke, ki lahko vsebuje ASCII črke, številke in podčrtaje (vendar ne dvojnih podčrtajev). <code>{collection-specific ID}</code>: zbirki specifičen identifikator, če je to primerno, npr. Z-Library ID. Lahko se izpusti ali skrajša. Mora biti izpuščen ali skrajšan, če bi AACID sicer presegel 150 znakov. <code>{ISO 8601 timestamp}</code>: kratka različica ISO 8601, vedno v UTC, npr. <code>20220723T194746Z</code>. Ta številka se mora monotono povečevati za vsako izdajo, čeprav se lahko njeni natančni pomeni razlikujejo glede na zbirko. Predlagamo uporabo časa strganja ali generiranja ID-ja. <code>{shortuuid}</code>: UUID, vendar stisnjen v ASCII, npr. z uporabo base57. Trenutno uporabljamo Python knjižnico <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Mapa z binarnimi podatki.</strong> Mapa z binarnimi podatki obsega AAC-jev za eno določeno zbirko. Imajo naslednje lastnosti: Mapa mora vsebovati datoteke z podatki za vse AAC-je znotraj določenega obsega. Vsaka datoteka z podatki mora imeti AACID kot ime datoteke (brez pripon). Ime mape mora biti obseg AACID, pred katerim je <code style="color: green">annas_archive_data__</code>, brez pripone. Na primer, ena od naših dejanskih izdaj ima mapo z imenom<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Priporočljivo je, da so te mape nekoliko obvladljive po velikosti, npr. ne večje od 100 GB-1 TB vsaka, čeprav se lahko to priporočilo sčasoma spremeni. <strong>Zbirka.</strong> Vsak AAC pripada zbirki, ki je po definiciji seznam AAC-jev, ki so semantično skladni. To pomeni, da če naredite pomembno spremembo v formatu metadata, morate ustvariti novo zbirko. Standard <strong>Datoteka z metadata.</strong> Datoteka z metadata vsebuje metadata obsega AAC-jev za eno določeno zbirko. Imajo naslednje lastnosti: <code>data_folder</code> je neobvezno in je ime mape z binarnimi podatki, ki vsebuje ustrezne binarne podatke. Ime datoteke ustreznih binarnih podatkov v tej mapi je AACID zapisa. Vsak JSON objekt mora vsebovati naslednja polja na najvišji ravni: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (neobvezno). Druga polja niso dovoljena. Ime datoteke mora biti obseg AACID, pred katerim je <code style="color: red">annas_archive_meta__</code> in sledi <code>.jsonl.zstd</code>. Na primer, ena od naših izdaj se imenuje<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kot nakazuje pripona datoteke, je tip datoteke <a %(jsonlines)s>JSON Lines</a> stisnjen z <a %(zstd)s>Zstandard</a>. <code>metadata</code> so poljubni metadata, glede na semantiko zbirke. Morajo biti semantično skladni znotraj zbirke. Predpona <code style="color: red">annas_archive_meta__</code> se lahko prilagodi imenu vaše institucije, npr. <code style="color: red">my_institute_meta__</code>. <strong>Zbirke “zapisov” in “datotek”.</strong> Po konvenciji je pogosto priročno izdati “zapise” in “datoteke” kot različne zbirke, da jih lahko izdajamo po različnih urnikih, npr. glede na stopnje strganja. “Zapis” je zbirka samo z metadata, ki vsebuje informacije, kot so naslovi knjig, avtorji, ISBN-ji itd., medtem ko “datoteke” so zbirke, ki vsebujejo dejanske datoteke (pdf, epub). Na koncu smo se odločili za razmeroma preprost standard. Je precej ohlapen, nenormativen in še v razvoju. <strong>Torrenti.</strong> Datoteke z metadata in mape z binarnimi podatki so lahko združene v torrentih, z enim torrentom na datoteko z metadata ali enim torrentom na mapo z binarnimi podatki. Torrenti morajo imeti izvirno ime datoteke/imenika plus pripono <code>.torrent</code> kot svoje ime datoteke. <a %(wikipedia_annas_archive)s>Annin Arhiv</a> je postal daleč največja senčna knjižnica na svetu in edina senčna knjižnica te velikosti, ki je popolnoma odprtokodna in odprta za podatke. Spodaj je tabela z naše strani Datasets (rahlo spremenjena): To smo dosegli na tri načine: Zrcaljenje obstoječih odprtokodnih senčnih knjižnic (kot sta Sci-Hub in Library Genesis). Pomoč senčnim knjižnicam, ki želijo biti bolj odprte, vendar niso imele časa ali sredstev za to (kot je zbirka stripov Libgen). Strganje knjižnic, ki ne želijo deliti v velikem obsegu (kot je Z-Library). Za (2) in (3) zdaj sami upravljamo z obsežno zbirko torrentov (100-ine TB-jev). Do sedaj smo te zbirke obravnavali kot enkratne, kar pomeni prilagojeno infrastrukturo in organizacijo podatkov za vsako zbirko. To dodaja znatne stroške vsakemu izidu in otežuje izvedbo bolj postopnih izdaj. Zato smo se odločili standardizirati naše izdaje. To je tehnična objava na blogu, v kateri predstavljamo naš standard: <strong>Annini Arhivski Kontejnerji</strong>. Annin Arhiv Kontejnerji (AAK): standardizacija izdaj iz največje senčne knjižnice na svetu Annin Arhiv je postal največja senčna knjižnica na svetu, kar zahteva standardizacijo naših izdaj. 300GB+ naslovnic knjig izdanih Na koncu z veseljem objavljamo majhno izdajo. V sodelovanju z ljudmi, ki upravljajo fork Libgen.rs, delimo vse njihove naslovnice knjig prek torrentov in IPFS. To bo porazdelilo obremenitev ogledovanja naslovnic med več strojev in jih bolje ohranilo. V mnogih (vendar ne vseh) primerih so naslovnice knjig vključene v same datoteke, zato so to nekakšni "izvedeni podatki". Vendar pa je imeti jih v IPFS še vedno zelo koristno za vsakodnevno delovanje tako Anninega Arhiva kot različnih forkov Library Genesis. Kot običajno, lahko to izdajo najdete v Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Annin Arhiv</a>). Tukaj ne bomo povezali, vendar jo lahko zlahka najdete. Upamo, da lahko malo upočasnimo tempo, zdaj ko imamo dostojno alternativo Z-Library. Ta delovna obremenitev ni posebej trajnostna. Če vas zanima pomoč pri programiranju, strežniških operacijah ali delu na ohranjanju, se nam vsekakor oglasite. Še vedno je veliko <a %(annas_archive)s>dela, ki ga je treba opraviti</a>. Hvala za vaše zanimanje in podporo. Preklop na ElasticSearch Nekatera iskanja so trajala zelo dolgo, do te mere, da so zasedla vse odprte povezave. Privzeto ima MySQL minimalno dolžino besede, ali pa lahko vaš indeks postane res velik. Ljudje so poročali, da ne morejo iskati "Ben Hur". Iskanje je bilo le nekoliko hitro, ko je bilo popolnoma naloženo v pomnilnik, kar je zahtevalo, da smo dobili dražji stroj za izvajanje tega, poleg nekaterih ukazov za prednalaganje indeksa ob zagonu. Ne bi ga mogli enostavno razširiti za gradnjo novih funkcij, kot so boljša <a %(wikipedia_cjk_characters)s>tokenizacija za jezike brez presledkov</a>, filtriranje/faceting, razvrščanje, predlogi "ste mislili", samodokončanje in tako naprej. Ena od naših <a %(annas_archive)s>vstopnic</a> je bila zbirka težav z našim iskalnim sistemom. Uporabljali smo MySQL iskanje s polnim besedilom, saj smo imeli vse podatke v MySQL. Vendar je imelo svoje omejitve: Po pogovorih z več strokovnjaki smo se odločili za ElasticSearch. Ni bil popoln (njihovi privzeti predlogi "ste mislili" in funkcije samodokončanja so slabe), vendar je bil na splošno veliko boljši od MySQL za iskanje. Še vedno nismo <a %(youtube)s>preveč navdušeni</a> nad uporabo za katero koli kritično misijo (čeprav so naredili veliko <a %(elastic_co)s>napredka</a>), vendar smo na splošno zelo zadovoljni s prehodom. Za zdaj smo implementirali veliko hitrejše iskanje, boljšo podporo za jezike, boljše razvrščanje po pomembnosti, različne možnosti razvrščanja in filtriranje po jeziku/vrsti knjige/vrsti datoteke. Če vas zanima, kako deluje, <a %(annas_archive_l140)s>si</a> <a %(annas_archive_l1115)s>oglejte</a> <a %(annas_archive_l1635)s>to</a>. Je precej dostopno, čeprav bi lahko uporabili še nekaj komentarjev… Annin Arhiv je popolnoma odprtokoden Verjamemo, da bi morale biti informacije brezplačne, in naša lastna koda ni izjema. Vso našo kodo smo objavili na naši zasebno gostovani instance Gitlaba: <a %(annas_archive)s>Annina Programska Oprema</a>. Prav tako uporabljamo sledilnik težav za organizacijo našega dela. Če želite sodelovati pri našem razvoju, je to odličen kraj za začetek. Da vam damo okus stvari, na katerih delamo, si oglejte naše nedavno delo na izboljšavah zmogljivosti na strani odjemalca. Ker še nismo implementirali straniščanja, bi pogosto vrnili zelo dolge strani z rezultati iskanja, s 100-200 rezultati. Nismo želeli prehitro prekiniti rezultatov iskanja, vendar je to pomenilo, da bi upočasnilo nekatere naprave. Za to smo implementirali majhen trik: večino rezultatov iskanja smo zavili v HTML komentarje (<code><!-- --></code>), nato pa napisali majhen Javascript, ki bi zaznal, kdaj naj rezultat postane viden, v tem trenutku pa bi razpakirali komentar: DOM "virtualizacija" izvedena v 23 vrsticah, brez potrebe po naprednih knjižnicah! To je vrsta hitre pragmatične kode, ki jo ustvarite, ko imate omejen čas in resnične težave, ki jih je treba rešiti. Poročali so, da naše iskanje zdaj dobro deluje na počasnih napravah! Še en velik napor je bil avtomatizirati gradnjo baze podatkov. Ko smo začeli, smo preprosto naključno združili različne vire. Zdaj jih želimo posodabljati, zato smo napisali kup skriptov za prenos novih metadata iz dveh forkov Library Genesis in jih integrirali. Cilj ni le, da je to koristno za naš arhiv, ampak tudi, da olajšamo delo vsakomur, ki želi raziskovati metadata senčnih knjižnic. Cilj bi bil Jupyter zvezek, ki ima na voljo vse vrste zanimivih metadata, da lahko opravimo več raziskav, kot je ugotavljanje, kakšen <a %(blog)s>odstotek ISBN-jev je ohranjen za vedno</a>. Na koncu smo prenovili naš sistem donacij. Zdaj lahko uporabite kreditno kartico za neposredno nakazilo denarja v naše kripto denarnice, ne da bi morali vedeti kaj o kriptovalutah. Še naprej bomo spremljali, kako dobro to deluje v praksi, vendar je to velik korak naprej. Z zaprtjem Z-Library in aretacijo (domnevnih) ustanoviteljev smo delali neprekinjeno, da bi zagotovili dobro alternativo z Anninim Arhivom (tukaj ga ne bomo povezali, vendar ga lahko poiščete na Googlu). Tukaj je nekaj stvari, ki smo jih nedavno dosegli. Annina posodobitev: popolnoma odprtokodni arhiv, ElasticSearch, 300 GB+ naslovnic knjig Delali smo neprekinjeno, da bi zagotovili dobro alternativo z Anninim Arhivom. Tukaj je nekaj stvari, ki smo jih nedavno dosegli. Analiza Semantične podvojitve (različni skeni iste knjige) je teoretično mogoče filtrirati, vendar je to zapleteno. Ko smo ročno pregledovali stripe, smo našli preveč lažnih pozitivnih rezultatov. Obstajajo nekatere podvojitve zgolj po MD5, kar je relativno potratno, vendar bi njihovo filtriranje prineslo le približno 1% in prihranka. Na tej ravni je to še vedno približno 1TB, vendar tudi na tej ravni 1TB ni res pomemben. Raje ne bi tvegali nenamernega uničenja podatkov v tem procesu. Našli smo kup neknjižnih podatkov, kot so filmi, ki temeljijo na stripih. To se zdi tudi potratno, saj so ti že široko dostopni po drugih poteh. Vendar smo ugotovili, da ne moremo preprosto filtrirati filmskih datotek, saj obstajajo tudi <em>interaktivne stripovske knjige</em>, ki so bile izdane na računalniku, nekdo pa jih je posnel in shranil kot filme. Na koncu bi lahko z brisanjem iz zbirke prihranili le nekaj odstotkov. Potem smo se spomnili, da smo zbiratelji podatkov, in ljudje, ki bodo to zrcalili, so prav tako zbiratelji podatkov, zato, "KAJ MISLITE, BRISATI?!" :) Ko dobite 95TB podatkov v svoj shranjevalni grozd, poskušate ugotoviti, kaj sploh je tam notri… Naredili smo nekaj analiz, da bi videli, ali lahko nekoliko zmanjšamo velikost, na primer z odstranitvijo podvojenih datotek. Tukaj je nekaj naših ugotovitev: Zato vam predstavljamo celotno, nespremenjeno zbirko. To je veliko podatkov, vendar upamo, da bo dovolj ljudi, ki bodo kljub temu skrbeli za njeno deljenje. Sodelovanje Glede na svojo velikost je bila ta zbirka že dolgo na našem seznamu želja, zato smo po uspehu z varnostno kopijo Z-Library usmerili pogled na to zbirko. Sprva smo jo neposredno strgali, kar je bil kar izziv, saj njihov strežnik ni bil v najboljšem stanju. Na ta način smo pridobili približno 15TB, vendar je šlo počasi. Na srečo smo uspeli stopiti v stik z operaterjem knjižnice, ki se je strinjal, da nam pošlje vse podatke neposredno, kar je bilo veliko hitreje. Kljub temu je trajalo več kot pol leta, da smo prenesli in obdelali vse podatke, in skoraj smo jih izgubili zaradi okvare diska, kar bi pomenilo začeti znova. Ta izkušnja nas je prepričala, da je pomembno, da te podatke čim prej spravimo v javnost, da jih lahko zrcalimo široko in daleč. Smo le en ali dva nesrečno časovno usklajena incidenta stran od tega, da bi to zbirko za vedno izgubili! Zbirka Hitro premikanje pomeni, da je zbirka nekoliko neorganizirana… Poglejmo si jo. Predstavljajte si, da imamo datotečni sistem (ki ga v resnici razdeljujemo po torrentih): Prva mapa, <code>/repository</code>, je bolj strukturiran del tega. Ta mapa vsebuje tako imenovane "tisoč mape": mape, vsaka s tisoč datotekami, ki so postopoma oštevilčene v bazi podatkov. Mapa <code>0</code> vsebuje datoteke s comic_id 0–999 in tako naprej. To je isti sistem, ki ga Library Genesis uporablja za svoje zbirke fikcije in nefikcije. Ideja je, da se vsaka "tisoč mapa" samodejno spremeni v torrent, takoj ko je napolnjena. Vendar pa operater Libgen.li nikoli ni ustvaril torrentov za to zbirko, zato so tisoč mape verjetno postale nepraktične in so se umaknile "neurejenim mapam". Te so <code>/comics0</code> do <code>/comics4</code>. Vse vsebujejo edinstvene strukture map, ki so verjetno imele smisel za zbiranje datotek, vendar nam zdaj ne pomenijo veliko. Na srečo metadata še vedno neposredno sklicuje na vse te datoteke, zato njihova organizacija shranjevanja na disku dejansko ni pomembna! Metadata je na voljo v obliki MySQL baze podatkov. To lahko prenesete neposredno s spletne strani Libgen.li, vendar jo bomo tudi mi naredili na voljo v torrentu, skupaj z našo lastno tabelo z vsemi MD5 hashi. <q>Dr. Barbara Gordon se poskuša izgubiti v vsakdanjem svetu knjižnice…</q> Libgen vilice Najprej nekaj ozadja. Morda poznate Library Genesis zaradi njihove epske zbirke knjig. Manj ljudi ve, da so prostovoljci Library Genesis ustvarili tudi druge projekte, kot so obsežna zbirka revij in standardnih dokumentov, popolna varnostna kopija Sci-Hub (v sodelovanju z ustanoviteljico Sci-Hub, Alexandro Elbakyan) in resnično ogromna zbirka stripov. Na neki točki so se različni operaterji zrcal Library Genesis razšli, kar je povzročilo trenutno situacijo z več različnimi "vilicami", ki še vedno nosijo ime Library Genesis. Vilica Libgen.li ima edinstveno to zbirko stripov, pa tudi obsežno zbirko revij (na kateri tudi delamo). Zbiranje sredstev Te podatke objavljamo v velikih kosih. Prvi torrent je <code>/comics0</code>, ki smo ga združili v eno ogromno 12TB .tar datoteko. To je boljše za vaš trdi disk in torrent programsko opremo kot milijon manjših datotek. Kot del te izdaje organiziramo zbiranje sredstev. Želimo zbrati 20.000 $ za kritje operativnih in pogodbenih stroškov za to zbirko ter omogočiti tekoče in prihodnje projekte. Imamo nekaj <em>ogromnih</em> v pripravi. <em>Koga podpirate z vašo donacijo?</em> Na kratko: podpiramo varnostno kopiranje vsega znanja in kulture človeštva ter omogočamo enostaven dostop do tega. Vsa naša koda in podatki so odprtokodni, smo popolnoma prostovoljski projekt in do sedaj smo shranili 125TB knjig (poleg obstoječih torrentov Libgen in Scihub). Na koncu gradimo vztrajnik, ki omogoča in spodbuja ljudi, da najdejo, skenirajo in varnostno kopirajo vse knjige na svetu. O našem glavnem načrtu bomo pisali v prihodnji objavi. :) Če donirate za 12-mesečno članstvo "Amazing Archivist" (780 $), lahko <strong>“posvojite torrent”</strong>, kar pomeni, da bomo vaše uporabniško ime ali sporočilo vključili v ime ene od torrent datotek! Donirate lahko tako, da obiščete <a %(wikipedia_annas_archive)s>Anin Arhiv</a> in kliknete gumb "Doniraj". Prav tako iščemo več prostovoljcev: programerje, raziskovalce varnosti, strokovnjake za anonimno trgovanje in prevajalce. Podprete nas lahko tudi z zagotavljanjem gostiteljskih storitev. In seveda, prosimo, delite naše torrente! Hvala vsem, ki ste nas že tako velikodušno podprli! Resnično delate razliko. Tukaj so torrenti, ki so bili do sedaj izdani (še vedno obdelujemo ostale): Vse torrente lahko najdete na <a %(wikipedia_annas_archive)s>Anin Arhiv</a> pod "Datasets" (ne povezujemo se neposredno tja, da povezave do tega bloga ne bi bile odstranjene z Reddita, Twitterja itd.). Od tam sledite povezavi na spletno stran Tor. <a %(news_ycombinator)s>Razpravljajte na Hacker News</a> Kaj sledi? Veliko torrentov je odličnih za dolgoročno ohranjanje, vendar ne toliko za vsakodnevni dostop. Sodelovali bomo z gostiteljskimi partnerji, da vse te podatke spravimo na splet (saj Anin Arhiv ničesar ne gosti neposredno). Seveda boste te povezave za prenos lahko našli na Aninem Arhivu. Prav tako vabimo vse, da kaj naredijo s temi podatki! Pomagajte nam jih bolje analizirati, odstraniti podvojene, jih postaviti na IPFS, jih premešati, trenirati svoje AI modele z njimi in tako naprej. Vse je vaše, in komaj čakamo, da vidimo, kaj boste storili z njimi. Nazadnje, kot smo že povedali, imamo še nekaj velikih izdaj v pripravi (če bi <em>nekdo</em> lahko <em>po nesreči</em> poslal izpisek <em>določene</em> ACS4 baze podatkov, veste, kje nas najti...), kot tudi gradnjo vztrajnika za varnostno kopiranje vseh knjig na svetu. Zato ostanite z nami, šele začenjamo. - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Največja senčna knjižnica stripov je verjetno tista določenega forka Library Genesis: Libgen.li. En sam administrator, ki vodi to spletno stran, je uspel zbrati neverjetno zbirko stripov z več kot 2 milijonoma datotek, ki skupaj obsegajo več kot 95TB. Vendar pa, za razliko od drugih zbirk Library Genesis, ta ni bila na voljo v celoti prek torrentov. Do teh stripov ste lahko dostopali le posamezno prek njegovega počasnega osebnega strežnika — ena sama točka odpovedi. Do danes! V tej objavi vam bomo povedali več o tej zbirki in o naši zbiralni akciji za podporo več takšnega dela. Annin Arhiv je varnostno kopiral največjo senčno knjižnico stripov na svetu (95TB) — lahko pomagate pri sejanju Največja senčna knjižnica stripov na svetu je imela eno samo točko odpovedi.. do danes. Opozorilo: ta objava na blogu je zastarela. Odločili smo se, da IPFS še ni pripravljen za glavni čas. Še vedno bomo povezovali datoteke na IPFS iz Anninega arhiva, ko bo mogoče, vendar jih ne bomo več gostili sami, niti ne priporočamo drugim, da zrcalijo z uporabo IPFS. Prosimo, oglejte si našo stran Torrents, če želite pomagati pri ohranjanju naše zbirke. Postavljanje 5.998.794 knjig na IPFS Množično razmnoževanje kopij Nazaj k našemu prvotnemu vprašanju: kako lahko trdimo, da bomo naše zbirke ohranili za vedno? Glavni problem tukaj je, da se je naša zbirka <a %(torrents_stats)s>hitro povečevala</a> z zbiranjem in odprtokodnim deljenjem nekaterih velikih zbirk (poleg neverjetnega dela, ki so ga že opravile druge knjižnice s prostimi podatki, kot sta Sci-Hub in Library Genesis). Ta rast podatkov otežuje zrcaljenje zbirk po vsem svetu. Shranjevanje podatkov je drago! Vendar smo optimistični, še posebej ob opazovanju naslednjih treh trendov. <a %(annas_archive_stats)s>Skupna velikost</a> naših zbirk v zadnjih nekaj mesecih, razčlenjena po številu torrent sejalcev. Trendi cen HDD iz različnih virov (kliknite za ogled študije). <a %(critical_window_chinese)s>Kitajska različica 中文版</a>, razpravljajte na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Pobirali smo nizko viseče sadove To neposredno sledi iz naših zgoraj obravnavanih prioritet. Raje delamo na osvobajanju velikih zbirk najprej. Zdaj, ko smo si zagotovili nekatere največje zbirke na svetu, pričakujemo, da bo naša rast veliko počasnejša. Še vedno obstaja dolg rep manjših zbirk, in nove knjige se skenirajo ali objavljajo vsak dan, vendar bo stopnja verjetno veliko počasnejša. Morda se bomo še vedno podvojili ali celo potrojili v velikosti, vendar v daljšem časovnem obdobju. Izboljšave OCR. Prioritete Znanstvena in inženirska programska koda Fikcijske ali zabavne različice vsega zgoraj naštetega Geografski podatki (npr. zemljevidi, geološke raziskave) Notranji podatki podjetij ali vlad (puščanja) Merilni podatki, kot so znanstvene meritve, ekonomski podatki, korporativna poročila Zapiski o metadata na splošno (o nefikciji in fikciji; o drugih medijih, umetnosti, ljudeh itd.; vključno z ocenami) Strokovne knjige Revije, časopisi, priročniki o nefikciji Prepisi govorov, dokumentarcev, podcastov o nefikciji Organski podatki, kot so DNK sekvence, semena rastlin ali mikrobni vzorci Akademski članki, revije, poročila Znanstvena in inženirska spletna mesta, spletne razprave Prepisi pravnih ali sodnih postopkov Edinstveno ogrožena uničenja (npr. zaradi vojne, zmanjšanja financiranja, tožb ali političnega preganjanja) Redka Edinstveno zapostavljena Zakaj nam je tako mar za članke in knjige? Pustimo ob strani naše temeljno prepričanje o ohranjanju na splošno — o tem bi lahko napisali še en prispevek. Zakaj torej članki in knjige posebej? Odgovor je preprost: <strong>gostota informacij</strong>. Na megabajt shranjevanja pisano besedilo shrani največ informacij med vsemi mediji. Medtem ko nam je mar za znanje in kulturo, nam je za prvo bolj mar. Na splošno najdemo hierarhijo gostote informacij in pomembnosti ohranjanja, ki je videti približno takole: Razvrstitev na tem seznamu je nekoliko arbitrarna — več elementov je izenačenih ali pa se naša ekipa ne strinja — in verjetno pozabljamo na nekatere pomembne kategorije. Vendar je to približno, kako dajemo prednost. Nekateri od teh elementov so preveč različni od drugih, da bi nas skrbelo (ali pa so že poskrbljeni s strani drugih institucij), kot so organski podatki ali geografski podatki. Vendar je večina elementov na tem seznamu dejansko pomembna za nas. Drug velik dejavnik pri naši prioritizaciji je, koliko je določeno delo ogroženo. Raje se osredotočamo na dela, ki so: Na koncu nam je pomemben obseg. Imamo omejen čas in denar, zato bi raje porabili mesec dni za reševanje 10.000 knjig kot 1.000 knjig — če so približno enako dragocene in ogrožene. <em><q>Izgubljenega ni mogoče povrniti; vendar rešimo, kar ostane: ne z zakladi in ključavnicami, ki jih varujejo pred očmi in uporabo javnosti, s tem da jih prepustimo zobu časa, temveč z množičnim razmnoževanjem kopij, ki jih postavi izven dosega nesreče.</q></em><br>— Thomas Jefferson, 1791 Sence knjižnic Koda je lahko odprtokodna na Githubu, vendar Github kot celota ne more biti enostavno zrcaljen in tako ohranjen (čeprav v tem primeru obstajajo dovolj razširjene kopije večine repozitorijev kode) Zapise o metadata je mogoče prosto pregledovati na spletni strani Worldcat, vendar jih ni mogoče prenesti v velikem obsegu (dokler jih nismo <a %(worldcat_scrape)s>strgali</a>) Reddit je brezplačen za uporabo, vendar je nedavno uvedel stroge ukrepe proti strganju, zaradi podatkovno lačnih LLM treningov (več o tem kasneje) Obstaja veliko organizacij s podobnimi misijami in podobnimi prioritetami. Dejansko obstajajo knjižnice, arhivi, laboratoriji, muzeji in druge institucije, zadolžene za tovrstno ohranjanje. Mnoge od teh so dobro financirane, s strani vlad, posameznikov ali podjetij. Vendar imajo eno veliko slepo točko: pravni sistem. Tukaj leži edinstvena vloga senčnih knjižnic in razlog, zakaj obstaja Annin Arhiv. Lahko počnemo stvari, ki jih drugim institucijam ni dovoljeno. Zdaj, ni (pogosto) tako, da lahko arhiviramo materiale, ki jih drugje ni dovoljeno ohranjati. Ne, v mnogih krajih je zakonito zgraditi arhiv s katerimikoli knjigami, članki, revijami in tako naprej. Toda pravna arhiva pogosto primanjkuje <strong>redundance in dolgotrajnosti</strong>. Obstajajo knjige, od katerih obstaja le en izvod v neki fizični knjižnici nekje. Obstajajo zapisi o metadata, ki jih varuje samo eno podjetje. Obstajajo časopisi, ki so ohranjeni le na mikrofilmu v enem arhivu. Knjižnice lahko doživijo zmanjšanje financiranja, podjetja lahko bankrotirajo, arhivi pa so lahko bombardirani in požgani do tal. To ni hipotetično — to se dogaja ves čas. Kar lahko edinstveno storimo v Anninem arhivu, je shranjevanje številnih kopij del v velikem obsegu. Lahko zbiramo članke, knjige, revije in še več ter jih distribuiramo v velikih količinah. Trenutno to počnemo prek torrentov, vendar natančne tehnologije niso pomembne in se bodo sčasoma spreminjale. Pomembno je, da se številne kopije razdelijo po vsem svetu. Ta citat izpred več kot 200 let še vedno drži: Kratka opomba o javni domeni. Ker se Annin arhiv edinstveno osredotoča na dejavnosti, ki so nezakonite v mnogih delih sveta, se ne ukvarjamo s široko dostopnimi zbirkami, kot so knjige v javni domeni. Pravne osebe pogosto že dobro skrbijo za to. Vendar pa obstajajo premisleki, zaradi katerih včasih delamo na javno dostopnih zbirkah: - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Stroški shranjevanja se še naprej eksponentno znižujejo 3. Izboljšave v gostoti informacij Trenutno shranjujemo knjige v surovih formatih, kot so nam bile posredovane. Seveda so stisnjene, vendar so pogosto še vedno veliki skeni ali fotografije strani. Do zdaj so bile edine možnosti za zmanjšanje skupne velikosti naše zbirke bolj agresivna kompresija ali deduplikacija. Vendar pa sta obe možnosti preveč izgubljivi za naš okus, da bi dosegli dovolj velike prihranke. Močna kompresija fotografij lahko naredi besedilo komaj berljivo. Dedupikacija pa zahteva visoko zaupanje, da so knjige popolnoma enake, kar je pogosto preveč netočno, še posebej, če je vsebina enaka, vendar so skeni narejeni ob različnih priložnostih. Vedno je obstajala tretja možnost, vendar je bila njena kakovost tako obupna, da je nismo nikoli upoštevali: <strong>OCR ali optično prepoznavanje znakov</strong>. To je proces pretvorbe fotografij v navadno besedilo z uporabo umetne inteligence za prepoznavanje znakov na fotografijah. Orodja za to obstajajo že dolgo in so bila precej spodobna, vendar "precej spodobno" ni dovolj za namene ohranjanja. Vendar so nedavni multimodalni modeli globokega učenja dosegli izjemno hiter napredek, čeprav še vedno z visokimi stroški. Pričakujemo, da se bosta natančnost in stroški v prihodnjih letih dramatično izboljšala, do točke, ko bo postalo realno uporabiti to na celotno našo knjižnico. Ko se to zgodi, bomo verjetno še vedno ohranili izvirne datoteke, vendar bi lahko poleg tega imeli veliko manjšo različico naše knjižnice, ki bi jo večina ljudi želela zrcaliti. Ključna točka je, da se surovo besedilo še bolje stisne in je veliko lažje za deduplikacijo, kar nam prinaša še več prihrankov. Na splošno ni nerealno pričakovati vsaj 5- do 10-kratno zmanjšanje skupne velikosti datotek, morda celo več. Tudi pri konservativnem 5-kratnem zmanjšanju bi gledali na <strong>1.000–3.000 $ v 10 letih, tudi če se naša knjižnica potroji po velikosti</strong>. V času pisanja so <a %(diskprices)s>cene diskov</a> na TB približno 12 $ za nove diske, 8 $ za rabljene diske in 4 $ za trak. Če smo konservativni in gledamo samo na nove diske, to pomeni, da shranjevanje petabajta stane približno 12.000 $. Če predpostavimo, da se bo naša knjižnica potrojila z 900 TB na 2,7 PB, bi to pomenilo 32.400 $ za zrcaljenje celotne knjižnice. Če dodamo elektriko, stroške druge strojne opreme in tako naprej, zaokrožimo na 40.000 $. Ali s trakom bolj kot 15.000–20.000 $. Po eni strani <strong>15.000–40.000 $ za vsoto vsega človeškega znanja je ugodno</strong>. Po drugi strani pa je nekoliko strmo pričakovati tone popolnih kopij, še posebej, če bi želeli, da ti ljudje še naprej delijo svoje torrente v korist drugih. To je danes. Toda napredek koraka naprej: Stroški trdih diskov na TB so bili v zadnjih 10 letih približno prepolovljeni in bodo verjetno še naprej padali s podobnim tempom. Trak se zdi na podobni poti. Cene SSD-jev padajo še hitreje in bi lahko do konca desetletja prehitele cene HDD-jev. Če to drži, bi lahko čez 10 let gledali na samo 5.000–13.000 $ za zrcaljenje celotne zbirke (1/3), ali še manj, če bomo manj rasli v velikosti. Čeprav je to še vedno veliko denarja, bo to dosegljivo za mnoge ljudi. In morda bo še bolje zaradi naslednje točke… Na Anninem Arhivu nas pogosto sprašujejo, kako lahko trdimo, da bomo naše zbirke ohranili za vedno, ko pa skupna velikost že dosega 1 petabajt (1000 TB) in še vedno raste. V tem članku bomo pogledali našo filozofijo in videli, zakaj je naslednje desetletje ključno za našo misijo ohranjanja človeškega znanja in kulture. Kritično obdobje Če so te napovedi točne, moramo <strong>samo počakati nekaj let</strong>, preden bo naša celotna zbirka široko zrcaljena. Tako bo, po besedah Thomasa Jeffersona, "postavljena izven dosega nesreče." Na žalost je pojav LLM-jev in njihovega podatkovno lačnega učenja veliko imetnikov avtorskih pravic postavil v obrambni položaj. Še bolj kot so že bili. Mnogi spletni strani otežujejo strganje in arhiviranje, tožbe letijo naokoli, medtem pa fizične knjižnice in arhivi še naprej ostajajo zanemarjeni. Lahko pričakujemo, da se bodo ti trendi še poslabšali, in da bo veliko del izgubljenih, preden bodo vstopila v javno domeno. <strong>Smo na pragu revolucije v ohranjanju, vendar <q>izgubljenega ni mogoče povrniti.</q></strong> Imamo kritično obdobje približno 5-10 let, v katerem je še vedno precej drago upravljati senčno knjižnico in ustvariti veliko zrcal po svetu, in v katerem dostop še ni popolnoma zaprt. Če lahko premostimo to obdobje, bomo resnično ohranili človeško znanje in kulturo za vedno. Ne smemo dovoliti, da ta čas gre v nič. Ne smemo dovoliti, da se to kritično obdobje zapre pred nami. Gremo. Kritično obdobje senčnih knjižnic Kako lahko trdimo, da bomo naše zbirke ohranili za vedno, ko pa že dosegajo 1 PB? Zbirka Nekaj več informacij o zbirki. <a %(duxiu)s>Duxiu</a> je ogromna baza podatkov skeniranih knjig, ki jo je ustvarila <a %(chaoxing)s>SuperStar Digital Library Group</a>. Večina so akademske knjige, skenirane, da bi jih digitalno omogočili univerzam in knjižnicam. Za našo angleško govorečo publiko imata <a %(library_princeton)s>Princeton</a> in <a %(guides_lib_uw)s>University of Washington</a> dobre preglede. Obstaja tudi odličen članek, ki daje več ozadja: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (poiščite ga v Anninem arhivu). Knjige iz Duxiu so bile dolgo piratizirane na kitajskem internetu. Običajno jih preprodajalci prodajajo za manj kot dolar. Običajno se distribuirajo z uporabo kitajskega ekvivalenta Google Drive, ki je bil pogosto vdrt, da omogoča več prostora za shranjevanje. Nekatere tehnične podrobnosti najdete <a %(github_duty_machine)s>tukaj</a> in <a %(github_821_github_io)s>tukaj</a>. Čeprav so bile knjige poljavnostno distribuirane, jih je precej težko pridobiti v velikem obsegu. To smo imeli visoko na našem seznamu opravil in dodelili več mesecev polnega delovnega časa za to. Vendar pa se je pred kratkim neverjeten, izjemen in nadarjen prostovoljec obrnil na nas in nam povedal, da je že opravil vse to delo — z velikimi stroški. Delili so celotno zbirko z nami, ne da bi pričakovali karkoli v zameno, razen zagotovila dolgoročne ohranitve. Resnično izjemno. Strinjali so se, da prosijo za pomoč na ta način, da se zbirka OCR-ira. Zbirka obsega 7.543.702 datotek. To je več kot Library Genesis nefikcija (približno 5,3 milijona). Skupna velikost datotek je približno 359TB (326TiB) v trenutni obliki. Odprti smo za druge predloge in ideje. Samo kontaktirajte nas. Oglejte si Annin arhiv za več informacij o naših zbirkah, prizadevanjih za ohranitev in kako lahko pomagate. Hvala! Primeri strani Da nam dokažete, da imate dober postopek, so tukaj nekaj primerov strani, s katerimi lahko začnete, iz knjige o superprevodnikih. Vaš postopek bi moral pravilno obravnavati matematiko, tabele, grafe, opombe in tako naprej. Pošljite svoje obdelane strani na naš e-poštni naslov. Če bodo videti dobro, vam bomo zasebno poslali več, in pričakujemo, da boste lahko hitro izvedli svoj postopek tudi na teh. Ko bomo zadovoljni, lahko sklenemo dogovor. - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kitajska različica 中文版</a>, <a %(news_ycombinator)s>Razpravljaj na Hacker News</a> To je kratek blog prispevek. Iščemo podjetje ali institucijo, ki bi nam pomagala z OCR in ekstrakcijo besedila za ogromno zbirko, ki smo jo pridobili, v zameno za ekskluziven zgodnji dostop. Po obdobju embarga bomo seveda izdali celotno zbirko. Visokokakovostno akademsko besedilo je izjemno koristno za usposabljanje LLM-jev. Čeprav je naša zbirka kitajska, bi to moralo biti koristno tudi za usposabljanje angleških LLM-jev: modeli se zdijo, da kodirajo koncepte in znanje ne glede na izvorni jezik. Za to je treba besedilo izvleči iz skenov. Kaj pridobi Annin arhiv iz tega? Iskanje po celotnem besedilu knjig za svoje uporabnike. Ker so naši cilji usklajeni s cilji razvijalcev LLM, iščemo sodelavca. Pripravljeni smo vam dati <strong>ekskluziven zgodnji dostop do te zbirke v velikem obsegu za 1 leto</strong>, če lahko izvedete pravilno OCR in izvlečete besedilo. Če ste pripravljeni z nami deliti celotno kodo vašega postopka, smo pripravljeni podaljšati embargo na zbirko. Ekskluziven dostop za podjetja LLM do največje zbirke kitajskih nefikcijskih knjig na svetu <em><strong>TL;DR:</strong> Annin Arhiv je pridobil edinstveno zbirko 7,5 milijona / 350TB kitajskih nefikcijskih knjig — večjo kot Library Genesis. Pripravljeni smo dati podjetju LLM ekskluziven dostop v zameno za visokokakovostno OCR in ekstrakcijo besedila.</em> Arhitektura sistema Recimo, da ste našli nekaj podjetij, ki so pripravljena gostiti vašo spletno stran, ne da bi vas zaprli — imenujmo jih “ponudniki, ki ljubijo svobodo” 😄. Hitro boste ugotovili, da je gostovanje vsega pri njih precej drago, zato boste morda želeli najti nekaj “poceni ponudnikov” in dejansko gostovanje opraviti tam, s posredovanjem prek ponudnikov, ki ljubijo svobodo. Če to storite pravilno, poceni ponudniki nikoli ne bodo vedeli, kaj gostite, in nikoli ne bodo prejeli nobenih pritožb. Pri vseh teh ponudnikih obstaja tveganje, da vas vseeno zaprejo, zato potrebujete tudi redundanco. To potrebujemo na vseh ravneh našega sklada. Ena nekoliko svobodoljubna družba, ki se je postavila v zanimiv položaj, je Cloudflare. Trdili so, da niso ponudnik gostovanja, ampak pripomoček, kot je ISP. Zato niso podvrženi zahtevam za odstranitev po DMCA ali drugih zahtevah in posredujejo vse zahteve vašemu dejanskemu ponudniku gostovanja. Šli so celo tako daleč, da so šli na sodišče, da bi zaščitili to strukturo. Zato jih lahko uporabimo kot še eno plast predpomnjenja in zaščite. Cloudflare ne sprejema anonimnih plačil, zato lahko uporabljamo le njihov brezplačni načrt. To pomeni, da ne moremo uporabljati njihovih funkcij za uravnoteženje obremenitve ali preklop v primeru napake. Zato smo to <a %(annas_archive_l255)s>implementirali sami</a> na ravni domene. Ob nalaganju strani bo brskalnik preveril, ali je trenutna domena še vedno na voljo, in če ni, prepiše vse URL-je na drugo domeno. Ker Cloudflare predpomni veliko strani, to pomeni, da lahko uporabnik pristane na naši glavni domeni, tudi če je proxy strežnik izklopljen, in nato ob naslednjem kliku preide na drugo domeno. Še vedno imamo tudi običajne operativne skrbi, kot so spremljanje zdravja strežnikov, beleženje napak v ozadju in sprednjem delu in tako naprej. Naša arhitektura preklopa v primeru napake omogoča večjo robustnost tudi na tem področju, na primer z zagonom popolnoma drugačnega nabora strežnikov na eni od domen. Na tej ločeni domeni lahko celo poganjamo starejše različice kode in podatkovnih nizov, v primeru da kritična napaka v glavni različici ostane neopažena. Lahko se tudi zaščitimo pred tem, da bi se Cloudflare obrnil proti nam, tako da ga odstranimo z ene od domen, kot je ta ločena domena. Možne so različne permutacije teh idej. Zaključek Bila je zanimiva izkušnja naučiti se, kako vzpostaviti robusten in odporen iskalnik senčne knjižnice. Obstaja še veliko podrobnosti, ki jih bomo delili v prihodnjih objavah, zato mi sporočite, o čem bi želeli izvedeti več! Kot vedno iščemo donacije za podporo temu delu, zato si oglejte stran Doniraj na Anninem Arhivu. Prav tako iščemo druge vrste podpore, kot so nepovratna sredstva, dolgoročni sponzorji, ponudniki plačil z visokim tveganjem, morda celo (okusni!) oglasi. In če želite prispevati svoj čas in veščine, vedno iščemo razvijalce, prevajalce in podobno. Hvala za vaše zanimanje in podporo. Inovacijski žetoni Začnimo z našim tehnološkim skladom. Namenoma je dolgočasen. Uporabljamo Flask, MariaDB in ElasticSearch. To je dobesedno vse. Iskanje je v veliki meri rešen problem in ga ne nameravamo ponovno izumiti. Poleg tega moramo naše <a %(mcfunley)s>inovacijske žetone</a> porabiti za nekaj drugega: da nas oblasti ne odstranijo. Kako zakonit ali nezakonit je torej pravzaprav Annin arhiv? To je večinoma odvisno od pravne jurisdikcije. Večina držav verjame v neko obliko avtorskih pravic, kar pomeni, da so ljudem ali podjetjem dodeljeni izključni monopol nad določenimi vrstami del za določeno obdobje. Mimogrede, v Anninem arhivu verjamemo, da čeprav obstajajo nekatere koristi, so avtorske pravice na splošno negativne za družbo — vendar je to zgodba za drugič. Ta izključni monopol nad določenimi deli pomeni, da je nezakonito, da kdorkoli zunaj tega monopola neposredno distribuira ta dela — vključno z nami. Vendar je Annin arhiv iskalnik, ki teh del ne distribuira neposredno (vsaj ne na naši spletni strani v jasnem omrežju), zato bi morali biti v redu, kajne? Ne ravno. V mnogih jurisdikcijah ni le nezakonito distribuirati avtorsko zaščitena dela, ampak tudi povezovati na mesta, ki to počnejo. Klasičen primer tega je ameriški zakon DMCA. To je najstrožji konec spektra. Na drugem koncu spektra bi teoretično lahko obstajale države brez kakršnih koli zakonov o avtorskih pravicah, vendar te v resnici ne obstajajo. Skoraj vsaka država ima v zakonih neko obliko avtorskih pravic. Izvrševanje je druga zgodba. Obstaja veliko držav z vladami, ki jih ne zanima izvrševanje zakonov o avtorskih pravicah. Obstajajo tudi države med obema skrajnostma, ki prepovedujejo distribucijo avtorsko zaščitenih del, vendar ne prepovedujejo povezovanja na taka dela. Druga stvar, ki jo je treba upoštevati, je na ravni podjetja. Če podjetje deluje v jurisdikciji, ki ne skrbi za avtorske pravice, vendar samo podjetje ni pripravljeno tvegati, lahko zaprejo vašo spletno stran takoj, ko se kdo pritoži nad njo. Nazadnje, velika skrb so plačila. Ker moramo ostati anonimni, ne moremo uporabljati tradicionalnih plačilnih metod. To nas pušča s kriptovalutami, in le majhen del podjetij jih podpira (obstajajo virtualne debetne kartice, plačane s kripto, vendar jih pogosto ne sprejemajo). - Anna in ekipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Vodim <a %(wikipedia_annas_archive)s>Annin arhiv</a>, največji odprtokodni neprofitni iskalnik za <a %(wikipedia_shadow_library)s>senčne knjižnice</a>, kot so Sci-Hub, Library Genesis in Z-Library. Naš cilj je omogočiti enostaven dostop do znanja in kulture ter na koncu zgraditi skupnost ljudi, ki skupaj arhivirajo in ohranjajo <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>vse knjige na svetu</a>. V tem članku bom pokazal, kako vodimo to spletno stran in edinstvene izzive, ki jih prinaša upravljanje spletne strani s spornim pravnim statusom, saj ni "AWS za senčne dobrodelne organizacije". <em>Oglejte si tudi sestrski članek <a %(blog_how_to_become_a_pirate_archivist)s>Kako postati piratski arhivar</a>.</em> Kako voditi senčno knjižnico: delovanje v Anninem arhivu Ni <q>AWS za senčne dobrodelne organizacije,</q> kako torej vodimo Annin arhiv? Orodja Aplikacijski strežnik: Flask, MariaDB, ElasticSearch, Docker. Razvoj: Gitlab, Weblate, Zulip. Upravljanje strežnikov: Ansible, Checkmk, UFW. Statično gostovanje Onion: Tor, Nginx. Proxy strežnik: Varnish. Poglejmo, katera orodja uporabljamo za dosego vsega tega. To se zelo razvija, ko naletimo na nove težave in najdemo nove rešitve. Obstajajo nekatere odločitve, pri katerih smo se večkrat premislili. Ena izmed njih je komunikacija med strežniki: prej smo za to uporabljali Wireguard, vendar smo ugotovili, da občasno preneha prenašati podatke ali pa jih prenaša le v eno smer. To se je zgodilo pri več različnih nastavitvah Wireguard, ki smo jih preizkusili, kot sta <a %(github_costela_wesher)s>wesher</a> in <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Prav tako smo poskusili tunelirati porte preko SSH, z uporabo autossh in sshuttle, vendar smo naleteli na <a %(github_sshuttle)s>težave tam</a> (čeprav mi še vedno ni jasno, ali autossh trpi zaradi težav TCP-over-TCP ali ne — zdi se mi kot neurejena rešitev, vendar morda je v resnici v redu?). Namesto tega smo se vrnili k neposrednim povezavam med strežniki, pri čemer smo skrili, da strežnik deluje na poceni ponudnikih z uporabo IP-filtriranja z UFW. To ima pomanjkljivost, da Docker ne deluje dobro z UFW, razen če uporabite <code>network_mode: "host"</code>. Vse to je nekoliko bolj nagnjeno k napakam, saj boste z le majhno napačno konfiguracijo izpostavili svoj strežnik internetu. Morda bi se morali vrniti k autossh — povratne informacije bi bile tukaj zelo dobrodošle. Prav tako smo se večkrat premislili glede Varnish proti Nginx. Trenutno nam je Varnish všeč, vendar ima svoje posebnosti in grobe robove. Enako velja za Checkmk: ni nam všeč, vendar za zdaj deluje. Weblate je bil v redu, vendar ne neverjeten — včasih se bojim, da bo izgubil moje podatke, kadar koli ga poskušam sinhronizirati z našim git repozitorijem. Flask je bil na splošno dober, vendar ima nekaj čudnih posebnosti, ki so zahtevale veliko časa za odpravljanje napak, kot so konfiguriranje prilagojenih domen ali težave z integracijo SqlAlchemy. Do sedaj so bili drugi orodji odlični: nimamo resnih pritožb glede MariaDB, ElasticSearch, Gitlab, Zulip, Docker in Tor. Vsa ta orodja so imela nekaj težav, vendar nič preveč resnega ali časovno potratnega. Skupnost Prvi izziv je lahko presenetljiv. Ni tehnični problem ali pravni problem. Je psihološki problem: opravljanje tega dela v senci je lahko izjemno osamljeno. Glede na to, kaj nameravate storiti, in vaš model grožnje, boste morda morali biti zelo previdni. Na enem koncu spektra imamo ljudi, kot je Alexandra Elbakyan*, ustanoviteljica Sci-Hub, ki je zelo odprta glede svojih dejavnosti. Vendar je v veliki nevarnosti, da bo aretirana, če bi v tem trenutku obiskala zahodno državo, in bi se lahko soočila z desetletji zaporne kazni. Je to tveganje, ki bi ga bili pripravljeni sprejeti? Mi smo na drugem koncu spektra; zelo pazimo, da ne puščamo nobenih sledi in imamo močno operativno varnost. * Kot je omenil "ynno" na HN, Alexandra sprva ni želela biti znana: "Njeni strežniki so bili nastavljeni tako, da so oddajali podrobna sporočila o napakah iz PHP, vključno s celotno potjo do izvorne datoteke, ki je bila pod imenikom /home/<USER>" Zato uporabite naključna uporabniška imena na računalnikih, ki jih uporabljate za te stvari, v primeru, da nekaj napačno konfigurirate. Ta skrivnostnost pa ima psihološko ceno. Večina ljudi ljubi, da so prepoznani za delo, ki ga opravljajo, in vendar ne morete prejeti nobenega priznanja za to v resničnem življenju. Tudi preproste stvari so lahko izziv, kot na primer, ko vas prijatelji vprašajo, kaj ste počeli (na neki točki "igranje z mojim NAS / homelab" postane staro). Zato je tako pomembno najti neko skupnost. Lahko se odpoveste nekaj operativne varnosti, če se zaupate nekaterim zelo bližnjim prijateljem, za katere veste, da jim lahko globoko zaupate. Tudi takrat bodite previdni, da ne zapišete ničesar, v primeru, da bi morali predati svoje e-pošte oblastem ali če bi bile njihove naprave kako drugače ogrožene. Še bolje je najti nekaj somišljenikov piratov. Če so vaši bližnji prijatelji zainteresirani, da se vam pridružijo, odlično! V nasprotnem primeru boste morda lahko našli druge na spletu. Na žalost je to še vedno nišna skupnost. Do sedaj smo našli le peščico drugih, ki so aktivni na tem področju. Dobri začetni kraji se zdijo forumi Library Genesis in r/DataHoarder. Ekipa Archive Team ima tudi somišljenike, čeprav delujejo znotraj zakona (tudi če v nekaterih sivih območjih zakona). Tradicionalne "warez" in piratske scene imajo tudi ljudi, ki razmišljajo na podoben način. Odprti smo za ideje o tem, kako spodbujati skupnost in raziskovati ideje. Prosto nam pošljite sporočilo na Twitterju ali Redditu. Morda bi lahko gostili nekakšen forum ali klepetalno skupino. Eden od izzivov je, da je to lahko hitro cenzurirano, če uporabljamo običajne platforme, zato bi morali to gostiti sami. Obstaja tudi kompromis med tem, da so te razprave popolnoma javne (večja možnost sodelovanja) in tem, da so zasebne (da potencialni "cilji" ne vedo, da jih bomo kmalu kopirali). O tem bomo morali razmisliti. Sporočite nam, če vas to zanima! Zaključek Upamo, da je to v pomoč novim piratskim arhivistom. Veselimo se, da vas lahko pozdravimo v tem svetu, zato ne oklevajte in se obrnite na nas. Ohranjajmo čim več svetovnega znanja in kulture ter ga razširimo daleč naokoli. Projekti 4. Izbor podatkov Pogosto lahko uporabite metapodatke, da ugotovite razumen podnabor podatkov za prenos. Tudi če želite na koncu prenesti vse podatke, je lahko koristno, da najprej določite prednost najpomembnejšim elementom, v primeru, da vas zaznajo in izboljšajo obrambo, ali ker bi morali kupiti več diskov, ali preprosto zato, ker se v vašem življenju zgodi kaj drugega, preden lahko prenesete vse. Na primer, zbirka lahko vsebuje več izdaj iste osnovne vsebine (kot je knjiga ali film), kjer je ena označena kot najboljša kakovost. Shranjevanje teh izdaj najprej bi imelo veliko smisla. Morda boste želeli na koncu shraniti vse izdaje, saj so v nekaterih primerih metapodatki lahko napačno označeni, ali pa obstajajo neznane razlike med izdajami (na primer, "najboljša izdaja" je lahko najboljša v večini pogledov, vendar slabša v drugih, kot je film z višjo ločljivostjo, vendar brez podnapisov). Prav tako lahko preiščete svojo bazo metapodatkov, da najdete zanimive stvari. Katera je največja datoteka, ki je gostovana, in zakaj je tako velika? Katera je najmanjša datoteka? Ali obstajajo zanimivi ali nepričakovani vzorci, ko gre za določene kategorije, jezike in tako naprej? Ali obstajajo podvojeni ali zelo podobni naslovi? Ali obstajajo vzorci, kdaj so bili podatki dodani, kot na primer en dan, ko je bilo dodanih veliko datotek naenkrat? Pogosto se lahko veliko naučite, če si ogledate nabor podatkov na različne načine. V našem primeru smo odstranili podvojene knjige Z-Library glede na md5 hashe v Library Genesis, s čimer smo prihranili veliko časa za prenos in prostora na disku. To je precej edinstvena situacija. V večini primerov ni celovitih baz podatkov, ki bi kazale, katere datoteke so že ustrezno ohranjene s strani drugih piratov. To je samo po sebi velika priložnost za nekoga tam zunaj. Bilo bi odlično imeti redno posodobljen pregled stvari, kot so glasba in filmi, ki so že široko razširjeni na torrent spletnih straneh, in so zato nižja prioriteta za vključitev v piratska zrcala. 6. Distribucija Imate podatke, s čimer imate v lasti prvi piratski zrcalni strežnik vašega cilja (najverjetneje). Na mnoge načine je najtežji del za vami, vendar je najnevarnejši del še pred vami. Navsezadnje ste bili do zdaj prikriti; leteli ste pod radarjem. Vse, kar ste morali storiti, je bilo uporabljati dober VPN ves čas, ne izpolnjevati svojih osebnih podatkov v nobenih obrazcih (seveda), in morda uporabljati posebno sejo brskalnika (ali celo drug računalnik). Zdaj morate razdeliti podatke. V našem primeru smo najprej želeli prispevati knjige nazaj v Library Genesis, vendar smo hitro odkrili težave pri tem (razvrščanje fikcije proti nefikciji). Zato smo se odločili za distribucijo z uporabo torrentov v slogu Library Genesis. Če imate priložnost prispevati k obstoječemu projektu, vam to lahko prihrani veliko časa. Vendar pa trenutno ni veliko dobro organiziranih piratskih zrcal. Recimo, da se odločite za distribucijo torrentov sami. Poskusite ohraniti te datoteke majhne, da jih je enostavno zrcaliti na drugih spletnih straneh. Nato boste morali sami sejati torrente, medtem ko ostajate anonimni. Uporabite lahko VPN (z ali brez posredovanja vrat) ali plačate s premešanimi Bitcoini za Seedbox. Če ne veste, kaj nekateri od teh izrazov pomenijo, boste morali veliko prebrati, saj je pomembno, da razumete tveganja tukaj. Torrent datoteke lahko gostite na obstoječih torrent spletnih straneh. V našem primeru smo se odločili dejansko gostiti spletno stran, saj smo želeli tudi jasno širiti našo filozofijo. To lahko storite sami na podoben način (uporabljamo Njalla za naše domene in gostovanje, plačano s premešanimi Bitcoini), vendar se tudi počutite svobodno, da nas kontaktirate, da gostimo vaše torrente. Želimo zgraditi celovit indeks piratskih zrcal skozi čas, če se ta ideja prime. Kar se tiče izbire VPN, je bilo o tem že veliko napisanega, zato bomo le ponovili splošni nasvet, da izberete glede na ugled. Dejanske sodno preizkušene politike brez beleženja z dolgo zgodovino zaščite zasebnosti so po našem mnenju najnižja tveganja. Upoštevajte, da tudi če naredite vse prav, nikoli ne morete doseči ničelnega tveganja. Na primer, ko sejate svoje torrente, lahko zelo motiviran akter države verjetno pogleda na dohodne in odhodne podatkovne tokove za VPN strežnike in ugotovi, kdo ste. Ali pa lahko preprosto nekako naredite napako. Verjetno smo že, in bomo spet. Na srečo, države ne skrbijo <em>toliko</em> za piratstvo. Ena odločitev, ki jo morate sprejeti za vsak projekt, je, ali ga objaviti z isto identiteto kot prej ali ne. Če še naprej uporabljate isto ime, se lahko napake v operativni varnosti iz prejšnjih projektov vrnejo, da vas ugriznejo. Toda objavljanje pod različnimi imeni pomeni, da ne gradite dolgotrajnejšega ugleda. Odločili smo se za močno operativno varnost od začetka, da lahko še naprej uporabljamo isto identiteto, vendar ne bomo oklevali objaviti pod drugim imenom, če naredimo napako ali če to zahtevajo okoliščine. Razširjanje besede je lahko težavno. Kot smo rekli, je to še vedno nišna skupnost. Prvotno smo objavili na Redditu, vendar smo resnično pridobili pozornost na Hacker News. Za zdaj je naša priporočilo, da objavite na nekaj mestih in vidite, kaj se zgodi. In spet, kontaktirajte nas. Radi bi širili besedo o več piratskih arhivskih prizadevanjih. 1. Izbira domene / filozofija Ni pomanjkanja znanja in kulturne dediščine, ki bi jo bilo treba ohraniti, kar je lahko preobremenjujoče. Zato je pogosto koristno, da si vzamete trenutek in premislite, kakšen je lahko vaš prispevek. Vsak ima drugačen način razmišljanja o tem, vendar so tukaj nekatera vprašanja, ki si jih lahko zastavite: V našem primeru smo se posebej zavzemali za dolgoročno ohranjanje znanosti. Poznali smo Library Genesis in kako je bil večkrat popolnoma zrcaljen s pomočjo torrentov. Ta ideja nam je bila všeč. Potem je nekega dne eden od nas poskušal najti nekaj znanstvenih učbenikov na Library Genesis, vendar jih ni mogel najti, kar je vzbudilo dvom o tem, kako popoln je v resnici. Nato smo te učbenike poiskali na spletu in jih našli drugje, kar je zasadilo seme za naš projekt. Še preden smo vedeli za Z-Library, smo imeli idejo, da ne poskušamo zbrati vseh teh knjig ročno, ampak se osredotočimo na zrcaljenje obstoječih zbirk in jih prispevamo nazaj v Library Genesis. Katere veščine imate, ki jih lahko izkoristite v svojo korist? Na primer, če ste strokovnjak za spletno varnost, lahko najdete načine za premagovanje blokad IP za varne cilje. Če ste odlični v organiziranju skupnosti, lahko morda zberete nekaj ljudi okoli cilja. Vendar je koristno poznati nekaj programiranja, če ne drugega, za ohranjanje dobre operativne varnosti skozi ta proces. Na katerem področju bi bilo smiselno osredotočiti se? Če boste porabili X ur za piratsko arhiviranje, kako lahko dosežete največji "učinek za svoj denar"? Kakšni so edinstveni načini, kako razmišljate o tem? Morda imate nekaj zanimivih idej ali pristopov, ki jih drugi morda niso opazili. Koliko časa imate za to? Naš nasvet bi bil, da začnete z majhnimi projekti in se lotite večjih, ko se navadite, vendar lahko postane vseobsegajoče. Zakaj vas to zanima? Kaj vas navdušuje? Če lahko zberemo skupino ljudi, ki vsi arhivirajo stvari, ki jih posebej zanimajo, bi to pokrilo veliko! O svoji strasti boste vedeli veliko več kot povprečna oseba, na primer, kateri podatki so pomembni za shranjevanje, katere so najboljše zbirke in spletne skupnosti itd. 3. Pridobivanje metadata Datum dodajanja/spremembe: da se lahko kasneje vrnete in prenesete datoteke, ki jih prej niste prenesli (čeprav lahko pogosto uporabite tudi ID ali hash za to). Hash (md5, sha1): za potrditev, da ste datoteko pravilno prenesli. ID: lahko je nek notranji ID, vendar so ID-ji, kot sta ISBN ali DOI, prav tako uporabni. Ime datoteke / lokacija Opis, kategorija, oznake, avtorji, jezik itd. Velikost: za izračun, koliko prostora na disku potrebujete. Pojdimo malo bolj tehnično. Za dejansko pridobivanje metadata s spletnih strani smo stvari ohranili precej preproste. Uporabljamo Python skripte, včasih curl, in MySQL bazo podatkov za shranjevanje rezultatov. Nismo uporabili nobene napredne programske opreme za pridobivanje, ki bi lahko mapirala kompleksne spletne strani, saj smo doslej morali pridobiti le eno ali dve vrsti strani z enostavnim naštevanjem ID-jev in razčlenjevanjem HTML-ja. Če ni enostavno naštevanih strani, boste morda potrebovali ustreznega pajka, ki poskuša najti vse strani. Preden začnete pridobivati celotno spletno stran, poskusite to narediti ročno za nekaj časa. Prebrskajte nekaj deset strani sami, da dobite občutek, kako to deluje. Včasih boste na ta način že naleteli na IP blokade ali drugo zanimivo vedenje. Enako velja za pridobivanje podatkov: preden se preveč poglobite v ta cilj, se prepričajte, da lahko dejansko učinkovito prenesete njegove podatke. Za obhod omejitev lahko poskusite nekaj stvari. Ali obstajajo drugi IP naslovi ali strežniki, ki gostijo iste podatke, vendar nimajo enakih omejitev? Ali obstajajo API končne točke, ki nimajo omejitev, medtem ko jih druge imajo? Pri kakšni hitrosti prenosa je vaš IP blokiran in za koliko časa? Ali pa niste blokirani, ampak upočasnjeni? Kaj se zgodi, če ustvarite uporabniški račun, kako se stvari spremenijo potem? Ali lahko uporabite HTTP/2 za ohranjanje odprtih povezav in ali to poveča hitrost, s katero lahko zahtevate strani? Ali obstajajo strani, ki naenkrat navajajo več datotek, in ali so tam navedene informacije zadostne? Stvari, ki jih verjetno želite shraniti, vključujejo: To običajno naredimo v dveh fazah. Najprej prenesemo surove HTML datoteke, običajno neposredno v MySQL (da se izognemo številnim majhnim datotekam, o čemer bomo govorili več spodaj). Nato v ločenem koraku pregledamo te HTML datoteke in jih razčlenimo v dejanske MySQL tabele. Na ta način vam ni treba vsega ponovno prenesti od začetka, če odkrijete napako v vaši kodi za razčlenjevanje, saj lahko preprosto ponovno obdelate HTML datoteke z novo kodo. Prav tako je pogosto lažje paralelizirati korak obdelave, s čimer prihranite nekaj časa (in lahko napišete kodo za obdelavo, medtem ko pridobivanje teče, namesto da bi morali napisati oba koraka hkrati). Na koncu, upoštevajte, da je za nekatere cilje vse, kar obstaja, le pridobivanje metapodatkov. Obstajajo ogromne zbirke metapodatkov, ki niso ustrezno ohranjene. Naslov Izbira domene / filozofija: Na katerem področju se želite osredotočiti in zakaj? Katere so vaše edinstvene strasti, veščine in okoliščine, ki jih lahko izkoristite v svojo korist? Izbira cilja: Katero specifično zbirko boste zrcalili? Pridobivanje metadata: Katalogiziranje informacij o datotekah, brez dejanskega prenosa (pogosto veliko večjih) datotek samih. Izbira podatkov: Na podlagi metadata zožitev, kateri podatki so trenutno najbolj pomembni za arhiviranje. Lahko je vse, vendar pogosto obstaja razumen način za prihranek prostora in pasovne širine. Pridobivanje podatkov: Dejanski prenos podatkov. Distribucija: Pakiranje v torrente, objava nekje, pridobivanje ljudi, da jih širijo. 5. Pridobivanje podatkov Zdaj ste pripravljeni, da dejansko prenesete podatke v velikem obsegu. Kot je bilo že omenjeno, bi morali do te točke že ročno prenesti nekaj datotek, da bi bolje razumeli vedenje in omejitve cilja. Vendar pa vas bodo še vedno čakale presenečenja, ko boste dejansko začeli prenašati veliko datotek naenkrat. Naš nasvet tukaj je predvsem, da ostanete preprosti. Začnite tako, da preprosto prenesete nekaj datotek. Uporabite lahko Python, nato pa razširite na več niti. Včasih pa je še enostavneje, da neposredno iz baze podatkov ustvarite Bash datoteke in jih nato zaženete v več terminalskih oknih, da povečate obseg. Hiter tehnični trik, ki ga je vredno omeniti, je uporaba OUTFILE v MySQL, ki ga lahko napišete kjerkoli, če onemogočite "secure_file_priv" v mysqld.cnf (in se prepričajte, da onemogočite/prekličete AppArmor, če ste na Linuxu). Podatke shranjujemo na preproste trde diske. Začnite s tem, kar imate, in počasi širite. Lahko je zastrašujoče razmišljati o shranjevanju stotin TB podatkov. Če je to situacija, s katero se soočate, najprej objavite dober podnabor in v svoji objavi prosite za pomoč pri shranjevanju preostalega. Če želite sami pridobiti več trdih diskov, ima r/DataHoarder nekaj dobrih virov za pridobivanje dobrih ponudb. Poskusite se ne obremenjevati preveč s sofisticiranimi datotečnimi sistemi. Enostavno je pasti v zajčjo luknjo pri nastavitvi stvari, kot je ZFS. Ena tehnična podrobnost, ki se je zavedajte, je, da se mnogi datotečni sistemi ne spopadajo dobro z veliko datotekami. Ugotovili smo, da je preprosta rešitev ustvariti več imenikov, npr. za različne razpone ID-jev ali predpone hashov. Po prenosu podatkov preverite celovitost datotek z uporabo hashov v metapodatkih, če so na voljo. 2. Izbira cilja Dostopna: ne uporablja veliko plasti zaščite, da bi preprečila pridobivanje njihove metadata in podatkov. Posebno znanje: imate nekaj posebnih informacij o tem cilju, kot na primer, da imate nekako poseben dostop do te zbirke, ali pa ste ugotovili, kako premagati njihove obrambe. To ni nujno (naš prihajajoči projekt ne počne nič posebnega), vendar zagotovo pomaga! Velika Torej, imamo naše področje, ki ga preučujemo, zdaj pa katero specifično zbirko naj zrcalimo? Obstaja nekaj stvari, ki so dober cilj: Ko smo našli naše učbenike za znanost na spletnih straneh, ki niso Library Genesis, smo poskušali ugotoviti, kako so prišli na internet. Nato smo našli Z-Library in ugotovili, da čeprav večina knjig ne pride najprej tja, se tam sčasoma znajdejo. Spoznali smo njegovo povezavo z Library Genesis in (finančno) spodbujevalno strukturo ter superiorni uporabniški vmesnik, ki sta oba prispevala k temu, da je bila zbirka veliko bolj popolna. Nato smo izvedli nekaj predhodnega pridobivanja metadata in podatkov ter ugotovili, da lahko obidemo njihove omejitve prenosa IP, s čimer smo izkoristili poseben dostop enega od naših članov do številnih proxy strežnikov. Ko raziskujete različne cilje, je že pomembno, da skrijete svoje sledi z uporabo VPN-jev in začasnih e-poštnih naslovov, o čemer bomo govorili več kasneje. Edinstvena: ni že dobro pokrita z drugimi projekti. Ko izvajamo projekt, ima ta več faz: To niso popolnoma neodvisne faze, pogosto pa vas spoznanja iz kasnejše faze vrnejo v prejšnjo fazo. Na primer, med pridobivanjem metadata lahko ugotovite, da ima izbrani cilj obrambne mehanizme, ki presegajo vašo raven znanja (kot so blokade IP), zato se vrnete in poiščete drug cilj. - Anna in ekipa (<a %(reddit)s>Reddit</a>) O celotnih knjigah bi lahko pisali o <em>zakaj</em> digitalnega ohranjanja na splošno in piratskega arhivizma posebej, vendar naj vam podamo kratek uvod za tiste, ki niso preveč seznanjeni. Svet proizvaja več znanja in kulture kot kdajkoli prej, vendar se tudi več tega izgublja kot kdajkoli prej. Človeštvo v veliki meri zaupa korporacijam, kot so akademski založniki, storitve pretakanja in podjetja za družbena omrežja, s to dediščino, vendar se pogosto niso izkazali za odlične skrbnike. Oglejte si dokumentarec Digitalna amnezija ali katerikoli govor Jasona Scotta. Obstajajo nekatere institucije, ki dobro arhivirajo, kolikor lahko, vendar so omejene z zakonom. Kot pirati smo v edinstvenem položaju, da arhiviramo zbirke, ki jih ne morejo dotakniti, zaradi uveljavljanja avtorskih pravic ali drugih omejitev. Prav tako lahko zrcalimo zbirke večkrat po vsem svetu, s čimer povečamo možnosti za pravilno ohranitev. Za zdaj se ne bomo spuščali v razprave o prednostih in slabostih intelektualne lastnine, morali o kršenju zakona, razmišljanjih o cenzuri ali vprašanju dostopa do znanja in kulture. Ko je vse to izven poti, se poglobimo v <em>kako</em>. Delili bomo, kako je naša ekipa postala piratski arhivisti in lekcije, ki smo se jih naučili na poti. Obstaja veliko izzivov, ko se podate na to pot, in upamo, da vam lahko pomagamo skozi nekatere izmed njih. Kako postati piratski arhivist Prvi izziv je lahko presenetljiv. Ni tehnični problem ali pravni problem. Je psihološki problem. Preden se poglobimo, dve posodobitvi o Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>): Prejeli smo nekaj izjemno velikodušnih donacij. Prva je bila 10.000 $ od anonimne osebe, ki je prav tako podpirala "bookwarriorja", prvotnega ustanovitelja Library Genesis. Posebna zahvala bookwarriorju za posredovanje te donacije. Druga je bila še ena donacija v višini 10.000 $ od anonimnega darovalca, ki se je obrnil na nas po naši zadnji izdaji in bil navdihnjen, da pomaga. Imeli smo tudi več manjših donacij. Najlepša hvala za vso vašo velikodušno podporo. Imamo nekaj vznemirljivih novih projektov v pripravi, ki jih bo to podprlo, zato ostanite z nami. Imeli smo nekaj tehničnih težav z velikostjo naše druge izdaje, vendar so naši torrenti zdaj na voljo in se delijo. Prav tako smo prejeli velikodušno ponudbo od anonimne osebe, da deli našo zbirko na njihovih zelo hitrih strežnikih, zato izvajamo poseben prenos na njihove naprave, po katerem bi morali vsi ostali, ki prenašajo zbirko, opaziti veliko izboljšanje hitrosti. Objave na blogu Živjo, sem Anna. Ustvarila sem <a %(wikipedia_annas_archive)s>Annin Arhiv</a>, največjo senčno knjižnico na svetu. To je moj osebni blog, v katerem jaz in moji sodelavci pišemo o piratstvu, digitalnem ohranjanju in še več. Povežite se z mano na <a %(reddit)s>Redditu</a>. Upoštevajte, da je ta spletna stran le blog. Tukaj gostimo le svoje besede. Tukaj niso gostene ali povezane nobene torrent datoteke ali druge datoteke, zaščitene z avtorskimi pravicami. <strong>Knjižnica</strong> - Tako kot večina knjižnic se osredotočamo predvsem na pisne materiale, kot so knjige. Morda se bomo v prihodnosti razširili na druge vrste medijev. <strong>Zrcalo</strong> - Smo strogo zrcalo obstoječih knjižnic. Osredotočamo se na ohranjanje, ne na omogočanje enostavnega iskanja in prenosa knjig (dostop) ali spodbujanje velike skupnosti ljudi, ki prispevajo nove knjige (pridobivanje). <strong>Piratsko</strong> - Namerno kršimo zakon o avtorskih pravicah v večini držav. To nam omogoča, da naredimo nekaj, česar pravne osebe ne morejo: zagotoviti, da so knjige zrcaljene daleč naokoli. <em>Ne povezujemo se na datoteke s tega bloga. Prosimo, poiščite jih sami.</em> - Anna in ekipa (<a %(reddit)s>Reddit</a>) Ta projekt (UREDI: preseljen na <a %(wikipedia_annas_archive)s>Annin arhiv</a>) si prizadeva prispevati k ohranjanju in osvoboditvi človeškega znanja. Prispevamo svoj majhen in skromen prispevek, po stopinjah velikih pred nami. Osredotočenost tega projekta je ponazorjena z njegovim imenom: Prva knjižnica, ki smo jo zrcalili, je Z-Knjižnica. To je priljubljena (in nezakonita) knjižnica. Vzeli so zbirko Library Genesis in jo naredili enostavno iskalno. Poleg tega so postali zelo učinkoviti pri pridobivanju novih prispevkov knjig, tako da spodbujajo uporabnike, ki prispevajo, z različnimi ugodnostmi. Trenutno teh novih knjig ne prispevajo nazaj v Library Genesis. In za razliko od Library Genesis, ne omogočajo enostavnega zrcaljenja svoje zbirke, kar preprečuje široko ohranjanje. To je pomembno za njihov poslovni model, saj zaračunavajo denar za dostop do svoje zbirke v večjih količinah (več kot 10 knjig na dan). Ne izrekamo moralnih sodb o zaračunavanju denarja za množični dostop do nezakonite zbirke knjig. Nedvomno je, da je Z-Library uspešno razširila dostop do znanja in pridobila več knjig. Tukaj smo preprosto zato, da opravimo svoj del: zagotovimo dolgoročno ohranitev te zasebne zbirke. Radi bi vas povabili, da pomagate ohranjati in osvobajati človeško znanje z nalaganjem in sejanjem naših torrentov. Za več informacij o tem, kako so podatki organizirani, si oglejte stran projekta. Zelo bi vas radi povabili, da prispevate svoje ideje o tem, katere zbirke naj zrcalimo naslednje in kako to storiti. Skupaj lahko dosežemo veliko. To je le majhen prispevek med neštetimi drugimi. Hvala vam, za vse, kar počnete. Predstavljamo Piratsko knjižnično zrcalo: Ohranjanje 7TB knjig (ki niso v Libgenu) 10% of človeške pisne dediščine ohranjene za vedno <strong>Google.</strong> Navsezadnje so opravili to raziskavo za Google Books. Vendar njihovi metadata niso dostopni v velikem obsegu in jih je precej težko strgati. <strong>Različni posamezni knjižnični sistemi in arhivi.</strong> Obstajajo knjižnice in arhivi, ki niso bili indeksirani in združeni z nobenim od zgoraj navedenih, pogosto zato, ker so podfinancirani ali iz drugih razlogov ne želijo deliti svojih podatkov z Open Library, OCLC, Google in tako naprej. Veliko teh ima digitalne zapise, dostopne prek interneta, in pogosto niso zelo dobro zaščiteni, zato, če želite pomagati in se zabavati pri učenju o nenavadnih knjižničnih sistemih, so to odlične izhodiščne točke. <strong>ISBNdb.</strong> To je tema te objave na blogu. ISBNdb strga različne spletne strani za knjižne metadata, zlasti podatke o cenah, ki jih nato prodajajo prodajalcem knjig, da lahko svoje knjige cenijo v skladu s preostalim trgom. Ker so ISBN-ji danes precej univerzalni, so učinkovito zgradili "spletno stran za vsako knjigo". <strong>Open Library.</strong> Kot že omenjeno, je to njihovo celotno poslanstvo. Zbrali so ogromne količine knjižničnih podatkov iz sodelujočih knjižnic in nacionalnih arhivov ter to še naprej počnejo. Imajo tudi prostovoljne knjižničarje in tehnično ekipo, ki poskušajo deduplicirati zapise in jih označiti z vsemi vrstami metadata. Najboljše od vsega je, da je njihov nabor podatkov popolnoma odprt. Preprosto ga lahko <a %(openlibrary)s>preneseš</a>. <strong>WorldCat.</strong> To je spletna stran, ki jo vodi neprofitna organizacija OCLC, ki prodaja sisteme za upravljanje knjižnic. Združujejo knjižnične metadata iz številnih knjižnic in jih omogočajo prek spletne strani WorldCat. Vendar pa tudi zaslužijo s prodajo teh podatkov, zato niso na voljo za množični prenos. Imajo pa nekaj bolj omejenih množičnih naborov podatkov, ki so na voljo za prenos, v sodelovanju z določenimi knjižnicami. 1. Za neko razumno definicijo "za vedno". ;) 2. Seveda je pisna dediščina človeštva veliko več kot le knjige, še posebej danes. Zaradi tega prispevka in naših nedavnih izdaj se osredotočamo na knjige, vendar se naši interesi raztezajo dlje. 3. O Aaronu Swartzu je mogoče povedati veliko več, vendar smo ga želeli le na kratko omeniti, saj igra ključno vlogo v tej zgodbi. Sčasoma bo več ljudi morda prvič naletelo na njegovo ime in se nato sami poglobili v to temo. <strong>Fizične kopije.</strong> Očitno to ni zelo koristno, saj so le dvojniki istega gradiva. Bilo bi super, če bi lahko ohranili vse opombe, ki jih ljudje naredijo v knjigah, kot so Fermatove slavne "črke na robovih". A žal, to bo ostalo sanje arhivista. <strong>“Izdaje”.</strong> Tukaj štejete vsako edinstveno različico knjige. Če je karkoli drugače, kot na primer drugačna naslovnica ali drugačen predgovor, šteje kot druga izdaja. <strong>Datoteke.</strong> Pri delu s senčnimi knjižnicami, kot so Library Genesis, Sci-Hub ali Z-Library, je treba upoštevati še nekaj. Lahko obstaja več skenov iste izdaje. In ljudje lahko ustvarijo boljše različice obstoječih datotek, tako da skenirajo besedilo z OCR ali popravijo strani, ki so bile skenirane pod kotom. Želimo šteti te datoteke kot eno izdajo, kar bi zahtevalo dobro metadata ali deduplikacijo z uporabo meril podobnosti dokumentov. <strong>“Dela”.</strong> Na primer "Harry Potter in Dvorana skrivnosti" kot logični koncept, ki zajema vse njegove različice, kot so različni prevodi in ponatisi. To je nekako uporabna definicija, vendar je težko določiti mejo, kaj šteje. Na primer, verjetno želimo ohraniti različne prevode, čeprav ponatisi z le manjšimi razlikami morda niso tako pomembni. - Anna in ekipa (<a %(reddit)s>Reddit</a>) S Piratskim knjižničnim zrcalom (UREDI: preseljen na <a %(wikipedia_annas_archive)s>Annin arhiv</a>) je naš cilj vzeti vse knjige na svetu in jih ohraniti za vedno.<sup>1</sup> Med našimi Z-Knjižničnimi torrenti in originalnimi Library Genesis torrenti imamo 11.783.153 datotek. Toda koliko je to v resnici? Če bi pravilno deduplicirali te datoteke, kakšen odstotek vseh knjig na svetu smo ohranili? Resnično bi si želeli imeti nekaj takega: Začnimo z nekaj grobimi številkami: V obeh Z-Library/Libgen in Open Library je veliko več knjig kot edinstvenih ISBN-jev. Ali to pomeni, da veliko teh knjig nima ISBN-jev, ali pa preprosto manjkajo metadata ISBN? Na to vprašanje lahko verjetno odgovorimo s kombinacijo avtomatiziranega ujemanja na podlagi drugih atributov (naslov, avtor, založnik itd.), vključevanjem več virov podatkov in izvlečenjem ISBN-jev iz dejanskih skenov knjig (v primeru Z-Library/Libgen). Koliko teh ISBN-jev je edinstvenih? To je najbolje ponazoriti z Vennovim diagramom: Da bi bili bolj natančni: Presenetili smo se, kako malo prekrivanja obstaja! ISBNdb ima ogromno število ISBN-jev, ki se ne pojavijo niti v Z-Library niti v Open Library, in enako velja (v manjši, a še vedno pomembni meri) za drugi dve. To odpira veliko novih vprašanj. Koliko bi avtomatizirano ujemanje pomagalo pri označevanju knjig, ki niso bile označene z ISBN-ji? Bi bilo veliko ujemanj in s tem povečano prekrivanje? Prav tako, kaj bi se zgodilo, če bi vključili 4. ali 5. nabor podatkov? Koliko prekrivanja bi videli takrat? To nam daje izhodišče. Zdaj lahko pogledamo vse ISBN-je, ki niso bili v naboru podatkov Z-Library, in ki se ne ujemajo niti z naslovom/avtorjem. To nam lahko pomaga pri ohranjanju vseh knjig na svetu: najprej z iskanjem skenov po internetu, nato pa z dejanskim skeniranjem knjig v resničnem življenju. Slednje bi lahko celo financirali množično, ali pa bi ga spodbudili z "nagradami" ljudi, ki bi želeli videti določene knjige digitalizirane. Vse to je zgodba za drugič. Če želite pomagati pri katerem koli od tega — nadaljnja analiza; iskanje več metadata; iskanje več knjig; OCR knjig; izvajanje tega za druga področja (npr. članki, avdio knjige, filmi, TV oddaje, revije) ali celo omogočanje dostopa do nekaterih teh podatkov za stvari, kot so ML / usposabljanje velikih jezikovnih modelov — me prosim kontaktirajte (<a %(reddit)s>Reddit</a>). Če vas posebej zanima analiza podatkov, delamo na tem, da bi naši nabori podatkov in skripti postali dostopni v bolj enostavni obliki. Bilo bi super, če bi lahko preprosto razdelili zvezek in začeli raziskovati. Nazadnje, če želite podpreti to delo, prosimo, razmislite o donaciji. To je popolnoma prostovoljno vodena operacija, in vaš prispevek naredi veliko razliko. Vsak prispevek pomaga. Trenutno sprejemamo donacije v kriptovalutah; glejte stran Donacije na Anninem Arhivu. Za odstotek potrebujemo imenovalec: skupno število vseh kdajkoli objavljenih knjig.<sup>2</sup> Pred propadom Google Books je inženir na projektu, Leonid Taycher, <a %(booksearch_blogspot)s>poskušal oceniti</a> to število. Prišel je — v šali — do 129.864.880 (“vsaj do nedelje”). To število je ocenil z gradnjo enotne baze podatkov vseh knjig na svetu. Za to je združil različne Datasets in jih nato združil na različne načine. Kot zanimivost, obstaja še ena oseba, ki je poskušala katalogizirati vse knjige na svetu: Aaron Swartz, pokojni digitalni aktivist in soustanovitelj Reddita.<sup>3</sup> On je <a %(youtube)s>začel Open Library</a> z namenom "ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena", združevanje podatkov iz različnih virov. Na koncu je plačal najvišjo ceno za svoje delo na področju digitalnega ohranjanja, ko je bil preganjan zaradi množičnega prenosa akademskih člankov, kar je vodilo v njegov samomor. Seveda je to eden od razlogov, zakaj je naša skupina psevdonimna in zakaj smo zelo previdni. Open Library še vedno herojsko vodijo ljudje pri Internet Archive, nadaljujejo Aaronovo zapuščino. K temu se bomo vrnili kasneje v tej objavi. V blogu na Googlu Taycher opisuje nekatere izzive pri ocenjevanju te številke. Najprej, kaj šteje kot knjiga? Obstaja nekaj možnih definicij: “Izdaje” se zdijo najbolj praktična definicija, kaj so “knjige”. Ta definicija se priročno uporablja tudi za dodeljevanje edinstvenih ISBN številk. ISBN ali mednarodna standardna knjižna številka se pogosto uporablja za mednarodno trgovino, saj je integrirana z mednarodnim sistemom črtnih kod ("Mednarodna številka artikla"). Če želite prodajati knjigo v trgovinah, potrebuje črtno kodo, zato dobite ISBN. Taycherjeva objava na blogu omenja, da čeprav so ISBN-ji koristni, niso univerzalni, saj so bili resnično sprejeti šele sredi sedemdesetih let in ne povsod po svetu. Kljub temu je ISBN verjetno najpogosteje uporabljen identifikator knjižnih izdaj, zato je to naša najboljša izhodiščna točka. Če lahko najdemo vse ISBN-je na svetu, dobimo uporaben seznam knjig, ki jih je še treba ohraniti. Torej, kje dobimo podatke? Obstaja več obstoječih prizadevanj, ki poskušajo sestaviti seznam vseh knjig na svetu: V tej objavi z veseljem napovedujemo majhno izdajo (v primerjavi z našimi prejšnjimi izdajami Z-Library). Strgali smo večino ISBNdb in podatke omogočili za prenos prek torrentov na spletni strani Pirate Library Mirror (UREDI: premaknjeno na <a %(wikipedia_annas_archive)s>Anin Arhiv</a>; tukaj ne bomo neposredno povezali, samo poiščite ga). To je približno 30,9 milijona zapisov (20 GB kot <a %(jsonlines)s>JSON Lines</a>; 4,4 GB stisnjeno). Na njihovi spletni strani trdijo, da imajo dejansko 32,6 milijona zapisov, zato smo morda nekako zgrešili nekaj, ali pa <em>oni</em> delajo nekaj narobe. V vsakem primeru za zdaj ne bomo delili, kako smo to storili — to bomo pustili kot vajo za bralca. ;-) Kar bomo delili, je nekaj predhodne analize, da bi se približali oceni števila knjig na svetu. Pregledali smo tri nabore podatkov: ta novi nabor podatkov ISBNdb, našo prvotno izdajo metadata, ki smo jih strgali iz senčne knjižnice Z-Library (ki vključuje Library Genesis), in podatkovni dump Open Library. ISBNdb izpis, ali Koliko knjig je ohranjenih za vedno? Če bi pravilno deduplicirali datoteke iz senčnih knjižnic, kakšen odstotek vseh knjig na svetu smo ohranili? Posodobitve o <a %(wikipedia_annas_archive)s>Arhivu Anne</a>, največji resnično odprti knjižnici v zgodovini človeštva. <em>Prenova WorldCat</em> Podatki <strong>Format?</strong> <a %(blog)s>Zabojniki Arhiva Anne (AAC)</a>, ki so v bistvu <a %(jsonlines)s>JSON Lines</a> stisnjeni z <a %(zstd)s>Zstandard</a>, plus nekaj standardizirane semantike. Ti zabojniki vključujejo različne vrste zapisov, glede na različne strganje, ki smo jih izvedli. Pred letom dni smo <a %(blog)s>začeli</a> odgovarjati na to vprašanje: <strong>Kakšen odstotek knjig je bil trajno ohranjen s senčnimi knjižnicami?</strong> Poglejmo nekaj osnovnih informacij o podatkih: Ko knjiga pride v senčno knjižnico z odprtimi podatki, kot je <a %(wikipedia_library_genesis)s>Library Genesis</a>, in zdaj <a %(wikipedia_annas_archive)s>Annin Arhiv</a>, se zrcali po vsem svetu (prek torrentov), s čimer se praktično ohrani za vedno. Da bi odgovorili na vprašanje, kakšen odstotek knjig je bil ohranjen, moramo vedeti imenovalec: koliko knjig obstaja v celoti? In idealno ne le številko, ampak dejanske metadata. Nato jih lahko ne le primerjamo s senčnimi knjižnicami, ampak tudi <strong>ustvarimo seznam knjig, ki jih je treba še ohraniti!</strong> Lahko bi celo začeli sanjati o množičnem prizadevanju, da bi šli po tem seznamu. Podatke smo pridobili iz <a %(wikipedia_isbndb_com)s>ISBNdb</a> in prenesli <a %(openlibrary)s>podatkovno zbirko Open Library</a>, vendar rezultati niso bili zadovoljivi. Glavni problem je bil, da ni bilo veliko prekrivanja ISBN-jev. Oglejte si ta Vennov diagram iz <a %(blog)s>naše objave na blogu</a>: Zelo nas je presenetilo, kako malo prekrivanja je bilo med ISBNdb in Open Library, ki oba obsežno vključujeta podatke iz različnih virov, kot so spletni strganji in knjižnične evidence. Če bi oba dobro opravljala svoje delo pri iskanju večine ISBN-jev, bi se njuni krogi zagotovo precej prekrivali ali pa bi bil eden podmnožica drugega. To nas je spodbudilo k razmišljanju, koliko knjig je <em>popolnoma zunaj teh krogov</em>? Potrebujemo večjo podatkovno zbirko. Takrat smo se osredotočili na največjo podatkovno zbirko knjig na svetu: <a %(wikipedia_worldcat)s>WorldCat</a>. To je lastniška podatkovna zbirka neprofitne organizacije <a %(wikipedia_oclc)s>OCLC</a>, ki zbira metapodatke iz knjižnic po vsem svetu, v zameno za to, da tem knjižnicam omogoča dostop do celotne podatkovne zbirke in da se pojavijo v rezultatih iskanja končnih uporabnikov. Čeprav je OCLC neprofitna organizacija, njihov poslovni model zahteva zaščito njihove podatkovne zbirke. No, žal nam je, prijatelji pri OCLC, mi jo bomo vseeno delili. :-) V preteklem letu smo natančno pridobili vse zapise iz WorldCat. Na začetku smo imeli srečo. WorldCat je ravno uvajal popolno prenovo svoje spletne strani (avgust 2022). To je vključevalo obsežno prenovo njihovih zalednih sistemov, kar je uvedlo številne varnostne pomanjkljivosti. Takoj smo izkoristili priložnost in v nekaj dneh pridobili stotine milijonov (!) zapisov. Po tem so bile varnostne pomanjkljivosti počasi odpravljene ena za drugo, dokler zadnja, ki smo jo našli, ni bila odpravljena pred približno mesecem dni. Do takrat smo imeli skoraj vse zapise in smo se osredotočali le na nekoliko kakovostnejše zapise. Zato smo menili, da je čas za izdajo! 1,3 milijarde WorldCat strganje <em><strong>TL;DR:</strong> Annin Arhiv je strgal celoten WorldCat (največjo zbirko knjižničnih metadata na svetu), da bi ustvaril seznam knjig, ki jih je treba ohraniti.</em> WorldCat Opozorilo: ta objava na blogu je zastarela. Odločili smo se, da IPFS še ni pripravljen za glavni čas. Še vedno bomo povezovali datoteke na IPFS iz Anninega arhiva, ko bo mogoče, vendar jih ne bomo več gostili sami, niti ne priporočamo drugim, da zrcalijo z uporabo IPFS. Prosimo, oglejte si našo stran Torrents, če želite pomagati pri ohranjanju naše zbirke. Pomagajte sejati Z-Library na IPFS Prenos iz partnerskega strežnika SciDB Zunanja izposoja Zunanja izposoja (tisk onemogočen) Zunanji prenos Raziščite metapodatke Vsebuje v torrentih Nazaj  (+%(num)s bonus) neplačljivo plačljivo preklicano potečeno čakam na Annino potrditev neveljaven Besedilo spodaj se nadaljuje v angleščini. Pojdi Ponastavi Naprej Zadnji Če vaš e-poštni naslov ne deluje na forumih Libgen, priporočamo uporabo <a %(a_mail)s>Proton Mail-a</a> (brezplačno). Lahko tudi <a %(a_manual)s>ročno zahtevate</a>, da se vaš račun aktivira. (morda zahteva <a %(a_browser)s>preverjanje brskalnika</a> — neomejeni prenosi!) Hitri partnerski strežnik #%(number)s (priporočeno) (malo hitreje, vendar s čakalnim seznamom) (preverjanje brskalnika ni potrebno) (brez preverjanja brskalnika ali čakalnih seznamov) (brez čakalnega seznama, vendar lahko zelo počasi) Počasen partnerski strežnik #%(number)s Avdioknjiga Strip Knjiga (leposlovje) Knjiga (neleposlovje) Knjiga (neznano) Članek v reviji Revija Glasbena partitura Drugo Standardni dokument Nekaterih strani ni bilo mogoče pretvoriti v PDF Označeno kot pokvarjeno v Libgen.li Ni vidno v Libgen.li Ni vidno v Libgen.rs Fiction Ni vidno v Libgen.rs Non-Fiction Zagon exiftool je na tej datoteki spodletel Označeno kot »slaba datoteka« v Z-Library Manjka v Z-knjižnici Označeno kot »spam« v Z-Library Datoteke ni mogoče odpreti (npr. okvarjena datoteka, DRM) Zahtevek glede avtorskih pravic Težave s prenosom (npr. ni povezave, sporočilo o napaki, zelo počasno delovanje) Nepravilni meta podatki (npr. naslov, opis, grafika naslovnice) Drugo Slaba kakovost (npr. težave z oblikovanjem, slaba kakovost skena, manjkajoče strani) Neželjeno / datoteka bi morala biti odstranjena (npr. oglaševanje, neprimerna vsebina) %(amount)s (%(amount_usd)s) %(amount)s skupaj %(amount)s (%(amount_usd)s) skupaj Sijajni knjižni molj Srečni knjižničar Bleščeči hranilec podatkov Čudovit arhivar Bonus prenosi Cerlalc Češki meta podatki DuXiu 读秀 EBSCOhost eBook Index Google Knjige Goodreads HathiTrust IA IA Nadzorovano digitalno posojanje ISBNdb ISBN GRP Libgen.li Izključujoč "scimag" Libgen.rs Ne-leposlovje in leposlovje Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Ruska državna knjižnica Sci-Hub Preko Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Naloženo v AA Z-Library Z-Library Chinese Naslov, avtor, DOI, ISBN, MD5, … Iskanje Avtor Opis in meta podatki Izdaja Izvirno ime datoteke Založnik (iskanje specifičnega polja) Naslov Leto izdaje Tehnične podrobnosti Ta kovanec ima višji minimum od običajnega. Izberite drugo trajanje ali drug kovanec. Zahteve ni bilo mogoče dokončati. Prosimo, poskusite znova čez nekaj minut in če se to ponovi, nam pišite na %(email)s in nam priložite posnetek zaslona. Prišlo je do neznane napake. Pišite nam na %(email)s s posnetkom zaslona. Napaka pri obdelavi plačila. Prosimo, počakajte trenutek in poskusite znova. Če težava traja več kot 24 ur, nas kontaktirajte na %(email)s s posnetkom zaslona. Izvajamo zbiranje sredstev za <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">varnostno kopiranje</a> največje senčne knjižnice za stripe na svetu. Hvala za tvojo podporo! <a href="/donate">Donirajte.</a> Če ne morete donirati, nas podprite tako, da to sporočite svojim prijateljem in nas spremljate na <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> ali <a href="https://t.me/annasarchiveorg">Telegram </a>. Ne pošiljajte nam e-pošte, da bi <a %(a_request)s>zahtevali knjige</a><br>ali majhne (<10k) <a %(a_upload)s>naložene vsebine</a>. Anin arhiv DMCA / zahtevki glede avtorskih pravic Ostanite v stiku Reddit Alternative SLUM (%(unaffiliated)s) nepovezan Annin arhiv potrebuje vašo pomoč! Če donirate zdaj, prejmete <strong>dvojno</strong> število hitrih prenosov. Mnogi nas poskušajo odstraniti, vendar se ne damo. Če donirate ta mesec, dobite <strong>dvojno</strong> število hitrih prenosov. Velja do konca tega meseca. Shranjevanje človeškega znanja: odlično darilo za praznike! Članstva bodo ustrezno podaljšana. Partnerski strežniki niso na voljo zaradi zaprtja gostovanja. Kmalu bi morali biti spet dostopni. Za povečanje odpornosti Anninega arhiva iščemo prostovoljce za zagon zrcalnih strežnikov. Na voljo imamo nov način darovanja: %(method_name)s. Razmislite o %(donate_link_open_tag)sdoniranju</a> – vodenje tega spletnega mesta ni poceni in vaša donacija resnično naredi razliko. Najlepša hvala. Priporočite prijatelja in tako vi kot vaš prijatelj prejmeta %(percentage)s%% bonus hitrih prenosov! Presenetite ljubljeno osebo, podarite ji račun s članstvom. Popolno valentinovo darilo! Preberi več o tem… Račun Dejavnost Napredno Anin Blog ↗ Annina programska oprema ↗ beta Raziskovalec kod Nabori podatkov Donirajte Prenesene datoteke Pogosta vprašanja Vstopna stran Izboljšaj meta podatke LLM podatki Prijavite se / Registrirajte se Moje donacije Javni profil Iskanje Varnost Torrenti Prevedi ↗ Prostovoljstvo & Nagrade Nedavni prenosi: 📚 Največja odprtokodna knjižnica na svetu. ⭐️ Preslikava strani Sci-Hub, Library Genesis, Z-Library in drugih. 📈 %(book_any)s knjig, %(journal_article)s člankov, %(book_comic)s stripov, %(magazine)s revij — ohranjeno za vedno.  in  in več DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Največja odprta knjižnica v zgodovini človeštva. 📈&nbsp;%(book_count)s&nbsp;knjig, %(paper_count)s&nbsp;člankov — ohranjenih za vedno. ⭐️&nbsp; Preslikujemo %(libraries)s. Preslikujemo in odpiramo %(scraped)s. Vsa programska koda in podatki so odprtokodni. OpenLib Sci-Hub ,  📚 Največja svetovna odprtokodna in odprtopodatkovna knjižnica.<br>⭐️ Preslikava Scihub, Libgen, Zlib, in druge. Z-Lib Anin arhiv Neveljavna zahteva. Obišči %(websites)s. Največja odprtokodna knjižnica odprtih podatkov na svetu. Mirrors Sci-Hub, Library Genesis, Z-Library in drugo. Preiščite Annin arhiv Anin arhiv Osvežite stran in poskusite znova. <a %(a_contact)s>Kontaktirajte nas</a> če težava traja več ur. 🔥 Težava pri nalaganju te strani <li>1. Sledite nam na <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ali <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Širite besedo o Anninem arhivu na Twitterju, Redditu, Tiktoku, Instagramu, v lokalni kavarni ali knjižnici, ali kjerkoli že ste! Ne verjamemo v omejevanje dostopa — če nas odstranijo, se bomo preprosto pojavili drugje, saj je vsa naša koda in podatki popolnoma odprtokodni.</li><li>3. Če lahko, razmislite o <a href="/donate">donaciji</a>.</li><li>4. Pomagajte <a href="https://translate.annas-software.org/">prevesti</a> našo spletno stran v različne jezike.</li><li>5. Če ste programski inženir, razmislite o prispevanju k našemu <a href="https://annas-software.org/">odprtokodnemu</a> projektu ali sejanju naših <a href="/datasets">torrentov</a>.</li> 10. Ustvarite ali pomagajte vzdrževati stran Wikipedije za Anin arhiv v svojem jeziku. 11. Objavljali bomo male, estetske oglase. Če želite oglaševati v Aninem arhivu, nam to sporočite. 6. Če ste varnostni raziskovalec, lahko vaše sposobnosti uporabimo tako za napad kot za obrambo. Oglejte si našo stran <a %(a_security)s>Varnost</a>. 7. Iščemo strokovnjake za plačila za anonimne trgovce. Ali nam lahko pomagate dodati bolj priročne načine za darovanje? PayPal, WeChat, darilne kartice. Če poznate koga, se obrnite na nas. 8. Vedno iščemo možnosti za povečanje zmogljivost strežnikov. 9. Pomagate lahko tako, da prijavite težave z datotekami, objavite komentarje in ustvarite sezname kar na tej spletni strani. Pomagate lahko tudi tako, da <a %(a_upload)s>naložite več knjig</a> ali odpravite težave z datotekami ali formatirate obstoječe knjige. Za obsežnejše informacije o tem, kako prostovoljno sodelovati, si oglejte našo stran <a %(a_volunteering)s>Prostovoljstvo in nagrade</a>. Trdno verjamemo v prost pretok informacij ter ohranjanje znanja in kulture. S tem iskalnikom gradimo na plečih velikanov. Globoko spoštujemo trdo delo ljudi, ki so ustvarili različne senčne knjižnice, in upamo, da bo ta iskalnik razširil njihov doseg. Če želite biti obveščeni o našem napredku, spremljajte Anno na <a href="https://www.reddit.com/r/Annas_Archive /">Reddit-i</a> ali <a href="https://t.me/annasarchiveorg">Telegram-u</a>. Za vprašanja in povratne informacije se obrnite na Anno na %(email)s. ID računa: %(account_id)s Odjava ❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova. ✅ Zdaj ste odjavljeni. Ponovno naložite stran, da se znova prijavite. Uporabljeni hitri prenosi (zadnjih 24 ur): <strong>%(used)s / %(total)s</strong> Članstvo: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(podaljšaj)</a> Kombinirate lahko več članstev (hitri prenosi v roku 24 ur bodo sešteti). Članstvo: <strong>Brez</strong> <a %(a_become)s>(postanite član)</a> Kontaktirajte Ano na %(email)s, če vas zanima nadgradnja članstva na višjo raven. Javni profil: %(profile_link)s Skrivni ključ (ne delite!): %(secret_key)s prikaži Pridruži se nam tukaj! Nadgradite na <a %(a_tier)s>višjo raven</a> za pridružitev naši skupini. Ekskluzivna Telegram skupina: %(link)s Račun kateri prenosi? Vpiši se Ne izgubite ključa! Neveljaven skrivni ključ. Preverite svoj ključ in poskusite znova ali pa spodaj registrirajte nov račun. Skrivni ključ Za prijavo vnesite svoj skrivni ključ: Stari e-poštni račun? Tukaj vnesite svoj <a %(a_open)s>e-poštni naslov</a>. Registrirajte nov račun Še nimate računa? Registracija uspešna! Vaš skrivni ključ je: <span %(span_key)s>%(key)s</span> Ta ključ skrbno shranite. Če ga izgubite, boste izgubili dostop do svojega računa. <li %(li_item)s><strong>Zaznamek.</strong> To stran lahko dodate med zaznamke, da pridobite svoj ključ.</li><li %(li_item)s><strong>Prenos.</strong> Kliknite <a %(a_download)s>to povezavo</a> za prenos ključa.</li><li %(li_item)s><strong>Upravitelj gesel.</strong> Za vaše udobje je ključ vnaprej izpolnjen spodaj, tako da ga lahko ob prijavi shranite v upravitelja gesel.</li> Prijavite se / Registrirajte se Preverjanje brskalnika Opozorilo: koda vsebuje nepravilne Unicode znake in se lahko obnaša nepravilno v različnih situacijah. Surovi binarni podatki se lahko dekodirajo iz base64 predstavitve v URL-ju. Opis Oznaka Predpona URL za določeno kodo Spletna stran Kode, ki se začnejo z “%(prefix_label)s” Prosimo, ne strgajte teh strani. Namesto tega priporočamo <a %(a_import)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov ter zagon naše <a %(a_software)s>odprtokodne kode</a>. Surove podatke lahko ročno raziskujete prek JSON datotek, kot je <a %(a_json_file)s>ta</a>. Manj kot %(count)s zapisov Generični URL Raziskovalec kod Kazalo Raziskujte kode, s katerimi so označeni zapisi, po predponi. Stolpec »zapisi« prikazuje število zapisov, označenih s kodami z dano predpono, kot je vidno v iskalniku (vključno z zapisi samo z meta podatki). Stolpec »kode« prikazuje, koliko dejanskih kod ima dano predpono. Znana predpona kode »%(key)s« Več… Predpona page.codes.record_starting_with page.codes.records_starting_with page.codes.records_starting_with %(count)s zapisi, ki se ujemajo z “%(prefix_label)s” kode zapisi »%%s« bo nadomeščen z vrednostjo kode Išči po Anninem arhivu Kode URL za določeno kodo: “%(url)s” Ta stran lahko traja nekaj časa za generiranje, zato zahteva Cloudflare captcha. <a %(a_donate)s>Člani</a> lahko preskočijo captcha. Zloraba prijavljena: Boljša različica Ali želite prijaviti tega uporabnika zaradi zlorabe ali neprimernega vedenja? Težava z datoteko: %(file_issue)s skriti komentar Odgovori Prijavi zlorabo Prijavili ste tega uporabnika zaradi zlorabe. Zahtevki glede avtorskih pravic v tem e-poštnem sporočilu bodo prezrti; namesto tega uporabite obrazec. Prikaži e-pošto Zelo bomo veseli vaših povratnih informacij in vprašanj! Vendar pa zaradi količine neželene in nesmiselne e-pošte, ki jo prejemamo, označite polja, da potrdite, da razumete te pogoje za stik z nami. Vsi drugi načini stika z nami v zvezi z avtorskimi pravicami bodo samodejno izbrisani. Za zahtevke glede DMCA / avtorskih pravic uporabite <a %(a_copyright)s>ta obrazec</a>. Kontaktni email URL-ji na Anninem arhivu (obvezno). En na vrstico. Prosimo, vključite samo URL-je, ki opisujejo točno isto izdajo knjige. Če želite vložiti zahtevek za več knjig ali več izdaj, prosimo, da ta obrazec oddate večkrat. Zahtevki, ki združujejo več knjig ali izdaj, bodo zavrnjeni. Naslov (obvezno) Jasen opis izvornega gradiva (obvezno) E-pošta (obvezno) URL-ji do izvornega gradiva, en na vrstico (obvezno). Prosimo, vključite čim več, da nam pomagate preveriti vaš zahtevek (npr. Amazon, WorldCat, Google Books, DOI). ISBN-ji izvornega gradiva (če je primerno). En na vrstico. Prosimo, vključite samo tiste, ki natančno ustrezajo izdaji, za katero prijavljate kršitev avtorskih pravic. Vaše ime (obvezno) ❌ Nekaj je šlo narobe. Prosimo, osvežite stran in poskusite znova. ✅ Hvala za oddajo vašega zahtevka za kršitev avtorskih pravic. Pregledali ga bomo čim prej. Prosimo, osvežite stran, da oddate novega. <a %(a_openlib)s>Open Library</a> URL-ji izvornega gradiva, en na vrstico. Prosimo, vzemite si trenutek in poiščite svoje izvorno gradivo v Open Library. To nam bo pomagalo preveriti vaš zahtevek. Telefonska številka (obvezno) Izjava in podpis (obvezno) Oddaj zahtevek Če imate zahtevek za kršitev avtorskih pravic ali DMCA, prosimo, da izpolnite ta obrazec čim bolj natančno. Če naletite na kakršne koli težave, nas kontaktirajte na našem posebnem DMCA naslovu: %(email)s. Upoštevajte, da zahtevki, poslani na ta naslov, ne bodo obdelani, namenjen je le za vprašanja. Prosimo, uporabite spodnji obrazec za oddajo svojih zahtevkov. Obrazec za prijavo kršitve avtorskih pravic / DMCA Primer zapisa na Anna’s Archive Torrenti s strani Anna’s Archive Format Anna’s Archive Containers Skripti za uvoz meta podatkov Če vas zanima zrcaljenje tega nabora podatkov za <a %(a_archival)s>arhivske</a> ali <a %(a_llm)s>LLM trening</a> namene, nas prosimo kontaktirajte. Zadnja posodobitev: %(date)s Glavna %(source)s spletna stran Dokumentacija meta podatkov (večina polj) Datoteke, zrcaljene s strani Anna’s Archive: %(count)s (%(percent)s%%) Viri Skupno število datotek: %(count)s Skupna velikost datotek: %(size)s Naš blog prispevek o teh podatkih <a %(duxiu_link)s>Duxiu</a> je ogromna baza skeniranih knjig, ki jo je ustvarila <a %(superstar_link)s>SuperStar Digital Library Group</a>. Večina so akademske knjige, skenirane z namenom, da bi jih digitalno omogočili univerzam in knjižnicam. Za našo angleško govorečo publiko imata <a %(princeton_link)s>Princeton</a> in <a %(uw_link)s>University of Washington</a> dobre preglede. Obstaja tudi odličen članek, ki ponuja več ozadja: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Knjige iz Duxiu so že dolgo piratizirane na kitajskem internetu. Običajno jih preprodajalci prodajajo za manj kot dolar. Običajno se distribuirajo z uporabo kitajskega ekvivalenta Google Drive, ki je pogosto vdrt, da omogoča več prostora za shranjevanje. Nekatere tehnične podrobnosti najdete <a %(link1)s>tukaj</a> in <a %(link2)s>tukaj</a>. Čeprav so bile knjige poljavnostno distribuirane, jih je precej težko pridobiti v večjih količinah. To smo imeli visoko na našem seznamu opravil in smo za to namenili več mesecev polnega delovnega časa. Vendar pa se je konec leta 2023 oglasil neverjeten, izjemen in nadarjen prostovoljec, ki nam je povedal, da je že opravil vse to delo — z velikimi stroški. Delil je celotno zbirko z nami, ne da bi pričakoval karkoli v zameno, razen zagotovila za dolgoročno ohranitev. Resnično izjemno. Več informacij od naših prostovoljcev (surovi zapiski): Prilagojeno iz našega <a %(a_href)s>blog prispevka</a>. DuXiu 读秀 page.datasets.file page.datasets.files page.datasets.files %(count)s datotek Ta nabor podatkov je tesno povezan z <a %(a_datasets_openlib)s>naborom podatkov Open Library</a>. Vsebuje strganje vseh meta podatkov in velik del datotek iz IA-jeve knjižnice nadzorovanega digitalnega izposojanja. Posodobitve se izdajajo v <a %(a_aac)s>formatu Anninih arhivskih vsebnikov</a>. Ti zapisi se neposredno nanašajo na nabor podatkov Open Library, vendar vsebujejo tudi zapise, ki niso v Open Library. Imamo tudi številne podatkovne datoteke, ki so jih skozi leta strgali člani skupnosti. Zbirka je sestavljena iz dveh delov. Potrebujete oba dela, da dobite vse podatke (razen nadomeščenih torrentov, ki so prečrtani na strani torrentov). Digitalna knjižnica naša prva izdaja, preden smo standardizirali na <a %(a_aac)s>format Anna’s Archive Containers (AAC)</a>. Vsebuje meta podatke (kot json in xml), pdf-je (iz digitalnih sistemov izposoje acsm in lcpdf) in sličice naslovnic. inkrementalne nove izdaje, z uporabo AAC. Vsebujejo samo meta podatke s časovnimi žigi po 2023-01-01, saj je preostalo že pokrito z “ia”. Prav tako vsi pdf-ji, tokrat iz sistemov izposoje acsm in “bookreader” (IA-jev spletni bralnik). Kljub temu, da ime ni povsem pravilno, še vedno vključujemo datoteke bookreader v zbirko ia2_acsmpdf_files, saj so medsebojno izključujoče. IA Nadzorovano digitalno posojanje 98%%+ datotek je mogoče iskati. Naše poslanstvo je arhivirati vse knjige na svetu (kot tudi članke, revije itd.) in jih narediti široko dostopne. Verjamemo, da bi morale biti vse knjige zrcaljene široko in daleč, da se zagotovi redundanca in odpornost. Zato zbiramo datoteke iz različnih virov. Nekateri viri so popolnoma odprti in jih je mogoče zrcaliti v velikem obsegu (kot je Sci-Hub). Drugi so zaprti in zaščitniški, zato jih poskušamo strgati, da »osvobodimo« njihove knjige. Spet drugi so nekje vmes. Vse naše podatke je mogoče <a %(a_torrents)s>torrenti</a>, in vse naše meta podatke je mogoče <a %(a_anna_software)s>generirati</a> ali <a %(a_elasticsearch)s>prenesti</a> kot ElasticSearch in MariaDB baze podatkov. Surove podatke je mogoče ročno raziskovati prek JSON datotek, kot je <a %(a_dbrecord)s>ta</a>. Meta podatki Spletna stran ISBN Zadnja posodobitev: %(isbn_country_date)s (%(link)s) Viri Mednarodna agencija za ISBN redno objavlja razpone, ki jih je dodelila nacionalnim agencijam za ISBN. Iz tega lahko ugotovimo, kateri državi, regiji ali jezikovni skupini pripada ta ISBN. Trenutno te podatke uporabljamo posredno, preko <a %(a_isbnlib)s>isbnlib</a> Python knjižnice. Informacije o državi ISBN To je zbirka številnih klicev na isbndb.com med septembrom 2022. Poskušali smo zajeti vse razpone ISBN. Gre za približno 30,9 milijona zapisov. Na njihovi spletni strani trdijo, da imajo dejansko 32,6 milijona zapisov, tako da smo morda kakšnega izpustili, ali pa <em>oni</em> delajo nekaj narobe. JSON odgovori so skoraj surovi iz njihovega strežnika. Ena od težav s kakovostjo podatkov, ki smo jo opazili, je, da za številke ISBN-13, ki se začnejo z drugačnim predpono kot “978-”, še vedno vključujejo polje “isbn”, ki je preprosto številka ISBN-13 s prvimi 3 številkami odrezanimi (in kontrolna številka ponovno izračunana). To je očitno napačno, vendar tako to počnejo, zato tega nismo spreminjali. Druga možna težava, na katero lahko naletite, je dejstvo, da polje “isbn13” vsebuje podvojene vrednosti, zato ga ne morete uporabiti kot primarni ključ v bazi podatkov. Kombinacija polj “isbn13”+“isbn” pa se zdi edinstvena. Izdaja 1 (2022-10-31) Leposlovni torrenti so zaostali (čeprav ID-ji ~4-6M niso torrentirani, ker se prekrivajo z našimi Zlib torrenti). Naša objava na blogu o izdaji stripov Stripovski torrenti na Anninem Arhivu Za ozadje različnih razcepov Library Genesis, glejte stran za <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li vsebuje večino iste vsebine in meta podatkov kot Libgen.rs, vendar ima nekaj dodatnih zbirk, in sicer stripe, revije in standardne dokumente. Prav tako je integriral <a %(a_scihub)s>Sci-Hub</a> v svoje meta podatke in iskalnik, kar uporabljamo za našo bazo podatkov. Meta podatki za to knjižnico so prosto dostopni <a %(a_libgen_li)s>na libgen.li</a>. Vendar je ta strežnik počasen in ne podpira nadaljevanja prekinjenih povezav. Iste datoteke so na voljo tudi na <a %(a_ftp)s>FTP strežniku</a>, ki deluje bolje. Zdi se, da se je tudi neleposlovje razširilo, vendar brez novih tokov. Zdi se, da se je to zgodilo od začetka leta 2022, čeprav tega nismo preverili. Po besedah administratorja Libgen.li naj bi zbirka "fiction_rus" (rusko leposlovje) bila pokrita z redno izdanimi torrenti iz <a %(a_booktracker)s>booktracker.org</a>, še posebej torrenti <a %(a_flibusta)s>flibusta</a> in <a %(a_librusec)s>lib.rus.ec</a> (ki jih zrcalimo <a %(a_torrents)s>tukaj</a>, čeprav še nismo ugotovili, kateri torrenti ustrezajo katerim datotekam). Zbirka leposlovja ima svoje torrente (ločeno od <a %(a_href)s>Libgen.rs</a>) od %(start)s naprej. Določeni razponi brez torrentov (kot so razponi leposlovja f_3463000 do f_4260000) so verjetno datoteke Z-Library (ali druge podvojene) datoteke, čeprav bi morda želeli narediti nekaj deduplikacije in ustvariti torrente za lgli-edinstvene datoteke v teh razponih. Statistike za vse zbirke so na voljo <a %(a_href)s>na spletni strani libgen</a>. Torrenti so na voljo za večino dodatne vsebine, še posebej torrenti za stripe, revije in standardne dokumente so bili izdani v sodelovanju z Anninim arhivom. Upoštevajte, da so torrent datoteke, ki se nanašajo na “libgen.is”, izrecno zrcalne kopije <a %(a_libgen)s>Libgen.rs</a> (“.is” je druga domena, ki jo uporablja Libgen.rs). Koristen vir za uporabo meta podatkov je <a %(a_href)s>ta stran</a>. %(icon)s Njihova zbirka "fiction_rus" (rusko leposlovje) nima posebnih torrentov, vendar je pokrita s torrenti drugih, mi pa ohranjamo <a %(fiction_rus)s>zrcalo</a>. Ruski leposlovni torrenti na Anninem arhivu Leposlovni torrenti na Anninem Arhivu Forum za razprave Meta podatki Meta podatki preko FTP Revijalni torrenti na Anninem Arhivu Informacije o poljih meta podatkov Zrcalna kopija drugih torrentov (in edinstveni leposlovni in stripovski torrenti) Standardni dokumentni torrenti na Anninem arhivu Libgen.li Torrenti po Anninem arhivu (naslovnice knjig) Library Genesis je znan po tem, da že velikodušno omogoča dostop do svojih podatkov v velikih količinah prek torrentov. Naša zbirka Libgen vsebuje pomožne podatke, ki jih oni ne objavljajo neposredno, v sodelovanju z njimi. Velika zahvala vsem, ki sodelujejo z Library Genesis za sodelovanje z nami! Naš blog o izdaji naslovnic knjig Ta stran je o različici “.rs”. Znana je po doslednem objavljanju tako svojih meta podatkov kot celotne vsebine svojega kataloga knjig. Njena zbirka knjig je razdeljena na leposlovni in neleposlovni del. Koristen vir za uporabo meta podatkov je <a %(a_metadata)s>ta stran</a> (blokira IP razpone, morda bo potreben VPN). Od marca 2024 se novi torrenti objavljajo v <a %(a_href)s>tej forum temi</a> (blokira IP razpone, morda bo potreben VPN). Torrent za fikcijo na Anninem arhivu Libgen.rs Torrenti za fikcijo Libgen.rs Diskusijski forum Libgen.rs Meta podatki Informacije o meta podatkih Libgen.rs Libgen.rs Torrenti za nefikcijo Torrent za nefikcijo na Anninem arhivu %(example)s za fikcijsko knjigo. Ta <a %(blog_post)s>prva izdaja</a> je precej majhna: približno 300 GB naslovnic knjig iz vilice Libgen.rs, tako fikcijo kot nefikcijo. Organizirane so na enak način, kot se pojavljajo na libgen.rs, npr.: %(example)s za nefikcijsko knjigo. Tako kot pri zbirki Z-Library, smo jih vse postavili v veliko .tar datoteko, ki jo lahko namestite z uporabo <a %(a_ratarmount)s>ratarmount</a>, če želite datoteke neposredno servirati. Izdaja 1 (%(date)s) Kratka zgodba o različnih različicah Library Genesis (ali “Libgen”) je, da so se skozi čas različni ljudje, vključeni v Library Genesis, sprli in šli vsak svojo pot. Po tem <a %(a_mhut)s>forumskem prispevku</a> je bil Libgen.li prvotno gostovan na “http://free-books.dontexist.com”. Različico “.fun” je ustvaril prvotni ustanovitelj. Prenavlja se v korist nove, bolj razpršene različice. <a %(a_li)s>Različica “.li”</a> ima ogromno zbirko stripov, pa tudi druge vsebine, ki (še) niso na voljo za množični prenos preko torrentov. Ima ločeno zbirko torrentov za leposlovne knjige in vsebuje meta podatke <a %(a_scihub)s>Sci-Hub</a> v svoji bazi podatkov. Različica “.rs” ima zelo podobne podatke in najpogosteje izdaja svojo zbirko v obliki torrentov. Približno je razdeljena na “leposlovni” in “neleposlovni” del. Prvotno na “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> je v nekem smislu tudi različica Library Genesis, čeprav so za svoj projekt uporabili drugo ime. Libgen.rs Našo zbirko obogatimo tudi z viri samo z meta podatki, ki jih lahko povežemo z datotekami, npr. z uporabo ISBN številk ali drugih polj. Spodaj je pregled teh virov. Spet, nekateri od teh virov so popolnoma odprti, medtem ko jih pri drugih moramo strgati. Upoštevajte, da pri iskanju meta podatkov prikazujemo izvirne zapise. Ne združujemo zapisov. Viri samo z meta podatki Open Library je odprtokodni projekt Internet Archive za katalogizacijo vsake knjige na svetu. Ima eno največjih operacij skeniranja knjig na svetu in ima veliko knjig na voljo za digitalno izposojo. Njegov katalog meta podatkov knjig je prosto dostopen za prenos in je vključen v Anninem arhivu (čeprav trenutno ni v iskanju, razen če izrecno iščete Open Library ID). Open Library Brez podvojenih Zadnja posodobitev Odstotki števila datotek %% zrcaljeno s strani AA / na voljo torrenti Velikost Vir Spodaj je hiter pregled virov datotek na Anna’s Archive. Ker senčne knjižnice pogosto sinhronizirajo podatke med seboj, je med knjižnicami precejšnje prekrivanje. Zato se številke ne ujemajo s skupnim številom. Odstotek »zrcaljeno in posejano s strani Anninega arhiva« prikazuje, koliko datotek zrcalimo sami. Te datoteke množično posejemo prek torrentov in jih omogočimo za neposreden prenos prek partnerskih spletnih strani. Pregled Skupaj Torrenti na Anninem arhivu Za več informacij o Sci-Hub obiščite njegovo <a %(a_scihub)s>uradno spletno stran</a>, <a %(a_wikipedia)s>stran na Wikipediji</a> in ta <a %(a_radiolab)s>podcast intervju</a>. Upoštevajte, da je Sci-Hub <a %(a_reddit)s>zamrznjen od leta 2021</a>. Bil je zamrznjen že prej, vendar je bilo leta 2021 dodanih nekaj milijonov člankov. Kljub temu se v zbirke “scimag” na Libgen še vedno dodaja omejeno število člankov, vendar ne dovolj, da bi upravičili nove obsežne torrente. Uporabljamo meta podatke Sci-Hub, ki jih zagotavlja <a %(a_libgen_li)s>Libgen.li</a> v svoji zbirki “scimag”. Uporabljamo tudi podatkovno zbirko <a %(a_dois)s>dois-2022-02-12.7z</a>. Upoštevajte, da so “smarch” torrenti <a %(a_smarch)s>zastareli</a> in zato niso vključeni na našem seznamu torrentov. Torrenti na Libgen.li Torrenti na Libgen.rs Meta podatki in torrenti Posodobitve na Redditu Podcast intervju Stran na Wikipediji Sci-Hub Sci-Hub: zamrznjeno od leta 2021; večina na voljo preko torrentov Libgen.li: manjši dodatki od takrat</div> Nekatere izvorne knjižnice spodbujajo množično deljenje svojih podatkov prek torrentov, medtem ko druge svoje zbirke ne delijo zlahka. V slednjem primeru Anna’s Archive poskuša strgati njihove zbirke in jih narediti dostopne (glejte našo stran <a %(a_torrents)s>Torrenti</a>). Obstajajo tudi vmesne situacije, na primer, ko so izvorne knjižnice pripravljene deliti, vendar nimajo sredstev za to. V teh primerih tudi poskušamo pomagati. Spodaj je pregled, kako sodelujemo z različnimi izvornimi knjižnicami. Izvorne knjižnice %(icon)s Različne baze podatkov razpršene po kitajskem internetu; pogosto plačljive baze podatkov %(icon)s Večina datotek je dostopna le z uporabo premium računov BaiduYun; počasne hitrosti prenosa. %(icon)s Annin arhiv upravlja zbirko <a %(duxiu)s>DuXiu datotek</a> %(icon)s Različne baze meta podatkov so razpršene po kitajskem internetu; pogosto so to plačljive baze %(icon)s Ni na voljo enostavno dostopnih metapodatkov za celotno zbirko. %(icon)s Annin arhiv upravlja zbirko <a %(duxiu)s>DuXiu metapodatkov</a> Datoteke %(icon)s Datoteke so na voljo za izposojo le v omejenem obsegu, z različnimi omejitvami dostopa %(icon)s Annin arhiv upravlja zbirko <a %(ia)s>IA datotek</a> %(icon)s Nekateri meta podatki so na voljo prek <a %(openlib)s>Open Library podatkovnih baz</a>, vendar ne pokrivajo celotne zbirke IA %(icon)s Ni enostavno dostopnih meta podatkov za celotno zbirko %(icon)s Annin arhiv upravlja zbirko <a %(ia)s>IA meta podatkov</a> Zadnja posodobitev %(icon)s Annin arhiv in Libgen.li skupaj upravljata zbirke <a %(comics)s>stripov</a>, <a %(magazines)s>revij</a>, <a %(standarts)s>standardnih dokumentov</a> in <a %(fiction)s>leposlovja (ločeno od Libgen.rs)</a>. %(icon)s Nefikcijski torrenti so deljeni z Libgen.rs (in zrcaljeni <a %(libgenli)s>tukaj</a>). %(icon)s Četrtletni <a %(dbdumps)s>HTTP podatkovni prenosi</a> %(icon)s Avtomatizirani torrenti za <a %(nonfiction)s>neleposlovje</a> in <a %(fiction)s>leposlovje</a> %(icon)s Annin arhiv upravlja zbirko <a %(covers)s>torrentov knjižnih ovitkov</a> %(icon)s Dnevni <a %(dbdumps)s>HTTP podatkovni prenosi</a> Meta podatki %(icon)s Mesečni <a %(dbdumps)s>izvozi baze podatkov</a> %(icon)s Podatkovni torrenti so na voljo <a %(scihub1)s>tukaj</a>, <a %(scihub2)s>tukaj</a> in <a %(libgenli)s>tukaj</a> %(icon)s Nekatere nove datoteke se <a %(libgenrs)s>dodajajo</a> v Libgenov “scimag”, vendar ne dovolj, da bi upravičile nove torrente %(icon)s Sci-Hub je zamrznil nove datoteke od leta 2021. %(icon)s Meta podatkovni prenosi so na voljo <a %(scihub1)s>tukaj</a> in <a %(scihub2)s>tukaj</a>, kot tudi kot del <a %(libgenli)s>Libgen.li baze podatkov</a> (ki jo uporabljamo) Vir %(icon)s Različni manjši ali enkratni viri. Spodbujamo ljudi, da najprej naložijo v druge senčne knjižnice, vendar včasih ljudje imajo zbirke, ki so prevelike, da bi jih drugi lahko pregledali, vendar ne dovolj velike, da bi si zaslužile svojo kategorijo. %(icon)s Ni na voljo neposredno v velikih količinah, zaščiteno pred strganjem %(icon)s Annin arhiv upravlja zbirko <a %(worldcat)s>OCLC (WorldCat) metapodatkov</a> %(icon)s Annin arhiv in Z-Library skupaj upravljata zbirko <a %(metadata)s>meta podatkov Z-Library</a> in <a %(files)s>datotek Z-Library</a> Datasets Vse zgoraj navedene vire združimo v eno enotno bazo podatkov, ki jo uporabljamo za to spletno stran. Ta enotna baza podatkov ni neposredno dostopna, vendar ker je Anna’s Archive popolnoma odprtokodna, jo je mogoče precej enostavno <a %(a_generated)s>ustvariti</a> ali <a %(a_downloaded)s>prenesti</a> kot ElasticSearch in MariaDB baze podatkov. Skripti na tej strani bodo samodejno prenesli vse potrebne meta podatke iz zgoraj omenjenih virov. Če želite raziskati naše podatke, preden te skripte zaženete lokalno, si lahko ogledate naše JSON datoteke, ki se povezujejo z drugimi JSON datotekami. <a %(a_json)s>Ta datoteka</a> je dober začetek. Enotna baza podatkov Torrenti pri Anninem arhivu brskaj išči Različni manjši ali enkratni viri. Spodbujamo ljudi, da najprej naložijo v druge senčne knjižnice, vendar včasih ljudje imajo zbirke, ki so prevelike, da bi jih drugi lahko pregledali, vendar ne dovolj velike, da bi si zaslužile svojo kategorijo. Pregled s <a %(a1)s>strani datasets</a>. Iz <a %(a_href)s>aaaaarg.fail</a>. Zdi se, da je precej popoln. Od našega prostovoljca “cgiym”. Iz <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrenta. Ima precej visoko prekrivanje z obstoječimi zbirkami člankov, vendar zelo malo ujemanj MD5, zato smo se odločili, da ga obdržimo v celoti. Zajem podatkov iz <q>iRead eBooks</q> (= fonetično <q>ai rit i-books</q>; airitibooks.com), s strani prostovoljca <q>j</q>. Ustreza <q>airitibooks</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>. Iz zbirke <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delno iz izvirnega vira, delno iz the-eye.eu, delno iz drugih zrcal. Iz zasebne torrent strani za knjige, <a %(a_href)s>Bibliotik</a> (pogosto imenovan kot “Bib”), kjer so bile knjige združene v torrente po imenu (A.torrent, B.torrent) in distribuirane preko the-eye.eu. Od našega prostovoljca “bpb9v”. Za več informacij o <a %(a_href)s>CADAL</a> glejte opombe na naši <a %(a_duxiu)s>DuXiu dataset strani</a>. Več od našega prostovoljca “bpb9v”, večinoma DuXiu datoteke, kot tudi mapa “WenQu” in “SuperStar_Journals” (SuperStar je podjetje za DuXiu). Od našega prostovoljca “cgiym”, kitajski teksti iz različnih virov (predstavljeni kot podimeniki), vključno z <a %(a_href)s>China Machine Press</a> (velik kitajski založnik). Ne-kitajske zbirke (predstavljene kot podimeniki) od našega prostovoljca “cgiym”. Zajem podatkov o knjigah o kitajski arhitekturi, s strani prostovoljca <q>cm</q>: <q>Pridobil sem jih z izkoriščanjem ranljivosti omrežja v založbi, vendar je bila ta luknja že zaprta</q>. Ustreza <q>chinese_architecture</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>. Knjige iz akademske založbe <a %(a_href)s>De Gruyter</a>, zbrane iz nekaj velikih torrentov. Scrape iz <a %(a_href)s>docer.pl</a>, poljske spletne strani za deljenje datotek, osredotočene na knjige in druga pisna dela. Scrape izveden konec leta 2023 s strani prostovoljca “p”. Nimamo dobrih meta podatkov iz izvorne spletne strani (niti končnic datotek), vendar smo filtrirali datoteke, ki so bile podobne knjigam, in pogosto uspeli izvleči meta podatke iz samih datotek. DuXiu epubi, neposredno iz DuXiu, zbrani s strani prostovoljca “w”. Samo nedavne DuXiu knjige so na voljo neposredno preko e-knjig, zato morajo biti večinoma nedavne. Preostale DuXiu datoteke od prostovoljca “m”, ki niso bile v DuXiu lastniškem PDG formatu (glavni <a %(a_href)s>DuXiu dataset</a>). Zbrane iz mnogih izvornih virov, žal brez ohranjanja teh virov v poti datoteke. <span></span> <span></span> <span></span> Zajem podatkov o erotičnih knjigah, s strani prostovoljca <q>do no harm</q>. Ustreza <q>hentai</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>. <span></span> <span></span> Zbirka, zbrana iz japonskega Manga založnika s strani prostovoljca “t”. <a %(a_href)s>Izbrani sodni arhivi Longquan</a>, zagotovljeni s strani prostovoljca “c”. Scrape iz <a %(a_href)s>magzdb.org</a>, zaveznik Library Genesis (povezan je na libgen.rs domači strani), vendar niso želeli neposredno zagotoviti svojih datotek. Pridobljeno s strani prostovoljca “p” konec leta 2023. <span></span> Različni majhni prenosi, premajhni za svojo lastno podzbirko, vendar predstavljeni kot imeniki. E-knjige iz AvaxHome, ruske spletne strani za deljenje datotek. Arhiv časopisov in revij. Ustreza <q>newsarch_magz</q> metadata v <a %(a1)s><q>Drugi zajemi metadata</q></a>. Zajem podatkov iz <a %(a1)s>Philosophy Documentation Center</a>. Zbirka prostovoljca “o”, ki je zbral poljske knjige neposredno iz izvornih “scene” spletnih strani. Združene zbirke <a %(a_href)s>shuge.org</a> s strani prostovoljcev “cgiym” in “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (imenovana po izmišljeni knjižnici), zbrana leta 2022 s strani prostovoljca “t”. <span></span> <span></span> <span></span> Pod-pod-zbirke (predstavljene kot imeniki) od prostovoljca “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (od <a %(a_sikuquanshu)s>Dizhi(迪志)</a> v Tajvanu), mebook (mebook.cc, 我的小书屋, moja mala knjižnica — woz9ts: “Ta stran se osredotoča predvsem na deljenje visokokakovostnih e-knjig, nekatere od njih so bile postavljene s strani lastnika samega. Lastnik je bil <a %(a_arrested)s>aretiran</a> leta 2019 in nekdo je naredil zbirko datotek, ki jih je delil.”). Preostale datoteke DuXiu od prostovoljca »woz9ts«, ki niso bile v lastniškem formatu PDG DuXiu (še vedno jih je treba pretvoriti v PDF). Zbirka "naloži" je razdeljena na manjše podzbirke, ki so označene v AACID-jih in imenih torrentov. Vse podzbirke so bile najprej deduplicirane proti glavni zbirki, vendar metapodatkovne datoteke "upload_records" JSON še vedno vsebujejo veliko referenc na izvirne datoteke. Ne-knjižne datoteke so bile prav tako odstranjene iz večine podzbir, in običajno <em>niso</em> navedene v "upload_records" JSON. Podzbirke so: Opombe Podzbirka Mnoge podzbirke same po sebi sestavljajo pod-podzbirke (npr. iz različnih izvirnih virov), ki so predstavljene kot imeniki v poljih "filepath". Nalaganja v Annin arhiv Naša objava na blogu o teh podatkih <a %(a_worldcat)s>WorldCat</a> je lastniška baza podatkov neprofitne organizacije <a %(a_oclc)s>OCLC</a>, ki združuje meta podatke iz knjižnic po vsem svetu. Verjetno je to največja zbirka knjižničnih meta podatkov na svetu. Oktobra 2023 smo <a %(a_scrape)s>objavili</a> obsežno strganje baze podatkov OCLC (WorldCat) v <a %(a_aac)s>formatu Anninih arhivskih vsebnikov</a>. Oktober 2023, začetna izdaja: OCLC (WorldCat) Torrenti na Anninem arhivu Primer zapisa pri Anninem arhivu (izvirna zbirka) Primer zapisa pri Anninem arhivu (zbirka »zlib3«) Torrenti pri Anninem arhivu (meta podatki + vsebina) Objava na blogu o Izdaji 1 Objava na blogu o Izdaji 2 Konec leta 2022 so bili domnevni ustanovitelji Z-Library aretirani, domene pa so zasegle oblasti Združenih držav. Od takrat se spletna stran počasi vrača na splet. Ni znano, kdo jo trenutno vodi. Posodobitev od februarja 2023. Z-Library ima svoje korenine v skupnosti <a %(a_href)s>Library Genesis</a> in je prvotno začel z njihovimi podatki. Od takrat se je precej profesionaliziral in ima veliko bolj sodoben vmesnik. Zato lahko prejmejo veliko več donacij, tako denarnih za nadaljnje izboljšanje njihove spletne strani kot tudi donacij novih knjig. Poleg Library Genesis so zbrali veliko zbirko. Zbirka je sestavljena iz treh delov. Spodaj so ohranjene izvirne opisne strani za prva dva dela. Potrebujete vse tri dele, da dobite vse podatke (razen nadomeščenih torrentov, ki so prečrtani na strani torrentov). %(title)s: naša prva izdaja. To je bila prva izdaja tistega, kar je bilo takrat imenovano »Pirate Library Mirror« (»pilimi«). %(title)s: druga izdaja, tokrat z vsemi datotekami zavitimi v .tar datoteke. %(title)s: inkrementalne nove izdaje, z uporabo <a %(a_href)s>formatov Anninih arhivskih vsebnikov (AAC)</a>, zdaj izdane v sodelovanju z ekipo Z-Library. Začetno zrcalo je bilo skrbno pridobljeno v letih 2021 in 2022. Trenutno je nekoliko zastarelo: odraža stanje zbirke junija 2021. To bomo posodobili v prihodnosti. Trenutno se osredotočamo na izdajo te prve izdaje. Ker je Library Genesis že ohranjen z javnimi torrenti in je vključen v Z-Library, smo junija 2022 izvedli osnovno deduplikacijo proti Library Genesis. Za to smo uporabili MD5 hashe. Verjetno je v knjižnici še veliko podvojene vsebine, kot so več formatov datotek iste knjige. To je težko natančno zaznati, zato tega ne počnemo. Po deduplikaciji nam ostane več kot 2 milijona datotek, skupaj nekaj manj kot 7TB. Zbirka je sestavljena iz dveh delov: MySQL “.sql.gz” dumpa meta podatkov in 72 torrent datotek, velikih približno 50-100GB vsaka. Meta podatki vsebujejo podatke, kot jih poroča spletna stran Z-Library (naslov, avtor, opis, vrsta datoteke), pa tudi dejansko velikost datoteke in md5sum, ki smo ju opazili, saj se včasih ti podatki ne ujemajo. Zdi se, da obstajajo obsegi datotek, za katere ima Z-Library napačne meta podatke. V nekaterih izoliranih primerih smo morda tudi napačno prenesli datoteke, kar bomo poskušali zaznati in popraviti v prihodnosti. Velike torrent datoteke vsebujejo dejanske podatke o knjigah, z ID-jem Z-Library kot imenom datoteke. Končnice datotek je mogoče rekonstruirati z uporabo dumpa meta podatkov. Zbirka je mešanica nefikcijske in fikcijske vsebine (ni ločena kot v Library Genesis). Kakovost je tudi zelo različna. Ta prva izdaja je zdaj popolnoma na voljo. Upoštevajte, da so torrent datoteke na voljo samo prek našega Tor zrcala. Izdaja 1 (%(date)s) To je ena dodatna torrent datoteka. Ne vsebuje nobenih novih informacij, vendar ima nekaj podatkov, ki lahko trajajo nekaj časa za izračun. To je priročno imeti, saj je prenos tega torrenta pogosto hitrejši kot izračunavanje od začetka. Zlasti vsebuje SQLite indekse za tar datoteke, za uporabo z <a %(a_href)s>ratarmount</a>. Dodatek k izdaji 2 (%(date)s) Dobili smo vse knjige, ki so bile dodane v Z-Library med našim zadnjim zrcalom in avgustom 2022. Prav tako smo se vrnili in zajeli nekaj knjig, ki smo jih prvič zgrešili. Vse skupaj je ta nova zbirka velika približno 24TB. Tudi to zbirko smo deduplicirali proti Library Genesis, saj so za to zbirko že na voljo torrenti. Podatki so organizirani podobno kot pri prvi izdaji. Obstaja MySQL “.sql.gz” dump meta podatkov, ki vključuje tudi vse meta podatke iz prve izdaje, s čimer jo nadomešča. Dodali smo tudi nekaj novih stolpcev: To smo omenili že zadnjič, vendar za pojasnilo: “filename” in “md5” sta dejanski lastnosti datoteke, medtem ko sta “filename_reported” in “md5_reported” tisto, kar smo zajeli iz Z-Library. Včasih se ti dve ne ujemata, zato smo vključili obe. Za to izdajo smo spremenili kolacijo v “utf8mb4_unicode_ci”, ki bi morala biti združljiva s starejšimi različicami MySQL. Podatkovne datoteke so podobne kot zadnjič, čeprav so veliko večje. Preprosto se nam ni dalo ustvarjati veliko manjših torrent datotek. “pilimi-zlib2-0-14679999-extra.torrent” vsebuje vse datoteke, ki smo jih zgrešili v zadnji izdaji, medtem ko so drugi torrenti vsi novi ID obsegi.  <strong>Posodobitev %(date)s:</strong> Večino naših torrentov smo naredili prevelike, kar je povzročilo težave torrent odjemalcem. Odstranili smo jih in izdali nove torrente. <strong>Posodobitev %(date)s:</strong> Še vedno je bilo preveč datotek, zato smo jih zavili v tar datoteke in ponovno izdali nove torrente. %(key)s: ali je ta datoteka že v Library Genesis, bodisi v nefikcijski ali fikcijski zbirki (ujemanje po md5). %(key)s: v katerem torrentu je ta datoteka. %(key)s: nastavljeno, ko nismo mogli prenesti knjige. Izdaja 2 (%(date)s) Zlib izdaje (izvirne opisne strani) Tor domena Glavna spletna stran Z-Library strganje Zbirka “kitajskih” knjig v Z-Library se zdi enaka naši zbirki DuXiu, vendar z različnimi MD5-ji. Te datoteke izključujemo iz torrentov, da se izognemo podvajanju, vendar jih še vedno prikazujemo v našem iskalnem indeksu. Meta podatki Dobili ste %(percentage)s%% bonusa hitrih prenosov, ker vas je priporočil uporabnik %(profile_link)s. To velja za celotno obdobje članstva. Doniraj Pridruži se Izbrano do %(percentage)s%% popustov Alipay podpira mednarodne kreditne/debetne kartice. Za več informacij si oglejte <a %(a_alipay)s>ta vodnik</a>. Pošljite nam darilne kartice Amazon.com z uporabo vaše kreditne/debetne kartice. Lahko kupite kripto z uporabo kreditnih/debetnih kartic. WeChat (Weixin Pay) podpira mednarodne kreditne/debetne kartice. V aplikaciji WeChat pojdite na “Me => Services => Wallet => Add a Card”. Če tega ne vidite, omogočite z “Me => Settings => General => Tools => Weixin Pay => Enable”. (uporabite pri pošiljanju Ethereuma iz Coinbase) kopirano! kopiraj (najnižji minimalni znesek) (opozorilo: visok minimalni znesek) -%(percentage)s%% 12 mesecev 1 mesec 24 mesecev 3 mesece 48 mesecev 6 mesecev 96 mesecev Izberite, za koliko časa se želite naročiti. <div %(div_monthly_cost)s></div><div %(div_after)s>po <span %(span_discount)s></span> popustih</div><div %(div_total)s></div> <div %(div_duration)s></div> %(percentage)s%% za 12 mesecev za 1 mesec za 24 mesecev za 3 mesece za 48 mesecev za 6 mesecev za 96 mesecev %(monthly_cost)s/mesec kontaktirajte nas Neposredni <strong>SFTP</strong> strežniki Donacija na ravni podjetja ali zamenjava za nove zbirke (npr. novi skeni, OCR-ani nabori podatkov). Strokovni dostop <strong>Neomejen</strong> hitri dostop <div %(div_question)s>Ali lahko nadgradim svoje članstvo ali pridobim več članstev?</div> <div %(div_question)s>Ali lahko doniram brez članstva?</div> Seveda. Sprejemamo donacije v poljubnem znesku na ta Monero (XMR) naslov: %(address)s. <div %(div_question)s>Kaj pomenijo razponi na mesec?</div> Do spodnje meje razpona lahko pridete z uporabo vseh popustov, kot je izbira daljšega obdobja od enega meseca. <div %(div_question)s>Ali se članstva samodejno podaljšujejo?</div> Članstva se <strong>ne</strong> podaljšujejo samodejno. Pridružite se nam lahko za kakor dolgo želite. <div %(div_question)s>Za kaj porabite donacije?</div> 100%% gre za ohranjanje in omogočanje dostopa do svetovnega znanja in kulture. Trenutno ga večinoma porabimo za strežnike, diskovni prostor in pasovno širino. Noben denar ne gre nobenemu članu ekipe osebno. <div %(div_question)s>Ali lahko prispevam veliko?</div> To bi bilo neverjetno! Za donacije nad nekaj tisoč dolarjev nas kontaktirajte neposredno na %(email)s. <div %(div_question)s>Ali imate druge načine plačila?</div> Trenutno ne. Veliko ljudi si ne želi, da bi takšni arhivi obstajali, zato moramo biti previdni. Če nam lahko pomagate vzpostaviti druge (priročnejše) načine varnega plačila, se obrnite na %(email)s. Pogosta vprašanja o donacijah V teku je že <a %(a_donation)s>obstoječa donacija</a>. Prosimo, dokončajte ali prekličite to donacijo, preden začnete z novo donacijo. <a %(a_all_donations)s>Ogled vseh mojih donacij</a> Za donacije nad 5000 USD nas kontaktirajte neposredno na %(email)s. Pozdravljamo velike donacije premožnih posameznikov ali ustanov.  Bodite pozorni, da so članstva na tej strani »na mesec«, vendar gre za enkratne donacije (ne ponavljajoče se). Oglejte si <a %(faq)s>pogosta vprašanja o donacijah</a>. Annin arhiv je neprofitni, odprtokodni projekt z javno dostopnimi podatki. Z donacijo in članstvom podpirate naše delovanje in razvoj. Vsem našim članom: hvala, ker nas podpirate! ❤️ Za več informacij si oglejte <a %(a_donate)s>Pogosta vprašanja o donacijah</a>. Če želite postati član, se <a %(a_login)s>prijavite ali registrirajte</a>. Hvala za podporo! $%(cost)s / mesec Če ste pri plačilu naredili napako, ne moremo izvesti vračila, vendar jo bomo poskušali popraviti. Poiščite stran »Kripto« v svoji aplikaciji ali spletnem mestu PayPal. To se običajno nahaja pod opcijo »Finance«. Pojdite na stran »Bitcoin« v aplikaciji ali spletnem mestu PayPal. Pritisnite gumb »Prenos« %(transfer_icon)s in nato »Pošlji«. Alipay Alipay 支付宝 / WeChat 微信 Darilna kartica Amazon %(amazon)s darilna kartica Bančna kartica Bančna kartica (z uporabo aplikacije) Binance Kreditna/debetna/Apple/Google (BMC) Cash App Kreditna/debetna kartica Kreditna/debetna kartica 2 Kreditna/debetna kartica (rezerva) Kripto %(bitcoin_icon)s Kartica / PayPal / Venmo PayPal (ZDA) %(bitcoin_icon)s PayPal PayPal (redni) Pix (Brazilija) Revolut (začasno nedosegljivo) WeChat Izberite želeni kripto kovanec: Donirajte z darilno kartico Amazon. <strong>POMEMBNO:</strong> Ta možnost je za %(amazon)s. Če želite uporabiti drugo spletno stran Amazon, jo izberite zgoraj. <strong>POMEMBNO:</strong> Podpiramo samo Amazon.com, ne pa drugih spletnih mest Amazon. Na primer, .de, .co.uk, .ca NISO podprti. Prosim, NE napišite svojega sporočila. Vnesite točen znesek: %(amount)s Upoštevajte, da moramo zaokrožiti na zneske, ki jih sprejemajo naši prodajalci (najmanj %(minimum)s). Donirajte s kreditno/debetno kartico prek aplikacije Alipay (zelo enostavno za nastavitev). Namestite aplikacijo Alipay iz <a %(a_app_store)s>Apple App Store</a> ali <a %(a_play_store)s>Google Play Store</a>. Registrirajte se s svojo telefonsko številko. Nadaljnji osebni podatki niso potrebni. <span %(style)s>1</span>Namestite aplikacijo Alipay Podprte: Visa, MasterCard, JCB, Diners Club in Discover. Za več informacij si oglejte <a %(a_alipay)s>ta vodnik</a>. <span %(style)s>2</span>Dodajte bančno kartico Z Binance lahko kupite Bitcoin s kreditno/debetno kartico ali bančnim računom in nato donirate ta Bitcoin nam. Na ta način lahko ostanemo varni in anonimni pri sprejemanju vaše donacije. Binance je na voljo v skoraj vsaki državi in podpira večino bank ter kreditnih/debetnih kartic. To je trenutno naša glavna priporočena metoda. Cenimo, da si vzamete čas za učenje, kako donirati s to metodo, saj nam to zelo pomaga. Za kreditne kartice, debetne kartice, Apple Pay in Google Pay uporabljamo “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). V njihovem sistemu je ena “kava” enaka 5 $, zato bo vaša donacija zaokrožena na najbližji večkratnik 5. Donirajte z aplikacijo Cash App. Če imate aplikacijo Cash App, je to najlažji način za doniranje! Upoštevajte, da lahko aplikacija Cash App za transakcije pod %(amount)s zaračuna provizijo %(fee)s. Za %(amount)s ali več je brezplačno! Donirajte s kreditno ali debetno kartico. Ta metoda uporablja ponudnika kriptovalut kot vmesno pretvorbo. To je lahko nekoliko zmedeno, zato uporabite to metodo le, če druge plačilne metode ne delujejo. Prav tako ne deluje v vseh državah. Ne moremo neposredno podpirati kreditnih/debetnih kartic, ker banke nočejo sodelovati z nami. ☹ Vendar pa obstaja več načinov za uporabo kreditnih/debetnih kartic preko drugih plačilnih metod: S kriptovalutami lahko donirate z uporabo BTC, ETH, XMR in SOL. Uporabite to možnost, če ste že seznanjeni s kriptovalutami. S kriptovalutami lahko donirate z uporabo valut BTC, ETH, XMR in drugimi. Izrazite kripto storitve Če prvič uporabljate kriptovalute, vam priporočamo uporabo %(options)s za nakup in donacijo Bitcoina (prvotne in najbolj uporabljene kriptovalute). Upoštevajte, da lahko provizija kreditne kartice za majhne donacije izniči naš %(discount)s%% popust, zato priporočamo naročnino za daljši čas. Donirajte s kreditno/debetno kartico, PayPal ali Venmo. Na naslednji strani lahko izberete med temi možnostmi. Morda bosta delovala tudi Google Pay in Apple Pay. Upoštevajte, da so provizije za majhne donacije visoke, zato priporočamo daljše naročnine. Za doniranje s PayPal US bomo uporabili PayPal Crypto, ki nam omogoča, da ostanemo anonimni. Cenimo, da ste si vzeli čas in se naučili darovati s to metodo, saj nam zelo pomaga. Donirajte z uporabo PayPala. Donirajte z uporabo vašega rednega PayPal računa. Donirajte z uporabo Revolut. Če imate Revolut, je to najlažji način za donacijo! Ta način plačila omogoča le največ %(amount)s. Izberite drugo obdobje trajanja ali način plačila. Ta način plačila zahteva najmanj %(amount)s. Izberite drugo obdobje trajanja ali način plačila. Binance Coinbase Kraken Prosim izberite način plačila. »Posvojite torrent«: vaše uporabniško ime ali sporočilo v imenu datoteke torrent <div %(div_months)s>enkrat na vsakih 12 mesecev članstva</div> Vaše uporabniško ime ali anonimna omemba v zaslugah Zgodnji dostop do novih funkcij Ekskluzivni Telegram z informacijami iz zakulisja %(number)s hitrih prenosov na dan če boste donirali ta mesec! <a %(a_api)s>JSON API</a> dostop Legendarni status pri ohranjanju znanja in kulture človeštva Prejšnje ugodnosti, plus: Zaslužite <strong>%(percentage)s%% bonus prenosov</strong> z <a %(a_refer)s>povabilom prijateljev</a>. <strong>Neomejeni</strong> dokumenti SciDB brez preverjanja Ko sprašujete o računu ali donacijah, dodajte svojo ID številko računa, posnetke zaslona, potrdila in čim več informacij. Na e-pošto odgovarjamo le vsakih 1-2 tedna, zato bo pomanjkanje teh informacij povzročilo zamudo pri reševanju. Za še več prenosov, <a %(a_refer)s>priporočite prijatelje</a>! Smo majhna ekipa prostovoljcev. Morda bomo potrebovali 1-2 tedna, da odgovorimo. Ime računa ali slika sta lahko videti nenavadna. Brez skrbi! Te račune upravljajo naši donatorski partnerji. Naši računi niso bili vdrti. Donirajte <span %(span_cost)s></span> <span %(span_label)s></span> za 12 mesecev »%(tier_name)s« za 1 mesec »%(tier_name)s« za 24 mesecev »%(tier_name)s« za 3 mesece »%(tier_name)s« za 48 mesecev “%(tier_name)s” za 6 mesecev »%(tier_name)s« za 96 mesecev “%(tier_name)s” Donacijo lahko še vedno prekličete postopkom. Kliknite gumb doniraj, da potrdite to donacijo. <strong>Pomembna opomba:</strong> Cene kriptovalut lahko močno nihajo, včasih tudi do 20 %% v nekaj minutah. To je še vedno manj od provizij, ki jih imamo pri številnih ponudnikih plačil, ki pogosto zaračunavajo 50–60 %% za sodelovanje z »dobrodelno organizacijo v senci«, kot je mi. <u>Če nam pošljete potrdilo s prvotno ceno, ki ste jo plačali, bomo vaš račun spremenili v izbrano članstvo</u> (če potrdilo ni starejše od nekaj ur). Resnično cenimo, da ste pripravljeni prenašati takšne stvari, da bi nas podprli! ❤️ ❌ Nekaj je šlo narobe. Prosimo ponovno naložite stran in poskusite znova. <span %(span_circle)s>1</span>Kupite Bitcoin na Paypalu <span %(span_circle)s>2</span>Prenesite Bitcoin na naš naslov ✅ Preusmeritev na stran za donacije… Doniraj Prosimo, počakajte vsaj <span %(span_hours)s>24 ur</span> (in osvežite to stran), preden nas kontaktirate. Če želite prispevati (poljuben znesek) brez članstva, uporabite ta Monero (XMR) naslov: %(address)s. Po pošiljanju vaše darilne kartice jo bo naš avtomatiziran sistem potrdil v nekaj minutah. Če to ne deluje, poskusite znova poslati darilno kartico (<a %(a_instr)s>navodila</a>). Če to še vedno ne deluje, nam pošljite e-pošto in Anna ga bo ročno pregledala (to lahko traja nekaj dni) in ne pozabite omeniti, da ste že poskusili znova poslati. Primer: Uporabite <a %(a_form)s>uradni obrazec Amazon.com</a>, da nam pošljete darilno kartico v vrednosti %(amount)s na spodnji e-poštni naslov. E-poštno sporočilo prejemnika »Za« v obliki: Amazonova darilna kartica Ne moremo sprejeti drugih načinov darilnih kartic, <strong>samo poslanih neposredno iz uradnega obrazca na Amazon.com</strong>. Vaše darilne kartice ne moremo vrniti, če ne uporabite tega obrazca. Uporabite samo enkrat. Edinstveno za vaš račun, ne delite. Čakanje na darilno kartico ... (za preverjanje osvežite stran) Odprite <a %(a_href)s>stran za donacije s QR kodo</a>. Skenirajte QR kodo z aplikacijo Alipay ali pritisnite gumb za odpiranje aplikacije Alipay. Prosimo, bodite potrpežljivi; stran se lahko nalaga dlje časa, saj je na Kitajskem. <span %(style)s>3</span>Opravite donacijo (skenirajte QR kodo ali pritisnite gumb) Kupite kovanec PYUSD s PayPal-om Kupite Bitcoin (BTC) na aplikaciji Cash Kupite malo več (priporočamo %(more)s več) kot znesek, ki ga donirate (%(amount)s), da pokrijete transakcijske stroške. Kar ostane, obdržite. Pojdite na stran »Bitcoin« (BTC) v aplikaciji Cash. Prenesite Bitcoin na naš naslov Za majhne donacije (pod $25) boste morda morali uporabiti Rush ali Priority. Kliknite gumb »Pošlji bitcoin«, da izvedete »dvig«. Preklopite iz dolarjev v BTC s pritiskom na ikono %(icon)s. Vnesite znesek BTC spodaj in kliknite »Pošlji«. Če se zataknete, si oglejte <a %(help_video)s>ta video</a>. Izrazite storitve so priročne, vendar zaračunavajo višje provizije. To lahko uporabite namesto kripto borze, če želite hitro opraviti večjo donacijo in vam ni mar za provizijo v višini 5-10 $. Bodite prepričani, da pošljete točen znesek kriptovalute, prikazan na strani za donacije, ne zneska v $USD. V nasprotnem primeru bo provizija odšteta in vašega članstva ne bomo mogli samodejno obdelati. Včasih lahko potrditev traja do 24 ur, zato poskrbite, da osvežite to stran (tudi če je potekla). Navodila za kreditno/debetno kartico Donirajte prek naše strani za donacije s kreditno/debetno kartico Nekateri koraki omenjajo kripto denarnice, vendar ne skrbite, za to se vam ni treba naučiti ničesar o kripto denarnicah. %(coin_name)s navodila Skeniraj to QR kodo s svojo aplikacijo za kripto denarnico, da hitro izpolniš podatke za plačilo Skeniraj QR kodo za plačilo Podpiramo samo standardno različico kripto kovancev, brez eksotičnih omrežij ali različic kovancev. Potrditev transakcije lahko traja do ene ure, odvisno od kovanca. Donirajte %(amount)s na <a %(a_page)s>tej strani</a>. Ta donacija je potekla. Prekličite in ustvarite novo. Če ste že plačali: Da, potrdilo sem poslal po e-pošti Če je tečaj kriptovalute med transakcijo nihal, obvezno priložite potrdilo, ki prikazuje prvotni menjalni tečaj. Resnično cenimo, da ste se potrudili pri uporabi kriptovalute, zelo nam pomaga! ❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova. <span %(span_circle)s>%(circle_number)s</span>Pošljite nam potrdilo po e-pošti Če naletite na kakršne koli težave, nas kontaktirajte na %(email)s in vključite čim več informacij (na primer posnetke zaslona). ✅ Hvala za vašo donacijo! Anna bo v nekaj dneh ročno aktivirala vaše članstvo. Pošljite potrdilo ali posnetek zaslona na svoj osebni naslov za preverjanje: Ko pošljete potrdilo po e-pošti, kliknite ta gumb, da ga bo Anna lahko ročno pregledala (to lahko traja nekaj dni): Pošljite potrdilo ali posnetek zaslona na vaš osebni naslov za preverjanje. NE uporabljajte tega e-poštnega naslova za vašo PayPal donacijo. Prekliči Da, prosim prekliči Ali ste prepričani, da želite preklicati? Ne prekličite, če ste že plačali. ❌ Nekaj je šlo narobe. Ponovno naložite stran in poskusite znova. Naredi novo donacijo ✅ Vaša donacija je bila preklicana. Datum: %(date)s Identifikator: %(id)s Preuredi vrstni red Stanje: <span %(span_label)s>%(label)s</span> Skupaj: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mesec za %(duration)s mesecev, vključno z %(discounts)s%% popustom)</span> Skupaj: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mesec za %(duration)s mesecev)</span> 1. Vnesite svoj e-poštni naslov. 2. Izberite način plačila. 3. Ponovno izberite način plačila. 4. Izberite »Self-hosted« denarnico. 5. Kliknite »Potrjujem lastništvo«. 6. Prejeti bi morali e-poštno potrdilo. Pošljite nam ga in vašo donacijo bomo potrdili v najkrajšem možnem času. (morda boste želeli preklicati in ustvariti novo donacijo) Navodila za plačilo so zdaj zastarela. Če želite prispevati še eno donacijo, uporabite zgornji gumb »Preuredi vrstni red. Plačilo je že bilo izvedeno. Če vseeno želite pregledati plačilna navodila, kliknite tukaj: Prikaži stara plačilna navodila Če je stran za donacije blokirana, poskusite z drugo internetno povezavo (npr. VPN ali internet na telefonu). Na žalost je stran Alipay pogosto dostopna samo iz <strong>celinske Kitajske</strong>. Morda boste morali začasno onemogočiti svoj VPN ali uporabiti VPN za celinsko Kitajsko (včasih deluje tudi Hong Kong). <span %(span_circle)s>1</span>Donirajte na Alipay Donirajte skupni znesek %(total)s z uporabo <a %(a_account)s>tega Alipay računa</a> Alipay navodila <span %(span_circle)s>1</span>Prenos na enega od naših kripto računov Donirajte skupni znesek %(total)s na enega od teh naslovov: Kripto navodila Sledite navodilom za nakup Bitcoin (BTC). Kupiti morate samo znesek, ki ga želite donirati, %(total)s. Vnesite naš naslov Bitcoin (BTC) kot prejemnika in sledite navodilom za pošiljanje donacije %(total)s: <span %(span_circle)s>1</span>Donirajte za Pix Donirajte skupni znesek %(total)s z <a %(a_account)s>tem računom Pix Pix navodila <span %(span_circle)s>1</span>Donirajte na WeChat Donirajte skupni znesek %(total)s z uporabo <a %(a_account)s>tega WeChat računa</a> Navodila za WeChat Uporabite katero koli od naslednjih storitev »kreditna kartica za Bitcoin«, ki trajajo le nekaj minut: BTC / Bitcoin naslov (zunanji denarnik): BTC / Bitcoin znesek: Izpolnite naslednje podatke v obrazcu: Če so te informacije zastarele, nam prosim pošljite e-pošto, da nas obvestite. Prosimo, uporabite ta <span %(underline)s>točen znesek</span>. Vaši skupni stroški so lahko višji zaradi provizij kreditne kartice. Pri majhnih zneskih je to lahko več kot naš popust, žal. (minimum: %(minimum)s, brez preverjanja za prvo transakcijo) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, brez preverjanja za prvo transakcijo) (minimum: %(minimum)s) (minimum: %(minimum)s odvisno od države, brez preverjanja za prvo transakcijo) Sledite navodilom za nakup kovanca PYUSD (PayPal USD). Kupite malo več (priporočamo %(more)s več) od zneska, ki ga donirate (%(amount)s), da pokrijete stroške transakcije. Vse, kar ostane, boste obdržali. Pojdite na stran »PYUSD« v aplikaciji ali spletnem mestu PayPal. Pritisnite gumb "Prenos" %(icon)s in nato "Pošlji". Posodobi status Če želite ponastaviti časovnik, preprosto ustvarite novo donacijo. Bodite prepričani, da uporabite spodnji znesek v BTC, <em>NE</em> v evrih ali dolarjih, sicer ne bomo prejeli pravilnega zneska in ne bomo mogli samodejno potrditi vašega članstva. Kupite Bitcoin (BTC) na Revolut Kupite malo več (priporočamo %(more)s več) kot znesek, ki ga donirate (%(amount)s), da pokrijete transakcijske stroške. Kar ostane, obdržite. Pojdite na stran »Crypto« v Revolut, da kupite Bitcoin (BTC). Prenesite Bitcoin na naš naslov Za majhne donacije (pod $25) boste morda morali uporabiti Rush ali Priority. Kliknite gumb »Pošlji bitcoin«, da izvedete »dvig«. Preklopite iz evrov v BTC s pritiskom na ikono %(icon)s. Vnesite znesek BTC spodaj in kliknite »Pošlji«. Če se zataknete, si oglejte <a %(help_video)s>ta video</a>. Status: 1 2 Vodnik po korakih Oglejte si spodnji vodnik po korakih. V nasprotnem primeru lahko izgubite dostop do tega računa! Če še niste, si zapišite svoj skrivni ključ za prijavo: Hvala za vašo donacijo! Preostali čas: Donacija Prenesite %(amount)s na %(account)s Čakanje na potrditev (za preveritev osvežite stran)… Čakanje na prenos (za preveritev osvežite stran)… Prej Hitri prenosi v zadnjih 24 urah se štejejo k dnevni omejitvi. Prenosi s hitrih partnerskih strežnikov so označeni z %(icon)s. Zadnjih 18 ur Nobena datoteka še ni bila prenesena. Prenesene datoteke niso javno prikazane. Vsi časi so v UTC. Prenesene datoteke Če ste datoteko prenesli hkrati s hitrim in počasnim prenosom, se bo prikazala dvakrat. Ne skrbite preveč, veliko ljudi prenaša s spletnih strani, na katere povezujemo, in zelo redko pride do težav. Vendar pa za varnost priporočamo uporabo VPN (plačljivega) ali <a %(a_tor)s>Tor</a> (brezplačnega). Prenesel sem 1984 od Georgea Orwella, ali bo policija prišla na moja vrata? Vi ste Anna! Kdo je Anna? Imamo eno stabilno JSON API za člane, za pridobitev hitrega URL-ja za prenos: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacija znotraj samega JSON-a). Za druge primere uporabe, kot so iteracija skozi vse naše datoteke, izdelava prilagojenega iskanja in podobno, priporočamo <a %(a_generate)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov. Surove podatke lahko ročno raziskujete <a %(a_explore)s>prek JSON datotek</a>. Naš seznam surovih torrentov je prav tako na voljo za prenos kot <a %(a_torrents)s>JSON</a>. Ali imate API? Tukaj ne gostimo nobenih avtorsko zaščitenih materialov. Smo iskalnik in kot takšni indeksiramo le meta podatke, ki so že javno dostopni. Pri prenosu iz teh zunanjih virov vam priporočamo, da preverite zakone v vaši jurisdikciji glede tega, kaj je dovoljeno. Nismo odgovorni za vsebine, ki jih gostijo drugi. Če imate pritožbe glede tega, kar vidite tukaj, je najbolje, da se obrnete na izvirno spletno stran. Redno posodabljamo njihove spremembe v naši bazi podatkov. Če resnično menite, da imate veljavno pritožbo po DMCA, na katero bi morali odgovoriti, izpolnite <a %(a_copyright)s>obrazec za pritožbo DMCA / avtorskih pravic</a>. Vaše pritožbe jemljemo resno in se bomo odzvali čim prej. Kako prijavim kršitev avtorskih pravic? Tukaj je nekaj knjig, ki imajo poseben pomen za svet senčnih knjižnic in digitalnega ohranjanja: Kateri so vaši najljubši knjigi? Radi bi tudi spomnili vse, da je vsa naša koda in podatki popolnoma odprtokodni. To je edinstveno za projekte, kot je naš — ne poznamo nobenega drugega projekta s tako obsežnim katalogom, ki bi bil prav tako popolnoma odprtokoden. Zelo pozdravljamo vsakogar, ki misli, da naš projekt slabo vodimo, da vzame našo kodo in podatke ter postavi svojo lastno knjižnico v senci! To ne govorimo iz kljubovanja ali kaj podobnega — resnično mislimo, da bi bilo to super, saj bi dvignilo standard za vse in bolje ohranilo dediščino človeštva. Sovražim, kako vodite ta projekt! Radi bi, da ljudje postavijo <a %(a_mirrors)s>zrcalne strežnike</a>, in to bomo finančno podprli. Kako lahko pomagam? Res je. Naš navdih za zbiranje metapodatkov je cilj Aarona Swartza "ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena", za kar je ustvaril <a %(a_openlib)s>Open Library</a>. Ta projekt je uspešen, vendar nam naš edinstven položaj omogoča pridobivanje metapodatkov, ki jih oni ne morejo. Drug navdih je bila naša želja vedeti <a %(a_blog)s>koliko knjig je na svetu</a>, da lahko izračunamo, koliko knjig moramo še rešiti. Ali zbirate meta podatke? Upoštevajte, da mhut.org blokira določene IP razpone, zato bo morda potreben VPN. <strong>Android:</strong> Kliknite meni s tremi pikami v zgornjem desnem kotu in izberite »Dodaj na začetni zaslon«. <strong>iOS:</strong> Kliknite gumb »Deli« na dnu in izberite »Dodaj na domači zaslon«. Nimamo uradne mobilne aplikacije, vendar lahko to spletno stran namestite kot aplikacijo. Ali imate mobilno aplikacijo? Prosimo, pošljite jih na <a %(a_archive)s>Internet Archive</a>. Tam jih bodo ustrezno ohranili. Kako lahko doniram knjige ali druge fizične materiale? Kako zahtevam knjige? <a %(a_blog)s>Annin Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — redne posodobitve <a %(a_software)s>Annina Programska Oprema</a> — naša odprtokodna koda <a %(a_datasets)s>Datasets</a> — o podatkih <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativne domene Ali obstajajo dodatni viri o Anninem Arhivu? <a %(a_translate)s>Prevedi na Annini Programska Oprema</a> — naš prevajalski sistem <a %(a_wikipedia)s>Wikipedia</a> — več o nas (prosimo, pomagajte ohranjati to stran posodobljeno ali ustvarite eno za svoj jezik!) Izberite želene nastavitve, pustite iskalno polje prazno, kliknite »Išči« in nato zaznamujte stran z zaznamki vašega brskalnika. Kako shranim nastavitve iskanja? Varnostne raziskovalce spodbujamo k iskanju ranljivosti v naših sistemih. Močno podpiramo odgovorno razkritje. Kontaktirajte nas <a %(a_contact)s>tukaj</a>. Trenutno ne moremo podeljevati nagrad za odkritje napak, razen za ranljivosti, ki imajo <a %(a_link)s>potencial ogroziti našo anonimnost</a>, za katere ponujamo nagrade v razponu od 10.000 do 50.000 USD. V prihodnosti bi radi razširili obseg nagrad za odkritje napak! Upoštevajte, da so napadi socialnega inženiringa izven obsega. Če vas zanima ofenzivna varnost in želite pomagati pri arhiviranju svetovnega znanja in kulture, nas kontaktirajte. Obstaja veliko načinov, kako lahko pomagate. Ali imate program za odgovorno razkritje? Preprosto nimamo dovolj virov, da bi vsem na svetu omogočili hitre prenose, čeprav bi si to želeli. Če bi se našel bogat dobrotnik, ki bi nam to omogočil, bi bilo neverjetno, vendar do takrat se trudimo po svojih najboljših močeh. Smo neprofitni projekt, ki se komaj vzdržuje z donacijami. Zato smo uvedli dva sistema za brezplačne prenose s partnerji: deljene strežnike s počasnimi prenosi in nekoliko hitrejše strežnike s čakalno listo (za zmanjšanje števila ljudi, ki prenašajo hkrati). Za naše počasne prenose imamo tudi <a %(a_verification)s>preverjanje brskalnika</a>, ker bi jih sicer zlorabljali boti in strgalniki, kar bi še bolj upočasnilo stvari za legitimne uporabnike. Upoštevajte, da boste pri uporabi brskalnika Tor morda morali prilagoditi svoje varnostne nastavitve. Pri najnižji možnosti, imenovani “Standard”, izziv Cloudflare turnstile uspe. Pri višjih možnostih, imenovanih “Varnejši” in “Najvarnejši”, izziv ne uspe. Za velike datoteke lahko počasni prenosi včasih prekinejo sredi prenosa. Priporočamo uporabo upravitelja prenosov (kot je JDownloader) za samodejno nadaljevanje velikih prenosov. Zakaj so počasni prenosi tako počasni? Pogosto zastavljena vprašanja (FAQ) Uporabite <a %(a_list)s>generator seznama torrentov</a> za ustvarjanje seznama torrentov, ki najbolj potrebujejo sejanje, znotraj vaših omejitev prostora za shranjevanje. Da, glejte stran <a %(a_llm)s>LLM podatki</a>. Večina torrentov vsebuje datoteke neposredno, kar pomeni, da lahko torrent odjemalcem naročite, naj prenesejo samo zahtevane datoteke. Za določitev, katere datoteke prenesti, lahko <a %(a_generate)s>generirate</a> naše meta podatke ali <a %(a_download)s>preneste</a> naše ElasticSearch in MariaDB baze podatkov. Na žalost številne zbirke torrentov vsebujejo .zip ali .tar datoteke v korenu, v tem primeru morate prenesti celoten torrent, preden lahko izberete posamezne datoteke. Orodja za enostavno filtriranje torrentov še niso na voljo, vendar pozdravljamo prispevke. (Vendar imamo <a %(a_ideas)s>nekaj idej</a> za slednji primer.) Dolg odgovor: Kratek odgovor: ne zlahka. Poskušamo ohraniti minimalno podvajanje ali prekrivanje med torrenti na tem seznamu, vendar to ni vedno mogoče in je močno odvisno od politik izvornih knjižnic. Za knjižnice, ki izdajajo svoje torrente, to ni v naših rokah. Za torrente, ki jih izda Annin Arhiv, dedupliciramo samo na podlagi MD5 hasha, kar pomeni, da različne različice iste knjige niso deduplicirane. Da. To so dejansko PDF-ji in EPUB-ji, le da v mnogih naših torrentih nimajo pripone. Obstajata dva mesta, kjer lahko najdete meta podatke za torrent datoteke, vključno z vrstami/priponami datotek: 1. Vsaka zbirka ali izdaja ima svoje meta podatke. Na primer, <a %(a_libgen_nonfic)s>Libgen.rs torrenti</a> imajo ustrezno bazo meta podatkov, ki je gostovana na spletni strani Libgen.rs. Običajno povezujemo na ustrezne vire meta podatkov iz vsake <a %(a_datasets)s>strani podatkovne zbirke</a> zbirke. 2. Priporočamo <a %(a_generate)s>generiranje</a> ali <a %(a_download)s>prenos</a> naših ElasticSearch in MariaDB baz podatkov. Te vsebujejo preslikavo za vsak zapis v Anninem Arhivu na ustrezne torrent datoteke (če so na voljo), pod »torrent_paths« v ElasticSearch JSON-u. Nekateri torrent odjemalci ne podpirajo velikih velikosti kosov, kar imajo številni naši torrenti (pri novejših tega ne počnemo več — čeprav je to v skladu s specifikacijami!). Če naletite na to težavo, poskusite z drugim odjemalcem ali se pritožite proizvajalcem vašega torrent odjemalca. Rad bi pomagal pri sejanju, vendar nimam veliko prostora na disku. Torrenti so prepočasni; ali lahko podatke prenesem neposredno od vas? Ali lahko prenesem samo podmnožico datotek, na primer samo določen jezik ali temo? Kako ravnate z dvojniki v torrentih? Ali lahko dobim seznam torrentov kot JSON? Ne vidim PDF-jev ali EPUB-jev v torrentih, samo binarne datoteke? Kaj naj storim? Zakaj moj torrent odjemalec ne more odpreti nekaterih vaših torrent datotek / magnetnih povezav? Pogosta vprašanja o torrentih Kako naložim nove knjige? Prosimo, oglejte si <a %(a_href)s>ta odličen projekt</a>. Ali imate monitor za čas delovanja? Kaj je Annin arhiv? Postanite član, če želite uporabljati hitre prenose. Zdaj podpiramo Amazon darilne kartice, kreditne in debetne kartice, kripto, Alipay in WeChat. Danes ste porabili vse hitre prenose. Dostop Prenosi/uro v zadnjih 30 dneh. Urno povprečje: %(hourly)s. Dnevno povprečje: %(daily)s. Sodelujemo s partnerji, da naredimo naše zbirke enostavno in prosto dostopne vsem. Verjamemo, da ima vsakdo pravico do kolektivne modrosti človeštva. In <a %(a_search)s>ne na račun avtorjev</a>. Nabori podatkov, uporabljeni v Aninem arhivu, so popolnoma odprti in jih je mogoče zrcaliti v velikem obsegu s pomočjo torrentov. <a %(a_datasets)s>Več o tem…</a> Dolgoročni arhiv Celotna zbirka podatkov Poišči Knjige, časopisi, revije, stripi, knjižnični zapisi, metapodatki, … Vsa naša <a %(a_code)s>koda</a> in <a %(a_datasets)s>podatki</a> so popolnoma odprtokodni. <span %(span_anna)s>Anin arhiv</span> je neprofitni projekt z dvema ciljema: <li><strong>Ohranjanje:</strong> Varnostno kopiranje vsega znanja in kulture človeštva.</li><li><strong>Dostop:</strong> Dajanje tega znanja in kulture na voljo vsem na svetu.</li> Imamo največjo zbirko visokokakovostnih besedil na svetu. <a %(a_llm)s>Več o tem…</a> Podatki o urjenju LLM 🪩 Zrcaljenje: poziv za prostovoljce Če vodite visoko tvegano anonimno plačilno procesor, nas kontaktirajte. Prav tako iščemo ljudi, ki želijo postaviti okusne majhne oglase. Vsi prihodki gredo v naše prizadevanja za ohranitev. Ohranjanje Ocenjujemo, da smo ohranili približno <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5 %% knjig na svetu </a>. Hranimo knjige, časopise, stripe, revije in drugo tako, da to gradivo prenašamo iz različnih <a href="https://en.wikipedia.org/wiki/Shadow_library">senčnih knjižnic</a>, uradnih knjižnic in druge zbirke skupaj na enem mestu. Vsi ti podatki so ohranjeni za vedno, saj jih je enostavno podvajati v velikem obsegu - z uporabo torrentov - kar ima za posledico veliko kopij po vsem svetu. Nekatere senčne knjižnice to že počnejo same (npr. Sci-Hub, Library Genesis), medtem ko Anin arhiv »osvobaja« druge knjižnice, ki ne ponujajo množične distribucije (npr. Z-Library) ali sploh niso senčne knjižnice (npr. Internet Archive , DuXiu). Zaradi te široke distribucije v kombinaciji z odprtokodno kodo je naše spletno mesto odporno na odstranitve in zagotavlja dolgoročno ohranjanje znanja in kulture človeštva. Več o <a href="/datasets">naših naborih podatkov</a>. Če ste <a %(a_member)s>član</a>, preverjanje brskalnika ni potrebno. 🧬&nbsp;SciDB je nadaljevanje Sci-Hub. SciDB Odpri DOI Sci-Hub je <a %(a_paused)s>začasno ustavil</a> nalaganje novih člankov. Neposreden dostop do %(count)s akademskih člankov 🧬&nbsp;SciDB je nadaljevanje Sci-Hub, z znanim vmesnikom in neposrednim ogledom PDF-jev. Vnesite svoj DOI za ogled. Imamo celotno zbirko Sci-Hub, kot tudi nove članke. Večino si lahko ogledate neposredno z znanim vmesnikom, podobnim Sci-Hub. Nekatere lahko prenesete preko zunanjih virov, v tem primeru vam prikažemo povezave do teh. S sejanjem torrentov lahko ogromno pomagate. <a %(a_torrents)s>Več o tem...</a> > %(count)s sejalcev <%(count)s sejalcev %(count_min)s–%(count_max)s sejalcev 🤝 Iščemo prostovoljce Kot neprofitni, odprtokodni projekt vedno iščemo ljudi, ki bi nam pomagali. Prenosi IPFS Seznam avtorja %(by)s, ustvarjen <span %(span_time)s>%(time)s</span> Shrani ❌ Nekaj je šlo narobe. Prosim poskusite ponovno. ✅ Shranjeno. Ponovno naložite stran. Seznam je prazen. uredi Dodajte ali odstranite s tega seznama tako, da poiščete datoteko in odprete zavihek »Seznami«. Seznam Kako vam lahko pomagamo Odstranjevanje prekrivanja (deduplikacija) Izvleček besedila in meta podatkov OCR Sposobni smo zagotoviti hiter dostop do naših celotnih zbirk, kot tudi do neizdanih zbirk. To je dostop na ravni podjetja, ki ga lahko zagotovimo za donacije v višini deset tisoč USD. Prav tako smo pripravljeni zamenjati to za visokokakovostne zbirke, ki jih še nimamo. Lahko vam povrnemo, če nam lahko zagotovite obogatitev naših podatkov, kot so: Podprite dolgoročno arhiviranje človeškega znanja, medtem ko pridobivate boljše podatke za svoj model! <a %(a_contact)s>Kontaktirajte nas</a> za pogovor o tem, kako lahko sodelujemo. Dobro je razumljeno, da LLM-ji uspevajo na visokokakovostnih podatkih. Imamo največjo zbirko knjig, člankov, revij itd. na svetu, ki so nekateri izmed najkakovostnejših besedilnih virov. LLM podatki Edinstvena velikost in razpon Naša zbirka vsebuje več kot sto milijonov datotek, vključno z akademskimi revijami, učbeniki in revijami. To velikost dosežemo s kombiniranjem velikih obstoječih repozitorijev. Nekatere naše izvorne zbirke so že na voljo v velikih količinah (Sci-Hub in deli Libgen). Druge vire smo osvobodili sami. <a %(a_datasets)s>Datasets</a> prikazuje celoten pregled. Naša zbirka vključuje milijone knjig, člankov in revij iz obdobja pred e-knjigami. Veliki deli te zbirke so že OCR-irani in imajo že malo notranjega prekrivanja. Nadaljuj Če ste izgubili ključ, prosimo <a %(a_contact)s>kontaktirajte nas</a> in navedite čim več informacij. Morda boste morali začasno ustvariti nov račun, da nas kontaktirate. Prosimo, <a %(a_account)s>prijavite se</a> za ogled te strani.</a> Da preprečimo, da bi roboti za neželeno pošto ustvarili veliko računov, moramo najprej preveriti vaš brskalnik. Če se znajdete v neskončni zanki, priporočamo namestitev <a %(a_privacypass)s>Privacy Pass</a>. Pomaga lahko tudi, če izklopite blokado oglasov in druge razširitve brskalnika. Prijavite se / Registrirajte se Annin Arhiv je začasno nedostopen zaradi vzdrževanja. Prosimo, vrnite se čez eno uro. Alternativni avtor Alternativni opis Alternativna izdaja Alternativna razširitev Alternativno ime datoteke Alternativni založnik Alternativni naslov datum odprtokodne objave Preberi več … opis Išči po Arhivu Anne za številko CADAL SSNO Išči po Anninem arhivu za DuXiu SSID številko Išči po Arhivu Anne za DuXiu DXID številko Poiščite ISBN v Aninem arhivu V Aninem arhivu poiščite številko OCLC (WorldCat) V Anninem arhivu poiščite Open Library ID Spletni pregledovalnik Anninega Arhiva %(count)s prizadete strani Po prenosu: Boljša različica te datoteke je morda na voljo na %(link)s Množični prenosi torrentov zbirka Uporabite spletna orodja za pretvorbo med formati. Priporočena orodja za pretvorbo: %(links)s Za velike datoteke priporočamo uporabo upravitelja prenosov, da preprečite prekinitve. Priporočeni upravitelji prenosov: %(links)s EBSCOhost eBook Index (samo strokovnjaki) (kliknite tudi "GET" na vrhu) (kliknite "GET" na vrhu) Zunanji prenosi <strong>🚀 Hitri prenosi</strong> Danes imate še %(remaining)s. Hvala, ker ste član! ❤️ <strong>🚀 Hitra prenosa</strong> Danes ste porabili vse hitre prenose. <strong>🚀 Hitri prenosi</strong> To datoteko ste prenesli nedavno tega. Povezave veljajo še nekaj časa. <strong>🚀 Hitri prenosi</strong> Postanite <a %(a_membership)s>član</a> in podprite dolgoročno hrambo knjig, papirjev in drugega. V znak hvaležnosti za vašo podporo prejmete hitre prenose. ❤️ 🚀 Hitri prenosi 🐢 Počasni prenosi Izposodi si iz Internet Archive Prehod IPFS #%(num)d (morda boste morali z IPFS poskusiti večkrat) Libgen.li Libgen.rs Leposlovje Libgen.rs Neleposlovje njihovi oglasi so znani po zlonamerni programski opremi, zato uporabite blokator oglasov ali ne klikajte oglasov Amazonovo »Pošlji na Kindle« djazzovo »Pošlji na Kobo/Kindle« MagzDB ManualsLib Nexus/STC (Datoteke Nexus/STC so lahko nezanesljive za prenos) Ni najdenih prenosov. Vse možnosti prenosa imajo isto datoteko in morajo biti varne za uporabo. Kljub temu bodite vedno previdni, ko prenašate datoteke iz interneta, zlasti s spletnih mest zunaj Anninega arhiva. Poskrbite tudi, da bodo vaše naprave posodobljene. (brez preusmeritve) Odpri v našem pregledovalniku (odpri v pregledovalniku) Možnost #%(num)d: %(link)s %(extra)s Poišči originalni zapis v CADAL Ročno iskanje na DuXiu Poiščite izvirni zapis v ISBNdb Poiščite izvirni zapis v WorldCat-u Poiščite izvirni zapis v Open Library Iščite po različnih drugih zbirkah podatkov za ISBN (samo pokrovitelji z onemogočenim tiskanjem) PubMed Za odpiranje datoteke boste potrebovali bralnik e-knjig ali PDF, odvisno od formata datoteke. Priporočeni bralniki e-knjig: %(links)s Anin arhiv 🧬 SciDB Sci-Hub: %(doi)s (povezani DOI morda ni na voljo v Sci-Hubu) Na svoj Kindle ali Kobo e-bralnik lahko pošljete tako PDF kot EPUB datoteke. Priporočena orodja: %(links)s Več informacij v <a %(a_slow)s>Pogosta vprašanja</a>. Podprite avtorje in knjižnice Če vam je to všeč in si to lahko privoščite, razmislite o nakupu izvirnika ali neposredni podpori avtorjem. Če je to na voljo v vaši lokalni knjižnici, razmislite o brezplačnem izposoji tam. Prenosi s partnerskega strežnika za to datoteko začasno niso na voljo. torrent Od zaupanja vrednih partnerjev. Z-Library Z-Library na Tor omrežju (zahteva brskalnik Tor) prikaži zunanje prenose <span class="font-bold">❌ Ta datoteka ima morda težave in je bila skrita v izvorni knjižnici.</span> Včasih je to na zahtevo imetnika avtorskih pravic, včasih zato, ker je na voljo boljša alternativa, včasih pa je to zaradi težave s samo datoteko. Prenos morda še vedno ustreza, vendar priporočamo, da najprej poiščete drugo datoteko. Več podrobnosti: Če še vedno želite prenesti to datoteko, jo odprite samo z zaupanja vredno in posodobljeno programsko opremo. meta podatki komentarji AA: Poiščite »%(name)s« v Aninem arhivu Raziskovalec kod: Poglej v Raziskovalcu kod “%(name)s” URL: Spletna stran: Če imate to datoteko in še ni na voljo v Aninem arhivu, jo <a %(a_request)s>naložite</a>. Internet Archive Controlled Digital Lending datoteka “%(id)s” To je zapis datoteke iz Internet Archive in ne neposredno prenesena datoteka. Lahko si poskusite izposoditi knjigo (povezava spodaj) ali uporabite ta URL, ko <a %(a_request)s>zahtevate datoteko</a>. Izboljšaj meta podatke CADAL SSNO %(id)s meta podatki zapis To je meta podatkovni zapis in ne datoteka za prenos. Ta URL lahko uporabite, ko <a %(a_request)s>zahtevate datoteko</a>. DuXiu SSID %(id)s meta podatki zapis ISBNdb %(id)s zapis meta podatkov MagzDB ID %(id)s meta podatki zapis Nexus/STC ID %(id)s meta podatki zapis OCLC (WorldCat) številka %(id)s zapis meta podatkov Zapis knjižnice Open Library %(id)s meta podatkov Sci-Hub datoteka “%(id)s” Ni najdeno »%(md5_input)s« ni bilo mogoče najti v naši bazi podatkov. Dodaj komentar (%(count)s) MD5 lahko dobite iz URL-ja, npr. MD5 boljše različice te datoteke (če je na voljo). Izpolnite to, če obstaja druga datoteka, ki se tesno ujema s to datoteko (ista izdaja, ista pripona datoteke, če jo lahko najdete), ki bi jo ljudje morali uporabiti namesto te datoteke. Če poznate boljšo različico te datoteke zunaj Anninega arhiva, jo prosimo <a %(a_upload)s>naložite</a>. Nekaj je šlo narobe. Prosimo, ponovno naložite stran in poskusite znova. Pustili ste komentar. Lahko traja nekaj minut, da se prikaže. Prosimo, uporabite <a %(a_copyright)s>obrazec za DMCA / zahtevek za avtorske pravice</a>. Opišite težavo (obvezno) Če je ta datoteka visoke kakovosti, lahko tukaj razpravljate o njej! Če ni, uporabite gumb »Prijavi težavo z datoteko«. Odlična kakovost datoteke (%(count)s) Kakovost datoteke Naučite se, kako lahko <a %(a_metadata)s>izboljšate meta podatke</a> za to datoteko sami. Opis težave Prosimo, <a %(a_login)s>prijavite se</a>. Oboževal sem to knjigo! Pomagajte skupnosti s prijavo kakovosti te datoteke! 🙌 Nekaj je šlo narobe. Prosimo, ponovno naložite stran in poskusite znova. Prijavi težavo z datoteko (%(count)s) Hvala za oddajo vašega poročila. Prikazano bo na tej strani in ročno pregledano s strani Anne (dokler ne bomo imeli ustreznega sistema moderiranja). Pustite komentar Pošlji poročilo Kaj je narobe s to datoteko? Izposoja (%(count)s) Komentarji (%(count)s) Prenosi (%(count)s) Raziščite meta podatke (%(count)s) Seznami (%(count)s) Statistika (%(count)s) Za informacije o tej določeni datoteki si oglejte njen <a %(a_href)s>JSON datoteko</a>. To je datoteka, ki jo upravlja knjižnica <a %(a_ia)s>IA’s Controlled Digital Lending</a> in jo je Anna’s Archive indeksirala za iskanje. Za informacije o različnih datasets, ki smo jih sestavili, si oglejte <a %(a_datasets)s>stran Datasets</a>. Meta podatki iz povezanega zapisa Izboljšaj meta podatke na Open Library »MD5 datoteke« je hash, ki se izračuna iz vsebine datoteke in je na podlagi te vsebine razmeroma edinstven. Vse sence knjižnic, ki smo jih tukaj indeksirali, primarno uporabljajo MD5 za identifikacijo datotek. Datoteka se lahko pojavi v več sence knjižnicah. Za informacije o različnih datasets, ki smo jih sestavili, si oglejte <a %(a_datasets)s>stran Datasets</a>. Prijavi kakovost datoteke Skupaj prenosov: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Češki meta podatki %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Opozorilo: več povezanih zapisov: Ko si ogledate knjigo na Anninem arhivu, lahko vidite različna polja: naslov, avtor, založnik, izdaja, leto, opis, ime datoteke in še več. Vse te informacije se imenujejo <em>meta podatki</em>. Ker združujemo knjige iz različnih <em>izvornih knjižnic</em>, prikazujemo vse meta podatke, ki so na voljo v tej izvorni knjižnici. Na primer, za knjigo, ki smo jo dobili iz Library Genesis, bomo prikazali naslov iz baze podatkov Library Genesis. Včasih je knjiga prisotna v <em>več izvornih knjižnicah</em>, ki imajo lahko različna polja meta podatkov. V tem primeru preprosto prikažemo najdaljšo različico vsakega polja, saj ta verjetno vsebuje najbolj uporabne informacije! Še vedno bomo prikazali druga polja pod opisom, npr. kot "alternativni naslov" (vendar le, če so različni). Prav tako izvlečemo <em>kode</em>, kot so identifikatorji in klasifikatorji, iz izvorne knjižnice. <em>Identifikatorji</em> edinstveno predstavljajo določeno izdajo knjige; primeri so ISBN, DOI, Open Library ID, Google Books ID ali Amazon ID. <em>Klasifikatorji</em> združujejo več podobnih knjig; primeri so Dewey Decimal (DCC), UDC, LCC, RVK ali GOST. Včasih so te kode izrecno povezane v izvornih knjižnicah, včasih pa jih lahko izvlečemo iz imena datoteke ali opisa (predvsem ISBN in DOI). Identifikatorje lahko uporabimo za iskanje zapisov v <em>zbirke samo z meta podatki</em>, kot so OpenLibrary, ISBNdb ali WorldCat/OCLC. V našem iskalniku je poseben <em>zavihek z meta podatki</em>, če želite brskati po teh zbirkah. Ujemajoče se zapise uporabljamo za izpolnjevanje manjkajočih polj meta podatkov (npr. če manjka naslov) ali npr. kot "alternativni naslov" (če že obstaja naslov). Če želite natančno videti, od kod izvirajo meta podatki knjige, si oglejte <em>zavihek "Tehnične podrobnosti"</em> na strani knjige. Ima povezavo do surovega JSON-a za to knjigo, s kazalci na surovi JSON izvirnih zapisov. Za več informacij si oglejte naslednje strani: <a %(a_datasets)s>Zbirke podatkov</a>, <a %(a_search_metadata)s>Iskanje (zavihek z meta podatki)</a>, <a %(a_codes)s>Raziskovalec kod</a> in <a %(a_example)s>Primer meta podatkov JSON</a>. Nazadnje lahko vse naše meta podatke <a %(a_generated)s>generirate</a> ali <a %(a_downloaded)s>preneste</a> kot baze podatkov ElasticSearch in MariaDB. Ozadje Lahko pomagate pri ohranjanju knjig z izboljšanjem meta podatkov! Najprej preberite ozadje o meta podatkih na Anninem arhivu, nato pa se naučite, kako izboljšati meta podatke s povezovanjem z Open Library in si prislužite brezplačno članstvo na Anninem arhivu. Izboljšaj meta podatke Torej, če naletite na datoteko s slabimi meta podatki, kako jo popraviti? Lahko greste v izvorno knjižnico in sledite njenim postopkom za popravljanje meta podatkov, vendar kaj storiti, če je datoteka prisotna v več izvornih knjižnicah? Obstaja en identifikator, ki je na Anninem arhivu obravnavan posebej. <strong>Polje annas_archive md5 na Open Library vedno preglasi vse druge meta podatke!</strong> Najprej se vrnimo nazaj in se naučimo o Open Library. Open Library je leta 2006 ustanovil Aaron Swartz z namenom "ena spletna stran za vsako knjigo, ki je bila kdajkoli objavljena". Je nekakšna Wikipedija za meta podatke knjig: vsakdo jo lahko ureja, je prosto licencirana in jo je mogoče prenesti v celoti. Je baza podatkov o knjigah, ki je najbolj usklajena z našim poslanstvom — pravzaprav je Annin arhiv navdihnjen z vizijo in življenjem Aarona Swartza. Namesto da bi izumljali kolo, smo se odločili preusmeriti naše prostovoljce na Open Library. Če vidite knjigo z napačnimi meta podatki, lahko pomagate na naslednji način: Upoštevajte, da to velja samo za knjige, ne za akademske članke ali druge vrste datotek. Za druge vrste datotek še vedno priporočamo iskanje izvorne knjižnice. Lahko traja nekaj tednov, da se spremembe vključijo v Annin arhiv, saj moramo prenesti najnovejši podatkovni prenos Open Library in ponovno ustvariti naš iskalni indeks.  Pojdite na <a %(a_openlib)s>spletno stran Open Library</a>. Poiščite pravilen zapis knjige. <strong>OPOZORILO:</strong> bodite prepričani, da izberete pravilno <strong>izdajo</strong>. V Open Library imate "dela" in "izdaje". "Delo" bi lahko bilo "Harry Potter in Kamen modrosti". "Izdaja" bi lahko bila: Prva izdaja iz leta 1997, ki jo je izdal Bloomsbery, s 256 stranmi. Mehka izdaja iz leta 2003, ki jo je izdal Raincoast Books, s 223 stranmi. Poljski prevod iz leta 2000 “Harry Potter I Kamie Filozoficzn” pri založbi Media Rodzina s 328 stranmi. Vse te izdaje imajo različne ISBN-je in različne vsebine, zato pazite, da izberete pravo! Uredite zapis (ali ga ustvarite, če ne obstaja) in dodajte čim več uporabnih informacij! Zdaj ste tukaj, zato lahko zapis resnično izboljšate. Pod “ID številke” izberite “Annin arhiv” in dodajte MD5 knjige iz Anninega arhiva. To je dolga vrsta črk in številk po “/md5/” v URL-ju. Poskusite najti druge datoteke v Anninem arhivu, ki se prav tako ujemajo s tem zapisom, in jih dodajte. V prihodnosti jih lahko združimo kot podvojene na iskalni strani Anninega arhiva. Ko končate, zapišite URL, ki ste ga pravkar posodobili. Ko boste posodobili vsaj 30 zapisov z MD5-ji iz Anninega arhiva, nam pošljite <a %(a_contact)s>e-pošto</a> in nam pošljite seznam. Dobili boste brezplačno članstvo za Annin arhiv, da boste lažje opravljali to delo (in kot zahvalo za vašo pomoč). To morajo biti visokokakovostne spremembe, ki dodajo znatne količine informacij, sicer bo vaša zahteva zavrnjena. Vaša zahteva bo prav tako zavrnjena, če bodo katere koli spremembe razveljavili ali popravili moderatorji Open Library. Povezovanje z Open Library Če se boste znatno vključili v razvoj in delovanje našega dela, lahko razpravljamo o delitvi večjega dela donacijskih prihodkov z vami, da jih uporabite po potrebi. Stroške gostovanja bomo plačali šele, ko boste vse nastavili in dokazali, da lahko arhiv posodabljate z novostmi. To pomeni, da boste morali prve 1-2 mesece plačati iz lastnega žepa. Vaš čas ne bo plačan (in tudi naš ne), saj gre za čisto prostovoljno delo. Pripravljeni smo kriti stroške gostovanja in VPN, sprva do 200 $ na mesec. To je dovolj za osnovni iskalni strežnik in proxy zaščiten z DMCA. Stroški gostovanja Prosimo <strong>ne kontaktirajte nas</strong> za dovoljenje ali za osnovna vprašanja. Dejanja govorijo glasneje kot besede! Vse informacije so na voljo, zato kar nadaljujte z nastavitvijo svojega zrcala. Prosto objavite vstopnice ali zahteve za združitev na našem Gitlabu, ko naletite na težave. Morda bomo morali z vami razviti nekatere funkcije, specifične za zrcalo, kot so preimenovanje iz »Annin arhiv« v ime vaše spletne strani, (sprva) onemogočanje uporabniških računov ali povezovanje nazaj na našo glavno stran s strani knjig. Ko imate svoje zrcalo v delovanju, nas prosim kontaktirajte. Radi bi pregledali vašo OpSec, in ko bo to trdno, bomo povezali vaše zrcalo in začeli tesneje sodelovati z vami. Hvala vnaprej vsem, ki so pripravljeni prispevati na ta način! To ni za tiste s šibkim srcem, vendar bi utrdilo dolgoživost največje resnično odprte knjižnice v zgodovini človeštva. Začetek Za povečanje odpornosti Anninega arhiva iščemo prostovoljce za zagon zrcalnikov. Vaša različica je jasno označena kot zrcalo, npr. »Bobov arhiv, zrcalo Anninega arhiva«. Pripravljeni ste prevzeti tveganja, povezana s tem delom, ki so precejšnja. Imate globoko razumevanje potrebne operativne varnosti. Vsebina <a %(a_shadow)s>teh</a> <a %(a_pirate)s>objav</a> vam je samoumevna. Sprva vam ne bomo omogočili dostopa do prenosov s partnerskih strežnikov, vendar če bo šlo vse dobro, lahko to delimo z vami. Upravljate odprtokodno bazo kode Anninega arhiva in redno posodabljate tako kodo kot podatke. Pripravljeni ste prispevati k naši <a %(a_codebase)s>bazi kode</a> — v sodelovanju z našo ekipo — da bi to uresničili. Iščemo naslednje: Zrcalniki: poziv za prostovoljce Izvedi še eno donacijo. Ni še nobenih donacij. <a %(a_donate)s>Izvedi prvo donacijo.</a> Podrobnosti o donacijah niso prikazane javno. Moje donacije 📡 Za množično zrcaljenje naše zbirke si oglejte strani <a %(a_datasets)s>Nabori podatkov</a> in <a %(a_torrents)s>Torrenti</a>. Prenosi z vašega IP naslova v zadnjih 24 urah: %(count)s. 🚀 Za hitrejše prenose in preskok preverjanja brskalnika <a %(a_membership)s>postanite član</a>. Prenesite s partnerskega spletnega mesta Med čakanjem lahko še naprej brskate po Anninem Arhivu v drugem zavihku (če vaš brskalnik podpira osveževanje zavihkov v ozadju). Prosto počakajte, da se naloži več strani za prenos hkrati (vendar prosimo, da naenkrat prenesete samo eno datoteko na strežnik). Ko prejmete povezavo za prenos, je veljavna več ur. Hvala za čakanje, to omogoča brezplačen dostop do spletne strani za vse! 😊 🔗 Vse povezave za prenos te datoteke: <a %(a_main)s>Glavna stran datoteke</a>. ❌ Počasni prenosi niso na voljo prek Cloudflare VPN-jev ali z drugih Cloudflare IP-naslovov. ❌ Počasni prenosi so na voljo samo prek uradne spletne strani. Obiščite %(websites)s. 📚 Za prenos uporabite naslednji URL: <a %(a_download)s>Prenesi zdaj</a>. Da bi vsem omogočili prenos datotek brezplačno, morate počakati, preden lahko prenesete to datoteko. Prosimo, počakajte <span %(span_countdown)s>%(wait_seconds)s</span> sekund, da prenesete to datoteko. Opozorilo: v zadnjih 24 urah je bilo veliko prenosov z vašega naslova IP. Prenosi so lahko počasnejši kot običajno. Če uporabljate VPN, deljeno internetno povezavo ali vaš ponudnik internetnih storitev deli IP naslove, je to opozorilo morda posledica tega. Shrani ❌ Nekaj je šlo narobe. Prosim poskusite ponovno. ✅ Shranjeno. Ponovno naložite stran. Spremenite svoje prikazno ime. Vašega identifikatorja (del za "#") ni mogoče spremeniti. Profil ustvarjen <span %(span_time)s>%(time)s</span> uredi Seznami Ustvarite nov seznam tako, da poiščete datoteko in odprete zavihek »Seznami«. Seznamov še ni Profil ni bil najden. Profil Trenutno ne moremo sprejemati zahtevkov za knjige. Ne pošiljajte nam e-pošte z zahtevki za knjige. Prosimo, podajte svoje zahteve na forumih Z-Library ali Libgen. Zapis v Aninem arhivu DOI: %(doi)s Prenesi SciDB Nexus/STC Predogled še ni na voljo. Prenesite datoteko iz <a %(a_path)s>Annin arhiv</a>. Za podporo dostopnosti in dolgoročno ohranitev človeškega znanja postanite <a %(a_donate)s>član</a>. Kot bonus, 🧬&nbsp;SciDB se nalaga hitreje za člane, brez omejitev. Ne deluje? Poskusite <a %(a_refresh)s>osvežiti stran</a>. Sci-Hub Dodaj specifično iskalno polje Išči opise in komentarje meta podatkov Leto izdaje Napredno Dostop Vsebina Prikaz Seznam Tabela File type Jezik Razvrsti po Največji Najbolj relevantno Najnovejša (velikost datoteke) (odprtokodno) (leto objave) Najstarejši Naključno Najmanjši Vir postrgal in odprl kodo AA Digitalno posojanje (%(count)s) Revijalni članki (%(count)s) Našli smo ujemanja v: %(in)s. Ko <a %(a_request)s>zahtevate datoteko</a>, se lahko sklicujete na tamkajšnji URL. Meta podatki (%(count)s) Za raziskovanje iskalnega indeksa po kodah uporabite <a %(a_href)s>Raziskovalec kod</a>. Iskalni indeks se posodablja mesečno. Trenutno vključuje vnose do %(last_data_refresh_date)s. Za več tehničnih informacij glejte %(link_open_tag)sstran z nabori podatkov</a>. Izključite Vključite samo Nepreverjeno več… Naslednji … Prejšnje Ta iskalni indeks trenutno vključuje meta podatke iz knjižnice Internet Archive’s Controlled Digital Lending. <a %(a_datasets)s>Več o naših naborih podatkov</a>. Za več digitalnih knjižnic za izposojo poglejte na <a %(a_wikipedia)s>Wikipedia</a> in <a %(a_mobileread)s>MobileRead Wiki</a>. Za zahtevke glede DMCA / avtorskih pravic <a %(a_copyright)s>kliknite tukaj</a>. Čas prenosa Napaka med iskanjem. Poskusite <a %(a_reload)s>ponovno naložiti stran</a>. Če težave ne odpravite, nam pišite na %(email)s. Hitri prenos Pravzaprav lahko vsakdo pomaga ohraniti te datoteke z deljenjem našega <a %(a_torrents)s>enotnega seznama torrentov</a>. ➡️ Včasih se to zgodi nepravilno, ko je strežnik za iskanje počasen. V takih primerih lahko pomaga <a %(a_attrs)s>ponovno nalaganje</a>. ❌ Ta datoteka ima lahko težave. Iščete članke? Ta iskalni indeks trenutno vključuje meta podatke iz različnih virov meta podatkov. <a %(a_datasets)s>Več o naših naborih podatkov</a>. Obstaja veliko, veliko virov meta podatkov za pisna dela po vsem svetu. <a %(a_wikipedia)s>Ta stran Wikipedije</a> je dober začetek, vendar če poznate tudi druge dobre sezname, nam to sporočite. Za meta podatke prikazujemo izvirne zapise. Ne združujemo zapisov. Trenutno imamo najobsežnejši odprti katalog knjig, člankov in drugih pisnih del na svetu. Zrcalimo Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>in še več</a>. <span class="font-bold">Ni najdenih datotek.</span> Poskusite z manj iskalnimi izrazi in filtri ali z različnimi iskalnimi izrazi. Rezultati %(from)s-%(to)s (%(total)s skupaj) Če najdete druge »senčne knjižnice«, ki bi jih morali zrcaliti, ali če imate kakršna koli vprašanja, nam pišite na %(email)s. %(num)d delna ujemanja %(num)d+ delna ujemanja Vnesite v polje iskalni pojem za iskanje datotek v knjižnicah digitalne izposoje. Vnesite iskalni pojem v polje za iskanje po našem katalogu %(count)s datotek, ki jih je mogoče neposredno prenesti in jih <a %(a_preserve)s>hranimo za vedno</a>. Vnesite iskalni pojem v polje za iskanje. Vnesite v polje za iskanje našega kataloga %(count)s akademskih člankov in revij, ki jih <a %(a_preserve)s>ohranjamo za vedno</a>. Vnesite v polje islani pojem za iskanje meta podatkov iz knjižnic. To je lahko uporabno, ko <a %(a_request)s>zahtevate datoteko</a>. Namig: za hitrejšo navigacijo uporabite bližnjične tipke »/« (fokus iskanja), »enter« (iskanje), »j« (gor), »k« (dol). To so meta podatki, <span %(classname)s>ne</span> datoteke za prenos. Nastavitve iskanja Poišči Digitalno posojanje Prenesi Revijalni članki Meta podatki Novo iskanje %(search_input)s - Iskanje Iskanje je trajalo predolgo, kar pomeni, da boste morda videli napačne rezultate. Včasih <a %(a_reload)s>ponovno nalaganje</a> strani pomaga. Iskanje je trajalo predolgo, kar je običajno za široke poizvedbe. Število filtrov morda ni natančno. Za velika nalaganja (več kot 10.000 datotek), ki jih Libgen ali Z-Library ne sprejmeta, nas kontaktirajte na %(a_email)s. Za Libgen.li se prepričajte, da se najprej prijavite na <a %(a_forum)s >njihovem forumu</a> z uporabniškim imenom %(username)s in geslom %(password)s, nato pa se vrnite na njihovo <a %(a_upload_page)s >stran za nalaganje</a>. Zaenkrat predlagamo nalaganje novih knjig v vejitve Library Genesis. Tukaj je <a %(a_guide)s>priročen vodnik</a>. Upoštevajte, da obe vejitvi kode, ki ju indeksiramo na tem spletnem mestu, črpata iz istega sistema za nalaganje. Za manjše prenose (do 10.000 datotek) jih prosimo naložite na oba %(first)s in %(second)s. Lahko jih tudi naložite na Z-Library <a %(a_upload)s>tukaj</a>. Za nalaganje akademskih člankov jih poleg Library Genesis naložite tudi na <a %(a_stc_nexus)s>STC Nexus</a>. So najboljša senčna knjižnica za nove članke. Še jih nismo integrirali, vendar jih bomo nekoč. Lahko uporabite njihov <a %(a_telegram)s>nalagalni bot na Telegramu</a> ali kontaktirate naslov, naveden v njihovem pripetem sporočilu, če imate preveč datotek za nalaganje na ta način. <span %(label)s>Težko prostovoljno delo (nagrade od 50 do 5.000 USD):</span> če lahko posvetite veliko časa in/ali virov naši misiji, bi radi tesneje sodelovali z vami. Sčasoma se lahko pridružite notranji ekipi. Čeprav imamo omejen proračun, lahko za najintenzivnejše delo podelimo <span %(bold)s>💰 denarne nagrade</span>. <span %(label)s>Lahka prostovoljska dela:</span> če lahko le občasno namenite nekaj ur, je še vedno veliko načinov, kako lahko pomagate. Dosledne prostovoljce nagrajujemo z <span %(bold)s>🤝 članstvi v Anninem arhivu</span>. Annin arhiv se zanaša na prostovoljce, kot ste vi. Pozdravljamo vse ravni zavezanosti in iščemo pomoč v dveh glavnih kategorijah: Če ne morete prostovoljno prispevati svojega časa, nam lahko še vedno veliko pomagate z <a %(a_donate)s>doniranjem denarja</a>, <a %(a_torrents)s>sejanjem naših torrentov</a>, <a %(a_uploading)s>nalaganjem knjig</a> ali <a %(a_help)s>povedovanjem prijateljem o Anninem arhivu</a>. <span %(bold)s>Podjetja:</span> ponujamo hiter neposreden dostop do naših zbirk v zameno za donacijo na ravni podjetja ali izmenjavo za nove zbirke (npr. novi skeni, OCR-irani datasets, obogatitev naših podatkov). <a %(a_contact)s>Kontaktirajte nas</a>, če ste to vi. Oglejte si tudi našo <a %(a_llm)s>stran LLM</a>. Nagrade Vedno iščemo ljudi s trdnimi programerskimi ali ofenzivnimi varnostnimi veščinami, da se vključijo. Lahko naredite resen prispevek k ohranjanju dediščine človeštva. Kot zahvalo podarjamo članstvo za trdne prispevke. Kot veliko zahvalo podarjamo denarne nagrade za posebej pomembne in težke naloge. To ne bi smelo biti nadomestilo za službo, vendar je dodatna spodbuda in lahko pomaga pri nastalih stroških. Večina naše kode je odprtokodna, in to bomo zahtevali tudi od vaše kode, ko bomo podeljevali nagrado. Obstajajo nekatere izjeme, o katerih se lahko pogovorimo individualno. Nagrade se podeljujejo prvi osebi, ki dokonča nalogo. Prosto komentirajte na vstopnici za nagrado, da drugim sporočite, da delate na nečem, da se lahko drugi zadržijo ali vas kontaktirajo za sodelovanje. Vendar bodite pozorni, da so tudi drugi še vedno svobodni delati na tem in poskušati biti hitrejši od vas. Vendar pa ne podeljujemo nagrad za površno delo. Če sta dve visokokakovostni oddaji narejeni blizu skupaj (v enem ali dveh dneh), se lahko odločimo, da podelimo nagrade obema, po naši presoji, na primer 100%% za prvo oddajo in 50%% za drugo oddajo (torej skupno 150%%). Za večje nagrade (zlasti nagrade za strganje podatkov), nas prosimo kontaktirajte, ko ste dokončali ~5%% tega, in ste prepričani, da bo vaša metoda obsegala celoten mejnik. Morali boste deliti svojo metodo z nami, da bomo lahko podali povratne informacije. Prav tako lahko na ta način odločimo, kaj storiti, če se več ljudi približuje nagradi, na primer potencialno podelitev nagrade več ljudem, spodbujanje ljudi k sodelovanju itd. OPOZORILO: naloge z visokimi nagradami so <span %(bold)s>težke</span> — morda bi bilo pametno začeti z lažjimi. Pojdite na naš <a %(a_gitlab)s>seznam težav na Gitlabu</a> in razvrstite po “Label priority”. To približno prikazuje vrstni red nalog, ki so nam pomembne. Naloge brez izrecnih nagrad so še vedno upravičene do članstva, zlasti tiste označene kot “Accepted” in “Annin favorit”. Morda bi želeli začeti s “Starter project”. Lahko prostovoljno delo Zdaj imamo tudi sinhroniziran Matrix kanal na %(matrix)s. Če imate nekaj ur na voljo, lahko pomagate na več načinov. Bodite prepričani, da se pridružite <a %(a_telegram)s>prostovoljnemu klepetu na Telegramu</a>. Kot znak hvaležnosti običajno podelimo 6 mesecev “Srečnega knjižničarja” za osnovne mejnike in več za nadaljnje prostovoljno delo. Vsi mejniki zahtevajo visokokakovostno delo — površno delo nam škodi bolj kot koristi in ga bomo zavrnili. Prosimo, <a %(a_contact)s>pošljite nam e-pošto</a>, ko dosežete mejnik. %(links)s povezav ali posnetkov zaslona izpolnjenih zahtev. Izpolnjevanje zahtev za knjige (ali članke itd.) na forumih Z-Library ali Library Genesis. Nimamo lastnega sistema za zahteve po knjigah, vendar zrcalimo te knjižnice, zato jih izboljšanje izboljša tudi Annin arhiv. Mejnik Naloga Odvisno od naloge. Majhne naloge, objavljene v našem <a %(a_telegram)s>prostovoljnem klepetu na Telegramu</a>. Običajno za članstvo, včasih za majhne nagrade. Majhne naloge objavljene v naši prostovoljski klepetalni skupini. Poskrbite, da boste pustili komentar na težave, ki jih odpravite, da drugi ne bodo podvajali vašega dela. %(links)s povezav zapisov, ki ste jih izboljšali. Lahko uporabite <a %(a_list)s >seznam naključnih težav z metapodatki</a> kot izhodišče. Izboljšajte meta podatke z <a %(a_metadata)s>povezovanjem</a> z Open Library. To bi moralo pokazati, da nekomu poveste o Anninem arhivu, in da se vam zahvalijo. %(links)s povezav ali posnetkov zaslona. Širjenje besede o Anninem arhivu. Na primer, z priporočanjem knjig na AA, povezovanjem na naše objave na blogu ali splošnim usmerjanjem ljudi na našo spletno stran. Popolnoma prevedite jezik (če ni bil že skoraj dokončan). <a %(a_translate)s>Prevedite</a> spletno stran. Povezava do zgodovine urejanja, ki prikazuje vaše pomembne prispevke. Izboljšajte stran Wikipedije za Annin arhiv v vašem jeziku. Vključite informacije iz Wikipedijine strani AA v drugih jezikih ter z naše spletne strani in bloga. Dodajte reference na AA na drugih ustreznih straneh. Prostovoljstvo in nagrade 