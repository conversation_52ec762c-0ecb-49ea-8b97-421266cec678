msgid "layout.index.invalid_request"
msgstr "Pedido inválido. Visita %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " e "

msgid "layout.index.header.tagline_and_more"
msgstr "e mais"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nós integramos %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Recolhemos e disponibilizamos conteúdo do %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Todo o nosso código e dados são completamente open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;A maior biblioteca livre na história."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;livros, %(paper_count)s&nbsp;artigos científicos — preservados para sempre."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;A maior biblioteca livre do mundo. ⭐️&nbsp;Conteúdo do Sci-Hub, Library Genesis, Z-Library, e mais. 📈&nbsp;%(book_any)s livros, %(journal_article)s artigos científicos, %(book_comic)s bandas desenhadas, %(magazine)s revistas — preservados para sempre."

msgid "layout.index.header.tagline_short"
msgstr "📚 A maior biblioteca livre do mundo.<br>⭐️ Conteúdo do Scihub, Libgen, Zlib, e mais."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadados incorretos (p. ex. título, descrição, imagem de capa)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemas no download (p. ex. a ligação não pode ser estabelecida, mensagem de erro, velocidade de download muito lenta)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Ficheiro não abre (p. ex. ficheiro corrompido, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Qualidade fraca (p. ex. problemas na formatação, fraca qualidade de digitalização, páginas em falta)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam/ficheiro deve ser removido (p. ex. publicidade, conteúdo abusivo)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamação de Direitos de Autor"

msgid "common.md5_report_type_mapping.other"
msgstr "Outros"

msgid "common.membership.tier_name.bonus"
msgstr "Downloads extra"

msgid "common.membership.tier_name.2"
msgstr "Explorador de Enigmas"

msgid "common.membership.tier_name.3"
msgstr "Navegador de Narrativas"

msgid "common.membership.tier_name.4"
msgstr "Arquiteto de Arquivos"

msgid "common.membership.tier_name.5"
msgstr "Mestre dos Manuscritos"

msgid "common.membership.format_currency.total"
msgstr "Total: %(amount)s"

msgid "common.membership.format_currency.total_with_usd"
msgstr "Total: %(amount)s (%(amount_usd)s)"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s extra)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "não pago"

msgid "common.donation.order_processing_status_labels.1"
msgstr "pago"

msgid "common.donation.order_processing_status_labels.2"
msgstr "cancelado"

msgid "common.donation.order_processing_status_labels.3"
msgstr "expirado"

msgid "common.donation.order_processing_status_labels.4"
msgstr "à espera de confirmação da Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "inválido"

msgid "page.donate.title"
msgstr "Doações"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Já existe um <a %(a_donation)s>donativo</a> em curso. Por favor termina ou cancela o donativo antes de iniciar outro."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ver todos os meus donativos</a>"

msgid "page.donate.header.text1"
msgstr "Annas's Archive é um projeto sem fins lucrativos, open source e open data. Ao fazeres um donativo e te tornares membro, estás a suportar toda as operações e desenvolvimento. A todos os membros: obrigado por nos darem força! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Para mais informações, consulte as <a %(a_donate)s>Perguntas sobre donativos</a>."

msgid "page.donate.refer.text1"
msgstr "Para teres ainda mais downloads, <a %(a_refer)s>partilha com os teus amigos</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Tens um extra de %(percentage)s%% downloads rápidos, porque foste referenciado pelo membro %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Aplica-se no tempo total de membro."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s downloads rápidos por dia"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se doar este mês!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mês"

msgid "page.donate.buttons.join"
msgstr "Aderir"

msgid "page.donate.buttons.selected"
msgstr "Selecionado"

msgid "page.donate.buttons.up_to_discounts"
msgstr "desconto até %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "Artigos SciDB <strong>ilimitados</strong> e sem verificação"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Acesso à <a %(a_api)s>API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Ganhe <strong>%(percentage)s%% downloads de bónus</strong> ao <a %(a_refer)s>referir amigos</a>."

msgid "page.donate.perks.credits"
msgstr "O teu nome de utilizador ou uma menção anónima nos créditos"

msgid "page.donate.perks.previous_plus"
msgstr "As vantagens anteriores, mais:"

msgid "page.donate.perks.early_access"
msgstr "Acesso prioritário a novas funcionalidades"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Acesso a notícias dos bastidores através do Telegram"

msgid "page.donate.perks.adopt"
msgstr "“Adota um torrent”: o teu nome de utilizador ou uma mensagem no nome de um torrent <div %(div_months)s>a cada 12 meses de subscrição</div>"

msgid "page.donate.perks.legendary"
msgstr "Estatuto de lenda na preservação do conhecimento e cultura da humanidade"

msgid "page.donate.expert.title"
msgstr "Acesso Avançado"

msgid "page.donate.expert.contact_us"
msgstr "entra em contacto"

msgid "page.donate.small_team"
msgstr "Somos uma pequena equipa de voluntários. Podemos demorar 1-2 semanas a responder."

msgid "page.donate.expert.unlimited_access"
msgstr "Acesso <strong>ilimitado</strong> a alta velocidade"

msgid "page.donate.expert.direct_sftp"
msgstr "Acesso a servidores <strong>SFTP</strong>"

msgid "page.donate.expert.enterprise_donation"
msgstr "Grandes donativos ou novas coleções (p. ex. novas digitalizações, conteúdo OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Agradecemos grandes donativos em nome individual ou de instituições. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Para donativos acima de $5000, por favor contacta-nos através do nosso %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Esteja ciente de que, embora as adesões nesta página sejam “por mês”, são donativos únicos (não recorrentes). Veja as <a %(faq)s>Perguntas sobre donativos</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Se quiser fazer uma doação (qualquer valor) sem ser membro, sinta-se à vontade para usar este endereço Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Seleciona um método de pagamento."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporariamente indisponível)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Cartão presente %(amazon)s"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Cartão bancário (usando app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Cartão de crédito/débito"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (EUA) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regular)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Cartão / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Crédito/débito/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Cartão bancário"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Cartão de crédito/débito (reserva)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Cartão de crédito/débito 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Com crypto, podes fazer o teu donativo usando BTC, ETH, XMR e SOL. Usa esta opção se já estás familiarizado com criptomoedas."

msgid "page.donate.payment.desc.crypto2"
msgstr "Com crypto, podes fazer o teu donativo usando BTC, ETH, XMR e outros."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se está a usar criptomoeda pela primeira vez, sugerimos usar %(options)s para comprar e doar Bitcoin (a criptomoeda original e mais utilizada)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Para fazeres o teu donativo via PayPal (EUA), usámos o PayPal Crypto, que nos permite permanecer anónimos. Sugerimos que te informes sobre como usar este método, é uma grande ajuda para nós."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Doar através do PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Doar através da Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se tens Cash App, esta é a forma mais fácil de fazeres o teu donativo!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Para transações abaixo de %(amount)s, a Cash App pode cobrar uma comissão de %(fee)s. Para donativos de %(amount)s ou superiores, é grátis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Doe usando Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se tem Revolut, esta é a forma mais fácil de doar!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Doar através de cartão de crédito ou débito."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay também podem funcionar."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Para donativos pequenos, as comissões do pagamento através de cartão de crédito podem eliminar o nosso desconto de %(discount)s%%, como tal recomendamos subscrições mais longas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Para donativos pequenos, as comissões são altas, como tal recomendamos subscrições mais longas."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Com a Binance, compra Bitcoin com um cartão de crédito/débito ou conta bancária, e depois doa esse Bitcoin para nós. Dessa forma, podemos permanecer seguros e anónimos ao aceitar a sua doação."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "A Binance está disponível em quase todos os países e suporta a maioria dos bancos e cartões de crédito/débito. Esta é atualmente a nossa principal recomendação. Agradecemos por dedicar tempo a aprender como doar usando este método, pois isso nos ajuda muito."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Doe usando a sua conta regular do PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Doe usando cartão de crédito/débito, PayPal ou Venmo. Pode escolher entre estes na próxima página."

msgid "page.donate.payment.desc.amazon"
msgstr "Doar através de cartões-presente Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "O montante tem de ser arredondado para um valor que seja aceite pelos nossos parceiros (mínimo de %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Apenas suportámos Amazon.com, não suportámos outros sites Amazon. Por exemplo, .de, .co.uk, .es NÃO são suportados."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Esta opção é para %(amazon)s. Se deseja usar outro site da Amazon, selecione-o acima."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Este método usa um fornecedor de criptomoedas como conversão intermediária. Isto pode ser um pouco confuso, por isso, use este método apenas se outros métodos de pagamento não funcionarem. Também não funciona em todos os países."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Doe usando um cartão de crédito/débito, através do app Alipay (super fácil de configurar)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instale o app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instale o app Alipay a partir da <a %(a_app_store)s>Apple App Store</a> ou <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registe-se usando o seu número de telefone."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Não são necessários mais detalhes pessoais."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Adicione cartão bancário"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Suportado: Visa, MasterCard, JCB, Diners Club e Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Veja <a %(a_alipay)s>este guia</a> para mais informações."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Não podemos aceitar cartões de crédito/débito diretamente, porque os bancos não querem trabalhar connosco. ☹ No entanto, há várias formas de usar cartões de crédito/débito, utilizando outros métodos de pagamento:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Cartões-presente Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Envia-nos cartões-presente Amazon.com utilizando o teu cartão de crédito/débito."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay suporta cartões de crédito/débito internacionais. Veja <a %(a_alipay)s>este guia</a> para mais informações."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) suporta cartões de crédito/débito internacionais. Na aplicação WeChat, vai a “Me => Services => Wallet => Add a Card”. Se não vires essa opção, ativa-a em “Me => Settings => General => Tools => Weixin Pay => Enable”."

msgid "page.donate.ccexp.crypto"
msgstr "Podes comprar crypto com cartões de crédito/débito."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Serviços expressos de criptomoeda"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Os serviços expressos são convenientes, mas cobram taxas mais altas."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Pode usar isto em vez de uma troca de criptomoeda se quiser fazer uma doação maior rapidamente e não se importar com uma taxa de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Certifique-se de enviar o valor exato de criptomoeda mostrado na página de doação, não o valor em $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Caso contrário, a taxa será subtraída e não podemos processar automaticamente a sua adesão."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(mínimo: %(minimum)s dependendo do país, sem verificação para a primeira transação)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(mínimo: %(minimum)s, sem verificação para a primeira transação)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(mínimo: %(minimum)s, sem verificação para a primeira transação)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se alguma destas informações estiver desatualizada, envie-nos um email para nos informar."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Para cartões de crédito, cartões de débito, Apple Pay e Google Pay, usamos “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). No sistema deles, um “café” equivale a $5, por isso a sua doação será arredondada para o múltiplo de 5 mais próximo."

msgid "page.donate.duration.intro"
msgstr "Seleciona a duração da tua subscrição."

msgid "page.donate.duration.1_mo"
msgstr "1 mês"

msgid "page.donate.duration.3_mo"
msgstr "3 meses"

msgid "page.donate.duration.6_mo"
msgstr "6 meses"

msgid "page.donate.duration.12_mo"
msgstr "12 meses"

msgid "page.donate.duration.24_mo"
msgstr "24 meses"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 meses"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 meses"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>com <span %(span_discount)s></span> de desconto</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Este método de pagamento tem um mínimo de %(amount)s. Por favor seleciona uma subscrição diferente ou outro método de pagamento."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Doar"

msgid "page.donate.payment.maximum_method"
msgstr "Este método de pagamento aceita um máximo de %(amount)s. Por favor seleciona uma subscrição diferente ou outro método de pagamento."

msgid "page.donate.login2"
msgstr "Para te tornares um membro, por favor <a %(a_login)s>Entra ou Regista-te</a>. Obrigado pelo teu apoio!"

msgid "page.donate.payment.crypto_select"
msgstr "Seleciona a crypto:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(menor valor mínimo)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(use ao enviar Ethereum do Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(aviso: valor mínimo alto)"

msgid "page.donate.submit.confirm"
msgstr "Clica no botão para confirmares este donativo."

msgid "page.donate.submit.button"
msgstr "Doar <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Podes cancelar o donativo durante o processo de checkout."

msgid "page.donate.submit.success"
msgstr "✅ A redirecionar para a página de donativos…"

msgid "page.donate.submit.failure"
msgstr "❌ Algo correu mal. Tenta novamente."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s/mês"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "por 1 mês"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "por 3 meses"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "por 6 meses"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "por 12 meses"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "por 24 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "por 48 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "por 96 meses"

msgid "page.donate.submit.button.label.1_mo"
msgstr "“%(tier_name)s” por 1 mês"

msgid "page.donate.submit.button.label.3_mo"
msgstr "“%(tier_name)s” por 3 meses"

msgid "page.donate.submit.button.label.6_mo"
msgstr "“%(tier_name)s” por 6 meses"

msgid "page.donate.submit.button.label.12_mo"
msgstr "“%(tier_name)s” por 12 meses"

msgid "page.donate.submit.button.label.24_mo"
msgstr "“%(tier_name)s” por 24 meses"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "por 48 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "por 96 meses “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donativos"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s)/mês por %(duration)s meses, incluíndo um desconto de %(discounts)s%%)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/mês por %(duration)s meses)</span>"

msgid "page.donation.header.status"
msgstr "Estado: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificador: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Cancelar"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Tens a certeza que queres cancelar? Não canceles se já tiveres feito o pagamento."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Sim, quero cancelar"

msgid "page.donation.header.cancel.success"
msgstr "✅ O teu donativo foi cancelado."

msgid "page.donation.header.cancel.new_donation"
msgstr "Fazer um novo donativo"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Algo correu mal. Tenta novamente."

msgid "page.donation.header.reorder"
msgstr "Repetir donativo"

msgid "page.donation.old_instructions.intro_paid"
msgstr "O pagamento já foi feito. Se mesmo assim quiseres ver as instruções de pagamento, clica aqui:"

msgid "page.donation.old_instructions.show_button"
msgstr "Mostrar instruções de pagamento antigas"

msgid "page.donation.thank_you_donation"
msgstr "Obrigado pelo teu donativo!"

msgid "page.donation.thank_you.secret_key"
msgstr "Se ainda não o fizeste, anota a tua chave secreta para entrares:"

msgid "page.donation.thank_you.locked_out"
msgstr "Caso contrário, podes não conseguir entrar novamente na tua conta!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "As instruções de pagamento expiraram. Se quiseres fazer um novo donativo, clica no botão \"Repetir donativo\" acima."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> Os preços de crypto podem variar de forma extrema, chegando por vezes a flutuações de 20%% em minutos. Contudo, isto continua a ser mais baixo que as comissões que muitos sistemas de pagamento nos cobram, normalmente entre 50 a 60%% para trabalharem com organizações como a nossa. <u>Se nos enviares a prova de pagamento com o preço original que pagaste, conseguimos alterar a tua subscrição para o plano que escolheste</u> (desde que a prova de pagamento não seja mais antiga do que algumas horas). Agradecemos imenso o esforço e a paciência neste processo para nos ajudares! ❤️"

msgid "page.donation.expired"
msgstr "Este donativo expirou. Por favor cancela e cria um novo."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instruções crypto"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span> Transferência para uma das nossas carteiras crypto"

msgid "page.donation.payment.crypto.text1"
msgstr "Donativo no total de %(total)s para um destes endereços:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compra Bitcoin no PayPal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Vai à pagina “Crypto” na aplicação do PayPal ou no website. Podes encontrar esta página na secção “Finances”."

msgid "page.donation.payment.paypal.text3"
msgstr "Segue as instruções para comprar Bitcoin (BTC). Só precisas de comprar a quantia que pretendes doar, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfere Bitcoin para o nosso endereço"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vai à página “Bitcoin” na aplicação do PayPal ou no website. Carrega no botão “Transfer” %(transfer_icon)s e carrega “Send”."

msgid "page.donation.payment.paypal.text5"
msgstr "Introduz o nosso endereço Bitcoin (BTC) como destinatário e segue as instruções para enviares o teu donativo de %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instruções de cartão de crédito/débito"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Faz o teu donativo através da página de cartões de crédito/débito"

msgid "page.donation.donate_on_this_page"
msgstr "Faz o teu donativo de %(amount)s <a %(a_page)s>nesta página</a>."

msgid "page.donation.stepbystep_below"
msgstr "Segue o guia passo a passo em baixo."

msgid "page.donation.status_header"
msgstr "Estado:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "À espera de confirmação (atualiza a página para verificar)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "À espera da transferência (atualiza a página para verificar)…"

msgid "page.donation.time_left_header"
msgstr "Tempo restante:"

msgid "page.donation.might_want_to_cancel"
msgstr "(podes cancelar e fazer um novo donativo)"

msgid "page.donation.reset_timer"
msgstr "Para recomeçar o tempo, por favor faz um novo donativo."

msgid "page.donation.refresh_status"
msgstr "Atualizar estado"

msgid "page.donation.footer.issues_contact"
msgstr "Se encontrares algum problema, por favor contacta-nos através do email %(email)s e inclui o máximo de informação possível (como screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Se já pagou:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Às vezes, a confirmação pode demorar até 24 horas, por isso, certifique-se de atualizar esta página (mesmo que tenha expirado)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Compra PYUSD no PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Segue as instruções para comprar PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Compra um pouco mais (recomendámos um extra de %(more)s) do que o valor que estás a doar (%(amount)s) para cobrir as comissões de transação. O restante continuará na tua posse."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Vai à página “PYUSD” na aplicação PayPal ou no website. Carrega no botão “Transfer” %(icon)s e depois em “Send”."

msgid "page.donation.transfer_amount_to"
msgstr "Transferência de %(amount)s para %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Comprar Bitcoin (BTC) na Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vá para a página “Bitcoin” (BTC) na Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que está a doar (%(amount)s), para cobrir as taxas de transação. Fica com o que sobrar."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfira o Bitcoin para o nosso endereço"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clique no botão “Enviar bitcoin” para fazer um “levantamento”. Mude de dólares para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se tiver dificuldades."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Para doações pequenas (menos de $25), pode ser necessário usar Rush ou Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Comprar Bitcoin (BTC) na Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Vá para a página “Crypto” na Revolut para comprar Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que está a doar (%(amount)s), para cobrir as taxas de transação. Fica com o que sobrar."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfira o Bitcoin para o nosso endereço"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Clique no botão “Enviar bitcoin” para fazer um “levantamento”. Mude de euros para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se tiver dificuldades."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Certifique-se de usar o valor em BTC abaixo, <em>NÃO</em> euros ou dólares, caso contrário, não receberemos o valor correto e não poderemos confirmar automaticamente a sua adesão."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Para doações pequenas (menos de $25) pode ser necessário usar Rush ou Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Use qualquer um dos seguintes serviços expressos de “cartão de crédito para Bitcoin”, que demoram apenas alguns minutos:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Preencha os seguintes detalhes no formulário:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Montante em BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Por favor, use este <span %(underline)s>montante exato</span>. O seu custo total pode ser maior devido às taxas do cartão de crédito. Para montantes pequenos, isso pode ser mais do que o nosso desconto, infelizmente."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Endereço BTC / Bitcoin (carteira externa):"

msgid "page.donation.crypto_instructions"
msgstr "Instruções %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Apenas suportamos as versões standard das crypto moedas, não suportamos novas redes ou outras versões das moedas. Pode demorar até uma hora para confirmar a transação, dependendo da moeda."

msgid "page.donation.crypto_qr_code_title"
msgstr "Digitalize o código QR para pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Digitalize este código QR com seu aplicativo Crypto Wallet para preencher rapidamente os detalhes do pagamento"

msgid "page.donation.amazon.header"
msgstr "Cartão-presente Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Por favor utiliza a <a %(a_form)s>página oficial da Amazon.com</a> para nos enviares o cartão-presente no valor de %(amount)s para o endereço de email em baixo."

msgid "page.donation.amazon.only_official"
msgstr "Não aceitámos outros métodos de cartão-presente, <strong>envia sempre diretamente da página oficial da Amazon.com</strong>. Não conseguimos devolver os cartões-presente se não utilizares a página oficial."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Insira o valor exato: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Por favor NÃO escrevas nenhuma mensagem."

msgid "page.donation.amazon.form_to"
msgstr "Endereço de email destinatário no campo “To”:"

msgid "page.donation.amazon.unique"
msgstr "Exclusivo da tua conta, não partilhes."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usar apenas uma vez."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "À espera do cartão-presente… (atualiza a página para verificar)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Depois de enviares o cartão-presente, o nosso sistema automática irá confirmar dentro de poucos minutos. Se isto não funcionar, tenta enviar novamente o cartão-presente (<a %(a_instr)s>instruções</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Se mesmo assim não funcionar, por favor envia um email e a Anna irá rever manualmente (isto pode demorar alguns dias), certifica-te que tentaste fazer o processo mais do que uma vez."

msgid "page.donation.amazon.example"
msgstr "Exemplo:"

msgid "page.donate.strange_account"
msgstr "Nota que o nome da conta ou a imagem podem parecer estranhos. Não te preocupes! Estas contas são geridas pelos nossos parceiros de donativos. As nossas contas não foram comprometidas."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instruções Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span> %(span_circle)s>1</span>Faz o teu donativo no Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Doe o valor total de %(total)s usando <a %(a_account)s>esta conta Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Se a página de doação for bloqueada, tente uma conexão de internet diferente (por exemplo, VPN ou internet do telemóvel)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Infelizmente, a página do Alipay é frequentemente acessível apenas a partir da <strong>China continental</strong>. Pode ser necessário desativar temporariamente a sua VPN ou usar uma VPN para a China continental (ou Hong Kong também funciona às vezes)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Fazer doação (escanear código QR ou pressionar botão)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Abrir a <a %(a_href)s>página de doação com código QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Escaneie o código QR com o aplicativo Alipay ou pressione o botão para abrir o aplicativo Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Por favor, seja paciente; a página pode demorar um pouco para carregar, pois está na China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instruções do WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Doe no WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Doe o valor total de %(total)s usando <a %(a_account)s>esta conta WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instruções Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Faz o teu donativo no Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Donativo no total de %(total)s usando a conta Pix <a %(a_account)s>"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Envia-nos o recibo por e-mail"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Envie um recibo ou captura de ecrã para o seu endereço de verificação pessoal. NÃO use este endereço de email para a sua doação via PayPal."

msgid "page.donation.footer.text1"
msgstr "Enviar recibo ou screenshot para o teu endereço pessoal de verificação:"

msgid "page.donation.footer.crypto_note"
msgstr "Se o valor da crypto alterou-se durante a transação, envia-nos o recibo original onde mostra o valor original. Obrigado pela paciência a usar crypto, ajuda-nos imenso!"

msgid "page.donation.footer.text2"
msgstr "Depois de enviares o recibo, clica neste botão para a Anna verificar manualmente (pode demorar alguns dias):"

msgid "page.donation.footer.button"
msgstr "Sim, já enviei o email com o recibo"

msgid "page.donation.footer.success"
msgstr "✅ Obrigado pelo teu donativo! A Anna irá ativar manualmente a tua subscrição dentro de alguns dias."

msgid "page.donation.footer.failure"
msgstr "❌ Alguma coisa correu mal. Por favor refresca a página e tenta novamente."

msgid "page.donation.stepbystep"
msgstr "Guia passo-a-passo"

msgid "page.donation.crypto_dont_worry"
msgstr "Alguns dos passos falam em carteiras crypto, mas não te preocupes, não precisas de aprender nada relacionado com crypto para o teu donativo."

msgid "page.donation.hoodpay.step1"
msgstr "1. Introduz o teu email."

msgid "page.donation.hoodpay.step2"
msgstr "2. Seleciona o teu método de pagamento."

msgid "page.donation.hoodpay.step3"
msgstr "3. Seleciona o teu método de pagamento novamente."

msgid "page.donation.hoodpay.step4"
msgstr "4. Seleciona wallet “self-hosted”."

msgid "page.donation.hoodpay.step5"
msgstr "5. Clica em “Eu confirmo a titularidade”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Deves receber um recibo por email. Envia-nos o recibo e iremos confirmar o teu donativo assim que possível."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Por favor, aguarde pelo menos <span %(span_hours)s>24 horas</span> (e atualize esta página) antes de nos contactar."

msgid "page.donate.mistake"
msgstr "Se te enganares durante o pagamento, não conseguimos fazer devoluções, mas iremos fazer os possíveis para resolver a situação."

msgid "page.my_donations.title"
msgstr "Os meus donativos"

msgid "page.my_donations.not_shown"
msgstr "Os detalhes dos donativos não são públicos."

msgid "page.my_donations.no_donations"
msgstr "Sem donativos. <a %(a_donate)s>Fazer o meu primeiro donativo.</a>"

msgid "page.my_donations.make_another"
msgstr "Fazer outro donativo."

msgid "page.downloaded.title"
msgstr "Ficheiros transferidos"

msgid "page.downloaded.fast_partner_star"
msgstr "Downloads dos nossos Servidores Rápidos de Parceiros estão marcados com %(icon)s."

msgid "page.downloaded.twice"
msgstr "Se transferiste um ficheiro com as opções rápido e lenta, irá aparecer duas vezes."

msgid "page.downloaded.fast_download_time"
msgstr "Transferências rápidas nas últimas 24 horas contam para o limite diário."

msgid "page.downloaded.times_utc"
msgstr "Todos as datas e horas estão em UTC."

msgid "page.downloaded.not_public"
msgstr "As transferências não são públicas."

msgid "page.downloaded.no_files"
msgstr "Nenhuma transferência."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Últimas 18 horas"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Anteriormente"

msgid "page.account.logged_in.title"
msgstr "Conta"

msgid "page.account.logged_out.title"
msgstr "Iniciar Sessão/Registar"

msgid "page.account.logged_in.account_id"
msgstr "ID Conta: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Perfil público: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chave secreta (não partilhe!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "mostrar"

msgid "page.account.logged_in.membership_has_some"
msgstr "Subscrição: <strong>%(tier_name)s</strong> até %(until_date)s <a %(a_extend)s>(extender)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Subscrição: <strong>Nenhuma</strong> <a %(a_become)s>(torna-te membro)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Transferências rápidas usadas (últimas 24 horas): <strong>%(used)s/%(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "Que tipo de transferência?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupo Telegram exclusivo: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Junta-te a nós!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Atualiza a tua subscrição para um <a %(a_tier)s>plano mais alto</a> para te juntares ao grupo."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Contacta a Anna via %(email)s se estiveres interessado em atualizar a tua subscrição para um plano mais alto."

msgid "page.contact.title"
msgstr "email"

msgid "page.account.logged_in.membership_multiple"
msgstr "Podes combinar várias subscrições (as transferências rápidas por 24 horas serão somadas)."

msgid "layout.index.header.nav.public_profile"
msgstr "Perfil público"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Ficheiros descarregados"

msgid "layout.index.header.nav.my_donations"
msgstr "Os meus donativos"

msgid "page.account.logged_in.logout.button"
msgstr "Terminar sessão"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Terminaste a tua sessão. Refresca a página para iniciar sessão novamente."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Algo correu mal. Por favor refresca a página e tenta novamente."

msgid "page.account.logged_out.registered.text1"
msgstr "Registo com sucesso! A tua chave secreta: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Guarda esta chave cuidadosamente. Se a perderes, não terás acesso à tua conta."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Adicionar aos favoritos.</strong> Podes adicionar está página aos favoritos para teres acesso à tua chave.</li><li %(li_item)s><strong>Download.</strong> Carrega <a %(a_download)s>neste link</a> para transferires a tua chave.</li><li %(li_item)s><strong>Gestor de passwords.</strong> Utilizar um password manager para gravar a chave assim que a colocares abaixo.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Introduz a tua chave secreta para iniciar sessão:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chave secreta"

msgid "page.account.logged_out.key_form.button"
msgstr "Iniciar sessão"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Chave secreta inválida. Verifica a chave e tenta novamente, ou, em alternativa, regista-te novamente abaixo."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Não percas a tua chave!"

msgid "page.account.logged_out.register.header"
msgstr "Ainda não tens uma conta?"

msgid "page.account.logged_out.register.button"
msgstr "Registar nova conta"

msgid "page.login.lost_key"
msgstr "Se perdeste a tua chave, por favor <a %(a_contact)s>contacta-nos</a> e fornece o máximo de informação possível."

msgid "page.login.lost_key_contact"
msgstr "Poderás ter de criar uma nova conta temporariamente para entrares em contacto connosco."

msgid "page.account.logged_out.old_email.button"
msgstr "Conta antiga com base no email? Introduz o teu <a %(a_open)s>email aqui</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "editar"

msgid "page.list.edit.button"
msgstr "Gravar"

msgid "page.list.edit.success"
msgstr "✅ Gravado. Por favor refresca a página."

msgid "page.list.edit.failure"
msgstr "❌ Algo correu mal. Por favor tenta novamente."

msgid "page.list.by_and_date"
msgstr "Lista de %(by)s, criada a <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Lista vazia."

msgid "page.list.new_item"
msgstr "Adiciona ou remove items desta lista abrindo um ficheiro e visitando a tab “Listas”."

msgid "page.profile.title"
msgstr "Perfil"

msgid "page.profile.not_found"
msgstr "Perfil não encontrado."

msgid "page.profile.header.edit"
msgstr "editar"

msgid "page.profile.change_display_name.text"
msgstr "Muda o teu nome público. O teu identificador (a parte depois do “#”) não pode ser mudada."

msgid "page.profile.change_display_name.button"
msgstr "Gravar"

msgid "page.profile.change_display_name.success"
msgstr "✅ Gravado. Por favor refresca a página."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo correu mal. Por favor tenta novamente."

msgid "page.profile.created_time"
msgstr "Perfil criado em <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listas"

msgid "page.profile.lists.no_lists"
msgstr "Nenhuma lista encontrada"

msgid "page.profile.lists.new_list"
msgstr "Cria uma nova lista ao encontrares um ficheiro e abrindo a tab “Listas”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "A reforma dos direitos de autor é necessária para a segurança nacional"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Resumo: Os LLMs chineses (incluindo o DeepSeek) são treinados no meu arquivo ilegal de livros e artigos — o maior do mundo. O Ocidente precisa de reformular a lei de direitos de autor como uma questão de segurança nacional."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artigos complementares por TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Não há muito tempo, as “bibliotecas-sombra” estavam a morrer. O Sci-Hub, o enorme arquivo ilegal de artigos académicos, tinha parado de receber novas obras, devido a processos judiciais. A “Z-Library”, a maior biblioteca ilegal de livros, viu os seus alegados criadores serem presos por acusações criminais de direitos de autor. Conseguiram incrivelmente escapar à prisão, mas a sua biblioteca não está menos ameaçada."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quando a Z-Library enfrentou o encerramento, eu já tinha feito backup de toda a sua biblioteca e estava à procura de uma plataforma para a alojar. Essa foi a minha motivação para iniciar o Arquivo da Anna: uma continuação da missão por trás dessas iniciativas anteriores. Desde então, crescemos para ser a maior biblioteca sombra do mundo, hospedando mais de 140 milhões de textos protegidos por direitos de autor em vários formatos — livros, artigos académicos, revistas, jornais, e além."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "A minha equipa e eu somos ideólogos. Acreditamos que preservar e hospedar esses arquivos é moralmente correto. As bibliotecas em todo o mundo estão a ver cortes de financiamento, e também não podemos confiar o património da humanidade às corporações."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Depois veio a IA. Praticamente todas as grandes empresas que constroem LLMs nos contactaram para treinar com os nossos dados. A maioria (mas não todas!) das empresas sediadas nos EUA reconsideraram quando perceberam a natureza ilegal do nosso trabalho. Em contraste, as empresas chinesas abraçaram entusiasticamente a nossa coleção, aparentemente sem se preocuparem com a sua legalidade. Isto é notável, dado o papel da China como signatária de quase todos os principais tratados internacionais de direitos de autor."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Demos acesso de alta velocidade a cerca de 30 empresas. A maioria delas são empresas de LLM, e algumas são corretores de dados, que revenderão a nossa coleção. A maioria são chinesas, embora também tenhamos trabalhado com empresas dos EUA, Europa, Rússia, Coreia do Sul e Japão. A DeepSeek <a %(arxiv)s>admitiu</a> que uma versão anterior foi treinada em parte da nossa coleção, embora sejam reservados sobre o seu modelo mais recente (provavelmente também treinado com os nossos dados)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se o Ocidente quer manter-se à frente na corrida dos LLMs, e, em última análise, da AGI, precisa de reconsiderar a sua posição sobre os direitos de autor, e rapidamente. Quer concorde ou não connosco sobre o nosso caso moral, isto está agora a tornar-se um caso de economia, e até de segurança nacional. Todos os blocos de poder estão a construir super-cientistas artificiais, super-hackers e super-militares. A liberdade de informação está a tornar-se uma questão de sobrevivência para esses países — até uma questão de segurança nacional."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "A nossa equipa é de todo o mundo, e não temos um alinhamento particular. Mas encorajaríamos os países com leis de direitos de autor fortes a usarem esta ameaça existencial para as reformar. Então, o que fazer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "A nossa primeira recomendação é simples: encurtar o prazo de direitos de autor. Nos EUA, os direitos de autor são concedidos por 70 anos após a morte do autor. Isto é absurdo. Podemos alinhar isto com as patentes, que são concedidas por 20 anos após o registo. Este prazo deve ser mais do que suficiente para que os autores de livros, artigos, música, arte e outras obras criativas sejam totalmente compensados pelos seus esforços (incluindo projetos de longo prazo, como adaptações cinematográficas)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Depois, no mínimo, os legisladores devem incluir exceções para a preservação em massa e disseminação de textos. Se a perda de receita de clientes individuais for a principal preocupação, a distribuição a nível pessoal poderia continuar proibida. Por sua vez, aqueles capazes de gerir vastos repositórios — empresas que treinam LLMs, juntamente com bibliotecas e outros arquivos — estariam cobertos por estas exceções."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Alguns países já estão a implementar uma versão disto. O TorrentFreak <a %(torrentfreak)s>relatou</a> que a China e o Japão introduziram exceções de IA nas suas leis de direitos de autor. Não nos é claro como isto interage com tratados internacionais, mas certamente oferece proteção às suas empresas domésticas, o que explica o que temos observado."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quanto ao Arquivo da Anna — continuaremos o nosso trabalho clandestino enraizado em convicções morais. No entanto, o nosso maior desejo é entrar na luz e amplificar o nosso impacto legalmente. Por favor, reformem os direitos de autor."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leia os artigos complementares do TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vencedores do prémio de visualização de ISBN de $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Resumo: Recebemos algumas submissões incríveis para o prémio de visualização de ISBN de $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Há alguns meses, anunciámos um <a %(all_isbns)s>prémio de $10,000</a> para criar a melhor visualização possível dos nossos dados mostrando o espaço ISBN. Enfatizámos mostrar quais os ficheiros que já arquivámos/não arquivámos, e mais tarde um conjunto de dados descrevendo quantas bibliotecas possuem ISBNs (uma medida de raridade)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Ficámos impressionados com a resposta. Houve tanta criatividade. Um grande obrigado a todos os que participaram: a vossa energia e entusiasmo são contagiantes!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "No final, queríamos responder às seguintes perguntas: <strong>quais livros existem no mundo, quantos já arquivámos, e em quais livros devemos focar-nos a seguir?</strong> É ótimo ver tantas pessoas preocupadas com estas questões."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Começámos com uma visualização básica nós mesmos. Em menos de 300kb, esta imagem representa sucintamente a maior “lista de livros” totalmente aberta já reunida na história da humanidade:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Todos os ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Ficheiros no Arquivo da Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNOs do CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Vazamento de dados do CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSIDs do DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Índice de eBooks da EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Livros"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Arquivo da Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registo Global de Editores ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Estatal Russa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Voltar"

#, fuzzy
msgid "common.forward"
msgstr "Avançar"

#, fuzzy
msgid "common.last"
msgstr "Último"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Por favor, consulte o <a %(all_isbns)s>post original do blog</a> para mais informações."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Lançámos um desafio para melhorar isto. Ofereceríamos um prémio de $6,000 para o primeiro lugar, $3,000 para o segundo lugar e $1,000 para o terceiro lugar. Devido à resposta esmagadora e às submissões incríveis, decidimos aumentar ligeiramente o prémio e atribuir um terceiro lugar a quatro participantes, com $500 cada. Os vencedores estão abaixo, mas não deixe de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou descarregar o nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primeiro lugar $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Esta <a %(phiresky_github)s>submissão</a> (<a %(annas_archive_note_2951)s>comentário no Gitlab</a>) é simplesmente tudo o que queríamos, e mais! Gostámos especialmente das opções de visualização incrivelmente flexíveis (até suportando shaders personalizados), mas com uma lista abrangente de predefinições. Também apreciámos a rapidez e fluidez de tudo, a implementação simples (que nem sequer tem um backend), o minimapa inteligente e a explicação extensa no seu <a %(phiresky_github)s>post no blog</a>. Trabalho incrível, e um vencedor bem merecido!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Segundo lugar $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Outra <a %(annas_archive_note_2913)s>submissão</a> incrível. Não tão flexível como o primeiro lugar, mas na verdade preferimos a sua visualização a nível macro em relação ao primeiro lugar (curva de preenchimento de espaço, bordas, rotulagem, destaque, panorâmica e zoom). Um <a %(annas_archive_note_2971)s>comentário</a> de Joe Davis ressoou connosco:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Embora quadrados e retângulos perfeitos sejam matematicamente agradáveis, eles não proporcionam uma superior localidade num contexto de mapeamento. Acredito que a assimetria inerente a estas curvas de Hilbert ou Morton clássicas não é uma falha, mas uma característica. Tal como o contorno em forma de bota da Itália a torna instantaneamente reconhecível num mapa, as \"peculiaridades\" únicas destas curvas podem servir como marcos cognitivos. Esta distintividade pode melhorar a memória espacial e ajudar os utilizadores a orientarem-se, potencialmente facilitando a localização de regiões específicas ou a identificação de padrões.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ainda muitas opções para visualização e renderização, bem como uma interface de utilizador incrivelmente suave e intuitiva. Um sólido segundo lugar!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terceiro lugar $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Nesta <a %(annas_archive_note_2940)s>submissão</a>, gostámos muito dos diferentes tipos de visualizações, em particular as visualizações de comparação e de editoras."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terceiro lugar $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Embora não tenha a interface mais polida, esta <a %(annas_archive_note_2917)s>submissão</a> cumpre muitos dos requisitos. Gostámos particularmente da sua funcionalidade de comparação."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terceiro lugar $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Tal como o primeiro lugar, esta <a %(annas_archive_note_2975)s>submissão</a> impressionou-nos pela sua flexibilidade. No final, é isso que faz uma ótima ferramenta de visualização: flexibilidade máxima para utilizadores avançados, mantendo a simplicidade para utilizadores comuns."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terceiro lugar $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "A última <a %(annas_archive_note_2947)s>submissão</a> a receber um prémio é bastante básica, mas tem algumas características únicas que realmente apreciámos. Gostámos de como mostram quantos datasets cobrem um ISBN específico como medida de popularidade/confiabilidade. Também gostámos muito da simplicidade, mas eficácia, de usar um controlo deslizante de opacidade para comparações."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ideias notáveis"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Algumas ideias e implementações adicionais que gostámos particularmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Arranha-céus para raridade"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Estatísticas ao vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotações e também estatísticas ao vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista de mapa única e filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Esquema de cores padrão e mapa de calor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Alternância fácil de datasets para comparações rápidas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etiquetas bonitas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de escala com número de livros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Muitos controlos deslizantes para comparar datasets, como se fosse um DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Poderíamos continuar por um tempo, mas vamos parar aqui. Certifique-se de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou descarregue o nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Tantas submissões, e cada uma traz uma perspetiva única, seja na interface ou na implementação."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Vamos pelo menos incorporar a submissão do primeiro lugar em nosso site principal, e talvez algumas outras. Também começamos a pensar em como organizar o processo de identificar, confirmar e depois arquivar os livros mais raros. Mais novidades virão nesta frente."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Obrigado a todos que participaram. É incrível que tantas pessoas se importem."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nossos corações estão cheios de gratidão."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizando Todos os ISBNs — recompensa de $10,000 até 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Esta imagem representa a maior “lista de livros” totalmente aberta já montada na história da humanidade."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Esta imagem tem 1000×800 pixels. Cada pixel representa 2.500 ISBNs. Se temos um arquivo para um ISBN, tornamos esse pixel mais verde. Se sabemos que um ISBN foi emitido, mas não temos um arquivo correspondente, tornamos mais vermelho."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Em menos de 300kb, esta imagem representa sucintamente a maior “lista de livros” totalmente aberta já montada na história da humanidade (algumas centenas de GB comprimidos na íntegra)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Também mostra: há muito trabalho a ser feito no backup de livros (temos apenas 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Contexto"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Como pode o Arquivo da Anna alcançar a sua missão de fazer backup de todo o conhecimento da humanidade, sem saber quais livros ainda estão por aí? Precisamos de uma lista de tarefas. Uma maneira de mapear isso é através dos números ISBN, que desde a década de 1970 foram atribuídos a cada livro publicado (na maioria dos países)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Não existe uma autoridade central que conheça todas as atribuições de ISBN. Em vez disso, é um sistema distribuído, onde os países recebem intervalos de números, que depois atribuem intervalos menores a grandes editoras, que podem subdividir ainda mais os intervalos para editoras menores. Finalmente, números individuais são atribuídos aos livros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Começámos a mapear os ISBNs <a %(blog)s>há dois anos</a> com a nossa extração do ISBNdb. Desde então, extraímos muitas mais fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e mais. Uma lista completa pode ser encontrada nas páginas “Datasets” e “Torrents” no Arquivo da Anna. Agora temos, de longe, a maior coleção totalmente aberta e facilmente descarregável de metadata de livros (e, portanto, ISBNs) do mundo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Escrevemos <a %(blog)s>extensivamente</a> sobre por que nos importamos com a preservação e por que estamos atualmente numa janela crítica. Devemos agora identificar livros raros, pouco focados e exclusivamente em risco e preservá-los. Ter bons metadata sobre todos os livros do mundo ajuda nisso."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualização"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Além da imagem de visão geral, também podemos olhar para os datasets individuais que adquirimos. Use o menu suspenso e os botões para alternar entre eles."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Há muitos padrões interessantes para ver nestas imagens. Por que há alguma regularidade de linhas e blocos, que parece acontecer em diferentes escalas? Quais são as áreas vazias? Por que certos datasets estão tão agrupados? Deixaremos estas perguntas como um exercício para o leitor."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensa de $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Há muito para explorar aqui, por isso estamos a anunciar uma recompensa para melhorar a visualização acima. Ao contrário da maioria das nossas recompensas, esta tem um prazo. Tem de <a %(annas_archive)s>submeter</a> o seu código open source até 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "A melhor submissão receberá $6,000, o segundo lugar $3,000, e o terceiro lugar $1,000. Todas as recompensas serão atribuídas usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Abaixo estão os critérios mínimos. Se nenhuma submissão cumprir os critérios, ainda podemos atribuir algumas recompensas, mas isso será a nosso critério."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Faça um fork deste repositório e edite este post de blog em HTML (não são permitidos outros backends além do nosso backend Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faça com que a imagem acima seja suavemente ampliável, para que possa ampliar até aos ISBNs individuais. Clicar nos ISBNs deve levá-lo a uma página de metadata ou pesquisa no Arquivo da Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Deve ainda ser possível alternar entre todos os diferentes datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Os intervalos de países e editoras devem ser destacados ao passar o cursor. Pode usar, por exemplo, <a %(github_xlcnd_isbnlib)s>data4info.py no isbnlib</a> para informações de países, e a nossa extração “isbngrp” para editoras (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Deve funcionar bem em desktop e mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Para pontos de bónus (estas são apenas ideias — deixe a sua criatividade correr solta):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Será dada forte consideração à usabilidade e ao quão bom parece."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mostrar metadata real para ISBNs individuais ao ampliar, como título e autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Melhor curva de preenchimento de espaço. Por exemplo, um ziguezague, indo de 0 a 4 na primeira linha e depois de volta (em reverso) de 5 a 9 na segunda linha — aplicado recursivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Esquemas de cores diferentes ou personalizáveis."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vistas especiais para comparar datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Maneiras de depurar problemas, como outras metadata que não concordam bem (por exemplo, títulos muito diferentes)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotar imagens com comentários sobre ISBNs ou intervalos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualquer heurística para identificar livros raros ou em risco."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Quaisquer ideias criativas que consiga imaginar!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Pode desviar-se completamente dos critérios mínimos e fazer uma visualização completamente diferente. Se for realmente espetacular, então qualifica-se para a recompensa, mas a nosso critério."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faça submissões postando um comentário em <a %(annas_archive)s>este problema</a> com um link para o seu repositório bifurcado, pedido de mesclagem ou diferença."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Código"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "O código para gerar estas imagens, bem como outros exemplos, pode ser encontrado neste <a %(annas_archive)s>diretório</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Criámos um formato de dados compacto, com o qual toda a informação necessária do ISBN ocupa cerca de 75MB (comprimido). A descrição do formato de dados e o código para gerá-lo podem ser encontrados <a %(annas_archive_l1244_1319)s>aqui</a>. Para a recompensa, não é necessário usar isto, mas provavelmente é o formato mais conveniente para começar. Pode transformar os nossos metadata como quiser (embora todo o seu código tenha de ser open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Estamos ansiosos para ver o que vai criar. Boa sorte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contentores do Arquivo da Anna (AAC): padronizando lançamentos da maior shadow library do mundo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "O Arquivo da Anna tornou-se a maior shadow library do mundo, exigindo que padronizássemos os nossos lançamentos."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Arquivo da Anna</a> tornou-se de longe a maior shadow library do mundo, e a única shadow library da sua escala que é totalmente open-source e open-data. Abaixo está uma tabela da nossa página de Datasets (ligeiramente modificada):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Conseguimos isto de três maneiras:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Espelhando shadow libraries de open-data existentes (como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Ajudando shadow libraries que querem ser mais abertas, mas não tinham tempo ou recursos para o fazer (como a coleção de comics do Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspando bibliotecas que não desejam partilhar em massa (como Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Para (2) e (3) agora gerimos uma coleção considerável de torrents nós mesmos (centenas de TBs). Até agora, abordámos estas coleções como casos únicos, o que significa infraestrutura e organização de dados personalizadas para cada coleção. Isto adiciona um overhead significativo a cada lançamento e torna particularmente difícil fazer lançamentos mais incrementais."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "É por isso que decidimos padronizar os nossos lançamentos. Este é um post técnico no blog em que estamos a introduzir o nosso padrão: <strong>Contentores do Arquivo da Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Objetivos de design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "O nosso caso de uso principal é a distribuição de ficheiros e metadata associados de diferentes coleções existentes. As nossas considerações mais importantes são:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Ficheiros e metadata heterogéneos, o mais próximo possível do formato original."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificadores heterogéneos nas bibliotecas de origem, ou até mesmo a falta de identificadores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Lançamentos separados de metadata vs dados de ficheiros, ou lançamentos apenas de metadata (por exemplo, o nosso lançamento ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuição através de torrents, embora com a possibilidade de outros métodos de distribuição (por exemplo, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registos imutáveis, já que devemos assumir que os nossos torrents viverão para sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Lançamentos incrementais / lançamentos adicionáveis."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Legível e gravável por máquina, de forma conveniente e rápida, especialmente para a nossa stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspeção humana relativamente fácil, embora isso seja secundário em relação à legibilidade por máquina."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Fácil de semear as nossas coleções com uma seedbox padrão alugada."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dados binários podem ser servidos diretamente por servidores web como o Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Alguns não-objetivos:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Não nos importamos que os ficheiros sejam fáceis de navegar manualmente no disco, ou pesquisáveis sem pré-processamento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Não nos importamos em ser diretamente compatíveis com software de biblioteca existente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Embora deva ser fácil para qualquer pessoa semear a nossa coleção usando torrents, não esperamos que os ficheiros sejam utilizáveis sem conhecimento técnico significativo e compromisso."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Como o Arquivo da Anna é de código aberto, queremos usar o nosso formato diretamente. Quando atualizamos o nosso índice de pesquisa, apenas acedemos a caminhos publicamente disponíveis, para que qualquer pessoa que faça um fork da nossa biblioteca possa começar rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "O padrão"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Por fim, optámos por um padrão relativamente simples. É bastante flexível, não normativo e está em desenvolvimento."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contêiner do Arquivo da Anna) é um único item que consiste em <strong>metadata</strong>, e opcionalmente <strong>dados binários</strong>, ambos imutáveis. Possui um identificador globalmente único, chamado <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Coleção.</strong> Cada AAC pertence a uma coleção, que por definição é uma lista de AACs que são semanticamente consistentes. Isso significa que se fizer uma alteração significativa no formato da metadata, terá de criar uma nova coleção."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Coleções de “registos” e “ficheiros”.</strong> Por convenção, é frequentemente conveniente lançar “registos” e “ficheiros” como coleções diferentes, para que possam ser lançados em cronogramas diferentes, por exemplo, com base nas taxas de scraping. Um “registo” é uma coleção apenas de metadata, contendo informações como títulos de livros, autores, ISBNs, etc., enquanto “ficheiros” são as coleções que contêm os próprios ficheiros reais (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> O formato do AACID é este: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por exemplo, um AACID real que lançámos é <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: o nome da coleção, que pode conter letras ASCII, números e sublinhados (mas sem sublinhados duplos)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: uma versão curta do ISO 8601, sempre em UTC, por exemplo, <code>20220723T194746Z</code>. Este número tem de aumentar monotonamente a cada lançamento, embora os seus significados exatos possam diferir por coleção. Sugerimos usar o tempo de scraping ou de geração do ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: um identificador específico da coleção, se aplicável, por exemplo, o ID da Z-Library. Pode ser omitido ou truncado. Deve ser omitido ou truncado se o AACID exceder 150 caracteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: um UUID mas comprimido para ASCII, por exemplo, usando base57. Atualmente usamos a biblioteca Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Intervalo de AACID.</strong> Como os AACIDs contêm timestamps que aumentam monotonamente, podemos usá-los para denotar intervalos dentro de uma coleção específica. Usamos este formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, onde os timestamps são inclusivos. Isto é consistente com a notação ISO 8601. Os intervalos são contínuos e podem sobrepor-se, mas em caso de sobreposição devem conter registos idênticos aos lançados anteriormente nessa coleção (uma vez que os AACs são imutáveis). Registos em falta não são permitidos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Ficheiro de metadata.</strong> Um ficheiro de metadata contém a metadata de um intervalo de AACs, para uma coleção específica. Estes têm as seguintes propriedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "O nome do ficheiro deve ser um intervalo de AACID, prefixado com <code style=\"color: red\">annas_archive_meta__</code> e seguido por <code>.jsonl.zstd</code>. Por exemplo, um dos nossos lançamentos é chamado<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Conforme indicado pela extensão do ficheiro, o tipo de ficheiro é <a %(jsonlines)s>JSON Lines</a> comprimido com <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada objeto JSON deve conter os seguintes campos no nível superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Não são permitidos outros campos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> é metadata arbitrária, de acordo com a semântica da coleção. Deve ser semanticamente consistente dentro da coleção."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> é opcional e é o nome da pasta de dados binários que contém os dados binários correspondentes. O nome do ficheiro dos dados binários correspondentes dentro dessa pasta é o AACID do registo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "O prefixo <code style=\"color: red\">annas_archive_meta__</code> pode ser adaptado ao nome da sua instituição, por exemplo, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Pasta de dados binários.</strong> Uma pasta com os dados binários de uma gama de AACs, para uma coleção específica. Estas têm as seguintes propriedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "O nome do diretório deve ser uma gama de AACID, prefixado com <code style=\"color: green\">annas_archive_data__</code>, e sem sufixo. Por exemplo, um dos nossos lançamentos reais tem um diretório chamado<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "O diretório deve conter ficheiros de dados para todos os AACs dentro da gama especificada. Cada ficheiro de dados deve ter o seu AACID como nome do ficheiro (sem extensões)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Recomenda-se que estas pastas sejam de tamanho gerenciável, por exemplo, não maiores que 100GB-1TB cada, embora esta recomendação possa mudar ao longo do tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Os ficheiros de metadata e as pastas de dados binários podem ser agrupados em torrents, com um torrent por ficheiro de metadata, ou um torrent por pasta de dados binários. Os torrents devem ter o nome original do ficheiro/diretório mais um sufixo <code>.torrent</code> como nome do ficheiro."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Vamos olhar para o nosso recente lançamento da Z-Library como exemplo. Consiste em duas coleções: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Isto permite-nos extrair e lançar separadamente registos de metadata dos ficheiros de livros reais. Assim, lançámos dois torrents com ficheiros de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Também lançámos um conjunto de torrents com pastas de dados binários, mas apenas para a coleção “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 no total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Ao executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver o que está dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Neste caso, é metadata de um livro conforme relatado pela Z-Library. No nível superior, temos apenas “aacid” e “metadata”, mas não “data_folder”, uma vez que não há dados binários correspondentes. O AACID contém “22430000” como ID principal, que podemos ver que é retirado de “zlibrary_id”. Podemos esperar que outros AACs nesta coleção tenham a mesma estrutura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Agora vamos executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Este é um metadata AAC muito menor, embora a maior parte deste AAC esteja localizada noutro lugar num ficheiro binário! Afinal, temos uma “pasta_de_dados” desta vez, por isso podemos esperar que os dados binários correspondentes estejam localizados em <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. O “metadata” contém o “zlibrary_id”, para que possamos facilmente associá-lo ao AAC correspondente na coleção “zlib_records”. Poderíamos ter associado de várias maneiras diferentes, por exemplo, através do AACID — o padrão não prescreve isso."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Note que também não é necessário que o campo “metadata” seja em si JSON. Poderia ser uma string contendo XML ou qualquer outro formato de dados. Poderia até armazenar informações de metadata no blob binário associado, por exemplo, se for uma grande quantidade de dados."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusão"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Com este padrão, podemos fazer lançamentos de forma mais incremental e adicionar mais facilmente novas fontes de dados. Já temos alguns lançamentos empolgantes em andamento!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Também esperamos que se torne mais fácil para outras bibliotecas sombra espelhar as nossas coleções. Afinal, o nosso objetivo é preservar o conhecimento e a cultura humana para sempre, por isso quanto mais redundância, melhor."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Atualização da Anna: arquivo totalmente open source, ElasticSearch, mais de 300GB de capas de livros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Temos trabalhado incessantemente para fornecer uma boa alternativa com o Arquivo da Anna. Aqui estão algumas das coisas que alcançámos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Com o Z-Library a cair e os seus (alegados) fundadores a serem presos, temos trabalhado incessantemente para fornecer uma boa alternativa com o Arquivo da Anna (não vamos colocar o link aqui, mas pode procurar no Google). Aqui estão algumas das coisas que alcançámos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "O Arquivo da Anna é totalmente open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Acreditamos que a informação deve ser livre, e o nosso próprio código não é exceção. Lançámos todo o nosso código na nossa instância privada do Gitlab: <a %(annas_archive)s>Software da Anna</a>. Também usamos o rastreador de problemas para organizar o nosso trabalho. Se quiser envolver-se com o nosso desenvolvimento, este é um ótimo lugar para começar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Para lhe dar uma ideia das coisas em que estamos a trabalhar, veja o nosso recente trabalho em melhorias de desempenho no lado do cliente. Como ainda não implementámos a paginação, muitas vezes devolvíamos páginas de pesquisa muito longas, com 100-200 resultados. Não queríamos cortar os resultados da pesquisa muito cedo, mas isso significava que iria abrandar alguns dispositivos. Para isso, implementámos um pequeno truque: envolvemos a maioria dos resultados da pesquisa em comentários HTML (<code><!-- --></code>), e depois escrevemos um pequeno Javascript que detetaria quando um resultado deveria tornar-se visível, momento em que descompactaríamos o comentário:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "\"Virtualização\" do DOM implementada em 23 linhas, sem necessidade de bibliotecas sofisticadas! Este é o tipo de código pragmático rápido que se obtém quando se tem tempo limitado e problemas reais que precisam de ser resolvidos. Foi relatado que a nossa pesquisa agora funciona bem em dispositivos lentos!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Outro grande esforço foi automatizar a construção da base de dados. Quando lançámos, juntámos diferentes fontes de forma desordenada. Agora queremos mantê-las atualizadas, por isso escrevemos um conjunto de scripts para descarregar novos metadata dos dois forks do Library Genesis e integrá-los. O objetivo é não só tornar isto útil para o nosso arquivo, mas também facilitar as coisas para quem quiser explorar metadata de shadow library. O objetivo seria um notebook Jupyter que tenha todos os tipos de metadata interessantes disponíveis, para que possamos fazer mais pesquisas, como descobrir que <a %(blog)s>percentagem de ISBNs são preservados para sempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, renovámos o nosso sistema de doações. Agora pode usar um cartão de crédito para depositar dinheiro diretamente nas nossas carteiras de criptomoedas, sem realmente precisar saber nada sobre criptomoedas. Vamos continuar a monitorizar como isto funciona na prática, mas é um grande avanço."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Mudar para ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Um dos nossos <a %(annas_archive)s>tickets</a> era um conjunto de problemas com o nosso sistema de pesquisa. Usávamos a pesquisa de texto completo do MySQL, já que tínhamos todos os nossos dados no MySQL de qualquer forma. Mas tinha os seus limites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Algumas consultas demoravam muito tempo, ao ponto de monopolizarem todas as conexões abertas."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Por padrão, o MySQL tem um comprimento mínimo de palavra, ou o seu índice pode ficar realmente grande. As pessoas relataram não conseguir pesquisar por “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "A pesquisa era apenas um pouco rápida quando totalmente carregada na memória, o que nos obrigava a obter uma máquina mais cara para executar isto, além de alguns comandos para pré-carregar o índice na inicialização."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Não teríamos conseguido estendê-lo facilmente para construir novas funcionalidades, como melhor <a %(wikipedia_cjk_characters)s>tokenização para línguas sem espaços</a>, filtragem/facetação, ordenação, sugestões de \"queria dizer\", autocompletar, e assim por diante."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Depois de falar com um grupo de especialistas, decidimos pelo ElasticSearch. Não tem sido perfeito (as suas sugestões de \"queria dizer\" e funcionalidades de autocompletar padrão são fracas), mas no geral tem sido muito melhor do que o MySQL para pesquisa. Ainda não estamos <a %(youtube)s>muito inclinados</a> a usá-lo para qualquer dado crítico (embora tenham feito muitos <a %(elastic_co)s>progressos</a>), mas no geral estamos bastante satisfeitos com a mudança."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por agora, implementámos uma pesquisa muito mais rápida, melhor suporte a línguas, melhor ordenação por relevância, diferentes opções de ordenação e filtragem por tipo de língua/livro/tipo de ficheiro. Se estiver curioso sobre como funciona, <a %(annas_archive_l140)s>dê</a> <a %(annas_archive_l1115)s>uma</a> <a %(annas_archive_l1635)s>olhada</a>. É bastante acessível, embora pudesse usar mais alguns comentários…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Mais de 300GB de capas de livros lançadas"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, estamos felizes em anunciar um pequeno lançamento. Em colaboração com o pessoal que opera o fork Libgen.rs, estamos a partilhar todas as suas capas de livros através de torrents e IPFS. Isto distribuirá a carga de visualização das capas entre mais máquinas e preservá-las-á melhor. Em muitos (mas não todos) casos, as capas dos livros estão incluídas nos próprios ficheiros, por isso isto é uma espécie de \"dados derivados\". Mas tê-las no IPFS ainda é muito útil para a operação diária tanto do Arquivo da Anna como dos vários forks do Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costume, pode encontrar este lançamento no Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>). Não vamos ligar para ele aqui, mas pode encontrá-lo facilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Esperamos poder relaxar um pouco o nosso ritmo, agora que temos uma alternativa decente ao Z-Library. Esta carga de trabalho não é particularmente sustentável. Se estiver interessado em ajudar com programação, operações de servidor ou trabalho de preservação, entre em contacto connosco. Ainda há muito <a %(annas_archive)s>trabalho a ser feito</a>. Obrigado pelo seu interesse e apoio."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "O Arquivo da Anna fez backup da maior shadow library de banda desenhada do mundo (95TB) — pode ajudar a semeá-la"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "A maior shadow library de banda desenhada do mundo tinha um único ponto de falha... até hoje."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discutir no Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "A maior shadow library de banda desenhada é provavelmente a de um fork específico do Library Genesis: Libgen.li. O único administrador que geria esse site conseguiu reunir uma coleção insana de banda desenhada com mais de 2 milhões de ficheiros, totalizando mais de 95TB. No entanto, ao contrário de outras coleções do Library Genesis, esta não estava disponível em massa através de torrents. Só podia aceder a estas bandas desenhadas individualmente através do seu servidor pessoal lento — um único ponto de falha. Até hoje!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Neste post, vamos contar-lhe mais sobre esta coleção e sobre a nossa angariação de fundos para apoiar mais deste trabalho."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>A Dra. Barbara Gordon tenta perder-se no mundo mundano da biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Forks do Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primeiro, um pouco de contexto. Talvez conheça a Library Genesis pela sua épica coleção de livros. Menos pessoas sabem que os voluntários da Library Genesis criaram outros projetos, como uma vasta coleção de revistas e documentos padrão, um backup completo do Sci-Hub (em colaboração com a fundadora do Sci-Hub, Alexandra Elbakyan) e, de fato, uma enorme coleção de quadradinhos."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Em algum momento, diferentes operadores dos espelhos da Library Genesis seguiram caminhos separados, o que deu origem à situação atual de ter vários \"forks\" diferentes, todos ainda carregando o nome Library Genesis. O fork Libgen.li tem, de forma única, esta coleção de quadradinhos, bem como uma considerável coleção de revistas (na qual também estamos a trabalhar)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaboração"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada a sua dimensão, esta coleção há muito que está na nossa lista de desejos, por isso, após o nosso sucesso com o backup da Z-Library, decidimos focar-nos nesta coleção. No início, fizemos a extração diretamente, o que foi um grande desafio, já que o servidor deles não estava nas melhores condições. Conseguimos cerca de 15TB desta forma, mas foi um processo lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Felizmente, conseguimos entrar em contacto com o operador da biblioteca, que concordou em enviar-nos todos os dados diretamente, o que foi muito mais rápido. Ainda assim, demorou mais de meio ano para transferir e processar todos os dados, e quase perdemos tudo devido a uma corrupção de disco, o que teria significado começar tudo de novo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Esta experiência fez-nos acreditar que é importante disponibilizar estes dados o mais rapidamente possível, para que possam ser espelhados amplamente. Estamos apenas a um ou dois incidentes de azar de perder esta coleção para sempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "A coleção"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Mover-se rapidamente significa que a coleção está um pouco desorganizada… Vamos dar uma olhada. Imagine que temos um sistema de ficheiros (que na realidade estamos a dividir em torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "O primeiro diretório, <code>/repository</code>, é a parte mais estruturada disto. Este diretório contém os chamados “mil dirs”: diretórios cada um com mil ficheiros, que são numerados incrementalmente na base de dados. O diretório <code>0</code> contém ficheiros com comic_id 0–999, e assim por diante."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Este é o mesmo esquema que a Library Genesis tem usado para as suas coleções de ficção e não-ficção. A ideia é que cada “mil dir” seja automaticamente transformado num torrent assim que estiver cheio."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "No entanto, o operador do Libgen.li nunca fez torrents para esta coleção, e assim os mil dirs provavelmente se tornaram inconvenientes, e deram lugar a “dirs não classificados”. Estes são <code>/comics0</code> até <code>/comics4</code>. Todos contêm estruturas de diretórios únicas, que provavelmente faziam sentido para a recolha dos ficheiros, mas que agora não fazem muito sentido para nós. Felizmente, o metadata ainda se refere diretamente a todos estes ficheiros, por isso a organização de armazenamento no disco não importa realmente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "O metadata está disponível na forma de uma base de dados MySQL. Esta pode ser descarregada diretamente do site da Libgen.li, mas também a disponibilizaremos num torrent, juntamente com a nossa própria tabela com todos os hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Análise"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quando recebe 95TB despejados no seu cluster de armazenamento, tenta perceber o que está lá dentro… Fizemos algumas análises para ver se podíamos reduzir um pouco o tamanho, como por exemplo removendo duplicados. Aqui estão algumas das nossas descobertas:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplicados semânticos (diferentes digitalizações do mesmo livro) podem teoricamente ser filtrados, mas é complicado. Ao olhar manualmente através dos quadradinhos, encontramos muitos falsos positivos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Existem alguns duplicados apenas por MD5, o que é relativamente desperdício, mas filtrar esses daria apenas cerca de 1% in de economia. Nesta escala, isso ainda é cerca de 1TB, mas também, nesta escala, 1TB não importa realmente. Preferimos não arriscar destruir dados acidentalmente neste processo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Encontramos um monte de dados não relacionados a livros, como filmes baseados em quadradinhos. Isso também parece desperdício, já que estes já estão amplamente disponíveis por outros meios. No entanto, percebemos que não podíamos simplesmente filtrar ficheiros de filmes, já que também existem <em>quadradinhos interativos</em> que foram lançados no computador, que alguém gravou e guardou como filmes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Em última análise, qualquer coisa que pudéssemos eliminar da coleção só pouparia alguns por cento. Então lembramos que somos acumuladores de dados, e as pessoas que vão espelhar isto também são acumuladores de dados, e assim, “O QUE QUER DIZER, ELIMINAR?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Estamos, portanto, a apresentar-lhe a coleção completa e não modificada. É uma grande quantidade de dados, mas esperamos que pessoas suficientes se importem em semeá-la de qualquer forma."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Angariação de Fundos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Estamos a lançar estes dados em grandes blocos. O primeiro torrent é de <code>/comics0</code>, que colocámos num enorme ficheiro .tar de 12TB. Isso é melhor para o seu disco rígido e software de torrent do que uma infinidade de ficheiros mais pequenos."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte deste lançamento, estamos a realizar uma angariação de fundos. Pretendemos angariar 20.000 dólares para cobrir os custos operacionais e de contratação para esta coleção, bem como para possibilitar projetos futuros e em curso. Temos alguns <em>enormes</em> em andamento."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Quem estou a apoiar com a minha doação?</em> Em resumo: estamos a fazer backup de todo o conhecimento e cultura da humanidade, tornando-o facilmente acessível. Todo o nosso código e dados são de código aberto, somos um projeto gerido completamente por voluntários, e já salvámos 125TB de livros até agora (além dos torrents existentes do Libgen e do Scihub). Em última análise, estamos a construir um ciclo que permite e incentiva as pessoas a encontrar, digitalizar e fazer backup de todos os livros do mundo. Escreveremos sobre o nosso plano mestre num post futuro. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se doar para uma adesão de 12 meses como “Archivista Admirável” (780 dólares), poderá <strong>“adotar um torrent”</strong>, o que significa que colocaremos o seu nome de utilizador ou mensagem no nome de um dos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Pode doar indo a <a %(wikipedia_annas_archive)s>Arquivo da Anna</a> e clicando no botão “Doar”. Também estamos à procura de mais voluntários: engenheiros de software, investigadores de segurança, especialistas em comércio anónimo e tradutores. Pode também apoiar-nos fornecendo serviços de alojamento. E claro, por favor, semeie os nossos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Obrigado a todos que já nos apoiaram tão generosamente! Estão realmente a fazer a diferença."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Aqui estão os torrents lançados até agora (ainda estamos a processar o resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Todos os torrents podem ser encontrados no <a %(wikipedia_annas_archive)s>Arquivo da Anna</a> em “Datasets” (não ligamos diretamente para lá, para que os links para este blog não sejam removidos do Reddit, Twitter, etc). A partir daí, siga o link para o site Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "O que vem a seguir?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Um conjunto de torrents é ótimo para preservação a longo prazo, mas não tanto para acesso diário. Vamos trabalhar com parceiros de alojamento para colocar todos estes dados na web (já que o Arquivo da Anna não aloja nada diretamente). Claro que poderá encontrar estes links de download no Arquivo da Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Estamos também a convidar todos a fazer algo com estes dados! Ajude-nos a analisá-los melhor, a deduplicá-los, a colocá-los no IPFS, a remixá-los, a treinar os seus modelos de IA com eles, e assim por diante. São todos seus, e mal podemos esperar para ver o que fará com eles."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como mencionado antes, ainda temos alguns lançamentos massivos a caminho (se <em>alguém</em> pudesse <em>acidentalmente</em> enviar-nos um dump de uma <em>certa</em> base de dados ACS4, sabe onde nos encontrar…), bem como construir o volante para fazer backup de todos os livros do mundo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Portanto, fiquem atentos, estamos apenas começando."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novos livros adicionados ao Espelho da Biblioteca Pirata (+24TB, 3,8 milhões de livros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Na versão original do Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>), fizemos um espelho da Z-Library, uma grande coleção ilegal de livros. Como lembrete, isto é o que escrevemos naquele post original do blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "A Z-Library é uma biblioteca popular (e ilegal). Eles pegaram na coleção da Library Genesis e tornaram-na facilmente pesquisável. Além disso, tornaram-se muito eficazes em solicitar novas contribuições de livros, incentivando os utilizadores contribuintes com várias vantagens. Atualmente, não contribuem com estes novos livros de volta para a Library Genesis. E ao contrário da Library Genesis, não tornam a sua coleção facilmente espelhável, o que impede uma preservação ampla. Isto é importante para o seu modelo de negócio, uma vez que cobram dinheiro pelo acesso em massa à sua coleção (mais de 10 livros por dia)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegal. É inegável que a Z-Library tem sido bem-sucedida em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer a nossa parte: garantir a preservação a longo prazo desta coleção privada."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Essa coleção data de meados de 2021. Entretanto, a Z-Library tem crescido a um ritmo impressionante: adicionaram cerca de 3,8 milhões de novos livros. Há alguns duplicados, claro, mas a maioria parece ser de livros realmente novos, ou digitalizações de maior qualidade de livros previamente submetidos. Isto deve-se em grande parte ao aumento do número de moderadores voluntários na Z-Library e ao seu sistema de upload em massa com deduplicação. Gostaríamos de os felicitar por estas conquistas."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Estamos felizes em anunciar que obtivemos todos os livros que foram adicionados à Z-Library entre o nosso último espelho e agosto de 2022. Também voltámos atrás e extraímos alguns livros que perdemos na primeira vez. No total, esta nova coleção tem cerca de 24TB, o que é muito maior do que a anterior (7TB). O nosso espelho agora tem 31TB no total. Novamente, deduplicámos em relação à Library Genesis, uma vez que já existem torrents disponíveis para essa coleção."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Por favor, vá ao Pirate Library Mirror para verificar a nova coleção (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>). Há mais informações lá sobre como os ficheiros estão estruturados e o que mudou desde a última vez. Não vamos ligar para lá a partir daqui, uma vez que este é apenas um site de blog que não aloja materiais ilegais."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Claro, semear também é uma ótima maneira de nos ajudar. Obrigado a todos que estão a semear o nosso conjunto anterior de torrents. Estamos gratos pela resposta positiva e felizes por haver tantas pessoas que se preocupam com a preservação do conhecimento e da cultura desta forma incomum."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Como se tornar um arquivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antes de começarmos, duas atualizações sobre o Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Recebemos algumas doações extremamente generosas. A primeira foi de $10 mil de um indivíduo anónimo que também tem apoiado \"bookwarrior\", o fundador original do Library Genesis. Um agradecimento especial a bookwarrior por facilitar esta doação. A segunda foi outra doação de $10 mil de um doador anónimo, que entrou em contacto após o nosso último lançamento e foi inspirado a ajudar. Também recebemos várias doações menores. Muito obrigado por todo o vosso generoso apoio. Temos alguns novos projetos empolgantes em andamento que isto irá apoiar, por isso fiquem atentos."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Tivemos algumas dificuldades técnicas com o tamanho do nosso segundo lançamento, mas os nossos torrents estão agora ativos e a semear. Também recebemos uma oferta generosa de um indivíduo anónimo para semear a nossa coleção nos seus servidores de altíssima velocidade, por isso estamos a fazer um upload especial para as suas máquinas, após o qual todos os outros que estão a descarregar a coleção deverão ver uma grande melhoria na velocidade."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Podem ser escritos livros inteiros sobre o <em>porquê</em> da preservação digital em geral, e do arquivismo pirata em particular, mas vamos dar uma breve introdução para aqueles que não estão muito familiarizados. O mundo está a produzir mais conhecimento e cultura do que nunca, mas também mais está a ser perdido do que nunca. A humanidade confia em grande parte a herança a corporações como editoras académicas, serviços de streaming e empresas de redes sociais, e muitas vezes não se têm mostrado grandes guardiões. Veja o documentário Digital Amnesia, ou qualquer palestra de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Existem algumas instituições que fazem um bom trabalho arquivando o máximo que podem, mas estão limitadas pela lei. Como piratas, estamos numa posição única para arquivar coleções que eles não podem tocar, devido à aplicação de direitos de autor ou outras restrições. Podemos também espelhar coleções muitas vezes, em todo o mundo, aumentando assim as hipóteses de preservação adequada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por agora, não vamos entrar em discussões sobre os prós e contras da propriedade intelectual, a moralidade de quebrar a lei, reflexões sobre censura, ou a questão do acesso ao conhecimento e à cultura. Com tudo isso fora do caminho, vamos mergulhar no <em>como</em>. Vamos partilhar como a nossa equipa se tornou arquivistas piratas, e as lições que aprendemos ao longo do caminho. Existem muitos desafios quando se embarca nesta jornada, e esperamos poder ajudar-vos a superar alguns deles."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunidade"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico: fazer este trabalho nas sombras pode ser incrivelmente solitário. Dependendo do que você planeja fazer e do seu modelo de ameaça, pode ter que ser muito cuidadoso. Em um extremo do espectro, temos pessoas como Alexandra Elbakyan*, a fundadora do Sci-Hub, que é muito aberta sobre suas atividades. Mas ela corre um alto risco de ser presa se visitar um país ocidental neste momento, e pode enfrentar décadas de prisão. É um risco que você estaria disposto a correr? Estamos no outro extremo do espectro; sendo muito cuidadosos para não deixar nenhum rastro e tendo uma forte segurança operacional."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como mencionado no HN por \"ynno\", Alexandra inicialmente não queria ser conhecida: \"Seus servidores foram configurados para emitir mensagens de erro detalhadas do PHP, incluindo o caminho completo do arquivo de origem com falha, que estava sob o diretório /home/<USER>" Portanto, use nomes de usuário aleatórios nos computadores que você usa para estas coisas, caso configure algo incorretamente."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Esse segredo, no entanto, vem com um custo psicológico. A maioria das pessoas adora ser reconhecida pelo trabalho que faz, e ainda assim você não pode receber nenhum crédito por isso na vida real. Até mesmo coisas simples podem ser desafiadoras, como amigos perguntando o que você tem feito (em algum momento \"mexendo com meu NAS / homelab\" fica velho)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "É por isso que é tão importante encontrar alguma comunidade. Você pode abrir mão de alguma segurança operacional confiando em alguns amigos muito próximos, que você sabe que pode confiar profundamente. Mesmo assim, tenha cuidado para não colocar nada por escrito, caso eles tenham que entregar seus e-mails às autoridades, ou se seus dispositivos forem comprometidos de alguma outra forma."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Melhor ainda é encontrar alguns companheiros piratas. Se seus amigos próximos estiverem interessados em se juntar a você, ótimo! Caso contrário, você pode encontrar outros online. Infelizmente, esta ainda é uma comunidade de nicho. Até agora, encontramos apenas um punhado de outros que estão ativos neste espaço. Bons pontos de partida parecem ser os fóruns do Library Genesis e o r/DataHoarder. A Archive Team também tem indivíduos com ideias semelhantes, embora operem dentro da lei (mesmo que em algumas áreas cinzentas da lei). As cenas tradicionais de \"warez\" e pirataria também têm pessoas que pensam de maneira semelhante."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Estamos abertos a ideias sobre como fomentar a comunidade e explorar ideias. Sinta-se à vontade para nos enviar uma mensagem no Twitter ou Reddit. Talvez possamos hospedar algum tipo de fórum ou grupo de chat. Um desafio é que isso pode ser facilmente censurado ao usar plataformas comuns, então teríamos que hospedá-lo nós mesmos. Há também um equilíbrio entre ter essas discussões totalmente públicas (mais potencial de engajamento) versus torná-las privadas (não deixar potenciais \"alvos\" saberem que estamos prestes a raspá-los). Teremos que pensar sobre isso. Deixe-nos saber se você está interessado nisso!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projetos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quando fazemos um projeto, ele tem algumas fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Seleção de domínio / filosofia: Onde você quer se concentrar aproximadamente, e por quê? Quais são suas paixões, habilidades e circunstâncias únicas que você pode usar em seu benefício?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Seleção de alvo: Qual coleção específica você irá espelhar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raspagem de metadata: Catalogar informações sobre os arquivos, sem realmente baixar os arquivos (geralmente muito maiores) em si."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Seleção de dados: Com base nos metadata, restringir quais dados são mais relevantes para arquivar agora. Pode ser tudo, mas muitas vezes há uma maneira razoável de economizar espaço e largura de banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raspagem de dados: Obter efetivamente os dados."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuição: Empacotá-los em torrents, anunciá-los em algum lugar, fazer com que as pessoas os espalhem."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Estas não são fases completamente independentes, e muitas vezes percepções de uma fase posterior fazem você voltar a uma fase anterior. Por exemplo, durante a raspagem de metadata, você pode perceber que o alvo que selecionou tem mecanismos de defesa além do seu nível de habilidade (como bloqueios de IP), então você volta e encontra um alvo diferente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Seleção de domínio / filosofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Não há falta de conhecimento e patrimônio cultural a serem salvos, o que pode ser avassalador. É por isso que muitas vezes é útil parar um momento e pensar sobre qual pode ser a sua contribuição."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Cada um tem uma maneira diferente de pensar sobre isso, mas aqui estão algumas perguntas que você poderia se fazer:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Por que você está interessado nisso? Pelo que você é apaixonado? Se conseguirmos um grupo de pessoas que arquivem os tipos de coisas que elas especificamente se importam, isso cobriria muito! Você saberá muito mais do que a pessoa média sobre sua paixão, como quais são os dados importantes a serem salvos, quais são as melhores coleções e comunidades online, e assim por diante."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Que habilidades você tem que pode usar em seu benefício? Por exemplo, se você é um especialista em segurança online, pode encontrar maneiras de derrotar bloqueios de IP para alvos seguros. Se você é ótimo em organizar comunidades, então talvez possa reunir algumas pessoas em torno de um objetivo. É útil saber um pouco de programação, mesmo que seja apenas para manter uma boa segurança operacional durante todo este processo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quanto tempo você tem para isso? Nosso conselho seria começar pequeno e fazer projetos maiores à medida que você se acostuma, mas pode se tornar algo que consome todo o seu tempo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Qual seria uma área de alto impacto para focar? Se você vai gastar X horas em arquivamento pirata, como pode obter o maior \"retorno pelo seu investimento\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quais são as formas únicas que está a considerar sobre isto? Pode ter algumas ideias ou abordagens interessantes que outros possam ter perdido."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "No nosso caso, nos importávamos particularmente com a preservação a longo prazo da ciência. Conhecíamos o Library Genesis, e como ele era totalmente espelhado muitas vezes usando torrents. Adorávamos essa ideia. Então, um dia, um de nós tentou encontrar alguns livros didáticos científicos no Library Genesis, mas não conseguiu encontrá-los, levantando dúvidas sobre quão completo ele realmente era. Procuramos então esses livros didáticos online e os encontramos em outros lugares, o que plantou a semente para o nosso projeto. Mesmo antes de conhecermos o Z-Library, tivemos a ideia de não tentar coletar todos esses livros manualmente, mas de nos concentrar em espelhar coleções existentes e contribuir com elas de volta para o Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Seleção de alvo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Então, temos nossa área de interesse, agora qual coleção específica vamos espelhar? Há algumas coisas que fazem um bom alvo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Único: não já bem coberto por outros projetos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Acessível: não usa toneladas de camadas de proteção para impedir que você raspe seus metadata e dados."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Insight especial: você tem alguma informação especial sobre este alvo, como ter acesso especial a esta coleção, ou descobriu como derrotar suas defesas. Isso não é necessário (nosso próximo projeto não faz nada especial), mas certamente ajuda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quando encontramos nossos livros didáticos de ciência em sites além do Library Genesis, tentamos descobrir como eles chegaram à internet. Então encontramos o Z-Library e percebemos que, embora a maioria dos livros não apareça primeiro lá, eles acabam chegando lá. Aprendemos sobre sua relação com o Library Genesis e a estrutura de incentivos (financeiros) e interface de usuário superior, ambos os quais o tornaram uma coleção muito mais completa. Fizemos então algumas raspagens preliminares de metadata e dados, e percebemos que poderíamos contornar seus limites de download de IP, aproveitando o acesso especial de um de nossos membros a muitos servidores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Enquanto você explora diferentes alvos, já é importante esconder seus rastros usando VPNs e endereços de e-mail descartáveis, sobre os quais falaremos mais tarde."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Raspagem de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Vamos ser um pouco mais técnicos aqui. Para realmente raspar os metadata de sites, mantivemos as coisas bem simples. Usamos scripts em Python, às vezes curl, e um banco de dados MySQL para armazenar os resultados. Não usamos nenhum software de raspagem sofisticado que possa mapear sites complexos, já que até agora só precisávamos raspar um ou dois tipos de páginas apenas enumerando através de ids e analisando o HTML. Se não houver páginas facilmente enumeradas, então você pode precisar de um rastreador adequado que tente encontrar todas as páginas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antes de começar a fazer scraping de um site inteiro, experimente fazê-lo manualmente por um tempo. Navegue por algumas dezenas de páginas você mesmo, para ter uma noção de como isso funciona. Às vezes, você já encontrará bloqueios de IP ou outros comportamentos interessantes dessa forma. O mesmo vale para a extração de dados: antes de se aprofundar muito nesse alvo, certifique-se de que pode realmente baixar seus dados de forma eficaz."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Para contornar restrições, há algumas coisas que pode tentar. Existem outros endereços IP ou servidores que hospedam os mesmos dados, mas sem as mesmas restrições? Existem endpoints de API que não têm restrições, enquanto outros têm? A que taxa de download seu IP é bloqueado e por quanto tempo? Ou você não é bloqueado, mas tem a velocidade reduzida? E se criar uma conta de usuário, como as coisas mudam? Pode usar HTTP/2 para manter as conexões abertas, e isso aumenta a taxa com que pode solicitar páginas? Existem páginas que listam vários arquivos de uma vez, e as informações listadas lá são suficientes?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Coisas que provavelmente deseja salvar incluem:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Título"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome do arquivo / localização"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pode ser algum ID interno, mas IDs como ISBN ou DOI também são úteis."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Tamanho: para calcular quanto espaço em disco precisa."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): para confirmar que baixou o arquivo corretamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data de adição/modificação: para que possa voltar mais tarde e baixar arquivos que não baixou antes (embora muitas vezes também possa usar o ID ou hash para isso)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrição, categoria, etiquetas, autores, idioma, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Normalmente fazemos isso em duas etapas. Primeiro, baixamos os arquivos HTML brutos, geralmente diretamente para o MySQL (para evitar muitos arquivos pequenos, sobre os quais falamos mais abaixo). Depois, em uma etapa separada, analisamos esses arquivos HTML e os transformamos em tabelas MySQL reais. Desta forma, não precisa baixar tudo novamente do zero se descobrir um erro no seu código de análise, pois pode simplesmente reprocessar os arquivos HTML com o novo código. Também é frequentemente mais fácil paralelizar a etapa de processamento, economizando assim algum tempo (e pode escrever o código de processamento enquanto o scraping está em execução, em vez de ter que escrever ambas as etapas de uma vez)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Por fim, note que para alguns alvos, a extração de metadata é tudo o que há. Existem algumas coleções enormes de metadata por aí que não estão devidamente preservadas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Seleção de dados"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Muitas vezes, pode usar a metadata para determinar um subconjunto razoável de dados para baixar. Mesmo que eventualmente queira baixar todos os dados, pode ser útil priorizar os itens mais importantes primeiro, caso seja detectado e as defesas sejam melhoradas, ou porque precisaria comprar mais discos, ou simplesmente porque algo mais surge na sua vida antes de conseguir baixar tudo."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Por exemplo, uma coleção pode ter várias edições do mesmo recurso subjacente (como um livro ou um filme), onde uma é marcada como sendo de melhor qualidade. Salvar essas edições primeiro faria muito sentido. Pode eventualmente querer salvar todas as edições, já que em alguns casos a metadata pode estar incorretamente etiquetada, ou pode haver compensações desconhecidas entre edições (por exemplo, a \"melhor edição\" pode ser a melhor na maioria dos aspectos, mas pior em outros, como um filme ter uma resolução mais alta, mas sem legendas)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Também pode pesquisar no seu banco de dados de metadata para encontrar coisas interessantes. Qual é o maior arquivo hospedado e por que é tão grande? Qual é o menor arquivo? Existem padrões interessantes ou inesperados quando se trata de certas categorias, idiomas, e assim por diante? Existem títulos duplicados ou muito semelhantes? Existem padrões de quando os dados foram adicionados, como um dia em que muitos arquivos foram adicionados de uma vez? Muitas vezes, pode aprender muito observando o conjunto de dados de diferentes maneiras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "No nosso caso, deduplicamos livros da Z-Library contra os hashes md5 na Library Genesis, economizando assim muito tempo de download e espaço em disco. Esta é uma situação bastante única, no entanto. Na maioria dos casos, não existem bancos de dados abrangentes de quais arquivos já estão devidamente preservados por outros piratas. Isso em si é uma grande oportunidade para alguém por aí. Seria ótimo ter uma visão geral regularmente atualizada de coisas como música e filmes que já estão amplamente semeados em sites de torrent, e são, portanto, de menor prioridade para incluir em mirrors piratas."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extração de dados"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Agora está pronto para realmente baixar os dados em massa. Como mencionado antes, neste ponto já deve ter baixado manualmente um monte de arquivos, para entender melhor o comportamento e as restrições do alvo. No entanto, ainda haverá surpresas reservadas para você quando realmente começar a baixar muitos arquivos de uma vez."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "O nosso conselho aqui é principalmente manter as coisas simples. Comece por descarregar um conjunto de ficheiros. Pode usar Python e depois expandir para múltiplas threads. Mas, por vezes, é ainda mais simples gerar ficheiros Bash diretamente a partir da base de dados e depois executar vários deles em múltiplas janelas de terminal para aumentar a escala. Um truque técnico rápido que vale a pena mencionar aqui é usar OUTFILE no MySQL, que pode escrever em qualquer lugar se desativar \"secure_file_priv\" no mysqld.cnf (e certifique-se também de desativar/contornar o AppArmor se estiver no Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Armazenamos os dados em discos rígidos simples. Comece com o que tiver e expanda lentamente. Pode ser avassalador pensar em armazenar centenas de TBs de dados. Se essa é a situação que está a enfrentar, comece por disponibilizar um bom subconjunto e, no seu anúncio, peça ajuda para armazenar o resto. Se quiser adquirir mais discos rígidos, o r/DataHoarder tem alguns bons recursos para encontrar boas ofertas."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Tente não se preocupar demasiado com sistemas de ficheiros sofisticados. É fácil cair na armadilha de configurar coisas como o ZFS. Um detalhe técnico a ter em conta, no entanto, é que muitos sistemas de ficheiros não lidam bem com muitos ficheiros. Descobrimos que uma solução simples é criar múltiplos diretórios, por exemplo, para diferentes intervalos de ID ou prefixos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Depois de descarregar os dados, certifique-se de verificar a integridade dos ficheiros usando hashes na metadata, se disponível."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuição"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Tem os dados, o que lhe dá a posse do primeiro mirror pirata do mundo do seu alvo (provavelmente). De muitas maneiras, a parte mais difícil já passou, mas a parte mais arriscada ainda está à sua frente. Afinal, até agora tem sido discreto; voando sob o radar. Tudo o que teve de fazer foi usar uma boa VPN durante todo o processo, não preencher os seus dados pessoais em nenhum formulário (óbvio), e talvez usar uma sessão de navegador especial (ou até mesmo um computador diferente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Agora tem de distribuir os dados. No nosso caso, primeiro quisemos contribuir com os livros de volta para a Library Genesis, mas rapidamente descobrimos as dificuldades nisso (classificação de ficção vs não-ficção). Então decidimos pela distribuição usando torrents ao estilo da Library Genesis. Se tiver a oportunidade de contribuir para um projeto existente, isso pode poupar-lhe muito tempo. No entanto, atualmente não existem muitos mirrors piratas bem organizados."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Então, digamos que decida distribuir torrents você mesmo. Tente manter esses arquivos pequenos, para que sejam fáceis de espelhar em outros sites. Terá então que semear os torrents você mesmo, enquanto permanece anônimo. Pode usar uma VPN (com ou sem encaminhamento de porta), ou pagar com Bitcoins misturados por um Seedbox. Se não souber o que alguns desses termos significam, terá muito o que ler, pois é importante que entenda os riscos envolvidos aqui."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Pode hospedar os ficheiros torrent em sites de torrents existentes. No nosso caso, decidimos realmente hospedar um site, pois também queríamos espalhar a nossa filosofia de forma clara. Pode fazer isso por si mesmo de maneira semelhante (usamos Njalla para os nossos domínios e hospedagem, pagos com Bitcoins misturados), mas também sinta-se à vontade para nos contactar para que possamos hospedar os seus torrents. Estamos a procurar construir um índice abrangente de espelhos piratas ao longo do tempo, se esta ideia pegar."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Quanto à seleção de VPN, já se escreveu muito sobre isso, por isso vamos apenas repetir o conselho geral de escolher pela reputação. Políticas de não-registo testadas em tribunal com longos históricos de proteção da privacidade são a opção de menor risco, na nossa opinião. Note que, mesmo quando faz tudo certo, nunca pode chegar a um risco zero. Por exemplo, ao semear os seus torrents, um ator estatal altamente motivado pode provavelmente observar os fluxos de dados de entrada e saída dos servidores VPN e deduzir quem é. Ou pode simplesmente cometer um erro. Provavelmente já o fizemos, e voltaremos a fazê-lo. Felizmente, os estados-nação não se importam <em>tanto</em> com a pirataria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Uma decisão a tomar para cada projeto é se deve publicá-lo usando a mesma identidade de antes ou não. Se continuar a usar o mesmo nome, então erros na segurança operacional de projetos anteriores podem voltar para o assombrar. Mas publicar sob nomes diferentes significa que não constrói uma reputação duradoura. Escolhemos ter uma forte segurança operacional desde o início para podermos continuar a usar a mesma identidade, mas não hesitaremos em publicar sob um nome diferente se cometermos um erro ou se as circunstâncias o exigirem."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Divulgar a informação pode ser complicado. Como dissemos, esta ainda é uma comunidade de nicho. Originalmente publicámos no Reddit, mas realmente ganhámos tração no Hacker News. Por agora, a nossa recomendação é publicá-lo em alguns lugares e ver o que acontece. E novamente, contacte-nos. Adoraríamos espalhar a palavra sobre mais esforços de arquivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusão"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Esperamos que isto seja útil para os novos arquivistas piratas que estão a começar. Estamos entusiasmados por recebê-lo neste mundo, por isso não hesite em entrar em contacto. Vamos preservar o máximo possível do conhecimento e cultura do mundo, e espelhá-lo por toda a parte."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Apresentando o Mirror da Biblioteca Pirata: Preservando 7TB de livros (que não estão no Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Este projeto (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>) visa contribuir para a preservação e libertação do conhecimento humano. Fazemos a nossa pequena e humilde contribuição, seguindo os passos dos grandes que vieram antes de nós."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "O foco deste projeto é ilustrado pelo seu nome:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Violamos deliberadamente a lei de direitos de autor na maioria dos países. Isso permite-nos fazer algo que entidades legais não podem fazer: garantir que os livros são espelhados por toda a parte."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Como a maioria das bibliotecas, focamo-nos principalmente em materiais escritos como livros. Podemos expandir para outros tipos de media no futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Somos estritamente um mirror de bibliotecas existentes. Focamo-nos na preservação, não em tornar os livros facilmente pesquisáveis e descarregáveis (acesso) ou em fomentar uma grande comunidade de pessoas que contribuem com novos livros (origem)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "A primeira biblioteca que espelhámos foi a Z-Library. Esta é uma biblioteca popular (e ilegal). Eles pegaram na coleção da Library Genesis e tornaram-na facilmente pesquisável. Além disso, tornaram-se muito eficazes em solicitar novas contribuições de livros, incentivando os utilizadores contribuintes com várias vantagens. Atualmente, não contribuem com esses novos livros de volta para a Library Genesis. E, ao contrário da Library Genesis, não tornam a sua coleção facilmente espelhável, o que impede uma ampla preservação. Isto é importante para o seu modelo de negócio, uma vez que cobram dinheiro pelo acesso à sua coleção em massa (mais de 10 livros por dia)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegal. É inegável que a Z-Library tem sido bem-sucedida em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer a nossa parte: garantir a preservação a longo prazo desta coleção privada."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Gostaríamos de convidá-lo a ajudar a preservar e libertar o conhecimento humano, descarregando e semeando os nossos torrents. Veja a página do projeto para mais informações sobre como os dados estão organizados."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Gostaríamos também de convidá-lo a contribuir com as suas ideias sobre quais coleções espelhar a seguir e como fazê-lo. Juntos, podemos alcançar muito. Esta é apenas uma pequena contribuição entre inúmeras outras. Obrigado por tudo o que faz."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Não ligamos para os ficheiros deste blog. Por favor, encontre-os você mesmo.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump do ISBNdb, ou Quantos Livros Estão Preservados Para Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se fôssemos deduplicar corretamente os ficheiros das bibliotecas sombra, que percentagem de todos os livros do mundo teríamos preservado?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Com o Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>), o nosso objetivo é pegar em todos os livros do mundo e preservá-los para sempre.<sup>1</sup> Entre os nossos torrents da Z-Library e os torrents originais da Library Genesis, temos 11.783.153 ficheiros. Mas quantos são realmente? Se deduplicássemos corretamente esses ficheiros, que percentagem de todos os livros do mundo teríamos preservado? Gostaríamos muito de ter algo assim:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of do património escrito da humanidade preservado para sempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Para uma percentagem, precisamos de um denominador: o número total de livros já publicados.<sup>2</sup> Antes do fim do Google Books, um engenheiro do projeto, Leonid Taycher, <a %(booksearch_blogspot)s>tentou estimar</a> este número. Ele chegou — em tom de brincadeira — a 129.864.880 (“pelo menos até domingo”). Ele estimou este número construindo uma base de dados unificada de todos os livros do mundo. Para isso, reuniu diferentes datasets e depois os fundiu de várias maneiras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como uma breve nota, há outra pessoa que tentou catalogar todos os livros do mundo: Aaron Swartz, o falecido ativista digital e cofundador do Reddit.<sup>3</sup> Ele <a %(youtube)s>começou a Open Library</a> com o objetivo de “uma página web para cada livro já publicado”, combinando dados de várias fontes diferentes. Ele acabou pagando o preço máximo pelo seu trabalho de preservação digital quando foi processado por descarregar em massa artigos académicos, levando ao seu suicídio. Escusado será dizer que esta é uma das razões pelas quais o nosso grupo é pseudónimo e por que estamos a ser muito cuidadosos. A Open Library ainda está a ser heroicamente gerida por pessoas do Internet Archive, continuando o legado de Aaron. Voltaremos a isto mais tarde neste post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "No post do blog do Google, Taycher descreve alguns dos desafios com a estimativa deste número. Primeiro, o que constitui um livro? Existem algumas definições possíveis:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Cópias físicas.</strong> Obviamente, isto não é muito útil, pois são apenas duplicados do mesmo material. Seria interessante se pudéssemos preservar todas as anotações que as pessoas fazem nos livros, como os famosos “rabiscos nas margens” de Fermat. Mas, infelizmente, isso permanecerá um sonho de arquivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obras”.</strong> Por exemplo, “Harry Potter e a Câmara dos Segredos” como um conceito lógico, englobando todas as suas versões, como diferentes traduções e reimpressões. Esta é uma definição útil, mas pode ser difícil traçar a linha do que conta. Por exemplo, provavelmente queremos preservar diferentes traduções, embora reimpressões com apenas pequenas diferenças possam não ser tão importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edições”.</strong> Aqui conta-se cada versão única de um livro. Se algo for diferente, como uma capa diferente ou um prefácio diferente, conta como uma edição diferente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Ficheiros.</strong> Ao trabalhar com bibliotecas sombra como a Library Genesis, Sci-Hub ou Z-Library, há uma consideração adicional. Pode haver múltiplas digitalizações da mesma edição. E as pessoas podem fazer melhores versões de ficheiros existentes, digitalizando o texto usando OCR ou retificando páginas que foram digitalizadas em ângulo. Queremos contar esses ficheiros apenas como uma edição, o que exigiria uma boa metadata ou deduplicação usando medidas de similaridade de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edições” parecem ser a definição mais prática do que são “livros”. Convenientemente, esta definição também é usada para atribuir números ISBN únicos. Um ISBN, ou Número Internacional Normalizado do Livro, é comumente usado para o comércio internacional, pois está integrado com o sistema internacional de códigos de barras (“Número Internacional de Artigo”). Se quiser vender um livro em lojas, ele precisa de um código de barras, então obtém um ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "O post do blog de Taycher menciona que, embora os ISBNs sejam úteis, não são universais, pois só foram realmente adotados em meados dos anos setenta e não em todo o mundo. Ainda assim, o ISBN é provavelmente o identificador mais amplamente usado para edições de livros, por isso é o nosso melhor ponto de partida. Se conseguirmos encontrar todos os ISBNs do mundo, obtemos uma lista útil de quais livros ainda precisam ser preservados."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Então, onde obtemos os dados? Existem vários esforços existentes que estão a tentar compilar uma lista de todos os livros do mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Afinal, eles fizeram esta pesquisa para o Google Books. No entanto, a sua metadata não está acessível em massa e é bastante difícil de extrair."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como mencionado anteriormente, esta é a sua missão inteira. Eles obtiveram enormes quantidades de dados de bibliotecas cooperantes e arquivos nacionais, e continuam a fazê-lo. Eles também têm bibliotecários voluntários e uma equipa técnica que estão a tentar deduplicar registos e etiquetá-los com todos os tipos de metadata. O melhor de tudo é que o seu conjunto de dados é completamente aberto. Pode simplesmente <a %(openlibrary)s>descarregá-lo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Este é um site gerido pela organização sem fins lucrativos OCLC, que vende sistemas de gestão de bibliotecas. Eles agregam metadata de livros de muitas bibliotecas e disponibilizam-na através do site WorldCat. No entanto, eles também ganham dinheiro vendendo esses dados, por isso não estão disponíveis para download em massa. Eles têm alguns conjuntos de dados em massa mais limitados disponíveis para download, em cooperação com bibliotecas específicas."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este é o tema deste post no blog. O ISBNdb extrai dados de vários sites para obter metadata de livros, em particular dados de preços, que depois vendem a livreiros, para que possam precificar os seus livros de acordo com o resto do mercado. Como os ISBNs são bastante universais hoje em dia, eles efetivamente construíram uma “página web para cada livro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Vários sistemas de bibliotecas individuais e arquivos.</strong> Existem bibliotecas e arquivos que não foram indexados e agregados por nenhum dos mencionados acima, muitas vezes porque são subfinanciados, ou por outras razões não querem compartilhar os seus dados com a Open Library, OCLC, Google, etc. Muitos destes têm registos digitais acessíveis através da internet, e muitas vezes não estão muito bem protegidos, por isso, se quiser ajudar e divertir-se a aprender sobre sistemas de bibliotecas peculiares, estes são ótimos pontos de partida."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Neste post, estamos felizes em anunciar um pequeno lançamento (comparado aos nossos lançamentos anteriores da Z-Library). Extraímos a maior parte do ISBNdb e disponibilizámos os dados para torrent no site do Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>; não vamos ligar diretamente aqui, basta procurar). São cerca de 30,9 milhões de registos (20GB como <a %(jsonlines)s>JSON Lines</a>; 4,4GB comprimidos). No site deles, afirmam que têm, na verdade, 32,6 milhões de registos, por isso, talvez tenhamos perdido alguns, ou <em>eles</em> podem estar a fazer algo errado. Em qualquer caso, por agora não vamos compartilhar exatamente como fizemos — deixaremos isso como um exercício para o leitor. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "O que vamos compartilhar é uma análise preliminar, para tentar nos aproximar da estimativa do número de livros no mundo. Analisámos três datasets: este novo dataset ISBNdb, a nossa versão original de metadata que extraímos da Z-Library shadow library (que inclui a Library Genesis), e o dump de dados da Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Vamos começar com alguns números aproximados:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Tanto na Z-Library/Libgen quanto na Open Library há muitos mais livros do que ISBNs únicos. Isso significa que muitos desses livros não têm ISBNs, ou a metadata do ISBN está simplesmente em falta? Provavelmente podemos responder a esta pergunta com uma combinação de correspondência automatizada baseada em outros atributos (título, autor, editor, etc.), integrando mais fontes de dados e extraindo ISBNs dos próprios scans dos livros (no caso da Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quantos desses ISBNs são únicos? Isso é melhor ilustrado com um diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Para ser mais preciso:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Ficámos surpreendidos com o quão pouco sobreposição existe! O ISBNdb tem uma enorme quantidade de ISBNs que não aparecem nem na Z-Library nem na Open Library, e o mesmo acontece (em menor, mas ainda substancial grau) com as outras duas. Isso levanta muitas novas questões. Quanto ajudaria a correspondência automatizada na etiquetagem dos livros que não foram etiquetados com ISBNs? Haveria muitas correspondências e, portanto, aumento da sobreposição? Além disso, o que aconteceria se trouxermos um 4º ou 5º conjunto de dados? Quanta sobreposição veríamos então?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Isso dá-nos um ponto de partida. Agora podemos olhar para todos os ISBNs que não estavam no conjunto de dados da Z-Library e que também não correspondem aos campos de título/autor. Isso pode dar-nos uma forma de preservar todos os livros do mundo: primeiro extraindo da internet scans, depois saindo na vida real para digitalizar livros. Este último poderia até ser financiado coletivamente, ou impulsionado por “recompensas” de pessoas que gostariam de ver determinados livros digitalizados. Tudo isso é uma história para outro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se quiser ajudar com qualquer uma destas tarefas — análise adicional; extração de mais metadata; encontrar mais livros; OCR de livros; fazer isso para outros domínios (por exemplo, artigos, audiolivros, filmes, programas de TV, revistas) ou até mesmo disponibilizar alguns desses dados para coisas como ML / treino de modelos de linguagem de grande escala — por favor, entre em contato comigo (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se está especificamente interessado na análise de dados, estamos a trabalhar para tornar os nossos conjuntos de dados e scripts disponíveis num formato mais fácil de usar. Seria ótimo se pudesse simplesmente fazer um fork de um notebook e começar a explorar isso."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, se quiser apoiar este trabalho, por favor, considere fazer uma doação. Esta é uma operação totalmente gerida por voluntários, e a sua contribuição faz uma enorme diferença. Toda ajuda conta. Por agora, aceitamos doações em criptomoeda; veja a página de Doações no Arquivo da Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Para alguma definição razoável de \"para sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Claro, o património escrito da humanidade é muito mais do que livros, especialmente hoje em dia. Para o propósito deste post e dos nossos lançamentos recentes, estamos a focar-nos em livros, mas os nossos interesses vão além."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Há muito mais que pode ser dito sobre Aaron Swartz, mas só queríamos mencioná-lo brevemente, já que ele desempenha um papel crucial nesta história. À medida que o tempo passa, mais pessoas podem encontrar o seu nome pela primeira vez e, posteriormente, mergulhar no buraco do coelho por si mesmas."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "A janela crítica das bibliotecas sombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Como podemos afirmar que preservamos as nossas coleções para sempre, quando já estão a aproximar-se de 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versão chinesa 中文版</a>, discuta no <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "No Arquivo da Anna, somos frequentemente questionados sobre como podemos afirmar que preservamos as nossas coleções para sempre, quando o tamanho total já está a aproximar-se de 1 Petabyte (1000 TB) e continua a crescer. Neste artigo, vamos olhar para a nossa filosofia e ver por que a próxima década é crítica para a nossa missão de preservar o conhecimento e a cultura da humanidade."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "O <a %(annas_archive_stats)s>tamanho total</a> das nossas coleções, nos últimos meses, dividido pelo número de seeders de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioridades"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Por que nos importamos tanto com artigos e livros? Vamos deixar de lado nossa crença fundamental na preservação em geral — talvez escrevamos outro post sobre isso. Então, por que especificamente artigos e livros? A resposta é simples: <strong>densidade de informação</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabyte de armazenamento, o texto escrito armazena mais informações do que qualquer outro meio. Embora nos importemos tanto com conhecimento quanto com cultura, nos importamos mais com o primeiro. No geral, encontramos uma hierarquia de densidade de informação e importância da preservação que se parece mais ou menos com isto:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Artigos acadêmicos, revistas, relatórios"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dados orgânicos como sequências de DNA, sementes de plantas ou amostras microbianas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Livros de não-ficção"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Código de software de ciência e engenharia"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dados de medição como medições científicas, dados económicos, relatórios corporativos"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sites de ciência e engenharia, discussões online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistas de não-ficção, jornais, manuais"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcrições de não-ficção de palestras, documentários, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dados internos de empresas ou governos (vazamentos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Registos de metadata em geral (de não-ficção e ficção; de outros meios, arte, pessoas, etc.; incluindo críticas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dados geográficos (por exemplo, mapas, levantamentos geológicos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcrições de processos legais ou judiciais"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versões ficcionais ou de entretenimento de todos os itens acima"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "A classificação nesta lista é um tanto arbitrária — vários itens estão empatados ou têm desacordos dentro da nossa equipa — e provavelmente estamos a esquecer algumas categorias importantes. Mas é mais ou menos assim que priorizamos."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Alguns destes itens são demasiado diferentes dos outros para nos preocuparmos (ou já são tratados por outras instituições), como dados orgânicos ou dados geográficos. Mas a maioria dos itens nesta lista são realmente importantes para nós."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Outro grande fator na nossa priorização é o quão em risco está uma determinada obra. Preferimos focar-nos em obras que são:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Raras"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamente subfocado"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamente em risco de destruição (por exemplo, por guerra, cortes de financiamento, processos judiciais ou perseguição política)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, preocupamo-nos com a escala. Temos tempo e dinheiro limitados, por isso preferimos passar um mês a salvar 10.000 livros do que 1.000 livros — se forem igualmente valiosos e estiverem em risco."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliotecas sombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Existem muitas organizações com missões e prioridades semelhantes. De fato, há bibliotecas, arquivos, laboratórios, museus e outras instituições encarregadas da preservação deste tipo. Muitas delas são bem financiadas, por governos, indivíduos ou empresas. Mas todas têm um ponto cego massivo: o sistema legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Aqui reside o papel único das shadow libraries e a razão pela qual o Arquivo da Anna existe. Podemos fazer coisas que outras instituições não têm permissão para fazer. Agora, não é (frequentemente) que possamos arquivar materiais que são ilegais de preservar em outros lugares. Não, é legal em muitos lugares construir um arquivo com quaisquer livros, artigos, revistas, e assim por diante."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Mas o que os arquivos legais muitas vezes carecem é de <strong>redundância e longevidade</strong>. Existem livros dos quais apenas uma cópia existe em alguma biblioteca física em algum lugar. Existem registros de metadata guardados por uma única empresa. Existem jornais preservados apenas em microfilme em um único arquivo. Bibliotecas podem sofrer cortes de financiamento, empresas podem falir, arquivos podem ser bombardeados e queimados até o chão. Isso não é hipotético — isso acontece o tempo todo."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "A coisa que podemos fazer de forma única no Arquivo da Anna é armazenar muitas cópias de obras, em grande escala. Podemos coletar artigos, livros, revistas e mais, e distribuí-los em massa. Atualmente, fazemos isso através de torrents, mas as tecnologias exatas não importam e mudarão com o tempo. A parte importante é distribuir muitas cópias pelo mundo. Esta citação de mais de 200 anos atrás ainda soa verdadeira:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>O que está perdido não pode ser recuperado; mas vamos salvar o que resta: não por cofres e fechaduras que os afastam do olhar e uso do público, consignando-os ao desperdício do tempo, mas por uma multiplicação de cópias, que os coloque além do alcance do acidente.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Uma nota rápida sobre domínio público. Como o Arquivo da Anna foca exclusivamente em atividades que são ilegais em muitos lugares ao redor do mundo, não nos preocupamos com coleções amplamente disponíveis, como livros de domínio público. Entidades legais geralmente já cuidam bem disso. No entanto, há considerações que às vezes nos fazem trabalhar em coleções publicamente disponíveis:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Registros de metadata podem ser visualizados livremente no site Worldcat, mas não baixados em massa (até que nós <a %(worldcat_scrape)s>os raspamos</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "O código pode ser de código aberto no Github, mas o Github como um todo não pode ser facilmente espelhado e, assim, preservado (embora, neste caso particular, existam cópias suficientemente distribuídas da maioria dos repositórios de código)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "O Reddit é gratuito para usar, mas recentemente implementou medidas rigorosas contra raspagem, na esteira do treinamento de LLM faminto por dados (mais sobre isso depois)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Uma multiplicação de cópias"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Voltando à nossa pergunta original: como podemos afirmar que preservamos nossas coleções para sempre? O principal problema aqui é que nossa coleção tem <a %(torrents_stats)s>crescido</a> rapidamente, raspando e tornando algumas coleções massivas de código aberto (além do trabalho incrível já feito por outras bibliotecas de dados abertos como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Esse crescimento de dados torna mais difícil que as coleções sejam espelhadas ao redor do mundo. O armazenamento de dados é caro! Mas estamos otimistas, especialmente ao observar as seguintes três tendências."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Colhemos os frutos mais fáceis"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Isso segue diretamente das nossas prioridades discutidas acima. Preferimos trabalhar na liberação de grandes coleções primeiro. Agora que garantimos algumas das maiores coleções do mundo, esperamos que nosso crescimento seja muito mais lento."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Ainda há uma longa cauda de coleções menores, e novos livros são digitalizados ou publicados todos os dias, mas a taxa provavelmente será muito mais lenta. Podemos ainda dobrar ou até triplicar de tamanho, mas ao longo de um período de tempo mais longo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Os custos de armazenamento continuam a cair exponencialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "No momento da escrita, <a %(diskprices)s>os preços dos discos</a> por TB estão em torno de $12 para discos novos, $8 para discos usados e $4 para fita. Se formos conservadores e considerarmos apenas discos novos, isso significa que armazenar um petabyte custa cerca de $12.000. Se assumirmos que a nossa biblioteca triplicará de 900TB para 2,7PB, isso significaria $32.400 para espelhar toda a nossa biblioteca. Adicionando eletricidade, custo de outros equipamentos, e assim por diante, vamos arredondar para $40.000. Ou com fita, mais como $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Por um lado, <strong>$15.000–$40.000 para a soma de todo o conhecimento humano é uma pechincha</strong>. Por outro lado, é um pouco caro esperar toneladas de cópias completas, especialmente se também quisermos que essas pessoas continuem a semear seus torrents para o benefício de outros."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Isso é hoje. Mas o progresso avança:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Os custos dos discos rígidos por TB foram reduzidos em cerca de um terço nos últimos 10 anos e provavelmente continuarão a cair a um ritmo semelhante. As fitas parecem estar em uma trajetória semelhante. Os preços dos SSDs estão caindo ainda mais rápido e podem superar os preços dos HDDs até o final da década."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendências de preços de HDD de diferentes fontes (clique para ver o estudo)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se isso se mantiver, então em 10 anos podemos estar olhando para apenas $5.000–$13.000 para espelhar toda a nossa coleção (1/3), ou até menos se crescermos menos em tamanho. Embora ainda seja muito dinheiro, isso será acessível para muitas pessoas. E pode ser ainda melhor por causa do próximo ponto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Melhorias na densidade de informação"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Atualmente, armazenamos livros nos formatos brutos em que nos são entregues. Claro, eles são comprimidos, mas muitas vezes ainda são grandes digitalizações ou fotografias de páginas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Até agora, as únicas opções para reduzir o tamanho total da nossa coleção têm sido através de compressão mais agressiva ou deduplicação. No entanto, para obter economias significativas, ambas são muito perdidas para o nosso gosto. A compressão pesada de fotos pode tornar o texto mal legível. E a deduplicação requer alta confiança de que os livros são exatamente os mesmos, o que muitas vezes é muito impreciso, especialmente se os conteúdos forem os mesmos, mas as digitalizações forem feitas em ocasiões diferentes."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Sempre houve uma terceira opção, mas sua qualidade era tão abismal que nunca a consideramos: <strong>OCR, ou Reconhecimento Óptico de Caracteres</strong>. Este é o processo de converter fotos em texto simples, usando IA para detectar os caracteres nas fotos. Ferramentas para isso existem há muito tempo e têm sido bastante decentes, mas \"bastante decente\" não é suficiente para fins de preservação."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "No entanto, modelos recentes de aprendizado profundo multimodal têm feito progressos extremamente rápidos, embora ainda a custos elevados. Esperamos que tanto a precisão quanto os custos melhorem dramaticamente nos próximos anos, a ponto de se tornar realista aplicá-los a toda a nossa biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Melhorias no OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quando isso acontecer, provavelmente ainda preservaremos os arquivos originais, mas, além disso, poderíamos ter uma versão muito menor da nossa biblioteca que a maioria das pessoas desejará espelhar. O ponto chave é que o texto bruto em si se comprime ainda melhor e é muito mais fácil de deduplicar, proporcionando-nos ainda mais economias."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "No geral, não é irrealista esperar pelo menos uma redução de 5-10x no tamanho total dos arquivos, talvez até mais. Mesmo com uma redução conservadora de 5x, estaríamos olhando para <strong>$1.000–$3.000 em 10 anos, mesmo que nossa biblioteca triplique de tamanho</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Janela crítica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se essas previsões forem precisas, nós <strong>só precisamos esperar alguns anos</strong> antes que toda a nossa coleção seja amplamente espelhada. Assim, nas palavras de Thomas Jefferson, \"colocada além do alcance do acidente\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Infelizmente, o advento dos LLMs, e seu treinamento faminto por dados, colocou muitos detentores de direitos autorais na defensiva. Ainda mais do que já estavam. Muitos sites estão tornando mais difícil raspar e arquivar, processos judiciais estão voando por aí, e enquanto isso, bibliotecas e arquivos físicos continuam a ser negligenciados."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Só podemos esperar que essas tendências continuem a piorar, e muitas obras sejam perdidas bem antes de entrarem no domínio público."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Estamos à beira de uma revolução na preservação, mas <q>o perdido não pode ser recuperado.</q></strong> Temos uma janela crítica de cerca de 5-10 anos durante a qual ainda é bastante caro operar uma shadow library e criar muitos mirrors ao redor do mundo, e durante a qual o acesso ainda não foi completamente fechado."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se conseguirmos aproveitar esta janela, então teremos realmente preservado o conhecimento e a cultura da humanidade para sempre. Não devemos deixar que este tempo seja desperdiçado. Não devemos deixar que esta janela crítica se feche para nós."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Vamos lá."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Acesso exclusivo para empresas de LLM à maior coleção de livros de não-ficção chineses do mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versão chinesa 中文版</a>, <a %(news_ycombinator)s>Discutir no Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Resumo:</strong> O Arquivo da Anna adquiriu uma coleção única de 7,5 milhões / 350TB de livros de não-ficção chineses — maior que a Library Genesis. Estamos dispostos a dar a uma empresa de LLM acesso exclusivo, em troca de OCR de alta qualidade e extração de texto.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Este é um breve post no blog. Estamos à procura de alguma empresa ou instituição para nos ajudar com OCR e extração de texto para uma coleção massiva que adquirimos, em troca de acesso exclusivo antecipado. Após o período de embargo, iremos, claro, liberar toda a coleção."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Texto acadêmico de alta qualidade é extremamente útil para o treinamento de LLMs. Embora nossa coleção seja chinesa, isso deve ser ainda útil para o treinamento de LLMs em inglês: os modelos parecem codificar conceitos e conhecimento independentemente da língua de origem."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Para isso, o texto precisa ser extraído dos scans. O que o Arquivo da Anna ganha com isso? Pesquisa de texto completo dos livros para seus usuários."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Como nossos objetivos estão alinhados com os dos desenvolvedores de LLM, estamos à procura de um colaborador. Estamos dispostos a dar-lhe <strong>acesso exclusivo antecipado a esta coleção em massa por 1 ano</strong>, se puder fazer OCR e extração de texto adequados. Se estiver disposto a compartilhar todo o código do seu pipeline conosco, estaríamos dispostos a embargar a coleção por mais tempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Páginas de exemplo"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Para nos provar que tem um bom pipeline, aqui estão algumas páginas de exemplo para começar, de um livro sobre supercondutores. O seu pipeline deve lidar corretamente com matemática, tabelas, gráficos, notas de rodapé, e assim por diante."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envie as suas páginas processadas para o nosso e-mail. Se estiverem boas, enviaremos mais em privado, e esperamos que consiga executar rapidamente o seu pipeline nessas também. Uma vez satisfeitos, podemos fazer um acordo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Coleção"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Algumas informações adicionais sobre a coleção. <a %(duxiu)s>Duxiu</a> é uma base de dados massiva de livros digitalizados, criada pelo <a %(chaoxing)s>SuperStar Digital Library Group</a>. A maioria são livros acadêmicos, digitalizados para torná-los disponíveis digitalmente para universidades e bibliotecas. Para nosso público de língua inglesa, <a %(library_princeton)s>Princeton</a> e a <a %(guides_lib_uw)s>Universidade de Washington</a> têm boas visões gerais. Há também um excelente artigo que fornece mais contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (procure no Arquivo da Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Os livros do Duxiu têm sido pirateados há muito tempo na internet chinesa. Normalmente, são vendidos por menos de um dólar por revendedores. Eles são tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(github_duty_machine)s>aqui</a> e <a %(github_821_github_io)s>aqui</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Tínhamos isso no topo da nossa lista de tarefas, e alocamos vários meses de trabalho em tempo integral para isso. No entanto, recentemente, um voluntário incrível, maravilhoso e talentoso nos contatou, dizendo que já havia feito todo esse trabalho — a um grande custo. Eles compartilharam a coleção completa conosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente notável. Eles concordaram em pedir ajuda desta forma para que a coleção fosse submetida a OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "A coleção possui 7.543.702 arquivos. Isso é mais do que a Library Genesis de não-ficção (cerca de 5,3 milhões). O tamanho total dos arquivos é de cerca de 359TB (326TiB) em sua forma atual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Estamos abertos a outras propostas e ideias. Basta nos contatar. Confira o Arquivo da Anna para mais informações sobre nossas coleções, esforços de preservação e como você pode ajudar. Obrigado!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Arquivo da Anna quando possível, mas não vamos mais hospedá-lo nós mesmos, nem recomendamos que outros façam mirror usando IPFS. Por favor, veja nossa página de Torrents se quiser ajudar a preservar nossa coleção."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Ajude a semear a Z-Library no IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Como operar uma shadow library: operações no Arquivo da Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Não há <q>AWS para instituições de caridade sombrias,</q> então como gerimos o Arquivo da Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Eu administro o <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>, o maior motor de busca sem fins lucrativos e de código aberto do mundo para <a %(wikipedia_shadow_library)s>bibliotecas sombrias</a>, como Sci-Hub, Library Genesis e Z-Library. O nosso objetivo é tornar o conhecimento e a cultura facilmente acessíveis e, em última análise, construir uma comunidade de pessoas que, juntas, arquivem e preservem <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos os livros do mundo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Neste artigo, vou mostrar como gerimos este site e os desafios únicos que surgem ao operar um site com um status legal questionável, já que não existe um “AWS para instituições de caridade sombrias”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Confira também o artigo irmão <a %(blog_how_to_become_a_pirate_archivist)s>Como se tornar um arquivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Fichas de inovação"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Vamos começar com a nossa pilha tecnológica. É deliberadamente entediante. Usamos Flask, MariaDB e ElasticSearch. E é literalmente isso. A pesquisa é em grande parte um problema resolvido, e não pretendemos reinventá-la. Além disso, temos que gastar as nossas <a %(mcfunley)s>fichas de inovação</a> em outra coisa: não sermos derrubados pelas autoridades."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Então, quão legal ou ilegal é exatamente o Arquivo da Anna? Isso depende principalmente da jurisdição legal. A maioria dos países acredita em alguma forma de direitos autorais, o que significa que pessoas ou empresas recebem um monopólio exclusivo sobre certos tipos de obras por um determinado período de tempo. Como nota à parte, no Arquivo da Anna acreditamos que, embora existam alguns benefícios, no geral os direitos autorais são um prejuízo líquido para a sociedade — mas essa é uma história para outra ocasião."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Este monopólio exclusivo sobre certas obras significa que é ilegal para qualquer pessoa fora deste monopólio distribuir diretamente essas obras — incluindo nós. Mas o Arquivo da Anna é um motor de busca que não distribui diretamente essas obras (pelo menos não no nosso site na clearnet), então deveríamos estar bem, certo? Não exatamente. Em muitas jurisdições, não é apenas ilegal distribuir obras protegidas por direitos autorais, mas também vincular a locais que o façam. Um exemplo clássico disso é a lei DMCA dos Estados Unidos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Esse é o extremo mais rigoroso do espectro. No outro extremo do espectro, teoricamente, poderiam existir países sem leis de direitos autorais, mas esses realmente não existem. Praticamente todos os países têm algum tipo de lei de direitos autorais nos livros. A aplicação é uma história diferente. Existem muitos países com governos que não se preocupam em aplicar a lei de direitos autorais. Existem também países entre os dois extremos, que proíbem a distribuição de obras protegidas por direitos autorais, mas não proíbem a vinculação a tais obras."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Outra consideração é no nível da empresa. Se uma empresa opera em uma jurisdição que não se importa com direitos autorais, mas a própria empresa não está disposta a correr nenhum risco, então ela pode fechar seu site assim que alguém reclamar."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, uma grande consideração são os pagamentos. Como precisamos de permanecer anónimos, não podemos usar métodos de pagamento tradicionais. Isso deixa-nos com criptomoedas, e apenas um pequeno subconjunto de empresas as suporta (existem cartões de débito virtuais pagos por cripto, mas muitas vezes não são aceites)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arquitetura do sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Então, digamos que você encontrou algumas empresas dispostas a hospedar seu site sem derrubá-lo — vamos chamá-las de “provedores amantes da liberdade” 😄. Você rapidamente descobrirá que hospedar tudo com eles é bastante caro, então pode querer encontrar alguns “provedores baratos” e fazer a hospedagem real lá, usando os provedores amantes da liberdade como proxy. Se fizer isso corretamente, os provedores baratos nunca saberão o que você está hospedando e nunca receberão reclamações."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Com todos esses provedores, há o risco de eles derrubarem seu site de qualquer maneira, então você também precisa de redundância. Precisamos disso em todos os níveis da nossa pilha."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Uma empresa um tanto amante da liberdade que se colocou em uma posição interessante é a Cloudflare. Eles <a %(blog_cloudflare)s>argumentaram</a> que não são um provedor de hospedagem, mas uma utilidade, como um ISP. Portanto, não estão sujeitos a pedidos de remoção do DMCA ou outros, e encaminham quaisquer pedidos para o seu provedor de hospedagem real. Eles chegaram a ir a tribunal para proteger essa estrutura. Podemos, portanto, usá-los como outra camada de cache e proteção."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "A Cloudflare não aceita pagamentos anônimos, então só podemos usar o plano gratuito deles. Isso significa que não podemos usar os recursos de balanceamento de carga ou failover deles. Portanto, <a %(annas_archive_l255)s>implementamos isso nós mesmos</a> no nível do domínio. Ao carregar a página, o navegador verificará se o domínio atual ainda está disponível e, caso contrário, reescreverá todos os URLs para um domínio diferente. Como a Cloudflare armazena em cache muitas páginas, isso significa que um usuário pode acessar nosso domínio principal, mesmo que o servidor proxy esteja fora do ar, e então, no próximo clique, ser movido para outro domínio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ainda temos preocupações operacionais normais para lidar, como monitorar a saúde do servidor, registrar erros de backend e frontend, e assim por diante. Nossa arquitetura de failover permite mais robustez nesse aspecto também, por exemplo, executando um conjunto completamente diferente de servidores em um dos domínios. Podemos até executar versões mais antigas do código e dos datasets nesse domínio separado, caso um bug crítico na versão principal passe despercebido."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Também podemos nos proteger contra a Cloudflare se voltar contra nós, removendo-a de um dos domínios, como este domínio separado. Diferentes permutações dessas ideias são possíveis."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ferramentas"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vamos ver quais ferramentas usamos para realizar tudo isso. Isso está evoluindo muito à medida que enfrentamos novos problemas e encontramos novas soluções."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidor de aplicação: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestão de servidores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Desenvolvimento: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Alojamento estático Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Há algumas decisões sobre as quais temos ido e voltado. Uma delas é a comunicação entre servidores: costumávamos usar Wireguard para isso, mas descobrimos que ocasionalmente ele para de transmitir qualquer dado, ou só transmite dados em uma direção. Isso aconteceu com várias configurações diferentes de Wireguard que tentamos, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Também tentamos tunelar portas via SSH, usando autossh e sshuttle, mas encontramos <a %(github_sshuttle)s>problemas lá</a> (embora ainda não esteja claro para mim se o autossh sofre de problemas de TCP-sobre-TCP ou não — só me parece uma solução instável, mas talvez esteja tudo bem?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Em vez disso, voltamos para conexões diretas entre servidores, escondendo que um servidor está rodando em provedores baratos usando filtragem de IP com UFW. Isso tem a desvantagem de que o Docker não funciona bem com UFW, a menos que você use <code>network_mode: \"host\"</code>. Tudo isso é um pouco mais propenso a erros, porque você exporá seu servidor à internet com apenas uma pequena má configuração. Talvez devêssemos voltar para o autossh — feedback seria muito bem-vindo aqui."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Também temos ido e voltado entre Varnish e Nginx. Atualmente gostamos do Varnish, mas ele tem suas peculiaridades e arestas. O mesmo se aplica ao Checkmk: não o amamos, mas ele funciona por enquanto. O Weblate tem sido aceitável, mas não incrível — às vezes temo que ele perca meus dados sempre que tento sincronizá-lo com nosso repositório git. O Flask tem sido bom no geral, mas tem algumas peculiaridades estranhas que custaram muito tempo para depurar, como configurar domínios personalizados ou problemas com sua integração SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Até agora, as outras ferramentas têm sido ótimas: não temos reclamações sérias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Todos eles tiveram alguns problemas, mas nada muito sério ou que consuma muito tempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusão"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Tem sido uma experiência interessante aprender a configurar um motor de busca de shadow library robusto e resiliente. Há muitos mais detalhes para compartilhar em posts futuros, então me avise sobre o que você gostaria de saber mais!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como sempre, estamos à procura de doações para apoiar este trabalho, então não deixe de visitar a página de Doações no Arquivo da Anna. Também estamos à procura de outros tipos de apoio, como subsídios, patrocinadores de longo prazo, provedores de pagamento de alto risco, talvez até anúncios (de bom gosto!). E se você quiser contribuir com seu tempo e habilidades, estamos sempre à procura de desenvolvedores, tradutores, e assim por diante. Obrigado pelo seu interesse e apoio."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e a equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Olá, sou a Anna. Criei o <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>, a maior shadow library do mundo. Este é o meu blog pessoal, no qual eu e meus colegas escrevemos sobre pirataria, preservação digital e muito mais."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conecte-se comigo no <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Note que este site é apenas um blog. Apenas hospedamos nossas próprias palavras aqui. Nenhum torrent ou outros arquivos protegidos por direitos autorais são hospedados ou vinculados aqui."

#, fuzzy
msgid "blog.index.heading"
msgstr "Posts do blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Colocando 5.998.794 livros no IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Arquivo da Anna quando possível, mas não vamos mais hospedá-lo nós mesmos, nem recomendamos que outros façam mirror usando IPFS. Por favor, veja nossa página de Torrents se quiser ajudar a preservar nossa coleção."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> O Arquivo da Anna raspou todo o WorldCat (a maior coleção de metadata de bibliotecas do mundo) para fazer uma lista de tarefas de livros que precisam ser preservados.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Há um ano, nós <a %(blog)s>começamos</a> a responder a esta pergunta: <strong>Que porcentagem de livros foi preservada permanentemente por shadow libraries?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Uma vez que um livro entra em uma shadow library de dados abertos como a <a %(wikipedia_library_genesis)s>Library Genesis</a>, e agora o <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>, ele é espelhado em todo o mundo (através de torrents), preservando-o praticamente para sempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Para responder à questão de qual a percentagem de livros que foi preservada, precisamos saber o denominador: quantos livros existem no total? E, idealmente, não temos apenas um número, mas metadados reais. Assim, podemos não só compará-los com bibliotecas sombra, mas também <strong>criar uma lista de tarefas dos livros restantes para preservar!</strong> Podemos até começar a sonhar com um esforço colaborativo para percorrer esta lista de tarefas."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Raspámos <a %(wikipedia_isbndb_com)s>ISBNdb</a> e descarregámos o <a %(openlibrary)s>conjunto de dados da Open Library</a>, mas os resultados foram insatisfatórios. O principal problema foi que não havia muita sobreposição de ISBNs. Veja este diagrama de Venn do <a %(blog)s>nosso post no blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ficámos muito surpreendidos com a pouca sobreposição entre ISBNdb e Open Library, ambas as quais incluem dados de várias fontes, como raspagens da web e registos de bibliotecas. Se ambas fazem um bom trabalho em encontrar a maioria dos ISBNs por aí, os seus círculos certamente teriam uma sobreposição substancial, ou um seria um subconjunto do outro. Isso fez-nos questionar, quantos livros caem <em>completamente fora destes círculos</em>? Precisamos de uma base de dados maior."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Foi então que fixámos os nossos olhos na maior base de dados de livros do mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta é uma base de dados proprietária da organização sem fins lucrativos <a %(wikipedia_oclc)s>OCLC</a>, que agrega registos de metadados de bibliotecas de todo o mundo, em troca de dar a essas bibliotecas acesso ao conjunto de dados completo e de as fazer aparecer nos resultados de pesquisa dos utilizadores finais."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Embora a OCLC seja uma organização sem fins lucrativos, o seu modelo de negócio exige a proteção da sua base de dados. Bem, lamentamos dizer, amigos da OCLC, estamos a dar tudo de graça. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Ao longo do último ano, raspámos meticulosamente todos os registos do WorldCat. No início, tivemos um golpe de sorte. O WorldCat estava a lançar a sua completa reformulação do site (em agosto de 2022). Isto incluiu uma revisão substancial dos seus sistemas de backend, introduzindo muitas falhas de segurança. Aproveitámos imediatamente a oportunidade e conseguimos raspar centenas de milhões (!) de registos em poucos dias."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Reformulação do WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Depois disso, as falhas de segurança foram sendo corrigidas lentamente uma a uma, até que a última que encontramos foi corrigida há cerca de um mês. Nessa altura, já tínhamos praticamente todos os registos e estávamos apenas a procurar registos de qualidade ligeiramente superior. Por isso, sentimos que é hora de lançar!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Vamos ver algumas informações básicas sobre os dados:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contêineres do Arquivo da Anna (AAC)</a>, que são essencialmente <a %(jsonlines)s>JSON Lines</a> comprimidos com <a %(zstd)s>Zstandard</a>, além de algumas semânticas padronizadas. Estes contêineres envolvem vários tipos de registos, com base nas diferentes raspagens que implementámos."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dados"

msgid "dyn.buy_membership.error.unknown"
msgstr "Ocorreu um erro desconhecido. Por favor contact-nos através do email %(email)s com um screenshot."

msgid "dyn.buy_membership.error.minimum"
msgstr "Esta moeda tem um mínimo maior do que o normal. Por favor seleciona uma duração ou moeda diferentes."

msgid "dyn.buy_membership.error.try_again"
msgstr "Não foi possível concluir o pedido. Por favor tenta novamente dentro de alguns minutos, e, se continuar a acontecer, contacta-nos através do email %(email)s com um screenshot."

msgid "dyn.buy_membership.error.wait"
msgstr "Erro a processar pagamento. Por favor espera um pouco e tenta novamente. Se o problema persistir por mais do que 24 horas, por favor contacta-nos através do email %(email)s com um screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentário oculto"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema no ficheiro: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versão melhorada"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Deseja denunciar este utilizador por comportamento abusivo ou impróprio?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Denunciar abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso denunciado:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Denunciou este utilizador por abuso."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responder"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Por favor, <a %(a_login)s>inicie sessão</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Deixou um comentário. Pode demorar um minuto para aparecer."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Algo correu mal. Por favor, recarregue a página e tente novamente."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s páginas afetadas"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Não visível em Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Não visível em Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Não visível em Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcado como não-funcional no Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Não encontrado na Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcado como “spam” no Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcado como “ficheiro ruim” no Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nem todas as páginas puderam ser convertidas para PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "A execução do exiftool falhou neste arquivo"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Livro (desconhecido)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Livro (não-ficção)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Livro (ficção)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artigo científico"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentos de normas"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Banda desenhada"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Peça musical"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolivro"

msgid "common.md5_content_type_mapping.other"
msgstr "Outro"

msgid "common.access_types_mapping.aa_download"
msgstr "Download de servidor de parceiro"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Download externo"

msgid "common.access_types_mapping.external_borrow"
msgstr "Empréstimo externo"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Empréstimo externo (impressão desativada)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Explorar metadados"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Contido em torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinês"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads para AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Índice de eBooks EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadados checos"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Livros"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Estatal Russa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Título"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Editora"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edição"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Ano de publicação"

msgid "common.specific_search_fields.original_filename"
msgstr "Nome do ficheiro original"

msgid "common.specific_search_fields.description_comments"
msgstr "Descrição e metadados"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads de servidores de parceiros temporariamente indisponíveis para este ficheiro."

msgid "common.md5.servers.fast_partner"
msgstr "Servidor Rápido de Parceiros #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomendado)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sem verificação do navegador ou listas de espera)"

msgid "common.md5.servers.slow_partner"
msgstr "Servidor Lento de Parceiros #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(ligeiramente mais rápido, mas com lista de espera)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sem lista de espera, mas pode ser muito lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Não-ficção"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficção"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(carrega também em “GET” no topo)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(carrega em “Get” no topo)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "os seus anúncios são conhecidos por conter software malicioso, por isso use um bloqueador de anúncios ou não clique nos anúncios"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Os arquivos Nexus/STC podem ser pouco confiáveis para download)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library no Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requer o Navegador Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Pedir emprestado ao Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(impressão desativada, apenas para membros)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI associado pode não estar disponível no Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "coleção"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Downloads por torrent em volume"

msgid "page.md5.box.download.experts_only"
msgstr "(apenas especialistas)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Procurar o Anna's Archive por ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Procurar outras bases de dados por ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Procurar registo original no ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Procurar no Anna's Archive por Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Procurar registo original na Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Procurar no Anna's Archive por número OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Procurar registo original no WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Procurar no Anna's Archive pelo número SSID DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Procurar manualmente no DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Procurar no Anna's Archive pelo número CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Procurar registo original no CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Procurar no Anna's Archive pelo número DuXiu DXID"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Índice de eBooks EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(sem verificação de browser necessária)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadados checos %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Livros %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadados"

msgid "page.md5.box.descr_title"
msgstr "descrição"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nome de ficheiro alternativo"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Título alternativo"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autor alternativo"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editora alternativa"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edição alternativa"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extensão alternativa"

msgid "page.md5.box.metadata_comments_title"
msgstr "comentários nos metadados"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descrição alternativa"

msgid "page.md5.box.date_open_sourced_title"
msgstr "data de open source"

msgid "page.md5.header.scihub"
msgstr "Ficheiro Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Ficheiro Internet Archive Controlled Digital Lending “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Este é um registo de um ficheiro do Internet Archive, não é um ficheiro transferível. Podes pedir o livro emprestado (link abaixo) ou usar este URL para <a %(a_request)s solicitar um ficheiro</a>."

msgid "page.md5.header.consider_upload"
msgstr "Se tiveres este ficheiro e ainda não estiver disponível no Anna's Archive, considera <a %(a_request)s>fazer upload</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Registo de metadados ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Registo de metadados Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Registo de metadados OCLC (WorldCat) número %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Registo de metadados DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Registo de metadados CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Registo de metadados MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Registo de metadados Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Este é um registo de metadados, não é um ficheiro transferível. Podes usar este URL para <a %(a_request)s>solicitar um ficheiro</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadados do registo associado"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Melhorar metadados na Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Aviso: múltiplos registos associados:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Melhorar metadados"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Reportar qualidade do ficheiro"

msgid "page.search.results.download_time"
msgstr "Tempo de download"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Website:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Procurar no Anna's Archive por “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorador de Códigos:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Ver no Explorador de Códigos “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Ler mais…"

msgid "page.md5.tabs.downloads"
msgstr "Transferências (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Empréstimos (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Visualização de metadados (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentários (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listas (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Estatísticas (%(count)s)"

msgid "common.tech_details"
msgstr "Detalhes técnicos"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Este ficheiro pode ter problemas e foi apagado de uma biblioteca de origem.</span> Isto pode acontecer por pedido do dono dos direitos de autor, por haver uma melhor alternativa disponível ou mesmo pelo ficheiro em si ter algum problema. Podes transferi-lo na mesma mas recomendamos que procures um ficheiro alternativo. Mais detalhes:"

msgid "page.md5.box.download.better_file"
msgstr "Uma versão melhor deste ficheiro pode estar disponível em %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Se mesmo assim quiseres transferir este ficheiro, certifica-te que o abres em software atualizado e seguro."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Downloads rápidos"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Transferências rápidas</strong> Torna-te um <a %(a_membership)s>membro</a> para ajudar a preservação de livros, artigos e outros trabalhos. Como forma de gratidão, tens direito a transferências rápidas. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se você doar este mês, receberá <strong>o dobro</strong> do número de downloads rápidos."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Transferências rápidas</strong> Tens %(remaining)s restantes hoje. Obrigado por seres um membro! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Transferências rápidas</strong>Gastaste todas as transferências rápidas de hoje."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Transferências rápidas</strong> Transferiste este ficheiro recentemente. Os links continuam válidos por algum tempo."

msgid "page.md5.box.download.option"
msgstr "Opção #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(sem redirecionamento)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(abrir no visualizador)"

msgid "layout.index.header.banner.refer"
msgstr "Partilha com um amigo, tu e o teu amigo recebem %(percentage)s%% extra de transferências rápidas!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Saber mais…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Transferências lentas"

msgid "page.md5.box.download.trusted_partners"
msgstr "De parceiros de confiança."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mais informações na <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(pode ser necessária <a %(a_browser)s>verificação do browser</a> — transferências ilimitadas!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Após o download:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Abrir no nosso visualizador"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostrar downloads externos"

msgid "page.md5.box.download.header_external"
msgstr "Transferências externas"

msgid "page.md5.box.download.no_found"
msgstr "Não foram encontradas transferências."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Todas as opções de transferência têm o mesmo ficheiro e devem ser seguras. No entanto, tem sempre cuidado com transferências da internet, especialmente de sites externos ao Anna's Archive. Confirma que tens os teus dispositivos e software atualizados."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Para ficheiros grandes, recomendamos o uso de um gestor de downloads para evitar interrupções."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Gestores de downloads recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Vai precisar de um leitor de ebooks ou PDF para abrir o ficheiro, dependendo do formato do ficheiro."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Leitores de ebooks recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visualizador online do Arquivo da Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Use ferramentas online para converter entre formatos."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Ferramentas de conversão recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Pode enviar ficheiros PDF e EPUB para o seu Kindle ou Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ferramentas recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Enviar para Kindle” da Amazon"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "\"Enviar para Kobo/Kindle\" de djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Apoie autores e bibliotecas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Se gostar disto e puder, considere comprar o original ou apoiar diretamente os autores."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Se isto estiver disponível na sua biblioteca local, considere pedi-lo emprestado gratuitamente lá."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Qualidade do ficheiro"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Ajude a comunidade reportando a qualidade deste ficheiro! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Reportar problema no ficheiro (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Ótima qualidade do ficheiro (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Adicionar comentário (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "O que está errado com este ficheiro?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Por favor, utilize o <a %(a_copyright)s>formulário de reclamação de DMCA / Direitos de Autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Descreva o problema (obrigatório)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descrição do problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 de uma versão melhor deste ficheiro (se aplicável)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Preencha isto se houver outro ficheiro que corresponda de perto a este ficheiro (mesma edição, mesma extensão de ficheiro, se conseguir encontrar um), que as pessoas devem usar em vez deste ficheiro. Se souber de uma versão melhor deste ficheiro fora do Arquivo da Anna, por favor <a %(a_upload)s>carregue-a</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Pode obter o md5 a partir do URL, por exemplo."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Submeter relatório"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Aprenda a <a %(a_metadata)s>melhorar os metadados</a> deste ficheiro você mesmo."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Obrigado por enviar o seu relatório. Ele será mostrado nesta página, bem como revisto manualmente por Anna (até termos um sistema de moderação adequado)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Algo correu mal. Por favor, recarregue a página e tente novamente."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Se este ficheiro tiver ótima qualidade, pode discutir qualquer coisa sobre ele aqui! Se não, por favor use o botão “Reportar problema no ficheiro”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Adorei este livro!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Deixar comentário"

msgid "common.english_only"
msgstr "O texto seguinte continua em Inglês."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total de downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Um “MD5 do ficheiro” é um hash que é calculado a partir do conteúdo do ficheiro, e é razoavelmente único com base nesse conteúdo. Todas as bibliotecas sombra que indexámos aqui usam principalmente MD5s para identificar ficheiros."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Um ficheiro pode aparecer em várias bibliotecas sombra. Para informações sobre os vários datasets que compilámos, veja a <a %(a_datasets)s>página de Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Este é um ficheiro gerido pela biblioteca de <a %(a_ia)s>Empréstimo Digital Controlado da IA</a>, e indexado pelo Arquivo da Anna para pesquisa. Para informações sobre os vários datasets que compilámos, veja a <a %(a_datasets)s>página de Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Para informações sobre este ficheiro em particular, consulte o seu <a %(a_href)s>ficheiro JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema ao carregar esta página"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Por favor, atualize a página para tentar novamente. <a %(a_contact)s>Contacte-nos</a> se o problema persistir por várias horas."

msgid "page.md5.invalid.header"
msgstr "Não encontrado"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” não foi encontrado na nossa base de dados."

msgid "page.login.title"
msgstr "Iniciar sessão/Registar"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verificação do navegador"

msgid "page.login.text1"
msgstr "Para prevenir a criação de contas por bots, precisamos de verificar o teu browser primeiro."

#, fuzzy
msgid "page.login.text2"
msgstr "Se ficar preso num loop infinito, recomendamos instalar o <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Desativar ad blockers ou outras extensões do browser também pode ajudar."

#, fuzzy
msgid "page.codes.title"
msgstr "Códigos"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorador de Códigos"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explore os códigos com os quais os registos são etiquetados, por prefixo. A coluna “registos” mostra o número de registos etiquetados com códigos com o prefixo dado, conforme visto no motor de busca (incluindo registos apenas de metadados). A coluna “códigos” mostra quantos códigos reais têm um determinado prefixo."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Esta página pode demorar a ser gerada, por isso requer um captcha do Cloudflare. <a %(a_donate)s>Membros</a> podem pular o captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Por favor, não faça scraping destas páginas. Em vez disso, recomendamos <a %(a_import)s>gerar</a> ou <a %(a_download)s>descarregar</a> as nossas bases de dados ElasticSearch e MariaDB, e executar o nosso <a %(a_software)s>código open source</a>. Os dados brutos podem ser explorados manualmente através de ficheiros JSON como <a %(a_json_file)s>este</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefixo"

#, fuzzy
msgid "common.form.go"
msgstr "Ir"

#, fuzzy
msgid "common.form.reset"
msgstr "Reiniciar"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Pesquisar Arquivo da Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Aviso: o código contém caracteres Unicode incorretos e pode comportar-se de forma incorreta em várias situações. O binário bruto pode ser decodificado a partir da representação base64 no URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefixo de código conhecido “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefixo"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiqueta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descrição"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL para um código específico"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” será substituído pelo valor do código"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL genérico"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s registo correspondente a “%(prefix_label)s”"
msgstr[1] "%(count)s registos correspondentes a “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL para código específico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mais…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Códigos começando com “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Índice de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "registos"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "códigos"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s registos"

msgid "page.contact.dmca.form"
msgstr "Para DMCA/reclamação de direitos de autor, utiliza <a %(a_copyright)s>este formulário</a>."

msgid "page.contact.dmca.delete"
msgstr "Outras formas de contacto acerca de reclamações de direitos de autor são automaticamente apagadas."

msgid "page.contact.checkboxes.text1"
msgstr "Agradecemos o teu feedback e dúvidas!"

msgid "page.contact.checkboxes.text2"
msgstr "No entanto, devido ao elevado número de emails spam que recebemos, por favor confirma que percebeste as condições para nos contactares."

msgid "page.contact.checkboxes.copyright"
msgstr "Reclamações de direitos de autor enviadas para este email serão ignoradas; usa o formulário."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Os servidores parceiros estão indisponíveis devido ao encerramento de hospedagens. Devem estar novamente operacionais em breve."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "As adesões serão prolongadas em conformidade."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Não nos envies um email para <a %(a_request)s>solicitar livros</a><br>ou <a %(a_upload)s>uploads</a> pequenos (<10 mil)."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Ao fazer perguntas sobre conta ou doações, adicione o seu ID de conta, capturas de ecrã, recibos, o máximo de informações possível. Verificamos o nosso email apenas a cada 1-2 semanas, por isso, não incluir estas informações atrasará qualquer resolução."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostrar email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulário de reclamação de direitos de autor / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Se tiver uma reclamação de direitos de autor ou DMCA, por favor preencha este formulário com a maior precisão possível. Se encontrar algum problema, contacte-nos através do nosso endereço dedicado ao DMCA: %(email)s. Note que as reclamações enviadas para este endereço não serão processadas, é apenas para questões. Utilize o formulário abaixo para submeter as suas reclamações."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs no Arquivo da Anna (obrigatório). Uma por linha. Por favor, inclua apenas URLs que descrevam exatamente a mesma edição de um livro. Se quiser fazer uma reclamação para vários livros ou várias edições, por favor, submeta este formulário várias vezes."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Reclamações que agrupem vários livros ou edições serão rejeitadas."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "O seu nome (obrigatório)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Endereço (obrigatório)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Número de telefone (obrigatório)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (obrigatório)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descrição clara do material de origem (obrigatório)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs do material de origem (se aplicável). Um por linha. Por favor, inclua apenas aqueles que correspondam exatamente à edição para a qual está a reportar uma reclamação de direitos de autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs do material de origem, uma por linha. Por favor, dedique um momento a procurar o seu material de origem na Open Library. Isto ajudará a verificar a sua reclamação."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs para o material de origem, uma por linha (obrigatório). Por favor, inclua o maior número possível, para nos ajudar a verificar a sua reclamação (por exemplo, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaração e assinatura (obrigatório)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Submeter reclamação"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Obrigado por submeter a sua reclamação de direitos de autor. Vamos revê-la o mais rapidamente possível. Por favor, recarregue a página para submeter outra."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Algo correu mal. Por favor, recarregue a página e tente novamente."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Se estiver interessado em espelhar este conjunto de dados para <a %(a_archival)s>arquivamento</a> ou para fins de <a %(a_llm)s>treino de LLM</a>, por favor contacte-nos."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "A nossa missão é arquivar todos os livros do mundo (bem como artigos, revistas, etc.), e torná-los amplamente acessíveis. Acreditamos que todos os livros devem ser espelhados amplamente, para garantir redundância e resiliência. É por isso que estamos a reunir ficheiros de uma variedade de fontes. Algumas fontes são completamente abertas e podem ser espelhadas em massa (como o Sci-Hub). Outras são fechadas e protetoras, por isso tentamos extraí-las para “libertar” os seus livros. Outras ainda situam-se algures no meio."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Todos os nossos dados podem ser <a %(a_torrents)s>torrented</a>, e todos os nossos metadados podem ser <a %(a_anna_software)s>gerados</a> ou <a %(a_elasticsearch)s>descarregados</a> como bases de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente através de ficheiros JSON como <a %(a_dbrecord)s>este</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Visão Geral"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Abaixo está uma visão geral rápida das fontes dos ficheiros no Arquivo da Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Tamanho"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% espelhado por AA / torrents disponíveis"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentagens do número de ficheiros"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Última atualização"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Não-Ficção e Ficção"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s ficheiro"
msgstr[1] "%(count)s ficheiros"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelado desde 2021; a maioria disponível através de torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: pequenas adições desde então</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluindo “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Os torrents de ficção estão atrasados (embora IDs ~4-6M não tenham sido torrenteados, pois se sobrepõem aos nossos torrents do Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "A coleção “Chinesa” na Z-Library parece ser a mesma que a nossa coleção DuXiu, mas com MD5s diferentes. Excluímos esses ficheiros dos torrents para evitar duplicação, mas ainda os mostramos no nosso índice de pesquisa."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Empréstimo Digital Controlado"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dos ficheiros são pesquisáveis."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluindo duplicados"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Como as bibliotecas sombra frequentemente sincronizam dados entre si, há uma considerável sobreposição entre as bibliotecas. É por isso que os números não somam ao total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "A percentagem de “espelhado e semeado pelo Arquivo da Anna” mostra quantos ficheiros espelhamos nós mesmos. Semeamos esses ficheiros em massa através de torrents e disponibilizamo-los para download direto através de websites parceiros."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliotecas de origem"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Algumas bibliotecas-fonte promovem o compartilhamento em massa dos seus dados através de torrents, enquanto outras não compartilham prontamente a sua coleção. Neste último caso, o Arquivo da Anna tenta extrair as suas coleções e torná-las disponíveis (veja a nossa página de <a %(a_torrents)s>Torrents</a>). Existem também situações intermediárias, por exemplo, onde as bibliotecas-fonte estão dispostas a compartilhar, mas não têm os recursos para fazê-lo. Nesses casos, também tentamos ajudar."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Abaixo está uma visão geral de como interagimos com as diferentes bibliotecas-fonte."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Ficheiros"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dumps diários de <a %(dbdumps)s>base de dados HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizados para <a %(nonfiction)s>Não-Ficção</a> e <a %(fiction)s>Ficção</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(covers)s>torrents de capas de livros</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub congelou novos arquivos desde 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dumps de metadados disponíveis <a %(scihub1)s>aqui</a> e <a %(scihub2)s>aqui</a>, bem como como parte do <a %(libgenli)s>banco de dados Libgen.li</a> (que usamos)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents de dados disponíveis <a %(scihub1)s>aqui</a>, <a %(scihub2)s>aqui</a>, e <a %(libgenli)s>aqui</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Alguns novos arquivos estão <a %(libgenrs)s>sendo</a> <a %(libgenli)s>adicionados</a> ao “scimag” do Libgen, mas não o suficiente para justificar novos torrents"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dumps trimestrais de <a %(dbdumps)s>base de dados HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrents de Não-Ficção são partilhados com o Libgen.rs (e espelhados <a %(libgenli)s>aqui</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s O Arquivo da Anna e o Libgen.li gerem colaborativamente coleções de <a %(comics)s>bandas desenhadas</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos padrão</a> e <a %(fiction)s>ficção (divergente do Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s A coleção “fiction_rus” (ficção russa) não tem torrents dedicados, mas é coberta por torrents de outros, e mantemos um <a %(fiction_rus)s>mirror</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Arquivo da Anna e Z-Library gerenciam colaborativamente uma coleção de <a %(metadata)s>metadados do Z-Library</a> e <a %(files)s>arquivos do Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Alguns metadados disponíveis através de <a %(openlib)s>dumps de base de dados da Open Library</a>, mas estes não cobrem toda a coleção da IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Não há dumps de metadados facilmente acessíveis para toda a sua coleção"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(ia)s>metadados da IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Ficheiros disponíveis apenas para empréstimo de forma limitada, com várias restrições de acesso"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(ia)s>ficheiros da IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Várias bases de dados de metadados espalhadas pela internet chinesa; embora muitas vezes sejam bases de dados pagas"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Não há dumps de metadados facilmente acessíveis para toda a sua coleção."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(duxiu)s>metadados DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Várias bases de dados de ficheiros espalhadas pela internet chinesa; embora muitas vezes sejam bases de dados pagas"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s A maioria dos ficheiros só é acessível usando contas premium do BaiduYun; velocidades de download lentas."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(duxiu)s>ficheiros DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Várias fontes menores ou pontuais. Encorajamos as pessoas a carregarem primeiro em outras bibliotecas sombra, mas às vezes as pessoas têm coleções que são grandes demais para outros classificarem, embora não sejam grandes o suficiente para justificar a sua própria categoria."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fontes apenas de metadados"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Também enriquecemos a nossa coleção com fontes apenas de metadados, que podemos associar a ficheiros, por exemplo, usando números ISBN ou outros campos. Abaixo está uma visão geral dessas fontes. Novamente, algumas dessas fontes são completamente abertas, enquanto outras temos que extrair."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "A nossa inspiração para coletar metadados é o objetivo de Aaron Swartz de “uma página web para cada livro já publicado”, para o qual ele criou a <a %(a_openlib)s>Open Library</a>. Esse projeto tem tido sucesso, mas a nossa posição única permite-nos obter metadados que eles não conseguem. Outra inspiração foi o nosso desejo de saber <a %(a_blog)s>quantos livros existem no mundo</a>, para que possamos calcular quantos livros ainda temos para salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Note que na pesquisa de metadados, mostramos os registos originais. Não fazemos qualquer fusão de registos."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Última atualização"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Despejos de base de dados mensais <a %(dbdumps)s>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Não disponível diretamente em massa, protegido contra raspagem"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(worldcat)s>metadados OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Base de dados unificada"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Combinamos todas as fontes acima numa base de dados unificada que usamos para servir este site. Esta base de dados unificada não está disponível diretamente, mas como o Arquivo da Anna é totalmente open source, pode ser relativamente fácil <a %(a_generated)s>gerada</a> ou <a %(a_downloaded)s>descarregada</a> como bases de dados ElasticSearch e MariaDB. Os scripts nessa página irão automaticamente descarregar todos os metadados necessários das fontes mencionadas acima."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Se gostaria de explorar os nossos dados antes de executar esses scripts localmente, pode consultar os nossos ficheiros JSON, que ligam a outros ficheiros JSON. <a %(a_json)s>Este ficheiro</a> é um bom ponto de partida."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptado do nosso <a %(a_href)s>post no blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> é uma enorme base de dados de livros digitalizados, criada pelo <a %(superstar_link)s>SuperStar Digital Library Group</a>. A maioria são livros académicos, digitalizados para os tornar disponíveis digitalmente para universidades e bibliotecas. Para o nosso público de língua inglesa, <a %(princeton_link)s>Princeton</a> e a <a %(uw_link)s>University of Washington</a> têm boas visões gerais. Há também um excelente artigo que dá mais contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Os livros do Duxiu têm sido pirateados há muito tempo na internet chinesa. Normalmente, são vendidos por menos de um dólar por revendedores. São tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(link1)s>aqui</a> e <a %(link2)s>aqui</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Tínhamos isto no topo da nossa lista de tarefas e alocámos vários meses de trabalho a tempo inteiro para isso. No entanto, no final de 2023, um voluntário incrível, espantoso e talentoso entrou em contacto connosco, dizendo-nos que já tinha feito todo este trabalho — a grande custo. Partilharam a coleção completa connosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente notável."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total de arquivos: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Tamanho total dos arquivos: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Arquivos espelhados pelo Arquivo da Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Última atualização: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents pelo Arquivo da Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplo de registo no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "O nosso post no blog sobre estes dados"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts para importar metadados"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formato de Contêineres do Arquivo da Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mais informações dos nossos voluntários (notas brutas):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Este conjunto de dados está intimamente relacionado com o <a %(a_datasets_openlib)s>conjunto de dados da Open Library</a>. Contém uma raspagem de todos os metadados e uma grande parte dos ficheiros da Biblioteca de Empréstimo Digital Controlado da IA. As atualizações são lançadas no <a %(a_aac)s>formato de Contêineres do Arquivo da Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Estes registos são referidos diretamente do conjunto de dados da Open Library, mas também contêm registos que não estão na Open Library. Temos também vários ficheiros de dados raspados por membros da comunidade ao longo dos anos."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "A coleção consiste em duas partes. Precisa de ambas as partes para obter todos os dados (exceto torrents substituídos, que estão riscados na página de torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "o nosso primeiro lançamento, antes de padronizarmos no formato <a %(a_aac)s>Contêineres do Arquivo da Anna (AAC)</a>. Contém metadados (em json e xml), pdfs (dos sistemas de empréstimo digital acsm e lcpdf) e miniaturas de capas."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "lançamentos incrementais novos, usando AAC. Contém apenas metadados com carimbos de data após 2023-01-01, já que o resto já está coberto por “ia”. Também todos os arquivos pdf, desta vez dos sistemas de empréstimo acsm e “bookreader” (leitor web do IA). Apesar do nome não ser exatamente correto, ainda populamos arquivos do bookreader na coleção ia2_acsmpdf_files, já que são mutuamente exclusivos."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Website principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca de Empréstimo Digital"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentação de metadados (a maioria dos campos)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informação sobre o país do ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "A Agência Internacional do ISBN lança regularmente as faixas que alocou às agências nacionais do ISBN. A partir disso, podemos derivar a que país, região ou grupo linguístico pertence este ISBN. Atualmente, usamos esses dados indiretamente, através da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Última atualização: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Website do ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadados"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Para a história dos diferentes forks do Library Genesis, veja a página do <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "O Libgen.li contém a maior parte do mesmo conteúdo e metadados do Libgen.rs, mas tem algumas coleções adicionais, nomeadamente quadrinhos, revistas e documentos padrão. Também integrou o <a %(a_scihub)s>Sci-Hub</a> em seus metadados e motor de busca, que é o que usamos para nosso banco de dados."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Os metadados desta biblioteca estão disponíveis gratuitamente <a %(a_libgen_li)s>em libgen.li</a>. No entanto, este servidor é lento e não suporta a retomada de conexões interrompidas. Os mesmos arquivos também estão disponíveis em <a %(a_ftp)s>um servidor FTP</a>, que funciona melhor."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents estão disponíveis para a maioria do conteúdo adicional, destacando-se os torrents para bandas desenhadas, revistas e documentos padrão, que foram lançados em colaboração com o Arquivo da Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "A coleção de ficção tem seus próprios torrents (divergente do <a %(a_href)s>Libgen.rs</a>) começando em %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "De acordo com o administrador do Libgen.li, a coleção “fiction_rus” (ficção russa) deve ser coberta por torrents regularmente lançados de <a %(a_booktracker)s>booktracker.org</a>, destacando-se os torrents <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que espelhamos <a %(a_torrents)s>aqui</a>, embora ainda não tenhamos estabelecido quais torrents correspondem a quais arquivos)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Estatísticas de todas as coleções podem ser encontradas <a %(a_href)s>no site do libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "A não-ficção também parece ter divergido, mas sem novos torrents. Parece que isso aconteceu desde o início de 2022, embora não tenhamos verificado isso."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certas faixas sem torrents (como as faixas de ficção f_3463000 a f_4260000) são provavelmente arquivos da Z-Library (ou outros duplicados), embora possamos querer fazer alguma desduplicação e criar torrents para arquivos únicos do lgli nessas faixas."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Note que os arquivos torrent referindo-se a “libgen.is” são explicitamente mirrors de <a %(a_libgen)s>Libgen.rs</a> (“.is” é um domínio diferente usado pelo Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Um recurso útil para usar os metadados é <a %(a_href)s>esta página</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de ficção no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de quadrinhos no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de revistas no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos padrão no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de ficção russa no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadados"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadados via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informações sobre campos de metadados"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror de outros torrents (e torrents únicos de ficção e quadrinhos)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Fórum de discussão"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nosso post no blog sobre o lançamento dos quadrinhos"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "A história rápida dos diferentes forks do Library Genesis (ou “Libgen”) é que, com o tempo, as diferentes pessoas envolvidas no Library Genesis tiveram desentendimentos e seguiram caminhos separados."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "A versão “.fun” foi criada pelo fundador original. Está sendo reformulada em favor de uma nova versão, mais distribuída."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "A versão “.rs” tem dados muito semelhantes e lança sua coleção em torrents em massa de forma mais consistente. É aproximadamente dividida em uma seção de “ficção” e outra de “não-ficção”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originalmente em “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "A <a %(a_li)s>versão “.li”</a> tem uma coleção enorme de quadrinhos, bem como outros conteúdos, que ainda não estão disponíveis para download em massa através de torrents. Ela tem uma coleção separada de torrents de livros de ficção e contém os metadados do <a %(a_scihub)s>Sci-Hub</a> em seu banco de dados."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "De acordo com este <a %(a_mhut)s>post no fórum</a>, o Libgen.li foi originalmente hospedado em “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> de certa forma também é um fork do Library Genesis, embora tenham usado um nome diferente para seu projeto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Esta página é sobre a versão “.rs”. É conhecida por publicar consistentemente tanto seus metadados quanto o conteúdo completo de seu catálogo de livros. Sua coleção de livros é dividida entre uma parte de ficção e outra de não-ficção."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Um recurso útil para usar os metadados é <a %(a_metadata)s>esta página</a> (bloqueia faixas de IP, pode ser necessário usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de 2024-03, novos torrents estão a ser publicados neste <a %(a_href)s>tópico do fórum</a> (bloqueia intervalos de IP, pode ser necessário usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de Não-Ficção no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de Ficção no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadados do Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informação sobre campos de metadados do Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de Não-Ficção do Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de Ficção do Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Fórum de Discussão do Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents pelo Arquivo da Anna (capas de livros)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "O nosso blog sobre o lançamento das capas de livros"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "A Library Genesis é conhecida por já disponibilizar generosamente os seus dados em massa através de torrents. A nossa coleção do Libgen consiste em dados auxiliares que eles não lançam diretamente, em parceria com eles. Muito obrigado a todos os envolvidos com a Library Genesis por trabalharem connosco!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Lançamento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Este <a %(blog_post)s>primeiro lançamento</a> é bastante pequeno: cerca de 300GB de capas de livros do fork do Libgen.rs, tanto de ficção como de não-ficção. Estão organizados da mesma forma que aparecem no libgen.rs, por exemplo:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s para um livro de não-ficção."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s para um livro de ficção."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Tal como com a coleção da Z-Library, colocámos todos num grande ficheiro .tar, que pode ser montado usando <a %(a_ratarmount)s>ratarmount</a> se quiser servir os ficheiros diretamente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> é uma base de dados proprietária da organização sem fins lucrativos <a %(a_oclc)s>OCLC</a>, que agrega registos de metadados de bibliotecas de todo o mundo. Provavelmente é a maior coleção de metadados de bibliotecas do mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Outubro de 2023, lançamento inicial:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Em outubro de 2023, <a %(a_scrape)s>lançámos</a> uma extração abrangente da base de dados OCLC (WorldCat), no <a %(a_aac)s>formato de Contêineres do Arquivo da Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents pelo Arquivo da Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nosso post no blog sobre esses dados"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "A Open Library é um projeto de código aberto do Internet Archive para catalogar todos os livros do mundo. Tem uma das maiores operações de digitalização de livros do mundo e tem muitos livros disponíveis para empréstimo digital. O seu catálogo de metadados de livros está disponível gratuitamente para download e está incluído no Arquivo da Anna (embora atualmente não esteja na pesquisa, exceto se procurar explicitamente por um ID da Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadados"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Lançamento 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Este é um dump de muitas chamadas para isbndb.com durante setembro de 2022. Tentamos cobrir todas as faixas de ISBN. São cerca de 30,9 milhões de registros. No site deles, afirmam que têm, na verdade, 32,6 milhões de registros, então podemos ter perdido alguns de alguma forma, ou <em>eles</em> podem estar fazendo algo errado."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "As respostas JSON são praticamente brutas do servidor deles. Um problema de qualidade de dados que notamos é que, para números ISBN-13 que começam com um prefixo diferente de “978-”, eles ainda incluem um campo “isbn” que simplesmente é o número ISBN-13 com os primeiros 3 números cortados (e o dígito de verificação recalculado). Isso é obviamente errado, mas é assim que eles parecem fazer, então não alteramos."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Outro problema potencial que você pode encontrar é o fato de que o campo “isbn13” tem duplicatas, então você não pode usá-lo como chave primária em um banco de dados. Os campos “isbn13”+“isbn” combinados parecem ser únicos."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Para obter mais informações sobre o Sci-Hub, consulte o seu <a %(a_scihub)s>site oficial</a>, a <a %(a_wikipedia)s>página da Wikipedia</a> e esta <a %(a_radiolab)s>entrevista em podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Note que o Sci-Hub foi <a %(a_reddit)s>congelado desde 2021</a>. Já tinha sido congelado antes, mas em 2021 foram adicionados alguns milhões de artigos. Ainda assim, um número limitado de artigos continua a ser adicionado às coleções “scimag” do Libgen, embora não o suficiente para justificar novos torrents em massa."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usamos os metadados do Sci-Hub fornecidos pelo <a %(a_libgen_li)s>Libgen.li</a> na sua coleção “scimag”. Também usamos o conjunto de dados <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Note que os torrents “smarch” estão <a %(a_smarch)s>obsoletos</a> e, portanto, não estão incluídos na nossa lista de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents no Arquivo da Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadados e torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents no Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents no Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Atualizações no Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Página da Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Entrevista em podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uploads para o Arquivo da Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Visão geral da <a %(a1)s>página de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Várias fontes menores ou pontuais. Encorajamos as pessoas a fazerem upload para outras bibliotecas sombra primeiro, mas às vezes as pessoas têm coleções que são grandes demais para outros analisarem, embora não sejam grandes o suficiente para justificar a sua própria categoria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "A coleção “upload” é dividida em subcoleções menores, que são indicadas nos AACIDs e nos nomes dos torrents. Todas as subcoleções foram primeiro desduplicadas em relação à coleção principal, embora os ficheiros JSON de metadados “upload_records” ainda contenham muitas referências aos ficheiros originais. Ficheiros não relacionados a livros também foram removidos da maioria das subcoleções e geralmente <em>não</em> são mencionados nos JSON “upload_records”."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Muitas subcoleções são compostas por sub-sub-coleções (por exemplo, de diferentes fontes originais), que são representadas como diretórios nos campos “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "As subcoleções são:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcoleção"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notas"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "navegar"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "pesquisar"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bastante completo. Do nosso voluntário “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "De um torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tem uma sobreposição bastante alta com coleções de artigos existentes, mas muito poucas correspondências de MD5, por isso decidimos mantê-lo completamente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raspagem de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), por voluntário <q>j</q>. Corresponde à metadata de <q>airitibooks</q> em <a %(a1)s><q>Outras raspagens de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "De uma coleção <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte da fonte original, parte de the-eye.eu, parte de outros espelhos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "De um site privado de torrents de livros, <a %(a_href)s>Bibliotik</a> (frequentemente referido como “Bib”), cujos livros foram agrupados em torrents por nome (A.torrent, B.torrent) e distribuídos através do the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Do nosso voluntário “bpb9v”. Para mais informações sobre <a %(a_href)s>CADAL</a>, veja as notas na nossa <a %(a_duxiu)s>página de dados DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mais do nosso voluntário “bpb9v”, principalmente ficheiros DuXiu, bem como uma pasta “WenQu” e “SuperStar_Journals” (SuperStar é a empresa por trás do DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Do nosso voluntário “cgiym”, textos chineses de várias fontes (representados como subdiretórios), incluindo da <a %(a_href)s>China Machine Press</a> (um grande editor chinês)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleções não chinesas (representadas como subdiretórios) do nosso voluntário “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raspagem de livros sobre arquitetura chinesa, por voluntário <q>cm</q>: <q>Consegui explorando uma vulnerabilidade de rede na editora, mas essa brecha já foi fechada</q>. Corresponde à metadata de <q>chinese_architecture</q> em <a %(a1)s><q>Outras raspagens de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Livros da editora acadêmica <a %(a_href)s>De Gruyter</a>, coletados de alguns grandes torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Raspagem de <a %(a_href)s>docer.pl</a>, um site polonês de compartilhamento de arquivos focado em livros e outras obras escritas. Raspado no final de 2023 pelo voluntário “p”. Não temos bons metadados do site original (nem mesmo extensões de arquivo), mas filtramos arquivos semelhantes a livros e muitas vezes conseguimos extrair metadados dos próprios arquivos."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epubs do DuXiu, diretamente do DuXiu, coletados pelo voluntário “w”. Apenas livros recentes do DuXiu estão disponíveis diretamente através de ebooks, então a maioria destes deve ser recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Arquivos restantes do DuXiu do voluntário “m”, que não estavam no formato proprietário PDG do DuXiu (o principal <a %(a_href)s>conjunto de dados DuXiu</a>). Coletados de muitas fontes originais, infelizmente sem preservar essas fontes no caminho do arquivo."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raspagem de livros eróticos, por voluntário <q>do no harm</q>. Corresponde à metadata de <q>hentai</q> em <a %(a1)s><q>Outras raspagens de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Coleção raspada de uma editora japonesa de mangá pelo voluntário “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arquivos judiciais selecionados de Longquan</a>, fornecidos pelo voluntário “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Raspagem de <a %(a_href)s>magzdb.org</a>, um aliado da Library Genesis (está ligado na página inicial do libgen.rs) mas que não quis fornecer seus arquivos diretamente. Obtido pelo voluntário “p” no final de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Vários pequenos uploads, demasiado pequenos para serem uma subcoleção própria, mas representados como diretórios."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks do AvaxHome, um site russo de compartilhamento de arquivos."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arquivo de jornais e revistas. Corresponde à metadata de <q>newsarch_magz</q> em <a %(a1)s><q>Outras raspagens de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raspagem do <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Coleção do voluntário “o” que recolheu livros polacos diretamente de sites de lançamento originais (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coleções combinadas de <a %(a_href)s>shuge.org</a> pelos voluntários “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomeada em homenagem à biblioteca fictícia), extraída em 2022 pelo voluntário “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-coleções (representadas como diretórios) do voluntário “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> em Taiwan), mebook (mebook.cc, 我的小书屋, meu pequeno quarto de livros — woz9ts: “Este site foca principalmente em compartilhar arquivos de ebooks de alta qualidade, alguns dos quais são formatados pelo próprio proprietário. O proprietário foi <a %(a_arrested)s>preso</a> em 2019 e alguém fez uma coleção dos arquivos que ele compartilhou.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Arquivos restantes do DuXiu do voluntário “woz9ts”, que não estavam no formato proprietário PDG do DuXiu (ainda a serem convertidos para PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents por Arquivo da Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Raspagem do Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "O Z-Library tem as suas raízes na comunidade <a %(a_href)s>Library Genesis</a> e, inicialmente, foi iniciado com os seus dados. Desde então, profissionalizou-se consideravelmente e tem uma interface muito mais moderna. Assim, conseguem obter muitas mais doações, tanto monetárias para continuar a melhorar o seu site, como doações de novos livros. Acumularam uma grande coleção além da Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Atualização de fevereiro de 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "No final de 2022, os alegados fundadores do Z-Library foram presos e os domínios foram apreendidos pelas autoridades dos Estados Unidos. Desde então, o site tem lentamente voltado a estar online. Não se sabe quem o gere atualmente."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "A coleção consiste em três partes. As páginas de descrição originais para as duas primeiras partes são preservadas abaixo. Você precisa de todas as três partes para obter todos os dados (exceto torrents substituídos, que estão riscados na página de torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nosso primeiro lançamento. Este foi o primeiro lançamento do que então se chamava “Espelho da Biblioteca Pirata” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: segundo lançamento, desta vez com todos os ficheiros embalados em ficheiros .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: novos lançamentos incrementais, usando o <a %(a_href)s>formato de Contêineres do Arquivo da Anna (AAC)</a>, agora lançados em colaboração com a equipe do Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents por Arquivo da Anna (metadados + conteúdo)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplo de registo no Arquivo da Anna (coleção original)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplo de registo no Arquivo da Anna (coleção “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Site principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domínio Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Postagem no blog sobre o Lançamento 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Postagem no blog sobre o Lançamento 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Lançamentos do Zlib (páginas de descrição originais)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Lançamento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "O espelho inicial foi obtido meticulosamente ao longo de 2021 e 2022. Neste momento, está ligeiramente desatualizado: reflete o estado da coleção em junho de 2021. Atualizaremos isto no futuro. Neste momento, estamos focados em lançar esta primeira versão."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Como a Library Genesis já está preservada com torrents públicos e está incluída na Z-Library, fizemos uma deduplicação básica contra a Library Genesis em junho de 2022. Para isso, usamos hashes MD5. Provavelmente há muito mais conteúdo duplicado na biblioteca, como múltiplos formatos de arquivo com o mesmo livro. Isso é difícil de detectar com precisão, então não o fazemos. Após a deduplicação, ficamos com mais de 2 milhões de arquivos, totalizando pouco menos de 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "A coleção consiste em duas partes: um dump MySQL “.sql.gz” dos metadados e os 72 arquivos torrent de cerca de 50-100GB cada. Os metadados contêm os dados conforme relatados pelo site da Z-Library (título, autor, descrição, tipo de arquivo), bem como o tamanho real do arquivo e o md5sum que observamos, já que às vezes esses não coincidem. Parece haver intervalos de arquivos para os quais a própria Z-Library tem metadados incorretos. Também podemos ter baixado arquivos incorretamente em alguns casos isolados, que tentaremos detectar e corrigir no futuro."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Os grandes arquivos torrent contêm os dados reais dos livros, com o ID da Z-Library como nome do arquivo. As extensões dos arquivos podem ser reconstruídas usando o dump de metadados."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "A coleção é uma mistura de conteúdo de não-ficção e ficção (não separado como na Library Genesis). A qualidade também varia amplamente."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Este primeiro lançamento está agora totalmente disponível. Note que os arquivos torrent estão disponíveis apenas através do nosso mirror Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Lançamento 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Obtivemos todos os livros que foram adicionados à Z-Library entre o nosso último mirror e agosto de 2022. Também voltamos e extraímos alguns livros que perdemos na primeira vez. No total, esta nova coleção tem cerca de 24TB. Novamente, esta coleção é deduplicada contra a Library Genesis, já que já existem torrents disponíveis para essa coleção."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Os dados estão organizados de forma semelhante ao primeiro lançamento. Há um dump MySQL “.sql.gz” dos metadados, que também inclui todos os metadados do primeiro lançamento, substituindo-o assim. Também adicionamos algumas novas colunas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: se este arquivo já está na Library Genesis, seja na coleção de não-ficção ou ficção (correspondido por md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: em qual torrent este arquivo está."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: definido quando não conseguimos baixar o livro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mencionamos isso da última vez, mas apenas para esclarecer: “filename” e “md5” são as propriedades reais do arquivo, enquanto “filename_reported” e “md5_reported” são o que extraímos da Z-Library. Às vezes, esses dois não coincidem, então incluímos ambos."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Para este lançamento, mudamos a collation para “utf8mb4_unicode_ci”, que deve ser compatível com versões mais antigas do MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Os arquivos de dados são semelhantes aos da última vez, embora sejam muito maiores. Simplesmente não nos preocupamos em criar toneladas de arquivos torrent menores. “pilimi-zlib2-0-14679999-extra.torrent” contém todos os arquivos que perdemos no último lançamento, enquanto os outros torrents são todos novos intervalos de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Atualização %(date)s:</strong> Fizemos a maioria dos nossos torrents muito grandes, causando dificuldades nos clientes de torrent. Removemos eles e lançamos novos torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Atualização %(date)s:</strong> Ainda havia muitos arquivos, então os embrulhamos em arquivos tar e lançamos novos torrents novamente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Lançamento 2 adendo (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Este é um único arquivo torrent extra. Não contém nenhuma informação nova, mas tem alguns dados que podem demorar para serem computados. Isso o torna conveniente de ter, já que baixar este torrent é muitas vezes mais rápido do que computá-lo do zero. Em particular, contém índices SQLite para os arquivos tar, para uso com <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Perguntas Frequentes (FAQ)"

msgid "page.faq.what_is.title"
msgstr "O que é o Anna's Archive?"

msgid "page.home.intro.text1"
msgstr "O <span %(span_anna)s>Anna's Archive</span> é um projeto sem fins lucrativos com dois objetivos:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Preservação:</strong> Fazer backup de todo o conhecimento e cultura da humanidade.</li><li><strong>Acesso:</strong> Tornar este conhecimento e cultura disponível para qualquer pessoa no mundo.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Todo o nosso <a %(a_code)s>código</a> e <a %(a_datasets)s>dados</a> são completamente open source."

msgid "page.home.preservation.header"
msgstr "Preservação"

msgid "page.home.preservation.text1"
msgstr "Nós preservamos livros, artigos científicos, bandas desenhadas, revistas e outros trabalhos, recolhendo num único sítio material de várias <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>, bibliotecas oficias e outras coleções. Toda esta informação é preservada tornando simples o processo de cópia em massa — através de torrents — resultando em muitas cópias espalhadas pelo mundo. Algumas shadow libraries já o fazem (p.e., Sci-Hub, Library Genesis) enquanto o Anna's Archive disponibiliza outras bibliotecas que ainda não suportam distribuição em massa (p.e., Z-Library) ou projetos que não são shadow librares (p.e., Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Esta larga distribuição combinada com código-fonte open source torna o nosso site resiliente a takedowns e garante a preservação a longo prazo de todo o conhecimento e cultura da humanidade. Ver mais sobre os <a href=\"/datasets\">nossos datasets</a>."

msgid "page.home.preservation.label"
msgstr "A nossa estimativa é que preservamos cerca de <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% de todos os livros do mundo</a>."

msgid "page.home.access.header"
msgstr "Acesso"

msgid "page.home.access.text"
msgstr "Trabalhámos com parceiros para manter as coleções acessíveis a qualquer um de forma simples e gratuita. Acreditámos que todos têm o direito do conhecimento coletivo da humanidade. Sem <a %(a_search)s>colocar os custos nos autores</a>."

msgid "page.home.access.label"
msgstr "Transferências por hora nos últimos 30 dias. Média por hora: %(hourly)s. Média por dia: %(daily)s."

msgid "page.about.text2"
msgstr "Acreditámos fortemente na troca gratuita de informação e na preservação do conhecimento e cultura. Com este motor de pesquisa, estamos apenas a construir em cima de projetos de enorme importância. Respeitámos imenso o trabalho árduo de todos os envolvidos nas várias shadow libraries e esperamos que este motor de pesquisa ajude a ampliar o seu alcance."

msgid "page.about.text3"
msgstr "Para ficares a par de todo o progresso, segue a Anna no <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou no <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Para questões e feedback, por favor contacta a Anna através do email %(email)s."

msgid "page.faq.help.title"
msgstr "Como posso ajudar?"

msgid "page.about.help.text"
msgstr "<li>1. Segue-nos no <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou no <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Espalha a palavra sobre o Anna's Archive no X, Reddit, TikTok, Instagram, no teu café ou biblioteca ou em qualquer outro sítio! Não acreditamos em gatekeeping — se nos mandarem abaixo, simplesmente apareceremos noutro sítio, visto que todo o nosso código e dados são totalmente open source.</li><li>3. Se conseguires, considera <a href=\"/donate\">fazer um donativo</a>.</li><li>4. Ajuda a <a href=\"https://translate.annas-software.org/\">traduzir</a> o nosso site noutras línguas.</li><li>5. Se és um engenheiro de software, considera contribuir para o nosso <a href=\"https://annas-software.org/\">código open source</a>, ou a seedar os nossos <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Agora também temos um canal Matrix sincronizado em %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Se és um investigador de segurança, agradecemos as tuas skills tanto para defesa como ataque. Vê mais na nossa página de <a %(a_security)s>Segurança</a>."

msgid "page.about.help.text7"
msgstr "7. Estamos à procura de especialistas em pagamentos anónimos. Achas que nos consegues ajudar a adicionar outros métodos de donativos? PayPal, WeChat, cartões-presente. Se souberes de alguém, por favor entra em contacto connosco."

msgid "page.about.help.text8"
msgstr "8. Estamos sempre à procura de mais capacidade de infraestrutura."

msgid "page.about.help.text9"
msgstr "9. Podes ajudar ao reportar problemas com ficheiros, deixando comentários ou criando listas aqui neste website. Podes também ajudar fazendo <a %(a_upload)s>uploads de mais livros</a> ou corrigindo problemas nos ficheiros ou formatação de livros já existentes."

msgid "page.about.help.text10"
msgstr "10. Cria ou ajuda a manter a página da Wikipedia do Anna's Archive na tua língua."

msgid "page.about.help.text11"
msgstr "11. Estamos à procura de anúncios relevantes e pequenos. Se tens interesse em fazer publicidade no Anna's Archive, por favor entra em contacto."

msgid "page.faq.help.mirrors"
msgstr "Adorávamos que começassem a aparecer mais <a%(a_mirrors)s>mirrors</a> e podemos suportar financeiramente estas ações."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Para mais informações sobre como ser voluntário, consulte a nossa página de <a %(a_volunteering)s>Voluntariado & Recompensas</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Porque são os downloads lentos tão lentos?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Literalmente, não temos recursos suficientes para oferecer downloads de alta velocidade a todos no mundo, por mais que gostaríamos. Se um benfeitor rico quiser intervir e fornecer isso para nós, seria incrível, mas até lá, estamos a fazer o nosso melhor. Somos um projeto sem fins lucrativos que mal consegue sustentar-se através de doações."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "É por isso que implementámos dois sistemas para downloads gratuitos, com os nossos parceiros: servidores partilhados com downloads lentos e servidores ligeiramente mais rápidos com uma lista de espera (para reduzir o número de pessoas a fazer download ao mesmo tempo)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Também temos <a %(a_verification)s>verificação do navegador</a> para os nossos downloads lentos, porque caso contrário, bots e scrapers abusariam deles, tornando as coisas ainda mais lentas para os utilizadores legítimos."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Note que, ao usar o Tor Browser, pode ser necessário ajustar as suas configurações de segurança. Na opção mais baixa, chamada “Padrão”, o desafio do Cloudflare turnstile é bem-sucedido. Nas opções mais altas, chamadas “Mais Seguro” e “Mais Seguro de Todos”, o desafio falha."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Para ficheiros grandes, por vezes, os downloads lentos podem ser interrompidos a meio. Recomendamos o uso de um gestor de downloads (como o JDownloader) para retomar automaticamente os downloads grandes."

msgid "page.donate.faq.title"
msgstr "Perguntas sobre os donativos"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>As subscrições renovam automaticamente?</div> As subscrições <strong>não</strong> renovam automaticamente. Podes juntar-te pelo período de tempo que quiseres."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Posso atualizar a minha adesão ou obter várias adesões?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Têm outros métodos de pagamento disponíveis?</div> De momento não. Muitas pessoas não querem que arquivos como este existam, como tal temos de ser extremamente cuidados. Se nos conseguires ajudar a trabalhar com outros métodos de pagamento de forma segura, por favor entra em contacto através do email %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>O que significam os intervalos por mês?</div> Pode alcançar o lado inferior de um intervalo aplicando todos os descontos, como escolher um período superior a um mês."

msgid "page.donate.faq.spend"
msgstr "<div> %(div_question)s>Onde é aplicado o dinheiro dos donativos?</div> Os donativos são utilizados 100%% na preservação e no acesso ao conhecimento e cultura. Neste momento gastamos a vasta maioria em servidores, discos e rede. Nenhum dinheiro dos donativos é enviado para nenhum membro da equipa."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Posso fazer um donativo grande?</div> Isso seria incrível! Para donativos de milhares, por favor contact-nos diretamente através do email %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Posso fazer uma doação sem me tornar membro?</div> Claro que sim. Aceitamos doações de qualquer valor neste endereço Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "Como posso fazer upload de novos livros?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativamente, pode carregá-los para a Z-Library <a %(a_upload)s>aqui</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Para pequenos uploads (até 10.000 ficheiros), por favor, carregue-os tanto para %(first)s como para %(second)s."

msgid "page.upload.text1"
msgstr "Por agora, sugerimos que faças upload de novos livros através dos forks do Library Genesis. Tens disponível um <%(a_guide)s>guia</a>. De notar que ambos os forks que indexamos aqui neste website vão buscar a informação ao mesmo sistema de uploads."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Para o Libgen.li, certifique-se de primeiro fazer login no <a %(a_forum)s>fórum deles</a> com o nome de utilizador %(username)s e a palavra-passe %(password)s, e depois volte à <a %(a_upload_page)s>página de upload</a> deles."

msgid "common.libgen.email"
msgstr "Se o teu endereço de email não funcionar nos fóruns Libgen, recomendamos que utilizes o <a %(a_mail)s>Proton Mail</a> (grátis). Podes também <a %(a_manual)s>pedir manualmente</a> para a tua conta ser ativada."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Note que mhut.org bloqueia certos intervalos de IP, por isso pode ser necessário um VPN."

msgid "page.upload.large.text"
msgstr "Para uploads grandes (acima de 10 mil ficheiros) não aceites pela Libgen ou pela Z-Library, por favor contacta-nos através do email %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Para carregar artigos académicos, por favor, além de Library Genesis, carregue também para <a %(a_stc_nexus)s>STC Nexus</a>. Eles são a melhor shadow library para novos artigos. Ainda não os integrámos, mas iremos fazê-lo em algum momento. Pode usar o <a %(a_telegram)s>bot de upload no Telegram</a> deles, ou contactar o endereço listado na mensagem fixada se tiver demasiados ficheiros para carregar desta forma."

msgid "page.faq.request.title"
msgstr "Como posso solicitar livros?"

msgid "page.request.cannot_accomodate"
msgstr "Neste momento não aceitámos pedidos de novos livros."

msgid "page.request.forums"
msgstr "Por favor faz os pedidos nos fóruns do Libgen ou da Z-Library."

msgid "page.request.dont_email"
msgstr "Não envies emails com pedidos de livros."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Recolhe metadados?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "De fato, sim."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Eu descarreguei 1984 de George Orwell, a polícia virá à minha porta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Não se preocupe muito, há muitas pessoas a descarregar de websites aos quais estamos ligados, e é extremamente raro ter problemas. No entanto, para se manter seguro, recomendamos o uso de uma VPN (paga) ou <a %(a_tor)s>Tor</a> (gratuito)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Como posso guardar as minhas definições de pesquisa?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selecione as definições que prefere, deixe a caixa de pesquisa vazia, clique em “Pesquisar” e depois adicione a página aos favoritos usando a funcionalidade de favoritos do seu navegador."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Têm uma aplicação móvel?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Não temos uma aplicação móvel oficial, mas pode instalar este website como uma aplicação."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Clique no menu de três pontos no canto superior direito e selecione “Adicionar à Tela Inicial”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Clique no botão “Partilhar” na parte inferior e selecione “Adicionar à Tela Inicial”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Têm uma API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Temos uma API JSON estável para membros, para obter um URL de download rápido: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentação dentro do próprio JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Para outros casos de uso, como iterar por todos os nossos ficheiros, construir pesquisas personalizadas, e assim por diante, recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>descarregar</a> as nossas bases de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente <a %(a_explore)s>através de ficheiros JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "A nossa lista de torrents brutos pode ser descarregada como <a %(a_torrents)s>JSON</a> também."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ de Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Gostaria de ajudar a semear, mas não tenho muito espaço em disco."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Use o <a %(a_list)s>gerador de lista de torrents</a> para gerar uma lista de torrents que mais precisam de semeação, dentro dos limites do seu espaço de armazenamento."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Os torrents são muito lentos; posso baixar os dados diretamente de vocês?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Sim, veja a página de <a %(a_llm)s>dados LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Posso baixar apenas um subconjunto dos arquivos, como apenas um idioma ou tópico específico?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Resposta curta: não é fácil."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Resposta longa:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "A maioria dos torrents contém os arquivos diretamente, o que significa que você pode instruir os clientes de torrent a baixar apenas os arquivos necessários. Para determinar quais arquivos baixar, você pode <a %(a_generate)s>gerar</a> nossos metadados, ou <a %(a_download)s>baixar</a> nossas bases de dados ElasticSearch e MariaDB. Infelizmente, algumas coleções de torrents contêm arquivos .zip ou .tar na raiz, caso em que você precisará baixar o torrent inteiro antes de poder selecionar arquivos individuais."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(No entanto, temos <a %(a_ideas)s>algumas ideias</a> para este último caso.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Ainda não existem ferramentas fáceis de usar para filtrar torrents, mas aceitamos contribuições."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Como vocês lidam com duplicatas nos torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Tentamos manter a duplicação ou sobreposição mínima entre os torrents nesta lista, mas isso nem sempre pode ser alcançado e depende muito das políticas das bibliotecas de origem. Para bibliotecas que lançam os seus próprios torrents, está fora do nosso controlo. Para torrents lançados pelo Arquivo da Anna, deduplicamos apenas com base no hash MD5, o que significa que diferentes versões do mesmo livro não são deduplicadas."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Posso obter a lista de torrents em JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Sim."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Não vejo PDFs ou EPUBs nos torrents, apenas ficheiros binários? O que devo fazer?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Estes são na verdade PDFs e EPUBs, eles apenas não têm uma extensão em muitos dos nossos torrents. Existem dois lugares onde pode encontrar os metadados para ficheiros torrent, incluindo os tipos/extensões de ficheiros:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Cada coleção ou lançamento tem os seus próprios metadados. Por exemplo, os <a %(a_libgen_nonfic)s>torrents do Libgen.rs</a> têm uma base de dados de metadados correspondente hospedada no site do Libgen.rs. Normalmente, ligamos a recursos de metadados relevantes a partir da <a %(a_datasets)s>página do conjunto de dados</a> de cada coleção."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>descarregar</a> as nossas bases de dados ElasticSearch e MariaDB. Estas contêm um mapeamento para cada registo no Arquivo da Anna para os seus ficheiros torrent correspondentes (se disponíveis), sob “torrent_paths” no JSON do ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Por que o meu cliente de torrent não consegue abrir alguns dos seus arquivos torrent / links magnéticos?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Alguns clientes de torrent não suportam tamanhos de peças grandes, que muitos dos nossos torrents possuem (para os mais recentes, não fazemos mais isso — mesmo que seja válido pelas especificações!). Portanto, experimente um cliente diferente se encontrar esse problema, ou reclame com os desenvolvedores do seu cliente de torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Tem um programa de divulgação responsável?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Convidamos investigadores de segurança a procurar vulnerabilidades nos nossos sistemas. Somos grandes defensores da divulgação responsável. Contacte-nos <a %(a_contact)s>aqui</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Atualmente, não podemos oferecer recompensas por bugs, exceto para vulnerabilidades que tenham o <a %(a_link)s>potencial de comprometer a nossa anonimidade</a>, para as quais oferecemos recompensas na faixa de $10k-50k. Gostaríamos de oferecer um escopo mais amplo para recompensas por bugs no futuro! Note que ataques de engenharia social estão fora do escopo."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Se está interessado em segurança ofensiva e quer ajudar a arquivar o conhecimento e a cultura do mundo, não hesite em contactar-nos. Há muitas maneiras pelas quais pode ajudar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Existem mais recursos sobre o Arquivo da Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog da Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — atualizações regulares"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software da Anna</a> — o nosso código open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduzir no Software da Anna</a> — o nosso sistema de tradução"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — sobre os dados"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domínios alternativos"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mais sobre nós (por favor, ajude a manter esta página atualizada ou crie uma na sua própria língua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Como posso reportar uma violação de direitos de autor?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Não hospedamos aqui quaisquer materiais protegidos por direitos de autor. Somos um motor de busca e, como tal, apenas indexamos metadados que já estão publicamente disponíveis. Ao fazer download dessas fontes externas, sugerimos que verifique as leis na sua jurisdição em relação ao que é permitido. Não somos responsáveis pelo conteúdo hospedado por outros."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Se tem queixas sobre o que vê aqui, a melhor opção é contactar o site original. Regularmente, atualizamos a nossa base de dados com as mudanças deles. Se realmente acha que tem uma queixa válida de DMCA à qual devemos responder, por favor preencha o <a %(a_copyright)s>formulário de reclamação de DMCA / Direitos de Autor</a>. Levamos as suas queixas a sério e responderemos o mais rápido possível."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Odeio a forma como estão a gerir este projeto!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Gostaríamos também de lembrar a todos que todo o nosso código e dados são completamente open source. Isto é único para projetos como o nosso — não conhecemos nenhum outro projeto com um catálogo tão massivo que também seja totalmente open source. Acolhemos de bom grado qualquer pessoa que pense que gerimos mal o nosso projeto a pegar no nosso código e dados e criar a sua própria shadow library! Não dizemos isto por despeito ou algo do género — genuinamente achamos que seria incrível, pois elevaria o nível para todos e melhor preservaria o legado da humanidade."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Tem um monitor de tempo de atividade?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Por favor, veja <a %(a_href)s>este excelente projeto</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Como posso doar livros ou outros materiais físicos?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Por favor, envie-os para o <a %(a_archive)s>Internet Archive</a>. Eles irão preservá-los adequadamente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Quem é a Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Você é a Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Quais são os seus livros favoritos?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Aqui estão alguns livros que têm um significado especial para o mundo das shadow libraries e da preservação digital:"

msgid "page.fast_downloads.no_more_new"
msgstr "Já gastaste todas as transferências rápidas de hoje."

msgid "page.fast_downloads.no_member"
msgstr "Torna-te um membro para teres acesso a transferências rápidas."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Agora aceitamos cartões de oferta da Amazon, cartões de crédito e débito, criptomoedas, Alipay e WeChat."

msgid "page.home.full_database.header"
msgstr "Base de dados completa"

msgid "page.home.full_database.subtitle"
msgstr "Livros, artigos científicos, revistas, bandas desenhadas, registos de biblioteca, metadados, …"

msgid "page.home.full_database.search"
msgstr "Procurar"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "O Sci-Hub <a %(a_paused)s>pausou</a> o carregamento de novos artigos."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB é uma continuação do Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Acesso direto a %(count)s papers científicos"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Abrir"

msgid "page.home.scidb.browser_verification"
msgstr "Se és um <a %(a_member)s>membro</a>, a verificação de browser não é necessária."

msgid "page.home.archive.header"
msgstr "Arquivo a longo-prazo"

msgid "page.home.archive.body"
msgstr "Os datasets utilizados no Anna's Archive estão disponíveis publicamente, e podem ser replicados em massa utilizando torrents. <a %(a_datasets)s>Saber mais…</a>"

msgid "page.home.torrents.body"
msgstr "Podes dar uma grande ajuda a seedar de torrents. <a %(a_torrents)s>Saber mais…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "dados de treino para LLMs"

msgid "page.home.llm.body"
msgstr "Temos a maior coleção de dados de alta qualidade do mundo. <a %(a_llm)s>Saber mais…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: procuramos voluntários"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Procuramos voluntários"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Como um projeto sem fins lucrativos e open source, estamos sempre à procura de pessoas para ajudar."

msgid "page.home.payment_processor.body"
msgstr "Se tens acesso a um sistema de pagamento anónimo de alto-risco, por favor contact-nos. Também temos espaço para pequenos anúncios relevantes. Todos os rendimentos irão para os esforços da preservação."

msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Downloads IPFS"

msgid "page.partner_download.main_page"
msgstr "🔗 <a %(a_main)s>&lt;Todos os links para transferência deste ficheiro</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(poderá ser necessário tentar várias vezes com IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Para teres acesso a transferências rápidas e passar à frente da verificação do browser, <a %(a_membership)s>torna-te membro</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Para fazeres mirror em larga escala da nossa coleção, visita as páginas dos <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Dados LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "É bem compreendido que os LLMs prosperam com dados de alta qualidade. Temos a maior coleção de livros, artigos, revistas, etc. do mundo, que são algumas das fontes de texto de maior qualidade."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Escala e alcance únicos"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "A nossa coleção contém mais de cem milhões de ficheiros, incluindo revistas académicas, manuais escolares e revistas. Alcançamos esta escala combinando grandes repositórios existentes."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Algumas das nossas coleções de origem já estão disponíveis em massa (Sci-Hub e partes do Libgen). Outras fontes foram libertadas por nós mesmos. <a %(a_datasets)s>Datasets</a> mostra uma visão geral completa."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "A nossa coleção inclui milhões de livros, artigos e revistas de antes da era dos e-books. Grandes partes desta coleção já foram OCRizadas e já têm pouca sobreposição interna."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Como podemos ajudar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Podemos fornecer acesso de alta velocidade às nossas coleções completas, bem como a coleções não lançadas."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Este é um acesso a nível empresarial que podemos fornecer por doações na ordem de dezenas de milhares de USD. Também estamos dispostos a trocar isto por coleções de alta qualidade que ainda não temos."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Podemos reembolsá-lo se nos conseguir fornecer enriquecimento dos nossos dados, como:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Remoção de sobreposição (deduplicação)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extração de texto e metadados"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Apoie o arquivamento a longo prazo do conhecimento humano, enquanto obtém melhores dados para o seu modelo!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contacte-nos</a> para discutir como podemos trabalhar juntos."

msgid "page.login.continue"
msgstr "Continuar"

msgid "page.login.please"
msgstr "Por favor <a %(a_account)s>inicia sessão</a> para veres esta página.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "O Arquivo da Anna está temporariamente fora de serviço para manutenção. Por favor, volte dentro de uma hora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Melhorar metadados"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Pode ajudar na preservação de livros melhorando os metadados! Primeiro, leia o contexto sobre metadados no Arquivo da Anna, e depois aprenda como melhorar metadados através da ligação com a Open Library, e ganhe uma adesão gratuita no Arquivo da Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Contexto"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Quando olha para um livro no Arquivo da Anna, pode ver vários campos: título, autor, editora, edição, ano, descrição, nome do ficheiro, e mais. Todas essas peças de informação são chamadas <em>metadados</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Como combinamos livros de várias <em>bibliotecas fonte</em>, mostramos quaisquer metadados disponíveis nessa biblioteca fonte. Por exemplo, para um livro que obtivemos da Library Genesis, mostraremos o título da base de dados da Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Às vezes, um livro está presente em <em>múltiplas</em> bibliotecas fonte, que podem ter diferentes campos de metadados. Nesse caso, simplesmente mostramos a versão mais longa de cada campo, já que essa, esperamos, contém a informação mais útil! Ainda assim, mostraremos os outros campos abaixo da descrição, por exemplo, como \"título alternativo\" (mas apenas se forem diferentes)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Também extraímos <em>códigos</em> como identificadores e classificadores da biblioteca fonte. <em>Identificadores</em> representam de forma única uma edição particular de um livro; exemplos são ISBN, DOI, Open Library ID, Google Books ID, ou Amazon ID. <em>Classificadores</em> agrupam vários livros semelhantes; exemplos são Dewey Decimal (DCC), UDC, LCC, RVK, ou GOST. Às vezes, esses códigos estão explicitamente ligados nas bibliotecas fonte, e às vezes podemos extraí-los do nome do ficheiro ou da descrição (principalmente ISBN e DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Podemos usar identificadores para encontrar registos em <em>coleções apenas de metadados</em>, como OpenLibrary, ISBNdb, ou WorldCat/OCLC. Há um <em>separador de metadados</em> específico no nosso motor de busca se quiser navegar por essas coleções. Usamos registos correspondentes para preencher campos de metadados em falta (por exemplo, se um título estiver em falta), ou por exemplo, como \"título alternativo\" (se houver um título existente)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Para ver exatamente de onde vieram os metadados de um livro, veja o <em>separador \"Detalhes técnicos\"</em> na página de um livro. Tem um link para o JSON bruto desse livro, com apontadores para o JSON bruto dos registos originais."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Para mais informações, veja as seguintes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Pesquisa (separador de metadados)</a>, <a %(a_codes)s>Explorador de Códigos</a>, e <a %(a_example)s>Exemplo de JSON de metadados</a>. Finalmente, todos os nossos metadados podem ser <a %(a_generated)s>gerados</a> ou <a %(a_downloaded)s>descarregados</a> como bases de dados ElasticSearch e MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Ligação à Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Então, se encontrar um ficheiro com metadados incorretos, como deve corrigi-lo? Pode ir à biblioteca fonte e seguir os seus procedimentos para corrigir metadados, mas o que fazer se um ficheiro estiver presente em várias bibliotecas fonte?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Há um identificador que é tratado de forma especial no Arquivo da Anna. <strong>O campo md5 do annas_archive na Open Library sempre substitui todos os outros metadados!</strong> Vamos recuar um pouco e aprender sobre a Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "A Open Library foi fundada em 2006 por Aaron Swartz com o objetivo de \"uma página web para cada livro já publicado\". É uma espécie de Wikipedia para metadados de livros: todos podem editá-la, é licenciada livremente, e pode ser descarregada em massa. É uma base de dados de livros que está mais alinhada com a nossa missão — de facto, o Arquivo da Anna foi inspirado pela visão e vida de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Em vez de reinventar a roda, decidimos redirecionar os nossos voluntários para a Open Library. Se vir um livro com metadados incorretos, pode ajudar da seguinte forma:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Vá ao <a %(a_openlib)s>site da Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Encontre o registo correto do livro. <strong>AVISO:</strong> certifique-se de selecionar a <strong>edição</strong> correta. Na Open Library, tem \"obras\" e \"edições\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Uma \"obra\" poderia ser \"Harry Potter e a Pedra Filosofal\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Uma \"edição\" poderia ser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "A primeira edição de 1997 publicada pela Bloomsbery com 256 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "A edição de bolso de 2003 publicada pela Raincoast Books com 223 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "A tradução polaca de 2000 “Harry Potter I Kamie Filozoficzn” pela Media Rodzina com 328 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Todas essas edições têm ISBNs e conteúdos diferentes, por isso certifique-se de selecionar a correta!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edite o registo (ou crie-o se não existir) e adicione o máximo de informações úteis que puder! Já que está aqui, aproveite para tornar o registo realmente incrível."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Em “Números de ID” selecione “Arquivo da Anna” e adicione o MD5 do livro do Arquivo da Anna. Este é o longo conjunto de letras e números após “/md5/” no URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Tente encontrar outros ficheiros no Arquivo da Anna que também correspondam a este registo e adicione-os também. No futuro, podemos agrupar esses como duplicados na página de pesquisa do Arquivo da Anna."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando terminar, anote o URL que acabou de atualizar. Depois de atualizar pelo menos 30 registos com os MD5s do Arquivo da Anna, envie-nos um <a %(a_contact)s>email</a> com a lista. Daremos-lhe uma adesão gratuita ao Arquivo da Anna, para que possa fazer este trabalho mais facilmente (e como um agradecimento pela sua ajuda). Estas edições têm de ser de alta qualidade e adicionar uma quantidade substancial de informações, caso contrário, o seu pedido será rejeitado. O seu pedido também será rejeitado se alguma das edições for revertida ou corrigida pelos moderadores da Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Note que isto só funciona para livros, não para artigos académicos ou outros tipos de ficheiros. Para outros tipos de ficheiros, ainda recomendamos encontrar a biblioteca de origem. Pode demorar algumas semanas para que as alterações sejam incluídas no Arquivo da Anna, pois precisamos de descarregar o último dump de dados da Open Library e regenerar o nosso índice de pesquisa."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Espelhos: chamada para voluntários"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Para aumentar a resiliência do Arquivo da Anna, estamos à procura de voluntários para gerir espelhos."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Estamos à procura disto:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Você executa a base de código open source do Arquivo da Anna e atualiza regularmente tanto o código quanto os dados."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "A sua versão é claramente distinguida como um mirror, por exemplo, “Arquivo do Bob, um mirror do Arquivo da Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Você está disposto a assumir os riscos associados a este trabalho, que são significativos. Você tem um profundo entendimento da segurança operacional necessária. O conteúdo destes <a %(a_shadow)s>posts</a> <a %(a_pirate)s>é autoevidente para você</a>."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Você está disposto a contribuir para a nossa <a %(a_codebase)s>base de código</a> — em colaboração com a nossa equipa — para que isso aconteça."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inicialmente, não lhe daremos acesso aos downloads do nosso servidor parceiro, mas se tudo correr bem, podemos partilhar isso consigo."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Despesas de hospedagem"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Estamos dispostos a cobrir as despesas de hospedagem e VPN, inicialmente até $200 por mês. Isto é suficiente para um servidor de pesquisa básico e um proxy protegido por DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Só pagaremos pela hospedagem depois de ter tudo configurado e demonstrado que é capaz de manter o arquivo atualizado com as atualizações. Isto significa que terá de pagar pelos primeiros 1-2 meses do seu próprio bolso."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "O seu tempo não será compensado (e nem o nosso), pois este é um trabalho puramente voluntário."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se se envolver significativamente no desenvolvimento e operações do nosso trabalho, podemos discutir a partilha de mais receitas de doações consigo, para que possa utilizá-las conforme necessário."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Começando"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Por favor, <strong>não nos contacte</strong> para pedir permissão ou para perguntas básicas. As ações falam mais alto que palavras! Toda a informação está disponível, então vá em frente e configure o seu mirror."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Sinta-se à vontade para postar tickets ou pedidos de merge no nosso Gitlab quando encontrar problemas. Podemos precisar de construir algumas funcionalidades específicas para mirrors consigo, como rebranding de “Arquivo da Anna” para o nome do seu site, (inicialmente) desativar contas de utilizador, ou ligar de volta ao nosso site principal a partir das páginas dos livros."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Assim que tiver o seu mirror a funcionar, por favor contacte-nos. Adoraríamos rever a sua OpSec, e uma vez que esteja sólida, ligaremos ao seu mirror e começaremos a trabalhar mais de perto consigo."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Agradecemos antecipadamente a todos os que estão dispostos a contribuir desta forma! Não é para os fracos de coração, mas solidificaria a longevidade da maior biblioteca verdadeiramente aberta da história humana."

msgid "page.partner_download.header"
msgstr "Transferência de um website parceiro"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Transferências lentas apenas estão disponíveis através do website oficial. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Descarregamentos lentos não estão disponíveis através de VPNs da Cloudflare ou de endereços IP da Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Por favor, aguarde <span %(span_countdown)s>%(wait_seconds)s</span> segundos para baixar este ficheiro."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Transferir agora</a>"

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Obrigado por esperar, isso mantém o site acessível gratuitamente para todos! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Aviso: encontramos muitas transferências do teu endereço IP nas últimas 24 horas. As transferências podem ser mais lentas do que o habitual."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descarregamentos do seu endereço IP nas últimas 24 horas: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Se estiver a usar uma VPN, uma conexão de internet compartilhada ou se o seu ISP compartilhar IPs, este aviso pode ser devido a isso."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Para dar a todos a oportunidade de descarregar ficheiros gratuitamente, precisa de esperar antes de poder descarregar este ficheiro."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Sinta-se à vontade para continuar a navegar no Arquivo da Anna em uma aba diferente enquanto espera (se o seu navegador suportar a atualização de abas em segundo plano)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Sinta-se à vontade para esperar que várias páginas de download carreguem ao mesmo tempo (mas, por favor, baixe apenas um arquivo por vez por servidor)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Uma vez que você obtém um link de download, ele é válido por várias horas."

msgid "layout.index.header.title"
msgstr "Anna's Archive"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Registo no Anna's Archive"

msgid "page.scidb.download"
msgstr "Download"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Para apoiar a acessibilidade e preservação a longo prazo do conhecimento humano, torne-se um <a %(a_donate)s>membro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Como bônus, 🧬&nbsp;SciDB carrega mais rápido para membros, sem quaisquer limites."

msgid "page.scidb.refresh"
msgstr "Não está a funcionar? Tenta <a %(a_refresh)s>refrescar a página</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Pré-visualização ainda não disponível. Descarregue o ficheiro de <a %(a_path)s>Arquivo da Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB é uma continuação do Sci-Hub, com sua interface familiar e visualização direta de PDFs. Insira o seu DOI para visualizar."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Temos a coleção completa do Sci-Hub, bem como novos artigos. A maioria pode ser visualizada diretamente com uma interface familiar, semelhante ao Sci-Hub. Alguns podem ser baixados através de fontes externas, caso em que mostramos links para esses."

msgid "page.search.title.results"
msgstr "%(search_input)s - Procurar"

msgid "page.search.title.new"
msgstr "Nova pesquisa"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Incluir apenas"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Excluir"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Não verificado"

msgid "page.search.tabs.download"
msgstr "Download"

msgid "page.search.tabs.journals"
msgstr "Artigos científicos"

msgid "page.search.tabs.digital_lending"
msgstr "Digital Lending"

msgid "page.search.tabs.metadata"
msgstr "Metadados"

msgid "common.search.placeholder"
msgstr "Título, autor, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Pesquisar"

msgid "page.search.search_settings"
msgstr "Opções de pesquisa"

msgid "page.search.submit"
msgstr "Pesquisar"

msgid "page.search.too_long_broad_query"
msgstr "A pesquisa demorou demasiado tempo, o que é comum para pesquisas muito abrangentes. Os contadores nos filtros podem não estar corretos."

msgid "page.search.too_inaccurate"
msgstr "A pesquisa demorou demasiado tempo, os resultados podem não estar corretos. Por vezes <a %(a_reload)s>refrescar</a> a página ajuda."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Mostrar"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabela"

msgid "page.search.advanced.header"
msgstr "Opções avançadas"

msgid "page.search.advanced.description_comments"
msgstr "Pesquisar descrição e observações nos metadados"

msgid "page.search.advanced.add_specific"
msgstr "Adiciona um campo de pesquisa específico"

msgid "common.specific_search_fields.select"
msgstr "(campo de pesquisa específico)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Ano de publicação"

msgid "page.search.filters.content.header"
msgstr "Categoria"

msgid "page.search.filters.filetype.header"
msgstr "Tipo de ficheiro"

msgid "page.search.more"
msgstr "mais…"

msgid "page.search.filters.access.header"
msgstr "Acesso"

msgid "page.search.filters.source.header"
msgstr "Origem"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "extraído e disponibilizado como código aberto pela AA"

msgid "page.search.filters.language.header"
msgstr "Idioma"

msgid "page.search.filters.order_by.header"
msgstr "Ordenar por"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Mais relevante"

msgid "page.search.filters.sorting.newest"
msgstr "Mais recente"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(ano de publicação)"

msgid "page.search.filters.sorting.oldest"
msgstr "Mais antigo"

msgid "page.search.filters.sorting.largest"
msgstr "Maior"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(tamanho do ficheiro)"

msgid "page.search.filters.sorting.smallest"
msgstr "Mais pequeno"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(disponível publicamente)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aleatório"

msgid "page.search.header.update_info"
msgstr "O índica de pesquisa é atualizado mensalmente. Neste momento inclui entradas até %(last_data_refresh_date)s. Para mais informações técnicas, visita a página de %(link_open_tag)sDatasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Para explorar o índice de pesquisa por códigos, use o <a %(a_href)s>Explorador de Códigos</a>."

msgid "page.search.results.search_downloads"
msgstr "Pesquisa no nosso catálogo de %(count)s ficheiros disponíveis para download, <a %(a_preserve)s preservados para sempre</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Na verdade, qualquer pessoa pode ajudar a preservar esses arquivos semeando nossa <a %(a_torrents)s>lista unificada de torrents</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Temos atualmente o maior catálogo do mundo de livros, papers e outros trabalhos escritos. Fazemos mirror do Sci-Hub, Library Genesis, Z-Library e <a %(a_datasets)s>outros</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Se encontrares outras “shadow libraries” que achas que devem ser mirrored, ou se tiveres outras questões, por favor contact-nos através do email %(email)s."

msgid "page.search.results.dmca"
msgstr "Para DMCA/reclamações de direitos de autor <a %(a_copyright)s>clica aqui</a>."

msgid "page.search.results.shortcuts"
msgstr "Dica: usa os atalhos de teclado “/” (saltar para a pesquisa), “enter” (pesquisar), “j” (cima), “k” (baixo), “<” (página anterior), “>” (página seguinte) para uma navegação mais rápida."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "À procura de artigos?"

msgid "page.search.results.search_journals"
msgstr "Pesquisa no nosso catálogo de %(count)s papers e artigos científicos, <a %(a_preserve)s>preservados para sempre</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Pesquisa por ficheiros nas bibliotecas de \"digital lending\"."

msgid "page.search.results.digital_lending_info"
msgstr "Este índice de pesquisa inclui atualmente os metadados da biblioteca Controlled Digital Lending do Internet Archive. <a %(a_datasets)s>Mais informação sobre os nossos datasets</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Para mais bibliotecas de digital lending, visita a <a %(a_wikipedia)s>Wikipedia</a> e a <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Pesquisa nos metadados das bibliotecas. Isto pode ser útil para <a %(a_request)s>pedir um ficheiro</a>."

msgid "page.search.results.metadata_info"
msgstr "Este índice de pesquisa inclui metadados de várias fontes. <a %(a_datasets)s>Mais informação sobre os nossos datasets</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Nos metadados, mostramos os registos originais. Não fazemos nenhuma operação nestes registos."

msgid "page.search.results.metadata_info_more"
msgstr "Existem muitas fontes de metadados no mundo para trabalhos escritos. <a %(a_wikipedia)s>Esta página de Wikipedia</a> é um bom começo, mas se souberes de outras listas, por favor informa-nos."

msgid "page.search.results.search_generic"
msgstr "Escreve aqui para pesquisar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Estes são registos de metadados, <span %(classname)s>não</span> ficheiros para download."

msgid "page.search.results.error.header"
msgstr "Erro a pesquisar."

msgid "page.search.results.error.unknown"
msgstr "Tenta <a %(a_reload)s>refrescar a página</a>. Se o problema persistir, por favor entra em contacto através do email %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Nenhum ficheiro encontrado.</span> Tenta outras palavras ou diferentes filtros de pesquisa."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Às vezes isso acontece incorretamente quando o servidor de busca está lento. Nesses casos, <a %(a_attrs)s>recarregar</a> pode ajudar."

msgid "page.search.found_matches.main"
msgstr "Encontrámos resultados em: %(in)s. Podes utilizar este URL quando fores <a %(a_request)s>solicitar um ficheiro</a>."

msgid "page.search.found_matches.journals"
msgstr "Artigos Científicos (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Digital Lending (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadados (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Resultados %(from)s-%(to)s (%(total)s no total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ resultados parciais"

msgid "page.search.results.partial"
msgstr "%(num)d resultados parciais"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariado & Recompensas"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "O Arquivo da Anna depende de voluntários como você. Acolhemos todos os níveis de compromisso e temos duas principais categorias de ajuda que procuramos:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Trabalho leve de voluntariado:</span> se só pode disponibilizar algumas horas aqui e ali, ainda há muitas maneiras de ajudar. Recompensamos voluntários consistentes com <span %(bold)s>🤝 adesões ao Arquivo da Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Trabalho voluntário intenso (recompensas de USD$50-USD$5,000):</span> se puder dedicar muito tempo e/ou recursos à nossa missão, adoraríamos trabalhar mais de perto consigo. Eventualmente, poderá juntar-se à equipa interna. Embora tenhamos um orçamento apertado, conseguimos atribuir <span %(bold)s>💰 recompensas monetárias</span> para o trabalho mais intenso."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Se não puder oferecer o seu tempo como voluntário, ainda pode ajudar-nos muito ao <a %(a_donate)s>doar dinheiro</a>, <a %(a_torrents)s>semear os nossos torrents</a>, <a %(a_uploading)s>carregar livros</a> ou <a %(a_help)s>contar aos seus amigos sobre o Arquivo da Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Empresas:</span> oferecemos acesso direto e de alta velocidade às nossas coleções em troca de uma doação a nível empresarial ou em troca de novas coleções (por exemplo, novos scans, datasets OCR, enriquecimento dos nossos dados). <a %(a_contact)s>Contacte-nos</a> se for o seu caso. Veja também a nossa <a %(a_llm)s>página de LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariado leve"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Se tiver algumas horas livres, pode ajudar de várias maneiras. Certifique-se de se inscrever no <a %(a_telegram)s>chat de voluntários no Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Como forma de agradecimento, normalmente oferecemos 6 meses de “Bibliotecário Sortudo” por marcos básicos, e mais por trabalho voluntário contínuo. Todos os marcos exigem trabalho de alta qualidade — trabalho descuidado prejudica-nos mais do que ajuda e será rejeitado. Por favor, <a %(a_contact)s>envie-nos um email</a> quando atingir um marco."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tarefa"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Marco"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Divulgando a palavra do Arquivo da Anna. Por exemplo, recomendando livros no AA, ligando para as nossas postagens no blog, ou geralmente direcionando pessoas para o nosso site."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links ou capturas de ecrã."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Estas devem mostrar você informando alguém sobre o Arquivo da Anna, e essa pessoa agradecendo-lhe."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Melhorar metadados ao <a %(a_metadata)s>ligar</a> com a Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Pode usar a <a %(a_list)s>lista de problemas de metadata aleatórios</a> como ponto de partida."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Certifique-se de deixar um comentário sobre os problemas que resolver, para que outros não dupliquem o seu trabalho."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links de registos que melhorou."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traduzir</a> o site."

msgid "page.volunteering.table.translate.milestone"
msgstr "Traduzir completamente uma língua (se não estava quase concluída.)"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Melhorar a página da Wikipédia do Arquivo da Anna na sua língua. Incluir informações da página da Wikipédia do AA noutras línguas, e do nosso site e blog. Adicionar referências ao AA noutras páginas relevantes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link para o histórico de edições mostrando que fez contribuições significativas."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Atender pedidos de livros (ou artigos, etc.) nos fóruns da Z-Library ou da Library Genesis. Não temos o nosso próprio sistema de pedidos de livros, mas espelhamos essas bibliotecas, por isso melhorá-las também melhora o Arquivo da Anna."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links ou capturas de ecrã de pedidos que atendeu."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Pequenas tarefas postadas no nosso <a %(a_telegram)s>chat de voluntários no Telegram</a>. Normalmente para adesão, às vezes para pequenas recompensas."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Pequenas tarefas publicadas no nosso grupo de chat de voluntários."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depende da tarefa."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Recompensas"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Estamos sempre à procura de pessoas com sólidas competências em programação ou segurança ofensiva para se envolverem. Pode fazer uma diferença significativa na preservação do legado da humanidade."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Como agradecimento, oferecemos adesão por contribuições sólidas. Como um grande agradecimento, oferecemos recompensas monetárias para tarefas particularmente importantes e difíceis. Isto não deve ser visto como um substituto para um emprego, mas é um incentivo extra e pode ajudar com os custos incorridos."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "A maior parte do nosso código é open source, e pediremos o mesmo do seu código ao atribuir a recompensa. Existem algumas exceções que podemos discutir individualmente."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "As recompensas são atribuídas à primeira pessoa que completar uma tarefa. Sinta-se à vontade para comentar num bilhete de recompensa para informar os outros que está a trabalhar em algo, para que os outros possam esperar ou contactá-lo para formar uma equipa. Mas esteja ciente de que os outros ainda são livres para trabalhar nisso também e tentar superá-lo. No entanto, não atribuímos recompensas por trabalho descuidado. Se duas submissões de alta qualidade forem feitas próximas uma da outra (dentro de um ou dois dias), podemos optar por atribuir recompensas a ambas, a nosso critério, por exemplo, 100%% para a primeira submissão e 50%% para a segunda submissão (totalizando 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Para as recompensas maiores (especialmente recompensas de scraping), por favor contacte-nos quando tiver completado ~5%% dela, e estiver confiante de que o seu método será escalável para o marco completo. Terá de partilhar o seu método connosco para que possamos dar feedback. Além disso, desta forma podemos decidir o que fazer se houver várias pessoas a aproximarem-se de uma recompensa, como potencialmente atribuí-la a várias pessoas, encorajar as pessoas a formarem equipas, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVISO: as tarefas de alta recompensa são <span %(bold)s>difíceis</span> — pode ser sensato começar com as mais fáceis."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Vá à nossa <a %(a_gitlab)s>lista de problemas do Gitlab</a> e ordene por “Prioridade de etiqueta”. Isto mostra aproximadamente a ordem das tarefas que nos importam. Tarefas sem recompensas explícitas ainda são elegíveis para adesão, especialmente aquelas marcadas como “Aceite” e “Favorita da Anna”. Pode querer começar com um “Projeto inicial”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Atualizações sobre o <a %(wikipedia_annas_archive)s>Arquivo da Anna</a>, a maior biblioteca verdadeiramente aberta da história da humanidade."

msgid "layout.index.title"
msgstr "Anna's Archive"

msgid "layout.index.meta.description"
msgstr "A maior biblioteca livre do mundo. Integração com Sci-Hub, Library Genesis, Z-Library e outros."

msgid "layout.index.meta.opensearch"
msgstr "Procurar no Anna's Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "O Arquivo da Anna precisa da sua ajuda!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Muitos tentam nos derrubar, mas nós lutamos de volta."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se doar agora, recebe <strong>o dobro</strong> do número de downloads rápidos."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Válido até ao final deste mês."

msgid "layout.index.header.nav.donate"
msgstr "Fazer um donativo"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Preservar o conhecimento humano: um excelente presente de Natal!"

msgid "layout.index.header.banner.surprise"
msgstr "Faz uma surpresa a alguém, oferece-lhe uma conta com subscrição."

msgid "layout.index.header.banner.mirrors"
msgstr "Para aumentar a resiliência do Anna's Archive, estamos à procura de voluntários para montar mirrors."

msgid "layout.index.header.banner.valentine_gift"
msgstr "A prenda perfeita de S. Valentim!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Temos um novo método de pagamento disponível: %(method_name)s. Por favor considera %(donate_link_open_tag)sfazer um donativo</a> — não é barato suportar este website e os vossos donativos fazem uma grande diferença. Muito obrigado."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Lançamos uma angariação de fundos para <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">fazer backup</a> da maior shadow library de banda desenhada do mundo. Muito obrigado pela vossa ajuda! <a href=\"/donate\">Fazer um donativo.</a> Se não conseguires fazer um donativo, ajuda-nos a espalhar a palavra falando com os teus amigos e segue-nos no <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> ou no <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Downloads recentes:"

msgid "layout.index.header.nav.search"
msgstr "Pesquisar"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Melhorar metadados"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariado & Recompensas"

msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Atividade"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorador de Códigos"

msgid "layout.index.header.nav.llm_data"
msgstr "dados para LLMs"

msgid "layout.index.header.nav.home"
msgstr "Início"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduzir ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Iniciar sessão/Registar"

msgid "layout.index.header.nav.account"
msgstr "Conta"

msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

msgid "layout.index.footer.list2.header"
msgstr "Contactos"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA/Reclamações de direitos de autor"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avançado"

msgid "layout.index.header.nav.security"
msgstr "Segurança"

msgid "layout.index.footer.list3.header"
msgstr "Alternativas"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "não afiliado"

msgid "page.search.results.issues"
msgstr "❌ Este ficheiro pode ter problemas."

msgid "page.search.results.fast_download"
msgstr "Transferência rápida"

msgid "page.donate.copy"
msgstr "copiar"

msgid "page.donate.copied"
msgstr "copiado!"

msgid "page.search.pagination.prev"
msgstr "Anterior"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Seguinte"

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Donativo no total de %(total)s usando a conta Alipay <a %(a_account)s>"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Em alternativa, podes fazer o upload para a Z-Library <a %(a_upload)s>aqui</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Para aumentar a resiliência do Anna's Archive, estamos à procura de voluntários para montar mirrors. <a href=\"/mirrors\">Saber mais…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Mirrors: procuramos voluntários"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "apenas este mês!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "O Sci-Hub <a %(a_closed)s>pausou</a> o upload de novos artigos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Seleciona um método de pagamento. Descontos em pagamentos crypto %(bitcoin_icon)s, as comissões que nos cobram são muito mais baixas."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Seleciona um método de pagamento. Neste momento, só aceitamos pagamentos em crypto %(bitcoin_icon)s visto que os fornecedores de pagamento tradicionais recusam-se a trabalhar connosco."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Não podemos aceitar cartões de crédito/débito diretamente, porque os bancos não querem trabalhar connosco. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "No entanto, existem várias maneiras de usar cartões de crédito/débito, utilizando os nossos outros métodos de pagamento:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Transferências externas lentas"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descarregamentos"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se estás a usar crypto pela primeira vez, sugerimos que uses %(option1)s, %(option2)s ou %(option3)s para comprar e fazeres o teu donativo em Bitcoin (a criptomoeda original e a mais usada)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links de registos que melhorou."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links ou capturas de ecrã."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links ou capturas de ecrã dos pedidos que atendeu."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se estiver interessado em espelhar estes datasets para <a %(a_faq)s>arquivamento</a> ou para fins de <a %(a_llm)s>treino de LLM</a>, por favor contacte-nos."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se estiver interessado em espelhar este conjunto de dados para <a %(a_archival)s>arquivamento</a> ou para fins de <a %(a_llm)s>treino de LLM</a>, por favor contacte-nos."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Site principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informação sobre o país do ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se estiver interessado em espelhar este conjunto de dados para fins de <a %(a_archival)s>arquivamento</a> ou <a %(a_llm)s>treino de LLM</a>, por favor contacte-nos."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "A Agência Internacional do ISBN lança regularmente os intervalos que alocou às agências nacionais do ISBN. A partir disso, podemos derivar a que país, região ou grupo linguístico pertence este ISBN. Atualmente, usamos esses dados indiretamente, através da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Recursos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Última atualização: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Website do ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadados"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluindo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "A nossa inspiração para a recolha de metadados é o objetivo de Aaron Swartz de “uma página web para cada livro alguma vez publicado”, para o qual ele criou a <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Esse projeto tem tido sucesso, mas a nossa posição única permite-nos obter metadados que eles não conseguem."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Outra inspiração foi o nosso desejo de saber <a %(a_blog)s>quantos livros existem no mundo</a>, para podermos calcular quantos livros ainda temos por salvar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Para dar a todos a oportunidade de baixar arquivos gratuitamente, você precisa esperar <strong>%(wait_seconds)s segundos</strong> antes de poder baixar este arquivo."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Atualizar página automaticamente. Se perder a janela de download, o temporizador será reiniciado, por isso é recomendada a atualização automática."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Transferir agora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converter: use ferramentas online para converter entre formatos. Por exemplo, para converter entre epub e pdf, use <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descarregue o ficheiro (pdf ou epub são suportados), depois <a %(a_kindle)s>envie-o para o Kindle</a> usando a web, app ou email. Ferramentas úteis: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Ajuda os autores: Se gostaste e tiveres possibilidade, considera comprar o original ou ajudar diretamente os autores."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Ajuda as bibliotecas: Se este livro estiver disponível numa biblioteca local, considera pedir emprestado gratuitamente."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Não disponível diretamente em massa, apenas em semi-massa atrás de um paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s O Arquivo da Anna gere uma coleção de <a %(isbndb)s>metadados ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "A ISBNdb é uma empresa que extrai dados de várias livrarias online para encontrar metadados de ISBN. O Arquivo da Anna tem feito backups dos metadados de livros da ISBNdb. Esses metadados estão disponíveis através do Arquivo da Anna (embora atualmente não estejam na pesquisa, exceto se você procurar explicitamente por um número ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Para detalhes técnicos, veja abaixo. Em algum momento, podemos usá-los para determinar quais livros ainda estão faltando nas bibliotecas sombra, a fim de priorizar quais livros encontrar e/ou digitalizar."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nosso post no blog sobre esses dados"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Extração da ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Atualmente, temos um único torrent, que contém um arquivo de 4,4GB em formato gzip <a %(a_jsonl)s>JSON Lines</a> (20GB descompactado): “isbndb_2022_09.jsonl.gz”. Para importar um arquivo “.jsonl” no PostgreSQL, você pode usar algo como <a %(a_script)s>este script</a>. Você pode até mesmo canalizá-lo diretamente usando algo como %(example_code)s para que ele descompacte em tempo real."

#~ msgid "page.donate.wait"
#~ msgstr "Por favor espera pelo menos <span %(span_hours)s>duas horas</span> (e refresca esta página) antes de entrares em contacto connosco."

#~ msgid "page.codes.search_archive"
#~ msgstr "Pesquisar no Arquivo da Anna por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Doar através de Alipay ou WeChat. Podes escolher entre estes dois serviços no próximo passo."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Divulgar o Arquivo da Anna nas redes sociais e fóruns online, recomendando livros ou listas no AA, ou respondendo a perguntas."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s A coleção de Ficção divergiu, mas ainda tem <a %(libgenli)s>torrents</a>, embora não atualizados desde 2022 (temos downloads diretos)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s O Arquivo da Anna e o Libgen.li gerem colaborativamente coleções de <a %(comics)s>bandas desenhadas</a> e <a %(magazines)s>revistas</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Não há torrents para coleções de ficção russa e documentos padrão."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Não há torrents disponíveis para o conteúdo adicional. Os torrents que estão no site Libgen.li são mirrors de outros torrents listados aqui. A única exceção são os torrents de ficção a partir de %(fiction_starting_point)s. Os torrents de quadrinhos e revistas são lançados como uma colaboração entre o Arquivo da Anna e o Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "De uma coleção <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origem exata desconhecida. Parte do the-eye.eu, parte de outras fontes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

