#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Nederīgs pieprasījums. Apmeklējiet %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Aizdevumu bibliotēka"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " un "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "un vēl"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;<PERSON><PERSON><PERSON> spo<PERSON> %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Mēs nokasām un atveram avotu %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Visi mūsu kodi un dati ir pilnīgi atvērti."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Lielākā patiesi atvērtā bibliotēka cilvēces vēsturē."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;grāmatas, %(paper_count)s&nbsp;raksti — saglabāti uz visiem laikiem."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka. ⭐️&nbsp;Spoguļo Sci-Hub, Library Genesis, Z-Library un vēl. 📈&nbsp;%(book_any)s grāmatas, %(journal_article)s raksti, %(book_comic)s komiksi, %(magazine)s žurnāli — saglabāti uz visiem laikiem."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka.<br>⭐️ Spoguļo Scihub, Libgen, Zlib un vēl."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Nepareiza metadati (piemēram, nosaukums, apraksts, vāka attēls)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Lejupielādes problēmas (piemēram, nevar pieslēgties, kļūdas ziņojums, ļoti lēni)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Failu nevar atvērt (piemēram, bojāts fails, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Slikta kvalitāte (piemēram, formatēšanas problēmas, slikta skenēšanas kvalitāte, trūkstošas lapas)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spams / fails jānoņem (piemēram, reklāmas, aizskarošs saturs)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Autortiesību prasība"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Cits"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonusa lejupielādes"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brīnišķīgais Bibliofils"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Laimes Bibliotekārs"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Dzirkstošais Datuvācējs"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Apbrīnojamais Arhīvists"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s kopā"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) kopā"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonuss)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "neapmaksāts"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "apmaksāts"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "atcelts"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "beidzies"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "gaida Annu, lai apstiprinātu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "nederīgs"

#, fuzzy
msgid "page.donate.title"
msgstr "Ziedot"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Jums ir <a %(a_donation)s>esošs ziedojums</a> procesā. Lūdzu, pabeidziet vai atceliet šo ziedojumu, pirms veicat jaunu ziedojumu."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Skatīt visus manus ziedojumus</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Annas Arhīvs ir bezpeļņas, atvērtā koda, atvērto datu projekts. Ziedojot un kļūstot par biedru, jūs atbalstāt mūsu darbību un attīstību. Visiem mūsu biedriem: paldies, ka palīdzat mums turpināt! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Lai iegūtu vairāk informācijas, skatiet <a %(a_donate)s>Ziedojumu BUJ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Lai iegūtu vēl vairāk lejupielāžu, <a %(a_refer)s>aiciniet savus draugus</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Jūs saņemat %(percentage)s%% papildu ātrās lejupielādes, jo jūs ieteica lietotājs %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Tas attiecas uz visu biedru periodu."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s ātrās lejupielādes dienā"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "ja ziedosiet šomēnes!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mēnesī"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Pievienoties"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Izvēlēts"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "līdz %(percentage)s%% atlaidēm"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB raksti <strong>neierobežoti</strong> bez verifikācijas"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> piekļuve"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Nopelniet <strong>%(percentage)s%% papildu lejupielādes</strong>, <a %(a_refer)s>aicinot draugus</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Jūsu lietotājvārds vai anonīms pieminējums kredītos"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Iepriekšējie labumi, kā arī:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Agrīna piekļuve jaunām funkcijām"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Ekskluzīvs Telegram ar aizkulišu atjauninājumiem"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adoptēt torrentu”: jūsu lietotājvārds vai ziņojums torrent faila nosaukumā <div %(div_months)s>reizi 12 mēnešos pēc dalības</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Leģendārs statuss cilvēces zināšanu un kultūras saglabāšanā"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Eksperta piekļuve"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "sazinieties ar mums"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Mēs esam neliela brīvprātīgo komanda. Atbilde var aizņemt 1-2 nedēļas."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Neierobežota</strong> ātrgaitas piekļuve"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Tiešie <strong>SFTP</strong> serveri"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Uzņēmuma līmeņa ziedojums vai apmaiņa pret jaunām kolekcijām (piemēram, jauni skenējumi, OCR dati)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Mēs priecājamies par lieliem ziedojumiem no turīgiem indivīdiem vai iestādēm. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Ziedojumiem virs $5000, lūdzu, sazinieties ar mums tieši pa %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Ņemiet vērā, ka, lai gan šajā lapā norādītās dalības ir \"mēnesī\", tās ir vienreizējas ziedojumi (neatkārtojas). Skatiet <a %(faq)s>Ziedojumu BUJ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Ja vēlaties veikt ziedojumu (jebkuru summu) bez dalības, droši izmantojiet šo Monero (XMR) adresi: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Lūdzu, izvēlieties maksājuma metodi."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(pagaidām nav pieejams)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s dāvanu karte"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankas karte (izmantojot lietotni)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Kriptovalūta %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredītkarte/debetkarte"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (ASV) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regulārs)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Karte / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredītkarte/debetkarte/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankas karte"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredītkarte/debetkarte (rezerves)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredītkarte/debetkarte 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Ar kriptovalūtu jūs varat ziedot, izmantojot BTC, ETH, XMR un SOL. Izmantojiet šo iespēju, ja jau esat pazīstams ar kriptovalūtu."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Ar kriptovalūtu jūs varat ziedot, izmantojot BTC, ETH, XMR un citas."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Ja jūs pirmo reizi izmantojat kriptovalūtu, mēs iesakām izmantot %(options)s, lai iegādātos un ziedotu Bitcoin (oriģinālo un visbiežāk izmantoto kriptovalūtu)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Lai ziedotu, izmantojot PayPal US, mēs izmantosim PayPal Crypto, kas ļauj mums palikt anonīmiem. Mēs novērtējam, ka veltāt laiku, lai iemācītos ziedot, izmantojot šo metodi, jo tas mums ļoti palīdz."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Ziedojiet, izmantojot PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Ziedot, izmantojot Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Ja jums ir Cash App, tas ir vienkāršākais veids, kā ziedot!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Ņemiet vērā, ka par darījumiem, kas ir mazāki par %(amount)s, Cash App var iekasēt %(fee)s maksu. Par %(amount)s vai vairāk, tas ir bez maksas!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Ziedot, izmantojot Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Ja jums ir Revolut, tas ir visvieglākais veids, kā ziedot!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Ziedot ar kredītkarti vai debetkarti."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay un Apple Pay arī varētu darboties."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Ņemiet vērā, ka mazu ziedojumu gadījumā kredītkartes maksas var iznīcināt mūsu %(discount)s%% atlaidi, tāpēc mēs iesakām ilgākus abonementus."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Ņemiet vērā, ka mazu ziedojumu gadījumā maksas ir augstas, tāpēc mēs iesakām ilgākus abonementus."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Ar Binance jūs pērkat Bitcoin ar kredītkarti/debetkarti vai bankas kontu un pēc tam ziedojat šo Bitcoin mums. Tādā veidā mēs varam palikt droši un anonīmi, pieņemot jūsu ziedojumu."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance ir pieejams gandrīz katrā valstī un atbalsta lielāko daļu banku un kredītkartes/debetkartes. Šobrīd tas ir mūsu galvenais ieteikums. Mēs novērtējam, ka jūs veltāt laiku, lai iemācītos ziedot, izmantojot šo metodi, jo tas mums ļoti palīdz."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Ziedot, izmantojot savu regulāro PayPal kontu."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Ziedot, izmantojot kredītkarti/debetkarti, PayPal vai Venmo. Jūs varat izvēlēties starp šiem nākamajā lapā."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Ziedot, izmantojot Amazon dāvanu karti."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Ņemiet vērā, ka mums ir jānoapaļo summas, ko pieņem mūsu tālākpārdevēji (minimums %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>SVARĪGI:</strong> Mēs atbalstām tikai Amazon.com, nevis citus Amazon tīmekļa vietnes. Piemēram, .de, .co.uk, .ca, NAV atbalstīti."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>SVARĪGI:</strong> Šī opcija ir paredzēta %(amazon)s. Ja vēlaties izmantot citu Amazon vietni, izvēlieties to augstāk."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Šī metode izmanto kriptovalūtas nodrošinātāju kā starpnieku konversijai. Tas var būt nedaudz mulsinoši, tāpēc, lūdzu, izmantojiet šo metodi tikai tad, ja citas maksājumu metodes nedarbojas. Tā arī nedarbojas visās valstīs."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Ziedojiet, izmantojot kredītkarti/debetkarti, caur Alipay lietotni (ļoti viegli iestatīt)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instalējiet Alipay lietotni"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instalējiet Alipay lietotni no <a %(a_app_store)s>Apple App Store</a> vai <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Reģistrējieties, izmantojot savu tālruņa numuru."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nav nepieciešama papildu personiskā informācija."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Pievienojiet bankas karti"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Atbalstītās: Visa, MasterCard, JCB, Diners Club un Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Lai iegūtu vairāk informācijas, skatiet <a %(a_alipay)s>šo ceļvedi</a>."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Mēs nevaram tieši atbalstīt kredītkartes/debetkartes, jo bankas nevēlas ar mums sadarboties. ☹ Tomēr ir vairāki veidi, kā izmantot kredītkartes/debetkartes, izmantojot citas maksājumu metodes:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon dāvanu karte"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Sūtiet mums Amazon.com dāvanu kartes, izmantojot savu kredītkarti/debetkarti."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay atbalsta starptautiskās kredītkartes/debetkartes. Skatiet <a %(a_alipay)s>šo ceļvedi</a> vairāk informācijas."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) atbalsta starptautiskās kredītkartes/debetkartes. WeChat lietotnē dodieties uz “Me => Services => Wallet => Add a Card”. Ja to neredzat, aktivizējiet to, izmantojot “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Jūs varat iegādāties kriptovalūtu, izmantojot kredītkartes/debetkartes."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Kripto ekspress pakalpojumi"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Ekspress pakalpojumi ir ērti, bet tiem ir augstākas maksas."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Jūs varat izmantot šo, nevis kripto biržu, ja vēlaties ātri veikt lielāku ziedojumu un jums nav iebildumu pret maksu $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Pārliecinieties, ka nosūtāt precīzu kripto summu, kas norādīta ziedojumu lapā, nevis summu $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Pretējā gadījumā maksa tiks atskaitīta, un mēs nevarēsim automātiski apstrādāt jūsu dalību."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimums: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimums: %(minimum)s atkarībā no valsts, nav nepieciešama verifikācija pirmajai transakcijai)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimums: %(minimum)s, nav nepieciešama verifikācija pirmajai transakcijai)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimums: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimums: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimums: %(minimum)s, nav nepieciešama verifikācija pirmajai transakcijai)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Ja kāda no šīm informācijām ir novecojusi, lūdzu, rakstiet mums e-pastu, lai mēs to varētu atjaunināt."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Kredītkartēm, debetkartēm, Apple Pay un Google Pay mēs izmantojam “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Viņu sistēmā viena “kafija” ir vienāda ar $5, tāpēc jūsu ziedojums tiks noapaļots līdz tuvākajam 5 reizinājumam."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Izvēlieties, cik ilgi vēlaties abonēt."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mēnesis"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 mēneši"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 mēneši"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 mēneši"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 mēneši"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 mēneši"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 mēneši"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>pēc <span %(span_discount)s></span> atlaidēm</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Šī maksājuma metode prasa minimālo summu %(amount)s. Lūdzu, izvēlieties citu ilgumu vai maksājuma metodi."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Ziedot"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Šī maksājuma metode atļauj maksimālo summu %(amount)s. Lūdzu, izvēlieties citu ilgumu vai maksājuma metodi."

#, fuzzy
msgid "page.donate.login2"
msgstr "Lai kļūtu par biedru, lūdzu, <a %(a_login)s>Piesakieties vai Reģistrējieties</a>. Paldies par jūsu atbalstu!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Izvēlieties savu vēlamo kriptovalūtu:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(zemākā minimālā summa)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(izmantojiet, sūtot Ethereum no Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(brīdinājums: augsta minimālā summa)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Noklikšķiniet uz ziedojuma pogas, lai apstiprinātu šo ziedojumu."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Ziedot <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Jūs joprojām varat atcelt ziedojumu izrakstīšanās laikā."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Pāradresācija uz ziedojumu lapu…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mēnesī"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "uz 1 mēnesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "uz 3 mēnešiem"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "uz 6 mēnešiem"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "uz 12 mēnešiem"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "uz 24 mēnešiem"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "uz 48 mēnešiem"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "uz 96 mēnešiem"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "uz 1 mēnesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "uz 3 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "uz 6 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "uz 12 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "uz 24 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "uz 48 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "uz 96 mēnešiem “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Ziedojums"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Datums: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Kopā: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mēnesī uz %(duration)s mēnešiem, ieskaitot %(discounts)s%% atlaidi)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Kopā: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mēnesī uz %(duration)s mēnešiem)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifikators: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Atcelt"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Vai tiešām vēlaties atcelt? Neatceliet, ja jau esat samaksājis."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Jā, lūdzu atcelt"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Jūsu ziedojums ir atcelts."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Veikt jaunu ziedojumu"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Pārkārtot"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Jūs jau esat samaksājis. Ja vēlaties pārskatīt maksājuma instrukcijas, noklikšķiniet šeit:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Rādīt vecās maksājuma instrukcijas"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Paldies par jūsu ziedojumu!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Ja vēl neesat to izdarījis, pierakstiet savu slepeno atslēgu, lai pieteiktos:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Pretējā gadījumā jūs varat zaudēt piekļuvi šim kontam!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Maksājuma instrukcijas ir novecojušas. Ja vēlaties veikt citu ziedojumu, izmantojiet pogu “Pārkārtot” augstāk."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Svarīga piezīme:</strong> Kriptovalūtu cenas var svārstīties ļoti strauji, dažreiz pat par 20%% dažu minūšu laikā. Tas joprojām ir mazāk nekā maksas, ko mēs maksājam daudziem maksājumu pakalpojumu sniedzējiem, kuri bieži iekasē 50-60%% par darbu ar tādu “ēnu labdarību” kā mēs. <u>Ja jūs mums nosūtīsiet kvīti ar sākotnējo samaksāto cenu, mēs joprojām piešķirsim jūsu kontam izvēlēto dalību</u> (ja kvīts nav vecāka par dažām stundām). Mēs ļoti novērtējam, ka esat gatavs izturēt šādas lietas, lai mūs atbalstītu! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Šis ziedojums ir beidzies. Lūdzu, atceliet un izveidojiet jaunu."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Kripto instrukcijas"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Pārskaitiet uz vienu no mūsu kripto kontiem"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Ziedojiet kopējo summu %(total)s uz kādu no šīm adresēm:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Pērciet Bitcoin vietnē Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Atrodiet “Kripto” lapu savā PayPal lietotnē vai vietnē. Parasti tā atrodas sadaļā “Finanses”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Izpildiet norādījumus, lai iegādātos Bitcoin (BTC). Jums tikai jāiegādājas summa, kuru vēlaties ziedot, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Pārskaitiet Bitcoin uz mūsu adresi"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Dodieties uz “Bitcoin” lapu savā PayPal lietotnē vai vietnē. Nospiediet pogu “Pārskaitīt” %(transfer_icon)s, un pēc tam “Sūtīt”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Ievadiet mūsu Bitcoin (BTC) adresi kā saņēmēju un izpildiet norādījumus, lai nosūtītu savu ziedojumu %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredītkartes / debetkartes norādījumi"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Ziedojiet caur mūsu kredītkartes / debetkartes lapu"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Ziedojiet %(amount)s <a %(a_page)s>šajā lapā</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Skatiet soli pa solim ceļvedi zemāk."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Statuss:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Gaida apstiprinājumu (atsvaidziniet lapu, lai pārbaudītu)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Gaida pārskaitījumu (atsvaidziniet lapu, lai pārbaudītu)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Atlikušais laiks:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(jūs varētu vēlēties atcelt un izveidot jaunu ziedojumu)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Lai atiestatītu taimeri, vienkārši izveidojiet jaunu ziedojumu."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Atjaunināt statusu"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Ja rodas kādas problēmas, lūdzu, sazinieties ar mums pa %(email)s un iekļaujiet pēc iespējas vairāk informācijas (piemēram, ekrānuzņēmumus)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Ja esat jau samaksājis:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Dažreiz apstiprināšana var aizņemt līdz 24 stundām, tāpēc noteikti atjauniniet šo lapu (pat ja tā ir beidzies derīguma termiņš)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Iegādājieties PYUSD monētu PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Izpildiet norādījumus, lai iegādātos PYUSD monētu (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Nopērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojat (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Dodieties uz “PYUSD” lapu savā PayPal lietotnē vai vietnē. Nospiediet pogu “Pārskaitīt” %(icon)s, un pēc tam “Sūtīt”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Pārskaitiet %(amount)s uz %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Pirkt Bitcoin (BTC) lietotnē Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Dodieties uz “Bitcoin” (BTC) lapu lietotnē Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Pērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojamā summa (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Pārskaitiet Bitcoin uz mūsu adresi"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Noklikšķiniet uz pogas “Sūtīt bitcoin”, lai veiktu “izņemšanu”. Pārslēdzieties no dolāriem uz BTC, nospiežot %(icon)s ikonu. Ievadiet BTC summu zemāk un noklikšķiniet uz “Sūtīt”. Ja esat apmulsis, skatiet <a %(help_video)s>šo video</a>."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Mazām ziedojumiem (zem $25) jums, iespējams, būs jāizmanto Rush vai Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Pirkt Bitcoin (BTC) lietotnē Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Dodieties uz “Crypto” lapu lietotnē Revolut, lai iegādātos Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Pērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojamā summa (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Pārskaitiet Bitcoin uz mūsu adresi"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Noklikšķiniet uz pogas “Sūtīt bitcoin”, lai veiktu “izņemšanu”. Pārslēdzieties no eiro uz BTC, nospiežot %(icon)s ikonu. Ievadiet BTC summu zemāk un noklikšķiniet uz “Sūtīt”. Ja esat apmulsis, skatiet <a %(help_video)s>šo video</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Noteikti izmantojiet zemāk norādīto BTC summu, <em>NE</em> eiro vai dolārus, citādi mēs nesaņemsim pareizo summu un nevarēsim automātiski apstiprināt jūsu dalību."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Mazām ziedojumiem (zem $25) jums, iespējams, būs jāizmanto Rush vai Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Izmantojiet kādu no šiem “kredītkartes uz Bitcoin” ekspress pakalpojumiem, kas aizņem tikai dažas minūtes:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Aizpildiet šādu informāciju formā:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin summa:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Lūdzu, izmantojiet šo <span %(underline)s>precīzo summu</span>. Jūsu kopējās izmaksas var būt augstākas kredītkartes maksas dēļ. Mazām summām tas diemžēl var pārsniegt mūsu atlaidi."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adrese (ārējais maks):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instrukcijas"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Mēs atbalstām tikai standarta kriptovalūtu versijas, nevis eksotiskus tīklus vai monētu versijas. Atkarībā no monētas, transakcijas apstiprināšana var aizņemt līdz stundai."

msgid "page.donation.crypto_qr_code_title"
msgstr "Skenējiet QR kodu, lai samaksātu"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Skenējiet šo QR kodu ar savu kripto maka lietotni, lai ātri aizpildītu maksājuma informāciju"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon dāvanu karte"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Lūdzu, izmantojiet <a %(a_form)s>oficiālo Amazon.com formu</a>, lai nosūtītu mums dāvanu karti %(amount)s uz zemāk norādīto e-pasta adresi."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Mēs nevaram pieņemt citas dāvanu karšu metodes, <strong>tikai nosūtītas tieši no oficiālās formas Amazon.com</strong>. Mēs nevaram atgriezt jūsu dāvanu karti, ja neizmantojat šo formu."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Ievadiet precīzu summu: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Lūdzu, NERAKSTIET savu ziņojumu."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“Kam” saņēmēja e-pasts formā:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unikāls jūsu kontam, nedalieties."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Izmantojiet tikai vienu reizi."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Gaidām dāvanu karti… (atsvaidziniet lapu, lai pārbaudītu)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Pēc dāvanu kartes nosūtīšanas mūsu automatizētā sistēma to apstiprinās dažu minūšu laikā. Ja tas nedarbojas, mēģiniet nosūtīt dāvanu karti vēlreiz (<a %(a_instr)s>instrukcijas</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Ja tas joprojām nedarbojas, lūdzu, rakstiet mums e-pastu, un Anna to manuāli pārskatīs (tas var aizņemt dažas dienas), un noteikti pieminiet, ja jau esat mēģinājis nosūtīt vēlreiz."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Piemērs:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Ņemiet vērā, ka konta nosaukums vai attēls var izskatīties dīvaini. Nav jāuztraucas! Šos kontus pārvalda mūsu ziedojumu partneri. Mūsu konti nav uzlauzti."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay instrukcijas"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Ziedojiet Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo Alipay kontu</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Ja ziedojumu lapa tiek bloķēta, mēģiniet izmantot citu interneta savienojumu (piemēram, VPN vai telefona internetu)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Diemžēl Alipay lapa bieži ir pieejama tikai no <strong>Ķīnas kontinentālās daļas</strong>. Jums, iespējams, būs īslaicīgi jāatspējo savs VPN vai jāizmanto VPN uz Ķīnas kontinentālo daļu (vai dažreiz arī Honkonga darbojas)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Veiciet ziedojumu (noskenējiet QR kodu vai nospiediet pogu)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Atveriet <a %(a_href)s>QR koda ziedojumu lapu</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Noskenējiet QR kodu ar Alipay lietotni vai nospiediet pogu, lai atvērtu Alipay lietotni."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Lūdzu, esiet pacietīgi; lapas ielāde var aizņemt kādu laiku, jo tā atrodas Ķīnā."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat instrukcijas"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Ziedot, izmantojot WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo WeChat kontu</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix instrukcijas"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Ziedot, izmantojot Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo Pix kontu</a>"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Nosūtiet mums kvīti pa e-pastu"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Nosūtiet kvīti vai ekrānuzņēmumu uz savu personīgo verifikācijas adresi. Nelietojiet šo e-pasta adresi PayPal ziedojumam."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Nosūtiet kvīti vai ekrānuzņēmumu uz jūsu personīgo verifikācijas adresi:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Ja kriptovalūtas maiņas kurss darījuma laikā mainījās, noteikti iekļaujiet kvīti, kurā redzams sākotnējais maiņas kurss. Mēs ļoti novērtējam, ka izmantojat kriptovalūtu, tas mums ļoti palīdz!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Kad esat nosūtījis kvīti pa e-pastu, noklikšķiniet uz šīs pogas, lai Anna to manuāli pārskatītu (tas var aizņemt dažas dienas):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Jā, es nosūtīju kvīti pa e-pastu"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Paldies par jūsu ziedojumu! Anna manuāli aktivizēs jūsu dalību dažu dienu laikā."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Soli pa solim ceļvedis"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Daži soļi piemin kriptovalūtas makus, bet neuztraucieties, jums nav jāapgūst nekas par kriptovalūtu."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Ievadiet savu e-pastu."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Izvēlieties maksājuma metodi."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Vēlreiz izvēlieties maksājuma metodi."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Izvēlieties “Pašhostēts” maks."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Noklikšķiniet uz “Es apstiprinu īpašumtiesības”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Jums vajadzētu saņemt e-pasta kvīti. Lūdzu, nosūtiet to mums, un mēs pēc iespējas ātrāk apstiprināsim jūsu ziedojumu."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Lūdzu, uzgaidiet vismaz <span %(span_hours)s>24 stundas</span> (un atsvaidziniet šo lapu) pirms sazināties ar mums."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Ja maksājuma laikā pieļāvāt kļūdu, mēs nevaram veikt atmaksu, bet centīsimies to labot."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Mani ziedojumi"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Ziedojumu detaļas netiek publiski rādītas."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Vēl nav ziedojumu. <a %(a_donate)s>Veikt manu pirmo ziedojumu.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Veikt vēl vienu ziedojumu."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Lejupielādētie faili"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Lejupielādes no Ātrajiem Partneru Serveriem ir atzīmētas ar %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Ja lejupielādējāt failu ar gan ātrām, gan lēnām lejupielādēm, tas parādīsies divreiz."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Ātrās lejupielādes pēdējo 24 stundu laikā tiek ieskaitītas dienas limitā."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Visi laiki ir UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Lejupielādētie faili netiek publiski rādīti."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Vēl nav lejupielādētu failu."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Pēdējās 18 stundas"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Agrāk"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Konts"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Pieteikties / Reģistrēties"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Konta ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Publiskais profils: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Slepenā atslēga (nedalieties!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "rādīt"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Dalība: <strong>%(tier_name)s</strong> līdz %(until_date)s <a %(a_extend)s>(pagarināt)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Dalība: <strong>Nav</strong> <a %(a_become)s>(kļūt par biedru)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Ātrie lejupielādes izmantoti (pēdējās 24 stundās): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "kuras lejupielādes?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Ekskluzīva Telegram grupa: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Pievienojieties mums šeit!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Pārejiet uz <a %(a_tier)s>augstāku līmeni</a>, lai pievienotos mūsu grupai."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Sazinieties ar Annu pa %(email)s, ja vēlaties paaugstināt savu dalību uz augstāku līmeni."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontakta e-pasts"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Jūs varat apvienot vairākas dalības (ātrās lejupielādes 24 stundu laikā tiks summētas)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Publiskais profils"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Lejupielādētie faili"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Mani ziedojumi"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Izrakstīties"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Jūs esat izrakstījies. Pārlādējiet lapu, lai atkal pieteiktos."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Reģistrācija veiksmīga! Jūsu slepenā atslēga ir: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Saglabājiet šo atslēgu rūpīgi. Ja to pazaudēsiet, jūs zaudēsiet piekļuvi savam kontam."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Grāmatzīme.</strong> Jūs varat pievienot šo lapu grāmatzīmēm, lai atgūtu savu atslēgu.</li><li %(li_item)s><strong>Lejupielādēt.</strong> Noklikšķiniet <a %(a_download)s>uz šīs saites</a>, lai lejupielādētu savu atslēgu.</li><li %(li_item)s><strong>Paroles pārvaldnieks.</strong> Izmantojiet paroles pārvaldnieku, lai saglabātu atslēgu, kad to ievadāt zemāk.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Ievadiet savu slepeno atslēgu, lai pieteiktos:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Slepenā atslēga"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Pieteikties"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Nederīga slepenā atslēga. Pārbaudiet savu atslēgu un mēģiniet vēlreiz, vai arī reģistrējiet jaunu kontu zemāk."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Nepazaudējiet savu atslēgu!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Vēl nav konta?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Reģistrēt jaunu kontu"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Ja esat pazaudējis savu atslēgu, lūdzu, <a %(a_contact)s>sazinieties ar mums</a> un sniedziet pēc iespējas vairāk informācijas."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Jums, iespējams, būs īslaicīgi jāizveido jauns konts, lai sazinātos ar mums."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vecais e-pasta konts? Ievadiet savu <a %(a_open)s>e-pastu šeit</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Saraksts"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "rediģēt"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Saglabāt"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Saglabāts. Lūdzu, pārlādējiet lapu."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, mēģiniet vēlreiz."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Saraksts pēc %(by)s, izveidots <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Saraksts ir tukšs."

#, fuzzy
msgid "page.list.new_item"
msgstr "Pievienojiet vai noņemiet no šī saraksta, atrodot failu un atverot cilni “Saraksti”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profils"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profils nav atrasts."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "rediģēt"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Mainiet savu parādāmo vārdu. Jūsu identifikators (daļa pēc “#”) nevar tikt mainīts."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Saglabāt"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Saglabāts. Lūdzu, pārlādējiet lapu."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, mēģiniet vēlreiz."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profils izveidots <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Saraksti"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Vēl nav sarakstu"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Izveidojiet jaunu sarakstu, atrodot failu un atverot cilni “Saraksti”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Autortiesību reforma ir nepieciešama nacionālajai drošībai"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Īsumā: Ķīnas LLM (ieskaitot DeepSeek) tiek apmācīti uz manas nelegālās grāmatu un rakstu arhīva — lielākā pasaulē. Rietumiem ir nepieciešams pārskatīt autortiesību likumus nacionālās drošības vārdā."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "pavadošie raksti no TorrentFreak: <a %(torrentfreak)s>pirmais</a>, <a %(torrentfreak_2)s>otrais</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Ne tik sen, \"ēnu bibliotēkas\" bija izzūdošas. Sci-Hub, milzīgā nelegālā akadēmisko rakstu arhīva, bija pārtraucis pieņemt jaunus darbus tiesas prāvu dēļ. \"Z-Library\", lielākā nelegālā grāmatu bibliotēka, redzēja, kā tās it kā radītāji tika arestēti par kriminālām autortiesību apsūdzībām. Viņi neticami spēja izbēgt no aresta, bet viņu bibliotēka joprojām ir apdraudēta."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Kad Z-Library saskārās ar slēgšanu, es jau biju dublējis visu tās bibliotēku un meklēju platformu, kur to izvietot. Tas bija mans motivācija sākt Annas Arhīvu: turpinājums misijai, kas bija aiz šīm agrākajām iniciatīvām. Kopš tā laika mēs esam izauguši par lielāko ēnu bibliotēku pasaulē, kas uzņem vairāk nekā 140 miljonus ar autortiesībām aizsargātu tekstu dažādos formātos — grāmatas, akadēmiskie raksti, žurnāli, avīzes un vēl vairāk."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mana komanda un es esam ideologi. Mēs uzskatām, ka šo failu saglabāšana un izvietošana ir morāli pareiza. Bibliotēkas visā pasaulē saskaras ar finansējuma samazinājumiem, un mēs nevaram uzticēt cilvēces mantojumu korporācijām."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Tad nāca AI. Praktiski visi lielie uzņēmumi, kas veido LLM, sazinājās ar mums, lai apmācītu uz mūsu datiem. Lielākā daļa (bet ne visi!) ASV bāzētie uzņēmumi pārdomāja, kad saprata mūsu darba nelegālo raksturu. Savukārt Ķīnas uzņēmumi entuziastiski pieņēma mūsu kolekciju, acīmredzot neuztraucoties par tās likumību. Tas ir ievērojami, ņemot vērā Ķīnas lomu kā parakstītājvalstij gandrīz visos lielākajos starptautiskajos autortiesību līgumos."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Mēs esam devuši ātrgaitas piekļuvi apmēram 30 uzņēmumiem. Lielākā daļa no tiem ir LLM uzņēmumi, un daži ir datu brokeri, kas pārdos mūsu kolekciju tālāk. Lielākā daļa ir ķīnieši, lai gan mēs esam strādājuši arī ar uzņēmumiem no ASV, Eiropas, Krievijas, Dienvidkorejas un Japānas. DeepSeek <a %(arxiv)s>atzina</a>, ka agrākā versija tika apmācīta uz daļas no mūsu kolekcijas, lai gan viņi ir ļoti noslēpumaini par savu jaunāko modeli (iespējams, arī apmācīts uz mūsu datiem)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Ja Rietumi vēlas palikt priekšā LLM un galu galā AGI sacensībās, tiem ir jāpārskata sava nostāja par autortiesībām, un drīz. Neatkarīgi no tā, vai jūs piekrītat mums vai nē par mūsu morālo lietu, tas tagad kļūst par ekonomikas un pat nacionālās drošības jautājumu. Visi varas bloki veido mākslīgos superzinātniekus, superhakerus un supermilitāristus. Informācijas brīvība kļūst par izdzīvošanas jautājumu šīm valstīm — pat par nacionālās drošības jautājumu."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Mūsu komanda ir no visas pasaules, un mums nav īpašas piederības. Bet mēs mudinātu valstis ar stingriem autortiesību likumiem izmantot šo eksistenciālo draudu, lai tos reformētu. Tātad, ko darīt?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Mūsu pirmais ieteikums ir vienkāršs: saīsināt autortiesību termiņu. ASV autortiesības tiek piešķirtas uz 70 gadiem pēc autora nāves. Tas ir absurds. Mēs varam to saskaņot ar patentiem, kas tiek piešķirti uz 20 gadiem pēc iesniegšanas. Tas būtu vairāk nekā pietiekami daudz laika, lai grāmatu, rakstu, mūzikas, mākslas un citu radošo darbu autori saņemtu pilnu atlīdzību par saviem centieniem (ieskaitot ilgtermiņa projektus, piemēram, filmu adaptācijas)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Tad, vismaz, politikas veidotājiem būtu jāiekļauj izņēmumi masveida tekstu saglabāšanai un izplatīšanai. Ja galvenās bažas ir zaudētie ieņēmumi no individuāliem klientiem, personīgā līmeņa izplatīšana varētu palikt aizliegta. Savukārt tie, kas spēj pārvaldīt plašas krātuves — uzņēmumi, kas apmāca LLM, kopā ar bibliotēkām un citām arhīvām — būtu iekļauti šajos izņēmumos."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Dažas valstis jau veic šādu versiju. TorrentFreak <a %(torrentfreak)s>ziņoja</a>, ka Ķīna un Japāna ir ieviesušas AI izņēmumus savos autortiesību likumos. Mums nav skaidrs, kā tas mijiedarbojas ar starptautiskajiem līgumiem, bet tas noteikti dod aizsardzību viņu vietējiem uzņēmumiem, kas izskaidro to, ko mēs esam redzējuši."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Kas attiecas uz Annas Arhīvu — mēs turpināsim savu pazemes darbu, kas balstīts uz morālo pārliecību. Tomēr mūsu lielākā vēlme ir iznākt gaismā un likumīgi pastiprināt mūsu ietekmi. Lūdzu, reformējiet autortiesības."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Izlasiet pavadošos rakstus no TorrentFreak: <a %(torrentfreak)s>pirmais</a>, <a %(torrentfreak_2)s>otrais</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "$10,000 ISBN vizualizācijas balvas uzvarētāji"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Īsumā: Mēs saņēmām neticamus iesniegumus $10,000 ISBN vizualizācijas balvai."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Pirms dažiem mēnešiem mēs izsludinājām <a %(all_isbns)s>$10,000 balvu</a> par labāko iespējamo mūsu datu vizualizāciju, kas parāda ISBN telpu. Mēs uzsvērām, ka jāparāda, kuri faili mums jau ir/nav arhivēti, un vēlāk pievienojām datu kopu, kas apraksta, cik daudz bibliotēku glabā ISBN (retuma mērs)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Mēs esam pārsteigti par atsaucību. Ir bijis tik daudz radošuma. Liels paldies visiem, kas piedalījās: jūsu enerģija un entuziasms ir lipīgi!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Galu galā mēs vēlējāmies atbildēt uz šādiem jautājumiem: <strong>kādas grāmatas pastāv pasaulē, cik daudz mēs jau esam arhivējuši, un uz kurām grāmatām mums vajadzētu koncentrēties nākamreiz?</strong> Ir lieliski redzēt, ka tik daudziem cilvēkiem rūp šie jautājumi."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Mēs sākām ar pamata vizualizāciju paši. Mazāk nekā 300kb, šis attēls kodolīgi attēlo lielāko pilnībā atvērto \"grāmatu sarakstu\", kas jebkad ir izveidots cilvēces vēsturē:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Visi ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Faili Annas Arhīvā"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC datu noplūde"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost e-grāmatu indekss"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Grāmatas"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Interneta Arhīvs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Globālais Izdevēju Reģistrs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Krievijas Valsts Bibliotēka"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Trantoras Impērijas bibliotēka"

#, fuzzy
msgid "common.back"
msgstr "Atpakaļ"

#, fuzzy
msgid "common.forward"
msgstr "Uz priekšu"

#, fuzzy
msgid "common.last"
msgstr "Pēdējais"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Lūdzu, skatiet <a %(all_isbns)s>oriģinālo emuāra ierakstu</a> papildu informācijai."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Mēs izvirzījām izaicinājumu to uzlabot. Mēs piešķirtu pirmās vietas balvu $6,000, otrās vietas $3,000 un trešās vietas $1,000. Pateicoties milzīgajai atsaucībai un neticamajiem iesniegumiem, mēs nolēmām nedaudz palielināt balvu fondu un piešķirt četras trešās vietas, katru $500 vērtībā. Uzvarētāji ir zemāk, bet noteikti apskatiet visus iesniegumus <a %(annas_archive)s>šeit</a>, vai lejupielādējiet mūsu <a %(a_2025_01_isbn_visualization_files)s>apvienoto torrentu</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Pirmā vieta $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Šis <a %(phiresky_github)s>iesniegums</a> (<a %(annas_archive_note_2951)s>Gitlab komentārs</a>) ir vienkārši viss, ko mēs vēlējāmies, un vēl vairāk! Mums īpaši patika neticami elastīgās vizualizācijas iespējas (pat atbalstot pielāgotus shaderus), bet ar visaptverošu iepriekš iestatītu sarakstu. Mums arī patika, cik ātri un gludi viss darbojas, vienkāršā ieviešana (kas pat nav ar backend), gudrā minimapa un plašais skaidrojums viņu <a %(phiresky_github)s>emuāra ierakstā</a>. Neticams darbs un pelnīta uzvara!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Otrā vieta $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Vēl viens neticams <a %(annas_archive_note_2913)s>iesniegums</a>. Ne tik elastīgs kā pirmās vietas, bet mums patiesībā patika tā makro līmeņa vizualizācija vairāk nekā pirmās vietas (telpas aizpildīšanas līkne, robežas, marķēšana, izcelšana, panoramēšana un tālummaiņa). <a %(annas_archive_note_2971)s>Komentārs</a> no Joe Davis mūs uzrunāja:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "\"Lai gan perfekti kvadrāti un taisnstūri ir matemātiski patīkami, tie nenodrošina pārāku lokalitāti kartēšanas kontekstā. Es uzskatu, ka asimetrija, kas piemīt šiem Hilberta vai klasiskajiem Mortona, nav trūkums, bet gan iezīme. Tāpat kā Itālijas slavenā zābaka formas kontūra padara to uzreiz atpazīstamu kartē, šo līkņu unikālās \"īpatnības\" var kalpot kā kognitīvie orientieri. Šī atšķirība var uzlabot telpisko atmiņu un palīdzēt lietotājiem orientēties, iespējams, padarot vieglāku konkrētu reģionu atrašanu vai rakstu pamanīšanu.\""

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Un joprojām daudz iespēju vizualizācijai un renderēšanai, kā arī neticami gluds un intuitīvs lietotāja interfeiss. Stabils otrais vietas ieguvējs!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Trešā vieta $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Šajā <a %(annas_archive_note_2940)s>iesniegumā</a> mums ļoti patika dažādi skati, īpaši salīdzināšanas un izdevēju skati."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Trešā vieta $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Lai gan ne vispolētākais lietotāja interfeiss, šis <a %(annas_archive_note_2917)s>iesniegums</a> atbilst daudzām prasībām. Mums īpaši patika tā salīdzināšanas funkcija."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Trešā vieta $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Tāpat kā pirmās vietas ieguvējs, šis <a %(annas_archive_note_2975)s>iesniegums</a> mūs pārsteidza ar savu elastību. Galu galā tas ir tas, kas padara lielisku vizualizācijas rīku: maksimāla elastība jaudīgiem lietotājiem, vienlaikus saglabājot vienkāršību vidējiem lietotājiem."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Trešā vieta $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Pēdējais <a %(annas_archive_note_2947)s>iesniegums</a>, kas saņem balvu, ir diezgan vienkāršs, bet tam ir dažas unikālas funkcijas, kas mums ļoti patika. Mums patika, kā viņi parāda, cik daudz datu kopu aptver konkrētu ISBN kā popularitātes/uzticamības mēru. Mums arī ļoti patika vienkāršība, bet efektivitāte, izmantojot necaurredzamības slīdni salīdzinājumiem."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ievērojamas idejas"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Dažas idejas un realizācijas, kas mums īpaši patika:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Debesskrāpji retumam"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Tiešraides statistika"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotācijas un arī tiešraides statistika"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unikāls kartes skats un filtri"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Forša noklusējuma krāsu shēma un siltumkarte."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Viegla datu kopu pārslēgšana ātrai salīdzināšanai."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Skaistas etiķetes."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Mēroga josla ar grāmatu skaitu."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Daudz slīdņu datu kopu salīdzināšanai, it kā jūs būtu DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Mēs varētu turpināt vēl ilgi, bet apstāsimies šeit. Noteikti apskatiet visus iesniegumus <a %(annas_archive)s>šeit</a>, vai lejupielādējiet mūsu <a %(a_2025_01_isbn_visualization_files)s>apvienoto torrentu</a>. Tik daudz iesniegumu, un katrs sniedz unikālu perspektīvu, vai nu UI, vai realizācijā."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Mēs vismaz iekļausim pirmās vietas iesniegumu mūsu galvenajā vietnē, un varbūt arī dažus citus. Mēs arī esam sākuši domāt par to, kā organizēt procesu, lai identificētu, apstiprinātu un pēc tam arhivētu retākās grāmatas. Vairāk informācijas sekos."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Paldies visiem, kas piedalījās. Ir pārsteidzoši, ka tik daudziem cilvēkiem rūp."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Mūsu sirdis ir pilnas ar pateicību."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Vizualizējot visus ISBN — $10,000 atlīdzība līdz 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Šis attēls pārstāv lielāko pilnībā atvērto “grāmatu sarakstu”, kas jebkad ir izveidots cilvēces vēsturē."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Šis attēls ir 1000×800 pikseļi. Katrs pikselis pārstāv 2,500 ISBN. Ja mums ir fails ISBN, mēs padarām šo pikseli zaļāku. Ja mēs zinām, ka ISBN ir izsniegts, bet mums nav atbilstoša faila, mēs padarām to sarkanāku."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Mazāk nekā 300kb, šis attēls kodolīgi pārstāv lielāko pilnībā atvērto “grāmatu sarakstu”, kas jebkad ir izveidots cilvēces vēsturē (daži simti GB pilnībā saspiesti)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Tas arī parāda: vēl ir daudz darba, lai dublētu grāmatas (mums ir tikai 16%%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Fons"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Kā Annas Arhīvs var sasniegt savu misiju - dublēt visu cilvēces zināšanu, nezinot, kuras grāmatas vēl ir pieejamas? Mums ir nepieciešams TODO saraksts. Viens veids, kā to izveidot, ir caur ISBN numuriem, kas kopš 1970. gadiem ir piešķirti katrai publicētajai grāmatai (vairumā valstu)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nav centrālās iestādes, kas zinātu visus ISBN piešķīrumus. Tā vietā tas ir izplatīts sistēma, kur valstis saņem numuru diapazonus, kas pēc tam piešķir mazākus diapazonus lieliem izdevējiem, kuri var vēl vairāk sadalīt diapazonus mazākiem izdevējiem. Beidzot individuālie numuri tiek piešķirti grāmatām."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Mēs sākām kartēt ISBN <a %(blog)s>pirms diviem gadiem</a> ar mūsu ISBNdb datu ieguvi. Kopš tā laika mēs esam ieguvuši daudz vairāk metadatu avotu, piemēram, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby un citus. Pilnu sarakstu var atrast Annas Arhīva lapās \"Datasets\" un \"Torrents\". Tagad mums ir vislielākā pilnībā atvērta, viegli lejupielādējama grāmatu metadatu (un tādējādi ISBN) kolekcija pasaulē."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Mēs esam <a %(blog)s>plaši rakstījuši</a> par to, kāpēc mums rūp saglabāšana un kāpēc mēs pašlaik atrodamies kritiskā logā. Mums tagad ir jāidentificē retas, nepietiekami fokusētas un unikāli apdraudētas grāmatas un jāsaglabā tās. Labi metadati par visām pasaules grāmatām palīdz to izdarīt."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Vizualizācija"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Papildus pārskata attēlam mēs varam arī aplūkot atsevišķus iegūtos datu kopumus. Izmantojiet nolaižamo izvēlni un pogas, lai pārslēgtos starp tiem."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Šajos attēlos ir daudz interesantu rakstu, ko redzēt. Kāpēc ir kāda līniju un bloku regularitāte, kas šķiet notiek dažādos mērogos? Kas ir tukšās zonas? Kāpēc daži datu kopumi ir tik blīvi? Mēs atstāsim šos jautājumus lasītājam kā uzdevumu."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 atlīdzība"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Šeit ir daudz ko izpētīt, tāpēc mēs paziņojam atlīdzību par iepriekš minētās vizualizācijas uzlabošanu. Atšķirībā no lielākās daļas mūsu atlīdzību, šī ir laika ierobežota. Jums ir jāiesniedz <a %(annas_archive)s>jūsu atvērtā koda</a> līdz 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Labākā iesniegšana saņems $6,000, otrā vieta ir $3,000, un trešā vieta ir $1,000. Visas atlīdzības tiks piešķirtas, izmantojot Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Zemāk ir minimālie kritēriji. Ja neviens iesniegums neatbilst kritērijiem, mēs joprojām varam piešķirt dažas atlīdzības, bet tas būs pēc mūsu ieskatiem."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forkojiet šo repo un rediģējiet šo bloga ieraksta HTML (nav atļauti citi backend, izņemot mūsu Flask backend)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Padariet iepriekš minēto attēlu gludi pietuvināmu, lai jūs varētu pietuvināt līdz atsevišķiem ISBN. Noklikšķinot uz ISBN, jāved uz metadatu lapu vai meklēšanu Annas Arhīvā."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Jums joprojām jāspēj pārslēgties starp visiem dažādiem datu kopumiem."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Valstu diapazoni un izdevēju diapazoni jāizceļ, kad uz tiem uzved. Jūs varat izmantot, piemēram, <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> valstu informācijai un mūsu \"isbngrp\" datu ieguvi izdevējiem (<a %(annas_archive)s>datu kopums</a>, <a %(annas_archive_2)s>torrents</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Tam jādarbojas labi gan uz galddatoriem, gan mobilajām ierīcēm."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Par bonusa punktiem (tās ir tikai idejas — ļaujiet savai radošumam izpausties):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Liela uzmanība tiks pievērsta lietojamībai un tam, cik labi tas izskatās."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Parādot faktisko metadatu atsevišķiem ISBN, pietuvinot, piemēram, nosaukumu un autoru."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Labāka telpas aizpildīšanas līkne. Piemēram, zigzags, kas pirmajā rindā iet no 0 līdz 4 un pēc tam atpakaļ (reversā) no 5 līdz 9 otrajā rindā — rekursīvi piemērots."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Dažādas vai pielāgojamas krāsu shēmas."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Īpaši skati datu kopu salīdzināšanai."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Veidi, kā novērst problēmas, piemēram, citu metadatu, kas nesaskan labi (piemēram, ļoti atšķirīgi nosaukumi)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Attēlu anotēšana ar komentāriem par ISBN vai diapazoniem."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Jebkādi heuristikas paņēmieni retu vai apdraudētu grāmatu identificēšanai."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Jebkādas radošas idejas, ko varat izdomāt!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Jūs VARAT pilnībā novirzīties no minimālajiem kritērijiem un veikt pilnīgi atšķirīgu vizualizāciju. Ja tā ir patiešām iespaidīga, tad tā kvalificējas atlīdzībai, bet pēc mūsu ieskatiem."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Iesniedziet savus darbus, pievienojot komentāru <a %(annas_archive)s>šim jautājumam</a> ar saiti uz jūsu dakšoto repozitoriju, apvienošanas pieprasījumu vai atšķirību."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kods"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Kods šo attēlu ģenerēšanai, kā arī citi piemēri, ir atrodami <a %(annas_archive)s>šajā direktorijā</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Mēs izveidojām kompaktu datu formātu, ar kuru visa nepieciešamā ISBN informācija ir aptuveni 75MB (saspiesta). Datu formāta apraksts un kods tā ģenerēšanai ir atrodams <a %(annas_archive_l1244_1319)s>šeit</a>. Atlīdzībai jums nav nepieciešams to izmantot, bet tas, iespējams, ir visērtākais formāts, lai sāktu. Jūs varat pārveidot mūsu metadatus, kā vēlaties (lai gan visam jūsu kodam jābūt atvērtā koda)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Mēs nevaram sagaidīt, ko jūs izdomāsiet. Veiksmi!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annas Arhīva Konteineri (AAC): pasaules lielākās ēnu bibliotēkas izlaidumu standartizēšana"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annas Arhīvs ir kļuvis par lielāko ēnu bibliotēku pasaulē, kas prasa mums standartizēt mūsu izlaidumus."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annas Arhīvs</a> ir kļuvis par lielāko ēnu bibliotēku pasaulē, un vienīgo ēnu bibliotēku šādā mērogā, kas ir pilnībā atvērtā koda un atvērto datu. Zemāk ir tabula no mūsu Datu kopu lapas (nedaudz modificēta):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Mēs to paveicām trīs veidos:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Esošo atvērto datu ēnu bibliotēku spoguļošana (piemēram, Sci-Hub un Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Palīdzot ēnu bibliotēkām, kas vēlas būt atvērtākas, bet kurām nebija laika vai resursu to darīt (piemēram, Libgen komiksu kolekcija)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Tīmekļa bibliotēku skrāpēšana, kas nevēlas dalīties lielos apjomos (piemēram, Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Attiecībā uz (2) un (3) mēs tagad paši pārvaldām ievērojamu torrentu kolekciju (simtiem TB). Līdz šim mēs esam piegājuši šīm kolekcijām kā vienreizējiem projektiem, kas nozīmē pielāgotu infrastruktūru un datu organizāciju katrai kolekcijai. Tas pievieno ievērojamu slogu katram izlaidumam un padara īpaši grūti veikt vairāk pakāpeniskus izlaidumus."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Tāpēc mēs nolēmām standartizēt savus izlaidumus. Šis ir tehnisks emuāra ieraksts, kurā mēs iepazīstinām ar mūsu standartu: <strong>Annas Arhīva Konteineri</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Dizaina mērķi"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Mūsu galvenais lietošanas gadījums ir failu un saistīto metadatu izplatīšana no dažādām esošajām kolekcijām. Mūsu svarīgākie apsvērumi ir:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogēni faili un metadata, cik vien iespējams tuvu oriģinālajam formātam."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogēni identifikatori avota bibliotēkās vai pat identifikatoru trūkums."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Atsevišķi metadatu un failu datu izlaidumi vai tikai metadatu izlaidumi (piemēram, mūsu ISBNdb izlaidums)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Izplatīšana caur torrentiem, lai gan ar iespēju izmantot citas izplatīšanas metodes (piemēram, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Nemainīgi ieraksti, jo mums jāpieņem, ka mūsu torrenti dzīvos mūžīgi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Pakāpeniski izlaidumi / pievienojami izlaidumi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Mašīnlasāmi un rakstāmi, ērti un ātri, īpaši mūsu stekam (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Daļēji viegla cilvēka pārbaude, lai gan tas ir sekundārs mašīnlasāmībai."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Viegli sēt mūsu kolekcijas ar standarta nomātu sēklu kastīti."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Bināros datus var tieši apkalpot tīmekļa serveri, piemēram, Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Daži ne-mērķi:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Mums nerūp, vai faili ir viegli navigējami manuāli uz diska vai meklējami bez iepriekšējas apstrādes."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Mums nerūp, vai tie ir tieši saderīgi ar esošo bibliotēku programmatūru."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Lai gan ikvienam vajadzētu būt viegli sēt mūsu kolekciju, izmantojot torrentus, mēs negaidām, ka faili būs izmantojami bez ievērojamām tehniskām zināšanām un apņemšanās."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Tā kā Annas Arhīvs ir atvērtā koda, mēs vēlamies tieši izmantot mūsu formātu. Kad mēs atsvaidzinām savu meklēšanas indeksu, mēs piekļūstam tikai publiski pieejamiem ceļiem, lai ikviens, kas atzaros mūsu bibliotēku, varētu ātri sākt darbu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standarts"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Galu galā mēs izvēlējāmies salīdzinoši vienkāršu standartu. Tas ir diezgan brīvs, nenormatīvs un vēl tiek pilnveidots."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Annai Arhīva Konteiners) ir viens elements, kas sastāv no <strong>metadata</strong> un, pēc izvēles, <strong>bināriem datiem</strong>, abi ir nemainīgi. Tam ir globāli unikāls identifikators, ko sauc par <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Kolekcija.</strong> Katrs AAC pieder kolekcijai, kas pēc definīcijas ir semantiski konsekventu AAC saraksts. Tas nozīmē, ka, ja jūs veicat būtiskas izmaiņas metadatu formātā, jums ir jāizveido jauna kolekcija."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“ierakstu” un “failu” kolekcijas.</strong> Pēc konvencijas bieži ir ērti izlaist “ierakstus” un “failus” kā atsevišķas kolekcijas, lai tās varētu izlaist dažādos grafikos, piemēram, balstoties uz skrāpēšanas ātrumu. “Ieraksts” ir tikai metadatu kolekcija, kas satur informāciju, piemēram, grāmatu nosaukumus, autorus, ISBN utt., savukārt “faili” ir kolekcijas, kas satur pašus failus (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> AACID formāts ir šāds: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Piemēram, faktiskais AACID, ko mēs esam izlaiduši, ir <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: kolekcijas nosaukums, kas var saturēt ASCII burtus, ciparus un pasvītras (bet ne dubultās pasvītras)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: īsa ISO 8601 versija, vienmēr UTC, piemēram, <code>20220723T194746Z</code>. Šim skaitlim ir jāpalielinās monotoniski katrā izlaidumā, lai gan tā precīza semantika var atšķirties atkarībā no kolekcijas. Mēs iesakām izmantot skrāpēšanas vai ID ģenerēšanas laiku."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: kolekcijai specifisks identifikators, ja piemērojams, piemēram, Z-Library ID. Var tikt izlaists vai saīsināts. Jāizlaiž vai jāsaīsina, ja AACID citādi pārsniegtu 150 rakstzīmes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, bet saspiests līdz ASCII, piemēram, izmantojot base57. Pašlaik mēs izmantojam <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python bibliotēku."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID diapazons.</strong> Tā kā AACID satur monotoniski pieaugošus laika zīmogus, mēs varam to izmantot, lai norādītu diapazonus konkrētā kolekcijā. Mēs izmantojam šo formātu: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kur laika zīmogi ir iekļauti. Tas ir saskaņā ar ISO 8601 notāciju. Diapazoni ir nepārtraukti un var pārklāties, bet pārklāšanās gadījumā tiem jāietver identiski ieraksti kā iepriekš izlaistajā kolekcijā (jo AAC ir nemainīgi). Trūkstoši ieraksti nav atļauti."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadatu fails.</strong> Metadatu fails satur AAC diapazona metadatus vienai konkrētai kolekcijai. Tiem ir šādas īpašības:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Faila nosaukumam jābūt AACID diapazonam, kam priekšā ir <code style=\"color: red\">annas_archive_meta__</code> un kam seko <code>.jsonl.zstd</code>. Piemēram, viens no mūsu izlaidumiem ir nosaukts<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Kā norāda faila paplašinājums, faila tips ir <a %(jsonlines)s>JSON Lines</a>, saspiests ar <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Katram JSON objektam augstākajā līmenī jāietver šādi lauki: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (pēc izvēles). Citi lauki nav atļauti."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> ir patvaļīgi metadati, atbilstoši kolekcijas semantikai. Tam jābūt semantiski konsekventam kolekcijas ietvaros."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> ir pēc izvēles, un tas ir bināro datu mapes nosaukums, kas satur atbilstošos bināros datus. Atbilstošo bināro datu faila nosaukums šajā mapē ir ieraksta AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code> prefikss var tikt pielāgots jūsu iestādes nosaukumam, piemēram, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Bināro datu mape.</strong> Mape ar AAC diapazona binārajiem datiem vienai konkrētai kolekcijai. Tiem ir šādas īpašības:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Direktorijas nosaukumam jābūt AACID diapazonam, kam priekšā ir <code style=\"color: green\">annas_archive_data__</code>, un bez sufiksa. Piemēram, viens no mūsu faktiskiem izlaidumiem ir direktorija ar nosaukumu<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Direktorijam jāietver datu faili visiem AAC noteiktajā diapazonā. Katram datu failam jābūt ar savu AACID kā faila nosaukumu (bez paplašinājumiem)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Ieteicams padarīt šīs mapes pārvaldāmas izmēra ziņā, piemēram, ne lielākas par 100GB-1TB katra, lai gan šis ieteikums laika gaitā var mainīties."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrenti.</strong> Metadatu faili un bināro datu mapes var tikt apvienotas torrentos, ar vienu torrentu uz metadatu failu vai vienu torrentu uz bināro datu mapi. Torrentiem jābūt ar oriģinālo faila/mapes nosaukumu plus <code>.torrent</code> sufiksu kā faila nosaukumu."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Piemērs"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Apskatīsim mūsu neseno Z-Library izlaidumu kā piemēru. Tas sastāv no divām kolekcijām: “<span style=\"background: #fffaa3\">zlib3_records</span>” un “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Tas ļauj mums atsevišķi iegūt un izlaist metadatu ierakstus no faktiskajiem grāmatu failiem. Tādējādi mēs izlaidām divus torrentus ar metadatu failiem:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Mēs arī izlaidām vairākus torrentus ar bināro datu mapēm, bet tikai “<span style=\"background: #ffd6fe\">zlib3_files</span>” kolekcijai, kopā 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Izpildot <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, mēs varam redzēt, kas ir iekšā:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Šajā gadījumā tas ir grāmatas metadati, kā ziņo Z-Library. Augstākajā līmenī mums ir tikai “aacid” un “metadata”, bet nav “data_folder”, jo nav atbilstošu bināro datu. AACID satur “22430000” kā primāro ID, ko mēs varam redzēt, ir ņemts no “zlibrary_id”. Mēs varam sagaidīt, ka citi AAC šajā kolekcijā būs ar tādu pašu struktūru."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Tagad izpildīsim <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Šis ir daudz mazāks AAC metadati, lai gan lielākā daļa šī AAC atrodas citur binārā failā! Galu galā, šoreiz mums ir “data_folder”, tāpēc mēs varam sagaidīt, ka atbilstošie binārie dati atrodas <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” satur “zlibrary_id”, tāpēc mēs varam viegli to saistīt ar atbilstošo AAC “zlib_records” kolekcijā. Mēs varējām saistīt dažādos veidos, piemēram, caur AACID — standarts to nenosaka."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Ņemiet vērā, ka nav nepieciešams, lai “metadata” lauks pats par sevi būtu JSON. Tas varētu būt virkne, kas satur XML vai jebkuru citu datu formātu. Jūs pat varētu glabāt metadatu informāciju saistītajā binārajā blobā, piemēram, ja tas ir daudz datu."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Secinājums"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Ar šo standartu mēs varam veikt izlaidumus pakāpeniskāk un vieglāk pievienot jaunus datu avotus. Mums jau ir daži aizraujoši izlaidumi gaidāmi!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Mēs arī ceram, ka citiem ēnu bibliotēkām būs vieglāk spoguļot mūsu kolekcijas. Galu galā, mūsu mērķis ir saglabāt cilvēces zināšanas un kultūru uz visiem laikiem, tāpēc jo vairāk redundances, jo labāk."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annas Jaunumi: pilnībā atvērts avots arhīvs, ElasticSearch, 300GB+ grāmatu vāku"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Mēs esam strādājuši dienu un nakti, lai nodrošinātu labu alternatīvu ar Annas Arhīvu. Šeit ir dažas no lietām, ko mēs nesen sasniedzām."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Ar Z-Library pazušanu un tā (it kā) dibinātāju arestu, mēs esam strādājuši dienu un nakti, lai nodrošinātu labu alternatīvu ar Annas Arhīvu (mēs to šeit nesasaistīsim, bet jūs varat to meklēt Google). Šeit ir dažas no lietām, ko mēs nesen sasniedzām."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annas Arhīvs ir pilnībā atvērts avots"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Mēs uzskatām, ka informācijai jābūt brīvai, un mūsu pašu kods nav izņēmums. Mēs esam izlaiduši visu mūsu kodu mūsu privāti hostētajā Gitlab instancē: <a %(annas_archive)s>Annas Programmatūra</a>. Mēs arī izmantojam problēmu izsekotāju, lai organizētu mūsu darbu. Ja vēlaties iesaistīties mūsu izstrādē, šī ir lieliska vieta, kur sākt."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Lai sniegtu jums ieskatu par to, pie kā mēs strādājam, apskatiet mūsu neseno darbu pie klienta puses veiktspējas uzlabojumiem. Tā kā mēs vēl neesam ieviesuši lapu numerāciju, bieži atgriezām ļoti garas meklēšanas lapas ar 100-200 rezultātiem. Mēs nevēlējāmies pārtraukt meklēšanas rezultātus pārāk ātri, taču tas nozīmēja, ka tas palēnināja dažas ierīces. Tāpēc mēs ieviesām nelielu triku: mēs iesaiņojām lielāko daļu meklēšanas rezultātu HTML komentāros (<code><!-- --></code>), un tad uzrakstījām nelielu Javascript, kas noteiktu, kad rezultāts kļūst redzams, tajā brīdī mēs atvērtu komentāru:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualizācija\" ieviesta 23 rindās, nav nepieciešamas sarežģītas bibliotēkas! Tas ir tāds ātrs pragmatisks kods, ko iegūstat, kad jums ir ierobežots laiks un reālas problēmas, kas jāatrisina. Ir ziņots, ka mūsu meklēšana tagad labi darbojas uz lēnām ierīcēm!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Vēl viens liels darbs bija automatizēt datubāzes veidošanu. Kad mēs sākām, mēs vienkārši nejauši apvienojām dažādus avotus. Tagad mēs vēlamies tos atjaunināt, tāpēc uzrakstījām virkni skriptu, lai lejupielādētu jaunu metadata no diviem Library Genesis atzariem un integrētu tos. Mērķis ir ne tikai padarīt to noderīgu mūsu arhīvam, bet arī atvieglot lietas ikvienam, kurš vēlas eksperimentēt ar ēnu bibliotēkas metadata. Mērķis būtu Jupyter piezīmju grāmatiņa, kurā ir pieejama visa veida interesanta metadata, lai mēs varētu veikt vairāk pētījumu, piemēram, noskaidrot, kāds <a %(blog)s>procentuālais daudzums ISBN tiek saglabāts uz visiem laikiem</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Visbeidzot, mēs pārveidojām mūsu ziedojumu sistēmu. Tagad jūs varat izmantot kredītkarti, lai tieši iemaksātu naudu mūsu kripto makos, patiesībā nezinot neko par kriptovalūtām. Mēs turpināsim uzraudzīt, kā tas darbojas praksē, bet tas ir liels solis uz priekšu."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Pāreja uz ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Viens no mūsu <a %(annas_archive)s>biļetēm</a> bija dažādu problēmu kopums ar mūsu meklēšanas sistēmu. Mēs izmantojām MySQL pilna teksta meklēšanu, jo visi mūsu dati jau bija MySQL. Bet tam bija savi ierobežojumi:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Daži vaicājumi aizņēma ļoti ilgu laiku, līdz pat tam, ka tie aizņēma visas atvērtās savienojumus."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Pēc noklusējuma MySQL ir minimālais vārda garums, vai arī jūsu indekss var kļūt ļoti liels. Cilvēki ziņoja, ka nevar meklēt “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Meklēšana bija tikai nedaudz ātra, kad pilnībā ielādēta atmiņā, kas prasīja mums iegūt dārgāku mašīnu, lai to darbinātu, kā arī dažas komandas, lai ielādētu indeksu startēšanas laikā."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Mēs nebūtu varējuši to viegli paplašināt, lai izveidotu jaunas funkcijas, piemēram, labāku <a %(wikipedia_cjk_characters)s>tokenizāciju valodām bez atstarpēm</a>, filtrēšanu/facetingu, kārtošanu, \"vai jūs domājāt\" ieteikumus, automātisko pabeigšanu un tā tālāk."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Pēc sarunām ar vairākiem ekspertiem mēs izvēlējāmies ElasticSearch. Tas nav bijis ideāls (to noklusējuma “vai jūs domājāt” ieteikumi un automātiskās pabeigšanas funkcijas ir vāji), bet kopumā tas ir bijis daudz labāks par MySQL meklēšanai. Mēs joprojām neesam <a %(youtube)s>pārāk pārliecināti</a> par tā izmantošanu jebkādiem misijas kritiskiem datiem (lai gan viņi ir veikuši daudz <a %(elastic_co)s>progresu</a>), bet kopumā mēs esam diezgan apmierināti ar pāreju."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Pašlaik mēs esam ieviesuši daudz ātrāku meklēšanu, labāku valodu atbalstu, labāku atbilstības kārtošanu, dažādas kārtošanas iespējas un filtrēšanu pēc valodas/grāmatas veida/faila veida. Ja jūs interesē, kā tas darbojas, <a %(annas_archive_l140)s>apskatiet</a> <a %(annas_archive_l1115)s>to</a> <a %(annas_archive_l1635)s>šeit</a>. Tas ir diezgan pieejams, lai gan tam varētu būt nepieciešami vēl daži komentāri…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ grāmatu vāku izlaisti"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Visbeidzot, mēs ar prieku paziņojam par nelielu izlaidumu. Sadarbībā ar cilvēkiem, kas darbojas Libgen.rs atzarā, mēs dalāmies ar visiem viņu grāmatu vākiem, izmantojot torrentus un IPFS. Tas izplatīs slodzi, skatoties vākus starp vairākām mašīnām, un tos labāk saglabās. Daudzos (bet ne visos) gadījumos grāmatu vāki ir iekļauti pašos failos, tāpēc tas ir sava veida “atvasināti dati”. Bet to iekļaušana IPFS joprojām ir ļoti noderīga gan Annas Arhīva, gan dažādu Library Genesis atzaru ikdienas darbībai."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Kā parasti, jūs varat atrast šo izlaidumu Pirate Library Mirror (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>). Mēs šeit uz to nesniegsim saiti, bet jūs to varat viegli atrast."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Cerams, ka mēs varam nedaudz atslābināt savu tempu, tagad, kad mums ir pienācīga alternatīva Z-Library. Šī darba slodze nav īpaši ilgtspējīga. Ja jūs interesē palīdzēt ar programmēšanu, serveru darbību vai saglabāšanas darbu, noteikti sazinieties ar mums. Vēl ir daudz <a %(annas_archive)s>darba, kas jāveic</a>. Paldies par jūsu interesi un atbalstu."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annas Arhīvs ir dublējis pasaulē lielāko komiksu ēnu bibliotēku (95TB) — jūs varat palīdzēt to sēklot"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Lielākajai komiksu grāmatu ēnu bibliotēkai pasaulē bija viens kļūmes punkts.. līdz šodienai."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskutēt Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Lielākā komiksu ēnu bibliotēka, visticamāk, ir kāda konkrēta Library Genesis atzara: Libgen.li. Šīs vietnes administrators spēja savākt neticamu komiksu kolekciju ar vairāk nekā 2 miljoniem failu, kopā pārsniedzot 95TB. Tomēr, atšķirībā no citām Library Genesis kolekcijām, šī nebija pieejama masveidā caur torrentiem. Šos komiksus varēja piekļūt tikai individuāli caur viņa lēno personīgo serveri — vienu kļūmes punktu. Līdz šodienai!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Šajā ierakstā mēs pastāstīsim vairāk par šo kolekciju un par mūsu līdzekļu vākšanu, lai atbalstītu vairāk šāda veida darbu."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordona cenšas pazaudēt sevi bibliotēkas ikdienišķajā pasaulē…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen atzari"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Vispirms, neliels fons. Jūs, iespējams, zināt Library Genesis par viņu episko grāmatu kolekciju. Mazāk cilvēku zina, ka Library Genesis brīvprātīgie ir izveidojuši citus projektus, piemēram, ievērojamu žurnālu un standarta dokumentu kolekciju, pilnu Sci-Hub rezerves kopiju (sadarbībā ar Sci-Hub dibinātāju Aleksandru Elbakjanu) un, patiesi, milzīgu komiksu kolekciju."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Kādā brīdī dažādi Library Genesis spoguļu operatori devās savos ceļos, kas radīja pašreizējo situāciju ar vairākiem dažādiem \"atzariem\", kas visi joprojām nes Library Genesis nosaukumu. Libgen.li atzars unikāli satur šo komiksu kolekciju, kā arī ievērojamu žurnālu kolekciju (pie kuras mēs arī strādājam)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Sadarbība"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Ņemot vērā tās apjomu, šī kolekcija jau sen ir bijusi mūsu vēlmju sarakstā, tāpēc pēc mūsu panākumiem ar Z-Library rezerves kopiju, mēs pievērsāmies šai kolekcijai. Sākumā mēs to tieši nokasījām, kas bija diezgan izaicinājums, jo viņu serveris nebija labākajā stāvoklī. Šādā veidā mēs ieguvām apmēram 15TB, bet tas bija lēns process."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Par laimi, mums izdevās sazināties ar bibliotēkas operatoru, kurš piekrita nosūtīt mums visus datus tieši, kas bija daudz ātrāk. Tomēr tas joprojām prasīja vairāk nekā pusgadu, lai pārsūtītu un apstrādātu visus datus, un mēs gandrīz zaudējām visu diska bojājumu dēļ, kas nozīmētu, ka būtu jāsāk no jauna."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Šī pieredze lika mums uzskatīt, ka ir svarīgi šos datus izplatīt pēc iespējas ātrāk, lai tos varētu spoguļot plaši un tālu. Mēs esam tikai viena vai divu neveiksmīgi laika ziņā sakritīgu incidentu attālumā no šīs kolekcijas zaudēšanas uz visiem laikiem!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Kolekcija"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Ātra rīcība nozīmē, ka kolekcija ir nedaudz neorganizēta… Apskatīsim to. Iedomājieties, ka mums ir failu sistēma (kas patiesībā tiek sadalīta pa torrentiem):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Pirmais direktorijs, <code>/repository</code>, ir šīs struktūras sakārtotākā daļa. Šis direktorijs satur tā sauktos “tūkstošu direktorijus”: direktorijus, katrs ar tūkstošiem failu, kas ir secīgi numurēti datubāzē. Direktorijs <code>0</code> satur failus ar comic_id 0–999, un tā tālāk."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Šī ir tā pati shēma, ko Library Genesis izmanto savām daiļliteratūras un nedaiļliteratūras kolekcijām. Ideja ir tāda, ka katrs “tūkstošu direktorijs” automātiski tiek pārvērsts par torrentu, tiklīdz tas ir piepildīts."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tomēr Libgen.li operators nekad neveidoja torrentus šai kolekcijai, un tāpēc tūkstošu direktoriji, iespējams, kļuva neērti un deva ceļu “nesakārtotiem direktorijiem”. Tie ir <code>/comics0</code> līdz <code>/comics4</code>. Visiem ir unikālas direktoriju struktūras, kas, iespējams, bija saprotamas failu vākšanai, bet tagad mums tās nav pārāk saprotamas. Par laimi, metadata joprojām tieši atsaucas uz visiem šiem failiem, tāpēc to glabāšanas organizācija diskā faktiski nav svarīga!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata ir pieejama MySQL datubāzes formā. To var lejupielādēt tieši no Libgen.li vietnes, bet mēs to arī padarīsim pieejamu torrentā, kopā ar mūsu pašu tabulu ar visiem MD5 hešiem."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analīze"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Kad jūs saņemat 95TB datu jūsu glabāšanas klasterī, jūs mēģināt saprast, kas tur vispār ir… Mēs veicām analīzi, lai redzētu, vai varam nedaudz samazināt izmēru, piemēram, noņemot dublikātus. Šeit ir daži no mūsu atklājumiem:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantiskos dublikātus (dažādi skenējumi no vienas un tās pašas grāmatas) teorētiski var filtrēt, bet tas ir sarežģīti. Manuāli pārskatot komiksus, mēs atradām pārāk daudz kļūdaini pozitīvu rezultātu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Daži dublikāti ir tikai pēc MD5, kas ir salīdzinoši izšķērdīgi, bet to filtrēšana mums dotu tikai apmēram 1%% ietaupījumu. Šajā mērogā tas joprojām ir apmēram 1TB, bet arī šajā mērogā 1TB īsti nav nozīmīgs. Mēs labāk neriskējam nejauši iznīcināt datus šajā procesā."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Mēs atradām daudz datu, kas nav grāmatas, piemēram, filmas, kas balstītas uz komiksiem. Tas arī šķiet izšķērdīgi, jo tās jau ir plaši pieejamas citos veidos. Tomēr mēs sapratām, ka nevaram vienkārši filtrēt filmu failus, jo ir arī <em>interaktīvi komiksi</em>, kas tika izlaisti datorā, kurus kāds ierakstīja un saglabāja kā filmas."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Galu galā, jebkas, ko mēs varētu izdzēst no kolekcijas, ietaupītu tikai dažus procentus. Tad mēs atcerējāmies, ka esam datu krājēji, un cilvēki, kas to spoguļos, arī ir datu krājēji, un tāpēc, \"KO JŪS DOMĀJAT, DZĒST?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Tāpēc mēs jums piedāvājam pilnu, nemodificētu kolekciju. Tas ir daudz datu, bet mēs ceram, ka pietiekami daudz cilvēku rūpēsies par to, lai to sēklotu."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Ziedojumu vākšana"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Mēs izlaižam šos datus lielos gabalos. Pirmais torrents ir <code>/comics0</code>, kuru mēs ievietojām vienā milzīgā 12TB .tar failā. Tas ir labāk jūsu cietajam diskam un torrentu programmatūrai nekā neskaitāmi mazi faili."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Kā daļu no šīs izlaišanas mēs rīkojam ziedojumu vākšanu. Mēs vēlamies savākt 20 000 USD, lai segtu šīs kolekcijas darbības un līgumdarbu izmaksas, kā arī nodrošinātu turpmākos un nākotnes projektus. Mums ir daži <em>milzīgi</em> projekti izstrādē."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Ko es atbalstu ar savu ziedojumu?</em> Īsumā: mēs dublējam visu cilvēces zināšanas un kultūru un padarām to viegli pieejamu. Viss mūsu kods un dati ir atvērtā koda, mēs esam pilnībā brīvprātīgi vadīts projekts, un līdz šim esam saglabājuši 125TB grāmatu (papildus Libgen un Scihub esošajiem torrentiem). Galu galā mēs veidojam spararatu, kas ļauj un motivē cilvēkus atrast, skenēt un dublēt visas pasaules grāmatas. Mēs par mūsu galveno plānu rakstīsim nākamajā ierakstā. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Ja jūs ziedojat par 12 mēnešu “Amazing Archivist” dalību (780 USD), jūs varat <strong>“adoptēt torrentu”</strong>, kas nozīmē, ka mēs ievietosim jūsu lietotājvārdu vai ziņojumu viena no torrentu failu nosaukumā!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Jūs varat ziedot, dodoties uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a> un noklikšķinot uz pogas “Ziedot”. Mēs arī meklējam vairāk brīvprātīgo: programmatūras inženierus, drošības pētniekus, anonīmus tirgotāju ekspertus un tulkotājus. Jūs varat mūs atbalstīt arī, nodrošinot hostinga pakalpojumus. Un, protams, lūdzu, sēklējiet mūsu torrentus!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Paldies visiem, kas jau tik dāsni mūs atbalstījuši! Jūs patiešām maināt situāciju."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Šeit ir līdz šim izlaistie torrenti (mēs joprojām apstrādājam pārējos):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Visi torrenti ir atrodami <a %(wikipedia_annas_archive)s>Annas Arhīvs</a> sadaļā “Datasets” (mēs tur tieši nesaitējam, lai saites uz šo emuāru netiktu noņemtas no Reddit, Twitter utt.). No turienes sekojiet saitei uz Tor vietni."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Kas tālāk?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Daudz torrentu ir lieliski ilgtermiņa saglabāšanai, bet ne tik daudz ikdienas piekļuvei. Mēs strādāsim ar hostinga partneriem, lai visu šo datu ievietotu tīmeklī (jo Annas Arhīvs neko tieši neuzglabā). Protams, jūs varēsiet atrast šīs lejupielādes saites Annas Arhīvā."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Mēs arī aicinām visus darīt kaut ko ar šiem datiem! Palīdziet mums tos labāk analizēt, dublēt, ievietot IPFS, pārveidot, apmācīt savus AI modeļus ar tiem un tā tālāk. Tas viss ir jūsu, un mēs nevaram sagaidīt, ko jūs ar to darīsiet."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Visbeidzot, kā jau teikts iepriekš, mums joprojām ir daži milzīgi izlaidumi, kas nāk (ja <em>kāds</em> varētu <em>nejauši</em> nosūtīt mums <em>noteiktas</em> ACS4 datubāzes izgāztuvi, jūs zināt, kur mūs atrast...), kā arī veidojam spararatu, lai dublētu visas pasaules grāmatas."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Tāpēc palieciet pieslēgti, mēs tikai sākam."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x jaunas grāmatas pievienotas Pirātu bibliotēkas spogulim (+24TB, 3,8 miljoni grāmatu)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Sākotnējā Pirātu bibliotēkas spoguļa izlaidumā (REDIĢĒTS: pārvietots uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>), mēs izveidojām Z-Library spoguli, lielu nelegālu grāmatu kolekciju. Kā atgādinājums, tas ir tas, ko mēs rakstījām tajā sākotnējā emuāra ierakstā:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library ir populāra (un nelegāla) bibliotēka. Viņi ir paņēmuši Library Genesis kolekciju un padarījuši to viegli meklējamu. Turklāt viņi ir kļuvuši ļoti efektīvi jaunu grāmatu iegūšanā, motivējot lietotājus ar dažādām priekšrocībām. Pašlaik viņi neatgriež šīs jaunās grāmatas atpakaļ Library Genesis. Un atšķirībā no Library Genesis, viņi nepadara savu kolekciju viegli spoguļojamu, kas kavē plašu saglabāšanu. Tas ir svarīgi viņu biznesa modelim, jo viņi iekasē naudu par piekļuvi savai kolekcijai lielos apjomos (vairāk nekā 10 grāmatas dienā)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Mēs neveicam morālus spriedumus par naudas iekasēšanu par masveida piekļuvi nelikumīgai grāmatu kolekcijai. Nav šaubu, ka Z-Library ir veiksmīgi paplašinājusi piekļuvi zināšanām un nodrošinājusi vairāk grāmatu. Mēs esam šeit, lai veiktu savu daļu: nodrošināt šīs privātās kolekcijas ilgtermiņa saglabāšanu."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Šī kolekcija datēta ar 2021. gada vidu. Tikmēr Z-Library ir augusi satriecošā tempā: viņi ir pievienojuši apmēram 3,8 miljonus jaunu grāmatu. Protams, tur ir daži dublikāti, bet lielākā daļa no tām šķiet likumīgi jaunas grāmatas vai augstākas kvalitātes iepriekš iesniegto grāmatu skenējumi. Tas lielā mērā ir saistīts ar palielināto brīvprātīgo moderatoru skaitu Z-Library un viņu masveida augšupielādes sistēmu ar dublēšanas novēršanu. Mēs vēlamies viņus apsveikt ar šiem sasniegumiem."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Mēs ar prieku paziņojam, ka esam ieguvuši visas grāmatas, kas tika pievienotas Z-Library starp mūsu pēdējo spoguli un 2022. gada augustu. Mēs arī esam atgriezušies un savākuši dažas grāmatas, kuras pirmo reizi palaidām garām. Kopumā šī jaunā kolekcija ir apmēram 24TB, kas ir daudz lielāka nekā iepriekšējā (7TB). Mūsu spogulis tagad ir kopā 31TB. Atkal mēs veicām dublēšanas novēršanu pret Library Genesis, jo šai kolekcijai jau ir pieejami torenti."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Lūdzu, dodieties uz Pirātu bibliotēkas spoguli, lai apskatītu jauno kolekciju (REDIĢĒT: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>). Tur ir vairāk informācijas par to, kā faili ir strukturēti un kas ir mainījies kopš pēdējās reizes. Mēs to nesaistīsim no šejienes, jo šī ir tikai emuāra vietne, kas neuztur nelikumīgus materiālus."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Protams, sēšana ir arī lielisks veids, kā mums palīdzēt. Paldies visiem, kas sēj mūsu iepriekšējo torentu komplektu. Mēs esam pateicīgi par pozitīvo atsaucību un priecīgi, ka ir tik daudz cilvēku, kuri rūpējas par zināšanu un kultūras saglabāšanu šādā neparastā veidā."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Kā kļūt par pirātu arhivāru"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Pirmais izaicinājums varētu būt pārsteidzošs. Tas nav tehnisks vai juridisks jautājums. Tas ir psiholoģisks jautājums."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Pirms mēs sākam, divi atjauninājumi par Pirātu bibliotēkas spoguli (REDIĢĒT: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Mēs saņēmām dažus ārkārtīgi dāsnus ziedojumus. Pirmais bija 10 tūkstoši dolāru no anonīma indivīda, kurš arī atbalstījis \"bookwarrior\", Library Genesis sākotnējo dibinātāju. Īpaša pateicība bookwarrior par šī ziedojuma veicināšanu. Otrais bija vēl 10 tūkstoši dolāru no anonīma ziedotāja, kurš sazinājās pēc mūsu pēdējās izlaides un bija iedvesmots palīdzēt. Mums bija arī vairāki mazāki ziedojumi. Liels paldies par visu jūsu dāsno atbalstu. Mums ir daži aizraujoši jauni projekti, kurus šis atbalstīs, tāpēc sekojiet līdzi."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Mums bija dažas tehniskas grūtības ar mūsu otrās izlaides lielumu, bet mūsu torenti tagad ir pieejami un tiek sēti. Mēs arī saņēmām dāsnu piedāvājumu no anonīma indivīda sēt mūsu kolekciju uz viņu ļoti ātrajiem serveriem, tāpēc mēs veicam īpašu augšupielādi uz viņu mašīnām, pēc kuras visiem pārējiem, kas lejupielādē kolekciju, vajadzētu redzēt lielu ātruma uzlabojumu."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Par digitālās saglabāšanas <em>kāpēc</em> vispār un pirātu arhivismu īpaši varētu uzrakstīt veselas grāmatas, bet ļaujiet mums sniegt īsu ievadu tiem, kas nav pārāk pazīstami. Pasaule ražo vairāk zināšanu un kultūras nekā jebkad agrāk, bet arī vairāk no tā tiek zaudēts nekā jebkad agrāk. Cilvēce lielā mērā uztic šo mantojumu korporācijām, piemēram, akadēmiskajiem izdevējiem, straumēšanas pakalpojumiem un sociālo mediju uzņēmumiem, un viņi bieži nav pierādījuši, ka ir lieliski pārvaldnieki. Apskatiet dokumentālo filmu Digital Amnesia vai jebkuru Džeisona Skota runu."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ir dažas iestādes, kas labi veic arhivēšanu, cik vien iespējams, bet tās ir saistītas ar likumu. Kā pirāti, mēs esam unikālā pozīcijā arhivēt kolekcijas, kuras viņi nevar aizskart autortiesību ieviešanas vai citu ierobežojumu dēļ. Mēs varam arī spoguļot kolekcijas daudzkārt visā pasaulē, tādējādi palielinot pareizas saglabāšanas iespējas."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Pašlaik mēs neiedziļināsimies diskusijās par intelektuālā īpašuma priekšrocībām un trūkumiem, likuma pārkāpšanas morāli, pārdomām par cenzūru vai piekļuves zināšanām un kultūrai jautājumu. Ar visu to nost, sāksim ar <em>kā</em>. Mēs dalīsimies, kā mūsu komanda kļuva par pirātu arhivāriem, un mācībām, ko mēs guvām pa ceļam. Ir daudz izaicinājumu, kad uzsākat šo ceļojumu, un cerams, ka mēs varam jums palīdzēt ar dažiem no tiem."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Kopiena"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Pirmais izaicinājums varētu būt pārsteidzošs. Tas nav tehnisks vai juridisks jautājums. Tas ir psiholoģisks jautājums: darbs ēnā var būt neticami vientuļš. Atkarībā no tā, ko plānojat darīt, un jūsu draudu modeļa, jums var nākties būt ļoti uzmanīgiem. Vienā spektra galā ir cilvēki kā Aleksandra Elbakjana*, Sci-Hub dibinātāja, kura ir ļoti atklāta par savām aktivitātēm. Bet viņa ir augsta riska zonā, ja viņa apmeklētu rietumu valsti šajā brīdī, un varētu saskarties ar desmitiem gadu cietumsodu. Vai tas ir risks, kuru jūs būtu gatavs uzņemties? Mēs esam spektra otrā galā; esam ļoti uzmanīgi, lai neatstātu nekādas pēdas, un mums ir spēcīga operatīvā drošība."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Kā minēts HN \"ynno\", Aleksandra sākotnēji nevēlējās būt pazīstama: \"Viņas serveri bija iestatīti, lai izdotu detalizētus kļūdu ziņojumus no PHP, ieskaitot pilnu kļūdu avota faila ceļu, kas atradās direktorijā /home/<USER>" Tāpēc izmantojiet nejaušus lietotājvārdus datoros, kurus izmantojat šīm lietām, gadījumā, ja kaut ko nepareizi konfigurējat."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Tomēr šī slepenība nāk ar psiholoģiskām izmaksām. Lielākā daļa cilvēku mīl, kad viņu darbs tiek atzīts, un tomēr jūs nevarat saņemt nekādu atzinību par to reālajā dzīvē. Pat vienkāršas lietas var būt izaicinošas, piemēram, draugi jautā, ar ko esat nodarbojies (kādā brīdī \"ķimerējos ar savu NAS / homelab\" kļūst vecs)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Tāpēc ir tik svarīgi atrast kādu kopienu. Jūs varat atteikties no kādas operatīvās drošības, uzticoties dažiem ļoti tuviem draugiem, kuriem jūs zināt, ka varat dziļi uzticēties. Pat tad esiet uzmanīgi, lai neko nerakstītu, gadījumā, ja viņiem nāktos nodot savus e-pastus iestādēm vai ja viņu ierīces būtu apdraudētas kādā citā veidā."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Vēl labāk ir atrast dažus līdzīgi domājošus pirātus. Ja jūsu tuvi draugi ir ieinteresēti pievienoties jums, lieliski! Pretējā gadījumā jūs varētu atrast citus tiešsaistē. Diemžēl šī joprojām ir nišas kopiena. Līdz šim mēs esam atraduši tikai dažus citus, kas ir aktīvi šajā jomā. Labi sākumpunkti šķiet Library Genesis forumi un r/DataHoarder. Arhīva komanda arī ir līdzīgi domājoši indivīdi, lai gan viņi darbojas likuma ietvaros (pat ja dažās likuma pelēkajās zonās). Tradicionālās \"warez\" un pirātisma ainas arī ir cilvēki, kas domā līdzīgi."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Mēs esam atvērti idejām par to, kā veicināt kopienas attīstību un izpētīt idejas. Jūtieties brīvi sazināties ar mums Twitter vai Reddit. Varbūt mēs varētu rīkot kādu forumu vai čata grupu. Viens no izaicinājumiem ir tas, ka tas var viegli tikt cenzēts, izmantojot parastās platformas, tāpēc mums būtu jānodrošina pašiem. Ir arī kompromiss starp šo diskusiju pilnīgu publiskošanu (lielāka potenciālā iesaiste) un privātumu (neļaujot potenciālajiem \"mērķiem\" zināt, ka mēs gatavojamies tos pārmeklēt). Mums par to būs jādomā. Paziņojiet mums, ja jūs interesē šis!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekti"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Kad mēs veicam projektu, tam ir vairāki posmi:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domēna izvēle / filozofija: Kur jūs aptuveni vēlaties koncentrēties un kāpēc? Kādas ir jūsu unikālās kaislības, prasmes un apstākļi, kurus varat izmantot savā labā?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Mērķa izvēle: Kuru konkrēto kolekciju jūs spoguļosiet?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata nokasīšana: Failu informācijas katalogizēšana, faktiski neielādējot pašus (bieži vien daudz lielākos) failus."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Datu izvēle: Pamatojoties uz metadata, sašaurinot, kuri dati šobrīd ir visatbilstošākie arhivēšanai. Tas var būt viss, bet bieži vien ir saprātīgs veids, kā ietaupīt vietu un joslas platumu."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Datu nokasīšana: Faktiski iegūstot datus."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Izplatīšana: Iepakojot to torrentos, paziņojot par to kaut kur, liekot cilvēkiem to izplatīt."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Šie posmi nav pilnīgi neatkarīgi, un bieži vien ieskati no vēlākā posma liek jums atgriezties pie agrākā posma. Piemēram, metadata nokasīšanas laikā jūs varat saprast, ka izvēlētais mērķis ir ar aizsardzības mehānismiem, kas pārsniedz jūsu prasmju līmeni (piemēram, IP bloķēšana), tāpēc jūs atgriežaties un atrodat citu mērķi."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domēna izvēle / filozofija"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nav trūkuma zināšanu un kultūras mantojuma, kas būtu jāsaglabā, kas var būt pārsteidzoši. Tāpēc bieži vien ir noderīgi veltīt brīdi un padomāt par to, kāds varētu būt jūsu ieguldījums."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Katram ir atšķirīgs veids, kā par to domāt, bet šeit ir daži jautājumi, kurus jūs varētu sev uzdot:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Kāpēc jūs interesē šis? Kas jūs aizrauj? Ja mēs varam iegūt grupu cilvēku, kuri visi arhivē lietas, kas viņiem īpaši rūp, tas aptvertu daudz! Jūs zināsiet daudz vairāk nekā vidusmēra cilvēks par savu aizraušanos, piemēram, kādi ir svarīgi dati, ko saglabāt, kādas ir labākās kolekcijas un tiešsaistes kopienas utt."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Kādas prasmes jums ir, kuras varat izmantot savā labā? Piemēram, ja esat tiešsaistes drošības eksperts, jūs varat atrast veidus, kā pārvarēt IP bloķējumus drošiem mērķiem. Ja esat lielisks kopienu organizēšanā, tad varbūt jūs varat apvienot dažus cilvēkus ap mērķi. Tomēr ir noderīgi zināt kādu programmēšanu, ja tikai, lai uzturētu labu operatīvo drošību visā šajā procesā."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Cik daudz laika jums ir tam? Mūsu padoms būtu sākt ar mazākiem projektiem un veikt lielākus projektus, kad jūs to apgūstat, bet tas var kļūt par visu patērējošu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Kāda būtu augstas ietekmes joma, uz kuru koncentrēties? Ja jūs plānojat pavadīt X stundas pirātu arhivēšanā, tad kā jūs varat iegūt lielāko \"sprādzienu par savu naudu\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Kādas ir unikālas domas, kuras jūs par to domājat? Jums varētu būt dažas interesantas idejas vai pieejas, kuras citi varētu būt palaiduši garām."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Mūsu gadījumā mēs īpaši rūpējāmies par zinātnes ilgtermiņa saglabāšanu. Mēs zinājām par Library Genesis un to, kā tas tika pilnībā spoguļots daudzas reizes, izmantojot torrentus. Mums patika šī ideja. Tad kādu dienu viens no mums mēģināja atrast dažas zinātniskās mācību grāmatas Library Genesis, bet nevarēja tās atrast, kas radīja šaubas par to, cik pilnīgs tas patiesībā bija. Mēs tad meklējām šīs mācību grāmatas tiešsaistē un atradām tās citās vietās, kas iedēstīja sēklu mūsu projektam. Pat pirms mēs zinājām par Z-Library, mums bija ideja nemēģināt savākt visas šīs grāmatas manuāli, bet koncentrēties uz esošo kolekciju spoguļošanu un to atgriešanu Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Mērķa izvēle"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Tātad, mums ir mūsu izpētes joma, tagad kuru konkrēto kolekciju mēs spoguļosim? Ir daži faktori, kas padara mērķi labu:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Liela"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unikāla: nav jau labi pārklāta ar citiem projektiem."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Pieejama: neizmanto daudz aizsardzības slāņu, lai novērstu jūsu metadatu un datu nokasīšanu."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Īpaša ieskats: jums ir kāda īpaša informācija par šo mērķi, piemēram, jums ir īpaša piekļuve šai kolekcijai, vai arī jūs esat izdomājis, kā pārvarēt viņu aizsardzību. Tas nav obligāti (mūsu gaidāmais projekts neko īpašu nedara), bet tas noteikti palīdz!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Kad mēs atradām mūsu zinātniskās mācību grāmatas vietnēs, kas nav Library Genesis, mēs mēģinājām noskaidrot, kā tās nonāca internetā. Tad mēs atradām Z-Library un sapratām, ka, lai gan lielākā daļa grāmatu sākotnēji neparādās tur, tās galu galā tur nonāk. Mēs uzzinājām par tās attiecībām ar Library Genesis un (finansiālo) stimulu struktūru un pārāko lietotāja saskarni, kas abas padarīja to par daudz pilnīgāku kolekciju. Tad mēs veicām sākotnējo metadatu un datu nokasīšanu un sapratām, ka varam apiet viņu IP lejupielādes ierobežojumus, izmantojot viena no mūsu biedru īpašo piekļuvi daudziem starpniekserveriem."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Izpētot dažādus mērķus, jau ir svarīgi slēpt savas pēdas, izmantojot VPN un vienreizējās e-pasta adreses, par ko mēs runāsim vēlāk."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadatu nokasīšana"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Iesim nedaudz tehniskāk. Lai faktiski nokasītu metadatus no vietnēm, mēs esam saglabājuši lietas diezgan vienkāršas. Mēs izmantojam Python skriptus, dažreiz curl, un MySQL datubāzi, lai saglabātu rezultātus. Mēs neesam izmantojuši nekādu sarežģītu nokasīšanas programmatūru, kas var kartēt sarežģītas vietnes, jo līdz šim mums bija nepieciešams nokasīt tikai vienu vai divu veidu lapas, vienkārši izskaitot cauri ID un parsējot HTML. Ja nav viegli izskaitāmu lapu, tad jums var būt nepieciešams pareizs rāpuļprogramma, kas mēģina atrast visas lapas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Pirms sākat nokasīt visu vietni, mēģiniet to darīt manuāli uz brīdi. Pārejiet cauri dažām desmitiem lapu paši, lai iegūtu sajūtu, kā tas darbojas. Dažreiz jūs jau šādā veidā sastapsieties ar IP bloķēšanu vai citu interesantu uzvedību. Tas pats attiecas uz datu nokasīšanu: pirms pārāk dziļi iedziļināties šajā mērķī, pārliecinieties, ka varat efektīvi lejupielādēt tā datus."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Lai apietu ierobežojumus, ir dažas lietas, ko varat izmēģināt. Vai ir kādas citas IP adreses vai serveri, kas mitina tos pašus datus, bet kuriem nav tādu pašu ierobežojumu? Vai ir kādi API galapunkti, kuriem nav ierobežojumu, kamēr citiem ir? Pie kāda lejupielādes ātruma jūsu IP tiek bloķēts un uz cik ilgu laiku? Vai arī jūs netiekat bloķēts, bet ātrums tiek samazināts? Kas notiek, ja izveidojat lietotāja kontu, kā tad mainās lietas? Vai varat izmantot HTTP/2, lai saglabātu savienojumus atvērtus, un vai tas palielina ātrumu, kādā varat pieprasīt lapas? Vai ir lapas, kas uzskaita vairākus failus vienlaikus, un vai tur uzskaitītā informācija ir pietiekama?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Lietas, kuras jūs, iespējams, vēlaties saglabāt, ietver:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Nosaukums"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Faila nosaukums / atrašanās vieta"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: var būt kāds iekšējs ID, bet ID, piemēram, ISBN vai DOI, ir noderīgi arī."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Izmērs: lai aprēķinātu, cik daudz diska vietas jums ir nepieciešams."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): lai apstiprinātu, ka esat pareizi lejupielādējis failu."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Pievienošanas/modificēšanas datums: lai jūs varētu atgriezties vēlāk un lejupielādēt failus, kurus iepriekš neesat lejupielādējis (lai gan bieži vien varat izmantot arī ID vai hash šim nolūkam)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Apraksts, kategorija, tagi, autori, valoda utt."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Parasti mēs to darām divos posmos. Vispirms mēs lejupielādējam neapstrādātos HTML failus, parasti tieši MySQL (lai izvairītos no daudz mazu failu, par ko mēs runājam vairāk zemāk). Tad, atsevišķā solī, mēs izskatām šos HTML failus un parsējam tos faktiskajās MySQL tabulās. Tādā veidā jums nav jāpārlejupielādē viss no jauna, ja atklājat kļūdu savā parsēšanas kodā, jo varat vienkārši pārstrādāt HTML failus ar jauno kodu. Tas arī bieži vien ir vieglāk paralelizēt apstrādes soli, tādējādi ietaupot laiku (un jūs varat rakstīt apstrādes kodu, kamēr nokasīšana notiek, nevis rakstīt abus soļus vienlaikus)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Visbeidzot, ņemiet vērā, ka dažiem mērķiem metadatu iegūšana ir viss, kas ir pieejams. Ir dažas milzīgas metadatu kolekcijas, kas nav pienācīgi saglabātas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Datu atlase"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Bieži vien jūs varat izmantot metadatus, lai noteiktu saprātīgu datu apakškopu, ko lejupielādēt. Pat ja jūs galu galā vēlaties lejupielādēt visus datus, var būt noderīgi prioritizēt vissvarīgākos vienumus vispirms, gadījumā, ja jūs tiekat atklāts un aizsardzība tiek uzlabota, vai arī tāpēc, ka jums būtu jāiegādājas vairāk disku, vai vienkārši tāpēc, ka kaut kas cits notiek jūsu dzīvē, pirms jūs varat lejupielādēt visu."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Piemēram, kolekcijā var būt vairākas vienas un tās pašas resursa (piemēram, grāmatas vai filmas) izdevumi, kur viens ir atzīmēts kā vislabākās kvalitātes. Saglabāt šos izdevumus vispirms būtu ļoti saprātīgi. Jūs varētu galu galā vēlēties saglabāt visus izdevumus, jo dažos gadījumos metadati var būt nepareizi marķēti, vai arī var būt nezināmi kompromisi starp izdevumiem (piemēram, \"labākais izdevums\" var būt labākais lielākajā daļā veidu, bet sliktāks citos veidos, piemēram, filmai ir augstāka izšķirtspēja, bet trūkst subtitru)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Jūs varat arī meklēt savā metadatu datubāzē, lai atrastu interesantas lietas. Kāds ir lielākais fails, kas tiek mitināts, un kāpēc tas ir tik liels? Kāds ir mazākais fails? Vai ir interesanti vai negaidīti modeļi attiecībā uz noteiktām kategorijām, valodām un tā tālāk? Vai ir dublikāti vai ļoti līdzīgi nosaukumi? Vai ir modeļi, kad dati tika pievienoti, piemēram, viena diena, kurā tika pievienoti daudzi faili vienlaikus? Jūs bieži varat daudz uzzināt, aplūkojot datu kopu dažādos veidos."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Mūsu gadījumā mēs no Z-Library grāmatām noņēmām dublikātus, salīdzinot md5 hashus ar Library Genesis, tādējādi ietaupot daudz lejupielādes laika un diska vietas. Tomēr šī ir diezgan unikāla situācija. Vairumā gadījumu nav visaptverošu datubāzu par to, kuri faili jau ir pienācīgi saglabāti citu pirātu vidū. Tas pats par sevi ir milzīga iespēja kādam tur ārā. Būtu lieliski, ja būtu regulāri atjaunināts pārskats par tādām lietām kā mūzika un filmas, kas jau ir plaši izplatītas torrentu vietnēs, un tāpēc ir zemāka prioritāte iekļaušanai pirātu spoguļos."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Datu iegūšana"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Tagad jūs esat gatavs faktiski lejupielādēt datus lielā apjomā. Kā minēts iepriekš, šajā brīdī jums jau vajadzētu manuāli lejupielādēt vairākus failus, lai labāk izprastu mērķa uzvedību un ierobežojumus. Tomēr, kad jūs faktiski sākat lejupielādēt daudz failu vienlaikus, joprojām būs pārsteigumi."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Mūsu padoms šeit ir galvenokārt saglabāt to vienkāršu. Sāciet, vienkārši lejupielādējot vairākus failus. Jūs varat izmantot Python un pēc tam paplašināt līdz vairākiem pavedieniem. Bet dažreiz pat vienkāršāk ir ģenerēt Bash failus tieši no datubāzes un pēc tam palaist vairākus no tiem vairākos termināļa logos, lai palielinātu apjomu. Ātrs tehnisks triks, kas šeit ir vērts pieminēt, ir OUTFILE izmantošana MySQL, ko varat rakstīt jebkur, ja atspējojat \"secure_file_priv\" mysqld.cnf (un pārliecinieties, ka arī atspējojat/pārrakstāt AppArmor, ja esat uz Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Mēs glabājam datus uz vienkāršiem cietajiem diskiem. Sāciet ar to, kas jums ir, un paplašiniet lēnām. Var būt pārsteidzoši domāt par simtiem TB datu glabāšanu. Ja tā ir situācija, ar kuru jūs saskaraties, vienkārši izveidojiet labu apakškopu vispirms un savā paziņojumā lūdziet palīdzību pārējā glabāšanā. Ja jūs vēlaties iegūt vairāk cieto disku pats, tad r/DataHoarder ir daži labi resursi, lai iegūtu labus piedāvājumus."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Centieties pārāk neuztraukties par sarežģītām failu sistēmām. Ir viegli iekrist truša alā, uzstādot tādas lietas kā ZFS. Tomēr viens tehnisks sīkums, kas jāņem vērā, ir tas, ka daudzas failu sistēmas netiek galā labi ar daudziem failiem. Mēs esam atraduši, ka vienkāršs risinājums ir izveidot vairākas direktorijas, piemēram, dažādiem ID diapazoniem vai hash prefiksiem."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Pēc datu lejupielādes pārliecinieties, ka pārbaudāt failu integritāti, izmantojot metadatos pieejamos hashus, ja tie ir pieejami."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Izplatīšana"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Jums ir dati, tādējādi dodot jums pasaules pirmo pirātu spoguli jūsu mērķim (visticamāk). Daudzos veidos grūtākā daļa ir beigusies, bet riskantākā daļa vēl ir priekšā. Galu galā, līdz šim jūs esat bijis slepens; lidojot zem radara. Viss, kas jums bija jādara, bija izmantot labu VPN visā laikā, neaizpildot savus personīgos datus nevienā formā (protams), un, iespējams, izmantojot īpašu pārlūkprogrammas sesiju (vai pat citu datoru)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Tagad jums ir jāizplata dati. Mūsu gadījumā mēs vispirms vēlējāmies atdot grāmatas atpakaļ Library Genesis, bet ātri atklājām grūtības tajā (daiļliteratūras un nedaiļliteratūras šķirošana). Tāpēc mēs nolēmām izplatīt, izmantojot Library Genesis stila torrentus. Ja jums ir iespēja piedalīties esošā projektā, tas varētu ietaupīt daudz laika. Tomēr pašlaik nav daudz labi organizētu pirātu spoguļu."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Tātad, pieņemsim, ka jūs nolemjat izplatīt torrentus pats. Centieties saglabāt šos failus mazos, lai tos būtu viegli spoguļot citās vietnēs. Jums tad būs jāizsēj torrentus pašam, vienlaikus paliekot anonīmam. Jūs varat izmantot VPN (ar vai bez portu pārsūtīšanas) vai maksāt ar sajauktiem Bitcoin par Seedbox. Ja jūs nezināt, ko daži no šiem terminiem nozīmē, jums būs daudz lasīšanas darāmā, jo ir svarīgi, lai jūs saprastu risku kompromisus šeit."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Jūs varat mitināt pašus torrent failus esošajās torrent vietnēs. Mūsu gadījumā mēs izvēlējāmies faktiski mitināt vietni, jo mēs arī vēlējāmies skaidri izplatīt savu filozofiju. Jūs varat to darīt pats līdzīgā veidā (mēs izmantojam Njalla mūsu domēniem un mitināšanai, maksājot ar sajauktiem Bitcoin), bet arī nevilcinieties sazināties ar mums, lai mēs varētu mitināt jūsu torrentus. Mēs vēlamies laika gaitā izveidot visaptverošu pirātu spoguļu indeksu, ja šī ideja kļūst populāra."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Kas attiecas uz VPN izvēli, par to jau ir daudz rakstīts, tāpēc mēs vienkārši atkārtosim vispārējo padomu izvēlēties pēc reputācijas. Faktiski tiesā pārbaudītas bezlogu politikas ar ilgu pieredzi privātuma aizsardzībā ir zemākais riska variants, mūsuprāt. Ņemiet vērā, ka pat tad, ja jūs darāt visu pareizi, jūs nekad nevarat sasniegt nulles risku. Piemēram, kad jūs izsējat savus torrentus, ļoti motivēts valsts aktors, iespējams, var apskatīt ienākošos un izejošos datu plūsmas VPN serveriem un secināt, kas jūs esat. Vai arī jūs vienkārši varat kaut kādā veidā kļūdīties. Mēs, iespējams, jau esam kļūdījušies un kļūdīsimies atkal. Par laimi, valstis neuztraucas <em>tik</em> daudz par pirātismu."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Viena no lēmumiem, kas jāpieņem katram projektam, ir, vai to publicēt, izmantojot to pašu identitāti kā iepriekš, vai nē. Ja jūs turpināt izmantot to pašu vārdu, tad kļūdas operatīvajā drošībā no iepriekšējiem projektiem var atgriezties un jūs sakost. Bet publicēšana ar dažādiem vārdiem nozīmē, ka jūs neveidojat ilgstošāku reputāciju. Mēs izvēlējāmies no sākuma nodrošināt spēcīgu operatīvo drošību, lai mēs varētu turpināt izmantot to pašu identitāti, bet mēs nevilcināsimies publicēt ar citu vārdu, ja mēs kļūdīsimies vai ja apstākļi to prasīs."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Vārda izplatīšana var būt sarežģīta. Kā mēs teicām, šī joprojām ir nišas kopiena. Mēs sākotnēji publicējām Reddit, bet patiesi guvām atsaucību Hacker News. Pašlaik mūsu ieteikums ir to publicēt dažās vietās un redzēt, kas notiek. Un atkal, sazinieties ar mums. Mēs labprāt izplatītu vairāk pirātu arhīvisma centienu vārdu."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Secinājums"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Ceramies, ka tas ir noderīgi jaunajiem pirātu arhivāriem. Mēs esam priecīgi jūs sveikt šajā pasaulē, tāpēc nevilcinieties sazināties. Saglabāsim pēc iespējas vairāk pasaules zināšanu un kultūras un izplatīsim to plaši un tālu."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Iepazīstinām ar Pirātu bibliotēkas spoguli: 7TB grāmatu saglabāšana (kas nav Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Šis projekts (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>) mērķē veicināt cilvēces zināšanu saglabāšanu un atbrīvošanu. Mēs sniedzam savu mazo un pieticīgo ieguldījumu, sekojot lielo priekšgājēju pēdās."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Šī projekta fokuss ir ilustrēts tā nosaukumā:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirāts</strong> - Mēs apzināti pārkāpjam autortiesību likumu lielākajā daļā valstu. Tas ļauj mums darīt to, ko juridiskas personas nevar: nodrošināt, ka grāmatas tiek izplatītas plaši un tālu."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotēka</strong> - Tāpat kā lielākā daļa bibliotēku, mēs galvenokārt koncentrējamies uz rakstiskiem materiāliem, piemēram, grāmatām. Iespējams, nākotnē paplašināsimies uz citiem mediju veidiem."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spogulis</strong> - Mēs esam stingri esošo bibliotēku spogulis. Mēs koncentrējamies uz saglabāšanu, nevis uz grāmatu vieglu meklēšanu un lejupielādi (piekļuvi) vai lielas kopienas veidošanu, kas pievieno jaunas grāmatas (avoti)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Pirmā bibliotēka, kuru mēs esam spoguļojuši, ir Z-Bibliotēka. Tā ir populāra (un nelegāla) bibliotēka. Viņi ir paņēmuši Library Genesis kolekciju un padarījuši to viegli meklējamu. Turklāt viņi ir kļuvuši ļoti efektīvi jaunu grāmatu ieguldījumu piesaistē, motivējot lietotājus ar dažādiem labumiem. Pašlaik viņi neatgriež šīs jaunās grāmatas atpakaļ Library Genesis. Un atšķirībā no Library Genesis, viņi nepadara savu kolekciju viegli spoguļojamu, kas kavē plašu saglabāšanu. Tas ir svarīgi viņu biznesa modelim, jo viņi iekasē maksu par piekļuvi savai kolekcijai lielos apjomos (vairāk nekā 10 grāmatas dienā)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Mēs neveicam morālus spriedumus par naudas iekasēšanu par masveida piekļuvi nelikumīgai grāmatu kolekcijai. Nav šaubu, ka Z-Library ir veiksmīgi paplašinājusi piekļuvi zināšanām un nodrošinājusi vairāk grāmatu. Mēs esam šeit, lai veiktu savu daļu: nodrošināt šīs privātās kolekcijas ilgtermiņa saglabāšanu."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Mēs vēlamies jūs aicināt palīdzēt saglabāt un atbrīvot cilvēces zināšanas, lejupielādējot un sējot mūsu torrentus. Skatiet projekta lapu, lai iegūtu vairāk informācijas par to, kā dati ir organizēti."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Mēs arī ļoti vēlamies aicināt jūs sniegt savas idejas par to, kuras kolekcijas spoguļot nākamās un kā to darīt. Kopā mēs varam sasniegt daudz. Tas ir tikai neliels ieguldījums starp neskaitāmiem citiem. Paldies par visu, ko darāt."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Mēs nesaistām failus no šī emuāra. Lūdzu, atrodiet tos paši.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb izgāztuve, vai Cik daudz grāmatu ir saglabātas uz visiem laikiem?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Ja mēs pareizi deduplicētu failus no ēnu bibliotēkām, kādu procentu no visām pasaules grāmatām mēs esam saglabājuši?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Ar Pirātu bibliotēkas spoguli (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>), mūsu mērķis ir paņemt visas pasaules grāmatas un saglabāt tās uz visiem laikiem.<sup>1</sup> Starp mūsu Z-Bibliotēkas torrentiem un oriģinālajiem Library Genesis torrentiem mums ir 11,783,153 faili. Bet cik tas patiesībā ir? Ja mēs pareizi deduplicētu šos failus, kādu procentu no visām pasaules grāmatām mēs esam saglabājuši? Mēs tiešām vēlētos, lai būtu kaut kas tāds:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10%% cilvēces rakstiskā mantojuma saglabāts uz visiem laikiem"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Lai iegūtu procentu, mums ir nepieciešams saucējs: kopējais jebkad publicēto grāmatu skaits.<sup>2</sup> Pirms Google Books izbeigšanās, projekta inženieris Leonids Teičers <a %(booksearch_blogspot)s>mēģināja novērtēt</a> šo skaitli. Viņš nonāca pie — ar humoru — 129,864,880 (“vismaz līdz svētdienai”). Viņš novērtēja šo skaitli, izveidojot vienotu datubāzi par visām pasaules grāmatām. Šim nolūkam viņš apvienoja dažādus Datasets un pēc tam tos dažādos veidos apvienoja."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Kā neliels ievads, ir vēl kāds cilvēks, kurš mēģināja katalogizēt visas pasaules grāmatas: Ārons Švarcs, nelaiķis digitālais aktīvists un Reddit līdzdibinātājs.<sup>3</sup> Viņš <a %(youtube)s>sāka Open Library</a> ar mērķi “viena tīmekļa lapa katrai jebkad publicētajai grāmatai”, apvienojot datus no dažādiem avotiem. Viņš samaksāja augstāko cenu par savu digitālās saglabāšanas darbu, kad tika apsūdzēts par akadēmisko rakstu masveida lejupielādi, kas noveda pie viņa pašnāvības. Needless to say, this is one of the reasons our group is pseudonymous, and why we’re being very careful. Open Library joprojām varonīgi vada cilvēki no Internet Archive, turpinot Ārona mantojumu. Mēs pie šī atgriezīsimies vēlāk šajā ierakstā."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Google emuāra ierakstā Teičers apraksta dažus izaicinājumus, kas saistīti ar šī skaitļa novērtēšanu. Pirmkārt, kas ir grāmata? Ir dažas iespējamās definīcijas:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fiziskās kopijas.</strong> Acīmredzot tas nav ļoti noderīgi, jo tās ir tikai tā paša materiāla dublikāti. Būtu lieliski, ja mēs varētu saglabāt visas anotācijas, ko cilvēki veic grāmatās, piemēram, Fermā slavenās “pierakstus malās”. Bet diemžēl tas paliks arhivāra sapnis."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Darbi”.</strong> Piemēram, “Harijs Poters un Noslēpumu kambaris” kā loģisks jēdziens, kas ietver visas tā versijas, piemēram, dažādus tulkojumus un atkārtotus izdevumus. Šī ir noderīga definīcija, bet var būt grūti noteikt, kas tiek skaitīts. Piemēram, mēs, iespējams, vēlamies saglabāt dažādus tulkojumus, lai gan atkārtoti izdevumi ar tikai nelielām atšķirībām var nebūt tik svarīgi."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Izdevumi”.</strong> Šeit jūs skaitāt katru unikālo grāmatas versiju. Ja kaut kas par to ir atšķirīgs, piemēram, atšķirīgs vāks vai atšķirīgs priekšvārds, tas tiek uzskatīts par citu izdevumu."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Faili.</strong> Strādājot ar ēnu bibliotēkām, piemēram, Library Genesis, Sci-Hub vai Z-Library, ir papildu apsvērums. Var būt vairāki viena izdevuma skenējumi. Un cilvēki var izveidot labākas esošo failu versijas, skenējot tekstu, izmantojot OCR, vai labojot lapas, kas tika skenētas leņķī. Mēs vēlamies skaitīt šos failus tikai kā vienu izdevumu, kas prasītu labu metadata vai dublēšanas novēršanu, izmantojot dokumentu līdzības mērus."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Editions” šķiet vispraktiskākā definīcija tam, kas ir “grāmatas”. Ērti, ka šī definīcija tiek izmantota arī unikālu ISBN numuru piešķiršanai. ISBN jeb Starptautiskais standarta grāmatas numurs tiek plaši izmantots starptautiskajā tirdzniecībā, jo tas ir integrēts starptautiskajā svītrkodu sistēmā (“Starptautiskais preču numurs”). Ja vēlaties pārdot grāmatu veikalos, tai ir nepieciešams svītrkods, tāpēc jūs saņemat ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Teičera emuāra ierakstā minēts, ka, lai gan ISBN ir noderīgi, tie nav universāli, jo tie tika patiešām pieņemti tikai septiņdesmito gadu vidū un ne visur pasaulē. Tomēr ISBN, iespējams, ir visplašāk izmantotais grāmatu izdevumu identifikators, tāpēc tas ir mūsu labākais sākumpunkts. Ja mēs varam atrast visus pasaules ISBN, mēs iegūstam noderīgu sarakstu ar grāmatām, kuras vēl ir jāsaglabā."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Tātad, kur mēs iegūstam datus? Ir vairāki esoši centieni, kas mēģina apkopot visu pasaules grāmatu sarakstu:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Galu galā viņi veica šo pētījumu Google Books. Tomēr viņu metadata nav pieejama masveidā un ir diezgan grūti nokasīt."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Kā minēts iepriekš, tas ir viņu viss mērķis. Viņi ir ieguvuši milzīgus bibliotēku datus no sadarbības bibliotēkām un nacionālajiem arhīviem un turpina to darīt. Viņiem ir arī brīvprātīgie bibliotekāri un tehniskā komanda, kas mēģina novērst ierakstu dublēšanos un marķēt tos ar visdažādākajiem metadata. Vislabākais ir tas, ka viņu datu kopums ir pilnīgi atvērts. Jūs varat vienkārši <a %(openlibrary)s>lejupielādēt to</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Šī ir vietne, ko pārvalda bezpeļņas organizācija OCLC, kas pārdod bibliotēku pārvaldības sistēmas. Viņi apkopo grāmatu metadata no daudzām bibliotēkām un padara to pieejamu caur WorldCat vietni. Tomēr viņi arī pelna naudu, pārdodot šos datus, tāpēc tie nav pieejami masveida lejupielādei. Viņiem ir pieejami daži ierobežotāki masveida datu kopumi lejupielādei, sadarbojoties ar konkrētām bibliotēkām."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Šis ir šī emuāra ieraksta temats. ISBNdb nokasa dažādas vietnes, lai iegūtu grāmatu metadata, īpaši cenu datus, kurus viņi pēc tam pārdod grāmatu pārdevējiem, lai viņi varētu noteikt savu grāmatu cenas atbilstoši pārējam tirgum. Tā kā ISBN mūsdienās ir diezgan universāli, viņi faktiski izveidoja “tīmekļa lapu katrai grāmatai”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Dažādas individuālas bibliotēku sistēmas un arhīvi.</strong> Ir bibliotēkas un arhīvi, kas nav indeksēti un apkopoti nevienā no iepriekš minētajiem, bieži vien tāpēc, ka tie ir nepietiekami finansēti vai citu iemeslu dēļ nevēlas dalīties ar saviem datiem ar Open Library, OCLC, Google un tā tālāk. Daudziem no tiem ir digitālie ieraksti, kas pieejami internetā, un tie bieži vien nav ļoti labi aizsargāti, tāpēc, ja vēlaties palīdzēt un izklaidēties, mācoties par dīvainām bibliotēku sistēmām, šie ir lieliski sākumpunkti."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Šajā ierakstā mēs ar prieku paziņojam par nelielu izlaidumu (salīdzinot ar mūsu iepriekšējiem Z-Library izlaidumiem). Mēs nokasījām lielāko daļu ISBNdb un padarījām datus pieejamus torrentēšanai Pirate Library Mirror vietnē (REDIĢĒT: pārvietots uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>; mēs to šeit tieši nesasaistīsim, vienkārši meklējiet to). Tie ir aptuveni 30,9 miljoni ierakstu (20GB kā <a %(jsonlines)s>JSON Lines</a>; 4,4GB saspiesti). Viņu vietnē viņi apgalvo, ka viņiem faktiski ir 32,6 miljoni ierakstu, tāpēc mēs, iespējams, kaut kā esam palaiduši garām dažus, vai arī <em>viņi</em> varētu kaut ko darīt nepareizi. Jebkurā gadījumā, pagaidām mēs neizpaudīsim, kā mēs to izdarījām — mēs to atstāsim kā uzdevumu lasītājam. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Ko mēs dalīsimies, ir daži sākotnējie analīzes rezultāti, lai mēģinātu tuvoties pasaules grāmatu skaita novērtēšanai. Mēs aplūkojām trīs datu kopas: šo jauno ISBNdb datu kopu, mūsu sākotnējo metadata izlaidumu, ko mēs nokasījām no Z-Library ēnu bibliotēkas (kas ietver Library Genesis), un Open Library datu izgāztuvi."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Sāksim ar dažiem aptuveniem skaitļiem:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Gan Z-Library/Libgen, gan Open Library ir daudz vairāk grāmatu nekā unikālu ISBN. Vai tas nozīmē, ka daudzām no šīm grāmatām nav ISBN, vai arī ISBN metadata vienkārši trūkst? Mēs, iespējams, varam atbildēt uz šo jautājumu, izmantojot automatizētu saskaņošanu, pamatojoties uz citiem atribūtiem (nosaukums, autors, izdevējs utt.), pievienojot vairāk datu avotu un iegūstot ISBN no pašiem grāmatu skenējumiem (Z-Library/Libgen gadījumā)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Cik no šiem ISBN ir unikāli? To vislabāk ilustrē ar Venn diagrammu:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Lai būtu precīzāk:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Mūs pārsteidza, cik maz ir pārklāšanās! ISBNdb ir milzīgs daudzums ISBN, kas neparādās ne Z-Library, ne Open Library, un tas pats attiecas (mazākā, bet joprojām ievērojamā mērā) uz pārējiem diviem. Tas rada daudz jaunu jautājumu. Cik daudz automatizēta saskaņošana palīdzētu marķēt grāmatas, kas nebija marķētas ar ISBN? Vai būtu daudz saskaņojumu un tādējādi palielināta pārklāšanās? Arī, kas notiktu, ja mēs pievienotu 4. vai 5. datu kopu? Cik daudz pārklāšanās mēs tad redzētu?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Tas mums dod sākumpunktu. Tagad mēs varam aplūkot visus ISBN, kas nebija Z-Library datu kopā un kas nesakrīt arī ar nosaukuma/autora laukiem. Tas var dot mums iespēju saglabāt visas pasaules grāmatas: vispirms, meklējot internetā skenējumus, pēc tam dodoties reālajā dzīvē, lai skenētu grāmatas. Pēdējo pat varētu finansēt pūļa veidā vai vadīt ar \"atlīdzībām\" no cilvēkiem, kuri vēlētos redzēt konkrētas grāmatas digitalizētas. Tas viss ir stāsts citam laikam."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Ja vēlaties palīdzēt ar kādu no šiem uzdevumiem — tālāka analīze; vairāk metadatu iegūšana; vairāk grāmatu atrašana; grāmatu OCR veikšana; to darīšana citās jomās (piemēram, raksti, audiogrāmatas, filmas, TV šovi, žurnāli) vai pat dažu šo datu pieejamības nodrošināšana tādām lietām kā ML / lielu valodu modeļu apmācība — lūdzu, sazinieties ar mani (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Ja jūs īpaši interesē datu analīze, mēs strādājam pie tā, lai mūsu datu kopas un skripti būtu pieejami vieglāk lietojamā formātā. Būtu lieliski, ja jūs varētu vienkārši dakšot piezīmju grāmatiņu un sākt ar to spēlēties."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Visbeidzot, ja vēlaties atbalstīt šo darbu, lūdzu, apsveriet iespēju veikt ziedojumu. Šī ir pilnībā brīvprātīga darbība, un jūsu ieguldījums rada milzīgu atšķirību. Katrs mazumiņš palīdz. Pašlaik mēs pieņemam ziedojumus kriptovalūtā; skatiet ziedojumu lapu Annas Arhīvā."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Par kādu saprātīgu \"mūžības\" definīciju. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Protams, cilvēces rakstītais mantojums ir daudz vairāk nekā grāmatas, īpaši mūsdienās. Šī ieraksta un mūsu neseno izlaidumu dēļ mēs koncentrējamies uz grāmatām, bet mūsu intereses sniedzas tālāk."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Par Āronu Svartzu var teikt daudz vairāk, bet mēs tikai vēlējāmies viņu īsi pieminēt, jo viņš spēlē izšķirošu lomu šajā stāstā. Laikam ejot, vairāk cilvēku varētu pirmo reizi sastapties ar viņa vārdu un pēc tam paši iedziļināties šajā tēmā."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Ēnu bibliotēku kritiskais logs"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi, ja tās jau tuvojas 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Ķīniešu versija 中文版</a>, apspriest <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Annas Arhīvā mums bieži jautā, kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi, ja to kopējais apjoms jau tuvojas 1 petabaitam (1000 TB) un turpina pieaugt. Šajā rakstā mēs aplūkosim mūsu filozofiju un redzēsim, kāpēc nākamā desmitgade ir kritiska mūsu misijai saglabāt cilvēces zināšanas un kultūru."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Mūsu kolekciju <a %(annas_archive_stats)s>kopējais apjoms</a> pēdējo mēnešu laikā, sadalīts pēc torrentu sējēju skaita."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritātes"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Kāpēc mums tik ļoti rūp raksti un grāmatas? Atstāsim malā mūsu pamatuzskatu par saglabāšanu vispār — mēs varētu par to uzrakstīt citu ierakstu. Tātad, kāpēc tieši raksti un grāmatas? Atbilde ir vienkārša: <strong>informācijas blīvums</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Uzglabāšanas megabaitā rakstītais teksts uzglabā visvairāk informācijas no visiem medijiem. Lai gan mums rūp gan zināšanas, gan kultūra, mēs vairāk rūpējamies par pirmajām. Kopumā mēs atrodam informācijas blīvuma un saglabāšanas nozīmīguma hierarhiju, kas izskatās aptuveni šādi:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akadēmiskie raksti, žurnāli, ziņojumi"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organiski dati, piemēram, DNS sekvences, augu sēklas vai mikrobu paraugi"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Daiļliteratūras grāmatas"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Zinātnes un inženierijas programmatūras kods"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Mērījumu dati, piemēram, zinātniskie mērījumi, ekonomiskie dati, korporatīvie ziņojumi"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Zinātnes un inženierijas tīmekļa vietnes, tiešsaistes diskusijas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Daiļliteratūras žurnāli, avīzes, rokasgrāmatas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Daiļliteratūras runu, dokumentālo filmu, podkāstu transkripti"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Iekšējie dati no korporācijām vai valdībām (noplūdes)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadatu ieraksti vispārīgi (par daiļliteratūru un nedaiļliteratūru; par citiem medijiem, mākslu, cilvēkiem utt.; ieskaitot atsauksmes)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Ģeogrāfiskie dati (piemēram, kartes, ģeoloģiskās aptaujas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Juridisko vai tiesas procesu transkripti"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Izdomāti vai izklaidējoši visu iepriekš minēto versijas"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Šī saraksta secība ir nedaudz patvaļīga — vairāki vienumi ir vienādi vai mūsu komandā ir nesaskaņas — un mēs, iespējams, aizmirstam dažas svarīgas kategorijas. Bet aptuveni tā mēs prioritizējam."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Daži no šiem vienumiem ir pārāk atšķirīgi no citiem, lai mēs par tiem uztrauktos (vai jau ir parūpējušās citas iestādes), piemēram, organiskie dati vai ģeogrāfiskie dati. Bet lielākā daļa no šī saraksta vienumiem mums patiesībā ir svarīgi."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Vēl viens liels faktors mūsu prioritizācijā ir tas, cik lielā mērā kāds darbs ir apdraudēts. Mēs dodam priekšroku koncentrēties uz darbiem, kas ir:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Reti"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unikāli nepietiekami fokusēti"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unikāli iznīcināšanas riskā (piemēram, kara, finansējuma samazinājumu, tiesas prāvu vai politiskās vajāšanas dēļ)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Visbeidzot, mums rūp mērogs. Mums ir ierobežots laiks un nauda, tāpēc mēs labprātāk pavadītu mēnesi, glābjot 10 000 grāmatu, nevis 1 000 grāmatu — ja tās ir aptuveni vienlīdz vērtīgas un apdraudētas."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Ēnu bibliotēkas"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ir daudz organizāciju, kurām ir līdzīgi mērķi un līdzīgas prioritātes. Patiesi, ir bibliotēkas, arhīvi, laboratorijas, muzeji un citas iestādes, kas ir atbildīgas par šāda veida saglabāšanu. Daudzas no tām ir labi finansētas, no valdībām, indivīdiem vai korporācijām. Bet tām ir viens milzīgs aklais punkts: tiesību sistēma."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Šeit slēpjas ēnu bibliotēku unikālā loma un iemesls, kāpēc pastāv Annas Arhīvs. Mēs varam darīt lietas, ko citas iestādes nedrīkst darīt. Tagad, tas nav (bieži) tā, ka mēs varam arhivēt materiālus, kas citur ir nelikumīgi saglabājami. Nē, daudzās vietās ir likumīgi veidot arhīvu ar jebkurām grāmatām, rakstiem, žurnāliem un tā tālāk."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Bet ko bieži trūkst juridiskajiem arhīviem, ir <strong>redundance un ilgmūžība</strong>. Ir grāmatas, no kurām tikai viena kopija eksistē kādā fiziskā bibliotēkā kaut kur. Ir metadatu ieraksti, kurus sargā tikai viena korporācija. Ir avīzes, kas saglabātas tikai mikrofilmā vienā arhīvā. Bibliotēkas var saņemt finansējuma samazinājumus, korporācijas var bankrotēt, arhīvi var tikt bombardēti un nodedzināti līdz pamatiem. Tas nav hipotētiski — tas notiek visu laiku."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Ko mēs varam unikāli darīt Annas Arhīvā, ir uzglabāt daudz kopiju darbiem, lielā mērogā. Mēs varam savākt rakstus, grāmatas, žurnālus un vairāk, un izplatīt tos lielos apjomos. Pašlaik mēs to darām caur torrentiem, bet precīzas tehnoloģijas nav svarīgas un laika gaitā mainīsies. Svarīgākais ir iegūt daudzas kopijas, kas izplatītas visā pasaulē. Šis citāts no vairāk nekā 200 gadiem atpakaļ joprojām ir aktuāls:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Zaudēto nevar atgūt; bet saglabāsim to, kas paliek: nevis ar seifiem un slēdzenēm, kas tos nošķir no sabiedrības acīm un lietošanas, nododot tos laika atkritumiem, bet ar tādu kopiju pavairošanu, kas tos padarīs nepieejamus nejaušībām.</q></em><br>— Tomass Džefersons, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Īsa piezīme par publisko domēnu. Tā kā Annas Arhīvs unikāli koncentrējas uz darbībām, kas daudzās vietās pasaulē ir nelikumīgas, mēs neuztraucamies par plaši pieejamām kolekcijām, piemēram, publiskā domēna grāmatām. Juridiskās iestādes bieži vien jau labi par to rūpējas. Tomēr ir apsvērumi, kas dažkārt liek mums strādāt pie publiski pieejamām kolekcijām:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadatu ierakstus var brīvi skatīt Worldcat vietnē, bet tos nevar lejupielādēt lielos apjomos (līdz mēs tos <a %(worldcat_scrape)s>nokasījām</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kods var būt atvērts avots Github, bet Github kopumā nevar viegli spoguļot un tādējādi saglabāt (lai gan šajā konkrētajā gadījumā ir pietiekami izplatītas lielākās daļas kodu repozitoriju kopijas)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit ir brīvi lietojams, bet nesen ir ieviesis stingrus pret nokasīšanas pasākumus, reaģējot uz datu izsalkušo LLM apmācību (vairāk par to vēlāk)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Kopiju pavairošana"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Atgriežoties pie mūsu sākotnējā jautājuma: kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi? Galvenā problēma šeit ir tā, ka mūsu kolekcija ir <a %(torrents_stats)s>strauji augusi</a>, nokasot un atverot dažas masīvas kolekcijas (papildus jau paveiktajam brīnišķīgajam darbam, ko veic citas atvērtu datu ēnu bibliotēkas, piemēram, Sci-Hub un Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Šī datu pieaugšana apgrūtina kolekciju spoguļošanu visā pasaulē. Datu uzglabāšana ir dārga! Bet mēs esam optimistiski, īpaši novērojot šādas trīs tendences."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Mēs esam noplūkuši viegli pieejamos augļus"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Tas tieši izriet no mūsu iepriekš apspriestajām prioritātēm. Mēs dodam priekšroku strādāt pie lielu kolekciju atbrīvošanas vispirms. Tagad, kad esam nodrošinājuši dažas no lielākajām kolekcijām pasaulē, mēs sagaidām, ka mūsu pieaugums būs daudz lēnāks."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Joprojām ir garš mazāku kolekciju saraksts, un katru dienu tiek skenētas vai publicētas jaunas grāmatas, bet temps, visticamāk, būs daudz lēnāks. Mēs varētu vēl dubultoties vai pat trīskāršoties, bet ilgākā laika periodā."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Uzglabāšanas izmaksas turpina eksponenciāli samazināties"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Rakstīšanas brīdī <a %(diskprices)s>diska cenas</a> par TB ir ap $12 jauniem diskiem, $8 lietotiem diskiem un $4 lentēm. Ja mēs esam konservatīvi un skatāmies tikai uz jauniem diskiem, tas nozīmē, ka petabaita uzglabāšana maksā ap $12,000. Ja pieņemam, ka mūsu bibliotēka trīskāršosies no 900TB līdz 2.7PB, tas nozīmētu $32,400, lai spoguļotu visu mūsu bibliotēku. Pievienojot elektrību, citu aparatūras izmaksas un tā tālāk, noapaļosim to līdz $40,000. Vai ar lentēm vairāk kā $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "No vienas puses <strong>$15,000–$40,000 par visu cilvēces zināšanu summu ir izdevīgi</strong>. No otras puses, tas ir nedaudz dārgi, lai sagaidītu daudz pilnu kopiju, īpaši, ja mēs arī vēlētos, lai šie cilvēki turpina sēt savus torrentus citu labā."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Tas ir šodien. Bet progress virzās uz priekšu:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Cietā diska izmaksas par TB ir aptuveni samazinājušās par trešdaļu pēdējo 10 gadu laikā, un, visticamāk, turpinās samazināties līdzīgā tempā. Lente šķietami ir uz līdzīgas trajektorijas. SSD cenas krītas vēl ātrāk, un varētu pārņemt HDD cenas līdz desmitgades beigām."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD cenu tendences no dažādiem avotiem (noklikšķiniet, lai skatītu pētījumu)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Ja tas turpināsies, tad pēc 10 gadiem mēs varētu skatīties uz tikai $5,000–$13,000, lai spoguļotu visu mūsu kolekciju (1/3), vai pat mazāk, ja mēs pieaugam mazāk apjomā. Lai gan tas joprojām ir daudz naudas, tas būs pieejams daudziem cilvēkiem. Un tas varētu būt vēl labāk, pateicoties nākamajam punktam…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Informācijas blīvuma uzlabojumi"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Pašlaik mēs glabājam grāmatas to sākotnējos formātos, kādos tās mums tiek nodotas. Protams, tās ir saspiestas, bet bieži vien tās joprojām ir lieli skenējumi vai lapu fotogrāfijas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Līdz šim vienīgās iespējas samazināt mūsu kolekcijas kopējo apjomu bija agresīvāka saspiešana vai dublēšanās novēršana. Tomēr, lai iegūtu pietiekami lielus ietaupījumus, abas metodes ir pārāk zaudējošas mūsu gaumei. Smaga fotogrāfiju saspiešana var padarīt tekstu gandrīz nelasāmu. Un dublēšanās novēršanai ir nepieciešama augsta pārliecība, ka grāmatas ir pilnīgi vienādas, kas bieži vien ir pārāk neprecīzi, īpaši, ja saturs ir vienāds, bet skenējumi veikti dažādos laikos."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Vienmēr ir bijusi trešā iespēja, bet tās kvalitāte bija tik slikta, ka mēs to nekad neapsvērām: <strong>OCR jeb optiskā rakstzīmju atpazīšana</strong>. Tas ir process, kurā fotogrāfijas tiek pārvērstas vienkāršā tekstā, izmantojot mākslīgo intelektu, lai atpazītu fotogrāfijās esošās rakstzīmes. Šādi rīki jau sen pastāv un ir bijuši diezgan labi, bet \"diezgan labi\" nav pietiekami saglabāšanas nolūkiem."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tomēr nesenie multimodālie dziļās mācīšanās modeļi ir guvuši ārkārtīgi strauju progresu, lai gan joprojām ar augstām izmaksām. Mēs sagaidām, ka gan precizitāte, gan izmaksas ievērojami uzlabosies nākamajos gados, līdz brīdim, kad tas kļūs reāli piemērojams visai mūsu bibliotēkai."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR uzlabojumi."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Kad tas notiks, mēs, iespējams, joprojām saglabāsim oriģinālos failus, bet papildus tam mēs varētu izveidot daudz mazāku mūsu bibliotēkas versiju, kuru lielākā daļa cilvēku vēlēsies spoguļot. Galvenais ir tas, ka neapstrādāts teksts pats par sevi saspiežas vēl labāk un ir daudz vieglāk dublēt, dodot mums vēl vairāk ietaupījumu."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Kopumā nav nereāli sagaidīt vismaz 5-10 reizes lielāku failu izmēra samazinājumu, varbūt pat vairāk. Pat ar konservatīvu 5 reizes samazinājumu, mēs skatītos uz <strong>1 000–3 000 $ 10 gadu laikā, pat ja mūsu bibliotēka trīskāršotos</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritiskais logs"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Ja šīs prognozes ir precīzas, mums <strong>vienkārši jāgaida pāris gadi</strong>, līdz mūsu visa kolekcija tiks plaši spoguļota. Tādējādi, Tomasa Džefersona vārdiem sakot, \"novietota ārpus negadījuma sasniedzamības.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Diemžēl LLM parādīšanās un to datu izsalkušā apmācība ir likusi daudziem autortiesību īpašniekiem aizsargāties. Vēl vairāk nekā viņi jau bija. Daudzas vietnes padara grūtāku datu nokasīšanu un arhivēšanu, tiesas prāvas ir izplatītas, un tajā pašā laikā fiziskās bibliotēkas un arhīvi turpina tikt atstāti novārtā."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Mēs varam tikai sagaidīt, ka šīs tendences turpinās pasliktināties, un daudzi darbi tiks zaudēti ilgi pirms tie nonāk publiskajā domēnā."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Mēs esam uz saglabāšanas revolūcijas sliekšņa, bet <q>zaudēto nevar atgūt.</q></strong> Mums ir kritisks logs apmēram 5-10 gadi, kuru laikā joprojām ir diezgan dārgi darbināt ēnu bibliotēku un izveidot daudzus spoguļus visā pasaulē, un kura laikā piekļuve vēl nav pilnībā slēgta."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Ja mēs varam pārvarēt šo logu, tad mēs patiešām būsim saglabājuši cilvēces zināšanas un kultūru uz mūžiem. Mums nevajadzētu ļaut šim laikam tikt izšķiestam. Mums nevajadzētu ļaut šim kritiskajam logam aizvērties."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Aiziet."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ekskluzīva piekļuve LLM uzņēmumiem pasaulē lielākajai ķīniešu zinātniskās literatūras kolekcijai"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Ķīniešu versija 中文版</a>, <a %(news_ycombinator)s>Diskutēt Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Īsumā:</strong> Annas Arhīvs ieguva unikālu 7,5 miljonu / 350TB ķīniešu zinātniskās literatūras grāmatu kolekciju — lielāku nekā Library Genesis. Mēs esam gatavi dot LLM uzņēmumam ekskluzīvu piekļuvi apmaiņā pret augstas kvalitātes OCR un teksta izvilkšanu.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Šis ir īss emuāra ieraksts. Mēs meklējam kādu uzņēmumu vai iestādi, kas palīdzētu mums ar OCR un teksta izvilkšanu masveida kolekcijai, kuru mēs ieguvām, apmaiņā pret ekskluzīvu agrīnu piekļuvi. Pēc embargo perioda mēs, protams, izlaidīsim visu kolekciju."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Augstas kvalitātes akadēmiskais teksts ir ārkārtīgi noderīgs LLM apmācībai. Lai gan mūsu kolekcija ir ķīniešu valodā, tā var būt noderīga arī angļu LLM apmācībai: modeļi, šķiet, kodē koncepcijas un zināšanas neatkarīgi no avota valodas."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Lai to izdarītu, teksts ir jāizvelk no skenējumiem. Ko no tā iegūst Annas Arhīvs? Pilnteksta meklēšanu grāmatās saviem lietotājiem."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Tā kā mūsu mērķi saskan ar LLM izstrādātāju mērķiem, mēs meklējam sadarbības partneri. Mēs esam gatavi dot jums <strong>ekskluzīvu agrīnu piekļuvi šai kolekcijai lielapjomā uz 1 gadu</strong>, ja jūs varat veikt pareizu OCR un teksta izvilkšanu. Ja esat gatavs dalīties ar visu sava procesa kodu ar mums, mēs būtu gatavi pagarināt kolekcijas embargo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Piemēra lapas"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Lai pierādītu mums, ka jums ir laba sistēma, šeit ir dažas piemēra lapas, ar kurām sākt, no grāmatas par supravadītājiem. Jūsu sistēmai vajadzētu pareizi apstrādāt matemātiku, tabulas, diagrammas, piezīmes un tā tālāk."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Nosūtiet savas apstrādātās lapas uz mūsu e-pastu. Ja tās izskatīsies labi, mēs jums nosūtīsim vairāk privāti, un mēs sagaidām, ka jūs varēsiet ātri palaist savu sistēmu arī uz tām. Kad būsim apmierināti, mēs varam noslēgt darījumu."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Kolekcija"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Daži papildu dati par kolekciju. <a %(duxiu)s>Duxiu</a> ir milzīga skenēto grāmatu datubāze, ko izveidojusi <a %(chaoxing)s>SuperStar Digital Library Group</a>. Lielākā daļa ir akadēmiskās grāmatas, kas skenētas, lai tās būtu pieejamas digitāli universitātēm un bibliotēkām. Mūsu angliski runājošajai auditorijai <a %(library_princeton)s>Princeton</a> un <a %(guides_lib_uw)s>Vašingtonas Universitāte</a> piedāvā labus pārskatus. Ir arī lielisks raksts, kas sniedz vairāk informācijas: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (meklējiet to Annas Arhīvā)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Duxiu grāmatas jau ilgi ir pirātētas Ķīnas internetā. Parasti tās tiek pārdotas par mazāk nekā dolāru no tālākpārdevējiem. Tās parasti tiek izplatītas, izmantojot Ķīnas ekvivalentu Google Drive, kas bieži ir uzlauzts, lai nodrošinātu vairāk vietas. Dažas tehniskās detaļas var atrast <a %(github_duty_machine)s>šeit</a> un <a %(github_821_github_io)s>šeit</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Lai gan grāmatas ir daļēji publiski izplatītas, tās ir diezgan grūti iegūt lielapjomā. Mums tas bija augstu mūsu TODO sarakstā, un mēs tam piešķīrām vairākus mēnešus pilna laika darba. Tomēr nesen pie mums vērsās neticams, apbrīnojams un talantīgs brīvprātīgais, kurš mums pastāstīja, ka viņš jau ir paveicis visu šo darbu — par lielām izmaksām. Viņi dalījās ar mums pilnā kolekcijā, negaidot neko pretī, izņemot ilgtermiņa saglabāšanas garantiju. Patiesi ievērojami. Viņi piekrita lūgt palīdzību šādā veidā, lai kolekcija tiktu OCR'ēta."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Kolekcijā ir 7,543,702 faili. Tas ir vairāk nekā Library Genesis daiļliteratūra (apmēram 5,3 miljoni). Kopējais failu izmērs ir aptuveni 359TB (326TiB) pašreizējā formā."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Mēs esam atvērti citiem priekšlikumiem un idejām. Vienkārši sazinieties ar mums. Apskatiet Annas Arhīvu, lai iegūtu vairāk informācijas par mūsu kolekcijām, saglabāšanas centieniem un to, kā jūs varat palīdzēt. Paldies!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Brīdinājums: šis emuāra ieraksts ir novecojis. Mēs esam nolēmuši, ka IPFS vēl nav gatavs plašai lietošanai. Mēs joprojām saistīsimies ar failiem IPFS no Annas Arhīva, kad tas būs iespējams, bet mēs tos vairs neuzturēsim paši, un mēs neiesakām citiem spoguļot, izmantojot IPFS. Lūdzu, skatiet mūsu Torrents lapu, ja vēlaties palīdzēt saglabāt mūsu kolekciju."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Palīdziet sēt Z-Bibliotēku IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Kā vadīt ēnu bibliotēku: darbības Annas Arhīvā"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nav <q>AWS ēnu labdarībām,</q> tāpēc kā mēs vadām Annas Arhīvu?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Es vadu <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, pasaulē lielāko atvērtā koda bezpeļņas meklētājprogrammu <a %(wikipedia_shadow_library)s>ēnu bibliotēkām</a>, piemēram, Sci-Hub, Library Genesis un Z-Bibliotēku. Mūsu mērķis ir padarīt zināšanas un kultūru viegli pieejamu un galu galā izveidot kopienu, kas kopā arhivē un saglabā <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>visas pasaules grāmatas</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Šajā rakstā es parādīšu, kā mēs vadām šo vietni, un unikālos izaicinājumus, kas rodas, vadot vietni ar apšaubāmu juridisko statusu, jo nav “AWS ēnu labdarībām”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Apskatiet arī saistīto rakstu <a %(blog_how_to_become_a_pirate_archivist)s>Kā kļūt par pirātu arhivāru</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Inovāciju žetoni"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Sāksim ar mūsu tehnoloģiju kaudzi. Tā ir apzināti garlaicīga. Mēs izmantojam Flask, MariaDB un ElasticSearch. Tas ir burtiski viss. Meklēšana lielā mērā ir atrisināta problēma, un mēs neplānojam to izgudrot no jauna. Turklāt mums ir jāiztērē mūsu <a %(mcfunley)s>inovāciju žetoni</a> kaut kam citam: lai mūs neapturētu iestādes."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Cik legāla vai nelegāla ir Annas Arhīvs? Tas lielākoties ir atkarīgs no juridiskās jurisdikcijas. Lielākā daļa valstu tic kādai autortiesību formai, kas nozīmē, ka cilvēkiem vai uzņēmumiem tiek piešķirta ekskluzīva monopola tiesības uz noteiktiem darbiem uz noteiktu laiku. Starp citu, Annas Arhīvā mēs uzskatām, ka, lai gan ir dažas priekšrocības, kopumā autortiesības ir negatīvas sabiedrībai — bet tas ir stāsts citai reizei."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Šis ekskluzīvais monopols uz noteiktiem darbiem nozīmē, ka ir nelegāli ikvienam ārpus šī monopola tieši izplatīt šos darbus — arī mums. Bet Annas Arhīvs ir meklētājprogramma, kas tieši neizplata šos darbus (vismaz ne mūsu tīmekļa vietnē), tāpēc mums vajadzētu būt kārtībā, vai ne? Ne gluži. Daudzās jurisdikcijās ir ne tikai nelegāli izplatīt aizsargātus darbus, bet arī saistīt uz vietām, kas to dara. Klasisks piemērs tam ir Amerikas Savienoto Valstu DMCA likums."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Tas ir spektra stingrākais gals. Spektra otrā galā teorētiski varētu būt valstis, kurās vispār nav autortiesību likumu, bet tādas īsti nepastāv. Gandrīz katrai valstij ir kāda autortiesību likuma forma. Izpilde ir cits stāsts. Ir daudz valstu, kuru valdības nevēlas izpildīt autortiesību likumus. Ir arī valstis starp abiem galējībām, kas aizliedz izplatīt aizsargātus darbus, bet neaizliedz saistīt uz šādiem darbiem."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Vēl viens apsvērums ir uzņēmuma līmenī. Ja uzņēmums darbojas jurisdikcijā, kas nerūpējas par autortiesībām, bet pats uzņēmums nevēlas uzņemties nekādu risku, tad viņi varētu slēgt jūsu vietni, tiklīdz kāds par to sūdzas."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Visbeidzot, liels apsvērums ir maksājumi. Tā kā mums ir jāpaliek anonīmiem, mēs nevaram izmantot tradicionālās maksājumu metodes. Tas atstāj mums kriptovalūtas, un tikai neliels uzņēmumu skaits tās atbalsta (ir virtuālās debetkartes, kas tiek apmaksātas ar kripto, bet tās bieži netiek pieņemtas)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistēmas arhitektūra"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Tātad, pieņemsim, ka jūs atradāt dažus uzņēmumus, kas ir gatavi mitināt jūsu vietni, neslēdzot to — sauksim tos par “brīvību mīlošiem nodrošinātājiem” 😄. Jūs ātri atklāsiet, ka viss mitināšana pie viņiem ir diezgan dārga, tāpēc jūs varētu vēlēties atrast dažus “lētos nodrošinātājus” un faktisko mitināšanu veikt tur, izmantojot starpniekus caur brīvību mīlošiem nodrošinātājiem. Ja to izdarīsiet pareizi, lētie nodrošinātāji nekad nezinās, ko jūs mitināt, un nekad nesaņems sūdzības."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Ar visiem šiem nodrošinātājiem pastāv risks, ka viņi jūs tomēr slēgs, tāpēc jums ir nepieciešama arī redundance. Mums tas ir nepieciešams visos mūsu kaudzes līmeņos."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Viena nedaudz brīvību mīloša kompānija, kas ir nostādījusi sevi interesantā pozīcijā, ir Cloudflare. Viņi ir <a %(blog_cloudflare)s>apgalvojuši</a>, ka viņi nav mitināšanas nodrošinātājs, bet gan komunālais pakalpojums, kā ISP. Tāpēc viņi nav pakļauti DMCA vai citiem izņemšanas pieprasījumiem un pārsūta jebkādus pieprasījumus jūsu faktiskajam mitināšanas nodrošinātājam. Viņi ir gājuši tik tālu, ka devušies uz tiesu, lai aizsargātu šo struktūru. Tāpēc mēs varam tos izmantot kā vēl vienu kešatmiņas un aizsardzības slāni."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nepieņem anonīmus maksājumus, tāpēc mēs varam izmantot tikai viņu bezmaksas plānu. Tas nozīmē, ka mēs nevaram izmantot viņu slodzes balansēšanas vai pārslēgšanās funkcijas. Tāpēc mēs <a %(annas_archive_l255)s>īstenojām to paši</a> domēna līmenī. Lapas ielādes laikā pārlūks pārbaudīs, vai pašreizējais domēns joprojām ir pieejams, un, ja nē, tas pārraksta visas URL uz citu domēnu. Tā kā Cloudflare kešatmiņā saglabā daudzas lapas, tas nozīmē, ka lietotājs var nonākt mūsu galvenajā domēnā, pat ja starpniekserveris ir izslēgts, un pēc tam nākamajā klikšķī tikt pārvietots uz citu domēnu."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Mums joprojām ir arī parastās darbības problēmas, ar kurām jātiek galā, piemēram, servera veselības uzraudzība, aizmugures un priekšpuses kļūdu reģistrēšana un tā tālāk. Mūsu pārslēgšanās arhitektūra ļauj lielāku robustumu arī šajā frontē, piemēram, darbinot pilnīgi atšķirīgu serveru komplektu vienā no domēniem. Mēs pat varam darbināt vecākas koda un datu kopu versijas šajā atsevišķajā domēnā, ja galvenajā versijā nepamanīts kritisks kļūdas."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Mēs varam arī nodrošināties pret Cloudflare vēršanos pret mums, noņemot to no viena no domēniem, piemēram, šī atsevišķā domēna. Iespējamas dažādas šo ideju permutācijas."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Rīki"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Apskatīsim, kādus rīkus mēs izmantojam, lai to visu paveiktu. Tas ļoti mainās, kad saskaramies ar jaunām problēmām un atrodam jaunus risinājumus."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Lietojumprogrammu serveris: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Starpniekserveris: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serveru pārvaldība: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Izstrāde: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Sīpola statiskā mitināšana: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ir dažas lēmumu jomas, kurās esam šaubījušies. Viens no tiem ir komunikācija starp serveriem: agrāk izmantojām Wireguard, bet atklājām, ka tas dažkārt pārtrauc datu pārraidi vai pārraida datus tikai vienā virzienā. Tas notika ar vairākiem dažādiem Wireguard iestatījumiem, kurus izmēģinājām, piemēram, <a %(github_costela_wesher)s>wesher</a> un <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Mēģinājām arī tunelēt portus caur SSH, izmantojot autossh un sshuttle, bet saskārāmies ar <a %(github_sshuttle)s>problēmām tur</a> (lai gan man joprojām nav skaidrs, vai autossh cieš no TCP-over-TCP problēmām vai nē — man tas šķiet kā neveikls risinājums, bet varbūt tas patiesībā ir labi?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Tā vietā mēs atgriezāmies pie tiešiem savienojumiem starp serveriem, slēpjot, ka serveris darbojas uz lētiem pakalpojumu sniedzējiem, izmantojot IP filtrēšanu ar UFW. Tam ir trūkums, ka Docker nedarbojas labi ar UFW, ja vien neizmantojat <code>network_mode: \"host\"</code>. Tas viss ir nedaudz vairāk pakļauts kļūdām, jo ar nelielu nepareizu konfigurāciju jūs varat pakļaut savu serveri internetam. Varbūt mums vajadzētu atgriezties pie autossh — atsauksmes šeit būtu ļoti noderīgas."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Mēs arī esam šaubījušies starp Varnish un Nginx. Pašlaik mums patīk Varnish, bet tam ir savas īpatnības un raupjās malas. Tas pats attiecas uz Checkmk: mēs to nemīlam, bet tas pagaidām darbojas. Weblate ir bijis pieņemams, bet ne izcils — dažkārt baidos, ka tas zaudēs manus datus, kad mēģinu to sinhronizēt ar mūsu git repo. Flask kopumā ir bijis labs, bet tam ir dažas dīvainas īpatnības, kas prasījušas daudz laika, lai tās novērstu, piemēram, pielāgotu domēnu konfigurēšana vai problēmas ar tā SqlAlchemy integrāciju."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Līdz šim pārējie rīki ir bijuši lieliski: mums nav nopietnu sūdzību par MariaDB, ElasticSearch, Gitlab, Zulip, Docker un Tor. Visiem šiem ir bijušas dažas problēmas, bet nekas pārāk nopietns vai laikietilpīgs."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Secinājums"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Ir bijusi interesanta pieredze mācīties, kā izveidot izturīgu un noturīgu ēnu bibliotēkas meklētājprogrammu. Ir daudz vairāk detaļu, ko dalīties nākamajos ierakstos, tāpēc ļaujiet man zināt, par ko jūs vēlētos uzzināt vairāk!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Kā vienmēr, mēs meklējam ziedojumus, lai atbalstītu šo darbu, tāpēc noteikti apskatiet Ziedojumu lapu Annas Arhīvā. Mēs arī meklējam citus atbalsta veidus, piemēram, dotācijas, ilgtermiņa sponsorus, augsta riska maksājumu nodrošinātājus, varbūt pat (gaumīgas!) reklāmas. Un, ja vēlaties ieguldīt savu laiku un prasmes, mēs vienmēr meklējam izstrādātājus, tulkotājus un tā tālāk. Paldies par jūsu interesi un atbalstu."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Sveiki, es esmu Anna. Es izveidoju <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, pasaulē lielāko ēnu bibliotēku. Šis ir mans personīgais blogs, kurā es un mani komandas biedri rakstām par pirātismu, digitālo saglabāšanu un daudz ko citu."

#, fuzzy
msgid "blog.index.text2"
msgstr "Sazinieties ar mani <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Ņemiet vērā, ka šī vietne ir tikai blogs. Mēs šeit mitinām tikai savus vārdus. Šeit netiek mitināti vai saistīti ne torrenti, ne citi ar autortiesībām aizsargāti faili."

#, fuzzy
msgid "blog.index.heading"
msgstr "Bloga ieraksti"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat nokasīšana"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5,998,794 grāmatu ievietošana IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Brīdinājums: šis emuāra ieraksts ir novecojis. Mēs esam nolēmuši, ka IPFS vēl nav gatavs plašai lietošanai. Mēs joprojām saistīsimies ar failiem IPFS no Annas Arhīva, kad tas būs iespējams, bet mēs tos vairs neuzturēsim paši, un mēs neiesakām citiem spoguļot, izmantojot IPFS. Lūdzu, skatiet mūsu Torrents lapu, ja vēlaties palīdzēt saglabāt mūsu kolekciju."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Annas Arhīvs nokasīja visu WorldCat (pasaulē lielāko bibliotēkas metadata kolekciju), lai izveidotu TODO sarakstu ar grāmatām, kuras nepieciešams saglabāt.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Pirms gada mēs <a %(blog)s>uzsākām</a> atbildēt uz šo jautājumu: <strong>Kāds procents grāmatu ir pastāvīgi saglabāts ēnu bibliotēkās?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Kad grāmata nonāk atvērtā datu ēnu bibliotēkā, piemēram, <a %(wikipedia_library_genesis)s>Library Genesis</a>, un tagad <a %(wikipedia_annas_archive)s>Annas Arhīvā</a>, tā tiek atspoguļota visā pasaulē (caur torrentiem), tādējādi praktiski saglabājot to uz visiem laikiem."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Lai atbildētu uz jautājumu, kāds procents grāmatu ir saglabāts, mums jāzina saucējs: cik grāmatu kopumā pastāv? Un ideālā gadījumā mums nav tikai skaitlis, bet arī faktiska metadata. Tad mēs varam ne tikai salīdzināt tās ar ēnu bibliotēkām, bet arī <strong>izveidot TODO sarakstu ar atlikušajām grāmatām, kuras jāsaglabā!</strong> Mēs pat varētu sākt sapņot par pūļa veidotu centienu iziet cauri šim TODO sarakstam."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Mēs nokasījām <a %(wikipedia_isbndb_com)s>ISBNdb</a> un lejupielādējām <a %(openlibrary)s>Open Library datu kopu</a>, taču rezultāti bija neapmierinoši. Galvenā problēma bija tā, ka ISBN pārklāšanās nebija liela. Skatiet šo Venn diagrammu no <a %(blog)s>mūsu emuāra ieraksta</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Mēs bijām ļoti pārsteigti par to, cik maz bija pārklāšanās starp ISBNdb un Open Library, kas abi plaši iekļauj datus no dažādiem avotiem, piemēram, tīmekļa nokasījumiem un bibliotēku ierakstiem. Ja abi labi veic darbu, atrodot lielāko daļu ISBN, to apļiem noteikti būtu jābūt ievērojamai pārklāšanās, vai arī vienam būtu jābūt otra apakškopai. Tas lika mums aizdomāties, cik daudz grāmatu atrodas <em>pilnīgi ārpus šiem apļiem</em>? Mums ir nepieciešama lielāka datubāze."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Tad mēs pievērsāmies pasaulē lielākajai grāmatu datubāzei: <a %(wikipedia_worldcat)s>WorldCat</a>. Tā ir patentēta datubāze, ko pārvalda bezpeļņas organizācija <a %(wikipedia_oclc)s>OCLC</a>, kas apkopo metadata ierakstus no bibliotēkām visā pasaulē, apmaiņā pret to, ka šīs bibliotēkas saņem piekļuvi pilnai datu kopai un tiek parādītas gala lietotāju meklēšanas rezultātos."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Lai gan OCLC ir bezpeļņas organizācija, viņu biznesa modelis prasa aizsargāt savu datubāzi. Nu, mēs atvainojamies, draugi OCLC, mēs to visu atdodam. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Pēdējā gada laikā mēs rūpīgi nokasījām visus WorldCat ierakstus. Sākumā mums paveicās. WorldCat tieši ieviesa pilnīgu vietnes pārveidi (2022. gada augustā). Tas ietvēra būtisku viņu aizmugures sistēmu pārveidi, ieviešot daudzas drošības nepilnības. Mēs nekavējoties izmantojām šo iespēju un spējām nokasīt simtiem miljonu (!) ierakstu tikai dažās dienās."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat pārveide</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Pēc tam drošības nepilnības tika lēnām labotas viena pēc otras, līdz pēdējā, ko atradām, tika aizlāpīta apmēram pirms mēneša. Līdz tam laikam mums bija gandrīz visi ieraksti, un mēs tikai centāmies iegūt nedaudz augstākas kvalitātes ierakstus. Tāpēc mēs jutām, ka ir laiks izlaist!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Apskatīsim dažus pamatdatus par datiem:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formāts?</strong> <a %(blog)s>Annas Arhīva Konteineri (AAC)</a>, kas būtībā ir <a %(jsonlines)s>JSON Lines</a>, saspiesti ar <a %(zstd)s>Zstandard</a>, plus daži standartizēti semantikas. Šie konteineri aptver dažāda veida ierakstus, pamatojoties uz dažādiem nokasījumiem, ko mēs veicām."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dati"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Radās nezināma kļūda. Lūdzu, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Šai monētai ir augstāks minimālais limits nekā parasti. Lūdzu, izvēlieties citu ilgumu vai citu monētu."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Pieprasījumu nevarēja izpildīt. Lūdzu, mēģiniet vēlreiz pēc dažām minūtēm, un, ja problēma turpinās, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Maksājumu apstrādes kļūda. Lūdzu, uzgaidiet mirkli un mēģiniet vēlreiz. Ja problēma saglabājas vairāk nekā 24 stundas, lūdzu, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "slēpts komentārs"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Faila problēma: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Labāka versija"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Vai vēlaties ziņot par šo lietotāju par ļaunprātīgu vai neatbilstošu uzvedību?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Ziņot par ļaunprātību"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Ziņots par ļaunprātību:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Jūs ziņojāt par šo lietotāju par ļaunprātību."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Atbildēt"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Lūdzu, <a %(a_login)s>piesakieties</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Jūs atstājāt komentāru. Tas var aizņemt minūti, lai tas parādītos."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s ietekmētās lapas"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nav redzams Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nav redzams Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nav redzams Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Atzīmēts kā bojāts Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Trūkst no Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Atzīmēts kā “spam” Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Atzīmēts kā “slikts fails” Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Ne visas lapas varēja konvertēt uz PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Exiftool palaišana neizdevās šajā failā"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Grāmata (nezināma)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Grāmata (dokumentālā)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Grāmata (daiļliteratūra)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Žurnāla raksts"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standartu dokuments"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Žurnāls"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komiksu grāmata"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Mūzikas partitūra"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiogrāmata"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Cits"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Partneru servera lejupielāde"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Ārējā lejupielāde"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Ārējā aizņemšanās"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Ārējā aizņemšanās (drukas ierobežojums)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Izpētīt metadatus"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Iekļauts torrentos"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Nederīgs pieprasījums. Apmeklējiet [X23X]."

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Augšupielādes uz AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost e-grāmatu indekss"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Čehu metadati"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google grāmatas"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Krievijas Valsts bibliotēka"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Nosaukums"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Autors"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Izdevējs"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Izdevums"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Publicēšanas gads"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Oriģinālais faila nosaukums"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Apraksts un metadatu komentāri"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partneru servera lejupielādes uz laiku nav pieejamas šim failam."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Ātrais partneru serveris #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(ieteicams)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(nav pārlūkprogrammas verifikācijas vai gaidīšanas sarakstu)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Lēnais partneru serveris #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(nedaudz ātrāks, bet ar gaidīšanas sarakstu)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(nav gaidīšanas saraksta, bet var būt ļoti lēns)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Ne-fantastika"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fantastika"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(arī noklikšķiniet uz “GET” augšpusē)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(noklikšķiniet uz “GET” augšpusē)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "viņu reklāmās var būt ļaunprātīga programmatūra, tāpēc izmantojiet reklāmu bloķētāju vai neklikšķiniet uz reklāmām"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC faili var būt neuzticami lejupielādei)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Bibliotēka Tor tīklā"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(prasa Tor pārlūku)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Aizņemties no Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(tikai drukas ierobežojumu apmeklētājiem)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(saistītais DOI var nebūt pieejams Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "kolekcija"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrents"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Masveida torrentu lejupielādes"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(tikai ekspertiem)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Meklēt Annas Arhīvā pēc ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Meklēt dažādās citās datubāzēs pēc ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Atrast oriģinālo ierakstu ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Meklēt Annas Arhīvā pēc Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Atrast oriģinālo ierakstu Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Meklēt Annas Arhīvā pēc OCLC (WorldCat) numura"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Atrast oriģinālo ierakstu WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Meklēt Annas Arhīvā pēc DuXiu SSID numura"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Meklēt manuāli DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Meklēt Annas Arhīvā pēc CADAL SSNO numura"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Atrast oriģinālo ierakstu CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Meklēt Annas Arhīvā pēc DuXiu DXID numura"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost e-grāmatu indekss"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Annas Arhīvs 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(nav nepieciešama pārlūkprogrammas verifikācija)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Čehu metadati %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Grāmatas %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadati"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "apraksts"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternatīvais faila nosaukums"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternatīvais nosaukums"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternatīvais autors"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternatīvais izdevējs"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternatīvais izdevums"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternatīvais paplašinājums"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadatu komentāri"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternatīvais apraksts"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "atvēršanas datums"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub fails “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Kontrolētās Digitālās Aizņemšanās fails “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Šis ir ieraksts no Internet Archive, nevis tieši lejupielādējams fails. Jūs varat mēģināt aizņemties grāmatu (saite zemāk), vai izmantot šo URL, kad <a %(a_request)s>pieprasāt failu</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Ja jums ir šis fails un tas vēl nav pieejams Annas Arhīvā, apsveriet iespēju <a %(a_request)s>augšupielādēt to</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) numurs %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadatu ieraksts"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Šis ir metadatu ieraksts, nevis lejupielādējams fails. Jūs varat izmantot šo URL, kad <a %(a_request)s>pieprasāt failu</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadati no saistītā ieraksta"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Uzlabot metadatus Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Brīdinājums: vairāki saistītie ieraksti:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Uzlabot metadatus"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Ziņot par faila kvalitāti"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Lejupielādes laiks"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Vietne:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Meklēt Annas Arhīvā “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Kodu Pētnieks:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Skatīt Kodu Pētniekā “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lasīt vairāk…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Lejupielādes (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Aizņemties (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Izpētīt metadatus (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentāri (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Saraksti (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistika (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Tehniskās detaļas"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Šim failam var būt problēmas, un tas ir paslēpts no avota bibliotēkas.</span> Dažreiz tas notiek pēc autortiesību īpašnieka pieprasījuma, dažreiz tāpēc, ka ir pieejama labāka alternatīva, bet dažreiz tas ir saistīts ar pašu failu. Iespējams, ka to joprojām var lejupielādēt, bet mēs iesakām vispirms meklēt alternatīvu failu. Vairāk informācijas:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Labāka šī faila versija var būt pieejama vietnē %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Ja jūs joprojām vēlaties lejupielādēt šo failu, pārliecinieties, ka izmantojat tikai uzticamu, atjauninātu programmatūru, lai to atvērtu."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Ātra lejupielāde"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Ātras lejupielādes</strong> Kļūstiet par <a %(a_membership)s>biedru</a>, lai atbalstītu grāmatu, rakstu un citu materiālu ilgtermiņa saglabāšanu. Lai izrādītu pateicību par jūsu atbalstu, jūs saņemat ātras lejupielādes. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Ja šomēnes ziedosiet, jūs saņemsiet <strong>dubultu</strong> ātro lejupielāžu skaitu."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Ātras lejupielādes</strong> Jums šodien ir atlikušas %(remaining)s. Paldies, ka esat biedrs! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Ātras lejupielādes</strong> Jūs esat izsmēlis ātro lejupielāžu limitu šodienai."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Ātras lejupielādes</strong> Jūs nesen lejupielādējāt šo failu. Saites paliek derīgas kādu laiku."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opcija #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nav bez novirzīšanas)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(atvērt skatītājā)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Iesakiet draugu, un gan jūs, gan jūsu draugs saņems %(percentage)s%% bonusa ātrās lejupielādes!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Uzzināt vairāk…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Lēnas lejupielādes"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "No uzticamiem partneriem."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Vairāk informācijas <a %(a_slow)s>BUJ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(var būt nepieciešama <a %(a_browser)s>pārlūkprogrammas verifikācija</a> — neierobežotas lejupielādes!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Pēc lejupielādes:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Atvērt mūsu skatītājā"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "rādīt ārējās lejupielādes"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Ārējas lejupielādes"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nav atrastas lejupielādes."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Visas lejupielādes iespējas satur to pašu failu un būtu drošas lietošanai. Tomēr vienmēr esiet piesardzīgi, lejupielādējot failus no interneta, īpaši no vietnēm ārpus Annas Arhīva. Piemēram, pārliecinieties, ka jūsu ierīces ir atjauninātas."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Lieliem failiem mēs iesakām izmantot lejupielādes pārvaldnieku, lai novērstu pārtraukumus."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Ieteicamie lejupielādes pārvaldnieki: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Lai atvērtu failu, atkarībā no faila formāta, jums būs nepieciešams e-grāmatu vai PDF lasītājs."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Ieteicamie e-grāmatu lasītāji: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Annas Arhīva tiešsaistes skatītājs"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Izmantojiet tiešsaistes rīkus, lai konvertētu starp formātiem."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Ieteicamie konvertēšanas rīki: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Jūs varat nosūtīt gan PDF, gan EPUB failus uz savu Kindle vai Kobo e-lasītāju."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ieteicamie rīki: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Atbalstiet autorus un bibliotēkas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Ja jums tas patīk un varat to atļauties, apsveriet iespēju iegādāties oriģinālu vai atbalstīt autorus tieši."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Ja tas ir pieejams jūsu vietējā bibliotēkā, apsveriet iespēju to aizņemties tur bez maksas."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Faila kvalitāte"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Palīdziet kopienai, ziņojot par šī faila kvalitāti! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Ziņot par faila problēmu (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Lieliska faila kvalitāte (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Pievienot komentāru (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Kas ir nepareizi ar šo failu?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Lūdzu, izmantojiet <a %(a_copyright)s>DMCA / Autortiesību prasības veidlapu</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Aprakstiet problēmu (obligāti)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Problēmas apraksts"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "Labākas versijas MD5 šim failam (ja piemērojams)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Aizpildiet šo, ja ir cits fails, kas cieši atbilst šim failam (tāds pats izdevums, tāds pats faila paplašinājums, ja varat atrast), kuru cilvēkiem vajadzētu izmantot šī faila vietā. Ja zināt labāku šī faila versiju ārpus Annas Arhīva, lūdzu, <a %(a_upload)s>augšupielādējiet to</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Jūs varat iegūt md5 no URL, piemēram,"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Iesniegt ziņojumu"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Uzziniet, kā <a %(a_metadata)s>uzlabot metadatus</a> šim failam pašam."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Paldies par jūsu ziņojuma iesniegšanu. Tas tiks parādīts šajā lapā, kā arī manuāli pārskatīts Annas (līdz mums būs pienācīga moderācijas sistēma)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Ja šim failam ir lieliska kvalitāte, jūs varat apspriest visu par to šeit! Ja nē, lūdzu, izmantojiet pogu “Ziņot par faila problēmu”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Man ļoti patika šī grāmata!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Atstāt komentāru"

#, fuzzy
msgid "common.english_only"
msgstr "Teksts zemāk turpinās angļu valodā."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Kopējie lejupielādes: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“Faila MD5” ir hešs, kas tiek aprēķināts no faila satura un ir diezgan unikāls, pamatojoties uz šo saturu. Visas ēnu bibliotēkas, kuras mēs esam indeksējuši šeit, galvenokārt izmanto MD5, lai identificētu failus."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Fails var parādīties vairākās ēnu bibliotēkās. Lai iegūtu informāciju par dažādiem datu kopumiem, kurus mēs esam apkopojuši, skatiet <a %(a_datasets)s>Datu kopumu lapu</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Šis ir fails, ko pārvalda <a %(a_ia)s>IA kontrolētā digitālā aizdošana</a> bibliotēka un indeksēts Annas Arhīvā meklēšanai. Lai iegūtu informāciju par dažādiem datu kopumiem, kurus mēs esam apkopojuši, skatiet <a %(a_datasets)s>Datu kopumu lapu</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Lai iegūtu informāciju par šo konkrēto failu, skatiet tā <a %(a_href)s>JSON failu</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problēma, ielādējot šo lapu"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Lūdzu, atsvaidziniet, lai mēģinātu vēlreiz. <a %(a_contact)s>Sazinieties ar mums</a>, ja problēma saglabājas vairākas stundas."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Nav atrasts"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” mūsu datubāzē netika atrasts."

#, fuzzy
msgid "page.login.title"
msgstr "Pieteikties / Reģistrēties"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Pārlūka verifikācija"

#, fuzzy
msgid "page.login.text1"
msgstr "Lai novērstu surogātpasta robotu veidošanu daudzos kontos, mums vispirms ir jāpārbauda jūsu pārlūks."

#, fuzzy
msgid "page.login.text2"
msgstr "Ja jūs nonākat bezgalīgā cilpā, mēs iesakām instalēt <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Var arī palīdzēt izslēgt reklāmu bloķētājus un citus pārlūka paplašinājumus."

#, fuzzy
msgid "page.codes.title"
msgstr "Kodi"

#, fuzzy
msgid "page.codes.heading"
msgstr "Kodu Pētnieks"

#, fuzzy
msgid "page.codes.intro"
msgstr "Izpētiet kodus, ar kuriem ieraksti ir atzīmēti, pēc prefiksa. Kolonnā “ieraksti” ir redzams, cik ierakstu ir atzīmēti ar kodiem ar doto prefiksu, kā redzams meklētājā (ieskaitot tikai metadatu ierakstus). Kolonnā “kodi” ir redzams, cik faktisko kodu ir ar doto prefiksu."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Šīs lapas ģenerēšana var aizņemt kādu laiku, tāpēc ir nepieciešama Cloudflare captcha. <a %(a_donate)s>Biedri</a> var izlaist captchu."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Lūdzu, neskrāpējiet šīs lapas. Tā vietā mēs iesakām <a %(a_import)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes un palaist mūsu <a %(a_software)s>atvērto pirmkodu</a>. Neapstrādātie dati var tikt manuāli izpētīti caur JSON failiem, piemēram, <a %(a_json_file)s>šo</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefikss"

#, fuzzy
msgid "common.form.go"
msgstr "Iet"

#, fuzzy
msgid "common.form.reset"
msgstr "Atiestatīt"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Meklēt Annas Arhīvā"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Brīdinājums: kodā ir nepareizi Unicode rakstzīmes, un tas var darboties nepareizi dažādās situācijās. Neapstrādāto bināro var dekodēt no base64 attēlojuma URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Zināms koda prefikss “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefikss"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiķete"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Apraksts"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL konkrētam kodam"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” tiks aizstāts ar koda vērtību"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Vispārīgs URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Vietne"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] ""
msgstr[1] ""
msgstr[2] "%(count)s ieraksti, kas atbilst “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL konkrētam kodam: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Vairāk…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kodus, kas sākas ar “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indekss"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "ieraksti"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kodi"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Mazāk nekā %(count)s ieraksti"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "DMCA / autortiesību prasībām izmantojiet <a %(a_copyright)s>šo veidlapu</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Jebkuri citi veidi, kā sazināties ar mums par autortiesību prasībām, tiks automātiski dzēsti."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Mēs ļoti priecājamies par jūsu atsauksmēm un jautājumiem!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tomēr, ņemot vērā saņemto surogātpasta un bezjēdzīgo e-pastu daudzumu, lūdzu, atzīmējiet rūtiņas, lai apstiprinātu, ka saprotat šos nosacījumus, lai sazinātos ar mums."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Autortiesību prasības uz šo e-pastu tiks ignorētas; tā vietā izmantojiet veidlapu."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partneru serveri nav pieejami hostinga slēgšanas dēļ. Tie drīzumā atkal būs pieejami."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Dalības termiņi tiks attiecīgi pagarināti."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nesūtiet mums e-pastu, lai <a %(a_request)s>pieprasītu grāmatas</a><br>vai nelielus (<10k) <a %(a_upload)s>augšupielādes</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Kad uzdodat jautājumus par kontu vai ziedojumiem, pievienojiet sava konta ID, ekrānuzņēmumus, kvītis, pēc iespējas vairāk informācijas. Mēs pārbaudām savu e-pastu tikai ik pēc 1-2 nedēļām, tāpēc šīs informācijas neiekļaušana aizkavēs jebkādu risinājumu."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Rādīt e-pastu"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Autortiesību prasības veidlapa"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Ja jums ir DCMA vai cita autortiesību prasība, lūdzu, aizpildiet šo veidlapu pēc iespējas precīzāk. Ja rodas kādas problēmas, lūdzu, sazinieties ar mums, izmantojot mūsu īpašo DMCA adresi: %(email)s. Ņemiet vērā, ka prasības, kas nosūtītas uz šo adresi, netiks apstrādātas, tā ir paredzēta tikai jautājumiem. Lūdzu, izmantojiet zemāk esošo veidlapu, lai iesniegtu savas prasības."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL adreses Annas Arhīvā (obligāti). Viena uz rindas. Lūdzu, iekļaujiet tikai URL adreses, kas apraksta tieši to pašu grāmatas izdevumu. Ja vēlaties iesniegt prasību par vairākām grāmatām vai vairākiem izdevumiem, lūdzu, iesniedziet šo veidlapu vairākas reizes."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Prasības, kas apvieno vairākas grāmatas vai izdevumus, tiks noraidītas."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Jūsu vārds (obligāti)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adrese (obligāti)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefona numurs (obligāti)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-pasts (obligāti)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Skaidrs avota materiāla apraksts (obligāti)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "Avota materiāla ISBN numuri (ja piemērojams). Viena uz rindas. Lūdzu, iekļaujiet tikai tos, kas precīzi atbilst izdevumam, par kuru ziņojat autortiesību prasību."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> avota materiāla URL adreses, viena uz rindas. Lūdzu, veltiet brīdi, lai meklētu savu avota materiālu Open Library. Tas palīdzēs mums pārbaudīt jūsu prasību."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "Avota materiāla URL adreses, viena uz rindas (obligāti). Lūdzu, iekļaujiet pēc iespējas vairāk, lai palīdzētu mums pārbaudīt jūsu prasību (piemēram, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Paziņojums un paraksts (obligāti)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Iesniegt prasību"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Paldies, ka iesniedzāt savu autortiesību prasību. Mēs to pārskatīsim pēc iespējas ātrāk. Lūdzu, pārlādējiet lapu, lai iesniegtu vēl vienu."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datu kopumi"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Ja jūs interesē šī datu kopuma spoguļošana <a %(a_archival)s>arhivēšanas</a> vai <a %(a_llm)s>LLM apmācības</a> nolūkos, lūdzu, sazinieties ar mums."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Mūsu misija ir arhivēt visas pasaules grāmatas (kā arī rakstus, žurnālus utt.) un padarīt tās plaši pieejamas. Mēs uzskatām, ka visas grāmatas būtu jāspoguļo plaši, lai nodrošinātu redundanci un noturību. Tāpēc mēs apvienojam failus no dažādiem avotiem. Daži avoti ir pilnīgi atvērti un var tikt spoguļoti lielapjomā (piemēram, Sci-Hub). Citi ir slēgti un aizsargāti, tāpēc mēs cenšamies tos nokasīt, lai “atbrīvotu” to grāmatas. Vēl citi atrodas kaut kur pa vidu."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Visi mūsu dati var tikt <a %(a_torrents)s>torrenti</a>, un visi mūsu metadati var tikt <a %(a_anna_software)s>ģenerēti</a> vai <a %(a_elasticsearch)s>lejupielādēti</a> kā ElasticSearch un MariaDB datubāzes. Neapstrādātie dati var tikt manuāli izpētīti caur JSON failiem, piemēram, <a %(a_dbrecord)s>šo</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Pārskats"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Zemāk ir ātrs pārskats par failu avotiem Annas Arhīvā."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Avots"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Izmērs"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% spoguļots ar AA / pieejami torrenti"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Failu skaita procenti"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Pēdējo reizi atjaunināts"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Daiļliteratūra un nedaiļliteratūra"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] ""
msgstr[1] ""
msgstr[2] "%(count)s faili"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Caur Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: iesaldēts kopš 2021. gada; lielākā daļa pieejama caur torrentiem"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: nelieli papildinājumi kopš tā laika</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Izslēdzot “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Daiļliteratūras torrenti ir aizkavēti (lai gan ID ~4-6M nav torrentēti, jo tie pārklājas ar mūsu Zlib torrentiem)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "“Ķīniešu” kolekcija Z-Library šķiet tāda pati kā mūsu DuXiu kolekcija, bet ar atšķirīgiem MD5. Mēs izslēdzam šos failus no torrentiem, lai izvairītos no dublēšanās, bet joprojām rādām tos mūsu meklēšanas indeksā."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrolētā Digitālā Aizdošana"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ failu ir meklējami."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Kopā"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Izslēdzot dublikātus"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Tā kā ēnu bibliotēkas bieži sinhronizē datus viena ar otru, starp bibliotēkām ir ievērojama pārklāšanās. Tāpēc skaitļi nesummējas līdz kopējam skaitam."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "“Anna’s Archive spoguļots un sēklots” procents parāda, cik daudz failu mēs paši spoguļojam. Mēs sēklojam šos failus masveidā caur torrentiem un padarām tos pieejamus tiešai lejupielādei caur partneru vietnēm."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Avotu bibliotēkas"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Dažas avotu bibliotēkas veicina savu datu masveida koplietošanu, izmantojot torrentus, savukārt citas ne tik viegli dalās ar savu kolekciju. Pēdējā gadījumā Anna’s Archive mēģina nokasīt viņu kolekcijas un padarīt tās pieejamas (skatiet mūsu <a %(a_torrents)s>Torrenti</a> lapu). Ir arī starpposma situācijas, piemēram, kad avotu bibliotēkas ir gatavas dalīties, bet tām nav resursu to darīt. Šādos gadījumos mēs arī cenšamies palīdzēt."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Zemāk ir pārskats par to, kā mēs sadarbojamies ar dažādām avotu bibliotēkām."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Avots"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Faili"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Ikdienas <a %(dbdumps)s>HTTP datubāzes izgāztuves</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automātiskie torrenti <a %(nonfiction)s>daiļliteratūrai</a> un <a %(fiction)s>nedaiļliteratūrai</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(covers)s>grāmatu vāku torrentu</a> kolekciju"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub kopš 2021. gada nav pievienojis jaunus failus."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadatu izgāztuves pieejamas <a %(scihub1)s>šeit</a> un <a %(scihub2)s>šeit</a>, kā arī kā daļa no <a %(libgenli)s>Libgen.li datubāzes</a> (ko mēs izmantojam)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Datu torrenti pieejami <a %(scihub1)s>šeit</a>, <a %(scihub2)s>šeit</a> un <a %(libgenli)s>šeit</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Daži jauni faili tiek <a %(libgenrs)s>pievienoti</a> Libgen “scimag”, bet nepietiekami, lai izveidotu jaunus torrentus"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Ceturkšņa <a %(dbdumps)s>HTTP datubāzes izgāztuves</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Nedaiļliteratūras torrenti tiek kopīgoti ar Libgen.rs (un spoguļoti <a %(libgenli)s>šeit</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annas Arhīvs un Libgen.li kopīgi pārvalda <a %(comics)s>komiksu grāmatu</a>, <a %(magazines)s>žurnālu</a>, <a %(standarts)s>standarta dokumentu</a> un <a %(fiction)s>daiļliteratūras (atšķirīga no Libgen.rs)</a> kolekcijas."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Viņu “fiction_rus” kolekcijai (krievu daiļliteratūra) nav īpašu torrentu, bet to sedz citu izlaistie torrenti, un mēs uzturam <a %(fiction_rus)s>spoguli</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annas Arhīvs un Z-Library kopīgi pārvalda <a %(metadata)s>Z-Library metadatu</a> un <a %(files)s>Z-Library failu</a> kolekciju"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Daži metadati pieejami caur <a %(openlib)s>Open Library datubāzes izgāztuvēm</a>, bet tie neaptver visu IA kolekciju"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nav viegli pieejamu metadatu izgāztuvju visai kolekcijai"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(ia)s>IA metadatu</a> kolekciju"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Faili pieejami tikai ierobežotai aizņemšanai, ar dažādiem piekļuves ierobežojumiem"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(ia)s>IA failu</a> kolekciju"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Dažādas metadatu datubāzes izkaisītas pa Ķīnas internetu; bieži vien maksas datubāzes"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nav viegli pieejamu metadatu izgāztuvju visai kolekcijai."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(duxiu)s>DuXiu metadatu</a> kolekciju"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Dažādas failu datubāzes, kas izkaisītas Ķīnas internetā; bieži vien maksas datubāzes"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Lielākā daļa failu pieejami tikai ar premium BaiduYun kontiem; lēns lejupielādes ātrums."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(duxiu)s>DuXiu failu</a> kolekciju"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Dažādi mazāki vai vienreizēji avoti. Mēs mudinām cilvēkus vispirms augšupielādēt citās ēnu bibliotēkās, bet dažreiz cilvēkiem ir kolekcijas, kas ir pārāk lielas, lai citi tās varētu sakārtot, bet ne pietiekami lielas, lai tām būtu sava kategorija."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Tikai metadatu avoti"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Mēs arī bagātinām savu kolekciju ar tikai metadatu avotiem, kurus varam saskaņot ar failiem, piemēram, izmantojot ISBN numurus vai citus laukus. Zemāk ir pārskats par tiem. Atkal, daži no šiem avotiem ir pilnīgi atvērti, savukārt citus mums ir jānokasa."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Mūsu iedvesma metadatu vākšanai ir Ārona Švarca mērķis “viena tīmekļa lapa katrai jebkad publicētai grāmatai”, kuram viņš izveidoja <a %(a_openlib)s>Open Library</a>. Šis projekts ir veiksmīgs, bet mūsu unikālā pozīcija ļauj mums iegūt metadatus, kurus viņi nevar. Vēl viena iedvesma bija mūsu vēlme zināt <a %(a_blog)s>cik daudz grāmatu ir pasaulē</a>, lai mēs varētu aprēķināt, cik daudz grāmatu mums vēl ir jāglābj."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Ņemiet vērā, ka metadatu meklēšanā mēs parādām oriģinālos ierakstus. Mēs neveicam ierakstu apvienošanu."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Pēdējo reizi atjaunināts"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Mēneša <a %(dbdumps)s>datubāzes izgāztuves</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nav pieejams tieši lielapjomā, aizsargāts pret skrāpēšanu"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annas Arhīvs pārvalda <a %(worldcat)s>OCLC (WorldCat) metadatu</a> kolekciju"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Vienotā datubāze"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Mēs apvienojam visus iepriekš minētos avotus vienotā datubāzē, kuru izmantojam šīs vietnes apkalpošanai. Šī vienotā datubāze nav pieejama tieši, bet, tā kā Anna’s Archive ir pilnīgi atvērta pirmkoda, to var diezgan viegli <a %(a_generated)s>ģenerēt</a> vai <a %(a_downloaded)s>lejupielādēt</a> kā ElasticSearch un MariaDB datubāzes. Skripti šajā lapā automātiski lejupielādēs visus nepieciešamos metadatus no iepriekš minētajiem avotiem."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Ja vēlaties izpētīt mūsu datus pirms šo skriptu palaišanas lokāli, varat apskatīt mūsu JSON failus, kas saista tālāk uz citiem JSON failiem. <a %(a_json)s>Šis fails</a> ir labs sākumpunkts."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Pielāgots no mūsu <a %(a_href)s>emūru ieraksta</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> ir milzīga skenēto grāmatu datubāze, ko izveidojusi <a %(superstar_link)s>SuperStar Digital Library Group</a>. Lielākā daļa ir akadēmiskās grāmatas, kas skenētas, lai padarītu tās pieejamas digitāli universitātēm un bibliotēkām. Mūsu angliski runājošajai auditorijai <a %(princeton_link)s>Princeton</a> un <a %(uw_link)s>Vašingtonas Universitāte</a> piedāvā labus pārskatus. Ir arī lielisks raksts, kas sniedz vairāk informācijas: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Duxiu grāmatas jau ilgu laiku tiek pirātētas Ķīnas internetā. Parasti tās tiek pārdotas par mazāk nekā dolāru no tālākpārdevējiem. Tās parasti tiek izplatītas, izmantojot Ķīnas ekvivalentu Google Drive, kas bieži tiek uzlauzts, lai nodrošinātu lielāku krātuves vietu. Dažas tehniskās detaļas var atrast <a %(link1)s>šeit</a> un <a %(link2)s>šeit</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Lai gan grāmatas ir daļēji publiski izplatītas, tās ir diezgan grūti iegūt lielos apjomos. Mums tas bija augstu mūsu TODO sarakstā, un mēs tam piešķīrām vairākus mēnešus pilna laika darba. Tomēr 2023. gada beigās neticams, apbrīnojams un talantīgs brīvprātīgais sazinājās ar mums, sakot, ka viņi jau ir paveikuši visu šo darbu — par lielām izmaksām. Viņi dalījās ar mums pilnu kolekciju, negaidot neko pretī, izņemot ilgtermiņa saglabāšanas garantiju. Patiesi ievērojami."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resursi"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Kopējais failu skaits: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Kopējais failu izmērs: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Faili, ko spoguļo Annas Arhīvs: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Pēdējoreiz atjaunināts: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrenti no Annas Arhīva"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Piemēra ieraksts Annas Arhīvā"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Mūsu emūru ieraksts par šiem datiem"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripti metadatu importēšanai"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Annas Arhīva Konteineru formāts"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Vairāk informācijas no mūsu brīvprātīgajiem (neapstrādātas piezīmes):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Kontrolētā Digitālā Aizdošana"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Šis datu kopums ir cieši saistīts ar <a %(a_datasets_openlib)s>Open Library datu kopumu</a>. Tas satur visu metadatu un lielas daļas failu no IA kontrolētās digitālās aizdevumu bibliotēkas nokasījumu. Atjauninājumi tiek izlaisti <a %(a_aac)s>Annas Arhīva konteineru formātā</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Šie ieraksti tiek tieši atsaukti no Open Library datu kopuma, bet satur arī ierakstus, kas nav Open Library. Mums ir arī vairāki datu faili, kurus gadu gaitā nokasījuši kopienas locekļi."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Kolekcija sastāv no divām daļām. Jums ir nepieciešamas abas daļas, lai iegūtu visus datus (izņemot aizstātās torrentus, kas ir izsvītrotas torrentu lapā)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "mūsu pirmais izlaidums, pirms mēs standartizējām <a %(a_aac)s>Annas Arhīva Konteineru (AAC) formātu</a>. Satur metadatus (kā json un xml), pdf failus (no acsm un lcpdf digitālās aizdošanas sistēmām) un vāku sīktēlus."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "pakāpeniski jauni izlaidumi, izmantojot AAC. Satur tikai metadatus ar laika zīmogiem pēc 2023-01-01, jo pārējo jau aptver “ia”. Tāpat visi pdf faili, šoreiz no acsm un “bookreader” (IA tīmekļa lasītājs) aizdošanas sistēmām. Lai gan nosaukums nav pilnīgi precīzs, mēs joprojām ievietojam bookreader failus ia2_acsmpdf_files kolekcijā, jo tie ir savstarpēji izslēdzoši."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Galvenā %(source)s vietne"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitālā Aizdošanas Bibliotēka"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadatu dokumentācija (vairums lauku)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN valsts informācija"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Starptautiskā ISBN aģentūra regulāri izplata diapazonus, kurus tā ir piešķīrusi nacionālajām ISBN aģentūrām. No tā mēs varam noteikt, kurai valstij, reģionam vai valodu grupai šis ISBN pieder. Pašlaik mēs izmantojam šos datus netieši, izmantojot <a %(a_isbnlib)s>isbnlib</a> Python bibliotēku."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resursi"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Pēdējoreiz atjaunināts: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN mājaslapa"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Par dažādu Library Genesis dakšu vēsturi skatiet lapu <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li satur lielāko daļu tā paša satura un metadatu kā Libgen.rs, bet tam ir dažas papildu kolekcijas, proti, komiksi, žurnāli un standarta dokumenti. Tas ir arī integrējis <a %(a_scihub)s>Sci-Hub</a> savā metadatu un meklēšanas dzinējā, ko mēs izmantojam mūsu datubāzei."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Šīs bibliotēkas metadati ir brīvi pieejami <a %(a_libgen_li)s>libgen.li</a>. Tomēr šis serveris ir lēns un neatbalsta pārtrauktu savienojumu atsākšanu. Tie paši faili ir pieejami arī <a %(a_ftp)s>FTP serverī</a>, kas darbojas labāk."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrenti ir pieejami lielākajai daļai papildu satura, īpaši komiksu, žurnālu un standarta dokumentu torrenti ir izlaisti sadarbībā ar Annas Arhīvu."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Daiļliteratūras kolekcijai ir savi torrenti (atšķirīgi no <a %(a_href)s>Libgen.rs</a>), sākot no %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Saskaņā ar Libgen.li administratoru, “fiction_rus” (krievu daiļliteratūra) kolekcijai vajadzētu būt segtai ar regulāri izlaistiem torrentiem no <a %(a_booktracker)s>booktracker.org</a>, īpaši <a %(a_flibusta)s>flibusta</a> un <a %(a_librusec)s>lib.rus.ec</a> torrentiem (kurus mēs spogulējam <a %(a_torrents)s>šeit</a>, lai gan vēl neesam noteikuši, kuri torrenti atbilst kuriem failiem)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistiku par visām kolekcijām var atrast <a %(a_href)s>libgen vietnē</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Šķiet, ka arī daiļliteratūra ir novirzījusies, taču bez jauniem straumēm. Šķiet, ka tas ir noticis kopš 2022. gada sākuma, lai gan mēs to neesam pārbaudījuši."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Noteikti diapazoni bez torrentiem (piemēram, daiļliteratūras diapazoni f_3463000 līdz f_4260000) visticamāk ir Z-Library (vai citi dublikāti) faili, lai gan mēs varētu vēlēties veikt deduplikāciju un izveidot torrentus lgli-unikāliem failiem šajos diapazonos."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Ņemiet vērā, ka torrentu faili, kas atsaucas uz “libgen.is”, ir tieši <a %(a_libgen)s>Libgen.rs</a> spoguļi (“.is” ir cita domēna, ko izmanto Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Noderīgs resurss metadatu izmantošanai ir <a %(a_href)s>šī lapa</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Daiļliteratūras torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Komiksu torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Žurnālu torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standarta dokumentu torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Krievu daiļliteratūras torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadati caur FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadatu lauku informācija"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Citu torrentu spogulis (un unikāli daiļliteratūras un komiksu torrenti)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskusiju forums"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Mūsu emuāra ieraksts par komiksu grāmatu izdošanu"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Īss stāsts par dažādiem Library Genesis (vai “Libgen”) atzariem ir tāds, ka laika gaitā dažādi cilvēki, kas bija iesaistīti Library Genesis, sastrīdējās un devās katrs savu ceļu."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "“.fun” versiju izveidoja sākotnējais dibinātājs. Tā tiek pārveidota par jaunu, vairāk izplatītu versiju."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "“.rs” versijai ir ļoti līdzīgi dati, un tā viskonsekventāk izlaiž savu kolekciju lielapjoma torrentos. Tā ir aptuveni sadalīta “daiļliteratūras” un “nedaiļliteratūras” sadaļās."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Sākotnēji pieejams “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>“.li” versijai</a> ir milzīga komiksu kolekcija, kā arī cits saturs, kas (vēl) nav pieejams lielapjoma lejupielādei caur torrentiem. Tai ir atsevišķa daiļliteratūras grāmatu torrentu kolekcija, un tās datubāzē ir <a %(a_scihub)s>Sci-Hub</a> metadati."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Saskaņā ar šo <a %(a_mhut)s>foruma ierakstu</a>, Libgen.li sākotnēji tika mitināts vietnē “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> zināmā mērā arī ir Library Genesis atzars, lai gan viņi savam projektam izmantoja citu nosaukumu."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Šī lapa ir par “.rs” versiju. Tā ir pazīstama ar to, ka konsekventi publicē gan savus metadatus, gan pilnu grāmatu kataloga saturu. Tās grāmatu kolekcija ir sadalīta starp daiļliteratūras un nedaiļliteratūras daļām."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Noderīgs resurss metadatu izmantošanai ir <a %(a_metadata)s>šī lapa</a> (bloķē IP diapazonus, var būt nepieciešams VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "No 2024. gada marta jauni torenti tiek publicēti <a %(a_href)s>šajā foruma pavedienā</a> (bloķē IP diapazonus, var būt nepieciešams VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Ne-fantastikas torenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fantastikas torenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadati"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadatu lauku informācija"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Ne-fantastikas torenti"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fantastikas torenti"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskusiju forums"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torenti no Annas Arhīva (grāmatu vāki)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Mūsu emuārs par grāmatu vāku izlaidumu"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis ir pazīstams ar to, ka jau dāsni padara savus datus pieejamus lielos apjomos caur torentiem. Mūsu Libgen kolekcija sastāv no papildu datiem, kurus viņi tieši neizlaiž, sadarbojoties ar viņiem. Liels paldies visiem, kas iesaistīti Library Genesis darbā ar mums!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Izlaidums 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Šis <a %(blog_post)s>pirmais izlaidums</a> ir diezgan mazs: aptuveni 300GB grāmatu vāku no Libgen.rs dakšas, gan fantastikas, gan ne-fantastikas. Tie ir organizēti tāpat kā tie parādās libgen.rs, piemēram:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s ne-fantastikas grāmatai."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s fantastikas grāmatai."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Tāpat kā ar Z-Library kolekciju, mēs tos visus ievietojām lielā .tar failā, kuru var uzmontēt, izmantojot <a %(a_ratarmount)s>ratarmount</a>, ja vēlaties failus tieši apkalpot."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> ir patentēta datubāze, ko pārvalda bezpeļņas organizācija <a %(a_oclc)s>OCLC</a>, kas apkopo metadatu ierakstus no bibliotēkām visā pasaulē. Iespējams, tā ir lielākā bibliotēku metadatu kolekcija pasaulē."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "2023. gada oktobris, sākotnējā versija:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "2023. gada oktobrī mēs <a %(a_scrape)s>izlaidām</a> visaptverošu OCLC (WorldCat) datubāzes skrāpējumu, <a %(a_aac)s>Annas Arhīva Konteineru formātā</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrenti no Annas Arhīva"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Mūsu emuāra ieraksts par šiem datiem"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library ir atvērtā koda projekts no Internet Archive, lai katalogizētu visas pasaules grāmatas. Tam ir viena no pasaulē lielākajām grāmatu skenēšanas operācijām, un tam ir daudz grāmatu, kas pieejamas digitālai aizņemšanai. Tā grāmatu metadatu katalogs ir brīvi pieejams lejupielādei un ir iekļauts Annas Arhīvā (lai gan pašlaik nav meklēšanā, izņemot, ja jūs tieši meklējat Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Izlaidums 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Šis ir daudz zvanu uz isbndb.com izgāztuve 2022. gada septembrī. Mēs mēģinājām aptvert visus ISBN diapazonus. Tie ir aptuveni 30,9 miljoni ierakstu. Viņu mājaslapā viņi apgalvo, ka viņiem faktiski ir 32,6 miljoni ierakstu, tāpēc mēs, iespējams, kaut kā esam palaiduši garām dažus, vai <em>viņi</em> varētu kaut ko darīt nepareizi."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON atbildes ir gandrīz neapstrādātas no viņu servera. Viena datu kvalitātes problēma, ko mēs pamanījām, ir tā, ka ISBN-13 numuriem, kas sākas ar citu prefiksu nekā “978-”, viņi joprojām iekļauj “isbn” lauku, kas vienkārši ir ISBN-13 numurs ar pirmajiem 3 numuriem noņemtiem (un pārbaudes cipars pārrēķināts). Tas acīmredzami ir nepareizi, bet tā viņi to dara, tāpēc mēs to nemainījām."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Vēl viena potenciāla problēma, ar kuru jūs varētu saskarties, ir fakts, ka “isbn13” laukam ir dublikāti, tāpēc jūs to nevarat izmantot kā primāro atslēgu datubāzē. “isbn13”+“isbn” lauki kopā šķiet unikāli."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Lai iegūtu informāciju par Sci-Hub, lūdzu, skatiet tā <a %(a_scihub)s>oficiālo vietni</a>, <a %(a_wikipedia)s>Wikipedia lapu</a> un šo <a %(a_radiolab)s>podkāsta interviju</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Ņemiet vērā, ka Sci-Hub ir <a %(a_reddit)s>iesaldēts kopš 2021. gada</a>. Tas bija iesaldēts arī agrāk, bet 2021. gadā tika pievienoti daži miljoni rakstu. Tomēr ierobežots skaits rakstu joprojām tiek pievienots Libgen “scimag” kolekcijām, lai gan nepietiekami, lai izveidotu jaunus lielapjoma torrentus."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Mēs izmantojam Sci-Hub metadatus, ko nodrošina <a %(a_libgen_li)s>Libgen.li</a> savā “scimag” kolekcijā. Mēs arī izmantojam <a %(a_dois)s>dois-2022-02-12.7z</a> datu kopu."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Ņemiet vērā, ka “smarch” torrenti ir <a %(a_smarch)s>novecojuši</a> un tāpēc nav iekļauti mūsu torrentu sarakstā."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrenti Annas Arhīvā"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadati un torrenti"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrenti Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrenti Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Atjauninājumi Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia lapa"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podkāsta intervija"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Augšupielādes Annas Arhīvā"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Pārskats no <a %(a1)s>datu kopu lapas</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Dažādi mazāki vai vienreizēji avoti. Mēs mudinām cilvēkus vispirms augšupielādēt citās ēnu bibliotēkās, bet dažreiz cilvēkiem ir kolekcijas, kas ir pārāk lielas, lai citi tās varētu sakārtot, bet ne pietiekami lielas, lai tām būtu sava kategorija."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "“Augšupielādes” kolekcija ir sadalīta mazākās apakškolekcijās, kas norādītas AACID un torrentu nosaukumos. Visas apakškolekcijas vispirms tika dublētas pret galveno kolekciju, lai gan metadatu “upload_records” JSON faili joprojām satur daudz atsauču uz oriģinālajiem failiem. Lielākā daļa apakškolekciju tika iztīrītas no ne-grāmatu failiem, un tie parasti <em>nav</em> norādīti “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Daudzas apakškolekcijas pašas sastāv no apakš-apakškolekcijām (piemēram, no dažādiem oriģinālajiem avotiem), kas ir attēlotas kā direktorijas “filepath” laukos."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Apakškolekcijas ir:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Apakškolekcija"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Piezīmes"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "pārlūkot"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "meklēt"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "No <a %(a_href)s>aaaaarg.fail</a>. Izskatās diezgan pilnīgs. No mūsu brīvprātīgā “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "No <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrenta. Ir diezgan liela pārklāšanās ar esošajām rakstu kolekcijām, bet ļoti maz MD5 atbilstību, tāpēc mēs nolēmām to pilnībā saglabāt."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "<iRead eBooks</q> (fonētiski <q>ai rit i-books</q>; airitibooks.com) skrāpējums, ko veicis brīvprātīgais <q>j</q>. Atbilst <q>airitibooks</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "No kolekcijas <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Daļēji no oriģinālā avota, daļēji no the-eye.eu, daļēji no citiem spoguļiem."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "No privātas grāmatu torrenta vietnes, <a %(a_href)s>Bibliotik</a> (bieži dēvēta par “Bib”), kuras grāmatas tika apvienotas torrentos pēc nosaukuma (A.torrent, B.torrent) un izplatītas caur the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "No mūsu brīvprātīgā “bpb9v”. Lai iegūtu vairāk informācijas par <a %(a_href)s>CADAL</a>, skatiet piezīmes mūsu <a %(a_duxiu)s>DuXiu datu kopas lapā</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Vairāk no mūsu brīvprātīgā “bpb9v”, galvenokārt DuXiu faili, kā arī mapes “WenQu” un “SuperStar_Journals” (SuperStar ir uzņēmums, kas stāv aiz DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "No mūsu brīvprātīgā “cgiym”, ķīniešu teksti no dažādiem avotiem (pārstāvēti kā apakšdirektorijas), ieskaitot no <a %(a_href)s>China Machine Press</a> (liels ķīniešu izdevējs)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Neķīniešu kolekcijas (pārstāvētas kā apakšdirektorijas) no mūsu brīvprātīgā “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Grāmatu par Ķīnas arhitektūru skrāpējums, ko veicis brīvprātīgais <q>cm</q>: <q>Es to ieguvu, izmantojot tīkla ievainojamību izdevniecībā, bet šī nepilnība tagad ir novērsta</q>. Atbilst <q>chinese_architecture</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Grāmatas no akadēmiskās izdevniecības <a %(a_href)s>De Gruyter</a>, savāktas no dažiem lieliem torrentiem."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "<a %(a_href)s>docer.pl</a> skrāpējums, poļu failu koplietošanas vietne, kas koncentrējas uz grāmatām un citiem rakstītiem darbiem. Skrāpēts 2023. gada beigās brīvprātīgā “p” veiktā. Mums nav labu metadatu no oriģinālās vietnes (pat ne failu paplašinājumi), bet mēs filtrējām grāmatu līdzīgus failus un bieži vien varējām izvilkt metadatus no pašiem failiem."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubi, tieši no DuXiu, savākti brīvprātīgā “w”. Tikai nesenās DuXiu grāmatas ir pieejamas tieši caur e-grāmatām, tāpēc lielākā daļa no tām ir nesenas."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Atlikušie DuXiu faili no brīvprātīgā “m”, kas nebija DuXiu patentētajā PDG formātā (galvenā <a %(a_href)s>DuXiu datu kopa</a>). Savākti no daudziem oriģinālajiem avotiem, diemžēl nesaglabājot šos avotus faila ceļā."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Erotisko grāmatu skrāpējums, ko veicis brīvprātīgais <q>do no harm</q>. Atbilst <q>hentai</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Kolekcija, kas skrāpēta no japāņu Manga izdevēja, veicis brīvprātīgais “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Izvēlētie Longquan tiesu arhīvi</a>, nodrošinājis brīvprātīgais “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "<a %(a_href)s>magzdb.org</a> nokasīšana, Library Genesis sabiedrotais (tas ir saistīts ar libgen.rs sākumlapu), bet kurš nevēlējās tieši nodrošināt savus failus. Iegūts brīvprātīgā “p” 2023. gada beigās."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Dažādas mazas augšupielādes, pārāk mazas, lai būtu sava apakškolekcija, bet attēlotas kā direktorijas."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-grāmatas no AvaxHome, Krievijas failu koplietošanas vietnes."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Avīžu un žurnālu arhīvs. Atbilst <q>newsarch_magz</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Filozofijas dokumentācijas centra</a> skrāpējums."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Brīvprātīgā “o” kolekcija, kurš savāca poļu grāmatas tieši no oriģinālajiem izlaiduma (“scene”) tīmekļa vietnēm."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Apvienotās <a %(a_href)s>shuge.org</a> kolekcijas, ko veidojuši brīvprātīgie “cgiym” un “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Trantoras Impērijas bibliotēka”</a> (nosaukta pēc izdomātas bibliotēkas), ko 2022. gadā savāca brīvprātīgais “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Apakšapakškolekcijas (pārstāvētas kā direktorijas) no brīvprātīgā “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (autors <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taivānā), mebook (mebook.cc, 我的小书屋, mana mazā grāmatu istaba — woz9ts: “Šī vietne galvenokārt koncentrējas uz augstas kvalitātes e-grāmatu failu koplietošanu, no kuriem dažus pats īpašnieks ir noformējis. Īpašnieks tika <a %(a_arrested)s>arestēts</a> 2019. gadā, un kāds izveidoja kolekciju ar viņa koplietotajiem failiem.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Atlikušie DuXiu faili no brīvprātīgā “woz9ts”, kas nebija DuXiu patentētajā PDG formātā (vēl jāpārvērš PDF formātā)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrenti no Annas Arhīva"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library skrāpējums"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ir saknes <a %(a_href)s>Library Genesis</a> kopienā, un sākotnēji tika izveidota ar viņu datiem. Kopš tā laika tā ir ievērojami profesionalizējusies un tai ir daudz modernāks interfeiss. Tāpēc viņi spēj saņemt daudz vairāk ziedojumu, gan finansiāli, lai turpinātu uzlabot savu vietni, gan arī jaunu grāmatu ziedojumus. Viņi ir uzkrājuši lielu kolekciju papildus Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Atjauninājums uz 2023. gada februāri."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "2022. gada beigās tika arestēti Z-Library iespējami dibinātāji, un domēnus konfiscēja Amerikas Savienoto Valstu iestādes. Kopš tā laika vietne lēnām atgriežas tiešsaistē. Nav zināms, kas to pašlaik pārvalda."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Kolekcija sastāv no trim daļām. Sākotnējās apraksta lapas pirmajām divām daļām ir saglabātas zemāk. Jums ir nepieciešamas visas trīs daļas, lai iegūtu visus datus (izņemot aizstātus torrentus, kas ir izsvītroti torrentu lapā)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: mūsu pirmais izlaidums. Tas bija pats pirmais izlaidums, ko toreiz sauca par “Pirātu bibliotēkas spoguli” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: otrais izlaidums, šoreiz ar visiem failiem ietvertiem .tar failos."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: pakāpeniski jauni izlaidumi, izmantojot <a %(a_href)s>Annas Arhīva Konteineru (AAC) formātu</a>, tagad izlaisti sadarbībā ar Z-Library komandu."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenti no Annas Arhīva (metadati + saturs)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Piemēra ieraksts Annas Arhīvā (oriģinālā kolekcija)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Piemēra ieraksts Annas Arhīvā (“zlib3” kolekcija)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Galvenā vietne"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domēns"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Emuāra ieraksts par 1. izlaidumu"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Emuāra ieraksts par 2. izlaidumu"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib izlaidumi (oriģinālās apraksta lapas)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Izlaidums 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Sākotnējais spogulis tika rūpīgi iegūts 2021. un 2022. gada laikā. Šobrīd tas ir nedaudz novecojis: tas atspoguļo kolekcijas stāvokli 2021. gada jūnijā. Mēs to atjaunināsim nākotnē. Pašlaik mēs koncentrējamies uz šī pirmā izlaiduma izdošanu."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Tā kā Library Genesis jau ir saglabāts ar publiskiem torrentiem un ir iekļauts Z-Library, mēs 2022. gada jūnijā veicām pamata dublēšanu pret Library Genesis. Šim nolūkam mēs izmantojām MD5 hešus. Bibliotēkā, iespējams, ir daudz vairāk dublēta satura, piemēram, vairāki failu formāti ar vienu un to pašu grāmatu. To ir grūti precīzi noteikt, tāpēc mēs to nedarām. Pēc dublēšanas mums paliek vairāk nekā 2 miljoni failu, kuru kopējais apjoms ir nedaudz mazāks par 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Kolekcija sastāv no divām daļām: MySQL “.sql.gz” metadatu izgāztuves un 72 torrent failiem, katrs apmēram 50-100GB. Metadati satur datus, kā ziņots Z-Library vietnē (nosaukums, autors, apraksts, faila tips), kā arī faktisko faila izmēru un md5sum, ko mēs novērojām, jo dažreiz šie dati nesakrīt. Šķiet, ka ir failu diapazoni, kuriem pašai Z-Library ir nepareizi metadati. Dažos atsevišķos gadījumos mēs varētu būt nepareizi lejupielādējuši failus, ko mēs centīsimies noteikt un labot nākotnē."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Lielie torrent faili satur faktisko grāmatu datus, ar Z-Library ID kā faila nosaukumu. Failu paplašinājumus var rekonstruēt, izmantojot metadatu izgāztuvi."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Kolekcija ir jaukums no daiļliteratūras un nedaiļliteratūras satura (nav atdalīts kā Library Genesis). Kvalitāte arī ļoti atšķiras."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Šis pirmais izlaidums tagad ir pilnībā pieejams. Ņemiet vērā, ka torrent faili ir pieejami tikai caur mūsu Tor spoguli."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Izlaidums 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Mēs esam ieguvuši visas grāmatas, kas tika pievienotas Z-Library starp mūsu pēdējo spoguli un 2022. gada augustu. Mēs arī atgriezāmies un nokasījām dažas grāmatas, kuras pirmo reizi palaidām garām. Kopumā šī jaunā kolekcija ir apmēram 24TB. Atkal, šī kolekcija ir dublēta pret Library Genesis, jo šai kolekcijai jau ir pieejami torrenti."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Dati ir organizēti līdzīgi kā pirmajā izlaidumā. Ir MySQL “.sql.gz” metadatu izgāztuve, kas ietver arī visus pirmā izlaiduma metadatus, tādējādi to aizstājot. Mēs arī pievienojām dažas jaunas kolonnas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: vai šis fails jau ir Library Genesis, vai nu nedaiļliteratūras, vai daiļliteratūras kolekcijā (saskaņots ar md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: kurā torrentā šis fails atrodas."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: iestatīts, kad mēs nevarējām lejupielādēt grāmatu."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mēs to minējām pēdējo reizi, bet tikai lai precizētu: “faila nosaukums” un “md5” ir faktiskās faila īpašības, savukārt “faila nosaukums_ziņots” un “md5_ziņots” ir tas, ko mēs nokasījām no Z-Library. Dažreiz šie divi nesakrīt, tāpēc mēs iekļāvām abus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Šim izlaidumam mēs mainījām kolāciju uz “utf8mb4_unicode_ci”, kas būtu saderīga ar vecākām MySQL versijām."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Datu faili ir līdzīgi kā iepriekšējā reizē, lai gan tie ir daudz lielāki. Mēs vienkārši nevarējām apgrūtināt izveidot daudzus mazākus torrent failus. “pilimi-zlib2-0-14679999-extra.torrent” satur visus failus, kurus mēs palaidām garām pēdējā izlaidumā, savukārt pārējie torrenti ir visi jauni ID diapazoni. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Atjauninājums %(date)s:</strong> Mēs izveidojām lielāko daļu mūsu torrentu pārāk lielus, izraisot torrent klientu problēmas. Mēs tos noņēmām un izlaidām jaunus torrentus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Atjauninājums %(date)s:</strong> Joprojām bija pārāk daudz failu, tāpēc mēs tos iesaiņojām tar failos un atkal izlaidām jaunus torrentus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Izlaiduma 2 papildinājums (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Šis ir viens papildu torrent fails. Tas nesatur nekādu jaunu informāciju, bet tajā ir dati, kuru aprēķināšana var aizņemt kādu laiku. Tas padara to ērtu, jo šī torrenta lejupielāde bieži vien ir ātrāka nekā aprēķināšana no nulles. Īpaši tas satur SQLite indeksus tar failiem, izmantošanai ar <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Biežāk Uzdotie Jautājumi (BUJ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Kas ir Annas Arhīvs?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Annas Arhīvs</span> ir bezpeļņas projekts ar diviem mērķiem:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Saglabāšana:</strong> Visu cilvēces zināšanu un kultūras dublēšana.</li><li><strong>Piekļuve:</strong> Šo zināšanu un kultūras padarīšana pieejamu ikvienam pasaulē.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Visi mūsu <a %(a_code)s>kodi</a> un <a %(a_datasets)s>dati</a> ir pilnībā atvērta pirmkoda."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Saglabāšana"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Mēs saglabājam grāmatas, rakstus, komiksus, žurnālus un daudz ko citu, apkopojot šos materiālus no dažādām <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ēnu bibliotēkām</a>, oficiālajām bibliotēkām un citām kolekcijām vienuviet. Visi šie dati tiek saglabāti uz visiem laikiem, padarot tos viegli dublējamus lielos apjomos — izmantojot torrentus —, radot daudz kopiju visā pasaulē. Dažas ēnu bibliotēkas to jau dara pašas (piemēram, Sci-Hub, Library Genesis), savukārt Annas Arhīvs “atbrīvo” citas bibliotēkas, kas nepiedāvā lielapjoma izplatīšanu (piemēram, Z-Library) vai vispār nav ēnu bibliotēkas (piemēram, Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Šī plašā izplatīšana, apvienojumā ar atvērta pirmkoda kodu, padara mūsu vietni izturīgu pret izņemšanu un nodrošina ilgtermiņa cilvēces zināšanu un kultūras saglabāšanu. Uzziniet vairāk par <a href=\"/datasets\">mūsu datu kopām</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Mēs lēšam, ka esam saglabājuši aptuveni <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% pasaules grāmatu</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Piekļuve"

#, fuzzy
msgid "page.home.access.text"
msgstr "Mēs sadarbojamies ar partneriem, lai padarītu mūsu kolekcijas viegli un brīvi pieejamas ikvienam. Mēs uzskatām, ka ikvienam ir tiesības uz cilvēces kolektīvo gudrību. Un <a %(a_search)s>ne uz autoru rēķina</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Stundu lejupielādes pēdējo 30 dienu laikā. Stundu vidējais: %(hourly)s. Dienas vidējais: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Mēs stingri ticam informācijas brīvai plūsmai un zināšanu un kultūras saglabāšanai. Ar šo meklētājprogrammu mēs balstāmies uz milžu pleciem. Mēs dziļi cienām cilvēku smago darbu, kuri ir izveidojuši dažādas ēnu bibliotēkas, un ceram, ka šī meklētājprogramma paplašinās to sasniedzamību."

#, fuzzy
msgid "page.about.text3"
msgstr "Lai sekotu līdzi mūsu progresam, sekojiet Annai <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> vai <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Jautājumiem un atsauksmēm, lūdzu, sazinieties ar Annu pa %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Kā es varu palīdzēt?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Sekojiet mums <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> vai <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Izplatiet informāciju par Annas Arhīvu Twitter, Reddit, Tiktok, Instagram, vietējā kafejnīcā vai bibliotēkā, vai kur vien ejat! Mēs neticam vārtu sargāšanai — ja mūs izņems, mēs vienkārši parādīsimies citur, jo visi mūsu kodi un dati ir pilnībā atvērta pirmkoda.</li><li>3. Ja varat, apsveriet iespēju <a href=\"/donate\">ziedot</a>.</li><li>4. Palīdziet <a href=\"https://translate.annas-software.org/\">tulkot</a> mūsu vietni dažādās valodās.</li><li>5. Ja esat programmatūras inženieris, apsveriet iespēju piedalīties mūsu <a href=\"https://annas-software.org/\">atvērtā pirmkoda</a> projektos vai sēdēt mūsu <a href=\"/datasets\">torrentus</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Tagad mums ir arī sinhronizēts Matrix kanāls pie %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Ja esat drošības pētnieks, mēs varam izmantot jūsu prasmes gan uzbrukumam, gan aizsardzībai. Apskatiet mūsu <a %(a_security)s>Drošības</a> lapu."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Mēs meklējam ekspertus maksājumu jomā anonīmiem tirgotājiem. Vai varat palīdzēt mums pievienot ērtākus ziedošanas veidus? PayPal, WeChat, dāvanu kartes. Ja zināt kādu, lūdzu, sazinieties ar mums."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Mēs vienmēr meklējam vairāk serveru jaudas."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Jūs varat palīdzēt, ziņojot par failu problēmām, atstājot komentārus un veidojot sarakstus tieši šajā vietnē. Jūs varat arī palīdzēt, <a %(a_upload)s>augšupielādējot vairāk grāmatu</a> vai labojot esošo grāmatu failu problēmas vai formatējumu."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Izveidojiet vai palīdziet uzturēt Annas Arhīva Wikipedia lapu savā valodā."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Mēs meklējam iespējas izvietot nelielas, gaumīgas reklāmas. Ja vēlaties reklamēties vietnē Annas Arhīvs, lūdzu, sazinieties ar mums."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Mēs būtu priecīgi, ja cilvēki izveidotu <a %(a_mirrors)s>spoguļus</a>, un mēs finansiāli atbalstīsim šo procesu."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Lai iegūtu plašāku informāciju par to, kā kļūt par brīvprātīgo, skatiet mūsu <a %(a_volunteering)s>Brīvprātīgo un atlīdzību</a> lapu."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Kāpēc lejupielādes ir tik lēnas?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Mums burtiski nav pietiekami daudz resursu, lai nodrošinātu visiem pasaulē ātrgaitas lejupielādes, cik ļoti mēs to arī vēlētos. Ja kāds bagāts labvēlis vēlētos mums to nodrošināt, tas būtu neticami, bet līdz tam mēs cenšamies darīt visu iespējamo. Mēs esam bezpeļņas projekts, kas tik tikko spēj sevi uzturēt ar ziedojumiem."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Tāpēc mēs ieviesām divas sistēmas bezmaksas lejupielādēm ar mūsu partneriem: koplietojami serveri ar lēnām lejupielādēm un nedaudz ātrāki serveri ar gaidīšanas sarakstu (lai samazinātu vienlaicīgi lejupielādējošo cilvēku skaitu)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Mums ir arī <a %(a_verification)s>pārlūka verifikācija</a> mūsu lēnajām lejupielādēm, jo citādi boti un skrāpētāji tos ļaunprātīgi izmantos, padarot lietas vēl lēnākas likumīgiem lietotājiem."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Ņemiet vērā, ka, izmantojot Tor pārlūku, jums, iespējams, būs jāpielāgo savi drošības iestatījumi. Zemākajā no opcijām, ko sauc par “Standarta”, Cloudflare turniketa izaicinājums izdodas. Augstākajās opcijās, ko sauc par “Drošāks” un “Drošākais”, izaicinājums neizdodas."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Lieliem failiem dažreiz lēna lejupielāde var pārtrūkt vidū. Mēs iesakām izmantot lejupielādes pārvaldnieku (piemēram, JDownloader), lai automātiski atsāktu lielas lejupielādes."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Ziedojumu BUJ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Vai dalības automātiski atjaunojas?</div> Dalības <strong>neatjaunojas</strong> automātiski. Jūs varat pievienoties uz tik ilgu vai īsu laiku, cik vēlaties."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Vai es varu uzlabot savu dalību vai iegūt vairākas dalības?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Vai jums ir citi maksājumu veidi?</div> Pašlaik nē. Daudzi cilvēki nevēlas, lai tādi arhīvi kā šis pastāvētu, tāpēc mums jābūt uzmanīgiem. Ja varat palīdzēt mums droši izveidot citus (ērtākus) maksājumu veidus, lūdzu, sazinieties ar mums pa %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Ko nozīmē diapazoni mēnesī?</div> Jūs varat sasniegt diapazona zemāko pusi, piemērojot visus atlaides, piemēram, izvēloties periodu, kas ir ilgāks par mēnesi."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Kam jūs tērējat ziedojumus?</div> 100%% tiek tērēti pasaules zināšanu un kultūras saglabāšanai un pieejamības nodrošināšanai. Pašlaik mēs lielākoties tērējam tos serveriem, glabāšanai un joslas platumam. Neviens naudas līdzeklis netiek piešķirts nevienam komandas loceklim personīgi."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Vai es varu veikt lielu ziedojumu?</div> Tas būtu lieliski! Par ziedojumiem, kas pārsniedz dažus tūkstošus dolāru, lūdzu, sazinieties ar mums tieši pa %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Vai es varu veikt ziedojumu, nekļūstot par biedru?</div> Protams. Mēs pieņemam jebkura apjoma ziedojumus uz šo Monero (XMR) adresi: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Kā es varu augšupielādēt jaunas grāmatas?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternatīvi, jūs varat augšupielādēt tās Z-Library <a %(a_upload)s>šeit</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Mazām augšupielādēm (līdz 10 000 failiem) lūdzu, augšupielādējiet tos gan %(first)s, gan %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Pašlaik mēs iesakām augšupielādēt jaunas grāmatas Library Genesis dakšās. Šeit ir <a %(a_guide)s>ērts ceļvedis</a>. Ņemiet vērā, ka abas dakšas, kuras mēs indeksējam šajā vietnē, izmanto šo pašu augšupielādes sistēmu."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.li gadījumā, vispirms piesakieties <a %(a_forum)s >viņu forumā</a> ar lietotājvārdu %(username)s un paroli %(password)s, un pēc tam atgriezieties viņu <a %(a_upload_page)s >augšupielādes lapā</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Ja jūsu e-pasta adrese nedarbojas Libgen forumos, mēs iesakām izmantot <a %(a_mail)s>Proton Mail</a> (bezmaksas). Jūs varat arī <a %(a_manual)s>manuāli pieprasīt</a>, lai jūsu konts tiktu aktivizēts."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Ņemiet vērā, ka mhut.org bloķē noteiktus IP diapazonus, tāpēc var būt nepieciešams VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Lielām augšupielādēm (vairāk nekā 10 000 failu), kuras netiek pieņemtas Libgen vai Z-Library, lūdzu, sazinieties ar mums pa %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Lai augšupielādētu akadēmiskos rakstus, lūdzu, augšupielādējiet tos arī (papildus Library Genesis) <a %(a_stc_nexus)s>STC Nexus</a>. Viņi ir labākā ēnu bibliotēka jauniem rakstiem. Mēs vēl neesam tos integrējuši, bet mēs to darīsim kādā brīdī. Jūs varat izmantot viņu <a %(a_telegram)s>augšupielādes botu Telegram</a>, vai sazināties ar adresi, kas norādīta viņu piespraustajā ziņojumā, ja jums ir pārāk daudz failu, lai tos augšupielādētu šādā veidā."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Kā es varu pieprasīt grāmatas?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Šobrīd mēs nevaram izpildīt grāmatu pieprasījumus."

#, fuzzy
msgid "page.request.forums"
msgstr "Lūdzu, veiciet savus pieprasījumus Z-Library vai Libgen forumos."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nesūtiet mums e-pastus ar grāmatu pieprasījumiem."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Vai jūs ievācat metadatus?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Jā, mēs to darām."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Es lejupielādēju Džordža Orvela grāmatu \"1984\", vai policija nāks pie manām durvīm?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Neuztraucieties pārāk daudz, ir daudz cilvēku, kas lejupielādē no mūsu saistītajām vietnēm, un ir ārkārtīgi reti nonākt nepatikšanās. Tomēr, lai būtu drošībā, mēs iesakām izmantot VPN (maksas) vai <a %(a_tor)s>Tor</a> (bezmaksas)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Kā saglabāt savus meklēšanas iestatījumus?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Izvēlieties sev vēlamo iestatījumu, atstājiet meklēšanas lodziņu tukšu, noklikšķiniet uz “Meklēt” un pēc tam pievienojiet lapu grāmatzīmēm, izmantojot pārlūkprogrammas grāmatzīmju funkciju."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Vai jums ir mobilā lietotne?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Mums nav oficiālas mobilās lietotnes, bet jūs varat instalēt šo vietni kā lietotni."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Noklikšķiniet uz trīs punktu izvēlnes augšējā labajā stūrī un izvēlieties “Pievienot sākuma ekrānam”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Noklikšķiniet uz “Kopīgot” pogas apakšā un izvēlieties “Pievienot sākuma ekrānam”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Vai jums ir API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Mums ir stabils JSON API biedriem, lai iegūtu ātru lejupielādes URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentācija pašā JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Citiem lietošanas gadījumiem, piemēram, visu mūsu failu iterēšanai, pielāgotas meklēšanas veidošanai un tā tālāk, mēs iesakām <a %(a_generate)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Neapstrādātie dati var tikt manuāli izpētīti <a %(a_explore)s>caur JSON failiem</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Mūsu neapstrādāto torrentu sarakstu var lejupielādēt kā <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrenti Biežāk Uzdotie Jautājumi (BUJ)"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Es vēlētos palīdzēt sēšanai, bet man nav daudz diska vietas."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Izmantojiet <a %(a_list)s>torrentu saraksta ģeneratoru</a>, lai ģenerētu torrentu sarakstu, kuriem visvairāk nepieciešama torrentēšana, atbilstoši jūsu uzglabāšanas vietas ierobežojumiem."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrentu lejupielāde ir pārāk lēna; vai es varu lejupielādēt datus tieši no jums?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Jā, skatiet <a %(a_llm)s>LLM datu</a> lapu."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Vai es varu lejupielādēt tikai daļu no failiem, piemēram, tikai konkrētu valodu vai tēmu?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Īsā atbilde: ne viegli."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Garā atbilde:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Lielākā daļa torrentu satur failus tieši, kas nozīmē, ka jūs varat norādīt torrentu klientiem lejupielādēt tikai nepieciešamos failus. Lai noteiktu, kuri faili jālejupielādē, jūs varat <a %(a_generate)s>ģenerēt</a> mūsu metadatus vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Diemžēl, daudzas torrentu kolekcijas satur .zip vai .tar failus saknē, tādā gadījumā jums ir jālejupielādē viss torrents, pirms varat izvēlēties atsevišķus failus."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Mums gan ir <a %(a_ideas)s>dažas idejas</a> šim gadījumam.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Vēl nav pieejami viegli lietojami rīki torrentu filtrēšanai, bet mēs priecājamies par ieguldījumiem."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Kā jūs apstrādājat dublikātus torrentos?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Mēs cenšamies uzturēt minimālu dublēšanos vai pārklāšanos starp šajā sarakstā esošajiem torrentiem, bet tas ne vienmēr ir iespējams un lielā mērā ir atkarīgs no avota bibliotēku politikām. Bibliotēkām, kas izplata savus torrentus, tas ir ārpus mūsu kontroles. Torrentiem, ko izplata Annas Arhīvs, mēs dublējam tikai pēc MD5 hash, kas nozīmē, ka dažādas vienas un tās pašas grāmatas versijas netiek dublētas."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Vai es varu iegūt torrentu sarakstu kā JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Jā."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Es neredzu PDF vai EPUB failus torrentos, tikai bināros failus? Ko man darīt?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Šie patiesībā ir PDF un EPUB faili, tiem vienkārši nav paplašinājuma daudzos mūsu torrentos. Ir divas vietas, kur varat atrast torrentu failu metadatus, ieskaitot failu tipus/paplašinājumus:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Katrai kolekcijai vai izlaidumam ir savi metadati. Piemēram, <a %(a_libgen_nonfic)s>Libgen.rs torrenti</a> ir atbilstoša metadatu datubāze, kas tiek mitināta Libgen.rs vietnē. Mēs parasti saistāmies ar atbilstošiem metadatu resursiem no katras kolekcijas <a %(a_datasets)s>datu kopas lapas</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Mēs iesakām <a %(a_generate)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Šajās datubāzēs ir katra ieraksta kartējums Annas Arhīvā uz atbilstošajiem torrentu failiem (ja pieejami), zem “torrent_paths” ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Kāpēc mans torrentu klients nevar atvērt dažus no jūsu torrentu failiem / magnētu saites?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Daži torrentu klienti neatbalsta lielus gabalu izmērus, kas ir daudzos mūsu torrentos (jaunākajos mēs to vairs nedarām — pat ja tas ir derīgs pēc specifikācijām!). Tāpēc, ja saskaraties ar šo problēmu, izmēģiniet citu klientu vai sūdzieties sava torrentu klienta izstrādātājiem."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Vai jums ir atbildīgas atklāšanas programma?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Mēs aicinām drošības pētniekus meklēt ievainojamības mūsu sistēmās. Mēs esam lieli atbildīgas atklāšanas atbalstītāji. Sazinieties ar mums <a %(a_contact)s>šeit</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Pašlaik mēs nevaram piešķirt atlīdzības par kļūdu atklāšanu, izņemot ievainojamības, kurām ir <a %(a_link)s>potenciāls apdraudēt mūsu anonimitāti</a>, par kurām mēs piedāvājam atlīdzības diapazonā no $10k-50k. Mēs vēlētos nākotnē piedāvāt plašāku atlīdzību klāstu! Lūdzu, ņemiet vērā, ka sociālās inženierijas uzbrukumi ir ārpus darbības jomas."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Ja jūs interesē uzbrukuma drošība un vēlaties palīdzēt arhivēt pasaules zināšanas un kultūru, noteikti sazinieties ar mums. Ir daudz veidu, kā jūs varat palīdzēt."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Vai ir vairāk resursu par Annas Arhīvu?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Annas Blogs</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regulāri atjauninājumi"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Annas Programmatūra</a> — mūsu atvērtā koda programmatūra"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Tulkot Annas Programmatūru</a> — mūsu tulkošanas sistēma"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datu kopas</a> — par datiem"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatīvi domēni"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — vairāk par mums (lūdzu, palīdziet atjaunināt šo lapu vai izveidojiet to savā valodā!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Kā ziņot par autortiesību pārkāpumu?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Mēs šeit neuzglabājam nekādus ar autortiesībām aizsargātus materiālus. Mēs esam meklētājprogramma, un tādējādi indeksējam tikai metadatus, kas jau ir publiski pieejami. Lejupielādējot no šiem ārējiem avotiem, mēs iesakām pārbaudīt likumus jūsu jurisdikcijā attiecībā uz to, kas ir atļauts. Mēs neesam atbildīgi par citu personu mitināto saturu."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Ja jums ir sūdzības par to, ko redzat šeit, vislabāk ir sazināties ar oriģinālo vietni. Mēs regulāri atjauninām viņu izmaiņas mūsu datubāzē. Ja jūs patiešām uzskatāt, ka jums ir derīga DMCA sūdzība, uz kuru mums būtu jāreaģē, lūdzu, aizpildiet <a %(a_copyright)s>DMCA / Autortiesību prasības veidlapu</a>. Mēs ņemam jūsu sūdzības nopietni un atbildēsim pēc iespējas ātrāk."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Es ienīstu, kā jūs vadāt šo projektu!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Mēs arī vēlamies atgādināt visiem, ka viss mūsu kods un dati ir pilnīgi atvērtā koda. Tas ir unikāls projektiem, piemēram, mūsu — mēs nezinām nevienu citu projektu ar līdzīgi masīvu katalogu, kas arī ir pilnīgi atvērtā koda. Mēs ļoti priecājamies par ikvienu, kurš domā, ka mēs slikti vadām savu projektu, lai paņemtu mūsu kodu un datus un izveidotu savu ēnu bibliotēku! Mēs to nesakām no ļaunprātības vai kaut kā tamlīdzīga — mēs patiesi domājam, ka tas būtu lieliski, jo tas paaugstinātu latiņu visiem un labāk saglabātu cilvēces mantojumu."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Vai jums ir pieejamības monitors?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Lūdzu, skatiet <a %(a_href)s>šo lielisko projektu</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Kā es varu ziedot grāmatas vai citus fiziskus materiālus?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Lūdzu, sūtiet tos uz <a %(a_archive)s>Internet Archive</a>. Viņi tos pienācīgi saglabās."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Kas ir Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Jūs esat Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Kādas ir jūsu mīļākās grāmatas?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Šeit ir dažas grāmatas, kurām ir īpaša nozīme ēnu bibliotēku un digitālās saglabāšanas pasaulē:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Jūs esat izsmēlis ātro lejupielāžu limitu šodien."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Kļūstiet par biedru, lai izmantotu ātrās lejupielādes."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Tagad mēs atbalstām Amazon dāvanu kartes, kredītkartes un debetkartes, kriptovalūtu, Alipay un WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Pilna datubāze"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Grāmatas, raksti, žurnāli, komiksi, bibliotēku ieraksti, metadati, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Meklēt"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ir <a %(a_paused)s>apturējis</a> jaunu rakstu augšupielādi."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB ir Sci-Hub turpinājums."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Tieša piekļuve %(count)s akadēmiskajiem rakstiem"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Atvērts"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Ja esat <a %(a_member)s>biedrs</a>, pārlūkprogrammas verifikācija nav nepieciešama."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Ilgtermiņa arhīvs"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Datu kopas, kas izmantotas Annas Arhīvā, ir pilnībā atvērtas un var tikt masveidā spoguļotas, izmantojot torrentus. <a %(a_datasets)s>Uzziniet vairāk…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Jūs varat ļoti palīdzēt, sējot torrentus. <a %(a_torrents)s>Uzziniet vairāk…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s sējēji"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s sējēji"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s sējēji"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM apmācības dati"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Mums ir pasaulē lielākā augstas kvalitātes teksta datu kolekcija. <a %(a_llm)s>Uzziniet vairāk…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Spoguļi: aicinājums brīvprātīgajiem"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Meklējam brīvprātīgos"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Kā bezpeļņas, atvērtā koda projekts, mēs vienmēr meklējam cilvēkus, kas varētu palīdzēt."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Ja jūs vadāt augsta riska anonīmo maksājumu apstrādātāju, lūdzu, sazinieties ar mums. Mēs arī meklējam cilvēkus, kas vēlas izvietot gaumīgas mazas reklāmas. Visi ienākumi tiek novirzīti mūsu saglabāšanas centieniem."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Annas Blogs ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS lejupielādes"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Visas lejupielādes saites šim failam: <a %(a_main)s>Faila galvenā lapa</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Vārteja #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(jums var būt nepieciešams mēģināt vairākas reizes ar IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Lai iegūtu ātrākas lejupielādes un izlaistu pārlūkprogrammas pārbaudes, <a %(a_membership)s>kļūstiet par biedru</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Lai masveidā spoguļotu mūsu kolekciju, apskatiet <a %(a_datasets)s>Datu kopas</a> un <a %(a_torrents)s>Torrentus</a> lapas."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM dati"

#, fuzzy
msgid "page.llm.intro"
msgstr "Ir labi saprotams, ka LLM plaukst uz augstas kvalitātes datiem. Mums ir lielākā grāmatu, rakstu, žurnālu utt. kolekcija pasaulē, kas ir daži no augstākās kvalitātes teksta avotiem."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unikāls mērogs un diapazons"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Mūsu kolekcijā ir vairāk nekā simts miljoni failu, tostarp akadēmiskie žurnāli, mācību grāmatas un žurnāli. Mēs sasniedzam šo mērogu, apvienojot lielas esošās krātuves."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Dažas no mūsu avotu kolekcijām jau ir pieejamas lielapjomā (Sci-Hub un daļas no Libgen). Citus avotus mēs atbrīvojām paši. <a %(a_datasets)s>Datasets</a> parāda pilnu pārskatu."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Mūsu kolekcijā ir miljoniem grāmatu, rakstu un žurnālu no laika pirms e-grāmatu ēras. Lielas šīs kolekcijas daļas jau ir OCR’ētas un tām jau ir maz iekšējās pārklāšanās."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Kā mēs varam palīdzēt"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Mēs varam nodrošināt ātrgaitas piekļuvi mūsu pilnajām kolekcijām, kā arī neizlaistajām kolekcijām."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Šī ir uzņēmuma līmeņa piekļuve, ko mēs varam nodrošināt par ziedojumiem desmitiem tūkstošu USD apmērā. Mēs arī esam gatavi apmainīt to pret augstas kvalitātes kolekcijām, kuras mums vēl nav."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Mēs varam jums atmaksāt, ja jūs varat nodrošināt mums mūsu datu bagātināšanu, piemēram:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Pārklāšanās noņemšana (deduplikācija)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Teksta un metadatu izvilkšana"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Atbalstiet ilgtermiņa cilvēces zināšanu arhivēšanu, vienlaikus iegūstot labākus datus savam modelim!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Sazinieties ar mums</a>, lai apspriestu, kā mēs varam sadarboties."

#, fuzzy
msgid "page.login.continue"
msgstr "Turpināt"

#, fuzzy
msgid "page.login.please"
msgstr "Lūdzu, <a %(a_account)s>pieslēdzieties</a>, lai skatītu šo lapu.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Annas arhīvs uz laiku ir slēgts uzturēšanas darbiem. Lūdzu, atgriezieties pēc stundas."

#, fuzzy
msgid "page.metadata.header"
msgstr "Uzlabot metadatus"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Jūs varat palīdzēt grāmatu saglabāšanā, uzlabojot metadatus! Vispirms izlasiet informāciju par metadatiem Anna’s Archive, un tad uzziniet, kā uzlabot metadatus, saistot tos ar Open Library, un nopelniet bezmaksas dalību Anna’s Archive."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Fons"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Kad skatāties grāmatu Anna’s Archive, jūs varat redzēt dažādus laukus: nosaukumu, autoru, izdevēju, izdevumu, gadu, aprakstu, faila nosaukumu un daudz ko citu. Visa šī informācija tiek saukta par <em>metadatiem</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Tā kā mēs apvienojam grāmatas no dažādām <em>avotu bibliotēkām</em>, mēs parādām jebkādus metadatus, kas ir pieejami šajā avotu bibliotēkā. Piemēram, grāmatai, kuru mēs ieguvām no Library Genesis, mēs parādīsim nosaukumu no Library Genesis datubāzes."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Dažreiz grāmata ir pieejama <em>vairākās</em> avotu bibliotēkās, kurām var būt dažādi metadatu lauki. Šādā gadījumā mēs vienkārši parādām katra lauka garāko versiju, jo tā, cerams, satur visnoderīgāko informāciju! Mēs joprojām parādīsim citus laukus zem apraksta, piemēram, kā “alternatīvais nosaukums” (bet tikai tad, ja tie ir atšķirīgi)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Mēs arī izvelkam <em>kodējumus</em>, piemēram, identifikatorus un klasifikatorus no avotu bibliotēkas. <em>Identifikatori</em> unikāli pārstāv konkrētu grāmatas izdevumu; piemēri ir ISBN, DOI, Open Library ID, Google Books ID vai Amazon ID. <em>Klasifikatori</em> grupē kopā vairākas līdzīgas grāmatas; piemēri ir Dewey Decimal (DCC), UDC, LCC, RVK vai GOST. Dažreiz šie kodi ir skaidri saistīti avotu bibliotēkās, un dažreiz mēs varam tos izvilkt no faila nosaukuma vai apraksta (galvenokārt ISBN un DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Mēs varam izmantot identifikatorus, lai atrastu ierakstus <em>tikai metadatu kolekcijās</em>, piemēram, OpenLibrary, ISBNdb vai WorldCat/OCLC. Mūsu meklētājā ir īpaša <em>metadatu cilne</em>, ja vēlaties pārlūkot šīs kolekcijas. Mēs izmantojam atbilstošos ierakstus, lai aizpildītu trūkstošos metadatu laukus (piemēram, ja trūkst nosaukuma), vai piemēram, kā “alternatīvais nosaukums” (ja ir esošs nosaukums)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Lai redzētu, no kurienes tieši nāk grāmatas metadati, skatiet <em>“Tehniskās detaļas” cilni</em> grāmatas lapā. Tajā ir saite uz neapstrādāto JSON šai grāmatai, ar norādēm uz neapstrādāto JSON oriģinālajiem ierakstiem."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Lai iegūtu vairāk informācijas, skatiet šādas lapas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, un <a %(a_example)s>Example metadata JSON</a>. Visbeidzot, visi mūsu metadati var tikt <a %(a_generated)s>ģenerēti</a> vai <a %(a_downloaded)s>lejupielādēti</a> kā ElasticSearch un MariaDB datubāzes."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library saistīšana"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Tātad, ja sastopaties ar failu ar sliktiem metadatiem, kā to labot? Jūs varat doties uz avotu bibliotēku un sekot tās procedūrām metadatu labošanai, bet ko darīt, ja fails ir pieejams vairākās avotu bibliotēkās?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Anna’s Archive ir viens identifikators, kas tiek īpaši apstrādāts. <strong>Open Library annas_archive md5 lauks vienmēr pārsniedz visus citus metadatus!</strong> Vispirms atgriezīsimies un uzzināsim par Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library tika dibināta 2006. gadā Ārona Švarca vadībā ar mērķi “viena tīmekļa lapa katrai jebkad publicētai grāmatai”. Tā ir sava veida Wikipedia grāmatu metadatiem: ikviens var to rediģēt, tā ir brīvi licencēta un var tikt lejupielādēta masveidā. Tā ir grāmatu datubāze, kas visvairāk saskan ar mūsu misiju — patiesībā, Anna’s Archive ir iedvesmojusies no Ārona Švarca vīzijas un dzīves."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Tā vietā, lai izgudrotu riteni no jauna, mēs nolēmām novirzīt mūsu brīvprātīgos uz Open Library. Ja redzat grāmatu ar nepareiziem metadatiem, jūs varat palīdzēt šādi:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Dodieties uz <a %(a_openlib)s>Open Library vietni</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Atrodiet pareizo grāmatas ierakstu. <strong>BRĪDINĀJUMS:</strong> pārliecinieties, ka izvēlaties pareizo <strong>izdevumu</strong>. Open Library ir “darbi” un “izdevumi”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "“Darbs” varētu būt “Harijs Poters un Filozofu akmens”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "“Izdevums” varētu būt:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997. gada pirmais izdevums, ko publicējusi Bloomsbery, ar 256 lappusēm."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003. gada mīksto vāku izdevums, ko publicējusi Raincoast Books, ar 223 lappusēm."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000. gada poļu tulkojums “Harry Potter I Kamie Filozoficzn” no Media Rodzina ar 328 lappusēm."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Visiem šiem izdevumiem ir atšķirīgi ISBN un atšķirīgs saturs, tāpēc pārliecinieties, ka izvēlaties pareizo!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Rediģējiet ierakstu (vai izveidojiet to, ja tāda nav), un pievienojiet pēc iespējas vairāk noderīgas informācijas! Jūs jau esat šeit, tāpēc varat padarīt ierakstu patiešām lielisku."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sadaļā “ID numuri” izvēlieties “Annas Arhīvs” un pievienojiet grāmatas MD5 no Annas Arhīva. Tas ir garais burtu un ciparu virknējums pēc “/md5/” URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Mēģiniet atrast citus failus Annas Arhīvā, kas arī atbilst šim ierakstam, un pievienojiet tos. Nākotnē mēs varam grupēt tos kā dublikātus Annas Arhīva meklēšanas lapā."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Kad esat pabeidzis, pierakstiet URL, kuru tikko atjauninājāt. Kad esat atjauninājis vismaz 30 ierakstus ar Annas Arhīva MD5, nosūtiet mums <a %(a_contact)s>e-pastu</a> un nosūtiet mums sarakstu. Mēs jums piešķirsim bezmaksas dalību Annas Arhīvā, lai jūs varētu vieglāk veikt šo darbu (un kā pateicību par jūsu palīdzību). Šiem jābūt augstas kvalitātes labojumiem, kas pievieno ievērojamu daudzumu informācijas, pretējā gadījumā jūsu pieprasījums tiks noraidīts. Jūsu pieprasījums tiks arī noraidīts, ja kāds no labojumiem tiks atcelts vai labots Open Library moderatoru."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Ņemiet vērā, ka tas darbojas tikai grāmatām, nevis akadēmiskiem rakstiem vai citiem failu veidiem. Citu failu veidu gadījumā mēs joprojām iesakām atrast avota bibliotēku. Var paiet dažas nedēļas, līdz izmaiņas tiks iekļautas Annas Arhīvā, jo mums ir jālejupielādē jaunākais Open Library datu izgāztuve un jāatjauno mūsu meklēšanas indekss."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spoguļi: aicinājums brīvprātīgajiem"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Lai palielinātu Annas Arhīva noturību, mēs meklējam brīvprātīgos, kas vadītu spoguļus."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Mēs meklējam sekojošo:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Jūs pārvaldāt Anna’s Archive atvērtā koda bāzi un regulāri atjaunināt gan kodu, gan datus."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Jūsu versija ir skaidri atšķirama kā spogulis, piemēram, “Boba arhīvs, Anna’s Archive spogulis”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Jūs esat gatavs uzņemties ar šo darbu saistītos riskus, kas ir ievērojami. Jums ir dziļa izpratne par nepieciešamo operacionālo drošību. <a %(a_shadow)s>Šo</a> <a %(a_pirate)s>ierakstu</a> saturs jums ir pašsaprotams."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Jūs esat gatavs piedalīties mūsu <a %(a_codebase)s>koda bāzes</a> izstrādē — sadarbībā ar mūsu komandu — lai to īstenotu."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Sākotnēji mēs nedosim jums piekļuvi mūsu partneru serveru lejupielādēm, bet, ja viss noritēs labi, mēs varam to dalīties ar jums."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hostinga izdevumi"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Mēs esam gatavi segt hostinga un VPN izdevumus, sākotnēji līdz 200 USD mēnesī. Tas ir pietiekami pamata meklēšanas serverim un DMCA aizsargātam starpniekserverim."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Mēs maksāsim par hostingu tikai tad, kad viss būs uzstādīts un jūs būsiet pierādījis, ka spējat uzturēt arhīvu atjauninātu. Tas nozīmē, ka jums būs jāmaksā par pirmajiem 1-2 mēnešiem no savas kabatas."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Jūsu laiks netiks kompensēts (un arī mūsu nē), jo tas ir tīrs brīvprātīgais darbs."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Ja jūs būtiski iesaistīsieties mūsu darba izstrādē un darbībā, mēs varam apspriest lielāku ziedojumu ieņēmumu daļu dalīšanu ar jums, lai jūs varētu to izmantot pēc nepieciešamības."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Sākšana"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Lūdzu, <strong>nesazinieties ar mums</strong>, lai lūgtu atļauju vai uzdotu pamata jautājumus. Darbi runā skaļāk par vārdiem! Visa informācija ir pieejama, tāpēc vienkārši turpiniet ar sava spoguļa izveidi."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Jūtieties brīvi iesniegt biļetes vai apvienošanas pieprasījumus mūsu Gitlab, kad sastopaties ar problēmām. Mums var būt nepieciešams izveidot dažas spoguļiem specifiskas funkcijas kopā ar jums, piemēram, pārzīmološana no “Annas Arhīvs” uz jūsu vietnes nosaukumu, (sākotnēji) lietotāju kontu atspējošana vai saites uz mūsu galveno vietni no grāmatu lapām."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Kad jūsu spogulis darbojas, lūdzu, sazinieties ar mums. Mēs labprāt pārskatīsim jūsu OpSec, un, kad tas būs stabils, mēs saistīsimies ar jūsu spoguli un sāksim ciešāk sadarboties ar jums."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Paldies jau iepriekš visiem, kas ir gatavi šādā veidā piedalīties! Tas nav vājprātīgajiem, bet tas nostiprinātu lielākās patiesi atvērtās bibliotēkas cilvēces vēsturē ilgmūžību."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Lejupielādēt no partneru vietnes"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Lēnas lejupielādes ir pieejamas tikai oficiālajā vietnē. Apmeklējiet %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Lēnas lejupielādes nav pieejamas caur Cloudflare VPN vai no Cloudflare IP adresēm."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Lūdzu, uzgaidiet <span %(span_countdown)s>%(wait_seconds)s</span> sekundes, lai lejupielādētu šo failu."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Izmantojiet šo URL, lai lejupielādētu: <a %(a_download)s>Lejupielādēt tagad</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Paldies, ka gaidījāt, tas palīdz uzturēt vietni pieejamu bez maksas visiem! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Brīdinājums: pēdējo 24 stundu laikā no jūsu IP adreses ir bijušas daudzas lejupielādes. Lejupielādes var būt lēnākas nekā parasti."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Lejupielādes no jūsu IP adreses pēdējo 24 stundu laikā: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Ja izmantojat VPN, koplietotu interneta savienojumu vai jūsu ISP koplieto IP adreses, šis brīdinājums var būt saistīts ar to."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Lai ikvienam būtu iespēja lejupielādēt failus bez maksas, jums ir jāuzgaida, pirms varat lejupielādēt šo failu."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Jūtieties brīvi turpināt pārlūkot Annas arhīvu citā cilnē, kamēr gaidāt (ja jūsu pārlūkprogramma atbalsta cilņu atsvaidzināšanu fonā)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Jūtieties brīvi gaidīt, kamēr ielādējas vairākas lejupielādes lapas vienlaicīgi (bet, lūdzu, lejupielādējiet tikai vienu failu vienlaicīgi no katra servera)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Kad saņemat lejupielādes saiti, tā ir derīga vairākas stundas."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Annas arhīvs"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Ieraksts Annas arhīvā"

#, fuzzy
msgid "page.scidb.download"
msgstr "Lejupielādēt"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Lai atbalstītu cilvēka zināšanu pieejamību un ilgtermiņa saglabāšanu, kļūstiet par <a %(a_donate)s>biedru</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Kā bonuss, 🧬&nbsp;SciDB biedriem ielādējas ātrāk, bez jebkādiem ierobežojumiem."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Nestrādā? Mēģiniet <a %(a_refresh)s>atsvaidzināt</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Priekšskatījums vēl nav pieejams. Lejupielādējiet failu no <a %(a_path)s>Annas Arhīva</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB ir Sci-Hub turpinājums ar pazīstamu saskarni un tiešu PDF skatīšanu. Ievadiet savu DOI, lai skatītu."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Mums ir pilna Sci-Hub kolekcija, kā arī jauni raksti. Lielāko daļu var skatīt tieši ar pazīstamu saskarni, līdzīgu Sci-Hub. Dažus var lejupielādēt caur ārējiem avotiem, un šādos gadījumos mēs parādām saites uz tiem."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Meklēt"

#, fuzzy
msgid "page.search.title.new"
msgstr "Jauna meklēšana"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Iekļaut tikai"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Izslēgt"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nepārbaudīts"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Lejupielādēt"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Žurnālu raksti"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digitālā aizdošana"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadati"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Nosaukums, autors, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Meklēt"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Meklēšanas iestatījumi"

#, fuzzy
msgid "page.search.submit"
msgstr "Meklēt"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Meklēšana aizņēma pārāk ilgu laiku, kas ir raksturīgi plašiem vaicājumiem. Filtru skaits var nebūt precīzs."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Meklēšana aizņēma pārāk ilgu laiku, kas nozīmē, ka rezultāti var būt neprecīzi. Dažreiz <a %(a_reload)s>lapas pārlādēšana</a> palīdz."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Parādīt"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Saraksts"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabula"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Paplašināts"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Meklēšanas apraksti un metadatu komentāri"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Pievienot specifisku meklēšanas lauku"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(meklēt specifisku lauku)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Publicēšanas gads"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Saturs"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Faila tips"

#, fuzzy
msgid "page.search.more"
msgstr "vairāk…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Piekļuve"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Avots"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "iegūts un atvērts avots no AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Valoda"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Kārtot pēc"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Visatbilstošākais"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Jaunākie"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publicēšanas gads)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Vecākie"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Lielākie"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(faila izmērs)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Mazākie"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(atvērtais kods)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Nejauši"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Meklēšanas indekss tiek atjaunināts katru mēnesi. Pašlaik tas ietver ierakstus līdz %(last_data_refresh_date)s. Lai iegūtu vairāk tehniskas informācijas, skatiet %(link_open_tag)sdatu kopu lapu</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Lai izpētītu meklēšanas indeksu pēc kodiem, izmantojiet <a %(a_href)s>Kodu izpētītāju</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Ierakstiet lodziņā, lai meklētu mūsu katalogā %(count)s tieši lejupielādējamu failu, kurus mēs <a %(a_preserve)s>saglabājam uz visiem laikiem</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Patiesībā ikviens var palīdzēt saglabāt šos failus, sējot mūsu <a %(a_torrents)s>vienoto torrentu sarakstu</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Pašlaik mums ir pasaulē visaptverošākais atvērtais grāmatu, rakstu un citu rakstisku darbu katalogs. Mēs spoguļojam Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>un vairāk</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Ja atrodat citas “ēnu bibliotēkas”, kuras mums vajadzētu spoguļot, vai ja jums ir kādi jautājumi, lūdzu, sazinieties ar mums pa %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Par DMCA / autortiesību prasībām <a %(a_copyright)s>noklikšķiniet šeit</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Padoms: izmantojiet tastatūras īsinājumtaustiņus “/” (meklēšanas fokuss), “enter” (meklēšana), “j” (uz augšu), “k” (uz leju), “<” (iepriekšējā lapa), “>” (nākamā lapa) ātrākai navigācijai."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Meklējat rakstus?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Ierakstiet lodziņā, lai meklētu mūsu katalogā %(count)s akadēmiskos rakstus un žurnālu rakstus, kurus mēs <a %(a_preserve)s>saglabājam uz visiem laikiem</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Ierakstiet lodziņā, lai meklētu failus digitālajās aizdevumu bibliotēkās."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Šis meklēšanas indekss pašlaik ietver metadatus no Internet Archive kontrolētās digitālās aizdevumu bibliotēkas. <a %(a_datasets)s>Vairāk par mūsu datu kopām</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Lai iegūtu vairāk digitālo aizdevumu bibliotēku, skatiet <a %(a_wikipedia)s>Wikipedia</a> un <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Ierakstiet lodziņā, lai meklētu metadatus no bibliotēkām. Tas var būt noderīgi, kad <a %(a_request)s>pieprasāt failu</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Šis meklēšanas indekss pašlaik ietver metadatus no dažādiem metadatu avotiem. <a %(a_datasets)s>Vairāk par mūsu datu kopām</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Metadatiem mēs parādām oriģinālos ierakstus. Mēs neveicam ierakstu apvienošanu."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Ir daudz un dažādu metadatu avotu rakstītajiem darbiem visā pasaulē. <a %(a_wikipedia)s>Šī Wikipedia lapa</a> ir labs sākums, bet, ja zināt citus labus sarakstus, lūdzu, informējiet mūs."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Ierakstiet lodziņā, lai meklētu."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Šie ir metadatu ieraksti, <span %(classname)s>nevis</span> lejupielādējami faili."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Kļūda meklēšanas laikā."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Mēģiniet <a %(a_reload)s>pārlādēt lapu</a>. Ja problēma saglabājas, lūdzu, rakstiet mums uz %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Faili netika atrasti.</span> Mēģiniet mazāk vai citus meklēšanas terminus un filtrus."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Dažreiz tas notiek nepareizi, kad meklēšanas serveris ir lēns. Šādos gadījumos var palīdzēt <a %(a_attrs)s>pārlādēšana</a>."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Mēs esam atraduši atbilstības: %(in)s. Jūs varat atsaukties uz tur atrasto URL, kad <a %(a_request)s>pieprasāt failu</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Žurnālu raksti (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digitālā aizņemšanās (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadati (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Rezultāti %(from)s-%(to)s (%(total)s kopā)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ daļējas atbilstības"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d daļējas atbilstības"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Brīvprātīgo un atlīdzību"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive paļaujas uz tādiem brīvprātīgajiem kā jūs. Mēs sveicam visu līmeņu iesaistīšanos un meklējam palīdzību divās galvenajās kategorijās:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Neliels brīvprātīgais darbs:</span> ja varat atvēlēt tikai dažas stundas šur un tur, joprojām ir daudz veidu, kā varat palīdzēt. Mēs apbalvojam pastāvīgus brīvprātīgos ar <span %(bold)s>🤝 dalībām Annas Arhīvā</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Intensīvs brīvprātīgais darbs (USD$50-USD$5,000 atlīdzības):</span> ja varat veltīt daudz laika un/vai resursu mūsu misijai, mēs labprāt strādātu ciešāk ar jums. Galu galā jūs varat pievienoties iekšējai komandai. Lai gan mums ir ierobežots budžets, mēs varam piešķirt <span %(bold)s>💰 naudas atlīdzības</span> par visintensīvāko darbu."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Ja nevarat veltīt savu laiku brīvprātīgajam darbam, jūs joprojām varat mums daudz palīdzēt, <a %(a_donate)s>ziedojot naudu</a>, <a %(a_torrents)s>sējot mūsu torrentus</a>, <a %(a_uploading)s>augšupielādējot grāmatas</a> vai <a %(a_help)s>stāstot saviem draugiem par Annas Arhīvu</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Uzņēmumi:</span> mēs piedāvājam ātrgaitas tiešo piekļuvi mūsu kolekcijām apmaiņā pret uzņēmuma līmeņa ziedojumu vai apmaiņā pret jaunām kolekcijām (piemēram, jauniem skenējumiem, OCR datu kopām, mūsu datu bagātināšanu). <a %(a_contact)s>Sazinieties ar mums</a>, ja tas esat jūs. Skatiet arī mūsu <a %(a_llm)s>LLM lapu</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Viegls brīvprātīgais darbs"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Ja jums ir dažas brīvas stundas, jūs varat palīdzēt dažādos veidos. Noteikti pievienojieties <a %(a_telegram)s>brīvprātīgo čatam Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Kā pateicības zīmi mēs parasti piešķiram 6 mēnešus “Veiksmīgais Bibliotekārs” par pamata sasniegumiem un vairāk par turpmāko brīvprātīgo darbu. Visiem sasniegumiem ir nepieciešams augstas kvalitātes darbs — paviršs darbs mums kaitē vairāk nekā palīdz, un mēs to noraidīsim. Lūdzu, <a %(a_contact)s>rakstiet mums e-pastu</a>, kad sasniedzat kādu sasniegumu."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Uzdevums"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Sasniegums"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Izplatot ziņu par Annas Arhīvu. Piemēram, iesakot grāmatas AA, saistot ar mūsu emuāra ierakstiem vai vispārīgi novirzot cilvēkus uz mūsu vietni."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s saites vai ekrānuzņēmumi."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Šiem vajadzētu parādīt, ka jūs kādam pastāstāt par Annas Arhīvu, un viņi jums pateicas."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Uzlabot metadatus, <a %(a_metadata)s>saistot</a> ar Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Jūs varat izmantot <a %(a_list)s >nejaušo metadata problēmu sarakstu</a> kā sākumpunktu."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Pārliecinieties, ka atstājat komentāru par problēmām, kuras esat novērsis, lai citi nedublētu jūsu darbu."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s saites uz ierakstiem, kurus uzlabojāt."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Tulkot</a> vietni."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Pilnībā iztulkot valodu (ja tā nebija jau gandrīz pabeigta)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Uzlabot Annas Arhīva Wikipedia lapu jūsu valodā. Iekļaujiet informāciju no AA Wikipedia lapas citās valodās, kā arī no mūsu vietnes un bloga. Pievienojiet atsauces uz AA citās atbilstošās lapās."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Saites uz rediģēšanas vēsturi, kas parāda, ka esat veicis nozīmīgus ieguldījumus."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Izpildīt grāmatu (vai rakstu utt.) pieprasījumus Z-Library vai Library Genesis forumos. Mums nav pašiem sava grāmatu pieprasījumu sistēma, bet mēs spoguļojam šīs bibliotēkas, tāpēc to uzlabošana padara arī Annas Arhīvu labāku."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s saites vai ekrānuzņēmumi ar pieprasījumiem, kurus izpildījāt."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Nelieli uzdevumi, kas publicēti mūsu <a %(a_telegram)s>brīvprātīgo čatā Telegram</a>. Parasti par dalību, dažreiz par nelielām atlīdzībām."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Mazi uzdevumi, kas ievietoti mūsu brīvprātīgo tērzēšanas grupā."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Atkarīgs no uzdevuma."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Atlīdzības"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Mēs vienmēr meklējam cilvēkus ar stabilām programmēšanas vai uzbrukuma drošības prasmēm, lai iesaistītos. Jūs varat būtiski ietekmēt cilvēces mantojuma saglabāšanu."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Kā pateicību mēs piešķiram dalību par stabilu ieguldījumu. Kā lielu pateicību mēs piešķiram naudas atlīdzības par īpaši svarīgiem un sarežģītiem uzdevumiem. Tas nevajadzētu uzskatīt par darba aizvietotāju, bet tas ir papildu stimuls un var palīdzēt segt radušās izmaksas."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Lielākā daļa mūsu koda ir atvērtā pirmkoda, un mēs lūgsim arī jūsu kodu padarīt atvērtu, piešķirot atlīdzību. Ir dažas izņēmumi, kurus varam apspriest individuāli."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Atlīdzības tiek piešķirtas pirmajam, kurš pabeidz uzdevumu. Jūtieties brīvi komentēt atlīdzības biļeti, lai informētu citus, ka strādājat pie kaut kā, lai citi varētu atturēties vai sazināties ar jums, lai apvienotos. Bet esiet informēti, ka citi joprojām var strādāt pie tā un mēģināt jūs apsteigt. Tomēr mēs nepiešķiram atlīdzības par paviršu darbu. Ja divi augstas kvalitātes iesniegumi tiek veikti tuvu viens otram (vienas vai divu dienu laikā), mēs varam izvēlēties piešķirt atlīdzības abiem, pēc mūsu ieskatiem, piemēram, 100%% par pirmo iesniegumu un 50%% par otro iesniegumu (tātad kopā 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Lielākām atlīdzībām (īpaši datu iegūšanas atlīdzībām) lūdzu sazinieties ar mums, kad esat pabeidzis ~5%% no tā, un esat pārliecināts, ka jūsu metode būs piemērota pilnam mērķim. Jums būs jādalās ar savu metodi, lai mēs varētu sniegt atsauksmes. Arī šādā veidā mēs varam izlemt, ko darīt, ja vairāki cilvēki tuvojas atlīdzībai, piemēram, potenciāli piešķirt to vairākiem cilvēkiem, mudināt cilvēkus apvienoties utt."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "BRĪDINĀJUMS: augstas atlīdzības uzdevumi ir <span %(bold)s>grūti</span> — varētu būt prātīgi sākt ar vieglākiem."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Dodieties uz mūsu <a %(a_gitlab)s>Gitlab problēmu sarakstu</a> un kārtojiet pēc “Etiķetes prioritātes”. Tas aptuveni parāda uzdevumu secību, kas mums rūp. Uzdevumi bez skaidrām atlīdzībām joprojām ir piemēroti dalībai, īpaši tie, kas atzīmēti kā “Accepted” un “Annai mīļākie”. Jūs varētu vēlēties sākt ar “Sākuma projektu”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Jaunumi par <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, lielāko patiesi atvērto bibliotēku cilvēces vēsturē."

#, fuzzy
msgid "layout.index.title"
msgstr "Annas Arhīvs"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka. Spoguļo Sci-Hub, Library Genesis, Z-Library un citus."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Meklēt Annas Arhīvā"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annas Arhīvam ir nepieciešama jūsu palīdzība!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Daudzi mēģina mūs apturēt, bet mēs cīnāmies pretī."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Ja ziedosiet tagad, jūs saņemsiet <strong>dubultu</strong> ātro lejupielāžu skaitu."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Derīgs līdz šī mēneša beigām."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Ziedot"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Cilvēces zināšanu saglabāšana: lieliska dāvana svētkos!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Pārsteidziet mīļoto, uzdāviniet viņam kontu ar dalību."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Lai palielinātu Annas Arhīva noturību, mēs meklējam brīvprātīgos, kas varētu uzturēt spoguļus."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Ideāla Valentīndienas dāvana!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Mums ir pieejama jauna ziedošanas metode: %(method_name)s. Lūdzu, apsveriet %(donate_link_open_tag)sziedot</a> — šīs vietnes uzturēšana nav lēta, un jūsu ziedojums patiešām ir nozīmīgs. Liels paldies."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Mēs rīkojam līdzekļu vākšanu, lai <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">dublētu</a> lielāko komiksu ēnu bibliotēku pasaulē. Paldies par jūsu atbalstu! <a href=\"/donate\">Ziedot.</a> Ja nevarat ziedot, apsveriet iespēju atbalstīt mūs, pastāstot draugiem un sekojot mums <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> vai <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Nesenās lejupielādes:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Meklēt"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "BUJ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Uzlabot metadatus"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Brīvprātīgie un atlīdzības"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datu kopas"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrenti"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktivitāte"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Kodu izpēte"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM dati"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Sākums"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Annas Programmatūra ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Tulkot ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Pieteikties / Reģistrēties"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Konts"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Annas Arhīvs"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Sazinieties ar mums"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / autortiesību prasības"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Paplašināts"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Drošība"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatīvas"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nepiesaistīts"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Šim failam var būt problēmas."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Ātra lejupielāde"

#, fuzzy
msgid "page.donate.copy"
msgstr "kopēt"

#, fuzzy
msgid "page.donate.copied"
msgstr "nokopēts!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Iepriekšējais"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Nākamais"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "tikai šomēnes!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ir <a %(a_closed)s>apturējis</a> jaunu rakstu augšupielādi."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Izvēlieties maksājuma iespēju. Mēs piedāvājam atlaides kriptovalūtas maksājumiem %(bitcoin_icon)s, jo mums ir (daudz) mazākas maksas."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Izvēlieties maksājuma iespēju. Pašlaik mums ir tikai kriptovalūtas maksājumi %(bitcoin_icon)s, jo tradicionālie maksājumu apstrādātāji atsakās ar mums sadarboties."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Mēs nevaram tieši atbalstīt kredītkartes/debetkartes, jo bankas nevēlas ar mums sadarboties. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tomēr ir vairāki veidi, kā izmantot kredītkartes/debetkartes, izmantojot mūsu citas maksājumu metodes:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Lēnas un ārējas lejupielādes"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Lejupielādes"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Ja pirmo reizi izmantojat kriptovalūtu, mēs iesakām izmantot %(option1)s, %(option2)s vai %(option3)s, lai iegādātos un ziedotu Bitcoin (oriģinālo un visbiežāk izmantoto kriptovalūtu)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 saites uz ierakstiem, kurus esat uzlabojuši."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 saites vai ekrānuzņēmumi."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 saites vai ekrānuzņēmumi par izpildītajiem pieprasījumiem."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Ja jūs interesē šo datu kopumu spoguļošana <a %(a_faq)s>arhivēšanas</a> vai <a %(a_llm)s>LLM apmācības</a> nolūkos, lūdzu, sazinieties ar mums."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Ja jūs interesē šī datu kopuma spoguļošana <a %(a_archival)s>arhivēšanas</a> vai <a %(a_llm)s>LLM apmācības</a> nolūkos, lūdzu, sazinieties ar mums."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Galvenā mājaslapa"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN valsts informācija"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Ja jūs interesē šī datu kopuma spoguļošana <a %(a_archival)s>arhivēšanas</a> vai <a %(a_llm)s>LLM apmācības</a> nolūkos, lūdzu, sazinieties ar mums."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Starptautiskā ISBN aģentūra regulāri izplata diapazonus, kurus tā ir piešķīrusi nacionālajām ISBN aģentūrām. No tā mēs varam noteikt, kurai valstij, reģionam vai valodu grupai šis ISBN pieder. Pašlaik mēs izmantojam šos datus netieši, izmantojot <a %(a_isbnlib)s>isbnlib</a> Python bibliotēku."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resursi"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Pēdējoreiz atjaunināts: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN vietne"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadati"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Izslēdzot “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Mūsu iedvesma metadatu vākšanai ir Ārona Švarca mērķis “viena tīmekļa lapa katrai jebkad publicētai grāmatai”, kurai viņš izveidoja <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Šis projekts ir veiksmīgs, bet mūsu unikālā pozīcija ļauj mums iegūt metadatus, kurus viņi nevar."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Vēl viena iedvesma bija mūsu vēlme zināt <a %(a_blog)s>cik daudz grāmatu ir pasaulē</a>, lai mēs varētu aprēķināt, cik daudz grāmatu mums vēl ir jāglābj."

#~ msgid "page.partner_download.text1"
#~ msgstr "Lai dotu iespēju visiem bez maksas lejupielādēt failus, jums jāgaida <strong>%(wait_seconds)s sekundes</strong> pirms varat lejupielādēt šo failu."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automātiski atsvaidzināt lapu. Ja jūs nokavējat lejupielādes logu, taimeris tiks restartēts, tāpēc ieteicams automātiski atsvaidzināt."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Lejupielādēt tagad"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvertēt: izmantojiet tiešsaistes rīkus, lai konvertētu starp formātiem. Piemēram, lai konvertētu starp epub un pdf, izmantojiet <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: lejupielādējiet failu (atbalstīti ir pdf vai epub), pēc tam <a %(a_kindle)s>nosūtiet to uz Kindle</a> izmantojot tīmekli, lietotni vai e-pastu. Noderīgi rīki: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Atbalstiet autorus: Ja jums tas patīk un varat to atļauties, apsveriet iespēju iegādāties oriģinālu vai tieši atbalstīt autorus."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Atbalstiet bibliotēkas: Ja tas ir pieejams jūsu vietējā bibliotēkā, apsveriet iespēju to aizņemties tur bez maksas."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nav pieejams tieši lielapjomā, tikai daļēji lielapjomā aiz maksas sienas"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annas Arhīvs pārvalda <a %(isbndb)s>ISBNdb metadatu</a> kolekciju"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb ir uzņēmums, kas apkopo dažādu tiešsaistes grāmatnīcu datus, lai atrastu ISBN metadatus. Annas Arhīvs ir veicis ISBNdb grāmatu metadatu dublējumus. Šie metadati ir pieejami caur Annas Arhīvu (pašlaik ne meklēšanā, izņemot, ja jūs skaidri meklējat ISBN numuru)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Tehniskās detaļas skatiet zemāk. Kādā brīdī mēs varam to izmantot, lai noteiktu, kuras grāmatas joprojām trūkst no ēnu bibliotēkām, lai prioritizētu, kuras grāmatas atrast un/vai skenēt."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Mūsu emuāra ieraksts par šiem datiem"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb apkopošana"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Pašlaik mums ir viens torrents, kas satur 4,4GB saspiestu <a %(a_jsonl)s>JSON Lines</a> failu (20GB nesaspiests): “isbndb_2022_09.jsonl.gz”. Lai importētu “.jsonl” failu PostgreSQL, jūs varat izmantot kaut ko līdzīgu <a %(a_script)s>šim skriptam</a>. Jūs pat varat to tieši caurulēt, izmantojot kaut ko līdzīgu %(example_code)s, lai tas dekompresētu uzreiz."

#~ msgid "page.donate.wait"
#~ msgstr "Lūdzu, uzgaidiet vismaz <span %(span_hours)s>divas stundas</span> (un atsvaidziniet šo lapu) pirms sazināties ar mums."

#~ msgid "page.codes.search_archive"
#~ msgstr "Meklēt Annas Arhīvā “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Ziedot, izmantojot Alipay vai WeChat. Jūs varat izvēlēties starp šiem nākamajā lapā."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Izplatīt ziņu par Annas Arhīvu sociālajos tīklos un tiešsaistes forumos, iesakot grāmatas vai sarakstus AA, vai atbildot uz jautājumiem."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Daiļliteratūras kolekcija ir atšķirīga, bet joprojām ir <a %(libgenli)s>torrenti</a>, lai gan nav atjaunināti kopš 2022. gada (mums ir tiešās lejupielādes)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annas Arhīvs un Libgen.li kopīgi pārvalda <a %(comics)s>komiksu</a> un <a %(magazines)s>žurnālu</a> kolekcijas."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nav torrentu krievu daiļliteratūras un standarta dokumentu kolekcijām."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Papildu saturam nav pieejami torrenti. Torrenti, kas atrodas Libgen.li vietnē, ir citu šeit uzskaitīto torrentu spoguļi. Vienīgais izņēmums ir daiļliteratūras torrenti, sākot no %(fiction_starting_point)s. Komiksu un žurnālu torrenti tiek izlaisti sadarbībā starp Annas Arhīvu un Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "No kolekcijas <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> precīza izcelsme nav skaidra. Daļēji no the-eye.eu, daļēji no citiem avotiem."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

