��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b [  �d Q  �f ,   Dh &  qh   �i �  �k t  �m (  	o [   2p n   �p F   �p d   Dq �  �q Z  Es �   �t �  �u �   pw �  dx   Jz �  S| �   M~ �   �  � ?   �� �   � X   ǃ �   �    �    6� F   W� %   ��    Ĉ    ؈ A   � !   0�    R� 1   f�    �� *   ��    � 8   �� 7  3�   k� U   |� 9   Ҍ 	   �    �    "�    8�    D�    a� 	   r�    |�    �� #   ��    ��    �� 	   ԍ 
   ލ    �    ��     �    6� H  P� r  �� �   � ,   �� B  ֒ �   � $  �� �   ԕ    �� �   ��    7� �   V� #   � )  0�    Z� �  y� 0   �� S   /�    �� -  �� I  �� �   � @  ��    �� M   � Z   ]� �   �� +   o� *   �� v   Ƣ =   =� Q   {� -   ͣ    �� r    � �  s� 5   '� u   ]� �   Ӧ L   �� *  ާ C   	� &  M� �   t� �   � T   �� �   � �   ׬ F   �� �   Э �   �� S   s� A   ǯ x   	�    �� �   �� 
  1� F   <�    �� �   �� �   '�    � {  � �   ��   '� w  +� �   �� 
  ��   �� Q   �� G   �� O   ?� n   �� k   �� M   j� 0   �� }   � Q   g� B   ��    �� l   � N   y� �   �� �   ��    �   /�   5� N  <� 5  �� �   �� �   F� =  *� �   h� �   "� �   �� v  E� �   �� �   Z� 	   >� �   H� �   �� �   �� q  Q� y   �� �   =� �   �� �  v� �   7�   ��   ��    �� \   �� �   Y� b   �� x  M� �   �� a   y� o   ��    K� #  j� �   �� w  b�    �� k   �� �   _� �   ��   �� �   �� �  �� �  �� )   �� o  �� �  �   �� �  ��   ��   �� U   �� �   �    �� �   �� -  �� g  �� �   !� �   � �   � 
   �� s  �� O  1�   �� 	   �� �   �� 8  N  �   �   w �   ~ Z   B 
   � �  � M  0    ~ �   �   ~	   �
 �   � }  �
 [   � P   [ �   � 1   �    �    � �   � +  � 0   ( F   Y �  � �   { q    e   z �  � $   c    � �  � �   + �   � T   ] �   � 1   ?   q �   �    y    � /   � <   � @     <   D  ^   �  �   �     p! 4   �! A   �! J   " )   N" E   x" (   �"    �"    g#    l#   �# 2  �$ �   �%   �& �   �' �   L( +  ()    T* �   f* �   =+ �   �+ `  y, q  �- �  L/ �  C1 �  3 F   �4 ?   �4 $   5 �   :5 
  �5 �  8 5  �9 e  �:   b<   i= 
  }? 1   �@ (  �@ G  �A U  +C    �D �   �D ]  }E �   �F 7  oG �   �H    �I     �I ]   �I 	   J �  &J �  �L Q  8N �   �P �   @Q    .R �   =R    -S F   .T r   uT   �T   �U �   W w  �W l   
Y *  zY    �Z (  �Z �   �\ B  �] �  �_ �  vb �   jd    (e �   4e �  ,f    �g b  h �  hi �  7k �  1m �   �n >  �o F   &q �  mq �   2s {   �s 5   dt H   �t    �t B   �t "   ,u ,   Ou +   |u    �u �   �u �  `v �  By 1  @{ �   r}    S~ �  [~ >  0� d  o� m  Ԅ Y  B� c  ��     � �   �    �    � �  � F  �   3� b  C�    �� �  �� �  �� �  A�   %� �  8� b  1� e  ��    �� �   � m   � �  W� �  B� �   � �   �� �   4� W  �    :� �   S� L   &� T   s� #   ȭ /   � I   � M  f� �  �� �  T� <   � �  N� �   � 	   �� �   �� <   N� �   �� �   � ,   ٺ b   �    i� F  |� ^  ü �  "� �  �� �   ��    � c   �   ��    �� �   �� �  #� �   �� 7   Z� 0   �� g  �� ,   +� q  X� u  �� �  @�     � ~   @� �   �� E  S� �  ��    5� �   D� -   B� �   p� �   (� �   � �   � L   �� ,   +� �   X� 2   U� �  �� Q  &� �   x� �   T� [   K� @   �� �   �� <  �� �  �� �  C� �  4� 5   � �   7�   � 4  (� �   ]� �  =� �  � ,   �� �  �� )   �� �  !� G   ��    =�   S� �  c� �  V� �   �� +  ��   �   %  �   ; �  � �  � t   m )  � Z   K   g �   � x   4
    �
    �
 6  �
 �    )   � "  � �  � *  �   � �  � �   x �   5  �    � �   �    � �  � "   C    f    �    � .   �    �    �    �            + 
   8    C    K    T 	   r '   |    �    �    � 
   � �   � p   � %   
     3  0   ?  4   p  A   �  5   �  %   ! 
   C!    Q!    b!    ~!    �!    �! 	   �!    �!    �!    �! (   �!     "    >"    T" !   r" ,   �" +   �"    �" #   # 3   &#    Z# Y   q# B   �#    $ l   $ B   �$    �$    �$ !   �$    %    .%    C%    ^%    x%    �%    �%    �%    �%    �% 	   �% 
   �%    �% %   �%    &    $& 	   -&    7& 	   O& &   Y&    �&    �& 	   �&    �&    �&    �& -   �&    �&    '    8'    @' 	   V'    `' &   r'    �'    �'    �'    �'    �' 	   �'    �' 	   (    (    2( o   F( �   �( d   l) �   �) �  �* �   _, 
   �,    �,    -    #- 
   *-    8-    P- 2   _- X   �- :   �- \   &. $   �. >   �. .   �. \   / i   s/ �   �/ e   �0 <   1     W1    x1    �1    �1 
   �1    �1    �1    �1    �1 
   �1    �1    �1    2    2    2    .2    72    T2    c2    v2 	   ~2    �2 
   �2    �2    �2   �2    �3    �3     4 &   4    -4 F   44 f   {4 +   �4 +   5 -   :5    h5    p5    x5 y   {5    �5 
   �5 3   	6 z   =6    �6 
   �6 �   �6 $   d7 E  �7 S   �: �   #; �   �; �   S< 2   )=   \= �   l> =  ? �   @@    /A 
   HA P   VA G   �A ^   �A ]   NB _   �B E   C ^   RC $   �C 0   �C    D    D O   +D $   {D    �D    �D    �D    �D z   �D    `E /   rE D   �E    �E    �E X   F _   hF �  �F    dH    �H �   �H    GI    PI    YI    bI    wI +   ~I S  �I    �J    K    .K    =K "  EK #   hL 
   �L    �L w   �L    M    M *   &M    QM    hM #   mM �   �M    "N    >N W   NN    �N    �N 	   �N    �N 5   �N X   3O    �O ?   �O �   �O d   �P P   �P    KQ   \Q K   tR    �R -   �R    S �   S �   �S    �T P   �T �   �T �   �U    ZV #   vV    �V �  �V '   FX     nX    �X !   �X    �X �   �X #   �Y    �Y '   �Y ;   �Y    /Z !   7Z !   YZ &   {Z Z  �Z {  �\   y^ N   {` 6   �`    a 6   a $  Ea �   jb �   .c "   �c �   �c �  �d %   nf    �f �  �f A  �h    �i    �i ;   j    Dj ;  Lj    �k b  �k �  m �   �n    �o y   �o 5   -p    cp [   �p %  �p �   r �   �r �  �s q   Bu   �u N   �v �   w �   �w H   Yx �   �x 0   Sy )   �y    �y    �y    �y !   �y    �y J   z +   cz 	   �z )   �z   �z *   �{ �   
| �   �| �   y} "   ~    .~    L~    g~ %   z~     �~ %   �~ "   �~ �   
 %   � �   �     �   ـ �   �� t   '�   �� �   �� 1   � �   �� 	   5�   ?� t   K�    �� �  Ն    ��    ��    ��     '   ؈     �    � ;   � �   J� �   �� 	   ڊ    �    � �   � B  �� �   �� ~   ��    2�    E�    X�    m�    ��    ��    �� N   �� 7   �� �  5� U   �    b� f   u� g   ܑ G   D� f   �� C   � J   7�    �� b   �� A   � �   -� B   �� D   �    7� �   S� l   F� C   �� q   �� T   i� @   ��    �� ;   � o   D� �   �� @   9� �   z�    ,�   2� H   J� W   �� �   �    v� �  �� �   [�    &�    9� 
   S�    ^�   f� +   u� d   �� �   � �   ֡ �   �� �   8� �   
� �   �� �   d� e   +� "  �� p   �� �  %� �   �� �   `� 
   N� 
   \� 
   j� �   x� 
   � 
   %� W   3� ^   �� �   � 
   ˭ p   ٭ @   J� u   �� >   � �   @� n   ǯ 
   6� �   D� 
   װ 
   � 
   � P  � �   R� �  ۳    �� 	   ��    �� �   ��    r� '   �� �   �� �   �� *   X�    ��    �� 9   �� 8   � -    � !   N� !   p� �   �� '   t� �  �� �   8� �   /� N   �� �   
�   �� �  ��   �� �   �� �   b� ~   ��    p� Z  �� %   �� i  � �   r� #  Q� x   u� Q  �� �   @� �   � �   �� ,   � B   L�    �� -   ��    ��    ��    �� �   �    �� j   � $   m�    ��    �� 
   ��     �� z   �� P   M� L   ��   �� *   �� 
   �    $�    +� (   G�    p�    �� 
   ��    �� 
   ��    �� 
   ��    �� )   �� �   ��    ��    ��    ��    ��    ��    ��    ��    �    �    8� %   L� l   r�    �� 3   �� [   &� �   �� �   .� �   �� J  �� �   �� (  �� 
   �� �   �� 9   p� H   �� T   �� �   H� �   �� M   �� r   �    �� `   �� k   �� �   k�     �     �    (�    =�    V� "   c�    �� *   ��    ��    ��    �� "   ��    �    6�    M�    k�    r�    ��    ��    ��    �� (   �� (   �� �   � �   �� "   "� #   E� j   i� ]   �� u   2� 4   �� 3   �� 4   � >   F� L   �� 1   �� �   � 	  �� )  ��    �� ?   � �   W� &   �� �   � �   � �   �� F   [�    �� �   �� �   i� s   � /   y� k   �� �   �    �� /   �    6� <   R� s   �� p   �    t�    |�    �� '   �� �   �� 9   E� $   � 3   �� '   ��     � #   � D   <�     �� f   �� >   	�   H� R   `� M   �� �   � ?   �� $   ��     � $   -� #   R� $   v� #   �� $   �� >   �� D   #� J  h� P   �� =   � B   B� (   ��    �� v   �� s   ,� �   �� �   j� 	   ,� �   6� %   ��    �� �        �  #   �  ?    2   N Z   � Y   � T   6 $   � &   � �   � 9   ~ $   � Q   �   / =   8 �   v g   � g   ` �   � (   V 5    j   �      b   ; "   � �   � 4   z	 B   �	    �	 $   
 �   0
 P    N   T �   � [   < P   � �   � �   u
    �
    �
 C    P   U    �    �    �    �     -    �   > m   �    = "   W +   z (   � <   � �    <   � x   � c   G &   � y   � �   L .   @ S   o    � K   � =   #    a z   u x   � 4   i P   �    � 7     S   8    � t   � &       < &   Q o   x �   � N   �    �     N   .    } c   � D   � �   > �   �    i B   } �   � %   o �   � P   < $   � Q   �                        &   -  >   T  P   �     �     ! 	   ! &   ! ?   E! ?   �!    �! Q   �! I   "    h"    ~" 0   �"    �"    �" `   �" �   Z# [   W$    �$    �$ �   �$ Z  m% Y   �&    "' x  3' �  �( )   H* m   r* &   �* R  + *   Z- y   �-    �-    . �  ,.    �/ `   0 �   v0 x   1 Y   �1    �1 ^   �1 <   W2 !   �2 y   �2 R   03 /   �3 X   �3 )   4 Q   64 �   �4 �   
5 /   �5 �   6 �  �6 �   L8 /   �8 `  .9 �   �: �   �; 2  `< �   �= $   R> !   w> �   �> ,   a? �  �? k   �A ?   �A    9B    HB �  bB    D �   $D 1  �D #  F ,  CG B   pH X   �H _   I -   lI .   �I O   �I _   J *   yJ .   �J 8   �J "   K    /K ;   EK k   �K 7   �K 	   %L i   /L �   �L �   xM    N    1N    AN J   IN _   �N U   �N �   JO j   P    {P -   �P �   �P 
   �Q �   �Q �  \R �   U W   V (   jV    �V    �V    �V F   �V 3   �V z   !W �   �W X   �X    �X    �X &   	Y    0Y d   OY    �Y C   �Y 	   Z 9   Z ,   OZ    |Z 	   �Z [   �Z    �Z    �Z ,   [    F[    e[ n   i[ �   �[ a   �\ l   
] U   z] �   �]    �^    �^ �   �^ �   r_ �   .` 	   �` �   �` R   ~a H   �a l   b c   �b X   �b    Dc \   ac    �c    �c    �c    d    #d    Bd    [d    td    �d    �d -   �d -   �d -   �d     -e 2   Ne +   �e &   �e    �e    �e =   f    Cf 	   cf B   mf +   �f `   �f 1   =g    og    �g .   �g )   �g    �g t   h d   �h v   �h   `i    bj    zj    �j    �j A   �j 	   
k    k    ,k �   Dk    �k    �k    l 
   l 	   l 3   !l    Ul   ql    wm    �m    �m #   �m !   �m    n "   n $   Bn (   gn /   �n ,   �n    �n h   �n +   ]o    �o    �o -   �o S   �o    5p ,   Qp "   ~p u   �p b   q B   zq    �q    �q 	   �q    �q    r    r �  :r �   �s    bt    vt %   zt    �t &   �t    �t    �t {   �t L   `u �   �u    uv #   �v �   �v #   -w    Qw "   qw %   �w /   �w %   �w    x    +x 4   7x     lx (   �x 4   �x 0  �x L   z J   iz S   �z     { �   ){ %   �{    �{ I   �{    <| (   P|    y| =   �| L   �| &   $} �   K}    �}    ~    ~    6~    N~    e~    ~    �~    �~ [   �~   '    4�    T� �   s� �   Z�    � !   0�    R�    e�    u�    ��    ��    ��    т    �    ��    � 
   �    $�    3�    E�    R�    ^� ,   n� �   ��   �� {  ��   � �  %� �   ܊   ΋    N� �   S�    M� �   _� �   @� �  #� �   Ց p  �� 7   �� �   5� @   �    .� J   K� U   �� d   � v   Q� �   Ȗ �   �� �   7� a  �    Q� �   k� �   4� [   � �   m�    � �   +� �  � �   �� �   T� 	   � `   %� l   �� �   � �   ٣ d   e� �   ʤ    R� (   l�    �� C   �� -   ��    #� �   2� G   �� �   �� "   �� �   �� �   <� C   � T   +� S   �� Y   ԩ \   .� Z   �� w   � k   ^� �   ʫ �   Z� 	   ެ 9   � ,   "� ^   O� 5   �� 	   �    � J   ��    B�    T�    i� 8   q� 4   �� B   ߮    "�    :�    G�    V� 	   \� `   f� x   ǯ Y   @� :   ��    հ '   ݰ ,   �    2� 
   F� 	   T�    ^� 	   e�    o�    x� 
   �    ��    �� 	   ��    �� 	   ��    Ʊ    ֱ    �    ��    �    �    �    � %   ?�    e� ~   ��    � b   � �   y�    I�    R�    a� 
   p� 	   {�    ��    �� �   �� �   H� Q   ҵ    $�    8� n   U�    Ķ w   ׶ �   O� $   ߷    � �   � �   �� V   g� �   �� r   �� ,   �� �   !�    ��    ֻ P   �� �   G� #   � �   � �   �� �   2� T   �    m�    ��    ��    ��    ��    ſ    ο    � �   �� v   �� �   	� �   �� �   q� m   e� S   �� �  '� ~  �    �� �   �� +  ?� o  k�    �� �   �� *  �� �   �� �  �� �  � }   �� q  V�    �� ?   �� �   &� �  �� M   E� �   �� 
   ��    ��    �� �   �� G   K� q   �� 1   � ]   7� A   �� a   �� &   9� �   `� @   �� %   <� Y   b� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: lv
Language-Team: lv <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library ir populāra (un nelegāla) bibliotēka. Viņi ir paņēmuši Library Genesis kolekciju un padarījuši to viegli meklējamu. Turklāt viņi ir kļuvuši ļoti efektīvi jaunu grāmatu iegūšanā, motivējot lietotājus ar dažādām priekšrocībām. Pašlaik viņi neatgriež šīs jaunās grāmatas atpakaļ Library Genesis. Un atšķirībā no Library Genesis, viņi nepadara savu kolekciju viegli spoguļojamu, kas kavē plašu saglabāšanu. Tas ir svarīgi viņu biznesa modelim, jo viņi iekasē naudu par piekļuvi savai kolekcijai lielos apjomos (vairāk nekā 10 grāmatas dienā). Mēs neveicam morālus spriedumus par naudas iekasēšanu par masveida piekļuvi nelikumīgai grāmatu kolekcijai. Nav šaubu, ka Z-Library ir veiksmīgi paplašinājusi piekļuvi zināšanām un nodrošinājusi vairāk grāmatu. Mēs esam šeit, lai veiktu savu daļu: nodrošināt šīs privātās kolekcijas ilgtermiņa saglabāšanu. - Anna un komanda (<a %(reddit)s>Reddit</a>) Sākotnējā Pirātu bibliotēkas spoguļa izlaidumā (REDIĢĒTS: pārvietots uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>), mēs izveidojām Z-Library spoguli, lielu nelegālu grāmatu kolekciju. Kā atgādinājums, tas ir tas, ko mēs rakstījām tajā sākotnējā emuāra ierakstā: Šī kolekcija datēta ar 2021. gada vidu. Tikmēr Z-Library ir augusi satriecošā tempā: viņi ir pievienojuši apmēram 3,8 miljonus jaunu grāmatu. Protams, tur ir daži dublikāti, bet lielākā daļa no tām šķiet likumīgi jaunas grāmatas vai augstākas kvalitātes iepriekš iesniegto grāmatu skenējumi. Tas lielā mērā ir saistīts ar palielināto brīvprātīgo moderatoru skaitu Z-Library un viņu masveida augšupielādes sistēmu ar dublēšanas novēršanu. Mēs vēlamies viņus apsveikt ar šiem sasniegumiem. Mēs ar prieku paziņojam, ka esam ieguvuši visas grāmatas, kas tika pievienotas Z-Library starp mūsu pēdējo spoguli un 2022. gada augustu. Mēs arī esam atgriezušies un savākuši dažas grāmatas, kuras pirmo reizi palaidām garām. Kopumā šī jaunā kolekcija ir apmēram 24TB, kas ir daudz lielāka nekā iepriekšējā (7TB). Mūsu spogulis tagad ir kopā 31TB. Atkal mēs veicām dublēšanas novēršanu pret Library Genesis, jo šai kolekcijai jau ir pieejami torenti. Lūdzu, dodieties uz Pirātu bibliotēkas spoguli, lai apskatītu jauno kolekciju (REDIĢĒT: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>). Tur ir vairāk informācijas par to, kā faili ir strukturēti un kas ir mainījies kopš pēdējās reizes. Mēs to nesaistīsim no šejienes, jo šī ir tikai emuāra vietne, kas neuztur nelikumīgus materiālus. Protams, sēšana ir arī lielisks veids, kā mums palīdzēt. Paldies visiem, kas sēj mūsu iepriekšējo torentu komplektu. Mēs esam pateicīgi par pozitīvo atsaucību un priecīgi, ka ir tik daudz cilvēku, kuri rūpējas par zināšanu un kultūras saglabāšanu šādā neparastā veidā. 3x jaunas grāmatas pievienotas Pirātu bibliotēkas spogulim (+24TB, 3,8 miljoni grāmatu) Izlasiet pavadošos rakstus no TorrentFreak: <a %(torrentfreak)s>pirmais</a>, <a %(torrentfreak_2)s>otrais</a> - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) pavadošie raksti no TorrentFreak: <a %(torrentfreak)s>pirmais</a>, <a %(torrentfreak_2)s>otrais</a> Ne tik sen, "ēnu bibliotēkas" bija izzūdošas. Sci-Hub, milzīgā nelegālā akadēmisko rakstu arhīva, bija pārtraucis pieņemt jaunus darbus tiesas prāvu dēļ. "Z-Library", lielākā nelegālā grāmatu bibliotēka, redzēja, kā tās it kā radītāji tika arestēti par kriminālām autortiesību apsūdzībām. Viņi neticami spēja izbēgt no aresta, bet viņu bibliotēka joprojām ir apdraudēta. Dažas valstis jau veic šādu versiju. TorrentFreak <a %(torrentfreak)s>ziņoja</a>, ka Ķīna un Japāna ir ieviesušas AI izņēmumus savos autortiesību likumos. Mums nav skaidrs, kā tas mijiedarbojas ar starptautiskajiem līgumiem, bet tas noteikti dod aizsardzību viņu vietējiem uzņēmumiem, kas izskaidro to, ko mēs esam redzējuši. Kas attiecas uz Annas Arhīvu — mēs turpināsim savu pazemes darbu, kas balstīts uz morālo pārliecību. Tomēr mūsu lielākā vēlme ir iznākt gaismā un likumīgi pastiprināt mūsu ietekmi. Lūdzu, reformējiet autortiesības. Kad Z-Library saskārās ar slēgšanu, es jau biju dublējis visu tās bibliotēku un meklēju platformu, kur to izvietot. Tas bija mans motivācija sākt Annas Arhīvu: turpinājums misijai, kas bija aiz šīm agrākajām iniciatīvām. Kopš tā laika mēs esam izauguši par lielāko ēnu bibliotēku pasaulē, kas uzņem vairāk nekā 140 miljonus ar autortiesībām aizsargātu tekstu dažādos formātos — grāmatas, akadēmiskie raksti, žurnāli, avīzes un vēl vairāk. Mana komanda un es esam ideologi. Mēs uzskatām, ka šo failu saglabāšana un izvietošana ir morāli pareiza. Bibliotēkas visā pasaulē saskaras ar finansējuma samazinājumiem, un mēs nevaram uzticēt cilvēces mantojumu korporācijām. Tad nāca AI. Praktiski visi lielie uzņēmumi, kas veido LLM, sazinājās ar mums, lai apmācītu uz mūsu datiem. Lielākā daļa (bet ne visi!) ASV bāzētie uzņēmumi pārdomāja, kad saprata mūsu darba nelegālo raksturu. Savukārt Ķīnas uzņēmumi entuziastiski pieņēma mūsu kolekciju, acīmredzot neuztraucoties par tās likumību. Tas ir ievērojami, ņemot vērā Ķīnas lomu kā parakstītājvalstij gandrīz visos lielākajos starptautiskajos autortiesību līgumos. Mēs esam devuši ātrgaitas piekļuvi apmēram 30 uzņēmumiem. Lielākā daļa no tiem ir LLM uzņēmumi, un daži ir datu brokeri, kas pārdos mūsu kolekciju tālāk. Lielākā daļa ir ķīnieši, lai gan mēs esam strādājuši arī ar uzņēmumiem no ASV, Eiropas, Krievijas, Dienvidkorejas un Japānas. DeepSeek <a %(arxiv)s>atzina</a>, ka agrākā versija tika apmācīta uz daļas no mūsu kolekcijas, lai gan viņi ir ļoti noslēpumaini par savu jaunāko modeli (iespējams, arī apmācīts uz mūsu datiem). Ja Rietumi vēlas palikt priekšā LLM un galu galā AGI sacensībās, tiem ir jāpārskata sava nostāja par autortiesībām, un drīz. Neatkarīgi no tā, vai jūs piekrītat mums vai nē par mūsu morālo lietu, tas tagad kļūst par ekonomikas un pat nacionālās drošības jautājumu. Visi varas bloki veido mākslīgos superzinātniekus, superhakerus un supermilitāristus. Informācijas brīvība kļūst par izdzīvošanas jautājumu šīm valstīm — pat par nacionālās drošības jautājumu. Mūsu komanda ir no visas pasaules, un mums nav īpašas piederības. Bet mēs mudinātu valstis ar stingriem autortiesību likumiem izmantot šo eksistenciālo draudu, lai tos reformētu. Tātad, ko darīt? Mūsu pirmais ieteikums ir vienkāršs: saīsināt autortiesību termiņu. ASV autortiesības tiek piešķirtas uz 70 gadiem pēc autora nāves. Tas ir absurds. Mēs varam to saskaņot ar patentiem, kas tiek piešķirti uz 20 gadiem pēc iesniegšanas. Tas būtu vairāk nekā pietiekami daudz laika, lai grāmatu, rakstu, mūzikas, mākslas un citu radošo darbu autori saņemtu pilnu atlīdzību par saviem centieniem (ieskaitot ilgtermiņa projektus, piemēram, filmu adaptācijas). Tad, vismaz, politikas veidotājiem būtu jāiekļauj izņēmumi masveida tekstu saglabāšanai un izplatīšanai. Ja galvenās bažas ir zaudētie ieņēmumi no individuāliem klientiem, personīgā līmeņa izplatīšana varētu palikt aizliegta. Savukārt tie, kas spēj pārvaldīt plašas krātuves — uzņēmumi, kas apmāca LLM, kopā ar bibliotēkām un citām arhīvām — būtu iekļauti šajos izņēmumos. Autortiesību reforma ir nepieciešama nacionālajai drošībai Īsumā: Ķīnas LLM (ieskaitot DeepSeek) tiek apmācīti uz manas nelegālās grāmatu un rakstu arhīva — lielākā pasaulē. Rietumiem ir nepieciešams pārskatīt autortiesību likumus nacionālās drošības vārdā. Lūdzu, skatiet <a %(all_isbns)s>oriģinālo emuāra ierakstu</a> papildu informācijai. Mēs izvirzījām izaicinājumu to uzlabot. Mēs piešķirtu pirmās vietas balvu $6,000, otrās vietas $3,000 un trešās vietas $1,000. Pateicoties milzīgajai atsaucībai un neticamajiem iesniegumiem, mēs nolēmām nedaudz palielināt balvu fondu un piešķirt četras trešās vietas, katru $500 vērtībā. Uzvarētāji ir zemāk, bet noteikti apskatiet visus iesniegumus <a %(annas_archive)s>šeit</a>, vai lejupielādējiet mūsu <a %(a_2025_01_isbn_visualization_files)s>apvienoto torrentu</a>. Pirmā vieta $6,000: phiresky Šis <a %(phiresky_github)s>iesniegums</a> (<a %(annas_archive_note_2951)s>Gitlab komentārs</a>) ir vienkārši viss, ko mēs vēlējāmies, un vēl vairāk! Mums īpaši patika neticami elastīgās vizualizācijas iespējas (pat atbalstot pielāgotus shaderus), bet ar visaptverošu iepriekš iestatītu sarakstu. Mums arī patika, cik ātri un gludi viss darbojas, vienkāršā ieviešana (kas pat nav ar backend), gudrā minimapa un plašais skaidrojums viņu <a %(phiresky_github)s>emuāra ierakstā</a>. Neticams darbs un pelnīta uzvara! - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Mūsu sirdis ir pilnas ar pateicību. Ievērojamas idejas Debesskrāpji retumam Daudz slīdņu datu kopu salīdzināšanai, it kā jūs būtu DJ. Mēroga josla ar grāmatu skaitu. Skaistas etiķetes. Forša noklusējuma krāsu shēma un siltumkarte. Unikāls kartes skats un filtri Anotācijas un arī tiešraides statistika Tiešraides statistika Dažas idejas un realizācijas, kas mums īpaši patika: Mēs varētu turpināt vēl ilgi, bet apstāsimies šeit. Noteikti apskatiet visus iesniegumus <a %(annas_archive)s>šeit</a>, vai lejupielādējiet mūsu <a %(a_2025_01_isbn_visualization_files)s>apvienoto torrentu</a>. Tik daudz iesniegumu, un katrs sniedz unikālu perspektīvu, vai nu UI, vai realizācijā. Mēs vismaz iekļausim pirmās vietas iesniegumu mūsu galvenajā vietnē, un varbūt arī dažus citus. Mēs arī esam sākuši domāt par to, kā organizēt procesu, lai identificētu, apstiprinātu un pēc tam arhivētu retākās grāmatas. Vairāk informācijas sekos. Paldies visiem, kas piedalījās. Ir pārsteidzoši, ka tik daudziem cilvēkiem rūp. Viegla datu kopu pārslēgšana ātrai salīdzināšanai. Visi ISBN CADAL SSNOs CERLALC datu noplūde DuXiu SSIDs EBSCOhost e-grāmatu indekss Google Grāmatas Goodreads Interneta Arhīvs ISBNdb ISBN Globālais Izdevēju Reģistrs Libby Faili Annas Arhīvā Nexus/STC OCLC/Worldcat OpenLibrary Krievijas Valsts Bibliotēka Trantoras Impērijas bibliotēka Otrā vieta $3,000: hypha "Lai gan perfekti kvadrāti un taisnstūri ir matemātiski patīkami, tie nenodrošina pārāku lokalitāti kartēšanas kontekstā. Es uzskatu, ka asimetrija, kas piemīt šiem Hilberta vai klasiskajiem Mortona, nav trūkums, bet gan iezīme. Tāpat kā Itālijas slavenā zābaka formas kontūra padara to uzreiz atpazīstamu kartē, šo līkņu unikālās "īpatnības" var kalpot kā kognitīvie orientieri. Šī atšķirība var uzlabot telpisko atmiņu un palīdzēt lietotājiem orientēties, iespējams, padarot vieglāku konkrētu reģionu atrašanu vai rakstu pamanīšanu." Vēl viens neticams <a %(annas_archive_note_2913)s>iesniegums</a>. Ne tik elastīgs kā pirmās vietas, bet mums patiesībā patika tā makro līmeņa vizualizācija vairāk nekā pirmās vietas (telpas aizpildīšanas līkne, robežas, marķēšana, izcelšana, panoramēšana un tālummaiņa). <a %(annas_archive_note_2971)s>Komentārs</a> no Joe Davis mūs uzrunāja: Un joprojām daudz iespēju vizualizācijai un renderēšanai, kā arī neticami gluds un intuitīvs lietotāja interfeiss. Stabils otrais vietas ieguvējs! - Anna un komanda (<a %(reddit)s>Reddit</a>) Pirms dažiem mēnešiem mēs izsludinājām <a %(all_isbns)s>$10,000 balvu</a> par labāko iespējamo mūsu datu vizualizāciju, kas parāda ISBN telpu. Mēs uzsvērām, ka jāparāda, kuri faili mums jau ir/nav arhivēti, un vēlāk pievienojām datu kopu, kas apraksta, cik daudz bibliotēku glabā ISBN (retuma mērs). Mēs esam pārsteigti par atsaucību. Ir bijis tik daudz radošuma. Liels paldies visiem, kas piedalījās: jūsu enerģija un entuziasms ir lipīgi! Galu galā mēs vēlējāmies atbildēt uz šādiem jautājumiem: <strong>kādas grāmatas pastāv pasaulē, cik daudz mēs jau esam arhivējuši, un uz kurām grāmatām mums vajadzētu koncentrēties nākamreiz?</strong> Ir lieliski redzēt, ka tik daudziem cilvēkiem rūp šie jautājumi. Mēs sākām ar pamata vizualizāciju paši. Mazāk nekā 300kb, šis attēls kodolīgi attēlo lielāko pilnībā atvērto "grāmatu sarakstu", kas jebkad ir izveidots cilvēces vēsturē: Trešā vieta $500 #1: maxlion Šajā <a %(annas_archive_note_2940)s>iesniegumā</a> mums ļoti patika dažādi skati, īpaši salīdzināšanas un izdevēju skati. Trešā vieta $500 #2: abetusk Lai gan ne vispolētākais lietotāja interfeiss, šis <a %(annas_archive_note_2917)s>iesniegums</a> atbilst daudzām prasībām. Mums īpaši patika tā salīdzināšanas funkcija. Trešā vieta $500 #3: conundrumer0 Tāpat kā pirmās vietas ieguvējs, šis <a %(annas_archive_note_2975)s>iesniegums</a> mūs pārsteidza ar savu elastību. Galu galā tas ir tas, kas padara lielisku vizualizācijas rīku: maksimāla elastība jaudīgiem lietotājiem, vienlaikus saglabājot vienkāršību vidējiem lietotājiem. Trešā vieta $500 #4: charelf Pēdējais <a %(annas_archive_note_2947)s>iesniegums</a>, kas saņem balvu, ir diezgan vienkāršs, bet tam ir dažas unikālas funkcijas, kas mums ļoti patika. Mums patika, kā viņi parāda, cik daudz datu kopu aptver konkrētu ISBN kā popularitātes/uzticamības mēru. Mums arī ļoti patika vienkāršība, bet efektivitāte, izmantojot necaurredzamības slīdni salīdzinājumiem. $10,000 ISBN vizualizācijas balvas uzvarētāji Īsumā: Mēs saņēmām neticamus iesniegumus $10,000 ISBN vizualizācijas balvai. Fons Kā Annas Arhīvs var sasniegt savu misiju - dublēt visu cilvēces zināšanu, nezinot, kuras grāmatas vēl ir pieejamas? Mums ir nepieciešams TODO saraksts. Viens veids, kā to izveidot, ir caur ISBN numuriem, kas kopš 1970. gadiem ir piešķirti katrai publicētajai grāmatai (vairumā valstu). Nav centrālās iestādes, kas zinātu visus ISBN piešķīrumus. Tā vietā tas ir izplatīts sistēma, kur valstis saņem numuru diapazonus, kas pēc tam piešķir mazākus diapazonus lieliem izdevējiem, kuri var vēl vairāk sadalīt diapazonus mazākiem izdevējiem. Beidzot individuālie numuri tiek piešķirti grāmatām. Mēs sākām kartēt ISBN <a %(blog)s>pirms diviem gadiem</a> ar mūsu ISBNdb datu ieguvi. Kopš tā laika mēs esam ieguvuši daudz vairāk metadatu avotu, piemēram, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby un citus. Pilnu sarakstu var atrast Annas Arhīva lapās "Datasets" un "Torrents". Tagad mums ir vislielākā pilnībā atvērta, viegli lejupielādējama grāmatu metadatu (un tādējādi ISBN) kolekcija pasaulē. Mēs esam <a %(blog)s>plaši rakstījuši</a> par to, kāpēc mums rūp saglabāšana un kāpēc mēs pašlaik atrodamies kritiskā logā. Mums tagad ir jāidentificē retas, nepietiekami fokusētas un unikāli apdraudētas grāmatas un jāsaglabā tās. Labi metadati par visām pasaules grāmatām palīdz to izdarīt. $10,000 atlīdzība Liela uzmanība tiks pievērsta lietojamībai un tam, cik labi tas izskatās. Parādot faktisko metadatu atsevišķiem ISBN, pietuvinot, piemēram, nosaukumu un autoru. Labāka telpas aizpildīšanas līkne. Piemēram, zigzags, kas pirmajā rindā iet no 0 līdz 4 un pēc tam atpakaļ (reversā) no 5 līdz 9 otrajā rindā — rekursīvi piemērots. Dažādas vai pielāgojamas krāsu shēmas. Īpaši skati datu kopu salīdzināšanai. Veidi, kā novērst problēmas, piemēram, citu metadatu, kas nesaskan labi (piemēram, ļoti atšķirīgi nosaukumi). Attēlu anotēšana ar komentāriem par ISBN vai diapazoniem. Jebkādi heuristikas paņēmieni retu vai apdraudētu grāmatu identificēšanai. Jebkādas radošas idejas, ko varat izdomāt! Kods Kods šo attēlu ģenerēšanai, kā arī citi piemēri, ir atrodami <a %(annas_archive)s>šajā direktorijā</a>. Mēs izveidojām kompaktu datu formātu, ar kuru visa nepieciešamā ISBN informācija ir aptuveni 75MB (saspiesta). Datu formāta apraksts un kods tā ģenerēšanai ir atrodams <a %(annas_archive_l1244_1319)s>šeit</a>. Atlīdzībai jums nav nepieciešams to izmantot, bet tas, iespējams, ir visērtākais formāts, lai sāktu. Jūs varat pārveidot mūsu metadatus, kā vēlaties (lai gan visam jūsu kodam jābūt atvērtā koda). Mēs nevaram sagaidīt, ko jūs izdomāsiet. Veiksmi! Forkojiet šo repo un rediģējiet šo bloga ieraksta HTML (nav atļauti citi backend, izņemot mūsu Flask backend). Padariet iepriekš minēto attēlu gludi pietuvināmu, lai jūs varētu pietuvināt līdz atsevišķiem ISBN. Noklikšķinot uz ISBN, jāved uz metadatu lapu vai meklēšanu Annas Arhīvā. Jums joprojām jāspēj pārslēgties starp visiem dažādiem datu kopumiem. Valstu diapazoni un izdevēju diapazoni jāizceļ, kad uz tiem uzved. Jūs varat izmantot, piemēram, <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> valstu informācijai un mūsu "isbngrp" datu ieguvi izdevējiem (<a %(annas_archive)s>datu kopums</a>, <a %(annas_archive_2)s>torrents</a>). Tam jādarbojas labi gan uz galddatoriem, gan mobilajām ierīcēm. Šeit ir daudz ko izpētīt, tāpēc mēs paziņojam atlīdzību par iepriekš minētās vizualizācijas uzlabošanu. Atšķirībā no lielākās daļas mūsu atlīdzību, šī ir laika ierobežota. Jums ir jāiesniedz <a %(annas_archive)s>jūsu atvērtā koda</a> līdz 2025-01-31 (23:59 UTC). Labākā iesniegšana saņems $6,000, otrā vieta ir $3,000, un trešā vieta ir $1,000. Visas atlīdzības tiks piešķirtas, izmantojot Monero (XMR). Zemāk ir minimālie kritēriji. Ja neviens iesniegums neatbilst kritērijiem, mēs joprojām varam piešķirt dažas atlīdzības, bet tas būs pēc mūsu ieskatiem. Par bonusa punktiem (tās ir tikai idejas — ļaujiet savai radošumam izpausties): Jūs VARAT pilnībā novirzīties no minimālajiem kritērijiem un veikt pilnīgi atšķirīgu vizualizāciju. Ja tā ir patiešām iespaidīga, tad tā kvalificējas atlīdzībai, bet pēc mūsu ieskatiem. Iesniedziet savus darbus, pievienojot komentāru <a %(annas_archive)s>šim jautājumam</a> ar saiti uz jūsu dakšoto repozitoriju, apvienošanas pieprasījumu vai atšķirību. - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Šis attēls ir 1000×800 pikseļi. Katrs pikselis pārstāv 2,500 ISBN. Ja mums ir fails ISBN, mēs padarām šo pikseli zaļāku. Ja mēs zinām, ka ISBN ir izsniegts, bet mums nav atbilstoša faila, mēs padarām to sarkanāku. Mazāk nekā 300kb, šis attēls kodolīgi pārstāv lielāko pilnībā atvērto “grāmatu sarakstu”, kas jebkad ir izveidots cilvēces vēsturē (daži simti GB pilnībā saspiesti). Tas arī parāda: vēl ir daudz darba, lai dublētu grāmatas (mums ir tikai 16%%). Vizualizējot visus ISBN — $10,000 atlīdzība līdz 2025-01-31 Šis attēls pārstāv lielāko pilnībā atvērto “grāmatu sarakstu”, kas jebkad ir izveidots cilvēces vēsturē. Vizualizācija Papildus pārskata attēlam mēs varam arī aplūkot atsevišķus iegūtos datu kopumus. Izmantojiet nolaižamo izvēlni un pogas, lai pārslēgtos starp tiem. Šajos attēlos ir daudz interesantu rakstu, ko redzēt. Kāpēc ir kāda līniju un bloku regularitāte, kas šķiet notiek dažādos mērogos? Kas ir tukšās zonas? Kāpēc daži datu kopumi ir tik blīvi? Mēs atstāsim šos jautājumus lasītājam kā uzdevumu. - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Secinājums Ar šo standartu mēs varam veikt izlaidumus pakāpeniskāk un vieglāk pievienot jaunus datu avotus. Mums jau ir daži aizraujoši izlaidumi gaidāmi! Mēs arī ceram, ka citiem ēnu bibliotēkām būs vieglāk spoguļot mūsu kolekcijas. Galu galā, mūsu mērķis ir saglabāt cilvēces zināšanas un kultūru uz visiem laikiem, tāpēc jo vairāk redundances, jo labāk. Piemērs Apskatīsim mūsu neseno Z-Library izlaidumu kā piemēru. Tas sastāv no divām kolekcijām: “<span style="background: #fffaa3">zlib3_records</span>” un “<span style="background: #ffd6fe">zlib3_files</span>”. Tas ļauj mums atsevišķi iegūt un izlaist metadatu ierakstus no faktiskajiem grāmatu failiem. Tādējādi mēs izlaidām divus torrentus ar metadatu failiem: Mēs arī izlaidām vairākus torrentus ar bināro datu mapēm, bet tikai “<span style="background: #ffd6fe">zlib3_files</span>” kolekcijai, kopā 62: Izpildot <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, mēs varam redzēt, kas ir iekšā: Šajā gadījumā tas ir grāmatas metadati, kā ziņo Z-Library. Augstākajā līmenī mums ir tikai “aacid” un “metadata”, bet nav “data_folder”, jo nav atbilstošu bināro datu. AACID satur “22430000” kā primāro ID, ko mēs varam redzēt, ir ņemts no “zlibrary_id”. Mēs varam sagaidīt, ka citi AAC šajā kolekcijā būs ar tādu pašu struktūru. Tagad izpildīsim <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Šis ir daudz mazāks AAC metadati, lai gan lielākā daļa šī AAC atrodas citur binārā failā! Galu galā, šoreiz mums ir “data_folder”, tāpēc mēs varam sagaidīt, ka atbilstošie binārie dati atrodas <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” satur “zlibrary_id”, tāpēc mēs varam viegli to saistīt ar atbilstošo AAC “zlib_records” kolekcijā. Mēs varējām saistīt dažādos veidos, piemēram, caur AACID — standarts to nenosaka. Ņemiet vērā, ka nav nepieciešams, lai “metadata” lauks pats par sevi būtu JSON. Tas varētu būt virkne, kas satur XML vai jebkuru citu datu formātu. Jūs pat varētu glabāt metadatu informāciju saistītajā binārajā blobā, piemēram, ja tas ir daudz datu. Heterogēni faili un metadata, cik vien iespējams tuvu oriģinālajam formātam. Bināros datus var tieši apkalpot tīmekļa serveri, piemēram, Nginx. Heterogēni identifikatori avota bibliotēkās vai pat identifikatoru trūkums. Atsevišķi metadatu un failu datu izlaidumi vai tikai metadatu izlaidumi (piemēram, mūsu ISBNdb izlaidums). Izplatīšana caur torrentiem, lai gan ar iespēju izmantot citas izplatīšanas metodes (piemēram, IPFS). Nemainīgi ieraksti, jo mums jāpieņem, ka mūsu torrenti dzīvos mūžīgi. Pakāpeniski izlaidumi / pievienojami izlaidumi. Mašīnlasāmi un rakstāmi, ērti un ātri, īpaši mūsu stekam (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Daļēji viegla cilvēka pārbaude, lai gan tas ir sekundārs mašīnlasāmībai. Viegli sēt mūsu kolekcijas ar standarta nomātu sēklu kastīti. Dizaina mērķi Mums nerūp, vai faili ir viegli navigējami manuāli uz diska vai meklējami bez iepriekšējas apstrādes. Mums nerūp, vai tie ir tieši saderīgi ar esošo bibliotēku programmatūru. Lai gan ikvienam vajadzētu būt viegli sēt mūsu kolekciju, izmantojot torrentus, mēs negaidām, ka faili būs izmantojami bez ievērojamām tehniskām zināšanām un apņemšanās. Mūsu galvenais lietošanas gadījums ir failu un saistīto metadatu izplatīšana no dažādām esošajām kolekcijām. Mūsu svarīgākie apsvērumi ir: Daži ne-mērķi: Tā kā Annas Arhīvs ir atvērtā koda, mēs vēlamies tieši izmantot mūsu formātu. Kad mēs atsvaidzinām savu meklēšanas indeksu, mēs piekļūstam tikai publiski pieejamiem ceļiem, lai ikviens, kas atzaros mūsu bibliotēku, varētu ātri sākt darbu. <strong>AAC.</strong> AAC (Annai Arhīva Konteiners) ir viens elements, kas sastāv no <strong>metadata</strong> un, pēc izvēles, <strong>bināriem datiem</strong>, abi ir nemainīgi. Tam ir globāli unikāls identifikators, ko sauc par <strong>AACID</strong>. <strong>AACID.</strong> AACID formāts ir šāds: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Piemēram, faktiskais AACID, ko mēs esam izlaiduši, ir <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID diapazons.</strong> Tā kā AACID satur monotoniski pieaugošus laika zīmogus, mēs varam to izmantot, lai norādītu diapazonus konkrētā kolekcijā. Mēs izmantojam šo formātu: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kur laika zīmogi ir iekļauti. Tas ir saskaņā ar ISO 8601 notāciju. Diapazoni ir nepārtraukti un var pārklāties, bet pārklāšanās gadījumā tiem jāietver identiski ieraksti kā iepriekš izlaistajā kolekcijā (jo AAC ir nemainīgi). Trūkstoši ieraksti nav atļauti. <code>{collection}</code>: kolekcijas nosaukums, kas var saturēt ASCII burtus, ciparus un pasvītras (bet ne dubultās pasvītras). <code>{collection-specific ID}</code>: kolekcijai specifisks identifikators, ja piemērojams, piemēram, Z-Library ID. Var tikt izlaists vai saīsināts. Jāizlaiž vai jāsaīsina, ja AACID citādi pārsniegtu 150 rakstzīmes. <code>{ISO 8601 timestamp}</code>: īsa ISO 8601 versija, vienmēr UTC, piemēram, <code>20220723T194746Z</code>. Šim skaitlim ir jāpalielinās monotoniski katrā izlaidumā, lai gan tā precīza semantika var atšķirties atkarībā no kolekcijas. Mēs iesakām izmantot skrāpēšanas vai ID ģenerēšanas laiku. <code>{shortuuid}</code>: UUID, bet saspiests līdz ASCII, piemēram, izmantojot base57. Pašlaik mēs izmantojam <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python bibliotēku. <strong>Bināro datu mape.</strong> Mape ar AAC diapazona binārajiem datiem vienai konkrētai kolekcijai. Tiem ir šādas īpašības: Direktorijam jāietver datu faili visiem AAC noteiktajā diapazonā. Katram datu failam jābūt ar savu AACID kā faila nosaukumu (bez paplašinājumiem). Direktorijas nosaukumam jābūt AACID diapazonam, kam priekšā ir <code style="color: green">annas_archive_data__</code>, un bez sufiksa. Piemēram, viens no mūsu faktiskiem izlaidumiem ir direktorija ar nosaukumu<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Ieteicams padarīt šīs mapes pārvaldāmas izmēra ziņā, piemēram, ne lielākas par 100GB-1TB katra, lai gan šis ieteikums laika gaitā var mainīties. <strong>Kolekcija.</strong> Katrs AAC pieder kolekcijai, kas pēc definīcijas ir semantiski konsekventu AAC saraksts. Tas nozīmē, ka, ja jūs veicat būtiskas izmaiņas metadatu formātā, jums ir jāizveido jauna kolekcija. Standarts <strong>Metadatu fails.</strong> Metadatu fails satur AAC diapazona metadatus vienai konkrētai kolekcijai. Tiem ir šādas īpašības: <code>data_folder</code> ir pēc izvēles, un tas ir bināro datu mapes nosaukums, kas satur atbilstošos bināros datus. Atbilstošo bināro datu faila nosaukums šajā mapē ir ieraksta AACID. Katram JSON objektam augstākajā līmenī jāietver šādi lauki: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (pēc izvēles). Citi lauki nav atļauti. Faila nosaukumam jābūt AACID diapazonam, kam priekšā ir <code style="color: red">annas_archive_meta__</code> un kam seko <code>.jsonl.zstd</code>. Piemēram, viens no mūsu izlaidumiem ir nosaukts<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kā norāda faila paplašinājums, faila tips ir <a %(jsonlines)s>JSON Lines</a>, saspiests ar <a %(zstd)s>Zstandard</a>. <code>metadata</code> ir patvaļīgi metadati, atbilstoši kolekcijas semantikai. Tam jābūt semantiski konsekventam kolekcijas ietvaros. <code style="color: red">annas_archive_meta__</code> prefikss var tikt pielāgots jūsu iestādes nosaukumam, piemēram, <code style="color: red">my_institute_meta__</code>. <strong>“ierakstu” un “failu” kolekcijas.</strong> Pēc konvencijas bieži ir ērti izlaist “ierakstus” un “failus” kā atsevišķas kolekcijas, lai tās varētu izlaist dažādos grafikos, piemēram, balstoties uz skrāpēšanas ātrumu. “Ieraksts” ir tikai metadatu kolekcija, kas satur informāciju, piemēram, grāmatu nosaukumus, autorus, ISBN utt., savukārt “faili” ir kolekcijas, kas satur pašus failus (pdf, epub). Galu galā mēs izvēlējāmies salīdzinoši vienkāršu standartu. Tas ir diezgan brīvs, nenormatīvs un vēl tiek pilnveidots. <strong>Torrenti.</strong> Metadatu faili un bināro datu mapes var tikt apvienotas torrentos, ar vienu torrentu uz metadatu failu vai vienu torrentu uz bināro datu mapi. Torrentiem jābūt ar oriģinālo faila/mapes nosaukumu plus <code>.torrent</code> sufiksu kā faila nosaukumu. <a %(wikipedia_annas_archive)s>Annas Arhīvs</a> ir kļuvis par lielāko ēnu bibliotēku pasaulē, un vienīgo ēnu bibliotēku šādā mērogā, kas ir pilnībā atvērtā koda un atvērto datu. Zemāk ir tabula no mūsu Datu kopu lapas (nedaudz modificēta): Mēs to paveicām trīs veidos: Esošo atvērto datu ēnu bibliotēku spoguļošana (piemēram, Sci-Hub un Library Genesis). Palīdzot ēnu bibliotēkām, kas vēlas būt atvērtākas, bet kurām nebija laika vai resursu to darīt (piemēram, Libgen komiksu kolekcija). Tīmekļa bibliotēku skrāpēšana, kas nevēlas dalīties lielos apjomos (piemēram, Z-Library). Attiecībā uz (2) un (3) mēs tagad paši pārvaldām ievērojamu torrentu kolekciju (simtiem TB). Līdz šim mēs esam piegājuši šīm kolekcijām kā vienreizējiem projektiem, kas nozīmē pielāgotu infrastruktūru un datu organizāciju katrai kolekcijai. Tas pievieno ievērojamu slogu katram izlaidumam un padara īpaši grūti veikt vairāk pakāpeniskus izlaidumus. Tāpēc mēs nolēmām standartizēt savus izlaidumus. Šis ir tehnisks emuāra ieraksts, kurā mēs iepazīstinām ar mūsu standartu: <strong>Annas Arhīva Konteineri</strong>. Annas Arhīva Konteineri (AAC): pasaules lielākās ēnu bibliotēkas izlaidumu standartizēšana Annas Arhīvs ir kļuvis par lielāko ēnu bibliotēku pasaulē, kas prasa mums standartizēt mūsu izlaidumus. 300GB+ grāmatu vāku izlaisti Visbeidzot, mēs ar prieku paziņojam par nelielu izlaidumu. Sadarbībā ar cilvēkiem, kas darbojas Libgen.rs atzarā, mēs dalāmies ar visiem viņu grāmatu vākiem, izmantojot torrentus un IPFS. Tas izplatīs slodzi, skatoties vākus starp vairākām mašīnām, un tos labāk saglabās. Daudzos (bet ne visos) gadījumos grāmatu vāki ir iekļauti pašos failos, tāpēc tas ir sava veida “atvasināti dati”. Bet to iekļaušana IPFS joprojām ir ļoti noderīga gan Annas Arhīva, gan dažādu Library Genesis atzaru ikdienas darbībai. Kā parasti, jūs varat atrast šo izlaidumu Pirate Library Mirror (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>). Mēs šeit uz to nesniegsim saiti, bet jūs to varat viegli atrast. Cerams, ka mēs varam nedaudz atslābināt savu tempu, tagad, kad mums ir pienācīga alternatīva Z-Library. Šī darba slodze nav īpaši ilgtspējīga. Ja jūs interesē palīdzēt ar programmēšanu, serveru darbību vai saglabāšanas darbu, noteikti sazinieties ar mums. Vēl ir daudz <a %(annas_archive)s>darba, kas jāveic</a>. Paldies par jūsu interesi un atbalstu. Pāreja uz ElasticSearch Daži vaicājumi aizņēma ļoti ilgu laiku, līdz pat tam, ka tie aizņēma visas atvērtās savienojumus. Pēc noklusējuma MySQL ir minimālais vārda garums, vai arī jūsu indekss var kļūt ļoti liels. Cilvēki ziņoja, ka nevar meklēt “Ben Hur”. Meklēšana bija tikai nedaudz ātra, kad pilnībā ielādēta atmiņā, kas prasīja mums iegūt dārgāku mašīnu, lai to darbinātu, kā arī dažas komandas, lai ielādētu indeksu startēšanas laikā. Mēs nebūtu varējuši to viegli paplašināt, lai izveidotu jaunas funkcijas, piemēram, labāku <a %(wikipedia_cjk_characters)s>tokenizāciju valodām bez atstarpēm</a>, filtrēšanu/facetingu, kārtošanu, "vai jūs domājāt" ieteikumus, automātisko pabeigšanu un tā tālāk. Viens no mūsu <a %(annas_archive)s>biļetēm</a> bija dažādu problēmu kopums ar mūsu meklēšanas sistēmu. Mēs izmantojām MySQL pilna teksta meklēšanu, jo visi mūsu dati jau bija MySQL. Bet tam bija savi ierobežojumi: Pēc sarunām ar vairākiem ekspertiem mēs izvēlējāmies ElasticSearch. Tas nav bijis ideāls (to noklusējuma “vai jūs domājāt” ieteikumi un automātiskās pabeigšanas funkcijas ir vāji), bet kopumā tas ir bijis daudz labāks par MySQL meklēšanai. Mēs joprojām neesam <a %(youtube)s>pārāk pārliecināti</a> par tā izmantošanu jebkādiem misijas kritiskiem datiem (lai gan viņi ir veikuši daudz <a %(elastic_co)s>progresu</a>), bet kopumā mēs esam diezgan apmierināti ar pāreju. Pašlaik mēs esam ieviesuši daudz ātrāku meklēšanu, labāku valodu atbalstu, labāku atbilstības kārtošanu, dažādas kārtošanas iespējas un filtrēšanu pēc valodas/grāmatas veida/faila veida. Ja jūs interesē, kā tas darbojas, <a %(annas_archive_l140)s>apskatiet</a> <a %(annas_archive_l1115)s>to</a> <a %(annas_archive_l1635)s>šeit</a>. Tas ir diezgan pieejams, lai gan tam varētu būt nepieciešami vēl daži komentāri… Annas Arhīvs ir pilnībā atvērts avots Mēs uzskatām, ka informācijai jābūt brīvai, un mūsu pašu kods nav izņēmums. Mēs esam izlaiduši visu mūsu kodu mūsu privāti hostētajā Gitlab instancē: <a %(annas_archive)s>Annas Programmatūra</a>. Mēs arī izmantojam problēmu izsekotāju, lai organizētu mūsu darbu. Ja vēlaties iesaistīties mūsu izstrādē, šī ir lieliska vieta, kur sākt. Lai sniegtu jums ieskatu par to, pie kā mēs strādājam, apskatiet mūsu neseno darbu pie klienta puses veiktspējas uzlabojumiem. Tā kā mēs vēl neesam ieviesuši lapu numerāciju, bieži atgriezām ļoti garas meklēšanas lapas ar 100-200 rezultātiem. Mēs nevēlējāmies pārtraukt meklēšanas rezultātus pārāk ātri, taču tas nozīmēja, ka tas palēnināja dažas ierīces. Tāpēc mēs ieviesām nelielu triku: mēs iesaiņojām lielāko daļu meklēšanas rezultātu HTML komentāros (<code><!-- --></code>), un tad uzrakstījām nelielu Javascript, kas noteiktu, kad rezultāts kļūst redzams, tajā brīdī mēs atvērtu komentāru: DOM "virtualizācija" ieviesta 23 rindās, nav nepieciešamas sarežģītas bibliotēkas! Tas ir tāds ātrs pragmatisks kods, ko iegūstat, kad jums ir ierobežots laiks un reālas problēmas, kas jāatrisina. Ir ziņots, ka mūsu meklēšana tagad labi darbojas uz lēnām ierīcēm! Vēl viens liels darbs bija automatizēt datubāzes veidošanu. Kad mēs sākām, mēs vienkārši nejauši apvienojām dažādus avotus. Tagad mēs vēlamies tos atjaunināt, tāpēc uzrakstījām virkni skriptu, lai lejupielādētu jaunu metadata no diviem Library Genesis atzariem un integrētu tos. Mērķis ir ne tikai padarīt to noderīgu mūsu arhīvam, bet arī atvieglot lietas ikvienam, kurš vēlas eksperimentēt ar ēnu bibliotēkas metadata. Mērķis būtu Jupyter piezīmju grāmatiņa, kurā ir pieejama visa veida interesanta metadata, lai mēs varētu veikt vairāk pētījumu, piemēram, noskaidrot, kāds <a %(blog)s>procentuālais daudzums ISBN tiek saglabāts uz visiem laikiem</a>. Visbeidzot, mēs pārveidojām mūsu ziedojumu sistēmu. Tagad jūs varat izmantot kredītkarti, lai tieši iemaksātu naudu mūsu kripto makos, patiesībā nezinot neko par kriptovalūtām. Mēs turpināsim uzraudzīt, kā tas darbojas praksē, bet tas ir liels solis uz priekšu. Ar Z-Library pazušanu un tā (it kā) dibinātāju arestu, mēs esam strādājuši dienu un nakti, lai nodrošinātu labu alternatīvu ar Annas Arhīvu (mēs to šeit nesasaistīsim, bet jūs varat to meklēt Google). Šeit ir dažas no lietām, ko mēs nesen sasniedzām. Annas Jaunumi: pilnībā atvērts avots arhīvs, ElasticSearch, 300GB+ grāmatu vāku Mēs esam strādājuši dienu un nakti, lai nodrošinātu labu alternatīvu ar Annas Arhīvu. Šeit ir dažas no lietām, ko mēs nesen sasniedzām. Analīze Semantiskos dublikātus (dažādi skenējumi no vienas un tās pašas grāmatas) teorētiski var filtrēt, bet tas ir sarežģīti. Manuāli pārskatot komiksus, mēs atradām pārāk daudz kļūdaini pozitīvu rezultātu. Daži dublikāti ir tikai pēc MD5, kas ir salīdzinoši izšķērdīgi, bet to filtrēšana mums dotu tikai apmēram 1%% ietaupījumu. Šajā mērogā tas joprojām ir apmēram 1TB, bet arī šajā mērogā 1TB īsti nav nozīmīgs. Mēs labāk neriskējam nejauši iznīcināt datus šajā procesā. Mēs atradām daudz datu, kas nav grāmatas, piemēram, filmas, kas balstītas uz komiksiem. Tas arī šķiet izšķērdīgi, jo tās jau ir plaši pieejamas citos veidos. Tomēr mēs sapratām, ka nevaram vienkārši filtrēt filmu failus, jo ir arī <em>interaktīvi komiksi</em>, kas tika izlaisti datorā, kurus kāds ierakstīja un saglabāja kā filmas. Galu galā, jebkas, ko mēs varētu izdzēst no kolekcijas, ietaupītu tikai dažus procentus. Tad mēs atcerējāmies, ka esam datu krājēji, un cilvēki, kas to spoguļos, arī ir datu krājēji, un tāpēc, "KO JŪS DOMĀJAT, DZĒST?!" :) Kad jūs saņemat 95TB datu jūsu glabāšanas klasterī, jūs mēģināt saprast, kas tur vispār ir… Mēs veicām analīzi, lai redzētu, vai varam nedaudz samazināt izmēru, piemēram, noņemot dublikātus. Šeit ir daži no mūsu atklājumiem: Tāpēc mēs jums piedāvājam pilnu, nemodificētu kolekciju. Tas ir daudz datu, bet mēs ceram, ka pietiekami daudz cilvēku rūpēsies par to, lai to sēklotu. Sadarbība Ņemot vērā tās apjomu, šī kolekcija jau sen ir bijusi mūsu vēlmju sarakstā, tāpēc pēc mūsu panākumiem ar Z-Library rezerves kopiju, mēs pievērsāmies šai kolekcijai. Sākumā mēs to tieši nokasījām, kas bija diezgan izaicinājums, jo viņu serveris nebija labākajā stāvoklī. Šādā veidā mēs ieguvām apmēram 15TB, bet tas bija lēns process. Par laimi, mums izdevās sazināties ar bibliotēkas operatoru, kurš piekrita nosūtīt mums visus datus tieši, kas bija daudz ātrāk. Tomēr tas joprojām prasīja vairāk nekā pusgadu, lai pārsūtītu un apstrādātu visus datus, un mēs gandrīz zaudējām visu diska bojājumu dēļ, kas nozīmētu, ka būtu jāsāk no jauna. Šī pieredze lika mums uzskatīt, ka ir svarīgi šos datus izplatīt pēc iespējas ātrāk, lai tos varētu spoguļot plaši un tālu. Mēs esam tikai viena vai divu neveiksmīgi laika ziņā sakritīgu incidentu attālumā no šīs kolekcijas zaudēšanas uz visiem laikiem! Kolekcija Ātra rīcība nozīmē, ka kolekcija ir nedaudz neorganizēta… Apskatīsim to. Iedomājieties, ka mums ir failu sistēma (kas patiesībā tiek sadalīta pa torrentiem): Pirmais direktorijs, <code>/repository</code>, ir šīs struktūras sakārtotākā daļa. Šis direktorijs satur tā sauktos “tūkstošu direktorijus”: direktorijus, katrs ar tūkstošiem failu, kas ir secīgi numurēti datubāzē. Direktorijs <code>0</code> satur failus ar comic_id 0–999, un tā tālāk. Šī ir tā pati shēma, ko Library Genesis izmanto savām daiļliteratūras un nedaiļliteratūras kolekcijām. Ideja ir tāda, ka katrs “tūkstošu direktorijs” automātiski tiek pārvērsts par torrentu, tiklīdz tas ir piepildīts. Tomēr Libgen.li operators nekad neveidoja torrentus šai kolekcijai, un tāpēc tūkstošu direktoriji, iespējams, kļuva neērti un deva ceļu “nesakārtotiem direktorijiem”. Tie ir <code>/comics0</code> līdz <code>/comics4</code>. Visiem ir unikālas direktoriju struktūras, kas, iespējams, bija saprotamas failu vākšanai, bet tagad mums tās nav pārāk saprotamas. Par laimi, metadata joprojām tieši atsaucas uz visiem šiem failiem, tāpēc to glabāšanas organizācija diskā faktiski nav svarīga! Metadata ir pieejama MySQL datubāzes formā. To var lejupielādēt tieši no Libgen.li vietnes, bet mēs to arī padarīsim pieejamu torrentā, kopā ar mūsu pašu tabulu ar visiem MD5 hešiem. <q>Dr. Barbara Gordona cenšas pazaudēt sevi bibliotēkas ikdienišķajā pasaulē…</q> Libgen atzari Vispirms, neliels fons. Jūs, iespējams, zināt Library Genesis par viņu episko grāmatu kolekciju. Mazāk cilvēku zina, ka Library Genesis brīvprātīgie ir izveidojuši citus projektus, piemēram, ievērojamu žurnālu un standarta dokumentu kolekciju, pilnu Sci-Hub rezerves kopiju (sadarbībā ar Sci-Hub dibinātāju Aleksandru Elbakjanu) un, patiesi, milzīgu komiksu kolekciju. Kādā brīdī dažādi Library Genesis spoguļu operatori devās savos ceļos, kas radīja pašreizējo situāciju ar vairākiem dažādiem "atzariem", kas visi joprojām nes Library Genesis nosaukumu. Libgen.li atzars unikāli satur šo komiksu kolekciju, kā arī ievērojamu žurnālu kolekciju (pie kuras mēs arī strādājam). Ziedojumu vākšana Mēs izlaižam šos datus lielos gabalos. Pirmais torrents ir <code>/comics0</code>, kuru mēs ievietojām vienā milzīgā 12TB .tar failā. Tas ir labāk jūsu cietajam diskam un torrentu programmatūrai nekā neskaitāmi mazi faili. Kā daļu no šīs izlaišanas mēs rīkojam ziedojumu vākšanu. Mēs vēlamies savākt 20 000 USD, lai segtu šīs kolekcijas darbības un līgumdarbu izmaksas, kā arī nodrošinātu turpmākos un nākotnes projektus. Mums ir daži <em>milzīgi</em> projekti izstrādē. <em>Ko es atbalstu ar savu ziedojumu?</em> Īsumā: mēs dublējam visu cilvēces zināšanas un kultūru un padarām to viegli pieejamu. Viss mūsu kods un dati ir atvērtā koda, mēs esam pilnībā brīvprātīgi vadīts projekts, un līdz šim esam saglabājuši 125TB grāmatu (papildus Libgen un Scihub esošajiem torrentiem). Galu galā mēs veidojam spararatu, kas ļauj un motivē cilvēkus atrast, skenēt un dublēt visas pasaules grāmatas. Mēs par mūsu galveno plānu rakstīsim nākamajā ierakstā. :) Ja jūs ziedojat par 12 mēnešu “Amazing Archivist” dalību (780 USD), jūs varat <strong>“adoptēt torrentu”</strong>, kas nozīmē, ka mēs ievietosim jūsu lietotājvārdu vai ziņojumu viena no torrentu failu nosaukumā! Jūs varat ziedot, dodoties uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a> un noklikšķinot uz pogas “Ziedot”. Mēs arī meklējam vairāk brīvprātīgo: programmatūras inženierus, drošības pētniekus, anonīmus tirgotāju ekspertus un tulkotājus. Jūs varat mūs atbalstīt arī, nodrošinot hostinga pakalpojumus. Un, protams, lūdzu, sēklējiet mūsu torrentus! Paldies visiem, kas jau tik dāsni mūs atbalstījuši! Jūs patiešām maināt situāciju. Šeit ir līdz šim izlaistie torrenti (mēs joprojām apstrādājam pārējos): Visi torrenti ir atrodami <a %(wikipedia_annas_archive)s>Annas Arhīvs</a> sadaļā “Datasets” (mēs tur tieši nesaitējam, lai saites uz šo emuāru netiktu noņemtas no Reddit, Twitter utt.). No turienes sekojiet saitei uz Tor vietni. <a %(news_ycombinator)s>Diskutēt Hacker News</a> Kas tālāk? Daudz torrentu ir lieliski ilgtermiņa saglabāšanai, bet ne tik daudz ikdienas piekļuvei. Mēs strādāsim ar hostinga partneriem, lai visu šo datu ievietotu tīmeklī (jo Annas Arhīvs neko tieši neuzglabā). Protams, jūs varēsiet atrast šīs lejupielādes saites Annas Arhīvā. Mēs arī aicinām visus darīt kaut ko ar šiem datiem! Palīdziet mums tos labāk analizēt, dublēt, ievietot IPFS, pārveidot, apmācīt savus AI modeļus ar tiem un tā tālāk. Tas viss ir jūsu, un mēs nevaram sagaidīt, ko jūs ar to darīsiet. Visbeidzot, kā jau teikts iepriekš, mums joprojām ir daži milzīgi izlaidumi, kas nāk (ja <em>kāds</em> varētu <em>nejauši</em> nosūtīt mums <em>noteiktas</em> ACS4 datubāzes izgāztuvi, jūs zināt, kur mūs atrast...), kā arī veidojam spararatu, lai dublētu visas pasaules grāmatas. Tāpēc palieciet pieslēgti, mēs tikai sākam. - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Lielākā komiksu ēnu bibliotēka, visticamāk, ir kāda konkrēta Library Genesis atzara: Libgen.li. Šīs vietnes administrators spēja savākt neticamu komiksu kolekciju ar vairāk nekā 2 miljoniem failu, kopā pārsniedzot 95TB. Tomēr, atšķirībā no citām Library Genesis kolekcijām, šī nebija pieejama masveidā caur torrentiem. Šos komiksus varēja piekļūt tikai individuāli caur viņa lēno personīgo serveri — vienu kļūmes punktu. Līdz šodienai! Šajā ierakstā mēs pastāstīsim vairāk par šo kolekciju un par mūsu līdzekļu vākšanu, lai atbalstītu vairāk šāda veida darbu. Annas Arhīvs ir dublējis pasaulē lielāko komiksu ēnu bibliotēku (95TB) — jūs varat palīdzēt to sēklot Lielākajai komiksu grāmatu ēnu bibliotēkai pasaulē bija viens kļūmes punkts.. līdz šodienai. Brīdinājums: šis emuāra ieraksts ir novecojis. Mēs esam nolēmuši, ka IPFS vēl nav gatavs plašai lietošanai. Mēs joprojām saistīsimies ar failiem IPFS no Annas Arhīva, kad tas būs iespējams, bet mēs tos vairs neuzturēsim paši, un mēs neiesakām citiem spoguļot, izmantojot IPFS. Lūdzu, skatiet mūsu Torrents lapu, ja vēlaties palīdzēt saglabāt mūsu kolekciju. 5,998,794 grāmatu ievietošana IPFS Kopiju pavairošana Atgriežoties pie mūsu sākotnējā jautājuma: kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi? Galvenā problēma šeit ir tā, ka mūsu kolekcija ir <a %(torrents_stats)s>strauji augusi</a>, nokasot un atverot dažas masīvas kolekcijas (papildus jau paveiktajam brīnišķīgajam darbam, ko veic citas atvērtu datu ēnu bibliotēkas, piemēram, Sci-Hub un Library Genesis). Šī datu pieaugšana apgrūtina kolekciju spoguļošanu visā pasaulē. Datu uzglabāšana ir dārga! Bet mēs esam optimistiski, īpaši novērojot šādas trīs tendences. Mūsu kolekciju <a %(annas_archive_stats)s>kopējais apjoms</a> pēdējo mēnešu laikā, sadalīts pēc torrentu sējēju skaita. HDD cenu tendences no dažādiem avotiem (noklikšķiniet, lai skatītu pētījumu). <a %(critical_window_chinese)s>Ķīniešu versija 中文版</a>, apspriest <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Mēs esam noplūkuši viegli pieejamos augļus Tas tieši izriet no mūsu iepriekš apspriestajām prioritātēm. Mēs dodam priekšroku strādāt pie lielu kolekciju atbrīvošanas vispirms. Tagad, kad esam nodrošinājuši dažas no lielākajām kolekcijām pasaulē, mēs sagaidām, ka mūsu pieaugums būs daudz lēnāks. Joprojām ir garš mazāku kolekciju saraksts, un katru dienu tiek skenētas vai publicētas jaunas grāmatas, bet temps, visticamāk, būs daudz lēnāks. Mēs varētu vēl dubultoties vai pat trīskāršoties, bet ilgākā laika periodā. OCR uzlabojumi. Prioritātes Zinātnes un inženierijas programmatūras kods Izdomāti vai izklaidējoši visu iepriekš minēto versijas Ģeogrāfiskie dati (piemēram, kartes, ģeoloģiskās aptaujas) Iekšējie dati no korporācijām vai valdībām (noplūdes) Mērījumu dati, piemēram, zinātniskie mērījumi, ekonomiskie dati, korporatīvie ziņojumi Metadatu ieraksti vispārīgi (par daiļliteratūru un nedaiļliteratūru; par citiem medijiem, mākslu, cilvēkiem utt.; ieskaitot atsauksmes) Daiļliteratūras grāmatas Daiļliteratūras žurnāli, avīzes, rokasgrāmatas Daiļliteratūras runu, dokumentālo filmu, podkāstu transkripti Organiski dati, piemēram, DNS sekvences, augu sēklas vai mikrobu paraugi Akadēmiskie raksti, žurnāli, ziņojumi Zinātnes un inženierijas tīmekļa vietnes, tiešsaistes diskusijas Juridisko vai tiesas procesu transkripti Unikāli iznīcināšanas riskā (piemēram, kara, finansējuma samazinājumu, tiesas prāvu vai politiskās vajāšanas dēļ) Reti Unikāli nepietiekami fokusēti Kāpēc mums tik ļoti rūp raksti un grāmatas? Atstāsim malā mūsu pamatuzskatu par saglabāšanu vispār — mēs varētu par to uzrakstīt citu ierakstu. Tātad, kāpēc tieši raksti un grāmatas? Atbilde ir vienkārša: <strong>informācijas blīvums</strong>. Uzglabāšanas megabaitā rakstītais teksts uzglabā visvairāk informācijas no visiem medijiem. Lai gan mums rūp gan zināšanas, gan kultūra, mēs vairāk rūpējamies par pirmajām. Kopumā mēs atrodam informācijas blīvuma un saglabāšanas nozīmīguma hierarhiju, kas izskatās aptuveni šādi: Šī saraksta secība ir nedaudz patvaļīga — vairāki vienumi ir vienādi vai mūsu komandā ir nesaskaņas — un mēs, iespējams, aizmirstam dažas svarīgas kategorijas. Bet aptuveni tā mēs prioritizējam. Daži no šiem vienumiem ir pārāk atšķirīgi no citiem, lai mēs par tiem uztrauktos (vai jau ir parūpējušās citas iestādes), piemēram, organiskie dati vai ģeogrāfiskie dati. Bet lielākā daļa no šī saraksta vienumiem mums patiesībā ir svarīgi. Vēl viens liels faktors mūsu prioritizācijā ir tas, cik lielā mērā kāds darbs ir apdraudēts. Mēs dodam priekšroku koncentrēties uz darbiem, kas ir: Visbeidzot, mums rūp mērogs. Mums ir ierobežots laiks un nauda, tāpēc mēs labprātāk pavadītu mēnesi, glābjot 10 000 grāmatu, nevis 1 000 grāmatu — ja tās ir aptuveni vienlīdz vērtīgas un apdraudētas. <em><q>Zaudēto nevar atgūt; bet saglabāsim to, kas paliek: nevis ar seifiem un slēdzenēm, kas tos nošķir no sabiedrības acīm un lietošanas, nododot tos laika atkritumiem, bet ar tādu kopiju pavairošanu, kas tos padarīs nepieejamus nejaušībām.</q></em><br>— Tomass Džefersons, 1791 Ēnu bibliotēkas Kods var būt atvērts avots Github, bet Github kopumā nevar viegli spoguļot un tādējādi saglabāt (lai gan šajā konkrētajā gadījumā ir pietiekami izplatītas lielākās daļas kodu repozitoriju kopijas) Metadatu ierakstus var brīvi skatīt Worldcat vietnē, bet tos nevar lejupielādēt lielos apjomos (līdz mēs tos <a %(worldcat_scrape)s>nokasījām</a>) Reddit ir brīvi lietojams, bet nesen ir ieviesis stingrus pret nokasīšanas pasākumus, reaģējot uz datu izsalkušo LLM apmācību (vairāk par to vēlāk) Ir daudz organizāciju, kurām ir līdzīgi mērķi un līdzīgas prioritātes. Patiesi, ir bibliotēkas, arhīvi, laboratorijas, muzeji un citas iestādes, kas ir atbildīgas par šāda veida saglabāšanu. Daudzas no tām ir labi finansētas, no valdībām, indivīdiem vai korporācijām. Bet tām ir viens milzīgs aklais punkts: tiesību sistēma. Šeit slēpjas ēnu bibliotēku unikālā loma un iemesls, kāpēc pastāv Annas Arhīvs. Mēs varam darīt lietas, ko citas iestādes nedrīkst darīt. Tagad, tas nav (bieži) tā, ka mēs varam arhivēt materiālus, kas citur ir nelikumīgi saglabājami. Nē, daudzās vietās ir likumīgi veidot arhīvu ar jebkurām grāmatām, rakstiem, žurnāliem un tā tālāk. Bet ko bieži trūkst juridiskajiem arhīviem, ir <strong>redundance un ilgmūžība</strong>. Ir grāmatas, no kurām tikai viena kopija eksistē kādā fiziskā bibliotēkā kaut kur. Ir metadatu ieraksti, kurus sargā tikai viena korporācija. Ir avīzes, kas saglabātas tikai mikrofilmā vienā arhīvā. Bibliotēkas var saņemt finansējuma samazinājumus, korporācijas var bankrotēt, arhīvi var tikt bombardēti un nodedzināti līdz pamatiem. Tas nav hipotētiski — tas notiek visu laiku. Ko mēs varam unikāli darīt Annas Arhīvā, ir uzglabāt daudz kopiju darbiem, lielā mērogā. Mēs varam savākt rakstus, grāmatas, žurnālus un vairāk, un izplatīt tos lielos apjomos. Pašlaik mēs to darām caur torrentiem, bet precīzas tehnoloģijas nav svarīgas un laika gaitā mainīsies. Svarīgākais ir iegūt daudzas kopijas, kas izplatītas visā pasaulē. Šis citāts no vairāk nekā 200 gadiem atpakaļ joprojām ir aktuāls: Īsa piezīme par publisko domēnu. Tā kā Annas Arhīvs unikāli koncentrējas uz darbībām, kas daudzās vietās pasaulē ir nelikumīgas, mēs neuztraucamies par plaši pieejamām kolekcijām, piemēram, publiskā domēna grāmatām. Juridiskās iestādes bieži vien jau labi par to rūpējas. Tomēr ir apsvērumi, kas dažkārt liek mums strādāt pie publiski pieejamām kolekcijām: - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Uzglabāšanas izmaksas turpina eksponenciāli samazināties 3. Informācijas blīvuma uzlabojumi Pašlaik mēs glabājam grāmatas to sākotnējos formātos, kādos tās mums tiek nodotas. Protams, tās ir saspiestas, bet bieži vien tās joprojām ir lieli skenējumi vai lapu fotogrāfijas. Līdz šim vienīgās iespējas samazināt mūsu kolekcijas kopējo apjomu bija agresīvāka saspiešana vai dublēšanās novēršana. Tomēr, lai iegūtu pietiekami lielus ietaupījumus, abas metodes ir pārāk zaudējošas mūsu gaumei. Smaga fotogrāfiju saspiešana var padarīt tekstu gandrīz nelasāmu. Un dublēšanās novēršanai ir nepieciešama augsta pārliecība, ka grāmatas ir pilnīgi vienādas, kas bieži vien ir pārāk neprecīzi, īpaši, ja saturs ir vienāds, bet skenējumi veikti dažādos laikos. Vienmēr ir bijusi trešā iespēja, bet tās kvalitāte bija tik slikta, ka mēs to nekad neapsvērām: <strong>OCR jeb optiskā rakstzīmju atpazīšana</strong>. Tas ir process, kurā fotogrāfijas tiek pārvērstas vienkāršā tekstā, izmantojot mākslīgo intelektu, lai atpazītu fotogrāfijās esošās rakstzīmes. Šādi rīki jau sen pastāv un ir bijuši diezgan labi, bet "diezgan labi" nav pietiekami saglabāšanas nolūkiem. Tomēr nesenie multimodālie dziļās mācīšanās modeļi ir guvuši ārkārtīgi strauju progresu, lai gan joprojām ar augstām izmaksām. Mēs sagaidām, ka gan precizitāte, gan izmaksas ievērojami uzlabosies nākamajos gados, līdz brīdim, kad tas kļūs reāli piemērojams visai mūsu bibliotēkai. Kad tas notiks, mēs, iespējams, joprojām saglabāsim oriģinālos failus, bet papildus tam mēs varētu izveidot daudz mazāku mūsu bibliotēkas versiju, kuru lielākā daļa cilvēku vēlēsies spoguļot. Galvenais ir tas, ka neapstrādāts teksts pats par sevi saspiežas vēl labāk un ir daudz vieglāk dublēt, dodot mums vēl vairāk ietaupījumu. Kopumā nav nereāli sagaidīt vismaz 5-10 reizes lielāku failu izmēra samazinājumu, varbūt pat vairāk. Pat ar konservatīvu 5 reizes samazinājumu, mēs skatītos uz <strong>1 000–3 000 $ 10 gadu laikā, pat ja mūsu bibliotēka trīskāršotos</strong>. Rakstīšanas brīdī <a %(diskprices)s>diska cenas</a> par TB ir ap $12 jauniem diskiem, $8 lietotiem diskiem un $4 lentēm. Ja mēs esam konservatīvi un skatāmies tikai uz jauniem diskiem, tas nozīmē, ka petabaita uzglabāšana maksā ap $12,000. Ja pieņemam, ka mūsu bibliotēka trīskāršosies no 900TB līdz 2.7PB, tas nozīmētu $32,400, lai spoguļotu visu mūsu bibliotēku. Pievienojot elektrību, citu aparatūras izmaksas un tā tālāk, noapaļosim to līdz $40,000. Vai ar lentēm vairāk kā $15,000–$20,000. No vienas puses <strong>$15,000–$40,000 par visu cilvēces zināšanu summu ir izdevīgi</strong>. No otras puses, tas ir nedaudz dārgi, lai sagaidītu daudz pilnu kopiju, īpaši, ja mēs arī vēlētos, lai šie cilvēki turpina sēt savus torrentus citu labā. Tas ir šodien. Bet progress virzās uz priekšu: Cietā diska izmaksas par TB ir aptuveni samazinājušās par trešdaļu pēdējo 10 gadu laikā, un, visticamāk, turpinās samazināties līdzīgā tempā. Lente šķietami ir uz līdzīgas trajektorijas. SSD cenas krītas vēl ātrāk, un varētu pārņemt HDD cenas līdz desmitgades beigām. Ja tas turpināsies, tad pēc 10 gadiem mēs varētu skatīties uz tikai $5,000–$13,000, lai spoguļotu visu mūsu kolekciju (1/3), vai pat mazāk, ja mēs pieaugam mazāk apjomā. Lai gan tas joprojām ir daudz naudas, tas būs pieejams daudziem cilvēkiem. Un tas varētu būt vēl labāk, pateicoties nākamajam punktam… Annas Arhīvā mums bieži jautā, kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi, ja to kopējais apjoms jau tuvojas 1 petabaitam (1000 TB) un turpina pieaugt. Šajā rakstā mēs aplūkosim mūsu filozofiju un redzēsim, kāpēc nākamā desmitgade ir kritiska mūsu misijai saglabāt cilvēces zināšanas un kultūru. Kritiskais logs Ja šīs prognozes ir precīzas, mums <strong>vienkārši jāgaida pāris gadi</strong>, līdz mūsu visa kolekcija tiks plaši spoguļota. Tādējādi, Tomasa Džefersona vārdiem sakot, "novietota ārpus negadījuma sasniedzamības." Diemžēl LLM parādīšanās un to datu izsalkušā apmācība ir likusi daudziem autortiesību īpašniekiem aizsargāties. Vēl vairāk nekā viņi jau bija. Daudzas vietnes padara grūtāku datu nokasīšanu un arhivēšanu, tiesas prāvas ir izplatītas, un tajā pašā laikā fiziskās bibliotēkas un arhīvi turpina tikt atstāti novārtā. Mēs varam tikai sagaidīt, ka šīs tendences turpinās pasliktināties, un daudzi darbi tiks zaudēti ilgi pirms tie nonāk publiskajā domēnā. <strong>Mēs esam uz saglabāšanas revolūcijas sliekšņa, bet <q>zaudēto nevar atgūt.</q></strong> Mums ir kritisks logs apmēram 5-10 gadi, kuru laikā joprojām ir diezgan dārgi darbināt ēnu bibliotēku un izveidot daudzus spoguļus visā pasaulē, un kura laikā piekļuve vēl nav pilnībā slēgta. Ja mēs varam pārvarēt šo logu, tad mēs patiešām būsim saglabājuši cilvēces zināšanas un kultūru uz mūžiem. Mums nevajadzētu ļaut šim laikam tikt izšķiestam. Mums nevajadzētu ļaut šim kritiskajam logam aizvērties. Aiziet. Ēnu bibliotēku kritiskais logs Kā mēs varam apgalvot, ka saglabājam savas kolekcijas mūžīgi, ja tās jau tuvojas 1 PB? Kolekcija Daži papildu dati par kolekciju. <a %(duxiu)s>Duxiu</a> ir milzīga skenēto grāmatu datubāze, ko izveidojusi <a %(chaoxing)s>SuperStar Digital Library Group</a>. Lielākā daļa ir akadēmiskās grāmatas, kas skenētas, lai tās būtu pieejamas digitāli universitātēm un bibliotēkām. Mūsu angliski runājošajai auditorijai <a %(library_princeton)s>Princeton</a> un <a %(guides_lib_uw)s>Vašingtonas Universitāte</a> piedāvā labus pārskatus. Ir arī lielisks raksts, kas sniedz vairāk informācijas: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (meklējiet to Annas Arhīvā). Duxiu grāmatas jau ilgi ir pirātētas Ķīnas internetā. Parasti tās tiek pārdotas par mazāk nekā dolāru no tālākpārdevējiem. Tās parasti tiek izplatītas, izmantojot Ķīnas ekvivalentu Google Drive, kas bieži ir uzlauzts, lai nodrošinātu vairāk vietas. Dažas tehniskās detaļas var atrast <a %(github_duty_machine)s>šeit</a> un <a %(github_821_github_io)s>šeit</a>. Lai gan grāmatas ir daļēji publiski izplatītas, tās ir diezgan grūti iegūt lielapjomā. Mums tas bija augstu mūsu TODO sarakstā, un mēs tam piešķīrām vairākus mēnešus pilna laika darba. Tomēr nesen pie mums vērsās neticams, apbrīnojams un talantīgs brīvprātīgais, kurš mums pastāstīja, ka viņš jau ir paveicis visu šo darbu — par lielām izmaksām. Viņi dalījās ar mums pilnā kolekcijā, negaidot neko pretī, izņemot ilgtermiņa saglabāšanas garantiju. Patiesi ievērojami. Viņi piekrita lūgt palīdzību šādā veidā, lai kolekcija tiktu OCR'ēta. Kolekcijā ir 7,543,702 faili. Tas ir vairāk nekā Library Genesis daiļliteratūra (apmēram 5,3 miljoni). Kopējais failu izmērs ir aptuveni 359TB (326TiB) pašreizējā formā. Mēs esam atvērti citiem priekšlikumiem un idejām. Vienkārši sazinieties ar mums. Apskatiet Annas Arhīvu, lai iegūtu vairāk informācijas par mūsu kolekcijām, saglabāšanas centieniem un to, kā jūs varat palīdzēt. Paldies! Piemēra lapas Lai pierādītu mums, ka jums ir laba sistēma, šeit ir dažas piemēra lapas, ar kurām sākt, no grāmatas par supravadītājiem. Jūsu sistēmai vajadzētu pareizi apstrādāt matemātiku, tabulas, diagrammas, piezīmes un tā tālāk. Nosūtiet savas apstrādātās lapas uz mūsu e-pastu. Ja tās izskatīsies labi, mēs jums nosūtīsim vairāk privāti, un mēs sagaidām, ka jūs varēsiet ātri palaist savu sistēmu arī uz tām. Kad būsim apmierināti, mēs varam noslēgt darījumu. - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Ķīniešu versija 中文版</a>, <a %(news_ycombinator)s>Diskutēt Hacker News</a> Šis ir īss emuāra ieraksts. Mēs meklējam kādu uzņēmumu vai iestādi, kas palīdzētu mums ar OCR un teksta izvilkšanu masveida kolekcijai, kuru mēs ieguvām, apmaiņā pret ekskluzīvu agrīnu piekļuvi. Pēc embargo perioda mēs, protams, izlaidīsim visu kolekciju. Augstas kvalitātes akadēmiskais teksts ir ārkārtīgi noderīgs LLM apmācībai. Lai gan mūsu kolekcija ir ķīniešu valodā, tā var būt noderīga arī angļu LLM apmācībai: modeļi, šķiet, kodē koncepcijas un zināšanas neatkarīgi no avota valodas. Lai to izdarītu, teksts ir jāizvelk no skenējumiem. Ko no tā iegūst Annas Arhīvs? Pilnteksta meklēšanu grāmatās saviem lietotājiem. Tā kā mūsu mērķi saskan ar LLM izstrādātāju mērķiem, mēs meklējam sadarbības partneri. Mēs esam gatavi dot jums <strong>ekskluzīvu agrīnu piekļuvi šai kolekcijai lielapjomā uz 1 gadu</strong>, ja jūs varat veikt pareizu OCR un teksta izvilkšanu. Ja esat gatavs dalīties ar visu sava procesa kodu ar mums, mēs būtu gatavi pagarināt kolekcijas embargo. Ekskluzīva piekļuve LLM uzņēmumiem pasaulē lielākajai ķīniešu zinātniskās literatūras kolekcijai <em><strong>Īsumā:</strong> Annas Arhīvs ieguva unikālu 7,5 miljonu / 350TB ķīniešu zinātniskās literatūras grāmatu kolekciju — lielāku nekā Library Genesis. Mēs esam gatavi dot LLM uzņēmumam ekskluzīvu piekļuvi apmaiņā pret augstas kvalitātes OCR un teksta izvilkšanu.</em> Sistēmas arhitektūra Tātad, pieņemsim, ka jūs atradāt dažus uzņēmumus, kas ir gatavi mitināt jūsu vietni, neslēdzot to — sauksim tos par “brīvību mīlošiem nodrošinātājiem” 😄. Jūs ātri atklāsiet, ka viss mitināšana pie viņiem ir diezgan dārga, tāpēc jūs varētu vēlēties atrast dažus “lētos nodrošinātājus” un faktisko mitināšanu veikt tur, izmantojot starpniekus caur brīvību mīlošiem nodrošinātājiem. Ja to izdarīsiet pareizi, lētie nodrošinātāji nekad nezinās, ko jūs mitināt, un nekad nesaņems sūdzības. Ar visiem šiem nodrošinātājiem pastāv risks, ka viņi jūs tomēr slēgs, tāpēc jums ir nepieciešama arī redundance. Mums tas ir nepieciešams visos mūsu kaudzes līmeņos. Viena nedaudz brīvību mīloša kompānija, kas ir nostādījusi sevi interesantā pozīcijā, ir Cloudflare. Viņi ir <a %(blog_cloudflare)s>apgalvojuši</a>, ka viņi nav mitināšanas nodrošinātājs, bet gan komunālais pakalpojums, kā ISP. Tāpēc viņi nav pakļauti DMCA vai citiem izņemšanas pieprasījumiem un pārsūta jebkādus pieprasījumus jūsu faktiskajam mitināšanas nodrošinātājam. Viņi ir gājuši tik tālu, ka devušies uz tiesu, lai aizsargātu šo struktūru. Tāpēc mēs varam tos izmantot kā vēl vienu kešatmiņas un aizsardzības slāni. Cloudflare nepieņem anonīmus maksājumus, tāpēc mēs varam izmantot tikai viņu bezmaksas plānu. Tas nozīmē, ka mēs nevaram izmantot viņu slodzes balansēšanas vai pārslēgšanās funkcijas. Tāpēc mēs <a %(annas_archive_l255)s>īstenojām to paši</a> domēna līmenī. Lapas ielādes laikā pārlūks pārbaudīs, vai pašreizējais domēns joprojām ir pieejams, un, ja nē, tas pārraksta visas URL uz citu domēnu. Tā kā Cloudflare kešatmiņā saglabā daudzas lapas, tas nozīmē, ka lietotājs var nonākt mūsu galvenajā domēnā, pat ja starpniekserveris ir izslēgts, un pēc tam nākamajā klikšķī tikt pārvietots uz citu domēnu. Mums joprojām ir arī parastās darbības problēmas, ar kurām jātiek galā, piemēram, servera veselības uzraudzība, aizmugures un priekšpuses kļūdu reģistrēšana un tā tālāk. Mūsu pārslēgšanās arhitektūra ļauj lielāku robustumu arī šajā frontē, piemēram, darbinot pilnīgi atšķirīgu serveru komplektu vienā no domēniem. Mēs pat varam darbināt vecākas koda un datu kopu versijas šajā atsevišķajā domēnā, ja galvenajā versijā nepamanīts kritisks kļūdas. Mēs varam arī nodrošināties pret Cloudflare vēršanos pret mums, noņemot to no viena no domēniem, piemēram, šī atsevišķā domēna. Iespējamas dažādas šo ideju permutācijas. Secinājums Ir bijusi interesanta pieredze mācīties, kā izveidot izturīgu un noturīgu ēnu bibliotēkas meklētājprogrammu. Ir daudz vairāk detaļu, ko dalīties nākamajos ierakstos, tāpēc ļaujiet man zināt, par ko jūs vēlētos uzzināt vairāk! Kā vienmēr, mēs meklējam ziedojumus, lai atbalstītu šo darbu, tāpēc noteikti apskatiet Ziedojumu lapu Annas Arhīvā. Mēs arī meklējam citus atbalsta veidus, piemēram, dotācijas, ilgtermiņa sponsorus, augsta riska maksājumu nodrošinātājus, varbūt pat (gaumīgas!) reklāmas. Un, ja vēlaties ieguldīt savu laiku un prasmes, mēs vienmēr meklējam izstrādātājus, tulkotājus un tā tālāk. Paldies par jūsu interesi un atbalstu. Inovāciju žetoni Sāksim ar mūsu tehnoloģiju kaudzi. Tā ir apzināti garlaicīga. Mēs izmantojam Flask, MariaDB un ElasticSearch. Tas ir burtiski viss. Meklēšana lielā mērā ir atrisināta problēma, un mēs neplānojam to izgudrot no jauna. Turklāt mums ir jāiztērē mūsu <a %(mcfunley)s>inovāciju žetoni</a> kaut kam citam: lai mūs neapturētu iestādes. Cik legāla vai nelegāla ir Annas Arhīvs? Tas lielākoties ir atkarīgs no juridiskās jurisdikcijas. Lielākā daļa valstu tic kādai autortiesību formai, kas nozīmē, ka cilvēkiem vai uzņēmumiem tiek piešķirta ekskluzīva monopola tiesības uz noteiktiem darbiem uz noteiktu laiku. Starp citu, Annas Arhīvā mēs uzskatām, ka, lai gan ir dažas priekšrocības, kopumā autortiesības ir negatīvas sabiedrībai — bet tas ir stāsts citai reizei. Šis ekskluzīvais monopols uz noteiktiem darbiem nozīmē, ka ir nelegāli ikvienam ārpus šī monopola tieši izplatīt šos darbus — arī mums. Bet Annas Arhīvs ir meklētājprogramma, kas tieši neizplata šos darbus (vismaz ne mūsu tīmekļa vietnē), tāpēc mums vajadzētu būt kārtībā, vai ne? Ne gluži. Daudzās jurisdikcijās ir ne tikai nelegāli izplatīt aizsargātus darbus, bet arī saistīt uz vietām, kas to dara. Klasisks piemērs tam ir Amerikas Savienoto Valstu DMCA likums. Tas ir spektra stingrākais gals. Spektra otrā galā teorētiski varētu būt valstis, kurās vispār nav autortiesību likumu, bet tādas īsti nepastāv. Gandrīz katrai valstij ir kāda autortiesību likuma forma. Izpilde ir cits stāsts. Ir daudz valstu, kuru valdības nevēlas izpildīt autortiesību likumus. Ir arī valstis starp abiem galējībām, kas aizliedz izplatīt aizsargātus darbus, bet neaizliedz saistīt uz šādiem darbiem. Vēl viens apsvērums ir uzņēmuma līmenī. Ja uzņēmums darbojas jurisdikcijā, kas nerūpējas par autortiesībām, bet pats uzņēmums nevēlas uzņemties nekādu risku, tad viņi varētu slēgt jūsu vietni, tiklīdz kāds par to sūdzas. Visbeidzot, liels apsvērums ir maksājumi. Tā kā mums ir jāpaliek anonīmiem, mēs nevaram izmantot tradicionālās maksājumu metodes. Tas atstāj mums kriptovalūtas, un tikai neliels uzņēmumu skaits tās atbalsta (ir virtuālās debetkartes, kas tiek apmaksātas ar kripto, bet tās bieži netiek pieņemtas). - Anna un komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Es vadu <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, pasaulē lielāko atvērtā koda bezpeļņas meklētājprogrammu <a %(wikipedia_shadow_library)s>ēnu bibliotēkām</a>, piemēram, Sci-Hub, Library Genesis un Z-Bibliotēku. Mūsu mērķis ir padarīt zināšanas un kultūru viegli pieejamu un galu galā izveidot kopienu, kas kopā arhivē un saglabā <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>visas pasaules grāmatas</a>. Šajā rakstā es parādīšu, kā mēs vadām šo vietni, un unikālos izaicinājumus, kas rodas, vadot vietni ar apšaubāmu juridisko statusu, jo nav “AWS ēnu labdarībām”. <em>Apskatiet arī saistīto rakstu <a %(blog_how_to_become_a_pirate_archivist)s>Kā kļūt par pirātu arhivāru</a>.</em> Kā vadīt ēnu bibliotēku: darbības Annas Arhīvā Nav <q>AWS ēnu labdarībām,</q> tāpēc kā mēs vadām Annas Arhīvu? Rīki Lietojumprogrammu serveris: Flask, MariaDB, ElasticSearch, Docker. Izstrāde: Gitlab, Weblate, Zulip. Serveru pārvaldība: Ansible, Checkmk, UFW. Sīpola statiskā mitināšana: Tor, Nginx. Starpniekserveris: Varnish. Apskatīsim, kādus rīkus mēs izmantojam, lai to visu paveiktu. Tas ļoti mainās, kad saskaramies ar jaunām problēmām un atrodam jaunus risinājumus. Ir dažas lēmumu jomas, kurās esam šaubījušies. Viens no tiem ir komunikācija starp serveriem: agrāk izmantojām Wireguard, bet atklājām, ka tas dažkārt pārtrauc datu pārraidi vai pārraida datus tikai vienā virzienā. Tas notika ar vairākiem dažādiem Wireguard iestatījumiem, kurus izmēģinājām, piemēram, <a %(github_costela_wesher)s>wesher</a> un <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Mēģinājām arī tunelēt portus caur SSH, izmantojot autossh un sshuttle, bet saskārāmies ar <a %(github_sshuttle)s>problēmām tur</a> (lai gan man joprojām nav skaidrs, vai autossh cieš no TCP-over-TCP problēmām vai nē — man tas šķiet kā neveikls risinājums, bet varbūt tas patiesībā ir labi?). Tā vietā mēs atgriezāmies pie tiešiem savienojumiem starp serveriem, slēpjot, ka serveris darbojas uz lētiem pakalpojumu sniedzējiem, izmantojot IP filtrēšanu ar UFW. Tam ir trūkums, ka Docker nedarbojas labi ar UFW, ja vien neizmantojat <code>network_mode: "host"</code>. Tas viss ir nedaudz vairāk pakļauts kļūdām, jo ar nelielu nepareizu konfigurāciju jūs varat pakļaut savu serveri internetam. Varbūt mums vajadzētu atgriezties pie autossh — atsauksmes šeit būtu ļoti noderīgas. Mēs arī esam šaubījušies starp Varnish un Nginx. Pašlaik mums patīk Varnish, bet tam ir savas īpatnības un raupjās malas. Tas pats attiecas uz Checkmk: mēs to nemīlam, bet tas pagaidām darbojas. Weblate ir bijis pieņemams, bet ne izcils — dažkārt baidos, ka tas zaudēs manus datus, kad mēģinu to sinhronizēt ar mūsu git repo. Flask kopumā ir bijis labs, bet tam ir dažas dīvainas īpatnības, kas prasījušas daudz laika, lai tās novērstu, piemēram, pielāgotu domēnu konfigurēšana vai problēmas ar tā SqlAlchemy integrāciju. Līdz šim pārējie rīki ir bijuši lieliski: mums nav nopietnu sūdzību par MariaDB, ElasticSearch, Gitlab, Zulip, Docker un Tor. Visiem šiem ir bijušas dažas problēmas, bet nekas pārāk nopietns vai laikietilpīgs. Kopiena Pirmais izaicinājums varētu būt pārsteidzošs. Tas nav tehnisks vai juridisks jautājums. Tas ir psiholoģisks jautājums: darbs ēnā var būt neticami vientuļš. Atkarībā no tā, ko plānojat darīt, un jūsu draudu modeļa, jums var nākties būt ļoti uzmanīgiem. Vienā spektra galā ir cilvēki kā Aleksandra Elbakjana*, Sci-Hub dibinātāja, kura ir ļoti atklāta par savām aktivitātēm. Bet viņa ir augsta riska zonā, ja viņa apmeklētu rietumu valsti šajā brīdī, un varētu saskarties ar desmitiem gadu cietumsodu. Vai tas ir risks, kuru jūs būtu gatavs uzņemties? Mēs esam spektra otrā galā; esam ļoti uzmanīgi, lai neatstātu nekādas pēdas, un mums ir spēcīga operatīvā drošība. * Kā minēts HN "ynno", Aleksandra sākotnēji nevēlējās būt pazīstama: "Viņas serveri bija iestatīti, lai izdotu detalizētus kļūdu ziņojumus no PHP, ieskaitot pilnu kļūdu avota faila ceļu, kas atradās direktorijā /home/<USER>" Tāpēc izmantojiet nejaušus lietotājvārdus datoros, kurus izmantojat šīm lietām, gadījumā, ja kaut ko nepareizi konfigurējat. Tomēr šī slepenība nāk ar psiholoģiskām izmaksām. Lielākā daļa cilvēku mīl, kad viņu darbs tiek atzīts, un tomēr jūs nevarat saņemt nekādu atzinību par to reālajā dzīvē. Pat vienkāršas lietas var būt izaicinošas, piemēram, draugi jautā, ar ko esat nodarbojies (kādā brīdī "ķimerējos ar savu NAS / homelab" kļūst vecs). Tāpēc ir tik svarīgi atrast kādu kopienu. Jūs varat atteikties no kādas operatīvās drošības, uzticoties dažiem ļoti tuviem draugiem, kuriem jūs zināt, ka varat dziļi uzticēties. Pat tad esiet uzmanīgi, lai neko nerakstītu, gadījumā, ja viņiem nāktos nodot savus e-pastus iestādēm vai ja viņu ierīces būtu apdraudētas kādā citā veidā. Vēl labāk ir atrast dažus līdzīgi domājošus pirātus. Ja jūsu tuvi draugi ir ieinteresēti pievienoties jums, lieliski! Pretējā gadījumā jūs varētu atrast citus tiešsaistē. Diemžēl šī joprojām ir nišas kopiena. Līdz šim mēs esam atraduši tikai dažus citus, kas ir aktīvi šajā jomā. Labi sākumpunkti šķiet Library Genesis forumi un r/DataHoarder. Arhīva komanda arī ir līdzīgi domājoši indivīdi, lai gan viņi darbojas likuma ietvaros (pat ja dažās likuma pelēkajās zonās). Tradicionālās "warez" un pirātisma ainas arī ir cilvēki, kas domā līdzīgi. Mēs esam atvērti idejām par to, kā veicināt kopienas attīstību un izpētīt idejas. Jūtieties brīvi sazināties ar mums Twitter vai Reddit. Varbūt mēs varētu rīkot kādu forumu vai čata grupu. Viens no izaicinājumiem ir tas, ka tas var viegli tikt cenzēts, izmantojot parastās platformas, tāpēc mums būtu jānodrošina pašiem. Ir arī kompromiss starp šo diskusiju pilnīgu publiskošanu (lielāka potenciālā iesaiste) un privātumu (neļaujot potenciālajiem "mērķiem" zināt, ka mēs gatavojamies tos pārmeklēt). Mums par to būs jādomā. Paziņojiet mums, ja jūs interesē šis! Secinājums Ceramies, ka tas ir noderīgi jaunajiem pirātu arhivāriem. Mēs esam priecīgi jūs sveikt šajā pasaulē, tāpēc nevilcinieties sazināties. Saglabāsim pēc iespējas vairāk pasaules zināšanu un kultūras un izplatīsim to plaši un tālu. Projekti 4. Datu atlase Bieži vien jūs varat izmantot metadatus, lai noteiktu saprātīgu datu apakškopu, ko lejupielādēt. Pat ja jūs galu galā vēlaties lejupielādēt visus datus, var būt noderīgi prioritizēt vissvarīgākos vienumus vispirms, gadījumā, ja jūs tiekat atklāts un aizsardzība tiek uzlabota, vai arī tāpēc, ka jums būtu jāiegādājas vairāk disku, vai vienkārši tāpēc, ka kaut kas cits notiek jūsu dzīvē, pirms jūs varat lejupielādēt visu. Piemēram, kolekcijā var būt vairākas vienas un tās pašas resursa (piemēram, grāmatas vai filmas) izdevumi, kur viens ir atzīmēts kā vislabākās kvalitātes. Saglabāt šos izdevumus vispirms būtu ļoti saprātīgi. Jūs varētu galu galā vēlēties saglabāt visus izdevumus, jo dažos gadījumos metadati var būt nepareizi marķēti, vai arī var būt nezināmi kompromisi starp izdevumiem (piemēram, "labākais izdevums" var būt labākais lielākajā daļā veidu, bet sliktāks citos veidos, piemēram, filmai ir augstāka izšķirtspēja, bet trūkst subtitru). Jūs varat arī meklēt savā metadatu datubāzē, lai atrastu interesantas lietas. Kāds ir lielākais fails, kas tiek mitināts, un kāpēc tas ir tik liels? Kāds ir mazākais fails? Vai ir interesanti vai negaidīti modeļi attiecībā uz noteiktām kategorijām, valodām un tā tālāk? Vai ir dublikāti vai ļoti līdzīgi nosaukumi? Vai ir modeļi, kad dati tika pievienoti, piemēram, viena diena, kurā tika pievienoti daudzi faili vienlaikus? Jūs bieži varat daudz uzzināt, aplūkojot datu kopu dažādos veidos. Mūsu gadījumā mēs no Z-Library grāmatām noņēmām dublikātus, salīdzinot md5 hashus ar Library Genesis, tādējādi ietaupot daudz lejupielādes laika un diska vietas. Tomēr šī ir diezgan unikāla situācija. Vairumā gadījumu nav visaptverošu datubāzu par to, kuri faili jau ir pienācīgi saglabāti citu pirātu vidū. Tas pats par sevi ir milzīga iespēja kādam tur ārā. Būtu lieliski, ja būtu regulāri atjaunināts pārskats par tādām lietām kā mūzika un filmas, kas jau ir plaši izplatītas torrentu vietnēs, un tāpēc ir zemāka prioritāte iekļaušanai pirātu spoguļos. 6. Izplatīšana Jums ir dati, tādējādi dodot jums pasaules pirmo pirātu spoguli jūsu mērķim (visticamāk). Daudzos veidos grūtākā daļa ir beigusies, bet riskantākā daļa vēl ir priekšā. Galu galā, līdz šim jūs esat bijis slepens; lidojot zem radara. Viss, kas jums bija jādara, bija izmantot labu VPN visā laikā, neaizpildot savus personīgos datus nevienā formā (protams), un, iespējams, izmantojot īpašu pārlūkprogrammas sesiju (vai pat citu datoru). Tagad jums ir jāizplata dati. Mūsu gadījumā mēs vispirms vēlējāmies atdot grāmatas atpakaļ Library Genesis, bet ātri atklājām grūtības tajā (daiļliteratūras un nedaiļliteratūras šķirošana). Tāpēc mēs nolēmām izplatīt, izmantojot Library Genesis stila torrentus. Ja jums ir iespēja piedalīties esošā projektā, tas varētu ietaupīt daudz laika. Tomēr pašlaik nav daudz labi organizētu pirātu spoguļu. Tātad, pieņemsim, ka jūs nolemjat izplatīt torrentus pats. Centieties saglabāt šos failus mazos, lai tos būtu viegli spoguļot citās vietnēs. Jums tad būs jāizsēj torrentus pašam, vienlaikus paliekot anonīmam. Jūs varat izmantot VPN (ar vai bez portu pārsūtīšanas) vai maksāt ar sajauktiem Bitcoin par Seedbox. Ja jūs nezināt, ko daži no šiem terminiem nozīmē, jums būs daudz lasīšanas darāmā, jo ir svarīgi, lai jūs saprastu risku kompromisus šeit. Jūs varat mitināt pašus torrent failus esošajās torrent vietnēs. Mūsu gadījumā mēs izvēlējāmies faktiski mitināt vietni, jo mēs arī vēlējāmies skaidri izplatīt savu filozofiju. Jūs varat to darīt pats līdzīgā veidā (mēs izmantojam Njalla mūsu domēniem un mitināšanai, maksājot ar sajauktiem Bitcoin), bet arī nevilcinieties sazināties ar mums, lai mēs varētu mitināt jūsu torrentus. Mēs vēlamies laika gaitā izveidot visaptverošu pirātu spoguļu indeksu, ja šī ideja kļūst populāra. Kas attiecas uz VPN izvēli, par to jau ir daudz rakstīts, tāpēc mēs vienkārši atkārtosim vispārējo padomu izvēlēties pēc reputācijas. Faktiski tiesā pārbaudītas bezlogu politikas ar ilgu pieredzi privātuma aizsardzībā ir zemākais riska variants, mūsuprāt. Ņemiet vērā, ka pat tad, ja jūs darāt visu pareizi, jūs nekad nevarat sasniegt nulles risku. Piemēram, kad jūs izsējat savus torrentus, ļoti motivēts valsts aktors, iespējams, var apskatīt ienākošos un izejošos datu plūsmas VPN serveriem un secināt, kas jūs esat. Vai arī jūs vienkārši varat kaut kādā veidā kļūdīties. Mēs, iespējams, jau esam kļūdījušies un kļūdīsimies atkal. Par laimi, valstis neuztraucas <em>tik</em> daudz par pirātismu. Viena no lēmumiem, kas jāpieņem katram projektam, ir, vai to publicēt, izmantojot to pašu identitāti kā iepriekš, vai nē. Ja jūs turpināt izmantot to pašu vārdu, tad kļūdas operatīvajā drošībā no iepriekšējiem projektiem var atgriezties un jūs sakost. Bet publicēšana ar dažādiem vārdiem nozīmē, ka jūs neveidojat ilgstošāku reputāciju. Mēs izvēlējāmies no sākuma nodrošināt spēcīgu operatīvo drošību, lai mēs varētu turpināt izmantot to pašu identitāti, bet mēs nevilcināsimies publicēt ar citu vārdu, ja mēs kļūdīsimies vai ja apstākļi to prasīs. Vārda izplatīšana var būt sarežģīta. Kā mēs teicām, šī joprojām ir nišas kopiena. Mēs sākotnēji publicējām Reddit, bet patiesi guvām atsaucību Hacker News. Pašlaik mūsu ieteikums ir to publicēt dažās vietās un redzēt, kas notiek. Un atkal, sazinieties ar mums. Mēs labprāt izplatītu vairāk pirātu arhīvisma centienu vārdu. 1. Domēna izvēle / filozofija Nav trūkuma zināšanu un kultūras mantojuma, kas būtu jāsaglabā, kas var būt pārsteidzoši. Tāpēc bieži vien ir noderīgi veltīt brīdi un padomāt par to, kāds varētu būt jūsu ieguldījums. Katram ir atšķirīgs veids, kā par to domāt, bet šeit ir daži jautājumi, kurus jūs varētu sev uzdot: Mūsu gadījumā mēs īpaši rūpējāmies par zinātnes ilgtermiņa saglabāšanu. Mēs zinājām par Library Genesis un to, kā tas tika pilnībā spoguļots daudzas reizes, izmantojot torrentus. Mums patika šī ideja. Tad kādu dienu viens no mums mēģināja atrast dažas zinātniskās mācību grāmatas Library Genesis, bet nevarēja tās atrast, kas radīja šaubas par to, cik pilnīgs tas patiesībā bija. Mēs tad meklējām šīs mācību grāmatas tiešsaistē un atradām tās citās vietās, kas iedēstīja sēklu mūsu projektam. Pat pirms mēs zinājām par Z-Library, mums bija ideja nemēģināt savākt visas šīs grāmatas manuāli, bet koncentrēties uz esošo kolekciju spoguļošanu un to atgriešanu Library Genesis. Kādas prasmes jums ir, kuras varat izmantot savā labā? Piemēram, ja esat tiešsaistes drošības eksperts, jūs varat atrast veidus, kā pārvarēt IP bloķējumus drošiem mērķiem. Ja esat lielisks kopienu organizēšanā, tad varbūt jūs varat apvienot dažus cilvēkus ap mērķi. Tomēr ir noderīgi zināt kādu programmēšanu, ja tikai, lai uzturētu labu operatīvo drošību visā šajā procesā. Kāda būtu augstas ietekmes joma, uz kuru koncentrēties? Ja jūs plānojat pavadīt X stundas pirātu arhivēšanā, tad kā jūs varat iegūt lielāko "sprādzienu par savu naudu"? Kādas ir unikālas domas, kuras jūs par to domājat? Jums varētu būt dažas interesantas idejas vai pieejas, kuras citi varētu būt palaiduši garām. Cik daudz laika jums ir tam? Mūsu padoms būtu sākt ar mazākiem projektiem un veikt lielākus projektus, kad jūs to apgūstat, bet tas var kļūt par visu patērējošu. Kāpēc jūs interesē šis? Kas jūs aizrauj? Ja mēs varam iegūt grupu cilvēku, kuri visi arhivē lietas, kas viņiem īpaši rūp, tas aptvertu daudz! Jūs zināsiet daudz vairāk nekā vidusmēra cilvēks par savu aizraušanos, piemēram, kādi ir svarīgi dati, ko saglabāt, kādas ir labākās kolekcijas un tiešsaistes kopienas utt. 3. Metadatu nokasīšana Pievienošanas/modificēšanas datums: lai jūs varētu atgriezties vēlāk un lejupielādēt failus, kurus iepriekš neesat lejupielādējis (lai gan bieži vien varat izmantot arī ID vai hash šim nolūkam). Hash (md5, sha1): lai apstiprinātu, ka esat pareizi lejupielādējis failu. ID: var būt kāds iekšējs ID, bet ID, piemēram, ISBN vai DOI, ir noderīgi arī. Faila nosaukums / atrašanās vieta Apraksts, kategorija, tagi, autori, valoda utt. Izmērs: lai aprēķinātu, cik daudz diska vietas jums ir nepieciešams. Iesim nedaudz tehniskāk. Lai faktiski nokasītu metadatus no vietnēm, mēs esam saglabājuši lietas diezgan vienkāršas. Mēs izmantojam Python skriptus, dažreiz curl, un MySQL datubāzi, lai saglabātu rezultātus. Mēs neesam izmantojuši nekādu sarežģītu nokasīšanas programmatūru, kas var kartēt sarežģītas vietnes, jo līdz šim mums bija nepieciešams nokasīt tikai vienu vai divu veidu lapas, vienkārši izskaitot cauri ID un parsējot HTML. Ja nav viegli izskaitāmu lapu, tad jums var būt nepieciešams pareizs rāpuļprogramma, kas mēģina atrast visas lapas. Pirms sākat nokasīt visu vietni, mēģiniet to darīt manuāli uz brīdi. Pārejiet cauri dažām desmitiem lapu paši, lai iegūtu sajūtu, kā tas darbojas. Dažreiz jūs jau šādā veidā sastapsieties ar IP bloķēšanu vai citu interesantu uzvedību. Tas pats attiecas uz datu nokasīšanu: pirms pārāk dziļi iedziļināties šajā mērķī, pārliecinieties, ka varat efektīvi lejupielādēt tā datus. Lai apietu ierobežojumus, ir dažas lietas, ko varat izmēģināt. Vai ir kādas citas IP adreses vai serveri, kas mitina tos pašus datus, bet kuriem nav tādu pašu ierobežojumu? Vai ir kādi API galapunkti, kuriem nav ierobežojumu, kamēr citiem ir? Pie kāda lejupielādes ātruma jūsu IP tiek bloķēts un uz cik ilgu laiku? Vai arī jūs netiekat bloķēts, bet ātrums tiek samazināts? Kas notiek, ja izveidojat lietotāja kontu, kā tad mainās lietas? Vai varat izmantot HTTP/2, lai saglabātu savienojumus atvērtus, un vai tas palielina ātrumu, kādā varat pieprasīt lapas? Vai ir lapas, kas uzskaita vairākus failus vienlaikus, un vai tur uzskaitītā informācija ir pietiekama? Lietas, kuras jūs, iespējams, vēlaties saglabāt, ietver: Parasti mēs to darām divos posmos. Vispirms mēs lejupielādējam neapstrādātos HTML failus, parasti tieši MySQL (lai izvairītos no daudz mazu failu, par ko mēs runājam vairāk zemāk). Tad, atsevišķā solī, mēs izskatām šos HTML failus un parsējam tos faktiskajās MySQL tabulās. Tādā veidā jums nav jāpārlejupielādē viss no jauna, ja atklājat kļūdu savā parsēšanas kodā, jo varat vienkārši pārstrādāt HTML failus ar jauno kodu. Tas arī bieži vien ir vieglāk paralelizēt apstrādes soli, tādējādi ietaupot laiku (un jūs varat rakstīt apstrādes kodu, kamēr nokasīšana notiek, nevis rakstīt abus soļus vienlaikus). Visbeidzot, ņemiet vērā, ka dažiem mērķiem metadatu iegūšana ir viss, kas ir pieejams. Ir dažas milzīgas metadatu kolekcijas, kas nav pienācīgi saglabātas. Nosaukums Domēna izvēle / filozofija: Kur jūs aptuveni vēlaties koncentrēties un kāpēc? Kādas ir jūsu unikālās kaislības, prasmes un apstākļi, kurus varat izmantot savā labā? Mērķa izvēle: Kuru konkrēto kolekciju jūs spoguļosiet? Metadata nokasīšana: Failu informācijas katalogizēšana, faktiski neielādējot pašus (bieži vien daudz lielākos) failus. Datu izvēle: Pamatojoties uz metadata, sašaurinot, kuri dati šobrīd ir visatbilstošākie arhivēšanai. Tas var būt viss, bet bieži vien ir saprātīgs veids, kā ietaupīt vietu un joslas platumu. Datu nokasīšana: Faktiski iegūstot datus. Izplatīšana: Iepakojot to torrentos, paziņojot par to kaut kur, liekot cilvēkiem to izplatīt. 5. Datu iegūšana Tagad jūs esat gatavs faktiski lejupielādēt datus lielā apjomā. Kā minēts iepriekš, šajā brīdī jums jau vajadzētu manuāli lejupielādēt vairākus failus, lai labāk izprastu mērķa uzvedību un ierobežojumus. Tomēr, kad jūs faktiski sākat lejupielādēt daudz failu vienlaikus, joprojām būs pārsteigumi. Mūsu padoms šeit ir galvenokārt saglabāt to vienkāršu. Sāciet, vienkārši lejupielādējot vairākus failus. Jūs varat izmantot Python un pēc tam paplašināt līdz vairākiem pavedieniem. Bet dažreiz pat vienkāršāk ir ģenerēt Bash failus tieši no datubāzes un pēc tam palaist vairākus no tiem vairākos termināļa logos, lai palielinātu apjomu. Ātrs tehnisks triks, kas šeit ir vērts pieminēt, ir OUTFILE izmantošana MySQL, ko varat rakstīt jebkur, ja atspējojat "secure_file_priv" mysqld.cnf (un pārliecinieties, ka arī atspējojat/pārrakstāt AppArmor, ja esat uz Linux). Mēs glabājam datus uz vienkāršiem cietajiem diskiem. Sāciet ar to, kas jums ir, un paplašiniet lēnām. Var būt pārsteidzoši domāt par simtiem TB datu glabāšanu. Ja tā ir situācija, ar kuru jūs saskaraties, vienkārši izveidojiet labu apakškopu vispirms un savā paziņojumā lūdziet palīdzību pārējā glabāšanā. Ja jūs vēlaties iegūt vairāk cieto disku pats, tad r/DataHoarder ir daži labi resursi, lai iegūtu labus piedāvājumus. Centieties pārāk neuztraukties par sarežģītām failu sistēmām. Ir viegli iekrist truša alā, uzstādot tādas lietas kā ZFS. Tomēr viens tehnisks sīkums, kas jāņem vērā, ir tas, ka daudzas failu sistēmas netiek galā labi ar daudziem failiem. Mēs esam atraduši, ka vienkāršs risinājums ir izveidot vairākas direktorijas, piemēram, dažādiem ID diapazoniem vai hash prefiksiem. Pēc datu lejupielādes pārliecinieties, ka pārbaudāt failu integritāti, izmantojot metadatos pieejamos hashus, ja tie ir pieejami. 2. Mērķa izvēle Pieejama: neizmanto daudz aizsardzības slāņu, lai novērstu jūsu metadatu un datu nokasīšanu. Īpaša ieskats: jums ir kāda īpaša informācija par šo mērķi, piemēram, jums ir īpaša piekļuve šai kolekcijai, vai arī jūs esat izdomājis, kā pārvarēt viņu aizsardzību. Tas nav obligāti (mūsu gaidāmais projekts neko īpašu nedara), bet tas noteikti palīdz! Liela Tātad, mums ir mūsu izpētes joma, tagad kuru konkrēto kolekciju mēs spoguļosim? Ir daži faktori, kas padara mērķi labu: Kad mēs atradām mūsu zinātniskās mācību grāmatas vietnēs, kas nav Library Genesis, mēs mēģinājām noskaidrot, kā tās nonāca internetā. Tad mēs atradām Z-Library un sapratām, ka, lai gan lielākā daļa grāmatu sākotnēji neparādās tur, tās galu galā tur nonāk. Mēs uzzinājām par tās attiecībām ar Library Genesis un (finansiālo) stimulu struktūru un pārāko lietotāja saskarni, kas abas padarīja to par daudz pilnīgāku kolekciju. Tad mēs veicām sākotnējo metadatu un datu nokasīšanu un sapratām, ka varam apiet viņu IP lejupielādes ierobežojumus, izmantojot viena no mūsu biedru īpašo piekļuvi daudziem starpniekserveriem. Izpētot dažādus mērķus, jau ir svarīgi slēpt savas pēdas, izmantojot VPN un vienreizējās e-pasta adreses, par ko mēs runāsim vēlāk. Unikāla: nav jau labi pārklāta ar citiem projektiem. Kad mēs veicam projektu, tam ir vairāki posmi: Šie posmi nav pilnīgi neatkarīgi, un bieži vien ieskati no vēlākā posma liek jums atgriezties pie agrākā posma. Piemēram, metadata nokasīšanas laikā jūs varat saprast, ka izvēlētais mērķis ir ar aizsardzības mehānismiem, kas pārsniedz jūsu prasmju līmeni (piemēram, IP bloķēšana), tāpēc jūs atgriežaties un atrodat citu mērķi. - Anna un komanda (<a %(reddit)s>Reddit</a>) Par digitālās saglabāšanas <em>kāpēc</em> vispār un pirātu arhivismu īpaši varētu uzrakstīt veselas grāmatas, bet ļaujiet mums sniegt īsu ievadu tiem, kas nav pārāk pazīstami. Pasaule ražo vairāk zināšanu un kultūras nekā jebkad agrāk, bet arī vairāk no tā tiek zaudēts nekā jebkad agrāk. Cilvēce lielā mērā uztic šo mantojumu korporācijām, piemēram, akadēmiskajiem izdevējiem, straumēšanas pakalpojumiem un sociālo mediju uzņēmumiem, un viņi bieži nav pierādījuši, ka ir lieliski pārvaldnieki. Apskatiet dokumentālo filmu Digital Amnesia vai jebkuru Džeisona Skota runu. Ir dažas iestādes, kas labi veic arhivēšanu, cik vien iespējams, bet tās ir saistītas ar likumu. Kā pirāti, mēs esam unikālā pozīcijā arhivēt kolekcijas, kuras viņi nevar aizskart autortiesību ieviešanas vai citu ierobežojumu dēļ. Mēs varam arī spoguļot kolekcijas daudzkārt visā pasaulē, tādējādi palielinot pareizas saglabāšanas iespējas. Pašlaik mēs neiedziļināsimies diskusijās par intelektuālā īpašuma priekšrocībām un trūkumiem, likuma pārkāpšanas morāli, pārdomām par cenzūru vai piekļuves zināšanām un kultūrai jautājumu. Ar visu to nost, sāksim ar <em>kā</em>. Mēs dalīsimies, kā mūsu komanda kļuva par pirātu arhivāriem, un mācībām, ko mēs guvām pa ceļam. Ir daudz izaicinājumu, kad uzsākat šo ceļojumu, un cerams, ka mēs varam jums palīdzēt ar dažiem no tiem. Kā kļūt par pirātu arhivāru Pirmais izaicinājums varētu būt pārsteidzošs. Tas nav tehnisks vai juridisks jautājums. Tas ir psiholoģisks jautājums. Pirms mēs sākam, divi atjauninājumi par Pirātu bibliotēkas spoguli (REDIĢĒT: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>): Mēs saņēmām dažus ārkārtīgi dāsnus ziedojumus. Pirmais bija 10 tūkstoši dolāru no anonīma indivīda, kurš arī atbalstījis "bookwarrior", Library Genesis sākotnējo dibinātāju. Īpaša pateicība bookwarrior par šī ziedojuma veicināšanu. Otrais bija vēl 10 tūkstoši dolāru no anonīma ziedotāja, kurš sazinājās pēc mūsu pēdējās izlaides un bija iedvesmots palīdzēt. Mums bija arī vairāki mazāki ziedojumi. Liels paldies par visu jūsu dāsno atbalstu. Mums ir daži aizraujoši jauni projekti, kurus šis atbalstīs, tāpēc sekojiet līdzi. Mums bija dažas tehniskas grūtības ar mūsu otrās izlaides lielumu, bet mūsu torenti tagad ir pieejami un tiek sēti. Mēs arī saņēmām dāsnu piedāvājumu no anonīma indivīda sēt mūsu kolekciju uz viņu ļoti ātrajiem serveriem, tāpēc mēs veicam īpašu augšupielādi uz viņu mašīnām, pēc kuras visiem pārējiem, kas lejupielādē kolekciju, vajadzētu redzēt lielu ātruma uzlabojumu. Bloga ieraksti Sveiki, es esmu Anna. Es izveidoju <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, pasaulē lielāko ēnu bibliotēku. Šis ir mans personīgais blogs, kurā es un mani komandas biedri rakstām par pirātismu, digitālo saglabāšanu un daudz ko citu. Sazinieties ar mani <a %(reddit)s>Reddit</a>. Ņemiet vērā, ka šī vietne ir tikai blogs. Mēs šeit mitinām tikai savus vārdus. Šeit netiek mitināti vai saistīti ne torrenti, ne citi ar autortiesībām aizsargāti faili. <strong>Bibliotēka</strong> - Tāpat kā lielākā daļa bibliotēku, mēs galvenokārt koncentrējamies uz rakstiskiem materiāliem, piemēram, grāmatām. Iespējams, nākotnē paplašināsimies uz citiem mediju veidiem. <strong>Spogulis</strong> - Mēs esam stingri esošo bibliotēku spogulis. Mēs koncentrējamies uz saglabāšanu, nevis uz grāmatu vieglu meklēšanu un lejupielādi (piekļuvi) vai lielas kopienas veidošanu, kas pievieno jaunas grāmatas (avoti). <strong>Pirāts</strong> - Mēs apzināti pārkāpjam autortiesību likumu lielākajā daļā valstu. Tas ļauj mums darīt to, ko juridiskas personas nevar: nodrošināt, ka grāmatas tiek izplatītas plaši un tālu. <em>Mēs nesaistām failus no šī emuāra. Lūdzu, atrodiet tos paši.</em> - Anna un komanda (<a %(reddit)s>Reddit</a>) Šis projekts (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>) mērķē veicināt cilvēces zināšanu saglabāšanu un atbrīvošanu. Mēs sniedzam savu mazo un pieticīgo ieguldījumu, sekojot lielo priekšgājēju pēdās. Šī projekta fokuss ir ilustrēts tā nosaukumā: Pirmā bibliotēka, kuru mēs esam spoguļojuši, ir Z-Bibliotēka. Tā ir populāra (un nelegāla) bibliotēka. Viņi ir paņēmuši Library Genesis kolekciju un padarījuši to viegli meklējamu. Turklāt viņi ir kļuvuši ļoti efektīvi jaunu grāmatu ieguldījumu piesaistē, motivējot lietotājus ar dažādiem labumiem. Pašlaik viņi neatgriež šīs jaunās grāmatas atpakaļ Library Genesis. Un atšķirībā no Library Genesis, viņi nepadara savu kolekciju viegli spoguļojamu, kas kavē plašu saglabāšanu. Tas ir svarīgi viņu biznesa modelim, jo viņi iekasē maksu par piekļuvi savai kolekcijai lielos apjomos (vairāk nekā 10 grāmatas dienā). Mēs neveicam morālus spriedumus par naudas iekasēšanu par masveida piekļuvi nelikumīgai grāmatu kolekcijai. Nav šaubu, ka Z-Library ir veiksmīgi paplašinājusi piekļuvi zināšanām un nodrošinājusi vairāk grāmatu. Mēs esam šeit, lai veiktu savu daļu: nodrošināt šīs privātās kolekcijas ilgtermiņa saglabāšanu. Mēs vēlamies jūs aicināt palīdzēt saglabāt un atbrīvot cilvēces zināšanas, lejupielādējot un sējot mūsu torrentus. Skatiet projekta lapu, lai iegūtu vairāk informācijas par to, kā dati ir organizēti. Mēs arī ļoti vēlamies aicināt jūs sniegt savas idejas par to, kuras kolekcijas spoguļot nākamās un kā to darīt. Kopā mēs varam sasniegt daudz. Tas ir tikai neliels ieguldījums starp neskaitāmiem citiem. Paldies par visu, ko darāt. Iepazīstinām ar Pirātu bibliotēkas spoguli: 7TB grāmatu saglabāšana (kas nav Libgen) 10%% cilvēces rakstiskā mantojuma saglabāts uz visiem laikiem <strong>Google.</strong> Galu galā viņi veica šo pētījumu Google Books. Tomēr viņu metadata nav pieejama masveidā un ir diezgan grūti nokasīt. <strong>Dažādas individuālas bibliotēku sistēmas un arhīvi.</strong> Ir bibliotēkas un arhīvi, kas nav indeksēti un apkopoti nevienā no iepriekš minētajiem, bieži vien tāpēc, ka tie ir nepietiekami finansēti vai citu iemeslu dēļ nevēlas dalīties ar saviem datiem ar Open Library, OCLC, Google un tā tālāk. Daudziem no tiem ir digitālie ieraksti, kas pieejami internetā, un tie bieži vien nav ļoti labi aizsargāti, tāpēc, ja vēlaties palīdzēt un izklaidēties, mācoties par dīvainām bibliotēku sistēmām, šie ir lieliski sākumpunkti. <strong>ISBNdb.</strong> Šis ir šī emuāra ieraksta temats. ISBNdb nokasa dažādas vietnes, lai iegūtu grāmatu metadata, īpaši cenu datus, kurus viņi pēc tam pārdod grāmatu pārdevējiem, lai viņi varētu noteikt savu grāmatu cenas atbilstoši pārējam tirgum. Tā kā ISBN mūsdienās ir diezgan universāli, viņi faktiski izveidoja “tīmekļa lapu katrai grāmatai”. <strong>Open Library.</strong> Kā minēts iepriekš, tas ir viņu viss mērķis. Viņi ir ieguvuši milzīgus bibliotēku datus no sadarbības bibliotēkām un nacionālajiem arhīviem un turpina to darīt. Viņiem ir arī brīvprātīgie bibliotekāri un tehniskā komanda, kas mēģina novērst ierakstu dublēšanos un marķēt tos ar visdažādākajiem metadata. Vislabākais ir tas, ka viņu datu kopums ir pilnīgi atvērts. Jūs varat vienkārši <a %(openlibrary)s>lejupielādēt to</a>. <strong>WorldCat.</strong> Šī ir vietne, ko pārvalda bezpeļņas organizācija OCLC, kas pārdod bibliotēku pārvaldības sistēmas. Viņi apkopo grāmatu metadata no daudzām bibliotēkām un padara to pieejamu caur WorldCat vietni. Tomēr viņi arī pelna naudu, pārdodot šos datus, tāpēc tie nav pieejami masveida lejupielādei. Viņiem ir pieejami daži ierobežotāki masveida datu kopumi lejupielādei, sadarbojoties ar konkrētām bibliotēkām. 1. Par kādu saprātīgu "mūžības" definīciju. ;) 2. Protams, cilvēces rakstītais mantojums ir daudz vairāk nekā grāmatas, īpaši mūsdienās. Šī ieraksta un mūsu neseno izlaidumu dēļ mēs koncentrējamies uz grāmatām, bet mūsu intereses sniedzas tālāk. 3. Par Āronu Svartzu var teikt daudz vairāk, bet mēs tikai vēlējāmies viņu īsi pieminēt, jo viņš spēlē izšķirošu lomu šajā stāstā. Laikam ejot, vairāk cilvēku varētu pirmo reizi sastapties ar viņa vārdu un pēc tam paši iedziļināties šajā tēmā. <strong>Fiziskās kopijas.</strong> Acīmredzot tas nav ļoti noderīgi, jo tās ir tikai tā paša materiāla dublikāti. Būtu lieliski, ja mēs varētu saglabāt visas anotācijas, ko cilvēki veic grāmatās, piemēram, Fermā slavenās “pierakstus malās”. Bet diemžēl tas paliks arhivāra sapnis. <strong>“Izdevumi”.</strong> Šeit jūs skaitāt katru unikālo grāmatas versiju. Ja kaut kas par to ir atšķirīgs, piemēram, atšķirīgs vāks vai atšķirīgs priekšvārds, tas tiek uzskatīts par citu izdevumu. <strong>Faili.</strong> Strādājot ar ēnu bibliotēkām, piemēram, Library Genesis, Sci-Hub vai Z-Library, ir papildu apsvērums. Var būt vairāki viena izdevuma skenējumi. Un cilvēki var izveidot labākas esošo failu versijas, skenējot tekstu, izmantojot OCR, vai labojot lapas, kas tika skenētas leņķī. Mēs vēlamies skaitīt šos failus tikai kā vienu izdevumu, kas prasītu labu metadata vai dublēšanas novēršanu, izmantojot dokumentu līdzības mērus. <strong>“Darbi”.</strong> Piemēram, “Harijs Poters un Noslēpumu kambaris” kā loģisks jēdziens, kas ietver visas tā versijas, piemēram, dažādus tulkojumus un atkārtotus izdevumus. Šī ir noderīga definīcija, bet var būt grūti noteikt, kas tiek skaitīts. Piemēram, mēs, iespējams, vēlamies saglabāt dažādus tulkojumus, lai gan atkārtoti izdevumi ar tikai nelielām atšķirībām var nebūt tik svarīgi. - Anna un komanda (<a %(reddit)s>Reddit</a>) Ar Pirātu bibliotēkas spoguli (REDIĢĒTS: pārcelts uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>), mūsu mērķis ir paņemt visas pasaules grāmatas un saglabāt tās uz visiem laikiem.<sup>1</sup> Starp mūsu Z-Bibliotēkas torrentiem un oriģinālajiem Library Genesis torrentiem mums ir 11,783,153 faili. Bet cik tas patiesībā ir? Ja mēs pareizi deduplicētu šos failus, kādu procentu no visām pasaules grāmatām mēs esam saglabājuši? Mēs tiešām vēlētos, lai būtu kaut kas tāds: Sāksim ar dažiem aptuveniem skaitļiem: Gan Z-Library/Libgen, gan Open Library ir daudz vairāk grāmatu nekā unikālu ISBN. Vai tas nozīmē, ka daudzām no šīm grāmatām nav ISBN, vai arī ISBN metadata vienkārši trūkst? Mēs, iespējams, varam atbildēt uz šo jautājumu, izmantojot automatizētu saskaņošanu, pamatojoties uz citiem atribūtiem (nosaukums, autors, izdevējs utt.), pievienojot vairāk datu avotu un iegūstot ISBN no pašiem grāmatu skenējumiem (Z-Library/Libgen gadījumā). Cik no šiem ISBN ir unikāli? To vislabāk ilustrē ar Venn diagrammu: Lai būtu precīzāk: Mūs pārsteidza, cik maz ir pārklāšanās! ISBNdb ir milzīgs daudzums ISBN, kas neparādās ne Z-Library, ne Open Library, un tas pats attiecas (mazākā, bet joprojām ievērojamā mērā) uz pārējiem diviem. Tas rada daudz jaunu jautājumu. Cik daudz automatizēta saskaņošana palīdzētu marķēt grāmatas, kas nebija marķētas ar ISBN? Vai būtu daudz saskaņojumu un tādējādi palielināta pārklāšanās? Arī, kas notiktu, ja mēs pievienotu 4. vai 5. datu kopu? Cik daudz pārklāšanās mēs tad redzētu? Tas mums dod sākumpunktu. Tagad mēs varam aplūkot visus ISBN, kas nebija Z-Library datu kopā un kas nesakrīt arī ar nosaukuma/autora laukiem. Tas var dot mums iespēju saglabāt visas pasaules grāmatas: vispirms, meklējot internetā skenējumus, pēc tam dodoties reālajā dzīvē, lai skenētu grāmatas. Pēdējo pat varētu finansēt pūļa veidā vai vadīt ar "atlīdzībām" no cilvēkiem, kuri vēlētos redzēt konkrētas grāmatas digitalizētas. Tas viss ir stāsts citam laikam. Ja vēlaties palīdzēt ar kādu no šiem uzdevumiem — tālāka analīze; vairāk metadatu iegūšana; vairāk grāmatu atrašana; grāmatu OCR veikšana; to darīšana citās jomās (piemēram, raksti, audiogrāmatas, filmas, TV šovi, žurnāli) vai pat dažu šo datu pieejamības nodrošināšana tādām lietām kā ML / lielu valodu modeļu apmācība — lūdzu, sazinieties ar mani (<a %(reddit)s>Reddit</a>). Ja jūs īpaši interesē datu analīze, mēs strādājam pie tā, lai mūsu datu kopas un skripti būtu pieejami vieglāk lietojamā formātā. Būtu lieliski, ja jūs varētu vienkārši dakšot piezīmju grāmatiņu un sākt ar to spēlēties. Visbeidzot, ja vēlaties atbalstīt šo darbu, lūdzu, apsveriet iespēju veikt ziedojumu. Šī ir pilnībā brīvprātīga darbība, un jūsu ieguldījums rada milzīgu atšķirību. Katrs mazumiņš palīdz. Pašlaik mēs pieņemam ziedojumus kriptovalūtā; skatiet ziedojumu lapu Annas Arhīvā. Lai iegūtu procentu, mums ir nepieciešams saucējs: kopējais jebkad publicēto grāmatu skaits.<sup>2</sup> Pirms Google Books izbeigšanās, projekta inženieris Leonids Teičers <a %(booksearch_blogspot)s>mēģināja novērtēt</a> šo skaitli. Viņš nonāca pie — ar humoru — 129,864,880 (“vismaz līdz svētdienai”). Viņš novērtēja šo skaitli, izveidojot vienotu datubāzi par visām pasaules grāmatām. Šim nolūkam viņš apvienoja dažādus Datasets un pēc tam tos dažādos veidos apvienoja. Kā neliels ievads, ir vēl kāds cilvēks, kurš mēģināja katalogizēt visas pasaules grāmatas: Ārons Švarcs, nelaiķis digitālais aktīvists un Reddit līdzdibinātājs.<sup>3</sup> Viņš <a %(youtube)s>sāka Open Library</a> ar mērķi “viena tīmekļa lapa katrai jebkad publicētajai grāmatai”, apvienojot datus no dažādiem avotiem. Viņš samaksāja augstāko cenu par savu digitālās saglabāšanas darbu, kad tika apsūdzēts par akadēmisko rakstu masveida lejupielādi, kas noveda pie viņa pašnāvības. Needless to say, this is one of the reasons our group is pseudonymous, and why we’re being very careful. Open Library joprojām varonīgi vada cilvēki no Internet Archive, turpinot Ārona mantojumu. Mēs pie šī atgriezīsimies vēlāk šajā ierakstā. Google emuāra ierakstā Teičers apraksta dažus izaicinājumus, kas saistīti ar šī skaitļa novērtēšanu. Pirmkārt, kas ir grāmata? Ir dažas iespējamās definīcijas: “Editions” šķiet vispraktiskākā definīcija tam, kas ir “grāmatas”. Ērti, ka šī definīcija tiek izmantota arī unikālu ISBN numuru piešķiršanai. ISBN jeb Starptautiskais standarta grāmatas numurs tiek plaši izmantots starptautiskajā tirdzniecībā, jo tas ir integrēts starptautiskajā svītrkodu sistēmā (“Starptautiskais preču numurs”). Ja vēlaties pārdot grāmatu veikalos, tai ir nepieciešams svītrkods, tāpēc jūs saņemat ISBN. Teičera emuāra ierakstā minēts, ka, lai gan ISBN ir noderīgi, tie nav universāli, jo tie tika patiešām pieņemti tikai septiņdesmito gadu vidū un ne visur pasaulē. Tomēr ISBN, iespējams, ir visplašāk izmantotais grāmatu izdevumu identifikators, tāpēc tas ir mūsu labākais sākumpunkts. Ja mēs varam atrast visus pasaules ISBN, mēs iegūstam noderīgu sarakstu ar grāmatām, kuras vēl ir jāsaglabā. Tātad, kur mēs iegūstam datus? Ir vairāki esoši centieni, kas mēģina apkopot visu pasaules grāmatu sarakstu: Šajā ierakstā mēs ar prieku paziņojam par nelielu izlaidumu (salīdzinot ar mūsu iepriekšējiem Z-Library izlaidumiem). Mēs nokasījām lielāko daļu ISBNdb un padarījām datus pieejamus torrentēšanai Pirate Library Mirror vietnē (REDIĢĒT: pārvietots uz <a %(wikipedia_annas_archive)s>Annas Arhīvs</a>; mēs to šeit tieši nesasaistīsim, vienkārši meklējiet to). Tie ir aptuveni 30,9 miljoni ierakstu (20GB kā <a %(jsonlines)s>JSON Lines</a>; 4,4GB saspiesti). Viņu vietnē viņi apgalvo, ka viņiem faktiski ir 32,6 miljoni ierakstu, tāpēc mēs, iespējams, kaut kā esam palaiduši garām dažus, vai arī <em>viņi</em> varētu kaut ko darīt nepareizi. Jebkurā gadījumā, pagaidām mēs neizpaudīsim, kā mēs to izdarījām — mēs to atstāsim kā uzdevumu lasītājam. ;-) Ko mēs dalīsimies, ir daži sākotnējie analīzes rezultāti, lai mēģinātu tuvoties pasaules grāmatu skaita novērtēšanai. Mēs aplūkojām trīs datu kopas: šo jauno ISBNdb datu kopu, mūsu sākotnējo metadata izlaidumu, ko mēs nokasījām no Z-Library ēnu bibliotēkas (kas ietver Library Genesis), un Open Library datu izgāztuvi. ISBNdb izgāztuve, vai Cik daudz grāmatu ir saglabātas uz visiem laikiem? Ja mēs pareizi deduplicētu failus no ēnu bibliotēkām, kādu procentu no visām pasaules grāmatām mēs esam saglabājuši? Jaunumi par <a %(wikipedia_annas_archive)s>Annas Arhīvu</a>, lielāko patiesi atvērto bibliotēku cilvēces vēsturē. <em>WorldCat pārveide</em> Dati <strong>Formāts?</strong> <a %(blog)s>Annas Arhīva Konteineri (AAC)</a>, kas būtībā ir <a %(jsonlines)s>JSON Lines</a>, saspiesti ar <a %(zstd)s>Zstandard</a>, plus daži standartizēti semantikas. Šie konteineri aptver dažāda veida ierakstus, pamatojoties uz dažādiem nokasījumiem, ko mēs veicām. Pirms gada mēs <a %(blog)s>uzsākām</a> atbildēt uz šo jautājumu: <strong>Kāds procents grāmatu ir pastāvīgi saglabāts ēnu bibliotēkās?</strong> Apskatīsim dažus pamatdatus par datiem: Kad grāmata nonāk atvērtā datu ēnu bibliotēkā, piemēram, <a %(wikipedia_library_genesis)s>Library Genesis</a>, un tagad <a %(wikipedia_annas_archive)s>Annas Arhīvā</a>, tā tiek atspoguļota visā pasaulē (caur torrentiem), tādējādi praktiski saglabājot to uz visiem laikiem. Lai atbildētu uz jautājumu, kāds procents grāmatu ir saglabāts, mums jāzina saucējs: cik grāmatu kopumā pastāv? Un ideālā gadījumā mums nav tikai skaitlis, bet arī faktiska metadata. Tad mēs varam ne tikai salīdzināt tās ar ēnu bibliotēkām, bet arī <strong>izveidot TODO sarakstu ar atlikušajām grāmatām, kuras jāsaglabā!</strong> Mēs pat varētu sākt sapņot par pūļa veidotu centienu iziet cauri šim TODO sarakstam. Mēs nokasījām <a %(wikipedia_isbndb_com)s>ISBNdb</a> un lejupielādējām <a %(openlibrary)s>Open Library datu kopu</a>, taču rezultāti bija neapmierinoši. Galvenā problēma bija tā, ka ISBN pārklāšanās nebija liela. Skatiet šo Venn diagrammu no <a %(blog)s>mūsu emuāra ieraksta</a>: Mēs bijām ļoti pārsteigti par to, cik maz bija pārklāšanās starp ISBNdb un Open Library, kas abi plaši iekļauj datus no dažādiem avotiem, piemēram, tīmekļa nokasījumiem un bibliotēku ierakstiem. Ja abi labi veic darbu, atrodot lielāko daļu ISBN, to apļiem noteikti būtu jābūt ievērojamai pārklāšanās, vai arī vienam būtu jābūt otra apakškopai. Tas lika mums aizdomāties, cik daudz grāmatu atrodas <em>pilnīgi ārpus šiem apļiem</em>? Mums ir nepieciešama lielāka datubāze. Tad mēs pievērsāmies pasaulē lielākajai grāmatu datubāzei: <a %(wikipedia_worldcat)s>WorldCat</a>. Tā ir patentēta datubāze, ko pārvalda bezpeļņas organizācija <a %(wikipedia_oclc)s>OCLC</a>, kas apkopo metadata ierakstus no bibliotēkām visā pasaulē, apmaiņā pret to, ka šīs bibliotēkas saņem piekļuvi pilnai datu kopai un tiek parādītas gala lietotāju meklēšanas rezultātos. Lai gan OCLC ir bezpeļņas organizācija, viņu biznesa modelis prasa aizsargāt savu datubāzi. Nu, mēs atvainojamies, draugi OCLC, mēs to visu atdodam. :-) Pēdējā gada laikā mēs rūpīgi nokasījām visus WorldCat ierakstus. Sākumā mums paveicās. WorldCat tieši ieviesa pilnīgu vietnes pārveidi (2022. gada augustā). Tas ietvēra būtisku viņu aizmugures sistēmu pārveidi, ieviešot daudzas drošības nepilnības. Mēs nekavējoties izmantojām šo iespēju un spējām nokasīt simtiem miljonu (!) ierakstu tikai dažās dienās. Pēc tam drošības nepilnības tika lēnām labotas viena pēc otras, līdz pēdējā, ko atradām, tika aizlāpīta apmēram pirms mēneša. Līdz tam laikam mums bija gandrīz visi ieraksti, un mēs tikai centāmies iegūt nedaudz augstākas kvalitātes ierakstus. Tāpēc mēs jutām, ka ir laiks izlaist! 1,3B WorldCat nokasīšana <em><strong>TL;DR:</strong> Annas Arhīvs nokasīja visu WorldCat (pasaulē lielāko bibliotēkas metadata kolekciju), lai izveidotu TODO sarakstu ar grāmatām, kuras nepieciešams saglabāt.</em> WorldCat Brīdinājums: šis emuāra ieraksts ir novecojis. Mēs esam nolēmuši, ka IPFS vēl nav gatavs plašai lietošanai. Mēs joprojām saistīsimies ar failiem IPFS no Annas Arhīva, kad tas būs iespējams, bet mēs tos vairs neuzturēsim paši, un mēs neiesakām citiem spoguļot, izmantojot IPFS. Lūdzu, skatiet mūsu Torrents lapu, ja vēlaties palīdzēt saglabāt mūsu kolekciju. Palīdziet sēt Z-Bibliotēku IPFS Partneru servera lejupielāde SciDB Ārējā aizņemšanās Ārējā aizņemšanās (drukas ierobežojums) Ārējā lejupielāde Izpētīt metadatus Iekļauts torrentos Atpakaļ  (+%(num)s bonuss) neapmaksāts apmaksāts atcelts beidzies gaida Annu, lai apstiprinātu nederīgs Teksts zemāk turpinās angļu valodā. Iet Atiestatīt Uz priekšu Pēdējais Ja jūsu e-pasta adrese nedarbojas Libgen forumos, mēs iesakām izmantot <a %(a_mail)s>Proton Mail</a> (bezmaksas). Jūs varat arī <a %(a_manual)s>manuāli pieprasīt</a>, lai jūsu konts tiktu aktivizēts. (var būt nepieciešama <a %(a_browser)s>pārlūkprogrammas verifikācija</a> — neierobežotas lejupielādes!) Ātrais partneru serveris #%(number)s (ieteicams) (nedaudz ātrāks, bet ar gaidīšanas sarakstu) (nav nepieciešama pārlūkprogrammas verifikācija) (nav pārlūkprogrammas verifikācijas vai gaidīšanas sarakstu) (nav gaidīšanas saraksta, bet var būt ļoti lēns) Lēnais partneru serveris #%(number)s Audiogrāmata Komiksu grāmata Grāmata (daiļliteratūra) Grāmata (dokumentālā) Grāmata (nezināma) Žurnāla raksts Žurnāls Mūzikas partitūra Cits Standartu dokuments Ne visas lapas varēja konvertēt uz PDF Atzīmēts kā bojāts Libgen.li Nav redzams Libgen.li Nav redzams Libgen.rs Fiction Nav redzams Libgen.rs Non-Fiction Exiftool palaišana neizdevās šajā failā Atzīmēts kā “slikts fails” Z-Library Trūkst no Z-Library Atzīmēts kā “spam” Z-Library Failu nevar atvērt (piemēram, bojāts fails, DRM) Autortiesību prasība Lejupielādes problēmas (piemēram, nevar pieslēgties, kļūdas ziņojums, ļoti lēni) Nepareiza metadati (piemēram, nosaukums, apraksts, vāka attēls) Cits Slikta kvalitāte (piemēram, formatēšanas problēmas, slikta skenēšanas kvalitāte, trūkstošas lapas) Spams / fails jānoņem (piemēram, reklāmas, aizskarošs saturs) %(amount)s (%(amount_usd)s) %(amount)s kopā %(amount)s (%(amount_usd)s) kopā Brīnišķīgais Bibliofils Laimes Bibliotekārs Dzirkstošais Datuvācējs Apbrīnojamais Arhīvists Bonusa lejupielādes Cerlalc Čehu metadati DuXiu 读秀 EBSCOhost e-grāmatu indekss Google grāmatas Goodreads HathiTrust IA IA Kontrolētā Digitālā Aizdošana ISBNdb ISBN GRP Libgen.li Izslēdzot “scimag” Libgen.rs Daiļliteratūra un nedaiļliteratūra Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Krievijas Valsts bibliotēka Nederīgs pieprasījums. Apmeklējiet [X23X]. Caur Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Augšupielādes uz AA Z-Library Z-Library Chinese Nosaukums, autors, DOI, ISBN, MD5, … Meklēt Autors Apraksts un metadatu komentāri Izdevums Oriģinālais faila nosaukums Izdevējs (meklēt specifisku lauku) Nosaukums Publicēšanas gads Tehniskās detaļas Šai monētai ir augstāks minimālais limits nekā parasti. Lūdzu, izvēlieties citu ilgumu vai citu monētu. Pieprasījumu nevarēja izpildīt. Lūdzu, mēģiniet vēlreiz pēc dažām minūtēm, un, ja problēma turpinās, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu. Radās nezināma kļūda. Lūdzu, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu. Maksājumu apstrādes kļūda. Lūdzu, uzgaidiet mirkli un mēģiniet vēlreiz. Ja problēma saglabājas vairāk nekā 24 stundas, lūdzu, sazinieties ar mums pa %(email)s un pievienojiet ekrānuzņēmumu. Mēs rīkojam līdzekļu vākšanu, lai <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">dublētu</a> lielāko komiksu ēnu bibliotēku pasaulē. Paldies par jūsu atbalstu! <a href="/donate">Ziedot.</a> Ja nevarat ziedot, apsveriet iespēju atbalstīt mūs, pastāstot draugiem un sekojot mums <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> vai <a href="https://t.me/annasarchiveorg">Telegram</a>. Nesūtiet mums e-pastu, lai <a %(a_request)s>pieprasītu grāmatas</a><br>vai nelielus (<10k) <a %(a_upload)s>augšupielādes</a>. Annas Arhīvs DMCA / autortiesību prasības Sazinieties ar mums Reddit Alternatīvas SLUM (%(unaffiliated)s) nepiesaistīts Annas Arhīvam ir nepieciešama jūsu palīdzība! Ja ziedosiet tagad, jūs saņemsiet <strong>dubultu</strong> ātro lejupielāžu skaitu. Daudzi mēģina mūs apturēt, bet mēs cīnāmies pretī. Ja šomēnes ziedosiet, jūs saņemsiet <strong>dubultu</strong> ātro lejupielāžu skaitu. Derīgs līdz šī mēneša beigām. Cilvēces zināšanu saglabāšana: lieliska dāvana svētkos! Dalības termiņi tiks attiecīgi pagarināti. Partneru serveri nav pieejami hostinga slēgšanas dēļ. Tie drīzumā atkal būs pieejami. Lai palielinātu Annas Arhīva noturību, mēs meklējam brīvprātīgos, kas varētu uzturēt spoguļus. Mums ir pieejama jauna ziedošanas metode: %(method_name)s. Lūdzu, apsveriet %(donate_link_open_tag)sziedot</a> — šīs vietnes uzturēšana nav lēta, un jūsu ziedojums patiešām ir nozīmīgs. Liels paldies. Iesakiet draugu, un gan jūs, gan jūsu draugs saņems %(percentage)s%% bonusa ātrās lejupielādes! Pārsteidziet mīļoto, uzdāviniet viņam kontu ar dalību. Ideāla Valentīndienas dāvana! Uzzināt vairāk… Konts Aktivitāte Paplašināts Annas Blogs ↗ Annas Programmatūra ↗ beta Kodu izpēte Datu kopas Ziedot Lejupielādētie faili BUJ Sākums Uzlabot metadatus LLM dati Pieteikties / Reģistrēties Mani ziedojumi Publiskais profils Meklēt Drošība Torrenti Tulkot ↗ Brīvprātīgie un atlīdzības Nesenās lejupielādes: 📚&nbsp;Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka. ⭐️&nbsp;Spoguļo Sci-Hub, Library Genesis, Z-Library un vēl. 📈&nbsp;%(book_any)s grāmatas, %(journal_article)s raksti, %(book_comic)s komiksi, %(magazine)s žurnāli — saglabāti uz visiem laikiem.  un  un vēl DuXiu Internet Archive Aizdevumu bibliotēka LibGen 📚&nbsp;Lielākā patiesi atvērtā bibliotēka cilvēces vēsturē. 📈&nbsp;%(book_count)s&nbsp;grāmatas, %(paper_count)s&nbsp;raksti — saglabāti uz visiem laikiem. ⭐️&nbsp;Mēs spoguļojam %(libraries)s. Mēs nokasām un atveram avotu %(scraped)s. Visi mūsu kodi un dati ir pilnīgi atvērti. OpenLib Sci-Hub ,  📚 Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka.<br>⭐️ Spoguļo Scihub, Libgen, Zlib un vēl. Z-Lib Annas arhīvs Nederīgs pieprasījums. Apmeklējiet %(websites)s. Pasaulē lielākā atvērtā pirmkoda un atvērto datu bibliotēka. Spoguļo Sci-Hub, Library Genesis, Z-Library un citus. Meklēt Annas Arhīvā Annas Arhīvs Lūdzu, atsvaidziniet, lai mēģinātu vēlreiz. <a %(a_contact)s>Sazinieties ar mums</a>, ja problēma saglabājas vairākas stundas. 🔥 Problēma, ielādējot šo lapu <li>1. Sekojiet mums <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> vai <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Izplatiet informāciju par Annas Arhīvu Twitter, Reddit, Tiktok, Instagram, vietējā kafejnīcā vai bibliotēkā, vai kur vien ejat! Mēs neticam vārtu sargāšanai — ja mūs izņems, mēs vienkārši parādīsimies citur, jo visi mūsu kodi un dati ir pilnībā atvērta pirmkoda.</li><li>3. Ja varat, apsveriet iespēju <a href="/donate">ziedot</a>.</li><li>4. Palīdziet <a href="https://translate.annas-software.org/">tulkot</a> mūsu vietni dažādās valodās.</li><li>5. Ja esat programmatūras inženieris, apsveriet iespēju piedalīties mūsu <a href="https://annas-software.org/">atvērtā pirmkoda</a> projektos vai sēdēt mūsu <a href="/datasets">torrentus</a>.</li> 10. Izveidojiet vai palīdziet uzturēt Annas Arhīva Wikipedia lapu savā valodā. 11. Mēs meklējam iespējas izvietot nelielas, gaumīgas reklāmas. Ja vēlaties reklamēties vietnē Annas Arhīvs, lūdzu, sazinieties ar mums. 6. Ja esat drošības pētnieks, mēs varam izmantot jūsu prasmes gan uzbrukumam, gan aizsardzībai. Apskatiet mūsu <a %(a_security)s>Drošības</a> lapu. 7. Mēs meklējam ekspertus maksājumu jomā anonīmiem tirgotājiem. Vai varat palīdzēt mums pievienot ērtākus ziedošanas veidus? PayPal, WeChat, dāvanu kartes. Ja zināt kādu, lūdzu, sazinieties ar mums. 8. Mēs vienmēr meklējam vairāk serveru jaudas. 9. Jūs varat palīdzēt, ziņojot par failu problēmām, atstājot komentārus un veidojot sarakstus tieši šajā vietnē. Jūs varat arī palīdzēt, <a %(a_upload)s>augšupielādējot vairāk grāmatu</a> vai labojot esošo grāmatu failu problēmas vai formatējumu. Lai iegūtu plašāku informāciju par to, kā kļūt par brīvprātīgo, skatiet mūsu <a %(a_volunteering)s>Brīvprātīgo un atlīdzību</a> lapu. Mēs stingri ticam informācijas brīvai plūsmai un zināšanu un kultūras saglabāšanai. Ar šo meklētājprogrammu mēs balstāmies uz milžu pleciem. Mēs dziļi cienām cilvēku smago darbu, kuri ir izveidojuši dažādas ēnu bibliotēkas, un ceram, ka šī meklētājprogramma paplašinās to sasniedzamību. Lai sekotu līdzi mūsu progresam, sekojiet Annai <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> vai <a href="https://t.me/annasarchiveorg">Telegram</a>. Jautājumiem un atsauksmēm, lūdzu, sazinieties ar Annu pa %(email)s. Konta ID: %(account_id)s Izrakstīties ❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. ✅ Jūs esat izrakstījies. Pārlādējiet lapu, lai atkal pieteiktos. Ātrie lejupielādes izmantoti (pēdējās 24 stundās): <strong>%(used)s / %(total)s</strong> Dalība: <strong>%(tier_name)s</strong> līdz %(until_date)s <a %(a_extend)s>(pagarināt)</a> Jūs varat apvienot vairākas dalības (ātrās lejupielādes 24 stundu laikā tiks summētas). Dalība: <strong>Nav</strong> <a %(a_become)s>(kļūt par biedru)</a> Sazinieties ar Annu pa %(email)s, ja vēlaties paaugstināt savu dalību uz augstāku līmeni. Publiskais profils: %(profile_link)s Slepenā atslēga (nedalieties!): %(secret_key)s rādīt Pievienojieties mums šeit! Pārejiet uz <a %(a_tier)s>augstāku līmeni</a>, lai pievienotos mūsu grupai. Ekskluzīva Telegram grupa: %(link)s Konts kuras lejupielādes? Pieteikties Nepazaudējiet savu atslēgu! Nederīga slepenā atslēga. Pārbaudiet savu atslēgu un mēģiniet vēlreiz, vai arī reģistrējiet jaunu kontu zemāk. Slepenā atslēga Ievadiet savu slepeno atslēgu, lai pieteiktos: Vecais e-pasta konts? Ievadiet savu <a %(a_open)s>e-pastu šeit</a>. Reģistrēt jaunu kontu Vēl nav konta? Reģistrācija veiksmīga! Jūsu slepenā atslēga ir: <span %(span_key)s>%(key)s</span> Saglabājiet šo atslēgu rūpīgi. Ja to pazaudēsiet, jūs zaudēsiet piekļuvi savam kontam. <li %(li_item)s><strong>Grāmatzīme.</strong> Jūs varat pievienot šo lapu grāmatzīmēm, lai atgūtu savu atslēgu.</li><li %(li_item)s><strong>Lejupielādēt.</strong> Noklikšķiniet <a %(a_download)s>uz šīs saites</a>, lai lejupielādētu savu atslēgu.</li><li %(li_item)s><strong>Paroles pārvaldnieks.</strong> Izmantojiet paroles pārvaldnieku, lai saglabātu atslēgu, kad to ievadāt zemāk.</li> Pieteikties / Reģistrēties Pārlūka verifikācija Brīdinājums: kodā ir nepareizi Unicode rakstzīmes, un tas var darboties nepareizi dažādās situācijās. Neapstrādāto bināro var dekodēt no base64 attēlojuma URL. Apraksts Etiķete Prefikss URL konkrētam kodam Vietne Kodus, kas sākas ar “%(prefix_label)s” Lūdzu, neskrāpējiet šīs lapas. Tā vietā mēs iesakām <a %(a_import)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes un palaist mūsu <a %(a_software)s>atvērto pirmkodu</a>. Neapstrādātie dati var tikt manuāli izpētīti caur JSON failiem, piemēram, <a %(a_json_file)s>šo</a>. Mazāk nekā %(count)s ieraksti Vispārīgs URL Kodu Pētnieks Indekss Izpētiet kodus, ar kuriem ieraksti ir atzīmēti, pēc prefiksa. Kolonnā “ieraksti” ir redzams, cik ierakstu ir atzīmēti ar kodiem ar doto prefiksu, kā redzams meklētājā (ieskaitot tikai metadatu ierakstus). Kolonnā “kodi” ir redzams, cik faktisko kodu ir ar doto prefiksu. Zināms koda prefikss “%(key)s” Vairāk… Prefikss page.codes.record_starting_with page.codes.records_starting_with %(count)s ieraksti, kas atbilst “%(prefix_label)s” kodi ieraksti “%%s” tiks aizstāts ar koda vērtību Meklēt Annas Arhīvā Kodi URL konkrētam kodam: “%(url)s” Šīs lapas ģenerēšana var aizņemt kādu laiku, tāpēc ir nepieciešama Cloudflare captcha. <a %(a_donate)s>Biedri</a> var izlaist captchu. Ziņots par ļaunprātību: Labāka versija Vai vēlaties ziņot par šo lietotāju par ļaunprātīgu vai neatbilstošu uzvedību? Faila problēma: %(file_issue)s slēpts komentārs Atbildēt Ziņot par ļaunprātību Jūs ziņojāt par šo lietotāju par ļaunprātību. Autortiesību prasības uz šo e-pastu tiks ignorētas; tā vietā izmantojiet veidlapu. Rādīt e-pastu Mēs ļoti priecājamies par jūsu atsauksmēm un jautājumiem! Tomēr, ņemot vērā saņemto surogātpasta un bezjēdzīgo e-pastu daudzumu, lūdzu, atzīmējiet rūtiņas, lai apstiprinātu, ka saprotat šos nosacījumus, lai sazinātos ar mums. Jebkuri citi veidi, kā sazināties ar mums par autortiesību prasībām, tiks automātiski dzēsti. DMCA / autortiesību prasībām izmantojiet <a %(a_copyright)s>šo veidlapu</a>. Kontakta e-pasts URL adreses Annas Arhīvā (obligāti). Viena uz rindas. Lūdzu, iekļaujiet tikai URL adreses, kas apraksta tieši to pašu grāmatas izdevumu. Ja vēlaties iesniegt prasību par vairākām grāmatām vai vairākiem izdevumiem, lūdzu, iesniedziet šo veidlapu vairākas reizes. Prasības, kas apvieno vairākas grāmatas vai izdevumus, tiks noraidītas. Adrese (obligāti) Skaidrs avota materiāla apraksts (obligāti) E-pasts (obligāti) Avota materiāla URL adreses, viena uz rindas (obligāti). Lūdzu, iekļaujiet pēc iespējas vairāk, lai palīdzētu mums pārbaudīt jūsu prasību (piemēram, Amazon, WorldCat, Google Books, DOI). Avota materiāla ISBN numuri (ja piemērojams). Viena uz rindas. Lūdzu, iekļaujiet tikai tos, kas precīzi atbilst izdevumam, par kuru ziņojat autortiesību prasību. Jūsu vārds (obligāti) ❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. ✅ Paldies, ka iesniedzāt savu autortiesību prasību. Mēs to pārskatīsim pēc iespējas ātrāk. Lūdzu, pārlādējiet lapu, lai iesniegtu vēl vienu. <a %(a_openlib)s>Open Library</a> avota materiāla URL adreses, viena uz rindas. Lūdzu, veltiet brīdi, lai meklētu savu avota materiālu Open Library. Tas palīdzēs mums pārbaudīt jūsu prasību. Telefona numurs (obligāti) Paziņojums un paraksts (obligāti) Iesniegt prasību Ja jums ir DCMA vai cita autortiesību prasība, lūdzu, aizpildiet šo veidlapu pēc iespējas precīzāk. Ja rodas kādas problēmas, lūdzu, sazinieties ar mums, izmantojot mūsu īpašo DMCA adresi: %(email)s. Ņemiet vērā, ka prasības, kas nosūtītas uz šo adresi, netiks apstrādātas, tā ir paredzēta tikai jautājumiem. Lūdzu, izmantojiet zemāk esošo veidlapu, lai iesniegtu savas prasības. DMCA / Autortiesību prasības veidlapa Piemēra ieraksts Annas Arhīvā Torrenti no Annas Arhīva Annas Arhīva Konteineru formāts Skripti metadatu importēšanai Ja jūs interesē šī datu kopuma spoguļošana <a %(a_archival)s>arhivēšanas</a> vai <a %(a_llm)s>LLM apmācības</a> nolūkos, lūdzu, sazinieties ar mums. Pēdējoreiz atjaunināts: %(date)s Galvenā %(source)s vietne Metadatu dokumentācija (vairums lauku) Faili, ko spoguļo Annas Arhīvs: %(count)s (%(percent)s%%) Resursi Kopējais failu skaits: %(count)s Kopējais failu izmērs: %(size)s Mūsu emūru ieraksts par šiem datiem <a %(duxiu_link)s>Duxiu</a> ir milzīga skenēto grāmatu datubāze, ko izveidojusi <a %(superstar_link)s>SuperStar Digital Library Group</a>. Lielākā daļa ir akadēmiskās grāmatas, kas skenētas, lai padarītu tās pieejamas digitāli universitātēm un bibliotēkām. Mūsu angliski runājošajai auditorijai <a %(princeton_link)s>Princeton</a> un <a %(uw_link)s>Vašingtonas Universitāte</a> piedāvā labus pārskatus. Ir arī lielisks raksts, kas sniedz vairāk informācijas: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Duxiu grāmatas jau ilgu laiku tiek pirātētas Ķīnas internetā. Parasti tās tiek pārdotas par mazāk nekā dolāru no tālākpārdevējiem. Tās parasti tiek izplatītas, izmantojot Ķīnas ekvivalentu Google Drive, kas bieži tiek uzlauzts, lai nodrošinātu lielāku krātuves vietu. Dažas tehniskās detaļas var atrast <a %(link1)s>šeit</a> un <a %(link2)s>šeit</a>. Lai gan grāmatas ir daļēji publiski izplatītas, tās ir diezgan grūti iegūt lielos apjomos. Mums tas bija augstu mūsu TODO sarakstā, un mēs tam piešķīrām vairākus mēnešus pilna laika darba. Tomēr 2023. gada beigās neticams, apbrīnojams un talantīgs brīvprātīgais sazinājās ar mums, sakot, ka viņi jau ir paveikuši visu šo darbu — par lielām izmaksām. Viņi dalījās ar mums pilnu kolekciju, negaidot neko pretī, izņemot ilgtermiņa saglabāšanas garantiju. Patiesi ievērojami. Vairāk informācijas no mūsu brīvprātīgajiem (neapstrādātas piezīmes): Pielāgots no mūsu <a %(a_href)s>emūru ieraksta</a>. DuXiu 读秀 page.datasets.file page.datasets.files %(count)s faili Šis datu kopums ir cieši saistīts ar <a %(a_datasets_openlib)s>Open Library datu kopumu</a>. Tas satur visu metadatu un lielas daļas failu no IA kontrolētās digitālās aizdevumu bibliotēkas nokasījumu. Atjauninājumi tiek izlaisti <a %(a_aac)s>Annas Arhīva konteineru formātā</a>. Šie ieraksti tiek tieši atsaukti no Open Library datu kopuma, bet satur arī ierakstus, kas nav Open Library. Mums ir arī vairāki datu faili, kurus gadu gaitā nokasījuši kopienas locekļi. Kolekcija sastāv no divām daļām. Jums ir nepieciešamas abas daļas, lai iegūtu visus datus (izņemot aizstātās torrentus, kas ir izsvītrotas torrentu lapā). Digitālā Aizdošanas Bibliotēka mūsu pirmais izlaidums, pirms mēs standartizējām <a %(a_aac)s>Annas Arhīva Konteineru (AAC) formātu</a>. Satur metadatus (kā json un xml), pdf failus (no acsm un lcpdf digitālās aizdošanas sistēmām) un vāku sīktēlus. pakāpeniski jauni izlaidumi, izmantojot AAC. Satur tikai metadatus ar laika zīmogiem pēc 2023-01-01, jo pārējo jau aptver “ia”. Tāpat visi pdf faili, šoreiz no acsm un “bookreader” (IA tīmekļa lasītājs) aizdošanas sistēmām. Lai gan nosaukums nav pilnīgi precīzs, mēs joprojām ievietojam bookreader failus ia2_acsmpdf_files kolekcijā, jo tie ir savstarpēji izslēdzoši. IA Kontrolētā Digitālā Aizdošana 98%%+ failu ir meklējami. Mūsu misija ir arhivēt visas pasaules grāmatas (kā arī rakstus, žurnālus utt.) un padarīt tās plaši pieejamas. Mēs uzskatām, ka visas grāmatas būtu jāspoguļo plaši, lai nodrošinātu redundanci un noturību. Tāpēc mēs apvienojam failus no dažādiem avotiem. Daži avoti ir pilnīgi atvērti un var tikt spoguļoti lielapjomā (piemēram, Sci-Hub). Citi ir slēgti un aizsargāti, tāpēc mēs cenšamies tos nokasīt, lai “atbrīvotu” to grāmatas. Vēl citi atrodas kaut kur pa vidu. Visi mūsu dati var tikt <a %(a_torrents)s>torrenti</a>, un visi mūsu metadati var tikt <a %(a_anna_software)s>ģenerēti</a> vai <a %(a_elasticsearch)s>lejupielādēti</a> kā ElasticSearch un MariaDB datubāzes. Neapstrādātie dati var tikt manuāli izpētīti caur JSON failiem, piemēram, <a %(a_dbrecord)s>šo</a>. Metadati ISBN mājaslapa Pēdējoreiz atjaunināts: %(isbn_country_date)s (%(link)s) Resursi Starptautiskā ISBN aģentūra regulāri izplata diapazonus, kurus tā ir piešķīrusi nacionālajām ISBN aģentūrām. No tā mēs varam noteikt, kurai valstij, reģionam vai valodu grupai šis ISBN pieder. Pašlaik mēs izmantojam šos datus netieši, izmantojot <a %(a_isbnlib)s>isbnlib</a> Python bibliotēku. ISBN valsts informācija Šis ir daudz zvanu uz isbndb.com izgāztuve 2022. gada septembrī. Mēs mēģinājām aptvert visus ISBN diapazonus. Tie ir aptuveni 30,9 miljoni ierakstu. Viņu mājaslapā viņi apgalvo, ka viņiem faktiski ir 32,6 miljoni ierakstu, tāpēc mēs, iespējams, kaut kā esam palaiduši garām dažus, vai <em>viņi</em> varētu kaut ko darīt nepareizi. JSON atbildes ir gandrīz neapstrādātas no viņu servera. Viena datu kvalitātes problēma, ko mēs pamanījām, ir tā, ka ISBN-13 numuriem, kas sākas ar citu prefiksu nekā “978-”, viņi joprojām iekļauj “isbn” lauku, kas vienkārši ir ISBN-13 numurs ar pirmajiem 3 numuriem noņemtiem (un pārbaudes cipars pārrēķināts). Tas acīmredzami ir nepareizi, bet tā viņi to dara, tāpēc mēs to nemainījām. Vēl viena potenciāla problēma, ar kuru jūs varētu saskarties, ir fakts, ka “isbn13” laukam ir dublikāti, tāpēc jūs to nevarat izmantot kā primāro atslēgu datubāzē. “isbn13”+“isbn” lauki kopā šķiet unikāli. Izlaidums 1 (2022-10-31) Daiļliteratūras torrenti ir aizkavēti (lai gan ID ~4-6M nav torrentēti, jo tie pārklājas ar mūsu Zlib torrentiem). Mūsu emuāra ieraksts par komiksu grāmatu izdošanu Komiksu torrenti Annas Arhīvā Par dažādu Library Genesis dakšu vēsturi skatiet lapu <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li satur lielāko daļu tā paša satura un metadatu kā Libgen.rs, bet tam ir dažas papildu kolekcijas, proti, komiksi, žurnāli un standarta dokumenti. Tas ir arī integrējis <a %(a_scihub)s>Sci-Hub</a> savā metadatu un meklēšanas dzinējā, ko mēs izmantojam mūsu datubāzei. Šīs bibliotēkas metadati ir brīvi pieejami <a %(a_libgen_li)s>libgen.li</a>. Tomēr šis serveris ir lēns un neatbalsta pārtrauktu savienojumu atsākšanu. Tie paši faili ir pieejami arī <a %(a_ftp)s>FTP serverī</a>, kas darbojas labāk. Šķiet, ka arī daiļliteratūra ir novirzījusies, taču bez jauniem straumēm. Šķiet, ka tas ir noticis kopš 2022. gada sākuma, lai gan mēs to neesam pārbaudījuši. Saskaņā ar Libgen.li administratoru, “fiction_rus” (krievu daiļliteratūra) kolekcijai vajadzētu būt segtai ar regulāri izlaistiem torrentiem no <a %(a_booktracker)s>booktracker.org</a>, īpaši <a %(a_flibusta)s>flibusta</a> un <a %(a_librusec)s>lib.rus.ec</a> torrentiem (kurus mēs spogulējam <a %(a_torrents)s>šeit</a>, lai gan vēl neesam noteikuši, kuri torrenti atbilst kuriem failiem). Daiļliteratūras kolekcijai ir savi torrenti (atšķirīgi no <a %(a_href)s>Libgen.rs</a>), sākot no %(start)s. Noteikti diapazoni bez torrentiem (piemēram, daiļliteratūras diapazoni f_3463000 līdz f_4260000) visticamāk ir Z-Library (vai citi dublikāti) faili, lai gan mēs varētu vēlēties veikt deduplikāciju un izveidot torrentus lgli-unikāliem failiem šajos diapazonos. Statistiku par visām kolekcijām var atrast <a %(a_href)s>libgen vietnē</a>. Torrenti ir pieejami lielākajai daļai papildu satura, īpaši komiksu, žurnālu un standarta dokumentu torrenti ir izlaisti sadarbībā ar Annas Arhīvu. Ņemiet vērā, ka torrentu faili, kas atsaucas uz “libgen.is”, ir tieši <a %(a_libgen)s>Libgen.rs</a> spoguļi (“.is” ir cita domēna, ko izmanto Libgen.rs). Noderīgs resurss metadatu izmantošanai ir <a %(a_href)s>šī lapa</a>. %(icon)s Viņu “fiction_rus” kolekcijai (krievu daiļliteratūra) nav īpašu torrentu, bet to sedz citu izlaistie torrenti, un mēs uzturam <a %(fiction_rus)s>spoguli</a>. Krievu daiļliteratūras torrenti Annas Arhīvā Daiļliteratūras torrenti Annas Arhīvā Diskusiju forums Metadati Metadati caur FTP Žurnālu torrenti Annas Arhīvā Metadatu lauku informācija Citu torrentu spogulis (un unikāli daiļliteratūras un komiksu torrenti) Standarta dokumentu torrenti Annas Arhīvā Libgen.li Torenti no Annas Arhīva (grāmatu vāki) Library Genesis ir pazīstams ar to, ka jau dāsni padara savus datus pieejamus lielos apjomos caur torentiem. Mūsu Libgen kolekcija sastāv no papildu datiem, kurus viņi tieši neizlaiž, sadarbojoties ar viņiem. Liels paldies visiem, kas iesaistīti Library Genesis darbā ar mums! Mūsu emuārs par grāmatu vāku izlaidumu Šī lapa ir par “.rs” versiju. Tā ir pazīstama ar to, ka konsekventi publicē gan savus metadatus, gan pilnu grāmatu kataloga saturu. Tās grāmatu kolekcija ir sadalīta starp daiļliteratūras un nedaiļliteratūras daļām. Noderīgs resurss metadatu izmantošanai ir <a %(a_metadata)s>šī lapa</a> (bloķē IP diapazonus, var būt nepieciešams VPN). No 2024. gada marta jauni torenti tiek publicēti <a %(a_href)s>šajā foruma pavedienā</a> (bloķē IP diapazonus, var būt nepieciešams VPN). Fantastikas torenti Annas Arhīvā Libgen.rs Fantastikas torenti Libgen.rs Diskusiju forums Libgen.rs Metadati Libgen.rs metadatu lauku informācija Libgen.rs Ne-fantastikas torenti Ne-fantastikas torenti Annas Arhīvā %(example)s fantastikas grāmatai. Šis <a %(blog_post)s>pirmais izlaidums</a> ir diezgan mazs: aptuveni 300GB grāmatu vāku no Libgen.rs dakšas, gan fantastikas, gan ne-fantastikas. Tie ir organizēti tāpat kā tie parādās libgen.rs, piemēram: %(example)s ne-fantastikas grāmatai. Tāpat kā ar Z-Library kolekciju, mēs tos visus ievietojām lielā .tar failā, kuru var uzmontēt, izmantojot <a %(a_ratarmount)s>ratarmount</a>, ja vēlaties failus tieši apkalpot. Izlaidums 1 (%(date)s) Īss stāsts par dažādiem Library Genesis (vai “Libgen”) atzariem ir tāds, ka laika gaitā dažādi cilvēki, kas bija iesaistīti Library Genesis, sastrīdējās un devās katrs savu ceļu. Saskaņā ar šo <a %(a_mhut)s>foruma ierakstu</a>, Libgen.li sākotnēji tika mitināts vietnē “http://free-books.dontexist.com”. “.fun” versiju izveidoja sākotnējais dibinātājs. Tā tiek pārveidota par jaunu, vairāk izplatītu versiju. <a %(a_li)s>“.li” versijai</a> ir milzīga komiksu kolekcija, kā arī cits saturs, kas (vēl) nav pieejams lielapjoma lejupielādei caur torrentiem. Tai ir atsevišķa daiļliteratūras grāmatu torrentu kolekcija, un tās datubāzē ir <a %(a_scihub)s>Sci-Hub</a> metadati. “.rs” versijai ir ļoti līdzīgi dati, un tā viskonsekventāk izlaiž savu kolekciju lielapjoma torrentos. Tā ir aptuveni sadalīta “daiļliteratūras” un “nedaiļliteratūras” sadaļās. Sākotnēji pieejams “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> zināmā mērā arī ir Library Genesis atzars, lai gan viņi savam projektam izmantoja citu nosaukumu. Libgen.rs Mēs arī bagātinām savu kolekciju ar tikai metadatu avotiem, kurus varam saskaņot ar failiem, piemēram, izmantojot ISBN numurus vai citus laukus. Zemāk ir pārskats par tiem. Atkal, daži no šiem avotiem ir pilnīgi atvērti, savukārt citus mums ir jānokasa. Ņemiet vērā, ka metadatu meklēšanā mēs parādām oriģinālos ierakstus. Mēs neveicam ierakstu apvienošanu. Tikai metadatu avoti Open Library ir atvērtā koda projekts no Internet Archive, lai katalogizētu visas pasaules grāmatas. Tam ir viena no pasaulē lielākajām grāmatu skenēšanas operācijām, un tam ir daudz grāmatu, kas pieejamas digitālai aizņemšanai. Tā grāmatu metadatu katalogs ir brīvi pieejams lejupielādei un ir iekļauts Annas Arhīvā (lai gan pašlaik nav meklēšanā, izņemot, ja jūs tieši meklējat Open Library ID). Open Library Izslēdzot dublikātus Pēdējo reizi atjaunināts Failu skaita procenti %% spoguļots ar AA / pieejami torrenti Izmērs Avots Zemāk ir ātrs pārskats par failu avotiem Annas Arhīvā. Tā kā ēnu bibliotēkas bieži sinhronizē datus viena ar otru, starp bibliotēkām ir ievērojama pārklāšanās. Tāpēc skaitļi nesummējas līdz kopējam skaitam. “Anna’s Archive spoguļots un sēklots” procents parāda, cik daudz failu mēs paši spoguļojam. Mēs sēklojam šos failus masveidā caur torrentiem un padarām tos pieejamus tiešai lejupielādei caur partneru vietnēm. Pārskats Kopā Torrenti Annas Arhīvā Lai iegūtu informāciju par Sci-Hub, lūdzu, skatiet tā <a %(a_scihub)s>oficiālo vietni</a>, <a %(a_wikipedia)s>Wikipedia lapu</a> un šo <a %(a_radiolab)s>podkāsta interviju</a>. Ņemiet vērā, ka Sci-Hub ir <a %(a_reddit)s>iesaldēts kopš 2021. gada</a>. Tas bija iesaldēts arī agrāk, bet 2021. gadā tika pievienoti daži miljoni rakstu. Tomēr ierobežots skaits rakstu joprojām tiek pievienots Libgen “scimag” kolekcijām, lai gan nepietiekami, lai izveidotu jaunus lielapjoma torrentus. Mēs izmantojam Sci-Hub metadatus, ko nodrošina <a %(a_libgen_li)s>Libgen.li</a> savā “scimag” kolekcijā. Mēs arī izmantojam <a %(a_dois)s>dois-2022-02-12.7z</a> datu kopu. Ņemiet vērā, ka “smarch” torrenti ir <a %(a_smarch)s>novecojuši</a> un tāpēc nav iekļauti mūsu torrentu sarakstā. Torrenti Libgen.li Torrenti Libgen.rs Metadati un torrenti Atjauninājumi Reddit Podkāsta intervija Wikipedia lapa Sci-Hub Sci-Hub: iesaldēts kopš 2021. gada; lielākā daļa pieejama caur torrentiem Libgen.li: nelieli papildinājumi kopš tā laika</div> Dažas avotu bibliotēkas veicina savu datu masveida koplietošanu, izmantojot torrentus, savukārt citas ne tik viegli dalās ar savu kolekciju. Pēdējā gadījumā Anna’s Archive mēģina nokasīt viņu kolekcijas un padarīt tās pieejamas (skatiet mūsu <a %(a_torrents)s>Torrenti</a> lapu). Ir arī starpposma situācijas, piemēram, kad avotu bibliotēkas ir gatavas dalīties, bet tām nav resursu to darīt. Šādos gadījumos mēs arī cenšamies palīdzēt. Zemāk ir pārskats par to, kā mēs sadarbojamies ar dažādām avotu bibliotēkām. Avotu bibliotēkas %(icon)s Dažādas failu datubāzes, kas izkaisītas Ķīnas internetā; bieži vien maksas datubāzes %(icon)s Lielākā daļa failu pieejami tikai ar premium BaiduYun kontiem; lēns lejupielādes ātrums. %(icon)s Annas Arhīvs pārvalda <a %(duxiu)s>DuXiu failu</a> kolekciju %(icon)s Dažādas metadatu datubāzes izkaisītas pa Ķīnas internetu; bieži vien maksas datubāzes %(icon)s Nav viegli pieejamu metadatu izgāztuvju visai kolekcijai. %(icon)s Annas Arhīvs pārvalda <a %(duxiu)s>DuXiu metadatu</a> kolekciju Faili %(icon)s Faili pieejami tikai ierobežotai aizņemšanai, ar dažādiem piekļuves ierobežojumiem %(icon)s Annas Arhīvs pārvalda <a %(ia)s>IA failu</a> kolekciju %(icon)s Daži metadati pieejami caur <a %(openlib)s>Open Library datubāzes izgāztuvēm</a>, bet tie neaptver visu IA kolekciju %(icon)s Nav viegli pieejamu metadatu izgāztuvju visai kolekcijai %(icon)s Annas Arhīvs pārvalda <a %(ia)s>IA metadatu</a> kolekciju Pēdējo reizi atjaunināts %(icon)s Annas Arhīvs un Libgen.li kopīgi pārvalda <a %(comics)s>komiksu grāmatu</a>, <a %(magazines)s>žurnālu</a>, <a %(standarts)s>standarta dokumentu</a> un <a %(fiction)s>daiļliteratūras (atšķirīga no Libgen.rs)</a> kolekcijas. %(icon)s Nedaiļliteratūras torrenti tiek kopīgoti ar Libgen.rs (un spoguļoti <a %(libgenli)s>šeit</a>). %(icon)s Ceturkšņa <a %(dbdumps)s>HTTP datubāzes izgāztuves</a> %(icon)s Automātiskie torrenti <a %(nonfiction)s>daiļliteratūrai</a> un <a %(fiction)s>nedaiļliteratūrai</a> %(icon)s Annas Arhīvs pārvalda <a %(covers)s>grāmatu vāku torrentu</a> kolekciju %(icon)s Ikdienas <a %(dbdumps)s>HTTP datubāzes izgāztuves</a> Metadati %(icon)s Mēneša <a %(dbdumps)s>datubāzes izgāztuves</a> %(icon)s Datu torrenti pieejami <a %(scihub1)s>šeit</a>, <a %(scihub2)s>šeit</a> un <a %(libgenli)s>šeit</a> %(icon)s Daži jauni faili tiek <a %(libgenrs)s>pievienoti</a> Libgen “scimag”, bet nepietiekami, lai izveidotu jaunus torrentus %(icon)s Sci-Hub kopš 2021. gada nav pievienojis jaunus failus. %(icon)s Metadatu izgāztuves pieejamas <a %(scihub1)s>šeit</a> un <a %(scihub2)s>šeit</a>, kā arī kā daļa no <a %(libgenli)s>Libgen.li datubāzes</a> (ko mēs izmantojam) Avots %(icon)s Dažādi mazāki vai vienreizēji avoti. Mēs mudinām cilvēkus vispirms augšupielādēt citās ēnu bibliotēkās, bet dažreiz cilvēkiem ir kolekcijas, kas ir pārāk lielas, lai citi tās varētu sakārtot, bet ne pietiekami lielas, lai tām būtu sava kategorija. %(icon)s Nav pieejams tieši lielapjomā, aizsargāts pret skrāpēšanu %(icon)s Annas Arhīvs pārvalda <a %(worldcat)s>OCLC (WorldCat) metadatu</a> kolekciju %(icon)s Annas Arhīvs un Z-Library kopīgi pārvalda <a %(metadata)s>Z-Library metadatu</a> un <a %(files)s>Z-Library failu</a> kolekciju Datu kopumi Mēs apvienojam visus iepriekš minētos avotus vienotā datubāzē, kuru izmantojam šīs vietnes apkalpošanai. Šī vienotā datubāze nav pieejama tieši, bet, tā kā Anna’s Archive ir pilnīgi atvērta pirmkoda, to var diezgan viegli <a %(a_generated)s>ģenerēt</a> vai <a %(a_downloaded)s>lejupielādēt</a> kā ElasticSearch un MariaDB datubāzes. Skripti šajā lapā automātiski lejupielādēs visus nepieciešamos metadatus no iepriekš minētajiem avotiem. Ja vēlaties izpētīt mūsu datus pirms šo skriptu palaišanas lokāli, varat apskatīt mūsu JSON failus, kas saista tālāk uz citiem JSON failiem. <a %(a_json)s>Šis fails</a> ir labs sākumpunkts. Vienotā datubāze Torrenti no Annas Arhīva pārlūkot meklēt Dažādi mazāki vai vienreizēji avoti. Mēs mudinām cilvēkus vispirms augšupielādēt citās ēnu bibliotēkās, bet dažreiz cilvēkiem ir kolekcijas, kas ir pārāk lielas, lai citi tās varētu sakārtot, bet ne pietiekami lielas, lai tām būtu sava kategorija. Pārskats no <a %(a1)s>datu kopu lapas</a>. No <a %(a_href)s>aaaaarg.fail</a>. Izskatās diezgan pilnīgs. No mūsu brīvprātīgā “cgiym”. No <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrenta. Ir diezgan liela pārklāšanās ar esošajām rakstu kolekcijām, bet ļoti maz MD5 atbilstību, tāpēc mēs nolēmām to pilnībā saglabāt. <iRead eBooks</q> (fonētiski <q>ai rit i-books</q>; airitibooks.com) skrāpējums, ko veicis brīvprātīgais <q>j</q>. Atbilst <q>airitibooks</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>. No kolekcijas <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Daļēji no oriģinālā avota, daļēji no the-eye.eu, daļēji no citiem spoguļiem. No privātas grāmatu torrenta vietnes, <a %(a_href)s>Bibliotik</a> (bieži dēvēta par “Bib”), kuras grāmatas tika apvienotas torrentos pēc nosaukuma (A.torrent, B.torrent) un izplatītas caur the-eye.eu. No mūsu brīvprātīgā “bpb9v”. Lai iegūtu vairāk informācijas par <a %(a_href)s>CADAL</a>, skatiet piezīmes mūsu <a %(a_duxiu)s>DuXiu datu kopas lapā</a>. Vairāk no mūsu brīvprātīgā “bpb9v”, galvenokārt DuXiu faili, kā arī mapes “WenQu” un “SuperStar_Journals” (SuperStar ir uzņēmums, kas stāv aiz DuXiu). No mūsu brīvprātīgā “cgiym”, ķīniešu teksti no dažādiem avotiem (pārstāvēti kā apakšdirektorijas), ieskaitot no <a %(a_href)s>China Machine Press</a> (liels ķīniešu izdevējs). Neķīniešu kolekcijas (pārstāvētas kā apakšdirektorijas) no mūsu brīvprātīgā “cgiym”. Grāmatu par Ķīnas arhitektūru skrāpējums, ko veicis brīvprātīgais <q>cm</q>: <q>Es to ieguvu, izmantojot tīkla ievainojamību izdevniecībā, bet šī nepilnība tagad ir novērsta</q>. Atbilst <q>chinese_architecture</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>. Grāmatas no akadēmiskās izdevniecības <a %(a_href)s>De Gruyter</a>, savāktas no dažiem lieliem torrentiem. <a %(a_href)s>docer.pl</a> skrāpējums, poļu failu koplietošanas vietne, kas koncentrējas uz grāmatām un citiem rakstītiem darbiem. Skrāpēts 2023. gada beigās brīvprātīgā “p” veiktā. Mums nav labu metadatu no oriģinālās vietnes (pat ne failu paplašinājumi), bet mēs filtrējām grāmatu līdzīgus failus un bieži vien varējām izvilkt metadatus no pašiem failiem. DuXiu epubi, tieši no DuXiu, savākti brīvprātīgā “w”. Tikai nesenās DuXiu grāmatas ir pieejamas tieši caur e-grāmatām, tāpēc lielākā daļa no tām ir nesenas. Atlikušie DuXiu faili no brīvprātīgā “m”, kas nebija DuXiu patentētajā PDG formātā (galvenā <a %(a_href)s>DuXiu datu kopa</a>). Savākti no daudziem oriģinālajiem avotiem, diemžēl nesaglabājot šos avotus faila ceļā. <span></span> <span></span> <span></span> Erotisko grāmatu skrāpējums, ko veicis brīvprātīgais <q>do no harm</q>. Atbilst <q>hentai</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>. <span></span> <span></span> Kolekcija, kas skrāpēta no japāņu Manga izdevēja, veicis brīvprātīgais “t”. <a %(a_href)s>Izvēlētie Longquan tiesu arhīvi</a>, nodrošinājis brīvprātīgais “c”. <a %(a_href)s>magzdb.org</a> nokasīšana, Library Genesis sabiedrotais (tas ir saistīts ar libgen.rs sākumlapu), bet kurš nevēlējās tieši nodrošināt savus failus. Iegūts brīvprātīgā “p” 2023. gada beigās. <span></span> Dažādas mazas augšupielādes, pārāk mazas, lai būtu sava apakškolekcija, bet attēlotas kā direktorijas. E-grāmatas no AvaxHome, Krievijas failu koplietošanas vietnes. Avīžu un žurnālu arhīvs. Atbilst <q>newsarch_magz</q> metadatai <a %(a1)s><q>Citi metadata skrāpējumi</q></a>. <a %(a1)s>Filozofijas dokumentācijas centra</a> skrāpējums. Brīvprātīgā “o” kolekcija, kurš savāca poļu grāmatas tieši no oriģinālajiem izlaiduma (“scene”) tīmekļa vietnēm. Apvienotās <a %(a_href)s>shuge.org</a> kolekcijas, ko veidojuši brīvprātīgie “cgiym” un “woz9ts”. <span></span> <a %(a_href)s>“Trantoras Impērijas bibliotēka”</a> (nosaukta pēc izdomātas bibliotēkas), ko 2022. gadā savāca brīvprātīgais “t”. <span></span> <span></span> <span></span> Apakšapakškolekcijas (pārstāvētas kā direktorijas) no brīvprātīgā “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (autors <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taivānā), mebook (mebook.cc, 我的小书屋, mana mazā grāmatu istaba — woz9ts: “Šī vietne galvenokārt koncentrējas uz augstas kvalitātes e-grāmatu failu koplietošanu, no kuriem dažus pats īpašnieks ir noformējis. Īpašnieks tika <a %(a_arrested)s>arestēts</a> 2019. gadā, un kāds izveidoja kolekciju ar viņa koplietotajiem failiem.”). Atlikušie DuXiu faili no brīvprātīgā “woz9ts”, kas nebija DuXiu patentētajā PDG formātā (vēl jāpārvērš PDF formātā). “Augšupielādes” kolekcija ir sadalīta mazākās apakškolekcijās, kas norādītas AACID un torrentu nosaukumos. Visas apakškolekcijas vispirms tika dublētas pret galveno kolekciju, lai gan metadatu “upload_records” JSON faili joprojām satur daudz atsauču uz oriģinālajiem failiem. Lielākā daļa apakškolekciju tika iztīrītas no ne-grāmatu failiem, un tie parasti <em>nav</em> norādīti “upload_records” JSON. Apakškolekcijas ir: Piezīmes Apakškolekcija Daudzas apakškolekcijas pašas sastāv no apakš-apakškolekcijām (piemēram, no dažādiem oriģinālajiem avotiem), kas ir attēlotas kā direktorijas “filepath” laukos. Augšupielādes Annas Arhīvā Mūsu emuāra ieraksts par šiem datiem <a %(a_worldcat)s>WorldCat</a> ir patentēta datubāze, ko pārvalda bezpeļņas organizācija <a %(a_oclc)s>OCLC</a>, kas apkopo metadatu ierakstus no bibliotēkām visā pasaulē. Iespējams, tā ir lielākā bibliotēku metadatu kolekcija pasaulē. 2023. gada oktobrī mēs <a %(a_scrape)s>izlaidām</a> visaptverošu OCLC (WorldCat) datubāzes skrāpējumu, <a %(a_aac)s>Annas Arhīva Konteineru formātā</a>. 2023. gada oktobris, sākotnējā versija: OCLC (WorldCat) Torrenti no Annas Arhīva Piemēra ieraksts Annas Arhīvā (oriģinālā kolekcija) Piemēra ieraksts Annas Arhīvā (“zlib3” kolekcija) Torrenti no Annas Arhīva (metadati + saturs) Emuāra ieraksts par 1. izlaidumu Emuāra ieraksts par 2. izlaidumu 2022. gada beigās tika arestēti Z-Library iespējami dibinātāji, un domēnus konfiscēja Amerikas Savienoto Valstu iestādes. Kopš tā laika vietne lēnām atgriežas tiešsaistē. Nav zināms, kas to pašlaik pārvalda. Atjauninājums uz 2023. gada februāri. Z-Library ir saknes <a %(a_href)s>Library Genesis</a> kopienā, un sākotnēji tika izveidota ar viņu datiem. Kopš tā laika tā ir ievērojami profesionalizējusies un tai ir daudz modernāks interfeiss. Tāpēc viņi spēj saņemt daudz vairāk ziedojumu, gan finansiāli, lai turpinātu uzlabot savu vietni, gan arī jaunu grāmatu ziedojumus. Viņi ir uzkrājuši lielu kolekciju papildus Library Genesis. Kolekcija sastāv no trim daļām. Sākotnējās apraksta lapas pirmajām divām daļām ir saglabātas zemāk. Jums ir nepieciešamas visas trīs daļas, lai iegūtu visus datus (izņemot aizstātus torrentus, kas ir izsvītroti torrentu lapā). %(title)s: mūsu pirmais izlaidums. Tas bija pats pirmais izlaidums, ko toreiz sauca par “Pirātu bibliotēkas spoguli” (“pilimi”). %(title)s: otrais izlaidums, šoreiz ar visiem failiem ietvertiem .tar failos. %(title)s: pakāpeniski jauni izlaidumi, izmantojot <a %(a_href)s>Annas Arhīva Konteineru (AAC) formātu</a>, tagad izlaisti sadarbībā ar Z-Library komandu. Sākotnējais spogulis tika rūpīgi iegūts 2021. un 2022. gada laikā. Šobrīd tas ir nedaudz novecojis: tas atspoguļo kolekcijas stāvokli 2021. gada jūnijā. Mēs to atjaunināsim nākotnē. Pašlaik mēs koncentrējamies uz šī pirmā izlaiduma izdošanu. Tā kā Library Genesis jau ir saglabāts ar publiskiem torrentiem un ir iekļauts Z-Library, mēs 2022. gada jūnijā veicām pamata dublēšanu pret Library Genesis. Šim nolūkam mēs izmantojām MD5 hešus. Bibliotēkā, iespējams, ir daudz vairāk dublēta satura, piemēram, vairāki failu formāti ar vienu un to pašu grāmatu. To ir grūti precīzi noteikt, tāpēc mēs to nedarām. Pēc dublēšanas mums paliek vairāk nekā 2 miljoni failu, kuru kopējais apjoms ir nedaudz mazāks par 7TB. Kolekcija sastāv no divām daļām: MySQL “.sql.gz” metadatu izgāztuves un 72 torrent failiem, katrs apmēram 50-100GB. Metadati satur datus, kā ziņots Z-Library vietnē (nosaukums, autors, apraksts, faila tips), kā arī faktisko faila izmēru un md5sum, ko mēs novērojām, jo dažreiz šie dati nesakrīt. Šķiet, ka ir failu diapazoni, kuriem pašai Z-Library ir nepareizi metadati. Dažos atsevišķos gadījumos mēs varētu būt nepareizi lejupielādējuši failus, ko mēs centīsimies noteikt un labot nākotnē. Lielie torrent faili satur faktisko grāmatu datus, ar Z-Library ID kā faila nosaukumu. Failu paplašinājumus var rekonstruēt, izmantojot metadatu izgāztuvi. Kolekcija ir jaukums no daiļliteratūras un nedaiļliteratūras satura (nav atdalīts kā Library Genesis). Kvalitāte arī ļoti atšķiras. Šis pirmais izlaidums tagad ir pilnībā pieejams. Ņemiet vērā, ka torrent faili ir pieejami tikai caur mūsu Tor spoguli. Izlaidums 1 (%(date)s) Šis ir viens papildu torrent fails. Tas nesatur nekādu jaunu informāciju, bet tajā ir dati, kuru aprēķināšana var aizņemt kādu laiku. Tas padara to ērtu, jo šī torrenta lejupielāde bieži vien ir ātrāka nekā aprēķināšana no nulles. Īpaši tas satur SQLite indeksus tar failiem, izmantošanai ar <a %(a_href)s>ratarmount</a>. Izlaiduma 2 papildinājums (%(date)s) Mēs esam ieguvuši visas grāmatas, kas tika pievienotas Z-Library starp mūsu pēdējo spoguli un 2022. gada augustu. Mēs arī atgriezāmies un nokasījām dažas grāmatas, kuras pirmo reizi palaidām garām. Kopumā šī jaunā kolekcija ir apmēram 24TB. Atkal, šī kolekcija ir dublēta pret Library Genesis, jo šai kolekcijai jau ir pieejami torrenti. Dati ir organizēti līdzīgi kā pirmajā izlaidumā. Ir MySQL “.sql.gz” metadatu izgāztuve, kas ietver arī visus pirmā izlaiduma metadatus, tādējādi to aizstājot. Mēs arī pievienojām dažas jaunas kolonnas: Mēs to minējām pēdējo reizi, bet tikai lai precizētu: “faila nosaukums” un “md5” ir faktiskās faila īpašības, savukārt “faila nosaukums_ziņots” un “md5_ziņots” ir tas, ko mēs nokasījām no Z-Library. Dažreiz šie divi nesakrīt, tāpēc mēs iekļāvām abus. Šim izlaidumam mēs mainījām kolāciju uz “utf8mb4_unicode_ci”, kas būtu saderīga ar vecākām MySQL versijām. Datu faili ir līdzīgi kā iepriekšējā reizē, lai gan tie ir daudz lielāki. Mēs vienkārši nevarējām apgrūtināt izveidot daudzus mazākus torrent failus. “pilimi-zlib2-0-14679999-extra.torrent” satur visus failus, kurus mēs palaidām garām pēdējā izlaidumā, savukārt pārējie torrenti ir visi jauni ID diapazoni.  <strong>Atjauninājums %(date)s:</strong> Mēs izveidojām lielāko daļu mūsu torrentu pārāk lielus, izraisot torrent klientu problēmas. Mēs tos noņēmām un izlaidām jaunus torrentus. <strong>Atjauninājums %(date)s:</strong> Joprojām bija pārāk daudz failu, tāpēc mēs tos iesaiņojām tar failos un atkal izlaidām jaunus torrentus. %(key)s: vai šis fails jau ir Library Genesis, vai nu nedaiļliteratūras, vai daiļliteratūras kolekcijā (saskaņots ar md5). %(key)s: kurā torrentā šis fails atrodas. %(key)s: iestatīts, kad mēs nevarējām lejupielādēt grāmatu. Izlaidums 2 (%(date)s) Zlib izlaidumi (oriģinālās apraksta lapas) Tor domēns Galvenā vietne Z-Library skrāpējums “Ķīniešu” kolekcija Z-Library šķiet tāda pati kā mūsu DuXiu kolekcija, bet ar atšķirīgiem MD5. Mēs izslēdzam šos failus no torrentiem, lai izvairītos no dublēšanās, bet joprojām rādām tos mūsu meklēšanas indeksā. Metadati Jūs saņemat %(percentage)s%% papildu ātrās lejupielādes, jo jūs ieteica lietotājs %(profile_link)s. Tas attiecas uz visu biedru periodu. Ziedot Pievienoties Izvēlēts līdz %(percentage)s%% atlaidēm Alipay atbalsta starptautiskās kredītkartes/debetkartes. Skatiet <a %(a_alipay)s>šo ceļvedi</a> vairāk informācijas. Sūtiet mums Amazon.com dāvanu kartes, izmantojot savu kredītkarti/debetkarti. Jūs varat iegādāties kriptovalūtu, izmantojot kredītkartes/debetkartes. WeChat (Weixin Pay) atbalsta starptautiskās kredītkartes/debetkartes. WeChat lietotnē dodieties uz “Me => Services => Wallet => Add a Card”. Ja to neredzat, aktivizējiet to, izmantojot “Me => Settings => General => Tools => Weixin Pay => Enable”. (izmantojiet, sūtot Ethereum no Coinbase) nokopēts! kopēt (zemākā minimālā summa) (brīdinājums: augsta minimālā summa) -%(percentage)s%% 12 mēneši 1 mēnesis 24 mēneši 3 mēneši 48 mēneši 6 mēneši 96 mēneši Izvēlieties, cik ilgi vēlaties abonēt. <div %(div_monthly_cost)s></div><div %(div_after)s>pēc <span %(span_discount)s></span> atlaidēm</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% uz 12 mēnešiem uz 1 mēnesi uz 24 mēnešiem uz 3 mēnešiem uz 48 mēnešiem uz 6 mēnešiem uz 96 mēnešiem %(monthly_cost)s / mēnesī sazinieties ar mums Tiešie <strong>SFTP</strong> serveri Uzņēmuma līmeņa ziedojums vai apmaiņa pret jaunām kolekcijām (piemēram, jauni skenējumi, OCR dati). Eksperta piekļuve <strong>Neierobežota</strong> ātrgaitas piekļuve <div %(div_question)s>Vai es varu uzlabot savu dalību vai iegūt vairākas dalības?</div> <div %(div_question)s>Vai es varu veikt ziedojumu, nekļūstot par biedru?</div> Protams. Mēs pieņemam jebkura apjoma ziedojumus uz šo Monero (XMR) adresi: %(address)s. <div %(div_question)s>Ko nozīmē diapazoni mēnesī?</div> Jūs varat sasniegt diapazona zemāko pusi, piemērojot visus atlaides, piemēram, izvēloties periodu, kas ir ilgāks par mēnesi. <div %(div_question)s>Vai dalības automātiski atjaunojas?</div> Dalības <strong>neatjaunojas</strong> automātiski. Jūs varat pievienoties uz tik ilgu vai īsu laiku, cik vēlaties. <div %(div_question)s>Kam jūs tērējat ziedojumus?</div> 100%% tiek tērēti pasaules zināšanu un kultūras saglabāšanai un pieejamības nodrošināšanai. Pašlaik mēs lielākoties tērējam tos serveriem, glabāšanai un joslas platumam. Neviens naudas līdzeklis netiek piešķirts nevienam komandas loceklim personīgi. <div %(div_question)s>Vai es varu veikt lielu ziedojumu?</div> Tas būtu lieliski! Par ziedojumiem, kas pārsniedz dažus tūkstošus dolāru, lūdzu, sazinieties ar mums tieši pa %(email)s. <div %(div_question)s>Vai jums ir citi maksājumu veidi?</div> Pašlaik nē. Daudzi cilvēki nevēlas, lai tādi arhīvi kā šis pastāvētu, tāpēc mums jābūt uzmanīgiem. Ja varat palīdzēt mums droši izveidot citus (ērtākus) maksājumu veidus, lūdzu, sazinieties ar mums pa %(email)s. Ziedojumu BUJ Jums ir <a %(a_donation)s>esošs ziedojums</a> procesā. Lūdzu, pabeidziet vai atceliet šo ziedojumu, pirms veicat jaunu ziedojumu. <a %(a_all_donations)s>Skatīt visus manus ziedojumus</a> Ziedojumiem virs $5000, lūdzu, sazinieties ar mums tieši pa %(email)s. Mēs priecājamies par lieliem ziedojumiem no turīgiem indivīdiem vai iestādēm.  Ņemiet vērā, ka, lai gan šajā lapā norādītās dalības ir "mēnesī", tās ir vienreizējas ziedojumi (neatkārtojas). Skatiet <a %(faq)s>Ziedojumu BUJ</a>. Annas Arhīvs ir bezpeļņas, atvērtā koda, atvērto datu projekts. Ziedojot un kļūstot par biedru, jūs atbalstāt mūsu darbību un attīstību. Visiem mūsu biedriem: paldies, ka palīdzat mums turpināt! ❤️ Lai iegūtu vairāk informācijas, skatiet <a %(a_donate)s>Ziedojumu BUJ</a>. Lai kļūtu par biedru, lūdzu, <a %(a_login)s>Piesakieties vai Reģistrējieties</a>. Paldies par jūsu atbalstu! $%(cost)s / mēnesī Ja maksājuma laikā pieļāvāt kļūdu, mēs nevaram veikt atmaksu, bet centīsimies to labot. Atrodiet “Kripto” lapu savā PayPal lietotnē vai vietnē. Parasti tā atrodas sadaļā “Finanses”. Dodieties uz “Bitcoin” lapu savā PayPal lietotnē vai vietnē. Nospiediet pogu “Pārskaitīt” %(transfer_icon)s, un pēc tam “Sūtīt”. Alipay Alipay 支付宝 / WeChat 微信 Amazon dāvanu karte %(amazon)s dāvanu karte Bankas karte Bankas karte (izmantojot lietotni) Binance Kredītkarte/debetkarte/Apple/Google (BMC) Cash App Kredītkarte/debetkarte Kredītkarte/debetkarte 2 Kredītkarte/debetkarte (rezerves) Kriptovalūta %(bitcoin_icon)s Karte / PayPal / Venmo PayPal (ASV) %(bitcoin_icon)s PayPal PayPal (regulārs) Pix (Brazil) Revolut (pagaidām nav pieejams) WeChat Izvēlieties savu vēlamo kriptovalūtu: Ziedot, izmantojot Amazon dāvanu karti. <strong>SVARĪGI:</strong> Šī opcija ir paredzēta %(amazon)s. Ja vēlaties izmantot citu Amazon vietni, izvēlieties to augstāk. <strong>SVARĪGI:</strong> Mēs atbalstām tikai Amazon.com, nevis citus Amazon tīmekļa vietnes. Piemēram, .de, .co.uk, .ca, NAV atbalstīti. Lūdzu, NERAKSTIET savu ziņojumu. Ievadiet precīzu summu: %(amount)s Ņemiet vērā, ka mums ir jānoapaļo summas, ko pieņem mūsu tālākpārdevēji (minimums %(minimum)s). Ziedojiet, izmantojot kredītkarti/debetkarti, caur Alipay lietotni (ļoti viegli iestatīt). Instalējiet Alipay lietotni no <a %(a_app_store)s>Apple App Store</a> vai <a %(a_play_store)s>Google Play Store</a>. Reģistrējieties, izmantojot savu tālruņa numuru. Nav nepieciešama papildu personiskā informācija. <span %(style)s>1</span>Instalējiet Alipay lietotni Atbalstītās: Visa, MasterCard, JCB, Diners Club un Discover. Lai iegūtu vairāk informācijas, skatiet <a %(a_alipay)s>šo ceļvedi</a>. <span %(style)s>2</span>Pievienojiet bankas karti Ar Binance jūs pērkat Bitcoin ar kredītkarti/debetkarti vai bankas kontu un pēc tam ziedojat šo Bitcoin mums. Tādā veidā mēs varam palikt droši un anonīmi, pieņemot jūsu ziedojumu. Binance ir pieejams gandrīz katrā valstī un atbalsta lielāko daļu banku un kredītkartes/debetkartes. Šobrīd tas ir mūsu galvenais ieteikums. Mēs novērtējam, ka jūs veltāt laiku, lai iemācītos ziedot, izmantojot šo metodi, jo tas mums ļoti palīdz. Kredītkartēm, debetkartēm, Apple Pay un Google Pay mēs izmantojam “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Viņu sistēmā viena “kafija” ir vienāda ar $5, tāpēc jūsu ziedojums tiks noapaļots līdz tuvākajam 5 reizinājumam. Ziedot, izmantojot Cash App. Ja jums ir Cash App, tas ir vienkāršākais veids, kā ziedot! Ņemiet vērā, ka par darījumiem, kas ir mazāki par %(amount)s, Cash App var iekasēt %(fee)s maksu. Par %(amount)s vai vairāk, tas ir bez maksas! Ziedot ar kredītkarti vai debetkarti. Šī metode izmanto kriptovalūtas nodrošinātāju kā starpnieku konversijai. Tas var būt nedaudz mulsinoši, tāpēc, lūdzu, izmantojiet šo metodi tikai tad, ja citas maksājumu metodes nedarbojas. Tā arī nedarbojas visās valstīs. Mēs nevaram tieši atbalstīt kredītkartes/debetkartes, jo bankas nevēlas ar mums sadarboties. ☹ Tomēr ir vairāki veidi, kā izmantot kredītkartes/debetkartes, izmantojot citas maksājumu metodes: Ar kriptovalūtu jūs varat ziedot, izmantojot BTC, ETH, XMR un SOL. Izmantojiet šo iespēju, ja jau esat pazīstams ar kriptovalūtu. Ar kriptovalūtu jūs varat ziedot, izmantojot BTC, ETH, XMR un citas. Kripto ekspress pakalpojumi Ja jūs pirmo reizi izmantojat kriptovalūtu, mēs iesakām izmantot %(options)s, lai iegādātos un ziedotu Bitcoin (oriģinālo un visbiežāk izmantoto kriptovalūtu). Ņemiet vērā, ka mazu ziedojumu gadījumā kredītkartes maksas var iznīcināt mūsu %(discount)s%% atlaidi, tāpēc mēs iesakām ilgākus abonementus. Ziedot, izmantojot kredītkarti/debetkarti, PayPal vai Venmo. Jūs varat izvēlēties starp šiem nākamajā lapā. Google Pay un Apple Pay arī varētu darboties. Ņemiet vērā, ka mazu ziedojumu gadījumā maksas ir augstas, tāpēc mēs iesakām ilgākus abonementus. Lai ziedotu, izmantojot PayPal US, mēs izmantosim PayPal Crypto, kas ļauj mums palikt anonīmiem. Mēs novērtējam, ka veltāt laiku, lai iemācītos ziedot, izmantojot šo metodi, jo tas mums ļoti palīdz. Ziedojiet, izmantojot PayPal. Ziedot, izmantojot savu regulāro PayPal kontu. Ziedot, izmantojot Revolut. Ja jums ir Revolut, tas ir visvieglākais veids, kā ziedot! Šī maksājuma metode atļauj maksimālo summu %(amount)s. Lūdzu, izvēlieties citu ilgumu vai maksājuma metodi. Šī maksājuma metode prasa minimālo summu %(amount)s. Lūdzu, izvēlieties citu ilgumu vai maksājuma metodi. Binance Coinbase Kraken Lūdzu, izvēlieties maksājuma metodi. “Adoptēt torrentu”: jūsu lietotājvārds vai ziņojums torrent faila nosaukumā <div %(div_months)s>reizi 12 mēnešos pēc dalības</div> Jūsu lietotājvārds vai anonīms pieminējums kredītos Agrīna piekļuve jaunām funkcijām Ekskluzīvs Telegram ar aizkulišu atjauninājumiem %(number)s ātrās lejupielādes dienā ja ziedosiet šomēnes! <a %(a_api)s>JSON API</a> piekļuve Leģendārs statuss cilvēces zināšanu un kultūras saglabāšanā Iepriekšējie labumi, kā arī: Nopelniet <strong>%(percentage)s%% papildu lejupielādes</strong>, <a %(a_refer)s>aicinot draugus</a>. SciDB raksti <strong>neierobežoti</strong> bez verifikācijas Kad uzdodat jautājumus par kontu vai ziedojumiem, pievienojiet sava konta ID, ekrānuzņēmumus, kvītis, pēc iespējas vairāk informācijas. Mēs pārbaudām savu e-pastu tikai ik pēc 1-2 nedēļām, tāpēc šīs informācijas neiekļaušana aizkavēs jebkādu risinājumu. Lai iegūtu vēl vairāk lejupielāžu, <a %(a_refer)s>aiciniet savus draugus</a>! Mēs esam neliela brīvprātīgo komanda. Atbilde var aizņemt 1-2 nedēļas. Ņemiet vērā, ka konta nosaukums vai attēls var izskatīties dīvaini. Nav jāuztraucas! Šos kontus pārvalda mūsu ziedojumu partneri. Mūsu konti nav uzlauzti. Ziedot <span %(span_cost)s></span> <span %(span_label)s></span> uz 12 mēnešiem “%(tier_name)s” uz 1 mēnesi “%(tier_name)s” uz 24 mēnešiem “%(tier_name)s” uz 3 mēnešiem “%(tier_name)s” uz 48 mēnešiem “%(tier_name)s” uz 6 mēnešiem “%(tier_name)s” uz 96 mēnešiem “%(tier_name)s” Jūs joprojām varat atcelt ziedojumu izrakstīšanās laikā. Noklikšķiniet uz ziedojuma pogas, lai apstiprinātu šo ziedojumu. <strong>Svarīga piezīme:</strong> Kriptovalūtu cenas var svārstīties ļoti strauji, dažreiz pat par 20%% dažu minūšu laikā. Tas joprojām ir mazāk nekā maksas, ko mēs maksājam daudziem maksājumu pakalpojumu sniedzējiem, kuri bieži iekasē 50-60%% par darbu ar tādu “ēnu labdarību” kā mēs. <u>Ja jūs mums nosūtīsiet kvīti ar sākotnējo samaksāto cenu, mēs joprojām piešķirsim jūsu kontam izvēlēto dalību</u> (ja kvīts nav vecāka par dažām stundām). Mēs ļoti novērtējam, ka esat gatavs izturēt šādas lietas, lai mūs atbalstītu! ❤️ ❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. <span %(span_circle)s>1</span>Pērciet Bitcoin vietnē Paypal <span %(span_circle)s>2</span>Pārskaitiet Bitcoin uz mūsu adresi ✅ Pāradresācija uz ziedojumu lapu… Ziedot Lūdzu, uzgaidiet vismaz <span %(span_hours)s>24 stundas</span> (un atsvaidziniet šo lapu) pirms sazināties ar mums. Ja vēlaties veikt ziedojumu (jebkuru summu) bez dalības, droši izmantojiet šo Monero (XMR) adresi: %(address)s. Pēc dāvanu kartes nosūtīšanas mūsu automatizētā sistēma to apstiprinās dažu minūšu laikā. Ja tas nedarbojas, mēģiniet nosūtīt dāvanu karti vēlreiz (<a %(a_instr)s>instrukcijas</a>). Ja tas joprojām nedarbojas, lūdzu, rakstiet mums e-pastu, un Anna to manuāli pārskatīs (tas var aizņemt dažas dienas), un noteikti pieminiet, ja jau esat mēģinājis nosūtīt vēlreiz. Piemērs: Lūdzu, izmantojiet <a %(a_form)s>oficiālo Amazon.com formu</a>, lai nosūtītu mums dāvanu karti %(amount)s uz zemāk norādīto e-pasta adresi. “Kam” saņēmēja e-pasts formā: Amazon dāvanu karte Mēs nevaram pieņemt citas dāvanu karšu metodes, <strong>tikai nosūtītas tieši no oficiālās formas Amazon.com</strong>. Mēs nevaram atgriezt jūsu dāvanu karti, ja neizmantojat šo formu. Izmantojiet tikai vienu reizi. Unikāls jūsu kontam, nedalieties. Gaidām dāvanu karti… (atsvaidziniet lapu, lai pārbaudītu) Atveriet <a %(a_href)s>QR koda ziedojumu lapu</a>. Noskenējiet QR kodu ar Alipay lietotni vai nospiediet pogu, lai atvērtu Alipay lietotni. Lūdzu, esiet pacietīgi; lapas ielāde var aizņemt kādu laiku, jo tā atrodas Ķīnā. <span %(style)s>3</span>Veiciet ziedojumu (noskenējiet QR kodu vai nospiediet pogu) Iegādājieties PYUSD monētu PayPal Pirkt Bitcoin (BTC) lietotnē Cash App Pērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojamā summa (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri. Dodieties uz “Bitcoin” (BTC) lapu lietotnē Cash App. Pārskaitiet Bitcoin uz mūsu adresi Mazām ziedojumiem (zem $25) jums, iespējams, būs jāizmanto Rush vai Priority. Noklikšķiniet uz pogas “Sūtīt bitcoin”, lai veiktu “izņemšanu”. Pārslēdzieties no dolāriem uz BTC, nospiežot %(icon)s ikonu. Ievadiet BTC summu zemāk un noklikšķiniet uz “Sūtīt”. Ja esat apmulsis, skatiet <a %(help_video)s>šo video</a>. Ekspress pakalpojumi ir ērti, bet tiem ir augstākas maksas. Jūs varat izmantot šo, nevis kripto biržu, ja vēlaties ātri veikt lielāku ziedojumu un jums nav iebildumu pret maksu $5-10. Pārliecinieties, ka nosūtāt precīzu kripto summu, kas norādīta ziedojumu lapā, nevis summu $USD. Pretējā gadījumā maksa tiks atskaitīta, un mēs nevarēsim automātiski apstrādāt jūsu dalību. Dažreiz apstiprināšana var aizņemt līdz 24 stundām, tāpēc noteikti atjauniniet šo lapu (pat ja tā ir beidzies derīguma termiņš). Kredītkartes / debetkartes norādījumi Ziedojiet caur mūsu kredītkartes / debetkartes lapu Daži soļi piemin kriptovalūtas makus, bet neuztraucieties, jums nav jāapgūst nekas par kriptovalūtu. %(coin_name)s instrukcijas Skenējiet šo QR kodu ar savu kripto maka lietotni, lai ātri aizpildītu maksājuma informāciju Skenējiet QR kodu, lai samaksātu Mēs atbalstām tikai standarta kriptovalūtu versijas, nevis eksotiskus tīklus vai monētu versijas. Atkarībā no monētas, transakcijas apstiprināšana var aizņemt līdz stundai. Ziedojiet %(amount)s <a %(a_page)s>šajā lapā</a>. Šis ziedojums ir beidzies. Lūdzu, atceliet un izveidojiet jaunu. Ja esat jau samaksājis: Jā, es nosūtīju kvīti pa e-pastu Ja kriptovalūtas maiņas kurss darījuma laikā mainījās, noteikti iekļaujiet kvīti, kurā redzams sākotnējais maiņas kurss. Mēs ļoti novērtējam, ka izmantojat kriptovalūtu, tas mums ļoti palīdz! ❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. <span %(span_circle)s>%(circle_number)s</span>Nosūtiet mums kvīti pa e-pastu Ja rodas kādas problēmas, lūdzu, sazinieties ar mums pa %(email)s un iekļaujiet pēc iespējas vairāk informācijas (piemēram, ekrānuzņēmumus). ✅ Paldies par jūsu ziedojumu! Anna manuāli aktivizēs jūsu dalību dažu dienu laikā. Nosūtiet kvīti vai ekrānuzņēmumu uz jūsu personīgo verifikācijas adresi: Kad esat nosūtījis kvīti pa e-pastu, noklikšķiniet uz šīs pogas, lai Anna to manuāli pārskatītu (tas var aizņemt dažas dienas): Nosūtiet kvīti vai ekrānuzņēmumu uz savu personīgo verifikācijas adresi. Nelietojiet šo e-pasta adresi PayPal ziedojumam. Atcelt Jā, lūdzu atcelt Vai tiešām vēlaties atcelt? Neatceliet, ja jau esat samaksājis. ❌ Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. Veikt jaunu ziedojumu ✅ Jūsu ziedojums ir atcelts. Datums: %(date)s Identifikators: %(id)s Pārkārtot Status: <span %(span_label)s>%(label)s</span> Kopā: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mēnesī uz %(duration)s mēnešiem, ieskaitot %(discounts)s%% atlaidi)</span> Kopā: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mēnesī uz %(duration)s mēnešiem)</span> 1. Ievadiet savu e-pastu. 2. Izvēlieties maksājuma metodi. 3. Vēlreiz izvēlieties maksājuma metodi. 4. Izvēlieties “Pašhostēts” maks. 5. Noklikšķiniet uz “Es apstiprinu īpašumtiesības”. 6. Jums vajadzētu saņemt e-pasta kvīti. Lūdzu, nosūtiet to mums, un mēs pēc iespējas ātrāk apstiprināsim jūsu ziedojumu. (jūs varētu vēlēties atcelt un izveidot jaunu ziedojumu) Maksājuma instrukcijas ir novecojušas. Ja vēlaties veikt citu ziedojumu, izmantojiet pogu “Pārkārtot” augstāk. Jūs jau esat samaksājis. Ja vēlaties pārskatīt maksājuma instrukcijas, noklikšķiniet šeit: Rādīt vecās maksājuma instrukcijas Ja ziedojumu lapa tiek bloķēta, mēģiniet izmantot citu interneta savienojumu (piemēram, VPN vai telefona internetu). Diemžēl Alipay lapa bieži ir pieejama tikai no <strong>Ķīnas kontinentālās daļas</strong>. Jums, iespējams, būs īslaicīgi jāatspējo savs VPN vai jāizmanto VPN uz Ķīnas kontinentālo daļu (vai dažreiz arī Honkonga darbojas). <span %(span_circle)s>1</span>Ziedojiet Alipay Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo Alipay kontu</a> Alipay instrukcijas <span %(span_circle)s>1</span>Pārskaitiet uz vienu no mūsu kripto kontiem Ziedojiet kopējo summu %(total)s uz kādu no šīm adresēm: Kripto instrukcijas Izpildiet norādījumus, lai iegādātos Bitcoin (BTC). Jums tikai jāiegādājas summa, kuru vēlaties ziedot, %(total)s. Ievadiet mūsu Bitcoin (BTC) adresi kā saņēmēju un izpildiet norādījumus, lai nosūtītu savu ziedojumu %(total)s: <span %(span_circle)s>1</span>Ziedot, izmantojot Pix Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo Pix kontu</a> Pix instrukcijas <span %(span_circle)s>1</span>Ziedot, izmantojot WeChat Ziedojiet kopējo summu %(total)s, izmantojot <a %(a_account)s>šo WeChat kontu</a> WeChat instrukcijas Izmantojiet kādu no šiem “kredītkartes uz Bitcoin” ekspress pakalpojumiem, kas aizņem tikai dažas minūtes: BTC / Bitcoin adrese (ārējais maks): BTC / Bitcoin summa: Aizpildiet šādu informāciju formā: Ja kāda no šīm informācijām ir novecojusi, lūdzu, rakstiet mums e-pastu, lai mēs to varētu atjaunināt. Lūdzu, izmantojiet šo <span %(underline)s>precīzo summu</span>. Jūsu kopējās izmaksas var būt augstākas kredītkartes maksas dēļ. Mazām summām tas diemžēl var pārsniegt mūsu atlaidi. (minimums: %(minimum)s, nav nepieciešama verifikācija pirmajai transakcijai) (minimums: %(minimum)s) (minimums: %(minimum)s) (minimums: %(minimum)s, nav nepieciešama verifikācija pirmajai transakcijai) (minimums: %(minimum)s) (minimums: %(minimum)s atkarībā no valsts, nav nepieciešama verifikācija pirmajai transakcijai) Izpildiet norādījumus, lai iegādātos PYUSD monētu (PayPal USD). Nopērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojat (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri. Dodieties uz “PYUSD” lapu savā PayPal lietotnē vai vietnē. Nospiediet pogu “Pārskaitīt” %(icon)s, un pēc tam “Sūtīt”. Atjaunināt statusu Lai atiestatītu taimeri, vienkārši izveidojiet jaunu ziedojumu. Noteikti izmantojiet zemāk norādīto BTC summu, <em>NE</em> eiro vai dolārus, citādi mēs nesaņemsim pareizo summu un nevarēsim automātiski apstiprināt jūsu dalību. Pirkt Bitcoin (BTC) lietotnē Revolut Pērciet nedaudz vairāk (mēs iesakām %(more)s vairāk) nekā ziedojamā summa (%(amount)s), lai segtu transakcijas maksas. Jūs paturēsiet visu, kas paliks pāri. Dodieties uz “Crypto” lapu lietotnē Revolut, lai iegādātos Bitcoin (BTC). Pārskaitiet Bitcoin uz mūsu adresi Mazām ziedojumiem (zem $25) jums, iespējams, būs jāizmanto Rush vai Priority. Noklikšķiniet uz pogas “Sūtīt bitcoin”, lai veiktu “izņemšanu”. Pārslēdzieties no eiro uz BTC, nospiežot %(icon)s ikonu. Ievadiet BTC summu zemāk un noklikšķiniet uz “Sūtīt”. Ja esat apmulsis, skatiet <a %(help_video)s>šo video</a>. Statuss: 1 2 Soli pa solim ceļvedis Skatiet soli pa solim ceļvedi zemāk. Pretējā gadījumā jūs varat zaudēt piekļuvi šim kontam! Ja vēl neesat to izdarījis, pierakstiet savu slepeno atslēgu, lai pieteiktos: Paldies par jūsu ziedojumu! Atlikušais laiks: Ziedojums Pārskaitiet %(amount)s uz %(account)s Gaida apstiprinājumu (atsvaidziniet lapu, lai pārbaudītu)… Gaida pārskaitījumu (atsvaidziniet lapu, lai pārbaudītu)… Agrāk Ātrās lejupielādes pēdējo 24 stundu laikā tiek ieskaitītas dienas limitā. Lejupielādes no Ātrajiem Partneru Serveriem ir atzīmētas ar %(icon)s. Pēdējās 18 stundas Vēl nav lejupielādētu failu. Lejupielādētie faili netiek publiski rādīti. Visi laiki ir UTC. Lejupielādētie faili Ja lejupielādējāt failu ar gan ātrām, gan lēnām lejupielādēm, tas parādīsies divreiz. Neuztraucieties pārāk daudz, ir daudz cilvēku, kas lejupielādē no mūsu saistītajām vietnēm, un ir ārkārtīgi reti nonākt nepatikšanās. Tomēr, lai būtu drošībā, mēs iesakām izmantot VPN (maksas) vai <a %(a_tor)s>Tor</a> (bezmaksas). Es lejupielādēju Džordža Orvela grāmatu "1984", vai policija nāks pie manām durvīm? Jūs esat Anna! Kas ir Anna? Mums ir stabils JSON API biedriem, lai iegūtu ātru lejupielādes URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentācija pašā JSON). Citiem lietošanas gadījumiem, piemēram, visu mūsu failu iterēšanai, pielāgotas meklēšanas veidošanai un tā tālāk, mēs iesakām <a %(a_generate)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Neapstrādātie dati var tikt manuāli izpētīti <a %(a_explore)s>caur JSON failiem</a>. Mūsu neapstrādāto torrentu sarakstu var lejupielādēt kā <a %(a_torrents)s>JSON</a>. Vai jums ir API? Mēs šeit neuzglabājam nekādus ar autortiesībām aizsargātus materiālus. Mēs esam meklētājprogramma, un tādējādi indeksējam tikai metadatus, kas jau ir publiski pieejami. Lejupielādējot no šiem ārējiem avotiem, mēs iesakām pārbaudīt likumus jūsu jurisdikcijā attiecībā uz to, kas ir atļauts. Mēs neesam atbildīgi par citu personu mitināto saturu. Ja jums ir sūdzības par to, ko redzat šeit, vislabāk ir sazināties ar oriģinālo vietni. Mēs regulāri atjauninām viņu izmaiņas mūsu datubāzē. Ja jūs patiešām uzskatāt, ka jums ir derīga DMCA sūdzība, uz kuru mums būtu jāreaģē, lūdzu, aizpildiet <a %(a_copyright)s>DMCA / Autortiesību prasības veidlapu</a>. Mēs ņemam jūsu sūdzības nopietni un atbildēsim pēc iespējas ātrāk. Kā ziņot par autortiesību pārkāpumu? Šeit ir dažas grāmatas, kurām ir īpaša nozīme ēnu bibliotēku un digitālās saglabāšanas pasaulē: Kādas ir jūsu mīļākās grāmatas? Mēs arī vēlamies atgādināt visiem, ka viss mūsu kods un dati ir pilnīgi atvērtā koda. Tas ir unikāls projektiem, piemēram, mūsu — mēs nezinām nevienu citu projektu ar līdzīgi masīvu katalogu, kas arī ir pilnīgi atvērtā koda. Mēs ļoti priecājamies par ikvienu, kurš domā, ka mēs slikti vadām savu projektu, lai paņemtu mūsu kodu un datus un izveidotu savu ēnu bibliotēku! Mēs to nesakām no ļaunprātības vai kaut kā tamlīdzīga — mēs patiesi domājam, ka tas būtu lieliski, jo tas paaugstinātu latiņu visiem un labāk saglabātu cilvēces mantojumu. Es ienīstu, kā jūs vadāt šo projektu! Mēs būtu priecīgi, ja cilvēki izveidotu <a %(a_mirrors)s>spoguļus</a>, un mēs finansiāli atbalstīsim šo procesu. Kā es varu palīdzēt? Jā, mēs to darām. Mūsu iedvesma metadatu vākšanai ir Ārona Švarca mērķis “viena tīmekļa lapa katrai jebkad publicētai grāmatai”, kuram viņš izveidoja <a %(a_openlib)s>Open Library</a>. Šis projekts ir veiksmīgs, bet mūsu unikālā pozīcija ļauj mums iegūt metadatus, kurus viņi nevar. Vēl viena iedvesma bija mūsu vēlme zināt <a %(a_blog)s>cik daudz grāmatu ir pasaulē</a>, lai mēs varētu aprēķināt, cik daudz grāmatu mums vēl ir jāglābj. Vai jūs ievācat metadatus? Ņemiet vērā, ka mhut.org bloķē noteiktus IP diapazonus, tāpēc var būt nepieciešams VPN. <strong>Android:</strong> Noklikšķiniet uz trīs punktu izvēlnes augšējā labajā stūrī un izvēlieties “Pievienot sākuma ekrānam”. <strong>iOS:</strong> Noklikšķiniet uz “Kopīgot” pogas apakšā un izvēlieties “Pievienot sākuma ekrānam”. Mums nav oficiālas mobilās lietotnes, bet jūs varat instalēt šo vietni kā lietotni. Vai jums ir mobilā lietotne? Lūdzu, sūtiet tos uz <a %(a_archive)s>Internet Archive</a>. Viņi tos pienācīgi saglabās. Kā es varu ziedot grāmatas vai citus fiziskus materiālus? Kā es varu pieprasīt grāmatas? <a %(a_blog)s>Annas Blogs</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regulāri atjauninājumi <a %(a_software)s>Annas Programmatūra</a> — mūsu atvērtā koda programmatūra <a %(a_datasets)s>Datu kopas</a> — par datiem <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatīvi domēni Vai ir vairāk resursu par Annas Arhīvu? <a %(a_translate)s>Tulkot Annas Programmatūru</a> — mūsu tulkošanas sistēma <a %(a_wikipedia)s>Wikipedia</a> — vairāk par mums (lūdzu, palīdziet atjaunināt šo lapu vai izveidojiet to savā valodā!) Izvēlieties sev vēlamo iestatījumu, atstājiet meklēšanas lodziņu tukšu, noklikšķiniet uz “Meklēt” un pēc tam pievienojiet lapu grāmatzīmēm, izmantojot pārlūkprogrammas grāmatzīmju funkciju. Kā saglabāt savus meklēšanas iestatījumus? Mēs aicinām drošības pētniekus meklēt ievainojamības mūsu sistēmās. Mēs esam lieli atbildīgas atklāšanas atbalstītāji. Sazinieties ar mums <a %(a_contact)s>šeit</a>. Pašlaik mēs nevaram piešķirt atlīdzības par kļūdu atklāšanu, izņemot ievainojamības, kurām ir <a %(a_link)s>potenciāls apdraudēt mūsu anonimitāti</a>, par kurām mēs piedāvājam atlīdzības diapazonā no $10k-50k. Mēs vēlētos nākotnē piedāvāt plašāku atlīdzību klāstu! Lūdzu, ņemiet vērā, ka sociālās inženierijas uzbrukumi ir ārpus darbības jomas. Ja jūs interesē uzbrukuma drošība un vēlaties palīdzēt arhivēt pasaules zināšanas un kultūru, noteikti sazinieties ar mums. Ir daudz veidu, kā jūs varat palīdzēt. Vai jums ir atbildīgas atklāšanas programma? Mums burtiski nav pietiekami daudz resursu, lai nodrošinātu visiem pasaulē ātrgaitas lejupielādes, cik ļoti mēs to arī vēlētos. Ja kāds bagāts labvēlis vēlētos mums to nodrošināt, tas būtu neticami, bet līdz tam mēs cenšamies darīt visu iespējamo. Mēs esam bezpeļņas projekts, kas tik tikko spēj sevi uzturēt ar ziedojumiem. Tāpēc mēs ieviesām divas sistēmas bezmaksas lejupielādēm ar mūsu partneriem: koplietojami serveri ar lēnām lejupielādēm un nedaudz ātrāki serveri ar gaidīšanas sarakstu (lai samazinātu vienlaicīgi lejupielādējošo cilvēku skaitu). Mums ir arī <a %(a_verification)s>pārlūka verifikācija</a> mūsu lēnajām lejupielādēm, jo citādi boti un skrāpētāji tos ļaunprātīgi izmantos, padarot lietas vēl lēnākas likumīgiem lietotājiem. Ņemiet vērā, ka, izmantojot Tor pārlūku, jums, iespējams, būs jāpielāgo savi drošības iestatījumi. Zemākajā no opcijām, ko sauc par “Standarta”, Cloudflare turniketa izaicinājums izdodas. Augstākajās opcijās, ko sauc par “Drošāks” un “Drošākais”, izaicinājums neizdodas. Lieliem failiem dažreiz lēna lejupielāde var pārtrūkt vidū. Mēs iesakām izmantot lejupielādes pārvaldnieku (piemēram, JDownloader), lai automātiski atsāktu lielas lejupielādes. Kāpēc lejupielādes ir tik lēnas? Biežāk Uzdotie Jautājumi (BUJ) Izmantojiet <a %(a_list)s>torrentu saraksta ģeneratoru</a>, lai ģenerētu torrentu sarakstu, kuriem visvairāk nepieciešama torrentēšana, atbilstoši jūsu uzglabāšanas vietas ierobežojumiem. Jā, skatiet <a %(a_llm)s>LLM datu</a> lapu. Lielākā daļa torrentu satur failus tieši, kas nozīmē, ka jūs varat norādīt torrentu klientiem lejupielādēt tikai nepieciešamos failus. Lai noteiktu, kuri faili jālejupielādē, jūs varat <a %(a_generate)s>ģenerēt</a> mūsu metadatus vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Diemžēl, daudzas torrentu kolekcijas satur .zip vai .tar failus saknē, tādā gadījumā jums ir jālejupielādē viss torrents, pirms varat izvēlēties atsevišķus failus. Vēl nav pieejami viegli lietojami rīki torrentu filtrēšanai, bet mēs priecājamies par ieguldījumiem. (Mums gan ir <a %(a_ideas)s>dažas idejas</a> šim gadījumam.) Garā atbilde: Īsā atbilde: ne viegli. Mēs cenšamies uzturēt minimālu dublēšanos vai pārklāšanos starp šajā sarakstā esošajiem torrentiem, bet tas ne vienmēr ir iespējams un lielā mērā ir atkarīgs no avota bibliotēku politikām. Bibliotēkām, kas izplata savus torrentus, tas ir ārpus mūsu kontroles. Torrentiem, ko izplata Annas Arhīvs, mēs dublējam tikai pēc MD5 hash, kas nozīmē, ka dažādas vienas un tās pašas grāmatas versijas netiek dublētas. Jā. Šie patiesībā ir PDF un EPUB faili, tiem vienkārši nav paplašinājuma daudzos mūsu torrentos. Ir divas vietas, kur varat atrast torrentu failu metadatus, ieskaitot failu tipus/paplašinājumus: 1. Katrai kolekcijai vai izlaidumam ir savi metadati. Piemēram, <a %(a_libgen_nonfic)s>Libgen.rs torrenti</a> ir atbilstoša metadatu datubāze, kas tiek mitināta Libgen.rs vietnē. Mēs parasti saistāmies ar atbilstošiem metadatu resursiem no katras kolekcijas <a %(a_datasets)s>datu kopas lapas</a>. 2. Mēs iesakām <a %(a_generate)s>ģenerēt</a> vai <a %(a_download)s>lejupielādēt</a> mūsu ElasticSearch un MariaDB datubāzes. Šajās datubāzēs ir katra ieraksta kartējums Annas Arhīvā uz atbilstošajiem torrentu failiem (ja pieejami), zem “torrent_paths” ElasticSearch JSON. Daži torrentu klienti neatbalsta lielus gabalu izmērus, kas ir daudzos mūsu torrentos (jaunākajos mēs to vairs nedarām — pat ja tas ir derīgs pēc specifikācijām!). Tāpēc, ja saskaraties ar šo problēmu, izmēģiniet citu klientu vai sūdzieties sava torrentu klienta izstrādātājiem. Es vēlētos palīdzēt sēšanai, bet man nav daudz diska vietas. Torrentu lejupielāde ir pārāk lēna; vai es varu lejupielādēt datus tieši no jums? Vai es varu lejupielādēt tikai daļu no failiem, piemēram, tikai konkrētu valodu vai tēmu? Kā jūs apstrādājat dublikātus torrentos? Vai es varu iegūt torrentu sarakstu kā JSON? Es neredzu PDF vai EPUB failus torrentos, tikai bināros failus? Ko man darīt? Kāpēc mans torrentu klients nevar atvērt dažus no jūsu torrentu failiem / magnētu saites? Torrenti Biežāk Uzdotie Jautājumi (BUJ) Kā es varu augšupielādēt jaunas grāmatas? Lūdzu, skatiet <a %(a_href)s>šo lielisko projektu</a>. Vai jums ir pieejamības monitors? Kas ir Annas Arhīvs? Kļūstiet par biedru, lai izmantotu ātrās lejupielādes. Tagad mēs atbalstām Amazon dāvanu kartes, kredītkartes un debetkartes, kriptovalūtu, Alipay un WeChat. Jūs esat izsmēlis ātro lejupielāžu limitu šodien. Piekļuve Stundu lejupielādes pēdējo 30 dienu laikā. Stundu vidējais: %(hourly)s. Dienas vidējais: %(daily)s. Mēs sadarbojamies ar partneriem, lai padarītu mūsu kolekcijas viegli un brīvi pieejamas ikvienam. Mēs uzskatām, ka ikvienam ir tiesības uz cilvēces kolektīvo gudrību. Un <a %(a_search)s>ne uz autoru rēķina</a>. Datu kopas, kas izmantotas Annas Arhīvā, ir pilnībā atvērtas un var tikt masveidā spoguļotas, izmantojot torrentus. <a %(a_datasets)s>Uzziniet vairāk…</a> Ilgtermiņa arhīvs Pilna datubāze Meklēt Grāmatas, raksti, žurnāli, komiksi, bibliotēku ieraksti, metadati, … Visi mūsu <a %(a_code)s>kodi</a> un <a %(a_datasets)s>dati</a> ir pilnībā atvērta pirmkoda. <span %(span_anna)s>Annas Arhīvs</span> ir bezpeļņas projekts ar diviem mērķiem: <li><strong>Saglabāšana:</strong> Visu cilvēces zināšanu un kultūras dublēšana.</li><li><strong>Piekļuve:</strong> Šo zināšanu un kultūras padarīšana pieejamu ikvienam pasaulē.</li> Mums ir pasaulē lielākā augstas kvalitātes teksta datu kolekcija. <a %(a_llm)s>Uzziniet vairāk…</a> LLM apmācības dati 🪩 Spoguļi: aicinājums brīvprātīgajiem Ja jūs vadāt augsta riska anonīmo maksājumu apstrādātāju, lūdzu, sazinieties ar mums. Mēs arī meklējam cilvēkus, kas vēlas izvietot gaumīgas mazas reklāmas. Visi ienākumi tiek novirzīti mūsu saglabāšanas centieniem. Saglabāšana Mēs lēšam, ka esam saglabājuši aptuveni <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% pasaules grāmatu</a>. Mēs saglabājam grāmatas, rakstus, komiksus, žurnālus un daudz ko citu, apkopojot šos materiālus no dažādām <a href="https://en.wikipedia.org/wiki/Shadow_library">ēnu bibliotēkām</a>, oficiālajām bibliotēkām un citām kolekcijām vienuviet. Visi šie dati tiek saglabāti uz visiem laikiem, padarot tos viegli dublējamus lielos apjomos — izmantojot torrentus —, radot daudz kopiju visā pasaulē. Dažas ēnu bibliotēkas to jau dara pašas (piemēram, Sci-Hub, Library Genesis), savukārt Annas Arhīvs “atbrīvo” citas bibliotēkas, kas nepiedāvā lielapjoma izplatīšanu (piemēram, Z-Library) vai vispār nav ēnu bibliotēkas (piemēram, Internet Archive, DuXiu). Šī plašā izplatīšana, apvienojumā ar atvērta pirmkoda kodu, padara mūsu vietni izturīgu pret izņemšanu un nodrošina ilgtermiņa cilvēces zināšanu un kultūras saglabāšanu. Uzziniet vairāk par <a href="/datasets">mūsu datu kopām</a>. Ja esat <a %(a_member)s>biedrs</a>, pārlūkprogrammas verifikācija nav nepieciešama. 🧬&nbsp;SciDB ir Sci-Hub turpinājums. SciDB Atvērts DOI Sci-Hub ir <a %(a_paused)s>apturējis</a> jaunu rakstu augšupielādi. Tieša piekļuve %(count)s akadēmiskajiem rakstiem 🧬&nbsp;SciDB ir Sci-Hub turpinājums ar pazīstamu saskarni un tiešu PDF skatīšanu. Ievadiet savu DOI, lai skatītu. Mums ir pilna Sci-Hub kolekcija, kā arī jauni raksti. Lielāko daļu var skatīt tieši ar pazīstamu saskarni, līdzīgu Sci-Hub. Dažus var lejupielādēt caur ārējiem avotiem, un šādos gadījumos mēs parādām saites uz tiem. Jūs varat ļoti palīdzēt, sējot torrentus. <a %(a_torrents)s>Uzziniet vairāk…</a> >%(count)s sējēji <%(count)s sējēji %(count_min)s–%(count_max)s sējēji 🤝 Meklējam brīvprātīgos Kā bezpeļņas, atvērtā koda projekts, mēs vienmēr meklējam cilvēkus, kas varētu palīdzēt. IPFS lejupielādes Saraksts pēc %(by)s, izveidots <span %(span_time)s>%(time)s</span> Saglabāt ❌ Kaut kas nogāja greizi. Lūdzu, mēģiniet vēlreiz. ✅ Saglabāts. Lūdzu, pārlādējiet lapu. Saraksts ir tukšs. rediģēt Pievienojiet vai noņemiet no šī saraksta, atrodot failu un atverot cilni “Saraksti”. Saraksts Kā mēs varam palīdzēt Pārklāšanās noņemšana (deduplikācija) Teksta un metadatu izvilkšana OCR Mēs varam nodrošināt ātrgaitas piekļuvi mūsu pilnajām kolekcijām, kā arī neizlaistajām kolekcijām. Šī ir uzņēmuma līmeņa piekļuve, ko mēs varam nodrošināt par ziedojumiem desmitiem tūkstošu USD apmērā. Mēs arī esam gatavi apmainīt to pret augstas kvalitātes kolekcijām, kuras mums vēl nav. Mēs varam jums atmaksāt, ja jūs varat nodrošināt mums mūsu datu bagātināšanu, piemēram: Atbalstiet ilgtermiņa cilvēces zināšanu arhivēšanu, vienlaikus iegūstot labākus datus savam modelim! <a %(a_contact)s>Sazinieties ar mums</a>, lai apspriestu, kā mēs varam sadarboties. Ir labi saprotams, ka LLM plaukst uz augstas kvalitātes datiem. Mums ir lielākā grāmatu, rakstu, žurnālu utt. kolekcija pasaulē, kas ir daži no augstākās kvalitātes teksta avotiem. LLM dati Unikāls mērogs un diapazons Mūsu kolekcijā ir vairāk nekā simts miljoni failu, tostarp akadēmiskie žurnāli, mācību grāmatas un žurnāli. Mēs sasniedzam šo mērogu, apvienojot lielas esošās krātuves. Dažas no mūsu avotu kolekcijām jau ir pieejamas lielapjomā (Sci-Hub un daļas no Libgen). Citus avotus mēs atbrīvojām paši. <a %(a_datasets)s>Datasets</a> parāda pilnu pārskatu. Mūsu kolekcijā ir miljoniem grāmatu, rakstu un žurnālu no laika pirms e-grāmatu ēras. Lielas šīs kolekcijas daļas jau ir OCR’ētas un tām jau ir maz iekšējās pārklāšanās. Turpināt Ja esat pazaudējis savu atslēgu, lūdzu, <a %(a_contact)s>sazinieties ar mums</a> un sniedziet pēc iespējas vairāk informācijas. Jums, iespējams, būs īslaicīgi jāizveido jauns konts, lai sazinātos ar mums. Lūdzu, <a %(a_account)s>pieslēdzieties</a>, lai skatītu šo lapu.</a> Lai novērstu surogātpasta robotu veidošanu daudzos kontos, mums vispirms ir jāpārbauda jūsu pārlūks. Ja jūs nonākat bezgalīgā cilpā, mēs iesakām instalēt <a %(a_privacypass)s>Privacy Pass</a>. Var arī palīdzēt izslēgt reklāmu bloķētājus un citus pārlūka paplašinājumus. Pieteikties / Reģistrēties Annas arhīvs uz laiku ir slēgts uzturēšanas darbiem. Lūdzu, atgriezieties pēc stundas. Alternatīvais autors Alternatīvais apraksts Alternatīvais izdevums Alternatīvais paplašinājums Alternatīvais faila nosaukums Alternatīvais izdevējs Alternatīvais nosaukums atvēršanas datums Lasīt vairāk… apraksts Meklēt Annas Arhīvā pēc CADAL SSNO numura Meklēt Annas Arhīvā pēc DuXiu SSID numura Meklēt Annas Arhīvā pēc DuXiu DXID numura Meklēt Annas Arhīvā pēc ISBN Meklēt Annas Arhīvā pēc OCLC (WorldCat) numura Meklēt Annas Arhīvā pēc Open Library ID Annas Arhīva tiešsaistes skatītājs %(count)s ietekmētās lapas Pēc lejupielādes: Labāka šī faila versija var būt pieejama vietnē %(link)s Masveida torrentu lejupielādes kolekcija Izmantojiet tiešsaistes rīkus, lai konvertētu starp formātiem. Ieteicamie konvertēšanas rīki: %(links)s Lieliem failiem mēs iesakām izmantot lejupielādes pārvaldnieku, lai novērstu pārtraukumus. Ieteicamie lejupielādes pārvaldnieki: %(links)s EBSCOhost e-grāmatu indekss (tikai ekspertiem) (arī noklikšķiniet uz “GET” augšpusē) (noklikšķiniet uz “GET” augšpusē) Ārējas lejupielādes <strong>🚀 Ātras lejupielādes</strong> Jums šodien ir atlikušas %(remaining)s. Paldies, ka esat biedrs! ❤️ <strong>🚀 Ātras lejupielādes</strong> Jūs esat izsmēlis ātro lejupielāžu limitu šodienai. <strong>🚀 Ātras lejupielādes</strong> Jūs nesen lejupielādējāt šo failu. Saites paliek derīgas kādu laiku. <strong>🚀 Ātras lejupielādes</strong> Kļūstiet par <a %(a_membership)s>biedru</a>, lai atbalstītu grāmatu, rakstu un citu materiālu ilgtermiņa saglabāšanu. Lai izrādītu pateicību par jūsu atbalstu, jūs saņemat ātras lejupielādes. ❤️ 🚀 Ātra lejupielāde 🐢 Lēnas lejupielādes Aizņemties no Internet Archive IPFS Vārteja #%(num)d (jums var būt nepieciešams mēģināt vairākas reizes ar IPFS) Libgen.li Libgen.rs Fantastika Libgen.rs Ne-fantastika viņu reklāmās var būt ļaunprātīga programmatūra, tāpēc izmantojiet reklāmu bloķētāju vai neklikšķiniet uz reklāmām Amazon “Send to Kindle” djazz “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC faili var būt neuzticami lejupielādei) Nav atrastas lejupielādes. Visas lejupielādes iespējas satur to pašu failu un būtu drošas lietošanai. Tomēr vienmēr esiet piesardzīgi, lejupielādējot failus no interneta, īpaši no vietnēm ārpus Annas Arhīva. Piemēram, pārliecinieties, ka jūsu ierīces ir atjauninātas. (nav bez novirzīšanas) Atvērt mūsu skatītājā (atvērt skatītājā) Opcija #%(num)d: %(link)s %(extra)s Atrast oriģinālo ierakstu CADAL Meklēt manuāli DuXiu Atrast oriģinālo ierakstu ISBNdb Atrast oriģinālo ierakstu WorldCat Atrast oriģinālo ierakstu Open Library Meklēt dažādās citās datubāzēs pēc ISBN (tikai drukas ierobežojumu apmeklētājiem) PubMed Lai atvērtu failu, atkarībā no faila formāta, jums būs nepieciešams e-grāmatu vai PDF lasītājs. Ieteicamie e-grāmatu lasītāji: %(links)s Annas Arhīvs 🧬 SciDB Sci-Hub: %(doi)s (saistītais DOI var nebūt pieejams Sci-Hub) Jūs varat nosūtīt gan PDF, gan EPUB failus uz savu Kindle vai Kobo e-lasītāju. Ieteicamie rīki: %(links)s Vairāk informācijas <a %(a_slow)s>BUJ</a>. Atbalstiet autorus un bibliotēkas Ja jums tas patīk un varat to atļauties, apsveriet iespēju iegādāties oriģinālu vai atbalstīt autorus tieši. Ja tas ir pieejams jūsu vietējā bibliotēkā, apsveriet iespēju to aizņemties tur bez maksas. Partneru servera lejupielādes uz laiku nav pieejamas šim failam. torrents No uzticamiem partneriem. Z-Library Z-Bibliotēka Tor tīklā (prasa Tor pārlūku) rādīt ārējās lejupielādes <span class="font-bold">❌ Šim failam var būt problēmas, un tas ir paslēpts no avota bibliotēkas.</span> Dažreiz tas notiek pēc autortiesību īpašnieka pieprasījuma, dažreiz tāpēc, ka ir pieejama labāka alternatīva, bet dažreiz tas ir saistīts ar pašu failu. Iespējams, ka to joprojām var lejupielādēt, bet mēs iesakām vispirms meklēt alternatīvu failu. Vairāk informācijas: Ja jūs joprojām vēlaties lejupielādēt šo failu, pārliecinieties, ka izmantojat tikai uzticamu, atjauninātu programmatūru, lai to atvērtu. metadatu komentāri AA: Meklēt Annas Arhīvā “%(name)s” Kodu Pētnieks: Skatīt Kodu Pētniekā “%(name)s” URL: Vietne: Ja jums ir šis fails un tas vēl nav pieejams Annas Arhīvā, apsveriet iespēju <a %(a_request)s>augšupielādēt to</a>. Internet Archive Kontrolētās Digitālās Aizņemšanās fails “%(id)s” Šis ir ieraksts no Internet Archive, nevis tieši lejupielādējams fails. Jūs varat mēģināt aizņemties grāmatu (saite zemāk), vai izmantot šo URL, kad <a %(a_request)s>pieprasāt failu</a>. Uzlabot metadatus CADAL SSNO %(id)s metadatu ieraksts Šis ir metadatu ieraksts, nevis lejupielādējams fails. Jūs varat izmantot šo URL, kad <a %(a_request)s>pieprasāt failu</a>. DuXiu SSID %(id)s metadatu ieraksts ISBNdb %(id)s metadatu ieraksts MagzDB ID %(id)s metadatu ieraksts Nexus/STC ID %(id)s metadatu ieraksts OCLC (WorldCat) numurs %(id)s metadatu ieraksts Open Library %(id)s metadatu ieraksts Sci-Hub fails “%(id)s” Nav atrasts “%(md5_input)s” mūsu datubāzē netika atrasts. Pievienot komentāru (%(count)s) Jūs varat iegūt md5 no URL, piemēram, Labākas versijas MD5 šim failam (ja piemērojams). Aizpildiet šo, ja ir cits fails, kas cieši atbilst šim failam (tāds pats izdevums, tāds pats faila paplašinājums, ja varat atrast), kuru cilvēkiem vajadzētu izmantot šī faila vietā. Ja zināt labāku šī faila versiju ārpus Annas Arhīva, lūdzu, <a %(a_upload)s>augšupielādējiet to</a>. Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. Jūs atstājāt komentāru. Tas var aizņemt minūti, lai tas parādītos. Lūdzu, izmantojiet <a %(a_copyright)s>DMCA / Autortiesību prasības veidlapu</a>. Aprakstiet problēmu (obligāti) Ja šim failam ir lieliska kvalitāte, jūs varat apspriest visu par to šeit! Ja nē, lūdzu, izmantojiet pogu “Ziņot par faila problēmu”. Lieliska faila kvalitāte (%(count)s) Faila kvalitāte Uzziniet, kā <a %(a_metadata)s>uzlabot metadatus</a> šim failam pašam. Problēmas apraksts Lūdzu, <a %(a_login)s>piesakieties</a>. Man ļoti patika šī grāmata! Palīdziet kopienai, ziņojot par šī faila kvalitāti! 🙌 Kaut kas nogāja greizi. Lūdzu, pārlādējiet lapu un mēģiniet vēlreiz. Ziņot par faila problēmu (%(count)s) Paldies par jūsu ziņojuma iesniegšanu. Tas tiks parādīts šajā lapā, kā arī manuāli pārskatīts Annas (līdz mums būs pienācīga moderācijas sistēma). Atstāt komentāru Iesniegt ziņojumu Kas ir nepareizi ar šo failu? Aizņemties (%(count)s) Komentāri (%(count)s) Lejupielādes (%(count)s) Izpētīt metadatus (%(count)s) Saraksti (%(count)s) Statistika (%(count)s) Lai iegūtu informāciju par šo konkrēto failu, skatiet tā <a %(a_href)s>JSON failu</a>. Šis ir fails, ko pārvalda <a %(a_ia)s>IA kontrolētā digitālā aizdošana</a> bibliotēka un indeksēts Annas Arhīvā meklēšanai. Lai iegūtu informāciju par dažādiem datu kopumiem, kurus mēs esam apkopojuši, skatiet <a %(a_datasets)s>Datu kopumu lapu</a>. Metadati no saistītā ieraksta Uzlabot metadatus Open Library “Faila MD5” ir hešs, kas tiek aprēķināts no faila satura un ir diezgan unikāls, pamatojoties uz šo saturu. Visas ēnu bibliotēkas, kuras mēs esam indeksējuši šeit, galvenokārt izmanto MD5, lai identificētu failus. Fails var parādīties vairākās ēnu bibliotēkās. Lai iegūtu informāciju par dažādiem datu kopumiem, kurus mēs esam apkopojuši, skatiet <a %(a_datasets)s>Datu kopumu lapu</a>. Ziņot par faila kvalitāti Kopējie lejupielādes: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Čehu metadati %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Grāmatas %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Brīdinājums: vairāki saistītie ieraksti: Kad skatāties grāmatu Anna’s Archive, jūs varat redzēt dažādus laukus: nosaukumu, autoru, izdevēju, izdevumu, gadu, aprakstu, faila nosaukumu un daudz ko citu. Visa šī informācija tiek saukta par <em>metadatiem</em>. Tā kā mēs apvienojam grāmatas no dažādām <em>avotu bibliotēkām</em>, mēs parādām jebkādus metadatus, kas ir pieejami šajā avotu bibliotēkā. Piemēram, grāmatai, kuru mēs ieguvām no Library Genesis, mēs parādīsim nosaukumu no Library Genesis datubāzes. Dažreiz grāmata ir pieejama <em>vairākās</em> avotu bibliotēkās, kurām var būt dažādi metadatu lauki. Šādā gadījumā mēs vienkārši parādām katra lauka garāko versiju, jo tā, cerams, satur visnoderīgāko informāciju! Mēs joprojām parādīsim citus laukus zem apraksta, piemēram, kā “alternatīvais nosaukums” (bet tikai tad, ja tie ir atšķirīgi). Mēs arī izvelkam <em>kodējumus</em>, piemēram, identifikatorus un klasifikatorus no avotu bibliotēkas. <em>Identifikatori</em> unikāli pārstāv konkrētu grāmatas izdevumu; piemēri ir ISBN, DOI, Open Library ID, Google Books ID vai Amazon ID. <em>Klasifikatori</em> grupē kopā vairākas līdzīgas grāmatas; piemēri ir Dewey Decimal (DCC), UDC, LCC, RVK vai GOST. Dažreiz šie kodi ir skaidri saistīti avotu bibliotēkās, un dažreiz mēs varam tos izvilkt no faila nosaukuma vai apraksta (galvenokārt ISBN un DOI). Mēs varam izmantot identifikatorus, lai atrastu ierakstus <em>tikai metadatu kolekcijās</em>, piemēram, OpenLibrary, ISBNdb vai WorldCat/OCLC. Mūsu meklētājā ir īpaša <em>metadatu cilne</em>, ja vēlaties pārlūkot šīs kolekcijas. Mēs izmantojam atbilstošos ierakstus, lai aizpildītu trūkstošos metadatu laukus (piemēram, ja trūkst nosaukuma), vai piemēram, kā “alternatīvais nosaukums” (ja ir esošs nosaukums). Lai redzētu, no kurienes tieši nāk grāmatas metadati, skatiet <em>“Tehniskās detaļas” cilni</em> grāmatas lapā. Tajā ir saite uz neapstrādāto JSON šai grāmatai, ar norādēm uz neapstrādāto JSON oriģinālajiem ierakstiem. Lai iegūtu vairāk informācijas, skatiet šādas lapas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, un <a %(a_example)s>Example metadata JSON</a>. Visbeidzot, visi mūsu metadati var tikt <a %(a_generated)s>ģenerēti</a> vai <a %(a_downloaded)s>lejupielādēti</a> kā ElasticSearch un MariaDB datubāzes. Fons Jūs varat palīdzēt grāmatu saglabāšanā, uzlabojot metadatus! Vispirms izlasiet informāciju par metadatiem Anna’s Archive, un tad uzziniet, kā uzlabot metadatus, saistot tos ar Open Library, un nopelniet bezmaksas dalību Anna’s Archive. Uzlabot metadatus Tātad, ja sastopaties ar failu ar sliktiem metadatiem, kā to labot? Jūs varat doties uz avotu bibliotēku un sekot tās procedūrām metadatu labošanai, bet ko darīt, ja fails ir pieejams vairākās avotu bibliotēkās? Anna’s Archive ir viens identifikators, kas tiek īpaši apstrādāts. <strong>Open Library annas_archive md5 lauks vienmēr pārsniedz visus citus metadatus!</strong> Vispirms atgriezīsimies un uzzināsim par Open Library. Open Library tika dibināta 2006. gadā Ārona Švarca vadībā ar mērķi “viena tīmekļa lapa katrai jebkad publicētai grāmatai”. Tā ir sava veida Wikipedia grāmatu metadatiem: ikviens var to rediģēt, tā ir brīvi licencēta un var tikt lejupielādēta masveidā. Tā ir grāmatu datubāze, kas visvairāk saskan ar mūsu misiju — patiesībā, Anna’s Archive ir iedvesmojusies no Ārona Švarca vīzijas un dzīves. Tā vietā, lai izgudrotu riteni no jauna, mēs nolēmām novirzīt mūsu brīvprātīgos uz Open Library. Ja redzat grāmatu ar nepareiziem metadatiem, jūs varat palīdzēt šādi: Ņemiet vērā, ka tas darbojas tikai grāmatām, nevis akadēmiskiem rakstiem vai citiem failu veidiem. Citu failu veidu gadījumā mēs joprojām iesakām atrast avota bibliotēku. Var paiet dažas nedēļas, līdz izmaiņas tiks iekļautas Annas Arhīvā, jo mums ir jālejupielādē jaunākais Open Library datu izgāztuve un jāatjauno mūsu meklēšanas indekss.  Dodieties uz <a %(a_openlib)s>Open Library vietni</a>. Atrodiet pareizo grāmatas ierakstu. <strong>BRĪDINĀJUMS:</strong> pārliecinieties, ka izvēlaties pareizo <strong>izdevumu</strong>. Open Library ir “darbi” un “izdevumi”. “Darbs” varētu būt “Harijs Poters un Filozofu akmens”. “Izdevums” varētu būt: 1997. gada pirmais izdevums, ko publicējusi Bloomsbery, ar 256 lappusēm. 2003. gada mīksto vāku izdevums, ko publicējusi Raincoast Books, ar 223 lappusēm. 2000. gada poļu tulkojums “Harry Potter I Kamie Filozoficzn” no Media Rodzina ar 328 lappusēm. Visiem šiem izdevumiem ir atšķirīgi ISBN un atšķirīgs saturs, tāpēc pārliecinieties, ka izvēlaties pareizo! Rediģējiet ierakstu (vai izveidojiet to, ja tāda nav), un pievienojiet pēc iespējas vairāk noderīgas informācijas! Jūs jau esat šeit, tāpēc varat padarīt ierakstu patiešām lielisku. Sadaļā “ID numuri” izvēlieties “Annas Arhīvs” un pievienojiet grāmatas MD5 no Annas Arhīva. Tas ir garais burtu un ciparu virknējums pēc “/md5/” URL. Mēģiniet atrast citus failus Annas Arhīvā, kas arī atbilst šim ierakstam, un pievienojiet tos. Nākotnē mēs varam grupēt tos kā dublikātus Annas Arhīva meklēšanas lapā. Kad esat pabeidzis, pierakstiet URL, kuru tikko atjauninājāt. Kad esat atjauninājis vismaz 30 ierakstus ar Annas Arhīva MD5, nosūtiet mums <a %(a_contact)s>e-pastu</a> un nosūtiet mums sarakstu. Mēs jums piešķirsim bezmaksas dalību Annas Arhīvā, lai jūs varētu vieglāk veikt šo darbu (un kā pateicību par jūsu palīdzību). Šiem jābūt augstas kvalitātes labojumiem, kas pievieno ievērojamu daudzumu informācijas, pretējā gadījumā jūsu pieprasījums tiks noraidīts. Jūsu pieprasījums tiks arī noraidīts, ja kāds no labojumiem tiks atcelts vai labots Open Library moderatoru. Open Library saistīšana Ja jūs būtiski iesaistīsieties mūsu darba izstrādē un darbībā, mēs varam apspriest lielāku ziedojumu ieņēmumu daļu dalīšanu ar jums, lai jūs varētu to izmantot pēc nepieciešamības. Mēs maksāsim par hostingu tikai tad, kad viss būs uzstādīts un jūs būsiet pierādījis, ka spējat uzturēt arhīvu atjauninātu. Tas nozīmē, ka jums būs jāmaksā par pirmajiem 1-2 mēnešiem no savas kabatas. Jūsu laiks netiks kompensēts (un arī mūsu nē), jo tas ir tīrs brīvprātīgais darbs. Mēs esam gatavi segt hostinga un VPN izdevumus, sākotnēji līdz 200 USD mēnesī. Tas ir pietiekami pamata meklēšanas serverim un DMCA aizsargātam starpniekserverim. Hostinga izdevumi Lūdzu, <strong>nesazinieties ar mums</strong>, lai lūgtu atļauju vai uzdotu pamata jautājumus. Darbi runā skaļāk par vārdiem! Visa informācija ir pieejama, tāpēc vienkārši turpiniet ar sava spoguļa izveidi. Jūtieties brīvi iesniegt biļetes vai apvienošanas pieprasījumus mūsu Gitlab, kad sastopaties ar problēmām. Mums var būt nepieciešams izveidot dažas spoguļiem specifiskas funkcijas kopā ar jums, piemēram, pārzīmološana no “Annas Arhīvs” uz jūsu vietnes nosaukumu, (sākotnēji) lietotāju kontu atspējošana vai saites uz mūsu galveno vietni no grāmatu lapām. Kad jūsu spogulis darbojas, lūdzu, sazinieties ar mums. Mēs labprāt pārskatīsim jūsu OpSec, un, kad tas būs stabils, mēs saistīsimies ar jūsu spoguli un sāksim ciešāk sadarboties ar jums. Paldies jau iepriekš visiem, kas ir gatavi šādā veidā piedalīties! Tas nav vājprātīgajiem, bet tas nostiprinātu lielākās patiesi atvērtās bibliotēkas cilvēces vēsturē ilgmūžību. Sākšana Lai palielinātu Annas Arhīva noturību, mēs meklējam brīvprātīgos, kas vadītu spoguļus. Jūsu versija ir skaidri atšķirama kā spogulis, piemēram, “Boba arhīvs, Anna’s Archive spogulis”. Jūs esat gatavs uzņemties ar šo darbu saistītos riskus, kas ir ievērojami. Jums ir dziļa izpratne par nepieciešamo operacionālo drošību. <a %(a_shadow)s>Šo</a> <a %(a_pirate)s>ierakstu</a> saturs jums ir pašsaprotams. Sākotnēji mēs nedosim jums piekļuvi mūsu partneru serveru lejupielādēm, bet, ja viss noritēs labi, mēs varam to dalīties ar jums. Jūs pārvaldāt Anna’s Archive atvērtā koda bāzi un regulāri atjaunināt gan kodu, gan datus. Jūs esat gatavs piedalīties mūsu <a %(a_codebase)s>koda bāzes</a> izstrādē — sadarbībā ar mūsu komandu — lai to īstenotu. Mēs meklējam sekojošo: Spoguļi: aicinājums brīvprātīgajiem Veikt vēl vienu ziedojumu. Vēl nav ziedojumu. <a %(a_donate)s>Veikt manu pirmo ziedojumu.</a> Ziedojumu detaļas netiek publiski rādītas. Mani ziedojumi 📡 Lai masveidā spoguļotu mūsu kolekciju, apskatiet <a %(a_datasets)s>Datu kopas</a> un <a %(a_torrents)s>Torrentus</a> lapas. Lejupielādes no jūsu IP adreses pēdējo 24 stundu laikā: %(count)s. 🚀 Lai iegūtu ātrākas lejupielādes un izlaistu pārlūkprogrammas pārbaudes, <a %(a_membership)s>kļūstiet par biedru</a>. Lejupielādēt no partneru vietnes Jūtieties brīvi turpināt pārlūkot Annas arhīvu citā cilnē, kamēr gaidāt (ja jūsu pārlūkprogramma atbalsta cilņu atsvaidzināšanu fonā). Jūtieties brīvi gaidīt, kamēr ielādējas vairākas lejupielādes lapas vienlaicīgi (bet, lūdzu, lejupielādējiet tikai vienu failu vienlaicīgi no katra servera). Kad saņemat lejupielādes saiti, tā ir derīga vairākas stundas. Paldies, ka gaidījāt, tas palīdz uzturēt vietni pieejamu bez maksas visiem! 😊 🔗 Visas lejupielādes saites šim failam: <a %(a_main)s>Faila galvenā lapa</a>. ❌ Lēnas lejupielādes nav pieejamas caur Cloudflare VPN vai no Cloudflare IP adresēm. ❌ Lēnas lejupielādes ir pieejamas tikai oficiālajā vietnē. Apmeklējiet %(websites)s. 📚 Izmantojiet šo URL, lai lejupielādētu: <a %(a_download)s>Lejupielādēt tagad</a>. Lai ikvienam būtu iespēja lejupielādēt failus bez maksas, jums ir jāuzgaida, pirms varat lejupielādēt šo failu. Lūdzu, uzgaidiet <span %(span_countdown)s>%(wait_seconds)s</span> sekundes, lai lejupielādētu šo failu. Brīdinājums: pēdējo 24 stundu laikā no jūsu IP adreses ir bijušas daudzas lejupielādes. Lejupielādes var būt lēnākas nekā parasti. Ja izmantojat VPN, koplietotu interneta savienojumu vai jūsu ISP koplieto IP adreses, šis brīdinājums var būt saistīts ar to. Saglabāt ❌ Kaut kas nogāja greizi. Lūdzu, mēģiniet vēlreiz. ✅ Saglabāts. Lūdzu, pārlādējiet lapu. Mainiet savu parādāmo vārdu. Jūsu identifikators (daļa pēc “#”) nevar tikt mainīts. Profils izveidots <span %(span_time)s>%(time)s</span> rediģēt Saraksti Izveidojiet jaunu sarakstu, atrodot failu un atverot cilni “Saraksti”. Vēl nav sarakstu Profils nav atrasts. Profils Šobrīd mēs nevaram izpildīt grāmatu pieprasījumus. Nesūtiet mums e-pastus ar grāmatu pieprasījumiem. Lūdzu, veiciet savus pieprasījumus Z-Library vai Libgen forumos. Ieraksts Annas arhīvā DOI: %(doi)s Lejupielādēt SciDB Nexus/STC Priekšskatījums vēl nav pieejams. Lejupielādējiet failu no <a %(a_path)s>Annas Arhīva</a>. Lai atbalstītu cilvēka zināšanu pieejamību un ilgtermiņa saglabāšanu, kļūstiet par <a %(a_donate)s>biedru</a>. Kā bonuss, 🧬&nbsp;SciDB biedriem ielādējas ātrāk, bez jebkādiem ierobežojumiem. Nestrādā? Mēģiniet <a %(a_refresh)s>atsvaidzināt</a>. Sci-Hub Pievienot specifisku meklēšanas lauku Meklēšanas apraksti un metadatu komentāri Publicēšanas gads Paplašināts Piekļuve Saturs Parādīt Saraksts Tabula Faila tips Valoda Kārtot pēc Lielākie Visatbilstošākais Jaunākie (faila izmērs) (atvērtais kods) (publicēšanas gads) Vecākie Nejauši Mazākie Avots iegūts un atvērts avots no AA Digitālā aizņemšanās (%(count)s) Žurnālu raksti (%(count)s) Mēs esam atraduši atbilstības: %(in)s. Jūs varat atsaukties uz tur atrasto URL, kad <a %(a_request)s>pieprasāt failu</a>. Metadati (%(count)s) Lai izpētītu meklēšanas indeksu pēc kodiem, izmantojiet <a %(a_href)s>Kodu izpētītāju</a>. Meklēšanas indekss tiek atjaunināts katru mēnesi. Pašlaik tas ietver ierakstus līdz %(last_data_refresh_date)s. Lai iegūtu vairāk tehniskas informācijas, skatiet %(link_open_tag)sdatu kopu lapu</a>. Izslēgt Iekļaut tikai Nepārbaudīts vairāk… Nākamais … Iepriekšējais Šis meklēšanas indekss pašlaik ietver metadatus no Internet Archive kontrolētās digitālās aizdevumu bibliotēkas. <a %(a_datasets)s>Vairāk par mūsu datu kopām</a>. Lai iegūtu vairāk digitālo aizdevumu bibliotēku, skatiet <a %(a_wikipedia)s>Wikipedia</a> un <a %(a_mobileread)s>MobileRead Wiki</a>. Par DMCA / autortiesību prasībām <a %(a_copyright)s>noklikšķiniet šeit</a>. Lejupielādes laiks Kļūda meklēšanas laikā. Mēģiniet <a %(a_reload)s>pārlādēt lapu</a>. Ja problēma saglabājas, lūdzu, rakstiet mums uz %(email)s. Ātra lejupielāde Patiesībā ikviens var palīdzēt saglabāt šos failus, sējot mūsu <a %(a_torrents)s>vienoto torrentu sarakstu</a>. ➡️ Dažreiz tas notiek nepareizi, kad meklēšanas serveris ir lēns. Šādos gadījumos var palīdzēt <a %(a_attrs)s>pārlādēšana</a>. ❌ Šim failam var būt problēmas. Meklējat rakstus? Šis meklēšanas indekss pašlaik ietver metadatus no dažādiem metadatu avotiem. <a %(a_datasets)s>Vairāk par mūsu datu kopām</a>. Ir daudz un dažādu metadatu avotu rakstītajiem darbiem visā pasaulē. <a %(a_wikipedia)s>Šī Wikipedia lapa</a> ir labs sākums, bet, ja zināt citus labus sarakstus, lūdzu, informējiet mūs. Metadatiem mēs parādām oriģinālos ierakstus. Mēs neveicam ierakstu apvienošanu. Pašlaik mums ir pasaulē visaptverošākais atvērtais grāmatu, rakstu un citu rakstisku darbu katalogs. Mēs spoguļojam Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>un vairāk</a>. <span class="font-bold">Faili netika atrasti.</span> Mēģiniet mazāk vai citus meklēšanas terminus un filtrus. Rezultāti %(from)s-%(to)s (%(total)s kopā) Ja atrodat citas “ēnu bibliotēkas”, kuras mums vajadzētu spoguļot, vai ja jums ir kādi jautājumi, lūdzu, sazinieties ar mums pa %(email)s. %(num)d daļējas atbilstības %(num)d+ daļējas atbilstības Ierakstiet lodziņā, lai meklētu failus digitālajās aizdevumu bibliotēkās. Ierakstiet lodziņā, lai meklētu mūsu katalogā %(count)s tieši lejupielādējamu failu, kurus mēs <a %(a_preserve)s>saglabājam uz visiem laikiem</a>. Ierakstiet lodziņā, lai meklētu. Ierakstiet lodziņā, lai meklētu mūsu katalogā %(count)s akadēmiskos rakstus un žurnālu rakstus, kurus mēs <a %(a_preserve)s>saglabājam uz visiem laikiem</a>. Ierakstiet lodziņā, lai meklētu metadatus no bibliotēkām. Tas var būt noderīgi, kad <a %(a_request)s>pieprasāt failu</a>. Padoms: izmantojiet tastatūras īsinājumtaustiņus “/” (meklēšanas fokuss), “enter” (meklēšana), “j” (uz augšu), “k” (uz leju), “<” (iepriekšējā lapa), “>” (nākamā lapa) ātrākai navigācijai. Šie ir metadatu ieraksti, <span %(classname)s>nevis</span> lejupielādējami faili. Meklēšanas iestatījumi Meklēt Digitālā aizdošana Lejupielādēt Žurnālu raksti Metadati Jauna meklēšana %(search_input)s - Meklēt Meklēšana aizņēma pārāk ilgu laiku, kas nozīmē, ka rezultāti var būt neprecīzi. Dažreiz <a %(a_reload)s>lapas pārlādēšana</a> palīdz. Meklēšana aizņēma pārāk ilgu laiku, kas ir raksturīgi plašiem vaicājumiem. Filtru skaits var nebūt precīzs. Lielām augšupielādēm (vairāk nekā 10 000 failu), kuras netiek pieņemtas Libgen vai Z-Library, lūdzu, sazinieties ar mums pa %(a_email)s. Libgen.li gadījumā, vispirms piesakieties <a %(a_forum)s >viņu forumā</a> ar lietotājvārdu %(username)s un paroli %(password)s, un pēc tam atgriezieties viņu <a %(a_upload_page)s >augšupielādes lapā</a>. Pašlaik mēs iesakām augšupielādēt jaunas grāmatas Library Genesis dakšās. Šeit ir <a %(a_guide)s>ērts ceļvedis</a>. Ņemiet vērā, ka abas dakšas, kuras mēs indeksējam šajā vietnē, izmanto šo pašu augšupielādes sistēmu. Mazām augšupielādēm (līdz 10 000 failiem) lūdzu, augšupielādējiet tos gan %(first)s, gan %(second)s. Alternatīvi, jūs varat augšupielādēt tās Z-Library <a %(a_upload)s>šeit</a>. Lai augšupielādētu akadēmiskos rakstus, lūdzu, augšupielādējiet tos arī (papildus Library Genesis) <a %(a_stc_nexus)s>STC Nexus</a>. Viņi ir labākā ēnu bibliotēka jauniem rakstiem. Mēs vēl neesam tos integrējuši, bet mēs to darīsim kādā brīdī. Jūs varat izmantot viņu <a %(a_telegram)s>augšupielādes botu Telegram</a>, vai sazināties ar adresi, kas norādīta viņu piespraustajā ziņojumā, ja jums ir pārāk daudz failu, lai tos augšupielādētu šādā veidā. <span %(label)s>Intensīvs brīvprātīgais darbs (USD$50-USD$5,000 atlīdzības):</span> ja varat veltīt daudz laika un/vai resursu mūsu misijai, mēs labprāt strādātu ciešāk ar jums. Galu galā jūs varat pievienoties iekšējai komandai. Lai gan mums ir ierobežots budžets, mēs varam piešķirt <span %(bold)s>💰 naudas atlīdzības</span> par visintensīvāko darbu. <span %(label)s>Neliels brīvprātīgais darbs:</span> ja varat atvēlēt tikai dažas stundas šur un tur, joprojām ir daudz veidu, kā varat palīdzēt. Mēs apbalvojam pastāvīgus brīvprātīgos ar <span %(bold)s>🤝 dalībām Annas Arhīvā</span>. Anna’s Archive paļaujas uz tādiem brīvprātīgajiem kā jūs. Mēs sveicam visu līmeņu iesaistīšanos un meklējam palīdzību divās galvenajās kategorijās: Ja nevarat veltīt savu laiku brīvprātīgajam darbam, jūs joprojām varat mums daudz palīdzēt, <a %(a_donate)s>ziedojot naudu</a>, <a %(a_torrents)s>sējot mūsu torrentus</a>, <a %(a_uploading)s>augšupielādējot grāmatas</a> vai <a %(a_help)s>stāstot saviem draugiem par Annas Arhīvu</a>. <span %(bold)s>Uzņēmumi:</span> mēs piedāvājam ātrgaitas tiešo piekļuvi mūsu kolekcijām apmaiņā pret uzņēmuma līmeņa ziedojumu vai apmaiņā pret jaunām kolekcijām (piemēram, jauniem skenējumiem, OCR datu kopām, mūsu datu bagātināšanu). <a %(a_contact)s>Sazinieties ar mums</a>, ja tas esat jūs. Skatiet arī mūsu <a %(a_llm)s>LLM lapu</a>. Atlīdzības Mēs vienmēr meklējam cilvēkus ar stabilām programmēšanas vai uzbrukuma drošības prasmēm, lai iesaistītos. Jūs varat būtiski ietekmēt cilvēces mantojuma saglabāšanu. Kā pateicību mēs piešķiram dalību par stabilu ieguldījumu. Kā lielu pateicību mēs piešķiram naudas atlīdzības par īpaši svarīgiem un sarežģītiem uzdevumiem. Tas nevajadzētu uzskatīt par darba aizvietotāju, bet tas ir papildu stimuls un var palīdzēt segt radušās izmaksas. Lielākā daļa mūsu koda ir atvērtā pirmkoda, un mēs lūgsim arī jūsu kodu padarīt atvērtu, piešķirot atlīdzību. Ir dažas izņēmumi, kurus varam apspriest individuāli. Atlīdzības tiek piešķirtas pirmajam, kurš pabeidz uzdevumu. Jūtieties brīvi komentēt atlīdzības biļeti, lai informētu citus, ka strādājat pie kaut kā, lai citi varētu atturēties vai sazināties ar jums, lai apvienotos. Bet esiet informēti, ka citi joprojām var strādāt pie tā un mēģināt jūs apsteigt. Tomēr mēs nepiešķiram atlīdzības par paviršu darbu. Ja divi augstas kvalitātes iesniegumi tiek veikti tuvu viens otram (vienas vai divu dienu laikā), mēs varam izvēlēties piešķirt atlīdzības abiem, pēc mūsu ieskatiem, piemēram, 100%% par pirmo iesniegumu un 50%% par otro iesniegumu (tātad kopā 150%%). Lielākām atlīdzībām (īpaši datu iegūšanas atlīdzībām) lūdzu sazinieties ar mums, kad esat pabeidzis ~5%% no tā, un esat pārliecināts, ka jūsu metode būs piemērota pilnam mērķim. Jums būs jādalās ar savu metodi, lai mēs varētu sniegt atsauksmes. Arī šādā veidā mēs varam izlemt, ko darīt, ja vairāki cilvēki tuvojas atlīdzībai, piemēram, potenciāli piešķirt to vairākiem cilvēkiem, mudināt cilvēkus apvienoties utt. BRĪDINĀJUMS: augstas atlīdzības uzdevumi ir <span %(bold)s>grūti</span> — varētu būt prātīgi sākt ar vieglākiem. Dodieties uz mūsu <a %(a_gitlab)s>Gitlab problēmu sarakstu</a> un kārtojiet pēc “Etiķetes prioritātes”. Tas aptuveni parāda uzdevumu secību, kas mums rūp. Uzdevumi bez skaidrām atlīdzībām joprojām ir piemēroti dalībai, īpaši tie, kas atzīmēti kā “Accepted” un “Annai mīļākie”. Jūs varētu vēlēties sākt ar “Sākuma projektu”. Viegls brīvprātīgais darbs Tagad mums ir arī sinhronizēts Matrix kanāls pie %(matrix)s. Ja jums ir dažas brīvas stundas, jūs varat palīdzēt dažādos veidos. Noteikti pievienojieties <a %(a_telegram)s>brīvprātīgo čatam Telegram</a>. Kā pateicības zīmi mēs parasti piešķiram 6 mēnešus “Veiksmīgais Bibliotekārs” par pamata sasniegumiem un vairāk par turpmāko brīvprātīgo darbu. Visiem sasniegumiem ir nepieciešams augstas kvalitātes darbs — paviršs darbs mums kaitē vairāk nekā palīdz, un mēs to noraidīsim. Lūdzu, <a %(a_contact)s>rakstiet mums e-pastu</a>, kad sasniedzat kādu sasniegumu. %(links)s saites vai ekrānuzņēmumi ar pieprasījumiem, kurus izpildījāt. Izpildīt grāmatu (vai rakstu utt.) pieprasījumus Z-Library vai Library Genesis forumos. Mums nav pašiem sava grāmatu pieprasījumu sistēma, bet mēs spoguļojam šīs bibliotēkas, tāpēc to uzlabošana padara arī Annas Arhīvu labāku. Sasniegums Uzdevums Atkarīgs no uzdevuma. Nelieli uzdevumi, kas publicēti mūsu <a %(a_telegram)s>brīvprātīgo čatā Telegram</a>. Parasti par dalību, dažreiz par nelielām atlīdzībām. Mazi uzdevumi, kas ievietoti mūsu brīvprātīgo tērzēšanas grupā. Pārliecinieties, ka atstājat komentāru par problēmām, kuras esat novērsis, lai citi nedublētu jūsu darbu. %(links)s saites uz ierakstiem, kurus uzlabojāt. Jūs varat izmantot <a %(a_list)s >nejaušo metadata problēmu sarakstu</a> kā sākumpunktu. Uzlabot metadatus, <a %(a_metadata)s>saistot</a> ar Open Library. Šiem vajadzētu parādīt, ka jūs kādam pastāstāt par Annas Arhīvu, un viņi jums pateicas. %(links)s saites vai ekrānuzņēmumi. Izplatot ziņu par Annas Arhīvu. Piemēram, iesakot grāmatas AA, saistot ar mūsu emuāra ierakstiem vai vispārīgi novirzot cilvēkus uz mūsu vietni. Pilnībā iztulkot valodu (ja tā nebija jau gandrīz pabeigta). <a %(a_translate)s>Tulkot</a> vietni. Saites uz rediģēšanas vēsturi, kas parāda, ka esat veicis nozīmīgus ieguldījumus. Uzlabot Annas Arhīva Wikipedia lapu jūsu valodā. Iekļaujiet informāciju no AA Wikipedia lapas citās valodās, kā arī no mūsu vietnes un bloga. Pievienojiet atsauces uz AA citās atbilstošās lapās. Brīvprātīgo un atlīdzību 