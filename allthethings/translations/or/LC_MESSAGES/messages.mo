��    �     �4              <i     =i     Xi     si     �i     �i     �i     �i     �i     j     j     <j     Xj     sj     �j     �j     �j     �j     �j     k     k     5k     Mk     ek     }k     �k     �k     �k     �k  "   l     $l      Bl     cl  '   �l  ,   �l  (   �l  $    m  (   %m  -   Nm  '   |m  )   �m  $   �m  $   �m  $   n  $   =n  )   bn     �n  %   �n  "   �n  %   �n  !   o  !   <o  $   ^o     �o  !   �o  "   �o      �o     p  #   &p     Jp     jp     �p  "   �p     �p  #   �p  #   q  #   0q      Tq     uq     �q     �q     �q     �q  #   r     +r  #   Ir     mr  #   �r     �r  #   �r     �r     s     *s     Ds     ds     �s     �s     �s     �s     �s     t     1t     Nt     kt     �t     �t     �t     �t      �t      u      <u     ]u     xu     �u     �u     �u     �u      v     v     8v     Tv     pv     �v     �v     �v     �v     �v     �v     
w      (w      Iw  (   jw  0   �w  .   �w  .   �w  -   "x  +   Px  +   |x  +   �x  +   �x  +    y  +   ,y  +   Xy  )   �y  *   �y  )   �y  )   z  )   -z  )   Wz  )   �z  )   �z  )   �z  )   �z  +   ){  ,   U{  ,   �{  ,   �{  )   �{  )   |  )   0|  *   Z|  ,   �|  2   �|  7   �|  :   }  4   X}  6   �}  -   �}  6   �}  2   )~  1   \~  1   �~  .   �~  4   �~  @   $  ;   e  =   �  >   �  =   �  ;   \�  4   ��  ,   ̀  /   ��  #   *�  #   N�  '   r�  '   ��  '     #   �  #   �  #   2�  "   V�     y�  #   ��  #   ��  #   ߂     �  "   �  "   @�  "   c�  "   ��     ��     Ƀ     �  "   	�  (   ,�  (   U�  (   ~�  (   ��  (   Є     ��     �     3�  !   O�  '   q�  '   ��  '   ��  '   �  '   �  '   9�  &   a�  ,   ��  ,   ��  ,   �  #   �  )   3�  )   ]�  )   ��  )   ��  )   ۇ     �     #�  $   B�  $   g�  #   ��  )   ��  )   ڈ  )   �  )   .�  )   X�  )   ��  )   ��  )   ։      �     �  #   =�  #   a�  #   ��  #   ��  "   ͊     ��     �     .�     M�     k�     ��     ��  !   ��  !   ��     �  &   "�  ,   I�  ,   v�     ��  *   ��  -   �  0   �  +   F�  2   r�  .   ��  6   ԍ  6   �  <   B�  -   �  6   ��  1   �  .   �  +   E�  3   q�  %   ��  %   ˏ  %   �  %   �  #   =�     a�  -   ��  /   ��  -   ސ  $   �  $   1�  $   V�  $   {�  $   ��     ő     �  $   �  *   &�  *   Q�  *   |�  *   ��  *   Ғ  *   ��  "   (�  "   K�  "   n�  "   ��  "   ��     ד  %   ��  %   �  %   C�  %   i�  %   ��  %   ��     ۔  %   ��  %   !�  %   G�  %   m�  %   ��  "   ��  (   ܕ  (   �     .�     K�     i�     ��     ��     ��     Ֆ     �     
�  !   &�     H�     b�     x�     ��     ��     ��     ӗ      �     �     *�     F�     b�     ~�     ��     ��     Ҙ     �     ��     �     �     *�     ;�     U�     p�     ��     ��     ��     ʙ     �     ��     �     )�     A�     Y�     p�     ��     ��     ��     ͚     �     �     �     9�     T�     o�     ��     ��     ��     ݛ     ��  '   �  $   :�  +   _�  9   ��  -   Ŝ  (   �  .   �  '   K�  0   s�  0   ��  0   ՝  0   �  0   7�  0   h�     ��     ��     ��     Ξ  1   �     �  ,   4�  '   a�  *   ��  7   ��  #   �     �  )   0�  *   Z�  ,   ��  /   ��  ,   �  /   �  (   ?�  -   h�  %   ��  2   ��  6   �  +   &�  ,   R�  /   �  .   ��  6   ޢ  -   �  ,   C�  )   p�  %   ��  (   ��  '   �  '   �  $   9�  $   ^�  #   ��  1   ��  '   ٤  0   �     2�     P�     n�     ��  !   ��  %   ̥  ,   �  #   �  $   C�  $   h�  '   ��  #   ��      ٦  #   ��  $   �  %   C�  "   i�  3   ��  "   ��  9   �  #   �  $   A�  &   f�  "   ��      ��  !   Ѩ  $   �  4   �  +   M�  %   y�  %   ��  "   ũ  $   �     
�     '�  $   <�  2   a�  +   ��  /   ��  '   �  $   �  #   =�  "   a�     ��      ��  "   ��      ܫ     ��  +   �     G�      f�  (   ��      ��      Ѭ      �  #   �  +   7�  *   c�  )   ��  .   ��  0   �  8   �  '   Q�  6   y�  1   ��  "   �  .   �      4�  #   U�  )   y�     ��     ¯      �      �  !   $�  %   F�     l�     ��      ��     Ȱ  (   �     �     ,�  (   I�      r�  &   ��  $   ��  &   ߱     �      %�      F�  !   g�  $   ��  $   ��     Ӳ     �  $   �  !   4�     V�  "   u�      ��      ��  $   ڳ  $   ��  '   $�  #   L�  "   p�  %   ��  !   ��      ۴     ��     �     3�     Q�     n�     ��     ��     ��     ɵ     �     ��     
�     #�     9�     O�     i�     z�  !   ��  $   ��  %   Ҷ  %   ��  5   �  *   T�  *   �  &   ��  )   ѷ  %   ��  ,   !�  &   N�  *   u�  -   ��  -   θ     ��  &   �  '   @�  .   h�  ,   ��  ,   Ĺ  %   �  (   �  '   @�  '   h�  (   ��  (   ��  (   �     �     )�     I�     `�     |�     ��     ��     ��     ջ     ��     �     !�     8�     K�     h�     y�     ��     ��  @   ��     ��     �  6   ,�     c�     ��     ��     ��     ½     ߽  )   ��     &�     ?�     \�     w�  &   ��  !   ��  )   ۾     �     #�     A�     Z�     q�     ��      ��     ��     ݿ     ��  !   �     9�     S�     l�     ��      ��     ��     ��      �     $�     9�  &   N�      u�     ��  #   ��     ��  !   ��  !   �  "   2�  (   U�     ~�      ��  #   ��     ��      �       �      A�  #   b�  !   ��     ��  &   ��     ��     �     $�     B�     ^�     u�     ��     ��     ��     ��  '   ��  &   �  &   E�  #   l�     ��     ��  #   ��  #   ��  #   �  #   <�  $   `�  +   ��  '   ��  $   ��  $   ��  $   #�  &   H�  0   o�  5   ��  .   ��  *   �  -   0�  $   ^�  $   ��  #   ��  ,   ��  (   ��     "�  %   @�  )   f�  *   ��  *   ��     ��  *   �     1�  !   O�     q�  +   ��  )   ��  ,   ��  0   �  (   C�  $   l�  "   ��  %   ��  ,   ��  '   �  +   /�  (   [�  &   ��  +   ��  &   ��  &   ��     %�  '   C�  %   k�  $   ��  $   ��  (   ��  "   �     '�  )   E�  )   o�  )   ��  !   ��     ��  +   �  *   -�  -   X�  &   ��  "   ��  $   ��     ��     �     /�     L�     i�      ��  !   ��  !   ��  !   ��  !   
�  ,   /�  ,   \�  "   ��      ��  !   ��  #   ��     �     .�     L�  $   j�  $   ��  $   ��  "   ��  "   ��  "   �  %   B�  %   h�  %   ��  "   ��     ��     ��  "   �  "   :�  "   ]�  )   ��  &   ��  &   ��  )   ��  &   "�  &   I�  )   p�  %   ��  '   ��  #   ��  #   �  &   0�  &   W�  #   ~�  0   ��  (   ��  (   ��  -   %�     S�  $   g�  $   ��  $   ��      ��  "   ��  "   �      =�     ^�  #   |�     ��  '   ��  '   ��  %   �  '   6�  (   ^�  )   ��  &   ��  0   ��  %   	�  !   /�  &   Q�  &   x�  $   ��  "   ��  "   ��  "   
�  (   -�  !   V�  *   x�  -   ��  "   ��  &   ��      �  +   <�  )   h�  &   ��  "   ��  !   ��  *   ��  #   )�  (   M�  %   v�     ��  )   ��  (   ��  #   �  !   3�     U�  '   u�  &   ��     ��  (   ��  "   �  #   +�  )   O�     y�     ��  -   ��  *   ��     �      .�      O�  *   p�  0   ��  $   ��  *   ��  0   �  1   M�  @   �  3   ��  3   ��  3   (�  3   \�  3   ��  3   ��  ,   ��  <   %�  5   b�  3   ��  3   ��  3    �  3   4�  3   h�  ;   ��  ;   ��  6   �  ;   K�  8   ��  ,   ��  #   ��     �     /�     L�     e�  #   ��      ��  "   ��     ��     �     !�  #   >�     b�     {�     ��     ��     ��     ��     ��  #   �  )   +�     U�     j�     ��     ��     ��     ��     ��     	�     $�     ?�  %   \�  +   ��  *   ��  +   ��  *   �  +   0�  *   \�  +   ��  )   ��     ��     ��  &   �     A�  #   Z�     ~�  #   ��     ��     ��     ��  #    �  #   $�     H�  +   ^�  4   ��  "   ��  *   ��     
�     *�     C�     \�      o�     ��  )   ��  )   ��  "   ��  )   �  "   E�  %   h�  %   ��  )   ��  #   ��     �  #   "�  (   F�  )   o�  /   ��  "   ��  &   ��  "   �  (   6�  %   _�     ��  #   ��  3   ��  "   ��  !    �     B�  "   b�  #   ��  '   ��  )   ��  %   ��  &   !�  2   H�  2   {�  2   ��  3   ��  2   �  2   H�  3   {�  #   ��  #   ��     ��      �  %   5�  $   [�  %   ��  ,   ��  /   ��     �      #�  0   D�  2   u�  )   ��  #   ��  %   ��  $   �     A�  %   a�  "   ��      ��  %   ��  "   ��  "   �  %   7�  &   ]�  $   ��  !   ��     ��     ��     ��  $   �      A�  *   b�     ��     ��     ��     ��     ��     �     .�     F�     ]�     y�  %   ��  $   ��  %   ��  $   �  %   )�  $   O�  %   t�     ��     ��     ��     ��     �     )�     D�     _�     q�     ��  &   ��      ��     ��  &   
�     1�     N�  "   j�  "   ��     ��  &   ��  (   ��  (   �  (   E�  (   n�     ��      ��  %   ��  &   ��      �  .   >�  )   m�  -   ��  -   ��  -   ��  -   !�  +   O�  ,   {�  (   ��     ��  !   ��  )     "   =     `  !   ~     �  "   �     �      �         3 #   O    s    �    � !   � "   � *    '   6 #   ^ (   � #   �    �    �         -   : +   h    �    �    �    �          "   < -   _ )   � *   �    � "   � $     &   E '   l $   � "   � '   � "    "   ' !   J    l $   � $   � "   � '   �    ! +   A *   m $   � (   � %   � (    (   5 '   ^ &   � &   � )   �     �    	    8	    U	    r	 &   �	    �	     �	 !   �	    
 )   .
 $   X
    }
    �
    �
    �
    �
 "   �
 "        ?    `         � .   � *   �     "   & !   I    k    �    �    �    �    �    
    
    *
    >
    R
    e
    x
    �
    �
    �
    �
    �
            -    A    W    k    �    �    �    �    �    �        '    ?    W    n !   �    �    �    �    �        8    S    n    �    �    �    �    �    �    
        2    F    U    j     .   � &   � &   � !       3    H    ]    r    �    �    �    �    �    �    
    "    7    L    d    z    �    �    �    �    �        3    J    `    w    �    �     �    �        !    7    J    _     x    �    �    �    � $       3    P    g    |    �    �    �    �    � !       9    X    x    �    �    �    �    �        *    :    U    h    x &   � #   �    �    �        7    V    u    �    �    �    �    �             0    D    `    r    �    �    �    �    � $   �      "   4 !   W "   y    � $   �    �    �        5    T    r    �     �    � $   � '    !   < #   ^     �     � &   � !   � '   
    5 "   R *   u %   � %   � (   � 9    2   O +   � &   � &   �    � "     (   ?     h     �     �      �  )   �  -   	!    7!     T!    u! )   �!    �! &   �! !   " (   &" $   O"    t" $   �" $   �" %   �" #   # &   %#     L# )   m#    �#    �# #   �#    �#    $ "   /$ "   R$ (   u$    �$    �$ %   �$ '   % -   )%    W% &   u%    �%    �% $   �%    �%    &    5& $   O&    t&    �&    �&    �&    �&    �&    '    &'    9'     Q'    r'    �'    �'    �'    �' !   (    '(    A(    ^(    u(    �(    �( !   �( !   �( !   )    &)    E)    e) #   �)    �)    �)    �) %   �) "    * !   C*    e*    �*    �*    �*    �*    �*    +    7+    V+    k+    �+    �+    �+    �+    �+    �+    , %   9,    _,    |,    �, #   �,    �,    �,    -    /-    K-    c-    {-    �-    �-    �-    �-    �-    
.    '.    =.    R.    k.    �.    �.    �.    �.    /    &/    E/    d/    �/    �/    �/    �/    �/     0    0 "   80 "   [0 $   ~0 $   �0 &   �0 &   �0 &   1 $   =1 "   b1 "   �1 $   �1 "   �1    �1 $   2 .   12 .   `2    �2    �2 "   �2 "   �2 "   
3 "   03 "   S3    v3 "   �3     �3    �3    �3 '   4    04    C4    V4    u4    �4    �4 $   �4 -   �4 &   5    B5    _5    y5    �5    �5    �5 /   �5 -   6    E6    _6 !   z6 ,   �6 -   �6 '   �6 (   7 (   H7 %   q7    �7    �7    �7    �7     8    8    38    F8    e8    }8    �8    �8    �8    �8    �8    �8    	9    "9    A9    T9 !   f9 )   �9 )   �9    �9 !   �9 "   : "   =:     `: !   �: #   �: #   �: #   �: #   ; )   3; "   ]; )   �; -   �; 1   �; "   
< "   -< $   P< !   u< "   �< )   �< "   �<    = "   &= !   I=    k=    �=    �=    �=    �=    �= &   	>    0> (   L> -   u>    �> !   �>     �> !   �> !   !? !   C? $   e?    �? &   �? !   �? &   �? '   @ &   =@    d@ !   }@ %   �@    �@     �@ *   A $   -A "   RA #   uA #   �A    �A %   �A    B    B     0B    QB    kB    �B    �B    �B    �B     �B    C '   "C    JC    \C    wC    �C    �C    �C    �C    �C    D *   ;D (   fD (   �D (   �D (   �D (   
E (   3E (   \E '   �E &   �E %   �E %   �E 8    F -   YF (   �F #   �F &   �F !   �F "   G 2   @G 4   sG 4   �G )   �G ?   H 7   GH 1   H +   �H &   �H +   I &   0I    WI �  oI �  �J   �Q 7   �T _  �T   >W �  ^[ ,  *_ �  Wb �   �d �   �e Q   [f �   �f �  bg 0  \k \  �n �  �p �  �u �  x �  �| �  �� 1  Q�   �� �  �� n   R�   �� ~   ƒ �  E� 0   � �  D� Q   �� N   H� +   �� A   Ý �   � a   �� :   � e   &� [   �� C   � +   ,� �   X� �  � �  �� �   l� z   W�    ҧ    � !   ��    �    "�    B�    b� 1   ~�    �� I   ��    � a   � .   p� 
   �� '   �� A   թ S   � 3   k� k  �� �  �   � 7   � �  *� y  � l  Z� �  ǻ 0   �� 6  �� 0   � q  � 5   �� �  �� 0   S� �  �� o   E� �   ��    a� �  }� v  G� �  �� 
  z�     �� �   �� �   u� �  o� ^   � z   {� P  �� �   G� �   �� Y   ��    !�    .�   /� �   <� H   � �  I� �   A� $  �� �   � �  �� *  !� u  L� �   �� �  �� &  l� Q   �� |  �� �  b� �   �� �   �� �   U�    J� �  f� �  
� Q   ��    
� �   � �  ��    � @  �    / J  P B  �   �
 1  � 4   �   M �   � �   � �   i �   * �   � V   � �   B �   ; �   � :   � ;  � �   �   � �  � #       �  3  �# �  �% �  �' %  �, �  �- �  �/ A  ^2 P  �3 �  �4 h  �6 �  �8 n  �:    = `  = �  |> �  @ D  �A �   �C =  �D   #F �  'G �   �J �  �K J  �N x   %Q �   �Q F  YR   �S �  �T �  SX �   Z 0  [ U   9\ *  �\ �  �a �  �c F   Mg �   �g ~  h   �i D  l �  cn 9  `p �  �t U    x G  vx �  �{ �  �� 2  V� �  �� �  m� �   � F  ��    � �  � �  ޓ �  �� :  M� p  �� �  ��    �� 0  �� J  ǣ R  �    e� �  x� e  �    h� �  i�   Q� �   W� %   /� �  U� >  �    D� �  ]� G  L�   ��   �� !  �� 6  �� �   �   �� T   � $   \�   �� $  �� �  �� p   B� Q   �� b  � 0  h� 4  �� �   �� 1  �� >   �� 4   9� {  n� �  �� �   �� b   8� +  �� 3  ��    �� d   � }   z� �   �� �   �� �   � �   �� +   �� e   �� �   ?� �   �� �   q� �   �� �   ��    ~� 5   ��   �� �  �� q  q�    �� %  � (   
 �  3 �   �  � W  j
   �
   B !  F �  h Q   F t   � 5   
 �  C X  ?!   �% �  �) $  O, 	  t/ t  ~1 0  �5 `   $8    �8 �  �: =   =    \= �  }? a  pB C  �C U  G    lI    �I   �I �  �N �  lR P  X �  _Y 7   K[ <  �[ �  �] Q   ` �   �` �  wa \  �c �  Wf �  �g �   sk ;  Tl    �n /  �n Q   �p -  (q �   Vt �   �t    �u �   �u *   ov B   �v P   �v 1   .w �  `w L  �x �  K h  ݃ �  F� [   � ]  {� 1   ٌ 1  � X   =�   �� �   �� 7   y�   �� b  ̕ �  /� w    � R  x� i   ˟ �  5� �   ܣ -   �� �  �� �  r�   \� �   q� `  � �   t� {  r� O  � X  >� ^  �� �  �� �  �� �  �� &   %� �  L� $   � 1  =� n   o� >   ��    �    #� D   =� (   �� G   �� =   ��    1�    M�    `� $   |� 2   �� S   ��    (� ^   5�    �� ,   �� �  �� �   �� A   m�    �� {   �� S   I� �   �� �   -� <   ��    ��    � !   /� (   Q� '   z� (   ��    �� "   ��    
� +   &� �   R� F   �� 9   0� W   j� ^   �� \   !� V   ~� ?   �� L   � |   b� %   �� �   � �   ��    e� �   �� �   1�    ��    
� .   (� 4   W� C   �� 3   �� E   � (   J�    s� %   {�    �� (   �� "   �� 	   ��    �    #� P   &�    w�    ~� 	   �� &   �� 	   �� D   ��    �    � .   �    J�    Z� A   f�    �� )   �� J   ��    %� .   -� 	   \�    f� ;   ��    ��    �� Q   ��    4� "   J�    m� O   ��    �� %   �� %   � 
  8� �  C� �   ��   �� s  �� �   @� 9   "� ,   \� &   ��    �� $   �� "   ��    �� e   � �   ~� �   6� �   �� I   �� h   �� m   Y� �   �� �   �� �  k� �   _� �   ^� N   � %   R�    x� !   ��    �� &   �� )   ��    � 1   �    P�    Y� L   x�    �� "   �� ;   ��    (� C   9�    }� 7   ��    ��    ��    �� &   � /   8� 2   h� �  ��    q�    }�    �� c   ��    �� �     �   �  L   $ n   q �   �    d    l    t   w    � (   � N   � �    >   � +   ! 7  M T   � k  �   F
 +  K �  w 5   k   E �  � �   3    �  3        $ �   ; �   � �   � �   \ �   � �   � �   3  I   (! h   r!    �! 4   �! �   )" >   �"    # &   # &   5# =   \# =  �#    �$ �   �$ �   �% E   2& S   x& �   �& �   V' `  L( A   �+ +   �+ �  ,    �-    �-    �- ?   .    W. e   p. %  �. 6   �1    32 1   J2    |2 %  �2 L   �5    6    6 �   16    �6    7 b   7 ;   z7    �7 N   �7 1  8 6   G9    ~9 �   �9 /   �: "   �: !   �: 8   �: �   7; �   �; (   �< �   �< U  �= 5  �> �   @ %   �@ k  �@ �   7C $   /D c   TD $   �D �  �D �  zF 1   >H �   pH l  9I �  �J 1   �L ;   �L ,   M �  1M 6   �P N   �P T   GQ W   �Q Z   �Q V  OR &   �S 3   �S q   T �   sT    U $   U /   >U \   nU �  �U �  S[ �  �^ n   �c [   :d    �d E   �d   �d L  �f h  ?i J   �j �  �j   �l P   �o N   8p i  �p a  �u    Sx .   lx >   �x    �x �  �x 2   �| 3  �| 5  � H  !� !   j�   �� u   �� Q   � �   k� r  F� )  �� T  � H  8� �   �� c  V� �   �� �  Y� �  �� �   }� �  =� h   �� W   I� %   ��    ǜ 5   �� T   � >   k� �   �� t   l� 	   � |   � �  h� n   ��   l� Q  �� `  ץ U   8� G   �� ,   ֧ "   � B   &� Q   i� _   �� D   � �  `� N   1� �  ��    z� �  �� �   R�   !� �  A� �  ѳ B   ��    ѵ 	   � &  ��   #� G   =� '  ��    �� "   ��    ݿ ;   �� o   6�    �� 	   �� �   �� �  �� s  ;�    �� 	   �� S   �� q   � n  �� P  �   R� 8   g� 8   �� J   ��     $� 7   E� 1   }�    �� �   �� J   B� *  �� �   �� 7   ��   �� �   �� �   ��   u� �   �� �   m�    � �   '� �   � >  �� �   �� �   ��    [� �  x� �   ?� e   )� �   �� �   -� \   �    d� W   }� �   ��    �� �   �� ?  J�    �� �  �� �   _� �   �   ��    �� O  �� K  A� 4   �� c   �� (   &� %   O� �  u� U   .� �   �� �  7� n  ��   S� �  e�   ^�   v� �  |  �   K �  . �   � �  � �  B
 �  � 
   �
 
   �
 
   �
 D  �
 
    
    �   - �   �   � 
   � H  � y       � �   � *  : �   e 
   � ;  � 
   6 
   D 
   R ~  ` H  � �  ( ;   �"    #    /# �  H# S   �$ S   8% @  �% G  �' N   )    d) `   t) w   �) y   M* �   �* D   a+ G   �+ h  �+ V   W. �  �. $  �2   �4 �   �5 c  �6 ~  8 N  �: �  �> z  C S  �D   NF    [G   {G 5   �J �  �J �  �M b  �O 9  �Q �  S �  �U   UW �   rX X   gY w   �Y    8Z a   XZ    �Z (   �Z ;   �Z �  9[    
] �   &] c   �]    W^ !   t^    �^ =   �^ 
  �^ �   �_ �   �` �  [a c   Rc    �c    �c A   �c N   2d    �d    �d    �d    �d    �d    �d    �d    �d �   e �   �e    Cf    Tf    nf    �f    �f    �f    �f    �f    g <   $g M   ag �   �g +   �h O   �h �   i =  �i u  (k �  �l �  An Q  �p �  0r )   u -  9u V   gv �   �v �   kw '  ,x �  Ty �   M{ �   �{    �| $  �| �   �} �   �~    �     �    � *   � %   "� T   H�    �� >   �� !   �� >   � A   V� M   ��    �     ��    �    <�    C�    _�    l� L   t�    �� u   Ȃ n   >� j  �� G  � w   `� N   ؆ 0  '� �   X� �   Q� �   @� a   Ŋ U   '� �   }� p   � T   ��   ٌ �  ގ �  a� G   I� y   �� *  � �   6� �  �� %  d� a  �� �   � D   �� �  Ϝ :  �� .  ̟ f   �� �   b� �  1� E   �� |   �� G   v� �   ��   f� �   r�    j�    ��    �� ^   �� e  � �   X� y   � R   \� N   �� K   �� )   J� �   t� 4   � �   :� a    �   b� �   j� �   � �  Ʊ W   �� -   � ,   5� -   b� ,   �� -   �� ,   � -   � w   F� ~   �� �  =� �   � I   �� �   � b   ��    � /  
�   :�    T� �  U�    0�   D� L   c� 2   �� $  �� E   � �   N� �   �� W   �� �   �� �   �� �   m� 6   
� W   D� �  �� y   %� B   �� �   �� G  �� �   �� O  �� �   �� �   �� r  �� e   ?� �   �� N  6� 5   �� �   �� T   �� �  
� _    � �   `� M   4� E   �� �  �� �   L� k   � W  �� �   �� �   �� P  w� X  �� '   !� B   I�   �� �   �� &   d� <   ��    ��    �� 8    � 9   9� �   s� {   ?� N   �� U   
� b   `� G   �� E   � '  Q� �   y� e  � D  q� ]   ��   � _  -� H   �� �   �� .   j� �   �� �   .� @   �� <  � .  @� E   o� �   �� 7   O� H   �� �   �� .   a� "  �� @   �� !   �� w   � �   �� �  k  �   R $    $   0 �   U $    �   3 �    n  � �    8    �   > �  � S   �
 �  �
 �   s B   "
 �   e
 A  .    p    �    � %   � T   � l    �   p :   :     u 	   � S   � �   � �   �    } �   � �        � p   � �   p 1    M   B �   � �  a �   	    �    � 8  �    �     ?   �  �  ! J  % o   O) �   �) R   �*   + �   1    �1 L   �2 B   �2 Y  13 P   �7 �   �7 �   �8 �   �9 	  ^: [   h; �   �; �   �< \   H= �   �= s   .> K   �> x   �> R   g? �   �? v  ]@ �  �A    �C �  0D q  �E   /I �   IK �  �K �  �N   �Q   �S �  �V ]   �X ;   Y o  UY U   �Z   [ "  5` �   Xa     �a O   b e  Sb 	   �f �  �f �  �h }  �k �  n �   �p �   �q �   tr �   @s k   �s �   Ut   /u ;   2v S   nv p   �v m   3w    �w �   �w �   >x e   �x    By �   Ry &  1z 9  X| 7   �} 4   �}    �} �   ~ �   �~ �   V �  � �   ��    r� c   ��   �    �   � �  �   �� �   �� O   ^�    ��    ď    я �   Տ s   W� D  ː %  � �   6�    ��    � 0   1� E   b� �   ��    �� K   �� "   � u   +� z   �� ,   �    I� �   b�    F� K   Y� A   �� N   �    6�   :� �  P� �   � �   �� �   �� �  U�    E� E   V� �  �� �  Q�   ۤ    [�   {� �   �� �   m� '  �� �   $� �   � .   �� �   #�    Ѭ +   � (   � +   F� +   r� (   �� %   ǭ ,   � "   �    =� i   V� s   �� s   4� 8   �� n   � Z   P� Q   �� G   �� 3   E� s   y� ;   �    )� �   <� a   �   i� j   � (   � 9   � K   M� :   �� +   Ե �    � �   �   ��   �� 0   �� '   � 4   �    I� ~   h� 	   � 5   � ?   '� �  g� '   � ,   �    A� 
   H� 	   S� �   ]� N   � �  W� R   �� 2   >� -   q� /   �� ?   �� K   � @   [� B   �� d   �� o   D� `   ��    � �   � C   � L   I�    �� ]   �� �   � B   �� <   	� m   F� X  ��   
� �   �    �� P   �� 	   � -   &� 4   T� A   �� Q  �� >  � .   \�    �� Y   �� 2   �� \   �    y�    ~� 1  �� u   ��   @� ;   ^� @   �� E  �� @   !� 9   b� <   �� ?   �� U   � ?   o� $   �� "   �� \   �� >   T� [   �� s   �� b  c� �   �� �   �� �   &� @   �� w  �� J   l� %   �� �   �� +   �� O   �� I   � �   R� �   �� T   �� �  � %   �� 5   �� 9   �� (   .� 3   W� $   �� S   �� -   � *   2� �   ]�   � A   � O   `� 2  �� _  �� N   C� -   ��    ��    �� *   ��    �    !� '   :�    b� &   t�    ��    ��    ��    ��    ��    ��    �� #   
� S   .�   �� ?  �� �  ��    e� ~  ��    A  
    O J  k ;   �	 O  �	 �  B      " l  > W   � x   \   | /   � �   	 �   � �   � �   ~ �  u D  >   �  V  �" "   �' �  
(   �) �   , |  �, (   |. �  �. �  �0 e  4 �  t6    f8 �   �8 �   :9   ": �  7< �   �= �   �> '   �? _   �? -   /@ �   ]@ j   �@    bA �   vA �   _B �   �B ^   �C n  D g  rE �   �F �   �G �   H �   9I �   J �   �J d  �K �   2M   N I  'O (   qP �   �P m    Q $  �Q h   �R    S !   5S �   WS Q   
T A   \T    �T �   �T ~   SU �   �U J   mV    �V    �V    �V 	   �V �   �V �   �W �   �X �   �Y    +Z N   3Z �   �Z %   [    5[    E[    U[    q[    �[    �[ "   �[    �[ "   �[     \ 1    \    R\    e\    �\ $   �\ (   �\    �\    ]    &] x   6] 7   �] 4   �]   ^ $   _ 	  C_ �  M` "   b A   4b    vb    �b    �b    �b    �b �  �b �   Rd �   Pe "   �e 8   �e   6f (   Cg 5  lg ;  �h K   �i 0   *j (  [j �  �k �   km �  :n �   �o 5   �p w  �p !   ^r (   �r �   �r x  Ws X   �t a  )u +  �v �  �w �   Cy (   z    Dz "   Zz    }z (   �z    �z    �z %   �z L  {   h| !  j} �  �~ \  � �   r� �   W� *  �   0� !  =� �  _� S  � �  >�    � �  )� �  � �  �� �  d� �  �� \  ϥ   ,� 4   .� �   c�   �� �  }� �    �   ��    ߲    �� B   � q  W� �   ɴ W  j� m   ¶ �   0� �   #� �   �� �   �� �  � �   �� _   T� �   �� �  m� S   g�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.hdd-prices blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.title blog.how-to.tldr blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.footnote blog.introducing.signature blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.isbndb-dump.10% blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text2 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.text1 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: or
Language-Team: or <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-ଲାଇବ୍ରେରୀ ଏକ ଲୋକପ୍ରିୟ (ଏବଂ ଅବୈଧ) ଲାଇବ୍ରେରୀ। ସେମାନେ ଲାଇବ୍ରେରୀ ଜେନେସିସ ସଂଗ୍ରହକୁ ନିଅଇ ଏହାକୁ ସହଜରେ ସନ୍ଧାନ ଯୋଗ୍ୟ କରିଛନ୍ତି। ଏହାର ଉପରେ, ସେମାନେ ନୂତନ ପୁସ୍ତକ ଅବଦାନକୁ ପ୍ରୋତ୍ସାହିତ କରିବାରେ ଖୁବ ଦକ୍ଷ ହୋଇଛନ୍ତି, ବିଭିନ୍ନ ସୁବିଧା ସହିତ ଅବଦାନକାରୀ ବ୍ୟବହାରକାରୀଙ୍କୁ ପ୍ରୋତ୍ସାହନ ଦେଇ। ସେମାନେ ବର୍ତ୍ତମାନ ଏହି ନୂତନ ପୁସ୍ତକଗୁଡ଼ିକୁ ଲାଇବ୍ରେରୀ ଜେନେସିସକୁ ପୁନଃଅବଦାନ କରୁନାହାନ୍ତି। ଏବଂ ଲାଇବ୍ରେରୀ ଜେନେସିସର ତୁଳନାରେ, ସେମାନେ ସେମାନଙ୍କର ସଂଗ୍ରହକୁ ସହଜରେ ମିରର କରିବା ଯୋଗ୍ୟ କରୁନାହାନ୍ତି, ଯାହା ବିଶାଳ ସଂରକ୍ଷଣକୁ ବାଧା ଦେଇଥାଏ। ଏହା ସେମାନଙ୍କର ବ୍ୟବସାୟ ମଡେଲରେ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ, କାରଣ ସେମାନେ ସେମାନଙ୍କର ସଂଗ୍ରହକୁ ବଡ଼ ପରିମାଣରେ ପ୍ରବେଶ କରିବା ପାଇଁ ଅର୍ଥ ଚାର୍ଜ କରନ୍ତି (ଦିନକୁ 10 ରୁ ଅଧିକ ପୁସ୍ତକ)। ଅବୈଧ ପୁସ୍ତକ ସଂଗ୍ରହକୁ ବଡ଼ ପରିମାଣରେ ପ୍ରବେଶ କରିବା ପାଇଁ ଅର୍ଥ ଚାର୍ଜ କରିବା ବିଷୟରେ ଆମେ ନୈତିକ ମତାମତ ଦେଇନାହିଁ। ଏହା ନିଶ୍ଚିତ ଯେ Z-ଲାଇବ୍ରେରୀ ଜ୍ଞାନର ପ୍ରବେଶକୁ ବିସ୍ତାର କରିବାରେ ଏବଂ ଅଧିକ ପୁସ୍ତକ ସଂଗ୍ରହ କରିବାରେ ସଫଳ ହୋଇଛି। ଆମେ କେବଳ ଆମର ଭାଗ କରିବାକୁ ଏଠାରେ ଅଛୁ: ଏହି ବ୍ୟକ୍ତିଗତ ସଂଗ୍ରହର ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣ ସୁନିଶ୍ଚିତ କରିବା। - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>) ପାଇରେଟ୍ ଲାଇବ୍ରେରୀ ମିରରର ମୂଳ ମୁକ୍ତିରେ (ସମ୍ପାଦନା: <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a> କୁ ସ୍ଥାନାନ୍ତରିତ), ଆମେ Z-ଲାଇବ୍ରେରୀର ଏକ ମିରର ତିଆରି କରିଥିଲୁ, ଯାହା ଏକ ବଡ଼ ଅବୈଧ ପୁସ୍ତକ ସଂଗ୍ରହ। ଏହାକୁ ସ୍ମରଣ କରାଇବା ପାଇଁ, ଆମେ ସେହି ମୂଳ ବ୍ଲଗ୍ ପୋଷ୍ଟରେ ଯାହା ଲେଖିଥିଲୁ: ସଂଗ୍ରହଟି ମଧ୍ୟ 2021ରୁ ତାରିଖ ହୋଇଥିଲା। ଏହି ମଧ୍ୟରେ, Z-ଲାଇବ୍ରେରୀ ଏକ ଚମତ୍କାର ହାରରେ ବୃଦ୍ଧି ପାଇଛି: ସେମାନେ ପ୍ରାୟ 3.8 ଲକ୍ଷ ନୂତନ ପୁସ୍ତକ ଯୋଡ଼ିଛନ୍ତି। ସେଠାରେ କିଛି ଡୁପ୍ଲିକେଟ୍ ଅଛି, ନିଶ୍ଚିତ, କିନ୍ତୁ ଏହାର ଅଧିକାଂଶ ନୂତନ ପୁସ୍ତକ କିମ୍ବା ପୂର୍ବରୁ ଦାଖଲ ହୋଇଥିବା ପୁସ୍ତକର ଉଚ୍ଚ ଗୁଣବତ୍ତାର ସ୍କାନ୍ ମନେହୁଏ। ଏହାର ମୁଖ୍ୟ କାରଣ ହେଉଛି Z-ଲାଇବ୍ରେରୀରେ ସ୍ୱେଚ୍ଛାସେବୀ ମଡେରେଟରମାନଙ୍କର ବୃଦ୍ଧି ଏବଂ ସେମାନଙ୍କର ଡିଡ୍ୟୁପ୍ଲିକେସନ ସହିତ ବଲ୍କ ଅପଲୋଡ୍ ପ୍ରଣାଳୀ। ଆମେ ଖୁସି ହୋଇ ଘୋଷଣା କରୁଛୁଅ ଯେ ଆମେ Z-ଲାଇବ୍ରେରୀକୁ ଆମର ଶେଷ ମିରର୍ ଏବଂ ଅଗଷ୍ଟ 2022 ମଧ୍ୟରେ ଯୋଡ଼ାଯାଇଥିବା ସମସ୍ତ ପୁସ୍ତକ ପାଇଛୁଅ। ଆମେ ପ୍ରଥମେ ମିସ୍ କରିଥିବା କିଛି ପୁସ୍ତକକୁ ଫେରି ଆଣିଛୁଅ। ସମସ୍ତ ମିଶାଇ ଏହି ନୂତନ ସଂଗ୍ରହ ପ୍ରାୟ 24TB, ଯାହା ପୂର୍ବରୁ ଥିବା (7TB) ଠାରୁ ବହୁତ ବଡ଼। ଆମର ମିରର୍ ଏବେ ମୋଟ 31TB। ଆମେ ଆବାରେ ଲାଇବ୍ରେରୀ ଜେନେସିସ୍ ସହିତ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ କରିଛୁଅ, କାରଣ ସେହି ସଂଗ୍ରହ ପାଇଁ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଏହାରେ ଉପଲବ୍ଧ। ଦୟାକରି Pirate Library Mirrorକୁ ଯାଇ ନୂତନ ସଂଗ୍ରହକୁ ଯାଞ୍ଚ କରନ୍ତୁ (ସମ୍ପାଦନା: <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a>କୁ ସଂଚାଳିତ)। ସେଠାରେ ଫାଇଲଗୁଡ଼ିକ କିପରି ଗଠିତ ହୋଇଛି ଏବଂ ପୂର୍ବରୁ କ'ଣ ପରିବର୍ତ୍ତନ ହୋଇଛି ସେ ବିଷୟରେ ଅଧିକ ସୂଚନା ଅଛି। ଆମେ ଏଠାରୁ ଏହାକୁ ଲିଙ୍କ୍ କରିବାକୁ ଯାଉନାହିଁ, କାରଣ ଏହା କେବଳ ଏକ ବ୍ଲଗ୍ ୱେବସାଇଟ୍ ଯାହା କୌଣସି ଅବୈଧ ସାମଗ୍ରୀ ହୋଷ୍ଟ କରେନାହିଁ। ନିଶ୍ଚିତ ଭାବେ, ସିଡିଂ ଆମକୁ ସାହାଯ୍ୟ କରିବାର ଏକ ଉତ୍ତମ ଉପାୟ। ଆମର ପୂର୍ବରୁ ଥିବା ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ସିଡ୍ କରୁଥିବା ସମସ୍ତଙ୍କୁ ଧନ୍ୟବାଦ। ଆମେ ସକାରାତ୍ମକ ପ୍ରତିକ୍ରିୟା ପାଇ ଋଣୀ ଏବଂ ଖୁସି ଯେ ଏତେ ଲୋକ ଏହି ଅସାଧାରଣ ଉପାୟରେ ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିର ସଂରକ୍ଷଣକୁ ନେଇ ଚିନ୍ତା କରୁଛନ୍ତି। ପାଇରେଟ୍ ଲାଇବ୍ରେରୀ ମିରରରେ 3x ନୂତନ ପୁସ୍ତକ ଯୋଗ ହୋଇଛି (+24TB, 3.8 ମିଲିୟନ ପୁସ୍ତକ) TorrentFreak ଦ୍ୱାରା ସହଯୋଗୀ ଲେଖାଗୁଡ଼ିକ ପଢ଼ନ୍ତୁ: <a %(torrentfreak)s>ପ୍ରଥମ</a>, <a %(torrentfreak_2)s>ଦ୍ୱିତୀୟ</a> - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ଟୋରେଣ୍ଟଫ୍ରିକ୍ ଦ୍ୱାରା ସହଯୋଗୀ ଲେଖା: <a %(torrentfreak)s>ପ୍ରଥମ</a>, <a %(torrentfreak_2)s>ଦ୍ୱିତୀୟ</a> ଅତିତରେ, “ଛାୟା-ପୁସ୍ତକାଳୟଗୁଡ଼ିକ” ମରିଯାଉଥିଲା। ସାଇ-ହବ୍, ଏକ ବିଶାଳ ଅବୈଧ ଶିକ୍ଷାଗତ ପତ୍ରର ସଂଗ୍ରହ, ମାମଲାର କାରଣରୁ ନୂତନ କାର୍ଯ୍ୟଗୁଡ଼ିକ ଗ୍ରହଣ କରିବା ବନ୍ଦ କରିଥିଲା। “ଜେଡ୍-ଲାଇବ୍ରେରୀ”, ପୁସ୍ତକର ସବୁଠାରୁ ବଡ଼ ଅବୈଧ ପୁସ୍ତକାଳୟ, ତାହାର ଅଭିଯୋକ ନିର୍ମାତାମାନଙ୍କୁ ଅପରାଧ ମାମଲାରେ ଗିରଫ କରାଯାଇଥିଲା। ସେମାନେ ଅବିଶ୍ୱସନୀୟ ଭାବରେ ସେମାନଙ୍କର ଗିରଫରୁ ପଳାଇବାରେ ସଫଳ ହୋଇଥିଲେ, କିନ୍ତୁ ସେମାନଙ୍କର ପୁସ୍ତକାଳୟ କୌଣସି କମ୍ ବିପଦରେ ନାହିଁ। କିଛି ଦେଶ ଏହାର ଏକ ସଂସ୍କରଣ ଆଗରୁ କରୁଛନ୍ତି। TorrentFreak <a %(torrentfreak)s>ଖବର କରିଛି</a> ଯେ ଚୀନ ଏବଂ ଜାପାନ ତାଙ୍କର କପିରାଇଟ୍ ଆଇନରେ AI ଅପବାଦ ପରିଚୟ କରାଇଛନ୍ତି। ଆମ ପାଇଁ ଏହା ଅନ୍ତର୍ଜାତୀୟ ଚୁକ୍ତିଗୁଡ଼ିକ ସହିତ କିପରି କାମ କରେ ତାହା ସ୍ପଷ୍ଟ ନୁହେଁ, କିନ୍ତୁ ନିଶ୍ଚିତ ଭାବରେ ତାଙ୍କର ଦେଶୀୟ କମ୍ପାନୀଗୁଡ଼ିକୁ ଆବରଣ ଦେଇଥାଏ, ଯାହା ଆମେ ଯାହା ଦେଖୁଛୁ ତାହାକୁ ବ୍ୟାଖ୍ୟା କରେ। ଆନାର ଆର୍କାଇଭ୍ ସମ୍ବନ୍ଧରେ — ଆମେ ନୀତିଗତ ଦୃଢ଼ତାରେ ଭିତି ରଖି ଆମର ଅଣ୍ତର୍ଗତ କାର୍ଯ୍ୟ ଜାରି ରଖିବାକୁ ଚାହୁଁଛୁ। ତଥାପି ଆମର ସର୍ବୋତ୍ତମ ଇଚ୍ଛା ହେଉଛି ଆଲୋକରେ ପ୍ରବେଶ କରିବା, ଏବଂ ଆମର ପ୍ରଭାବକୁ ଆଇନଗତ ଭାବରେ ବୃଦ୍ଧି କରିବା। ଦୟାକରି କପିରାଇଟ୍ ସଂଶୋଧନ କରନ୍ତୁ। ଯେତେବେଳେ ଜେଡ୍-ଲାଇବ୍ରେରୀ ବନ୍ଦ ହେବାକୁ ସମ୍ମୁଖୀନ ହେଲା, ମୁଁ ତାହାର ସମଗ୍ର ପୁସ୍ତକାଳୟକୁ ପୃଷ୍ଠାପୋଷଣ କରିଥିଲି ଏବଂ ଏହାକୁ ରଖିବା ପାଇଁ ଏକ ପ୍ଲାଟଫର୍ମ ଖୋଜୁଥିଲି। ଏହା ମୋର ଅନ୍ନାର ଆର୍କାଇଭ୍ ଆରମ୍ଭ କରିବାର ପ୍ରେରଣା ଥିଲା: ସେହି ପୂର୍ବତନ ପ୍ରୟାସମାନଙ୍କ ପଛରେ ଥିବା ମିଶନର ଏକ ଅବ୍ୟାହତତା। ଆମେ ଏହିପର୍ଯ୍ୟନ୍ତ ପୃଥିବୀର ସବୁଠାରୁ ବଡ଼ ଛାୟା ପୁସ୍ତକାଳୟରେ ବିକଶିତ ହୋଇଛୁ, ଅନେକ ଫର୍ମାଟରେ 140 ମିଲିୟନରୁ ଅଧିକ କପିରାଇଟ ଥିବା ପାଠ୍ୟଗୁଡ଼ିକ ଆଶ୍ରୟ କରିଛି — ପୁସ୍ତକ, ଶିକ୍ଷାଗତ ପତ୍ର, ପତ୍ରିକା, ସମ୍ବାଦପତ୍ର, ଏବଂ ତାହାର ପରେ। ମୋ ଦଳ ଏବଂ ମୁଁ ଧ୍ରୁବବାଦୀ। ଆମେ ବିଶ୍ୱାସ କରୁଛୁ ଯେ ଏହି ଫାଇଲଗୁଡ଼ିକୁ ସଂରକ୍ଷଣ ଏବଂ ଆଶ୍ରୟ କରିବା ନୈତିକ ଭାବରେ ଠିକ୍। ପ୍ରାପ୍ତି ହେଉଛି ଯେ ପୁରା ପୃଥିବୀର ପୁସ୍ତକାଳୟଗୁଡ଼ିକର ଅର୍ଥାନୁଦାନ କମିଯାଉଛି, ଏବଂ ଆମେ ମାନବତାର ଐତିହ୍ୟକୁ କର୍ପୋରେସନମାନଙ୍କୁ ବିଶ୍ୱାସ କରିପାରିବା ନାହିଁ। ତାପରେ AI ଆସିଲା। ପ୍ରାୟତଃ ସମସ୍ତ ପ୍ରମୁଖ କମ୍ପାନୀ ଯାହା LLMଗୁଡ଼ିକ ନିର୍ମାଣ କରୁଛନ୍ତି ସେମାନେ ଆମ ସଂଗ୍ରହରେ ଶିକ୍ଷା ଦେବା ପାଇଁ ଆମ ସହିତ ଯୋଗାଯୋଗ କରିଥିଲେ। ଅଧିକାଂଶ (କିନ୍ତୁ ସମସ୍ତ ନୁହେଁ!) ଯୁକ୍ତରାଷ୍ଟ୍ର ଭିତ୍ତିକ କମ୍ପାନୀମାନେ ଆମର କାର୍ଯ୍ୟର ଅବୈଧ ସ୍ୱଭାବ ବୁଝିବା ପରେ ପୁନଃବିଚାର କରିଥିଲେ। ତାହାର ବିପରୀତ, ଚୀନୀ କମ୍ପାନୀମାନେ ଆମର ସଂଗ୍ରହକୁ ଉତ୍ସାହର ସହିତ ଗ୍ରହଣ କରିଛନ୍ତି, ଏହାର ଆଇନିତା ଦ୍ୱାରା ପ୍ରଭାବିତ ନ ହୋଇ। ଏହା ଉଲ୍ଲେଖନୀୟ ଯେ ଚୀନ ପ୍ରାୟ ସମସ୍ତ ପ୍ରମୁଖ ଆନ୍ତର୍ଜାତୀୟ କପିରାଇଟ ଚୁକ୍ତିର ସଂକେତକାରୀ ଭାବରେ ଭୂମିକା ନେଇଛି। ଆମେ ପ୍ରାୟ 30ଟି କମ୍ପାନୀକୁ ଉଚ୍ଚ-ଗତି ଅଭିଗମ ଦେଇଛୁ। ସେମାନଙ୍କ ମଧ୍ୟରୁ ଅଧିକାଂଶ ହେଉଛନ୍ତି LLM କମ୍ପାନୀ, ଏବଂ କିଛି ତଥ୍ୟ ବ୍ରୋକର, ଯେଉଁମାନେ ଆମର ସଂଗ୍ରହକୁ ପୁନଃବିକ୍ରୟ କରିବେ। ଅଧିକାଂଶ ଚୀନର, ଯଦିଓ ଆମେ ଯୁକ୍ତରାଷ୍ଟ୍ର, ଇଉରୋପ, ରୁଷିଆ, ଦକ୍ଷିଣ କୋରିଆ, ଏବଂ ଜାପାନର କମ୍ପାନୀମାନଙ୍କ ସହିତ କାମ କରିଛୁ। ଡିପସିକ୍ <a %(arxiv)s>ସ୍ୱୀକାର କରିଛି</a> ଯେ ଏକ ପୂର୍ବତନ ସଂସ୍କରଣ ଆମର ସଂଗ୍ରହର ଏକ ଅଂଶରେ ଶିକ୍ଷିତ ହୋଇଥିଲା, ଯଦିଓ ସେମାନେ ସେମାନଙ୍କର ସାଂପ୍ରତିକ ମଡେଲ ବିଷୟରେ ଅତ୍ୟଧିକ ଗୁପ୍ତ ଅଛନ୍ତି (ହୋଇପାରେ ଆମର ତଥ୍ୟରେ ମଧ୍ୟ ଶିକ୍ଷିତ ହୋଇଥାଏ)। ଯଦି ପଶ୍ଚିମ ଦେଶଗୁଡ଼ିକ LLMs ର ଦୌଡ଼ରେ ଆଗରେ ରହିବାକୁ ଚାହୁଁଛନ୍ତି, ଏବଂ ଶେଷରେ, AGI, ତେବେ ସେମାନେ ତାଙ୍କର କପିରାଇଟ୍ ସ୍ଥିତିକୁ ପୁନଃବିଚାର କରିବାକୁ ଆବଶ୍ୟକ, ଏବଂ ଶୀଘ୍ର। ଆପଣ ଆମ ସାଧାରଣ ମତରେ ସହମତ କରନ୍ତି କି ନାହିଁ, ଏହା ବର୍ତ୍ତମାନ ଏକ ଅର୍ଥନୀତିକ ମାମଲା ହେଉଛି, ଏବଂ ଏକ ଜାତୀୟ ସୁରକ୍ଷା ମାମଲା ମଧ୍ୟ ହେଉଛି। ସମସ୍ତ ଶକ୍ତି ଗଠନଗୁଡ଼ିକ କୃତ୍ରିମ ସୁପର-ବୈଜ୍ଞାନିକ, ସୁପର-ହ୍ୟାକର୍, ଏବଂ ସୁପର-ସେନା ତିଆରି କରୁଛନ୍ତି। ସୂଚନାର ସ୍ୱାଧୀନତା ଏହି ଦେଶଗୁଡ଼ିକ ପାଇଁ ଏକ ଅସ୍ତିତ୍ୱର ମାମଲା ହେଉଛି — ଏକ ଜାତୀୟ ସୁରକ୍ଷା ମାମଲା ମଧ୍ୟ ହେଉଛି। ଆମ ଦଳ ସମଗ୍ର ପୃଥିବୀରୁ ଆସିଛି, ଏବଂ ଆମର କୌଣସି ବିଶେଷ ସମ୍ମିଳନ ନାହିଁ। କିନ୍ତୁ ଆମେ ଶକ୍ତିଶାଳୀ କପିରାଇଟ୍ ଆଇନ ଥିବା ଦେଶଗୁଡ଼ିକୁ ଏହି ଅସ୍ତିତ୍ୱ ଜନିତ ଆପଦକୁ ବ୍ୟବସ୍ଥାପନ କରିବାକୁ ପ୍ରୋତ୍ସାହିତ କରିବାକୁ ଚାହୁଁଛୁ। ତାହାହେଲେ କ'ଣ କରିବା ଉଚିତ? ଆମର ପ୍ରଥମ ପରାମର୍ଶ ସରଳ: କପିରାଇଟ୍ ମିଆଦ କମାନ୍ତୁ। ଯୁକ୍ତରାଷ୍ଟ୍ରରେ, କପିରାଇଟ୍ ଲେଖକଙ୍କ ମୃତ୍ୟୁ ପରେ 70 ବର୍ଷ ପାଇଁ ଦିଆଯାଏ। ଏହା ଅସମ୍ଭବ। ଆମେ ଏହାକୁ ପେଟେଣ୍ଟ ସହିତ ସମନ୍ୱୟ କରିପାରିବା, ଯାହା ଦାଖଲ ପରେ 20 ବର୍ଷ ପାଇଁ ଦିଆଯାଏ। ଏହା ପୁସ୍ତକ, ପତ୍ର, ସଙ୍ଗୀତ, କଳା, ଏବଂ ଅନ୍ୟାନ୍ୟ ସୃଜନାତ୍ମକ କାର୍ଯ୍ୟର ଲେଖକଙ୍କୁ ତାଙ୍କର ପ୍ରୟାସ ପାଇଁ ପୂରାପୂରି ପ୍ରତିଫଳିତ ହେବା ପାଇଁ ପ୍ରଚୁର ସମୟ ହେବ (ଚଳଚ୍ଚିତ୍ର ଅନୁକୃତି ଭଳି ଦୀର୍ଘକାଳୀନ ପ୍ରକଳ୍ପଗୁଡ଼ିକ ସହିତ)। ତାପରେ, ଅନ୍ୟତମରେ, ନୀତି ନିର୍ଦ୍ଦେଶକମାନେ ପାଠ୍ୟଗୁଡ଼ିକର ଗଣ-ସଂରକ୍ଷଣ ଏବଂ ପ୍ରସାରଣ ପାଇଁ ଛାଡ଼ ଦେବା ଉଚିତ। ବ୍ୟକ୍ତିଗତ ଗ୍ରାହକମାନଙ୍କରୁ ହାରାଇଥିବା ଆୟ ମୁଖ୍ୟ ଚିନ୍ତା ହେଲେ, ବ୍ୟକ୍ତିଗତ ସ୍ତରର ବିତରଣ ନିଷିଦ୍ଧ ରହିପାରେ। ଏହାର ପରିବର୍ତ୍ତେ, ବିଶାଳ ଭଣ୍ଡାରଗୁଡ଼ିକୁ ପରିଚାଳନା କରିବାକୁ ସକ୍ଷମ — LLMs ଶିକ୍ଷା ଦେଉଥିବା କମ୍ପାନୀଗୁଡ଼ିକ, ପୁସ୍ତକାଳୟ ଏବଂ ଅନ୍ୟାନ୍ୟ ଆର୍କାଇଭ୍ ସହିତ — ଏହି ଅପବାଦଗୁଡ଼ିକ ଦ୍ୱାରା ଆବରଣ କରାଯିବ। ଜାତୀୟ ସୁରକ୍ଷା ପାଇଁ କପିରାଇଟ ସଂଶୋଧନ ଆବଶ୍ୟକ ସାରକଥା: ଚୀନୀ LLMଗୁଡ଼ିକ (ଡିପସିକ୍ ସହିତ) ଆମର ଅବୈଧ ପୁସ୍ତକ ଏବଂ ପତ୍ରଗୁଡ଼ିକର ସଂଗ୍ରହରେ ଶିକ୍ଷିତ ହୋଇଛି — ଯାହା ପୃଥିବୀର ସବୁଠାରୁ ବଡ଼। ପଶ୍ଚିମ ଦେଶଗୁଡ଼ିକର ଜାତୀୟ ସୁରକ୍ଷା ଭାବରେ କପିରାଇଟ ଆଇନକୁ ପୁନଃଗଠନ କରିବା ଆବଶ୍ୟକ। ଅଧିକ ସୂଚନା ପାଇଁ <a %(all_isbns)s>ମୂଳ ବ୍ଲଗ ପୋଷ୍ଟ</a> ଦେଖନ୍ତୁ। ଏହା ଉପରେ ଉନ୍ନତି କରିବାକୁ ଆମେ ଏକ ଆହ୍ୱାନ ଜାରି କରିଥିଲୁ। ଆମେ ପ୍ରଥମ ସ୍ଥାନର ପାଇଁ $6,000, ଦ୍ୱିତୀୟ ସ୍ଥାନର ପାଇଁ $3,000, ଏବଂ ତୃତୀୟ ସ୍ଥାନର ପାଇଁ $1,000 ପୁରସ୍କାର ଦେବାକୁ ଥିଲୁ। ଅପରିମିତ ପ୍ରତିକ୍ରିୟା ଏବଂ ଅବିଶ୍ୱସନୀୟ ଦାଖଲ ଦେଖି, ଆମେ ପୁରସ୍କାର ତହବିଲକୁ ଅଲ୍ପକିଛି ବୃଦ୍ଧି କରିବାକୁ ନିଷ୍ପତ୍ତି ନେଲୁ, ଏବଂ ଚାରି-ପଥର ତୃତୀୟ ସ୍ଥାନର ପାଇଁ ପ୍ରତ୍ୟେକ $500 ପୁରସ୍କାର ଦେବାକୁ ନିଷ୍ପତ୍ତି ନେଲୁ। ବିଜେତାମାନେ ନିମ୍ନରେ ଅଛନ୍ତି, କିନ୍ତୁ ସମସ୍ତ ଦାଖଲକୁ <a %(annas_archive)s>ଏଠାରେ</a> ଦେଖନ୍ତୁ, କିମ୍ବା ଆମର <a %(a_2025_01_isbn_visualization_files)s>ଯୁକ୍ତ ଟୋରେଣ୍ଟ</a> ଡାଉନଲୋଡ୍ କରନ୍ତୁ। ପ୍ରଥମ ସ୍ଥାନ $6,000: phiresky ଏହି <a %(phiresky_github)s>ଦାଖଲ</a> (<a %(annas_archive_note_2951)s>Gitlab ମନ୍ତବ୍ୟ</a>) ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ଆମେ ଯାହା ଚାହୁଁଥିଲୁ, ଏବଂ ଅଧିକ! ଆମେ ବିଶେଷତଃ ଅତ୍ୟନ୍ତ ଯାଚିତ ଭିଜୁଆଲାଇଜେସନ ବିକଳ୍ପଗୁଡ଼ିକ (ଏକାଂତ ଶେଡରକୁ ସମର୍ଥନ କରିବା ସହିତ) ପସନ୍ଦ କରିଲୁ, କିନ୍ତୁ ଏକ ସମ୍ପୂର୍ଣ୍ଣ ପ୍ରିସେଟ ତାଲିକା ସହିତ। ଆମେ ଏହାର ତ୍ୱରିତ ଏବଂ ସ୍ମୂଥ ହେବାକୁ, ସରଳ କାର୍ଯ୍ୟାନ୍ବୟନକୁ (ଯାହାର ପଛରେ କୌଣସି ବ୍ୟାକେଣ୍ଡ ନାହିଁ), ଚତୁର ମିନିମାପ୍, ଏବଂ ସେମାନଙ୍କର <a %(phiresky_github)s>ବ୍ଲଗ ପୋଷ୍ଟ</a>ରେ ବିସ୍ତୃତ ବ୍ୟାଖ୍ୟାକୁ ମଧ୍ୟ ପସନ୍ଦ କରିଲୁ। ଅବିଶ୍ୱସନୀୟ କାମ, ଏବଂ ଭଲ ଭାବରେ ଯୋଗ୍ୟ ବିଜେତା! - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ଆମର ହୃଦୟ ଋଣୀତାରେ ପରିପୂର୍ଣ୍ଣ। ଉଲ୍ଲେଖନୀୟ ଧାରଣା ଦୁର୍ଲଭତା ପାଇଁ ଗଗନଚୁମ୍ବୀ Datasets ତୁଳନା ପାଇଁ ବହୁତ ସ୍ଲାଇଡର୍, ଯେପରିକି ଆପଣ ଜେ ହେବେ। ପୁସ୍ତକଗୁଡ଼ିକର ସଂଖ୍ୟା ସହିତ ମାପ ଦଣ୍ଡ। ସୁନ୍ଦର ଲେବଲ୍‌ଗୁଡ଼ିକ। ଶୀତଳ ଡିଫଲ୍ଟ ରଙ୍ଗ ଯୋଜନା ଏବଂ ହିଟମ୍ୟାପ୍। ବିଶିଷ୍ଟ ମାନଚିତ୍ର ଭ୍ୟୁ ଏବଂ ଫିଲ୍ଟର୍ ଟୀକା, ଏବଂ ସଜୀବ ପରିସଂଖ୍ୟାନ ସଜୀବ ପରିସଂଖ୍ୟାନ କିଛି ଅଧିକ ଧାରଣା ଏବଂ କାର୍ଯ୍ୟାନ୍ବୟନ ଯାହାକୁ ଆମେ ବିଶେଷକରି ପସନ୍ଦ କରୁଥିଲୁ: ଆମେ କିଛି ସମୟ ପାଇଁ ଚାଲି ଯାଇପାରୁ, କିନ୍ତୁ ଆସନ୍ତୁ ଏଠାରେ ରୁକିଯାଉ। ସମସ୍ତ ଜମାଦାନକୁ <a %(annas_archive)s>ଏଠାରେ</a> ଦେଖନ୍ତୁ, କିମ୍ବା ଆମର <a %(a_2025_01_isbn_visualization_files)s>ଯୁକ୍ତ ଟୋରେଣ୍ଟ</a> ଡାଉନଲୋଡ୍ କରନ୍ତୁ। ଏତେ ଅଧିକ ଜମାଦାନ, ଏବଂ ପ୍ରତ୍ୟେକଟି ଏକ ଅନନ୍ୟ ଦୃଷ୍ଟିକୋଣ ଆଣିଥାଏ, ଯାହା UI କିମ୍ବା କାର୍ଯ୍ୟାନ୍ବୟନରେ ହେଉ। ଆମେ ଅନ୍ତତଃ ପ୍ରଥମ ସ୍ଥାନର ଜମାଦାନକୁ ଆମର ପ୍ରଧାନ ୱେବସାଇଟ୍ ରେ ଅନ୍ତର୍ଭୁକ୍ତ କରିବା, ଏବଂ ସମ୍ଭବତଃ କିଛି ଅନ୍ୟମାନଙ୍କୁ। ଆମେ ଏହି ପ୍ରକ୍ରିୟାକୁ କିପରି ସଂଗଠିତ କରିବା ନେଇ ଚିନ୍ତା କରିବା ଆରମ୍ଭ କରିଛୁ, ଦୁର୍ଲଭ ପୁସ୍ତକଗୁଡ଼ିକ ଚିହ୍ନଟ, ନିଶ୍ଚିତ, ଏବଂ ପରେ ସଂରକ୍ଷଣ କରିବା। ଏହି ମୋର୍ଚ୍ଚାରେ ଅଧିକ ଆସିବ। ଯେଉଁମାନେ ଅଂଶଗ୍ରହଣ କରିଥିଲେ ସେମାନଙ୍କୁ ଧନ୍ୟବାଦ। ଏତେ ଅଧିକ ଲୋକ ଯେ ଯତ୍ନ ନେଉଛନ୍ତି ଏହା ଅଦ୍ଭୁତ। ଦ୍ରୁତ ତୁଳନା ପାଇଁ ଡାଟାସେଟ୍‌ଗୁଡ଼ିକର ସହଜ ଟଗଲିଂ। ସମସ୍ତ ISBNs CADAL SSNOs CERLALC ତଥ୍ୟ ଲିକ୍ DuXiu SSIDs EBSCOhostର eBook ସୂଚୀ ଗୁଗୁଲ ବୁକ୍ସ ଗୁଡ୍ରିଡ୍ସ ଇଣ୍ଟରନେଟ ଆର୍କାଇଭ୍ ISBNdb ISBN ଗ୍ଲୋବାଲ ପ୍ରକାଶକ ରେଜିଷ୍ଟର ଲିବି ଆନାର ଆର୍କାଇଭ୍ ଭିତରେ ଥିବା ଫାଇଲଗୁଡ଼ିକ ନେକ୍ସସ୍/ଏସ୍‌ଟିସି OCLC/Worldcat ଓପେନଲାଇବ୍ରେରୀ ରୁଷିଆନ୍ ରାଜ୍ୟ ପୁସ୍ତକାଳୟ ଟ୍ରାଣ୍ଟରର ଇମ୍ପେରିଆଲ ଲାଇବ୍ରେରୀ ଦ୍ୱିତୀୟ ସ୍ଥାନ $3,000: hypha “ଯେତେବେଳେ ସଂପୂର୍ଣ୍ଣ ବର୍ଗ ଏବଂ ଆୟତକାର ଗଣିତ ଦୃଷ୍ଟିରୁ ଆକର୍ଷଣୀୟ, ସେମାନେ ଏକ ମାପିଂ ପରିପ୍ରେକ୍ଷ୍ୟରେ ଉତ୍କୃଷ୍ଟ ସ୍ଥାନୀୟତା ପ୍ରଦାନ କରନ୍ତି ନାହିଁ। ମୁଁ ବିଶ୍ୱାସ କରେ ଯେ ଏହି ହିଲବର୍ଟ କିମ୍ବା ପାରମ୍ପରିକ ମର୍ଟନ୍‌ରେ ଅନ୍ତର୍ନିହିତ ଅସମତା ଏକ ତ୍ରୁଟି ନୁହେଁ, ବରଂ ଏକ ବିଶେଷତା। ଇଟାଲୀର ପ୍ରସିଦ୍ଧ ଜୁତା ଆକୃତିର ଆଉଟଲାଇନ୍ ଯେପରି ଏକ ମାନଚିତ୍ରରେ ତୁରନ୍ତ ପରିଚିତ ହୋଇଯାଏ, ଏହି କର୍ଭଗୁଡ଼ିକର ବିଶିଷ୍ଟ "ବିଚିତ୍ରତା" ଜ୍ଞାନାତ୍ମକ ସ୍ମାରକ ଭାବେ କାମ କରିପାରେ। ଏହି ବିଶିଷ୍ଟତା ସ୍ପାଟିଆଲ୍ ସ୍ମୃତିକୁ ବୃଦ୍ଧି କରିପାରେ ଏବଂ ଉପଯୋଗକର୍ତ୍ତାମାନଙ୍କୁ ନିଜେମାନଙ୍କୁ ଅନୁକୂଳ କରିବାରେ ସାହାଯ୍ୟ କରିପାରେ, ଯାହା ନିର୍ଦ୍ଦିଷ୍ଟ ଅଞ୍ଚଳଗୁଡ଼ିକୁ ଅବସ୍ଥିତ କରିବା କିମ୍ବା ପ୍ରତିକୃତିଗୁଡ଼ିକୁ ଚିହ୍ନଟ କରିବାକୁ ସହଜ କରିପାରେ।” ଅନ୍ୟ ଏକ ଅଦ୍ଭୁତ <a %(annas_archive_note_2913)s>ଦାଖଲ</a>। ପ୍ରଥମ ସ୍ଥାନ ଭଳି ସୁବିନ୍ୟାସିତ ନୁହେଁ, କିନ୍ତୁ ଆମେ ଏହାର ମାକ୍ରୋ-ସ୍ତର ଭିଜୁଆଲାଇଜେସନ୍‌କୁ ପ୍ରଥମ ସ୍ଥାନ ଠାରୁ ଅଧିକ ପସନ୍ଦ କରୁଥିଲୁ (ସ୍ପେସ୍-ଫିଲିଂ କର୍ଭ୍, ସୀମା, ଲେବଲିଂ, ହାଇଲାଇଟିଂ, ପ୍ୟାନିଂ, ଏବଂ ଜୁମିଂ)। ଜୋ ଡେଭିସଙ୍କ ଦ୍ୱାରା ଏକ <a %(annas_archive_note_2971)s>ମନ୍ତବ୍ୟ</a> ଆମ ସହିତ ମିଳିଲା: ଏବଂ ତଥାପି ଭିଜୁଆଲାଇଜେସନ୍ ଏବଂ ରେଣ୍ଡରିଂ ପାଇଁ ବହୁତ ପସନ୍ଦ, ସହଜ ଏବଂ ସ୍ମୂଥ୍ UI। ଦ୍ୱିତୀୟ ସ୍ଥାନରେ ଏକ ମଜବୁତ ସ୍ଥାନ! - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>) କିଛି ମାସ ପୂର୍ବରୁ ଆମେ ଏକ <a %(all_isbns)s>$10,000 ପୁରସ୍କାର</a> ଘୋଷଣା କରିଥିଲୁ ଯାହା ଆମର ତଥ୍ୟର ସର୍ବୋତ୍ତମ ଭିଜୁଆଲାଇଜେସନ୍ କରିବାକୁ ଥିଲା ଯାହା ISBN ସ୍ଥାନକୁ ଦେଖାଏ। ଆମେ ଯେଉଁ ଫାଇଲଗୁଡ଼ିକ ଆମେ ଆର୍କାଇଭ୍ କରିନାହିଁ ଏବଂ ପରେ ଏକ ଡାଟାସେଟ୍ ଯାହା ବର୍ଣ୍ଣନା କରେ କିଏସବୁ ପୁସ୍ତକାଳୟ ISBN ଧାରଣ କରେ (ଏକ ଦୁର୍ଲଭତାର ମାପ)। ଆମେ ପ୍ରତିକ୍ରିୟାରେ ଭାବବିହ୍ବଳ ହୋଇଛୁ। ଏତେ ସୃଜନାଶୀଳତା ରହିଛି। ସମସ୍ତଙ୍କୁ ଏକ ବଡ଼ ଧନ୍ୟବାଦ ଯିଏ ଅଂଶଗ୍ରହଣ କରିଛନ୍ତି: ଆପଣଙ୍କର ଉର୍ଜା ଏବଂ ଉତ୍ସାହ ସଂକ୍ରାମକ! ଶେଷରେ ଆମେ ନିମ୍ନଲିଖିତ ପ୍ରଶ୍ନଗୁଡ଼ିକର ଉତ୍ତର ଦେବାକୁ ଚାହୁଁଥିଲୁ: <strong>ପୃଥିବୀରେ କେଉଁ ପୁସ୍ତକ ଅଛି, ଆମେ କେତେଟି ଆର୍କାଇଭ୍ କରିଛୁ, ଏବଂ ପରବର୍ତ୍ତୀରେ କେଉଁ ପୁସ୍ତକ ଉପରେ ଆମେ ଗୁରୁତ୍ୱ ଦେବା ଉଚିତ?</strong> ଏତେ ଲୋକ ଏହି ପ୍ରଶ୍ନଗୁଡ଼ିକରେ ଚିନ୍ତା କରୁଥିବା ଦେଖି ଭଲ ଲାଗୁଛି। ଆମେ ନିଜେ ଏକ ମୂଳ ଭିଜୁଆଲାଇଜେସନ୍ ସହିତ ଆରମ୍ଭ କରିଥିଲୁ। 300kb ରୁ କମ୍, ଏହି ଚିତ୍ର ମାନବତାର ଇତିହାସରେ ସବୁଠାରୁ ବଡ଼ ସଂପୂର୍ଣ୍ଣ ଖୋଲା "ପୁସ୍ତକର ତାଲିକା"କୁ ସଂକ୍ଷିପ୍ତ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରେ: ତୃତୀୟ ସ୍ଥାନ $500 #1: maxlion ଏହି <a %(annas_archive_note_2940)s>ଦାଖଲ</a>ରେ ଆମେ ବିଭିନ୍ନ ପ୍ରକାରର ଭ୍ୟୁଗୁଡ଼ିକୁ ଖୁବ ପସନ୍ଦ କରୁଥିଲୁ, ବିଶେଷକରି ତୁଳନା ଏବଂ ପ୍ରକାଶକ ଭ୍ୟୁଗୁଡ଼ିକୁ। ତୃତୀୟ ସ୍ଥାନ $500 #2: abetusk ଯଦିଓ ସବୁଠାରୁ ଅଧିକ ପ୍ରସ୍ତୁତ UI ନୁହେଁ, ଏହି <a %(annas_archive_note_2917)s>ଦାଖଲ</a> ବହୁତ ଗୋଟିଏ ବିକଳ୍ପକୁ ଚିହ୍ନଟ କରେ। ଆମେ ବିଶେଷକରି ଏହାର ତୁଳନା ବିଶେଷତାକୁ ପସନ୍ଦ କରୁଥିଲୁ। ତୃତୀୟ ସ୍ଥାନ $500 #3: conundrumer0 ପ୍ରଥମ ସ୍ଥାନ ଭଳି, ଏହି <a %(annas_archive_note_2975)s>ଦାଖଲ</a> ତାହାର ସୁବିନ୍ୟାସିତତା ସହିତ ଆମକୁ ଆଶ୍ଚର୍ଯ୍ୟ ଜନକ କରିଲା। ଶେଷରେ ଏହା ଏକ ମହାନ ଭିଜୁଆଲାଇଜେସନ୍ ଟୁଲ୍ ତିଆରି କରେ: ପାୱାର୍ ଉପଯୋଗକର୍ତ୍ତାମାନଙ୍କ ପାଇଁ ସର୍ବାଧିକ ସୁବିନ୍ୟାସିତତା, ସାଧାରଣ ଉପଯୋଗକର୍ତ୍ତାମାନଙ୍କ ପାଇଁ ସରଳତା ରଖିବା ସହିତ। ତୃତୀୟ ସ୍ଥାନ $500 #4: charelf ଅନ୍ତିମ <a %(annas_archive_note_2947)s>ଦାଖଲ</a> ଯାହାକି ଏକ ବାଉଣ୍ଟି ପାଇବାକୁ ମିଳିଲା, ସରଳ ହେଲେ ମଧ୍ୟ କିଛି ବିଶିଷ୍ଟ ବିଶେଷତା ରହିଛି ଯାହାକୁ ଆମେ ଖୁବ ପସନ୍ଦ କରୁଥିଲୁ। ଆମେ ଏହାକୁ ଖୁବ ପସନ୍ଦ କରୁଥିଲୁ କିପରି ସେମାନେ ଦେଖାଇଥିଲେ କିଏ ନିର୍ଦ୍ଦିଷ୍ଟ ISBN ଆବରଣ କରିଥିବା କିଛି ଡାଟାସେଟ୍ ଏକ ଲୋକପ୍ରିୟତା/ଭରସାର ମାପ ଭାବେ। ଆମେ ତୁଳନା ପାଇଁ ଏକ ଅପାସିଟି ସ୍ଲାଇଡର୍ ବ୍ୟବହାର କରିବାର ସରଳତା କିନ୍ତୁ ପ୍ରଭାବଶାଳୀତାକୁ ମଧ୍ୟ ଖୁବ ପସନ୍ଦ କରୁଥିଲୁ। $10,000 ISBN ଭିଜୁଆଲାଇଜେସନ୍ ପୁରସ୍କାରର ବିଜେତାମାନେ TL;DR: ଆମେ $10,000 ISBN ଭିଜୁଆଲାଇଜେସନ୍ ପୁରସ୍କାର ପାଇଁ କିଛି ଅଦ୍ଭୁତ ଜମା ଦେଖିଲୁ। ପୃଷ୍ଠଭୂମି ଆନାର ଆର୍କାଇଭ୍ କିପରି ସମସ୍ତ ମାନବତାର ଜ୍ଞାନକୁ ସଂରକ୍ଷଣ କରିବାର ଲକ୍ଷ୍ୟକୁ ସାଧନ କରିପାରିବ, ଯଦି ଆମେ ଜାଣିନାହିଁ କିଏଁସି ପୁସ୍ତକ ଅବଶିଷ୍ଟ ଅଛି? ଆମକୁ ଏକ TODO ତାଲିକା ଆବଶ୍ୟକ। ଏହାକୁ ମାପିବାର ଏକ ଉପାୟ ହେଉଛି ISBN ସଂଖ୍ୟା, ଯାହା 1970 ଦଶକରୁ ପ୍ରତ୍ୟେକ ପ୍ରକାଶିତ ପୁସ୍ତକକୁ ନିର୍ଦ୍ଦିଷ୍ଟ କରାଯାଇଛି (ଅଧିକାଂଶ ଦେଶରେ)। ସମସ୍ତ ISBN ନିଯୁକ୍ତିଗୁଡ଼ିକୁ ଜାଣିଥିବା କୌଣସି କେନ୍ଦ୍ରୀୟ କ୍ଷମତା ନାହିଁ। ତାହା ପରିବର୍ତ୍ତେ, ଏହା ଏକ ବିତରଣ ପ୍ରଣାଳୀ, ଯେଉଁଠାରେ ଦେଶଗୁଡ଼ିକ ସଂଖ୍ୟାର ରେଞ୍ଜ ପାଉଛନ୍ତି, ଯେଉଁଠାରେ ପ୍ରମୁଖ ପ୍ରକାଶକମାନଙ୍କୁ ଛୋଟ ରେଞ୍ଜ ନିଯୁକ୍ତ କରାଯାଏ, ଯେଉଁଠାରେ ସେମାନେ ଅପର ପ୍ରକାଶକମାନଙ୍କୁ ରେଞ୍ଜ ବିଭାଜନ କରିପାରନ୍ତି। ଶେଷରେ ବ୍ୟକ୍ତିଗତ ସଂଖ୍ୟାଗୁଡ଼ିକ ପୁସ୍ତକଗୁଡ଼ିକୁ ନିଯୁକ୍ତ କରାଯାଇଥାଏ। ଆମେ <a %(blog)s>ଦୁଇ ବର୍ଷ ଆଗରୁ</a> ISBNs ମାପିବା ଆରମ୍ଭ କରିଥିଲୁ ଆମର ISBNdb ର ସ୍କ୍ରାପ୍ ସହିତ। ସେଥିଠାରୁ, ଆମେ ଅନେକ ଅଧିକ metadata ଉତ୍ସଗୁଡ଼ିକୁ ସ୍କ୍ରାପ୍ କରିଛୁ, ଯେପରିକି <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, ଏବଂ ଅନ୍ୟାନ୍ୟ। ଏକ ସମ୍ପୂର୍ଣ୍ଣ ତାଲିକା "Datasets" ଏବଂ "Torrents" ପୃଷ୍ଠାରେ ଆନାର ଆର୍କାଇଭ୍‌ରେ ମିଳିବ। ଆମେ ବର୍ତ୍ତମାନ ପ୍ରଥମେ ସବୁଠାରୁ ବଡ଼ ସଂପୂର୍ଣ୍ଣ ଖୋଲା, ସହଜରେ ଡାଉନଲୋଡ଼ କରିପାରିବା ଯୋଗ୍ୟ ପୁସ୍ତକ metadata (ଏବଂ ସେହିପରି ISBNs) ସଂଗ୍ରହ ରଖିଛୁ। ଆମେ <a %(blog)s>ବିସ୍ତୃତ ଭାବରେ ଲେଖିଛୁ</a> କାହିଁକି ଆମେ ସଂରକ୍ଷଣକୁ ନେଇ ଚିନ୍ତିତ, ଏବଂ କାହିଁକି ଆମେ ବର୍ତ୍ତମାନ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ସମୟରେ ଅଛୁ। ଆମେ ବର୍ତ୍ତମାନ ଦୁର୍ଲଭ, ଅଧିକ ଧ୍ୟାନ ନ ଦେଇଥିବା, ଏବଂ ବିଶିଷ୍ଟ ଭାବରେ ଜୋଖିମ ଥିବା ପୁସ୍ତକଗୁଡ଼ିକୁ ଚିହ୍ନଟ କରିବା ଏବଂ ସଂରକ୍ଷଣ କରିବା ଆବଶ୍ୟକ। ସମସ୍ତ ପୁସ୍ତକର ଭଲ metadata ରଖିବା ଏଥିରେ ସାହାଯ୍ୟ କରେ। $10,000 ପୁରସ୍କାର ବ୍ୟବହାରକୁ ସହଜ କରିବା ଏବଂ ଏହା କିପରି ଦେଖାଯାଏ ତାହାକୁ ଭଲ କରିବାକୁ ଭାବିବା ଯାଇପାରେ। ବିଶେଷ ଭାବରେ ଜୁମ୍ କଲାବେଳେ ଅନ୍ୟତମ ISBN ଗୁଡ଼ିକର ନିଜସ୍ୱ ମେଟାଡାଟା ଦେଖାନ୍ତୁ, ଯେପରିକି ଶୀର୍ଷକ ଏବଂ ଲେଖକ। ଭଲ ସ୍ଥାନ-ପୂରଣ ବକ୍ରରେଖା। ଉଦାହରଣ ସ୍ୱରୂପ, ପ୍ରଥମ ଧାଡ଼ିରେ 0 ରୁ 4 ଯାଏଁ ଯାଇଥିବା ଜିଗ-ଜାଗ ଏବଂ ତା'ପରେ ଦ୍ୱିତୀୟ ଧାଡ଼ିରେ 5 ରୁ 9 ଯାଏଁ ପଛକୁ ଯାଇଥିବା — ପୁନରାବୃତ ଭାବରେ ପ୍ରୟୋଗ କରାଯାଇଛି। ଭିନ୍ନ କିମ୍ବା ଅନୁକୂଳନୀୟ ରଙ୍ଗ ଯୋଜନା। ଡାଟାସେଟଗୁଡ଼ିକୁ ତୁଳନା କରିବା ପାଇଁ ବିଶେଷ ଦୃଶ୍ୟ। ସମସ୍ୟାଗୁଡ଼ିକୁ ଡିବଗ୍ କରିବା ପାଇଁ ପଦ୍ଧତି, ଯେପରିକି ଅନ୍ୟ ମେଟାଡାଟା ଯାହା ଭଲ ଭାବରେ ସମ୍ମତି ଦେଉନାହିଁ (ଉଦାହରଣ ସ୍ୱରୂପ, ବହୁତ ଭିନ୍ନ ଶୀର୍ଷକ)। ISBN କିମ୍ବା ରେଞ୍ଜଗୁଡ଼ିକରେ ଟିପ୍ପଣୀ ସହିତ ଛବିଗୁଡ଼ିକୁ ଅନୁଲେଖନ କରିବା। ଦୁର୍ଲଭ କିମ୍ବା ବିପଦଗ୍ରସ୍ତ ପୁସ୍ତକଗୁଡ଼ିକ ଚିହ୍ନଟ କରିବା ପାଇଁ କୌଣସି ହ୍ୟୁରିଷ୍ଟିକ୍ସ। ଆପଣ ଯାହା କୃତ୍ରିମ ଧାରଣା ଆଣିପାରିବେ! କୋଡ୍ ଏହି ଛବିଗୁଡ଼ିକୁ ସୃଷ୍ଟି କରିବା ପାଇଁ କୋଡ୍, ଏବଂ ଅନ୍ୟ ଉଦାହରଣଗୁଡ଼ିକ <a %(annas_archive)s>ଏହି ଡାଇରେକ୍ଟୋରୀ</a>ରେ ମିଳିବ। ଆମେ ଏକ ସଂକୁଚିତ ତଥ୍ୟ ଆକୃତି ଆଣିଛୁ, ଯାହା ସହିତ ସମସ୍ତ ଆବଶ୍ୟକ ISBN ସୂଚନା ପ୍ରାୟ 75MB (ସଂକୁଚିତ) ଅଟେ। ତଥ୍ୟ ଆକୃତିର ବର୍ଣ୍ଣନା ଏବଂ ଏହାକୁ ସୃଷ୍ଟି କରିବା ପାଇଁ କୋଡ୍ <a %(annas_archive_l1244_1319)s>ଏଠାରେ</a> ମିଳିବ। ବାଉଣ୍ଟି ପାଇଁ ଆପଣଙ୍କୁ ଏହା ବ୍ୟବହାର କରିବାକୁ ଆବଶ୍ୟକ ନୁହେଁ, କିନ୍ତୁ ଏହା ସମ୍ଭବତଃ ଆରମ୍ଭ କରିବା ପାଇଁ ସବୁଠାରୁ ସୁବିଧାଜନକ ଆକୃତି। ଆପଣ ଆମର ମେଟାଡାଟାକୁ ଯେପରି ଚାହିଁବେ ସେପରି ପରିବର୍ତ୍ତନ କରିପାରିବେ (ଯଦି ଆପଣଙ୍କ ସମସ୍ତ କୋଡ୍ ଖୋଲା ଉତ୍ସ ହେବା ଆବଶ୍ୟକ)। ଆମେ ଆପଣଙ୍କ ଆଣିଥିବା ଜିନିଷଗୁଡ଼ିକୁ ଦେଖିବାକୁ ଅସହ୍ୟ ଅପେକ୍ଷା କରୁଛୁ। ଶୁଭେଚ୍ଛା! ଏହି ରେପୋକୁ ଫୋର୍କ କରନ୍ତୁ, ଏବଂ ଏହି ବ୍ଲଗ୍ ପୋଷ୍ଟ HTML ସମ୍ପାଦନ କରନ୍ତୁ (ଆମର Flask ବ୍ୟାକେଣ୍ଡ ବ୍ୟତୀତ ଅନ୍ୟ କୌଣସି ବ୍ୟାକେଣ୍ଡ ଅନୁମୋଦିତ ନୁହେଁ)। ଉପରେ ଥିବା ଛବିକୁ ସ୍ମୂଥ୍ ଭାବରେ ଝୁମାଇବା ଯୋଗ୍ୟ କରନ୍ତୁ, ଯାହାର ଫଳରେ ଆପଣ ବ୍ୟକ୍ତିଗତ ISBNs ପର୍ଯ୍ୟନ୍ତ ଝୁମାଇପାରିବେ। ISBNs ଉପରେ କ୍ଲିକ୍ କରିବା ଆପଣଙ୍କୁ ଏକ metadata ପୃଷ୍ଠା କିମ୍ବା ଆନାର ଆର୍କାଇଭ୍‌ରେ ଖୋଜକୁ ନେଇଯିବା ଉଚିତ। ଆପଣ ତଥାପି ସମସ୍ତ ଭିନ୍ନ Datasets ମଧ୍ୟରୁ ପରିବର୍ତ୍ତନ କରିପାରିବା ଉଚିତ। ଦେଶ ରେଞ୍ଜ ଏବଂ ପ୍ରକାଶକ ରେଞ୍ଜ ହୋଭର୍ କରିବାରେ ହାଇଲାଇଟ୍ ହେବା ଉଚିତ। ଆପଣ ଦେଶ ସୂଚନା ପାଇଁ ଉଦାହରଣ ସ୍ୱରୂପ <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> ବ୍ୟବହାର କରିପାରନ୍ତି, ଏବଂ ପ୍ରକାଶକମାନଙ୍କ ପାଇଁ ଆମର “isbngrp” ସ୍କ୍ରାପ୍ (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)। ଏହା ଡେସ୍କଟପ୍ ଏବଂ ମୋବାଇଲ୍‌ରେ ଭଲ ଭାବରେ କାମ କରିବା ଉଚିତ। ଏଠାରେ ଅନେକ କୁହାଯାଇବାକୁ ଅଛି, ତେଣୁ ଆମେ ଉପରେ ଦର୍ଶାଯାଇଥିବା ଭିଜୁଆଲାଇଜେସନ୍‌କୁ ଉନ୍ନତ କରିବା ପାଇଁ ଏକ ପୁରସ୍କାର ଘୋଷଣା କରୁଛୁ। ଆମର ଅଧିକାଂଶ ପୁରସ୍କାର ଭଳି ନୁହେଁ, ଏହା ସମୟ ସୀମାବଦ୍ଧ। ଆପଣଙ୍କୁ ଆପଣଙ୍କର ଖୋଲା ଉତ୍ସ କୋଡ୍ 2025-01-31 (23:59 UTC) ପର୍ଯ୍ୟନ୍ତ <a %(annas_archive)s>ଦାଖଲ କରିବାକୁ</a> ପଡ଼ିବ। ସର୍ବୋତ୍ତମ ଦାଖଲ $6,000 ପାଇବ, ଦ୍ୱିତୀୟ ସ୍ଥାନ $3,000 ଏବଂ ତୃତୀୟ ସ୍ଥାନ $1,000 ପାଇବ। ସମସ୍ତ ପୁରସ୍କାର Monero (XMR) ବ୍ୟବହାର କରି ପ୍ରଦାନ କରାଯିବ। ନିମ୍ନରେ ନ୍ୟୁନତମ ମାନଦଣ୍ଡ ଅଛି। ଯଦି କୌଣସି ଦାଖଲ ମାନଦଣ୍ଡ ପୂରଣ କରେନାହିଁ, ଆମେ ତଥାପି କିଛି ପୁରସ୍କାର ପ୍ରଦାନ କରିପାରିବା, କିନ୍ତୁ ସେହି ଆମର ବିବେଚନାରେ ହେବ। ବୋନସ ପଏଣ୍ଟ ପାଇଁ (ଏହାମାନେ କେବଳ ଧାରଣା — ଆପଣଙ୍କ ସୃଜନଶୀଳତାକୁ ମୁକ୍ତ ହୋଇ ଚାଲନ୍ତୁ): ଆପଣ ସମ୍ପୂର୍ଣ୍ଣ ଭିନ୍ନ ଭିଜୁଆଲାଇଜେସନ୍ କରିବାକୁ ନ୍ୟୁନତମ ମାନଦଣ୍ଡରୁ ସମ୍ପୂର୍ଣ୍ଣ ଭିନ୍ନ ହୋଇପାରନ୍ତି, ଯଦି ଏହା ନିଜେ ଅତ୍ୟନ୍ତ ଚମତ୍କାର ହୁଏ, ତେବେ ଏହା ବାଉଣ୍ଟି ପାଇଁ ଯୋଗ୍ୟ ହେବ, କିନ୍ତୁ ଆମର ବିବେଚନାରେ। ଆପଣଙ୍କ ଫୋର୍କ୍ ରେପୋ, ମର୍ଜ୍ ଅନୁରୋଧ, କିମ୍ବା ଡିଫ୍ ସହିତ <a %(annas_archive)s>ଏହି ସମସ୍ୟା</a>କୁ ଟିପ୍ପଣୀ ପୋଷ୍ଟ କରି ଦ୍ୱାରା ଦାଖଲ କରନ୍ତୁ। - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ଏହି ଛବିଟି 1000×800 ପିକ୍ସେଲ୍। ପ୍ରତ୍ୟେକ ପିକ୍ସେଲ୍ 2,500 ISBNs ପ୍ରତିନିଧିତ୍ୱ କରେ। ଯଦି ଆମେ କୌଣସି ISBN ପାଇଁ ଏକ ଫାଇଲ୍ ରଖିଛୁ, ଆମେ ସେହି ପିକ୍ସେଲ୍‌କୁ ଅଧିକ ସବୁଜ କରିଥାଉ। ଯଦି ଆମେ ଜାଣିଛୁ ଯେ ଏକ ISBN ଜାରି କରାଯାଇଛି, କିନ୍ତୁ ଆମେ ଏକ ମେଳାନ୍ତି ଫାଇଲ୍ ନାହିଁ, ଆମେ ଏହାକୁ ଅଧିକ ଲାଲ କରିଥାଉ। 300kb ରୁ କମ୍ ଆକାରରେ, ଏହି ଛବି ମାନବତାର ଇତିହାସରେ ସବୁଠାରୁ ବଡ଼ ସଂପୂର୍ଣ୍ଣ ଖୋଲା "ପୁସ୍ତକ ତାଲିକା"କୁ ସଂକ୍ଷିପ୍ତ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରେ (କିଛି ଶତାଧିକ GB ସଂକୋଚିତ ଭାବରେ)। ଏହା ଏହାକୁ ମଧ୍ୟ ଦର୍ଶାଏ: ପୁସ୍ତକଗୁଡ଼ିକୁ ସଂରକ୍ଷଣ କରିବାରେ ଅଧିକ କାମ ଅଛି (ଆମେ କେବଳ 16% ରଖିଛୁ)। ସମସ୍ତ ISBN ଗୁଡ଼ିକୁ ଭିଜୁଆଲାଇଜ୍ କରିବା — 2025-01-31 ରେ $10,000 ଇନାମ ଏହି ଚିତ୍ର ମାନବତାର ଇତିହାସରେ ସମସ୍ତୁଠାରୁ ବଡ଼ ସଂପୂର୍ଣ୍ଣ ଖୋଲା "ପୁସ୍ତକ ତାଲିକା" ପ୍ରତିନିଧିତ୍ୱ କରେ। ଦୃଶ୍ୟୀକରଣ ସାରାଂଶ ଛବି ବ୍ୟତୀତ, ଆମେ ଆମେ ଅଧିଗ୍ରହଣ କରିଥିବା ବ୍ୟକ୍ତିଗତ Datasets ମଧ୍ୟ ଦେଖିପାରିବା। ସେମାନଙ୍କ ମଧ୍ୟରୁ ପରିବର୍ତ୍ତନ କରିବା ପାଇଁ ଡ୍ରପଡାଉନ୍ ଏବଂ ବଟନ୍‌ଗୁଡ଼ିକୁ ବ୍ୟବହାର କରନ୍ତୁ। ଏହି ଛବିଗୁଡ଼ିକରେ ଦେଖିବାକୁ ଅନେକ ଆକର୍ଷଣୀୟ ଆକୃତିଗୁଡ଼ିକ ଅଛି। କାହିଁକି କିଛି ରେଗୁଲାରିଟି ଲାଇନ୍ ଏବଂ ବ୍ଲକ୍‌ର ଅଛି, ଯାହା ଭିନ୍ନ ମାପରେ ଘଟିବା ପାରେ? ଖାଲି ଅଞ୍ଚଳଗୁଡ଼ିକ କ'ଣ? କାହିଁକି କିଛି Datasets ଏତେ ଗୋଛାଗୋଛି ହୋଇଛି? ଆମେ ଏହି ପ୍ରଶ୍ନଗୁଡ଼ିକୁ ପାଠକଙ୍କ ପାଇଁ ଅଭ୍ୟାସ ଭାବରେ ରଖିବାକୁ ଛାଡ଼ିବା। - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ସମାପ୍ତି ଏହି ମାନକ ସହିତ, ଆମେ ମୁକ୍ତିଗୁଡ଼ିକୁ ଅଧିକ ଭାବରେ ଅନୁକ୍ରମିକ ଭାବରେ କରିପାରିବା, ଏବଂ ନୂତନ ଡାଟା ସ୍ରୋତଗୁଡ଼ିକୁ ସହଜରେ ଯୋଡ଼ିବାରେ ସକ୍ଷମ ହେବା। ଆମ ପାଖରେ ପାଇପଲାଇନ୍ ରେ କିଛି ରୋମାଞ୍ଚକ ମୁକ୍ତି ଅଛି! ଆମେ ଆଶା କରୁଛୁ ଯେ ଅନ୍ୟ ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ଆମ ସଂଗ୍ରହଗୁଡ଼ିକୁ ଆଦର୍ଶ କରିବା ସହଜ ହେବ। ଅବଶ୍ୟ, ଆମର ଲକ୍ଷ୍ୟ ହେଉଛି ମାନବ ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ସଦାର ରକ୍ଷା କରିବା, ତେଣୁ ଯେତେ ଅଧିକ ପୁନରାବୃତ୍ତି ତେତେ ଭଲ। ଉଦାହରଣ ଆସନ୍ତୁ ଆମର ସମ୍ପ୍ରତିକ Z-ଲାଇବ୍ରେରୀ ମୁକ୍ତିକୁ ଏକ ଉଦାହରଣ ଭାବେ ଦେଖିବା। ଏହା ଦୁଇଟି ସଂଗ୍ରହରୁ ଗଠିତ: “<span style="background: #fffaa3">zlib3_records</span>” ଏବଂ “<span style="background: #ffd6fe">zlib3_files</span>”। ଏହା ଆମକୁ ବହିର ଫାଇଲଗୁଡ଼ିକରୁ ଅସଲି ମେଟାଡାଟା ରେକର୍ଡଗୁଡ଼ିକୁ ଅଲଗା ଭାବରେ ସଂଗ୍ରହ କରିବା ଏବଂ ମୁକ୍ତ କରିବାକୁ ସମ୍ଭବ କରେ। ଏହିପରି, ଆମେ ମେଟାଡାଟା ଫାଇଲଗୁଡ଼ିକ ସହିତ ଦୁଇଟି ଟୋରେଣ୍ଟ ମୁକ୍ତ କରିଛୁ: ଆମେ ବାଇନାରୀ ଡାଟା ଫୋଲ୍ଡର ସହିତ ଅନେକ ଟୋରେଣ୍ଟ ମୁକ୍ତ କରିଛୁ, କିନ୍ତୁ କେବଳ “<span style="background: #ffd6fe">zlib3_files</span>” ସଂଗ୍ରହ ପାଇଁ, ମୋଟ 62ଟି: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> ଚାଲାଇବା ଦ୍ୱାରା ଆମେ ଭିତରେ କ'ଣ ଅଛି ଦେଖିପାରିବା: ଏହି ମାମଲାରେ, ଏହା Z-ଲାଇବ୍ରେରୀ ଦ୍ୱାରା ରିପୋର୍ଟ କରାଯାଇଥିବା ଏକ ବହିର ମେଟାଡାଟା। ଶୀର୍ଷ ସ୍ତରରେ ଆମେ କେବଳ “aacid” ଏବଂ “metadata” ରଖିଛୁ, କିନ୍ତୁ “data_folder” ନାହିଁ, କାରଣ ସମ୍ପର୍କିତ ବାଇନାରୀ ଡାଟା ନାହିଁ। AACID ମୁଖ୍ୟ ID ଭାବେ “22430000” ଧାରଣ କରେ, ଯାହା ଆମେ ଦେଖିପାରୁ ଯେ “zlibrary_id” ରୁ ଗ୍ରହଣ କରାଯାଇଛି। ଆମେ ଆଶା କରିପାରିବା ଯେ ଏହି ସଂଗ୍ରହର ଅନ୍ୟାନ୍ୟ AAC ଗୁଡ଼ିକରେ ସମାନ ଗଠନ ରହିବ। ଏବେ ଆସନ୍ତୁ <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> ଚାଲାଇବା: ଏହା ଏକ ଅପେକ୍ଷାକୃତ ଛୋଟ AAC ମେଟାଡାଟା, ଯଦିଓ ଏହି AAC ର ମୂଖ୍ୟ ଅଂଶ ଅନ୍ୟଏକ ବାଇନାରୀ ଫାଇଲ୍ ରେ ଅବସ୍ଥିତ! ଅବଶ୍ୟ, ଆମର ପାଖରେ ଏଥର "data_folder" ଅଛି, ତେଣୁ ଆମେ ଆଶା କରିପାରିବା ଯେ ସମ୍ବନ୍ଧିତ ବାଇନାରୀ ଡାଟା <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> ରେ ଅବସ୍ଥିତ ହୋଇପାରେ। "metadata" ରେ "zlibrary_id" ଅଛି, ତେଣୁ ଆମେ ଏହାକୁ "zlib_records" ସଂଗ୍ରହର ସମ୍ବନ୍ଧିତ AAC ସହିତ ସହଜରେ ସମ୍ବନ୍ଧିତ କରିପାରିବା। ଆମେ ଅନେକ ଭିନ୍ନ ଉପାୟରେ ସମ୍ବନ୍ଧିତ କରିପାରୁଥାନ୍ତା, ଯଥା AACID ଦ୍ୱାରା — ମାନକ ଏହାକୁ ନିର୍ଦ୍ଦେଶ କରେ ନାହିଁ। ଏହା ମଧ୍ୟ ଆବଶ୍ୟକ ନୁହେଁ ଯେ "metadata" କ୍ଷେତ୍ର ନିଜେ JSON ହେବା ଦରକାର। ଏହା XML କିମ୍ବା ଅନ୍ୟ କୌଣସି ଡାଟା ଫର୍ମାଟ୍ ଥିବା ଏକ ଷ୍ଟ୍ରିଙ୍ଗ ହୋଇପାରେ। ଆପଣ ଏକ ସମ୍ବନ୍ଧିତ ବାଇନାରୀ ବ୍ଲବ୍ ରେ ମେଟାଡାଟା ସୂଚନା ସଂଗ୍ରହ କରିପାରନ୍ତି, ଯଦି ଏହା ଅଧିକ ଡାଟା ହେଉଛି। ଅସମାନ ଫାଇଲ ଏବଂ ତଥ୍ୟ, ସମ୍ଭବ ଥିଲେ ମୂଳ ଫର୍ମାଟରେ ଅତ୍ୟନ୍ତ ନିକଟରେ। ବାଇନେରୀ ତଥ୍ୟଗୁଡ଼ିକୁ Nginx ଭଳି ୱେବସର୍ଭର ଦ୍ୱାରା ସିଧାସଳଖ ବିତରଣ କରାଯାଇପାରେ। ମୂଳ ପୁସ୍ତକାଳୟଗୁଡ଼ିକରେ ଅସମାନ ପରିଚୟକର୍ତ୍ତା, କିମ୍ବା ପରିଚୟକର୍ତ୍ତାର ଅଭାବ। ତଥ୍ୟ ତଥ୍ୟର ଅଲଗା ମୁକ୍ତି, କିମ୍ବା କେବଳ ତଥ୍ୟ ମାତ୍ର ମୁକ୍ତି (ଯଥା ଆମର ISBNdb ମୁକ୍ତି)। ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ବିତରଣ, ଯଦିଓ ଅନ୍ୟ ବିତରଣ ପ୍ରକ୍ରିୟାର ସମ୍ଭାବନା ରହିଛି (ଯଥା IPFS)। ଅପରିବର୍ତ୍ତନଶୀଳ ରେକର୍ଡଗୁଡ଼ିକ, କାରଣ ଆମେ ଧାରଣ କରିବା ଉଚିତ ଯେ ଆମର ଟୋରେଣ୍ଟଗୁଡ଼ିକ ସଦାକାଳ ପାଇଁ ରହିବ। ଅନୁକ୍ରମିକ ମୁକ୍ତି / ଯୋଗ୍ୟ ମୁକ୍ତି। ମେସିନ୍-ପଠନୀୟ ଏବଂ ଲେଖନୀୟ, ସୁବିଧାଜନକ ଏବଂ ଶୀଘ୍ର, ବିଶେଷକରି ଆମର ସ୍ଟାକ୍ ପାଇଁ (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)। କିଛି ସହଜ ମାନବ ନିରୀକ୍ଷଣ, ଯଦିଓ ଏହା ମେସିନ୍ ପଠନୀୟତା ପାଇଁ ଦ୍ୱିତୀୟ। ମାନକ ଭାଡା ନିଆଁ ସିଡବକ୍ସ ସହିତ ଆମର ସଂଗ୍ରହଗୁଡ଼ିକୁ ସିଡ କରିବା ସହଜ। ଡିଜାଇନ୍ ଲକ୍ଷ୍ୟଗୁଡ଼ିକ ଫାଇଲଗୁଡ଼ିକ ହାତରେ ଡିସ୍କରେ ସହଜରେ ନାଭିଗେଟ୍ କରିବାକୁ, କିମ୍ବା ପ୍ରିପ୍ରୋସେସିଂ ବିନା ଖୋଜିବାକୁ ସହଜ ହେବାକୁ ଆମେ ଚିନ୍ତା କରୁନାହିଁ। ଆମେ ଅବସ୍ଥାନରତ ଲାଇବ୍ରେରୀ ସଫ୍ଟୱେର ସହିତ ସିଧାସଳଖ ଉପଯୋଗୀ ହେବାକୁ ଚିନ୍ତା କରୁନାହିଁ। ଯେତେବେଳେ ଯେକୌଣସି ଲୋକଙ୍କ ପାଇଁ ଟୋରେଣ୍ଟ ବ୍ୟବହାର କରି ଆମର ସଂଗ୍ରହକୁ ସିଡ୍ କରିବା ସହଜ ହେବା ଉଚିତ, ଆମେ ଆଶା କରୁନାହିଁ ଯେ ଫାଇଲଗୁଡ଼ିକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ପ୍ରାଯୁକ୍ତିକ ଜ୍ଞାନ ଓ ପ୍ରତିବଦ୍ଧତା ବିନା ବ୍ୟବହାର କରିବା ଯୋଗ୍ୟ ହେବ। ଆମର ପ୍ରାଥମିକ ବ୍ୟବହାର ମାମଲା ହେଉଛି ଭିନ୍ନ ଥିବା ବର୍ତ୍ତମାନର ସଂଗ୍ରହଗୁଡ଼ିକରୁ ଫାଇଲ ଏବଂ ସମ୍ବନ୍ଧିତ ତଥ୍ୟଗୁଡ଼ିକର ବିତରଣ। ଆମର ସବୁଠାରୁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ବିଚାରଗୁଡ଼ିକ ହେଉଛି: କିଛି ଅଲକ୍ଷ୍ୟ: ଯେତେବେଳେ ଆନାର ଆର୍କାଇଭ୍ ଖୋଲା ଉତ୍ସ ଅଟେ, ଆମେ ଆମର ଫର୍ମାଟକୁ ସିଧାସଳଖ ଭାବରେ ପରୀକ୍ଷା କରିବାକୁ ଚାହୁଁଛୁ। ଯେତେବେଳେ ଆମେ ଆମର ସନ୍ଧାନ ସୂଚକ ତାଲିକାକୁ ତରୋତାଜା କରୁଛୁ, ଆମେ କେବଳ ସାର୍ବଜନିକ ଉପଲବ୍ଧ ପଥଗୁଡ଼ିକୁ ପ୍ରବେଶ କରୁଛୁ, ଯାହା ଦ୍ୱାରା ଯେକୌଣସି ଲୋକ ଯେଉଁମାନେ ଆମର ଲାଇବ୍ରେରୀକୁ ଫର୍କ କରନ୍ତି ସେମାନେ ଶୀଘ୍ର ଚାଲୁ କରିପାରିବେ। <strong>AAC.</strong> AAC (ଆନାର ଆର୍କାଇଭ୍ କଣ୍ଟେନର) ହେଉଛି ଏକ ଏକକ ଉପାଦାନ ଯାହାରେ <strong>ମେଟାଡାଟା</strong>, ଏବଂ ବୈକଳ୍ପିକ ଭାବରେ <strong>ବାଇନାରୀ ତଥ୍ୟ</strong> ଥାଏ, ଯାହା ଦୁଇଟି ଅପରିବର୍ତ୍ତନୀୟ। ଏହାର ଏକ ବିଶ୍ୱବ୍ୟାପୀ ଅନନ୍ୟ ପରିଚୟକାରୀ ଅଛି, ଯାହାକୁ <strong>AACID</strong> କୁହାଯାଏ। <strong>AACID।</strong> AACIDର ଆକୃତି ଏହିପରି: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>। ଉଦାହରଣ ସ୍ୱରୂପ, ଆମେ ମୁକ୍ତ କରିଥିବା ଏକ ପ୍ରକୃତ AACID ହେଉଛି <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>। <strong>AACID ରେଞ୍ଜ୍।</strong> ଯେହେତୁ AACIDs ମନୋଟୋନିକ୍ ଭାବରେ ବୃଦ୍ଧି ପାଉଥିବା ଟାଇମସ୍ଟାମ୍ପ ଧାରଣ କରେ, ଆମେ ଏହାକୁ ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ ସଂଗ୍ରହ ମଧ୍ୟରେ ରେଞ୍ଜ୍ ଦର୍ଶାଇବାକୁ ବ୍ୟବହାର କରିପାରିବା। ଆମେ ଏହି ଆକୃତି ବ୍ୟବହାର କରୁଛୁ: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, ଯେଉଁଠାରେ ଟାଇମସ୍ଟାମ୍ପଗୁଡ଼ିକ ଅନ୍ତର୍ଭୁକ୍ତ। ଏହା ISO 8601 ନୋଟେସନ ସହିତ ସମନ୍ୱୟ ରଖେ। ରେଞ୍ଜ୍ଗୁଡ଼ିକ ଅବିରତ ହୋଇଥାଏ, ଏବଂ ଓଭରଲାପ୍ ହୋଇପାରେ, କିନ୍ତୁ ଓଭରଲାପ୍ ହେଲେ ଏହାରେ ପୂର୍ବରୁ ମୁକ୍ତ ହୋଇଥିବା ସଂଗ୍ରହର ସମାନ ରେକର୍ଡସ୍ ଥାଇପାରେ (ଯେହେତୁ AACs ଅପରିବର୍ତ୍ତନଶୀଳ)। ନିଖୋଜ ରେକର୍ଡସ୍ ଅନୁମୋଦିତ ନୁହେଁ। <code>{collection}</code>: ସଂଗ୍ରହର ନାମ, ଯାହା ASCII ଅକ୍ଷର, ସଂଖ୍ୟା, ଏବଂ ଅଣ୍ଡରସ୍କୋର୍ସ୍ ଧାରଣ କରିପାରେ (କିନ୍ତୁ ଡବଲ୍ ଅଣ୍ଡରସ୍କୋର୍ସ୍ ନୁହେଁ)। <code>{collection-specific ID}</code>: ସଂଗ୍ରହ-ନିର୍ଦ୍ଦିଷ୍ଟ ପରିଚୟକର୍ତ୍ତା, ଯଦି ଲାଗୁ ହୁଏ, ଯଥା Z-Library ID। ଅପ୍ରୟୋଜନୀୟ କିମ୍ବା କ୍ଷୁଦ୍ରିତ ହୋଇପାରେ। ଯଦି AACID 150 ଅକ୍ଷର ଅତିକ୍ରମ କରିବାକୁ ଯାଏ, ତେବେ ଅପ୍ରୟୋଜନୀୟ କିମ୍ବା କ୍ଷୁଦ୍ରିତ ହୋଇପାରେ। <code>{ISO 8601 timestamp}</code>: ISO 8601ର ଏକ ସଂକ୍ଷିପ୍ତ ସଂସ୍କରଣ, ସଦା UTCରେ, ଯଥା <code>20220723T194746Z</code>। ଏହି ସଂଖ୍ୟା ପ୍ରତ୍ୟେକ ମୁକ୍ତି ପାଇଁ ମନୋଟୋନିକ୍ ଭାବରେ ବୃଦ୍ଧି ପାଇବା ଆବଶ୍ୟକ, ଯଦିଓ ଏହାର ନିଜସ୍ୱ ଅର୍ଥ ପ୍ରତ୍ୟେକ ସଂଗ୍ରହ ପାଇଁ ଭିନ୍ନ ହୋଇପାରେ। ଆମେ ସ୍କ୍ରାପିଂ କିମ୍ବା ID ଉତ୍ପାଦନର ସମୟ ବ୍ୟବହାର କରିବାକୁ ପ୍ରସ୍ତାବ କରୁଛୁ। <code>{shortuuid}</code>: ଏକ UUID କିନ୍ତୁ ASCIIକୁ ସଂକୁଚିତ, ଯଥା base57 ବ୍ୟବହାର କରି। ଆମେ ବର୍ତ୍ତମାନ <a %(github_skorokithakis_shortuuid)s>shortuuid</a> ପାଇଥନ୍ ଲାଇବ୍ରେରୀ ବ୍ୟବହାର କରୁଛୁ। <strong>ବାଇନାରୀ ଡାଟା ଫୋଲ୍ଡର।</strong> ଏକ ସଂଗ୍ରହ ପାଇଁ ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ AAC ରେଞ୍ଜର ବାଇନାରୀ ଡାଟା ସହିତ ଏକ ଫୋଲ୍ଡର। ଏହାର ନିମ୍ନଲିଖିତ ବୈଶିଷ୍ଟ୍ୟ ଅଛି: ଡାଇରେକ୍ଟରୀରେ ଉଲ୍ଲିଖିତ ରେଞ୍ଜ ମଧ୍ୟରେ ସମସ୍ତ AAC ପାଇଁ ଡାଟା ଫାଇଲ୍ ଥିବା ଆବଶ୍ୟକ। ପ୍ରତ୍ୟେକ ଡାଟା ଫାଇଲରେ ତାହାର AACID ଫାଇଲ୍ନାମ୍ ଭାବେ ଥିବା ଆବଶ୍ୟକ (କୌଣସି ଏକ୍ସଟେନସନ୍ ନାହିଁ)। ଡାଇରେକ୍ଟରୀ ନାମଟି ଏକ AACID ରେଞ୍ଜ ହେବା ଆବଶ୍ୟକ, ଯାହା <code style="color: green">annas_archive_data__</code> ସହିତ ପ୍ରିଫିକ୍ସ ହୋଇଥାଏ, ଏବଂ କୌଣସି ସଫିକ୍ସ ନାହିଁ। ଉଦାହରଣ ସ୍ୱରୂପ, ଆମର ଏକ ବାସ୍ତବିକ ମୁକ୍ତିରେ ଏକ ଡାଇରେକ୍ଟରୀ ଅଛି<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>। ଏହି ଫୋଲ୍ଡରଗୁଡ଼ିକୁ କିଛି ପରିମାଣରେ ପରିଚାଳନା ଯୋଗ୍ୟ କରିବା ପାଇଁ ସୁପାରିଶ କରାଯାଏ, ଯଥା ପ୍ରତ୍ୟେକ 100GB-1TB ରୁ ବଡ଼ ନ ହେବା ଦରକାର, ଯଦିଓ ଏହି ସୁପାରିଶ ସମୟ ସହିତ ପରିବର୍ତ୍ତିତ ହୋଇପାରେ। <strong>ସଂଗ୍ରହ.</strong> ପ୍ରତ୍ୟେକ AAC ଏକ ସଂଗ୍ରହର ଅଂଶ, ଯାହା ପରିଭାଷାନୁସାରେ ସେମାନ୍ତିକ ଭାବରେ ସମନ୍ୱିତ AAC ଗୁଡ଼ିକର ଏକ ତାଲିକା। ଏହାର ଅର୍ଥ ହେଉଛି ଯଦି ଆପଣ ମେଟାଡାଟାର ଫର୍ମାଟରେ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ପରିବର୍ତ୍ତନ କରନ୍ତି, ତେବେ ଆପଣଙ୍କୁ ଏକ ନୂତନ ସଂଗ୍ରହ ସୃଷ୍ଟି କରିବାକୁ ପଡ଼ିବ। ମାନକ <strong>ମେଟାଡାଟା ଫାଇଲ୍।</strong> ଏକ ମେଟାଡାଟା ଫାଇଲ୍ ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ ସଂଗ୍ରହ ପାଇଁ AACsର ଏକ ରେଞ୍ଜ୍ର ମେଟାଡାଟା ଧାରଣ କରେ। ଏହାର ନିମ୍ନଲିଖିତ ବୈଶିଷ୍ଟ୍ୟ ଅଛି: <code>data_folder</code> ঐଚ୍ଛିକ, ଏବଂ ଏହା ହେଉଛି ସମ୍ବନ୍ଧିତ ବାଇନାରୀ ଡାଟା ଥାକିଥିବା ବାଇନାରୀ ଡାଟା ଫୋଲ୍ଡରର ନାମ। ସେହି ଫୋଲ୍ଡରରେ ସମ୍ବନ୍ଧିତ ବାଇନାରୀ ଡାଟାର ଫାଇଲ୍ନାମ୍ ହେଉଛି ରେକର୍ଡର AACID। ପ୍ରତ୍ୟେକ JSON ଅବଜେକ୍ଟରେ ଶୀର୍ଷ ସ୍ତରରେ ନିମ୍ନଲିଖିତ କ୍ଷେତ୍ରଗୁଡ଼ିକ ଥିବା ଆବଶ୍ୟକ: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (ঐଚ୍ଛିକ)। ଅନ୍ୟ କୌଣସି କ୍ଷେତ୍ର ଅନୁମୋଦିତ ନୁହେଁ। ଫାଇଲନାମ୍ ଏକ AACID ରେଞ୍ଜ୍ ହେବା ଆବଶ୍ୟକ, ଯାହା <code style="color: red">annas_archive_meta__</code> ସହିତ ପ୍ରାରମ୍ଭ ହୋଇଥାଏ ଏବଂ <code>.jsonl.zstd</code> ସହିତ ଶେଷ ହୋଇଥାଏ। ଉଦାହରଣ ସ୍ୱରୂପ, ଆମର ଏକ ମୁକ୍ତିକୁ <br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> ବୋଲି କୁହାଯାଏ। ଫାଇଲ୍ ଏକ୍ସଟେନସନ୍ ଦ୍ୱାରା ଇଙ୍ଗିତ ହେଉଛି, ଫାଇଲ୍ ପ୍ରକାର <a %(jsonlines)s>JSON Lines</a> ଯାହା <a %(zstd)s>Zstandard</a> ସହିତ ସଂକୋଚିତ। <code>metadata</code> ହେଉଛି ସଂଗ୍ରହର ସେମାଣ୍ଟିକ୍ସ ଅନୁଯାୟୀ ଇଚ୍ଛାଧୀନ ମେଟାଡାଟା। ଏହା ସଂଗ୍ରହର ଭିତରେ ସେମାଣ୍ଟିକ୍ ଭାବେ ସମନ୍ୱିତ ହେବା ଆବଶ୍ୟକ। <code style="color: red">annas_archive_meta__</code> ପ୍ରିଫିକ୍ସକୁ ଆପଣଙ୍କ ସଂସ୍ଥାର ନାମକୁ ଅନୁକୂଳ କରାଯାଇପାରେ, ଯଥା <code style="color: red">my_institute_meta__</code>। <strong>“ରେକର୍ଡସ୍” ଏବଂ “ଫାଇଲ୍ସ” ସଂଗ୍ରହଗୁଡିକ।</strong> ପରମ୍ପରାନୁସାରେ, “ରେକର୍ଡସ୍” ଏବଂ “ଫାଇଲ୍ସ”କୁ ଭିନ୍ନ ସଂଗ୍ରହ ଭାବରେ ମୁକ୍ତ କରିବା ସୁବିଧାଜନକ ହୋଇପାରେ, ଯାହାକି ଭିନ୍ନ ସମୟସୂଚୀରେ ମୁକ୍ତ କରାଯାଇପାରେ, ଯଥା ସ୍କ୍ରାପିଂ ହାର ଆଧାରରେ। ଏକ “ରେକର୍ଡ” ହେଉଛି ମେଟାଡାଟା-ମାତ୍ର ସଂଗ୍ରହ, ଯାହାରେ ପୁସ୍ତକ ଶୀର୍ଷକ, ଲେଖକ, ISBN ଇତ୍ୟାଦି ସୂଚନା ଥାଏ, ଯେଉଁଠାରେ “ଫାଇଲ୍ସ” ହେଉଛି ସଂଗ୍ରହ ଯାହାରେ ପ୍ରକୃତ ଫାଇଲ୍ସ (pdf, epub) ଥାଏ। ଶେଷରେ, ଆମେ ଏକ ତୁଳନାମୂଳକ ସରଳ ମାନକ ଉପରେ ସମ୍ମତ ହେଲୁ। ଏହା ଅପେକ୍ଷାକୃତ ଢିଲା, ଅନନ୍ତିକ ଓ ଏକ ଚାଲୁଥିବା କାମ। <strong>ଟୋରେଣ୍ଟସ୍।</strong> ମେଟାଡାଟା ଫାଇଲ୍ ଏବଂ ବାଇନାରୀ ଡାଟା ଫୋଲ୍ଡରଗୁଡ଼ିକୁ ଟୋରେଣ୍ଟସ୍ ରେ ବାଣ୍ଟାଯାଇପାରେ, ପ୍ରତ୍ୟେକ ମେଟାଡାଟା ଫାଇଲ୍ ପାଇଁ ଗୋଟିଏ ଟୋରେଣ୍ଟ, କିମ୍ବା ପ୍ରତ୍ୟେକ ବାଇନାରୀ ଡାଟା ଫୋଲ୍ଡର ପାଇଁ ଗୋଟିଏ ଟୋରେଣ୍ଟ। ଟୋରେଣ୍ଟସ୍ ମୂଳ ଫାଇଲ୍/ଡାଇରେକ୍ଟରୀ ନାମ ସହିତ ଏକ <code>.torrent</code> ସଫିକ୍ସ ଥିବା ଫାଇଲ୍ନାମ୍ ଭାବେ ଥିବା ଆବଶ୍ୟକ। <a %(wikipedia_annas_archive)s>ଆନାଙ୍କ ଅଭିଲେଖ</a> ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଛାୟା ପୁସ୍ତକାଳୟ ହୋଇଯାଇଛି, ଏବଂ ଏହାର ମାପର ଏକମାତ୍ର ଛାୟା ପୁସ୍ତକାଳୟ ଯାହା ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଉତ୍ସ ଏବଂ ଖୋଲା ତଥ୍ୟ ଅଟେ। ତଳେ ଆମର ଡାଟାସେଟ୍ ପୃଷ୍ଠାରୁ ଏକ ତାଲିକା ଅଛି (ସାନ୍ତ୍ରସ୍ତ ଭାବରେ ସଂଶୋଧିତ): ଏହାକୁ ଆମେ ତିନିଟି ପ୍ରକାରରେ ସାଧିତାରେ ପହଞ୍ଚିଲୁ: ବର୍ତ୍ତମାନର ଖୋଲା-ଡାଟା ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକର ମିରରିଂ (ଯଥା Sci-Hub ଏବଂ Library Genesis)। ଯେଉଁ ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ଅଧିକ ଖୋଲା ହେବାକୁ ଚାହୁଁଛନ୍ତି, କିନ୍ତୁ ସେମାନଙ୍କ ପାଖରେ ସମୟ କିମ୍ବା ସାଧନ ନଥିଲା (ଯଥା Libgen କମିକ୍ସ ସଂଗ୍ରହ)। ଯେଉଁ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ଗୋଟା ଗୋଟିଏ ଭାବରେ ଅଂଶୀଦାର ହେବାକୁ ଚାହୁଁନାହାନ୍ତି ସେଗୁଡ଼ିକୁ ସ୍କ୍ରାପିଂ କରିବା (ଯଥା Z-Library)। (2) ଏବଂ (3) ପାଇଁ ଆମେ ବର୍ତ୍ତମାନ ନିଜେ ଏକ ବ୍ୟାପକ ସଂଗ୍ରହର ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ପରିଚାଳନା କରୁଛୁ (100s ଟିବିଗୁଡ଼ିକ)। ଏପର୍ଯ୍ୟନ୍ତ ଆମେ ଏହି ସଂଗ୍ରହଗୁଡ଼ିକୁ ଏକ ଏକ ଭାବରେ ଆଗକୁ ଆଣିଛୁ, ଯାହାର ଅର୍ଥ ହେଉଛି ପ୍ରତ୍ୟେକ ସଂଗ୍ରହ ପାଇଁ ବିଶେଷ ଅବସ୍ଥାପନା ଏବଂ ତଥ୍ୟ ସଂଗଠନ। ଏହା ପ୍ରତ୍ୟେକ ମୁକ୍ତିକୁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅତିରିକ୍ତ ଭାର ଯୋଗାଏ, ଏବଂ ଏହାକୁ ଅଧିକ ଅନୁକ୍ରମିକ ମୁକ୍ତିକୁ କରିବାକୁ ବିଶେଷ ଭାବରେ କଠିନ କରେ। ଏହି କାରଣରୁ ଆମେ ଆମର ମୁକ୍ତିଗୁଡ଼ିକୁ ମାନକରେ ଆଣିବାକୁ ନିଷ୍ପତ୍ତି ନେଲୁ। ଏହା ଏକ ପ୍ରାଯୁକ୍ତିକ ବ୍ଲଗ ପୋଷ୍ଟ ଯେଉଁଥିରେ ଆମେ ଆମର ମାନକୁ ପରିଚୟ କରାଇବାକୁ ଯାଉଛୁ: <strong>ଆନାର ଆର୍କାଇଭ୍ କଣ୍ଟେନର୍ସ</strong>। ଆନାଙ୍କ ଅଭିଲେଖ ପାତ୍ର (AAC): ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଛାୟା ପୁସ୍ତକାଳୟରୁ ମୁକ୍ତିଗୁଡ଼ିକୁ ମାନକରଣ କରିବା ଆନାଙ୍କ ଅଭିଲେଖ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଛାୟା ପୁସ୍ତକାଳୟ ହୋଇଯାଇଛି, ଯାହା ଆମକୁ ଆମର ମୁକ୍ତିଗୁଡ଼ିକୁ ମାନକରଣ କରିବାକୁ ଆବଶ୍ୟକ କରେ। 300GB+ ବହି ମୁଖପୃଷ୍ଠା ମୁକ୍ତ କରାଯାଇଛି ଶେଷରେ, ଆମେ ଏକ ଛୋଟ ମୁକ୍ତି ଘୋଷଣା କରିବାରେ ଖୁସି ଅନୁଭବ କରୁଛୁ। Libgen.rs ଫର୍କ ଚାଳନା କରୁଥିବା ଲୋକମାନଙ୍କ ସହିତ ସହଯୋଗରେ, ଆମେ ସମସ୍ତ ପୁସ୍ତକ ଆବରଣକୁ ଟୋରେଣ୍ଟ ଏବଂ IPFS ମାଧ୍ୟମରେ ଅଂଶୀଦାର କରୁଛୁ। ଏହା ଆବରଣଗୁଡ଼ିକୁ ଦେଖିବାର ଭାରକୁ ଅଧିକ ଯନ୍ତ୍ର ମଧ୍ୟରେ ବଣ୍ଟନ କରିବ, ଏବଂ ସେଗୁଡ଼ିକୁ ଭଲ ଭାବରେ ସଂରକ୍ଷଣ କରିବ। ଅନେକ (କିନ୍ତୁ ସମସ୍ତ ନୁହେଁ) ଘଟଣାରେ, ପୁସ୍ତକ ଆବରଣଗୁଡ଼ିକ ନିଜେ ଫାଇଲଗୁଡ଼ିକରେ ଅନ୍ତର୍ଭୁକ୍ତ ଅଛି, ତେଣୁ ଏହା ଏକ ପ୍ରକାରର "ଉତ୍ପନ୍ନ ତଥ୍ୟ"। କିନ୍ତୁ ଏହାକୁ IPFS ରେ ରଖିବା ଦୈନିକ କାର୍ଯ୍ୟକଳାପ ପାଇଁ ଏବଂ ଆନାର ଅଭିଲେଖ ଏବଂ ବିଭିନ୍ନ Library Genesis ଫର୍କ ପାଇଁ ଏବେ ମଧ୍ୟ ଖୁବ ଉପଯୋଗୀ। ସାଧାରଣତଃ, ଆପଣ ଏହି ମୁକ୍ତିକୁ Pirate Library Mirror ରେ ପାଇପାରିବେ (ସମ୍ପାଦନା: <a %(wikipedia_annas_archive)s>ଆନାର ଅଭିଲେଖ</a> କୁ ସଂଚାଳିତ ହୋଇଛି)। ଆମେ ଏଠାରେ ଏହାକୁ ଲିଙ୍କ କରିବାକୁ ଚାହୁଁନାହିଁ, କିନ୍ତୁ ଆପଣ ସହଜରେ ଏହାକୁ ପାଇପାରିବେ। ଆଶା କରୁଛୁ ଆମେ ଆମର ଗତିକୁ ଅଳ୍ପ ଶିଥିଳ କରିପାରିବା, ବର୍ତ୍ତମାନ ଯେ ଆମର ଜେ-ଲାଇବ୍ରେରୀ ପାଇଁ ଏକ ଭଲ ବିକଳ୍ପ ଅଛି। ଏହି କାର୍ଯ୍ୟଭାର ବିଶେଷ ଭାବରେ ଟିକିଏ ଟିକିଏ ରହିବା ଯୋଗ୍ୟ ନୁହେଁ। ଯଦି ଆପଣ ପ୍ରୋଗ୍ରାମିଂ, ସର୍ଭର କାର୍ଯ୍ୟକଳାପ, କିମ୍ବା ସଂରକ୍ଷଣ କାମରେ ସାହାଯ୍ୟ କରିବାରେ ଆଗ୍ରହୀ, ନିଶ୍ଚିତ ଭାବେ ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଏବେ ମଧ୍ୟ ଅଧିକ <a %(annas_archive)s>କାମ କରିବାକୁ ଅଛି</a>। ଆପଣଙ୍କର ଆଗ୍ରହ ଏବଂ ସମର୍ଥନ ପାଇଁ ଧନ୍ୟବାଦ। ElasticSearch କୁ ପରିବର୍ତ୍ତନ କରନ୍ତୁ କିଛି ପ୍ରଶ୍ନ ଅତ୍ୟଧିକ ସମୟ ନେଉଥିଲା, ଯେପର୍ଯ୍ୟନ୍ତ ସେଗୁଡ଼ିକ ସମସ୍ତ ଖୋଲା ସଂଯୋଗକୁ ଦାବି କରୁଥିଲା। ଡିଫଲ୍ଟ ଭାବରେ MySQL ରେ ଏକ ନ୍ୟୁନତମ ଶବ୍ଦ ଲମ୍ବ ଅଛି, କିମ୍ବା ଆପଣଙ୍କର ସୂଚକ ଖୁବ ବଡ଼ ହୋଇପାରେ। ଲୋକମାନେ "Ben Hur" ପାଇଁ ସନ୍ଧାନ କରିପାରୁନାହାନ୍ତି ବୋଲି ରିପୋର୍ଟ କରିଥିଲେ। ସନ୍ଧାନ କେବଳ ଅଳ୍ପ ସମୟରେ ତ୍ୱରିତ ଥିଲା ଯେତେବେଳେ ସମ୍ପୂର୍ଣ୍ଣ ମେମୋରିରେ ଲୋଡ୍ ହୋଇଥିଲା, ଯାହା ଆମକୁ ଏହାକୁ ଚାଲାଇବା ପାଇଁ ଅଧିକ ମୂଲ୍ୟବାନ ଯନ୍ତ୍ର ପାଇବାକୁ ବାଧ୍ୟ କରିଥିଲା, ପ୍ରାରମ୍ଭରେ ସୂଚକକୁ ପ୍ରିଲୋଡ୍ କରିବାକୁ କିଛି ଆଦେଶ ସହିତ। ଆମେ ଏହାକୁ ସହଜରେ ବିକାଶ କରି ନୂତନ ବୈଶିଷ୍ଟ୍ୟଗୁଡ଼ିକ ନିର୍ମାଣ କରିପାରିବା ନଥିଲୁ, ଯେପରିକି ଅଧିକ ଭାଲ %(wikipedia_cjk_characters)sଅବିରାମ ଭାଷାଗୁଡ଼ିକ ପାଇଁ ଟୋକେନାଇଜେସନ</a>, ଫିଲ୍ଟରିଂ/ଫାସେଟିଂ, ସର୍ଟିଂ, "ଆପଣଙ୍କ ଅର୍ଥ ହେଉଛି" ପ୍ରସ୍ତାବନା, ଅଟୋକମ୍ପ୍ଲିଟ୍ ଇତ୍ୟାଦି। ଆମର <a %(annas_archive)s>ଟିକେଟ୍‌ଗୁଡ଼ିକ</a>ର ମଧ୍ୟରୁ ଗୋଟିଏ ଥିଲା ଆମର ସନ୍ଧାନ ପ୍ରଣାଳୀ ସହିତ ସମସ୍ତ ସମସ୍ୟାର ଏକ ଗୋଛା। ଆମେ MySQL ପୂର୍ଣ୍ଣ-ପାଠ୍ୟ ସନ୍ଧାନ ବ୍ୟବହାର କରୁଥିଲୁ, କାରଣ ଆମର ସମସ୍ତ ତଥ୍ୟ MySQL ରେ ଥିଲା। କିନ୍ତୁ ଏହାର ସୀମା ଥିଲା: ଅନେକ ବିଶେଷଜ୍ଞଙ୍କ ସହିତ କଥା ହେବା ପରେ, ଆମେ ElasticSearch ଉପରେ ନିର୍ଣ୍ଣୟ ନେଲୁ। ଏହା ସଂପୂର୍ଣ୍ଣ ନିର୍ଦ୍ଦୋଷ ହୋଇନାହିଁ (ତାଙ୍କର ଡିଫଲ୍ଟ “ଆପଣ କହିବାକୁ ଚାହୁଁଛନ୍ତି” ପ୍ରସ୍ତାବନା ଏବଂ ଅଟୋକମ୍ପ୍ଲିଟ୍ ବିଶେଷତା ଖରାପ), କିନ୍ତୁ ସମଗ୍ର ଭାବରେ ଏହା ଖୋଜ ପାଇଁ MySQL ଠାରୁ ଅଧିକ ଭଲ ହୋଇଛି। ଆମେ ଏହାକୁ କୌଣସି ମିଶନ୍-କ୍ରିଟିକାଲ୍ ଡାଟା ପାଇଁ ବ୍ୟବହାର କରିବାକୁ <a %(youtube)s>ଅଧିକ ଆଗ୍ରହୀ ନୁହଁ</a> (ଯଦିଓ ସେମାନେ ବହୁତ <a %(elastic_co)s>ଅଗ୍ରଗତି</a> କରିଛନ୍ତି), କିନ୍ତୁ ସମଗ୍ର ଭାବରେ ଆମେ ପରିବର୍ତ୍ତନରେ ଖୁସି। ବର୍ତ୍ତମାନ ପାଇଁ, ଆମେ ଅଧିକ ତ୍ୱରିତ ଖୋଜ, ଭାଷା ସମର୍ଥନରେ ଉନ୍ନତି, ଉପଯୁକ୍ତତା ଅନୁସାରେ ଶ୍ରେଣୀବଦ୍ଧ କରିବା, ଭିନ୍ନ ଶ୍ରେଣୀବଦ୍ଧ ବିକଳ୍ପ ଏବଂ ଭାଷା/ବହି ପ୍ରକାର/ଫାଇଲ ପ୍ରକାରରେ ଫିଲ୍ଟରିଂ କାର୍ଯ୍ୟକାରୀ କରିଛୁ। ଏହା କିପରି କାମ କରେ ଆପଣ ଉତ୍ସୁକ ହେଲେ, <a %(annas_archive_l140)s>ଦେଖନ୍ତୁ</a> <a %(annas_archive_l1115)s>ଏକ</a> <a %(annas_archive_l1635)s>ଦୃଷ୍ଟି</a> ଦିଅନ୍ତୁ। ଏହା ମଧ୍ୟମ ଭାବରେ ପ୍ରବେଶଯୋଗ୍ୟ, ଯଦିଓ ଏହାକୁ ଅଧିକ ଟିପ୍ପଣୀ ଆବଶ୍ୟକ… ଆନାର ଅଭିଲେଖ ସଂପୂର୍ଣ୍ଣ ଖୋଲା ଉତ୍ସ ଆମେ ବିଶ୍ୱାସ କରୁଛୁ ଯେ ସୂଚନା ମୁକ୍ତ ହେବା ଉଚିତ, ଏବଂ ଆମର ନିଜସ୍ୱ କୋଡ୍ ଏଥିରୁ ବାଦ ନୁହେଁ। ଆମେ ଆମର ସମସ୍ତ କୋଡ୍ ଆମର ନିଜସ୍ୱ ହୋଷ୍ଟ କରାଯାଇଥିବା Gitlab ଇନ୍ସଟାନ୍ସରେ ମୁକ୍ତ କରିଛୁ: <a %(annas_archive)s>ଆନାର ସଫ୍ଟୱେର</a>। ଆମେ ଆମର କାମକୁ ସଂଗଠିତ କରିବା ପାଇଁ ଇସ୍ୟୁ ଟ୍ରାକର୍ ବ୍ୟବହାର କରୁଛୁ। ଯଦି ଆପଣ ଆମର ବିକାଶ ସହିତ ଜୁଡ଼ିବାକୁ ଚାହାଁନ୍ତି, ଏହା ଆରମ୍ଭ କରିବା ପାଇଁ ଏକ ଭଲ ସ୍ଥାନ। ଆମେ କାମ କରୁଥିବା କିଛି ଜିନିଷର ଏକ ରୁଚି ଦେବା ପାଇଁ, ଆମର କ୍ଲାଇଏଣ୍ଟ-ସାଇଡ୍ ପରିଣାମ ଉନ୍ନତିରେ ଆମର ସମ୍ପ୍ରତି କାମ ନିଅନ୍ତୁ। ଯେହେତୁ ଆମେ ଏପର୍ଯ୍ୟନ୍ତ ପୃଷ୍ଠାକ୍ରମଣକୁ କାର୍ଯ୍ୟକାରୀ କରିନାହିଁ, ଆମେ ପ୍ରାୟ 100-200 ପରିଣାମ ସହିତ ଖୁବ ଲମ୍ବା ସନ୍ଧାନ ପୃଷ୍ଠା ଫେରାଇଥାନ୍ତୁ। ଆମେ ସନ୍ଧାନ ପରିଣାମକୁ ଖୁବ ଶୀଘ୍ର କାଟି ଦେବାକୁ ଚାହୁଁନଥିଲୁ, କିନ୍ତୁ ଏହା କିଛି ଉପକରଣକୁ ମନ୍ଦ କରିଦେବାର ଅର୍ଥ ହେଉଥିଲା। ଏହା ପାଇଁ, ଆମେ ଏକ ଛୋଟ ଚାଳ ଅନୁଷ୍ଠାନ କରିଥିଲୁ: ଆମେ ଅଧିକାଂଶ ସନ୍ଧାନ ପରିଣାମକୁ HTML ମନ୍ତବ୍ୟରେ ମୁଡିଥିଲୁ (<code><!-- --></code>), ଏବଂ ତାପରେ ଏକ ଛୋଟ Javascript ଲେଖିଥିଲୁ ଯାହା ଏକ ପରିଣାମ ଦୃଶ୍ୟମାନ ହେବା ଉଚିତ ବୋଲି ଚିହ୍ନଟ କରିଥାଏ, ଯେତେବେଳେ ଆମେ ମନ୍ତବ୍ୟକୁ ଖୋଲିଦେବାକୁ ଚାହୁଁଥିଲୁ: DOM "ଭର୍ଚୁଆଲାଇଜେସନ" 23 ଲାଇନରେ କାର୍ଯ୍ୟକାରୀ ହୋଇଛି, ଫ୍ୟାନ୍ସି ଲାଇବ୍ରେରୀର ଆବଶ୍ୟକତା ନାହିଁ! ଏହା ଏକ ପ୍ରକାରର ଶୀଘ୍ର ବ୍ୟବହାରିକ କୋଡ୍ ଯାହା ଆପଣ ଅଳ୍ପ ସମୟ ରହିଲେ ଏବଂ ବାସ୍ତବ ସମସ୍ୟାଗୁଡ଼ିକୁ ସମାଧାନ କରିବାକୁ ହୋଇଥାଏ। ଏହା ରିପୋର୍ଟ ହୋଇଛି ଯେ ଆମର ସନ୍ଧାନ ବର୍ତ୍ତମାନ ମନ୍ଦ ଉପକରଣରେ ଭଲ କାମ କରୁଛି! ଅନ୍ୟ ଏକ ବଡ଼ ପ୍ରୟାସ ଥିଲା ଡାଟାବେସ ନିର୍ମାଣକୁ ସ୍ୱୟଂଚାଳିତ କରିବା। ଯେତେବେଳେ ଆମେ ଆରମ୍ଭ କରିଲୁ, ଆମେ ବିଭିନ୍ନ ଉତ୍ସଗୁଡ଼ିକୁ ଅବ୍ୟବସ୍ଥିତ ଭାବରେ ଏକତ୍ର କରିଲୁ। ବର୍ତ୍ତମାନ ଆମେ ସେଗୁଡ଼ିକୁ ଅଦ୍ୟତନ ରଖିବାକୁ ଚାହୁଁଛୁ, ତେଣୁ ଆମେ ଦୁଇଟି Library Genesis ଫର୍କରୁ ନୂତନ metadata ଡାଉନଲୋଡ୍ କରିବା ପାଇଁ ଏକ ଗୋଛା ସ୍କ୍ରିପ୍ଟ ଲେଖିଛୁ, ଏବଂ ସେଗୁଡ଼ିକୁ ଏକତ୍ର କରିଛୁ। ଲକ୍ଷ୍ୟ ହେଉଛି ଏହାକୁ ଆମର ଅଭିଲେଖ ପାଇଁ ମାତ୍ର ଉପଯୋଗୀ କରିବା ନୁହେଁ, କିନ୍ତୁ ଯେକୌଣସି ଲୋକଙ୍କ ପାଇଁ ସହଜ କରିବା ଯାହା ଛାୟା ଲାଇବ୍ରେରୀ metadata ସହିତ ଖେଳିବାକୁ ଚାହୁଁଛି। ଲକ୍ଷ୍ୟ ହେଉଛି ଏକ Jupyter ନୋଟବୁକ୍ ଯାହାରେ ସମସ୍ତ ପ୍ରକାରର ଆକର୍ଷଣୀୟ metadata ଉପଲବ୍ଧ ଅଛି, ଯାହା ଆମେ ଅଧିକ ଗବେଷଣା କରିପାରିବା ଯେପରିକି କି <a %(blog)s>କେତେ ପ୍ରତିଶତ ISBN ସଦାର ରଖାଯାଇଛି</a>। ଶେଷରେ, ଆମେ ଆମର ଦାନ ପ୍ରଣାଳୀକୁ ପୁନଃସଂରଚନା କରିଛୁ। ଆପଣ ବର୍ତ୍ତମାନ ଏକ କ୍ରେଡିଟ୍ କାର୍ଡ ବ୍ୟବହାର କରି ଆମର କ୍ରିପ୍ଟୋ ୱାଲେଟ୍‌ଗୁଡ଼ିକରେ ସିଧାସଳଖ ଟଙ୍କା ଜମା କରିପାରିବେ, କ୍ରିପ୍ଟୋକରେନ୍ସି ବିଷୟରେ କିଛି ଜାଣିବାର ଆବଶ୍ୟକତା ନାହିଁ। ଆମେ ଏହା କିପରି କାମ କରୁଛି ତାହା ଅବଲୋକନ କରିବାକୁ ରଖିବା, କିନ୍ତୁ ଏହା ଏକ ବଡ଼ ବିଷୟ। Z-Library ତଳକୁ ଯାଇବା ଏବଂ ତାହାର (ଅଭିଯୋଗିତ) ସ୍ଥାପକମାନେ ଗିରଫ ହେବା ସହିତ, ଆମେ ଆନାର ଆର୍କାଇଭ୍ ସହିତ ଏକ ଭଲ ବିକଳ୍ପ ପ୍ରଦାନ କରିବା ପାଇଁ ଦିନ ରାତି କାମ କରୁଛୁ (ଆମେ ଏଠାରେ ଏହାକୁ ଲିଙ୍କ କରିବୁ ନାହିଁ, କିନ୍ତୁ ଆପଣ ଏହାକୁ ଗୁଗୁଲ୍ କରିପାରନ୍ତି)। ଏଠାରେ କିଛି ଜିନିଷ ଅଛି ଯାହା ଆମେ ଏବେ ଭାବରେ ସଫଳ ହୋଇଛୁ। ଆନାର ଅଦ୍ୟତନ: ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଉତ୍ସ ଆର୍କାଇଭ୍, ElasticSearch, 300GB+ ପୁସ୍ତକ ଆବରଣ ଆମେ ଆନାର ଆର୍କାଇଭ୍ ସହିତ ଏକ ଭଲ ବିକଳ୍ପ ପ୍ରଦାନ କରିବା ପାଇଁ ଦିନ ରାତି କାମ କରୁଛୁ। ଏଠାରେ କିଛି ଜିନିଷ ଅଛି ଯାହା ଆମେ ଏବେ ଭାବରେ ସଫଳ ହୋଇଛୁ। ବିଶ୍ଳେଷଣ ସେମାଣ୍ଟିକ୍ ଡୁପ୍ଲିକେଟ୍ (ଏକେ ବହିର ଭିନ୍ନ ସ୍କାନ୍) ସୂତ୍ରରୁ ହଟାଇଯାଇପାରେ, କିନ୍ତୁ ଏହା ଜଟିଳ। ଯେତେବେଳେ ଆମେ କମିକ୍ସ ମାନଙ୍କୁ ମାନବୀୟ ଭାବରେ ଦେଖିଲୁ ଆମେ ଅତ୍ୟଧିକ ଭୁଲ୍ ସକାରାତ୍ମକ ଦେଖିଲୁ। କିଛି ଡୁପ୍ଲିକେଟ୍ କେବଳ MD5 ଦ୍ୱାରା ଅଛି, ଯାହା ତୁଳନାମୂଳକ ଭାବରେ ବ୍ୟର୍ଥ, କିନ୍ତୁ ସେଗୁଡ଼ିକୁ ହଟାଇବା ଆମକୁ ପ୍ରାୟ 1% in ସଂରକ୍ଷଣ ଦେବ। ଏହି ଆକାରରେ ଏହା ଏକ 1TB ହେବ, କିନ୍ତୁ ଏହି ଆକାରରେ 1TB ବାସ୍ତବରେ ମହତ୍ତ୍ୱପୂର୍ଣ୍ଣ ନୁହେଁ। ଆମେ ଏହି ପ୍ରକ୍ରିୟାରେ ତଥ୍ୟକୁ ଅକସ୍ମାତ ଧ୍ୱଂସ କରିବାର ଜୋଖିମ ନେବାକୁ ଚାହୁଁ ନାହିଁ। ଆମେ କିଛି ଅପୁସ୍ତକ ତଥ୍ୟ ଭେଟିଲୁ, ଯେପରିକି କମିକ୍ସ ପୁସ୍ତକ ଉପରେ ଆଧାରିତ ଚଳଚ୍ଚିତ୍ର। ଏହା ମଧ୍ୟ ବ୍ୟର୍ଥ ଲାଗୁଛି, କାରଣ ଏହାମାନେ ଅନ୍ୟ ଉପାୟ ଦ୍ୱାରା ପ୍ରଚୁର ଭାବରେ ଉପଲବ୍ଧ। ତେବେ, ଆମେ ବୁଝିଲୁ ଯେ ଆମେ ଚଳଚ୍ଚିତ୍ର ଫାଇଲ୍‌ଗୁଡ଼ିକୁ ହଟାଇପାରିବା ନୁହେଁ, କାରଣ ଏହାରେ କମ୍ପ୍ୟୁଟରରେ ମୁକ୍ତି ପାଇଥିବା <em>ଇଣ୍ଟରାକ୍ଟିଭ୍ କମିକ୍ସ ପୁସ୍ତକ</em> ମଧ୍ୟ ଅଛି, ଯାହାକୁ କେହି ରେକର୍ଡ କରି ଚଳଚ୍ଚିତ୍ର ଭାବରେ ସଂରକ୍ଷଣ କରିଛନ୍ତି। ଶେଷରେ, ଆମେ ସଂଗ୍ରହରୁ କଣସି ହଟାଇପାରିବା ମାତ୍ର କିଛି ପ୍ରତିଶତ ସଂରକ୍ଷଣ କରିପାରିବ। ତାହାପରେ ଆମେ ମନେ ପକାଇଲୁ ଯେ ଆମେ ତଥ୍ୟ ସଂଗ୍ରହକାରୀ, ଏବଂ ଯେଉଁମାନେ ଏହାକୁ ମିରର୍ କରିବେ ସେମାନେ ମଧ୍ୟ ତଥ୍ୟ ସଂଗ୍ରହକାରୀ, ଏବଂ ତେଣୁ, “କଣ ଅର୍ଥ ହେଉଛି, ହଟାଇବା?!” :) ଯେତେବେଳେ ଆପଣଙ୍କ ଷ୍ଟୋରେଜ୍ କ୍ଲଷ୍ଟରରେ 95TB ଡମ୍ପ୍ ହୋଇଥାଏ, ଆପଣ ଏହାରେ କଣ ଅଛି ତାହା ବୁଝିବାକୁ ଚେଷ୍ଟା କରନ୍ତି… ଆମେ କିଛି ବିଶ୍ଳେଷଣ କରିଲୁ ଯାହା ଆମେ ଆକାରକୁ ଅଳ୍ପ କରିପାରିବା କି ନାହିଁ ଦେଖିବାକୁ, ଯେପରିକି ଡୁପ୍ଲିକେଟ୍ ହଟାଇବା ଦ୍ୱାରା। ଏଠାରେ ଆମର କିଛି ଆବିଷ୍କାର ଅଛି: ଏହିପରିସର୍ବାଧିକ ତଥ୍ୟ ଆପଣଙ୍କୁ ପ୍ରଦାନ କରୁଛୁଅ, ଯାହା ଅସଂଶୋଧିତ ସଂଗ୍ରହ। ଏହା ବହୁତ ତଥ୍ୟ, କିନ୍ତୁ ଆମେ ଆଶା କରୁଛୁଅ ଯେ ପ୍ରଚୁର ଲୋକ ଏହାକୁ ସିଡ୍ କରିବାକୁ ଚିନ୍ତା କରିବେ। ସହଯୋଗ ଏହାର ଆକାର ଦେଖି, ଏହି ସଂଗ୍ରହ ଦୀର୍ଘ ଦିନ ଧରି ଆମର ଇଚ୍ଛାସୂଚୀରେ ରହିଛି, ତେଣୁ Z-ଲାଇବ୍ରେରୀକୁ ବ୍ୟାକଅପ୍ କରିବାରେ ଆମର ସଫଳତା ପରେ, ଆମେ ଏହି ସଂଗ୍ରହ ଉପରେ ନଜର ଦେଲୁ। ପ୍ରଥମେ ଆମେ ଏହାକୁ ସିଧାସଳଖ ଉପରେ ନେଲୁ, ଯାହା ଏକ ବଡ଼ ଚ୍ୟାଲେଞ୍ଜ ଥିଲା, କାରଣ ତାଙ୍କର ସର୍ଭର ଶ୍ରେଷ୍ଠ ଅବସ୍ଥାରେ ନଥିଲା। ଏହି ପ୍ରକାରରେ ଆମେ ପ୍ରାୟ 15TB ପାଇଲୁ, କିନ୍ତୁ ଏହା ଧୀର ଗତିରେ ଚାଲିଲା। ଭାଗ୍ୟକ୍ରମେ, ଆମେ ଲାଇବ୍ରେରୀର ଅପରେଟର ସହିତ ସଂଯୋଗ ସ୍ଥାପନ କରିବାରେ ସମର୍ଥ ହେଲୁ, ଯିଏ ଆମକୁ ସମସ୍ତ ତଥ୍ୟ ସିଧାସଳଖ ପଠାଇବାକୁ ସମ୍ମତ ହେଲେ, ଯାହା ଅଧିକ ତ୍ୱରିତ ଥିଲା। ସମସ୍ତ ତଥ୍ୟକୁ ସ୍ଥାନାନ୍ତରଣ ଏବଂ ପ୍ରକ୍ରିୟାକରଣ କରିବାକୁ ଅଧିକ ହାଫ୍ ବର୍ଷ ଲାଗିଲା, ଏବଂ ଆମେ ପ୍ରାୟ ସମସ୍ତକୁ ଡିସ୍କ କରପ୍ସନ୍‌ରେ ହରାଇବାକୁ ଯାଇଥିଲୁ, ଯାହା ଅର୍ଥ ହେଉଥାନ୍ତା ଆରମ୍ଭରୁ ଆରମ୍ଭ କରିବା। ଏହି ଅନୁଭବ ଆମକୁ ବିଶ୍ୱାସ କରାଇଛି ଯେ ଏହି ତଥ୍ୟକୁ ଯଥାଶୀଘ୍ର ସାମ୍ନାରେ ଆଣିବା ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ, ଯାହା ଯେଉଁଠାରେ ତାହାକୁ ଦୂର ଦୂର ଯାଇ ମିରର୍ କରାଯାଇପାରେ। ଆମେ ଏହି ସଂଗ୍ରହକୁ ସର୍ବଦା ପାଇଁ ହରାଇବାରୁ ମାତ୍ର ଏକ କିମ୍ବା ଦୁଇଟି ଅଭାଗ୍ୟ ସମୟର ଘଟଣା ଦୂରରେ ଅଛୁ! ସଂଗ୍ରହ ତ୍ୱରିତ ଗତିରେ ଗତି କରିବାର ଅର୍ଥ ହେଉଛି ସଂଗ୍ରହ ଅଳ୍ପ ଅସଂଗଠିତ… ଆସନ୍ତୁ ଦେଖିବା। ଧରନ୍ତୁ ଆମେ ଏକ ଫାଇଲସିଷ୍ଟମ୍ ରଖିଛୁ (ଯାହା ବାସ୍ତବରେ ଆମେ ଟୋରେଣ୍ଟରେ ବିଭାଜିତ କରୁଛୁ): ପ୍ରଥମ ଡାଇରେକ୍ଟରୀ, <code>/ରିପୋଜିଟୋରୀ</code>, ଏହାର ଅଧିକ ସଂଗଠିତ ଅଂଶ। ଏହି ଡାଇରେକ୍ଟରୀରେ ଏକ ହଜାର ଫାଇଲ୍‌ର ସହିତ ଏକ ଡାଇରେକ୍ଟରୀ ରହିଛି, ଯାହା ଡାଟାବେସରେ କ୍ରମାଗତ ଭାବରେ ସଂଖ୍ୟାନ୍ତରିତ ହୋଇଛି। ଡାଇରେକ୍ଟରୀ <code>0</code>ରେ କମିକ୍ସ_ଆଇଡି 0–999 ସହିତ ଫାଇଲ୍ ରହିଛି, ଏବଂ ଏହିପରି। ଏହା ଲାଇବ୍ରେରୀ ଜେନେସିସ୍ ତାଙ୍କର କଳ୍ପନା ଏବଂ ଅକଳ୍ପନା ସଂଗ୍ରହ ପାଇଁ ବ୍ୟବହାର କରୁଥିବା ସମାନ ଯୋଜନା। ଧାରଣା ହେଉଛି ଯେପରିକି ଏକ "ହଜାର ଡିର୍" ସଂପୂର୍ଣ୍ଣ ହେଲା ବେଳେ ଏହାକୁ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଟୋରେଣ୍ଟରେ ପରିଣତ କରାଯାଏ। ହେଲେ ଲିବଜେନ.ଲି ଅପରେଟର ଏହି ସଂଗ୍ରହ ପାଇଁ କେବେ ଟୋରେଣ୍ଟ ତିଆରି କରିନଥିଲେ, ଏବଂ ତେଣୁ ହଜାର ଡିର୍‌ଗୁଡିକ ନିଶ୍ଚିତ ଭାବରେ ଅସୁବିଧାଜନକ ହୋଇଗଲା, ଏବଂ "ଅସଂଗଠିତ ଡିର୍‌ଗୁଡିକ" ଦେଇ ରାସ୍ତା ଦେଲା। ଏହାମାନେ <code>/କମିକ୍ସ0</code> ଠାରୁ <code>/କମିକ୍ସ4</code> ପର୍ଯ୍ୟନ୍ତ। ସମସ୍ତ ଅନନ୍ୟ ଡାଇରେକ୍ଟରୀ ଗଠନରେ ରହିଛି, ଯାହା ସମ୍ଭବତଃ ଫାଇଲ୍ ସଂଗ୍ରହ କରିବା ପାଇଁ ଅର୍ଥପୂର୍ଣ୍ଣ ଥିଲା, କିନ୍ତୁ ଏବେ ଆମ ପାଇଁ ଅର୍ଥପୂର୍ଣ୍ଣ ନୁହେଁ। ଭାଗ୍ୟକ୍ରମେ, ମେଟାଡାଟା ଏହି ସମସ୍ତ ଫାଇଲ୍‌ରେ ସିଧାସଳଖ ଉଲ୍ଲେଖ କରେ, ତେଣୁ ଡିସ୍କରେ ତାଙ୍କର ସଂଗ୍ରହ ସଂଗଠନ ବାସ୍ତବରେ ମହତ୍ତ୍ୱପୂର୍ଣ୍ଣ ନୁହେଁ! ମେଟାଡାଟା ଏକ MySQL ଡାଟାବେସ୍ ଆକାରରେ ଉପଲବ୍ଧ ଅଛି। ଏହାକୁ ଲିବଜେନ.ଲି ୱେବସାଇଟରୁ ସିଧାସଳଖ ଡାଉନଲୋଡ୍ କରାଯାଇପାରେ, କିନ୍ତୁ ଆମେ ଏହାକୁ ଏକ ଟୋରେଣ୍ଟରେ ଉପଲବ୍ଧ କରାଇବାକୁ ମଧ୍ୟ ଯାଉଛୁ, ଆମର ସମସ୍ତ MD5 ହାସ୍‌ ସହିତ ଏକ ଟେବୁଲ୍ ସହିତ। <q>ଡ଼ା. ବାର୍ବାରା ଗୋର୍ଡନ ଲାଇବ୍ରେରୀର ସାଧାରଣ ଜଗତରେ ନିଜକୁ ହରାଇବାକୁ ଚେଷ୍ଟା କରୁଛନ୍ତି…</q> ଲିବଜେନ ଫର୍କସ୍ ପ୍ରଥମେ, କିଛି ପୃଷ୍ଠଭୂମି। ଆପଣ ଲାଇବ୍ରେରୀ ଜେନେସିସ୍‌କୁ ତାଙ୍କର ଅପାର ବହି ସଂଗ୍ରହ ପାଇଁ ଜାଣିଥାଇପାରନ୍ତି। କମ୍ ଲୋକ ଜାଣନ୍ତି ଯେ ଲାଇବ୍ରେରୀ ଜେନେସିସ୍ ସେବକମାନେ ଅନ୍ୟାନ୍ୟ ପ୍ରକଳ୍ପ ସୃଷ୍ଟି କରିଛନ୍ତି, ଯେପରିକି ଏକ ବଡ଼ ସଂଗ୍ରହ ମ୍ୟାଗାଜିନ୍ ଏବଂ ମାନକ ଡକ୍ୟୁମେଣ୍ଟ, ସାଇ-ହବର ପୂର୍ଣ୍ଣ ବ୍ୟାକଅପ୍ (ସାଇ-ହବର ସ୍ଥାପକ ଆଲେକ୍ସାଣ୍ଡ୍ରା ଏଲବାକ୍ୟାନ ସହିତ ସହଯୋଗରେ), ଏବଂ ନିଶ୍ଚିତ ଭାବରେ, ଏକ ବିଶାଳ କମିକ୍ସ ସଂଗ୍ରହ। କିଛି ସମୟରେ ଲାଇବ୍ରେରୀ ଜେନେସିସ୍ ମିରର୍‌ର ଭିନ୍ନ ଅପରେଟରମାନେ ତାଙ୍କର ଅଲଗା ରାସ୍ତା ନେଲେ, ଯାହା ଏହି ସମୟରେ ବିଭିନ୍ନ "ଫର୍କସ୍" ରହିବାର ପରିସ୍ଥିତିକୁ ଜନ୍ମ ଦେଲା, ସବୁ ଏବେ ମଧ୍ୟ ଲାଇବ୍ରେରୀ ଜେନେସିସ୍ ନାମ ବହନ କରୁଛି। ଲିବଜେନ.ଲି ଫର୍କରେ ଏହି କମିକ୍ସ ସଂଗ୍ରହ ଅନନ୍ୟ ଭାବରେ ରହିଛି, ଏବଂ ଏକ ବଡ଼ ମ୍ୟାଗାଜିନ୍ ସଂଗ୍ରହ ମଧ୍ୟ ରହିଛି (ଯାହା ଉପରେ ଆମେ ମଧ୍ୟ କାମ କରୁଛୁ)। ଧନସଂଗ୍ରହ ଆମେ ଏହି ତଥ୍ୟକୁ କିଛି ବଡ଼ ଖଣ୍ଡରେ ମୁକ୍ତ କରୁଛୁଅ। ପ୍ରଥମ ଟୋରେଣ୍ଟ ହେଉଛି <code>/comics0</code>, ଯାହାକୁ ଆମେ ଗୋଟିଏ ବଡ଼ 12TB .tar ଫାଇଲରେ ରଖିଛୁଅ। ଏହା ଆପଣଙ୍କ ହାର୍ଡ ଡ୍ରାଇଭ୍ ଏବଂ ଟୋରେଣ୍ଟ ସଫ୍ଟୱେର ପାଇଁ ଅନେକ ଛୋଟ ଫାଇଲ ଠାରୁ ଭଲ। ଏହି ମୁକ୍ତିର ଅଂଶ ଭାବରେ, ଆମେ ଧନସଂଗ୍ରହ କରୁଛୁଅ। ଏହି ସଂଗ୍ରହ ପାଇଁ ପ୍ରଚାଳନ ଏବଂ ଠିକାଦାରୀ ଖର୍ଚ୍ଚ ଆବରଣ କରିବାକୁ ଏବଂ ଚାଲୁ ଏବଂ ଭବିଷ୍ୟତ ପ୍ରକଳ୍ପଗୁଡ଼ିକୁ ସକ୍ଷମ କରିବାକୁ ଆମେ $20,000 ଉଠାଇବାକୁ ଚାହୁଁଛୁଅ। ଆମେ କିଛି <em>ବିଶାଳ</em> ପ୍ରକଳ୍ପରେ କାମ କରୁଛୁଅ। <em>ମୁଁ ମୋ ଦାନରେ କାହାକୁ ସମର୍ଥନ କରୁଛି?</em> ସଂକ୍ଷେପରେ: ଆମେ ସମସ୍ତ ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ସଂରକ୍ଷଣ କରୁଛୁଅ, ଏବଂ ଏହାକୁ ସହଜରେ ପ୍ରାପ୍ୟ କରୁଛୁଅ। ଆମ ସମସ୍ତ କୋଡ୍ ଏବଂ ତଥ୍ୟ ଖୋଲା ଉତ୍ସରେ ଅଛି, ଆମେ ସଂପୂର୍ଣ୍ଣ ସ୍ୱେଚ୍ଛାସେବୀ ପ୍ରକଳ୍ପ ଚାଲାଉଛୁଅ, ଏବଂ ଆମେ 125TB ପୁସ୍ତକ ସଂରକ୍ଷଣ କରିଛୁଅ (Libgen ଏବଂ Scihubର ଅବସ୍ଥିତ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଅତିରିକ୍ତ ଭାବରେ)। ଶେଷରେ ଆମେ ଏକ ଫ୍ଲାଇୱିଲ୍ ନିର୍ମାଣ କରୁଛୁଅ ଯାହା ଲୋକମାନଙ୍କୁ ସମସ୍ତ ପୁସ୍ତକ ଖୋଜିବା, ସ୍କାନ କରିବା ଏବଂ ସଂରକ୍ଷଣ କରିବାକୁ ସକ୍ଷମ ଏବଂ ପ୍ରେରିତ କରେ। ଆମେ ଆମର ପ୍ରଧାନ ଯୋଜନା ବିଷୟରେ ଭବିଷ୍ୟତ ପୋଷ୍ଟରେ ଲେଖିବାକୁ ଯାଉଛୁଅ। :) ଯଦି ଆପଣ 12 ମାସର “Amazing Archivist” ସଦସ୍ୟତା ପାଇଁ ଦାନ କରନ୍ତି ($780), ତେବେ ଆପଣ <strong>“ଏକ ଟୋରେଣ୍ଟକୁ ଅପନାଇବା”</strong> ପାଇଁ ପାଇବେ, ଅର୍ଥାତ ଆମେ ଆପଣଙ୍କ ଉପଯୋଗକର୍ତ୍ତାନାମ କିମ୍ବା ସନ୍ଦେଶକୁ ଟୋରେଣ୍ଟଗୁଡ଼ିକର ଫାଇଲନାମରେ ରଖିବାକୁ ଯାଉଛୁଅ! <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a>କୁ ଯାଇ ଏବଂ “ଦାନ କରନ୍ତୁ” ବଟନ୍ କ୍ଲିକ୍ କରି ଆପଣ ଦାନ କରିପାରିବେ। ଆମେ ଅଧିକ ସ୍ୱେଚ୍ଛାସେବକଙ୍କୁ ଖୋଜୁଛୁଅ: ସଫ୍ଟୱେର ଇଞ୍ଜିନିୟର, ସୁରକ୍ଷା ଗବେଷକ, ଗୋପନୀୟ ବ୍ୟବସାୟ ବିଶେଷଜ୍ଞ, ଏବଂ ଅନୁବାଦକ। ଆପଣ ଆମକୁ ହୋଷ୍ଟିଂ ସେବା ପ୍ରଦାନ କରି ସମର୍ଥନ କରିପାରିବେ। ଏବଂ ନିଶ୍ଚିତ ଭାବେ, ଦୟାକରି ଆମର ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ସିଡ୍ କରନ୍ତୁ! ଯେଉଁମାନେ ଆଗରୁ ଏତେ ଉଦାର ଭାବରେ ଆମକୁ ସମର୍ଥନ କରିଛନ୍ତି ସେମାନଙ୍କୁ ଧନ୍ୟବାଦ! ଆପଣମାନେ ନିଶ୍ଚିତ ଭାବରେ ଏକ ପରିବର୍ତ୍ତନ ଆଣୁଛନ୍ତି। ଏପର୍ଯ୍ୟନ୍ତ ମୁକ୍ତ ହୋଇଥିବା ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଏହାରେ ଅଛି (ଆମେ ଅନ୍ୟମାନଙ୍କୁ ପ୍ରକ୍ରିୟାକରଣ କରୁଛୁଅ): ସମସ୍ତ ଟୋରେଣ୍ଟ <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a>ରେ “Datasets” ଅଧୀନରେ ମିଳିବ (ଆମେ ସେଠାରେ ସିଧାସଳଖ ଲିଙ୍କ୍ ଦେଉନାହିଁ, ତେଣୁ ଏହି ବ୍ଲଗ୍ ରେଡିଟ୍, ଟ୍ୱିଟର୍ ଇତ୍ୟାଦିରୁ ହଟାଯାଏ ନାହିଁ)। ସେଠାରୁ, ଟୋର୍ ୱେବସାଇଟକୁ ଲିଙ୍କ୍ ଅନୁସରଣ କରନ୍ତୁ। <a %(news_ycombinator)s>Hacker News ରେ ଆଲୋଚନା କରନ୍ତୁ</a> ପରବର୍ତ୍ତୀ କ'ଣ? ଏକ ଗୋଷ୍ଠୀ ଟୋରେଣ୍ଟ ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣ ପାଇଁ ଉତ୍ତମ, କିନ୍ତୁ ପ୍ରତିଦିନ ଅଭିଗମ ପାଇଁ ଏତେ ନୁହେଁ। ଆମେ ଏହି ସମସ୍ତ ତଥ୍ୟକୁ ଇଣ୍ଟରନେଟରେ ରଖିବା ପାଇଁ ହୋଷ୍ଟିଂ ସହଭାଗୀମାନଙ୍କ ସହିତ କାମ କରିବାକୁ ଯାଉଛୁଅ (ଯେହେତୁ ଆନାର ଆର୍କାଇଭ୍ କିଛି ସିଧାସଳଖ ହୋଷ୍ଟ କରେନାହିଁ)। ନିଶ୍ଚିତ ଭାବେ ଆପଣ ଆନାର ଆର୍କାଇଭ୍ରେ ଏହି ଡାଉନଲୋଡ୍ ଲିଙ୍କ୍ ମିଳିବ। ଆମେ ସମସ୍ତଙ୍କୁ ଏହି ତଥ୍ୟ ସହିତ କିଛି କରିବାକୁ ଆମନ୍ତ୍ରଣ କରୁଛୁଅ! ଆମକୁ ଏହାକୁ ଭଲ ଭାବରେ ବିଶ୍ଳେଷଣ କରିବାରେ, ଏହାକୁ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ କରିବାରେ, ଏହାକୁ IPFSରେ ରଖିବାରେ, ଏହାକୁ ପୁନଃମିଶ୍ରଣ କରିବାରେ, ଆପଣଙ୍କ AI ମଡେଲ୍ ସହିତ ଏହାକୁ ପ୍ରଶିକ୍ଷଣ କରିବାରେ ଆଦିରେ ସାହାଯ୍ୟ କରନ୍ତୁ। ଏହା ସମସ୍ତ ଆପଣଙ୍କର, ଏବଂ ଆମେ ଆପଣ କ'ଣ କରିବେ ଦେଖିବାକୁ ଅପେକ୍ଷା କରୁଛୁଅ। ଶେଷରେ, ପୂର୍ବରୁ କହାଯାଇଥିବା ଭଳି, ଆମେ ଏବେ ମଧ୍ୟ କିଛି ବିଶାଳ ମୁକ୍ତି ଆଣୁଛୁ (ଯଦି <em>କେହି</em> ଏକ <em>ନିର୍ଦ୍ଦିଷ୍ଟ</em> ACS4 ଡାଟାବେସର ଏକ ଡମ୍ପ ଆକସ୍ମିକ ଭାବେ ଆମକୁ ପଠାଇପାରନ୍ତି, ଆପଣ ଆମକୁ କେଉଁଠାରେ ମିଳିବା ଜାଣିଛନ୍ତି…), ଏବଂ ସମସ୍ତ ପୁସ୍ତକକୁ ସଂରକ୍ଷଣ କରିବା ପାଇଁ ଫ୍ଲାଇୱିଲ୍ ନିର୍ମାଣ କରିବା। ତେଣୁ ଅପେକ୍ଷା କରନ୍ତୁ, ଆମେ କେବଳ ଆରମ୍ଭ କରୁଛୁ। - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) କମିକ୍ସ ପୁସ୍ତକର ସବୁଠାରୁ ବଡ଼ ଛାୟା ଲାଇବ୍ରେରୀ ସମ୍ଭବତଃ ଏକ ବିଶେଷ Library Genesis ଫର୍କର: Libgen.li। ସେହି ସାଇଟ୍ ଚାଳନା କରୁଥିବା ଏକ ଏକାକୀ ପ୍ରଶାସକ 2 ମିଲିଅନ ଫାଇଲର ଉପରେ ଏକ ଅବିଶ୍ୱସନୀୟ କମିକ୍ସ ସଂଗ୍ରହ ସଂଗ୍ରହ କରିବାରେ ସଫଳ ହୋଇଥିଲେ, ମୋଟ 95TB ରୁ ଅଧିକ। କିନ୍ତୁ, ଅନ୍ୟ Library Genesis ସଂଗ୍ରହଗୁଡ଼ିକ ଭଳି ନୁହେଁ, ଏହା ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ଏକାଠା ଉପଲବ୍ଧ ନଥିଲା। ଆପଣ ଏହି କମିକ୍ସଗୁଡ଼ିକୁ ତାଙ୍କର ଧୀର ନିଜସ୍ୱ ସର୍ଭର ମାଧ୍ୟମରେ ଏକାକୀ ଭାବରେ ପ୍ରାପ୍ତ କରିପାରୁଥିଲେ — ଏକ ମାତ୍ର ବିଫଳତା ବିନ୍ଦୁ। ଆଜି ପର୍ଯ୍ୟନ୍ତ! ଏହି ପୋଷ୍ଟରେ ଆମେ ଆପଣଙ୍କୁ ଏହି ସଂଗ୍ରହ ବିଷୟରେ ଅଧିକ କହିବାକୁ ଚାହୁଁଛୁ, ଏବଂ ଏହି କାମର ଅଧିକ ସମର୍ଥନ ପାଇଁ ଆମର ଧନସଂଗ୍ରହ ବିଷୟରେ। ଆନାର ଅଭିଲେଖ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ କମିକ୍ସ ଛାୟା ଲାଇବ୍ରେରୀକୁ (95TB) ସଂରକ୍ଷଣ କରିଛି — ଆପଣ ଏହାକୁ ସିଡ୍ କରିବାରେ ସାହାଯ୍ୟ କରିପାରିବେ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ କମିକ୍ସ ପୁସ୍ତକ ଛାୟା ଲାଇବ୍ରେରୀର ଏକ ମାତ୍ର ବିଫଳତା ବିନ୍ଦୁ ଥିଲା.. ଆଜି ପର୍ଯ୍ୟନ୍ତ। ସତର୍କତା: ଏହି ବ୍ଲଗ୍ ପୋଷ୍ଟ ଅବସରପ୍ରାପ୍ତ ହୋଇଛି। ଆମେ ନିଷ୍ପତ୍ତି ନେଇଛୁ ଯେ IPFS ଏଯାବତ୍ ମୁଖ୍ୟ ସମୟ ପାଇଁ ପ୍ରସ୍ତୁତ ନୁହେଁ। ଆମେ ଆନାର ଆର୍କାଇଭ୍ ଠାରୁ ସମ୍ଭବ ହେଲେ IPFS ଉପରେ ଫାଇଲ୍ ଲିଙ୍କ କରିବାକୁ ଚାଲିବା, କିନ୍ତୁ ଆମେ ଏହାକୁ ଆପଣା ମାନେ ଆରମ୍ଭ କରିବାକୁ ସୁପାରିଶ କରୁନାହିଁ, ନାହିଁ କି ଅନ୍ୟମାନଙ୍କୁ IPFS ବ୍ୟବହାର କରି ମିରର୍ କରିବାକୁ ସୁପାରିଶ କରୁନାହିଁ। ଯଦି ଆପଣ ଆମ ସଂଗ୍ରହକୁ ସଂରକ୍ଷଣ କରିବାକୁ ସାହାଯ୍ୟ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ତାହେଲେ ଆମର ଟୋରେଣ୍ଟ ପୃଷ୍ଠା ଦେଖନ୍ତୁ। 5,998,794 ପୁସ୍ତକକୁ IPFS ରେ ରଖିବା ପ୍ରତିର ପୁନରାବୃତ୍ତି ଆମର ମୂଳ ପ୍ରଶ୍ନକୁ ଫେରିବା: କିପରି ଆମେ ଆମର ସଂଗ୍ରହକୁ ଚିରକାଳ ପର୍ଯ୍ୟନ୍ତ ସଂରକ୍ଷିତ କରିବାକୁ ଦାବି କରିପାରିବା? ଏଠାରେ ମୁଖ୍ୟ ସମସ୍ୟା ହେଉଛି ଯେ ଆମର ସଂଗ୍ରହ <a %(torrents_stats)s>ଦ୍ରୁତ ଗତିରେ ବୃଦ୍ଧି</a> ପାଇଛି, କିଛି ବିଶାଳ ସଂଗ୍ରହକୁ ସ୍କ୍ରାପିଂ ଏବଂ ଖୋଲା ଉତ୍ସ କରିବା ଦ୍ୱାରା (ଅନ୍ୟ ଖୋଲା-ତଥ୍ୟ ଛାୟା ପୁସ୍ତକାଳୟ ଯଥା ସାଇ-ହବ୍ ଏବଂ ଲାଇବ୍ରେରୀ ଜେନେସିସ ଦ୍ୱାରା ଆଗରୁ କରାଯାଇଥିବା ଅଦ୍ଭୁତ କାମର ଉପରେ) ଏହି ତଥ୍ୟର ବୃଦ୍ଧି ଏହାକୁ ବିଶ୍ୱରେ ଆଲୋକିତ କରିବାକୁ କଠିନ କରେ। ତଥ୍ୟ ସଂରକ୍ଷଣ ମହଙ୍ଗା! କିନ୍ତୁ ଆମେ ଆଶାବାଦୀ, ବିଶେଷକରି ନିମ୍ନଲିଖିତ ତିନିଟି ପ୍ରବୃତ୍ତିକୁ ଅବଲୋକନ କରିବା ସମୟରେ। ବିଭିନ୍ନ ଉତ୍ସରୁ HDD ମୂଲ୍ୟ ପ୍ରବନ୍ଧ (ଅଧ୍ୟୟନ ଦେଖିବାକୁ କ୍ଲିକ୍ କରନ୍ତୁ)। 1. ଆମେ ସହଜରେ ଉପଲବ୍ଧ ଫଳଗୁଡ଼ିକ ଚୟନ କରିଛୁ ଏହା ଆମର ପୂର୍ବରୁ ଆଲୋଚିତ ପ୍ରାଥମିକତାରୁ ସିଧାସଳଖ ଅନୁସରଣ କରେ। ଆମେ ପ୍ରଥମେ ବଡ଼ ସଂଗ୍ରହଗୁଡ଼ିକୁ ମୁକ୍ତ କରିବାକୁ ପସନ୍ଦ କରିଥାଉ। ବର୍ତ୍ତମାନ ଆମେ ବିଶ୍ୱର କିଛି ବଡ଼ ସଂଗ୍ରହ ସୁରକ୍ଷିତ କରିଛୁ, ଆମେ ଆଶା କରୁଛୁ ଯେ ଆମର ବୃଦ୍ଧି ଅନେକ ଧୀର ହେବ। ଏଠାରେ ଏକ ଦୀର୍ଘ ପୁଛ ଅଛି ଛୋଟ ସଂଗ୍ରହଗୁଡ଼ିକର, ଏବଂ ପ୍ରତିଦିନ ନୂତନ ପୁସ୍ତକଗୁଡ଼ିକ ସ୍କାନ କିମ୍ବା ପ୍ରକାଶିତ ହେଉଛି, କିନ୍ତୁ ହାର ଅନେକ ଧୀର ହେବ। ଆମେ ଏପରିକି ଆମର ଆକାରକୁ ଦୁଇଗୁଣା କିମ୍ବା ତିନିଗୁଣା କରିପାରିବା, କିନ୍ତୁ ଏକ ଦୀର୍ଘ ସମୟ ଅବଧିରେ। OCR ଉନ୍ନତି। ବିଜ୍ଞାନ ଏବଂ ଇଞ୍ଜିନିୟରିଂ ସଫ୍ଟୱେର କୋଡ୍ ଉପରୋକ୍ତ ସମସ୍ତର କଳ୍ପିତ କିମ୍ବା ମନୋରଞ୍ଜନ ସଂସ୍କରଣ ଭୌଗୋଳିକ ତଥ୍ୟ (ଉଦାହରଣ ସ୍ୱରୂପ ମାନଚିତ୍ର, ଭୂତାତ୍ତ୍ୱିକ ସର୍ଭେ) କର୍ପୋରେସନ କିମ୍ବା ସରକାରର ଆଭ୍ୟନ୍ତରୀଣ ତଥ୍ୟ (ଅଭିସରଣ) ବିଜ୍ଞାନ ମାପନ, ଅର୍ଥନୈତିକ ତଥ୍ୟ, କର୍ପୋରେଟ୍ ରିପୋର୍ଟ ଭଳି ମାପନ ତଥ୍ୟ ମେଟାଡାଟା ରେକର୍ଡ ସାଧାରଣତଃ (ଅପ୍ରବନ୍ଧ ଏବଂ ପ୍ରବନ୍ଧ; ଅନ୍ୟ ମାଧ୍ୟମ, କଳା, ଲୋକମାନଙ୍କ ଆଦି; ସମୀକ୍ଷା ସହିତ) ଅପ୍ରବନ୍ଧ ପୁସ୍ତକ ଅପ୍ରବନ୍ଧ ପତ୍ରିକା, ସମାଚାରପତ୍ର, ମାନୁଆଲ୍ ଅପ୍ରବନ୍ଧ ଟ୍ରାନ୍ସକ୍ରିପ୍ଟ, ଡକ୍ୟୁମେଣ୍ଟାରୀ, ପଡକାଷ୍ଟ ଡିଏନଏ ଅନୁକ୍ରମ, ଉଦ୍ଭିଦ ବୀଜ, କିମ୍ବା ଜୀବାଣୁ ନମୁନା ଭଳି ସ୍ୱାଭାବିକ ତଥ୍ୟ ବିଜ୍ଞାନ ଏବଂ ଇଞ୍ଜିନିୟରିଂ ୱେବସାଇଟ୍, ଅନଲାଇନ୍ ଆଲୋଚନା କାନୁନିକ କିମ୍ବା ଅଦାଲତୀ କାର୍ଯ୍ୟବିବରଣୀର ଟ୍ରାନ୍ସକ୍ରିପ୍ଟ ବିନାଶର ବିଶିଷ୍ଟ ବିପଦରେ ଅଛି (ଉଦାହରଣ ସ୍ୱରୂପ ଯୁଦ୍ଧ, ଅର୍ଥାନୁଦାନ କମିବା, ମାମଲା, କିମ୍ବା ରାଜନୈତିକ ଦମନ) ଦୁର୍ଲଭ ଅନନ୍ୟ ଭାବରେ ଅବହେଳିତ ଏହି ତାଲିକାରେ ଶ୍ରେଣୀବଦ୍ଧତା କିଛି ଅନିୟମିତ — କିଛି ଆଇଟମ୍ ସମାନ କିମ୍ବା ଆମର ଦଳ ମଧ୍ୟରେ ମତଭେଦ ଅଛି — ଏବଂ ଆମେ ସମ୍ଭବତଃ କିଛି ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଶ୍ରେଣୀ ଭୁଲିଯାଇଛୁ। କିନ୍ତୁ ଏହା ମୋଟାମୋଟି ଭାବରେ ଆମେ କିପରି ପ୍ରାଥମିକତା ଦେଉଛୁ। ଏହି ଆଇଟମଗୁଡ଼ିକର କିଛି ଅନ୍ୟମାନଙ୍କ ଠାରୁ ବହୁତ ଭିନ୍ନ ଅଛି ଯାହାକୁ ଆମେ ଚିନ୍ତା କରିବାକୁ ହୋଇନାହିଁ (କିମ୍ବା ଅନ୍ୟ ସଂସ୍ଥାମାନଙ୍କ ଦ୍ୱାରା ଏହାର ଦେଖାଶୁଣା ହୋଇଯାଇଛି), ଯଥା ଜୈବିକ ତଥ୍ୟ କିମ୍ବା ଭୌଗୋଳିକ ତଥ୍ୟ। କିନ୍ତୁ ଏହି ତାଲିକାର ଅଧିକାଂଶ ଆଇଟମ ଆମ ପାଇଁ ପ୍ରକୃତରେ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ। ଆମର ପ୍ରାଥମିକତାର ଆଉ ଗୋଟିଏ ବଡ଼ ତତ୍ତ୍ୱ ହେଉଛି ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ କାର୍ଯ୍ୟ କେତେ ବିପଦରେ ଅଛି। ଆମେ ସେହି କାର୍ଯ୍ୟଗୁଡ଼ିକରେ ଧ୍ୟାନ ଦେବାକୁ ପସନ୍ଦ କରୁଛୁ ଯାହା: ଶେଷରେ, ଆମେ ପ୍ରମାଣରେ ଧ୍ୟାନ ଦେଉଛୁ। ଆମ ପାଖରେ ସୀମିତ ସମୟ ଏବଂ ଧନ ଅଛି, ତେଣୁ ଆମେ 1,000 ପୁସ୍ତକ ରକ୍ଷା କରିବାଠାରୁ 10,000 ପୁସ୍ତକ ରକ୍ଷା କରିବାକୁ ଏକ ମାସ ବ୍ୟୟ କରିବାକୁ ଚାହୁଁଛୁ — ଯଦି ସେଗୁଡ଼ିକ ସମାନ ମୂଲ୍ୟବାନ ଏବଂ ବିପଦରେ ଅଛି। <em><q>ହାରାଇ ଯାଇଥିବାକୁ ପୁନଃପ୍ରାପ୍ତ କରାଯାଇପାରିବ ନାହିଁ; କିନ୍ତୁ ଯାହା ଅବଶିଷ୍ଟ ଅଛି ତାହାକୁ ରକ୍ଷା କରିବା ଯାଉ: ଜନସାଧାରଣଙ୍କ ଦୃଷ୍ଟି ଏବଂ ବ୍ୟବହାରରୁ ସେଗୁଡ଼ିକୁ ଅଲଗା କରିବାକୁ ଭାଣ୍ଡାର ଏବଂ ତାଲା ଦ୍ୱାରା ନୁହେଁ, କିନ୍ତୁ ଏମିତି ପ୍ରତିର ପୁନରାବୃତ୍ତି ଦ୍ୱାରା, ଯାହା ସେଗୁଡ଼ିକୁ ଦୁର୍ଘଟଣାର ଅପହାରଣରୁ ଅପରିହାର୍ଯ୍ୟ କରିବ।</q></em><br>— ଥୋମାସ ଜେଫରସନ, 1791 ଛାୟା ପୁସ୍ତକାଳୟ କୋଡ୍ ଗିଥବ୍ରେ ଖୋଲା ଉତ୍ସ ହୋଇପାରେ, କିନ୍ତୁ ସମଗ୍ର ଗିଥବ୍ ସହଜରେ ଆଲୋକିତ ହୋଇପାରେ ନାହିଁ ଏବଂ ଏପରିକାରେ ସଂରକ୍ଷିତ ହୋଇପାରେ ନାହିଁ (ଯଦିଓ ଏହି ବିଶେଷ ମାମଲାରେ ଅଧିକାଂଶ କୋଡ୍ ରିପୋଜିଟୋରିର ପ୍ରଚୁର ପ୍ରତି ଅଛି) ମେଟାଡାଟା ରେକର୍ଡଗୁଡ଼ିକୁ ୱାର୍ଲ୍ଡକ୍ୟାଟ୍ ୱେବସାଇଟରେ ମାଗଣାରେ ଦେଖାଯାଇପାରେ, କିନ୍ତୁ ବ୍ୟାପକ ଭାବରେ ଡାଉନଲୋଡ୍ କରାଯାଇପାରେ ନାହିଁ (ଯାଏପର୍ଯ୍ୟନ୍ତ ଆମେ <a %(worldcat_scrape)s>ସ୍କ୍ରାପ୍</a> କରିନଥିଲୁ) ରେଡିଟ୍ ମାଗଣାରେ ବ୍ୟବହାର କରାଯାଇପାରେ, କିନ୍ତୁ ଏବେ ତଥ୍ୟ-ତୃଷ୍ଣା ଏଲଏଲଏମ୍ ପ୍ରଶିକ୍ଷଣ ପରେ, ସ୍କ୍ରାପିଂ ବିରୋଧରେ କଠୋର ପଦକ୍ଷେପ ନେଇଛି (ଏହା ବିଷୟରେ ପରେ ଅଧିକ ଆଲୋଚନା କରାଯିବ) ଅନେକ ସଂଗଠନ ଅଛନ୍ତି ଯାହାର ସମାନ ମିଶନ ଏବଂ ସମାନ ପ୍ରାଥମିକତା ଅଛି। ପ୍ରକୃତରେ, ଏମିତି ପୁସ୍ତକାଳୟ, ଆର୍କାଇଭ୍, ଲାବ୍, ସଂଗ୍ରହାଳୟ ଏବଂ ଅନ୍ୟ ସଂସ୍ଥାମାନେ ଏହି ପ୍ରକାରର ସଂରକ୍ଷଣର ଦାୟିତ୍ୱ ନେଇଛନ୍ତି। ସେମାନଙ୍କ ମଧ୍ୟରୁ ଅନେକ ଭଲ ଭାବରେ ଅର୍ଥାନୁଦାନ ପାଇଛନ୍ତି, ସରକାର, ବ୍ୟକ୍ତିଗତ କିମ୍ବା କର୍ପୋରେସନ ଦ୍ୱାରା। କିନ୍ତୁ ସେମାନଙ୍କର ଗୋଟିଏ ବଡ଼ ଅନ୍ଧା ଦୃଷ୍ଟି ଅଛି: ଆଇନ ରାଜ୍ୟ। ଏଠାରେ ଛାୟା ପୁସ୍ତକାଳୟର ବିଶିଷ୍ଟ ଭୂମିକା ରହିଛି, ଏବଂ ଆନାର ଆର୍କାଇଭ୍ ରହିବାର କାରଣ। ଆମେ ଏହା କରିପାରିବା ଯାହା ଅନ୍ୟ ସଂସ୍ଥାଗୁଡ଼ିକ କରିବାକୁ ଅନୁମତି ନାହିଁ। ଏବେ, ଏହା ନୁହେଁ (ପ୍ରାୟତଃ) ଯେ ଆମେ ଅନ୍ୟ ସ୍ଥାନରେ ସଂରକ୍ଷଣ କରିବାକୁ ଅବୈଧ ଥିବା ସାମଗ୍ରୀଗୁଡ଼ିକୁ ଆର୍କାଇଭ୍ କରିପାରିବା। ନାହିଁ, ଅନେକ ସ୍ଥାନରେ ଯେକୌଣସି ପୁସ୍ତକ, ପତ୍ର, ପତ୍ରିକା ଇତ୍ୟାଦି ସହିତ ଏକ ଆର୍କାଇଭ୍ ତିଆରି କରିବା ବୈଧ। କିନ୍ତୁ ଆଇନଗତ ଆର୍କାଇଭ୍ ମାନଙ୍କର ଅନେକ ସମୟରେ ଯାହା ଅଭାବ ଥାଏ ସେହି ହେଉଛି <strong>ପୁନରାବୃତ୍ତି ଏବଂ ଦୀର୍ଘାୟୁ</strong>। କେତେକ ପୁସ୍ତକ ଅଛି ଯାହାର କେବଳ ଗୋଟିଏ ପ୍ରତି ଅଛି କୌଣସି ଶାରୀରିକ ପୁସ୍ତକାଳୟରେ। କେତେକ ମେଟାଡାଟା ରେକର୍ଡ ଅଛି ଯାହାକୁ ଗୋଟିଏ କର୍ପୋରେସନ ରକ୍ଷା କରୁଛି। କେତେକ ସମାଚାରପତ୍ର ଅଛି ଯାହାକୁ କେବଳ ଗୋଟିଏ ଆର୍କାଇଭ୍ରେ ମାଇକ୍ରୋଫିଲ୍ମରେ ସଂରକ୍ଷିତ କରାଯାଇଛି। ପୁସ୍ତକାଳୟମାନେ ଅର୍ଥ ପାଇବାରେ କମି ଯାଇପାରନ୍ତି, କର୍ପୋରେସନ ଦିବାଳିଆ ହୋଇପାରନ୍ତି, ଆର୍କାଇଭ୍ ଉଡ଼ାଇ ଦିଆଯାଇପାରନ୍ତି ଏବଂ ଜଳି ଯାଇପାରନ୍ତି। ଏହା କଳ୍ପନା ନୁହେଁ — ଏହା ସବୁବେଳେ ଘଟେ। ଆମେ ଅନ୍ୟାନ୍ୟ ଯାହା କରିପାରିବା ନାହିଁ, ଆମେ ଆନ୍ନାର ଆର୍କାଇଭ୍ରେ ଅନେକ ପ୍ରତି ସଂରକ୍ଷଣ କରିପାରିବା ହେଉଛି। ଆମେ ପେପର, ପୁସ୍ତକ, ପତ୍ରିକା ଏବଂ ଅଧିକ ସଂଗ୍ରହ କରିପାରିବା ଏବଂ ସେଗୁଡ଼ିକୁ ବ୍ୟାପକ ଭାବରେ ବଣ୍ଟନ କରିପାରିବା। ଆମେ ବର୍ତ୍ତମାନ ଏହାକୁ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ କରୁଛୁ, କିନ୍ତୁ ନିଜେ ଯାହା ତଥ୍ୟ ନୁହେଁ ଏବଂ ସମୟ ସହିତ ପରିବର୍ତ୍ତିତ ହେବ। ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅଂଶ ହେଉଛି ବିଶ୍ୱରେ ଅନେକ ପ୍ରତି ବଣ୍ଟନ କରିବା। 200 ବର୍ଷ ପୂର୍ବରୁ ଏହି ଉଦ୍ଧୃତି ଏବେ ବି ଏକାକାର ହେଉଛି: ସାର୍ବଜନିକ ଡୋମେନ ବିଷୟରେ ଏକ ତ୍ୱରିତ ଟିପ୍ପଣୀ। ଯେହେତୁ ଆନ୍ନାର ଆର୍କାଇଭ୍ ବିଶିଷ୍ଟ ଭାବରେ ବିଶ୍ୱର ଅନେକ ସ୍ଥାନରେ ଅବୈଧ କାର୍ଯ୍ୟକଳାପରେ ଧ୍ୟାନ ଦେଇଥାଏ, ଆମେ ସାର୍ବଜନିକ ଡୋମେନ ପୁସ୍ତକ ଭଳି ବ୍ୟାପକ ଭାବରେ ଉପଲବ୍ଧ ସଂଗ୍ରହ ସହିତ ଯତ୍ନ କରିବାକୁ ଚେଷ୍ଟା କରୁନାହିଁ। ଆଇନଗତ ସଂସ୍ଥାମାନେ ସେଥିରେ ଭଲ ଯତ୍ନ ନେଇଥାନ୍ତି। ତଥାପି, କେତେକ ବିଚାର ଅଛି ଯାହା ଆମକୁ କେବେ କେବେ ସାର୍ବଜନିକ ଭାବରେ ଉପଲବ୍ଧ ସଂଗ୍ରହରେ କାମ କରିବାକୁ ପ୍ରେରିତ କରେ: - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. ସଂଗ୍ରହଣ ଖର୍ଚ୍ଚ ଦ୍ରୁତ ହାରରେ କମିବାକୁ ଚାଲିଛି 3. ସୂଚନା ଘନତାରେ ସୁଧାର ଆମେ ବର୍ତ୍ତମାନ ପୁସ୍ତକଗୁଡ଼ିକୁ ସେମାନଙ୍କ ଦ୍ୱାରା ଦିଆଯାଇଥିବା କଚ୍ଚା ଫର୍ମାଟରେ ସଂରକ୍ଷଣ କରୁଛୁ। ନିଶ୍ଚିତ ଭାବେ, ସେଗୁଡ଼ିକ ସଂକୋଚିତ ହୋଇଛି, କିନ୍ତୁ ପ୍ରାୟତଃ ସେଗୁଡ଼ିକ ଏବେ ବି ବଡ଼ ସ୍କାନ କିମ୍ବା ପୃଷ୍ଠାର ଫଟୋ ଅଟେ। ଏପର୍ଯ୍ୟନ୍ତ, ଆମର ସଂଗ୍ରହର ମୋଟ ଆକାର କମାଇବା ପାଇଁ ଏକମାତ୍ର ବିକଳ୍ପ ହେଉଛି ଅଧିକ ଆକ୍ରାମକ ସଂକୋଚନ କିମ୍ବା ଡିଡ୍ୟୁପ୍ଲିକେସନ ମାଧ୍ୟମରେ। ତେବେ, ପ୍ରମାଣପତ୍ର ମିଳିବା ପାଇଁ, ଦୁଇଟି ଆମର ପସନ୍ଦ ପାଇଁ ଅତ୍ୟଧିକ ହାନିକାରକ। ଫଟୋର ଭାରୀ ସଂକୋଚନ ଟେକ୍ସଟକୁ କ୍ଷୁଦ୍ର ପଠନୀୟ କରିପାରେ। ଏବଂ ଡିଡ୍ୟୁପ୍ଲିକେସନ ପୁସ୍ତକଗୁଡ଼ିକ ସଠିକ୍ ଭାବରେ ସମାନ ଥିବାର ଉଚ୍ଚ ବିଶ୍ୱାସ ଆବଶ୍ୟକ କରେ, ଯାହା ପ୍ରାୟତଃ ଅତ୍ୟଧିକ ଅସଂଗତ, ବିଶେଷକରି ଯଦି ବିଷୟବସ୍ତୁ ସମାନ ହେଲେ କିନ୍ତୁ ସ୍କାନ ଭିନ୍ନ ସମୟରେ କରାଯାଇଥାଏ। ସଦା ଥରେ ଏକ ତୃତୀୟ ବିକଳ୍ପ ଥିଲା, କିନ୍ତୁ ଏହାର ଗୁଣବତ୍ତା ଏତେ ଖରାପ ଥିଲା ଯେ ଆମେ କେବେ ଏହାକୁ ବିଚାର କରିନଥିଲୁ: <strong>OCR, କିମ୍ବା ଅପ୍ଟିକାଲ୍ କ୍ୟାରାକ୍ଟର ରିକଗ୍ନିସନ୍</strong>। ଏହା ଫଟୋଗୁଡ଼ିକୁ ସାଧାରଣ ଟେକ୍ସଟ୍ ରେ ପରିବର୍ତ୍ତନ କରିବା ପ୍ରକ୍ରିୟା, ଏହାରେ AI ଫଟୋରେ ଅକ୍ଷରଗୁଡ଼ିକ ଚିହ୍ନଟ କରିବାକୁ ବ୍ୟବହାର କରାଯାଏ। ଏହା ପାଇଁ ଉପକରଣଗୁଡ଼ିକ ଦୀର୍ଘ ସମୟ ଧରି ଅବସ୍ଥିତ ଅଛି, ଏବଂ ବହୁତ ଭଲ ଥିଲା, କିନ୍ତୁ “ବହୁତ ଭଲ” ସଂରକ୍ଷଣ ଉଦ୍ଦେଶ୍ୟ ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ନୁହଁ। ଏହାସହିତ, ସମ୍ପ୍ରତି ମଲ୍ଟି-ମୋଡାଲ୍ ଡିପ୍-ଲର୍ଣ୍ଣିଂ ମଡେଲଗୁଡ଼ିକ ବହୁତ ତ୍ୱରିତ ଅଗ୍ରଗତି କରିଛି, ଯଦିଓ ଏହା ଅଧିକ ଖର୍ଚ୍ଚରେ ଅଛି। ଆମେ ଆଶା କରୁଛୁ ଯେ ଆଗାମୀ ବର୍ଷଗୁଡ଼ିକରେ ଦୁଇଟି ଉପରେ ଅଧିକ ନିକଟତା ଏବଂ ଖର୍ଚ୍ଚରେ ଅଧିକ ଉନ୍ନତି ହେବ, ଯାହା ଆମର ସମଗ୍ର ଲାଇବ୍ରେରୀକୁ ଆବେଦନ କରିବାକୁ ବାସ୍ତବବାଦୀ ହେବ। ଯେତେବେଳେ ଏହା ଘଟିବ, ଆମେ ସମ୍ଭବତଃ ଅସଲି ଫାଇଲଗୁଡ଼ିକୁ ସଂରକ୍ଷଣ କରିବାକୁ ଚାହିଁବୁ, କିନ୍ତୁ ଏହା ସହିତ ଆମର ଲାଇବ୍ରେରୀର ଏକ ବହୁତ ଛୋଟ ଭାର୍ସନ୍ ଥାଇପାରେ ଯାହା ଅଧିକାଂଶ ଲୋକ ମିରର୍ କରିବାକୁ ଚାହିଁବେ। ର ଅନ୍ତର୍ଭାଗ ହେଉଛି ଯେ କଚା ଟେକ୍ସଟ୍ ନିଜେ ଅଧିକ ଭଲ ଭାବରେ ସଂକୋଚିତ ହୁଏ, ଏବଂ ଅଧିକ ସହଜରେ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ ହୁଏ, ଯାହା ଆମକୁ ଅଧିକ ସଂରକ୍ଷଣ ଦେଇଥାଏ। ସମଗ୍ର ଭାବରେ ଏହା ଅନ୍ୟୁନ 5-10x ମୋଟ ଫାଇଲ ଆକାରରେ କମିବାକୁ ଆଶା କରିବା ଅବାସ୍ତବ ନୁହଁ, ସମ୍ଭବତଃ ତାଠାରୁ ଅଧିକ। ଏକ ସଂରକ୍ଷଣାତ୍ମକ 5x କମିବା ସହିତ, ଆମେ ଦେଖୁଥିବା <strong>$1,000–$3,000 10 ବର୍ଷରେ ଯଦି ଆମର ଲାଇବ୍ରେରୀ ଆକାରରେ ତିନିଗୁଣା ହୁଏ</strong>। ଲେଖା ସମୟରେ, <a %(diskprices)s>ଡିସ୍କ ମୂଲ୍ୟ</a> ପ୍ରତି TB ନୂତନ ଡିସ୍କ ପାଇଁ ପ୍ରାୟ $12, ବ୍ୟବହୃତ ଡିସ୍କ ପାଇଁ $8, ଏବଂ ଟେପ୍ ପାଇଁ $4 ଅଟେ। ଯଦି ଆମେ ସଂରକ୍ଷଣଶୀଳ ହେବା ଏବଂ କେବଳ ନୂତନ ଡିସ୍କ ଦେଖିବା, ତାହେଲେ ଏକ ପେଟାବାଇଟ୍ ସଂଗ୍ରହ କରିବାର ମୂଲ୍ୟ ପ୍ରାୟ $12,000 ହେବ। ଯଦି ଆମେ ଧାରଣା କରୁ ଯେ ଆମର ଲାଇବ୍ରେରୀ 900TB ରୁ 2.7PB ହେବ, ତାହେଲେ ଆମର ସମଗ୍ର ଲାଇବ୍ରେରୀକୁ ମିରର୍ କରିବାର ମୂଲ୍ୟ $32,400 ହେବ। ବିଦ୍ୟୁତ, ଅନ୍ୟ ହାର୍ଡୱେର୍ ମୂଲ୍ୟ ଇତ୍ୟାଦି ଯୋଗ କରିବା, ଆମେ ଏହାକୁ $40,000 କରିବାକୁ ଗୋଲାକାର କରିବା। କିମ୍ବା ଟେପ୍ ସହିତ ଅଧିକ $15,000–$20,000 ହେବ। ଏକ ପକ୍ଷରେ <strong>ମାନବ ଜ୍ଞାନର ସମସ୍ତ ମୂଲ୍ୟ ପାଇଁ $15,000–$40,000 ଏକ ଚୋରା ଅଟେ</strong>। ଅନ୍ୟ ପକ୍ଷରେ, ଏହା ଅନ୍ୟମାନଙ୍କ ପାଇଁ ଏହାର ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ଧାରାବାହିକ ରଖିବାକୁ ଆମେ ଚାହୁଁଥିବା ବେଳେ ପୂର୍ଣ୍ଣ ପ୍ରତିଲିପିଗୁଡ଼ିକ ଆଶା କରିବାକୁ ଏକ ଅଳ୍ପ ଅଧିକ ଅଟେ। ଏହା ଆଜି। କିନ୍ତୁ ଅଗ୍ରଗତି ଆଗକୁ ଚାଲିଛି: ପିଛଲା 10 ବର୍ଷରେ ହାର୍ଡ ଡ୍ରାଇଭ୍ ମୂଲ୍ୟ ପ୍ରତି TB ଏକ ତୃତୀୟ ହାରରେ କମିଛି, ଏବଂ ସମାନ ହାରରେ କମିବାକୁ ଚାଲିଛି। ଟେପ୍ ଏକ ସମାନ ପଥରେ ଅଛି। SSD ମୂଲ୍ୟ ଅଧିକ ଦ୍ରୁତ ହାରରେ କମିଛି, ଏବଂ ଏହା ଦଶକର ଶେଷରେ HDD ମୂଲ୍ୟକୁ ଅତିକ୍ରମ କରିପାରେ। ଯଦି ଏହା ଧରିବ, ତାହେଲେ 10 ବର୍ଷରେ ଆମେ ମାତ୍ର $5,000–$13,000 ରେ ଆମ ସମଗ୍ର ସଂଗ୍ରହକୁ (1/3ର୍ଡ) ମିରର୍ କରିବାକୁ ଦେଖିପାରିବା, କିମ୍ବା ଆମେ ଆକାରରେ କମିବାକୁ ଚାହୁଁଥିଲେ ଏହା ଅଧିକ କମ୍ ହେବ। ଯଦିଓ ଏହା ଅନେକ ଟଙ୍କା, ଏହା ଅନେକ ଲୋକଙ୍କ ପାଇଁ ପ୍ରାପ୍ୟ ହେବ। ଏବଂ ଏହା ଅଧିକ ଭଲ ହୋଇପାରେ କାରଣ ଆଗାମୀ ବିନ୍ଦୁରେ… ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଜାନ୍ଲା ଯଦି ଏହି ପୂର୍ବାନୁମାନଗୁଡ଼ିକ ସଠିକ୍ ହୁଏ, ଆମେ <strong>କେବଳ କିଛି ବର୍ଷ ଅପେକ୍ଷା କରିବାକୁ ପଡ଼ିବ</strong> ପୂର୍ଣ୍ଣ ସଂଗ୍ରହ ବ୍ୟାପକ ଭାବରେ ମିରର୍ ହେବା ପୂର୍ବରୁ। ଏହିପରି, ଥୋମାସ ଜେଫରସନଙ୍କ କଥାରେ, “ଦୁର୍ଘଟଣାର ଅଭିଗମ୍ୟତାରୁ ଦୂରେ ରଖାଯାଇଛି।” ଦୁର୍ଭାଗ୍ୟବଶତଃ, LLMs ର ଆଗମନ, ଏବଂ ସେମାନଙ୍କର ଡାଟା-ଭୁକ୍ତ ଶିକ୍ଷା, ବହୁତ ଅଧିକ କପିରାଇଟ୍ ଧାରକମାନଙ୍କୁ ରକ୍ଷାରେ ରଖିଛି। ସେମାନେ ଆଗରୁ ଯେତେ ଥିଲେ ତାଠାରୁ ଅଧିକ। ଅନେକ ୱେବସାଇଟ୍ ସଂଗ୍ରହ କରିବା ଏବଂ ଆର୍କାଇଭ୍ କରିବାକୁ କଠିନ କରୁଛନ୍ତି, ମାମଲାଗୁଡ଼ିକ ଚାଲିଛି, ଏବଂ ସମସ୍ତ ସମୟରେ ଭୌତିକ ଲାଇବ୍ରେରୀ ଏବଂ ଆର୍କାଇଭ୍ ଅବହେଳିତ ହେଉଛି। ଆମେ କେବଳ ଆଶା କରିପାରିବା ଯେ ଏହି ପ୍ରବୃତ୍ତିଗୁଡ଼ିକ ଅଧିକ ଖରାପ ହେବାକୁ ଚାଲିବ, ଏବଂ ଅନେକ କାର୍ଯ୍ୟ ସାଧାରଣ ଡୋମେନରେ ପ୍ରବେଶ କରିବା ପୂର୍ବରୁ ହରାଇଯିବ। <strong>ଆମେ ସଂରକ୍ଷଣରେ ବିପ୍ଲବର ଆଗରେ ଅଛୁ, କିନ୍ତୁ <q>ହରାଇଯାଇଥିବାକୁ ପୁନଃପ୍ରାପ୍ତ କରାଯାଇପାରିବ ନାହିଁ।</q></strong> ଆମର ପାଖରେ ପ୍ରାୟ 5-10 ବର୍ଷର ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଜାନ୍ଲା ଅଛି ଯାହାରେ ଏକ ଛାୟା ଲାଇବ୍ରେରୀ ଚାଲାଇବା ଏବଂ ସାରା ପୃଥିବୀରେ ଅନେକ ମିରର୍ ସୃଷ୍ଟି କରିବା ଏବେ ମଧ୍ୟ ଅଧିକ ଖର୍ଚ୍ଚ ସମ୍ପର୍କିତ ଅଛି, ଏବଂ ଯାହାରେ ପ୍ରବେଶ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ବନ୍ଦ ହୋଇଯାଇନାହିଁ। ଯଦି ଆମେ ଏହି ଜାନ୍ଲାକୁ ସେତୁ କରିପାରିବା, ତେବେ ଆମେ ନିଶ୍ଚିତ ଭାବରେ ମାନବତାର ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ଚିରକାଳ ପାଇଁ ସଂରକ୍ଷଣ କରିଛୁ। ଆମେ ଏହି ସମୟକୁ ବ୍ୟର୍ଥ କରିବା ଉଚିତ୍ ନୁହଁ। ଆମେ ଏହି ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଜାନ୍ଲାକୁ ଆମ ଉପରେ ବନ୍ଦ ହେବାକୁ ଦେବା ଉଚିତ୍ ନୁହଁ। ଆସନ୍ତୁ ଯାଉ। ସଂଗ୍ରହ ସଂଗ୍ରହ ବିଷୟରେ କିଛି ଅଧିକ ସୂଚନା। <a %(duxiu)s>Duxiu</a> ହେଉଛି ସ୍କାନ କରାଯାଇଥିବା ପୁସ୍ତକମାନଙ୍କର ଏକ ବିଶାଳ ତଥ୍ୟାଧାର, ଯାହା <a %(chaoxing)s>SuperStar Digital Library Group</a> ଦ୍ୱାରା ସୃଷ୍ଟିତ। ଅଧିକାଂଶ ହେଉଛି ଶାସ୍ତ୍ରୀୟ ପୁସ୍ତକ, ଯାହାକୁ ବିଶ୍ୱବିଦ୍ୟାଳୟ ଏବଂ ପୁସ୍ତକାଳୟମାନଙ୍କୁ ଡିଜିଟାଲ ଭାବରେ ଉପଲବ୍ଧ କରାଇବା ପାଇଁ ସ୍କାନ କରାଯାଇଛି। ଆମର ଇଂରାଜୀ-ଭାଷୀ ପ୍ରେକ୍ଷାପଟ ପାଇଁ, <a %(library_princeton)s>Princeton</a> ଏବଂ <a %(guides_lib_uw)s>University of Washington</a> ଭଲ ସମୀକ୍ଷା ରଖିଛନ୍ତି। ଏକ ଅତ୍ୟନ୍ତ ଉତ୍କୃଷ୍ଟ ଲେଖା ମଧ୍ୟ ଅଛି ଯାହା ଅଧିକ ପୃଷ୍ଠଭୂମି ଦେଇଥାଏ: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (ଆନାର ଅଭିଲେଖାଗାରରେ ଏହାକୁ ଖୋଜନ୍ତୁ)। Duxiu ର ପୁସ୍ତକଗୁଡ଼ିକ ଚୀନା ଇଣ୍ଟରନେଟରେ ଦୀର୍ଘ ସମୟ ଧରି ଅନୁମତି ବିନା ବଣିଜ୍ୟ ହୋଇଛି। ସାଧାରଣତଃ ସେଗୁଡ଼ିକ ପୁନର୍ବିକ୍ରେତାମାନଙ୍କ ଦ୍ୱାରା ଏକ ଡଲାରରୁ କମ୍ ମୂଲ୍ୟରେ ବିକ୍ରି ହେଉଛି। ସାଧାରଣତଃ ସେଗୁଡ଼ିକ ଗୁଗୁଲ ଡ୍ରାଇଭର ଚୀନା ସମାନର ଉପଯୋଗ କରି ବଣ୍ଟନ କରାଯାଉଛି, ଯାହାକୁ ପ୍ରାୟତଃ ଅଧିକ ସଂଗ୍ରହ ସ୍ଥାନ ପାଇଁ ହ୍ୟାକ୍ କରାଯାଇଛି। କିଛି ପ୍ରାକ୍ରିତିକ ବିବରଣୀ <a %(github_duty_machine)s>ଏଠାରେ</a> ଏବଂ <a %(github_821_github_io)s>ଏଠାରେ</a> ମିଳିବ। ଯଦିଓ ପୁସ୍ତକଗୁଡ଼ିକ ଅର୍ଦ୍ଧ-ସାର୍ବଜନିକ ଭାବରେ ବଣ୍ଟନ କରାଯାଇଛି, ସେଗୁଡ଼ିକୁ ବଲ୍କରେ ପାଇବା ଅନେକ କଠିନ। ଆମେ ଏହାକୁ ଆମର TODO-ସୂଚୀରେ ଉଚ୍ଚ ସ୍ଥାନରେ ରଖିଥିଲୁ, ଏବଂ ଏହା ପାଇଁ ପୂର୍ଣ୍ଣ ସମୟର କାମ ପାଇଁ ଅନେକ ମାସ ଆବଣ୍ଟନ କରିଥିଲୁ। ତଥାପି, ଆଖିରେ ଏକ ଅବିଶ୍ୱସନୀୟ, ଅଦ୍ଭୁତ, ଏବଂ ପ୍ରତିଭାଶାଳୀ ସେବକ ଆମ ସହିତ ଯୋଗାଯୋଗ କରିଥିଲେ, ତାଙ୍କର କହିବା ଯେ ସେମାନେ ଏହାର ସମସ୍ତ କାମ ଆଗରୁ କରିଛନ୍ତି — ବହୁତ ବ୍ୟୟରେ। ସେମାନେ ଆମ ସହିତ ସମଗ୍ର ସଂଗ୍ରହକୁ ଅଂଶୀଦାର କରିଥିଲେ, ବଦଳରେ କିଛି ଆଶା କରିନଥିଲେ, ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣର ଆଶ୍ୱାସନ ବ୍ୟତୀତ। ସତ୍ୟରେ ଅଦ୍ଭୁତ। ସେମାନେ ସଂଗ୍ରହକୁ OCR କରିବା ପାଇଁ ଏହି ପ୍ରକାର ସାହାଯ୍ୟ ଚାହିଁବାକୁ ସମ୍ମତ ହୋଇଥିଲେ। ସଂଗ୍ରହଟି 7,543,702 ଫାଇଲ୍ ଅଟେ। ଏହା Library Genesis ଅପ୍ରାକୃତିକ (ପ୍ରାୟ 5.3 ମିଲିୟନ୍) ଠାରୁ ଅଧିକ। ମୋଟ ଫାଇଲ୍ ଆକାର ଏହାର ବର୍ତ୍ତମାନ ଆକାରରେ ପ୍ରାୟ 359TB (326TiB) ଅଟେ। ଆମେ ଅନ୍ୟ ପ୍ରସ୍ତାବ ଏବଂ ଧାରଣା ପାଇଁ ଖୋଲା ଅଛୁ। କେବଳ ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଆମ ସଂଗ୍ରହ, ସଂରକ୍ଷଣ ପ୍ରୟାସ, ଏବଂ ଆପଣ କିପରି ସାହାଯ୍ୟ କରିପାରିବେ ବିଷୟରେ ଅଧିକ ସୂଚନା ପାଇଁ ଆନାର ଆର୍କାଇଭ୍ ଦେଖନ୍ତୁ। ଧନ୍ୟବାଦ! ଉଦାହରଣ ପୃଷ୍ଠାଗୁଡ଼ିକ ଆପଣଙ୍କର ପାଇପଲାଇନ ଭଲ ଥିବାକୁ ଆମକୁ ପ୍ରମାଣ କରିବା ପାଇଁ, ଏକ ସୁପରକଣ୍ଡକ୍ଟର ଉପରେ ଏକ ପୁସ୍ତକରୁ କିଛି ଉଦାହରଣ ପୃଷ୍ଠା ଆରମ୍ଭ କରିବାକୁ ଏଠାରେ ଅଛି। ଆପଣଙ୍କର ପାଇପଲାଇନ ଠିକ ଭାବରେ ଗଣିତ, ତାଲିକା, ଚାର୍ଟ, ତଳଲିଖିତ ଟୀକା ଇତ୍ୟାଦିକୁ ସମ୍ଭାଳିବା ଉଚିତ। ଆପଣଙ୍କର ପ୍ରକ୍ରିୟାକୃତ ପୃଷ୍ଠାଗୁଡ଼ିକୁ ଆମ ଇମେଲକୁ ପଠାନ୍ତୁ। ଯଦି ସେଗୁଡ଼ିକ ଭଲ ଦେଖାଯାଏ, ଆମେ ଆପଣଙ୍କୁ ଅଧିକ ଗୋପନୀୟ ଭାବରେ ପଠାଇବୁ, ଏବଂ ଆମେ ଆପଣଙ୍କୁ ସେଗୁଡ଼ିକରେ ତ୍ୱରିତ ଭାବରେ ଆପଣଙ୍କର ପାଇପଲାଇନ ଚାଲାଇବାକୁ ସକ୍ଷମ ହେବାକୁ ଆଶା କରୁଛୁ। ଯେତେବେଳେ ଆମେ ସନ୍ତୁଷ୍ଟ ହେବୁ, ଆମେ ଏକ ଚୁକ୍ତି କରିପାରିବୁ। - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>ଚୀନା ସଂସ୍କରଣ 中文版</a>, <a %(news_ycombinator)s>Hacker News ରେ ଆଲୋଚନା କରନ୍ତୁ</a> ଏହା ଏକ ସଂକ୍ଷିପ୍ତ ବ୍ଲଗ ପୋଷ୍ଟ। ଆମେ ଏକ ବଡ଼ ସଂଗ୍ରହ ପାଇଁ OCR ଏବଂ ପାଠ୍ୟ ତତ୍ତ୍ୱାବଧାନରେ ଆମକୁ ସାହାଯ୍ୟ କରିବାକୁ କିଛି କମ୍ପାନୀ କିମ୍ବା ସଂସ୍ଥାନ ଖୋଜୁଛୁ, ବିଶେଷ ପ୍ରାରମ୍ଭିକ ପ୍ରବେଶର ବଦଳରେ। ନିଷେଧାଜ୍ଞା ସମୟ ଶେଷ ହେବା ପରେ, ନିଶ୍ଚିତ ଭାବେ ଆମେ ସମଗ୍ର ସଂଗ୍ରହକୁ ମୁକ୍ତ କରିବୁ। ଉଚ୍ଚ-ଗୁଣସ୍ତରୀୟ ଶାସ୍ତ୍ରୀୟ ପାଠ୍ୟ LLM ଗୁଡ଼ିକର ପ୍ରଶିକ୍ଷଣ ପାଇଁ ଅତ୍ୟନ୍ତ ଉପଯୋଗୀ। ଯଦିଓ ଆମ ସଂଗ୍ରହ ଚୀନା, ଏହା ଇଂରାଜୀ LLM ଗୁଡ଼ିକର ପ୍ରଶିକ୍ଷଣ ପାଇଁ ମଧ୍ୟ ଉପଯୋଗୀ ହେବା ଉଚିତ: ମଡେଲଗୁଡ଼ିକ ମୂଳ ଭାଷା ବିନାପରି ଧାରଣା ଏବଂ ଜ୍ଞାନକୁ ସଂକେତ କରିବା ପାଇଁ ପ୍ରତୀତ ହୁଏ। ଏହା ପାଇଁ, ସ୍କାନରୁ ପାଠ୍ୟକୁ ତତ୍ତ୍ୱାବଧାନ କରିବା ଆବଶ୍ୟକ। ଆନାର ଅଭିଲେଖାଗାର ଏଥିରୁ କ'ଣ ପାଉଛି? ଏହାର ବ୍ୟବହାରକାରୀମାନଙ୍କ ପାଇଁ ପୁସ୍ତକଗୁଡ଼ିକର ପୂର୍ଣ୍ଣ-ପାଠ୍ୟ ଖୋଜ। ଯେହେତୁ ଆମ ଲକ୍ଷ୍ୟଗୁଡ଼ିକ LLM ବିକାଶକାରୀମାନଙ୍କ ସହିତ ସମନ୍ୱୟ ରଖେ, ଆମେ ଏକ ସହଯୋଗୀକୁ ଖୋଜୁଛୁ। ଯଦି ଆପଣ ଠିକ OCR ଏବଂ ପାଠ୍ୟ ତତ୍ତ୍ୱାବଧାନ କରିପାରିବେ, ଆମେ ଆପଣଙ୍କୁ <strong>1 ବର୍ଷ ପାଇଁ ଏହି ସଂଗ୍ରହକୁ ବଡ଼ ପରିମାଣରେ ବିଶେଷ ପ୍ରାରମ୍ଭିକ ପ୍ରବେଶ ଦେବାକୁ ଇଚ୍ଛୁକ। ଯଦି ଆପଣ ଆପଣଙ୍କର ପାଇପଲାଇନର ସମଗ୍ର କୋଡ୍ ଆମ ସହିତ ଅଂଶୀଦାର କରିବାକୁ ଇଚ୍ଛୁକ, ଆମେ ସଂଗ୍ରହକୁ ଅଧିକ ସମୟ ପାଇଁ ନିଷେଧାଜ୍ଞା ଦେବାକୁ ଇଚ୍ଛୁକ। LLM କମ୍ପାନୀମାନଙ୍କ ପାଇଁ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଚୀନା ଅପ୍ରବନ୍ଧ ପୁସ୍ତକ ସଂଗ୍ରହକୁ ବିଶେଷ ପ୍ରବେଶ <em><strong>ସାରାଂଶ:</strong> ଆନାର ଅଭିଲେଖାଗାର 7.5 ମିଲିୟନ / 350TB ଚୀନା ଅପ୍ରବନ୍ଧ ପୁସ୍ତକର ଏକ ବିଶିଷ୍ଟ ସଂଗ୍ରହ ଅଧିଗ୍ରହଣ କରିଛି — ଯାହା Library Genesis ଠାରୁ ବଡ଼। ଆମେ ଏକ LLM କମ୍ପାନୀକୁ ଉଚ୍ଚ-ଗୁଣସ୍ତରୀୟ OCR ଏବଂ ପାଠ୍ୟ ତତ୍ତ୍ୱାବଧାନ ପାଇଁ ବିଶେଷ ପ୍ରବେଶ ଦେବାକୁ ଇଚ୍ଛୁକ।</em> ସମାପ୍ତି ଏକ ଦୃଢ଼ ଏବଂ ଦୃଢ଼ ଛାୟା ପୁସ୍ତକାଳୟ ଖୋଜ ଇଞ୍ଜିନ୍ ସେଟଅପ୍ କରିବା କିପରି ଶିଖିବା ଏକ ଆକର୍ଷଣୀୟ ଅନୁଭବ ହୋଇଛି। ପରବର୍ତ୍ତୀ ପୋଷ୍ଟଗୁଡ଼ିକରେ ଅଧିକ ତଥ୍ୟ ସେୟାର କରିବାକୁ ଅନେକ ଅଛି, ତେଣୁ ଆପଣ ଅଧିକ ଜାଣିବାକୁ ଚାହୁଁଛନ୍ତି କି ନାହିଁ ମୋତେ ଜଣାନ୍ତୁ! - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ମୁଁ <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a> ଚାଲାଉଛି, ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଖୋଲା-ସୂତ୍ର ଅଲାଭକାରୀ ସନ୍ଧାନ ଇଞ୍ଜିନ୍ <a %(wikipedia_shadow_library)s>ଛାୟା ଲାଇବ୍ରେରୀଗୁଡ଼ିକ</a> ପାଇଁ, ଯେପରି Sci-Hub, Library Genesis, ଏବଂ Z-Library। ଆମର ଲକ୍ଷ୍ୟ ହେଉଛି ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ସହଜରେ ଉପଲବ୍ଧ କରାଇବା, ଏବଂ ଶେଷରେ ଏକ ସମୁଦାୟ ଗଠନ କରିବା ଯାହା ଏକାଠି ହୋଇ ସମସ୍ତ ପୁସ୍ତକକୁ ଆର୍କାଇଭ୍ ଏବଂ ସଂରକ୍ଷଣ କରିବ। କିପରି ଏକ ଛାୟା ଲାଇବ୍ରେରୀ ଚାଲାଇବେ: ଆନାର ଆର୍କାଇଭ୍ ରେ କାର୍ଯ୍ୟକଳାପ ଛାୟା ଦାନଶୀଳତା ପାଇଁ କୌଣସି <q>AWS ନାହିଁ,</q> ତାହେଲେ ଆମେ କିପରି ଆନାର ଆର୍କାଇଭ୍ ଚାଲାଉଛୁ? ସାଧନ ଆପ୍ଲିକେସନ ସର୍ଭର: ଫ୍ଲାସ୍କ, ମାରିଆଡିବି, ଇଲାଷ୍ଟିକସର୍ଚ୍ଚ, ଡକର୍। ବିକାଶ: Gitlab, Weblate, Zulip। ସର୍ଭର ପରିଚାଳନା: Ansible, Checkmk, UFW। ଓନିଅନ୍ ସ୍ଟାଟିକ୍ ହୋଷ୍ଟିଂ: Tor, Nginx। ପ୍ରକ୍ସି ସର୍ଭର: Varnish। ଆମେ ଏହା ସମସ୍ତ କାମ ସଫଳ କରିବା ପାଇଁ କେଉଁ ସାଧନ ବ୍ୟବହାର କରୁଛୁ ତାହା ଦେଖିବା ଯାକ। ଏହା ନିରନ୍ତର ଉନ୍ନତି ହେଉଛି ଯେପରିକି ଆମେ ନୂତନ ସମସ୍ୟାରେ ପଡ଼ୁଛୁ ଏବଂ ନୂତନ ସମାଧାନ ଖୋଜୁଛୁ। କିଛି ସିଦ୍ଧାନ୍ତ ଅଛି ଯାହା ଉପରେ ଆମେ ଆଗକୁ ପଛକୁ ଯାଇଛୁ। ଗୋଟିଏ ହେଉଛି ସର୍ଭରଗୁଡ଼ିକ ମଧ୍ୟରେ ଯୋଗାଯୋଗ: ଆମେ ପୂର୍ବରୁ ଏହି ପାଇଁ Wireguard ବ୍ୟବହାର କରୁଥିଲୁ, କିନ୍ତୁ ଦେଖିଲୁ ଯେ ଏହା କେବେ କେବେ କୌଣସି ତଥ୍ୟ ପ୍ରେଷଣ ବନ୍ଦ କରିଦିଏ, କିମ୍ବା କେବଳ ଏକ ଦିଗରେ ତଥ୍ୟ ପ୍ରେଷଣ କରିଥାଏ। ଏହା ଆମେ ପରୀକ୍ଷା କରିଥିବା ବିଭିନ୍ନ Wireguard ସେଟଅପ୍‌ଗୁଡ଼ିକ ସହିତ ଘଟିଥିଲା, ଯେପରିକି <a %(github_costela_wesher)s>wesher</a> ଏବଂ <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>। ଆମେ SSH ଉପରେ ପୋର୍ଟ ଟନେଲିଂ କରିବାକୁ ଚେଷ୍ଟା କରିଥିଲୁ, autossh ଏବଂ sshuttle ବ୍ୟବହାର କରି, କିନ୍ତୁ <a %(github_sshuttle)s>ସମସ୍ୟାଗୁଡ଼ିକ ସେଠାରେ</a> ଆସିଲା (ଯଦିଓ ଏହା ମୋ ପାଇଁ ସ୍ପଷ୍ଟ ନୁହେଁ ଯେ autossh TCP-over-TCP ସମସ୍ୟାରୁ ପୀଡ଼ିତ କି ନାହିଁ — ଏହା ମୋ ପାଇଁ ଏକ ଅସ୍ଥିର ସମାଧାନ ଭାବେ ଲାଗୁଥାଏ କିନ୍ତୁ ହୋଇପାରେ ଏହା ବାସ୍ତବରେ ଠିକ୍ ଅଛି?)। ତା'ପରେ, ଆମେ ସର୍ଭରଗୁଡ଼ିକ ମଧ୍ୟରେ ପ୍ରତ୍ୟକ୍ଷ ସଂଯୋଗକୁ ପୁନଃସ୍ଥାପିତ କରିଲୁ, ଏକ ସର୍ଭର ସସ୍ତା ପ୍ରଦାନକାରୀମାନଙ୍କ ଉପରେ ଚାଲୁଛି ବୋଲି ଲୁଚାଇବାକୁ UFW ସହିତ IP-ଫିଲ୍ଟରିଂ ବ୍ୟବହାର କରି। ଏହାର ଅବନତି ହେଉଛି ଯେ Docker UFW ସହିତ ଭଲ ଭାବରେ କାମ କରେ ନାହିଁ, ଯଦିଓ ଆପଣ <code>network_mode: "host"</code> ବ୍ୟବହାର କରନ୍ତି। ଏହା ସବୁ ଅଧିକ ତ୍ରୁଟିପୂର୍ଣ୍ଣ ହୋଇଯାଏ, କାରଣ ଆପଣ ଏକ ଛୋଟ ଭୁଲ ସଂରଚନା ସହିତ ଆପଣଙ୍କ ସର୍ଭରକୁ ଇଣ୍ଟରନେଟକୁ ସମ୍ପର୍କ କରିବେ। ସମ୍ଭବତଃ ଆମେ autossh କୁ ପୁନଃସ୍ଥାପନ କରିବା ଉଚିତ — ଏଠାରେ ପ୍ରତିକ୍ରିୟା ବହୁତ ସ୍ୱାଗତ ଯୋଗ୍ୟ ହେବ। ଆମେ Varnish ବନାମ Nginx ଉପରେ ମଧ୍ୟ ଆଗକୁ ପଛକୁ ଯାଇଛୁ। ଆମେ ବର୍ତ୍ତମାନ Varnish କୁ ପସନ୍ଦ କରୁଛୁ, କିନ୍ତୁ ଏହାର କିଛି ଅସୁବିଧା ଏବଂ ଖରାପ ପାଖ ଅଛି। Checkmk ପାଇଁ ମଧ୍ୟ ଏହି ଲାଗୁ ହୁଏ: ଆମେ ଏହାକୁ ପସନ୍ଦ କରୁନାହିଁ, କିନ୍ତୁ ଏହା ବର୍ତ୍ତମାନ ପାଇଁ କାମ କରୁଛି। Weblate ଠିକ୍ ଥିଲା କିନ୍ତୁ ଅସାଧାରଣ ନୁହେଁ — ଆମେ ଯେତେବେଳେ ଏହାକୁ ଆମର git ରେପୋ ସହିତ ସମନ୍ୱୟ କରିବାକୁ ଚେଷ୍ଟା କରୁଛୁ ତା'ବେଳେ ମୋର ତଥ୍ୟ ହରାଇଯିବ ବୋଲି ମୁଁ କେବେ କେବେ ଭୟ କରେ। Flask ସାଧାରଣତଃ ଭଲ ଥିଲା, କିନ୍ତୁ ଏହାର କିଛି ଅଜ୍ଞାତ ଅସୁବିଧା ଅଛି ଯାହା ଡିବଗ୍ କରିବାକୁ ବହୁତ ସମୟ ଲାଗିଛି, ଯେପରିକି କଷ୍ଟମ ଡୋମେନ୍‌ଗୁଡ଼ିକ ସଂରଚନା କରିବା, କିମ୍ବା ଏହାର SqlAlchemy ଏକତା ସହିତ ସମସ୍ୟା। ଏପର୍ଯ୍ୟନ୍ତ ଅନ୍ୟ ଉପକରଣଗୁଡ଼ିକ ଉତ୍ତମ ଥିଲା: MariaDB, ElasticSearch, Gitlab, Zulip, Docker, ଏବଂ Tor ବିଷୟରେ ଆମର କୌଣସି ଗୁରୁତର ଅଭିଯୋଗ ନାହିଁ। ଏହାର ସବୁରେ କିଛି ସମସ୍ୟା ଥିଲା, କିନ୍ତୁ କୌଣସି ଗୁରୁତର କିମ୍ବା ସମୟ ଲାଗୁଥିବା ନୁହେଁ। କିପରି ଏକ ପାଇରେଟ୍ ଆର୍କାଇଭିଷ୍ଟ ହେବା ପ୍ରଥମ ଚ୍ୟାଲେଞ୍ଜ୍ ହୋଇପାରେ ଏକ ଆଶ୍ଚର୍ଯ୍ୟଜନକ। ଏହା ଏକ ପ୍ରାକୃତିକ ସମସ୍ୟା ନୁହେଁ, କିମ୍ବା ଏକ ଆଇନଗତ ସମସ୍ୟା ନୁହେଁ। ଏହା ଏକ ମନୋବୃତ୍ତିକ ଅସୁବିଧା। ବ୍ଲଗ୍ ପୋଷ୍ଟଗୁଡ଼ିକ ନମସ୍କାର, ମୁଁ ଅନ୍ନା। ମୁଁ <a %(wikipedia_annas_archive)s>ଅନ୍ନାଙ୍କ ଅଭିଲେଖ</a> ସୃଷ୍ଟି କରିଛି, ଯାହା ଜଗତର ସବୁଠାରୁ ବଡ଼ ଛାୟା ପୁସ୍ତକାଳୟ। ଏହା ମୋର ବ୍ୟକ୍ତିଗତ ବ୍ଲଗ୍, ଯେଉଁଥିରେ ମୁଁ ଏବଂ ମୋର ସହଯୋଗୀମାନେ ଚୋରି, ଡିଜିଟାଲ ସଂରକ୍ଷଣ, ଏବଂ ଅଧିକ ବିଷୟରେ ଲେଖିଥାଉଛୁ। <a %(reddit)s>Reddit</a> ରେ ମୋ ସହିତ ଯୋଡ଼ିଯାନ୍ତୁ। ଏହି ୱେବସାଇଟ୍ କେବଳ ଏକ ବ୍ଲଗ୍ ମାତ୍ର। ଆମେ କେବଳ ଆମର ନିଜସ୍ୱ ଶବ୍ଦଗୁଡ଼ିକୁ ଏଠାରେ ହୋଷ୍ଟ କରୁଛୁ। କୌଣସି ଟୋରେଣ୍ଟ କିମ୍ବା ଅନ୍ୟ କୌଣସି ସ୍ୱାମୀତ୍ୱ ଅଧିକାର ରଖିଥିବା ଫାଇଲ୍ ଏଠାରେ ହୋଷ୍ଟ କରାଯାଇନାହିଁ କିମ୍ବା ଲିଙ୍କ କରାଯାଇନାହିଁ। <em>ଆମେ ଏହି ବ୍ଲଗ୍ ରୁ ଫାଇଲଗୁଡ଼ିକୁ ଲିଙ୍କ କରୁନାହିଁ। ଦୟାକରି ଏହାକୁ ନିଜେ ଖୋଜନ୍ତୁ।</em> - ଆନା ଏବଂ ଦଳ (<a %(reddit)s>Reddit</a>) ଅବୈଧ ପୁସ୍ତକ ସଂଗ୍ରହକୁ ବଡ଼ ପରିମାଣରେ ପ୍ରବେଶ କରିବା ପାଇଁ ଅର୍ଥ ଚାର୍ଜ କରିବା ବିଷୟରେ ଆମେ ନୈତିକ ମତାମତ ଦେଇନାହିଁ। ଏହା ନିଶ୍ଚିତ ଯେ Z-ଲାଇବ୍ରେରୀ ଜ୍ଞାନର ପ୍ରବେଶକୁ ବିସ୍ତାର କରିବାରେ ଏବଂ ଅଧିକ ପୁସ୍ତକ ସଂଗ୍ରହ କରିବାରେ ସଫଳ ହୋଇଛି। ଆମେ କେବଳ ଆମର ଭାଗ କରିବାକୁ ଏଠାରେ ଅଛୁ: ଏହି ବ୍ୟକ୍ତିଗତ ସଂଗ୍ରହର ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣ ସୁନିଶ୍ଚିତ କରିବା। ଆମେ ଆପଣଙ୍କୁ ଆମର ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ଡାଉନଲୋଡ୍ କରିବା ଏବଂ ସିଡ୍ କରିବା ଦ୍ୱାରା ମାନବ ଜ୍ଞାନକୁ ସଂରକ୍ଷଣ ଏବଂ ମୁକ୍ତ କରିବାରେ ସାହାଯ୍ୟ କରିବାକୁ ଆମନ୍ତ୍ରଣ କରିବାକୁ ଚାହୁଁଛୁ। ତଥ୍ୟଗୁଡ଼ିକ କିପରି ସଂଗଠିତ ହୋଇଛି ବିଷୟରେ ଅଧିକ ତଥ୍ୟ ପାଇଁ ପ୍ରକଳ୍ପ ପୃଷ୍ଠା ଦେଖନ୍ତୁ। ଆମେ ଆପଣଙ୍କୁ ଆଗାମୀରେ କେଉଁ ସଂଗ୍ରହଗୁଡ଼ିକୁ ମିରର୍ କରିବା ଏବଂ କିପରି ଏହାକୁ କରିବା ଯାଇପାରେ ବିଷୟରେ ଆପଣଙ୍କର ଧାରଣାଗୁଡ଼ିକ ଅବଦାନ ଦେବାକୁ ବହୁତ ଆମନ୍ତ୍ରଣ କରୁଛୁ। ଆମେ ସମ୍ମିଳିତ ଭାବେ ବହୁତ କିଛି ସାଧନ କରିପାରିବା। ଏହା ଅନେକ ଅନ୍ୟାନ୍ୟ ମଧ୍ୟରେ ଏକ ଛୋଟ ଅବଦାନ ମାତ୍ର। ଆପଣଙ୍କର ସମସ୍ତ କାର୍ଯ୍ୟ ପାଇଁ ଧନ୍ୟବାଦ। 10% o ମାନବତାର ଲିଖିତ ଐତିହ୍ୟ ସଦାକାଳ ପାଇଁ ସଂରକ୍ଷିତ ପାଇରେଟ୍ ଲାଇବ୍ରେରୀ ମିରର୍ ସହିତ (ସମ୍ପାଦନା: <a %(wikipedia_annas_archive)s>ଆନାର ଆର୍କାଇଭ୍</a> କୁ ସ୍ଥାନାନ୍ତରିତ), ଆମର ଲକ୍ଷ୍ୟ ସମସ୍ତ ପୁସ୍ତକକୁ ସଂରକ୍ଷଣ କରିବା ଏବଂ ସଦାକାଳ ପାଇଁ ସଂରକ୍ଷିତ କରିବା।<sup>1</sup> ଆମର Z-Library ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଏବଂ ମୂଳ Library Genesis ଟୋରେଣ୍ଟଗୁଡ଼ିକ ମଧ୍ୟରେ, ଆମେ 11,783,153 ଫାଇଲ ରଖିଛୁ। କିନ୍ତୁ ଏହା ଆସଲେ କେତେ? ଯଦି ଆମେ ସେହି ଫାଇଲଗୁଡ଼ିକୁ ଠିକ ଭାବରେ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ କରିଥାନ୍ତା, ତେବେ ସମସ୍ତ ପୁସ୍ତକର ମଧ୍ୟରୁ କେତେ ପ୍ରତିଶତ ଆମେ ସଂରକ୍ଷଣ କରିଛୁ? ଆମେ ଏକ ଏପରି କିଛି ରଖିବାକୁ ଆଶା କରୁଛୁ: କିଛି ମୋଟାମୋଟି ସଂଖ୍ୟାରୁ ଆରମ୍ଭ କରିବା ଯାଉ: Z-Library/Libgen ଏବଂ Open Library ଦୁଇଟିରେ ଅନେକ ଅଧିକ ପୁସ୍ତକ ଅଛି ଯାହା ଅନନ୍ୟ ISBN ଠାରୁ ଅଧିକ। ଏହାର ଅର୍ଥ ହେଉଛି ଯେ ଅନେକ ପୁସ୍ତକର ISBN ନାହିଁ, କିମ୍ବା ISBN ମେଟାଡାଟା ସାଧାରଣତଃ ନାହିଁ? ଆମେ ସମ୍ଭବତଃ ଏହି ପ୍ରଶ୍ନକୁ ଅନ୍ୟ ଗୁଣ (ଶିରୋନାମା, ଲେଖକ, ପ୍ରକାଶକ, ଇତ୍ୟାଦି) ଉପରେ ଆଧାର କରି ସ୍ୱୟଂଚାଳିତ ମେଳକରଣ, ଅଧିକ ତଥ୍ୟ ଉତ୍ସଗୁଡ଼ିକୁ ଆଣିବା, ଏବଂ ପ୍ରକୃତ ପୁସ୍ତକ ସ୍କାନରୁ ISBN ଗୁଡ଼ିକୁ ଉତ୍ପାଦନ କରି ଉତ୍ତର ଦେଇପାରିବା (Z-Library/Libgen ମାମଲାରେ)। ସେହି ISBN ଗୁଡ଼ିକରୁ କେତେ ଅନନ୍ୟ? ଏହାକୁ ଭଲ ଭାବରେ ଏକ Venn ଚିତ୍ରରେ ଦର୍ଶାଯାଇଛି: ଅଧିକ ସଠିକ ହେବାକୁ: ଆମେ ଏହା ଦେଖି ଆଶ୍ଚର୍ଯ୍ୟ ହେଲୁ ଯେ କିଛି ଅତ୍ୟଳ୍ପ ଓଭରଲାପ୍ ଅଛି! ISBNdb ରେ ଏକ ବିଶାଳ ପରିମାଣର ISBN ଅଛି ଯାହା Z-Library କିମ୍ବା Open Library ରେ ଦେଖାଯାଏ ନାହିଁ, ଏବଂ ଅନ୍ୟ ଦୁଇଟି ପାଇଁ ଏହା ଏକ ଛୋଟ କିନ୍ତୁ ତଥାପି ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ମାପରେ ହୋଇଥାଏ। ଏହା ଅନେକ ନୂତନ ପ୍ରଶ୍ନ ଉତ୍ପନ୍ନ କରେ। ସ୍ୱୟଂଚାଳିତ ମେଳକରଣ କେତେ ମଧ୍ୟରେ ସାହାଯ୍ୟ କରିପାରେ ଯେଉଁ ପୁସ୍ତକଗୁଡ଼ିକ ISBN ସହିତ ଟ୍ୟାଗ୍ ହୋଇନଥିଲା? ଅନେକ ମେଳକ ହେବ କି ଏବଂ ଏହିପରି ଓଭରଲାପ୍ ବୃଦ୍ଧି ପାଇବ? ଏହା ସହିତ ଏକ 4ଥ କିମ୍ବା 5ମ ତଥ୍ୟସମୂହ ଆଣିଲେ କ’ଣ ହେବ? ତାହା ପରେ ଆମେ କେତେ ଓଭରଲାପ୍ ଦେଖିବାକୁ ପାଇବୁ? ଏକ ପ୍ରତିଶତ ପାଇଁ, ଆମକୁ ଏକ ହାରାହାରି ଆବଶ୍ୟକ: ସମସ୍ତ ପ୍ରକାଶିତ ପୁସ୍ତକର ସଂଖ୍ୟା।<sup>2</sup> ଗୁଗୁଲ ବୁକ୍ସର ଅନ୍ତିମ ଅବସ୍ଥାର ପୂର୍ବରୁ, ପ୍ରକଳ୍ପରେ ଥିବା ଏକ ଇଞ୍ଜିନିୟର, ଲିଓନିଡ୍ ଟାୟଚର୍, <a %(booksearch_blogspot)s>ଏହି ସଂଖ୍ୟାକୁ ଅନୁମାନ କରିବାକୁ ଚେଷ୍ଟା କରିଥିଲେ</a>। ସେ ଏହି ସଂଖ୍ୟାକୁ ନିର୍ଦ୍ଦିଷ୍ଟ କରିଥିଲେ — ହାସ୍ୟରେ — 129,864,880 (“କମ୍ ସେ କମ୍ ରବିବାର ପର୍ଯ୍ୟନ୍ତ”)। ସେ ସମସ୍ତ ପୁସ୍ତକର ଏକ ଏକତ୍ର ତଥ୍ୟାଧାର ତିଆରି କରି ଏହି ସଂଖ୍ୟାକୁ ଅନୁମାନ କରିଥିଲେ। ଏହା ପାଇଁ, ସେ ଭିନ୍ନ ତଥ୍ୟସମୂହଗୁଡ଼ିକୁ ଏକତ୍ର କରିଥିଲେ ଏବଂ ପରେ ସେଗୁଡ଼ିକୁ ବିଭିନ୍ନ ଭାବରେ ମିଶ୍ରଣ କରିଥିଲେ। ଆମେ କିଛି ପ୍ରାରମ୍ଭିକ ବିଶ୍ଳେଷଣ ସାଂଯାମା କରିବାକୁ ଯାଉଛୁ, ଯାହା ଜଗତର ପୁସ୍ତକ ସଂଖ୍ୟାକୁ ଅନୁମାନ କରିବାରେ ଆଗକୁ ଯିବାକୁ ଚେଷ୍ଟା କରିବାକୁ। ଆମେ ତିନୋଟି ତଥ୍ୟସମୂହକୁ ଦେଖିଲୁ: ଏହି ନୂତନ ISBNdb ତଥ୍ୟସମୂହ, ଆମର ମୂଳ ମେଟାଡାଟା ମୁକ୍ତି ଯାହା ଆମେ Z-Library ଛାୟା ପୁସ୍ତକାଳୟରୁ ସ୍କ୍ରାପ୍ କରିଥିଲୁ (ଯାହା Library Genesis କୁ ଅନ୍ତର୍ଭୁକ୍ତ କରେ), ଏବଂ Open Library ତଥ୍ୟ ଡମ୍ପ। ISBNdb ଡମ୍ପ, କିମ୍ବା କେତେ ଗୋଟିଏ ପୁସ୍ତକ ସଦାକାଳ ପାଇଁ ସଂରକ୍ଷିତ ହୋଇଛି? ଯଦି ଆମେ ଛାୟା ପୁସ୍ତକାଳୟରୁ ଫାଇଲଗୁଡ଼ିକୁ ଠିକ ଭାବରେ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ କରିଥାନ୍ତା, ତେବେ ସମସ୍ତ ପୁସ୍ତକର ମଧ୍ୟରୁ କେତେ ପ୍ରତିଶତ ଆମେ ସଂରକ୍ଷଣ କରିଛୁ? <a %(wikipedia_annas_archive)s>ଆନାର ଅଭିଲେଖ</a> ବିଷୟରେ ଅଦ୍ୟତନ, ମାନବ ଇତିହାସରେ ସବୁଠାରୁ ବଡ଼ ସତ୍ୟପୂର୍ଣ୍ଣ ଖୋଲା ପୁସ୍ତକାଳୟ। ଏକ ବର୍ଷ ପୂର୍ବରୁ, ଆମେ ଏହି ପ୍ରଶ୍ନର ଉତ୍ତର ଦେବାକୁ <a %(blog)s>ଆରମ୍ଭ କରିଥିଲୁ</a>: <strong>କେତେ ପ୍ରତିଶତ ପୁସ୍ତକ ଛାୟା ପୁସ୍ତକାଳୟ ଦ୍ୱାରା ସ୍ଥାୟୀ ଭାବେ ସଂରକ୍ଷିତ ହୋଇଛି?</strong> ଏକ ଖୋଲା-ଡାଟା ଛାୟା ପୁସ୍ତକାଳୟ ଭଳି <a %(wikipedia_library_genesis)s>Library Genesis</a>, ଏବଂ ବର୍ତ୍ତମାନ <a %(wikipedia_annas_archive)s>ଅନ୍ନାଙ୍କ ଅଭିଲେଖ</a> ରେ ଯେତେବେଳେ ଏକ ପୁସ୍ତକ ପହଞ୍ଚିବା, ଏହା ସମଗ୍ର ବିଶ୍ୱରେ ପ୍ରତିବିମ୍ବିତ ହୁଏ (ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ), ଏହାକୁ ପ୍ରାୟ ସଦାକାଳ ପାଇଁ ସଂରକ୍ଷଣ କରିଥାଏ। କେତେ ପ୍ରତିଶତ ପୁସ୍ତକ ସଂରକ୍ଷିତ ହୋଇଛି ଏହାର ଉତ୍ତର ଦେବାକୁ, ଆମେ ନିମ୍ନାକର୍ତ୍ତାକୁ ଜାଣିବା ଆବଶ୍ୟକ: ସମଗ୍ର ମିଶି କେତେ ପୁସ୍ତକ ଅଛି? ଏବଂ ଆଦର୍ଶ ଭାବେ ଆମେ କେବଳ ଏକ ସଂଖ୍ୟା ନୁହେଁ, କିନ୍ତୁ ବାସ୍ତବିକ metadata ରଖିଛୁ। ତାହେଲେ ଆମେ କେବଳ ଛାୟା ପୁସ୍ତକାଳୟ ସହିତ ସେମାନଙ୍କୁ ମେଳ କରିପାରିବା ନୁହେଁ, କିନ୍ତୁ ଏକ TODO ତାଲିକା ତିଆରି କରିପାରିବା ଯାହାକି ସଂରକ୍ଷଣ କରିବାକୁ ଅବଶିଷ୍ଟ ପୁସ୍ତକଗୁଡ଼ିକ!<strong> ଆମେ ଏହି TODO ତାଲିକାକୁ ନିମ୍ନ କରିବାକୁ ଏକ ଭିଡ଼ିତ ପ୍ରୟାସର ସ୍ୱପ୍ନ ଦେଖିବା ଆରମ୍ଭ କରିପାରିବା। ଆମେ <a %(wikipedia_isbndb_com)s>ISBNdb</a> କୁ ସ୍କ୍ରାପ୍ କରିଲୁ, ଏବଂ <a %(openlibrary)s>ଓପେନ୍ ଲାଇବ୍ରେରୀ ଡାଟାସେଟ୍</a> ଡାଉନଲୋଡ୍ କରିଲୁ, କିନ୍ତୁ ଫଳାଫଳ ଅସନ୍ତୋଷଜନକ ଥିଲା। ମୁଖ୍ୟ ସମସ୍ୟା ହେଉଛି ଯେ ISBN ଗୁଡ଼ିକର ଅଧିକ ଅଭିବ୍ୟାପନ ନଥିଲା। ଆମର <a %(blog)s>ବ୍ଲଗ୍ ପୋଷ୍ଟ</a>ରୁ ଏହି ଭେନ୍ ଡାୟାଗ୍ରାମ ଦେଖନ୍ତୁ: ଆମେ ISBNdb ଏବଂ ଓପେନ୍ ଲାଇବ୍ରେରୀ ମଧ୍ୟରେ କିତେକମ ଅଭିବ୍ୟାପନ ଥିବାରେ ଅତ୍ୟନ୍ତ ଆଶ୍ଚର୍ଯ୍ୟ ହେଲୁ। ଏହି ଦୁଇଟି ବିଭିନ୍ନ ଉତ୍ସରୁ ତଥ୍ୟ ଅନେକ ଭାବରେ ସାମିଲ କରନ୍ତି, ଯେପରିକି ୱେବ୍ ସ୍କ୍ରାପ୍ ଏବଂ ଲାଇବ୍ରେରୀ ରେକର୍ଡଗୁଡ଼ିକ। ଯଦି ସେମାନେ ବାହାରେ ଅଧିକାଂଶ ISBN ଖୋଜିବାରେ ଭଲ କାମ କରନ୍ତି, ତାହାହେଲେ ସେମାନଙ୍କର ବୃତ୍ତଗୁଡ଼ିକ ନିଶ୍ଚିତ ଭାବରେ ଅଧିକ ଅଭିବ୍ୟାପନ ରଖିଥାନ୍ତା, କିମ୍ବା ଗୋଟିଏ ଅନ୍ୟର ଉପସମୁଚ୍ଚୟ ହୋଇଥାନ୍ତା। ଏହା ଆମକୁ ଆଶ୍ଚର୍ଯ୍ୟ କରାଇଲା, କିତେକ ପୁସ୍ତକ <em>ଏହି ବୃତ୍ତଗୁଡ଼ିକରୁ ସମ୍ପୂର୍ଣ୍ଣ ବାହାରେ ପଡ଼ିଛି</em>? ଆମକୁ ଏକ ବଡ଼ ଡାଟାବେସ୍ ଆବଶ୍ୟକ। ସେତେବେଳେ ଆମେ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ପୁସ୍ତକ ଡାଟାବେସ୍ ଉପରେ ନଜର ଦେଲୁ: <a %(wikipedia_worldcat)s>ୱର୍ଲ୍ଡକ୍ୟାଟ୍</a>। ଏହା ଏକ ନିଜସ୍ୱ ଡାଟାବେସ୍ ଯାହା ଏକ ନନ୍-ପ୍ରଫିଟ୍ <a %(wikipedia_oclc)s>OCLC</a> ଦ୍ୱାରା ନିର୍ମିତ, ଯାହା ବିଶ୍ୱର ସମସ୍ତ ଲାଇବ୍ରେରୀରୁ ମେଟାଡାଟା ରେକର୍ଡଗୁଡ଼ିକୁ ସଂଗ୍ରହ କରେ, ଏବଂ ସେମାନଙ୍କୁ ସମ୍ପୂର୍ଣ୍ଣ ଡାଟାସେଟ୍ ପ୍ରବେଶ ଦେବା ପାଇଁ ଏବଂ ଅନ୍ତିମ ବ୍ୟବହାରକାରୀଙ୍କ ସନ୍ଧାନ ଫଳାଫଳରେ ସେମାନଙ୍କୁ ଦେଖାଇବା ପାଇଁ ଏହାକୁ ଦେଇଥାଏ। ଯଦିଓ OCLC ଏକ ନନ୍-ପ୍ରଫିଟ୍, ସେମାନଙ୍କର ବ୍ୟବସାୟ ମଡେଲ୍ ସେମାନଙ୍କର ଡାଟାବେସ୍ ସୁରକ୍ଷା କରିବାକୁ ଆବଶ୍ୟକ। ଭଲ, ଆମେ କହିବାକୁ ଦୁଃଖିତ, OCLCର ମିତ୍ରମାନେ, ଆମେ ସବୁକିଛି ଦେଉଛୁ। :-) 1.3B WorldCat ସ୍କ୍ରାପ୍ <em><strong>ସାରାଂଶ:</strong> ଅନ୍ନାଙ୍କ ଅଭିଲେଖ ସମସ୍ତ WorldCat (ଜଗତର ସବୁଠାରୁ ବଡ଼ ପୁସ୍ତକାଳୟ metadata ସଂଗ୍ରହ) କୁ ସ୍କ୍ରାପ୍ କରିଛି ଯାହାକି ସଂରକ୍ଷଣ କରିବାକୁ ଆବଶ୍ୟକ ପୁସ୍ତକଗୁଡ଼ିକର ଏକ TODO ତାଲିକା ତିଆରି କରିବାକୁ।</em> ୱର୍ଲ୍ଡକ୍ୟାଟ୍ ସତର୍କତା: ଏହି ବ୍ଲଗ୍ ପୋଷ୍ଟ ଅବସରପ୍ରାପ୍ତ ହୋଇଛି। ଆମେ ନିଷ୍ପତ୍ତି ନେଇଛୁ ଯେ IPFS ଏଯାବତ୍ ମୁଖ୍ୟ ସମୟ ପାଇଁ ପ୍ରସ୍ତୁତ ନୁହେଁ। ଆମେ ଆନାର ଆର୍କାଇଭ୍ ଠାରୁ ସମ୍ଭବ ହେଲେ IPFS ଉପରେ ଫାଇଲ୍ ଲିଙ୍କ କରିବାକୁ ଚାଲିବା, କିନ୍ତୁ ଆମେ ଏହାକୁ ଆପଣା ମାନେ ଆରମ୍ଭ କରିବାକୁ ସୁପାରିଶ କରୁନାହିଁ, ନାହିଁ କି ଅନ୍ୟମାନଙ୍କୁ IPFS ବ୍ୟବହାର କରି ମିରର୍ କରିବାକୁ ସୁପାରିଶ କରୁନାହିଁ। ଯଦି ଆପଣ ଆମ ସଂଗ୍ରହକୁ ସଂରକ୍ଷଣ କରିବାକୁ ସାହାଯ୍ୟ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ତାହେଲେ ଆମର ଟୋରେଣ୍ଟ ପୃଷ୍ଠା ଦେଖନ୍ତୁ। Z-Library କୁ IPFS ଉପରେ ସିଡ୍ କରିବାରେ ସାହାଯ୍ୟ କରନ୍ତୁ ପାର୍ଟନର ସର୍ଭର ଡାଉନଲୋଡ୍ SciDB ବାହ୍ୟ ଧାର ବାହାରି ଧାର (ମୁଦ୍ରଣ ଅସମର୍ଥ) ବାହ୍ୟ ଡାଉନଲୋଡ୍ ମେଟାଡାଟା ଅନୁସନ୍ଧାନ କରନ୍ତୁ ଟୋରେଣ୍ଟରେ ଅନ୍ତର୍ଭୁକ୍ତ  (+%(num)s ବୋନସ୍) ଅନାଦାୟ କିଣାଯାଇଛି ବାତିଲ୍ ହୋଇଛି | ମିଆଦ ପୂର୍ଣ୍ଣ ହୋଇଛି ନିଶ୍ଚିତ କରିବାକୁ ଅପେକ୍ଷା କରିଛି | ଅବୈଧ ପାଠ୍ୟ ନିମ୍ନରେ ଇଂରାଜୀରେ ଜାରି ରହିଛି। ଯାଆନ୍ତୁ ପୁନଃ ସେଟ୍ କରନ୍ତୁ ଯଦି ଆପଣଙ୍କ ଇମେଲ୍ ଠିକଣା Libgen ଫୋରମରେ କାମ କରୁନାହିଁ, ଆମେ <a %(a_mail)s>ପ୍ରୋଟନ ମେଲ୍</a> (ନିଶୁଳ୍କ) ବ୍ୟବହାର କରିବାକୁ ସୁପାରିଶ କରୁ। ଆପଣ ମାନୁଆଲି <a %(a_manual)s>ଅନୁରୋଧ କରିପାରିବେ</a> ଆପଣଙ୍କ ଆକାଉଣ୍ଟକୁ ସକ୍ରିୟ କରିବା ପାଇଁ। (ହୋଇପାରେ <a %(a_browser)s>ବ୍ରାଉଜର ସତ୍ୟାପନ</a> ଆବଶ୍ୟକ ହୋଇପାରେ — ଅସୀମିତ ଡାଉନଲୋଡ୍!) ଦ୍ରୁତ ସହଭାଗୀ ସର୍ଭର୍ #%(number)s (ସୁପାରିଶିତ) (ସ୍ଲୋ ହେଲେ ମଧ୍ୟ ପ୍ରତୀକ୍ଷା ତାଲିକା ସହିତ କିଛି ତେଜ) (ବ୍ରାଉଜର ସତ୍ୟାପନା ଆବଶ୍ୟକ ନୁହେଁ) (କୌଣସି ବ୍ରାଉଜର ସତ୍ୟାପନ କିମ୍ବା ପ୍ରତୀକ୍ଷା ତାଲିକା ନାହିଁ) (କୌଣସି ପ୍ରତୀକ୍ଷା ତାଲିକା ନାହିଁ, କିନ୍ତୁ ଖୁବ ଧୀର ହୋଇପାରେ) ଧୀର ଭାଗୀଦାର ସର୍ଭର # %(number)s ଅଡିଓବୁକ୍ କମିକ୍ ବୁକ୍ ପୁସ୍ତକ (ଗଳ୍ପ) ପୁସ୍ତକ (ଅଣ-ଗଳ୍ପ) ପୁସ୍ତକ (ଅଜ୍ଞାତ) ଜର୍ଣ୍ଣାଲ୍ ଲେଖା ମ୍ୟାଗାଜିନ୍ ସଙ୍ଗୀତ ସ୍କୋର ଅନ୍ୟାନ୍ୟ ମାନକ ଡକ୍ୟୁମେଣ୍ଟ ସମସ୍ତ ପୃଷ୍ଠାଗୁଡ଼ିକୁ PDF କୁ ପରିବର୍ତ୍ତନ କରାଯାଇପାରିଲା ନାହିଁ Libgen.li ରେ ଭଙ୍ଗା ଭାବରେ ଚିହ୍ନିତ Libgen.li ରେ ଦୃଶ୍ୟମାନ ନୁହେଁ Libgen.rs କାଳ୍ପନିକତାରେ ଦୃଶ୍ୟମାନ ନୁହେଁ Libgen.rs ଗଞ୍ଜନା ଗଞ୍ଜନାରେ ଦୃଶ୍ୟମାନ ନୁହେଁ ଏହି ଫାଇଲରେ exiftool ଚାଲୁ କରିବା ବିଫଳ ହେଲା Z-Library ରେ “ଖରାପ ଫାଇଲ” ଭାବରେ ଚିହ୍ନିତ Z-ଲାଇବ୍ରେରୀରୁ ଅନୁପସ୍ଥିତ Z-Library ରେ “ସ୍ପାମ” ଭାବରେ ଚିହ୍ନିତ ଫାଇଲ୍ ଖୋଲାଯାଇପାରିବ ନାହିଁ | (ଯଥା ଭ୍ରଷ୍ଟ ଫାଇଲ୍, DRM |) କପିରାଇଟ୍ ଦାବି ସମସ୍ୟାଗୁଡ଼ିକୁ ଡାଉନଲୋଡ୍ କରୁଛି | (ଯଥା ସଂଯୋଗ ହୋଇପାରିବ ନାହିଁ, ତ୍ରୁଟି ବାର୍ତ୍ତା, ବହୁତ ଧୀର |) ଭୁଲ ମେଟାଡାଟା | (ଯଥା ଆଖ୍ୟା, ବର୍ଣ୍ଣନା, କଭର ପ୍ରତିଛବି |) ଅନ୍ୟମାନେ | ଖରାପ ଗୁଣ | (ଯଥା ଫର୍ମାଟିଂ ସମସ୍ୟା, ଖରାପ ସ୍କାନ୍ ଗୁଣ, ନିଖୋଜ ପୃଷ୍ଠାଗୁଡିକ |) ସ୍ପାମ୍ / ଫାଇଲ୍ ଅପସାରଣ କରାଯିବା ଉଚିତ୍ | (ଯଥା ବିଜ୍ଞାପନ, ଅପମାନଜନକ ବିଷୟବସ୍ତୁ |) %(amount)s (%(amount_usd)s) %(amount)s ସମୁଦାୟ %(amount)s (%(amount_usd)s) ସମୁଦାୟ ଉଜ୍ଜ୍ୱଳ ପୁସ୍ତକ କୀଟ | ଭାଗ୍ୟଶାଳୀ ପୁସ୍ତକାଳୟକାରୀ ଚମତ୍କାର ଡାଟାହୋଡର୍ | ଆଶ୍ଚର୍ଯ୍ୟଜନକ ଅଭିଲେଖାଗାର | ବୋନସ୍ ଡାଉନଲୋଡ୍ Cerlalc ଚେକ୍ ମେଟାଡାଟା DuXiu 读秀 EBSCOhost eBook ଇଣ୍ଡେକ୍ସ ଗୁଗୁଲ୍ ବୁକ୍ସ Goodreads ହାଥିଟ୍ରଷ୍ଟ IA IA ନିୟନ୍ତ୍ରିତ ଡିଜିଟାଲ୍ ଲେଣ୍ଡିଂ ISBNdb ISBN GRP Libgen.li “scimag” ବାହାର କରି Libgen.rs ଅପ୍ରାକୃତିକ ଏବଂ ପ୍ରାକୃତିକ ଲିବି MagzDB ନେକ୍ସସ୍/ଏସ୍‌ଟିସି OCLC (WorldCat) OpenLibrary ରୁଷିଆନ୍ ରାଜ୍ୟ ପୁସ୍ତକାଳୟ Sci-Hub Libgen.li “scimag” ଦ୍ୱାରା ସାଇ-ହବ୍ / ଲିବଜେନ୍ “ସ୍କିମାଗ୍” Trantor AA କୁ ଅପଲୋଡ୍‌ଗୁଡିକ Z-Library Z-Library ଚାଇନୀଜ୍ ଶିରୋନାମା, ଲେଖକ, DOI, ISBN, MD5, … ସନ୍ଧାନ ଲେଖକ ବର୍ଣ୍ଣନା ଏବଂ ମେଟାଡାଟା ମନ୍ତବ୍ୟ ସଂସ୍କରଣ ମୂଳ ଫାଇଲନାମ୍ ପ୍ରକାଶକ (ନିର୍ଦ୍ଦିଷ୍ଟ କ୍ଷେତ୍ର ଖୋଜନ୍ତୁ) ଶିରୋନାମା ପ୍ରକାଶିତ ବର୍ଷ ବୈଷୟିକ ବିବରଣୀ ଏହି କଏନର ସାଧାରଣ ତୁଳନାରେ ଅଧିକ ନ୍ୟୁନତମ ମୂଲ୍ୟ ରହିଛି। ଦୟାକରି ଅନ୍ୟ ଏକ ଅବଧି କିମ୍ବା ଅନ୍ୟ ଏକ କଏନ ଚୟନ କରନ୍ତୁ। ଅନୁରୋଧ ପୂରଣ ହୋଇପାରିଲା ନାହିଁ। ଦୟାକରି କିଛି ମିନିଟ ପରେ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ, ଏବଂ ଯଦି ଏହା ଅବିରତ ଘଟିଥାଏ ତେବେ ଆମକୁ %(email)s ଠାରେ ଏକ ସ୍କ୍ରିନସଟ୍ ସହିତ ସମ୍ପର୍କ କରନ୍ତୁ। ଅଜଣା ତ୍ରୁଟି ଘଟିଛି। ଦୟାକରି ଏକ ସ୍କ୍ରିନସ୍ହଟ୍ ସହିତ %(email)s ଠାରେ ଆମ ସହିତ ସଂପର୍କ କରନ୍ତୁ। ପେମେଣ୍ଟ ପ୍ରକ୍ରିୟାରେ ତ୍ରୁଟି ହେଲା। ଦୟାକରି ଏକ ମୁହୂର୍ତ୍ତ ଅପେକ୍ଷା କରନ୍ତୁ ଏବଂ ପୁନଃଚେଷ୍ଟା କରନ୍ତୁ। ଯଦି ସମସ୍ୟା 24 ଘଣ୍ଟାରୁ ଅଧିକ ସମୟ ପର୍ଯ୍ୟନ୍ତ ଚାଲିଥାଏ, ଦୟାକରି %(email)s ଠାରେ ଏକ ସ୍କ୍ରିନସ୍ହଟ୍ ସହିତ ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଆମେ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ କମିକ୍ସ ଛାୟା ଗ୍ରନ୍ଥାଗାରକୁ <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">ସମର୍ଥନ କରିବା</a> ପାଇଁ ଏକ ଧନସଂଗ୍ରହ ଅଭିଯାନ ଚାଲାଉଛୁ। ଆପଣଙ୍କର ସମର୍ଥନ ପାଇଁ ଧନ୍ୟବାଦ! <a href="/donate">ଦାନ କରନ୍ତୁ।</a> ଯଦି ଆପଣ ଦାନ କରିପାରିବେ ନାହିଁ, ଆମକୁ ଆପଣଙ୍କ ମିତ୍ରମାନଙ୍କୁ କହିବା ଏବଂ <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, କିମ୍ବା <a href="https://t.me/annasarchiveorg">Telegram</a>ରେ ଅନୁସରଣ କରି ସମର୍ଥନ କରିବା ବିଚାର କରନ୍ତୁ। ଆମକୁ <a %(a_request)s>ପୁସ୍ତକ ଅନୁରୋଧ</a> କରିବାକୁ ଇମେଲ୍ କରନ୍ତୁ ନାହିଁ<br>କିମ୍ବା ଛୋଟ (<10k) <a %(a_upload)s>ଅପଲୋଡ୍</a>। ଆନ୍ନାଙ୍କ ଅଭିଲେଖାଗାର | DMCA / କପିରାଇଟ୍ ଦାବି ଯୋଗାଯୋଗ ରେ ରୁହ Reddit ବିକଳ୍ପଗୁଡ଼ିକ ସ୍ଲମ୍ (%(unaffiliated)s) ଅସଂପୃକ୍ତ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଆପଣଙ୍କ ସହାୟତା ଆବଶ୍ୟକ! ଯଦି ଆପଣ ବର୍ତ୍ତମାନ ଦାନ କରନ୍ତି, ଆପଣ <strong>ଦୁଗୁଣା</strong> ଦ୍ରୁତ ଡାଉନଲୋଡ୍ ପାଇବେ। ଅନେକ ଲୋକ ଆମକୁ ନିମ୍ନକୁ ନେବାକୁ ଚେଷ୍ଟା କରନ୍ତି, କିନ୍ତୁ ଆମେ ପ୍ରତିରୋଧ କରୁଛୁ। ଯଦି ଆପଣ ଏହି ମାସରେ ଦାନ କରନ୍ତି, ଆପଣ <strong>ଦୁଗୁଣ</strong> ଦ୍ରୁତ ଡାଉନଲୋଡ୍ ପାଇବେ। ଏହି ମାସର ଶେଷ ପର୍ଯ୍ୟନ୍ତ ବୈଧ। ମାନବ ଜ୍ଞାନ ସଞ୍ଚୟ: ଏକ ମହାନ ଛୁଟିଦିନ ଉପହାର! ସଦସ୍ୟତାଗୁଡ଼ିକ ଉଚିତ ଭାବରେ ବୃଦ୍ଧି କରାଯିବ। ସହଭାଗୀ ସର୍ଭରଗୁଡ଼ିକ ହୋଷ୍ଟିଂ ବନ୍ଦ ହେବାରୁ ଅନୁପଲବ୍ଧ ଅଛି। ସେଗୁଡ଼ିକ ଶୀଘ୍ର ଉପଲବ୍ଧ ହେବା ଉଚିତ। Anna’s Archive ର ସ୍ଥିରତା ବୃଦ୍ଧି ପାଇଁ, ଆମେ ମିରର୍ସ ଚଲାଇବାକୁ ସେବକମାନଙ୍କୁ ଖୋଜୁଛୁ। ଆମେ ଏକ ନୂତନ ଦାନ ପ୍ରକ୍ରିୟା ଉପଲବ୍ଧ କରାଇଛୁ: %(method_name)s। ଦୟାକରି %(donate_link_open_tag)sଦାନ କରନ୍ତୁ</a> — ଏହି ୱେବସାଇଟ୍ ଚାଲୁ ରଖିବା ଖର୍ଚ୍ଚାଳୁ ନୁହେଁ, ଏବଂ ଆପଣଙ୍କ ଦାନ ନିଶ୍ଚିତ ଭାବେ ଏକ ପରିବର୍ତ୍ତନ ଆଣେ। ଆପଣଙ୍କୁ ଅନେକ ଧନ୍ୟବାଦ। ଏକ ମିତ୍ରଙ୍କୁ ସୁପାରିଶ କରନ୍ତୁ, ଏବଂ ଆପଣ ଏବଂ ଆପଣଙ୍କ ମିତ୍ର ଦୁହେଁ %(percentage)s%% ବୋନାସ୍ ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ପାଇବେ! ପ୍ରିୟଜନଙ୍କୁ ଆଶ୍ଚର୍ଯ୍ୟ କର, ସେମାନଙ୍କୁ ସଦସ୍ୟତା ସହିତ ଏକ ଖାତା ଦିଅ | ସଂପୂର୍ଣ୍ଣ ଭାଲେଣ୍ଟାଇନ୍ ଉପହାର! ଅଧିକ ଜାଣନ୍ତୁ… ଖାତା କାର୍ଯ୍ୟକଳାପ ଅଗ୍ରଗାମୀ ଆନାଙ୍କ ବ୍ଲଗ୍ ↗ ଆନାଙ୍କ ସଂଗ୍ରହ ↗ ବେଟା କୋଡ୍ସ ଏକ୍ସପ୍ଲୋରର୍ Datasets ଦାନ କରନ୍ତୁ | ଡାଉନଲୋଡ୍ ହୋଇଥିବା ଫାଇଲଗୁଡିକ | FAQ ମୁଖ୍ୟ ପୃଷ୍ଠା ମେଟାଡାଟା ଉନ୍ନତ କରନ୍ତୁ LLM ତଥ୍ୟ ଲଗ୍ ଇନ୍ / ରେଜିଷ୍ଟର କରନ୍ତୁ | ମୋର ଦାନ ସାର୍ବଜନିକ ପ୍ରୋଫାଇଲ୍ ଖୋଜନ୍ତୁ ସୁରକ୍ଷା ଟୋରେଣ୍ଟସ୍ ଅନୁବାଦ କରିବେ ↗ ସେବା ଏବଂ ପୁରସ୍କାର ସମ୍ପ୍ରତି ଡାଉନଲୋଡ୍: 📚&nbsp;ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଖୋଲା-ସ୍ରୋତ ଖୋଲା-ଡାଟା ପୁସ୍ତକାଳୟ। ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library, ଏବଂ ଅଧିକର ମିରର୍ସ। 📈&nbsp;%(book_any)s ପୁସ୍ତକ, %(journal_article)s ପେପର୍ସ, %(book_comic)s କମିକ୍ସ, %(magazine)s ମ୍ୟାଗାଜିନ୍ସ — ସଦାରଣ ପାଇଁ ସଂରକ୍ଷିତ।  ଏବଂ  ଏବଂ ଅଧିକ DuXiu ଇଣ୍ଟରନେଟ ଆର୍କାଇଭ୍ ଲେଣ୍ଡିଂ ଲାଇବ୍ରେରୀ LibGen 📚&nbsp;ମାନବ ଇତିହାସରେ ସବୁଠାରୁ ବଡ ପ୍ରକୃତ ଖୋଲା ଲାଇବ୍ରେରୀ | 📈&nbsp;%(book_count)s&nbsp;ପୁସ୍ତକ, %(paper_count)s&nbsp;ପେପର୍ — ସଦାରଣ ପାଇଁ ସଂରକ୍ଷିତ। ⭐️&nbsp;ଆମେ %(libraries)sକୁ ମିରର୍ କରୁଛୁ। ଆମେ %(scraped)sକୁ ସ୍କ୍ରାପ୍ ଏବଂ ଖୋଲା-ସ୍ରୋତ କରୁଛୁ। ଆମ ସମସ୍ତ କୋଡ୍ ଏବଂ ଡାଟା ସଂପୂର୍ଣ୍ଣ ଭାବେ ଖୋଲା ସ୍ରୋତ। OpenLib Sci-Hub ,  📚 ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଖୋଲା-ସ୍ରୋତ ଖୋଲା-ଡାଟା ପୁସ୍ତକାଳୟ।<br>⭐️ ସାଇହବ୍, ଲିବଜେନ୍, ଜେଲିବ୍ ଏବଂ ଅଧିକକୁ ମିରର୍ କରେ। Z-Lib ଆନା’ସ ଆର୍କାଇଭ୍ ଅବୈଧ ଅନୁରୋଧ। %(websites)sକୁ ଯାଆନ୍ତୁ। ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଖୋଲା-ସ୍ରୋତ ଖୋଲା-ଡାଟା ଲାଇବ୍ରେରୀ। Sci-Hub, Library Genesis, Z-Library ଏବଂ ଅଧିକର ମିରର୍ସ। ଆନାଙ୍କ ସଂଗ୍ରହରେ ସନ୍ଧାନ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଦୟାକରି ପୁନଃ ଚେଷ୍ଟା କରିବା ପାଇଁ ରିଫ୍ରେଶ କରନ୍ତୁ। ଯଦି ସମସ୍ୟା ଅନେକ ଘଣ୍ଟା ଧରି ଚାଲିଥାଏ ତେବେ <a %(a_contact)s>ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ</a>। 🔥 ଏହି ପୃଷ୍ଠା ଲୋଡ୍ ହେବାରେ ସମସ୍ୟା <li>1. <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, କିମ୍ବା <a href="https://t.me/annasarchiveorg">Telegram</a> ରେ ଆମକୁ ଫଲୋ କରନ୍ତୁ।</li><li>2. Twitter, Reddit, Tiktok, Instagram, ଆପଣଙ୍କ ସ୍ଥାନୀୟ କଫେ କିମ୍ବା ପୁସ୍ତକାଳୟରେ, କିମ୍ବା ଆପଣ ଯେଉଁଠାରେ ଯାଆନ୍ତି ସେଠାରେ Anna’s Archive ବିଷୟରେ ଅନ୍ୟମାନଙ୍କୁ କୁହନ୍ତୁ! ଆମେ ଗେଟକିପିଂରେ ବିଶ୍ୱାସ କରୁନାହିଁ — ଯଦି ଆମକୁ ଟେକଡାଉନ୍ କରାଯାଏ ତେବେ ଆମେ ଅନ୍ୟଠାରେ ପୁନଃ ଉପସ୍ଥିତ ହେବୁ, କାରଣ ଆମର ସମସ୍ତ କୋଡ୍ ଏବଂ ଡାଟା ପୂରାପୁରି ଖୋଲା ମୂଳର।</li><li>3. ଯଦି ଆପଣ ସମର୍ଥ ହୋଇଥାନ୍ତି, <a href="/donate">ଦାନ କରିବାକୁ</a> ଭାବନ୍ତୁ।</li><li>4. ଆମର ୱେବସାଇଟକୁ ଭିନ୍ନ ଭାଷାରେ <a href="https://translate.annas-software.org/">ଅନୁବାଦ କରିବାରେ</a> ସାହାଯ୍ୟ କରନ୍ତୁ।</li><li>5. ଯଦି ଆପଣ ଏକ ସଫ୍ଟୱେର ଇଞ୍ଜିନିୟର ହୋଇଥାନ୍ତି, ଆମ <a href="https://annas-software.org/">ଖୋଲା ମୂଳର</a> କିମ୍ବା ଆମର <a href="/datasets">ଟୋରେଣ୍ଟସ୍</a> ସିଡ୍ କରିବାରେ ଅବଦାନ ରଖନ୍ତୁ।</li> 10. ଆପଣଙ୍କ ଭାଷାରେ Anna’s Archive ପାଇଁ ଉଇକିପିଡ଼ିଆ ପୃଷ୍ଠା ସୃଷ୍ଟି କରନ୍ତୁ କିମ୍ବା ରକ୍ଷା କରିବାରେ ସାହାଯ୍ୟ କରନ୍ତୁ। 11. ଆମେ ଛୋଟ, ରୁଚିକର ବିଜ୍ଞାପନ ରଖିବାକୁ ଚାହୁଁଛୁ। ଆପଣ ଆନା’ସ ଆର୍କାଇଭରେ ବିଜ୍ଞାପନ ଦେବାକୁ ଚାହୁଁଥିଲେ, ଦୟାକରି ଆମକୁ ଜଣାନ୍ତୁ। 6. ଯଦି ଆପଣ ଏକ ସୁରକ୍ଷା ଗବେଷକ, ଆମେ ଆପଣଙ୍କ ଦକ୍ଷତାକୁ ଦୁଇଟି ଉଦ୍ଦେଶ୍ୟ ପାଇଁ ବ୍ୟବହାର କରିପାରିବା: ଆକ୍ରମଣ ଏବଂ ପ୍ରତିରକ୍ଷା। ଆମର <a %(a_security)s>ସୁରକ୍ଷା</a> ପୃଷ୍ଠାକୁ ଯାଞ୍ଚ କରନ୍ତୁ। 7. ଆମେ ଗୋପନୀୟ ବ୍ୟବସାୟୀ ପାଇଁ ପେମେଣ୍ଟ ବିଶେଷଜ୍ଞଙ୍କୁ ଖୋଜୁଛୁ। ଆପଣ ଆମକୁ ଅଧିକ ସୁବିଧାଜନକ ଦାନ ପ୍ରକ୍ରିୟା ଯୋଗ କରିବାରେ ସାହାଯ୍ୟ କରିପାରିବେ କି? PayPal, WeChat, ଉପହାର କାର୍ଡଗୁଡ଼ିକ। ଯଦି ଆପଣ କାହାକୁ ଜାଣିଥାନ୍ତି, ଦୟାକରି ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। 8. ଆମେ ସବୁବେଳେ ଅଧିକ ସର୍ଭର୍ କ୍ଷମତା ଖୋଜୁଛୁ। 9. ଆପଣ ଫାଇଲ୍ ସମସ୍ୟାଗୁଡ଼ିକୁ ରିପୋର୍ଟ କରି, ମନ୍ତବ୍ୟ ଛାଡ଼ି ଏବଂ ଏହି ୱେବସାଇଟ୍ ଉପରେ ତାଲିକାଗୁଡ଼ିକ ତିଆରି କରି ସାହାଯ୍ୟ କରିପାରିବେ। ଆପଣ <a %(a_upload)s>ଅଧିକ ପୁସ୍ତକ ଅପଲୋଡ୍ କରି</a>, କିମ୍ବା ଅବସ୍ଥିତ ପୁସ୍ତକଗୁଡ଼ିକର ଫାଇଲ୍ ସମସ୍ୟା କିମ୍ବା ଫର୍ମାଟିଂ ଠିକ୍ କରି ସାହାଯ୍ୟ କରିପାରିବେ। ସେବା ଦେବା ବିଷୟରେ ଅଧିକ ସୂଚନା ପାଇଁ, ଆମର <a %(a_volunteering)s>ସେବା ଦେବା ଏବଂ ବାଉଣ୍ଟିସ୍</a> ପୃଷ୍ଠା ଦେଖନ୍ତୁ। ଆମେ ତଥ୍ୟର ମୁକ୍ତ ପ୍ରବାହ ଏବଂ ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିର ସଂରକ୍ଷଣରେ ଦୃଢ଼ ଭାବରେ ବିଶ୍ୱାସ କରୁ। ଏହି ସର୍ଚ୍ଚ ଇଞ୍ଜିନ୍ ସହିତ, ଆମେ ବିଶାଳ ମନ୍ଥନର ଉପରେ ନିର୍ମାଣ କରୁ। ଆମେ ବିଭିନ୍ନ ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକର ସୃଷ୍ଟିକର୍ତ୍ତାମାନଙ୍କର କଠିନ ପ୍ରୟାସକୁ ଗଭୀର ସମ୍ମାନ କରୁ ଏବଂ ଆଶା କରୁ ଯେ ଏହି ସର୍ଚ୍ଚ ଇଞ୍ଜିନ୍ ସେମାନଙ୍କର ପହଞ୍ଚକୁ ବିସ୍ତାରିତ କରିବ। ଆମର ପ୍ରଗତି ସମ୍ବନ୍ଧରେ ଅପଡେଟ୍ ରହିବା ପାଇଁ, <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> କିମ୍ବା <a href="https://t.me/annasarchiveorg">Telegram</a> ରେ ଆନାକୁ ଫଲୋ କରନ୍ତୁ। ପ୍ରଶ୍ନ ଏବଂ ପ୍ରତିକ୍ରିୟା ପାଇଁ ଦୟାକରି %(email)s ଆନା ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଖାତା ID: %(account_id)s ଲଗ୍ ଆଉଟ୍ ❌ କିଛି ଭୁଲ ହୋଇଛି। ପୃଷ୍ଠାଟିକୁ ପୁନଃ ଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନଃଚେଷ୍ଟା କରନ୍ତୁ। ✅ ଆପଣ ବର୍ତ୍ତମାନ ଲଗ୍ ଆଉଟ୍ ହୋଇଛନ୍ତି। ପୁନଃ ଲଗ୍ ଇନ୍ କରିବାକୁ ପୃଷ୍ଠାକୁ ପୁନଃ ଲୋଡ୍ କରନ୍ତୁ। ତ୍ୱରିତ ଡାଉନଲୋଡ୍ସ ବ୍ୟବହୃତ (ଶେଷ 24 ଘଣ୍ଟା): <strong>%(used)s / %(total)s</strong> ସଦସ୍ୟତା: <strong>%(tier_name)s</strong> ଯାଏଁ %(until_date)s <a %(a_extend)s>(ବୃଦ୍ଧି କରନ୍ତୁ)</a> ଆପଣ ଅନେକ ସଦସ୍ୟତାକୁ ଯୋଡ଼ିପାରିବେ (ପ୍ରତି 24 ଘଣ୍ଟାରେ ତୀବ୍ର ଡାଉନଲୋଡ୍ ଯୋଡ଼ାଯିବ)। ସଦସ୍ୟତା: <strong>କୌଣସି ନାହିଁ</strong> <a %(a_become)s>(ସଦସ୍ୟ ହୁଅନ୍ତୁ)</a> ଆପଣଙ୍କ ସଦସ୍ୟତାକୁ ଉଚ୍ଚ ସ୍ତରକୁ ଅପଗ୍ରେଡ୍ କରିବାକୁ ଆଗ୍ରହୀ ହେଲେ, ଆନାଙ୍କୁ %(email)s ଠାରେ ସଂପର୍କ କରନ୍ତୁ। ସାର୍ବଜନିକ ପ୍ରୋଫାଇଲ୍: %(profile_link)s ଗୁପ୍ତ କୁଞ୍ଜି (ସେୟାର କରନ୍ତୁ ନାହିଁ!): %(secret_key)s ଦେଖାନ୍ତୁ ଆମ ସହିତ ଯୋଗ ଦିଅନ୍ତୁ! ଆମ ଦଳରେ ଯୋଗ ଦେବା ପାଇଁ <a %(a_tier)s>ଉଚ୍ଚ ସ୍ତରକୁ</a> ଅପଗ୍ରେଡ୍ କରନ୍ତୁ। ଏକାନ୍ତିକ Telegram ଗ୍ରୁପ୍: %(link)s ଖାତା କେଉଁ ଡାଉନଲୋଡ୍? ଲଗ୍ ଇନ୍ କରନ୍ତୁ ଆପଣଙ୍କ କୀ ହରାଇବେ ନାହିଁ! ଅବୈଧ ଗୁପ୍ତ କୀ। ଆପଣଙ୍କର କୀ ଯାଞ୍ଚ କରନ୍ତୁ ଏବଂ ପୁନଃ ଚେଷ୍ଟା କରନ୍ତୁ, କିମ୍ବା ବିକଳ୍ପ ଭାବେ ନିମ୍ନରେ ଏକ ନୂତନ ଖାତା ରେଜିଷ୍ଟର କରନ୍ତୁ। ଗୁପ୍ତ କୀ ଲଗ୍ ଇନ୍ କରିବା ପାଇଁ ଆପଣଙ୍କର ଗୁପ୍ତ କୁଞ୍ଜି ପ୍ରବେଶ କରନ୍ତୁ: ପୁରୁଣା ଇମେଲ୍ ଆଧାରିତ ଖାତା? ଆପଣଙ୍କ <a %(a_open)s>ଇମେଲ୍ ଏଠାରେ ପ୍ରବେଶ କରନ୍ତୁ</a>। ନୂଆ ଖାତା ପଞ୍ଜିକରଣ କରାନ୍ତୁ ଏପର୍ଯ୍ୟନ୍ତ ଗୋଟିଏ ଖାତା ନାହିଁ କି? ପଞ୍ଜୀକରଣ ସଫଳ! ଆପଣଙ୍କର ଗୁପ୍ତ ଚାବି ହେଉଛି: <span %(span_key)s>%(key)s</span> ଏହି କୀକୁ ସାବଧାନରେ ସଂରକ୍ଷଣ କରନ୍ତୁ। ଯଦି ଆପଣ ଏହାକୁ ହରାଇବେ, ଆପଣ ଆପଣଙ୍କ ଆକାଉଣ୍ଟକୁ ପ୍ରବେଶ ହରାଇବେ। <li %(li_item)s><strong>ବୁକମାର୍କ୍ କରନ୍ତୁ।</strong> ଆପଣଙ୍କ କୀ ପୁନଃ ପ୍ରାପ୍ତି କରିବା ପାଇଁ ଏହି ପୃଷ୍ଠାକୁ ବୁକମାର୍କ୍ କରିପାରିବେ।</li><li %(li_item)s><strong>ଡାଉନଲୋଡ୍ କରନ୍ତୁ।</strong> ଆପଣଙ୍କ କୀ ଡାଉନଲୋଡ୍ କରିବା ପାଇଁ <a %(a_download)s>ଏହି ଲିଙ୍କ୍</a> କ୍ଲିକ୍ କରନ୍ତୁ।</li><li %(li_item)s><strong>ପାସୱାର୍ଡ୍ ମ୍ୟାନେଜର୍।</strong> ନିମ୍ନରେ ଏହା ପ୍ରବେଶ କରିବା ସମୟରେ ଏକ ପାସୱାର୍ଡ୍ ମ୍ୟାନେଜର୍ ବ୍ୟବହାର କରି ଏହାକୁ ସଂରକ୍ଷଣ କରନ୍ତୁ।</li> ଲଗ୍ ଇନ୍ / ପଞ୍ଜିକରଣ କରନ୍ତୁ ବ୍ରାଉଜର ସତ୍ୟାପନ ସତର୍କତା: କୋଡ୍ ରେ ତ୍ରୁଟିପୂର୍ଣ୍ଣ Unicode ଅକ୍ଷର ଅଛି, ଏବଂ ବିଭିନ୍ନ ପରିସ୍ଥିତିରେ ତ୍ରୁଟିପୂର୍ଣ୍ଣ ଭାବରେ ବ୍ୟବହାର କରିପାରେ। URL ରେ base64 ପ୍ରତିନିଧିତ୍ୱରୁ କଚା ବାଇନାରୀକୁ ଡିକୋଡ୍ କରାଯାଇପାରେ। ବର୍ଣ୍ଣନା ଲେବଲ୍ ପ୍ରିଫିକ୍ସ ନିର୍ଦ୍ଦିଷ୍ଟ କୋଡ୍ ପାଇଁ URL ୱେବସାଇଟ୍ “%(prefix_label)s” ସହିତ ଆରମ୍ଭ ହେଉଥିବା କୋଡ୍ଗୁଡିକ ଦୟାକରି ଏହି ପୃଷ୍ଠାଗୁଡିକୁ ସ୍କ୍ରାପ୍ କରନ୍ତୁ ନାହିଁ। ତାହା ପରିବର୍ତ୍ତେ ଆମେ <a %(a_import)s>ଜେନେରେଟ୍ କରିବାକୁ</a> କିମ୍ବା <a %(a_download)s>ଡାଉନଲୋଡ୍ କରିବାକୁ</a> ଆମର ElasticSearch ଏବଂ MariaDB ଡାଟାବେସ୍ ଗୁଡିକୁ ସୁପାରିଶ କରୁଛୁ, ଏବଂ ଆମର <a %(a_software)s>ଖୋଲା ଉତ୍ସ କୋଡ୍</a> ଚଲାନ୍ତୁ। କଚା ତଥ୍ୟକୁ <a %(a_json_file)s>ଏହା ଭଳି</a> JSON ଫାଇଲ୍ ମାନଙ୍କ ମାଧ୍ୟମରେ ମାନୁଆଲ୍ ଭାବେ ଏକ୍ସପ୍ଲୋର୍ କରାଯାଇପାରେ। %(count)s ରେକର୍ଡ୍ ଠାରୁ କମ୍ ସାଧାରଣ URL କୋଡ୍ସ ଏକ୍ସପ୍ଲୋରର୍ ସୂଚୀକରଣ ପ୍ରିଫିକ୍ସ ଦ୍ୱାରା ରେକର୍ଡ୍ ଗୁଡିକ ଚିହ୍ନିତ ହେଉଥିବା କୋଡ୍ସ ଗୁଡିକୁ ଏକ୍ସପ୍ଲୋର୍ କରନ୍ତୁ। “ରେକର୍ଡ୍” କଲମ୍ ଦେଖାଏ କେତେ ରେକର୍ଡ୍ ଗୁଡିକ ଦିଆଯାଇଥିବା ପ୍ରିଫିକ୍ସ ସହିତ କୋଡ୍ସ ଦ୍ୱାରା ଚିହ୍ନିତ ହୋଇଛି, ଯାହା ସର୍ଚ୍ଚ ଇଞ୍ଜିନ୍ (ମେଟାଡାଟା-ମାତ୍ର ରେକର୍ଡ୍ ସମେତ) ରେ ଦେଖାଯାଏ। “କୋଡ୍ସ” କଲମ୍ ଦେଖାଏ କେତେ ଆସଲ୍ କୋଡ୍ସ ଦିଆଯାଇଥିବା ପ୍ରିଫିକ୍ସ ରଖିଛି। ଜଣାଶୁଣା କୋଡ୍ ପ୍ରିଫିକ୍ସ “%(key)s” ଅଧିକ… ପ୍ରିଫିକ୍ସ %(count)s ରେକର୍ଡ୍ “%(prefix_label)s” ସହିତ ମେଳାଉଛି %(count)s ରେକର୍ଡ୍ “%(prefix_label)s” ସହିତ ମେଳାଉଛି କୋଡ୍ଗୁଡିକ ରେକର୍ଡ୍ “%%s” କୋଡ୍ର ମୂଲ୍ୟ ସହିତ ପରିବର୍ତ୍ତିତ ହେବ ଆନାର ଆର୍କାଇଭ୍ ଖୋଜନ୍ତୁ କୋଡ୍ସ ନିର୍ଦ୍ଦିଷ୍ଟ କୋଡ୍ ପାଇଁ URL: “%(url)s” ଏହି ପୃଷ୍ଠା ତିଆରି ହେବାକୁ କିଛି ସମୟ ନେଇପାରେ, ଯାହାକୁ ଏକ Cloudflare କ୍ୟାପଚା ଆବଶ୍ୟକ କରେ। <a %(a_donate)s>ସଦସ୍ୟମାନେ</a> କ୍ୟାପଚା ଏଡାଇ ପାରିବେ। ଅପମାନ ରିପୋର୍ଟ ହୋଇଛି: ଭଲ ଅନୁସ୍କରଣ ଆପଣ ଏହି ବ୍ୟବହାରକାରୀଙ୍କୁ ଅପମାନଜନକ କିମ୍ବା ଅଯୋଗ୍ୟ ଆଚରଣ ପାଇଁ ରିପୋର୍ଟ କରିବାକୁ ଚାହାଁନ୍ତି କି? ଫାଇଲ ସମସ୍ୟା: %(file_issue)s ଲୁଚା ମନ୍ତବ୍ୟ ପ୍ରତିକ୍ରିୟା ଅପମାନ ରିପୋର୍ଟ କରନ୍ତୁ ଆପଣ ଏହି ବ୍ୟବହାରକାରୀଙ୍କୁ ଅପମାନ ପାଇଁ ରିପୋର୍ଟ କରିଛନ୍ତି। ଏହି ଇମେଲ୍ ପାଇଁ କପିରାଇଟ୍ ଦାବିଗୁଡିକୁ ଅନଦେଖା କରାଯିବ; ତାହାର ପରିବର୍ତ୍ତେ ଫର୍ମ ବ୍ୟବହାର କରନ୍ତୁ। ଇମେଲ୍ ଦେଖାନ୍ତୁ ଆମେ ଆପଣଙ୍କର ପ୍ରତିକ୍ରିୟା ଏବଂ ପ୍ରଶ୍ନଗୁଡ଼ିକୁ ବହୁତ ସ୍ୱାଗତ କରୁଛୁ! ହେଲେ, ଆମେ ଯେତେକି ସ୍ପାମ୍ ଏବଂ ଅର୍ଥହୀନ ଇମେଲ୍ ପାଉଛୁ, ଦୟାକରି ଏହି ସର୍ତ୍ତଗୁଡ଼ିକୁ ବୁଝିବାକୁ ନିଶ୍ଚିତ କରିବା ପାଇଁ ଘର୍ଷଣ ଚିହ୍ନ ଯାଞ୍ଚ କରନ୍ତୁ। କପିରାଇଟ୍ ଦାବି ବିଷୟରେ ଆମକୁ ଯେକୌଣସି ଅନ୍ୟ ଉପାୟରେ ସମ୍ପର୍କ କରିବାକୁ ଚେଷ୍ଟା କଲେ ସେଗୁଡ଼ିକ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଡିଲିଟ୍ ହୋଇଯିବ। DMCA / କପିରାଇଟ୍ ଦାବି ପାଇଁ, <a %(a_copyright)s>ଏହି ଫର୍ମ</a> ବ୍ୟବହାର କରନ୍ତୁ। ସମ୍ପର୍କ ଇମେଲ୍ Anna’s Archive ରେ URLs (ଆବଶ୍ୟକ) । ପ୍ରତି ଲାଇନରେ ଗୋଟିଏ କରନ୍ତୁ। ଦୟାକରି କେବଳ ସେହି URLs ଯୋଡ଼ନ୍ତୁ ଯାହା ଗୋଟିଏ ପୁସ୍ତକର ସମାନ ସଂସ୍କରଣକୁ ବର୍ଣ୍ଣନା କରେ। ଯଦି ଆପଣ ଅନେକ ପୁସ୍ତକ କିମ୍ବା ଅନେକ ସଂସ୍କରଣ ପାଇଁ ଦାବି କରିବାକୁ ଚାହୁଁଛନ୍ତି, ଦୟାକରି ଏହି ଫର୍ମକୁ ଅନେକଥର ଦାଖଲ କରନ୍ତୁ। ଯେଉଁ ଦାବିଗୁଡ଼ିକ ଅନେକ ପୁସ୍ତକ କିମ୍ବା ସଂସ୍କରଣକୁ ଏକାତ୍ମ କରିଥାଏ ସେଗୁଡ଼ିକୁ ପ୍ରତ୍ୟାଖ୍ୟାନ କରାଯିବ। ଠିକଣା (ଆବଶ୍ୟକ) ମୂଳ ସାମଗ୍ରୀର ସ୍ପଷ୍ଟ ବର୍ଣ୍ଣନା (ଆବଶ୍ୟକ) ଇମେଲ୍ (ଆବଶ୍ୟକ) ମୂଳ ସାମଗ୍ରୀର URLs, ପ୍ରତି ଲାଇନରେ ଗୋଟିଏ (ଆବଶ୍ୟକ) । ଆମକୁ ଆପଣଙ୍କ ଦାବିକୁ ସତ୍ୟାପିତ କରିବାରେ ସାହାଯ୍ୟ କରିବା ପାଇଁ ଯଥାସମ୍ଭବ ଅଧିକ URLs ଯୋଡ଼ନ୍ତୁ (ଉଦାହରଣ ସ୍ୱରୂପ Amazon, WorldCat, Google Books, DOI)। ମୂଳ ସାମଗ୍ରୀର ISBNs (ଯଦି ଲାଗୁକ) । ପ୍ରତି ଲାଇନରେ ଗୋଟିଏ କରନ୍ତୁ। ଦୟାକରି କେବଳ ସେଗୁଡ଼ିକୁ ଯୋଡ଼ନ୍ତୁ ଯାହା ଆପଣ ଯାହା ପାଇଁ କପିରାଇଟ ଦାବି ରିପୋର୍ଟ କରୁଛନ୍ତି ସେହି ସଂସ୍କରଣ ସହିତ ସଠିକ ମେଳ ଖାଉଛି। ଆପଣଙ୍କ ନାମ (ଆବଶ୍ୟକ) ❌ କିଛି ଭୁଲ୍ ହୋଇଛି। ଦୟାକରି ପୃଷ୍ଠାକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନଃ ଚେଷ୍ଟା କରନ୍ତୁ। ✅ ଆପଣଙ୍କ କପିରାଇଟ ଦାବି ଦାଖଲ କରିଥିବାରୁ ଧନ୍ୟବାଦ। ଆମେ ଏହାକୁ ଯଥାଶୀଘ୍ର ସମୀକ୍ଷା କରିବୁ। ଦୟାକରି ଆଉ ଗୋଟିଏ ଦାଖଲ କରିବାକୁ ପୃଷ୍ଠାକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ। <a %(a_openlib)s>Open Library</a> ରେ ମୂଳ ସାମଗ୍ରୀର URLs, ପ୍ରତି ଲାଇନରେ ଗୋଟିଏ କରନ୍ତୁ। ଦୟାକରି ଆପଣଙ୍କ ମୂଳ ସାମଗ୍ରୀ ପାଇଁ Open Library ରେ ସନ୍ଧାନ କରିବାକୁ ଥୋଡ଼ା ସମୟ ନିଅନ୍ତୁ। ଏହା ଆମକୁ ଆପଣଙ୍କ ଦାବିକୁ ସତ୍ୟାପିତ କରିବାରେ ସାହାଯ୍ୟ କରିବ। ଫୋନ୍ ନମ୍ବର (ଆବଶ୍ୟକ) ବିବରଣୀ ଏବଂ ସହି (ଆବଶ୍ୟକ) ଦାବି ଦାଖଲ କରନ୍ତୁ ଯଦି ଆପଣଙ୍କ ପାଖରେ DCMA କିମ୍ବା ଅନ୍ୟ କୌଣସି କପିରାଇଟ ଦାବି ଅଛି, ଦୟାକରି ଏହି ଫର୍ମକୁ ସଠିକ ଭାବରେ ପୂରଣ କରନ୍ତୁ। ଯଦି ଆପଣ କୌଣସି ସମସ୍ୟାରେ ପଡ଼ନ୍ତି, ଆମର ନିର୍ଦ୍ଦିଷ୍ଟ DMCA ଠିକଣାରେ ଯୋଗାଯୋଗ କରନ୍ତୁ: %(email)s। ଏହି ଠିକଣାକୁ ଇମେଲ୍ କରାଯାଇଥିବା ଦାବିଗୁଡ଼ିକୁ ପ୍ରକ୍ରିୟାକରଣ କରାଯିବ ନାହିଁ, ଏହା କେବଳ ପ୍ରଶ୍ନଗୁଡ଼ିକ ପାଇଁ ଅଟେ। ଦୟାକରି ନିମ୍ନର ଫର୍ମକୁ ବ୍ୟବହାର କରି ଆପଣଙ୍କ ଦାବିଗୁଡ଼ିକ ଦାଖଲ କରନ୍ତୁ। DMCA / କପିରାଇଟ ଦାବି ଫର୍ମ ଆନାର ଆର୍କାଇଭ୍ ଉଦାହରଣ ରେକର୍ଡ୍ ଆନାର ଆର୍କାଇଭ୍ ଦ୍ୱାରା ଟୋରେଣ୍ଟ୍ସ ଆନାର ଆର୍କାଇଭ୍ କଣ୍ଟେନର୍ସ ଫର୍ମାଟ୍ ମେଟାଡାଟା ଆମଦାନି ପାଇଁ ସ୍କ୍ରିପ୍ଟ୍ସ ଯଦି ଆପଣ ଏହି ଡାଟାସେଟ୍‌କୁ <a %(a_archival)s>ଆର୍କାଇଭାଲ</a> କିମ୍ବା <a %(a_llm)s>LLM ଟ୍ରେନିଂ</a> ପାଇଁ ମିରର୍ କରିବାରେ ରୁଚି ରଖନ୍ତି, ଦୟାକରି ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଶେଷ ଅଦ୍ୟତନ: %(date)s ମୁଖ୍ୟ %(source)s ୱେବସାଇଟ୍ ମେଟାଡାଟା ଡକ୍ୟୁମେଣ୍ଟେସନ୍ (ଅଧିକାଂଶ ଫିଲ୍ଡ୍ସ) ଆନାର ଆର୍କାଇଭ୍ ଦ୍ୱାରା ମିରର୍ କରାଯାଇଥିବା ଫାଇଲ୍: %(count)s (%(percent)s%%) ସମ୍ପଦା ମୋଟ ଫାଇଲ୍: %(count)s ମୋଟ ଫାଇଲସାଇଜ୍: %(size)s ଏହି ତଥ୍ୟ ସମ୍ବନ୍ଧରେ ଆମର ବ୍ଲଗ୍ ପୋଷ୍ଟ <a %(duxiu_link)s>Duxiu</a> ହେଉଛି ଏକ ବିଶାଳ ସ୍କାନ୍ କରାଯାଇଥିବା ପୁସ୍ତକ ଡାଟାବେସ୍, ଯାହାକି <a %(superstar_link)s>ସୁପରଷ୍ଟାର ଡିଜିଟାଲ ଲାଇବ୍ରେରୀ ଗ୍ରୁପ୍</a> ଦ୍ୱାରା ସୃଷ୍ଟି କରାଯାଇଛି। ଅଧିକାଂଶ ପୁସ୍ତକ ଗୁଡ଼ିକ ଏକାଡେମିକ୍ ପୁସ୍ତକ, ଯାହାକି ବିଶ୍ୱବିଦ୍ୟାଳୟ ଏବଂ ପୁସ୍ତକାଳୟଗୁଡ଼ିକୁ ଡିଜିଟାଲ ଭାବରେ ଉପଲବ୍ଧ କରାଇବା ପାଇଁ ସ୍କାନ୍ କରାଯାଇଛି। ଆମର ଇଂରାଜୀ-ଭାଷୀ ପାଠକମାନଙ୍କ ପାଇଁ, <a %(princeton_link)s>ପ୍ରିନ୍ସଟନ୍</a> ଏବଂ <a %(uw_link)s>ୟୁନିଭର୍ସିଟି ଅଫ୍ ୱାସିଙ୍ଗଟନ୍</a> ଭଲ ଅଭିଧାନ ରଖିଛନ୍ତି। ଏକ ଉତ୍କୃଷ୍ଟ ଲେଖା ମଧ୍ୟ ଅଛି ଯାହା ଅଧିକ ପୃଷ୍ଠଭୂମି ଦେଇଛି: <a %(article_link)s>“ଡିଜିଟାଇଜିଂ ଚାଇନିଜ୍ ପୁସ୍ତକ: ଏକ କେସ୍ ଷ୍ଟଡି ଅଫ୍ ସୁପରଷ୍ଟାର DuXiu ସ୍କଲାର ସର୍ଚ୍ ଇଞ୍ଜିନ୍”</a>। Duxiu ର ପୁସ୍ତକଗୁଡ଼ିକ ଚାଇନିଜ୍ ଇଣ୍ଟରନେଟରେ ଦୀର୍ଘ ସମୟ ଧରି ପାଇରେଟ୍ ହୋଇଛି। ସାଧାରଣତଃ ସେଗୁଡ଼ିକ ପୁନର୍ବିକ୍ରେତାମାନଙ୍କ ଦ୍ୱାରା ଏକ ଡଲାରରୁ କମ୍ ମୂଲ୍ୟରେ ବିକ୍ରି ହୋଇଥାଏ। ସେଗୁଡ଼ିକ ସାଧାରଣତଃ ଗୁଗୁଲ୍ ଡ୍ରାଇଭ୍‌ର ଚାଇନିଜ୍ ସମାନରେ ବଣ୍ଟନ କରାଯାଇଥାଏ, ଯାହାକି ବହୁତ ସମୟ ଧରି ଅଧିକ ସ୍ଥାନ ସଂରକ୍ଷଣ ପାଇଁ ହ୍ୟାକ୍ କରାଯାଇଛି। କିଛି ପ୍ରାକ୍ରିୟାତ୍ମକ ବିବରଣୀ <a %(link1)s>ଏଠାରେ</a> ଏବଂ <a %(link2)s>ଏଠାରେ</a> ମିଳିପାରେ। ଯଦିଓ ପୁସ୍ତକଗୁଡ଼ିକ ଅର୍ଦ୍ଧ-ସାର୍ବଜନିକ ଭାବରେ ବଣ୍ଟନ କରାଯାଇଛି, ସେଗୁଡ଼ିକୁ ବଲ୍କରେ ପାଇବା ଏକଦମ କଷ୍ଟକର। ଆମେ ଏହାକୁ ଆମର TODO-ଲିଷ୍ଟରେ ଉଚ୍ଚ ସ୍ଥାନରେ ରଖିଥିଲୁ, ଏବଂ ଏହା ପାଇଁ ଅନେକ ମାସ ପୂର୍ଣ୍ଣ ସମୟର କାମ ଆବଣ୍ଟିତ କରିଥିଲୁ। ତଥାପି, 2023 ଶେଷରେ ଏକ ଅବିଶ୍ୱସନୀୟ, ଅଦ୍ଭୁତ, ଏବଂ ପ୍ରତିଭାଶାଳୀ ସେବାକ ଆମ ସହିତ ଯୋଗାଯୋଗ କଲେ, ତାଙ୍କର କହିବା ଥିଲା ଯେ ସେମାନେ ଏହା ସବୁ କାମ ଆଗରୁ କରିଛନ୍ତି — ବହୁତ ବ୍ୟୟରେ। ସେମାନେ ଆମ ସହିତ ପୂର୍ଣ୍ଣ ସଂଗ୍ରହ ଅଂଶୀଦାର କରିଥିଲେ, ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣର ଆଶ୍ୱାସନ ବ୍ୟତୀତ କିଛି ମଧ୍ୟ ଆଶା କରିନଥିଲେ। ସତ୍ୟରେ ଅଦ୍ଭୁତ। ଆମ ସେବକମାନଙ୍କରୁ ଅଧିକ ତଥ୍ୟ (କଚ୍ଚା ଟିପ୍ପଣୀ): ଆମ <a %(a_href)s>ବ୍ଲଗ୍ ପୋଷ୍ଟ</a> ରୁ ଅନୁକୂଳିତ। DuXiu 读秀 %(count)s ଫାଇଲ୍ %(count)s ଫାଇଲ୍ଗୁଡ଼ିକ ଏହି ଡାଟାସେଟ <a %(a_datasets_openlib)s>Open Library ଡାଟାସେଟ</a> ସହିତ ଘନିଷ୍ଠ ଭାବରେ ସମ୍ପର୍କିତ। ଏହାରେ ସମସ୍ତ ମେଟାଡାଟା ଏବଂ IA’s Controlled Digital Lending Library ରୁ ବହୁ ଫାଇଲ୍ ଗୁଡ଼ିକର ସ୍କ୍ରାପ୍ ଅଛି। ଅପଡେଟଗୁଡ଼ିକ <a %(a_aac)s>Anna’s Archive Containers ଫର୍ମାଟ</a> ରେ ମୁକ୍ତି ପାଉଛି। ଏହି ରେକର୍ଡଗୁଡ଼ିକ Open Library ଡାଟାସେଟରୁ ପ୍ରତ୍ୟକ୍ଷ ଭାବରେ ଉଲ୍ଲେଖ କରାଯାଇଛି, କିନ୍ତୁ Open Library ରେ ନଥିବା ରେକର୍ଡଗୁଡ଼ିକୁ ମଧ୍ୟ ସମ୍ମିଳିତ କରିଛି। ଆମ ପାଖରେ ବର୍ଷଗୁଡ଼ିକ ଧରି ସମୁଦାୟ ସଦସ୍ୟମାନଙ୍କ ଦ୍ୱାରା ସ୍କ୍ରାପ୍ କରାଯାଇଥିବା ଅନେକ ଡାଟା ଫାଇଲ୍ ମଧ୍ୟ ଅଛି। ସଂଗ୍ରହ ଦୁଇଟି ଅଂଶରେ ଗଠିତ। ସମସ୍ତ ଡାଟା ପାଇଁ ଆପଣଙ୍କୁ ଦୁଇଟି ଅଂଶ ଆବଶ୍ୟକ (ସୁପରସିଡେଡ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଛାଡ଼ି, ଯାହା ଟୋରେଣ୍ଟ ପୃଷ୍ଠାରେ କ୍ରସ୍ ହୋଇଛି)। ଡିଜିଟାଲ୍ ଲେଣ୍ଡିଂ ଲାଇବ୍ରେରୀ ଆମର ପ୍ରଥମ ମୁକ୍ତି, ଯାହା ଆମେ <a %(a_aac)s>ଆନାର ଆର୍କାଇଭ୍ କଣ୍ଟେନର୍ସ (AAC) ଫର୍ମାଟ୍</a> ଉପରେ ଆଧାରିତ କରିଥିଲୁ। ଏହାରେ ମେଟାଡାଟା (json ଏବଂ xml ରୂପରେ), pdfs (acsm ଏବଂ lcpdf ଡିଜିଟାଲ୍ ଲେଣ୍ଡିଂ ସିଷ୍ଟମ୍ ରୁ), ଏବଂ କଭର୍ ଥମ୍ନେଲ୍ସ ରହିଛି। AAC ବ୍ୟବହାର କରି ନୂତନ ମୁକ୍ତିଗୁଡିକ, ମେଟାଡାଟା କେବଳ 2023-01-01 ପରେ ଥିବା ଟାଇମସ୍ଟାମ୍ପ ସହିତ ରହିଛି, କାରଣ ଅନ୍ୟାନ୍ୟ ଆଗରୁ “ia” ଦ୍ୱାରା ଆବରଣ କରାଯାଇଛି। ଏହା ସହିତ ସମସ୍ତ pdf ଫାଇଲ୍, ଏଥିରେ acsm ଏବଂ “bookreader” (IA’s web reader) ଲେଣ୍ଡିଂ ସିଷ୍ଟମ୍ ରୁ ଆସିଛି। ନାମ ଠିକ୍ ନ ଥିଲେ ମଧ୍ୟ, ଆମେ bookreader ଫାଇଲ୍ଗୁଡିକୁ ia2_acsmpdf_files ସଂଗ୍ରହରେ ରଖୁଛୁ, କାରଣ ସେମାନେ ପରସ୍ପର ବିରୋଧୀ। IA ନିୟନ୍ତ୍ରିତ ଡିଜିଟାଲ୍ ଲେଣ୍ଡିଂ ୯୮%%+ ଫାଇଲଗୁଡ଼ିକ ଖୋଜିବା ଯୋଗ୍ୟ। ଆମର ମିଶନ୍ ହେଉଛି ସମସ୍ତ ପୁସ୍ତକଗୁଡ଼ିକୁ (ଏବଂ ପେପର୍, ମ୍ୟାଗାଜିନ୍ ଇତ୍ୟାଦି) ଆର୍କାଇଭ୍ କରିବା ଏବଂ ସେଗୁଡ଼ିକୁ ବ୍ୟାପକ ଭାବରେ ପ୍ରାପ୍ୟ କରାଇବା। ଆମେ ବିଶ୍ୱାସ କରୁଛୁ ଯେ ସମସ୍ତ ପୁସ୍ତକ ଦୂରଦୂରନ୍ତରେ ମିରର୍ ହେବା ଉଚିତ୍, ପୁନର୍ବୃତ୍ତି ଏବଂ ସ୍ଥିରତା ନିଶ୍ଚିତ କରିବା ପାଇଁ। ଏହି କାରଣରୁ ଆମେ ବିଭିନ୍ନ ସ୍ରୋତରୁ ଫାଇଲ୍ଗୁଡ଼ିକୁ ଏକତ୍ର କରୁଛୁ। କିଛି ସ୍ରୋତ ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଏବଂ ବଲ୍କରେ ମିରର୍ କରାଯାଇପାରେ (ଯଥା Sci-Hub)। ଅନ୍ୟମାନେ ବନ୍ଦ ଏବଂ ସୁରକ୍ଷିତ ଅଛନ୍ତି, ତେଣୁ ଆମେ ସେମାନଙ୍କର ପୁସ୍ତକଗୁଡ଼ିକୁ “ମୁକ୍ତ” କରିବା ପାଇଁ ସ୍କ୍ରାପ୍ କରିବାକୁ ଚେଷ୍ଟା କରୁଛୁ। ଅନ୍ୟମାନେ ମଧ୍ୟ ଏହାର ମଧ୍ୟରେ କେଉଁଠି ଅଛନ୍ତି। ଆମ ସମସ୍ତ ତଥ୍ୟ <a %(a_torrents)s>ଟୋରେଣ୍ଟ୍</a> କରାଯାଇପାରେ, ଏବଂ ଆମ ସମସ୍ତ ମେଟାଡାଟା <a %(a_anna_software)s>ଜେନେରେଟ୍</a> କିମ୍ବା <a %(a_elasticsearch)s>ଡାଉନଲୋଡ୍</a> କରାଯାଇପାରେ ElasticSearch ଏବଂ MariaDB ଡାଟାବେସ୍ ଭାବରେ। କଚା ତଥ୍ୟକୁ JSON ଫାଇଲ୍ ମାଧ୍ୟମରେ ମାନୁଆଲ୍ ଭାବରେ ପରୀକ୍ଷା କରାଯାଇପାରେ <a %(a_dbrecord)s>ଏଠାରେ</a>। ମେଟାଡାଟା ଆଇଏସବିଏନ ୱେବସାଇଟ ଶେଷ ଅଦ୍ୟତନ: %(isbn_country_date)s (%(link)s) ସମ୍ପଦା ଆନ୍ତର୍ଜାତୀୟ ଆଇଏସବିଏନ ଏଜେନ୍ସୀ ନିୟମିତ ଭାବରେ ଏହାର ଅନୁମୋଦିତ ଦେଶୀୟ ଆଇଏସବିଏନ ଏଜେନ୍ସୀଗୁଡ଼ିକୁ ଆବଣ୍ଟନ କରିଥିବା ରେଞ୍ଜଗୁଡ଼ିକ ମୁକ୍ତ କରେ। ଏହାରୁ ଆମେ ଏହି ଆଇଏସବିଏନ କେଉଁ ଦେଶ, ଅଞ୍ଚଳ, କିମ୍ବା ଭାଷା ଗୋଷ୍ଠୀର ହେଉଛି ତାହା ନିର୍ଣ୍ଣୟ କରିପାରିବା। ଆମେ ବର୍ତ୍ତମାନ ଏହି ତଥ୍ୟକୁ ପ୍ରତ୍ୟକ୍ଷ ଭାବରେ ବ୍ୟବହାର କରୁନାହିଁ, <a %(a_isbnlib)s>isbnlib</a> ପାଇଥନ୍ ଲାଇବ୍ରେରି ମାଧ୍ୟମରେ ପରୋକ୍ଷ ଭାବରେ ବ୍ୟବହାର କରୁଛୁ। ଆଇଏସବିଏନ ଦେଶ ସୂଚନା ଏହା ହେଉଛି ସେପ୍ଟେମ୍ବର 2022 ଦରମିଆନ isbndb.com କୁ ଅନେକ କଲ୍‌ଗୁଡ଼ିକର ଏକ ଡମ୍ପ୍। ଆମେ ସମସ୍ତ ଆଇଏସବିଏନ ରେଞ୍ଜଗୁଡ଼ିକୁ ଆବରଣ କରିବାକୁ ଚେଷ୍ଟା କରିଥିଲୁ। ଏହା ଲାଗଭାଗ 30.9 ମିଲିୟନ ରେକର୍ଡ୍। ସେମାନେ ତାଙ୍କର ୱେବସାଇଟରେ ଦାବି କରନ୍ତି ଯେ ସେମାନଙ୍କ ପାଖରେ ବାସ୍ତବରେ 32.6 ମିଲିୟନ ରେକର୍ଡ୍ ଅଛି, ତେଣୁ ଆମେ କିଛି ଭୁଲ କରିଥାନ୍ତି କିମ୍ବା <em>ସେମାନେ</em> କିଛି ଭୁଲ କରୁଥାନ୍ତି। JSON ପ୍ରତିକ୍ରିୟାଗୁଡ଼ିକ ତାଙ୍କର ସର୍ଭରରୁ ପ୍ରାୟ ର ଅବସ୍ଥାରେ ଅଛି। ଆମେ ଯେଉଁ ତଥ୍ୟ ଗୁଣାତ୍ମକ ସମସ୍ୟା ଦେଖିଲୁ, ସେହି ଆଇଏସବିଏନ-13 ସଂଖ୍ୟାଗୁଡ଼ିକ ପାଇଁ ଯେଉଁଗୁଡ଼ିକ ଏକ ଭିନ୍ନ ପ୍ରିଫିକ୍ସ “978-” ରୁ ଆରମ୍ଭ ହୁଏ, ସେମାନେ ଏକ “isbn” କ୍ଷେତ୍ରକୁ ଅନ୍ତର୍ଭୁକ୍ତ କରନ୍ତି ଯାହା ସରଳତାରେ ଆଇଏସବିଏନ-13 ସଂଖ୍ୟାର ପ୍ରଥମ 3 ସଂଖ୍ୟାକୁ କାଟି ଦିଏ (ଏବଂ ଚେକ୍ ଡିଜିଟ୍ ପୁନଃ ଗଣନା କରାଯାଇଛି)। ଏହା ସ୍ପଷ୍ଟତାରେ ଭୁଲ, କିନ୍ତୁ ସେମାନେ ଏହାକୁ ଏଭଳି କରନ୍ତି, ତେଣୁ ଆମେ ଏହାକୁ ପରିବର୍ତ୍ତନ କରିନାହିଁ। ଆପଣ ଯେଉଁ ଅନ୍ୟ ସମ୍ଭାବ୍ୟ ସମସ୍ୟା ସହିତ ସମ୍ମୁଖୀନ ହୋଇପାରନ୍ତି, ସେହି ତଥ୍ୟ ଯେ “isbn13” କ୍ଷେତ୍ରର ଡୁପ୍ଲିକେଟ୍ ଅଛି, ତେଣୁ ଆପଣ ଏହାକୁ ଏକ ଡାଟାବେସରେ ପ୍ରାଥମିକ କୀ ଭାବରେ ବ୍ୟବହାର କରିପାରିବେ ନାହିଁ। “isbn13”+“isbn” କ୍ଷେତ୍ରଗୁଡ଼ିକ ଯୁକ୍ତ ହେଲେ ଏକାକୀ ଲାଗୁଛି। ମୁକ୍ତି 1 (2022-10-31) ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟ୍ ପଛରେ ଅଛି (ଯଦିଓ ID ~4-6M ଟୋରେଣ୍ଟ୍ ହୋଇନାହିଁ କାରଣ ସେମାନେ ଆମର Zlib ଟୋରେଣ୍ଟ୍ ସହିତ ଓଭରଲାପ୍ କରୁଛନ୍ତି)। ଆମର କମିକ୍ସ ପୁସ୍ତକ ମୁକ୍ତି ବିଷୟରେ ବ୍ଲଗ୍ ପୋଷ୍ଟ Anna’s Archiveରେ କମିକ୍ସ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ବିଭିନ୍ନ ଲାଇବ୍ରେରି ଜେନେସିସ ଫର୍କଗୁଡ଼ିକର ପୃଷ୍ଠପଟ ପାଇଁ, <a %(a_libgen_rs)s>Libgen.rs</a> ପୃଷ୍ଠା ଦେଖନ୍ତୁ। Libgen.li ରେ Libgen.rs ର ଅଧିକାଂଶ ସମଗ୍ରୀ ଏବଂ ମେଟାଡାଟା ଅଛି, କିନ୍ତୁ ଏହାରେ କିଛି ସଂଗ୍ରହ ଅଛି, ଯାହାକି କମିକ୍ସ, ପତ୍ରିକା ଏବଂ ମାନକ ଡକ୍ୟୁମେଣ୍ଟଗୁଡ଼ିକ। ଏହା <a %(a_scihub)s>Sci-Hub</a> କୁ ଏହାର ମେଟାଡାଟା ଏବଂ ସନ୍ଧାନ ଇଞ୍ଜିନ୍ ସହିତ ଏକତ୍ର କରିଛି, ଯାହାକୁ ଆମେ ଆମର ଡାଟାବେସ ପାଇଁ ବ୍ୟବହାର କରୁଛୁ। ଏହି ଲାଇବ୍ରେରି ପାଇଁ ମେଟାଡାଟା ମୁକ୍ତ ଭାବରେ <a %(a_libgen_li)s>libgen.li ରେ</a> ଉପଲବ୍ଧ ଅଛି। ତଥାପି, ଏହି ସର୍ଭର ଧୀର ଏବଂ ଭଙ୍ଗା ଯୋଗାଯୋଗକୁ ପୁନଃ ଆରମ୍ଭ କରିବାକୁ ସମର୍ଥ ନୁହେଁ। ସେହି ଫାଇଲଗୁଡ଼ିକ <a %(a_ftp)s>ଏକ FTP ସର୍ଭର</a> ରେ ମଧ୍ୟ ଉପଲବ୍ଧ ଅଛି, ଯାହା ଭଲ କାମ କରେ। ଅପ୍ରବନ୍ଧ ମଧ୍ୟ ଅନ୍ୟ ଦିଗକୁ ଯାଇଛି, କିନ୍ତୁ ନୂତନ ଟୋରେଣ୍ଟ ଛଡ଼ା। ଏହା 2022 ଆରମ୍ଭରୁ ଘଟିଛି ବୋଲି ଦେଖାଯାଉଛି, ଯଦିଓ ଆମେ ଏହାକୁ ସତ୍ୟାପିତ କରିନାହିଁ। Libgen.li ପରିଚାଳକଙ୍କ ଅନୁସାରେ, “fiction_rus” (ରୁଷ ଉପନ୍ୟାସ) ସଂଗ୍ରହ <a %(a_booktracker)s>booktracker.org</a> ଦ୍ୱାରା ନିୟମିତ ଭାବରେ ମୁକ୍ତି ପାଇଥିବା ଟୋରେଣ୍ଟ ଦ୍ୱାରା ଆବରଣ କରାଯିବା ଉଚିତ, ବିଶେଷତଃ <a %(a_flibusta)s>flibusta</a> ଏବଂ <a %(a_librusec)s>lib.rus.ec</a> ଟୋରେଣ୍ଟ (ଯାହାକୁ ଆମେ <a %(a_torrents)s>ଏଠାରେ</a> ମିରର କରିଛୁ, ଯଦିଓ ଆମେ ଏପର୍ଯ୍ୟନ୍ତ କୌଣସି ଟୋରେଣ୍ଟ କୌଣସି ଫାଇଲ୍ ସହିତ ସମ୍ପର୍କିତ ହେଉଛି କି ନାହିଁ ନିର୍ଦ୍ଧାରଣ କରିନାହିଁ)। ଉପନ୍ୟାସ ସଂଗ୍ରହର ନିଜସ୍ୱ ଟୋରେଣ୍ଟ ଅଛି (ଏକ <a %(a_href)s>Libgen.rs</a> ରୁ ବିଭିନ୍ନ) %(start)s ରୁ ଆରମ୍ଭ ହୋଇଛି। କିଛି ରେଞ୍ଜ ଯାହାରେ ଟୋରେଣ୍ଟ ନାହିଁ (ଯଥା ଉପନ୍ୟାସ ରେଞ୍ଜ f_3463000 ରୁ f_4260000) ସମ୍ଭାବ୍ୟ ଭାବେ Z-Library (କିମ୍ବା ଅନ୍ୟାନ୍ୟ ଡୁପ୍ଲିକେଟ୍) ଫାଇଲ୍ ଅଟେ, ଯଦିଓ ଆମେ କିଛି ଡିଡୁପ୍ଲିକେସନ୍ କରିବାକୁ ଚାହୁଁଛୁ ଏବଂ ଏହି ରେଞ୍ଜରେ lgli-ଏକାକୀ ଫାଇଲ୍ ପାଇଁ ଟୋରେଣ୍ଟ ତିଆରି କରିବାକୁ ଚାହୁଁଛୁ। ସମସ୍ତ ସଂଗ୍ରହ ପାଇଁ ପରିସଂଖ୍ୟାନ <a %(a_href)s>libgen ର ୱେବସାଇଟ୍</a> ରେ ମିଳିବ। ଅଧିକାଂଶ ଅତିରିକ୍ତ ବିଷୟବସ୍ତୁ ପାଇଁ ଟୋରେଣ୍ଟ ଉପଲବ୍ଧ ଅଛି, ବିଶେଷତଃ କମିକ୍ସ, ମ୍ୟାଗାଜିନ୍, ଏବଂ ମାନକ ଡକ୍ୟୁମେଣ୍ଟ ପାଇଁ ଟୋରେଣ୍ଟ ଆନାର ଆର୍କାଇଭ୍ ସହିତ ମିଶିତ ହୋଇ ମୁକ୍ତି ପାଇଛି। ଏହା ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ “libgen.is”କୁ ସୂଚିତ କରୁଥିବା ଟୋରେଣ୍ଟ ଫାଇଲଗୁଡ଼ିକ ସ୍ପଷ୍ଟ ଭାବରେ <a %(a_libgen)s>Libgen.rs</a>ର ମିରର୍ ଅଟେ (“.is” ହେଉଛି Libgen.rs ଦ୍ୱାରା ବ୍ୟବହୃତ ଏକ ଭିନ୍ନ ଡୋମେନ୍)। ମେଟାଡାଟା ବ୍ୟବହାର କରିବାରେ ଏକ ସାହାଯ୍ୟକାରୀ ସମ୍ପଦ ହେଉଛି <a %(a_href)s>ଏହି ପୃଷ୍ଠା</a>। %(icon)s ତାଙ୍କର “fiction_rus” ସଂଗ୍ରହ (ରୁଷ ଉପନ୍ୟାସ) କୌଣସି ନିର୍ଦ୍ଦିଷ୍ଟ ଟୋରେଣ୍ଟ ନାହିଁ, କିନ୍ତୁ ଅନ୍ୟମାନଙ୍କ ଦ୍ୱାରା ଟୋରେଣ୍ଟ ଦ୍ୱାରା ଆବରଣ କରାଯାଇଛି, ଏବଂ ଆମେ ଏକ <a %(fiction_rus)s>ମିରର</a> ରଖିଛୁ। ଆନାର ଆର୍କାଇଭ୍ ଉପରେ ରୁଷ ଉପନ୍ୟାସ ଟୋରେଣ୍ଟ Anna’s Archiveରେ କାଳ୍ପନିକ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଚର୍ଚ୍ଚା ଫୋରମ୍ ମେଟାଡାଟା FTP ମାଧ୍ୟମରେ ମେଟାଡାଟା Anna’s Archiveରେ ପତ୍ରିକା ଟୋରେଣ୍ଟଗୁଡ଼ିକ ମେଟାଡାଟା କ୍ଷେତ୍ର ସୂଚନା ଅନ୍ୟାନ୍ୟ ଟୋରେଣ୍ଟର ମିରର୍ (ଏବଂ ବିଶିଷ୍ଟ କାଳ୍ପନିକ ଏବଂ କମିକ୍ସ ଟୋରେଣ୍ଟଗୁଡ଼ିକ) ଆନାର ଆର୍କାଇଭ୍ ଉପରେ ମାନକ ଡକ୍ୟୁମେଣ୍ଟ ଟୋରେଣ୍ଟ Libgen.li Anna’s Archive ଦ୍ୱାରା ଟୋରେଣ୍ଟଗୁଡ଼ିକ (ପୁସ୍ତକ ମୁଖପୃଷ୍ଠା) Library Genesis ତାଙ୍କର ତଥ୍ୟଗୁଡ଼ିକ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ବହୁତ ଉଦାରତାର ସହିତ ଉପଲବ୍ଧ କରାଇବା ପାଇଁ ପରିଚିତ। ଆମର Libgen ସଂଗ୍ରହ ତାଙ୍କ ସହିତ ମିଶିତା ତଥ୍ୟଗୁଡ଼ିକ ଅନ୍ତର୍ଭୁକ୍ତ କରେ ଯାହାକି ସେମାନେ ପ୍ରତ୍ୟକ୍ଷ ଭାବେ ମୁକ୍ତି କରନ୍ତି ନାହିଁ। Library Genesis ସହିତ କାମ କରିବା ପାଇଁ ସମସ୍ତଙ୍କୁ ଅନେକ ଧନ୍ୟବାଦ! ପୁସ୍ତକ ମୁଖପୃଷ୍ଠା ମୁକ୍ତି ବିଷୟରେ ଆମର ବ୍ଲଗ୍ ଏହି ପୃଷ୍ଠା “.rs” ସଂସ୍କରଣ ବିଷୟରେ ଅଟେ। ଏହା ତାର ମେଟାଡାଟା ଏବଂ ତାର ପୁସ୍ତକ ସଂଗ୍ରହର ପୂରା ବିଷୟବସ୍ତୁକୁ ନିୟମିତ ଭାବରେ ପ୍ରକାଶ କରିବା ପାଇଁ ପରିଚିତ। ଏହାର ପୁସ୍ତକ ସଂଗ୍ରହକୁ କାଳ୍ପନିକ ଏବଂ ଅକାଳ୍ପନିକ ଅଂଶରେ ବିଭାଜିତ କରାଯାଇଛି। ମେଟାଡାଟା ବ୍ୟବହାର କରିବାରେ ଏକ ସାହାଯ୍ୟକାରୀ ସମ୍ପଦ ହେଉଛି <a %(a_metadata)s>ଏହି ପୃଷ୍ଠା</a> (ଆଇପି ରେଞ୍ଜଗୁଡ଼ିକୁ ବ୍ଲକ୍ କରେ, ଭିପିଏନ୍ ଆବଶ୍ୟକ ହୋଇପାରେ)। 2024-03 ମାସରୁ ଆରମ୍ଭ କରି, ନୂଆ ଟୋରେଣ୍ଟଗୁଡ଼ିକ <a %(a_href)s>ଏହି ଫୋରମ୍ ଥ୍ରେଡ୍</a>ରେ ପୋଷ୍ଟ କରାଯାଉଛି (ଆଇପି ରେଞ୍ଜଗୁଡ଼ିକ ଅବରୋଧ କରେ, ଭିପିଏନ୍ ଆବଶ୍ୟକ ହୋଇପାରେ)। Anna’s Archive ରେ ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Libgen.rs ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Libgen.rs ଆଲୋଚନା ଫୋରମ୍ Libgen.rs ମେଟାଡାଟା Libgen.rs ମେଟାଡାଟା ଫିଲ୍ଡ ସୂଚନା Libgen.rs ନନ୍-ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Anna’s Archive ରେ ନନ୍-ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଫିକ୍ସନ୍ ପୁସ୍ତକ ପାଇଁ %(example)s। ଏହି <a %(blog_post)s>ପ୍ରଥମ ମୁକ୍ତି</a> ବହୁତ ଛୋଟ: Libgen.rs ଫୋର୍କରୁ ପ୍ରାୟ 300GB ପୁସ୍ତକ ମୁଖପୃଷ୍ଠା, ଫିକ୍ସନ୍ ଏବଂ ନନ୍-ଫିକ୍ସନ୍ ଉଭୟ। ସେମାନେ libgen.rs ରେ ଯେପରି ଦେଖାଯାଏ ସେହିପରି ଭାବରେ ସଂଗଠିତ ହୋଇଛନ୍ତି, ଉଦାହରଣ ସ୍ୱରୂପ: ନନ୍-ଫିକ୍ସନ୍ ପୁସ୍ତକ ପାଇଁ %(example)s। Z-Library ସଂଗ୍ରହ ସହିତ ଏକେପରି, ଆମେ ସେମାନଙ୍କୁ ସମସ୍ତଙ୍କୁ ଏକ ବଡ଼ .tar ଫାଇଲ୍ ଭିତରେ ରଖିଛୁ, ଯାହାକି ଯଦି ଆପଣ ସଂଗ୍ରହକୁ ପ୍ରତ୍ୟକ୍ଷ ଭାବରେ ସର୍ଭ କରିବାକୁ ଚାହୁଁଛନ୍ତି ତେବେ <a %(a_ratarmount)s>ratarmount</a> ବ୍ୟବହାର କରି ମାଉଣ୍ଟ କରାଯାଇପାରେ। ମୁକ୍ତି 1 (%(date)s) ବିଭିନ୍ନ Library Genesis (କିମ୍ବା “Libgen”) ଫର୍କଗୁଡ଼ିକର ସଂକ୍ଷିପ୍ତ କାହାଣୀ ହେଉଛି, ସମୟ କ୍ରମେ, Library Genesis ସହିତ ଜଡିତ ବିଭିନ୍ନ ଲୋକମାନେ ଏକ ମତଭେଦରେ ପଡ଼ିଲେ, ଏବଂ ସେମାନେ ତାଙ୍କର ଆଲଗା ମାର୍ଗରେ ଯାଇଥିଲେ। ଏହି <a %(a_mhut)s>ଫୋରମ୍ ପୋଷ୍ଟ</a> ଅନୁସାରେ, Libgen.li ପ୍ରଥମେ “http://free-books.dontexist.com” ରେ ହୋଷ୍ଟ କରାଯାଇଥିଲା। “.fun” ସଂସ୍କରଣ ମୂଳ ସ୍ଥାପକ ଦ୍ୱାରା ସୃଷ୍ଟି ହୋଇଥିଲା। ଏହାକୁ ଏକ ନୂତନ, ଅଧିକ ବିତରିତ ସଂସ୍କରଣ ପାଇଁ ପୁନଃସଂରଚନା କରାଯାଉଛି। <a %(a_li)s>“.li” ସଂସ୍କରଣ</a>ରେ ଏକ ବିଶାଳ କମିକ୍ସ ସଂଗ୍ରହ ଅଛି, ଏବଂ ଅନ୍ୟାନ୍ୟ ବିଷୟବସ୍ତୁ ଅଛି, ଯାହା ଏପର୍ଯ୍ୟନ୍ତ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ବଲ୍କ୍ ଡାଉନଲୋଡ୍ ପାଇଁ ଉପଲବ୍ଧ ନୁହେଁ। ଏହାରେ କାଳ୍ପନିକ ପୁସ୍ତକଗୁଡ଼ିକର ଏକ ଅଲଗା ଟୋରେଣ୍ଟ ସଂଗ୍ରହ ଅଛି, ଏବଂ ଏହାର ଡାଟାବେସରେ <a %(a_scihub)s>Sci-Hub</a>ର ମେଟାଡାଟା ଅଛି। “.rs” ସଂସ୍କରଣରେ ଅତ୍ୟନ୍ତ ସମାନ ତଥ୍ୟ ଅଛି, ଏବଂ ସେମାନେ ସାଧାରଣତଃ ତାଙ୍କର ସଂଗ୍ରହକୁ ବଲ୍କ୍ ଟୋରେଣ୍ଟରେ ମୁକ୍ତି କରନ୍ତି। ଏହାକୁ ଏକ “କାଳ୍ପନିକ” ଏବଂ ଏକ “ଅକାଳ୍ପନିକ” ବିଭାଗରେ ଭାଗ କରାଯାଇଛି। ମୂଳତଃ “http://gen.lib.rus.ec” ରେ ଥିଲା। <a %(a_zlib)s>Z-Library</a> କିଛି ଅର୍ଥରେ Library Genesisର ଏକ ଫର୍କ ମଧ୍ୟ ଅଟେ, ଯଦିଓ ସେମାନେ ତାଙ୍କର ପ୍ରକଳ୍ପ ପାଇଁ ଏକ ଭିନ୍ନ ନାମ ବ୍ୟବହାର କରିଥିଲେ। Libgen.rs ଆମେ ମେଟାଡାଟା-ମାତ୍ର ଉତ୍ସଗୁଡ଼ିକ ସହିତ ଆମ ସଂଗ୍ରହକୁ ସମୃଦ୍ଧ କରୁଛୁ, ଯାହାକୁ ଆମେ ଫାଇଲଗୁଡ଼ିକ ସହିତ ମେଳ କରିପାରିବା, ଉଦାହରଣ ସ୍ୱରୂପ ISBN ସଂଖ୍ୟା ବା ଅନ୍ୟ ଫିଲ୍ଡଗୁଡ଼ିକ ବ୍ୟବହାର କରି। ନିମ୍ନରେ ସେଗୁଡ଼ିକର ଏକ ସାରାଂଶ ଦିଆଯାଇଛି। ପୁନଃ, ଏହି ଉତ୍ସଗୁଡ଼ିକର କିଛି ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଅଛି, ଯେଉଁଠାରେ ଅନ୍ୟମାନେ ଆମେ ସେଗୁଡ଼ିକୁ ସ୍କ୍ରାପ୍ କରିବାକୁ ପଡ଼େ। ମେଟାଡାଟା ସନ୍ଧାନରେ, ଆମେ ମୂଳ ରେକର୍ଡଗୁଡ଼ିକୁ ଦେଖାଉଛୁ ବୋଲି ଧ୍ୟାନ ଦିଅନ୍ତୁ। ଆମେ କୌଣସି ରେକର୍ଡର ମର୍ଜିଂ କରୁନାହିଁ। ମେଟାଡାଟା-ମାତ୍ର ଉତ୍ସଗୁଡ଼ିକ Open Library ହେଉଛି ପ୍ରତ୍ୟେକ ପୁସ୍ତକକୁ ବିବରଣୀ କରିବା ପାଇଁ Internet Archive ଦ୍ୱାରା ଏକ ଖୋଲା ଉତ୍ସ ପ୍ରକଳ୍ପ। ଏହା ପୃଥିବୀର ସବୁଠାରୁ ବଡ଼ ପୁସ୍ତକ ସ୍କାନିଂ ପ୍ରକ୍ରିୟାଗୁଡ଼ିକ ମଧ୍ୟରୁ ଗୋଟିଏ ରଖିଛି, ଏବଂ ଅନେକ ପୁସ୍ତକ ଡିଜିଟାଲ୍ ଧାର ଦେବା ପାଇଁ ଉପଲବ୍ଧ ଅଛି। ଏହାର ପୁସ୍ତକ ମେଟାଡାଟା କ୍ୟାଟାଲଗ୍ ମାଗଣାରେ ଡାଉନଲୋଡ୍ ପାଇଁ ଉପଲବ୍ଧ ଅଛି, ଏବଂ Anna’s Archive ରେ ଅନ୍ତର୍ଭୁକ୍ତ ହୋଇଛି (ଯଦିଓ ବର୍ତ୍ତମାନ ସନ୍ଧାନରେ ନୁହେଁ, ଯଦି ଆପଣ ଏକ Open Library ID ପାଇଁ ସ୍ପଷ୍ଟ ଭାବରେ ସନ୍ଧାନ କରନ୍ତି ତେବେ ଛାଡ଼ି)। Open Library ନକଲ ଛାଡ଼ିକରି ଶେଷ ଅଦ୍ୟତନ ଫାଇଲ୍ ସଂଖ୍ୟାର ପ୍ରତିଶତ %% AA ଦ୍ୱାରା ମିରର୍ କରାଯାଇଛି / ଟୋରେଣ୍ଟ୍ ଉପଲବ୍ଧ ଆକାର ମୂଳ ନିମ୍ନରେ ଆନାର ଆର୍କାଇଭ୍ରେ ଥିବା ଫାଇଲ୍ଗୁଡ଼ିକର ସ୍ରୋତଗୁଡ଼ିକର ଏକ ତ୍ୱରିତ ସାରାଂଶ ଅଛି। ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ପ୍ରାୟଃ ଏକାଅନ୍ୟରୁ ତଥ୍ୟ ସମନ୍ୱୟ କରିଥାଏ, ତେଣୁ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ମଧ୍ୟରେ ଅନେକ ସମାନତା ରହିଛି। ଏହି କାରଣରୁ ସଂଖ୍ୟାଗୁଡ଼ିକ ମୋଟ ସଂଖ୍ୟାରେ ଯୋଗ ହୁଏ ନାହିଁ। “Anna’s Archive ଦ୍ୱାରା ମିରର୍ ଏବଂ ସିଡ୍ କରାଯାଇଛି” ପ୍ରତିଶତ ଦେଖାଏ କିଏମାନେ ଫାଇଲଗୁଡ଼ିକ ଆମେ ନିଜେ ମିରର୍ କରୁଛୁ। ଆମେ ସେହି ଫାଇଲଗୁଡ଼ିକୁ ବହୁ ପରିମାଣରେ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ସିଡ୍ କରୁଛୁ, ଏବଂ ସେଗୁଡ଼ିକୁ ସହଯୋଗୀ ୱେବସାଇଟଗୁଡ଼ିକ ମାଧ୍ୟମରେ ସିଧାସଳଖ ଡାଉନଲୋଡ୍ ଯୋଗ୍ୟ କରୁଛୁ। ସାରାଂଶ ମୋଟ ଆନା’ସ ଆର୍କାଇଭରେ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Sci-Hub ବିଷୟରେ ଅଧିକ ତଥ୍ୟ ପାଇଁ, ଦୟାକରି ଏହାର <a %(a_scihub)s>ଆଧିକାରିକ ୱେବସାଇଟ</a>, <a %(a_wikipedia)s>ଉଇକିପିଡ଼ିଆ ପୃଷ୍ଠା</a>, ଏବଂ ଏହି <a %(a_radiolab)s>ପଡକାଷ୍ଟ ସାକ୍ଷାତ୍କାର</a>କୁ ଦେଖନ୍ତୁ। ନୋଟ କରନ୍ତୁ ଯେ Sci-Hub 2021 ରୁ <a %(a_reddit)s>ଫ୍ରିଜ୍ ହୋଇଛି</a>। ଏହା ପୂର୍ବରୁ ମଧ୍ୟ ଫ୍ରିଜ୍ ହୋଇଥିଲା, କିନ୍ତୁ 2021 ରେ କିଛି ମିଲିଅନ୍ ପେପର୍ ଯୋଡ଼ାଯାଇଥିଲା। ତଥାପି, କିଛି ସୀମିତ ସଂଖ୍ୟକ ପେପର୍ Libgen “scimag” ସଂଗ୍ରହରେ ଯୋଡ଼ାଯାଉଛି, ଯଦିଓ ନୂତନ ବଲ୍କ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ନୁହେଁ। ଆମେ Sci-Hub ମେଟାଡାଟାକୁ <a %(a_libgen_li)s>Libgen.li</a> ଦ୍ୱାରା ଦିଆଯାଇଥିବା “scimag” ସଂଗ୍ରହରେ ବ୍ୟବହାର କରୁଛୁ। ଆମେ ମଧ୍ୟ <a %(a_dois)s>dois-2022-02-12.7z</a> ଡାଟାସେଟ୍ ବ୍ୟବହାର କରୁଛୁ। ନୋଟ କରନ୍ତୁ ଯେ “smarch” ଟୋରେଣ୍ଟଗୁଡ଼ିକ <a %(a_smarch)s>ବିଲୁପ୍ତ</a> ହୋଇଛି ଏବଂ ଏହିପରିସରରେ ଆମ ଟୋରେଣ୍ଟ ତାଲିକାରେ ଶାମିଲ ନୁହେଁ। Libgen.li ରେ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Libgen.rs ରେ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ମେଟାଡାଟା ଏବଂ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Reddit ରେ ଅପଡେଟ୍ ପଡକାଷ୍ଟ ସାକ୍ଷାତ୍କାର ଉଇକିପିଡ଼ିଆ ପୃଷ୍ଠା Sci-Hub Sci-Hub: 2021 ରୁ ଜମା ହୋଇଛି; ଅଧିକାଂଶ ଟୋରେଣ୍ଟ୍ ମାଧ୍ୟମରେ ଉପଲବ୍ଧ Libgen.li: ସେଥାରୁ ଅଳ୍ପ ଯୋଗ ହୋଇଛି</div> କିଛି ଉତ୍ସ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ତାଙ୍କର ତଥ୍ୟକୁ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ବ୍ୟାପକ ଭାବରେ ସେୟାର କରିବାକୁ ପ୍ରୋତ୍ସାହିତ କରନ୍ତି, ଯେଉଁଠାରେ ଅନ୍ୟମାନେ ସହଜରେ ତାଙ୍କର ସଂଗ୍ରହକୁ ସେୟାର କରନ୍ତି ନାହିଁ। ଏହି ଶେଷ ମାମଲାରେ, ଆନ୍ନାର ଆର୍କାଇଭ୍ ତାଙ୍କର ସଂଗ୍ରହକୁ ସ୍କ୍ରାପ୍ କରିବାକୁ ଚେଷ୍ଟା କରେ, ଏବଂ ସେଗୁଡ଼ିକୁ ଉପଲବ୍ଧ କରାଏ (ଆମର <a %(a_torrents)s>ଟୋରେଣ୍ଟ</a> ପୃଷ୍ଠା ଦେଖନ୍ତୁ)। ମଧ୍ୟରେ ମଧ୍ୟରେ ଏହିପରି ପରିସ୍ଥିତି ମଧ୍ୟ ରହିଛି, ଯେଉଁଠାରେ ଉତ୍ସ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ସେୟାର କରିବାକୁ ଇଚ୍ଛୁକ, କିନ୍ତୁ ସେହି କାମ କରିବାକୁ ସମ୍ପଦ ନାହିଁ। ସେହି ମାମଲାରେ, ଆମେ ମଧ୍ୟ ସାହାଯ୍ୟ କରିବାକୁ ଚେଷ୍ଟା କରୁଛୁ। ନିମ୍ନରେ ଆମେ କିପରି ଭିନ୍ନ ଉତ୍ସ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ ସହିତ ଇଣ୍ଟରଫେସ୍ କରୁଛୁ ତାହାର ଏକ ସାରାଂଶ ଦିଆଯାଇଛି। ମୂଳ ପୁସ୍ତକାଳୟଗୁଡ଼ିକ %(icon)s ବିଭିନ୍ନ ଫାଇଲ୍ ଡାଟାବେସଗୁଡ଼ିକ ଚାଇନିଜ୍ ଇଣ୍ଟରନେଟରେ ଛିଟିଏ ଛାଟିଏ ରହିଛି; ଯଦିଓ ପ୍ରାୟତଃ ପେଡ୍ ଡାଟାବେସ। %(icon)s ଅଧିକାଂଶ ଫାଇଲ୍ଗୁଡ଼ିକ କେବଳ ପ୍ରିମିୟମ୍ BaiduYun ଆକାଉଣ୍ଟ ବ୍ୟବହାର କରି ଉପଲବ୍ଧ; ଧୀର ଡାଉନଲୋଡିଂ ଗତି। %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(duxiu)s>DuXiu ଫାଇଲ୍ସ</a> ସଂଗ୍ରହ ପରିଚାଳନା କରେ। %(icon)s ବିଭିନ୍ନ ମେଟାଡାଟା ଡାଟାବେସଗୁଡ଼ିକ ଚାଇନିଜ୍ ଇଣ୍ଟରନେଟରେ ଛିଟିଏ ଛାଟିଏ ରହିଛି; ଯଦିଓ ପ୍ରାୟତଃ ପେଡ୍ ଡାଟାବେସ। %(icon)s ସେମାନଙ୍କ ସମ୍ପୂର୍ଣ୍ଣ ସଂଗ୍ରହ ପାଇଁ କୌଣସି ସହଜରେ ପ୍ରାପ୍ୟ ମେଟାଡାଟା ଡମ୍ପସ୍ ଉପଲବ୍ଧ ନାହିଁ। %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(duxiu)s>DuXiu ମେଟାଡାଟା</a> ସଂଗ୍ରହ ପରିଚାଳନା କରେ। ଫାଇଲଗୁଡ଼ିକ %(icon)s ଫାଇଲ୍ଗୁଡ଼ିକ କେବଳ ସୀମିତ ଭାବରେ ଧାର ଦେବା ପାଇଁ ଉପଲବ୍ଧ, ବିଭିନ୍ନ ପ୍ରବେଶ ସୀମାବଦ୍ଧତା ସହିତ। %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(ia)s>IA ଫାଇଲ୍ସ</a> ସଂଗ୍ରହ ପରିଚାଳନା କରେ। %(icon)s କିଛି ମେଟାଡାଟା <a %(openlib)s>Open Library ଡାଟାବେସ ଡମ୍ପସ୍</a> ମାଧ୍ୟମରେ ଉପଲବ୍ଧ ଅଛି, କିନ୍ତୁ ସେଗୁଡ଼ିକ ସମ୍ପୂର୍ଣ୍ଣ IA ସଂଗ୍ରହକୁ ଆବରଣ କରେନାହିଁ। %(icon)s ସେମାନଙ୍କ ସମ୍ପୂର୍ଣ୍ଣ ସଂଗ୍ରହ ପାଇଁ କୌଣସି ସହଜରେ ପ୍ରାପ୍ୟ ମେଟାଡାଟା ଡମ୍ପସ୍ ଉପଲବ୍ଧ ନାହିଁ। %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(ia)s>IA ମେଟାଡାଟା</a> ସଂଗ୍ରହ ପରିଚାଳନା କରେ। ଶେଷ ଅଦ୍ୟତନ %(icon)s ଆନାର ଆର୍କାଇଭ୍ ଏବଂ Libgen.li ଏକାଠି ମିଶି ଏହି ସଂଗ୍ରହଗୁଡ଼ିକର ନିର୍ବାହ କରନ୍ତି <a %(comics)s>କମିକ୍ସ ପୁସ୍ତକ</a>, <a %(magazines)s>ମ୍ୟାଗାଜିନ୍</a>, <a %(standarts)s>ମାନକ ଡକ୍ୟୁମେଣ୍ଟ</a>, ଏବଂ <a %(fiction)s>କଳ୍ପନା (Libgen.rs ରୁ ବିଭିନ୍ନ)</a>। %(icon)s ନନ୍-ଫିକ୍ସନ୍ ଟୋରେଣ୍ଟଗୁଡ଼ିକ Libgen.rs ସହିତ ଅଂଶୀଦାର ହୋଇଛି (ଏବଂ ଏଠାରେ <a %(libgenli)s>ମିରର୍ କରାଯାଇଛି</a>)। %(icon)s ତ୍ରିମାସିକ <a %(dbdumps)s>HTTP ଡାଟାବେସ ଡମ୍ପସ୍</a> %(icon)s <a %(nonfiction)s>Non-Fiction</a> ଓ <a %(fiction)s>Fiction</a> ପାଇଁ ସ୍ୱୟଂଚାଳିତ ଟୋରେଣ୍ଟଗୁଡ଼ିକ %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(covers)s>ପୁସ୍ତକ ମୁଖପୃଷ୍ଠା ଟୋରେଣ୍ଟଗୁଡ଼ିକ</a>ର ସଂଗ୍ରହକୁ ପରିଚାଳନା କରେ %(icon)s ଦୈନିକ <a %(dbdumps)s>HTTP ଡାଟାବେସ୍ ଡମ୍ପ୍ସ</a> ମେଟାଡାଟା %(icon)s ମାସିକ <a %(dbdumps)s>ଡାଟାବେସ୍ ଡମ୍ପ୍ସ</a> %(icon)s ଡାଟା ଟୋରେଣ୍ଟଗୁଡ଼ିକ <a %(scihub1)s>ଏଠାରେ</a>, <a %(scihub2)s>ଏଠାରେ</a>, ଓ <a %(libgenli)s>ଏଠାରେ</a> ଉପଲବ୍ଧ ଅଛି %(icon)s କିଛି ନୂଆ ଫାଇଲଗୁଡ଼ିକ <a %(libgenrs)s>ଯୋଗ</a> ହେଉଛି <a %(libgenli)s>Libgen’s “scimag”</a>କୁ, କିନ୍ତୁ ନୂଆ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ନୁହେଁ %(icon)s Sci-Hub 2021 ରୁ ନୂଆ ଫାଇଲଗୁଡ଼ିକୁ ଜମା କରିବାକୁ ବନ୍ଦ କରିଛି। %(icon)s ମେଟାଡାଟା ଡମ୍ପ୍ସ <a %(scihub1)s>ଏଠାରେ</a> ଓ <a %(scihub2)s>ଏଠାରେ</a> ଉପଲବ୍ଧ ଅଛି, ଏବଂ <a %(libgenli)s>Libgen.li ଡାଟାବେସ୍</a>ର ଅଂଶ ଭାବରେ (ଯାହାକୁ ଆମେ ବ୍ୟବହାର କରୁଛୁ) ଉତ୍ସ %(icon)s ବିଭିନ୍ନ ଛୋଟ କିମ୍ବା ଏକକ ଉତ୍ସଗୁଡ଼ିକ। ଆମେ ଲୋକମାନଙ୍କୁ ପ୍ରଥମେ ଅନ୍ୟ ଛାୟା ଲାଇବ୍ରେରୀକୁ ଅପଲୋଡ୍ କରିବାକୁ ଉତ୍ସାହିତ କରୁଛୁ, କିନ୍ତୁ କେବେ କେବେ ଲୋକମାନଙ୍କ ପାଖରେ ଏମିତି ସଂଗ୍ରହ ଅଛି ଯାହା ଅନ୍ୟମାନଙ୍କ ପାଇଁ ବିନ୍ୟାସ କରିବାକୁ ବହୁତ ବଡ଼, ଯଦିଓ ସେଗୁଡ଼ିକ ନିଜର ଶ୍ରେଣୀ ଦେବା ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ନୁହେଁ। %(icon)s ସିଧାସଳଖ ଭାବରେ ବଲ୍କରେ ଉପଲବ୍ଧ ନୁହେଁ, ସ୍କ୍ରାପିଂ ବିରୋଧରେ ସୁରକ୍ଷିତ %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ <a %(worldcat)s>OCLC (WorldCat) ମେଟାଡାଟା</a>ର ସଂଗ୍ରହକୁ ପରିଚାଳନା କରେ %(icon)s ଆନା’ସ ଆର୍କାଇଭ୍ ଏବଂ Z-Library ମିଶିତି ମିଶିତ <a %(metadata)s>Z-Library ମେଟାଡାଟା</a> ଏବଂ <a %(files)s>Z-Library ଫାଇଲ୍ସ</a> ସଂଗ୍ରହ ପରିଚାଳନା କରନ୍ତି। ଡାଟାସେଟ୍ ଉପରୋକ୍ତ ସମସ୍ତ ଉତ୍ସଗୁଡ଼ିକୁ ଆମେ ଏକତ୍ରିତ ତଥ୍ୟଭଣ୍ଡାରରେ ଏକତ୍ର କରି ଏହି ୱେବସାଇଟ୍ ସର୍ଭ କରିବାକୁ ବ୍ୟବହାର କରୁଛୁ। ଏହି ଏକତ୍ରିତ ତଥ୍ୟଭଣ୍ଡାର ସିଧାସଳଖ ଉପଲବ୍ଧ ନୁହେଁ, କିନ୍ତୁ ଆନ୍ନାର ଆର୍କାଇଭ୍ ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଉତ୍ସ ହେବାରୁ, ଏହାକୁ ସହଜରେ <a %(a_generated)s>ସୃଷ୍ଟି</a> କିମ୍ବା <a %(a_downloaded)s>ଡାଉନଲୋଡ୍</a> କରିପାରିବା ElasticSearch ଏବଂ MariaDB ତଥ୍ୟଭଣ୍ଡାର ଭାବରେ। ସେହି ପୃଷ୍ଠାର ସ୍କ୍ରିପ୍ଟଗୁଡ଼ିକ ଉପରୋକ୍ତ ଉଲ୍ଲେଖିତ ଉତ୍ସଗୁଡ଼ିକରୁ ସମସ୍ତ ଆବଶ୍ୟକ ମେଟାଡାଟା ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଡାଉନଲୋଡ୍ କରିବ। ସେହି ସ୍କ୍ରିପ୍ଟଗୁଡ଼ିକୁ ସ୍ଥାନୀୟ ଭାବରେ ଚାଲାଇବା ପୂର୍ବରୁ ଯଦି ଆପଣ ଆମର ତଥ୍ୟଗୁଡ଼ିକୁ ଅନୁସନ୍ଧାନ କରିବାକୁ ଇଚ୍ଛା କରନ୍ତି, ଆପଣ ଆମର JSON ଫାଇଲଗୁଡ଼ିକୁ ଦେଖିପାରିବେ, ଯାହା ଅନ୍ୟ JSON ଫାଇଲଗୁଡ଼ିକୁ ଆଗକୁ ଲିଙ୍କ କରେ। <a %(a_json)s>ଏହି ଫାଇଲ୍</a> ଏକ ଭଲ ଆରମ୍ଭ ବିନ୍ଦୁ। ଏକତ୍ରିତ ତଥ୍ୟଭଣ୍ଡାର ଆନାର ଆର୍କାଇଭ୍‌ ଦ୍ୱାରା ଟୋରେଣ୍ଟଗୁଡ଼ିକ ବ୍ରାଉଜ୍ କରନ୍ତୁ ସନ୍ଧାନ କରନ୍ତୁ ବିଭିନ୍ନ ଛୋଟ ଅଥବା ଏକକ ଉତ୍ସଗୁଡ଼ିକ। ଆମେ ଲୋକମାନଙ୍କୁ ପ୍ରଥମେ ଅନ୍ୟ ଛାୟା ଲାଇବ୍ରେରୀକୁ ଅପଲୋଡ୍ କରିବାକୁ ଉତ୍ସାହିତ କରୁଛୁ, କିନ୍ତୁ କେବେ କେବେ ଲୋକମାନଙ୍କ ପାଖରେ ଏମିତି କଲେକ୍ସନ ଥାଏ ଯାହା ଅନ୍ୟମାନଙ୍କ ପାଇଁ ବିନ୍ୟାସ କରିବାକୁ ବହୁତ ବଡ଼ ହୁଏ, ଯଦିଓ ସେଗୁଡ଼ିକ ନିଜର ଶ୍ରେଣୀ ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ନୁହେଁ। <a %(a1)s>ଡାଟାସେଟ୍ ପୃଷ୍ଠା</a>ରୁ ସାରାଂଶ। <a %(a_href)s>aaaaarg.fail</a> ଠାରୁ। ମଧ୍ୟମ ଭାବରେ ସମ୍ପୂର୍ଣ୍ଣ ଲାଗୁଛି। ଆମର ସେବକ "cgiym" ଠାରୁ। <a %(a_href)s><q>ACM ଡିଜିଟାଲ ଲାଇବ୍ରେରୀ 2020</q></a> ଟୋରେଣ୍ଟରୁ। ଅବସ୍ଥାନ୍ତର ଉପରେ ଉଚ୍ଚ ସମାନତା ରହିଛି, କିନ୍ତୁ ଅତ୍ୟନ୍ତ କମ୍ MD5 ମେଳ ରହିଛି, ତେଣୁ ଆମେ ସେଟିକୁ ସମ୍ପୂର୍ଣ୍ଣ ରଖିବାକୁ ନିଷ୍ପତ୍ତି ନେଲୁ। ସେବକ <q>j</q> ଦ୍ୱାରା <q>iRead eBooks</q> (= ଧ୍ୱନିଗତ ଭାବରେ <q>ai rit i-books</q>; airitibooks.com) ର ସ୍କ୍ରାପ୍। <a %(a1)s><q>ଅନ୍ୟାନ୍ୟ ମେଟାଡାଟା ସ୍କ୍ରାପ୍ସ</q></a>ରେ <q>airitibooks</q> ମେଟାଡାଟା ସହିତ ସମ୍ବନ୍ଧିତ। <a %(a1)s><q>ବିବ୍ଲିଓଥେକା ଏଲେକ୍ସାଣ୍ଡ୍ରିନା</q></a> ର ସଂଗ୍ରହରୁ। ଅଂଶତଃ ମୂଳ ସ୍ରୋତରୁ, ଅଂଶତଃ the-eye.eu ରୁ, ଅଂଶତଃ ଅନ୍ୟ ମିରର୍ସରୁ। ଏକ ବ୍ୟକ୍ତିଗତ ପୁସ୍ତକ ଟୋରେଣ୍ଟ ୱେବସାଇଟ୍, <a %(a_href)s>ବିବ୍ଲିଓଟିକ୍</a> (ସାଧାରଣତଃ "ବିବ୍" ଭାବରେ ଉଲ୍ଲେଖିତ), ଯାହାର ପୁସ୍ତକଗୁଡ଼ିକ ନାମ ଅନୁଯାୟୀ ଟୋରେଣ୍ଟରେ ଗଠିତ ହୋଇଥିଲା (A.torrent, B.torrent) ଏବଂ the-eye.eu ମାଧ୍ୟମରେ ବିତରଣ କରାଯାଇଥିଲା। ଆମର ସେବକ "bpb9v" ଠାରୁ। <a %(a_href)s>CADAL</a> ବିଷୟରେ ଅଧିକ ତଥ୍ୟ ପାଇଁ, ଆମର <a %(a_duxiu)s>DuXiu ଡାଟାସେଟ୍ ପୃଷ୍ଠା</a>ରେ ଟିପ୍ପଣୀଗୁଡ଼ିକ ଦେଖନ୍ତୁ। ଆମର ସେବକ "bpb9v" ଠାରୁ ଅଧିକ, ମୁଖ୍ୟତଃ DuXiu ଫାଇଲଗୁଡ଼ିକ, ଏବଂ ଏକ ଫୋଲ୍ଡର "WenQu" ଏବଂ "SuperStar_Journals" (SuperStar DuXiu ପଛରେ ଥିବା କମ୍ପାନୀ)। ଆମର ସେବକ "cgiym" ଠାରୁ, ବିଭିନ୍ନ ଉତ୍ସରୁ ଚାଇନିଜ୍ ପାଠ୍ୟଗୁଡ଼ିକ (ଉପ-ଡାଇରେକ୍ଟରୀ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରାଯାଇଛି), ଯାହାରେ <a %(a_href)s>ଚାଇନା ମେସିନ୍ ପ୍ରେସ୍</a> (ଏକ ପ୍ରମୁଖ ଚାଇନିଜ୍ ପ୍ରକାଶକ) ଠାରୁ ମଧ୍ୟ ଅଛି। ଆମର ସେବକ "cgiym" ଠାରୁ ଅଚାଇନିଜ୍ ସଂଗ୍ରହଗୁଡ଼ିକ (ଉପ-ଡାଇରେକ୍ଟରୀ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରାଯାଇଛି)। ଚୀନ୍ ଶିଳ୍ପକଳା ବିଷୟରେ ପୁସ୍ତକଗୁଡ଼ିକର ସ୍କ୍ରାପ୍, ସେବକ <q>cm</q> ଦ୍ୱାରା: <q>ମୁଁ ପ୍ରକାଶନ ଘରରେ ଏକ ନେଟୱର୍କ ଅସୁରକ୍ଷାକୁ ଦୁର୍ବଳ କରି ଏହାକୁ ପାଇଲି, କିନ୍ତୁ ସେହି ତ୍ରୁଟି ଏବେ ବନ୍ଦ ହୋଇଯାଇଛି</q>। <a %(a1)s><q>ଅନ୍ୟାନ୍ୟ ମେଟାଡାଟା ସ୍କ୍ରାପ୍ସ</q></a>ରେ <q>chinese_architecture</q> ମେଟାଡାଟା ସହିତ ସମ୍ବନ୍ଧିତ। ଏକ ଏକାଡେମିକ ପ୍ରକାଶନ ଘର <a %(a_href)s>ଡି ଗ୍ରୁୟଟର</a>ରୁ ପୁସ୍ତକଗୁଡ଼ିକ, କିଛି ବଡ଼ ଟୋରେଣ୍ଟରୁ ସଂଗ୍ରହ କରାଯାଇଛି। <a %(a_href)s>docer.pl</a>ର ସ୍କ୍ରାପ୍, ଏକ ପୋଲିଶ୍ ଫାଇଲ୍ ଶେୟାରିଂ ୱେବସାଇଟ୍ ଯାହା ପୁସ୍ତକ ଏବଂ ଅନ୍ୟ ଲିଖିତ କାର୍ଯ୍ୟରେ କେନ୍ଦ୍ରିତ। ସେବକ "p" ଦ୍ୱାରା 2023 ଶେଷରେ ସ୍କ୍ରାପ୍ କରାଯାଇଛି। ଆମ ପାଖରେ ମୂଳ ୱେବସାଇଟ୍ ଠାରୁ ଭଲ ମେଟାଡାଟା ନାହିଁ (ଏକାଠି ଫାଇଲ୍ ଏକ୍ସଟେନ୍ସନ ମଧ୍ୟ ନୁହେଁ), କିନ୍ତୁ ଆମେ ପୁସ୍ତକ ଭଳି ଫାଇଲଗୁଡ଼ିକୁ ଫିଲ୍ଟର କରିଥିଲୁ ଏବଂ ଅନେକ ସମୟରେ ଫାଇଲଗୁଡ଼ିକରୁ ମେଟାଡାଟା ଉତ୍ପାଦନ କରିବାରେ ସକ୍ଷମ ହୋଇଥିଲୁ। DuXiu epubs, ସିଧାସଳଖ DuXiu ଠାରୁ, ସେବକ "w" ଦ୍ୱାରା ସଂଗ୍ରହ କରାଯାଇଛି। କେବଳ ନିକଟର DuXiu ପୁସ୍ତକଗୁଡ଼ିକ ସିଧାସଳଖ ଇବୁକ୍ ମାଧ୍ୟମରେ ଉପଲବ୍ଧ, ତେଣୁ ଅଧିକାଂଶ ଏହା ନିକଟର ହେବା ଦରକାର। ସେବକ "m" ଠାରୁ ବାକି ଥିବା DuXiu ଫାଇଲଗୁଡ଼ିକ, ଯାହା DuXiu ର ମୂଳ PDG ଫର୍ମାଟରେ ନଥିଲା (ମୁଖ୍ୟ <a %(a_href)s>DuXiu ଡାଟାସେଟ୍</a>)। ଅନେକ ମୂଳ ଉତ୍ସରୁ ସଂଗ୍ରହ କରାଯାଇଛି, ଦୁର୍ଭାଗ୍ୟବଶତଃ ସେଗୁଡ଼ିକୁ filepathରେ ସଂରକ୍ଷଣ କରିବାକୁ ବଞ୍ଚିତ। <span></span> <span></span> <span></span> ସେବକ <q>do no harm</q> ଦ୍ୱାରା ଅଶ୍ଳୀଳ ପୁସ୍ତକଗୁଡ଼ିକର ସ୍କ୍ରାପ୍। <a %(a1)s><q>ଅନ୍ୟାନ୍ୟ ମେଟାଡାଟା ସ୍କ୍ରାପ୍ସ</q></a>ରେ <q>hentai</q> ମେଟାଡାଟା ସହିତ ସମ୍ବନ୍ଧିତ। <span></span> <span></span> ସେବକ "t" ଦ୍ୱାରା ଜାପାନୀଜ୍ ମାଙ୍ଗା ପ୍ରକାଶକ ଠାରୁ ସଂଗ୍ରହ କରାଯାଇଥିବା ସଂଗ୍ରହ। <a %(a_href)s>ଲଙ୍ଗକୁଆନର ଚୟନିତ ନ୍ୟାୟିକ ଆର୍କାଇଭ୍‌ଗୁଡ଼ିକ</a>, ସେବକ "c" ଦ୍ୱାରା ପ୍ରଦାନ କରାଯାଇଛି। <a %(a_href)s>magzdb.org</a> ର ସ୍କ୍ରାପ୍, ଯାହା ଲାଇବ୍ରେରୀ ଜେନେସିସର ଏକ ମିତ୍ର (ଏହା libgen.rs ହୋମପେଜରେ ଲିଙ୍କ୍ ହୋଇଛି) କିନ୍ତୁ ଯିଏ ସିଧାସଳଖ ଭାବରେ ତାଙ୍କର ଫାଇଲ୍‌ଗୁଡ଼ିକ ପ୍ରଦାନ କରିବାକୁ ଚାହିଁଲେ ନାହିଁ। 2023 ର ଶେଷରେ ସେବକ "p" ଦ୍ୱାରା ପ୍ରାପ୍ତ। <span></span> ବିଭିନ୍ନ ଛୋଟ ଅପଲୋଡ୍‌ଗୁଡ଼ିକ, ଯାହା ତାଙ୍କର ନିଜସ୍ୱ ସବକଲେକ୍ସନ୍ ଭାବେ ଅତ୍ୟନ୍ତ ଛୋଟ, କିନ୍ତୁ ଡାଇରେକ୍ଟରୀ ଭାବେ ପ୍ରତିନିଧିତ୍ୱ କରାଯାଇଛି। AvaxHome, ଏକ ରୁଷିୟ ଫାଇଲ୍ ଶେୟରିଂ ୱେବସାଇଟ୍ ରୁ ଇବୁକ୍ସ। ସମ୍ବାଦପତ୍ର ଏବଂ ପତ୍ରିକାର ଅର୍କାଇଭ୍। <a %(a1)s><q>ଅନ୍ୟାନ୍ୟ ମେଟାଡାଟା ସ୍କ୍ରାପ୍ସ</q></a>ରେ <q>newsarch_magz</q> ମେଟାଡାଟା ସହିତ ସମ୍ବନ୍ଧିତ। <a %(a1)s>ଦାର୍ଶନିକ ଡକ୍ୟୁମେଣ୍ଟେସନ ସେଣ୍ଟର</a>ର ସ୍କ୍ରାପ୍। ସେବକ "o" ଙ୍କ ସଂଗ୍ରହ, ଯିଏ ପୋଲିଶ୍ ପୁସ୍ତକଗୁଡ଼ିକୁ ମୂଳ ମୁକ୍ତି ("ଦୃଶ୍ୟ") ୱେବସାଇଟ୍‌ଗୁଡ଼ିକରୁ ସିଧାସଳଖ ଭାବରେ ସଂଗ୍ରହ କରିଥିଲେ। ସେବକ "cgiym" ଏବଂ "woz9ts" ଦ୍ୱାରା <a %(a_href)s>shuge.org</a> ର ସଂଯୁକ୍ତ ସଂଗ୍ରହ। <span></span> <a %(a_href)s>“ଇମ୍ପେରିଆଲ୍ ଲାଇବ୍ରେରୀ ଅଫ୍ ଟ୍ରାଣ୍ଟର”</a> (କଳ୍ପନାସ୍ଥ ଲାଇବ୍ରେରୀ ନାମରେ ନାମିତ), 2022 ରେ ସେବକ "t" ଦ୍ୱାରା ସ୍କ୍ରାପ୍ କରାଯାଇଥିଲା। <span></span> <span></span> <span></span> ସବ୍-ସବ୍-ସଂଗ୍ରହଗୁଡ଼ିକ (ଡିରେକ୍ଟୋରୀ ଭାବରେ ପ୍ରତିନିଧିତ) ସ୍ୱେଚ୍ଛାସେବୀ “woz9ts” ଠାରୁ: <a %(a_program_think)s>ପ୍ରୋଗ୍ରାମ-ଥିଙ୍କ</a>, <a %(a_haodoo)s>ହାଓଡୁ</a>, <a %(a_skqs)s>skqs</a> (ତାଇୱାନରେ <a %(a_sikuquanshu)s>ଡିଜି(迪志)</a> ଦ୍ୱାରା), ମେବୁକ୍ (mebook.cc, ମୋର ଛୋଟ ପୁସ୍ତକାଳୟ, my little bookroom — woz9ts: “ଏହି ସାଇଟ୍ ମୁଖ୍ୟତଃ ଉଚ୍ଚ ଗୁଣବତ୍ତାର ଇବୁକ୍ ଫାଇଲ୍ଗୁଡ଼ିକ ଅଂଶୀଦାର କରିବାରେ ଧ୍ୟାନ ଦେଇଥାଏ, ଯାହାର କିଛି ମାଲିକ ନିଜେ ଟାଇପସେଟ୍ କରିଛନ୍ତି। ମାଲିକ <a %(a_arrested)s>2019 ରେ ଗିରଫ</a> ହୋଇଥିଲେ ଏବଂ କେହି ତାଙ୍କ ଅଂଶୀଦାର କରାଯାଇଥିବା ଫାଇଲ୍ଗୁଡ଼ିକର ସଂଗ୍ରହ ତିଆରି କରିଥିଲେ।”)। ସ୍ୱେଚ୍ଛାସେବୀ “woz9ts” ଠାରୁ ବାକି ଥିବା DuXiu ଫାଇଲ୍ଗୁଡ଼ିକ, ଯାହା DuXiu ମାଲିକାନା PDG ଫର୍ମାଟରେ ନଥିଲା (ଏପର୍ଯ୍ୟନ୍ତ PDF କୁ ପରିବର୍ତ୍ତିତ ହେବାକୁ ଅଛି)। "ଅପଲୋଡ୍" କଲେକ୍ସନକୁ ଛୋଟ ଉପ-ସବ୍‌କଲେକ୍ସନରେ ବିଭକ୍ତ କରାଯାଇଛି, ଯାହା AACIDs ଏବଂ ଟୋରେଣ୍ଟ ନାମରେ ଉଲ୍ଲେଖିତ ହୋଇଛି। ସମସ୍ତ ସବ୍‌କଲେକ୍ସନ ପ୍ରଥମେ ମୁଖ୍ୟ କଲେକ୍ସନ ସହିତ ଡିଡ୍ୟୁପ୍ଲିକେଟ୍ କରାଯାଇଥିଲା, ଯଦିଓ ମେଟାଡାଟା "upload_records" JSON ଫାଇଲଗୁଡ଼ିକ ତଥାପି ମୂଳ ଫାଇଲଗୁଡ଼ିକର ଅନେକ ସନ୍ଦର୍ଭ ଧାରଣ କରିଛି। ଅଧିକାଂଶ ସବ୍‌କଲେକ୍ସନରୁ ଅପୁସ୍ତକ ଫାଇଲଗୁଡ଼ିକୁ ମଧ୍ୟ ହଟାଯାଇଛି, ଏବଂ ସାଧାରଣତଃ <em>ନୁହେଁ</em> "upload_records" JSONରେ ଉଲ୍ଲେଖିତ। ଉପସଂଗ୍ରହଗୁଡ଼ିକ ହେଉଛି: ଟିପ୍ପଣୀ ଉପସଂଗ୍ରହ ବହୁତ ସବ୍‌କଲେକ୍ସନ ନିଜେ ଉପ-ସବ୍‌କଲେକ୍ସନ (ଉଦାହରଣ ସ୍ୱରୂପ, ଭିନ୍ନ ମୂଳ ସ୍ରୋତରୁ) ଦ୍ୱାରା ଗଠିତ ହୋଇଥାଏ, ଯାହାକି "filepath" କ୍ଷେତ୍ରରେ ଡାଇରେକ୍ଟରୀ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରାଯାଇଛି। ଆନା’ସ ଆର୍କାଇଭକୁ ଅପଲୋଡ୍‌ଗୁଡ଼ିକ ଏହି ତଥ୍ୟ ବିଷୟରେ ଆମର ବ୍ଲଗ୍ ପୋଷ୍ଟ <a %(a_worldcat)s>WorldCat</a> ହେଉଛି ଏକ ଆଧିକାରିକ ଡାଟାବେସ୍ ଯାହା ଏକ ନନ-ପ୍ରଫିଟ୍ <a %(a_oclc)s>OCLC</a> ଦ୍ୱାରା ପ୍ରଦାନ କରାଯାଇଛି, ଯାହା ବିଶ୍ୱର ସମସ୍ତ ଲାଇବ୍ରେରୀରୁ ମେଟାଡାଟା ରେକର୍ଡଗୁଡ଼ିକୁ ସଂଗ୍ରହ କରେ। ଏହା ସମ୍ଭବତଃ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଲାଇବ୍ରେରୀ ମେଟାଡାଟା ସଂଗ୍ରହ। ଅକ୍ଟୋବର 2023 ରେ ଆମେ <a %(a_scrape)s>ମୁକ୍ତ କରିଥିଲୁ</a> ଏକ ସମ୍ପୂର୍ଣ୍ଣ ସ୍କ୍ରାପ୍ ଅଫ୍ OCLC (WorldCat) ଡାଟାବେସ୍, <a %(a_aac)s>ଆନା’ସ ଆର୍କାଇଭ କଣ୍ଟେନର୍ ଫର୍ମାଟ୍</a>ରେ। ଅକ୍ଟୋବର 2023, ପ୍ରାରମ୍ଭିକ ମୁକ୍ତି: OCLC (WorldCat) ଆନା’ସ ଆର୍କାଇଭ ଦ୍ୱାରା ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଆନାର ଆର୍କାଇଭ୍ ଉପରେ ଉଦାହରଣ ରେକର୍ଡ (ମୂଳ ସଂଗ୍ରହ) ଆନାର ଆର୍କାଇଭ୍ ଉପରେ ଉଦାହରଣ ରେକର୍ଡ (“zlib3” ସଂଗ୍ରହ) ଆନାର ଆର୍କାଇଭ୍ ଦ୍ୱାରା ଟୋରେଣ୍ଟଗୁଡ଼ିକ (ମେଟାଡାଟା + ବିଷୟବସ୍ତୁ) ମୁକ୍ତି 1 ବିଷୟରେ ବ୍ଲଗ ପୋଷ୍ଟ ରିଲିଜ୍ 2 ବିଷୟରେ ବ୍ଲଗ୍ ପୋଷ୍ଟ 2022 ର ଶେଷରେ, ଜେ-ଲାଇବ୍ରେରୀର ଅଭିଯୋଗୀ ସ୍ଥାପକମାନେ ଗିରଫ ହୋଇଥିଲେ, ଏବଂ ଡୋମେନଗୁଡ଼ିକ ଯୁକ୍ତ ରାଷ୍ଟ୍ର ସରକାରୀ କର୍ତ୍ତୃପକ୍ଷ ଦ୍ୱାରା ଜବତ କରାଯାଇଥିଲା। ସେଠାରୁ ବେବସାଇଟ୍ ମନ୍ତ୍ରେ ମନ୍ତ୍ରେ ପୁନଃ ଅନଲାଇନ୍ ହେଉଛି। ବର୍ତ୍ତମାନ ଏହା କିଏ ପରିଚାଳନା କରୁଛି ତାହା ଅଜ୍ଞାତ। ଫେବୃଆରୀ 2023 ର ଅବସ୍ଥାନୁସାର ଅଦ୍ୟତନ। ଜେ-ଲାଇବ୍ରେରୀର ମୂଳ ଉତ୍ସ <a %(a_href)s>ଲାଇବ୍ରେରୀ ଜେନେସିସ</a> ସମୁଦାୟରେ ରହିଛି, ଏବଂ ମୂଳତଃ ତାଙ୍କର ତଥ୍ୟ ସହିତ ଆରମ୍ଭ କରାଯାଇଥିଲା। ସେଥିଠାରୁ, ଏହା ବ୍ୟାବସାୟିକ ଭାବରେ ଅନେକ ଉନ୍ନତ ହୋଇଛି, ଏବଂ ଅଧୁନିକ ଇଣ୍ଟରଫେସ୍ ରହିଛି। ସେମାନେ ତେଣୁ ତାଙ୍କର ୱେବସାଇଟ୍ ଉନ୍ନତ କରିବା ପାଇଁ ଅର୍ଥନୈତିକ ଭାବରେ ଏବଂ ନୂତନ ପୁସ୍ତକଗୁଡ଼ିକର ଦାନ ଦୁଇଥରି ଅଧିକ ଦାନ ପାଇବାରେ ସକ୍ଷମ ହୋଇଛନ୍ତି। ସେମାନେ ଲାଇବ୍ରେରୀ ଜେନେସିସ ସହିତ ଅନେକ ବଡ଼ ସଂଗ୍ରହ ସଂଗ୍ରହ କରିଛନ୍ତି। ସଂଗ୍ରହଟି ତିନୋଟି ଅଂଶରୁ ଗଠିତ। ପ୍ରଥମ ଦୁଇ ଅଂଶ ପାଇଁ ମୂଳ ବର୍ଣ୍ଣନା ପୃଷ୍ଠାଗୁଡ଼ିକ ତଳେ ସଂରକ୍ଷିତ ଅଛି। ସମସ୍ତ ତଥ୍ୟ ପାଇବା ପାଇଁ ଆପଣଙ୍କୁ ତିନୋଟି ଅଂଶ ଆବଶ୍ୟକ (ଅତିରିକ୍ତ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ଛାଡ଼ି, ଯାହା ଟୋରେଣ୍ଟ ପୃଷ୍ଠାରେ କାଟା ଯାଇଛି)। %(title)s: ଆମର ପ୍ରଥମ ମୁକ୍ତି। ଏହା ତାହାବେଳେ "ପାଇରେଟ୍ ଲାଇବ୍ରେରୀ ମିରର" ("ପିଲିମି") ଭାବରେ ଡାକାଯାଉଥିବା ପ୍ରଥମ ମୁକ୍ତି ଥିଲା। %(title)s: ଦ୍ୱିତୀୟ ମୁକ୍ତି, ଏଥାରେ ସମସ୍ତ ଫାଇଲ୍‌ଗୁଡ଼ିକ .ଟାର୍ ଫାଇଲ୍‌ରେ ମୁଡ଼ି ରହିଛି। %(title)s: ନୂତନ ମୁକ୍ତିଗୁଡ଼ିକ, <a %(a_href)s>ଆନାର ଆର୍କାଇଭ୍ କଣ୍ଟେନର୍ସ (AAC) ଫର୍ମାଟ୍</a> ବ୍ୟବହାର କରି, ବର୍ତ୍ତମାନ ଜେ-ଲାଇବ୍ରେରୀ ଟିମ୍ ସହିତ ସହଯୋଗରେ ମୁକ୍ତି ପାଇଛି। ପ୍ରାରମ୍ଭିକ ମିରର୍ 2021 ଓ 2022 ମଧ୍ୟରେ ଅତ୍ୟନ୍ତ ପରିଶ୍ରମରେ ପ୍ରାପ୍ତ ହୋଇଥିଲା। ଏହି ସମୟରେ ଏହା କିଛି ପୁରାତନ ହୋଇଯାଇଛି: ଏହା ଜୁନ୍ 2021 ରେ ସଂଗ୍ରହର ଅବସ୍ଥାକୁ ପ୍ରତିବିମ୍ବିତ କରେ। ଆମେ ଭବିଷ୍ୟତରେ ଏହାକୁ ଅଦ୍ୟତନ କରିବୁ। ଏହି ସମୟରେ ଆମେ ପ୍ରଥମ ରିଲିଜ୍ ବାହାର କରିବାରେ ଧ୍ୟାନ ଦେଉଛୁ। Library Genesis ଏହାର ପବ୍ଲିକ ଟୋରେଣ୍ଟ ସହିତ ପୂର୍ବରୁ ସଂରକ୍ଷିତ ହୋଇଥିବାରୁ, ଏବଂ Z-Library ରେ ଅନ୍ତର୍ଭୁକ୍ତ ହେବାରୁ, ଆମେ ଜୁନ 2022 ରେ Library Genesis ବିରୋଧରେ ମୂଳ ଡିଡୁପ୍ଲିକେସନ୍ କରିଥିଲୁ। ଏହା ପାଇଁ ଆମେ MD5 ହାସ୍ ଉପଯୋଗ କରିଥିଲୁ। ଲାଇବ୍ରେରୀରେ ଅଧିକ ଡୁପ୍ଲିକେଟ୍ ବିଷୟବସ୍ତୁ ଥିବା ସମ୍ଭାବନା ଅଛି, ଯେପରିକି ସେହି ପୁସ୍ତକ ସହିତ ଅନେକ ଫାଇଲ୍ ଫର୍ମାଟ୍। ଏହାକୁ ନିଖୁତ ଭାବରେ ଚିହ୍ନଟ କରିବା କଷ୍ଟକର, ତେଣୁ ଆମେ ଏହାକୁ ନାହିଁ କରୁ। ଡିଡୁପ୍ଲିକେସନ୍ ପରେ ଆମେ 2 ମିଲିୟନ୍ ଫାଇଲ୍ ରଖିଛୁ, ଯାହାର ମୋଟ ଆକାର 7TB ରୁ କମ୍। ସଂଗ୍ରହଟି ଦୁଇଟି ଅଂଶରେ ବିଭକ୍ତ: ଏକ MySQL “.sql.gz” ଡମ୍ପ ମେଟାଡାଟାର, ଏବଂ 72 ଟୋରେଣ୍ଟ ଫାଇଲ୍ ଯାହା ପ୍ରତ୍ୟେକ 50-100GB ଆକାରର। ମେଟାଡାଟାରେ Z-Library ୱେବସାଇଟ୍ ଦ୍ୱାରା ରିପୋର୍ଟ କରାଯାଇଥିବା ତଥ୍ୟ (ଶୀର୍ଷକ, ଲେଖକ, ବର୍ଣ୍ଣନା, ଫାଇଲ୍ ଟାଇପ୍) ଥାଏ, ଏବଂ ଆମେ ଦେଖିଥିବା ବାସ୍ତବ ଫାଇଲସାଇଜ୍ ଏବଂ md5sum ମଧ୍ୟ ଥାଏ, କାରଣ କେବେ କେବେ ଏହାଗୁଡ଼ିକ ସହମତ ନୁହେଁ। ଏହାରେ କିଛି ଫାଇଲ୍ ରେଞ୍ଜ୍ ଥାଏ ଯାହା ପାଇଁ Z-Library ନିଜେ ଭୁଲ ମେଟାଡାଟା ରଖିଛି। ଆମେ କିଛି ଭୁଲ ଡାଉନଲୋଡ୍ ଫାଇଲ୍ ରଖିଥାନ୍ତି ଯାହାକୁ ଆମେ ଭବିଷ୍ୟତରେ ଚିହ୍ନଟ କରି ଠିକ୍ କରିବାକୁ ଚେଷ୍ଟା କରିବା। ବଡ଼ ଟୋରେଣ୍ଟ ଫାଇଲ୍ ଗୁଡ଼ିକରେ ବାସ୍ତବ ପୁସ୍ତକ ତଥ୍ୟ ରହିଛି, ଫାଇଲନାମ୍ ଭାବରେ Z-Library ID ସହିତ। ଫାଇଲ୍ ଏକ୍ସଟେନସନ୍ ମେଟାଡାଟା ଡମ୍ପ ଉପଯୋଗ କରି ପୁନଃ ଗଠନ କରାଯାଇପାରେ। ସଂଗ୍ରହଟି ଅପ୍ରକୃତିକ ଏବଂ ପ୍ରକୃତିକ ବିଷୟବସ୍ତୁର ମିଶ୍ରଣ (Library Genesis ରେ ଯେପରି ସ୍ୱତନ୍ତ୍ର ଭାବରେ ବିଭକ୍ତ ନୁହେଁ)। ଗୁଣବତ୍ତା ମଧ୍ୟ ବିଭିନ୍ନ ରହିଛି। ଏହି ପ୍ରଥମ ମୁକ୍ତି ଏବେ ପୂରାପୂରି ଉପଲବ୍ଧ ଅଛି। ଟୋରେଣ୍ଟ ଫାଇଲ୍ ଗୁଡ଼ିକ କେବଳ ଆମର Tor ମିରର୍ ମାଧ୍ୟମରେ ଉପଲବ୍ଧ ଅଛି। ରିଲିଜ୍ 1 (%(date)s) ଏହା ଏକ ଏକାକୀ ଅତିରିକ୍ତ ଟୋରେଣ୍ଟ ଫାଇଲ୍। ଏହାରେ କୌଣସି ନୂଆ ତଥ୍ୟ ନାହିଁ, କିନ୍ତୁ ଏହାରେ କିଛି ତଥ୍ୟ ଅଛି ଯାହା ଗଣନା କରିବାରେ ସମୟ ଲାଗିପାରେ। ଏହାକୁ ରଖିବା ସୁବିଧାଜନକ, କାରଣ ଏହି ଟୋରେଣ୍ଟ ଡାଉନଲୋଡ୍ କରିବା ଗଣନା କରିବା ଠାରୁ ଶୀଘ୍ର ହୋଇପାରେ। ବିଶେଷକରି, ଏହାରେ <a %(a_href)s>ratarmount</a> ସହିତ ବ୍ୟବହାର ପାଇଁ ଟାର୍ ଫାଇଲ୍ ନିମନ୍ତେ SQLite ଇଣ୍ଡେକ୍ସ ଅଛି। ମୁକ୍ତି 2 ଅନୁବନ୍ଧ (%(date)s) ଆମେ ଆଗଷ୍ଟ 2022 ମଧ୍ୟରେ ଆମର ଶେଷ ମିରର୍ ଏବଂ Z-Library ରେ ଯୋଡ଼ାଯାଇଥିବା ସମସ୍ତ ପୁସ୍ତକ ପାଇଛୁ। ଆମେ ମଧ୍ୟ ପ୍ରଥମ ଥରେ ମିସ୍ କରିଥିବା କିଛି ପୁସ୍ତକ ଫେରି ଆଣିଛୁ। ସମସ୍ତ ମିଶି ଏହି ନୂଆ ସଂଗ୍ରହଟି ପ୍ରାୟ 24TB ଅଛି। ପୁନଃ, ଏହି ସଂଗ୍ରହଟି Library Genesis ବିରୋଧରେ ଡିଡୁପ୍ଲିକେସନ୍ ହୋଇଛି, କାରଣ ସେହି ସଂଗ୍ରହ ପାଇଁ ଟୋରେଣ୍ଟ ଉପଲବ୍ଧ ଅଛି। ତଥ୍ୟଗୁଡ଼ିକ ପ୍ରଥମ ମୁକ୍ତି ସହିତ ସମାନ ଭାବରେ ସଂଗଠିତ ଅଛି। ଏକ MySQL “.sql.gz” ଡମ୍ପ ମେଟାଡାଟାର ଅଛି, ଯାହା ପ୍ରଥମ ମୁକ୍ତିର ସମସ୍ତ ମେଟାଡାଟାକୁ ମଧ୍ୟ ଅନ୍ତର୍ଭୁକ୍ତ କରେ, ଏହାକୁ ପରିବର୍ତ୍ତନ କରିଛି। ଆମେ କିଛି ନୂଆ କଲମ୍ ଯୋଡ଼ିଛୁ: ଆମେ ଗତ ଥରେ ଏହା ଉଲ୍ଲେଖ କରିଥିଲୁ, କିନ୍ତୁ ସ୍ପଷ୍ଟ କରିବାକୁ: “filename” ଏବଂ “md5” ହେଉଛି ଫାଇଲର ବାସ୍ତବ ଗୁଣ, ଯେଉଁଠାରେ “filename_reported” ଏବଂ “md5_reported” ହେଉଛି ଆମେ Z-Library ରୁ ସ୍କ୍ରାପ୍ କରିଥିବା। କେବେ କେବେ ଏହି ଦୁଇଟି ଏକାଠି ମେଳ ହୁଏ ନାହିଁ, ତେଣୁ ଆମେ ଦୁଇଟିକୁ ଅନ୍ତର୍ଭୁକ୍ତ କରିଛୁ। ଏହି ମୁକ୍ତି ପାଇଁ, ଆମେ କଲେସନ୍ କୁ “utf8mb4_unicode_ci” କୁ ପରିବର୍ତ୍ତନ କରିଛୁ, ଯାହା ପୁରୁଣା ଭାର୍ସନ୍ ମାଇଏସ୍କ୍ୟୁଏଲ୍ ସହିତ ସମନ୍ୱୟ ହେବା ଉଚିତ। ତଥ୍ୟ ଫାଇଲ୍ ଗୁଡ଼ିକ ପ୍ରଥମ ଥର ଭଳି ଅଛି, ଯଦିଓ ସେଗୁଡ଼ିକ ବହୁତ ବଡ଼। ଆମେ ଅନେକ ଛୋଟ ଟୋରେଣ୍ଟ ଫାଇଲ୍ ସୃଷ୍ଟି କରିବାକୁ ଅସୁବିଧା ଭାବିଲୁ। “pilimi-zlib2-0-14679999-extra.torrent” ଗତ ମୁକ୍ତିରେ ଆମେ ମିସ୍ କରିଥିବା ସମସ୍ତ ଫାଇଲ୍ ଅନ୍ତର୍ଭୁକ୍ତ କରେ, ଯେଉଁଠାରେ ଅନ୍ୟ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ସମସ୍ତ ନୂଆ ID ରେଞ୍ଜ୍ ଅଛି।  <strong>ଅଦ୍ୟତନ %(date)s:</strong> ଆମେ ଅଧିକାଂଶ ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ବହୁତ ବଡ଼ କରିଛୁ, ଯାହାର ଫଳରେ ଟୋରେଣ୍ଟ କ୍ଲାଇଣ୍ଟଗୁଡ଼ିକ ସଂଘର୍ଷ କରୁଛି। ଆମେ ସେଗୁଡ଼ିକୁ ଅପସାରଣ କରି ନୂଆ ଟୋରେଣ୍ଟ ମୁକ୍ତି କରିଛୁ। <strong>ଅଦ୍ୟତନ %(date)s:</strong> ତଥାପି ଅଧିକ ଫାଇଲ୍ ଥିଲା, ତେଣୁ ଆମେ ସେଗୁଡ଼ିକୁ ଟାର୍ ଫାଇଲ୍ ରେ ରାପ୍ କରି ନୂଆ ଟୋରେଣ୍ଟ ମୁକ୍ତି କରିଛୁ। %(key)s: ଏହି ଫାଇଲ୍ Library Genesis ରେ ଅଛି କି ନାହିଁ, ଅପ୍ରକୃତିକ କିମ୍ବା ପ୍ରକୃତିକ ସଂଗ୍ରହରେ (md5 ଦ୍ୱାରା ମେଳାଯାଇଛି)। %(key)s: ଏହି ଫାଇଲ୍ କେଉଁ ଟୋରେଣ୍ଟରେ ଅଛି। %(key)s: ଆମେ ପୁସ୍ତକ ଡାଉନଲୋଡ୍ କରିବାରେ ଅସମର୍ଥ ହେଲା। ମୁକ୍ତି 2 (%(date)s) Zlib ରିଲିଜ୍ (ମୂଳ ବର୍ଣ୍ଣନା ପୃଷ୍ଠାଗୁଡ଼ିକ) ଟୋର ଡୋମେନ ମୁଖ୍ୟ ୱେବସାଇଟ୍ ଜେ-ଲାଇବ୍ରେରୀ ସ୍କ୍ରାପ୍ Z-Library ର “ଚାଇନିଜ୍” ସଂଗ୍ରହ ଆମ DuXiu ସଂଗ୍ରହ ସହିତ ସମାନ ଲାଗୁଛି, କିନ୍ତୁ ଭିନ୍ନ MD5 ସହିତ। ଆମେ ନକଲ ରୋକିବା ପାଇଁ ଏହି ଫାଇଲଗୁଡ଼ିକୁ ଟୋରେଣ୍ଟରୁ ବାହାର କରିଥାଉ, କିନ୍ତୁ ଆମର ଖୋଜି ସୂଚକରେ ସେଗୁଡ଼ିକୁ ଦେଖାଉଛୁ। ମେଟାଡାଟା ଆପଣ %(profile_link)s ଦ୍ୱାରା ଉଲ୍ଲେଖିତ ହେବାରୁ ଆପଣ %(percentage)s%% ବୋନସ୍ ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ପାଉଛନ୍ତି। ଏହା ସମଗ୍ର ସଦସ୍ୟତା ଅବଧି ପାଇଁ ଲାଗୁ ହୁଏ। ଦାନ କରନ୍ତୁ ଯୋଗ ଦିଅନ୍ତୁ | ମନୋନୀତ | %(percentage)s ପର୍ଯ୍ୟନ୍ତ ରିହାତି Alipay ଆନ୍ତର୍ଜାତୀୟ କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡଗୁଡ଼ିକୁ ସମର୍ଥନ କରେ। ଅଧିକ ସୂଚନା ପାଇଁ <a %(a_alipay)s>ଏହି ଗାଇଡ୍</a> ଦେଖନ୍ତୁ। ଆମକୁ ଆପଣଙ୍କର କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ ବ୍ୟବହାର କରି Amazon.com ଗିଫ୍ଟ କାର୍ଡ ପଠାନ୍ତୁ। ଆପଣ କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ୍ ବ୍ୟବହାର କରି କ୍ରିପ୍ଟୋ କିଣିପାରିବେ। WeChat (Weixin Pay) ଆନ୍ତର୍ଜାତୀୟ କ୍ରେଡିଟ/ଡେବିଟ କାର୍ଡଗୁଡ଼ିକୁ ସମର୍ଥନ କରେ। WeChat ଆପ୍ରେ, “Me => Services => Wallet => Add a Card” କୁ ଯାଆନ୍ତୁ। ଯଦି ଆପଣ ଏହାକୁ ଦେଖିନାହାନ୍ତି, ତେବେ “Me => Settings => General => Tools => Weixin Pay => Enable” ବ୍ୟବହାର କରି ଏହାକୁ ସକ୍ରିୟ କରନ୍ତୁ। (Coinbase ରୁ Ethereum ପଠାଇବା ବେଳେ ବ୍ୟବହାର କରନ୍ତୁ) କପି ହୋଇଛି! କପି କରନ୍ତୁ | (ସବୁଠାରୁ କମ ନ୍ୟୁନତମ ରାଶି) (ଚେତାବନୀ: ଉଚ୍ଚ ନ୍ୟୁନତମ ପରିମାଣ) -%(percentage)s%% 12 ମାସ 1 ମାସ 24 ମାସ 3 ମାସ 48 ମାସ ପାଇଁ 6 ମାସ 96 ମାସ ପାଇଁ ଆପଣ କେତେ ଦିନ ସବସ୍କ୍ରାଇବ କରିବାକୁ ଚାହୁଁଛନ୍ତି ଚୟନ କରନ୍ତୁ | <div %(div_monthly_cost)s></div><div %(div_after)s>ପରେ<span %(span_discount)s></span> ରିହାତି</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 ମାସ ପାଇଁ 1 ମାସ ପାଇଁ 24 ମାସ ପାଇଁ 3 ମାସ ପାଇଁ 48 ମାସ ପାଇଁ 6 ମାସ ପାଇଁ 96 ମାସ ପାଇଁ %(monthly_cost)s / ମାସ ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ ସିଧାସଳଖ <strong>SFTP</strong> ସର୍ଭରଗୁଡ଼ିକ ନୂତନ ସଂଗ୍ରହଗୁଡ଼ିକ ପାଇଁ ଉଦ୍ୟମ ସ୍ତରର ଦାନ କିମ୍ବା ବଦଳାଉ (ଉଦାହରଣ ସ୍ୱରୂପ ନୂତନ ସ୍କାନ୍, OCR’ed datasets)। ବିଶେଷଜ୍ଞ ପ୍ରବେଶ <strong>ଅସୀମିତ |</strong> ଉଚ୍ଚ-ଗତି ପ୍ରବେଶ <div %(div_question)s>ମୁଁ ମୋର ସଦସ୍ୟତା ଅପଗ୍ରେଡ୍ କରିପାରିବି କିମ୍ବା ଅନେକ ସଦସ୍ୟତା ପାଇପାରିବି କି?</div> <div %(div_question)s>ସଦସ୍ୟ ହୋଇବା ବିନା ମୁଁ ଦାନ କରିପାରିବି କି?</div> ନିଶ୍ଚିତ ଭାବେ। ଆମେ ଏହି Monero (XMR) ଠିକଣାରେ ଯେକୌଣସି ପରିମାଣର ଦାନ ଗ୍ରହଣ କରୁ: %(address)s। <div %(div_question)s>ମାସ ପ୍ରତି ରେଞ୍ଜ୍ ମାନେ କ’ଣ?</div> ଆପଣ ସମସ୍ତ ଛାଡ଼ ପ୍ରୟୋଗ କରି ଏକ ରେଞ୍ଜ୍ ର ନିମ୍ନ ପ୍ରାନ୍ତକୁ ପହଞ୍ଚିପାରିବେ, ଯେପରିକି ଏକ ମାସରୁ ଅଧିକ ସମୟ ଚୟନ କରିବା। <div %(div_question)s>ସଦସ୍ୟତା ସ୍ୱୟଂଚାଳିତ ଭାବେ ପୁନର୍ନବୀକରଣ ହୁଏ କି?</div> ସଦସ୍ୟତା<strong>ସ୍ୱୟଂଚାଳିତ ଭାବେ ପୁନର୍ନବୀକରଣ ହୁଏ ନାହିଁ</strong>। ଆପଣ ଯେତେ ଦିନ ଚାହୁଁଛନ୍ତି ସେତେ ଦିନ ଯୋଗ ଦେଇପାରିବେ। <div %(div_question)s>ଆପଣ ଦାନରେ କେଉଁଠି ଖର୍ଚ୍ଚ କରନ୍ତି?</div> 100%% ବିଶ୍ୱର ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ସଂରକ୍ଷଣ କରିବା ଏବଂ ପ୍ରବେଶ ଯୋଗାଇବାରେ ଯାଉଛି। ବର୍ତ୍ତମାନ ଆମେ ଏହାକୁ ମୁଖ୍ୟତଃ ସର୍ଭର, ସ୍ଟୋରେଜ୍ ଏବଂ ବ୍ୟାଣ୍ଡୱିଡ୍‌ଥରେ ଖର୍ଚ୍ଚ କରୁ। କୌଣସି ଟଙ୍କା କୌଣସି ଦଳ ସଦସ୍ୟଙ୍କୁ ବ୍ୟକ୍ତିଗତ ଭାବରେ ଯାଉନାହିଁ। <div %(div_question)s>ମୁଁ ବଡ଼ ଦାନ କରିପାରିବି କି?</div> ଏହା ଅଦ୍ଭୁତ ହେବ! କିଛି ହଜାର ଡଲାର ଠାରୁ ଅଧିକ ଦାନ ପାଇଁ, ଦୟାକରି ଆମ ସହିତ ସିଧାସଳଖ କଂଟାକ୍ଟ କରନ୍ତୁ %(email)s। <div %(div_question)s>ଆପଣଙ୍କ ପାଖରେ ଅନ୍ୟ କ payment ଣସି ଦେୟ ପ୍ରଣାଳୀ ଅଛି କି?</div> ବର୍ତ୍ତମାନ ନାହିଁ। ବହୁତ ଲୋକ ଏପରି ଆର୍କାଇଭ୍ ରହିବାକୁ ଚାହୁଁନ୍ତି ନାହିଁ, ତେଣୁ ଆମେ ସାବଧାନ ରହିବାକୁ ପଡ଼େ। ଯଦି ଆପଣ ଆମକୁ ଅନ୍ୟ (ଅଧିକ ସୁବିଧାଜନକ) ଦେୟ ପ୍ରଣାଳୀ ସୁରକ୍ଷିତ ଭାବରେ ସେଟ୍ ଅପ୍ କରିବାରେ ସାହାଯ୍ୟ କରିପାରିବେ, ତେବେ %(email)s ରେ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଦାନ ସମ୍ବନ୍ଧୀୟ FAQ ଆପଣଙ୍କର ଏକ <a %(a_donation)s>ବିଦ୍ୟମାନ ଦାନ</a> ଚାଲିଛି | ଏକ ନୂତନ ଦାନ କରିବା ପୂର୍ବରୁ ଦୟାକରି ସେହି ଦାନ ଶେଷ କରନ୍ତୁ କିମ୍ବା ବାତିଲ କରନ୍ତୁ | <a %(a_all_donations)s>ମୋର ସମସ୍ତ ଦାନ ଦେଖନ୍ତୁ |</a> $ ୫୦୦୦ ରୁ ଅଧିକ ଦାନ ପାଇଁ ଦୟାକରି ଆମ ସହିତ ସିଧାସଳଖ ଯୋଗାଯୋଗ କରନ୍ତୁ | %(email)s. ଆମେ ଧନାଢ୍ୟ ବ୍ୟକ୍ତିବିଶେଷ କିମ୍ବା ସଂସ୍ଥାଗୁଡ଼ିକରୁ ବଡ଼ ଦାନକୁ ସ୍ୱାଗତ କରୁଛୁ।  ଏହି ପୃଷ୍ଠାରେ ସଦସ୍ୟତାଗୁଡିକ "ପ୍ରତି ମାସ" ହେଲେ ମଧ୍ୟ, ସେଗୁଡିକ ଏକ-ବାର ଦାନ (ପୁନରାବୃତ୍ତି ନ ହେବା) ଅଟେ। <a %(faq)s>ଦାନ FAQ</a> ଦେଖନ୍ତୁ। ଆନ୍ନାଙ୍କ ଅଭିଲେଖାଗାର ହେଉଛି ଏକ ଅଣ-ଲାଭ, ମୁକ୍ତ ଉତ୍ସ, ଖୋଲା ଡାଟା ପ୍ରୋଜେକ୍ଟ | ଦାନ ଏବଂ ସଦସ୍ୟ ହେବା ଦ୍ୱାରା, ଆପଣ ଆମର କାର୍ଯ୍ୟ ଏବଂ ବିକାଶକୁ ସମର୍ଥନ କରନ୍ତି | ଆମର ସମସ୍ତ ସଦସ୍ୟଙ୍କୁ: ଆମକୁ ଜାରି ରଖିଥିବାରୁ ଧନ୍ୟବାଦ! ❤️ ଅଧିକ ସୂଚନା ପାଇଁ, <a %(a_donate)s>ଦାନ ସମ୍ବନ୍ଧୀୟ FAQ</a> ଦେଖନ୍ତୁ। ସଦସ୍ୟ ହେବାକୁ ଦୟାକରି <a %(a_login)s>ଲଗ୍ ଇନ୍ କିମ୍ବା ରେଜିଷ୍ଟର କରନ୍ତୁ |</a> ଆପଣଙ୍କର ସମର୍ଥନ ପାଇଁ ଧନ୍ୟବାଦ! $%(cost)s / ମାସ ଯଦି ଆପଣ ଅର୍ଥ ପ୍ରଦାନ ସମୟରେ ଭୁଲ କରିଛନ୍ତି, ଆମେ ଫେରତି ଦେଇପାରିବୁ ନାହିଁ, କିନ୍ତୁ ଆମେ ଏହାକୁ ଠିକ୍ କରିବାକୁ ଚେଷ୍ଟା କରିବା। ଆପଣଙ୍କ PayPal ଆପ୍ ବା ୱେବସାଇଟରେ “Crypto” ପୃଷ୍ଠା ଖୋଜନ୍ତୁ। ଏହା ସାଧାରଣତଃ “Finances” ଅଧୀନରେ ରହିଥାଏ। ଆପଣଙ୍କ PayPal ଆପ୍ ବା ୱେବସାଇଟରେ “Bitcoin” ପୃଷ୍ଠାକୁ ଯାଆନ୍ତୁ। “Transfer” ବଟନ୍ %(transfer_icon)s ଦବାନ୍ତୁ, ଏବଂ ପରେ “Send”। Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s ଉପହାର କାର୍ଡ ବ୍ୟାଙ୍କ କାର୍ଡ ବ୍ୟାଙ୍କ କାର୍ଡ (ଆପ୍ପ ବ୍ୟବହାର କରି) ବାଇନାନ୍ସ କ୍ରେଡିଟ୍/ଡେବିଟ୍/Apple/Google (BMC) ଆପ୍ପ ଚାଷ (Cash App) କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ୍ କ୍ରେଡିଟ୍ / ଡେବିଟ୍ କାର୍ଡ ୨ କ୍ରେଡିଟ/ଡେବିଟ କାର୍ଡ (ବ୍ୟାକଅପ) Crypto %(bitcoin_icon)s କାର୍ଡ / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (ନିୟମିତ) Pix (Brazil) Revolut (ଅସ୍ଥାୟୀ ଭାବରେ ଉପଲବ୍ଧ ନାହିଁ |) WeChat ଆପଣଙ୍କର ପସନ୍ଦିତ କ୍ରିପ୍ଟୋ ମୁଦ୍ରା ଚୟନ କରନ୍ତୁ: ଆମାଜନ ଉପହାର କାର୍ଡ ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ | <strong>ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ:</strong> ଏହି ବିକଳ୍ପ %(amazon)s ପାଇଁ ଅଟେ। ଯଦି ଆପଣ ଅନ୍ୟାନ୍ୟ Amazon ୱେବସାଇଟ୍ ବ୍ୟବହାର କରିବାକୁ ଚାହାଁନ୍ତି, ତାହାହେଲେ ଉପରେ ଏହାକୁ ଚୟନ କରନ୍ତୁ। <strong>ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ |:</strong> ଆମେ କେବଳ ଆମାଜନ ଡଟ୍ କମ୍ କୁ ସମର୍ଥନ କରୁ, ଅନ୍ୟ ଆମାଜନ ୱେବସାଇଟ୍ ନୁହେଁ | ଉଦାହରଣ ସ୍ୱରୂପ, .de, .co.uk, .ca, ସମର୍ଥିତ ନୁହେଁ | ଦୟାକରି ଆପଣଙ୍କର ନିଜସ୍ୱ ସନ୍ଦେଶ ଲେଖନ୍ତୁ ନାହିଁ। ଠିକ ମାତ୍ରା ପ୍ରବେଶ କରନ୍ତୁ: %(amount)s ଦୟାକରି ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ଆମେ ଆମର ରିସେଲର୍ମାନଙ୍କ ଦ୍ୱାରା ଗ୍ରହଣ କରାଯାଇଥିବା ରାଶିକୁ ରାଉଣ୍ଡ କରିବାକୁ ପଡ଼ିବ (ନ୍ୟୁନତମ %(minimum)s)। କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ ବ୍ୟବହାର କରି, Alipay ଆପ୍ପ ମାଧ୍ୟମରେ ଦାନ କରନ୍ତୁ (ସଜା ସଜିଲା ସଜାଇବା ଅତ୍ୟନ୍ତ ସହଜ)। <a %(a_app_store)s>ଆପଲ୍ ଆପ୍ ପ୍ରଦାନାଳୟ</a> କିମ୍ବା <a %(a_play_store)s>ଗୁଗୁଲ୍ ପ୍ଲେ ସ୍ଟୋର୍</a> ରୁ Alipay ଆପ୍ପ ଇନଷ୍ଟଲ୍ କରନ୍ତୁ। ଆପଣଙ୍କର ଫୋନ୍ ନମ୍ବର ବ୍ୟବହାର କରି ରେଜିଷ୍ଟର୍ କରନ୍ତୁ। ଅଧିକ ବ୍ୟକ୍ତିଗତ ବିବରଣୀ ଆବଶ୍ୟକ ନାହିଁ। <span %(style)s>1</span> Alipay ଆପ୍ପ ଇନଷ୍ଟଲ୍ କରନ୍ତୁ ସମର୍ଥିତ: ଭିସା, ମାଷ୍ଟରକାର୍ଡ, JCB, ଡାଇନର୍ସ କ୍ଲବ୍ ଏବଂ ଡିସକଭର୍। ଅଧିକ ସୂଚନା ପାଇଁ <a %(a_alipay)s>ଏହି ଗାଇଡ୍</a> ଦେଖନ୍ତୁ। <span %(style)s>2</span> ବ୍ୟାଙ୍କ କାର୍ଡ ଯୋଡନ୍ତୁ Binance ସହିତ, ଆପଣ କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ କିମ୍ବା ବ୍ୟାଙ୍କ ଖାତା ସହିତ ବିଟକୋଇନ୍ କିଣନ୍ତି, ଏବଂ ପରେ ଆମକୁ ସେହି ବିଟକୋଇନ୍ ଦାନ କରନ୍ତି। ଏହା ମାଧ୍ୟମରେ ଆମେ ଆପଣଙ୍କ ଦାନ ଗ୍ରହଣ କରିବା ସମୟରେ ସୁରକ୍ଷିତ ଏବଂ ଗୋପନୀୟ ରହିପାରିବା। Binance ପ୍ରାୟ ସମସ୍ତ ଦେଶରେ ଉପଲବ୍ଧ ଅଛି, ଏବଂ ଅଧିକାଂଶ ବ୍ୟାଙ୍କ ଏବଂ କ୍ରେଡିଟ/ଡେବିଟ କାର୍ଡଗୁଡ଼ିକୁ ସମର୍ଥନ କରେ। ଏହା ଏବେ ଆମର ପ୍ରମୁଖ ସୁପାରିଶ। ଆମେ ଆପଣଙ୍କୁ ଏହି ପ୍ରକ୍ରିୟା ବ୍ୟବହାର କରି ଦାନ କରିବାକୁ ଶିଖିବା ପାଇଁ ସମୟ ନେବାକୁ ଆଭାର ଜଣାଉଛୁ, କାରଣ ଏହା ଆମକୁ ବହୁତ ସାହାଯ୍ୟ କରେ। କ୍ରେଡିଟ କାର୍ଡ, ଡେବିଟ କାର୍ଡ, Apple Pay, ଏବଂ Google Pay ପାଇଁ, ଆମେ “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) ବ୍ୟବହାର କରୁଛୁ। ତାଙ୍କ ସିଷ୍ଟମରେ, ଗୋଟିଏ “coffee” $5 ସମାନ, ତେଣୁ ଆପଣଙ୍କ ଦାନ 5ର ନିକଟତମ ଗୁଣିତକରେ ରାଉଣ୍ଡ କରାଯିବ। Cash App ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ | Cash App ଯଦି ତୁମର ଅଛି, ଦାନ କରିବାର ଏହା ହେଉଛି ସହଜ ଉପାୟ! ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ଅଧୀନରେ କାରବାର ପାଇଁ %(amount)s, ଆପ୍ପ ଚାଷ (Cash App) ଚାର୍ଜ କରିପାରନ୍ତି a %(fee)s ଶୁଳ୍କ ପାଇଁ %(amount)s କିମ୍ବା ଉପରେ, ଏହା ମୁକ୍ତ! ଏକ କ୍ରେଡିଟ୍ କିମ୍ବା ଡେବିଟ୍ କାର୍ଡ ସହିତ ଦାନ କରନ୍ତୁ | ଏହି ପ୍ରକ୍ରିୟା ଏକ ଆନ୍ତର୍ଭୁକ୍ତିକ ରୂପାନ୍ତରଣ ଭାବରେ ଏକ କ୍ରିପ୍ଟୋକରେନ୍ସି ପ୍ରଦାନକାରୀକୁ ବ୍ୟବହାର କରେ। ଏହା କିଛି ଅସ୍ପଷ୍ଟ ହୋଇପାରେ, ତେଣୁ ଦୟାକରି ଏହି ପ୍ରକ୍ରିୟାକୁ କେବଳ ଉପଯୁକ୍ତ ଅନ୍ୟ ପ୍ରଦାନ ପ୍ରକ୍ରିୟାଗୁଡ଼ିକ କାମ କରୁନାହିଁ ତେବେ ବ୍ୟବହାର କରନ୍ତୁ। ଏହା ସମସ୍ତ ଦେଶରେ କାମ କରେ ନାହିଁ। ଆମେ ସିଧାସଳଖ କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ ସମର୍ଥନ କରିପାରିବୁ ନାହିଁ, କାରଣ ବ୍ୟାଙ୍କମାନେ ଆମ ସହିତ କାମ କରିବାକୁ ଚାହୁଁନ୍ତି ନାହିଁ। ☹ ତଥାପି, ଅନ୍ୟାନ୍ୟ ଦେୟ ପ୍ରଣାଳୀ ବ୍ୟବହାର କରି କ୍ରେଡିଟ୍/ଡେବିଟ୍ କାର୍ଡ ବ୍ୟବହାର କରିବାର କିଛି ଉପାୟ ଅଛି: କ୍ରିପ୍ଟୋ ସହିତ ଆପଣ BTC, ETH, XMR, ଏବଂ SOL ବ୍ୟବହାର କରି ଦାନ କରିପାରିବେ | ଯଦି ଆପଣ କ୍ରିପ୍ଟୋକରେନ୍ସି ସହିତ ପୂର୍ବରୁ ପରିଚିତ ତେବେ ଏହି ବିକଳ୍ପ ବ୍ୟବହାର କରନ୍ତୁ | କ୍ରିପ୍ଟୋ ସହିତ ଆପଣ BTC, ETH, XMR, ଏବଂ ଅଧିକ ବ୍ୟବହାର କରି ଦାନ କରିପାରିବେ | କ୍ରିପ୍ଟୋ ଏକ୍ସପ୍ରେସ୍ ସେବା ଯଦି ଆପଣ ପ୍ରଥମଥର ପାଇଁ କ୍ରିପ୍ଟୋ ବ୍ୟବହାର କରୁଛନ୍ତି, ଆମେ %(options)s ବ୍ୟବହାର କରି ବିଟକୋଇନ୍ (ମୂଳ ଏବଂ ସବୁଠାରୁ ବହୁଳ ଭାବରେ ବ୍ୟବହୃତ କ୍ରିପ୍ଟୋକରେନ୍ସି) କିଣିବା ଏବଂ ଦାନ କରିବାକୁ ପରାମର୍ଶ ଦେଉଛୁ। ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ଛୋଟ ଦାନ ପାଇଁ କ୍ରେଡିଟ୍ କାର୍ଡ ଫି ଆମର %(discount)s%% ରିହାତିକୁ ଦୂର କରିପାରେ | ତେଣୁ ଆମେ ଅଧିକ ସଦସ୍ୟତା ପାଇଁ ସୁପାରିଶ କରୁ | କ୍ରେଡିଟ/ଡେବିଟ କାର୍ଡ, PayPal, କିମ୍ବା Venmo ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ। ଆପଣ ପରବର୍ତ୍ତୀ ପୃଷ୍ଠାରେ ଏହାମାନଙ୍କ ମଧ୍ୟରୁ ଏକ ବାଛିପାରିବେ। ଗୁଗୁଲ୍ ପେ ଏବଂ ଆପଲ୍ ପେ ମଧ୍ୟ କାମ କରିପାରେ | ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ଛୋଟ ଦାନ ପାଇଁ ଦେୟ ଅଧିକ, ତେଣୁ ଆମେ ଅଧିକ ସଦସ୍ୟତା ପାଇଁ ସୁପାରିଶ କରୁ | ପେପାଲ US ବ୍ୟବହାର କରି ଦାନ କରିବାକୁ, ଆମେ ପେପାଲ କ୍ରିପ୍ଟୋ ବ୍ୟବହାର କରିବାକୁ ଯାଉଛୁ, ଯାହା ଆମକୁ ଅଜ୍ଞାତ ରହିବାକୁ ଦେଇଥାଏ | ଏହି ପଦ୍ଧତିକୁ ବ୍ୟବହାର କରି କିପରି ଦାନ କରିବେ ଶିଖିବା ପାଇଁ ଆମେ ସମୟ ନେଇଥିବାରୁ ଆମେ ଆପଣଙ୍କୁ ପ୍ରଶଂସା କରୁଛୁ, ଯେହେତୁ ଏହା ଆମକୁ ବହୁତ ସାହାଯ୍ୟ କରିଥାଏ | PayPal ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ | ଆପଣଙ୍କର ନିୟମିତ PayPal ଖାତା ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ। Revolut ବ୍ୟବହାର କରି ଦାନ କରନ୍ତୁ। ଯଦି ଆପଣଙ୍କ ପାଖରେ Revolut ଅଛି, ତାହେଲେ ଏହା ଦାନ କରିବାର ସବୁଠାରୁ ସହଜ ଉପାୟ! ଏହି ଦେୟ ପଦ୍ଧତି କେବଳ ସର୍ବାଧିକ ପାଇଁ ଅନୁମତି ଦିଏ | %(amount)s ଦୟାକରି ଏକ ଭିନ୍ନ ସମୟସୀମା କିମ୍ବା ଦେୟ ପଦ୍ଧତି ବାଛନ୍ତୁ | ଏହି ଦେୟ ପ୍ରଣାଳୀ ସର୍ବନିମ୍ନ ଆବଶ୍ୟକ କରେ | %(amount)s ଦୟାକରି ଏକ ଭିନ୍ନ ସମୟସୀମା କିମ୍ବା ଦେୟ ପଦ୍ଧତି ବାଛନ୍ତୁ | ବାଇନାନ୍ସ Coinbase Kraken ଦୟାକରି ଏକ ଅଦାୟନ ପ୍ରକ୍ରିୟା ବାଛନ୍ତୁ। “ଏକ ଟୋରେଣ୍ଟ ଫାଇଲ୍ ନାମ ପାଆନ୍ତୁ |”: ଏକ ଟୋରେଣ୍ଟ ଫାଇଲ ନାମରେ ଆପଣଙ୍କର ଉପଯୋଗକର୍ତ୍ତା ନାମ କିମ୍ବା ବାର୍ତ୍ତା | <div %(div_months)s>ପ୍ରତି ୧୨ ମାସରେ ଥରେ ସଦସ୍ୟତା |</div> ଆପଣଙ୍କର ୟୁଜରନେମ୍ କିମ୍ବା ଗୋପନୀୟ ଉଲ୍ଲେଖ କ୍ରେଡିଟ୍‌ରେ ନୂତନ ବୈଶିଷ୍ଟ୍ୟଗୁଡ଼ିକ ପାଇଁ ପ୍ରାରମ୍ଭିକ ପ୍ରବେଶ ପରଦାର ପଛର ଅଦ୍ୟତନ ସହିତ ବିଶେଷ Telegram %(number)s ପ୍ରତିଦିନ ଦ୍ରୁତ ଡାଉନଲୋଡ୍ | ଯଦି ଆପଣ ଏହି ମାସରେ ଦାନ କରନ୍ତି! <a %(a_api)s>JSON API</a> ଅଭିଗମ ମାନବତାର ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିର ସଂରକ୍ଷଣରେ ପ୍ରସିଦ୍ଧ ସ୍ଥାନ ପୂର୍ବ ମାନ୍ୟତା, ସହିତ: <a %(a_refer)s>ମିତ୍ରମାନଙ୍କୁ ଉଲ୍ଲେଖ କରି</a> <strong>%(percentage)s%% ବୋନସ୍ ଡାଉନଲୋଡ୍</strong> ଅର୍ଜନ କରନ୍ତୁ। SciDB ଯାଞ୍ଚ ବିନା <strong>ଅସୀମିତ</strong> କାଗଜପତ୍ର | ଖାତା କିମ୍ବା ଦାନ ବିଷୟରେ ପ୍ରଶ୍ନ କରିବା ସମୟରେ, ଆପଣଙ୍କର ଖାତା ID, ସ୍କ୍ରିନସ୍ହଟ୍, ରସିଦ, ଯେତେକି ସମ୍ଭବ ତଥ୍ୟ ଯୋଗ କରନ୍ତୁ। ଆମେ ପ୍ରତି 1-2 ସପ୍ତାହରେ ଏକଥର ଇମେଲ୍ ଯାଞ୍ଚ କରୁଛୁ, ଏହି ତଥ୍ୟ ଯୋଗ ନକଲେ କୌଣସି ସମାଧାନ ବିଳମ୍ବିତ ହେବ। ଅଧିକ ଡାଉନଲୋଡ୍ ପାଇଁ, <a %(a_refer)s>ଆପଣଙ୍କ ମିତ୍ରମାନଙ୍କୁ ଉଲ୍ଲେଖ କରନ୍ତୁ</a>! ଆମେ ଏକ ଛୋଟ ସ୍ୱେଚ୍ଛାସେବୀ ଦଳ। ଆମକୁ ପ୍ରତିସାଦ ଦେବାକୁ 1-2 ସପ୍ତାହ ଲାଗିପାରେ। ଏହାକୁ ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ଖାତା ନାମ କିମ୍ବା ଛବି ଅସମାନ୍ୟ ଦେଖାଯାଇପାରେ। ଚିନ୍ତା କରିବାକୁ କିଛି ନାହିଁ! ଏହି ଖାତାଗୁଡ଼ିକ ଆମର ଦାନ ସହଯୋଗୀମାନଙ୍କ ଦ୍ୱାରା ପରିଚାଳିତ ହେଉଛି। ଆମର ଖାତାଗୁଡ଼ିକ ହ୍ୟାକ ହୋଇନାହିଁ। ଦାନ କରନ୍ତୁ | <span %(span_cost)s></span> <span %(span_label)s></span> 12 ମାସ ପାଇଁ “%(tier_name)s” 1 ମାସ ପାଇଁ “%(tier_name)s” 24 ମାସ ପାଇଁ “%(tier_name)s” 3 ମାସ ପାଇଁ “%(tier_name)s” 48 ମାସ ପାଇଁ “%(tier_name)s” 6 ମାସ ପାଇଁ “%(tier_name)s” 96 ମାସ ପାଇଁ “%(tier_name)s” ଚେକଆଉଟ୍ ସମୟରେ ଆପଣ ତଥାପି ଦାନ ବାତିଲ କରିପାରିବେ | ଏହି ଦାନ ନିଶ୍ଚିତ କରିବାକୁ ଦାନ ବଟନ୍ କ୍ଲିକ୍ କରନ୍ତୁ | <strong>ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଟିପ୍ପଣୀ:</strong> କ୍ରିପ୍ଟୋ ମୂଲ୍ୟଗୁଡ଼ିକ ବହୁତ ଅସ୍ଥିର ହୋଇପାରେ, କେବଳ କିଛି ମିନିଟ୍ ମଧ୍ୟରେ 20%% ପର୍ଯ୍ୟନ୍ତ। ଏହା ଆମେ ଅନେକ ପେମେଣ୍ଟ ପ୍ରଦାନକାରୀ ସହିତ ଯେଉଁ ଶୁଳ୍କ ଭୋଗ କରୁଥିଲୁ ସେଥାରୁ କମ୍, ଯେଉଁମାନେ ପ୍ରାୟତଃ 50-60%% ଚାର୍ଜ କରନ୍ତି ଏକ “ଛାୟା ଦାନଶୀଳ ସଂସ୍ଥା” ଭଳି ଆମ ସହିତ କାମ କରିବା ପାଇଁ। <u>ଯଦି ଆପଣ ଆମକୁ ଆପଣଙ୍କ ଦ୍ୱାରା ଦିଆଯାଇଥିବା ମୂଲ୍ୟ ସହିତ ରସିଦ ପଠାନ୍ତି, ଆମେ ଆପଣଙ୍କ ଖାତାକୁ ଚୟନିତ ସଦସ୍ୟତା ପାଇଁ କ୍ରେଡିଟ୍ କରିବାକୁ ଚେଷ୍ଟା କରିବୁ</u> (ଯେପର୍ଯ୍ୟନ୍ତ ରସିଦ କିଛି ଘଣ୍ଟାରୁ ଅଧିକ ପୁରୁଣା ନୁହେଁ)। ଆପଣ ଆମକୁ ସମର୍ଥନ କରିବା ପାଇଁ ଏହି ପ୍ରକାର ଜିନିଷ ସହିବାକୁ ଇଚ୍ଛୁକ ଥିବାରୁ ଆମେ ଆପଣଙ୍କୁ ଅତ୍ୟନ୍ତ ଆଭାରୀ! ❤️ ❌ କିଛି ଭୁଲ ହୋଇ ଗଲା। ଦୟାକରି ପୃଷ୍ଠା ପୁନ o ଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ | <span %(span_circle)s>1</span>Paypal ରେ Bitcoin କିଣନ୍ତୁ <span %(span_circle)s>2</span>ଆମ ଠିକଣାକୁ ବିଟକୋଇନ୍ ହସ୍ତାନ୍ତର କରନ୍ତୁ ✅ ଦାନ ପୃଷ୍ଠାକୁ ପୁନ ir ନିର୍ଦ୍ଦେଶ କରିବା… ଦାନ କରନ୍ତୁ ଦୟାକରି ଆମ ସହିତ ଯୋଗାଯୋଗ କରିବା ପୂର୍ବରୁ କମରେ କମେ <span %(span_hours)s>24 ଘଣ୍ଟା</span> ଅପେକ୍ଷା କରନ୍ତୁ (ଏବଂ ଏହି ପୃଷ୍ଠାକୁ ରିଫ୍ରେଶ୍ କରନ୍ତୁ)। ଯଦି ଆପଣ ଏକ ଦାନ (କୌଣସି ରାଶି) ଦେବାକୁ ଚାହୁଁଛନ୍ତି ଯାହାକି ସଦସ୍ୟତା ବିନା, ତେବେ ଏହି Monero (XMR) ଠିକଣା ବ୍ୟବହାର କରନ୍ତୁ: %(address)s। ଆପଣଙ୍କ ଉପହାର କାର୍ଡ ପଠାଇବା ପରେ, ଆମର ସ୍ୱୟଂଚାଳିତ ପ୍ରଣାଳୀ କିଛି ମିନିଟ ମଧ୍ୟରେ ଏହାକୁ ନିଶ୍ଚିତ କରିବ। ଯଦି ଏହା କାମ କରେ ନାହିଁ, ତେବେ ଆପଣଙ୍କ ଉପହାର କାର୍ଡକୁ ପୁନଃ ପଠାଇବାକୁ ଚେଷ୍ଟା କରନ୍ତୁ (<a %(a_instr)s>ନିର୍ଦ୍ଦେଶନାମା</a>)। ଯଦି ଏହା ତଥାପି କାମ କରେ ନାହିଁ, ଦୟାକରି ଆମକୁ ଇମେଲ୍ କରନ୍ତୁ ଏବଂ ଆନା ଏହାକୁ ହସ୍ତଚାଳିତ ଭାବରେ ସମୀକ୍ଷା କରିବେ (ଏହାକୁ କିଛି ଦିନ ଲାଗିପାରେ), ଏବଂ ଆପଣ ପୁନଃ ପଠାଇବାକୁ ଚେଷ୍ଟା କରିଥିବା କଥା ଉଲ୍ଲେଖ କରନ୍ତୁ। ଉଦାହରଣ: ଦୟାକରି <a %(a_form)s>ଆଧିକାରିକ Amazon.com ଫର୍ମ</a> ବ୍ୟବହାର କରନ୍ତୁ ଆମକୁ %(amount)s ଗିଫ୍ଟ କାର୍ଡ ପଠାଇବା ପାଇଁ ନିମ୍ନଲିଖିତ ଇମେଲ୍ ଠିକଣାକୁ। ଫର୍ମରେ “ପ୍ରତି” ଗ୍ରାହକ ଇମେଲ୍: ଆମାଜନ୍ ଉପହାର କାର୍ଡ ଆମେ ଅନ୍ୟ ଉପହାର କାର୍ଡ ପ୍ରକାରଗୁଡ଼ିକ ଗ୍ରହଣ କରିପାରିବୁ ନାହିଁ, <strong>କେବଳ ଆମାଜନ୍.କମ୍ର ସରକାରୀ ଫର୍ମରୁ ସିଧାସଳଖ ଭାବରେ ପଠାଯାଇଥିବା</strong>। ଯଦି ଆପଣ ଏହି ଫର୍ମ ବ୍ୟବହାର କରିନାହାନ୍ତି, ଆମେ ଆପଣଙ୍କର ଉପହାର କାର୍ଡ ଫେରତ ଦେଇପାରିବୁ ନାହିଁ। କେବଳ ଏକଥର ବ୍ୟବହାର କରନ୍ତୁ। ଅନ୍ୟଙ୍କ ସହିତ ଆପଣଙ୍କ ଖାତାର ଏହି ବିଶେଷତାକୁ ଅଂଶୀଦାର କରନ୍ତୁ ନାହିଁ। ଗିଫ୍ଟ କାର୍ଡ ପାଇଁ ଅପେକ୍ଷା କରୁଛି… (ପୃଷ୍ଠାକୁ ତାଜା କରି ଯାଞ୍ଚ କରନ୍ତୁ) <a %(a_href)s>QR-କୋଡ୍ ଦାନ ପୃଷ୍ଠା</a> ଖୋଲନ୍ତୁ। Alipay ଆପ୍ ସହିତ QR କୋଡ୍ ସ୍କାନ କରନ୍ତୁ, କିମ୍ବା Alipay ଆପ୍ ଖୋଲିବାକୁ ବଟନ୍ ଦବାନ୍ତୁ। ଦୟାକରି ଧୈର୍ଯ୍ୟ ଧରନ୍ତୁ; ପୃଷ୍ଠାଟି ଚୀନରେ ଥିବାରୁ ଲୋଡ୍ ହେବାକୁ ସମୟ ଲାଗିପାରେ। <span %(style)s>3</span>ଦାନ କରନ୍ତୁ (QR କୋଡ୍ ସ୍କାନ କରନ୍ତୁ କିମ୍ବା ବଟନ୍ ଦବାନ୍ତୁ) PayPal ରେ PYUSD କଏନ୍ କିଣନ୍ତୁ କ୍ୟାଶ୍ ଆପ୍‌ରେ ବିଟକଏନ୍ (BTC) କିଣନ୍ତୁ ଲେନଦେନ ଶୁଳ୍କ ଆବରଣ କରିବା ପାଇଁ ଆପଣ ଯେତେକି ଦାନ କରୁଛନ୍ତି (%(amount)s), ତାହାଠାରୁ ଅଧିକ (ଆମେ %(more)s ଅଧିକ ସୁପାରିଶ କରୁଛୁ) କିଣନ୍ତୁ। ଯାହା ଅବଶିଷ୍ଟ ରହିବ, ତାହା ଆପଣ ରଖିପାରିବେ। କ୍ୟାଶ୍ ଆପ୍‌ରେ “ବିଟକଏନ୍” (BTC) ପୃଷ୍ଠାକୁ ଯାଆନ୍ତୁ। ଆମ ପତାକୁ ବିଟକଏନ୍ ପଠାନ୍ତୁ ଛୋଟ ଦାନ (25 ଡଲାର୍‌ରୁ କମ୍) ପାଇଁ, ଆପଣଙ୍କୁ Rush କିମ୍ବା Priority ବ୍ୟବହାର କରିବାକୁ ପଡ଼ିପାରେ। “ବିଟକଏନ୍ ପଠାନ୍ତୁ” ବଟନ୍‌କୁ କ୍ଲିକ୍ କରି “ଉତ୍ତୋଳନ” କରନ୍ତୁ। %(icon)s ଆଇକନ୍‌କୁ ଦବାଇ ଡଲାର୍‌ରୁ BTC କୁ ସ୍ୱିଚ୍ କରନ୍ତୁ। ନିମ୍ନରେ BTC ପରିମାଣ ପ୍ରବେଶ କରନ୍ତୁ ଏବଂ “ପଠାନ୍ତୁ” ବଟନ୍‌କୁ କ୍ଲିକ୍ କରନ୍ତୁ। ଯଦି ଆପଣ ଅଟକିଯାନ୍ତି, <a %(help_video)s>ଏହି ଭିଡିଓ</a> ଦେଖନ୍ତୁ। ଏକ୍ସପ୍ରେସ୍ ସେବାଗୁଡିକ ସୁବିଧାଜନକ, କିନ୍ତୁ ଅଧିକ ଶୁଳ୍କ ଆଦାୟ କରନ୍ତି। ଯଦି ଆପଣ ଏକ ବଡ଼ ଦାନ ଦେବାକୁ ଚାହୁଁଛନ୍ତି ଏବଂ $5-10 ଶୁଳ୍କ ଦେବାକୁ ମନ ନାହିଁ, ତେବେ ଆପଣ ଏହାକୁ କ୍ରିପ୍ଟୋ ଏକ୍ସଚେଞ୍ଜ୍ ବଦଳରେ ବ୍ୟବହାର କରିପାରିବେ। ଦାନ ପୃଷ୍ଠାରେ ଦର୍ଶାଯାଇଥିବା ନିକ୍ଷିପ୍ତ କ୍ରିପ୍ଟୋ ରାଶିକୁ ନିଶ୍ଚିତ କରନ୍ତୁ, $USD ରାଶିକୁ ନୁହେଁ। ନହେଲେ ଶୁଳ୍କ କମିଯିବ ଏବଂ ଆମେ ଆପଣଙ୍କର ସଦସ୍ୟତାକୁ ସ୍ୱୟଂଚାଳିତ ଭାବେ ପ୍ରକ୍ରିୟାକରଣ କରିପାରିବୁ ନାହିଁ। କେବେ କେବେ ନିଶ୍ଚିତିକରଣକୁ 24 ଘଣ୍ଟା ପର୍ଯ୍ୟନ୍ତ ସମୟ ଲାଗିପାରେ, ତେଣୁ ନିଶ୍ଚିତ ହେବାକୁ ଏହି ପୃଷ୍ଠାକୁ ପୁନଃତାଜା କରନ୍ତୁ (ଯଦି ଏହାର ସମୟ ସମାପ୍ତ ହୋଇଥାଏ ମଧ୍ୟ)। କ୍ରେଡିଟ୍ / ଡେବିଟ୍ କାର୍ଡ ନିର୍ଦ୍ଦେଶାବଳୀ ଆମର କ୍ରେଡିଟ୍ / ଡେବିଟ୍ କାର୍ଡ ପୃଷ୍ଠା ମାଧ୍ୟମରେ ଦାନ କରନ୍ତୁ କିଛି ପଦକ୍ଷେପ ରେ କ୍ରିପ୍ଟୋ ୱାଲେଟ୍ ଉଲ୍ଲେଖ ହୋଇଛି, କିନ୍ତୁ ଚିନ୍ତା କରନ୍ତୁ ନାହିଁ, ଆପଣଙ୍କୁ କ୍ରିପ୍ଟୋ ବିଷୟରେ କିଛି ଶିଖିବାକୁ ପଡ଼ିବ ନାହିଁ। %(coin_name)s ନିର୍ଦ୍ଦେଶନାମା ଦେୟ ବିବରଣୀଗୁଡିକ ଶୀଘ୍ର ପୂରଣ କରିବାକୁ ଆପଣଙ୍କର କ୍ରିପ୍ଟୋ ୱାଲେଟ୍ ଆପ୍ ସହିତ ଏହି QR କୋଡ୍ ସ୍କାନ୍ କରନ୍ତୁ | ଦେୟ ଦେବାକୁ QR କୋଡ୍ ସ୍କାନ୍ କରନ୍ତୁ | ଆମେ କେବଳ ମାନକ ସଂସ୍କରଣର କ୍ରିପ୍ଟୋ କଏନ୍ଗୁଡ଼ିକୁ ସମର୍ଥନ କରୁଛୁ, କୌଣସି ଅଜାଣା ନେଟୱର୍କ କିମ୍ବା କଏନ୍ର ସଂସ୍କରଣକୁ ନୁହେଁ। କଏନ୍ ଉପରେ ନିର୍ଭର କରି ସମ୍ପର୍କ ସତ୍ୟାପନ କରିବାକୁ ଏକ ଘଣ୍ଟା ପର୍ଯ୍ୟନ୍ତ ସମୟ ଲାଗିପାରେ। <a %(a_page)s>ଏହି ପୃଷ୍ଠାରେ</a> %(amount)s ଦାନ କରନ୍ତୁ। ଏହି ଦାନର ସମୟ ସମାପ୍ତ ହୋଇଛି। ଦୟାକରି ଏହାକୁ ବାତିଲ୍ କରନ୍ତୁ ଏବଂ ଏକ ନୂତନ ତିଆରି କରନ୍ତୁ। ଯଦି ଆପଣ ପୂର୍ବରୁ ଦେୟ ଦେଇଛନ୍ତି: ହଁ, ମୁଁ ମୋର ରସିଦ ଇମେଲ କରିଛି ଯଦି ଟ୍ରାଞ୍ଜାକ୍ସନ ସମୟରେ କ୍ରିପ୍ଟୋ ଏକ୍ସଚେଞ୍ଜ ରେଟ୍ ପରିବର୍ତ୍ତିତ ହୋଇଥାଏ, ମୂଳ ଏକ୍ସଚେଞ୍ଜ ରେଟ୍ ଦେଖାଉଥିବା ରସିଦ୍ ଅନ୍ତର୍ଭୁକ୍ତ କରିବାକୁ ନିଶ୍ଚିତ କରନ୍ତୁ। ଆମେ ଆପଣଙ୍କୁ କ୍ରିପ୍ଟୋ ବ୍ୟବହାର କରିବାରେ ଅସୁବିଧା ହେବାକୁ ଆପଣଙ୍କୁ ଆଭାର ଜଣାଉଛୁ, ଏହା ଆମକୁ ବହୁତ ସାହାଯ୍ୟ କରେ! ❌ କିଛି ଭୁଲ୍ ହୋଇଛି। ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ। <span %(span_circle)s>%(circle_number)s</span> ଆମକୁ ରସିଦ ଇମେଲ୍ କରନ୍ତୁ ଯଦି ଆପଣ କୌଣସି ସମସ୍ୟାରେ ପଡ଼ନ୍ତି, ଦୟାକରି %(email)s ଠାରେ ଆମକୁ ସମ୍ପର୍କ କରନ୍ତୁ ଏବଂ ସମ୍ଭବ ଥିବା ସବୁ ତଥ୍ୟ ଯୋଗାନ୍ତୁ (ଯଥା ସ୍କ୍ରିନସ୍ୟାଟ୍ ଇତ୍ୟାଦି)। ✅ ଆପଣଙ୍କ ଦାନ ପାଇଁ ଧନ୍ୟବାଦ! ଆନା କିଛି ଦିନ ମଧ୍ୟରେ ଆପଣଙ୍କର ସଦସ୍ୟତାକୁ ମାନୁଆଲି ସକ୍ରିୟ କରିବେ। ଆପଣଙ୍କର ବ୍ୟକ୍ତିଗତ ସତ୍ୟାପନ ଠିକଣାକୁ ଏକ ରସିଦ ବା ସ୍କ୍ରିନସ୍ୟାଟ୍ ପଠାନ୍ତୁ: ଯେତେବେଳେ ଆପଣ ଆପଣଙ୍କର ରସିଦ ଇମେଲ୍ କରିଛନ୍ତି, ଏହି ବଟନ୍ ଦବାନ୍ତୁ, ଯାହା ଫଳରେ ଆନା ମାନୁଆଲି ଏହାକୁ ସମୀକ୍ଷା କରିବେ (ଏହାକୁ କିଛି ଦିନ ଲାଗିପାରେ): ଆପଣଙ୍କର ବ୍ୟକ୍ତିଗତ ସତ୍ୟାପନ ଠିକଣାକୁ ରସିଦ କିମ୍ବା ସ୍କ୍ରିନସ୍ନାପ୍ ପଠାନ୍ତୁ। ଆପଣଙ୍କର PayPal ଦାନ ପାଇଁ ଏହି ଇମେଲ୍ ଠିକଣା ବ୍ୟବହାର କରନ୍ତୁ ନାହିଁ। ବାତିଲ୍ କରନ୍ତୁ | ହଁ, ଦୟାକରି ବାତିଲ୍ କରନ୍ତୁ | ଆପଣ ନିଶ୍ଚିତ କି ଆପଣ ବାତିଲ କରିବାକୁ ଚାହୁଁଛନ୍ତି? ଯଦି ଆପଣ ପୂର୍ବରୁ ଦେୟ ଦେଇଛନ୍ତି ତେବେ ବାତିଲ୍ କରନ୍ତୁ ନାହିଁ | ❌ କିଛି ଭୁଲ ହୋଇ ଗଲା। ଦୟାକରି ପୃଷ୍ଠା ପୁନ o ଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ | ନୂଆ ଦାନ କରନ୍ତୁ ✅ ତୁମର ଦାନ ବାତିଲ ହୋଇଛି | ତାରିଖ: %(date)s ପରିଚାୟକ: %(id)s ପୁନଃ ବ୍ୟବସ୍ଥା କରନ୍ତୁ ସ୍ଥିତି: <span %(span_label)s>%(label)s</span> ମୋଟ: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ମାସ ପାଇଁ %(duration)s ମାସ, ଯାହାରେ %(discounts)s%% ରିଆୟତି ସମ୍ମିଳିତ)</span> ମୋଟ: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ମାସ ପାଇଁ %(duration)s ମାସ)</span> 1. ଆପଣଙ୍କ ଇମେଲ୍ ପ୍ରବେଶ କରନ୍ତୁ। 2. ଆପଣଙ୍କର ଦେୟ ପ୍ରକାର ଚୟନ କରନ୍ତୁ। 3. ପୁନଃ ଆପଣଙ୍କର ଦେୟ ପ୍ରକାର ଚୟନ କରନ୍ତୁ। 4. “Self-hosted” ୱାଲେଟ୍ ଚୟନ କରନ୍ତୁ। 5. “I confirm ownership” କ୍ଲିକ୍ କରନ୍ତୁ। 6. ଆପଣ ଏକ ଇମେଲ୍ ରସିଦ ପାଇବା ଉଚିତ। ଦୟାକରି ଏହାକୁ ଆମକୁ ପଠାନ୍ତୁ, ଏବଂ ଆମେ ଆପଣଙ୍କ ଦାନକୁ ସମ୍ଭବ ଶୀଘ୍ର ସମୟରେ ନିଶ୍ଚିତ କରିବା। (ଆପଣ ବାତିଲ କରି ଏକ ନୂତନ ଦାନ ସୃଷ୍ଟି କରିବାକୁ ଚାହିଁପାରନ୍ତି) ପେମେଣ୍ଟ ନିର୍ଦ୍ଦେଶନାମା ବର୍ତ୍ତମାନ ଅପ୍ରସଙ୍ଗିକ ଅଟେ। ଯଦି ଆପଣ ଆଉ ଏକ ଦାନ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ଉପରେ ଥିବା “ପୁନଃକ୍ରମବଦ୍ଧ” ବଟନ୍ ବ୍ୟବହାର କରନ୍ତୁ। ଆପଣ ପୂର୍ବରୁ ଦେୟ ଦେଇସାରିଛନ୍ତି | ଯଦି ଆପଣ ଯେକ way ଣସି ପ୍ରକାରେ ଦେୟ ନିର୍ଦ୍ଦେଶାବଳୀ ସମୀକ୍ଷା କରିବାକୁ ଚାହାଁନ୍ତି, ଏଠାରେ କ୍ଲିକ୍ କରନ୍ତୁ: ପୁରୁଣା ଦେୟ ନିର୍ଦ୍ଦେଶନାମା ଦେଖାନ୍ତୁ ଯଦି ଦାନ ପୃଷ୍ଠା ଅବରୋଧିତ ହୁଏ, ତେବେ ଅନ୍ୟ ଇଣ୍ଟରନେଟ ସଂଯୋଗ (ଉଦାହରଣ ସ୍ୱରୂପ VPN କିମ୍ବା ଫୋନ୍ ଇଣ୍ଟରନେଟ) ଚେଷ୍ଟା କରନ୍ତୁ। ଦୁଃଖର ସହିତ, Alipay ପୃଷ୍ଠାଟି ପ୍ରାୟତଃ କେବଳ <strong>ମେନଲ୍ୟାଣ୍ଡ ଚାଇନା</strong>ରୁ ପ୍ରବେଶଯୋଗ୍ୟ। ଆପଣଙ୍କୁ ଅସ୍ଥାୟୀ ଭାବେ ଆପଣଙ୍କର VPN କୁ ଅକ୍ରିୟ କରିବାକୁ ପଡ଼ିପାରେ, କିମ୍ବା ମେନଲ୍ୟାଣ୍ଡ ଚାଇନାକୁ VPN ବ୍ୟବହାର କରିବାକୁ ପଡ଼ିପାରେ (କିମ୍ବା କେବେ କେବେ ହଂକଙ୍ଗ ମଧ୍ୟ କାମ କରେ)। <span %(span_circle)s>1</span>Alipay ରେ ଦାନ କରନ୍ତୁ <a %(a_account)s>ଏହି Alipay ଖାତା</a> ବ୍ୟବହାର କରି %(total)sର ମୋଟ ରାଶି ଦାନ କରନ୍ତୁ Alipay ନିର୍ଦ୍ଦେଶାବଳୀ <span %(span_circle)s>1</span>ଆମର କୃପ୍ଟୋ ଆକାଉଣ୍ଟଗୁଡିକରେ ହସ୍ତାନ୍ତର କରନ୍ତୁ ଏହି ଠିକଣାଗୁଡିକର ମଧ୍ୟରୁ ଗୋଟିଏକୁ ମୋଟ ରାଶି %(total)s ଦାନ କରନ୍ତୁ: କ୍ରିପ୍ଟୋ ନିର୍ଦ୍ଦେଶନାମା ବିଟକୋଇନ୍ (BTC) କିଣିବା ପାଇଁ ନିର୍ଦ୍ଦେଶାବଳୀକୁ ଅନୁସରଣ କରନ୍ତୁ। ଆପଣକୁ କେବଳ ଆପଣ ଦାନ କରିବାକୁ ଚାହୁଁଥିବା ରାଶି କିଣିବାକୁ ପଡ଼ିବ, %(total)s। ଆମର ବିଟକୋଇନ୍ (BTC) ଠିକଣାକୁ ଗ୍ରାହକ ଭାବରେ ପ୍ରବେଶ କରନ୍ତୁ, ଏବଂ ଆପଣଙ୍କର ଦାନର %(total)s ପଠାଇବା ପାଇଁ ନିର୍ଦ୍ଦେଶନାମା ଅନୁସରଣ କରନ୍ତୁ: <span %(span_circle)s>1</span>Pix ରେ ଦାନ କରନ୍ତୁ <a %(a_account)s>ଏହି ପିକ୍ସ ଖାତା</a> ବ୍ୟବହାର କରି %(total)s ମୋଟ ରାଶି ଦାନ କରନ୍ତୁ ପିକ୍ସ ନିର୍ଦ୍ଦେଶନାମା <span %(span_circle)s>1</span>WeChat ରେ ଦାନ କରନ୍ତୁ <a %(a_account)s>ଏହି WeChat ଖାତା</a> ବ୍ୟବହାର କରି %(total)s ମୋଟ ରାଶି ଦାନ କରନ୍ତୁ WeChat ନିର୍ଦ୍ଦେଶାବଳୀ ନିମ୍ନଲିଖିତ “କ୍ରେଡିଟ୍ କାର୍ଡ୍‌ରୁ ବିଟକଏନ୍” ଏକ୍ସପ୍ରେସ୍ ସେବାଗୁଡିକ ବ୍ୟବହାର କରନ୍ତୁ, ଯାହା କେବଳ କିଛି ମିନିଟ୍ ନେଇଥାଏ: BTC / Bitcoin ଠିକଣା (ବାହାର ଓଲେଟ୍): BTC / Bitcoin ପରିମାଣ: ଫର୍ମରେ ନିମ୍ନଲିଖିତ ବିବରଣୀଗୁଡ଼ିକ ପୂରଣ କରନ୍ତୁ: ଯଦି ଏହି ସମସ୍ତ ସୂଚନା ଅପରିବର୍ତ୍ତିତ ରହିଛି, ଦୟାକରି ଆମକୁ ଇମେଲ୍ କରନ୍ତୁ ଏବଂ ଆମକୁ ଜଣାନ୍ତୁ। ଦୟାକରି ଏହି <span %(underline)s>ନିଖୁଦ୍ଧ ପରିମାଣ</span> ବ୍ୟବହାର କରନ୍ତୁ। ଆପଣଙ୍କର ମୋଟ ଖର୍ଚ୍ଚ କ୍ରେଡିଟ୍ କାର୍ଡ ଫିସ୍ ଦ୍ୱାରା ଅଧିକ ହୋଇପାରେ। ଦୁର୍ଭାଗ୍ୟବଶତଃ ଛୋଟ ପରିମାଣ ପାଇଁ ଏହା ଆମର ଡିସ୍କାଉଣ୍ଟ ଠାରୁ ଅଧିକ ହୋଇପାରେ। (ନ୍ୟୁନତମ: %(minimum)s, ପ୍ରଥମ ଲେନଦେନ ପାଇଁ କୌଣସି ପରିଚୟ ପ୍ରମାଣ ପତ୍ର ଆବଶ୍ୟକ ନୁହେଁ) (ନ୍ୟୁନତମ: %(minimum)s) (ନ୍ୟୁନତମ: %(minimum)s) (ନ୍ୟୁନତମ: %(minimum)s, ପ୍ରଥମ ଲେନଦେନ ପାଇଁ କୌଣସି ପରିଚୟ ପ୍ରମାଣ ପତ୍ର ଆବଶ୍ୟକ ନୁହେଁ) (ନ୍ୟୁନତମ: %(minimum)s) (ନ୍ୟୁନତମ: %(minimum)s ଦେଶ ଅନୁସାରେ, ପ୍ରଥମ ଲେନଦେନ ପାଇଁ କୌଣସି ପରିଚୟ ପ୍ରମାଣ ପତ୍ର ଆବଶ୍ୟକ ନୁହେଁ) PYUSD କୋଇନ୍ (PayPal USD) କିଣିବା ପାଇଁ ନିର୍ଦ୍ଦେଶାବଳୀ ଅନୁସରଣ କରନ୍ତୁ। ଦାନ କରୁଥିବା ପରିମାଣ (%(amount)s) ଠାରୁ ଅଧିକ କିଛି କିଣନ୍ତୁ (ଆମେ %(more)s ଅଧିକ ସୁପାରିଶ କରୁଛୁ), ଯାହା ଲେନଦେନ ଶୁଳ୍କକୁ ଆବରଣ କରିବ। ଅବଶିଷ୍ଟ ରାଶି ଆପଣଙ୍କ ପାଖରେ ରହିବ। ଆପଣଙ୍କ PayPal ଆପ୍ ବା ୱେବସାଇଟରେ “PYUSD” ପୃଷ୍ଠାକୁ ଯାଆନ୍ତୁ। “Transfer” ବଟନ୍ %(icon)s ଦବାନ୍ତୁ, ଏବଂ ପରେ “Send” କରନ୍ତୁ। ସ୍ଥିତି ଅଦ୍ୟତନ କରନ୍ତୁ ଟାଇମର୍ ପୁନଃସେଟ୍ କରିବାକୁ, କେବଳ ଏକ ନୂତନ ଦାନ ସୃଷ୍ଟି କରନ୍ତୁ। ଦୟାକରି ନିମ୍ନଲିଖିତ BTC ପରିମାଣ ବ୍ୟବହାର କରନ୍ତୁ, <em>ୟୁରୋ କିମ୍ବା ଡଲାର୍ସ</em> ନୁହେଁ, ନହେଲେ ଆମେ ଠିକ ପରିମାଣ ପାଇବୁ ନାହିଁ ଏବଂ ଆପଣଙ୍କର ସଦସ୍ୟତା ସ୍ୱୟଂଚାଳିତ ଭାବରେ ନିଶ୍ଚିତ କରିପାରିବୁ ନାହିଁ। ରେଭୋଲୁଟ୍‌ରେ ବିଟକଏନ୍ (BTC) କିଣନ୍ତୁ ଲେନଦେନ ଶୁଳ୍କ ଆବରଣ କରିବା ପାଇଁ ଆପଣ ଯେତେକି ଦାନ କରୁଛନ୍ତି (%(amount)s), ତାହାଠାରୁ ଅଧିକ (ଆମେ %(more)s ଅଧିକ ସୁପାରିଶ କରୁଛୁ) କିଣନ୍ତୁ। ଯାହା ଅବଶିଷ୍ଟ ରହିବ, ତାହା ଆପଣ ରଖିପାରିବେ। ବିଟକଏନ୍ (BTC) କିଣିବା ପାଇଁ ରେଭୋଲୁଟ୍‌ରେ “କ୍ରିପ୍ଟୋ” ପୃଷ୍ଠାକୁ ଯାଆନ୍ତୁ। ଆମ ପତାକୁ ବିଟକଏନ୍ ପଠାନ୍ତୁ ଛୋଟ ଦାନ (25 ଡଲାର୍‌ରୁ କମ୍) ପାଇଁ, ଆପଣଙ୍କୁ Rush କିମ୍ବା Priority ବ୍ୟବହାର କରିବାକୁ ପଡ଼ିପାରେ। “ବିଟକଏନ୍ ପଠାନ୍ତୁ” ବଟନ୍‌କୁ କ୍ଲିକ୍ କରି “ଉତ୍ତୋଳନ” କରନ୍ତୁ। %(icon)s ଆଇକନ୍‌କୁ ଦବାଇ ଇଉରୋରୁ BTC କୁ ସ୍ୱିଚ୍ କରନ୍ତୁ। ନିମ୍ନରେ BTC ପରିମାଣ ପ୍ରବେଶ କରନ୍ତୁ ଏବଂ “ପଠାନ୍ତୁ” ବଟନ୍‌କୁ କ୍ଲିକ୍ କରନ୍ତୁ। ଯଦି ଆପଣ ଅଟକିଯାନ୍ତି, <a %(help_video)s>ଏହି ଭିଡିଓ</a> ଦେଖନ୍ତୁ। ସ୍ଥିତି: 1 2 ପଦକ୍ଷେପ ଗାଇଡ୍ ନିମ୍ନରେ ପଦକ୍ଷେପ ଗାଇଡ୍ ଦେଖନ୍ତୁ। ନହେଲେ ଆପଣ ଏହି ଖାତାରୁ ବନ୍ଦ ହୋଇଯାଇପାରନ୍ତି! ଯଦି ଆପଣ ଏପର୍ଯ୍ୟନ୍ତ ଲେଖିନାହାନ୍ତି, ଲଗଇନ୍ କରିବା ପାଇଁ ଆପଣଙ୍କର ଗୁପ୍ତ କୀ ଲେଖନ୍ତୁ: ତୁମର ଦାନ ପାଇଁ ଧନ୍ୟବାଦ! ଅବଶିଷ୍ଟ ସମୟ: ଦାନ %(amount)s କୁ %(account)s କୁ ହସ୍ତାନ୍ତର କରନ୍ତୁ ସତ୍ୟାପନ ପାଇଁ ଅପେକ୍ଷା କରୁଛି (ଯାଞ୍ଚ କରିବା ପାଇଁ ପୃଷ୍ଠାକୁ ରିଫ୍ରେସ୍ କରନ୍ତୁ)… ସ୍ଥାନାନ୍ତର ପାଇଁ ଅପେକ୍ଷା କରୁଛି (ଯାଞ୍ଚ କରିବା ପାଇଁ ପୃଷ୍ଠାକୁ ରିଫ୍ରେସ୍ କରନ୍ତୁ)… ପୂର୍ବରୁ ଗତ 24 ଘଣ୍ଟାରେ ଫାସ୍ଟ ଡାଉନଲୋଡ୍ ଦୈନିକ ସୀମାରେ ଗଣନା ହେଉଛି। ଫାସ୍ଟ ପାର୍ଟନର ସର୍ଭରରୁ ଡାଉନଲୋଡ୍ ହୋଇଥିବା ଫାଇଲଗୁଡ଼ିକ %(icon)s ଦ୍ୱାରା ଚିହ୍ନିତ। ଶେଷ ୧୮ ଘଣ୍ଟା ଏପର୍ଯ୍ୟନ୍ତ କୌଣସି ଫାଇଲ ଡାଉନଲୋଡ୍ ହୋଇନାହିଁ। ଡାଉନଲୋଡ୍ ହୋଇଥିବା ଫାଇଲଗୁଡ଼ିକ ସାର୍ବଜନୀନ ଭାବେ ଦେଖାଯାଏ ନାହିଁ। ସମସ୍ତ ସମୟ UTC ରେ ଅଛି। ଡାଉନଲୋଡ୍ ହୋଇଥିବା ଫାଇଲଗୁଡ଼ିକ ଯଦି ଆପଣ ଏକ ଫାଇଲକୁ ଦୁଇଟି ଭିନ୍ନ ଗତିରେ ଡାଉନଲୋଡ୍ କରିଛନ୍ତି, ତେବେ ଏହା ଦୁଇଥର ଦେଖାଯିବ। ଅଧିକ ଚିନ୍ତା କରନ୍ତୁ ନାହିଁ, ଆମେ ଯାହାକୁ ଲିଙ୍କ୍ କରୁଥିବା ୱେବସାଇଟଗୁଡ଼ିକରୁ ଅନେକ ଲୋକ ଡାଉନଲୋଡ୍ କରୁଛନ୍ତି, ଏବଂ ସମସ୍ୟାରେ ପଡ଼ିବା ଅତ୍ୟନ୍ତ ଦୁର୍ଲଭ। ତଥାପି, ସୁରକ୍ଷିତ ରହିବା ପାଇଁ ଆମେ ଏକ ଭୁକ୍ତାନ ହୋଇଥିବା VPN ବ୍ୟବହାର କରିବାକୁ, କିମ୍ବା <a %(a_tor)s>Tor</a> (ମାଗଣା) ବ୍ୟବହାର କରିବାକୁ ପରାମର୍ଶ ଦେଉଛୁ। ମୁଁ ଜର୍ଜ ଓରୱେଲଙ୍କ 1984 ଡାଉନଲୋଡ୍ କଲି, ପୋଲିସ୍ ମୋ ଘରକୁ ଆସିବେ କି? ଆପଣ ଅନ୍ନା! ଅନ୍ନା କିଏ? ଆମ ପାଖରେ ଏକ ସ୍ଥିର JSON API ଅଛି ସଦସ୍ୟମାନଙ୍କ ପାଇଁ, ଏକ ତେଜ ଡାଉନଲୋଡ୍ URL ପାଇବା ପାଇଁ: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON ମଧ୍ୟରେ ଡକ୍ୟୁମେଣ୍ଟେସନ)। ଅନ୍ୟ ଉପଯୋଗ ମାମଲା ପାଇଁ, ଯେପରିକି ଆମ ସମସ୍ତ ଫାଇଲ୍ ମାନଙ୍କୁ ଆବର୍ତ୍ତନ କରିବା, କଷ୍ଟମ୍ ସର୍ଚ୍ଚ ନିର୍ମାଣ କରିବା, ଇତ୍ୟାଦି, ଆମେ <a %(a_generate)s>ଜେନେରେଟ୍ କରିବାକୁ</a> କିମ୍ବା <a %(a_download)s>ଡାଉନଲୋଡ୍ କରିବାକୁ</a> ଆମ ElasticSearch ଏବଂ MariaDB ଡାଟାବେସ୍‌ଗୁଡ଼ିକୁ ପରାମର୍ଶ ଦେଉଛୁ। କଚା ତଥ୍ୟକୁ ମାନୁଆଲି <a %(a_explore)s>JSON ଫାଇଲ୍‌ଗୁଡ଼ିକ ମାଧ୍ୟମରେ</a> ଅନୁସନ୍ଧାନ କରାଯାଇପାରେ। ଆମର କাঁচା ଟୋରେଣ୍ଟ ତାଲିକାକୁ <a %(a_torrents)s>JSON</a> ଭାବରେ ମଧ୍ୟ ଡାଉନଲୋଡ୍ କରାଯାଇପାରେ। ଆପଣଙ୍କ ପାଖରେ ଏକ API ଅଛି କି? ଆମେ ଏଠାରେ କୌଣସି କପିରାଇଟ୍ ସମ୍ପର୍କିତ ସାମଗ୍ରୀ ହୋଷ୍ଟ କରୁନାହିଁ। ଆମେ ଏକ ସର୍ଚ୍ଚ ଇଞ୍ଜିନ୍ ଭାବେ କାମ କରୁଛୁ, ଏବଂ ଏହିପରି ମାତ୍ର ମେଟାଡାଟାକୁ ଇଣ୍ଡେକ୍ସ କରୁଛୁ ଯାହା ପୂର୍ବରୁ ସାର୍ବଜନିକ ଭାବେ ଉପଲବ୍ଧ ଅଛି। ଏହି ବାହ୍ୟ ସ୍ତ୍ରୋତରୁ ଡାଉନଲୋଡ୍ କରିବା ସମୟରେ, ଆପଣଙ୍କ ଅଞ୍ଚଳରେ କୌଣସି ନିୟମ ଅନୁସାରେ କ’ଣ ଅନୁମତି ଅଛି ତାହା ଯାଞ୍ଚ କରିବାକୁ ଆମେ ପରାମର୍ଶ ଦେବୁ। ଆମେ ଅନ୍ୟମାନଙ୍କ ଦ୍ୱାରା ହୋଷ୍ଟ କରାଯାଇଥିବା ବିଷୟବସ୍ତୁ ପାଇଁ ଦାୟୀ ନୁହଁ। ଯଦି ଆପଣ ଏଠାରେ ଦେଖିଥିବା ବିଷୟବସ୍ତୁ ପାଇଁ ଅଭିଯୋଗ ରଖିଛନ୍ତି, ତାହାହେଲେ ମୂଳ ୱେବସାଇଟ୍ ସହିତ ଯୋଗାଯୋଗ କରିବା ଆପଣଙ୍କର ସର୍ବୋତ୍ତମ ପରିକଳ୍ପନା ହେବ। ଆମେ ନିୟମିତ ଭାବେ ସେମାନଙ୍କର ପରିବର୍ତ୍ତନଗୁଡ଼ିକୁ ଆମ ଡାଟାବେସରେ ଆଣି ନେଉଛୁ। ଯଦି ଆପଣ ମାନିବେ ଯେ ଆପଣଙ୍କର ଏକ ବୈଧ DMCA ଅଭିଯୋଗ ଅଛି ଯାହାକୁ ଆମେ ପ୍ରତିସାଦ ଦେବା ଉଚିତ୍, ଦୟାକରି <a %(a_copyright)s>DMCA / କପିରାଇଟ୍ ଅଭିଯୋଗ ଫର୍ମ</a> ପୂରଣ କରନ୍ତୁ। ଆମେ ଆପଣଙ୍କର ଅଭିଯୋଗକୁ ଗୁରୁତ୍ୱର ସହିତ ନେବୁ, ଏବଂ ସମ୍ଭବତଃ ଶୀଘ୍ର ଆପଣଙ୍କୁ ପ୍ରତିସାଦ ଦେବୁ। ମୁଁ କପିରାଇଟ୍ ଉଲ୍ଲଂଘନ କିପରି ରିପୋର୍ଟ କରିବି? ଏହା ହେଉଛି କିଛି ପୁସ୍ତକ ଯାହା ଛାୟା ଗ୍ରନ୍ଥାଗାର ଏବଂ ଡିଜିଟାଲ ସଂରକ୍ଷଣର ଜଗତ ପାଇଁ ବିଶେଷ ମହତ୍ତ୍ୱ ରଖେ: ଆପଣଙ୍କ ପ୍ରିୟ ପୁସ୍ତକଗୁଡ଼ିକ କ’ଣ? ଆମେ ସମସ୍ତଙ୍କୁ ଏହା ମଧ୍ୟ ସ୍ମରଣ କରାଇବାକୁ ଚାହୁଁଛୁ ଯେ ଆମର ସମସ୍ତ କୋଡ଼ ଏବଂ ତଥ୍ୟ ସଂପୂର୍ଣ୍ଣ ଭାବରେ ଖୋଲା ମୂଲ୍ୟସ୍ତରରେ ଉପଲବ୍ଧ ଅଛି। ଏହା ଆମର ପରି ପ୍ରକଳ୍ପଗୁଡ଼ିକ ପାଇଁ ବିଶିଷ୍ଟ — ଆମେ ଏକ ଏପରି ଅନ୍ୟ ପ୍ରକଳ୍ପ ବିଷୟରେ ଜାଣିନାହିଁ ଯାହା ଏତେ ବିଶାଳ ସଂଗ୍ରହ ସହିତ ସଂପୂର୍ଣ୍ଣ ଖୋଲା ମୂଲ୍ୟସ୍ତରରେ ଅଛି। ଯେଉଁମାନେ ଭାବନ୍ତି ଯେ ଆମେ ଆମର ପ୍ରକଳ୍ପକୁ ଖରାପ ଭାବରେ ପରିଚାଳନା କରୁଛୁ ସେମାନଙ୍କୁ ଆମର କୋଡ଼ ଏବଂ ତଥ୍ୟ ନେଇ ନିଜର ଛାୟା ଗ୍ରନ୍ଥାଗାର ସ୍ଥାପନ କରିବାକୁ ଆମେ ଅତ୍ୟନ୍ତ ସ୍ୱାଗତ କରୁଛୁ! ଆମେ ଏହା ଦ୍ୱେଷରୁ କିଛି କହୁନାହିଁ — ଆମେ ସତରେ ଭାବୁଛୁ ଯେ ଏହା ଅତ୍ୟନ୍ତ ଦାରୁଣ ହେବ କାରଣ ଏହା ସମସ୍ତଙ୍କ ପାଇଁ ମାନକୁ ବୃଦ୍ଧି କରିବ ଏବଂ ମାନବତାର ଉତ୍ତାରାଧିକାରକୁ ଭଲ ଭାବରେ ସଂରକ୍ଷଣ କରିବ। ମୁଁ ଆପଣଙ୍କର ଏହି ପ୍ରକଳ୍ପ କିପରି ଚାଲୁଛି ତାହା ଘୃଣା କରେ! ମନୋଷ୍ଟମାନଙ୍କୁ <a %(a_mirrors)s>ମିରର୍ସ</a> ସେଟଅପ୍ କରିବାକୁ ଆମେ ଆଗ୍ରହୀ ହେବୁ, ଏବଂ ଆମେ ଏହାକୁ ଆର୍ଥିକ ସମର୍ଥନ ଦେବୁ। ମୁଁ କିପରି ସାହାଯ୍ୟ କରିପାରିବି? ଆମେ ନିଶ୍ଚିତ ଭାବେ କରିଥାଉ। ମେଟାଡାଟା ସଂଗ୍ରହ କରିବା ପାଇଁ ଆମର ପ୍ରେରଣା ହେଉଛି ଆରନ୍ ସ୍ୱାର୍ଟଜ୍ଙ୍କ “ପ୍ରତ୍ୟେକ ପ୍ରକାଶିତ ପୁସ୍ତକ ପାଇଁ ଗୋଟିଏ ୱେବ୍ ପୃଷ୍ଠା” ଲକ୍ଷ୍ୟ, ଯାହା ପାଇଁ ସେ <a %(a_openlib)s>Open Library</a> ସୃଷ୍ଟି କରିଥିଲେ। ସେହି ପ୍ରକଳ୍ପ ଭଲ ହେଉଛି, କିନ୍ତୁ ଆମର ବିଶିଷ୍ଟ ସ୍ଥିତି ଆମକୁ ମେଟାଡାଟା ପାଇବାକୁ ଅନୁମତି ଦେଇଥାଏ ଯାହା ସେମାନେ ପାରିବେ ନାହିଁ। ଆଉ ଏକ ପ୍ରେରଣା ହେଉଛି ଆମର ଜାଣିବାକୁ ଇଚ୍ଛା <a %(a_blog)s>ବିଶ୍ୱରେ କେତେ ପୁସ୍ତକ ଅଛି</a>, ଯାହା ଆମେ ଗଣନା କରିପାରିବା ଯେ ଆମେ ଏଯାଏଁ କେତେ ପୁସ୍ତକ ରକ୍ଷା କରିବାକୁ ଥିବା ଅଛି। ଆପଣ ମେଟାଡାଟା ସଂଗ୍ରହ କରନ୍ତି କି? ମନେ ରଖନ୍ତୁ ଯେ mhut.org ନିର୍ଦ୍ଦିଷ୍ଟ IP ରେଞ୍ଜଗୁଡ଼ିକୁ ଅବରୋଧ କରେ, ତେଣୁ ଏକ VPN ଆବଶ୍ୟକ ହୋଇପାରେ। <strong>ଆଣ୍ଡ୍ରଏଡ୍:</strong> ଉପରେ ଡାହାଣ ପଟରେ ଥିବା ତିନୋଟି ଡଟ୍ ମେନୁକୁ କ୍ଲିକ୍ କରନ୍ତୁ, ଏବଂ "Add to Home Screen" ଚୟନ କରନ୍ତୁ। <strong>iOS:</strong> ତଳେ ଥିବା "Share" ବଟନ୍‌କୁ କ୍ଲିକ୍ କରନ୍ତୁ, ଏବଂ "Add to Home Screen" ଚୟନ କରନ୍ତୁ। ଆମ ପାଖରେ ଏକ ଆଧିକାରିକ ମୋବାଇଲ୍ ଆପ୍ ନାହିଁ, କିନ୍ତୁ ଆପଣ ଏହି ୱେବସାଇଟ୍‌କୁ ଏକ ଆପ୍ ଭାବରେ ସଂସ୍ଥାପନ କରିପାରିବେ। ଆପଣଙ୍କ ପାଖରେ ଏକ ମୋବାଇଲ୍ ଆପ୍ ଅଛି କି? ଦୟାକରି ସେଗୁଡ଼ିକୁ <a %(a_archive)s>Internet Archive</a> କୁ ପଠାନ୍ତୁ। ସେମାନେ ସଠିକ ଭାବରେ ସେଗୁଡ଼ିକୁ ସଂରକ୍ଷଣ କରିବେ। ମୁଁ କିପରି ପୁସ୍ତକ କିମ୍ବା ଅନ୍ୟ ଭୌତିକ ସାମଗ୍ରୀ ଦାନ କରିପାରିବି? ମୁଁ କିପରି ପୁସ୍ତକ ଅନୁରୋଧ କରିପାରିବି? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — ନିୟମିତ ଅଦ୍ୟତନ <a %(a_software)s>ଆନାଙ୍କ ସଫ୍ଟୱେର</a> — ଆମର ଖୋଲା ଉତ୍ସ କୋଡ଼ <a %(a_datasets)s>Datasets</a> — ତଥ୍ୟ ସମ୍ବନ୍ଧରେ <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — ବିକଳ୍ପ ଡୋମେନଗୁଡ଼ିକ Anna’s Archive ବିଷୟରେ ଅଧିକ ସମ୍ପଦ ଅଛି କି? <a %(a_translate)s>ଆନାଙ୍କ ସଫ୍ଟୱେରରେ ଅନୁବାଦ କରନ୍ତୁ</a> — ଆମର ଅନୁବାଦ ପ୍ରଣାଳୀ <a %(a_wikipedia)s>ଉଇକିପିଡ଼ିଆ</a> — ଆମ ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ (ଦୟାକରି ଏହି ପୃଷ୍ଠାକୁ ଅଦ୍ୟତନ ରଖିବାରେ ସାହାଯ୍ୟ କରନ୍ତୁ, କିମ୍ବା ଆପଣଙ୍କ ନିଜ ଭାଷା ପାଇଁ ଏକ ତିଆରି କରନ୍ତୁ!) ଆପଣ ଆପଣଙ୍କ ପସନ୍ଦର ସେଟିଂଗୁଡ଼ିକ ଚୟନ କରନ୍ତୁ, ସର୍ଚ୍ଚ ବକ୍ସକୁ ଖାଲି ରଖନ୍ତୁ, "ସର୍ଚ୍ଚ" କ୍ଲିକ୍ କରନ୍ତୁ, ଏବଂ ତାପରେ ପୃଷ୍ଠାଟିକୁ ଆପଣଙ୍କ ବ୍ରାଉଜରର ବୁକମାର୍କ ବୈଶିଷ୍ଟ୍ୟ ବ୍ୟବହାର କରି ବୁକମାର୍କ କରନ୍ତୁ। ମୁଁ କିପରି ମୋର ସର୍ଚ୍ଚ ସେଟିଂସ୍ ସଂରକ୍ଷଣ କରିପାରିବି? ଆମେ ସୁରକ୍ଷା ଗବେଷକମାନଙ୍କୁ ଆମ ସିଷ୍ଟମରେ ଅସୁରକ୍ଷା ବିଷୟଗୁଡ଼ିକ ଖୋଜିବାକୁ ସ୍ୱାଗତ କରୁଛୁ। ଆମେ ଜିମ୍ମେଦାର ଖୁଲାସାର ବଡ଼ ସମର୍ଥକ। ଆମ ସହିତ <a %(a_contact)s>ଯୋଗାଯୋଗ କରନ୍ତୁ</a>। ଆମେ ବର୍ତ୍ତମାନ ବଗ୍ ବାଉଣ୍ଟି ପୁରସ୍କାର ଦେଇପାରୁନାହୁଁ, କିନ୍ତୁ ଯେଉଁ ଅସୁରକ୍ଷା ବିଷୟଗୁଡ଼ିକର <a %(a_link)s>ଆମର ଗୋପନୀୟତାକୁ ବିପଦରେ ପକାଇବାର ସମ୍ଭାବନା ଅଛି</a>, ସେଗୁଡ଼ିକ ପାଇଁ ଆମେ $10k-50k ରେଞ୍ଜରେ ପୁରସ୍କାର ଦେଇଥାଉ। ଭବିଷ୍ୟତରେ ବଗ୍ ବାଉଣ୍ଟି ପାଇଁ ଅଧିକ ବ୍ୟାପକ କ୍ଷେତ୍ର ଦେବାକୁ ଆମେ ଚାହୁଁଛୁ! ଦୟାକରି ଧ୍ୟାନ ଦିଅନ୍ତୁ ଯେ ସୋସିଆଲ୍ ଇଞ୍ଜିନିୟରିଂ ଆକ୍ରମଣଗୁଡ଼ିକ କ୍ଷେତ୍ରରୁ ବାହାରେ ଅଛି। ଯଦି ଆପଣ ଆକ୍ରମଣାତ୍ମକ ସୁରକ୍ଷାରେ ରୁଚି ରଖନ୍ତି, ଏବଂ ବିଶ୍ୱର ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ଆର୍କାଇଭ୍ କରିବାକୁ ସାହାଯ୍ୟ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ନିଶ୍ଚିତ ଭାବେ ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଆପଣ ସାହାଯ୍ୟ କରିପାରିବା ନେଇ ଅନେକ ପଦ୍ଧତି ଅଛି। ଆପଣଙ୍କ ପାଖରେ ଜିମ୍ମେଦାର ଖୁଲାସା କାର୍ଯ୍ୟକ୍ରମ ଅଛି କି? ଆମେ ପୃଥିବୀର ସମସ୍ତ ଲୋକଙ୍କୁ ଉଚ୍ଚ-ଗତି ଡାଉନଲୋଡ୍ ଦେବା ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ ସାଧନ ନାହିଁ, ଯେପରି ଆମେ ଚାହୁଁଛୁ। ଯଦି ଏକ ଧନୀ ଉପକାରକ ଆମକୁ ଏହା ପ୍ରଦାନ କରିବାକୁ ଆଗକୁ ଆସନ୍ତି, ତାହା ଅଦ୍ଭୁତ ହେବ, କିନ୍ତୁ ସେଇଯାଏଁ, ଆମେ ଆମର ସର୍ବୋତ୍ତମ ଚେଷ୍ଟା କରୁଛୁ। ଆମେ ଏକ ନନ-ପ୍ରଫିଟ ପ୍ରକଳ୍ପ ଯାହା ଦାନର ମାଧ୍ୟମରେ ମାତ୍ର ଆପଣାକୁ ଚାଲୁ ରଖିପାରେ। ଏହି କାରଣରୁ ଆମେ ଦୁଇଟି ପ୍ରଣାଳୀକୁ ନିଷ୍ପାଦନ କରିଛୁ ମାଗଣା ଡାଉନଲୋଡ୍‌ଗୁଡ଼ିକ ପାଇଁ, ଆମର ସହଯୋଗୀମାନଙ୍କ ସହିତ: ଶେୟାର୍ କରାଯାଇଥିବା ସର୍ଭରଗୁଡ଼ିକ ଯାହା ଧୀର ଡାଉନଲୋଡ୍ ସହିତ ଅଛି, ଏବଂ ଥୋଡ଼ା ତେଜ ସର୍ଭରଗୁଡ଼ିକ ଯାହା ଏକ ଅପେକ୍ଷା ସୂଚୀ ସହିତ ଅଛି (ଏକେ ସମୟରେ ଅଧିକ ସଂଖ୍ୟକ ଲୋକଙ୍କ ଡାଉନଲୋଡ୍ କରିବାକୁ କମାଇବା ପାଇଁ)। ଆମେ ମଧ୍ୟ <a %(a_verification)s>ବ୍ରାଉଜର ସତ୍ୟାପନ</a> ରଖିଛୁ ଆମ ଧୀର ଡାଉନଲୋଡ୍‌ଗୁଡ଼ିକ ପାଇଁ, କାରଣ ନହେଲେ ବଟ୍ ଏବଂ ସ୍କ୍ରାପର୍‌ମାନେ ସେଗୁଡ଼ିକର ଦୁରୁପଯୋଗ କରିବେ, ଯାହା ବୈଧ ଉପଯୋଗକର୍ତ୍ତାମାନଙ୍କ ପାଇଁ ଜିନିଷଗୁଡ଼ିକୁ ଅଧିକ ଧୀର କରିଦେବ। ଟୋର୍ ବ୍ରାଉଜର୍ ବ୍ୟବହାର କରିବା ସମୟରେ, ଆପଣଙ୍କର ସୁରକ୍ଷା ସେଟିଂସ୍ ସମନ୍ୱୟ କରିବାକୁ ପଡ଼ିପାରେ ବୋଲି ଧ୍ୟାନ ଦିଅନ୍ତୁ। ସବୁଠାରୁ ନିମ୍ନ ଅପ୍ସନ୍, ଯାହାକୁ “ସ୍ଟାଣ୍ଡାର୍ଡ” ବୋଲି କୁହାଯାଏ, ଏହାରେ କ୍ଲାଉଡଫ୍ଲାର୍ ଟର୍ନସ୍ଟାଇଲ୍ ଚ୍ୟାଲେଞ୍ଜ ସଫଳ ହୁଏ। ଉଚ୍ଚ ଅପ୍ସନ୍ସ, ଯାହାକୁ “ସେଫର୍” ଏବଂ “ସେଫେଷ୍ଟ” ବୋଲି କୁହାଯାଏ, ଏହାରେ ଚ୍ୟାଲେଞ୍ଜ ବିଫଳ ହୁଏ। ବଡ଼ ଫାଇଲଗୁଡ଼ିକ ପାଇଁ କେବେ କେବେ ଧୀର ଡାଉନଲୋଡ୍ ମଧ୍ୟରେ ବିଚ୍ଛିନ୍ନ ହୋଇପାରେ। ଆମେ ଏକ ଡାଉନଲୋଡ୍ ମ୍ୟାନେଜର୍ (ଯଥା JDownloader) ବ୍ୟବହାର କରିବାକୁ ସୁପାରିଶ କରୁଛୁଁ ଯାହା ସ୍ୱୟଂଚାଳିତ ଭାବେ ବଡ଼ ଡାଉନଲୋଡ୍ ରିଜ୍ୟୁମ୍ କରିପାରିବ। କାହିଁ ଧୀର ଡାଉନଲୋଡ୍ ଏତେ ଧୀର ହୁଅନ୍ତି? ପ୍ରାୟ ପଚାରିତ ପ୍ରଶ୍ନ (FAQ) <a %(a_list)s>ଟୋରେଣ୍ଟ ତାଲିକା ଉତ୍ପାଦକ</a> ବ୍ୟବହାର କରନ୍ତୁ ଯାହା ଆପଣଙ୍କର ସ୍ଟୋରେଜ୍ ସ୍ପେସ୍ ସୀମାରେ ଥିବା ସବୁଠାରୁ ଆବଶ୍ୟକ ଟୋରେଣ୍ଟଗୁଡ଼ିକର ତାଲିକା ତିଆରି କରିବ। ହଁ, <a %(a_llm)s>LLM ଡାଟା</a> ପୃଷ୍ଠା ଦେଖନ୍ତୁ। ଅଧିକାଂଶ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ସିଧାସଳଖ ଫାଇଲ୍ ଧାରଣ କରେ, ଯାହାର ଅର୍ଥ ହେଉଛି ଆପଣ ଟୋରେଣ୍ଟ କ୍ଲାଇଅଣ୍ଟଗୁଡ଼ିକୁ କେବଳ ଆବଶ୍ୟକ ଥିବା ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରିବାକୁ ନିର୍ଦ୍ଦେଶ ଦେଇପାରିବେ। କେଉଁ ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରିବାକୁ ନିର୍ଦ୍ଦିଷ୍ଟ କରିବାକୁ, ଆପଣ ଆମର ମେଟାଡାଟା <a %(a_generate)s>ସୃଷ୍ଟି</a> କରିପାରିବେ କିମ୍ବା ଆମର ElasticSearch ଏବଂ MariaDB ଡାଟାବେସଗୁଡ଼ିକୁ <a %(a_download)s>ଡାଉନଲୋଡ୍</a> କରିପାରିବେ। ଦୁର୍ଭାଗ୍ୟବଶତଃ, କିଛି ଟୋରେଣ୍ଟ ସଂଗ୍ରହଗୁଡ଼ିକ ମୂଳରେ .zip କିମ୍ବା .tar ଫାଇଲ୍ ଧାରଣ କରେ, ଏହି ପରିସ୍ଥିତିରେ ଆପଣକୁ ବ୍ୟକ୍ତିଗତ ଫାଇଲ୍ ଚୟନ କରିବା ପୂର୍ବରୁ ସମଗ୍ର ଟୋରେଣ୍ଟକୁ ଡାଉନଲୋଡ୍ କରିବାକୁ ପଡ଼ିବ। ଟୋରେଣ୍ଟଗୁଡ଼ିକୁ ଫିଲ୍ଟର କରିବା ପାଇଁ ସହଜ ଉପକରଣଗୁଡ଼ିକ ଏପର୍ଯ୍ୟନ୍ତ ଉପଲବ୍ଧ ନୁହେଁ, କିନ୍ତୁ ଆମେ ଅବଦାନକୁ ସ୍ୱାଗତ କରୁଛୁ। (ଯଦିଓ ଆମ ପାଖରେ <a %(a_ideas)s>କିଛି ଧାରଣା</a> ଅଛି ଏହି ମାମଲା ପାଇଁ।) ଦୀର୍ଘ ଉତ୍ତର: ସଂକ୍ଷିପ୍ତ ଉତ୍ତର: ସହଜରେ ନୁହେଁ। ଆମେ ଏହି ତାଲିକାରେ ଥିବା ଟୋରେଣ୍ଟଗୁଡ଼ିକ ମଧ୍ୟରେ ନ୍ୟୁନତମ ଡୁପ୍ଲିକେସନ୍ କିମ୍ବା ଓଭରଲାପ୍ ରଖିବାକୁ ଚେଷ୍ଟା କରୁଛୁ, କିନ୍ତୁ ଏହା ସବୁବେଳେ ସମ୍ଭବ ନୁହେଁ, ଏବଂ ଏହା ମୂଳ ଲାଇବ୍ରେରୀଗୁଡ଼ିକର ନୀତିଗୁଡ଼ିକର ଉପରେ ଭାରସା କରେ। ଯେଉଁ ଲାଇବ୍ରେରୀଗୁଡ଼ିକ ନିଜର ଟୋରେଣ୍ଟଗୁଡ଼ିକ ପ୍ରକାଶ କରେ, ଏହା ଆମ ହାତରେ ନାହିଁ। Anna’s Archive ଦ୍ୱାରା ପ୍ରକାଶିତ ଟୋରେଣ୍ଟଗୁଡ଼ିକ ପାଇଁ, ଆମେ କେବଳ MD5 ହାସ୍‌ ଆଧାରରେ ଡୁପ୍ଲିକେଟ୍ ହଟାଉଛୁ, ଯାହାର ଅର୍ଥ ହେଉଛି ଏକେ ପୁସ୍ତକର ଭିନ୍ନ ସଂସ୍କରଣଗୁଡ଼ିକ ଡୁପ୍ଲିକେଟ୍ ହେଉ ନାହିଁ। ହଁ। ଏଗୁଡ଼ିକ ବାସ୍ତବରେ PDF ଏବଂ EPUB ଅଟେ, ସେମାନଙ୍କର ଅନେକ ଟୋରେଣ୍ଟରେ ଏକ ଏକ୍ସଟେନ୍ସନ୍ ନାହିଁ। ଟୋରେଣ୍ଟ ଫାଇଲ୍‌ଗୁଡ଼ିକ ପାଇଁ ମେଟାଡାଟା ଦେଖିବାକୁ ଦୁଇଟି ସ୍ଥାନ ଅଛି, ଯାହାରେ ଫାଇଲ୍ ପ୍ରକାର/ଏକ୍ସଟେନ୍ସନ୍ ଅନ୍ତର୍ଭୁକ୍ତ ଅଛି: 1. ପ୍ରତ୍ୟେକ ସଂଗ୍ରହ କିମ୍ବା ପ୍ରକାଶର ନିଜସ୍ୱ ମେଟାଡାଟା ଅଛି। ଉଦାହରଣ ସ୍ୱରୂପ, <a %(a_libgen_nonfic)s>Libgen.rs ଟୋରେଣ୍ଟଗୁଡ଼ିକ</a>ର ଏକ ସମ୍ବନ୍ଧିତ ମେଟାଡାଟା ଡାଟାବେସ୍ Libgen.rs ୱେବସାଇଟରେ ହୋଷ୍ଟ କରାଯାଇଛି। ଆମେ ସାଧାରଣତଃ ପ୍ରତ୍ୟେକ ସଂଗ୍ରହର <a %(a_datasets)s>ଡାଟାସେଟ୍ ପୃଷ୍ଠା</a>ରୁ ସମ୍ବନ୍ଧିତ ମେଟାଡାଟା ସମ୍ପଦଗୁଡ଼ିକୁ ଲିଙ୍କ୍ କରୁଛୁ। 2. ଆମର ElasticSearch ଏବଂ MariaDB ଡାଟାବେସଗୁଡ଼ିକୁ <a %(a_generate)s>ସୃଷ୍ଟି</a> କରିବା କିମ୍ବା <a %(a_download)s>ଡାଉନଲୋଡ୍</a> କରିବାକୁ ଆମେ ସୁପାରିଶ କରୁଛୁ। ଏହାରେ Anna’s Archiveର ପ୍ରତ୍ୟେକ ରେକର୍ଡ ପାଇଁ ଏକ ମ୍ୟାପିଂ ଅଛି ଯାହାର ସମ୍ବନ୍ଧିତ ଟୋରେଣ୍ଟ ଫାଇଲ୍‌ଗୁଡ଼ିକ (ଯଦି ଉପଲବ୍ଧ ଅଛି), ElasticSearch JSONରେ “torrent_paths” ଅଧିନରେ ଅଛି। କିଛି ଟୋରେଣ୍ଟ କ୍ଲାଇଏଣ୍ଟ ବଡ଼ ପିସ ସାଇଜକୁ ସମର୍ଥନ କରୁନାହାନ୍ତି, ଯାହା ଆମର ଅନେକ ଟୋରେଣ୍ଟରେ ଅଛି (ନୂତନଗୁଡ଼ିକ ପାଇଁ ଆମେ ଏହା ଆରମ୍ଭ କରିନାହିଁ — ଯଦିଓ ଏହା ବିବରଣୀ ଅନୁଯାୟୀ ବୈଧ!). ଯଦି ଆପଣ ଏହାରେ ପଡ଼ନ୍ତି, ତେବେ ଅନ୍ୟ କ୍ଲାଇଏଣ୍ଟକୁ ପରୀକ୍ଷା କରନ୍ତୁ, କିମ୍ବା ଆପଣଙ୍କ ଟୋରେଣ୍ଟ କ୍ଲାଇଏଣ୍ଟର ନିର୍ମାତାଙ୍କୁ ଅଭିଯୋଗ କରନ୍ତୁ। ମୁଁ ସହଯୋଗ କରିବାକୁ ଚାହେଁ, କିନ୍ତୁ ମୋର ପାଖରେ ବହୁତ ଡିସ୍କ ସ୍ପେସ୍ ନାହିଁ। ଟୋରେଣ୍ଟଗୁଡ଼ିକ ବହୁତ ଧୀରେ ଚାଲୁଛି; ମୁଁ ଆପଣଙ୍କୁ ସିଧାସଳଖ ଡାଟା ଡାଉନଲୋଡ୍ କରିପାରିବି କି? ମୁଁ କେବଳ ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ ଭାଷା କିମ୍ବା ବିଷୟର ଫାଇଲଗୁଡ଼ିକ ଡାଉନଲୋଡ୍ କରିପାରିବି କି? ଆପଣ ଟୋରେଣ୍ଟଗୁଡ଼ିକରେ ଡୁପ୍ଲିକେଟ୍‌ଗୁଡ଼ିକୁ କିପରି ହାଣ୍ଡଲ୍ କରନ୍ତି? ମୁଁ JSON ଭାବରେ ଟୋରେଣ୍ଟ ତାଲିକା ପାଇପାରିବି କି? ମୁଁ ଟୋରେଣ୍ଟଗୁଡ଼ିକରେ PDF କିମ୍ବା EPUB ଦେଖୁନାହିଁ, କେବଳ ବାଇନାରି ଫାଇଲ୍ ଦେଖୁଛି? ମୁଁ କଣ କରିବି? ମୋର ଟୋରେଣ୍ଟ କ୍ଲାଇଏଣ୍ଟ କାହିଁକି ଆପଣଙ୍କ ଟୋରେଣ୍ଟ ଫାଇଲଗୁଡ଼ିକ / ମ୍ୟାଗ୍ନେଟ ଲିଙ୍କଗୁଡ଼ିକ ଖୋଲିପାରୁନାହିଁ? ଟୋରେଣ୍ଟସ୍ ସମ୍ବନ୍ଧୀୟ FAQ ନୂତନ ପୁସ୍ତକ ଆପଲୋଡ୍ କିପରି କରିବି? ଦୟାକରି <a %(a_href)s>ଏହି ଅଦ୍ଭୁତ ପ୍ରକଳ୍ପ</a> ଦେଖନ୍ତୁ। ଆପଣଙ୍କ ପାଖରେ କୌଣସି ଅପଟାଇମ୍ ମନିଟର୍ ଅଛି କି? Anna’s Archive କ’ଣ? ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ବ୍ୟବହାର କରିବାକୁ ସଦସ୍ୟ ହୁଅନ୍ତୁ। ଆମେ ବର୍ତ୍ତମାନ Amazon gift cards, credit ଏବଂ debit cards, crypto, Alipay, ଏବଂ WeChat ସମର୍ଥନ କରୁଛୁ। ଆପଣ ଆଜି ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ଶେଷ କରିଛନ୍ତି। ଅଭିଗମ ଗତ 30 ଦିନରେ ପ୍ରତି ଘଣ୍ଟା ଡାଉନଲୋଡ୍। ପ୍ରତି ଘଣ୍ଟା ହାରାହାରି: %(hourly)s। ପ୍ରତି ଦିନ ହାରାହାରି: %(daily)s। ଆମେ ଆମର ସଂଗ୍ରହକୁ ସହଜ ଏବଂ ମାଗଣାରେ ଯେକୌଣସି ଲୋକଙ୍କ ପାଇଁ ଉପଲବ୍ଧ କରିବା ପାଇଁ ସହଯୋଗୀମାନଙ୍କ ସହିତ କାମ କରୁଛୁ। ଆମେ ବିଶ୍ୱାସ କରୁଛୁ ଯେ ସମସ୍ତଙ୍କର ମାନବତାର ସାମୂହିକ ଜ୍ଞାନର ଅଧିକାର ଅଛି। ଏବଂ <a %(a_search)s>ଲେଖକମାନଙ୍କର ଖର୍ଚ୍ଚରେ ନୁହଁ</a>। Anna’s Archiveରେ ବ୍ୟବହୃତ datasets ସଂପୂର୍ଣ୍ଣ ଖୋଲା ଅଟେ, ଏବଂ ଟୋରେଣ୍ଟ ମାଧ୍ୟମରେ ବୃହତ୍ ପରିମାଣରେ ମିରର୍ କରାଯାଇପାରେ। <a %(a_datasets)s>ଅଧିକ ଜାଣନ୍ତୁ…</a> ଦୀର୍ଘକାଳୀନ ଆର୍କାଇଭ୍ ସମ୍ପୂର୍ଣ୍ଣ ଡାଟାବେସ ସନ୍ଧାନ ପୁସ୍ତକ, ପେପର, ପତ୍ରିକା, କମିକ୍ସ, ଲାଇବ୍ରେରୀ ରେକର୍ଡ, ମେଟାଡାଟା, … ଆମ ସମସ୍ତ <a %(a_code)s>କୋଡ୍</a> ଏବଂ <a %(a_datasets)s>ଡାଟା</a> ସମ୍ପୂର୍ଣ୍ଣ ଖୋଲା ଉତ୍ସ ଅଟେ। <span %(span_anna)s>Anna’s Archive</span> ଦୁଇଟି ଲକ୍ଷ୍ୟ ସହିତ ଏକ ନନ-ପ୍ରଫିଟ ପ୍ରକଳ୍ପ: <li><strong>ସଂରକ୍ଷଣ:</strong> ମାନବତାର ସମସ୍ତ ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ସଂରକ୍ଷଣ କରିବା।</li><li><strong>ପ୍ରବେଶ:</strong> ଏହି ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିକୁ ପୃଥିବୀର ଯେକୌଣସି ଲୋକଙ୍କ ପାଇଁ ଉପଲବ୍ଧ କରାଇବା।</li> ଆମ ପାଖରେ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ଉଚ୍ଚ-ଗୁଣବତ୍ତା ଟେକ୍ସଟ୍ ତଥ୍ୟ ସଂଗ୍ରହ ଅଛି। <a %(a_llm)s>ଅଧିକ ଜାଣନ୍ତୁ…</a> LLM training data 🪩 ମିରର୍ସ: ସେବାକର୍ତ୍ତାଙ୍କ ପାଇଁ ଆହ୍ୱାନ ଯଦି ଆପଣ ଏକ ଉଚ୍ଚ-ଜୋଖିମ ଗୋପନୀୟ ପେମେଣ୍ଟ ପ୍ରୋସେସର ଚାଲାନ୍ତି, ଦୟାକରି ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଆମେ ମଧ୍ୟ ଲୋକମାନଙ୍କୁ ଖୋଜୁଛୁ ଯେଉଁମାନେ ରুচିକର ଛୋଟ ବିଜ୍ଞାପନ ଦେବାକୁ ଚାହୁଁଛନ୍ତି। ସମସ୍ତ ଆୟ ଆମର ସଂରକ୍ଷଣ ପ୍ରୟାସକୁ ଯାଇଥାଏ। ସଂରକ୍ଷଣ ଆମେ ଆନୁମାନ କରୁଛୁ ଯେ ଆମେ ପୃଥିବୀର ପ୍ରାୟ <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% ପୁସ୍ତକ ସଂରକ୍ଷଣ କରିଛୁ।</a> ଆମେ ପୁସ୍ତକ, ପେପର, କମିକ୍ସ, ପତ୍ରିକା ଏବଂ ଅନ୍ୟାନ୍ୟ ଜିନିଷଗୁଡ଼ିକୁ ସଂରକ୍ଷଣ କରୁଛୁ, ଏହି ସାମଗ୍ରୀଗୁଡ଼ିକୁ ବିଭିନ୍ନ <a href="https://en.wikipedia.org/wiki/Shadow_library">ଛାୟା ପୁସ୍ତକାଳୟଗୁଡ଼ିକ</a>, ଆଧିକାରିକ ପୁସ୍ତକାଳୟ ଏବଂ ଅନ୍ୟାନ୍ୟ ସଂଗ୍ରହଗୁଡ଼ିକରୁ ଏକ ସ୍ଥାନରେ ଆଣି ଏକତ୍ର କରିବା ମାଧ୍ୟମରେ। ଏହି ସମସ୍ତ ତଥ୍ୟକୁ ସର୍ବଦା ସଂରକ୍ଷିତ କରାଯାଏ ଏବଂ ବହୁ ପ୍ରତି ତିଆରି କରିବାକୁ ସହଜ କରାଯାଏ — ଟୋରେଣ୍ଟ ବ୍ୟବହାର କରି — ଯାହା ଦ୍ୱାରା ପୃଥିବୀର ଅନେକ ସ୍ଥାନରେ ଅନେକ ପ୍ରତି ରହିଥାଏ। କିଛି ଛାୟା ପୁସ୍ତକାଳୟ ଏହା ନିଜେ କରେ (ଉଦାହରଣ ସ୍ୱରୂପ Sci-Hub, Library Genesis), ଯେତେବେଳେ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଅନ୍ୟ ପୁସ୍ତକାଳୟଗୁଡ଼ିକୁ "ମୁକ୍ତ" କରେ ଯେଉଁମାନେ ବହୁ ପ୍ରତି ବଣ୍ଟନ ପ୍ରଦାନ କରନ୍ତି ନାହିଁ (ଉଦାହରଣ ସ୍ୱରୂପ Z-Library) କିମ୍ବା ସେମାନେ ଛାୟା ପୁସ୍ତକାଳୟ ନୁହଁ (ଉଦାହରଣ ସ୍ୱରୂପ Internet Archive, DuXiu)। ଏହି ବିସ୍ତୃତ ବିତରଣ, ଖୋଲା-ମୂଲ ର କୋଡ୍ ସହିତ ମିଶି, ଆମର ୱେବସାଇଟକୁ ଟେକଡାଉନ୍ ବିରୋଧରେ ସହନଶୀଳ କରେ, ଏବଂ ମାନବତାର ଜ୍ଞାନ ଏବଂ ସଂସ୍କୃତିର ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣକୁ ସୁନିଶ୍ଚିତ କରେ। <a href="/datasets">ଆମର ଡାଟାସେଟ୍ସ</a> ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ। ଯଦି ଆପଣ <a %(a_member)s>ସଦସ୍ୟ</a> ଅଟନ୍ତି, ବ୍ରାଉଜର ସତ୍ୟାପନ ଆବଶ୍ୟକ ନୁହେଁ। 🧬&nbsp;SciDB ହେଉଛି Sci-Hub ର ଏକ ଅବିରତ ଅଂଶ। ସାଇଡିବି ଖୋଲା DOI Sci-Hub ନୂତନ ପେପର ଅପଲୋଡ୍ କରିବାକୁ <a %(a_paused)s>ବିରତି</a> ନେଇଛି। ସିଧାସଳଖ ଅଭିଗମ %(count)s ଶିକ୍ଷାଗତ ପ୍ରବନ୍ଧଗୁଡ଼ିକୁ 🧬&nbsp;SciDB ହେଉଛି Sci-Hub ର ଏକ ଅବିରତତା, ଏହାର ପରିଚିତ ଇଣ୍ଟରଫେସ୍ ଏବଂ ପିଡ଼ିଏଫ୍ ଗୁଡ଼ିକର ସିଧାସଳଖ ଦେଖା। ଆପଣଙ୍କ DOI ପ୍ରବେଶ କରନ୍ତୁ ଦେଖିବା ପାଇଁ। ଆମ ପାଖରେ ସମ୍ପୂର୍ଣ୍ଣ Sci-Hub ସଂଗ୍ରହ ଅଛି, ଏବଂ ନୂତନ ପେପର୍ ମଧ୍ୟ ଅଛି। ଅଧିକାଂଶକୁ Sci-Hub ସଦୃଶ ପରିଚିତ ଇଣ୍ଟରଫେସ୍ ସହିତ ସିଧାସଳଖ ଦେଖାଯାଇପାରେ। କିଛିକୁ ବାହ୍ୟ ସ୍ରୋତରୁ ଡାଉନଲୋଡ୍ କରାଯାଇପାରେ, ଏହି ମାମଲାରେ ଆମେ ସେଗୁଡ଼ିକର ଲିଙ୍କ ଦେଖାଉଛୁ। ଟୋରେଣ୍ଟ ସିଡ୍ କରି ଆପଣ ବହୁତ ବଡ଼ ସାହାଯ୍ୟ କରିପାରିବେ। <a %(a_torrents)s>ଅଧିକ ଜାଣନ୍ତୁ…</a> >%(count)s ସିଡର୍ସ <%(count)s ସିଡର୍ସ %(count_min)s–%(count_max)s ସିଡର୍ସ 🤝 ସେବାକର୍ତ୍ତାଙ୍କୁ ଖୋଜୁଛୁ ଏକ ନନ-ପ୍ରଫିଟ୍, ଖୋଲା-ମୂଲ୍ୟସ୍ତର ପ୍ରକଳ୍ପ ଭାବରେ, ଆମେ ସବୁବେଳେ ଲୋକମାନଙ୍କୁ ସାହାଯ୍ୟ କରିବାକୁ ଖୋଜୁଛୁ। IPFS ଡାଉନଲୋଡ୍ ସୂଚୀ %(by)s, ସୃଷ୍ଟି <span %(span_time)s>%(time)s</span> ସଞ୍ଚୟ କରନ୍ତୁ ❌ କିଛି ଭୁଲ ହୋଇଛି। ଦୟାକରି ପୁନଃଚେଷ୍ଟା କରନ୍ତୁ। ✅ ସଞ୍ଚିତ। ଦୟାକରି ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ। ତାଲିକା ଖାଲି ଅଛି। ସମ୍ପାଦନା ଏହି ତାଲିକାରୁ ଫାଇଲ୍ ଖୋଜିବା ଏବଂ “ତାଲିକା” ଟାବ୍ ଖୋଲିବା ଦ୍ୱାରା ଯୋଡ଼ନ୍ତୁ କିମ୍ବା କାଢ଼ନ୍ତୁ। ତାଲିକା ଆମେ କିପରି ସାହାଯ୍ୟ କରିପାରିବା ଅତିରିକ୍ତତା ହଟାଇବା (deduplication) ଟେକ୍ସଟ୍ ଏବଂ ମେଟାଡାଟା ଉତ୍ତୋଳନ OCR ଆମେ ଆମର ସମ୍ପୂର୍ଣ୍ଣ ସଂଗ୍ରହଗୁଡ଼ିକୁ ଉଚ୍ଚ-ଗତି ଆକ୍ସେସ୍ ପ୍ରଦାନ କରିପାରିବା, ଏବଂ ଅପ୍ରକାଶିତ ସଂଗ୍ରହଗୁଡ଼ିକୁ ମଧ୍ୟ। ଏହା ହେଉଛି ଉଦ୍ୟମ-ସ୍ତରର ଆକ୍ସେସ୍ ଯାହାକୁ ଆମେ ଦଶ ହଜାର USD ର ରେଞ୍ଜର ଦାନରେ ପ୍ରଦାନ କରିପାରିବା। ଆମେ ଏହାକୁ ଉଚ୍ଚ-ଗୁଣବତ୍ତାର ସଂଗ୍ରହଗୁଡ଼ିକ ପାଇଁ ବଦଳାଇବାକୁ ମଧ୍ୟ ଇଚ୍ଛୁକ। ଯଦି ଆପଣ ଆମକୁ ଆମର ତଥ୍ୟର ସମୃଦ୍ଧି ପ୍ରଦାନ କରିପାରିବେ, ତେବେ ଆମେ ଆପଣଙ୍କୁ ଫେରତ ଦେଇପାରିବା: ଆପଣଙ୍କର ମଡେଲ୍ ପାଇଁ ଭଲ ତଥ୍ୟ ପାଇଁ ମାନବ ଜ୍ଞାନର ଦୀର୍ଘକାଳୀନ ଅଭିଲେଖ ସମର୍ଥନ କରନ୍ତୁ! <a %(a_contact)s>ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ</a> ଆମେ କିପରି ଏକାଠି କାମ କରିପାରିବା ଆଲୋଚନା କରିବାକୁ। ଏହା ଭଲଭାବରେ ବୁଝାଯାଇଛି ଯେ LLMs ଉଚ୍ଚ-ଗୁଣବତ୍ତାର ତଥ୍ୟରେ ଫଳିଥାଏ। ଆମ ପାଖରେ ବିଶ୍ୱର ସବୁଠାରୁ ବଡ଼ ସଂଗ୍ରହ ଅଛି, ଯାହାରେ ପୁସ୍ତକ, ପେପର୍, ପତ୍ରିକା ଇତ୍ୟାଦି ଅଛି, ଯାହା ଉଚ୍ଚ ଗୁଣବତ୍ତାର ଟେକ୍ସଟ୍ ସ୍ରୋତ ମଧ୍ୟରୁ କିଛି। LLM ତଥ୍ୟ ବିଶିଷ୍ଟ ପ୍ରମାଣ ଏବଂ ପ୍ରକାର ଆମ ସଂଗ୍ରହରେ ଏକ ଶତାଧିକ ମିଲିଅନ୍ ଫାଇଲ୍ ଅଛି, ଯାହାରେ ଏକାଡେମିକ୍ ଜର୍ଣ୍ଣାଲ୍, ପାଠ୍ୟପୁସ୍ତକ ଏବଂ ପତ୍ରିକା ଅଛି। ଆମେ ବଡ଼ ଅବସ୍ଥିତ ସଂଗ୍ରହଗୁଡ଼ିକୁ ଏକତ୍ର କରି ଏହି ପ୍ରମାଣକୁ ସାଧନ କରିଛୁ। ଆମର କିଛି ସ୍ରୋତ ସଂଗ୍ରହଗୁଡ଼ିକ ଏକାଠାରେ ଉପଲବ୍ଧ ଅଛି (Sci-Hub, ଏବଂ Libgen ର କିଛି ଅଂଶ)। ଅନ୍ୟାନ୍ୟ ସ୍ରୋତ ଆମେ ସ୍ୱୟଂ ମୁକ୍ତ କରିଛୁ। <a %(a_datasets)s>Datasets</a> ଏକ ସମ୍ପୂର୍ଣ୍ଣ ଅଭିଯାନ ଦେଖାଏ। ଆମ ସଂଗ୍ରହରେ ଇ-ବୁକ୍ ଯୁଗ ପୂର୍ବରୁ ଲକ୍ଷାଧିକ ପୁସ୍ତକ, ପେପର୍ ଏବଂ ପତ୍ରିକା ଅଛି। ଏହି ସଂଗ୍ରହର ବଡ଼ ଅଂଶ ଆଗରୁ OCR କରାଯାଇଛି, ଏବଂ ଆଗରୁ ଅଳ୍ପ ଆନ୍ତରିକ ଅତିରିକ୍ତତା ଅଛି। ଚାଲୁ ରଖନ୍ତୁ ଯଦି ଆପଣ ଆପଣଙ୍କର କୀ ହରାଇଛନ୍ତି, ଦୟାକରି <a %(a_contact)s>ଆମକୁ ସମ୍ପର୍କ କରନ୍ତୁ</a> ଏବଂ ସମ୍ଭବ ଥିବା ସବୁ ତଥ୍ୟ ପ୍ରଦାନ କରନ୍ତୁ। ଆପଣଙ୍କୁ ଆମ ସହିତ ସମ୍ପର୍କ କରିବା ପାଇଁ ଅସ୍ଥାୟୀ ଭାବେ ନୂତନ ଖାତା ସୃଷ୍ଟି କରିବାକୁ ପଡ଼ିପାରେ। ଦୟାକରି <a %(a_account)s>ଲଗିନ୍</a> କରନ୍ତୁ ଏହି ପୃଷ୍ଠା ଦେଖିବା ପାଇଁ।</a> ଅନେକ ଖାତା ସୃଷ୍ଟି କରିବାକୁ ଆସୁଥିବା ସ୍ପାମ୍-ବଟ୍ ରୋକିବା ପାଇଁ, ଆମେ ପ୍ରଥମେ ଆପଣଙ୍କର ବ୍ରାଉଜରକୁ ସତ୍ୟାପିତ କରିବାକୁ ପଡ଼ିବ। ଯଦି ଆପଣ ଏକ ଅସୀମ ଲୁପ୍ ରେ ଧରାପଡ଼ନ୍ତି, ଆମେ <a %(a_privacypass)s>Privacy Pass</a> ସଂସ୍ଥାପନ କରିବାକୁ ସୁପାରିଶ କରୁଛୁ। ବିଜ୍ଞାପନ ଅବରୋଧକ ଏବଂ ଅନ୍ୟାନ୍ୟ ବ୍ରାଉଜର ବିସ୍ତାରଣଗୁଡିକ ବନ୍ଦ କରିବାକୁ ମଧ୍ୟ ସାହାଯ୍ୟ କରିପାରେ। ଲଗ୍ ଇନ୍ / ପଞ୍ଜିକରଣ Anna’s Archive ଅସ୍ଥାୟୀ ଭାବେ ରକ୍ଷାରେ ଅଛି। ଦୟାକରି ଏକ ଘଣ୍ଟା ପରେ ଫେରି ଆସନ୍ତୁ। ବିକଳ୍ପ ଲେଖକ ବିକଳ୍ପ ବର୍ଣ୍ଣନା ବିକଳ୍ପ ସଂସ୍କରଣ ବିକଳ୍ପ ବିସ୍ତାରଣ ବିକଳ୍ପ ଫାଇଲନାମ୍ ବିକଳ୍ପ ପ୍ରକାଶକ ବିକଳ୍ପ ଶୀର୍ଷକ ଖୋଲା ସ୍ରୋତ ତାରିଖ ଅଧିକ ପଢନ୍ତୁ… ବର୍ଣ୍ଣନା CADAL SSNO ନମ୍ବର ପାଇଁ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଖୋଜନ୍ତୁ ଡୁସିଉ SSID ନମ୍ବର ପାଇଁ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଖୋଜନ୍ତୁ ଡୁସିଉ DXID ନମ୍ବର ପାଇଁ ଆନାଙ୍କ ଆର୍କାଇଭ୍ ଖୋଜନ୍ତୁ ISBN ପାଇଁ Anna’s Archive ଖୋଜନ୍ତୁ OCLC (WorldCat) ସଂଖ୍ୟା ପାଇଁ ଆନା’ସ ଆର୍କାଇଭ୍ ଖୋଜନ୍ତୁ Open Library ID ପାଇଁ Anna’s Archive ରେ ସନ୍ଧାନ କରନ୍ତୁ ଆନାଙ୍କ ଅର୍କାଇଭ୍ ଅନଲାଇନ୍ ଦର୍ଶକ %(count)s ପ୍ରଭାବିତ ପୃଷ୍ଠାଗୁଡ଼ିକ ଡାଉନଲୋଡ଼ କରିବା ପରେ: ଏହି ଫାଇଲର ଏକ ଭଲ ଭାର୍ସନ %(link)s ରେ ଉପଲବ୍ଧ ହୋଇପାରେ ବଲ୍କ ଟୋରେଣ୍ଟ ଡାଉନଲୋଡ୍ ସଂଗ୍ରହ ଫର୍ମାଟଗୁଡ଼ିକ ମଧ୍ୟରେ ପରିବର୍ତ୍ତନ କରିବାକୁ ଅନଲାଇନ୍ ଟୁଲଗୁଡ଼ିକ ବ୍ୟବହାର କରନ୍ତୁ। ସୁପାରିଶିତ ପରିବର୍ତ୍ତନ ଟୁଲଗୁଡ଼ିକ: %(links)s ବଡ଼ ଫାଇଲଗୁଡ଼ିକ ପାଇଁ, ବିଚ୍ଛିନ୍ନତାକୁ ରୋକିବା ପାଇଁ ଏକ ଡାଉନଲୋଡ୍ ମ୍ୟାନେଜର ବ୍ୟବହାର କରିବାକୁ ସୁପାରିଶ କରାଯାଉଛି। ସୁପାରିଶିତ ଡାଉନଲୋଡ୍ ମ୍ୟାନେଜରଗୁଡ଼ିକ: %(links)s EBSCOhost eBook ଇଣ୍ଡେକ୍ସ (ବିଶେଷଜ୍ଞମାନଙ୍କ ପାଇଁ) (ଉପରେ “GET” ଉପରେ କ୍ଲିକ୍ କରନ୍ତୁ) (ଉପରେ "GET" କ୍ଲିକ୍ କରନ୍ତୁ) ବାହାରି ଡାଉନଲୋଡ୍ <strong>🚀 ତ୍ୱରିତ ଡାଉନଲୋଡ୍ </strong> ଆପଣଙ୍କ ପାଖରେ ଆଜି %(remaining)s ବାକି ଅଛି। ସଦସ୍ୟ ହେବା ପାଇଁ ଧନ୍ୟବାଦ! ❤️ <strong>🚀 ଦ୍ରୁତ ଡାଉନଲୋଡ୍ </strong> ଆପଣ ଆଜି ପାଇଁ ଦ୍ରୁତ ଡାଉନଲୋଡ୍ ସମାପ୍ତ କରିଛନ୍ତି। <strong>🚀 ତ୍ୱରିତ ଡାଉନଲୋଡ୍ସ</strong> ଆପଣ ଏହି ଫାଇଲ୍ ରିସେଣ୍ଟ୍ ଡାଉନଲୋଡ୍ କରିଛନ୍ତି। ଲିଙ୍କଗୁଡ଼ିକ କିଛି ସମୟ ପାଇଁ ବ valid ହୁଅନ୍ତି। <strong>🚀 ତ୍ୱରିତ ଡାଉନଲୋଡ୍</strong> ପୁସ୍ତକ, ପେପର୍ ଏବଂ ଅଧିକର ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣକୁ ସମର୍ଥନ କରିବା ପାଇଁ <a %(a_membership)s>ସଦସ୍ୟ</a> ହୁଅନ୍ତୁ। ଆପଣଙ୍କ ସମର୍ଥନ ପାଇଁ ଆମର କୃତଜ୍ଞତା ଦେଖାଇବା ପାଇଁ, ଆପଣଙ୍କୁ ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ମିଳିବ। ❤️ 🚀 ତ୍ୱରିତ ଡାଉନଲୋଡ୍ 🐢 ଧୀର ଡାଉନଲୋଡ୍ Internet Archive ରୁ ଧାର କରନ୍ତୁ IPFS ଗେଟୱେ # %(num)d (IPFS ସହିତ ଆପଣଙ୍କୁ ଅନେକଥର ଚେଷ୍ଟା କରିବାକୁ ପଡ଼ିପାରେ) Libgen.li ଲିବଜେନ୍.ରସ୍ ଫିକ୍ସନ୍ ଲିବଜେନ୍.ରସ୍ ନନ୍-ଫିକ୍ସନ୍ ଏହାର ବିଜ୍ଞାପନଗୁଡ଼ିକ ମାଲିସିଅସ୍ ସଫ୍ଟୱେର୍ ଧାରଣ କରିଥାଏ ବୋଲି ଜଣାପଡ଼େ, ତେଣୁ ଏକ ଆଡ୍ ବ୍ଲକର୍ ବ୍ୟବହାର କରନ୍ତୁ କିମ୍ବା ବିଜ୍ଞାପନଗୁଡ଼ିକୁ କ୍ଲିକ୍ କରନ୍ତୁ ନାହିଁ। ଆମାଜନର “Send to Kindle” ଡିଜାଜର “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (ନେକ୍ସସ୍/ଏସ୍‌ଟିସି ଫାଇଲଗୁଡିକ ଡାଉନଲୋଡ୍ କରିବାକୁ ଅନିଶ୍ଚିତ ହୋଇପାରେ) କୌଣସି ଡାଉନଲୋଡ୍ ମିଳିଲା ନାହିଁ। ସମସ୍ତ ଡାଉନଲୋଡ୍ ବିକଳ୍ପରେ ସେଇ ଫାଇଲ୍ ଅଛି, ଏବଂ ବ୍ୟବହାର କରିବାକୁ ସୁରକ୍ଷିତ ହେବା ଉଚିତ। ତଥାପି, ଇଣ୍ଟରନେଟରୁ ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରିବା ସମୟରେ ସବୁବେଳେ ସତର୍କ ରୁହନ୍ତୁ, ବିଶେଷକରି Anna’s Archive ବାହାରର ସାଇଟ୍ ଗୁଡିକରୁ। ଉଦାହରଣ ସ୍ୱରୂପ, ଆପଣଙ୍କ ଡିଭାଇସ୍ ଅପଡେଟ୍ ରଖିବାକୁ ନିଶ୍ଚିତ କରନ୍ତୁ। (କୌଣସି ପୁନର୍ନିର୍ଦ୍ଦେଶନା ନାହିଁ) ଆମ ଦର୍ଶକରେ ଖୋଲନ୍ତୁ (ଦର୍ଶକରେ ଖୋଲନ୍ତୁ) ବିକଳ୍ପ #%(num)d: %(link)s %(extra)s CADAL ରେ ମୂଳ ରେକର୍ଡ ଖୋଜନ୍ତୁ DuXiu ରେ ହସ୍ତଚାଳିତ ଭାବେ ଖୋଜନ୍ତୁ ISBNdb ରେ ମୂଳ ରେକର୍ଡ ଖୋଜନ୍ତୁ WorldCat ରେ ମୂଳ ରେକର୍ଡ ଖୋଜନ୍ତୁ ଓପେନ୍ ଲାଇବ୍ରେରୀରେ ମୂଳ ରେକର୍ଡ ଖୋଜନ୍ତୁ ISBN ପାଇଁ ବିଭିନ୍ନ ଅନ୍ୟାନ୍ୟ ଡାଟାବେସ୍ ଖୋଜନ୍ତୁ (କେବଳ ମୁଦ୍ରଣ ଅସମର୍ଥ ପାଠକମାନଙ୍କ ପାଇଁ) PubMed ଫାଇଲ ଖୋଲିବା ପାଇଁ ଆପଣଙ୍କୁ ଏକ ଇବୁକ୍ କିମ୍ବା PDF ପାଠକ ଆବଶ୍ୟକ ହେବ, ଫାଇଲ ଫର୍ମାଟ ଉପରେ ନିର୍ଭର କରି। ସୁପାରିଶିତ ଇବୁକ୍ ପାଠକ: %(links)s ଆନ୍ନାଙ୍କ ଆର୍କାଇଭ୍ 🧬 ସାଇଡିବି ସାଇ-ହବ୍: %(doi)s (ସମ୍ବନ୍ଧିତ DOI Sci-Hub ରେ ଉପଲବ୍ଧ ନ ହୋଇପାରେ) ଆପଣ ଦୁଇଥରି PDF ଏବଂ EPUB ଫାଇଲଗୁଡ଼ିକୁ ଆପଣଙ୍କ Kindle କିମ୍ବା Kobo eReader କୁ ପଠାଇପାରିବେ। ସୁପାରିଶିତ ଟୁଲଗୁଡ଼ିକ: %(links)s ଅଧିକ ସୂଚନା <a %(a_slow)s>FAQ</a> ରେ। ଲେଖକ ଏବଂ ପୁସ୍ତକାଳୟଗୁଡ଼ିକୁ ସମର୍ଥନ କରନ୍ତୁ ଯଦି ଆପଣଙ୍କୁ ଏହା ଭଲ ଲାଗେ ଏବଂ ଆପଣ ଏହାକୁ ଖରିଦିବାକୁ ସକ୍ଷମ, ତେବେ ମୂଳ ଉତ୍ସ କିଣିବାକୁ କିମ୍ବା ସିଧାସଳଖ ଲେଖକମାନଙ୍କୁ ସମର୍ଥନ କରିବାକୁ ଭାବନ୍ତୁ। ଯଦି ଏହା ଆପଣଙ୍କ ସ୍ଥାନୀୟ ପୁସ୍ତକାଳୟରେ ଉପଲବ୍ଧ ଅଛି, ତାହେଲେ ଏଠାରୁ ନିଶୁଳ୍କରେ ଧାର କରିବାକୁ ଭାବନା କରନ୍ତୁ। ଏହି ଫାଇଲ ପାଇଁ ସହଭାଗୀ ସର୍ଭର ଡାଉନଲୋଡ୍ ସାମୟିକ ଭାବେ ଉପଲବ୍ଧ ନୁହେଁ। ଟୋରେଣ୍ଟ ଭରସାଯୋଗ୍ୟ ସହଯୋଗୀମାନଙ୍କ ଠାରୁ। Z-Library Z-ଲାଇବ୍ରେରୀ ଟୋରରେ (ଟୋର ବ୍ରାଉଜର ଆବଶ୍ୟକ) ବାହାର ଡାଉନଲୋଡ୍ ଦେଖାନ୍ତୁ <span class="font-bold">❌ ଏହି ଫାଇଲରେ ସମସ୍ୟା ଥାଇପାରେ, ଏବଂ ଏକ ଉତ୍ସ ଲାଇବ୍ରେରୀରୁ ଲୁଚାଇ ରଖାଯାଇଛି। </span> କେବେ କେବେ ଏହା କପିରାଇଟ୍ ଧାରକଙ୍କ ଅନୁରୋଧରେ ହୁଏ, କେବେ କେବେ ଏହା ଏକ ଭଲ ବିକଳ୍ପ ଉପଲବ୍ଧ ଥିବାରୁ ହୁଏ, କିନ୍ତୁ କେବେ କେବେ ଏହା ଫାଇଲର ସମସ୍ୟା ହେତୁ ହୁଏ। ଏହା ଡାଉନଲୋଡ଼ କରିବାକୁ ଠିକ୍ ହୋଇପାରେ, କିନ୍ତୁ ଆମେ ପ୍ରଥମେ ଏକ ବିକଳ୍ପ ଫାଇଲ୍ ଖୋଜିବାକୁ ସୁପାରିଶ କରୁଛୁ। ଅଧିକ ବିବରଣୀ: ଯଦି ଆପଣ ଏହି ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ତେବେ ଏହାକୁ ଖୋଲିବା ପାଇଁ କେବଳ ଭରସାଯୋଗ୍ୟ, ଅଦ୍ୟତିତ ସଫ୍ଟୱେର୍ ବ୍ୟବହାର କରନ୍ତୁ। ମେଟାଡାଟା ମନ୍ତବ୍ୟ AA: “%(name)s” ପାଇଁ Anna’s Archive ରେ ସନ୍ଧାନ କରନ୍ତୁ କୋଡ୍ସ ଏକ୍ସପ୍ଲୋରର୍: କୋଡ୍‌ସ ଏକ୍ସପ୍ଲୋରରରେ “%(name)s” ଦେଖନ୍ତୁ URL: ୱେବସାଇଟ୍: ଯଦି ଆପଣଙ୍କ ପାଖରେ ଏହି ଫାଇଲ ରହିଛି ଏବଂ ଏହା ଆନାଙ୍କ ସଂଗ୍ରହରେ ଏପର୍ଯ୍ୟନ୍ତ ଉପଲବ୍ଧ ନୁହେଁ, ତେବେ <a %(a_request)s>ଏହାକୁ ଅପଲୋଡ୍ କରନ୍ତୁ</a>। Internet Archive ନିୟନ୍ତ୍ରିତ ଡିଜିଟାଲ ଲେଣ୍ଡିଂ ଫାଇଲ “%(id)s” ଏହା Internet Archive ର ଏକ ଫାଇଲର ରେକର୍ଡ, ସିଧାସଳଖ ଡାଉନଲୋଡ଼ ହୋଇପାରିବା ଫାଇଲ ନୁହେଁ। ଆପଣ ପୁସ୍ତକଟିକୁ ଧାର କରିବାକୁ ଚେଷ୍ଟା କରିପାରନ୍ତି (ନିମ୍ନରେ ଲିଙ୍କ ଅଛି), କିମ୍ବା ଏହି URL ବ୍ୟବହାର କରନ୍ତୁ ଯେତେବେଳେ <a %(a_request)s>ଏକ ଫାଇଲ ଅନୁରୋଧ କରୁଛନ୍ତି</a>। ମେଟାଡାଟା ଉନ୍ନତ କରନ୍ତୁ CADAL SSNO %(id)s ମେଟାଡାଟା ରେକର୍ଡ୍ ଏହା ଏକ ମେଟାଡାଟା ରେକର୍ଡ, ଏକ ଡାଉନଲୋଡ଼ ହୋଇପାରିବା ଫାଇଲ୍ ନୁହେଁ। ଆପଣ ଏହି URL ବ୍ୟବହାର କରିପାରିବେ <a %(a_request)s>ଏକ ଫାଇଲ୍ ଅନୁରୋଧ କରିବା</a> ବେଳେ। DuXiu SSID %(id)s ମେଟାଡାଟା ରେକର୍ଡ୍ ISBNdb %(id)s ମେଟାଡାଟା ରେକର୍ଡ MagzDB ID %(id)s ମେଟାଡାଟା ରେକର୍ଡ Nexus/STC ID %(id)s ମେଟାଡାଟା ରେକର୍ଡ OCLC (WorldCat) ସଂଖ୍ୟା %(id)s ମେଟାଡାଟା ରେକର୍ଡ Open Library %(id)s ମେଟାଡାଟା ରେକର୍ଡ Sci-Hub ଫାଇଲ୍ “%(id)s” ମିଳିଲା ନାହିଁ “%(md5_input)s” ଆମ ଡାଟାବେସରେ ମିଳିଲା ନାହିଁ। ମନ୍ତବ୍ୟ ଯୋଗ କରନ୍ତୁ (%(count)s) ଆପଣ URL ରୁ md5 ପାଇପାରିବେ, ଉଦାହରଣ ସ୍ୱରୂପ ଏହି ଫାଇଲର ଏକ ଉନ୍ନତ ସଂସ୍କରଣର MD5 (ଯଦି ଲାଗୁ ହୁଏ)। ଯଦି ଏହି ଫାଇଲର ସମାନ ଏଡିସନ୍, ସମାନ ଫାଇଲ ଏକ୍ସଟେନସନ୍ ଥାଏ ତେବେ ଏହାକୁ ମିଳାଇବାକୁ ଅନ୍ୟ ଫାଇଲ ଅଛି, ତେବେ ଏହି ଫାଇଲ ବଦଳରେ ଏହାକୁ ବ୍ୟବହାର କରିବା ଉଚିତ। ଯଦି ଆପଣ Anna’s Archive ବାହାରେ ଏହି ଫାଇଲର ଏକ ଉନ୍ନତ ସଂସ୍କରଣ ଜାଣନ୍ତି, ତେବେ ଦୟାକରି <a %(a_upload)s>ଏହାକୁ ଅପଲୋଡ୍ କରନ୍ତୁ</a>। କିଛି ଭୁଲ୍ ହୋଇଛି। ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ। ଆପଣ ଏକ ମତାମତ ଦେଇଛନ୍ତି। ଏହା ଦେଖାଯିବାକୁ ଏକ ମିନିଟ୍ ଲାଗିପାରେ। ଦୟାକରି <a %(a_copyright)s>DMCA / କପିରାଇଟ ଦାବି ଫର୍ମ</a> ବ୍ୟବହାର କରନ୍ତୁ। ସମସ୍ୟା ବର୍ଣ୍ଣନା (ଆବଶ୍ୟକ) ଯଦି ଏହି ଫାଇଲର ଗୁଣବତ୍ତା ଉତ୍କୃଷ୍ଟ ଅଛି, ଆପଣ ଏଠାରେ ଏହା ବିଷୟରେ କିଛି ଆଲୋଚନା କରିପାରିବେ! ଯଦି ନୁହେଁ, ଦୟାକରି “ଫାଇଲ୍ ଇସ୍ୟୁ ରିପୋର୍ଟ” ବଟନ୍ ବ୍ୟବହାର କରନ୍ତୁ। ଉତ୍କୃଷ୍ଟ ଫାଇଲ ଗୁଣବତ୍ତା (%(count)s) ଫାଇଲ ଗୁଣବତ୍ତା ଏହି ଫାଇଲର ମେଟାଡାଟା କିପରି ଉନ୍ନତ କରିବେ ନିଜେ ଶିଖନ୍ତୁ <a %(a_metadata)s>ଏଠାରେ</a>। ସମସ୍ୟା ବର୍ଣ୍ଣନା ଦୟାକରି <a %(a_login)s>ଲଗ୍ ଇନ୍</a> କରନ୍ତୁ। ମୁଁ ଏହି ପୁସ୍ତକଟିକୁ ଭଲପାଇଲି! ଏହି ଫାଇଲର ଗୁଣବତ୍ତା ରିପୋର୍ଟ କରି ସମୁଦାୟକୁ ସାହାଯ୍ୟ କରନ୍ତୁ! 🙌 କିଛି ଭୁଲ୍ ହୋଇଛି। ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ ଏବଂ ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ। ଫାଇଲ ସମସ୍ୟା ରିପୋର୍ଟ କରନ୍ତୁ (%(count)s) ଆପଣଙ୍କ ରିପୋର୍ଟ ଦାଖଲ କରିବା ପାଇଁ ଧନ୍ୟବାଦ। ଏହା ଏହି ପୃଷ୍ଠାରେ ଦେଖାଯିବ, ଏବଂ ଏକ ଠିକ୍ ମଡରେସନ୍ ସିଷ୍ଟମ୍ ହେବା ପର୍ଯ୍ୟନ୍ତ ଆନା ଦ୍ୱାରା ମାନୁଆଲ୍ ଭାବରେ ସମୀକ୍ଷା କରାଯିବ। ମତାମତ ଦିଅନ୍ତୁ ରିପୋର୍ଟ ଦାଖଲ କରନ୍ତୁ ଏହି ଫାଇଲରେ କ'ଣ ଭୁଲ ଅଛି? ଋଣ ନିଅନ୍ତୁ (%(count)s) ମନ୍ତବ୍ୟଗୁଡ଼ିକ (%(count)s) ଡାଉନଲୋଡ୍ (%(count)s) ମେଟାଡାଟା ଅନୁସନ୍ଧାନ କରନ୍ତୁ (%(count)s) ତାଲିକାଗୁଡିକ (%(count)s) ପରିସଂଖ୍ୟାନ (%(count)s) ଏହି ବିଶେଷ ଫାଇଲ୍ ବିଷୟରେ ତଥ୍ୟ ପାଇଁ, ଏହାର <a %(a_href)s>JSON ଫାଇଲ୍</a> ଦେଖନ୍ତୁ। ଏହା <a %(a_ia)s>IA’s Controlled Digital Lending</a> ପୁସ୍ତକାଳୟ ଦ୍ୱାରା ପରିଚାଳିତ ଏକ ଫାଇଲ୍, ଏବଂ ଖୋଜିବା ପାଇଁ ଆନାର ଆର୍କାଇଭ୍ ଦ୍ୱାରା ଇଣ୍ଡେକ୍ସ କରାଯାଇଛି। ଆମେ ସଂକଳନ କରିଥିବା ବିଭିନ୍ନ ଡାଟାସେଟ୍ ବିଷୟରେ ତଥ୍ୟ ପାଇଁ, <a %(a_datasets)s>ଡାଟାସେଟ୍ ପୃଷ୍ଠା</a> ଦେଖନ୍ତୁ। ଲିଙ୍କ ରେକର୍ଡରୁ ମେଟାଡାଟା Open Library ରେ ମେଟାଡାଟା ଉନ୍ନତ କରନ୍ତୁ ଏକ “ଫାଇଲ୍ MD5” ହେଉଛି ଏକ ହାସ୍ ଯାହା ଫାଇଲ୍ ବିଷୟବସ୍ତୁରୁ ଗଣନା କରାଯାଏ, ଏବଂ ସେହି ବିଷୟବସ୍ତୁ ଆଧାରରେ ଯଥାପି ଅନନ୍ୟ ହୁଏ। ଆମେ ଏଠାରେ ଇଣ୍ଡେକ୍ସ କରିଥିବା ସମସ୍ତ ଛାୟା ପୁସ୍ତକାଳୟ ପ୍ରାଥମିକ ଭାବରେ ଫାଇଲ୍ ଚିହ୍ନଟ କରିବା ପାଇଁ MD5 ବ୍ୟବହାର କରନ୍ତି। ଏକ ଫାଇଲ୍ ଅନେକ ଛାୟା ପୁସ୍ତକାଳୟରେ ଦେଖାଯାଇପାରେ। ଆମେ ସଂକଳନ କରିଥିବା ବିଭିନ୍ନ ଡାଟାସେଟ୍ ବିଷୟରେ ତଥ୍ୟ ପାଇଁ, <a %(a_datasets)s>ଡାଟାସେଟ୍ ପୃଷ୍ଠା</a> ଦେଖନ୍ତୁ। ଫାଇଲ ଗୁଣବତ୍ତା ରିପୋର୍ଟ କରନ୍ତୁ ମୋଟ ଡାଉନଲୋଡ୍: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} ଚେକ ମେଟାଡାଟା %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} ଗୁଗୁଲ ବୁକ୍ସ %(id)s} Goodreads %(id)s} ହାଥିଟ୍ରଷ୍ଟ %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} ଲିବି %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} ଟ୍ରାଣ୍ଟର୍ %(id)s} ସତର୍କ: ଅନେକ ଲିଙ୍କ ରେକର୍ଡଗୁଡ଼ିକ: Anna’s Archive ରେ ଆପଣ ଯେତେବେଳେ ଏକ ପୁସ୍ତକ ଦେଖନ୍ତି, ଆପଣ ବିଭିନ୍ନ କ୍ଷେତ୍ରଗୁଡ଼ିକ ଦେଖିପାରିବେ: ଶୀର୍ଷକ, ଲେଖକ, ପ୍ରକାଶକ, ସଂସ୍କରଣ, ବର୍ଷ, ବର୍ଣ୍ଣନା, ଫାଇଲନାମ, ଏବଂ ଅଧିକ। ସେହି ସମସ୍ତ ତଥ୍ୟଗୁଡ଼ିକୁ <em>ମେଟାଡାଟା</em> ବୋଲି କୁହାଯାଏ। ଯେହେତୁ ଆମେ ବିଭିନ୍ନ <em>ସ୍ରୋତ ପୁସ୍ତକାଳୟଗୁଡ଼ିକରୁ</em> ପୁସ୍ତକଗୁଡ଼ିକୁ ଏକତ୍ର କରିଥାଉ, ଆମେ ସେହି ସ୍ରୋତ ପୁସ୍ତକାଳୟରେ ଉପଲବ୍ଧ ମେଟାଡାଟାକୁ ଦେଖାଉ। ଉଦାହରଣ ସ୍ୱରୂପ, ଯଦି ଆମେ Library Genesis ରୁ ଏକ ପୁସ୍ତକ ପାଇଥାଉ, ଆମେ Library Genesis ର ଡାଟାବେସରୁ ଶୀର୍ଷକକୁ ଦେଖାଉ। କେବେ କେବେ ଏକ ପୁସ୍ତକ <em>ବହୁତ ସ୍ରୋତ ପୁସ୍ତକାଳୟରେ</em> ଉପସ୍ଥିତ ଥାଏ, ଯାହାର ମେଟାଡାଟା କ୍ଷେତ୍ରଗୁଡ଼ିକ ଭିନ୍ନ ହୋଇପାରେ। ସେହି ପରିସ୍ଥିତିରେ, ଆମେ ପ୍ରତ୍ୟେକ କ୍ଷେତ୍ରର ସବୁଠାରୁ ଲମ୍ବା ଭାର୍ସନକୁ ଦେଖାଉ, କାରଣ ସେହିଟି ସବୁଠାରୁ ଉପଯୋଗୀ ତଥ୍ୟ ଥିବା ଆଶା କରିବାଯୋଗ୍ୟ! ଆମେ ତଥାପି ବର୍ଣ୍ଣନାର ତଳେ ଅନ୍ୟ କ୍ଷେତ୍ରଗୁଡ଼ିକୁ ଦେଖାଉ, ଉଦାହରଣ ସ୍ୱରୂପ "ବିକଳ୍ପ ଶୀର୍ଷକ" ଭାବରେ (କିନ୍ତୁ ସେମାନେ ଭିନ୍ନ ଥିଲେ ମାତ୍ର)। ଆମେ ମଧ୍ୟ <em>କୋଡ୍ସ</em> ଯେପରିକି ଚିହ୍ନଟକାରୀ ଏବଂ ବର୍ଗୀକରଣକୁ ସ୍ରୋତ ପୁସ୍ତକାଳୟରୁ ଉତ୍ପାଦନ କରିଥାଉ। <em>ଚିହ୍ନଟକାରୀଗୁଡ଼ିକ</em> ଏକ ନିର୍ଦ୍ଦିଷ୍ଟ ସଂସ୍କରଣର ପୁସ୍ତକକୁ ଅନନ୍ୟ ଭାବରେ ପ୍ରତିନିଧିତ୍ୱ କରନ୍ତି; ଉଦାହରଣ ସ୍ୱରୂପ ISBN, DOI, Open Library ID, Google Books ID, କିମ୍ବା Amazon ID। <em>ବର୍ଗୀକରଣକାରୀଗୁଡ଼ିକ</em> ଅନେକ ସମାନ ପୁସ୍ତକଗୁଡ଼ିକୁ ଏକତ୍ର କରନ୍ତି; ଉଦାହରଣ ସ୍ୱରୂପ ଡ୍ୟୁଇ ଡେସିମାଲ (DCC), UDC, LCC, RVK, କିମ୍ବା GOST। କେବେ କେବେ ଏହି କୋଡ୍ସଗୁଡ଼ିକ ସ୍ରୋତ ପୁସ୍ତକାଳୟରେ ସ୍ପଷ୍ଟ ଭାବରେ ଲିଙ୍କ ହୋଇଥାଏ, ଏବଂ କେବେ କେବେ ଆମେ ସେଗୁଡ଼ିକୁ ଫାଇଲନାମ କିମ୍ବା ବର୍ଣ୍ଣନାରୁ ଉତ୍ପାଦନ କରିପାରିବା (ପ୍ରାୟତଃ ISBN ଏବଂ DOI)। ଆମେ ଚିହ୍ନଟକାରୀଗୁଡ଼ିକୁ ବ୍ୟବହାର କରି <em>ମେଟାଡାଟା-ମାତ୍ର ସଂଗ୍ରହଗୁଡ଼ିକରେ</em> ରେକର୍ଡଗୁଡ଼ିକୁ ଖୋଜିପାରିବା, ଯେପରିକି OpenLibrary, ISBNdb, କିମ୍ବା WorldCat/OCLC। ଯଦି ଆପଣ ସେହି ସଂଗ୍ରହଗୁଡ଼ିକୁ ବ୍ରାଉଜ କରିବାକୁ ଚାହୁଁଛନ୍ତି, ଆମର ସର୍ଚ୍ଚ ଇଞ୍ଜିନରେ ଏକ ବିଶିଷ୍ଟ <em>ମେଟାଡାଟା ଟାବ୍</em> ଅଛି। ଆମେ ମିଳିତ ରେକର୍ଡଗୁଡ଼ିକୁ ବ୍ୟବହାର କରି ଅନୁପସ୍ଥିତ ମେଟାଡାଟା କ୍ଷେତ୍ରଗୁଡ଼ିକୁ ପୂରଣ କରିବା (ଉଦାହରଣ ସ୍ୱରୂପ ଯଦି ଏକ ଶୀର୍ଷକ ଅନୁପସ୍ଥିତ ଅଛି), କିମ୍ବା ଉଦାହରଣ ସ୍ୱରୂପ "ବିକଳ୍ପ ଶୀର୍ଷକ" ଭାବରେ (ଯଦି ଏକ ଅବସ୍ଥିତ ଶୀର୍ଷକ ଅଛି)। ଏକ ପୁସ୍ତକର ମେଟାଡାଟା କେଉଁଠାରୁ ଆସିଛି ଠିକ୍ କିପରି ଦେଖିବାକୁ, ଏକ ପୁସ୍ତକ ପୃଷ୍ଠାରେ <em>“Technical details” ଟାବ୍</em> କୁ ଦେଖନ୍ତୁ। ଏହାରେ ସେହି ପୁସ୍ତକର କଚା JSON ରେକର୍ଡରେ ଲିଙ୍କ ଅଛି, ଯାହାରେ ମୂଳ ରେକର୍ଡଗୁଡ଼ିକର କଚା JSON ରେ ଇଙ୍ଗିତ ଅଛି। ଅଧିକ ତଥ୍ୟ ପାଇଁ, ନିମ୍ନଲିଖିତ ପୃଷ୍ଠାଗୁଡ଼ିକୁ ଦେଖନ୍ତୁ: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, ଏବଂ <a %(a_example)s>Example metadata JSON</a>। ଶେଷରେ, ଆମ ସମସ୍ତ ମେଟାଡାଟାକୁ <a %(a_generated)s>generated</a> କିମ୍ବା <a %(a_downloaded)s>downloaded</a> ଭାବରେ ElasticSearch ଏବଂ MariaDB ଡାଟାବେସ ଭାବରେ ହୋଇପାରେ। ପୃଷ୍ଠଭୂମି ଆପଣ ମେଟାଡାଟା ଉନ୍ନତ କରି ପୁସ୍ତକଗୁଡ଼ିକର ସଂରକ୍ଷଣରେ ସାହାଯ୍ୟ କରିପାରିବେ! ପ୍ରଥମେ, Anna’s Archive ରେ ମେଟାଡାଟା ବିଷୟରେ ପୃଷ୍ଠଭୂମି ପଢ଼ନ୍ତୁ, ଏବଂ Open Library ସହିତ ଲିଙ୍କ କରି ମେଟାଡାଟା କିପରି ଉନ୍ନତ କରିବା ଶିଖନ୍ତୁ, ଏବଂ Anna’s Archive ରେ ମାଗଣା ସଦସ୍ୟତା ଅର୍ଜନ କରନ୍ତୁ। ମେଟାଡାଟା ଉନ୍ନତ କରନ୍ତୁ ଯଦି ଆପଣ ଖରାପ ମେଟାଡାଟା ସହିତ ଏକ ଫାଇଲକୁ ମିଳନ୍ତି, ଆପଣ କିପରି ଏହାକୁ ସଠିକ କରିବେ? ଆପଣ ସ୍ରୋତ ପୁସ୍ତକାଳୟକୁ ଯାଇ ଏହାର ମେଟାଡାଟା ସଠିକ କରିବା ପ୍ରକ୍ରିୟାକୁ ଅନୁସରଣ କରିପାରିବେ, କିନ୍ତୁ ଯଦି ଏକ ଫାଇଲ ବହୁତ ସ୍ରୋତ ପୁସ୍ତକାଳୟରେ ଉପସ୍ଥିତ ଥାଏ ତେବେ କଣ କରିବେ? Anna’s Archive ରେ ଗୋଟିଏ ଚିହ୍ନଟକାରୀକୁ ବିଶେଷ ଭାବରେ ବ୍ୟବହାର କରାଯାଏ। <strong>Open Library ରେ annas_archive md5 କ୍ଷେତ୍ର ସବୁଠାରୁ ଅନ୍ୟ ମେଟାଡାଟାକୁ ସବୁବେଳେ ଅତିକ୍ରମ କରେ!</strong> ଆମେ ପ୍ରଥମେ ଥୋଡ଼ା ପଛକୁ ଯାଇ Open Library ବିଷୟରେ ଜାଣିବା। Open Library କୁ 2006 ମସିହାରେ Aaron Swartz ଦ୍ୱାରା ପ୍ରତିଷ୍ଠା କରାଯାଇଥିଲା ଯାହାର ଲକ୍ଷ୍ୟ ଥିଲା "ପ୍ରତ୍ୟେକ ପ୍ରକାଶିତ ପୁସ୍ତକ ପାଇଁ ଗୋଟିଏ ଜାଲ ପୃଷ୍ଠା"। ଏହା ପୁସ୍ତକ ମେଟାଡାଟା ପାଇଁ ଗୋଟିଏ Wikipedia ପରି: ସମସ୍ତେ ଏହାକୁ ସମ୍ପାଦନା କରିପାରନ୍ତି, ଏହା ମାଗଣା ଲାଇସେନ୍ସ ହୋଇଥାଏ, ଏବଂ ଏହାକୁ ବଲ୍କରେ ଡାଉନଲୋଡ୍ କରାଯାଇପାରେ। ଏହା ଗୋଟିଏ ପୁସ୍ତକ ଡାଟାବେସ ଯାହା ଆମର ମିଶନ ସହିତ ସବୁଠାରୁ ଅଧିକ ସମନ୍ୱୟ ରଖେ — ପ୍ରକୃତରେ, Anna’s Archive Aaron Swartz ର ଦୃଷ୍ଟିକୋଣ ଏବଂ ଜୀବନରୁ ପ୍ରେରିତ ହୋଇଛି। ଚକ୍ରକୁ ପୁନଃ ଆବିଷ୍କାର କରିବା ପରିବର୍ତ୍ତେ, ଆମେ ଆମର ସେବକମାନଙ୍କୁ Open Library ଦିଗରେ ପୁନଃନିର୍ଦ୍ଦେଶିତ କରିବାକୁ ନିଷ୍ପତ୍ତି ନେଲୁ। ଯଦି ଆପଣ ଏକ ପୁସ୍ତକକୁ ଭୁଲ ମେଟାଡାଟା ସହିତ ଦେଖନ୍ତି, ଆପଣ ନିମ୍ନଲିଖିତ ପ୍ରକାରରେ ସାହାଯ୍ୟ କରିପାରିବେ: ଏହା କେବଳ ପୁସ୍ତକ ପାଇଁ କାମ କରେ, ଏକାଡେମିକ ପେପର କିମ୍ବା ଅନ୍ୟ ପ୍ରକାରର ଫାଇଲ ପାଇଁ ନୁହେଁ। ଅନ୍ୟ ପ୍ରକାରର ଫାଇଲ ପାଇଁ ଆମେ ଏବେ ମଧ୍ୟ ଉତ୍ସ ଲାଇବ୍ରେରୀ ଖୋଜିବାକୁ ସୁପାରିଶ କରୁ। ଆମେ Anna’s Archive ରେ ପରିବର୍ତ୍ତନଗୁଡ଼ିକୁ ସମ୍ମିଳିତ କରିବାକୁ କିଛି ସପ୍ତାହ ଲାଗିପାରେ, କାରଣ ଆମେ ସବୁଠାରୁ ନୂତନ Open Library ଡାଟା ଡମ୍ପ ଡାଉନଲୋଡ୍ କରିବାକୁ ଏବଂ ଆମର ସନ୍ଧାନ ସୂଚକାଙ୍କ ପୁନଃ ସୃଷ୍ଟି କରିବାକୁ ପଡ଼େ।  <a %(a_openlib)s>Open Library ୱେବସାଇଟକୁ</a> ଯାଆନ୍ତୁ। ସଠିକ ପୁସ୍ତକ ରେକର୍ଡକୁ ଖୋଜନ୍ତୁ। <strong>ସତର୍କତା:</strong> ନିଶ୍ଚିତ ହୁଅନ୍ତୁ ଯେ ସଠିକ <strong>ସଂସ୍କରଣକୁ</strong> ଚୟନ କରନ୍ତି। Open Library ରେ, ଆପଣଙ୍କର "କାର୍ଯ୍ୟ" ଏବଂ "ସଂସ୍କରଣ" ଅଛି। ଏକ "କାର୍ଯ୍ୟ" ହେଉଛି "Harry Potter and the Philosopher's Stone"। ଏକ "ସଂସ୍କରଣ" ହେଉଛି: ୧୯୯୭ ମସିହାରେ Bloomsbery ଦ୍ୱାରା ପ୍ରକାଶିତ ପ୍ରଥମ ସଂସ୍କରଣ, ଯାହାରେ ୨୫୬ ପୃଷ୍ଠା ଅଛି। ୨୦୦୩ ମସିହାରେ Raincoast Books ଦ୍ୱାରା ପ୍ରକାଶିତ ପେପରବ୍ୟାକ ସଂସ୍କରଣ, ଯାହାରେ ୨୨୩ ପୃଷ୍ଠା ଅଛି। ୨୦୦୦ ମସିହାରେ Media Rodzina ଦ୍ୱାରା ପ୍ରକାଶିତ ପୋଲିଶ୍ ଅନୁବାଦ “Harry Potter I Kamie Filozoficzn”, ଯାହାରେ ୩୨୮ ପୃଷ୍ଠା ଅଛି। ଏହି ସମସ୍ତ ସଂସ୍କରଣରେ ଭିନ୍ନ ISBN ଏବଂ ଭିନ୍ନ ବିଷୟବସ୍ତୁ ଅଛି, ତେଣୁ ନିଶ୍ଚିତ ଭାବରେ ସଠିକ ଏକଟି ଚୟନ କରନ୍ତୁ! ରେକର୍ଡଟି ସମ୍ପାଦନ କରନ୍ତୁ (କିମ୍ବା ଯଦି କୌଣସି ରେକର୍ଡ ନାହିଁ, ତାହା ସୃଷ୍ଟି କରନ୍ତୁ), ଏବଂ ଯଥାସମ୍ଭବ ଅଧିକ ତଥ୍ୟ ଯୋଗ କରନ୍ତୁ! ଆପଣ ଏଠାରେ ଅଛନ୍ତି, ତେଣୁ ରେକର୍ଡଟିକୁ ଅତ୍ୟନ୍ତ ଅଦ୍ଭୁତ କରିଦିଅନ୍ତୁ। “ID Numbers” ଅଧିନରେ “Anna’s Archive” ଚୟନ କରନ୍ତୁ ଏବଂ Anna’s Archive ରୁ ପୁସ୍ତକର MD5 ଯୋଗ କରନ୍ତୁ। ଏହା URL ରେ “/md5/” ପରେ ଥିବା ଦୀର୍ଘ ଅକ୍ଷର ଏବଂ ସଂଖ୍ୟାର ଶୃଙ୍ଖଳା। Anna’s Archive ରେ ଏହି ରେକର୍ଡ ସହିତ ମେଳ ହେଉଥିବା ଅନ୍ୟ ଫାଇଲଗୁଡ଼ିକୁ ଖୋଜିବାକୁ ଚେଷ୍ଟା କରନ୍ତୁ, ଏବଂ ସେଗୁଡ଼ିକୁ ମଧ୍ୟ ଯୋଗ କରନ୍ତୁ। ଭବିଷ୍ୟତରେ ଆମେ ସେଗୁଡ଼ିକୁ Anna’s Archive ସନ୍ଧାନ ପୃଷ୍ଠାରେ ଡୁପ୍ଲିକେଟ୍ ଭାବରେ ଗୋଷ୍ଠୀକରଣ କରିପାରିବା। ଆପଣ ଶେଷ କଲେ, ଆପଣ ଯେଉଁ URL ଅପଡେଟ୍ କରିଛନ୍ତି ତାହା ଲେଖନ୍ତୁ। ଆପଣ Anna’s Archive MD5 ସହିତ କମ୍ ସେ କମ୍ ୩୦ଟି ରେକର୍ଡ ଅପଡେଟ୍ କଲେ, ଆମକୁ <a %(a_contact)s>ଇମେଲ୍</a> ପଠାନ୍ତୁ ଏବଂ ତାଲିକା ପଠାନ୍ତୁ। ଆମେ ଆପଣଙ୍କୁ Anna’s Archive ପାଇଁ ଏକ ମାଗଣା ସଦସ୍ୟତା ଦେବୁ, ଯାହା ଆପଣଙ୍କୁ ଏହି କାମ କରିବାକୁ ସହଜ କରିବ (ଏବଂ ଆପଣଙ୍କ ସହଯୋଗ ପାଇଁ ଧନ୍ୟବାଦ ଭାବରେ)। ଏହା ଉଚ୍ଚ ଗୁଣବତ୍ତାର ସମ୍ପାଦନ ହେବା ଆବଶ୍ୟକ, ଯାହାରେ ପ୍ରଚୁର ପରିମାଣର ତଥ୍ୟ ଯୋଗ ହୋଇଛି, ନାହିଁହେଲେ ଆପଣଙ୍କ ଅନୁରୋଧ ଅସ୍ୱୀକୃତ ହେବ। ଯଦି କୌଣସି ସମ୍ପାଦନ Open Library ମଡ଼ରେଟରମାନଙ୍କ ଦ୍ୱାରା ପୁନଃ ପ୍ରତ୍ୟାବର୍ତ୍ତନ କିମ୍ବା ସଂଶୋଧନ ହୋଇଥାଏ, ତେବେ ଆପଣଙ୍କ ଅନୁରୋଧ ମଧ୍ୟ ଅସ୍ୱୀକୃତ ହେବ। Open Library ଲିଙ୍କିଂ ଯଦି ଆପଣ ଆମ କାର୍ଯ୍ୟର ବିକାଶ ଏବଂ ପ୍ରଚାଳନାରେ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଭାବରେ ଜଡ଼ିତ ହୁଅନ୍ତି, ଆମେ ଆପଣଙ୍କ ସହିତ ଅଧିକ ଦାନ ଆୟକୁ ଅଂଶୀଦାର କରିବାକୁ ଆଲୋଚନା କରିପାରିବା, ଯାହାକୁ ଆପଣ ଆବଶ୍ୟକ ଭାବରେ ବ୍ୟବହାର କରିପାରିବେ। ଆମେ କେବଳ ହୋଷ୍ଟିଂ ପାଇଁ ଦେବୁ ଯେତେବେଳେ ଆପଣ ସବୁକିଛି ସେଟ୍ ଅପ୍ କରିଥିବେ, ଏବଂ ଅଦ୍ୟତନ ସହିତ ଆର୍କାଇଭ୍ କୁ ଅଦ୍ୟତନ ରଖିବାକୁ ସକ୍ଷମ ହେବାକୁ ପ୍ରମାଣ କରିଥିବେ। ଏହାର ଅର୍ଥ ହେଉଛି ଆପଣ ପ୍ରଥମ 1-2 ମାସ ପାଇଁ ନିଜ ଖର୍ଚ୍ଚରେ ଦେବାକୁ ପଡ଼ିବ। ଆପଣଙ୍କ ସମୟ ପ୍ରତିଦାନ ଦିଆଯିବ ନାହିଁ (ଏବଂ ଆମର ମଧ୍ୟ ନୁହେଁ), କାରଣ ଏହା ସୁଦ୍ଧା ପ୍ରକୃତ ସେବାକାର୍ଯ୍ୟ। ଆମେ ଆରମ୍ଭରେ ହୋଷ୍ଟିଂ ଏବଂ VPN ଖର୍ଚ୍ଚକୁ $200 ପ୍ରତି ମାସ ପର୍ଯ୍ୟନ୍ତ ଭରପାଇ କରିବାକୁ ଇଚ୍ଛୁକ। ଏହା ଏକ ମୂଳ ଖୋଜ ସର୍ଭର ଏବଂ ଏକ DMCA-ସୁରକ୍ଷିତ ପ୍ରକ୍ସି ପାଇଁ ପର୍ଯ୍ୟାପ୍ତ। ହୋଷ୍ଟିଂ ଖର୍ଚ୍ଚ ଦୟାକରି <strong>ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ ନାହିଁ</strong> ଅନୁମତି ପାଇଁ, କିମ୍ବା ମୂଳ ଉତ୍ତର ପାଇଁ। କାର୍ଯ୍ୟ ଶବ୍ଦଠାରୁ ଉଚ୍ଚରେ କହେ! ସମସ୍ତ ତଥ୍ୟ ବାହାରେ ଅଛି, ସେହିପରି ଆପଣଙ୍କ ମିରର୍ ସେଟ୍ ଅପ୍ କରିବାକୁ ଆଗକୁ ଯାଆନ୍ତୁ। ଆପଣ ଯେତେବେଳେ ସମସ୍ୟାରେ ପଡ଼ନ୍ତି, ଆମର Gitlab କୁ ଟିକେଟ୍ କିମ୍ବା ମର୍ଜ୍ ଅନୁରୋଧ ପୋଷ୍ଟ କରିବାକୁ ନିର୍ବିକାର ହୁଅନ୍ତୁ। ଆମେ ଆପଣଙ୍କ ସହିତ କିଛି ମିରର୍-ନିର୍ଦ୍ଦିଷ୍ଟ ବୈଶିଷ୍ଟ୍ୟଗୁଡ଼ିକ ନିର୍ମାଣ କରିବାକୁ ପଡ଼ିପାରେ, ଯଥା “Anna’s Archive” ରୁ ଆପଣଙ୍କ ୱେବସାଇଟ୍ ନାମକୁ ପୁନଃବ୍ରାଣ୍ଡିଂ, (ପ୍ରାରମ୍ଭରେ) ୟୁଜର୍ ଆକାଉଣ୍ଟଗୁଡ଼ିକ ଅକ୍ଷମ କରିବା, କିମ୍ବା ପୁସ୍ତକ ପୃଷ୍ଠାରୁ ଆମ ମୁଖ୍ୟ ସାଇଟ୍ କୁ ଲିଙ୍କ୍ କରିବା। ଯେତେବେଳେ ଆପଣଙ୍କର ମିରର୍ ଚାଲୁ ହେବ, ଦୟାକରି ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ। ଆମେ ଆପଣଙ୍କର OpSec ରିଭ୍ୟୁ କରିବାକୁ ଇଚ୍ଛୁକ, ଏବଂ ଯେତେବେଳେ ସେହିଟା ମଜବୁତ ହେବ, ଆମେ ଆପଣଙ୍କର ମିରର୍ କୁ ଲିଙ୍କ୍ କରିବାକୁ ଆରମ୍ଭ କରିବା, ଏବଂ ଆପଣଙ୍କ ସହିତ ଆଉ ନିକଟରେ କାମ କରିବାକୁ ଆରମ୍ଭ କରିବା। ଏହି ପ୍ରକାରରେ ଯୋଗାଯୋଗ କରିବାକୁ ଇଚ୍ଛୁକ ଥିବା ସମସ୍ତଙ୍କୁ ପୂର୍ବ ହାତ ଧନ୍ୟବାଦ! ଏହା ହୃଦୟର ଦୃଢ଼ତା ପାଇଁ ନୁହେଁ, କିନ୍ତୁ ଏହା ମାନବ ଇତିହାସରେ ସବୁଠାରୁ ବଡ଼ ସତ୍ୟ ସ୍ୱତନ୍ତ୍ର ପୁସ୍ତକାଳୟର ଦୀର୍ଘାୟୁ କୁ ମଜବୁତ କରିବ। ଆରମ୍ଭ କରିବା Anna’s Archive ର ସ୍ଥିରତା ବୃଦ୍ଧି ପାଇଁ, ଆମେ ମିରର୍ସ ଚାଲାଇବାକୁ ସେବକଙ୍କୁ ଖୋଜୁଛୁ। ଆପଣଙ୍କର ସଂସ୍କରଣକୁ ସ୍ପଷ୍ଟ ଭାବରେ ଏକ ମିରର୍ ଭାବରେ ପରିଚିତ କରାଯାଇଛି, ଯଥା “Bob’s Archive, an Anna’s Archive mirror”। ଆପଣ ଏହି କାମ ସହିତ ସଂପୃକ୍ତ ବିପଦଗୁଡ଼ିକ ଗ୍ରହଣ କରିବାକୁ ଇଚ୍ଛୁକ, ଯାହା ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ। ଆପଣ ଆବଶ୍ୟକ ଅପରେସନାଲ୍ ସୁରକ୍ଷାର ଗଭୀର ବୁଝାମଣି ରଖନ୍ତି। <a %(a_shadow)s>ଏହି</a> <a %(a_pirate)s>ପୋଷ୍ଟଗୁଡ଼ିକ</a>ର ବିଷୟବସ୍ତୁ ଆପଣଙ୍କ ପାଇଁ ସ୍ୱତଃସିଦ୍ଧ। ପ୍ରାରମ୍ଭରେ ଆମେ ଆପଣଙ୍କୁ ଆମ ପାର୍ଟନର୍ ସର୍ଭର ଡାଉନଲୋଡ୍ ଗୁଡ଼ିକର ଆକ୍ସେସ୍ ଦେବୁ ନାହିଁ, କିନ୍ତୁ ଯଦି ସବୁଠିକ୍ ହୁଏ, ଆମେ ସେଗୁଡ଼ିକ ଆପଣଙ୍କ ସହିତ ଅଂଶୀଦାର କରିପାରିବା। ଆପଣ Anna’s Archive ର ଖୋଲା ମୂଲ କୋଡବେସ୍ ଚଲାଉଛନ୍ତି, ଏବଂ ଆପଣ ନିୟମିତ ଭାବରେ କୋଡ୍ ଏବଂ ତଥ୍ୟ ଅଦ୍ୟତନ କରନ୍ତି। ଆପଣ ଆମ <a %(a_codebase)s>କୋଡବେସ୍</a>କୁ ଯୋଗାଯୋଗ କରିବାକୁ ଇଚ୍ଛୁକ — ଆମ ଟିମ୍ ସହିତ ସହଯୋଗରେ — ଏହାକୁ ସଫଳ କରିବା ପାଇଁ। ଆମେ ଏହା ଖୋଜୁଛୁ: ମିରର୍ସ: ସେବା ଦିବାକୁ ସେବକଙ୍କୁ ଆହ୍ୱାନ ଆଉ ଏକ ଦାନ କରନ୍ତୁ। ଏପର୍ଯ୍ୟନ୍ତ କୌଣସି ଦାନ ନାହିଁ। <a %(a_donate)s>ମୋର ପ୍ରଥମ ଦାନ କରନ୍ତୁ।</a> ଦାନ ବିବରଣୀ ସର୍ବସାଧାରଣରେ ଦେଖାଯାଏ ନାହିଁ। ମୋର ଦାନ 📡 ଆମ ସଂଗ୍ରହର ବ୍ୟାପକ ମିରରିଂ ପାଇଁ, <a %(a_datasets)s>Datasets</a> ଏବଂ <a %(a_torrents)s>Torrents</a> ପୃଷ୍ଠାଗୁଡ଼ିକ ଯାଞ୍ଚ କରନ୍ତୁ। ଗତ 24 ଘଣ୍ଟାରେ ଆପଣଙ୍କ IP ଠିକଣାରୁ ଡାଉନଲୋଡ୍‌ଗୁଡ଼ିକ: %(count)s। 🚀 ତ୍ୱରିତ ଡାଉନଲୋଡ୍ ଓ ବ୍ରାଉଜର ଚେକ୍ସ ଏଡାଇବା ପାଇଁ, <a %(a_membership)s>ସଦସ୍ୟ ହୁଅନ୍ତୁ</a>। ସହଭାଗୀ ୱେବସାଇଟ୍ ରୁ ଡାଉନଲୋଡ୍ କରନ୍ତୁ ଅନ୍ୟ ଟାବରେ Anna’s Archive ବ୍ରାଉଜ୍ କରିବାକୁ ମନେ ରଖନ୍ତୁ ଯେତେବେଳେ ଅପେକ୍ଷା କରୁଛନ୍ତି (ଯଦି ଆପଣଙ୍କ ବ୍ରାଉଜର୍ ପୃଷ୍ଠଭୂମି ଟାବ୍ ରିଫ୍ରେସ୍ କରିବାକୁ ସମର୍ଥନ କରେ)। ଏକାଧିକ ଡାଉନଲୋଡ୍ ପୃଷ୍ଠା ଏକାସାଥେ ଲୋଡ୍ ହେବାକୁ ଅପେକ୍ଷା କରନ୍ତୁ (କିନ୍ତୁ ଦୟାକରି ପ୍ରତ୍ୟେକ ସର୍ଭରରୁ ଏକ ସମୟରେ କେବଳ ଗୋଟିଏ ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରନ୍ତୁ)। ଯେତେବେଳେ ଆପଣ ଏକ ଡାଉନଲୋଡ୍ ଲିଙ୍କ ପାଇବେ ସେହିଟି ଅନେକ ଘଣ୍ଟା ପାଇଁ ବୈଧ ରହିବ। ଅପେକ୍ଷା କରିଥିବା ପାଇଁ ଧନ୍ୟବାଦ, ଏହା ସମସ୍ତଙ୍କ ପାଇଁ ଏହି ୱେବସାଇଟ୍ ମାଗଣାରେ ପ୍ରବେଶଯୋଗ୍ୟ ରଖେ! 😊 🔗 ଏହି ଫାଇଲ ପାଇଁ ସମସ୍ତ ଡାଉନଲୋଡ୍ ଲିଙ୍କଗୁଡ଼ିକ: <a %(a_main)s>ଫାଇଲ ମୁଖ୍ୟ ପୃଷ୍ଠା</a>। ❌ ଧୀର ଡାଉନଲୋଡ୍ ଗୁଡ଼ିକ Cloudflare VPNs ମାଧ୍ୟମରେ କିମ୍ବା Cloudflare IP ଠିକଣାଗୁଡ଼ିକରୁ ଉପଲବ୍ଧ ନୁହେଁ। ❌ ଧୀର ଡାଉନଲୋଡ୍‌ଗୁଡ଼ିକ କେବଳ ଆଧିକାରିକ ୱେବସାଇଟ୍‌ ମାଧ୍ୟମରେ ଉପଲବ୍ଧ। %(websites)sକୁ ଯାଆନ୍ତୁ। 📚 ଡାଉନଲୋଡ୍ କରିବା ପାଇଁ ନିମ୍ନଲିଖିତ URL ବ୍ୟବହାର କରନ୍ତୁ: <a %(a_download)s>ବର୍ତ୍ତମାନ ଡାଉନଲୋଡ୍ କରନ୍ତୁ</a>। ସମସ୍ତଙ୍କୁ ମାଗଣାରେ ଫାଇଲ୍‌ଗୁଡ଼ିକ ଡାଉନଲୋଡ୍ କରିବାର ସୁଯୋଗ ଦେବା ପାଇଁ, ଆପଣଙ୍କୁ ଏହି ଫାଇଲ୍‌ଟି ଡାଉନଲୋଡ୍ କରିବା ପୂର୍ବରୁ ଅପେକ୍ଷା କରିବାକୁ ପଡ଼ିବ। ଦୟାକରି ଏହି ଫାଇଲ୍‌ଟି ଡାଉନଲୋଡ୍ କରିବା ପାଇଁ <span %(span_countdown)s>%(wait_seconds)s</span> ସେକେଣ୍ଡ ଅପେକ୍ଷା କରନ୍ତୁ। ସତର୍କତା: ଗତ 24 ଘଣ୍ଟାରେ ଆପଣଙ୍କ IP ଠିକଣାରୁ ବହୁତ ଡାଉନଲୋଡ୍ ହୋଇଛି। ଡାଉନଲୋଡ୍‌ଗୁଡ଼ିକ ସାଧାରଣ ତୁଳନାରେ ଧୀର ହୋଇପାରେ। ଯଦି ଆପଣ ଏକ VPN, ଶେୟାର୍ ଇଣ୍ଟରନେଟ୍ ସଂଯୋଗ ବ୍ୟବହାର କରୁଛନ୍ତି, କିମ୍ବା ଆପଣଙ୍କ ISP IP ଗୁଡ଼ିକୁ ଶେୟାର୍ କରେ, ତେବେ ଏହି ସତର୍କତା ଏହାର ଫଳ ହୋଇପାରେ। ସଂରକ୍ଷଣ କରନ୍ତୁ ❌ କିଛି ଭୁଲ୍ ହୋଇଛି। ଦୟାକରି ପୁନର୍ବାର ଚେଷ୍ଟା କରନ୍ତୁ। ✅ ସଂରକ୍ଷିତ। ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ। ଆପଣଙ୍କର ପ୍ରଦର୍ଶନ ନାମ ପରିବର୍ତ୍ତନ କରନ୍ତୁ। ଆପଣଙ୍କର ପରିଚୟକର୍ତ୍ତା ( "#" ପରେ ଥିବା ଅଂଶ) ପରିବର୍ତ୍ତନ କରାଯାଇପାରିବ ନାହିଁ। ପ୍ରୋଫାଇଲ ସୃଷ୍ଟି କରାଯାଇଛି <span %(span_time)s>%(time)s</span> ସମ୍ପାଦନା ତାଲିକାଗୁଡିକ ଏକ ନୂତନ ତାଲିକା ସୃଷ୍ଟି କରନ୍ତୁ ଏକ ଫାଇଲ ଖୋଜି ଏବଂ "ତାଲିକା" ଟାବ୍ ଖୋଲନ୍ତୁ। ଏପର୍ଯ୍ୟନ୍ତ କୌଣସି ତାଲିକା ନାହିଁ ପ୍ରୋଫାଇଲ୍ ମିଳିଲା ନାହିଁ। ପ୍ରୋଫାଇଲ୍ ଏହି ସମୟରେ, ଆମେ ପୁସ୍ତକ ଅନୁରୋଧଗୁଡ଼ିକ ପୂରଣ କରିପାରିବୁ ନାହିଁ। ଆମକୁ ଆପଣଙ୍କର ପୁସ୍ତକ ଅନୁରୋଧ ଇମେଲ୍ କରନ୍ତୁ ନାହିଁ। ଦୟାକରି ଆପଣଙ୍କର ଅନୁରୋଧଗୁଡ଼ିକ Z-Library କିମ୍ବା Libgen ଫୋରମରେ କରନ୍ତୁ। ଆନାଙ୍କ ଆର୍କାଇଭ୍‌ରେ ରେକର୍ଡ୍ DOI: %(doi)s ଡାଉନଲୋଡ୍ SciDB Nexus/STC ଏପର୍ଯ୍ୟନ୍ତ କୌଣସି ପ୍ରିଭ୍ୟୁ ଉପଲବ୍ଧ ନାହିଁ। <a %(a_path)s>ଆନାଙ୍କ ଆର୍କାଇଭ୍</a> ରୁ ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରନ୍ତୁ। ମାନବ ଜ୍ଞାନର ପ୍ରବେଶଯୋଗ୍ୟତା ଏବଂ ଦୀର୍ଘକାଳୀନ ସଂରକ୍ଷଣକୁ ସମର୍ଥନ କରିବା ପାଇଁ, <a %(a_donate)s>ସଦସ୍ୟ</a> ହୁଅନ୍ତୁ। ଏକ ବୋନସ୍ ଭାବରେ, 🧬&nbsp;SciDB ସଦସ୍ୟମାନଙ୍କ ପାଇଁ ତ୍ୱରିତ ଲୋଡ୍ ହୁଏ, କୌଣସି ସୀମା ବିନା। କାମ କରୁନାହିଁ? <a %(a_refresh)s>ରିଫ୍ରେସ୍</a> କରିବାକୁ ଚେଷ୍ଟା କରନ୍ତୁ। Sci-Hub ବିଶେଷ ସନ୍ଧାନ କ୍ଷେତ୍ର ଯୋଡନ୍ତୁ ବର୍ଣ୍ଣନା ଏବଂ ମେଟାଡାଟା ମନ୍ତବ୍ୟଗୁଡ଼ିକୁ ସନ୍ଧାନ କରନ୍ତୁ ପ୍ରକାଶିତ ବର୍ଷ ଉନ୍ନତ ଅଭିଗମ ବିଷୟବସ୍ତୁ ପ୍ରଦର୍ଶନ ତାଲିକା ସାରଣୀ ଫାଇଲ୍ ପ୍ରକାର ଭାଷା କ୍ରମ ଅନୁସାରେ ସବୁଠାରୁ ବଡ଼ ସବୁଠାରୁ ସମ୍ପର୍କିତ ନୂତନତମ (ଫାଇଲସାଇଜ୍) (ଖୋଲା ସ୍ରୋତ) (ପ୍ରକାଶନ ବର୍ଷ) ସବୁଠାରୁ ପୁରୁଣା ଅନିୟମିତ ସବୁଠାରୁ ଛୋଟ ସ୍ରୋତ AA ଦ୍ୱାରା ସଂଗ୍ରହ କରାଯାଇ ଏବଂ ଖୋଲା ମୂଲରେ ମିଳିଥାଏ ଡିଜିଟାଲ ଲେଣ୍ଡିଂ (%(count)s) ଜର୍ଣ୍ଣାଲ୍ ଲେଖା (%(count)s) ଆମେ ମିଳାନ ମିଳାଇଛୁ: %(in)s। ଆପଣ <a %(a_request)s>ଫାଇଲ୍ ଅନୁରୋଧ କରିବା</a> ସମୟରେ ସେଠାରେ ମିଳିଥିବା URL ଉଲ୍ଲେଖ କରିପାରନ୍ତି। ମେଟାଡାଟା (%(count)s) କୋଡ୍‌ଗୁଡ଼ିକ ଦ୍ୱାରା ସନ୍ଧାନ ସୂଚକାଙ୍କକୁ ଅନୁସନ୍ଧାନ କରିବାକୁ, <a %(a_href)s>କୋଡ୍‌ସ ଏକ୍ସପ୍ଲୋରର</a> ବ୍ୟବହାର କରନ୍ତୁ। ସନ୍ଧାନ ଇଣ୍ଡେକ୍ସ ମାସିକ ଭାବେ ଅଦ୍ୟତନ ହୁଏ। ଏହା ବର୍ତ୍ତମାନ %(last_data_refresh_date)s ପର୍ଯ୍ୟନ୍ତ ଏଣ୍ଟ୍ରିଗୁଡ଼ିକ ଅନ୍ତର୍ଭୁକ୍ତ କରେ। ଅଧିକ ପ୍ରାକ୍ରୁତିକ ସୂଚନା ପାଇଁ, %(link_open_tag)sଡାଟାସେଟ୍ ପୃଷ୍ଠା</a> ଦେଖନ୍ତୁ। ବାହାର କରନ୍ତୁ କେବଳ ଅନ୍ତର୍ଭୁକ୍ତ କରନ୍ତୁ ଅନନ୍ୟ ଅଧିକ… ପରବର୍ତ୍ତୀ … ପୂର୍ବ ଏହି ସନ୍ଧାନ ସୂଚକାଙ୍କ ଏବେ ଇଣ୍ଟରନେଟ୍ ଆର୍କାଇଭ୍ର କଣ୍ଟ୍ରୋଲ୍ଡ ଡିଜିଟାଲ୍ ଲେଣ୍ଡିଂ ଲାଇବ୍ରେରୀରୁ ମେଟାଡାଟା ଅନ୍ତର୍ଭୁକ୍ତ କରେ। <a %(a_datasets)s>ଆମର ଡାଟାସେଟ୍ ବିଷୟରେ ଅଧିକ</a>। ଅଧିକ ଡିଜିଟାଲ୍ ଧାରଣ ପୁସ୍ତକାଳୟ ପାଇଁ, <a %(a_wikipedia)s>ଉଇକିପିଡ଼ିଆ</a> ଏବଂ <a %(a_mobileread)s>ମୋବାଇଲ୍ ରିଡ୍ ଉଇକି</a> ଦେଖନ୍ତୁ। DMCA / କପିରାଇଟ୍ ଦାବି ପାଇଁ <a %(a_copyright)s>ଏଠାରେ କ୍ଲିକ୍ କରନ୍ତୁ</a>। ଡାଉନଲୋଡ୍ ସମୟ ସନ୍ଧାନ ସମୟରେ ତ୍ରୁଟି। <a %(a_reload)s>ପୃଷ୍ଠାକୁ ପୁନଃଲୋଡ୍ କରନ୍ତୁ</a> ଚେଷ୍ଟା କରନ୍ତୁ। ଯଦି ସମସ୍ୟା ଚାଲିଥାଏ, ଦୟାକରି ଆମକୁ %(email)s ରେ ଇମେଲ୍ କରନ୍ତୁ। ଦ୍ରୁତ ଡାଉନଲୋଡ୍ ବାସ୍ତବରେ, ଯେକୌଣସି ଲୋକ ଆମର <a %(a_torrents)s>ଏକତ୍ର ଟୋରେଣ୍ଟ ତାଲିକା</a> ଦ୍ୱାରା ଏହି ଫାଇଲଗୁଡ଼ିକୁ ସୁରକ୍ଷିତ କରିବାରେ ସାହାଯ୍ୟ କରିପାରିବେ। ➡️ କେବେ କେବେ ଏହା ଭୁଲରେ ଘଟେ ଯେତେବେଳେ ସର୍ଚ୍ଚ ସର୍ଭର ଧୀର ହୋଇଥାଏ। ଏହିପରି ପରିସ୍ଥିତିରେ, <a %(a_attrs)s>ପୁନଃଲୋଡ୍ କରିବା</a> ସହାୟକ ହୋଇପାରେ। ❌ ଏହି ଫାଇଲରେ ସମସ୍ୟା ଥାଇପାରେ | ପେପର ଖୋଜୁଛନ୍ତି କି? ଏହି ସର୍ଚ୍ଚ ଇଣ୍ଡେକ୍ସ ବିଭିନ୍ନ ମେଟାଡାଟା ଉତ୍ସରୁ ମେଟାଡାଟା ସମ୍ମିଳିତ କରେ। <a %(a_datasets)s>ଆମର ଡାଟାସେଟ୍ ବିଷୟରେ ଅଧିକ ଜାଣନ୍ତୁ</a>। ବିଶ୍ୱର ଚାରିପାଖରେ ଲିଖିତ କାର୍ଯ୍ୟଗୁଡ଼ିକ ପାଇଁ ଅନେକ, ଅନେକ ମେଟାଡାଟା ସ୍ରୋତ ଅଛି। <a %(a_wikipedia)s>ଏହି ଉଇକିପିଡ଼ିଆ ପୃଷ୍ଠା</a> ଏକ ଭଲ ଆରମ୍ଭ, କିନ୍ତୁ ଯଦି ଆପଣ ଅନ୍ୟ ଭଲ ତାଲିକାଗୁଡ଼ିକ ଜାଣନ୍ତି, ଦୟାକରି ଆମକୁ ଜଣାନ୍ତୁ। ମେଟାଡାଟା ପାଇଁ, ଆମେ ମୂଳ ରେକର୍ଡଗୁଡିକ ଦେଖାଉଛୁ। ଆମେ କୌଣସି ରେକର୍ଡ ମର୍ଜ୍ କରୁନାହିଁ। ଆମେ ବର୍ତ୍ତମାନ ବିଶ୍ୱର ସବୁଠାରୁ ବ୍ୟାପକ ଖୋଲା ପୁସ୍ତକ, ପେପର୍ ଏବଂ ଅନ୍ୟାନ୍ୟ ଲିଖିତ କାର୍ଯ୍ୟର ସଂଗ୍ରହ ରଖିଛୁ। ଆମେ Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ଏବଂ ଅଧିକ</a> ମିରର୍ କରୁଛୁ। <span class="font-bold">କୌଣସି ଫାଇଲ୍ ମିଳିଲା ନାହିଁ।</span> କମ କିମ୍ବା ଭିନ୍ନ ଖୋଜ ଶବ୍ଦ ଏବଂ ଫିଲ୍ଟର୍ସ ଚେଷ୍ଟା କରନ୍ତୁ। ଫଳାଫଳ %(from)s-%(to)s (%(total)s ମୋଟ) ଯଦି ଆପଣ ଅନ୍ୟ “ଛାୟା ପୁସ୍ତକାଳୟ” ମିଳାନ୍ତି ଯାହାକୁ ଆମେ ମିରର୍ କରିବା ଉଚିତ, କିମ୍ବା ଯଦି ଆପଣଙ୍କର କୌଣସି ପ୍ରଶ୍ନ ଅଛି, ଦୟାକରି %(email)s ଠାରେ ଆମକୁ ସମ୍ପର୍କ କରନ୍ତୁ। %(num)d ଅଂଶିକ ମେଳ %(num)d+ ଅଂଶିକ ମେଳାନ ଡିଜିଟାଲ ଧାର ଗ୍ରନ୍ଥାଗାରରେ ଫାଇଲ୍ ଖୋଜିବା ପାଇଁ ବକ୍ସରେ ଟାଇପ୍ କରନ୍ତୁ। ଆମର %(count)s ସିଧାସଳଖ ଡାଉନଲୋଡ୍ ହୋଇପାରୁଥିବା ଫାଇଲଗୁଡ଼ିକର ତାଲିକାକୁ ସନ୍ଧାନ କରିବାକୁ ବକ୍ସରେ ଟାଇପ୍ କରନ୍ତୁ, ଯାହାକୁ ଆମେ <a %(a_preserve)s>ସଦାରଣ ପାଇଁ ସଂରକ୍ଷଣ କରିବା</a>। ଖୋଜିବା ପାଇଁ ବକ୍ସରେ ଟାଇପ୍ କରନ୍ତୁ। ଆମର %(count)s ଶିକ୍ଷାଗତ ପ୍ରବନ୍ଧ ଏବଂ ପତ୍ରିକା ଲେଖାମାନଙ୍କର ତାଲିକା ଖୋଜିବା ପାଇଁ ବକ୍ସରେ ଟାଇପ୍ କରନ୍ତୁ, ଯାହାକୁ ଆମେ <a %(a_preserve)s>ସଦାରଣ ପାଇଁ ସଂରକ୍ଷଣ କରୁ</a>। ଗ୍ରନ୍ଥାଗାରରୁ ମେଟାଡାଟା ଖୋଜିବା ପାଇଁ ବକ୍ସରେ ଟାଇପ୍ କରନ୍ତୁ। ଏହା <a %(a_request)s>ଏକ ଫାଇଲ୍ ଅନୁରୋଧ କରିବା</a> ସମୟରେ ଉପଯୋଗୀ ହୋଇପାରେ। ସୂଚନା: ଶୀଘ୍ର ନାଭିଗେସନ ପାଇଁ କୀବୋର୍ଡ୍ ଶର୍ଟକଟ୍ “/” (ସନ୍ଧାନ ଫୋକସ୍), “enter” (ସନ୍ଧାନ), “j” (ଉପରକୁ), “k” (ତଳକୁ), “<” (ପୂର୍ବ ପୃଷ୍ଠା), “>” (ପରବର୍ତ୍ତୀ ପୃଷ୍ଠା) ବ୍ୟବହାର କରନ୍ତୁ। ଏଗୁଡ଼ିକ ହେଉଛି ମେଟାଡାଟା ରେକର୍ଡଗୁଡ଼ିକ, <span %(classname)s>ଡାଉନଲୋଡ୍ ହୋଇପାରିବା ଫାଇଲ୍ ନୁହେଁ</span>। ସନ୍ଧାନ ସେଟିଂସ୍ ଖୋଜନ୍ତୁ ଡିଜିଟାଲ ଧାରଣ ଡାଉନଲୋଡ୍ ଜର୍ଣ୍ଣାଲ୍ ଲେଖା ମେଟାଡାଟା ନୂଆ ସନ୍ଧାନ %(search_input)s - ସନ୍ଧାନ ସନ୍ଧାନଟି ଅଧିକ ସମୟ ନେଲା, ଯାହାର ଅର୍ଥ ହେଉଛି ଆପଣ ଅସଠିକ ଫଳାଫଳ ଦେଖିପାରନ୍ତି। କେବେ କେବେ <a %(a_reload)s>ପୃଷ୍ଠାଟିକୁ ପୁନଃଲୋଡ୍ କରିବା</a> ସହାୟକ ହୁଏ। ସନ୍ଧାନଟି ଅଧିକ ସମୟ ନେଲା, ଯାହା ବ୍ୟାପକ ପ୍ରଶ୍ନଗୁଡ଼ିକ ପାଇଁ ସାଧାରଣ। ଫିଲ୍ଟର ଗଣନାଗୁଡ଼ିକ ସଠିକ ନ ହୋଇପାରେ। ବଡ଼ ଅପଲୋଡ୍ (10,000 ଫାଇଲ୍‌ରୁ ଅଧିକ) ଯାହା Libgen କିମ୍ବା Z-Library ଦ୍ୱାରା ଗ୍ରହଣ କରାଯାଏ ନାହିଁ, ଦୟାକରି %(a_email)sରେ ଆମକୁ ସମ୍ପର୍କ କରନ୍ତୁ। Libgen.li ପାଇଁ, ପ୍ରଥମେ <a %(a_forum)s >ତାଙ୍କର ଫୋରମ୍</a> ରେ ବ୍ୟବହାରକାରୀ ନାମ %(username)s ଏବଂ ପାସୱାର୍ଡ %(password)s ସହିତ ଲଗଇନ୍ କରନ୍ତୁ, ଏବଂ ପରେ ତାଙ୍କର <a %(a_upload_page)s >ଅପଲୋଡ୍ ପୃଷ୍ଠା</a>କୁ ଫେରନ୍ତୁ। ବର୍ତ୍ତମାନ ପାଇଁ, ଆମେ Library Genesis ଫୋର୍କଗୁଡ଼ିକରେ ନୂତନ ପୁସ୍ତକ ଅପଲୋଡ୍ କରିବାକୁ ପରାମର୍ଶ ଦେଉଛୁ। ଏଠାରେ ଏକ <a %(a_guide)s>ସୁବିଧାଜନକ ଗାଇଡ୍</a> ଅଛି। ଦୟାକରି ମନେ ରଖନ୍ତୁ ଯେ ଆମେ ଏହି ୱେବସାଇଟରେ ଇଣ୍ଡେକ୍ସ କରୁଥିବା ଦୁଇଟି ଫୋର୍କ ଏହି ଏକେ ଅପଲୋଡ୍ ସିଷ୍ଟମ୍‌ରୁ ତାଣି ନେଉଛି। ଛୋଟ ଅପଲୋଡ୍ (10,000 ଫାଇଲ ପର୍ଯ୍ୟନ୍ତ) ପାଇଁ, ଦୟାକରି ସେଗୁଡ଼ିକୁ ଉଭୟ %(first)s ଏବଂ %(second)s ରେ ଅପଲୋଡ୍ କରନ୍ତୁ। ବିକଳ୍ପ ଭାବେ, ଆପଣ ସେଗୁଡ଼ିକୁ Z-Library ରେ ଅପଲୋଡ୍ କରିପାରିବେ <a %(a_upload)s>ଏଠାରେ</a>। ଏକାଡେମିକ୍ ପେପର ଅପଲୋଡ୍ କରିବାକୁ, ଦୟାକରି (Library Genesis ସହିତ) <a %(a_stc_nexus)s>STC Nexus</a> କୁ ମଧ୍ୟ ଅପଲୋଡ୍ କରନ୍ତୁ। ସେମାନେ ନୂତନ ପେପର ପାଇଁ ସର୍ବୋତ୍ତମ ଛାୟା ପୁସ୍ତକାଳୟ। ଆମେ ଏଯାବତ୍ ସେମାନଙ୍କୁ ଏକତ୍ର କରିନାହିଁ, କିନ୍ତୁ ଆମେ କିଛି ସମୟରେ କରିବୁ। ଆପଣ ସେମାନଙ୍କର <a %(a_telegram)s>Telegram ଉପଲୋଡ୍ ବଟ୍</a> ବ୍ୟବହାର କରିପାରିବେ, କିମ୍ବା ଯଦି ଆପଣଙ୍କର ପ୍ରଚୁର ଫାଇଲ୍ ଅଛି ଯାହାକୁ ଏହି ପ୍ରକାରରେ ଅପଲୋଡ୍ କରିବାକୁ ସମ୍ଭବ ନୁହେଁ, ତେବେ ସେମାନଙ୍କର ପିନ୍ ମେସେଜ୍‌ରେ ଥିବା ଠିକଣାକୁ ସମ୍ପର୍କ କରନ୍ତୁ। <span %(label)s>ଭାରୀ ସେବାକାର୍ଯ୍ୟ (USD$50-USD$5,000 ପୁରସ୍କାର):</span> ଯଦି ଆପଣ ଆମର ମିଶନ ପାଇଁ ବହୁତ ସମୟ ଏବଂ/କିମ୍ବା ସାଧନ ଦେବାକୁ ସକ୍ଷମ, ଆମେ ଆପଣଙ୍କ ସହିତ ଅଧିକ ନିକଟରେ କାମ କରିବାକୁ ଚାହୁଁଛୁ। ଶେଷରେ ଆପଣ ଆଭ୍ୟନ୍ତରୀଣ ଦଳରେ ଯୋଗ ଦେଇପାରିବେ। ଯଦିଓ ଆମର ବଜେଟ ତିକ୍କା ଅଛି, ଆମେ ସବୁଠାରୁ ତୀବ୍ର କାର୍ଯ୍ୟ ପାଇଁ <span %(bold)s>💰 ଅର୍ଥ ପୁରସ୍କାର</span> ଦେଇପାରିବାକୁ ସକ୍ଷମ। <span %(label)s>ସହଜ ସେବାକାର୍ଯ୍ୟ:</span> ଯଦି ଆପଣ କେବଳ କେତେକ ଘଣ୍ଟା ଏଠାରେ ସେଠାରେ ଖର୍ଚ୍ଚ କରିପାରିବେ, ତାହେଲେ ଆପଣ ତଥାପି ସାହାଯ୍ୟ କରିପାରିବେ। ଆମେ ନିୟମିତ ସେବାକାର୍ଯ୍ୟକର୍ତ୍ତାମାନଙ୍କୁ <span %(bold)s>🤝 Anna’s Archive ର ସଦସ୍ୟତା</span> ସହିତ ପୁରସ୍କୃତ କରୁଛୁ। Anna’s Archive ଆପଣଙ୍କ ଭଳି ସେବାକାର୍ଯ୍ୟକର୍ତ୍ତାମାନଙ୍କ ଉପରେ ନିର୍ଭର କରେ। ଆମେ ସମସ୍ତ ପ୍ରତିବଦ୍ଧତା ସ୍ତରକୁ ସ୍ୱାଗତ କରୁଛୁ, ଏବଂ ଆମେ ଦୁଇଟି ପ୍ରମୁଖ ସାହାଯ୍ୟ ଶ୍ରେଣୀ ଖୋଜୁଛୁ: ଯଦି ଆପଣ ସମୟ ଦେଇ ସେବାକାର୍ଯ୍ୟ କରିପାରିବେ ନାହିଁ, ଆପଣ ଆମକୁ <a %(a_donate)s>ଅର୍ଥ ଦାନ</a>, <a %(a_torrents)s>ଆମର ଟୋରେଣ୍ଟଗୁଡ଼ିକ ସିଡ୍ କରିବା</a>, <a %(a_uploading)s>ପୁସ୍ତକ ଅପଲୋଡ୍ କରିବା</a>, କିମ୍ବା <a %(a_help)s>ଆପଣଙ୍କ ମିତ୍ରମାନଙ୍କୁ Anna’s Archive ବିଷୟରେ କହିବା</a> ଦ୍ୱାରା ବହୁତ ସାହାଯ୍ୟ କରିପାରିବେ। <span %(bold)s>କମ୍ପାନୀଗୁଡ଼ିକ:</span> ଆମେ ଉଦ୍ୟମ-ସ୍ତରର ଦାନ ବା ନୂତନ ସଂଗ୍ରହଗୁଡ଼ିକ (ଯେପରିକି ନୂତନ ସ୍କାନ୍, OCR’ed datasets, ଆମର ତଥ୍ୟକୁ ସମୃଦ୍ଧ କରିବା) ପାଇଁ ବଦଳରେ ଆମ ସଂଗ୍ରହଗୁଡ଼ିକୁ ଉଚ୍ଚ-ଗତି ସିଧାସଳଖ ଅଭିଗମନ ଦେଇଥାଉ। ଯଦି ଏହା ଆପଣଙ୍କୁ ଲାଗୁ ହୁଏ ତେବେ <a %(a_contact)s>ଆମ ସହିତ ଯୋଗାଯୋଗ କରନ୍ତୁ</a>। ଆମର <a %(a_llm)s>LLM ପୃଷ୍ଠା</a> ମଧ୍ୟ ଦେଖନ୍ତୁ। ବାଉଣ୍ଟି ଆମେ ସବୁବେଳେ ମଜବୁତ ପ୍ରୋଗ୍ରାମିଂ କିମ୍ବା ଅଫେନସିଭ୍ ସେକ୍ୟୁରିଟି ଦକ୍ଷତା ଥିବା ଲୋକମାନଙ୍କୁ ଖୋଜୁଛୁ। ଆପଣ ମାନବତାର ଉତ୍ତରାଧିକାର ସୁରକ୍ଷିତ କରିବାରେ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଭୂମିକା ନେଇପାରିବେ। ଧନ୍ୟବାଦ ରୂପେ, ଆମେ ମଜବୁତ ଅବଦାନ ପାଇଁ ସଦସ୍ୟତା ଦେଇଥାଉ। ବିଶେଷ ଧନ୍ୟବାଦ ରୂପେ, ଆମେ ବିଶେଷ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଏବଂ କଠିନ କାର୍ଯ୍ୟ ପାଇଁ ଧନରାଶି ଦେଇଥାଉ। ଏହାକୁ ଏକ ଚାକିରିର ପରିବର୍ତ୍ତନା ଭାବରେ ଦେଖାଯିବା ଉଚିତ୍ ନୁହେଁ, କିନ୍ତୁ ଏହା ଏକ ଅତିରିକ୍ତ ପ୍ରେରଣା ଏବଂ ଖର୍ଚ୍ଚ ହେବାରେ ସାହାଯ୍ୟ କରିପାରେ। ଆମର ଅଧିକାଂଶ କୋଡ୍ ଖୋଲା ଉତ୍ସ ଅଟେ, ଏବଂ ଆମେ ବାଉଣ୍ଟି ପ୍ରଦାନ କରିବା ସମୟରେ ଆପଣଙ୍କର କୋଡ୍ ମଧ୍ୟ ଏହିପରି ହେବାକୁ ଅନୁରୋଧ କରିବା। କିଛି ଅପବାଦ ଅଛି ଯାହାକୁ ଆମେ ବ୍ୟକ୍ତିଗତ ଭାବରେ ଆଲୋଚନା କରିପାରିବା। ବାଉଣ୍ଟି ପ୍ରଥମେ କାର୍ଯ୍ୟ ସମାପ୍ତ କରିଥିବା ବ୍ୟକ୍ତିକୁ ପ୍ରଦାନ କରାଯାଏ। ଆପଣ ଏକ ବାଉଣ୍ଟି ଟିକେଟ୍ ଉପରେ ମନ୍ତବ୍ୟ କରିପାରନ୍ତି ଯାହା ଅନ୍ୟମାନଙ୍କୁ ଜଣାଇବାକୁ ଯେ ଆପଣ କିଛି କାମ କରୁଛନ୍ତି, ଯାହା ଅନ୍ୟମାନଙ୍କୁ ରୋକିବାକୁ କିମ୍ବା ଆପଣଙ୍କ ସହିତ ଦଳଗଠନ କରିବାକୁ ସମ୍ପର୍କ କରିବାକୁ ସାହାଯ୍ୟ କରିପାରେ। କିନ୍ତୁ ଜାଣି ରଖନ୍ତୁ ଯେ ଅନ୍ୟମାନେ ମଧ୍ୟ ଏହା ଉପରେ କାମ କରିବାକୁ ସ୍ୱାଧୀନ ଅଟନ୍ତି ଏବଂ ଆପଣଙ୍କୁ ପ୍ରତିଦ୍ୱନ୍ଦ୍ୱିତା କରିପାରନ୍ତି। ତଥାପି, ଆମେ ଅସ୍ତବ୍ୟସ୍ତ କାମ ପାଇଁ ବାଉଣ୍ଟି ପ୍ରଦାନ କରୁନାହିଁ। ଯଦି ଦୁଇଟି ଉଚ୍ଚ ଗୁଣବତ୍ତାର ସମର୍ପଣ ଏକାଠି ହୋଇଥାଏ (ଏକ ଦିନ କିମ୍ବା ଦୁଇ ଦିନ ମଧ୍ୟରେ), ଆମେ ଆମର ବିବେଚନାରେ ଦୁଇଟିକୁ ବାଉଣ୍ଟି ପ୍ରଦାନ କରିପାରିବା, ଉଦାହରଣ ସ୍ୱରୂପ ପ୍ରଥମ ସମର୍ପଣ ପାଇଁ 100%% ଏବଂ ଦ୍ୱିତୀୟ ସମର୍ପଣ ପାଇଁ 50%% (ସେହିପରି 150%% ମୋଟ)। ବଡ଼ ବାଉଣ୍ଟିଗୁଡ଼ିକ ପାଇଁ (ବିଶେଷକରି ସ୍କ୍ରାପିଂ ବାଉଣ୍ଟିଗୁଡ଼ିକ), ଦୟାକରି ଆମ ସହିତ ସମ୍ପର୍କ କରନ୍ତୁ ଯେତେବେଳେ ଆପଣ ~5%% ସମାପ୍ତ କରିଛନ୍ତି, ଏବଂ ଆପଣଙ୍କର ବିଶ୍ୱାସ ଅଛି ଯେ ଆପଣଙ୍କର ପ୍ରକ୍ରିୟା ପୂର୍ଣ୍ଣ ମାଇଲସ୍ଟୋନ୍ ପାଇଁ ସ୍କେଲ୍ କରିବ। ଆପଣଙ୍କର ପ୍ରକ୍ରିୟା ଆମ ସହିତ ଅଂଶୀଦାର କରିବାକୁ ପଡ଼ିବ ଯାହା ଆମକୁ ପ୍ରତିକ୍ରିୟା ଦେବାରେ ସାହାଯ୍ୟ କରିବ। ଏହିପରି ଭାବରେ ଆମେ ନିଷ୍ପତ୍ତି ନେଇପାରିବା ଯଦି ଅନେକ ଲୋକ ବାଉଣ୍ଟି ନିକଟରେ ଆସୁଛନ୍ତି, ଯେପରିକି ଏହାକୁ ଅନେକ ଲୋକଙ୍କୁ ପ୍ରଦାନ କରିବା, ଲୋକମାନଙ୍କୁ ଦଳଗଠନ କରିବାକୁ ପ୍ରେରିତ କରିବା, ଇତ୍ୟାଦି। ସତର୍କତା: ଉଚ୍ଚ-ବାଉଣ୍ଟି କାର୍ଯ୍ୟଗୁଡ଼ିକ <span %(bold)s>କଠିନ</span> — ଏହା ଆରମ୍ଭ କରିବା ପୂର୍ବରୁ ସହଜ କାର୍ଯ୍ୟଗୁଡ଼ିକ ସହିତ ଆରମ୍ଭ କରିବା ବୁଦ୍ଧିମାନ ହୋଇପାରେ। ଆମ <a %(a_gitlab)s>Gitlab issues list</a> କୁ ଯାଆନ୍ତୁ ଏବଂ “Label priority” ଦ୍ୱାରା ଚୟନ କରନ୍ତୁ। ଏହା ଆମେ ଯେ କାର୍ଯ୍ୟଗୁଡ଼ିକୁ ଗୁରୁତ୍ୱ ଦେଉଛୁ ତାହାର କ୍ରମକୁ ଦର୍ଶାଏ। ସ୍ପଷ୍ଟ ବାଉଣ୍ଟି ବିନା କାର୍ଯ୍ୟଗୁଡ଼ିକ ତଥାପି ସଦସ୍ୟତା ପାଇଁ ଯୋଗ୍ୟ, ବିଶେଷକରି ସେଗୁଡ଼ିକ “Accepted” ଏବଂ “Anna’s favorite” ଭାବରେ ଚିହ୍ନିତ ହୋଇଛି। ଆପଣ ଏକ “Starter project” ସହିତ ଆରମ୍ଭ କରିବାକୁ ଚାହିଁପାରନ୍ତି। ହାଲୁକା ସେବାକାର୍ଯ୍ୟ ଏବେ ଆମେ %(matrix)s ରେ ସମନ୍ୱୟିତ ମ୍ୟାଟ୍ରିକ୍ସ ଚ୍ୟାନେଲ୍ ମଧ୍ୟ ରଖିଛୁ। ଯଦି ଆପଣଙ୍କ ପାଖରେ କିଛି ଘଣ୍ଟା ସମୟ ଅଛି, ଆପଣ ଅନେକ ପ୍ରକାରରେ ସାହାଯ୍ୟ କରିପାରିବେ। <a %(a_telegram)s>Telegram ରେ ସେବାକାର୍ଯ୍ୟକାରୀମାନଙ୍କ ଚାଟ୍</a> ଯୋଗ ଦେବାକୁ ନିଶ୍ଚିତ କରନ୍ତୁ। କୃତଜ୍ଞତାର ପ୍ରତୀକ ଭାବରେ, ଆମେ ସାଧାରଣ ମାଇଲସ୍ଟୋନ୍ ପାଇଁ ସାଧାରଣତଃ 6 ମାସର “ଭାଗ୍ୟଶାଳୀ ପୁସ୍ତକାଧ୍ୟକ୍ଷ” ଦେଇଥାଉ, ଏବଂ ଅବିରତ ସେବାକାର୍ଯ୍ୟ ପାଇଁ ଅଧିକ ଦେଇଥାଉ। ସମସ୍ତ ମାଇଲସ୍ଟୋନ୍ ଉଚ୍ଚ ଗୁଣବତ୍ତାର କାର୍ଯ୍ୟ ଆବଶ୍ୟକ କରେ — ଅସାବଧାନ କାର୍ଯ୍ୟ ଆମକୁ ଉପକାର କରିବାଠାରୁ ଅଧିକ କ୍ଷତି କରେ ଏବଂ ଆମେ ଏହାକୁ ପ୍ରତ୍ୟାଖ୍ୟାନ କରିବା। ଆପଣ ମାଇଲସ୍ଟୋନ୍ ପ୍ରାପ୍ତ କଲେ <a %(a_contact)s>ଆମକୁ ଇମେଲ୍ କରନ୍ତୁ</a>। %(links)s ଲିଙ୍କ୍‌ଗୁଡ଼ିକ କିମ୍ବା ସ୍କ୍ରିନ୍‌ଶଟ୍‌ଗୁଡ଼ିକ ଯାହା ଆପଣ ପୂରଣ କରିଛନ୍ତି। Z-Library ବା Library Genesis ଫୋରମ୍‌ଗୁଡ଼ିକରେ ପୁସ୍ତକ (ବା ପେପର୍, ଇତ୍ୟାଦି) ଅନୁରୋଧ ପୂରଣ କରିବା। ଆମର ନିଜସ୍ୱ ପୁସ୍ତକ ଅନୁରୋଧ ପ୍ରଣାଳୀ ନାହିଁ, କିନ୍ତୁ ଆମେ ସେହି ପୁସ୍ତକାଳୟଗୁଡ଼ିକୁ ମିରର୍ କରୁ, ତେଣୁ ସେମାନଙ୍କୁ ଉନ୍ନତ କରିବା Anna’s Archive କୁ ଉନ୍ନତ କରେ। ମାଇଲସ୍ଟୋନ୍ କାର୍ଯ୍ୟ କାର୍ଯ୍ୟ ଉପରେ ନିର୍ଭର କରେ। ଆମର <a %(a_telegram)s>Telegram ରେ ସେବାକାର୍ଯ୍ୟକାରୀମାନଙ୍କ ଚାଟ୍</a> ରେ ପୋଷ୍ଟ କରାଯାଇଥିବା ଛୋଟ କାର୍ଯ୍ୟଗୁଡ଼ିକ। ସାଧାରଣତଃ ସଦସ୍ୟତା ପାଇଁ, କେବଳ କେବଳ ଛୋଟ ପୁରସ୍କାର ପାଇଁ। ଆମର ସେବକ ଚାଟ୍ ଗୋଷ୍ଠୀରେ ପୋଷ୍ଟ କରାଯାଇଥିବା ଛୋଟ କାର୍ଯ୍ୟଗୁଡ଼ିକ। ଆପଣ ସମାଧାନ କରିଥିବା ସମସ୍ୟାଗୁଡ଼ିକରେ ଏକ ମନ୍ତବ୍ୟ ଛାଡ଼ିବାକୁ ନିଶ୍ଚିତ କରନ୍ତୁ, ଯେପରିକି ଅନ୍ୟମାନେ ଆପଣଙ୍କ କାମକୁ ପୁନରାବୃତ୍ତି କରିବେ ନାହିଁ। %(links)s ଲିଙ୍କ୍‌ଗୁଡ଼ିକ ଯାହା ଆପଣ ସୁଧାରିଛନ୍ତି। ଆପଣ ଏକ ଆରମ୍ଭ ବିନ୍ଦୁ ଭାବେ <a %(a_list)s >ଅନିୟମିତ ମେଟାଡାଟା ସମସ୍ୟାଗୁଡ଼ିକର ତାଲିକା</a> ବ୍ୟବହାର କରିପାରିବେ। <a %(a_metadata)s>Open Library ସହିତ</a> ଲିଙ୍କ କରି ମେଟାଡାଟାକୁ ଉନ୍ନତ କରନ୍ତୁ। ଏହାଗୁଡ଼ିକ ଆପଣଙ୍କୁ Anna’s Archive ବିଷୟରେ କାହାକୁ ଜଣାଇବା ଏବଂ ସେମାନେ ଆପଣଙ୍କୁ ଧନ୍ୟବାଦ ଦେବାକୁ ଦେଖାଏ। %(links)s ଲିଙ୍କ୍‌ଗୁଡ଼ିକ କିମ୍ବା ସ୍କ୍ରିନ୍‌ଶଟ୍‌ଗୁଡ଼ିକ। Anna’s Archive ବିଷୟରେ ସୂଚନା ପ୍ରସାରଣ। ଉଦାହରଣ ସ୍ୱରୂପ, AA ରେ ପୁସ୍ତକ ସୁପାରିଶ କରିବା, ଆମର ବ୍ଲଗ୍ ପୋଷ୍ଟଗୁଡ଼ିକର ସଂଯୋଗ ଦେବା, କିମ୍ବା ସାଧାରଣତଃ ଲୋକମାନଙ୍କୁ ଆମର ୱେବସାଇଟକୁ ନେଇଯିବା। ଏକ ଭାଷାକୁ ପୂରା ଅନୁବାଦ କରନ୍ତୁ (ଯଦି ଏହା ପୂରଣ ନିକଟରେ ନଥିଲା)। <a %(a_translate)s>ଏହି ୱେବସାଇଟ୍</a> ଅନୁବାଦ କରିବା। ଆପଣ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଅବଦାନ ଦେଇଥିବା ଦେଖାଉଥିବା ସମ୍ପାଦନା ଇତିହାସର ଲିଙ୍କ। ଆପଣଙ୍କ ଭାଷାରେ Anna’s Archive ପାଇଁ Wikipedia ପୃଷ୍ଠାକୁ ଉନ୍ନତ କରନ୍ତୁ। ଅନ୍ୟ ଭାଷାର AA ର Wikipedia ପୃଷ୍ଠାରୁ ତଥ୍ୟ ଏବଂ ଆମର ୱେବସାଇଟ୍ ଏବଂ ବ୍ଲଗ୍‌ରୁ ତଥ୍ୟ ଅନ୍ତର୍ଭୁକ୍ତ କରନ୍ତୁ। ଅନ୍ୟ ସମ୍ପର୍କିତ ପୃଷ୍ଠାରେ AA ପାଇଁ ସନ୍ଦର୍ଭ ଯୋଡ଼ନ୍ତୁ। ସେବାକାର୍ଯ୍ୟ ଏବଂ ବାଉଣ୍ଟିଗୁଡ଼ିକ 