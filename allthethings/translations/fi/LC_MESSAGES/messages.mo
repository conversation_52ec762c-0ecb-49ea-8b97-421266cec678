��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b U  sd Q  �f *   h   Fh   Si    \k K  ]m *  �n O   �o p   $p D   �p l   �p �  Gq {  �r �   lt �  Hu �   w #  	x �  -z �  +| �   !~    �  -� J   ͂ �   � M   ��   F� *   I� Q  t� D   ƈ (   �    4� "   L� E   o� '   ��    ݉ +   � ,   � -   H�    v� B   �� A  ъ   � T   '� 3   |� 
   ��    ��    ˍ    ލ    �    � 	   �    �    -� #   4�    X�    ^� 	   y� 
   ��    ��    ��     �� !   َ ^  �� n  Z� �   ɒ *   Y� U  �� �   ڔ   x� �   �� $   M� �   r� $   � �   ,� )   � -  � $   A� �  f� 5   �� W   3�    �� <  �� P  ϝ �   � J  ޠ    )� F   A� g   �� �   �     �� (   � q   
� <   |� T   �� "   �    1� n   7� �  �� 0   >� �   o� �   � D   �� 4  � F   9�   �� �   �� �   (� G   ì �   � �   �� D   9�   ~� �   �� j   6� V   �� k   �� 
   d� �   r� .  � D   @�    �� �   �� �   9� 	   � �  � �   ��   E� �  M� �   � /  ɺ   �� U   � H   W� O   �� g   � Z   X� ]   �� 5   � �   G� f   �� B   Q�    �� �   �� a   8� �   �� �   a�     �   � -  � ?  J� c  �� �   �� �   u� 3  [� �   �� �   I� �   �� v  �� �   �� �   �� 	   �� �   �� �   <� �   �� q  �� z   8� �   �� �   G� �  �� w   �� #  3� �   W� $   U� ]   z� �   �� R   l� �  �� �   \� X   �� i   K� !   �� 9  �� �   � }  ��    Y� i   v� �   �� �   y� ,  H� �   u�   K� �  T� -   �� S  -� _  ��   �� �  ��   �� #  �� ]   �� �   @�    �� �   �� ^  �� {  � �   �� �   �� �   p� 
   � U  
� N  c� 
  �     � �   � K  } �   � 8  � �   � U   � 
   * �  8 ]  �	     �   (    D   
 �   e d  C U   � Q   �   P 7   V    � >  � ,  � ;  
 )   I D   s   � h   � v   9 c   � p   '   �    � t  � �   9 �   � D   w �   � 1   T  �   �  �   g!    Z"    k" $   x" B   �" <   �" 1   # H   O# �   �#    %$ 5   1$ D   g$ K   �$ +   �$ 0   $% <   U% t   �%    &     & %  4& 0  Z' �   �(   �) �   �* �   $+ J  �+    I- �   X- �   2. �   �. �  �/ �  1   �2 �  �4 �  }6 D   *8 <   o8    �8 �   �8 �  �9 �  q; ;  = ^  Z> .  �? R  �@   ;C &   QD >  xD B  �E g  �F    bH �   tH J  [I r   �J C  K �   ]L 
   1M "   <M j   _M    �M �  �M }  \P C  �Q �   T �   �T    �U    �U �   �V D   �W x   �W 
  kX    yY �   �Z �  [ d   �\ %  ]    ,^   G^ �   d` &  
a �  4c �  �e �   �g    �h �   �h �  �i    2k b  Ck �  �l    �n   �p �   �r F  �s D   u �  Tu �   .w }   &x ;   �x ]   �x 	   >y 8   Hy     �y +   �y %   �y    �y �   z �  �z �  �} Y  �   �    �� �  � O  �� q  N� �  �� }  O� �  ͍    m� *  |� 	   ��    �� �   4  e�   �� g  �� 	   � �  )� �  '� �  ��   ן �  ��   ɤ r  ܦ    O� �   m� p   &� �  �� �  z� �   #� �   �� �   a� [  �    ]� �   s� A   #� X   e�    �� 4   ֲ 5   � I  A� �  �� m  +� 8   �� �  ҹ �   ��    +� �   3� 6   ɽ �    � �   �� &   B� a   i�    ˿ >  ݿ F  � �  c� �  � l   ��    � k   #�   ��    �� �   �� �  H� �   �� ?   �� .   �� ^  �� *   ]� ~  �� �  � �  �� (   b� �   �� �   � M  �� �  ��    u� �   �� 0   q� �   �� �   i� �   "� �   
� F   �� *   !� �   L� 5   7� �  m� Q  � �   Y� �   &� c   "� >   �� �   �� (  z� �  �� �  )� �   � <   � �   M�   *� I  G� �   �� �  J� �  7� *   � �  1�    �   =� p   D�    �� r  �� !  =� �  _� �   �     � �  �   � �    �  � �  �
 �   o   � P   @   b ~   � y   "    �    � 0  � �   � .   �   � �  � <  � 4  � w  ( �   � �  U '      3  �   P     ! p  ! %   �"    �"    �"    �" !   �"    �"    	#    #    1#    :# 
   L#    W# 
   `#    k#    w#    �#    �#    �#    �# 
   �# 	   �# �   �# R   �$ "   �$    % (   *% !   S% ,   u% 2   �% "   �%    �% 	   &    &    (&    B&    U& 
   i&    w&    �&    �& +   �& &   �&    �& )   ' -   9' 9   g' -   �'    �' &   �' 7   (    D( H   [( :   �(    �( J   �( J   /)    z)    �) %   �)    �)    �)     �)    *    4*    G*    O*    i*    v*    �* 	   �* 
   �*    �* *   �*    �*    �* 	   �*    �* 	   + &   +    <+    B+ 	   I+    S+    c+    o+    �+    �+    �+    �+    �+ 	   �+    �+ %   �+    $, 
   ),    4,    I,    P, 
   k,    v,    �, 
   �,    �, a   �, �   - d   �- �   (. �  �. �   �0 
   1     1    31    C1    J1    V1    n1    z1 U   �1 <   �1 `   -2 (   �2 4   �2 $   �2 z   3 ]   �3 �   �3 f   �4 3   .5 #   b5    �5    �5    �5 	   �5    �5    �5    �5    �5    �5    �5    �5    6    6    6    (6 !   16 
   S6    a6    s6    x6 	   �6 
   �6    �6    �6 #  �6    �7    �7    
8 !   8    28 @   98 a   z8 $   �8 =   9 =   ?9    }9    �9    �9 |   �9    
: 
   : 8   !: {   Z:    �: 
   �: j   �: '   e; l  �; W   �> r   R? �   �? �   b@ .   "A   QA j   ZB <  �B   D    E 
   $E B   2E L   uE X   �E Y   F Y   uF N   �F s   G #   �G +   �G    �G    �G I   H (   LH    uH    zH    �H    �H s   �H    *I &   9I S   `I    �I     �I M   �I S   9J s  �J !   L    #L �   6L    �L    �L    �L    �L 
   M ,   #M :  PM    �N    �N    �N 	   �N J  �N "   P 
   8P    CP h   LP    �P    �P    �P    �P    �P $   �P �   #Q    �Q    �Q Z   �Q     9R    ZR    oR    vR 8   �R \   �R    (S @   =S �   ~S `   T X   qT    �T   �T O   �U    /V /   CV    sV �   �V �   >W    �W B   X �   HX �   �X    �Y &   �Y    �Y N  �Y #   B[ #   f[    �[ &   �[    �[ �   �[    �\    �\ -   �\ @   �\ 	   +] '   5] "   ]] #   �] b  �] U  `   ]a .   ec 4   �c    �c &   �c &  �c �   $e �    f    �f   �f �  �g &   Ri "   yi �  �i -  �k 
   �l    �l 6   �l 	   m C  (m    ln u  |n �  �o   �q    �r �   �r +   Ws &   �s \   �s A  t   Iu �   Qv �  �v p   �x   y U   :z �   �z �   P{ P   �{ �   P| ;   } .   O}    ~}    �}    �} *   �}    �} Z   �} 0   M~ 	   ~~ *   �~ !  �~ #   � �   � �   ݀ �   a� .   �� '   �    G�    c� "   v� '   �� .   ��    �� �   �    � �   	�    Є �   � �   �� q   )�   �� �   �� 3   s� ~   �� 	   &�   0� W   M�    �� �  ��    p�    }�    �� &   �� 0   Ռ    �    � E   � �   X� �   ��    � 	   ��    � �   � D  ֏ �   � ~   Ǒ    F�    _�    x�    ��    ��    ��    ʒ M   Ғ 4    � �  U� Z   4�    �� f   �� _   � M   f� l   �� S   !� M   u� 	   × \   ͗ G   *� �   r� R   �� H   Q�    �� �   �� o   �� O   � x   i� S   � E   6� 
   |� <   �� }   Ĝ �   B� J   ӝ �   �    ў 
  ؞ J   � [   .� �   ��    %� �  .� �   $�    �    �    6�    <�   A� ,   J� k   w� �   � �   զ �   �� �   =� �   � �   �� �   8� O   � (  B� k   k� �  ׬ �   a�   	� 
   � 
   � 
   ,� �   :� 
   ˰ 
   ٰ \   � R   D� �   �� 
   ~� n   �� G   ��    C� A   ó t   � i   z� 
   � �   � 
   |� 
   �� 
   �� #  �� �   ʷ �  [�    � 
   $�    2� �   >�    ޺ #   �� �   � �   � $   ��    ��    μ :   � 8   $� 0   ]�    ��    �� �   Ƚ    �� �  پ �   �� �   z� Y   � �   j�   � �  0� \  � �   q� �   � �   ��    � �  2�    �� �  �� �   l� '  g� �   �� W  � �   r� �   0� �   �� /   T� 7   ��    �� *   ��    ��    �    � �   1� 
   !� i   ,� #   ��    ��    ��    ��     �� t   �� Q   g� 3   �� "  �� 8   � 	   I�    S�    Z� &   u�    ��    �� 
   ��    ��    ��    ��    ��    �� $   � �   *�    ��    �� 
   ��    �� 
   	�    � 
   &�    4�    C� 
   _� '   m� [   ��    �� ,   � ^   2� �   �� �   K� �   � 6  �� �   � 1  �� 
   �� �   � 7   �� T   �� g   � �   �� �   '� A   � g   _�    �� Y   �� x   6� �   ��    5�     <�    ]�    p�    �� !   ��    ��    ��    ��    ��    �� (   �    <�    T�    l�    ��    ��    ��    ��    ��    �� !   �� -   	� �   7� �   ��    T�     t� l   �� U   � r   X� !   �� !   �� .   � 7   >� >   v� ,   �� �   ��   �� 3  �� $   �� ;   � |   H� $   �� �   �� �   �� �   �� H   0�    y� �   �� �   J� n   �� /   K� d   {� �   �� "   �� /   ��    � :   4� R   o� T   ��    �    �    (�    /� �   B� 5   �� '   
� <   5� &   r�    �� "   �� G   ��    "� l   8� G   �� �   �� K   �� J   *� �   u� A   � "   Y� !   |� "   �� !   �� "   �� !   � "   (� +   K� =   w� 9  �� B   �� 7   2  <   j  )   �     �  u   �  �   P �   � �   � 
   h �   s .       2 �   E     $   # 9   H /   � Y   � P    M   ]    � !   � �   � /   �    � \   � �   2 F   /	 �   v	 k   �	 T   j
 r   �
 "   2 +   U w   �    � Z       i �   � 6   )
 5   `
    �
 '   �
 �   �
 B   � T    �   V `   � K   9 �   � �   #    �    � J   � B       Y     l    �    �    � +   � �   � r   � "       * !   @ *   b (   � q   � 8   ( }   a D   �    $ b   ? �   � 0   � V   � 
    ?    B   ^    � k   � o   " /   � O   � 
    1    V   O 
   � s   � )   (    R &   j ^   � �   � B   �        & B   <     R   � 6   � �    �   �    9 <   H �   �     *  �   K  B   �     )! \   H! �   �!    �"    �"    �"    �" $   �" 1   �" R   #    f#    # 	   �# )   �# 8   �# 4   �#    3$ M   ;$ =   �$    �$    �$ -   �$    '%    D% T   V% �   �% D   �&    �& 
   �& �   ' D  �' P   �(    4) P  F) �  �* +   ;, w   g,    �, q  �, .   k/ i   �/    0    0 �  ,0    	2 P   $2 �   u2 o   �2 d   j3    �3 Z   �3 @   J4    �4 }   �4 H    5 1   i5 b   �5 7   �5 T   66 �   �6 �   7    �7 �   �7 v  l8 �   �9 1   �: w  �:   6< �   ?=   > �   #? )   �?    @ �   #@ .   �@ �  �@    �B V   fC    �C    �C �  �C    �E �   �E B  �F /  �G %  
I D   3J C   xJ J   �J 3   K *   ;K T   fK `   �K    L    )L 4   FL %   {L    �L 3   �L c   �L 5   QM    �M �   �M �   N �   �N    �O    �O    �O G   �O c   	P h   mP �   �P a   �Q    R $   R �   DR 	   S �   #S �  �S �   vV E   tW #   �W    �W    �W    �W M   �W 3   ;X ~   oX �   �X R   �Y    +Z    >Z %   QZ    wZ b   �Z    �Z ?   [    D[ *   M[ &   x[    �[    �[ `   �[    \    \ 1   4\    f\    �\ S   �\ �   �\ T   �] X   �] P   K^ �   �^    j_ $   s_ �   �_ �   `` �   -a     b y   b L   �b S   �b Z   !c i   |c \   �c !   Cd C   ed    �d    �d    �d    �d    e    $e    >e -   Ue    �e    �e 5   �e (   �e +   �e    $f :   Df 6   f    �f    �f    �f O   g    Vg    lg 9   ug '   �g `   �g &   8h    _h    uh %   �h     �h    �h w   �h Z   _i z   �i �   5j    k    5k    Kk    fk :   �k 	   �k    �k    �k n   �k    jl .   �l    �l 
   �l 	   �l ;   �l    m   'm    Fn    ]n    rn '   �n &   �n    �n (   �n )   o ,   Eo )   ro )   �o    �o S   �o %   !p    Gp    `p =   qp T   �p     q '   %q    Mq n   mq b   �q `   ?r    �r    �r 	   �r    �r    �r    �r �  s �   �t    'u    >u '   Bu    ju )   {u    �u    �u �   �u B   :v �   }v    \w !   pw �   �w    x    3x    Qx    mx -   �x    �x    �x    �x 2   y    8y '   Vy 8   ~y A  �y >   �z 9   8{ K   r{    �{ �   �{ '   e|    �| L   �|    �| 2   �|    -} >   E} >   �} (   �} �   �}    �~    �~ $   �~    �~    �~            :    M P   b �   � #   �� #   ƀ �   � �   ݁    o�    ��    ��    ��    ʂ    �    ��    �    ,�    >�    Q�    `� 
   q�    �    ��    ��    ��    �� )   Ƀ �   � �   ń i  ą 
  .� �  <� &  � Z  �    m� �   z�    t� �   �� �   �� �  m� �   6� b  � 4   Q� �   �� ;   -�    i� F   �� E   ɕ f   � j   v� �   � �   �� �   Y� U  �    q� �   �� �   4� \   � �   p�    � �   .� i  � �   q� �   �    � `   �� _   ^� �   �� |   �� j   +� ~   ��    �    %�    A� K   W� )   �� 
   ͥ n   ۥ A   J� q   ��    �� �   � y   �� 5   � L   N� S   �� j   � a   Z� R   �� }   � ^   �� t   � �   a�    � *   �� &   #� O   J� 2   ��    ͬ    լ L   ܬ    )�    ;�    S� 9   \� 6   �� =   ͭ    �    '�    4�    :� 	   @� W   J� q   �� K   � 7   `�    ��    �� )   �� 
   � 	   ��    � 	   
�    �    �    %�    .�    =� 
   C�    N�    U�    f�    o�    ~�    ��    ��    ��    ��    ��     ð     �    � y    �    �� M   �� �   �� 
   ��    �� 
   ɲ 
   ײ    �    � 	   � �   �� �   �� O   &� 
   v�    �� |   ��    � �   � �   �� .   6�    e� �   |� �   � M   շ �   #� u   � .   d� �   ��    '�    A� I   \� �   ��    .� �   G� �   ޻ �   `� X   � 
   j�    x�    |�    ��    �� 
   �� 	   ��    �� �   н {   c� �   ߾ �   i� �   K� ^   9� H   �� �  �� ^  �� �   � �   ��   �� >  ��    �� �   ��   �� �   �� �  j� �  � u   �� h  F�    �� F   �� �   � �  �� F   � �   c�    M� 	   \�    f� �   }� N   � c   j� .   �� Y   �� J   W� \   �� (   �� �   (� A   �� /   � A   H� �   ��    j�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: fi
Language-Team: fi <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library on suosittu (ja laiton) kirjasto. He ovat ottaneet Library Genesis -kokoelman ja tehneet siitä helposti haettavan. Lisäksi he ovat erittäin tehokkaita uusien kirjalahjoitusten hankkimisessa, kannustamalla käyttäjiä erilaisilla eduilla. Tällä hetkellä he eivät palauta näitä uusia kirjoja Library Genesikseen. Ja toisin kuin Library Genesis, he eivät tee kokoelmaansa helposti peilattavaksi, mikä estää laajan säilytyksen. Tämä on tärkeää heidän liiketoimintamallilleen, koska he veloittavat rahaa kokoelman laajamittaisesta käytöstä (yli 10 kirjaa päivässä). Emme tee moraalisia arvioita siitä, että rahaa veloitetaan laittoman kirjakokoelman laajasta käytöstä. On kiistatonta, että Z-Library on onnistunut laajentamaan tiedon saatavuutta ja hankkimaan lisää kirjoja. Olemme täällä vain tekemässä omaa osuutemme: varmistamassa tämän yksityisen kokoelman pitkäaikaisen säilymisen. - Anna ja tiimi (<a %(reddit)s>Reddit</a>) Pirate Library Mirrorin alkuperäisessä julkaisussa (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkisto</a>) teimme peilin Z-Librarystä, suuresta laittomasta kirjakokoelmasta. Muistutuksena, tämä on mitä kirjoitimme alkuperäisessä blogikirjoituksessa: Tuo kokoelma on peräisin vuoden 2021 puolivälistä. Sillä välin Z-Library on kasvanut huimaa vauhtia: he ovat lisänneet noin 3,8 miljoonaa uutta kirjaa. Siellä on toki joitakin kaksoiskappaleita, mutta suurin osa vaikuttaa olevan aidosti uusia kirjoja tai aiemmin lähetettyjen kirjojen parempilaatuisia skannauksia. Tämä johtuu suurelta osin Z-Libraryn vapaaehtoisten moderaattoreiden lisääntyneestä määrästä ja heidän deduplikointijärjestelmästään. Haluamme onnitella heitä näistä saavutuksista. Olemme iloisia voidessamme ilmoittaa, että olemme saaneet kaikki kirjat, jotka lisättiin Z-Libraryyn viimeisen peilikuvamme ja elokuun 2022 välillä. Olemme myös palanneet takaisin ja keränneet joitakin kirjoja, jotka jäivät ensimmäisellä kerralla väliin. Kaiken kaikkiaan tämä uusi kokoelma on noin 24TB, mikä on paljon suurempi kuin edellinen (7TB). Peilikuvamme on nyt yhteensä 31TB. Jälleen kerran deduplikoimme Library Genesiksen kanssa, koska tuolle kokoelmalle on jo saatavilla torrentteja. Käy Pirate Library Mirrorissa tutustumassa uuteen kokoelmaan (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkisto</a>). Siellä on lisätietoa tiedostojen rakenteesta ja siitä, mitä on muuttunut viime kerrasta. Emme linkitä siihen täältä, koska tämä on vain blogisivusto, joka ei isännöi laittomia materiaaleja. Tietysti jakaminen on myös loistava tapa auttaa meitä. Kiitos kaikille, jotka jakavat edellistä torrenttisarjaamme. Olemme kiitollisia positiivisesta vastaanotosta ja iloisia siitä, että niin monet ihmiset välittävät tiedon ja kulttuurin säilyttämisestä tällä epätavallisella tavalla. 3x uutta kirjaa lisätty Pirate Library Mirroriin (+24TB, 3,8 miljoonaa kirjaa) Lue kumppaniartikkelit TorrentFreakiltä: <a %(torrentfreak)s>ensimmäinen</a>, <a %(torrentfreak_2)s>toinen</a> - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) kumppaniartikkelit TorrentFreakiltä: <a %(torrentfreak)s>ensimmäinen</a>, <a %(torrentfreak_2)s>toinen</a> Ei niin kauan sitten, "varjokirjastot" olivat kuolemassa. Sci-Hub, valtava laiton akateemisten artikkelien arkisto, oli lakannut ottamasta vastaan uusia teoksia oikeusjuttujen vuoksi. "Z-Library", suurin laiton kirjakirjasto, näki väitettyjen luojiensa joutuvan pidätetyiksi rikollisista tekijänoikeusrikkomuksista. He onnistuivat uskomattomasti pakenemaan pidätyksestään, mutta heidän kirjastonsa on silti uhattuna. Jotkut maat tekevät jo versiota tästä. TorrentFreak <a %(torrentfreak)s>raportoi</a>, että Kiina ja Japani ovat ottaneet käyttöön tekoälypoikkeuksia tekijänoikeuslainsäädäntöönsä. Meille on epäselvää, miten tämä vaikuttaa kansainvälisiin sopimuksiin, mutta se antaa varmasti suojan heidän kotimaisille yrityksilleen, mikä selittää, mitä olemme nähneet. Mitä tulee Anna's Archiveen — jatkamme maanalaista työtämme moraalisen vakaumuksen pohjalta. Silti suurin toiveemme on päästä valoon ja vahvistaa vaikutustamme laillisesti. Ole hyvä ja uudista tekijänoikeudet. Kun Z-Library kohtasi sulkemisuhan, olin jo varmuuskopioinut koko sen kirjaston ja etsinyt alustaa sen säilyttämiseksi. Tämä oli motivaationi Anna's Archiven perustamiseen: jatkaa aiempien aloitteiden missiota. Olemme sittemmin kasvaneet maailman suurimmaksi varjokirjastoksi, joka isännöi yli 140 miljoonaa tekijänoikeudella suojattua tekstiä useissa eri muodoissa — kirjoja, akateemisia artikkeleita, aikakauslehtiä, sanomalehtiä ja muuta. Tiimini ja minä olemme ideologeja. Uskomme, että näiden tiedostojen säilyttäminen ja isännöinti on moraalisesti oikein. Kirjastot ympäri maailmaa kokevat rahoitusleikkauksia, emmekä voi luottaa ihmiskunnan perintöä myöskään yrityksille. Sitten tuli tekoäly. Käytännössä kaikki suuret LLM:itä rakentavat yritykset ottivat meihin yhteyttä kouluttaakseen dataamme. Useimmat (mutta eivät kaikki!) Yhdysvalloissa toimivat yritykset harkitsivat uudelleen, kun he tajusivat työmme laittoman luonteen. Sen sijaan kiinalaiset yritykset ovat innokkaasti omaksuneet kokoelmamme, eivätkä näytä olevan huolissaan sen laillisuudesta. Tämä on huomionarvoista, kun otetaan huomioon Kiinan rooli lähes kaikkien merkittävien kansainvälisten tekijänoikeussopimusten allekirjoittajana. Olemme antaneet nopean pääsyn noin 30 yritykselle. Useimmat niistä ovat LLM-yrityksiä, ja jotkut ovat datavälittäjiä, jotka jälleenmyyvät kokoelmamme. Useimmat ovat kiinalaisia, vaikka olemme työskennelleet myös yritysten kanssa Yhdysvalloista, Euroopasta, Venäjältä, Etelä-Koreasta ja Japanista. DeepSeek <a %(arxiv)s>myönsi</a>, että aiempi versio koulutettiin osalla kokoelmaamme, vaikka he ovatkin vaitonaisia uusimmasta mallistaan (joka todennäköisesti on myös koulutettu datallamme). Jos länsi haluaa pysyä edellä LLM-kilpailussa ja lopulta AGI:ssa, sen on harkittava uudelleen kantaansa tekijänoikeuksiin, ja pian. Riippumatta siitä, oletko kanssamme samaa mieltä moraalisesta näkökulmastamme, tästä on nyt tulossa taloudellinen ja jopa kansallisen turvallisuuden kysymys. Kaikki valtablokit rakentavat keinotekoisia supertiedemiehiä, superhakkerit ja superarmeijat. Tiedonvapaudesta on tulossa näiden maiden selviytymiskysymys — jopa kansallisen turvallisuuden kysymys. Tiimimme on ympäri maailmaa, emmekä ole erityisesti kallellaan mihinkään suuntaan. Mutta kannustaisimme maita, joilla on vahvat tekijänoikeuslait, käyttämään tätä eksistentiaalista uhkaa niiden uudistamiseen. Joten mitä tehdä? Ensimmäinen suosituksemme on yksinkertainen: lyhentää tekijänoikeuden kestoa. Yhdysvalloissa tekijänoikeus myönnetään 70 vuodeksi tekijän kuoleman jälkeen. Tämä on järjetöntä. Voimme tuoda tämän linjaan patenttien kanssa, jotka myönnetään 20 vuodeksi hakemuksen jättämisen jälkeen. Tämä pitäisi olla enemmän kuin tarpeeksi aikaa kirjojen, artikkelien, musiikin, taiteen ja muiden luovien teosten tekijöille saada täysi korvaus ponnisteluistaan (mukaan lukien pitkäaikaiset projektit, kuten elokuvasovitukset). Sitten vähintäänkin päättäjien tulisi sisällyttää poikkeuksia tekstien massasäilytykseen ja levitykseen. Jos yksittäisten asiakkaiden menetetty tulo on pääasiallinen huolenaihe, henkilökohtaisen tason jakelu voisi pysyä kiellettynä. Vastineeksi ne, jotka pystyvät hallitsemaan laajoja arkistoja — LLM:itä kouluttavat yritykset, kirjastot ja muut arkistot — katettaisiin näillä poikkeuksilla. Tekijänoikeusreformi on välttämätön kansallisen turvallisuuden vuoksi TL;DR: Kiinalaiset LLM:t (mukaan lukien DeepSeek) on koulutettu laittomalla kirja- ja artikkeliarkistollani — maailman suurimmalla. Lännen on uudistettava tekijänoikeuslainsäädäntö kansallisen turvallisuuden vuoksi. Katso lisätietoja <a %(all_isbns)s>alkuperäisestä blogikirjoituksesta</a>. Annoimme haasteen parantaa tätä. Palkitsisimme ensimmäisen sijan 6 000 dollarin palkkiolla, toisen sijan 3 000 dollarilla ja kolmannen sijan 1 000 dollarilla. Ylivoimaisen vastauksen ja uskomattomien osallistumisten vuoksi olemme päättäneet hieman kasvattaa palkintopottia ja myöntää neljä kolmatta sijaa, kukin 500 dollaria. Voittajat ovat alla, mutta muista katsoa kaikki osallistumiset <a %(annas_archive)s>täältä</a> tai ladata <a %(a_2025_01_isbn_visualization_files)s>yhdistetty torrentimme</a>. Ensimmäinen sija 6 000 dollaria: phiresky Tämä <a %(phiresky_github)s>osallistuminen</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentti</a>) on yksinkertaisesti kaikkea, mitä halusimme, ja enemmän! Pidimme erityisesti uskomattoman joustavista visualisointivaihtoehdoista (jopa mukautettujen shaderien tukeminen), mutta kattavalla esiasetusten listalla. Pidimme myös siitä, kuinka nopeaa ja sujuvaa kaikki on, yksinkertaisesta toteutuksesta (jossa ei ole edes taustajärjestelmää), nerokkaasta minimapista ja laajasta selityksestä heidän <a %(phiresky_github)s>blogikirjoituksessaan</a>. Uskomaton työ ja ansaittu voittaja! - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Sydämemme ovat täynnä kiitollisuutta. Huomionarvoisia ideoita Pilvenpiirtäjät harvinaisuudelle Paljon liukusäätimiä datasetsien vertailuun, aivan kuin olisit DJ. Mittakaavapalkki kirjojen määrällä. Kauniit etiketit. Siisti oletusvärimaailma ja lämpökartta. Ainutlaatuinen karttanäkymä ja suodattimet Huomautukset ja myös reaaliaikaiset tilastot Reaaliaikaiset tilastot Joitakin muita ideoita ja toteutuksia, joista pidimme erityisesti: Voisimme jatkaa vielä hetken, mutta lopetetaan tähän. Muista katsoa kaikki lähetykset <a %(annas_archive)s>täältä</a> tai lataa <a %(a_2025_01_isbn_visualization_files)s>yhdistetty torrentimme</a>. Niin monta lähetystä, ja jokainen tuo ainutlaatuisen näkökulman, olipa kyseessä käyttöliittymä tai toteutus. Integroidaan ainakin ensimmäisen sijan lähetys pääsivustollemme, ja ehkä joitakin muita. Olemme myös alkaneet miettiä, miten järjestää prosessi harvinaisimpien kirjojen tunnistamiseksi, vahvistamiseksi ja sitten arkistoimiseksi. Lisää tästä aiheesta myöhemmin. Kiitos kaikille osallistujille. On hämmästyttävää, että niin moni välittää. Helppo datasets-vaihtaminen nopeisiin vertailuihin. Kaikki ISBN:t CADAL SSNO:t CERLALC-tietovuoto DuXiu SSID:t EBSCOhostin eBook-indeksi Google Books Goodreads Internet Archive ISBNdb ISBN:n globaali kustantajarekisteri Libby Tiedostot Annan Arkistossa Nexus/STC OCLC/Worldcat OpenLibrary Venäjän valtion kirjasto Trantorin keisarillinen kirjasto Toinen sija 3 000 dollaria: hypha ”Vaikka täydelliset neliöt ja suorakulmiot ovat matemaattisesti miellyttäviä, ne eivät tarjoa parempaa paikallisuutta kartoituskontekstissa. Uskon, että näiden Hilbertin tai klassisen Mortonin epäsymmetria ei ole virhe vaan ominaisuus. Aivan kuten Italian kuuluisasti saappaanmuotoinen ääriviiva tekee siitä heti tunnistettavan kartalla, näiden käyrien ainutlaatuiset "omituisuudet" voivat toimia kognitiivisina maamerkkeinä. Tämä erottuvuus voi parantaa tilamuistia ja auttaa käyttäjiä orientoitumaan, mikä voi helpottaa tiettyjen alueiden löytämistä tai kuvioiden huomaamista.” Toinen uskomaton <a %(annas_archive_note_2913)s>osallistuminen</a>. Ei yhtä joustava kuin ensimmäinen sija, mutta pidimme itse asiassa sen makrotason visualisoinnista enemmän kuin ensimmäisestä sijasta (tilaa täyttävä käyrä, rajat, merkinnät, korostukset, panorointi ja zoomaus). Joe Davisin <a %(annas_archive_note_2971)s>kommentti</a> resonoi kanssamme: Ja silti paljon vaihtoehtoja visualisointiin ja renderöintiin sekä uskomattoman sujuva ja intuitiivinen käyttöliittymä. Vahva toinen sija! - Anna ja tiimi (<a %(reddit)s>Reddit</a>) Muutama kuukausi sitten ilmoitimme <a %(all_isbns)s>10 000 dollarin palkinnosta</a> parhaan mahdollisen visualisoinnin tekemiseksi datastamme, joka näyttää ISBN-tilan. Korostimme, mitkä tiedostot olemme jo arkistoineet ja mitkä emme, ja myöhemmin datasetin, joka kuvaa, kuinka monella kirjastolla on ISBN:itä (harvinaisuuden mittari). Olemme olleet häkeltyneitä vastauksista. Luovuutta on ollut niin paljon. Suuri kiitos kaikille osallistuneille: energianne ja innostuksenne ovat tarttuvia! Lopulta halusimme vastata seuraaviin kysymyksiin: <strong>mitkä kirjat ovat olemassa maailmassa, kuinka monta olemme jo arkistoineet, ja mihin kirjoihin meidän tulisi keskittyä seuraavaksi?</strong> On hienoa nähdä, että niin monet ihmiset välittävät näistä kysymyksistä. Aloitimme itse perusvisualisoinnilla. Alle 300 kt:n kokoisena tämä kuva edustaa ytimekkäästi suurinta täysin avointa "kirjalistaa", joka on koskaan koottu ihmiskunnan historiassa: Kolmas sija 500 dollaria #1: maxlion Tässä <a %(annas_archive_note_2940)s>osallistumisessa</a> pidimme todella erilaisista näkymistä, erityisesti vertailu- ja julkaisijanäkymistä. Kolmas sija 500 dollaria #2: abetusk Vaikka käyttöliittymä ei olekaan kaikkein hiotuin, tämä <a %(annas_archive_note_2917)s>osallistuminen</a> täyttää monia vaatimuksia. Pidimme erityisesti sen vertailuominaisuudesta. Kolmas sija 500 dollaria #3: conundrumer0 Kuten ensimmäinen sija, tämä <a %(annas_archive_note_2975)s>osallistuminen</a> teki meihin vaikutuksen joustavuudellaan. Lopulta tämä tekee erinomaisesta visualisointityökalusta: maksimaalinen joustavuus tehokäyttäjille, samalla kun asiat pidetään yksinkertaisina tavallisille käyttäjille. Kolmas sija 500 dollaria #4: charelf Viimeinen <a %(annas_archive_note_2947)s>osallistuminen</a>, joka saa palkkion, on melko perus, mutta siinä on joitain ainutlaatuisia ominaisuuksia, joista pidimme todella. Pidimme siitä, kuinka he näyttävät, kuinka monta datasettiä kattaa tietyn ISBN:n suosion/luotettavuuden mittarina. Pidimme myös todella yksinkertaisuudesta mutta tehokkuudesta käyttää läpinäkyvyyssäädintä vertailuihin. 10 000 dollarin ISBN-visualisointipalkinnon voittajat TL;DR: Saimme uskomattomia osallistumisia 10 000 dollarin ISBN-visualisointipalkintoon. Tausta Miten Annan Arkisto voi saavuttaa tavoitteensa varmuuskopioida koko ihmiskunnan tieto, ilman tietoa siitä, mitkä kirjat ovat vielä olemassa? Tarvitsemme TODO-listan. Yksi tapa kartoittaa tämä on ISBN-numeroiden avulla, jotka 1970-luvulta lähtien on annettu jokaiselle julkaistulle kirjalle (useimmissa maissa). Ei ole olemassa keskitettyä viranomaista, joka tietäisi kaikki ISBN-määritykset. Sen sijaan se on hajautettu järjestelmä, jossa maat saavat numeroalueita, jotka sitten jakavat pienempiä alueita suurille kustantajille, jotka voivat edelleen jakaa alueita pienemmille kustantajille. Lopulta yksittäiset numerot annetaan kirjoille. Aloitimme ISBN-numeroiden kartoittamisen <a %(blog)s>kaksi vuotta sitten</a> ISBNdb:n kaavinnalla. Sen jälkeen olemme kaavineet monia muita metadata-lähteitä, kuten <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ja muita. Täydellinen lista löytyy Annan Arkiston "Datasets" ja "Torrents" -sivuilta. Meillä on nyt ylivoimaisesti maailman suurin täysin avoin, helposti ladattava kokoelma kirjametadataa (ja siten ISBN-numeroita). Olemme <a %(blog)s>kirjoittaneet laajasti</a> siitä, miksi välitämme säilyttämisestä ja miksi olemme tällä hetkellä kriittisessä vaiheessa. Meidän on nyt tunnistettava harvinaiset, aliarvostetut ja ainutlaatuisesti vaarassa olevat kirjat ja säilytettävä ne. Hyvä metadata kaikista maailman kirjoista auttaa tässä. 10 000 dollarin palkkio Käytettävyyteen ja ulkonäköön kiinnitetään erityistä huomiota. Näytä yksittäisten ISBN-numeroiden todellinen metadata, kuten nimi ja tekijä, kun zoomaat sisään. Parempi tilan täyttävä käyrä. Esimerkiksi siksak, joka menee ensimmäisellä rivillä 0:sta 4:ään ja sitten takaisin (käänteisesti) toisella rivillä 5:stä 9:ään — sovellettuna rekursiivisesti. Eri tai muokattavat värimallit. Erityisnäkymät datasetsien vertailuun. Tapoja debugata ongelmia, kuten muuta metadataa, joka ei sovi hyvin yhteen (esim. huomattavan erilaiset otsikot). Kuvien annotointi kommenteilla ISBN-numeroista tai alueista. Mitkä tahansa heuristiikat harvinaisten tai riskialttiiden kirjojen tunnistamiseen. Mitä luovia ideoita keksitkään! Koodi Koodi näiden kuvien luomiseen sekä muita esimerkkejä löytyy <a %(annas_archive)s>tästä hakemistosta</a>. Keksimme kompaktin tiedostomuodon, jolla kaikki tarvittavat ISBN-tiedot ovat noin 75MB (pakattuna). Tiedostomuodon kuvaus ja koodi sen luomiseen löytyy <a %(annas_archive_l1244_1319)s>täältä</a>. Palkkion saamiseksi sinun ei tarvitse käyttää tätä, mutta se on luultavasti kätevin tapa aloittaa. Voit muokata metadataamme haluamallasi tavalla (vaikka kaiken koodisi on oltava avoimen lähdekoodin). Emme malta odottaa, mitä keksit. Onnea matkaan! Haarauta tämä repo ja muokkaa tätä blogipostauksen HTML:ää (muita taustajärjestelmiä kuin meidän Flask-taustajärjestelmää ei sallita). Tee yllä olevasta kuvasta sujuvasti zoomattava, jotta voit zoomata yksittäisiin ISBN-numeroihin asti. ISBN-numeroiden klikkaaminen vie sinut metadata-sivulle tai hakuun Annan Arkistossa. Sinun on edelleen voitava vaihtaa kaikkien eri datasetien välillä. Maa- ja kustantaja-alueet tulisi korostaa, kun niiden päälle viedään hiiri. Voit käyttää esimerkiksi <a %(github_xlcnd_isbnlib)s>data4info.py:tä isbnlibissä</a> maa-tietoja varten ja meidän "isbngrp"-kaavintaamme kustantajille (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Sen on toimittava hyvin sekä työpöydällä että mobiililaitteilla. Täällä on paljon tutkittavaa, joten julkaisemme palkkion yllä olevan visualisoinnin parantamiseksi. Toisin kuin useimmat palkkiomme, tämä on aikarajoitettu. Sinun on <a %(annas_archive)s>lähetettävä</a> avoimen lähdekoodin koodisi viimeistään 2025-01-31 (23:59 UTC). Paras lähetys saa 6 000 dollaria, toiseksi paras 3 000 dollaria ja kolmanneksi paras 1 000 dollaria. Kaikki palkkiot maksetaan Monerolla (XMR). Alla ovat vähimmäiskriteerit. Jos mikään lähetys ei täytä kriteerejä, voimme silti myöntää joitakin palkkioita, mutta se on harkintamme mukaan. Lisäpisteitä (nämä ovat vain ideoita — anna luovuutesi lentää): Voit täysin poiketa vähimmäiskriteereistä ja tehdä täysin erilaisen visualisoinnin. Jos se on todella upea, se voi oikeuttaa palkkioon, mutta harkintamme mukaan. Tee lähetykset kommentoimalla <a %(annas_archive)s>tätä asiaa</a> linkillä haarukoituun repoosi, yhdistämispyyntöön tai eroon. - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Tämä kuva on 1000×800 pikseliä. Jokainen pikseli edustaa 2 500 ISBN-numeroa. Jos meillä on tiedosto ISBN:lle, teemme pikselistä vihreämmän. Jos tiedämme, että ISBN on myönnetty, mutta meillä ei ole vastaavaa tiedostoa, teemme siitä punaisemman. Alle 300 kt:ssa tämä kuva tiivistää suurimman täysin avoimen "kirjalistan", joka on koskaan koottu ihmiskunnan historiassa (muutama sata gigatavua pakattuna kokonaisuudessaan). Se osoittaa myös: kirjojen varmuuskopioinnissa on vielä paljon työtä jäljellä (meillä on vain 16%). Kaikkien ISBN-numeroiden visualisointi — 10 000 dollarin palkkio 31.1.2025 mennessä Tämä kuva edustaa suurinta täysin avointa "kirjalistaa", joka on koskaan koottu ihmiskunnan historiassa. Visualisointi Yleiskuvan lisäksi voimme tarkastella myös yksittäisiä hankkimiamme datasettejä. Käytä avattavaa valikkoa ja painikkeita vaihtaaksesi niiden välillä. Näissä kuvissa on paljon mielenkiintoisia kuvioita. Miksi on olemassa jonkinlaista säännöllisyyttä viivoissa ja lohkoissa, joka näyttää tapahtuvan eri mittakaavoissa? Mitä ovat tyhjät alueet? Miksi tietyt datasetit ovat niin ryhmittyneitä? Jätämme nämä kysymykset lukijan pohdittavaksi. - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Johtopäätös Tämän standardin avulla voimme tehdä julkaisuja asteittain ja lisätä uusia tietolähteitä helpommin. Meillä on jo muutamia jännittäviä julkaisuja tulossa! Toivomme myös, että muiden varjokirjastojen on helpompi peilata kokoelmiamme. Loppujen lopuksi tavoitteemme on säilyttää ihmiskunnan tieto ja kulttuuri ikuisesti, joten mitä enemmän redundanssia, sen parempi. Esimerkki Katsotaanpa viimeisintä Z-Library-julkaisua esimerkkinä. Se koostuu kahdesta kokoelmasta: ”<span style="background: #fffaa3">zlib3_records</span>” ja ”<span style="background: #ffd6fe">zlib3_files</span>”. Tämä mahdollistaa metadata-tietueiden erillisen keräämisen ja julkaisemisen varsinaisista kirjatiedostoista. Näin ollen julkaisimme kaksi torrenttia metadata-tiedostoilla: Julkaisimme myös joukon torrentteja binääritietokansioilla, mutta vain ”<span style="background: #ffd6fe">zlib3_files</span>” -kokoelmalle, yhteensä 62: Suorittamalla <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> voimme nähdä, mitä sisällä on: Tässä tapauksessa se on kirjan metadata, kuten Z-Library raportoi. Ylimmällä tasolla meillä on vain ”aacid” ja ”metadata”, mutta ei ”data_folder”, koska vastaavaa binääritietoa ei ole. AACID sisältää ”22430000” ensisijaisena tunnisteena, jonka voimme nähdä olevan otettu ”zlibrary_id”:stä. Voimme odottaa muiden AAC:iden tässä kokoelmassa olevan saman rakenteen mukaisia. Nyt suoritetaan <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Tämä on paljon pienempi AAC-metadata, vaikka suurin osa tästä AAC:sta sijaitsee muualla binääritiedostossa! Meillä on tällä kertaa ”data_folder”, joten voimme odottaa vastaavan binääritiedon sijaitsevan osoitteessa <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. ”Metadata” sisältää ”zlibrary_id”:n, joten voimme helposti yhdistää sen vastaavaan AAC:hen ”zlib_records” -kokoelmassa. Voisimme yhdistää useilla eri tavoilla, esimerkiksi AACID:n kautta — standardi ei määrää sitä. Huomaa, että ”metadata”-kentän ei myöskään tarvitse olla JSON. Se voi olla merkkijono, joka sisältää XML:ää tai mitä tahansa muuta tiedostomuotoa. Voit jopa tallentaa metadata-tiedot liitettyyn binäärilohkoon, esimerkiksi jos se on paljon tietoa. Heterogeeniset tiedostot ja metadata, mahdollisimman lähellä alkuperäistä muotoa. Binaaritietoja voidaan palvella suoraan verkkopalvelimilla, kuten Nginx. Heterogeeniset tunnisteet lähdekirjastoissa tai jopa tunnisteiden puuttuminen. Erilliset metadatan ja tiedostodatan julkaisut tai pelkät metadatan julkaisut (esim. ISBNdb-julkaisu). Jakelu torrenttien kautta, mutta mahdollisuus myös muihin jakelumenetelmiin (esim. IPFS). Muuttumattomat tietueet, koska meidän tulisi olettaa, että torrenttimme elävät ikuisesti. Inkrementaaliset julkaisut / lisättävät julkaisut. Koneellisesti luettavissa ja kirjoitettavissa, kätevästi ja nopeasti, erityisesti meidän pinollemme (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Jossain määrin helppo ihmisen tarkastaa, vaikka tämä on toissijaista koneen luettavuuteen nähden. Helppo kylvää kokoelmiamme standardilla vuokratulla seedboxilla. Suunnittelutavoitteet Emme välitä siitä, että tiedostoja olisi helppo navigoida manuaalisesti levyllä tai että ne olisivat haettavissa ilman esikäsittelyä. Emme välitä siitä, että olisimme suoraan yhteensopivia olemassa olevan kirjastosoftan kanssa. Vaikka kenen tahansa pitäisi olla helppo kylvää kokoelmaamme torrenttien avulla, emme odota, että tiedostot olisivat käytettävissä ilman merkittävää teknistä tietämystä ja sitoutumista. Ensisijainen käyttötapauksemme on tiedostojen ja niihin liittyvän metadatan jakelu eri olemassa olevista kokoelmista. Tärkeimmät huomioitavat asiat ovat: Joitakin ei-tavoitteita: Koska Annan Arkisto on avoimen lähdekoodin, haluamme käyttää omaa formaattiamme suoraan. Kun päivitämme hakemistoamme, käytämme vain julkisesti saatavilla olevia polkuja, jotta kuka tahansa, joka haarauttaa kirjastomme, voi päästä nopeasti alkuun. <strong>AAC.</strong> AAC (Annin Arkiston Säiliö) on yksittäinen kohde, joka koostuu <strong>metadatasta</strong> ja valinnaisesti <strong>binaaritiedoista</strong>, jotka molemmat ovat muuttumattomia. Sillä on maailmanlaajuisesti yksilöllinen tunniste, jota kutsutaan <strong>AACID:ksi</strong>. <strong>AACID.</strong> AACID:n muoto on seuraava: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Esimerkiksi, yksi julkaisemamme AACID on <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-alue.</strong> Koska AACID:t sisältävät monotonisesti kasvavia aikaleimoja, voimme käyttää niitä merkitsemään alueita tietyn kokoelman sisällä. Käytämme tätä muotoa: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, jossa aikaleimat ovat inklusiivisia. Tämä on johdonmukaista ISO 8601 -merkinnän kanssa. Alueet ovat jatkuvia ja voivat mennä päällekkäin, mutta päällekkäisyyden tapauksessa niiden on sisällettävä samat tietueet kuin aiemmin julkaistussa kokoelmassa (koska AAC:t ovat muuttumattomia). Puuttuvia tietueita ei sallita. <code>{collection}</code>: kokoelman nimi, joka voi sisältää ASCII-kirjaimia, numeroita ja alaviivoja (mutta ei kaksoisalaviivoja). <code>{collection-specific ID}</code>: kokoelmakohtainen tunniste, jos sovellettavissa, esim. Z-Library ID. Voidaan jättää pois tai lyhentää. On jätettävä pois tai lyhennettävä, jos AACID muuten ylittäisi 150 merkkiä. <code>{ISO 8601 timestamp}</code>: lyhyt versio ISO 8601:stä, aina UTC-ajassa, esim. <code>20220723T194746Z</code>. Tämän numeron on kasvettava monotonisesti jokaisessa julkaisussa, vaikka sen tarkka merkitys voi vaihdella kokoelman mukaan. Suosittelemme käyttämään kaavinnan tai ID:n luomisen aikaa. <code>{shortuuid}</code>: UUID, mutta pakattu ASCII:ksi, esim. base57:ää käyttäen. Käytämme tällä hetkellä <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-kirjastoa. <strong>Binaaritietokansio.</strong> Kansio, joka sisältää AAC:iden binaaritiedot yhdelle tietylle kokoelmalle. Näillä on seuraavat ominaisuudet: Hakemiston on sisällettävä datatiedostot kaikille AAC:ille määritellyllä alueella. Jokaisen datatiedoston on oltava nimetty AACID:n mukaan (ei laajennuksia). Hakemistonimen on oltava AACID-alue, jonka etuliitteenä on <code style="color: green">annas_archive_data__</code>, eikä mitään jälkiliitettä. Esimerkiksi yksi todellisista julkaisuistamme on hakemisto nimeltä<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. On suositeltavaa tehdä näistä kansioista kohtuullisen hallittavia kooltaan, esimerkiksi enintään 100GB-1TB kukin, vaikka tämä suositus saattaa muuttua ajan myötä. <strong>Kokoelma.</strong> Jokainen AAC kuuluu kokoelmaan, joka määritelmän mukaan on semanttisesti johdonmukaisten AAC:iden lista. Tämä tarkoittaa, että jos teet merkittävän muutoksen metadatan muotoon, sinun on luotava uusi kokoelma. Standardi <strong>Metadata-tiedosto.</strong> Metadata-tiedosto sisältää AAC:iden metadatan yhdelle tietylle kokoelmalle. Näillä on seuraavat ominaisuudet: <code>data_folder</code> on valinnainen, ja se on binaaritietokansion nimi, joka sisältää vastaavat binaaritiedot. Vastaavan binaaritiedoston nimi kyseisessä kansiossa on tietueen AACID. Jokaisen JSON-objektin on sisällettävä seuraavat kentät ylimmällä tasolla: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valinnainen). Muita kenttiä ei sallita. Tiedostonimen on oltava AACID-alue, jonka etuliitteenä on <code style="color: red">annas_archive_meta__</code> ja jota seuraa <code>.jsonl.zstd</code>. Esimerkiksi yksi julkaisuistamme on nimeltään<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kuten tiedostopääte osoittaa, tiedostotyyppi on <a %(jsonlines)s>JSON Lines</a>, pakattu <a %(zstd)s>Zstandard</a>:illa. <code>metadata</code> on mielivaltainen metadata, kokoelman semantiikan mukaisesti. Sen on oltava semanttisesti johdonmukainen kokoelman sisällä. <code style="color: red">annas_archive_meta__</code> -etuliitettä voidaan mukauttaa laitoksenne nimeen, esim. <code style="color: red">my_institute_meta__</code>. <strong>“tietueet” ja “tiedostot” kokoelmat.</strong> Käytännön mukaan on usein kätevää julkaista “tietueet” ja “tiedostot” eri kokoelmina, jotta ne voidaan julkaista eri aikatauluilla, esim. kaavintanopeuksien perusteella. “Tietue” on vain metadataa sisältävä kokoelma, joka sisältää tietoja kuten kirjan nimet, tekijät, ISBN:t jne., kun taas “tiedostot” ovat kokoelmia, jotka sisältävät varsinaiset tiedostot (pdf, epub). Lopulta päädyimme suhteellisen yksinkertaiseen standardiin. Se on melko väljä, ei-normatiivinen ja kehityksen alla. <strong>Torrentit.</strong> Metadata-tiedostot ja binääritietokansiot voidaan niputtaa torrenteiksi, yksi torrent per metadata-tiedosto tai yksi torrent per binääritietokansio. Torrenttien tiedostonimenä on oltava alkuperäinen tiedosto/hakemistonimi plus <code>.torrent</code>-pääte. <a %(wikipedia_annas_archive)s>Annan Arkisto</a> on ylivoimaisesti maailman suurin varjokirjasto ja ainoa tämän mittakaavan varjokirjasto, joka on täysin avoimen lähdekoodin ja avoimen datan. Alla on taulukko Datasets-sivultamme (hieman muokattuna): Saavutimme tämän kolmella tavalla: Peilaamalla olemassa olevia avoimen datan varjokirjastoja (kuten Sci-Hub ja Library Genesis). Auttaen varjokirjastoja, jotka haluavat olla avoimempia, mutta joilla ei ollut aikaa tai resursseja tehdä niin (kuten Libgenin sarjakuvakokoelma). Scrapaten kirjastoja, jotka eivät halua jakaa suuria määriä (kuten Z-Library). (2) ja (3) varten hallitsemme nyt huomattavaa kokoelmaa torrentteja itse (satoja teratavuja). Tähän asti olemme lähestyneet näitä kokoelmia yksittäisinä tapauksina, mikä tarkoittaa räätälöityä infrastruktuuria ja tiedon järjestämistä jokaiselle kokoelmalle. Tämä lisää merkittävästi jokaisen julkaisun työmäärää ja tekee erityisen vaikeaksi tehdä enemmän inkrementaalisia julkaisuja. Siksi päätimme standardoida julkaisumme. Tämä on tekninen blogikirjoitus, jossa esittelemme standardimme: <strong>Annan Arkiston Kontit</strong>. Annan Arkiston Kontit (AAC): maailman suurimman varjokirjaston julkaisujen standardointi Annan Arkistosta on tullut maailman suurin varjokirjasto, mikä vaatii meitä standardoimaan julkaisumme. Yli 300GB kirjan kansia julkaistu Lopuksi olemme iloisia voidessamme ilmoittaa pienestä julkaisusta. Yhteistyössä Libgen.rs-haaran ylläpitäjien kanssa jaamme kaikki heidän kirjankantensa torrenttien ja IPFS:n kautta. Tämä jakaa kansien katselun kuormituksen useammille koneille ja säilyttää ne paremmin. Monissa (mutta ei kaikissa) tapauksissa kirjan kannet sisältyvät itse tiedostoihin, joten tämä on eräänlaista "johdettua dataa". Mutta niiden saaminen IPFS:ään on silti erittäin hyödyllistä sekä Annan Arkiston että eri Library Genesis -haarojen päivittäisessä toiminnassa. Kuten tavallista, löydät tämän julkaisun Pirate Library Mirrorista (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkistoon</a>). Emme linkitä siihen täällä, mutta löydät sen helposti. Toivottavasti voimme hieman hidastaa tahtia, nyt kun meillä on kunnollinen vaihtoehto Z-Kirjastolle. Tämä työmäärä ei ole erityisen kestävä. Jos olet kiinnostunut auttamaan ohjelmoinnissa, palvelimen ylläpidossa tai säilytystyössä, ota ehdottomasti yhteyttä meihin. Paljon <a %(annas_archive)s>työtä on vielä tehtävänä</a>. Kiitos mielenkiinnostasi ja tuestasi. Siirtyminen ElasticSearchiin Jotkut kyselyt kestivät todella kauan, siihen pisteeseen asti, että ne veivät kaikki avoimet yhteydet. Oletuksena MySQL:ssä on vähimmäissanapituus, tai indeksi voi kasvaa todella suureksi. Ihmiset raportoivat, etteivät he pystyneet hakemaan "Ben Hur". Haku oli vain jonkin verran nopea, kun se oli täysin ladattu muistiin, mikä vaati meitä hankkimaan kalliimman koneen tämän ajamiseen, sekä joitakin komentoja indeksin esilataamiseen käynnistyksessä. Emme olisi voineet laajentaa sitä helposti uusien ominaisuuksien rakentamiseen, kuten parempaan <a %(wikipedia_cjk_characters)s>tokenisointiin ei-välilyönnillisille kielille</a>, suodattamiseen/fasetoimiseen, lajitteluun, "tarkoititko" -ehdotuksiin, automaattiseen täydennykseen ja niin edelleen. Yksi <a %(annas_archive)s>lipuistamme</a> oli kokoelma ongelmia hakujärjestelmämme kanssa. Käytimme MySQL:n täysimittaista hakua, koska kaikki datamme oli joka tapauksessa MySQL:ssä. Mutta sillä oli rajansa: Keskusteltuamme useiden asiantuntijoiden kanssa päädyimme ElasticSearchiin. Se ei ole ollut täydellinen (heidän oletus "tarkoititko" -ehdotuksensa ja automaattisen täydennyksen ominaisuudet ovat huonoja), mutta kaiken kaikkiaan se on ollut paljon parempi kuin MySQL hakua varten. Emme ole vielä <a %(youtube)s>liian innostuneita</a> käyttämään sitä mihinkään kriittiseen dataan (vaikka he ovat tehneet paljon <a %(elastic_co)s>edistystä</a>), mutta kaiken kaikkiaan olemme melko tyytyväisiä siirtymään. Toistaiseksi olemme toteuttaneet paljon nopeamman haun, paremman kielituen, paremman relevanssilajittelun, erilaisia lajitteluvaihtoehtoja ja suodatuksen kielen/kirjatyypin/tiedostotyypin mukaan. Jos olet utelias, miten se toimii, <a %(annas_archive_l140)s>katso</a> <a %(annas_archive_l1115)s>tätä</a> <a %(annas_archive_l1635)s>tarkemmin</a>. Se on melko helposti lähestyttävä, vaikka se kaipaisi lisää kommentteja… Annan Arkisto on täysin avoimen lähdekoodin Uskomme, että tiedon tulisi olla vapaata, eikä oma koodimme ole poikkeus. Olemme julkaisseet kaiken koodimme yksityisesti isännöidyssä Gitlab-instanssissamme: <a %(annas_archive)s>Annan Ohjelmisto</a>. Käytämme myös ongelmaseurantaa työmme järjestämiseen. Jos haluat osallistua kehitykseemme, tämä on loistava paikka aloittaa. Antaaksemme maistiaisen siitä, mitä olemme työstämässä, katsokaa viimeaikaista työtämme asiakaspuolen suorituskyvyn parantamiseksi. Koska emme ole vielä toteuttaneet sivutusta, palautamme usein erittäin pitkiä hakusivuja, joissa on 100-200 tulosta. Emme halunneet katkaista hakutuloksia liian aikaisin, mutta tämä hidasti joitakin laitteita. Tätä varten toteutimme pienen tempun: kääritimme suurimman osan hakutuloksista HTML-kommentteihin (<code><!-- --></code>), ja sitten kirjoitimme pienen Javascriptin, joka tunnistaa, milloin tulos pitäisi tulla näkyviin, jolloin purimme kommentin: DOM "virtualisointi" toteutettu 23 rivillä, ei tarvetta hienoille kirjastoille! Tämä on sellaista nopeaa ja käytännöllistä koodia, jota syntyy, kun aika on rajallista ja todellisia ongelmia on ratkaistavana. On raportoitu, että hakumme toimii nyt hyvin hitailla laitteilla! Toinen suuri ponnistus oli tietokannan rakentamisen automatisointi. Kun aloitimme, yhdistelimme vain satunnaisesti eri lähteitä. Nyt haluamme pitää ne ajan tasalla, joten kirjoitimme joukon skriptejä, jotka lataavat uutta metadataa kahdesta Library Genesis -haarasta ja integroivat ne. Tavoitteena ei ole vain tehdä tästä hyödyllistä arkistollemme, vaan myös helpottaa asioita kaikille, jotka haluavat leikkiä varjokirjaston metadatan kanssa. Tavoitteena olisi Jupyter-muistikirja, jossa on saatavilla kaikenlaista mielenkiintoista metadataa, jotta voimme tehdä enemmän tutkimusta, kuten selvittää, mikä <a %(blog)s>prosenttiosuus ISBN-numeroista säilyy ikuisesti</a>. Lopuksi uudistimme lahjoitusjärjestelmämme. Voit nyt käyttää luottokorttia tallettamaan rahaa suoraan kryptolompakoihimme, ilman että sinun tarvitsee tietää mitään kryptovaluutoista. Jatkamme tämän toimivuuden seurantaa käytännössä, mutta tämä on iso juttu. Kun Z-Library kaatui ja sen (väitetyt) perustajat pidätettiin, olemme työskennelleet yötä päivää tarjotaksemme hyvän vaihtoehdon Annan Arkiston kanssa (emme linkitä sitä tähän, mutta voit etsiä sen Googlesta). Tässä on joitakin asioita, joita olemme saavuttaneet äskettäin. Annan päivitys: täysin avoimen lähdekoodin arkisto, ElasticSearch, yli 300GB kirjan kansia Olemme työskennelleet yötä päivää tarjotaksemme hyvän vaihtoehdon Annan Arkiston kanssa. Tässä on joitakin asioita, joita olemme saavuttaneet äskettäin. Analyysi Semanttiset kaksoiskappaleet (eri skannaukset samasta kirjasta) voidaan teoriassa suodattaa pois, mutta se on hankalaa. Kun katsoimme sarjakuvia manuaalisesti, löysimme liian monta väärää positiivista. On joitakin kaksoiskappaleita pelkästään MD5:n perusteella, mikä on suhteellisen tuhlaavaa, mutta niiden suodattaminen pois antaisi meille vain noin 1% in säästön. Tässä mittakaavassa se on silti noin 1TB, mutta myös tässä mittakaavassa 1TB ei oikeastaan merkitse. Emme halua riskeerata tietojen tuhoamista vahingossa tässä prosessissa. Löysimme joukon ei-kirjallista dataa, kuten sarjakuviin perustuvia elokuvia. Se vaikuttaa myös tuhlaavalta, koska nämä ovat jo laajalti saatavilla muilla keinoilla. Kuitenkin huomasimme, ettemme voineet vain suodattaa pois elokuvatiedostoja, koska on myös <em>interaktiivisia sarjakuvakirjoja</em>, jotka julkaistiin tietokoneella, ja joku tallensi ja tallensi ne elokuvina. Lopulta kaikki, mitä voisimme poistaa kokoelmasta, säästäisi vain muutaman prosentin. Sitten muistimme, että olemme datankerääjiä, ja ne, jotka peilaavat tätä, ovat myös datankerääjiä, joten, "MITÄ TARKOITAT, POISTAA?!" :) Kun saat 95TB dataa tallennusklusteriisi, yrität ymmärtää, mitä siellä edes on… Teimme analyysin nähdäksemme, voisimmeko pienentää kokoa hieman, esimerkiksi poistamalla kaksoiskappaleita. Tässä on joitakin havaintojamme: Esittelemme teille siis koko, muokkaamattoman kokoelman. Se on paljon dataa, mutta toivomme, että tarpeeksi moni välittää siitä jaettavaksi. Yhteistyö Kokonsa vuoksi tämä kokoelma on ollut pitkään toivelistallamme, joten Z-Libraryn varmuuskopioinnin onnistumisen jälkeen suuntasimme katseemme tähän kokoelmaan. Aluksi keräsimme sen suoraan, mikä oli melkoinen haaste, sillä heidän palvelimensa ei ollut parhaassa kunnossa. Saimme tällä tavalla noin 15TB, mutta se eteni hitaasti. Onneksi onnistuimme saamaan yhteyden kirjaston ylläpitäjään, joka suostui lähettämään meille kaikki tiedot suoraan, mikä oli paljon nopeampaa. Silti kesti yli puoli vuotta siirtää ja käsitellä kaikki tiedot, ja olimme lähellä menettää ne kaikki levyn vioittumisen vuoksi, mikä olisi tarkoittanut alusta aloittamista. Tämä kokemus on saanut meidät uskomaan, että on tärkeää saada nämä tiedot liikkeelle mahdollisimman nopeasti, jotta ne voidaan peilata laajalle. Olemme vain yhden tai kahden huonosti ajoitetun tapahtuman päässä menettämästä tätä kokoelmaa ikuisesti! Kokoelma Nopea eteneminen tarkoittaa, että kokoelma on hieman järjestämätön… Katsotaanpa. Kuvittele, että meillä on tiedostojärjestelmä (joka todellisuudessa jaamme torrentteihin): Ensimmäinen hakemisto, <code>/repository</code>, on tämän järjestäytyneempi osa. Tämä hakemisto sisältää niin sanottuja "tuhannen hakemistoja": hakemistoja, joissa kussakin on tuhat tiedostoa, jotka on numeroitu tietokannassa. Hakemisto <code>0</code> sisältää tiedostoja, joiden comic_id on 0–999, ja niin edelleen. Tämä on sama järjestelmä, jota Library Genesis on käyttänyt kaunokirjallisuuden ja tietokirjallisuuden kokoelmissaan. Ajatuksena on, että jokainen "tuhannen hakemisto" muutetaan automaattisesti torrentiksi heti, kun se on täynnä. Kuitenkin Libgen.li-ylläpitäjä ei koskaan tehnyt torrentteja tälle kokoelmalle, joten tuhannen hakemistot muuttuivat todennäköisesti hankaliksi ja antoivat tilaa "lajittelemattomille hakemistoille". Nämä ovat <code>/comics0</code> kautta <code>/comics4</code>. Ne kaikki sisältävät ainutlaatuisia hakemistorakenteita, jotka todennäköisesti olivat järkeviä tiedostojen keräämiseen, mutta eivät enää meille. Onneksi metadata viittaa suoraan kaikkiin näihin tiedostoihin, joten niiden tallennusjärjestely levyllä ei oikeastaan ole merkityksellinen! Metadata on saatavilla MySQL-tietokannan muodossa. Tämä voidaan ladata suoraan Libgen.li-verkkosivustolta, mutta teemme sen myös saataville torrentissa, yhdessä oman taulukkomme kanssa, jossa on kaikki MD5-tarkistussummat. <q>Dr. Barbara Gordon yrittää kadottaa itsensä kirjaston arkiseen maailmaan…</q> Libgen-haarat Ensin hieman taustaa. Saatat tuntea Library Genesisin heidän valtavasta kirjavalikoimastaan. Harvemmat tietävät, että Library Genesisin vapaaehtoiset ovat luoneet muita projekteja, kuten laajan kokoelman aikakauslehtiä ja standardidokumentteja, täydellisen varmuuskopion Sci-Hubista (yhteistyössä Sci-Hubin perustajan, Alexandra Elbakyanin kanssa) ja valtavan kokoelman sarjakuvia. Jossain vaiheessa eri Library Genesis -peilien ylläpitäjät lähtivät omille teilleen, mikä johti nykyiseen tilanteeseen, jossa on useita eri "haaroja", jotka kaikki kantavat edelleen Library Genesis -nimeä. Libgen.li-haaralla on ainutlaatuisesti tämä sarjakuvakokoelma sekä laaja aikakauslehtikokoelma (jonka parissa työskentelemme myös). Varainkeruu Julkaisemme tämän datan suurina paloina. Ensimmäinen torrent on <code>/comics0</code>, jonka laitoimme yhteen valtavaan 12TB .tar-tiedostoon. Se on parempi kiintolevyllesi ja torrent-ohjelmistollesi kuin lukemattomat pienemmät tiedostot. Osana tätä julkaisua järjestämme varainkeruun. Tavoitteenamme on kerätä 20 000 dollaria kattamaan tämän kokoelman operatiiviset ja sopimuskustannukset sekä mahdollistamaan jatkuvat ja tulevat projektit. Meillä on joitakin <em>valtavia</em> työn alla. <em>Ketä tuen lahjoituksellani?</em> Lyhyesti: varmuuskopioimme kaiken ihmiskunnan tiedon ja kulttuurin ja teemme sen helposti saatavilla olevaksi. Kaikki koodimme ja datamme ovat avoimen lähdekoodin, olemme täysin vapaaehtoisvoimin toimiva projekti, ja olemme tallentaneet 125TB kirjoja tähän mennessä (Libgenin ja Scihubin olemassa olevien torrenttien lisäksi). Lopulta rakennamme vauhtipyörää, joka mahdollistaa ja kannustaa ihmisiä löytämään, skannaamaan ja varmuuskopioimaan kaikki maailman kirjat. Kirjoitamme pääsuunnitelmastamme tulevassa postauksessa. :) Jos lahjoitat 12 kuukauden "Amazing Archivist" -jäsenyyteen (780 dollaria), saat <strong>”adoptoida torrentin”</strong>, mikä tarkoittaa, että laitamme käyttäjänimesi tai viestisi yhden torrentin tiedostonimeen! Voit lahjoittaa menemällä <a %(wikipedia_annas_archive)s>Annan Arkisto</a> -sivustolle ja klikkaamalla "Lahjoita"-painiketta. Etsimme myös lisää vapaaehtoisia: ohjelmistoinsinöörejä, tietoturvatutkijoita, anonyymejä kauppiasasiantuntijoita ja kääntäjiä. Voit myös tukea meitä tarjoamalla hosting-palveluita. Ja tietenkin, jaa torrenttejamme! Kiitos kaikille, jotka ovat jo niin anteliaasti tukeneet meitä! Teette todella eron. Tässä ovat tähän mennessä julkaistut torrentit (käsittelemme vielä loput): Kaikki torrentit löytyvät <a %(wikipedia_annas_archive)s>Annan Arkisto</a> -sivustolta kohdasta "Datasets" (emme linkitä suoraan sinne, jotta linkkejä tähän blogiin ei poisteta Redditistä, Twitteristä jne.). Sieltä voit seurata linkkiä Tor-sivustolle. <a %(news_ycombinator)s>Keskustele Hacker Newsissä</a> Mitä seuraavaksi? Useat torrentit ovat erinomaisia pitkäaikaiseen säilytykseen, mutta eivät niinkään jokapäiväiseen käyttöön. Teemme yhteistyötä hosting-kumppaneiden kanssa saadaksemme kaiken tämän datan verkkoon (koska Annan Arkisto ei isännöi mitään suoraan). Tietenkin löydät nämä latauslinkit Annan Arkistosta. Kutsumme myös kaikkia tekemään jotain tämän datan kanssa! Auta meitä analysoimaan sitä paremmin, poistamaan päällekkäisyyksiä, laittamaan se IPFS:ään, muokkaamaan sitä, kouluttamaan tekoälymallejasi sillä ja niin edelleen. Se on kaikki sinun, ja emme malta odottaa, mitä teet sillä. Lopuksi, kuten aiemmin sanottu, meillä on vielä joitakin massiivisia julkaisuja tulossa (jos <em>joku</em> voisi <em>vahingossa</em> lähettää meille tietokannan <em>tietyn</em> ACS4-dumpin, tiedät mistä löytää meidät…), sekä vauhtipyörän rakentaminen kaikkien maailman kirjojen varmuuskopioimiseksi. Joten pysy kuulolla, olemme vasta alussa. - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Maailman suurin sarjakuvien varjokirjasto on todennäköisesti tietyn Library Genesis -haaran: Libgen.li:n hallussa. Sivustoa ylläpitävä yksi ylläpitäjä onnistui keräämään valtavan sarjakuvakokoelman, joka sisältää yli 2 miljoonaa tiedostoa, yhteensä yli 95TB. Kuitenkin, toisin kuin muut Library Genesis -kokoelmat, tämä ei ollut saatavilla massana torrenttien kautta. Voit vain käyttää näitä sarjakuvia yksitellen hänen hitaalla henkilökohtaisella palvelimellaan — yksi heikko kohta. Tähän päivään asti! Tässä julkaisussa kerromme lisää tästä kokoelmasta ja varainkeruustamme tämän työn tukemiseksi. Annan Arkisto on varmuuskopioinut maailman suurimman sarjakuvien varjokirjaston (95TB) — voit auttaa sen jakamisessa Maailman suurimmalla sarjakuvien varjokirjastolla oli yksi heikko kohta... tähän päivään asti. Varoitus: tämä blogikirjoitus on vanhentunut. Olemme päättäneet, että IPFS ei ole vielä valmis laajaan käyttöön. Linkitämme edelleen tiedostoihin IPFS:ssä Annan Arkistosta, kun mahdollista, mutta emme enää isännöi niitä itse, emmekä suosittele muita peilaamaan IPFS:n avulla. Katso Torrentit-sivumme, jos haluat auttaa kokoelmamme säilyttämisessä. 5 998 794 kirjan laittaminen IPFS:ään Kopioiden monistaminen Palataksemme alkuperäiseen kysymykseemme: miten voimme väittää säilyttävämme kokoelmiamme ikuisesti? Tärkein ongelma tässä on, että kokoelmamme on <a %(torrents_stats)s>kasvanut</a> nopeasti, kaappaamalla ja avaamalla massiivisia kokoelmia (lisäksi muiden avoimen datan varjokirjastojen, kuten Sci-Hubin ja Library Genesisin, jo tekemän upean työn päälle). Tämä datan kasvu vaikeuttaa kokoelmien peilaamista ympäri maailmaa. Datan tallentaminen on kallista! Mutta olemme optimistisia, erityisesti kun tarkastelemme seuraavia kolmea trendiä. Kokoelmiemme <a %(annas_archive_stats)s>kokonaiskoko</a> viime kuukausien aikana, jaoteltuna torrentin jakajien määrän mukaan. HDD-hintatrendit eri lähteistä (klikkaa nähdäksesi tutkimuksen). <a %(critical_window_chinese)s>Kiinalainen versio 中文版</a>, keskustele <a %(reddit)s>Redditissä</a>, <a %(news_ycombinator)s>Hacker Newsissä</a> 1. Olemme poimineet matalalla roikkuvat hedelmät Tämä seuraa suoraan yllä käsitellyistä prioriteeteistamme. Suosimme suurten kokoelmien vapauttamista ensin. Nyt kun olemme turvanneet joitakin maailman suurimmista kokoelmista, odotamme kasvumme olevan paljon hitaampaa. On yhä pitkä häntä pienempiä kokoelmia, ja uusia kirjoja skannataan tai julkaistaan joka päivä, mutta tahti on todennäköisesti paljon hitaampi. Saatamme silti kaksin- tai jopa kolminkertaistua kooltaan, mutta pidemmän ajan kuluessa. OCR-parannukset. Prioriteetit Tiede- ja insinööriohjelmistokoodi Kaikkien edellä mainittujen fiktiiviset tai viihteelliset versiot Maantieteellinen data (esim. kartat, geologiset tutkimukset) Yritysten tai hallitusten sisäinen data (vuodot) Mittausdata, kuten tieteelliset mittaukset, taloustiedot, yritysraportit Metadata-tietueet yleisesti (tietokirjallisuuden ja kaunokirjallisuuden; muiden medioiden, taiteen, ihmisten jne.; mukaan lukien arvostelut) Tietokirjat Tietokirjallisuuslehdet, sanomalehdet, käyttöoppaat Tietokirjallisuuden puheiden, dokumenttien, podcastien transkriptiot Orgaaninen data kuten DNA-sekvenssit, kasvien siemenet tai mikrobinäytteet Akatemialliset artikkelit, lehdet, raportit Tiede- ja insinöörisivustot, verkkokeskustelut Oikeudenkäyntien tai tuomioistuinmenettelyjen transkriptiot Ainutlaatuisesti tuhoutumisvaarassa (esim. sodan, rahoitusleikkausten, oikeusjuttujen tai poliittisen vainon vuoksi) Harvinaisia Ainutlaatuisesti aliarvostettuja Miksi välitämme niin paljon artikkeleista ja kirjoista? Jätetään syrjään perustavanlaatuinen uskomuksemme säilyttämiseen yleensä — saatamme kirjoittaa siitä toisen viestin. Miksi siis erityisesti artikkelit ja kirjat? Vastaus on yksinkertainen: <strong>informaatiotiheys</strong>. Tallennusmegatavua kohden kirjoitettu teksti tallentaa eniten tietoa kaikista medioista. Vaikka välitämme sekä tiedosta että kulttuurista, välitämme enemmän edellisestä. Yleisesti ottaen löydämme informaatiotiheyden ja säilyttämisen tärkeyden hierarkian, joka näyttää suunnilleen tältä: Tämän listan järjestys on jossain määrin mielivaltainen — useat kohdat ovat tasoissa tai tiimimme sisällä on erimielisyyksiä — ja todennäköisesti unohdamme joitain tärkeitä kategorioita. Mutta tämä on suurin piirtein, miten priorisoimme. Jotkut näistä kohteista ovat liian erilaisia muihin verrattuna, jotta meidän tarvitsisi huolehtia niistä (tai muut instituutiot ovat jo hoitaneet ne), kuten orgaaninen data tai maantieteellinen data. Mutta suurin osa tämän listan kohteista on meille oikeasti tärkeitä. Toinen suuri tekijä priorisoinnissamme on se, kuinka suuressa vaarassa tietty työ on. Keskitymme mieluummin teoksiin, jotka ovat: Lopuksi, välitämme mittakaavasta. Meillä on rajallisesti aikaa ja rahaa, joten käytämme mieluummin kuukauden 10 000 kirjan pelastamiseen kuin 1 000 kirjan — jos ne ovat suunnilleen yhtä arvokkaita ja vaarassa. <em><q>Menetettyä ei voida palauttaa; mutta pelastakaamme se, mikä on jäljellä: ei holveilla ja lukoilla, jotka estävät niitä pääsemästä yleisön silmiin ja käyttöön, tuomitsemalla ne ajan hukkaan, vaan monistamalla kopioita, jotka asettavat ne onnettomuuksien ulottumattomiin.</q></em><br>— Thomas Jefferson, 1791 Varjokirjastot Koodi voi olla avointa lähdekoodia Githubissa, mutta Githubia kokonaisuutena ei voida helposti peilata ja siten säilyttää (vaikka tässä tapauksessa useimmista koodivarastoista on riittävästi jaettuja kopioita) Metadata-tietueita voi vapaasti katsella Worldcat-verkkosivustolla, mutta niitä ei voi ladata suurina määrinä (ennen kuin <a %(worldcat_scrape)s>kaappasimme</a> ne) Reddit on ilmainen käyttää, mutta on äskettäin asettanut tiukkoja anti-scraping-toimenpiteitä, datanälkäisen LLM-koulutuksen vuoksi (lisää siitä myöhemmin) On monia organisaatioita, joilla on samanlaiset tehtävät ja prioriteetit. Itse asiassa on kirjastoja, arkistoja, laboratorioita, museoita ja muita instituutioita, joiden tehtävänä on tämänkaltaisen materiaalin säilyttäminen. Monet niistä ovat hyvin rahoitettuja, hallitusten, yksityishenkilöiden tai yritysten toimesta. Mutta niillä on yksi valtava sokea piste: oikeusjärjestelmä. Tässä piilee varjokirjastojen ainutlaatuinen rooli ja syy, miksi Anna’s Arkisto on olemassa. Voimme tehdä asioita, joita muut instituutiot eivät saa tehdä. Nyt, ei ole (usein) niin, että voisimme arkistoida materiaaleja, joita ei saa säilyttää muualla. Ei, monissa paikoissa on laillista rakentaa arkisto, joka sisältää mitä tahansa kirjoja, papereita, lehtiä ja niin edelleen. Mutta mitä lailliset arkistot usein kaipaavat, on <strong>redundanssi ja pitkäikäisyys</strong>. On olemassa kirjoja, joista on vain yksi kappale jossain fyysisessä kirjastossa. On olemassa metadata-tietueita, joita vartioi vain yksi yritys. On olemassa sanomalehtiä, jotka on säilytetty vain mikrofilmillä yhdessä arkistossa. Kirjastot voivat saada rahoitusleikkauksia, yritykset voivat mennä konkurssiin, arkistot voidaan pommittaa ja polttaa maan tasalle. Tämä ei ole hypoteettista — tämä tapahtuu koko ajan. Se, mitä voimme tehdä ainutlaatuisesti Annan Arkistossa, on tallentaa monia kopioita teoksista laajassa mittakaavassa. Voimme kerätä papereita, kirjoja, aikakauslehtiä ja muuta, ja jakaa niitä suurina määrinä. Tällä hetkellä teemme tämän torrenttien kautta, mutta tarkat teknologiat eivät ole tärkeitä ja ne muuttuvat ajan myötä. Tärkeintä on saada monia kopioita jaettua ympäri maailmaa. Tämä yli 200 vuotta vanha lainaus on yhä ajankohtainen: Pieni huomautus julkisesta omistuksesta. Koska Annan Arkisto keskittyy ainutlaatuisesti toimintoihin, jotka ovat laittomia monissa paikoissa ympäri maailmaa, emme vaivaudu laajasti saatavilla olevien kokoelmien, kuten julkisen omistuksen kirjojen, kanssa. Lailliset tahot huolehtivat usein jo hyvin niistä. On kuitenkin seikkoja, jotka saavat meidät joskus työskentelemään julkisesti saatavilla olevien kokoelmien parissa: - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Tallennuskustannukset jatkavat eksponentiaalista laskuaan 3. Parannuksia tietotiheydessä Tällä hetkellä säilytämme kirjoja niiden alkuperäisissä muodoissa, joissa ne meille annetaan. Ne ovat toki pakattuja, mutta usein ne ovat silti suuria skannauksia tai valokuvia sivuista. Tähän asti ainoat vaihtoehdot kokoelmamme koon pienentämiseksi ovat olleet aggressiivisempi pakkaus tai deduplikointi. Kuitenkin merkittävien säästöjen saavuttamiseksi molemmat ovat liian häviöllisiä makuumme. Valokuvien voimakas pakkaus voi tehdä tekstistä tuskin luettavaa. Ja deduplikointi vaatii suurta varmuutta siitä, että kirjat ovat täsmälleen samoja, mikä on usein liian epätarkkaa, varsinkin jos sisältö on sama, mutta skannaukset on tehty eri aikoina. On aina ollut kolmas vaihtoehto, mutta sen laatu on ollut niin surkea, ettemme ole koskaan harkinneet sitä: <strong>OCR eli optinen merkkien tunnistus</strong>. Tämä on prosessi, jossa valokuvat muunnetaan pelkäksi tekstiksi käyttämällä tekoälyä valokuvien merkkien tunnistamiseen. Työkaluja tähän on ollut olemassa jo pitkään, ja ne ovat olleet melko hyviä, mutta "melko hyvä" ei riitä säilytystarkoituksiin. Kuitenkin viimeaikaiset monimodaaliset syväoppimismallit ovat edistyneet erittäin nopeasti, vaikkakin edelleen korkeilla kustannuksilla. Odotamme sekä tarkkuuden että kustannusten paranevan dramaattisesti tulevina vuosina, siihen pisteeseen asti, että niiden soveltaminen koko kirjastoomme tulee realistiseksi. Kun tämä tapahtuu, säilytämme todennäköisesti edelleen alkuperäiset tiedostot, mutta lisäksi voisimme luoda paljon pienemmän version kirjastostamme, jota useimmat haluavat peilata. Juju on siinä, että pelkkä teksti itsessään pakkaantuu vielä paremmin ja on paljon helpompi deduplikoida, mikä antaa meille vielä enemmän säästöjä. Kaiken kaikkiaan ei ole epärealistista odottaa vähintään 5-10-kertaista vähennystä tiedostojen kokonaiskoossa, ehkä jopa enemmän. Jopa konservatiivisella 5-kertaisella vähennyksellä, puhuisimme <strong>1 000–3 000 dollarista 10 vuodessa, vaikka kirjastomme koko kolminkertaistuisi</strong>. Kirjoitushetkellä <a %(diskprices)s>levyjen hinnat</a> per TB ovat noin 12 dollaria uusille levyille, 8 dollaria käytetyille levyille ja 4 dollaria nauhalle. Jos olemme konservatiivisia ja tarkastelemme vain uusia levyjä, se tarkoittaa, että petatavun tallentaminen maksaa noin 12 000 dollaria. Jos oletamme, että kirjastomme kolminkertaistuu 900TB:stä 2,7PB:hen, se tarkoittaisi 32 400 dollaria koko kirjastomme peilaamiseen. Lisäämällä sähkön, muun laitteiston kustannukset ja niin edelleen, pyöristetään se 40 000 dollariin. Tai nauhalla enemmänkin 15 000–20 000 dollariin. Toisaalta <strong>15 000–40 000 dollaria koko ihmiskunnan tiedon summasta on edullista</strong>. Toisaalta on hieman jyrkkää odottaa valtavia määriä täydellisiä kopioita, varsinkin jos haluaisimme myös, että ihmiset jatkavat torrenttiensa jakamista muiden hyödyksi. Se on tänään. Mutta edistys etenee: Kiintolevyjen kustannukset per TB ovat kutakuinkin kolmanneksen laskeneet viimeisen 10 vuoden aikana, ja ne todennäköisesti jatkavat laskuaan samassa tahdissa. Nauha näyttää olevan samanlaisella kehityspolulla. SSD-hinnat laskevat vielä nopeammin, ja saattavat ohittaa HDD-hinnat vuosikymmenen loppuun mennessä. Jos tämä pitää paikkansa, niin 10 vuoden kuluttua saatamme katsoa vain 5 000–13 000 dollaria koko kokoelmamme peilaamiseen (1/3), tai jopa vähemmän, jos kasvamme vähemmän kooltaan. Vaikka se on yhä paljon rahaa, se on monien ihmisten saavutettavissa. Ja se saattaa olla vielä parempi seuraavan kohdan vuoksi… Annan Arkistossa meiltä kysytään usein, kuinka voimme väittää säilyttävämme kokoelmiamme ikuisesti, kun niiden kokonaiskoko on jo lähestymässä 1 petatavua (1000 TB) ja kasvaa edelleen. Tässä artikkelissa tarkastelemme filosofiaamme ja näemme, miksi seuraava vuosikymmen on kriittinen tehtävällemme säilyttää ihmiskunnan tieto ja kulttuuri. Kriittinen ikkuna Jos nämä ennusteet pitävät paikkansa, meidän <strong>tarvitsee vain odottaa pari vuotta</strong>, ennen kuin kokoelmaamme peilataan laajasti. Näin ollen, Thomas Jeffersonin sanoin, "asetettu onnettomuuksien ulottumattomiin". Valitettavasti LLM:ien tulo ja niiden datanälkäinen koulutus ovat saaneet monet tekijänoikeuksien haltijat puolustuskannalle. Vielä enemmän kuin he jo olivat. Monet verkkosivustot tekevät vaikeammaksi kaavinnan ja arkistoinnin, oikeusjutut lentelevät, ja samalla fyysiset kirjastot ja arkistot jäävät edelleen huomiotta. Voimme vain odottaa näiden trendien pahenevan, ja monien teosten katoavan kauan ennen kuin ne tulevat julkisiksi. <strong>Olemme säilytyksen vallankumouksen kynnyksellä, mutta <q>kadonnutta ei voida palauttaa.</q></strong> Meillä on kriittinen ikkuna noin 5-10 vuotta, jonka aikana on vielä melko kallista ylläpitää varjokirjastoa ja luoda monia peilejä ympäri maailmaa, ja jonka aikana pääsyä ei ole vielä täysin suljettu. Jos voimme ylittää tämän ikkunan, olemme todellakin säilyttäneet ihmiskunnan tiedon ja kulttuurin ikuisesti. Emme saa antaa tämän ajan mennä hukkaan. Emme saa antaa tämän kriittisen ikkunan sulkeutua. Mennään. Varjokirjastojen kriittinen ikkuna Kuinka voimme väittää säilyttävämme kokoelmiamme ikuisesti, kun ne ovat jo lähestymässä 1 PB:tä? Kokoelma Lisätietoa kokoelmasta. <a %(duxiu)s>Duxiu</a> on valtava tietokanta skannatuista kirjoista, jonka on luonut <a %(chaoxing)s>SuperStar Digital Library Group</a>. Suurin osa on akateemisia kirjoja, jotka on skannattu, jotta ne olisivat digitaalisesti saatavilla yliopistoille ja kirjastoille. Englanninkieliselle yleisöllemme <a %(library_princeton)s>Princeton</a> ja <a %(guides_lib_uw)s>Washingtonin yliopisto</a> tarjoavat hyviä yleiskatsauksia. On myös erinomainen artikkeli, joka antaa lisää taustatietoa: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (etsi se Annan Arkistosta). Duxiun kirjat on pitkään piratoitu Kiinan internetissä. Yleensä niitä myydään alle dollarilla jälleenmyyjien toimesta. Ne jaetaan tyypillisesti Kiinan vastineella Google Drivelle, jota on usein hakkeroitu mahdollistamaan suurempi tallennustila. Joitakin teknisiä yksityiskohtia löytyy <a %(github_duty_machine)s>täältä</a> ja <a %(github_821_github_io)s>täältä</a>. Vaikka kirjat on jaettu puolijulkisesti, niiden hankkiminen massana on melko vaikeaa. Tämä oli korkealla tehtävälistallamme, ja varasimme useita kuukausia kokopäiväistä työtä sitä varten. Kuitenkin, äskettäin uskomaton, mahtava ja lahjakas vapaaehtoinen otti meihin yhteyttä, kertoen tehneensä kaiken tämän työn jo — suurin kustannuksin. He jakoivat koko kokoelman kanssamme, odottamatta mitään vastineeksi, paitsi pitkäaikaisen säilytyksen takaamisen. Todella merkittävää. He suostuivat pyytämään apua tällä tavalla saadakseen kokoelman OCR:attua. Kokoelma sisältää 7 543 702 tiedostoa. Tämä on enemmän kuin Library Genesis -tietokannan tietokirjallisuus (noin 5,3 miljoonaa). Kokonaiskoko on noin 359TB (326TiB) nykyisessä muodossaan. Olemme avoimia muille ehdotuksille ja ideoille. Ota vain yhteyttä meihin. Tutustu Annan Arkistoon saadaksesi lisätietoa kokoelmistamme, säilytyspyrkimyksistämme ja siitä, miten voit auttaa. Kiitos! Esimerkkisivut Todistaaksesi meille, että sinulla on hyvä putki, tässä on joitakin esimerkkisivuja, joista voit aloittaa, kirjasta, joka käsittelee suprajohteita. Putkesi tulisi käsitellä oikein matematiikkaa, taulukoita, kaavioita, alaviitteitä ja niin edelleen. Lähetä käsitellyt sivusi sähköpostiimme. Jos ne näyttävät hyviltä, lähetämme sinulle lisää yksityisesti, ja odotamme, että pystyt nopeasti ajamaan putkesi myös niiden läpi. Kun olemme tyytyväisiä, voimme tehdä sopimuksen. - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kiinalainen versio 中文版</a>, <a %(news_ycombinator)s>Keskustele Hacker Newsissä</a> Tämä on lyhyt blogikirjoitus. Etsimme yritystä tai laitosta auttamaan meitä OCR:ssä ja tekstin poiminnassa valtavalle kokoelmalle, jonka hankimme, vastineeksi yksinoikeudellisesta varhaisesta pääsystä. Embargokauden jälkeen julkaisemme tietysti koko kokoelman. Korkealaatuinen akateeminen teksti on äärimmäisen hyödyllistä LLM:ien koulutuksessa. Vaikka kokoelmamme on kiinankielinen, sen pitäisi olla hyödyllinen myös englanninkielisten LLM:ien koulutuksessa: mallit näyttävät koodaavan käsitteitä ja tietoa riippumatta lähdekielestä. Tätä varten teksti täytyy poimia skannauksista. Mitä Annan Arkisto hyötyy tästä? Kirjojen kokotekstihaku käyttäjilleen. Koska tavoitteemme ovat yhteneväiset LLM-kehittäjien kanssa, etsimme yhteistyökumppania. Olemme valmiita antamaan sinulle <strong>yksinoikeudellisen varhaisen pääsyn tähän kokoelmaan massana 1 vuodeksi</strong>, jos pystyt tekemään asianmukaisen OCR:n ja tekstin poiminnan. Jos olet valmis jakamaan koko putkesi koodin kanssamme, olemme valmiita pidentämään kokoelman embargoa. Yksinoikeudellinen pääsy LLM-yrityksille maailman suurimpaan kiinalaisten tietokirjojen kokoelmaan <em><strong>TL;DR:</strong> Annan Arkisto hankki ainutlaatuisen kokoelman 7,5 miljoonaa / 350TB kiinalaisia tietokirjoja — suurempi kuin Library Genesis. Olemme valmiita antamaan LLM-yritykselle yksinoikeudellisen pääsyn, vastineeksi korkealaatuisesta OCR:stä ja tekstin poiminnasta.</em> Järjestelmäarkkitehtuuri Oletetaan, että löysit joitain yrityksiä, jotka ovat valmiita isännöimään verkkosivustoasi ilman, että ne sulkevat sinut — kutsutaan näitä "vapauden rakastaviksi tarjoajiksi" 😄. Huomaat nopeasti, että kaiken isännöinti heidän kanssaan on melko kallista, joten saatat haluta löytää joitain "halpoja tarjoajia" ja tehdä varsinaisen isännöinnin siellä, välittäen vapauden rakastavien tarjoajien kautta. Jos teet sen oikein, halvat tarjoajat eivät koskaan tiedä, mitä isännöit, eivätkä koskaan saa valituksia. Kaikkien näiden tarjoajien kanssa on riski, että he sulkevat sinut joka tapauksessa, joten tarvitset myös redundanssia. Tarvitsemme tätä kaikilla pinomme tasoilla. Yksi jonkin verran vapauden rakastava yritys, joka on asettanut itsensä mielenkiintoiseen asemaan, on Cloudflare. He ovat <a %(blog_cloudflare)s>väittäneet</a>, etteivät he ole isännöintipalveluntarjoaja, vaan apuohjelma, kuten ISP. He eivät siksi ole DMCA:n tai muiden poistopyyntöjen alaisia, ja välittävät kaikki pyynnöt varsinaiselle isännöintipalveluntarjoajallesi. He ovat menneet niin pitkälle, että ovat menneet oikeuteen suojellakseen tätä rakennetta. Voimme siksi käyttää heitä toisena välimuisti- ja suojakerroksena. Cloudflare ei hyväksy anonyymejä maksuja, joten voimme käyttää vain heidän ilmaista suunnitelmaansa. Tämä tarkoittaa, että emme voi käyttää heidän kuormituksen tasapainotus- tai vikasietoominaisuuksiaan. Siksi <a %(annas_archive_l255)s>toteutimme tämän itse</a> verkkotunnustasolla. Sivun latauksen yhteydessä selain tarkistaa, onko nykyinen verkkotunnus edelleen saatavilla, ja jos ei, se kirjoittaa kaikki URL-osoitteet uudelleen eri verkkotunnukseen. Koska Cloudflare välimuistittaa monia sivuja, tämä tarkoittaa, että käyttäjä voi laskeutua pääverkkotunnuksellemme, vaikka välityspalvelin olisi alhaalla, ja sitten seuraavalla klikkauksella siirtyä toiselle verkkotunnukselle. Meillä on edelleen myös normaaleja operatiivisia huolenaiheita, kuten palvelimen terveyden seuranta, tausta- ja etupään virheiden kirjaaminen ja niin edelleen. Vikasietoarkkitehtuurimme mahdollistaa myös enemmän vikasietoisuutta tällä rintamalla, esimerkiksi ajamalla täysin erilaista palvelinsarjaa yhdellä verkkotunnuksista. Voimme jopa ajaa vanhempia versioita koodista ja Datasetsista tällä erillisellä verkkotunnuksella, jos pääversion kriittinen virhe jää huomaamatta. Voimme myös suojautua Cloudflaren kääntymiseltä meitä vastaan poistamalla sen yhdeltä verkkotunnuksista, kuten tältä erilliseltä verkkotunnukselta. Näiden ideoiden eri permutaatiot ovat mahdollisia. Johtopäätös On ollut mielenkiintoinen kokemus oppia, kuinka luoda vankka ja kestävä varjokirjaston hakukone. On paljon enemmän yksityiskohtia jaettavana myöhemmissä viesteissä, joten kerro, mistä haluaisit oppia lisää! Kuten aina, etsimme lahjoituksia tämän työn tukemiseksi, joten muista tarkistaa Anna Arkiston Lahjoita-sivu. Etsimme myös muunlaista tukea, kuten apurahoja, pitkäaikaisia sponsoreita, korkean riskin maksupalveluntarjoajia, ehkä jopa (tyylikkäitä!) mainoksia. Ja jos haluat antaa aikaasi ja taitojasi, etsimme aina kehittäjiä, kääntäjiä ja niin edelleen. Kiitos mielenkiinnostasi ja tuestasi. Innovointimerkit Aloitetaan teknologiapinostamme. Se on tarkoituksella tylsä. Käytämme Flaskia, MariaDB:tä ja ElasticSearchia. Siinä se kirjaimellisesti on. Haku on suurelta osin ratkaistu ongelma, emmekä aio keksiä sitä uudelleen. Lisäksi meidän on käytettävä <a %(mcfunley)s>innovointimerkkejä</a> johonkin muuhun: siihen, ettei viranomaiset sulje meitä. Kuinka laillinen tai laiton Anna's Arkisto tarkalleen ottaen on? Tämä riippuu enimmäkseen oikeudellisesta lainkäyttöalueesta. Useimmat maat uskovat jonkinlaiseen tekijänoikeuteen, mikä tarkoittaa, että ihmisille tai yrityksille annetaan yksinoikeus tiettyihin teoksiin tietyn ajanjakson ajaksi. Sivuhuomautuksena, Anna's Arkistossa uskomme, että vaikka tekijänoikeuksista on joitain hyötyjä, kokonaisuudessaan ne ovat yhteiskunnalle negatiivisia — mutta se on tarina toiseen kertaan. Tämä yksinoikeus tiettyihin teoksiin tarkoittaa, että on laitonta kenenkään tämän monopolin ulkopuolella suoraan jakaa näitä teoksia — mukaan lukien meidän. Mutta Anna's Arkisto on hakukone, joka ei suoraan jaa näitä teoksia (ainakaan ei meidän clearnet-sivustollamme), joten meidän pitäisi olla kunnossa, eikö niin? Ei aivan. Monilla lainkäyttöalueilla on laitonta paitsi jakaa tekijänoikeudella suojattuja teoksia, myös linkittää paikkoihin, jotka tekevät niin. Klassinen esimerkki tästä on Yhdysvaltojen DMCA-laki. Tämä on spektrin tiukin pää. Toisessa päässä spektriä voisi teoriassa olla maita, joissa ei ole lainkaan tekijänoikeuslakeja, mutta näitä ei oikeastaan ole olemassa. Lähes jokaisella maalla on jonkinlainen tekijänoikeuslaki kirjoissa. Lainvalvonta on eri asia. On paljon maita, joiden hallitukset eivät välitä tekijänoikeuslain valvonnasta. On myös maita näiden kahden ääripään välissä, jotka kieltävät tekijänoikeudella suojattujen teosten jakamisen, mutta eivät kiellä linkittämistä tällaisiin teoksiin. Toinen huomioon otettava asia on yritystasolla. Jos yritys toimii lainkäyttöalueella, joka ei välitä tekijänoikeuksista, mutta yritys itse ei ole valmis ottamaan riskiä, he saattavat sulkea verkkosivustosi heti, kun joku valittaa siitä. Lopuksi, suuri huomioon otettava asia on maksut. Koska meidän on pysyttävä anonyymeinä, emme voi käyttää perinteisiä maksutapoja. Tämä jättää meille kryptovaluutat, ja vain pieni osa yrityksistä tukee niitä (on olemassa virtuaalisia debit-kortteja, jotka maksetaan kryptolla, mutta niitä ei usein hyväksytä). - Anna ja tiimi (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Pyöritän <a %(wikipedia_annas_archive)s>Annan Arkistoa</a>, maailman suurinta avoimen lähdekoodin voittoa tavoittelematonta hakukonetta <a %(wikipedia_shadow_library)s>varjokirjastoille</a>, kuten Sci-Hub, Library Genesis ja Z-Library. Tavoitteenamme on tehdä tiedosta ja kulttuurista helposti saatavilla olevaa ja lopulta rakentaa yhteisö, joka yhdessä arkistoi ja säilyttää <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>kaikki maailman kirjat</a>. Tässä artikkelissa näytän, miten pyöritämme tätä verkkosivustoa ja ainutlaatuiset haasteet, jotka liittyvät verkkosivuston ylläpitoon, jonka laillinen asema on kyseenalainen, koska ei ole olemassa "AWS:ää varjohyväntekeväisyyksille". <em>Tutustu myös sisarartikkeliin <a %(blog_how_to_become_a_pirate_archivist)s>Kuinka tulla merirosvoarkistoijaksi</a>.</em> Kuinka pyörittää varjokirjastoa: Annan Arkiston toiminta Ei ole <q>AWS:ää varjohyväntekeväisyyksille,</q> joten miten pyöritämme Annan Arkistoa? Työkalut Sovelluspalvelin: Flask, MariaDB, ElasticSearch, Docker. Kehitys: Gitlab, Weblate, Zulip. Palvelimen hallinta: Ansible, Checkmk, UFW. Onion staattinen hosting: Tor, Nginx. Välityspalvelin: Varnish. Katsotaanpa, mitä työkaluja käytämme kaiken tämän saavuttamiseksi. Tämä kehittyy hyvin paljon, kun kohtaamme uusia ongelmia ja löydämme uusia ratkaisuja. On joitakin päätöksiä, joita olemme pohtineet edestakaisin. Yksi niistä on palvelimien välinen viestintä: käytimme aiemmin Wireguardia tähän, mutta huomasimme, että se lakkaa ajoittain lähettämästä dataa tai lähettää dataa vain yhteen suuntaan. Tämä tapahtui useiden eri Wireguard-asetusten kanssa, joita kokeilimme, kuten <a %(github_costela_wesher)s>wesher</a> ja <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Kokeilimme myös porttien tunnelointia SSH:n kautta, käyttäen autossh:ta ja sshuttlea, mutta kohtasimme <a %(github_sshuttle)s>ongelmia siellä</a> (vaikka en ole vieläkään varma, kärsiikö autossh TCP-over-TCP-ongelmista vai ei — se vain tuntuu kömpelöltä ratkaisulta, mutta ehkä se onkin ihan hyvä?). Sen sijaan palasimme takaisin suoriin yhteyksiin palvelimien välillä, piilottaen, että palvelin toimii halvoilla tarjoajilla käyttämällä IP-suodatusta UFW:llä. Tällä on se haittapuoli, että Docker ei toimi hyvin UFW:n kanssa, ellei käytä <code>network_mode: "host"</code>. Kaikki tämä on hieman alttiimpaa virheille, koska pienikin väärä konfiguraatio voi altistaa palvelimesi internetille. Ehkä meidän pitäisi palata autossh:iin — palaute olisi erittäin tervetullutta tässä. Olemme myös pohtineet Varnishin ja Nginxin välillä. Tällä hetkellä pidämme Varnishista, mutta siinä on omat omituisuutensa ja karheutensa. Sama pätee Checkmkiin: emme rakasta sitä, mutta se toimii toistaiseksi. Weblate on ollut ihan ok, mutta ei uskomaton — pelkään joskus, että se menettää datani aina, kun yritän synkronoida sen git-repomme kanssa. Flask on ollut hyvä kokonaisuudessaan, mutta siinä on joitakin outoja omituisuuksia, jotka ovat maksaneet paljon aikaa selvittää, kuten mukautettujen verkkotunnusten konfigurointi tai ongelmat sen SqlAlchemy-integraation kanssa. Toistaiseksi muut työkalut ovat olleet loistavia: meillä ei ole vakavia valituksia MariaDB:stä, ElasticSearchista, Gitlabista, Zulipista, Dockerista ja Torista. Kaikissa näissä on ollut joitakin ongelmia, mutta ei mitään liian vakavaa tai aikaa vievää. Yhteisö Ensimmäinen haaste saattaa olla yllättävä. Se ei ole tekninen ongelma tai oikeudellinen ongelma. Se on psykologinen ongelma: tämän työn tekeminen varjoissa voi olla uskomattoman yksinäistä. Riippuen siitä, mitä aiot tehdä ja uhkamallisi, sinun on ehkä oltava erittäin varovainen. Toisessa ääripäässä on ihmisiä kuten Alexandra Elbakyan*, Sci-Hubin perustaja, joka on hyvin avoin toiminnastaan. Mutta hän on suuressa vaarassa tulla pidätetyksi, jos hän vierailisi länsimaassa tällä hetkellä, ja voisi kohdata vuosikymmenten vankeusrangaistuksen. Onko se riski, jonka olisit valmis ottamaan? Me olemme toisessa ääripäässä; olemme erittäin varovaisia, ettemme jätä mitään jälkiä ja pidämme vahvan operatiivisen turvallisuuden. * Kuten HN:ssä mainitsi "ynno", Alexandra ei alun perin halunnut olla tunnettu: "Hänen palvelimensa oli asetettu lähettämään yksityiskohtaisia virheilmoituksia PHP:stä, mukaan lukien virheellisen lähdetiedoston täydellinen polku, joka oli hakemistossa /home/<USER>" Joten käytä satunnaisia käyttäjänimiä tietokoneilla, joita käytät tähän, siltä varalta, että konfiguroit jotain väärin. Tuo salailu kuitenkin aiheuttaa psykologisia kustannuksia. Useimmat ihmiset rakastavat saada tunnustusta tekemästään työstä, mutta et voi ottaa mitään kunniaa tästä tosielämässä. Jopa yksinkertaiset asiat voivat olla haastavia, kuten ystävien kysyessä, mitä olet puuhaillut (jossain vaiheessa "säätämistä NAS:ni / kotilaboratorioni kanssa" vanhenee). Siksi on niin tärkeää löytää jokin yhteisö. Voit luopua osasta operatiivista turvallisuutta luottamalla joihinkin hyvin läheisiin ystäviin, joihin tiedät voivasi luottaa syvästi. Silloinkin ole varovainen, ettet laita mitään kirjallisesti, siltä varalta, että heidän on luovutettava sähköpostinsa viranomaisille tai jos heidän laitteensa ovat vaarantuneet jollain muulla tavalla. Vielä parempi on löytää joitakin muita merirosvoja. Jos läheiset ystäväsi ovat kiinnostuneita liittymään seuraasi, hienoa! Muuten saatat löytää muita verkosta. Valitettavasti tämä on edelleen marginaalinen yhteisö. Toistaiseksi olemme löytäneet vain kourallisen muita, jotka ovat aktiivisia tällä alalla. Hyviä aloituspaikkoja näyttävät olevan Library Genesiksen foorumit ja r/DataHoarder. Myös Archive Teamissa on samanhenkisiä henkilöitä, vaikka he toimivat lain puitteissa (vaikka lain harmailla alueilla). Perinteisissä "warez"- ja piratismipiireissä on myös ihmisiä, jotka ajattelevat samalla tavalla. Olemme avoimia ideoille siitä, miten voisimme edistää yhteisöä ja tutkia uusia ajatuksia. Voit vapaasti lähettää meille viestiä Twitterissä tai Redditissä. Ehkä voisimme järjestää jonkinlaisen foorumin tai keskusteluryhmän. Yksi haaste on, että tämä voi helposti joutua sensuurin kohteeksi yleisillä alustoilla, joten meidän olisi isännöitävä sitä itse. On myös tasapainoteltava sen välillä, että keskustelut ovat täysin julkisia (enemmän potentiaalista osallistumista) tai yksityisiä (ettei mahdolliset "kohteet" tiedä, että aiomme kerätä heiltä tietoja). Meidän on mietittävä tätä. Kerro meille, jos olet kiinnostunut tästä! Johtopäätös Toivottavasti tämä on hyödyllistä aloitteleville piraattiarkistoijille. Olemme innoissamme toivottaessamme teidät tervetulleiksi tähän maailmaan, joten älkää epäröikö ottaa yhteyttä. Säilyttäkäämme niin paljon maailman tietoa ja kulttuuria kuin voimme ja peilatkaamme se laajalle. Projektit 4. Datan valinta Usein voit käyttää metadataa selvittääksesi järkevän osajoukon ladattavasta datasta. Vaikka lopulta haluaisit ladata kaiken datan, voi olla hyödyllistä priorisoida tärkeimmät kohteet ensin, siltä varalta, että sinut havaitaan ja puolustuksia parannetaan, tai koska sinun täytyisi ostaa lisää levyjä, tai yksinkertaisesti siksi, että elämässäsi tapahtuu jotain muuta ennen kuin ehdit ladata kaiken. Esimerkiksi kokoelmassa saattaa olla useita painoksia samasta perusresurssista (kuten kirjasta tai elokuvasta), joista yksi on merkitty parhaaksi laaduksi. Näiden painosten tallentaminen ensin olisi järkevää. Saatat lopulta haluta tallentaa kaikki painokset, sillä joissakin tapauksissa metadata voi olla merkitty väärin, tai painosten välillä voi olla tuntemattomia kompromisseja (esimerkiksi "paras painos" voi olla paras useimmilla tavoilla, mutta huonompi muilla tavoilla, kuten elokuvassa, jossa on korkeampi resoluutio mutta puuttuvat tekstitykset). Voit myös etsiä metadata-tietokannastasi löytääksesi mielenkiintoisia asioita. Mikä on suurin isännöity tiedosto, ja miksi se on niin suuri? Mikä on pienin tiedosto? Onko tiettyjen kategorioiden, kielten ja niin edelleen kohdalla mielenkiintoisia tai odottamattomia kuvioita? Onko olemassa kaksoiskappaleita tai hyvin samankaltaisia nimikkeitä? Onko kuvioita siinä, milloin dataa lisättiin, kuten yhtenä päivänä, jolloin lisättiin paljon tiedostoja kerralla? Voit usein oppia paljon tarkastelemalla tietojoukkoa eri tavoin. Meidän tapauksessamme deduplikoimme Z-Libraryn kirjat Library Genesisin md5-tiivisteiden avulla, mikä säästi paljon latausaikaa ja levytilaa. Tämä on kuitenkin melko ainutlaatuinen tilanne. Useimmissa tapauksissa ei ole kattavia tietokantoja siitä, mitkä tiedostot ovat jo asianmukaisesti säilytettyjä muiden piraattien toimesta. Tämä itsessään on valtava mahdollisuus jollekin. Olisi hienoa saada säännöllisesti päivitetty yleiskatsaus esimerkiksi musiikista ja elokuvista, jotka ovat jo laajasti jaettuja torrent-sivustoilla, ja ovat siksi pienempi prioriteetti sisällyttää piraattipeileihin. 6. Jakelu Sinulla on data, mikä antaa sinulle hallussasi maailman ensimmäisen piraattipeilin kohteestasi (todennäköisesti). Monin tavoin vaikein osa on ohi, mutta riskialttein osa on vielä edessäsi. Loppujen lopuksi, tähän asti olet ollut huomaamaton; lentänyt tutkan alla. Kaikki mitä sinun piti tehdä, oli käyttää hyvää VPN:ää koko ajan, olla täyttämättä henkilökohtaisia tietojasi mihinkään lomakkeisiin (tietenkin), ja ehkä käyttää erityistä selainistuntoa (tai jopa eri tietokonetta). Nyt sinun täytyy jakaa data. Meidän tapauksessamme halusimme ensin palauttaa kirjat Library Genesisiin, mutta huomasimme nopeasti siihen liittyvät vaikeudet (kaunokirjallisuuden ja tietokirjallisuuden lajittelu). Joten päätimme jakelusta Library Genesis -tyylisillä torrenteilla. Jos sinulla on mahdollisuus osallistua olemassa olevaan projektiin, se voi säästää paljon aikaa. Kuitenkin, tällä hetkellä ei ole monia hyvin organisoituja piraattipeilejä. Oletetaan, että päätät jakaa torrentteja itse. Yritä pitää tiedostot pieninä, jotta ne on helppo peilata muilla verkkosivustoilla. Sinun täytyy sitten siementää torrentit itse, pysyen samalla anonyyminä. Voit käyttää VPN:ää (porttiohjauksella tai ilman), tai maksaa sekoitetuilla Bitcoineilla Seedboxista. Jos et tiedä, mitä jotkut näistä termeistä tarkoittavat, sinulla on paljon luettavaa, sillä on tärkeää ymmärtää riskien vaihtokaupat tässä. Voit isännöidä torrent-tiedostoja olemassa olevilla torrent-sivustoilla. Meidän tapauksessamme päätimme itse asiassa isännöidä verkkosivustoa, koska halusimme myös levittää filosofiaamme selkeällä tavalla. Voit tehdä tämän itse samalla tavalla (käytämme Njallaa verkkotunnuksillemme ja isännöinnille, maksettuna sekoitetuilla Bitcoineilla), mutta voit myös ottaa meihin yhteyttä, jotta voimme isännöidä torrenttejasi. Haluamme rakentaa kattavan indeksin piraattipeileistä ajan myötä, jos tämä idea saa suosiota. Mitä tulee VPN:n valintaan, tästä on jo kirjoitettu paljon, joten toistamme vain yleisen neuvon valita maineen perusteella. Oikeudessa testatut lokittomat käytännöt, joilla on pitkä historia yksityisyyden suojaamisessa, ovat mielestämme vähäriskisin vaihtoehto. Huomaa, että vaikka tekisit kaiken oikein, et voi koskaan päästä nollariskiin. Esimerkiksi, kun siementät torrenttejasi, erittäin motivoitunut valtiollinen toimija voi todennäköisesti tarkastella VPN-palvelimien sisään- ja ulosvirtausta ja päätellä, kuka olet. Tai voit yksinkertaisesti tehdä jonkin virheen. Me olemme todennäköisesti jo tehneet, ja teemme uudelleen. Onneksi valtiot eivät välitä <em>niin</em> paljon piratismista. Jokaiselle projektille on tehtävä päätös, julkaistaanko se samalla identiteetillä kuin aiemmin vai ei. Jos käytät samaa nimeä, aiempien projektien operatiivisen turvallisuuden virheet voivat palata kummittelemaan sinua. Mutta julkaiseminen eri nimillä tarkoittaa, että et rakenna pitkäkestoista mainetta. Me valitsimme vahvan operatiivisen turvallisuuden alusta alkaen, jotta voimme jatkaa saman identiteetin käyttöä, mutta emme epäröi julkaista eri nimellä, jos teemme virheen tai jos olosuhteet sitä vaativat. Sanan levittäminen voi olla hankalaa. Kuten sanoimme, tämä on edelleen niche-yhteisö. Alun perin postasimme Redditiin, mutta saimme todella jalansijaa Hacker Newsissa. Tällä hetkellä suosittelemme postaamaan muutamaan paikkaan ja katsomaan, mitä tapahtuu. Ja jälleen, ota meihin yhteyttä. Haluaisimme levittää sanaa lisää piraattiarkistointiponnisteluista. 1. Alueen valinta / filosofia Tietoa ja kulttuuriperintöä on pelastettavana yllin kyllin, mikä voi olla ylivoimaista. Siksi on usein hyödyllistä ottaa hetki aikaa ja miettiä, mikä voisi olla sinun panoksesi. Jokaisella on oma tapansa ajatella tätä, mutta tässä on joitain kysymyksiä, joita voisit kysyä itseltäsi: Meidän tapauksessamme välitimme erityisesti tieteen pitkäaikaisesta säilyttämisestä. Tiesimme Library Genesisistä ja siitä, miten se oli täysin peilattu monta kertaa torrenttien avulla. Rakastimme sitä ideaa. Sitten eräänä päivänä yksi meistä yritti löytää tieteellisiä oppikirjoja Library Genesisistä, mutta ei löytänyt niitä, mikä herätti epäilyksiä sen täydellisyydestä. Etsimme sitten näitä oppikirjoja verkosta ja löysimme ne muualta, mikä istutti siemenen projektillemme. Jo ennen kuin tiesimme Z-kirjastosta, meillä oli ajatus olla yrittämättä kerätä kaikkia niitä kirjoja manuaalisesti, vaan keskittyä olemassa olevien kokoelmien peilaamiseen ja niiden palauttamiseen Library Genesisiin. Mitä taitoja sinulla on, joita voit hyödyntää? Esimerkiksi, jos olet verkkoturvallisuuden asiantuntija, voit löytää tapoja kiertää IP-estoja turvallisille kohteille. Jos olet loistava yhteisöjen organisoinnissa, voit ehkä koota ihmisiä yhteisen tavoitteen ympärille. On kuitenkin hyödyllistä osata jonkin verran ohjelmointia, jos vain hyvän operatiivisen turvallisuuden ylläpitämiseksi koko prosessin ajan. Mikä olisi korkean vipuvaikutuksen alue, johon keskittyä? Jos aiot käyttää X tuntia piraattiarkistointiin, miten saat suurimman "hyödyn" ajastasi? Mitkä ovat ainutlaatuisia tapoja, joilla ajattelet tätä? Sinulla saattaa olla mielenkiintoisia ideoita tai lähestymistapoja, jotka muut ovat saattaneet ohittaa. Kuinka paljon aikaa sinulla on tähän? Neuvomme olisi aloittaa pienestä ja tehdä suurempia projekteja, kun saat siitä otteen, mutta se voi viedä mukanaan. Miksi olet kiinnostunut tästä? Mikä sinua innostaa? Jos saamme joukon ihmisiä, jotka kaikki arkistoivat asioita, joista he erityisesti välittävät, se kattaisi paljon! Tiedät paljon enemmän kuin keskivertoihminen intohimostasi, kuten mikä data on tärkeää säilyttää, mitkä ovat parhaat kokoelmat ja verkkoyhteisöt ja niin edelleen. 3. Metadatan kaappaus Lisätty/muokattu päivämäärä: jotta voit palata myöhemmin ja ladata tiedostoja, joita et aiemmin ladannut (vaikka voit usein käyttää myös ID:tä tai hashia tähän). Hash (md5, sha1): varmistaaksesi, että latasit tiedoston oikein. ID: voi olla jokin sisäinen ID, mutta ID:t kuten ISBN tai DOI ovat myös hyödyllisiä. Tiedostonimi / sijainti Kuvaus, kategoria, tunnisteet, tekijät, kieli, jne. Koko: laskeaksesi, kuinka paljon levytilaa tarvitset. Mennään hieman teknisemmäksi tässä. Metadatan kaappaamiseksi verkkosivustoilta olemme pitäneet asiat melko yksinkertaisina. Käytämme Python-skriptejä, joskus curlia, ja MySQL-tietokantaa tulosten tallentamiseen. Emme ole käyttäneet mitään hienoa kaappausohjelmistoa, joka voisi kartoittaa monimutkaisia verkkosivustoja, koska toistaiseksi olemme tarvinneet kaapata vain yhden tai kaksi sivutyyppiä vain luettelemalla id:t ja jäsentämällä HTML:ää. Jos ei ole helposti lueteltavia sivuja, saatat tarvita kunnollisen indeksoijan, joka yrittää löytää kaikki sivut. Ennen kuin aloitat koko verkkosivuston kaappaamisen, kokeile tehdä se manuaalisesti hetken aikaa. Käy läpi muutama kymmenen sivua itse, saadaksesi käsityksen siitä, miten se toimii. Joskus kohtaat jo IP-estoja tai muuta mielenkiintoista käyttäytymistä tällä tavalla. Sama pätee datan kaappaamiseen: ennen kuin syvennyt liikaa tähän kohteeseen, varmista, että voit todella ladata sen dataa tehokkaasti. Rajoitusten kiertämiseksi voit kokeilla muutamia asioita. Onko muita IP-osoitteita tai palvelimia, jotka isännöivät samaa dataa, mutta joilla ei ole samoja rajoituksia? Onko API-päätepisteitä, joilla ei ole rajoituksia, kun taas muilla on? Millä latausnopeudella IP:si estetään ja kuinka kauan? Vai etkö ole estetty, mutta hidastettu? Mitä jos luot käyttäjätilin, miten asiat muuttuvat silloin? Voitko käyttää HTTP/2:ta pitämään yhteydet auki, ja lisääkö se nopeutta, jolla voit pyytää sivuja? Onko sivuja, jotka listaavat useita tiedostoja kerralla, ja onko siellä listattu tieto riittävää? Asioita, jotka haluat todennäköisesti tallentaa, ovat: Teemme tämän yleensä kahdessa vaiheessa. Ensin lataamme raakat HTML-tiedostot, yleensä suoraan MySQL:ään (välttääksemme paljon pieniä tiedostoja, joista puhumme lisää alla). Sitten, erillisessä vaiheessa, käymme läpi nuo HTML-tiedostot ja jäsennämme ne varsinaisiin MySQL-tauluihin. Tällä tavalla sinun ei tarvitse ladata kaikkea uudelleen alusta, jos huomaat virheen jäsennyskoodissasi, koska voit vain käsitellä HTML-tiedostot uudella koodilla. On myös usein helpompaa rinnakkaistaa käsittelyvaihe, mikä säästää aikaa (ja voit kirjoittaa käsittelykoodin samalla kun kaappaus on käynnissä, sen sijaan että sinun pitäisi kirjoittaa molemmat vaiheet kerralla). Lopuksi, huomaa, että joissakin kohteissa metadata-kaavinta on ainoa vaihtoehto. On olemassa suuria metadata-kokoelmia, joita ei ole säilytetty asianmukaisesti. Otsikko Alueen valinta / filosofia: Mihin haluat keskittyä ja miksi? Mitkä ovat ainutlaatuiset intohimosi, taitosi ja olosuhteesi, joita voit hyödyntää? Kohteen valinta: Minkä tietyn kokoelman aiot peilata? Metadatan kerääminen: Tiedostojen tietojen luettelointi ilman, että varsinaisia (usein paljon suurempia) tiedostoja ladataan. Datan valinta: Metadatan perusteella rajataan, mikä data on tällä hetkellä tärkeintä arkistoida. Se voi olla kaikkea, mutta usein on järkevä tapa säästää tilaa ja kaistanleveyttä. Datan kerääminen: Datan hankkiminen. Jakelu: Pakkaaminen torrentteihin, ilmoittaminen jossain, ihmisten saaminen levittämään sitä. 5. Datan kaavinta Nyt olet valmis lataamaan dataa suurina määrinä. Kuten aiemmin mainittiin, tässä vaiheessa sinun pitäisi jo manuaalisesti olla ladannut joukko tiedostoja, jotta ymmärrät paremmin kohteen käyttäytymistä ja rajoituksia. Kuitenkin, kun alat ladata paljon tiedostoja kerralla, sinua odottaa vielä yllätyksiä. Neuvomme tässä on pitää asiat yksinkertaisina. Aloita vain lataamalla joukko tiedostoja. Voit käyttää Pythonia ja laajentaa sitten useisiin säikeisiin. Mutta joskus vielä yksinkertaisempaa on luoda Bash-tiedostoja suoraan tietokannasta ja ajaa niitä useita eri terminaali-ikkunoissa laajentaaksesi toimintaa. Nopea tekninen niksi, joka kannattaa mainita, on OUTFILE:n käyttö MySQL:ssä, jonka voit kirjoittaa mihin tahansa, jos poistat käytöstä "secure_file_priv" mysqld.cnf:ssä (ja varmista myös, että poistat käytöstä/ohitat AppArmorin, jos käytät Linuxia). Tallennamme datan yksinkertaisille kiintolevyille. Aloita siitä, mitä sinulla on, ja laajenna hitaasti. Voi olla ylivoimaista ajatella satojen teratavujen datan tallentamista. Jos kohtaat tällaisen tilanteen, julkaise ensin hyvä osajoukko ja pyydä ilmoituksessasi apua lopun tallentamiseen. Jos haluat hankkia lisää kiintolevyjä itse, r/DataHoarderilla on hyviä resursseja hyvien tarjousten löytämiseen. Yritä olla huolehtimatta liikaa hienoista tiedostojärjestelmistä. On helppo ajautua syvälle asioiden, kuten ZFS:n, asettamiseen. Yksi tekninen yksityiskohta, joka on hyvä tietää, on se, että monet tiedostojärjestelmät eivät käsittele hyvin suuria määriä tiedostoja. Olemme huomanneet, että yksinkertainen kiertotapa on luoda useita hakemistoja, esimerkiksi eri ID-alueille tai tiiviste-etuliitteille. Datan lataamisen jälkeen varmista tiedostojen eheys käyttämällä metadatan tiivisteitä, jos saatavilla. 2. Kohteen valinta Saavutettava: ei käytä valtavasti suojakerroksia estääkseen sinua kaapimasta niiden metadataa ja dataa. Erityinen oivallus: sinulla on erityistä tietoa tästä kohteesta, kuten erityinen pääsy tähän kokoelmaan tai olet selvittänyt, miten voittaa heidän puolustuksensa. Tämä ei ole välttämätöntä (tuleva projektimme ei tee mitään erityistä), mutta se auttaa varmasti! Suuri Joten, meillä on alue, jota tarkastelemme, mutta mikä tietty kokoelma meidän pitäisi peilata? On muutamia asioita, jotka tekevät hyvästä kohteesta: Kun löysimme tiedekirjamme muilta verkkosivustoilta kuin Library Genesis, yritimme selvittää, miten ne päätyivät internetiin. Sitten löysimme Z-Libraryn ja huomasimme, että vaikka useimmat kirjat eivät ensin ilmesty siellä, ne päätyvät lopulta sinne. Opimme sen suhteesta Library Genesikseen sekä (taloudellisesta) kannustinjärjestelmästä ja paremmasta käyttöliittymästä, jotka molemmat tekivät siitä paljon täydellisemmän kokoelman. Teimme sitten alustavaa metadata- ja datakaappausta ja huomasimme, että voisimme kiertää heidän IP-latausrajoituksensa hyödyntämällä yhden jäsenemme erityistä pääsyä moniin välityspalvelimiin. Kun tutkit erilaisia kohteita, on jo tärkeää piilottaa jälkesi käyttämällä VPN:ää ja kertakäyttöisiä sähköpostiosoitteita, joista puhumme lisää myöhemmin. Ainutlaatuinen: ei jo hyvin katettu muiden projektien toimesta. Kun teemme projektin, siinä on muutama vaihe: Nämä eivät ole täysin itsenäisiä vaiheita, ja usein myöhemmän vaiheen oivallukset vievät sinut takaisin aikaisempaan vaiheeseen. Esimerkiksi metadatan keräämisen aikana saatat huomata, että valitsemallasi kohteella on puolustusmekanismeja, jotka ylittävät taitotasosi (kuten IP-estoja), joten palaat takaisin ja löydät toisen kohteen. - Anna ja tiimi (<a %(reddit)s>Reddit</a>) Kokonaisia kirjoja voidaan kirjoittaa digitaalisen säilyttämisen <em>miksi</em> yleensä ja merirosvoarkistoinnin erityisesti, mutta annetaan nopea johdanto niille, jotka eivät ole kovin perehtyneitä. Maailma tuottaa enemmän tietoa ja kulttuuria kuin koskaan ennen, mutta myös enemmän siitä katoaa kuin koskaan ennen. Ihmiskunta luottaa suurelta osin yrityksiin, kuten akateemisiin kustantajiin, suoratoistopalveluihin ja sosiaalisen median yrityksiin, tämän perinnön säilyttämisessä, ja ne eivät ole usein osoittautuneet suuriksi huoltajiksi. Tutustu dokumenttiin Digital Amnesia tai mihin tahansa Jason Scottin puheeseen. On olemassa joitakin instituutioita, jotka tekevät hyvää työtä arkistoimalla niin paljon kuin voivat, mutta ne ovat lain rajoittamia. Merirosvoina olemme ainutlaatuisessa asemassa arkistoimaan kokoelmia, joihin he eivät voi koskea tekijänoikeuksien valvonnan tai muiden rajoitusten vuoksi. Voimme myös peilata kokoelmia monta kertaa ympäri maailmaa, mikä lisää asianmukaisen säilyttämisen mahdollisuuksia. Toistaiseksi emme mene keskusteluihin immateriaalioikeuksien eduista ja haitoista, lain rikkomisen moraalista, sensuurin pohdinnoista tai tiedon ja kulttuurin saatavuuden ongelmasta. Kun kaikki tämä on käsitelty, sukeltakaamme <em>miten</em>. Kerromme, kuinka tiimimme ryhtyi merirosvoarkistonhoitajiksi ja mitä opimme matkan varrella. Tällä matkalla on monia haasteita, ja toivottavasti voimme auttaa sinua joidenkin niistä läpi. Kuinka tulla merirosvoarkistonhoitajaksi Ensimmäinen haaste saattaa olla yllättävä. Se ei ole tekninen ongelma tai oikeudellinen ongelma. Se on psykologinen ongelma. Ennen kuin sukellamme sisään, kaksi päivitystä Pirate Library Mirrorista (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkisto</a>): Saimme erittäin anteliaita lahjoituksia. Ensimmäinen oli 10 000 dollaria anonyymiltä henkilöltä, joka on myös tukenut "bookwarrioria", Library Genesiksen alkuperäistä perustajaa. Erityiskiitokset bookwarriorille tämän lahjoituksen mahdollistamisesta. Toinen oli toinen 10 000 dollaria anonyymiltä lahjoittajalta, joka otti yhteyttä viime julkaisumme jälkeen ja inspiroitui auttamaan. Saimme myös useita pienempiä lahjoituksia. Kiitos paljon kaikesta anteliaisuudestanne. Meillä on jännittäviä uusia projekteja suunnitteilla, joita tämä tukee, joten pysykää kuulolla. Meillä oli teknisiä vaikeuksia toisen julkaisumme koon kanssa, mutta torrenttimme ovat nyt ylhäällä ja jakautuvat. Saimme myös anteliaan tarjouksen anonyymiltä henkilöltä jakaa kokoelmamme heidän erittäin nopeilla palvelimillaan, joten teemme erityislatauksen heidän koneilleen, minkä jälkeen kaikkien muiden, jotka lataavat kokoelmaa, pitäisi nähdä suuri parannus nopeudessa. Blogikirjoitukset Hei, olen Anna. Loimme <a %(wikipedia_annas_archive)s>Anna Arkiston</a>, maailman suurimman varjokirjaston. Tämä on henkilökohtainen blogini, jossa minä ja tiimini kirjoitamme piratismista, digitaalisen säilyttämisen ja muusta. Yhdistä kanssani <a %(reddit)s>Redditissä</a>. Huomaa, että tämä verkkosivusto on vain blogi. Isännöimme täällä vain omia sanojamme. Täällä ei isännöidä tai linkitetä torrentteja tai muita tekijänoikeudella suojattuja tiedostoja. <strong>Kirjasto</strong> - Kuten useimmat kirjastot, keskitymme ensisijaisesti kirjallisiin materiaaleihin, kuten kirjoihin. Saatamme laajentaa muihin mediatyyppeihin tulevaisuudessa. <strong>Peili</strong> - Olemme tiukasti olemassa olevien kirjastojen peili. Keskitymme säilyttämiseen, emme kirjojen helppoon hakemiseen ja lataamiseen (pääsy) tai suuren yhteisön luomiseen, joka tuo uusia kirjoja (hankinta). <strong>Piraatti</strong> - Rikomme tietoisesti tekijänoikeuslakia useimmissa maissa. Tämä mahdollistaa meidän tehdä jotain, mitä lailliset tahot eivät voi: varmistaa, että kirjat peilataan laajalle. <em>Emme linkitä tiedostoihin tästä blogista. Löydä ne itse.</em> - Anna ja tiimi (<a %(reddit)s>Reddit</a>) Tämä projekti (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkisto</a>) pyrkii edistämään ihmiskunnan tiedon säilyttämistä ja vapauttamista. Teemme pienen ja nöyrän panoksemme, suurten edeltäjiemme jalanjäljissä. Tämän projektin painopiste on kuvattu sen nimessä: Ensimmäinen kirjasto, jonka olemme peilanneet, on Z-Library. Tämä on suosittu (ja laiton) kirjasto. He ovat ottaneet Library Genesis -kokoelman ja tehneet siitä helposti haettavan. Lisäksi he ovat erittäin tehokkaita uusien kirjalahjoitusten hankkimisessa, kannustamalla käyttäjiä erilaisilla eduilla. Tällä hetkellä he eivät palauta näitä uusia kirjoja Library Genesikseen. Ja toisin kuin Library Genesis, he eivät tee kokoelmaansa helposti peilattavaksi, mikä estää laajaa säilyttämistä. Tämä on tärkeää heidän liiketoimintamallilleen, koska he veloittavat rahaa kokoelmaansa pääsystä suurina määrinä (yli 10 kirjaa päivässä). Emme tee moraalisia arvioita siitä, että rahaa veloitetaan laittoman kirjakokoelman laajasta käytöstä. On kiistatonta, että Z-Library on onnistunut laajentamaan tiedon saatavuutta ja hankkimaan lisää kirjoja. Olemme täällä vain tekemässä omaa osuutemme: varmistamassa tämän yksityisen kokoelman pitkäaikaisen säilymisen. Haluaisimme kutsua teidät auttamaan ihmiskunnan tiedon säilyttämisessä ja vapauttamisessa lataamalla ja jakamalla torrenttejamme. Katso projektisivulta lisätietoja siitä, miten data on järjestetty. Haluaisimme myös kovasti kutsua teidät jakamaan ideoitanne siitä, mitkä kokoelmat peilata seuraavaksi ja miten se tehdään. Yhdessä voimme saavuttaa paljon. Tämä on vain pieni panos lukemattomien muiden joukossa. Kiitos kaikesta, mitä teette. Esittelyssä Piraattikirjaston Peili: 7TB kirjoja säilytettäväksi (jotka eivät ole Libgenissä) 10% o ihmiskunnan kirjallista perintöä säilytetty ikuisesti <strong>Google.</strong> Loppujen lopuksi he tekivät tämän tutkimuksen Google Booksia varten. Kuitenkin heidän metadatansa ei ole saatavilla massana ja on melko vaikeaa kaapia. <strong>Erilaiset yksittäiset kirjastojärjestelmät ja arkistot.</strong> On kirjastoja ja arkistoja, joita ei ole indeksoitu ja koottu yhteen yllä mainituista, usein siksi, että ne ovat alirahoitettuja tai muista syistä eivät halua jakaa tietojaan Open Libraryn, OCLC:n, Googlen jne. kanssa. Monilla näistä on digitaalisia tietueita, jotka ovat saatavilla internetin kautta, ja ne eivät usein ole kovin hyvin suojattuja, joten jos haluat auttaa ja pitää hauskaa oppiessasi outoja kirjastojärjestelmiä, nämä ovat loistavia lähtökohtia. <strong>ISBNdb.</strong> Tämä on tämän blogikirjoituksen aihe. ISBNdb kaapii erilaisia verkkosivustoja kirjametadatan, erityisesti hintatietojen, saamiseksi, joita he sitten myyvät kirjakauppiaille, jotta he voivat hinnoitella kirjansa markkinoiden mukaisesti. Koska ISBN:t ovat nykyään melko universaaleja, he ovat käytännössä rakentaneet ”verkkosivun jokaiselle kirjalle”. <strong>Open Library.</strong> Kuten aiemmin mainittiin, tämä on heidän koko tehtävänsä. He ovat hankkineet valtavia määriä kirjastotietoja yhteistyökirjastoilta ja kansallisista arkistoista, ja jatkavat sitä edelleen. Heillä on myös vapaaehtoisia kirjastonhoitajia ja tekninen tiimi, joka yrittää deduplikoida tietueita ja merkitä ne kaikenlaisella metadatalla. Parasta kaikessa on, että heidän tietokantansa on täysin avoin. Voit yksinkertaisesti <a %(openlibrary)s>ladata sen</a>. <strong>WorldCat.</strong> Tämä on voittoa tavoittelemattoman OCLC:n ylläpitämä verkkosivusto, joka myy kirjastonhallintajärjestelmiä. He kokoavat kirjametadatan monista kirjastoista ja tekevät sen saataville WorldCat-verkkosivuston kautta. Kuitenkin he myös ansaitsevat rahaa myymällä näitä tietoja, joten ne eivät ole saatavilla massalatauksena. Heillä on kuitenkin joitakin rajoitetumpia massadatasettejä saatavilla latausta varten yhteistyössä tiettyjen kirjastojen kanssa. 1. Joillekin kohtuullisille "ikuisuuden" määritelmille. ;) 2. Tietenkin ihmiskunnan kirjallinen perintö on paljon enemmän kuin kirjat, erityisesti nykyään. Tämän viestin ja viimeaikaisten julkaisujemme vuoksi keskitymme kirjoihin, mutta kiinnostuksemme ulottuu pidemmälle. 3. Aaron Swartzista voi sanoa paljon enemmän, mutta halusimme vain mainita hänet lyhyesti, koska hänellä on keskeinen rooli tässä tarinassa. Ajan kuluessa yhä useammat ihmiset saattavat törmätä hänen nimeensä ensimmäistä kertaa ja voivat siten sukeltaa itse kaninkoloon. <strong>Fyysiset kopiot.</strong> Tämä ei tietenkään ole kovin hyödyllistä, koska ne ovat vain saman materiaalin kopioita. Olisi hienoa, jos voisimme säilyttää kaikki merkinnät, joita ihmiset tekevät kirjoihin, kuten Fermat’n kuuluisat ”marginaalimerkinnät”. Mutta valitettavasti se jää arkistoijan unelmaksi. <strong>”Painokset”.</strong> Tässä lasketaan jokainen kirjan ainutlaatuinen versio. Jos jokin siinä on erilaista, kuten eri kansi tai eri esipuhe, se lasketaan eri painokseksi. <strong>Tiedostot.</strong> Kun työskennellään varjokirjastojen, kuten Library Genesis, Sci-Hub tai Z-Library, kanssa, on lisähuomioitavaa. Samasta painoksesta voi olla useita skannauksia. Ja ihmiset voivat tehdä parempia versioita olemassa olevista tiedostoista skannaamalla tekstiä OCR:llä tai korjaamalla kulmassa skannattuja sivuja. Haluamme laskea nämä tiedostot vain yhdeksi painokseksi, mikä vaatisi hyvää metadataa tai deduplikointia asiakirjan samankaltaisuusmittareilla. <strong>”Teokset”.</strong> Esimerkiksi ”Harry Potter ja salaisuuksien kammio” loogisena käsitteenä, joka kattaa kaikki sen versiot, kuten eri käännökset ja uusintapainokset. Tämä on eräänlainen hyödyllinen määritelmä, mutta voi olla vaikeaa vetää rajaa siihen, mikä lasketaan. Esimerkiksi haluamme todennäköisesti säilyttää eri käännökset, vaikka uusintapainokset, joissa on vain pieniä eroja, eivät ehkä ole yhtä tärkeitä. - Anna ja tiimi (<a %(reddit)s>Reddit</a>) Piraattikirjaston Peilin (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkisto</a>) avulla tavoitteemme on ottaa kaikki maailman kirjat ja säilyttää ne ikuisesti.<sup>1</sup> Z-Library-torrenttiemme ja alkuperäisten Library Genesis -torrenttiemme välillä meillä on 11 783 153 tiedostoa. Mutta kuinka paljon se oikeastaan on? Jos deduplikoisimme nuo tiedostot kunnolla, kuinka suuren osan kaikista maailman kirjoista olemme säilyttäneet? Haluaisimme todella jotain tällaista: Aloitetaan karkeilla luvuilla: Sekä Z-Library/Libgenissä että Open Libraryssa on paljon enemmän kirjoja kuin ainutlaatuisia ISBN-numeroita. Tarkoittaako tämä, että monilla näistä kirjoista ei ole ISBN-numeroita, vai puuttuuko ISBN-metadata yksinkertaisesti? Voimme luultavasti vastata tähän kysymykseen yhdistämällä automaattista sovittamista muiden ominaisuuksien (otsikko, tekijä, kustantaja jne.) perusteella, tuomalla lisää tietolähteitä ja poimimalla ISBN-numeroita itse kirjan skannauksista (Z-Library/Libgenin tapauksessa). Kuinka moni näistä ISBN-numeroista on ainutlaatuisia? Tämä on parhaiten havainnollistettu Venn-diagrammilla: Tarkemmin sanottuna: Olimme yllättyneitä siitä, kuinka vähän päällekkäisyyksiä on! ISBNdb:llä on valtava määrä ISBN-numeroita, jotka eivät näy Z-Libraryssä tai Open Libraryssä, ja sama pätee (pienemmässä mutta silti merkittävässä määrin) myös muihin kahteen. Tämä herättää paljon uusia kysymyksiä. Kuinka paljon automaattinen yhdistäminen auttaisi merkitsemään kirjoja, joita ei ole merkitty ISBN-numeroilla? Olisiko paljon osumia ja siten lisääntyvää päällekkäisyyttä? Entä mitä tapahtuisi, jos toisimme mukaan neljännen tai viidennen datasetin? Kuinka paljon päällekkäisyyttä silloin näkisimme? Tämä antaa meille lähtökohdan. Voimme nyt tarkastella kaikkia ISBN-numeroita, jotka eivät olleet Z-Libraryn datasetissä, eivätkä vastaa myöskään otsikko/kirjailija-kenttiä. Tämä voi antaa meille mahdollisuuden säilyttää kaikki maailman kirjat: ensin etsimällä internetistä skannauksia, sitten menemällä tosielämässä skannaamaan kirjoja. Jälkimmäinen voisi jopa olla joukkorahoitettu tai "palkkioiden" ohjaama, kun ihmiset haluaisivat nähdä tiettyjen kirjojen digitalisoituvan. Kaikki tämä on tarina toiseen aikaan. Jos haluat auttaa tässä — lisäanalyysissä; lisää metadataa keräämällä; lisää kirjoja löytämällä; kirjojen OCR-käsittelyssä; tämän tekemisessä muille aloille (esim. artikkelit, äänikirjat, elokuvat, tv-ohjelmat, lehdet) tai jopa tekemällä osan tästä datasta saataville esimerkiksi ML / suurten kielimallien koulutukseen — ota yhteyttä minuun (<a %(reddit)s>Reddit</a>). Jos olet erityisesti kiinnostunut data-analyysistä, työskentelemme datasettiemme ja skriptiemme saattamiseksi helpommin käytettävään muotoon. Olisi hienoa, jos voisit vain haarauttaa muistikirjan ja alkaa leikkiä tällä. Lopuksi, jos haluat tukea tätä työtä, harkitse lahjoituksen tekemistä. Tämä on täysin vapaaehtoisvoimin toimiva operaatio, ja panoksesi tekee suuren eron. Jokainen pieni apu on tärkeä. Tällä hetkellä otamme lahjoituksia kryptovaluutassa; katso lahjoitussivu Annan Arkistossa. Prosenttiosuutta varten tarvitsemme nimittäjän: kaikkien koskaan julkaistujen kirjojen kokonaismäärän.<sup>2</sup> Ennen Google Booksin loppua, projektin insinööri Leonid Taycher <a %(booksearch_blogspot)s>yritti arvioida</a> tätä lukua. Hän päätyi — kieli poskessa — lukuun 129 864 880 ("ainakin sunnuntaihin asti"). Hän arvioi tämän luvun rakentamalla yhtenäisen tietokannan kaikista maailman kirjoista. Tätä varten hän kokosi yhteen erilaisia Datasets ja yhdisti ne eri tavoin. Pienenä sivuhuomautuksena, on toinen henkilö, joka yritti luetteloida kaikki maailman kirjat: Aaron Swartz, edesmennyt digitaalinen aktivisti ja Redditin toinen perustaja.<sup>3</sup> Hän <a %(youtube)s>perusti Open Libraryn</a> tavoitteenaan ”yksi verkkosivu jokaiselle koskaan julkaistulle kirjalle”, yhdistäen tietoja monista eri lähteistä. Hän maksoi lopulta kovan hinnan digitaalisesta säilytystyöstään, kun häntä syytettiin akateemisten artikkelien massalataamisesta, mikä johti hänen itsemurhaansa. Tarpeetonta sanoa, että tämä on yksi syy, miksi ryhmämme on pseudonyymi ja miksi olemme erittäin varovaisia. Open Librarya pyörittävät edelleen sankarillisesti Internet Archiven ihmiset, jatkaen Aaronin perintöä. Palaamme tähän myöhemmin tässä kirjoituksessa. Google-blogikirjoituksessa Taycher kuvailee joitakin haasteita tämän luvun arvioimisessa. Ensinnäkin, mikä määrittää kirjan? On olemassa muutamia mahdollisia määritelmiä: ”Painokset” vaikuttavat käytännöllisimmältä määritelmältä siitä, mitä ”kirjat” ovat. Kätevästi tätä määritelmää käytetään myös ainutlaatuisten ISBN-numeroiden antamiseen. ISBN, tai kansainvälinen standardikirjanumero, on yleisesti käytetty kansainvälisessä kaupankäynnissä, koska se on integroitu kansainväliseen viivakoodijärjestelmään (”International Article Number”). Jos haluat myydä kirjan kaupoissa, se tarvitsee viivakoodin, joten saat ISBN:n. Taycherin blogikirjoituksessa mainitaan, että vaikka ISBN:t ovat hyödyllisiä, ne eivät ole universaaleja, koska ne otettiin käyttöön vasta 1970-luvun puolivälissä, eivätkä kaikkialla maailmassa. Silti ISBN on luultavasti laajimmin käytetty tunniste kirjan painoksille, joten se on paras lähtökohtamme. Jos voimme löytää kaikki maailman ISBN:t, saamme hyödyllisen listan kirjoista, jotka vielä tarvitsevat säilyttämistä. Joten, mistä saamme tiedot? On olemassa useita nykyisiä pyrkimyksiä, jotka yrittävät koota listan kaikista maailman kirjoista: Tässä kirjoituksessa olemme iloisia voidessamme ilmoittaa pienestä julkaisusta (verrattuna aiempiin Z-Library-julkaisuihimme). Kaavimme suurimman osan ISBNdb:stä ja teimme tiedot saataville torrenttina Pirate Library Mirrorin verkkosivustolla (EDIT: siirretty <a %(wikipedia_annas_archive)s>Annan Arkistoon</a>; emme linkitä sitä suoraan täällä, etsi se vain). Nämä ovat noin 30,9 miljoonaa tietuetta (20GB <a %(jsonlines)s>JSON Lines</a>; 4,4GB pakattuna). Heidän verkkosivustollaan he väittävät, että heillä on itse asiassa 32,6 miljoonaa tietuetta, joten saatamme jollain tavalla olla jääneet joistakin paitsi, tai <em>he</em> saattavat tehdä jotain väärin. Joka tapauksessa, toistaiseksi emme jaa tarkalleen, miten teimme sen — jätämme sen lukijan harjoitukseksi. ;-) Mitä jaamme, on joitakin alustavia analyysejä, jotta pääsisimme lähemmäksi maailman kirjojen määrän arvioimista. Tarkastelimme kolmea datasettiä: tätä uutta ISBNdb-datasettiä, alkuperäistä metadatajulkaisuamme, jonka kaavimme Z-Library-varjokirjastosta (joka sisältää Library Genesisin), ja Open Libraryn tietodumppia. ISBNdb-dumppi, tai Kuinka Monta Kirjaa On Säilytetty Ikuisesti? Jos deduplikoisimme varjokirjastojen tiedostot kunnolla, kuinka suuren osan kaikista maailman kirjoista olemme säilyttäneet? Päivityksiä <a %(wikipedia_annas_archive)s>Annin Arkistosta</a>, ihmiskunnan suurimmasta aidosti avoimesta kirjastosta. <em>WorldCat-uudistus</em> Data <strong>Muoto?</strong> <a %(blog)s>Annin Arkiston Kontit (AAC)</a>, joka on käytännössä <a %(jsonlines)s>JSON Lines</a> pakattuna <a %(zstd)s>Zstandardilla</a>, sekä joitakin standardoituja semantiikkoja. Nämä kontit sisältävät erilaisia tietueita, perustuen eri kaappauksiin, joita käytimme. Vuosi sitten <a %(blog)s>aloitimme</a> vastaamaan tähän kysymykseen: <strong>Mikä prosenttiosuus kirjoista on pysyvästi säilytetty varjokirjastoissa?</strong> Katsotaanpa joitakin perustietoja aineistosta: Kun kirja pääsee avoimen datan varjokirjastoon, kuten <a %(wikipedia_library_genesis)s>Library Genesis</a>, ja nyt <a %(wikipedia_annas_archive)s>Anna Arkisto</a>, se peilataan ympäri maailmaa (torrenttien kautta), mikä käytännössä säilyttää sen ikuisesti. Vastataksemme kysymykseen siitä, mikä prosenttiosuus kirjoista on säilytetty, meidän on tiedettävä nimittäjä: kuinka monta kirjaa on olemassa yhteensä? Ja ihanteellisesti meillä ei ole vain numero, vaan myös todellista metadataa. Sitten voimme paitsi verrata niitä varjokirjastoihin, myös <strong>luoda TODO-listan jäljellä olevista kirjoista, jotka täytyy säilyttää!</strong> Voisimme jopa alkaa unelmoida joukkoistetusta ponnistuksesta käydä läpi tämä TODO-lista. Olemme keränneet tietoja <a %(wikipedia_isbndb_com)s>ISBNdb:stä</a> ja ladanneet <a %(openlibrary)s>Open Library -aineiston</a>, mutta tulokset olivat epätyydyttäviä. Suurin ongelma oli, että ISBN-numeroiden päällekkäisyys oli vähäistä. Katso tämä Venn-diagrammi <a %(blog)s>blogikirjoituksestamme</a>: Olimme erittäin yllättyneitä siitä, kuinka vähän päällekkäisyyttä oli ISBNdb:n ja Open Libraryn välillä, vaikka molemmat sisältävät runsaasti tietoja eri lähteistä, kuten verkkokaappauksista ja kirjastotietueista. Jos molemmat onnistuvat löytämään suurimman osan ISBN-numeroista, niiden ympyröiden pitäisi varmasti olla merkittävästi päällekkäisiä tai toisen pitäisi olla toisen osajoukko. Tämä sai meidät miettimään, kuinka monta kirjaa jää <em>täysin näiden ympyröiden ulkopuolelle</em>? Tarvitsemme suuremman tietokannan. Silloin suuntasimme katseemme maailman suurimpaan kirjatietokantaan: <a %(wikipedia_worldcat)s>WorldCat</a>. Tämä on voittoa tavoittelemattoman <a %(wikipedia_oclc)s>OCLC:n</a> omistama tietokanta, joka kokoaa metadata-tietueita kirjastoista ympäri maailmaa vastineeksi siitä, että kirjastot saavat pääsyn koko aineistoon ja näkyvät loppukäyttäjien hakutuloksissa. Vaikka OCLC on voittoa tavoittelematon, heidän liiketoimintamallinsa edellyttää tietokannan suojaamista. No, olemme pahoillamme, ystävät OCLC:ssä, me annamme kaiken pois. :-) Viimeisen vuoden aikana olemme huolellisesti keränneet kaikki WorldCat-tietueet. Aluksi meillä kävi tuuri. WorldCat oli juuri julkaisemassa täydellistä verkkosivustonsa uudistusta (elokuussa 2022). Tämä sisälsi merkittävän uudistuksen heidän taustajärjestelmissään, mikä aiheutti monia tietoturva-aukkoja. Hyödynsimme tilaisuuden heti ja pystyimme keräämään satoja miljoonia (!) tietueita vain muutamassa päivässä. Sen jälkeen tietoturva-aukkoja korjattiin hitaasti yksi kerrallaan, kunnes viimeinen löytämämme aukko paikattiin noin kuukausi sitten. Siihen mennessä meillä oli käytännössä kaikki tietueet, ja tavoittelimme vain hieman laadukkaampia tietueita. Joten tunsimme, että on aika julkaista! 1,3 miljardia WorldCat-hakua <em><strong>TL;DR:</strong> Anna Arkisto kaappasi koko WorldCatin (maailman suurimman kirjaston metadata-kokoelman) tehdäkseen TODO-listan kirjoista, jotka täytyy säilyttää.</em> WorldCat Varoitus: tämä blogikirjoitus on vanhentunut. Olemme päättäneet, että IPFS ei ole vielä valmis laajaan käyttöön. Linkitämme edelleen tiedostoihin IPFS:ssä Annan Arkistosta, kun mahdollista, mutta emme enää isännöi niitä itse, emmekä suosittele muita peilaamaan IPFS:n avulla. Katso Torrentit-sivumme, jos haluat auttaa kokoelmamme säilyttämisessä. Auta kylvämään Z-Library IPFS:ssä Partneripalvelimen lataus SciDB Ulkoinen laina Ulkoinen laina (tulostus estetty) Ulkoinen lataus Tutki metadataa Sisältyy torrentteihin Takaisin  (+%(num)s bonus) maksamaton maksettu peruutettu vanhentunut odottaa Annan vahvistusta virheellinen Teksti jatkuu englanniksi. Siirry Nollaa Eteenpäin Viimeinen Jos sähköpostiosoitteesi ei toimi Libgen-foorumeilla, suosittelemme käyttämään <a %(a_mail)s>Proton Mailia</a> (ilmainen). Voit myös <a %(a_manual)s>pyytää manuaalisesti</a> tilisi aktivointia. (voidaan vaatia <a %(a_browser)s>selaimen vahvistus</a> — rajattomat lataukset!) Nopea Kumppanipalvelin #%(number)s (suositeltu) (hieman nopeampi, mutta jonotuslistalla) (ei selaimen vahvistusta vaadita) (ei selaimen vahvistusta tai jonotuslistoja) (ei jonotuslistaa, mutta voi olla erittäin hidas) Hidas kumppanipalvelin #%(number)s Äänikirja Sarjakuva Kirja (kaunokirjallisuus) Kirja (tietokirjallisuus) Kirja (tuntematon) Tiedelehtiartikkeli Aikakauslehti Nuottijulkaisu Muu Standardidokumentti Kaikkia sivuja ei voitu muuntaa PDF-muotoon Merkitty rikkinäiseksi Libgen.li:ssä Ei näkyvissä Libgen.li:ssä Ei näkyvissä Libgen.rs Fiction -osiossa Ei näkyvissä Libgen.rs Non-Fiction -osiossa Exiftoolin suorittaminen epäonnistui tässä tiedostossa Merkitty "huonoksi tiedostoksi" Z-Libraryssä Puuttuu Z-Librarystä Merkitty "roskapostiksi" Z-Libraryssä Tiedostoa ei voi avata (esim. vioittunut tiedosto, DRM) Tekijänoikeusvaatimus Latausongelmat (esim. ei voi yhdistää, virheilmoitus, erittäin hidas) Virheelliset metatiedot (esim. otsikko, kuvaus, kansikuva) Muut Huono laatu (esim. muotoiluongelmat, huono skannauslaatu, puuttuvat sivut) Roskaposti / tiedosto tulisi poistaa (esim. mainonta, loukkaava sisältö) %(amount)s (%(amount_usd)s) %(amount)s yhteensä %(amount)s (%(amount_usd)s) yhteensä Loistava Lukutoukka Onnekas Kirjastonhoitaja Häikäisevä Tietojenkerääjä Uskomaton Arkistoija Bonuksen lataukset Cerlalc Tšekkiläiset metatiedot DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA:n kontrolloitu digitaalinen lainaaminen ISBNdb ISBN GRP Libgen.li Pois lukien "scimag" Libgen.rs Tietokirjallisuus ja kaunokirjallisuus Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Venäjän valtion kirjasto Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Lataukset AA:han Z-Library Z-Library kiina Otsikko, tekijä, DOI, ISBN, MD5, … Haku Kirjailija Kuvaus ja metatiedot Painos Alkuperäinen tiedostonimi Julkaisija (hae tiettyä kenttää) Otsikko Julkaisuvuosi Tekniset tiedot Tällä kolikolla on tavallista korkeampi vähimmäismäärä. Valitse eri kesto tai eri kolikko. Pyyntöä ei voitu suorittaa. Yritä uudelleen muutaman minuutin kuluttua, ja jos ongelma jatkuu, ota meihin yhteyttä osoitteessa %(email)s ja liitä mukaan kuvakaappaus. Tapahtui tuntematon virhe. Ota yhteyttä meihin osoitteessa %(email)s ja liitä mukaan kuvakaappaus. Virhe maksun käsittelyssä. Odota hetki ja yritä uudelleen. Jos ongelma jatkuu yli 24 tuntia, ota yhteyttä osoitteeseen %(email)s ja liitä mukaan kuvakaappaus. Keräämme varoja <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">maailman suurimman sarjakuvien varjokirjaston</a> varmuuskopiointiin. Kiitos tuestasi! <a href="/donate">Lahjoita.</a> Jos et voi lahjoittaa, harkitse tukemista kertomalla ystävillesi ja seuraamalla meitä <a href="https://www.reddit.com/r/Annas_Archive">Redditissä</a> tai <a href="https://t.me/annasarchiveorg">Telegramissa</a>. Älä lähetä meille sähköpostia <a %(a_request)s>pyytääksesi kirjoja</a><br>tai pieniä (<10k) <a %(a_upload)s>latauksia</a>. Annan Arkisto DMCA / tekijänoikeusvaatimukset Pidä yhteyttä Reddit Vaihtoehdot SLUM (%(unaffiliated)s) riippumaton Annan Arkisto tarvitsee apuasi! Jos lahjoitat nyt, saat <strong>kaksinkertaisen</strong> määrän nopeita latauksia. Monet yrittävät kaataa meidät, mutta taistelemme vastaan. Jos lahjoitat tässä kuussa, saat <strong>kaksinkertaisen</strong> määrän nopeita latauksia. Voimassa tämän kuukauden loppuun asti. Ihmistiedon säilyttäminen: loistava lahja lomalle! Jäsenyyksiä jatketaan vastaavasti. Yhteistyöpalvelimet eivät ole käytettävissä isännöinnin sulkemisten vuoksi. Ne pitäisi olla pian taas toiminnassa. Lisätäksemme Annin Arkiston kestävyyttä, etsimme vapaaehtoisia ylläpitämään peilejä. Meillä on uusi lahjoitusmenetelmä saatavilla: %(method_name)s. Harkitse %(donate_link_open_tag)slahjoittamista</a> — tämän sivuston ylläpitäminen ei ole halpaa, ja lahjoituksesi todella tekee eron. Kiitos paljon. Suosittele ystävää, ja sekä sinä että ystäväsi saatte %(percentage)s%% bonusnopeita latauksia! Yllätä rakkaasi, anna heille tili jäsenyydellä. Täydellinen ystävänpäivälahja! Lue lisää… Tili Toiminta Edistynyt Annan blogi ↗ Annan ohjelmisto ↗ beta Koodien tutkija Datasets Lahjoita Ladatut tiedostot UKK Koti Paranna metatietoja LLM-data Kirjaudu sisään / Rekisteröidy Lahjoitukseni Julkinen profiili Haku Turvallisuus Torrentit Käännä ↗ Vapaaehtoistyö & Palkkiot Viimeisimmät lataukset: 📚&nbsp;Maailman suurin avoimen lähdekoodin ja avoimen datan kirjasto. ⭐️&nbsp;Peilit Sci-Hub, Library Genesis, Z-Library ja paljon muuta. 📈&nbsp;%(book_any)s kirjaa, %(journal_article)s artikkelia, %(book_comic)s sarjakuvaa, %(magazine)s aikakauslehteä — säilytetty ikuisesti.  ja  ja paljon muuta DuXiu Internet Archive -lainauskirjasto LibGen 📚&nbsp;Suurin aidosti avoin kirjasto ihmiskunnan historiassa. 📈&nbsp;%(book_count)s&nbsp;kirjat, %(paper_count)s&nbsp;tutkimukset — säilytetty ikuisesti. ⭐️&nbsp;Peilaamme %(libraries)s. Keräämme ja julkaisemme avoimena lähdekoodina %(scraped)s. Kaikki koodimme ja datamme ovat täysin avoimen lähdekoodin. OpenLib Sci-Hub ,  📚 Maailman suurin avoimen lähdekoodin ja avoimen datan kirjasto.<br>⭐️ Peilaa Scihubia, Libgeniä, Zlibiä ja muita. Z-Lib Annan Arkisto Virheellinen pyyntö. Vieraile osoitteessa %(websites)s. Maailman suurin avoimen lähdekoodin ja avoimen datan kirjasto. Peilit Sci-Hub, Library Genesis, Z-Library ja paljon muuta. Etsi Annan Arkistosta Annan Arkisto Päivitä sivu ja yritä uudelleen. <a %(a_contact)s>Ota yhteyttä</a>, jos ongelma jatkuu useita tunteja. 🔥 Ongelma tämän sivun lataamisessa <li>1. Seuraa meitä <a href="https://www.reddit.com/r/Annas_Archive/">Redditissä</a> tai <a href="https://t.me/annasarchiveorg">Telegramissa</a>.</li><li>2. Levitä sanaa Anna’s Archivesta Twitterissä, Redditissä, Tiktokissa, Instagramissa, paikallisessa kahvilassa tai kirjastossa, tai missä tahansa liikutkin! Emme usko portinvartiointiin — jos meidät poistetaan, ilmestymme vain uudelleen jossain muualla, sillä kaikki koodimme ja datamme ovat täysin avoimen lähdekoodin.</li><li>3. Jos pystyt, harkitse <a href="/donate">lahjoittamista</a>.</li><li>4. Auta <a href="https://translate.annas-software.org/">kääntämään</a> verkkosivustomme eri kielille.</li><li>5. Jos olet ohjelmistoinsinööri, harkitse osallistumista <a href="https://annas-software.org/">avoimen lähdekoodin</a> projekteihimme tai torrenttiemme <a href="/datasets">jakamiseen</a>.</li> 10. Luo tai auta ylläpitämään Anna’s Archiven Wikipedia-sivua omalla kielelläsi. 11. Etsimme pieniä, tyylikkäitä mainoksia. Jos haluat mainostaa Anna’s Archivessa, ilmoitathan siitä meille. 6. Jos olet tietoturvatutkija, voimme hyödyntää taitojasi sekä hyökkäyksessä että puolustuksessa. Tutustu <a %(a_security)s>Tietoturva</a>-sivuumme. 7. Etsimme asiantuntijoita anonyymien kauppiaiden maksuihin. Voitko auttaa meitä lisäämään kätevämpiä tapoja lahjoittaa? PayPal, WeChat, lahjakortit. Jos tunnet jonkun, ota yhteyttä. 8. Etsimme aina lisää palvelinkapasiteettia. 9. Voit auttaa raportoimalla tiedosto-ongelmista, jättämällä kommentteja ja luomalla listoja suoraan tällä sivustolla. Voit myös auttaa <a %(a_upload)s>lataamalla lisää kirjoja</a> tai korjaamalla olemassa olevien kirjojen tiedosto-ongelmia tai muotoilua. Lisätietoja vapaaehtoistyöstä löytyy <a %(a_volunteering)s>Vapaaehtoistyö & Palkkiot</a> -sivultamme. Uskomme vahvasti tiedon vapaaseen kulkuun sekä tiedon ja kulttuurin säilyttämiseen. Tämän hakukoneen avulla rakennamme jättiläisten harteille. Kunnioitamme syvästi niiden ihmisten kovaa työtä, jotka ovat luoneet erilaisia varjokirjastoja, ja toivomme, että tämä hakukone laajentaa niiden tavoittavuutta. Pysyäksesi ajan tasalla edistymisestämme, seuraa Annaa <a href="https://www.reddit.com/r/Annas_Archive/">Redditissä</a> tai <a href="https://t.me/annasarchiveorg">Telegramissa</a>. Kysymyksiä ja palautetta varten ota yhteyttä Annaan osoitteessa %(email)s. Tilin tunnus: %(account_id)s Kirjaudu ulos ❌ Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudelleen. ✅ Olet nyt kirjautunut ulos. Lataa sivu uudelleen kirjautuaksesi sisään. Nopeita latauksia käytetty (viimeiset 24 tuntia): <strong>%(used)s / %(total)s</strong> Jäsenyys: <strong>%(tier_name)s</strong> asti %(until_date)s <a %(a_extend)s>(jatka)</a> Voit yhdistää useita jäsenyyksiä (nopeat lataukset per 24 tuntia lisätään yhteen). Jäsenyys: <strong>Ei mitään</strong> <a %(a_become)s>(liity jäseneksi)</a> Ota yhteyttä Annaan osoitteessa %(email)s, jos olet kiinnostunut päivittämään jäsenyytesi korkeampaan tasoon. Julkinen profiili: %(profile_link)s Salainen avain (älä jaa!): %(secret_key)s näytä Liity mukaan täällä! Päivitä <a %(a_tier)s>korkeampaan tasoon</a> liittyäksesi ryhmäämme. Eksklusiivinen Telegram-ryhmä: %(link)s Tili mitkä lataukset? Kirjaudu sisään Älä hukkaa avaintasi! Virheellinen salainen avain. Varmista avain ja yritä uudelleen, tai vaihtoehtoisesti rekisteröidy uudelleen alla. Salainen avain Syötä salainen avain kirjautuaksesi: Vanha sähköpostipohjainen tili? Syötä <a %(a_open)s>sähköpostisi tähän</a>. Rekisteröi uusi tili Eikö sinulla ole vielä tiliä? Rekisteröinti onnistui! Salainen avain on: <span %(span_key)s>%(key)s</span> Säilytä tämä avain huolellisesti. Jos kadotat sen, menetät pääsyn tilillesi. <li %(li_item)s><strong>Kirjanmerkki.</strong> Voit lisätä tämän sivun kirjanmerkkeihin hakeaksesi avaimen myöhemmin.</li><li %(li_item)s><strong>Lataa.</strong> Napsauta <a %(a_download)s>tästä linkistä</a> ladataksesi avaimen.</li><li %(li_item)s><strong>Salasananhallinta.</strong> Käytä salasananhallintaa tallentaaksesi avaimen, kun syötät sen alla.</li> Kirjaudu sisään / Rekisteröidy Selaimen vahvistus Varoitus: koodissa on virheellisiä Unicode-merkkejä, ja se saattaa toimia virheellisesti eri tilanteissa. Raakabinaari voidaan dekoodata base64-esityksestä URL-osoitteessa. Kuvaus Tunniste Etuliite URL tietylle koodille Verkkosivusto Koodit, jotka alkavat “%(prefix_label)s” Älä kaavi näitä sivuja. Sen sijaan suosittelemme <a %(a_import)s>luomaan</a> tai <a %(a_download)s>lataamaan</a> ElasticSearch- ja MariaDB-tietokantamme ja ajamaan <a %(a_software)s>avointa lähdekoodiamme</a>. Raakadataa voi tutkia manuaalisesti JSON-tiedostojen, kuten <a %(a_json_file)s>tämän</a>, kautta. Alle %(count)s tietuetta Yleinen URL Koodien tutkija Hakemisto Tutki koodeja, joilla tietueet on merkitty, etuliitteen mukaan. "Tietueet" -sarake näyttää, kuinka monta tietuetta on merkitty koodeilla, joilla on annettu etuliite, kuten hakukoneessa näkyy (mukaan lukien vain metadataa sisältävät tietueet). "Koodit" -sarake näyttää, kuinka monta todellista koodia on annettu etuliite. Tunnettu koodin etuliite "%(key)s" Lisää… Etuliite %(count)s tietue, joka vastaa "%(prefix_label)s" %(count)s tietuetta, jotka vastaavat "%(prefix_label)s" koodit tietueet "%%t" korvataan koodin arvolla Etsi Annan Arkisto Koodit URL tietylle koodille: “%(url)s” Tämän sivun luominen voi kestää hetken, minkä vuoksi se vaatii Cloudflare captchan. <a %(a_donate)s>Jäsenet</a> voivat ohittaa captchan. Väärinkäytös ilmoitettu: Parempi versio Haluatko ilmoittaa tästä käyttäjästä sopimattomasta tai loukkaavasta käytöksestä? Tiedosto-ongelma: %(file_issue)s piilotettu kommentti Vastaa Ilmoita väärinkäytöstä Ilmoitit tästä käyttäjästä väärinkäytöksestä. Tekijänoikeusvaatimuksia tähän sähköpostiin ei huomioida; käytä sen sijaan lomaketta. Näytä sähköposti Otamme erittäin mielellämme vastaan palautetta ja kysymyksiä! Kuitenkin, roskapostin ja turhien sähköpostien määrän vuoksi, tarkistathan ruudut vahvistaaksesi, että ymmärrät nämä yhteydenottoehdot. Kaikki muut tavat ottaa meihin yhteyttä tekijänoikeusvaatimuksista poistetaan automaattisesti. DMCA / tekijänoikeusvaatimuksia varten käytä <a %(a_copyright)s>tätä lomaketta</a>. Yhteyssähköposti URL-osoitteet Anna’s Archivessa (pakollinen). Yksi per rivi. Sisällytä vain URL-osoitteet, jotka kuvaavat täsmälleen samaa kirjan painosta. Jos haluat tehdä vaatimuksen useista kirjoista tai useista painoksista, lähetä tämä lomake useita kertoja. Vaatimukset, jotka niputtavat useita kirjoja tai painoksia yhteen, hylätään. Osoite (pakollinen) Selkeä kuvaus lähdemateriaalista (pakollinen) Sähköposti (pakollinen) URL-osoitteet lähdemateriaaliin, yksi per rivi (pakollinen). Sisällytä mahdollisimman monta, jotta voimme vahvistaa vaatimuksesi (esim. Amazon, WorldCat, Google Books, DOI). Lähdemateriaalin ISBN-numerot (jos sovellettavissa). Yksi per rivi. Sisällytä vain ne, jotka täsmäävät tarkasti sen painoksen kanssa, josta teet tekijänoikeusvaatimuksen. Nimesi (pakollinen) ❌ Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudelleen. ✅ Kiitos tekijänoikeusvaatimuksesi lähettämisestä. Käsittelemme sen mahdollisimman pian. Lataa sivu uudelleen lähettääksesi toisen. <a %(a_openlib)s>Open Library</a> -URL-osoitteet lähdemateriaalista, yksi per rivi. Käytä hetki aikaa etsiäksesi lähdemateriaalisi Open Librarysta. Tämä auttaa meitä vahvistamaan vaatimuksesi. Puhelinnumero (pakollinen) Lausunto ja allekirjoitus (pakollinen) Lähetä vaatimus Jos sinulla on DCMA- tai muu tekijänoikeusvaatimus, täytä tämä lomake mahdollisimman tarkasti. Jos kohtaat ongelmia, ota yhteyttä DMCA-osoitteeseemme: %(email)s. Huomaa, että tähän osoitteeseen lähetettyjä vaatimuksia ei käsitellä, se on vain kysymyksiä varten. Käytä alla olevaa lomaketta vaatimusten lähettämiseen. DMCA / Tekijänoikeusvaatimuslomake Esimerkkitietue Anna’s Archivessa Anna’s Archiven torrentit Anna’s Archive Containers -formaatti Skriptit metatietojen tuontiin Jos olet kiinnostunut peilaamaan tätä tietokantaa <a %(a_archival)s>arkistointia</a> tai <a %(a_llm)s>LLM-koulutusta</a> varten, ota meihin yhteyttä. Viimeksi päivitetty: %(date)s Pääsivusto %(source)s Metatietojen dokumentaatio (useimmat kentät) Anna’s Archiven peilaamat tiedostot: %(count)s (%(percent)s%%) Resurssit Tiedostojen kokonaismäärä: %(count)s Tiedostojen kokonaiskoko: %(size)s Blogikirjoituksemme tästä datasta <a %(duxiu_link)s>Duxiu</a> on valtava skannattujen kirjojen tietokanta, jonka on luonut <a %(superstar_link)s>SuperStar Digital Library Group</a>. Suurin osa on akateemisia kirjoja, jotka on skannattu, jotta ne olisivat digitaalisesti saatavilla yliopistoille ja kirjastoille. Englanninkieliselle yleisöllemme <a %(princeton_link)s>Princeton</a> ja <a %(uw_link)s>Washingtonin yliopisto</a> tarjoavat hyviä yleiskatsauksia. Lisäksi on erinomainen artikkeli, joka antaa enemmän taustatietoa: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Duxiun kirjoja on pitkään piratoitu Kiinan internetissä. Yleensä jälleenmyyjät myyvät niitä alle dollarilla. Ne jaetaan tyypillisesti Kiinan Google Driven vastineen kautta, jota on usein hakkeroitu tarjoamaan enemmän tallennustilaa. Joitakin teknisiä yksityiskohtia löytyy <a %(link1)s>täältä</a> ja <a %(link2)s>täältä</a>. Vaikka kirjat on jaettu puolijulkisesti, niiden hankkiminen suurina määrinä on melko vaikeaa. Tämä oli korkealla TODO-listallamme, ja varasimme useita kuukausia kokopäiväistä työtä sen toteuttamiseksi. Kuitenkin loppuvuodesta 2023 uskomaton, mahtava ja lahjakas vapaaehtoinen otti meihin yhteyttä ja kertoi tehneensä kaiken tämän työn jo — suurin kustannuksin. He jakoivat koko kokoelman kanssamme odottamatta mitään vastineeksi, paitsi pitkäaikaisen säilytyksen takaamisen. Todella merkittävää. Lisätietoja vapaaehtoisiltamme (raakatiedot): Mukautettu <a %(a_href)s>blogikirjoituksestamme</a>. DuXiu 读秀 %(count)s tiedosto %(count)s tiedostot Tämä tietokanta liittyy läheisesti <a %(a_datasets_openlib)s>Open Library -tietokantaan</a>. Se sisältää kaavinnan kaikista metatiedoista ja suuren osan IA:n Controlled Digital Lending Libraryn tiedostoista. Päivitykset julkaistaan <a %(a_aac)s>Anna’s Archive Containers -muodossa</a>. Nämä tietueet viitataan suoraan Open Library -tietokannasta, mutta ne sisältävät myös tietueita, joita ei ole Open Libraryssa. Meillä on myös useita yhteisön jäsenten vuosien varrella kaapimia tietotiedostoja. Kokoelma koostuu kahdesta osasta. Tarvitset molemmat osat saadaksesi kaikki tiedot (paitsi vanhentuneet torrentit, jotka on yliviivattu torrenttisivulla). Digitaalinen lainakirjasto ensimmäinen julkaisumme, ennen kuin vakiinnutimme <a %(a_aac)s>Anna’s Archive Containers (AAC) -formaatin</a>. Sisältää metatietoja (json- ja xml-muodossa), pdf-tiedostoja (acsm- ja lcpdf-digitaalisista lainausjärjestelmistä) ja kansikuvien pikkukuvia. inkrementaaliset uudet julkaisut, käyttäen AAC:ta. Sisältää vain metatietoja aikaleimoilla 2023-01-01 jälkeen, koska loput on jo katettu "ia":lla. Myös kaikki pdf-tiedostot, tällä kertaa acsm- ja "bookreader" (IA:n verkkolukija) -lainausjärjestelmistä. Vaikka nimi ei olekaan täysin oikea, lisäämme silti bookreader-tiedostot ia2_acsmpdf_files-kokoelmaan, koska ne ovat toisensa poissulkevia. IA:n hallittu digitaalinen lainaaminen 98%%+ tiedostoista on haettavissa. Missiomme on arkistoida kaikki maailman kirjat (sekä artikkelit, lehdet jne.) ja tehdä ne laajalti saatavilla. Uskomme, että kaikkien kirjojen tulisi olla laajasti peilattuja, jotta varmuus ja kestävyys varmistetaan. Siksi keräämme tiedostoja useista lähteistä. Jotkut lähteet ovat täysin avoimia ja voidaan peilata suurina määrinä (kuten Sci-Hub). Toiset ovat suljettuja ja suojelevia, joten yritämme kaapia niitä “vapauttaaksemme” niiden kirjat. Toiset taas ovat jossain näiden välissä. Kaikki datamme voidaan <a %(a_torrents)s>torrenttaa</a>, ja kaikki metadatamme voidaan <a %(a_anna_software)s>luoda</a> tai <a %(a_elasticsearch)s>ladata</a> ElasticSearch- ja MariaDB-tietokantoina. Raakadataa voidaan tutkia manuaalisesti JSON-tiedostojen, kuten <a %(a_dbrecord)s>tämän</a>, kautta. Metatiedot ISBN-verkkosivusto Viimeksi päivitetty: %(isbn_country_date)s (%(link)s) Resurssit Kansainvälinen ISBN-virasto julkaisee säännöllisesti alueet, jotka se on myöntänyt kansallisille ISBN-virastoille. Tästä voimme päätellä, mihin maahan, alueeseen tai kieliryhmään tämä ISBN kuuluu. Käytämme tällä hetkellä näitä tietoja epäsuorasti <a %(a_isbnlib)s>isbnlib</a> Python-kirjaston kautta. ISBN-maa tiedot Tämä on suuri määrä isbndb.com-sivustolle tehtyjä pyyntöjä syyskuussa 2022. Yritimme kattaa kaikki ISBN-alueet. Näitä on noin 30,9 miljoonaa tietuetta. Heidän verkkosivustollaan he väittävät, että heillä on itse asiassa 32,6 miljoonaa tietuetta, joten saatamme jollain tavalla olla jääneet jostain paitsi, tai <em>he</em> saattavat tehdä jotain väärin. JSON-vastaukset ovat melko raakoja heidän palvelimeltaan. Yksi havaittu tietojen laatuongelma on, että ISBN-13-numeroille, jotka alkavat eri etuliitteellä kuin "978-", he sisällyttävät silti "isbn"-kentän, joka yksinkertaisesti on ISBN-13-numero, josta on poistettu ensimmäiset 3 numeroa (ja tarkistusnumero laskettu uudelleen). Tämä on selvästi väärin, mutta näin he näyttävät tekevän, joten emme muuttaneet sitä. Toinen mahdollinen ongelma, johon saatat törmätä, on se, että "isbn13"-kentässä on kaksoiskappaleita, joten et voi käyttää sitä ensisijaisena avaimena tietokannassa. "isbn13"+"isbn"-kentät yhdistettynä näyttävät kuitenkin olevan ainutlaatuisia. Julkaisu 1 (2022-10-31) Kaunokirjallisuuden torrentit ovat jäljessä (vaikka ID:t ~4-6M eivät ole torrentteina, koska ne menevät päällekkäin Zlib-torrenttiemme kanssa). Blogikirjoituksemme sarjakuvien julkaisusta Sarjakuvien torrentit Annan Arkistossa Eri Library Genesis -haarojen taustatarinan näet <a %(a_libgen_rs)s>Libgen.rs</a> -sivulta. Libgen.li sisältää suurimman osan samasta sisällöstä ja metatiedoista kuin Libgen.rs, mutta sillä on joitakin lisäkokoelmia, nimittäin sarjakuvia, aikakauslehtiä ja standardidokumentteja. Se on myös integroinut <a %(a_scihub)s>Sci-Hub</a>:n metatietoihinsa ja hakukoneeseensa, jota käytämme tietokannassamme. Tämän kirjaston metatiedot ovat vapaasti saatavilla <a %(a_libgen_li)s>libgen.li</a>. Tämä palvelin on kuitenkin hidas eikä tue katkenneiden yhteyksien jatkamista. Samat tiedostot ovat myös saatavilla <a %(a_ftp)s>FTP-palvelimella</a>, joka toimii paremmin. Tietokirjallisuus näyttää myös eriytyneen, mutta ilman uusia torrentteja. Tämä näyttää tapahtuneen vuoden 2022 alusta lähtien, vaikka emme ole vahvistaneet tätä. Libgen.li:n ylläpitäjän mukaan "fiction_rus" (venäläinen kaunokirjallisuus) -kokoelman pitäisi olla katettu säännöllisesti julkaistuilla torrenteilla <a %(a_booktracker)s>booktracker.org</a>:sta, erityisesti <a %(a_flibusta)s>flibusta</a> ja <a %(a_librusec)s>lib.rus.ec</a> torrentit (joita peilaamme <a %(a_torrents)s>täällä</a>, vaikka emme ole vielä selvittäneet, mitkä torrentit vastaavat mitäkin tiedostoja). Kaunokirjallisuuskokoelmalla on omat torrenttinsa (eriytynyt <a %(a_href)s>Libgen.rs:stä</a>) alkaen %(start)s. Tietyt alueet ilman torrenteja (kuten kaunokirjallisuusalueet f_3463000 - f_4260000) ovat todennäköisesti Z-Libraryn (tai muiden kaksoiskappaleiden) tiedostoja, vaikka saatamme haluta tehdä deduplikointia ja luoda torrentteja lgli-ainutlaatuisille tiedostoille näillä alueilla. Tilastot kaikista kokoelmista löytyvät <a %(a_href)s>libgenin verkkosivustolta</a>. Torrentit ovat saatavilla suurimmalle osalle lisäsisällöstä, erityisesti sarjakuvien, aikakauslehtien ja standardidokumenttien torrentit on julkaistu yhteistyössä Annan Arkiston kanssa. Huomaa, että torrent-tiedostot, jotka viittaavat "libgen.is" ovat nimenomaisesti peilejä <a %(a_libgen)s>Libgen.rs</a>:stä ("is" on eri domain, jota Libgen.rs käyttää). Hyödyllinen resurssi metadatan käyttämiseen on <a %(a_href)s>tämä sivu</a>. %(icon)s Heidän "fiction_rus" -kokoelmallaan (venäläinen kaunokirjallisuus) ei ole omia torrentteja, mutta se on katettu muiden torrenttien kautta, ja pidämme <a %(fiction_rus)s>peiliä</a>. Venäläisen kaunokirjallisuuden torrentit Annan Arkistossa Kaunokirjallisuuden torrentit Annan Arkistossa Keskustelufoorumi Metadata Metadata FTP:n kautta Aikakauslehtien torrentit Annan Arkistossa Metadatakenttien tiedot Peili muista torrenteista (ja ainutlaatuiset kaunokirjallisuuden ja sarjakuvien torrentit) Standardidokumenttien torrentit Annan Arkistossa Libgen.li Torrentit Annan Arkistosta (kirjan kannet) Library Genesis on tunnettu siitä, että se tarjoaa anteliaasti tietojaan suurina erinä torrenttien kautta. Libgen-kokoelmamme koostuu lisätiedoista, joita he eivät julkaise suoraan, yhteistyössä heidän kanssaan. Suuret kiitokset kaikille Library Genesisin kanssa työskenteleville! Blogimme kirjan kansien julkaisusta Tämä sivu koskee ".rs" versiota. Se tunnetaan siitä, että se julkaisee johdonmukaisesti sekä metadatansa että koko kirjaluettelonsa sisällön. Sen kirjakokoelma on jaettu kaunokirjallisuuden ja tietokirjallisuuden osiin. Hyödyllinen resurssi metadatan käyttämiseen on <a %(a_metadata)s>tämä sivu</a> (estää IP-alueita, VPN saattaa olla tarpeen). Vuodesta 2024-03 alkaen uusia torrentteja julkaistaan <a %(a_href)s>tässä foorumiketjussa</a> (estää IP-alueet, VPN saattaa olla tarpeen). Kaunokirjallisuuden torrentit Annan Arkistossa Libgen.rs Kaunokirjallisuuden torrentit Libgen.rs Keskustelufoorumi Libgen.rs Metadata Libgen.rs metatietokenttien tiedot Libgen.rs Tietokirjallisuuden torrentit Tietokirjallisuuden torrentit Annan Arkistossa %(example)s kaunokirjalle. Tämä <a %(blog_post)s>ensimmäinen julkaisu</a> on melko pieni: noin 300GB kirjan kansia Libgen.rs haarasta, sekä kauno- että tietokirjallisuutta. Ne on järjestetty samalla tavalla kuin ne näkyvät libgen.rs:ssä, esim.: %(example)s tietokirjalle. Aivan kuten Z-Library-kokoelman kanssa, laitamme ne kaikki suureen .tar-tiedostoon, joka voidaan liittää käyttämällä <a %(a_ratarmount)s>ratarmount</a>, jos haluat palvella tiedostoja suoraan. Julkaisu 1 (%(date)s) Lyhyt tarina eri Library Genesis (tai "Libgen") haaroista on, että ajan myötä eri ihmiset, jotka olivat mukana Library Genesisissä, ajautuivat erilleen ja lähtivät omille teilleen. Tämän <a %(a_mhut)s>foorumiviestin</a> mukaan Libgen.li oli alun perin isännöity osoitteessa ”http://free-books.dontexist.com”. ".fun" versio luotiin alkuperäisen perustajan toimesta. Sitä uudistetaan uuden, hajautetumman version hyväksi. <a %(a_li)s>".li" versiossa</a> on valtava kokoelma sarjakuvia sekä muuta sisältöä, joka ei (vielä) ole saatavilla suurina torrent-latauksina. Sillä on erillinen kaunokirjallisuuden torrent-kokoelma, ja se sisältää <a %(a_scihub)s>Sci-Hub</a>:n metadataa tietokannassaan. ".rs" versiossa on hyvin samanlaista dataa, ja se julkaisee kokoelmansa johdonmukaisesti suurina torrentteina. Se on karkeasti jaettu "kaunokirjallisuuden" ja "tietokirjallisuuden" osioihin. Alun perin osoitteessa ”http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> on jossain määrin myös Library Genesisin haara, vaikka he käyttivät projektissaan eri nimeä. Libgen.rs Täydennämme kokoelmaamme myös vain metatietolähteillä, jotka voimme yhdistää tiedostoihin esimerkiksi ISBN-numeroiden tai muiden kenttien avulla. Alla on yleiskatsaus näistä. Jälleen, jotkut näistä lähteistä ovat täysin avoimia, kun taas toiset meidän on kerättävä. Huomaa, että metatietohaussa näytämme alkuperäiset tiedot. Emme yhdistä tietueita. Vain metatietolähteet Open Library on Internet Archive -projektin avoimen lähdekoodin hanke, jonka tavoitteena on luetteloida kaikki maailman kirjat. Sillä on yksi maailman suurimmista kirjojen skannausoperaatioista, ja sillä on monia kirjoja saatavilla digitaaliseen lainaukseen. Sen kirjan metatietoluettelo on vapaasti ladattavissa ja sisältyy Annan Arkistoon (vaikkakaan ei tällä hetkellä haussa, paitsi jos haet nimenomaan Open Library ID:llä). Open Library Poislukien kaksoiskappaleet Viimeksi päivitetty Tiedostojen määrän prosenttiosuudet %% peilattu AA:n toimesta / torrentit saatavilla Koko Lähde Alla on nopea yleiskatsaus Anna’s Archiven tiedostojen lähteistä. Koska varjokirjastot synkronoivat usein tietoja toisiltaan, kirjastoissa on huomattavaa päällekkäisyyttä. Siksi luvut eivät täsmää kokonaismäärään. "Peilattu ja Anna’s Archive -sivuston jakama" prosenttiosuus näyttää, kuinka monta tiedostoa peilaamme itse. Jaamme nämä tiedostot suurina erinä torrenttien kautta ja teemme ne saataville suoraan ladattaviksi kumppanisivustojen kautta. Yleiskatsaus Yhteensä Torrentit Annan Arkistossa Lisätietoja Sci-Hubista löytyy sen <a %(a_scihub)s>virallisilta verkkosivuilta</a>, <a %(a_wikipedia)s>Wikipedia-sivulta</a> ja tästä <a %(a_radiolab)s>podcast-haastattelusta</a>. Huomaa, että Sci-Hub on ollut <a %(a_reddit)s>jäädytetty vuodesta 2021</a>. Se oli jäädytetty aiemminkin, mutta vuonna 2021 lisättiin muutama miljoona artikkelia. Silti joitakin rajoitettuja määriä artikkeleita lisätään Libgenin "scimag"-kokoelmiin, mutta ei tarpeeksi uusien suurten torrenttien julkaisemiseksi. Käytämme Sci-Hubin metadataa, jonka tarjoaa <a %(a_libgen_li)s>Libgen.li</a> sen "scimag"-kokoelmassa. Käytämme myös <a %(a_dois)s>dois-2022-02-12.7z</a>-datasettiä. Huomaa, että "smarch"-torrentit ovat <a %(a_smarch)s>vanhentuneita</a> eikä niitä siksi sisällytetä torrenttilistallemme. Torrentit Libgen.li:ssä Torrentit Libgen.rs:ssä Metadata ja torrentit Päivitykset Redditissä Podcast-haastattelu Wikipedia-sivu Sci-Hub Sci-Hub: jäädytetty vuodesta 2021; suurin osa saatavilla torrenttien kautta Libgen.li: pieniä lisäyksiä siitä lähtien</div> Jotkin lähdekirjastot edistävät tietojensa laajamittaista jakamista torrenttien kautta, kun taas toiset eivät jaa kokoelmaansa helposti. Jälkimmäisessä tapauksessa Anna’s Archive yrittää kerätä heidän kokoelmansa ja tehdä ne saataville (katso <a %(a_torrents)s>Torrents</a>-sivumme). On myös välimuotoja, esimerkiksi tapauksia, joissa lähdekirjastot ovat halukkaita jakamaan, mutta heillä ei ole resursseja siihen. Näissä tapauksissa yritämme myös auttaa. Alla on yleiskatsaus siitä, miten olemme vuorovaikutuksessa eri lähdekirjastojen kanssa. Lähdekirjastot %(icon)s Erilaisia tiedostotietokantoja hajallaan Kiinan internetissä; usein maksullisia tietokantoja %(icon)s Useimmat tiedostot saatavilla vain premium BaiduYun -tileillä; hitaat latausnopeudet. %(icon)s Annan Arkisto hallinnoi <a %(duxiu)s>DuXiu-tiedostojen</a> kokoelmaa %(icon)s Erilaisia metatietokantoja hajallaan Kiinan internetissä; usein kuitenkin maksullisia tietokantoja %(icon)s Ei helposti saatavilla olevia metatietojen kaatopaikkoja koko kokoelmalle. %(icon)s Annan arkisto hallinnoi kokoelmaa <a %(duxiu)s>DuXiu-metatietoja</a> Tiedostot %(icon)s Tiedostot saatavilla vain rajoitetusti lainattavaksi, erilaisin käyttörajoituksin %(icon)s Annan Arkisto hallinnoi <a %(ia)s>IA-tiedostojen</a> kokoelmaa %(icon)s Joitakin metatietoja saatavilla <a %(openlib)s>Open Library -tietokantadumppien</a> kautta, mutta ne eivät kata koko IA-kokoelmaa %(icon)s Ei helposti saatavilla olevia metatietojen kaatopaikkoja koko kokoelmalle %(icon)s Annan Arkisto hallinnoi <a %(ia)s>IA-metatietojen</a> kokoelmaa Viimeksi päivitetty %(icon)s Annan Arkisto ja Libgen.li hallinnoivat yhdessä <a %(comics)s>sarjakuvien</a>, <a %(magazines)s>aikakauslehtien</a>, <a %(standarts)s>standardidokumenttien</a> ja <a %(fiction)s>kaunokirjallisuuden (eriytynyt Libgen.rs:stä)</a> kokoelmia. %(icon)s Tietokirjallisuuden torrentit jaetaan Libgen.rs:n kanssa (ja peilataan <a %(libgenli)s>täällä</a>). %(icon)s Neljännesvuosittaiset <a %(dbdumps)s>HTTP-tietokannan kaatopaikat</a> %(icon)s Automaattiset torrentit <a %(nonfiction)s>Tietokirjallisuudelle</a> ja <a %(fiction)s>Kaunokirjallisuudelle</a> %(icon)s Annan arkisto hallinnoi kokoelmaa <a %(covers)s>kirjankansitorrentteja</a> %(icon)s Päivittäiset <a %(dbdumps)s>HTTP-tietokantakaatopaikat</a> Metatiedot %(icon)s Kuukausittaiset <a %(dbdumps)s>tietokantadumpit</a> %(icon)s Datatorrentit saatavilla <a %(scihub1)s>täällä</a>, <a %(scihub2)s>täällä</a> ja <a %(libgenli)s>täällä</a> %(icon)s Joitakin uusia tiedostoja <a %(libgenrs)s>lisätään</a> Libgenin "scimag"-kokoelmaan, mutta ei tarpeeksi uusien torrenttien luomiseen %(icon)s Sci-Hub on jäädyttänyt uudet tiedostot vuodesta 2021 lähtien. %(icon)s Metatietojen kaatopaikat saatavilla <a %(scihub1)s>täällä</a> ja <a %(scihub2)s>täällä</a>, sekä osana <a %(libgenli)s>Libgen.li-tietokantaa</a> (jota käytämme) Lähde %(icon)s Erilaisia pienempiä tai kertaluonteisia lähteitä. Kannustamme ihmisiä lataamaan ensin muihin varjokirjastoihin, mutta joskus ihmisillä on kokoelmia, jotka ovat liian suuria muiden lajiteltavaksi, mutta eivät tarpeeksi suuria oman kategorian luomiseen. %(icon)s Ei saatavilla suoraan suurina erinä, suojattu kaapimista vastaan %(icon)s Annan arkisto hallinnoi kokoelmaa <a %(worldcat)s>OCLC (WorldCat) -metatietoja</a> %(icon)s Annan Arkisto ja Z-Library hallinnoivat yhdessä <a %(metadata)s>Z-Libraryn metatietojen</a> ja <a %(files)s>Z-Libraryn tiedostojen</a> kokoelmaa Datasets Yhdistämme kaikki yllä mainitut lähteet yhdeksi yhtenäiseksi tietokannaksi, jota käytämme tämän verkkosivuston palvelemiseen. Tämä yhtenäinen tietokanta ei ole suoraan saatavilla, mutta koska Anna’s Archive on täysin avoimen lähdekoodin, se voidaan melko helposti <a %(a_generated)s>luoda</a> tai <a %(a_downloaded)s>ladata</a> ElasticSearch- ja MariaDB-tietokantoina. Tällä sivulla olevat skriptit lataavat automaattisesti kaikki tarvittavat metatiedot yllä mainituista lähteistä. Jos haluat tutkia tietojamme ennen näiden skriptien suorittamista paikallisesti, voit katsoa JSON-tiedostojamme, jotka linkittävät edelleen muihin JSON-tiedostoihin. <a %(a_json)s>Tämä tiedosto</a> on hyvä aloituskohta. Yhdistetty tietokanta Anna's Archiven torrentit selaa etsi Erilaisia pienempiä tai kertaluonteisia lähteitä. Kannustamme ihmisiä lataamaan ensin muihin varjokirjastoihin, mutta joskus ihmisillä on kokoelmia, jotka ovat liian suuria muiden läpikäytäväksi, mutta eivät tarpeeksi suuria ansaitakseen oman kategorian. Yleiskatsaus <a %(a1)s>Datasets-sivulta</a>. Alkuperä <a %(a_href)s>aaaaarg.fail</a>. Näyttää olevan melko täydellinen. Vapaaehtoiseltamme "cgiym". Peräisin <a %(a_href)s><q>ACM Digital Library 2020</q></a> -torrentista. Sisältää melko paljon päällekkäisyyksiä olemassa olevien artikkelikokoelmien kanssa, mutta hyvin vähän MD5-osumia, joten päätimme säilyttää sen kokonaan. <iRead eBooks</q> (äännetään <q>ai rit i-books</q>; airitibooks.com) -sivuston kaappaus, vapaaehtoiselta <q>j</q>. Vastaa <q>airitibooks</q> metadataa <a %(a1)s><q>Muut metadata-kaappaukset</q></a>. Kokoelmasta <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Osittain alkuperäisestä lähteestä, osittain the-eye.eu-sivustolta, osittain muista peileistä. Yksityiseltä kirjojen torrent-sivustolta, <a %(a_href)s>Bibliotik</a> (usein kutsutaan "Bib"), jonka kirjat on niputettu torrentteihin nimellä (A.torrent, B.torrent) ja jaettu the-eye.eu:n kautta. Vapaaehtoiseltamme “bpb9v”. Lisätietoja <a %(a_href)s>CADALista</a> löytyy muistiinpanoista <a %(a_duxiu)s>DuXiu-tietosivullamme</a>. Lisää vapaaehtoiseltamme “bpb9v”, pääasiassa DuXiu-tiedostoja, sekä kansiot “WenQu” ja “SuperStar_Journals” (SuperStar on DuXiun takana oleva yritys). Vapaaehtoiseltamme “cgiym”, kiinalaisia tekstejä eri lähteistä (edustettuina alihakemistoina), mukaan lukien <a %(a_href)s>China Machine Press</a> (suuri kiinalainen kustantaja). Ei-kiinalaiset kokoelmat (esitetty alihakemistoina) vapaaehtoiseltamme "cgiym". Kiinalaista arkkitehtuuria käsittelevien kirjojen kaappaus, vapaaehtoiselta <q>cm</q>: <q>Sain sen hyödyntämällä kustantamon verkkohaavoittuvuutta, mutta tuo porsaanreikä on sittemmin suljettu</q>. Vastaa <q>chinese_architecture</q> metadataa <a %(a1)s><q>Muut metadata-kaappaukset</q></a>. Kirjoja akateemiselta kustantamolta <a %(a_href)s>De Gruyter</a>, kerätty muutamasta suuresta torrentista. <a %(a_href)s>docer.pl</a>-sivuston kaapiminen, puolalainen tiedostonjakosivusto, joka keskittyy kirjoihin ja muihin kirjallisiin teoksiin. Vapaaehtoinen “p” kaapi sen loppuvuodesta 2023. Meillä ei ole hyvää metatietoa alkuperäisestä sivustosta (ei edes tiedostopäätteitä), mutta suodattimme kirjamaisia tiedostoja ja pystyimme usein poimimaan metatietoja tiedostoista itsestään. DuXiu-epubit, suoraan DuXiusta, kerätty vapaaehtoiselta "w". Vain uusimmat DuXiu-kirjat ovat saatavilla suoraan e-kirjoina, joten suurin osa näistä on oltava uusia. Jäljellä olevat DuXiu-tiedostot vapaaehtoiselta "m", jotka eivät olleet DuXiun omaa PDG-muotoa (pää <a %(a_href)s>DuXiu-tietokanta</a>). Kerätty monista alkuperäisistä lähteistä, valitettavasti ilman näiden lähteiden säilyttämistä tiedostopolussa. <span></span> <span></span> <span></span> Eroottisten kirjojen kaappaus, vapaaehtoiselta <q>do no harm</q>. Vastaa <q>hentai</q> metadataa <a %(a1)s><q>Muut metadata-kaappaukset</q></a>. <span></span> <span></span> Kokoelma, joka on kaapattu japanilaiselta mangakustantajalta vapaaehtoisen “t” toimesta. <a %(a_href)s>Valitut Longquanin oikeusarkistot</a>, tarjottu vapaaehtoiselta "c". Kaavinta <a %(a_href)s>magzdb.org</a>:sta, joka on Library Genesisin liittolainen (se on linkitetty libgen.rs:n etusivulle), mutta joka ei halunnut tarjota tiedostojaan suoraan. Vapaaehtoinen ”p” hankki sen loppuvuodesta 2023. <span></span> Erilaisia pieniä latauksia, jotka ovat liian pieniä omaksi alikokoelmakseen, mutta esitetään hakemistoina. E-kirjoja AvaxHome-sivustolta, venäläiseltä tiedostonjakosivustolta. Sanomalehtien ja aikakauslehtien arkisto. Vastaa <q>newsarch_magz</q> metadataa <a %(a1)s><q>Muut metadata-kaappaukset</q></a>. <a %(a1)s>Philosophy Documentation Center</a> -sivuston kaappaus. Vapaaehtoisen "o" kokoelma, joka keräsi puolalaisia kirjoja suoraan alkuperäisiltä julkaisusivustoilta ("scene"). Yhdistetyt kokoelmat <a %(a_href)s>shuge.org</a>:sta vapaaehtoisten ”cgiym” ja ”woz9ts” toimesta. <span></span> <a %(a_href)s>"Trantorin keisarillinen kirjasto"</a> (nimetty kuvitteellisen kirjaston mukaan), kaapattu vuonna 2022 vapaaehtoiselta "t". <span></span> <span></span> <span></span> Alakokoelmat (edustettuina hakemistoina) vapaaehtoiselta “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (kirjoittanut <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taiwanissa), mebook (mebook.cc, 我的小书屋, minun pieni kirjahyllyni — woz9ts: “Tämä sivusto keskittyy pääasiassa korkealaatuisten e-kirjatiedostojen jakamiseen, joista osa on omistajan itse taittamia. Omistaja <a %(a_arrested)s>pidätettiin</a> vuonna 2019 ja joku teki kokoelman tiedostoista, joita hän jakoi.”). Jäljellä olevat DuXiu-tiedostot vapaaehtoiselta “woz9ts”, jotka eivät olleet DuXiun omassa PDG-muodossa (muutettavissa edelleen PDF:ksi). "Lataus" -kokoelma on jaettu pienempiin alikokoelmiin, jotka on merkitty AACID-tunnuksilla ja torrenttien nimillä. Kaikki alikokoelmat on ensin deduplikoitu pääkokoelmaa vastaan, vaikka metatietojen "upload_records" JSON-tiedostot sisältävät edelleen paljon viittauksia alkuperäisiin tiedostoihin. Ei-kirjatiedostot on myös poistettu useimmista alikokoelmista, eikä niitä yleensä <em>ei</em> mainita "upload_records" JSON:ssa. Alakokoelmat ovat: Muistiinpanot Alakokoelma Monet alakokoelmat koostuvat itsessään ala-alakokoelmista (esim. eri alkuperäisistä lähteistä), jotka esitetään hakemistoina "tiedostopolku"-kentissä. Lataukset Annan arkistoon Blogikirjoituksemme tästä datasta <a %(a_worldcat)s>WorldCat</a> on voittoa tavoittelemattoman <a %(a_oclc)s>OCLC</a>:n omistama tietokanta, joka kokoaa metatietoja kirjastoista ympäri maailmaa. Se on todennäköisesti maailman suurin kirjastometatietokokoelma. Lokakuussa 2023 <a %(a_scrape)s>julkaisimme</a> kattavan kaappauksen OCLC (WorldCat) -tietokannasta, <a %(a_aac)s>Annan Arkiston konttiformaatissa</a>. Lokakuu 2023, ensimmäinen julkaisu: OCLC (WorldCat) Torrentit Annan Arkistossa Esimerkkitietue Anna's Archivessa (alkuperäinen kokoelma) Esimerkkitietue Anna's Archivessa (”zlib3” kokoelma) Anna's Archiven torrentit (metadata + sisältö) Blogikirjoitus Julkaisusta 1 Blogikirjoitus Julkaisusta 2 Vuoden 2022 lopulla Z-Libraryn väitetyt perustajat pidätettiin, ja Yhdysvaltain viranomaiset takavarikoivat verkkotunnukset. Sen jälkeen verkkosivusto on hitaasti palannut verkkoon. Ei ole tiedossa, kuka sitä tällä hetkellä ylläpitää. Päivitys helmikuulta 2023. Z-Librarylla on juurensa <a %(a_href)s>Library Genesis</a> -yhteisössä, ja se alun perin käynnistettiin heidän datallaan. Sen jälkeen se on ammattimaistunut huomattavasti ja sillä on paljon modernimpi käyttöliittymä. He pystyvät siksi saamaan paljon enemmän lahjoituksia, sekä rahallisesti verkkosivustonsa parantamiseksi että uusien kirjojen lahjoituksina. He ovat keränneet suuren kokoelman Library Genesiksen lisäksi. Kokoelma koostuu kolmesta osasta. Alkuperäiset kuvaussivut kahdelle ensimmäiselle osalle on säilytetty alla. Tarvitset kaikki kolme osaa saadaksesi kaikki tiedot (paitsi vanhentuneet torrentit, jotka on yliviivattu torrenttisivulla). %(title)s: ensimmäinen julkaisumme. Tämä oli aivan ensimmäinen julkaisu siitä, mitä silloin kutsuttiin "Piraattikirjaston peiliksi" ("pilimi"). %(title)s: toinen julkaisu, tällä kertaa kaikki tiedostot pakattuina .tar-tiedostoihin. %(title)s: inkrementaaliset uudet julkaisut, käyttäen <a %(a_href)s>Anna's Archive Containers (AAC) -formaattia</a>, nyt julkaistu yhteistyössä Z-Libraryn tiimin kanssa. Alkuperäinen peili saatiin vaivalloisesti vuosien 2021 ja 2022 aikana. Tällä hetkellä se on hieman vanhentunut: se heijastaa kokoelman tilaa kesäkuussa 2021. Päivitämme tämän tulevaisuudessa. Tällä hetkellä keskitymme saamaan tämän ensimmäisen julkaisun valmiiksi. Koska Library Genesis on jo säilytetty julkisilla torrenteilla ja sisältyy Z-Libraryyn, teimme perusduplikaatin poiston Library Genesistä kesäkuussa 2022. Käytimme tähän MD5-tiivisteitä. Kirjastossa on todennäköisesti paljon enemmän päällekkäistä sisältöä, kuten useita tiedostomuotoja samalle kirjalle. Tämä on vaikea havaita tarkasti, joten emme tee sitä. Duplikaatin poiston jälkeen meillä on jäljellä yli 2 miljoonaa tiedostoa, yhteensä hieman alle 7TB. Kokoelma koostuu kahdesta osasta: MySQL “.sql.gz” -dumppi metatiedoista ja 72 torrenttitiedostoa, joiden koko on noin 50-100GB kukin. Metatiedot sisältävät Z-Library-verkkosivuston ilmoittamat tiedot (otsikko, tekijä, kuvaus, tiedostotyyppi) sekä todellisen tiedostokoon ja md5summin, jotka havaitsimme, koska joskus nämä eivät täsmää. Näyttää siltä, että Z-Libraryllä itsellään on virheellisiä metatietoja tietyille tiedostoalueille. Saatamme myös olla ladanneet virheellisiä tiedostoja joissakin yksittäisissä tapauksissa, jotka yritämme havaita ja korjata tulevaisuudessa. Suuret torrent-tiedostot sisältävät varsinaiset kirjatiedot, ja tiedostonimenä on Z-Library ID. Tiedostopäätteet voidaan rekonstruoida metadatan avulla. Kokoelma on sekoitus tietokirjallisuutta ja kaunokirjallisuutta (ei eroteltu kuten Library Genesisissä). Laatu vaihtelee myös suuresti. Tämä ensimmäinen julkaisu on nyt täysin saatavilla. Huomaa, että torrent-tiedostot ovat saatavilla vain Tor-peilimme kautta. Julkaisu 1 (%(date)s) Tämä on yksi ylimääräinen torrenttitiedosto. Se ei sisällä mitään uutta tietoa, mutta siinä on joitakin tietoja, joiden laskeminen voi kestää jonkin aikaa. Se tekee siitä kätevän, koska tämän torrentin lataaminen on usein nopeampaa kuin sen laskeminen alusta alkaen. Erityisesti se sisältää SQLite-indeksit tar-tiedostoille, käytettäväksi <a %(a_href)s>ratarmount</a>-ohjelman kanssa. Julkaisu 2 lisäosa (%(date)s) Olemme saaneet kaikki kirjat, jotka lisättiin Z-Libraryyn viimeisen peilimme ja elokuun 2022 välillä. Olemme myös palanneet ja kaapineet joitakin kirjoja, jotka jäivät ensimmäisellä kerralla väliin. Kaiken kaikkiaan tämä uusi kokoelma on noin 24TB. Tämä kokoelma on jälleen deduplikoitu Library Genesiksen kanssa, koska kyseiselle kokoelmalle on jo saatavilla torrentteja. Tiedot on järjestetty samalla tavalla kuin ensimmäisessä julkaisussa. On olemassa MySQL “.sql.gz” -dumppi metatiedoista, joka sisältää myös kaikki ensimmäisen julkaisun metatiedot, korvaten sen. Lisäsimme myös joitakin uusia sarakkeita: Mainitsimme tämän viime kerralla, mutta selvennykseksi: "tiedostonimi" ja "md5" ovat tiedoston todellisia ominaisuuksia, kun taas "tiedostonimi_ilmoitettu" ja "md5_ilmoitettu" ovat mitä kaappasimme Z-Librarystä. Joskus nämä kaksi eivät täsmää keskenään, joten sisällytimme molemmat. Tätä julkaisua varten muutimme lajittelua "utf8mb4_unicode_ci":ksi, jonka pitäisi olla yhteensopiva vanhempien MySQL-versioiden kanssa. Tietotiedostot ovat samanlaisia kuin viime kerralla, vaikka ne ovat paljon suurempia. Emme yksinkertaisesti viitsineet luoda valtavaa määrää pienempiä torrenttitiedostoja. "pilimi-zlib2-0-14679999-extra.torrent" sisältää kaikki tiedostot, jotka jäivät väliin viime julkaisussa, kun taas muut torrentit ovat kaikki uusia ID-alueita.  <strong>Päivitys %(date)s:</strong> Teimme suurimman osan torrenteistamme liian suuriksi, mikä aiheutti torrenttiohjelmien vaikeuksia. Olemme poistaneet ne ja julkaisseet uudet torrentit. <strong>Päivitys %(date)s:</strong> Tiedostoja oli edelleen liikaa, joten pakkasimme ne tar-tiedostoihin ja julkaisimme uudet torrentit uudelleen. %(key)s: onko tämä tiedosto jo Library Genesisissä, joko tietokirjallisuus- tai kaunokirjallisuuskokoelmassa (yhdistetty md5:n perusteella). %(key)s: missä torrentissa tämä tiedosto on. %(key)s: asetettu, kun emme pystyneet lataamaan kirjaa. Julkaisu 2 (%(date)s) Zlib-julkaisut (alkuperäiset kuvaussivut) Tor-verkkotunnus Pääsivusto Z-Libraryn kaapiminen Z-Libraryn “kiinalainen” kokoelma näyttää olevan sama kuin DuXiu-kokoelmamme, mutta eri MD5-tunnisteilla. Suljemme nämä tiedostot pois torrenteista välttääksemme päällekkäisyyksiä, mutta näytämme ne silti hakemistossamme. Metatiedot Saat %(percentage)s%% bonuksen nopeista latauksista, koska käyttäjä %(profile_link)s suositteli sinua. Tämä koskee koko jäsenyysjaksoa. Lahjoita Liity Valittu jopa %(percentage)s%% alennuksia Alipay tukee kansainvälisiä luotto-/debit-kortteja. Katso <a %(a_alipay)s>tämä opas</a> saadaksesi lisätietoja. Lähetä meille Amazon.com-lahjakortteja käyttämällä luotto-/pankkikorttiasi. Voit ostaa kryptovaluuttaa luotto-/pankkikorteilla. WeChat (Weixin Pay) tukee kansainvälisiä luotto-/pankkikortteja. WeChat-sovelluksessa mene kohtaan ”Minä => Palvelut => Lompakko => Lisää kortti”. Jos et näe tätä, ota se käyttöön valitsemalla ”Minä => Asetukset => Yleiset => Työkalut => Weixin Pay => Ota käyttöön”. (käytetään, kun lähetetään Ethereumia Coinbasesta) kopioitu! kopioi (alin vähimmäismäärä) (varoitus: korkea vähimmäismäärä) -%(percentage)s%% 12 kuukautta 1 kuukausi 24 kuukautta 3 kuukautta 48 kuukautta 6 kuukautta 96 kuukautta Valitse, kuinka kauan haluat tilata. <div %(div_monthly_cost)s></div><div %(div_after)s>alennusten <span %(span_discount)s></span> jälkeen</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 kuukaudeksi 1 kuukaudeksi 24 kuukaudeksi 3 kuukaudeksi 48 kuukaudeksi 6 kuukaudeksi 96 kuukaudeksi %(monthly_cost)s / kuukausi ota yhteyttä Suorat <strong>SFTP</strong>-palvelimet Yritystason lahjoitus tai vaihto uusista kokoelmista (esim. uudet skannaukset, OCR-tiedot). Asiantuntijapääsy <strong>Rajoittamaton</strong> nopea pääsy <div %(div_question)s>Voinko päivittää jäsenyyteni tai hankkia useita jäsenyyksiä?</div> <div %(div_question)s>Voinko tehdä lahjoituksen liittymättä jäseneksi?</div> Tottakai. Hyväksymme lahjoituksia minkä tahansa summan tällä Monero (XMR) -osoitteella: %(address)s. <div %(div_question)s>Mitä kuukausittaiset vaihteluvälit tarkoittavat?</div> Voit päästä vaihteluvälin alarajalle soveltamalla kaikki alennukset, kuten valitsemalla yli kuukauden pituisen jakson. <div %(div_question)s>Uusitaanko jäsenyydet automaattisesti?</div> Jäsenyydet <strong>eivät</strong> uusiudu automaattisesti. Voit liittyä niin pitkäksi tai lyhyeksi ajaksi kuin haluat. <div %(div_question)s>Mihin lahjoitukset käytetään?</div> 100%% menee maailman tiedon ja kulttuurin säilyttämiseen ja saataville saattamiseen. Tällä hetkellä käytämme sen pääasiassa palvelimiin, tallennustilaan ja kaistanleveyteen. Rahaa ei mene henkilökohtaisesti kenellekään tiimin jäsenelle. <div %(div_question)s>Voinko tehdä suuren lahjoituksen?</div> Se olisi mahtavaa! Jos lahjoituksesi on muutaman tuhannen dollarin suuruinen, ota meihin suoraan yhteyttä osoitteessa %(email)s. <div %(div_question)s>Onko teillä muita maksutapoja?</div> Tällä hetkellä ei. Monet ihmiset eivät halua tällaisten arkistojen olevan olemassa, joten meidän on oltava varovaisia. Jos voit auttaa meitä perustamaan muita (kätevämpiä) maksutapoja turvallisesti, ota yhteyttä osoitteessa %(email)s. Lahjoitus-FAQ Sinulla on <a %(a_donation)s>olemassa oleva lahjoitus</a> kesken. Viimeistele tai peruuta se ennen uuden lahjoituksen tekemistä. <a %(a_all_donations)s>Näytä kaikki lahjoitukseni</a> Yli 5000 dollarin lahjoituksissa ota meihin suoraan yhteyttä osoitteessa %(email)s. Otamme mielellämme vastaan suuria lahjoituksia varakkailta yksityishenkilöiltä tai instituutioilta.  Huomaa, että vaikka tämän sivun jäsenyydet ovat "kuukausittaisia", ne ovat kertaluonteisia lahjoituksia (eivät toistuvia). Katso <a %(faq)s>Lahjoitus-FAQ</a>. Anna’s Archive on voittoa tavoittelematon, avoimen lähdekoodin ja avoimen datan projekti. Lahjoittamalla ja liittymällä jäseneksi tuet toimintaamme ja kehitystämme. Kiitos kaikille jäsenillemme, että pidätte meidät käynnissä! ❤️ Lisätietoja saat tutustumalla <a %(a_donate)s>Lahjoitus-FAQ</a>. Liity jäseneksi, ole hyvä ja <a %(a_login)s>Kirjaudu sisään tai Rekisteröidy</a>. Kiitos tuestasi! $%(cost)s / kuukausi Jos teit virheen maksun aikana, emme voi palauttaa rahoja, mutta yritämme korjata asian. Löydä “Crypto”-sivu PayPal-sovelluksestasi tai -verkkosivustoltasi. Tämä löytyy yleensä “Talous”-kohdasta. Siirry PayPal-sovelluksesi tai -verkkosivustosi "Bitcoin"-sivulle. Paina "Siirrä"-painiketta %(transfer_icon)s ja sitten "Lähetä". Alipay Alipay 支付宝 / WeChat 微信 Amazon-lahjakortti %(amazon)s lahjakortti Pankkikortti Pankkikortti (sovelluksen kautta) Binance Luotto/debit/Apple/Google (BMC) Cash App Luotto-/pankkikortti Luotto-/pankkikortti 2 Luotto-/pankkikortti (varajärjestelmä) Krypto %(bitcoin_icon)s Kortti / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (säännöllinen) Pix (Brasilia) Revolut (väliaikaisesti ei saatavilla) WeChat Valitse haluamasi kryptovaluutta: Lahjoita käyttämällä Amazon-lahjakorttia. <strong>TÄRKEÄÄ:</strong> Tämä vaihtoehto on tarkoitettu %(amazon)s. Jos haluat käyttää toista Amazon-verkkosivustoa, valitse se ylhäältä. <strong>TÄRKEÄÄ:</strong> Tuemme vain Amazon.comia, emme muita Amazon-sivustoja. Esimerkiksi .de, .co.uk, .ca, EIVÄT ole tuettuja. ÄLÄ kirjoita omaa viestiäsi. Syötä tarkka summa: %(amount)s Huomaa, että meidän on pyöristettävä jälleenmyyjiemme hyväksymiin summiin (vähintään %(minimum)s). Lahjoita luotto-/pankkikortilla Alipay-sovelluksen kautta (erittäin helppo asentaa). Asenna Alipay-sovellus <a %(a_app_store)s>Apple App Storesta</a> tai <a %(a_play_store)s>Google Play Storesta</a>. Rekisteröidy puhelinnumerollasi. Muita henkilötietoja ei tarvita. <span %(style)s>1</span>Asenna Alipay-sovellus Tuetut: Visa, MasterCard, JCB, Diners Club ja Discover. Katso <a %(a_alipay)s>tämä opas</a> saadaksesi lisätietoja. <span %(style)s>2</span>Lisää pankkikortti Binancen avulla voit ostaa Bitcoinia luotto-/pankkikortilla tai pankkitilillä ja lahjoittaa sitten Bitcoinin meille. Näin voimme pysyä turvallisina ja anonyymeinä vastaanottaessamme lahjoituksesi. Binance on saatavilla lähes kaikissa maissa ja tukee useimpia pankkeja sekä luotto-/pankkikortteja. Tämä on tällä hetkellä pääsuosituksemme. Arvostamme, että käytät aikaa oppiaksesi lahjoittamaan tällä menetelmällä, sillä se auttaa meitä paljon. Luotto- ja pankkikorttien, Apple Payn ja Google Payn osalta käytämme "Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Heidän järjestelmässään yksi "kahvi" vastaa 5 dollaria, joten lahjoituksesi pyöristetään lähimpään viiden dollarin kerrannaiseen. Lahjoita käyttämällä Cash Appia. Jos sinulla on Cash App, tämä on helpoin tapa lahjoittaa! Huomaa, että alle %(amount)s tapahtumista Cash App saattaa veloittaa %(fee)s maksun. %(amount)s tai enemmän on maksutonta! Lahjoita luotto- tai pankkikortilla. Tämä menetelmä käyttää kryptovaluuttapalveluntarjoajaa välivaiheen muuntamiseen. Tämä voi olla hieman hämmentävää, joten käytä tätä menetelmää vain, jos muut maksutavat eivät toimi. Se ei myöskään toimi kaikissa maissa. Emme voi tukea luotto-/debit-kortteja suoraan, koska pankit eivät halua tehdä yhteistyötä kanssamme. ☹ Kuitenkin, on useita tapoja käyttää luotto-/debit-kortteja muiden maksutapojen kautta: Kryptovaluutalla voit lahjoittaa käyttämällä BTC, ETH, XMR ja SOL. Käytä tätä vaihtoehtoa, jos olet jo perehtynyt kryptovaluuttaan. Kryptovaluutalla voit lahjoittaa käyttämällä BTC, ETH, XMR ja muita. Krypto express -palvelut Jos käytät kryptovaluuttaa ensimmäistä kertaa, suosittelemme käyttämään %(options)s Bitcoinin (alkuperäinen ja eniten käytetty kryptovaluutta) ostamiseen ja lahjoittamiseen. Huomaa, että pienissä lahjoituksissa luottokorttimaksut saattavat poistaa %(discount)s%% alennuksemme, joten suosittelemme pidempiä tilauksia. Lahjoita käyttämällä luotto-/pankkikorttia, PayPalia tai Venmoa. Voit valita näistä seuraavalla sivulla. Google Pay ja Apple Pay saattavat myös toimia. Huomaa, että pienissä lahjoituksissa maksut ovat korkeat, joten suosittelemme pidempiä tilauksia. Lahjoittaaksesi PayPal US:n kautta, käytämme PayPal Cryptoa, joka mahdollistaa anonymiteetin säilyttämisen. Arvostamme, että käytät aikaa oppiaksesi lahjoittamaan tällä menetelmällä, sillä se auttaa meitä paljon. Lahjoita käyttämällä PayPalia. Lahjoita käyttäen tavallista PayPal-tiliäsi. Lahjoita käyttäen Revolutia. Jos sinulla on Revolut, tämä on helpoin tapa lahjoittaa! Tämä maksutapa sallii enintään %(amount)s. Valitse toinen kesto tai maksutapa. Tämä maksutapa vaatii vähintään %(amount)s. Valitse toinen kesto tai maksutapa. Binance Coinbase Kraken Valitse maksutapa. ”Adoptoi torrentti”: käyttäjänimesi tai viestisi torrenttitiedoston nimessä <div %(div_months)s>joka 12 kuukauden jäsenyyden jälkeen</div> Käyttäjänimesi tai anonyymi maininta krediiteissä Varhainen pääsy uusiin ominaisuuksiin Eksklusiivinen Telegram kulissien takaisilla päivityksillä %(number)s nopeaa latausta päivässä jos lahjoitat tässä kuussa! <a %(a_api)s>JSON API</a> -pääsy Legendaarinen asema ihmiskunnan tiedon ja kulttuurin säilyttämisessä Edelliset edut, plus: Ansaitse <strong>%(percentage)s%% bonusta latauksista</strong> <a %(a_refer)s>suosittelemalla ystäviä</a>. SciDB-tutkimukset <strong>rajoittamattomasti</strong> ilman vahvistusta Kun kysyt tilin tai lahjoituksen asioista, lisää tilitunnuksesi, kuvakaappauksia, kuitteja ja mahdollisimman paljon tietoa. Tarkistamme sähköpostimme vain 1-2 viikon välein, joten näiden tietojen puuttuminen viivästyttää ratkaisua. Saadaksesi vielä enemmän latauksia, <a %(a_refer)s>kutsu ystäviäsi</a>! Olemme pieni vapaaehtoisten tiimi. Vastaamisessa voi kestää 1-2 viikkoa. Huomaa, että tilin nimi tai kuva saattaa näyttää oudolta. Ei syytä huoleen! Näitä tilejä hallinnoivat lahjoituskumppanimme. Tilejämme ei ole hakkeroitu. Lahjoita <span %(span_cost)s></span> <span %(span_label)s></span> 12 kuukaudeksi “%(tier_name)s” 1 kuukaudeksi “%(tier_name)s” 24 kuukaudeksi “%(tier_name)s” 3 kuukaudeksi “%(tier_name)s” 48 kuukaudeksi “%(tier_name)s” 6 kuukaudeksi “%(tier_name)s” 96 kuukaudeksi “%(tier_name)s” Voit silti peruuttaa lahjoituksen kassalla. Vahvista tämä lahjoitus napsauttamalla lahjoituspainiketta. <strong>Tärkeä huomautus:</strong> Kryptovaluuttojen hinnat voivat vaihdella rajusti, joskus jopa 20%% muutamassa minuutissa. Tämä on silti vähemmän kuin monet maksupalveluntarjoajat veloittavat, jotka usein perivät 50-60%% työskennellessään "varjohyväntekeväisyyden" kanssa kuten meidän. <u>Jos lähetät meille kuitin alkuperäisestä maksamastasi hinnasta, hyvitetään tilillesi valitsemasi jäsenyys</u> (kunhan kuitti ei ole vanhempi kuin muutama tunti). Arvostamme todella, että olet valmis sietämään tällaisia asioita tukeaksesi meitä! ❤️ ❌ Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudelleen. <span %(span_circle)s>1</span>Osta Bitcoinia Paypalilla <span %(span_circle)s>2</span>Siirrä Bitcoin osoitteeseemme ✅ Uudelleenohjataan lahjoitussivulle… Lahjoita Odota vähintään <span %(span_hours)s>24 tuntia</span> (ja päivitä tämä sivu) ennen kuin otat meihin yhteyttä. Jos haluat tehdä lahjoituksen (minkä tahansa summan) ilman jäsenyyttä, voit käyttää tätä Monero (XMR) -osoitetta: %(address)s. Lähetettyäsi lahjakorttisi, automaattinen järjestelmämme vahvistaa sen muutaman minuutin kuluessa. Jos tämä ei toimi, yritä lähettää lahjakortti uudelleen (<a %(a_instr)s>ohjeet</a>). Jos tämä ei vieläkään toimi, lähetä meille sähköpostia ja Anna tarkistaa sen manuaalisesti (tämä voi kestää muutaman päivän), ja muista mainita, jos olet jo yrittänyt lähettää uudelleen. Esimerkki: Käytä <a %(a_form)s>virallista Amazon.com-lomaketta</a> lähettääksesi meille lahjakortin %(amount)s alla olevaan sähköpostiosoitteeseen. “To” vastaanottajan sähköposti muodossa: Amazon-lahjakortti Emme voi hyväksyä muita lahjakorttimenetelmiä, <strong>vain suoraan Amazon.comin virallisesta lomakkeesta lähetettyjä</strong>. Emme voi palauttaa lahjakorttiasi, jos et käytä tätä lomaketta. Käytä vain kerran. Ainutlaatuinen tilillesi, älä jaa. Odotetaan lahjakorttia… (päivitä sivu tarkistaaksesi) Avaa <a %(a_href)s>QR-koodin lahjoitussivu</a>. Skannaa QR-koodi Alipay-sovelluksella tai paina painiketta avataksesi Alipay-sovelluksen. Ole kärsivällinen; sivun lataaminen voi kestää hetken, koska se on Kiinassa. <span %(style)s>3</span>Tee lahjoitus (skannaa QR-koodi tai paina painiketta) Osta PYUSD-kolikko PayPalilla Osta Bitcoinia (BTC) Cash Appissa Osta hieman enemmän (suosittelemme %(more)s enemmän) kuin lahjoitettava summa (%(amount)s), kattaaksesi transaktiomaksut. Pidät kaiken ylimääräisen. Siirry Cash Appin “Bitcoin” (BTC) -sivulle. Siirrä Bitcoin osoitteeseemme Pienille lahjoituksille (alle $25) saatat joutua käyttämään Rush- tai Priority-palvelua. Napsauta “Lähetä bitcoin” -painiketta tehdäksesi “noston”. Vaihda dollareista BTC:hen painamalla %(icon)s -kuvaketta. Syötä alla oleva BTC-määrä ja napsauta “Lähetä”. Katso <a %(help_video)s>tästä videosta</a> jos jäät jumiin. Express-palvelut ovat käteviä, mutta veloittavat korkeampia maksuja. Voit käyttää tätä kryptopörssin sijasta, jos haluat tehdä nopeasti suuremman lahjoituksen etkä välitä 5-10 dollarin maksusta. Varmista, että lähetät täsmälleen lahjoitussivulla näkyvän kryptomäärän, ei määrää $USD:ssä. Muuten maksu vähennetään, emmekä voi käsitellä jäsenyyttäsi automaattisesti. Vahvistus voi joskus kestää jopa 24 tuntia, joten muista päivittää tämä sivu (vaikka se olisi vanhentunut). Luottokortti- / pankkikorttiohjeet Lahjoita luotto-/pankkikorttisivumme kautta Jotkut vaiheet mainitsevat kryptolompakot, mutta älä huoli, sinun ei tarvitse oppia mitään kryptosta tätä varten. %(coin_name)s ohjeet Skannaa tämä QR -koodi Crypto Wallet -sovelluksella täyttääksesi maksutiedot nopeasti Skannaa QR -koodi maksettavaksi Tuemme vain kryptovaluuttojen standardiversioita, emme eksoottisia verkkoja tai versioita. Siirron vahvistaminen voi kestää jopa tunnin, riippuen valuutasta. Lahjoita %(amount)s <a %(a_page)s>tällä sivulla</a>. Tämä lahjoitus on vanhentunut. Peruuta ja luo uusi. Jos olet jo maksanut: Kyllä, lähetin sähköpostitse kuitin Jos kryptovaluutan vaihtokurssi vaihteli tapahtuman aikana, muista liittää mukaan kuitti, joka näyttää alkuperäisen vaihtokurssin. Arvostamme todella, että näet vaivaa käyttääksesi kryptovaluuttaa, se auttaa meitä paljon! ❌ Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudestaan. <span %(span_circle)s>%(circle_number)s</span>Lähetä meille kuitti sähköpostitse Jos kohtaat ongelmia, ota yhteyttä osoitteeseen %(email)s ja liitä mukaan mahdollisimman paljon tietoa (kuten kuvakaappauksia). ✅ Kiitos lahjoituksestasi! Anna aktivoi jäsenyytesi manuaalisesti muutaman päivän kuluessa. Lähetä kuitti tai kuvakaappaus henkilökohtaiseen vahvistusosoitteeseesi: Kun olet lähettänyt kuitin sähköpostitse, napsauta tätä painiketta, jotta Anna voi tarkistaa sen manuaalisesti (tämä voi kestää muutaman päivän): Lähetä kuitti tai kuvakaappaus henkilökohtaiseen vahvistusosoitteeseesi. Älä käytä tätä sähköpostiosoitetta PayPal-lahjoituksellesi. Peruuta Kyllä, peruuta Oletko varma, että haluat peruuttaa? Älä peruuta, jos olet jo maksanut. ❌ Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudelleen. Tee uusi lahjoitus ✅ Lahjoituksesi on peruutettu. Päivämäärä: %(date)s Tunniste: %(id)s Järjestä uudelleen Tila: <span %(span_label)s>%(label)s</span> Yhteensä: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / kuukausi %(duration)s kuukauden ajan, sisältäen %(discounts)s%% alennuksen)</span> Yhteensä: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / kuukausi %(duration)s kuukauden ajan)</span> 1. Syötä sähköpostiosoitteesi. 2. Valitse maksutapa. 3. Valitse maksutapasi uudelleen. 4. Valitse ”Itseisännöity” lompakko. 5. Klikkaa “Vahvistan omistajuuden”. 6. Sinun pitäisi saada sähköpostikuitti. Lähetä se meille, ja vahvistamme lahjoituksesi mahdollisimman pian. (voit halutessasi peruuttaa ja luoda uuden lahjoituksen) Maksuohjeet ovat nyt vanhentuneet. Jos haluat tehdä toisen lahjoituksen, käytä yllä olevaa "Tilaa uudelleen" -painiketta. Olet jo maksanut. Jos haluat tarkistaa maksutiedot, klikkaa tästä: Näytä vanhat maksutiedot Jos lahjoitussivu estetään, kokeile toista internetyhteyttä (esim. VPN tai puhelimen internet). Valitettavasti Alipay-sivu on usein käytettävissä vain <strong>Manner-Kiinasta</strong>. Saatat joutua väliaikaisesti poistamaan VPN:n käytöstä tai käyttämään VPN:ää Manner-Kiinaan (tai joskus myös Hongkong toimii). <span %(span_circle)s>1</span>Lahjoita Alipaylla Lahjoita yhteensä %(total)s käyttämällä <a %(a_account)s>tätä Alipay-tiliä</a> Alipay-ohjeet <span %(span_circle)s>1</span>Siirrä yhteen kryptotileistämme Lahjoita kokonaismäärä %(total)s johonkin näistä osoitteista: Kryptovaluuttaohjeet Noudata ohjeita ostaaksesi Bitcoinia (BTC). Sinun tarvitsee ostaa vain haluamasi lahjoitussumma, %(total)s. Syötä Bitcoin (BTC) -osoitteemme vastaanottajaksi ja seuraa ohjeita lähettääksesi lahjoituksesi %(total)s: <span %(span_circle)s>1</span>Lahjoita Pixillä Lahjoita yhteensä %(total)s käyttämällä <a %(a_account)s>tätä Pix-tiliä Pix-ohjeet <span %(span_circle)s>1</span>Lahjoita WeChatissa Lahjoita yhteensä %(total)s käyttämällä <a %(a_account)s>tätä WeChat-tiliä</a> WeChat-ohjeet Käytä jotakin seuraavista “luottokortista Bitcoiniin” -pikapalveluista, jotka vievät vain muutaman minuutin: BTC / Bitcoin osoite (ulkoinen lompakko): BTC / Bitcoin määrä: Täytä seuraavat tiedot lomakkeeseen: Jos jokin näistä tiedoista on vanhentunut, lähetä meille sähköpostia ja ilmoita asiasta. Käytä tätä <span %(underline)s>tarkkaa määrää</span>. Kokonaiskustannuksesi saattavat olla korkeammat luottokorttimaksujen vuoksi. Pienissä summissa tämä saattaa valitettavasti olla enemmän kuin alennuksemme. (minimi: %(minimum)s, ei vahvistusta ensimmäiselle transaktiolle) (minimi: %(minimum)s) (minimi: %(minimum)s) (minimi: %(minimum)s, ei vahvistusta ensimmäiselle transaktiolle) (minimi: %(minimum)s) (minimi: %(minimum)s riippuen maasta, ei vahvistusta ensimmäiselle transaktiolle) Noudata ohjeita ostaaksesi PYUSD-kolikon (PayPal USD). Osta hieman enemmän (suosittelemme %(more)s enemmän) kuin lahjoitettava summa (%(amount)s), kattaaksesi maksut. Saat pitää kaiken ylimääräisen. Siirry "PYUSD" -sivulle PayPal-sovelluksessasi tai -verkkosivustollasi. Paina "Siirrä" -painiketta %(icon)s ja sitten "Lähetä". Päivitä tila Nollataksesi ajastimen, luo yksinkertaisesti uusi lahjoitus. Muista käyttää alla olevaa BTC-määrää, <em>EI</em> euroja tai dollareita, muuten emme saa oikeaa summaa emmekä voi automaattisesti vahvistaa jäsenyyttäsi. Osta Bitcoinia (BTC) Revolutissa Osta hieman enemmän (suosittelemme %(more)s enemmän) kuin lahjoitettava summa (%(amount)s), kattaaksesi transaktiomaksut. Pidät kaiken ylimääräisen. Siirry Revolutin “Crypto” -sivulle ostaaksesi Bitcoinia (BTC). Siirrä Bitcoin osoitteeseemme Pienille lahjoituksille (alle $25) saatat joutua käyttämään Rush- tai Priority-palvelua. Napsauta “Lähetä bitcoin” -painiketta tehdäksesi “noston”. Vaihda euroista BTC:hen painamalla %(icon)s -kuvaketta. Syötä alla oleva BTC-määrä ja napsauta “Lähetä”. Katso <a %(help_video)s>tästä videosta</a> jos jäät jumiin. Tila: 1 2 Vaiheittainen opas Katso alla oleva vaiheittainen opas. Muuten saatat menettää pääsyn tähän tiliin! Jos et ole vielä tehnyt niin, kirjoita salainen avain ylös kirjautumista varten: Kiitos lahjoituksestasi! Aikaa jäljellä: Lahjoitus Siirrä %(amount)s kohteeseen %(account)s Odotetaan vahvistusta (päivitä sivu tarkistaaksesi)… Odotetaan siirtoa (päivitä sivu tarkistaaksesi)… Aiemmin Nopeat lataukset viimeisen 24 tunnin aikana lasketaan päivittäiseen rajaan. Lataukset nopeilta kumppanipalvelimilta on merkitty %(icon)s. Viimeiset 18 tuntia Ei tiedostoja ladattu vielä. Ladattuja tiedostoja ei näytetä julkisesti. Kaikki ajat ovat UTC-ajassa. Ladatut tiedostot Jos latasit tiedoston sekä nopeilla että hitailla latauksilla, se näkyy kahdesti. Älä huoli liikaa, monet ihmiset lataavat sivustoilta, joihin linkitämme, ja on erittäin harvinaista joutua ongelmiin. Kuitenkin, pysyäksesi turvassa suosittelemme käyttämään VPN:ää (maksullinen) tai <a %(a_tor)s>Tor</a> (ilmainen). Latasin George Orwellin teoksen Vuonna 1984, tuleeko poliisi ovelle? Sinä olet Anna! Kuka on Anna? Meillä on yksi vakaa JSON API jäsenille, nopean lataus-URL:n saamiseksi: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentaatio JSON:ssa). Muita käyttötapauksia varten, kuten kaikkien tiedostojemme läpikäynti, mukautetun haun rakentaminen ja niin edelleen, suosittelemme <a %(a_generate)s>luomaan</a> tai <a %(a_download)s>lataamaan</a> ElasticSearch- ja MariaDB-tietokantamme. Raakadataa voi tutkia manuaalisesti <a %(a_explore)s>JSON-tiedostojen kautta</a>. Raakatorrenttilistamme voidaan myös ladata muodossa <a %(a_torrents)s>JSON</a>. Onko teillä API? Emme isännöi täällä mitään tekijänoikeudella suojattua materiaalia. Olemme hakukone, ja siten indeksoimme vain julkisesti saatavilla olevia metatietoja. Kun lataat näistä ulkoisista lähteistä, suosittelemme tarkistamaan lainmukaisuuden omassa lainkäyttöalueessasi. Emme ole vastuussa muiden isännöimästä sisällöstä. Jos sinulla on valituksia siitä, mitä näet täällä, paras vaihtoehto on ottaa yhteyttä alkuperäiseen verkkosivustoon. Päivitämme säännöllisesti heidän muutoksensa tietokantaamme. Jos todella uskot, että sinulla on pätevä DMCA-valitus, johon meidän pitäisi vastata, täytä <a %(a_copyright)s>DMCA / Tekijänoikeusvaatimuslomake</a>. Otamme valituksesi vakavasti ja palaamme asiaan mahdollisimman pian. Miten ilmoitan tekijänoikeusrikkomuksesta? Tässä on joitakin kirjoja, joilla on erityinen merkitys varjokirjastoille ja digitaalisen säilyttämisen maailmalle: Mitkä ovat lempikirjasi? Haluamme myös muistuttaa kaikkia, että kaikki koodimme ja datamme ovat täysin avoimen lähdekoodin. Tämä on ainutlaatuista kaltaisillemme projekteille — emme tiedä yhtään muuta projektia, jolla olisi yhtä massiivinen katalogi ja joka olisi myös täysin avoimen lähdekoodin. Toivotamme erittäin tervetulleeksi kaikki, jotka ajattelevat, että hoidamme projektimme huonosti, ottamaan koodimme ja datamme ja perustamaan oman varjokirjastonsa! Emme sano tätä ilkeydestä tai muusta — uskomme aidosti, että tämä olisi mahtavaa, koska se nostaisi rimaa kaikille ja säilyttäisi paremmin ihmiskunnan perinnön. Vihaan tapaa, jolla johdatte tätä projektia! Haluaisimme, että ihmiset perustaisivat <a %(a_mirrors)s>peilejä</a>, ja tuemme tätä taloudellisesti. Kuinka voin auttaa? Kyllä, meillä on. Inspiraatiomme metatietojen keräämiseen on Aaron Swartzin tavoite "yksi verkkosivu jokaiselle koskaan julkaistulle kirjalle", jota varten hän loi <a %(a_openlib)s>Open Libraryn</a>. Tämä projekti on menestynyt hyvin, mutta ainutlaatuinen asemamme mahdollistaa metatietojen saamisen, joita he eivät voi. Toinen inspiraatio oli halumme tietää <a %(a_blog)s>kuinka monta kirjaa maailmassa on</a>, jotta voimme laskea, kuinka monta kirjaa meillä on vielä pelastettavana. Keräättekö metatietoja? Huomaa, että mhut.org estää tietyt IP-alueet, joten VPN saattaa olla tarpeen. <strong>Android:</strong> Napsauta oikeassa yläkulmassa olevaa kolmen pisteen valikkoa ja valitse ”Lisää aloitusnäyttöön”. <strong>iOS:</strong> Napsauta alareunassa olevaa ”Jaa”-painiketta ja valitse ”Lisää Koti-valikkoon”. Meillä ei ole virallista mobiilisovellusta, mutta voit asentaa tämän verkkosivuston sovelluksena. Onko teillä mobiilisovellusta? Lähetä ne <a %(a_archive)s>Internet Archiveen</a>. He säilyttävät ne asianmukaisesti. Kuinka voin lahjoittaa kirjoja tai muita fyysisiä materiaaleja? Miten pyydän kirjoja? <a %(a_blog)s>Annan blogi</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — säännölliset päivitykset <a %(a_software)s>Annan Ohjelmisto</a> — avoimen lähdekoodin koodimme <a %(a_datasets)s>Datasets</a> — tietoa datasta <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — vaihtoehtoiset verkkotunnukset Onko Anna’s Archivesta saatavilla lisää resursseja? <a %(a_translate)s>Käännä Anna’s Softwarella</a> — käännösjärjestelmämme <a %(a_wikipedia)s>Wikipedia</a> — lisää meistä (auta pitämään tämä sivu ajan tasalla tai luo oma sivu omalle kielellesi!) Valitse haluamasi asetukset, jätä hakukenttä tyhjäksi, klikkaa “Haku” ja tallenna sivu selaimesi kirjanmerkkitoiminnolla. Miten tallennan hakuasetukseni? Toivotamme tietoturvatutkijat tervetulleiksi etsimään haavoittuvuuksia järjestelmistämme. Kannatamme vahvasti vastuullista ilmoittamista. Ota yhteyttä <a %(a_contact)s>tästä</a>. Emme tällä hetkellä pysty myöntämään bugipalkkioita, paitsi haavoittuvuuksista, joilla on <a %(a_link)s>potentiaali vaarantaa anonymiteettimme</a>, joista tarjoamme palkkioita 10 000–50 000 dollarin välillä. Haluaisimme tarjota laajemman bugipalkkioiden kattavuuden tulevaisuudessa! Huomaa, että sosiaalisen manipuloinnin hyökkäykset eivät kuulu kattavuuteen. Jos olet kiinnostunut hyökkäysturvallisuudesta ja haluat auttaa arkistoimaan maailman tietoa ja kulttuuria, ota meihin yhteyttä. On monia tapoja, joilla voit auttaa. Onko teillä vastuullisen ilmoittamisen ohjelmaa? Meillä ei kirjaimellisesti ole tarpeeksi resursseja tarjota kaikille maailman ihmisille nopeita latauksia, vaikka kuinka haluaisimme. Jos rikas hyväntekijä haluaisi astua esiin ja tarjota tämän meille, se olisi uskomatonta, mutta siihen asti yritämme parhaamme. Olemme voittoa tavoittelematon projekti, joka tuskin pystyy ylläpitämään itseään lahjoitusten avulla. Siksi olemme ottaneet käyttöön kaksi järjestelmää ilmaisille latauksille yhteistyökumppaneidemme kanssa: jaetut palvelimet hitailla latauksilla ja hieman nopeammat palvelimet jonotuslistalla (vähentääksemme samanaikaisesti lataavien ihmisten määrää). Meillä on myös <a %(a_verification)s>selaimen vahvistus</a> hitaille latauksille, koska muuten botit ja kaapimet väärinkäyttäisivät niitä, mikä hidastaisi asioita entisestään oikeille käyttäjille. Huomaa, että käyttäessäsi Tor-selainta saatat joutua säätämään suojausasetuksiasi. Alimmalla vaihtoehdolla, nimeltään “Standard”, Cloudflare-turnstile-haaste onnistuu. Korkeammilla vaihtoehdoilla, nimeltään “Safer” ja “Safest”, haaste epäonnistuu. Suuret tiedostot saattavat joskus keskeytyä hitaiden latausten aikana. Suosittelemme käyttämään lataushallintaa (kuten JDownloader) suurten latausten automaattiseen jatkamiseen. Miksi hitaat lataukset ovat niin hitaita? Usein kysytyt kysymykset (UKK) Käytä <a %(a_list)s>torrenttilistageneraattoria</a> luodaksesi listan torrenteista, jotka eniten tarvitsevat torrentointia, tallennustilarajojesi puitteissa. Kyllä, katso <a %(a_llm)s>LLM data</a> -sivu. Useimmat torrentit sisältävät tiedostot suoraan, mikä tarkoittaa, että voit ohjeistaa torrent-asiakkaita lataamaan vain tarvittavat tiedostot. Määrittääksesi, mitkä tiedostot ladata, voit <a %(a_generate)s>luoda</a> metatietomme tai <a %(a_download)s>ladata</a> ElasticSearch- ja MariaDB-tietokantamme. Valitettavasti useat torrent-kokoelmat sisältävät .zip- tai .tar-tiedostoja juurihakemistossa, jolloin sinun on ladattava koko torrent ennen kuin voit valita yksittäisiä tiedostoja. Helppokäyttöisiä työkaluja torrenttien suodattamiseen ei ole vielä saatavilla, mutta otamme mielellämme vastaan panoksia. (Meillä on kuitenkin <a %(a_ideas)s>joitakin ideoita</a> jälkimmäiseen tapaukseen.) Pitkä vastaus: Lyhyt vastaus: ei helposti. Yritämme pitää tämän listan torrenttien päällekkäisyyden tai päällekkäisyyden minimissä, mutta tämä ei aina ole mahdollista ja riippuu suuresti lähdekirjastojen käytännöistä. Kirjastojen, jotka julkaisevat omia torrentejaan, osalta se ei ole meidän käsissämme. Anna’s Archiven julkaisemien torrenttien osalta deduplikoimme vain MD5-hashin perusteella, mikä tarkoittaa, että eri versiot samasta kirjasta eivät tule deduplikoiduiksi. Kyllä. Nämä ovat itse asiassa PDF- ja EPUB-tiedostoja, niissä ei vain ole tiedostopäätettä monissa torrenteissamme. On kaksi paikkaa, joista löydät torrent-tiedostojen metatiedot, mukaan lukien tiedostotyypit/päätteet: 1. Jokaisella kokoelmalla tai julkaisulla on omat metatietonsa. Esimerkiksi <a %(a_libgen_nonfic)s>Libgen.rs-torrentit</a> sisältävät vastaavan metatietokannan, joka on isännöity Libgen.rs-sivustolla. Linkitämme yleensä asiaankuuluviin metatietoresursseihin jokaisen kokoelman <a %(a_datasets)s>dataset-sivulta</a>. 2. Suosittelemme <a %(a_generate)s>luomaan</a> tai <a %(a_download)s>lataamaan</a> ElasticSearch- ja MariaDB-tietokantamme. Nämä sisältävät kartoituksen jokaiselle Anna’s Archiven tietueelle vastaaviin torrent-tiedostoihin (jos saatavilla), ElasticSearch JSON -tiedoston "torrent_paths"-kohdassa. Jotkut torrent-asiakasohjelmat eivät tue suuria palakokoja, joita monissa torrenteissamme on (uusimmissa emme enää tee näin — vaikka se onkin spesifikaatioiden mukaista!). Kokeile siis toista asiakasohjelmaa, jos kohtaat tämän ongelman, tai valita torrent-asiakasohjelmasi tekijöille. Haluaisin auttaa jakamisessa, mutta minulla ei ole paljon levytilaa. Torrentit ovat liian hitaita; voinko ladata tiedot suoraan teiltä? Voinko ladata vain osan tiedostoista, kuten vain tietyn kielen tai aiheen? Miten käsittelette kaksoiskappaleita torrenteissa? Voinko saada torrent-listan JSON-muodossa? En näe PDF- tai EPUB-tiedostoja torrenteissa, vain binääritiedostoja? Mitä teen? Miksi torrent-asiakasohjelmani ei voi avata joitakin torrent-tiedostojanne / magneettilinkkejä? Torrents UKK Kuinka lataan uusia kirjoja? Katso <a %(a_href)s>tämä erinomainen projekti</a>. Onko sinulla käytettävyysseurantaa? Mikä on Annan Arkisto? Liity jäseneksi käyttääksesi nopeita latauksia. Tuemme nyt Amazon-lahjakortteja, luotto- ja pankkikortteja, kryptovaluuttoja, Alipayta ja WeChatia. Nopeat latauksesi ovat loppuneet tältä päivältä. Pääsy Tuntikohtaiset lataukset viimeisen 30 päivän aikana. Tuntikohtainen keskiarvo: %(hourly)s. Päivittäinen keskiarvo: %(daily)s. Teemme yhteistyötä kumppaneiden kanssa, jotta kokoelmamme olisivat helposti ja vapaasti kaikkien saatavilla. Uskomme, että kaikilla on oikeus ihmiskunnan yhteiseen viisauteen. Ja <a %(a_search)s>ei kirjailijoiden kustannuksella</a>. Anna’s Archiven käyttämät datasetsit ovat täysin avoimia, ja ne voidaan peilata massoittain torrenttien avulla. <a %(a_datasets)s>Lue lisää…</a> Pitkäaikaisarkisto Koko tietokanta Etsi Kirjat, artikkelit, lehdet, sarjakuvat, kirjastotiedot, metatiedot, … Kaikki <a %(a_code)s>koodimme</a> ja <a %(a_datasets)s>datan</a> ovat täysin avoimen lähdekoodin. <span %(span_anna)s>Annin Arkisto</span> on voittoa tavoittelematon projekti, jolla on kaksi tavoitetta: <li><strong>Säilyttäminen:</strong> Kaiken ihmiskunnan tiedon ja kulttuurin varmuuskopiointi.</li><li><strong>Pääsy:</strong> Tämän tiedon ja kulttuurin saattaminen kaikkien saataville ympäri maailmaa.</li> Meillä on maailman suurin kokoelma korkealaatuisia tekstidatoja. <a %(a_llm)s>Lue lisää…</a> LLM-koulutusdata 🪩 Peilit: vapaaehtoisia tarvitaan Jos ylläpidät korkean riskin anonyymiä maksupalvelua, ota meihin yhteyttä. Etsimme myös ihmisiä, jotka haluavat sijoittaa tyylikkäitä pieniä mainoksia. Kaikki tuotot menevät säilyttämispyrkimyksiimme. Säilytys Arvioimme, että olemme säilyttäneet noin <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% maailman kirjoista</a>. Säilytämme kirjoja, artikkeleita, sarjakuvia, aikakauslehtiä ja paljon muuta tuomalla nämä materiaalit eri <a href="https://en.wikipedia.org/wiki/Shadow_library">varjokirjastoista</a>, virallisista kirjastoista ja muista kokoelmista yhteen paikkaan. Kaikki nämä tiedot säilytetään ikuisesti tekemällä niiden monistaminen helpoksi — käyttämällä torrentteja — mikä johtaa moniin kopioihin ympäri maailmaa. Jotkut varjokirjastot tekevät tämän jo itse (esim. Sci-Hub, Library Genesis), kun taas Anna’s Archive "vapauttaa" muita kirjastoja, jotka eivät tarjoa massajakelua (esim. Z-Library) tai eivät ole lainkaan varjokirjastoja (esim. Internet Archive, DuXiu). Tämä laaja jakelu, yhdistettynä avoimen lähdekoodin ohjelmistoon, tekee verkkosivustostamme kestävän alasajoja vastaan ja varmistaa ihmiskunnan tiedon ja kulttuurin pitkäaikaisen säilymisen. Lue lisää <a href="/datasets">datanjoukoistamme</a>. Jos olet <a %(a_member)s>jäsen</a>, selaimen vahvistusta ei tarvita. 🧬&nbsp;SciDB on Sci-Hubin jatko. SciDB Avaa DOI Sci-Hub on <a %(a_paused)s>keskeyttänyt</a> uusien artikkeleiden lataamisen. Suora pääsy %(count)s tieteellisiin artikkeleihin 🧬&nbsp;SciDB on jatkoa Sci-Hubille, sen tutulla käyttöliittymällä ja suoralla PDF-katselulla. Syötä DOI nähdäksesi. Meillä on koko Sci-Hub-kokoelma sekä uusia julkaisuja. Suurin osa voidaan katsoa suoraan tutulla käyttöliittymällä, joka on samanlainen kuin Sci-Hub. Joitakin voi ladata ulkoisista lähteistä, jolloin näytämme linkit niihin. Voit auttaa valtavasti jakamalla torrentteja. <a %(a_torrents)s>Lue lisää…</a> >%(count)s jakajaa <%(count)s jakajaa %(count_min)s–%(count_max)s jakajaa 🤝 Etsimme vapaaehtoisia Olemme voittoa tavoittelematon, avoimen lähdekoodin projekti, ja etsimme aina ihmisiä auttamaan. IPFS-lataukset Listaa %(by)s mukaan, luotu <span %(span_time)s>%(time)s</span> Tallenna ❌ Jotain meni pieleen. Yritä uudelleen. ✅ Tallennettu. Lataa sivu uudelleen. Lista on tyhjä. muokkaa Lisää tai poista tästä luettelosta etsimällä tiedosto ja avaamalla "Luettelot"-välilehti. Lista Kuinka voimme auttaa Päällekkäisyyksien poistaminen (deduplikointi) Tekstin ja metadatan poiminta OCR Pystymme tarjoamaan nopean pääsyn kokoelmiimme sekä julkaisematomiin kokoelmiin. Tämä on yritystason pääsy, jonka voimme tarjota kymmenien tuhansien USD:n lahjoituksilla. Olemme myös valmiita vaihtamaan tämän korkealaatuisiin kokoelmiin, joita meillä ei vielä ole. Voimme hyvittää sinulle, jos pystyt tarjoamaan meille datamme rikastamista, kuten: Tue ihmistiedon pitkäaikaista arkistointia samalla, kun saat parempaa dataa mallillesi! <a %(a_contact)s>Ota yhteyttä</a> keskustellaksesi yhteistyömahdollisuuksista. On hyvin ymmärretty, että LLM:t menestyvät korkealaatuisella datalla. Meillä on maailman suurin kokoelma kirjoja, artikkeleita, lehtiä jne., jotka ovat eräitä korkealaatuisimmista tekstilähteistä. LLM-data Ainutlaatuinen mittakaava ja laajuus Kokoelmamme sisältää yli sata miljoonaa tiedostoa, mukaan lukien tieteelliset lehdet, oppikirjat ja aikakauslehdet. Saavutamme tämän mittakaavan yhdistämällä suuria olemassa olevia arkistoja. Jotkut lähdekokoelmistamme ovat jo saatavilla suurina erinä (Sci-Hub ja osat Libgenistä). Muut lähteet olemme vapauttaneet itse. <a %(a_datasets)s>Datasets</a> näyttää täydellisen yleiskatsauksen. Kokoelmaamme kuuluu miljoonia kirjoja, artikkeleita ja lehtiä ajalta ennen e-kirjojen aikakautta. Suuret osat tästä kokoelmasta on jo OCR-käsitelty, ja niissä on jo vähän sisäistä päällekkäisyyttä. Jatka Jos olet kadottanut avaimen, ole hyvä ja <a %(a_contact)s>ota meihin yhteyttä</a> ja anna mahdollisimman paljon tietoa. Saatat joutua tilapäisesti luomaan uuden tilin ottaaksesi meihin yhteyttä. Ole hyvä ja <a %(a_account)s>kirjaudu sisään</a> nähdäksesi tämän sivun.</a> Estääksemme roskapostibottien tilien luomisen, meidän on ensin vahvistettava selaimesi. Jos jäät kiinni loputtomaan silmukkaan, suosittelemme asentamaan <a %(a_privacypass)s>Privacy Pass</a>. Saattaa myös auttaa, jos poistat mainosten eston ja muut selaimen laajennukset käytöstä. Kirjaudu sisään / Rekisteröidy Anna’s Archive on tilapäisesti huollossa. Palaa tunnin kuluttua. Vaihtoehtoinen kirjailija Vaihtoehtoinen kuvaus Vaihtoehtoinen painos Vaihtoehtoinen laajennus Vaihtoehtoinen tiedostonimi Vaihtoehtoinen julkaisija Vaihtoehtoinen otsikko päivämäärä avoimen lähdekoodin julkaisu Lue lisää… kuvaus Etsi Anna’s Archive -sivustolta CADAL SSNO -numeroa Etsi Annin Arkistosta DuXiu SSID-numeroa Etsi Anna’s Archivesta DuXiu DXID-numeroa Etsi Annan Arkistosta ISBN:llä Etsi Anna’s Archive -arkistosta OCLC (WorldCat) -numeroa Etsi Anna’s Archive -sivustolta Open Library ID:llä Annan Arkiston verkkokatselija %(count)s vaikuttavat sivut Lataamisen jälkeen: Parempi versio tästä tiedostosta saattaa olla saatavilla osoitteessa %(link)s Massatorrentlataukset kokoelma Käytä verkkotyökaluja muuntaaksesi muotojen välillä. Suositellut muunnostyökalut: %(links)s Suurten tiedostojen kohdalla suosittelemme lataushallinnan käyttöä keskeytysten estämiseksi. Suositellut lataushallinnat: %(links)s EBSCOhost eBook Index (vain asiantuntijoille) (klikkaa myös “GET” ylhäällä) (napsauta “GET” ylhäällä) Ulkoiset lataukset <strong>🚀 Nopeat lataukset</strong> Sinulla on %(remaining)s jäljellä tänään. Kiitos, että olet jäsen! ❤️ <strong>🚀 Nopeat lataukset</strong> Olet käyttänyt tämän päivän nopeat lataukset. <strong>🚀 Nopeat lataukset</strong> Olet ladannut tämän tiedoston äskettäin. Linkit pysyvät voimassa jonkin aikaa. <strong>🚀 Nopeat lataukset</strong> Liity <a %(a_membership)s>jäseneksi</a> tukeaksesi kirjojen, artikkeleiden ja muun materiaalin pitkäaikaista säilyttämistä. Kiitollisuuden osoituksena tuestasi saat nopeat lataukset. ❤️ 🚀 Nopeat lataukset 🐢 Hitaat lataukset Lainaa Internet Archivesta IPFS-yhdyskäytävä #%(num)d (saatat joutua yrittämään useita kertoja IPFS:n kanssa) Libgen.li Libgen.rs Kaunokirjallisuus Libgen.rs Non-Fiction heidän mainoksensa tunnetaan haittaohjelmista, joten käytä mainosten estäjää tai älä klikkaa mainoksia Amazonin ”Send to Kindle” djazzin ”Lähetä Kobo/Kindle-laitteeseen” MagzDB ManualsLib Nexus/STC (Nexus/STC-tiedostojen lataaminen voi olla epäluotettavaa) Ei latauksia löytynyt. Kaikissa latausvaihtoehdoissa on sama tiedosto, ja niiden pitäisi olla turvallisia käyttää. Siitä huolimatta ole aina varovainen ladatessasi tiedostoja internetistä, erityisesti Anna’s Archiven ulkopuolisilta sivustoilta. Esimerkiksi varmista, että laitteesi ovat ajan tasalla. (ei uudelleenohjausta) Avaa katselijassamme (avaa katselijassa) Vaihtoehto #%(num)d: %(link)s %(extra)s Löydä alkuperäinen tietue CADAL:ssa Etsi manuaalisesti DuXiu:sta Löydä alkuperäinen tietue ISBNdb:stä Löydä alkuperäinen tietue WorldCatista Löydä alkuperäinen tietue Open Librarysta Etsi erilaisista tietokannoista ISBN:ää (vain tulostusrajoitteisille asiakkaille) PubMed Tarvitset e-kirja- tai PDF-lukijan avataksesi tiedoston, riippuen tiedostomuodosta. Suositellut e-kirjalukijat: %(links)s Annan Arkisto 🧬 SciDB Sci-Hub: %(doi)s (liittyvä DOI ei välttämättä ole saatavilla Sci-Hubissa) Voit lähettää sekä PDF- että EPUB-tiedostoja Kindle- tai Kobo-lukulaitteeseesi. Suositellut työkalut: %(links)s Lisätietoja <a %(a_slow)s>UKK:ssa</a>. Tue kirjailijoita ja kirjastoja Jos pidät tästä ja sinulla on varaa, harkitse alkuperäisen ostamista tai kirjailijoiden tukemista suoraan. Jos tämä on saatavilla paikallisessa kirjastossasi, harkitse sen lainaamista ilmaiseksi sieltä. Yhteistyökumppanin palvelimen lataukset eivät ole tilapäisesti saatavilla tälle tiedostolle. torrent Luotettavilta kumppaneilta. Z-Library Z-Library Tor-verkossa (vaatii Tor-selaimen) näytä ulkoiset lataukset <span class="font-bold">❌ Tässä tiedostossa saattaa olla ongelmia, ja se on piilotettu lähdekirjastosta.</span> Joskus tämä tapahtuu tekijänoikeuden haltijan pyynnöstä, joskus siksi, että parempi vaihtoehto on saatavilla, mutta joskus se johtuu itse tiedoston ongelmasta. Se saattaa silti olla ladattavissa, mutta suosittelemme ensin etsimään vaihtoehtoista tiedostoa. Lisätietoja: Jos haluat edelleen ladata tämän tiedoston, varmista, että käytät vain luotettavaa ja päivitettyä ohjelmistoa sen avaamiseen. metatietojen kommentit AA: Etsi Anna’s Archivesta “%(name)s” Koodien tutkija: Näytä Codes Explorerissa ”%(name)s” URL: Verkkosivusto: Jos sinulla on tämä tiedosto eikä se ole vielä saatavilla Anna’s Archivessa, harkitse sen <a %(a_request)s>lataamista</a>. Internet Archive Controlled Digital Lending -tiedosto ”%(id)s” Tämä on Internet Archivesta peräisin olevan tiedoston tietue, ei suoraan ladattava tiedosto. Voit yrittää lainata kirjan (linkki alla) tai käyttää tätä URL-osoitetta <a %(a_request)s>tiedostoa pyydettäessä</a>. Paranna metatietoja CADAL SSNO %(id)s metadata record Tämä on metatietotietue, ei ladattava tiedosto. Voit käyttää tätä URL-osoitetta, kun <a %(a_request)s>pyydät tiedostoa</a>. DuXiu SSID %(id)s metatietue ISBNdb %(id)s metadata-tietue MagzDB ID %(id)s metatietue Nexus/STC ID %(id)s metatietue OCLC (WorldCat) numero %(id)s metadata-tietue Open Library %(id)s -metatietue Sci-Hub-tiedosto ”%(id)s” Ei löytynyt “%(md5_input)s” ei löytynyt tietokannastamme. Lisää kommentti (%(count)s) Voit saada md5:n URL-osoitteesta, esim. Tämän tiedoston paremman version MD5 (jos saatavilla). Täytä tämä, jos on olemassa toinen tiedosto, joka vastaa läheisesti tätä tiedostoa (sama painos, sama tiedostopääte, jos löydät sellaisen), jota ihmisten tulisi käyttää tämän tiedoston sijaan. Jos tiedät paremman version tästä tiedostosta Anna’s Archivessa, ole hyvä ja <a %(a_upload)s>lataa se</a>. Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudestaan. Jätit kommentin. Sen näkyminen saattaa kestää hetken. Käytäthän <a %(a_copyright)s>DMCA / Tekijänoikeusvaatimuslomaketta</a>. Kuvaile ongelma (pakollinen) Jos tällä tiedostolla on hyvä laatu, voit keskustella siitä täällä! Jos ei, käytä “Ilmoita tiedosto-ongelmasta” -painiketta. Erinomainen tiedoston laatu (%(count)s) Tiedoston laatu Opi, kuinka <a %(a_metadata)s>parantaa tämän tiedoston metadataa</a> itse. Ongelman kuvaus Ole hyvä ja <a %(a_login)s>kirjaudu sisään</a>. Rakastin tätä kirjaa! Auta yhteisöä ilmoittamalla tämän tiedoston laadusta! 🙌 Jotain meni pieleen. Lataa sivu uudelleen ja yritä uudestaan. Ilmoita tiedoston ongelmasta (%(count)s) Kiitos raportin lähettämisestä. Se näytetään tällä sivulla ja tarkistetaan manuaalisesti Annan toimesta (kunnes meillä on asianmukainen moderointijärjestelmä). Jätä kommentti Lähetä raportti Mikä tässä tiedostossa on vialla? Lainaa (%(count)s) Kommentit (%(count)s) Lataukset (%(count)s) Tutki metadataa (%(count)s) Listat (%(count)s) Tilastot (%(count)s) Lisätietoja tästä tiedostosta löytyy sen <a %(a_href)s>JSON-tiedostosta</a>. Tämä on tiedosto, jota hallinnoi <a %(a_ia)s>IA:n Controlled Digital Lending</a> -kirjasto, ja Anna’s Archive on indeksoinut sen hakua varten. Lisätietoja kokoamistamme eri dataseteista löytyy <a %(a_datasets)s>Datasets-sivulta</a>. Metatiedot linkitetystä tietueesta Paranna metatietoja Open Libraryssa “File MD5” on hash, joka lasketaan tiedoston sisällöstä ja on kohtuullisen ainutlaatuinen sen sisällön perusteella. Kaikki täällä indeksoimamme varjokirjastot käyttävät ensisijaisesti MD5-tunnisteita tiedostojen tunnistamiseen. Tiedosto saattaa esiintyä useissa varjokirjastoissa. Lisätietoja kokoamistamme eri dataseteista löytyy <a %(a_datasets)s>Datasets-sivulta</a>. Ilmoita tiedoston laadusta Kokonaislataukset: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tšekkiläinen metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Kirjat %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Varoitus: useita linkitettyjä tietueita: Kun katsot kirjaa Anna’s Archivessa, näet erilaisia kenttiä: otsikko, kirjoittaja, kustantaja, painos, vuosi, kuvaus, tiedostonimi ja paljon muuta. Kaikkia näitä tietoja kutsutaan <em>metatiedoiksi</em>. Koska yhdistämme kirjoja eri <em>lähdekirjastoista</em>, näytämme mitä tahansa metatietoja, jotka ovat saatavilla kyseisessä lähdekirjastossa. Esimerkiksi, jos saamme kirjan Library Genesisistä, näytämme otsikon Library Genesisin tietokannasta. Joskus kirja on saatavilla <em>useissa</em> lähdekirjastoissa, joilla saattaa olla erilaisia metatietokenttiä. Tällöin näytämme yksinkertaisesti kunkin kentän pisimmän version, koska se toivottavasti sisältää hyödyllisimmät tiedot! Näytämme silti muut kentät kuvauksen alla, esim. ”vaihtoehtoinen otsikko” (mutta vain jos ne ovat erilaisia). Poimimme myös <em>koodeja</em> kuten tunnisteita ja luokittelijoita lähdekirjastosta. <em>Tunnisteet</em> edustavat yksilöllisesti tiettyä kirjan painosta; esimerkkejä ovat ISBN, DOI, Open Library ID, Google Books ID tai Amazon ID. <em>Luokittelijat</em> ryhmittelevät useita samanlaisia kirjoja; esimerkkejä ovat Dewey Decimal (DCC), UDC, LCC, RVK tai GOST. Joskus nämä koodit ovat nimenomaisesti linkitetty lähdekirjastoissa, ja joskus voimme poimia ne tiedostonimestä tai kuvauksesta (pääasiassa ISBN ja DOI). Voimme käyttää tunnisteita löytääksemme tietueita <em>vain metatietoja sisältävistä kokoelmista</em>, kuten OpenLibrary, ISBNdb tai WorldCat/OCLC. Hakukoneessamme on erityinen <em>metatietovälilehti</em>, jos haluat selata näitä kokoelmia. Käytämme vastaavia tietueita täyttääksemme puuttuvia metatietokenttiä (esim. jos otsikko puuttuu) tai esim. ”vaihtoehtoisena otsikkona” (jos olemassa oleva otsikko on). Jos haluat nähdä tarkalleen, mistä kirjan metatiedot ovat peräisin, katso kirjan sivulla olevaa <em>”Tekniset tiedot” -välilehteä</em>. Siellä on linkki kyseisen kirjan raakamuotoiseen JSON-tiedostoon, jossa on viittauksia alkuperäisten tietueiden raakamuotoisiin JSON-tiedostoihin. Lisätietoja saat seuraavilta sivuilta: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> ja <a %(a_example)s>Example metadata JSON</a>. Lopuksi, kaikki metatietomme voidaan <a %(a_generated)s>luoda</a> tai <a %(a_downloaded)s>ladata</a> ElasticSearch- ja MariaDB-tietokantoina. Taustatiedot Voit auttaa kirjojen säilyttämisessä parantamalla metatietoja! Lue ensin taustatietoa metatiedoista Anna’s Archivessa ja opi sitten, miten parantaa metatietoja linkittämällä Open Libraryyn, ja ansaitse ilmainen jäsenyys Anna’s Archivessa. Paranna metatietoja Joten jos kohtaat tiedoston, jossa on huonot metatiedot, miten sinun pitäisi korjata se? Voit mennä lähdekirjastoon ja noudattaa sen menettelytapoja metatietojen korjaamiseksi, mutta mitä tehdä, jos tiedosto on saatavilla useissa lähdekirjastoissa? Anna’s Archivessa on yksi tunniste, joka käsitellään erityisesti. <strong>Open Libraryn annas_archive md5 -kenttä ohittaa aina kaikki muut metatiedot!</strong> Palataanpa ensin hieman taaksepäin ja opitaan Open Librarysta. Open Library perustettiin vuonna 2006 Aaron Swartzin toimesta tavoitteena ”yksi verkkosivu jokaiselle koskaan julkaistulle kirjalle”. Se on eräänlainen Wikipedia kirjan metatiedoille: kaikki voivat muokata sitä, se on vapaasti lisensoitu ja se voidaan ladata kokonaisuudessaan. Se on kirjatietokanta, joka on eniten linjassa meidän missiomme kanssa — itse asiassa, Anna’s Archive on saanut inspiraationsa Aaron Swartzin visiosta ja elämästä. Sen sijaan, että keksisimme pyörän uudelleen, päätimme ohjata vapaaehtoisemme Open Libraryyn. Jos näet kirjan, jolla on virheelliset metatiedot, voit auttaa seuraavalla tavalla: Huomaa, että tämä toimii vain kirjoille, ei tieteellisille artikkeleille tai muille tiedostotyypeille. Muiden tiedostotyyppien osalta suosittelemme edelleen lähdekirjaston etsimistä. Muutosten sisällyttäminen Anna’s Archiveen voi kestää muutaman viikon, koska meidän on ladattava uusin Open Library -datadumppi ja luotava hakemisto uudelleen.  Mene <a %(a_openlib)s>Open Library -sivustolle</a>. Löydä oikea kirjatietue. <strong>VAROITUS:</strong> varmista, että valitset oikean <strong>painoksen</strong>. Open Libraryssa on ”teoksia” ja ”painoksia”. ”Teos” voisi olla ”Harry Potter ja viisasten kivi”. ”Painos” voisi olla: Vuoden 1997 ensimmäinen painos, jonka julkaisi Bloomsbery, 256 sivua. Vuoden 2003 pokkaripainos, jonka julkaisi Raincoast Books, 223 sivua. Vuoden 2000 puolankielinen käännös “Harry Potter I Kamie Filozoficzn” Media Rodzina, 328 sivua. Kaikilla näillä painoksilla on eri ISBN-numerot ja eri sisältö, joten varmista, että valitset oikean! Muokkaa tietuetta (tai luo se, jos sitä ei ole olemassa) ja lisää niin paljon hyödyllistä tietoa kuin voit! Olet täällä nyt joka tapauksessa, joten tee tietueesta todella upea. Valitse kohdasta “ID-numerot” “Anna’s Archive” ja lisää kirjan MD5 Anna’s Archivesta. Tämä on pitkä kirjain- ja numerosarja, joka tulee “/md5/” jälkeen URL-osoitteessa. Yritä löytää muita tiedostoja Anna’s Archivesta, jotka vastaavat tätä tietuetta, ja lisää ne myös. Tulevaisuudessa voimme ryhmitellä ne duplikaateiksi Anna’s Archiven hakusivulla. Kun olet valmis, kirjoita ylös URL-osoite, jota juuri päivitit. Kun olet päivittänyt vähintään 30 tietuetta Anna’s Archiven MD5-tunnuksilla, lähetä meille <a %(a_contact)s>sähköpostia</a> ja lähetä meille lista. Annamme sinulle ilmaisen jäsenyyden Anna’s Archiveen, jotta voit helpommin tehdä tätä työtä (ja kiitoksena avustasi). Näiden on oltava korkealaatuisia muokkauksia, jotka lisäävät huomattavan määrän tietoa, muuten pyyntösi hylätään. Pyyntösi hylätään myös, jos jokin muokkauksista peruutetaan tai korjataan Open Libraryn moderaattoreiden toimesta. Open Library -linkitys Jos osallistut merkittävästi työmme kehittämiseen ja toimintaan, voimme keskustella lahjoitustulojen jakamisesta kanssasi, jotta voit käyttää niitä tarpeen mukaan. Maksamme hostingista vasta, kun kaikki on asetettu ja olet osoittanut pystyväsi pitämään arkiston ajan tasalla päivityksillä. Tämä tarkoittaa, että sinun on maksettava ensimmäiset 1-2 kuukautta omasta taskustasi. Aikaasi ei korvata (eikä myöskään meidän), koska tämä on puhdasta vapaaehtoistyötä. Olemme valmiita kattamaan isännöinti- ja VPN-kulut, aluksi jopa 200 dollaria kuukaudessa. Tämä riittää perushakupalvelimelle ja DMCA-suojatulle välityspalvelimelle. Isännöintikulut Ole hyvä <strong>äläkä ota meihin yhteyttä</strong> pyytääksesi lupaa tai esittääksesi peruskysymyksiä. Teot puhuvat enemmän kuin sanat! Kaikki tiedot ovat saatavilla, joten ryhdy vain peilin asettamiseen. Voit vapaasti lähettää tikettejä tai yhdistämispyyntöjä Gitlabiimme, kun kohtaat ongelmia. Saatamme joutua rakentamaan kanssasi joitakin peiliin liittyviä ominaisuuksia, kuten uudelleenbrändäys "Anna’s Archive" -nimestä verkkosivustosi nimeksi, (aluksi) käyttäjätilien poistaminen käytöstä tai linkittäminen pääsivustollemme kirjasivuilta. Kun peilisi on toiminnassa, ota meihin yhteyttä. Haluaisimme tarkistaa OpSecisi, ja kun se on kunnossa, linkitämme peiliisi ja aloitamme tiiviimmän yhteistyön kanssasi. Kiitos etukäteen kaikille, jotka ovat valmiita osallistumaan tähän tapaan! Tämä ei ole heikkohermoisille, mutta se vahvistaisi ihmiskunnan historian suurimman aidosti avoimen kirjaston pitkäikäisyyttä. Aloittaminen Lisätäksemme Anna’s Archiven kestävyyttä, etsimme vapaaehtoisia ylläpitämään peilejä. Versiosi on selvästi erotettavissa peiliksi, esim. "Bob’s Archive, Anna’s Archive -peili". Olet valmis ottamaan tämän työn merkittävät riskit. Sinulla on syvällinen ymmärrys tarvittavasta operatiivisesta turvallisuudesta. <a %(a_shadow)s>Näiden</a> <a %(a_pirate)s>julkaisujen</a> sisältö on sinulle itsestäänselvää. Aluksi emme anna sinulle pääsyä kumppanipalvelimemme latauksiin, mutta jos asiat sujuvat hyvin, voimme jakaa ne kanssasi. Ajet Anna’s Archive -avoin lähdekoodipohjaa ja päivität säännöllisesti sekä koodia että tietoja. Olet valmis osallistumaan <a %(a_codebase)s>koodipohjaamme</a> — yhteistyössä tiimimme kanssa — tämän toteuttamiseksi. Etsimme tätä: Peilit: vapaaehtoisten haku Tee toinen lahjoitus. Ei lahjoituksia vielä. <a %(a_donate)s>Tee ensimmäinen lahjoitukseni.</a> Lahjoitustietoja ei näytetä julkisesti. Lahjoitukseni 📡 Massapeilauksen osalta tutustu <a %(a_datasets)s>Datasets</a> ja <a %(a_torrents)s>Torrents</a> sivuihin. Lataukset IP-osoitteestasi viimeisen 24 tunnin aikana: %(count)s. 🚀 Saadaksesi nopeammat lataukset ja ohittaaksesi selainvarmistukset, <a %(a_membership)s>liity jäseneksi</a>. Lataa kumppanisivustolta Voit jatkaa Anna's Archiven selaamista toisessa välilehdessä odottaessasi (jos selaimesi tukee taustavälilehtien päivittämistä). Voit odottaa useiden lataussivujen latautumista samanaikaisesti (mutta lataa vain yksi tiedosto kerrallaan per palvelin). Kun saat latauslinkin, se on voimassa useita tunteja. Kiitos odottamisesta, tämä pitää verkkosivuston ilmaisena kaikille! 😊 🔗 Kaikki latauslinkit tälle tiedostolle: <a %(a_main)s>Tiedoston pääsivu</a>. ❌ Hitaat lataukset eivät ole saatavilla Cloudflare VPN:ien tai muiden Cloudflare IP-osoitteiden kautta. ❌ Hitaita latauksia on saatavilla vain virallisen verkkosivuston kautta. Vieraile %(websites)s. 📚 Käytä seuraavaa URL-osoitetta ladataksesi: <a %(a_download)s>Lataa nyt</a>. Jotta kaikilla olisi mahdollisuus ladata tiedostoja ilmaiseksi, sinun on odotettava ennen kuin voit ladata tämän tiedoston. Odota <span %(span_countdown)s>%(wait_seconds)s</span> sekuntia ladataksesi tämän tiedoston. Varoitus: IP-osoitteestasi on ladattu paljon viimeisen 24 tunnin aikana. Lataukset voivat olla tavallista hitaampia. Jos käytät VPN:ää, jaettua internet-yhteyttä tai internet-palveluntarjoajasi jakaa IP-osoitteita, tämä varoitus saattaa johtua siitä. Tallenna ❌ Jotain meni pieleen. Yritä uudelleen. ✅ Tallennettu. Lataa sivu uudelleen. Vaihda näyttönimesi. Tunnisteesi (osa “#” jälkeen) ei ole muutettavissa. Profiili luotu <span %(span_time)s>%(time)s</span> muokkaa Listat Luo uusi lista löytämällä tiedosto ja avaamalla “Listat”-välilehti. Ei listoja vielä Profiilia ei löytynyt. Profiili Tällä hetkellä emme voi ottaa vastaan kirjapyyntöjä. Älä lähetä meille kirjapyyntöjä sähköpostitse. Ole hyvä ja tee pyyntösi Z-Library- tai Libgen-foorumeilla. Tallenna Anna’s Archiveen DOI: %(doi)s Lataa SciDB Nexus/STC Esikatselu ei ole vielä saatavilla. Lataa tiedosto <a %(a_path)s>Annin Arkistosta</a>. Tukeaksesi ihmistiedon saavutettavuutta ja pitkäaikaista säilyttämistä, liity <a %(a_donate)s>jäseneksi</a>. Bonuksena 🧬&nbsp;SciDB latautuu nopeammin jäsenille, ilman rajoituksia. Eikö toimi? Kokeile <a %(a_refresh)s>päivittää</a>. Sci-Hub Lisää tarkennettu hakukenttä Hae kuvauksia ja metatietojen kommentteja Julkaisuvuosi Edistynyt Pääsy Sisältö Näytä Luettelo Taulukko Tiedostotyyppi Kieli Järjestä Suurin Merkityksellisin Uusimmat (tiedostokoko) (avoin lähdekoodi) (julkaisuvuosi) Vanhin Satunnainen Pienin Lähde kaapattu ja avattu AA:n toimesta Digitaalinen lainaus (%(count)s) Tiedejulkaisut (%(count)s) Löysimme osumia: %(in)s. Voit viitata siellä löytyvään URL-osoitteeseen, kun <a %(a_request)s>pyydät tiedostoa</a>. Metatiedot (%(count)s) Tutkiaksesi hakemistoa koodeittain, käytä <a %(a_href)s>Codes Explorer</a>. Hakemisto päivitetään kuukausittain. Se sisältää tällä hetkellä merkinnät %(last_data_refresh_date)s asti. Lisätietoja saat %(link_open_tag)sdatasets-sivulta</a>. Sulje pois Sisällytä vain Tarkistamaton lisää… Seuraava … Edellinen Tämä hakemisto sisältää tällä hetkellä metatietoja Internet Archiven Controlled Digital Lending -kirjastosta. <a %(a_datasets)s>Lisätietoja dataseteistämme</a>. Lisää digitaalisia lainakirjastoja löydät <a %(a_wikipedia)s>Wikipediasta</a> ja <a %(a_mobileread)s>MobileRead Wikistä</a>. DMCA / tekijänoikeusvaatimuksia varten <a %(a_copyright)s>klikkaa tästä</a>. Latausaika Virhe haun aikana. Kokeile <a %(a_reload)s>ladata sivu uudelleen</a>. Jos ongelma jatkuu, lähetä meille sähköpostia osoitteeseen %(email)s. Nopea lataus Itse asiassa kuka tahansa voi auttaa säilyttämään nämä tiedostot jakamalla <a %(a_torrents)s>yhdistettyä torrent-listaamme</a>. ➡️ Joskus tämä tapahtuu virheellisesti, kun hakupalvelin on hidas. Tällaisissa tapauksissa <a %(a_attrs)s>uudelleenlataus</a> voi auttaa. ❌ Tässä tiedostossa saattaa olla ongelmia. Etsitkö artikkeleita? Tämä hakemisto sisältää tällä hetkellä metatietoja eri metatietolähteistä. <a %(a_datasets)s>Lisätietoja dataseteistämme</a>. Kirjallisten teosten metatietoja on monista, monista lähteistä ympäri maailmaa. <a %(a_wikipedia)s>Tämä Wikipedian sivu</a> on hyvä aloitus, mutta jos tiedät muita hyviä listoja, ilmoitathan meille. Metatietojen osalta näytämme alkuperäiset tiedot. Emme yhdistä tietueita. Meillä on tällä hetkellä maailman kattavin avoin kirjojen, julkaisujen ja muiden kirjoitettujen teosten luettelo. Peilaamme Sci-Hubia, Library Genesistä, Z-Librarya, <a %(a_datasets)s>ja muita</a>. <span class="font-bold">Ei tiedostoja löytynyt.</span> Kokeile vähemmän tai erilaisia hakutermejä ja suodattimia. Tulokset %(from)s-%(to)s (%(total)s yhteensä) Jos löydät muita "varjokirjastoja", jotka meidän pitäisi peilata, tai jos sinulla on kysyttävää, ota meihin yhteyttä osoitteessa %(email)s. %(num)d osittaiset osumat %(num)d+ osittaiset osumat Kirjoita ruutuun etsiäksesi tiedostoja digitaalisista lainakirjastoista. Kirjoita ruutuun hakeaksesi suoraan ladattavia tiedostoja %(count)s luettelostamme, joita <a %(a_preserve)s>säilytämme ikuisesti</a>. Kirjoita hakukenttään. Kirjoita ruutuun hakeaksesi %(count)s akateemisten artikkelien ja lehtiartikkelien luettelostamme, joita <a %(a_preserve)s>säilytämme ikuisesti</a>. Kirjoita ruutuun etsiäksesi metatietoja kirjastoista. Tämä voi olla hyödyllistä, kun <a %(a_request)s>pyydät tiedostoa</a>. Vinkki: käytä pikanäppäimiä “/” (haku), “enter” (haku), “j” (ylös), “k” (alas), “<” (edellinen sivu), “>” (seuraava sivu) nopeampaan navigointiin. Nämä ovat metatietotietueita, <span %(classname)s>eivät</span> ladattavia tiedostoja. Hakuasetukset Hae Digitaalinen lainaus Lataa Tiedelehdet Metatiedot Uusi haku %(search_input)s - Haku Haku kesti liian kauan, mikä tarkoittaa, että saatat nähdä epätarkkoja tuloksia. Joskus <a %(a_reload)s>sivun uudelleenlataaminen</a> auttaa. Haku kesti liian kauan, mikä on yleistä laajoille kyselyille. Suodattimien määrät eivät välttämättä ole tarkkoja. Suuria latauksia varten (yli 10 000 tiedostoa), joita Libgen tai Z-Library eivät hyväksy, ota yhteyttä meihin osoitteessa %(a_email)s. Libgen.li:lle varmista, että kirjaudut ensin sisään heidän <a %(a_forum)s>foorumilleen</a> käyttäjätunnuksella %(username)s ja salasanalla %(password)s, ja palaa sitten heidän <a %(a_upload_page)s>lataussivulleen</a>. Toistaiseksi suosittelemme uusien kirjojen lataamista Library Genesis -haaroihin. Tässä on <a %(a_guide)s>kätevä opas</a>. Huomaa, että molemmat haarat, joita indeksoimme tällä sivustolla, käyttävät samaa latausjärjestelmää. Pienille latauksille (enintään 10 000 tiedostoa) lataa ne molempiin %(first)s ja %(second)s. Vaihtoehtoisesti voit ladata ne Z-Libraryyn <a %(a_upload)s>tästä</a>. Ladataksesi akateemisia artikkeleita, lataa ne myös (Library Genesisin lisäksi) <a %(a_stc_nexus)s>STC Nexukseen</a>. He ovat paras varjokirjasto uusille artikkeleille. Emme ole vielä integroineet heitä, mutta teemme sen jossain vaiheessa. Voit käyttää heidän <a %(a_telegram)s>Telegram-latausbotiaan</a> tai ottaa yhteyttä heidän kiinnitettyyn viestiinsä listattuun osoitteeseen, jos sinulla on liikaa tiedostoja ladattavaksi tällä tavalla. <span %(label)s>Raskas vapaaehtoistyö (USD$50-USD$5,000 palkkiot):</span> jos pystyt omistamaan paljon aikaa ja/tai resursseja missiollemme, haluaisimme työskennellä tiiviimmin kanssasi. Lopulta voit liittyä sisäiseen tiimiin. Vaikka budjettimme on tiukka, voimme palkita <span %(bold)s>💰 rahapalkkioilla</span> intensiivisimmästä työstä. <span %(label)s>Kevyt vapaaehtoistyö:</span> jos sinulla on vain muutama tunti silloin tällöin, on silti monia tapoja, joilla voit auttaa. Palkitsemme johdonmukaisia vapaaehtoisia <span %(bold)s>🤝 jäsenyyksillä Anna’s Archiveen</span>. Anna’s Archive luottaa kaltaisiisi vapaaehtoisiin. Otamme vastaan kaikenlaisia sitoutumistasoja, ja etsimme apua kahteen pääkategoriaan: Jos et pysty vapaaehtoistyöhön, voit silti auttaa meitä paljon <a %(a_donate)s>lahjoittamalla rahaa</a>, <a %(a_torrents)s>jakamalla torrenttejamme</a>, <a %(a_uploading)s>lataamalla kirjoja</a> tai <a %(a_help)s>kertomalla ystävillesi Anna’s Archivesta</a>. <span %(bold)s>Yritykset:</span> tarjoamme nopean suoran pääsyn kokoelmiimme vastineeksi yritystason lahjoituksesta tai uusien kokoelmien vaihdosta (esim. uudet skannaukset, OCR-datasetit, tietojemme rikastaminen). <a %(a_contact)s>Ota yhteyttä</a> jos tämä koskee sinua. Katso myös <a %(a_llm)s>LLM-sivumme</a>. Palkkiot Etsimme aina ihmisiä, joilla on vankat ohjelmointi- tai hyökkäysturvallisuustaidot, osallistumaan. Voit tehdä merkittävän vaikutuksen ihmiskunnan perinnön säilyttämisessä. Kiitoksena annamme jäsenyyden merkittävistä panoksista. Suurena kiitoksena annamme rahapalkkioita erityisen tärkeistä ja vaikeista tehtävistä. Tätä ei pitäisi pitää työn korvikkeena, mutta se on ylimääräinen kannustin ja voi auttaa syntyneissä kustannuksissa. Suurin osa koodistamme on avoimen lähdekoodin, ja pyydämme samaa myös sinun koodiltasi palkkion myöntämisen yhteydessä. On joitakin poikkeuksia, joista voimme keskustella tapauskohtaisesti. Palkkiot myönnetään ensimmäiselle, joka suorittaa tehtävän. Voit kommentoida palkkiolippua ilmoittaaksesi muille, että työskentelet jonkin asian parissa, jotta muut voivat odottaa tai ottaa sinuun yhteyttä tiimiytymistä varten. Ole kuitenkin tietoinen, että muut voivat silti työskennellä sen parissa ja yrittää voittaa sinut. Emme kuitenkaan myönnä palkkioita huolimattomasta työstä. Jos kaksi korkealaatuista suoritusta tehdään lähellä toisiaan (päivän tai kahden sisällä), voimme harkintamme mukaan myöntää palkkioita molemmille, esimerkiksi 100%% ensimmäisestä suorituksesta ja 50%% toisesta suorituksesta (joten yhteensä 150%%). Suurempien palkkioiden (erityisesti kaavintapalkkioiden) osalta, ota meihin yhteyttä, kun olet suorittanut ~5%% siitä ja olet varma, että menetelmäsi skaalautuu koko virstanpylvääseen. Sinun on jaettava menetelmäsi kanssamme, jotta voimme antaa palautetta. Näin voimme myös päättää, mitä tehdä, jos useat ihmiset ovat lähellä palkkion saavuttamista, kuten mahdollisesti myöntää se useille ihmisille, kannustaa ihmisiä tiimiytymään jne. VAROITUS: korkean palkkion tehtävät ovat <span %(bold)s>vaikeita</span> — voi olla viisasta aloittaa helpommista. Siirry <a %(a_gitlab)s>Gitlab-ongelmalistallemme</a> ja lajittele "Label priority" mukaan. Tämä näyttää suunnilleen tehtävien tärkeysjärjestyksen. Tehtävät, joilla ei ole nimenomaisia palkkioita, ovat silti oikeutettuja jäsenyyteen, erityisesti ne, jotka on merkitty "Hyväksytty" ja "Anna’s favorite". Saatat haluta aloittaa "Aloitusprojektista". Kevyt vapaaehtoistyö Meillä on nyt myös synkronoitu Matrix-kanava osoitteessa %(matrix)s. Jos sinulla on muutama tunti aikaa, voit auttaa monin tavoin. Muista liittyä <a %(a_telegram)s>vapaaehtoisten chattiin Telegramissa</a>. Kiitollisuuden osoituksena annamme yleensä 6 kuukautta “Onnekas Kirjastonhoitaja” perusvirstanpylväistä, ja enemmän jatkuvasta vapaaehtoistyöstä. Kaikki virstanpylväät vaativat korkealaatuista työtä — huolimaton työ vahingoittaa meitä enemmän kuin auttaa, ja hylkäämme sen. Ole hyvä ja <a %(a_contact)s>lähetä meille sähköpostia</a> kun saavutat virstanpylvään. %(links)s linkkejä tai kuvakaappauksia täyttämistäsi pyynnöistä. Täytä kirja- (tai paperi jne.) pyyntöjä Z-Libraryn tai Library Genesis -foorumeilla. Meillä ei ole omaa kirjapyyntöjärjestelmää, mutta peilaamme näitä kirjastoja, joten niiden parantaminen parantaa myös Anna’s Archivea. Virstanpylväs Tehtävä Riippuu tehtävästä. Pienet tehtävät, jotka on julkaistu <a %(a_telegram)s>vapaaehtoisten chatissa Telegramissa</a>. Yleensä jäsenyyttä varten, joskus pienistä palkkioista. Pieniä tehtäviä, jotka on julkaistu vapaaehtoisten keskusteluryhmässämme. Muista jättää kommentti korjaamistasi ongelmista, jotta muut eivät tee samaa työtä uudelleen. %(links)s linkkejä parantamistasi tietueista. Voit käyttää <a %(a_list)s >satunnaisten metadata-ongelmien listaa</a> lähtökohtana. Paranna metatietoja <a %(a_metadata)s>linkittämällä</a> Open Libraryyn. Näiden tulisi näyttää, että kerrot jollekin Anna's Arkistosta, ja he kiittävät sinua. %(links)s linkkejä tai kuvakaappauksia. Anna's Arkiston sanan levittäminen. Esimerkiksi suosittelemalla kirjoja AA:ssa, linkittämällä blogikirjoituksiimme tai ohjaamalla ihmisiä yleisesti verkkosivustollemme. Käännä kokonaan yksi kieli (jos se ei ollut jo lähes valmis). <a %(a_translate)s>Käännä</a> verkkosivusto. Linkki muokkaushistoriaan, joka osoittaa merkittävät panoksesi. Paranna Anna’s Archiven Wikipedia-sivua omalla kielelläsi. Sisällytä tietoa AA:n Wikipedia-sivulta muilla kielillä sekä verkkosivustoltamme ja blogistamme. Lisää viittauksia AA:han muille asiaankuuluville sivuille. Vapaaehtoistyö ja palkkiot 