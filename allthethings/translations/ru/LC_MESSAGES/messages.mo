��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  �d .  �h 7   �j �  k r  �l #  @p k  ds �  �u �   �w �   #x Q   �x �   y �  �y 0  N| �  ~ *  � �  D� S  � �  C� h  ȋ �  1� _  ِ   9� |   <� r  �� �   ,� �  �� (   ڜ u  � Q   y� <   ˠ    � ,   "� �   O� .   ѡ     � `   � 8   ~� @   ��    �� z   � �  �� �  �� �   �� k   
�    y�    �� !   ��    �� 6   ��    �� 	   �    �    .� ?   5�    u� #   {� 	   �� 
   ��    �� H   é 8   � %   E� �  k� �  �   |� 7   �� A  �� �   �� �  �� _  ÷ (   #� �   L� (   -�   V� -   ]� �  �� (   P� �  y� c   #� �   ��    +�    B� 0  c� �  �� �  Y�    S� �   j� �   �� A  �� Q   �� C   '� �   k� v   A� �   �� ^   C�    �� �   ��   c� q   f� �   �� X  �� �   � �  �� �   W� �  �� �   �� =  �� �   �� h  f� �   �� Q   �� �  � C  �� �   � Q   �� �   �    �� 2  � ,  E� Q   r�    �� 5  �� �  �    �� -  �� �   ��   �� d  ��   I� �  K� �  �� �   �� �   Q� �   �� �   f� �   
� �   �� P   n� �   �� �   �� �   ;� %   �� �   �� �   �  E  x   �     � �  � �  � �  � �  	 �   � x  �
 �  $    �   "    �  0    �      � �   � }  �   ; �  Y �    �   � �   � �  �    o#   ~$ �  �& ?   �( �   �( 
  ) �   �* �  /+   �- �   	/ �   �/ <   p0 u  �0 2  #4 �  V5 !   �7 �   8   �8 �  �9 �  w; b  "=   �> �  �A V   0D j  �D n  �F   aK �  }M �  1R �  T �   �U   �V    �W O  �W *  �X �  [ �  �] �  �_   5a    ;b g  Xb .  �d �  �f    }h H  �h   �i �  �k R  �m A  �p �   )r    �r �  �r 8  [u    �w �  �w �  `y j  &{ �  �~ �  � �   �� �   1� b  �� =   #�    a� B  v� �  ��   �� U   �� Q   ދ �  0� �   �� �   t� �   J� �  �� 1   ��    �� �  ۔ *  �� �   �� �   �� �   7� >   ݚ �  � �  �    ��    Ԟ A   � y   +� }   �� e   #� �   �� �   0� ,   
� V   :� �   �� �   "� E   ϣ Y   � b   o� �   Ҥ    Х -   ݥ �  � �  ç �  �� �  U� 1  ,� b  ^� M  �� #   � �  3� �   ߳ -  ô e  � �  W� �  �   z� �  �� Q   �� m   �� @   L� #  �� A  �� �  ��   �� V  �� �  8� �  � �  �� C   �� �  �� 
  �� l  ��     � �  @� /  �� #  
� "  1� �  T� 
   �� A   �� �   8�    �� (  �� j  $�   �� (  �� �  ��    S� �  o� �   � Q   �� �   @� �  �� �  �� �   C� i  ,� �   � �  M %    h  ?   � W  � ,   r  1 @  �    � �  �   �    � =  � w   �  � �  �# �  `' `  �( Q   [+ �  �+ y  u. �   �/ j   �0 �    1    �1 I   �1 -   (2 =   V2 B   �2 #   �2 A  �2 �  =4 B  �8 �  *< �  �?    WA   lA �  F m  vJ �  �L �  �O �  �S    hX �  }X    'Z    6Z   QZ �  W] �  8a   �d !   i }  1i %  �l V  �o �  ,s 	  �v   �{ �  � /   ̂ W  �� �   T� |  � �  �� w  k� �   �   �� @  ͏    �   #� r   C� u   �� 0   ,� P   ]� k   �� M  � �  h� �  L� g   3� 0  �� =  ̥    
� C  � u   _� �   ը l  �� N   � �   b�     � �  ,� �  � 1  α �   � �   շ    �� �   �� �  c�    =�   L� R  \� �   �� R   �� g   � @  i� 7   �� ,  �� �  � 6  �� =   � �   X� �   8�    �� �  ��    �� c  �� ?   S� b  �� K  �� �  B� c  �� �   `� 7   �� �  0� \   �� T  +� .  �� |  �� �  ,� �   �� k   ��   �� �  �� �  �� 
  2� @  =� a   ~� ~  �� �  _� 	  "� L  ,  P  y �  � 7   r �  � M   �
 �  �
 �   �
 ,   k �  � �  U �   �  � ,  p �  � �  �   u$ (  {% �  �( �   �+ �  W, /  ,1 \   \3 �   �3 �   �4 "   _5    �5   �5 �   �7 Z   �8 �  �8 �  �: �  = I  Y? �  �B   @E �  GF 3  I 0   MK -  ~K    �L �  �L 7   GO 7   O    �O    �O 9   �O    P +   /P *   [P 
   �P    �P    �P    �P    �P    �P 9   �P    'Q W   6Q    �Q    �Q    �Q    �Q @  �Q �   S :   �S    �S F   T E   JT O   �T \   �T >   =U    |U    �U >   �U )   �U !   V    )V    EV )   RV    |V (   �V R   �V U   W    [W Q   {W Q   �W H   X J   hX #   �X 1   �X f   	Y @   pY �   �Y t   MZ    �Z �   �Z �   �[    &\    B\ !   S\ -   u\ -   �\ +   �\ +   �\ !   )]    K]    S]    s] 6   �]    �] 	   �] 
   �]    �] =   �]    ^    &^ 	   /^    9^ 	   U^ F   _^    �^    �^ 	   �^    �^    �^ H   �^    "_ ,   *_    W_    u_    �_ 	   �_ !   �_ 1   �_ 
   �_ 
   ` B   `    T` *   c`    �` 5   �`    �`    �` #   �` �    a )  �a �   !c Q  �c �  e �   �g    yh >   �h 3   �h     i    i     i     8i 7   Yi �   �i N   $j �   sj C   k r   Vk J   �k �   l �   �l �  om �   �n o   �o P   �o    Ap    Wp    fp    {p    �p    �p    �p %   �p    �p    q    .q    Lq    Uq %   dq    �q #   �q !   �q !   �q 
   r    r    'r &   8r 8   _r &   �r �  �r    Lt    Qt    ft =   lt    �t �   �t v   :u 0   �u ]   �u Z   @v    �v    �v    �v �   �v    kw    qw =   �w �   �w %   �x    �x �   �x E   �y _  z �   l~   	 8  � �  G� n   ˂ �  :� �   �   ҅ K  Շ #   !� 
   E� �   P� w   ׉ �   O� z   ؊ �   S� n   �� �   g� ;   �� ^   4�    �� 4   �� �   ٍ @   l�    ��    Ɏ 
   � *   � �   �    �� K   � {   ]� $   ِ 1   �� w   0� �   �� 	  >� 1   H� !   z� ;  ��    ؕ 
   �    �� *   �    .� :   =�   x� #   �    �� %   ��    ؘ   � 6   ��    2�    B� �   Q�    7�    @� ?   M� #   ��    �� 7   �� �   � $   ՝ !   �� �   � 0   Ξ %   ��    %� &   6� f   ]� �   ğ 2   �� G   � l  3� �   �� �   s� 6   �� �  0� |   � #   � \   �� :    � ,  ;� J  h� (   �� �   ܩ   j� 5  �� 4   �� =   ��    3� �  M� Y   )� 2   �� &   �� 4   ݰ 9   �   L� 1   X� $   �� V   �� ]   �    d� "   s� .   �� 8   ų i  �� A  h� f  �� y   � V   ��    � >   � �  .� _  � '  F� %   n� x  �� �  
� =   �� 8   �� y  (� �  ��    u�    �� I   ��    ��   �� )   �   <� �  O� �  J�    	� �   %� ;   � :   ?� �   z� %  � �  '�   �� f  �� �   Q� �  ,� w   � J  ��   �� �   �� 8  b� j   �� ]   � &   d�    �� #   �� :   �� 7   �� �   7� U   �� 	   -� E   7�   }� 9   �� �  �� �   c� �   A� ]   %� L   �� 0   ��    � A    � Q   b� b   �� ;   � p  S� @   �� ?  �    E� "  _� �   �� �   4� �  � �  �� 4   |� �   �� 	   ��   �� �   �� 9   {� 
  ��    � 0   � '   � M   & D   t    �    � q   � /  I �  y 
    
    1    +  K "  w   �	 �   �
    `    � (   �     �    � &   
    1 t   9 S   �   
 �     %   � �   � �   � h   O �   � w   x p   � 
   a �   l b   . �   � w   c j   � '   F a  n �   � ^   } �   � �   k V   �    D S   Y �   � �   D P    �   c    Z   k y   m  }   �  �   e!    (" ,  B" r  o% "   �& )   '    /' 
   @' �  K' 8   D) �   }) M  *   O+ �   X, ;  H-    �. �   �/ )  \0 �   �1 �  2 �   �3 b  �4 5  �6 �  #8 
   �9 
   �9 
   �9 �   �9 
   �: 
   �: x   �: �   X; =  �; 
   &= �   4= t   �= �   t> ;   ? �   R? w   "@ 
   �@ �   �@ 
   �A 
   �A 
   �A $  �A �   �D �  �E *   mH    �H    �H   �H '   �I 3    J �  4J �   �K ?   �L    M 3   M `   FM Q   �M Q   �M (   KN (   tN m  �N 7   P �  CP �  �R �   �T �   eU 
  �U �  �V <  �X �  �[ 7  �_ �   �` �   �a    �b R  �b 3   e g  Be �  �g �  7i �   �j   �k ?  �m �   �n �   �o M   �p ]   �p    \q N   vq    �q    �q    �q   r    �s �   �s N   5t    �t    �t    �t "   �t �   �t �   �u }   rv {  �v G   lx    �x    �x 8   �x H   y    cy    uy    �y    �y    �y    �y    �y    �y X   �y �   Az    �z    �z    
{    {    4{    H{    _{    u{    �{    �{ B   �{ �   | !   �| O   �| �   =}   �}   �~ 0  � �  ?� c  ڂ   >� (   Z�   �� Q   �� �   � �   x� T  � S  `� �   �� �   ��    C� �   ]� �   � �   �    Ϗ    ֏ &   � *   
�    8� V   X�    �� 8   ��    � 0   �� 2   *� T   ]� )   ��    ܑ     ��    �     �    8�    O� '   W�    � 9   �� Y   �� �   � �   	� K   Д 2   � �   O� �   �� �   �� \   1� O   �� H   ޗ M   '� �   u� H   � �  W� �  � �  �� )   L� �   v� �   �� v   П �  G� y  � �   �� �   U� *   � 1  �   D� �   _� 2   C� �   v� �  8� '   Ԫ W   �� 1   T� �   �� �   � �   ɬ    �    ��    �� ?   �� �   ׭ \   �� 8   
� T   F� 6   �� >   ү )   � u   ;� 4   �� �   � \   v� �  ӱ �   � �   � �  �� Q   � *   j� %   �� (   �� '   � (   � )   5� (   _� i   �� �   � �  z� �   ?� D   Ǽ Q   � V   ^�    �� �   ν �   �� R  �� M  �� 
   -�   ;� B   S� &   �� |  �� 6   :� k   q� t   �� `   R� �   �� �   O� �   !� )   �� &   ��   �� G   � 3   d� �   �� �  C� m   �� �   6� �   ,� �   �� �   �� J   p� �   �� �   A� )   � �   =� 5   �� R  � U   n� �   �� &   `� $   �� �  �� �   8� r   ��   2� �   6� z   �� �   J�   #�    2�    C� �   Z� �   �� 4   i� 9   ��    �� "   ��    � 3   ,� �   `� �   � H   �� 5   �� @   "� 3   c� |   �� �   � v   � �   �� �   b� F   
�   Q� �  j� H   �� �   9�     �� t   �� d   m� +   �� �   �� �   �� C   �� �   ��    `� H   ~� �   �� "   K� �   n� 9   \�    �� C   �� �   �� �  ��    x�    ��    �� _   ��    2� �   P� Z   �� ?  7� �   w� !   M� n   o� 3  �� %   � r  8� f   �� 3   � �   F� |  ,� 
   ��    ��    �� )   �� F   �� g   ,� �   �� >   �    Z�    l� 0   �� o   �� e   (� 
   �� ~   �� a   �     z� *   �� T   �� (   �    D� �   b� �    e   �    "    5   Q   e y   l    � �  � 
  � J   � �   � .   �   � M   � �   A     �     %   -   C �   q �   0 �   � �   � ;   D �   � s   	 7   } �   � �   F C   � m    G   y �   �   P 5  W A   � W  � �  '  K  '# k   s$ �  �$ �  d' �  )   �* c  �, O   . 9   d. &  �. J   �/ +  0 �   <3 m   
4    {4 4   �4 �  �4    �7 J  �7   9 �  ; 6  �< j   �> �   Z? �   �? P   �@ Y   �@ }   EA �   �A O   hB 7   �B o   �B [   `C &   �C c   �C �   GD U   �D    QE �   ^E }  F   �G #   �H    �H 
   �H z   �H �   zI �   �I   �J �   �K (   hL 4   �L �  �L    �N �   �N �  qO �  %T �    V I   �V    �V    �V    �V ^   �V D   NW �   �W �  gX �   Z    �Z    �Z ,   �Z "   �Z �   [     �[ D   �[    +\ H   >\ M   �\    �\    �\ �   �\    �] #   �] D   �] 9   ^ F   L^ �   �^ �  c_ �   �` �   �a �   lb l  �b    ad 7   rd 9  �d C  �e �  (g    �h �   �h �   �i y   j �   �j �   Lk �   �k #   �l �   �l '   Um -   }m +   �m 1   �m .   	n -   8n -   fn *   �n    �n    �n B   �n B   )o B   lo -   �o E   �o 8   #p 9   \p -   �p    �p n   �p 4   Pq    �q w   �q Z   r �   kr I   s 6   bs (   �s .   �s #   �s    t ~   5t U   �t �   
u W  �u $   w (   &w    Ow    lw �   �w '   x Y   Dx Y   �x �   �x )   �y -   �y    "z 
   )z 	   4z U   >z %   �z y  �z '   4| 5   \| ,   �| '   �| 9   �| &   !} :   H} <   �} @   �} I   ~ G   K~    �~ �   �~ l   E    �    � T  � e   7� <   �� �   ځ ;   ^� �   �� �   �� w   9�    �� -   �� 	   � "   �� *   � 0   F� o  w�   � .   �    � 5   #� &   Y� L   ��    ͉    ҉ �   � o   �� K  � %   P� 3   v� �   �� 3   �� /   ˍ 2   �� 5   .� E   d� 5   ��    ��    �� O   � 3   b� N   �� R   �   8� �   ;� �   Œ �   O� 8   � �   $� 8   �    V� �   r� !    � J   "� ;   m� e   �� �   � @   ��   ڗ '   �    � ,   5�    b� "   y�    �� 3   ��    �     � �   '� �  ̚ 9   z� 7   �� �  � 7  �� /   ٟ &   	�    0�    C� '   S�    {�    ��    ��    ��    Ӡ    �    �� 
   �    �    #�    5�    B�    Q� S   h� k  �� �  (� �  ٤ u  r� $  � �  
� a  ��    � �  � %   �� �  ҳ v  �� �  � =  �� �  <� K   ս   !� t   8� '   �� �   տ �   i� �   � �   �� D  l� �   �� O  �� �  ��    �� w  �� �  c� �   � 	  �� "   �� x  �� �  U� �  �� [  ��    �� �   �� �   �� u  %� �   �� �   �� �   1� !   �� 4   �     F� �   g� ^   �� !   I� �   k� `   [� �   �� 4   b� �   �� �   �� �   r� �   � �   �� �   -� �   �� �   U� �   �� �   ��   N� �   Q�    B� H   U� M   �� �   �� ?   ��    ��    �� n   ��    ]� !   |�    �� r   �� g    � `   �� %   ��    �    �    +� 	   1� �   ;� �   �� �   �� m   0�    �� D   �� X   ��    D�    Z�    q�    ~�    ��    ��    ��    ��    ��    ��    �� '   �    8�    I� #   c�    ��    ��    ��    ��    �� H   �� )   ?� -   i� �   ��     ^� �   � |  "�    ��    ��    ��    ��    ��    
�    �   #� �   9� w   ��    V� *   r� �   ��    Z� �   z� �   7� <   )�    f�   � ~  �� �    � O  �� �   �� =   j� 
  �� /   �� 0   �� t     �   �  /   s �   � �   � �   ] q   1    � 
   �    �    � !   �        2    H �   f �   S �   # 0  � �  '	 �   �
 m   V �  � �  � q  U �   � �  � (  p    � k  � 5  " ~  X �  � U  b! �   �$ m  �% %   ( r   ((   �( �  �) j   2, p  �,    .    . !   $. �   F. s   4/ �   �/ O   f0 �   �0 n   a1 �   �1 1   {2 �   �2 r   �3 1   4 �   O4 x  �4 *   W6  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ru
Language-Team: ru <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library — это популярная (и незаконная) библиотека. Они взяли коллекцию Library Genesis и сделали ее легко доступной для поиска. Кроме того, они стали очень эффективными в привлечении новых книг, поощряя пользователей, вносящих вклад, различными привилегиями. В настоящее время они не возвращают эти новые книги в Library Genesis. И в отличие от Library Genesis, они не делают свою коллекцию легко зеркалируемой, что препятствует широкому сохранению. Это важно для их бизнес-модели, так как они взимают плату за доступ к своей коллекции в большом объеме (более 10 книг в день). Мы не делаем моральных суждений о взимании платы за массовый доступ к незаконной коллекции книг. Несомненно, Z-Library добилась успеха в расширении доступа к знаниям и привлечении большего количества книг. Мы просто здесь, чтобы сделать свою часть: обеспечить долгосрочное сохранение этой частной коллекции. - Анна и команда (<a %(reddit)s>Reddit</a>) В первоначальном выпуске Зеркала Пиратской Библиотеки (ИЗМЕНЕНО: перемещено в <a %(wikipedia_annas_archive)s>Архив Анны</a>), мы создали зеркало Z-Library, большой нелегальной коллекции книг. В качестве напоминания, вот что мы написали в том первоначальном блоге: Эта коллекция датируется серединой 2021 года. Тем временем Z-Library растет с ошеломляющей скоростью: они добавили около 3,8 миллиона новых книг. Конечно, там есть некоторые дубликаты, но большая часть из них, похоже, действительно новые книги или более качественные сканы ранее представленных книг. Это в значительной степени благодаря увеличению числа волонтеров-модераторов в Z-Library и их системе массовой загрузки с удалением дубликатов. Мы хотели бы поздравить их с этими достижениями. Мы рады сообщить, что получили все книги, которые были добавлены в Z-Library между нашим последним зеркалом и августом 2022 года. Мы также вернулись и собрали некоторые книги, которые пропустили в первый раз. В целом, эта новая коллекция составляет около 24 ТБ, что значительно больше, чем предыдущая (7 ТБ). Наше зеркало теперь составляет 31 ТБ в общей сложности. Мы снова удалили дубликаты с Library Genesis, так как для этой коллекции уже доступны торренты. Пожалуйста, перейдите на Pirate Library Mirror, чтобы ознакомиться с новой коллекцией (ИЗМЕНЕНИЕ: перемещено в <a %(wikipedia_annas_archive)s>Архив Анны</a>). Там есть больше информации о том, как структурированы файлы и что изменилось с прошлого раза. Мы не будем ссылаться на это отсюда, так как это просто блог, который не размещает никаких незаконных материалов. Конечно, раздача — это также отличный способ помочь нам. Спасибо всем, кто раздает наш предыдущий набор торрентов. Мы благодарны за положительный отклик и рады, что так много людей заботятся о сохранении знаний и культуры таким необычным способом. 3x новых книги добавлены в Зеркало Пиратской Библиотеки (+24TB, 3,8 миллиона книг) Читайте сопутствующие статьи от TorrentFreak: <a %(torrentfreak)s>первая</a>, <a %(torrentfreak_2)s>вторая</a> - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) сопутствующие статьи от TorrentFreak: <a %(torrentfreak)s>первая</a>, <a %(torrentfreak_2)s>вторая</a> Не так давно «теневые библиотеки» умирали. Sci-Hub, огромный нелегальный архив научных статей, перестал принимать новые работы из-за судебных исков. «Z-Library», крупнейшая нелегальная библиотека книг, увидела, как ее предполагаемых создателей арестовали по обвинению в нарушении авторских прав. Им невероятно удалось избежать ареста, но их библиотека по-прежнему находится под угрозой. Некоторые страны уже делают что-то подобное. TorrentFreak <a %(torrentfreak)s>сообщил</a>, что Китай и Япония ввели исключения для ИИ в свои законы об авторском праве. Нам неясно, как это взаимодействует с международными договорами, но это, безусловно, дает защиту их внутренним компаниям, что объясняет то, что мы наблюдаем. Что касается Архива Анны — мы продолжим нашу подпольную работу, основанную на моральных убеждениях. Однако наше величайшее желание — выйти на свет и легально усилить наше влияние. Пожалуйста, реформируйте авторское право. Когда Z-Library столкнулась с закрытием, я уже сделал резервную копию всей ее библиотеки и искал платформу для ее размещения. Это и стало моей мотивацией для создания Архива Анны: продолжение миссии, стоящей за этими предыдущими инициативами. С тех пор мы выросли до крупнейшей теневой библиотеки в мире, размещая более 140 миллионов защищенных авторским правом текстов в различных форматах — книги, научные статьи, журналы, газеты и многое другое. Моя команда и я — идеологи. Мы считаем, что сохранение и размещение этих файлов — это морально правильно. Библиотеки по всему миру сталкиваются с сокращением финансирования, и мы не можем доверить наследие человечества корпорациям. Затем пришел ИИ. Практически все крупные компании, занимающиеся разработкой LLM, обратились к нам с просьбой обучаться на наших данных. Большинство (но не все!) компаний из США пересмотрели свое решение, когда поняли незаконный характер нашей работы. Напротив, китайские компании с энтузиазмом приняли нашу коллекцию, не беспокоясь о ее законности. Это примечательно, учитывая роль Китая как подписанта почти всех крупных международных договоров об авторском праве. Мы предоставили высокоскоростной доступ примерно 30 компаниям. Большинство из них — компании, занимающиеся LLM, а некоторые — брокеры данных, которые будут перепродавать нашу коллекцию. Большинство из них — китайские, хотя мы также работали с компаниями из США, Европы, России, Южной Кореи и Японии. DeepSeek <a %(arxiv)s>признали</a>, что более ранняя версия была обучена на части нашей коллекции, хотя они неохотно делятся информацией о своей последней модели (вероятно, также обученной на наших данных). Если Запад хочет оставаться впереди в гонке LLM и, в конечном итоге, AGI, ему необходимо пересмотреть свою позицию по авторскому праву, и сделать это вскоре. Согласны вы с нами или нет в нашем моральном обосновании, это теперь становится вопросом экономики и даже национальной безопасности. Все блоки власти строят искусственных суперученых, супер-хакеров и супер-армии. Свобода информации становится вопросом выживания для этих стран — даже вопросом национальной безопасности. Наша команда состоит из людей со всего мира, и у нас нет определенной приверженности. Но мы бы призвали страны с сильными законами об авторском праве использовать эту экзистенциальную угрозу для их реформирования. Итак, что делать? Наш первый совет прост: сократить срок действия авторского права. В США авторское право предоставляется на 70 лет после смерти автора. Это абсурдно. Мы можем привести это в соответствие с патентами, которые предоставляются на 20 лет после подачи заявки. Этого должно быть более чем достаточно для того, чтобы авторы книг, статей, музыки, искусства и других творческих работ получили полное вознаграждение за свои усилия (включая долгосрочные проекты, такие как экранизации). Затем, как минимум, законодатели должны включить исключения для массового сохранения и распространения текстов. Если основной проблемой является потеря доходов от индивидуальных клиентов, то распространение на личном уровне может оставаться запрещенным. В свою очередь, те, кто способен управлять обширными хранилищами — компании, обучающие LLM, а также библиотеки и другие архивы — будут охвачены этими исключениями. Реформа авторского права необходима для национальной безопасности Кратко: китайские LLM (включая DeepSeek) обучены на моем нелегальном архиве книг и статей — крупнейшем в мире. Западу необходимо пересмотреть законы об авторском праве в интересах национальной безопасности. Пожалуйста, ознакомьтесь с <a %(all_isbns)s>оригинальной записью в блоге</a> для получения дополнительной информации. Мы объявили конкурс на улучшение этого. Мы бы наградили первое место призом в $6,000, второе место — $3,000, и третье место — $1,000. Из-за огромного отклика и невероятных заявок мы решили немного увеличить призовой фонд и наградить четыре третьих места по $500 каждое. Победители указаны ниже, но обязательно посмотрите все заявки <a %(annas_archive)s>здесь</a> или скачайте наш <a %(a_2025_01_isbn_visualization_files)s>объединенный торрент</a>. Первое место $6,000: phiresky Эта <a %(phiresky_github)s>заявка</a> (<a %(annas_archive_note_2951)s>комментарий на Gitlab</a>) — это просто все, что мы хотели, и даже больше! Нам особенно понравились невероятно гибкие варианты визуализации (включая поддержку пользовательских шейдеров), но с обширным списком предустановок. Нам также понравилось, насколько все быстро и плавно работает, простая реализация (которая даже не имеет бэкенда), умная миникарта и подробное объяснение в их <a %(phiresky_github)s>блоге</a>. Невероятная работа и заслуженная победа! - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Наши сердца полны благодарности. Заметные идеи Небоскребы для редкости Множество ползунков для сравнения наборов данных, как будто вы диджей. Шкала с количеством книг. Красивые метки. Крутая цветовая схема по умолчанию и тепловая карта. Уникальный вид карты и фильтры Аннотации и также живая статистика Живая статистика Еще несколько идей и реализаций, которые нам особенно понравились: Мы могли бы продолжать еще долго, но давайте остановимся здесь. Обязательно посмотрите все работы <a %(annas_archive)s>здесь</a> или скачайте наш <a %(a_2025_01_isbn_visualization_files)s>объединенный торрент</a>. Так много работ, и каждая приносит уникальную перспективу, будь то в интерфейсе или реализации. Мы, по крайней мере, интегрируем работу, занявшую первое место, в наш основной сайт, а возможно, и некоторые другие. Мы также начали думать о том, как организовать процесс идентификации, подтверждения и затем архивирования самых редких книг. Больше информации будет позже. Спасибо всем, кто участвовал. Это удивительно, что так много людей заботятся. Легкое переключение наборов данных для быстрых сравнений. Все ISBN CADAL SSNOs Утечка данных CERLALC DuXiu SSIDs Индекс электронных книг EBSCOhost Google Книги Goodreads Интернет-архив ISBNdb Международный реестр издателей ISBN Libby Файлы в Архиве Анны Nexus/STC OCLC/Worldcat OpenLibrary Российская государственная библиотека Имперская библиотека Трантора Второе место $3,000: hypha «Хотя идеальные квадраты и прямоугольники математически приятны, они не обеспечивают превосходной локальности в контексте картирования. Я считаю, что асимметрия, присущая этим кривым Гильберта или классическим кривым Мортон, не является недостатком, а скорее особенностью. Точно так же, как знаменитый сапожок Италии делает её мгновенно узнаваемой на карте, уникальные "особенности" этих кривых могут служить когнитивными ориентирами. Эта уникальность может улучшить пространственную память и помочь пользователям ориентироваться, что потенциально облегчает нахождение конкретных регионов или выявление закономерностей». Еще одно невероятное <a %(annas_archive_note_2913)s>представление</a>. Не такое гибкое, как первое место, но нам действительно больше понравилась его макроуровневая визуализация по сравнению с первым местом (заполняющая пространство кривая, границы, маркировка, выделение, панорамирование и масштабирование). <a %(annas_archive_note_2971)s>Комментарий</a> Джо Дэвиса нашел у нас отклик: И всё же множество вариантов для визуализации и рендеринга, а также невероятно плавный и интуитивно понятный интерфейс. Уверенное второе место! - Анна и команда (<a %(reddit)s>Reddit</a>) Несколько месяцев назад мы объявили <a %(all_isbns)s>конкурс с призом в $10,000</a> на создание лучшей возможной визуализации наших данных, показывающей пространство ISBN. Мы подчеркнули важность показа, какие файлы мы уже архивировали, а также предоставили набор данных, описывающий, сколько библиотек хранят ISBN (мера редкости). Мы были поражены откликом. Было так много креативности. Огромное спасибо всем, кто принял участие: ваша энергия и энтузиазм заразительны! В конечном итоге мы хотели ответить на следующие вопросы: <strong>какие книги существуют в мире, сколько из них мы уже архивировали и на какие книги нам следует сосредоточиться дальше?</strong> Приятно видеть, что так много людей заботятся об этих вопросах. Мы начали с базовой визуализации сами. Менее чем в 300 кб это изображение кратко представляет собой самый большой полностью открытый «список книг», когда-либо собранный в истории человечества: Третье место $500 #1: maxlion В этой <a %(annas_archive_note_2940)s>работе</a> нам очень понравились различные виды представлений, особенно сравнение и виды издателей. Третье место $500 #2: abetusk Хотя интерфейс не самый отполированный, эта <a %(annas_archive_note_2917)s>работа</a> отмечает множество пунктов. Нам особенно понравилась функция сравнения. Третье место $500 #3: conundrumer0 Как и первое место, эта <a %(annas_archive_note_2975)s>работа</a> впечатлила нас своей гибкостью. В конечном итоге, это то, что делает инструмент визуализации отличным: максимальная гибкость для опытных пользователей, при этом простота для обычных пользователей. Третье место $500 #4: charelf Последняя <a %(annas_archive_note_2947)s>работа</a>, получившая награду, довольно простая, но имеет уникальные особенности, которые нам очень понравились. Нам понравилось, как они показывают, сколько наборов данных охватывают определенный ISBN как меру популярности/надежности. Также нам очень понравилась простота, но эффективность использования ползунка непрозрачности для сравнений. Победители конкурса на визуализацию ISBN с призом в $10,000 Кратко: Мы получили невероятные представления на конкурс визуализации ISBN с призом в $10,000. Предыстория Как Анна Архив может достичь своей миссии по резервному копированию всех знаний человечества, не зная, какие книги еще существуют? Нам нужен список задач. Один из способов составить его — использовать номера ISBN, которые с 1970-х годов присваиваются каждой опубликованной книге (в большинстве стран). Нет центрального органа, который знает все назначения ISBN. Вместо этого это распределенная система, где страны получают диапазоны номеров, которые затем назначают меньшим издателям, которые могут дальше подразделять диапазоны для мелких издателей. В конечном итоге отдельные номера присваиваются книгам. Мы начали картографировать ISBN <a %(blog)s>два года назад</a> с нашего сбора данных ISBNdb. С тех пор мы собрали много других источников metadata, таких как <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby и другие. Полный список можно найти на страницах «Datasets» и «Torrents» на Аннином Архиве. Теперь у нас есть, безусловно, самая большая полностью открытая, легко загружаемая коллекция metadata книг (и, следовательно, ISBN) в мире. Мы <a %(blog)s>много писали</a> о том, почему нам важно сохранение, и почему мы сейчас находимся в критическом окне. Мы должны сейчас идентифицировать редкие, недостаточно изученные и уникально уязвимые книги и сохранить их. Наличие хорошей metadata по всем книгам в мире помогает в этом. Награда $10,000 Большое внимание будет уделено удобству использования и внешнему виду. Показывайте фактическую metadata для отдельных ISBN при увеличении, такую как название и автор. Лучшая кривая заполнения пространства. Например, зигзаг, идущий от 0 до 4 в первой строке, а затем обратно (в обратном порядке) от 5 до 9 во второй строке — применяется рекурсивно. Различные или настраиваемые цветовые схемы. Специальные виды для сравнения Datasets. Способы отладки проблем, таких как другие metadata, которые плохо согласуются (например, сильно различающиеся названия). Аннотирование изображений с комментариями по ISBN или диапазонам. Любые эвристики для идентификации редких или находящихся под угрозой книг. Любые креативные идеи, которые вы можете придумать! Код Код для генерации этих изображений, а также другие примеры, можно найти в <a %(annas_archive)s>этом каталоге</a>. Мы разработали компактный формат данных, с которым вся необходимая информация ISBN составляет около 75 МБ (в сжатом виде). Описание формата данных и код для его генерации можно найти <a %(annas_archive_l1244_1319)s>здесь</a>. Для вознаграждения вам не обязательно использовать это, но это, вероятно, самый удобный формат для начала. Вы можете преобразовать наши метаданные как угодно (хотя весь ваш код должен быть с открытым исходным кодом). Мы не можем дождаться, чтобы увидеть, что вы придумаете. Удачи! Сделайте форк этого репозитория и отредактируйте HTML этого блога (никакие другие бэкенды, кроме нашего Flask бэкенда, не разрешены). Сделайте так, чтобы изображение выше можно было плавно увеличивать, чтобы вы могли увеличивать до отдельных ISBN. Нажатие на ISBN должно приводить к странице metadata или поиску на Аннином Архиве. Вы всё ещё должны иметь возможность переключаться между всеми различными datasets. Диапазоны стран и издателей должны быть выделены при наведении. Вы можете использовать, например, <a %(github_xlcnd_isbnlib)s>data4info.py в isbnlib</a> для информации о странах и наш сбор данных «isbngrp» для издателей (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Это должно хорошо работать как на настольных компьютерах, так и на мобильных устройствах. Здесь есть много чего исследовать, поэтому мы объявляем награду за улучшение визуализации выше. В отличие от большинства наших наград, эта ограничена по времени. Вы должны <a %(annas_archive)s>представить</a> свой открытый исходный код до 2025-01-31 (23:59 UTC). Лучшее представление получит $6,000, второе место — $3,000, а третье место — $1,000. Все награды будут выплачены с использованием Monero (XMR). Ниже приведены минимальные критерии. Если ни одно представление не соответствует критериям, мы всё равно можем присудить некоторые награды, но это будет на наше усмотрение. Для бонусных баллов (это всего лишь идеи — дайте волю своему творчеству): Вы МОЖЕТЕ полностью отклониться от минимальных критериев и сделать совершенно другую визуализацию. Если она действительно впечатляющая, то это квалифицируется для награды, но на наше усмотрение. Делайте отправки, оставляя комментарий к <a %(annas_archive)s>этому вопросу</a> с ссылкой на ваш форкнутый репозиторий, запрос на слияние или дифф. - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Эта картина имеет размер 1000×800 пикселей. Каждый пиксель представляет 2 500 ISBN. Если у нас есть файл для ISBN, мы делаем этот пиксель более зелёным. Если мы знаем, что ISBN был выдан, но у нас нет соответствующего файла, мы делаем его более красным. Менее чем в 300 КБ эта картина кратко представляет самый большой полностью открытый «список книг», когда-либо собранный в истории человечества (несколько сотен ГБ в сжатом виде). Это также показывает: еще много работы предстоит по резервному копированию книг (у нас всего 16%). Визуализация всех ISBN — награда $10,000 до 2025-01-31 Эта картина представляет собой самый большой полностью открытый «список книг», когда-либо собранный в истории человечества. Визуализация Помимо обзорного изображения, мы также можем посмотреть на отдельные datasets, которые мы приобрели. Используйте выпадающее меню и кнопки, чтобы переключаться между ними. В этих изображениях можно увидеть множество интересных узоров. Почему существует некоторая регулярность линий и блоков, которая, кажется, происходит на разных масштабах? Что представляют собой пустые области? Почему некоторые datasets так сгруппированы? Мы оставим эти вопросы как упражнение для читателя. - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Заключение С этим стандартом мы можем выпускать обновления более постепенно и легче добавлять новые источники данных. У нас уже есть несколько захватывающих релизов в разработке! Мы также надеемся, что другим теневым библиотекам станет легче зеркалировать наши коллекции. В конце концов, наша цель — сохранить человеческие знания и культуру навсегда, так что чем больше избыточности, тем лучше. Пример Рассмотрим наш недавний релиз Z-Library в качестве примера. Он состоит из двух коллекций: “<span style="background: #fffaa3">zlib3_records</span>” и “<span style="background: #ffd6fe">zlib3_files</span>”. Это позволяет нам отдельно извлекать и выпускать метаданные из фактических файлов книг. Таким образом, мы выпустили два торрента с файлами метаданных: Мы также выпустили множество торрентов с папками двоичных данных, но только для коллекции “<span style="background: #ffd6fe">zlib3_files</span>”, всего 62: Запустив <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, мы можем увидеть, что внутри: В этом случае это метаданные книги, как сообщается Z-Library. На верхнем уровне у нас есть только “aacid” и “metadata”, но нет “data_folder”, так как нет соответствующих двоичных данных. AACID содержит “22430000” в качестве основного ID, который, как мы видим, взят из “zlibrary_id”. Мы можем ожидать, что другие AAC в этой коллекции будут иметь такую же структуру. Теперь давайте запустим <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Это гораздо меньший метаданные AAC, хотя основная часть этого AAC находится в другом месте в бинарном файле! В конце концов, у нас есть «data_folder» на этот раз, так что мы можем ожидать, что соответствующие бинарные данные будут находиться в <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. «Метаданные» содержат «zlibrary_id», так что мы можем легко связать его с соответствующим AAC в коллекции «zlib_records». Мы могли бы связать его несколькими способами, например, через AACID — стандарт этого не предписывает. Обратите внимание, что также не обязательно, чтобы поле «метаданные» само по себе было JSON. Это может быть строка, содержащая XML или любой другой формат данных. Вы даже можете хранить информацию о метаданных в связанном бинарном блобе, например, если это большой объем данных. Разнородные файлы и metadata, максимально приближенные к оригинальному формату. Бинарные данные могут обслуживаться напрямую веб-серверами, такими как Nginx. Разнородные идентификаторы в исходных библиотеках или даже их отсутствие. Отдельные выпуски metadata и данных файлов или выпуски только metadata (например, наш выпуск ISBNdb). Распределение через торренты, хотя с возможностью других методов распространения (например, IPFS). Неизменяемые записи, так как мы должны предполагать, что наши торренты будут существовать вечно. Инкрементные выпуски / добавляемые выпуски. Машиночитаемость и возможность записи, удобно и быстро, особенно для нашего стека (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Достаточно простая проверка человеком, хотя это вторично по сравнению с машиночитаемостью. Легкость засевания наших коллекций с помощью стандартного арендованного seedbox. Цели проектирования Нам не важно, чтобы файлы было легко просматривать вручную на диске или искать без предварительной обработки. Нам не важно, чтобы быть напрямую совместимыми с существующим библиотечным программным обеспечением. Хотя должно быть легко для любого засевать нашу коллекцию с помощью торрентов, мы не ожидаем, что файлы будут использоваться без значительных технических знаний и обязательств. Наш основной случай использования — это распространение файлов и связанных с ними metadata из различных существующих коллекций. Наши самые важные соображения: Некоторые нецели: Поскольку Архив Анны является проектом с открытым исходным кодом, мы хотим использовать наш формат напрямую. Когда мы обновляем наш поисковый индекс, мы обращаемся только к общедоступным путям, чтобы любой, кто форкнет нашу библиотеку, мог быстро начать работу. <strong>AAC.</strong> AAC (Контейнер Анны Архив) — это единичный элемент, состоящий из <strong>метаданных</strong> и, по желанию, <strong>бинарных данных</strong>, которые оба являются неизменяемыми. Он имеет глобально уникальный идентификатор, называемый <strong>AACID</strong>. <strong>AACID.</strong> Формат AACID следующий: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Например, фактический AACID, который мы выпустили, это <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Диапазон AACID.</strong> Поскольку AACID содержат монотонно увеличивающиеся временные метки, мы можем использовать это для обозначения диапазонов в рамках определенной коллекции. Мы используем этот формат: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, где временные метки включены. Это соответствует нотации ISO 8601. Диапазоны непрерывны и могут перекрываться, но в случае перекрытия должны содержать идентичные записи, как и ранее выпущенные в этой коллекции (поскольку AAC неизменяемы). Пропущенные записи не допускаются. <code>{collection}</code>: имя коллекции, которое может содержать ASCII буквы, цифры и подчеркивания (но не двойные подчеркивания). <code>{collection-specific ID}</code>: идентификатор, специфичный для коллекции, если применимо, например, ID Z-Library. Может быть опущен или усечен. Должен быть опущен или усечен, если AACID в противном случае превысит 150 символов. <code>{ISO 8601 timestamp}</code>: короткая версия ISO 8601, всегда в UTC, например, <code>20220723T194746Z</code>. Это число должно монотонно увеличиваться с каждым выпуском, хотя его точная семантика может различаться в зависимости от коллекции. Мы предлагаем использовать время сканирования или генерации ID. <code>{shortuuid}</code>: UUID, но сжатый до ASCII, например, с использованием base57. В настоящее время мы используем библиотеку Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Папка с двоичными данными.</strong> Папка с двоичными данными для ряда AAC, для одной конкретной коллекции. Они имеют следующие свойства: Директория должна содержать файлы данных для всех AAC в указанном диапазоне. Каждый файл данных должен иметь AACID в качестве имени файла (без расширений). Имя директории должно быть диапазоном AACID, с префиксом <code style="color: green">annas_archive_data__</code>, и без суффикса. Например, одна из наших фактических версий имеет директорию с именем<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Рекомендуется делать эти папки управляемыми по размеру, например, не более 100 ГБ-1 ТБ каждая, хотя эта рекомендация может измениться со временем. <strong>Коллекция.</strong> Каждый AAC принадлежит коллекции, которая по определению является списком AAC, которые семантически согласованы. Это означает, что если вы вносите значительное изменение в формат метаданных, то вам нужно создать новую коллекцию. Стандарт <strong>Файл metadata.</strong> Файл metadata содержит metadata диапазона AAC для одной конкретной коллекции. Они имеют следующие свойства: <code>data_folder</code> является необязательным и представляет собой имя папки с двоичными данными, содержащей соответствующие двоичные данные. Имя файла соответствующих двоичных данных в этой папке — это AACID записи. Каждый объект JSON должен содержать следующие поля на верхнем уровне: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (необязательно). Другие поля не допускаются. Имя файла должно быть диапазоном AACID, с префиксом <code style="color: red">annas_archive_meta__</code> и суффиксом <code>.jsonl.zstd</code>. Например, один из наших релизов называется<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Как указано в расширении файла, тип файла — <a %(jsonlines)s>JSON Lines</a>, сжатый с помощью <a %(zstd)s>Zstandard</a>. <code>metadata</code> — это произвольные metadata, в соответствии с семантикой коллекции. Они должны быть семантически согласованы в рамках коллекции. Префикс <code style="color: red">annas_archive_meta__</code> может быть адаптирован к названию вашего учреждения, например, <code style="color: red">my_institute_meta__</code>. <strong>Коллекции «записей» и «файлов».</strong> По традиции, часто удобно выпускать «записи» и «файлы» как разные коллекции, чтобы их можно было выпускать по разным графикам, например, на основе скорости сканирования. «Запись» — это коллекция только метаданных, содержащая информацию, такую как названия книг, авторы, ISBN и т. д., в то время как «файлы» — это коллекции, содержащие сами файлы (pdf, epub). В конечном итоге мы остановились на относительно простом стандарте. Он довольно гибкий, не является нормативным и находится в процессе разработки. <strong>Торренты.</strong> Файлы метаданных и папки с двоичными данными могут быть объединены в торренты, с одним торрентом на файл метаданных или одним торрентом на папку с двоичными данными. Торренты должны иметь оригинальное имя файла/директории плюс суффикс <code>.torrent</code> в качестве имени файла. <a %(wikipedia_annas_archive)s>Архив Анны</a> стал, безусловно, крупнейшей в мире теневой библиотекой и единственной теневой библиотекой такого масштаба, которая полностью с открытым исходным кодом и открытыми данными. Ниже представлена таблица с нашей страницы Датасетов (слегка измененная): Мы достигли этого тремя способами: Зеркалирование существующих теневых библиотек с открытыми данными (таких как Sci-Hub и Library Genesis). Помощь теневым библиотекам, которые хотят стать более открытыми, но у них не было времени или ресурсов для этого (например, коллекция комиксов Libgen). Скрапинг библиотек, которые не хотят делиться данными в большом объеме (например, Z-Library). Для (2) и (3) мы теперь управляем значительной коллекцией торрентов самостоятельно (сотни терабайт). До сих пор мы подходили к этим коллекциям как к единичным проектам, что означало индивидуальную инфраструктуру и организацию данных для каждой коллекции. Это добавляет значительные накладные расходы к каждому выпуску и делает особенно сложным выполнение более инкрементных выпусков. Вот почему мы решили стандартизировать наши выпуски. Это технический блог-пост, в котором мы представляем наш стандарт: <strong>Контейнеры Архива Анны</strong>. Контейнеры Архива Анны (AAC): стандартизация релизов из крупнейшей в мире теневой библиотеки Архив Анны стал крупнейшей в мире теневой библиотекой, что требует от нас стандартизации наших релизов. Выпущено более 300 ГБ обложек книг Наконец, мы рады объявить о небольшом выпуске. В сотрудничестве с людьми, управляющими форком Libgen.rs, мы делимся всеми их обложками книг через торренты и IPFS. Это распределит нагрузку по просмотру обложек между большим количеством машин и лучше их сохранит. Во многих (но не во всех) случаях обложки книг включены в сами файлы, так что это своего рода «производные данные». Но наличие их в IPFS все равно очень полезно для повседневной работы как Архива Анны, так и различных форков Library Genesis. Как обычно, вы можете найти этот выпуск в Pirate Library Mirror (ИЗМЕНЕНО: перемещено в <a %(wikipedia_annas_archive)s>Архив Анны</a>). Мы не будем ссылаться на него здесь, но вы легко его найдете. Надеемся, что теперь мы сможем немного расслабить темп, так как у нас есть достойная альтернатива Z-Library. Эта рабочая нагрузка не особенно устойчива. Если вы заинтересованы в помощи с программированием, управлением серверами или работой по сохранению, обязательно свяжитесь с нами. Еще много <a %(annas_archive)s>работы предстоит сделать</a>. Спасибо за ваш интерес и поддержку. Переход на ElasticSearch Некоторые запросы занимали очень много времени, до такой степени, что они захватывали все открытые соединения. По умолчанию MySQL имеет минимальную длину слова, или ваш индекс может стать действительно большим. Люди сообщали, что не могут искать «Бен Гур». Поиск был лишь частично быстрым, когда полностью загружался в память, что требовало от нас приобретения более дорогой машины для его запуска, а также некоторых команд для предварительной загрузки индекса при запуске. Мы не смогли бы легко расширить его для создания новых функций, таких как лучшая <a %(wikipedia_cjk_characters)s>токенизация для языков без пробелов</a>, фильтрация/фасетирование, сортировка, предложения "вы имели в виду", автозаполнение и так далее. Один из наших <a %(annas_archive)s>тикетов</a> был сборником проблем с нашей поисковой системой. Мы использовали полнотекстовый поиск MySQL, так как все наши данные были в MySQL. Но у него были свои ограничения: После консультаций с рядом экспертов мы остановились на ElasticSearch. Это решение не было идеальным (их стандартные предложения «вы имели в виду» и функции автозаполнения оставляют желать лучшего), но в целом оно оказалось намного лучше, чем MySQL для поиска. Мы все еще не <a %(youtube)s>слишком уверены</a> в использовании его для критически важных данных (хотя они сделали много <a %(elastic_co)s>прогресса</a>), но в целом мы довольны переходом. На данный момент мы внедрили гораздо более быстрый поиск, лучшую поддержку языков, более релевантную сортировку, различные варианты сортировки и фильтрацию по языку/типу книги/типу файла. Если вам интересно, как это работает, <a %(annas_archive_l140)s>ознакомьтесь</a> <a %(annas_archive_l1115)s>с</a> <a %(annas_archive_l1635)s>этим</a>. Это довольно доступно, хотя не помешало бы добавить больше комментариев… Архив Анны полностью с открытым исходным кодом Мы считаем, что информация должна быть свободной, и наш собственный код не является исключением. Мы выпустили весь наш код на нашем частном экземпляре Gitlab: <a %(annas_archive)s>Программное обеспечение Анны</a>. Мы также используем трекер задач для организации нашей работы. Если вы хотите участвовать в нашем развитии, это отличное место для начала. Чтобы дать вам представление о том, над чем мы работаем, возьмите нашу недавнюю работу по улучшению производительности на стороне клиента. Поскольку мы еще не реализовали пагинацию, мы часто возвращали очень длинные страницы поиска с 100-200 результатами. Мы не хотели обрезать результаты поиска слишком рано, но это означало, что это замедляло некоторые устройства. Для этого мы реализовали небольшую хитрость: мы обернули большинство результатов поиска в HTML-комментарии (<code><!-- --></code>), а затем написали небольшой Javascript, который определял, когда результат должен стать видимым, в этот момент мы разворачивали комментарий: DOM «виртуализация» реализована в 23 строках, нет необходимости в сложных библиотеках! Это тот тип быстрого прагматичного кода, который вы получаете, когда у вас ограничено время и реальные проблемы, которые нужно решать. Сообщается, что наш поиск теперь хорошо работает на медленных устройствах! Еще одним большим усилием было автоматизировать создание базы данных. Когда мы запустились, мы просто хаотично собрали разные источники вместе. Теперь мы хотим поддерживать их в актуальном состоянии, поэтому мы написали кучу скриптов для загрузки новых метаданных из двух форков Library Genesis и интеграции их. Цель состоит не только в том, чтобы сделать это полезным для нашего архива, но и упростить задачу для любого, кто хочет поиграть с метаданными теневой библиотеки. Целью будет Jupyter notebook, который содержит всевозможные интересные метаданные, чтобы мы могли проводить больше исследований, например, выясняя, какой <a %(blog)s>процент ISBN сохраняется навсегда</a>. Наконец, мы обновили нашу систему пожертвований. Теперь вы можете использовать кредитную карту, чтобы напрямую вносить деньги в наши криптокошельки, не зная ничего о криптовалютах. Мы будем продолжать следить за тем, как это работает на практике, но это большое дело. С закрытием Z-Library и арестом (предполагаемых) основателей, мы работали круглосуточно, чтобы предоставить хорошую альтернативу с Архивом Анны (мы не будем ссылаться на него здесь, но вы можете найти его в Google). Вот некоторые из достижений, которых мы добились недавно. Обновление Анны: полностью открытый архив, ElasticSearch, более 300 ГБ обложек книг Мы работали круглосуточно, чтобы предоставить хорошую альтернативу с Архивом Анны. Вот некоторые из достижений, которых мы добились недавно. Анализ Семантические дубликаты (разные сканы одной и той же книги) теоретически можно отфильтровать, но это сложно. При ручном просмотре комиксов мы нашли слишком много ложных срабатываний. Есть некоторые дубликаты только по MD5, что относительно расточительно, но фильтрация их дала бы нам только около 1% in экономии. В таких масштабах это все равно около 1 ТБ, но также, в таких масштабах 1 ТБ не имеет большого значения. Мы предпочли бы не рисковать случайным уничтожением данных в этом процессе. Мы нашли кучу данных, не относящихся к книгам, таких как фильмы, основанные на комиксах. Это также кажется расточительным, поскольку они уже широко доступны другими способами. Однако мы поняли, что не можем просто отфильтровать файлы фильмов, так как есть также <em>интерактивные комиксы</em>, которые были выпущены на компьютере, и кто-то записал и сохранил их как фильмы. В конечном итоге, все, что мы могли бы удалить из коллекции, сэкономило бы лишь несколько процентов. Затем мы вспомнили, что мы — хранители данных, и люди, которые будут зеркалировать это, тоже хранители данных, и поэтому: «ЧТО ВЫ ИМЕЕТЕ В ВИДУ, УДАЛИТЬ?!» :) Когда вы получаете 95 ТБ данных в ваш кластер хранения, вы пытаетесь понять, что же там вообще находится… Мы провели анализ, чтобы выяснить, можем ли мы немного уменьшить размер, например, удалив дубликаты. Вот некоторые из наших находок: Поэтому мы представляем вам полную, немодифицированную коллекцию. Это много данных, но мы надеемся, что достаточно людей захотят раздавать их. Сотрудничество Учитывая его размер, эта коллекция давно была в нашем списке желаний, поэтому после нашего успеха с резервным копированием Z-Library мы нацелились на эту коллекцию. Сначала мы напрямую извлекали данные, что было довольно сложной задачей, так как их сервер был не в лучшем состоянии. Таким образом, мы получили около 15 ТБ, но это было медленно. К счастью, нам удалось связаться с оператором библиотеки, который согласился отправить нам все данные напрямую, что было намного быстрее. Тем не менее, потребовалось более полугода, чтобы передать и обработать все данные, и мы почти потеряли их из-за повреждения диска, что означало бы начало всего заново. Этот опыт заставил нас поверить, что важно как можно быстрее распространить эти данные, чтобы их можно было зеркалировать повсеместно. Мы всего в одном или двух неудачных инцидентах от потери этой коллекции навсегда! Коллекция Быстрое движение означает, что коллекция немного неорганизована… Давайте посмотрим. Представьте, что у нас есть файловая система (которую на самом деле мы разбиваем на торренты): Первый каталог, <code>/repository</code>, является более структурированной частью этого. Этот каталог содержит так называемые «тысячные директории»: каталоги, каждый из которых содержит тысячи файлов, которые инкрементально нумеруются в базе данных. Каталог <code>0</code> содержит файлы с comic_id 0–999 и так далее. Это та же схема, которую использует Library Genesis для своих коллекций художественной и научной литературы. Идея заключается в том, что каждая «тысячная директория» автоматически превращается в торрент, как только она заполняется. Однако оператор Libgen.li никогда не создавал торренты для этой коллекции, и поэтому тысячи директорий, вероятно, стали неудобными и уступили место «несортированным директориям». Это <code>/comics0</code> до <code>/comics4</code>. Все они содержат уникальные структуры директорий, которые, вероятно, имели смысл для сбора файлов, но сейчас для нас не очень понятны. К счастью, metadata все еще напрямую ссылается на все эти файлы, так что их организация на диске на самом деле не имеет значения! Metadata доступна в виде базы данных MySQL. Ее можно скачать напрямую с сайта Libgen.li, но мы также сделаем ее доступной в торренте, вместе с нашей собственной таблицей со всеми хешами MD5. <q>Доктор Барбара Гордон пытается потеряться в обыденном мире библиотеки…</q> Форки Libgen Сначала немного предыстории. Вы, возможно, знаете Library Genesis за их эпическую коллекцию книг. Меньше людей знают, что волонтеры Library Genesis создали другие проекты, такие как значительная коллекция журналов и стандартных документов, полный резервный копия Sci-Hub (в сотрудничестве с основателем Sci-Hub, Александрой Элбакян), и, действительно, огромная коллекция комиксов. В какой-то момент разные операторы зеркал Library Genesis пошли своими путями, что привело к текущей ситуации, когда существует несколько различных «форков», все еще носящих имя Library Genesis. Форк Libgen.li уникален тем, что имеет эту коллекцию комиксов, а также значительную коллекцию журналов (над которой мы также работаем). Сбор средств Мы выпускаем эти данные в нескольких больших частях. Первый торрент — это <code>/comics0</code>, который мы поместили в один огромный 12 ТБ .tar файл. Это лучше для вашего жесткого диска и торрент-программного обеспечения, чем множество мелких файлов. В рамках этого выпуска мы проводим сбор средств. Мы стремимся собрать $20,000 для покрытия операционных и контрактных расходов на эту коллекцию, а также для поддержки текущих и будущих проектов. У нас в разработке несколько <em>грандиозных</em> проектов. <em>Кого я поддерживаю своим пожертвованием?</em> Вкратце: мы сохраняем все знания и культуру человечества и делаем их легко доступными. Весь наш код и данные являются открытым исходным кодом, мы полностью волонтерский проект, и мы уже сохранили 125 ТБ книг (в дополнение к существующим торрентам Libgen и Scihub). В конечном итоге мы создаем маховик, который позволяет и стимулирует людей находить, сканировать и сохранять все книги в мире. Мы напишем о нашем мастер-плане в будущем посте. :) Если вы пожертвуете на 12-месячное членство «Удивительный Архивариус» ($780), вы сможете <strong>«усыновить торрент»</strong>, что означает, что мы добавим ваше имя пользователя или сообщение в имя файла одного из торрентов! Вы можете сделать пожертвование, перейдя на <a %(wikipedia_annas_archive)s>Архив Анны</a> и нажав кнопку «Пожертвовать». Мы также ищем больше волонтеров: инженеров-программистов, исследователей безопасности, экспертов по анонимной торговле и переводчиков. Вы также можете поддержать нас, предоставив услуги хостинга. И, конечно, пожалуйста, раздавайте наши торренты! Спасибо всем, кто уже так щедро нас поддержал! Вы действительно делаете разницу. Вот торренты, выпущенные на данный момент (мы все еще обрабатываем остальные): Все торренты можно найти на <a %(wikipedia_annas_archive)s>Архиве Анны</a> в разделе «Datasets» (мы не даем прямых ссылок, чтобы ссылки на этот блог не удалялись с Reddit, Twitter и т.д.). Оттуда следуйте по ссылке на сайт Tor. <a %(news_ycombinator)s>Обсудить на Hacker News</a> Что дальше? Множество торрентов отлично подходит для долгосрочного сохранения, но не так уж и для повседневного доступа. Мы будем работать с партнерами по хостингу, чтобы разместить все эти данные в интернете (поскольку Архив Анны ничего не размещает напрямую). Конечно, вы сможете найти эти ссылки для скачивания в Архиве Анны. Мы также приглашаем всех работать с этими данными! Помогите нам лучше их анализировать, удалять дубликаты, размещать на IPFS, ремиксировать, обучать ваши модели ИИ и так далее. Это все ваше, и мы не можем дождаться, чтобы увидеть, что вы с этим сделаете. Наконец, как уже говорилось ранее, у нас все еще есть несколько крупных выпусков, которые скоро появятся (если <em>кто-то</em> случайно <em>пришлет</em> нам дамп <em>определенной</em> базы данных ACS4, вы знаете, где нас найти...), а также создание маховика для резервного копирования всех книг в мире. Так что оставайтесь с нами, мы только начинаем. - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Крупнейшая теневая библиотека комиксов, вероятно, принадлежит определенному форку Library Genesis: Libgen.li. Один администратор, управляющий этим сайтом, сумел собрать невероятную коллекцию комиксов из более чем 2 миллионов файлов, общим объемом более 95 ТБ. Однако, в отличие от других коллекций Library Genesis, эта не была доступна в массовом порядке через торренты. Вы могли получить доступ к этим комиксам только индивидуально через его медленный личный сервер — единую точку отказа. До сегодняшнего дня! В этом посте мы расскажем вам больше об этой коллекции и о нашем сборе средств для поддержки этой работы. Архив Анны сделал резервную копию крупнейшей в мире теневой библиотеки комиксов (95 ТБ) — вы можете помочь с раздачей Крупнейшая в мире теневая библиотека комиксов имела единую точку отказа... до сегодняшнего дня. Внимание: этот блог-пост устарел. Мы решили, что IPFS еще не готов для массового использования. Мы все еще будем ссылаться на файлы в IPFS из Архива Анны, когда это возможно, но больше не будем размещать их у себя и не рекомендуем другим зеркалировать с использованием IPFS. Пожалуйста, посетите нашу страницу Торрентов, если вы хотите помочь сохранить нашу коллекцию. Размещение 5 998 794 книг на IPFS Умножение копий Возвращаясь к нашему первоначальному вопросу: как мы можем утверждать, что сохраняем наши коллекции навсегда? Основная проблема здесь в том, что наша коллекция <a %(torrents_stats)s>растет</a> быстрыми темпами, благодаря сбору данных и открытию некоторых крупных коллекций (в дополнение к удивительной работе, уже проделанной другими библиотеками открытых данных, такими как Sci-Hub и Library Genesis). Этот рост данных усложняет зеркалирование коллекций по всему миру. Хранение данных дорого! Но мы оптимистичны, особенно наблюдая за следующими тремя тенденциями. <a %(annas_archive_stats)s>Общий размер</a> наших коллекций за последние несколько месяцев, разбитый по количеству раздающих торрент. Тенденции цен на HDD из разных источников (нажмите, чтобы просмотреть исследование). <a %(critical_window_chinese)s>Китайская версия 中文版</a>, обсудить на <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Мы собрали легкодоступные плоды Это напрямую следует из наших приоритетов, обсужденных выше. Мы предпочитаем сначала работать над освобождением крупных коллекций. Теперь, когда мы обеспечили некоторые из крупнейших коллекций в мире, мы ожидаем, что наш рост будет значительно медленнее. Существует еще длинный хвост меньших коллекций, и новые книги сканируются или публикуются каждый день, но скорость, вероятно, будет значительно медленнее. Мы все еще можем удвоиться или даже утроиться в размере, но за более длительный период времени. Улучшения OCR. Приоритеты Программный код для науки и техники Вымышленные или развлекательные версии всего вышеперечисленного Географические данные (например, карты, геологические исследования) Внутренние данные корпораций или правительств (утечки) Данные измерений, такие как научные измерения, экономические данные, корпоративные отчеты Записи метаданных в целом (о научной и художественной литературе; о других медиа, искусстве, людях и т. д.; включая отзывы) Научно-популярные книги Научно-популярные журналы, газеты, руководства Транскрипты научно-популярных выступлений, документальных фильмов, подкастов Органические данные, такие как последовательности ДНК, семена растений или микробные образцы Академические статьи, журналы, отчеты Научные и инженерные веб-сайты, онлайн-дискуссии Транскрипты юридических или судебных разбирательств Уникально подвержены риску уничтожения (например, из-за войны, сокращения финансирования, судебных исков или политических преследований) Редкие Уникально недооцененные Почему нас так волнуют статьи и книги? Давайте отложим в сторону нашу фундаментальную веру в сохранение в целом — возможно, мы напишем об этом отдельный пост. Так почему именно статьи и книги? Ответ прост: <strong>информационная плотность</strong>. На мегабайт хранения, письменный текст хранит больше всего информации из всех медиа. Хотя нас волнуют как знания, так и культура, мы больше заботимся о первых. В целом, мы видим иерархию информационной плотности и важности сохранения, которая выглядит примерно так: Рейтинг в этом списке несколько произволен — некоторые пункты имеют одинаковые позиции или вызывают разногласия в нашей команде — и мы, вероятно, забыли некоторые важные категории. Но это примерно так, как мы расставляем приоритеты. Некоторые из этих пунктов слишком отличаются от других, чтобы мы беспокоились о них (или уже находятся под опекой других учреждений), например, органические данные или географические данные. Но большинство пунктов в этом списке действительно важны для нас. Еще одним важным фактором в нашей приоритизации является степень риска для определенного произведения. Мы предпочитаем сосредотачиваться на произведениях, которые: Наконец, нас волнует масштаб. У нас ограничено время и деньги, поэтому мы предпочли бы потратить месяц на сохранение 10 000 книг, чем 1 000 книг — если они примерно одинаково ценны и подвержены риску. <em><q>Потерянное не может быть восстановлено; но давайте сохраним то, что осталось: не с помощью хранилищ и замков, которые ограждают их от общественного взгляда и использования, предавая их забвению времени, а с помощью такого множества копий, которое сделает их недоступными для случайностей.</q></em><br>— Томас Джефферсон, 1791 Теневые библиотеки Код может быть с открытым исходным кодом на Github, но сам Github в целом не может быть легко зеркалирован и, следовательно, сохранен (хотя в этом конкретном случае существует достаточно распределенных копий большинства репозиториев кода). Записи метаданных можно свободно просматривать на сайте Worldcat, но не загружать массово (пока мы их не <a %(worldcat_scrape)s>собрали</a>). Reddit бесплатен для использования, но недавно ввел строгие меры против сбора данных, в связи с обучением LLM, требующим большого объема данных (подробнее об этом позже). Существует множество организаций с похожими миссиями и приоритетами. Действительно, есть библиотеки, архивы, лаборатории, музеи и другие учреждения, занимающиеся сохранением такого рода. Многие из них хорошо финансируются правительствами, частными лицами или корпорациями. Но у них есть одно огромное слепое пятно: правовая система. В этом заключается уникальная роль теневых библиотек и причина существования Архива Анны. Мы можем делать то, что другим учреждениям не разрешено. Теперь, это не (часто) означает, что мы можем архивировать материалы, которые незаконно сохранять в других местах. Нет, во многих местах законно создавать архив с любыми книгами, статьями, журналами и так далее. Но чего часто не хватает в легальных архивах, так это <strong>избыточности и долговечности</strong>. Существуют книги, из которых существует только одна копия в какой-то физической библиотеке. Существуют записи метаданных, охраняемые одной корпорацией. Существуют газеты, сохраненные только на микрофильмах в одном архиве. Библиотеки могут столкнуться с сокращением финансирования, корпорации могут обанкротиться, архивы могут быть разбомблены и сожжены дотла. Это не гипотеза — это происходит постоянно. То, что мы можем уникально делать в Архиве Анны, — это хранить множество копий произведений в большом масштабе. Мы можем собирать статьи, книги, журналы и многое другое и распространять их оптом. В настоящее время мы делаем это через торренты, но точные технологии не имеют значения и будут меняться со временем. Важно, чтобы множество копий было распространено по всему миру. Эта цитата, сделанная более 200 лет назад, все еще актуальна: Краткое замечание о общественном достоянии. Поскольку Архив Анны уникально сосредоточен на деятельности, которая является незаконной во многих местах по всему миру, мы не беспокоимся о широко доступных коллекциях, таких как книги общественного достояния. Юридические лица часто уже хорошо заботятся об этом. Однако существуют соображения, которые иногда заставляют нас работать над общедоступными коллекциями: - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Стоимость хранения продолжает экспоненциально снижаться 3. Улучшения в плотности информации В настоящее время мы храним книги в тех форматах, в которых они нам предоставлены. Конечно, они сжаты, но часто это все еще большие сканы или фотографии страниц. До сих пор единственными вариантами уменьшения общего размера нашей коллекции были более агрессивное сжатие или дедупликация. Однако, чтобы получить значительную экономию, оба варианта слишком потеряны для нашего вкуса. Сильное сжатие фотографий может сделать текст едва читаемым. А дедупликация требует высокой уверенности в том, что книги точно такие же, что часто слишком неточно, особенно если содержимое одинаково, но сканы сделаны в разное время. Всегда был третий вариант, но его качество было настолько ужасным, что мы никогда его не рассматривали: <strong>OCR, или оптическое распознавание символов</strong>. Это процесс преобразования фотографий в простой текст с помощью ИИ для распознавания символов на фотографиях. Инструменты для этого существуют давно и были довольно приличными, но «довольно прилично» недостаточно для целей сохранения. Однако недавние многомодальные модели глубокого обучения сделали чрезвычайно быстрый прогресс, хотя и при высоких затратах. Мы ожидаем, что как точность, так и затраты значительно улучшатся в ближайшие годы, до такой степени, что это станет реалистичным для применения ко всей нашей библиотеке. Когда это произойдет, мы, вероятно, все равно сохраним оригинальные файлы, но, кроме того, у нас может быть гораздо меньшая версия нашей библиотеки, которую большинство людей захотят зеркалировать. Преимущество в том, что сам сырой текст сжимается еще лучше и его гораздо легче дедуплицировать, что дает нам еще больше экономии. В целом, не нереалистично ожидать как минимум 5-10-кратного уменьшения общего размера файлов, возможно, даже больше. Даже при консервативном 5-кратном уменьшении, мы бы смотрели на <strong>1 000–3 000 долларов через 10 лет, даже если наша библиотека утроится в размере</strong>. На момент написания <a %(diskprices)s>цены на диски</a> за ТБ составляют около $12 за новые диски, $8 за использованные диски и $4 за ленты. Если мы будем консервативны и посмотрим только на новые диски, это означает, что хранение петабайта стоит около $12,000. Если мы предположим, что наша библиотека утроится с 900 ТБ до 2.7 ПБ, это будет означать $32,400 для зеркалирования всей нашей библиотеки. Добавив стоимость электроэнергии, другого оборудования и так далее, округлим до $40,000. Или с лентами это будет около $15,000–$20,000. С одной стороны, <strong>$15,000–$40,000 за сумму всех человеческих знаний — это выгодная сделка</strong>. С другой стороны, это немного круто ожидать множество полных копий, особенно если мы также хотим, чтобы эти люди продолжали раздавать свои торренты на благо других. Это сегодня. Но прогресс идет вперед: Стоимость жестких дисков за ТБ была примерно сокращена на треть за последние 10 лет и, вероятно, продолжит снижаться с аналогичной скоростью. Ленты, похоже, следуют аналогичной траектории. Цены на SSD падают еще быстрее и могут обогнать цены на HDD к концу десятилетия. Если это сохранится, то через 10 лет мы можем рассчитывать на всего $5,000–$13,000 для зеркалирования всей нашей коллекции (1/3), или даже меньше, если мы будем расти медленнее. Хотя это все еще много денег, это будет доступно для многих людей. И это может быть даже лучше из-за следующего пункта… В Архиве Анны нас часто спрашивают, как мы можем утверждать, что сохраняем наши коллекции навсегда, если их общий объем уже приближается к 1 петабайту (1000 ТБ) и продолжает расти. В этой статье мы рассмотрим нашу философию и увидим, почему следующее десятилетие критически важно для нашей миссии по сохранению знаний и культуры человечества. Критическое окно Если эти прогнозы точны, нам <strong>просто нужно подождать пару лет</strong>, прежде чем вся наша коллекция будет широко зеркалирована. Таким образом, по словам Томаса Джефферсона, она будет «помещена вне досягаемости случайности». К сожалению, появление LLM и их жадного к данным обучения поставило многих правообладателей в оборонительную позицию. Даже больше, чем они уже были. Многие веб-сайты усложняют скрейпинг и архивирование, иски летают повсюду, и все это время физические библиотеки и архивы продолжают оставаться без внимания. Мы можем только ожидать, что эти тенденции будут продолжать ухудшаться, и многие работы будут утеряны задолго до того, как они войдут в общественное достояние. <strong>Мы на пороге революции в сохранении, но <q>утраченное не может быть восстановлено.</q></strong> У нас есть критическое окно в 5-10 лет, в течение которого все еще довольно дорого содержать теневую библиотеку и создавать множество зеркал по всему миру, и в течение которого доступ еще не полностью закрыт. Если мы сможем преодолеть это окно, то действительно сохраним знания и культуру человечества навсегда. Мы не должны позволить этому времени пропасть зря. Мы не должны позволить этому критическому окну закрыться для нас. Вперед. Критическое окно теневых библиотек Как мы можем утверждать, что сохраняем наши коллекции навсегда, если они уже приближаются к 1 ПБ? Коллекция Некоторая дополнительная информация о коллекции. <a %(duxiu)s>Duxiu</a> — это огромная база данных отсканированных книг, созданная <a %(chaoxing)s>SuperStar Digital Library Group</a>. Большинство из них — академические книги, отсканированные для того, чтобы сделать их доступными в цифровом виде для университетов и библиотек. Для нашей англоязычной аудитории <a %(library_princeton)s>Принстон</a> и <a %(guides_lib_uw)s>Вашингтонский университет</a> имеют хорошие обзоры. Также есть отличная статья, дающая больше информации: <a %(doi)s>«Оцифровка китайских книг: исследование поисковой системы SuperStar DuXiu Scholar»</a> (найдите ее в Архиве Анны). Книги из Duxiu давно пиратски распространяются в китайском интернете. Обычно они продаются перепродавцами за менее чем доллар. Их обычно распространяют с помощью китайского аналога Google Drive, который часто взламывают для увеличения объема хранилища. Некоторые технические детали можно найти <a %(github_duty_machine)s>здесь</a> и <a %(github_821_github_io)s>здесь</a>. Хотя книги были полупублично распространены, получить их в большом количестве довольно сложно. Это было в верхней части нашего списка дел, и мы выделили на это несколько месяцев полной занятости. Однако недавно к нам обратился невероятный, удивительный и талантливый волонтер, сообщивший, что он уже проделал всю эту работу — за большие деньги. Он поделился с нами полной коллекцией, не ожидая ничего взамен, кроме гарантии долгосрочного сохранения. Поистине замечательно. Они согласились попросить помощи таким образом, чтобы коллекция была распознана с помощью OCR. Коллекция состоит из 7 543 702 файлов. Это больше, чем Library Genesis в разделе нон-фикшн (около 5,3 миллиона). Общий размер файлов составляет около 359 ТБ (326 ТиБ) в текущем виде. Мы открыты для других предложений и идей. Просто свяжитесь с нами. Ознакомьтесь с Архивом Анны для получения дополнительной информации о наших коллекциях, усилиях по сохранению и о том, как вы можете помочь. Спасибо! Пример страниц Чтобы доказать нам, что у вас есть хорошая система, вот несколько примерных страниц для начала, из книги о сверхпроводниках. Ваша система должна правильно обрабатывать математику, таблицы, диаграммы, сноски и так далее. Отправьте обработанные страницы на наш электронный адрес. Если они будут выглядеть хорошо, мы отправим вам больше в частном порядке, и мы ожидаем, что вы сможете быстро обработать их с помощью вашей системы. Как только мы будем удовлетворены, мы сможем заключить сделку. - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Китайская версия 中文版</a>, <a %(news_ycombinator)s>Обсудить на Hacker News</a> Это короткий блог-пост. Мы ищем компанию или учреждение, которое поможет нам с OCR и извлечением текста для огромной коллекции, которую мы приобрели, в обмен на эксклюзивный ранний доступ. После периода эмбарго мы, конечно, выпустим всю коллекцию. Высококачественный академический текст чрезвычайно полезен для обучения LLM. Хотя наша коллекция китайская, это должно быть полезно даже для обучения английских LLM: модели, похоже, кодируют концепции и знания независимо от исходного языка. Для этого текст необходимо извлечь из сканов. Что получает Архив Анны? Полнотекстовый поиск по книгам для своих пользователей. Поскольку наши цели совпадают с целями разработчиков LLM, мы ищем сотрудника. Мы готовы предоставить вам <strong>эксклюзивный ранний доступ к этой коллекции в течение 1 года</strong>, если вы сможете выполнить надлежащий OCR и извлечение текста. Если вы готовы поделиться с нами всем кодом вашего конвейера, мы готовы продлить эмбарго на коллекцию. Эксклюзивный доступ для компаний LLM к крупнейшей в мире коллекции китайских научно-популярных книг <em><strong>Кратко:</strong> Архив Анны приобрел уникальную коллекцию из 7,5 миллионов / 350 ТБ китайских научно-популярных книг — больше, чем Library Genesis. Мы готовы предоставить компании LLM эксклюзивный доступ в обмен на высококачественный OCR и извлечение текста.</em> Архитектура системы Итак, допустим, вы нашли несколько компаний, которые готовы разместить ваш сайт, не закрывая его — назовем их «провайдерами, любящими свободу» 😄. Вы быстро обнаружите, что размещение всего у них довольно дорого, поэтому вы можете захотеть найти «дешевых провайдеров» и разместить там фактический хостинг, проксируя через провайдеров, любящих свободу. Если вы сделаете это правильно, дешевые провайдеры никогда не узнают, что вы размещаете, и никогда не получат никаких жалоб. Существует риск, что все эти провайдеры могут вас отключить, поэтому вам также нужна избыточность. Нам это нужно на всех уровнях нашего стека. Одна из компаний, которая занимает интересную позицию и склонна к свободе, — это Cloudflare. Они <a %(blog_cloudflare)s>утверждают</a>, что они не являются хостинг-провайдером, а утилитой, как интернет-провайдер. Поэтому они не подлежат требованиям DMCA или другим запросам на удаление и перенаправляют любые запросы вашему фактическому хостинг-провайдеру. Они даже дошли до суда, чтобы защитить эту структуру. Поэтому мы можем использовать их как еще один уровень кэширования и защиты. Cloudflare не принимает анонимные платежи, поэтому мы можем использовать только их бесплатный план. Это означает, что мы не можем использовать их функции балансировки нагрузки или резервирования. Поэтому мы <a %(annas_archive_l255)s>реализовали это самостоятельно</a> на уровне домена. При загрузке страницы браузер проверяет, доступен ли текущий домен, и если нет, переписывает все URL на другой домен. Поскольку Cloudflare кэширует многие страницы, это означает, что пользователь может попасть на наш основной домен, даже если прокси-сервер не работает, а затем при следующем клике перейти на другой домен. У нас также есть обычные операционные задачи, такие как мониторинг состояния серверов, ведение журнала ошибок бэкенда и фронтенда и так далее. Наша архитектура отказоустойчивости позволяет обеспечить большую надежность в этом отношении, например, запуская совершенно другой набор серверов на одном из доменов. Мы даже можем запускать более старые версии кода и наборов данных на этом отдельном домене, на случай, если критическая ошибка в основной версии останется незамеченной. Мы также можем защититься от того, что Cloudflare обернется против нас, удалив его с одного из доменов, например, с этого отдельного домена. Возможны различные комбинации этих идей. Заключение Это был интересный опыт — научиться настраивать надежный и устойчивый поисковый движок теневой библиотеки. Есть еще много деталей, которые можно поделиться в будущих постах, так что дайте знать, о чем вы хотели бы узнать больше! Как всегда, мы ищем пожертвования для поддержки этой работы, так что обязательно посетите страницу "Пожертвовать" на Архиве Анны. Мы также ищем другие виды поддержки, такие как гранты, долгосрочные спонсоры, провайдеры высокорисковых платежей, возможно, даже (со вкусом!) реклама. И если вы хотите внести свой вклад своим временем и навыками, мы всегда ищем разработчиков, переводчиков и так далее. Спасибо за ваш интерес и поддержку. Токены инноваций Начнем с нашего технологического стека. Он намеренно скучный. Мы используем Flask, MariaDB и ElasticSearch. Это буквально все. Поиск в значительной степени решенная проблема, и мы не собираемся изобретать его заново. Кроме того, мы должны потратить наши <a %(mcfunley)s>токены инноваций</a> на что-то другое: чтобы нас не закрыли власти. Так насколько легален или нелегален Архив Анны? Это в основном зависит от юридической юрисдикции. Большинство стран верят в какую-то форму авторского права, что означает, что людям или компаниям предоставляется исключительная монополия на определенные виды работ на определенный период времени. В качестве отступления, в Архиве Анны мы считаем, что, хотя есть некоторые преимущества, в целом авторское право является чистым минусом для общества — но это история для другого времени. Эта исключительная монополия на определенные работы означает, что незаконно для кого-либо за пределами этой монополии напрямую распространять эти работы — включая нас. Но Архив Анны — это поисковая система, которая не распространяет эти работы напрямую (по крайней мере, не на нашем сайте в открытом интернете), так что мы должны быть в порядке, верно? Не совсем. Во многих юрисдикциях незаконно не только распространять защищенные авторским правом работы, но и ссылаться на места, где это делается. Классический пример этого — закон DMCA в Соединенных Штатах. Это самый строгий конец спектра. На другом конце спектра теоретически могут существовать страны, в которых вообще нет законов об авторском праве, но таких на самом деле не существует. Практически в каждой стране есть какие-то законы об авторском праве. Применение — это другая история. Есть много стран с правительствами, которые не заботятся о применении законов об авторском праве. Также есть страны между двумя крайностями, которые запрещают распространение защищенных авторским правом работ, но не запрещают ссылки на такие работы. Еще одно соображение на уровне компании. Если компания работает в юрисдикции, которая не заботится об авторских правах, но сама компания не хочет рисковать, то они могут закрыть ваш сайт, как только кто-то пожалуется на него. Наконец, важным соображением являются платежи. Поскольку нам нужно оставаться анонимными, мы не можем использовать традиционные методы оплаты. Это оставляет нам криптовалюты, и только небольшое количество компаний поддерживает их (существуют виртуальные дебетовые карты, оплачиваемые криптовалютой, но они часто не принимаются). - Анна и команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Я управляю <a %(wikipedia_annas_archive)s>Архивом Анны</a>, крупнейшей в мире некоммерческой поисковой системой с открытым исходным кодом для <a %(wikipedia_shadow_library)s>теневых библиотек</a>, таких как Sci-Hub, Library Genesis и Z-Library. Наша цель — сделать знания и культуру легко доступными и в конечном итоге создать сообщество людей, которые вместе архивируют и сохраняют <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>все книги в мире</a>. В этой статье я покажу, как мы управляем этим сайтом, и уникальные проблемы, связанные с управлением сайтом с сомнительным юридическим статусом, поскольку нет "AWS для теневых благотворительных организаций". <em>Также ознакомьтесь с сестринской статьей <a %(blog_how_to_become_a_pirate_archivist)s>Как стать пиратским архивариусом</a>.</em> Как управлять теневой библиотекой: операции в Архиве Анны Нет <q>AWS для теневых благотворительных организаций,</q> так как же мы управляем Архивом Анны? Инструменты Сервер приложений: Flask, MariaDB, ElasticSearch, Docker. Разработка: Gitlab, Weblate, Zulip. Управление сервером: Ansible, Checkmk, UFW. Статический хостинг в сети Tor: Tor, Nginx. Прокси-сервер: Varnish. Давайте посмотрим, какие инструменты мы используем для достижения всего этого. Это постоянно развивается, так как мы сталкиваемся с новыми проблемами и находим новые решения. Есть некоторые решения, по которым мы колебались. Одно из них — это связь между серверами: раньше мы использовали Wireguard для этого, но обнаружили, что он иногда перестает передавать данные или передает их только в одном направлении. Это происходило с несколькими различными настройками Wireguard, которые мы пробовали, такими как <a %(github_costela_wesher)s>wesher</a> и <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Мы также пробовали туннелирование портов через SSH, используя autossh и sshuttle, но столкнулись с <a %(github_sshuttle)s>проблемами там</a> (хотя мне до сих пор не ясно, страдает ли autossh от проблем TCP-over-TCP или нет — это просто кажется мне неуклюжим решением, но, возможно, оно на самом деле нормально?). Вместо этого мы вернулись к прямым соединениям между серверами, скрывая, что сервер работает на дешевых провайдерах, используя IP-фильтрацию с UFW. Это имеет недостаток, что Docker плохо работает с UFW, если не использовать <code>network_mode: "host"</code>. Все это немного более подвержено ошибкам, потому что вы можете подвергнуть свой сервер интернету с помощью небольшой ошибки в конфигурации. Возможно, нам стоит вернуться к autossh — обратная связь здесь будет очень полезна. Мы также колебались между Varnish и Nginx. В настоящее время нам нравится Varnish, но у него есть свои причуды и шероховатости. То же самое относится и к Checkmk: мы его не любим, но он работает на данный момент. Weblate был неплох, но не впечатляющий — я иногда боюсь, что он потеряет мои данные, когда я пытаюсь синхронизировать его с нашим git-репозиторием. Flask в целом был хорош, но у него есть странные причуды, которые стоили много времени на отладку, такие как настройка пользовательских доменов или проблемы с интеграцией SqlAlchemy. Пока что другие инструменты были отличными: у нас нет серьезных жалоб на MariaDB, ElasticSearch, Gitlab, Zulip, Docker и Tor. У всех этих инструментов были некоторые проблемы, но ничего слишком серьезного или отнимающего много времени. Сообщество Первый вызов может оказаться неожиданным. Это не техническая проблема и не юридическая. Это психологическая проблема: работа в тени может быть невероятно одинокой. В зависимости от того, что вы планируете делать, и вашей модели угроз, вам, возможно, придется быть очень осторожным. На одном конце спектра находятся такие люди, как Александра Элбакян*, основательница Sci-Hub, которая открыто говорит о своей деятельности. Но она находится под высоким риском ареста, если посетит западную страну, и может столкнуться с десятилетиями тюремного заключения. Готовы ли вы пойти на такой риск? Мы находимся на другом конце спектра; мы очень осторожны, чтобы не оставить следов, и имеем сильную операционную безопасность. * Как упомянуто на HN пользователем "ynno", Александра изначально не хотела быть известной: "Ее серверы были настроены на выдачу подробных сообщений об ошибках PHP, включая полный путь к файлу с ошибкой, который находился в каталоге /home/<USER>" Поэтому используйте случайные имена пользователей на компьютерах, которые вы используете для этого, на случай, если вы что-то неправильно настроите. Однако такая секретность имеет психологическую цену. Большинство людей любят, когда их работа признается, но вы не можете получить за это никакого признания в реальной жизни. Даже простые вещи могут быть сложными, например, когда друзья спрашивают, чем вы занимались (в какой-то момент "возиться с моим NAS / домашней лабораторией" надоедает). Вот почему так важно найти какое-то сообщество. Вы можете пожертвовать частью операционной безопасности, доверившись очень близким друзьям, которым вы знаете, что можете глубоко доверять. Даже тогда будьте осторожны, чтобы не оставлять ничего в письменной форме, на случай, если им придется передать свои электронные письма властям или если их устройства будут скомпрометированы каким-либо другим образом. Еще лучше найти единомышленников-пиратов. Если ваши близкие друзья заинтересованы присоединиться к вам, отлично! В противном случае вы можете найти других в интернете. К сожалению, это все еще нишевое сообщество. Пока мы нашли лишь несколько других, кто активен в этой области. Хорошими отправными точками кажутся форумы Library Genesis и r/DataHoarder. В команде Archive Team также есть единомышленники, хотя они действуют в рамках закона (даже если в некоторых серых зонах закона). Традиционные сцены "warez" и пиратства также имеют людей, которые думают схожим образом. Мы открыты для идей о том, как развивать сообщество и исследовать идеи. Не стесняйтесь писать нам в Twitter или Reddit. Возможно, мы могли бы организовать какой-то форум или чат-группу. Одна из проблем заключается в том, что это может легко подвергнуться цензуре при использовании общих платформ, поэтому нам придется размещать это самостоятельно. Также существует компромисс между тем, чтобы эти обсуждения были полностью публичными (больше потенциального вовлечения) и тем, чтобы сделать их приватными (не позволяя потенциальным "целям" узнать, что мы собираемся их сканировать). Нам нужно будет подумать об этом. Дайте нам знать, если вы заинтересованы в этом! Заключение Надеемся, это будет полезно для начинающих пиратских архивистов. Мы рады приветствовать вас в этом мире, так что не стесняйтесь обращаться. Давайте сохраним как можно больше мировых знаний и культуры и распространим их повсеместно. Проекты 4. Выбор данных Часто вы можете использовать метаданные, чтобы определить разумное подмножество данных для загрузки. Даже если вы в конечном итоге хотите загрузить все данные, может быть полезно сначала приоритизировать самые важные элементы, на случай если вас обнаружат и улучшат защиту, или потому что вам нужно будет купить больше дисков, или просто потому что что-то еще произойдет в вашей жизни, прежде чем вы сможете загрузить все. Например, в коллекции может быть несколько изданий одного и того же ресурса (например, книги или фильма), где одно издание отмечено как лучшее по качеству. Сохранение этих изданий в первую очередь имеет смысл. В конечном итоге вы можете захотеть сохранить все издания, так как в некоторых случаях метаданные могут быть неправильно помечены, или могут быть неизвестные компромиссы между изданиями (например, "лучшее издание" может быть лучшим во многих отношениях, но хуже в других, например, фильм с более высоким разрешением, но без субтитров). Вы также можете искать в своей базе данных метаданных, чтобы найти интересные вещи. Какой самый большой файл, который размещен, и почему он такой большой? Какой самый маленький файл? Есть ли интересные или неожиданные закономерности в определенных категориях, языках и так далее? Есть ли дубликаты или очень похожие названия? Есть ли закономерности в том, когда данные были добавлены, например, один день, когда было добавлено много файлов одновременно? Часто можно многому научиться, рассматривая набор данных с разных сторон. В нашем случае мы устранили дублирование книг Z-Library по md5-хэшам в Library Genesis, тем самым сэкономив много времени на загрузку и место на диске. Это довольно уникальная ситуация. В большинстве случаев нет всеобъемлющих баз данных, в которых указано, какие файлы уже должным образом сохранены другими пиратами. Это само по себе является огромной возможностью для кого-то. Было бы здорово иметь регулярно обновляемый обзор таких вещей, как музыка и фильмы, которые уже широко раздаются на торрент-сайтах, и поэтому имеют более низкий приоритет для включения в пиратские зеркала. 6. Распространение У вас есть данные, тем самым вы обладаете первым в мире пиратским зеркалом вашей цели (скорее всего). Во многих отношениях самая сложная часть позади, но самая рискованная часть все еще впереди. В конце концов, до сих пор вы были скрытны; летали под радаром. Все, что вам нужно было сделать, это использовать хороший VPN на протяжении всего времени, не заполнять свои личные данные в каких-либо формах (очевидно), и, возможно, использовать специальную сессию браузера (или даже другой компьютер). Теперь вам нужно распространить данные. В нашем случае мы сначала хотели вернуть книги в Library Genesis, но быстро обнаружили трудности в этом (сортировка художественной и научной литературы). Поэтому мы решили распространять с помощью торрентов в стиле Library Genesis. Если у вас есть возможность внести вклад в существующий проект, это может сэкономить вам много времени. Однако в настоящее время не так много хорошо организованных пиратских зеркал. Допустим, вы решили самостоятельно распространять торренты. Старайтесь, чтобы эти файлы были небольшими, чтобы их было легко зеркалировать на других сайтах. Вам придется раздавать торренты самостоятельно, оставаясь при этом анонимным. Вы можете использовать VPN (с переадресацией портов или без нее) или оплатить Seedbox с помощью перемешанных биткоинов. Если вы не знаете, что означают некоторые из этих терминов, вам придется много читать, так как важно понимать риски. Вы можете разместить сами торрент-файлы на существующих торрент-сайтах. В нашем случае мы решили фактически разместить сайт, так как также хотели ясно распространить нашу философию. Вы можете сделать это самостоятельно аналогичным образом (мы используем Njalla для наших доменов и хостинга, оплаченных перемешанными биткоинами), но также не стесняйтесь связаться с нами, чтобы мы разместили ваши торренты. Мы стремимся со временем создать всеобъемлющий индекс пиратских зеркал, если эта идея приживется. Что касается выбора VPN, об этом уже много написано, поэтому мы просто повторим общий совет выбирать по репутации. Реальные политики без логов, проверенные в суде и имеющие долгую историю защиты конфиденциальности, являются наименее рискованным вариантом, на наш взгляд. Обратите внимание, что даже если вы делаете все правильно, вы никогда не сможете достичь нулевого риска. Например, при раздаче ваших торрентов, высокомотивированный государственный актор, вероятно, сможет просмотреть входящие и исходящие потоки данных для VPN-серверов и определить, кто вы. Или вы можете просто как-то ошибиться. Мы, вероятно, уже ошибались и сделаем это снова. К счастью, государствам не так уж важно <em>это</em> пиратство. Одно из решений, которое нужно принять для каждого проекта, — публиковать его под тем же именем, что и раньше, или нет. Если вы продолжаете использовать то же имя, то ошибки в операционной безопасности из предыдущих проектов могут вернуться и навредить вам. Но публикация под разными именами означает, что вы не создаете долгосрочную репутацию. Мы решили с самого начала обеспечить сильную операционную безопасность, чтобы продолжать использовать то же имя, но не будем колебаться публиковаться под другим именем, если ошибемся или если обстоятельства потребуют этого. Распространение информации может быть сложной задачей. Как мы уже говорили, это все еще нишевое сообщество. Изначально мы публиковались на Reddit, но действительно получили внимание на Hacker News. На данный момент мы рекомендуем разместить информацию в нескольких местах и посмотреть, что произойдет. И снова, свяжитесь с нами. Мы будем рады распространить информацию о новых усилиях по пиратскому архивированию. 1. Выбор домена / философия Нет недостатка в знаниях и культурном наследии, которые нужно сохранить, что может быть ошеломляющим. Вот почему часто полезно сделать паузу и подумать о том, какой вклад вы можете внести. У каждого есть свой способ думать об этом, но вот некоторые вопросы, которые вы могли бы задать себе: В нашем случае нас особенно заботило долгосрочное сохранение науки. Мы знали о Library Genesis и о том, как она многократно полностью зеркалировалась с помощью торрентов. Нам понравилась эта идея. Однажды один из нас попытался найти научные учебники на Library Genesis, но не смог их найти, что поставило под сомнение, насколько она действительно полная. Затем мы искали эти учебники в интернете и нашли их в других местах, что послужило началом нашего проекта. Даже до того, как мы узнали о Z-Library, у нас была идея не пытаться собирать все эти книги вручную, а сосредоточиться на зеркалировании существующих коллекций и возвращении их в Library Genesis. Какие навыки у вас есть, которые вы можете использовать в своих интересах? Например, если вы эксперт по онлайн-безопасности, вы можете найти способы обхода блокировок IP для защищенных целей. Если вы отлично организуете сообщества, возможно, вы сможете собрать людей вокруг какой-то цели. Полезно знать немного программирования, хотя бы для поддержания хорошей операционной безопасности в этом процессе. На какую область стоит обратить внимание, чтобы получить максимальную отдачу? Если вы собираетесь потратить X часов на пиратское архивирование, то как вы можете получить наибольшую "отдачу за свои деньги"? Какие уникальные способы вы обдумываете? У вас могут быть интересные идеи или подходы, которые другие могли упустить. Сколько у вас времени на это? Наш совет — начинать с малого и переходить к более крупным проектам по мере освоения, но это может стать всепоглощающим. Почему вас это интересует? Чем вы увлечены? Если мы сможем собрать группу людей, которые все архивируют то, что им особенно важно, это покроет многое! Вы будете знать гораздо больше, чем средний человек, о своем увлечении, например, какие данные важно сохранить, какие коллекции и онлайн-сообщества лучшие и так далее. 3. Сбор metadata Дата добавления/изменения: чтобы вы могли вернуться позже и скачать файлы, которые не скачали ранее (хотя часто можно также использовать ID или хэш для этого). Хэш (md5, sha1): чтобы подтвердить, что вы правильно загрузили файл. ID: может быть внутренним ID, но такие ID, как ISBN или DOI, тоже полезны. Имя файла / местоположение Описание, категория, теги, авторы, язык и т.д. Размер: чтобы рассчитать, сколько места на диске вам нужно. Давайте немного углубимся в технические детали. Для фактического сбора metadata с веб-сайтов мы сохранили все довольно простым. Мы используем скрипты на Python, иногда curl, и базу данных MySQL для хранения результатов. Мы не использовали никакое сложное программное обеспечение для сбора данных, которое может отображать сложные веб-сайты, так как до сих пор нам нужно было собирать данные только с одного или двух типов страниц, просто перечисляя идентификаторы и анализируя HTML. Если страницы не могут быть легко перечислены, то вам может понадобиться правильный краулер, который попытается найти все страницы. Прежде чем начать сбор данных с целого веб-сайта, попробуйте сделать это вручную. Пройдитесь по нескольким десяткам страниц самостоятельно, чтобы понять, как это работает. Иногда вы уже таким образом столкнетесь с блокировками IP или другим интересным поведением. То же самое касается сбора данных: прежде чем углубляться в эту цель, убедитесь, что вы действительно можете эффективно загружать ее данные. Чтобы обойти ограничения, есть несколько вещей, которые вы можете попробовать. Есть ли другие IP-адреса или серверы, которые хранят те же данные, но не имеют таких же ограничений? Есть ли API-эндпоинты, которые не имеют ограничений, в то время как другие имеют? С какой скоростью загрузки ваш IP блокируется и на сколько времени? Или вас не блокируют, а замедляют? Что если вы создадите учетную запись пользователя, как тогда изменятся вещи? Можете ли вы использовать HTTP/2, чтобы держать соединения открытыми, и увеличивает ли это скорость, с которой вы можете запрашивать страницы? Есть ли страницы, которые перечисляют несколько файлов одновременно, и достаточно ли информации, указанной там? Вещи, которые вы, вероятно, захотите сохранить, включают: Обычно мы делаем это в два этапа. Сначала мы загружаем сырые HTML-файлы, обычно напрямую в MySQL (чтобы избежать множества мелких файлов, о чем мы поговорим ниже). Затем, на отдельном этапе, мы проходим через эти HTML-файлы и разбираем их в реальные таблицы MySQL. Таким образом, вам не придется загружать все заново, если вы обнаружите ошибку в вашем коде разбора, так как вы можете просто перепроцессировать HTML-файлы с новым кодом. Также часто проще параллелизировать этап обработки, что экономит время (и вы можете писать код обработки, пока идет скрапинг, вместо того чтобы писать оба этапа сразу). Наконец, обратите внимание, что для некоторых целей скрапинг метаданных — это все, что есть. Существуют огромные коллекции метаданных, которые не сохранены должным образом. Название Выбор домена / философия: На чем вы хотите сосредоточиться и почему? Какие у вас уникальные увлечения, навыки и обстоятельства, которые вы можете использовать в своих интересах? Выбор цели: Какую конкретную коллекцию вы будете зеркалировать? Сбор metadata: Каталогизация информации о файлах без фактической загрузки самих (часто гораздо более крупных) файлов. Выбор данных: На основе metadata, сужение круга данных, которые наиболее актуальны для архивации прямо сейчас. Это может быть все, но часто есть разумный способ сэкономить место и пропускную способность. Сбор данных: Фактическое получение данных. Распределение: Упаковка в торренты, объявление где-то, привлечение людей к распространению. 5. Скрапинг данных Теперь вы готовы к массовой загрузке данных. Как упоминалось ранее, на этом этапе вы уже должны были вручную загрузить кучу файлов, чтобы лучше понять поведение и ограничения цели. Однако вас все равно ждут сюрпризы, когда вы начнете загружать много файлов одновременно. Наш совет здесь — держать все просто. Начните с загрузки кучи файлов. Вы можете использовать Python, а затем расширить до нескольких потоков. Но иногда даже проще генерировать Bash-файлы прямо из базы данных, а затем запускать несколько из них в нескольких окнах терминала для масштабирования. Быстрый технический трюк, который стоит упомянуть здесь, — использование OUTFILE в MySQL, который вы можете записать где угодно, если отключите "secure_file_priv" в mysqld.cnf (и обязательно отключите/переопределите AppArmor, если вы находитесь на Linux). Мы храним данные на простых жестких дисках. Начните с того, что у вас есть, и расширяйтесь медленно. Может быть ошеломляюще думать о хранении сотен терабайт данных. Если это ситуация, с которой вы столкнулись, просто выложите хороший подмножество сначала, и в вашем объявлении попросите помощи в хранении остального. Если вы хотите получить больше жестких дисков самостоятельно, то r/DataHoarder имеет хорошие ресурсы по получению выгодных предложений. Старайтесь не слишком беспокоиться о сложных файловых системах. Легко попасть в кроличью нору настройки таких вещей, как ZFS. Однако одна техническая деталь, о которой стоит знать, заключается в том, что многие файловые системы плохо справляются с большим количеством файлов. Мы обнаружили, что простое решение — создать несколько каталогов, например, для разных диапазонов ID или префиксов хэшей. После загрузки данных обязательно проверьте целостность файлов с помощью хэшей в метаданных, если они доступны. 2. Выбор цели Доступная: не использует множество слоев защиты, чтобы предотвратить сбор их metadata и данных. Особая информация: у вас есть какая-то особая информация об этой цели, например, у вас есть особый доступ к этой коллекции, или вы поняли, как обойти их защиту. Это не обязательно (наш предстоящий проект не делает ничего особенного), но это, безусловно, помогает! Большая Итак, у нас есть область, на которую мы смотрим, теперь какую конкретную коллекцию мы зеркалируем? Есть несколько вещей, которые делают цель хорошей: Когда мы нашли наши научные учебники на сайтах, отличных от Library Genesis, мы попытались выяснить, как они попали в интернет. Затем мы нашли Z-Library и поняли, что, хотя большинство книг не появляются там в первую очередь, они в конечном итоге оказываются там. Мы узнали о его связи с Library Genesis и структуре (финансовых) стимулов и превосходном пользовательском интерфейсе, которые сделали его гораздо более полной коллекцией. Затем мы провели предварительный сбор metadata и данных и поняли, что можем обойти их ограничения на загрузку по IP, используя особый доступ одного из наших участников к множеству прокси-серверов. Когда вы исследуете разные цели, уже важно скрывать свои следы, используя VPN и временные адреса электронной почты, о чем мы поговорим позже. Уникальная: не охваченная другими проектами. Когда мы выполняем проект, он проходит несколько этапов: Эти этапы не полностью независимы, и часто выводы из более позднего этапа возвращают вас к более раннему. Например, во время сбора metadata вы можете понять, что выбранная вами цель имеет защитные механизмы, которые превышают ваш уровень навыков (например, блокировки IP), поэтому вы возвращаетесь и находите другую цель. - Анна и команда (<a %(reddit)s>Reddit</a>) Целые книги можно написать о <em>почему</em> цифрового сохранения в целом и пиратского архивирования в частности, но давайте дадим краткое введение для тех, кто не слишком знаком. Мир производит больше знаний и культуры, чем когда-либо прежде, но также больше из этого теряется, чем когда-либо прежде. Человечество в значительной степени доверяет корпорациям, таким как академические издатели, стриминговые сервисы и компании социальных сетей, это наследие, и они часто не проявляют себя как отличные хранители. Посмотрите документальный фильм Digital Amnesia или любой доклад Джейсона Скотта. Есть некоторые учреждения, которые хорошо справляются с архивированием всего, что могут, но они связаны законом. Как пираты, мы находимся в уникальном положении архивировать коллекции, к которым они не могут прикоснуться из-за соблюдения авторских прав или других ограничений. Мы также можем зеркалировать коллекции много раз по всему миру, тем самым увеличивая шансы на правильное сохранение. Пока мы не будем вдаваться в обсуждения о плюсах и минусах интеллектуальной собственности, морали нарушения закона, размышлениях о цензуре или вопросе доступа к знаниям и культуре. С учетом всего этого, давайте погрузимся в <em>как</em>. Мы поделимся, как наша команда стала пиратскими архивариусами, и уроками, которые мы извлекли на этом пути. Есть много вызовов, когда вы начинаете это путешествие, и, надеемся, мы сможем помочь вам с некоторыми из них. Как стать пиратским архивариусом Первый вызов может быть неожиданным. Это не техническая проблема или юридическая проблема. Это психологическая проблема. Прежде чем мы начнем, два обновления о Pirate Library Mirror (ИЗМЕНЕНИЕ: перемещено в <a %(wikipedia_annas_archive)s>Архив Анны</a>): Мы получили несколько чрезвычайно щедрых пожертвований. Первое было $10k от анонимного лица, которое также поддерживало "bookwarrior", оригинального основателя Library Genesis. Особая благодарность bookwarrior за содействие этому пожертвованию. Второе было еще $10k от анонимного донора, который связался с нами после нашего последнего релиза и был вдохновлен помочь. У нас также было несколько меньших пожертвований. Огромное спасибо за всю вашу щедрую поддержку. У нас есть несколько захватывающих новых проектов в разработке, которые это поддержит, так что оставайтесь с нами. У нас были некоторые технические трудности с размером нашего второго релиза, но наши торренты теперь доступны и раздаются. Мы также получили щедрое предложение от анонимного лица раздавать нашу коллекцию на их очень высокоскоростных серверах, поэтому мы делаем специальную загрузку на их машины, после чего все остальные, кто загружает коллекцию, должны заметить значительное улучшение скорости. Посты в блоге Привет, я Анна. Я создала <a %(wikipedia_annas_archive)s>Архив Анны</a>, крупнейшую в мире теневую библиотеку. Это мой личный блог, в котором я и моя команда пишем о пиратстве, цифровом сохранении и многом другом. Свяжитесь со мной на <a %(reddit)s>Reddit</a>. Обратите внимание, что этот сайт — всего лишь блог. Мы размещаем здесь только наши собственные слова. Здесь не размещаются и не ссылаются торренты или другие файлы, защищенные авторским правом. <strong>Библиотека</strong> — Как и большинство библиотек, мы в первую очередь сосредоточены на письменных материалах, таких как книги. В будущем мы можем расшириться на другие виды медиа. <strong>Зеркало</strong> — Мы строго являемся зеркалом существующих библиотек. Мы сосредоточены на сохранении, а не на обеспечении легкого поиска и загрузки книг (доступ) или создании большой сообщества людей, которые вносят новые книги (источник). <strong>Пират</strong> — Мы намеренно нарушаем закон об авторском праве в большинстве стран. Это позволяет нам делать то, что легальные организации не могут: обеспечивать широкое распространение книг. <em>Мы не ссылаемся на файлы с этого блога. Пожалуйста, найдите их самостоятельно.</em> - Анна и команда (<a %(reddit)s>Reddit</a>) Этот проект (ИЗМЕНЕНИЕ: перемещен в <a %(wikipedia_annas_archive)s>Архив Анны</a>) направлен на содействие сохранению и освобождению человеческих знаний. Мы вносим наш небольшой и скромный вклад, следуя по стопам великих, которые были до нас. Фокус этого проекта иллюстрируется его названием: Первая библиотека, которую мы зеркалировали, — это Z-Library. Это популярная (и незаконная) библиотека. Они взяли коллекцию Library Genesis и сделали ее легко доступной для поиска. Кроме того, они стали очень эффективными в привлечении новых книг, поощряя пользователей, вносящих вклад, различными привилегиями. В настоящее время они не возвращают эти новые книги обратно в Library Genesis. И в отличие от Library Genesis, они не делают свою коллекцию легко зеркалируемой, что препятствует широкому сохранению. Это важно для их бизнес-модели, так как они взимают плату за доступ к своей коллекции в большом объеме (более 10 книг в день). Мы не делаем моральных суждений о взимании платы за массовый доступ к незаконной коллекции книг. Несомненно, Z-Library добилась успеха в расширении доступа к знаниям и привлечении большего количества книг. Мы просто здесь, чтобы сделать свою часть: обеспечить долгосрочное сохранение этой частной коллекции. Мы хотели бы пригласить вас помочь сохранить и освободить человеческие знания, загружая и раздавая наши торренты. См. страницу проекта для получения дополнительной информации о том, как организованы данные. Мы также очень хотели бы пригласить вас внести свои идеи о том, какие коллекции зеркалировать дальше и как это сделать. Вместе мы можем достичь многого. Это лишь небольшой вклад среди бесчисленных других. Спасибо за все, что вы делаете. Представляем Зеркало Пиратской Библиотеки: Сохранение 7 ТБ книг (которые не находятся в Libgen) 10% of письменного наследия человечества сохранено навсегда <strong>Google.</strong> В конце концов, они провели это исследование для Google Books. Однако их metadata недоступны в массовом порядке и довольно трудно извлекаются. <strong>Различные индивидуальные библиотечные системы и архивы.</strong> Существуют библиотеки и архивы, которые не были индексированы и агрегированы ни одной из вышеперечисленных, часто потому, что они недофинансированы или по другим причинам не хотят делиться своими данными с Open Library, OCLC, Google и так далее. Многие из них имеют цифровые записи, доступные через интернет, и они часто не очень хорошо защищены, поэтому, если вы хотите помочь и повеселиться, изучая странные библиотечные системы, это отличные отправные точки. <strong>ISBNdb.</strong> Это тема этого блога. ISBNdb извлекает данные о книгах с различных веб-сайтов, в частности данные о ценах, которые они затем продают продавцам книг, чтобы они могли устанавливать цены на свои книги в соответствии с остальным рынком. Поскольку ISBN в настоящее время довольно универсальны, они фактически создали «веб-страницу для каждой книги». <strong>Open Library.</strong> Как уже упоминалось, это их основная миссия. Они собрали огромное количество библиотечных данных от сотрудничающих библиотек и национальных архивов и продолжают это делать. У них также есть волонтеры-библиотекари и техническая команда, которые пытаются удалять дубликаты записей и помечать их всевозможными metadata. Лучшее из всего, их набор данных полностью открыт. Вы можете просто <a %(openlibrary)s>скачать его</a>. <strong>WorldCat.</strong> Это веб-сайт, управляемый некоммерческой организацией OCLC, которая продает системы управления библиотеками. Они агрегируют metadata книг из множества библиотек и делают их доступными через веб-сайт WorldCat. Однако они также зарабатывают деньги, продавая эти данные, поэтому они недоступны для массовой загрузки. У них есть некоторые более ограниченные массовые наборы данных, доступные для загрузки, в сотрудничестве с конкретными библиотеками. 1. Для некоторого разумного определения «навсегда». ;) 2. Конечно, письменное наследие человечества — это гораздо больше, чем книги, особенно в наши дни. В рамках этого поста и наших недавних релизов мы сосредоточены на книгах, но наши интересы простираются дальше. 3. О Аароне Шварце можно сказать гораздо больше, но мы просто хотели кратко упомянуть его, так как он играет ключевую роль в этой истории. Со временем больше людей могут впервые столкнуться с его именем и затем самостоятельно углубиться в эту тему. <strong>Физические копии.</strong> Очевидно, это не очень полезно, так как они просто дубликаты одного и того же материала. Было бы здорово, если бы мы могли сохранить все аннотации, которые люди делают в книгах, как знаменитые «пометки на полях» Ферма. Но, увы, это останется мечтой архивариуса. <strong>«Издания».</strong> Здесь вы учитываете каждую уникальную версию книги. Если что-то в ней отличается, например, другая обложка или другое предисловие, это считается другим изданием. <strong>Файлы.</strong> При работе с теневыми библиотеками, такими как Library Genesis, Sci-Hub или Z-Library, есть дополнительное соображение. Может быть несколько сканов одного и того же издания. И люди могут создавать лучшие версии существующих файлов, сканируя текст с помощью OCR или исправляя страницы, которые были отсканированы под углом. Мы хотим учитывать эти файлы как одно издание, что потребует хорошего metadata или удаления дубликатов с использованием мер сходства документов. <strong>«Произведения».</strong> Например, «Гарри Поттер и Тайная комната» как логическая концепция, охватывающая все его версии, такие как различные переводы и переиздания. Это полезное определение, но может быть трудно провести грань, что считать. Например, мы, вероятно, хотим сохранить разные переводы, хотя переиздания с незначительными изменениями могут быть не так важны. - Анна и команда (<a %(reddit)s>Reddit</a>) С помощью Pirate Library Mirror (ИЗМЕНЕНИЕ: перемещено на <a %(wikipedia_annas_archive)s>Архив Анны</a>), наша цель — собрать все книги в мире и сохранить их навсегда.<sup>1</sup> Между нашими торрентами Z-Library и оригинальными торрентами Library Genesis у нас есть 11 783 153 файла. Но сколько это на самом деле? Если мы правильно удалим дубликаты этих файлов, какой процент всех книг в мире мы сохранили? Мы действительно хотели бы иметь что-то вроде этого: Начнем с некоторых приблизительных чисел: Как в Z-Library/Libgen, так и в Open Library, книг гораздо больше, чем уникальных ISBN. Означает ли это, что у многих из этих книг нет ISBN, или просто отсутствуют метаданные ISBN? Мы, вероятно, можем ответить на этот вопрос, используя комбинацию автоматического сопоставления на основе других атрибутов (название, автор, издатель и т. д.), привлекая больше источников данных и извлекая ISBN из самих сканов книг (в случае Z-Library/Libgen). Сколько из этих ISBN уникальны? Это лучше всего иллюстрируется с помощью диаграммы Венна: Чтобы быть более точным: Мы были удивлены, насколько мало пересечений! ISBNdb содержит огромное количество ISBN, которые не появляются ни в Z-Library, ни в Open Library, и то же самое справедливо (в меньшей, но все же значительной степени) для других двух. Это вызывает множество новых вопросов. Насколько автоматическое сопоставление поможет в маркировке книг, которые не были помечены ISBN? Будет ли много совпадений и, следовательно, увеличится ли пересечение? Также, что произойдет, если мы добавим 4-й или 5-й набор данных? Насколько велико будет пересечение тогда? Это дает нам отправную точку. Теперь мы можем рассмотреть все ISBN, которые не были в наборе данных Z-Library и которые не совпадают с полями названия/автора. Это может помочь нам в сохранении всех книг в мире: сначала путем сканирования интернета на наличие сканов, затем путем выхода в реальную жизнь для сканирования книг. Последнее даже может быть профинансировано сообществом или стимулировано «вознаграждениями» от людей, которые хотели бы видеть определенные книги в цифровом формате. Все это — история для другого времени. Если вы хотите помочь с чем-либо из этого — дальнейший анализ; сбор дополнительных метаданных; поиск большего количества книг; OCR книг; выполнение этого для других областей (например, статьи, аудиокниги, фильмы, телешоу, журналы) или даже предоставление некоторых из этих данных для таких вещей, как обучение ML / больших языковых моделей — пожалуйста, свяжитесь со мной (<a %(reddit)s>Reddit</a>). Если вас особенно интересует анализ данных, мы работаем над тем, чтобы сделать наши наборы данных и скрипты доступными в более удобном формате. Было бы здорово, если бы вы могли просто форкнуть блокнот и начать с ним работать. Наконец, если вы хотите поддержать эту работу, пожалуйста, рассмотрите возможность пожертвования. Это полностью волонтерская операция, и ваш вклад имеет огромное значение. Каждая помощь важна. На данный момент мы принимаем пожертвования в криптовалюте; смотрите страницу «Пожертвовать» на Архиве Анны. Для процента нам нужен знаменатель: общее количество когда-либо опубликованных книг.<sup>2</sup> До закрытия Google Books инженер проекта Леонид Тайчер <a %(booksearch_blogspot)s>попытался оценить</a> это число. Он пришел — в шутку — к 129 864 880 («по крайней мере, до воскресенья»). Он оценил это число, создав единую базу данных всех книг в мире. Для этого он собрал различные наборы данных и затем объединил их различными способами. Кратко отвлечемся: есть еще один человек, который пытался каталогизировать все книги в мире: Аарон Шварц, покойный цифровой активист и соучредитель Reddit.<sup>3</sup> Он <a %(youtube)s>начал Open Library</a> с целью «одна веб-страница для каждой когда-либо опубликованной книги», объединяя данные из множества различных источников. Он заплатил высшую цену за свою работу по цифровому сохранению, когда его обвинили в массовой загрузке научных статей, что привело к его самоубийству. Не нужно говорить, что это одна из причин, по которой наша группа анонимна, и почему мы очень осторожны. Open Library все еще героически управляется сотрудниками Internet Archive, продолжая наследие Аарона. Мы вернемся к этому позже в этом посте. В блоге Google Тайчер описывает некоторые проблемы с оценкой этого числа. Во-первых, что такое книга? Существует несколько возможных определений: «Издания» кажутся наиболее практичным определением того, что такое «книги». Удобно, что это определение также используется для присвоения уникальных номеров ISBN. ISBN, или Международный стандартный книжный номер, обычно используется для международной торговли, так как он интегрирован с международной системой штрих-кодов («Международный номер статьи»). Если вы хотите продавать книгу в магазинах, ей нужен штрих-код, поэтому вы получаете ISBN. В блоге Тайчера упоминается, что хотя ISBN полезны, они не универсальны, так как они были действительно приняты только в середине семидесятых и не везде по всему миру. Тем не менее, ISBN, вероятно, является наиболее широко используемым идентификатором книжных изданий, поэтому это наш лучший отправной пункт. Если мы сможем найти все ISBN в мире, мы получим полезный список книг, которые еще нужно сохранить. Итак, где мы получаем данные? Существует ряд существующих усилий, которые пытаются составить список всех книг в мире: В этом посте мы рады объявить о небольшом выпуске (по сравнению с нашими предыдущими выпусками Z-Library). Мы извлекли большую часть ISBNdb и сделали данные доступными для торрентов на сайте Pirate Library Mirror (ИЗМЕНЕНИЕ: перемещено на <a %(wikipedia_annas_archive)s>Архив Анны</a>; мы не будем ссылаться на него здесь напрямую, просто найдите его). Это около 30,9 миллиона записей (20 ГБ в формате <a %(jsonlines)s>JSON Lines</a>; 4,4 ГБ в сжатом виде). На их сайте они утверждают, что у них на самом деле 32,6 миллиона записей, так что мы, возможно, как-то пропустили некоторые, или <em>они</em> могли что-то сделать неправильно. В любом случае, пока мы не будем делиться тем, как мы это сделали — оставим это как упражнение для читателя. ;-) Что мы поделимся, так это некоторым предварительным анализом, чтобы попытаться приблизиться к оценке количества книг в мире. Мы рассмотрели три набора данных: этот новый набор данных ISBNdb, наш оригинальный выпуск metadata, который мы извлекли из теневой библиотеки Z-Library (включая Library Genesis), и дамп данных Open Library. Выгрузка ISBNdb, или Сколько Книг Сохранено Навсегда? Если бы мы правильно удалили дубликаты файлов из теневых библиотек, какой процент всех книг в мире мы бы сохранили? Обновления о <a %(wikipedia_annas_archive)s>Архиве Анны</a>, крупнейшей по-настоящему открытой библиотеке в истории человечества. <em>Редизайн WorldCat</em> Данные <strong>Формат?</strong> <a %(blog)s>Контейнеры Архива Анны (AAC)</a>, которые по сути представляют собой <a %(jsonlines)s>JSON Lines</a>, сжатые с помощью <a %(zstd)s>Zstandard</a>, плюс некоторые стандартизированные семантики. Эти контейнеры оборачивают различные типы записей, основанные на различных скрейпах, которые мы развернули. Год назад мы <a %(blog)s>начали</a> отвечать на этот вопрос: <strong>Какой процент книг был навсегда сохранен теневыми библиотеками?</strong> Давайте рассмотрим основную информацию о данных: Как только книга попадает в теневую библиотеку с открытыми данными, такую как <a %(wikipedia_library_genesis)s>Library Genesis</a>, а теперь и <a %(wikipedia_annas_archive)s>Архив Анны</a>, она зеркалируется по всему миру (через торренты), тем самым практически сохраняясь навсегда. Чтобы ответить на вопрос, какой процент книг был сохранен, нам нужно знать знаменатель: сколько книг существует в целом? И в идеале у нас есть не просто число, а фактические метаданные. Тогда мы сможем не только сопоставить их с теневыми библиотеками, но и <strong>создать список книг, которые еще нужно сохранить!</strong> Мы даже можем начать мечтать о краудсорсинговом усилии по выполнению этого списка. Мы собрали данные из <a %(wikipedia_isbndb_com)s>ISBNdb</a> и загрузили <a %(openlibrary)s>набор данных Open Library</a>, но результаты были неудовлетворительными. Основная проблема заключалась в том, что не было большого пересечения ISBN. Смотрите эту диаграмму Венна из <a %(blog)s>нашего блога</a>: Мы были очень удивлены тем, насколько мало пересечений было между ISBNdb и Open Library, которые щедро включают данные из различных источников, таких как веб-скрейпинг и библиотечные записи. Если бы они оба хорошо справлялись с поиском большинства ISBN, их круги, безусловно, имели бы значительное пересечение, или один был бы подмножеством другого. Это заставило нас задуматься, сколько книг выпадают <em>полностью за пределы этих кругов</em>? Нам нужна большая база данных. Именно тогда мы обратили внимание на крупнейшую базу данных книг в мире: <a %(wikipedia_worldcat)s>WorldCat</a>. Это собственная база данных некоммерческой организации <a %(wikipedia_oclc)s>OCLC</a>, которая агрегирует метаданные из библиотек по всему миру в обмен на предоставление этим библиотекам доступа к полному набору данных и их отображение в результатах поиска конечных пользователей. Хотя OCLC является некоммерческой организацией, их бизнес-модель требует защиты их базы данных. Ну, мы сожалеем, друзья из OCLC, но мы раздаем все. :-) За последний год мы тщательно скрейпили все записи WorldCat. Сначала нам повезло. WorldCat только что запускал полное обновление своего веб-сайта (в августе 2022 года). Это включало значительное обновление их бэкэнд-систем, что привело к появлению множества уязвимостей безопасности. Мы немедленно воспользовались этой возможностью и смогли скрейпить сотни миллионов (!) записей всего за несколько дней. После этого уязвимости безопасности постепенно исправлялись одна за другой, пока последняя из найденных нами не была устранена около месяца назад. К тому времени у нас были практически все записи, и мы стремились только к немного более качественным записям. Поэтому мы решили, что пришло время выпустить их! 1,3 миллиарда скрейпов WorldCat <em><strong>Кратко:</strong> Архив Анны собрал все данные WorldCat (крупнейшей в мире коллекции библиотечных метаданных), чтобы составить список книг, которые нужно сохранить.</em> WorldCat Внимание: этот блог-пост устарел. Мы решили, что IPFS еще не готов для массового использования. Мы все еще будем ссылаться на файлы в IPFS из Архива Анны, когда это возможно, но больше не будем размещать их у себя и не рекомендуем другим зеркалировать с использованием IPFS. Пожалуйста, посетите нашу страницу Торрентов, если вы хотите помочь сохранить нашу коллекцию. Помогите раздавать Z-Library на IPFS Загрузка через Север Партнёра SciDB Внешний заём Внешний заём (печать отключена) Внешняя загрузка Просмотреть метаданные Содержится в торрентах Назад  (+%(num)s бонус) не оплачен оплачен отменён просрочен ожидание подтверждения от Анны неверно Этот текст доступен только на английском языке. Перейти Сбросить Вперёд Последний Если ваш email адрес не работает на форумах Libgen, мы рекомендуем использовать <a %(a_mail)s>Proton Mail</a> (бесплатно). Вы также можете <a %(a_manual)s>вручную запросить</a> активацию вашего аккаунта. (может потребоваться <a %(a_browser)s>браузерная верификация</a> — неограниченные загрузки!) Быстрый Сервер Партнёра №%(number)s (рекомендуется) (немного быстрее, но с листом ожидания) (не требуется браузерная верификация) (без проверки браузера или листов ожидания) (без листа ожидания, но может быть очень медленным) Медленный Сервер Партнёра №%(number)s Аудиокнига Комикс Книга (Художественная Литература) Книга (Документальная) Книга (неизвестно) Статья журнала Журнал Музыкальная партитура Другое Документ о стандартах Не все страницы могут быть преобразованы в PDF Помечено как сломанное в библиотеке Genesis ".li-fork" Невидимый в Libgen.li Невидимая в Libgen.rs Документальная Литература Невидимая в Libgen.rs Художественная Литература Не удалось запустить exiftool на этом файле Отмечено как «неправильный файл» в Z-Library Отсутствует в Z-Library Отмечено как «спам» в Z-Library Файл не может быть открыт (напр. из-за повреждения или DRM) Претензия по поводу авторских прав Проблемы с загрузкой (например: невозможно подключиться, ошибка, медленная скорость) Неправильные метаданные (например: название, описание, обложка) Другое Плохое качество (например: проблемы с форматированием, плохое качество сканирования, отсутствующие страницы) Спам / файл должен быть удален (например: реклама, оскорбительное содержание) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Восхитительный Книголюб Счастливый Библиотекарь Блестящий Коллекционер Потрясающий Архивариус Бонусные загрузки Cerlalc Чешская метадата DuXiu 读秀 Индекс электронных книг EBSCOhost Google Книги Goodreads HathiTrust IA Контролируемая цифровая выдача IA ISBNdb ISBN GRP Libgen.li Исключая «scimag» Libgen.rs Нон-фикшн и художественная литература Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Российская государственная библиотека Sci-Hub Посредством  Libgen.li «scimag» Sci-Hub / Libgen “scimag” Трантор Загрузки в АА Z-Library Z-Library на китайском Название, автор, DOI, ISBN, MD5, … Поиск Автор Описание и комментарии к метаданным Издание Оригинальное имя файла Издатель (специальное поле для поиска) Название Год издания Технические детали Эта монета имеет более высокий минимум, чем обычно. Пожалуйста, выберите другую продолжительность или другую монету. Запрос не может быть выполнен. Пожалуйста, попробуйте ещё раз через пару минут и если это продолжит происходить, напишите нам на адрес %(email)s, предоставив скриншот. Произошла неизвестная ошибка. Пожалуйста, напишите нам на адрес %(email)s, предоставив скриншот. Ошибка в процессе платежа. Пожалуйста, подождите немного и попробуйте снова. Если проблема повторится по истечении 24 часов, пожалуйста, напишите нам на почту: %(email)s, прикрепив скриншот. Мы проводим сбор средств на <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">поддержку</a> самой большой теневой библиотеки комиксов в мире. Спасибо за вашу поддержку!<a href="/donate">Пожертвовать.</a> Если вы не можете сделать пожертвование, поддержите нас, рассказав своим друзьям, и подпишитесь на нас в <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> или <a href="https://t.me/annasarchiveorg">Telegram</a>. Не пишите нам по электронной почте, чтобы <a %(a_request)s>запросить книги</a><br> или маленькие (<10k) <a %(a_upload)s>загрузки</a>. Архив Анны DMCA / претензии по авторским правам Оставайтесь в курсе событий Reddit Альтернативы SLUM (%(unaffiliated)s) неаффилированный Архиву Анны нужна ваша помощь! Если вы пожертвуете сейчас, вы получите <strong>вдвое</strong> больше быстрых загрузок. Многие пытаются нас закрыть, но мы боремся. Если вы пожертвуете в этом месяце, вы получите <strong>вдвое</strong> больше быстрых загрузок. Действительно до конца этого месяца. Сбережение человеческих знаний: отличный подарок к празднику! Членства будут продлены соответственно. Серверы партнёров недоступны из-за закрытия хостинга. Они должны снова заработать в ближайшее время. Мы ищем волонтёров, готовых поддерживать зеркала, чтобы увеличить устойчивость архива. У нас есть новый способ пожертвования :%(method_name)s. Пожалуйста, подумайте о %(donate_link_open_tag)s пожертвовании</a> — содержание этого сайта обходится недешево, и ваше пожертвование действительно имеет значение. Большое спасибо. Пригласите друга и вы оба получите %(percentage)s%% бонусных быстрых загрузок! Удивите близкого человека, подарите ему аккаунт с членством. Идеальный подарок ко Дню святого Валентина! Подробнее… Аккаунт Активность Дополнительно Блог Анны ↗ Программы Анны ↗ бета Исследователь кодов Наборы данных Пожертвовать Скачанные файлы ЧаВо Главная Улучшить метаданные Данные LLM Войти / Регистрация Мои пожертвования Публичный профиль Поиск Безопасность Торренты Помочь с переводом ↗ Волонтерство и вознаграждения Недавние скачивания: 📚&nbsp;Самая большая в мире открытая библиотека знаний с открытым исходным кодом. ⭐️&nbsp;Зеркала Sci-Hub, Library Genesis, Z-Library и других. 📈&nbsp;%(book_any)s книги, %(journal_article)s статьи, %(book_comic)s комиксы, %(magazine)s журналы — сохранены навсегда.  и  и не только DuXiu Абонементная библиотека Internet Achive LibGen 📚&nbsp;Самая большая полностью открытая библиотека в истории человечества. 📈&nbsp;%(book_count)s&nbsp;книг, %(paper_count)s&nbsp;документов сохранено навсегда. ⭐️&nbsp;Мы зеркалим %(libraries)s. Мы собираем и выкладываем в открытый доступ %(scraped)s. Весь наш исходный код и данные полностью открыты. OpenLib Sci-Hub ,  📚 Крупнейшая в мире библиотека с открытыми кодом и наборами данных.<br>⭐️ Зеркала Scihub, Libgen, ZLib и других. Z-Lib Архив Анны Неверный запрос. Посетите %(websites)s. Крупнейшая в мире библиотека открытых данных с открытым исходным кодом. Зеркало Sci-Hub, Library Genesis, Z-Library и других. Поиск по Архиву Анны Архив Анны Пожалуйста, обновите страницу, чтобы попробовать снова. <a %(a_contact)s>Свяжитесь с нами</a>, если проблема сохраняется в течение нескольких часов. 🔥 Проблема с загрузкой этой страницы <li>1. Следите за нами на <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, или <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Делитесь об Архиве Анны на Twitter, Reddit, Tiktok, Instagram, в кафе или библиотеке по соседству, или где угодно ещё! Мы верим в открытость — если нас прикроют, то мы появимся где-нибудь ещё, ведь весь наш код и данные полностью открыты.</li><li>3. Если у вас есть возможность, рассмотрите <a href="/donate">внесение пожертвования</a>.</li><li>4. Помогите <a href="https://translate.annas-software.org/">перевести</a> наш сайт на другие языки.</li><li>5. Если вы разработчик, вы можете помочь с <a href="https://annas-software.org/">исходным кодом</a>, или стать сидером наших <a href="/datasets">торрентов</a>.</li> 10. Создайте или помогите поддержать страницу Википедии об Архиве Анны на вашем языке. 11. Мы хотим разместить небольшую, со вкусом подобранную рекламу. Если вы хотите разместить рекламу в Архиве Анны, пожалуйста, свяжитесь с нами. 6. Если вы являетесь исследователем безопасности, мы можем использовать ваши навыки как для нападения, так и для защиты. Посетите нашу страницу о <a %(a_security)s>безопасности</a>. 7. Мы ищем специалистов по платежам для анонимных торговцев. Можете ли вы помочь нам добавить больше удобных способов пожертвования? PayPal, WeChat, подарочные карты. Если вы знаете кого-то, пожалуйста, свяжитесь с нами. 8. Мы постоянно ищем возможность увеличить мощность сервера. 9. Вы можете помочь, сообщая о проблемах с файлами, оставляя комментарии и создавая списки прямо на этом сайте. Вы также можете помочь <a %(a_upload)s>загрузив больше книг</a> или исправив проблемы с файлами или форматированием существующих книг. Для получения более подробной информации о том, как стать волонтером, см. нашу страницу <a %(a_volunteering)s>Волонтерство и награды</a>. Мы твёрдо верим в свободный поток информации и сохранении знаний и культуры. С этой поисковой системой, мы сооружаем на плечах гигантов. Мы глубоко уважаем тяжёлую работу людей, создавших различные теневые библиотеки, и надеемся, что эта поисковая система расширит их возможности. Чтобы быть в курсе наших обновлений, следите за нами в <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> или <a href="https://t.me/annasarchiveorg">Telegram</a>. Чтобы задать вопрос или оставить отзыв напишите нам на %(email)s. ID аккаунта: %(account_id)s Выйти ❌ Что-то пошло не так. Просим перезагрузить страницу и попробовать снова. ✅ Вы вышли из системы. Перезагрузите страницу, чтобы зайти снова. Быстрых загрузок использовано (за последние 24 часа): <strong>%(used)s из %(total)s</strong> Уровень участника: <strong>%(tier_name)s</strong> до %(until_date)s <a %(a_extend)s>(поднять)</a> Вы можете комбинировать разные членства (быстрые загрузки за 24 часа будут суммироваться). Уровень участника: <strong>нет</strong> <a %(a_become)s>(принять участие)</a> Свяжитесь с Анной по %(email)s в случае если хотите поднять свой уровень участника. Общедоступный профиль: %(profile_link)s Секретный ключ (не показывайте его никому!): %(secret_key)s показать Присоединяйтесь к нам здесь! Увеличьте свой <a %(a_tier)s>уровень членства</a>, чтобы присоединиться к нашей группе. Эксклюзивная Телеграм группа: %(link)s Учётная запись какие загрузки? Войти Не потеряйте свой ключ! Секретный ключ недействителен. Проверьте ключ и попробуйте снова, а если хотите — создайте новую учётную запись ниже. Секретный ключ Введите свой секретный ключ, чтобы войти: Старая учётная запись на базе email? Введите <a %(a_open)s>эл. почту здесь</a>. Зарегистрироваться Вы пока без учётной записи? Регистрация выполнена! Вот ваш секретный ключ: <span %(span_key)s>%(key)s</span> Берегите этот ключ. Если потеряете его, лишитесь и доступа к своей учётной записи. <li %(li_item)s><strong>В закладки.</strong> Вы можете добавить эту страницу в закладки, чтобы найти свой ключ вновь.</li><li %(li_item)s><strong>Скачать.</strong> Нажмите <a %(a_download)s>на эту ссылку</a>, чтобы скачать ключ.</li><li %(li_item)s><strong>Менеджер паролей.</strong> Сохраните ключ в менеджере паролей, когда введёте его ниже.</li> Войти / Зарегистрироваться Проверка браузера Предупреждение: код содержит некорректные символы Unicode и может работать неправильно в различных ситуациях. Сырой бинарный код можно декодировать из base64 представления в URL. Описание Ярлык Префикс URL для конкретного кода Вебсайт Коды, начинающиеся с «%(prefix_label)s» Пожалуйста, не сканируйте эти страницы. Вместо этого мы рекомендуем <a %(a_import)s>генерировать</a> или <a %(a_download)s>скачивать</a> наши базы данных ElasticSearch и MariaDB и запускать наш <a %(a_software)s>открытый исходный код</a>. Сырые данные можно вручную исследовать через JSON файлы, такие как <a %(a_json_file)s>этот</a>. Менее %(count)s записей Общий URL Исследователь кодов Индекс Просматривайте коды, которыми помечены записи, по префиксу. Колонка «записи» показывает количество записей, помеченных кодами с данным префиксом, как видно в поисковой системе (включая записи только с метаданными). Колонка «коды» показывает, сколько фактических кодов имеют данный префикс. Известный префикс кода «%(key)s» Больше… Префикс %(count)s запись, соответсвующая “%(prefix_label)s” %(count)s записи, соответсвующие “%(prefix_label)s” %(count)s записей, соответствующих «%(prefix_label)s» коды записи «%%s» будет заменено значением кода Поиск в Архиве Анны Коды URL для конкретного кода: «%(url)s» Эта страница может генерироваться некоторое время, поэтому требуется капча Cloudflare. <a %(a_donate)s>Члены</a> могут пропустить капчу. Нарушение сообщено: Улучшенная версия …Хотите-ли Вы послать жалобу на этого пользователя, за оскорбительное или неуместное поведение? Проблема с файлом: %(file_issue)s скрытый комментарий Ответить Сообщить о нарушении Вы отправили жалобу на этого пользователя за нарушение. Претензии по авторским правам, отправленные на данный адрес будут проигнорированы. Вместо этого используйте соответствующую форму. Показать электронную почту Мы очень рады вашим отзывам и вопросам! В связи с огромным количеством спама и бессмысленных писем, которые мы получаем, вам необходимо проставить галочки напротив пунктов ниже, чтобы подтвердить, что вы поняли все условия обратной связи. Любые попытки связаться с нами по вопросам авторских прав другими способами будут автоматически проигнорированы. Для претензий по авторским правам/DMCA, используйте <a %(a_copyright)s>эту форму</a>. Контактная электронная почта URL-адреса на Архиве Анны (обязательно). Один на строку. Пожалуйста, включайте только URL-адреса, которые описывают точно такое же издание книги. Если вы хотите подать иск на несколько книг или несколько изданий, пожалуйста, заполните эту форму несколько раз. Иски, включающие в себя несколько книг или изданий, будут отклонены. Адрес (обязательно) Четкое описание исходного материала (обязательно) Электронная почта (обязательно) URL-адреса исходного материала, один на строку (обязательно). Пожалуйста, включите как можно больше, чтобы помочь нам проверить ваш иск (например, Amazon, WorldCat, Google Books, DOI). ISBN исходного материала (если применимо). Один на строку. Пожалуйста, включайте только те, которые точно соответствуют изданию, по которому вы подаете иск о нарушении авторских прав. Ваше имя (обязательно) ❌ Что-то пошло не так. Пожалуйста, перезагрузите страницу и попробуйте снова. ✅ Спасибо за подачу вашего иска о нарушении авторских прав. Мы рассмотрим его в ближайшее время. Пожалуйста, перезагрузите страницу, чтобы подать следующий. <a %(a_openlib)s>Open Library</a> URL-адреса исходного материала, один на строку. Пожалуйста, найдите время, чтобы найти ваш исходный материал в Open Library. Это поможет нам проверить ваш иск. Номер телефона (обязательно) Заявление и подпись (обязательно) Отправить иск Если у вас есть иск DMCA или другой иск о нарушении авторских прав, пожалуйста, заполните эту форму как можно точнее. Если у вас возникнут проблемы, свяжитесь с нами по нашему специальному адресу для DMCA: %(email)s. Обратите внимание, что иски, отправленные на этот адрес по электронной почте, не будут обработаны, он предназначен только для вопросов. Пожалуйста, используйте форму ниже для подачи ваших исков. Форма подачи иска DMCA / о нарушении авторских прав Пример записи в Архиве Анны Торренты Архива Анны Формат контейнеров Anna’s Archive Скрипты для импорта метаданных Если вы заинтересованы в зеркалировании этого набора данных для <a %(a_archival)s>архивирования</a> или <a %(a_llm)s>обучения LLM</a>, пожалуйста, свяжитесь с нами. Последнее обновление: %(date)s Основной сайт %(source)s Документация по метаданным (большинство полей) Файлы, зеркалированные Архивом Анны: %(count)s (%(percent)s%%) Ресурсы Всего файлов: %(count)s Общий размер файлов: %(size)s Наш пост в блоге об этих данных <a %(duxiu_link)s>DuXiu</a> — это огромная база данных отсканированных книг, созданная <a %(superstar_link)s>SuperStar Digital Library Group</a>. Большинство из них — академические книги, отсканированные для цифрового доступа университетам и библиотекам. Для нашей англоязычной аудитории – хорошие обзоры от<a %(princeton_link)s>Принстон</a> и <a %(uw_link)s>Вашингтонского университета</a>. Также есть отличная статья, содержащая больше информации: <a %(article_link)s>«Оцифровка китайских книг: пример поисковой системы SuperStar DuXiu Scholar»</a>. Книги из DuXiu давно пиратятся в китайском интернете. Обычно их продают за менее чем доллар перекупщики. Они, как правило, распространяются с использованием китайского аналога Google Drive, который часто взламывают для увеличения объема хранилища. Некоторые технические детали можно найти <a %(link1)s>здесь</a> и <a %(link2)s>здесь</a>. Хотя книги были полупублично распространены, их довольно сложно получить в больших объемах. Это было в верхней части нашего списка дел, и мы выделили на это несколько месяцев полной занятости. Однако в конце 2023 года к нам обратился невероятный, удивительный и талантливый волонтер, сообщивший, что он уже проделал всю эту работу — за большие деньги. Он поделился с нами всей коллекцией, не ожидая ничего взамен, кроме гарантии долгосрочного сохранения. Поистине замечательно. Дополнительная информация от наших волонтеров (черновые заметки): Адаптировано из нашего <a %(a_href)s>поста в блоге</a>. DuXiu 读秀 %(count)s файл %(count)s файла %(count)s файлов Этот набор данных тесно связан с <a %(a_datasets_openlib)s>набором данных Open Library</a>. Он содержит скрейп всех метаданных и большую часть файлов из контролируемой цифровой библиотеки IA. Обновления выпускаются в <a %(a_aac)s>формате контейнеров Архива Анны</a>. Эти записи ссылаются непосредственно на набор данных Open Library, но также содержат записи, которых нет в Open Library. У нас также есть несколько файлов данных, собранных членами сообщества за эти годы. Коллекция состоит из двух частей. Вам нужны обе части, чтобы получить все данные (за исключением устаревших торрентов, которые вычеркнуты на странице торрентов). Цифровая библиотека наш первый выпуск, до того как мы стандартизировали формат <a %(a_aac)s>Контейнеры Архива Анны (AAC)</a>. Содержит метаданные (в формате json и xml), pdf-файлы (из систем цифрового кредитования acsm и lcpdf) и миниатюры обложек. инкрементальные новые выпуски, используя AAC. Содержит только метаданные с временными метками после 2023-01-01, так как остальное уже покрыто «ia». Также все pdf-файлы, на этот раз из систем кредитования acsm и «bookreader» (веб-ридер IA). Несмотря на то, что название не совсем правильное, мы все равно добавляем файлы bookreader в коллекцию ia2_acsmpdf_files, так как они взаимно исключают друг друга. Контролируемая цифровая выдача IA 98%%+ файлов доступны для поиска. Наша миссия — архивировать все книги в мире (а также статьи, журналы и т.д.) и сделать их широко доступными. Мы считаем, что все книги должны быть широко зеркалированы, чтобы обеспечить избыточность и устойчивость. Именно поэтому мы собираем файлы из различных источников. Некоторые источники полностью открыты и могут быть зеркалированы массово (например, Sci-Hub). Другие закрыты и защищены, поэтому мы стараемся извлечь их, чтобы «освободить» их книги. Третьи находятся где-то посередине. Все наши данные можно <a %(a_torrents)s>скачать через торрент</a>, а все наши метаданные можно <a %(a_anna_software)s>сгенерировать</a> или <a %(a_elasticsearch)s>скачать</a> в виде баз данных ElasticSearch и MariaDB. Сырые данные можно вручную исследовать через JSON-файлы, такие как <a %(a_dbrecord)s>этот</a>. Метаданные Веб-сайт ISBN Последнее обновление: %(isbn_country_date)s (%(link)s) Ресурсы Международное агентство ISBN регулярно выпускает диапазоны, которые оно выделило национальным агентствам ISBN. Из этого мы можем определить, к какой стране, региону или языковой группе принадлежит этот ISBN. В настоящее время мы используем эти данные косвенно, через библиотеку Python <a %(a_isbnlib)s>isbnlib</a>. Информация о стране ISBN Это дамп большого количества запросов к isbndb.com в сентябре 2022 года. Мы пытались охватить все диапазоны ISBN. Это около 30,9 миллионов записей. На их веб-сайте они утверждают, что у них на самом деле 32,6 миллиона записей, так что мы могли что-то упустить, или <em>они</em> могли что-то сделать неправильно. Ответы в формате JSON практически не изменены с их сервера. Одна из проблем с качеством данных, которую мы заметили, заключается в том, что для номеров ISBN-13, которые начинаются с другого префикса, чем «978-», они все равно включают поле «isbn», которое просто является номером ISBN-13 с обрезанными первыми 3 цифрами (и пересчитанной контрольной цифрой). Это явно неправильно, но они, похоже, так делают, поэтому мы не изменяли это. Еще одна потенциальная проблема, с которой вы можете столкнуться, заключается в том, что поле «isbn13» имеет дубликаты, поэтому вы не можете использовать его в качестве первичного ключа в базе данных. Поля «isbn13»+«isbn» в сочетании, похоже, уникальны. Выпуск 1 (2022-10-31) Торренты художественной литературы отстают (хотя ID ~4-6M не торрентились, так как они пересекаются с нашими Zlib торрентами). Наш блог-пост о выпуске комиксов Торренты комиксов в Архиве Анны Для предыстории различных форков Library Genesis см. страницу <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li содержит большую часть того же контента и метаданных, что и Libgen.rs, но имеет некоторые дополнительные коллекции, а именно комиксы, журналы и стандартные документы. Он также интегрирует <a %(a_scihub)s>Sci-Hub</a> в свою систему метаданных и поисковую систему, и это то, что мы используем для нашей базы данных. Метаданные для этой библиотеки свободно доступны <a %(a_libgen_li)s>на libgen.li</a>. Однако это медленный сервер, который не поддерживает возобновление прерванных соединений. Те же файлы также доступны на <a %(a_ftp)s>FTP-сервере</a>, который работает лучше. Похоже, что и нехудожественная литература также отклонилась, но без новых торрентов. Похоже, это произошло с начала 2022 года, хотя мы это не проверяли. По словам администратора Libgen.li, коллекция «fiction_rus» (русская художественная литература) должна быть покрыта регулярно выпускаемыми торрентами от <a %(a_booktracker)s>booktracker.org</a>, особенно торрентами <a %(a_flibusta)s>flibusta</a> и <a %(a_librusec)s>lib.rus.ec</a> (которые мы зеркалим <a %(a_torrents)s>здесь</a>, хотя мы еще не установили, какие торренты соответствуют каким файлам). Коллекция художественной литературы имеет свои собственные торренты (отличающиеся от <a %(a_href)s>Libgen.rs</a>), начиная с %(start)s. Определенные диапазоны без торрентов (например, диапазоны художественной литературы f_3463000 до f_4260000) вероятно содержат файлы Z-Library (или другие дубликаты), хотя мы можем захотеть провести дедупликацию и создать торренты для уникальных файлов lgli в этих диапазонах. Статистику по всем коллекциям можно найти <a %(a_href)s>на сайте libgen</a>. Торренты доступны для большей части дополнительного контента, в частности, торренты для комиксов, журналов и стандартных документов были выпущены в сотрудничестве с Архивом Анны. Обратите внимание, что торрент-файлы, относящиеся к «libgen.is», являются явными зеркалами <a %(a_libgen)s>Libgen.rs</a> («.is» — это другой домен, используемый Libgen.rs). Полезный ресурс для использования метаданных — <a %(a_href)s>эта страница</a>. %(icon)s Их коллекция «fiction_rus» (русская художественная литература) не имеет выделенных торрентов, но покрывается торрентами от других, и мы поддерживаем <a %(fiction_rus)s>зеркало</a>. Торренты русской художественной литературы в Архиве Анны Торренты художественной литературы на Архиве Анны Форум для обсуждений Метаданные Метаданные через FTP Торренты журналов в Архиве Анны Информация о полях метаданных Зеркало других торрентов (и уникальные торренты художественной литературы и комиксов) Торренты стандартных документов в Архиве Анны Libgen.li Торренты от Архива Анны (обложки книг) Library Genesis известен тем, что уже щедро предоставляет свои данные в больших объемах через торренты. Наша коллекция Libgen состоит из вспомогательных данных, которые они не выпускают напрямую, в партнерстве с ними. Огромная благодарность всем, кто работает с Library Genesis, за сотрудничество с нами! Наш блог о выпуске обложек книг Эта страница посвящена версии «.rs». Она известна тем, что последовательно публикует как свои метаданные, так и полное содержание своего каталога книг. Коллекция книг разделена на художественную и нехудожественную литературу. Полезный ресурс для использования метаданных — <a %(a_metadata)s>эта страница</a> (блокирует диапазоны IP, может потребоваться VPN). По состоянию на 2024-03, новые торренты публикуются в <a %(a_href)s>этой теме форума</a> (блокирует диапазоны IP, может потребоваться VPN). Торренты художественной литературы на Архиве Анны Торренты художественной литературы Libgen.rs Форум для обсуждений Libgen.rs Метаданные Libgen.rs Информация о полях метаданных Libgen.rs Торренты научно-популярной литературы Libgen.rs Торренты научно-популярной литературы на Архиве Анны %(example)s для художественной книги. Этот <a %(blog_post)s>первый выпуск</a> довольно мал: около 300 ГБ обложек книг из форка Libgen.rs, как художественной, так и научно-популярной литературы. Они организованы так же, как они появляются на libgen.rs, например: %(example)s для научно-популярной книги. Так же, как и с коллекцией Z-Library, мы поместили их все в один большой .tar файл, который можно смонтировать с помощью <a %(a_ratarmount)s>ratarmount</a>, если вы хотите напрямую обслуживать файлы. Выпуск 1 (%(date)s) Краткая история различных форков Library Genesis (или «Libgen») заключается в том, что со временем разные люди, участвовавшие в Library Genesis, разошлись и пошли своими путями. Согласно этому <a %(a_mhut)s>сообщению на форуме</a>, Libgen.li изначально размещался на «http://free-books.dontexist.com». Версия «.fun» была создана оригинальным основателем. Она обновляется в пользу новой, более распределенной версии. <a %(a_li)s>Версия «.li»</a> имеет огромную коллекцию комиксов, а также другого контента, который (пока) недоступен для массовой загрузки через торренты. У нее есть отдельная коллекция торрентов художественных книг, и она содержит метаданные <a %(a_scihub)s>Sci-Hub</a> в своей базе данных. Версия «.rs» имеет очень похожие данные и наиболее последовательно выпускает свою коллекцию в виде массовых торрентов. Она примерно разделена на секции «художественная литература» и «нехудожественная литература». Изначально на «http://gen.lib.rus.ec». <a %(a_zlib)s>Z-Library</a> в некотором смысле также является форком Library Genesis, хотя они использовали другое название для своего проекта. Libgen.rs Мы также обогащаем нашу коллекцию источниками только с метаданными, которые мы можем сопоставить с файлами, например, используя номера ISBN или другие поля. Ниже приведен их обзор. Опять же, некоторые из этих источников полностью открыты, в то время как данные из других нам приходится извлекать. Обратите внимание, что в поиске по метаданным мы показываем оригинальные записи. Мы не объединяем записи. Источники только с метаданными Open Library — это проект с открытым исходным кодом от Internet Archive, целью которого является каталогизация каждой книги в мире. У него одна из крупнейших в мире операций по сканированию книг, и многие книги доступны для цифрового заимствования. Его каталог метаданных книг доступен для свободного скачивания и включен в Архив Анны (хотя в настоящее время не доступен для поиска, за исключением случаев, когда вы явно ищете по ID Open Library). Open Library За исключением дубликатов Последнее обновление Процентное соотношение количества файлов %% зеркалировано AA / торренты доступны Размер Источник Ниже приведен краткий обзор источников файлов на Архиве Анны. Поскольку теневые библиотеки часто синхронизируют данные друг с другом, между ними существует значительное совпадение. Поэтому цифры не складываются в общую сумму. Процент «зеркалированных и загруженных Архивом Анны» показывает, сколько файлов мы зеркалим сами. Мы загружаем эти файлы оптом через торренты и делаем их доступными для прямого скачивания через партнерские сайты. Обзор Всего Торренты на Аннином Архиве Для получения информации о Sci-Hub, пожалуйста, обратитесь к его <a %(a_scihub)s>официальному сайту</a>, <a %(a_wikipedia)s>странице в Википедии</a> и этому <a %(a_radiolab)s>подкаст-интервью</a>. Обратите внимание, что Sci-Hub был <a %(a_reddit)s>заморожен с 2021 года</a>. Он был заморожен и ранее, но в 2021 году было добавлено несколько миллионов статей. Тем не менее, некоторое ограниченное количество статей добавляется в коллекции Libgen “scimag”, хотя этого недостаточно для создания новых массовых торрентов. Мы используем метаданные Sci-Hub, предоставленные <a %(a_libgen_li)s>Libgen.li</a> в его коллекции “scimag”. Мы также используем набор данных <a %(a_dois)s>dois-2022-02-12.7z</a>. Обратите внимание, что торренты “smarch” <a %(a_smarch)s>устарели</a> и поэтому не включены в наш список торрентов. Торренты на Libgen.li Торренты на Libgen.rs Метаданные и торренты Обновления на Reddit Подкаст-интервью Страница в Википедии Sci-Hub Sci-Hub: заморожен с 2021 года; большая часть доступно через торренты Libgen.li: незначительные дополнения с тех пор</div> Некоторые исходные библиотеки продвигают массовый обмен своими данными через торренты, в то время как другие неохотно делятся своей коллекцией. В последнем случае Архив Анны пытается скопировать их коллекции и сделать их доступными (см. наши <a %(a_torrents)s>Торренты</a>). Существуют также промежуточные ситуации, например, когда исходные библиотеки готовы делиться, но у них нет ресурсов для этого. В таких случаях мы также стараемся помочь. Ниже приведен обзор того, как мы взаимодействуем с различными исходными библиотеками. Исходные библиотеки %(icon)s Различные базы данных файлов, разбросанные по китайскому интернету; часто платные базы данных. %(icon)s Большинство файлов доступны только с премиум-аккаунтами BaiduYun; медленные скорости загрузки. %(icon)s Архив Анны управляет коллекцией <a %(duxiu)s>файлов DuXiu</a> %(icon)s Различные базы данных метаданных, разбросанные по китайскому интернету; часто платные базы данных. %(icon)s Нет легкодоступных дампов метаданных для всей их коллекции. %(icon)s Архив Анны управляет коллекцией <a %(duxiu)s>метаданных DuXiu</a> Файлы %(icon)s Файлы доступны для заимствования только на ограниченной основе, с различными ограничениями доступа %(icon)s Архив Анны управляет коллекцией <a %(ia)s>IA файлов</a> %(icon)s Некоторые метаданные доступны через <a %(openlib)s>дампы базы данных Open Library</a>, но они не охватывают всю коллекцию IA %(icon)s Нет легко доступных дампов метаданных для всей их коллекции %(icon)s Архив Анны управляет коллекцией <a %(ia)s>метаданных IA</a> Последнее обновление %(icon)s Архив Анны и Libgen.li совместно управляют коллекциями <a %(comics)s>комиксов</a>, <a %(magazines)s>журналов</a>, <a %(standarts)s>стандартных документов</a> и <a %(fiction)s>художественной литературы (отделенной от Libgen.rs)</a>. %(icon)s Торренты научно-популярной литературы делятся с Libgen.rs (и зеркалируются <a %(libgenli)s>здесь</a>). %(icon)s Ежеквартальные <a %(dbdumps)s>дампы базы данных HTTP</a> %(icon)s Автоматизированные торренты для <a %(nonfiction)s>Нон-фикшн</a> и <a %(fiction)s>Фикшн</a> %(icon)s Архив Анны управляет коллекцией <a %(covers)s>торрентов обложек книг</a> %(icon)s Ежедневные <a %(dbdumps)s>дампы базы данных HTTP</a> Метаданные %(icon)s Ежемесячные <a %(dbdumps)s>дампы базы данных</a> %(icon)s Торренты данных доступны <a %(scihub1)s>здесь</a>, <a %(scihub2)s>здесь</a> и <a %(libgenli)s>здесь</a> %(icon)s Некоторые новые файлы <a %(libgenrs)s>добавляются</a> в “scimag” Libgen, но их недостаточно для создания новых торрентов %(icon)s Sci-Hub не добавляет новые файлы с 2021 года. %(icon)s Дамп метаданных доступен <a %(scihub1)s>здесь</a> и <a %(scihub2)s>здесь</a>, а также как часть <a %(libgenli)s>базы данных Libgen.li</a> (которую мы используем) Источник %(icon)s Различные мелкие или одноразовые источники. Мы призываем людей сначала загружать файлы в другие теневые библиотеки, но иногда у людей есть коллекции, которые слишком велики, чтобы другие могли их отсортировать, но недостаточно велики, чтобы заслужить собственную категорию. %(icon)s Недоступно напрямую в большом объеме, защищено от скрейпинга %(icon)s Архив Анны управляет коллекцией <a %(worldcat)s>метаданных OCLC (WorldCat)</a> %(icon)s Архив Анны и Z-Library совместно управляют коллекцией <a %(metadata)s>метаданных Z-Library</a> и <a %(files)s>файлов Z-Library</a> Наборы данных Мы объединяем все вышеперечисленные источники в единую базу данных, которую используем для обслуживания этого веб-сайта. Эта единая база данных недоступна напрямую, но поскольку Архив Анны полностью с открытым исходным кодом, её можно довольно легко <a %(a_generated)s>сгенерировать</a> или <a %(a_downloaded)s>скачать</a> в виде баз данных ElasticSearch и MariaDB. Скрипты на этой странице автоматически загрузят все необходимые метаданные из упомянутых выше источников. Если вы хотите изучить наши данные перед запуском этих скриптов локально, вы можете посмотреть наши JSON-файлы, которые ссылаются на другие JSON-файлы. <a %(a_json)s>Этот файл</a> является хорошей отправной точкой. Единая база данных Торренты от Анны Архив просмотр поиск Различные мелкие или одноразовые источники. Мы призываем людей сначала загружать файлы в другие теневые библиотеки, но иногда у людей есть коллекции, которые слишком велики, чтобы другие могли их отсортировать, но недостаточно велики, чтобы заслужить собственную категорию. Обзор со страницы <a %(a1)s>Datasets</a>. Из <a %(a_href)s>aaaaarg.fail</a>. Кажется, довольно полная. От нашего волонтера «cgiym». Из торрента <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Имеет довольно высокое совпадение с существующими коллекциями статей, но очень мало совпадений MD5, поэтому мы решили сохранить его полностью. Скрейп <q>iRead eBooks</q> (фонически <q>ai rit i-books</q>; airitibooks.com), выполненный волонтером <q>j</q>. Соответствует metadata <q>airitibooks</q> в <a %(a1)s><q>Другие скрейпы metadata</q></a>. Из коллекции <a %(a1)s><q>Библиотека Александрина</q></a>. Частично из оригинального источника, частично с the-eye.eu, частично с других зеркал. С частного сайта торрентов книг, <a %(a_href)s>Bibliotik</a> (часто упоминается как «Bib»), книги которого были объединены в торренты по имени (A.torrent, B.torrent) и распространялись через the-eye.eu. От нашего волонтера «bpb9v». Для получения дополнительной информации о <a %(a_href)s>CADAL</a> см. заметки на нашей <a %(a_duxiu)s>странице набора данных DuXiu</a>. Еще от нашего волонтера «bpb9v», в основном файлы DuXiu, а также папка «WenQu» и «SuperStar_Journals» (SuperStar — компания, стоящая за DuXiu). От нашего волонтера «cgiym», китайские тексты из различных источников (представлены как подкаталоги), включая <a %(a_href)s>China Machine Press</a> (крупное китайское издательство). Некитайские коллекции (представлены как подкаталоги) от нашего волонтера «cgiym». Скрейп книг о китайской архитектуре, выполненный волонтером <q>cm</q>: <q>Я получил это, воспользовавшись уязвимостью сети в издательстве, но эта лазейка с тех пор была закрыта</q>. Соответствует metadata <q>chinese_architecture</q> в <a %(a1)s><q>Другие скрейпы metadata</q></a>. Книги от академического издательства <a %(a_href)s>De Gruyter</a>, собранные из нескольких крупных торрентов. Скрейп <a %(a_href)s>docer.pl</a>, польского сайта обмена файлами, ориентированного на книги и другие письменные работы. Скрейпинг был выполнен в конце 2023 года волонтером «p». У нас нет хороших метаданных с оригинального сайта (даже расширений файлов), но мы отфильтровали файлы, похожие на книги, и часто могли извлечь метаданные из самих файлов. DuXiu epubs, напрямую из DuXiu, собранные волонтером «w». Только последние книги DuXiu доступны напрямую через электронные книги, поэтому большинство из них должны быть недавними. Оставшиеся файлы DuXiu от волонтера «m», которые не были в проприетарном формате PDG DuXiu (основной <a %(a_href)s>набор данных DuXiu</a>). Собраны из многих оригинальных источников, к сожалению, без сохранения этих источников в пути к файлу. <span></span> <span></span> <span></span> Скрейп эротических книг, выполненный волонтером <q>do no harm</q>. Соответствует metadata <q>hentai</q> в <a %(a1)s><q>Другие скрейпы metadata</q></a>. <span></span> <span></span> Коллекция, скрейпнутая у японского издателя манги волонтером «t». <a %(a_href)s>Выбранные судебные архивы Лунцюань</a>, предоставленные волонтером «c». Скрейп <a %(a_href)s>magzdb.org</a>, союзника Library Genesis (он указан на главной странице libgen.rs), но который не хотел предоставлять свои файлы напрямую. Получено волонтером «p» в конце 2023 года. <span></span> Различные мелкие загрузки, слишком маленькие для собственной подколлекции, но представленные как директории. Электронные книги с AvaxHome, российского сайта для обмена файлами. Архив газет и журналов. Соответствует metadata <q>newsarch_magz</q> в <a %(a1)s><q>Другие скрейпы metadata</q></a>. Скрейп <a %(a1)s>Philosophy Documentation Center</a>. Коллекция волонтера «o», который собирал польские книги непосредственно с оригинальных сайтов релизов («сцена»). Объединенные коллекции <a %(a_href)s>shuge.org</a> волонтерами «cgiym» и «woz9ts». <span></span> <a %(a_href)s>«Имперская библиотека Трантора»</a> (названа в честь вымышленной библиотеки), скрейпнута в 2022 году волонтером «t». <span></span> <span></span> <span></span> Подколлекции (представленные как каталоги) от волонтера «woz9ts»: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (от <a %(a_sikuquanshu)s>Dizhi(迪志)</a> на Тайване), mebook (mebook.cc, 我的书屋, моя маленькая библиотека — woz9ts: «Этот сайт в основном сосредоточен на обмене высококачественными файлами электронных книг, некоторые из которых верстает сам владелец. Владелец был <a %(a_arrested)s>арестован</a> в 2019 году, и кто-то собрал коллекцию файлов, которыми он делился.»). Оставшиеся файлы DuXiu от волонтера «woz9ts», которые не были в проприетарном формате PDG DuXiu (еще предстоит конвертировать в PDF). Коллекция «upload» разделена на более мелкие подколлекции, которые указаны в AACIDs и названиях торрентов. Все подколлекции сначала были дедуплицированы по сравнению с основной коллекцией, хотя JSON-файлы метаданных «upload_records» все еще содержат много ссылок на оригинальные файлы. Небуквенные файлы также были удалены из большинства подколлекций и обычно <em>не</em> отмечены в JSON «upload_records». Подколлекции включают: Заметки Подколлекция Многие подколлекции сами состоят из под-подколлекций (например, из разных оригинальных источников), которые представлены как директории в полях «filepath». Загрузки в Архив Анны Наш блог-пост об этих данных <a %(a_worldcat)s>WorldCat</a> — это проприетарная база данных некоммерческой организации <a %(a_oclc)s>OCLC</a>, которая агрегирует записи метаданных из библиотек по всему миру. Вероятно, это крупнейшая коллекция библиотечных метаданных в мире. В октябре 2023 года мы <a %(a_scrape)s>выпустили</a> комплексный скрапинг базы данных OCLC (WorldCat) в <a %(a_aac)s>формате контейнеров Анниного Архива</a>. Октябрь 2023, первоначальный выпуск: OCLC (WorldCat) Торренты от Анниного Архива Пример записи на Анны Архив (оригинальная коллекция) Пример записи на Анны Архив (коллекция «zlib3») Торренты от Анны Архив (метаданные + контент) Пост в блоге о Релизе 1 Пост в блоге о Релизе 2 В конце 2022 года предполагаемые основатели Z-Library были арестованы, а домены были конфискованы властями США. С тех пор сайт медленно возвращается в онлайн. Неизвестно, кто в настоящее время им управляет. Обновление на февраль 2023 года. Z-Library берет свои корни в сообществе <a %(a_href)s>Library Genesis</a> и изначально использовал их данные. С тех пор он значительно профессионализировался и имеет гораздо более современный интерфейс. Поэтому они могут получать гораздо больше пожертвований, как денежных для дальнейшего улучшения своего сайта, так и пожертвований новых книг. Они собрали большую коллекцию в дополнение к Library Genesis. Коллекция состоит из трех частей. Оригинальные страницы описания для первых двух частей сохранены ниже. Вам нужны все три части, чтобы получить все данные (за исключением устаревших торрентов, которые вычеркнуты на странице торрентов). %(title)s: наш первый релиз. Это был самый первый релиз того, что тогда называлось «Pirate Library Mirror» («pilimi»). %(title)s: второй релиз, на этот раз со всеми файлами, упакованными в .tar файлы. %(title)s: инкрементальные новые релизы, использующие <a %(a_href)s>формат контейнеров Анны Архив (AAC)</a>, теперь выпущенные в сотрудничестве с командой Z-Library. Первое зеркало было тщательно получено в течение 2021 и 2022 годов. На данный момент оно немного устарело: оно отражает состояние коллекции на июнь 2021 года. Мы обновим его в будущем. Сейчас мы сосредоточены на выпуске этой первой версии. Поскольку Library Genesis уже сохранен с помощью публичных торрентов и включен в Z-Library, мы провели базовую дедупликацию по сравнению с Library Genesis в июне 2022 года. Для этого мы использовали хэши MD5. Вероятно, в библиотеке все еще много дублирующегося контента, например, несколько форматов файлов с одной и той же книгой. Это сложно точно обнаружить, поэтому мы этого не делаем. После дедупликации у нас осталось более 2 миллионов файлов, общим объемом чуть менее 7 ТБ. Коллекция состоит из двух частей: дампа метаданных MySQL в формате «.sql.gz» и 72 торрент-файлов объемом около 50-100 ГБ каждый. Метаданные содержат данные, предоставленные сайтом Z-Library (название, автор, описание, тип файла), а также фактический размер файла и md5sum, которые мы наблюдали, так как иногда они не совпадают. Кажется, что есть диапазоны файлов, для которых сам Z-Library имеет некорректные метаданные. В некоторых отдельных случаях мы могли неправильно скачать файлы, что мы постараемся обнаружить и исправить в будущем. Большие торрент-файлы содержат фактические данные книг, с идентификатором Z-Library в качестве имени файла. Расширения файлов можно восстановить с помощью дампа метаданных. Коллекция представляет собой смесь научной и художественной литературы (не разделена, как в Library Genesis). Качество также сильно варьируется. Эта первая версия теперь полностью доступна. Обратите внимание, что торрент-файлы доступны только через наше зеркало в Tor. Релиз 1 (%(date)s) Это один дополнительный торрент-файл. Он не содержит новой информации, но в нем есть данные, которые могут занять некоторое время для вычисления. Это делает его удобным, так как загрузка этого торрента часто быстрее, чем вычисление с нуля. В частности, он содержит индексы SQLite для tar-файлов, для использования с <a %(a_href)s>ratarmount</a>. Дополнение к выпуску 2 (%(date)s) Мы получили все книги, добавленные в Z-Library между нашим последним зеркалом и августом 2022 года. Мы также вернулись и собрали некоторые книги, которые пропустили в первый раз. В целом, эта новая коллекция составляет около 24 ТБ. Опять же, эта коллекция дедуплицирована по сравнению с Library Genesis, так как для этой коллекции уже доступны торренты. Данные организованы аналогично первой версии. Существует дамп метаданных MySQL в формате «.sql.gz», который также включает все метаданные из первой версии, тем самым заменяя её. Мы также добавили несколько новых столбцов: Мы упоминали об этом в прошлый раз, но для ясности: «filename» и «md5» — это фактические свойства файла, тогда как «filename_reported» и «md5_reported» — это то, что мы собрали из Z-Library. Иногда эти два значения не совпадают, поэтому мы включили оба. Для этого выпуска мы изменили сопоставление на «utf8mb4_unicode_ci», что должно быть совместимо с более старыми версиями MySQL. Файлы данных аналогичны прошлому разу, хотя они намного больше. Мы просто не могли утруждать себя созданием множества меньших торрент-файлов. «pilimi-zlib2-0-14679999-extra.torrent» содержит все файлы, которые мы пропустили в последнем выпуске, в то время как другие торренты — это все новые диапазоны ID.  <strong>Обновление %(date)s:</strong> Мы сделали большинство наших торрентов слишком большими, из-за чего торрент-клиенты испытывали трудности. Мы удалили их и выпустили новые торренты. <strong>Обновление %(date)s:</strong> Файлов все еще было слишком много, поэтому мы упаковали их в tar-файлы и снова выпустили новые торренты. %(key)s: содержит ли этот файл уже Library Genesis, в коллекции научной или художественной литературы (сопоставлено по md5). %(key)s: в каком торренте находится этот файл. %(key)s: установлено, когда мы не смогли скачать книгу. Выпуск 2 (%(date)s) Релизы Zlib (оригинальные страницы описания) Tor-домен Основной сайт Скрейп Z-Library «Китайская» коллекция Z-Library, по-видимому, совпадает с нашей коллекцией DuXiu, но с разными MD5. Мы исключаем эти файлы из торрентов, чтобы избежать дублирования, но все равно показываем их в нашем поисковом индексе. Метаданные Вы получили %(percentage)s бонусных быстрых загрузок, потому что стали рефералом %(profile_link)s. Это применяется ко всему периоду членства. Пожертвовать Присоединиться Выбрано скидка до %(percentage)s%% Alipay поддерживает международные кредитные/дебетовые карты. См. <a %(a_alipay)s>это руководство</a> для получения дополнительной информации. Отправьте нам подарочные карты Amazon.com, используя свою кредитную/дебетовую карту. Вы можете купить криптовалюту, используя кредитную/дебетовую карту. WeChat (Weixin Pay) поддерживает международные кредитные/дебетовые карты. В приложении WeChat, перейдите в: “Me → Services → Wallet → Add a Card”. Если вы не видите этот пункт, то включите его: “Me → Settings → General → Tools → Weixin Pay → Enable”. (используйте при отправке Ethereum из Coinbase) скопировано! копировать (наименьшая минимальная сумма) (внимание: наивысшая минимальная сумма) -%(percentage)s%% 12 месяцев 1 месяц 24 месяца 3 месяца 48 месяцев 6 месяцев 96 месяцев Выберите, на какой период вы хотите подписаться. <div %(div_monthly_cost)s></div><div %(div_after)s>после <span %(span_discount)s></span> скидок</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s на 12 месяцев на 1 месяц на 24 месяца на 3 месяца на 48 месяцев на 6 месяцев на 96 месяцев %(monthly_cost)s / месяц свяжитесь с нами <strong>SFTP</strong>-серверы прямого доступа Пожертвование уровня Enterprise или обмен на новые коллекции (напр. новые сканы, распознанные наборы данных). Экспертный доступ <strong>Неограниченный</strong> скоростной доступ <div %(div_question)s>Могу ли я улучшить свой уровень участника или получить несколько членств?</div> <div %(div_question)s>Могу ли я сделать пожертвование, не становясь участником?</div> Конечно. Мы принимаем пожертвования любого размера на этот адрес Monero (XMR): %(address)s. <div %(div_question)s>Что означают диапазоны в месяц?</div> Вы можете достичь нижней границы диапазона, применив все скидки, например, выбрав период дольше месяца. <div %(div_question)s>Обновляются ли пожертвования автоматически?</div> Уровни участников <strong>НЕ</strong> обновляются автоматически. Будьте с нами так долго или недолго, как хотите. <div %(div_question)s>На что тратятся пожертвования?</div> 100%% тратится на сохранение и доступность мировых знаний и культуры. Пока мы тратим их в основном на серверы, хранилища и скорость доступа. Лично членам команды эти деньги не идут. <div %(div_question)s>Могу ли я сделать крупное пожертвование?</div> Это было бы потрясающе! Для пожертвований на сумму более нескольких тысяч долларов, пожалуйста, свяжитесь с нами напрямую по адресу %(email)s. <div %(div_question)s>Есть ли у Вас другие способы оплаты?</div> В настоящее время нет. Многие люди не хотят, чтобы подобные архивы существовали, поэтому мы должны быть осторожны. Если Вы можете помочь нам безопасно настроить другие (более удобные) способы оплаты, пожалуйста, свяжитесь с нами по адресу %(email)s. ЧаВО о пожертвованиях У вас есть <a %(a_donation)s>существующее пожертвование</a> в процессе. Пожалуйста, завершите или отмените это пожертвование, прежде чем делать новое пожертвование. <a %(a_all_donations)s>Посмотреть мои пожертвования</a> Для пожертвований выше $5,000 свяжитесь, пожалуйста, с нами напрямую: %(email)s. Мы приветствуем крупные пожертвования от состоятельных людей или организаций.  Имейте в виду, что хотя членства на этой странице указаны как «в месяц», они являются разовыми пожертвованиями (не повторяющимися). См. <a %(faq)s>Часто задаваемые вопросы о пожертвованиях</a>. Архив Анны — некоммерческий проект с открытым кодом и данными. Пожертвования и приобретение членства поддерживают нашу работу и развитие. Всем нашим членам: спасибо за вашу помощь! ❤️ Для получения дополнительной информации ознакомьтесь с <a %(a_donate)s>Часто задаваемыми вопросами о пожертвованиях</a>. Чтобы стать участником, пожалуйста, <a %(a_login)s>войдите или зарегистрируйтесь</a>. Благодарим за поддержку! $%(cost)s / в месяц Если вы совершили ошибку во время оплаты, мы не сможем вернуть деньги, но постараемся всё исправить. Найдите страницу "Криптовалюты" ("Crypto") в приложении или на сайте PayPal. Обычно она расположена в разделе "Финансы" ("Finances"). Перейдите на страницу "Bitcoin" в Вашем приложении или на сайте PayPal. Нажмите кнопку "Перевести" %(transfer_icon)s, а потом на "Отправить". Alipay Alipay / WeChat Подарочная Карта Amazon %(amazon)s подарочная карта Банковская карта Банковская карта (с использованием приложения) Binance Кредитная/дебетовая/Apple/Google (BMC) Cash App Кредитная/дебетовая карта Кредитная/дебетовая карта 2 Кредитная/дебетовая карта (резервный вариант) Криптовалюта %(bitcoin_icon)s Карта / PayPal / Venmo PayPal (США) %(bitcoin_icon)s PayPal PayPal (обычный) Pix (Бразилия) Revolut (временно недоступно) WeChat Выберите любимую криптовалюту: Пожертвование посредством подарочной карты Amazon. %(amazon)s%(amazon)s<strong>ВНИМАНИЕ:</strong> Этот вариант предназначен для %(amazon)s. Если вы хотите использовать другой сайт Amazon, выберите его выше. <strong>ВАЖНО:</strong> поддерживается только Amazon.com, а не другие веб-сайты Amazon. Например .de, .co.uk, .ca НЕ поддерживаются. Пожалуйста, НЕ меняйте сообщение на своё. Введите точную сумму: %(amount)s Заметим, что мы вынуждены округлять до сумм, принимаемых нашими реселлерами (минимум — %(minimum)s). Пожертвуйте с кредитной/дебетовой картой через приложение Alipay (его очень легко настроить). Установите приложение Alipay с <a %(a_app_store)s>Apple App Store</a> или <a %(a_play_store)s>Google Play Store</a>. Зарегистрируйтесь, используя свой номер телефона. Дополнительные личные данные не требуются. <span %(style)s>1</span>Установите приложение Alipay Поддерживаются: Visa, MasterCard, JCB, Diners Club и Discover. Для получения дополнительной информации обратитесь к этому руководству: <a %(a_alipay)s>. <span %(style)s>2</span>Добавьте банковскую карту С Binance вы можете купить биткойн с помощью кредитной/дебетовой карты или банковского счета, а затем пожертвовать этот биткойн нам. Таким образом, мы можем сохранять безопасность и анонимность, принимая ваше пожертвование. Binance доступен почти в каждой стране и поддерживает большинство банков и кредитных/дебетовых карт. В настоящее время это наша основная рекомендация. Мы ценим ваше время, потраченное на изучение этого метода пожертвования, так как это нам очень помогает. Для кредитных карт, дебетовых карт, Apple Pay и Google Pay мы используем «Buy Me a Coffee» (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). В их системе один «кофе» равен 5 $, поэтому ваше пожертвование будет округлено до ближайшего кратного 5. Задонатить через Cash App. Если у вас есть Cash App — это самый простой способ отправить пожертвование! Заметим, что при транзацкиях ниже %(amount)s Cash App может взимать комиссию в %(fee)s. Для %(amount)s или выше транзакции бесплатны! Сделать пожертвование, используя кредитную или дебетовую карту. Этот метод использует поставщика криптовалюты в качестве промежуточного конвертера. Это может быть немного запутанно, поэтому используйте этот метод только в том случае, если другие способы оплаты не работают. Этот метод также не работает во всех странах. Мы не можем принимать кредитные/дебетовые карты напрямую, потому что банки не хотят с нами работать. ☹ Однако, есть несколько способов использовать кредитные/дебетовые карты через другие платежные методы: Вы можете пожертвовать BTC, ETH, XMR и SOL. Используйте этот вариант, если вы уже имеете опыт с криптовалютами. Вы можете сделать пожертвование через криптовалюту, с помощью BTC, ETH, XMR и других. Крипто экспресс-услуги Если вы используете криптовалюту впервые, мы рекомендуем использовать %(options)s для покупки и пожертвования биткоинов (оригинальной и самой используемой криптовалюты). Заметим, что для малых пожертвований комиссии кредитной карты могут перекрыть нашу скидку в %(discount)s%%, поэтому мы рекомендуем более долгосрочные подписки. Сделайте пожертвование с помощью кредитной/дебетовой карты, PayPal или Venmo. Вы можете выбрать между ними на следующей странице. Google Pay и Apple Pay тоже работают. Заметим, что у малых пожертвований крупные комиссии, поэтому мы рекомендуем более долгосрочные подписки. Для пожертвований через PayPal US мы собираемся использовать PayPal Crypto, который позволяет нам оставаться анонимными. Мы благодарны вам за время, которое вы тратите, чтобы разобраться с этим способом оплаты - это нам очень помогает. Задонатить через PayPal. Пожертвуйте, используя ваш обычный аккаунт PayPal. Пожертвуйте с помощью Revolut. Если у вас есть Revolut — это самый простой способ сделать пожертвование! Этот метод оплаты позволяет внести максимум %(amount)s. Просим выбрать другую длительность либо другой метод. Этот метод оплаты требует как минимум %(amount)s. Просим выбрать другую длительность либо другой метод. Binance Coinbase Kraken Пожалуйста, выберите метод оплаты. «Усыновление торрента»: ваш ник или сообщение в имени файла торрента <div %(div_months)s>один раз каждые 12 месяцев членства</div> Ваш ник или анонимное упоминание в благодарностях Ранний доступ к новым функциям Эксклюзивный Telegram с внутренними обновлениями %(number)s быстрых загрузок в день если вы пожертвуете в этом месяце! Доступ к <a %(a_api)s>JSON API</a> Легендарный статус в сохранении знаний и культуры человечества Предыдущие привилегии, плюс: Заработайте <strong>%(percentage)s%% бонусных загрузок</strong>, <a %(a_refer)s>пригласив друзей</a>. Статьи SciDB <strong>без ограничений</strong> и без проверки При обращении с вопросами по аккаунту или пожертвованиям добавьте ID вашего аккаунта, скриншоты, квитанции и как можно больше информации. Мы проверяем нашу почту каждые 1–2 недели, поэтому отсутствие этой информации задержит решение. Чтобы получить ещё больше загрузок, <a %(a_refer)s>пригласите своих друзей</a>! Мы маленькая команда волонтёров. Ответ может занять от одной до двух недель. Обратите внимание, что имя учетной записи или картинка могут выглядеть странно. Не беспокойтесь! Этими учётными записями управляют наши партнеры, работающие с пожертвованиями. Наши учётные записи не были взломаны. Пожертвовать <span %(span_cost)s></span> <span %(span_label)s></span> на 12 месяцев “%(tier_name)s” на 1 месяц “%(tier_name)s” на 24 месяца “%(tier_name)s” на 3 месяца “%(tier_name)s” на 48 месяцев «%(tier_name)s» на 6 месяцев “%(tier_name)s” на 96 месяцев «%(tier_name)s» Вы всё ещё можете отменить пожертвование во время оплаты. Нажмите на кнопку пожертвования, чтобы подтвердить данное пожертвование. <strong>Это важно</strong>: цены на криптовалюты могут варьироваться порой до 20%% за пару минут. Это всё ещё меньше, чем комиссии множества операторов оплаты, которые часто взимают 50–60%% на том основании, что работают с такой «теневой благотворительностью», как наша. <u>Если отправите нам чек с указанием суммы, которую отдали, мы зачтём Вашей учётной записи выбранный уровень участника</u> (если чек не старше нескольких часов). Мы невероятно признательны Вам за то, что готовы мириться с подобными вещами во имя поддержки нашего дела! ❤️ ❌ Что-то пошло не так. Пожалуйста, обновите страницу, и попробуйте ещё раз. <span %(span_circle)s>1</span>Купить Bitcoin через PayPal <span %(span_circle)s>2</span>Переведите Bitcoin на наш адрес ✅ Перенаправление на страницу пожертвований… Пожертвовать Пожалуйста, подождите как минимум <span %(span_hours)s>24 часа</span> (и обновите эту страницу) перед тем, как связываться с нами. Если вы хотите сделать пожертвование (на любую сумму), не становясь участником, то можете использовать этот Monero (XMR) адрес: %(address)s. Когда отправите подарочную карту, наша автоматика подтвердит получение в пределах нескольких минут. Если не сработало, попробуйте отправить карту ещё раз (<a %(a_instr)s>инструкции здесь</a>). Если всё равно не срабатывает, просим написать нам по почте, и Anna вручную проверит всё (что может занять несколько дней); если пробовали отправить ещё раз, обязательно скажите об этом. Пример: Просим воспользоваться <a %(a_form)s>официальным бланком Amazon.com</a> для пересылки нам подарочной карты на %(amount)s по адресу электронной почты, приведённому ниже. Адрес эл. почты «To» («кому») в бланке: Подарочная карта Amazon Мы не можем принять другие методы через подарочные карты — <strong>только отправленные напрямую через официальный бланк Amazon.com</strong>. Если решите не пользоваться этой формой, мы также не сможем вернуть вам карту. Использовать только один раз. Уникально для вашей учётной записи, не показывайте никому. Ожидаем подарочную карту… (обновите страницу, чтобы проверить) Откройте <a %(a_href)s>страницу пожертвования с QR-кодом</a>. Отсканируйте QR-код приложением Alipay или нажмите кнопку, чтобы открыть приложение Alipay. Пожалуйста, будьте терпеливы; страница может загружаться некоторое время, так как она грузится из сервера в Китае. <span %(style)s>3</span>Сделайте пожертвование (отсканируйте QR-код или нажмите кнопку) Купить PYUSD coin через PayPal Купить Bitcoin (BTC) в Cash App Купите немного больше (мы рекомендуем больше на %(more)s), чем сумма вашего пожертвования (%(amount)s), чтобы покрыть комиссию за транзакцию. Остаток останется у вас. Перейдите на страницу «Bitcoin» (BTC) в Cash App. Переведите Bitcoin на наш адрес Для небольших пожертвований (менее $25) вам, возможно, придется использовать опции Rush или Priority. Нажмите кнопку «Отправить Bitcoin», чтобы сделать «вывод». Переключитесь с долларов на BTC, нажав на значок %(icon)s. Введите сумму BTC ниже и нажмите «Отправить». Если возникнут трудности, посмотрите <a %(help_video)s>это видео</a>. Экспресс-сервисы удобны, но взимают более высокие комиссии. Вы можете использовать это вместо криптообменника, если хотите быстро сделать более крупное пожертвование и не против комиссии в $5-10. Обязательно отправьте точную сумму криптовалюты, указанную на странице пожертвования, а не сумму в $USD. В противном случае, комиссия будет вычтена, и мы не сможем автоматически обработать ваше членство. Иногда подтверждение сможет занять до 24 часов, поэтому обязательно обновите эту страницу (даже если она устарела). Инструкции для кредитных/дебетовых карт Пожертвование посредством нашей страницы для кредитной/дебетовой карты Некоторые шаги упоминают криптокошельки, но не волнуйтесь — вам не потребуются никакие знания о крипто для этого. Инструкции для %(coin_name)s Сканируйте этот QR -код с вашим приложением Crypto Wallet, чтобы быстро заполнить платежные данные Сканировать QR -код для оплаты Мы поддерживаем только стандартные версии крипто монет, никаких экзотических сетей или версий монет. В зависимости от монеты, время на подтверждение транзакции может доходить до часа. Пожертвовать %(amount)s на <a %(a_page)s>этой странице</a>. Это пожертвование более не действительно. Пожалуйста, отмените его и создайте новое. Если вы уже оплатили: Да, я отправил(а) чек Если курс обмена криптовалюты совершил скачок во время транзакции, обязательно приложите чек с изначальным курсом обмена. Мы очень ценим, что ради нас вы проходите через семь кругов крипто — это нас сильно выручает! ❌ Что-то пошло не так. Просим перезагрузить страницу и попробовать снова. <span %(span_circle)s>%(circle_number)s</span>Отправьте нам чек электронной почтой В случае любых проблем просим связаться с нами по адресу %(email)s и предоставить как можно больше информации (например снимки экрана в процессе). ✅ Спасибо за пожертвование! Анна активирует Ваше членство вручную через пару дней. Отправьте чек или снимок экрана на ваш личный адрес подтверждения: Когда отправите чек, нажмите на эту кнопку, и Анна сможет проверить ситуацию вручную (что может занять несколько дней): Отправьте квитанцию или скриншот на Ваш личный проверочный адрес. НЕ ИСПОЛЬЗУЙТЕ тот же самый адрес электронной почты для пожертвований через PayPal. Отменить Да, отменяем Вы уверены, что хотите отменить? Не делайте этого, если уже провели оплату. ❌ Что-то пошло не так. Просим перезагрузить страницу и попробовать снова. Сделать новое пожертвование ✅ Ваше пожертвование отменено. Дата: %(date)s Идентификатор: %(id)s Повторить заказ Статус: <span %(span_label)s>%(label)s</span> Итого: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s в месяц в течение %(duration)s месяцев, с учётом скидки в %(discounts)s%%)</span> Итого: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s в месяц в течение %(duration)s месяцев)</span> 1. Введите свой адрес электронной почты. 2. Выберите свой метод оплаты. 3. Снова выберите свой метод оплаты. 4. Выберите «Self-hosted» кошелёк. 5. Нажмите «I confirm ownership» («Я подтверждаю, что являюсь собственником»). 6. На Вашу электронную почту должен будет прийти чек. Отправьте его нам, пожалуйста, и мы подтвердим пожертвование насколько возможно скоро. (возможно, настало время отменить пожертвование и создать новое) Эти платёжные инструкции устарели. Если хотите сделать ещё одно пожертвование нажмите кнопку "Повторить заказ" выше. Вы уже провели оплату. Если хотите посмотреть ещё раз на инструкции по оплате, нажмите сюда: Показать прежние инструкции по оплате Если страница для пожертвований будет заблокирована, попытайтесь открыть её через другие типы интернет-соединения (например, VPN или мобильный интернет). К сожалению, страница Alipay часто доступна только с <strong>материкового Китая</strong>. Возможно, вам придется временно отключить ваш VPN или использовать VPN для доступа к материковому Китаю (иногда также работает Гонконг). <span %(span_circle)s>1</span>Пожертвовать через Alipay Сделайте пожертвование на общую сумму в %(total)s с помощью <a %(a_account)s>этого аккаунта Alipay</a> Инструкции по Alipay <span %(span_circle)s>1</span>Сделайте перевод на один из наших криптосчетов Пожертвуйте общим счётом %(total)s на один из этих адресов: Касательно криптовалют Следуйте инструкциям, чтобы купить Bitcoin (BTC). Вам нужно купить не более желаемой суммы пожертвования, %(total)s. В качестве получателя укажите наш Bitcoin (BTC)-адрес и следуйте инструкциям, чтобы отправить пожертвование в %(total)s: <span %(span_circle)s>1</span>Пожертвуйте через Pix Пожертвование общим счётом в %(total)s посредством <a %(a_account)s>этой учётной записи Pix Инструкции по Pix <span %(span_circle)s>1</span>Пожертвовать через WeChat Пожертвуйте общую сумму в %(total)s с помощью <a %(a_account)s>этого аккаунта WeChat</a> Инструкции для WeChat Используйтесь любым из нижеуказанных экспресс-сервисов «Покупка Bitcoin кредитной картой», которые занимают всего несколько минут: Адрес BTC / Bitcoin (внешний кошелек): Сумма BTC / Bitcoin: Введите следующие данные в формуляр: Если какая-либо информация более не актуальна, пожалуйста, напишите нам по электронной почте, чтобы сообщить об этом. Пожалуйста, указывайте эту <span %(underline)s>точную сумму</span>. Ваша общая сумма может быть превышена комиссией Вашей кредитной карты за транзакции. К сожалению, в случаях небольших сумм, Ваша общая сумма может, в итоге, превысить нашу скидку. (минимум: %(minimum)s) (минимум: %(minimum)s) (минимум: %(minimum)s) (минимум: %(minimum)s, без проверки для первой транзакции) (минимум: %(minimum)s) (минимум: %(minimum)s в зависимости от страны, без проверки для первой транзакции) Следуйте инструкциям, чтобы купить PYUSD coin (PayPayl USD). Купите чуть больше (мы рекомендуем больше на %(more)s), чем на жертвуемую сумму (%(amount)s), чтобы покрыть комиссию за перевод. Если что-то останется, деньги будут в вашем распоряжении. В вашем приложении или на сайте PayPal пройдите на страницу «PYUSD». Нажмите кнопку «Перевести» %(icon)s, а затем «Отправить». Статус обновления Чтобы сбросить таймер, просто создайте новое пожертвование. Обязательно внесите указанную ниже сумму в BTC, <em>А НЕ</em> в евро или в долларах. Иначе мы не получим правильную сумму и не сможем автоматически подтвердить ваше членство. Купить Bitcoin (BTC) в Revolut Купите немного больше криптовалюты (мы рекомендуем %(more)s больше), чем на сумму вашего пожертвования (%(amount)s), чтобы осталось на покрытие платы за перевод. Неиспользованная валюта останется на Вашем счету. Перейдите на страницу «Crypto» в Revolut, чтобы купить Bitcoin (BTC). Переведите Bitcoin на наш адрес Для небольших пожертвований (менее $25) вам, возможно, придется использовать функции Rush («Ускоренно») или Priority («Первый класс»). Нажмите кнопку «Отправить Bitcoin», чтобы сделать «вывод». Переключитесь с евро на BTC, нажав на значок %(icon)s. Введите сумму BTC ниже и нажмите «Отправить». Если возникнут трудности, посмотрите <a %(help_video)s>это видео</a>. Статус: 1 2 Пошаговое руководство Следуйте пошаговому руководству ниже. В противном случае вы можете потерять доступ к аккаунту! Если вы ещё этого не сделали, напишите ниже свой секретный ключ для входа: Благодарим за Ваше пожертвование! Осталось: Пожертвование Перевести %(amount)s для %(account)s Ожидаем подтверждение (обновите страницу, чтобы проверить)… Ожидаем перевода (обновите страницу, чтобы проверить)… Ранее Быстрые загрузки за последние 24 часа засчитываются в суточный лимит. Загрузки с Быстрых Серверов Партнёров отмечены %(icon)s. Последние 18 часов Пока ничего не скачано. Список скачанных файлов не доступен публично. Всё время указано в UTC. Скачанные файлы Если вы загрузили один и тот же файл при помощи быстрой и медленной загрузки, он появится дважды. Не переживайте слишком сильно, многие люди скачивают с сайтов, на которые мы ссылаемся, и крайне редко возникают проблемы. Однако, чтобы оставаться в безопасности, мы рекомендуем использовать VPN (платный) или <a %(a_tor)s>Tor</a> (бесплатный). Я скачал «1984» Джорджа Оруэлла, придет ли ко мне полиция? Вы — Анна! Кто такая Анна? У нас есть стабильный JSON API для участников, позволяющий получить быстрый URL для загрузки: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (документация внутри самого JSON). Для других случаев использования, таких как перебор всех наших файлов, создание пользовательского поиска и так далее, мы рекомендуем <a %(a_generate)s>генерировать</a> или <a %(a_download)s>загружать</a> наши базы данных ElasticSearch и MariaDB. Сырые данные можно вручную исследовать <a %(a_explore)s>через JSON файлы</a>. Наш список торрентов можно также скачать в формате <a %(a_torrents)s>JSON</a>. У вас есть API? Мы не размещаем здесь никаких материалов, защищенных авторским правом. Мы являемся поисковой системой и, как таковые, индексируем только метаданные, которые уже доступны публично. При загрузке с этих внешних источников мы рекомендуем проверить законы в вашей юрисдикции относительно того, что разрешено. Мы не несем ответственности за контент, размещенный другими. Если у вас есть жалобы на то, что вы видите здесь, ваш лучший вариант — связаться с оригинальным сайтом. Мы регулярно обновляем их изменения в нашей базе данных. Если вы действительно считаете, что у вас есть обоснованная жалоба по DMCA, на которую мы должны ответить, пожалуйста, заполните <a %(a_copyright)s>форму жалобы DMCA / заявления о нарушении авторских прав</a>. Мы серьёзно относимся к вашим жалобам и свяжемся с вами как можно скорее. Как сообщить о нарушении авторских прав? Вот несколько книг, которые имеют особое значение для мира теневых библиотек и цифрового сохранения: Какие ваши любимые книги? Мы также хотели бы напомнить всем, что весь наш код и данные полностью открыты. Это уникально для проектов, подобных нашему — мы не знаем ни одного другого проекта с таким же огромным каталогом, который также полностью открыт. Мы приветствуем всех, кто считает, что мы плохо управляем нашим проектом, ведь каждый может взять наш код и данные и создать свою собственную теневую библиотеку! Мы не говорим это из злобы или чего-то подобного — мы действительно думаем, что это было бы здорово, так как это повысило бы планку для всех и лучше сохранило бы наследие человечества. Мне не нравится, как вы ведёте этот проект! Мы были бы рады созданию <a %(a_mirrors)s>зеркал</a> волонтёрами и готовы это финансово поддержать. Как я могу помочь? Да, это так. Наше вдохновение для сбора метаданных — цель Аарона Шварца создать «одну веб-страницу для каждой когда-либо опубликованной книги», для чего он создал <a %(a_openlib)s>Open Library</a>. Этот проект добился успеха, но наше уникальное положение позволяет нам получать метаданные, которые они не могут. Еще одним вдохновением было наше желание узнать <a %(a_blog)s>сколько книг существует в мире</a>, чтобы мы могли подсчитать, сколько книг нам еще предстоит спасти. Вы собираете метаданные? Обратите внимание, что mhut.org блокирует определенные диапазоны IP-адресов, поэтому может потребоваться VPN. <strong>Android:</strong> Нажмите на меню с тремя точками в правом верхнем углу и выберите «Добавить на главный экран». <strong>iOS:</strong> Нажмите кнопку «Поделиться» внизу и выберите «Добавить на главный экран». У нас нет официального мобильного приложения, но вы можете установить этот сайт как приложение. У вас есть мобильное приложение? Пожалуйста, отправьте их в <a %(a_archive)s>Internet Archive</a>. Они правильно сохранят их. Как я могу пожертвовать книги или другие физические материалы? Как мне запросить новые книги? <a %(a_blog)s>Блог Анны</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — регулярные обновления <a %(a_software)s>Программное обеспечение Анны</a> — наш открытый исходный код <a %(a_datasets)s>Наборы данных</a> — о данных <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтернативные домены Есть ли больше ресурсов об Архиве Анны? <a %(a_translate)s>Перевод на Программном обеспечении Анны</a> — наша система перевода <a %(a_wikipedia)s>Википедия</a> — больше о нас (пожалуйста, помогите поддерживать эту страницу в актуальном состоянии или создайте её для вашего языка!) Выберите настройки, которые вам нравятся, оставьте поле поиска пустым, нажмите «Поиск», а затем добавьте страницу в закладки с помощью функции закладок вашего браузера. Как сохранить мои настройки поиска? Мы приветствуем исследователей в области безопасности, ищущих уязвимости в наших системах. Мы большие сторонники ответственного раскрытия информации. Свяжитесь с нами <a %(a_contact)s>здесь</a>. В настоящее время мы не можем выплачивать вознаграждения за обнаружение ошибок, за исключением уязвимостей, имеющих <a %(a_link)s>потенциал для компрометации нашей анонимности</a>, за которые мы предлагаем вознаграждения в диапазоне 10–50 тыс. долларов. Мы хотели бы предложить более широкий охват для вознаграждений за обнаружение ошибок в будущем! Обратите внимание, что атаки социальной инженерии не входят в сферу охвата. Если вы интересуетесь наступательной безопасностью и хотите помочь архивировать мировые знания и культуру, обязательно свяжитесь с нами. Есть много способов, как вы можете помочь. У вас есть программа ответственного раскрытия информации? У нас буквально нет достаточного количества ресурсов, чтобы предоставить всем в мире высокоскоростные загрузки, как бы нам этого ни хотелось. Если бы нашелся богатый меценат, который бы обеспечил это для нас, это было бы невероятно, но до тех пор мы стараемся изо всех сил. Мы некоммерческий проект, который едва поддерживает себя за счет пожертвований. Именно поэтому мы внедрили две системы для бесплатных загрузок с нашими партнерами: общие серверы с медленными загрузками и немного более быстрые серверы с листом ожидания (чтобы уменьшить количество людей, загружающих одновременно). У нас также есть <a %(a_verification)s>проверка браузера</a> для наших медленных загрузок, потому что в противном случае боты и скраперы будут злоупотреблять ими, делая загрузки еще медленнее для легитимных пользователей. Обратите внимание, что при использовании Tor Browser вам, возможно, потребуется настроить параметры безопасности. На самом низком уровне, называемом «Стандартный», проверка Cloudflare turnstile проходит успешно. На более высоких уровнях, называемых «Безопаснее» и «Самый безопасный», проверка не проходит. Для больших файлов иногда медленные загрузки могут прерываться в середине. Мы рекомендуем использовать менеджер загрузок (например, JDownloader) для автоматического возобновления больших загрузок. Почему медленные загрузки такие медленные? Часто задаваемые вопросы (ЧаВо) Используйте <a %(a_list)s>генератор списка торрентов</a>, чтобы создать список торрентов, которые больше всего нуждаются в раздаче, в пределах вашего объема хранилища. Да, смотрите страницу <a %(a_llm)s>данных LLM</a>. Большинство торрентов содержат файлы напрямую, что означает, что вы можете указать торрент-клиентам загружать только нужные файлы. Чтобы определить, какие файлы загружать, вы можете <a %(a_generate)s>сгенерировать</a> наши метаданные или <a %(a_download)s>скачать</a> наши базы данных ElasticSearch и MariaDB. К сожалению, ряд коллекций торрентов в корне содержат файлы .zip или .tar, в этом случае вам нужно скачать весь торрент, прежде чем вы сможете выбрать отдельные файлы. Пока что нет простых в использовании инструментов для фильтрации торрентов, но мы приветствуем ваши предложения. (У нас есть <a %(a_ideas)s>некоторые идеи</a> для последнего случая.) Длинный ответ: Краткий ответ: не так просто. Мы стараемся минимизировать дублирование или пересечение между торрентами в этом списке, но это не всегда возможно и сильно зависит от политики исходных библиотек. Для библиотек, которые выпускают свои собственные торренты, это вне нашего контроля. Для торрентов, выпущенных Архивом Анны, мы устраняем дубликаты только на основе хэша MD5, что означает, что разные версии одной и той же книги не дублируются. Да. На самом деле это PDF и EPUB, они просто не имеют расширения во многих наших торрентах. Есть два места, где вы можете найти метаданные для торрент-файлов, включая типы/расширения файлов: 1. Каждая коллекция или релиз имеют свои метаданные. Например, <a %(a_libgen_nonfic)s>торренты Libgen.rs</a> имеют соответствующую базу данных метаданных, размещенную на сайте Libgen.rs. Мы обычно ссылаемся на соответствующие ресурсы метаданных со <a %(a_datasets)s>страницы набора данных</a> каждой коллекции. 2. Мы рекомендуем <a %(a_generate)s>генерировать</a> или <a %(a_download)s>скачивать</a> наши базы данных ElasticSearch и MariaDB. Они содержат сопоставление для каждой записи в Архиве Анны с соответствующими торрент-файлами (если доступны), под "torrent_paths" в JSON ElasticSearch. Некоторые торрент-клиенты не поддерживают большие размеры частей, которые есть у многих наших торрентов (для новых мы этого больше не делаем — хотя это и соответствует спецификациям!). Поэтому попробуйте другой клиент, если столкнетесь с этой проблемой, или пожалуйтесь разработчикам вашего торрент-клиента. Я бы хотел помочь с раздачей, но у меня мало места на диске. Торренты слишком медленные; могу ли я загрузить данные напрямую от вас? Могу ли я скачать только часть файлов, например, только на определённом языке или по определённой теме? Как вы обрабатываете дубликаты в торрентах? Могу ли я получить список торрентов в формате JSON? Я не вижу PDF или EPUB в торрентах, только бинарные файлы? Что мне делать? Почему мой торрент-клиент не может открыть некоторые из ваших торрент-файлов / magnet-ссылок? Часто задаваемые вопросы (ЧаВо) о торрентах Как мне загрузить новые книги? Пожалуйста, ознакомьтесь с <a %(a_href)s>этим отличным проектом</a>. У вас есть мониторинг времени безотказной работы? Что такое Архив Анны? Станьте членом, чтобы пользоваться быстрой загрузкой. Теперь мы поддерживаем подарочные карты Amazon, кредитные и дебетовые карты, криптовалюту, Alipay и WeChat. У вас закончились быстрые загрузки на сегодня. Доступ Почасовые загрузки за последние 30 дней. Среднее часовое значение: %(hourly)s. Среднее значение за день: %(daily)s. Мы работаем с партнёрами, чтобы сделать нашу коллекцию легкодоступной и бесплатной для каждого. Мы верим, что каждый человек имеет права на коллективную мудрость человечества. И <a %(a_search)s>не в ущерб авторам</a>. Наборы данных, используемые в Архиве Анны, полностью открыты и могут быть зеркалированы в массовом порядке с помощью торрентов. <a %(a_datasets)s>Подробнее…</a> Долгосрочный архив Полная датабаза Поиск Книги, газеты, журналы, комиксы, библиотечные записи, метаданные, … Весь наш <a %(a_code)s>исходный код </a> и <a %(a_datasets)s>данные</a> полностью открыты. <span %(span_anna)s>Архив Анны</span> — это некоммерческий проект с двумя основными целями: <li><strong>Хранение:</strong> Сохранять все знания и культуру человечества.</li><li><strong>Доступность:</strong> Делать все эти знания и культуру доступной для каждого.</li> Мы располагаем крупнейшей в мире коллекцией высококачественных текстовых данных. <a %(a_llm)s>Подробнее…</a> Данные для обучения LLM 🪩 Зеркала: поиск волонтёров Если вы пользуетесь анонимным платежным оператором с высоким уровнем риска, пожалуйста, свяжитесь с нами. Мы также ищем людей, заинтересованных в размещении небольших рекламных баннеров со вкусом. Все вырученные средства пойдут на наши цели по сохранению. Хранение По нашим оценкам, мы сохранили около <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% всех книг мира</a>. Мы сохраняем книги, статьи, комиксы, журналы и многое другое, собирая их в одном месте из различных <a href="https://ru.wikipedia.org/wiki/Теневая_библиотека">теневых библиотек</a>, официальных библиотек и других коллекций в одном месте. Все эти данные сохранены навсегда, за счёт создания возможности простого копирования в массовом порядке при помощи торрентов, что приводит к появлению множества копий по всему миру. Некоторые теневые библиотеки уже делают это самостоятельно (напр. Sci-Hub, Library Genesis), в то время, как Архив Анны «освобождает» другие библиотеки, которые не предоставляют массового распространения (напр. Z-Library) или не являются теневыми вовсе (напр. Internet Archive, DuXiu). Это широкое распространение, совмещённое с открытым исходным кодом, делает наш сайт устойчивым к разного рода падениям или блокировкам, и обеспечивает длительное сохранение знаний и культуры человечества. Узнайте больше о <a href="/datasets">наших наборах данных</a>. Если вы являетесь <a %(a_member)s>участником</a>, верификация браузера не требуется. 🧬&nbsp;SciDB — это идейное продолжение Sci-Hub. SciDB Открыть DOI Sci-Hub <a %(a_paused)s>приостановил</a> загрузку новых статей. Прямой доступ к %(count)s научным статьям 🧬&nbsp;SciDB является продолжением Sci-Hub, с его знакомым интерфейсом и прямым просмотром PDF. Введите ваш DOI для просмотра. У нас есть полная коллекция Sci-Hub, а также новые статьи. Большинство можно просмотреть напрямую через знакомый интерфейс, аналогичный Sci-Hub. Некоторые можно скачать через внешние источники, в этом случае мы показываем ссылки на них. Вы можете оказать огромную помощь, раздавая торренты. <a %(a_torrents)s>Подробнее…</a> >%(count)s сидеров <%(count)s сидеров %(count_min)s–%(count_max)s сидеров 🤝 Ищем волонтёров Как некоммерческий проект с открытым исходным кодом, мы всегда ищем людей, готовых помочь. Загрузки через IPFS Список создан %(by)s <span %(span_time)s>%(time)s</span> Сохранить ❌ Что-то пошло не так. Попробуйте снова. ✅ Изменения сохранены. Обновите страницу. Список пуст. изменить Добавить в этот список или удалить из него можно, найдя файл и открыв вкладку «Списки». Список Как мы можем помочь Удаление дублирования (дедупликация) Извлечение текста и метаданных Оптическое распознавание символов (OCR) Мы можем предоставить высокоскоростной доступ к нашим полным коллекциям, а также к неопубликованным коллекциям. Это доступ корпоративного уровня, который мы можем предоставить за пожертвования в размере нескольких десятков тысяч долларов США. Мы также готовы обменять это на высококачественные коллекции, которых у нас ещё нет. Мы можем вернуть вам деньги, если вы сможете предоставить нам обогащение наших данных, такое как: Поддержите долгосрочное архивирование человеческих знаний, получая при этом лучшие данные для вашей модели! <a %(a_contact)s>Свяжитесь с нами</a>, чтобы обсудить, как мы можем работать вместе. Хорошо известно, что LLM процветают на высококачественных данных. У нас самая большая коллекция книг, статей, журналов и т.д. в мире, которые являются одними из самых качественных текстовых источников. Данные LLM Уникальный масштаб и диапазон Наша коллекция содержит более ста миллионов файлов, включая научные журналы, учебники и журналы. Мы достигаем этого масштаба, объединяя существующие крупные репозитории. Некоторые из наших исходных коллекций уже доступны в большом объёме (Sci-Hub и части Libgen). Другие источники мы освободили сами. <a %(a_datasets)s>Наборы данных</a> показывают полную картину. Наша коллекция включает миллионы книг, статей и журналов из эпохи до электронных книг. Большие части этой коллекции уже прошли оптическое распознавание символов (OCR) и имеют минимальное внутреннее дублирование. Продолжить Если вы потеряли свой ключ, пожалуйста, <a %(a_contact)s>свяжитесь с нами</a> и предоставьте как можно больше информации. Вам может потребоваться создать временный аккаунт, чтобы связаться с нами. Пожалуйста, <a %(a_account)s>войдите</a>, чтобы просмотреть эту страницу.</a> Чтобы спам-боты не смогли создать множество аккаунтов, нам необходимо сначала проверить ваш браузер. Если вы застряли в бесконечном цикле, мы рекомендуем установить <a %(a_privacypass)s>Privacy Pass</a>. Также может помочь отключение блокировщиков рекламы и других расширений браузера. Войти / Регистрация Архив Анны временно недоступен из-за технического обслуживания. Пожалуйста, зайдите через час. Альтернативный автор Альтернативное описание Альтернативное издание Альтернативное расширение Альтернативное имя файла Альтернативный издатель Альтернативное название дата открытого доступа Подробнее… описание Искать в Архиве Анны по CADAL SSNO номеру Искать в Архиве Анны по DuXiu SSID номеру Искать в Архиве Анны по DuXiu DXID номеру Поиск в Архиве Анны по ISBN Поиск в Архиве Анны по номеру OCLC (WorldCat) Поиск в Архиве Анны по Open Library ID Онлайн-просмотрщик Архива Анны %(count)s затронутых страниц После загрузки: Более качественная версия файла может быть доступна на %(link)s Массовая загрузка торрентов коллекция Используйте онлайн-инструменты для конвертации между форматами. Рекомендуемые инструменты для конвертации: %(links)s Для больших файлов мы рекомендуем использовать менеджер загрузок, чтобы избежать прерываний. Рекомендуемые менеджеры загрузок: %(links)s Индекс электронных книг EBSCOhost (только для экспертов) (также нажмите "GET" вверху) (нажмите "GET" вверху) Внешние загрузки На сегодня у вас осталось %(remaining)s. Спасибо, что поддерживаете нас! ❤️ У вас закончились быстрые загрузки на сегодня. Вы недавно загружали этот файл. Ссылка останется действительной ещё на какое-то время. Станьте <a %(a_membership)s>участником</a>, чтобы поддержать долгосрочное сохранение книг, статей и многого другого. Чтобы выразить вам благодарность за поддержку, мы даём вам быстрые загрузки. ❤️ 🚀 Быстрые загрузки 🐢 Медленные загрузки Заём с Internet Archive IPFS-портал №%(num)d (вам может потребоваться несколько попыток для загрузки, используя протокол IPFS) Библиотека Genesis ".li-fork" Библиотека Genesis ".rs-fork" Художественная Литература Библиотека Genesis ".rs-fork" Документальная Литература их реклама известна наличием вредоносного ПО, поэтому используйте блокировщик рекламы или не кликайте на рекламу Amazon «Отправить на Kindle» djazz «Отправить на Kobo/Kindle» MagzDB ManualsLib Nexus/STC (Файлы Nexus/STC могут быть ненадежны для загрузки) Загрузки не найдены. Все зеркала содержат один и тот же файл и должны быть безопасными при использовании. Тем не менее всегда будьте осторожны при загрузке файлов из интернета. Например, обязательно обновляйте свои устройства. (без перенаправления) Открыть в нашем просмотрщике (открыть в просмотрщике) Опция #%(num)d: %(link)s %(extra)s Найти оригинальную запись в CADAL Искать на DuXiu вручную Найти оригинальную запись в ISBNdb Найти оригинальную запись в WorldCat Найти оригинальную запись в Open Library Поиск в разных других базах данных по ISBN (печать отключена, только для патронов) PubMed Вам понадобится программа для чтения электронных книг или PDF, в зависимости от формата файла. Рекомендуемые программы для чтения электронных книг: %(links)s Архив Анны 🧬 SciDB Sci-Hub: %(doi)s (соответствующий цифровой идентификатор объекта (DOI) может быть недоступен на Sci-Hub - интернет-ресурсе, предоставляющем автоматический и бесплатный доступ к полным текстам научных работ) Вы можете отправлять файлы PDF и EPUB на ваш Kindle или Kobo eReader. Рекомендуемые инструменты: %(links)s Дополнительная информация в <a %(a_slow)s>Часто задаваемых вопросах (ЧаВо)</a>. Поддержите авторов и библиотеки Если вам понравилось это произведение и вы можете себе это позволить, подумайте о покупке оригинала или поддержите авторов напрямую. Если это доступно в вашей местной библиотеке, подумайте о том, чтобы взять это бесплатно там. Загрузка с Сервера Партнёра временно недоступна для этого файла. торрент От доверенных партнёров. Z-Library Z-Библиотека в ТОРе (требуется ТОР браузер) показать внешние загрузки <span class="font-bold">❌ Этот файл может содержать ошибки и поэтому был скрыт из исходной библиотеки.</span>Иногда это происходит по просьбе правообладателя, иногда — потому что доступен лучший вариант, а иногда — из-за проблем с самим файлом. Возможно, файл ещё можно скачать, но мы рекомендуем сначала поискать другой файл. Дополнительная информация: Если Вы всё ещё хотите загрузить этот файл, обязательно используйте для его открытия только проверенное, обновлённое программное обеспечение. комментарии к метаданным АА: Поиск в Архиве Анны по «%(name)s» Исследователь кодов: Просмотреть в Исследователе кодов «%(name)s» URL: Веб-сайт: Если у вас есть этот файл и он ещё не доступен на Архиве Анны, вы можете <a %(a_request)s>загрузить его</a>. Файл с Контролируемой цифровой выдачей (CDL) из Internet Archive «%(id)s» Это запись файла из Internet Archive, а не напрямую загружаемый файл. Вы можете попытаться занять книгу (по ссылке ниже) или использовать другой URL-адрес, когда <a %(a_request)s>запрашиваете файл</a>. Улучшить метаданные Запись метаданных CADAL SSNO %(id)s Это запись метаданных, а не напрямую загружаемый файл. Вы можете использовать этот URL-адрес, когда <a %(a_request)s>запрашиваете книгу</a>. Запись метаданных DuXiu SSID %(id)s Запись метаданных ISBNdb %(id)s Запись метаданных MagzDB ID %(id)s Запись метаданных Nexus/STC ID %(id)s Запись метаданных номера OCLC (WorldCat) %(id)s Запись метаданных Open Library %(id)s Sci-Hub файл “%(id)s” Не найдено “%(md5_input)s” отсутствует в нашей базе данных. Добавить комментарий (%(count)s) Вы можете получить md5 из URL-адреса, например MD5 лучшей версии этого файла (если применимо). Заполните это, если есть другой файл, точно соответствующий этому (то же издание, то же расширение файла, если сможете найти), который люди должны использовать вместо этого файла. Если вы знаете лучшую версию этого файла за пределами Архива Анны, пожалуйста, <a %(a_upload)s>загрузите её</a>. Что-то пошло не так. Пожалуйста, перезагрузите страницу и попробуйте снова. Вы оставили комментарий. Может потребоваться минута, чтобы он отобразился. Пожалуйста, используйте <a %(a_copyright)s>форму DMCA / заявления о нарушении авторских прав</a>. Опишите проблему (обязательно) Если этот файл высокого качества, вы можете обсудить его здесь! Если нет, пожалуйста, используйте кнопку «Сообщить о проблеме с файлом». Отличное качество файла (%(count)s) Качество файла Узнайте, как <a %(a_metadata)s>улучшить метаданные</a> для этого файла самостоятельно. Описание проблемы Пожалуйста, <a %(a_login)s>войдите в систему</a>. Мне очень понравилась эта книга! Помогите сообществу, сообщив о качестве этого файла! 🙌 Что-то пошло не так. Пожалуйста, перезагрузите страницу и попробуйте снова. Сообщить о проблеме с файлом (%(count)s) Спасибо за отправку вашего отчета. Он будет показан на этой странице, а также проверен вручную Анной (пока у нас не будет надлежащей системы модерации). Оставить комментарий Отправить отчет Что не так с этим файлом? Займы (%(count)s) Комментарии (%(count)s) Загрузки (%(count)s) Просмотры метаданных (%(count)s) Списки (%(count)s) Статистика (%(count)s) Для получения информации об этом конкретном файле ознакомьтесь с его <a %(a_href)s>JSON-файлом</a>. Это файл, управляемый библиотекой <a %(a_ia)s>Контролируемой Цифровой Выдачи (CDL) IA</a> и индексируемый Архивом Анны для поиска. Для получения информации о различных наборах данных, которые мы собрали, см. <a %(a_datasets)s>страницу наборов данных</a>. Метаданные из связанной записи Улучшить метаданные на Open Library «MD5 файла» — это хеш, который вычисляется из содержимого файла и является достаточно уникальным на основе этого содержимого. Все теневые библиотеки, которые мы индексировали здесь, в первую очередь используют MD5 для идентификации файлов. Файл может появляться в нескольких теневых библиотеках. Для получения информации о различных наборах данных, которые мы собрали, см. <a %(a_datasets)s>страницу наборов данных</a>. Сообщить о качестве файла Всего загрузок: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Чешская метадата %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Книги %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} РГБ %(id)s} Трантор %(id)s} Предупреждение: несколько связанных записей: Когда вы смотрите на книгу в Архиве Анны, вы можете увидеть различные поля: название, автор, издатель, издание, год, описание, имя файла и многое другое. Вся эта информация называется <em>метаданными</em>. Поскольку мы объединяем книги из различных <em>исходных библиотек</em>, мы показываем любые доступные метаданные из этой исходной библиотеки. Например, для книги, которую мы получили из Library Genesis, мы покажем название из базы данных Library Genesis. Иногда книга присутствует в <em>нескольких</em> исходных библиотеках, которые могут иметь разные поля метаданных. В этом случае мы просто показываем самую длинную версию каждого поля, так как она, вероятно, содержит самую полезную информацию! Мы все равно показываем другие поля ниже описания, например, как «альтернативное название» (но только если они отличаются). Мы также извлекаем <em>коды</em>, такие как идентификаторы и классификаторы, из источниковой библиотеки. <em>Идентификаторы</em> уникально представляют конкретное издание книги; примеры включают ISBN, DOI, Open Library ID, Google Books ID или Amazon ID. <em>Классификаторы</em> группируют несколько похожих книг; примеры включают Десятичную классификацию Дьюи (DCC), УДК, LCC, RVK или ГОСТ. Иногда эти коды явно связаны в источниковых библиотеках, а иногда мы можем извлечь их из имени файла или описания (в основном ISBN и DOI). Мы можем использовать идентификаторы для поиска записей в <em>коллекциях только метаданных</em>, таких как OpenLibrary, ISBNdb или WorldCat/OCLC. В нашем поисковом движке есть специальная <em>вкладка метаданных</em>, если вы хотите просмотреть эти коллекции. Мы используем совпадающие записи для заполнения отсутствующих полей метаданных (например, если отсутствует название) или, например, как «альтернативное название» (если уже есть существующее название). Чтобы точно увидеть, откуда взялись метаданные книги, смотрите вкладку <em>«Технические детали»</em> на странице книги. Там есть ссылка на необработанный JSON для этой книги с указателями на необработанный JSON оригинальных записей. Для получения дополнительной информации смотрите следующие страницы: <a %(a_datasets)s>Наборы данных</a>, <a %(a_search_metadata)s>Поиск (вкладка метаданных)</a>, <a %(a_codes)s>Исследователь кодов</a> и <a %(a_example)s>Пример метаданных JSON</a>. Наконец, все наши метаданные могут быть <a %(a_generated)s>сгенерированы</a> или <a %(a_downloaded)s>загружены</a> в виде баз данных ElasticSearch и MariaDB. Фон Вы можете помочь в сохранении книг, улучшая метаданные! Сначала прочитайте информацию о метаданных в Архиве Анны, а затем узнайте, как улучшить метаданные через связь с Open Library, и получите бесплатное членство в Архиве Анны. Улучшить метаданные Итак, если вы наткнулись на файл с плохими метаданными, как его исправить? Вы можете перейти в источниковую библиотеку и следовать ее процедурам по исправлению метаданных, но что делать, если файл присутствует в нескольких источниковых библиотеках? Есть один идентификатор, который имеет особое значение в Аннином Архиве. <strong>Поле annas_archive md5 в Open Library всегда переопределяет все другие метаданные!</strong> Давайте сначала немного отступим и узнаем об Open Library. Open Library была основана в 2006 году Аароном Шварцем с целью «одна веб-страница для каждой когда-либо опубликованной книги». Это своего рода Википедия для метаданных книг: каждый может редактировать ее, она свободно лицензирована и может быть загружена в большом объеме. Это база данных книг, которая наиболее соответствует нашей миссии — на самом деле, Аннин Архив был вдохновлен видением и жизнью Аарона Шварца. Вместо того чтобы изобретать велосипед, мы решили направить наших волонтеров в Open Library. Если вы видите книгу с неправильными метаданными, вы можете помочь следующим образом: Обратите внимание, что это работает только для книг, а не для научных статей или других типов файлов. Для других типов файлов мы все же рекомендуем найти исходную библиотеку. Может потребоваться несколько недель, чтобы изменения были включены в Архив Анны, так как нам нужно загрузить последнюю выгрузку данных Open Library и заново сгенерировать наш поисковый индекс.  Перейдите на <a %(a_openlib)s>веб-сайт Open Library</a>. Найдите правильную запись книги. <strong>ПРЕДУПРЕЖДЕНИЕ:</strong> обязательно выберите правильное <strong>издание</strong>. В Open Library есть «произведения» и «издания». «Произведение» может быть «Гарри Поттер и философский камень». «Издание» может быть: Первое издание 1997 года, опубликованное издательством Bloomsbery, содержит 256 страниц. Издание в мягкой обложке 2003 года, опубликованное издательством Raincoast Books, содержит 223 страницы. Польский перевод 2000 года «Гарри Поттер и философский камень» от Media Rodzina содержит 328 страниц. Все эти издания имеют разные ISBN и разное содержание, поэтому обязательно выберите правильное! Отредактируйте запись (или создайте её, если она не существует) и добавьте как можно больше полезной информации! Вы уже здесь, так что сделайте запись действительно потрясающей. В разделе «ID Numbers» выберите «Архив Анны» и добавьте MD5 книги из Архива Анны. Это длинная строка букв и цифр после «/md5/» в URL. Попробуйте найти другие файлы в Архиве Анны, которые также соответствуют этой записи, и добавьте их. В будущем мы сможем сгруппировать их как дубликаты на странице поиска Архива Анны. Когда закончите, запишите URL, который вы только что обновили. Как только вы обновите как минимум 30 записей с MD5 из Архива Анны, отправьте нам <a %(a_contact)s>электронное письмо</a> и пришлите список. Мы предоставим вам бесплатное членство в Архиве Анны, чтобы вы могли легче выполнять эту работу (и в знак благодарности за вашу помощь). Эти правки должны быть качественными и содержать значительное количество информации, иначе ваш запрос будет отклонен. Ваш запрос также будет отклонен, если какие-либо правки будут отменены или исправлены модераторами Open Library. Связь с Open Library Если вы значительно вовлечетесь в разработку и эксплуатацию нашей работы, мы можем обсудить разделение большей части доходов от пожертвований с вами, чтобы вы могли использовать их по мере необходимости. Мы будем оплачивать хостинг только после того, как вы все настроите и продемонстрируете, что можете поддерживать архив в актуальном состоянии с обновлениями. Это означает, что вам придется оплатить первые 1-2 месяца из своего кармана. Ваше время не будет компенсировано (и наше тоже), так как это чисто волонтерская работа. Мы готовы покрыть расходы на хостинг и VPN, первоначально до $200 в месяц. Этого достаточно для базового поискового сервера и прокси, защищенного DMCA. Расходы на хостинг Пожалуйста, <strong>не связывайтесь с нами</strong> для получения разрешения или для базовых вопросов. Действия говорят громче слов! Вся информация доступна, так что просто приступайте к настройке вашего зеркала. Не стесняйтесь оставлять тикеты или запросы на слияние в нашем Gitlab, когда столкнетесь с проблемами. Нам, возможно, придется разработать некоторые функции, специфичные для зеркал, такие как ребрендинг с «Anna’s Archive» на название вашего сайта, (первоначально) отключение учетных записей пользователей или создание ссылок на наш основной сайт со страниц книг. Как только ваше зеркало будет работать, пожалуйста, свяжитесь с нами. Мы будем рады проверить вашу операционную безопасность, и как только она будет надежной, мы добавим ссылку на ваше зеркало и начнем работать с вами более тесно. Заранее благодарим всех, кто готов внести вклад таким образом! Это не для слабонервных, но это укрепит долговечность самой большой по-настоящему открытой библиотеки в истории человечества. Начало работы Чтобы повысить устойчивость Архива Анны, мы ищем волонтёров для запуска зеркал. Ваша версия четко обозначена как зеркало, например, «Архив Боба, зеркало Anna’s Archive». Вы готовы принять на себя риски, связанные с этой работой, которые значительны. Вы глубоко понимаете требования к операционной безопасности. Содержание <a %(a_shadow)s>этих</a> <a %(a_pirate)s>постов</a> очевидно для вас. Сначала мы не дадим вам доступ к загрузкам с серверов наших партнеров, но если все пойдет хорошо, мы можем поделиться этим с вами. Вы управляете открытым исходным кодом Anna’s Archive и регулярно обновляете как код, так и данные. Вы готовы внести вклад в наш <a %(a_codebase)s>код</a> — в сотрудничестве с нашей командой — чтобы это произошло. Мы ищем следующее: Зеркала: призыв к волонтёрам Пожертвовать ещё. Пока пожертвований нет. <a %(a_donate)s>Сделайте своё первое пожертвование.</a> Подробности о пожертвованиях не доступны публично. Мои пожертвования 📡 Для массового зеркального отображения нашей коллекции посмотрите страницы <a %(a_datasets)s>Наборов данных</a> и <a %(a_torrents)s>Торрентов</a>. Загрузок с вашего IP-адреса за последние 24 часа: %(count)s. 🚀 Чтобы ускорить загрузку и избежать проверок браузера, <a %(a_membership)s>станьте участником</a>. Скачать с веб-сайта партнёра Вы можете продолжить просмотр Архива Анны в другой вкладке, пока ждете (если ваш браузер поддерживает обновление фоновых вкладок). Вы можете ждать загрузки нескольких страниц одновременно (но, пожалуйста, загружайте только один файл за раз с каждого сервера). После получения ссылки на загрузку она будет действительна в течение нескольких часов. Спасибо за ожидание, это помогает сделать сайт доступным бесплатно для всех! 😊 🔗 Все ссылки на скачивание этого файла: <a %(a_main)s>Главная страница файла</a>. ❌ Медленные загрузки недоступны через Cloudflare VPN или иным образом с IP-адресов Cloudflare. ❌ Медленная загрузка доступна только через официальный сайт. Посетите %(websites)s. 📚 Для загрузки используйте следующий URL-адрес: <a %(a_download)s>Загрузить сейчас</a>. Чтобы дать всем возможность скачивать файлы бесплатно, вам нужно подождать, прежде чем вы сможете скачать этот файл. Пожалуйста, подождите <span %(span_countdown)s>%(wait_seconds)s</span> секунд, чтобы скачать этот файл. Предупреждение: за последние 24 часа с вашего IP-адреса было произведено много скачиваний. Скачивание может происходить медленнее, чем обычно. Если вы используете VPN, общую интернет-сеть или ваш интернет-провайдер делит IP-адреса, это предупреждение может быть связано с этим. Сохранить ❌ Что-то пошло не так. Попробуйте снова. ✅ Изменения сохранены. Обновите страницу. Смените ваше отображаемое имя. Ваш идентификатор (часть после «#») изменить нельзя. Профиль создан <span %(span_time)s>%(time)s</span> редактирование Списки Создайте новый список, найдя файл и открыв вкладку «Списки». Пока списков нет Профиль не найден. Профиль В настоящий момент мы не можем удовлетворять запросы на книги. Не отправляйте нам заявки на книги по электронной почте. Пожалуйста, сделайте запрос на форумах Z-Library или Libgen. Запись в Архиве Анны DOI: %(doi)s Скачать SciDB Nexus/STC Предварительный просмотр пока недоступен. Скачайте файл с <a %(a_path)s>Архива Анны</a>. Чтобы поддержать доступность и долгосрочное сохранение человеческих знаний, станьте <a %(a_donate)s>членом</a>. В качестве бонуса, 🧬&nbsp;SciDB загружается быстрее для членов, без каких-либо ограничений. Не работает? Попробуйте <a %(a_refresh)s>перезагрузить страницу</a>. Sci-Hub Добавить специальное поле для поиска Поиск по описанию или комментариям к метаданным Год издания Продвинутое Доступ Содержание Отобразить Список Таблица Тип файла Язык Сортировать по Наибольшой Наиболее актуальными Новейшие (размер файла) (открытый источник) (год публикации) Самый старый Случайный Наименьший Источник собрано и выложено в открытый доступ АА Цифровая выдача (%(count)s) (%(count)s) Журнальных Статей Мы нашли совпадения в: %(in)s. Вы можете ссылаться на найденный там URL-адрес когда <a %(a_request)s>запрашиваете файл</a>. Метаданные (%(count)s) Чтобы исследовать поисковый индекс по кодам, используйте <a %(a_href)s>Исследователь кодов</a>. Поисковый индекс обновляется ежемесячно. В настоящее время он включает в себя записи до %(last_data_refresh_date)s. Для получения более подробной технической информации перейдите на страницу %(link_open_tag)sнаборов данных</a>. Исключить Включить только Непроверено больше… Следующая … Предыдущая В настоящее время этот поисковый индекс включает метаданные из Контролируемой цифровой выдачи (CDL) Internet Archive. <a %(a_datasets)s>Больше о наших наборах данных</a>. О других электронных библиотеках смотрите <a %(a_wikipedia)s>Википедию</a> и <a %(a_mobileread)s>MobileRead Wiki</a>. Для DMCA / претензий по авторским правам <a %(a_copyright)s>нажмите здесь</a>. Время загрузки Ошибка во время поиска. Попробуйте <a %(a_reload)s>перезагрузить страницу</a>. Если проблема сохраняется, напишите нам по адресу %(email)s. Быстрая загрузка На самом деле, любой может помочь сохранить эти файлы, сидируя наш <a %(a_torrents)s>единый список торрентов</a>. ➡️ Иногда это происходит некорректно, когда сервер поиска работает медленно. В таких случаях <a %(a_attrs)s>перезагрузка</a> может помочь. ❌Этот файл может быть повреждён. Ищите статьи? В настоящее время этот поисковый индекс включает метаданные из различных источников метаданных.<a %(a_datasets)s>Подробнее о наших базах данных</a>. В мире существует много, очень много источников метаданных для письменных произведений. <a %(a_wikipedia)s>Эта страница Википедии</a> — хорошее начало, но если вы знаете другие хорошие списки, пожалуйста, сообщите нам. Для метаданных мы показываем оригинальные записи. Мы никак не объединяем записи. В настоящее время мы располагаем самым полным в мире открытым каталогом книг, работ и других письменных произведений. Мы являемся зеркалами Sci-Hub, Library Genesis и Z-Library, <a %(a_datasets)s>и больше</a>. <span %(classname)s>Файлы не найдены.</span> Попробуйте изменить запрос или фильтры. Результаты %(from)s–%(to)s (%(total)s всего) Если вы найдете другие «теневые библиотеки», которые мы должны зеркалить, или у вас возникнут вопросы, пожалуйста, свяжитесь с нами по адресу %(email)s. %(num)d частичные совпадения %(num)d+ частичное совпадение Напишите в поле для поиска файлов из библиотек цифровой выдачи. Напишите в поле для поиска по нашему каталогу из %(count)s напрямую загружаемых файлов, которые мы <a%(a_preserve)s>сохраняем навсегда</a>. Введите в поле для поиска. Введите текст в строку для поиска в нашем каталоге из %(count)s научных работ и журнальных статей, которые мы <a %(a_preserve)s>сохраняем навсегда</a>. Введите запрос для поиска метаданных из библиотек. Это может быть полезно при <a %(a_request)s>запросе файла</a>. Подсказка: используйте горячие клавиши «/» (фокус на поиск), «enter» (поиск), «j» (вверх), «k» (вниз) для быстрой навигации. Это записи метаданных, <span %(classname)s>не</span> файлы для скачивания. Настройки поиска Поиск Цифровая выдача Скачать Журнальные статьи Метаданные Новый поиск %(search_input)s - Поиск Поиск занял слишком много времени, поэтому вы можете увидеть неточные результаты. Попробуйте <a %(a_reload)s>перезагрузить</a> страницу. Поиск занял слишком много времени, что характерно для широких запросов. Количество фильтров может быть неточным. Для больших загрузок (более 10000 файлов), которые не принимаются Libgen или Z-Library, пожалуйста, свяжитесь с нами по %(a_email)s. Для Libgen.li, убедитесь, что вы сначала вошли на <a %(a_forum)s>их форум</a> с именем пользователя %(username)s и паролем %(password)s, а затем вернитесь на их <a %(a_upload_page)s>страницу загрузки</a>. На данный момент мы рекомендуем размещать книги на каком-либо из форков Library Genesis. Вот тут <a %(a_guide)s>простенькая инструкция</a>. Имейте в виду, что оба индексируемых сайтом форка используют одну и ту же систему загрузки. Для небольших загрузок (до 10 000 файлов) пожалуйста загрузите их как на %(first)s, так и на %(second)s. Кроме того, вы можете загрузить их на Z-Library <a %(a_upload)s>здесь</a>. Для загрузки научных статей, пожалуйста, также (в дополнение к Library Genesis) загружайте на <a %(a_stc_nexus)s>STC Nexus</a>. Это лучшая теневой библиотека для новых статей. Мы еще не интегрировали их, но сделаем это рано или поздно. Вы можете использовать их <a %(a_telegram)s>бота для загрузки в Telegram</a> или связаться по адресу, указанному в их закрепленном сообщении, если у вас слишком много файлов для загрузки таким образом. <span %(label)s>Тяжелая волонтерская работа (вознаграждения от 50 до 5000 долларов США):</span> если вы можете посвятить много времени и/или ресурсов нашей миссии, мы будем рады работать с вами более тесно. В конечном итоге вы можете присоединиться к внутренней команде. Хотя у нас ограниченный бюджет, мы можем награждать <span %(bold)s>💰 денежными вознаграждениями</span> за самую интенсивную работу. <span %(label)s>Легкая волонтерская работа:</span> если у вас есть только несколько свободных часов, все равно есть много способов помочь. Мы награждаем постоянных волонтеров <span %(bold)s>🤝 членством в Anna’s Archive</span>. Anna’s Archive полагается на таких волонтеров, как вы. Мы приветствуем все уровни вовлеченности и ищем помощь в двух основных категориях: Если вы не можете посвятить свое время волонтерству, вы все равно можете нам очень помочь, <a %(a_donate)s>пожертвовав деньги</a>, <a %(a_torrents)s>раздавая наши торренты</a>, <a %(a_uploading)s>загружая книги</a> или <a %(a_help)s>рассказывая своим друзьям о Архиве Анны</a>. <span %(bold)s>Компании:</span> мы предлагаем высокоскоростной прямой доступ к нашим коллекциям в обмен на корпоративное пожертвование или обмен на новые коллекции (например, новые сканы, OCR-данные, обогащение наших данных). <a %(a_contact)s>Свяжитесь с нами</a>, если это про вас. Также смотрите нашу <a %(a_llm)s>страницу LLM</a>. Вознаграждения Мы всегда ищем людей с хорошими навыками программирования или наступательной безопасности, чтобы они могли принять участие. Вы можете внести значительный вклад в сохранение наследия человечества. В качестве благодарности мы предоставляем членство за значительные вклады. В качестве огромной благодарности мы предоставляем денежные вознаграждения за особенно важные и сложные задачи. Это не должно рассматриваться как замена работы, но это дополнительный стимул и может помочь с понесенными расходами. Большая часть нашего кода является открытым исходным кодом, и мы попросим того же от вашего кода при присуждении вознаграждения. Есть некоторые исключения, которые мы можем обсудить в индивидуальном порядке. Вознаграждения присуждаются первому человеку, который выполнит задачу. Не стесняйтесь комментировать билет на вознаграждение, чтобы другие знали, что вы работаете над чем-то, чтобы они могли подождать или связаться с вами для совместной работы. Но имейте в виду, что другие все еще могут работать над этим и пытаться опередить вас. Однако мы не присуждаем вознаграждения за небрежную работу. Если два качественных представления будут сделаны близко друг к другу (в течение дня или двух), мы можем решить присудить вознаграждения обоим, по нашему усмотрению, например, 100%% за первое представление и 50%% за второе представление (итого 150%%). Для более крупных вознаграждений (особенно за сбор данных), пожалуйста, свяжитесь с нами, когда вы завершите ~5%% от задачи и будете уверены, что ваш метод масштабируется до полного этапа. Вам придется поделиться своим методом с нами, чтобы мы могли дать обратную связь. Также таким образом мы можем решить, что делать, если несколько человек приближаются к вознаграждению, например, потенциально присудить его нескольким людям, поощрить людей к совместной работе и т.д. ПРЕДУПРЕЖДЕНИЕ: задачи с высоким вознаграждением <span %(bold)s>сложные</span> — возможно, будет разумно начать с более простых. Перейдите к нашему <a %(a_gitlab)s>списку задач на Gitlab</a> и отсортируйте по «Приоритет метки». Это показывает приблизительный порядок задач, которые нас волнуют. Задачи без явных вознаграждений все еще могут претендовать на членство, особенно те, которые помечены как «Принято» и «Любимое Анны». Возможно, вам стоит начать с «Начального проекта». Легкое волонтерство Теперь у нас также есть синхронизированный канал Matrix на %(matrix)s. Если у вас есть несколько свободных часов, вы можете помочь различными способами. Обязательно присоединяйтесь к <a %(a_telegram)s>чату волонтеров в Telegram</a>. В знак признательности мы обычно дарим 6 месяцев «Счастливого библиотекаря» за выполнение базовых этапов, и больше за продолжительную волонтерскую работу. Все этапы требуют высококачественной работы — небрежная работа вредит нам больше, чем помогает, и мы ее отклоним. Пожалуйста, <a %(a_contact)s>напишите нам по электронной почте</a>, когда достигнете этапа. %(links)s ссылки или скриншоты запросов, которые вы выполнили. Выполнение запросов на книги (или статьи и т.д.) на форумах Z-Library или Library Genesis. У нас нет собственной системы запросов на книги, но мы зеркалим эти библиотеки, поэтому улучшение их делает Архив Анны лучше. Этап Задача Зависит от задачи. Маленькие задачи, размещенные в нашем <a %(a_telegram)s>чате волонтеров в Telegram</a>. Обычно за членство, иногда за небольшие вознаграждения. Небольшие задачи размещены в нашей группе чата для волонтеров. Обязательно оставляйте комментарии к исправленным проблемам, чтобы другие не дублировали вашу работу. %(links)s ссылки на записи, которые вы улучшили. Вы можете использовать <a %(a_list)s>список случайных проблем с metadata</a> в качестве отправной точки. Улучшение метаданных путем <a %(a_metadata)s>связывания</a> с Open Library. Здесь должно быть показано, как вы рассказываете кому-то об Архиве Анны, и они благодарят вас. %(links)s ссылки или скриншоты. Распространение информации об Архиве Анны. Например, рекомендуя книги на AA, ссылаясь на наши блоги или просто направляя людей на наш сайт. Полностью переведите язык (если он не был близок к завершению). <a %(a_translate)s>Перевод</a> сайта. Ссылка на историю правок, показывающая, что вы внесли значительные изменения. Улучшение страницы Википедии о Архиве Анны на вашем языке. Включите информацию со страницы Википедии AA на других языках, а также с нашего сайта и блога. Добавьте ссылки на AA на других релевантных страницах. Волонтерство и награды 