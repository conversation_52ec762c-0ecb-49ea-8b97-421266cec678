# read MARC mnemonics
# result is in MARC8 and still needs to be converted to Unicode

import re

re_brace = re.compile(b'(\\{.+?\\})')

mapping = {
    b'{00}': b'\x00',
    b'{01}': b'\x01',
    b'{02}': b'\x02',
    b'{03}': b'\x03',
    b'{04}': b'\x04',
    b'{05}': b'\x05',
    b'{06}': b'\x06',
    b'{07}': b'\x07',
    b'{08}': b'\x08',
    b'{09}': b'\t',
    b'{0A}': b'\n',
    b'{0B}': b'\x0b',
    b'{0C}': b'\x0c',
    b'{0D}': b'\r',
    b'{0E}': b'\x0e',
    b'{0F}': b'\x0f',
    b'{0}': b'0',
    b'{10}': b'\x10',
    b'{11}': b'\x11',
    b'{12}': b'\x12',
    b'{13}': b'\x13',
    b'{14}': b'\x14',
    b'{15}': b'\x15',
    b'{16}': b'\x16',
    b'{17}': b'\x17',
    b'{18}': b'\x18',
    b'{19}': b'\x19',
    b'{1A}': b'\x1a',
    b'{1B}': b'\x1b',
    b'{1C}': b'\x1c',
    b'{1D}': b'\x1d',
    b'{1E}': b'\x1e',
    b'{1F}': b'\x1f',
    b'{1}': b'1',
    b'{20}': b' ',
    b'{21}': b'!',
    b'{22}': b'"',
    b'{23}': b'#',
    b'{24}': b'$',
    b'{25}': b'%',
    b'{26}': b'&',
    b'{27}': "'",
    b'{28}': b'(',
    b'{29}': b')',
    b'{2A}': b'*',
    b'{2B}': b'+',
    b'{2C}': b',',
    b'{2D}': b'-',
    b'{2E}': b'.',
    b'{2F}': b'/',
    b'{2}': b'2',
    b'{30}': b'0',
    b'{31}': b'1',
    b'{32}': b'2',
    b'{33}': b'3',
    b'{34}': b'4',
    b'{35}': b'5',
    b'{36}': b'6',
    b'{37}': b'7',
    b'{38}': b'8',
    b'{39}': b'9',
    b'{3A}': b':',
    b'{3B}': b';',
    b'{3C}': b'<',
    b'{3D}': b'=',
    b'{3E}': b'>',
    b'{3F}': b'?',
    b'{3}': b'3',
    b'{40}': b'@',
    b'{41}': b'A',
    b'{42}': b'B',
    b'{43}': b'C',
    b'{44}': b'D',
    b'{45}': b'E',
    b'{46}': b'F',
    b'{47}': b'G',
    b'{48}': b'H',
    b'{49}': b'I',
    b'{4A}': b'J',
    b'{4B}': b'K',
    b'{4C}': b'L',
    b'{4D}': b'M',
    b'{4E}': b'N',
    b'{4F}': b'O',
    b'{4}': b'4',
    b'{50}': b'P',
    b'{51}': b'Q',
    b'{52}': b'R',
    b'{53}': b'S',
    b'{54}': b'T',
    b'{55}': b'U',
    b'{56}': b'V',
    b'{57}': b'W',
    b'{58}': b'X',
    b'{59}': b'Y',
    b'{5A}': b'Z',
    b'{5B}': b'[',
    b'{5C}': b'\\',
    b'{5D}': b']',
    b'{5E}': b'^',
    b'{5F}': b'_',
    b'{5}': b'5',
    b'{60}': b'`',
    b'{61}': b'a',
    b'{62}': b'b',
    b'{63}': b'c',
    b'{64}': b'd',
    b'{65}': b'e',
    b'{66}': b'f',
    b'{67}': b'g',
    b'{68}': b'h',
    b'{69}': b'i',
    b'{6A}': b'j',
    b'{6B}': b'k',
    b'{6C}': b'l',
    b'{6D}': b'm',
    b'{6E}': b'n',
    b'{6F}': b'o',
    b'{6}': b'6',
    b'{70}': b'p',
    b'{71}': b'q',
    b'{72}': b'r',
    b'{73}': b's',
    b'{74}': b't',
    b'{75}': b'u',
    b'{76}': b'v',
    b'{77}': b'w',
    b'{78}': b'x',
    b'{79}': b'y',
    b'{7A}': b'z',
    b'{7B}': b'{',
    b'{7C}': b'|',
    b'{7D}': b'}',
    b'{7E}': b'~',
    b'{7F}': b'\x7f',
    b'{7}': b'7',
    b'{80}': b'\x80',
    b'{81}': b'\x81',
    b'{82}': b'\x82',
    b'{83}': b'\x83',
    b'{84}': b'\x84',
    b'{85}': b'\x85',
    b'{86}': b'\x86',
    b'{87}': b'\x87',
    b'{88}': b'\x88',
    b'{89}': b'\x89',
    b'{8A}': b'\x8a',
    b'{8B}': b'\x8b',
    b'{8C}': b'\x8c',
    b'{8D}': b'\x8d',
    b'{8E}': b'\x8e',
    b'{8F}': b'\x8f',
    b'{8}': b'8',
    b'{90}': b'\x90',
    b'{91}': b'\x91',
    b'{92}': b'\x92',
    b'{93}': b'\x93',
    b'{94}': b'\x94',
    b'{95}': b'\x95',
    b'{96}': b'\x96',
    b'{97}': b'\x97',
    b'{98}': b'\x98',
    b'{99}': b'\x99',
    b'{9A}': b'\x9a',
    b'{9B}': b'\x9b',
    b'{9C}': b'\x9c',
    b'{9D}': b'\x9d',
    b'{9E}': b'\x9e',
    b'{9F}': b'\x9f',
    b'{9}': b'9',
    b'{A0}': b'\xa0',
    b'{A1}': b'\xa1',
    b'{A2}': b'\xa2',
    b'{A3}': b'\xa3',
    b'{A4}': b'\xa4',
    b'{A5}': b'\xa5',
    b'{A6}': b'\xa6',
    b'{A7}': b'\xa7',
    b'{A8}': b'\xa8',
    b'{A9}': b'\xa9',
    b'{AA}': b'\xaa',
    b'{AB}': b'\xab',
    b'{AC}': b'\xac',
    b'{AD}': b'\xad',
    b'{AElig}': b'\xa5',
    b'{AE}': b'\xae',
    b'{AF}': b'\xaf',
    b'{Aacute}': b'\xe2A',
    b'{Abreve}': b'\xe6A',
    b'{Acirc}': b'\xe3A',
    b'{Acy}': b'A',
    b'{Agrave}': b'\xe1A',
    b'{Aogon}': b'\xf1A',
    b'{Aring}': b'\xeaA',
    b'{Atilde}': b'\xe4A',
    b'{Auml}': b'\xe8A',
    b'{A}': b'A',
    b'{B0}': b'\xb0',
    b'{B1}': b'\xb1',
    b'{B2}': b'\xb2',
    b'{B3}': b'\xb3',
    b'{B4}': b'\xb4',
    b'{B5}': b'\xb5',
    b'{B6}': b'\xb6',
    b'{B7}': b'\xb7',
    b'{B8}': b'\xb8',
    b'{B9}': b'\xb9',
    b'{BA}': b'\xba',
    b'{BB}': b'\xbb',
    b'{BC}': b'\xbc',
    b'{BD}': b'\xbd',
    b'{BE}': b'\xbe',
    b'{BF}': b'\xbf',
    b'{Bcy}': b'B',
    b'{B}': b'B',
    b'{C0}': b'\xc0',
    b'{C1}': b'\xc1',
    b'{C2}': b'\xc2',
    b'{C3}': b'\xc3',
    b'{C4}': b'\xc4',
    b'{C5}': b'\xc5',
    b'{C6}': b'\xc6',
    b'{C7}': b'\xc7',
    b'{C8}': b'\xc8',
    b'{C9}': b'\xc9',
    b'{CA}': b'\xca',
    b'{CB}': b'\xcb',
    b'{CC}': b'\xcc',
    b'{CD}': b'\xcd',
    b'{CE}': b'\xce',
    b'{CF}': b'\xcf',
    b'{CHcy}': b'Ch',
    b'{Cacute}': b'\xe2C',
    b'{Ccaron}': b'\xe9C',
    b'{Ccedil}': b'\xf0C',
    b'{C}': b'C',
    b'{D0}': b'\xd0',
    b'{D1}': b'\xd1',
    b'{D2}': b'\xd2',
    b'{D3}': b'\xd3',
    b'{D4}': b'\xd4',
    b'{D5}': b'\xd5',
    b'{D6}': b'\xd6',
    b'{D7}': b'\xd7',
    b'{D8}': b'\xd8',
    b'{D9}': b'\xd9',
    b'{DA}': b'\xda',
    b'{DB}': b'\xdb',
    b'{DC}': b'\xdc',
    b'{DD}': b'\xdd',
    b'{DE}': b'\xde',
    b'{DF}': b'\xdf',
    b'{DJEcy}': b'\xa3',
    b'{DZEcy}': b'Dz',
    b'{DZHEcy}': b'D\xe9z',
    b'{Dagger}': b'|',
    b'{Dcaron}': b'\xe9D',
    b'{Dcy}': b'D',
    b'{Dstrok}': b'\xa3',
    b'{D}': b'D',
    b'{E0}': b'\xe0',
    b'{E1}': b'\xe1',
    b'{E2}': b'\xe2',
    b'{E3}': b'\xe3',
    b'{E4}': b'\xe4',
    b'{E5}': b'\xe5',
    b'{E6}': b'\xe6',
    b'{E7}': b'\xe7',
    b'{E8}': b'\xe8',
    b'{E9}': b'\xe9',
    b'{EA}': b'\xea',
    b'{EB}': b'\xeb',
    b'{EC}': b'\xec',
    b'{ED}': b'\xed',
    b'{EE}': b'\xee',
    b'{EF}': b'\xef',
    b'{ETH}': b'\xa3',
    b'{Eacute}': b'\xe2E',
    b'{Ecaron}': b'\xe9E',
    b'{Ecirc}': b'\xe3E',
    b'{Ecy}': b'\xe7E',
    b'{Egrave}': b'\xe1E',
    b'{Ehookr}': b'\xf1E',
    b'{Eogon}': b'\xf1E',
    b'{Euml}': b'\xe8E',
    b'{E}': b'E',
    b'{F0}': b'\xf0',
    b'{F1}': b'\xf1',
    b'{F2}': b'\xf2',
    b'{F3}': b'\xf3',
    b'{F4}': b'\xf4',
    b'{F5}': b'\xf5',
    b'{F6}': b'\xf6',
    b'{F7}': b'\xf7',
    b'{F8}': b'\xf8',
    b'{F9}': b'\xf9',
    b'{FA}': b'\xfa',
    b'{FB}': b'\xfb',
    b'{FC}': b'\xfc',
    b'{FD}': b'\xfd',
    b'{FE}': b'\xfe',
    b'{FF}': b'\xff',
    b'{Fcy}': b'F',
    b'{F}': b'F',
    b'{GEcy}': b'G',
    b'{GHcy}': b'G',
    b'{GJEcy}': b'\xe2G',
    b'{Gcy}': b'G',
    b'{G}': b'G',
    b'{HARDcy}': b'\xb7',
    b'{Hcy}': b'H',
    b'{H}': b'H',
    b'{IEcy}': b'\xebI\xecE',
    b'{IJlig}': b'IJ',
    b'{IOcy}': b'\xebI\xecO',
    b'{IYcy}': b'Y',
    b'{Iacute}': b'\xe2I',
    b'{Icaron}': b'\xe9I',
    b'{Icirc}': b'\xe3I',
    b'{Icy}': b'I',
    b'{Idot}': b'\xe7I',
    b'{Igrave}': b'\xe1I',
    b'{Iumlcy}': b'\xe8I',
    b'{Iuml}': b'\xe8I',
    b'{I}': b'I',
    b'{JEcy}': b'J',
    b'{JIcy}': b'\xe8I',
    b'{Jcy}': b'\xe6I',
    b'{J}': b'J',
    b'{KHcy}': b'Kh',
    b'{KJEcy}': b'\xe2K',
    b'{Kcy}': b'K',
    b'{K}': b'K',
    b'{LJEcy}': b'Lj',
    b'{Lacute}': b'\xe2L',
    b'{Lcy}': b'L',
    b'{Lstrok}': b'\xa1',
    b'{L}': b'L',
    b'{Mcy}': b'M',
    b'{M}': b'M',
    b'{NJEcy}': b'Nj',
    b'{Nacute}': b'\xe2N',
    b'{Ncaron}': b'\xe9N',
    b'{Ncy}': b'N',
    b'{No}': b'No.',
    b'{Ntilde}': b'\xb4N',
    b'{N}': b'N',
    b'{OElig}': b'\xa6',
    b'{Oacute}': b'\xe2O',
    b'{Ocirc}': b'\xe3O',
    b'{Ocy}': b'O',
    b'{Odblac}': b'\xeeO',
    b'{Ograve}': b'\xe1O',
    b'{Ohorn}': b'\xac',
    b'{Ostrok}': b'\xa2',
    b'{Otilde}': b'\xe4O',
    b'{Ouml}': b'\xe8O',
    b'{O}': b'O',
    b'{Pcy}': b'P',
    b'{P}': b'P',
    b'{Q}': b'Q',
    b'{Racute}': b'\xe2R',
    b'{Rcaron}': b'\xe9R',
    b'{Rcy}': b'R',
    b'{R}': b'R',
    b'{SHCHcy}': b'Shch',
    b'{SHcy}': b'Sh',
    b'{SOFTcy}': b'\xa7',
    b'{Sacute}': b'\xe2S',
    b'{Scommab}': b'\xf7S',
    b'{Scy}': b'S',
    b'{S}': b'S',
    b'{THORN}': b'\xa4',
    b'{TSHEcy}': b'\xe2C',
    b'{TScy}': b'\xebT\xecS',
    b'{Tcaron}': b'\xe9T',
    b'{Tcommab}': b'\xf7T',
    b'{Tcy}': b'T',
    b'{T}': b'T',
    b'{Uacute}': b'\xe2U',
    b'{Ubrevecy}': b'\xe6U',
    b'{Ucirc}': b'\xe3U',
    b'{Ucy}': b'U',
    b'{Udblac}': b'\xeeU',
    b'{Ugrave}': b'\xe1U',
    b'{Uhorn}': b'\xad',
    b'{Uring}': b'\xeaU',
    b'{Uuml}': b'\xe8U',
    b'{U}': b'U',
    b'{Vcy}': b'V',
    b'{V}': b'V',
    b'{W}': b'W',
    b'{X}': b'X',
    b'{YAcy}': b'\xebI\xecA',
    b'{YEcy}': b'E',
    b'{YIcy}': b'I',
    b'{YUcy}': b'\xebI\xecU',
    b'{Yacute}': b'\xe2Y',
    b'{Ycy}': b'Y',
    b'{Y}': b'Y',
    b'{ZHcy}': b'Zh',
    b'{ZHuacy}': b'\xebZ\xech',
    b'{Zacute}': b'\xe2Z',
    b'{Zcy}': b'Z',
    b'{Zdot}': b'\xe7Z',
    b'{Z}': b'Z',
    b'{aacute}': b'\xe2a',
    b'{abreve}': b'\xe6a',
    b'{acirc}': b'\xe3a',
    b'{acute}': b'\xe2',
    b'{acy}': b'a',
    b'{aelig}': b'\xb5',
    b'{agrave}': b'\xe1a',
    b'{agr}': b'b',
    b'{alif}': b'\xae',
    b'{amp}': b'&',
    b'{aogon}': b'\xf1a',
    b'{apos}': b"'",
    b'{arab}': b'(3',
    b'{aring}': b'\xeaa',
    b'{ast}': b'*',
    b'{asuper}': b'a',
    b'{atilde}': b'\xe4a',
    b'{auml}': b'\xe8a',
    b'{ayn}': b'\xb0',
    b'{a}': b'a',
    b'{bcy}': b'b',
    b'{bgr}': b'c',
    b'{breveb}': b'\xf9',
    b'{breve}': b'\xe6',
    b'{brvbar}': b'|',
    b'{bsol}': b'\\',
    b'{bull}': b'*',
    b'{b}': b'b',
    b'{cacute}': b'\xe2c',
    b'{candra}': b'\xef',
    b'{caron}': b'\xe9',
    b'{ccaron}': b'\xe9c',
    b'{ccedil}': b'\xf0c',
    b'{cedil}': b'\xf0',
    b'{cent}': b'c',
    b'{chcy}': b'ch',
    b'{circb}': b'\xf4',
    b'{circ}': b'\xe3',
    b'{cjk}': b'$1',
    b'{colon}': b':',
    b'{commaa}': b'\xfe',
    b'{commab}': b'\xf7',
    b'{commat}': b'@',
    b'{comma}': b',',
    b'{copy}': b'\xc3',
    b'{curren}': b'*',
    b'{cyril}': b'(N',
    b'{c}': b'c',
    b'{dagger}': b'|',
    b'{dblac}': b'\xee',
    b'{dbldotb}': b'\xf3',
    b'{dblunder}': b'\xf5',
    b'{dcaron}': b'\xe9d',
    b'{dcy}': b'd',
    b'{deg}': b'\xc0',
    b'{diaer}': b'\xe8',
    b'{divide}': b'/',
    b'{djecy}': b'\xb3',
    b'{dollar}': b'$',
    b'{dotb}': b'\xf2',
    b'{dot}': b'\xe7',
    b'{dstrok}': b'\xb3',
    b'{dzecy}': b'dz',
    b'{dzhecy}': b'd\xe9z',
    b'{d}': b'd',
    b'{eacute}': b'\xe2e',
    b'{ea}': b'\xea',
    b'{ecaron}': b'\xe9e',
    b'{ecirc}': b'\xe3e',
    b'{ecy}': b'\xe7e',
    b'{egrave}': b'\xe1e',
    b'{ehookr}': b'\xf1e',
    b'{eogon}': b'\xf1e',
    b'{equals}': b'=',
    b'{esc}': b'\x1b',
    b'{eth}': b'\xba',
    b'{euml}': b'\xe8e',
    b'{excl}': b'!',
    b'{e}': b'e',
    b'{fcy}': b'f',
    b'{flat}': b'\xa9',
    b'{fnof}': b'f',
    b'{frac12}': b'1/2',
    b'{frac14}': b'1/4',
    b'{frac34}': b'3/4',
    b'{f}': b'f',
    b'{gcy}': b'g',
    b'{gecy}': b'g',
    b'{ggr}': b'g',
    b'{ghcy}': b'g',
    b'{gjecy}': b'\xe2g',
    b'{grave}': b'\xe1',
    b'{greek}': b'g',
    b'{gs}': b'\x1d',
    b'{gt}': b'>',
    b'{g}': b'g',
    b'{hardcy}': b'\xb7',
    b'{hardsign}': b'\xb7',
    b'{hcy}': b'h',
    b'{hebrew}': b'(2',
    b'{hellip}': b'...',
    b'{hooka}': b'\xe0',
    b'{hookl}': b'\xf7',
    b'{hookr}': b'\xf1',
    b'{hyphen}': b'-',
    b'{h}': b'h',
    b'{iacute}': b'\xe2i',
    b'{icaron}': b'\xe9i',
    b'{icirc}': b'\xe3i',
    b'{icy}': b'i',
    b'{iecy}': b'\xebi\xece',
    b'{iexcl}': b'\xc6',
    b'{igrave}': b'\xe1i',
    b'{ijlig}': b'ij',
    b'{inodot}': b'\xb8',
    b'{iocy}': b'\xebi\xeco',
    b'{iquest}': b'\xc5',
    b'{iumlcy}': b'\xe8i',
    b'{iuml}': b'\xe8i',
    b'{iycy}': b'y',
    b'{i}': b'i',
    b'{jcy}': b'\xe6i',
    b'{jecy}': b'j',
    b'{jicy}': b'\xe8i',
    b'{joiner}': b'\x8d',
    b'{j}': b'j',
    b'{kcy}': b'k',
    b'{khcy}': b'kh',
    b'{kjecy}': b'\xe2k',
    b'{k}': b'k',
    b'{lacute}': b'\xe2l',
    b'{laquo}': b'"',
    b'{latin}': b'(B',
    b'{lcub}': b'{',
    b'{lcy}': b'l',
    b'{ldbltil}': b'\xfa',
    b'{ldquo}': b'"',
    b'{ljecy}': b'lj',
    b'{llig}': b'\xeb',
    b'{lpar}': b'(',
    b'{lsqb}': b'[',
    b'{lsquor}': b"'",
    b'{lsquo}': b"'",
    b'{lstrok}': b'\xb1',
    b'{lt}': b'<',
    b'{l}': b'l',
    b'{macr}': b'\xe5',
    b'{mcy}': b'm',
    b'{mdash}': b'--',
    b'{middot}': b'\xa8',
    b'{mlPrime}': b'\xb7',
    b'{mllhring}': b'\xb0',
    b'{mlprime}': b'\xa7',
    b'{mlrhring}': b'\xae',
    b'{m}': b'm',
    b'{nacute}': b'\xe2n',
    b'{ncaron}': b'\xe9n',
    b'{ncy}': b'n',
    b'{ndash}': b'--',
    b'{njecy}': b'nj',
    b'{nonjoin}': b'\x8e',
    b'{ntilde}': b'\xb4n',
    b'{num}': b'#',
    b'{n}': b'n',
    b'{oacute}': b'\xe2o',
    b'{ocirc}': b'\xe3o',
    b'{ocy}': b'o',
    b'{odblac}': b'\xeeo',
    b'{oelig}': b'\xb6',
    b'{ogon}': b'\xf1',
    b'{ograve}': b'\xe1o',
    b'{ohorn}': b'\xbc',
    b'{ordf}': b'a',
    b'{ordm}': b'o',
    b'{ostrok}': b'\xb2',
    b'{osuper}': b'o',
    b'{otilde}': b'\xe4o',
    b'{ouml}': b'\xe8o',
    b'{o}': b'o',
    b'{para}': b'|',
    b'{pcy}': b'p',
    b'{percnt}': b'%',
    b'{period}': b'.',
    b'{phono}': b'\xc2',
    b'{pipe}': b'|',
    b'{plusmn}': b'\xab',
    b'{plus}': b'+',
    b'{pound}': b'\xb9',
    b'{p}': b'p',
    b'{quest}': b'?',
    b'{quot}': b'"',
    b'{q}': b'q',
    b'{racute}': b'\xe2r',
    b'{raquo}': b'"',
    b'{rcaron}': b'\xe9r',
    b'{rcedil}': b'\xf8',
    b'{rcommaa}': b'\xed',
    b'{rcub}': b'}',
    b'{rcy}': b'r',
    b'{rdbltil}': b'\xfb',
    b'{rdquofh}': b'"',
    b'{rdquor}': b'"',
    b'{reg}': b'\xaa',
    b'{ringb}': b'\xf4',
    b'{ring}': b'\xea',
    b'{rlig}': b'\xec',
    b'{rpar}': b')',
    b'{rsqb}': b']',
    b'{rsquor}': b"'",
    b'{rsquo}': b"'",
    b'{rs}': b'\x1e',
    b'{r}': b'r',
    b'{sacute}': b'\xe2s',
    b'{scommab}': b'\xf7s',
    b'{scriptl}': b'\xc1',
    b'{scy}': b's',
    b'{sect}': b'|',
    b'{semi}': b';',
    b'{sharp}': b'\xc4',
    b'{shchcy}': b'shch',
    b'{shcy}': b'sh',
    b'{shy}': b'-',
    b'{softcy}': b'\xa7',
    b'{softsign}': b'\xa7',
    b'{sol}': b'/',
    b'{space}': b' ',
    b'{spcirc}': b'^',
    b'{spgrave}': b'`',
    b'{sptilde}': b'~',
    b'{spundscr}': b'_',
    b'{squf}': b'|',
    b'{sub}': b'b',
    b'{sup1}': b'\x1bp1\x1bs',
    b'{sup2}': b'\x1bp2\x1bs',
    b'{sup3}': b'\x1bp3\x1bs',
    b'{super}': b'p',
    b'{szlig}': b'ss',
    b'{s}': b's',
    b'{tcaron}': b'\xe9t',
    b'{tcommab}': b'\xf7t',
    b'{tcy}': b't',
    b'{thorn}': b'\xb4',
    b'{tilde}': b'\xe4',
    b'{times}': b'x',
    b'{trade}': b'(Tm)',
    b'{tscy}': b'\xebt\xecs',
    b'{tshecy}': b'\xe2c',
    b'{t}': b't',
    b'{uacute}': b'\xe2u',
    b'{ubrevecy}': b'\xe6u',
    b'{ucirc}': b'\xe3u',
    b'{ucy}': b'u',
    b'{udblac}': b'\xeeu',
    b'{ugrave}': b'\xe1u',
    b'{uhorn}': b'\xbd',
    b'{uml}': b'\xe8',
    b'{under}': b'\xf6',
    b'{uring}': b'\xeau',
    b'{us}': b'\x1f',
    b'{uuml}': b'\xe8u',
    b'{u}': b'u',
    b'{vcy}': b'v',
    b'{verbar}': b'|',
    b'{vlineb}': b'\xf2',
    b'{v}': b'v',
    b'{w}': b'w',
    b'{x}': b'x',
    b'{yacute}': b'\xe2y',
    b'{yacy}': b'\xebi\xeca',
    b'{ycy}': b'y',
    b'{yecy}': b'e',
    b'{yen}': b'Y',
    b'{yicy}': b'i',
    b'{yucy}': b'\xebi\xecu',
    b'{y}': b'y',
    b'{zacute}': b'\xe2z',
    b'{zcy}': b'z',
    b'{zdot}': b'\xe7z',
    b'{zhcy}': b'zh',
    b'{zhuacy}': b'\xebz\xech',
    b'{z}': b'z',
}


def load_table(filename):
    mapping = {}
    for line in (i.split(',') for i in open(filename) if i.startswith('{')):
        key = line[0]
        value = ''
        for d in line[2].strip().split(" "):
            assert len(d) == 4
            assert d[3] == 'd'
            value += chr(int(d[0:3]))

        mapping[key] = value
    return mapping


def read(input):
    """
    :param input bytes: MARC21 binary field data
    :rtype: bytes
    """
    return re_brace.sub(lambda x: mapping.get(x.group(1), x.group(1)), input)
