{% import 'macros/shared_links.j2' as a %}
{% import 'macros/helpers.j2' as h %}
{% extends "layouts/index.html" %}

{% block title %}{{ gettext('page.volunteering.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.volunteering.title') }}</h2>

  <p class="mb-4">
    {{ gettext('page.volunteering.intro.text1') }}
  </p>

  <ul class="list-inside mb-4">
    <li class="list-disc">{{ gettext('page.volunteering.intro.light', label=(h.bold|xmlattr), bold=(h.bold|xmlattr)) }}</li>
    <li class="list-disc">{{ gettext('page.volunteering.intro.heavy', label=(h.bold|xmlattr), bold=(h.bold|xmlattr)) }}</li>
  </ul>

  <p class="mb-4">
    {{ gettext('page.volunteering.intro.text2', a_donate=(a.donate|xmlattr), a_torrents=(a.torrents|xmlattr), a_uploading=(a.faqs_upload|xmlattr), a_help=(a.faqs_help|xmlattr)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.intro.text3', a_contact=(a.contact|xmlattr), a_llm=(a.llm|xmlattr), bold=(h.bold|xmlattr)) }}
  </p>

  <h3 class="group mt-8 mb-1 text-xl font-bold" id="light">{{ gettext('page.volunteering.section.light.heading') }} <a href="#light" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    <!-- {{ gettext('page.volunteering.section.light.text1', a_telegram=(a.telegram_volunteers|xmlattr)) }} -->
    {{ gettext('page.volunteering.section.light.matrix', matrix="#annas:archivecommunication.org") }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.light.text2', a_contact=(a.contact|xmlattr)) }}
  </p>

  <table class="mb-4 w-full">
    <tr class="even:bg-[#f2f2f2]">
      <th class="align-bottom px-4 py-1 w-[60%]">{{ gettext('page.volunteering.table.header.task') }}</th>
      <th class="align-bottom px-4 py-1 ">{{ gettext('page.volunteering.table.header.milestone') }}</th>
    </tr>
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-4">{{ gettext('page.volunteering.table.spread_the_word.task.alt1') }}</td>
      <td class="p-4">{{ gettext('page.volunteering.table.spread_the_word.milestone_count', links=30) }} {{ gettext('page.volunteering.table.spread_the_word.milestone.let_them_know') }}</td>
    </tr>
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-4">{{ gettext('page.volunteering.table.open_library.task', a_metadata=(a.metadata|xmlattr)) }} {{ gettext('page.volunteering.table.open_library.random_metadata', a_list=(' href="/activity?filter=metadata&randomize=true" ' | safe)) }} {{ gettext('page.volunteering.table.open_library.leave_comment') }}</td>
      <td class="p-4">{{ gettext('page.volunteering.table.open_library.milestone_count', links=30) }}</td>
    </tr>
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-4">{{ gettext('page.volunteering.table.translate.task', a_translate=(a.annas_translations|xmlattr)) }}</td>
      <td class="p-4">{{ gettext('page.volunteering.table.translate.milestone') }}</td>
    </tr>
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/250" rel="noopener noreferrer" target="_blank">{{ gettext('page.volunteering.table.wikipedia.task') }}</a></td>
      <td class="p-4">{{ gettext('page.volunteering.table.wikipedia.milestone') }}</td>
    </tr>
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-4">{{ gettext('page.volunteering.table.fulfill_requests.task') }}</td>
      <td class="p-4">{{ gettext('page.volunteering.table.fulfill_requests.milestone_count', links=10) }}</td>
    </tr>
    <!-- TODO: fixing file or formatting issues? -->
    <tr class="even:bg-[#f2f2f2]">
      <!-- <td class="p-4">{{ gettext('page.volunteering.table.misc.task', a_telegram=(a.telegram_volunteers|xmlattr)) }}</td> -->
      <td class="p-4">{{ gettext('page.volunteering.table.misc.task2') }}</td>
      <td class="p-4">{{ gettext('page.volunteering.table.misc.milestone') }}</td>
    </tr>
  </table>

  <h3 class="group mt-8 mb-1 text-xl font-bold" id="bounties">{{ gettext('page.volunteering.section.bounties.heading') }} <a href="#bounties" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text2') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text3') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text4') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text5') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text6', bold=(h.bold|xmlattr)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.volunteering.section.bounties.text7', a_gitlab=(a.gitlab_issues|xmlattr)) }}
  </p>

  <div class="overflow-hidden h-[2500px]">
    <iframe credentialless scrolling="no" allow="vertical-scroll none" sandbox="allow-scripts allow-same-origin" class="mt-[-150px] h-[calc(2500px+150px)] w-full overflow-hidden pointer-events-none" src="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/?sort=label_priority&state=opened&label_name%5B%5D=2-Bounty&first_page_size=100"></iframe>
  </div>
{% endblock %}
