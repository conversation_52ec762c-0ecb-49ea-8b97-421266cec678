{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }} ▶ {{ gettext('page.datasets.worldcat.title') }} [oclc]{% endblock %}

{% block body %}
  <div class="mb-4"><a href="/datasets">{{ gettext('page.datasets.title') }}</a> ▶ {{ gettext('page.datasets.worldcat.title') }} [oclc]</div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    <div class="text-xs mb-2">Overview from <a href="/datasets">datasets page</a>.</div>
    <table class="w-full mx-[-8px]">
      <tr class="even:bg-[#f2f2f2]">
        <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.source.header') }}</th>
        <th class="p-2 align-bottom text-left w-[65%]">{{ gettext('page.datasets.sources.metadata.header') }}</th>
        <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.last_updated.header') }}</th>
      </tr>

      <tr class="even:bg-[#f2f2f2]">
        <td class="p-2 align-top">
          <a class="custom-a underline hover:opacity-60" href="/datasets/oclc">
            {{ gettext('common.record_sources_mapping.oclc') }} [oclc]
          </a>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.worldcat.metadata1', icon='❌') }}
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.worldcat.metadata2', icon='👩‍💻',
                worldcat=(dict(href="/torrents#worldcat") | xmlattr),
            ) }}
          </div>
        </td>
        <td class="p-2 align-top">{{ stats_data.oclc_date }}</td>
      </tr>
    </table>
  </div>

  <p class="mb-4">
    {{ gettext(
      'page.datasets.worldcat.description',
      a_worldcat=(dict(href="https://en.wikipedia.org/wiki/WorldCat") | xmlattr),
      a_oclc=(dict(href="https://en.wikipedia.org/wiki/OCLC") | xmlattr)
    ) }}
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.datasets.worldcat.description2.label') }}</strong>
    {{ gettext(
        'page.datasets.worldcat.description2',
        a_scrape=(dict(href="/blog/worldcat-scrape.html") | xmlattr),
        a_aac=(dict(href="/blog/annas-archive-containers.html") | xmlattr)
    ) }}
  </p>

  <p class="">
    Read the <a {{ dict(href="/blog/annas-archive-containers.html") | xmlattr }}>original blog post</a> for much more detail, but the record types in this original release were:
  </p>

  <ul class="list-inside mb-4 ml-4">
    <li class="list-disc"><em>title_json:</em> This is the JSON that is loaded when going to a worldcat.org/title/:id page.</li>
    <li class="list-disc"><em>briefrecords_json:</em> Some scrapes used search endpoints that returned a little bit less JSON, in a briefRecords array.</li>
    <li class="list-disc"><em>providersearchrequest_json:</em> This API leaked the raw internal search request. It has the most information of all our scrapes, but unfortunately we only have a very small number of records using this method.</li>
    <li class="list-disc"><em>legacysearch_html:</em> We discovered pages that still used the old search UI. There is very little information in here, but the basics such as title, author, and even ISBN are present.</li>
    <li class="list-disc"><em>not_found_title_json:</em> Records for which we got a 404 during a “title_json” request.</li>
    <li class="list-disc"><em>redirect_title_json:</em> We made a request for a certain OCLC ID, but received data for another OCLC ID (which happens when the original records are merged or deduplicated).</li>
  </ul>

  <p class="mb-4">
    <strong>October 2024, not_found_title_json bug:</strong> a volunteer “m” discovered that our “not_found_title_json” entries might be incorrect in some cases. For example, we have a such an entry for ID 1405, even though that appears to be a <a href="https://worldcat.org/title/1405" rel="noopener noreferrer nofollow">legitimate record</a>, suggesting that this might have been a bug in our scraper. Before rescraping everything, we should do some analysis by rescraping some of these records, and investigating if there are some patterns to this bug, such as only certain ID ranges, or original scraper filenames.
  </p>

  <p class="mb-4">
    <strong>December 2024:</strong> we released a new scrape: <code class="text-xs break-all text-gray-600">“annas_archive_meta__aacid__worldcat__20241230T203056Z--20241230T203056Z.jsonl.seekable.zst.torrent”</code>. This includes two new sources of data:
  </p>

  <p class="mb-4">
    1. <em>Recursive range queries.</em> As we briefly mentioned in the original blog post, we found some IDs outside our original scrape range of 1 to 1,350,000,000. It appeared that the records went all the way until the 10,000,000,000 range. This is too much to iterate, and we didn't know exactly where the ranges were. Luckily we found a way to scrape ranges of IDs, by searching for e.g. <code class="text-xs text-gray-600">“12345#####”</code>, where # is a wildcard (single character). We could get the total records from the search result, and if it’s big enough, recursively also search for <code class="text-xs text-gray-600">“123450####”, “123451####”, …, “123459####”</code>. This would also match non-IDs (ISBNs, numbers in text, other identifiers), but at least it would ALSO match IDs.
  </p>

  <ul class="list-inside mb-4 ml-4">
    <li class="list-disc"><em>briefrecords_json:</em> All scrapes returned data in this format, which we also had in our original release, so we kept this type.
      <ul class="list-inside mb-4 ml-4">
        <li class="list-disc">You can identify records from these range scrape because they have a <code class="text-xs break-all text-gray-600">from_filenames</code> field with something like <code class="text-xs text-gray-600">"range_query/992350####"</code>.</li>
        <li class="list-disc">Paginated searches (page 2 and futher) are denoted like <code class="text-xs text-gray-600">"range_query/904802####____2"</code>.</li>
        <li class="list-disc">At some point we had a bug in our pagination, which meant that it didn’t actually add the <code class="text-xs text-gray-600">&amp;page=2</code> query parameter to the URL. We've still kept those records (in case they happen to have unique results), but they’re marked like <code class="text-xs text-gray-600">"range_query/backup_995980####____2"</code>.</li>
      </ul>
    </li>
    <li class="list-disc"><em>other_metadata_type:</em> We wanted to include metadata that doesn’t correspond to OCLC IDs. These contain “other_metadata_type” as their first JSON key.
      <ul class="list-inside mb-4 ml-4">
        <li class="list-disc"><em>successful_range_query:</em> Example: <code class="text-xs break-all text-gray-600">{"other_meta_type":"successful_range_query","query":"98846#####","from_query":"9884######","search_limit":50,"number_of_records":311,"len_brief_records":50}</code>. Metadata for a single query. Shows where it was recursively derived from (<code class="text-xs break-all text-gray-600">“from_query”</code>). For later queries, shows the value of the <code class="text-xs text-gray-600">&amp;limit=</code> parameter, which we varied to help with scraping (when <code class="text-xs break-all text-gray-600">“search_limit”</code> is null it was actually 50). The result of the <code class="text-xs break-all text-gray-600">“numberOfRecords”</code> field, and the actual length of <code class="text-xs break-all text-gray-600">“briefRecords”</code> are both included as well.</li>
        <li class="list-disc"><em>status_internal_server_error:</em> Apparently there were specific records that caused an internal server error when we queried them. Since this would break lots of higher-level searches, we had no choice but to always recurse down when encountering this case. Example: <code class="text-xs break-all text-gray-600">{"other_meta_type":"status_internal_server_error","query":"48161#####","from_query":"4816######","search_limit":1}</code>.</li>
        <li class="list-disc"><em>todo_range_query:</em> The WorldCat developers appear to have blocked these kinds of wildcard searches, so we had to stop. These ranges are still TODO. You can help by scraping them for us! Example: <code class="text-xs break-all text-gray-600">{"other_meta_type":"todo_range_query","query":"7561719###","from_query":"756171####"}</code>.</li>
      </ul>
    </li>
  </ul>

  <p class="mb-4">
    2. <em>Edition and holding information.</em> To start answering the question “which rare books do we not yet have”, our incredibly talented and thorough volunteer “m” scraped holding information: how many and which libraries hold a particular item. Holding information can be requested either for “only the current edition”, or “all editions”. We used the latter, in order to cut down on the total number of requests. So we first requested lists of which records are considered the same “editions”, and then holding information for each “edition cluster”.
  </p>

  <ul class="list-inside mb-4 ml-4">
    <li class="list-disc"><em>briefrecords_json:</em> Edition scrapes returned records in this format. Like above, you can see in <code class="text-xs break-all text-gray-600">from_filenames</code> which edition scrapes they were from, e.g. <code class="text-xs break-all text-gray-600">"search_editions_response/1"</code> (which corresponds to the <em>search_editions_response</em> records below).
    </li>
    <li class="list-disc"><em>search_holdings_all_editions_response:</em> The actual list of libraries that hold a certain OCLC ID. Example: <code class="text-xs break-all text-gray-600">{"oclc_number":"0000000000001","type":"search_holdings_all_editions_response","from_filenames":["search_holdings_all_editions_response/1"],"record":{"totalHoldingCount":4,"holdings":[760,104020,87542,4688],"numPublicLibraries":1}}</code>. This corresponds to <code class="text-xs break-all text-gray-600">https://search.worldcat.org/api/search-holdings?oclcNumber=&lt;ID&gt;&amp;allEditions=true&amp;&lt;VARIOUS-OTHER-FIELDS&gt;</code> as found on the individual record page (<code class="text-xs break-all text-gray-600">https://search.worldcat.org/title/&lt;ID&gt;</code>).</li>
    <li class="list-disc"><em>search_holdings_summary_all_editions:</em> “Summary response” for a certain OCLC ID, containing the number of holdings and editions (easier to scrape than full holding information). Example: <code class="text-xs break-all text-gray-600">{"oclc_number":"0000000000069","type":"search_holdings_summary_all_editions","from_filenames":["search_holdings_summary_all_editions/69"],"record":{"oclc_number":69,"total_holding_count":448,"total_editions":15}}</code>. This corresponds to <code class="text-xs break-all text-gray-600">https://search.worldcat.org/api/search-holdings-summary?oclcNumber=&lt;ID&gt;&amp;allEditions=true</code> as found on the individual record page (<code class="text-xs break-all text-gray-600">https://search.worldcat.org/title/&lt;ID&gt;</code>).</li>
    <li class="list-disc"><em>other_metadata_type:</em> (like above)
      <ul class="list-inside mb-4 ml-4">
        <li class="list-disc"><em>search_editions_response:</em> Example: <code class="text-xs break-all text-gray-600">{"other_meta_type":"search_editions_response","query":"0005830191291","number_of_records":1,"len_brief_records":1}</code>. This corresponds to <code class="text-xs break-all text-gray-600">https://search.worldcat.org/api/search-editions/&lt;ID&gt;</code> as found on the “View all formats and editions” page (<code class="text-xs break-all text-gray-600">https://search.worldcat.org/formats-editions/&lt;ID&gt;</code>).</li>
        <li class="list-disc"><em>library:</em> Deduplicated library records as encountered in holding endpoints (therefore probably not complete). Example: <code class="text-xs break-all text-gray-600">{"other_meta_type":"library","registry_id":"0000000000004","record":{"oclcSymbol":"MWT","registryId":4,"institutionName":"Alabama A&amp;M University","institutionType":"ACADEMIC","alsoCalled":"J. F. Drake Memorial Learning Resources Center","street1":"4900 Meridian Street North","city":"Normal","state":"US-AL","postalCode":"35762","country":"US","latitude":34.78361,"longitude":-86.57018,"distance":413.2236760232868,"distanceUnit":"M"}}</code>.</li>
      </ul>
    </li>
  </ul>

  <p class="mb-4">
    <strong>January 2024, edition clusters confusion:</strong> in our last scrape, we scraped “edition clusters” (the <em>search_editions_response</em> records, which are represented part as <em>briefrecords_json</em> with “search_editions_response/&lt;ID&gt;” as the filename), and part as standalone <em>search_editions_response</em> records (with the full counts).
  </p>

  <p class="mb-4">
    We then only scraped one <em>search_holdings_summary_all_editions</em> record for each “edition cluster”, since we thought this would indeed cover exactly all the OCLC IDs in that cluster.
  </p>

  <p class="mb-4">
    However, it now seems that those two records don’t operate on the same set of OCLC IDs. For example, <a href="https://search.worldcat.org/formats-editions/1305021518">this page</a> (which corresponds to our <em>search_editions_response</em>) has many different languages merged into one. When looking at two books on that page, such as <a href="https://search.worldcat.org/title/37975719">this</a> and <a href="https://search.worldcat.org/title/46728744">this</a>, you can see that it shows different counts for “X editions in Y libraries” (when scrolling down a bit). Those counts correspond to our <em>search_holdings_summary_all_editions</em>. If our assumption was correct (both records operate on the same set of OCLC IDs), then those numbers should always be the same.
  </p>

  <p class="">
    We’ve tried to untangle this using OCLC documentation, without too much success:
  </p>

  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc"><a href="https://developer.api.oclc.org/worldcat-discovery#/Member%20General%20Holdings/find-bib-summary-holdings">This API</a> has two parameters, <code class="text-xs text-gray-600">holdingsAllEditions</code> and <code class="text-xs text-gray-600">holdingsAllVariantRecords</code>. What are “variant records”?</li>
    <li class="list-disc"><a href="https://help.oclc.org/Discovery_and_Reference/WorldCat_Discovery/Search_results/Representative_record_and_availability_display_on_grouped_search_results">“Variant records group records for the same edition which may have different languages of cataloging or may be duplicate records which have not yet been resolved.”</a> This sounds like variant records are nested under edition. But that would not make much sense. Maybe they just wrote it down in an awkward way?</li>
    <li class="list-disc">Also, “different languages of cataloging” might not mean different language of the BOOK, but of the metadata record itself?</li>
    <li class="list-disc">“Default Grouping” under <a href="https://help.oclc.org/Librarian_Toolbox/OCLC_Service_Configuration/WorldCat_Discovery_and_WorldCat_Local/005Search_Settings">Search Settings</a> is slightly clearer, but still confusing.</li>
    <li class="list-disc"><a href="https://help.oclc.org/Library_Management/WorldShare_Circulation/Holds_management/Work_with_holds/050View_holds">“If the Fulfill using variant records setting is enabled in Holds and Schedules, Settings, the system will store all OCLC numbers held by the library (or its circulation group) in the same edition cluster as the bibliographic record selected by the user. The list of OCLC numbers will be visible to library staff in WorldShare Circulation. Any item cataloged for the requested edition will be available to fulfil title-level hold requests.”</a> Here it sounds like a “variant record” is just an “edition cluster member”…</li>
    <li class="list-disc">We could investigate how often this happens. 1. is it limited to cases with multiple languages? 2. is it limited to cases with popular books and many editions? Our hypothesis is that for the rare books, none of this matters too much. Rare books won't be translated in many languages.</li>
  </ul>

  <p class="mb-4">
    Can someone clear up our understanding, and help determine if we need to expand our scrape of holdings?
  </p>

  <p class="font-bold">{{ gettext('page.datasets.common.resources') }}</p>
  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.datasets.common.last_updated', date=stats_data.oclc_date) }}</li>
    <li class="list-disc"><a href="/torrents#worldcat">{{ gettext('page.datasets.worldcat.torrents') }}</a></li>
    <li class="list-disc"><a href="/db/source_record/get_oclc_dicts/oclc/1.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="https://worldcat.org/">{{ gettext('page.datasets.common.main_website', source=gettext('page.datasets.worldcat.title')) }}</a></li>
    <li class="list-disc"><a href="/blog/worldcat-scrape.html">{{ gettext('page.datasets.worldcat.blog_announcement') }}</a></li>
    <li class="list-disc"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/data-imports">{{ gettext('page.datasets.common.import_scripts') }}</a></li>
    <li class="list-disc"><a href="/blog/annas-archive-containers.html">{{ gettext('page.datasets.common.aac') }}</a></li>
  </ul>
{% endblock %}
