{% extends "layouts/index.html" %}

{% block title %}{{ gettext('page.llm.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-1 text-3xl font-bold">{{ gettext('page.llm.title') }}</h2>

  <p class="mb-4">
    {{ gettext('page.llm.intro') }}
  </p>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.llm.unique_scale') }}</h3>
  
  <p class="mb-4">
    {{ gettext('page.llm.unique_scale.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.llm.unique_scale.text2', a_datasets=(' href="/datasets"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.llm.unique_scale.text3') }}
  </p>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.llm.how_we_can_help') }}</h3>

  <p class="mb-4">
    {{ gettext('page.llm.how_we_can_help.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.llm.how_we_can_help.text2') }}
  </p>

  <p class="">
    {{ gettext('page.llm.how_we_can_help.text3') }}
  </p>

  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.llm.how_we_can_help.ocr') }}</li>
    <li class="list-disc">{{ gettext('page.llm.how_we_can_help.deduplication') }}</li>
    <li class="list-disc">{{ gettext('page.llm.how_we_can_help.extraction') }}</li>
  </ul>

  <p class="mb-4">
    <em>{{ gettext('page.llm.how_we_can_help.text4') }}</em>
  </p>

  <p class="mb-4">
    {{ gettext('page.llm.how_we_can_help.text5', a_contact=(' href="/contact"' | safe)) }}
  </p>
{% endblock %}
