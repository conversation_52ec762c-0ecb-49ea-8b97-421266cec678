{% extends "layouts/index.html" %}

{% block title %}{{ gettext('page.ipfs_downloads.title') }}{% endblock %}

{% block meta_tags %}
  <meta property="robots" content="noindex" />
{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.ipfs_downloads.title') }}</h2>

  <p class="mb-4">
    {{ gettext('page.partner_download.main_page', a_main=((' href="' + original_path + '"') | safe)) }}
  </p>

  <ul class="mb-4">
    {% for url in ipfs_urls %}
      <li>- <a rel="noopener noreferrer nofollow" href="{{ url.url }}">{{ gettext('page.md5.box.download.ipfs_gateway', num=loop.index) }} {{ url.name }}</a> [{{ url.from }}] {% if loop.index == 1 %}{{ gettext('page.md5.box.download.ipfs_gateway_extra')}}{% endif %}</li>
    {% endfor %}
  </ul>

  <p class="mb-4">
    {{ gettext('page.partner_download.faster_downloads', a_membership=(' href="/donate"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.partner_download.bulk_mirroring', a_datasets=(' href="/datasets"' | safe), a_torrents=(' href="/torrents"' | safe)) }}
  </p>
{% endblock %}
