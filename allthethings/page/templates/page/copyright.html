{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.copyright.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.copyright.title') }}</h2>

  <p class="mb-4 bg-black/6.7 p-4 rounded">{{ gettext('page.copyright.intro', email=(a.email_dmca_link | safe)) }}</p>

  <form autocomplete="on" onsubmit="window.submitForm(event, '/dyn/copyright/')" class="mb-4">
    <fieldset class="mb-4">
      <p class="mb-1">
        <span>{{ gettext('page.copyright.form.aa_urls') }}</span>
        <strong>{{ gettext('page.copyright.form.aa_urls.note') }}</strong>
      </p>
      <textarea required name="aa_urls" class="w-full h-[150px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <p class="mb-1">{{ gettext('page.copyright.form.name') }}</p>
      <input required type="text" name="name" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full">

      <p class="mb-1">{{ gettext('page.copyright.form.address') }}</p>
      <input required type="text" name="address" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full">

      <p class="mb-1">{{ gettext('page.copyright.form.phone') }}</p>
      <input required type="text" name="phone" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full">

      <p class="mb-1">{{ gettext('page.copyright.form.email') }}</p>
      <input required type="email" name="email" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full">

      <p class="mb-1">{{ gettext('page.copyright.form.description') }}</p>
      <textarea required name="description" class="w-full h-[70px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <p class="mb-1">{{ gettext('page.copyright.form.isbns') }}</p>
      <textarea name="isbns" class="w-full h-[150px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <p class="mb-1">{{ gettext('page.copyright.form.openlib_urls', a_openlib=({"href": "https://openlibrary.org/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
      <textarea name="openlib" class="w-full h-[150px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <p class="mb-1">{{ gettext('page.copyright.form.external_urls') }}</p>
      <textarea required name="external_urls" class="w-full h-[150px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <p class="mb-1">{{ gettext('page.copyright.form.statement') }}</p>
      <textarea required name="statement" class="w-full h-[100px] bg-black/6.7 text-black p-2 mb-4 rounded"></textarea>

      <div>
        <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.copyright.form.submit_claim') }}</button>
        <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
      </div>
    </fieldset>
    <div class="hidden js-success">{{ gettext('page.copyright.form.on_success') }}</div>
    <div class="hidden js-failure">{{ gettext('page.copyright.form.on_failure') }}</div>
  </form>
{% endblock %}
