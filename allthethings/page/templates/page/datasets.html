{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-1 text-3xl font-bold">{{ gettext('page.datasets.title') }}</h2>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <p class="mb-4">
    {{ gettext('page.datasets.intro.text2') }}
  </p>

  <p class="mb-4">
    {{ gettext(
      'page.datasets.intro.text3',
      a_torrents=(a.torrents | xmlattr),
      a_anna_software=(a.anna_data_imports | xmlattr),
      a_elasticsearch=(a.torrents_derived_metadata | xmlattr),
      a_dbrecord=(a.example_metadata_record | xmlattr)
    ) }}
    <!-- TODO:TRANSLATE -->
    <a href="https://github.com/RArtutos/Data-science-starter-kit-Enhance/" rel="noopener noreferrer nofollow" target="_blank">This repo</a> is excellent for getting started with data analysis.
  </p>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.datasets.overview.title') }}</h3>

  <p class="mb-4">
    {{ gettext('page.datasets.overview.text1') }}
  </p>

  <table class="mb-4 w-full">
    <tr class="even:bg-[#f2f2f2]">
      <th class="p-2 align-bottom text-left" width="28%">{{ gettext('page.datasets.overview.source.header') }}</th>
      <th class="p-2 align-bottom text-left" width="20%">{{ gettext('page.datasets.overview.size.header') }}</th>
      <th class="p-2 align-bottom text-left" width="20%">{{ gettext('page.datasets.overview.mirrored.header') }}<div class="font-normal text-sm text-gray-500">{{ gettext('page.datasets.overview.mirrored.clarification') }}</div></th>
      <th class="p-2 align-bottom text-left" width="22%">{{ gettext('page.datasets.overview.last_updated.header') }}</th>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/lgrs">{{ gettext('common.record_sources_mapping.lgrs') }} [lgrs]</a>
        <div class="text-sm text-gray-500">{{ gettext('common.record_sources_mapping.lgrs.nonfiction_and_fiction') }}</div>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.lgrs.count, count=(stats_data.stats_by_group.lgrs.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.lgrs.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.lgrs.aa_count/(stats_data.stats_by_group.lgrs.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.lgrs.torrent_count/(stats_data.stats_by_group.lgrs.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.libgenrs_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/scihub">{{ gettext('common.record_sources_mapping.scihub') }} [scihub]</a>
        <div class="text-sm text-gray-500">{{ gettext('common.record_sources_mapping.scihub.via_lgli_scimag') }}</div>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.journals.count, count=(stats_data.stats_by_group.journals.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.journals.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.journals.aa_count/(stats_data.stats_by_group.journals.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.journals.torrent_count/(stats_data.stats_by_group.journals.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        <div class="text-sm text-gray-500 whitespace-normal font-normal">
          {{ gettext('page.datasets.scihub_frozen_1') }}<br>
          {{ gettext('page.datasets.scihub_frozen_2') }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/lgli">{{ gettext('common.record_sources_mapping.lgli') }} [lgli]</a>
        <div class="text-sm text-gray-500">{{ gettext('common.record_sources_mapping.lgli.excluding_scimag') }}</div>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.lgli.count, count=(stats_data.stats_by_group.lgli.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.lgli.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.lgli.aa_count/(stats_data.stats_by_group.lgli.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.lgli.torrent_count/(stats_data.stats_by_group.lgli.count+1)*100.0) | decimalformat }}%
        <div class="text-sm text-gray-500 whitespace-normal font-normal">{{ gettext('page.datasets.lgli_fiction_is_behind') }}</div>
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.libgenli_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/zlib">{{ gettext('common.record_sources_mapping.zlib') }} [zlib]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.zlib.count, count=(stats_data.stats_by_group.zlib.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.zlib.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.zlib.aa_count/(stats_data.stats_by_group.zlib.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.zlib.torrent_count/(stats_data.stats_by_group.zlib.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.zlib_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/zlibzh">{{ gettext('common.record_sources_mapping.zlibzh') }} [zlibzh]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.zlibzh.count, count=(stats_data.stats_by_group.zlibzh.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.zlibzh.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.zlibzh.aa_count/(stats_data.stats_by_group.zlibzh.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.zlibzh.torrent_count/(stats_data.stats_by_group.zlibzh.count+1)*100.0) | decimalformat }}%
        <div class="text-sm text-gray-500 whitespace-normal font-normal">{{ gettext('page.datasets.zlibzh.searchable') }}</div>
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.zlib_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/ia">{{ gettext('common.record_sources_mapping.iacdl') }} [ia]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.ia.count, count=(stats_data.stats_by_group.ia.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.ia.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.ia.aa_count/(stats_data.stats_by_group.ia.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.ia.torrent_count/(stats_data.stats_by_group.ia.count+1)*100.0) | decimalformat }}%
        <div class="text-sm text-gray-500 whitespace-normal font-normal">{{ gettext('page.datasets.iacdl.searchable') }}</div>
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.ia_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/duxiu">{{ gettext('common.record_sources_mapping.duxiu') }} [duxiu]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.duxiu.count, count=(stats_data.stats_by_group.duxiu.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.duxiu.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.duxiu.aa_count/(stats_data.stats_by_group.duxiu.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.duxiu.torrent_count/(stats_data.stats_by_group.duxiu.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.duxiu_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/upload">{{ gettext('common.record_sources_mapping.uploads') }} [upload]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.upload.count, count=(stats_data.stats_by_group.upload.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.upload.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.upload.aa_count/(stats_data.stats_by_group.upload.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.upload.torrent_count/(stats_data.stats_by_group.upload.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.upload_file_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/magzdb">{{ gettext('common.record_sources_mapping.magzdb') }} [magzdb]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.magzdb.count, count=(stats_data.stats_by_group.magzdb.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.magzdb.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.magzdb.aa_count/(stats_data.stats_by_group.magzdb.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.magzdb.torrent_count/(stats_data.stats_by_group.magzdb.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.magzdb_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/nexusstc">{{ gettext('common.record_sources_mapping.nexusstc') }} [nexusstc]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.nexusstc.count, count=(stats_data.stats_by_group.nexusstc.count|numberformat)) }}<br>
        {{ stats_data.stats_by_group.nexusstc.filesize | filesizeformat }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.nexusstc.aa_count/(stats_data.stats_by_group.nexusstc.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.nexusstc.torrent_count/(stats_data.stats_by_group.nexusstc.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.nexusstc_date }}
      </td>
    </tr>

    <!-- TODO:TRANSLATE -->
    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/hathi">HathiTrust [hathi]</a>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.hathi.count, count=(stats_data.stats_by_group.hathi.count|numberformat)) }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.hathi.aa_count/(stats_data.stats_by_group.hathi.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.hathi.torrent_count/(stats_data.stats_by_group.hathi.count+1)*100.0) | decimalformat }}% / {{ stats_data.stats_by_group.hathi.filesize | filesizeformat }}
        <div class="text-sm text-gray-500 whitespace-normal font-normal">We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/276">$30k bounty</a> if you can get the full collection, or a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/234">$200k bounty</a> if you can get the diverged Google Books collection.</div>
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ stats_data.hathitrust_file_date }}
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2] font-bold">
      <td class="p-2 align-top">
        {{ gettext('page.datasets.overview.total') }}
        <div class="text-sm font-normal text-gray-500">{{ gettext('page.datasets.overview.excluding_duplicates') }}</div>
      </td>
      <td class="p-2 align-top">
        {{ ngettext('page.datasets.file', 'page.datasets.files', stats_data.stats_by_group.total.count, count=(stats_data.stats_by_group.total.count|numberformat)) }}
      </td>
      <td class="p-2 align-top whitespace-nowrap">
        {{ (stats_data.stats_by_group.total.aa_count/(stats_data.stats_by_group.total.count+1)*100.0) | decimalformat }}% / {{ (stats_data.stats_by_group.total.torrent_count/(stats_data.stats_by_group.total.count+1)*100.0) | decimalformat }}%
      </td>
      <td class="p-2 align-top whitespace-nowrap"></td>
    </tr>
  </table>

  <p class="mb-4">
    {{ gettext('page.datasets.overview.text4') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.datasets.overview.text5') }}
  </p>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.datasets.source_libraries.title') }}</h3>

  <p class="mb-4">
    {{ gettext('page.datasets.source_libraries.text1', a_torrents=(' href="/torrents"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.datasets.source_libraries.text2') }}
  </p>

  <table class="mb-4 w-full">
    <tr class="even:bg-[#f2f2f2]">
      <th class="p-2 align-bottom text-left" width="20%">{{ gettext('page.datasets.sources.source.header') }}</th>
      <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.metadata.header') }}</th>
      <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.files.header') }}</th>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/lgrs">
          {{ gettext('common.record_sources_mapping.lgrs') }} [lgrs]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_rs.metadata1', icon='✅',
              dbdumps=(dict(href="https://data.library.bz/dbdumps/") | xmlattr),
          ) }}
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_rs.files1', icon='✅',
              nonfiction=(dict(href="https://libgen.is/repository_torrent/") | xmlattr),
              fiction=(dict(href="https://libgen.is/fiction/repository_torrent/") | xmlattr),
          ) }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_rs.files2', icon='👩‍💻',
              covers=(dict(href="/torrents#libgenrs_covers") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/scihub">
          {{ gettext('common.record_sources_mapping.scihub_scimag') }} [scihub]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.scihub.metadata1', icon='❌') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.scihub.metadata2', icon='✅',
              scihub1=(dict(href="https://sci-hub.ru/database") | xmlattr),
              scihub2=(dict(href="https://data.library.bz/dbdumps/") | xmlattr),
              libgenli=(dict(href="https://libgen.li/dirlist.php?dir=dbdumps") | xmlattr),
          ) }}
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.scihub.files1', icon='✅',
              scihub1=(dict(href="https://sci-hub.ru/database") | xmlattr),
              scihub2=(dict(href="https://libgen.is/scimag/repository_torrent/") | xmlattr),
              libgenli=(dict(href="https://libgen.li/torrents/scimag/") | xmlattr),
          ) }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.scihub.files2', icon='❌',
              libgenrs=(dict(href="https://libgen.is/scimag/recent") | xmlattr),
              libgenli=(dict(href="https://libgen.li/index.php?req=fmode:last&topics%5B%5D=a") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/lgli">
          {{ gettext('common.record_sources_mapping.lgli') }} [lgli]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_li.metadata1', icon='✅',
              dbdumps=(dict(href="https://libgen.li/dirlist.php?dir=dbdumps") | xmlattr),
          ) }}
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_li.files1', icon='✅',
              libgenli=(dict(href="https://libgen.li/torrents/libgen/") | xmlattr),
          ) }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.libgen_li.collab', icon='👩‍💻',
              comics=(dict(href="/torrents#libgen_li_comics") | xmlattr),
              magazines=(dict(href="/torrents#libgen_li_magazines") | xmlattr),
              standarts=(dict(href="/torrents#libgen_li_standarts") | xmlattr),
              fiction=(dict(href="/torrents#libgen_li_fiction") | xmlattr),
          ) }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.libgen_li.fiction_rus', icon='🙃',
            fiction_rus=(dict(href="/torrents#libgen_li_fiction_rus") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/zlib">
          {{ gettext('common.record_sources_mapping.zlib') }} [zlib/zlibzh]
        </a>
      </td>
      <td class="p-2 align-top" colspan="2">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.zlib.metadata_and_files', icon='👩‍💻',
              metadata=(dict(href="/torrents#zlib") | xmlattr),
              files=(dict(href="/torrents#zlib") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/ia">
          {{ gettext('common.record_sources_mapping.iacdl') }} [ia]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.ia.metadata1', icon='✅',
              openlib=(dict(href="https://openlibrary.org/developers/dumps") | xmlattr),
          ) }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.ia.metadata2', icon='❌') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.ia.metadata3', icon='👩‍💻',
              ia=(dict(href="/torrents#ia") | xmlattr),
          ) }}
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">{{ gettext('page.datasets.sources.ia.files1', icon='❌') }}</div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.ia.files2', icon='👩‍💻',
              ia=(dict(href="/torrents#ia") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/duxiu">
          {{ gettext('common.record_sources_mapping.duxiu') }} [duxiu]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.metadata1', icon='✅') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.metadata2', icon='❌') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.metadata3', icon='👩‍💻',
              duxiu=(dict(href="/torrents#duxiu") | xmlattr),
          ) }}
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.files1', icon='✅') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.files2', icon='❌') }}
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.duxiu.files3', icon='👩‍💻',
              duxiu=(dict(href="/torrents#duxiu") | xmlattr),
          ) }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/uploads">
          {{ gettext('common.record_sources_mapping.uploads') }} [uploads]
        </a>
      </td>
      <td class="p-2 align-top" colspan="2">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.uploads.metadata_and_files', icon='') }}
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/magzdb">
          MagzDB [magzdb]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ Appears defunct since July 2023.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ No easily accessible metadata dumps available for their entire collection.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive manages a collection of <a href="/torrents#magzdb">MagzDB metadata</a>.
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          ✅ Since MagzDB was a fork from Libgen.li magazines, a large part is covered by <a href="/torrents#libgen_li_magazines">those torrents</a>.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ No official torrents from MagzDB for their unique files.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive manages a collection of magzdb files as part of our <a href="/datasets/upload">upload collection</a> (the ones with “magzdb” in the filename).
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/nexusstc">
          Nexus/STC [nexusstc]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          ✅ Summa database available through IPFS, though can be slow to download or directly interact with.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive manages a collection of <a href="/torrents#nexusstc">Nexus/STC metadata</a>, through <a href="https://software.annas-archive.li/AnnaArchivist/stc-dump">this code</a>.
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          ✅ Data can be <a href="https://libstc.cc/#/help/replication">replicated through Iroh</a>.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ No mirroring by Anna’s Archive or partner servers yet.
        </div>
      </td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/hathi">
          HathiTrust [hathi]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          ✅ Daily <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/hathifiles/">database dumps</a>.
        </div>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive has the <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/research-datasets/#available-research-datasets" rel="noopener noreferrer nofollow" target="_blank">“ht_text_pd” public domain dataset</a>, and ~7% of the <a href="https://github.com/hathitrust/datasets?tab=readme-ov-file#superset-ht_text" rel="noopener noreferrer nofollow" target="_blank">“ht_text” private dataset</a>.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ Most files are closely guarded. We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/276">$30k bounty</a> if you can get the full collection.
        </div>
      </td>
    </tr>
  </table>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.datasets.metadata_only_sources.title') }}</h3>

  <p class="mb-4">
    {{ gettext('page.datasets.metadata_only_sources.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.metadata.inspiration',
        a_openlib=(dict(href="https://en.wikipedia.org/wiki/Open_Library") | xmlattr),
        a_blog=(dict(href="/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html") | xmlattr),
    ) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.datasets.metadata_only_sources.text2') }}
  </p>

  <table class="mb-4 w-full">
    <tr class="even:bg-[#f2f2f2]">
      <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.source.header') }}</th>
      <th class="p-2 align-bottom text-left w-[65%]">{{ gettext('page.datasets.sources.metadata.header') }}</th>
      <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.last_updated.header') }}</th>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/ol">
          {{ gettext('common.record_sources_mapping.ol') }} [ol]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.openlib.metadata1', icon='✅',
              dbdumps=(dict(href="https://openlibrary.org/developers/dumps") | xmlattr),
          ) }}.
        </div>
      </td>
      <td class="p-2 align-top">{{ stats_data.openlib_date }}</td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/oclc">
          {{ gettext('common.record_sources_mapping.oclc') }} [oclc]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.worldcat.metadata1', icon='❌') }}.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.worldcat.metadata2', icon='👩‍💻',
              worldcat=(dict(href="/torrents#worldcat") | xmlattr),
          ) }}.
        </div>
      </td>
      <td class="p-2 align-top">{{ stats_data.oclc_date }}</td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/gbooks">
          Google Books [gbooks]
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          {{ gettext('page.datasets.sources.worldcat.metadata1', icon='❌') }}.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive manages a collection of <a href="/torrents#gbooks">Google Books metadata</a>.
        </div>
        <div class="my-2 first:mt-0 last:mb-0">
          ❌ Most files are closely guarded. We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/234">$200k bounty</a> if you can get the full collection.
        </div>
      </td>
      <td class="p-2 align-top">{{ stats_data.gbooks_record_date }}</td>
    </tr>

    <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top">
        <a class="custom-a underline hover:opacity-60" href="/datasets/other_metadata">
          Other metadata scrapes
        </a>
      </td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">
          👩‍💻 Anna’s Archive manages scrapes of metadata from other sources.
        </div>
      </td>
      <td class="p-2 align-top">Varies</td>
    </tr>

    <!-- <tr class="even:bg-[#f2f2f2]">
      <td class="p-2 align-top"><a class="custom-a underline hover:opacity-60" href="/datasets/isbn_ranges">ISBN country information</a></td>
      <td class="p-2 align-top">
        <div class="my-2 first:mt-0 last:mb-0">✅ Available for <a href="https://www.isbn-international.org/range_file_generation">automatic generation</a>.</div>
      </td>
      <td class="p-2 align-top">{{ stats_data.isbn_country_date }}</td>
    </tr> -->
  </table>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.datasets.unified_database.title') }}</h3>

  <p class="mb-4">
    {{ gettext(
      'page.datasets.unified_database.text1',
      a_generated=(a.anna_data_imports | xmlattr),
      a_downloaded=(a.torrents_derived_metadata | xmlattr),
    ) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.datasets.unified_database.text2', a_json=(a.example_metadata_record | xmlattr)) }}
  </p>
{% endblock %}
