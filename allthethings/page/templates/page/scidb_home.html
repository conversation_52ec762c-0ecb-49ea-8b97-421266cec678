{% extends "layouts/index.html" %}

{% block title %}🧬 {{ gettext('page.home.scidb.header') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-1 text-3xl font-bold">🧬&nbsp;{{ gettext('page.home.scidb.header') }}</h2>

  <p class="mb-4">
    {{ gettext('page.home.scidb.scihub_paused', a_paused=(' href="https://www.reddit.com/r/scihub/comments/lofj0r/announcement_scihub_has_been_paused_no_new/" target="_blank" rel="noopener noreferrer nofollow"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.home.scidb.text2') }}
  </p>

  <div class="mb-4">
    <form action="/scidb/" method="get" onsubmit='window.location="/scidb/" + new FormData(event.currentTarget).get("doi"); event.preventDefault(); return false'>
      <div class="flex max-w-[600px]">
        <input required pattern=".*10\..+" name="doi" type="search" placeholder="{{ gettext('page.home.scidb.placeholder_doi') }}" class="grow bg-black/6.7 px-2 py-1 mr-2 rounded" value="{{ doi_input or '' }}">
        <button class="px-4 py-1 bg-[#0195ff] text-white rounded hover:bg-blue-600" type="submit">{{ gettext('page.home.scidb.open') }}</button>
      </div>
    </form>
  </div>

  <p class="mb-4">
    {{ gettext('page.home.scidb.text3') }}
  </p>
{% endblock %}
