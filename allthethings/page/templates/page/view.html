{% extends "layouts/index.html" %}
{% block darkreader_code_block %}{% endblock %}
{% block title %}{{"Viewer"}}{% endblock %}

{% block stylesheets %}<link rel="stylesheet" href="/villainjs/style.css">{% endblock %}

{% block meta_tags %}
  <!-- TODO:TRANSLATE (entire page) -->
  <meta name="description" content="View Files" />
{% endblock %}

{% block main %}
  <script src="/react/react.production.min.js"></script>
  <script src="/react/react-dom.production.min.js"></script>
  <script src="/react/reakit.min.js"></script>
  <script src="/djvujs/djvu.js"></script>
  <script src="/djvujs/djvu_viewer.js"></script>
  <script>window.reakit = window.Reakit;</script>
  <script src="/villainjs/villain.js"></script>
  <script src="/zipjs/dist/zip.min.js"></script>
  <script>
    const supportedExtensions = {{ viewer_supported_extensions | tojson }};

    function parseUrl(url) {
      // Remote URL might have query params
      try {
        const parsedUrl = new URL(url);
        return parsedUrl.pathname;
      } catch (e) {
        return url;
      }
    }

    
    function attachFrameListener() {
      const iframe = document.querySelector(".viewer-frame");
      const contentWindow = iframe.contentWindow;

      contentWindow.addEventListener("unhandledrejection", e => {
        displayError(e.reason.message);
      });
      contentWindow.addEventListener("error", e => {
        if (e.error) {
          displayError(e.error.message);
        } else {
          displayError(e.message);
        }
      });
      // in reality, only kthoom + pdf.js support re-drag and drop, but better to disable for all iframes
      contentWindow.EventTarget.prototype.addEventListener = new Proxy(contentWindow.EventTarget.prototype.addEventListener, {
        apply(target, that, args) {          
          if (args[0] === "drop") {
            return;
          }
          return Reflect.apply(...arguments);
        }
      });
    }

    function loadWithVillain(file) {
      const Villain = window.villain;
      const props = { 
        source: file, // url or blob
        style: { width: "100%", height: "100%" },
        options: {
          allowFullScreen: true,
          autoHideControls: false,
        },
        workerUrl: "/libarchivejs/libarchivejs-1.3.0/dist/worker-bundle.js"
      };

      const root = ReactDOM.createRoot(document.body);
      root.render(React.createElement(Villain, props));
    }

    async function loadZip(fileUrl) {
      const blob = await (await fetch(fileUrl)).blob()
      const reader = new zip.ZipReader(new zip.BlobReader(blob));

      let entries = await reader.getEntries();
      entries.sort((a, b) => a.filename.localeCompare(b.filename));
      if (entries.length === 0) {
        displayError("Zip file is empty");
        return;
      }
      if (entries.some(e => !e.filename.endsWith(".txt"))) {
        loadWithVillain(blob);
        return;
      }

      document.getElementById("viewer-container").innerHTML = "";

      const loadingContainer = document.createElement("div");
      loadingContainer.className = "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 rounded border";
      const loadingBar = document.createElement("div");
      loadingBar.className = "h-2 bg-gray-600 w-0 transition-all";
      loadingContainer.appendChild(loadingBar);
      document.body.appendChild(loadingContainer);

      const outputDiv = document.createElement("div");
      outputDiv.className = "font-mono whitespace-pre-wrap text-black p-4 w-full max-w-full mx-auto overflow-auto rounded-md break-words flex justify-center text-base";
      document.body.appendChild(outputDiv);
      const innerContainer = document.createElement("div");
      innerContainer.className = "w-full max-w-3xl";
      outputDiv.appendChild(innerContainer);

      let innerHTML = "";
      for (const [idx, entry] of entries.entries()) {
        const text = await entry.getData(new zip.TextWriter());
        if (!text.trim()) {
          continue;
        }
        innerHTML += text;
        innerHTML += `<hr class="my-4"></hr>`;
        loadingBar.style.width = `${((idx + 1) / entries.length) * 100}%`;
      }
      
      // buffer time so user sees loading bar full
      setTimeout(() => {
        innerContainer.innerHTML = innerHTML;
        loadingContainer.remove();
      }, 250);
    }

    async function loadViewerByUrl(fileUrl, fileType) {
      if (!fileType) {
        const parsedUrl = parseUrl(encodeURI(fileUrl));
        fileType = parsedUrl.split(".").pop();
      }
      
      const viewerContainer = document.getElementById("viewer-container");
      if (supportedExtensions["pdfjs"].includes(fileType)) {
        viewerContainer.innerHTML = `<iframe src="/pdfjs/web/viewer.html?file=${encodeURI(fileUrl)}" title="webviewer" frameborder="0" class="viewer-frame w-full h-full"></iframe>`;
        attachFrameListener()
      } else if (supportedExtensions["djvujs"].includes(fileType)) {
        const viewer = new DjVu.Viewer();
        viewer.render(document.body);
        viewer.loadDocumentByUrl(fileUrl);
      } else if (supportedExtensions["foliatejs"].includes(fileType)) {
        viewerContainer.innerHTML = `<iframe id="foliate-iframe" src="/foliatejs/reader.html?url=${encodeURI(fileUrl)}" title="webviewer" frameborder="0" class="viewer-frame w-full h-full"></iframe>`;
        attachFrameListener()
      } else if (supportedExtensions["kthoom"].includes(fileType)) {
        viewerContainer.innerHTML = `<iframe src="/kthoom/index.html?bookUri=${encodeURI(fileUrl)}" title="webviewer" frameborder="0" class="viewer-frame w-full h-full"></iframe>`;
        attachFrameListener()
      } else if (supportedExtensions["villainjs"].includes(fileType)) {
        if (fileType == "zip") {
          await loadZip(fileUrl);
          return;
        }
        loadWithVillain(fileUrl);
      } else {
        displayError("File type not supported");
      }
    }

    function getBuffer(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(reader.error);
        reader.readAsArrayBuffer(file);
      });
    }

    async function loadDjvuByFile(file) {
      const viewer = new DjVu.Viewer();
      const buffer = await getBuffer(file);
      viewer.render(document.body);
      viewer.loadDocument(buffer);
    }

    // Monkey patched foliate.js function
    window.fileInfoForMonkeyPatchedFetchFile = {};
    window.fetchFile = async url => {
      const res = await fetch(url);
      if (!res.ok) throw new ResponseError(`${res.status} ${res.statusText}`, { cause: res });
    
      const fileInfo = window.fileInfoForMonkeyPatchedFetchFile;
      if (url.startsWith("blob:") && "name" in fileInfo && "type" in fileInfo) {
        return new File([await res.blob()], fileInfo.name, {type: fileInfo.type});
      } else {
        return new File([await res.blob()], new URL(res.url).pathname);
      }
    }

    window.onmessage = e => {
      const iframe = document.getElementById("foliate-iframe");
      if (e.data === "refocus-iframe" && iframe) {
        iframe.contentWindow.focus();
      }
    };

    function displayError(error) {
      const popup = document.getElementById("error-popup");
      popup.classList.remove("hidden");

      const popupMsg = document.getElementById("error-message");
      popupMsg.innerText = error;

      console.log("displayError full error object:", error);
    }

    window.addEventListener("error", e => {
      displayError(e.error.message);
    })

    window.addEventListener("unhandledrejection", e => {
      displayError(e.reason.message);
    })
  </script>

  <div id="error-popup" class="fixed inset-0 flex items-center justify-center backdrop-blur-md hidden">
    <div class="bg-gray-50 border border-gray-300 p-6 rounded-lg shadow-lg w-96">
      <div class="text-lg font-semibold mb-4">Error opening this file</div>
      <div class="leading-tight mb-4">
        <!-- TODO:TRANSLATE -- separate language strings for each sentence. -->
        It’s possible there is something wrong with this file.
        Try opening it in a different application.
        If you still have issues, please report it on the file’s page on Anna’s Archive.
      </div>
      <p id="error-message" class="text-xs text-gray-500 mt-2 font-mono overflow-auto"></p>
    </div>
  </div>

  <div id="viewer-container" class="flex flex-row w-full h-full items-center justify-center">
    {% if url %}
      <script>(async () => {await loadViewerByUrl("{{ url }}")})()</script>
    {% else %} 
      <div id="drop-area" class="p-16 m-4 cursor-pointer border-2 border-dashed border-gray-300 rounded-lg text-center bg-gray-50 hover:bg-gray-100">
        <div class="mb-4">Drop file here to open (or click)</div>
        <div class="text-gray-500 text-sm">Supported files: {{ viewer_supported_extensions.values() | sum(start=[]) | join(', ') }}</div>
        <input type="file" id="file-upload" class="hidden" accept="{% for ext in viewer_supported_extensions.values() | sum(start=[]) %}.{{ ext }}{% if not loop.last %},{% endif %}{% endfor %}"/>
      </div>
      <script>
        document.addEventListener("DOMContentLoaded", () => {
          const dropArea = document.getElementById("drop-area");
          const fileInput = document.getElementById("file-upload");

          dropArea.addEventListener("dragover", e => {
            e.preventDefault();
            dropArea.classList.replace("bg-gray-50", "bg-gray-100");
          });

          dropArea.addEventListener("dragleave", () => {
            dropArea.classList.replace("bg-gray-100", "bg-gray-50");
          });

          dropArea.addEventListener("drop", e => {
            e.preventDefault();
            dropArea.classList.replace("bg-gray-100", "bg-gray-50");
            const files = e.dataTransfer.files;
            if (files.length > 1) {
              displayError("Please upload only one file.");
              return
            }
            if (files.length > 0) {
              handleFileUpload(files[0]);
            }
          });

          dropArea.addEventListener("click", () => fileInput.click());

          fileInput.addEventListener("change", e => {
            const files = fileInput.files;
            if (files.length > 0) {
              handleFileUpload(fileInput.files[0]);
            }
          });

          async function handleFileUpload(file) {
            const fileType = file.name.split(".").pop();
            if (supportedExtensions["djvujs"].includes(fileType)) {
              await loadDjvuByFile(file);
            } else {
              let fileUrl = URL.createObjectURL(file);
              window.fileInfoForMonkeyPatchedFetchFile = {
                name: file.name,
                type: file.type
              };
              await loadViewerByUrl(fileUrl, fileType);
            }
          }
        });
      </script>
    {% endif %}
  </div>
{% endblock %}
