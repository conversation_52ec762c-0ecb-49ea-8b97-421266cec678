{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }} ▶ HathiTrust{% endblock %}

{% block body %}
  <div class="mb-4"><a href="/datasets">{{ gettext('page.datasets.title') }}</a> ▶ HathiTrust</div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    <div class="text-xs mb-2">Overview from <a href="/datasets">datasets page</a>.</div>
    <table class="w-full mx-[-8px]">
      <tr class="even:bg-[#f2f2f2]">
        <th class="p-2 align-bottom text-left" width="20%">{{ gettext('page.datasets.sources.source.header') }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.metadata.header') }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.files.header') }}</th>
      </tr>

      <tr class="even:bg-[#f2f2f2]">
        <td class="p-2 align-top">
          <a class="custom-a underline hover:opacity-60" href="/datasets/hathi">
            HathiTrust [hathi]
          </a>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            ✅ Daily <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/hathifiles/">database dumps</a>.
          </div>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            👩‍💻 Anna’s Archive has the <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/research-datasets/#available-research-datasets" rel="noopener noreferrer nofollow" target="_blank">“ht_text_pd” public domain dataset</a>, and ~7% of the <a href="https://github.com/hathitrust/datasets?tab=readme-ov-file#superset-ht_text" rel="noopener noreferrer nofollow" target="_blank">“ht_text” private dataset</a>.
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            ❌ Most files are closely guarded. We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/276">$30k bounty</a> if you can get the full collection.
          </div>
        </td>
      </tr>
    </table>
  </div>

  <p class="mb-4">
    <a href="https://en.wikipedia.org/wiki/HathiTrust">HathiTrust</a> was started in 2008 when Google had started scanning books (which they then made available to the libraries), in order to share these scans between libraries. It is a substantial subset of the <a href="/datasets/gbooks">Google Books scans</a>, though it now also has some scans of its own.
  </p>

  <p class="mb-4">
    Their metadata is public (and integrated into Anna’s Archive), showing that as of early 2025 they have about 18 million scans. This is a lot less than the more than 40 million scans that Google <a href="https://blog.google/products/search/15-years-google-books/">claimed</a> in 2019, but still a large number.
  </p>

  <p class="mb-4">
    We managed to get the <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/research-datasets/#available-research-datasets" rel="noopener noreferrer nofollow" target="_blank">“ht_text_pd” dataset</a> in March 2025. While this is a public-domain dataset, it’s still closely guarded.
  </p>

  <p class="mb-4">
    In June 2025 we managed to get about 750k files (~7%) from the private <a href="https://github.com/hathitrust/datasets?tab=readme-ov-file#superset-ht_text" rel="noopener noreferrer nofollow" target="_blank">“ht_text” dataset</a>.
  </p>

  <p class="mb-4">
    We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/276">$30k bounty</a> if you can get the full collection.
  </p>

  <p class="font-bold">{{ gettext('page.datasets.common.resources') }}</p>
  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.datasets.common.total_files', count=(stats_data.stats_by_group.hathi.count | numberformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.mirrored_file_count', count=(stats_data.stats_by_group.hathi.aa_count | numberformat), percent=((stats_data.stats_by_group.hathi.aa_count/(stats_data.stats_by_group.hathi.count+1)*100.0) | decimalformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.last_updated', date=stats_data.hathitrust_file_date) }}</li>
    <li class="list-disc"><a href="/torrents#hathitrust">Torrents by Anna’s Archive</a></li>
    <li class="list-disc"><a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/hathifiles/">Daily database dumps</a></li>
    <li class="list-disc"><a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/research-datasets/#available-research-datasets">ht_text_pd research dataset</a></li>
    <li class="list-disc"><a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/hathifiles/hathifiles-description/">Hathifiles metadata fields</a></li>
    <li class="list-disc"><a href="/db/source_record/get_aac_hathi_book_dicts/hathi_id/aeu.ark:/13960/t3tt5cr6j.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="/db/aac_record/aacid__hathitrust_records__20230505T141431Z__WB2SiCfx5q4DJETuByMSd4.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="/db/aac_record/aacid__hathitrust_files__20250227T120812Z__22GT7yrb3SpiFbNagtGGv8.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/data-imports">{{ gettext('page.datasets.common.import_scripts') }}</a></li>
    <li class="list-disc"><a href="/blog/annas-archive-containers.html">{{ gettext('page.datasets.common.aac') }}</a></li>
  </ul>
{% endblock %}
