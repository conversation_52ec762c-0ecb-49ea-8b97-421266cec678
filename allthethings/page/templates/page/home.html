{% extends "layouts/index.html" %}

{% block title %}Anna’s Archive: LibGen (Library Genesis), Sci-Hub, Z-Library in one place{% endblock %}

{% block body %}

<div class="max-lg:max-w-[450px] max-lg:mx-auto lg:flex lg:flex-wrap lg:justify-between">
  <div class="lg:w-[485px]">
    <h2 class="mt-4 text-xl font-bold">📚 {{ gettext('page.home.full_database.header') }}</h2>

    <form action="/search" method="get" role="search">
      <div class="mb-1 text-sm text-gray-500">{{ gettext('page.home.full_database.subtitle') }}</div>
      <div class="flex max-w-[600px]">
        <input name="q" type="search" placeholder="" class="js-slash-focus grow bg-black/6.7 px-2 py-1 mr-2 rounded">
        <button class="px-4 py-1 bg-[#0195ff] text-white rounded hover:bg-blue-600" type="submit">{{ gettext('page.home.full_database.search') }}</button>
      </div>
    </form>

    <h2 class="mt-8 text-xl font-bold">🧬&nbsp;{{ gettext('page.home.scidb.header') }}<!-- <span class="mr-1 bg-[#0195ff] text-white text-xs font-medium px-1 py-0.5 align-[2px] rounded">{{ gettext('layout.index.header.nav.beta') }}</span>--></h2>

    <p class="mb-1">
      {{ gettext('page.home.scidb.scihub_paused', a_paused=(' href="https://www.reddit.com/r/scihub/comments/lofj0r/announcement_scihub_has_been_paused_no_new/" target="_blank" rel="noopener noreferrer nofollow"' | safe)) }}
      {{ gettext('page.home.scidb.continuation') }}
      {{ gettext('page.home.scidb.subtitle', count=g.header_stats.journal_article) }}.
      <a class="text-xs" href="/scidb">{{ gettext('layout.index.header.learn_more') }}</a>
    </p>

    <form action="/scidb/" method="get" onsubmit='window.location="/scidb/" + new FormData(event.currentTarget).get("doi"); event.preventDefault(); return false'>
      <div class="flex max-w-[600px]">
        <input required pattern=".*10\..+" name="doi" type="search" placeholder="{{ gettext('page.home.scidb.placeholder_doi') }}" class="grow bg-black/6.7 px-2 py-1 mr-2 rounded">
        <button class="px-4 py-1 bg-[#0195ff] text-white rounded hover:bg-blue-600" type="submit">{{ gettext('page.home.scidb.open') }}</button>
      </div>
      <!-- <div class="mb-1 text-sm text-gray-500">{{ gettext('page.home.scidb.browser_verification', a_member=(' href="/donate" ' | safe)) }}</div> -->
      <!-- <div class="mb-1 text-sm text-gray-500">Contains all of Sci-Hub, and more. <a class="" href="/scidb">{{ gettext('layout.index.header.learn_more') }}</a></div> -->
    </form>

    <h2 class="mt-8 text-xl font-bold">🏛️ {{ gettext('page.home.archive.header') }}</h2>

    <p class="mb-4">
      {{ gettext('page.home.archive.body', a_datasets=(' href="/datasets" ' | safe)) }}
    </p>

    <div class="mt-4 -mx-2 bg-yellow-100 p-2 rounded text-sm">
      <p class="mb-1">{{ gettext('page.home.torrents.body', a_torrents=(' href="/torrents" ' | safe)) }}</p>

      <table class="mb-1 text-sm">
        <tr><td>🔴 {{ torrents_data.seeder_size_strings[0] }}</td><td class="text-xs text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_less', count=4) }}</td></tr>
        <tr><td>🟡 {{ torrents_data.seeder_size_strings[1] }}</td><td class="text-xs text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_range', count_min=4, count_max=10) }}</td></tr>
        <tr><td>🟢 {{ torrents_data.seeder_size_strings[2] }}</td><td class="text-xs text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_greater', count=10) }}</td></tr>
      </table>
    </div>
  </div>

  <div class="lg:w-[485px]">
    {% if g.domain_lang_code == 'zh' %}
      <!-- <p class="mt-8 -mx-2 bg-yellow-100 p-2 rounded text-sm">
        [广告] 还在担心打不开各种学术网站？嘎嘎快加速器，加速全球，又快又稳。<a target="_blank" href="https://sososofast.com/">立即试用。</a>使用优惠码“annas-archive”在结账时立享9折优惠！
      </p> -->
    {% endif %}

    <!-- <h2 class="mt-8 text-xl font-bold">🤖 {{ gettext('page.home.llm.header') }}</h2>

    <p class="mb-4">
      {{ gettext('page.home.llm.body', a_llm=(' href="/llm" ' | safe)) }}
    </p> -->

    <!-- <h2 class="mt-8 text-xl font-bold">{{ gettext('page.home.mirrors.header') }}</h2> -->

    <h2 class="mt-8 text-xl font-bold">{{ gettext('page.home.volunteering.header') }}</h2>
    <p class="mb-4">
      {{ gettext('page.home.volunteering.help_out') }} <a class="text-sm" href="/volunteering">{{ gettext('layout.index.header.learn_more') }}</a>  
    </p>

    {% if g.domain_lang_code == 'zh' %}
      <!-- Volunteers -->
      <!-- <p class="mt-8 bg-yellow-100 p-2 rounded text-sm">
        我们正在寻找能够流利地说英语和中文的志愿者，帮助我们创建一个非官方微信群，以便人们可以及时了解我们的最新动态。如果您对保护人类知识的兴趣，请联系我们。谢谢！<a class="text-xs" href="/contact">{{ gettext('page.contact.title') }}</a> <span class="block text-xs text-gray-500">我们还在寻找能够让我们保持匿名的专业支付宝/微信支付处理器，使用加密货币。</span>
      </p> -->

      <!-- GFW, payment processors, ads -->
      <p class="mt-8 -mx-2 bg-yellow-100 p-2 rounded text-sm">
        我们正在寻找专业服务，可以帮助可靠地绕过GFW，例如通过设置定期更改的代理和域名，或其他技巧。如果您确实具有此方面的实际专业经验，请与我们联系。<strong>我们愿意付出酬劳。</strong><a class="text-xs" href="/contact">{{ gettext('page.contact.title') }}</a> <span class="block text-xs text-gray-500">我们还在寻找能够让我们保持匿名的专业支付宝/微信支付处理器，使用加密货币。此外，我们正在寻找希望放置小而别致广告的公司。</span>
      </p>

      <!-- <p class="mt-8 -mx-2 bg-yellow-100 p-2 rounded text-sm">
        Anna's Archive收购了一批独特的750万/350TB中文非虚构图书，比Library Genesis还要大。我们愿意为LLM公司提供独家早期访问权限，以换取高质量的OCR和文本提取。<a class="text-xs" href="/blog/duxiu-exclusive-chinese.html">了解更多</a>
      </p> -->
    {% else %}
      <p class="mt-8 -mx-2 bg-yellow-100 p-2 rounded text-sm">
        {{ gettext('page.home.payment_processor.body') }} <a class="text-xs" href="/contact">{{ gettext('page.contact.title') }}</a>
      </p>

      <!-- <p class="mt-8 -mx-2 bg-yellow-100 p-2 rounded text-sm">
        Anna’s Archive acquired a unique collection of 7.5 million / 350TB non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction. <a class="text-xs" href="/blog/duxiu-exclusive.html">Learn more…</a>
      </p> -->
    {% endif %}

    <h2 class="mt-8 text-xl font-bold">📄 {{ gettext('layout.index.header.nav.annasblog') | replace('↗', '') }}</h2>

    <table cellpadding="0" cellspacing="0" style="border-collapse: collapse;">
    <!-- TODO:TRANSLATE -->
    <tr style="background: #f2f2f2">
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;"><a href="/blog/all-isbns-winners.html">Winners of the $10,000 ISBN visualization bounty</a></td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2025-02-24</td>
    </tr>
    <tr>
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;"><a href="/blog/ai-copyright.html">Copyright reform is necessary for national security</a></td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2025-01-31</td>
    </tr>
    <tr style="background: #f2f2f2">
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;"><a href="/blog/all-isbns.html">Visualizing All ISBNs — $10k by 2025-01-31</a></td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2024-12-15</td>
    </tr>
    <tr>
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;">{% if g.domain_lang_code == 'zh' %}<a href="/blog/critical-window-chinese.html">海盗图书馆的关键时期</a>{% else %}<a href="/blog/critical-window.html">The critical window of shadow libraries</a>{% endif %}</td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2024-07-16</td>
    </tr>
    <tr style="background: #f2f2f2">
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;">{% if g.domain_lang_code == 'zh' %}<a href="/blog/duxiu-exclusive.html">独家访问：全球最大的中文非虚构图书馆藏，仅限LLM公司使用</a>{% else %}<a href="/blog/duxiu-exclusive.html">Exclusive access for LLM companies to largest Chinese non-fiction book collection in the world</a>{% endif %}</td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2023-11-04</td>
    </tr>
    <tr>
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;"><a href="/blog/worldcat-scrape.html">1.3B WorldCat scrape</a></td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2023-10-03</td>
    </tr>
    <tr style="background: #f2f2f2">
      <td style="padding: 4px; vertical-align: top; margin: 0 8px;"><a href="/blog/annas-archive-containers.html">Anna’s Archive Containers (AAC): standardizing releases from the world’s largest shadow library</a></td>
      <td style="padding: 4px; white-space: nowrap; vertical-align: top;">2023-08-15</td>
    </tr>
    </table>

    <p class="mb-4 mt-1">
      <a class="text-sm" href="/blog">{{ gettext('layout.index.header.learn_more') }}</a>
    </p>
  </div>
</div>

{% endblock %}
