{% extends "layouts/index.html" %}

{% block title %}{{ gettext('page.mirrors.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-1 text-3xl font-bold">{{ gettext('page.mirrors.title') }}</h2>

  <p class="mb-4">
    {{ gettext('page.mirrors.intro') }}
  </p>

  <p class="">
    {{ gettext('page.mirrors.text1') }}
  </p>

  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.mirrors.list.run_anna') }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.list.clearly_a_mirror') }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.list.know_the_risks', a_shadow=(' href="/blog/how-to-run-a-shadow-library.html"' | safe), a_pirate=(' href="/blog/blog-how-to-become-a-pirate-archivist.html"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.list.willing_to_contribute', a_codebase=(' href="https://software.annas-archive.li/"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.list.maybe_partner') }}</li>
  </ul>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.mirrors.expenses.title') }}</h3>

  <p class="mb-4">
    {{ gettext('page.mirrors.expenses.text1') }}
  </p>

  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.mirrors.expenses.must_demonstrate_ability') }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.expenses.no_compensation_for_time') }}</li>
    <li class="list-disc">{{ gettext('page.mirrors.expenses.maybe_donation') }}</li>
  </ul>

  <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.mirrors.getting_started.title') }}</h3>

  <p class="mb-4">
    {{ gettext('page.mirrors.getting_started.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.mirrors.getting_started.text2') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.mirrors.getting_started.text3') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.mirrors.getting_started.text4') }}
  </p>
{% endblock %}
