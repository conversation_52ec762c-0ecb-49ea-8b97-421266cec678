{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }} ▶ {{ gettext('page.datasets.duxiu.title') }} [duxiu]{% endblock %}

{% block body %}
  <div class="mb-4"><a href="/datasets">{{ gettext('page.datasets.title') }}</a> ▶ {{ gettext('page.datasets.duxiu.title') }} [duxiu]</div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    <div class="text-xs mb-2">Overview from <a href="/datasets">datasets page</a>.</div>
    <table class="w-full mx-[-8px]">
      <tr class="even:bg-[#f2f2f2]">
        <th class="p-2 align-bottom text-left" width="20%">{{ gettext('page.datasets.sources.source.header') }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.metadata.header') }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext('page.datasets.sources.files.header') }}</th>
      </tr>

      <tr class="even:bg-[#f2f2f2]">
        <td class="p-2 align-top">
          <a class="custom-a underline hover:opacity-60" href="/datasets/duxiu">
            {{ gettext('common.record_sources_mapping.duxiu') }} [duxiu]
          </a>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.metadata1', icon='✅') }}
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.metadata2', icon='❌') }}
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.metadata3', icon='👩‍💻',
                duxiu=(dict(href="/torrents#duxiu") | xmlattr),
            ) }}
          </div>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.files1', icon='✅') }}
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.files2', icon='❌') }}
          </div>
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.duxiu.files3', icon='👩‍💻',
                duxiu=(dict(href="/torrents#duxiu") | xmlattr),
            ) }}
          </div>
        </td>
      </tr>
    </table>
  </div>

  <p class="mb-4 italic">
    {{ gettext('page.datasets.duxiu.see_blog_post', a_href=(dict(href="/blog/duxiu-exclusive.html") | xmlattr)) }}
  </p>

  <p class="mb-4">
    {{ gettext(
      'page.datasets.duxiu.description',
      duxiu_link=(dict(href="https://www.duxiu.com/bottom/about.html") | xmlattr),
      superstar_link=(dict(href="https://www.chaoxing.com/") | xmlattr),
      princeton_link=(dict(href="https://library.princeton.edu/eastasian/duxiu") | xmlattr),
      uw_link=(dict(href="https://guides.lib.uw.edu/c.php?g=341344&p=2303522") | xmlattr),
      article_link=(dict(href="/scidb/10.1016/j.acalib.2009.03.012?scidb_verified=1") | xmlattr),
    ) }}
  </p>

  <p class="mb-4">
    {{ gettext(
      'page.datasets.duxiu.description2', 
      link1=(dict(href="https://github.com/duty-machine/duty-machine/issues/2010") | xmlattr),
      link2=(dict(href="https://github.com/821/821.github.io/blob/7bbcdc8dd2ec4bb637480e054fe760821b4ad7b8/_Notes/IT/DX-CX.md") | xmlattr),
    ) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.datasets.duxiu.description3') }}
  </p>

  <p class="font-bold">{{ gettext('page.datasets.common.resources') }}</p>
  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.datasets.common.total_files', count=(stats_data.stats_by_group.duxiu.count | numberformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.total_filesize', size=(stats_data.stats_by_group.duxiu.filesize | filesizeformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.mirrored_file_count', count=(stats_data.stats_by_group.duxiu.aa_count | numberformat), percent=((stats_data.stats_by_group.duxiu.aa_count/(stats_data.stats_by_group.duxiu.count+1)*100.0) | decimalformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.last_updated', date=stats_data.duxiu_date) }}</li>
    <li class="list-disc"><a href="/torrents#duxiu">{{ gettext('page.datasets.common.aa_torrents') }}</a></li>
    <li class="list-disc"><a href="/db/source_record/get_duxiu_dicts/md5/1636dce8b1030f193cb15528af75f1b6.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="/db/source_record/get_duxiu_dicts/duxiu_ssid/10436577.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="/db/source_record/get_duxiu_dicts/cadal_ssno/33206336.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="/blog/duxiu-exclusive.html">{{ gettext('page.datasets.duxiu.blog_post') }}</a></li>
    <li class="list-disc"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/data-imports">{{ gettext('page.datasets.common.import_scripts') }}</a></li>
    <li class="list-disc"><a href="/blog/annas-archive-containers.html">{{ gettext('page.datasets.common.aac') }}</a></li>
  </ul>

  <p class="font-bold">{{ gettext('page.datasets.duxiu.raw_notes.title') }}</p>

  <div class="whitespace-pre-wrap font-mono text-sm">
# Anonymous volunteer "bpb9v" shared the following information with us. They have been doing their own smaller scale rescue operation of Duxiu data, and compared their intel with our directory dumps.
* As far as I know, Chaoxing（超星） scans books for libraries (both public and university libraries). All books are on their server, and readers of a specific library can access to specific sets of books. So there are many small subsets of Duxiu library. As far as I know, there are seven versions of Duxiu, named from 1.0 to 7.0 (not released now). It is said that after Duxiu 5.0, Chaoxing stopped to release a whole library (I do not know particular details), so for Duxiu 6.0 and Duxiu 7.0 there is no a complete library on the Internet.
* I do not know how books from Chaoxing are leaked. Book sellers sells the entire Duxiu library, and almost every files are compressed. Chaoxing converts all .pdf file into pictures, including .png and .jpg, and then renames them into .pdg. These compressed files contains those .pdg files. We use some tools to convert them into the original .pdf files.
* Every book in Duxiu has a SS number (超星 literally means SuperStar), just like an ID. Each SS number has eight digits usually starting with 10, 11, 12, 13, 14, 15, 40, and 96.
* Book sellers will sell a cloud driver account, particularly BaiduYun, which you called "the Chinese version of Google Drive" in your blog post. By some means they can store hundreds of terabytes of files in BaiduYun, as you called "hacking".
* BaiduYun does not store every file for every user. It will store just one file, and detect if the file you are uploading can match the file they already have by computing its hash (MD5). Using this principle someone invented a way to "upload" a file. We call it "Miaochuan（秒传）".
  * A Miaochuan link is like this: 02af816bd7ae2cf07de1707ecb5fe2f4#ebda7a8dc7ca0853fb0f680ec7d45cb6#6789834#********.pdf
hash1#hash2#size#name
  * or like this: cc86a5223a685c2d7cab79ee32f4bb97#********#********.zip
hash#size#name
* After Miaochuan is invented it is much easier to upload and download books. Someone converted the entire Duxiu library to millions of Miaochuan link, and that's how we had downloaded and shared books for almost one year. Many book download sites are running with Miaochuan links, such as https://freembook.com. But a few months ago, Miaochuan was invalid.
* Now if we want to use Miaochuan we have to authorize it on BaiduYun, and it is not stable to use. I bought a BaiduYun account from a seller, and my coworker use Miaochuan. That is how we download books.
* There are too many risks if all files remain in a cloud drive, whether it is Baidu or Ali. My BaiduYun account was once blocked, and all files are inaccessible.

# Anonymous volunteer "woz9ts" responded to the above information:
* Most books are in a zip of pdg files format. One pdg file is a single image. Pdg files are not just simply png/jpg/tiff files. They are either simple png/jpg/gif/... renamed into pdg, or a proprietary format invented by SuperStar. The proprietary format internally uses djvu compression, sometimes also other image formats, encrypted with some kinds of uncommon algorithm. Obscured encryption key is embedded in the file. There is a freeware Pdg2pic can open and convert this file to pdf. Source code not available. (The first-party software ssreader has many restrictions.)
* Miaochuan links (I called it "fast-transfer" link before) are composed of four parts: file md5, file header (first 256k) md5, file size, filename. If we have the actual file, we can generate one. There is an older version of Miaochuan link, without file header md5. This kind of link is no longer valid.
* To use Miaochuan link, one must register a developer account, get a token, and call an API method with proper parameters. There are risks that, Baidu bans the token/developer account or it can ban the current API.
* My access to this collection is different from theirs. I buy a subscription from a reseller at a low price (accidentally found). The reseller add me to a BaiduYun group. (like Telegram group) The whole collection is in the group shared folder. It's my own account, not shared account, so less likely to get banned. The Miaochuan links are also useable. Therefore, I can download the files from the shared folder in batch much easier. I can't get the whole list of shared files (from the software/website), but there's a list from the reseller.
* The reseller's list does not contain filename to metadata relationship. Most files contain SS id, or ISBN in the filename, so I wrote a script to extract them. The md5, size, SS id, DX id, ISBN are extracted from my downloaded files, in upload/metadata/local_files.db
* Note. DX id is another id used in the Duxiu website. It's less useful in this collection. The primary/unique key for metadata is SS id.

# Anonymous volunteer "woz9ts" noted about the file name convention within the .zips:
二、文件更名
散页DjVu需要更名为PDG，并且符合PDG文件名规范：主文件名为6位字母、数字，控制名位pdg，均为小写。
主文件名由前缀加数字组成，前缀含义为：
cov：封面
bok：书名
leg：版权
fow：前言
!：目录
att：附录
bac：封底
ins：插页
正文页无前缀，直接用6位数字编码。
from https://www.cnblogs.com/stronghorse/p/4913267.html

# Anonymous volunteer "woz9ts" noted this:
I found several compressed files with missing pages. For example, the last page is 001366.pdg, but there are 1252 00xxxx.pdg in total, which means 114 pages are missing.
Perhaps you need to write a script to check if the number of 00000x.pdg files can match the number of pages in a zip archive?
And a pdg file of 0kb or 1kb seems to be an invalid page. This will resulting in a missing page in the final pdf file too.
You can also detect the real file type of pdg files using file magic. Real pdg files have magic 0x4848, 0x01/0x02 (version), the byte at 0x0F is the pdg encryption type.

bpb9v:
Some more information about CADAL:
1. CADAL has two building stages, the first one(one million books digitized) from 2001 to 2006 and the second one(1.5 million books digitized) from 2007 to 2012. The library whose download link were sent by "woz9ts" before is from the first stage.
2. This library was downloaded before 2016, by someone named "h". They exploited some loopholes to download. The earliest link I found about this library was posted in April 2015.
3. In this library there are more than 600,000 files, about half of them are books or magazines, the other half is papers. There doesn't seem to be a way to separate them by id.
4. I heard that "h" shared some files downloaded from the second stage in 2021, but I didn't find any other information source for this. Besides, I found a folder called &lt;REDACTED&gt; in my cloud drive, which contains many Duxiu books, but I don't know where it comes from.

woz9ts:
* 读秀512w.txt.txt is the original catalog from resellers.
* 512w_catalog.tsv is from Freembook, which seems converted from 读秀512w.txt.txt.
* 2.0-5.0全部书表.txt, 6.0书表.txt, 读秀2.0.txt, 读秀3.0.txt are file lists from resellers.
* combined_md5.tsv is the Miaochuan link list from Freembook.
* DX_2.0-5.0_有直链.db is probably from "bpb9v".
* Other files are found in the shared SFTP metadata or other websites.
woz9ts:
Duxiu: dx_20240122.db is unified metadata. dx_cover.db only contains covers, dx_toc.db only contains TOCs (ignore the other table).
CADAL: cadal_20240106.db, explained in cadal_db.md. cadal_html.db original html, if we want to fix something.
woz9ts:
* mebook: Use the embedded metadata. They are mostly correct.
* pku_press: Use filename. (id-title-[author])
* program-think: Metadata and detail see __所有电子书的清单__.html, or the filename. Directory name is category. Don't use embedded metadata in PDF. 博客 is the collector's blog. 
* shuge: Use filename. Directory name is category/collection.

skqs: Use filename, see real title in long_title. They mostly overlap with CADAL or Duxiu, please do dedup first.
四库全书珍本初集分类打包, 影印版文渊阁四库全书djvu格式分类打包, 续修四库全书PDF或djvu格式1800册104G分类打包 are three different collections.
文渊阁四库全书文本数据光盘迪志版 is a digitial edition. It has a main EXE program to read the data. NOTE: Some isos (skqs.iso, 201-208, 308) are not complete. I can't find a complete version online. Can others find them?
I have a magnet link for the 光盘迪志版: magnet:?xt=urn:btih:8b9482f29292ca52f3be52cba815ca5f87748037&dn=%E5%9B%9B%E5%BA%93%E5%85%A8%E4%B9%A6
It lacks some data, the same part (skqs.iso, 201-208, 308) as uploaded files.
No seeders for a long time.
bpb9v:
This is a software made by Dizhi(迪志) in Taiwan. It's the text version of Siku Quanshu(skqs, 四库全书, Complete Library of the Four Treasuries). They ran OCR on the entire collection and manually did some corrections.
(Dizhi is a company)
Normally Chinese ancient classics have multiple versions, differing in printing, proofreading, footnotes-almost everything during publishing. And for digitalization there are different scanners and different compress methods. All these make Chinese classics difficult to collect.

a:
how does ssno prefixed with "hj" work exactly (in CADAL)? Do you know what "hj" stands for?
bpb9v:
hj stands for "heji(合集, literally "collections").
CADAL lists some books in collections, such as https://cadal.edu.cn/cardpage/bookCardPage?ssno=01061998, other books in the same collection can be found in the list below
But these "hjxxxxxxxx" can't be opened with https://cadal.edu.cn/cardpage/bookCardPage?ssno=hjxxxxxxxx, they are not valid ID

bpb9v:
I upload a correction of about 3k lines, as /upload/DX_corrections240209.csv, mainly correcting lines with underscores.
I'll make a list for publishers in China mainland and their ISBN&CSBN codes, and collate all useful sources for books and metadata I found so far. My work with the catalog you made for all files will continue, too.

Ads in some of the files uploaded by "w":
bpb9v: "I guess removing watermarks and ad pages aren't that necessary. Ad pages should be easy to remove though, they're all 1273*1800 in size."

# Earlier notes
More passwords:
* https://github.com/Williamsunsir/dx-pdg2pdf/blob/main/passwords/passwords.txt
* https://github.com/Davy-Zhou/zip2pdf/blob/main/passwords/passwords.txt

PDG2PIC.exe related:
* https://github.com/Linkeer365/pdg2pic_autoRun
* https://github.com/TanixLu/Pdg2Pic_more_than_one/blob/main/main.py
* https://github.com/Williamsunsir/dx-pdg2pdf/tree/main
* https://github.com/Davy-Zhou/zip2pdf

The maker of PDG2PIC.exe and related tools, seems to be:
* https://www.cnblogs.com/stronghorse/

Multi-library search script:
* https://greasyfork.org/en/scripts/420751-%E5%9B%BE%E4%B9%A6%E4%BA%92%E5%8A%A9/code
  * Example url: https://u.xueshu86.com/ebook/?book_id=13000010 (sadly no direct covers access)
* Different: https://greasyfork.org/en/scripts/435569-%E6%96%87%E7%8C%AE%E4%BA%92%E5%8A%A9%E5%B0%8F%E5%B8%AE%E6%89%8B-%E8%AF%BB%E7%A7%80pdf%E4%B8%80%E9%94%AE%E4%B8%8B%E8%BD%BD-%E5%9B%BE%E4%B9%A6%E9%A6%86%E8%81%94%E7%9B%9F-%E8%AF%BB%E7%A7%80-%E8%B6%85%E6%98%9F-%E4%B8%AD%E7%BE%8E%E7%99%BE%E4%B8%87%E6%98%BE%E7%A4%BAssid%E7%AD%89%E7%B4%A2%E4%B9%A6%E5%8F%B7-%E5%90%84%E6%96%87%E7%8C%AE%E7%AB%99-%E5%9B%BE%E4%B9%A6%E7%94%B5%E5%95%86%E7%AB%99%E4%B8%8E%E8%B1%86%E7%93%A3%E7%9A%84%E4%BA%92%E8%AE%BF%E9%93%BE%E6%8E%A5-%E4%B8%80%E9%94%AE%E5%A4%8D%E5%88%B6%E5%85%83%E6%95%B0%E6%8D%AE/code
  * https://commons.wikimedia.org/wiki/Commons:Library_back_up_project

Code:
* https://github.com/Simpleyyt/eReading is excellent.
* Prefix order: "cov", "bok", "leg", "fow", "!"

Repos with BookContents.dat in their code:
* https://github.com/search?q=%22BookContents.dat%22&type=code

Incredibly detailed guide on various things PDG: https://github.com/duty-machine/duty-machine/issues/2010

Another great article: https://github.com/821/821.github.io/blob/7bbcdc8dd2ec4bb637480e054fe760821b4ad7b8/_Notes/IT/DX-CX.md

More good articles:
* https://bbs.agoil.cn/simple/?t162253.html
* https://zhuanlan.kanxue.com/article-899.htm

### Different types from the 821 article

* The standard Superstar format is pdg, but the downloaded book folder usually has two files: bookinfo.dat and bookcontent.dat. The former provides copyright information about the book, such as the title, author, publisher, year, etc. (often missing and sometimes incorrect), and the latter is a bookmark file (often with more errors and sometimes missing). We divide the downloaded pdg into several categories:
* The first category is called the clear version, with a resolution of 300dpi. For a 32k book, its image width is always above 1000. There are many encrypted formats for the clear version, but this article mainly involves four types. The first type is called 04H, which is a color picture. Sometimes black and white books also use this format, which is very wasteful of space. The second type is called 00H, which is purely black and white and has no grayscale, so it is very space-saving. The third type is a highly encrypted format, including high-density pdg and the newly released pdz. After using the appropriate tools, high-density pdg cannot be downloaded, and pdz is a new thing and rarely seen, so it is not discussed. The fourth type is pdf, which is not a standard format but a pdf with Superstar characteristics. I don't know how to download this type, and I don't want the books I download to become like this, so I won't discuss it.
* The second category is called the big picture, with a resolution of 150dpi and a width of 983. It will turn black and white books into grayscale, so the volume is often larger but looks hazy. The big picture used to have watermarks, but they were canceled in July 2014. If you don't download it correctly, it will become a medium picture or a small picture with a smaller width. Since there is no discussion value, this article will not discuss medium and small pictures.
* The third category is called the fast version, with a resolution of 150dpi and a very small width. The characters are not clear and often seem to be missing a few strokes. It generally appears in "mirror full text" and some low-level users who don't know how to operate it. It is useless and will not be discussed.
* The fourth category is called the text. The file name is n_n.pdg format, which is essentially a text pdf but encrypted. This is a book that Superstar scans and OCRs into text, with many typos and less useful than the fast version.
* The fifth category is the mysterious pre-press pdg, which means that the publisher directly gave Superstar the sample, and Superstar converted it into its format. The file naming rules for this format are the same as for text pdg, but the typesetting is correct, very space-saving, and particularly clear. However, it is rare and cannot be found easily. I have only seen it once, and it was very good, starting with the number 9.
* In summary, under the correct download, we generally download three types of unencrypted clear versions, big pictures, and text. Among them, the clear version and big picture are useful for academic research. Since the big picture is obviously inferior to the clear version, it is best to download the clear version.

woz9ts: CADAL's ssno is not Duxiu's SSid. They are completely different systems.
DX id in CADAL is the same as Duxiu's DXid.

woz9ts: The ToC API will return an empty template XML for any unknown ID or unavailable ToC.
In the database, it's the id=-1 record.
If the table doesn't have some ID, it's because I don't know this ID and haven't checked with the API.
To save space, I set the record to NULL if the content exactly matches this template XML
  </div>
{% endblock %}
