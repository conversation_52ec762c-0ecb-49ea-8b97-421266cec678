{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}
{% import 'macros/helpers.j2' as h %}

{% block title %}{{ gettext('page.codes.title') }}{% endblock %}

{% block body %}
  {% from 'macros/copy_button.html' import copy_button %}

  <h2 class="mt-4 mb-1 text-3xl font-bold">{{ gettext('page.codes.heading') }}</h2>

  {% if prefix_label == '' %}
    <div class="mt-4">{{ gettext('page.codes.intro') }}</div>

    <div class="mt-4 text-sm text-gray-500">{{ gettext('page.codes.why_cloudflare', a_donate=({"href": "/donate"} | xmlattr)) }}</div>

    <div class="mt-4 pb-2 text-sm text-gray-500">{{ gettext('page.codes.dont_scrape', a_import=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md"} | xmlattr), a_download=({"href": "/torrents#aa_derived_mirror_metadata"} | xmlattr), a_software=({"href": "https://software.annas-archive.li/"} | xmlattr), a_json_file=({"href": "/db/aarecord_elasticsearch/md5:8336332bf5877e3adbfb60ac70720cd5.json.html"} | xmlattr)) }}</div>
  {% endif %}

  <form action="/member_codes" method="get" class="mt-4">
    <div class="mb-1">
      <input name="prefix" value="{{ prefix_label }}" placeholder="{{ gettext('page.codes.prefix') }}" class="js-slash-focus grow bg-black/6.7 px-2 py-1 mr-2 rounded text-sm w-full">
    </div>
    <button class="px-4 py-1 bg-[#0195ff] text-white rounded hover:bg-blue-600 text-sm" type="submit">{{ gettext('common.form.go') }}</button>
    <a href="/member_codes" class="custom-a bg-[#777] hover:bg-[#999] text-white py-1 px-3 rounded text-sm">{{ gettext('common.form.reset') }}</a>
    {% set search_start = '/search?q="{}"'.format(prefix_label) %}
    <a class="custom-a bg-[#777] hover:bg-[#999] text-white py-1 px-3 rounded text-sm" {{ dict(href='/search?q="{}"'.format(prefix_label)) | xmlattr }}>{{ gettext('page.codes.search_archive_start') }}</a>
  </form>

  {% if bad_unicode %}
    <div class="text-sm italic mt-4">{{ gettext('page.codes.bad_unicode') }}</div>
  {% endif %}

  {% if code_item and ((code_item.info | length) > 0) %}
    <div class="mt-4">
      <div class="font-bold">{{ gettext('page.codes.known_code_prefix', key=(code_item.key)) }}</div>
      <table class="text-sm">
        <tbody>
        <tr class=""><td class="pr-8 pb-1">{{ gettext('page.codes.code_prefix') }}</td><td><a href="/member_codes?prefix={{ code_item.key }}:"><q>{{ code_item.key }}</q></a></td></tr>
        <tr class=""><td class="pr-8 pb-1">{{ gettext('page.codes.code_label') }}</td><td>{{ code_item.info.label }}</td></tr>
        {% if code_item.info.description %}
        <tr class=""><td class="pr-8 pb-1">{{ gettext('page.codes.code_description') }}</td><td class="py-2">{{ code_item.info.description }}</td></tr>
        {% endif %}
        {% if code_item.info.url %}
          {% if '%s' in code_item.info.url %}
            <tr class=""><td class="pr-8 pb-1">{{ gettext('page.codes.code_url') }}</td><td class="py-2">{{ code_item.info.url }} <div class="text-sm text-gray-500">{{ pgettext('the %s should not be changed', 'page.codes.s_substitution') }}</div></td></tr>
          {% else %}
            <tr class=""><td class="pr-8 pb-1">{{ gettext('page.codes.generic_url') }}</td><td class="py-2"><a href="{{ code_item.info.url }}" rel="noopener noreferrer nofollow">{{ code_item.info.url }}</a></td></tr>
          {% endif %}
        {% endif %}
        {% if code_item.info.website %}
        <tr class=""><td class="pr-8 py-2">{{ gettext('page.codes.code_website') }}</td><td class="py-2"><a href="{{ code_item.info.website }}" rel="noopener noreferrer nofollow">{{ code_item.info.website }}</a></td></tr>
        {% endif %}
        </tbody>
      </table>
    </div>
  {% endif %}

  {% if (aarecords | length) > 0 %}
    <div class="font-bold mt-4">
      {{ ngettext('page.codes.record_starting_with', 'page.codes.records_starting_with', (aarecords | length), prefix_label=prefix_label, count=("{}{}".format((aarecords | length), "+" if hit_max_exact_matches else ""))) }}
    </div>

    {% if code_item.info.url and ('%s' in code_item.info.url) %}
      <div class="text-sm"><a href="{{ code_item.info.url | replace('%s', code_item.value) }}">{{ gettext('page.codes.url_link', url=(code_item.info.url | replace('%s', code_item.value))) }}</a></div>
    {% endif %}

    <div class="mt-2">
      {% from 'macros/aarecord_list.html' import aarecord_list %}
      {{ aarecord_list(aarecords[0:3]) }}
      {% if (aarecords | length) > 3 %}
        <div class="js-codes-aarecord-list-more hidden">
          {{ aarecord_list(aarecords[3:]) }}
        </div>
        <a href="#" onclick="event.preventDefault(); document.querySelector('.js-codes-aarecord-list-more').classList.remove('hidden'); this.classList.add('hidden'); return false">{{ gettext('page.codes.more') }}</a>
      {% endif %}
    </div>
  {% endif %}

  {% if (prefix_rows | length) > 0 %}
    {% if prefix_label != '' and dir_path == None %}
      <div class="font-bold mt-4">{{ gettext('page.codes.codes_starting_with', prefix_label=(prefix_label)) }}</div>
    {% endif %}

    {% if dir_path != None %}
    <div class="font-bold leading-none my-4">
      <span>{{ gettext('page.codes.index_of_dir_path') }}</span><br>
      {% for path_segment in dir_path[:-1] %}<a class="!no-underline" href="{{ path_segment.link }}">{{ path_segment.label }}</a>{% endfor %}{{ dir_path[-1].label }}
    </div>
    {% endif %}

    <table>
      <tbody>
      <tr>
        <td>
          {% if dir_path != None and dir_path|length > 1 %}
            <a class="!no-underline" href="{{ dir_path[-2].link }}">../</a>
          {% endif %}
        </td>
        <td></td>
        <td class="text-sm font-bold px-4">{{ gettext('page.codes.records_prefix') }}</td>
        <td class="text-sm font-bold px-4">{{ gettext('page.codes.records_codes') }}</td>
      </tr>
      {% macro prefix_row_render(prefix_row) %}
        <tr>
          <td class="break-all"><a href="{{ prefix_row.link }}">
            {% if prefix_row.highlight %}
            {{ prefix_row.label[:prefix_row.highlight] }}<span class="opacity-50 hover:opacity-70">{{ prefix_row.label[prefix_row.highlight:] }}</span>
            {% else %}
            {{ prefix_row.label }}
            {% endif %}
          </a></td>
          <td class="text-sm text-gray-500 pl-4">
            {% if prefix_row.code_item %}{{ prefix_row.code_item.info.label }}{% endif %}
          </td>
          <td class="text-sm text-gray-500 px-4">{{ prefix_row.records | numberformat }}</td>
          <td class="text-sm text-gray-500 px-4">{{ (prefix_row.codes or 1) | numberformat }}</td>
        </tr>
      {% endmacro %}

      {% if prefix_label == '' %}
        {% set magic_number = 10000 %}

        <!-- TODO:TRANSLATE -->
        <tr><td colspan="100" class="pt-4 text-xl font-bold">Interesting codes</td></tr>

        {% for label in INTERESTING_LABELS %}
          {% for prefix_row in prefix_rows %}
            {% if prefix_row.label == label %}
              {{ prefix_row_render(prefix_row) }}
            {% endif %}
          {% endfor %}
        {% endfor %}

        <!-- TODO:TRANSLATE -->
        <tr><td colspan="100" class="pt-4 text-xl font-bold">Codes with many records</td></tr>

        {% for prefix_row in prefix_rows %}
          {% if (prefix_row.records >= magic_number) and (prefix_row.label not in BROWSABLE_PREFIXES) %}
            {{ prefix_row_render(prefix_row) }}
          {% endif %}
        {% endfor %}

        <tr><td colspan="100" class="pt-4 text-xl font-bold">{{ gettext('page.codes.fewer_than', count=(magic_number | numberformat)) }}</td></tr>
        {% for prefix_row in prefix_rows %}
          {% if (prefix_row.records < magic_number) and (prefix_row.label not in BROWSABLE_PREFIXES) %}
            {{ prefix_row_render(prefix_row) }}
          {% endif %}
        {% endfor %}
      {% else %}
        {% for prefix_row in prefix_rows %}
          {{ prefix_row_render(prefix_row) }}
        {% endfor %}
        {% if hit_max_dirs %}
          <tr><td colspan="100" class="pt-4">
            <a href="{{ dir_path[-1].link }}&raw=1">{{ gettext('page.codes.more') }}</a>
          </td></tr>
        {% endif %}
      {% endif %}
      </tbody>
    </table>
  {% endif %}
{% endblock %}
