{% macro small_file_row(small_file, uuid_prefix) -%}
<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0 pr-1 text-xs whitespace-nowrap">{% if small_file.metadata.embargo %}<span title="Torrent under embargo. Download speed extremely limited.">🔒</span> {% endif %}{% if ('/scihub/' not in small_file.file_path) and ('/libgen_li_fiction_rus/' not in small_file.file_path) %}{% if small_file.aa_currently_seeding %}<span title="Seeded by Anna’s Archive">✅</span>{% else %}<span title="Not currently seeded by Anna’s Archive">❌</span>{% endif %}{% else %}<span title="Currently not directly seeded by <PERSON>’s Archive, but we keep a backup in extracted form.">—</span>{% endif %}</td>
  <td class="p-0 break-all"><a href="/dyn/small_file/{{ small_file.file_path }}">{{ small_file.file_path_short }}</a><a class="ml-2 text-sm whitespace-nowrap" href="{{ small_file.magnet_link }}">magnet</a>{% if not small_file.is_metadata %}<a class="ml-2 text-sm whitespace-nowrap" href='/search?q="{{ small_file.torrent_code }}"'>search</a><a class="ml-2 text-sm whitespace-nowrap" href="/member_codes?prefix={{ small_file.torrent_code }}">code</a>{% endif %}</td>
  <td class="p-0 text-sm pl-2 max-sm:hidden md:whitespace-nowrap" title="Date added">{{ small_file.created }}{% if small_file.new %} <span class="inline-block bg-red-600 text-white px-1 rounded text-xs">new</span>{% endif %}</td>
  <td class="p-0 text-sm pl-2"><span class="whitespace-nowrap" title="Data size">{{ small_file.size_string }}</span><span class="whitespace-nowrap max-md:hidden" title="Number of files (there may be more files inside a .tar or .zip file)"> / {{ small_file.metadata.num_files | numberformat }}</span></td>
  <td class="p-0 text-sm pl-2 whitespace-nowrap max-md:hidden" title="Data type">{% if small_file.is_metadata %}metadata{% else %}data{% endif %}</td>
  <td class="p-0 text-sm pl-2 pr-2 lg:whitespace-nowrap">{% if small_file.scrape_metadata.scrape %}<span class="whitespace-nowrap"><span class="text-[10px] leading-none align-[2px]">{% if small_file.scrape_metadata.scrape.seeders < 4 %}<span title="<4 seeders">🔴</span>{% elif small_file.scrape_metadata.scrape.seeders < 11 %}<span title="4–10 seeders">🟡</span>{% else %}<span title=">10 seeders">🟢</span>{% endif %}</span> {{ small_file.scrape_metadata.scrape.seeders }}&nbsp;seed</span><span class="whitespace-nowrap max-md:hidden"> / {{ small_file.scrape_metadata.scrape.leechers }}&nbsp;leech </span><span class="max-md:hidden text-xs text-gray-500 whitespace-nowrap js-scrape-created-{{ uuid_prefix }}-{{ small_file.temp_uuid }}" title="{{ small_file.scrape_created | datetimeformat(format='long') }}">—</span>{% endif %}</td>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelector('.js-scrape-created-{{ uuid_prefix }}-{{ small_file.temp_uuid }}').innerText = window.timeAgo.format(new Date({{ small_file.scrape_created | tojson }}), 'mini');
    });
  </script>
</tr>{% if small_file.partially_broken %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">The above torrent file is partially broken, but still in use. It can never get to 100% seeding, so leechers are treated as seeders.</td>
</tr>{% endif %}{% if (not small_file.aa_currently_seeding) and ('/scihub/' not in small_file.file_path) and ('/libgen_li_fiction_rus/' not in small_file.file_path) %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Not currently seeded by Anna’s Archive.</td>
</tr>{% endif %}{% if 'aa_derived_mirror_metadata_20241104' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Latest dump with consistent aarecords_codes table. Help with <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/230">this ticket</a> to ensure all dumps have consistent aarecords_codes tables.</td>
</tr>{% endif %}{% if 'aa_derived_mirror_metadata_20241109' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs"><a href="https://gofile.io/d/55JpT3">Alternative download.</a></td>
</tr>{% endif %}{% if 'peoples-daily-rmrb.tar.zst' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Seems a web-based database of the “People’s Daily”, and maybe more. Someone wrote a <a href="https://github.com/liuzhiliangpc/third_corpus?search=1#111-%E4%BA%BA%E6%B0%91%E6%97%A5%E6%8A%A5%E6%96%B0%E9%97%BB%E6%95%B0%E6%8D%AE">script</a> to extract the text, but not actual good PDFs. Can someone help make good PDFs from this?</td>
</tr>{% endif %}{% if 'skqs-isos.tar.zst' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Volunteer “n” explains: “This is a software made by Dizhi/迪志 (a company) in Taiwan. It's the text version of Siku Quanshu (skqs, 四库全书, Complete Library of the Four Treasuries). They ran OCR on the entire collection and manually did some corrections. Normally Chinese ancient classics have multiple versions, differing in printing, proofreading, footnotes — almost everything during publishing. And for digitalization there are different scanners and different compress methods. All these make Chinese classics difficult to collect.” Volunteer “l” adds: “文渊阁四库全书文本数据光盘迪志版 is a digitial edition. It has a main EXE program to read the data. NOTE: Some isos (skqs.iso, 201-208, 308) are not complete. I can't find a complete version online. Can others find them? I have a <a href="magnet:?xt=urn:btih:8b9482f29292ca52f3be52cba815ca5f87748037&dn=%E5%9B%9B%E5%BA%93%E5%85%A8%E4%B9%A6">magnet link</a> for the 光盘迪志版. It lacks some data, the same part (skqs.iso, 201-208, 308) as uploaded files. No seeders for a long time.” Can someone help make good PDFs from this?</td>
</tr>{% endif %}{% if 'taiwanese-scrapes-2023-11-09.tar.zst' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Scrapes of pttweb.cc and Taiwanese news sites. Could be useful for LLM training.</td>
</tr>{% endif %}{% if 'isbn-cerlalc-2022-11-scrubbed-annas-archive.sql.zst' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Full data leak of CERLALC, scrubbed from personal information. Used to generate the <a href="/datasets/cerlalc">“cerlalc” metadata collection</a>.</td>
</tr>{% endif %}{% if 'world_lending_library_2024_11.tar.zst.torrent' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Yet another “complete library of the world”. The book files have been imported into the <a href="/datasets/upload">upload_files_wll</a>. The original library also contains videos and music, and has been preserved in its entirety in this torrent, as a historical curiosity.</td>
</tr>{% endif %}{% if 'annas-torrents-2025-07-14' in small_file.file_path %}<tr class="{% if small_file.obsolete %}line-through{% endif %}">
  <td class="p-0"></td><td colspan="5" class="p-0 text-xs">Contains all torrent files from /torrents on 2025-07-14, from <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/287">#287</a>.</td>
</tr>{% endif %}
{%- endmacro %}

{% extends "layouts/index.html" %}

{% block title %}Torrents {% if detailview %}▶ {{ ((torrents_data.small_file_dicts_grouped.values() | list)[0].keys() | list)[0] }}{% endif %}{% endblock %}

{% block body %}
  {% if gettext('common.english_only') != 'Text below continues in English.' %}
    <p class="mb-4 font-bold">{{ gettext('common.english_only') }}</p>
  {% endif %}

  <div lang="en">
    {% if detailview %}
      <p class="mb-4">
        <a href="/torrents">Torrents</a> ▶ {{ ((torrents_data.small_file_dicts_grouped.values() | list)[0].keys() | list)[0] }}
      </p>

      <p class="mb-4">
        See the main <a href="/torrents">Torrents page</a> for more details.
      </p>
    {% else %}
      <h2 class="mt-4 mb-1 text-3xl font-bold">Torrents</h2>

      <p class="mb-4">
        This torrent list is the “ultimate unified list” of releases by Anna’s Archive, Library Genesis, Sci-Hub, and others. By seeding these torrents, you help preserve humanity’s knowledge and culture. These torrents represent the vast majority of human knowledge that can be mirrored in bulk.
      </p>

      <p class="mb-4">
        These torrents are not meant for downloading individual books. They are meant for long-term preservation. With these torrents you can set up a full mirror of Anna’s Archive, using our <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive">source code</a> and metadata (which can be <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md">generated</a> or <a href="/torrents#aa_derived_mirror_metadata">downloaded</a> as ElasticSearch and MariaDB databases). We also have full lists of torrents, as <a href="/dyn/torrents.json">JSON</a>.
      </p>

      <p class="mb-4">
        Currently <strong>{{ (((torrents_data.seeder_sizes[1]+torrents_data.seeder_sizes[2])/(1+torrents_data.seeder_sizes[0]+torrents_data.seeder_sizes[1]+torrents_data.seeder_sizes[2]))*100) | round | int }}%</strong> of the total <strong>{{ torrents_data.seeder_size_total_string }}</strong> is copied in at least 4 locations, and only {{ (((torrents_data.seeder_sizes[2])/(1+torrents_data.seeder_sizes[0]+torrents_data.seeder_sizes[1]+torrents_data.seeder_sizes[2]))*100) | round | int }}% in more than 10 locations. We need your help to get to 100%!
      </p>

      <div class="mb-4 bg-yellow-100 p-4 rounded">
        <em>“The lost cannot be recovered; but let us save what remains: not by vaults and locks which fence them from the public eye and use, in consigning them to the waste of time, but by such a multiplication of copies, as shall place them beyond the reach of accident.”</em><div class="text-sm">— Thomas Jefferson, 1791</div>
      </div>

      <div class="mt-8 group"><span class="text-xl font-bold" id="stats">Stats</span> <a href="#stats" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

      <p class="mb-1">
        You can help out enormously by seeding torrents that are low on seeders. If everyone who reads this chips in, we can preserve these collections forever. This is the current breakdown, excluding embargoed torrents, but including external torrents:
      </p>

      <table class="mb-2">
        <tr><td>🔴 {{ torrents_data.seeder_size_strings[0] }}</td><td class="text-sm text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_less', count=4) }}</td></tr>
        <tr><td>🟡 {{ torrents_data.seeder_size_strings[1] }}</td><td class="text-sm text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_range', count_min=4, count_max=10) }}</td></tr>
        <tr><td>🟢 {{ torrents_data.seeder_size_strings[2] }}</td><td class="text-sm text-gray-500 pl-4">{{ gettext('page.home.torrents.legend_greater', count=10) }}</td></tr>
      </table>

      <div class="js-torrents-chart h-[300px]"></div>
      <div class="mb-1 text-xs text-gray-500">Scraped from <a href="https://opentrackr.org">opentrackr.org</a>.</div>

      <script>
        new Promise((resolve, reject) => document.addEventListener("DOMContentLoaded", () => { resolve () })).then(() => {
          const seedingHistogram = {{ histogram | tojson }};

          const colorsBySeederGroup = ['rgb(240,85,79)', 'rgb(255,218,1)', 'rgb(1,180,1)'];

          Plotly.newPlot(document.querySelector(".js-torrents-chart"), [2,1,0].map((seederGroup) => {
            const seederGroupData = seedingHistogram.filter((item) => item.seeder_group === seederGroup);
            return {
              type: "scatter",
              x: seederGroupData.map((item) => item.day),
              y: seederGroupData.map((item) => item.total_tb),
              marker: {color: colorsBySeederGroup[seederGroup]},
              stackgroup: 'one',
            };
          }), {
            margin: { l: 50, r: 16, b: 50, t: 0, pad: 4 },
            showlegend: false,
                yaxis: { ticksuffix: "TB" },
          }, {staticPlot: true});
        });
      </script>

      <div class="mt-8 group"><span class="text-xl font-bold" id="generate_torrent_list"><span class="underline">HELP SEED</span> — Torrent List Generator</span> <a href="#generate_torrent_list" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

      <p class="mb-4">
        Enter how many TBs you can help seed, and we’ll give you a list of torrents that need the most seeding! The list is somewhat random every time, so two people generating at the same time will still cover different parts of the collection.
      </p>

      <form action="/dyn/generate_torrents" class="flex items-center mb-4">
        <label class="mr-2 flex items-center">Max TB: <input type="number" step="any" name="max_tb" class="ml-1 bg-black/6.7 px-2 py-1 rounded" placeholder="(empty for no limit)" /></label>
        <label class="mr-2 flex items-center">Type: <select name="format" class="ml-1 bg-black/6.7 px-2 py-1 rounded"><option value="magnet">Magnet links</option><option value="json">JSON</option><option value="url">URLs</option></select></label>
        <button type="submit" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white">Generate</button>
      </form>

      <p class="mb-4">
        The list is sorted by <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/157">(seeders + 0.1*leechers)*fraction-of-torrent-size-compared-to-average-size + random-number-between-0.0-and-2.0</a>, ascending. Specify a maximum TB to store (we simply keep adding torrents until max TB is reached).
      </p>

      <p class="mb-4">
        We only show non-obsolete, non-embargoed files with at least one seeder here. For a complete list see the full <a href="/dyn/torrents.json">torrents JSON</a>. For an unofficial tool that actually downloads the torrent files, see <a href="https://github.com/shaped1/annas-torrents">this repo</a>.
      </p>

      <div class="mt-8 group"><span class="text-xl font-bold" id="guide">Guide</span> <a href="#guide" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

      <p class="mb-4">
        The list of torrents is split in three parts:<br>
        1. The first part is managed and released by Anna’s Archive. These include books, papers, and magazines from websites such as Z-Library and IA. It also includes metadata records from websites such as WorldCat and ISBNdb.<br>
        2. The second part is managed and released by others, such as Library Genesis and Sci-Hub. We include these torrents in order to present a unified list of everything you need to mirror Anna’s Archive.<br>
        3. Miscellaneous other torrents; not critical to seed and not included in stats or the torrent list generator.<br>
      </p>

      <p class="mb-4">
        Torrents seeded by Anna’s Archive are indicated with a checkmark (✅). Some torrents get temporarily embargoed (🔒) upon release, for various reasons (e.g. protecting our scraping methods). An embargo means very slow initial seeding speeds. They get lifted within a year.
      </p>

      <p class="mb-4">
        For more information about the different collections, see the <a href="/datasets">Datasets</a> page. Also see the <a href="/faq#torrents">Torrents FAQ</a>.
        <a href="https://github.com/RArtutos/Data-science-starter-kit-Enhance/" rel="noopener noreferrer nofollow" target="_blank">This repo</a> is excellent for getting started with data analysis.
      </p>

      <p class="mb-4">
        <strong>IMPORTANT:</strong> If you seed large amounts of our collection (50TB or more), please <a href="/contact">contact us</a> so we can let you know when we deprecate any large torrents.
      </p>

<!--       <div class="mt-8 group"><span class="text-xl font-bold" id="long_term_seeders">Long Term Seeders</span> <a href="#long_term_seeders" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

      <p class="mb-4">
        List of heroes who are committed to long term seeding of all or large parts of this torrent list. These people help preserve humanity’s knowledge and culture, and we are deeply grateful for that. <a href="/contact">Contact us</a> if you wish to be added. We’ll give you Amazing Archivist-level membership if you seed 100TB+. IP addresses are required to supply so we can verify if you’re still seeding.
      </p> -->

      <!-- <table>
        <tr><th class="text-left pr-4">Username</th><th class="text-left pr-4">Contact</th><th class="text-left pr-4">IPs</th><th class="text-left pr-4">Notes</th></tr>
        <tr><td class="pr-4"><a href="/profile/Anna000">AnnaArchivist #Anna000</a></td><td class="pr-4"><a href="/contact">Contact</a></td><td class="pr-4">---</td><td class="pr-4">Anna’s Archive is committed to seeding all the torrents in this list for as long as possible.</td></tr>
      </table> -->


      <div class="mt-8 group"><span class="text-xl font-bold" id="similar_lists">Similar Lists</span> <a href="#similar_lists" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

      <p class="">
        Similar lists, independently maintained. Note that at the time of this writing, all these lists are included in our list, under <a href="#external">External Collections</a>, similarly to how Anna’s Archive itself is a meta-collection of many external collections.
      </p>

      <ul class="list-inside mb-4 ml-1">
        <li class="list-disc"><a href="https://annas-archive.listmirror.org">Torrent List Mirror for Anna's Archive (exact mirror of this page)</a> / <a href="https://software.annas-archive.li/ptfall/torrent_list_mirror">code</a></li>
        <li class="list-disc"><a href="https://aa.i4.mom/">aa.i4.mom (exact mirror of this page)</a> / <a href="https://github.com/teamcoltra/AnnasTorrentMirror">code</a></li>
        <li class="list-disc"><a href="https://torrents.bobs-archive.org/">Bob’s Archive torrents (exact mirror of this page)</a> / <a href="http://c5tbehd6apsmqyf5p4cfgky2njxd3tz37nrpt7qur6p7rczsuakqxkqd.onion/">Tor .onion</a> / same code as aa.i4.mom</li>
        <li class="list-disc"><a href="https://mirror.annas-archive-torrents.com/">Yet Another Anna's Archive Torrents Mirror (exact mirror of this page)</a> / same code as aa.i4.mom</li>
        <li class="list-disc"><a href="https://ipdl.cat/">ipdl.cat (partial mirror of this page)</a></li>
        <li class="list-disc"><a href="https://phillm.net/libgen-seeds-needed.php">PhillM's LibGen torrent index (only libgen)</a></li>
      </ul>
    {% endif %}

    {% for toplevel, groups in torrents_data.small_file_dicts_grouped.items() %}
      {% if not detailview %}
        {% if toplevel == 'managed_by_aa' %}
          <div class="mt-8 group"><span class="text-2xl font-bold" id="managed_by_aa">Managed by Anna’s Archive</span> <a href="#managed_by_aa" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

          <p class="mb-4">
            These torrents are managed and released by Anna’s Archive.
          </p>

          <p class="mb-0">
            Torrents with “aac” in the filename use the <a href="/blog/annas-archive-containers.html">Anna’s Archive Containers format</a>. Torrents that are crossed out have been superseded by newer torrents, for example because newer metadata has become available — we normally only do this with small metadata torrents.
            <!-- Some torrents that have messages in their filename are “adopted torrents”, which is a perk of our top tier <a href="/donate">“Amazing Archivist” membership</a>. -->
          </p>
        {% elif toplevel == 'external' %}
          <div class="mt-8 group"><span class="text-2xl font-bold" id="external">External Collections</span> <a href="#external" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

          <p class="mb-0">
            These torrents are managed and released by others. We include these torrents in order to present a unified list of everything you need to mirror Anna’s Archive.
          </p>
        {% else %}
          <div class="mt-8 group"><span class="text-2xl font-bold" id="other_aa">Other Torrents by Anna’s Archive</span> <a href="#other_aa" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a></div>

          <p class="mb-0">
            These are miscellaneous torrents which are not critical to seed, but contain useful data for certain use cases. These torrents are not included in the seeding stats or torrent list generator.
          </p>
        {% endif %}
      {% endif %}

      <div class="overflow-hidden max-w-full">
        <table>
          {% for group, small_files in groups.items() %}
            <tr><td colspan="100" class="pt-4"><span class="text-xl font-bold" id="{{ group | replace('/', '__') }}">{{ group }}</span> <span class="text-xs text-gray-500">{{ torrents_data.group_size_strings[group] }} / {% if group not in ['ia', 'scihub', 'zlib', 'libgen_li_fiction_rus'] %}{{ torrents_data.group_num_files[group] | numberformat }} files / {% if torrents_data.group_avg_size_strings[group] %}avg {{ torrents_data.group_avg_size_strings[group] }} per file / {% endif %}{% endif %}{{ small_files | length | numberformat }} {{ 'torrent' if (small_files | length == 1) else 'torrents' }} / 🔴 {{ (torrents_data.group_seeder_size_strings[group] | list())[0] }} / 🟡 {{ (torrents_data.group_seeder_size_strings[group] | list())[1] }} / 🟢 {{ (torrents_data.group_seeder_size_strings[group] | list())[2] }}{% if small_files | length > 0 %} / updated: {{ small_files[-1].created }}{% if small_files[-1].new %} <span class="inline-block bg-red-600 text-white px-1 rounded text-xs">new</span>{% endif %}{% endif %}</span> {% if not detailview %}<a href="#{{ group | replace('/', '__') }}" class="custom-a invisible [td:hover>&]:visible text-gray-400 hover:text-gray-500 text-sm align-[2px]">§</a>{% endif %}

              {% if group == 'zlib' %}
                <div class="mb-1 text-sm">Z-Library books. The different types of torrents in this list are cumulative — you need them all to get the full collection. *file count is hidden because of big .tar files. <a href="/torrents/zlib">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/zlib">dataset</a></div>
              {% elif group == 'other_metadata' %}
                <div class="mb-1 text-sm">Other metadata. <a href="/torrents/other_metadata">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/other_metadata">dataset</a></div>
              {% elif group == 'aa_misc_data' %}
                <div class="mb-1 text-sm">Miscellaneous files which are not critical to seed, but which may help with long-term preservation. <a href="/torrents/aa_misc_data">full list</a></div>
              {% elif group == 'ia' %}
                <div class="mb-1 text-sm">IA Controlled Digital Lending books and magazines. The different types of torrents in this list are cumulative — you need them all to get the full collection. *file count is hidden because of big .tar files. <a href="/torrents/ia">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/ia">dataset</a></div>
              {% elif group == 'worldcat' %}
                <div class="mb-1 text-sm">Metadata from OCLC/Worldcat. <a href="/torrents/worldcat">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/oclc">dataset</a><span class="text-xs text-gray-500"> / </span><a href="/blog/worldcat-scrape.html">blog</a></div>
              {% elif group == 'libgen_rs_non_fic' %}
                <div class="mb-1 text-sm">Non-fiction book collection from Libgen.rs. <a href="/torrents/libgen_rs_non_fic">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgrs">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.is/repository_torrent/">original</a><span class="text-xs text-gray-500"> / </span><a href="https://forum.mhut.org/viewtopic.php?f=17&t=6395&p=217286">new additions</a> (blocks IP ranges, VPN might be required)</div>
              {% elif group == 'libgen_rs_fic' %}
                <div class="mb-1 text-sm">Fiction book collection from Libgen.rs. <a href="/torrents/libgen_rs_fic">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgrs">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.is/fiction/repository_torrent/">original</a><span class="text-xs text-gray-500"> / </span><a href="https://forum.mhut.org/viewtopic.php?f=17&t=6395&p=217286">new additions</a> (blocks IP ranges, VPN might be required)</div>
              {% elif group == 'libgen_li_fic' %}
                <div class="mb-1 text-sm">Fiction book collection from Libgen.li, from the point of divergence from Libgen.rs. <a href="/torrents/libgen_li_fic">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgli">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.li/torrents/fiction/">original</a></div>
              {% elif group == 'libgen_li_comics' %}
                <div class="mb-1 text-sm">Comics collection from Libgen.li. Note that some ranges are omitted since they only contain deleted or repacked files. <a href="/torrents/libgen_li_comics">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgli">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.li/torrents/comics/">original</a></div>
              {% elif group == 'libgen_li_magazines' %}
                <div class="mb-1 text-sm">Magazines collection from Libgen.li. <a href="/torrents/libgen_li_magazines">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgli">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.li/torrents/magazines/">original</a></div>
              {% elif group == 'libgen_li_standarts' %}
                <div class="mb-1 text-sm">Collection of standard documents from Libgen.li. <a href="/torrents/libgen_li_standarts">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgli">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.li/torrents/standarts/">original</a></div>
              {% elif group == 'libgen_li_fiction_rus' %}
                <div class="mb-1 text-sm">Russian fiction torrents, supplied by Libgen.li, but originated on Russian torrent sites such as <a href="https://booktracker.org/index.php?c=18">booktracker.org</a> We don’t seed these torrents, nor have we yet established which torrents correspond to which files, but we do hold a backup (decompressed) of most of these files. *file count is hidden because of big .zip files. <a href="/torrents/libgen_li_fiction_rus">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/lgli">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.li/torrents/">original</a></div>
              {% elif group == 'scihub' %}
                <div class="mb-1 text-sm">Sci-Hub / Libgen.rs “scimag” collection of academic papers. Currently not directly seeded by Anna’s Archive, but we keep a backup in extracted form. Note that the “smarch” torrents are <a href="https://www.reddit.com/r/libgen/comments/15qa5i0/what_are_smarch_files/">deprecated</a> and therefore not included in our list. *file count is hidden because of big .zip files. <a href="/torrents/scihub">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/scihub">dataset</a><span class="text-xs text-gray-500"> / </span><a href="https://libgen.is/scimag/repository_torrent/">original</a></div>
              {% elif group == 'duxiu' %}
                <div class="mb-1 text-sm">DuXiu and related. <a href="/torrents/duxiu">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/duxiu">dataset</a><span class="text-xs text-gray-500"> / </span><a href="/blog/duxiu-exclusive.html">blog</a></div>
              {% elif group == 'upload' %}
                <div class="mb-1 text-sm">Sets of files that were uploaded to Anna’s Archive by volunteers, which are too small to warrant their own datasets page, but together make for a formidable collection. <a href="/torrents/upload">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/upload">dataset</a></div>
              {% elif group == 'aa_derived_mirror_metadata' %}
                <div class="mb-1 text-sm">Our raw metadata database (ElasticSearch and MariaDB), published occasionally to make it easier to set up mirrors. All this data can be generated from scratch using our <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md">open source code</a>, but this can take a while. At this time you do still need to run the AAC-related scripts. These files have been created using the data-imports/scripts/dump_*.sh scripts in our codebase. <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md#importing-from-aa_derived_mirror_metadata">This section</a> describes how to load them. Documentation for the ElasticSearch records can be found inline in our <a href="https://annas-archive.li/db/aarecord_elasticsearch/md5:8336332bf5877e3adbfb60ac70720cd5.json.html">example JSON</a>. <a href="https://github.com/RArtutos/Data-science-starter-kit-Enhance/" rel="noopener noreferrer nofollow" target="_blank">This repo</a> is excellent for getting started with data analysis. <a href="/torrents/aa_derived_mirror_metadata">full list</a></div>
              {% elif group == 'magzdb' %}
                <div class="mb-1 text-sm">MagzDB metadata (content files are in the <a href="/torrents#upload">upload</a> collection). <a href="/torrents/magzdb">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/magzdb">dataset</a></div>
              {% elif group == 'nexusstc' %}
                <div class="mb-1 text-sm">Nexus/STC metadata. <a href="/torrents/nexusstc">full list</a><span class="text-xs text-gray-500"> / </span><a href="/datasets/nexusstc">dataset</a></div>
              {% elif group == 'hathitrust' %}
                <div class="mb-1 text-sm">Raw files from the <a href="https://www.hathitrust.org/member-libraries/resources-for-librarians/data-resources/research-datasets/#available-research-datasets" rel="noopener noreferrer nofollow" target="_blank">“ht_text_pd” public domain dataset from HathiTrust</a>, and ~7% of the <a href="https://github.com/hathitrust/datasets?tab=readme-ov-file#superset-ht_text" rel="noopener noreferrer nofollow" target="_blank">“ht_text” private dataset</a>. We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/276">$30k bounty</a> if you can get the full collection. <a href="/torrents/hathitrust">full list</a> / </span><a href="/datasets/hathi">dataset</a></div>
              {% elif group == 'gbooks' %}
                <div class="mb-1 text-sm">Metadata scraped from Google Books. Metadata is good to have, but the real goal is to get their actual scans. We will award a <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/234">$200k bounty</a> if you can get the full collection. <a href="/torrents/gbooks">full list</a> / </span><a href="/datasets/gbooks">dataset</a></div>
              {% endif %}
            </td></tr>

            {% if detailview %}
              {% for small_file in small_files %}
                {{ small_file_row(small_file, 'regular') }}
              {% endfor %}
            {% else %}
              {% for small_file in (small_files | reverse | list)[0:4] %}
                {{ small_file_row(small_file, 'regular') }}
              {% endfor %}
              <td colspan="100" class=""><a class="text-sm" href="/torrents/{{ group }}">full list for “{{ group }}” ({{ small_files | length }} {{ 'torrent' if (small_files | length == 1) else 'torrents' }})</a>
            {% endif %}
          {% endfor %}
        </table>
      </div>
    {% endfor %}
  </div>
{% endblock %}
