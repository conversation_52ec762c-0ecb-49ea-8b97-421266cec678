{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }} ▶ {{ gettext('page.datasets.upload.title') }} [upload]{% endblock %}

{% block body %}
  <div class="mb-4"><a href="/datasets">{{ gettext('page.datasets.title') }}</a> ▶ {{ gettext('page.datasets.upload.title') }} [upload]</div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    <div class="text-xs mb-2" t-msgid="page.datasets.upload.overview">Overview from <a t-key="a1" href="/datasets">datasets page</a>.</div>
    <table class="w-full mx-[-8px]">
      <tr class="even:bg-[#f2f2f2]">
        <th class="p-2 align-bottom text-left" width="20%">{{ gettext("page.datasets.sources.source.header") }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext("page.datasets.sources.metadata.header") }}</th>
        <th class="p-2 align-bottom text-left" width="40%">{{ gettext("page.datasets.sources.files.header") }}</th>
      </tr>

      <tr class="even:bg-[#f2f2f2]">
        <td class="p-2 align-top">
          <a class="custom-a underline hover:opacity-60" href="/datasets/uploads">
            {{ gettext('common.record_sources_mapping.uploads') }} [upload]
          </a>
        </td>
        <td class="p-2 align-top" colspan="2">
          <div class="my-2 first:mt-0 last:mb-0">
            {{ gettext('page.datasets.sources.uploads.metadata_and_files', icon='') }}
          </div>
        </td>
      </tr>
    </table>
  </div>

  <p class="mb-4" t-msgid="page.datasets.upload.description">
    Various smaller or one-off sources. We encourage people to upload to other shadow libraries first, but sometimes people have collections that are too big for others to sort through, though not big enough to warrant their own category.
  </p>

  <p class="mb-4" t-msgid="page.datasets.upload.subcollections">
    The <q>upload</q> collection is split up in smaller subcollections, which are indicated in the AACIDs and torrent names. All subcollections were first deduplicated against the main collection, though the metadata <q>upload_records</q> JSON files still contain a lot of references to the original files. Non-book files were also removed from most subcollections, and are typically <em>not</em> noted in the <q>upload_records</q> JSON.
  </p>

  <p class="mb-4" t-msgid="page.datasets.upload.subsubcollections">
    Many subcollections themselves are comprised of sub-sub-collections (e.g. from different original sources), which are represented as directories in the <q>filepath</q> fields.
  </p>

  <p class="mb-4" t-msgid="page.datasets.upload.subs.heading">
    The subcollections are:
  </p>

  <div class="relative overflow-x-auto border sm:rounded-lg mb-4">
    <table class="w-full text-sm text-left">
      <thead class="text-xs text-gray-700 uppercase bg-black/5">
        <tr>
          <th scope="col" class="px-6 py-3" colspan="3" t-msgid="page.datasets.upload.subs.subcollection">Subcollection</th>
          <th scope="col" class="px-6 py-3" t-msgid="page.datasets.upload.subs.notes">Notes</th>
        </tr>
      </thead>

      <tbody>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">aaaaarg</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/aaaaarg/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/aaaaarg">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.aaaaarg">
            From <a t-key="a_href" href="http://aaaaarg.fail" rel="noopener noreferrer nofollow" target="_blank">aaaaarg.fail</a>. Appears to be fairly complete. From our volunteer <q>cgiym</q>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">acm</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/acm/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/acm">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.acm">
            From an <a t-key="a_href" href="https://1337x.to/torrent/4536161/ACM-Digital-Library-2020/" rel="noopener noreferrer nofollow" target="_blank"><q>ACM Digital Library 2020</q></a> torrent. Has fairly high overlap with existing papers collections, but very few MD5 matches, so we decided to keep it completely.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">airitibooks</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/airitibooks/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/airitibooks">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.airitibooks">
            Scrape of <q>iRead eBooks</q> (= phonetically <q>ai rit i-books</q>; airitibooks.com), by volunteer <q>j</q>. Corresponds to <q>airitibooks</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">alexandrina</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/alexandrina/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/alexandrina">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.alexandrina">
            From a collection <a t-key="a1" href="https://www.reddit.com/r/DataHoarder/comments/zuniqw/bibliotheca_alexandrina_a_600_gb_hoard_of_history/" rel="noopener noreferrer nofollow" target="_blank"><q>Bibliotheca Alexandrina</q></a>. Partly from the original source, partly from the-eye.eu, partly from other mirrors.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">bibliotik</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/bibliotik/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/bibliotik">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.bibliotik">
            From a private books torrent website, <a t-key="a_href" href="https://bibliotik.me/" rel="noopener noreferrer nofollow" target="_blank">Bibliotik</a> (often referred to as <q>Bib</q>), of which books were bundled into torrents by name (A.torrent, B.torrent) and distributed through the-eye.eu.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">bpb9v_cadal</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/bpb9v_cadal/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/bpb9v_cadal">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.bpb9v_cadal">
            From our volunteer <q>bpb9v</q>. From more information about <a t-key="a_href" href="https://cadal.edu.cn/" rel="noopener noreferrer nofollow" target="_blank">CADAL</a>, see the notes in our <a t-key="a_duxiu" href="/datasets/duxiu">DuXiu dataset page</a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">bpb9v_direct</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/bpb9v_direct/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/bpb9v_direct">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.bpb9v_direct">
            More from our volunteer <q>bpb9v</q>, mostly DuXiu files, as well as a folder <q>WenQu</q> and <q>SuperStar_Journals</q> (SuperStar is the company behind DuXiu).
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">cgiym_chinese</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/cgiym_chinese/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/cgiym_chinese">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.cgiym_chinese">
            From our volunteer <q>cgiym</q>, Chinese texts from various sources (represented as subdirectories), including from <a t-key="a_href" href="http://cmpedu.com/" rel="noopener noreferrer nofollow" target="_blank">China Machine Press</a> (a major Chinese publisher).
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">cgiym_more</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/cgiym_more/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/cgiym_more">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.cgiym_more">
            Non-Chinese collections (represented as subdirectories) from our volunteer <q>cgiym</q>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">chinese_architecture</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/chinese_architecture/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/chinese_architecture">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.chinese_architecture">
            Scrape of books about Chinese architecture, by volunteer <q>cm</q>: <q>I got it by exploiting a network vulnerability at the publishing house, but that loophole has since been closed</q>. Corresponds to <q>chinese_architecture</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">degruyter</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/degruyter/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/degruyter">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.degruyter">
            Books from academic publishing house <a t-key="a_href" href="https://www.degruyter.com/" rel="noopener noreferrer nofollow" target="_blank">De Gruyter</a>, collected from a few large torrents.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">docer</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/docer/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/docer">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.docer">
            Scrape of <a t-key="a_href" href="https://docer.pl/" rel="noopener noreferrer nofollow" target="_blank">docer.pl</a>, a polish file sharing website focused on books and other written works. Scraped in late 2023 by volunteer <q>p</q>. We don't have good metadata from the original website (not even file extensions), but we filtered for book-like files and were often able to extract metadata from the files themselves.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">duxiu_epub</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/duxiu_epub/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/duxiu_epub">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.duxiu_epub">
            DuXiu epubs, directly from DuXiu, collected by volunteer <q>w</q>. Only recent DuXiu books are available directly through ebooks, so most of these must be recent.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">duxiu_main</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/duxiu_main/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/duxiu_main">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.duxiu_main">
            Remaining DuXiu files from volunteer <q>m</q>, which weren’t in the DuXiu proprietary PDG format (the main <a t-key="a_href" href="/datasets/duxiu" rel="noopener noreferrer nofollow" target="_blank">DuXiu dataset</a>). Collected from many original sources, unfortunately without preserving those sources in the filepath.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">elsevier</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/elsevier/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/elsevier">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.elsevier">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">emo37c</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/emo37c/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/emo37c">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.emo37c">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">french</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/french/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/french">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.french">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">hentai</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/hentai/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/hentai">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.hentai">
            Scrape of erotic books, by volunteer <q>do no harm</q>. Corresponds to <q>hentai</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">ia_multipart</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/ia_multipart/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/ia_multipart">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.ia_multipart">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">imslp</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/imslp/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/imslp">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.imslp">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">japanese_manga</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/japanese_manga/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/japanese_manga">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.japanese_manga">
            Collection scraped from a Japanese Manga publisher by volunteer <q>t</q>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">longquan_archives</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/longquan_archives/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/longquan_archives">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.longquan_archives">
            <a t-key="a_href" href="http://www.xinhuanet.com/english/2019-11/15/c_138557853.htm" rel="noopener noreferrer nofollow" target="_blank">Selected judicial archives of Longquan</a>, provided by volunteer <q>c</q>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">magzdb</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/magzdb/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/magzdb">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.magzdb">
            Scrape of <a t-key="a_href" href="https://magzdb.org/" rel="noopener noreferrer nofollow" target="_blank">magzdb.org</a>, an ally of Library Genesis (it’s linked on the libgen.rs homepage) but who didn’t want to provide their files directly. Obtained by volunteer <q>p</q> in late 2023.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">mangaz_com</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/mangaz_com/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/mangaz_com">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.mangaz_com">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">misc</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/misc/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/misc">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.misc">
            Various small uploads, too small as their own subcollection, but represented as directories. The <q>oo42hcksBxZYAOjqwGWu</q> directory corresponds to the <q>czech_oo42hcks</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">newsarch_ebooks</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/newsarch_ebooks/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/newsarch_ebooks">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.newsarch_ebooks">
            Ebooks from AvaxHome, a Russian file sharing website.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">newsarch_magz</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/newsarch_magz/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/newsarch_magz">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.newsarch_magz">
            Archive of newspapers and magazines. Corresponds to <q>newsarch_magz</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">pdcnet_org</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/pdcnet_org/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/pdcnet_org">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.pdcnet_org">
            Scrape of the <a t-key="a1" href="https://www.pdcnet.org/" rel="noopener noreferrer nofollow" target="_blank">Philosophy Documentation Center</a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">polish</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/polish/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/polish">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.polish">
            Collection of volunteer <q>o</q> who collected Polish books directly from original release (<q>scene</q>) websites.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">shuge</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/shuge/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/shuge">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.shuge">
            Combined collections of <a t-key="a_href" href="https://www.shuge.org/" rel="noopener noreferrer nofollow" target="_blank">shuge.org</a> by volunteers <q>cgiym</q> and <q>woz9ts</q>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">shukui_net_cdl</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/shukui_net_cdl/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/shukui_net_cdl">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.shukui_net_cdl">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">trantor</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/trantor/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/trantor">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.trantor">
            <a t-key="a_href" href="https://github.com/trantor-library/trantor" rel="noopener noreferrer nofollow" target="_blank"><q>Imperial Library of Trantor</q></a> (named after the fictional library), scraped in 2022 by volunteer <q>t</q>. Corresponds to <q>trantor</q> metadata in <a t-key="a1" href="/datasets/other_metadata"><q>Other metadata scrapes</q></a>.
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">turkish_pdfs</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/turkish_pdfs/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/turkish_pdfs">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.turkish_pdfs">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">twlibrary</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/twlibrary/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/twlibrary">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.twlibrary">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">wll</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/wll/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/wll">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.wll">
            <span></span>
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">woz9ts_direct</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/woz9ts_direct/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/woz9ts_direct">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.woz9ts_direct">
            Sub-sub-collections (represented as directories) from volunteer <q>woz9ts</q>: <a t-key="a_program_think" href="https://github.com/programthink/books" rel="noopener noreferrer nofollow" target="_blank">program-think</a>, <a t-key="a_haodoo" href="https://haodoo.net" rel="noopener noreferrer nofollow" target="_blank">haodoo</a>, <a t-key="a_skqs" href="https://en.wikipedia.org/wiki/Siku_Quanshu" rel="noopener noreferrer nofollow" target="_blank">skqs</a> (by <a t-key="a_sikuquanshu" href="http://www.sikuquanshu.com/" rel="noopener noreferrer nofollow" target="_blank">Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: <q>This site mainly focused on sharing high quality ebook files, some of which are typeset by the owner himself. The owner was <a t-key="a_arrested" href="https://www.thepaper.cn/newsDetail_forward_7943463" rel="noopener noreferrer nofollow" target="_blank">arrested in 2019</a>, and someone made a collection of files he shared.</q>).
          </td>
        </tr>
        <tr class="odd:bg-white even:bg-black/5">
          <th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">woz9ts_duxiu</th>
          <td class="px-6 py-4"><a href="/member_codes?prefix=filepath:upload/woz9ts_duxiu/">{{ gettext('page.datasets.upload.action.browse') }}</a></td>
          <td class="px-6 py-4"><a href="/search?termtype_1=original_filename&termval_1=upload/woz9ts_duxiu">{{ gettext('page.datasets.upload.action.search') }}</a></td>
          <td class="px-6 py-4" t-msgid="page.datasets.upload.source.woz9ts_duxiu">
            Remaining DuXiu files from volunteer <q>woz9ts</q>, which weren’t in the DuXiu proprietary PDG format (still to be converted to PDF).
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="font-bold">{{ gettext('page.datasets.common.resources') }}</p>
  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc">{{ gettext('page.datasets.common.total_files', count=(stats_data.stats_by_group.upload.count | numberformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.total_filesize', size=(stats_data.stats_by_group.upload.filesize | filesizeformat)) }}</li>
    <li class="list-disc">{{ gettext('page.datasets.common.mirrored_file_count', count=(stats_data.stats_by_group.upload.aa_count | numberformat), percent=((stats_data.stats_by_group.upload.aa_count/(stats_data.stats_by_group.upload.count+1)*100.0) | decimalformat)) }}</li>
    <li class="list-disc"><a href="/torrents#upload" t-msgid="page.datasets.upload.aa_torrents">Torrents by Anna’s Archive</a></li>
    <li class="list-disc"><a href="/db/source_record/get_aac_upload_book_dicts/md5/b6b884b30179add94c388e72d077cdb0.json.html">{{ gettext('page.datasets.common.aa_example_record') }}</a></li>
    <li class="list-disc"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/data-imports">{{ gettext('page.datasets.common.import_scripts') }}</a></li>
    <li class="list-disc"><a href="/blog/annas-archive-containers.html">{{ gettext('page.datasets.common.aac') }}</a></li>
  </ul>
{% endblock %}
