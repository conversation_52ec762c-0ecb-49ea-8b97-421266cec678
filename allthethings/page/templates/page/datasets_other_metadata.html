{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.datasets.title') }} ▶ Other metadata scrapes{% endblock %}

{% block body %}
  <div class="mb-4"><a href="/datasets">{{ gettext('page.datasets.title') }}</a> ▶ Other metadata scrapes</div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    {{ gettext('page.datasets.common.intro', a_archival=(a.faqs_what | xmlattr), a_llm=(a.llm | xmlattr)) }}
  </div>

  <div class="mb-4 p-2 overflow-hidden bg-black/5 break-words">
    <div class="text-xs mb-2">Overview from <a href="/datasets">datasets page</a>.</div>
    <table class="w-full mx-[-8px]">
      <tr class="even:bg-[#f2f2f2]">
        <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.source.header') }}</th>
        <th class="p-2 align-bottom text-left w-[65%]">{{ gettext('page.datasets.sources.metadata.header') }}</th>
        <th class="p-2 align-bottom text-left">{{ gettext('page.datasets.sources.last_updated.header') }}</th>
      </tr>

      <tr class="even:bg-[#f2f2f2]">
        <td class="p-2 align-top">
          <a class="custom-a underline hover:opacity-60" href="/datasets/other_metadata">
            Other metadata scrapes
          </a>
        </td>
        <td class="p-2 align-top">
          <div class="my-2 first:mt-0 last:mb-0">
            👩‍💻 Anna’s Archive manages scrapes of metadata from other sources.
          </div>
        </td>
        <td class="p-2 align-top">Varies</td>
      </tr>
    </table>
  </div>

  <p class="mb-4">
    Various smaller or one-off metadata scrapes.
  </p>

  <div class="relative overflow-x-auto border sm:rounded-lg mb-4">
    <table class="w-full text-sm text-left">
      <thead class="text-xs text-gray-700 uppercase bg-black/5">
        <tr>
          <th scope="col" class="px-6 py-3" colspan="4">Collection</th>
          <th scope="col" class="px-6 py-3">Notes</th>
        </tr>
      </thead>

      <tbody>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">airitibooks</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/airitibooks_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Scrape of “iRead eBooks” (= phonetically “ai rit i-books”; airitibooks.com), by volunteer “j”. Corresponds to “airitibooks” subcollection in the <a href="/datasets/upload">“upload” dataset</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">bloomsbury</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/bloomsbury_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Metadata directly from the <a {{ (dict(href="https://www.bloomsburycollections.com/for-librarians", **a.external_link) | xmlattr) }}>Bloomsbury Collections website</a> transformed into AAC by volunteer “n”, who explains: “It gives a full set of ISBNs for each book. Many of these ISBNs are not easy to find via other sources.”</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">cerlalc</th><td class="px-6 py-4"><a href="/cerlalc/cerlalc_bolivia__titulos__1">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_cerlalc_book_dicts/cerlalc_id/cerlalc_bolivia__titulos__1.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/cerlalc_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Data leak from <a href="http://cerlalc.org/" rel="noopener noreferrer nofollow" target="_blank">CERLALC</a>, a consortium of Latin American publishers, which included lots of book metadata. The original data (scrubbed from personal info) can be found in <a href="/torrents#aa_misc_data">isbn-cerlalc-2022-11-scrubbed-annas-archive.sql.zst.torrent</a>. Special thanks to the anonymous group that worked hard on this.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">chinese_architecture</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/chinese_architecture_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Scrape of books about Chinese architecture, by volunteer “cm”: “I got it by exploiting a network vulnerability at the publishing house, but that loophole has since been closed”. Corresponds to “chinese_architecture” subcollection in the <a href="/datasets/upload">“upload” dataset</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">czech_oo42hcks</th><td class="px-6 py-4"><a href="/czech_oo42hcks/cccc_csv_1">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_czech_oo42hcks_book_dicts/czech_oo42hcks_id/cccc_csv_1.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/czech_oo42hcks_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Metadata extracted from CSV and Excel files, corresponding to “upload/misc/oo42hcksBxZYAOjqwGWu” in the <a href="/datasets/upload">“upload” dataset</a>. Original files can be found through the <a href="/member_codes?prefix_b64=ZmlsZXBhdGg6dXBsb2FkL21pc2Mvb280Mmhja3NCeFpZQU9qcXdHV3UvQ0NDQy9DQ0NDLmNzdg==">Codes Explorer</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">edsebk</th><td class="px-6 py-4"><a href="/edsebk/1509715">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_edsebk_book_dicts/edsebk_id/1509715.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/ebscohost-scrape">Scraper code</a></td><td class="px-6 py-4"><p class="mb-4">Scrape of EBSCOhost’s eBook Index (edsebk; "eds" = "EBSCOhost Discovery Service", "ebk" = "eBook"). Code made by our volunteer “tc” <a href="https://software.annas-archive.li/AnnaArchivist/ebscohost-scrape">here</a>. This is a fairly small ebook metadata index, but still contains some unique files. If you have access to the other EBSCOhost databases, please let us know, since we’d like to index more of them.</p><p>The filename of the latest release (annas_archive_meta__aacid__ebscohost_records__20240823T161729Z--Wk44RExtNXgJ3346eBgRk9.jsonl) is incorrect (the timestamp should be a range, and there should not be a uid). We’ll correct this in the next release.</p></td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">goodreads</th><td class="px-6 py-4"><a href="/goodreads/1115623">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_goodreads_book_dicts/goodreads_id/1115623.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/goodreads_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Goodreads scrape by volunteer “tc”.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">hentai</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/hentai_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Scrape of erotic books, by volunteer “do no harm”. Corresponds to “hentai” subcollection in the <a href="/datasets/upload">“upload” dataset</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">isbndb</th><td class="px-6 py-4"><a href="/isbndb/9780060512804">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_isbndb_dicts/isbn13/9780060512804.json.html">AAC example</a></td><td class="px-6 py-4"></td><td class="px-6 py-4"><p class="mb-4">ISBNdb is a company that scrapes various online bookstores to find ISBN metadata. We made an initial scrape in 2022, with more information in our blog post <a href="/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html">“ISBNdb dump, or How Many Books Are Preserved Forever?”</a>. Future releases will be made in the AAC format.</p><p><strong>{{ gettext('page.datasets.isbndb.release1.title') }}</strong></p><p class="mb-4">{{ gettext('page.datasets.isbndb.release1.text1') }}</p><p class="mb-4">{{ gettext('page.datasets.isbndb.release1.text2') }}</p><p class="">{{ gettext('page.datasets.isbndb.release1.text3') }}</p></td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">isbngrp</th><td class="px-6 py-4"><a href="/isbngrp/613c6db6bfe2375c452b2fe7ae380658">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_isbngrp_book_dicts/isbngrp_id/613c6db6bfe2375c452b2fe7ae380658.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/isbngrp_make_aac.py">AAC generation code</a></td><td class="px-6 py-4"><a href="https://grp.isbn-international.org/" rel="noopener noreferrer nofollow" target="_blank">ISBN Global Register of Publishers</a> scrape. Thanks to volunteer “g” for doing this: “using the URL <code class="text-xs">https://grp.isbn-international.org/piid_rest_api/piid_search?q="{}"&wt=json&rows=150</code> and recursively filling in the q parameter with all possible digits until the result is less than 150 rows.” It’s also possible to extract this information from <a href="/md5/d3c0202d609c6aa81780750425229366">certain books</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">kulturpass</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/kulturpass_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Metadata scrape of <a {{ (dict(href="https://kulturpass.de", **a.external_link) | xmlattr) }}>Kulturpass</a>, by volunteer “a”, who explains: “It seems that we have scraped the whole VLB! <a {{ (dict(href="https://buchhandel.de/", **a.external_link) | xmlattr) }}>The VLB contains</a> the metadata of every book you can order today in Germany from every shop. So that is the official source behind the Kulturpass app.”</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">libby</th><td class="px-6 py-4"><a href="/libby/10371786">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_libby_book_dicts/libby_id/10371786.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/libby_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Libby (OverDrive) scrape by volunteer “tc”.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">newsarch_magz</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/newsarch_magz_records_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Archive of newspapers and magazines. Corresponds to “newsarch_magz” subcollection in the <a href="/datasets/upload">“upload” dataset</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">rgb</th><td class="px-6 py-4"><a href="/rgb/000000012">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_rgb_book_dicts/rgb_id/000000012.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/rgb_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Scrape of the <a href="https://ru.wikipedia.org/wiki/%D0%A0%D0%BE%D1%81%D1%81%D0%B8%D0%B9%D1%81%D0%BA%D0%B0%D1%8F_%D0%B3%D0%BE%D1%81%D1%83%D0%B4%D0%B0%D1%80%D1%81%D1%82%D0%B2%D0%B5%D0%BD%D0%BD%D0%B0%D1%8F_%D0%B1%D0%B8%D0%B1%D0%BB%D0%B8%D0%BE%D1%82%D0%B5%D0%BA%D0%B0" rel="noopener noreferrer nofollow" target="_blank">Russian State Library</a> (Российская государственная библиотека; RGB) catalog, the third largest (regular) library in the world. Thanks to volunteer “w”.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">torrents_byteoffsets</th><td class="px-6 py-4"></td><td class="px-6 py-4"></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/torrents_byteoffsets_rclone_offsets_ia.py">Generation code 1</a><br><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/torrents_byteoffsets_qbitorrent_offsets.py">Generation code 2</a><br><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/torrents_byteoffsets_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">There are packed torrents where files are in archives, such as the Sci-Hub torrents (.zip) and early Zlib/IA torrents (.tar). Luckily, none of these use compression, so we can use byte indexes to find the files within them.<br>&nbsp;<br>Most files have only the fields "md5", "torrent_filename", and "byte_start". Some files turned out to be compressed after all, and have "compressed":true,"compress_size":1234. Some files were corrupted, so we couldn't compute their MD5, and instead have: "md5":"CORRUPT:10.1145/2413076.2413091.pdf". Done by volunteer “a” for <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/279#note_3175">this bounty</a>.</td></tr>
        <tr class="odd:bg-white even:bg-black/5"><th scope="row" class="px-6 py-4 font-medium whitespace-nowrap">trantor</th><td class="px-6 py-4"><a href="/trantor/mw1J0sHU4nPYlVkS">Page example</a></td><td class="px-6 py-4"><a href="/db/source_record/get_aac_trantor_book_dicts/trantor_id/mw1J0sHU4nPYlVkS.json.html">AAC example</a></td><td class="px-6 py-4"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/scrapes/trantor_make_aac.py">AAC generation code</a></td><td class="px-6 py-4">Metadata dump from the <a href="https://github.com/trantor-library/trantor" rel="noopener noreferrer nofollow" target="_blank">“Imperial Library of Trantor”</a> (named after the fictional library), corresponding to the “trantor” subcollection in the <a href="/datasets/upload">“upload” dataset</a>. Converted from MongoDB dump.</td></tr>
      </tbody>
    </table>
  </div>

  <p class="font-bold">{{ gettext('page.datasets.common.resources') }}</p>
  <ul class="list-inside mb-4 ml-1">
    <li class="list-disc"><a href="/torrents#other_metadata">Metadata torrents by Anna’s Archive</a></li>
    <li class="list-disc"><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/data-imports">{{ gettext('page.datasets.common.import_scripts') }}</a></li>
    <li class="list-disc"><a href="/blog/annas-archive-containers.html">{{ gettext('page.datasets.common.aac') }}</a></li>
  </ul>
{% endblock %}
