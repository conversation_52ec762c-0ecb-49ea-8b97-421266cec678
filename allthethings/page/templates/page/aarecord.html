{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{aarecord.additional.top_box.meta_information[0]}}{% endblock %}

{% block meta_tags %}
  <meta name="description" content="{{aarecord.additional.top_box.meta_information[1:4] | join('\n\n')}}" />
{% endblock %}

{% block body %}
  {% from 'macros/copy_button.html' import copy_button %}

  {% if aarecord_id_split[0] == 'doi' %}
    <div class="text-xl mb-1 font-bold mb-4">
      {{ gettext('page.md5.header.scihub', id=aarecord_id_split[1]) }}
    </div>
  {% elif aarecord_id_split[0] == 'ia' %}
    <div class="text-xl mb-1 font-bold">
      {{ gettext('page.md5.header.ia', id=aarecord_id_split[1]) }}
    </div>
    <p class="mb-4">
      {{ gettext('page.md5.header.ia_desc', a_request=(' href="/faq#request" ' | safe)) }}
      {{ gettext('page.md5.header.consider_upload', a_request=(' href="/faq#upload" ' | safe)) }}
    </p>
  {% elif aarecord_id_split[0] in ['isbn', 'ol', 'oclc', 'duxiu_ssid', 'cadal_ssno', 'magzdb', 'nexusstc', 'edsebk'] %}
    <div class="text-xl mb-1 font-bold">
      {% if aarecord_id_split[0] == 'isbn' %}
        {{ gettext('page.md5.header.meta_isbn', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'ol' %}
        {{ gettext('page.md5.header.meta_openlib', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'oclc' %}
        {{ gettext('page.md5.header.meta_oclc', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'duxiu_ssid' %}
        {{ gettext('page.md5.header.meta_duxiu_ssid', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'cadal_ssno' %}
        {{ gettext('page.md5.header.meta_cadal_ssno', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'magzdb' %}
        {{ gettext('page.md5.header.meta_magzdb_id', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'nexusstc' %}
        {{ gettext('page.md5.header.meta_nexus_stc_id', id=aarecord_id_split[1]) }}
      {% elif aarecord_id_split[0] == 'edsebk' %}
        EBSCOhost eBook Index (edsebk) {{ aarecord_id_split[1] }} metadata record
      {% endif %}
    </div>
    <p class="mb-4">
      {{ gettext('page.md5.header.meta_desc', a_request=(' href="/faq#request" ' | safe)) }}
      {{ gettext('page.md5.header.consider_upload', a_request=(' href="/faq#upload" ' | safe)) }}
    </p>
  {% endif %}

  <div class="mb-4 p-6 overflow-hidden bg-black/5 break-words rounded">
    <div class="float-right w-[25%] ml-4 pb-4 aspect-[0.64] relative">
      <div id="cover_aarecord_id__{{ aarecord.id }}" class="relative overflow-hidden w-full h-full flex flex-col justify-center">
        <img class="w-full max-h-full absolute" src="{{aarecord.additional.top_box.cover_url}}" alt="" referrerpolicy="no-referrer" onerror="this.parentNode.removeChild(this)" onload="cbg = document.querySelector('.js-cover-background'); cbg.style.aspectRatio = this.clientWidth / this.clientHeight; cbg.style.marginTop = 0" loading="lazy" decoding="async"/>
        <div class="w-full aspect-[0.85] mt-[7%] js-cover-background" style="background-color: hsl({{ aarecord.additional.top_box.cover_missing_hue_deg }}deg 43% 73%)"></div>
      </div>
      {% if (aarecord.additional.ol_primary_linked_source_records) | length > 0 %}
        <div class="mt-2 text-xs text-right">
          <div>✅ {{ gettext('page.md5.text.linked_metadata') }}</div>
          <a href="https://openlibrary.org/books/{{ aarecord.additional.ol_primary_linked_source_records[0].ol_edition }}" class="block">{{ gettext('page.md5.text.linked_metadata_openlib') }}</a>
          {% if aarecord.additional.ol_primary_linked_source_records | length > 1 %}
            <div>
              {{ gettext('page.md5.warning.multiple_links') }}
              {% for ol_linked in aarecord.additional.ol_primary_linked_source_records %}
                <a href="https://openlibrary.org/books/{{ ol_linked.ol_edition }}">[{{ loop.index }}]</a>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      {% else %}
        <!-- <a href="/metadata" class="block mt-2 text-xs text-right">{{ gettext('page.md5.header.improve_metadata') }}</a> -->
        <!-- TODO: don't show this report button on metadata-only records -->
        <div class="block mt-2 text-xs text-right"><a href="#md5-tab-discussion" onclick="document.querySelector('#md5-tab-discussion').click();"> {{ gettext('page.md5.text.report_quality') }}</a></div>
      {% endif %}
    </div>
    <script>
      (function() {
        const signedIn = {{ signed_in | tojson }};
        const addDownloadInfo = downloadEvent => {
          const coverContainer = document.getElementById("cover_aarecord_id__md5:" + downloadEvent["md5"]);
          const timestampHtml = `
          <div class="absolute bottom-0 p-1 text-sm bg-[rgba(200,200,200,0.9)] leading-none w-full flex flex-col gap-1">
            ${
              signedIn ? `<a href="/account/downloaded">Downloaded on</a>`
                    : `<span href="/account/downloaded">Downloaded on</span>`
            }
            <span title="{{ gettext('page.search.results.download_time') }}">
              ${downloadEvent["timestamp"]}
            </span>
          </div>
          `;
          coverContainer.insertAdjacentHTML("beforeend", timestampHtml);
        };

        const hash = "{{ aarecord.id }}".split("md5:")[1];
        if (!hash) {
          return;
        }

        const entry = localStorage.getItem("md5_download_counted_new_" + hash);
        try {
            const downloadEvent = JSON.parse(entry);
            if (downloadEvent && typeof downloadEvent === "object" && "md5" in downloadEvent && "timestamp" in downloadEvent) {
              addDownloadInfo(downloadEvent);
              return;
            }
        } catch { }

        const params = new URLSearchParams({
          hashes: [hash]
        }).toString();
        fetch(`/dyn/downloads/check_downloaded/?${params}`, { method: "POST" })
          .then(res => res.json())
          .then(json => {
            json.forEach(addDownloadInfo);
          })
          .catch(e => {});
      })();
    </script>

    <div class="text-sm text-gray-500">{{aarecord.additional.top_box.top_row}}</div>
    <div class="text-3xl font-bold">{{aarecord.additional.top_box.title}}{% if aarecord.additional.top_box.title %}<span class="select-none"> <a class="custom-a text-xs align-[2px] opacity-80 hover:opacity-100" href="/search?q={{ aarecord.additional.top_box.title | urlencode }}">🔍</a></span>{% endif %}</div>
    <div class="text-md">{{aarecord.additional.top_box.publisher_and_edition}}</div>
    <div class="italic">{{aarecord.additional.top_box.author}}{% if aarecord.additional.top_box.author %}<span class="select-none"> <a class="custom-a text-xs align-[2px] opacity-80 hover:opacity-100" href="/search?q={{ aarecord.additional.top_box.author | urlencode }}">🔍</a></span>{% endif %}</div>
    <div class="mt-4 line-clamp-[10] js-md5-top-box-description">{% for field in aarecord.additional.top_box.freeform_fields %}<div class="text-xs text-gray-500 uppercase">{{ field[0] }}</div><div class="mb-1">{{ field[1] | escape | replace('\n', '<br>' | safe)}}</div>{% endfor %}</div>

    <div class="js-md5-codes-container hidden">
      <div class="mt-4 text-xs flex flex-wrap js-md5-codes-tabs" role="tablist" aria-label="code tabs" aria-multiselectable="true">
        {% for code_from_additional in aarecord.additional.codes %}
          {% set code_item = make_code_for_display(code_from_additional) %}
          {% if (not code_item.highlight) and (loop.index0 > 0) and (aarecord.additional.codes[loop.index0 - 1].highlight)%}<div style="width: 100%"></div>{% endif %}
          <a class="rounded-sm flex mb-1 mr-1 pr-1 border border-[#aaa] opacity-60 hover:opacity-80 aria-selected:opacity-100 custom-a js-md5-codes-tabs-tab max-w-[calc(50%-8px)]" href="#" aria-selected="false" id="md5-codes-tab-{{ loop.index }}" aria-controls="md5-codes-panel-{{ loop.index }}" tabindex="0"><span class="py-0.5 bg-[#aaa] mr-1 px-1 truncate {% if code_item.info.shortenvalue %}max-w-[50px]{% else %}max-w-[60px] sm:max-w-[120px]{% endif %} flex-shrink-0">{{ code_item.info.label or code_item.key }}</span><span class="py-0.5 truncate {% if code_item.info.shortenvalue %}max-w-[35px]{% else %}max-w-[100px] sm:max-w-[300px]{% endif %}">{{ code_item.masked_isbn or code_item.value }}</span></a>
        {% endfor %}
      </div>
      <div>
        {% for code_from_additional in aarecord.additional.codes %}
          {% set code_item = make_code_for_display(code_from_additional) %}
          <div id="md5-codes-panel-{{ loop.index }}" role="tabpanel" aria-labelledby="md5-codes-tab-{{ loop.index }}" hidden class="text-sm mt-2">
            <div><strong>{{ code_item.info.label or code_item.key }}:</strong>
            {% if code_item.masked_isbn %}{{ code_item.masked_isbn }} {{ copy_button(code_item.masked_isbn) }} / {% endif %}{{ code_item.value }} {{ copy_button(code_item.value) }}</div>
            {% if code_item.info.description %}<div class="">{{ code_item.info.description }}</div>{% endif %}
            {% if code_item.info.url %}<div class="">{{ gettext('page.md5.codes.url') }} <a href="{{ code_item.info.url | replace('%s', code_item.value) }}" rel="noopener noreferrer nofollow">{{ code_item.info.url | replace('%s', code_item.value) }}</a></div>{% endif %}
            {% if code_item.info.website %}<div class="">{{ gettext('page.md5.codes.website') }} <a href="{{ code_item.info.website }}" rel="noopener noreferrer nofollow">{{ code_item.info.website }}</a></div>{% endif %}
            <div>{{ gettext('page.md5.codes.aa_abbr') }} <a href='/search?q="{{ code_item.key | urlencode }}:{{ code_item.value | urlencode }}"'>{{ gettext('page.md5.codes.aa_search', name=(code_item.key + ':' + code_item.value)) }}</a></div>
            <div>{{ gettext('page.md5.codes.code_explorer') }} <a href="/member_codes?prefix={{(code_item.key + ':' + code_item.value)}}">{{ gettext('page.md5.codes.code_search', name=(code_item.key + ':' + code_item.value)) }}</a></div>
          </div>
        {% endfor %}
      </div>
    </div>
    <script>
      (function() {
        const tabEls = document.querySelectorAll('.js-md5-codes-tabs-tab');
        for (const el of tabEls) {
          el.addEventListener('tabOpen', () => {
            for (otherEl of tabEls) {
              if (otherEl != el && otherEl.getAttribute('aria-selected') === "true") {
                document.querySelector('.js-md5-codes-tabs').ariaTablist.close(otherEl);
              }
            }
          })
        }
      })();
    </script>
    <a href="#" class="mt-4 js-md5-top-box-description-link text-sm" onclick="document.querySelector('.js-md5-top-box-description').classList.remove('line-clamp-[10]'); document.querySelector('.js-md5-codes-container').classList.remove('hidden'); this.parentNode.removeChild(this); event.preventDefault(); return false;">{{ gettext('page.md5.box.descr_read_more') }}</a>
  </div>

  {% if (range(0, 100) | random) == 0 %}
    <!-- <a href="" class="custom-a block mb-4 text-sm bg-[#ffd] hover:bg-[#ffa] p-2">
      <span class="font-bold">Ad:</span> Some ad here?
    </a> -->
  {% endif %}

  {% if g.domain_lang_code == 'zh' %}
    <!-- <div class="mb-4 text-sm bg-[#ffd] p-2">
      [广告] 想要下载速度更快？嘎嘎快加速器，加速全球，又快又稳。<a target="_blank" href="https://sososofast.com/">立即试用。</a>使用优惠码“annas-archive”在结账时立享9折优惠！
    </div> -->
  {% endif %}

  <div class="flex flex-wrap mb-1 text-black/64" role="tablist" aria-label="file tabs">
    <button class="mr-4 mb-1 border-b-[3px] border-transparent aria-selected:border-[#0095ff] aria-selected:text-black aria-selected:font-bold js-md5-tab-downloads" aria-selected="true" id="md5-tab-downloads" aria-controls="md5-panel-downloads" tabindex="0">{% if aarecord_id_split[0] in ['md5','doi','nexusstc_download'] %}{{ gettext('page.md5.tabs.downloads', count=((aarecord.additional.fast_partner_urls | length) + (aarecord.additional.slow_partner_urls | length) + (aarecord.additional.download_urls | length))) }}{% elif aarecord_id_split[0] == 'ia' %}{{ gettext('page.md5.tabs.borrow', count=((aarecord.additional.fast_partner_urls | length) + (aarecord.additional.slow_partner_urls | length) + (aarecord.additional.download_urls | length))) }}{% elif aarecord_id_split[0] in ['isbn', 'ol', 'oclc', 'duxiu_ssid', 'cadal_ssno', 'magzdb', 'nexusstc', 'edsebk'] %}{{ gettext('page.md5.tabs.explore_metadata', count=((aarecord.additional.fast_partner_urls | length) + (aarecord.additional.slow_partner_urls | length) + (aarecord.additional.download_urls | length))) }}{% endif %}</button>
    {% if aarecord_id_split[0] == 'md5' %}
      <button class="mr-4 mb-1 border-b-[3px] border-transparent aria-selected:border-[#0095ff] aria-selected:text-black aria-selected:font-bold" aria-selected="false" id="md5-tab-discussion" aria-controls="md5-panel-discussion" tabindex="0">{{ gettext('page.md5.tabs.comments', count=('<span class="js-md5-tab-discussion">–</span>' | safe)) }}</button>
      <button class="mr-4 mb-1 border-b-[3px] border-transparent aria-selected:border-[#0095ff] aria-selected:text-black aria-selected:font-bold" aria-selected="false" id="md5-tab-lists" aria-controls="md5-panel-lists" tabindex="0">{{ gettext('page.md5.tabs.lists', count=('<span class="js-md5-tab-lists">–</span>' | safe)) }}</button>
      <button class="mr-4 mb-1 border-b-[3px] border-transparent aria-selected:border-[#0095ff] aria-selected:text-black aria-selected:font-bold" aria-selected="false" id="md5-tab-stats" aria-controls="md5-panel-stats" tabindex="0">{{ gettext('page.md5.tabs.stats', count=('<span class="js-md5-tab-stats">–</span>' | safe)) }}</button>
    {% endif %}
    <button class="mr-4 mb-1 border-b-[3px] border-transparent aria-selected:border-[#0095ff] aria-selected:text-black aria-selected:font-bold" aria-selected="false" id="md5-tab-details" aria-controls="md5-panel-details" tabindex="0">{{ gettext('common.tech_details') }}</button>
  </div>

  {% if aarecord_id_split[0] == 'md5' %}
    <script>
      (function() {
        const md5 = {{ aarecord_id_split[1] | tojson }};

        function fetchComments() {
          fetch("/dyn/md5_reports/" + md5).then((response) => response.ok ? response.text() : 'Error 827151').then((text) => {
            const reloadNode = document.querySelector(".js-md5-issues-reports");
            reloadNode.innerHTML = text;
            window.executeScriptElements(reloadNode);
          });
        }

        window.md5ReloadSummary = function() {
          fetch("/dyn/md5/summary/" + md5).then((response) => response.json()).then((json) => {
            document.querySelector(".js-md5-tab-discussion").innerText = (json.comments_count + json.reports_count + json.great_quality_count);
            document.querySelector(".js-md5-tab-lists").innerText = json.lists_count;
            document.querySelector(".js-md5-tab-stats").innerText = json.downloads_total;
            document.querySelector(".js-md5-button-new-issue-label .js-count").innerText = json.reports_count;
            document.querySelector(".js-md5-button-great-quality-label .js-count").innerText = json.great_quality_count;
            document.querySelector(".js-md5-button-new-comment .js-count").innerText = json.comments_count;
            window.md5UserReaction = json.user_reaction;
            document.querySelector(".js-md5-button-great-quality").classList.toggle("selected", window.md5UserReaction === 2);
            document.querySelector(".js-md5-button-new-comment").classList.toggle("disabled", window.md5UserReaction !== 2);
            if (window.md5UserReaction !== 2) {
              document.querySelector('.js-new-comment').classList.toggle('hidden', true);
            }

            if (json.comments_count > 0 || json.reports_count > 0) {
              document.getElementById('md5-panel-discussion').addEventListener("panelOpen", fetchComments);
              // fetchComments();
            } else {
              document.querySelector(".js-md5-issues-reports").classList.add("hidden");
            }

            if (json.is_member) {
              window.showExternalDownloads();
              for (const el of document.querySelectorAll('.js-fast-download-no-member-header')) {
                el.classList.add('hidden');
              }
              if (json.download_still_active) {
                for (const el of document.querySelectorAll('.js-fast-download-member-header-valid-for')) {
                  el.classList.remove('hidden');
                }
              } else {
                if (json.downloads_left) {
                  const elRemaining = document.querySelector('.js-fast-download-member-header-remaining');
                  if (elRemaining) {
                    elRemaining.classList.remove('hidden');
                    elRemaining.innerHTML = elRemaining.innerHTML.replace('XXXXXX', json.downloads_left);
                    for (const el of document.querySelectorAll('.js-download-link')) {
                      el.addEventListener("click", function() {
                        elRemaining.classList.add('hidden');
                        for (const el of document.querySelectorAll('.js-fast-download-member-header-valid-for')) {
                          el.classList.remove('hidden');
                        }
                      });
                    }
                  }
                } else {
                  for (const el of document.querySelectorAll('.js-fast-download-member-header-no-remaining')) {
                    el.classList.remove('hidden');
                  }
                }
              }
            }
          });
        };

        document.addEventListener("DOMContentLoaded", window.md5ReloadSummary);
      })();
    </script>
  {% endif %}

  <div id="md5-panel-downloads" role="tabpanel" tabindex="0" aria-labelledby="md5-tab-downloads">
    {% if aarecord.file_unified_data.has_meaningful_problems %}
      <p class="">{{ gettext('page.md5.box.issues.text1') }}</p>
      <ul class="list-inside ml-1">
        {% for problem in aarecord.file_unified_data.problems %}
          <li class="list-disc">{{ md5_problem_type_mapping[problem.type] }}{% if problem.descr %} ("{{problem.descr}}"){% endif %}</li>
          {% if problem.better_aarecord_id %}
            <li class="list-disc">{{ gettext('page.md5.box.download.better_file', link=(('<a href="/' + (problem.better_aarecord_id | replace(':', '/') | urlencode) + '">' + problem.better_aarecord_id + '</a>') | safe)) }}</li>
          {% endif %}
        {% endfor %}
      </ul>

      <div class="mb-4">{{ gettext('page.md5.box.issues.text2') }}</div>
    {% endif %}

    {% if (aarecord.additional.fast_partner_urls | length) > 0 %}
      <div class="mb-4">
        <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.md5.box.download.header_fast_only') }}</h3>
        <div class="mb-1 js-fast-download-no-member-header">
          {{ gettext('page.md5.box.download.header_fast_no_member', a_membership=(' href="/donate"' | safe)) }}
          {% if g.is_membership_double %}<div class="inline-block px-1 bg-[#ff005b]">{{ gettext('layout.index.header.banner.fundraiser.this_month') }}</div>{% endif %}
        </div>
        <p class="mb-1 hidden js-fast-download-member-header-remaining">{{ gettext('page.md5.box.download.header_fast_member', remaining='XXXXXX') }}</p>
        <p class="mb-1 hidden js-fast-download-member-header-no-remaining">{{ gettext('page.md5.box.download.header_fast_member_no_remaining_new') }}</p>
        <p class="mb-1 hidden js-fast-download-member-header-valid-for">{{ gettext('page.md5.box.download.header_fast_member_valid_for') }}</p>

        <ul class="list-inside mb-4 ml-1">
          {% for label, url, extra in aarecord.additional.fast_partner_urls %}
            {% if label %}
              <!-- <li class="list-disc">{{ gettext('page.md5.box.download.option', num=loop.index, link=(("<a href='" + url + "'" + 'rel="noopener noreferrer nofollow" class="js-download-link">' + label + '</a>') | safe), extra=((((('<a class="text-xs" href="' | safe) + url + ('?no_redirect=1">' | safe) + gettext('page.md5.box.download.no_redirect') + ('</a> ') | safe) | safe) + (extra | safe)) | safe )) }}</li> -->
              <!-- TODO:TRANSLATE "(short filename)" -->
              <li class="list-disc">{{ gettext('page.md5.box.download.option', num=loop.index, link=(("<a href='" + url + "'" + 'rel="noopener noreferrer nofollow" class="js-download-link">' + label + '</a>') | safe), extra=(((
                ((('<a class="text-xs" href="' | safe) + url + ('?viewer=1">' | safe) + gettext('page.md5.box.download.open_in_viewer') + ('</a> ') | safe) if (aarecord.file_unified_data.extension_best | lower) in viewer_supported_extensions.values() | sum(start=[]) else '') + ('<a class="text-xs" href="' | safe) + url + ('?no_redirect=1">' | safe) + gettext('page.md5.box.download.no_redirect') + ('</a> ' | safe) + ('<a class="text-xs" href="' | safe) + url + ('?short=1">' | safe) + '(short filename)' + ('</a> ') | safe)) + (extra | safe)) | safe ) }}</li>
              {% else %}
              <li class="list-disc">{{ extra | safe }}</li>
            {% endif %}
          {% endfor %}
          <!-- <li class="list-disc">{{ gettext('layout.index.header.banner.refer', percentage=50) }} <a href="/refer">{{ gettext('layout.index.header.learn_more') }}</a></li> -->
        </ul>
      </div>
    {% endif %}

    {% if (aarecord.additional.slow_partner_urls | length) > 0 %}
      <div class="mb-4">
        <h3 class="mt-4 mb-1 text-xl font-bold">{{ gettext('page.md5.box.download.header_slow_only') }}</h3>
        <p class="mb-1">{{ gettext('page.md5.box.download.trusted_partners') }} {{ gettext('page.md5.box.download.slow_faq', a_slow=(' href="/faq#slow"' | safe)) }} {{ gettext("common.md5.servers.browser_verification_unlimited", a_browser=' href="/browser_verification" ' | safe) }}</p>
        <ul class="list-inside mb-4 ml-1">
          {% for label, url, extra in aarecord.additional.slow_partner_urls %}
            {% if label %}
              <li class="list-disc">{{ gettext('page.md5.box.download.option', num=loop.index, link=(("<a href='" + url + "'" + 'rel="noopener noreferrer nofollow" ' + (' target="_blank" ' if (not url.startswith("/")) else '') + ' class="js-download-link">' + label + '</a>') | safe), extra=(extra | safe)) }}</li>
            {% else %}
              <li class="list-disc">{{ extra | safe }}</li>
            {% endif %}
          {% endfor %}
          {% if (aarecord.file_unified_data.extension_best | lower) in viewer_supported_extensions.values() | sum(start=[]) %}
            <li class="list-disc">{{ gettext('page.md5.box.download.after_downloading') }} <a href="/view" target="_blank">{{ gettext('page.md5.box.download.open_in_our_viewer') }}</a></li>
          {% endif %}
        </ul>
      </div>
    {% endif %}

    {% if aarecord_id_split[0] in ['md5','doi','nexusstc_download'] %}
      {% if (aarecord.additional.fast_partner_urls | length) > 0 %}
        <div class="mb-4"><a href="#" class="text-sm js-show-external-button" onClick="event.preventDefault(); window.showExternalDownloads()">{{ gettext('page.md5.box.external_downloads') }}</a></div>
        <h3 class="mt-4 mb-1 text-xl font-bold js-show-external hidden">{{ gettext('page.md5.box.download.header_external') }}</h3>
      {% else %}
        {# no heading needed, because this list is now right under the "Downloads" tab #}
      {% endif %}

      <script>
        window.showExternalDownloads = function() {
          for (const el of document.querySelectorAll('.js-show-external')) {
            el.classList.remove('hidden');
          };
          for (const el of document.querySelectorAll('.js-show-external-button')) {
            el.classList.add('hidden');
          };
        }
      </script>
    {% endif %}

    <ul class="list-inside mb-4 ml-1 {% if (aarecord.additional.fast_partner_urls | length) > 0 %}js-show-external hidden{% endif %}">
      {% for label, url, extra in aarecord.additional.download_urls %}
        {% if label %}
          <li class="list-disc">{{ gettext('page.md5.box.download.option', num=loop.index, link=(("<a href='" + url + "'" + 'rel="noopener noreferrer nofollow" ' + (' target="_blank" ' if (not url.startswith("/")) else '') + ' class="js-download-link">' + label + '</a>') | safe), extra=(extra | safe)) }}</li>
        {% else %}
          <li class="list-disc">{{ extra | safe }}</li>
        {% endif %}
      {% else %}
        <li class="list-disc">{{ gettext('page.md5.box.download.no_found') }}</li>
      {% endfor %}
    </ul>

    {% if aarecord_id_split[0] in ['md5','doi','nexusstc_download'] %}
      {% if (aarecord.file_unified_data.problems | length) == 0 %}
        <div class="mb-4 text-sm text-gray-500">{{ gettext('page.md5.box.download.no_issues_notice') }}</div>
      {% endif %}

      <div class="mb-4 pt-4 border-dashed border-t">
        <ul class="list-outside mb-4 ml-5">
          <li class="list-disc mb-2">
            {{ gettext('page.md5.box.download.dl_managers') }}<br>
            {{ gettext(
                'page.md5.box.download.dl_managers.links',
                links=(format_list([
                  (a.html_a('JDownloader', href="https://jdownloader.org/", **a.external_link) | safe),
                ], style='standard') | safe),
            ) }}
          </li>
          <li class="list-disc mb-2">
            {{ gettext('page.md5.box.download.readers') }}<br>
            {{ gettext(
                'page.md5.box.download.readers.links',
                links=(format_list([
                  (a.html_a(gettext('page.md5.box.download.aa_viewer'), href="/view") | safe),
                  (a.html_a('ReadEra', href="https://readera.org/", **a.external_link) | safe),
                  (a.html_a('Calibre', href="https://calibre-ebook.com/", **a.external_link) | safe),
                ], style='standard') | safe),
            ) }}
          </li>
          <li class="list-disc mb-2">
            {{ gettext('page.md5.box.download.conversion') }}<br>
            {{ gettext(
                'page.md5.box.download.conversion.links',
                links=(format_list([
                  (a.html_a('CloudConvert', href="https://cloudconvert.com/epub-to-pdf", **a.external_link) | safe),
                ], style='standard') | safe),
            ) }}
          </li>
          <li class="list-disc mb-2">
            {{ gettext('page.md5.box.download.sendtokindle') }}<br>
            {{ gettext(
                'page.md5.box.download.sendtokindle.links',
                links=(format_list([
                    (a.html_a(gettext('page.md5.box.download.link.send_to_kindle'), href="https://www.amazon.com/sendtokindle", **a.external_link) | safe),
                    (a.html_a(gettext('page.md5.box.download.link.send_to_kobokindle'), href="https://send.djazz.se", **a.external_link) | safe),
                ], style='standard') | safe),
            ) }}
          </li>
          <li class="list-disc mb-2">
            {{ gettext('page.md5.box.download.support') }}<br>
            ✍️ {{ gettext('page.md5.box.download.support.authors') }}<br>
            📚 {{ gettext('page.md5.box.download.support.libraries') }}
          </li>
        </ul>
      </div>
    {% endif %}

    {% if aarecord_id_split[0] == 'md5' %}
      <script>
        (function() {
          const md5 = {{ aarecord_id_split[1] | tojson }};
          for (const el of document.querySelectorAll(".js-download-link")) {
            // Increase counter when clicked.
            el.addEventListener("click", function() {
              try {
                if (window.localStorage['md5_download_counted_new_' + md5]) {
                  return;
                }
                
                const timestamp = new Date().toISOString().replace("T", " ").slice(0, 19);
                const downloadInstance = {
                  "md5": md5,
                  "timestamp": timestamp
                };
                window.localStorage['md5_download_counted_new_' + md5] = JSON.stringify(downloadInstance);
              } catch(e) {
                console.error("Error with localStorage: ", e);
              }
              navigator.sendBeacon("/dyn/downloads/increment/" + md5);
            });
          }
        })();
      </script>
    {% endif %}
  </div>

  {% if aarecord_id_split[0] == 'md5' %}
    <div id="md5-panel-discussion" role="tabpanel" tabindex="0" aria-labelledby="md5-tab-discussion" hidden>
      <a name="quality"></a>
      <h3 class="font-bold">📂 {{ gettext('page.md5.quality.header') }}</h3>

      <p class="mb-4">
        {{ gettext('page.md5.quality.report') }}
      </p>

      <div class="">
        <button class="custom bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow mb-2" onclick='if (localStorage["aa_logged_in"] !== "1") { document.querySelector(".js-discussion-logged-out").classList.toggle("hidden"); return; }; document.querySelector(".js-report-file-issues").classList.toggle("hidden"); document.querySelector(".js-new-comment").classList.add("hidden")'><span class='text-[18px] align-text-bottom text-white inline-block icon-[uil--exclamation-triangle]'></span> <span class="js-md5-button-new-issue-label">{{ gettext('page.md5.quality.report_issue', count=('<output class="js-count">0</output>' | safe)) }}</span></button>

        <span class="inline-block mb-2"><button class="shadow js-md5-button-great-quality custom bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded-l border-r border-[#999] align-bottom [&.selected]:bg-[#555] [&.selected]:pt-[5px] [&.selected]:pb-[3px] [&.selected]:shadow-[inset_0px_-1px_0px_0px_rgba(255,255,255,0.2),_inset_0px_1px_5px_0px_rgba(0,0,0,0.6)]" onclick='if (localStorage["aa_logged_in"] !== "1") { document.querySelector(".js-discussion-logged-out").classList.toggle("hidden"); return; }; fetch("/dyn/reactions/" + (window.md5UserReaction === 2 ? 0 : 2) + "/md5:" + {{ aarecord_id_split[1] | tojson }}, { method: "PUT" }).then(() => window.md5ReloadSummary())'> <span class='text-[21px] align-[-4px] text-white inline-block icon-[material-symbols--star-outline] [button.selected>&]:icon-[material-symbols--star]'></span> <span class="js-md5-button-great-quality-label">{{ gettext('page.md5.quality.great_quality', count=('<output class="js-count">0</output>' | safe)) }}</span></button><button class="disabled shadow js-md5-button-new-comment custom bg-[#777] hover:bg-[#999] [&.disabled]:opacity-40 [&.disabled]:hover:bg-[#777] [&.disabled]:cursor-auto text-white font-bold py-1 px-3 rounded-r" onclick='if (this.classList.contains("disabled")) { return; }; document.querySelector(".js-new-comment").classList.toggle("hidden"); document.querySelector(".js-report-file-issues").classList.add("hidden")'> {{ gettext('page.md5.quality.add_comment', count=('<output class="js-count">0</output>' | safe)) }}</button></span>
      </div>

      <div class="js-discussion-logged-out hidden">{{ gettext('page.md5.quality.logged_out_login', a_login=(' href="/login"' | safe)) }}</div>

      <form class="js-report-file-issues hidden mb-6" onsubmit='window.submitForm(event, "/dyn/md5_report/" + {{ aarecord_id_split[1] | tojson }})'>
        <fieldset>
          <p class="mb-2 font-bold">{{ gettext('page.md5.quality.what_is_wrong') }}</p>
          <select name="type" class="bg-black/6.7 px-2 py-1 rounded mb-4 w-full max-w-[400px]" oninput="for (el of document.querySelectorAll('.js-report-file-issues-submenu')) { el.classList.add('hidden'); } for (el of document.querySelectorAll('.js-report-file-issues-submenu-' + this.value)) { el.classList.remove('hidden'); }">
            <option></option>
            {% for type in md5_report_type_mapping %}
              <option value="{{ type }}">{{ md5_report_type_mapping[type] }}</option>
            {% endfor %}
          </select>

          <div class="hidden mb-4 js-report-file-issues-submenu js-report-file-issues-submenu-copyright">
            <p class="">
              {{ gettext('page.md5.quality.copyright', a_copyright=(' href="/copyright"' | safe)) }}
            </p>
          </div>

          <div class="hidden mb-4 js-report-file-issues-submenu js-report-file-issues-submenu-download js-report-file-issues-submenu-broken js-report-file-issues-submenu-pages js-report-file-issues-submenu-spam js-report-file-issues-submenu-other js-report-file-issues-submenu-metadata">
            <p class="mb-1 font-bold">{{ gettext('page.md5.quality.describe_the_issue') }}</p>
            <textarea required name="content" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full h-[120px]" placeholder="{{ gettext('page.md5.quality.issue_description') }}"></textarea>

            <div class="hidden js-report-file-issues-submenu js-report-file-issues-submenu-download js-report-file-issues-submenu-broken js-report-file-issues-submenu-pages js-report-file-issues-submenu-spam js-report-file-issues-submenu-other">
              <p class="mb-2">
                <strong>{{ gettext('page.md5.quality.better_md5.text1') }}</strong> {{ gettext('page.md5.quality.better_md5.text2', a_upload=' href="/faq#upload" target="_blank"' | safe) }}
              </p>
              <p class="mb-1">
                {{ gettext('page.md5.quality.better_md5.line1') }}<br>
                https://annas-archive.li/md5/<strong>{{ aarecord_id_split[1] }}</strong>
              </p>
              <input type="text" name="better_md5" class="grow bg-black/6.7 px-2 py-1 mb-4 rounded w-full" placeholder="{{ aarecord_id_split[1] }}" minlength="32" maxlength="32" />
            </div>

            <div class="">
              <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.md5.quality.submit_report') }}</button>
              <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
            </div>

            <div class="hidden mt-4 js-report-file-issues-submenu js-report-file-issues-submenu-metadata">
              {{ gettext('page.md5.quality.improve_the_metadata', a_metadata=(' href="/metadata"' | safe)) }}
            </div>
          </div>
        </fieldset>
        <div class="hidden js-success">✅ {{ gettext('page.md5.quality.report_thanks') }}</div>
        <div class="hidden js-failure mb-4">❌ {{ gettext('page.md5.quality.report_error') }}</div>
      </form>

      <form class="js-new-comment hidden mb-4" onsubmit='window.submitForm(event, "/dyn/comments/md5:" + {{ aarecord_id_split[1] | tojson }})'>
        <fieldset>
          <p class="mb-1">
            {{ gettext('page.md5.quality.great.summary') }}
          </p>
          <textarea required name="content" class="grow bg-black/6.7 px-2 py-1 mb-1 rounded w-full h-20" placeholder="{{ gettext('page.md5.quality.loved_the_book') }}"></textarea>
          <div class="">
            <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.md5.quality.submit_comment') }}</button>
            <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
          </div>
        </fieldset>
        <div class="hidden js-success">✅ {{ gettext('page.md5.quality.comment_thanks') }}</div>
        <div class="hidden js-failure mb-4">❌ {{ gettext('page.md5.quality.comment_error') }}</div>
      </form>

      <div class="js-md5-issues-reports mt-4"><span class="mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span></div>
    </div>
  {% endif %}

  {% if aarecord_id_split[0] == 'md5' %}
    <div id="md5-panel-lists" role="tabpanel" tabindex="0" aria-labelledby="md5-tab-lists" hidden>
      {% if gettext('common.english_only') != 'Text below continues in English.' %}
        <p class="mb-4 font-bold">{{ gettext('common.english_only') }}</p>
      {% endif %}

      <div lang="en">
        <script>
          document.getElementById('md5-panel-lists').addEventListener("panelOpen", () => {
            const md5 = {{ aarecord_id_split[1] | tojson }};
            fetch("/dyn/lists/md5:" + md5).then((response) => response.ok ? response.text() : 'Error 921857').then((text) => {
              const reloadNode = document.querySelector(".js-md5-lists-wrapper");
              reloadNode.innerHTML = text;
              window.executeScriptElements(reloadNode);
            });
          });
        </script>
        <div class="js-md5-lists-wrapper"><span class="mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span></div>
      </div>
    </div>

    <div id="md5-panel-stats" role="tabpanel" tabindex="0" aria-labelledby="md5-tab-stats" hidden>
      <div lang="en">
        <p class="mb-4">
          {{ gettext('page.md5.text.stats.total_downloads', total=('<span class="js-md5-stats-total-downloads"><span class="mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span></span>' | safe)) }}<br>
          <div class="js-md5-stats-downloads-chart h-[200px]"></div>
        </p>
        <script>
          document.getElementById('md5-panel-stats').addEventListener("panelOpen", () => {
            const md5 = {{ aarecord_id_split[1] | tojson }};
            fetch("/dyn/downloads/stats/" + md5).then((response) => response.json()).then((json) => {
              document.querySelector(".js-md5-stats-total-downloads").innerText = json.total;
              Plotly.newPlot(document.querySelector(".js-md5-stats-downloads-chart"), [{
                type: "bar",
                x: json.timeseries_x.map((t) => new Date(t * 3600000)),
                y: json.timeseries_y,
                line: {color: '#0095ff'}
              }], {
                margin: {
                  l: 40,
                  r: 16,
                  b: 50,
                  t: 8,
                  pad: 0
                }
              }, {staticPlot: true});
            });
          });
        </script>
      </div>
    </div>
  {% endif %}

  <div id="md5-panel-details" role="tabpanel" tabindex="0" aria-labelledby="md5-tab-details" hidden>
    {% if aarecord_id_split[0] == 'md5' %}
      <p class="mb-4">
        {{ gettext('page.md5.text.md5_info.text1') }}
      </p>

      <p class="mb-4">
        {{ gettext('page.md5.text.md5_info.text2', a_datasets=(' href="/datasets"' | safe)) }}
      </p>
    {% elif aarecord_id_split[0] == 'ia' %}
      <p class="mb-4">
        {{ gettext('page.md5.text.ia_info.text1', a_ia=(' href="https://archive.org/details/inlibrary"' | safe), a_datasets=(' href="/datasets"' | safe)) }}
      </p>
    {% endif %}

    <p class="mb-4">
      {{ gettext('page.md5.text.file_info.text1', a_href=((' href="/db/aarecord_elasticsearch/' | safe) + aarecord_id + '.json.html"' | safe)) }}
      <!-- TODO:TRANSLATE -->
      <a href="/db/aarecord_mysql_debug/{{ aarecord_id }}.json.html">Live/debug JSON version.</a>
      <a href="?livedebug=1">Live/debug page.</a>
    </p>
  </div>
{% endblock %}
