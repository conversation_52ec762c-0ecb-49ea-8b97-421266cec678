{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}
{% import 'macros/helpers.j2' as h %}

{% block title %}{{ gettext('page.faq.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-1 text-3xl font-bold">{{ gettext('page.faq.title') }}</h2>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="what">{{ gettext('page.faq.what_is.title') }} <a href="#what" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="">
    {{ gettext('page.home.intro.text1', span_anna=('class=""' | safe)) }}
  </p>

  <ol class="list-decimal list-inside mb-4">
    {{ gettext('page.home.intro.text2') }}
  </ol>

  <p class="mb-4">
    {{ gettext('page.home.intro.open_source', a_code=(' href="https://software.annas-archive.li/" ' | safe), a_datasets=(' href="/datasets" ' | safe)) }}
  </p>

  <div class="bg-[#f2f2f2] p-4 pb-3 rounded-lg mb-4">
    <div class="mb-1 font-bold text-lg">{{ gettext('page.home.preservation.header') }}</div>

    <p class="mb-4">{{ gettext('page.home.preservation.text1') }}</p>

    <p class="mb-4">{{ gettext('page.home.preservation.text2') }}</p>

    <div style="position: relative; height: 16px; margin-top: 16px;">
      <div style="position: absolute; left: 0; right: 0; top: 0; bottom: 0; background: white; overflow: hidden; border-radius: 16px; box-shadow: 0px 2px 4px 0px #00000038">
        <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 5%; background: #0095ff"></div>
      </div>
      <div style="position: absolute; left: 5%; top: 50%; width: 16px; height: 16px; transform: translate(-50%, -50%)">
        <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: #0095ff66; border-radius: 100%; animation: header-ping 1.5s cubic-bezier(0,0,.2,1) infinite"></div>
        <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: white; border-radius: 100%; box-shadow: 0 0 3px #00000069;"></div>
      </div>
    </div>

    <div style="position: relative; padding-bottom: 12px">
      <div style="width: 14px; height: 14px; border-left: 1px solid gray; border-bottom: 1px solid gray; position: absolute; top: 5px; left: calc(5% - 1px)"></div>
      <div style="position: relative; left: calc(5% + 20px); width: calc(90% - 20px); top: 8px; font-size: 90%; color: #555">{{ gettext('page.home.preservation.label') | replace ('https://annas-blog.org', '/blog') }}</div>
    </div>
  </div>

  <div class="bg-[#f2f2f2] p-4 pb-3 rounded-lg mb-4">
    <div class="mb-1 font-bold text-lg">{{ gettext('page.home.access.header') }}</div>

    <p class="mb-4">{{ gettext('page.home.access.text', a_search=(' href="/search?q=Against%20intellectual%20monopoly"' | safe)) }}</a></p>

    <div class="js-home-stats-downloads-chart h-[200px] mb-2 rounded overflow-hidden"></div>

    <div class="text-center" style="font-size: 90%; color: #555">{{ gettext('page.home.access.label', hourly=('<span class="js-home-stats-downloads-hourly">…</span>' | safe), daily=('<span class="js-home-stats-downloads-daily">…</span>' | safe)) }}</div>

    <script>
      const fetchPromise = fetch("/dyn/downloads/stats/").then((response) => response.json());
      const loadedPromise = new Promise((resolve, reject) => document.addEventListener("DOMContentLoaded", () => { resolve () }));
      Promise.all([fetchPromise, loadedPromise]).then(([json]) => {
        const hourlyAverage = json.timeseries_y.reduce((a,c) => a + c, 0) / json.timeseries_y.length;
        document.querySelector(".js-home-stats-downloads-hourly").innerText = Math.round(hourlyAverage/1000).toLocaleString() + "k";
        document.querySelector(".js-home-stats-downloads-daily").innerText = Math.round(hourlyAverage*24/1000).toLocaleString() + "k";
        Plotly.newPlot(document.querySelector(".js-home-stats-downloads-chart"), [{
          type: "bar",
          x: json.timeseries_x.map((t) => new Date(t * 3600000)),
          y: json.timeseries_y,
          line: {color: '#0095ff'}
        }], {
          margin: {
            l: 40,
            r: 16,
            b: 50,
            t: 12,
            pad: 0
          }
        }, {staticPlot: true});
      });
    </script>
  </div>

  <p class="mb-4">
    {{ gettext('page.about.text2') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.about.text3', email=(a.contact_page_link | safe)) | replace('https://t.me/annasarchiveorg', 'https://t.me/+D0zemuNzEdgyOGVk') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="help">{{ gettext('page.faq.help.title') }} <a href="#help" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <ol class="list-inside mb-4">
    {{ gettext('page.about.help.text') | replace('https://annas-software.org', 'https://software.annas-archive.li') | replace('https://translate.annas-software.org', 'https://translate.annas-archive.li') | replace('https://t.me/annasarchiveorg', 'https://t.me/+D0zemuNzEdgyOGVk') | replace(('</li>' | safe), (('</li><li>' | safe) + gettext('page.volunteering.section.light.matrix', matrix="#annas:archivecommunication.org") + ('</li>' | safe)) | safe, 1) }}
    <li>{{ gettext('page.about.help.text6', a_security=(a.faqs_security | xmlattr)) }}</li>
    <li>{{ gettext('page.about.help.text7') }}</li>
    <li>{{ gettext('page.about.help.text8') }}</li>
    <li>{{ gettext('page.about.help.text9', a_upload=(a.faqs_upload | xmlattr)) }}</li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/250" rel="noopener noreferrer" target="_blank">{{ gettext('page.about.help.text10') }}</a></li>
    <li>{{ gettext('page.about.help.text11') }}</li>
    <li>12. {{ gettext('page.faq.help.mirrors', a_mirrors=(a.gitlab_issue_mirrors | xmlattr)) }}</li>
  </ol>

  <p class="mb-4">
    {{ gettext('page.about.help.volunteer', a_volunteering=(a.volunteering | xmlattr)) }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="slow">{{ gettext('page.faq.slow.title') }} <a href="#slow" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.slow.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.slow.text2') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.slow.text3', a_verification=(a.browser_verification | xmlattr)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.slow.text4') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.slow.text5') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="donate">{{ gettext('page.donate.faq.title') }} <a href="#donate" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <div class="mb-4">
    {{ gettext('page.donate.faq.renew', div_question=(h.bold | xmlattr)) }}
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.membership', div_question=(h.bold | xmlattr)) }}
    {{ gettext('page.account.logged_in.membership_multiple') }}
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.text_other_payment1', div_question=(h.bold | xmlattr), email=(a.contact_page_link | safe)) }}
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.ranges', div_question=(h.bold | xmlattr)) }}
  </div>

  <div class="mb-4">
    <!-- TODO:TRANSLATE -->
    <div {{ h.bold | xmlattr }}>When does the fast downloads counter reset?</div>
    {{ gettext('page.downloaded.fast_download_time') | replace('24', '18') }}
    <!-- TODO:TRANSLATE -->
    Check the <a href="/account/downloaded">Downloaded files page</a> to see which files you downloaded.
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.spend', div_question=(h.bold | xmlattr)) }}
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.text_large_donation', div_question=(h.bold | xmlattr), email=(a.contact_page_link | safe)) }}
  </div>

  <div class="mb-4">
    {{ gettext('page.donate.faq.non_member_donation', div_question=(h.bold | xmlattr), address=(a.xmr_address | safe)) }}
  </div>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="upload">{{ gettext('page.faq.upload.title') }} <a href="#upload" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4 hidden"> <!-- NOTE: HIDDEN! -->
    {{ gettext('page.upload.zlib.text1', a_upload=(' href="https://z-lib.fm/book-add" rel="noopener noreferrer" target="_blank"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.upload.upload_to_both', first=('<a href="https://z-lib.fm/book-add" rel="noopener noreferrer" target="_blank">Z-Library</a>' | safe), second=('<a href="https://libgen.li/librarian.php" rel="noopener noreferrer" target="_blank">Libgen.li</a>' | safe)) }}
    <!-- {{ gettext('page.upload.text1', a_guide=(' href="https://wiki.mhut.org/content:how_to_upload" rel="noopener noreferrer" target="_blank"' | safe)) }} -->
  </p>

  <p class="mb-4">
    {{ gettext('page.upload.libgenli_login_instructions', a_forum=(' href="https://libgen.li/community/ucp.php?mode=login" rel="noopener noreferrer" target="_blank" ' | safe), username=('' | safe), password=('' | safe), a_upload_page=(' href="https://libgen.li/librarian.php" rel="noopener noreferrer" target="_blank" ' | safe)) }}
  </p>

  <p class="mb-4 hidden"> <!-- NOTE: HIDDEN! -->
    {{ gettext('common.libgen.email', a_mail=(' href="https://proton.me/" rel="noopener noreferrer" target="_blank"' | safe), a_manual=(' href="https://forum.mhut.org/viewtopic.php?f=6&t=29" rel="noopener noreferrer" target="_blank"' | safe)) }}
    {{ gettext('page.faq.mhut_upload') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.upload.large.text', a_email=(a.contact_page_link | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.upload.zlib.text2', a_stc_nexus=(a.reddit_science_nexus | xmlattr), a_telegram=(a.nexus_telegram | xmlattr)) }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="request">{{ gettext('page.faq.request.title') }} <a href="#request" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.request.cannot_accomodate') }}
    {{ gettext('page.request.forums') }}
    {{ gettext('page.request.dont_email') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="metadata">{{ gettext('page.faq.metadata.title') }} <a href="#metadata" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    <a href="/datasets">{{ gettext('page.faq.metadata.indeed') }}</a>
    {{ gettext('page.faq.metadata.inspiration',
        a_openlib=(dict(href="https://en.wikipedia.org/wiki/Open_Library") | xmlattr),
        a_blog=(dict(href="/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html") | xmlattr),
    ) }}
  </p>

  <p class="mb-4">
    <!-- TODO:TRANSLATE -->
    <a href="https://github.com/RArtutos/Data-science-starter-kit-Enhance/" rel="noopener noreferrer nofollow" target="_blank">This repo</a> is excellent for getting started with data analysis.
  </p>

  <!-- TODO:TRANSLATE -->
  <h3 class="group mt-4 mb-1 text-xl font-bold" id="categories">Can I browse categories? <a href="#categories" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <!-- TODO:TRANSLATE -->
  <p class="">
    We don't have categories ourselves, but you can use the <a href="/member_codes">Codes Explorer</a> to browse categories from different sources of metadata:<br>
  </p>

  <!-- TODO:TRANSLATE -->
  <ul class="list-inside mb-4">
    <li class="list-disc"><a href="/member_codes?prefix_b64=emxpYl9jYXRlZ29yeV9uYW1lOg==">Z-Library Category</a></li>
    <li class="list-disc"><a href="/member_codes?prefix_b64=bGdyc25mX3RvcGljOg==">Libgen.rs Topic</a></li>
    <li class="list-disc"><a href="/member_codes?prefix_b64=ZWRzZWJrX3N1YmplY3Q6YmlzYWMv">EBSCOhost Subject</a></li>
    <li class="list-disc"><a href="/member_codes?prefix_b64=bWFnemRiX2tleXdvcmQ6">MagzDB Keyword</a></li>
  </ul>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="1984">{{ gettext('page.faq.1984.title') }} <a href="#1984" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.1984.text', a_tor=(' href="https://www.torproject.org/download/"' | safe)) }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="save_search">{{ gettext('page.faq.save_search.title') }} <a href="#save_search" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.save_search.text1') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="mobile">{{ gettext('page.faq.mobile.title') }} <a href="#mobile" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.mobile.text1') }}<br>
    {{ gettext('page.faq.mobile.android') }}<br>
    {{ gettext('page.faq.mobile.ios') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="api">{{ gettext('page.faq.api.title') }} <a href="#api" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.api.text1', a_fast_download=(' href="/dyn/api/fast_download.json"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.api.text2', a_generate=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md"' | safe), a_download=(' href="/torrents#aa_derived_mirror_metadata"' | safe), a_explore=(' href="/db/aarecord_elasticsearch/md5:8336332bf5877e3adbfb60ac70720cd5.json.html"' | safe)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.api.text3', a_torrents=(' href="/dyn/torrents.json"' | safe)) }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="torrents">{{ gettext('page.faq.torrents.title') }} <a href="#torrents" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q1') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a1', a_list=(' href="/torrents#generate_torrent_list"' | safe)) }}
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q2') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a2', a_llm=(a.llm | xmlattr)) }}
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q3') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a3_short_answer') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.torrents.a3_long_answer_start') }}
    {{ gettext('page.faq.torrents.a3', a_generate=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md"' | safe), a_download=(' href="/torrents#aa_derived_mirror_metadata"' | safe)) }}
    {{ gettext('page.faq.torrents.a3_long_answer_ideas', a_ideas=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/219" ' | safe)) }}
    {{ gettext('page.faq.torrents.a3_long_answer_contributions') }}
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q4') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a4') }}
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q5') }}</strong>
    <br>
    <a href="/dyn/torrents.json">{{ gettext('page.faq.torrents.a5') }}</a>
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q6') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a6') }}
    <br>
    {{ gettext('page.faq.torrents.a6.li1', a_libgen_nonfic=(' href="/torrents#libgen_rs_non_fic"' | safe), a_download=(' href="/torrents#aa_derived_mirror_metadata"' | safe), a_datasets=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md"' | safe)) }}
    <br>
    {{ gettext('page.faq.torrents.a6.li2', a_generate=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md"' | safe), a_download=(' href="/datasets"' | safe)) }}
  </p>

  <p class="mb-4">
    <!-- TODO:TRANSLATE -->
    <a href="https://github.com/RArtutos/Data-science-starter-kit-Enhance/" rel="noopener noreferrer nofollow" target="_blank">This repo</a> is excellent for getting started with data analysis.
  </p>

  <p class="mb-4">
    <strong>{{ gettext('page.faq.torrents.q7') }}</strong>
    <br>
    {{ gettext('page.faq.torrents.a7') }}
  </p>

  <!-- TODO:TRANSLATE -->
  <h3 class="group mt-4 mb-1 text-xl font-bold" id="delete">Can I delete my account or my download history? <a href="#delete" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    Unfortunately not. Just throw away your secret key.
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="security">{{ gettext('page.faq.security.title') }} <a href="#security" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.security.text1', a_contact=(a.contact | xmlattr)) }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.security.text2', a_link=(' href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/194" ' | safe)) | replace('5', '15') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.security.text3') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="resources">{{ gettext('page.faq.resources.title') }} <a href="#resources" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <ul class="list-inside mb-4">
    <li class="list-disc">{{ gettext('page.faq.resources.annas_blog', a_blog=(' href="/blog"' | safe), a_reddit_u=(' href="https://www.reddit.com/user/AnnaArchivist"' | safe), a_reddit_r=(' href="https://www.reddit.com/r/Annas_Archive"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.faq.resources.annas_software', a_software=(' href="https://software.annas-archive.li"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.faq.resources.translate', a_translate=(' href="https://translate.annas-archive.li"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.faq.resources.datasets', a_datasets=(' href="/datasets"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.faq.resources.domains', a_li=(' href="https://annas-archive.li"' | safe), a_se=(' href="https://annas-archive.se"' | safe), a_org=(' href="https://annas-archive.org"' | safe)) }}</li>
    <li class="list-disc">{{ gettext('page.faq.resources.wikipedia', a_wikipedia=(' href="https://en.wikipedia.org/wiki/Anna%27s_Archive"' | safe)) }}</li>
  </ul>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="copyright">{{ gettext('page.faq.copyright.title') }} <a href="#copyright" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.copyright.text1') }}
  </p>

  <p class="mb-4">
    {{ gettext('page.faq.copyright.text2', a_copyright=(' href="/copyright"' | safe)) }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="hate">{{ gettext('page.faq.hate.title') }} <a href="#hate" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.hate.text1') }}
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="uptime">{{ gettext('page.faq.uptime.title') }} <a href="#uptime" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.uptime.text1', a_href=(dict(href="https://open-slum.org/", **a.external_link) | xmlattr)) }}
  </p>

<!--   <h3 class="group mt-4 mb-1 text-xl font-bold" id="telegram">Are you going to move off Telegram? <a href="#telegram" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    No. Any events regarding the Telegram organization don’t change anything in terms of operational security. Our channels there have always been public, so you should always assume that what you say in them is public.
  </p>

  <p class="mb-4">
    Asking about moving to another chat platform will result in one warning, then a ban.
  </p> -->

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="physical">{{ gettext('page.faq.physical.title') }} <a href="#physical" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext( 'page.faq.physical.text1', a_archive=(dict(href="https://help.archive.org/help/how-do-i-make-a-physical-donation-to-the-internet-archive/", **a.external_link) | xmlattr) ) }}
  </p>

  <!-- TODO:TRANSLATE -->
  <h3 class="group mt-4 mb-1 text-xl font-bold" id="anna">What are your official mirrors? <a href="#mirrors" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    Currently our official mirrors are:<br>
    - annas-archive.org<br>
    - annas-archive.se<br>
    - annas-archive.li<br>
  </p>

  <p class="mb-4">
    We’re also aware of welib.org. They have forked our codebase and files. However, they unfortunately haven’t released their new code as open source. They are not affiliated with us.
  </p>
  
  <h3 class="group mt-4 mb-1 text-xl font-bold" id="anna">{{ gettext('page.faq.anna.title') }} <a href="#anna" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    <a rel="noopener noreferrer" target="_blank" href="https://www.reddit.com/r/Annas_Archive/comments/1f6h74r/im_curious_actually_who_is_anna/">
      {{ gettext('page.faq.anna.text1') }}
    </a>
  </p>

  <h3 class="group mt-4 mb-1 text-xl font-bold" id="favorite">{{ gettext('page.faq.favorite.title') }} <a href="#favorite" class="custom-a invisible group-hover:visible text-gray-400 hover:text-gray-500 font-normal text-sm align-[2px]">§</a></h3>

  <p class="mb-4">
    {{ gettext('page.faq.favorite.text1') }}
  </p>

  {% for aarecord in aarecords %}
    <a href="/search?q={{aarecord.file_unified_data.title_best | urlencode}}" class="custom-a flex items-center relative left-[-10] px-[10] py-2 hover:bg-black/6.7" rel="nofollow">
      <div class="flex-none">
        <div class="relative overflow-hidden w-[72px] h-[108px] flex flex-col justify-center">
          <div class="absolute w-full h-[90px]" style="background-color: hsl({{ (loop.index0 % 4) * (256//3) + (range(0, 256//3) | random) }}deg 43% 73%)"></div>
          <img class="relative inline-block" src="{{aarecord.file_unified_data.cover_url_best if 'zlibcdn2' not in aarecord.file_unified_data.cover_url_best}}" alt="" referrerpolicy="no-referrer" onerror="this.parentNode.removeChild(this)" loading="lazy" decoding="async"/>
        </div>
      </div>
      <div class="relative top-[-1] pl-4 grow overflow-hidden">
        <h3 class="group text-xl font-bold">{{aarecord.file_unified_data.title_best}}</h3>
        <div class="text-lg italic">{{aarecord.file_unified_data.author_best}}</div>
      </div>
    </a>
  {% endfor %}
{% endblock %}
