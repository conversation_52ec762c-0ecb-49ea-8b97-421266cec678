[{"category": "Arts", "id": "1", "subcategories": [{"name": "Architecture", "id": "40", "type": "non-fiction", "count": "17063"}, {"name": "Business of Art", "id": "49", "type": "non-fiction", "count": "462"}, {"name": "Conservation, Restoration & Care", "id": "51", "type": "non-fiction", "count": "327"}, {"name": "Contemporary Art", "id": "46", "type": "non-fiction", "count": "1389"}, {"name": "Dance", "id": "754", "type": "non-fiction", "count": "608"}, {"name": "Digital Music", "id": "179", "type": "non-fiction", "count": "247"}, {"name": "Fashion, Decorative Arts & Design", "id": "47", "type": "non-fiction", "count": "3075"}, {"name": "Film", "id": "44", "type": "non-fiction", "count": "11253"}, {"name": "Graphic Arts", "id": "41", "type": "non-fiction", "count": "10981"}, {"name": "History & Criticism", "id": "50", "type": "non-fiction", "count": "16203"}, {"name": "Museums & Collections", "id": "53", "type": "non-fiction", "count": "1638"}, {"name": "Music", "id": "42", "type": "non-fiction", "count": "35369"}, {"name": "Painting", "id": "56", "type": "non-fiction", "count": "4511"}, {"name": "Performing Arts", "id": "48", "type": "non-fiction", "count": "7889"}, {"name": "Photography", "id": "43", "type": "non-fiction", "count": "16447"}, {"name": "Sculpture", "id": "45", "type": "non-fiction", "count": "543"}, {"name": "Study & Teaching", "id": "54", "type": "non-fiction", "count": "2277"}, {"name": "Techniques", "id": "52", "type": "non-fiction", "count": "2203"}]}, {"category": "Astronomy", "id": "2", "subcategories": [{"name": "Astronomers & Astrophysicists - Biography", "id": "57", "type": "non-fiction", "count": "234"}, {"name": "Astronomical Star Guides", "id": "58", "type": "non-fiction", "count": "259"}, {"name": "Astrophysics & Space Science", "id": "62", "type": "non-fiction", "count": "1079"}, {"name": "Cosmology", "id": "60", "type": "non-fiction", "count": "1020"}, {"name": "Galaxies - Astronomical Studies & Observations", "id": "63", "type": "non-fiction", "count": "142"}, {"name": "History of Astronomy", "id": "61", "type": "non-fiction", "count": "689"}, {"name": "Outer Space - Observation & Exploration", "id": "64", "type": "non-fiction", "count": "374"}, {"name": "Stars - Astronomical Studies & Observations", "id": "65", "type": "non-fiction", "count": "229"}, {"name": "The Solar System - Astronomical Studies & Observations", "id": "66", "type": "non-fiction", "count": "346"}, {"name": "The Universe - Astonomical Studies & Observations", "id": "67", "type": "non-fiction", "count": "462"}]}, {"category": "Biography & Autobiography", "id": "3", "subcategories": [{"name": "Artists, Architects & Photographers", "id": "68", "type": "non-fiction", "count": "9895"}, {"name": "Business & Finance", "id": "70", "type": "non-fiction", "count": "3906"}, {"name": "Education Biography", "id": "72", "type": "non-fiction", "count": "1057"}, {"name": "Entertainment Biography", "id": "73", "type": "non-fiction", "count": "2697"}, {"name": "Essays, Journals & Letters", "id": "74", "type": "non-fiction", "count": "7994"}, {"name": "Film, Television & Music", "id": "75", "type": "non-fiction", "count": "7291"}, {"name": "General & Miscellaneous Biography", "id": "76", "type": "non-fiction", "count": "6479"}, {"name": "Historical", "id": "77", "type": "non-fiction", "count": "39540"}, {"name": "Holocaust", "id": "78", "type": "non-fiction", "count": "2407"}, {"name": "Literary Biography", "id": "79", "type": "non-fiction", "count": "7623"}, {"name": "Medical, Legal & Social Sciences", "id": "80", "type": "non-fiction", "count": "1676"}, {"name": "Novelists, Poets & Playwrights", "id": "81", "type": "non-fiction", "count": "5728"}, {"name": "Peoples & Cultures - Biography", "id": "82", "type": "non-fiction", "count": "11933"}, {"name": "Political", "id": "83", "type": "non-fiction", "count": "14424"}, {"name": "Reference", "id": "84", "type": "non-fiction", "count": "384"}, {"name": "Religious", "id": "85", "type": "non-fiction", "count": "3003"}, {"name": "Science, Mathematics & Technology", "id": "86", "type": "non-fiction", "count": "3900"}, {"name": "Social & Health Issues", "id": "87", "type": "non-fiction", "count": "1398"}, {"name": "Sports & Adventure Biography", "id": "88", "type": "non-fiction", "count": "4754"}, {"name": "Theatre & Performance Art", "id": "89", "type": "non-fiction", "count": "1099"}, {"name": "Tragic Life Stories", "id": "90", "type": "non-fiction", "count": "2175"}, {"name": "True Crime", "id": "91", "type": "non-fiction", "count": "7183"}, {"name": "War & Espionage", "id": "92", "type": "non-fiction", "count": "5215"}]}, {"category": "Biology and other natural sciences", "id": "4", "subcategories": [{"name": "Biology", "id": "94", "type": "non-fiction", "count": "10458"}, {"name": "Biophysics", "id": "97", "type": "non-fiction", "count": "935"}, {"name": "Biostatistics", "id": "98", "type": "non-fiction", "count": "484"}, {"name": "Biotechnology", "id": "99", "type": "non-fiction", "count": "1817"}, {"name": "Cytology", "id": "109", "type": "non-fiction", "count": "119"}, {"name": "Ecology", "id": "100", "type": "non-fiction", "count": "11947"}, {"name": "Genetics", "id": "101", "type": "non-fiction", "count": "3255"}, {"name": "History of Biology", "id": "110", "type": "non-fiction", "count": "599"}, {"name": "Human Biology", "id": "741", "type": "non-fiction", "count": "2900"}, {"name": "Microbiology", "id": "102", "type": "non-fiction", "count": "2080"}, {"name": "Molecular", "id": "103", "type": "non-fiction", "count": "1961"}, {"name": "Paleontology", "id": "108", "type": "non-fiction", "count": "2212"}, {"name": "Plants: Agriculture and Forestry", "id": "104", "type": "non-fiction", "count": "12265"}, {"name": "Plants: Botany", "id": "105", "type": "non-fiction", "count": "7776"}, {"name": "Virology", "id": "106", "type": "non-fiction", "count": "443"}, {"name": "Zoology", "id": "107", "type": "non-fiction", "count": "8771"}]}, {"category": "Business & Economics", "id": "5", "subcategories": [{"name": "Accounting", "id": "111", "type": "non-fiction", "count": "7735"}, {"name": "E-Commerce", "id": "112", "type": "non-fiction", "count": "1100"}, {"name": "Econometrics", "id": "121", "type": "non-fiction", "count": "3233"}, {"name": "Economics", "id": "819", "type": "non-fiction", "count": "5230"}, {"name": "Human Resources", "id": "773", "type": "non-fiction", "count": "6825"}, {"name": "Industries", "id": "757", "type": "non-fiction", "count": "7646"}, {"name": "Investing", "id": "122", "type": "non-fiction", "count": "11328"}, {"name": "Job Hunting & Careers", "id": "774", "type": "non-fiction", "count": "1615"}, {"name": "Logistics", "id": "116", "type": "non-fiction", "count": "1667"}, {"name": "Management & Leadership", "id": "113", "type": "non-fiction", "count": "49768"}, {"name": "Markets", "id": "123", "type": "non-fiction", "count": "2808"}, {"name": "Mathematical Economics", "id": "124", "type": "non-fiction", "count": "17752"}, {"name": "Others", "id": "756", "type": "non-fiction", "count": "7905"}, {"name": "Personal Finance", "id": "118", "type": "non-fiction", "count": "8958"}, {"name": "Popular", "id": "125", "type": "non-fiction", "count": "2013"}, {"name": "Professional Finance", "id": "119", "type": "non-fiction", "count": "3717"}, {"name": "Project Management", "id": "115", "type": "non-fiction", "count": "2343"}, {"name": "Real Estate", "id": "758", "type": "non-fiction", "count": "1533"}, {"name": "Responsibility and Business Ethics", "id": "117", "type": "non-fiction", "count": "6751"}, {"name": "Sales & Marketing", "id": "114", "type": "non-fiction", "count": "17969"}, {"name": "Small Business", "id": "120", "type": "non-fiction", "count": "3845"}, {"name": "Trading", "id": "820", "type": "non-fiction", "count": "844"}]}, {"category": "Chemistry", "id": "6", "subcategories": [{"name": "Analytical Chemistry", "id": "126", "type": "non-fiction", "count": "1903"}, {"name": "Biochemistry", "id": "133", "type": "non-fiction", "count": "2435"}, {"name": "Chemistry - General & Miscellaneous", "id": "135", "type": "non-fiction", "count": "2125"}, {"name": "History of Chemistry", "id": "130", "type": "non-fiction", "count": "281"}, {"name": "Inorganic Chemistry", "id": "127", "type": "non-fiction", "count": "1781"}, {"name": "Microchemistry", "id": "131", "type": "non-fiction", "count": "48"}, {"name": "Organic Chemistry", "id": "128", "type": "non-fiction", "count": "4719"}, {"name": "Physical Chemistry", "id": "129", "type": "non-fiction", "count": "2099"}, {"name": "Technical & Industrial Chemistry", "id": "132", "type": "non-fiction", "count": "1327"}]}, {"category": "Children's Books", "id": "7", "subcategories": [{"name": "Activities, Crafts & Games", "id": "136", "type": "non-fiction", "count": "1828"}, {"name": "Animals", "id": "137", "type": "non-fiction", "count": "4742"}, {"name": "Arts, Music & Photography", "id": "138", "type": "non-fiction", "count": "1169"}, {"name": "Cars, Trains & Things That Go", "id": "139", "type": "non-fiction", "count": "311"}, {"name": "Comics & Graphic Novels", "id": "140", "type": "fiction", "count": "3274"}, {"name": "Computers & Technology", "id": "141", "type": "non-fiction", "count": "721"}, {"name": "Early Learning", "id": "142", "type": "non-fiction", "count": "1546"}, {"name": "Education & Reference", "id": "143", "type": "non-fiction", "count": "3197"}, {"name": "Fairy Tales, Folk Tales & Myths", "id": "144", "type": "fiction", "count": "5773"}, {"name": "Geography & Cultures", "id": "145", "type": "non-fiction", "count": "1420"}, {"name": "Growing Up & Facts of Life", "id": "146", "type": "non-fiction", "count": "15405"}, {"name": "History", "id": "147", "type": "non-fiction", "count": "2295"}, {"name": "Holidays & Celebrations", "id": "148", "type": "non-fiction", "count": "599"}, {"name": "<PERSON><PERSON>", "id": "149", "type": "non-fiction", "count": "1625"}, {"name": "Literature & Fiction", "id": "150", "type": "fiction", "count": "26622"}, {"name": "Mysteries & Detective Stories", "id": "151", "type": "fiction", "count": "9059"}, {"name": "Others", "id": "718", "type": "non-fiction", "count": "2428"}, {"name": "Religions", "id": "152", "type": "non-fiction", "count": "643"}, {"name": "Science Fiction & Fantasy", "id": "153", "type": "fiction", "count": "27454"}, {"name": "Science, Nature & How It Works", "id": "154", "type": "non-fiction", "count": "2530"}, {"name": "Sports & Outdoors", "id": "155", "type": "non-fiction", "count": "305"}]}, {"category": "Comics & Graphic Novels", "id": "8", "subcategories": [{"name": "Alternative & Underground Comics", "id": "156", "type": "fiction", "count": "1101"}, {"name": "Anime & Manga", "id": "157", "type": "fiction", "count": "34564"}, {"name": "Cartooning", "id": "158", "type": "fiction", "count": "1935"}, {"name": "Children's", "id": "159", "type": "fiction", "count": "2923"}, {"name": "Comic Books - History & Criticism", "id": "160", "type": "fiction", "count": "1404"}, {"name": "Comics & Graphic Novels - General & Miscellaneous", "id": "161", "type": "fiction", "count": "19168"}, {"name": "DC Comics & Graphic Novels", "id": "163", "type": "fiction", "count": "2265"}, {"name": "Marvel Comics & Graphic Novels", "id": "164", "type": "fiction", "count": "684"}, {"name": "Mystery & Thriller Comics & Graphic Novels", "id": "165", "type": "fiction", "count": "1409"}, {"name": "Others", "id": "762", "type": "fiction", "count": "1707"}, {"name": "Science Fiction, Fantasy & Horror Comics & Graphic Novels", "id": "166", "type": "fiction", "count": "4103"}, {"name": "Superhero Comics & Graphic Novels", "id": "167", "type": "fiction", "count": "896"}]}, {"category": "Computers", "id": "10", "subcategories": [{"name": "Algorithms and Data Structures", "id": "168", "type": "non-fiction", "count": "4550"}, {"name": "Applications & Software", "id": "760", "type": "non-fiction", "count": "13912"}, {"name": "Artificial Intelligence (AI)", "id": "169", "type": "non-fiction", "count": "7559"}, {"name": "Computer Business & Culture", "id": "170", "type": "non-fiction", "count": "1726"}, {"name": "Computer Certification & Training", "id": "171", "type": "non-fiction", "count": "3084"}, {"name": "Computer Graphics & Design", "id": "172", "type": "non-fiction", "count": "5593"}, {"name": "Computer Science", "id": "173", "type": "non-fiction", "count": "34510"}, {"name": "Computers - General & Miscellaneous", "id": "174", "type": "non-fiction", "count": "1538"}, {"name": "Cryptography", "id": "175", "type": "non-fiction", "count": "1381"}, {"name": "Cybernetics", "id": "176", "type": "non-fiction", "count": "2180"}, {"name": "Databases", "id": "177", "type": "non-fiction", "count": "8270"}, {"name": "Digital Photography", "id": "180", "type": "non-fiction", "count": "408"}, {"name": "Digital Video", "id": "181", "type": "non-fiction", "count": "149"}, {"name": "Enterprise Computing Systems", "id": "182", "type": "non-fiction", "count": "882"}, {"name": "Hardware", "id": "183", "type": "non-fiction", "count": "7691"}, {"name": "Home Computer User & Beginner", "id": "184", "type": "non-fiction", "count": "444"}, {"name": "Information Systems", "id": "185", "type": "non-fiction", "count": "2656"}, {"name": "Internet & World Wide Web", "id": "187", "type": "non-fiction", "count": "1907"}, {"name": "Lectures, monographs", "id": "188", "type": "non-fiction", "count": "108"}, {"name": "Mac OS", "id": "189", "type": "non-fiction", "count": "189"}, {"name": "Media", "id": "190", "type": "non-fiction", "count": "569"}, {"name": "Microsoft Windows", "id": "191", "type": "non-fiction", "count": "757"}, {"name": "Networking", "id": "192", "type": "non-fiction", "count": "25124"}, {"name": "New to Computing", "id": "193", "type": "non-fiction", "count": "316"}, {"name": "Operating Systems", "id": "194", "type": "non-fiction", "count": "8169"}, {"name": "Organization and Data Processing", "id": "195", "type": "non-fiction", "count": "2920"}, {"name": "Other", "id": "775", "type": "non-fiction", "count": "1613"}, {"name": "PC & Video Games", "id": "196", "type": "non-fiction", "count": "2880"}, {"name": "Professionals", "id": "197", "type": "non-fiction", "count": "224"}, {"name": "Programming", "id": "198", "type": "non-fiction", "count": "75840"}, {"name": "Security", "id": "199", "type": "non-fiction", "count": "6580"}, {"name": "UNIX & Linux", "id": "200", "type": "non-fiction", "count": "2281"}, {"name": "Web Development", "id": "186", "type": "non-fiction", "count": "13194"}]}, {"category": "Cookbooks, Food & Wine", "id": "311", "subcategories": [{"name": "Asian", "id": "844", "type": "non-fiction", "count": "2595"}, {"name": "Baking & Desserts", "id": "838", "type": "non-fiction", "count": "4274"}, {"name": "Barbecue & Grilling", "id": "839", "type": "non-fiction", "count": "467"}, {"name": "<PERSON><PERSON><PERSON>'s cookbook", "id": "846", "type": "non-fiction", "count": "4652"}, {"name": "French", "id": "843", "type": "non-fiction", "count": "516"}, {"name": "International", "id": "845", "type": "non-fiction", "count": "4469"}, {"name": "Italian", "id": "842", "type": "non-fiction", "count": "1279"}, {"name": "Quick & Easy Meals", "id": "847", "type": "non-fiction", "count": "1258"}, {"name": "Special Diet", "id": "848", "type": "non-fiction", "count": "2245"}, {"name": "Vegetarian & Vegan", "id": "840", "type": "non-fiction", "count": "1569"}, {"name": "Wine & Beverages", "id": "841", "type": "non-fiction", "count": "1416"}]}, {"category": "Crime, Thrillers & Mystery", "id": "11", "subcategories": [{"name": "Action & Adventure", "id": "201", "type": "fiction", "count": "32773"}, {"name": "Anthologies", "id": "202", "type": "fiction", "count": "1133"}, {"name": "Cozy Mysteries & Amateur Sleuths", "id": "204", "type": "fiction", "count": "68620"}, {"name": "Detective & Crime Fiction", "id": "205", "type": "fiction", "count": "142917"}, {"name": "Historical", "id": "206", "type": "fiction", "count": "20651"}, {"name": "Legal", "id": "207", "type": "fiction", "count": "4578"}, {"name": "Medical", "id": "208", "type": "fiction", "count": "2858"}, {"name": "Military Thrillers", "id": "209", "type": "fiction", "count": "2559"}, {"name": "Other Mystery Categories", "id": "211", "type": "fiction", "count": "70965"}, {"name": "Other Thrillers Categories", "id": "212", "type": "fiction", "count": "17521"}, {"name": "Police Stories", "id": "213", "type": "fiction", "count": "13810"}, {"name": "Political", "id": "214", "type": "fiction", "count": "1384"}, {"name": "Psychological", "id": "215", "type": "fiction", "count": "9202"}, {"name": "Series", "id": "216", "type": "fiction", "count": "15020"}, {"name": "Short Stories", "id": "218", "type": "fiction", "count": "15053"}, {"name": "Spy Stories", "id": "219", "type": "fiction", "count": "8102"}, {"name": "Technothrillers", "id": "220", "type": "fiction", "count": "1911"}, {"name": "Thrillers", "id": "221", "type": "fiction", "count": "214352"}, {"name": "True Crime", "id": "818", "type": "fiction", "count": "2118"}]}, {"category": "Earth Sciences", "id": "722", "subcategories": [{"name": "Cartography", "id": "288", "type": "non-fiction", "count": "1592"}, {"name": "Earth History", "id": "743", "type": "non-fiction", "count": "714"}, {"name": "Geochemistry", "id": "744", "type": "non-fiction", "count": "92"}, {"name": "Geodesy", "id": "287", "type": "non-fiction", "count": "146"}, {"name": "Geography", "id": "15", "type": "non-fiction", "count": "8043"}, {"name": "Geology", "id": "16", "type": "non-fiction", "count": "9119"}, {"name": "Geophysics", "id": "491", "type": "non-fiction", "count": "1162"}, {"name": "Hydrogeology", "id": "291", "type": "non-fiction", "count": "1619"}, {"name": "Meteorology, Climatology", "id": "290", "type": "non-fiction", "count": "3274"}, {"name": "Mineralogy", "id": "134", "type": "non-fiction", "count": "246"}, {"name": "Mining", "id": "292", "type": "non-fiction", "count": "1931"}, {"name": "Oceanography", "id": "745", "type": "non-fiction", "count": "309"}, {"name": "Palaeontology", "id": "746", "type": "non-fiction", "count": "178"}, {"name": "Reference", "id": "747", "type": "non-fiction", "count": "344"}, {"name": "Rivers & Lakes", "id": "748", "type": "non-fiction", "count": "108"}, {"name": "The Environment", "id": "749", "type": "non-fiction", "count": "2338"}, {"name": "Volcanoes, Earthquakes & Tectonics", "id": "750", "type": "non-fiction", "count": "541"}]}, {"category": "Education Studies & Teaching", "id": "12", "subcategories": [{"name": "Academic Administration", "id": "223", "type": "non-fiction", "count": "737"}, {"name": "Adult & Continuing Education", "id": "224", "type": "non-fiction", "count": "890"}, {"name": "Bilingual & Multicultural Education", "id": "225", "type": "non-fiction", "count": "667"}, {"name": "Education - General & Miscellaneous", "id": "226", "type": "non-fiction", "count": "7918"}, {"name": "Education Management & Organisation", "id": "228", "type": "non-fiction", "count": "895"}, {"name": "Educational Guidance & Counseling", "id": "229", "type": "non-fiction", "count": "1603"}, {"name": "Educational Theory, Research & History", "id": "231", "type": "non-fiction", "count": "2842"}, {"name": "Elementary", "id": "232", "type": "non-fiction", "count": "5850"}, {"name": "Higher & Further Education", "id": "233", "type": "non-fiction", "count": "2414"}, {"name": "History of Education", "id": "234", "type": "non-fiction", "count": "457"}, {"name": "Homeschooling", "id": "235", "type": "non-fiction", "count": "349"}, {"name": "Individual Colleges & Universities", "id": "236", "type": "non-fiction", "count": "218"}, {"name": "International Conferences and Symposiums", "id": "237", "type": "non-fiction", "count": "414"}, {"name": "Philosophy of Education", "id": "239", "type": "non-fiction", "count": "1598"}, {"name": "Pre-school & Early Learning", "id": "238", "type": "non-fiction", "count": "544"}, {"name": "School Education & Teaching", "id": "240", "type": "non-fiction", "count": "24356"}, {"name": "Special Education", "id": "241", "type": "non-fiction", "count": "781"}, {"name": "Studying & Test Preparation", "id": "242", "type": "non-fiction", "count": "3705"}, {"name": "Teaching & Teacher Training", "id": "244", "type": "non-fiction", "count": "1205"}, {"name": "Teaching - Reading & Language", "id": "243", "type": "non-fiction", "count": "2251"}, {"name": "Theses abstracts", "id": "245", "type": "non-fiction", "count": "1426"}, {"name": "Vocational", "id": "721", "type": "non-fiction", "count": "170"}]}, {"category": "Engineering", "id": "13", "subcategories": [{"name": "Aerospace Engineering", "id": "246", "type": "non-fiction", "count": "1453"}, {"name": "Automotive", "id": "723", "type": "non-fiction", "count": "2754"}, {"name": "Bioengineering", "id": "724", "type": "non-fiction", "count": "365"}, {"name": "Chemical Engineering", "id": "247", "type": "non-fiction", "count": "681"}, {"name": "Civil & Structural Engineering", "id": "248", "type": "non-fiction", "count": "3602"}, {"name": "Computer Technology", "id": "249", "type": "non-fiction", "count": "1996"}, {"name": "Construction & Building Trades", "id": "250", "type": "non-fiction", "count": "1071"}, {"name": "Electrical & Electronic Engineering", "id": "251", "type": "non-fiction", "count": "6410"}, {"name": "Energy & Power Resources", "id": "252", "type": "non-fiction", "count": "1886"}, {"name": "Engineering - General & Miscellaneous", "id": "253", "type": "non-fiction", "count": "2824"}, {"name": "Engineering Technology", "id": "254", "type": "non-fiction", "count": "1651"}, {"name": "Environmental", "id": "725", "type": "non-fiction", "count": "1465"}, {"name": "Hydraulic Engineering", "id": "255", "type": "non-fiction", "count": "571"}, {"name": "Industrial Engineering & Materials Science", "id": "256", "type": "non-fiction", "count": "2361"}, {"name": "Mechanical Engineering & Dynamics", "id": "257", "type": "non-fiction", "count": "10647"}, {"name": "Robotics & Artificial Intelligence", "id": "258", "type": "non-fiction", "count": "807"}, {"name": "Social & Cultural Aspects of Technology", "id": "259", "type": "non-fiction", "count": "1633"}, {"name": "Telecommunications", "id": "260", "type": "non-fiction", "count": "1432"}]}, {"category": "Erotica", "id": "712", "subcategories": [{"name": "Arts & Photography", "id": "713", "type": "non-fiction", "count": "4631"}, {"name": "Comics & Manga", "id": "714", "type": "fiction", "count": "14453"}, {"name": "Fiction", "id": "715", "type": "fiction", "count": "64932"}, {"name": "LGBTQ+", "id": "810", "type": "fiction", "count": "3413"}, {"name": "<PERSON>, <PERSON><PERSON><PERSON>", "id": "524", "type": "non-fiction", "count": "28717"}, {"name": "Others", "id": "716", "type": "fiction", "count": "3739"}]}, {"category": "Fantasy", "id": "794", "subcategories": [{"name": "Dark Fantasy", "id": "834", "type": "fiction", "count": "8133"}, {"name": "Fairy Tales", "id": "835", "type": "fiction", "count": "1506"}, {"name": "High Fantasy", "id": "830", "type": "fiction", "count": "14486"}, {"name": "Low Fantasy", "id": "831", "type": "fiction", "count": "3463"}, {"name": "Magical Realism", "id": "832", "type": "fiction", "count": "1881"}, {"name": "Other Fantasy Fiction Categories", "id": "576", "type": "fiction", "count": "34557"}, {"name": "Role-Playing Game Fiction", "id": "578", "type": "fiction", "count": "6985"}, {"name": "Superhero Fiction", "id": "836", "type": "fiction", "count": "682"}, {"name": "Sword And Sorcery", "id": "833", "type": "fiction", "count": "3790"}, {"name": "Urban Fantasy", "id": "837", "type": "fiction", "count": "16844"}]}, {"category": "Fiction", "id": "14", "subcategories": [{"name": "Adventure Stories & Action", "id": "261", "type": "fiction", "count": "25859"}, {"name": "American Fiction", "id": "262", "type": "fiction", "count": "36295"}, {"name": "Anthologies", "id": "263", "type": "fiction", "count": "4118"}, {"name": "Classics", "id": "265", "type": "fiction", "count": "41126"}, {"name": "Contemporary Fiction", "id": "266", "type": "fiction", "count": "105150"}, {"name": "Drama", "id": "768", "type": "fiction", "count": "16852"}, {"name": "Essays", "id": "769", "type": "fiction", "count": "12774"}, {"name": "Family Sagas", "id": "269", "type": "fiction", "count": "7449"}, {"name": "Fantasy", "id": "811", "type": "fiction", "count": "12302"}, {"name": "Historical", "id": "270", "type": "fiction", "count": "67965"}, {"name": "Horror", "id": "271", "type": "fiction", "count": "53121"}, {"name": "<PERSON><PERSON>", "id": "272", "type": "fiction", "count": "32101"}, {"name": "Literary Fiction", "id": "273", "type": "fiction", "count": "86267"}, {"name": "Metaphysical & Visionary", "id": "274", "type": "fiction", "count": "1852"}, {"name": "Myths & Folklore", "id": "275", "type": "fiction", "count": "10498"}, {"name": "Nautical & Maritime Fiction", "id": "276", "type": "fiction", "count": "1867"}, {"name": "Others", "id": "770", "type": "fiction", "count": "23714"}, {"name": "Peoples & Cultures - Fiction", "id": "277", "type": "fiction", "count": "7684"}, {"name": "Psychological", "id": "278", "type": "fiction", "count": "4776"}, {"name": "Religious & Inspirational", "id": "279", "type": "fiction", "count": "16861"}, {"name": "Short Stories", "id": "281", "type": "fiction", "count": "25282"}, {"name": "War & Military Fiction", "id": "283", "type": "fiction", "count": "9931"}, {"name": "Westerns", "id": "284", "type": "fiction", "count": "19344"}, {"name": "Women's Fiction", "id": "285", "type": "fiction", "count": "26411"}, {"name": "World Fiction", "id": "286", "type": "fiction", "count": "29770"}, {"name": "Young Adult", "id": "812", "type": "fiction", "count": "6296"}]}, {"category": "History", "id": "17", "subcategories": [{"name": "African History", "id": "299", "type": "non-fiction", "count": "3743"}, {"name": "American Studies", "id": "294", "type": "non-fiction", "count": "40479"}, {"name": "Ancient History", "id": "298", "type": "non-fiction", "count": "21072"}, {"name": "Archaeology", "id": "295", "type": "non-fiction", "count": "14923"}, {"name": "Asian History", "id": "300", "type": "non-fiction", "count": "31335"}, {"name": "Australian & Oceanian History", "id": "301", "type": "non-fiction", "count": "1313"}, {"name": "Canadian History", "id": "302", "type": "non-fiction", "count": "1094"}, {"name": "Chinese History", "id": "807", "type": "non-fiction", "count": "20810"}, {"name": "European History", "id": "303", "type": "non-fiction", "count": "59887"}, {"name": "Indian History", "id": "806", "type": "non-fiction", "count": "1888"}, {"name": "Islamic History", "id": "809", "type": "non-fiction", "count": "700"}, {"name": "Latin American History", "id": "304", "type": "non-fiction", "count": "6071"}, {"name": "Middle Ages", "id": "808", "type": "non-fiction", "count": "3084"}, {"name": "Middle Eastern History", "id": "305", "type": "non-fiction", "count": "6437"}, {"name": "Military History", "id": "297", "type": "non-fiction", "count": "53852"}, {"name": "Russian & Soviet History", "id": "306", "type": "non-fiction", "count": "9678"}, {"name": "World History", "id": "307", "type": "non-fiction", "count": "39372"}]}, {"category": "Housekeeping & Leisure", "id": "18", "subcategories": [{"name": "Activity & Game Books", "id": "755", "type": "non-fiction", "count": "610"}, {"name": "Antiques & Collectables", "id": "310", "type": "non-fiction", "count": "2408"}, {"name": "Drawing", "id": "55", "type": "non-fiction", "count": "1667"}, {"name": "Fashion, Jewelry", "id": "312", "type": "non-fiction", "count": "1679"}, {"name": "Floral & Nature Crafts", "id": "319", "type": "non-fiction", "count": "1134"}, {"name": "Games: Board Games", "id": "313", "type": "non-fiction", "count": "2264"}, {"name": "Games: Chess", "id": "314", "type": "non-fiction", "count": "8768"}, {"name": "Gardening", "id": "308", "type": "non-fiction", "count": "8926"}, {"name": "Glass & Metal Work", "id": "321", "type": "non-fiction", "count": "285"}, {"name": "Handicraft", "id": "315", "type": "non-fiction", "count": "10738"}, {"name": "Handicraft: Cutting and Sewing", "id": "316", "type": "non-fiction", "count": "5245"}, {"name": "Interior Design & Decoration", "id": "322", "type": "non-fiction", "count": "10346"}, {"name": "Modelmaking", "id": "318", "type": "non-fiction", "count": "2075"}, {"name": "Others", "id": "761", "type": "non-fiction", "count": "1181"}, {"name": "Papercrafts", "id": "317", "type": "non-fiction", "count": "1892"}, {"name": "Role-Playing & Fantasy Games", "id": "766", "type": "non-fiction", "count": "11635"}, {"name": "Woodworking & Carving", "id": "320", "type": "non-fiction", "count": "1723"}]}, {"category": "Jurisprudence & Law", "id": "20", "subcategories": [{"name": "Business, Commercial & Financial Law", "id": "349", "type": "non-fiction", "count": "4278"}, {"name": "Civil Rights Law", "id": "350", "type": "non-fiction", "count": "1952"}, {"name": "Constitutional Law", "id": "351", "type": "non-fiction", "count": "2872"}, {"name": "Courts & Trial Practice", "id": "352", "type": "non-fiction", "count": "439"}, {"name": "Criminal Law & Procedure", "id": "353", "type": "non-fiction", "count": "3189"}, {"name": "Criminology, Forensic Science", "id": "355", "type": "non-fiction", "count": "3369"}, {"name": "Criminology: Court. Examination", "id": "356", "type": "non-fiction", "count": "440"}, {"name": "Environmental Law", "id": "357", "type": "non-fiction", "count": "650"}, {"name": "Family Law", "id": "358", "type": "non-fiction", "count": "674"}, {"name": "Foreign & International Law", "id": "359", "type": "non-fiction", "count": "1800"}, {"name": "General & Miscellaneous Law", "id": "360", "type": "non-fiction", "count": "2750"}, {"name": "Intellectual Property", "id": "776", "type": "non-fiction", "count": "918"}, {"name": "Labor Law", "id": "361", "type": "non-fiction", "count": "525"}, {"name": "Legal Reference", "id": "362", "type": "non-fiction", "count": "765"}, {"name": "Legal Theory & Philosophy", "id": "363", "type": "non-fiction", "count": "2593"}, {"name": "Medical Law", "id": "777", "type": "non-fiction", "count": "1573"}, {"name": "Real Estate & Property Law", "id": "364", "type": "non-fiction", "count": "307"}, {"name": "Religious Law", "id": "365", "type": "non-fiction", "count": "234"}, {"name": "Tort & Liability Law", "id": "366", "type": "non-fiction", "count": "176"}]}, {"category": "Languages", "id": "21", "subcategories": [{"name": "Arabic Language Reference", "id": "367", "type": "non-fiction", "count": "693"}, {"name": "Chinese Language Reference", "id": "368", "type": "non-fiction", "count": "3629"}, {"name": "Comparative Studies", "id": "369", "type": "non-fiction", "count": "999"}, {"name": "Dictionaries", "id": "370", "type": "non-fiction", "count": "7040"}, {"name": "English as a Foreign Language & Reference", "id": "377", "type": "non-fiction", "count": "29380"}, {"name": "French Language Reference", "id": "372", "type": "non-fiction", "count": "4827"}, {"name": "General & Miscellaneous Languages - Reference", "id": "373", "type": "non-fiction", "count": "14558"}, {"name": "German Language Reference", "id": "378", "type": "non-fiction", "count": "2592"}, {"name": "Germanic Languages Reference", "id": "385", "type": "non-fiction", "count": "574"}, {"name": "Grammar, dictionaries & phrasebooks", "id": "374", "type": "non-fiction", "count": "17279"}, {"name": "Hindi Language Reference", "id": "799", "type": "non-fiction", "count": "69"}, {"name": "Indic Languages Reference", "id": "379", "type": "non-fiction", "count": "185"}, {"name": "Indonesian Language Reference", "id": "796", "type": "non-fiction", "count": "111"}, {"name": "Italian Language Reference", "id": "380", "type": "non-fiction", "count": "883"}, {"name": "Japanese Language Reference", "id": "381", "type": "non-fiction", "count": "2991"}, {"name": "Korean Language Reference", "id": "795", "type": "non-fiction", "count": "485"}, {"name": "Latin Language Reference", "id": "382", "type": "non-fiction", "count": "1147"}, {"name": "Polyglot Dictionaries", "id": "375", "type": "non-fiction", "count": "135"}, {"name": "Portuguese Language Reference", "id": "798", "type": "non-fiction", "count": "333"}, {"name": "Rhetoric", "id": "376", "type": "non-fiction", "count": "855"}, {"name": "Romance Languages Reference", "id": "801", "type": "non-fiction", "count": "173"}, {"name": "Russian Language Reference", "id": "384", "type": "non-fiction", "count": "5959"}, {"name": "Slavic Languages Reference", "id": "386", "type": "non-fiction", "count": "521"}, {"name": "Spanish Language Reference", "id": "383", "type": "non-fiction", "count": "1417"}, {"name": "Traditional Chinese Language Ref.", "id": "800", "type": "non-fiction", "count": "130"}, {"name": "Turkish Language Reference", "id": "797", "type": "non-fiction", "count": "146"}]}, {"category": "Linguistics", "id": "22", "subcategories": [{"name": "Historical & Comparative Linguistics", "id": "389", "type": "non-fiction", "count": "3054"}, {"name": "Linguistics", "id": "387", "type": "non-fiction", "count": "12974"}, {"name": "Semiotics", "id": "390", "type": "non-fiction", "count": "1470"}, {"name": "Sociolinguistics", "id": "388", "type": "non-fiction", "count": "1125"}, {"name": "Stylistics", "id": "391", "type": "non-fiction", "count": "854"}]}, {"category": "Mathematics", "id": "23", "subcategories": [{"name": "Algebra", "id": "392", "type": "non-fiction", "count": "12515"}, {"name": "Analysis", "id": "393", "type": "non-fiction", "count": "9806"}, {"name": "Applied Mathematics", "id": "394", "type": "non-fiction", "count": "5227"}, {"name": "Automatic Control Theory", "id": "395", "type": "non-fiction", "count": "1418"}, {"name": "Combinatorics", "id": "396", "type": "non-fiction", "count": "1091"}, {"name": "Computational Mathematics", "id": "397", "type": "non-fiction", "count": "3492"}, {"name": "Computer Algebra", "id": "398", "type": "non-fiction", "count": "713"}, {"name": "Continued fractions", "id": "399", "type": "non-fiction", "count": "70"}, {"name": "Differential Equations", "id": "400", "type": "non-fiction", "count": "3512"}, {"name": "Discrete Mathematics", "id": "401", "type": "non-fiction", "count": "1200"}, {"name": "Dynamical Systems", "id": "402", "type": "non-fiction", "count": "955"}, {"name": "Elementary", "id": "403", "type": "non-fiction", "count": "4814"}, {"name": "Functional Analysis", "id": "404", "type": "non-fiction", "count": "1715"}, {"name": "Fuzzy Logic and Applications", "id": "405", "type": "non-fiction", "count": "274"}, {"name": "Game Theory", "id": "406", "type": "non-fiction", "count": "608"}, {"name": "Geometry and Topology", "id": "407", "type": "non-fiction", "count": "10541"}, {"name": "Graph Theory", "id": "408", "type": "non-fiction", "count": "1053"}, {"name": "Lectures", "id": "409", "type": "non-fiction", "count": "461"}, {"name": "Logic", "id": "410", "type": "non-fiction", "count": "3100"}, {"name": "Mathematical Foundations", "id": "730", "type": "non-fiction", "count": "1872"}, {"name": "Mathematical Physics", "id": "411", "type": "non-fiction", "count": "2798"}, {"name": "Mathematical Statistics", "id": "412", "type": "non-fiction", "count": "4413"}, {"name": "Mathematical Theory", "id": "731", "type": "non-fiction", "count": "1397"}, {"name": "Number Theory", "id": "413", "type": "non-fiction", "count": "2948"}, {"name": "Numerical Analysis", "id": "414", "type": "non-fiction", "count": "1306"}, {"name": "Operator Theory", "id": "415", "type": "non-fiction", "count": "305"}, {"name": "Optimal control", "id": "416", "type": "non-fiction", "count": "183"}, {"name": "Optimization. Operations Research", "id": "417", "type": "non-fiction", "count": "1692"}, {"name": "Others", "id": "732", "type": "non-fiction", "count": "3646"}, {"name": "Probability", "id": "418", "type": "non-fiction", "count": "3903"}, {"name": "Puzzle", "id": "419", "type": "non-fiction", "count": "945"}, {"name": "Symmetry and group", "id": "420", "type": "non-fiction", "count": "1332"}, {"name": "The complex variable", "id": "421", "type": "non-fiction", "count": "1163"}, {"name": "Wavelets and signal processing", "id": "422", "type": "non-fiction", "count": "1192"}]}, {"category": "Medicine", "id": "24", "subcategories": [{"name": "Anatomy and physiology", "id": "423", "type": "non-fiction", "count": "3714"}, {"name": "Anesthesiology and Intensive Care", "id": "424", "type": "non-fiction", "count": "1305"}, {"name": "Cardiology", "id": "425", "type": "non-fiction", "count": "1851"}, {"name": "Chinese Medicine", "id": "426", "type": "non-fiction", "count": "8424"}, {"name": "Clinical Medicine", "id": "427", "type": "non-fiction", "count": "4242"}, {"name": "Dentistry, Orthodontics", "id": "428", "type": "non-fiction", "count": "2181"}, {"name": "Dermatology", "id": "429", "type": "non-fiction", "count": "816"}, {"name": "Diabetes", "id": "430", "type": "non-fiction", "count": "486"}, {"name": "Diseases", "id": "431", "type": "non-fiction", "count": "1536"}, {"name": "Endocrinology", "id": "433", "type": "non-fiction", "count": "712"}, {"name": "ENT", "id": "434", "type": "non-fiction", "count": "291"}, {"name": "Epidemiology", "id": "435", "type": "non-fiction", "count": "754"}, {"name": "Feng <PERSON>", "id": "436", "type": "non-fiction", "count": "306"}, {"name": "Health-Related Professions", "id": "437", "type": "non-fiction", "count": "1551"}, {"name": "Histology", "id": "438", "type": "non-fiction", "count": "541"}, {"name": "Homeopathy", "id": "439", "type": "non-fiction", "count": "845"}, {"name": "Immunology", "id": "440", "type": "non-fiction", "count": "1651"}, {"name": "Infectious diseases", "id": "441", "type": "non-fiction", "count": "1389"}, {"name": "Internal Medicine", "id": "432", "type": "non-fiction", "count": "2327"}, {"name": "Medicine & Nursing Reference", "id": "442", "type": "non-fiction", "count": "1225"}, {"name": "Medicine & Nursing Test Prep", "id": "443", "type": "non-fiction", "count": "700"}, {"name": "Molecular Medicine", "id": "444", "type": "non-fiction", "count": "438"}, {"name": "Natural Medicine", "id": "445", "type": "non-fiction", "count": "7382"}, {"name": "Neurology", "id": "446", "type": "non-fiction", "count": "2527"}, {"name": "Neuroscience", "id": "742", "type": "non-fiction", "count": "2059"}, {"name": "Nursing", "id": "447", "type": "non-fiction", "count": "828"}, {"name": "Oncology", "id": "448", "type": "non-fiction", "count": "1678"}, {"name": "Ophthalmology", "id": "449", "type": "non-fiction", "count": "792"}, {"name": "Others", "id": "729", "type": "non-fiction", "count": "4907"}, {"name": "Pediatrics", "id": "450", "type": "non-fiction", "count": "1403"}, {"name": "Pharmacology", "id": "451", "type": "non-fiction", "count": "4354"}, {"name": "Popular scientific literature", "id": "452", "type": "non-fiction", "count": "2363"}, {"name": "Psychiatry", "id": "814", "type": "non-fiction", "count": "1668"}, {"name": "Radiology", "id": "813", "type": "non-fiction", "count": "909"}, {"name": "Surgery, Orthopedics", "id": "453", "type": "non-fiction", "count": "3412"}, {"name": "Therapy, Psychotherapy", "id": "454", "type": "non-fiction", "count": "2339"}, {"name": "Trial", "id": "455", "type": "non-fiction", "count": "483"}, {"name": "Veterinary Medicine", "id": "456", "type": "non-fiction", "count": "2035"}, {"name": "Yoga, Ayurveda", "id": "457", "type": "non-fiction", "count": "2344"}]}, {"category": "Nature, Animals & Pets", "id": "25", "subcategories": [{"name": "Animal Care & Pets", "id": "309", "type": "non-fiction", "count": "2783"}, {"name": "Animals", "id": "458", "type": "non-fiction", "count": "1185"}, {"name": "Birds", "id": "459", "type": "non-fiction", "count": "1364"}, {"name": "Cats", "id": "460", "type": "non-fiction", "count": "505"}, {"name": "Dinosaurs", "id": "461", "type": "non-fiction", "count": "232"}, {"name": "Dogs", "id": "462", "type": "non-fiction", "count": "1722"}, {"name": "Exotic & Other Pets", "id": "463", "type": "non-fiction", "count": "28"}, {"name": "Field Guides", "id": "464", "type": "non-fiction", "count": "349"}, {"name": "Fossils", "id": "465", "type": "non-fiction", "count": "28"}, {"name": "Home Aquarium", "id": "466", "type": "non-fiction", "count": "66"}, {"name": "Horses", "id": "467", "type": "non-fiction", "count": "264"}, {"name": "Insects & Arachnids", "id": "468", "type": "non-fiction", "count": "507"}, {"name": "Mammals", "id": "469", "type": "non-fiction", "count": "167"}, {"name": "Marine & Aquatic Life", "id": "470", "type": "non-fiction", "count": "418"}, {"name": "Natural Disasters", "id": "471", "type": "non-fiction", "count": "134"}, {"name": "Natural History", "id": "472", "type": "non-fiction", "count": "779"}, {"name": "Natural Terrain", "id": "473", "type": "non-fiction", "count": "87"}, {"name": "Nature - Other", "id": "474", "type": "non-fiction", "count": "748"}, {"name": "Pet Birds", "id": "475", "type": "non-fiction", "count": "47"}, {"name": "Pet Birds", "id": "752", "type": "non-fiction", "count": "7"}, {"name": "Pet Fish", "id": "476", "type": "non-fiction", "count": "46"}, {"name": "Pet Memoirs", "id": "477", "type": "non-fiction", "count": "264"}, {"name": "<PERSON>, <PERSON><PERSON>, Hamsters, Etc.", "id": "478", "type": "non-fiction", "count": "27"}, {"name": "Pet Pigs", "id": "479", "type": "non-fiction", "count": "5"}, {"name": "Pet Reptiles & Amphibians", "id": "480", "type": "non-fiction", "count": "28"}, {"name": "Pets - General & Miscellaneous", "id": "481", "type": "non-fiction", "count": "63"}, {"name": "Plants & Fungi", "id": "482", "type": "non-fiction", "count": "729"}, {"name": "Stargazing - Amateur's Manuals", "id": "483", "type": "non-fiction", "count": "12"}, {"name": "Weather", "id": "484", "type": "non-fiction", "count": "73"}]}, {"category": "Others", "id": "26", "subcategories": []}, {"category": "Physics", "id": "27", "subcategories": [{"name": "Astronomy: Astrophysics", "id": "486", "type": "non-fiction", "count": "1545"}, {"name": "Atomic & Molecular", "id": "733", "type": "non-fiction", "count": "373"}, {"name": "Chaos & Dynamic Systems", "id": "734", "type": "non-fiction", "count": "85"}, {"name": "Crystal Physics", "id": "487", "type": "non-fiction", "count": "256"}, {"name": "Electricity and Magnetism", "id": "488", "type": "non-fiction", "count": "1781"}, {"name": "Electrodynamics", "id": "489", "type": "non-fiction", "count": "1051"}, {"name": "General Courses", "id": "490", "type": "non-fiction", "count": "3620"}, {"name": "Light, Optics & Laser", "id": "500", "type": "non-fiction", "count": "507"}, {"name": "Mechanics", "id": "492", "type": "non-fiction", "count": "3984"}, {"name": "Mechanics: Fluid Mechanics", "id": "493", "type": "non-fiction", "count": "1583"}, {"name": "Mechanics: Mechanics of deformable bodies", "id": "494", "type": "non-fiction", "count": "570"}, {"name": "Mechanics: Nonlinear dynamics and chaos", "id": "495", "type": "non-fiction", "count": "539"}, {"name": "Mechanics: Oscillations and Waves", "id": "496", "type": "non-fiction", "count": "839"}, {"name": "Mechanics: Strength of Materials", "id": "497", "type": "non-fiction", "count": "720"}, {"name": "Mechanics: Theory of Elasticity", "id": "498", "type": "non-fiction", "count": "275"}, {"name": "Nuclear", "id": "736", "type": "non-fiction", "count": "558"}, {"name": "Optics", "id": "499", "type": "non-fiction", "count": "1336"}, {"name": "Others", "id": "735", "type": "non-fiction", "count": "1433"}, {"name": "Physics of the Atmosphere", "id": "501", "type": "non-fiction", "count": "203"}, {"name": "Plasma Physics", "id": "502", "type": "non-fiction", "count": "988"}, {"name": "Quantum Mechanics", "id": "503", "type": "non-fiction", "count": "1698"}, {"name": "Quantum Physics", "id": "504", "type": "non-fiction", "count": "4115"}, {"name": "Relativity", "id": "737", "type": "non-fiction", "count": "492"}, {"name": "Solid State Physics", "id": "505", "type": "non-fiction", "count": "1476"}, {"name": "Spectroscopy", "id": "506", "type": "non-fiction", "count": "153"}, {"name": "States of Matter", "id": "738", "type": "non-fiction", "count": "99"}, {"name": "Theoretical Physics", "id": "739", "type": "non-fiction", "count": "1840"}, {"name": "Theory of Relativity and Gravitation", "id": "507", "type": "non-fiction", "count": "1366"}, {"name": "Thermodynamics", "id": "821", "type": "non-fiction", "count": "517"}]}, {"category": "Poetry", "id": "28", "subcategories": [{"name": "American Poetry", "id": "508", "type": "fiction", "count": "43887"}, {"name": "Ancient & Classical Poetry", "id": "509", "type": "fiction", "count": "4459"}, {"name": "Arabic & Middle Eastern Poetry", "id": "510", "type": "fiction", "count": "559"}, {"name": "Asian Poetry", "id": "511", "type": "fiction", "count": "3616"}, {"name": "Australian, New Zealand, & Pacific Island Poetry", "id": "512", "type": "fiction", "count": "94"}, {"name": "Canadian Poetry", "id": "513", "type": "fiction", "count": "206"}, {"name": "English, Irish, & Scottish Poetry", "id": "514", "type": "fiction", "count": "1983"}, {"name": "European Poetry", "id": "515", "type": "fiction", "count": "5492"}, {"name": "General & Miscellaneous Poetry", "id": "516", "type": "fiction", "count": "1760"}, {"name": "Inspirational & Religious Poetry", "id": "517", "type": "fiction", "count": "380"}, {"name": "Latin American & Caribbean Poetry", "id": "518", "type": "fiction", "count": "1440"}, {"name": "Poetry Anthologies", "id": "519", "type": "fiction", "count": "1072"}, {"name": "Russian Poetry", "id": "520", "type": "fiction", "count": "1888"}]}, {"category": "Psychology", "id": "29", "subcategories": [{"name": "Clinical Psychology", "id": "521", "type": "non-fiction", "count": "3767"}, {"name": "Cognitive Psychology", "id": "778", "type": "non-fiction", "count": "7331"}, {"name": "Developmental Psychology", "id": "779", "type": "non-fiction", "count": "4744"}, {"name": "Neuropsychology", "id": "780", "type": "non-fiction", "count": "2303"}, {"name": "Pedagogy", "id": "526", "type": "non-fiction", "count": "24222"}, {"name": "Psychological Disorders", "id": "527", "type": "non-fiction", "count": "2069"}, {"name": "Psychology - Theory, History & Research", "id": "528", "type": "non-fiction", "count": "7969"}, {"name": "Psychopathy", "id": "781", "type": "non-fiction", "count": "1005"}, {"name": "Psychotherapy", "id": "782", "type": "non-fiction", "count": "6744"}, {"name": "Social Psychology", "id": "816", "type": "non-fiction", "count": "2332"}]}, {"category": "Reference", "id": "30", "subcategories": [{"name": "Almanacs & Yearbooks", "id": "530", "type": "non-fiction", "count": "366"}, {"name": "Atlases & Maps", "id": "531", "type": "non-fiction", "count": "454"}, {"name": "Consumer Guides", "id": "532", "type": "non-fiction", "count": "388"}, {"name": "Encyclopaedias", "id": "534", "type": "non-fiction", "count": "9203"}, {"name": "Genealogy & Family History", "id": "535", "type": "non-fiction", "count": "837"}, {"name": "Library & Information Science", "id": "536", "type": "non-fiction", "count": "2294"}, {"name": "Other Reference By Subject", "id": "537", "type": "non-fiction", "count": "3021"}, {"name": "School Guides & Test Preparation", "id": "538", "type": "non-fiction", "count": "626"}, {"name": "<PERSON><PERSON><PERSON>", "id": "533", "type": "non-fiction", "count": "1644"}, {"name": "Writing", "id": "539", "type": "non-fiction", "count": "21135"}]}, {"category": "Religion & Spirituality", "id": "31", "subcategories": [{"name": "Atheism & Agnosticism", "id": "815", "type": "non-fiction", "count": "253"}, {"name": "Bible", "id": "540", "type": "non-fiction", "count": "27013"}, {"name": "Buddhism", "id": "541", "type": "non-fiction", "count": "22989"}, {"name": "Christianity", "id": "542", "type": "non-fiction", "count": "53090"}, {"name": "Esoteric, Occult, & Divination", "id": "543", "type": "non-fiction", "count": "21094"}, {"name": "Hinduism", "id": "544", "type": "non-fiction", "count": "3667"}, {"name": "Islam", "id": "545", "type": "non-fiction", "count": "10884"}, {"name": "Judaism", "id": "546", "type": "non-fiction", "count": "3000"}, {"name": "Kabbalah", "id": "547", "type": "non-fiction", "count": "434"}, {"name": "Other Religions", "id": "549", "type": "non-fiction", "count": "6836"}, {"name": "Religious Conflict", "id": "550", "type": "non-fiction", "count": "1307"}, {"name": "Religious Studies", "id": "551", "type": "non-fiction", "count": "14159"}, {"name": "Sacred Texts", "id": "552", "type": "non-fiction", "count": "890"}, {"name": "Spirituality", "id": "553", "type": "non-fiction", "count": "16279"}, {"name": "Taoism & Confucianism", "id": "817", "type": "non-fiction", "count": "1135"}, {"name": "Theosophy & New Age", "id": "753", "type": "non-fiction", "count": "6338"}]}, {"category": "Romance", "id": "32", "subcategories": [{"name": "Collections & Anthologies", "id": "554", "type": "fiction", "count": "7828"}, {"name": "Contemporary Romance", "id": "555", "type": "fiction", "count": "245815"}, {"name": "Dark Romance", "id": "802", "type": "fiction", "count": "14199"}, {"name": "Erotic Romance", "id": "805", "type": "fiction", "count": "11827"}, {"name": "Fantasy", "id": "557", "type": "fiction", "count": "27784"}, {"name": "Gothic Romance", "id": "558", "type": "fiction", "count": "3223"}, {"name": "Historical Romance", "id": "559", "type": "fiction", "count": "115220"}, {"name": "Interracial", "id": "560", "type": "fiction", "count": "1810"}, {"name": "LGBTQ+ Romance", "id": "803", "type": "fiction", "count": "33022"}, {"name": "Military Romance", "id": "561", "type": "fiction", "count": "6471"}, {"name": "Other Romance Categories", "id": "562", "type": "fiction", "count": "42048"}, {"name": "Paranormal Romance", "id": "563", "type": "fiction", "count": "81427"}, {"name": "Religious Romance", "id": "564", "type": "fiction", "count": "2018"}, {"name": "Romantic Comedy", "id": "565", "type": "fiction", "count": "15719"}, {"name": "Romantic Westerns", "id": "566", "type": "fiction", "count": "9567"}, {"name": "Science Fiction Romance", "id": "567", "type": "fiction", "count": "14979"}, {"name": "Time Travel Romance", "id": "568", "type": "fiction", "count": "3957"}, {"name": "Young Adult Romance", "id": "804", "type": "fiction", "count": "3577"}]}, {"category": "Science (General)", "id": "33", "subcategories": [{"name": "International Conferences and Symposiums", "id": "569", "type": "non-fiction", "count": "2624"}, {"name": "Research & Development", "id": "740", "type": "non-fiction", "count": "1936"}, {"name": "Science of Science", "id": "570", "type": "non-fiction", "count": "1996"}, {"name": "Scientific and popular: Journalism", "id": "572", "type": "non-fiction", "count": "4579"}, {"name": "Scientific-popular", "id": "571", "type": "non-fiction", "count": "9618"}, {"name": "Theories of Science", "id": "767", "type": "non-fiction", "count": "2061"}]}, {"category": "Science Fiction", "id": "34", "subcategories": [{"name": "Alternate Realities", "id": "573", "type": "fiction", "count": "24397"}, {"name": "Cyberpunk", "id": "828", "type": "fiction", "count": "912"}, {"name": "Dystopian Fiction", "id": "825", "type": "fiction", "count": "8465"}, {"name": "Fantasy Fiction", "id": "574", "type": "fiction", "count": "178022"}, {"name": "High Tech & Hard Sci-Fi", "id": "575", "type": "fiction", "count": "21454"}, {"name": "Military Science Fiction", "id": "823", "type": "fiction", "count": "4775"}, {"name": "Other Sci-Fi", "id": "577", "type": "fiction", "count": "64259"}, {"name": "Space Opera", "id": "826", "type": "fiction", "count": "19625"}, {"name": "Space Western", "id": "827", "type": "fiction", "count": "349"}, {"name": "Steampunk", "id": "829", "type": "fiction", "count": "520"}, {"name": "Supernatural Fiction", "id": "822", "type": "fiction", "count": "2274"}, {"name": "Utopian Fiction", "id": "824", "type": "fiction", "count": "1631"}]}, {"category": "Self-Help, Relationships & Lifestyle", "id": "35", "subcategories": [{"name": "Addiction & Recovery", "id": "582", "type": "non-fiction", "count": "1410"}, {"name": "Aging", "id": "583", "type": "non-fiction", "count": "935"}, {"name": "Alternative Medicine & Natural Healing", "id": "584", "type": "non-fiction", "count": "10629"}, {"name": "Beauty & Fashion", "id": "585", "type": "non-fiction", "count": "1701"}, {"name": "Dating", "id": "587", "type": "non-fiction", "count": "906"}, {"name": "Diet & Nutrition", "id": "588", "type": "non-fiction", "count": "26262"}, {"name": "Digital Lifestyle", "id": "178", "type": "non-fiction", "count": "744"}, {"name": "Divorce", "id": "589", "type": "non-fiction", "count": "107"}, {"name": "Emotional Healing", "id": "590", "type": "non-fiction", "count": "3423"}, {"name": "Exercise & Fitness", "id": "591", "type": "non-fiction", "count": "6249"}, {"name": "Families & Parents", "id": "592", "type": "non-fiction", "count": "6567"}, {"name": "Health - Diseases & Disorders", "id": "593", "type": "non-fiction", "count": "8129"}, {"name": "Love & Romance", "id": "595", "type": "non-fiction", "count": "3538"}, {"name": "Marriage", "id": "596", "type": "non-fiction", "count": "568"}, {"name": "Men's Health & Lifestyle", "id": "598", "type": "non-fiction", "count": "1065"}, {"name": "Others", "id": "719", "type": "non-fiction", "count": "2707"}, {"name": "Personal Growth & Inspiration", "id": "599", "type": "non-fiction", "count": "31908"}, {"name": "Pregnancy & Childcare", "id": "600", "type": "non-fiction", "count": "1022"}, {"name": "Psychological Self-Help", "id": "601", "type": "non-fiction", "count": "28040"}, {"name": "Relationships", "id": "604", "type": "non-fiction", "count": "9022"}, {"name": "Sexuality", "id": "606", "type": "non-fiction", "count": "5241"}, {"name": "The Art of Communication", "id": "529", "type": "non-fiction", "count": "3219"}, {"name": "Weddings", "id": "607", "type": "non-fiction", "count": "146"}, {"name": "Women's Health & Lifestyle", "id": "608", "type": "non-fiction", "count": "2672"}]}, {"category": "Society, Politics & Philosophy", "id": "36", "subcategories": [{"name": "Ancient & Medieval Philosophy", "id": "609", "type": "non-fiction", "count": "6992"}, {"name": "Anthropology", "id": "95", "type": "non-fiction", "count": "66042"}, {"name": "Asian Philosophy", "id": "610", "type": "non-fiction", "count": "5511"}, {"name": "Cultural", "id": "611", "type": "non-fiction", "count": "15851"}, {"name": "Ethnography", "id": "612", "type": "non-fiction", "count": "4602"}, {"name": "European & American Philosophy", "id": "613", "type": "non-fiction", "count": "11070"}, {"name": "General & Miscellaneous Philosophy", "id": "614", "type": "non-fiction", "count": "49086"}, {"name": "Government & Politics", "id": "615", "type": "non-fiction", "count": "71392"}, {"name": "International Relations", "id": "620", "type": "non-fiction", "count": "10678"}, {"name": "Journalism, Media", "id": "616", "type": "non-fiction", "count": "6252"}, {"name": "Major Branches of Philosophical Study", "id": "617", "type": "non-fiction", "count": "3512"}, {"name": "Others", "id": "720", "type": "non-fiction", "count": "4789"}, {"name": "Philosophical Positions & Movements", "id": "764", "type": "non-fiction", "count": "7616"}, {"name": "Politics", "id": "619", "type": "non-fiction", "count": "33110"}, {"name": "Renaissance & Modern Philosophy", "id": "621", "type": "non-fiction", "count": "3854"}, {"name": "Social Sciences", "id": "622", "type": "non-fiction", "count": "89511"}, {"name": "Sociology", "id": "623", "type": "non-fiction", "count": "31841"}, {"name": "Warfare & Defence", "id": "624", "type": "non-fiction", "count": "4607"}, {"name": "Women's Studies", "id": "625", "type": "non-fiction", "count": "6813"}]}, {"category": "Sports, Hobbies & Games", "id": "37", "subcategories": [{"name": "Air Sports", "id": "626", "type": "non-fiction", "count": "113"}, {"name": "Athletics & Gymnastics", "id": "627", "type": "non-fiction", "count": "638"}, {"name": "Ball Games", "id": "628", "type": "non-fiction", "count": "1297"}, {"name": "Bike", "id": "632", "type": "non-fiction", "count": "428"}, {"name": "Boating & Sailing", "id": "629", "type": "non-fiction", "count": "362"}, {"name": "Bodybuilding", "id": "630", "type": "non-fiction", "count": "1911"}, {"name": "Climbing & Mountaineering", "id": "631", "type": "non-fiction", "count": "932"}, {"name": "Fencing", "id": "633", "type": "non-fiction", "count": "84"}, {"name": "Football & Rugby", "id": "634", "type": "non-fiction", "count": "1082"}, {"name": "Golf", "id": "635", "type": "non-fiction", "count": "142"}, {"name": "Hockey", "id": "636", "type": "non-fiction", "count": "88"}, {"name": "Hunting & Fishing", "id": "637", "type": "non-fiction", "count": "1032"}, {"name": "Martial Arts", "id": "638", "type": "non-fiction", "count": "5348"}, {"name": "Motor Sports", "id": "639", "type": "non-fiction", "count": "201"}, {"name": "Olympics & Olympic Sports", "id": "640", "type": "non-fiction", "count": "226"}, {"name": "Other Sports", "id": "641", "type": "non-fiction", "count": "2040"}, {"name": "Outdoor & Adventure Sports", "id": "642", "type": "non-fiction", "count": "841"}, {"name": "Survival", "id": "643", "type": "non-fiction", "count": "1804"}, {"name": "Training & Coaching", "id": "644", "type": "non-fiction", "count": "1444"}, {"name": "Water Sports", "id": "645", "type": "non-fiction", "count": "607"}, {"name": "Winter Sports", "id": "646", "type": "non-fiction", "count": "194"}]}, {"category": "Technique", "id": "38", "subcategories": [{"name": "Aerospace Equipment", "id": "647", "type": "non-fiction", "count": "908"}, {"name": "Automation", "id": "648", "type": "non-fiction", "count": "2639"}, {"name": "Communication", "id": "649", "type": "non-fiction", "count": "1457"}, {"name": "Construction", "id": "650", "type": "non-fiction", "count": "9305"}, {"name": "Construction: Cement Industry", "id": "651", "type": "non-fiction", "count": "814"}, {"name": "Construction: Renovation and interior design", "id": "652", "type": "non-fiction", "count": "816"}, {"name": "Construction: Ventilation and Air Conditioning", "id": "653", "type": "non-fiction", "count": "760"}, {"name": "Electronics", "id": "654", "type": "non-fiction", "count": "10525"}, {"name": "Electronics: Fiber Optics", "id": "655", "type": "non-fiction", "count": "372"}, {"name": "Electronics: Hardware", "id": "656", "type": "non-fiction", "count": "1143"}, {"name": "Electronics: Home Electronics", "id": "657", "type": "non-fiction", "count": "617"}, {"name": "Electronics: Microprocessor Technology", "id": "658", "type": "non-fiction", "count": "1217"}, {"name": "Electronics: Radio", "id": "659", "type": "non-fiction", "count": "4703"}, {"name": "Electronics: Robotics", "id": "660", "type": "non-fiction", "count": "592"}, {"name": "Electronics: Signal Processing", "id": "661", "type": "non-fiction", "count": "470"}, {"name": "Electronics: Telecommunications", "id": "662", "type": "non-fiction", "count": "1181"}, {"name": "Electronics: TV. Video", "id": "663", "type": "non-fiction", "count": "185"}, {"name": "Electronics: VLSI", "id": "664", "type": "non-fiction", "count": "211"}, {"name": "Energy", "id": "665", "type": "non-fiction", "count": "12342"}, {"name": "Energy: Renewable Energy", "id": "666", "type": "non-fiction", "count": "909"}, {"name": "Experiments, Instruments & Measurements", "id": "671", "type": "non-fiction", "count": "4508"}, {"name": "Food Manufacturing", "id": "667", "type": "non-fiction", "count": "4275"}, {"name": "Fuel Technology", "id": "668", "type": "non-fiction", "count": "450"}, {"name": "Heat", "id": "669", "type": "non-fiction", "count": "640"}, {"name": "Industrial Equipment and Technology", "id": "670", "type": "non-fiction", "count": "2212"}, {"name": "Light Industry", "id": "672", "type": "non-fiction", "count": "1411"}, {"name": "Marine & Nautical", "id": "726", "type": "non-fiction", "count": "322"}, {"name": "Materials", "id": "673", "type": "non-fiction", "count": "682"}, {"name": "Metallurgy", "id": "674", "type": "non-fiction", "count": "5367"}, {"name": "Metrology", "id": "675", "type": "non-fiction", "count": "628"}, {"name": "Military equipment", "id": "676", "type": "non-fiction", "count": "8018"}, {"name": "Military equipment: Missiles", "id": "678", "type": "non-fiction", "count": "276"}, {"name": "Military equipment: Weapon", "id": "677", "type": "non-fiction", "count": "4664"}, {"name": "Nanotechnology", "id": "679", "type": "non-fiction", "count": "1763"}, {"name": "Oil and Gas Technologies", "id": "680", "type": "non-fiction", "count": "2498"}, {"name": "Others", "id": "727", "type": "non-fiction", "count": "998"}, {"name": "Patent Business. Ingenuity. Innovation", "id": "681", "type": "non-fiction", "count": "517"}, {"name": "Publishing", "id": "682", "type": "non-fiction", "count": "1001"}, {"name": "Refrigeration", "id": "683", "type": "non-fiction", "count": "429"}, {"name": "Regulatory Literature", "id": "684", "type": "non-fiction", "count": "2077"}, {"name": "Safety and Security", "id": "685", "type": "non-fiction", "count": "4970"}, {"name": "Transport", "id": "686", "type": "non-fiction", "count": "4663"}, {"name": "Transportation: Aviation", "id": "687", "type": "non-fiction", "count": "4756"}, {"name": "Transportation: Cars, Motorcycles", "id": "688", "type": "non-fiction", "count": "4914"}, {"name": "Transportation: Rail", "id": "689", "type": "non-fiction", "count": "2134"}, {"name": "Transportation: Ships", "id": "690", "type": "non-fiction", "count": "2301"}, {"name": "Water Treatment", "id": "691", "type": "non-fiction", "count": "615"}]}, {"category": "Travel", "id": "39", "subcategories": [{"name": "Africa - Travel", "id": "692", "type": "non-fiction", "count": "1218"}, {"name": "Asia - Travel", "id": "693", "type": "non-fiction", "count": "9748"}, {"name": "Atlases & Maps", "id": "694", "type": "non-fiction", "count": "252"}, {"name": "Australia & Oceania - Travel", "id": "695", "type": "non-fiction", "count": "630"}, {"name": "Canadian & North American Travel", "id": "696", "type": "non-fiction", "count": "968"}, {"name": "Europe - Travel", "id": "697", "type": "non-fiction", "count": "8269"}, {"name": "Food, Lodging & Transportation", "id": "698", "type": "non-fiction", "count": "467"}, {"name": "Latin American & Caribbean Travel", "id": "699", "type": "non-fiction", "count": "1618"}, {"name": "Middle East - Travel", "id": "700", "type": "non-fiction", "count": "581"}, {"name": "Pictorial", "id": "701", "type": "non-fiction", "count": "66"}, {"name": "Reference", "id": "702", "type": "non-fiction", "count": "408"}, {"name": "Specialty Travel", "id": "703", "type": "non-fiction", "count": "1047"}, {"name": "Travel - Cities of the World", "id": "704", "type": "non-fiction", "count": "2312"}, {"name": "Travel - General & Miscellaneous", "id": "705", "type": "non-fiction", "count": "810"}, {"name": "Travel - Interests, Activities & Accommodations", "id": "706", "type": "non-fiction", "count": "250"}, {"name": "Travel - Natural & Man-made Wonders of the World", "id": "707", "type": "non-fiction", "count": "166"}, {"name": "Travel Essays & Descriptions", "id": "708", "type": "non-fiction", "count": "1586"}, {"name": "Travel Photography", "id": "709", "type": "non-fiction", "count": "71"}, {"name": "Travel Writing", "id": "710", "type": "non-fiction", "count": "2782"}, {"name": "United States of America - Travel", "id": "711", "type": "non-fiction", "count": "3626"}]}]