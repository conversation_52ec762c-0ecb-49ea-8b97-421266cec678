<div class="mt-4 mb-8">
  <div class="[html.aa-logged-in_&]:hidden">Please <a href="/login">log in</a> to add this book to a list.</div>

  <div class="[html:not(.aa-logged-in)_&]:hidden">
    <h2 class="mb-2 text-xl font-bold">Add to my lists</h2>

    <form class="js-add-to-list" onsubmit='window.submitForm(event, "/dyn/lists_update/" + {{ resource | tojson }})'>
      <fieldset>
        {% for list_dict in my_list_dicts %}
          <div class="flex items-center mb-1"><label class="flex items-center cursor-pointer"><input class="mr-1 cursor-pointer" type="checkbox" name="{{ list_dict.list_id }}" {% if list_dict.selected %}checked{% endif %}> {{ list_dict.name }}</label><a class="ml-2 text-sm" target="_blank" href="/list/{{ list_dict.list_id }}">view</a></div>
        {% endfor %}
        <div class="flex"><input class="mr-1" type="checkbox" disabled checked maxlength="200"><input type="text" name="list_new_name" class="grow bg-black/6.7 px-2 py-1 rounded w-full max-w-[300px] text-sm" placeholder="New list" /></div>
        <p class="mt-4 mb-2 text-sm text-gray-500">
          All lists are public on your profile.
        </p>
        <div class="">
          <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">Save lists</button>
          <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
        </div>
      </fieldset>
      <div class="hidden js-success">✅ Updated lists. It might take a minute to show up.</div>
      <div class="hidden js-failure mb-4">❌ Something went wrong. Please reload the page and try again.</div>
    </form>
  </div>
</div>

<h2 class="mb-2 text-xl font-bold">Lists containing this book</h2>

{% for list_dict in resource_list_dicts %}
  <div class="mb-4">
    <div class="font-bold"><a href="/list/{{ list_dict.list_id }}">{{ list_dict.name }}</a></div>
    <div class="text-sm">by {{ list_dict.display_name }} #{{ list_dict.account_id }}</div>
  </div>
{% else %}
  <div>No lists yet.</div>
{% endfor %}
