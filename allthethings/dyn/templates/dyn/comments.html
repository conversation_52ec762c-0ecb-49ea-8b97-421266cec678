<script>
  (function() {
    const reloadNode = document.currentScript.parentNode;
    const reloadUrl = {{ reload_url | tojson }};

    window.reloadCommentsListFor = window.reloadCommentsListFor || {};
    window.reloadCommentsListFor[reloadUrl] = () => {
      fetch(reloadUrl).then((response) => response.ok ? response.text() : 'Error 12918371').then((text) => {
        reloadNode.innerHTML = text;
        window.executeScriptElements(reloadNode);
      });
    };
  })();
</script>

{% from 'macros/profile_link.html' import profile_link %}

{% macro comment_base(comment_dict) %}
  {% if (comment_dict.abuse_total.count >= 2) or ((comment_dict.thumbs_up.count - comment_dict.thumbs_down.count) <= -2) %}
  <div>
    <a href="#" class="mb-2 text-sm" onclick="event.preventDefault(); this.parentNode.querySelector('.js-comments-comment-inner').classList.toggle('hidden')">{{ gettext('page.comments.hidden_comment') }}</a>
    <div class="mb-4 hidden js-comments-comment-inner">
  {% else %}
  <div>
    <div class="mb-4">
  {% endif %}
      <div>
        {{ profile_link(comment_dict, current_account_id) }}
        <span class="ml-2 text-black/64 text-sm" title="{{ comment_dict.created | datetimeformat(format='long') }}">{{ comment_dict.created_delta | timedeltaformat(add_direction=True) }}</span>
      </div>

      {% if comment_dict.report_dict %}
        {% if md5_report_type_mapping %}<div><span class='text-[18px] align-text-bottom inline-block icon-[uil--exclamation-triangle]'></span> <span class="italic">{{ gettext('page.comments.file_issue', file_issue=md5_report_type_mapping[comment_dict.report_dict.type]) }}</span></div>{% endif %}
        {% if comment_dict.report_dict.better_md5 %}<div><a href="/md5/{{ comment_dict.report_dict.better_md5 }}">{{ gettext('page.comments.better_version') }}</a></div>{% endif %}
      {% endif %}

      <div class="whitespace-pre-line mb-1">{{ comment_dict.content }}</div>

      <div>
        <button {% if (not current_account_id) or (comment_dict.account_id == current_account_id) %}disabled class="text-[#aaa]{% else %}class="hover:text-black{% endif %} mb-[-3px] text-xl text-[#777] align-[-4px] {% if comment_dict.user_reaction == 2 %}icon-[tabler--thumb-up-filled]{% else %}icon-[tabler--thumb-up]{% endif %}" onclick='event.preventDefault(); fetch("/dyn/reactions/{% if comment_dict.user_reaction == 2 %}0{% else %}2{% endif %}/comment:{{ comment_dict.comment_id }}", { method: "PUT" }).then(() => window.reloadCommentsListFor[{{ reload_url | tojson }}]())'></button>
        {% if comment_dict.thumbs_up.count > 0 %}{{ comment_dict.thumbs_up.count }}{% endif %}
        <button {% if (not current_account_id) or (comment_dict.account_id == current_account_id) %}disabled class="text-[#aaa]{% else %}class="hover:text-black{% endif %} ml-2 mb-[-3px] text-xl text-[#777] align-[-4px] {% if comment_dict.user_reaction == 3 %}icon-[tabler--thumb-down-filled]{% else %}icon-[tabler--thumb-down]{% endif %}" onclick='event.preventDefault(); fetch("/dyn/reactions/{% if comment_dict.user_reaction == 3 %}0{% else %}3{% endif %}/comment:{{ comment_dict.comment_id }}", { method: "PUT" }).then(() => window.reloadCommentsListFor[{{ reload_url | tojson }}]())'></button>
        {% if comment_dict.thumbs_down.count > 0 %}{{ comment_dict.thumbs_down.count }}{% endif %}

        <span class="relative">
          <div class="absolute left-0 top-full bg-[#f2f2f2] mt-1 px-3 py-1 shadow w-[70vw] max-w-[400px] hidden js-comments-menu">
            {% if current_account_id and (comment_dict.account_id != current_account_id) and comment_dict.user_reaction != 1 %}
              <a href="#" class="custom-a block text-black/64 hover:text-black" onclick='event.preventDefault(); if (confirm({{ gettext('page.comments.do_you_want_to_report_abuse') | tojson }})) { fetch("/dyn/reactions/1/comment:{{ comment_dict.comment_id }}", { method: "PUT" }).then(() => window.reloadCommentsListFor[{{ reload_url | tojson }}]()); }'>
                {{ gettext('page.comments.report_abuse') }}
              </a>
              {% endif %}
              {% if comment_dict.abuse_total.count %}
                <div class="leading-none">
                  {{ gettext('page.comments.abuse_reported') }}
                  {{ comment_dict.abuse_total.count }}
                  <span class="text-xs ml-2 align-text-bottom">
                    {% for reaction_account in comment_dict.abuse_total.accounts %}
                      {{ profile_link(reaction_account, current_account_id) }}
                    {% endfor %}
                  </span>
                </div>
              {% endif %}
              {% if comment_dict.thumbs_up.count %}
                <div class="leading-none">
                  <span class="text-xl text-[#777] align-[-4px] icon-[tabler--thumb-up]"></span>
                  {{ comment_dict.thumbs_up.count }}
                  <span class="text-xs ml-2 align-text-bottom">
                    {% for reaction_account in comment_dict.thumbs_up.accounts %}
                      {{ profile_link(reaction_account, current_account_id) }}
                    {% endfor %}
                  </span>
                </div>
              {% endif %}
              {% if comment_dict.thumbs_down.count %}
                <div class="leading-none">
                  <span class="text-xl text-[#777] align-[-4px] icon-[tabler--thumb-down]"></span>
                  {{ comment_dict.thumbs_down.count }}
                  <span class="text-xs ml-2 align-text-bottom">
                    {% for reaction_account in comment_dict.thumbs_down.accounts %}
                      {{ profile_link(reaction_account, current_account_id) }}
                    {% endfor %}
                  </span>
                </div>
              {% endif %}
          </div>
          <a href="#" class="ml-1 mb-[-5px] text-xl inline-block icon-[mdi--dots-vertical]" onclick="event.preventDefault(); this.parentNode.querySelector('.js-comments-menu').classList.toggle('hidden')"></a>
        </span>
      </div>

      {% if comment_dict.user_reaction == 1 %}
        <div class="italic text-sm text-[#555]">{{ gettext('page.comments.reported_abuse_this_user') }}</div>
      {% endif %}

      {% if comment_dict.can_have_replies and ((comment_dict.reply_dicts | length) == 0) %}
        <div><button class="ml-2 text-[#777] hover:text-black" onclick='event.preventDefault(); document.querySelector(".js-comments-reply-" + {{ comment_dict.comment_id | tojson }}).classList.toggle("hidden")'>{{ gettext('page.comments.reply_button') }}</button></div>
      {% endif %}

      {% if comment_dict.can_have_replies %}
        <div class="mx-6 sm:mx-12 mt-2">
          {% for reply_dict in comment_dict.reply_dicts %}
            {{ comment_base(reply_dict) }}
          {% endfor %}

          {% if comment_dict.can_have_replies and ((comment_dict.reply_dicts | length) > 0) %}
            <div class="mt-[-8px] mb-4">
              <a href="#" onclick='event.preventDefault(); this.classList.toggle("hidden"); document.querySelector(".js-comments-reply-" + {{ comment_dict.comment_id | tojson }}).classList.toggle("hidden")'>{{ gettext('page.comments.reply_button') }}</a>
            </div>
          {% endif %}

          <div class="hidden js-comments-reply-{{ comment_dict.comment_id }}">
            <div class="[html.aa-logged-in_&]:hidden">{{ gettext('page.md5.quality.logged_out_login', a_login=(' href="/login"' | safe)) }}</div>
            <form class="[html:not(.aa-logged-in)_&]:hidden" onsubmit='window.submitForm(event, "/dyn/comments/comment:" + {{ comment_dict.comment_id | tojson }})'>
              <fieldset>
                <textarea required name="content" class="grow bg-black/6.7 px-2 py-1 mb-1 rounded w-full h-[50px] max-w-[500px]" placeholder=""></textarea>
                <div class="">
                  <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.comments.reply_button') }}</button>
                  <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
                </div>
              </fieldset>
              <div class="hidden js-success">✅ {{ gettext('page.md5.quality.comment_thanks') }}</div>
              <div class="hidden js-failure mb-4">❌ {{ gettext('page.md5.quality.comment_error') }}</div>
            </form>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
{% endmacro %}

{% for comment_dict in comment_dicts %}
  {{ comment_base(comment_dict) }}
{% endfor %}
