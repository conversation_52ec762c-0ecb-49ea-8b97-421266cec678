!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e=e||self).Reakit={},e.<PERSON>,e.<PERSON>actD<PERSON>)}(this,function(e,t,n){"use strict";var r=t.createContext({});function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(n,!0).forEach(function(t){o(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}var s=function(e,n,o){void 0===o&&(o=n.children);var a=t.useContext(r);if(a.useCreateElement)return a.useCreateElement(e,n,o);if(function(e){return"function"==typeof e}(o)){n.children;return o(u(n,["children"]))}return t.createElement(e,n,o)};function l(e,t){for(var n={},r={},o=0,a=Object.keys(e);o<a.length;o++){var i=a[o];t.indexOf(i)>=0?n[i]=e[i]:r[i]=e[i]}return[n,r]}function c(e){var n,r=e.as,o=e.useHook,a=e.keys,c=void 0===a?o&&o.__keys||[]:a,f=e.propsAreEqual,d=void 0===f?o&&o.__propsAreEqual:f,p=e.useCreateElement,v=void 0===p?s:p,m=function(e,t){var n=e.as,a=void 0===n?r:n,s=u(e,["as"]);if(o){var f=l(s,c),d=f[0],p=f[1],m=o(d,i({ref:t},p)),b=m.unstable_wrap,h=u(m,["unstable_wrap"]),g=a.render?a.render.__keys:a.__keys,y=g?l(s,g)[0]:{},k=v(a,i({},h,{},y));return b?b(k):k}return v(a,s)};return m.__keys=c,function(e,n){return t.memo(e,n)}((n=m,t.forwardRef(n)),d)}function f(e,n){t.useDebugValue(e);var o=t.useContext(r);return null!=o[e]?o[e]:n}function d(e){return"object"==typeof e&&null!=e}function p(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}function v(e){var n=p(e.compose),r=function(n,r){return e.useOptions&&(n=e.useOptions(n,r)),e.name&&(n=function(e,n,r){void 0===n&&(n={}),void 0===r&&(r={});var o="use"+e+"Options";t.useDebugValue(o);var a=f(o);return a?i({},n,{},a(n,r)):n}(e.name,n,r)),n},o=function(o,a,i){return void 0===o&&(o={}),void 0===a&&(a={}),void 0===i&&(i=!1),i||(o=r(o,a)),e.compose&&n.forEach(function(e){o=e.__useOptions(o,a)}),e.useProps&&(a=e.useProps(o,a)),e.name&&(a=function(e,n,r){void 0===n&&(n={}),void 0===r&&(r={});var o="use"+e+"Props";t.useDebugValue(o);var a=f(o);return a?a(n,r):r}(e.name,o,a)),e.compose&&(e.useComposeOptions&&(o=e.useComposeOptions(o,a)),n.forEach(function(e){a=e(o,a,!0)})),a};return o.__useOptions=r,o.__keys=[].concat(n.reduce(function(e,t){return e.push.apply(e,t.__keys||[]),e},[]),e.useState?e.useState.__keys:[],e.keys||[]),Boolean(e.propsAreEqual||n.find(function(e){return Boolean(e.__propsAreEqual)}))&&(o.__propsAreEqual=function(t,r){var o=e.propsAreEqual&&e.propsAreEqual(t,r);if(null!=o)return o;var a=n,i=Array.isArray(a),u=0;for(a=i?a:a[Symbol.iterator]();;){var s;if(i){if(u>=a.length)break;s=a[u++]}else{if((u=a.next()).done)break;s=u.value}var l=s.__propsAreEqual,c=l&&l(t,r);if(null!=c)return c}return function e(t,n,r){if(void 0===r&&(r=1),t===n)return!0;if(!t||!n)return!1;var o=Object.keys(t),a=Object.keys(n),i=o.length;if(a.length!==i)return!1;for(var u=0,s=o;u<s.length;u++){var l=s[u];if(t[l]!==n[l]&&!(r&&d(t[l])&&d(n[l])&&e(t[l],n[l],r-1)))return!1}return!0}(t,r)}),o}var m=v({name:"Box",keys:["unstable_system"]}),b=c({as:"div",useHook:m});function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(n,!0).forEach(function(t){h(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function k(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.filter(Boolean);return r.length?1===r.length?r[0]:function(e){var t=r,n=Array.isArray(t),o=0;for(t=n?t:t[Symbol.iterator]();;){var a;if(n){if(o>=t.length)break;a=t[o++]}else{if((o=t.next()).done)break;a=o.value}var i=a;"function"==typeof i?i(e):i&&(i.current=e)}}:null}var _="input, select, textarea, a[href], button, [tabindex], audio[controls], video[controls], [contenteditable]:not([contenteditable=false])";function E(e){return e instanceof HTMLElement}function S(e){return e.hasAttribute("tabindex")}function O(e){if(!E(e))return!1;if(function e(t){return!(!t.parentElement||!e(t.parentElement))||t.hidden}(e))return!1;if(function(e){return Boolean(e.disabled)}(e))return!1;var t=e.localName;if(["input","select","textarea","button"].indexOf(t)>=0)return!0;var n={a:function(){return e.hasAttribute("href")},audio:function(){return e.hasAttribute("controls")},video:function(){return e.hasAttribute("controls")}};return t in n?n[t]():!!function(e){var t=e.getAttribute("contenteditable");return"false"!==t&&null!=t}(e)||S(e)}function C(e){return E(e)&&O(e)&&!function(e){return S(e)&&e.tabIndex<0}(e)}function x(e,t){var n=Array.from(e.querySelectorAll(_)),r=n.filter(C);return C(e)&&r.unshift(e),!r.length&&t?n:r}function I(e,t){return x(e,t)[0]||null}function P(e){return(e.ownerDocument||document).activeElement===e}function M(e,t){var n=void 0===t?{}:t,r=n.isActive,o=void 0===r?P:r,a=n.preventScroll;return o(e)?-1:(e.focus({preventScroll:a}),o(e)?-1:requestAnimationFrame(function(){e.focus({preventScroll:a})}))}function T(e){var t=e.ownerDocument||document;return!!t.activeElement&&e.contains(t.activeElement)}function D(e){return e instanceof HTMLButtonElement||e instanceof HTMLInputElement||e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement||e instanceof HTMLAnchorElement||e instanceof HTMLAudioElement||e instanceof HTMLVideoElement}var A=v({name:"Tabbable",compose:m,keys:["disabled","focusable","unstable_clickOnEnter","unstable_clickOnSpace"],useOptions:function(e,t){var n=t.disabled,r=e.unstable_clickOnEnter,o=void 0===r||r,a=e.unstable_clickOnSpace;return y({disabled:n,unstable_clickOnEnter:o,unstable_clickOnSpace:void 0===a||a},k(e,["unstable_clickOnEnter","unstable_clickOnSpace"]))},useProps:function(e,n){var r=n.ref,o=n.tabIndex,a=n.onClick,i=n.onMouseDown,u=n.onKeyDown,s=n.style,l=k(n,["ref","tabIndex","onClick","onMouseDown","onKeyDown","style"]),c=t.useRef(null),f=e.disabled&&!e.focusable,d=t.useState(!0),p=d[0],v=d[1],m=p?o:o||0,b=e.disabled&&!p?y({pointerEvents:"none"},s):s;t.useEffect(function(){c.current&&!D(c.current)&&v(!1)},[]);var h=t.useCallback(function(t){e.disabled?(t.stopPropagation(),t.preventDefault()):a&&a(t)},[e.disabled,a]),g=t.useCallback(function(t){if(e.disabled)return t.stopPropagation(),void t.preventDefault();var n,r=t.currentTarget,o=t.target;if(r.contains(o)&&!((n=o)instanceof HTMLInputElement||n instanceof HTMLTextAreaElement||n instanceof HTMLSelectElement)&&!function(e){var t=navigator.userAgent,n=function(e){return-1!==t.indexOf(e)},r=n("Mac")||n("like Mac"),o=n("Safari")||n("Firefox");return!(r&&o&&e instanceof HTMLButtonElement)}(r)){t.preventDefault();var a=O(o)||o instanceof HTMLLabelElement;T(r)&&r!==o&&a||r.focus()}i&&i(t)},[e.disabled,i]),_=t.useCallback(function(t){u&&u(t),e.disabled||D(t.currentTarget)||(e.unstable_clickOnEnter&&"Enter"===t.key||e.unstable_clickOnSpace&&" "===t.key)&&(t.preventDefault(),t.target.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1})))},[e.disabled,e.unstable_clickOnEnter,e.unstable_clickOnSpace,u]);return y({ref:w(c,r),disabled:f,tabIndex:f?void 0:m,"aria-disabled":e.disabled,onClick:h,onMouseDown:g,onKeyDown:_,style:b},l)}}),H=c({as:"button",useHook:A}),F=v({name:"Button",compose:A,useProps:function(e,n){var r=n.ref,o=k(n,["ref"]),a=t.useRef(null),i=t.useState(void 0),u=i[0],s=i[1],l=t.useState("button"),c=l[0],f=l[1];return t.useEffect(function(){var e=a.current;e instanceof HTMLButtonElement||e instanceof HTMLInputElement||(e instanceof HTMLAnchorElement||s("button"),f(void 0))},[]),y({ref:w(a,r),role:u,type:c},o)}}),B=c({as:"button",useHook:F});function L(e,t){return-1===t?e:[].concat(e.slice(0,t),e.slice(t+1))}function R(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.useCallback(function(){var e=n.filter(Boolean),t=e,r=Array.isArray(t),o=0;for(t=r?t:t[Symbol.iterator]();;){var a;if(r){if(o>=t.length)break;a=t[o++]}else{if((o=t.next()).done)break;a=o.value}var i=a;i.apply(void 0,arguments)}},n)}function N(e){return t.useState(e)[0]}function j(e){void 0===e&&(e={});var n=N(e).state,r=void 0!==n&&n,o=t.useState(r);return{state:o[0],setState:o[1]}}j.__keys=["state","setState"];var V=v({name:"Checkbox",compose:A,useState:j,keys:["value","checked"],useOptions:function(e,t){var n=t.value,r=t.checked,o=e.unstable_clickOnEnter;return y({unstable_clickOnEnter:void 0!==o&&o,value:n,checked:r},k(e,["unstable_clickOnEnter"]))},useProps:function(e,n){var r=n.ref,o=n.onChange,a=n.onClick,i=k(n,["ref","onChange","onClick"]),u=t.useRef(null),s=function(e){var t=void 0===e.value;return void 0!==e.checked?e.checked:t?Boolean(e.state):-1!==(Array.isArray(e.state)?e.state:[]).indexOf(e.value)}(e);!function(e,n){t.useEffect(function(){e.current&&("indeterminate"===n.state?e.current.indeterminate=!0:e.current.indeterminate&&(e.current.indeterminate=!1))},[n.state,e])}(u,e);var l=t.useCallback(function(t){var n=e.state,r=e.value,a=e.setState,i=e.disabled,u=t.currentTarget;if(!i&&(o&&(u instanceof HTMLInputElement||(u.checked=!u.checked),o(t)),a))if(void 0===r)a(!s);else{var l=Array.isArray(n)?n:[],c=l.indexOf(r);a(-1===c?[].concat(l,[r]):L(l,c))}},[o,s,e.disabled,e.setState,e.state,e.value]),c=t.useCallback(function(e){e.target instanceof HTMLInputElement||l(e)},[l]);return y({ref:w(u,r),checked:s,"aria-checked":"indeterminate"===e.state?"mixed":s,value:e.value,role:"checkbox",type:"checkbox",onChange:l,onClick:R(c,a)},i)}}),K=c({as:"input",useHook:V});function W(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.useCallback(function(e){return n.filter(Boolean).reduce(function(e,t){return t(e)},e)},n)}function q(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")||void 0}var G="id";var z=t.createContext(function(e){return void 0===e&&(e=G),(e?e+"-":"")+Math.random().toString(32).substr(2,6)});function U(e){var n=e.children,r=e.prefix,o=void 0===r?G:r,a=t.useRef(0),i=t.useCallback(function(e){return void 0===e&&(e=o),(e?e+"-":"")+ ++a.current},[]);return t.createElement(z.Provider,{value:i},n)}function Y(e){void 0===e&&(e={});var n=N(e).baseId,r=t.useContext(z),o=t.useRef(0),a=t.useState(function(){return n||r()});return{baseId:a[0],unstable_setBaseId:a[1],unstable_idCountRef:o}}Y.__keys=["baseId","unstable_setBaseId","unstable_idCountRef"];var Z=v({name:"IdGroup",compose:m,useState:Y,keys:["id"],useOptions:function(e,n){var r=t.useContext(z),o=t.useState(function(){return e.id||n.id||e.baseId||r()})[0];return e.unstable_setBaseId&&o!==e.baseId&&e.unstable_setBaseId(o),y({},e,{baseId:o})},useProps:function(e,t){return y({id:e.id},t)}}),X=c({as:"div",useHook:Z}),$="undefined"==typeof window?t.useEffect:t.useLayoutEffect;function J(e){void 0===e&&(e={});var n,r,o=N(e),a=o.unstable_animated,i=void 0!==a&&a,u=o.visible,s=void 0!==u&&u,l=o.unstable_isMounted,c=void 0!==l&&l,f=Y(k(o,["unstable_animated","visible","unstable_isMounted"])),d=t.useState(s),p=d[0],v=d[1],m=t.useState(!1),b=m[0],h=m[1],g=t.useState(c),w=g[0],_=g[1],E=(n=p,r=t.useRef(null),$(function(){r.current=n},[n]),r);i&&!b&&null!=E.current&&E.current!==p&&h(!0),$(function(){if("number"==typeof i){var e=setTimeout(function(){return h(!1)},i);return function(){return clearTimeout(e)}}},[i]);var S=t.useCallback(function(){v(!0)},[w]),O=t.useCallback(function(){return v(!1)},[]),C=t.useCallback(function(){v(function(e){return!e})},[w]),x=t.useCallback(function(){return h(!1)},[]);return y({},f,{unstable_animated:i,unstable_animating:b,visible:p,show:S,hide:O,toggle:C,unstable_stopAnimation:x,unstable_setIsMounted:_})}var Q=[].concat(Y.__keys,["unstable_animated","unstable_animating","visible","show","hide","toggle","unstable_stopAnimation","unstable_setIsMounted"]);J.__keys=Q;var ee=v({name:"Hidden",compose:Z,useState:J,useProps:function(e,n){var r=n.onAnimationEnd,o=n.onTransitionEnd,a=n.className,i=n.style,u=k(n,["onAnimationEnd","onTransitionEnd","className","style"]),s=t.useState(null),l=s[0],c=s[1];t.useEffect(function(){c(e.visible?null:"hidden")},[e.visible]);var f=t.useCallback(function(){e.unstable_animated&&e.unstable_stopAnimation&&e.unstable_stopAnimation()},[e.unstable_animated,e.unstable_stopAnimation]),d=e.unstable_animated&&e.unstable_animating,p=!e.visible&&!d;return y({role:"region",id:e.baseId,className:q(l,a),onAnimationEnd:R(f,r),onTransitionEnd:R(f,o),hidden:p},p?{style:y({display:"none"},i)}:i?{style:i}:{},{},u)}}),te=c({as:"div",useHook:ee});"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var ne,re=function(e,t){return e(t={exports:{}},t.exports),t.exports}(function(e,t){!function(e){function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var r={get passive(){n=!0}};window.addEventListener("testPassive",null,r),window.removeEventListener("testPassive",null,r)}var o="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),a=[],i=!1,u=-1,s=void 0,l=void 0,c=function(e){return a.some(function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))})},f=function(e){var t=e||window.event;return!!c(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},d=function(){setTimeout(function(){void 0!==l&&(document.body.style.paddingRight=l,l=void 0),void 0!==s&&(document.body.style.overflow=s,s=void 0)})};e.disableBodyScroll=function(e,r){if(o){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!a.some(function(t){return t.targetElement===e})){var d={targetElement:e,options:r||{}};a=[].concat(t(a),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(u=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,r,o,a;1===t.targetTouches.length&&(r=e,a=(n=t).targetTouches[0].clientY-u,!c(n.target)&&(r&&0===r.scrollTop&&0<a?f(n):(o=r)&&o.scrollHeight-o.scrollTop<=o.clientHeight&&a<0?f(n):n.stopPropagation()))},i||(document.addEventListener("touchmove",f,n?{passive:!1}:void 0),i=!0)}}else{v=r,setTimeout(function(){if(void 0===l){var e=!!v&&!0===v.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(l=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===s&&(s=document.body.style.overflow,document.body.style.overflow="hidden")});var p={targetElement:e,options:r||{}};a=[].concat(t(a),[p])}var v},e.clearAllBodyScrollLocks=function(){o?(a.forEach(function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null}),i&&(document.removeEventListener("touchmove",f,n?{passive:!1}:void 0),i=!1),a=[],u=-1):(d(),a=[])},e.enableBodyScroll=function(e){if(o){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,a=a.filter(function(t){return t.targetElement!==e}),i&&0===a.length&&(document.removeEventListener("touchmove",f,n?{passive:!1}:void 0),i=!1)}else(a=a.filter(function(t){return t.targetElement!==e})).length||d()}}(t)});(ne=re)&&ne.__esModule&&Object.prototype.hasOwnProperty.call(ne,"default")&&ne.default;var oe=re.enableBodyScroll,ae=re.disableBodyScroll;function ie(e,n){var r=t.useRef(!1);t.useEffect(function(){if(r.current)return e();r.current=!0},n)}function ue(e,t){return"matches"in e?e.matches(t):"msMatchesSelector"in e?e.msMatchesSelector(t):e.webkitMatchesSelector(t)}function se(e,t){if("closest"in e)return e.closest(t);do{if(ue(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}var le=t.createContext("undefined"!=typeof document?document.body:null);function ce(e){var r=e.children,o=t.useContext(le),a=t.useState(function(){if("undefined"!=typeof document){var e=document.createElement("div");return e.className=ce.__className,e}return null})[0];return t.useEffect(function(){if(a&&o)return o.appendChild(a),function(){o.removeChild(a)}},[a,o]),a?n.createPortal(t.createElement(le.Provider,{value:a},r),a):null}function fe(e){null!=e.parentNode&&e.parentNode.removeChild(e)}function de(e){return e.find(function(e){return Boolean(e.current&&!e.current.hidden&&"true"===e.current.getAttribute("aria-modal"))})}ce.__className="__reakit-portal",ce.__selector="."+ce.__className;var pe="__reakit-focus-trap";function ve(e,n,r){var o=function(e,n){var r=t.useRef(null);return t.useEffect(function(){var t=e.current;t&&n.visible&&(r.current=se(t,ce.__selector))},[e,n.visible]),r}(e,r),a=r.visible&&r.modal,i=t.useRef(null),u=t.useRef(null);t.useEffect(function(){if(a){var e=o.current;if(e)return i.current||(i.current=document.createElement("div"),i.current.className=pe,i.current.tabIndex=0,i.current.style.position="fixed",i.current.setAttribute("aria-hidden","true")),u.current||(u.current=i.current.cloneNode()),e.insertAdjacentElement("beforebegin",i.current),e.insertAdjacentElement("afterend",u.current),function(){i.current&&fe(i.current),u.current&&fe(u.current)}}},[o,a]),t.useEffect(function(){var t=i.current,r=u.current;if(a&&t&&r){var o=function(t){var o=e.current;if(o&&!de(n)){t.preventDefault();var a,i,u=t.target===r?I(o):(i=x(o,a))[i.length-1]||null;u?u.focus():o.focus()}};return t.addEventListener("focus",o),r.addEventListener("focus",o),function(){t.removeEventListener("focus",o),r.removeEventListener("focus",o)}}},[e,n,a]),t.useEffect(function(){if(a){var t=function(){var t=e.current,r=o.current;t&&r&&!de(n)&&(r.contains(document.activeElement)||t.focus())};return document.addEventListener("click",t),function(){document.removeEventListener("click",t)}}},[e,n,o,a])}function me(e,t){var n=e.indexOf(t);return L(e,n)}var be=t.createContext({});function he(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e}),n}function ge(e,n,r,o,a,i){var u=he(a);t.useEffect(function(){if(i){var t=function(t){if(u.current){var o=e.current,a=n.current||[],i=t.target;if(o)if(!o.contains(i))if(!a.length||!a.some(function(e){return e.contains(i)}))if(!i.classList.contains(pe)&&!r.find(function(e){return Boolean(e.current&&e.current.contains(i))}))u.current(t)}};return document.addEventListener(o,t,!0),function(){document.removeEventListener(o,t,!0)}}},[e,n,r,o,i,u])}function ye(e){return void 0===e&&(e={}),J(e)}var ke=[].concat(J.__keys);ye.__keys=ke;var we=v({name:"Dialog",compose:ee,useState:ye,keys:["modal","hideOnEsc","hideOnClickOutside","preventBodyScroll","unstable_initialFocusRef","unstable_finalFocusRef","unstable_autoFocusOnShow","unstable_autoFocusOnHide","unstable_portal","unstable_orphan"],useOptions:function(e){var t=e.modal,n=void 0===t||t,r=e.hideOnEsc,o=void 0===r||r,a=e.hideOnClickOutside,i=void 0===a||a,u=e.preventBodyScroll,s=void 0===u||u,l=e.unstable_autoFocusOnShow,c=void 0===l||l,f=e.unstable_autoFocusOnHide,d=void 0===f||f,p=e.unstable_portal,v=void 0===p?n:p,m=e.unstable_orphan;return y({modal:n,hideOnEsc:o,hideOnClickOutside:i,preventBodyScroll:s,unstable_autoFocusOnShow:c,unstable_autoFocusOnHide:d,unstable_portal:v,unstable_orphan:n&&m},k(e,["modal","hideOnEsc","hideOnClickOutside","preventBodyScroll","unstable_autoFocusOnShow","unstable_autoFocusOnHide","unstable_portal","unstable_orphan"]))},useProps:function(e,n){var r=n.ref,o=n.onKeyDown,a=n.unstable_wrap,i=k(n,["ref","onKeyDown","unstable_wrap"]),u=t.useRef(null),s=function(e){var n=t.useRef([]),r=t.useRef(null);return $(function(){if(!e.visible){var t=function(){r.current=document.activeElement};return document.addEventListener("focus",t,!0),function(){return document.removeEventListener("focus",t,!0)}}},[e.visible]),t.useEffect(function(){if(e.visible){var t='[aria-controls~="'+e.baseId+'"]',o=Array.from(document.querySelectorAll(t));r.current instanceof HTMLElement?-1!==o.indexOf(r.current)?n.current=[r.current].concat(o.filter(function(e){return e!==r.current})):n.current=[r.current].concat(o):n.current=o}},[e.baseId,e.visible]),n}(e),l=function(e,n){var r=t.useContext(be),o=t.useState([]),a=o[0],i=o[1],u=t.useCallback(function(e){r.addDialog&&r.addDialog(e),i(function(t){return[].concat(t,[e])})},[r.addDialog]),s=t.useCallback(function(e){r.removeDialog&&r.removeDialog(e),i(function(t){return me(t,e)})},[r.removeDialog]);t.useEffect(function(){if(r.addDialog&&!n.unstable_orphan)return r.addDialog(e),function(){r.removeDialog&&r.removeDialog(e)}},[e,r.addDialog,r.removeDialog,n.unstable_orphan]),t.useEffect(function(){!1===r.visible&&n.visible&&n.hide&&!n.unstable_orphan&&n.hide()},[r.visible,n.visible,n.hide,n.unstable_orphan]);var l=t.useMemo(function(){return{visible:n.visible,addDialog:u,removeDialog:s}},[n.visible,u,s]);return{dialogs:a,wrap:t.useCallback(function(e){return t.createElement(be.Provider,{value:l},e)},[l])}}(u,e),c=l.dialogs,f=l.wrap,d=function(e,n){var r=t.useContext(le);return t.useCallback(function(o){if(n.unstable_portal&&(o=t.createElement(ce,null,t.createElement(le.Provider,{value:e.current},o))),n.unstable_orphan&&r){var a=se(r,ce.__selector);o=t.createElement(le.Provider,{value:a},o)}return o},[e,r,n.unstable_portal,n.unstable_orphan])}(u,e);!function(e,n){var r=!!n.modal&&Boolean(n.preventBodyScroll&&n.visible);t.useEffect(function(){var t=e.current;if(t&&r)return ae(t,{reserveScrollBarGap:!0}),function(){return oe(t)}},[e,r])}(u,e),ve(u,c,e),function(e,t,n){var r=n.unstable_initialFocusRef,o=n.visible&&n.unstable_autoFocusOnShow;ie(function(){var n=e.current;if(o&&n&&!t.find(function(e){return Boolean(e.current&&!e.current.hidden)}))if(r&&r.current)r.current.focus({preventScroll:!0});else{var a=I(n,!0),i=function(){return n.contains(document.activeElement)};M(a||n,{preventScroll:!0,isActive:i})}},[e,t,r,o])}(u,c,e),function(e,t,n){var r=n.unstable_autoFocusOnHide&&!n.visible;ie(function(){if(r){var o=e.current;if(!document.activeElement||!o||o.contains(document.activeElement)||!C(document.activeElement)&&"true"!==document.activeElement.getAttribute("data-dialog")){var a=n.unstable_finalFocusRef&&n.unstable_finalFocusRef.current||t.current&&t.current[0];a&&M(a)}}},[e,t,r])}(u,s,e),function(e,t,n,r){var o=function(o){return ge(e,t,n,o,r.hide,r.visible&&r.hideOnClickOutside)};o("click"),o("focus")}(u,s,c,e),function(e,t,n){var r=function(r){return ge(e,{current:null},t,r,function(e){e.stopPropagation(),e.preventDefault()},n.visible&&n.modal)};r("mouseover"),r("mouseout")}(u,c,e);var p=t.useCallback(function(t){if("Escape"===t.key&&e.hideOnEsc){if(!e.hide)return;t.stopPropagation(),e.hide()}},[e.hideOnEsc,e.hide]);return y({ref:w(u,r),role:"dialog",tabIndex:-1,onKeyDown:R(p,o),unstable_wrap:W(f,d,a),"aria-modal":!!e.modal||void 0,"data-dialog":!0},i)}}),_e=c({as:"div",useHook:we,useCreateElement:function(e,t,n){return s(e,t,n)}}),Ee=v({name:"DialogBackdrop",compose:ee,useState:ye,useProps:function(e,t){return y({id:void 0,role:"presentation"},t)}}),Se=c({as:"div",useHook:Ee}),Oe=v({name:"HiddenDisclosure",compose:F,useState:J,useProps:function(e,t){var n=t.onClick,r=t["aria-controls"],o=k(t,["onClick","aria-controls"]),a=r?r+" "+e.baseId:e.baseId;return y({onClick:R(e.toggle,n),"aria-expanded":Boolean(e.visible),"aria-controls":a},o)}}),Ce=c({as:"button",useHook:Oe}),xe=v({name:"DialogDisclosure",compose:Oe,useState:ye,useProps:function(e,t){return y({"aria-haspopup":"dialog"},t)}}),Ie=c({as:"button",useHook:xe});function Pe(e){return Array.isArray(e)?!e.length:d(e)?!Object.keys(e).length:null==e||""===e}function Me(e){return d(e)&&!Pe(e)}function Te(e,t){for(var n=e,r={},o=0,a=Object.keys(e);o<a.length;o++){var i=a[o],u=n[i];Array.isArray(u)?r[i]=u.map(function(e){return d(e)?Te(e,t):t}):d(u)?r[i]=Te(u,t):r[i]=t}return r}function De(e,t,n){if("string"==typeof t)return null==e[t]?n:e[t];var r=e,o=t,a=Array.isArray(o),i=0;for(o=a?o:o[Symbol.iterator]();;){var u;if(a){if(i>=o.length)break;u=o[i++]}else{if((i=o.next()).done)break;u=i.value}if(!(u in r))return n;r=r[u]}return null==r?n:r}function Ae(e){return"number"==typeof e?Math.floor(e)===e:String(Math.floor(Number(e)))===e}function He(e,t,n){var r,o=p(t),a=o[0],i=o.slice(1);if(null==a)return e;var u=Ae(a)?e||[]:e||{},s=i.length?He(u[a],i,n):n;return Ae(a)?e?[].concat(e.slice(0,Number(a)),[s],e.slice(Number(a)+1)):[s]:y({},e,((r={})[a]=s,r))}function Fe(e,t){return Pe(t)?Pe(e)?e:{}:t}function Be(e,t){switch(t.type){case"reset":return y({},e,{values:e.initialValues,touched:{},errors:{},messages:{},valid:!0,validating:!1,submitting:!1,submitFailed:0,submitSucceed:0});case"startValidate":return y({},e,{validating:!0});case"endValidate":return y({},e,{validating:!1,errors:Fe(e.errors,t.errors),messages:Fe(e.messages,t.messages),valid:!Me(t.errors)});case"startSubmit":return y({},e,{touched:Te(e.values,!0),submitting:!0});case"endSubmit":var n=!Me(t.errors);return y({},e,{valid:n,submitting:!1,errors:Fe(e.errors,t.errors),messages:Fe(e.messages,t.messages),submitSucceed:n?e.submitSucceed+1:e.submitSucceed,submitFailed:n?e.submitFailed:e.submitFailed+1});case"update":var r=t.name,o=t.value,a="function"==typeof o?o(De(e.values,r)):o;return y({},e,{values:He(e.values,r,null!=a?a:"")});case"blur":return y({},e,{touched:He(e.touched,t.name,!0)});case"push":var i=De(e.values,t.name,[]);return y({},e,{values:He(e.values,t.name,[].concat(i,[t.value]))});case"remove":var u=De(e.values,t.name,[]).slice();return delete u[t.index],y({},e,{values:He(e.values,t.name,u)});default:throw new Error}}function Le(e){void 0===e&&(e={});var n=N(e),r=n.values,o=void 0===r?{}:r,a=n.validateOnBlur,i=void 0===a||a,u=n.validateOnChange,s=void 0===u||u,l=n.resetOnSubmitSucceed,c=void 0!==l&&l,f=n.resetOnUnmount,p=void 0===f||f,v=n.onValidate,m=n.onSubmit,b=k(n,["values","validateOnBlur","validateOnChange","resetOnSubmitSucceed","resetOnUnmount","onValidate","onSubmit"]),h=he("function"!=typeof e?e.onValidate:v),g=he("function"!=typeof e?e.onSubmit:m),w=Y(b),_=t.useReducer(Be,{initialValues:o,values:o,touched:{},errors:{},messages:{},valid:!0,validating:!1,submitting:!1,submitFailed:0,submitSucceed:0}),E=_[0],S=(E.initialValues,k(E,["initialValues"])),O=_[1],C=t.useCallback(function(e){return void 0===e&&(e=S.values),new Promise(function(t){if(h.current){var n=h.current(e);r=n,Boolean(r&&r.then)&&O({type:"startValidate"}),t(Promise.resolve(n).then(function(e){return O({type:"endValidate",messages:e}),e}))}else t(void 0);var r}).catch(function(e){throw O({type:"endValidate",errors:e}),e})},[S.values]);return ie(function(){s&&C().catch(function(){})},[C,s]),t.useEffect(function(){if(p)return function(){O({type:"reset"})}},[p]),y({},w,{},S,{values:S.values,validate:C,reset:t.useCallback(function(){return O({type:"reset"})},[]),submit:t.useCallback(function(){return O({type:"startSubmit"}),C().then(function(e){if(g.current)return Promise.resolve(g.current(function e(t){if(Array.isArray(t))return t.filter(function(t){return!d(t)||e(t)});for(var n={},r=0,o=Object.keys(t);r<o.length;r++){var a=o[r],i=t[a];n[a]=d(i)?e(i):t[a]}return n}(S.values))).then(function(t){var n=y({},e,{},t);O({type:"endSubmit",messages:n}),c&&O({type:"reset"})});O({type:"endSubmit",messages:e})}).catch(function(e){O({type:"endSubmit",errors:e})})},[C]),update:t.useCallback(function(e,t){return O({type:"update",name:e,value:t})},[]),blur:t.useCallback(function(e){O({type:"blur",name:e}),i&&C().catch(function(){})},[C]),push:t.useCallback(function(e,t){return O({type:"push",name:e,value:t})},[]),remove:t.useCallback(function(e,t){return O({type:"remove",name:e,index:t})},[])})}var Re=[].concat(Y.__keys,["values","touched","messages","errors","validating","valid","submitting","submitSucceed","submitFailed","validate","submit","reset","update","blur","push","remove"]);Le.__keys=Re;var Ne=v({name:"Form",compose:Z,useState:Le,useProps:function(e,n){var r=n.onSubmit,o=k(n,["onSubmit"]);return y({role:"form",noValidate:!0,onSubmit:R(t.useCallback(function(t){t.preventDefault(),e.submit()},[e.submit]),r)},o)}}),je=c({as:"form",useHook:Ne});function Ve(e,t){return void 0===t&&(t="."),Array.isArray(e)?e.join(t):e}function Ke(e,t,n){if(void 0===n&&(n=""),t)return t+"-"+Ve(e,"-")+n}function We(e,t){return Ke(e,t,"-label")}function qe(e,t){return Ke(e,t,"-message")}function Ge(e,t){var n=e.touched,r=e.errors;return Boolean(De(n,t)&&De(r,t))}var ze=v({name:"FormCheckbox",compose:V,useState:Le,keys:["name","value"],useOptions:function(e,t){var n=e.name||t.name,r=void 0!==e.value?e.value:t.value,o=De(e.values,n);return y({},e,{state:o,setState:function(t){return e.update(n,t)},name:n,value:r})},useProps:function(e,n){var r=n.onBlur,o=k(n,["onBlur"]),a=void 0===e.value,i=t.useCallback(function(){e.blur(e.name)},[e.blur,e.name]);return y({"aria-invalid":Ge(e,e.name),name:Ve(e.name),onBlur:R(i,r)},a?{id:Ke(e.name,e.baseId),"aria-describedby":qe(e.name,e.baseId),"aria-labelledby":We(e.name,e.baseId)}:{},{},o)}}),Ue=c({as:"input",useHook:ze}),Ye=v({name:"Group",compose:m,useProps:function(e,t){return y({role:"group"},t)}}),Ze=c({as:"div",useHook:Ye}),Xe=v({name:"FormGroup",compose:Ye,useState:Le,keys:["name"],useProps:function(e,t){return y({id:Ke(e.name,e.baseId),tabIndex:-1,"aria-describedby":qe(e.name,e.baseId),"aria-labelledby":We(e.name,e.baseId),"aria-invalid":Ge(e,e.name)},t)}}),$e=c({as:"fieldset",useHook:Xe}),Je=v({name:"FormInput",compose:A,useState:Le,keys:["name"],useOptions:function(e,t){return y({name:t.name},e,{unstable_clickOnEnter:!1,unstable_clickOnSpace:!1})},useProps:function(e,n){var r=n.onChange,o=n.onBlur,a=k(n,["onChange","onBlur"]),i=t.useCallback(function(t){e.update(e.name,t.target.value)},[e.update,e.name]),u=t.useCallback(function(){e.blur(e.name)},[e.blur,e.name]);return y({id:Ke(e.name,e.baseId),name:Ve(e.name),value:De(e.values,e.name,""),onChange:R(i,r),onBlur:R(u,o),"aria-describedby":qe(e.name,e.baseId),"aria-labelledby":We(e.name,e.baseId),"aria-invalid":Ge(e,e.name)},a)}}),Qe=c({as:"input",useHook:Je}),et=v({name:"FormLabel",compose:m,useState:Le,keys:["name","label"],useProps:function(e,t){return y({children:e.label,id:We(e.name,e.baseId),htmlFor:Ke(e.name,e.baseId)},t)}}),tt=c({as:"label",useHook:et});var nt=v({name:"FormMessage",compose:m,useState:Le,keys:["name"],useProps:function(e,t){var n,r,o,a,i=Ge(e,e.name)?De(e.errors,e.name):void 0;return i=i||(n=e,r=e.name,o=n.touched,a=n.messages,Boolean(De(o,r)&&De(a,r))?De(e.messages,e.name):void 0),y({role:"alert",id:qe(e.name,e.baseId),children:i},t)}}),rt=c({as:"div",useHook:nt});function ot(e,t){return Ke(e,t,"-push")}var at=v({name:"FormPushButton",compose:F,useState:Le,keys:["name","value"],useOptions:function(e,t){return y({name:t.name,value:t.value},e)},useProps:function(e,n){var r=n.onClick,o=k(n,["onClick"]),a=t.useCallback(function(){e.push(e.name,e.value);var t=De(e.values,e.name,[]).length,n=Ke(Ve(e.name,"-")+"-"+t,e.baseId);n&&window.requestAnimationFrame(function(){var e='[id^="'+n+'"]',t=document.querySelector(e);t&&t.focus()})},[e.push,e.name,e.value,e.values,e.baseId]);return y({id:ot(e.name,e.baseId),onClick:R(a,r)},o)}}),it=c({as:"button",useHook:at});function ut(e){var t=void 0===e?{}:e,n=t.keyMap,r=t.onKey,o=t.stopPropagation,a=t.onKeyDown,i=t.shouldKeyDown,u=void 0===i?function(){return!0}:i,s=t.preventDefault,l=void 0===s||s;return function(e){if(n){var t="function"==typeof n?n(e):n,i="function"==typeof l?l(e):l,s="function"==typeof o?o(e):o;if(e.key in t){var c=t[e.key];if("function"==typeof c&&u(e))return i&&e.preventDefault(),s&&e.stopPropagation(),r&&r(e),void c(e)}a&&a(e)}}}var st=v({name:"Id",compose:m,useState:Y,keys:["id"],useOptions:function(e,n){var r=t.useContext(z),o=t.useState(function(){return e.unstable_idCountRef?(e.unstable_idCountRef.current+=1,"-"+e.unstable_idCountRef.current):e.baseId?"-"+r(""):""})[0],a=t.useMemo(function(){return e.baseId||r()},[e.baseId,r]),i=e.id||n.id||""+a+o;return y({},e,{id:i})},useProps:function(e,t){return y({id:e.id},t)}}),lt=c({as:"div",useHook:st});function ct(e,t){var n=e.stops,r=e.currentId,o=e.unstable_pastId,a=e.unstable_moves,i=e.loop;switch(t.type){case"register":var u=t.id,s=t.ref;if(0===n.length)return y({},e,{stops:[{id:u,ref:s}]});if(n.findIndex(function(e){return e.id===u})>=0)return e;var l=n.findIndex(function(e){return!(!e.ref.current||!s.current)&&Boolean(e.ref.current.compareDocumentPosition(s.current)&Node.DOCUMENT_POSITION_PRECEDING)});return y({},e,-1===l?{stops:[].concat(n,[{id:u,ref:s}])}:{stops:[].concat(n.slice(0,l),[{id:u,ref:s}],n.slice(l))});case"unregister":var c=t.id,f=n.filter(function(e){return e.id!==c});return f.length===n.length?e:y({},e,{stops:f,unstable_pastId:o&&o===c?null:o,currentId:r&&r===c?null:r});case"move":var d=t.id,p=t.silent?a:a+1;if(null===d)return y({},e,{currentId:null,unstable_pastId:r,unstable_moves:p});var v=n.findIndex(function(e){return e.id===d});return-1===v?e:n[v].id===r?y({},e,{unstable_moves:p}):y({},e,{currentId:n[v].id,unstable_pastId:r,unstable_moves:p});case"next":if(null==r)return ct(e,{type:"move",id:n[0]&&n[0].id});var m=n.findIndex(function(e){return e.id===r}),b=[].concat(n.slice(m+1),i?n.slice(0,m):[]),h=b.findIndex(function(e){return e.id===r})+1;return ct(e,{type:"move",id:b[h]&&b[h].id});case"previous":var g=ct(y({},e,{stops:n.slice().reverse()}),{type:"next"});g.stops;return y({},e,{},k(g,["stops"]));case"first":var w=n[0];return ct(e,{type:"move",id:w&&w.id});case"last":var _=n[n.length-1];return ct(e,{type:"move",id:_&&_.id});case"reset":return y({},e,{currentId:null,unstable_pastId:null});case"orientate":return y({},e,{orientation:t.orientation});default:throw new Error}}function ft(e){void 0===e&&(e={});var n=N(e),r=n.orientation,o=n.currentId,a=void 0===o?null:o,i=n.loop,u=void 0!==i&&i,s=k(n,["orientation","currentId","loop"]),l=t.useReducer(ct,{orientation:r,stops:[],currentId:a,unstable_pastId:null,unstable_moves:0,loop:u}),c=l[0],f=l[1];return y({},Y(s),{},c,{register:t.useCallback(function(e,t){return f({type:"register",id:e,ref:t})},[]),unregister:t.useCallback(function(e){return f({type:"unregister",id:e})},[]),move:t.useCallback(function(e,t){return f({type:"move",id:e,silent:t})},[]),next:t.useCallback(function(){return f({type:"next"})},[]),previous:t.useCallback(function(){return f({type:"previous"})},[]),first:t.useCallback(function(){return f({type:"first"})},[]),last:t.useCallback(function(){return f({type:"last"})},[]),unstable_reset:t.useCallback(function(){return f({type:"reset"})},[]),unstable_orientate:t.useCallback(function(e){return f({type:"orientate",orientation:e})},[])})}var dt=[].concat(Y.__keys,["orientation","stops","currentId","unstable_pastId","unstable_moves","loop","register","unregister","move","next","previous","first","last","unstable_reset","unstable_orientate"]);ft.__keys=dt;var pt=v({name:"Rover",compose:[A,st],useState:ft,keys:["stopId"],useProps:function(e,n){var r=n.ref,o=n.tabIndex,a=void 0===o?0:o,i=n.onFocus,u=n.onKeyDown,s=k(n,["ref","tabIndex","onFocus","onKeyDown"]),l=t.useRef(null),c=e.stopId||s.id||e.id,f=e.disabled&&!e.focusable,d=null==e.currentId,p=e.currentId===c,v=(e.stops||[])[0]&&e.stops[0].id===c,m=p||v&&d;t.useEffect(function(){if(!f&&c)return e.register&&e.register(c,l),function(){return e.unregister&&e.unregister(c)}},[c,f,e.register,e.unregister]),t.useEffect(function(){l.current&&e.unstable_moves&&p&&!T(l.current)&&l.current.focus()},[p,e.unstable_moves]);var b=t.useCallback(function(t){c&&t.currentTarget.contains(t.target)&&e.move(c,!0)},[e.move,c]),h=t.useMemo(function(){return ut({onKeyDown:u,stopPropagation:!0,shouldKeyDown:function(e){return e.currentTarget.contains(e.target)},keyMap:{ArrowUp:"horizontal"!==e.orientation&&e.previous,ArrowRight:"vertical"!==e.orientation&&e.next,ArrowDown:"horizontal"!==e.orientation&&e.next,ArrowLeft:"vertical"!==e.orientation&&e.previous,Home:e.first,End:e.last,PageUp:e.first,PageDown:e.last}})},[u,e.orientation,e.previous,e.next,e.first,e.last]);return y({ref:w(l,r),id:c,tabIndex:m?a:-1,onFocus:R(b,i),onKeyDown:h},s)}}),vt=c({as:"button",useHook:pt});function mt(e){void 0===e&&(e={});var n=N(e),r=n.state,o=n.loop,a=void 0===o||o,i=k(n,["state","loop"]),u=t.useState(r),s=u[0],l=u[1],c=ft(y({},i,{loop:a}));return y({},c,{state:s,setState:l})}var bt=[].concat(ft.__keys,["state","setState"]);mt.__keys=bt;var ht=v({name:"Radio",compose:pt,useState:mt,keys:["value","checked"],useOptions:function(e,t){var n=t.value,r=t.checked,o=e.unstable_clickOnEnter;return y({value:n,checked:r,unstable_clickOnEnter:void 0!==o&&o},k(e,["unstable_clickOnEnter"]))},useProps:function(e,n){var r=n.onChange,o=n.onClick,a=k(n,["onChange","onClick"]),i=void 0!==e.checked?e.checked:e.state===e.value,u=t.useCallback(function(t){r&&r(t),!e.disabled&&e.setState&&e.setState(e.value)},[r,e.disabled,e.setState,e.value]),s=t.useCallback(function(e){e.target instanceof HTMLInputElement||u(e)},[u]);return y({checked:i,"aria-checked":i,value:e.value,role:"radio",type:"radio",onChange:u,onClick:R(s,o)},a)}}),gt=c({as:"input",useHook:ht}),yt=t.createContext(null),kt=v({name:"FormRadioGroup",compose:Xe,useState:Le,keys:["name"],useOptions:function(e,t){return y({name:t.name},e)},useProps:function(e,n){var r=n.unstable_wrap,o=k(n,["unstable_wrap"]),a=ft({baseId:Ke(e.name,e.baseId),loop:!0}),i=t.useMemo(function(){return a},[a.stops,a.currentId,a.unstable_pastId]);return y({role:"radiogroup",unstable_wrap:W(t.useCallback(function(e){return t.createElement(yt.Provider,{value:i},e)},[i]),r)},o)}}),wt=c({as:"fieldset",useHook:kt}),_t=v({name:"FormRadio",compose:ht,useState:Le,keys:["name","value"],useOptions:function(e,n){var r=e.name||n.name,o=void 0!==e.value?e.value:n.value,a=t.useContext(yt),i=De(e.values,r)===o;if(!a)throw new Error("Missing FormRadioGroup");return y({},e,{},a,{checked:i,name:r,value:o})},useProps:function(e,n){var r=n.onChange,o=n.onBlur,a=n.onFocus,i=k(n,["onChange","onBlur","onFocus"]),u=t.useCallback(function(){e.update(e.name,e.value)},[e.update,e.name,e.value]),s=t.useCallback(function(){e.blur(e.name)},[e.blur,e.name]),l=t.useCallback(function(){e.update(e.name,e.value)},[e.update,e.name,e.value]);return y({name:Ve(e.name),onChange:R(u,r),onBlur:R(s,o),onFocus:R(l,a)},i)}}),Et=c({as:"input",useHook:_t}),St=v({name:"FormRemoveButton",compose:F,useState:Le,keys:["name","index"],useOptions:function(e,t){return y({name:t.name},e)},useProps:function(e,n){var r=n.onClick,o=k(n,["onClick"]);return y({onClick:R(t.useCallback(function(){e.remove(e.name,e.index);var t=Ke(e.name,e.baseId);t&&window.requestAnimationFrame(function(){var n='[id^="'+t+'-"]',r=document.querySelectorAll(n);if(r.length){var o=Array.from(r).reduce(function(n,r){var o=r.id.match(new RegExp(t+"-([0-9]+)"));if(!o)return n;var a=o[1];return Number(a)>n&&e.index>=n?Number(a):n},0),a='[id^="'+t+"-"+o+'"]',i=document.querySelector(a);if(i)return void i.focus()}var u=ot(e.name,e.baseId);if(u){var s=document.getElementById(u);s&&s.focus()}})},[e.remove,e.name,e.index,e.baseId]),r)},o)}}),Ot=c({as:"button",useHook:St});var Ct=v({name:"FormSubmitButton",compose:F,useState:Le,useOptions:function(e){return y({disabled:e.submitting},e)},useProps:function(e,n){var r=n.onClick,o=k(n,["onClick"]);return y({type:"submit",onClick:R(t.useCallback(function(){window.requestAnimationFrame(function(){var t=function(e){var t="[aria-invalid=true][id^="+e+"]";return document.querySelector(t)}(e.baseId);t&&(t.focus(),"select"in t&&t.select())})},[e.baseId]),r)},o)}}),xt=c({as:"button",useHook:Ct}),It="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Pt=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(It&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}();var Mt=It&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},Pt))}};function Tt(e){return e&&"[object Function]"==={}.toString.call(e)}function Dt(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function At(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function Ht(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=Dt(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/(auto|scroll|overlay)/.test(n+o+r)?e:Ht(At(e))}function Ft(e){return e&&e.referenceNode?e.referenceNode:e}var Bt=It&&!(!window.MSInputMethodContext||!document.documentMode),Lt=It&&/MSIE 10/.test(navigator.userAgent);function Rt(e){return 11===e?Bt:10===e?Lt:Bt||Lt}function Nt(e){if(!e)return document.documentElement;for(var t=Rt(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===Dt(n,"position")?Nt(n):n:e?e.ownerDocument.documentElement:document.documentElement}function jt(e){return null!==e.parentNode?jt(e.parentNode):e}function Vt(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,o=n?t:e,a=document.createRange();a.setStart(r,0),a.setEnd(o,0);var i,u,s=a.commonAncestorContainer;if(e!==s&&t!==s||r.contains(o))return"BODY"===(u=(i=s).nodeName)||"HTML"!==u&&Nt(i.firstElementChild)!==i?Nt(s):s;var l=jt(e);return l.host?Vt(l.host,t):Vt(e,jt(t).host)}function Kt(e){var t="top"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=e.nodeName;if("BODY"===n||"HTML"===n){var r=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||r)[t]}return e[t]}function Wt(e,t){var n="x"===t?"Left":"Top",r="Left"===n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"],10)+parseFloat(e["border"+r+"Width"],10)}function qt(e,t,n,r){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],Rt(10)?parseInt(n["offset"+e])+parseInt(r["margin"+("Height"===e?"Top":"Left")])+parseInt(r["margin"+("Height"===e?"Bottom":"Right")]):0)}function Gt(e){var t=e.body,n=e.documentElement,r=Rt(10)&&getComputedStyle(n);return{height:qt("Height",t,n,r),width:qt("Width",t,n,r)}}var zt=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Ut=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Yt=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},Zt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};function Xt(e){return Zt({},e,{right:e.left+e.width,bottom:e.top+e.height})}function $t(e){var t={};try{if(Rt(10)){t=e.getBoundingClientRect();var n=Kt(e,"top"),r=Kt(e,"left");t.top+=n,t.left+=r,t.bottom+=n,t.right+=r}else t=e.getBoundingClientRect()}catch(e){}var o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},a="HTML"===e.nodeName?Gt(e.ownerDocument):{},i=a.width||e.clientWidth||o.width,u=a.height||e.clientHeight||o.height,s=e.offsetWidth-i,l=e.offsetHeight-u;if(s||l){var c=Dt(e);s-=Wt(c,"x"),l-=Wt(c,"y"),o.width-=s,o.height-=l}return Xt(o)}function Jt(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Rt(10),o="HTML"===t.nodeName,a=$t(e),i=$t(t),u=Ht(e),s=Dt(t),l=parseFloat(s.borderTopWidth,10),c=parseFloat(s.borderLeftWidth,10);n&&o&&(i.top=Math.max(i.top,0),i.left=Math.max(i.left,0));var f=Xt({top:a.top-i.top-l,left:a.left-i.left-c,width:a.width,height:a.height});if(f.marginTop=0,f.marginLeft=0,!r&&o){var d=parseFloat(s.marginTop,10),p=parseFloat(s.marginLeft,10);f.top-=l-d,f.bottom-=l-d,f.left-=c-p,f.right-=c-p,f.marginTop=d,f.marginLeft=p}return(r&&!n?t.contains(u):t===u&&"BODY"!==u.nodeName)&&(f=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Kt(t,"top"),o=Kt(t,"left"),a=n?-1:1;return e.top+=r*a,e.bottom+=r*a,e.left+=o*a,e.right+=o*a,e}(f,t)),f}function Qt(e){if(!e||!e.parentElement||Rt())return document.documentElement;for(var t=e.parentElement;t&&"none"===Dt(t,"transform");)t=t.parentElement;return t||document.documentElement}function en(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a={top:0,left:0},i=o?Qt(e):Vt(e,Ft(t));if("viewport"===r)a=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.ownerDocument.documentElement,r=Jt(e,n),o=Math.max(n.clientWidth,window.innerWidth||0),a=Math.max(n.clientHeight,window.innerHeight||0),i=t?0:Kt(n),u=t?0:Kt(n,"left");return Xt({top:i-r.top+r.marginTop,left:u-r.left+r.marginLeft,width:o,height:a})}(i,o);else{var u=void 0;"scrollParent"===r?"BODY"===(u=Ht(At(t))).nodeName&&(u=e.ownerDocument.documentElement):u="window"===r?e.ownerDocument.documentElement:r;var s=Jt(u,i,o);if("HTML"!==u.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===Dt(t,"position"))return!0;var r=At(t);return!!r&&e(r)}(i))a=s;else{var l=Gt(e.ownerDocument),c=l.height,f=l.width;a.top+=s.top-s.marginTop,a.bottom=c+s.top,a.left+=s.left-s.marginLeft,a.right=f+s.left}}var d="number"==typeof(n=n||0);return a.left+=d?n:n.left||0,a.top+=d?n:n.top||0,a.right-=d?n:n.right||0,a.bottom-=d?n:n.bottom||0,a}function tn(e,t,n,r,o){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var i=en(n,r,a,o),u={top:{width:i.width,height:t.top-i.top},right:{width:i.right-t.right,height:i.height},bottom:{width:i.width,height:i.bottom-t.bottom},left:{width:t.left-i.left,height:i.height}},s=Object.keys(u).map(function(e){return Zt({key:e},u[e],{area:(t=u[e],t.width*t.height)});var t}).sort(function(e,t){return t.area-e.area}),l=s.filter(function(e){var t=e.width,r=e.height;return t>=n.clientWidth&&r>=n.clientHeight}),c=l.length>0?l[0].key:s[0].key,f=e.split("-")[1];return c+(f?"-"+f:"")}function nn(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return Jt(n,r?Qt(t):Vt(t,Ft(n)),r)}function rn(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),r=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+r,height:e.offsetHeight+n}}function on(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function an(e,t,n){n=n.split("-")[0];var r=rn(e),o={width:r.width,height:r.height},a=-1!==["right","left"].indexOf(n),i=a?"top":"left",u=a?"left":"top",s=a?"height":"width",l=a?"width":"height";return o[i]=t[i]+t[s]/2-r[s]/2,o[u]=n===u?t[u]-r[l]:t[on(u)],o}function un(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function sn(e,t,n){return(void 0===n?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var r=un(e,function(e){return e[t]===n});return e.indexOf(r)}(e,"name",n))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var n=e.function||e.fn;e.enabled&&Tt(n)&&(t.offsets.popper=Xt(t.offsets.popper),t.offsets.reference=Xt(t.offsets.reference),t=n(t,e))}),t}function ln(e,t){return e.some(function(e){var n=e.name;return e.enabled&&n===t})}function cn(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var o=t[r],a=o?""+o+n:e;if(void 0!==document.body.style[a])return a}return null}function fn(e){var t=e.ownerDocument;return t?t.defaultView:window}function dn(e,t,n,r){n.updateBound=r,fn(e).addEventListener("resize",n.updateBound,{passive:!0});var o=Ht(e);return function e(t,n,r,o){var a="BODY"===t.nodeName,i=a?t.ownerDocument.defaultView:t;i.addEventListener(n,r,{passive:!0}),a||e(Ht(i.parentNode),n,r,o),o.push(i)}(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function pn(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,fn(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function vn(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function mn(e,t){Object.keys(t).forEach(function(n){var r="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&vn(t[n])&&(r="px"),e.style[n]=t[n]+r})}var bn=It&&/Firefox/i.test(navigator.userAgent);function hn(e,t,n){var r=un(e,function(e){return e.name===t}),o=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});if(!o){var a="`"+t+"`",i="`"+n+"`";console.warn(i+" modifier is required by "+a+" modifier in order to work, be sure to include it before "+a+"!")}return o}var gn=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],yn=gn.slice(3);function kn(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=yn.indexOf(e),r=yn.slice(n+1).concat(yn.slice(0,n));return t?r.reverse():r}var wn={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"};function _n(e,t,n,r){var o=[0,0],a=-1!==["right","left"].indexOf(r),i=e.split(/(\+|\-)/).map(function(e){return e.trim()}),u=i.indexOf(un(i,function(e){return-1!==e.search(/,|\s/)}));i[u]&&-1===i[u].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var s=/\s*,\s*|\s+/,l=-1!==u?[i.slice(0,u).concat([i[u].split(s)[0]]),[i[u].split(s)[1]].concat(i.slice(u+1))]:[i];return(l=l.map(function(e,r){var o=(1===r?!a:a)?"height":"width",i=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,r){var o=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),a=+o[1],i=o[2];if(!a)return e;if(0===i.indexOf("%")){var u=void 0;switch(i){case"%p":u=n;break;case"%":case"%r":default:u=r}return Xt(u)[t]/100*a}if("vh"===i||"vw"===i){return("vh"===i?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*a}return a}(e,o,t,n)})})).forEach(function(e,t){e.forEach(function(n,r){vn(n)&&(o[t]+=n*("-"===e[r-1]?-1:1))})}),o}var En={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],r=t.split("-")[1];if(r){var o=e.offsets,a=o.reference,i=o.popper,u=-1!==["bottom","top"].indexOf(n),s=u?"left":"top",l=u?"width":"height",c={start:Yt({},s,a[s]),end:Yt({},s,a[s]+a[l]-i[l])};e.offsets.popper=Zt({},i,c[r])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,r=e.placement,o=e.offsets,a=o.popper,i=o.reference,u=r.split("-")[0],s=void 0;return s=vn(+n)?[+n,0]:_n(n,a,i,u),"left"===u?(a.top+=s[0],a.left-=s[1]):"right"===u?(a.top+=s[0],a.left+=s[1]):"top"===u?(a.left+=s[0],a.top-=s[1]):"bottom"===u&&(a.left+=s[0],a.top+=s[1]),e.popper=a,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var n=t.boundariesElement||Nt(e.instance.popper);e.instance.reference===n&&(n=Nt(n));var r=cn("transform"),o=e.instance.popper.style,a=o.top,i=o.left,u=o[r];o.top="",o.left="",o[r]="";var s=en(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);o.top=a,o.left=i,o[r]=u,t.boundaries=s;var l=t.priority,c=e.offsets.popper,f={primary:function(e){var n=c[e];return c[e]<s[e]&&!t.escapeWithReference&&(n=Math.max(c[e],s[e])),Yt({},e,n)},secondary:function(e){var n="right"===e?"left":"top",r=c[n];return c[e]>s[e]&&!t.escapeWithReference&&(r=Math.min(c[n],s[e]-("right"===e?c.width:c.height))),Yt({},n,r)}};return l.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";c=Zt({},c,f[t](e))}),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,r=t.reference,o=e.placement.split("-")[0],a=Math.floor,i=-1!==["top","bottom"].indexOf(o),u=i?"right":"bottom",s=i?"left":"top",l=i?"width":"height";return n[u]<a(r[s])&&(e.offsets.popper[s]=a(r[s])-n[l]),n[s]>a(r[u])&&(e.offsets.popper[s]=a(r[u])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!hn(e.instance.modifiers,"arrow","keepTogether"))return e;var r=t.element;if("string"==typeof r){if(!(r=e.instance.popper.querySelector(r)))return e}else if(!e.instance.popper.contains(r))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var o=e.placement.split("-")[0],a=e.offsets,i=a.popper,u=a.reference,s=-1!==["left","right"].indexOf(o),l=s?"height":"width",c=s?"Top":"Left",f=c.toLowerCase(),d=s?"left":"top",p=s?"bottom":"right",v=rn(r)[l];u[p]-v<i[f]&&(e.offsets.popper[f]-=i[f]-(u[p]-v)),u[f]+v>i[p]&&(e.offsets.popper[f]+=u[f]+v-i[p]),e.offsets.popper=Xt(e.offsets.popper);var m=u[f]+u[l]/2-v/2,b=Dt(e.instance.popper),h=parseFloat(b["margin"+c],10),g=parseFloat(b["border"+c+"Width"],10),y=m-e.offsets.popper[f]-h-g;return y=Math.max(Math.min(i[l]-v,y),0),e.arrowElement=r,e.offsets.arrow=(Yt(n={},f,Math.round(y)),Yt(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(ln(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var n=en(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),r=e.placement.split("-")[0],o=on(r),a=e.placement.split("-")[1]||"",i=[];switch(t.behavior){case wn.FLIP:i=[r,o];break;case wn.CLOCKWISE:i=kn(r);break;case wn.COUNTERCLOCKWISE:i=kn(r,!0);break;default:i=t.behavior}return i.forEach(function(u,s){if(r!==u||i.length===s+1)return e;r=e.placement.split("-")[0],o=on(r);var l=e.offsets.popper,c=e.offsets.reference,f=Math.floor,d="left"===r&&f(l.right)>f(c.left)||"right"===r&&f(l.left)<f(c.right)||"top"===r&&f(l.bottom)>f(c.top)||"bottom"===r&&f(l.top)<f(c.bottom),p=f(l.left)<f(n.left),v=f(l.right)>f(n.right),m=f(l.top)<f(n.top),b=f(l.bottom)>f(n.bottom),h="left"===r&&p||"right"===r&&v||"top"===r&&m||"bottom"===r&&b,g=-1!==["top","bottom"].indexOf(r),y=!!t.flipVariations&&(g&&"start"===a&&p||g&&"end"===a&&v||!g&&"start"===a&&m||!g&&"end"===a&&b),k=!!t.flipVariationsByContent&&(g&&"start"===a&&v||g&&"end"===a&&p||!g&&"start"===a&&b||!g&&"end"===a&&m),w=y||k;(d||h||w)&&(e.flipped=!0,(d||h)&&(r=i[s+1]),w&&(a=function(e){return"end"===e?"start":"start"===e?"end":e}(a)),e.placement=r+(a?"-"+a:""),e.offsets.popper=Zt({},e.offsets.popper,an(e.instance.popper,e.offsets.reference,e.placement)),e=sn(e.instance.modifiers,e,"flip"))}),e},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,o=r.popper,a=r.reference,i=-1!==["left","right"].indexOf(n),u=-1===["top","left"].indexOf(n);return o[i?"left":"top"]=a[n]-(u?o[i?"width":"height"]:0),e.placement=on(t),e.offsets.popper=Xt(o),e}},hide:{order:800,enabled:!0,fn:function(e){if(!hn(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=un(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,r=t.y,o=e.offsets.popper,a=un(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==a&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var i=void 0!==a?a:t.gpuAcceleration,u=Nt(e.instance.popper),s=$t(u),l={position:o.position},c=function(e,t){var n=e.offsets,r=n.popper,o=n.reference,a=Math.round,i=Math.floor,u=function(e){return e},s=a(o.width),l=a(r.width),c=-1!==["left","right"].indexOf(e.placement),f=-1!==e.placement.indexOf("-"),d=t?c||f||s%2==l%2?a:i:u,p=t?a:u;return{left:d(s%2==1&&l%2==1&&!f&&t?r.left-1:r.left),top:p(r.top),bottom:p(r.bottom),right:d(r.right)}}(e,window.devicePixelRatio<2||!bn),f="bottom"===n?"top":"bottom",d="right"===r?"left":"right",p=cn("transform"),v=void 0,m=void 0;if(m="bottom"===f?"HTML"===u.nodeName?-u.clientHeight+c.bottom:-s.height+c.bottom:c.top,v="right"===d?"HTML"===u.nodeName?-u.clientWidth+c.right:-s.width+c.right:c.left,i&&p)l[p]="translate3d("+v+"px, "+m+"px, 0)",l[f]=0,l[d]=0,l.willChange="transform";else{var b="bottom"===f?-1:1,h="right"===d?-1:1;l[f]=m*b,l[d]=v*h,l.willChange=f+", "+d}var g={"x-placement":e.placement};return e.attributes=Zt({},g,e.attributes),e.styles=Zt({},l,e.styles),e.arrowStyles=Zt({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return mn(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&mn(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,r,o){var a=nn(o,t,e,n.positionFixed),i=tn(n.placement,a,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",i),mn(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},Sn=function(){function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};zt(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(r.update)},this.update=Mt(this.update.bind(this)),this.options=Zt({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(Zt({},e.Defaults.modifiers,o.modifiers)).forEach(function(t){r.options.modifiers[t]=Zt({},e.Defaults.modifiers[t]||{},o.modifiers?o.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return Zt({name:e},r.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&Tt(e.onLoad)&&e.onLoad(r.reference,r.popper,r.options,e,r.state)}),this.update();var a=this.options.eventsEnabled;a&&this.enableEventListeners(),this.state.eventsEnabled=a}return Ut(e,[{key:"update",value:function(){return function(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=nn(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=tn(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=an(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=sn(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,ln(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[cn("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=dn(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return pn.call(this)}}]),e}();function On(e){void 0===e&&(e={});var n=N(e),r=n.gutter,o=void 0===r?12:r,a=n.placement,i=void 0===a?"bottom":a,u=n.unstable_flip,s=void 0===u||u,l=n.unstable_shift,c=void 0===l||l,f=n.unstable_preventOverflow,d=void 0===f||f,p=n.unstable_boundariesElement,v=void 0===p?"scrollParent":p,m=n.unstable_fixed,b=void 0!==m&&m,h=k(n,["gutter","placement","unstable_flip","unstable_shift","unstable_preventOverflow","unstable_boundariesElement","unstable_fixed"]),g=t.useRef(null),w=t.useRef(null),_=t.useRef(null),E=t.useRef(null),S=t.useState(i),O=S[0],C=S[1],x=t.useState(i),I=x[0],P=x[1],M=t.useState({}),T=M[0],D=M[1],A=t.useState({}),H=A[0],F=A[1],B=ye(h),L=t.useCallback(function(){return!!g.current&&(g.current.scheduleUpdate(),!0)},[]),R=t.useCallback(function(){return!!g.current&&(g.current.update(),!0)},[]);return $(function(){return w.current&&_.current&&(g.current=new Sn(w.current,_.current,{placement:O,eventsEnabled:B.visible,positionFixed:b,modifiers:{applyStyle:{enabled:!1},flip:{enabled:s,padding:16},shift:{enabled:c},offset:{enabled:c,offset:"0, "+o},preventOverflow:{enabled:d,boundariesElement:v},arrow:E.current?{enabled:!0,element:E.current}:void 0,updateStateModifier:{order:900,enabled:!0,fn:function(e){return P(e.placement),D(e.styles),null==e.arrowStyles.left||isNaN(+e.arrowStyles.left)||null==e.arrowStyles.top||isNaN(+e.arrowStyles.top)||F(e.arrowStyles),e}}}})),function(){g.current&&g.current.destroy()}},[B.visible,O,s,c,o,d,v,b]),t.useEffect(function(){B.visible&&g.current&&g.current.scheduleUpdate()},[B.visible]),t.useEffect(function(){h.visible&&g.current&&g.current.scheduleUpdate()},[h.visible]),y({},B,{unstable_referenceRef:w,unstable_popoverRef:_,unstable_arrowRef:E,unstable_popoverStyles:T,unstable_arrowStyles:H,unstable_scheduleUpdate:L,unstable_update:R,unstable_originalPlacement:O,placement:I,place:t.useCallback(C,[])})}Sn.Utils=("undefined"!=typeof window?window:global).PopperUtils,Sn.placements=gn,Sn.Defaults=En;var Cn=[].concat(ye.__keys,["unstable_referenceRef","unstable_popoverRef","unstable_arrowRef","unstable_popoverStyles","unstable_arrowStyles","unstable_scheduleUpdate","unstable_update","unstable_originalPlacement","placement","place"]);On.__keys=Cn;var xn=v({name:"Popover",compose:we,useState:On,useOptions:function(e){var t=e.modal;return y({modal:void 0!==t&&t},k(e,["modal"]))},useProps:function(e,t){var n=t.ref,r=t.style,o=k(t,["ref","style"]);return y({ref:w(e.unstable_popoverRef,n),style:y({},e.unstable_popoverStyles,{},r)},o)}}),In=c({as:"div",useHook:xn,useCreateElement:function(e,t,n){return s(e,t,n)}});var Pn=t.createContext(null);function Mn(e){void 0===e&&(e={});var n=N(e),r=n.orientation,o=void 0===r?"horizontal":r,a=n.unstable_values,i=void 0===a?{}:a,u=k(n,["orientation","unstable_values"]),s=t.useState(i),l=s[0],c=s[1],f=ft(y({},u,{orientation:o}));return y({},f,{unstable_values:l,unstable_setValue:t.useCallback(function(e,t){c(function(n){var r;return y({},n,((r={})[e]="function"==typeof t?t(n):t,r))})},[])})}var Tn=[].concat(ft.__keys,["unstable_values","unstable_setValue"]);function Dn(e){void 0===e&&(e={});var n=N(e),r=n.orientation,o=void 0===r?"vertical":r,a=n.gutter,i=void 0===a?0:a,u=k(n,["orientation","gutter"]),s=t.useContext(Pn),l=u.placement||(s&&"vertical"===s.orientation?"right-start":"bottom-start"),c=Mn(y({},u,{orientation:o})),f=On(y({},u,{placement:l,gutter:i}));return t.useEffect(function(){f.visible||c.unstable_reset()},[f.visible]),y({},c,{},f)}Mn.__keys=Tn;var An=[].concat(Mn.__keys,On.__keys);Dn.__keys=An;var Hn=v({name:"MenuBar",compose:m,useState:Dn,useProps:function(e,n){var r=n.ref,o=n.unstable_wrap,a=n.role,i=void 0===a?"menubar":a,u=k(n,["ref","unstable_wrap","role"]),s=t.useRef(null),l=function(e,n,r){var o="unstable_orphan"in r&&r.unstable_orphan,a=t.useContext(Pn),i=t.useState([]),u=i[0],s=i[1],l=a||{},c=l.addChild,f=l.removeChild,d=t.useCallback(function(e){return s(function(t){return[].concat(t,[e])})},[]),p=t.useCallback(function(e){return s(function(t){return me(t,e)})},[]);t.useEffect(function(){if(c&&!o)return c(e),function(){f&&f(e)}},[e,c,f,o]);var v=t.useMemo(function(){return{orientation:r.orientation,next:r.next,previous:r.previous,ref:e,role:n,parent:a,children:u,addChild:d,removeChild:p}},[r.orientation,r.next,r.previous,e,n,a,u,d,p]);return t.useCallback(function(e){return t.createElement(Pn.Provider,{value:v},e)},[v])}(s,i,e);return function(e,n,r){var o=n.stops,a=n.move;void 0===r&&(r=500);var i=t.useState(""),u=i[0],s=i[1];t.useEffect(function(){if(u){var e=setTimeout(function(){return s("")},r),t=o.find(function(e){return Boolean(e.ref.current&&e.ref.current.textContent&&e.ref.current.textContent.toLowerCase().startsWith(u))});return t&&a(t.id),function(){return clearTimeout(e)}}},[u,o,a,r]),t.useEffect(function(){var t=e.current;if(t){var n=function(e){if(!(e.metaKey||e.altKey||e.shiftKey||e.ctrlKey)){var n=e.target,r=n.getAttribute("role"),o=n===t,a=r&&r.indexOf("menuitem")>=0&&se(n,"[role=menu],[role=menubar]")===t;(o||a)&&/^[a-z0-9_-]$/i.test(e.key)&&(e.stopPropagation(),e.preventDefault(),s(function(t){return""+t+e.key}))}};return t.addEventListener("keydown",n),function(){return t.removeEventListener("keydown",n)}}},[e,s])}(s,e),y({ref:w(s,r),role:i,"aria-orientation":e.orientation,unstable_wrap:W(l,o)},u)}}),Fn=c({as:"div",useHook:Hn,useCreateElement:function(e,t,n){return s(e,t,n)}}),Bn=v({name:"Menu",compose:[Hn,xn],useState:Dn,useOptions:function(e){var n=t.useContext(Pn);return y({unstable_autoFocusOnShow:!n,unstable_autoFocusOnHide:!(n&&"menubar"===n.role),modal:!1},e,{hideOnEsc:!1})},useProps:function(e,n){for(var r=n.onKeyDown,o=k(n,["onKeyDown"]),a=t.useContext(Pn),i="horizontal"===e.orientation,u="vertical"===e.orientation,s=Boolean(a),l=a;l&&"menubar"!==l.role;)l=l.parent;var c=l||{},f=c.next,d=c.previous,p="horizontal"===c.orientation,v=(e.placement||"").split("-")[0];return y({role:"menu",onKeyDown:R(t.useMemo(function(){return ut({stopPropagation:function(e){return"Escape"!==e.key||!s},keyMap:function(t){var n=t.target===t.currentTarget;return{Escape:e.hide,ArrowUp:n&&!i&&e.last,ArrowRight:n&&!u&&e.first,ArrowDown:n&&!i&&e.first,ArrowLeft:n&&!u&&e.last,Home:n&&e.first,End:n&&e.last,PageUp:n&&e.first,PageDown:n&&e.last}}})},[s,i,u,e.hide,e.last,e.first]),t.useMemo(function(){return ut({stopPropagation:!0,shouldKeyDown:function(e){return Boolean(s&&e.currentTarget.contains(e.target))},keyMap:s?{ArrowRight:p&&"left"!==v?f:"left"===v&&e.hide,ArrowLeft:p&&"right"!==v?d:"right"===v&&e.hide}:{}})},[s,p,f,d,v,e.hide]),r)},o)}}),Ln=c({as:"div",useHook:Bn,useCreateElement:function(e,t,n){return s(e,t,n)}}),Rn=v({name:"PopoverArrow",compose:m,useState:On,keys:["size"],useOptions:function(e){var t=e.size;return y({size:void 0===t?30:t},k(e,["size"]))},useProps:function(e,n){var r,o=n.ref,a=n.style,i=k(n,["ref","style"]),u=e.placement.split("-")[0],s=e.unstable_arrowStyles;return y({ref:w(e.unstable_arrowRef,o),style:y({},s,(r={top:s&&s.top||void 0,position:"absolute",fontSize:e.size,width:"1em",height:"1em",pointerEvents:"none",transform:{top:"rotateZ(180deg)",right:"rotateZ(-90deg)",bottom:"rotateZ(360deg)",left:"rotateZ(90deg)"}[u]},r[u]="100%",r),a),children:t.createElement("svg",{viewBox:"0 0 30 30"},t.createElement("path",{className:"stroke",d:"M23.7,27.1L17,19.9C16.5,19.3,15.8,19,15,19s-1.6,0.3-2.1,0.9l-6.6,7.2C5.3,28.1,3.4,29,2,29h26 C26.7,29,24.6,28.1,23.7,27.1z"}),t.createElement("path",{className:"fill",d:"M23,27.8c1.1,1.2,3.4,2.2,5,2.2h2H0h2c1.7,0,3.9-1,5-2.2l6.6-7.2c0.7-0.8,2-0.8,2.7,0L23,27.8L23,27.8z"}))},i)}}),Nn=c({as:"div",useHook:Rn}),jn=v({name:"MenuArrow",compose:Rn,useState:Dn}),Vn=c({as:"div",useHook:jn}),Kn=v({name:"PopoverDisclosure",compose:xe,useState:On,useProps:function(e,t){var n=t.ref,r=k(t,["ref"]);return y({ref:w(e.unstable_referenceRef,n)},r)}}),Wn=c({as:"button",useHook:Kn}),qn=function(){},Gn=v({name:"MenuDisclosure",compose:Kn,useState:Dn,useProps:function(e,n){var r=n.ref,o=n.onClick,a=n.onKeyDown,i=n.onFocus,u=n.onMouseOver,s=k(n,["ref","onClick","onKeyDown","onFocus","onMouseOver"]),l=t.useContext(Pn),c=t.useRef(null),f=t.useState(!1),d=f[0],p=f[1],v=e.placement.split("-")[0],m=Boolean(l),b=l&&"menubar"===l.role,h=t.useMemo(function(){return ut({stopPropagation:function(e){return"Escape"!==e.key},onKey:e.show,keyMap:function(){var t=function(){return setTimeout(e.first)};return{Escape:e.hide,Enter:m&&t," ":m&&t,ArrowUp:("top"===v||"bottom"===v)&&e.last,ArrowRight:"right"===v&&t,ArrowDown:("bottom"===v||"top"===v)&&t,ArrowLeft:"left"===v&&t}}})},[v,m,e.show,e.hide,e.first,e.last]),g=t.useCallback(function(){b&&(p(!0),e.show())},[b,p,e.show]);t.useEffect(function(){d&&setTimeout(function(){return p(!1)},200)},[d]);var _=t.useCallback(function(t){if(l){var n=t.currentTarget;if(b)l.ref.current&&l.ref.current.querySelector("[aria-expanded='true']")&&n.focus();else setTimeout(function(){n.contains(document.activeElement)&&(e.show(),document.activeElement!==n&&n.focus())},200)}},[l,b,e.show]),E=t.useCallback(function(){!m||b&&!d?e.toggle():e.show()},[m,b,d,e.show,e.toggle]);return y({ref:w(c,r),"aria-haspopup":"menu",onClick:R(E,o),onKeyDown:R(h,a),onFocus:R(g,i),onMouseOver:R(_,u)},s)},useComposeOptions:function(e){return y({},e,{toggle:qn})}}),zn=c({as:"button",useHook:Gn}),Un=v({name:"MenuGroup",compose:m,useState:Dn,useProps:function(e,t){return y({role:"group"},t)}}),Yn=c({as:"div",useHook:Un});function Zn(){return"undefined"!=typeof window&&("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)}var Xn=v({name:"MenuItem",compose:pt,useState:Dn,useProps:function(e,n){var r=n.onMouseOver,o=n.onMouseOut,a=k(n,["onMouseOver","onMouseOut"]),i=t.useContext(Pn),u=t.useCallback(function(e){e.currentTarget&&(Zn()||i&&"menubar"===i.role||e.currentTarget.focus())},[e.orientation]),s=t.useCallback(function(e){if(e.currentTarget&&i){var t=e.currentTarget;t.hasAttribute("aria-controls")&&"true"===t.getAttribute("aria-expanded")||t.blur(),document.activeElement===document.body&&i.ref.current&&!Zn()&&i.ref.current.focus()}},[e.move]);return y({role:"menuitem",onMouseOver:R(u,r),onMouseOut:R(s,o)},a)}}),$n=c({as:"button",useHook:Xn}),Jn=v({name:"MenuItemCheckbox",compose:[Xn,V],useState:Dn,keys:["name"],useOptions:function(e){var n=t.useCallback(function(t){return e.unstable_setValue(e.name,t)},[e.unstable_setValue,e.name]);return y({},e,{state:e.unstable_values[e.name],setState:n})},useProps:function(e,t){return y({role:"menuitemcheckbox",name:e.name},t)}}),Qn=c({as:"button",useHook:Jn}),er=v({name:"MenuItemRadio",compose:[Xn,ht],useState:Dn,keys:["name"],useOptions:function(e){var n=t.useCallback(function(t){return e.unstable_setValue(e.name,t)},[e.unstable_setValue,e.name]);return y({},e,{state:e.unstable_values[e.name],setState:n})},useProps:function(e,t){return y({role:"menuitemradio"},t)}}),tr=c({as:"button",useHook:er}),nr=v({name:"Separator",compose:m,keys:["orientation"],useOptions:function(e){var t=e.orientation;return y({orientation:void 0===t?"horizontal":t},k(e,["orientation"]))},useProps:function(e,t){return y({role:"separator","aria-orientation":e.orientation},t)}}),rr=c({as:"hr",useHook:nr}),or=v({name:"MenuSeparator",compose:nr,useState:Dn,useOptions:function(e){var t=e.orientation;return y({orientation:"vertical"===(void 0===t?"vertical":t)?"horizontal":"vertical"},k(e,["orientation"]))}}),ar=c({as:"hr",useHook:or}),ir=v({name:"PopoverBackdrop",compose:Ee,useState:On}),ur=c({as:"div",useHook:ir}),sr=c({as:"fieldset",useHook:v({name:"RadioGroup",compose:Z,useState:mt,useProps:function(e,t){return y({role:"radiogroup"},t)}}),useCreateElement:function(e,t,n){return s(e,t,n)}});function lr(e,t,n){return[t,e,n].filter(Boolean).join("-")}function cr(e,t){return lr(e,t,"panel")}function fr(e){void 0===e&&(e={});var n=N(e),r=n.selectedId,o=void 0===r?null:r,a=n.loop,i=void 0===a||a,u=n.manual,s=void 0!==u&&u,l=k(n,["selectedId","loop","manual"]),c=t.useState(o),f=c[0],d=c[1],p=ft(y({loop:i,currentId:f},l));return y({},p,{selectedId:f,manual:s,select:d})}var dr=[].concat(ft.__keys,["selectedId","select","manual"]);fr.__keys=dr;var pr=v({name:"Tab",compose:pt,useState:fr,useOptions:function(e){var t=e.focusable;return y({focusable:void 0===t||t},k(e,["focusable"]))},useProps:function(e,n){var r=n.onClick,o=n.onFocus,a=k(n,["onClick","onFocus"]),i=e.selectedId===e.stopId,u=t.useCallback(function(){e.disabled||i||e.select(e.stopId)},[e.disabled,i,e.select,e.stopId]),s=t.useCallback(function(){e.disabled||e.manual||i||e.select(e.stopId)},[e.disabled,e.manual,i,e.select,e.stopId]);return y({role:"tab",id:lr(e.stopId,e.baseId),"aria-selected":i,"aria-controls":cr(e.stopId,e.baseId),onClick:R(u,r),onFocus:R(s,o)},a)}}),vr=c({as:"button",useHook:pr}),mr=v({name:"TabList",compose:Z,useState:fr,useProps:function(e,t){return y({role:"tablist","aria-orientation":e.orientation},t)}}),br=c({as:"div",useHook:mr,useCreateElement:function(e,t,n){return s(e,t,n)}}),hr=v({name:"TabPanel",compose:ee,useState:fr,keys:["stopId"],useOptions:function(e){return y({visible:e.selectedId===e.stopId},e,{unstable_setBaseId:void 0})},useProps:function(e,t){return y({role:"tabpanel",tabIndex:0,id:cr(e.stopId,e.baseId),"aria-labelledby":lr(e.stopId,e.baseId)},t)}}),gr=c({as:"div",useHook:hr});function yr(e){void 0===e&&(e={});var t=N(e),n=t.orientation;return ft(y({orientation:void 0===n?"horizontal":n},k(t,["orientation"])))}var kr=[].concat(ft.__keys);yr.__keys=kr;var wr=v({name:"Toolbar",compose:Z,useState:yr,useProps:function(e,t){return y({role:"toolbar","aria-orientation":e.orientation},t)}}),_r=c({as:"div",useHook:wr,useCreateElement:function(e,t,n){return s(e,t,n)}}),Er=v({name:"ToolbarItem",compose:pt,useState:yr}),Sr=c({as:"button",useHook:Er}),Or=v({name:"ToolbarSeparator",compose:nr,useState:yr,useOptions:function(e){var t=e.orientation;return y({orientation:"vertical"===(void 0===t?"vertical":t)?"horizontal":"vertical"},k(e,["orientation"]))}}),Cr=c({as:"hr",useHook:Or});function xr(e){void 0===e&&(e={});var t=N(e),n=t.placement,r=void 0===n?"top":n,o=t.unstable_boundariesElement,a=void 0===o?"window":o;return On(y({},k(t,["placement","unstable_boundariesElement"]),{placement:r,unstable_boundariesElement:a}))}var Ir=[].concat(On.__keys);xr.__keys=Ir;var Pr=v({name:"Tooltip",compose:ee,useState:xr,keys:["unstable_portal"],useOptions:function(e){var t=e.unstable_portal;return y({unstable_portal:void 0===t||t},k(e,["unstable_portal"]))},useProps:function(e,n){var r=n.ref,o=n.style,a=n.unstable_wrap,i=k(n,["ref","style","unstable_wrap"]),u=t.useCallback(function(n){return e.unstable_portal?t.createElement(ce,null,n):n},[e.unstable_portal]);return y({ref:w(e.unstable_popoverRef,r),role:"tooltip",style:y({},e.unstable_popoverStyles,{pointerEvents:"none"},o),unstable_wrap:W(u,a)},i)}}),Mr=c({as:"div",useHook:Pr}),Tr=v({name:"TooltipArrow",compose:Rn,useState:xr,useOptions:function(e){var t=e.size;return y({size:void 0===t?16:t},k(e,["size"]))}}),Dr=c({as:"div",useHook:Tr}),Ar=v({name:"TooltipReference",compose:m,useState:xr,useProps:function(e,t){var n=t.ref,r=t.onFocus,o=t.onBlur,a=t.onMouseEnter,i=t.onMouseLeave,u=k(t,["ref","onFocus","onBlur","onMouseEnter","onMouseLeave"]);return y({ref:w(e.unstable_referenceRef,n),tabIndex:0,onFocus:R(e.show,r),onBlur:R(e.hide,o),onMouseEnter:R(e.show,a),onMouseLeave:R(e.hide,i),"aria-describedby":e.baseId},u)}}),Hr=c({as:"div",useHook:Ar}),Fr=v({name:"VisuallyHidden",compose:m,useProps:function(e,t){var n=t.style,r=k(t,["style"]);return y({style:y({border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"},n)},r)}}),Br=c({as:"span",useHook:Fr});function Lr(e){var n=e.children,o=e.unstable_system;return t.createElement(r.Provider,{value:o},n)}e.Box=b,e.Button=B,e.Checkbox=K,e.Dialog=_e,e.DialogBackdrop=Se,e.DialogDisclosure=Ie,e.FormRadioGroupContext=yt,e.Group=Ze,e.Hidden=te,e.HiddenDisclosure=Ce,e.Menu=Ln,e.MenuArrow=Vn,e.MenuBar=Fn,e.MenuDisclosure=zn,e.MenuGroup=Yn,e.MenuItem=$n,e.MenuItemCheckbox=Qn,e.MenuItemRadio=tr,e.MenuSeparator=ar,e.Popover=In,e.PopoverArrow=Nn,e.PopoverBackdrop=ur,e.PopoverDisclosure=Wn,e.Portal=ce,e.PortalContext=le,e.Provider=function(e){var n=e.unstable_prefix,r=e.unstable_system,o=void 0===r?{}:r,a=e.children;return t.createElement(U,{prefix:n},t.createElement(Lr,{unstable_system:o},a))},e.Radio=gt,e.RadioGroup=sr,e.Rover=vt,e.Separator=rr,e.Tab=vr,e.TabList=br,e.TabPanel=gr,e.Tabbable=H,e.Toolbar=_r,e.ToolbarItem=Sr,e.ToolbarSeparator=Cr,e.Tooltip=Mr,e.TooltipArrow=Dr,e.TooltipReference=Hr,e.VisuallyHidden=Br,e.unstable_Form=je,e.unstable_FormCheckbox=Ue,e.unstable_FormGroup=$e,e.unstable_FormInput=Qe,e.unstable_FormLabel=tt,e.unstable_FormMessage=rt,e.unstable_FormPushButton=it,e.unstable_FormRadio=Et,e.unstable_FormRadioGroup=wt,e.unstable_FormRemoveButton=Ot,e.unstable_FormSubmitButton=xt,e.unstable_Id=lt,e.unstable_IdContext=z,e.unstable_IdGroup=X,e.unstable_IdProvider=U,e.unstable_useForm=Ne,e.unstable_useFormCheckbox=ze,e.unstable_useFormGroup=Xe,e.unstable_useFormInput=Je,e.unstable_useFormLabel=et,e.unstable_useFormMessage=nt,e.unstable_useFormPushButton=at,e.unstable_useFormRadio=_t,e.unstable_useFormRadioGroup=kt,e.unstable_useFormRemoveButton=St,e.unstable_useFormState=Le,e.unstable_useFormSubmitButton=Ct,e.unstable_useId=st,e.unstable_useIdGroup=Z,e.unstable_useIdState=Y,e.useBox=m,e.useButton=F,e.useCheckbox=V,e.useCheckboxState=j,e.useDialog=we,e.useDialogBackdrop=Ee,e.useDialogDisclosure=xe,e.useDialogState=ye,e.useGroup=Ye,e.useHidden=ee,e.useHiddenDisclosure=Oe,e.useHiddenState=J,e.useMenu=Bn,e.useMenuArrow=jn,e.useMenuBar=Hn,e.useMenuBarState=Mn,e.useMenuDisclosure=Gn,e.useMenuGroup=Un,e.useMenuItem=Xn,e.useMenuItemCheckbox=Jn,e.useMenuItemRadio=er,e.useMenuSeparator=or,e.useMenuState=Dn,e.usePopover=xn,e.usePopoverArrow=Rn,e.usePopoverBackdrop=ir,e.usePopoverDisclosure=Kn,e.usePopoverState=On,e.useRadio=ht,e.useRadioState=mt,e.useRover=pt,e.useRoverState=ft,e.useSeparator=nr,e.useTab=pr,e.useTabList=mr,e.useTabPanel=hr,e.useTabState=fr,e.useTabbable=A,e.useToolbar=wr,e.useToolbarItem=Er,e.useToolbarSeparator=Or,e.useToolbarState=yr,e.useTooltip=Pr,e.useTooltipArrow=Tr,e.useTooltipReference=Ar,e.useTooltipState=xr,e.useVisuallyHidden=Fr,Object.defineProperty(e,"__esModule",{value:!0})});
