<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="441px" height="233px" viewBox="-0.5 -0.5 441 233" content="&lt;mxfile host=&quot;app.diagrams.net&quot; etag=&quot;Qknl4O5RGC5Y3YQG0KG8&quot; version=&quot;21.0.8&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;kayBRQB2wAEJ8_C1nams&quot;&gt;3VnZcpswFP0az7QPyQDCSx+9pp3pkhlP2yRvCiigRJZsIW/9+gojbJBwII5tPHnxSEcbOvdwF9MA/cnqhsNp+IP5iDQcy181wKDhOG3Xlb8xsE4AtwUSIODYTyB7B4zxP6RAS6Fz7KMoN1EwRgSe5kGPUYo8kcMg52yZn/bESP7UKQyQAYw9SEz0L/ZFmKCdprXDvyIchOnJtqVGJjCdrIAohD5bZiAwbIA+Z0wkrcmqj0jMXcpLsm60Z3T7YBxRUWXB3ff7bw/W8/C3Q8dBSGffnn8Nr9QuC0jm6sLd6ZRgDwrMqBwYI75AXDY+TTlbSFPE7e5ndSOxTmnibE59FJ9kNUBvGWKBxlPoxaNLqQuJhWJCZM+WTfPJ08dAXKBVBlI3uUFsggRfyynpaMqqkpWT0r7cGWk7J8wYqKUwqHQRbLfeUScbir03MOkYTN5ytlpnOGwR+Qy9x7gVxK0spf36KXU6l0YpMCiFlMLoCnIvxAt0zXhQO2uue2msuQZrBkmI+t3YN8qeR2AUYS/PS55ESQ5f36mhTec+Hrlupt3BKjtzsE57KyzuMu3MKtnbLYo76Zq9FonYnHuoXCwC8gCJ8vcU+Tm/b9o3Y79mgflSjCMifeUiHy2KbKpOuGVY3mwrH9DKywdYmiySe6tVWe+ubeTamg6b2kYJMcZGG4ltr3246pqXorq9CipVhn1RyjDcMTiWMtzzKqNlKKMfIumJdXlIhyvygogEZy+ozwjjEqGMypm9J0yIBkGCAxqrSppcRlPQi923zGFIVw1MsO/HxxTGg7zsTpCb2K2KIUG38NFCQtswwYgj5LPJFZEZCDWj6Iexhf4W1W+LzpscpWLVh1G4ocXOWyXGb6GQTNMN4lhga6u0XHGq83oprk8Piu0DXZ++kX1C1zeaPY5ms8HL0v3y4HTDPz+f2pWrq9cqg95RKgPjjSmwf/Viq3POHLeI2PcVW4P6KTWie+2UlhVbQVQ7aUatVTtpH6DW0nx+efH1inhyKfbe97ausOI2SxLhS8yoi1g8W63VPkx2ztl05xTobm9pV5vutCzEKM0r606Pwyes8YvYNiu5E+nOOUx37ZPo7pU/kUr9Xb26a2ty0cNgZd2Bko1OrDuzfH2v7uqNlvVGQT3zPLi40rIxYB9LFbK7+yyWTN99WwTD/w==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application Server (provider A)</div></div></div></foreignObject><text x="60" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Application Server (...</text></switch></g><rect x="160" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 161px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Proxy Server<br />(provider C)</div></div></div></foreignObject><text x="220" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Proxy Server...</text></switch></g><rect x="320" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">annas-archive.li</div></div></div></foreignObject><text x="380" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">annas-archive.li</text></switch></g><path d="M 320 91 L 286.37 91" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 281.12 91 L 288.12 87.5 L 286.37 91 L 288.12 94.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 160 91 L 126.37 91" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 121.12 91 L 128.12 87.5 L 126.37 91 L 128.12 94.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="21" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 36px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Cheap</div></div></div></foreignObject><text x="60" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Cheap</text></switch></g><rect x="160" y="21" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 36px; margin-left: 161px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Freedom-loving</div></div></div></foreignObject><text x="220" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Freedom-loving</text></switch></g><path d="M 140 231 L 140 1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/><rect x="0" y="141" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 171px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application Server<br />(provider B)</div></div></div></foreignObject><text x="60" y="175" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Application Server...</text></switch></g><rect x="160" y="141" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 171px; margin-left: 161px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Proxy Server<br />(provider D)</div></div></div></foreignObject><text x="220" y="175" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Proxy Server...</text></switch></g><rect x="320" y="141" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 171px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">annas-archive.li</div></div></div></foreignObject><text x="380" y="175" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">annas-archive.li</text></switch></g><path d="M 320 171 L 286.37 171" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 281.12 171 L 288.12 167.5 L 286.37 171 L 288.12 174.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 160 156 L 123.98 110.97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 120.7 106.87 L 127.8 110.15 L 123.98 110.97 L 122.34 114.53 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 160 106 L 123.98 151.03" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 120.7 155.13 L 122.34 147.47 L 123.98 151.03 L 127.8 151.85 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 160 171 L 126.37 171" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 121.12 171 L 128.12 167.5 L 126.37 171 L 128.12 174.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>