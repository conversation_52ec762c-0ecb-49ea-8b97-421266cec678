<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="441px" height="163px" viewBox="-0.5 -0.5 441 163" content="&lt;mxfile host=&quot;app.diagrams.net&quot; etag=&quot;nIekTR1RE39ASIPEJu9b&quot; version=&quot;21.0.8&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;kayBRQB2wAEJ8_C1nams&quot;&gt;3VfbjpswEP2aPGYFmGTzmtu2K7VqpKhqtm8WngVvjU2Nk0C/viaYqxNld5Vuor4gz5nx2D5zGMMAzePsk8RJ9FUQYAPPIdkALQaed+/7+lkAeQn4Y1QCoaSkhNwGWNM/YEDHoFtKIO0EKiGYokkXDATnEKgOhqUU+27Ys2DdVRMcggWsA8xs9AclKirRychp8M9Aw6ha2XWMJ8ZVsAHSCBOxb0FoOUBzKYQqR3E2B1ZwV/FSzns44a03JoGr10zYfHl6/Om8LL97fB1G/Pfjy7fl0GTZYbY1B54mCaMBVlRw7ViD3IE0B1B5xYoUW06gSOwM0GwfUQXrBAeFd69loLFIxUxbrh6aJUAqyE7u3a0Z0UoCEYOSuQ6pJlQkGhV5Fcv7piZ1TNSqx9hg2MggrFM3TOmBIesNxHkWcSspsvx2KPMmt0YZsijDnON0iGUQ0R3cCRlenTXfvzXWfIs1iyTgZFq0Om0FDKcpDbq8dEnUTMh8Y1wH46nw3I0qc5G1Ixd5ZWVUbVrj1ixtNZMKo5pTbhWI1WN79dDHEVsZwHn1KCxDUOdeTLu+rfqNjpSvwiQw3fp23e0eq6lZYSWoPkgtHzTuygc5PVmUxzSz2s26l8h3ezoc9RKVPFiJDhKrj/1+1Y1uRXWvV9BZZZx48z9GGVY7RpdShv+xyhhbyphHoDtxXx66B6uuIFIlxS+YCyakRrjgOnL2TBnrQZjRkBeq0hXWFymaFR1df5KwqXHElJBimaP3QVd2/+Dbwx2/8kroV/hiV8K9VYIHCUBEPGRiR7l9i/43tei/RdevxeRNjdKwSnAaHWhxu1Up8BVWmml+QDwH1bWq/j68E7yeb5FXa32XuhT7idyLtT5tNj9iZXjzN4uWfwE=&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application Server</div></div></div></foreignObject><text x="60" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Application Server</text></switch></g><rect x="160" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 161px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Proxy Server</div></div></div></foreignObject><text x="220" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Proxy Server</text></switch></g><rect x="320" y="61" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 91px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">annas-archive.li</div></div></div></foreignObject><text x="380" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">annas-archive.li</text></switch></g><path d="M 320 91 L 286.37 91" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 281.12 91 L 288.12 87.5 L 286.37 91 L 288.12 94.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 160 91 L 126.37 91" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 121.12 91 L 128.12 87.5 L 126.37 91 L 128.12 94.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="21" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 36px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Cheap</div></div></div></foreignObject><text x="60" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Cheap</text></switch></g><rect x="160" y="21" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 36px; margin-left: 161px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Freedom-loving</div></div></div></foreignObject><text x="220" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Freedom-loving</text></switch></g><path d="M 140 161 L 140 1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>