name: "CI"

on:
  pull_request:
    branches:
      - "*"
  push:
    branches:
      - "main"
      - "master"

jobs:
  test:
    runs-on: "ubuntu-22.04"

    steps:
      - uses: "actions/checkout@v2"

      - name: "Install CI dependencies"
        run: |
          ./run ci:install-deps

      - name: "Test"
        run: |
          # Remove volumes in CI to avoid permission errors due to UID / GID.
          sed -i "s|.:/app|/tmp:/tmp|g" .env*
          sed -i "s|.:/app|/tmp:/tmp|g" docker-compose.yml

          ./run ci:test
