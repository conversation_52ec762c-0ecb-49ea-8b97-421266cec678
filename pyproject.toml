[project]
name = "allthethings"
version = "0.1.0"
description = "Anna's Archive, the comprehensive search engine for books, papers, comics, magazines, and more."
readme = "README.md"
requires-python = "==3.10.*"
dependencies = [
    "base58==2.1.1",
    "cachetools==5.3.0",
    "celery==5.2.7",
    "cryptography==43.0.1",
    "curlify2==*******",
    "elasticsearch==8.5.2",
    "fast-langdetect==0.2.1",
    "flask-babel==4.0.0",
    "flask-cors==3.0.10",
    "flask-debugtoolbar==0.16.0",
    "flask-elasticsearch==0.2.5",
    "flask-mail==0.9.1",
    "flask-secrets==0.1.0",
    "flask-static-digest==0.2.1",
    "flask==2.3.3",
    "forex-python==1.8",
    "gunicorn==20.1.0",
    "httpx[socks]==0.23.0",
    "indexed-zstd==1.6.1",
    "isbnlib==3.10.10",
    "jinja2==3.1.4",
    "langcodes[data]==3.3.0",
    "more-itertools==9.1.0",
    "natsort==8.4.0",
    "orjson==3.9.7",
    "orjsonl==0.2.2",
    "py-pinyin-split==5.0.0",
    "pyjwt==2.6.0",
    "pymarc>=5.2.2",
    "pymysql==1.0.2",
    "python-barcode==0.14.0",
    "python-slugify==7.0.0",
    "py-spy==0.4.0",
    "rdflib==7.0.0",
    "redis==4.3.4",
    "retry==0.9.2",
    "rfeed==1.1.1",
    "shortuuid==1.0.11",
    "sqlalchemy==1.4.41",
    "tqdm==4.64.1",
    "werkzeug==2.3.8",
    "wget==3.2",
    "xmltodict==0.13.0",
    "yappi==1.6.0",
    "zstandard==0.23.0",
    "Flask-Compress==1.17",
    "python-dateutil==2.9.0.post0",
    "Pairtree==0.8.1",
    "beautifulsoup4>=4.13.4",
    "polib>=1.2.0",
    "html5lib-modern>=1.2",
]

[tool.uv]
dev-dependencies = [
    "ruff>=0.6.1,<7",
    "pytest-cov==3.0.0",
    "pytest==7.1.3",
    "playwright>=1.47.0",
    "pytest-playwright>=0.5.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 80

[tool.ruff]
line-length = 120
