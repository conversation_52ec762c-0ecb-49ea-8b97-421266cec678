#!/usr/bin/env python3

import argparse
import pathlib
import time
import urllib.parse

import requests
from tqdm import tqdm

def main():

    print("waiting for the server to start")
    count = 0

    while True:
        response = requests.get('http://localtest.me:8000/dyn/up/databases/')
        try:
            response.raise_for_status()
            break
        except Exception:
            time.sleep(1)
            count += 1

    print(f"server started in {count} seconds")

    print("running the smoke test")

    pages=[
        # homepage
        "/",
        # search tabs
        "/search",
        "/search?index=journals",
        "/search?index=digital_lending",
        "/search?index=meta",
        # single pages
        "/scidb",
        "/faq",
        "/metadata",
        "/volunteering",
        "/torrents",
        "/llm",
        "/contact",
        "/copyright",
        # content pages
        "/md5/74f3b80bbb292475043d13f21e5f5059",
        "/slow_download/74f3b80bbb292475043d13f21e5f5059/0/0",
        "/nexusstc_download/1040wjyuo9pwa31p5uquwt0wx",
        "/scidb/10.1145/1543135.1542528", # test pdf doi
        # the donation pages
        "/donate",
        "/donate?tier=2&method=amazon",
        "/donate?tier=2&method=payment2",
        "/donate?tier=2&method=payment2cashapp",
        "/donate?tier=2&method=payment2revolut",
        "/donate?tier=2&method=ccexp",
        "/donate?tier=2&method=payment3a",
        "/donate?tier=2&method=payment3a_cc",
        "/donate?tier=2&method=payment1b_alipay",
        "/donate?tier=2&method=payment1b_alipay_cc",
        "/donate?tier=2&method=payment1b_wechat",
        "/donate?tier=2&method=payment1c_alipay",
        "/donate?tier=2&method=payment1c_alipay_cc",
        "/donate?tier=2&method=payment1c_wechat",
        "/donate?tier=2&method=payment1d_alipay",
        "/donate?tier=2&method=payment1d_alipay_cc",
        "/donate?tier=2&method=payment1d_wechat",
        "/donate?tier=2&method=payment3b",
        # the data set pages
        "/datasets",
        "/datasets/duxiu",
        "/datasets/ia",
        "/datasets/isbndb",
        "/datasets/lgli",
        "/datasets/lgrs",
        "/datasets/magzdb",
        "/datasets/edsebk",
        "/datasets/nexusstc",
        "/datasets/oclc",
        "/datasets/ol",
        "/datasets/scihub",
        "/datasets/upload",
        "/datasets/zlib",
        # codes
        "/codes?prefix_b64=",
        "/codes?prefix_b64=YWFjaWQ6",
        # the blog
        "/blog",
        "/blog/critical-window.html",
        # the api
        # "/dyn/api/fast_download.json", # TODO
        "/dyn/torrents.json",
        # "/db/aarecord_elasticsearch/md5:8336332bf5877e3adbfb60ac70720cd5.json.html", # TODO
        # account pages
        "/account",
    ]

    # tell the user how many pages we are testing
    print(f"testing {len(pages)} pages")

    # take the translations from the command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("translation", nargs="*")
    args = parser.parse_args()

    translations = args.translation

    # if no translations were provided, get them from the server
    if not translations:
        print("no translations provided; reading from server")
        response = requests.get("http://localtest.me:8000/dyn/translations/")
        response.raise_for_status()
        translations = response.json()['translations']

    print(f"testing {len(translations)} translations: {', '.join(translations)}")

    to_test = [
        (f"http://{translation}.localtest.me:8000{page}", urllib.parse.quote_plus(f"{translation}--{page}.html"))
        for translation in translations
        for page in pages
    ]

    for url, filename in tqdm(to_test, bar_format='{l_bar}{bar}{r_bar} {eta}'):
        filepath = pathlib.Path(filename)
        response = requests.get(url)
        try:
            response.raise_for_status()
            filepath.unlink(missing_ok=True)
        except Exception:
            print(f"! failed to load {url}")
            filepath.write_bytes(response.content)
            print(f"! output was saved to ./{filepath}")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        pass
