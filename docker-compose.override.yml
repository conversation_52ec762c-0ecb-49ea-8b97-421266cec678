# Override only for local development; do not sync to prod servers.

services:
  mariadb:
    # ports:
    #   - "${MARIADB_PORT_FORWARD:-127.0.0.1:3306}:3306"
    network_mode: ""
    networks:
      - "mynetwork"

  mariapersist:
    # ports:
    #   - "${MARIAPERSIST_PORT_FORWARD:-127.0.0.1:3333}:3333"
    network_mode: ""
    networks:
      - "mynetwork"

  mariapersistreplica:
    network_mode: ""
    networks:
      - "mynetwork"

  web:
    ports:
      - "${DOCKER_WEB_PORT_FORWARD:-127.0.0.1:8000}:${PORT:-8000}"
    network_mode: ""
    networks:
      - "mynetwork"
    volumes:
      - "../annas-archive-dev--temp-dir:/temp-dir"

  elasticsearch:
    # ports:
    #   - "${ELASTICSEARCH_PORT_FORWARD:-127.0.0.1:9200}:9200"
    environment:
      - "cluster.routing.allocation.disk.threshold_enabled=false"
    network_mode: ""
    networks:
      - "mynetwork"

  elasticsearchaux:
    # ports:
    #   - "${ELASTICSEARCHAUX_PORT_FORWARD:-127.0.0.1:9201}:9201"
    environment:
      - "cluster.routing.allocation.disk.threshold_enabled=false"
    network_mode: ""
    networks:
      - "mynetwork"

  kibana:
    ports:
      - "${KIBANA_PORT_FORWARD:-127.0.0.1:5601}:5601"
    network_mode: ""
    networks:
      - "mynetwork"

  mailpit:
    ports:
      - '127.0.0.1:8025:8025' # web ui
    network_mode: ""
    networks:
      - "mynetwork"

networks:
  mynetwork:
