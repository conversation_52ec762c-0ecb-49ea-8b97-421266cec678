version: '3.6'

x-app: &default-app
  build:
    context: "."
    target: "app"
    args:
      - "UID=${UID:-1000}"
      - "GID=${GID:-1000}"
      - "FLASK_DEBUG=${FLASK_DEBUG:-false}"
      - "NODE_ENV=${NODE_ENV:-production}"
  env_file:
    - ".env"
  restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
  stop_grace_period: "3s"
  tty: true
  volumes:
    - "${DOCKER_WEB_VOLUME:-./public:/app/public}"
    - "../allthethings-file-data:/file-data/"
    - "./test/data-dumps:/data-dumps/"
  logging:
    driver: "local"
    options:
      max-size: 10m
      max-file: "3"
      compress: "false"
      mode: "non-blocking"
  network_mode: "${NETWORK_MODE:-bridge}"

x-assets: &default-assets
  build:
    context: "."
    target: "assets"
    args:
      - "UID=${UID:-1000}"
      - "GID=${GID:-1000}"
      - "NODE_ENV=${NODE_ENV:-production}"
  env_file:
    - ".env"
  profiles: ["assets"]
  restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
  stop_grace_period: "0"
  tty: true
  volumes:
    - ".:/app"

services:
  mariadb:
    container_name: mariadb
    network_mode: "${NETWORK_MODE:-bridge}"
    environment:
      MARIADB_USER: "allthethings"
      MARIADB_PASSWORD: "password"
      MARIADB_ROOT_PASSWORD: "password"
      MARIADB_DATABASE: "allthethings"
      MARIADB_INITDB_SKIP_TZINFO: "1" # https://github.com/MariaDB/mariadb-docker/issues/262#issuecomment-672375238
    image: "mariadb:10.10.2"
    profiles: ["mariadb"]
    restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
    stop_grace_period: "3s"
    command: "--init-file /etc/mysql/conf.d/init.sql"
    # entrypoint: mysqld_safe --skip-grant-tables --user=mysql
    volumes:
      - "../allthethings-mysql-data:/var/lib/mysql/"
      - "./mariadb-conf:/etc/mysql/conf.d"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    logging:
      driver: "local"

  mariapersist:
    container_name: mariapersist
    network_mode: "${NETWORK_MODE:-bridge}"
    environment:
      MARIADB_USER: "${MARIAPERSIST_USER}"
      MARIADB_PASSWORD: "${MARIAPERSIST_PASSWORD}"
      MARIADB_RANDOM_ROOT_PASSWORD: "1"
      MARIADB_DATABASE: "${MARIAPERSIST_DATABASE}"
      MARIADB_INITDB_SKIP_TZINFO: "1" # https://github.com/MariaDB/mariadb-docker/issues/262#issuecomment-672375238
    image: "mariadb:10.10.2"
    profiles: ["mariapersist"]
    restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
    stop_grace_period: "3s"
    command: "--init-file /etc/mysql/conf.d/init.sql"
    # command: "--init-file /etc/mysql/conf.d/init.sql --tc-heuristic-recover=ROLLBACK"
    # entrypoint: mysqld_safe --skip-grant-tables --user=mysql
    volumes:
      - "../allthethings-mariapersist-data:/var/lib/mysql/"
      - "./mariapersist-conf:/etc/mysql/conf.d"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    logging:
      driver: "local"

  mariapersistreplica:
    container_name: mariapersistreplica
    network_mode: "${NETWORK_MODE:-bridge}"
    environment:
      MARIADB_USER: "${MARIAPERSIST_USER}"
      MARIADB_PASSWORD: "${MARIAPERSIST_PASSWORD}"
      MARIADB_RANDOM_ROOT_PASSWORD: "1"
      MARIADB_DATABASE: "${MARIAPERSIST_DATABASE}"
      MARIADB_INITDB_SKIP_TZINFO: "1" # https://github.com/MariaDB/mariadb-docker/issues/262#issuecomment-672375238
    image: "mariadb:10.10.2"
    profiles: ["mariapersistreplica"]
    restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
    stop_grace_period: "3s"
    command: "--init-file /etc/mysql/conf.d/init.sql  --server_id=${MARIAPERSISTREPLICA_SERVER_ID}"
    # entrypoint: mysqld_safe --skip-grant-tables --user=mysql
    volumes:
      - "../allthethings-mariapersistreplica-data:/var/lib/mysql/"
      - "./mariapersistreplica-conf:/etc/mysql/conf.d"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    logging:
      driver: "local"

  # redis:
  #   container_name: redis
  #   network_mode: "${NETWORK_MODE:-bridge}"
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "${DOCKER_REDIS_CPUS:-0}"
  #         memory: "${DOCKER_REDIS_MEMORY:-0}"
  #   image: "redis:7.0.5-bullseye"
  #   profiles: ["redis"]
  #   restart: "${DOCKER_RESTART_POLICY:-unless-stopped}"
  #   stop_grace_period: "3s"
  #   volumes:
  #     - "redis:/data"

  web:
    <<: *default-app
    container_name: web
    healthcheck:
      test: "${DOCKER_WEB_HEALTHCHECK_TEST:-curl localhost:8000/dyn/up/}"
      interval: "60s"
      timeout: "3s"
      start_period: "180s"
      retries: 3
    profiles: ["web"]
    deploy:
      resources:
        limits:
          memory: "${DOCKER_MAX_MEMORY_WEB:-10G}"

  # worker:
  #   <<: *default-app
  #   container_name: worker
  #   network_mode: "${NETWORK_MODE:-bridge}"
  #   command: celery -A "allthethings.app.celery_app" worker -l "${CELERY_LOG_LEVEL:-info}"
  #   entrypoint: []
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "${DOCKER_WORKER_CPUS:-0}"
  #         memory: "${DOCKER_WORKER_MEMORY:-0}"
  #   profiles: ["worker"]

  js:
    <<: *default-assets
    container_name: js
    command: "../run yarn:build:js"

  css:
    <<: *default-assets
    container_name: css
    command: "../run yarn:build:css"

  elasticsearch:
    container_name: elasticsearch
    network_mode: "${NETWORK_MODE:-bridge}"
    build:
      context: .
      dockerfile: Dockerfile-elasticsearch
    environment:
      - "ES_PORT=9200"
      - "ES_SETTING_TRANSPORT_PORT=9300"
      - "ES_SETTING_DISCOVERY_TYPE=single-node"
      - "ES_SETTING_BOOTSTRAP_MEMORY__LOCK=true"
      - "ES_JAVA_OPTS=${ES_JAVA_OPTS_ELASTICSEARCH:-}"
      - "ES_SETTING_XPACK_SECURITY_ENABLED=false"
    deploy:
      resources:
        limits:
          memory: "${DOCKER_MAX_MEMORY_ELASTICSEARCH:-10G}"
    cap_add:
      - IPC_LOCK
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    restart: unless-stopped
    profiles: ["elasticsearch"]
    volumes:
      - "../allthethings-elastic-data:/usr/share/elasticsearch/data"
    logging:
      driver: "local"

  elasticsearchaux:
    container_name: elasticsearchaux
    network_mode: "${NETWORK_MODE:-bridge}"
    build:
      context: .
      dockerfile: Dockerfile-elasticsearch
    environment:
      - "ES_PORT=9201"
      - "ES_SETTING_HTTP_PORT=9201"
      - "ES_SETTING_TRANSPORT_PORT=9301"
      - "ES_SETTING_DISCOVERY_TYPE=single-node"
      - "ES_SETTING_BOOTSTRAP_MEMORY__LOCK=true"
      - "ES_JAVA_OPTS=${ES_JAVA_OPTS_ELASTICSEARCHAUX:-}"
      - "ES_SETTING_XPACK_SECURITY_ENABLED=false"
    deploy:
      resources:
        limits:
          memory: "${DOCKER_MAX_MEMORY_ELASTICSEARCHAUX:-10G}"
    cap_add:
      - IPC_LOCK
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    restart: unless-stopped
    profiles: ["elasticsearchaux"]
    volumes:
      - "../allthethings-elasticsearchaux-data:/usr/share/elasticsearch/data"
    logging:
      driver: "local"

  kibana:
    container_name: kibana
    network_mode: "${NETWORK_MODE:-bridge}"
    image: docker.elastic.co/kibana/kibana:8.5.2
    environment:
      ELASTICSEARCH_HOSTS: '["http://elasticsearch:9200"]'
    restart: unless-stopped
    depends_on:
      - "elasticsearch"
    profiles: ["kibana"]
    logging:
      driver: "local"

  mailpit:
    container_name: 'mailpit'
    image: 'axllent/mailpit'
    network_mode: "${NETWORK_MODE:-bridge}"
    restart: unless-stopped
    profiles: ["mailpit"]
    logging:
      driver: "local"
