/*!40101 SET NAMES binary*/;
/*!40014 SET FOREIGN_KEY_CHECKS=0*/;
/*!40101 SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'*/;
/*!40103 SET TIME_ZONE='+00:00' */;
INSERT INTO `libgenli_editions_to_files` VALUES(1,1213650,244394,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(2,1213651,244394,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(3,1213652,244393,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(4,1213653,244393,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(5,1213654,244392,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(6,1213655,244392,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(7,1213656,244396,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(8,1213657,244396,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(9,1213658,244395,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(10,1213659,244395,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(11,1213660,244391,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(12,1213661,244391,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(13,1213662,1,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(14,1213663,1,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(15,1213664,2,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(16,1213665,2,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(17,1213666,3,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(18,1213667,4,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(19,1213668,244199,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(20,1213669,244198,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(21,1213670,244190,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(22,1213671,244190,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(23,1213672,244190,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(24,1213673,244189,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(25,1213674,244189,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(26,1213675,244189,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(27,1213676,244188,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(28,1213677,244188,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(29,1213678,244188,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(30,1213679,244187,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(31,1213680,244187,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(32,1213681,244186,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(33,1213682,244186,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(34,1213683,244185,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(35,1213684,244185,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(36,1213685,244190,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(37,1213686,244203,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(38,1213687,5,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(39,1213688,5,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(40,1213689,244200,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(41,1213690,244200,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(42,1213691,244200,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(43,1213692,244200,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(44,1213693,244200,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(45,1213694,244199,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(46,1213695,244199,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(47,1213696,244199,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(48,1213697,244198,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(49,1213698,244198,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(50,1213699,244201,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(51,1213700,244201,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(52,1213701,244201,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(53,1213702,152917,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(54,1213703,152917,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(55,1213704,152917,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(56,1213705,152922,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(57,1213706,152922,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(58,1213707,152922,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(59,1213708,152918,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(60,1213709,152918,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(61,1213710,152918,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(62,1213711,152921,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(63,1213712,152921,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(64,1213713,152920,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(65,1213714,152919,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(66,1213715,244204,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(67,1213716,6,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(68,1213717,6,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(69,1213718,6,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(70,1213719,7,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(71,1213720,7,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(72,1213721,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(73,1213722,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(74,1213723,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(75,1213724,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(76,1213725,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(77,1213726,9,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(78,1213727,9,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(79,1213728,9,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(80,1213729,9,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(81,1213730,10,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(82,1213731,10,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(83,1213732,10,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(84,1213733,10,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(85,1213734,11,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(86,1213735,11,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(87,1213736,11,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(88,1213737,8,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(89,1213738,12,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(90,1213739,12,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(91,1213740,12,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(92,1213741,13,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(93,1213742,13,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(94,1213743,13,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(95,1213744,14,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(96,1213745,14,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(97,1213746,15,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(98,1213747,15,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(99,1213748,16,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(100,1213749,17,"2020-08-05 14:39:08","2020-08-05 14:39:08",0)
,(542407,99,89,"2020-08-05 14:42:17","2020-08-05 14:42:17",0)
;
