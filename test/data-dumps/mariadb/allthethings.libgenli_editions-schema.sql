/*!40101 SET NAMES binary*/;
/*!40014 SET FOREIGN_KEY_CHECKS=0*/;
/*!40101 SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'*/;
/*!40103 SET TIME_ZONE='+00:00' */;
CREATE TABLE `libgenli_editions` (
  `e_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `libgen_topic` enum('a','s','l','f','r','m','c') NOT NULL COMMENT 'раздел LG',
  `type` enum('','b','ch','bpart','bsect','bs','bset','btrack','component','dataset','diss','j','a','ji','jv','mon','oth','peer-review','posted-content','proc','proca','ref','refent','rep','repser','s','fnz','m','col','chb','nonfict','omni','nov','ant','c') NOT NULL DEFAULT '',
  `series_name` varchar(500) NOT NULL DEFAULT '',
  `title` varchar(2000) NOT NULL DEFAULT '' COMMENT 'Заголовок',
  `title_add` varchar(200) NOT NULL DEFAULT '' COMMENT 'Дополнение к заглавию',
  `author` varchar(2000) NOT NULL DEFAULT '',
  `publisher` varchar(1000) NOT NULL DEFAULT '',
  `city` varchar(200) NOT NULL DEFAULT '' COMMENT 'Город',
  `edition` varchar(250) NOT NULL DEFAULT '',
  `year` varchar(45) NOT NULL DEFAULT '' COMMENT 'Год',
  `month` enum('','1q','1s','1t','2q','2s','2t','3q','3t','4q','4t','apr','aug','chr','dec','fal','feb','hol','jan','jul','jun','mar','may','mon','nov','oct','sep','spr','sum','win','aut') NOT NULL DEFAULT '',
  `day` varchar(2) NOT NULL DEFAULT '' COMMENT 'День издания',
  `pages` varchar(100) NOT NULL DEFAULT '',
  `editions_add_info` varchar(500) NOT NULL DEFAULT '' COMMENT 'Библиографический комментарий к изданию (вид выпуска -  спец., ежегодник (если не выделены  в отдельную подшивку))',
  `cover_url` varchar(450) NOT NULL DEFAULT '' COMMENT 'Ссылка на обложку',
  `cover_exists` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Наличие обложки в репозитории lg editions',
  `issue_s_id` int(11) NOT NULL DEFAULT 0 COMMENT 'Ссылка на таблицу series для периодических изданий',
  `issue_number_in_year` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Техническая нумерация в году для сортировки',
  `issue_year_number` varchar(45) NOT NULL DEFAULT '' COMMENT 'Номер за год',
  `issue_number` varchar(95) NOT NULL DEFAULT '' COMMENT 'Номер выпуска (в рамках тома)',
  `issue_volume` varchar(45) NOT NULL DEFAULT '' COMMENT 'Том',
  `issue_split` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Признак того, что номер сдвоен, 0-не сдвоен, 1,2,3 - с каким числом номеров сдвоен',
  `issue_total_number` varchar(45) NOT NULL DEFAULT '' COMMENT 'Сквозная нумерация всей подшивки',
  `issue_first_page` varchar(45) NOT NULL DEFAULT '',
  `issue_last_page` varchar(45) NOT NULL DEFAULT '',
  `issue_year_end` varchar(4) NOT NULL DEFAULT '' COMMENT 'Конечный год, заполняется если номер сдвоенный или приходится на границу 2-х годов',
  `issue_month_end` enum('','1q','1s','1t','2q','2s','2t','3q','3t','4q','4t','apr','aug','chr','dec','fal','feb','hol','jan','jul','jun','mar','may','mon','nov','oct','sep','spr','sum','win','aut') NOT NULL DEFAULT '',
  `issue_day_end` varchar(2) NOT NULL DEFAULT '' COMMENT 'Конечный день, заполняется если номер сдвоенный или приходится на границу 2-х годов',
  `issue_closed` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Если номер нe издавался 0, иначе=1',
  `doi` varchar(200) NOT NULL DEFAULT '',
  `full_text` longtext NOT NULL,
  `time_added` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'Дата добавления',
  `time_last_modified` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Дата последнего изменения',
  `visible` varchar(3) NOT NULL DEFAULT '' COMMENT 'Видимое, или закрыто для просмотра по разным причинам',
  `editable` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Возможность редактирования пользователями',
  `uid` int(10) unsigned NOT NULL DEFAULT 0,
  `commentary` varchar(200) NOT NULL DEFAULT '',
  PRIMARY KEY (`e_id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=142182992 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Издания, в.т.ч. периодические';
