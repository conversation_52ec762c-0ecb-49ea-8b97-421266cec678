/*!40101 SET NAMES binary*/;
/*!40014 SET FOREIGN_KEY_CHECKS=0*/;
/*!40101 SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'*/;
/*!40103 SET TIME_ZONE='+00:00' */;
CREATE TABLE `libgenli_elem_descr` (
  `key` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `commentary` varchar(1000) NOT NULL DEFAULT '' COMMENT 'Комментарий',
  `name_ru` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на русском - зависит от языка интерфейса',
  `name_en` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на английском',
  `type` varchar(3) NOT NULL DEFAULT '' COMMENT 'тип данных - гиперссылка, xml, ссылка на картинку и пр.',
  `checks` varchar(100) NOT NULL DEFAULT '' COMMENT 'проверка значения через регулярные выражения или ссылки на справочники',
  `link_pattern` varchar(100) NOT NULL DEFAULT '' COMMENT 'Гиперссылка для дополнения id- ссылки на справочник',
  `name_add1_ru` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на русском - зависит от языка интерфейса',
  `name_add1_en` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на английском',
  `type_add1` varchar(3) NOT NULL DEFAULT '' COMMENT 'тип данных - гиперссылка, xml, ссылка на картинку и пр.',
  `checks_add1` varchar(100) NOT NULL DEFAULT '' COMMENT 'проверка значения через регулярные выражения или ссылки на справочники',
  `filled_add1` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Обязательность заполнения',
  `link_pattern_add1` varchar(50) NOT NULL DEFAULT '' COMMENT 'Гиперссылка для дополнения id- ссылки на справочник',
  `name_add2_ru` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на русском - зависит от языка интерфейса',
  `name_add2_en` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на английском',
  `type_add2` varchar(3) NOT NULL DEFAULT '' COMMENT 'тип данных - гиперссылка, xml, ссылка на картинку и пр.',
  `checks_add2` varchar(100) NOT NULL DEFAULT '' COMMENT 'проверка значения через регулярные выражения или ссылки на справочники',
  `filled_add2` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Обязательность заполнения',
  `link_pattern_add2` varchar(50) NOT NULL DEFAULT '' COMMENT 'Гиперссылка для дополнения id- ссылки на справочник',
  `name_add3_ru` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на русском - зависит от языка интерфейса',
  `name_add3_en` varchar(100) NOT NULL DEFAULT '' COMMENT 'Наименование описательного элемента на английском',
  `type_add3` varchar(3) NOT NULL DEFAULT '' COMMENT 'тип данных - гиперссылка, xml, ссылка на картинку и пр.',
  `checks_add3` varchar(100) NOT NULL DEFAULT '' COMMENT 'проверка значения через регулярные выражения или ссылки на справочники',
  `filled_add3` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Обязательность заполнения',
  `link_pattern_add3` varchar(50) NOT NULL DEFAULT '' COMMENT 'Гиперссылка для дополнения id- ссылки на справочник',
  `for_works` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для работ',
  `for_publishers` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для издательств',
  `for_editions` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для изданий',
  `for_authors` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для авторов',
  `for_series` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'для серий',
  `for_files` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для файлов',
  `dateable` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Может ли иметь период действия с - по',
  `issueable` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Может ли иметь период действия с выпуска - по выпуск',
  `default_view_for_edit` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Показывать по умолчанию при редактировании',
  `multiple_values` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'у объекта может быть несколько описательных полей с одним и тем же типом',
  `for_libgen` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_fiction` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_fiction_rus` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_scimag` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_magz` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_standarts` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `for_comics` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Для раздела',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT 'Сортировка',
  `visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Видимое в описани',
  `editable` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Возможно ручное редактирование пользователем',
  PRIMARY KEY (`key`) USING BTREE,
  UNIQUE KEY `UNIQ1` (`name_ru`),
  UNIQUE KEY `UNIQ2` (`name_en`)
) ENGINE=MyISAM AUTO_INCREMENT=1001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Виды элементов описания';
