/*!40101 SET NAMES binary*/;
/*!40014 SET FOREIGN_KEY_CHECKS=0*/;
/*!40101 SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'*/;
/*!40103 SET TIME_ZONE='+00:00' */;
CREATE TABLE `libgenli_files` (
  `f_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `md5` varchar(32) NOT NULL,
  `pages` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Техническое количество страниц в скане',
  `dpi` varchar(45) NOT NULL DEFAULT '' COMMENT 'Разрешение',
  `visible` varchar(3) NOT NULL DEFAULT '' COMMENT 'Видимый, если не пусто, то указывает по каким причинам - cpr -  абуза, del- удален физически, no - прочие причины',
  `time_added` datetime NOT NULL,
  `time_last_modified` datetime NOT NULL,
  `cover_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'Ссылка на обложку',
  `cover_exists` tinyint(1) NOT NULL DEFAULT 0,
  `commentary` varchar(1000) NOT NULL DEFAULT '' COMMENT 'Доп. инфо о скане (fixed и пр.)',
  `color` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Цветной',
  `cleaned` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Очищенный скан',
  `orientation` enum('P','L','') NOT NULL DEFAULT '' COMMENT 'Ориентация скана - Портретная, Ландшафтная',
  `paginated` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Разворот разрезан на страницы',
  `scanned` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Сканированный',
  `vector` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Векторный',
  `bookmarked` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Есть оглавление',
  `ocr` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Есть текстовый слой',
  `filesize` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Размер файла',
  `extension` varchar(45) NOT NULL DEFAULT '' COMMENT 'Расширение',
  `locator` varchar(500) NOT NULL DEFAULT '' COMMENT 'Имя файла (до загрузки  в репозиторий)',
  `broken` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Битый',
  `editable` tinyint(3) unsigned NOT NULL DEFAULT 1 COMMENT 'Запись редактируема',
  `generic` char(32) NOT NULL DEFAULT '' COMMENT 'Ссылка на лучшую версию файла',
  `cover_info` varchar(200) NOT NULL DEFAULT '' COMMENT 'Информация об обложках (если их несколько)',
  `file_create_date` datetime NOT NULL DEFAULT '2000-01-01 05:00:00' COMMENT 'Техническая дата создания файла',
  `archive_files_count` int(10) unsigned NOT NULL DEFAULT 0,
  `archive_dop_files_flag` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'наличие доп. файлов кроме картинок, для cbr, cbz, rar, zip, 7z',
  `archive_files_pic_count` int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'Количество картинок в архиве',
  `scan_type` varchar(45) NOT NULL DEFAULT '' COMMENT 'Тип скана - цифровой, веб, бумажный скан, микропленка',
  `scan_content` varchar(145) NOT NULL DEFAULT '',
  `c2c` enum('Y','N','') NOT NULL DEFAULT '' COMMENT 'Наличие рекламы в скане (c2c)',
  `scan_quality` varchar(45) NOT NULL DEFAULT '' COMMENT 'Качество скана (HQ, Q10)',
  `releaser` varchar(125) DEFAULT '' COMMENT 'Автор релиза',
  `libgen_id` int(10) unsigned NOT NULL DEFAULT 0,
  `fiction_id` int(10) unsigned NOT NULL DEFAULT 0,
  `fiction_rus_id` int(10) unsigned NOT NULL DEFAULT 0,
  `comics_id` int(10) unsigned NOT NULL DEFAULT 0,
  `scimag_id` int(10) unsigned NOT NULL DEFAULT 0,
  `standarts_id` int(10) unsigned NOT NULL DEFAULT 0,
  `magz_id` int(10) unsigned NOT NULL DEFAULT 0,
  `libgen_topic` enum('l','s','m','c','f','r','a') NOT NULL COMMENT 'правильный раздел для файла',
  `scan_size` varchar(45) NOT NULL DEFAULT '' COMMENT 'размер рандомной картинки из архива',
  `scimag_archive_path` varchar(1000) NOT NULL DEFAULT '',
  `scimag_archive_path_is_doi` tinyint(1) DEFAULT 0 COMMENT 'Путь в архиве соответствует doi в editions',
  `uid` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`f_id`),
  UNIQUE KEY `MD5_UNIQ` (`md5`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=97945963 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Файлы';
