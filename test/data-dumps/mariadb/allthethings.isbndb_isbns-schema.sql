/*!40101 SET NAMES binary*/;
/*!40014 SET FOREIGN_KEY_CHECKS=0*/;
/*!40101 SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'*/;
/*!40103 SET TIME_ZONE='+00:00' */;
CREATE TABLE `isbndb_isbns` (
  `isbn13` char(13) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `isbn10` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`json`)),
  PRIMARY KEY (`isbn13`,`isbn10`),
  <PERSON><PERSON>Y `isbn10` (`isbn10`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
