{"name": "@phiresky/isbn-visualization", "version": "0.0.0", "description": "", "type": "module", "scripts": {"lint": "tsc && eslint .", "start": "vite", "dev": "vite", "build": "pnpm lint && vite build", "serve": "vite preview"}, "license": "MIT", "devDependencies": {"@eslint/js": "^9.21.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.5.2", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1", "vite": "^6.1.1"}, "dependencies": {"@react-three/drei": "^10.0.1", "@react-three/fiber": "9.0.4", "@types/bencode": "^2.0.4", "@types/better-sqlite3": "^7.6.12", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/three": "^0.173.0", "@vitejs/plugin-react-swc": "^3.8.0", "bencode": "^4.0.0", "better-sqlite3": "^11.8.1", "isbn3": "^1.2.7", "jsbarcode": "^3.11.6", "lru-cache": "^11.0.2", "mobx": "^6.13.6", "mobx-react-lite": "^4.1.0", "mobx-utils": "^6.1.0", "prando": "^6.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-select": "^5.10.0", "sharp": "^0.33.5", "simple-zstd": "^1.4.2", "three": "^0.173.0", "tsx": "^4.19.3", "zlib": "^1.0.5"}, "packageManager": "pnpm@10.5.0+sha512.11106a5916c7406fe4b8cb8e3067974b8728f47308a4f5ac5e850304afa6f57e2847d7950dfe78877d8d36bfb401d381c4215db3a4c3547ffa63c14333a6fa51", "pnpm": {"onlyBuiltDependencies": ["@swc/core", "better-sqlite3", "esbuild", "sharp"]}, "prettier": {}}