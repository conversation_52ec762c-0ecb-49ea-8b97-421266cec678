[package]
name = "rarity"
version = "0.1.0"
edition = "2021"

[dependencies]
simd-json = { version = "*", default-features = false, features = ["serde_impl", "known-key"] }
rusqlite = { version = "0.30", features = ["bundled"] }
zstd = "0.13.2"
humansize = "*"
serde = { version = "1.0", features = ["derive"] }
parking_lot = "0.12.3"
crossbeam-channel = "0.5.14"
num_cpus = "1.16.0"
snmalloc-rs = { version = "0.3.7", features = ["lto", "native-cpu"] }
memory-stats = "1.2.0"
regex = "1.11.1"

[profile.release]
codegen-units = 1
lto = "fat"
